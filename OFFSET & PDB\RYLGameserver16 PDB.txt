//----- (0049C3D0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSiegeArmsCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  VirtualArea::CVirtualArea *SiegeObject; // esi
  unsigned __int8 v4; // al
  CSiegeObjectMgr *v6; // eax
  unsigned int m_CodePage; // [esp-4h] [ebp-Ch]
  unsigned int Myhead; // [esp-4h] [ebp-Ch]

  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CSiegeObjectMgr::GetInstance();
  SiegeObject = (VirtualArea::CVirtualArea *)CSiegeObjectMgr::GetSiegeObject(Instance, m_CodePage);
  if ( SiegeObject )
  {
    v4 = BYTE1(lpPktBase[2].m_CodePage);
    switch ( v4 )
    {
      case 2u:
        CParty::ReLogin(SiegeObject, (CCharacter *)lpPktBase[2].m_Cmd);
        return 1;
      case 3u:
      case 9u:
        CSiegeObject::ChangeToStartKit((CSiegeObject *)SiegeObject);
        goto $L109205;
      case 6u:
      case 8u:
        CSiegeObject::UpdateSiegeArms(
          (CSiegeObject *)SiegeObject,
          lpPktBase[1].m_SrvInfo.dwServerInfo,
          *(_DWORD *)&lpPktBase[2].m_Cmd,
          v4);
        return 1;
      case 7u:
        CParty::ReLogin(SiegeObject, (CCharacter *)*(unsigned __int16 *)&lpPktBase[2].m_Cmd);
        return 1;
      case 0xAu:
$L109205:
        CAggresiveCreature::GetConsumeMPCount((CSiegeObject *)SiegeObject);
        Myhead = (unsigned int)SiegeObject->m_SpectatorList._Myhead;
        v6 = CSiegeObjectMgr::GetInstance();
        CSiegeObjectMgr::DeleteSiegeObject(v6, Myhead);
        break;
      default:
        return 1;
    }
  }
  return 1;
}

//----- (0049C490) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCastleRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CMsgProcessMgr *Instance; // eax
  Castle::CCastle *Castle; // eax
  CastleRight v5; // [esp-Ch] [ebp-10h]
  unsigned int m_CodePage; // [esp-4h] [ebp-8h]

  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = (Castle::CCastle *)Castle::CCastleMgr::GetCastle(Instance, m_CodePage);
  if ( Castle )
  {
    *(_QWORD *)v5.m_aryCastleRight = *(_QWORD *)&lpPktBase[1].m_SrvInfo.SrvState.wError;
    *(_WORD *)&v5.m_aryCastleRight[8] = lpPktBase[2].m_CodePage;
    Castle::CCastle::SetRight(Castle, v5);
  }
  return 1;
}

//----- (0049C4D0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCampRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *Camp; // eax
  CampRight v5; // [esp-Ch] [ebp-10h]
  unsigned int m_CodePage; // [esp-4h] [ebp-8h]

  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CSiegeObjectMgr::GetInstance();
  Camp = CSiegeObjectMgr::GetCamp(Instance, m_CodePage);
  if ( Camp )
  {
    *(_QWORD *)v5.m_aryCampRight = *(_QWORD *)&lpPktBase[1].m_SrvInfo.SrvState.wError;
    *(_WORD *)&v5.m_aryCampRight[8] = lpPktBase[2].m_CodePage;
    CSiegeObject::SetRight(Camp, v5);
  }
  return 1;
}

//----- (0049C510) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSiegeTimeInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  Castle::CCastleMgr *Instance; // eax
  char m_StartBit; // [esp-4h] [ebp-4h]

  m_StartBit = lpPktBase[1].m_StartBit;
  Instance = Castle::CCastleMgr::GetInstance();
  Castle::CCastleMgr::SetSiegeTime(Instance, m_StartBit);
  return 1;
}

//----- (0049C530) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCastleUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CMsgProcessMgr *Instance; // eax
  Castle::CCastle *Castle; // eax
  unsigned int v5; // [esp-4h] [ebp-Ch]

  v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = (Castle::CCastle *)Castle::CCastleMgr::GetCastle(Instance, v5);
  if ( Castle )
    Castle::CCastle::UpdateCastleInfo(
      Castle,
      lpPktBase[1].m_CodePage,
      lpPktBase[1].m_SrvInfo.SrvState.wError,
      *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
      lpPktBase[2].m_Len,
      HIBYTE(lpPktBase[2].m_Len),
      *(CastleRight *)&lpPktBase[2].m_CodePage,
      (const char *)&lpPktBase[3].m_Len);
  return 1;
}

//----- (0049C590) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCreateGuild(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  PktBase *v2; // esi
  unsigned int m_CodePage; // ebx
  unsigned int v5; // edi
  unsigned int v6; // esi
  Guild::CGuildMgr *v7; // eax
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  Guild::CGuildMgr *v11; // eax
  CCreatureManager *v12; // eax
  CCharacter *v13; // ebp
  CPartyMgr *v14; // eax
  Guild::CGuild *Guild; // eax
  CSendStream *v16; // eax
  CSingleDispatch *DispatchTable; // eax
  unsigned __int8 cInclination; // [esp+4h] [ebp-28h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+8h] [ebp-24h] BYREF
  char szName[12]; // [esp+10h] [ebp-1Ch] BYREF
  int v21; // [esp+28h] [ebp-4h]

  v2 = lpPktBase;
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x20 )
  {
    m_CodePage = lpPktBase[1].m_CodePage;
    v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    cInclination = lpPktBase[1].m_SrvInfo.dwServerInfo;
    strncpy(szName, (const char *)&lpPktBase[1].m_SrvInfo + 1, 0xBu);
    lpPktBase = (PktBase *)v2->m_SrvInfo.SrvState.wError;
    v6 = CServerSetup::GetInstance()->m_eNationType != CHINA ? 1000000 : 100000;
    GAMELOG::LogGuildCreate(1u, m_CodePage, v5, v6, (unsigned __int16)lpPktBase);
    if ( (_WORD)lpPktBase )
    {
      if ( (unsigned __int16)lpPktBase == 2 )
      {
        Instance = CCreatureManager::GetInstance();
        Character = CCreatureManager::GetCharacter(Instance, v5);
        if ( Character )
        {
          m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
          if ( m_lpGameClientDispatch )
            GameClientSendPacket::SendCharCreateGuild(
              m_lpGameClientDispatch + 8,
              v5,
              m_CodePage,
              cInclination,
              szName,
              (unsigned __int16)lpPktBase);
        }
      }
      else if ( (unsigned __int16)lpPktBase == 3 )
      {
        v7 = Guild::CGuildMgr::GetInstance();
        Guild::CGuildMgr::DissolveGuild(v7, m_CodePage);
        GAMELOG::LogGuildDispose(1u, m_CodePage, szGuildDestroyFileName, 129, 0);
      }
    }
    else
    {
      v11 = Guild::CGuildMgr::GetInstance();
      Guild::CGuildMgr::CreateGuild(v11, v5, m_CodePage, cInclination, szName);
      v12 = CCreatureManager::GetInstance();
      v13 = CCreatureManager::GetCharacter(v12, v5);
      if ( v13 )
      {
        v14 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
        Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v14, m_CodePage);
        if ( Guild && Guild::CGuild::JoinMember(Guild, v5, 1u, (unsigned __int16 *)&lpPktBase) == 1 )
          CCharacter::DeductGold(v13, v6, 1);
        v16 = (CSendStream *)v13->m_lpGameClientDispatch;
        if ( v16 )
          GameClientSendPacket::SendCharCreateGuild(
            v16 + 8,
            v5,
            m_CodePage,
            cInclination,
            szName,
            (unsigned __int16)lpPktBase);
        DispatchTable = CChatDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
        v21 = 0;
        if ( StoragelpChatDispatch.m_lpDispatch )
          CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], v13);
        v21 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
      }
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0049C7B0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  ServerInfo m_SrvInfo; // ecx
  __int16 v4; // dx
  unsigned int m_CodePage; // esi
  int v6; // edi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // ebx
  CCreatureManager *v9; // eax
  CCharacter *Character; // eax
  void (__thiscall **v11)(Guild::CGuild *, Guild::MemberInfo *); // eax
  CCharacter *m_lpDispatch; // ebp
  CPacketDispatch_vtbl *v13; // eax
  unsigned int m_dwGold; // edx
  CSingleDispatch *DispatchTable; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  CCreatureManager *v17; // eax
  CCharacter *v18; // eax
  CSendStream *v19; // ecx
  GuildRight v21; // [esp-3Ch] [ebp-B0h] BYREF
  unsigned int dwGID; // [esp+10h] [ebp-64h]
  int wCmd; // [esp+14h] [ebp-60h]
  int wError; // [esp+18h] [ebp-5Ch]
  unsigned int dwTitle; // [esp+1Ch] [ebp-58h]
  unsigned int dwSenderID; // [esp+20h] [ebp-54h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+24h] [ebp-50h] BYREF
  Guild::MemberInfo memberInfo; // [esp+2Ch] [ebp-48h] BYREF
  int v29; // [esp+70h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+7Ch] [ebp+8h]

  m_SrvInfo = lpPktBase[1].m_SrvInfo;
  v4 = *(unsigned __int16 *)((char *)&lpPktBase[4].m_Len + 1);
  m_CodePage = lpPktBase[1].m_CodePage;
  v6 = lpPktBase->m_SrvInfo.SrvState.wError;
  dwGID = *(_DWORD *)&lpPktBase[1].m_StartBit;
  dwSenderID = m_CodePage;
  lpPktBasea = (PktBase *)m_SrvInfo.dwServerInfo;
  LOWORD(wCmd) = v4;
  wError = v6;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, dwGID);
  if ( Guild )
  {
    switch ( (__int16)wCmd )
    {
      case 1:
        dwTitle = 5;
        v9 = CCreatureManager::GetInstance();
        Character = CCreatureManager::GetCharacter(v9, m_CodePage);
        StoragelpChatDispatch.m_lpDispatch = (CPacketDispatch *)Character;
        if ( (_WORD)v6 )
        {
          if ( Character )
          {
            m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
            if ( m_lpGameClientDispatch )
              GameClientSendPacket::SendCharGuildCmd(
                m_lpGameClientDispatch + 8,
                dwGID,
                m_CodePage,
                (unsigned int)lpPktBasea,
                Character->m_DBData.m_Info.Name,
                Guild->m_strName,
                wCmd,
                v6);
          }
        }
        else
        {
          v11 = (void (__thiscall **)(Guild::CGuild *, Guild::MemberInfo *))Guild->__vftable;
          qmemcpy(&memberInfo, (char *)&lpPktBase[4].m_CodePage + 1, sizeof(memberInfo));
          v11[1](Guild, &memberInfo);
          dwTitle = memberInfo.m_MemberListInfo.m_cTitle;
          if ( PacketWrap::WrapCrypt((char *)lpPktBase, 0x35u, 0x89u, 0, 0) )
            Guild::CGuild::SendAllMember(Guild, (char *)lpPktBase, 0x35u, 0x89u);
          m_lpDispatch = (CCharacter *)StoragelpChatDispatch.m_lpDispatch;
          if ( StoragelpChatDispatch.m_lpDispatch )
          {
            v13 = StoragelpChatDispatch.m_lpDispatch[188].__vftable;
            if ( v13 )
            {
              m_dwGold = Guild->m_dwGold;
              qmemcpy(&v21, &Guild->m_GuildRight, sizeof(v21));
              GameClientSendPacket::SendCharMyGuildInfo(
                (CSendStream *)&v13[3].ParsePacket,
                m_dwGold,
                v21,
                (unsigned __int8)lpPktBasea,
                0);
            }
            DispatchTable = CChatDispatch::GetDispatchTable();
            CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
            v29 = 0;
            if ( StoragelpChatDispatch.m_lpDispatch )
              CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], m_lpDispatch);
            v29 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
          }
          m_CodePage = dwSenderID;
          LOWORD(v6) = wError;
        }
        GAMELOG::LogGuildJoin(1u, dwGID, m_CodePage, dwTitle, v6);
        break;
      case 5:
        Guild::CGuild::LeaveMember(Guild, m_CodePage);
        GAMELOG::LogGuildLeave(1u, Guild->m_dwGID, 0, m_CodePage, v6);
        break;
      case 6:
        if ( (_WORD)v6 )
        {
          v17 = CCreatureManager::GetInstance();
          v18 = CCreatureManager::GetCharacter(v17, m_CodePage);
          if ( v18 )
          {
            v19 = (CSendStream *)v18->m_lpGameClientDispatch;
            if ( v19 )
              GameClientSendPacket::SendCharGuildCmd(
                v19 + 8,
                dwGID,
                m_CodePage,
                (unsigned int)lpPktBasea,
                v18->m_DBData.m_Info.Name,
                Guild->m_strName,
                wCmd,
                v6);
          }
        }
        else
        {
          (*(void (__thiscall **)(Guild::CGuild *, unsigned int, PktBase *))Guild->__vftable)(
            Guild,
            m_CodePage,
            lpPktBasea);
        }
        GAMELOG::LogGuildMemberLevelAdjust(1u, dwGID, 0, m_CodePage, (unsigned int)lpPktBasea, v6);
        break;
      case 7:
        Guild::CGuild::LogInOutMember(Guild, m_CodePage, (unsigned int)lpPktBasea);
        break;
      default:
        return 1;
    }
  }
  return 1;
}

//----- (0049CA50) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildMark(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v7; // esi
  unsigned int m_dwGold; // ebp
  char *p_m_SrvInfo; // edi
  unsigned int dwCID; // [esp+4h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+10h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1C9 )
  {
    m_CodePage = lpPktBase[1].m_CodePage;
    dwCID = *(_DWORD *)&lpPktBase[1].m_StartBit;
    lpPktBasea = *(PktBase **)((char *)&lpPktBase[37].m_SrvInfo + 1);
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_CodePage);
    v7 = Guild;
    if ( Guild )
    {
      m_dwGold = Guild->m_dwGold;
      p_m_SrvInfo = (char *)&lpPktBase[1].m_SrvInfo;
      Guild::CGuild::SetMark(Guild, dwCID, p_m_SrvInfo, (unsigned int)lpPktBasea);
      GAMELOG::LogGuildMarkChange(1u, m_CodePage, 0, m_dwGold, v7->m_dwGID, p_m_SrvInfo, 0x1B1u, 0);
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0049CAF0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildLevel(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int8 m_CodePage; // bl
  unsigned int v4; // edi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v7; // esi
  unsigned int m_dwGold; // ebp
  PktBase *lpPktBasea; // [esp+Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    m_CodePage = lpPktBase[1].m_CodePage;
    v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    lpPktBasea = *(PktBase **)((char *)&lpPktBase[1].m_CodePage + 1);
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v4);
    v7 = Guild;
    if ( Guild )
    {
      m_dwGold = Guild->m_dwGold;
      Guild::CGuild::SetLevel(Guild, m_CodePage, (unsigned int)lpPktBasea);
      GAMELOG::LogGuildLevel(1u, v4, 0, m_CodePage, m_dwGold, v7->m_dwGold, 0);
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0049CB90) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v3; // ebp
  unsigned int *p_m_CodePage; // ecx
  char *v5; // eax
  int v6; // edx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v9; // ebp
  GuildRight v10; // [esp-34h] [ebp-17Ch] BYREF
  GuildRight guildRight; // [esp+Ch] [ebp-13Ch] BYREF
  char szTempBuf[260]; // [esp+40h] [ebp-108h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x42 )
  {
    v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    qmemcpy(&guildRight, &lpPktBase[1].m_CodePage, 0x30u);
    p_m_CodePage = &lpPktBase[1].m_CodePage;
    *(_WORD *)&guildRight.m_aryRight[48] = lpPktBase[5].m_CodePage;
    v5 = szTempBuf;
    v6 = 50;
    do
    {
      *(_WORD *)v5 = Math::Convert::m_FastHeToBi[*(unsigned __int8 *)p_m_CodePage];
      v5[2] = 0;
      p_m_CodePage = (unsigned int *)((char *)p_m_CodePage + 1);
      v5 += 2;
      --v6;
    }
    while ( v6 );
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v3);
    v9 = Guild;
    if ( Guild )
    {
      qmemcpy(&v10, &guildRight, 0x30u);
      *(_WORD *)&v10.m_aryRight[48] = *(_WORD *)&guildRight.m_aryRight[48];
      Guild::CGuild::SetRight(Guild, v10);
      GAMELOG::LogGuildRightsChange(0, v9->m_dwGID, 0, (const char *)&guildRight, 0x32u, 0);
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0049CC90) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildInclination(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int v5; // [esp-4h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+8h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x11 )
  {
    v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    lpPktBasea = lpPktBase[1].m_CodePage;
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v5);
    if ( Guild )
      Guild::CGuild::SetInclination(Guild, lpPktBasea);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0049CCF0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int8 m_StartBit; // bl
  unsigned __int16 wError; // di
  ServerInfo *p_m_SrvInfo; // ecx
  char *v5; // eax
  int v6; // edx
  Guild::CGuildMgr *Instance; // eax
  Guild::CGuildMgr *v8; // eax
  unsigned __int8 cFriendlyNum; // [esp+10h] [ebp-114h]
  unsigned int cTotalMemberNum; // [esp+14h] [ebp-110h]
  unsigned __int8 cHostilityNum; // [esp+18h] [ebp-10Ch]
  char szTempBuf[260]; // [esp+1Ch] [ebp-108h] BYREF

  m_StartBit = lpPktBase[1].m_StartBit;
  wError = lpPktBase[1].m_SrvInfo.SrvState.wError;
  LOBYTE(cTotalMemberNum) = BYTE1(lpPktBase[1].m_CodePage);
  cFriendlyNum = BYTE2(lpPktBase[1].m_CodePage);
  cHostilityNum = HIBYTE(lpPktBase[1].m_CodePage);
  p_m_SrvInfo = &lpPktBase[44].m_SrvInfo;
  v5 = szTempBuf;
  v6 = 50;
  do
  {
    *(_WORD *)v5 = Math::Convert::m_FastHeToBi[LOBYTE(p_m_SrvInfo->dwServerInfo)];
    v5[2] = 0;
    p_m_SrvInfo = (ServerInfo *)((char *)p_m_SrvInfo + 1);
    v5 += 2;
    --v6;
  }
  while ( v6 );
  if ( m_StartBit == 1 )
  {
    Instance = Guild::CGuildMgr::GetInstance();
    Guild::CGuildMgr::Destroy(Instance);
  }
  v8 = Guild::CGuildMgr::GetInstance();
  Guild::CGuildMgr::SerializeIn(
    v8,
    (char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2,
    wError,
    cTotalMemberNum,
    cFriendlyNum,
    cHostilityNum,
    0);
  return 1;
}
// 49CD87: variable 'cTotalMemberNum' is possibly undefined

//----- (0049CDB0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildSafe(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int16 wError; // bp
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // ebx
  CPartyMgr *v8; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v10; // ecx
  CSendStream *m_lpGameClientDispatch; // ebx
  unsigned int v12; // [esp-10h] [ebp-24h]
  unsigned int dwCharGold; // [esp+4h] [ebp-10h]
  ServerInfo dwSafeGold; // [esp+8h] [ebp-Ch]
  unsigned __int8 cCmd; // [esp+Ch] [ebp-8h]
  unsigned int dwCID; // [esp+10h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+1Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x2D )
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  wError = lpPktBase->m_SrvInfo.SrvState.wError;
  m_CodePage = lpPktBase[1].m_CodePage;
  dwSafeGold = lpPktBase[1].m_SrvInfo;
  v12 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  dwCID = v12;
  dwCharGold = *(_DWORD *)&lpPktBase[2].m_StartBit;
  cCmd = lpPktBase[2].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v12);
  v8 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v8, m_CodePage);
  v10 = Guild;
  lpPktBasea = (PktBase *)Guild;
  if ( wError )
  {
    if ( Character )
    {
      m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        return GameClientSendPacket::SendCharGuildSafe(
                 m_lpGameClientDispatch + 8,
                 dwCID,
                 m_CodePage,
                 dwSafeGold.dwServerInfo,
                 dwCharGold,
                 cCmd,
                 (const char *)&lpPktBase[2].m_CodePage + 1,
                 wError);
    }
  }
  else if ( Guild )
  {
    if ( cCmd )
    {
      if ( cCmd == 1 )
      {
        Guild::CGuild::AddGold(Guild, dwSafeGold.dwServerInfo);
        if ( Character )
          CCharacter::DeductGold(Character, dwCharGold, 0);
      }
      else
      {
        if ( cCmd != 2 )
          return Guild::CGuild::SendGuildSafe(v10, dwCID, (char *)&lpPktBase[2].m_CodePage + 1, cCmd);
        Guild::CGuild::DeductGold(Guild, dwSafeGold.dwServerInfo);
        Guild::CGuild::ReleaseGold((Guild::CGuild *)lpPktBasea, dwCharGold);
      }
    }
    else
    {
      Guild::CGuild::DeductGold(Guild, dwSafeGold.dwServerInfo);
      if ( Character )
        CCharacter::AddGold(Character, dwCharGold, 0);
    }
    v10 = (Guild::CGuild *)lpPktBasea;
    return Guild::CGuild::SendGuildSafe(v10, dwCID, (char *)&lpPktBase[2].m_CodePage + 1, cCmd);
  }
  GAMELOG::LogGuildStoreGoldChange(1u, m_CodePage, dwCID, 0, 0, wError);
  return 1;
}

//----- (0049CF20) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseGuildMemberInfoUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // edi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int v6; // [esp-4h] [ebp-Ch]

  m_CodePage = lpPktBase[1].m_CodePage;
  v6 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v6);
  if ( Guild )
    Guild::CGuild::UpdateMemberInfo(
      Guild,
      m_CodePage,
      (Guild::MemberListInfo)lpPktBase[1].m_SrvInfo,
      *(Guild::MemberDetailInfo *)&lpPktBase[2].m_StartBit);
  return 1;
}

//----- (0049CF60) --------------------------------------------------------
unsigned __int8 __thiscall CRankingMgr::GetRank(CRankingMgr *this, const char *szCharName, unsigned __int8 cClass)
{
  int v3; // ebp
  const char *i; // edi
  int v5; // eax

  v3 = 0;
  for ( i = this->m_aryRanking[cClass][0].m_szCharName; strcmp(szCharName, i); i += 22 )
  {
    if ( ++v3 >= 100 )
    {
      LOBYTE(v5) = 0;
      return v5;
    }
  }
  return v3 + 1;
}

//----- (0049CFD0) --------------------------------------------------------
void __cdecl std::_Push_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
        RankingNode *_First,
        int _Hole,
        int _Top,
        RankingNode _Val)
{
  int v4; // esi
  int i; // eax
  RankingNode *v6; // ecx
  RankingNode *v7; // esi

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First[i];
    if ( v6->m_dwPoint <= _Val.m_dwPoint )
      break;
    v7 = &_First[v4];
    *(_DWORD *)v7->m_szCharName = *(_DWORD *)v6->m_szCharName;
    *(_DWORD *)&v7->m_szCharName[4] = *(_DWORD *)&v6->m_szCharName[4];
    *(_DWORD *)&v7->m_szCharName[8] = *(_DWORD *)&v6->m_szCharName[8];
    *(_DWORD *)&v7->m_szCharName[12] = *(_DWORD *)&v6->m_szCharName[12];
    v7->m_dwPoint = v6->m_dwPoint;
    *(_WORD *)&v7->m_cLevel = *(_WORD *)&v6->m_cLevel;
    v4 = i;
  }
  _First[v4] = _Val;
}

//----- (0049D070) --------------------------------------------------------
void __cdecl std::swap<RankingNode>(RankingNode *_Left, RankingNode *_Right)
{
  int v2; // edx
  int v3; // esi
  int v4; // edi
  int _Tmp_12; // [esp+1Ch] [ebp-10h]
  unsigned int _Tmp_16; // [esp+20h] [ebp-Ch]
  __int16 _Tmp_20; // [esp+24h] [ebp-8h]

  v2 = *(_DWORD *)_Left->m_szCharName;
  v3 = *(_DWORD *)&_Left->m_szCharName[4];
  v4 = *(_DWORD *)&_Left->m_szCharName[8];
  _Tmp_12 = *(_DWORD *)&_Left->m_szCharName[12];
  _Tmp_16 = _Left->m_dwPoint;
  _Tmp_20 = *(_WORD *)&_Left->m_cLevel;
  *_Left = *_Right;
  *(_DWORD *)_Right->m_szCharName = v2;
  *(_DWORD *)&_Right->m_szCharName[4] = v3;
  *(_DWORD *)&_Right->m_szCharName[8] = v4;
  *(_DWORD *)&_Right->m_szCharName[12] = _Tmp_12;
  _Right->m_dwPoint = _Tmp_16;
  *(_WORD *)&_Right->m_cLevel = _Tmp_20;
}

//----- (0049D100) --------------------------------------------------------
void __cdecl std::_Rotate<RankingNode *,int,RankingNode>(RankingNode *_First, RankingNode *_Mid, RankingNode *_Last)
{
  int v4; // ebx
  int v5; // eax
  int v6; // esi
  int v7; // edx
  int v8; // edx
  RankingNode *v9; // edi
  int m_szCharName; // ecx
  RankingNode *v11; // esi
  int v12; // eax
  int v13; // eax
  int v14; // ecx
  bool v15; // zf
  int _Holeval; // [esp+Ch] [ebp-1Ch]
  int _Holeval_4; // [esp+10h] [ebp-18h]
  int _Holeval_8; // [esp+14h] [ebp-14h]
  int _Holeval_12; // [esp+18h] [ebp-10h]
  unsigned int _Holeval_16; // [esp+1Ch] [ebp-Ch]
  __int16 _Holeval_20; // [esp+20h] [ebp-8h]
  RankingNode *_Lasta; // [esp+34h] [ebp+Ch]

  v4 = _Mid - _First;
  v5 = _Last - _First;
  v6 = v4;
  if ( v4 )
  {
    do
    {
      v7 = v5 % v6;
      v5 = v6;
      v6 = v7;
    }
    while ( v7 );
  }
  if ( v5 < _Last - _First && v5 > 0 )
  {
    v8 = v4;
    v9 = &_First[v5];
    _Lasta = (RankingNode *)v5;
    do
    {
      _Holeval = *(_DWORD *)v9->m_szCharName;
      _Holeval_4 = *(_DWORD *)&v9->m_szCharName[4];
      _Holeval_8 = *(_DWORD *)&v9->m_szCharName[8];
      _Holeval_12 = *(_DWORD *)&v9->m_szCharName[12];
      _Holeval_16 = v9->m_dwPoint;
      m_szCharName = (int)v9[v8].m_szCharName;
      v11 = v9;
      _Holeval_20 = *(_WORD *)&v9->m_cLevel;
      if ( &v9[v8] == _Last )
        m_szCharName = (int)_First;
      if ( (RankingNode *)m_szCharName != v9 )
      {
        do
        {
          *(_DWORD *)v11->m_szCharName = *(_DWORD *)m_szCharName;
          *(_DWORD *)&v11->m_szCharName[4] = *(_DWORD *)(m_szCharName + 4);
          *(_DWORD *)&v11->m_szCharName[8] = *(_DWORD *)(m_szCharName + 8);
          *(_DWORD *)&v11->m_szCharName[12] = *(_DWORD *)(m_szCharName + 12);
          v11->m_dwPoint = *(_DWORD *)(m_szCharName + 16);
          *(_WORD *)&v11->m_cLevel = *(_WORD *)(m_szCharName + 20);
          v12 = (int)&_Last->m_szCharName[-m_szCharName] / 22;
          v11 = (RankingNode *)m_szCharName;
          if ( v4 >= v12 )
          {
            v14 = v4 - v12;
            v13 = (int)_First;
            m_szCharName = 22 * v14;
          }
          else
          {
            v13 = 22 * v4;
          }
          m_szCharName += v13;
        }
        while ( (RankingNode *)m_szCharName != v9 );
        v8 = v4;
      }
      *(_DWORD *)v11->m_szCharName = _Holeval;
      *(_DWORD *)&v11->m_szCharName[4] = _Holeval_4;
      *(_DWORD *)&v11->m_szCharName[8] = _Holeval_8;
      *(_DWORD *)&v11->m_szCharName[12] = _Holeval_12;
      v11->m_dwPoint = _Holeval_16;
      --v9;
      v15 = _Lasta == (RankingNode *)1;
      *(_WORD *)&v11->m_cLevel = _Holeval_20;
      _Lasta = (RankingNode *)((char *)_Lasta - 1);
    }
    while ( !v15 );
  }
}

//----- (0049D270) --------------------------------------------------------
void __thiscall CRankingMgr::CRankingMgr(CRankingMgr *this)
{
  unsigned __int8 *p_m_cLevel; // edx
  int v2; // esi
  unsigned __int8 *v3; // edi

  CSingleton<CRankingMgr>::ms_pSingleton = this;
  p_m_cLevel = &this->m_aryRanking[0][0].m_cLevel;
  v2 = 2500;
  do
  {
    *((_DWORD *)p_m_cLevel - 1) = 0;
    *p_m_cLevel = 0;
    p_m_cLevel[1] = 0;
    v3 = p_m_cLevel - 20;
    *(_DWORD *)v3 = 0;
    *((_DWORD *)v3 + 1) = 0;
    *((_DWORD *)v3 + 2) = 0;
    p_m_cLevel += 22;
    --v2;
    *((_DWORD *)v3 + 3) = 0;
  }
  while ( v2 );
}

//----- (0049D2B0) --------------------------------------------------------
void __thiscall CRankingMgr::~CRankingMgr(CRankingMgr *this)
{
  CSingleton<CRankingMgr>::ms_pSingleton = 0;
}

//----- (0049D2C0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
        RankingNode *_First,
        int _Hole,
        int _Bottom,
        RankingNode _Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf
  RankingNode *v7; // esi
  RankingNode *v8; // ecx
  RankingNode *v9; // eax
  RankingNode *v10; // ecx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First[v5].m_dwPoint > _First[v5 - 1].m_dwPoint )
      --v5;
    v7 = &_First[v5];
    v8 = &_First[v4];
    *(_DWORD *)v8->m_szCharName = *(_DWORD *)v7->m_szCharName;
    *(_DWORD *)&v8->m_szCharName[4] = *(_DWORD *)&v7->m_szCharName[4];
    *(_DWORD *)&v8->m_szCharName[8] = *(_DWORD *)&v7->m_szCharName[8];
    *(_DWORD *)&v8->m_szCharName[12] = *(_DWORD *)&v7->m_szCharName[12];
    v8->m_dwPoint = v7->m_dwPoint;
    *(_WORD *)&v8->m_cLevel = *(_WORD *)&v7->m_cLevel;
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v9 = &_First[_Bottom - 1];
    v10 = &_First[v4];
    *(_DWORD *)v10->m_szCharName = *(_DWORD *)v9->m_szCharName;
    *(_DWORD *)&v10->m_szCharName[4] = *(_DWORD *)&v9->m_szCharName[4];
    *(_DWORD *)&v10->m_szCharName[8] = *(_DWORD *)&v9->m_szCharName[8];
    *(_DWORD *)&v10->m_szCharName[12] = *(_DWORD *)&v9->m_szCharName[12];
    v10->m_dwPoint = v9->m_dwPoint;
    *(_WORD *)&v10->m_cLevel = *(_WORD *)&v9->m_cLevel;
    v4 = _Bottom - 1;
  }
  std::_Push_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First, v4, _Hole, _Val);
}

//----- (0049D3B0) --------------------------------------------------------
void __cdecl std::_Make_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
        RankingNode *_First,
        RankingNode *_Last)
{
  int v2; // esi
  RankingNode *v3; // edi

  v2 = (_Last - _First) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First[v2];
    do
      std::_Adjust_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First, --v2, _Last - _First, *--v3);
    while ( v2 > 0 );
  }
}

//----- (0049D430) --------------------------------------------------------
void __cdecl std::_Pop_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
        RankingNode *_First,
        RankingNode *_Last,
        RankingNode *_Dest,
        RankingNode _Val)
{
  *_Dest = *_First;
  std::_Adjust_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First, 0, _Last - _First, _Val);
}

//----- (0049D4C0) --------------------------------------------------------
void __cdecl std::_Median<RankingNode *,RankingNode::ComparePoint>(
        RankingNode *_First,
        RankingNode *_Mid,
        RankingNode *_Last)
{
  RankingNode *v3; // esi
  int v4; // eax
  unsigned int m_dwPoint; // ecx
  int v6; // eax
  unsigned int v7; // edi
  RankingNode *v8; // ebx
  unsigned int v9; // ebp
  RankingNode *v10; // esi
  RankingNode *v11; // eax
  RankingNode *v12; // esi
  unsigned int v13; // ecx
  RankingNode *v14; // edi

  v3 = _First;
  v4 = _Last - _First;
  if ( v4 <= 40 )
  {
    if ( _Mid->m_dwPoint > _First->m_dwPoint )
      std::swap<RankingNode>(_Mid, _First);
    if ( _Last->m_dwPoint > _Mid->m_dwPoint )
      std::swap<RankingNode>(_Last, _Mid);
    if ( _Mid->m_dwPoint > _First->m_dwPoint )
      goto LABEL_31;
  }
  else
  {
    m_dwPoint = _First->m_dwPoint;
    v6 = (v4 + 1) / 8;
    v7 = 22 * v6;
    v8 = &_First[v6];
    v9 = 44 * v6;
    _First = v8;
    if ( v8->m_dwPoint > m_dwPoint )
      std::swap<RankingNode>(v8, v3);
    if ( v3[v9 / 0x16].m_dwPoint > v8->m_dwPoint )
      std::swap<RankingNode>(&v3[v9 / 0x16], v8);
    if ( v8->m_dwPoint > v3->m_dwPoint )
      std::swap<RankingNode>(v8, v3);
    v10 = &_Mid[v7 / 0xFFFFFFEA];
    if ( _Mid->m_dwPoint > _Mid[v7 / 0xFFFFFFEA].m_dwPoint )
      std::swap<RankingNode>(_Mid, &_Mid[v7 / 0xFFFFFFEA]);
    if ( _Mid[v7 / 0x16].m_dwPoint > _Mid->m_dwPoint )
      std::swap<RankingNode>(&_Mid[v7 / 0x16], _Mid);
    if ( _Mid->m_dwPoint > v10->m_dwPoint )
      std::swap<RankingNode>(_Mid, v10);
    v11 = _Last;
    v12 = &_Last[v7 / 0xFFFFFFEA];
    v13 = _Last[v7 / 0xFFFFFFEA].m_dwPoint;
    v14 = &_Last[v9 / 0xFFFFFFEA];
    if ( v13 > _Last[v9 / 0xFFFFFFEA].m_dwPoint )
    {
      std::swap<RankingNode>(v12, v14);
      v11 = _Last;
    }
    if ( v11->m_dwPoint > v12->m_dwPoint )
      std::swap<RankingNode>(v11, v12);
    if ( v12->m_dwPoint > v14->m_dwPoint )
      std::swap<RankingNode>(v12, v14);
    if ( _Mid->m_dwPoint > v8->m_dwPoint )
      std::swap<RankingNode>(_Mid, v8);
    if ( v12->m_dwPoint > _Mid->m_dwPoint )
      std::swap<RankingNode>(v12, _Mid);
    if ( _Mid->m_dwPoint > v8->m_dwPoint )
LABEL_31:
      std::swap<RankingNode>(_Mid, _First);
  }
}

//----- (0049D650) --------------------------------------------------------
std::pair<RankingNode *,RankingNode *> *__cdecl std::_Unguarded_partition<RankingNode *,RankingNode::ComparePoint>(
        std::pair<RankingNode *,RankingNode *> *result,
        RankingNode *_First,
        RankingNode *_Last)
{
  RankingNode *v3; // esi
  RankingNode *i; // ebp
  unsigned int m_dwPoint; // eax
  unsigned int v6; // ecx
  unsigned int v7; // ecx
  unsigned int v8; // eax
  RankingNode *v9; // edi
  RankingNode *v10; // ebx
  unsigned int v11; // eax
  unsigned int v12; // ecx
  RankingNode *v13; // edx
  bool v14; // zf
  unsigned int v15; // eax
  unsigned int v16; // ecx
  std::pair<RankingNode *,RankingNode *> *v17; // eax
  RankingNode *v18; // [esp-8h] [ebp-18h]
  RankingNode *v19; // [esp-8h] [ebp-18h]
  RankingNode *v20; // [esp-4h] [ebp-14h]

  v3 = &_First[(_Last - _First) / 2];
  std::_Median<RankingNode *,RankingNode::ComparePoint>(_First, v3, _Last - 1);
  for ( i = v3 + 1; _First < v3; --v3 )
  {
    m_dwPoint = v3->m_dwPoint;
    v6 = v3[-1].m_dwPoint;
    if ( m_dwPoint < v6 )
      break;
    if ( m_dwPoint > v6 )
      break;
  }
  if ( i < _Last )
  {
    v7 = v3->m_dwPoint;
    do
    {
      v8 = i->m_dwPoint;
      if ( v7 < v8 )
        break;
      if ( v7 > v8 )
        break;
      ++i;
    }
    while ( i < _Last );
  }
  v9 = i;
  v10 = v3;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v9 < _Last; ++v9 )
      {
        v11 = v9->m_dwPoint;
        v12 = v3->m_dwPoint;
        if ( v11 >= v12 )
        {
          if ( v11 > v12 )
            break;
          v18 = i++;
          std::swap<RankingNode>(v18, v9);
        }
      }
      v13 = _First;
      v14 = v10 == _First;
      if ( v10 > _First )
      {
        do
        {
          v15 = v10[-1].m_dwPoint;
          v16 = v3->m_dwPoint;
          if ( v16 >= v15 )
          {
            if ( v16 > v15 )
              break;
            std::swap<RankingNode>(--v3, v10 - 1);
            v13 = _First;
          }
          --v10;
        }
        while ( v13 < v10 );
        v14 = v10 == v13;
      }
      if ( v14 )
        break;
      --v10;
      if ( v9 == _Last )
      {
        if ( v10 != --v3 )
          std::swap<RankingNode>(v10, v3);
        std::swap<RankingNode>(v3, --i);
      }
      else
      {
        std::swap<RankingNode>(v9++, v10);
      }
    }
    if ( v9 == _Last )
      break;
    if ( i != v9 )
      std::swap<RankingNode>(v3, i);
    v20 = v9;
    v19 = v3;
    ++i;
    ++v3;
    ++v9;
    std::swap<RankingNode>(v19, v20);
  }
  v17 = result;
  result->first = v3;
  result->second = i;
  return v17;
}

//----- (0049D7B0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<RankingNode *,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last)
{
  RankingNode *v2; // esi
  RankingNode *v3; // edi
  unsigned int m_dwPoint; // ecx
  RankingNode *v5; // eax
  RankingNode *v6; // edx

  if ( _First != _Last )
  {
    v2 = _First + 1;
    if ( &_First[1] != _Last )
    {
      v3 = _First + 2;
      do
      {
        m_dwPoint = v3[-1].m_dwPoint;
        if ( m_dwPoint <= _First->m_dwPoint )
        {
          v5 = v3 - 2;
          if ( m_dwPoint > v3[-2].m_dwPoint )
          {
            do
              v6 = v5--;
            while ( m_dwPoint > v5->m_dwPoint );
            if ( v6 != v2 && v2 != v3 )
              std::_Rotate<RankingNode *,int,RankingNode>(v6, v2, v3);
          }
        }
        else if ( _First != v2 && v2 != v3 )
        {
          std::_Rotate<RankingNode *,int,RankingNode>(_First, v2, v3);
        }
        ++v2;
        ++v3;
      }
      while ( v2 != _Last );
    }
  }
}

//----- (0049D830) --------------------------------------------------------
void __cdecl std::sort_heap<RankingNode *,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last)
{
  RankingNode *v2; // esi

  if ( _Last - _First > 1 )
  {
    v2 = _Last - 1;
    do
    {
      std::_Pop_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First, v2, v2, *v2);
      --v2;
    }
    while ( (int)&v2->m_szCharName[22 - (_DWORD)_First] / 22 > 1 );
  }
}

//----- (0049D8D0) --------------------------------------------------------
void __cdecl std::_Sort<RankingNode *,int,RankingNode::ComparePoint>(
        RankingNode *_First,
        RankingNode *_Last,
        int _Ideal,
        RankingNode::ComparePoint _Pred)
{
  RankingNode *v4; // ebx
  RankingNode *first; // edi
  int v6; // eax
  RankingNode *second; // ebp
  std::pair<RankingNode *,RankingNode *> _Mid; // [esp+10h] [ebp-8h] BYREF

  v4 = _First;
  first = _Last;
  v6 = _Last - _First;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<RankingNode *,RankingNode::ComparePoint>(v4, first);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<RankingNode *,RankingNode::ComparePoint>(&_Mid, v4, first);
      second = _Mid.second;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first - v4 >= first - _Mid.second )
      {
        std::_Sort<RankingNode *,int,RankingNode::ComparePoint>(_Mid.second, first, _Ideal, _Pred);
        first = _Mid.first;
      }
      else
      {
        std::_Sort<RankingNode *,int,RankingNode::ComparePoint>(v4, _Mid.first, _Ideal, _Pred);
        v4 = second;
      }
      v6 = first - v4;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( first - v4 > 1 )
      std::_Make_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(v4, first);
    std::sort_heap<RankingNode *,RankingNode::ComparePoint>(v4, first);
  }
}
// 49D9CF: conditional instruction was optimized away because eax.4>=21

//----- (0049DA20) --------------------------------------------------------
RankingNode *__cdecl std::_Partial_sort_copy<RankingNode *,RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
        RankingNode *_First1,
        RankingNode *_Last1,
        RankingNode *_First2,
        RankingNode *_Last2)
{
  RankingNode *v4; // esi
  RankingNode *i; // ebx
  RankingNode *v6; // edx
  __int16 v7; // cx

  v4 = _First1;
  for ( i = _First2; v4 != _Last1; *(_WORD *)&v6->m_cLevel = v7 )
  {
    if ( i == _Last2 )
      break;
    v6 = i;
    *(_DWORD *)i->m_szCharName = *(_DWORD *)v4->m_szCharName;
    *(_DWORD *)&i->m_szCharName[4] = *(_DWORD *)&v4->m_szCharName[4];
    *(_DWORD *)&i->m_szCharName[8] = *(_DWORD *)&v4->m_szCharName[8];
    *(_DWORD *)&i->m_szCharName[12] = *(_DWORD *)&v4->m_szCharName[12];
    i->m_dwPoint = v4->m_dwPoint;
    v7 = *(_WORD *)&v4->m_cLevel;
    ++v4;
    ++i;
  }
  if ( i - _First2 > 1 )
    std::_Make_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First2, i);
  for ( ; v4 != _Last1; ++v4 )
  {
    if ( v4->m_dwPoint > _First2->m_dwPoint )
      std::_Adjust_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(_First2, 0, i - _First2, *v4);
  }
  std::sort_heap<RankingNode *,RankingNode::ComparePoint>(_First2, i);
  return i;
}

//----- (0049DB10) --------------------------------------------------------
RankingNode *__cdecl std::partial_sort_copy<RankingNode *,RankingNode *,RankingNode::ComparePoint>(
        RankingNode *_First1,
        RankingNode *_Last1,
        RankingNode *_First2,
        RankingNode *_Last2)
{
  RankingNode *result; // eax

  result = _First2;
  if ( _First1 != _Last1 && _First2 != _Last2 )
    return std::_Partial_sort_copy<RankingNode *,RankingNode *,int,RankingNode,RankingNode::ComparePoint>(
             _First1,
             _Last1,
             _First2,
             _Last2);
  return result;
}

//----- (0049DB50) --------------------------------------------------------
void __thiscall CRankingMgr::UpdateRanking(CRankingMgr *this, const RankingNode *node)
{
  int m_cClass; // edx
  const char *m_szCharName; // ebx
  bool v6; // cc
  CRankingMgr *v7; // esi
  RankingNode *v8; // eax
  const RankingNode *v9; // ecx
  RankingNode *v10; // ecx
  RankingNode *v11; // [esp+8h] [ebp-Ch]
  RankingNode *v13; // [esp+10h] [ebp-4h]
  const RankingNode *nodea; // [esp+18h] [ebp+4h]

  if ( CClass::GetJobLevel(node->m_cClass) == 2 )
  {
    m_cClass = node->m_cClass;
    m_szCharName = this->m_aryRanking[m_cClass][0].m_szCharName;
    nodea = 0;
    v13 = this->m_aryRanking[node->m_cClass];
    v11 = v13;
    while ( 1 )
    {
      if ( !strcmp(szLoseCharName, m_szCharName) )
      {
LABEL_6:
        if ( v13[99].m_dwPoint >= node->m_dwPoint )
          return;
        v7 = this;
        v8 = v13 + 99;
        v9 = node;
        *(_DWORD *)v13[99].m_szCharName = *(_DWORD *)node->m_szCharName;
        *(_DWORD *)&v13[99].m_szCharName[4] = *(_DWORD *)&node->m_szCharName[4];
        *(_DWORD *)&v13[99].m_szCharName[8] = *(_DWORD *)&node->m_szCharName[8];
        *(_DWORD *)&v13[99].m_szCharName[12] = *(_DWORD *)&node->m_szCharName[12];
        v13[99].m_dwPoint = node->m_dwPoint;
        goto LABEL_8;
      }
      if ( !strcmp(node->m_szCharName, m_szCharName) )
        break;
      m_szCharName = v11[1].m_szCharName;
      v6 = (int)&nodea->m_szCharName[1] < 100;
      nodea = (const RankingNode *)((char *)nodea + 1);
      ++v11;
      if ( !v6 )
        goto LABEL_6;
    }
    v7 = this;
    v8 = (RankingNode *)((char *)this + 22 * ((_DWORD)nodea + 100 * m_cClass));
    if ( node->m_dwPoint != v8->m_dwPoint
      || node->m_cLevel != v8->m_cLevel
      || node->m_cClass != this->m_aryRanking[0][(int)nodea + 100 * node->m_cClass].m_cClass )
    {
      v9 = node;
      *(_DWORD *)v8->m_szCharName = *(_DWORD *)node->m_szCharName;
      *(_DWORD *)&v8->m_szCharName[4] = *(_DWORD *)&node->m_szCharName[4];
      *(_DWORD *)&v8->m_szCharName[8] = *(_DWORD *)&node->m_szCharName[8];
      *(_DWORD *)&v8->m_szCharName[12] = *(_DWORD *)&node->m_szCharName[12];
      v8->m_dwPoint = node->m_dwPoint;
LABEL_8:
      *(_WORD *)&v8->m_cLevel = *(_WORD *)&v9->m_cLevel;
      v10 = v7->m_aryRanking[node->m_cClass];
      std::_Sort<RankingNode *,int,RankingNode::ComparePoint>(v10, v10 + 100, 100, 0);
    }
  }
}

//----- (0049DCF0) --------------------------------------------------------
char __thiscall CRankingMgr::SendRankingInfo(
        CRankingMgr *this,
        unsigned int dwCID,
        unsigned __int8 cClass,
        unsigned __int8 cPage)
{
  CRankingMgr *v4; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  int Nationality; // eax
  int v9; // ebx
  char v10; // al
  RankingNode *v11; // edx
  unsigned int m_dwPoint; // edi
  __int16 v13; // dx
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned __int16 wError; // [esp+Ch] [ebp-1B4h]
  RankingNode *lpRankingNode; // [esp+10h] [ebp-1B0h]
  CCharacter *lpCharacter; // [esp+18h] [ebp-1A8h]
  char szBuffer[416]; // [esp+1Ch] [ebp-1A4h] BYREF

  v4 = this;
  if ( cClass && CClass::GetJobLevel(cClass) != 2 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRankingMgr::SendRankingInfo",
      aDWorkRylSource_59,
      88,
      aCid0x08x_233,
      dwCID,
      cClass);
    return 1;
  }
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, dwCID);
  lpCharacter = Character;
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRankingMgr::SendRankingInfo",
      aDWorkRylSource_59,
      95,
      aCid0x08x_139,
      dwCID);
    return 1;
  }
  lpRankingNode = (RankingNode *)&szBuffer[20];
  *(_DWORD *)&szBuffer[12] = dwCID;
  szBuffer[16] = cClass;
  szBuffer[17] = cPage;
  *(_WORD *)&szBuffer[18] = 0;
  wError = 0;
  if ( !cClass )
  {
    Nationality = (unsigned __int8)Character->m_DBData.m_Info.Nationality;
    if ( Nationality )
    {
      if ( Nationality == 1 )
        std::partial_sort_copy<RankingNode *,RankingNode *,RankingNode::ComparePoint>(
          v4->m_aryRanking[19],
          (RankingNode *)&v4[1],
          (RankingNode *)v4,
          v4->m_aryRanking[1]);
    }
    else
    {
      std::partial_sort_copy<RankingNode *,RankingNode *,RankingNode::ComparePoint>(
        v4->m_aryRanking[5],
        v4->m_aryRanking[13],
        (RankingNode *)v4,
        v4->m_aryRanking[1]);
    }
  }
  v9 = 0;
  v10 = 18 * cPage;
  while ( (unsigned __int8)(v9 + v10) < 0x64u )
  {
    v11 = &v4->m_aryRanking[cClass][(unsigned __int8)(v9 + v10)];
    if ( !strcmp(szLoseCharName, v11->m_szCharName) )
    {
      if ( v9 )
        goto LABEL_20;
      break;
    }
    *(_DWORD *)lpRankingNode->m_szCharName = *(_DWORD *)v11->m_szCharName;
    *(_DWORD *)&lpRankingNode->m_szCharName[4] = *(_DWORD *)&v11->m_szCharName[4];
    *(_DWORD *)&lpRankingNode->m_szCharName[8] = *(_DWORD *)&v11->m_szCharName[8];
    *(_DWORD *)&lpRankingNode->m_szCharName[12] = *(_DWORD *)&v11->m_szCharName[12];
    m_dwPoint = v11->m_dwPoint;
    v13 = *(_WORD *)&v11->m_cLevel;
    lpRankingNode->m_dwPoint = m_dwPoint;
    *(_WORD *)&lpRankingNode->m_cLevel = v13;
    ++*(_WORD *)&szBuffer[18];
    ++v9;
    ++lpRankingNode;
    if ( v9 >= 18 )
      goto LABEL_20;
    v4 = this;
  }
  wError = 2;
LABEL_20:
  m_lpGameClientDispatch = (CSendStream *)lpCharacter->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
    return 1;
  return CSendStream::WrapCompress(
           m_lpGameClientDispatch + 8,
           szBuffer,
           (char *)(22 * *(_DWORD *)&szBuffer[18] + 20),
           0x73u,
           0,
           wError);
}

//----- (0049DF20) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::Enter(
        VirtualArea::CBGServerMgr *this,
        CCharacter *lpCharacter,
        unsigned __int16 wMapIndex,
        int cMoveType)
{
  unsigned __int16 v5; // dx
  VirtualArea::CBGServerMap **Myfirst; // edi
  unsigned int v7; // eax
  VirtualArea::CBGServerMap *v8; // edi
  unsigned __int16 v9; // ax
  CGameClientDispatch *m_lpGameClientDispatch; // ebp
  unsigned __int8 VirtualZone; // al
  const Position *StartPosition; // eax
  CGameClientDispatch *v14; // esi
  unsigned __int8 v15; // al
  unsigned __int16 v16; // [esp-Ch] [ebp-20h]
  Position result; // [esp+8h] [ebp-Ch] BYREF
  char wMapIndexa; // [esp+1Ch] [ebp+8h]

  if ( !lpCharacter )
    return 0;
  v5 = wMapIndex & 0x7FFF;
  if ( (wMapIndex & 0x7FFF) == 0 )
    return 0;
  Myfirst = this->m_lstBGServerMap._Myfirst;
  v7 = Myfirst ? this->m_lstBGServerMap._Mylast - Myfirst : 0;
  if ( v5 > v7 )
    return 0;
  v8 = Myfirst[v5 - 1];
  if ( !v8 )
    return 0;
  v9 = v8->Enter(v8, lpCharacter, cMoveType);
  if ( !v9 )
  {
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( !m_lpGameClientDispatch )
    {
      v8->Leave(v8, lpCharacter);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "VirtualArea::CBGServerMgr::Enter",
        aDWorkRylSource_87,
        129,
        aCid0x08x_128,
        lpCharacter->m_dwCID,
        lpCharacter->m_CellPos.m_wMapIndex,
        wMapIndex);
      return 0;
    }
    VirtualZone = VirtualArea::CVirtualArea::GetVirtualZone(v8);
    if ( !GameClientSendPacket::SendCharBGServerMoveZone(
            &m_lpGameClientDispatch->m_SendStream,
            VirtualZone,
            cMoveType,
            0) )
    {
      v8->Leave(v8, lpCharacter);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "VirtualArea::CBGServerMgr::Enter",
        aDWorkRylSource_87,
        107,
        aCid0x08x_128,
        lpCharacter->m_dwCID,
        lpCharacter->m_CellPos.m_wMapIndex,
        wMapIndex);
      return 0;
    }
    wMapIndexa = lpCharacter->m_DBData.m_Info.Nationality;
    lpCharacter->m_CellPos.m_wMapIndex = wMapIndex;
    StartPosition = VirtualArea::CVirtualArea::GetStartPosition(v8, &result, wMapIndexa);
    CAggresiveCreature::MoveTo(lpCharacter, StartPosition, 0);
    if ( (_BYTE)cMoveType == 1 )
      lpCharacter->m_dwStatusFlag |= 0x40000000u;
    VirtualArea::CBGServerMap::SendMapInfo(v8);
    return 1;
  }
  if ( v9 == 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CBGServerMgr::Enter",
      aDWorkRylSource_87,
      138,
      aCid0x08x_128,
      lpCharacter->m_dwCID,
      lpCharacter->m_CellPos.m_wMapIndex,
      wMapIndex);
    return 1;
  }
  else
  {
    v14 = lpCharacter->m_lpGameClientDispatch;
    if ( !v14 )
      return 1;
    v16 = v9;
    v15 = VirtualArea::CVirtualArea::GetVirtualZone(v8);
    return GameClientSendPacket::SendCharBGServerMoveZone(&v14->m_SendStream, v15, cMoveType, v16);
  }
}



//----- (0049E100) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::Leave(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter)
{
  unsigned __int16 v3; // dx
  VirtualArea::CBGServerMap **Myfirst; // edi
  unsigned int v5; // eax
  VirtualArea::CBGServerMap *v6; // edi
  unsigned __int8 Nationality; // bl
  unsigned int v8; // eax
  float fPointY; // ecx
  float fPointZ; // edx
  DWORD TickCount; // eax
  DWORD v12; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  Position RespawnPos; // [esp+4h] [ebp-Ch] BYREF

  if ( !lpCharacter )
    return 0;
  v3 = lpCharacter->m_CellPos.m_wMapIndex & 0x7FFF;
  if ( !v3 )
    return 0;
  Myfirst = this->m_lstBGServerMap._Myfirst;
  v5 = Myfirst ? this->m_lstBGServerMap._Mylast - Myfirst : 0;
  if ( v3 > v5 )
    return 0;
  v6 = Myfirst[v3 - 1];
  if ( !v6 )
    return 0;
  Nationality = lpCharacter->m_DBData.m_Info.Nationality;
  lpCharacter->m_CellPos.m_wMapIndex = 0;
  v8 = Math::Random::ComplexRandom(2, 0) + 2 * Nationality;
  fPointY = RespawnPos_0[0][v8].fPointY;
  RespawnPos.m_fPointX = RespawnPos_0[0][v8].fPointX;
  fPointZ = RespawnPos_0[0][v8].fPointZ;
  RespawnPos.m_fPointY = fPointY;
  RespawnPos.m_fPointZ = fPointZ;
  TickCount = GetTickCount();
  RespawnPos.m_fPointX = (double)(Math::Random::SimpleRandom(TickCount, 20, 0) - 10) + RespawnPos.m_fPointX;
  v12 = GetTickCount();
  RespawnPos.m_fPointZ = (double)(Math::Random::SimpleRandom(v12, 20, 0) - 10) + RespawnPos.m_fPointZ;
  CAggresiveCreature::MoveTo(lpCharacter, &RespawnPos, 0);
  lpCharacter->m_dwStatusFlag &= ~0x40000000u;
  v6->Leave(v6, lpCharacter);
  m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    if ( !GameClientSendPacket::SendCharBGServerMoveZone(&m_lpGameClientDispatch->m_SendStream, 0xBu, 0, 0) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "VirtualArea::CBGServerMgr::Leave",
        aDWorkRylSource_87,
        178,
        aCid0x08x_118,
        lpCharacter->m_dwCID,
        lpCharacter->m_CellPos.m_wMapIndex);
      return 0;
    }
    if ( VirtualArea::CBGServerMap::IsPlayer(v6, lpCharacter) )
      VirtualArea::CBGServerMap::SendMapInfo(v6);
  }
  return 1;
}

//----- (0049E280) --------------------------------------------------------
VirtualArea::CBGServerMap *__thiscall VirtualArea::CBGServerMgr::GetVirtualArea(
        VirtualArea::CBGServerMgr *this,
        unsigned __int16 wMapIndex)
{
  unsigned __int16 v2; // dx
  VirtualArea::CBGServerMap **Myfirst; // esi
  unsigned int v4; // eax

  v2 = wMapIndex & 0x7FFF;
  if ( (wMapIndex & 0x7FFF) != 0
    && ((Myfirst = this->m_lstBGServerMap._Myfirst) != 0 ? (v4 = this->m_lstBGServerMap._Mylast - Myfirst) : (v4 = 0),
        v2 <= v4) )
  {
    return Myfirst[v2 - 1];
  }
  else
  {
    return 0;
  }
}

//----- (0049E2C0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::SendMapList(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter)
{
  CPerformanceCheck *Instance; // eax
  VirtualArea::CBGServerMap **Myfirst; // esi
  unsigned __int8 v6; // bl
  char *v7; // ecx
  unsigned int v8; // eax
  VirtualArea::CBGServerMap *v9; // eax
  char m_cRemainPlayMin; // dl
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // ecx
  char v13; // bl
  CPerformanceInstrument functionInstrument; // [esp+Ch] [ebp-BCh] BYREF
  _LARGE_INTEGER v15; // [esp+24h] [ebp-A4h]
  CAutoInstrument autofunctionInstrument; // [esp+2Ch] [ebp-9Ch]
  char szBuffer[136]; // [esp+30h] [ebp-98h] BYREF
  int v18; // [esp+C4h] [ebp-4h]

  functionInstrument.m_szfunctionName = "VirtualArea::CBGServerMgr::SendMapList";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "VirtualArea::CBGServerMgr::SendMapList", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  v15.QuadPart = __rdtsc();
  functionInstrument.m_startTime = v15;
  v18 = 0;
  if ( lpCharacter )
  {
    Myfirst = this->m_lstBGServerMap._Myfirst;
    szBuffer[12] = 1;
    szBuffer[13] = 0;
    v6 = 0;
    v7 = &szBuffer[16];
    do
    {
      if ( Myfirst )
        v8 = this->m_lstBGServerMap._Mylast - Myfirst;
      else
        v8 = 0;
      if ( v6 >= v8 )
        break;
      v9 = Myfirst[v6];
      if ( v9 )
      {
        *(v7 - 2) = v9->m_cStatus == 1;
        *(v7 - 1) = v9->m_MapInfo.m_cMapType;
        *v7 = v9->m_MapInfo.m_cMaxCharNumOfNation;
        *((_WORD *)v7 + 1) = v9->m_MapInfo.m_wTargetScore;
        if ( v9->m_cStatus == 1 )
          m_cRemainPlayMin = v9->m_MapInfo.m_cRemainPlayMin;
        else
          m_cRemainPlayMin = v9->m_MapInfo.m_cRemainRestMin;
        v7[1] = m_cRemainPlayMin;
        v7[4] = v9->m_MapInfo.m_cCurrentCharNum[0];
        v7[5] = v9->m_MapInfo.m_cCurrentCharNum[1];
        *((_WORD *)v7 + 3) = v9->m_MapInfo.m_wScore[0];
        *((_WORD *)v7 + 4) = v9->m_MapInfo.m_wScore[1];
      }
      ++v6;
      v7 += 12;
      ++szBuffer[13];
    }
    while ( v6 < 0xAu );
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
      LOWORD(m_lpGameClientDispatch) = (unsigned __int8)szBuffer[13];
      v13 = CSendStream::WrapCompress(
              p_m_SendStream,
              szBuffer,
              (char *)(12 * (_DWORD)m_lpGameClientDispatch + 14),
              0x98u,
              0,
              0);
      v18 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return v13;
    }
    else
    {
      v18 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return 1;
    }
  }
  else
  {
    v18 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
}
// 49E2C0: could not find valid save-restore pair for ebx
// 49E2C0: could not find valid save-restore pair for esi

//----- (0049E4B0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::SendResultList(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter)
{
  CPerformanceCheck *Instance; // eax
  VirtualArea::CBGServerMap **Myfirst; // esi
  unsigned __int8 v6; // bl
  char *v7; // eax
  unsigned int v8; // ecx
  VirtualArea::CBGServerMap *v9; // ecx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // ecx
  char v12; // bl
  CPerformanceInstrument functionInstrument; // [esp+Ch] [ebp-A8h] BYREF
  _LARGE_INTEGER v14; // [esp+24h] [ebp-90h]
  CAutoInstrument autofunctionInstrument; // [esp+2Ch] [ebp-88h]
  char szBuffer[116]; // [esp+30h] [ebp-84h] BYREF
  int v17; // [esp+B0h] [ebp-4h]

  functionInstrument.m_szfunctionName = "VirtualArea::CBGServerMgr::SendResultList";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "VirtualArea::CBGServerMgr::SendResultList", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  v14.QuadPart = __rdtsc();
  functionInstrument.m_startTime = v14;
  v17 = 0;
  if ( lpCharacter )
  {
    Myfirst = this->m_lstBGServerMap._Myfirst;
    szBuffer[12] = 1;
    szBuffer[13] = 0;
    v6 = 0;
    v7 = &szBuffer[18];
    do
    {
      if ( Myfirst )
        v8 = this->m_lstBGServerMap._Mylast - Myfirst;
      else
        v8 = 0;
      if ( v6 >= v8 )
        break;
      v9 = Myfirst[v6];
      if ( v9 )
      {
        *(v7 - 4) = v9->m_ResultInfo.m_cWinNation;
        *(v7 - 3) = 0;
        *(_WORD *)v7 = 0;
        *((_WORD *)v7 + 1) = v9->m_ResultInfo.m_wScore[0];
        *((_WORD *)v7 + 2) = v9->m_ResultInfo.m_wScore[1];
      }
      ++v6;
      v7 += 10;
      ++szBuffer[13];
    }
    while ( v6 < 0xAu );
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
      LOWORD(m_lpGameClientDispatch) = (unsigned __int8)szBuffer[13];
      v12 = CSendStream::WrapCompress(
              p_m_SendStream,
              szBuffer,
              (char *)(10 * (_DWORD)m_lpGameClientDispatch + 14),
              0x99u,
              0,
              0);
      v17 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return v12;
    }
    else
    {
      v17 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return 1;
    }
  }
  else
  {
    v17 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
}
// 49E4B0: could not find valid save-restore pair for ebx
// 49E4B0: could not find valid save-restore pair for esi

//----- (0049E670) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::SendMapListToAllCharacter(VirtualArea::CBGServerMgr *this)
{
  CPerformanceCheck *Instance; // eax
  VirtualArea::CBGServerMap **Myfirst; // esi
  unsigned __int8 v4; // bl
  char *v5; // ecx
  unsigned int v6; // eax
  VirtualArea::CBGServerMap *v7; // eax
  char m_cRemainPlayMin; // dl
  CCreatureManager *v9; // eax
  unsigned int v11; // [esp-4h] [ebp-158h]
  unsigned int dwDstLength; // [esp+Ch] [ebp-148h] BYREF
  CPerformanceInstrument functionInstrument; // [esp+10h] [ebp-144h] BYREF
  _LARGE_INTEGER v14; // [esp+28h] [ebp-12Ch]
  CAutoInstrument autofunctionInstrument; // [esp+30h] [ebp-124h]
  char szSrcBuffer[136]; // [esp+34h] [ebp-120h] BYREF
  char szDstBuffer[134]; // [esp+BCh] [ebp-98h] BYREF
  int v18; // [esp+150h] [ebp-4h]

  functionInstrument.m_szfunctionName = "VirtualArea::CBGServerMgr::SendMapListToAllCharacter";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "VirtualArea::CBGServerMgr::SendMapListToAllCharacter", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  v14.QuadPart = __rdtsc();
  functionInstrument.m_startTime = v14;
  v18 = 0;
  if ( LOWORD(CCreatureManager::GetInstance()->m_CharacterMap._Mysize) )
  {
    Myfirst = this->m_lstBGServerMap._Myfirst;
    szSrcBuffer[12] = 1;
    szSrcBuffer[13] = 0;
    v4 = 0;
    v5 = &szSrcBuffer[16];
    do
    {
      if ( Myfirst )
        v6 = this->m_lstBGServerMap._Mylast - Myfirst;
      else
        v6 = 0;
      if ( v4 >= v6 )
        break;
      v7 = Myfirst[v4];
      if ( v7 )
      {
        *(v5 - 2) = v7->m_cStatus == 1;
        *(v5 - 1) = v7->m_MapInfo.m_cMapType;
        *v5 = v7->m_MapInfo.m_cMaxCharNumOfNation;
        *((_WORD *)v5 + 1) = v7->m_MapInfo.m_wTargetScore;
        if ( v7->m_cStatus == 1 )
          m_cRemainPlayMin = v7->m_MapInfo.m_cRemainPlayMin;
        else
          m_cRemainPlayMin = v7->m_MapInfo.m_cRemainRestMin;
        v5[1] = m_cRemainPlayMin;
        v5[4] = v7->m_MapInfo.m_cCurrentCharNum[0];
        v5[5] = v7->m_MapInfo.m_cCurrentCharNum[1];
        *((_WORD *)v5 + 3) = v7->m_MapInfo.m_wScore[0];
        *((_WORD *)v5 + 4) = v7->m_MapInfo.m_wScore[1];
      }
      ++v4;
      v5 += 12;
      ++szSrcBuffer[13];
    }
    while ( v4 < 0xAu );
    dwDstLength = 134;
    if ( PacketWrap::WrapCompress(
           (PktBase *)szDstBuffer,
           &dwDstLength,
           szSrcBuffer,
           12 * (unsigned __int8)szSrcBuffer[13] + 14,
           0x98u,
           0,
           0) )
    {
      v11 = dwDstLength;
      v9 = CCreatureManager::GetInstance();
      CCreatureManager::SendAllCharacter(v9, szDstBuffer, v11, 0x98u, 0);
    }
  }
  v18 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
  return 1;
}
// 49E670: could not find valid save-restore pair for ebx
// 49E670: could not find valid save-restore pair for esi

//----- (0049E850) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::LoginAllMonster(VirtualArea::CBGServerMgr *this)
{
  unsigned int i; // ebx
  VirtualArea::CBGServerMap **Myfirst; // ecx
  VirtualArea::CBGServerMap *v4; // eax
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  char *m_szArrangementFileName; // edi
  CCellManager *v8; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-14h]

  for ( i = 0; ; ++i )
  {
    Myfirst = this->m_lstBGServerMap._Myfirst;
    if ( !Myfirst || i >= this->m_lstBGServerMap._Mylast - Myfirst )
      break;
    v4 = Myfirst[i];
    if ( v4 )
    {
      m_dwVID = v4->m_dwVID;
      Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
      if ( VirtualAreaProtoType )
      {
        m_szArrangementFileName = VirtualAreaProtoType->m_szArrangementFileName;
        if ( VirtualAreaProtoType->m_szArrangementFileName[0] )
        {
          VirtualArea::CVirtualArea::CreateVirtualMonsterManager(this->m_lstBGServerMap._Myfirst[i]);
          v8 = CCellManager::GetInstance();
          CCellManager::LoginMonster(v8, m_szArrangementFileName, (i + 1) | 0x8000);
        }
      }
    }
  }
  return 1;
}

//----- (0049E8D0) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::ProcessAllMonster(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessAllMonster(*Myfirst);
  }
}

//----- (0049E900) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::ProcessMonsterRegenHPAndMP(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessMonsterRegenHPAndMP(*Myfirst);
  }
}

//----- (0049E930) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::ProcessSummonMonsterDead(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessSummonMonsterDead(*Myfirst);
  }
}

//----- (0049E960) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::ProcessDeleteItem(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessDeleteItem(*Myfirst);
  }
}

//----- (0049E990) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::ProcessAllCellPrepareBroadCast(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessAllCellPrepareBroadCast(*Myfirst);
  }
  return 1;
}

//----- (0049E9C0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::ProcessAllCellBroadCast(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CVirtualArea **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
      VirtualArea::CVirtualArea::ProcessAllCellBroadCast(*Myfirst);
  }
  return 1;
}

//----- (0049E9F0) --------------------------------------------------------
Quest::QuestNode **__thiscall std::vector<Quest::EventNode *>::_Ufill(
        std::vector<Quest::QuestNode *> *this,
        Quest::QuestNode **_Ptr,
        unsigned int _Count,
        Quest::QuestNode *const *_Val)
{
  Quest::QuestNode **v4; // eax
  unsigned int v5; // ecx

  v4 = _Ptr;
  if ( _Count )
  {
    v5 = _Count;
    do
    {
      *v4++ = *_Val;
      --v5;
    }
    while ( v5 );
  }
  return &_Ptr[_Count];
}

//----- (0049EA20) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::Process(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CBGServerMap **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // edi
  DWORD Time; // eax

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
    (*Myfirst)->Process(*Myfirst);
  Time = timeGetTime();
  if ( Time - s_dwLastTime > 0xEA60 )
  {
    s_dwLastTime = Time;
    VirtualArea::CBGServerMgr::SendMapListToAllCharacter(this);
  }
}

//----- (0049EA70) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::DestroyBGServerMap(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CBGServerMap **Myfirst; // esi
  VirtualArea::CBGServerMap **i; // ebx

  Myfirst = this->m_lstBGServerMap._Myfirst;
  for ( i = this->m_lstBGServerMap._Mylast; Myfirst != i; ++Myfirst )
  {
    if ( *Myfirst )
    {
      ((void (__thiscall *)(VirtualArea::CBGServerMap *, int))(*Myfirst)->~VirtualArea::CBGServerMap)(*Myfirst, 1);
      *Myfirst = 0;
    }
  }
  if ( this->m_lstBGServerMap._Myfirst )
    operator delete(this->m_lstBGServerMap._Myfirst);
  this->m_lstBGServerMap._Myfirst = 0;
  this->m_lstBGServerMap._Mylast = 0;
  this->m_lstBGServerMap._Myend = 0;
}

//----- (0049EAD0) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMgr::~CBGServerMgr(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CBGServerMgr::DestroyBGServerMap(this);
  if ( this->m_lstBGServerMap._Myfirst )
    operator delete(this->m_lstBGServerMap._Myfirst);
  this->m_lstBGServerMap._Myfirst = 0;
  this->m_lstBGServerMap._Mylast = 0;
  this->m_lstBGServerMap._Myend = 0;
}

//----- (0049EB30) --------------------------------------------------------
void __thiscall __noreturn std::vector<VirtualArea::CBGServerMap *>::_Xlen(
        std::vector<VirtualArea::CBGServerMap *> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0049EBA0) --------------------------------------------------------
void __thiscall std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
        std::vector<VirtualArea::CBGServerMap *> *this,
        std::vector<VirtualArea::CBGServerMap *>::iterator _Where,
        unsigned int _Count,
        VirtualArea::CBGServerMap *const *_Val)
{
  VirtualArea::CBGServerMap **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  VirtualArea::CBGServerMap **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  VirtualArea::CBGServerMap **v24; // ecx
  Quest::QuestNode **v25; // edi
  VirtualArea::CBGServerMap **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<VirtualArea::CBGServerMap *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (VirtualArea::CBGServerMap *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<VirtualArea::CBGServerMap *>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (VirtualArea::CBGServerMap **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (VirtualArea::CBGServerMap **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                                        &Mylast[-v23],
                                                        (int)Mylast,
                                                        Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (VirtualArea::CBGServerMap **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (VirtualArea::CBGServerMap **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 49EC84: variable 'v15' is possibly undefined

//----- (0049ED80) --------------------------------------------------------
VirtualArea::CBGServerMgr *__cdecl VirtualArea::CBGServerMgr::GetInstance()
{
  if ( (_S5_10 & 1) == 0 )
  {
    _S5_10 |= 1u;
    ms_this_2.m_lstBGServerMap._Myfirst = 0;
    ms_this_2.m_lstBGServerMap._Mylast = 0;
    ms_this_2.m_lstBGServerMap._Myend = 0;
    atexit(_E6_22);
  }
  return &ms_this_2;
}

//----- (0049EDE0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMgr::CreateBGServerMap(VirtualArea::CBGServerMgr *this)
{
  VirtualArea::CBGServerMap **Myfirst; // ecx
  VirtualArea::CBGServerMap *v3; // edi
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  VirtualArea::CBGServerMap *v6; // eax
  VirtualArea::CBGServerMap **v7; // edx
  unsigned int v8; // edi
  VirtualArea::CBGServerMap **Mylast; // ecx
  VirtualArea::CBGServerMap *v10; // edi
  VirtualArea::CVirtualAreaMgr *v11; // eax
  const VirtualArea::ProtoType *v12; // eax
  VirtualArea::CBGServerMap *v13; // eax
  VirtualArea::CBGServerMap **v14; // edx
  unsigned int v15; // edi
  VirtualArea::CBGServerMap **v16; // ecx
  VirtualArea::CBGServerMap *v17; // edi
  VirtualArea::CVirtualAreaMgr *v18; // eax
  const VirtualArea::ProtoType *v19; // eax
  VirtualArea::CBGServerMap *v20; // eax
  VirtualArea::CBGServerMap **v21; // edx
  unsigned int v22; // edi
  VirtualArea::CBGServerMap **v23; // ecx
  VirtualArea::CBGServerMap *v24; // edi
  VirtualArea::CVirtualAreaMgr *v25; // eax
  const VirtualArea::ProtoType *v26; // eax
  VirtualArea::CBGServerMap *v27; // eax
  VirtualArea::CBGServerMap **v28; // edx
  unsigned int v29; // edi
  VirtualArea::CBGServerMap **v30; // ecx
  VirtualArea::CBGServerMap *v31; // edi
  VirtualArea::CVirtualAreaMgr *v32; // eax
  const VirtualArea::ProtoType *v33; // eax
  VirtualArea::CBGServerMap *v34; // eax
  VirtualArea::CBGServerMap **v35; // edx
  unsigned int v36; // edi
  VirtualArea::CBGServerMap **v37; // ecx
  VirtualArea::CBGServerMap *v38; // edi
  VirtualArea::CVirtualAreaMgr *v39; // eax
  const VirtualArea::ProtoType *v40; // eax
  VirtualArea::CBGServerMap *v41; // eax
  VirtualArea::CBGServerMap **v42; // edx
  unsigned int v43; // edi
  VirtualArea::CBGServerMap **v44; // ecx
  VirtualArea::CBGServerMap *v45; // edi
  VirtualArea::CVirtualAreaMgr *v46; // eax
  const VirtualArea::ProtoType *v47; // eax
  VirtualArea::CBGServerMap *v48; // eax
  VirtualArea::CBGServerMap **v49; // edx
  unsigned int v50; // edi
  VirtualArea::CBGServerMap **v51; // ecx
  VirtualArea::CBGServerMap *v52; // edi
  VirtualArea::CVirtualAreaMgr *v53; // eax
  const VirtualArea::ProtoType *v54; // eax
  VirtualArea::CBGServerMap *v55; // eax
  VirtualArea::CBGServerMap **v56; // edx
  unsigned int v57; // edi
  VirtualArea::CBGServerMap **v58; // ecx
  VirtualArea::CBGServerMap **v59; // edi
  VirtualArea::CBGServerMap **i; // esi
  VirtualArea::CBGServerMap *_Val; // [esp+8h] [ebp-10h] BYREF
  int v63; // [esp+14h] [ebp-4h]

  Myfirst = this->m_lstBGServerMap._Myfirst;
  if ( Myfirst && this->m_lstBGServerMap._Mylast - Myfirst )
    VirtualArea::CBGServerMgr::DestroyBGServerMap(this);
  v3 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v3;
  v63 = 0;
  if ( v3 )
  {
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v3, VirtualAreaProtoType, 1u);
  }
  else
  {
    v6 = 0;
  }
  v7 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v6;
  if ( v7 )
    v8 = this->m_lstBGServerMap._Mylast - v7;
  else
    v8 = 0;
  if ( v7 && v8 < this->m_lstBGServerMap._Myend - v7 )
  {
    Mylast = this->m_lstBGServerMap._Mylast;
    *Mylast = v6;
    this->m_lstBGServerMap._Mylast = Mylast + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v10 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v10;
  v63 = 1;
  if ( v10 )
  {
    v11 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v12 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v11, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v10, v12, 2u);
  }
  else
  {
    v13 = 0;
  }
  v14 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v13;
  if ( v14 )
    v15 = this->m_lstBGServerMap._Mylast - v14;
  else
    v15 = 0;
  if ( v14 && v15 < this->m_lstBGServerMap._Myend - v14 )
  {
    v16 = this->m_lstBGServerMap._Mylast;
    *v16 = v13;
    this->m_lstBGServerMap._Mylast = v16 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v17 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v17;
  v63 = 2;
  if ( v17 )
  {
    v18 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v19 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v18, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v17, v19, 3u);
  }
  else
  {
    v20 = 0;
  }
  v21 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v20;
  if ( v21 )
    v22 = this->m_lstBGServerMap._Mylast - v21;
  else
    v22 = 0;
  if ( v21 && v22 < this->m_lstBGServerMap._Myend - v21 )
  {
    v23 = this->m_lstBGServerMap._Mylast;
    *v23 = v20;
    this->m_lstBGServerMap._Mylast = v23 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v24 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v24;
  v63 = 3;
  if ( v24 )
  {
    v25 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v26 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v25, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v24, v26, 4u);
  }
  else
  {
    v27 = 0;
  }
  v28 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v27;
  if ( v28 )
    v29 = this->m_lstBGServerMap._Mylast - v28;
  else
    v29 = 0;
  if ( v28 && v29 < this->m_lstBGServerMap._Myend - v28 )
  {
    v30 = this->m_lstBGServerMap._Mylast;
    *v30 = v27;
    this->m_lstBGServerMap._Mylast = v30 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v31 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v31;
  v63 = 4;
  if ( v31 )
  {
    v32 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v33 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v32, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v31, v33, 5u);
  }
  else
  {
    v34 = 0;
  }
  v35 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v34;
  if ( v35 )
    v36 = this->m_lstBGServerMap._Mylast - v35;
  else
    v36 = 0;
  if ( v35 && v36 < this->m_lstBGServerMap._Myend - v35 )
  {
    v37 = this->m_lstBGServerMap._Mylast;
    *v37 = v34;
    this->m_lstBGServerMap._Mylast = v37 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v38 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v38;
  v63 = 5;
  if ( v38 )
  {
    v39 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v40 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v39, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v38, v40, 6u);
  }
  else
  {
    v41 = 0;
  }
  v42 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v41;
  if ( v42 )
    v43 = this->m_lstBGServerMap._Mylast - v42;
  else
    v43 = 0;
  if ( v42 && v43 < this->m_lstBGServerMap._Myend - v42 )
  {
    v44 = this->m_lstBGServerMap._Mylast;
    *v44 = v41;
    this->m_lstBGServerMap._Mylast = v44 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v45 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v45;
  v63 = 6;
  if ( v45 )
  {
    v46 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v47 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v46, "BG_FRAG");
    VirtualArea::CBGServerMap::CBGServerMap(v45, v47, 7u);
  }
  else
  {
    v48 = 0;
  }
  v49 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v48;
  if ( v49 )
    v50 = this->m_lstBGServerMap._Mylast - v49;
  else
    v50 = 0;
  if ( v49 && v50 < this->m_lstBGServerMap._Myend - v49 )
  {
    v51 = this->m_lstBGServerMap._Mylast;
    *v51 = v48;
    this->m_lstBGServerMap._Mylast = v51 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  v52 = (VirtualArea::CBGServerMap *)operator new((tagHeader *)0x54);
  _Val = v52;
  v63 = 7;
  if ( v52 )
  {
    v53 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v54 = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(v53, "BG_STATUE");
    VirtualArea::CBGServerMap::CBGServerMap(v52, v54, 8u);
  }
  else
  {
    v55 = 0;
  }
  v56 = this->m_lstBGServerMap._Myfirst;
  v63 = -1;
  _Val = v55;
  if ( v56 )
    v57 = this->m_lstBGServerMap._Mylast - v56;
  else
    v57 = 0;
  if ( v56 && v57 < this->m_lstBGServerMap._Myend - v56 )
  {
    v58 = this->m_lstBGServerMap._Mylast;
    *v58 = v55;
    this->m_lstBGServerMap._Mylast = v58 + 1;
  }
  else
  {
    std::vector<VirtualArea::CBGServerMap *>::_Insert_n(
      &this->m_lstBGServerMap,
      (std::vector<VirtualArea::CBGServerMap *>::iterator)this->m_lstBGServerMap._Mylast,
      1u,
      &_Val);
  }
  VirtualArea::CBGServerMgr::LoginAllMonster(this);
  v59 = this->m_lstBGServerMap._Mylast;
  for ( i = this->m_lstBGServerMap._Myfirst; i != v59; ++i )
    VirtualArea::CBGServerMap::GameStart(*i);
  return 1;
}
// 49EE5C: variable 'v6' is possibly undefined
// 49EEE1: variable 'v13' is possibly undefined
// 49EF66: variable 'v20' is possibly undefined
// 49EFEB: variable 'v27' is possibly undefined
// 49F070: variable 'v34' is possibly undefined
// 49F0F5: variable 'v41' is possibly undefined
// 49F17A: variable 'v48' is possibly undefined
// 49F1FF: variable 'v55' is possibly undefined

//----- (0049F280) --------------------------------------------------------
void __thiscall VirtualArea::ResultInfo::Initialize(VirtualArea::ResultInfo *this)
{
  this->m_cWinNation = 2;
  *(_DWORD *)this->m_wScore = 0;
}

//----- (0049F290) --------------------------------------------------------
void __thiscall VirtualArea::ResultInfo::ResultInfo(VirtualArea::ResultInfo *this)
{
  this->m_cWinNation = 2;
  *(_DWORD *)this->m_wScore = 0;
}

//----- (0049F2A0) --------------------------------------------------------
void __thiscall VirtualArea::MapInfo::Initialize(VirtualArea::MapInfo *this)
{
  unsigned __int8 m_cRestMin; // dl
  std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo> *p_m_PersonalInfoMap; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax

  m_cRestMin = this->m_cRestMin;
  this->m_cRemainPlayMin = this->m_cLimitMin;
  this->m_cRemainRestMin = m_cRestMin;
  *(_WORD *)this->m_cCurrentCharNum = 0;
  p_m_PersonalInfoMap = &this->m_PersonalInfoMap;
  *(_DWORD *)this->m_wScore = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(
    &this->m_PersonalInfoMap,
    this->m_PersonalInfoMap._Myhead->_Parent);
  p_m_PersonalInfoMap->_Myhead->_Parent = p_m_PersonalInfoMap->_Myhead;
  Myhead = p_m_PersonalInfoMap->_Myhead;
  p_m_PersonalInfoMap->_Mysize = 0;
  Myhead->_Left = Myhead;
  p_m_PersonalInfoMap->_Myhead->_Right = p_m_PersonalInfoMap->_Myhead;
}

//----- (0049F2F0) --------------------------------------------------------
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Node::_Node(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rarg,
        const std::pair<std::string const ,unsigned char> *_Val,
        char _Carg)
{
  std::pair<std::string const ,unsigned char> *p_Myval; // edi

  this->_Parent = _Parg;
  p_Myval = &this->_Myval;
  this->_Left = _Larg;
  this->_Right = _Rarg;
  this->_Myval.first._Myres = 15;
  this->_Myval.first._Mysize = 0;
  this->_Myval.first._Bx._Buf[0] = 0;
  std::string::assign(&this->_Myval.first, &_Val->first, 0, 0xFFFFFFFF);
  p_Myval->second = _Val->second;
  this->_Color = _Carg;
  this->_Isnil = 0;
}

//----- (0049F350) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rarg,
        const std::pair<std::string const ,unsigned char> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v6; // esi
  int v8; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v9; // [esp+Ch] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode; // [esp+10h] [ebp-14h]
  int *v11; // [esp+14h] [ebp-10h]
  int v12; // [esp+20h] [ebp-4h]

  v11 = &v8;
  v6 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)operator new((tagHeader *)0x30);
  _Wherenode = v6;
  v12 = 1;
  v9 = (CPacketDispatch *)v6;
  if ( v6 )
    std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Node::_Node(
      v6,
      _Larg,
      _Parg,
      _Rarg,
      _Val,
      _Carg);
  return v6;
}

//----- (0049F3E0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode,
        const std::pair<std::string const ,unsigned char> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<std::string const ,unsigned char> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x7FFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<std::string const ,unsigned char> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0049F590) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> *result,
        std::pair<std::string const ,unsigned char> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // ebx
  char v5; // al
  const std::pair<std::string const ,unsigned char> *v6; // edi
  unsigned int i; // ecx
  unsigned int Mysize; // ebx
  const char *Buf; // eax
  unsigned int v10; // edx
  int v11; // ecx
  const char *v12; // esi
  int v13; // eax
  bool v14; // sf
  int v15; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v16; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> *v18; // eax
  const char *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v20; // edx
  bool _Addleft; // [esp+10h] [ebp-14h]
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *v22; // [esp+14h] [ebp-10h]
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode; // [esp+20h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  v5 = 1;
  v6 = _Val;
  v22 = this;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    for ( i = _Val->first._Mysize; ; i = _Val->first._Mysize )
    {
      Mysize = Parent->_Myval.first._Mysize;
      _Wherenode = Parent;
      if ( Parent->_Myval.first._Myres < 0x10 )
        Buf = Parent->_Myval.first._Bx._Buf;
      else
        Buf = Parent->_Myval.first._Bx._Ptr;
      v10 = _Val->first._Mysize;
      if ( i < v10 )
        v10 = i;
      if ( !v10 )
        goto LABEL_16;
      v11 = v10;
      if ( v10 >= Mysize )
        v11 = Parent->_Myval.first._Mysize;
      v12 = v6->first._Myres < 0x10 ? (const char *)&v6->first._Bx : v6->first._Bx._Ptr;
      v13 = memcmp(v12, Buf, v11);
      v14 = v13 < 0;
      v6 = _Val;
      if ( !v13 )
      {
LABEL_16:
        if ( v10 >= Mysize )
          v15 = v10 != Mysize;
        else
          v15 = -1;
        v14 = v15 < 0;
      }
      v5 = v14;
      _Addleft = v14;
      Parent = v14 ? Parent->_Left : Parent->_Right;
      if ( Parent->_Isnil )
        break;
    }
    Myhead = _Wherenode;
    this = v22;
  }
  v16 = Myhead;
  _Val = (std::pair<std::string const ,unsigned char> *)Myhead;
  if ( v5 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v6)->_Ptr;
      v18 = result;
      result->first._Ptr = Ptr;
      result->second = 1;
      return v18;
    }
    std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&_Val);
    v16 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)_Val;
  }
  if ( v6->first._Myres < 0x10 )
    v19 = v6->first._Bx._Buf;
  else
    v19 = v6->first._Bx._Ptr;
  if ( std::string::compare(&v16->_Myval.first, 0, v16->_Myval.first._Mysize, v19, v6->first._Mysize) >= 0 )
  {
    v18 = result;
    result->first._Ptr = v16;
    result->second = 0;
  }
  else
  {
    v20 = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Insert(
            v22,
            (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v6)->_Ptr;
    v18 = result;
    result->first._Ptr = v20;
    result->second = 1;
  }
  return v18;
}

//----- (0049F710) --------------------------------------------------------
void __thiscall VirtualArea::MapInfo::MapInfo(VirtualArea::MapInfo *this, unsigned __int8 cMapType)
{
  std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo> *p_m_PersonalInfoMap; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v4; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax

  this->m_cMapType = cMapType;
  this->m_cMaxCharNumOfNation = DefaultMaxCharacterNumOfNation[cMapType];
  this->m_cRemainPlayMin = DefaultLimitMin[cMapType];
  this->m_cRemainRestMin = DefaultRestMin[cMapType];
  this->m_wTargetScore = DefaultTargetScore[cMapType];
  this->m_cLimitMin = DefaultLimitMin[cMapType];
  p_m_PersonalInfoMap = &this->m_PersonalInfoMap;
  this->m_cRestMin = DefaultRestMin[cMapType];
  v4 = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_PersonalInfoMap);
  p_m_PersonalInfoMap->_Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)v4;
  v4->_Isnil = 1;
  p_m_PersonalInfoMap->_Myhead->_Parent = p_m_PersonalInfoMap->_Myhead;
  p_m_PersonalInfoMap->_Myhead->_Left = p_m_PersonalInfoMap->_Myhead;
  p_m_PersonalInfoMap->_Myhead->_Right = p_m_PersonalInfoMap->_Myhead;
  p_m_PersonalInfoMap->_Mysize = 0;
  *(_WORD *)this->m_cCurrentCharNum = 0;
  *(_DWORD *)this->m_wScore = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(
    p_m_PersonalInfoMap,
    p_m_PersonalInfoMap->_Myhead->_Parent);
  p_m_PersonalInfoMap->_Myhead->_Parent = p_m_PersonalInfoMap->_Myhead;
  Myhead = p_m_PersonalInfoMap->_Myhead;
  p_m_PersonalInfoMap->_Mysize = 0;
  Myhead->_Left = Myhead;
  p_m_PersonalInfoMap->_Myhead->_Right = p_m_PersonalInfoMap->_Myhead;
}

//----- (0049F7E0) --------------------------------------------------------
void __thiscall VirtualArea::MapTypeMatching::MapTypeMatching(VirtualArea::MapTypeMatching *this)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // [esp-4h] [ebp-4Ch]
  std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> result; // [esp+14h] [ebp-34h] BYREF
  std::pair<std::string const ,unsigned char> v6; // [esp+1Ch] [ebp-2Ch] BYREF
  int v7; // [esp+44h] [ebp-4h]

  v2 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(&this->m_matchMap);
  this->m_matchMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_matchMap._Myhead->_Parent = this->m_matchMap._Myhead;
  this->m_matchMap._Myhead->_Left = this->m_matchMap._Myhead;
  this->m_matchMap._Myhead->_Right = this->m_matchMap._Myhead;
  this->m_matchMap._Mysize = 0;
  Parent = this->m_matchMap._Myhead->_Parent;
  v7 = 0;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Erase(
    &this->m_matchMap,
    Parent);
  this->m_matchMap._Myhead->_Parent = this->m_matchMap._Myhead;
  Myhead = this->m_matchMap._Myhead;
  this->m_matchMap._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_matchMap._Myhead->_Right = this->m_matchMap._Myhead;
  v6.first._Myres = 15;
  v6.first._Mysize = 0;
  v6.first._Bx._Buf[0] = 0;
  std::string::assign(&v6.first, "BG_FRAG", strlen("BG_FRAG"));
  v6.second = 0;
  LOBYTE(v7) = 1;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(
    &this->m_matchMap,
    &result,
    &v6);
  LOBYTE(v7) = 0;
  if ( v6.first._Myres >= 0x10 )
    operator delete(v6.first._Bx._Ptr);
  v6.first._Myres = 15;
  v6.first._Mysize = 0;
  v6.first._Bx._Buf[0] = 0;
  std::string::assign(&v6.first, "BG_STATUE", strlen("BG_STATUE"));
  v6.second = 1;
  LOBYTE(v7) = 2;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(
    &this->m_matchMap,
    &result,
    &v6);
  LOBYTE(v7) = 0;
  if ( v6.first._Myres >= 0x10 )
    operator delete(v6.first._Bx._Ptr);
  v6.first._Myres = 15;
  v6.first._Mysize = 0;
  v6.first._Bx._Buf[0] = 0;
  std::string::assign(&v6.first, "DUEL_FRAG", strlen("DUEL_FRAG"));
  v6.second = 0;
  LOBYTE(v7) = 3;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(
    &this->m_matchMap,
    &result,
    &v6);
  LOBYTE(v7) = 0;
  if ( v6.first._Myres >= 0x10 )
    operator delete(v6.first._Bx._Ptr);
  v6.first._Myres = 15;
  v6.first._Mysize = 0;
  v6.first._Bx._Buf[0] = 0;
  std::string::assign(&v6.first, "DUEL_STATUE", strlen("DUEL_STATUE"));
  v6.second = 1;
  LOBYTE(v7) = 4;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(
    &this->m_matchMap,
    &result,
    &v6);
  if ( v6.first._Myres >= 0x10 )
    operator delete(v6.first._Bx._Ptr);
}

//----- (0049F9E0) --------------------------------------------------------
VirtualArea::CDungeonMgr *__cdecl VirtualArea::CDungeonMgr::GetInstance()
{
  return &ms_this_3;
}

//----- (0049F9F0) --------------------------------------------------------
VirtualArea::CDuelMgr *__cdecl VirtualArea::CDuelMgr::GetInstance()
{
  return &ms_this_4;
}

//----- (0049FA10) --------------------------------------------------------
void __thiscall CMonster::AttackAction(CMonster *this)
{
  CMonster_vtbl *v2; // eax

  this->m_MotionInfo.m_fDirection = CAggresiveCreature::CalcDir2D(
                                      this,
                                      this->m_CurrentPos.m_fPointX,
                                      this->m_CurrentPos.m_fPointZ,
                                      this->m_lpTarget->m_CurrentPos.m_fPointX,
                                      this->m_lpTarget->m_CurrentPos.m_fPointZ);
  if ( !CMonster::MultiAttack(this) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::AttackAction",
      aDWorkRylSource_2,
      959,
      aCid0x08x_63,
      this->m_dwCID);
  v2 = this->__vftable;
  this->m_bAttacking = 1;
  this->m_bLongRangeAttacked = 0;
  v2->GetMotion(this, 2u, &this->m_MotionInfo);
  this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
  this->m_MotionInfo.m_fVelocity = 0.0;
  CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
  this->m_nLeaveMovingNum = 0;
}

//----- (0049FAB0) --------------------------------------------------------
bool __thiscall CMonster::IsReturn(CMonster *this)
{
  double v1; // st7
  double v2; // st6
  unsigned int v3; // eax
  float fOriginalDistance; // [esp+4h] [ebp-4h]

  v1 = this->m_OriginalPosition.m_fPointX - this->m_CurrentPos.m_fPointX;
  v2 = this->m_OriginalPosition.m_fPointZ - this->m_CurrentPos.m_fPointZ;
  if ( this->m_bLongRangeAttacked )
    v3 = Math::Random::ComplexRandom(6, 0) + 43;
  else
    v3 = Math::Random::ComplexRandom(6, 0) + 18;
  fOriginalDistance = v2 * v2 + v1 * v1;
  return (double)(int)(v3 * v3) < fOriginalDistance;
}

//----- (0049FB20) --------------------------------------------------------
void __thiscall CSkillMonster::CastingAttackAction(CSkillMonster *this)
{
  CCreatureManager *Instance; // eax
  CMsgProc *AggresiveCreature; // eax
  int v4; // ecx
  unsigned __int8 m_cSkillPattern; // al
  CAggresiveCreature *m_lpTarget; // eax
  int v7; // eax
  CSkillMonster_vtbl *v8; // edx
  unsigned int m_dwTargetCID; // [esp-4h] [ebp-Ch]

  if ( this->m_lCurrentFrame <= 0 )
  {
    m_cSkillPattern = this->m_MonsterInfo.m_cSkillPattern;
    if ( (m_cSkillPattern == 10 || m_cSkillPattern == 3 || m_cSkillPattern == 4) && this->m_nCastingCount > 0 )
    {
      m_lpTarget = this->m_lpTarget;
      if ( m_lpTarget )
      {
        this->m_MotionInfo.m_fDirection = CAggresiveCreature::CalcDir2D(
                                            this,
                                            this->m_CurrentPos.m_fPointX,
                                            this->m_CurrentPos.m_fPointZ,
                                            m_lpTarget->m_CurrentPos.m_fPointX,
                                            m_lpTarget->m_CurrentPos.m_fPointZ);
        this->GetMotion(this, 24u, &this->m_MotionInfo);
        this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
        this->m_MotionInfo.m_fVelocity = 0.0;
        CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
        v7 = this->m_nCastingCount - 1;
        this->m_nLeaveMovingNum = 0;
        this->m_nCastingCount = v7;
      }
      else
      {
        this->m_dwTargetCID = 0;
        this->m_bCasting = 0;
        this->m_nCastingCount = 0;
        CMonster::CancelTarget(this);
      }
    }
    else
    {
      CSkillMonster::UseCastedSkill(this);
      v8 = this->__vftable;
      this->m_bCasting = 0;
      this->m_bAttacking = 1;
      this->m_bLongRangeAttacked = 0;
      this->m_nCastingCount = 0;
      v8->GetMotion(this, 2u, &this->m_MotionInfo);
      this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
    }
  }
  else
  {
    m_dwTargetCID = this->m_dwTargetCID;
    Instance = CCreatureManager::GetInstance();
    AggresiveCreature = CCreatureManager::GetAggresiveCreature(Instance, m_dwTargetCID);
    if ( !AggresiveCreature
      || LOWORD(AggresiveCreature[101].__vftable) != this->m_CellPos.m_wMapIndex
      || ((int)AggresiveCreature[103].__vftable & 0x40000000) != 0
      || !Creature::GetCreatureType((int)AggresiveCreature[8].__vftable) && *(_DWORD *)(v4 + 1516) )
    {
      this->m_dwTargetCID = 0;
      this->m_lpTarget = 0;
      this->m_bCasting = 0;
      CMonster::CancelTarget(this);
    }
  }
}
// 49FB75: variable 'v4' is possibly undefined

//----- (0049FC80) --------------------------------------------------------
void __thiscall CSkillMonster::SkillAttackAction(CSkillMonster *this)
{
  CAggresiveCreature *m_lpTarget; // eax
  CSkillMonster_vtbl *v3; // edx
  unsigned int v4; // ecx
  MotionInfo *p_m_MotionInfo; // [esp-4h] [ebp-Ch]

  m_lpTarget = this->m_lpTarget;
  this->m_bAttacking = 1;
  this->m_MotionInfo.m_fDirection = CAggresiveCreature::CalcDir2D(
                                      this,
                                      this->m_CurrentPos.m_fPointX,
                                      this->m_CurrentPos.m_fPointZ,
                                      m_lpTarget->m_CurrentPos.m_fPointX,
                                      m_lpTarget->m_CurrentPos.m_fPointZ);
  v3 = this->__vftable;
  p_m_MotionInfo = &this->m_MotionInfo;
  if ( this->m_bCasting )
  {
    v3->GetMotion(this, 24u, p_m_MotionInfo);
    this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
    this->m_MotionInfo.m_fVelocity = 0.0;
    CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
    v4 = *((unsigned __int8 *)&this->m_attackType + 2);
    this->m_nLeaveMovingNum = 0;
    this->m_nCastingCount = v4 >> 4;
  }
  else
  {
    v3->GetMotion(this, 2u, p_m_MotionInfo);
    this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
    this->m_MotionInfo.m_fVelocity = 0.0;
    CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
    this->m_nCastingCount = 0;
    this->m_nLeaveMovingNum = 0;
  }
}

//----- (0049FD30) --------------------------------------------------------
Position *__thiscall CMonster::CalculateCoor(CMonster *this, Position *result)
{
  double m_fVelocity; // st7
  Position *v3; // eax
  long double v4; // st6
  double m_fPointY; // st5

  m_fVelocity = this->m_MotionInfo.m_fVelocity;
  if ( (this->m_dwStatusFlag & 0x1000) != 0 )
    m_fVelocity = m_fVelocity * 0.25;
  v3 = result;
  v4 = sin(this->m_MotionInfo.m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
  m_fPointY = this->m_CurrentPos.m_fPointY;
  result->m_fPointX = cos(this->m_MotionInfo.m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointX;
  result->m_fPointY = m_fPointY;
  result->m_fPointZ = v4;
  return v3;
}

//----- (0049FD70) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CMonster::ReturnBehavior(CMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  bool v4; // cc
  CMonster_vtbl *v5; // eax
  double v6; // st7
  double v7; // st6
  unsigned __int64 v8; // rax
  double m_fVelocity; // st7
  unsigned int m_dwStatusFlag; // eax
  long double m_fDirection; // st6
  long double v12; // st6
  double m_fPointY; // st5
  int m_nCurrentState; // eax
  float v15; // [esp-Ch] [ebp-50h]
  float m_fPointZ; // [esp-8h] [ebp-4Ch]
  float m_fPointX; // [esp-4h] [ebp-48h]
  float fEstimateTime; // [esp+0h] [ebp-44h]
  _LARGE_INTEGER fOriginalDistance; // [esp+10h] [ebp-34h] OVERLAPPED BYREF
  float v20; // [esp+18h] [ebp-2Ch]
  CAutoInstrument autofunctionInstrument; // [esp+1Ch] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+20h] [ebp-24h] BYREF
  int v23; // [esp+40h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CMonster::ReturnBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMonster::ReturnBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  fOriginalDistance.QuadPart = __rdtsc();
  functionInstrument.m_startTime = fOriginalDistance;
  v4 = this->m_nLeaveMovingNum <= 0;
  v23 = 0;
  if ( v4 )
  {
    v5 = this->__vftable;
    this->m_MotionInfo.m_wAction = 1;
    v5->GetMotion(this, 1u, &this->m_MotionInfo);
    v6 = this->m_OriginalPosition.m_fPointX - this->m_CurrentPos.m_fPointX;
    v7 = this->m_OriginalPosition.m_fPointZ - this->m_CurrentPos.m_fPointZ;
    fEstimateTime = this->m_OriginalPosition.m_fPointZ;
    m_fPointX = this->m_OriginalPosition.m_fPointX;
    m_fPointZ = this->m_CurrentPos.m_fPointZ;
    v15 = this->m_CurrentPos.m_fPointX;
    *(float *)&fOriginalDistance.LowPart = v7 * v7 + v6 * v6;
    this->m_MotionInfo.m_fDirection = CAggresiveCreature::CalcDir2D(this, v15, m_fPointZ, m_fPointX, fEstimateTime);
    v8 = (unsigned __int64)(sqrt(*(float *)&fOriginalDistance.LowPart) / this->m_MotionInfo.m_fVelocity);
    if ( (unsigned __int16)v8 <= 3u )
    {
      if ( !(_WORD)v8 )
      {
        m_nCurrentState = this->m_nCurrentState;
        this->m_wSearchRange = this->m_wDefaultSearchRange;
        this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 104);
        CThreat::ClearThreatList(&this->m_Threat);
        CMonster::CancelTarget(this);
        goto LABEL_10;
      }
    }
    else
    {
      LOWORD(v8) = 3;
    }
    CMonster::ms_ReturnBehaviorSendCount += CMonster::SendMove(this, v8);
  }
  m_fVelocity = this->m_MotionInfo.m_fVelocity;
  m_dwStatusFlag = this->m_dwStatusFlag;
  --this->m_nLeaveMovingNum;
  if ( (m_dwStatusFlag & 0x1000) != 0 )
    m_fVelocity = m_fVelocity * 0.25;
  m_fDirection = this->m_MotionInfo.m_fDirection;
  *(float *)&fOriginalDistance.LowPart = cos(m_fDirection);
  v12 = sin(m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
  m_fPointY = this->m_CurrentPos.m_fPointY;
  *(float *)&fOriginalDistance.LowPart = *(float *)&fOriginalDistance.LowPart * m_fVelocity
                                       + this->m_CurrentPos.m_fPointX;
  *(float *)&fOriginalDistance.HighPart = m_fPointY;
  v20 = v12;
  if ( CAggresiveCreature::MoveTo(this, (const Position *)&fOriginalDistance, 0) == 3 )
  {
    CMonster::ms_ReturnBehaviorSendCount += CMonster::SendMove(this, 1u);
    this->m_nLeaveMovingNum = 0;
  }
  this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
LABEL_10:
  v23 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}
// 49FD70: variables would overlap: ^1C.8 and stkvar "fOriginalDistance" ^1C.4(has user info)

//----- (0049FF60) --------------------------------------------------------
void __thiscall CMonster::EscapeBehavior(CMonster *this)
{
  CPerformanceCheck *Instance; // eax
  bool v3; // zf
  CMonster_vtbl *v4; // edx
  MotionInfo *p_m_MotionInfo; // edi
  double v6; // st7
  double v7; // st6
  double m_fVelocity; // st7
  long double m_fDirection; // st6
  long double v10; // st6
  double m_fPointY; // st5
  unsigned int m_dwFrame; // eax
  Position NewPosition; // [esp+10h] [ebp-34h] BYREF
  CAutoInstrument autofunctionInstrument; // [esp+1Ch] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+20h] [ebp-24h] BYREF
  int v16; // [esp+40h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CMonster::EscapeBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMonster::EscapeBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  *(_QWORD *)&NewPosition.m_fPointX = __rdtsc();
  functionInstrument.m_startTime.QuadPart = *(_QWORD *)&NewPosition.m_fPointX;
  v3 = this->m_lpTarget == 0;
  v16 = 0;
  if ( v3 )
  {
    this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 103);
    v16 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
  }
  else
  {
    v4 = this->__vftable;
    p_m_MotionInfo = &this->m_MotionInfo;
    this->m_MotionInfo.m_wAction = 1;
    v4->GetMotion(this, 1u, &this->m_MotionInfo);
    v6 = CAggresiveCreature::CalcDir2D(
           this,
           this->m_CurrentPos.m_fPointX,
           this->m_CurrentPos.m_fPointZ,
           this->m_lpTarget->m_CurrentPos.m_fPointX,
           this->m_lpTarget->m_CurrentPos.m_fPointZ)
       + PI_35;
    this->m_MotionInfo.m_fDirection = v6;
    v7 = PI_35 + PI_35;
    if ( v6 >= v7 )
      p_m_MotionInfo->m_fDirection = v6 - v7;
    CMonster::ms_EscapeBehaviorSendCount += CMonster::SendMove(this, 1u);
    m_fVelocity = this->m_MotionInfo.m_fVelocity;
    if ( (this->m_dwStatusFlag & 0x1000) != 0 )
      m_fVelocity = m_fVelocity * 0.25;
    m_fDirection = p_m_MotionInfo->m_fDirection;
    NewPosition.m_fPointX = cos(m_fDirection);
    v10 = sin(m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
    m_fPointY = this->m_CurrentPos.m_fPointY;
    NewPosition.m_fPointX = NewPosition.m_fPointX * m_fVelocity + this->m_CurrentPos.m_fPointX;
    NewPosition.m_fPointY = m_fPointY;
    NewPosition.m_fPointZ = v10;
    CAggresiveCreature::MoveTo(this, &NewPosition, 0);
    m_dwFrame = this->m_MotionInfo.m_dwFrame;
    this->m_nLeaveMovingNum = 0;
    this->m_lCurrentFrame = m_dwFrame;
    v16 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
  }
}

//----- (004A0110) --------------------------------------------------------
void __thiscall CMonster::DeadBehavior(CMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  unsigned int v4; // ecx
  CMonster_vtbl *v5; // edx
  CPerformanceInstrument functionInstrument; // [esp+1Ch] [ebp-24h] BYREF
  int v7; // [esp+3Ch] [ebp-4h]

  functionInstrument.m_szfunctionName = "CMonster::DeadBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMonster::DeadBehavior", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  v4 = this->m_dwLastTime + this->m_MonsterInfo.m_dwRespawnTime;
  v7 = 0;
  if ( dwTick >= v4 )
  {
    v5 = this->__vftable;
    this->m_nLeaveMovingNum = 0;
    v5->Respawn(this, dwTick);
  }
  else
  {
    this->m_lCurrentFrame = 30;
  }
  v7 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}

//----- (004A01E0) --------------------------------------------------------
void __thiscall CSummonMonster::ReturnBehavior(CSummonMonster *this, unsigned int dwTick)
{
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 104);
  CThreat::ClearThreatList(&this->m_Threat);
  CMonster::CancelTarget(this);
}

//----- (004A0210) --------------------------------------------------------
void __thiscall CStatue::AttackBehavior(CStatue *this, unsigned int dwTick)
{
  CAggresiveCreature *Target; // eax
  double v4; // st6
  double v5; // st5
  double v6; // st3

  Target = CThreat::GetTarget(&this->m_Threat);
  this->m_lpTarget = Target;
  if ( !Target
    || (v4 = this->m_CurrentPos.m_fPointY - Target->m_CurrentPos.m_fPointY,
        v5 = this->m_CurrentPos.m_fPointX - Target->m_CurrentPos.m_fPointX,
        v6 = this->m_CurrentPos.m_fPointZ - Target->m_CurrentPos.m_fPointZ,
        this->m_wDefaultSearchRange < (unsigned int)(unsigned __int64)sqrt(v5 * v5 + v4 * v4 + v6 * v6))
    || (Target->m_dwStatusFlag & 0x40000000) != 0 )
  {
    CMonster::CancelTarget(this);
  }
  this->NormalBehavior(this, dwTick);
}

//----- (004A0290) --------------------------------------------------------
void __thiscall CStatue::ReturnBehavior(CStatue *this, unsigned int dwTick)
{
  int v3; // eax
  CStatue_vtbl *v4; // edx

  v3 = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 104);
  v4 = this->__vftable;
  this->m_nCurrentState = v3;
  v4->NormalBehavior(this, dwTick);
}

//----- (004A02C0) --------------------------------------------------------
void __thiscall CMonster::WalkAttackAction(CMonster *this, float fVelocity)
{
  CMonster_vtbl *v3; // eax
  MotionInfo *p_m_MotionInfo; // edi
  double m_fVelocity; // st7
  long double v6; // st6
  CMonster_vtbl *v7; // eax
  Position NewPosition; // [esp+8h] [ebp-Ch] BYREF

  v3 = this->__vftable;
  p_m_MotionInfo = &this->m_MotionInfo;
  this->m_MotionInfo.m_wAction = 8;
  ((void (__stdcall *)(int, MotionInfo *))v3->GetMotion)(8, &this->m_MotionInfo);
  p_m_MotionInfo->m_fDirection = CAggresiveCreature::CalcDir2D(
                                   this,
                                   this->m_CurrentPos.m_fPointX,
                                   this->m_CurrentPos.m_fPointZ,
                                   this->m_lpTarget->m_CurrentPos.m_fPointX,
                                   this->m_lpTarget->m_CurrentPos.m_fPointZ);
  this->m_MotionInfo.m_fVelocity = fVelocity;
  if ( !CMonster::MultiAttack(this) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::WalkAttackAction",
      aDWorkRylSource_2,
      990,
      aCid0x08x_63,
      this->m_dwCID);
  this->m_bAttacking = 1;
  this->m_bLongRangeAttacked = 0;
  CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
  m_fVelocity = this->m_MotionInfo.m_fVelocity;
  if ( (this->m_dwStatusFlag & 0x1000) != 0 )
    m_fVelocity = m_fVelocity * 0.25;
  v6 = cos(p_m_MotionInfo->m_fDirection);
  NewPosition.m_fPointY = this->m_CurrentPos.m_fPointY;
  NewPosition.m_fPointX = v6 * m_fVelocity + this->m_CurrentPos.m_fPointX;
  NewPosition.m_fPointZ = sin(p_m_MotionInfo->m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
  CAggresiveCreature::MoveTo(this, &NewPosition, 0);
  v7 = this->__vftable;
  this->m_nLeaveMovingNum = 0;
  v7->GetMotion(this, 2u, p_m_MotionInfo);
  this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
}

//----- (004A03C0) --------------------------------------------------------
void __thiscall CMonster::RunAction(CMonster *this, float fDistance, float fDstX, float fDstZ)
{
  CMonster_vtbl *v5; // eax
  MotionInfo *p_m_MotionInfo; // edi
  double v7; // st7
  double m_fVelocity; // st7
  unsigned int m_dwStatusFlag; // eax
  long double v10; // st6
  Position NewPosition; // [esp+8h] [ebp-Ch] BYREF

  v5 = this->__vftable;
  p_m_MotionInfo = &this->m_MotionInfo;
  this->m_MotionInfo.m_wAction = 1;
  ((void (__stdcall *)(int, MotionInfo *))v5->GetMotion)(1, &this->m_MotionInfo);
  v7 = CAggresiveCreature::CalcDir2D(this, this->m_CurrentPos.m_fPointX, this->m_CurrentPos.m_fPointZ, fDstX, fDstZ);
  if ( this->m_nLeaveMovingNum <= 0
    || p_m_MotionInfo->m_fDirection - v7 > 1.0
    || v7 - p_m_MotionInfo->m_fDirection > 1.0 )
  {
    p_m_MotionInfo->m_fDirection = v7;
    CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(
                                              this,
                                              (unsigned __int16)(unsigned __int64)(fDistance
                                                                                 / this->m_MotionInfo.m_fVelocity) > 3u
                                            ? 3
                                            : 1);
  }
  m_fVelocity = this->m_MotionInfo.m_fVelocity;
  m_dwStatusFlag = this->m_dwStatusFlag;
  --this->m_nLeaveMovingNum;
  if ( (m_dwStatusFlag & 0x1000) != 0 )
    m_fVelocity = m_fVelocity * 0.25;
  v10 = cos(p_m_MotionInfo->m_fDirection);
  NewPosition.m_fPointY = this->m_CurrentPos.m_fPointY;
  NewPosition.m_fPointX = v10 * m_fVelocity + this->m_CurrentPos.m_fPointX;
  NewPosition.m_fPointZ = sin(p_m_MotionInfo->m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
  if ( CAggresiveCreature::MoveTo(this, &NewPosition, 0) == 3 )
  {
    CMonster::ms_AttackBehaviorSendCount += CMonster::SendMove(this, 1u);
    this->m_nLeaveMovingNum = 0;
  }
}

//----- (004A04D0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (004A0510) --------------------------------------------------------
void __thiscall CMonster::SearchPlayer(CMonster *this)
{
  unsigned int v2; // eax
  CCell *ConnectCell; // eax
  CCell *v4; // edi
  CCharacter *FirstCharacter; // esi
  double v6; // st7
  double v7; // st6
  int v8; // ebp
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // edx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  bool v11; // zf
  int nCellCount; // [esp+4h] [ebp-Ch]
  CCharacter *pCurrentTarget; // [esp+8h] [ebp-8h]
  float fSquareSearchRange; // [esp+Ch] [ebp-4h]

  if ( this->m_CellPos.m_lpCell )
  {
    v2 = 0;
    pCurrentTarget = 0;
    nCellCount = 0;
    fSquareSearchRange = (float)(this->m_wSearchRange * this->m_wSearchRange);
    do
    {
      ConnectCell = CCell::GetConnectCell(this->m_CellPos.m_lpCell, v2);
      v4 = ConnectCell;
      if ( ConnectCell )
      {
        if ( ConnectCell->m_lstCharacter._Mysize )
        {
          FirstCharacter = CCell::GetFirstCharacter(ConnectCell);
          if ( FirstCharacter )
          {
            while ( 1 )
            {
              if ( FirstCharacter->m_CreatureStatus.m_nNowHP )
              {
                if ( !FirstCharacter->m_dwRideArmsCID && (FirstCharacter->m_dwStatusFlag & 0x60010000) == 0 )
                {
                  v6 = FirstCharacter->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
                  v7 = FirstCharacter->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
                  if ( v7 * v7 + v6 * v6 < fSquareSearchRange )
                  {
                    v8 = 2 * CAggresiveCreature::CalculateLevelGap(this, (int)FirstCharacter) + 20;
                    if ( v8 > (int)Math::Random::ComplexRandom(100, 0) )
                      break;
                  }
                }
              }
              Ptr = v4->m_CharacterIt._Ptr;
              Next = Ptr->_Next;
              v11 = Ptr->_Next == v4->m_lstCharacter._Myhead;
              v4->m_CharacterIt._Ptr = Ptr->_Next;
              if ( !v11 )
              {
                FirstCharacter = Next->_Myval;
                if ( FirstCharacter )
                  continue;
              }
              goto LABEL_16;
            }
            pCurrentTarget = FirstCharacter;
          }
        }
      }
LABEL_16:
      v2 = ++nCellCount;
    }
    while ( nCellCount < 9 );
    if ( pCurrentTarget )
    {
      CThreat::AddToThreatList(&this->m_Threat, pCurrentTarget, 1);
      this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 100);
    }
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonster::SearchPlayer", aDWorkRylSource_2, 137, aCid0x08, this->m_dwCID);
  }
}

//----- (004A0680) --------------------------------------------------------
void __thiscall CStatue::NormalBehavior(CStatue *this, unsigned int dwTick)
{
  int v3; // eax
  int v4; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *Instance; // eax
  unsigned int m_dwKID; // eax
  CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > result; // [esp+4h] [ebp-14h] BYREF
  CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > _Func; // [esp+8h] [ebp-10h] BYREF
  int v9; // [esp+Ch] [ebp-Ch]
  CStatueInfo v10; // [esp+10h] [ebp-8h] BYREF

  CStatueInfo::CStatueInfo(&v10, this, 0);
  v4 = *(_DWORD *)(v3 + 4);
  _Func.m_fnSecondProcess = *(CStatueInfo **)v3;
  v9 = v4;
  Instance = (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)CCreatureManager::GetInstance();
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const,CCharacter *>>>(
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Instance[10]._Ptr->_Left,
    Instance[10],
    (CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> >)&_Func);
  m_dwKID = this->m_MonsterInfo.m_dwKID;
  if ( m_dwKID == 1035 || m_dwKID == 1037 )
  {
    if ( dwTick > this->m_dwLastTime + 300000 )
    {
LABEL_6:
      this->Dead(this, 0);
      return;
    }
  }
  else if ( (m_dwKID == 1040 || m_dwKID == 1042) && dwTick > this->m_dwLastTime + 120000 )
  {
    goto LABEL_6;
  }
  this->m_lCurrentFrame = 150;
}
// 4A0694: variable 'v3' is possibly undefined

//----- (004A0740) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<float const ,CAggresiveCreature *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<float const ,CAggresiveCreature *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004A08F0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004A0BB0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> *result,
        std::pair<float const ,CAggresiveCreature *> *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Parent; // ecx
  std::pair<float const ,CAggresiveCreature *> *v7; // esi
  char v8; // al
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v9; // ecx
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> *v12; // eax
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = (std::pair<float const ,CAggresiveCreature *> *)Myhead;
  v8 = 1;
  _Addleft = 1;
  while ( !Parent->_Isnil )
  {
    v7 = (std::pair<float const ,CAggresiveCreature *> *)Parent;
    if ( _Val->first >= (double)Parent->_Myval.first )
    {
      Parent = Parent->_Right;
      v8 = 0;
      _Addleft = 0;
    }
    else
    {
      Parent = Parent->_Left;
      v8 = 1;
      _Addleft = 1;
    }
  }
  v9 = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)v7;
  _Val = v7;
  if ( v8 )
  {
    if ( v7 == (std::pair<float const ,CAggresiveCreature *> *)this->_Myhead->_Left )
    {
      v10 = std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *)&_Val,
              1,
              (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)v7,
              v3);
LABEL_9:
      Ptr = v10->_Ptr;
      v12 = result;
      result->first._Ptr = Ptr;
      result->second = 1;
      return v12;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first < (double)*(float *)&v3->first )
  {
    v10 = std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *)&_Val,
            _Addleft,
            (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)v7,
            v3);
    goto LABEL_9;
  }
  v12 = result;
  result->first._Ptr = v9;
  result->second = 0;
  return v12;
}

//----- (004A0C70) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}



//----- (004A0D30) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CMonster::AttackBehavior(CMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  CAggresiveCreature *Target; // eax
  double v5; // st7
  double v6; // st5
  double v7; // st5
  CParty *v8; // eax
  CAggresiveCreature *m_lpTarget; // ecx
  CMonster_vtbl *v10; // eax
  float fEstimateTime_4; // [esp+8h] [ebp-48h]
  float fDistance; // [esp+14h] [ebp-3Ch]
  _LARGE_INTEGER fAttackRange; // [esp+18h] [ebp-38h] OVERLAPPED BYREF
  CAutoInstrument autofunctionInstrument; // [esp+20h] [ebp-30h]
  std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> result; // [esp+24h] [ebp-2Ch] BYREF
  CPerformanceInstrument functionInstrument; // [esp+2Ch] [ebp-24h] BYREF
  int v17; // [esp+4Ch] [ebp-4h]

  functionInstrument.m_szfunctionName = "CMonster::AttackBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMonster::AttackBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  fAttackRange.QuadPart = __rdtsc();
  functionInstrument.m_startTime = fAttackRange;
  v17 = 0;
  Target = CThreat::GetTarget(&this->m_Threat);
  this->m_lpTarget = Target;
  if ( Target
    && this->m_MonsterInfo.m_cSkillPattern != 6
    && (Target->m_dwStatusFlag & 0x40000000) == 0
    && ((Target->m_dwCID & 0xD0000000) != 0 || !Target[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[6])
    && ((v5 = Target->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX,
         fAttackRange.LowPart = this->m_wSearchRange,
         v6 = Target->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ,
         v7 = sqrt(v6 * v6 + v5 * v5),
         fDistance = v7,
         v7 <= (double)(int)fAttackRange.LowPart)
     || this->m_bLongRangeAttacked)
    && Target->m_CreatureStatus.m_nNowHP )
  {
    v8 = this->GetParty(this);
    if ( v8 )
    {
      m_lpTarget = this->m_lpTarget;
      if ( LOWORD(v8[1].m_Party.m_dwLeaderID) + 10 > m_lpTarget->m_CreatureStatus.m_nLevel )
      {
        fAttackRange.LowPart = m_lpTarget->m_dwCID;
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
          (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&v8[1].m_PartySpellMgr.m_pPartyMember[9],
          &result,
          &fAttackRange.LowPart);
      }
    }
    fAttackRange.LowPart = this->m_CreatureStatus.m_StatusInfo.m_nAttackRange;
    *(float *)&fAttackRange.LowPart = (double)(int)fAttackRange.LowPart * 0.0099999998;
    if ( fDistance <= (double)*(float *)&fAttackRange.LowPart || this->m_lCurrentFrame > 0 )
    {
      if ( !this->m_bAttacking )
        CMonster::AttackAction(this);
    }
    else
    {
      v10 = this->__vftable;
      this->m_MotionInfo.m_wAction = 8;
      v10->GetMotion(this, 8u, &this->m_MotionInfo);
      if ( *(float *)&fAttackRange.LowPart + this->m_MotionInfo.m_fVelocity <= fDistance )
      {
        CMonster::RunAction(
          this,
          fDistance,
          this->m_lpTarget->m_CurrentPos.m_fPointX,
          this->m_lpTarget->m_CurrentPos.m_fPointZ);
        if ( this->m_MonsterInfo.m_bReturnPosition && CMonster::IsReturn(this) )
        {
          this->m_wSearchRange = 10;
          CMonster::CancelTarget(this);
        }
      }
      else
      {
        fEstimateTime_4 = fDistance - *(float *)&fAttackRange.LowPart + 0.1;
        CMonster::WalkAttackAction(this, fEstimateTime_4);
      }
      if ( !this->m_bAttacking )
        this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
    }
  }
  else
  {
    CMonster::CancelTarget(this);
  }
  v17 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}
// 4A0D30: variables would overlap: ^18.8 and stkvar "fAttackRange" ^18.4(has user info)

//----- (004A0FA0) --------------------------------------------------------
void __thiscall CSummonMonster::AttackBehavior(CSummonMonster *this, unsigned int dwTick)
{
  CCharacter *m_lpMaster; // eax

  m_lpMaster = this->m_lpMaster;
  if ( m_lpMaster )
    this->m_OriginalPosition = m_lpMaster->m_CurrentPos;
  CMonster::AttackBehavior(this, dwTick);
}

//----- (004A0FD0) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CSkillMonster::AttackBehavior(CSkillMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  bool m_bCasting; // zf
  CAggresiveCreature *Target; // eax
  double v6; // st7
  double v7; // st5
  double v8; // st5
  CParty *v9; // eax
  CAggresiveCreature *m_lpTarget; // ecx
  CSkillMonster_vtbl *v11; // edx
  float fEstimateTime_4; // [esp+8h] [ebp-48h]
  float fDistance; // [esp+14h] [ebp-3Ch]
  _LARGE_INTEGER fAttackRange; // [esp+18h] [ebp-38h] OVERLAPPED BYREF
  CAutoInstrument autofunctionInstrument; // [esp+20h] [ebp-30h]
  std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> result; // [esp+24h] [ebp-2Ch] BYREF
  CPerformanceInstrument functionInstrument; // [esp+2Ch] [ebp-24h] BYREF
  int v18; // [esp+4Ch] [ebp-4h]

  functionInstrument.m_szfunctionName = "CSkillMonster::AttackBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CSkillMonster::AttackBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  fAttackRange.QuadPart = __rdtsc();
  functionInstrument.m_startTime = fAttackRange;
  m_bCasting = this->m_bCasting;
  v18 = 0;
  if ( m_bCasting )
  {
    CSkillMonster::CastingAttackAction(this);
  }
  else
  {
    Target = CThreat::GetTarget(&this->m_Threat);
    this->m_lpTarget = Target;
    if ( Target
      && this->m_MonsterInfo.m_cSkillPattern != 6
      && (Target->m_dwStatusFlag & 0x40000000) == 0
      && ((Target->m_dwCID & 0xD0000000) != 0 || !Target[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[6])
      && ((v6 = Target->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX,
           fAttackRange.LowPart = this->m_wSearchRange,
           v7 = Target->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ,
           v8 = sqrt(v7 * v7 + v6 * v6),
           fDistance = v8,
           v8 <= (double)(int)fAttackRange.LowPart)
       || this->m_bLongRangeAttacked)
      && Target->m_CreatureStatus.m_nNowHP )
    {
      v9 = this->GetParty(this);
      if ( v9 )
      {
        m_lpTarget = this->m_lpTarget;
        if ( LOWORD(v9[1].m_Party.m_dwLeaderID) + 10 > m_lpTarget->m_CreatureStatus.m_nLevel )
        {
          fAttackRange.LowPart = m_lpTarget->m_dwCID;
          std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
            (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&v9[1].m_PartySpellMgr.m_pPartyMember[9],
            &result,
            &fAttackRange.LowPart);
        }
      }
      fAttackRange.LowPart = this->m_CreatureStatus.m_StatusInfo.m_nAttackRange;
      *(float *)&fAttackRange.LowPart = (double)(int)fAttackRange.LowPart * 0.0099999998;
      if ( fDistance <= (double)*(float *)&fAttackRange.LowPart || this->m_lCurrentFrame > 0 )
      {
        if ( !this->m_bAttacking )
        {
          if ( this->SkillAttack(this) )
            CSkillMonster::SkillAttackAction(this);
          else
            CMonster::AttackAction(this);
        }
      }
      else
      {
        if ( this->SkillAttack(this) )
        {
          CSkillMonster::SkillAttackAction(this);
        }
        else
        {
          v11 = this->__vftable;
          this->m_MotionInfo.m_wAction = 8;
          v11->GetMotion(this, 8u, &this->m_MotionInfo);
          if ( *(float *)&fAttackRange.LowPart + this->m_MotionInfo.m_fVelocity <= fDistance )
          {
            CMonster::RunAction(
              this,
              fDistance,
              this->m_lpTarget->CMonster::m_CurrentPos.m_fPointX,
              this->m_lpTarget->CMonster::m_CurrentPos.m_fPointZ);
            if ( this->m_MonsterInfo.m_bReturnPosition && CMonster::IsReturn(this) )
            {
              this->m_wSearchRange = 10;
              CMonster::CancelTarget(this);
            }
          }
          else
          {
            fEstimateTime_4 = fDistance - *(float *)&fAttackRange.LowPart + 0.1;
            CMonster::WalkAttackAction(this, fEstimateTime_4);
          }
        }
        if ( !this->m_bAttacking )
          this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
      }
    }
    else
    {
      CMonster::CancelTarget(this);
    }
  }
  v18 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}
// 4A0FD0: variables would overlap: ^18.8 and stkvar "fAttackRange" ^18.4(has user info)

//----- (004A1270) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CMageMonster::AttackBehavior(CMageMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  CMageMonster_vtbl *v4; // eax
  CAggresiveCreature *Target; // eax
  int v6; // ecx
  double v7; // st7
  double v8; // st5
  double v9; // st5
  CParty *v10; // eax
  CAggresiveCreature *m_lpTarget; // ecx
  CAggresiveCreature *v12; // eax
  CMonster *HighestLVMember; // eax
  double m_fPointX; // st7
  double v15; // st7
  float fToMemberZ; // [esp+10h] [ebp-3Ch]
  float fToMemberZa; // [esp+10h] [ebp-3Ch]
  std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> fDstX; // [esp+14h] [ebp-38h] BYREF
  _LARGE_INTEGER fToTargetZ; // [esp+1Ch] [ebp-30h] OVERLAPPED BYREF
  CAutoInstrument autofunctionInstrument; // [esp+24h] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+28h] [ebp-24h] BYREF
  int v22; // [esp+48h] [ebp-4h]
  float dwTicka; // [esp+50h] [ebp+4h]

  functionInstrument.m_szfunctionName = "CMageMonster::AttackBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMageMonster::AttackBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  fToTargetZ.QuadPart = __rdtsc();
  functionInstrument.m_startTime = fToTargetZ;
  v4 = this->__vftable;
  v22 = 0;
  if ( !v4->GetParty(this) )
  {
    CSkillMonster::AttackBehavior(this, dwTick);
    goto LABEL_34;
  }
  if ( this->m_bCasting )
  {
    CSkillMonster::CastingAttackAction(this);
    goto LABEL_34;
  }
  Target = CThreat::GetTarget(&this->m_Threat);
  this->m_lpTarget = Target;
  if ( !Target
    || this->m_MonsterInfo.m_cSkillPattern == 6
    || (Target->m_dwStatusFlag & 0x40000000) != 0
    || !Creature::GetCreatureType(Target->m_dwCID) && *(_DWORD *)(v6 + 1516)
    || (v7 = *(float *)(v6 + 4) - this->m_CurrentPos.m_fPointX,
        fToTargetZ.LowPart = this->m_wSearchRange,
        v8 = *(float *)(v6 + 12) - this->m_CurrentPos.m_fPointZ,
        v9 = sqrt(v8 * v8 + v7 * v7),
        dwTicka = v9,
        v9 > (double)(int)fToTargetZ.LowPart)
    && !this->m_bLongRangeAttacked
    || !*(_WORD *)(v6 + 148) )
  {
    CMonster::CancelTarget(this);
    goto LABEL_34;
  }
  v10 = this->GetParty(this);
  if ( v10 )
  {
    m_lpTarget = this->m_lpTarget;
    if ( LOWORD(v10[1].m_Party.m_dwLeaderID) + 10 > m_lpTarget->m_CreatureStatus.m_nLevel )
    {
      fToTargetZ.LowPart = m_lpTarget->m_dwCID;
      std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
        (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&v10[1].m_PartySpellMgr.m_pPartyMember[9],
        &fDstX,
        &fToTargetZ.LowPart);
    }
  }
  v12 = this->m_lpTarget;
  *(float *)&fDstX.first._Ptr = v12->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
  *(float *)&fToTargetZ.LowPart = v12->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
  HighestLVMember = CSkillMonster::FindHighestLVMember(this);
  if ( HighestLVMember )
  {
    m_fPointX = HighestLVMember->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
    fToMemberZ = HighestLVMember->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
  }
  else
  {
    m_fPointX = this->m_CurrentPos.m_fPointX;
    fToMemberZ = this->m_CurrentPos.m_fPointZ;
  }
  if ( this->m_lCurrentFrame <= 0 )
  {
    if ( dwTicka >= 10.0 )
    {
      if ( dwTicka <= 15.0 )
      {
        if ( this->m_bAttacking )
          goto LABEL_34;
        if ( this->SkillAttack(this) )
          CSkillMonster::SkillAttackAction(this);
        goto LABEL_30;
      }
      *(float *)&fDstX.first._Ptr = (m_fPointX + *(float *)&fDstX.first._Ptr) * 0.5 + this->m_CurrentPos.m_fPointX;
      v15 = (fToMemberZ + *(float *)&fToTargetZ.LowPart) * 0.5 + this->m_CurrentPos.m_fPointZ;
    }
    else
    {
      *(float *)&fDstX.first._Ptr = this->m_CurrentPos.m_fPointX - (m_fPointX + *(float *)&fDstX.first._Ptr) * 0.5;
      v15 = this->m_CurrentPos.m_fPointZ - (fToMemberZ + *(float *)&fToTargetZ.LowPart) * 0.5;
    }
    fToMemberZa = v15;
    CMonster::RunAction(this, dwTicka, *(float *)&fDstX.first._Ptr, fToMemberZa);
    if ( this->m_MonsterInfo.m_bReturnPosition && CMonster::IsReturn(this) )
    {
      this->m_wSearchRange = 10;
      CMonster::CancelTarget(this);
    }
LABEL_30:
    if ( !this->m_bAttacking )
      this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
  }
LABEL_34:
  v22 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}
// 4A1270: variables would overlap: ^20.8 and stkvar "fToTargetZ" ^20.4(has user info)

//----- (004A1560) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CAcolyteMonster::AttackBehavior(CAcolyteMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  CAcolyteMonster_vtbl *v4; // eax
  CAggresiveCreature *Target; // eax
  int v6; // ecx
  double v7; // st7
  double v8; // st5
  double v9; // st5
  CParty *v10; // eax
  CAggresiveCreature *m_lpTarget; // ecx
  CAggresiveCreature *v12; // eax
  CMonster *HighestLVMember; // eax
  double m_fPointX; // st7
  double v15; // st7
  float fToMemberZ; // [esp+10h] [ebp-3Ch]
  float fToMemberZa; // [esp+10h] [ebp-3Ch]
  std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> fDstX; // [esp+14h] [ebp-38h] BYREF
  _LARGE_INTEGER fToTargetZ; // [esp+1Ch] [ebp-30h] OVERLAPPED BYREF
  CAutoInstrument autofunctionInstrument; // [esp+24h] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+28h] [ebp-24h] BYREF
  int v22; // [esp+48h] [ebp-4h]
  float dwTicka; // [esp+50h] [ebp+4h]

  functionInstrument.m_szfunctionName = "CAcolyteMonster::AttackBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CAcolyteMonster::AttackBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  fToTargetZ.QuadPart = __rdtsc();
  functionInstrument.m_startTime = fToTargetZ;
  v4 = this->__vftable;
  v22 = 0;
  if ( !v4->GetParty(this) )
  {
    CSkillMonster::AttackBehavior(this, dwTick);
    goto LABEL_34;
  }
  if ( this->m_bCasting )
  {
    CSkillMonster::CastingAttackAction(this);
    goto LABEL_34;
  }
  Target = CThreat::GetTarget(&this->m_Threat);
  this->m_lpTarget = Target;
  if ( !Target
    || this->m_MonsterInfo.m_cSkillPattern == 6
    || (Target->m_dwStatusFlag & 0x40000000) != 0
    || !Creature::GetCreatureType(Target->m_dwCID) && *(_DWORD *)(v6 + 1516)
    || (v7 = *(float *)(v6 + 4) - this->m_CurrentPos.m_fPointX,
        fToTargetZ.LowPart = this->m_wSearchRange,
        v8 = *(float *)(v6 + 12) - this->m_CurrentPos.m_fPointZ,
        v9 = sqrt(v8 * v8 + v7 * v7),
        dwTicka = v9,
        v9 > (double)(int)fToTargetZ.LowPart)
    && !this->m_bLongRangeAttacked
    || !*(_WORD *)(v6 + 148) )
  {
    CMonster::CancelTarget(this);
    goto LABEL_34;
  }
  v10 = this->GetParty(this);
  if ( v10 )
  {
    m_lpTarget = this->m_lpTarget;
    if ( LOWORD(v10[1].m_Party.m_dwLeaderID) + 10 > m_lpTarget->m_CreatureStatus.m_nLevel )
    {
      fToTargetZ.LowPart = m_lpTarget->m_dwCID;
      std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
        (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&v10[1].m_PartySpellMgr.m_pPartyMember[9],
        &fDstX,
        &fToTargetZ.LowPart);
    }
  }
  v12 = this->m_lpTarget;
  *(float *)&fDstX.first._Ptr = v12->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
  *(float *)&fToTargetZ.LowPart = v12->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
  HighestLVMember = CSkillMonster::FindHighestLVMember(this);
  if ( HighestLVMember )
  {
    m_fPointX = HighestLVMember->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
    fToMemberZ = HighestLVMember->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
  }
  else
  {
    m_fPointX = this->m_CurrentPos.m_fPointX;
    fToMemberZ = this->m_CurrentPos.m_fPointZ;
  }
  if ( this->m_lCurrentFrame <= 0 )
  {
    if ( dwTicka >= 10.0 )
    {
      if ( dwTicka <= 15.0 )
      {
        if ( this->m_bAttacking )
          goto LABEL_34;
        if ( this->SkillAttack(this) )
          CSkillMonster::SkillAttackAction(this);
        goto LABEL_30;
      }
      *(float *)&fDstX.first._Ptr = (m_fPointX + *(float *)&fDstX.first._Ptr) * 0.5 + this->m_CurrentPos.m_fPointX;
      v15 = (fToMemberZ + *(float *)&fToTargetZ.LowPart) * 0.5 + this->m_CurrentPos.m_fPointZ;
    }
    else
    {
      *(float *)&fDstX.first._Ptr = this->m_CurrentPos.m_fPointX - (m_fPointX + *(float *)&fDstX.first._Ptr) * 0.5;
      v15 = this->m_CurrentPos.m_fPointZ - (fToMemberZ + *(float *)&fToTargetZ.LowPart) * 0.5;
    }
    fToMemberZa = v15;
    CMonster::RunAction(this, dwTicka, *(float *)&fDstX.first._Ptr, fToMemberZa);
    if ( this->m_MonsterInfo.m_bReturnPosition && CMonster::IsReturn(this) )
    {
      this->m_wSearchRange = 10;
      CMonster::CancelTarget(this);
    }
LABEL_30:
    if ( !this->m_bAttacking )
      this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
  }
LABEL_34:
  v22 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}
// 4A1560: variables would overlap: ^20.8 and stkvar "fToTargetZ" ^20.4(has user info)

//----- (004A1850) --------------------------------------------------------
void __thiscall std::map<float,CAggresiveCreature *>::~map<float,CAggresiveCreature *>(
        std::map<float,CAggresiveCreature *> *this)
{
  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004A1880) --------------------------------------------------------
char __thiscall CMonster::CheckPartyTarget(CMonster *this)
{
  CParty *v2; // eax
  CAggresiveCreature *v3; // edi
  CAggresiveCreature **v4; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node **v5; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Ptr; // esi
  bool v7; // zf
  signed int Myval; // ebx
  VirtualArea::CVirtualAreaMgr *v9; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CMsgProc *AggresiveCreature; // eax
  CCreatureManager *Instance; // eax
  double v14; // st5
  double v15; // st4
  double v16; // st7
  std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *Left; // eax
  int v18; // eax
  bool v19; // c0
  unsigned __int8 m_cSkillPattern; // al
  CAggresiveCreature *second; // [esp-8h] [ebp-58h]
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-54h]
  signed int v24; // [esp-4h] [ebp-54h]
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator itr; // [esp+14h] [ebp-3Ch] BYREF
  float fDistance; // [esp+18h] [ebp-38h]
  float fAttackRange; // [esp+1Ch] [ebp-34h]
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator v28; // [esp+20h] [ebp-30h] BYREF
  std::pair<float const ,CAggresiveCreature *> _Val; // [esp+24h] [ebp-2Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> result; // [esp+2Ch] [ebp-24h] BYREF
  std::map<float,CAggresiveCreature *> attackTargetMap; // [esp+34h] [ebp-1Ch] BYREF
  int v32; // [esp+4Ch] [ebp-4h]

  v2 = this->GetParty(this);
  v3 = 0;
  fDistance = *(float *)&v2;
  if ( !v2 )
    return 0;
  v4 = &v2[1].m_PartySpellMgr.m_pPartyMember[9];
  if ( !v2[1].m_Party.m_dwPartyID )
    return 0;
  attackTargetMap._Myhead = (std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&attackTargetMap);
  attackTargetMap._Myhead->_Isnil = 1;
  attackTargetMap._Myhead->_Parent = attackTargetMap._Myhead;
  attackTargetMap._Myhead->_Left = attackTargetMap._Myhead;
  attackTargetMap._Myhead->_Right = attackTargetMap._Myhead;
  attackTargetMap._Mysize = 0;
  v5 = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node **)v4[1];
  Ptr = *v5;
  v7 = *v5 == (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v5;
  v32 = 0;
  fAttackRange = *(float *)&v5;
  itr._Ptr = Ptr;
  if ( v7 )
  {
LABEL_24:
    v32 = -1;
    std::map<float,CAggresiveCreature *>::~map<float,CAggresiveCreature *>(&attackTargetMap);
    return 0;
  }
  do
  {
    Myval = Ptr->_Myval;
    if ( !this->m_CellPos.m_wMapIndex )
    {
      v24 = Ptr->_Myval;
      Instance = CCreatureManager::GetInstance();
      AggresiveCreature = CCreatureManager::GetAggresiveCreature(Instance, v24);
      goto LABEL_9;
    }
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    v9 = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v9, m_wMapIndex);
    if ( VirtualArea )
    {
      m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
      if ( m_pVirtualMonsterMgr )
      {
        AggresiveCreature = CVirtualMonsterMgr::GetAggresiveCreature(m_pVirtualMonsterMgr, Myval);
LABEL_9:
        v3 = (CAggresiveCreature *)AggresiveCreature;
      }
    }
    if ( !v3 || (v3->m_dwStatusFlag & 0x40000000) != 0 )
    {
      Ptr = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
              (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)(LODWORD(fDistance) + 348),
              &v28,
              (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)Ptr)->_Ptr;
      itr._Ptr = Ptr;
    }
    else
    {
      v14 = this->m_CurrentPos.m_fPointX - v3->m_CurrentPos.m_fPointX;
      v15 = this->m_CurrentPos.m_fPointZ - v3->m_CurrentPos.m_fPointZ;
      v16 = v14 * v14 + v15 * v15;
      if ( v16 <= *(float *)(LODWORD(fDistance) + 344) * *(float *)(LODWORD(fDistance) + 344) )
      {
        _Val.first = v16;
        _Val.second = v3;
        std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::insert(
          &attackTargetMap,
          &result,
          &_Val);
      }
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *)&itr);
      Ptr = itr._Ptr;
    }
  }
  while ( Ptr != (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)LODWORD(fAttackRange) );
  if ( !attackTargetMap._Mysize )
    goto LABEL_24;
  Left = attackTargetMap._Myhead->_Left;
  LODWORD(fAttackRange) = this->m_CreatureStatus.m_StatusInfo.m_nAttackRange;
  fAttackRange = (double)SLODWORD(fAttackRange) * 0.0099999998;
  second = Left->_Myval.second;
  fDistance = sqrt(Left->_Myval.first);
  CThreat::AddToThreatList(&this->m_Threat, second, 1);
  v18 = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 100);
  v19 = fAttackRange < (double)fDistance;
  this->m_nCurrentState = v18;
  if ( v19 || (m_cSkillPattern = this->m_MonsterInfo.m_cSkillPattern, m_cSkillPattern == 3) || m_cSkillPattern == 4 )
    this->m_bLongRangeAttacked = 1;
  v32 = -1;
  std::map<float,CAggresiveCreature *>::~map<float,CAggresiveCreature *>(&attackTargetMap);
  return 1;
}

//----- (004A1AD0) --------------------------------------------------------
void __thiscall CMonster::NormalBehavior(CMonster *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  int m_nLeaveMovingNum; // eax
  double m_fVelocity; // st7
  long double m_fDirection; // st6
  long double v7; // st6
  double m_fPointY; // st5
  unsigned __int16 m_wNumMoving; // di
  double v10; // st6
  double v11; // st4
  CMonster_vtbl *v12; // edx
  MotionInfo *p_m_MotionInfo; // edi
  double v14; // st7
  CMonster_vtbl *v15; // edx
  int m_nMovingPattern; // eax
  int v17; // eax
  const Position *v18; // eax
  Position NewPosition; // [esp+10h] [ebp-34h] BYREF
  CAutoInstrument autofunctionInstrument; // [esp+1Ch] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+20h] [ebp-24h] BYREF
  int v22; // [esp+40h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CMonster::NormalBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CMonster::NormalBehavior", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  *(_QWORD *)&NewPosition.m_fPointX = __rdtsc();
  functionInstrument.m_startTime.QuadPart = *(_QWORD *)&NewPosition.m_fPointX;
  m_nLeaveMovingNum = this->m_nLeaveMovingNum;
  v22 = 0;
  if ( m_nLeaveMovingNum )
  {
    m_fVelocity = this->m_MotionInfo.m_fVelocity;
    this->m_nLeaveMovingNum = m_nLeaveMovingNum - 1;
    if ( (this->m_dwStatusFlag & 0x1000) != 0 )
      m_fVelocity = m_fVelocity * 0.25;
    m_fDirection = this->m_MotionInfo.m_fDirection;
    NewPosition.m_fPointX = cos(m_fDirection);
    v7 = sin(m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
    m_fPointY = this->m_CurrentPos.m_fPointY;
    NewPosition.m_fPointX = NewPosition.m_fPointX * m_fVelocity + this->m_CurrentPos.m_fPointX;
    NewPosition.m_fPointY = m_fPointY;
    NewPosition.m_fPointZ = v7;
    if ( CAggresiveCreature::MoveTo(this, &NewPosition, 0) == 3 )
    {
      CMonster::ms_NormalBehaviorSendCount += CMonster::SendMove(this, 1u);
      this->m_nLeaveMovingNum = 0;
    }
    this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
  }
  else if ( !this->GetParty(this) || !CMonster::CheckPartyTarget(this) )
  {
    if ( !this->m_lpTarget && this->m_MonsterInfo.m_bFirstAttack )
      CMonster::SearchPlayer(this);
    if ( CCellManager::GetInstance()->m_bMoving
      && this->m_nNormalMovingDelay <= 0
      && (m_wNumMoving = CCellManager::GetInstance()->m_wNumMoving, m_wNumMoving > Math::Random::ComplexRandom(100, 0)) )
    {
      v10 = this->m_OriginalPosition.m_fPointZ - this->m_CurrentPos.m_fPointZ;
      v11 = this->m_OriginalPosition.m_fPointX - this->m_CurrentPos.m_fPointX;
      if ( v10 * v10 + v11 * v11 <= 225.0 || this->m_nMovingPattern )
      {
        v15 = this->__vftable;
        p_m_MotionInfo = &this->m_MotionInfo;
        this->m_MotionInfo.m_wAction = 8;
        v15->GetMotion(this, 8u, &this->m_MotionInfo);
        LODWORD(NewPosition.m_fPointX) = Math::Random::ComplexRandom(62832, 0);
        v14 = (double)LODWORD(NewPosition.m_fPointX) * 0.000099999997;
      }
      else
      {
        v12 = this->__vftable;
        p_m_MotionInfo = &this->m_MotionInfo;
        this->m_MotionInfo.m_wAction = 1;
        v12->GetMotion(this, 1u, &this->m_MotionInfo);
        v14 = CAggresiveCreature::CalcDir2D(
                this,
                this->m_CurrentPos.m_fPointX,
                this->m_CurrentPos.m_fPointZ,
                this->m_OriginalPosition.m_fPointX,
                this->m_OriginalPosition.m_fPointZ);
      }
      m_nMovingPattern = this->m_nMovingPattern;
      p_m_MotionInfo->m_fDirection = v14;
      if ( m_nMovingPattern == 1 )
      {
        p_m_MotionInfo->m_fDirection = 0.0;
        this->m_MotionInfo.m_fVelocity = 0.0;
      }
      v17 = CMonster::SendMove(this, 1u);
      this->m_nNormalMovingDelay = v17;
      CMonster::ms_NormalBehaviorSendCount += v17;
      --this->m_nLeaveMovingNum;
      v18 = CMonster::CalculateCoor(this, &NewPosition);
      CAggresiveCreature::MoveTo(this, v18, 0);
      this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
    }
    else
    {
      this->m_nNormalMovingDelay -= 10;
      this->m_lCurrentFrame = 30;
    }
  }
  v22 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}

//----- (004A1D80) --------------------------------------------------------
void __thiscall CSummonMonster::NormalBehavior(CSummonMonster *this, unsigned int dwTick)
{
  CCharacter *m_lpMaster; // ecx
  int v4; // eax
  bool v5; // zf
  double v6; // st6
  double v7; // st4
  long double v8; // st7
  CSummonMonster_vtbl *v9; // edx
  unsigned __int64 v10; // rax
  double m_fVelocity; // st7
  long double v12; // st6
  Position NewPosition; // [esp+8h] [ebp-Ch] BYREF
  float dwTicka; // [esp+18h] [ebp+4h]

  m_lpMaster = this->m_lpMaster;
  if ( !m_lpMaster )
  {
    CMonster::NormalBehavior(this, dwTick);
    return;
  }
  v4 = this->m_nLeaveMovingNum - 1;
  v5 = this->m_nLeaveMovingNum == 1;
  this->m_nLeaveMovingNum = v4;
  if ( v4 < 0 || v5 )
  {
    v6 = m_lpMaster->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
    v7 = m_lpMaster->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
    v8 = sqrt(v6 * v6 + v7 * v7);
    dwTicka = v8;
    if ( v8 <= 3.0 )
      return;
    v9 = this->__vftable;
    this->m_MotionInfo.m_wAction = 1;
    v9->GetMotion(this, 1u, &this->m_MotionInfo);
    this->m_MotionInfo.m_fDirection = CAggresiveCreature::CalcDir2D(
                                        this,
                                        this->m_CurrentPos.m_fPointX,
                                        this->m_CurrentPos.m_fPointZ,
                                        this->m_lpMaster->m_CurrentPos.m_fPointX,
                                        this->m_lpMaster->m_CurrentPos.m_fPointZ);
    v10 = (unsigned __int64)(dwTicka / this->m_MotionInfo.m_fVelocity);
    if ( (unsigned __int16)v10 <= 3u )
    {
      if ( !(_WORD)v10 )
        return;
    }
    else
    {
      LOWORD(v10) = 3;
    }
    CMonster::ms_NormalBehaviorSendCount += CMonster::SendMove(this, v10);
  }
  m_fVelocity = this->m_MotionInfo.m_fVelocity;
  if ( (this->m_dwStatusFlag & 0x1000) != 0 )
    m_fVelocity = m_fVelocity * 0.25;
  v12 = cos(this->m_MotionInfo.m_fDirection);
  NewPosition.m_fPointY = this->m_CurrentPos.m_fPointY;
  NewPosition.m_fPointX = v12 * m_fVelocity + this->m_CurrentPos.m_fPointX;
  NewPosition.m_fPointZ = sin(this->m_MotionInfo.m_fDirection) * m_fVelocity + this->m_CurrentPos.m_fPointZ;
  if ( CAggresiveCreature::MoveTo(this, &NewPosition, 0) == 3 )
  {
    this->m_nLeaveMovingNum = 0;
    CMonster::ms_ReturnBehaviorSendCount += CMonster::SendMove(this, 1u);
  }
  this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
}

//----- (004A1EC0) --------------------------------------------------------
void __thiscall CSkillMonster::NormalBehavior(CSkillMonster *this, unsigned int dwTick)
{
  unsigned __int8 m_cSkillPattern; // al

  CMonster::NormalBehavior(this, dwTick);
  m_cSkillPattern = this->m_MonsterInfo.m_cSkillPattern;
  if ( m_cSkillPattern == 5 || m_cSkillPattern == 9 || m_cSkillPattern == 10 )
    CCastingSpell::DisableChant(&this->m_SpellMgr.m_CastingInfo, 0);
}

//----- (004A1EF0) --------------------------------------------------------
void __cdecl std::fill_n<float *,float,float>(float *_First, float _Count, const float *_Val)
{
  double i; // st7

  for ( i = _Count; i > 0.0; ++_First )
  {
    i = i - 1.0;
    *_First = *_Val;
  }
}

//----- (004A1F30) --------------------------------------------------------
void __thiscall MonsterInfo::MonsterInfo(MonsterInfo *this)
{
  MonsterInfo *v2; // edi
  int v3; // ebx
  float _Val; // [esp+Ch] [ebp-4h] BYREF

  v2 = this;
  v3 = 4;
  do
  {
    MotionInfo::MotionInfo(v2->m_MonsterMotions);
    v2 = (MonsterInfo *)((char *)v2 + 16);
    --v3;
  }
  while ( v3 );
  this->m_dwKID = 0;
  this->m_dwRespawnTime = 0;
  this->m_fSize = 0.0;
  this->m_fAttackAngle = 0.0;
  this->m_cSkillPattern = 0;
  this->m_cSkillLevel = 0;
  this->m_cFixLevelGap = 0;
  this->m_bFixLevelGap = 0;
  this->m_cNation = 0;
  this->m_wSkillUseRate = 0;
  this->m_dwEnchantSpellType = 0;
  this->m_dwChantSpellType = 0;
  this->m_fSkillEffectSize = 1.0;
  this->m_bCollision = 0;
  this->m_bStealth = 0;
  this->m_bFirstAttack = 0;
  this->m_bReturnPosition = 0;
  this->m_bEscape = 0;
  *(_DWORD *)this->m_aryAwardItem = 0;
  *(_DWORD *)&this->m_aryAwardItem[2] = 0;
  this->m_aryAwardItem[4] = 0;
  *(_DWORD *)this->m_aryDropRate = 0;
  *(_DWORD *)&this->m_aryDropRate[2] = 0;
  *(_DWORD *)&this->m_aryDropRate[4] = 0;
  *(_DWORD *)&this->m_aryDropRate[6] = 0;
  *(_DWORD *)&this->m_aryDropRate[8] = 0;
  *(_DWORD *)&this->m_aryDropRate[10] = 0;
  this->m_aryDropRate[12] = 0;
  *(_DWORD *)this->m_strName = 0;
  *(_DWORD *)&this->m_strName[4] = 0;
  *(_DWORD *)&this->m_strName[8] = 0;
  *(_DWORD *)&this->m_strName[12] = 0;
  *(_DWORD *)&this->m_strName[16] = 0;
  *(_DWORD *)&this->m_strName[20] = 0;
  *(_DWORD *)&this->m_strName[24] = 0;
  *(_DWORD *)&this->m_strName[28] = 0;
  *(_DWORD *)this->m_strModelingFlag = 0;
  *(_DWORD *)&this->m_strModelingFlag[4] = 0;
  *(_DWORD *)&this->m_strModelingFlag[8] = 0;
  *(_DWORD *)&this->m_strModelingFlag[12] = 0;
  *(_DWORD *)&this->m_strModelingFlag[16] = 0;
  *(_DWORD *)&this->m_strModelingFlag[20] = 0;
  _Val = 0.0;
  *(_DWORD *)&this->m_strModelingFlag[24] = 0;
  *(_DWORD *)&this->m_strModelingFlag[28] = 0;
  std::fill_n<float *,float,float>(this->m_fHitBox, 4.0, &_Val);
  *(_DWORD *)this->m_wSkillID = 0;
  *(_DWORD *)&this->m_wSkillID[2] = 0;
  this->m_wSkillID[4] = 0;
}

//----- (004A2070) --------------------------------------------------------
unsigned int __cdecl MonsterInfo::GetMonsterPattern(const char *szMonsterType)
{
  unsigned int *v1; // edi

  if ( (_S5_11 & 1) == 0 )
  {
    _S5_11 |= 1u;
    monsterTypeName[0].m_szName = "Common";
    dword_53B0B4 = 0;
    dword_53B0B8 = (int)"Warrior";
    dword_53B0BC = 1;
    dword_53B0C0 = (int)"Defender";
    dword_53B0C4 = 2;
    dword_53B0C8 = (int)"Mage";
    dword_53B0CC = 3;
    dword_53B0D0 = (int)"Acolyte";
    dword_53B0D4 = 4;
    dword_53B0D8 = (int)"Boss";
    dword_53B0DC = 5;
    dword_53B0E0 = (int)"BG";
    dword_53B0E4 = 6;
    dword_53B0E8 = (int)"Summon";
    dword_53B0EC = 7;
    dword_53B0F0 = (int)"Structure";
    dword_53B0F4 = 8;
    dword_53B0F8 = (int)"Named";
    dword_53B0FC = 9;
    dword_53B100 = (int)"Chief";
    dword_53B104 = 10;
  }
  v1 = (unsigned int *)monsterTypeName;
  while ( strcmp(szMonsterType, (const char *)*v1) )
  {
    v1 += 2;
    if ( v1 == &_S5_11 )
      return 0;
  }
  return v1[1];
}
// 53B0B4: using guessed type int dword_53B0B4;
// 53B0B8: using guessed type int dword_53B0B8;
// 53B0BC: using guessed type int dword_53B0BC;
// 53B0C0: using guessed type int dword_53B0C0;
// 53B0C4: using guessed type int dword_53B0C4;
// 53B0C8: using guessed type int dword_53B0C8;
// 53B0CC: using guessed type int dword_53B0CC;
// 53B0D0: using guessed type int dword_53B0D0;
// 53B0D4: using guessed type int dword_53B0D4;
// 53B0D8: using guessed type int dword_53B0D8;
// 53B0DC: using guessed type int dword_53B0DC;
// 53B0E0: using guessed type int dword_53B0E0;
// 53B0E4: using guessed type int dword_53B0E4;
// 53B0E8: using guessed type int dword_53B0E8;
// 53B0EC: using guessed type int dword_53B0EC;
// 53B0F0: using guessed type int dword_53B0F0;
// 53B0F4: using guessed type int dword_53B0F4;
// 53B0F8: using guessed type int dword_53B0F8;
// 53B0FC: using guessed type int dword_53B0FC;
// 53B100: using guessed type int dword_53B100;
// 53B104: using guessed type int dword_53B104;

//----- (004A21C0) --------------------------------------------------------
char __thiscall CCharacter::HasQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  int v2; // eax
  Quest::ExecutingQuest *i; // edx
  int v4; // eax
  unsigned __int16 *j; // ecx

  v2 = 0;
  for ( i = this->m_ExecutingQuest; i->m_wQuestID != wQuestID; ++i )
  {
    if ( ++v2 >= 10 )
    {
      v4 = 0;
      for ( j = this->m_wHistoryQuest; *j != wQuestID; ++j )
      {
        if ( ++v4 >= 100 )
          return 0;
      }
      return 1;
    }
  }
  return 1;
}

//----- (004A2200) --------------------------------------------------------
char __thiscall CCharacter::GiveQuest(CCharacter *this, Quest::QuestNode *lpQuestNode)
{
  Quest::QuestNode *m_QuestNode; // edx
  Quest::QuestNode *v4; // esi
  Quest::QuestNode *v5; // esi
  Quest::QuestNode *v6; // esi
  Quest::QuestNode *v7; // esi
  Quest::QuestNode *v8; // esi
  Quest::QuestNode *v9; // esi
  Quest::QuestNode *v10; // esi
  Quest::QuestNode *v11; // esi

  if ( this->m_ExecutingQuest[9].m_wQuestID )
    return 0;
  *(_DWORD *)&this->m_ExecutingQuest[9].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[8].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[9].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[9].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[5];
  m_QuestNode = this->m_ExecutingQuest[8].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[9].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[9];
  this->m_ExecutingQuest[9].m_QuestNode = m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[8].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[7].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[5];
  v4 = this->m_ExecutingQuest[7].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[8].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[9];
  this->m_ExecutingQuest[8].m_QuestNode = v4;
  *(_DWORD *)&this->m_ExecutingQuest[7].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[6].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[5];
  v5 = this->m_ExecutingQuest[6].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[7].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[9];
  this->m_ExecutingQuest[7].m_QuestNode = v5;
  *(_DWORD *)&this->m_ExecutingQuest[6].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[5].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[5];
  v6 = this->m_ExecutingQuest[5].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[6].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[9];
  this->m_ExecutingQuest[6].m_QuestNode = v6;
  *(_DWORD *)&this->m_ExecutingQuest[5].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[4].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[5];
  v7 = this->m_ExecutingQuest[4].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[5].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[9];
  this->m_ExecutingQuest[5].m_QuestNode = v7;
  *(_DWORD *)&this->m_ExecutingQuest[4].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[3].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[5];
  v8 = this->m_ExecutingQuest[3].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[4].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[9];
  this->m_ExecutingQuest[4].m_QuestNode = v8;
  *(_DWORD *)&this->m_ExecutingQuest[3].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[2].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[5];
  v9 = this->m_ExecutingQuest[2].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[3].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[9];
  this->m_ExecutingQuest[3].m_QuestNode = v9;
  *(_DWORD *)&this->m_ExecutingQuest[2].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[1].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[5];
  v10 = this->m_ExecutingQuest[1].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[2].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[9];
  this->m_ExecutingQuest[2].m_QuestNode = v10;
  *(_DWORD *)&this->m_ExecutingQuest[1].m_wQuestID = *(_DWORD *)&this->m_ExecutingQuest[0].m_wQuestID;
  *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[1] = *(_DWORD *)&this->m_ExecutingQuest[0].m_cTriggerCount[1];
  *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[5] = *(_DWORD *)&this->m_ExecutingQuest[0].m_cTriggerCount[5];
  v11 = this->m_ExecutingQuest[0].m_QuestNode;
  *(_DWORD *)&this->m_ExecutingQuest[1].m_cTriggerCount[9] = *(_DWORD *)&this->m_ExecutingQuest[0].m_cTriggerCount[9];
  this->m_ExecutingQuest[1].m_QuestNode = v11;
  this->m_ExecutingQuest[0].m_wQuestID = lpQuestNode->m_wQuestID;
  this->m_ExecutingQuest[0].m_QuestNode = lpQuestNode;
  return 1;
}

//----- (004A2380) --------------------------------------------------------
char __thiscall CCharacter::CancelQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  int v3; // eax
  Quest::ExecutingQuest *i; // ecx
  _DWORD *v6; // eax
  Quest::ExecutingQuest *v7; // ebx
  Quest::ExecutingQuest v8; // [esp+4h] [ebp-14h] BYREF

  v3 = 0;
  for ( i = this->m_ExecutingQuest; i->m_wQuestID != wQuestID; ++i )
  {
    if ( ++v3 >= 10 )
      return 0;
  }
  if ( v3 < 9 )
    qmemcpy(&this->m_ExecutingQuest[v3], &this->m_ExecutingQuest[v3 + 1], 4 * ((unsigned int)(20 * (9 - v3)) >> 2));
  Quest::ExecutingQuest::ExecutingQuest(&v8);
  v7 = &this->m_ExecutingQuest[9];
  *(_DWORD *)&v7->m_wQuestID = *v6;
  *(_DWORD *)&v7->m_cTriggerCount[1] = v6[1];
  *(_DWORD *)&v7->m_cTriggerCount[5] = v6[2];
  *(_DWORD *)&v7->m_cTriggerCount[9] = v6[3];
  v7->m_QuestNode = (Quest::QuestNode *)v6[4];
  return 1;
}
// 4A23E2: variable 'v6' is possibly undefined

//----- (004A2410) --------------------------------------------------------
void __thiscall CCharacter::EventAward(CCharacter *this, unsigned int dwExp, unsigned int dwGold)
{
  unsigned int Gold; // ebx

  CCharacter::IncrementExp(this, dwExp);
  if ( dwGold )
  {
    Gold = this->m_DBData.m_Info.Gold;
    CCharacter::AddGold(this, dwGold, 1);
    GAMELOG::LogTakeGold(this, Gold, this->m_DBData.m_Info.Gold, dwGold, 2u, 2u, 4u, 0);
  }
}

//----- (004A2460) --------------------------------------------------------
char __thiscall CCharacter::HasExecutingQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  int v2; // eax
  Quest::ExecutingQuest *i; // ecx

  v2 = 0;
  for ( i = this->m_ExecutingQuest; i->m_wQuestID != wQuestID; ++i )
  {
    if ( ++v2 >= 10 )
      return 0;
  }
  return 1;
}

//----- (004A2490) --------------------------------------------------------
char __thiscall CCharacter::HasHistoryQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  int v2; // eax
  unsigned __int16 *i; // ecx

  v2 = 0;
  for ( i = this->m_wHistoryQuest; *i != wQuestID; ++i )
  {
    if ( ++v2 >= 100 )
      return 0;
  }
  return 1;
}

//----- (004A24C0) --------------------------------------------------------
char __thiscall CCharacter::InsertHistoryQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  unsigned __int16 *v2; // eax
  int v4; // edx

  v2 = &this->m_wHistoryQuest[99];
  if ( this->m_wHistoryQuest[99] )
    return 0;
  v4 = 99;
  do
  {
    *v2 = *(v2 - 1);
    --v2;
    --v4;
  }
  while ( v4 );
  this->m_wHistoryQuest[0] = wQuestID;
  return 1;
}

//----- (004A2500) --------------------------------------------------------
void __thiscall CCharacter::EventEnd(CCharacter *this, unsigned __int8 cQuestIndex)
{
  int v3; // eax
  unsigned __int16 *m_wHistoryQuest; // ecx
  Quest::ExecutingQuest *v5; // eax
  Quest::ExecutingQuest *v6; // ecx
  Quest::ExecutingQuest *v7; // ebx
  Quest::QuestNode *m_QuestNode; // edi
  _DWORD *v9; // eax
  Quest::ExecutingQuest *v10; // esi
  Quest::ExecutingQuest v11; // [esp+8h] [ebp-14h] BYREF

  if ( this->m_ExecutingQuest[cQuestIndex].m_QuestNode->m_bSave )
  {
    v3 = 0;
    m_wHistoryQuest = this->m_wHistoryQuest;
    while ( *m_wHistoryQuest )
    {
      ++v3;
      ++m_wHistoryQuest;
      if ( v3 >= 100 )
        goto LABEL_7;
    }
    this->m_wHistoryQuest[v3] = this->m_ExecutingQuest[cQuestIndex].m_wQuestID;
LABEL_7:
    if ( v3 == 100 )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::EventEnd",
        aDWorkRylSource_3,
        470,
        aCid0x08x_52,
        this->m_dwCID,
        100);
  }
  v5 = &this->m_ExecutingQuest[cQuestIndex + 1];
  if ( v5 != (Quest::ExecutingQuest *)&this->m_DBData )
  {
    v6 = v5 - 1;
    do
    {
      v7 = v6;
      *(_DWORD *)&v6->m_wQuestID = *(_DWORD *)&v5->m_wQuestID;
      *(_DWORD *)&v6->m_cTriggerCount[1] = *(_DWORD *)&v5->m_cTriggerCount[1];
      *(_DWORD *)&v6->m_cTriggerCount[5] = *(_DWORD *)&v5->m_cTriggerCount[5];
      *(_DWORD *)&v6->m_cTriggerCount[9] = *(_DWORD *)&v5->m_cTriggerCount[9];
      m_QuestNode = v5->m_QuestNode;
      ++v5;
      ++v6;
      v7->m_QuestNode = m_QuestNode;
    }
    while ( v5 != (Quest::ExecutingQuest *)&this->m_DBData );
  }
  Quest::ExecutingQuest::ExecutingQuest(&v11);
  v10 = &this->m_ExecutingQuest[9];
  *(_DWORD *)&v10->m_wQuestID = *v9;
  *(_DWORD *)&v10->m_cTriggerCount[1] = v9[1];
  *(_DWORD *)&v10->m_cTriggerCount[5] = v9[2];
  *(_DWORD *)&v10->m_cTriggerCount[9] = v9[3];
  v10->m_QuestNode = (Quest::QuestNode *)v9[4];
}
// 4A25C5: variable 'v9' is possibly undefined

//----- (004A25F0) --------------------------------------------------------
char __thiscall CCharacter::DeleteHistoryQuest(CCharacter *this, unsigned __int16 wQuestID)
{
  unsigned __int16 *m_wHistoryQuest; // ecx
  int v4; // eax
  unsigned __int16 *i; // edx
  int v7; // eax

  m_wHistoryQuest = this->m_wHistoryQuest;
  v4 = 0;
  for ( i = m_wHistoryQuest; *i != wQuestID; ++i )
  {
    if ( ++v4 >= 100 )
      return 0;
  }
  v7 = 0;
  while ( wQuestID != *m_wHistoryQuest )
  {
    ++v7;
    ++m_wHistoryQuest;
    if ( v7 >= 100 )
      return 0;
  }
  if ( v7 != 99 )
    memmove(
      (unsigned __int8 *)&this->m_wHistoryQuest[v7],
      (unsigned __int8 *)&this->m_wHistoryQuest[v7 + 1],
      2 * ((998 - (2 * v7 + 802)) >> 1));
  this->m_wHistoryQuest[99] = 0;
  return 1;
}

//----- (004A2680) --------------------------------------------------------
char __thiscall CCharacter::ClearGarbage(CCharacter *this, const std::vector<Item::ItemGarbage> *vecItemGarbage)
{
  unsigned int m_dwRemainNum; // edi
  unsigned int Gold; // ebx
  char result; // al
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int i; // ebx
  Item::ItemGarbage *Myfirst; // ecx
  Item::CItem *m_lpItem; // edi
  unsigned __int8 v10; // cl
  CGameClientDispatch *v11; // eax
  unsigned __int8 cRemainNum; // [esp+10h] [ebp-8h]

  m_dwRemainNum = vecItemGarbage->_Myfirst->m_dwRemainNum;
  if ( m_dwRemainNum )
  {
    Gold = this->m_DBData.m_Info.Gold;
    result = CCharacter::DeductGold(this, m_dwRemainNum, 0);
    if ( !result )
      return result;
    GAMELOG::LogTakeGold(this, Gold, this->m_DBData.m_Info.Gold, m_dwRemainNum, 2u, 2u, 5u, 0);
    m_lpGameClientDispatch = this->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharTakeGold(
        &m_lpGameClientDispatch->m_SendStream,
        this->m_dwCID,
        m_dwRemainNum,
        2u,
        0,
        0);
  }
  for ( i = 1; ; ++i )
  {
    Myfirst = vecItemGarbage->_Myfirst;
    if ( !Myfirst || i >= vecItemGarbage->_Mylast - Myfirst )
      break;
    m_lpItem = Myfirst[i].m_lpItem;
    v10 = Myfirst[i].m_dwRemainNum;
    cRemainNum = v10;
    if ( v10 )
      m_lpItem->m_ItemData.m_cNumOrDurability = v10;
    else
      CCharacter::RemoveItem(this, m_lpItem->m_ItemData.m_ItemPos);
    v11 = this->m_lpGameClientDispatch;
    if ( v11 )
      GameClientSendPacket::SendCharDisappearItem(
        &v11->m_SendStream,
        this->m_dwCID,
        m_lpItem->m_ItemData.m_ItemPos,
        cRemainNum,
        0);
  }
  return 1;
}

//----- (004A2770) --------------------------------------------------------
void __thiscall CCharacter::CheckTrigger(
        CCharacter *this,
        unsigned __int8 cTriggerKind,
        unsigned __int16 wReferenceID,
        Position Pos,
        __int16 wCount)
{
  Quest::QuestNode **p_m_QuestNode; // ecx
  Quest::PhaseNode *v6; // ebp
  Quest::TriggerNode **Myfirst; // ebx
  unsigned int i; // edi
  Quest::TriggerNode **v9; // ecx
  Quest::TriggerNode *v10; // edx
  float **v11; // esi
  unsigned int v12; // ecx
  unsigned __int8 v13; // bl
  unsigned __int8 *v14; // ecx
  float *v15; // ecx
  double v16; // st6
  double v17; // st5
  bool v18; // cc
  int nQuestIndex; // [esp+10h] [ebp-10h]
  float v20; // [esp+14h] [ebp-Ch]
  Quest::QuestNode **v21; // [esp+18h] [ebp-8h]

  p_m_QuestNode = &this->m_ExecutingQuest[0].m_QuestNode;
  nQuestIndex = 0;
  v21 = p_m_QuestNode;
  do
  {
    if ( !*((_WORD *)p_m_QuestNode - 8) || !*p_m_QuestNode )
      break;
    v6 = (*p_m_QuestNode)->m_lstPhase._Myfirst[*((unsigned __int8 *)p_m_QuestNode - 14) - 1];
    Myfirst = v6->m_lstTrigger._Myfirst;
    for ( i = 0; Myfirst && i < v6->m_lstTrigger._Mylast - Myfirst; ++i )
    {
      v9 = v6->m_lstTrigger._Myfirst;
      v10 = v9[i];
      v11 = (float **)&v9[i];
      if ( cTriggerKind == v10->m_dwTriggerKind )
      {
        if ( cTriggerKind == 1 )
        {
          v15 = *v11;
          if ( wReferenceID == *((_DWORD *)*v11 + 1) )
          {
            v17 = Pos.m_fPointX - v15[4];
            v16 = Pos.m_fPointY - v15[5];
            v20 = Pos.m_fPointZ - v15[6];
            if ( (double)(unsigned int)(unsigned __int64)sqrt(v17 * v17 + v16 * v16 + v20 * v20) <= (*v11)[7] )
            {
LABEL_12:
              v12 = 20 * nQuestIndex + i;
              v13 = this->m_ExecutingQuest[0].m_cTriggerCount[v12];
              v14 = &this->m_ExecutingQuest[0].m_cTriggerCount[v12];
              *v14 = v13 - wCount;
              CCharacter::OperateTrigger(
                this,
                this->m_ExecutingQuest[nQuestIndex].m_wQuestID,
                this->m_ExecutingQuest[nQuestIndex].m_cPhase,
                i,
                *v14,
                Pos);
              return;
            }
          }
        }
        else if ( cTriggerKind > 3u && cTriggerKind <= 5u && wReferenceID == v10->m_dwTriggerID )
        {
          goto LABEL_12;
        }
      }
      p_m_QuestNode = v21;
    }
    p_m_QuestNode += 5;
    v18 = ++nQuestIndex < 10;
    v21 = p_m_QuestNode;
  }
  while ( v18 );
}

//----- (004A2930) --------------------------------------------------------
char __thiscall CCharacter::StartPhase(CCharacter *this, unsigned __int16 wQuestID, char cPhase)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  int v5; // eax
  Quest::ExecutingQuest *i; // ecx
  Quest::QuestNode *m_QuestNode; // esi
  Quest::PhaseNode **Myfirst; // edx
  char *v10; // ebx
  unsigned int v11; // ecx
  Quest::PhaseNode *v12; // ebp
  unsigned int v13; // esi
  Quest::TriggerNode **v14; // ecx
  char m_dwMaxCount; // al
  Quest::TriggerNode *v16; // ecx
  __int16 v17; // ax
  CSingleDispatch *DispatchTable; // eax
  __int128 v19; // [esp-1Ch] [ebp-38h] BYREF
  __int128 *v20; // [esp+4h] [ebp-18h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v22; // [esp+10h] [ebp-Ch]
  void *v23; // [esp+14h] [ebp-8h]
  int v24; // [esp+18h] [ebp-4h]
  int wQuestIDa; // [esp+20h] [ebp+4h]
  Quest::ExecutingQuest *wProtoTypeID; // [esp+24h] [ebp+8h]

  v24 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v23 = &_ehhandler__StartPhase_CCharacter__QAE_NGE_Z;
  v22 = ExceptionList;
  v5 = 0;
  for ( i = this->m_ExecutingQuest; wQuestID != i->m_wQuestID; ++i )
  {
    if ( ++v5 >= 10 )
      return 0;
  }
  m_QuestNode = this->m_ExecutingQuest[v5].m_QuestNode;
  Myfirst = m_QuestNode->m_lstPhase._Myfirst;
  v10 = (char *)this + 20 * v5;
  if ( Myfirst )
    v11 = m_QuestNode->m_lstPhase._Mylast - Myfirst;
  else
    v11 = 0;
  if ( (unsigned __int8)cPhase > v11 )
    return 0;
  v10[1002] = cPhase;
  v12 = m_QuestNode->m_lstPhase._Myfirst[(unsigned __int8)cPhase - 1];
  v13 = 0;
  wProtoTypeID = &this->m_ExecutingQuest[v5];
  while ( 1 )
  {
    v14 = v12->m_lstTrigger._Myfirst;
    if ( !v14 || v13 >= v12->m_lstTrigger._Mylast - v14 )
      break;
    m_dwMaxCount = v14[v13]->m_dwMaxCount;
    v10[v13 + 1003] = m_dwMaxCount;
    if ( !v12->m_lstTrigger._Myfirst[v13]->m_dwTriggerKind )
    {
      v10[v13 + 1003] = m_dwMaxCount - 1;
      *(_QWORD *)((char *)&v19 + 4) = *(_QWORD *)&this->m_CurrentPos.m_fPointX;
      HIDWORD(v19) = LODWORD(this->m_CurrentPos.m_fPointZ);
      CCharacter::OperateTrigger(
        this,
        wProtoTypeID->m_wQuestID,
        v10[1002],
        v13,
        v10[v13 + 1003],
        *(Position *)((char *)&v19 + 4));
    }
    v16 = v12->m_lstTrigger._Myfirst[v13];
    if ( v16->m_dwTriggerKind == 5 )
    {
      wQuestIDa = LOWORD(v16->m_dwTriggerID);
      v17 = this->m_Inventory.GetItemNum(&this->m_Inventory, wQuestIDa);
      if ( v17 )
      {
        *(_QWORD *)&v19 = 0LL;
        DWORD2(v19) = 0;
        v20 = &v19;
        CCharacter::CheckTrigger(this, 5u, wQuestIDa, (Position)v19, v17);
      }
    }
    ++v13;
  }
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v24 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
    GameClientSendPacket::SendCharQuestInfo((CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8], this);
  v24 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  return 1;
}

//----- (004A2B00) --------------------------------------------------------
int __thiscall CCharacter::ExecuteEvent(
        CCharacter *this,
        unsigned __int8 cQuestIndex,
        Quest::TriggerNode *triggerNode,
        Position Pos)
{
  const Item::ItemGarbage *v5; // eax
  unsigned int v6; // edi
  Quest::EventNode **Myfirst; // ecx
  Quest::EventNode **v8; // eax
  Quest::EventNode *v9; // edi
  unsigned int m_dwEventAmount2; // eax
  Item::CItem *v12; // eax
  Item::CItem *v13; // ebp
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CCellManager *Instance; // eax
  CCell *Cell; // eax
  unsigned int m_lpItem; // edi
  unsigned int m_fPointX; // [esp+Ch] [ebp-74h]
  unsigned int m_fPointY; // [esp+10h] [ebp-70h]
  unsigned int m_fPointZ; // [esp+14h] [ebp-6Ch]
  unsigned __int8 cNum; // [esp+28h] [ebp-58h]
  Item::ItemGarbage nIndex; // [esp+2Ch] [ebp-54h] BYREF
  std::vector<Item::ItemGarbage> vecItemGarbage; // [esp+34h] [ebp-4Ch] BYREF
  CCell::ItemInfo itemInfo; // [esp+44h] [ebp-3Ch] BYREF
  int v25; // [esp+7Ch] [ebp-4h]

  memset(&vecItemGarbage._Myfirst, 0, 12);
  v25 = 0;
  Item::ItemGarbage::ItemGarbage(&nIndex, 0, 0);
  std::vector<Item::ItemGarbage>::push_back(&vecItemGarbage, v5);
  v6 = 0;
  while ( 1 )
  {
    Myfirst = triggerNode->m_lstEvent._Myfirst;
    nIndex.m_lpItem = (Item::CItem *)v6;
    if ( !Myfirst || v6 >= triggerNode->m_lstEvent._Mylast - Myfirst )
      break;
    v8 = &Myfirst[v6];
    v9 = *v8;
    if ( (*v8)->m_dwEventKind && vecItemGarbage._Myfirst && vecItemGarbage._Mylast - vecItemGarbage._Myfirst )
      CCharacter::ClearGarbage(this, &vecItemGarbage);
    switch ( v9->m_dwEventKind )
    {
      case 0u:
        m_dwEventAmount2 = v9->m_dwEventAmount2;
        if ( m_dwEventAmount2 )
          vecItemGarbage._Myfirst->m_dwRemainNum += m_dwEventAmount2;
        if ( this->m_Inventory.DisappearItem(
               &this->m_Inventory,
               v9->m_dwEventNumber,
               v9->m_dwEventAmount,
               &vecItemGarbage) )
        {
          goto $L103104;
        }
        if ( vecItemGarbage._Myfirst )
          operator delete(vecItemGarbage._Myfirst);
        return 2;
      case 1u:
        Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v9->m_dwEventNumber);
        v13 = v12;
        if ( !v12 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::ExecuteEvent",
            aDWorkRylSource_3,
            312,
            aCid0x08x_91,
            this->m_dwCID,
            v9->m_dwEventNumber);
          if ( vecItemGarbage._Myfirst )
            operator delete(vecItemGarbage._Myfirst);
          return 3;
        }
        if ( (v12->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
          v12->m_ItemData.m_cNumOrDurability = v9->m_dwEventAmount;
        memset(&itemInfo, 0, 12);
        itemInfo.cNum = 0;
        memset(&itemInfo.UID, 0, 18);
        itemInfo.lpItem = 0;
        if ( (v12->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
          cNum = v12->m_ItemData.m_cNumOrDurability;
        else
          cNum = 1;
        CCell::SetItem(
          this->m_CellPos.m_lpCell,
          v12,
          &this->m_CurrentPos,
          &itemInfo,
          this->m_dwCID,
          this->m_dwCID,
          0,
          cNum,
          1);
        m_lpGameClientDispatch = this->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
        {
          GameClientSendPacket::SendCharAutoRouting(
            &m_lpGameClientDispatch->m_SendStream,
            this->m_dwCID,
            itemInfo.UID.nUniqueID,
            v13->m_ItemData.m_usProtoTypeID,
            cNum,
            0);
          v6 = (unsigned int)&nIndex.m_lpItem->__vftable + 1;
        }
        else
        {
$L103104:
          v6 = (unsigned int)&nIndex.m_lpItem->__vftable + 1;
        }
        break;
      case 2u:
      case 3u:
        m_fPointZ = (unsigned __int64)Pos.m_fPointZ;
        m_fPointY = (unsigned __int64)Pos.m_fPointY;
        m_fPointX = (unsigned __int64)Pos.m_fPointX;
        Instance = CCellManager::GetInstance();
        Cell = CCellManager::GetCell(Instance, 0, m_fPointX, m_fPointY, m_fPointZ);
        if ( Cell
          && CCell::SetItem(Cell, 0, &Pos, 0, this->m_dwCID, this->m_dwCID, v9->m_dwEventNumber, v9->m_dwEventAmount, 0) == 1 )
        {
          goto $L103104;
        }
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::ExecuteEvent",
          aDWorkRylSource_3,
          359,
          aCid0x08x_249,
          this->m_dwCID,
          Pos.m_fPointX,
          Pos.m_fPointY,
          Pos.m_fPointZ);
        if ( vecItemGarbage._Myfirst )
          operator delete(vecItemGarbage._Myfirst);
        return 4;
      case 4u:
        CCharacter::EventEnd(this, cQuestIndex);
        if ( vecItemGarbage._Myfirst )
          operator delete(vecItemGarbage._Myfirst);
        return 1;
      case 5u:
        CCharacter::EventAward(this, v9->m_dwEventNumber, v9->m_dwEventAmount);
        v6 = (unsigned int)&nIndex.m_lpItem->__vftable + 1;
        continue;
      case 6u:
        goto $L103104;
      case 7u:
        m_lpItem = (unsigned int)nIndex.m_lpItem;
        ++this->m_ExecutingQuest[cQuestIndex].m_cPhase;
        v6 = m_lpItem + 1;
        continue;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::ExecuteEvent",
          aDWorkRylSource_3,
          388,
          aCid0x08x_153,
          this->m_dwCID,
          v9->m_dwEventKind);
        goto $L103104;
    }
  }
  if ( vecItemGarbage._Myfirst )
  {
    if ( vecItemGarbage._Mylast - vecItemGarbage._Myfirst )
      CCharacter::ClearGarbage(this, &vecItemGarbage);
    if ( vecItemGarbage._Myfirst )
      operator delete(vecItemGarbage._Myfirst);
  }
  return 0;
}
// 4A2B40: variable 'v5' is possibly undefined
// 4A2C1D: variable 'v12' is possibly undefined

//----- (004A2F20) --------------------------------------------------------
unsigned __int16 __thiscall CCharacter::OperateTrigger(
        CCharacter *this,
        unsigned __int16 wQuestID,
        unsigned __int8 cPhase,
        unsigned __int8 cTrigger,
        unsigned __int8 cCount,
        Position Pos)
{
  unsigned int v6; // ebp
  unsigned __int16 v8; // bx
  int v9; // edi
  Quest::ExecutingQuest *m_ExecutingQuest; // eax
  int v11; // eax
  Quest::QuestNode *m_QuestNode; // ebx
  Quest::PhaseNode *v13; // ecx
  Quest::TriggerNode **Myfirst; // edx
  int v15; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  Quest::ErrorCode eError; // [esp+14h] [ebp-8h]
  bool bSave; // [esp+18h] [ebp-4h]

  v6 = 0;
  v8 = 2;
  eError = S_SUCCESS;
  bSave = 0;
  v9 = 0;
  m_ExecutingQuest = this->m_ExecutingQuest;
  while ( wQuestID != m_ExecutingQuest->m_wQuestID )
  {
    ++v9;
    ++m_ExecutingQuest;
    if ( v9 >= 10 )
      goto LABEL_17;
  }
  v11 = v9;
  if ( cPhase == this->m_ExecutingQuest[v9].m_cPhase )
  {
    m_QuestNode = this->m_ExecutingQuest[v11].m_QuestNode;
    v13 = m_QuestNode->m_lstPhase._Myfirst[cPhase - 1];
    Myfirst = v13->m_lstTrigger._Myfirst;
    if ( Myfirst )
      v6 = v13->m_lstTrigger._Mylast - Myfirst;
    if ( cTrigger < v6 )
    {
      bSave = m_QuestNode->m_bSave;
      this->m_ExecutingQuest[v11].m_cTriggerCount[cTrigger] = cCount;
      if ( !cCount
        && ((v15 = CCharacter::ExecuteEvent(this, v9, v13->m_lstTrigger._Myfirst[cTrigger], Pos), eError = v15, v15 == 2)
         || v15 == 3
         || v15 == 4) )
      {
        v8 = 5;
      }
      else
      {
        v8 = 0;
      }
    }
    else
    {
      v8 = 4;
    }
  }
  else
  {
    v8 = 3;
  }
LABEL_17:
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch
    && (GameClientSendPacket::SendCharOperateTrigger(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          wQuestID,
          cPhase,
          cTrigger,
          cCount,
          v8),
        eError == E_SERVER_ERROR) )
  {
    GameClientSendPacket::SendCharEndQuest(&this->m_lpGameClientDispatch->m_SendStream, this->m_dwCID, wQuestID, bSave);
    return v8;
  }
  else
  {
    if ( cPhase != this->m_ExecutingQuest[v9].m_cPhase )
      CCharacter::StartPhase(this, wQuestID, this->m_ExecutingQuest[v9].m_cPhase);
    return v8;
  }
}

//----- (004A30A0) --------------------------------------------------------
void __thiscall CSingleton<CQuestMgr>::~CSingleton<CQuestMgr>(CSingleton<CQuestMgr> *this)
{
  CSingleton<CQuestMgr>::ms_pSingleton = 0;
}

//----- (004A30B0) --------------------------------------------------------
void __cdecl std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
        Quest::QuestNode **_First,
        Quest::QuestNode **_Last,
        Quest::QuestNode **_Val)
{
  Quest::QuestNode **i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (004A30D0) --------------------------------------------------------
void __cdecl std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
        Quest::QuestNode **_First,
        Quest::QuestNode **_Last,
        Quest::QuestNode **_Dest)
{
  memmove((unsigned __int8 *)&_Dest[-(_Last - _First)], (unsigned __int8 *)_First, 4 * (_Last - _First));
}

//----- (004A3100) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Mid,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  Quest::QuestNode *v3; // edx
  Quest::QuestNode *v4; // esi
  Quest::QuestNode *v5; // edx

  if ( **(_WORD **)_Mid._Myptr < **(_WORD **)_First._Myptr )
  {
    v3 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v3;
  }
  if ( **(_WORD **)_Last._Myptr < **(_WORD **)_Mid._Myptr )
  {
    v4 = *_Last._Myptr;
    *_Last._Myptr = *_Mid._Myptr;
    *_Mid._Myptr = v4;
  }
  if ( **(_WORD **)_Mid._Myptr < **(_WORD **)_First._Myptr )
  {
    v5 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v5;
  }
}

//----- (004A3150) --------------------------------------------------------
int __stdcall std::vector<unsigned long>::_Ucopy<unsigned long *>(unsigned __int8 *src, int a2, unsigned __int8 *dst)
{
  int v3; // eax

  memmove(dst, src, 4 * ((a2 - (int)src) >> 2));
  return 4 * ((a2 - (int)src) >> 2) + v3;
}
// 4A3174: variable 'v3' is possibly undefined

//----- (004A3180) --------------------------------------------------------
void __cdecl std::_Median<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Mid,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  int v4; // eax
  int v5; // eax
  unsigned int v6; // esi
  Quest::QuestNode **v7; // [esp+4h] [ebp-4h]
  std::vector<Quest::QuestNode *>::iterator _Firsta; // [esp+Ch] [ebp+4h]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(_First, _Mid, _Last);
  }
  else
  {
    v5 = (v4 + 1) / 8;
    _Firsta._Myptr = (Quest::QuestNode **)(8 * v5);
    v6 = 4 * v5;
    v7 = &_First._Myptr[v5];
    std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
      _First,
      (std::vector<Quest::QuestNode *>::iterator)v7,
      (std::vector<Quest::QuestNode *>::iterator)&_First._Myptr[2 * v5]);
    std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
      (std::vector<Quest::QuestNode *>::iterator)&_Mid._Myptr[v6 / 0xFFFFFFFC],
      _Mid,
      (std::vector<Quest::QuestNode *>::iterator)&_Mid._Myptr[v6 / 4]);
    std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
      (std::vector<Quest::QuestNode *>::iterator)((char *)_Last._Myptr - (char *)_Firsta._Myptr),
      (std::vector<Quest::QuestNode *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC],
      _Last);
    std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
      (std::vector<Quest::QuestNode *>::iterator)v7,
      _Mid,
      (std::vector<Quest::QuestNode *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC]);
  }
}

//----- (004A3230) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        int _Hole,
        int _Bottom,
        Quest::QuestNode *_Val)
{
  int v4; // esi
  int v5; // eax
  bool i; // zf
  int j; // eax

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5]->m_wQuestID < _First._Myptr[v5 - 1]->m_wQuestID )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  for ( j = (v4 - 1) / 2; _Hole < v4; j = (j - 1) / 2 )
  {
    if ( _First._Myptr[j]->m_wQuestID >= _Val->m_wQuestID )
      break;
    _First._Myptr[v4] = _First._Myptr[j];
    v4 = j;
  }
  _First._Myptr[v4] = _Val;
}

//----- (004A32C0) --------------------------------------------------------
std::vector<Quest::QuestNode *>::iterator *__cdecl std::_Lower_bound<std::vector<Quest::QuestNode *>::iterator,unsigned short,int,CompareQuestIDForSearch>(
        std::vector<Quest::QuestNode *>::iterator *result,
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last,
        unsigned __int16 *_Val)
{
  Quest::QuestNode **Myptr; // esi
  int v5; // ecx
  int v6; // eax
  std::vector<Quest::QuestNode *>::iterator *v7; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    v6 = v5 / 2;
    if ( Myptr[v5 / 2]->m_wQuestID >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v6 + 1;
      v5 += -1 - v6;
    }
  }
  v7 = result;
  result->_Myptr = Myptr;
  return v7;
}

//----- (004A3310) --------------------------------------------------------
std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> *result,
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  Quest::QuestNode **Myptr; // ebx
  Quest::QuestNode **v4; // ecx
  Quest::QuestNode **i; // esi
  unsigned __int16 m_wQuestID; // ax
  unsigned __int16 v7; // dx
  unsigned __int16 v8; // dx
  unsigned __int16 v9; // ax
  Quest::QuestNode **v10; // eax
  Quest::QuestNode **v11; // ebp
  unsigned __int16 v12; // dx
  unsigned __int16 v13; // di
  Quest::QuestNode *v14; // edi
  bool v15; // zf
  Quest::QuestNode **v16; // edx
  unsigned __int16 v17; // di
  unsigned __int16 v18; // bx
  Quest::QuestNode *v19; // edi
  Quest::QuestNode *v20; // edx
  Quest::QuestNode **v21; // edx
  Quest::QuestNode *v22; // edx
  Quest::QuestNode *v23; // edi
  Quest::QuestNode *v24; // edx
  Quest::QuestNode *v25; // edi
  std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> *v26; // eax
  Quest::QuestNode *v27; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
    _First,
    (std::vector<Quest::QuestNode *>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<Quest::QuestNode *>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    m_wQuestID = (*v4)->m_wQuestID;
    v7 = (*(v4 - 1))->m_wQuestID;
    if ( m_wQuestID > v7 )
      break;
    if ( m_wQuestID < v7 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v8 = (*v4)->m_wQuestID;
    do
    {
      v9 = (*i)->m_wQuestID;
      if ( v8 > v9 )
        break;
      if ( v8 < v9 )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v10 = i;
  v11 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v10 < Myptr; ++v10 )
      {
        v12 = (*v4)->m_wQuestID;
        v13 = (*v10)->m_wQuestID;
        if ( v13 <= v12 )
        {
          if ( v13 < v12 )
            break;
          v14 = *i;
          *i = *v10;
          Myptr = _Last._Myptr;
          ++i;
          *v10 = v14;
        }
      }
      v15 = v11 == _First._Myptr;
      if ( v11 > _First._Myptr )
      {
        v16 = v11 - 1;
        do
        {
          v17 = (*v16)->m_wQuestID;
          v18 = (*v4)->m_wQuestID;
          if ( v18 <= v17 )
          {
            if ( v18 < v17 )
              break;
            v19 = *--v4;
            *v4 = *v16;
            *v16 = v19;
          }
          --v11;
          --v16;
        }
        while ( _First._Myptr < v11 );
        Myptr = _Last._Myptr;
        v15 = v11 == _First._Myptr;
      }
      if ( v15 )
        break;
      --v11;
      if ( v10 == Myptr )
      {
        if ( v11 != --v4 )
        {
          v22 = *v11;
          *v11 = *v4;
          *v4 = v22;
        }
        v23 = *(i - 1);
        v24 = *v4;
        --i;
        *v4 = v23;
        *i = v24;
      }
      else
      {
        v25 = *v10;
        *v10 = *v11;
        Myptr = _Last._Myptr;
        ++v10;
        *v11 = v25;
      }
    }
    if ( v10 == Myptr )
      break;
    if ( i != v10 )
    {
      v20 = *v4;
      *v4 = *i;
      *i = v20;
    }
    v21 = v10;
    v27 = *v4;
    *v4 = *v10;
    Myptr = _Last._Myptr;
    ++i;
    ++v10;
    ++v4;
    *v21 = v27;
  }
  v26 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v26;
}

//----- (004A3490) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  int i; // esi
  Quest::QuestNode *v3; // eax

  for ( i = (_Last._Myptr - _First._Myptr) / 2;
        i > 0;
        std::_Adjust_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          v3) )
  {
    v3 = _First._Myptr[--i];
  }
}

//----- (004A34D0) --------------------------------------------------------
void __thiscall Quest::QuestNode::~QuestNode(Quest::QuestNode *this)
{
  if ( this->m_lstPhase._Myfirst )
    operator delete(this->m_lstPhase._Myfirst);
  this->m_lstPhase._Myfirst = 0;
  this->m_lstPhase._Mylast = 0;
  this->m_lstPhase._Myend = 0;
}

//----- (004A3500) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  Quest::QuestNode **i; // esi
  unsigned __int16 m_wQuestID; // cx
  unsigned int *v4; // eax
  unsigned __int16 *v5; // ebp
  std::vector<unsigned long>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_wQuestID = (*i)->m_wQuestID;
      if ( m_wQuestID >= **(_WORD **)_First._Myptr )
      {
        v4 = (unsigned int *)(i - 1);
        if ( m_wQuestID < (*(i - 1))->m_wQuestID )
        {
          do
          {
            v5 = (unsigned __int16 *)*(v4 - 1);
            v6._Myptr = v4--;
          }
          while ( m_wQuestID < *v5 );
          if ( (Quest::QuestNode **)v6._Myptr != i )
            std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
              v6,
              (std::vector<unsigned long>::iterator)i,
              (std::vector<unsigned long>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          (std::vector<unsigned long>::iterator)_First._Myptr,
          (std::vector<unsigned long>::iterator)i,
          (std::vector<unsigned long>::iterator)(i + 1));
      }
    }
  }
}

//----- (004A3580) --------------------------------------------------------
Quest::QuestNode *__thiscall CQuestMgr::GetQuestNode(CQuestMgr *this, unsigned __int16 wQuestID)
{
  Quest::QuestNode **Mylast; // esi
  Quest::QuestNode **Myfirst; // ecx
  Quest::QuestNode *result; // eax
  std::vector<Quest::QuestNode *>::iterator itr; // [esp+4h] [ebp-4h] BYREF

  Mylast = this->m_lstQuest._Mylast;
  Myfirst = this->m_lstQuest._Myfirst;
  LOBYTE(itr._Myptr) = 0;
  std::_Lower_bound<std::vector<Quest::QuestNode *>::iterator,unsigned short,int,CompareQuestIDForSearch>(
    &itr,
    (std::vector<Quest::QuestNode *>::iterator)Myfirst,
    (std::vector<Quest::QuestNode *>::iterator)Mylast,
    &wQuestID);
  if ( itr._Myptr == Mylast )
    return 0;
  result = *itr._Myptr;
  if ( **(_WORD **)itr._Myptr != wQuestID )
    return 0;
  return result;
}

//----- (004A35D0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last)
{
  int i; // esi
  Quest::QuestNode *v3; // [esp-Ch] [ebp-14h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 2 > 1; i -= 4 )
  {
    v3 = *(Quest::QuestNode **)((char *)_First._Myptr + i - 4);
    *(Quest::QuestNode **)((char *)_First._Myptr + i - 4) = *_First._Myptr;
    std::_Adjust_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(
      _First,
      0,
      (i - 4) >> 2,
      v3);
  }
}

//----- (004A3620) --------------------------------------------------------
void __thiscall CQuestMgr::ClearQuest(CQuestMgr *this)
{
  CQuestMgr *v1; // ebx
  unsigned __int8 *Myfirst; // ebp
  Quest::QuestNode *v3; // esi
  unsigned __int8 *v4; // edi
  Quest::PhaseNode *v5; // ebx
  unsigned __int8 *Myptr; // ebp
  Quest::TriggerNode *v7; // esi
  unsigned __int8 *v8; // edi
  unsigned __int8 *v9; // edi
  Quest::EventNode *lpEvent; // [esp+Ch] [ebp-18h]
  Quest::EventNode *lpEventa; // [esp+Ch] [ebp-18h]
  std::vector<Quest::TriggerNode *>::iterator itTrigger; // [esp+10h] [ebp-14h]
  std::vector<Quest::PhaseNode *>::iterator itPhase; // [esp+14h] [ebp-10h]
  Quest::QuestNode *lpQuest; // [esp+18h] [ebp-Ch]
  std::vector<Quest::QuestNode *>::iterator itQuest; // [esp+20h] [ebp-4h]

  v1 = this;
  Myfirst = (unsigned __int8 *)this->m_lstQuest._Myfirst;
  itQuest._Myptr = (Quest::QuestNode **)Myfirst;
  if ( Myfirst != (unsigned __int8 *)this->m_lstQuest._Mylast )
  {
    do
    {
      v3 = *(Quest::QuestNode **)Myfirst;
      lpQuest = *(Quest::QuestNode **)Myfirst;
      memmove(Myfirst, Myfirst + 4, 4 * (((char *)v1->m_lstQuest._Mylast - (char *)(Myfirst + 4)) >> 2));
      --v1->m_lstQuest._Mylast;
      v4 = (unsigned __int8 *)v3->m_lstPhase._Myfirst;
      itPhase._Myptr = (Quest::PhaseNode **)v4;
      if ( v4 != (unsigned __int8 *)v3->m_lstPhase._Mylast )
      {
        do
        {
          v5 = *(Quest::PhaseNode **)v4;
          memmove(v4, v4 + 4, 4 * (((char *)v3->m_lstPhase._Mylast - (char *)(v4 + 4)) >> 2));
          --v3->m_lstPhase._Mylast;
          Myptr = (unsigned __int8 *)v5->m_lstTrigger._Myfirst;
          itTrigger._Myptr = (Quest::TriggerNode **)Myptr;
          if ( Myptr != (unsigned __int8 *)v5->m_lstTrigger._Mylast )
          {
            do
            {
              v7 = *(Quest::TriggerNode **)Myptr;
              memmove(Myptr, Myptr + 4, 4 * (((char *)v5->m_lstTrigger._Mylast - (char *)(Myptr + 4)) >> 2));
              --v5->m_lstTrigger._Mylast;
              v8 = (unsigned __int8 *)v7->m_lstEvent._Myfirst;
              if ( v8 != (unsigned __int8 *)v7->m_lstEvent._Mylast )
              {
                do
                {
                  lpEvent = *(Quest::EventNode **)v8;
                  memmove(v8, v8 + 4, 4 * (((char *)v7->m_lstEvent._Mylast - (char *)(v8 + 4)) >> 2));
                  --v7->m_lstEvent._Mylast;
                  operator delete(lpEvent);
                }
                while ( v8 != (unsigned __int8 *)v7->m_lstEvent._Mylast );
                Myptr = (unsigned __int8 *)itTrigger._Myptr;
              }
              if ( v7->m_lstEvent._Myfirst )
                operator delete(v7->m_lstEvent._Myfirst);
              v7->m_lstEvent._Myfirst = 0;
              v7->m_lstEvent._Mylast = 0;
              v7->m_lstEvent._Myend = 0;
              v9 = (unsigned __int8 *)v7->m_lstFalseEvent._Myfirst;
              if ( v9 != (unsigned __int8 *)v7->m_lstFalseEvent._Mylast )
              {
                do
                {
                  lpEventa = *(Quest::EventNode **)v9;
                  memmove(v9, v9 + 4, 4 * (((char *)v7->m_lstFalseEvent._Mylast - (char *)(v9 + 4)) >> 2));
                  --v7->m_lstFalseEvent._Mylast;
                  operator delete(lpEventa);
                }
                while ( v9 != (unsigned __int8 *)v7->m_lstFalseEvent._Mylast );
                Myptr = (unsigned __int8 *)itTrigger._Myptr;
              }
              if ( v7->m_lstFalseEvent._Myfirst )
                operator delete(v7->m_lstFalseEvent._Myfirst);
              v7->m_lstFalseEvent._Myfirst = 0;
              v7->m_lstFalseEvent._Mylast = 0;
              v7->m_lstFalseEvent._Myend = 0;
              Quest::TriggerNode::~TriggerNode(v7);
              operator delete(v7);
            }
            while ( Myptr != (unsigned __int8 *)v5->m_lstTrigger._Mylast );
            v4 = (unsigned __int8 *)itPhase._Myptr;
            v3 = lpQuest;
          }
          if ( v5->m_lstTrigger._Myfirst )
            operator delete(v5->m_lstTrigger._Myfirst);
          v5->m_lstTrigger._Myfirst = 0;
          v5->m_lstTrigger._Mylast = 0;
          v5->m_lstTrigger._Myend = 0;
          Quest::PhaseNode::~PhaseNode(v5);
          operator delete(v5);
        }
        while ( v4 != (unsigned __int8 *)v3->m_lstPhase._Mylast );
        v1 = this;
        Myfirst = (unsigned __int8 *)itQuest._Myptr;
      }
      if ( v3->m_lstPhase._Myfirst )
        operator delete(v3->m_lstPhase._Myfirst);
      v3->m_lstPhase._Myfirst = 0;
      v3->m_lstPhase._Mylast = 0;
      v3->m_lstPhase._Myend = 0;
      Quest::QuestNode::~QuestNode(v3);
      operator delete(v3);
    }
    while ( Myfirst != (unsigned __int8 *)v1->m_lstQuest._Mylast );
  }
  if ( v1->m_lstQuest._Myfirst )
    operator delete(v1->m_lstQuest._Myfirst);
  v1->m_lstQuest._Myfirst = 0;
  v1->m_lstQuest._Mylast = 0;
  v1->m_lstQuest._Myend = 0;
}

//----- (004A3850) --------------------------------------------------------
void __thiscall __noreturn std::vector<Quest::QuestNode *>::_Xlen(std::vector<Quest::QuestNode *> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004A38C0) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<Quest::QuestNode *>::iterator,int,CompareQuestIDForSort>(
        std::vector<Quest::QuestNode *>::iterator _First,
        std::vector<Quest::QuestNode *>::iterator _Last,
        int _Ideal,
        CompareQuestIDForSort _Pred)
{
  Quest::QuestNode **Myptr; // ebx
  Quest::QuestNode **v5; // edi
  int v6; // eax
  Quest::QuestNode **v8; // ebp
  std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Last._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        (std::vector<Quest::QuestNode *>::iterator)Myptr,
        (std::vector<Quest::QuestNode *>::iterator)v5);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
        &_Mid,
        (std::vector<Quest::QuestNode *>::iterator)Myptr,
        (std::vector<Quest::QuestNode *>::iterator)v5);
      v8 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFC) >= (int)(((char *)v5
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFC) )
      {
        std::_Sort<std::vector<Quest::QuestNode *>::iterator,int,CompareQuestIDForSort>(
          _Mid.second,
          (std::vector<Quest::QuestNode *>::iterator)v5,
          _Ideal,
          _Pred);
        v5 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<Quest::QuestNode *>::iterator,int,CompareQuestIDForSort>(
          (std::vector<Quest::QuestNode *>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          _Pred);
        Myptr = v8;
      }
      v6 = v5 - Myptr;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v5 - (char *)Myptr) & 0xFFFFFFFC) > 4 )
      std::_Make_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(
        (std::vector<Quest::QuestNode *>::iterator)Myptr,
        (std::vector<Quest::QuestNode *>::iterator)v5);
    std::sort_heap<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(
      (std::vector<Quest::QuestNode *>::iterator)Myptr,
      (std::vector<Quest::QuestNode *>::iterator)v5);
  }
}
// 4A3976: conditional instruction was optimized away because eax.4>=21

//----- (004A39B0) --------------------------------------------------------
void __thiscall CQuestMgr::~CQuestMgr(CQuestMgr *this)
{
  CQuestMgr::ClearQuest(this);
  if ( this->m_lstQuest._Myfirst )
    operator delete(this->m_lstQuest._Myfirst);
  this->m_lstQuest._Myfirst = 0;
  this->m_lstQuest._Mylast = 0;
  this->m_lstQuest._Myend = 0;
  CSingleton<CQuestMgr>::ms_pSingleton = 0;
}

//----- (004A39E0) --------------------------------------------------------
void __thiscall std::vector<Quest::EventNode *>::_Insert_n(
        std::vector<Quest::PhaseNode *> *this,
        std::vector<Quest::PhaseNode *>::iterator _Where,
        unsigned int _Count,
        Quest::PhaseNode *const *_Val)
{
  Quest::PhaseNode **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  Quest::PhaseNode **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  Quest::PhaseNode **v24; // ecx
  Quest::QuestNode **v25; // edi
  Quest::PhaseNode **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<Quest::PhaseNode *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (Quest::PhaseNode *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<unsigned short>::_Xlen((std::vector<unsigned short> *)this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (Quest::PhaseNode **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (Quest::PhaseNode **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                               &Mylast[-v23],
                                               (int)Mylast,
                                               Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (Quest::PhaseNode **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (Quest::PhaseNode **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 4A3AC4: variable 'v15' is possibly undefined

//----- (004A3BC0) --------------------------------------------------------
void __thiscall std::vector<Quest::QuestNode *>::_Insert_n(
        std::vector<Quest::QuestNode *> *this,
        std::vector<Quest::QuestNode *>::iterator _Where,
        unsigned int _Count,
        Quest::QuestNode *const *_Val)
{
  Quest::QuestNode **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  Quest::QuestNode **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  Quest::QuestNode **v24; // ecx
  Quest::QuestNode **v25; // edi
  Quest::QuestNode **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<Quest::QuestNode *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (Quest::QuestNode *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<Quest::QuestNode *>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (Quest::QuestNode **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (Quest::QuestNode **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                               &Mylast[-v23],
                                               (int)Mylast,
                                               Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(_Where._Myptr, v25, (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          _Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          this,
          this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (Quest::QuestNode **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          _Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (Quest::QuestNode **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 4A3CA4: variable 'v15' is possibly undefined

//----- (004A3DA0) --------------------------------------------------------
void __thiscall CQuestMgr::SortQuests(CQuestMgr *this)
{
  std::_Sort<std::vector<Quest::QuestNode *>::iterator,int,CompareQuestIDForSort>(
    (std::vector<Quest::QuestNode *>::iterator)this->m_lstQuest._Myfirst,
    (std::vector<Quest::QuestNode *>::iterator)this->m_lstQuest._Mylast,
    this->m_lstQuest._Mylast - this->m_lstQuest._Myfirst,
    0);
}

//----- (004A3DD0) --------------------------------------------------------
void __thiscall CQuestMgr::CQuestMgr(CQuestMgr *this)
{
  CSingleton<CQuestMgr>::ms_pSingleton = this;
  this->m_lstQuest._Myfirst = 0;
  this->m_lstQuest._Mylast = 0;
  this->m_lstQuest._Myend = 0;
  this->m_lpQuestNode = 0;
  this->m_lpPhaseNode = 0;
  this->m_lpTriggerNode = 0;
  this->m_bFalseEvent = 0;
  this->m_bDangerousEvent = 0;
}

//----- (004A3E20) --------------------------------------------------------
void __thiscall std::vector<Quest::TriggerNode *>::push_back(
        std::vector<Quest::PhaseNode *> *this,
        Quest::PhaseNode **_Val)
{
  Quest::PhaseNode **Myfirst; // esi
  unsigned int v3; // edx
  Quest::PhaseNode **Mylast; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v3 = this->_Mylast - Myfirst;
  else
    v3 = 0;
  if ( Myfirst && v3 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    *Mylast = *_Val;
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Quest::EventNode *>::_Insert_n(this, (std::vector<Quest::PhaseNode *>::iterator)this->_Mylast, 1u, _Val);
  }
}

//----- (004A3E70) --------------------------------------------------------
char __thiscall CQuestMgr::AddQuest(CQuestMgr *this, Quest::QuestNode *questNode)
{
  Quest::QuestNode **Myfirst; // ecx
  Quest::QuestNode *v4; // edi
  unsigned int v5; // edx
  Quest::QuestNode **Mylast; // eax

  Myfirst = this->m_lstQuest._Myfirst;
  v4 = questNode;
  this->m_lpQuestNode = questNode;
  if ( Myfirst )
    v5 = this->m_lstQuest._Mylast - Myfirst;
  else
    v5 = 0;
  if ( Myfirst && v5 < this->m_lstQuest._Myend - Myfirst )
  {
    Mylast = this->m_lstQuest._Mylast;
    *Mylast = v4;
    this->m_lstQuest._Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Quest::QuestNode *>::_Insert_n(
      &this->m_lstQuest,
      (std::vector<Quest::QuestNode *>::iterator)this->m_lstQuest._Mylast,
      1u,
      &questNode);
  }
  this->m_lpPhaseNode = 0;
  this->m_lpTriggerNode = 0;
  return 1;
}

//----- (004A3EE0) --------------------------------------------------------
char __thiscall CQuestMgr::AddPhase(CQuestMgr *this, Quest::PhaseNode *phaseNode)
{
  Quest::QuestNode *m_lpQuestNode; // eax

  m_lpQuestNode = this->m_lpQuestNode;
  if ( !m_lpQuestNode )
    return 0;
  this->m_lpPhaseNode = phaseNode;
  std::vector<Quest::TriggerNode *>::push_back(&m_lpQuestNode->m_lstPhase, &phaseNode);
  this->m_lpTriggerNode = 0;
  return 1;
}

//----- (004A3F20) --------------------------------------------------------
char __thiscall CQuestMgr::AddTrigger(CQuestMgr *this, Quest::TriggerNode *triggerNode)
{
  Quest::PhaseNode *m_lpPhaseNode; // eax

  m_lpPhaseNode = this->m_lpPhaseNode;
  if ( !m_lpPhaseNode )
    return 0;
  this->m_lpTriggerNode = triggerNode;
  std::vector<Quest::TriggerNode *>::push_back(
    (std::vector<Quest::PhaseNode *> *)&m_lpPhaseNode->m_lstTrigger,
    (Quest::PhaseNode **)&triggerNode);
  this->m_bFalseEvent = 0;
  this->m_bDangerousEvent = 0;
  return 1;
}

//----- (004A3F60) --------------------------------------------------------
char __thiscall CQuestMgr::AddEvent(CQuestMgr *this, Quest::EventNode *eventNode)
{
  std::vector<Quest::PhaseNode *> *m_lpTriggerNode; // edx
  Quest::EventNode *v3; // eax

  m_lpTriggerNode = (std::vector<Quest::PhaseNode *> *)this->m_lpTriggerNode;
  if ( !m_lpTriggerNode )
    return 0;
  v3 = eventNode;
  if ( eventNode->m_dwEventKind == 5 )
    this->m_bDangerousEvent = 1;
  if ( this->m_bDangerousEvent && v3->m_dwEventKind <= 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "CQuestMgr::AddEvent",
      aDWorkRylSource_14,
      74,
      (char *)&byte_4F75C4,
      this->m_lpQuestNode->m_wQuestID,
      this->m_lpPhaseNode->m_dwPhaseNumber);
    return 0;
  }
  if ( this->m_bFalseEvent )
    std::vector<Quest::TriggerNode *>::push_back(m_lpTriggerNode + 3, (Quest::PhaseNode **)&eventNode);
  else
    std::vector<Quest::TriggerNode *>::push_back(m_lpTriggerNode + 2, (Quest::PhaseNode **)&eventNode);
  return 1;
}

//----- (004A3FF0) --------------------------------------------------------
char __thiscall CSiegeObject::SkillAttack(CSiegeObject *this)
{
  char v2; // al
  int v3; // edi
  char v4; // al
  AtType attackType; // [esp+8h] [ebp-30h]
  char attackType_2; // [esp+Ah] [ebp-2Eh]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+10h] [ebp-28h] BYREF

  v2 = this->m_cUpgradeStep + 1;
  if ( (unsigned __int8)v2 >= 4u )
    v2 = 3;
  v3 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | attackType_2 & 0xF;
  attackType.m_cSkillLevel = 3;
  while ( 1 )
  {
    while ( !v3 )
    {
      if ( this->m_MonsterInfo.m_wSkillID[0] )
      {
        attackType.m_wType = this->m_MonsterInfo.m_wSkillID[0];
        ppAggresiveCreature[0] = this->m_lpTarget;
        v4 = CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, 0);
        goto LABEL_11;
      }
      v3 = 1;
    }
    if ( v3 != 1 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSiegeObject::SkillAttack",
        aDWorkRylSource_38,
        200,
        aCid0x08x_284,
        this->m_dwCID);
      return 0;
    }
    if ( !this->m_MonsterInfo.m_wSkillID[3] )
      return 0;
    attackType.m_wType = this->m_MonsterInfo.m_wSkillID[3];
    ppAggresiveCreature[0] = this->m_lpTarget;
    v4 = CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, 3);
LABEL_11:
    if ( v4 == 1 )
      return 1;
    if ( ++v3 >= 2 )
      return 0;
  }
}
// 4A4029: variable 'attackType_2' is possibly undefined

//----- (004A4100) --------------------------------------------------------
void __thiscall CSiegeObject::SetACTByObjectType(CSiegeObject *this)
{
  unsigned int m_wObjectType; // eax
  unsigned int v2; // eax

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType <= 0x150A )
  {
    if ( m_wObjectType == 5386 )
    {
      this->m_cAttackableCreatureType = 6;
      return;
    }
    if ( this->m_wObjectType > 0x14D8u )
    {
      v2 = m_wObjectType - 5337;
      if ( !v2 )
      {
        this->m_cAttackableCreatureType = 5;
        return;
      }
      if ( v2 != 48 )
        return;
    }
    else if ( m_wObjectType != 5336 )
    {
      if ( m_wObjectType == 5000 )
      {
        this->m_cAttackableCreatureType = 3;
      }
      else if ( m_wObjectType == 5288 )
      {
        this->m_cAttackableCreatureType = 4;
      }
      return;
    }
    this->m_cAttackableCreatureType = 12;
    return;
  }
  switch ( this->m_wObjectType )
  {
    case 0x153Au:
      this->m_cAttackableCreatureType = 7;
      break;
    case 0x156Au:
      this->m_cAttackableCreatureType = 8;
      break;
    case 0x159Au:
      this->m_cAttackableCreatureType = 9;
      break;
    case 0x15CAu:
      this->m_cAttackableCreatureType = 10;
      break;
    case 0x15FAu:
      this->m_cAttackableCreatureType = 11;
      break;
    default:
      return;
  }
}

//----- (004A4260) --------------------------------------------------------
char __thiscall CSiegeObject::CastleArmsDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_dwLastTick; // eax
  unsigned int *m_dwRideCID; // edi
  int v6; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter_vtbl *v9; // edx
  CSingleDispatch *DispatchTable; // eax
  char v11; // bl
  unsigned int v13; // [esp-Ch] [ebp-28h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v15; // [esp+18h] [ebp-4h]

  if ( pOffencer )
  {
    if ( this->m_nCurrentState != 5 )
    {
      m_wObjectType = this->m_wObjectType;
      if ( m_wObjectType == 5434 || m_wObjectType == 5482 )
      {
        this->m_wObjectType = 5385;
        this->m_CreatureStatus.m_nNowHP = 0;
        m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
        this->m_dwLastTime = m_dwLastTick;
        this->m_dwLastBehaviorTick = m_dwLastTick;
        this->m_lCurrentFrame = 30;
        this->m_bAttacking = 0;
        this->m_bCasting = 0;
        CSiegeObject::SetACTByObjectType(this);
        m_dwRideCID = this->m_dwRideCID;
        v6 = 10;
        do
        {
          v13 = *m_dwRideCID;
          Instance = CCreatureManager::GetInstance();
          Character = CCreatureManager::GetCharacter(Instance, v13);
          if ( Character )
          {
            *m_dwRideCID = 0;
            v9 = Character->__vftable;
            Character->m_dwRideArmsCID = 0;
            v9->Dead(Character, pOffencer);
          }
          ++m_dwRideCID;
          --v6;
        }
        while ( v6 );
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v15 = 0;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          v11 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  this->m_dwOwnerID,
                  this->m_dwCID,
                  0,
                  0,
                  0x1Du,
                  0);
          v15 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return v11;
        }
        v15 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      }
    }
  }
  return 0;
}

//----- (004A43C0) --------------------------------------------------------
char __thiscall CSiegeObject::SiegeArmsDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_dwLastTick; // eax
  unsigned int *m_dwRideCID; // edi
  int v6; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter_vtbl *v9; // edx
  CSingleDispatch *DispatchTable; // eax
  char v11; // bl
  unsigned int v13; // [esp-Ch] [ebp-28h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v15; // [esp+18h] [ebp-4h]

  if ( pOffencer )
  {
    if ( this->m_nCurrentState != 5 )
    {
      m_wObjectType = this->m_wObjectType;
      if ( m_wObjectType == 5530 || m_wObjectType == 5578 || m_wObjectType == 5626 )
      {
        this->m_CreatureStatus.m_nNowHP = 0;
        m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
        this->m_dwLastTime = m_dwLastTick;
        this->m_dwLastBehaviorTick = m_dwLastTick;
        this->m_lCurrentFrame = 30;
        this->m_bAttacking = 0;
        this->m_bCasting = 0;
        m_dwRideCID = this->m_dwRideCID;
        v6 = 10;
        do
        {
          v13 = *m_dwRideCID;
          Instance = CCreatureManager::GetInstance();
          Character = CCreatureManager::GetCharacter(Instance, v13);
          if ( Character )
          {
            v9 = Character->__vftable;
            Character->m_dwRideArmsCID = 0;
            v9->Dead(Character, pOffencer);
          }
          *m_dwRideCID++ = 0;
          --v6;
        }
        while ( v6 );
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v15 = 0;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          v11 = GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  0,
                  this->m_dwCID,
                  0,
                  0xAu,
                  0);
          v15 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return v11;
        }
        v15 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      }
    }
  }
  return 0;
}

//----- (004A4500) --------------------------------------------------------
void __thiscall CSiegeObject::AttackBehavior(CSiegeObject *this, unsigned int dwTick)
{
  CPerformanceCheck *Instance; // eax
  unsigned __int16 m_wObjectType; // ax
  CAggresiveCreature *Target; // eax
  double v6; // st7
  double v7; // st5
  double v8; // st5
  CPerformanceInstrument functionInstrument; // [esp+1Ch] [ebp-24h] BYREF
  int v10; // [esp+3Ch] [ebp-4h]

  functionInstrument.m_szfunctionName = "CSiegeObject::AttackBehavior";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CSiegeObject::AttackBehavior", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  m_wObjectType = this->m_wObjectType;
  v10 = 0;
  if ( m_wObjectType == 5000 || m_wObjectType == 5386 )
  {
    if ( this->m_bCasting )
    {
      CSkillMonster::CastingAttackAction(this);
    }
    else
    {
      Target = CThreat::GetTarget(&this->m_Threat);
      this->m_lpTarget = Target;
      if ( Target
        && (Target->m_dwStatusFlag & 0x40000000) == 0
        && ((v6 = Target->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX,
             v7 = Target->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ,
             v8 = sqrt(v7 * v7 + v6 * v6),
             v8 <= (double)this->m_wSearchRange)
         || this->m_bLongRangeAttacked)
        && Target->m_CreatureStatus.m_nNowHP )
      {
        if ( v8 <= (double)this->m_CreatureStatus.m_StatusInfo.m_nAttackRange * 0.0099999998
          || this->m_lCurrentFrame > 0 )
        {
          if ( !this->m_bAttacking && this->SkillAttack(this) )
            CSkillMonster::SkillAttackAction(this);
        }
        else
        {
          if ( this->SkillAttack(this) )
            CSkillMonster::SkillAttackAction(this);
          if ( !this->m_bAttacking )
            this->m_lCurrentFrame = this->m_MotionInfo.m_dwFrame;
        }
      }
      else
      {
        CMonster::CancelTarget(this);
      }
    }
  }
  v10 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
}

//----- (004A46B0) --------------------------------------------------------
void __thiscall CSiegeObject::UpdateObjectInfo(CSiegeObject *this, bool bFullHP)
{
  unsigned __int8 m_cState; // al
  unsigned __int16 m_wObjectType; // dx
  unsigned __int8 m_cUpgradeStep; // cl
  unsigned __int16 m_nNowHP; // bp
  int v7; // eax
  unsigned int v8; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax

  m_cState = this->m_cState;
  m_wObjectType = this->m_wObjectType;
  m_cUpgradeStep = this->m_cUpgradeStep;
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  v7 = 3 * m_cState;
  if ( m_wObjectType == 5000 )
    v8 = m_cUpgradeStep + 6 * (this->m_cUpgradeType + 2 * v7) + 5000;
  else
    v8 = m_cUpgradeStep + m_wObjectType + 2 * v7;
  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, v8);
  if ( MonsterProtoType )
  {
    qmemcpy(&this->m_CreatureStatus, &MonsterProtoType->m_CreatureStatus, sizeof(this->m_CreatureStatus));
    qmemcpy(&this->m_MonsterInfo, MonsterProtoType, sizeof(this->m_MonsterInfo));
  }
  if ( !bFullHP )
    this->m_CreatureStatus.m_nNowHP = m_nNowHP;
}

//----- (004A4750) --------------------------------------------------------
void __thiscall CSiegeObject::UpgradeByEmblem(
        CSiegeObject *this,
        unsigned __int8 cUpgradeType,
        unsigned __int8 cUpgradeStep)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  float *v5; // edi
  unsigned __int8 m_cDevelopSpeed; // bl
  unsigned __int8 v7; // al
  __int16 m_nDefence; // bx
  __int16 v9; // ax
  __int16 m_nMagicResistance; // bx
  __int16 v11; // ax
  unsigned __int16 m_nMaxHP; // bx
  unsigned __int64 v13; // rax
  __int16 m_nMaxDamage; // bx
  __int16 m_nMagicPower; // bx
  double m_dwDevelopGold; // st7
  unsigned int v17; // ebx
  unsigned __int64 v18; // rax
  double m_dwUpgradeGold; // st7
  signed int v20; // ebx

  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                       CSingleton<CMonsterMgr>::ms_pSingleton,
                       cUpgradeStep + 6 * cUpgradeType + 5000);
  v5 = (float *)MonsterProtoType;
  if ( MonsterProtoType )
  {
    m_cDevelopSpeed = this->m_MonsterInfo.m_cDevelopSpeed;
    this->m_MonsterInfo.m_cUpgradeSpeed += (unsigned __int64)((double)this->m_MonsterInfo.m_cUpgradeSpeed
                                                            * MonsterProtoType->m_MonsterInfo.m_fUpgradeSpeedUp);
    v7 = m_cDevelopSpeed
       + (unsigned __int64)((double)m_cDevelopSpeed * MonsterProtoType->m_MonsterInfo.m_fDevelopSpeedUp);
    m_nDefence = this->m_CreatureStatus.m_StatusInfo.m_nDefence;
    this->m_MonsterInfo.m_cDevelopSpeed = v7;
    v9 = m_nDefence + (unsigned __int64)((double)m_nDefence * v5[67]);
    m_nMagicResistance = this->m_CreatureStatus.m_StatusInfo.m_nMagicResistance;
    this->m_CreatureStatus.m_StatusInfo.m_nDefence = v9;
    v11 = m_nMagicResistance + (unsigned __int64)((double)m_nMagicResistance * v5[67]);
    m_nMaxHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    this->m_CreatureStatus.m_StatusInfo.m_nMagicResistance = v11;
    v13 = (unsigned __int64)((double)m_nMaxHP * v5[69]);
    this->m_CreatureStatus.m_nNowHP += v13;
    this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v13 + m_nMaxHP;
    m_nMaxDamage = this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage;
    this->m_CreatureStatus.m_StatusInfo.m_nMinDamage += (unsigned __int64)((double)this->m_CreatureStatus.m_StatusInfo.m_nMinDamage
                                                                         * v5[68]);
    LOWORD(v13) = m_nMaxDamage + (unsigned __int64)((double)m_nMaxDamage * v5[68]);
    m_nMagicPower = this->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
    this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage = v13;
    m_dwDevelopGold = (double)this->m_MonsterInfo.m_dwDevelopGold;
    LOWORD(v13) = m_nMagicPower + (unsigned __int64)((double)m_nMagicPower * v5[68]);
    v17 = this->m_MonsterInfo.m_dwDevelopGold;
    this->m_CreatureStatus.m_StatusInfo.m_nMagicPower = v13;
    v18 = (unsigned __int64)(m_dwDevelopGold * v5[63]);
    m_dwUpgradeGold = (double)(int)this->m_MonsterInfo.m_dwUpgradeGold;
    this->m_MonsterInfo.m_dwDevelopGold = v17 - v18;
    v20 = this->m_MonsterInfo.m_dwUpgradeGold;
    if ( v20 < 0 )
      m_dwUpgradeGold = m_dwUpgradeGold + 4294967300.0;
    this->m_MonsterInfo.m_dwUpgradeGold = v20 - (unsigned __int64)(m_dwUpgradeGold * v5[65]);
  }
}

//----- (004A4910) --------------------------------------------------------
void __thiscall CSiegeObject::DegradeByEmblem(
        CSiegeObject *this,
        unsigned __int8 cUpgradeType,
        unsigned __int8 cUpgradeStep)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // edi
  unsigned int KID; // eax
  const CMonsterMgr::MonsterProtoType *v6; // ebp
  unsigned __int16 m_nNowHP; // si
  unsigned __int64 v8; // rax

  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                       CSingleton<CMonsterMgr>::ms_pSingleton,
                       cUpgradeStep + 6 * cUpgradeType + 5000);
  KID = CSiegeObject::GetKID(this);
  v6 = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, KID);
  if ( v6 )
  {
    if ( MonsterProtoType )
    {
      m_nNowHP = this->m_CreatureStatus.m_nNowHP;
      v8 = (unsigned __int64)((double)m_nNowHP * MonsterProtoType->m_MonsterInfo.m_fHPUp);
      qmemcpy(&this->m_CreatureStatus, &v6->m_CreatureStatus, sizeof(this->m_CreatureStatus));
      qmemcpy(&this->m_MonsterInfo, v6, sizeof(this->m_MonsterInfo));
      this->m_CreatureStatus.m_nNowHP = m_nNowHP - v8;
    }
  }
}

//----- (004A49B0) --------------------------------------------------------
void __thiscall CSiegeObject::SearchAirShip(CSiegeObject *this)
{
  signed int v2; // ebp
  CCell *ConnectCell; // eax
  CCell *v4; // ebx
  CSiegeObject *FirstAirShip; // esi
  double v6; // st7
  double v7; // st6
  CSiegeObject *pCurrentTarget; // [esp+8h] [ebp-4h]

  v2 = 0;
  if ( this->m_CellPos.m_lpCell )
  {
    pCurrentTarget = 0;
    do
    {
      ConnectCell = CCell::GetConnectCell(this->m_CellPos.m_lpCell, v2);
      v4 = ConnectCell;
      if ( ConnectCell )
      {
        if ( ConnectCell->m_lstSiegeObject._Mysize )
        {
          FirstAirShip = CCell::GetFirstAirShip(ConnectCell);
          if ( FirstAirShip )
          {
            while ( 1 )
            {
              if ( FirstAirShip->m_CreatureStatus.m_nNowHP )
              {
                if ( this->IsEnemy(this, FirstAirShip) == HOSTILITY && (FirstAirShip->m_dwStatusFlag & 0x60010000) == 0 )
                {
                  v6 = FirstAirShip->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
                  v7 = FirstAirShip->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
                  if ( v7 * v7 + v6 * v6 < 2025.0 )
                    break;
                }
              }
              FirstAirShip = CCell::GetNextAirShip(v4);
              if ( !FirstAirShip )
                goto LABEL_14;
            }
            pCurrentTarget = FirstAirShip;
          }
        }
      }
LABEL_14:
      ++v2;
    }
    while ( v2 < 9 );
    if ( pCurrentTarget )
    {
      CThreat::AddToThreatList(&this->m_Threat, pCurrentTarget, 1);
      this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 100);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::SearchAirShip",
      aDWorkRylSource_38,
      355,
      aCid0x08_0,
      this->m_dwCID);
  }
}

//----- (004A4AC0) --------------------------------------------------------
char __thiscall CSiegeObject::CampDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // eax
  int v6; // eax
  CCell *m_lpCell; // ecx
  CSingleDispatch *DispatchTable; // eax
  char v9; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-14h] BYREF
  int v11; // [esp+14h] [ebp-4h]

  if ( this->m_nCurrentState == 5 )
    return 0;
  this->m_CreatureStatus.m_nNowHP = 0;
  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  this->m_bCasting = 0;
  v6 = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  m_lpCell = this->m_CellPos.m_lpCell;
  this->m_nCurrentState = v6;
  if ( m_lpCell )
  {
    CCell::DeleteCreature(
      m_lpCell,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    this->m_CellPos.m_lpCell = 0;
  }
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v11 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v9 = GameClientSendPacket::SendCharCampCmdToDBAgent(
           (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
           this->m_dwCID,
           this->m_dwOwnerID,
           0,
           0xBu,
           0);
    v11 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return v9;
  }
  else
  {
    v11 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return 0;
  }
}

//----- (004A4BF0) --------------------------------------------------------
char __thiscall CSiegeObject::EmblemDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebp
  unsigned int v8; // eax
  char v9; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+Ch] [ebp-14h] BYREF
  int v11; // [esp+1Ch] [ebp-4h]

  if ( !pOffencer || this->m_nCurrentState == 5 )
    return 0;
  this->m_CreatureStatus.m_nNowHP = 0;
  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  this->m_bCasting = 0;
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
  v11 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v8 = pOffencer->GetGID(pOffencer);
    v9 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
           (CSendStream *)&m_lpDispatch[8],
           this->m_dwOwnerID,
           this->m_dwCID,
           0,
           v8,
           5u,
           0);
    v11 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return v9;
  }
  else
  {
    v11 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return 0;
  }
}

//----- (004A4D10) --------------------------------------------------------
char __thiscall CSiegeObject::GateDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // eax
  CSingleDispatch *DispatchTable; // eax
  char v6; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v9; // [esp+18h] [ebp-4h]

  if ( pOffencer && this->m_nCurrentState != 5 )
  {
    this->m_CreatureStatus.m_nNowHP = 0;
    m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
    this->m_dwLastTime = m_dwLastTick;
    this->m_dwLastBehaviorTick = m_dwLastTick;
    m_nCurrentState = this->m_nCurrentState;
    this->m_lCurrentFrame = 30;
    this->m_bAttacking = 0;
    this->m_bCasting = 0;
    this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v9 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      v6 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
             (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
             this->m_dwOwnerID,
             this->m_dwCID,
             0,
             0,
             0x13u,
             0);
      v9 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return v6;
    }
    v9 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
  return 0;
}

//----- (004A4E20) --------------------------------------------------------
char __thiscall CSiegeObject::GuardDead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  CSingleDispatch *DispatchTable; // eax
  char v5; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v8; // [esp+18h] [ebp-4h]

  if ( pOffencer && this->m_nCurrentState != 5 )
  {
    this->m_wObjectType = 5385;
    this->m_CreatureStatus.m_nNowHP = 0;
    m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
    this->m_dwLastTime = m_dwLastTick;
    this->m_dwLastBehaviorTick = m_dwLastTick;
    this->m_lCurrentFrame = 30;
    this->m_bAttacking = 0;
    this->m_bCasting = 0;
    CSiegeObject::SetACTByObjectType(this);
    this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 102);
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v8 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      v5 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
             (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
             this->m_dwOwnerID,
             this->m_dwCID,
             0,
             0,
             0x1Du,
             0);
      v8 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return v5;
    }
    v8 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
  return 0;
}

//----- (004A4F40) --------------------------------------------------------
char __thiscall CSiegeObject::Dead(CSiegeObject *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_wObjectType; // eax
  char result; // al

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType > 0x153A )
  {
    switch ( this->m_wObjectType )
    {
      case 0x156Au:
        return CSiegeObject::CastleArmsDead(this, pOffencer);
      case 0x159Au:
      case 0x15CAu:
      case 0x15FAu:
        result = CSiegeObject::SiegeArmsDead(this, pOffencer);
        break;
      default:
        return 0;
    }
  }
  else if ( m_wObjectType == 5434 )
  {
    return CSiegeObject::CastleArmsDead(this, pOffencer);
  }
  else
  {
    if ( this->m_wObjectType > 0x14D9u )
    {
      if ( m_wObjectType == 5386 )
        return CSiegeObject::GuardDead(this, pOffencer);
    }
    else
    {
      switch ( m_wObjectType )
      {
        case 0x14D9u:
          return CSiegeObject::CampDead(this, pOffencer);
        case 0x1388u:
          return CSiegeObject::EmblemDead(this, pOffencer);
        case 0x14A8u:
          return CSiegeObject::GateDead(this, pOffencer);
      }
    }
    return 0;
  }
  return result;
}

//----- (004A5050) --------------------------------------------------------
void __thiscall CSiegeObject::SearchEnemyPlayer(CSiegeObject *this)
{
  signed int v2; // ebp
  CCell *ConnectCell; // eax
  CCell *v4; // edi
  CCharacter *FirstCharacter; // esi
  double v6; // st7
  double v7; // st6
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ecx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  CCharacter *pCurrentTarget; // [esp+8h] [ebp-4h]

  v2 = 0;
  if ( this->m_CellPos.m_lpCell )
  {
    pCurrentTarget = 0;
    do
    {
      ConnectCell = CCell::GetConnectCell(this->m_CellPos.m_lpCell, v2);
      v4 = ConnectCell;
      if ( ConnectCell )
      {
        if ( ConnectCell->m_lstCharacter._Mysize )
        {
          FirstCharacter = CCell::GetFirstCharacter(ConnectCell);
          if ( FirstCharacter )
          {
            while ( 1 )
            {
              if ( FirstCharacter->m_CreatureStatus.m_nNowHP )
              {
                if ( this->IsEnemy(this, FirstCharacter) == HOSTILITY
                  && (FirstCharacter->m_dwStatusFlag & 0x60010000) == 0 )
                {
                  v6 = FirstCharacter->m_CurrentPos.m_fPointX - this->m_CurrentPos.m_fPointX;
                  v7 = FirstCharacter->m_CurrentPos.m_fPointZ - this->m_CurrentPos.m_fPointZ;
                  if ( v7 * v7 + v6 * v6 < 1024.0 )
                    break;
                }
              }
              Myhead = v4->m_lstCharacter._Myhead;
              Next = v4->m_CharacterIt._Ptr->_Next;
              v4->m_CharacterIt._Ptr = Next;
              if ( Next != Myhead )
              {
                FirstCharacter = Next->_Myval;
                if ( FirstCharacter )
                  continue;
              }
              goto LABEL_15;
            }
            pCurrentTarget = FirstCharacter;
          }
        }
      }
LABEL_15:
      ++v2;
    }
    while ( v2 < 9 );
    if ( pCurrentTarget )
    {
      CThreat::AddToThreatList(&this->m_Threat, pCurrentTarget, 1);
      this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 100);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::SearchEnemyPlayer",
      aDWorkRylSource_38,
      412,
      aCid0x08_0,
      this->m_dwCID);
  }
}

//----- (004A5170) --------------------------------------------------------
void __thiscall CSiegeObject::NormalBehavior(CSiegeObject *this, unsigned int dwTick)
{
  unsigned __int16 m_wObjectType; // ax
  int v3; // eax
  int v4; // eax

  m_wObjectType = this->m_wObjectType;
  if ( (m_wObjectType == 5000 || m_wObjectType == 5386) && !this->m_lpTarget && this->m_MonsterInfo.m_bFirstAttack )
  {
    v3 = m_wObjectType - 5000;
    if ( v3 && (v4 = v3 - 337) != 0 )
    {
      if ( v4 == 49 )
        CSiegeObject::SearchAirShip(this);
    }
    else
    {
      CSiegeObject::SearchEnemyPlayer(this);
    }
  }
}

//----- (004A51C0) --------------------------------------------------------
int __usercall lzo1x_1_do_compress@<eax>(
        unsigned int in_len@<eax>,
        const unsigned __int8 *in,
        unsigned __int8 *out,
        unsigned int *out_len,
        _DWORD *wrkmem)
{
  unsigned __int8 *v5; // esi
  const unsigned __int8 *v6; // ebp
  unsigned __int8 v7; // dl
  unsigned int v8; // eax
  unsigned int v9; // ebx
  char *v10; // edi
  unsigned int v11; // ecx
  unsigned int v12; // eax
  unsigned int v13; // eax
  unsigned int v14; // eax
  unsigned __int8 v15; // dl
  unsigned int v16; // eax
  unsigned __int8 v17; // al
  char v18; // dl
  char v19; // al
  unsigned __int8 v20; // dl
  unsigned __int8 v21; // al
  unsigned __int8 v22; // dl
  _BYTE *i; // ebx
  unsigned int v24; // eax
  unsigned int v25; // ebx
  unsigned int v26; // eax
  unsigned int v27; // ecx
  unsigned int v28; // ebx
  char v29; // al
  unsigned int v30; // eax
  char v31; // al
  unsigned int v32; // ecx
  _BYTE *v33; // esi
  char v34; // al
  _BYTE *v35; // esi
  unsigned int m_off; // [esp+10h] [ebp-18h]
  unsigned int m_offa; // [esp+10h] [ebp-18h]
  const unsigned __int8 *ii; // [esp+14h] [ebp-14h]
  unsigned __int8 tt; // [esp+18h] [ebp-10h]
  const unsigned __int8 *v41; // [esp+1Ch] [ebp-Ch]
  unsigned int v42; // [esp+20h] [ebp-8h]

  v5 = out;
  ii = in;
  v41 = &in[in_len];
  v6 = in + 4;
  do
  {
    v7 = v6[3];
    v8 = ((unsigned int)(33 * (*v6 ^ (32 * (v6[1] ^ (32 * (v6[2] ^ (v7 << 6))))))) >> 5) & 0x3FFF;
    v9 = wrkmem[v8];
    v10 = (char *)&wrkmem[v8];
    if ( v9 < (unsigned int)in )
      goto literal;
    v11 = (unsigned int)&v6[-v9];
    m_off = (unsigned int)&v6[-v9];
    if ( v6 == (const unsigned __int8 *)v9 || v11 > 0xBFFF )
      goto literal;
    if ( v11 > 0x800 && *(_BYTE *)(v9 + 3) != v7 )
    {
      v12 = ((unsigned int)(33 * (*v6 ^ (32 * (v6[1] ^ (32 * (v6[2] ^ (v7 << 6))))))) >> 5) & 0x7FF ^ 0x201F;
      v9 = wrkmem[v12];
      v10 = (char *)&wrkmem[v12];
      if ( v9 < (unsigned int)in )
        goto literal;
      v13 = (unsigned int)&v6[-v9];
      m_off = (unsigned int)&v6[-v9];
      if ( v6 == (const unsigned __int8 *)v9 || v13 > 0xBFFF || v13 > 0x800 && *(_BYTE *)(v9 + 3) != v7 )
        goto literal;
      v11 = (unsigned int)&v6[-v9];
    }
    if ( *(_WORD *)v9 == *(_WORD *)v6 && *(_BYTE *)(v9 + 2) == v6[2] )
    {
      v14 = v6 - ii;
      *(_DWORD *)v10 = v6;
      if ( v6 != ii )
      {
        if ( v14 > 3 )
        {
          if ( v14 > 0x12 )
          {
            *v5++ = 0;
            tt = v14 - 18;
            if ( v14 - 18 > 0xFF )
            {
              v42 = (v14 - 274) / 0xFF + 1;
              memset(v5, 0, v42);
              v16 = v42;
              v5 += v42;
              do
              {
                --v16;
                ++tt;
              }
              while ( v16 );
              v14 = v6 - ii;
              v11 = m_off;
            }
            v15 = tt;
          }
          else
          {
            v15 = v14 - 3;
          }
          *v5++ = v15;
        }
        else
        {
          *(v5 - 2) |= v14;
        }
        do
        {
          *v5++ = *ii;
          --v14;
          ++ii;
        }
        while ( v14 );
      }
      v17 = v6[3];
      v6 += 4;
      if ( *(_BYTE *)(v9 + 3) != v17 )
        goto LABEL_47;
      v18 = *v6++;
      if ( *(_BYTE *)(v9 + 4) != v18 )
        goto LABEL_47;
      v19 = *v6++;
      if ( *(_BYTE *)(v9 + 5) != v19
        || (v20 = *v6, ++v6, *(_BYTE *)(v9 + 6) != v20)
        || (v21 = *v6, ++v6, *(_BYTE *)(v9 + 7) != v21)
        || (v22 = *v6, ++v6, *(_BYTE *)(v9 + 8) != v22) )
      {
LABEL_47:
        v31 = (_BYTE)--v6 - (_BYTE)ii;
        if ( v11 <= 0x800 )
        {
          v32 = v11 - 1;
          *v5 = 4 * (v32 & 7 | (8 * (v31 - 1)));
          v33 = v5 + 1;
          *v33 = v32 >> 3;
          v5 = v33 + 1;
          ii = v6;
          continue;
        }
        v34 = v31 - 2;
        if ( v11 > 0x4000 )
        {
          v27 = v11 - 0x4000;
          *v5 = v34 | (v27 >> 11) & 8 | 0x10;
        }
        else
        {
          v27 = v11 - 1;
          *v5 = v34 | 0x20;
        }
        goto LABEL_52;
      }
      for ( i = (_BYTE *)(v9 + 9); v6 < v41; ++v6 )
      {
        if ( *i != *v6 )
          break;
        ++i;
      }
      v24 = m_off;
      v25 = v6 - ii;
      if ( m_off > 0x4000 )
      {
        m_offa = m_off - 0x4000;
        v29 = ((v24 - 0x4000) >> 11) & 8;
        if ( v25 <= 9 )
        {
          v27 = m_offa;
          *v5 = (v25 - 2) | v29 | 0x10;
          goto LABEL_52;
        }
        v28 = v25 - 9;
        *v5 = v29 | 0x10;
      }
      else
      {
        v26 = m_off - 1;
        m_offa = m_off - 1;
        if ( v25 <= 0x21 )
        {
          *v5 = (v25 - 2) | 0x20;
          v27 = v26;
LABEL_52:
          v35 = v5 + 1;
          *v35++ = 4 * v27;
          *v35 = v27 >> 6;
          v5 = v35 + 1;
          ii = v6;
          continue;
        }
        v28 = v25 - 33;
        *v5 = 32;
      }
      ++v5;
      if ( v28 > 0xFF )
      {
        memset(v5, 0, (v28 - 256) / 0xFF + 1);
        v30 = (v28 - 256) / 0xFF + 1;
        v5 += v30;
        do
        {
          LOBYTE(v28) = v28 + 1;
          --v30;
        }
        while ( v30 );
      }
      v27 = m_offa;
      *v5 = v28;
      goto LABEL_52;
    }
literal:
    *(_DWORD *)v10 = v6++;
  }
  while ( v6 < v41 - 13 );
  *out_len = v5 - out;
  return v41 - ii;
}

//----- (004A5510) --------------------------------------------------------
int __cdecl lzo1x_1_compress(
        const unsigned __int8 *in,
        unsigned int in_len,
        unsigned __int8 *out,
        unsigned int *out_len,
        _DWORD *wrkmem)
{
  unsigned __int8 *v5; // ecx
  unsigned __int8 *v6; // esi
  unsigned int v7; // ebx
  const unsigned __int8 *v8; // edi
  unsigned __int8 v9; // al
  unsigned int v10; // edx
  unsigned __int8 v11; // cl
  _BYTE *v12; // esi
  char in_lena; // [esp+1Ch] [ebp+8h]

  v5 = out;
  v6 = out;
  if ( in_len > 0xD )
  {
    v7 = lzo1x_1_do_compress(in_len, in, out, out_len, wrkmem);
    v6 = &out[*out_len];
    v5 = out;
  }
  else
  {
    v7 = in_len;
  }
  if ( v7 )
  {
    v8 = &in[in_len - v7];
    if ( v6 == v5 && v7 <= 0xEE )
    {
      *v6 = v7 + 17;
    }
    else
    {
      if ( v7 <= 3 )
      {
        *(v6 - 2) |= v7;
        goto LABEL_18;
      }
      if ( v7 > 0x12 )
      {
        v9 = v7 - 18;
        *v6++ = 0;
        in_lena = v7 - 18;
        if ( v7 - 18 > 0xFF )
        {
          v10 = (v7 - 274) / 0xFF + 1;
          memset(v6, 0, v10);
          v6 += v10;
          do
          {
            v11 = in_lena + 1;
            --v10;
            ++in_lena;
          }
          while ( v10 );
          v9 = v11;
          v5 = out;
        }
      }
      else
      {
        v9 = v7 - 3;
      }
      *v6 = v9;
    }
    ++v6;
    do
    {
LABEL_18:
      *v6++ = *v8++;
      --v7;
    }
    while ( v7 );
  }
  *v6 = 17;
  v12 = v6 + 1;
  *v12++ = 0;
  *v12 = 0;
  *out_len = v12 - v5 + 1;
  return 0;
}

//----- (004A5610) --------------------------------------------------------
int __cdecl lzo1x_decompress_safe(
        const unsigned __int8 *in,
        unsigned __int8 *in_len,
        unsigned __int8 *out,
        unsigned int *out_len)
{
  const unsigned __int8 *v4; // eax
  unsigned __int8 *v5; // ebx
  const unsigned __int8 *v6; // ebp
  int v7; // edx
  unsigned __int8 v8; // cl
  unsigned __int8 *v9; // edx
  bool v10; // cc
  unsigned __int8 *v11; // esi
  unsigned int v12; // ecx
  unsigned int v13; // ecx
  unsigned int v14; // ecx
  unsigned __int8 *v15; // edx
  _BYTE *v16; // esi
  unsigned int v17; // edi
  unsigned int v18; // ecx
  _BYTE *v19; // esi
  unsigned __int8 *v20; // edi
  unsigned __int8 *v21; // edi
  unsigned __int8 *v22; // edi
  unsigned __int8 *v23; // edi
  unsigned int v24; // ecx
  unsigned __int8 *v25; // edx
  _BYTE *v26; // esi
  const unsigned __int8 *ina; // [esp+14h] [ebp+4h]

  v4 = in;
  v5 = out;
  v6 = &in_len[(_DWORD)in];
  v7 = *out_len;
  *out_len = 0;
  v8 = *in;
  v9 = &out[v7];
  v10 = *in <= 0x11u;
  ina = v9;
  v11 = out;
  if ( !v10 )
  {
    v12 = v8 - 17;
    ++v4;
    if ( v12 < 4 )
      goto match_next;
    if ( v9 - out >= v12 )
    {
      if ( v6 - v4 >= v12 + 1 )
      {
        do
        {
          *v11++ = *v4++;
          --v12;
        }
        while ( v12 );
        goto first_literal_run;
      }
      goto input_overrun;
    }
output_overrun:
    *out_len = v11 - v5;
    return -5;
  }
LABEL_7:
  if ( v4 < v6 )
  {
    v13 = *v4++;
    if ( v13 >= 0x10 )
      goto match_0;
    if ( !v13 )
    {
      if ( v6 == v4 )
        goto input_overrun;
      if ( !*v4 )
      {
        while ( 1 )
        {
          v13 += 255;
          if ( v6 == ++v4 )
            break;
          if ( *v4 )
            goto LABEL_14;
        }
input_overrun:
        *out_len = v11 - v5;
        return -4;
      }
LABEL_14:
      v13 += *v4++ + 15;
    }
    if ( v9 - v11 < v13 + 3 )
      goto output_overrun;
    if ( v6 - v4 < v13 + 4 )
      goto input_overrun;
    *(_DWORD *)v11 = *(_DWORD *)v4;
    v11 += 4;
    v4 += 4;
    v14 = v13 - 1;
    if ( v14 )
    {
      if ( v14 < 4 )
      {
        do
        {
          *v11++ = *v4++;
          --v14;
        }
        while ( v14 );
      }
      else
      {
        do
        {
          *(_DWORD *)v11 = *(_DWORD *)v4;
          v14 -= 4;
          v11 += 4;
          v4 += 4;
        }
        while ( v14 >= 4 );
        for ( ; v14; --v14 )
          *v11++ = *v4++;
      }
    }
first_literal_run:
    v13 = *v4++;
    if ( v13 < 0x10 )
    {
      v15 = &v11[-(v13 >> 2) - 2049 + -4 * *v4++];
      if ( v15 < v5 )
      {
lookbehind_overrun:
        *out_len = v11 - v5;
        return -6;
      }
      if ( (unsigned int)(ina - v11) >= 3 )
      {
        *v11 = *v15;
        v16 = v11 + 1;
        *v16++ = v15[1];
        *v16 = v15[2];
        v11 = v16 + 1;
        goto match_done;
      }
      goto output_overrun;
    }
    while ( 1 )
    {
match_0:
      if ( v13 < 0x40 )
      {
        if ( v13 < 0x20 )
        {
          if ( v13 < 0x10 )
          {
            v25 = &v11[-(v13 >> 2) - 1 + -4 * *v4++];
            if ( v25 < v5 )
              goto lookbehind_overrun;
            if ( (unsigned int)(ina - v11) < 2 )
              goto output_overrun;
            *v11 = *v25;
            v26 = v11 + 1;
            *v26 = v25[1];
            v11 = v26 + 1;
            goto match_done;
          }
          v21 = &v11[-2048 * (v13 & 8)];
          v18 = v13 & 7;
          if ( !v18 )
          {
            if ( v6 == v4 )
              goto input_overrun;
            while ( !*v4 )
            {
              v18 += 255;
              if ( v6 == ++v4 )
                goto input_overrun;
            }
            v18 += *v4++ + 7;
          }
          v22 = &v21[-(*(unsigned __int16 *)v4 >> 2)];
          v4 += 2;
          if ( v22 == v11 )
          {
            *out_len = v11 - v5;
            if ( v4 == v6 )
              return 0;
            else
              return v4 < v6 ? -8 : -4;
          }
          v17 = (unsigned int)(v22 - 0x4000);
        }
        else
        {
          v18 = v13 & 0x1F;
          if ( !v18 )
          {
            if ( v6 == v4 )
              goto input_overrun;
            while ( !*v4 )
            {
              v18 += 255;
              if ( v6 == ++v4 )
                goto input_overrun;
            }
            v18 += *v4++ + 31;
          }
          v17 = (unsigned int)&v11[-(*(unsigned __int16 *)v4 >> 2) - 1];
          v4 += 2;
        }
        if ( v17 < (unsigned int)v5 )
          goto lookbehind_overrun;
        if ( ina - v11 < v18 + 2 )
        {
LABEL_75:
          v5 = out;
          goto output_overrun;
        }
        if ( v18 >= 6 && (int)&v11[-v17] >= 4 )
        {
          *(_DWORD *)v11 = *(_DWORD *)v17;
          v11 += 4;
          v23 = (unsigned __int8 *)(v17 + 4);
          v24 = v18 - 2;
          do
          {
            *(_DWORD *)v11 = *(_DWORD *)v23;
            v24 -= 4;
            v11 += 4;
            v23 += 4;
          }
          while ( v24 >= 4 );
          for ( ; v24; --v24 )
            *v11++ = *v23++;
          goto LABEL_28;
        }
      }
      else
      {
        v17 = (unsigned int)&v11[-((v13 >> 2) & 7) - 1 + -8 * *v4++];
        v18 = (v13 >> 5) - 1;
        if ( v17 < (unsigned int)v5 )
          goto lookbehind_overrun;
        if ( ina - v11 < v18 + 2 )
          goto LABEL_75;
      }
      *v11 = *(_BYTE *)v17;
      v19 = v11 + 1;
      *v19 = *(_BYTE *)(v17 + 1);
      v11 = v19 + 1;
      v20 = (unsigned __int8 *)(v17 + 2);
      do
      {
        *v11++ = *v20++;
        --v18;
      }
      while ( v18 );
LABEL_28:
      v5 = out;
match_done:
      v9 = (unsigned __int8 *)ina;
      v12 = *(v4 - 2) & 3;
      if ( (*(v4 - 2) & 3) == 0 )
        goto LABEL_7;
match_next:
      if ( v9 - v11 < v12 )
        goto output_overrun;
      if ( v6 - v4 < v12 + 1 )
        goto input_overrun;
      do
      {
        *v11++ = *v4++;
        --v12;
      }
      while ( v12 );
      v13 = *v4++;
      if ( v4 >= v6 )
      {
        v9 = (unsigned __int8 *)ina;
        goto LABEL_7;
      }
    }
  }
  *out_len = v11 - v5;
  return -7;
}

//----- (004A59D0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharAdminCmdToDBAgent(CSendStream *AgentSendStream, PktBase *lpPktBase)
{
  char *Buffer; // esi

  if ( lpPktBase )
  {
    Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x34);
    if ( Buffer )
    {
      *((_WORD *)Buffer + 6) = *(_WORD *)&lpPktBase[1].m_StartBit;
      strncpy(Buffer + 14, (const char *)&lpPktBase[1].m_Len, 0x10u);
      *((_WORD *)Buffer + 15) = HIWORD(lpPktBase[2].m_CodePage);
      *((_DWORD *)Buffer + 8) = lpPktBase[2].m_SrvInfo.dwServerInfo;
      *((PktBase *)Buffer + 3) = lpPktBase[3];
      return CSendStream::WrapHeader(AgentSendStream, 0x34u, 0x62u, 0, 0);
    }
    else
    {
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharAdminCmdToDBAgent",
      aDWorkRylSource_53,
      15,
      (char *)&byte_4F7B50);
    return 0;
  }
}

//----- (004A5A70) --------------------------------------------------------
void __cdecl LogGuild(
        unsigned __int8 cCommand,
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwSrcCID,
        unsigned int dwDstCID,
        const char *szExtraData,
        unsigned __int16 usExtraDataSize,
        unsigned __int16 usErrorCode)
{
  CGameLog *Instance; // ebp
  char *v9; // eax
  unsigned __int16 v10; // di

  Instance = CGameLog::GetInstance();
  v9 = CGameLog::ReserveBuffer(Instance, usExtraDataSize + 36);
  if ( v9 )
  {
    *((_DWORD *)v9 + 2) = Instance->m_time;
    v9[18] = cCommand;
    v9[19] = usErrorCode;
    *((_DWORD *)v9 + 5) = dwGID;
    *((_DWORD *)v9 + 7) = dwDstCID;
    v10 = 36;
    *(_DWORD *)v9 = 0;
    *((_DWORD *)v9 + 1) = dwSrcCID;
    *((_WORD *)v9 + 6) = 0;
    *((_WORD *)v9 + 7) = 0;
    *((_WORD *)v9 + 8) = 0;
    *((_DWORD *)v9 + 6) = dwSrcCID;
    v9[32] = cType;
    v9[33] = 0;
    *((_WORD *)v9 + 17) = 0;
    if ( usExtraDataSize )
    {
      if ( szExtraData )
      {
        qmemcpy(v9 + 36, szExtraData, usExtraDataSize);
        v10 = usExtraDataSize + 36;
        *((_WORD *)v9 + 17) = usExtraDataSize;
      }
    }
    Instance->m_lpLogBuffer->m_nUsage += v10;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "LogGuild",
      aDWorkRylSource_32,
      50,
      aGid0x08xComman,
      dwGID,
      cCommand,
      cType,
      dwSrcCID,
      dwDstCID);
  }
}

//----- (004A5B60) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildCreate(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwCreateCID,
        unsigned int dwCreateGold,
        unsigned __int16 usErrorCode)
{
  LogGuild(0x30u, cType, dwGID, dwCreateCID, 0, (const char *)&dwCreateGold, 4u, usErrorCode);
}

//----- (004A5B90) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildJoin(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwJoinCID,
        unsigned int dwFirstTitle,
        unsigned __int16 usErrorCode)
{
  LogGuild(0x31u, cType, dwGID, dwJoinCID, 0, (const char *)&dwFirstTitle, 4u, usErrorCode);
}

//----- (004A5BC0) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildLeave(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwPermitterCID,
        unsigned int dwLeaveCID,
        unsigned __int16 usErrorCode)
{
  LogGuild(0x33u, cType, dwGID, dwPermitterCID, dwLeaveCID, 0, 0, usErrorCode);
}

//----- (004A5BF0) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildMemberLevelAdjust(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwPermitterCID,
        unsigned int dwMemberCID,
        unsigned int dwGuildMemberLevel,
        unsigned __int16 usErrorCode)
{
  LogGuild(0x32u, cType, dwGID, dwPermitterCID, dwMemberCID, (const char *)&dwGuildMemberLevel, 4u, usErrorCode);
}

//----- (004A5C20) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildRightsChange(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwPermitterCID,
        const char *cGuildRights,
        unsigned __int16 usGuildRightsSize,
        unsigned __int16 usErrorCode)
{
  LogGuild(0x34u, cType, dwGID, dwPermitterCID, 0, cGuildRights, usGuildRightsSize, usErrorCode);
}

//----- (004A5C50) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildLevel(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwPermitterCID,
        unsigned int dwGuildLevel,
        unsigned int dwPreChangeGold,
        unsigned int dwPostChangeGold,
        unsigned __int16 usErrorCode)
{
  char szData[12]; // [esp+0h] [ebp-10h] BYREF

  *(_DWORD *)szData = dwGuildLevel;
  *(_DWORD *)&szData[4] = dwPreChangeGold;
  *(_DWORD *)&szData[8] = dwPostChangeGold;
  LogGuild(0x35u, cType, dwGID, dwPermitterCID, 0, szData, 0xCu, usErrorCode);
}

//----- (004A5CB0) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildMarkChange(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwChangerCID,
        unsigned int dwPreChangeGold,
        unsigned int dwPostChangeGold,
        const char *szGuildMarkData,
        unsigned __int16 usGuildMarkDataSize,
        unsigned __int16 usErrorCode)
{
  _BYTE *v8; // eax
  char szExtraData[4]; // [esp+8h] [ebp-1008h] BYREF
  unsigned int v10; // [esp+Ch] [ebp-1004h]
  _BYTE v11[4092]; // [esp+10h] [ebp-1000h] BYREF

  *(_DWORD *)szExtraData = dwPreChangeGold;
  v10 = dwPostChangeGold;
  v8 = v11;
  if ( szGuildMarkData && (unsigned int)usGuildMarkDataSize + 8 < 0x1000 )
  {
    qmemcpy(v11, szGuildMarkData, usGuildMarkDataSize);
    v8 = &v11[usGuildMarkDataSize];
  }
  LogGuild(0x36u, cType, dwGID, dwChangerCID, 0, szExtraData, (_WORD)v8 - (unsigned __int16)szExtraData, usErrorCode);
}

//----- (004A5D50) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildStoreGoldChange(
        unsigned __int8 cType,
        unsigned int dwGID,
        unsigned int dwChangerCID,
        unsigned int dwPreChangeGold,
        unsigned int dwPostChangeGold,
        unsigned __int16 usErrorCode)
{
  char szData[8]; // [esp+0h] [ebp-Ch] BYREF

  *(_DWORD *)szData = dwPreChangeGold;
  *(_DWORD *)&szData[4] = dwPostChangeGold;
  LogGuild(
    0x37u,
    cType,
    dwGID,
    dwChangerCID,
    0,
    szData,
    (unsigned __int16)&szData[4] - (unsigned __int16)szData + 4,
    usErrorCode);
}

//----- (004A5DB0) --------------------------------------------------------
void __cdecl GAMELOG::LogGuildDispose(
        unsigned __int8 cType,
        unsigned int dwGID,
        char *szGuildDestroyFileName,
        int nGuildDestroyLine,
        unsigned __int16 usErrorCode)
{
  unsigned __int16 v5; // ax
  char szData[520]; // [esp+8h] [ebp-318h] BYREF
  char szExt[268]; // [esp+210h] [ebp-110h] BYREF

  memset(szData, 0, sizeof(szData));
  *(_DWORD *)szData = nGuildDestroyLine;
  v5 = 4;
  if ( szGuildDestroyFileName )
  {
    _splitpath(szGuildDestroyFileName, 0, 0, &szData[4], szExt);
    strncat(&szData[4], szExt, 0x204u);
    v5 = strlen(&szData[4]) + 5;
  }
  LogGuild(0x38u, cType, dwGID, 0, 0, szData, v5, usErrorCode);
}

//----- (004A5E60) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharInstallSocket(
        CSendStream *SendStream,
        unsigned int dwCharID,
        Item::ItemPos EquipPos,
        Item::ItemPos GemPos,
        Item::CEquipment *lpEquipment,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  Item::CEquipment_vtbl *v8; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-4h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x49);
  if ( !Buffer )
    return 0;
  nItemSize = 0;
  if ( lpEquipment )
  {
    v8 = lpEquipment->__vftable;
    nItemSize = 52;
    if ( !v8->SerializeOut(lpEquipment, Buffer + 21, &nItemSize) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientSendPacket::SendCharInstallSocket",
        aDWorkRylSource_61,
        26,
        aCid0x08x_196,
        dwCharID);
  }
  *((Item::ItemPos *)Buffer + 8) = EquipPos;
  *((Item::ItemPos *)Buffer + 9) = GemPos;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[20] = nItemSize;
  return CSendStream::WrapCrypt(SendStream, nItemSize + 21, 0x39u, 0, usError);
}

//----- (004A5F10) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharItemChemical(
        CSendStream *SendStream,
        unsigned int dwCharID,
        Item::ItemPos PickkingPos,
        Item::ItemPos TargetPos,
        unsigned __int16 wPickkingID,
        char cPickkingNum,
        Item::CItem *lpResultItem,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  Item::CItem_vtbl *v10; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-4h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4C);
  if ( !Buffer )
    return 0;
  nItemSize = 0;
  if ( lpResultItem )
  {
    v10 = lpResultItem->__vftable;
    nItemSize = 52;
    if ( !v10->SerializeOut(lpResultItem, Buffer + 24, &nItemSize) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientSendPacket::SendCharItemChemical",
        aDWorkRylSource_61,
        56,
        aCid0x08x_196,
        dwCharID);
  }
  *((Item::ItemPos *)Buffer + 8) = PickkingPos;
  *((_WORD *)Buffer + 10) = wPickkingID;
  *((Item::ItemPos *)Buffer + 9) = TargetPos;
  Buffer[22] = cPickkingNum;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[23] = nItemSize;
  return CSendStream::WrapCrypt(SendStream, nItemSize + 24, 0xA1u, 0, usError);
}

//----- (004A5FD0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharUpgradeItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned int dwCurrentGold,
        Item::CItem *lpItem,
        unsigned __int8 cCurrentMineralNum,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  Item::CItem_vtbl *v8; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-4h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4A);
  if ( !Buffer )
    return 0;
  nItemSize = 0;
  if ( lpItem )
  {
    v8 = lpItem->__vftable;
    nItemSize = 52;
    if ( !v8->SerializeOut(lpItem, Buffer + 22, &nItemSize) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientSendPacket::SendCharUpgradeItem",
        aDWorkRylSource_61,
        89,
        aCid0x08x_196,
        dwCharID);
  }
  *((_DWORD *)Buffer + 4) = dwCurrentGold;
  Buffer[20] = cCurrentMineralNum;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[21] = nItemSize;
  return CSendStream::WrapCrypt(SendStream, nItemSize + 22, 0x3Cu, 0, usError);
}

//----- (004A6080) --------------------------------------------------------
void __thiscall CVirtualMachine::SetSysVars(CVirtualMachine *this)
{
  int v1; // esi
  char *v2; // edi
  char *v3; // edi

  v1 = *((_DWORD *)this + 1);
  *(_DWORD *)(v1 + 4 * (*((_DWORD *)this + 7) / 4)) = 1065353216;
  v2 = (char *)*((_DWORD *)this + 11);
  strcpy(v2, "true");
  *(_DWORD *)(v1 + 4 * (*((_DWORD *)this + 8) / 4)) = v2;
  v3 = (char *)(*((_DWORD *)this + 11) + 5);
  strcpy(v3, "false");
  *(_DWORD *)(v1 + 4 * (*((_DWORD *)this + 9) / 4)) = v3;
  *(_DWORD *)(v1 + 4 * (*((_DWORD *)this + 10) / 4)) = *((_DWORD *)this + 11) + 11;
}

//----- (004A6110) --------------------------------------------------------
void __thiscall CVirtualMachine::Execute(CVirtualMachine *this)
{
  (*((void (__cdecl **)(_DWORD))this + 3))(*((_DWORD *)this + 3));
}

//----- (004A6130) --------------------------------------------------------
int __thiscall CVirtualMachine::CallScriptFunction(CVirtualMachine *this, struct ScriptFunc a2)
{
  return ((int (__thiscall *)(CVirtualMachine *))a2.pFunc)(this);
}

//----- (004A6140) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (004A6160) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (004A61C0) --------------------------------------------------------
_DWORD *__cdecl std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Min(
        _DWORD *a1)
{
  _DWORD *result; // eax
  int *v2; // ecx

  result = a1;
  v2 = (int *)*a1;
  if ( !*(_BYTE *)(*a1 + 49) )
  {
    do
    {
      result = v2;
      v2 = (int *)*v2;
    }
    while ( !*((_BYTE *)v2 + 49) );
  }
  return result;
}

//----- (004A61E0) --------------------------------------------------------
int *__cdecl MakeFuncType(int *a1, int a2, int a3)
{
  int v3; // edx
  int v4; // ecx
  int v5; // esi
  int v6; // eax
  int v7; // eax
  int *result; // eax
  int v9; // [esp+4h] [ebp-4h] BYREF

  CharacterFightInfo::CharacterFightInfo((CharacterFightInfo *)&v9);
  v3 = (a2 << 28) | v9;
  v4 = 0;
  v5 = a3 - 4;
  do
  {
    v6 = *(_DWORD *)(v5 + 4);
    v5 += 4;
    if ( !v6 )
      break;
    v7 = (v6 & 0xF) << v4;
    v4 += 4;
    v3 |= v7;
  }
  while ( v4 < 28 );
  result = a1;
  *a1 = v3;
  return result;
}

//----- (004A6230) --------------------------------------------------------
_BYTE *__thiscall std::istream::seekg(_BYTE *this, int a2, int a3)
{
  int v4; // ecx
  int v5; // ecx
  int v6; // eax
  int v7; // edx
  std::ios_base *v8; // ecx
  char v9; // al
  _DWORD v11[6]; // [esp+10h] [ebp-18h] BYREF

  v4 = *(_DWORD *)(*(_DWORD *)this + 4);
  if ( (this[v4 + 8] & 6) == 0 )
  {
    (*(void (__thiscall **)(_DWORD, _DWORD *, int, int, int))(**(_DWORD **)&this[v4 + 40] + 32))(
      *(_DWORD *)&this[v4 + 40],
      v11,
      a2,
      a3,
      1);
    if ( v11[2] + v11[0] == std::_BADOFF )
    {
      v5 = *(_DWORD *)(*(_DWORD *)this + 4);
      v6 = *(_DWORD *)&this[v5 + 8];
      v7 = *(_DWORD *)&this[v5 + 40];
      v8 = (std::ios_base *)&this[v5];
      v9 = v6 | 2;
      if ( !v7 )
        v9 |= 4u;
      std::ios_base::clear(v8, v9, 0);
    }
  }
  return this;
}

//----- (004A62A0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (004A6300) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase(void **p)
{
  void **v1; // edi
  char result; // al
  void **i; // esi

  v1 = p;
  result = *((_BYTE *)p + 17);
  for ( i = p; !result; v1 = i )
  {
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase(i[2]);
    i = (void **)*i;
    operator delete(v1);
    result = *((_BYTE *)i + 17);
  }
  return result;
}

//----- (004A6340) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)operator new((tagHeader *)0x14);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (004A6380) --------------------------------------------------------
_DWORD *std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode()
{
  _DWORD *result; // eax

  result = operator new((tagHeader *)0x34);
  if ( result )
    *result = 0;
  if ( result != (_DWORD *)-4 )
    result[1] = 0;
  if ( result != (_DWORD *)-8 )
    result[2] = 0;
  *((_BYTE *)result + 48) = 1;
  *((_BYTE *)result + 49) = 0;
  return result;
}

//----- (004A63C0) --------------------------------------------------------
_DWORD *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::equal_range(
        _DWORD *this,
        _DWORD *a2,
        _DWORD *a3)
{
  int *v3; // edx
  int *v4; // eax
  int *v5; // ecx
  int *v6; // eax
  _DWORD *result; // eax

  v3 = (int *)this[1];
  v4 = (int *)v3[1];
  while ( !*((_BYTE *)v4 + 17) )
  {
    if ( *a3 >= (unsigned int)v4[3] )
    {
      v4 = (int *)v4[2];
    }
    else
    {
      v3 = v4;
      v4 = (int *)*v4;
    }
  }
  v5 = (int *)this[1];
  v6 = (int *)v5[1];
  while ( !*((_BYTE *)v6 + 17) )
  {
    if ( (unsigned int)v6[3] >= *a3 )
    {
      v5 = v6;
      v6 = (int *)*v6;
    }
    else
    {
      v6 = (int *)v6[2];
    }
  }
  result = a2;
  *a2 = v5;
  a2[1] = v3;
  return result;
}

//----- (004A6430) --------------------------------------------------------
void __cdecl std::_Distance<std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::iterator,unsigned int>(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a1,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *a2,
        _DWORD *a3)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v3; // edi
  _DWORD *v4; // esi

  v3 = a2;
  if ( a1._Ptr != a2 )
  {
    v4 = a3;
    do
    {
      ++*v4;
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(&a1);
    }
    while ( a1._Ptr != v3 );
  }
}

//----- (004A6470) --------------------------------------------------------
std::istream *__thiscall std::istream::read(std::istream *_Istr, int a2, int a3)
{
  int v4; // edi
  int v5; // edx
  int v6; // eax
  std::ios_base *v7; // ecx
  int v8; // eax
  std::_Mutex *v9; // eax
  int v11; // [esp+0h] [ebp-2Ch] BYREF
  std::istream::sentry v12; // [esp+Ch] [ebp-20h] BYREF
  int v13; // [esp+14h] [ebp-18h]
  std::istream *v14; // [esp+18h] [ebp-14h]
  int *v15; // [esp+1Ch] [ebp-10h]
  int v16; // [esp+28h] [ebp-4h]

  v15 = &v11;
  v4 = 0;
  v14 = _Istr;
  v13 = 0;
  _Istr->_Chcount = 0;
  std::istream::sentry::sentry(&v12, _Istr, (std::locale)1);
  v16 = 0;
  if ( v12._Ok )
  {
    v5 = **(_DWORD **)&_Istr->gap8[*(_DWORD *)(*(_DWORD *)_Istr->gap0 + 4) + 32];
    LOBYTE(v16) = 1;
    v6 = (*(int (__stdcall **)(int, int))(v5 + 24))(a2, a3);
    _Istr->_Chcount += v6;
    if ( v6 != a3 )
      v4 = 3;
  }
  v7 = (std::ios_base *)&_Istr->gap0[*(_DWORD *)(*(_DWORD *)_Istr->gap0 + 4)];
  v16 = 0;
  if ( v4 )
  {
    v8 = v4 | v7->_Mystate;
    if ( !v7[1].__vftable )
      LOBYTE(v8) = v8 | 4;
    std::ios_base::clear(v7, v8, 0);
  }
  v9 = *(std::_Mutex **)&v12._Myistr->gap8[*(_DWORD *)(*(_DWORD *)v12._Myistr + 4) + 32];
  v16 = -1;
  if ( v9 )
    std::_Mutex::_Unlock(v9 + 1);
  return _Istr;
}

//----- (004A6570) --------------------------------------------------------
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *this)
{
  if ( this->_Myval.first._Myres >= 0x10 )
    operator delete(this->_Myval.first._Bx._Ptr);
  this->_Myval.first._Myres = 15;
  this->_Myval.first._Mysize = 0;
  this->_Myval.first._Bx._Buf[0] = 0;
}

//----- (004A65A0) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Insert(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **a2,
        char a3,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Parg,
        unsigned int *_Val)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **result; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  unsigned int *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x3FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(
                                                                                                (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)this,
                                                                                                (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead,
                                                                                                _Parg,
                                                                                                (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead,
                                                                                                _Val,
                                                                                                0);
  Myhead = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead;
  _Vala = (unsigned int *)v6;
  ++this->_Mysize;
  if ( _Parg == Myhead )
  {
    Myhead->_Parent = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( a3 )
  {
    _Parg->_Left = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v6;
    v8 = this->_Myhead;
    if ( _Parg == (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Parg->_Right = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v6;
    v9 = this->_Myhead;
    if ( _Parg == (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  result = a2;
  this->_Myhead->_Parent->_Color = 1;
  *a2 = v6;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004A6750) --------------------------------------------------------
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *a2,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a3)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *result; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *p; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( a3._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = a3._Ptr;
  p = a3._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(&a3);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == p )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == p )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = a3._Ptr;
  Right = a3._Ptr->_Right;
  if ( a3._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = a3._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( p->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(p);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  result = a2;
  a2->_Ptr = a3._Ptr;
  return result;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004A6A10) --------------------------------------------------------
int __cdecl GetFuncPtr(_DWORD *a1, char *_Ptr, int a3)
{
  int *v3; // edi
  int *v4; // esi
  int v5; // eax
  int j; // eax
  int i; // eax
  std::string v9; // [esp+8h] [ebp-28h] BYREF
  int v10; // [esp+2Ch] [ebp-4h]

  v9._Myres = 15;
  v9._Mysize = 0;
  v9._Bx._Buf[0] = 0;
  std::string::assign(&v9, _Ptr, strlen(_Ptr));
  v10 = 0;
  v3 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Ubound(
         a1,
         (int)&v9);
  v4 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lbound(
         a1,
         (int)&v9);
  if ( v9._Myres >= 0x10 )
    operator delete(v9._Bx._Ptr);
  if ( v4 == v3 )
    return 0;
  while ( (v4[10] & 0xFFFFFFF) != (a3 & 0xFFFFFFF) )
  {
    if ( !*((_BYTE *)v4 + 49) )
    {
      v5 = v4[2];
      if ( *(_BYTE *)(v5 + 49) )
      {
        for ( i = v4[1]; !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 4) )
        {
          if ( v4 != *(int **)(i + 8) )
            break;
          v4 = (int *)i;
        }
        v4 = (int *)i;
      }
      else
      {
        v4 = (int *)v4[2];
        for ( j = *(_DWORD *)v5; !*(_BYTE *)(j + 49); j = *(_DWORD *)j )
          v4 = (int *)j;
      }
    }
    if ( v4 == v3 )
      return 0;
  }
  return v4[11];
}

//----- (004A6B30) --------------------------------------------------------
void __thiscall CVirtualMachine::RegisterFunction(
        CVirtualMachine *this,
        char *a2,
        enum eDataType a3,
        char *_Ptr,
        char *a5)
{
  int FuncPtr; // eax
  char *v7; // eax
  _BYTE *v8; // edi
  int ArgCount; // esi
  int v10; // edx
  _BYTE *i; // ecx
  _BYTE *v12; // ecx
  _BYTE *v13; // ecx
  char *v14; // eax

  MakeFuncType((int *)&a5, a3, (int)a5);
  FuncPtr = GetFuncPtr(*((_DWORD **)this + 5), _Ptr, (int)a5);
  if ( !FuncPtr )
  {
    v7 = SFuncType::ToString((SFuncType *)&a5, _Ptr);
    ErrorMessage2((char *)&byte_4F7E04, v7);
  }
  v8 = (_BYTE *)(FuncPtr + 3);
  if ( *(_BYTE *)(FuncPtr + 3) != 0x90 )
    ScriptSystemError("RegisterFunction Error!!( at CVirtualMachine::RegisterFunction )");
  ArgCount = SFuncType::GetArgCount((SFuncType *)&a5);
  v10 = 0;
  for ( i = v8; v10 < ArgCount; ++v10 )
  {
    *i = -1;
    v12 = i + 1;
    *v12++ = 117;
    *v12 = 4 * (v10 + 2);
    i = v12 + 1;
  }
  *i = -24;
  v13 = i + 1;
  v14 = (char *)(a2 - v13 - 4);
  *v13++ = (_BYTE)v14;
  *v13++ = BYTE1(v14);
  *v13 = BYTE2(v14);
  v13[1] = HIBYTE(v14);
}

//----- (004A6BF0) --------------------------------------------------------
int *__thiscall CVirtualMachine::GetScriptFunction(CVirtualMachine *this, int *a2, const char *a3, char *_Ptr, int a5)
{
  int FuncPtr; // esi
  char *v7; // eax
  int *result; // eax
  int v9; // edx

  MakeFuncType(&a5, (int)a3, a5);
  FuncPtr = GetFuncPtr(*((_DWORD **)this + 5), _Ptr, a5);
  if ( !FuncPtr )
  {
    v7 = SFuncType::ToString((SFuncType *)&a5, _Ptr);
    ErrorMessage2((char *)&byte_4F7E40, v7);
  }
  result = a2;
  v9 = a5;
  *a2 = FuncPtr;
  a2[1] = v9;
  return result;
}

//----- (004A6C50) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::insert(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        int a2,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator a3)
{
  unsigned int *Ptr; // ebp
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // eax
  bool v7; // cl
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v10; // edx
  int result; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  char v13; // [esp+Ch] [ebp-4h]

  Ptr = (unsigned int *)a3._Ptr;
  Myhead = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  v13 = 1;
  if ( !Parent->_Isnil )
  {
    Left = a3._Ptr->_Left;
    do
    {
      v7 = (unsigned int)Left < Parent->_Myval;
      Myhead = Parent;
      v13 = v7;
      if ( (unsigned int)Left >= Parent->_Myval )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  a3._Ptr = Myhead;
  if ( v7 )
  {
    if ( Myhead == (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead->_Left )
    {
      v10 = *std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Insert(
               this,
               (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)&a3,
               1,
               Myhead,
               Ptr);
      result = a2;
      *(_BYTE *)(a2 + 4) = 1;
      *(_DWORD *)a2 = v10;
      return result;
    }
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec(&a3);
    v9 = a3._Ptr;
  }
  if ( v9->_Myval >= *Ptr )
  {
    result = a2;
    *(_BYTE *)(a2 + 4) = 0;
    *(_DWORD *)a2 = v9;
  }
  else
  {
    v12 = *std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Insert(
             this,
             (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)&a3,
             v13,
             Myhead,
             Ptr);
    result = a2;
    *(_DWORD *)a2 = v12;
    *(_BYTE *)(a2 + 4) = 1;
  }
  return result;
}

//----- (004A6D10) --------------------------------------------------------
char *__thiscall std::ifstream::ifstream(char *this, char *_Filename, int a3, int _Prot, int a5)
{
  int v6; // edx
  std::ios *v7; // edi
  int v8; // ecx
  int v9; // eax
  int v10; // edx
  std::ios_base *v11; // ecx
  char v12; // al

  if ( a5 )
  {
    *(_DWORD *)this = &std::ifstream::`vbtable';
    *((_DWORD *)this + 25) = &std::ios::`vftable';
  }
  *(_DWORD *)&this[*(_DWORD *)(*(_DWORD *)this + 4)] = &std::istream::`vftable';
  v6 = *(_DWORD *)this;
  *((_DWORD *)this + 1) = 0;
  v7 = (std::ios *)&this[*(_DWORD *)(v6 + 4)];
  std::ios_base::_Init(v7);
  v7->_Mystrbuf = (std::streambuf *)(this + 8);
  v7->_Tiestr = 0;
  v7->_Fillch = std::ios::widen(v7, 32);
  if ( !v7->_Mystrbuf )
    std::ios_base::clear(v7, LOBYTE(v7->_Mystate) | 4, 0);
  v7->_Stdstr = 0;
  *(_DWORD *)&this[*(_DWORD *)(*(_DWORD *)this + 4)] = &std::ifstream::`vftable';
  std::streambuf::streambuf((std::streambuf *)(this + 8));
  *((_DWORD *)this + 2) = &std::filebuf::`vftable';
  *((_DWORD *)this + 20) = 0;
  this[92] = 0;
  this[84] = 0;
  std::streambuf::_Init((std::streambuf *)(this + 8));
  *((_DWORD *)this + 24) = 0;
  *((_DWORD *)this + 22) = `std::filebuf::_Init'::`2'::_Stinit;
  *((_DWORD *)this + 18) = `std::filebuf::_Init'::`2'::_Stinit;
  *((_DWORD *)this + 17) = 0;
  if ( !std::filebuf::open((std::filebuf *)(this + 8), _Filename, a3 | 1, _Prot) )
  {
    v8 = *(_DWORD *)(*(_DWORD *)this + 4);
    v9 = *(_DWORD *)&this[v8 + 8];
    v10 = *(_DWORD *)&this[v8 + 40];
    v11 = (std::ios_base *)&this[v8];
    v12 = v9 | 2;
    if ( !v10 )
      v12 |= 4u;
    std::ios_base::clear(v11, v12, 0);
  }
  return this;
}
// 4DCF70: using guessed type void *std::istream::`vftable';
// 4F7E8C: using guessed type void *std::ifstream::`vftable';
// 4FCA14: using guessed type void *std::ios::`vftable';
// 4FCA7C: using guessed type void *std::filebuf::`vftable';
// 523C50: using guessed type int `std::filebuf::_Init'::`2'::_Stinit;

//----- (004A6E50) --------------------------------------------------------
int __thiscall std::ifstream::~ifstream<char,std::char_traits<char>>(std::filebuf *this)
{
  std::filebuf *v1; // esi
  int result; // eax

  *(_DWORD *)((char *)&this[-1] + *(_DWORD *)(*(_DWORD *)&this[-2]._Closef + 4) - 8) = &std::ifstream::`vftable';
  v1 = this - 1;
  std::filebuf::~filebuf<char,std::char_traits<char>>(this - 1);
  result = *(_DWORD *)&v1[-1]._Closef;
  *(_DWORD *)((char *)v1 + *(_DWORD *)(result + 4) - 8) = &std::istream::`vftable';
  return result;
}
// 4DCF70: using guessed type void *std::istream::`vftable';
// 4F7E8C: using guessed type void *std::ifstream::`vftable';

//----- (004A6EB0) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **a2,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a3,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *a4)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v4; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **result; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator v10; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // eax

  v4 = a4;
  Ptr = a3._Ptr;
  Myhead = this->_Myhead;
  if ( a3._Ptr == Myhead->_Left && a4 == Myhead )
  {
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase((void **)&Myhead->_Parent->_Left);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    result = a2;
    *a2 = this->_Myhead->_Left;
  }
  else
  {
    if ( a3._Ptr != a4 )
    {
      do
      {
        v10._Ptr = Ptr;
        if ( !Ptr->_Isnil )
        {
          Right = Ptr->_Right;
          if ( Right->_Isnil )
          {
            for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( Ptr != i->_Right )
                break;
              Ptr = i;
            }
            Ptr = i;
          }
          else
          {
            Ptr = Ptr->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              Ptr = j;
          }
        }
        std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(this, &a3, v10);
      }
      while ( Ptr != v4 );
    }
    result = a2;
    *a2 = Ptr;
  }
  return result;
}

//----- (004A6F70) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(
        _DWORD *this,
        int **a2,
        int *p)
{
  int *v4; // ebp
  int v5; // edi
  int *v6; // ecx
  int v7; // esi
  int v8; // eax
  int **v9; // ebx
  int *v10; // eax
  int v11; // ebx
  int v12; // eax
  int **v13; // eax
  char v14; // al
  _DWORD *v15; // ecx
  _BYTE *v16; // eax
  bool v17; // zf
  int v18; // eax
  int **result; // eax
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( *((_BYTE *)p + 49) )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  v4 = p;
  std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::const_iterator::_Inc((int *)&p);
  if ( *(_BYTE *)(*v4 + 49) )
  {
    v5 = v4[2];
LABEL_8:
    v7 = v4[1];
    if ( !*(_BYTE *)(v5 + 49) )
      *(_DWORD *)(v5 + 4) = v7;
    v8 = this[1];
    if ( *(int **)(v8 + 4) == v4 )
    {
      *(_DWORD *)(v8 + 4) = v5;
    }
    else if ( *(int **)v7 == v4 )
    {
      *(_DWORD *)v7 = v5;
    }
    else
    {
      *(_DWORD *)(v7 + 8) = v5;
    }
    v9 = (int **)this[1];
    if ( *v9 == v4 )
    {
      if ( *(_BYTE *)(v5 + 49) )
        v10 = (int *)v7;
      else
        v10 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Min((_DWORD *)v5);
      *v9 = v10;
    }
    v11 = this[1];
    if ( *(int **)(v11 + 8) == v4 )
    {
      if ( *(_BYTE *)(v5 + 49) )
        *(_DWORD *)(v11 + 8) = v7;
      else
        *(_DWORD *)(v11 + 8) = std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Max(v5);
    }
    goto LABEL_35;
  }
  if ( *(_BYTE *)(v4[2] + 49) )
  {
    v5 = *v4;
    goto LABEL_8;
  }
  v6 = p;
  v5 = p[2];
  if ( p == v4 )
    goto LABEL_8;
  *(_DWORD *)(*v4 + 4) = p;
  *v6 = *v4;
  if ( v6 == (int *)v4[2] )
  {
    v7 = (int)v6;
  }
  else
  {
    v7 = v6[1];
    if ( !*(_BYTE *)(v5 + 49) )
      *(_DWORD *)(v5 + 4) = v7;
    *(_DWORD *)v7 = v5;
    v6[2] = v4[2];
    *(_DWORD *)(v4[2] + 4) = v6;
  }
  v12 = this[1];
  if ( *(int **)(v12 + 4) == v4 )
  {
    *(_DWORD *)(v12 + 4) = v6;
  }
  else
  {
    v13 = (int **)v4[1];
    if ( *v13 == v4 )
      *v13 = v6;
    else
      v13[2] = v6;
  }
  v6[1] = v4[1];
  v14 = *((_BYTE *)v6 + 48);
  *((_BYTE *)v6 + 48) = *((_BYTE *)v4 + 48);
  *((_BYTE *)v4 + 48) = v14;
LABEL_35:
  if ( *((_BYTE *)v4 + 48) == 1 )
  {
    v15 = this;
    if ( v5 != *(_DWORD *)(this[1] + 4) )
    {
      do
      {
        if ( *(_BYTE *)(v5 + 48) != 1 )
          break;
        v16 = *(_BYTE **)v7;
        if ( v5 == *(_DWORD *)v7 )
        {
          v16 = *(_BYTE **)(v7 + 8);
          if ( !v16[48] )
          {
            v16[48] = 1;
            *(_BYTE *)(v7 + 48) = 0;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              v15,
              v7);
            v16 = *(_BYTE **)(v7 + 8);
            v15 = this;
          }
          if ( v16[49] )
            goto LABEL_53;
          if ( *(_BYTE *)(*(_DWORD *)v16 + 48) != 1 || *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) != 1 )
          {
            if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) == 1 )
            {
              *(_BYTE *)(*(_DWORD *)v16 + 48) = 1;
              v16[48] = 0;
              std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
                v15,
                v16);
              v16 = *(_BYTE **)(v7 + 8);
              v15 = this;
            }
            v16[48] = *(_BYTE *)(v7 + 48);
            *(_BYTE *)(v7 + 48) = 1;
            *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) = 1;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              v15,
              v7);
            break;
          }
        }
        else
        {
          if ( !v16[48] )
          {
            v16[48] = 1;
            *(_BYTE *)(v7 + 48) = 0;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            v16 = *(_BYTE **)v7;
            v15 = this;
          }
          if ( v16[49] )
            goto LABEL_53;
          if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) != 1 || *(_BYTE *)(*(_DWORD *)v16 + 48) != 1 )
          {
            if ( *(_BYTE *)(*(_DWORD *)v16 + 48) == 1 )
            {
              *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) = 1;
              v16[48] = 0;
              std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
                v15,
                (int)v16);
              v16 = *(_BYTE **)v7;
              v15 = this;
            }
            v16[48] = *(_BYTE *)(v7 + 48);
            *(_BYTE *)(v7 + 48) = 1;
            *(_BYTE *)(*(_DWORD *)v16 + 48) = 1;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            break;
          }
        }
        v16[48] = 0;
LABEL_53:
        v5 = v7;
        v17 = v7 == *(_DWORD *)(v15[1] + 4);
        v7 = *(_DWORD *)(v7 + 4);
      }
      while ( !v17 );
    }
    *(_BYTE *)(v5 + 48) = 1;
  }
  if ( (unsigned int)v4[9] >= 0x10 )
    operator delete((void *)v4[4]);
  v4[9] = 15;
  v4[8] = 0;
  *((_BYTE *)v4 + 16) = 0;
  operator delete(v4);
  v18 = this[2];
  if ( v18 )
    this[2] = v18 - 1;
  result = a2;
  *a2 = p;
  return result;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004A7250) --------------------------------------------------------
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode(
        int a1,
        int a2,
        int a3,
        const std::string *_Right,
        char a5)
{
  CPacketDispatch *v5; // esi
  int v7; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v8; // [esp+Ch] [ebp-18h]
  void *p; // [esp+10h] [ebp-14h]
  int *v10; // [esp+14h] [ebp-10h]
  int v11; // [esp+20h] [ebp-4h]

  v10 = &v7;
  v5 = (CPacketDispatch *)operator new((tagHeader *)0x34);
  p = v5;
  v11 = 1;
  v8 = v5;
  if ( v5 )
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Node::_Node(
      (char *)v5,
      a1,
      a2,
      a3,
      _Right,
      a5);
  return v5;
}

//----- (004A72E0) --------------------------------------------------------
void __cdecl RegisterAllocatedMemory(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator a1)
{
  int v1; // [esp+0h] [ebp-8h] BYREF

  std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::insert(
    pAllocatedPtrs,
    (int)&v1,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator)&a1);
}

//----- (004A7300) --------------------------------------------------------
void __thiscall std::ifstream::`vbase destructor'(int this)
{
  std::ios *v1; // esi

  v1 = (std::ios *)(this + 100);
  std::ifstream::~ifstream<char,std::char_traits<char>>((std::filebuf *)(this + 100));
  std::ios::~ios<char,std::char_traits<char>>(v1);
}

//----- (004A7320) --------------------------------------------------------
_DWORD *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        _DWORD *a2)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v3; // edi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // ebx
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator v6; // [esp+Ch] [ebp-8h] BYREF
  int v7; // [esp+10h] [ebp-4h]

  std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::equal_range(this, &v6, a2);
  v3 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v7;
  Ptr = v6._Ptr;
  a2 = 0;
  std::_Distance<std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::iterator,unsigned int>(
    v6,
    (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v7,
    &a2);
  std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(
    this,
    &v6._Ptr,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator)Ptr,
    v3);
  return a2;
}

//----- (004A7380) --------------------------------------------------------
bool *__thiscall std::ifstream::`vector deleting destructor'(std::filebuf *this, char a2)
{
  bool *p_Closef; // esi

  p_Closef = &this[-2]._Closef;
  std::ifstream::~ifstream<char,std::char_traits<char>>(this);
  std::ios::~ios<char,std::char_traits<char>>((std::ios *)this);
  if ( (a2 & 1) != 0 )
    operator delete(p_Closef);
  return p_Closef;
}

//----- (004A73B0) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Erase(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v1; // edi
  char result; // al
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // esi

  v1 = a1;
  result = BYTE1(a1[1]._Left);
  for ( i = a1; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Erase(i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v1);
    operator delete(v1);
    result = BYTE1(i[1]._Left);
  }
  return result;
}

//----- (004A73F0) --------------------------------------------------------
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Insert(
        _DWORD *this,
        CPacketDispatch **a2,
        char a3,
        CPacketDispatch **a4,
        const std::string *_Right)
{
  CPacketDispatch *v6; // ecx
  CPacketDispatch **v7; // eax
  CPacketDispatch **v8; // eax
  int v9; // eax
  int *p_m_Session; // eax
  int v11; // esi
  _DWORD *v12; // ecx
  int *v13; // ebp
  int v14; // edx
  CPacketDispatch **result; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::string *_Righta; // [esp+68h] [ebp+10h]

  if ( this[2] >= 0x71C71C6u )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode(
         this[1],
         (int)a4,
         this[1],
         _Right,
         0);
  v7 = (CPacketDispatch **)this[1];
  _Righta = (const std::string *)v6;
  ++this[2];
  if ( a4 == v7 )
  {
    v7[1] = v6;
    *(_DWORD *)this[1] = v6;
    *(_DWORD *)(this[1] + 8) = v6;
  }
  else if ( a3 )
  {
    *a4 = v6;
    v8 = (CPacketDispatch **)this[1];
    if ( a4 == (CPacketDispatch **)*v8 )
      *v8 = v6;
  }
  else
  {
    a4[2] = v6;
    v9 = this[1];
    if ( a4 == *(CPacketDispatch ***)(v9 + 8) )
      *(_DWORD *)(v9 + 8) = v6;
  }
  p_m_Session = (int *)&v6->m_Session;
  v11 = (int)v6;
  if ( !v6->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2] )
  {
    while ( 1 )
    {
      v12 = (_DWORD *)*p_m_Session;
      v13 = *(int **)(*p_m_Session + 4);
      v14 = *v13;
      if ( *p_m_Session == *v13 )
      {
        v14 = v13[2];
        if ( *(_BYTE *)(v14 + 48) )
        {
          if ( v11 == v12[2] )
          {
            v11 = *p_m_Session;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              this,
              *p_m_Session);
          }
          *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) = 1;
          *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 48) = 0;
          std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
            this,
            *(_DWORD **)(*(_DWORD *)(v11 + 4) + 4));
          goto LABEL_21;
        }
      }
      else if ( *(_BYTE *)(v14 + 48) )
      {
        if ( v11 == *v12 )
        {
          v11 = *p_m_Session;
          std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
            this,
            (_DWORD *)*p_m_Session);
        }
        *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) = 1;
        *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 48) = 0;
        std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
          this,
          *(_DWORD *)(*(_DWORD *)(v11 + 4) + 4));
        goto LABEL_21;
      }
      *(_BYTE *)(*p_m_Session + 48) = 1;
      *(_BYTE *)(v14 + 48) = 1;
      *(_BYTE *)(*(_DWORD *)(*p_m_Session + 4) + 48) = 0;
      v11 = *(_DWORD *)(*p_m_Session + 4);
LABEL_21:
      p_m_Session = (int *)(v11 + 4);
      if ( *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) )
      {
        v6 = (CPacketDispatch *)_Righta;
        break;
      }
    }
  }
  result = a2;
  *(_BYTE *)(*(_DWORD *)(this[1] + 4) + 48) = 1;
  *a2 = v6;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004A75A0) --------------------------------------------------------
void __cdecl UnregisterAllocatedMemory(void *a1)
{
  std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(pAllocatedPtrs, &a1);
}

//----- (004A75C0) --------------------------------------------------------
int __thiscall std::set<void *>::~set<void *>(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this)
{
  int result; // eax
  int v3; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(
    this,
    (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)&v3,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator)this->_Myhead->_Left,
    this->_Myhead);
  operator delete(this->_Myhead);
  result = 0;
  this->_Myhead = 0;
  this->_Mysize = 0;
  return result;
}

//----- (004A75F0) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::insert(
        _DWORD *this,
        int a2,
        const std::string *_Right)
{
  int v3; // eax
  int v4; // ebp
  const std::string *v5; // esi
  unsigned int i; // ecx
  unsigned int v7; // ebx
  const char *v8; // edi
  unsigned int Mysize; // edx
  int v10; // ecx
  const char *v11; // esi
  int v12; // eax
  bool v13; // sf
  int v14; // eax
  CPacketDispatch *v15; // ecx
  int result; // eax
  char v17; // [esp+8h] [ebp-14h]
  int v18; // [esp+14h] [ebp-8h]
  _DWORD *v19; // [esp+18h] [ebp-4h]

  v3 = this[1];
  v4 = *(_DWORD *)(v3 + 4);
  v5 = _Right;
  v19 = this;
  v17 = 1;
  if ( !*(_BYTE *)(v4 + 49) )
  {
    for ( i = _Right->_Mysize; ; i = _Right->_Mysize )
    {
      v7 = *(_DWORD *)(v4 + 32);
      v18 = v4;
      if ( *(_DWORD *)(v4 + 36) < 0x10u )
        v8 = (const char *)(v4 + 16);
      else
        v8 = *(const char **)(v4 + 16);
      Mysize = _Right->_Mysize;
      if ( i < Mysize )
        Mysize = i;
      if ( !Mysize )
        goto LABEL_16;
      v10 = Mysize;
      if ( Mysize >= v7 )
        v10 = *(_DWORD *)(v4 + 32);
      v11 = v5->_Myres < 0x10 ? (const char *)&v5->_Bx : v5->_Bx._Ptr;
      v12 = memcmp(v11, v8, v10);
      v13 = v12 < 0;
      v5 = _Right;
      if ( !v12 )
      {
LABEL_16:
        if ( Mysize >= v7 )
          v14 = Mysize != v7;
        else
          v14 = -1;
        v13 = v14 < 0;
      }
      v17 = v13;
      v4 = v13 ? *(_DWORD *)v4 : *(_DWORD *)(v4 + 8);
      if ( *(_BYTE *)(v4 + 49) )
        break;
    }
    v3 = v18;
    this = v19;
  }
  v15 = *std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Insert(
           this,
           (CPacketDispatch **)&_Right,
           v17,
           (CPacketDispatch **)v3,
           v5);
  result = a2;
  *(_DWORD *)a2 = v15;
  *(_BYTE *)(a2 + 4) = 1;
  return result;
}

//----- (004A76E0) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(
        _DWORD *this,
        int **a2,
        int *p,
        int *a4)
{
  int *v4; // ebx
  int *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **v7; // eax
  _DWORD *v8; // eax
  int **result; // eax
  int *v10; // ecx
  int v11; // eax
  int *j; // eax
  int i; // eax

  v4 = a4;
  v5 = p;
  v7 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)this[1];
  if ( p == (int *)*v7 && a4 == (int *)v7 )
  {
    std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Erase(v7[1]);
    *(_DWORD *)(this[1] + 4) = this[1];
    v8 = (_DWORD *)this[1];
    this[2] = 0;
    *v8 = v8;
    *(_DWORD *)(this[1] + 8) = this[1];
    result = a2;
    *a2 = *(int **)this[1];
  }
  else
  {
    if ( p != a4 )
    {
      do
      {
        v10 = v5;
        if ( !*((_BYTE *)v5 + 49) )
        {
          v11 = v5[2];
          if ( *(_BYTE *)(v11 + 49) )
          {
            for ( i = v5[1]; !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 4) )
            {
              if ( v5 != *(int **)(i + 8) )
                break;
              v5 = (int *)i;
            }
            v5 = (int *)i;
          }
          else
          {
            v5 = (int *)v5[2];
            for ( j = *(int **)v11; !*((_BYTE *)j + 49); j = (int *)*j )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(
          this,
          &p,
          v10);
      }
      while ( v5 != v4 );
    }
    result = a2;
    *a2 = v5;
  }
  return result;
}

//----- (004A77A0) --------------------------------------------------------
void __thiscall CVirtualMachine::Destroy(CVirtualMachine *this)
{
  int v2; // edi
  _DWORD *v3; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v4; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  int v6; // esi
  _DWORD *v7; // ebp
  _DWORD *i; // edi
  _DWORD *v9; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator v10; // [esp+10h] [ebp-4h] BYREF

  operator delete[](*(void **)this);
  v2 = *((_DWORD *)this + 5);
  *((_DWORD *)this + 3) = 0;
  *((_DWORD *)this + 2) = 0;
  *((_DWORD *)this + 1) = 0;
  *(_DWORD *)this = 0;
  *((_DWORD *)this + 4) = 0;
  std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Erase(*(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)(*(_DWORD *)(v2 + 4) + 4));
  *(_DWORD *)(*(_DWORD *)(v2 + 4) + 4) = *(_DWORD *)(v2 + 4);
  v3 = *(_DWORD **)(v2 + 4);
  *(_DWORD *)(v2 + 8) = 0;
  *v3 = v3;
  *(_DWORD *)(*(_DWORD *)(v2 + 4) + 8) = *(_DWORD *)(v2 + 4);
  CRelocTable::Destroy(*((CRelocTable **)this + 6));
  v4 = *(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)(*((_DWORD *)this + 13)
                                                                                               + 4);
  Left = v4->_Left;
  v10._Ptr = v4->_Left;
  if ( v10._Ptr != v4 )
  {
    do
    {
      free((tagEntry *)Left->_Myval);
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(&v10);
      Left = v10._Ptr;
    }
    while ( v10._Ptr != *(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)(*((_DWORD *)this + 13) + 4) );
  }
  v6 = *((_DWORD *)this + 13);
  v7 = *(_DWORD **)(*(_DWORD *)(v6 + 4) + 4);
  for ( i = v7; !*((_BYTE *)i + 17); v7 = i )
  {
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase((void **)i[2]);
    i = (_DWORD *)*i;
    operator delete(v7);
  }
  *(_DWORD *)(*(_DWORD *)(v6 + 4) + 4) = *(_DWORD *)(v6 + 4);
  v9 = *(_DWORD **)(v6 + 4);
  *(_DWORD *)(v6 + 8) = 0;
  *v9 = v9;
  *(_DWORD *)(*(_DWORD *)(v6 + 4) + 8) = *(_DWORD *)(v6 + 4);
}

//----- (004A7870) --------------------------------------------------------
void __thiscall CVirtualMachine::Create(CVirtualMachine *this, int __formal, unsigned int a3)
{
  unsigned int *v4; // eax
  unsigned int v5; // esi
  unsigned int v6; // edx
  char *v7; // ebp
  char *v8; // eax
  Quest::PhaseNode **Myfirst; // edx
  void *v10; // edi
  char *v11; // eax
  char *v12; // ebp
  int v13; // esi
  Quest::PhaseNode **v14; // eax
  int v15; // ebp
  char v16; // cl
  char *i; // eax
  int v18; // esi
  unsigned int *v19; // ebp
  unsigned int v20; // ecx
  unsigned int v21; // eax
  int v22; // esi
  Quest::PhaseNode **Mylast; // edi
  _DWORD *v24; // ecx
  bool v25; // zf
  void *v26; // ecx
  Quest::QuestNode v27; // [esp-8h] [ebp-174h] BYREF
  void *_Right_4; // [esp+1Ch] [ebp-150h]
  int _Right_20; // [esp+2Ch] [ebp-140h]
  unsigned int _Right_24; // [esp+30h] [ebp-13Ch]
  std::string v31; // [esp+34h] [ebp-138h] BYREF
  Quest::PhaseNode **v32; // [esp+50h] [ebp-11Ch]
  int v33; // [esp+54h] [ebp-118h]
  int v34; // [esp+58h] [ebp-114h] BYREF
  char _Ptr[256]; // [esp+60h] [ebp-10Ch] BYREF
  int v36; // [esp+168h] [ebp-4h]

  CVirtualMachine::Destroy(this);
  *(_DWORD *)&v27.m_wMinLevel = a3;
  *(_DWORD *)&v27.m_wQuestID = __formal;
  v4 = CRelocTable::Create(*((CRelocTable **)this + 6), v27);
  v5 = *v4;
  v6 = v4[2];
  *(_DWORD *)&v27.m_wMinLevel = *v4 + v4[1] + v6;
  v27.m_lstPhase._Myfirst = (Quest::PhaseNode **)v4[1];
  *((_DWORD *)this + 4) = v6;
  v7 = (char *)(v4 + 3);
  v8 = (char *)operator new[](*(unsigned int *)&v27.m_wMinLevel);
  Myfirst = v27.m_lstPhase._Myfirst;
  v10 = v8;
  v11 = &v8[v5];
  *((_DWORD *)this + 2) = v11;
  *((_DWORD *)this + 3) = &v11[(_DWORD)Myfirst];
  *(_DWORD *)this = v10;
  *((_DWORD *)this + 1) = v10;
  memset(v10, 0, v5);
  qmemcpy(*((void **)this + 2), v7, (unsigned int)Myfirst);
  v12 = &v7[(_DWORD)Myfirst];
  qmemcpy(*((void **)this + 3), v12, *((_DWORD *)this + 4));
  v13 = *((_DWORD *)this + 4);
  v14 = *(Quest::PhaseNode ***)&v12[v13];
  v15 = (int)&v12[v13 + 4];
  if ( (int)v14 > 0 )
  {
    v27.m_lstPhase._Myfirst = v14;
    do
    {
      v16 = *(_BYTE *)v15;
      for ( i = _Ptr; *(_BYTE *)v15 != 10; ++i )
      {
        ++v15;
        *i = v16;
        v16 = *(_BYTE *)v15;
      }
      v18 = *((_DWORD *)this + 3);
      v19 = (unsigned int *)(v15 + 1);
      v20 = v19[1];
      *i = 0;
      v21 = *v19;
      v22 = v20 + v18;
      v15 = (int)(v19 + 2);
      SFuncType::SFuncType((SFuncType *)&v27.m_lstPhase._Mylast, v21);
      _Right_24 = 15;
      _Right_20 = 0;
      LOBYTE(_Right_4) = 0;
      std::string::assign((std::string *)&v27.m_lstPhase._Myend, _Ptr, strlen(_Ptr));
      Mylast = v27.m_lstPhase._Mylast;
      v36 = 0;
      v31._Myres = 15;
      v31._Mysize = 0;
      v31._Bx._Buf[0] = 0;
      std::string::assign(&v31, (const std::string *)&v27.m_lstPhase._Myend, 0, 0xFFFFFFFF);
      v32 = Mylast;
      v33 = v22;
      v24 = (_DWORD *)*((_DWORD *)this + 5);
      LOBYTE(v36) = 1;
      std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::insert(
        v24,
        (int)&v34,
        &v31);
      if ( v31._Myres >= 0x10 )
        operator delete(v31._Bx._Ptr);
      v31._Myres = 15;
      v31._Mysize = 0;
      v31._Bx._Buf[0] = 0;
      v36 = -1;
      if ( _Right_24 >= 0x10 )
        operator delete(_Right_4);
      v25 = v27.m_lstPhase._Myfirst == (Quest::PhaseNode **)1;
      _Right_24 = 15;
      _Right_20 = 0;
      LOBYTE(_Right_4) = 0;
      --v27.m_lstPhase._Myfirst;
    }
    while ( !v25 );
  }
  *((_DWORD *)this + 7) = *(_DWORD *)v15;
  *((_DWORD *)this + 8) = *(_DWORD *)(v15 + 4);
  *((_DWORD *)this + 9) = *(_DWORD *)(v15 + 8);
  v26 = (void *)*((_DWORD *)this + 2);
  *((_DWORD *)this + 10) = *(_DWORD *)(v15 + 12);
  CRelocTable::Relocate(*((CRelocTable **)this + 6), *((void **)this + 1), v26, *((char **)this + 3));
  *((_BYTE *)this + 48) = 1;
  CVirtualMachine::SetSysVars(this);
}

//----- (004A7AD0) --------------------------------------------------------
void __thiscall CVirtualMachine::Create(
        CVirtualMachine *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a2,
        CAggresiveCreature *a3)
{
  int v4; // eax
  CAggresiveCreature *v5; // ebp
  CSymbolTable *v6; // ecx
  void *v7; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **v9; // ecx
  int v10; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v13; // edi
  bool v14; // zf
  const char *NameOfVar; // ebp
  int v16; // edi
  int v17; // ebp
  _DWORD *v18; // ecx
  char **Var; // eax
  char **v20; // eax
  char **v21; // eax
  char **v22; // eax
  char *v23; // [esp-8h] [ebp-78h]
  char *v24; // [esp-4h] [ebp-74h]
  struct CRelocTable *v25; // [esp-4h] [ebp-74h]
  unsigned int GlobalVarSize; // [esp+10h] [ebp-60h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v27; // [esp+10h] [ebp-60h]
  CParty *StringBufferSize; // [esp+14h] [ebp-5Ch]
  int v29; // [esp+14h] [ebp-5Ch]
  int v30; // [esp+18h] [ebp-58h] BYREF
  int v31; // [esp+1Ch] [ebp-54h] BYREF
  std::string _Right; // [esp+24h] [ebp-4Ch] BYREF
  std::string v33; // [esp+40h] [ebp-30h] BYREF
  int v34; // [esp+5Ch] [ebp-14h]
  int v35; // [esp+60h] [ebp-10h]
  int v36; // [esp+6Ch] [ebp-4h]

  CVirtualMachine::Destroy(this);
  v4 = CIntermediateCode::Addressing((CIntermediateCode *)a2._Ptr, 0);
  v5 = a3;
  v6 = (CSymbolTable *)a3;
  *((_DWORD *)this + 4) = v4;
  GlobalVarSize = CSymbolTable::GetGlobalVarSize(v6);
  StringBufferSize = CSymbolTable::GetStringBufferSize(v5);
  v7 = operator new[]((unsigned int)StringBufferSize + GlobalVarSize + *((_DWORD *)this + 4));
  *(_DWORD *)this = v7;
  *((_DWORD *)this + 1) = v7;
  memset(v7, 0, GlobalVarSize);
  v24 = (char *)(GlobalVarSize + *(_DWORD *)this);
  *((_DWORD *)this + 2) = v24;
  CSymbolTable::StringBuffer((CSymbolTable *)v5, v24);
  Ptr = a2._Ptr;
  v25 = (struct CRelocTable *)*((_DWORD *)this + 6);
  v23 = (char *)StringBufferSize + *((_DWORD *)this + 2);
  v9 = (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **)a2._Ptr;
  *((_DWORD *)this + 3) = v23;
  if ( CIntermediateCode::ToMachineCode(v9, v23, v25) != *((struct CRelocTable **)this + 4) )
    ScriptSystemError(&byte_4F7EC8);
  CRelocTable::Relocate(*((CRelocTable **)this + 6), *((void **)this + 1), *((void **)this + 2), *((char **)this + 3));
  v10 = *((_DWORD *)this + 3);
  *((_BYTE *)this + 48) = 1;
  Right = Ptr->_Right;
  p_Left = &Right->_Parent->_Left;
  v27 = Right;
  v13 = *p_Left;
  v14 = *p_Left == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)p_Left;
  v29 = v10;
  a2._Ptr = *p_Left;
  if ( !v14 )
  {
    do
    {
      NameOfVar = CSymbolTable::GetNameOfVar((CSymbolTable *)v5, v13->_Myval.first);
      CSymbolTable::GetTypeOfFunc(&v30, v13->_Myval.first);
      v16 = v29 + v13->_Myval.second;
      _Right._Myres = 15;
      _Right._Mysize = 0;
      _Right._Bx._Buf[0] = 0;
      std::string::assign(&_Right, NameOfVar, strlen(NameOfVar));
      v17 = v30;
      v36 = 0;
      v33._Myres = 15;
      v33._Mysize = 0;
      v33._Bx._Buf[0] = 0;
      std::string::assign(&v33, &_Right, 0, 0xFFFFFFFF);
      v34 = v17;
      v35 = v16;
      v18 = (_DWORD *)*((_DWORD *)this + 5);
      LOBYTE(v36) = 1;
      std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::insert(
        v18,
        (int)&v31,
        &v33);
      if ( v33._Myres >= 0x10 )
        operator delete(v33._Bx._Ptr);
      v33._Myres = 15;
      v33._Mysize = 0;
      v33._Bx._Buf[0] = 0;
      v36 = -1;
      if ( _Right._Myres >= 0x10 )
        operator delete(_Right._Bx._Ptr);
      _Right._Myres = 15;
      _Right._Mysize = 0;
      _Right._Bx._Buf[0] = 0;
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(&a2);
      v13 = a2._Ptr;
      v5 = a3;
    }
    while ( a2._Ptr != v27->_Parent );
  }
  Var = CSymbolTable::FindVar((CSymbolTable *)v5, "<FloatUnit>");
  *((_DWORD *)this + 7) = CSymbolTable::GetOffsetOfVar((CSymbolTable *)v5, (int)Var);
  v20 = CSymbolTable::FindVar((CSymbolTable *)v5, "<True>");
  *((_DWORD *)this + 8) = CSymbolTable::GetOffsetOfVar((CSymbolTable *)v5, (int)v20);
  v21 = CSymbolTable::FindVar((CSymbolTable *)v5, "<False>");
  *((_DWORD *)this + 9) = CSymbolTable::GetOffsetOfVar((CSymbolTable *)v5, (int)v21);
  v22 = CSymbolTable::FindVar((CSymbolTable *)v5, "<ConvBuffer>");
  *((_DWORD *)this + 10) = CSymbolTable::GetOffsetOfVar((CSymbolTable *)v5, (int)v22);
  CVirtualMachine::SetSysVars(this);
}

//----- (004A7D50) --------------------------------------------------------
int __thiscall std::multimap<std::string,std::pair<SFuncType,void *>>::~multimap<std::string,std::pair<SFuncType,void *>>(
        int this)
{
  int result; // eax
  int v3; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(
    (_DWORD *)this,
    (int **)&v3,
    **(int ***)(this + 4),
    *(int **)(this + 4));
  operator delete(*(void **)(this + 4));
  result = 0;
  *(_DWORD *)(this + 4) = 0;
  *(_DWORD *)(this + 8) = 0;
  return result;
}

//----- (004A7D80) --------------------------------------------------------
void __thiscall CVirtualMachine::Create(CVirtualMachine *this, char *_Filename)
{
  int v3; // eax
  unsigned int v4; // edi
  _DWORD *v5; // esi
  signed int v6; // eax
  int v7; // ecx
  int v8; // eax
  int v9; // edx
  std::ios_base *v10; // ecx
  char v11; // al
  int v12; // [esp+0h] [ebp-BCh] BYREF
  __int64 v13; // [esp+8h] [ebp-B4h]
  int v14; // [esp+10h] [ebp-ACh]
  std::istream v15; // [esp+18h] [ebp-A4h] BYREF
  int v16; // [esp+78h] [ebp-44h]
  std::ios_base v17; // [esp+7Ch] [ebp-40h] BYREF
  int v18; // [esp+B8h] [ebp-4h]

  std::ifstream::ifstream(v15.gap0, _Filename, 33, 438, 1);
  v18 = 0;
  if ( !v16 )
    ErrorMessage2((char *)&byte_4F7F18, _Filename);
  std::istream::seekg(&v15, 0, 2);
  v3 = *(_DWORD *)(*(_DWORD *)v15.gap0 + 4);
  if ( (v15.gap8[v3] & 6) != 0 )
  {
    v12 = std::_BADOFF;
    v13 = std::_Fpz;
    v14 = std::fpos<int>::_Stz;
  }
  else
  {
    (*(void (__thiscall **)(_DWORD, int *, _DWORD, int, int))(**(_DWORD **)&v15.gap8[v3 + 32] + 32))(
      *(_DWORD *)&v15.gap8[v3 + 32],
      &v12,
      0,
      1,
      1);
  }
  v4 = v13 + v12;
  std::istream::seekg(&v15, 0, 0);
  v5 = operator new[](v4);
  std::istream::read(&v15, (int)v5, v4);
  v6 = 0;
  if ( v4 >> 2 )
  {
    do
      v5[v6++] ^= 0x425529ACu;
    while ( v6 < (int)(v4 >> 2) );
  }
  CVirtualMachine::Create(this, (int)v5, v4);
  operator delete[](v5);
  if ( !std::filebuf::close((std::filebuf *)v15.gap8) )
  {
    v7 = *(_DWORD *)(*(_DWORD *)v15.gap0 + 4);
    v8 = *(_DWORD *)&v15.gap8[v7];
    v9 = *(_DWORD *)&v15.gap8[v7 + 32];
    v10 = (std::ios_base *)&v15.gap0[v7];
    v11 = v8 | 2;
    if ( !v9 )
      v11 |= 4u;
    std::ios_base::clear(v10, v11, 0);
  }
  v18 = -1;
  std::ifstream::~ifstream<char,std::char_traits<char>>((std::filebuf *)&v17);
  v17.__vftable = (std::ios_base_vtbl *)&std::ios::`vftable';
  std::ios_base::~ios_base(&v17);
}
// 4FCA14: using guessed type void *std::ios::`vftable';

//----- (004A7F00) --------------------------------------------------------
CVirtualMachine *__thiscall CVirtualMachine::CVirtualMachine(CVirtualMachine *this)
{
  _DWORD *v2; // esi
  _DWORD *v3; // eax
  CRelocTable *v4; // eax
  CRelocTable *v5; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v6; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *v7; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v8; // eax
  CVirtualMachine *result; // eax

  *(_DWORD *)this = 0;
  *((_DWORD *)this + 1) = 0;
  *((_DWORD *)this + 2) = 0;
  *((_DWORD *)this + 3) = 0;
  *((_DWORD *)this + 4) = 0;
  v2 = operator new((tagHeader *)0xC);
  if ( v2 )
  {
    v3 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode();
    v2[1] = v3;
    *((_BYTE *)v3 + 49) = 1;
    *(_DWORD *)(v2[1] + 4) = v2[1];
    *(_DWORD *)v2[1] = v2[1];
    *(_DWORD *)(v2[1] + 8) = v2[1];
    v2[2] = 0;
  }
  else
  {
    v2 = 0;
  }
  *((_DWORD *)this + 5) = v2;
  v4 = (CRelocTable *)operator new((tagHeader *)0xC);
  if ( v4 )
    v5 = CRelocTable::CRelocTable(v4);
  else
    v5 = 0;
  *((_DWORD *)this + 6) = v5;
  *((_DWORD *)this + 11) = operator new[](0x20u);
  *((_BYTE *)this + 48) = 0;
  v6 = (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)operator new((tagHeader *)0xC);
  v7 = (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)v6;
  if ( v6 )
  {
    v8 = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(v6);
    v7->_Myhead = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v8;
    v8->_Isnil = 1;
    v7->_Myhead->_Parent = v7->_Myhead;
    v7->_Myhead->_Left = v7->_Myhead;
    v7->_Myhead->_Right = v7->_Myhead;
    v7->_Mysize = 0;
  }
  else
  {
    v7 = 0;
  }
  *((_DWORD *)this + 13) = v7;
  result = this;
  pAllocatedPtrs = v7;
  return result;
}

//----- (004A8010) --------------------------------------------------------
void __thiscall CVirtualMachine::~CVirtualMachine(CVirtualMachine *this)
{
  void *v2; // edi
  void *v3; // edi
  void *v4; // edi

  CVirtualMachine::Destroy(this);
  v2 = (void *)*((_DWORD *)this + 5);
  if ( v2 )
  {
    std::multimap<std::string,std::pair<SFuncType,void *>>::~multimap<std::string,std::pair<SFuncType,void *>>(*((_DWORD *)this + 5));
    operator delete(v2);
  }
  v3 = (void *)*((_DWORD *)this + 6);
  if ( v3 )
  {
    CRelocTable::~CRelocTable(*((CRelocTable **)this + 6));
    operator delete(v3);
  }
  v4 = (void *)*((_DWORD *)this + 13);
  if ( v4 )
  {
    std::set<void *>::~set<void *>(*((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > **)this
                                   + 13));
    operator delete(v4);
  }
  operator delete[](*((void **)this + 11));
}

//----- (004A8080) --------------------------------------------------------
void CompilerMessage2(char *format, ...)
{
  char string[4096]; // [esp+0h] [ebp-1000h] BYREF
  va_list ap; // [esp+1008h] [ebp+8h] BYREF

  va_start(ap, format);
  vsprintf(string, format, ap);
  g_pfuncCompilerMessage(string);
}
// 50C434: using guessed type void (__cdecl *g_pfuncCompilerMessage)(const char *);

//----- (004A80C0) --------------------------------------------------------
void __cdecl SetCompilerMessageFunction(void (__cdecl *a1)(const char *))
{
  g_pfuncCompilerMessage = a1;
}
// 50C434: using guessed type void (__cdecl *g_pfuncCompilerMessage)(const char *);

//----- (004A80D0) --------------------------------------------------------
void __cdecl __noreturn ErrorMessage(int a1, const char *a2)
{
  CompilerMessage2("Error (line %d) : %s", a1, a2);
  exit(0);
}

//----- (004A80F0) --------------------------------------------------------
void __noreturn ErrorMessage2(int a1, char *format, ...)
{
  char string[4096]; // [esp+0h] [ebp-1000h] BYREF
  va_list ap; // [esp+100Ch] [ebp+Ch] BYREF

  va_start(ap, format);
  vsprintf(string, format, ap);
  CompilerMessage2("Error (line %d) : %s", a1, string);
  exit(0);
}

//----- (004A8140) --------------------------------------------------------
void __noreturn ErrorMessage2(char *format, ...)
{
  char string[4096]; // [esp+0h] [ebp-1000h] BYREF
  va_list ap; // [esp+1008h] [ebp+8h] BYREF

  va_start(ap, format);
  vsprintf(string, format, ap);
  CompilerMessage2("Error : %s", string);
  exit(0);
}

//----- (004A8180) --------------------------------------------------------
void WarningMessage2(int a1, char *format, ...)
{
  char string[4096]; // [esp+0h] [ebp-1000h] BYREF
  va_list ap; // [esp+100Ch] [ebp+Ch] BYREF

  va_start(ap, format);
  vsprintf(string, format, ap);
  CompilerMessage2("Warning (line %d) : %s", a1, string);
}

//----- (004A81D0) --------------------------------------------------------
void __cdecl ScriptSystemError(const char *a1)
{
  CompilerMessage2("Script System Error : %s", a1);
}

//----- (004A81F0) --------------------------------------------------------
void __cdecl DefaultMessageFunction(char *_Val)
{
  std::ostream *v1; // esi

  v1 = std::operator<<<std::char_traits<char>>(&std::cout, _Val);
  std::ostream::put(v1, 0xAu);
  std::ostream::flush(v1);
}

//----- (004A8230) --------------------------------------------------------
int __thiscall std::codecvt_base::do_encoding(std::codecvt_base *this)
{
  return 1;
}

//----- (004A8240) --------------------------------------------------------
char __thiscall SNode::SemanticCheck(SNode *this, struct CSymbolTable *a2)
{
  enum eDataType v4; // eax
  int v5; // edx
  enum eDataType v6; // eax
  int v7; // ecx
  enum eDataType TypeOfVar; // eax
  int v9; // eax
  enum eDataType v10; // eax
  int v11; // eax
  int v12; // ecx
  int v13; // ecx
  enum eDataType v14; // eax
  enum eDataType v15; // eax
  int v16; // edx
  int v17; // eax
  bool v18; // zf
  int v19; // eax
  enum eDataType v20; // eax
  int v21; // ecx
  int v22; // ecx

  switch ( *((_DWORD *)this + 1) )
  {
    case 3:
      *((_DWORD *)this + 6) = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      return 1;
    case 6:
      if ( *(_DWORD *)(*((_DWORD *)this + 2) + 24) == 2 )
        goto LABEL_70;
      return 0;
    case 8:
      if ( *(_DWORD *)(*((_DWORD *)this + 2) + 24) == 2 )
        goto LABEL_70;
      return 0;
    case 0x16:
    case 0x19:
    case 0x22:
      goto LABEL_42;
    case 0x17:
      v10 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      if ( v10 == *(_DWORD *)(*((_DWORD *)this + 3) + 24) )
        goto LABEL_43;
      return 0;
    case 0x18:
    case 0x24:
      TypeOfVar = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      *((_DWORD *)this + 6) = TypeOfVar;
      if ( TypeOfVar == T_VOID )
        return 0;
      return TypeOfVar == *(_DWORD *)(*((_DWORD *)this + 2) + 24) || TypeOfVar == T_STRING;
    case 0x1A:
      v6 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      v7 = *((_DWORD *)this + 2);
      *((_DWORD *)this + 6) = v6;
      return v6 == *(_DWORD *)(v7 + 24);
    case 0x1B:
      v4 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      v5 = *((_DWORD *)this + 2);
      *((_DWORD *)this + 6) = v4;
      if ( *(_DWORD *)(v5 + 24) != 2 )
        return 0;
      return v4 == *(_DWORD *)(*((_DWORD *)this + 3) + 24);
    case 0x1C:
      if ( *(_DWORD *)(*((_DWORD *)this + 2) + 24) != 2 )
        return 0;
LABEL_42:
      v10 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
LABEL_43:
      *((_DWORD *)this + 6) = v10;
      return 1;
    case 0x1E:
      v9 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      *((_DWORD *)this + 6) = v9;
      if ( v9 == 4 )
        goto LABEL_23;
      if ( *(_DWORD *)(*((_DWORD *)this + 3) + 24) != 4 )
        return 1;
      if ( v9 )
        goto LABEL_28;
      return 0;
    case 0x25:
    case 0x26:
      v16 = *((_DWORD *)this + 2);
      *((_DWORD *)this + 6) = 1;
      return *(_DWORD *)(v16 + 24) == 1 && *(_DWORD *)(*((_DWORD *)this + 3) + 24) == 1;
    case 0x27:
      if ( *(_DWORD *)(*((_DWORD *)this + 2) + 24) == 1 )
        goto LABEL_55;
      return 0;
    case 0x28:
    case 0x29:
      v17 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      v18 = v17 == *(_DWORD *)(*((_DWORD *)this + 3) + 24);
      dword_53C140 = v17;
      if ( !v18 || !v17 )
        return 0;
      *((_DWORD *)this + 6) = 1;
      return 1;
    case 0x2A:
    case 0x2B:
    case 0x2C:
    case 0x2D:
      v19 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      v18 = v19 == *(_DWORD *)(*((_DWORD *)this + 3) + 24);
      dword_53C140 = v19;
      if ( !v18 || v19 != 2 && v19 != 3 )
        return 0;
LABEL_55:
      *((_DWORD *)this + 6) = 1;
      return 1;
    case 0x2E:
      v11 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      *((_DWORD *)this + 6) = v11;
      if ( v11 == 4 )
      {
LABEL_23:
        v12 = *(_DWORD *)(*((_DWORD *)this + 3) + 24);
        return v12 != 0;
      }
      v13 = *(_DWORD *)(*((_DWORD *)this + 3) + 24);
      if ( v13 != 4 )
        return v11 == v13;
      if ( !v11 )
        return 0;
LABEL_28:
      *((_DWORD *)this + 6) = 4;
      return 1;
    case 0x2F:
    case 0x30:
    case 0x31:
      v14 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      if ( v14 == *(_DWORD *)(*((_DWORD *)this + 3) + 24) )
        goto LABEL_38;
      return 0;
    case 0x32:
      v15 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      if ( v15 != *(_DWORD *)(*((_DWORD *)this + 3) + 24) )
        return 0;
      *((_DWORD *)this + 6) = v15;
      return v15 == T_INT;
    case 0x33:
      v20 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      *((_DWORD *)this + 6) = v20;
      if ( v20 != T_INT && v20 != T_FLOAT && v20 != T_STRING )
        return 0;
      v12 = *(_DWORD *)(*((_DWORD *)this + 2) + 24);
      if ( v20 == v12 )
        return 1;
      if ( v20 == T_STRING )
        return v12 != 0;
      return 0;
    case 0x34:
    case 0x35:
    case 0x36:
      v14 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      v21 = *((_DWORD *)this + 2);
      *((_DWORD *)this + 6) = v14;
      if ( v14 == *(_DWORD *)(v21 + 24) )
        return v14 == T_INT || v14 == T_FLOAT;
      return 0;
    case 0x37:
      v15 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      v22 = *((_DWORD *)this + 2);
      *((_DWORD *)this + 6) = v15;
      if ( v15 == *(_DWORD *)(v22 + 24) )
        return v15 == T_INT;
      return 0;
    case 0x38:
    case 0x39:
    case 0x3C:
    case 0x3D:
      v14 = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
LABEL_38:
      *((_DWORD *)this + 6) = v14;
      return v14 == T_INT || v14 == T_FLOAT;
    case 0x3A:
      if ( *(_DWORD *)(*((_DWORD *)this + 2) + 24) != 2 )
        return 0;
      *((_DWORD *)this + 6) = CSymbolTable::GetTypeOfVar(a2, *((_DWORD *)this + 5));
      return 1;
    case 0x3B:
      CSymbolTable::GetTypeOfFunc(&a2, *((_DWORD *)this + 5));
      *((_DWORD *)this + 6) = (unsigned int)a2 >> 28;
      return 1;
    default:
LABEL_70:
      *((_DWORD *)this + 6) = 0;
      return 1;
  }
}
// 53C140: using guessed type int dword_53C140;

//----- (004A8640) --------------------------------------------------------
void __thiscall CSyntaxTree::Create(CSyntaxTree *this, char *file)
{
  _iobuf *v3; // eax

  v3 = fopen(file, "rt");
  if ( v3 )
  {
    dword_53D1D4 = 1;
    yyin = v3;
    g_pSynTree = this;
    g_pSymbolTable = (CSymbolTable *)*((_DWORD *)this + 1);
    CSymbolTable::Create((CPacketDispatch *)g_pSymbolTable);
    dword_53D1D4 = 1;
    yyparse();
  }
}
// 53D1D4: using guessed type int dword_53D1D4;

//----- (004A8690) --------------------------------------------------------
int *__thiscall CSyntaxTree::Insert(struct CSymbolTable **this, int a2, int a3, int a4, int a5, int a6)
{
  int *v7; // eax
  int *v8; // esi

  v7 = (int *)operator new((tagHeader *)0x1C);
  v8 = 0;
  if ( v7 )
  {
    *v7 = a2;
    v7[1] = a3;
    v7[2] = a4;
    v7[5] = 0;
    v7[6] = 0;
    v7[3] = a5;
    v7[4] = a6;
    v8 = v7;
  }
  if ( !SNode::SemanticCheck((SNode *)v8, this[1]) )
    ErrorMessage(*v8, &byte_4F85B0);
  return v8;
}

//----- (004A8700) --------------------------------------------------------
SNode *__thiscall CSyntaxTree::Insert(struct CSymbolTable **this, int a2, int a3, int a4, int a5, int a6, int a7)
{
  SNode *v8; // eax
  SNode *v9; // esi

  v8 = (SNode *)operator new((tagHeader *)0x1C);
  v9 = 0;
  if ( v8 )
  {
    *(_DWORD *)v8 = a2;
    *((_DWORD *)v8 + 1) = a4;
    *((_DWORD *)v8 + 2) = a5;
    *((_DWORD *)v8 + 5) = 0;
    *((_DWORD *)v8 + 6) = 0;
    *((_DWORD *)v8 + 3) = a6;
    *((_DWORD *)v8 + 4) = a7;
    v9 = v8;
  }
  *((_DWORD *)v9 + 5) = a3;
  if ( !SNode::SemanticCheck(v9, this[1]) )
    CompilerMessage2(aErrorLineD_0, *(_DWORD *)v9);
  return v9;
}

//----- (004A8770) --------------------------------------------------------
void __thiscall CSyntaxTree::Destroy(CSyntaxTree *this)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v2; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  int v4; // edi
  _DWORD *v5; // ebx
  _DWORD *i; // esi
  _DWORD *v7; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator v8; // [esp+10h] [ebp-4h] BYREF

  v2 = *(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)(*((_DWORD *)this + 2)
                                                                                               + 4);
  Left = v2->_Left;
  v8._Ptr = v2->_Left;
  if ( v8._Ptr != v2 )
  {
    do
    {
      operator delete((void *)Left->_Myval);
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(&v8);
      Left = v8._Ptr;
    }
    while ( v8._Ptr != *(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)(*((_DWORD *)this + 2) + 4) );
  }
  v4 = *((_DWORD *)this + 2);
  v5 = *(_DWORD **)(*(_DWORD *)(v4 + 4) + 4);
  for ( i = v5; !*((_BYTE *)i + 17); v5 = i )
  {
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase((void **)i[2]);
    i = (_DWORD *)*i;
    operator delete(v5);
  }
  *(_DWORD *)(*(_DWORD *)(v4 + 4) + 4) = *(_DWORD *)(v4 + 4);
  v7 = *(_DWORD **)(v4 + 4);
  *(_DWORD *)(v4 + 8) = 0;
  *v7 = v7;
  *(_DWORD *)(*(_DWORD *)(v4 + 4) + 8) = *(_DWORD *)(v4 + 4);
  CSymbolTable::Destroy(*((CSymbolTable **)this + 1));
}

//----- (004A8810) --------------------------------------------------------
void __thiscall CSyntaxTree::SetRoot(CSyntaxTree *this, int a2)
{
  if ( a2 )
  {
    if ( *(_DWORD *)this )
      CSyntaxTree::Destroy(this);
    *(_DWORD *)this = a2;
  }
}

//----- (004A8830) --------------------------------------------------------
CSyntaxTree *__thiscall CSyntaxTree::CSyntaxTree(CSyntaxTree *this)
{
  CSymbolTable *v2; // eax
  CSymbolTable *v3; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v4; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v5; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v6; // eax

  *(_DWORD *)this = 0;
  v2 = (CSymbolTable *)operator new((tagHeader *)0x30);
  if ( v2 )
    v3 = CSymbolTable::CSymbolTable(v2);
  else
    v3 = 0;
  *((_DWORD *)this + 1) = v3;
  v4 = (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)operator new((tagHeader *)0xC);
  v5 = v4;
  if ( v4 )
  {
    v6 = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(v4);
    v5->_Myhead = v6;
    v6->_Isnil = 1;
    v5->_Myhead->_Parent = v5->_Myhead;
    v5->_Myhead->_Left = v5->_Myhead;
    v5->_Myhead->_Right = v5->_Myhead;
    v5->_Mysize = 0;
    *((_DWORD *)this + 2) = v5;
  }
  else
  {
    *((_DWORD *)this + 2) = 0;
  }
  return this;
}

//----- (004A88F0) --------------------------------------------------------
void __thiscall CSyntaxTree::~CSyntaxTree(CSymbolTable **this)
{
  CSymbolTable *v2; // edi
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *v3; // esi

  CSyntaxTree::Destroy((CSyntaxTree *)this);
  v2 = this[1];
  if ( v2 )
  {
    CSymbolTable::~CSymbolTable(this[1]);
    operator delete(v2);
  }
  v3 = (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this[2];
  if ( v3 )
  {
    std::set<void *>::~set<void *>(v3);
    operator delete(v3);
  }
}

//----- (004A8930) --------------------------------------------------------
void __thiscall CIntermediateCode::Create(CIntermediateCode *this, CThread *_Val)
{
  GenerateCode(_Val, *(CThread **)this, *((_DWORD *)this + 1));
}

//----- (004A8950) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Erase(
        void **p)
{
  void **v1; // edi
  char result; // al
  void **i; // esi

  v1 = p;
  result = *((_BYTE *)p + 21);
  for ( i = p; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Erase(i[2]);
    i = (void **)*i;
    operator delete(v1);
    result = *((_BYTE *)i + 21);
  }
  return result;
}

//----- (004A8990) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Erase(
        void **p)
{
  void **v1; // edi
  char result; // al
  void **i; // esi

  v1 = p;
  result = *((_BYTE *)p + 21);
  for ( i = p; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Erase(i[2]);
    i = (void **)*i;
    operator delete(v1);
    result = *((_BYTE *)i + 21);
  }
  return result;
}

//----- (004A89D0) --------------------------------------------------------
_DWORD *__stdcall CIntermediateCode::DestroyIMCodes(int a1)
{
  _DWORD *v1; // eax
  _DWORD *v2; // edi
  void (__thiscall ***v3)(_DWORD, int); // ecx
  _DWORD **v4; // ecx
  _DWORD *result; // eax
  bool v6; // zf
  _DWORD *v7; // edi

  v1 = *(_DWORD **)(a1 + 4);
  v2 = (_DWORD *)*v1;
  if ( (_DWORD *)*v1 != v1 )
  {
    do
    {
      v3 = (void (__thiscall ***)(_DWORD, int))v2[2];
      if ( v3 )
        (**v3)(v3, 1);
      v2 = (_DWORD *)*v2;
    }
    while ( v2 != *(_DWORD **)(a1 + 4) );
  }
  v4 = *(_DWORD ***)(a1 + 4);
  result = *v4;
  *v4 = v4;
  *(_DWORD *)(*(_DWORD *)(a1 + 4) + 4) = *(_DWORD *)(a1 + 4);
  v6 = result == *(_DWORD **)(a1 + 4);
  *(_DWORD *)(a1 + 8) = 0;
  if ( !v6 )
  {
    do
    {
      v7 = (_DWORD *)*result;
      operator delete(result);
      result = v7;
    }
    while ( v7 != *(_DWORD **)(a1 + 4) );
  }
  return result;
}

//----- (004A8A30) --------------------------------------------------------
void __thiscall CIntermediateCode::Destroy(CIntermediateCode *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // esi
  unsigned int second; // esi
  _DWORD **v5; // ecx
  _DWORD *v6; // eax
  bool v7; // zf
  _DWORD *v8; // edi
  int v9; // edi
  _DWORD *v10; // ebp
  _DWORD *i; // esi
  _DWORD *v12; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator v13; // [esp+10h] [ebp-4h] BYREF

  v2 = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)(*((_DWORD *)this + 1) + 4);
  Left = v2->_Left;
  v13._Ptr = v2->_Left;
  if ( v13._Ptr != v2 )
  {
    do
    {
      CIntermediateCode::DestroyIMCodes(Left->_Myval.second);
      second = Left->_Myval.second;
      if ( second )
      {
        v5 = *(_DWORD ***)(second + 4);
        v6 = *v5;
        *v5 = v5;
        *(_DWORD *)(*(_DWORD *)(second + 4) + 4) = *(_DWORD *)(second + 4);
        v7 = v6 == *(_DWORD **)(second + 4);
        *(_DWORD *)(second + 8) = 0;
        if ( !v7 )
        {
          do
          {
            v8 = (_DWORD *)*v6;
            operator delete(v6);
            v6 = v8;
          }
          while ( v8 != *(_DWORD **)(second + 4) );
        }
        operator delete(*(void **)(second + 4));
        *(_DWORD *)(second + 4) = 0;
        operator delete((void *)second);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(&v13);
      Left = v13._Ptr;
    }
    while ( v13._Ptr != *(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)(*((_DWORD *)this + 1) + 4) );
  }
  v9 = *((_DWORD *)this + 1);
  v10 = *(_DWORD **)(*(_DWORD *)(v9 + 4) + 4);
  for ( i = v10; !*((_BYTE *)i + 21); v10 = i )
  {
    std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Erase((void **)i[2]);
    i = (_DWORD *)*i;
    operator delete(v10);
  }
  *(_DWORD *)(*(_DWORD *)(v9 + 4) + 4) = *(_DWORD *)(v9 + 4);
  v12 = *(_DWORD **)(v9 + 4);
  *(_DWORD *)(v9 + 8) = 0;
  *v12 = v12;
  *(_DWORD *)(*(_DWORD *)(v9 + 4) + 8) = *(_DWORD *)(v9 + 4);
  CIntermediateCode::DestroyIMCodes(*(_DWORD *)this);
}

//----- (004A8B10) --------------------------------------------------------
int __thiscall CIntermediateCode::Addressing(CIntermediateCode *this, int a2)
{
  int **v3; // eax
  int *v4; // esi
  bool v5; // zf
  int result; // eax
  _DWORD *v7; // ecx
  _DWORD *v8; // edi
  int v9; // ebx
  _DWORD *v10; // ecx
  _DWORD *v11; // esi
  int **v12; // ecx
  int *j; // ecx
  int i; // ecx

  v3 = *(int ***)(*(_DWORD *)this + 4);
  v4 = *v3;
  v5 = *v3 == (int *)v3;
  result = a2;
  if ( !v5 )
  {
    do
    {
      result = (*(int (__thiscall **)(int, int))(*(_DWORD *)v4[2] + 8))(v4[2], result);
      v4 = (int *)*v4;
    }
    while ( v4 != *(int **)(*(_DWORD *)this + 4) );
  }
  v7 = *(_DWORD **)(*((_DWORD *)this + 1) + 4);
  v8 = (_DWORD *)*v7;
  if ( (_DWORD *)*v7 != v7 )
  {
    do
    {
      v9 = v8[4];
      v10 = *(_DWORD **)(v9 + 4);
      v11 = (_DWORD *)*v10;
      if ( (_DWORD *)*v10 != v10 )
      {
        do
        {
          result = (*(int (__thiscall **)(_DWORD, int))(*(_DWORD *)v11[2] + 8))(v11[2], result);
          v11 = (_DWORD *)*v11;
        }
        while ( v11 != *(_DWORD **)(v9 + 4) );
      }
      if ( !*((_BYTE *)v8 + 21) )
      {
        v12 = (int **)v8[2];
        if ( *((_BYTE *)v12 + 21) )
        {
          for ( i = v8[1]; !*(_BYTE *)(i + 21); i = *(_DWORD *)(i + 4) )
          {
            if ( v8 != *(_DWORD **)(i + 8) )
              break;
            v8 = (_DWORD *)i;
          }
          v8 = (_DWORD *)i;
        }
        else
        {
          v8 = (_DWORD *)v8[2];
          for ( j = *v12; !*((_BYTE *)j + 21); j = (int *)*j )
            v8 = j;
        }
      }
    }
    while ( v8 != *(_DWORD **)(*((_DWORD *)this + 1) + 4) );
  }
  return result;
}

//----- (004A8BC0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *__thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *a2,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *p; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( a3._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = a3._Ptr;
  p = a3._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(&a3);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == p )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == p )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = a3._Ptr;
  Right = a3._Ptr->_Right;
  if ( a3._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = a3._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( p->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(p);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  result = a2;
  a2->_Ptr = a3._Ptr;
  return result;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004A8E80) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2,
        char a3,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Parg,
        std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **result; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  std::pair<unsigned long const ,unsigned long> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Parg,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (std::pair<unsigned long const ,unsigned long> *)v6;
  ++this->_Mysize;
  if ( _Parg == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( a3 )
  {
    _Parg->_Left = v6;
    v8 = this->_Myhead;
    if ( _Parg == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Parg->_Right = v6;
    v9 = this->_Myhead;
    if ( _Parg == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Vala;
        break;
      }
    }
  }
  result = a2;
  this->_Myhead->_Parent->_Color = 1;
  *a2 = v6;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004A9030) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *a4)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v4; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **result; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // eax

  v4 = a4;
  Ptr = a3._Ptr;
  Myhead = this->_Myhead;
  if ( a3._Ptr == Myhead->_Left && a4 == Myhead )
  {
    std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Erase((void **)&Myhead->_Parent->_Left);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    result = a2;
    *a2 = this->_Myhead->_Left;
  }
  else
  {
    if ( a3._Ptr != a4 )
    {
      do
      {
        v10._Ptr = Ptr;
        if ( !Ptr->_Isnil )
        {
          Right = Ptr->_Right;
          if ( Right->_Isnil )
          {
            for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( Ptr != i->_Right )
                break;
              Ptr = i;
            }
            Ptr = i;
          }
          else
          {
            Ptr = Ptr->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              Ptr = j;
          }
        }
        std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(
          this,
          &a3,
          v10);
      }
      while ( Ptr != v4 );
    }
    result = a2;
    *a2 = Ptr;
  }
  return result;
}

//----- (004A90F0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *a4)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v4; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **result; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // eax

  v4 = a4;
  Ptr = a3._Ptr;
  Myhead = this->_Myhead;
  if ( a3._Ptr == Myhead->_Left && a4 == Myhead )
  {
    std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Erase((void **)&Myhead->_Parent->_Left);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    result = a2;
    *a2 = this->_Myhead->_Left;
  }
  else
  {
    if ( a3._Ptr != a4 )
    {
      do
      {
        v10._Ptr = Ptr;
        if ( !Ptr->_Isnil )
        {
          Right = Ptr->_Right;
          if ( Right->_Isnil )
          {
            for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( Ptr != i->_Right )
                break;
              Ptr = i;
            }
            Ptr = i;
          }
          else
          {
            Ptr = Ptr->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              Ptr = j;
          }
        }
        std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(
          this,
          &a3,
          v10);
      }
      while ( Ptr != v4 );
    }
    result = a2;
    *a2 = Ptr;
  }
  return result;
}

//----- (004A91B0) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        int a2,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator a3)
{
  std::pair<unsigned long const ,unsigned long> *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // edx
  int result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  char v13; // [esp+Ch] [ebp-4h]

  Ptr = (std::pair<unsigned long const ,unsigned long> *)a3._Ptr;
  Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  v13 = 1;
  if ( !Parent->_Isnil )
  {
    Left = a3._Ptr->_Left;
    do
    {
      v7 = (int)Left < (signed int)Parent->_Myval.first;
      Myhead = Parent;
      v13 = v7;
      if ( (int)Left >= (signed int)Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  a3._Ptr = Myhead;
  if ( v7 )
  {
    if ( Myhead == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->_Myhead->_Left )
    {
      v10 = *std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Insert(
               this,
               (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&a3,
               1,
               (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Myhead,
               Ptr);
      result = a2;
      *(_BYTE *)(a2 + 4) = 1;
      *(_DWORD *)a2 = v10;
      return result;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec(&a3);
    v9 = a3._Ptr;
  }
  if ( (signed int)v9->_Myval.first >= (signed int)Ptr->first )
  {
    result = a2;
    *(_BYTE *)(a2 + 4) = 0;
    *(_DWORD *)a2 = v9;
  }
  else
  {
    v12 = *std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Insert(
             this,
             (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&a3,
             v13,
             (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Myhead,
             Ptr);
    result = a2;
    *(_DWORD *)a2 = v12;
    *(_BYTE *)(a2 + 4) = 1;
  }
  return result;
}

