
//----- (0047A670) --------------------------------------------------------
char __thiscall CCharacterParty::AutoRouting(
        CCharacterParty *this,
        CCharacter *lpPickkingCreature,
        unsigned __int64 nObjectID,
        Item::CItem *lpItem,
        unsigned int *dwGold,
        unsigned int *dwOwnerID)
{
  bool v6; // al
  CCell *m_lpCell; // edx
  int v8; // eax
  int v9; // esi
  unsigned int v10; // ecx
  unsigned int v11; // ebx
  int v12; // edi
  CCharacter *v13; // ecx
  CCharacter *v15; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned __int8 cHighestLevel; // [esp+Fh] [ebp-29h] BYREF
  CCharacter *aryNearCharacterList[10]; // [esp+10h] [ebp-28h] BYREF
  unsigned __int8 dwGolda; // [esp+4Ch] [ebp+14h]

  v6 = *dwGold == 0;
  m_lpCell = lpPickkingCreature->m_CellPos.m_lpCell;
  cHighestLevel = 0;
  v8 = CCharacterParty::GetNearMemberList(this, m_lpCell, v6, aryNearCharacterList, &cHighestLevel);
  v9 = v8;
  if ( !v8 )
    return 0;
  v10 = *dwGold;
  if ( *dwGold )
  {
    v11 = v10 / v8;
    *dwGold = v10 + v10 / v8 * (1 - v8);
    v12 = 0;
    if ( v8 > 0 )
    {
      do
      {
        v13 = aryNearCharacterList[v12];
        if ( v13 != lpPickkingCreature )
          CCharacter::AddGold(v13, v11, 1);
        ++v12;
      }
      while ( v12 < v9 );
    }
    return 0;
  }
  if ( !lpItem )
    return 0;
  v15 = aryNearCharacterList[(unsigned __int8)Math::Random::ComplexRandom(v8, 0)];
  if ( lpPickkingCreature == v15 )
    return 0;
  m_lpGameClientDispatch = (CSendStream *)v15->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    if ( (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
      dwGolda = lpItem->m_ItemData.m_cNumOrDurability;
    else
      dwGolda = 1;
    *dwOwnerID = v15->m_dwCID;
    GameClientSendPacket::SendCharAutoRouting(
      m_lpGameClientDispatch + 8,
      v15->m_dwCID,
      nObjectID,
      lpItem->m_ItemData.m_usProtoTypeID,
      dwGolda,
      0);
  }
  return 1;
}

//----- (0047A780) --------------------------------------------------------
int __thiscall CCharacterParty::Attack(
        CCharacterParty *this,
        AtType attackType,
        CAggresiveCreature **pDefenders,
        unsigned __int8 *cDefenserJudges,
        CAggresiveCreature *lpOffencer,
        float fDistance,
        unsigned __int8 cTargetType)
{
  unsigned __int8 v8; // bl
  CCharacter **i; // edi
  CAggresiveCreature *v10; // esi
  double v11; // st6
  double v12; // st5
  double v13; // st3
  int v14; // edx
  char cDefenderNum; // [esp+8h] [ebp-Ch]
  int nIndex; // [esp+Ch] [ebp-8h]

  v8 = 0;
  cDefenderNum = 0;
  nIndex = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    for ( i = this->m_pMemberPointer; ; ++i )
    {
      v10 = *i;
      if ( *i )
      {
        v13 = (*pDefenders)->m_CurrentPos.m_fPointZ - v10->m_CurrentPos.m_fPointZ;
        v12 = (*pDefenders)->m_CurrentPos.m_fPointX - v10->m_CurrentPos.m_fPointX;
        v11 = (*pDefenders)->m_CurrentPos.m_fPointY - v10->m_CurrentPos.m_fPointY;
        if ( (double)(unsigned int)(unsigned __int64)sqrt(v12 * v12 + v11 * v11 + v13 * v13) < fDistance
          && (v10->m_dwStatusFlag & 0x40000000) == 0 )
        {
          if ( cTargetType != 2
            || lpOffencer->IsEnemy(lpOffencer, v10) == NEUTRALITY
            && (v10 = *i, lpOffencer->m_CellPos.m_wMapIndex == (*i)->m_CellPos.m_wMapIndex) )
          {
            v14 = v8++;
            pDefenders[v14] = v10;
            if ( v8 > 0xAu )
              break;
          }
        }
      }
      if ( ++nIndex >= this->m_Party.m_cMemberNum )
      {
        cDefenderNum = v8;
        return ((int (__thiscall *)(_DWORD, _DWORD, _BYTE, _DWORD, _DWORD))lpOffencer->Attack)(
                 lpOffencer,
                 attackType,
                 cDefenderNum,
                 pDefenders,
                 cDefenserJudges);
      }
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::Attack",
      FileName,
      1113,
      aPid0x08x_7,
      this->m_Party.m_dwPartyID,
      v8);
    cDefenderNum = 10;
  }
  return ((int (__thiscall *)(_DWORD, _DWORD, _BYTE, _DWORD, _DWORD))lpOffencer->Attack)(
           lpOffencer,
           attackType,
           cDefenderNum,
           pDefenders,
           cDefenserJudges);
}

//----- (0047A8D0) --------------------------------------------------------
char __thiscall CCharacterParty::StartTeamBattle(CCharacterParty *this, CCharacterParty *pHostileParty)
{
  CCharacterParty *v4; // ecx
  CCharacter *v5; // edx
  int v6; // eax
  CCharacter *v7; // edx
  CCharacter *v8; // edx
  CCharacter *v9; // edx
  CCharacter *v10; // edx
  CCharacter *v11; // edx
  CCharacter *v12; // edx
  CCharacter *v13; // edx
  CCharacter *v14; // edx
  CCharacter *v15; // edx
  char Buffer[84]; // [esp+4h] [ebp-58h] BYREF

  if ( this->m_pHostileParty )
    return 0;
  v4 = pHostileParty;
  this->m_pHostileParty = pHostileParty;
  v5 = this->m_pMemberPointer[0];
  v6 = 0;
  if ( v5 )
  {
    this->m_pFightingMember[0] = v5;
    v6 = 1;
  }
  v7 = this->m_pMemberPointer[1];
  if ( v7 )
    this->m_pFightingMember[v6++] = v7;
  v8 = this->m_pMemberPointer[2];
  if ( v8 )
    this->m_pFightingMember[v6++] = v8;
  v9 = this->m_pMemberPointer[3];
  if ( v9 )
    this->m_pFightingMember[v6++] = v9;
  v10 = this->m_pMemberPointer[4];
  if ( v10 )
    this->m_pFightingMember[v6++] = v10;
  v11 = this->m_pMemberPointer[5];
  if ( v11 )
    this->m_pFightingMember[v6++] = v11;
  v12 = this->m_pMemberPointer[6];
  if ( v12 )
    this->m_pFightingMember[v6++] = v12;
  v13 = this->m_pMemberPointer[7];
  if ( v13 )
    this->m_pFightingMember[v6++] = v13;
  v14 = this->m_pMemberPointer[8];
  if ( v14 )
    this->m_pFightingMember[v6++] = v14;
  v15 = this->m_pMemberPointer[9];
  if ( v15 )
    this->m_pFightingMember[v6] = v15;
  pHostileParty = 0;
  if ( CCharacterParty::MakeTeamBattleInfo(v4, Buffer, (unsigned __int16 *)&pHostileParty, 0, 6) == 1 )
    CCharacterParty::SendAllLoggedMember(this, Buffer, (unsigned __int16)pHostileParty, 0, 0x63u);
  return 1;
}

//----- (0047AA10) --------------------------------------------------------
int __thiscall CCharacterParty::DropMember(CCharacterParty *this, CCharacter *pDropMember, PktDuC::DuelCmd eCmd)
{
  int result; // eax
  int v5; // esi
  CCharacter **i; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CCharacter **v8; // ecx
  bool v9; // zf

  if ( !pDropMember )
    return -1;
  v5 = 0;
  for ( i = this->m_pFightingMember; pDropMember != *i; ++i )
  {
    if ( ++v5 >= 10 )
      return -1;
  }
  m_lpGameClientDispatch = pDropMember->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    GameClientSendPacket::SendCharDuelCmd(
      &m_lpGameClientDispatch->m_SendStream,
      pDropMember->m_dwCID,
      this->m_pHostileParty->m_Party.m_dwLeaderID,
      eCmd,
      0);
  CCharacterParty::SendDropMember(this->m_pHostileParty, pDropMember, eCmd);
  result = v5;
  if ( v5 >= 9 )
  {
LABEL_14:
    this->m_pFightingMember[9] = 0;
    return 9;
  }
  else
  {
    v8 = &this->m_pFightingMember[v5 + 1];
    while ( 1 )
    {
      v9 = *v8 == 0;
      *(v8 - 1) = *v8;
      if ( v9 )
        break;
      ++result;
      ++v8;
      if ( result >= 9 )
        goto LABEL_14;
    }
  }
  return result;
}

//----- (0047AAC0) --------------------------------------------------------
void __thiscall CCharacterParty::EndTeamBattle(CCharacterParty *this)
{
  int v2; // ebx
  CCharacter **m_pFightingMember; // esi
  CCharacter *v4; // eax
  CSendStream *m_lpGameClientDispatch; // ecx

  if ( !this->m_pHostileParty )
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacterParty::EndTeamBattle", FileName, 1191, (char *)&byte_4EF6C8);
  v2 = 0;
  m_pFightingMember = this->m_pFightingMember;
  do
  {
    v4 = *m_pFightingMember;
    if ( !*m_pFightingMember )
      break;
    m_lpGameClientDispatch = (CSendStream *)v4->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharDuelCmd(m_lpGameClientDispatch + 8, v4->m_dwCID, v4->m_dwCID, 8u, 0);
    ++v2;
    ++m_pFightingMember;
  }
  while ( v2 < 10 );
  this->m_pHostileParty = 0;
  this->m_pFightingMember[0] = 0;
  this->m_pFightingMember[1] = 0;
  this->m_pFightingMember[2] = 0;
  this->m_pFightingMember[3] = 0;
  this->m_pFightingMember[4] = 0;
  this->m_pFightingMember[5] = 0;
  this->m_pFightingMember[6] = 0;
  this->m_pFightingMember[7] = 0;
  this->m_pFightingMember[8] = 0;
  this->m_pFightingMember[9] = 0;
}

//----- (0047AB60) --------------------------------------------------------
void __thiscall CCharacterParty::SendPartyCommand(
        CCharacterParty *this,
        PktPC::PartyCmd Command,
        const char *SenderName_In,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        const AddressInfo *Address)
{
  unsigned int m_dwPartyID; // edx
  PktPCAck pktPCAck; // [esp+10h] [ebp-54h] BYREF

  m_dwPartyID = this->m_Party.m_dwPartyID;
  pktPCAck.m_cCmd = Command;
  pktPCAck.m_dwPartyID = m_dwPartyID;
  strncpy(pktPCAck.m_strSenderName, SenderName_In, 0x10u);
  pktPCAck.m_dwSenderID = dwSenderCID;
  pktPCAck.m_dwReferenceID = dwReferenceID;
  qmemcpy(&pktPCAck.m_SenderAddressInfo, Address, sizeof(pktPCAck.m_SenderAddressInfo));
  if ( PacketWrap::WrapCrypt((char *)&pktPCAck, 0x4Du, 0x1Fu, 0, 0) )
    CCharacterParty::SendAllLoggedMember(this, (char *)&pktPCAck, 0x4Du, Command != PC_LOGIN ? 0 : dwSenderCID, 0x1Fu);
}

//----- (0047AC30) --------------------------------------------------------
void __thiscall CCharacterParty::SendPartyInfo(CCharacterParty *this, CCharacter *lpCharacter)
{
  CGameClientDispatch *m_lpGameClientDispatch; // edi
  CSendStream *p_m_SendStream; // ebx
  int v5; // edi
  float *p_fPointY; // ebx
  CCharacter *Character; // eax
  float *v8; // esi
  char m_nLevel; // al
  float v10; // edx
  unsigned __int16 v11; // ax
  int v12; // ecx
  int v13; // edx
  int m_cMemberNum; // eax
  char *v15; // edi
  _BYTE v16[250]; // [esp-FCh] [ebp-2BCh] BYREF
  unsigned int *MemberCID; // [esp+10h] [ebp-1B0h]
  CSendStream *SendStream; // [esp+14h] [ebp-1ACh]
  char *lpBuffer; // [esp+18h] [ebp-1A8h]
  CCreatureManager *creatureManager; // [esp+1Ch] [ebp-1A4h]
  PARTY_EX PartyEx; // [esp+20h] [ebp-1A0h] BYREF

  if ( lpCharacter )
  {
    CPartyMgr::DeleteFindPartyList(CSingleton<CPartyMgr>::ms_pSingleton, lpCharacter->m_dwCID);
    lpCharacter->m_pParty = this;
    CCharacter::SetPID(lpCharacter, this->m_Party.m_dwPartyID);
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
      SendStream = &m_lpGameClientDispatch->m_SendStream;
      lpBuffer = CSendStream::GetBuffer(&m_lpGameClientDispatch->m_SendStream, (char *)0x1A5);
      if ( lpBuffer )
      {
        qmemcpy(v16, &this->m_Party, 0xF8u);
        creatureManager = CCreatureManager::GetInstance();
        v16[248] = HIBYTE(this->m_Party.ServerID[9]);
        PARTY_EX::PARTY_EX(&PartyEx, *(PARTY *)v16);
        v5 = 0;
        if ( this->m_Party.m_cMemberNum )
        {
          p_fPointY = &PartyEx.m_Position[0].fPointY;
          MemberCID = this->m_Party.MemberCID;
          do
          {
            Character = CCreatureManager::GetCharacter(creatureManager, *MemberCID);
            v8 = (float *)Character;
            if ( Character )
            {
              m_nLevel = Character->m_CreatureStatus.m_nLevel;
              PartyEx.m_bAutoRouting[v5] = this->m_bAutoRouting[v5];
              v10 = *v8;
              PartyEx.m_cLevel[v5] = m_nLevel;
              v11 = (*(int (__thiscall **)(float *))(LODWORD(v10) + 92))(v8);
              v12 = *((_DWORD *)v8 + 2);
              v13 = *((_DWORD *)v8 + 3);
              PartyEx.m_wClass[v5] = v11;
              *(p_fPointY - 1) = v8[1];
              *(_DWORD *)p_fPointY = v12;
              *((_DWORD *)p_fPointY + 1) = v13;
            }
            m_cMemberNum = this->m_Party.m_cMemberNum;
            ++v5;
            p_fPointY += 3;
            ++MemberCID;
          }
          while ( v5 < m_cMemberNum );
          p_m_SendStream = SendStream;
        }
        v15 = lpBuffer + 12;
        qmemcpy(lpBuffer + 12, &PartyEx, 0x198u);
        *(_WORD *)&v16[248] = 0;
        *(_WORD *)&v16[244] = 0;
        v16[240] = 59;
        *(_WORD *)&v16[236] = 421;
        v15[408] = HIBYTE(PartyEx.m_Position[9].fPointZ);
        CSendStream::WrapCrypt(
          p_m_SendStream,
          *(unsigned __int16 *)&v16[236],
          v16[240],
          *(unsigned __int16 *)&v16[244],
          *(unsigned __int16 *)&v16[248]);
      }
    }
  }
}

//----- (0047ADA0) --------------------------------------------------------
void __thiscall CCharacterParty::SendDivisionExp(
        CCharacterParty *this,
        CCharacter *lpCharacter,
        CAggresiveCreature *lpDeadCreature,
        unsigned int dwExp,
        int nStandardLevel)
{
  int v6; // eax
  int v7; // ebx
  unsigned int v8; // ebp
  CCharacter *v9; // edi
  int v10; // esi
  double Aggravation; // st7
  signed int v12; // esi
  CCharacter_vtbl *v13; // edx
  CCell *m_lpCell; // [esp-10h] [ebp-48h]
  unsigned __int8 cHighestLevel; // [esp+7h] [ebp-31h] BYREF
  int nNearPartyMemberNum; // [esp+8h] [ebp-30h]
  int nNearCharacterLevel; // [esp+Ch] [ebp-2Ch]
  CCharacter *aryNearCharacterList[10]; // [esp+10h] [ebp-28h] BYREF

  m_lpCell = lpDeadCreature->m_CellPos.m_lpCell;
  cHighestLevel = 0;
  v6 = CCharacterParty::GetNearMemberList(this, m_lpCell, 0, aryNearCharacterList, &cHighestLevel);
  nNearPartyMemberNum = v6;
  if ( v6 )
  {
    v7 = 0;
    if ( v6 > 0 )
    {
      v8 = 14 * v6;
      do
      {
        v9 = aryNearCharacterList[v7];
        v10 = v9->m_CreatureStatus.m_nLevel - cHighestLevel + 14;
        Aggravation = CThreat::GetAggravation(&lpDeadCreature->m_Threat, v9);
        nNearCharacterLevel = dwExp * (v10 < 0 ? 0 : v10) / v8;
        v12 = (unsigned __int64)(Aggravation * (double)(unsigned int)nNearCharacterLevel);
        if ( CServerSetup::GetInstance()->m_eNationType == THAILAND && nNearPartyMemberNum >= 2 )
        {
          nNearCharacterLevel = 40 * v12;
          v12 += (unsigned __int64)((double)(unsigned int)(40 * v12) * 0.0099999998);
        }
        v13 = v9->__vftable;
        nNearCharacterLevel = v9->m_CreatureStatus.m_nLevel;
        v13->IsPeaceMode(v9);
        if ( (int)abs32(nStandardLevel - nNearCharacterLevel) > 14 || !v12 )
          v12 = 1;
        CCharacter::GetHuntingExp(v9, (unsigned int)lpDeadCreature, v12, nNearPartyMemberNum);
        ++v7;
      }
      while ( v7 < nNearPartyMemberNum );
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::SendDivisionExp",
      FileName,
      1417,
      (char *)&byte_4EF710,
      this->m_Party.m_dwPartyID);
  }
}

//----- (0047AF00) --------------------------------------------------------
void __thiscall CCharacterParty::SendAutoRouting(
        CCharacterParty *this,
        unsigned int dwCharID,
        unsigned __int16 wItemID,
        unsigned __int8 cCmd)
{
  PktAutoRouting pktAR; // [esp+8h] [ebp-20h] BYREF

  pktAR.m_itemPos = 0;
  pktAR.m_nObjectID = 0LL;
  pktAR.m_cNum = 0;
  pktAR.m_dwCharID = dwCharID;
  pktAR.m_wItemID = wItemID;
  pktAR.m_cCmd = cCmd;
  if ( PacketWrap::WrapCrypt((char *)&pktAR, 0x1Eu, 0x72u, 0, 0) )
    CCharacterParty::SendAllLoggedMember(this, (char *)&pktAR, 0x1Eu, dwCharID, 0x72u);
}

//----- (0047AF70) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (0047AFD0) --------------------------------------------------------
CParty *__thiscall CParty::`vector deleting destructor'(CParty *this, char a2)
{
  CParty::~CParty(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0047AFF0) --------------------------------------------------------
void __thiscall CCharacterParty::CCharacterParty(CCharacterParty *this, const PARTY *PartyInfo, bool bCreate)
{
  CPartySpellMgr *p_m_PartySpellMgr; // ebx
  CServerSetup *Instance; // eax
  unsigned int *MemberCID; // edi
  CCreatureManager *v7; // eax
  CCharacter *Character; // eax
  CCharacter *v9; // esi
  CSingleDispatch *DispatchTable; // eax
  unsigned int v11; // [esp-4h] [ebp-30h]
  int nMemberIndex; // [esp+10h] [ebp-1Ch]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+18h] [ebp-14h] BYREF
  int v14; // [esp+28h] [ebp-4h]
  const PARTY *PartyInfoa; // [esp+30h] [ebp+4h]

  p_m_PartySpellMgr = &this->m_PartySpellMgr;
  this->__vftable = (CCharacterParty_vtbl *)&CParty::`vftable';
  CPartySpellMgr::CPartySpellMgr(&this->m_PartySpellMgr);
  qmemcpy(&this->m_Party, PartyInfo, sizeof(this->m_Party));
  p_m_PartySpellMgr->m_lpOwnerParty = this;
  this->__vftable = (CCharacterParty_vtbl *)&CCharacterParty::`vftable';
  this->m_pHostileParty = 0;
  this->m_pMemberPointer[0] = 0;
  this->m_pMemberPointer[1] = 0;
  this->m_pMemberPointer[2] = 0;
  this->m_pMemberPointer[3] = 0;
  this->m_pMemberPointer[4] = 0;
  this->m_pMemberPointer[5] = 0;
  this->m_pMemberPointer[6] = 0;
  this->m_pMemberPointer[7] = 0;
  this->m_pMemberPointer[8] = 0;
  this->m_pMemberPointer[9] = 0;
  *(_DWORD *)this->m_bAutoRouting = ********;
  *(_DWORD *)&this->m_bAutoRouting[4] = ********;
  *(_WORD *)&this->m_bAutoRouting[8] = 257;
  this->m_pFightingMember[0] = 0;
  this->m_pFightingMember[1] = 0;
  this->m_pFightingMember[2] = 0;
  this->m_pFightingMember[3] = 0;
  this->m_pFightingMember[4] = 0;
  this->m_pFightingMember[5] = 0;
  this->m_pFightingMember[6] = 0;
  this->m_pFightingMember[7] = 0;
  this->m_pFightingMember[8] = 0;
  this->m_pFightingMember[9] = 0;
  v14 = 0;
  CPartyMgr::AddParty(CSingleton<CPartyMgr>::ms_pSingleton, this);
  Instance = CServerSetup::GetInstance();
  PartyInfoa = (const PARTY *)CServerSetup::GetServerID(Instance);
  nMemberIndex = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    do
    {
      if ( (const PARTY *)MemberCID[10] == PartyInfoa )
      {
        v11 = *MemberCID;
        v7 = CCreatureManager::GetInstance();
        Character = CCreatureManager::GetCharacter(v7, v11);
        v9 = Character;
        if ( Character )
        {
          LogChantBug(Character, this, &byte_4EF744, "CCharacterParty::CCharacterParty", FileName, 406);
          *(unsigned int *)((char *)MemberCID + 83) = (unsigned int)v9;
          CPartySpellMgr::AddMember(p_m_PartySpellMgr, v9);
          CCharacterParty::SendPartyInfo(this, v9);
          DispatchTable = CChatDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
          LOBYTE(v14) = 1;
          if ( StoragelpChatDispatch.m_lpDispatch )
            CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], v9);
          if ( bCreate )
            CCharacterParty::SendPartyCmdInfo(this, PC_ACCEPT, v9);
          LOBYTE(v14) = 0;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacterParty::CCharacterParty",
            FileName,
            402,
            (char *)&byte_4EF578,
            this->m_Party.m_dwPartyID,
            *MemberCID,
            MemberCID[10],
            PartyInfoa);
        }
      }
      ++MemberCID;
      ++nMemberIndex;
    }
    while ( nMemberIndex < this->m_Party.m_cMemberNum );
  }
}
// 4EF170: using guessed type void *CParty::`vftable';
// 4EF654: using guessed type void *CCharacterParty::`vftable';

//----- (0047B210) --------------------------------------------------------
CCharacterParty *__thiscall CCharacterParty::`scalar deleting destructor'(CCharacterParty *this, char a2)
{
  CCharacterParty::~CCharacterParty(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0047B230) --------------------------------------------------------
char __thiscall CCharacterParty::Join(
        CCharacterParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        const char *strSenderName,
        unsigned __int16 wMapIndex)
{
  unsigned int v7; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v10; // ebx
  CServerSetup *v11; // eax
  CServerSetup *v12; // eax
  const void *v13; // eax
  int v14; // eax
  unsigned int *MemberCID; // ecx
  char *v16; // ecx
  CSingleDispatch *DispatchTable; // eax
  unsigned int ServerID; // [esp-14h] [ebp-78h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-60h] BYREF
  AddressInfo v20; // [esp+Ch] [ebp-58h] BYREF
  AddressInfo addressInfo; // [esp+30h] [ebp-34h] BYREF
  int v22; // [esp+60h] [ebp-4h]

  if ( this->m_Party.m_cMemberNum >= 0xAu )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacterParty::Join", FileName, 454, (char *)&byte_4EF2F0);
    return 0;
  }
  memset(&addressInfo, 0, sizeof(addressInfo));
  v7 = dwSenderCID;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, dwSenderCID);
  v10 = Character;
  if ( Character )
  {
    if ( strcmp(strSenderName, Character->m_DBData.m_Info.Name) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacterParty::Join",
        FileName,
        465,
        aPid0x08x,
        this->m_Party.m_dwPartyID,
        dwSenderCID,
        v10->m_DBData.m_Info.Name,
        strSenderName);
      return 0;
    }
    v11 = CServerSetup::GetInstance();
    if ( dwReferenceID != CServerSetup::GetServerID(v11) )
    {
      v12 = CServerSetup::GetInstance();
      ServerID = CServerSetup::GetServerID(v12);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacterParty::Join",
        FileName,
        472,
        aPid0x08x_2,
        this->m_Party.m_dwPartyID,
        dwSenderCID,
        ServerID,
        dwReferenceID);
      return 0;
    }
    AddressInfo::AddressInfo(&v20, &v10->m_PublicAddress, &v10->m_PrivateAddress, v10->m_dwCID);
    qmemcpy(&addressInfo, v13, sizeof(addressInfo));
    v7 = dwSenderCID;
  }
  v14 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != v7 )
    {
      ++v14;
      ++MemberCID;
      if ( v14 >= this->m_Party.m_cMemberNum )
        goto LABEL_14;
    }
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacterParty::Join", FileName, 483, (char *)&byte_4EF250);
    return 0;
  }
  else
  {
LABEL_14:
    CCharacterParty::SendPartyCommand(this, PC_ACCEPT, strSenderName, v7, dwReferenceID, &addressInfo);
    this->m_Party.MemberCID[this->m_Party.m_cMemberNum] = v7;
    v16 = this->m_Party.Name[this->m_Party.m_cMemberNum];
    *(_DWORD *)v16 = *(_DWORD *)strSenderName;
    *((_DWORD *)v16 + 1) = *((_DWORD *)strSenderName + 1);
    *((_DWORD *)v16 + 2) = *((_DWORD *)strSenderName + 2);
    *((_DWORD *)v16 + 3) = *((_DWORD *)strSenderName + 3);
    this->m_Party.ServerID[this->m_Party.m_cMemberNum] = dwReferenceID;
    this->m_pMemberPointer[this->m_Party.m_cMemberNum++] = v10;
    if ( v10 )
    {
      LogChantBug(v10, this, &byte_4EF744, "CCharacterParty::Join", FileName, 499);
      CPartySpellMgr::AddMember(&this->m_PartySpellMgr, v10);
      CCharacterParty::SendPartyInfo(this, v10);
      CCharacterParty::SendPartyCmdInfo(this, PC_ACCEPT, v10);
      DispatchTable = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
      v22 = 0;
      if ( StoragelpChatDispatch.m_lpDispatch )
        CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], v10);
      v22 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
    }
    return 1;
  }
}
// 47B3A3: variable 'v13' is possibly undefined

//----- (0047B500) --------------------------------------------------------
int __thiscall CCharacterParty::Leave(
        CCharacterParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        unsigned __int16 wMapIndex)
{
  int v5; // ebp
  unsigned int *MemberCID; // eax
  CServerSetup *Instance; // eax
  CCreatureManager *v9; // eax
  CCharacter *Character; // edi
  unsigned int Party; // eax
  CSingleDispatch *DispatchTable; // eax
  unsigned __int8 v13; // cl
  char *v14; // edx
  bool *v15; // ecx
  unsigned int *v16; // eax
  int v17; // ebx
  int v18; // eax
  char *v19; // ecx
  unsigned int *i; // ecx
  unsigned int dwServerID; // [esp+Ch] [ebp-40h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+10h] [ebp-3Ch] BYREF
  AddressInfo Address; // [esp+18h] [ebp-34h] BYREF
  int v24; // [esp+48h] [ebp-4h]

  memset(&Address, 0, sizeof(Address));
  v5 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != dwSenderCID )
    {
      ++v5;
      ++MemberCID;
      if ( v5 >= this->m_Party.m_cMemberNum )
        goto LABEL_5;
    }
    Instance = CServerSetup::GetInstance();
    dwServerID = CServerSetup::GetServerID(Instance);
    CCharacterParty::SendPartyCommand(
      this,
      (PktPC::PartyCmd)((dwReferenceID != 0) + 3),
      this->m_Party.Name[v5],
      dwSenderCID,
      dwReferenceID,
      &Address);
    v9 = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(v9, dwSenderCID);
    if ( Character )
    {
      if ( this->m_pHostileParty && !CCharacterParty::DropMember(this, Character, DUC_DEAD) )
      {
        CCharacterParty::EndTeamBattle(this->m_pHostileParty);
        CCharacterParty::EndTeamBattle(this);
      }
      CCharacterParty::SendPartyCmdInfo(this, (PktPC::PartyCmd)((dwReferenceID != 0) + 3), Character);
      LogChantBug(Character, this, &byte_4EF4A4, "CCharacterParty::Leave", FileName, 562);
      CPartySpellMgr::RemoveMember(&this->m_PartySpellMgr, Character);
      Party = Character->m_DBData.m_Info.Party;
      if ( Party == this->m_Party.m_dwPartyID )
      {
        Character->m_pParty = 0;
        CCharacter::SetPID(Character, 0);
        DispatchTable = CChatDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
        v24 = 0;
        if ( StoragelpChatDispatch.m_lpDispatch )
          CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], Character);
        v24 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacterParty::Leave",
          FileName,
          568,
          aCid0x08x_100,
          Character->m_dwCID,
          Party,
          this->m_Party.m_dwPartyID);
      }
    }
    v13 = this->m_Party.m_cMemberNum - 1;
    this->m_Party.m_cMemberNum = v13;
    if ( v5 < v13 )
    {
      v14 = this->m_Party.Name[v5];
      v15 = &this->m_bAutoRouting[v5];
      v16 = &this->m_Party.MemberCID[v5];
      do
      {
        *v16 = v16[1];
        *(_DWORD *)v14 = *((_DWORD *)v14 + 4);
        *((_DWORD *)v14 + 1) = *((_DWORD *)v14 + 5);
        v17 = *((_DWORD *)v14 + 7);
        *((_DWORD *)v14 + 2) = *((_DWORD *)v14 + 6);
        *((_DWORD *)v14 + 3) = v17;
        v16[10] = v16[11];
        *(unsigned int *)((char *)v16 + 83) = *(unsigned int *)((char *)v16 + 87);
        *v15 = v15[1];
        v14 += 16;
        ++v16;
        ++v15;
      }
      while ( (int)&v15[-344 - (_DWORD)this] < this->m_Party.m_cMemberNum );
    }
    v18 = 0;
    this->m_Party.MemberCID[this->m_Party.m_cMemberNum] = 0;
    v19 = this->m_Party.Name[this->m_Party.m_cMemberNum];
    *(_DWORD *)v19 = 0;
    *((_DWORD *)v19 + 1) = 0;
    *((_DWORD *)v19 + 2) = 0;
    *((_DWORD *)v19 + 3) = 0;
    this->m_Party.ServerID[this->m_Party.m_cMemberNum] = 0;
    this->m_pMemberPointer[this->m_Party.m_cMemberNum] = 0;
    this->m_bAutoRouting[this->m_Party.m_cMemberNum] = 1;
    if ( this->m_Party.m_cMemberNum )
    {
      for ( i = this->m_Party.ServerID; *i != dwServerID; ++i )
      {
        if ( ++v18 >= this->m_Party.m_cMemberNum )
          return 1;
      }
      return 0;
    }
    else
    {
      return 1;
    }
  }
  else
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::Leave",
      FileName,
      614,
      (char *)&byte_4EF888,
      this->m_Party.m_dwPartyID,
      dwSenderCID);
    return -1;
  }
}

//----- (0047B7E0) --------------------------------------------------------
char __thiscall CCharacterParty::Login(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID)
{
  int m_cMemberNum; // eax
  int v5; // edi
  unsigned int *MemberCID; // ecx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v10; // ebx
  unsigned int S_addr; // eax
  int v12; // edx
  int v13; // eax
  int v14; // edx
  unsigned int v15; // eax
  int v16; // edx
  int v17; // eax
  unsigned int m_dwCID; // ecx
  AddressInfo Address; // [esp+Ch] [ebp-24h] BYREF

  m_cMemberNum = this->m_Party.m_cMemberNum;
  v5 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != dwSenderCID )
    {
      ++v5;
      ++MemberCID;
      if ( v5 >= m_cMemberNum )
        goto LABEL_5;
    }
    this->m_Party.ServerID[v5] = dwReferenceID;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, dwSenderCID);
    v10 = Character;
    if ( Character )
    {
      S_addr = Character->m_PublicAddress.sin_addr.S_un.S_addr;
      *(_DWORD *)&Address.m_PublicAddress.sin_family = *(_DWORD *)&v10->m_PublicAddress.sin_family;
      v12 = *(_DWORD *)v10->m_PublicAddress.sin_zero;
      Address.m_PublicAddress.sin_addr.S_un.S_addr = S_addr;
      v13 = *(_DWORD *)&v10->m_PublicAddress.sin_zero[4];
      *(_DWORD *)Address.m_PublicAddress.sin_zero = v12;
      v14 = *(_DWORD *)&v10->m_PrivateAddress.sin_family;
      *(_DWORD *)&Address.m_PublicAddress.sin_zero[4] = v13;
      v15 = v10->m_PrivateAddress.sin_addr.S_un.S_addr;
      *(_DWORD *)&Address.m_PrivateAddress.sin_family = v14;
      v16 = *(_DWORD *)v10->m_PrivateAddress.sin_zero;
      Address.m_PrivateAddress.sin_addr.S_un.S_addr = v15;
      v17 = *(_DWORD *)&v10->m_PrivateAddress.sin_zero[4];
      m_dwCID = v10->m_dwCID;
      *(_DWORD *)Address.m_PrivateAddress.sin_zero = v16;
      Address.m_dwCharID = m_dwCID;
      *(_DWORD *)&Address.m_PrivateAddress.sin_zero[4] = v17;
      CCharacterParty::SendPartyCommand(this, PC_LOGIN, this->m_Party.Name[v5], dwSenderCID, dwReferenceID, &Address);
      LogChantBug(v10, this, &byte_4EF744, "CCharacterParty::Login", FileName, 645);
      this->m_pMemberPointer[v5] = v10;
      CPartySpellMgr::AddMember(&this->m_PartySpellMgr, v10);
      CCharacterParty::SendPartyInfo(this, v10);
    }
    else
    {
      memset(&Address, 0, sizeof(Address));
      CCharacterParty::SendPartyCommand(this, PC_LOGIN, this->m_Party.Name[v5], dwSenderCID, dwReferenceID, &Address);
    }
    return 1;
  }
  else
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::Login",
      FileName,
      662,
      (char *)&byte_4EF8F8,
      this->m_Party.m_dwPartyID,
      dwSenderCID);
    return 0;
  }
}

//----- (0047B960) --------------------------------------------------------
char __thiscall CCharacterParty::ReLogin(CCharacterParty *this, CCharacter *lpCharacter)
{
  unsigned int S_addr; // edx
  int v4; // ecx
  int v5; // edx
  int v6; // ecx
  unsigned int v7; // edx
  unsigned int m_dwCID; // eax
  AddressInfo Address; // [esp+8h] [ebp-24h] BYREF

  if ( !lpCharacter )
    return 0;
  S_addr = lpCharacter->m_PublicAddress.sin_addr.S_un.S_addr;
  *(_DWORD *)&Address.m_PublicAddress.sin_family = *(_DWORD *)&lpCharacter->m_PublicAddress.sin_family;
  v4 = *(_DWORD *)lpCharacter->m_PublicAddress.sin_zero;
  Address.m_PublicAddress.sin_addr.S_un.S_addr = S_addr;
  v5 = *(_DWORD *)&lpCharacter->m_PublicAddress.sin_zero[4];
  *(_DWORD *)Address.m_PublicAddress.sin_zero = v4;
  v6 = *(_DWORD *)&lpCharacter->m_PrivateAddress.sin_family;
  *(_DWORD *)&Address.m_PublicAddress.sin_zero[4] = v5;
  v7 = lpCharacter->m_PrivateAddress.sin_addr.S_un.S_addr;
  *(_DWORD *)&Address.m_PrivateAddress.sin_family = v6;
  *(_DWORD *)Address.m_PrivateAddress.sin_zero = *(_DWORD *)lpCharacter->m_PrivateAddress.sin_zero;
  Address.m_PrivateAddress.sin_addr.S_un.S_addr = v7;
  m_dwCID = lpCharacter->m_dwCID;
  *(_DWORD *)&Address.m_PrivateAddress.sin_zero[4] = *(_DWORD *)&lpCharacter->m_PrivateAddress.sin_zero[4];
  Address.m_dwCharID = m_dwCID;
  CCharacterParty::SendPartyCommand(this, PC_RELOGIN, lpCharacter->m_DBData.m_Info.Name, m_dwCID, 0, &Address);
  CCharacterParty::SendPartyInfo(this, lpCharacter);
  return 1;
}

//----- (0047B9F0) --------------------------------------------------------
int __thiscall CCharacterParty::Logout(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID)
{
  CServerSetup *Instance; // eax
  unsigned int ServerID; // ebp
  int v6; // edi
  unsigned int *MemberCID; // ecx
  CCharacter *v9; // eax
  int v10; // eax
  unsigned int *i; // edx
  AddressInfo Address; // [esp+Ch] [ebp-28h] BYREF

  Instance = CServerSetup::GetInstance();
  ServerID = CServerSetup::GetServerID(Instance);
  memset(&Address, 0, sizeof(Address));
  v6 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != dwSenderCID )
    {
      ++v6;
      ++MemberCID;
      if ( v6 >= this->m_Party.m_cMemberNum )
        goto LABEL_5;
    }
    v9 = this->m_pMemberPointer[v6];
    if ( v9 )
    {
      LogChantBug(v9, this, &byte_4EF4A4, "CCharacterParty::Logout", FileName, 718);
      CPartySpellMgr::RemoveMember(&this->m_PartySpellMgr, this->m_pMemberPointer[v6]);
    }
    this->m_Party.ServerID[v6] = 0;
    this->m_pMemberPointer[v6] = 0;
    this->m_bAutoRouting[v6] = 1;
    v10 = 0;
    if ( this->m_Party.m_cMemberNum )
    {
      for ( i = this->m_Party.ServerID; *i != ServerID; ++i )
      {
        if ( ++v10 >= this->m_Party.m_cMemberNum )
          return 1;
      }
      CCharacterParty::SendPartyCommand(this, PC_LOGOUT, this->m_Party.Name[v6], dwSenderCID, dwReferenceID, &Address);
      return 0;
    }
    else
    {
      return 1;
    }
  }
  else
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::Logout",
      FileName,
      742,
      (char *)&byte_4EF970,
      this->m_Party.m_dwPartyID,
      dwSenderCID);
    return -1;
  }
}

//----- (0047BB60) --------------------------------------------------------
char __thiscall CCharacterParty::TransferLeader(CCharacterParty *this, unsigned int dwLeaderCID)
{
  int v3; // eax
  unsigned int *MemberCID; // ecx
  unsigned int m_dwPartyID; // ecx
  AddressInfo Address; // [esp+8h] [ebp-28h] BYREF

  memset(&Address, 0, sizeof(Address));
  v3 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != dwLeaderCID )
    {
      ++v3;
      ++MemberCID;
      if ( v3 >= this->m_Party.m_cMemberNum )
        goto LABEL_5;
    }
    CCharacterParty::SendPartyCommand(
      this,
      PC_TRANSFER,
      this->m_Party.Name[v3],
      dwLeaderCID,
      this->m_Party.m_dwLeaderID,
      &Address);
    m_dwPartyID = this->m_Party.m_dwPartyID;
    this->m_Party.m_dwLeaderID = dwLeaderCID;
    CPartyMgr::DeleteFindMemberList(CSingleton<CPartyMgr>::ms_pSingleton, m_dwPartyID);
    return 1;
  }
  else
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::TransferLeader",
      FileName,
      806,
      (char *)&byte_4EF9F0,
      this->m_Party.m_dwPartyID,
      dwLeaderCID);
    return 0;
  }
}

//----- (0047BC40) --------------------------------------------------------
char __thiscall CCharacterParty::Destory(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID)
{
  unsigned __int8 m_cMemberNum; // al
  CCharacter **m_pMemberPointer; // esi
  CCharacter *v7; // eax
  CSingleDispatch *DispatchTable; // eax
  int v9; // [esp+Ch] [ebp-44h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+10h] [ebp-40h] BYREF
  AddressInfo Address; // [esp+18h] [ebp-38h] BYREF
  int v12; // [esp+4Ch] [ebp-4h]

  m_cMemberNum = this->m_Party.m_cMemberNum;
  if ( m_cMemberNum == 2 )
  {
    memset(&Address, 0, sizeof(Address));
    CCharacterParty::SendPartyCommand(this, PC_DESTROY, &SenderName_In, dwSenderCID, dwReferenceID, &Address);
    LogChantBug(0, this, &byte_4EFA4C, "CCharacterParty::Destory", FileName, 871);
    CPartySpellMgr::ClearMember(&this->m_PartySpellMgr);
    m_pMemberPointer = this->m_pMemberPointer;
    v9 = 2;
    do
    {
      if ( *m_pMemberPointer )
      {
        if ( this->m_pHostileParty && !CCharacterParty::DropMember(this, *m_pMemberPointer, DUC_DEAD) )
        {
          CCharacterParty::EndTeamBattle(this->m_pHostileParty);
          CCharacterParty::EndTeamBattle(this);
        }
        CCharacterParty::SendPartyCmdInfo(this, PC_DESTROY, *m_pMemberPointer);
        v7 = *m_pMemberPointer;
        if ( (*m_pMemberPointer)->m_DBData.m_Info.Party == this->m_Party.m_dwPartyID )
        {
          v7->m_pParty = 0;
          CCharacter::SetPID(*m_pMemberPointer, 0);
          DispatchTable = CChatDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
          v12 = 0;
          if ( StoragelpChatDispatch.m_lpDispatch )
            CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], *m_pMemberPointer);
          v12 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacterParty::Destory",
            FileName,
            893,
            aCid0x08x_100,
            v7->m_dwCID,
            v7->m_DBData.m_Info.Party,
            this->m_Party.m_dwPartyID);
        }
      }
      ++m_pMemberPointer;
      --v9;
    }
    while ( v9 );
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::Destory",
      FileName,
      864,
      (char *)&byte_4EFAA0,
      this->m_Party.m_dwPartyID,
      m_cMemberNum);
    return 0;
  }
}

//----- (0047BE20) --------------------------------------------------------
char __thiscall CCharacterParty::AdjustAutoRouting(CCharacterParty *this, unsigned int dwTargetID, bool bSwitch)
{
  char v4; // bl
  int v5; // edi
  unsigned int *MemberCID; // eax
  AddressInfo Address; // [esp+10h] [ebp-28h] BYREF

  v4 = 0;
  v5 = 0;
  MemberCID = this->m_Party.MemberCID;
  do
  {
    if ( dwTargetID )
    {
      if ( *MemberCID == dwTargetID )
      {
        if ( this->m_bAutoRouting[v5] == bSwitch )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacterParty::AdjustAutoRouting",
            FileName,
            989,
            aCid0x08x_310,
            dwTargetID,
            this->m_Party.m_dwPartyID);
        this->m_bAutoRouting[v5] = bSwitch;
        v4 = 1;
        goto LABEL_11;
      }
    }
    else
    {
      this->m_bAutoRouting[v5] = bSwitch;
      v4 = 1;
    }
    ++v5;
    ++MemberCID;
  }
  while ( v5 < 10 );
  if ( !v4 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::AdjustAutoRouting",
      FileName,
      1001,
      aCid0x08x_30,
      dwTargetID,
      this->m_Party.m_dwPartyID);
    return 0;
  }
LABEL_11:
  memset(&Address, 0, sizeof(Address));
  CCharacterParty::SendPartyCommand(
    this,
    (PktPC::PartyCmd)(!bSwitch + 13),
    &byte_4EFAF4,
    dwTargetID,
    this->m_Party.m_dwLeaderID,
    &Address);
  return v4;
}

//----- (0047BF50) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Erase(this, i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0047BF90) --------------------------------------------------------
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Insert(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode,
        unsigned int *_Val)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // edx
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const int *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x3FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(
                                                                                                (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)this,
                                                                                                (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead,
                                                                                                (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)_Wherenode,
                                                                                                (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)this->_Myhead,
                                                                                                _Val,
                                                                                                0);
  Myhead = this->_Myhead;
  _Vala = (const int *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0047C140) --------------------------------------------------------
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(this, Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0047C400) --------------------------------------------------------
std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::insert(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> *result,
        unsigned int *_Val)
{
  unsigned int *v3; // ebp
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // eax
  bool v7; // cl
  signed int v8; // edx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    v8 = *_Val;
    do
    {
      v7 = v8 < Parent->_Myval;
      Myhead = Parent;
      _Addleft = v7;
      if ( v8 >= Parent->_Myval )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (unsigned int *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec((std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)_Val;
  }
  if ( v9->_Myval >= (signed int)*v3 )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (0047C4C0) --------------------------------------------------------
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _First,
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Erase(this, Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(this, &_First, v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0047C580) --------------------------------------------------------
void __thiscall CMonsterParty::~CMonsterParty(CMonsterParty *this)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator v3; // [esp-8h] [ebp-24h]
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  this->__vftable = (CMonsterParty_vtbl *)&CMonsterParty::`vftable';
  Myhead = this->m_partyTargets._Myhead;
  v3._Ptr = Myhead->_Left;
  v5 = 0;
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
    &this->m_partyTargets,
    &result,
    v3,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)Myhead);
  operator delete(this->m_partyTargets._Myhead);
  this->m_partyTargets._Myhead = 0;
  this->m_partyTargets._Mysize = 0;
  v5 = -1;
  CParty::~CParty(this);
}
// 4EFBB8: using guessed type void *CMonsterParty::`vftable';

//----- (0047C610) --------------------------------------------------------
void __thiscall std::set<int>::~set<int>(std::set<int> *this)
{
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (0047C640) --------------------------------------------------------
CMonsterParty *__thiscall CMonsterParty::`vector deleting destructor'(CMonsterParty *this, char a2)
{
  CMonsterParty::~CMonsterParty(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0047C660) --------------------------------------------------------
void __thiscall CMonsterParty::CMonsterParty(CMonsterParty *this)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v2; // eax

  this->__vftable = (CMonsterParty_vtbl *)&CParty::`vftable';
  CPartySpellMgr::CPartySpellMgr(&this->m_PartySpellMgr);
  PARTY::PARTY(&this->m_Party);
  this->__vftable = (CMonsterParty_vtbl *)&CMonsterParty::`vftable';
  this->m_fHelpRange = 36.0;
  v2 = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(&this->m_partyTargets);
  this->m_partyTargets._Myhead = v2;
  v2->_Isnil = 1;
  this->m_partyTargets._Myhead->_Parent = this->m_partyTargets._Myhead;
  this->m_partyTargets._Myhead->_Left = this->m_partyTargets._Myhead;
  this->m_partyTargets._Myhead->_Right = this->m_partyTargets._Myhead;
  this->m_partyTargets._Mysize = 0;
  this->m_nAvgLevel = 0;
  this->m_pMemberPointer[0] = 0;
  this->m_pMemberPointer[1] = 0;
  this->m_pMemberPointer[2] = 0;
  this->m_pMemberPointer[3] = 0;
  this->m_pMemberPointer[4] = 0;
  this->m_pMemberPointer[5] = 0;
  this->m_pMemberPointer[6] = 0;
  this->m_pMemberPointer[7] = 0;
  this->m_pMemberPointer[8] = 0;
  this->m_pMemberPointer[9] = 0;
}
// 4EF170: using guessed type void *CParty::`vftable';
// 4EFBB8: using guessed type void *CMonsterParty::`vftable';

//----- (0047C740) --------------------------------------------------------
unsigned int __thiscall CMonsterParty::GetMemberTypeNum(CMonsterParty *this)
{
  unsigned __int8 m_cMemberNum; // cl
  int v3; // edi
  CMonster **m_pMemberPointer; // ebx
  unsigned int Mysize; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // [esp-8h] [ebp-34h]
  int _Val; // [esp+8h] [ebp-24h] BYREF
  std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> result; // [esp+Ch] [ebp-20h] BYREF
  std::set<int> typeSet; // [esp+14h] [ebp-18h] BYREF
  int v11; // [esp+28h] [ebp-4h]

  typeSet._Myhead = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode((std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&typeSet);
  typeSet._Myhead->_Isnil = 1;
  typeSet._Myhead->_Parent = typeSet._Myhead;
  typeSet._Myhead->_Left = typeSet._Myhead;
  typeSet._Myhead->_Right = typeSet._Myhead;
  typeSet._Mysize = 0;
  m_cMemberNum = this->m_Party.m_cMemberNum;
  v3 = 0;
  v11 = 0;
  if ( m_cMemberNum )
  {
    m_pMemberPointer = this->m_pMemberPointer;
    do
    {
      _Val = (*m_pMemberPointer)->m_MonsterInfo.m_cSkillPattern;
      std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::insert(
        &typeSet,
        &result,
        (unsigned int *)&_Val);
      ++v3;
      ++m_pMemberPointer;
    }
    while ( v3 < this->m_Party.m_cMemberNum );
  }
  Mysize = typeSet._Mysize;
  Left = typeSet._Myhead->_Left;
  v11 = -1;
  std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(
    &typeSet,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *)&_Val,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator)Left,
    (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator)typeSet._Myhead);
  operator delete(typeSet._Myhead);
  return Mysize;
}

//----- (0047C810) --------------------------------------------------------
void __cdecl CNPC::ScriptErrorMessage(const char *msg)
{
  MessageBoxA(0, msg, "Script Error", 0);
}

//----- (0047C830) --------------------------------------------------------
void __cdecl CNPC::SetPosition(unsigned int nUID, float fDirection, float fPosX, float fPosY, float fPosZ)
{
  CMsgProcessMgr *Instance; // eax
  CMsgProc *Castle; // eax
  float *v7; // eax

  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = Castle::CCastleMgr::GetCastle(Instance, nUID);
  if ( Castle )
  {
    v7 = (float *)&Castle[1];
    *v7 = fPosX;
    v7[1] = fPosY;
    v7[2] = fPosZ;
  }
}

//----- (0047C890) --------------------------------------------------------
unsigned int __thiscall CNPC::RepairItem(CNPC *this, Item::CEquipment *lpEquipment, unsigned int *dwCurrentGold)
{
  unsigned int result; // eax

  if ( !this->m_bRepairable || !lpEquipment )
    return 0;
  result = (lpEquipment->m_dwPrice
          * (lpEquipment->m_cMaxNumOrDurability - lpEquipment->m_ItemData.m_cNumOrDurability)
          / lpEquipment->m_cMaxNumOrDurability) >> 1;
  if ( !result )
    result = 1;
  if ( result > *dwCurrentGold )
    return 0;
  *dwCurrentGold -= result;
  lpEquipment->m_ItemData.m_cNumOrDurability = lpEquipment->m_cMaxNumOrDurability;
  return result;
}

//----- (0047C8E0) --------------------------------------------------------
void __cdecl std::fill<unsigned short *,unsigned short>(
        unsigned __int16 *_First,
        unsigned __int16 *_Last,
        unsigned __int16 *_Val)
{
  unsigned __int16 *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (0047C900) --------------------------------------------------------
void __cdecl CNPC::QuestCompleteSave(bool bSave)
{
  CSingleton<CQuestMgr>::ms_pSingleton->m_lpQuestNode->m_bSave = bSave;
}

//----- (0047C910) --------------------------------------------------------
void __cdecl CNPC::Else()
{
  CSingleton<CQuestMgr>::ms_pSingleton->m_bFalseEvent = 1;
}

//----- (0047C920) --------------------------------------------------------
void __cdecl CNPC::EventDisappear(unsigned int nAmount, unsigned int nItemID, unsigned int nGold)
{
  Quest::EventNode *v3; // eax
  Quest::EventNode *v4; // esi

  v3 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  v4 = 0;
  if ( v3 )
  {
    v3->m_dwEventNumber = nItemID;
    v3->m_dwEventKind = 0;
    v3->m_fPosX = 0.0;
    v3->m_fPosY = 0.0;
    v3->m_fPosZ = 0.0;
    v3->m_dwEventAmount = nAmount;
    v3->m_dwEventAmount2 = nGold;
    v4 = v3;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v4) )
    operator delete(v4);
}

//----- (0047C970) --------------------------------------------------------
void __cdecl CNPC::EventGet(unsigned int nAmount, unsigned int nItemID)
{
  Quest::EventNode *v2; // eax
  Quest::EventNode *v3; // esi

  v2 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  v3 = 0;
  if ( v2 )
  {
    v2->m_dwEventAmount2 = 0;
    v2->m_fPosX = 0.0;
    v2->m_fPosY = 0.0;
    v2->m_fPosZ = 0.0;
    v2->m_dwEventKind = 1;
    v2->m_dwEventNumber = nItemID;
    v2->m_dwEventAmount = nAmount;
    v3 = v2;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v3) )
    operator delete(v3);
}

//----- (0047C9C0) --------------------------------------------------------
void __cdecl CNPC::EventSpawn(unsigned int nMonsterID, float fPosX, float fPosY, float fPosZ)
{
  Quest::EventNode *v4; // eax
  Quest::EventNode *v5; // esi

  v4 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  v5 = 0;
  if ( v4 )
  {
    v4->m_dwEventNumber = nMonsterID;
    v4->m_fPosX = fPosX;
    v4->m_dwEventAmount = 0;
    v4->m_dwEventAmount2 = 0;
    v4->m_dwEventKind = 2;
    v4->m_fPosY = fPosY;
    v4->m_fPosZ = fPosZ;
    v5 = v4;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v5) )
    operator delete(v5);
}

//----- (0047CA20) --------------------------------------------------------
void __cdecl CNPC::EventMonsterDrop(unsigned int nAmount, unsigned int nItemID)
{
  Quest::EventNode *v2; // eax
  Quest::EventNode *v3; // esi

  v2 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  v3 = 0;
  if ( v2 )
  {
    v2->m_dwEventAmount2 = 0;
    v2->m_fPosX = 0.0;
    v2->m_fPosY = 0.0;
    v2->m_fPosZ = 0.0;
    v2->m_dwEventKind = 3;
    v2->m_dwEventNumber = nItemID;
    v2->m_dwEventAmount = nAmount;
    v3 = v2;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v3) )
    operator delete(v3);
}

//----- (0047CA70) --------------------------------------------------------
void __cdecl CNPC::EventAward(unsigned int nExp, unsigned int nGold)
{
  Quest::EventNode *v2; // eax
  Quest::EventNode *v3; // esi

  v2 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  v3 = 0;
  if ( v2 )
  {
    v2->m_dwEventAmount2 = 0;
    v2->m_fPosX = 0.0;
    v2->m_fPosY = 0.0;
    v2->m_fPosZ = 0.0;
    v2->m_dwEventKind = 5;
    v2->m_dwEventNumber = nExp;
    v2->m_dwEventAmount = nGold;
    v3 = v2;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v3) )
    operator delete(v3);
}

//----- (0047CAC0) --------------------------------------------------------
void __cdecl CNPC::EventMsgBox()
{
  Quest::EventNode *v0; // eax
  Quest::EventNode *v1; // esi

  v0 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  if ( v0 )
  {
    v0->m_dwEventKind = 6;
    v0->m_dwEventNumber = 0;
    v0->m_dwEventAmount = 0;
    v0->m_dwEventAmount2 = 0;
    v0->m_fPosX = 0.0;
    v0->m_fPosY = 0.0;
    v0->m_fPosZ = 0.0;
    v1 = v0;
  }
  else
  {
    v1 = 0;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v1) )
    operator delete(v1);
}

//----- (0047CB10) --------------------------------------------------------
void __cdecl CNPC::EventPhase(unsigned int nPhaseNumber)
{
  Quest::EventNode *v1; // eax
  Quest::EventNode *v2; // esi

  v1 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  if ( v1 )
  {
    v1->m_dwEventKind = 7;
    v1->m_dwEventNumber = nPhaseNumber;
    v1->m_dwEventAmount = 0;
    v1->m_dwEventAmount2 = 0;
    v1->m_fPosX = 0.0;
    v1->m_fPosY = 0.0;
    v1->m_fPosZ = 0.0;
    v2 = v1;
  }
  else
  {
    v2 = 0;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v2) )
    operator delete(v2);
}

//----- (0047CB60) --------------------------------------------------------
void __cdecl CNPC::EventEnd()
{
  Quest::EventNode *v0; // eax
  Quest::EventNode *v1; // esi

  v0 = (Quest::EventNode *)operator new((tagHeader *)0x1C);
  if ( v0 )
  {
    v0->m_dwEventKind = 4;
    v0->m_dwEventNumber = 0;
    v0->m_dwEventAmount = 0;
    v0->m_dwEventAmount2 = 0;
    v0->m_fPosX = 0.0;
    v0->m_fPosY = 0.0;
    v0->m_fPosZ = 0.0;
    v1 = v0;
  }
  else
  {
    v1 = 0;
  }
  if ( !CQuestMgr::AddEvent(CSingleton<CQuestMgr>::ms_pSingleton, v1) )
    operator delete(v1);
}

//----- (0047CBB0) --------------------------------------------------------
void __cdecl std::copy_backward<unsigned short *,unsigned short *>(
        unsigned __int8 *_First,
        unsigned __int16 *_Last,
        unsigned __int16 *_Dest)
{
  memmove(
    (unsigned __int8 *)&_Dest[-(((char *)_Last - (char *)_First) >> 1)],
    _First,
    2 * (((char *)_Last - (char *)_First) >> 1));
}

//----- (0047CBD0) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<unsigned short>::iterator,int,unsigned short>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Mid,
        std::vector<unsigned short>::iterator _Last)
{
  unsigned __int16 *Myptr; // ebx
  unsigned __int16 *v4; // ebp
  int v5; // esi
  int v6; // eax
  int v7; // edi
  int v8; // edx
  unsigned __int16 *v9; // edx
  unsigned __int16 *v10; // edi
  std::vector<unsigned short>::iterator *p_First; // eax
  unsigned __int16 *v12; // ecx
  int v13; // eax
  unsigned __int16 **v14; // eax
  bool v15; // zf
  unsigned __int16 *v16; // eax
  unsigned __int16 *v17; // [esp+10h] [ebp-Ch] BYREF
  unsigned __int16 *v18; // [esp+14h] [ebp-8h] BYREF
  unsigned __int16 _Holeval; // [esp+18h] [ebp-4h]

  Myptr = _Last._Myptr;
  v4 = _First._Myptr;
  v5 = _Mid._Myptr - _First._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  v7 = v5;
  if ( v5 )
  {
    do
    {
      v8 = v6 % v7;
      v6 = v7;
      v7 = v8;
    }
    while ( v8 );
  }
  if ( v6 < _Last._Myptr - _First._Myptr && v6 > 0 )
  {
    v9 = &_First._Myptr[v6];
    _Mid._Myptr = (unsigned __int16 *)v6;
    do
    {
      _Holeval = *v9;
      v10 = v9;
      if ( &v9[v5] == Myptr )
      {
        p_First = &_First;
      }
      else
      {
        _Last._Myptr = &v9[v5];
        p_First = &_Last;
      }
      v12 = p_First->_Myptr;
      if ( p_First->_Myptr != v9 )
      {
        do
        {
          *v10 = *v12;
          v13 = Myptr - v12;
          v10 = v12;
          if ( v5 >= v13 )
          {
            v18 = &v4[v5 + 0x7FFFFFFF * v13];
            v14 = &v18;
          }
          else
          {
            v17 = &v12[v5];
            v14 = &v17;
          }
          v12 = *v14;
        }
        while ( *v14 != v9 );
      }
      --v9;
      v16 = (unsigned __int16 *)((char *)_Mid._Myptr - 1);
      v15 = _Mid._Myptr == (unsigned __int16 *)1;
      *v10 = _Holeval;
      _Mid._Myptr = v16;
    }
    while ( !v15 );
  }
}

//----- (0047CCA0) --------------------------------------------------------
int __stdcall std::vector<unsigned short>::_Ucopy<unsigned short *>(unsigned __int8 *src, int a2, unsigned __int8 *dst)
{
  int v3; // eax

  memmove(dst, src, 2 * ((a2 - (int)src) >> 1));
  return 2 * ((a2 - (int)src) >> 1) + v3;
}
// 47CCBF: variable 'v3' is possibly undefined


//----- (0047CCD0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<unsigned short>::iterator>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Mid,
        std::vector<unsigned short>::iterator _Last)
{
  int v4; // eax
  int v5; // eax
  unsigned int v6; // ebx
  unsigned int v7; // eax
  unsigned __int16 v8; // dx
  unsigned __int16 v9; // dx
  unsigned __int16 v10; // dx
  unsigned __int16 *v11; // esi
  unsigned __int16 *v12; // esi
  unsigned __int16 *v13; // edi
  unsigned __int16 v14; // bx
  unsigned __int16 v15; // di
  unsigned __int16 v16; // di
  unsigned __int16 v17; // si
  unsigned __int16 v18; // dx
  unsigned __int16 v19; // dx
  unsigned __int16 v20; // dx
  unsigned __int16 _Lasta; // [esp+14h] [ebp+Ch]
  unsigned __int16 _Lastb; // [esp+14h] [ebp+Ch]
  unsigned __int16 _Lastc; // [esp+14h] [ebp+Ch]
  unsigned __int16 _Lastd; // [esp+14h] [ebp+Ch]
  unsigned __int16 _Laste; // [esp+14h] [ebp+Ch]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    v18 = *_Mid._Myptr;
    if ( *_Mid._Myptr < *_First._Myptr )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v18;
    }
    v19 = *_Last._Myptr;
    if ( *_Last._Myptr < *_Mid._Myptr )
    {
      *_Last._Myptr = *_Mid._Myptr;
      *_Mid._Myptr = v19;
    }
    v20 = *_Mid._Myptr;
    if ( *_Mid._Myptr < *_First._Myptr )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v20;
    }
  }
  else
  {
    v5 = (v4 + 1) / 8;
    v6 = 4 * v5;
    v7 = 2 * v5;
    v8 = _First._Myptr[v7 / 2];
    if ( v8 < *_First._Myptr )
    {
      _First._Myptr[v7 / 2] = *_First._Myptr;
      *_First._Myptr = v8;
    }
    v9 = _First._Myptr[v6 / 2];
    if ( v9 < _First._Myptr[v7 / 2] )
    {
      _First._Myptr[v6 / 2] = _First._Myptr[v7 / 2];
      _First._Myptr[v7 / 2] = v9;
    }
    v10 = _First._Myptr[v7 / 2];
    if ( v10 < *_First._Myptr )
    {
      _First._Myptr[v7 / 2] = *_First._Myptr;
      *_First._Myptr = v10;
    }
    v11 = &_Mid._Myptr[v7 / 0xFFFFFFFE];
    if ( *_Mid._Myptr < _Mid._Myptr[v7 / 0xFFFFFFFE] )
    {
      _Lasta = *_Mid._Myptr;
      *_Mid._Myptr = *v11;
      *v11 = _Lasta;
    }
    if ( _Mid._Myptr[v7 / 2] < *_Mid._Myptr )
    {
      _Lastb = _Mid._Myptr[v7 / 2];
      _Mid._Myptr[v7 / 2] = *_Mid._Myptr;
      *_Mid._Myptr = _Lastb;
    }
    if ( *_Mid._Myptr < *v11 )
    {
      _Lastc = *_Mid._Myptr;
      *_Mid._Myptr = *v11;
      *v11 = _Lastc;
    }
    v12 = &_Last._Myptr[v7 / 0xFFFFFFFE];
    v13 = &_Last._Myptr[v6 / 0xFFFFFFFE];
    if ( _Last._Myptr[v7 / 0xFFFFFFFE] < _Last._Myptr[v6 / 0xFFFFFFFE] )
    {
      _Lastd = _Last._Myptr[v7 / 0xFFFFFFFE];
      *v12 = *v13;
      *v13 = _Lastd;
    }
    if ( *_Last._Myptr < *v12 )
    {
      _Laste = *_Last._Myptr;
      *_Last._Myptr = *v12;
      *v12 = _Laste;
    }
    v14 = *v12;
    if ( *v12 < *v13 )
    {
      *v12 = *v13;
      *v13 = v14;
    }
    v15 = *_Mid._Myptr;
    if ( *_Mid._Myptr < _First._Myptr[v7 / 2] )
    {
      *_Mid._Myptr = _First._Myptr[v7 / 2];
      _First._Myptr[v7 / 2] = v15;
    }
    v16 = *v12;
    if ( *v12 < *_Mid._Myptr )
    {
      *v12 = *_Mid._Myptr;
      *_Mid._Myptr = v16;
    }
    v17 = *_Mid._Myptr;
    if ( *_Mid._Myptr < _First._Myptr[v7 / 2] )
    {
      *_Mid._Myptr = _First._Myptr[v7 / 2];
      _First._Myptr[v7 / 2] = v17;
    }
  }
}

//----- (0047CE60) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<unsigned short>::iterator,int,unsigned short>(
        std::vector<unsigned short>::iterator _First,
        int _Hole,
        int _Bottom,
        unsigned __int16 _Val)
{
  int v4; // esi
  int v5; // eax
  bool i; // zf
  int j; // eax

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5] < _First._Myptr[v5 - 1] )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  for ( j = (v4 - 1) / 2; _Hole < v4; j = (j - 1) / 2 )
  {
    if ( _First._Myptr[j] >= _Val )
      break;
    _First._Myptr[v4] = _First._Myptr[j];
    v4 = j;
  }
  _First._Myptr[v4] = _Val;
}

//----- (0047CEF0) --------------------------------------------------------
unsigned __int16 *__thiscall std::vector<unsigned short>::_Ufill(
        std::vector<unsigned short> *this,
        unsigned __int16 *_Ptr,
        unsigned int _Count,
        const unsigned __int16 *_Val)
{
  unsigned __int16 *v4; // eax
  unsigned int v5; // ecx

  v4 = _Ptr;
  if ( _Count )
  {
    v5 = _Count;
    do
    {
      *v4++ = *_Val;
      --v5;
    }
    while ( v5 );
  }
  return &_Ptr[_Count];
}

//----- (0047CF20) --------------------------------------------------------
std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> *__cdecl std::_Unguarded_partition<std::vector<unsigned short>::iterator>(
        std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> *result,
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last)
{
  unsigned __int16 *Myptr; // ebx
  unsigned __int16 *v4; // ecx
  unsigned __int16 *i; // esi
  unsigned __int16 v6; // ax
  unsigned __int16 v7; // dx
  unsigned __int16 *v8; // eax
  unsigned __int16 *v9; // ebp
  unsigned __int16 v10; // di
  bool v11; // zf
  unsigned __int16 *v12; // edx
  unsigned __int16 v13; // di
  unsigned __int16 v14; // dx
  unsigned __int16 *v15; // edx
  unsigned __int16 v16; // dx
  unsigned __int16 v17; // di
  unsigned __int16 v18; // dx
  unsigned __int16 v19; // di
  std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> *v20; // eax
  unsigned __int16 v21; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<unsigned short>::iterator>(
    _First,
    (std::vector<unsigned short>::iterator)&_First._Myptr[_Last._Myptr - _First._Myptr],
    (std::vector<unsigned short>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[_Last._Myptr - _First._Myptr];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    v6 = *(v4 - 1);
    if ( *v4 > v6 )
      break;
    if ( *v4 < v6 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v7 = *v4;
    do
    {
      if ( v7 > *i )
        break;
      if ( v7 < *i )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v8 = i;
  v9 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v8 < Myptr; ++v8 )
      {
        if ( *v8 <= *v4 )
        {
          if ( *v8 < *v4 )
            break;
          v10 = *i;
          *i = *v8;
          Myptr = _Last._Myptr;
          ++i;
          *v8 = v10;
        }
      }
      v11 = v9 == _First._Myptr;
      if ( v9 > _First._Myptr )
      {
        v12 = v9 - 1;
        do
        {
          if ( *v4 <= *v12 )
          {
            if ( *v4 < *v12 )
              break;
            v13 = *--v4;
            *v4 = *v12;
            *v12 = v13;
          }
          --v9;
          --v12;
        }
        while ( _First._Myptr < v9 );
        Myptr = _Last._Myptr;
        v11 = v9 == _First._Myptr;
      }
      if ( v11 )
        break;
      --v9;
      if ( v8 == Myptr )
      {
        if ( v9 != --v4 )
        {
          v16 = *v9;
          *v9 = *v4;
          *v4 = v16;
        }
        v17 = *(i - 1);
        v18 = *v4;
        --i;
        *v4 = v17;
        *i = v18;
      }
      else
      {
        v19 = *v8;
        *v8 = *v9;
        Myptr = _Last._Myptr;
        ++v8;
        *v9 = v19;
      }
    }
    if ( v8 == Myptr )
      break;
    if ( i != v8 )
    {
      v14 = *v4;
      *v4 = *i;
      *i = v14;
    }
    v15 = v8;
    v21 = *v4;
    *v4 = *v8;
    Myptr = _Last._Myptr;
    ++i;
    ++v8;
    ++v4;
    *v15 = v21;
  }
  v20 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v20;
}

//----- (0047D0A0) --------------------------------------------------------
std::vector<unsigned short>::iterator *__cdecl std::_Lower_bound<std::vector<unsigned short>::iterator,unsigned short,int>(
        std::vector<unsigned short>::iterator *result,
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last,
        const unsigned __int16 *_Val)
{
  unsigned __int16 *Myptr; // esi
  int v5; // ecx
  int v6; // eax
  std::vector<unsigned short>::iterator *v7; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    v6 = v5 / 2;
    if ( Myptr[v5 / 2] >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v6 + 1;
      v5 += -1 - v6;
    }
  }
  v7 = result;
  result->_Myptr = Myptr;
  return v7;
}

//----- (0047D0F0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<unsigned short>::iterator,int,unsigned short>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last)
{
  int i; // esi

  for ( i = _Last._Myptr - _First._Myptr;
        i > 0;
        std::_Adjust_heap<std::vector<unsigned short>::iterator,int,unsigned short>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          _First._Myptr[i]) )
  {
    --i;
  }
}

//----- (0047D130) --------------------------------------------------------
CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const,CNPC *>>>(
        CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // edi
  std::mem_fun_t<bool,CNPC> *m_fnSecondProcess; // esi
  CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > *v7; // eax

  Ptr = _First._Ptr;
  v5 = _Last._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v7 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  else
  {
    m_fnSecondProcess = _Func.m_fnSecondProcess;
    do
    {
      m_fnSecondProcess->_Pmemfun(Ptr->_Myval.second);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
      Ptr = _First._Ptr;
    }
    while ( _First._Ptr != v5 );
    v7 = result;
    result->m_fnSecondProcess = m_fnSecondProcess;
  }
  return v7;
}

//----- (0047D170) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<unsigned short>::iterator>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last)
{
  unsigned __int16 *i; // esi
  unsigned __int16 v3; // cx
  unsigned __int16 *v4; // eax
  std::vector<unsigned short>::iterator v5; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      v3 = *i;
      if ( *i >= *_First._Myptr )
      {
        v4 = i - 1;
        if ( v3 < *(i - 1) )
        {
          do
            v5._Myptr = v4--;
          while ( v3 < *v4 );
          if ( v5._Myptr != i )
            std::_Rotate<std::vector<unsigned short>::iterator,int,unsigned short>(
              v5,
              (std::vector<unsigned short>::iterator)i,
              (std::vector<unsigned short>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<unsigned short>::iterator,int,unsigned short>(
          _First,
          (std::vector<unsigned short>::iterator)i,
          (std::vector<unsigned short>::iterator)(i + 1));
      }
    }
  }
}

//----- (0047D1E0) --------------------------------------------------------
void __thiscall Quest::TriggerNode::~TriggerNode(Quest::TriggerNode *this)
{
  if ( this->m_lstFalseEvent._Myfirst )
    operator delete(this->m_lstFalseEvent._Myfirst);
  this->m_lstFalseEvent._Myfirst = 0;
  this->m_lstFalseEvent._Mylast = 0;
  this->m_lstFalseEvent._Myend = 0;
  if ( this->m_lstEvent._Myfirst )
    operator delete(this->m_lstEvent._Myfirst);
  this->m_lstEvent._Myfirst = 0;
  this->m_lstEvent._Mylast = 0;
  this->m_lstEvent._Myend = 0;
}

//----- (0047D220) --------------------------------------------------------
void __thiscall CNPC::~CNPC(CNPC *this)
{
  this->__vftable = (CNPC_vtbl *)&CNPC::`vftable';
  if ( this->m_Quests._Myfirst )
    operator delete(this->m_Quests._Myfirst);
  this->m_Quests._Myfirst = 0;
  this->m_Quests._Mylast = 0;
  this->m_Quests._Myend = 0;
  if ( this->m_Goods._Myfirst )
    operator delete(this->m_Goods._Myfirst);
  this->m_Goods._Myfirst = 0;
  this->m_Goods._Mylast = 0;
  this->m_Goods._Myend = 0;
  this->__vftable = (CNPC_vtbl *)&CCreature::`vftable';
}
// 4DABA8: using guessed type void *CCreature::`vftable';
// 4EFC58: using guessed type void *CNPC::`vftable';

//----- (0047D270) --------------------------------------------------------
int __thiscall CNPC::IsEnemy(CNPC *this, CCreature *lpTarget)
{
  return 1;
}

//----- (0047D280) --------------------------------------------------------
CParty *__thiscall std::streambuf::showmanyc(CNPC *this)
{
  return 0;
}

//----- (0047D290) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<unsigned short>::iterator>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last)
{
  int i; // esi
  unsigned __int16 v3; // ax

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 1 > 1; i -= 2 )
  {
    v3 = *(unsigned __int16 *)((char *)_First._Myptr + i - 2);
    *(unsigned __int16 *)((char *)_First._Myptr + i - 2) = *_First._Myptr;
    std::_Adjust_heap<std::vector<unsigned short>::iterator,int,unsigned short>(_First, 0, (i - 2) >> 1, v3);
  }
}

//----- (0047D2E0) --------------------------------------------------------
CNPC *__thiscall CNPC::`scalar deleting destructor'(CNPC *this, char a2)
{
  CNPC::~CNPC(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0047D300) --------------------------------------------------------
void __thiscall CNPC::SellToCharacter(
        CNPC *this,
        CCharacter *lpCustomer,
        unsigned __int16 wKindItem,
        TakeType takeType,
        unsigned int *dwPrice)
{
  unsigned int Gold; // ebx
  bool m_bBlackMarketeer; // zf
  unsigned __int16 v8; // bp
  const Item::ItemInfo *ItemInfo; // eax
  const Item::ItemInfo *v10; // esi
  unsigned int v11; // eax
  unsigned __int16 BlackMarketItem; // di
  const Item::ItemInfo *v13; // eax
  Item::CEquipment *v14; // eax
  Item::CEquipment *v15; // eax
  unsigned __int16 *Mylast; // edi
  unsigned __int16 v17; // bp
  const Item::ItemInfo *v18; // edi
  int m_cNum; // ecx
  unsigned int v20; // edx
  unsigned int v21; // edx
  int v22; // eax
  bool v23; // bl
  unsigned __int8 m_cMaxDurabilityOrStack; // al
  int v25; // eax
  unsigned __int8 v26; // [esp-8h] [ebp-1Ch]
  unsigned int dwCurrentMileage; // [esp+10h] [ebp-4h]

  Gold = lpCustomer->m_DBData.m_Info.Gold;
  m_bBlackMarketeer = this->m_bBlackMarketeer;
  dwCurrentMileage = lpCustomer->m_DBData.m_Info.Mileage;
  LOBYTE(lpCustomer) = lpCustomer->m_CreatureStatus.m_nLevel;
  if ( m_bBlackMarketeer )
  {
    v8 = wKindItem;
    ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, wKindItem);
    v10 = ItemInfo;
    if ( ItemInfo )
    {
      v11 = takeType.m_cNum * ItemInfo->m_DetailData.m_dwBlackPrice;
      *dwPrice = v11;
      if ( v11 <= Gold )
      {
        BlackMarketItem = AwardTable::CAward::GetBlackMarketItem(CSingleton<AwardTable::CAward>::ms_pSingleton, v8);
        v13 = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, BlackMarketItem);
        if ( v13 )
        {
          if ( v13->m_DetailData.m_cXSize > v10->m_DetailData.m_cXSize
            || v13->m_DetailData.m_cYSize > v10->m_DetailData.m_cYSize )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CNPC::SellToCharacter",
              aDWorkRylSource_22,
              473,
              byte_4EFE58,
              BlackMarketItem,
              v8);
          }
          else
          {
            Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v13);
            if ( v14 )
            {
              v26 = (unsigned __int8)lpCustomer;
              v15 = Item::CEquipment::DowncastToEquipment(v14);
              Item::CEquipment::AddRandomOption(v15, v26, 1);
            }
            else
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "CNPC::SellToCharacter",
                aDWorkRylSource_22,
                480,
                (char *)&byte_4EFEBC);
            }
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CNPC::SellToCharacter",
            aDWorkRylSource_22,
            465,
            (char *)&byte_4EFF78,
            BlackMarketItem);
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CNPC::SellToCharacter",
          aDWorkRylSource_22,
          456,
          byte_4EFFB4,
          v11,
          Gold);
      }
    }
    else
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CNPC::SellToCharacter", aDWorkRylSource_22, 449, byte_4EFFE8, v8);
    }
  }
  else
  {
    Mylast = this->m_Goods._Mylast;
    std::_Lower_bound<std::vector<unsigned short>::iterator,unsigned short,int>(
      (std::vector<unsigned short>::iterator *)&lpCustomer,
      (std::vector<unsigned short>::iterator)this->m_Goods._Myfirst,
      (std::vector<unsigned short>::iterator)Mylast,
      &wKindItem);
    v17 = wKindItem;
    if ( lpCustomer == (CCharacter *)Mylast || wKindItem < LOWORD(lpCustomer->__vftable) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CNPC::SellToCharacter",
        aDWorkRylSource_22,
        554,
        aNid0x08x_4,
        this->m_dwCID,
        wKindItem);
    }
    else
    {
      v18 = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, wKindItem);
      if ( v18 )
      {
        m_cNum = takeType.m_cNum;
        if ( this->m_bMustSellWithMileage )
        {
          v20 = takeType.m_cNum * v18->m_DetailData.m_dwMedalPrice;
          *dwPrice = v20;
          if ( v20 > dwCurrentMileage )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CNPC::SellToCharacter",
              aDWorkRylSource_22,
              504,
              aNid0x08x_3,
              this->m_dwCID,
              dwCurrentMileage,
              v17,
              v20,
              m_cNum,
              m_cNum * v20);
            return;
          }
        }
        else
        {
          v21 = takeType.m_cNum * v18->m_DetailData.m_dwPrice;
          *dwPrice = v21;
          if ( v21 > Gold )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CNPC::SellToCharacter",
              aDWorkRylSource_22,
              515,
              aNid0x08x_2,
              this->m_dwCID,
              Gold,
              v17,
              v21,
              m_cNum,
              m_cNum * v21);
            return;
          }
        }
        v22 = v18->m_DetailData.m_dwFlags & 8;
        v23 = (_BYTE)v22 == 8;
        if ( (_BYTE)v22 == 8
          && (m_cMaxDurabilityOrStack = v18->m_DetailData.m_cMaxDurabilityOrStack,
              takeType.m_cNum > m_cMaxDurabilityOrStack) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CNPC::SellToCharacter",
            aDWorkRylSource_22,
            543,
            aNid0x08x,
            this->m_dwCID,
            v23,
            m_cNum,
            m_cMaxDurabilityOrStack);
        }
        else
        {
          Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v18);
          if ( v25 )
          {
            if ( v23 )
              *(_BYTE *)(v25 + 21) = takeType.m_cNum;
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CNPC::SellToCharacter",
              aDWorkRylSource_22,
              535,
              aNid0x08x_5,
              this->m_dwCID);
          }
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CNPC::SellToCharacter",
          aDWorkRylSource_22,
          549,
          aNid0x08x_1,
          this->m_dwCID,
          v17);
      }
    }
  }
}
// 47D3EA: variable 'v14' is possibly undefined
// 47D585: variable 'v25' is possibly undefined

//----- (0047D620) --------------------------------------------------------
char __thiscall CNPC::GetQuest(
        CNPC *this,
        CCharacter *lpCharacter,
        unsigned __int16 wQuestID,
        Quest::QuestNode **lppQuestNode)
{
  unsigned __int16 *Mylast; // edi
  unsigned __int16 v6; // bp
  Quest::QuestNode *QuestNode; // edi
  unsigned __int16 m_nLevel; // ax
  int v10; // [esp-8h] [ebp-18h]
  std::vector<unsigned short>::iterator result; // [esp+Ch] [ebp-4h] BYREF

  Mylast = this->m_Quests._Mylast;
  std::_Lower_bound<std::vector<unsigned short>::iterator,unsigned short,int>(
    &result,
    (std::vector<unsigned short>::iterator)this->m_Quests._Myfirst,
    (std::vector<unsigned short>::iterator)Mylast,
    &wQuestID);
  v6 = wQuestID;
  if ( result._Myptr == Mylast || wQuestID < *result._Myptr )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CNPC::GetQuest",
      aDWorkRylSource_22,
      583,
      aCid0x08xNpc0x0,
      lpCharacter->m_dwCID,
      this->m_dwCID,
      wQuestID);
    return 0;
  }
  QuestNode = CQuestMgr::GetQuestNode(CSingleton<CQuestMgr>::ms_pSingleton, wQuestID);
  if ( !QuestNode )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CNPC::GetQuest", aDWorkRylSource_22, 590, aNid0x08x_0, this->m_dwCID, v6);
    return 0;
  }
  m_nLevel = lpCharacter->m_CreatureStatus.m_nLevel;
  if ( QuestNode->m_wMinLevel > m_nLevel || QuestNode->m_wMaxLevel < m_nLevel )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CNPC::GetQuest",
      aDWorkRylSource_22,
      598,
      aCid0x08x_205,
      this->m_dwCID,
      v6,
      m_nLevel);
  }
  else
  {
    if ( ((1 << (((int (__thiscall *)(CCharacter *))lpCharacter->GetClass)(lpCharacter) - 1)) & QuestNode->m_dwClass) != 0 )
    {
      *lppQuestNode = QuestNode;
      return 1;
    }
    v10 = lpCharacter->GetClass(lpCharacter);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CNPC::GetQuest",
      aDWorkRylSource_22,
      605,
      aCid0x08x_313,
      this->m_dwCID,
      v6,
      v10);
  }
  return 0;
}

//----- (0047D770) --------------------------------------------------------
void __thiscall __noreturn std::vector<unsigned short>::_Xlen(std::vector<unsigned short> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0047D7E0) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<unsigned short>::iterator,int>(
        std::vector<unsigned short>::iterator _First,
        std::vector<unsigned short>::iterator _Last,
        int _Ideal)
{
  unsigned __int16 *Myptr; // ebx
  unsigned __int16 *v4; // edi
  int v5; // eax
  unsigned __int16 *v7; // ebp
  std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<unsigned short>::iterator>(
        (std::vector<unsigned short>::iterator)Myptr,
        (std::vector<unsigned short>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<unsigned short>::iterator>(
        &_Mid,
        (std::vector<unsigned short>::iterator)Myptr,
        (std::vector<unsigned short>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFE) >= (int)(((char *)v4
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFE) )
      {
        std::_Sort<std::vector<unsigned short>::iterator,int>(
          _Mid.second,
          (std::vector<unsigned short>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<unsigned short>::iterator,int>(
          (std::vector<unsigned short>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v4 - (char *)Myptr) & 0xFFFFFFFE) > 2 )
      std::_Make_heap<std::vector<unsigned short>::iterator,int,unsigned short>(
        (std::vector<unsigned short>::iterator)Myptr,
        (std::vector<unsigned short>::iterator)v4);
    std::sort_heap<std::vector<unsigned short>::iterator>(
      (std::vector<unsigned short>::iterator)Myptr,
      (std::vector<unsigned short>::iterator)v4);
  }
}
// 47D87C: conditional instruction was optimized away because eax.4>=21

//----- (0047D8B0) --------------------------------------------------------
void __thiscall std::vector<unsigned short>::_Insert_n(
        std::vector<unsigned short> *this,
        std::vector<unsigned short>::iterator _Where,
        unsigned int _Count,
        const unsigned __int16 *_Val)
{
  unsigned __int16 *Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  unsigned __int16 *v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  unsigned __int16 *v24; // ecx
  unsigned __int16 *v25; // edi
  unsigned __int16 *_Newvec; // [esp+8h] [ebp-4h]
  std::vector<unsigned short>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (const unsigned __int16 *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x7FFFFFFF - v8 < _Count )
      std::vector<unsigned short>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 1;
      v23 = 2 * _Count;
      _Wherea._Myptr = (unsigned __int16 *)(2 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (unsigned __int16 *)&Mylast[-v23];
        this->_Mylast = (unsigned __int16 *)std::vector<unsigned short>::_Ucopy<unsigned short *>(
                                              &Mylast[-v23],
                                              (int)Mylast,
                                              Mylast);
        std::copy_backward<unsigned short *,unsigned short *>(
          (unsigned __int8 *)_Where._Myptr,
          v25,
          (unsigned __int16 *)Mylast);
        std::fill<unsigned short *,unsigned short>(
          _Where._Myptr,
          (unsigned __int16 *)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (unsigned __int16 *)&_Val);
      }
      else
      {
        std::vector<unsigned short>::_Ucopy<unsigned short *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 2]);
        std::vector<unsigned short>::_Ufill(
          this,
          this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (const unsigned __int16 *)&_Val);
        v24 = (unsigned __int16 *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<unsigned short *,unsigned short>(
          _Where._Myptr,
          (unsigned __int16 *)((char *)v24 - (char *)_Wherea._Myptr),
          (unsigned __int16 *)&_Val);
      }
    }
    else
    {
      if ( 0x7FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(2 * v10));
      v14 = 2 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (unsigned __int16 *)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<unsigned short>::_Ufill(
                                 this,
                                 (unsigned __int16 *)(v14 + v15),
                                 _Count,
                                 (const unsigned __int16 *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 2 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 47D98C: variable 'v15' is possibly undefined

//----- (0047DA80) --------------------------------------------------------
char __thiscall CNPC::SortGoods(CNPC *this)
{
  std::_Sort<std::vector<unsigned short>::iterator,int>(
    (std::vector<unsigned short>::iterator)this->m_Goods._Myfirst,
    (std::vector<unsigned short>::iterator)this->m_Goods._Mylast,
    this->m_Goods._Mylast - this->m_Goods._Myfirst);
  return 1;
}

//----- (0047DAA0) --------------------------------------------------------
char __thiscall CNPC::SortQuests(CNPC *this)
{
  std::_Sort<std::vector<unsigned short>::iterator,int>(
    (std::vector<unsigned short>::iterator)this->m_Quests._Myfirst,
    (std::vector<unsigned short>::iterator)this->m_Quests._Mylast,
    this->m_Quests._Mylast - this->m_Quests._Myfirst);
  return 1;
}

//----- (0047DAC0) --------------------------------------------------------
void __thiscall Quest::TriggerNode::TriggerNode(
        Quest::TriggerNode *this,
        unsigned int dwTriggerKind,
        unsigned int dwTriggerID,
        unsigned int dwZoneID,
        unsigned int dwMaxCount,
        float fPosX,
        float fPosY,
        float fPosZ,
        float fDistance)
{
  this->m_dwTriggerKind = dwTriggerKind;
  this->m_dwTriggerID = dwTriggerID;
  this->m_dwZoneID = dwZoneID;
  this->m_dwMaxCount = dwMaxCount;
  this->m_fPosX = fPosX;
  this->m_fPosY = fPosY;
  this->m_fPosZ = fPosZ;
  this->m_fDistance = fDistance;
  this->m_lstEvent._Myfirst = 0;
  this->m_lstEvent._Mylast = 0;
  this->m_lstEvent._Myend = 0;
  this->m_lstFalseEvent._Myfirst = 0;
  this->m_lstFalseEvent._Mylast = 0;
  this->m_lstFalseEvent._Myend = 0;
}

//----- (0047DB40) --------------------------------------------------------
void __cdecl CNPC::AddPhase(unsigned int nZoneID, unsigned int nPhaseNumber)
{
  Quest::PhaseNode *v2; // eax
  Quest::PhaseNode *v3; // esi

  v2 = (Quest::PhaseNode *)operator new((tagHeader *)0x18);
  if ( v2 )
  {
    v2->m_dwZondID = nZoneID;
    v2->m_dwPhaseNumber = nPhaseNumber;
    v2->m_lstTrigger._Myfirst = 0;
    v2->m_lstTrigger._Mylast = 0;
    v2->m_lstTrigger._Myend = 0;
    v3 = v2;
  }
  else
  {
    v3 = 0;
  }
  if ( !CQuestMgr::AddPhase(CSingleton<CQuestMgr>::ms_pSingleton, v3) && v3 )
  {
    if ( v3->m_lstTrigger._Myfirst )
      operator delete(v3->m_lstTrigger._Myfirst);
    v3->m_lstTrigger._Myfirst = 0;
    v3->m_lstTrigger._Mylast = 0;
    v3->m_lstTrigger._Myend = 0;
    operator delete(v3);
  }
}

//----- (0047DBE0) --------------------------------------------------------
void __cdecl CNPC::TriggerStart()
{
  Quest::TriggerNode *v0; // eax
  Quest::TriggerNode *v1; // esi

  v0 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v0 )
  {
    v0->m_dwTriggerKind = 0;
    v0->m_dwTriggerID = 0;
    v0->m_dwZoneID = 0;
    v0->m_dwMaxCount = 1;
    v0->m_fPosX = 0.0;
    v0->m_fPosY = 0.0;
    v0->m_fPosZ = 0.0;
    v0->m_fDistance = 0.0;
    v0->m_lstEvent._Myfirst = 0;
    v0->m_lstEvent._Mylast = 0;
    v0->m_lstEvent._Myend = 0;
    v0->m_lstFalseEvent._Myfirst = 0;
    v0->m_lstFalseEvent._Mylast = 0;
    v0->m_lstFalseEvent._Myend = 0;
    v1 = v0;
  }
  else
  {
    v1 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v1) && v1 )
  {
    if ( v1->m_lstFalseEvent._Myfirst )
      operator delete(v1->m_lstFalseEvent._Myfirst);
    v1->m_lstFalseEvent._Myfirst = 0;
    v1->m_lstFalseEvent._Mylast = 0;
    v1->m_lstFalseEvent._Myend = 0;
    if ( v1->m_lstEvent._Myfirst )
      operator delete(v1->m_lstEvent._Myfirst);
    v1->m_lstEvent._Myfirst = 0;
    v1->m_lstEvent._Mylast = 0;
    v1->m_lstEvent._Myend = 0;
    operator delete(v1);
  }
}

//----- (0047DCB0) --------------------------------------------------------
void __cdecl CNPC::TriggerPuton(
        unsigned int nItemID,
        unsigned int nZoneID,
        float fPosX,
        float fPosY,
        float fPosZ,
        float fDistance)
{
  Quest::TriggerNode *v6; // eax
  Quest::TriggerNode *v7; // eax
  Quest::TriggerNode *v8; // esi

  v6 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v6 )
  {
    Quest::TriggerNode::TriggerNode(v6, 1u, nItemID, nZoneID, 1u, fDistance, fPosX, fPosY, fPosZ);
    v8 = v7;
  }
  else
  {
    v8 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v8) && v8 )
  {
    if ( v8->m_lstFalseEvent._Myfirst )
      operator delete(v8->m_lstFalseEvent._Myfirst);
    v8->m_lstFalseEvent._Myfirst = 0;
    v8->m_lstFalseEvent._Mylast = 0;
    v8->m_lstFalseEvent._Myend = 0;
    if ( v8->m_lstEvent._Myfirst )
      operator delete(v8->m_lstEvent._Myfirst);
    v8->m_lstEvent._Myfirst = 0;
    v8->m_lstEvent._Mylast = 0;
    v8->m_lstEvent._Myend = 0;
    operator delete(v8);
  }
}
// 47DD09: variable 'v7' is possibly undefined

//----- (0047DD80) --------------------------------------------------------
void __cdecl CNPC::TriggerGeton(
        unsigned int nItemID,
        unsigned int nZoneID,
        float fPosX,
        float fPosY,
        float fPosZ,
        float fDistance)
{
  Quest::TriggerNode *v6; // eax
  Quest::TriggerNode *v7; // eax
  Quest::TriggerNode *v8; // esi

  v6 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v6 )
  {
    Quest::TriggerNode::TriggerNode(v6, 2u, nItemID, nZoneID, 1u, fDistance, fPosX, fPosY, fPosZ);
    v8 = v7;
  }
  else
  {
    v8 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v8) && v8 )
  {
    if ( v8->m_lstFalseEvent._Myfirst )
      operator delete(v8->m_lstFalseEvent._Myfirst);
    v8->m_lstFalseEvent._Myfirst = 0;
    v8->m_lstFalseEvent._Mylast = 0;
    v8->m_lstFalseEvent._Myend = 0;
    if ( v8->m_lstEvent._Myfirst )
      operator delete(v8->m_lstEvent._Myfirst);
    v8->m_lstEvent._Myfirst = 0;
    v8->m_lstEvent._Mylast = 0;
    v8->m_lstEvent._Myend = 0;
    operator delete(v8);
  }
}
// 47DDD9: variable 'v7' is possibly undefined

//----- (0047DE50) --------------------------------------------------------
void __cdecl CNPC::TriggerTalk(unsigned int nNpcID)
{
  Quest::TriggerNode *v1; // eax
  Quest::TriggerNode *v2; // eax
  Quest::TriggerNode *v3; // esi

  v1 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v1 )
  {
    Quest::TriggerNode::TriggerNode(v1, 3u, nNpcID, 0, 1u, 0.0, 0.0, 0.0, 0.0);
    v3 = v2;
  }
  else
  {
    v3 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v3) && v3 )
  {
    if ( v3->m_lstFalseEvent._Myfirst )
      operator delete(v3->m_lstFalseEvent._Myfirst);
    v3->m_lstFalseEvent._Myfirst = 0;
    v3->m_lstFalseEvent._Mylast = 0;
    v3->m_lstFalseEvent._Myend = 0;
    if ( v3->m_lstEvent._Myfirst )
      operator delete(v3->m_lstEvent._Myfirst);
    v3->m_lstEvent._Myfirst = 0;
    v3->m_lstEvent._Mylast = 0;
    v3->m_lstEvent._Myend = 0;
    operator delete(v3);
  }
}
// 47DE95: variable 'v2' is possibly undefined

//----- (0047DF10) --------------------------------------------------------
void __cdecl CNPC::TriggerKill(unsigned int nCount, unsigned int nMonsterID)
{
  Quest::TriggerNode *v2; // eax
  Quest::TriggerNode *v3; // eax
  Quest::TriggerNode *v4; // esi

  v2 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v2 )
  {
    Quest::TriggerNode::TriggerNode(v2, 4u, nMonsterID, 0, nCount, 0.0, 0.0, 0.0, 0.0);
    v4 = v3;
  }
  else
  {
    v4 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v4) && v4 )
  {
    if ( v4->m_lstFalseEvent._Myfirst )
      operator delete(v4->m_lstFalseEvent._Myfirst);
    v4->m_lstFalseEvent._Myfirst = 0;
    v4->m_lstFalseEvent._Mylast = 0;
    v4->m_lstFalseEvent._Myend = 0;
    if ( v4->m_lstEvent._Myfirst )
      operator delete(v4->m_lstEvent._Myfirst);
    v4->m_lstEvent._Myfirst = 0;
    v4->m_lstEvent._Mylast = 0;
    v4->m_lstEvent._Myend = 0;
    operator delete(v4);
  }
}
// 47DF58: variable 'v3' is possibly undefined

//----- (0047DFD0) --------------------------------------------------------
void __cdecl CNPC::TriggerPick(unsigned int nCount, unsigned int nItemID)
{
  Quest::TriggerNode *v2; // eax
  Quest::TriggerNode *v3; // eax
  Quest::TriggerNode *v4; // esi

  v2 = (Quest::TriggerNode *)operator new((tagHeader *)0x40);
  if ( v2 )
  {
    Quest::TriggerNode::TriggerNode(v2, 5u, nItemID, 0, nCount, 0.0, 0.0, 0.0, 0.0);
    v4 = v3;
  }
  else
  {
    v4 = 0;
  }
  if ( !CQuestMgr::AddTrigger(CSingleton<CQuestMgr>::ms_pSingleton, v4) && v4 )
  {
    if ( v4->m_lstFalseEvent._Myfirst )
      operator delete(v4->m_lstFalseEvent._Myfirst);
    v4->m_lstFalseEvent._Myfirst = 0;
    v4->m_lstFalseEvent._Mylast = 0;
    v4->m_lstFalseEvent._Myend = 0;
    if ( v4->m_lstEvent._Myfirst )
      operator delete(v4->m_lstEvent._Myfirst);
    v4->m_lstEvent._Myfirst = 0;
    v4->m_lstEvent._Mylast = 0;
    v4->m_lstEvent._Myend = 0;
    operator delete(v4);
  }
}
// 47E018: variable 'v3' is possibly undefined

//----- (0047E090) --------------------------------------------------------
void __thiscall CNPC::CNPC(
        CNPC *this,
        unsigned int dwCID,
        int nZone,
        int nJob,
        bool bBindable,
        bool bRepairable,
        bool bBlackMarketeer,
        bool bMustSellWithMileage)
{
  this->__vftable = (CNPC_vtbl *)&CCreature::`vftable';
  this->m_CurrentPos.m_fPointX = 0.0;
  this->m_CurrentPos.m_fPointY = 0.0;
  this->m_CurrentPos.m_fPointZ = 0.0;
  MotionInfo::MotionInfo(&this->m_MotionInfo);
  this->m_dwCID = dwCID;
  this->m_nZone = nZone;
  this->m_nJob = nJob;
  this->m_bBindable = bBindable;
  this->__vftable = (CNPC_vtbl *)&CNPC::`vftable';
  this->m_bRepairable = bRepairable;
  this->m_bBlackMarketeer = bBlackMarketeer;
  this->m_bMustSellWithMileage = bMustSellWithMileage;
  this->m_Goods._Myfirst = 0;
  this->m_Goods._Mylast = 0;
  this->m_Goods._Myend = 0;
  this->m_Quests._Myfirst = 0;
  this->m_Quests._Mylast = 0;
  this->m_Quests._Myend = 0;
  this->m_cAttackableCreatureType = 12;
}
// 4DABA8: using guessed type void *CCreature::`vftable';
// 4EFC58: using guessed type void *CNPC::`vftable';

//----- (0047E130) --------------------------------------------------------
void __thiscall CNPC::AddGoodsToNPC(CNPC *this, unsigned __int16 wKindItem)
{
  std::vector<unsigned short> *p_m_Goods; // ecx
  unsigned __int16 *Myfirst; // esi
  unsigned int v4; // edx
  unsigned __int16 *Mylast; // eax

  p_m_Goods = &this->m_Goods;
  Myfirst = p_m_Goods->_Myfirst;
  if ( Myfirst )
    v4 = p_m_Goods->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < p_m_Goods->_Myend - Myfirst )
  {
    Mylast = p_m_Goods->_Mylast;
    *Mylast = wKindItem;
    p_m_Goods->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<unsigned short>::_Insert_n(
      p_m_Goods,
      (std::vector<unsigned short>::iterator)p_m_Goods->_Mylast,
      1u,
      &wKindItem);
  }
}

//----- (0047E180) --------------------------------------------------------
void __thiscall CNPC::AddQuestsToNPC(CNPC *this, unsigned __int16 wQuestID)
{
  std::vector<unsigned short> *p_m_Quests; // ecx
  unsigned __int16 *Myfirst; // esi
  unsigned int v4; // edx
  unsigned __int16 *Mylast; // eax

  p_m_Quests = &this->m_Quests;
  Myfirst = p_m_Quests->_Myfirst;
  if ( Myfirst )
    v4 = p_m_Quests->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < p_m_Quests->_Myend - Myfirst )
  {
    Mylast = p_m_Quests->_Mylast;
    *Mylast = wQuestID;
    p_m_Quests->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<unsigned short>::_Insert_n(
      p_m_Quests,
      (std::vector<unsigned short>::iterator)p_m_Quests->_Mylast,
      1u,
      &wQuestID);
  }
}

//----- (0047E1D0) --------------------------------------------------------
void __cdecl CNPC::SetNPC(int nZone, unsigned int nUID, int nJob)
{
  CNPC *v3; // eax
  CNPC *v4; // eax
  CNPC *v5; // esi
  CCreatureManager *Instance; // eax
  bool bMustSellWithMileage; // [esp+10h] [ebp-1Ch]
  bool bBlackMarketeer; // [esp+14h] [ebp-18h]
  bool bRepareable; // [esp+18h] [ebp-14h]
  bool bBindable; // [esp+1Ch] [ebp-10h]

  bBindable = 0;
  bRepareable = 0;
  bBlackMarketeer = 0;
  bMustSellWithMileage = 0;
  switch ( nJob )
  {
    case 1:
    case 2:
    case 26:
    case 41:
    case 51:
    case 52:
    case 76:
    case 91:
      bBindable = 1;
      break;
    default:
      break;
  }
  switch ( nJob )
  {
    case 11:
    case 12:
    case 13:
    case 14:
    case 16:
    case 17:
    case 18:
    case 21:
    case 27:
    case 31:
    case 32:
    case 33:
    case 34:
    case 35:
    case 42:
    case 61:
    case 62:
    case 63:
    case 66:
    case 67:
    case 68:
    case 71:
    case 77:
    case 81:
    case 82:
    case 83:
    case 92:
      bRepareable = 1;
      break;
    default:
      break;
  }
  switch ( nJob )
  {
    case 16:
    case 17:
    case 18:
    case 66:
    case 67:
    case 68:
      bBlackMarketeer = 1;
      break;
    default:
      break;
  }
  if ( nJob == 27 || nJob == 77 )
    bMustSellWithMileage = 1;
  v3 = (CNPC *)operator new((tagHeader *)0x54);
  if ( v3 )
  {
    CNPC::CNPC(v3, nUID, nZone, nJob, bBindable, bRepareable, bBlackMarketeer, bMustSellWithMileage);
    v5 = v4;
  }
  else
  {
    v5 = 0;
  }
  Instance = CCreatureManager::GetInstance();
  if ( !CCreatureManager::AddCreature(Instance, v5) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CNPC::SetNPC", aDWorkRylSource_22, 175, aNid0x08xNpc, nUID, nJob);
    if ( v5 )
      ((void (__thiscall *)(CNPC *, int))v5->~CNPC)(v5, 1);
  }
}
// 47E29E: variable 'v4' is possibly undefined

//----- (0047E410) --------------------------------------------------------
void __cdecl CNPC::AddItem(unsigned int nUID, unsigned __int16 nKindItem)
{
  CMsgProcessMgr *Instance; // eax
  CNPC *Castle; // eax

  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = (CNPC *)Castle::CCastleMgr::GetCastle(Instance, nUID);
  if ( Castle )
    CNPC::AddGoodsToNPC(Castle, nKindItem);
}

//----- (0047E440) --------------------------------------------------------
void __cdecl CNPC::AddQuest(
        unsigned int nUID,
        unsigned __int16 nQuestID,
        unsigned __int16 nMinLevel,
        unsigned __int16 nMaxLevel,
        unsigned int nClass,
        unsigned int nCompleteClass,
        char *strQuestFunc)
{
  Quest::QuestNode *v7; // eax
  Quest::QuestNode *v8; // esi
  Quest::PhaseNode **Myfirst; // ecx
  int v10; // eax
  CMsgProcessMgr *Instance; // eax
  CNPC *Castle; // eax
  char v13[4]; // [esp+10h] [ebp-18h] BYREF
  ScriptFunc Func_Quest; // [esp+14h] [ebp-14h] BYREF
  int v15; // [esp+24h] [ebp-4h]

  v7 = (Quest::QuestNode *)operator new((tagHeader *)0x24);
  if ( v7 )
  {
    v7->m_wMinLevel = nMinLevel;
    v7->m_wMaxLevel = nMaxLevel;
    *(_DWORD *)&v7->m_wQuestID = nQuestID;
    v7->m_dwClass = nClass;
    v7->m_dwCompletedQuest = nCompleteClass;
    v7->m_bSave = 0;
    v7->m_lstPhase._Myfirst = 0;
    v7->m_lstPhase._Mylast = 0;
    v7->m_lstPhase._Myend = 0;
    v8 = v7;
  }
  else
  {
    v8 = 0;
  }
  v15 = -1;
  CQuestMgr::AddQuest(CSingleton<CQuestMgr>::ms_pSingleton, v8);
  _SE_GetScriptFunction((int *)&Func_Quest, (CVirtualMachine *)CNPC::ms_scQuestScript.pFunc, 0, strQuestFunc, 0);
  _SE_CallScriptFunction(
    (struct CVirtualMachine *)v13,
    (struct ScriptFunc)__PAIR64__((unsigned int)Func_Quest.pFunc, (unsigned int)CNPC::ms_scQuestScript.pFunc),
    Func_Quest.Type);
  Myfirst = v8->m_lstPhase._Myfirst;
  if ( Myfirst )
    v10 = v8->m_lstPhase._Mylast - Myfirst;
  else
    LOWORD(v10) = 0;
  v8->m_wMaxPhase = v10;
  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = (CNPC *)Castle::CCastleMgr::GetCastle(Instance, nUID);
  if ( Castle )
    CNPC::AddQuestsToNPC(Castle, nQuestID);
}

//----- (0047E540) --------------------------------------------------------
char __cdecl CNPC::LoadNPCInfo()
{
  CCreatureManager *Instance; // eax
  struct CVirtualMachine *v1; // eax
  struct CVirtualMachine *v3; // eax
  struct CVirtualMachine *v4; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v5; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v6; // eax
  CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > _Func; // [esp+0h] [ebp-8h] BYREF
  CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > result; // [esp+4h] [ebp-4h] BYREF

  Instance = CCreatureManager::GetInstance();
  CCreatureManager::DestoryNPCList(Instance);
  _SE_SetMessageFunction(CNPC::ScriptErrorMessage);
  v1 = _SE_Create((char *)szQuestScriptFileName);
  CNPC::ms_scQuestScript.pFunc = v1;
  if ( !v1 )
    return 0;
  _SE_RegisterFunction(v1, (char *)CSymbolTable::Create, T_VOID, "QuestTitle", 4, 0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "QuestDesc",
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "QuestShortDesc",
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "QuestIcon",
    4,
    2,
    2,
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::QuestCompleteSave,
    T_VOID,
    "QuestCompleteSave",
    1,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "QuestLevel",
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "QuestAward",
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::AddPhase,
    T_VOID,
    "AddPhase",
    2,
    2,
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CSymbolTable::Create,
    T_VOID,
    "Phase_Target",
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerStart,
    T_VOID,
    "Trigger_Start",
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerPuton,
    T_VOID,
    "Trigger_Puton",
    2,
    2,
    3,
    3,
    3,
    3,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerGeton,
    T_VOID,
    "Trigger_Geton",
    2,
    2,
    3,
    3,
    3,
    3,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerTalk,
    T_VOID,
    "Trigger_Talk",
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerKill,
    T_VOID,
    "Trigger_Kill",
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::TriggerPick,
    T_VOID,
    "Trigger_Pick",
    2,
    2,
    0);
  _SE_RegisterFunction((struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc, (char *)CNPC::Else, T_VOID, "Else", 0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventDisappear,
    T_VOID,
    "Event_Disappear",
    2,
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventGet,
    T_VOID,
    "Event_Get",
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventSpawn,
    T_VOID,
    "Event_Spawn",
    2,
    3,
    3,
    3,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventMonsterDrop,
    T_VOID,
    "Event_MonsterDrop",
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventAward,
    T_VOID,
    "Event_Award",
    2,
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventMsgBox,
    T_VOID,
    "Event_MsgBox",
    4,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventPhase,
    T_VOID,
    "Event_Phase",
    2,
    0);
  _SE_RegisterFunction(
    (struct CVirtualMachine *)CNPC::ms_scQuestScript.pFunc,
    (char *)CNPC::EventEnd,
    T_VOID,
    "Event_End",
    0,
    0);
  v3 = _SE_Create((char *)szNPCScriptFileName);
  v4 = v3;
  if ( !v3 )
    return 0;
  _SE_RegisterFunction(v3, (char *)CNPC::SetNPC, T_VOID, "SetNPC", 2, 2, 2, 4, 4, 0);
  _SE_RegisterFunction(v4, (char *)CNPC::SetPosition, T_VOID, "SetPosition", 2, 3, 3, 3, 3, 0);
  _SE_RegisterFunction(v4, (char *)CSymbolTable::Create, T_VOID, "AddWords", 2, 4, 0);
  _SE_RegisterFunction(v4, (char *)CSymbolTable::Create, T_VOID, "AddDialog", 2, 2, 2, 4, 0);
  _SE_RegisterFunction(v4, (char *)CNPC::AddItem, T_VOID, "AddItem", 2, 2, 2, 2, 0);
  _SE_RegisterFunction(v4, (char *)CSymbolTable::Create, T_VOID, "AddZoneMove", 2, 2, 3, 3, 3, 0);
  _SE_RegisterFunction(v4, (char *)CNPC::AddQuest, T_VOID, "AddQuest", 2, 2, 2, 2, 2, 2, 4, 0);
  _SE_Execute(v4);
  _SE_Destroy(v4);
  _Func.m_fnSecondProcess = (std::mem_fun_t<bool,CNPC> *)CNPC::SortGoods;
  v5 = (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)CCreatureManager::GetInstance();
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const,CNPC *>>>(
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v5[1]._Ptr->_Left,
    v5[1],
    (CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> >)&_Func);
  _Func.m_fnSecondProcess = (std::mem_fun_t<bool,CNPC> *)CNPC::SortQuests;
  v6 = (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)CCreatureManager::GetInstance();
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const,CNPC *>>>(
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v6[1]._Ptr->_Left,
    v6[1],
    (CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> >)&_Func);
  CQuestMgr::SortQuests(CSingleton<CQuestMgr>::ms_pSingleton);
  return 1;
}

//----- (0047E9B0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharTakeItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        TakeType takeType,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  char *v5; // eax
  unsigned __int8 m_cNum; // cl

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  if ( usError )
  {
    m_cNum = 0;
    *((_DWORD *)Buffer + 3) = 0;
    v5 = Buffer + 16;
    *(_DWORD *)v5 = 0;
  }
  else
  {
    *((_DWORD *)Buffer + 3) = dwCharID;
    v5 = Buffer + 16;
    *(_DWORD *)v5 = *(_DWORD *)&takeType.m_srcPos;
    m_cNum = takeType.m_cNum;
  }
  v5[4] = m_cNum;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x11u, 0, usError);
}

//----- (0047EA30) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSwapItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        TakeType takeSrc,
        TakeType takeDst,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  char *v6; // eax
  unsigned __int8 m_cNum; // cl

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1A);
  if ( !Buffer )
    return 0;
  if ( usError )
  {
    *((_DWORD *)Buffer + 4) = 0;
    Buffer[20] = 0;
    m_cNum = 0;
    *((_DWORD *)Buffer + 3) = 0;
    v6 = Buffer + 21;
    *(_DWORD *)v6 = 0;
  }
  else
  {
    *((_DWORD *)Buffer + 3) = dwCharID;
    *(TakeType *)(Buffer + 16) = takeSrc;
    v6 = Buffer + 21;
    *(_DWORD *)v6 = *(_DWORD *)&takeDst.m_srcPos;
    m_cNum = takeDst.m_cNum;
  }
  v6[4] = m_cNum;
  return CSendStream::WrapCrypt(SendStream, 0x1Au, 0x12u, 0, usError);
}

//----- (0047EAE0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharRepairItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned int dwRepairGold,
        Item::ItemPos itemPos,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x16);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwRepairGold;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((Item::ItemPos *)Buffer + 10) = itemPos;
  return CSendStream::WrapCrypt(SendStream, 0x16u, 0x13u, 0, usError);
}

//----- (0047EB30) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharRepairAllItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned int dwRepairGold,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x14);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((_DWORD *)Buffer + 4) = dwRepairGold;
  return CSendStream::WrapCrypt(SendStream, 0x14u, 0x9Fu, 0, usError);
}

//----- (0047EB70) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharUseItem(
        CSendStream *SendStream,
        unsigned int dwSender,
        unsigned int dwReceiver,
        Item::ItemPos itemPos,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x16);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwReceiver;
  *((_DWORD *)Buffer + 3) = dwSender;
  *((Item::ItemPos *)Buffer + 10) = itemPos;
  return CSendStream::WrapCrypt(SendStream, 0x16u, 0x14u, 0, usError);
}

//----- (0047EBC0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharTradeItem(
        CSendStream *SendStream,
        CCharacter *lpCharacter,
        unsigned int dwNPCID,
        Item::CItem *lpItem,
        Item::ItemPos itemPos,
        unsigned __int8 cNum,
        unsigned __int16 usError)
{
  unsigned int v7; // ecx
  char *Buffer; // esi
  __int16 v9; // ax
  unsigned int m_dwCID; // eax
  Item::CItem_vtbl *v11; // edx
  unsigned int nItemSize; // [esp+0h] [ebp-4h] BYREF

  nItemSize = v7;
  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x55);
  if ( !Buffer )
    return 0;
  v9 = 0;
  nItemSize = 0;
  if ( usError )
  {
    *((_DWORD *)Buffer + 3) = 0;
    *((_DWORD *)Buffer + 4) = 0;
    *((_DWORD *)Buffer + 5) = 0;
    *((_WORD *)Buffer + 14) = 0;
    *((_WORD *)Buffer + 15) = 0;
    Buffer[32] = 0;
  }
  else
  {
    m_dwCID = lpCharacter->m_dwCID;
    *((_DWORD *)Buffer + 4) = dwNPCID;
    *((_DWORD *)Buffer + 3) = m_dwCID;
    *((_DWORD *)Buffer + 5) = lpCharacter->m_DBData.m_Info.Gold;
    *((_DWORD *)Buffer + 6) = lpCharacter->m_DBData.m_Info.Mileage;
    if ( lpItem )
    {
      v11 = lpItem->__vftable;
      nItemSize = 52;
      if ( !v11->SerializeOut(lpItem, Buffer + 33, &nItemSize) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientSendPacket::SendCharTradeItem",
          aDWorkRylSource_39,
          143,
          aCid0x08x_196,
          lpCharacter->m_dwCID);
    }
    else
    {
      nItemSize = 0;
    }
    v9 = nItemSize;
    *((_WORD *)Buffer + 14) = nItemSize;
    *((Item::ItemPos *)Buffer + 15) = itemPos;
    Buffer[32] = cNum;
  }
  return CSendStream::WrapCrypt(SendStream, v9 + 33, 0x15u, 0, usError);
}
// 47EBC0: variable 'v7' is possibly undefined

//----- (0047ECB0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPickUp(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned __int64 nObjectID,
        unsigned int dwGold,
        Item::CItem *lpItem,
        Item::ItemPos dstPos,
        unsigned __int8 cNum,
        unsigned __int16 usError)
{
  unsigned int v8; // ecx
  char *Buffer; // esi
  unsigned int v10; // eax
  Item::CItem_vtbl *v11; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-4h] BYREF

  nItemSize = v8;
  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x54);
  if ( !Buffer )
    return 0;
  LOWORD(v10) = 0;
  nItemSize = 0;
  Buffer[30] = 0;
  *((_DWORD *)Buffer + 6) = 0;
  if ( !usError )
  {
    if ( dwGold )
    {
      Buffer[30] = 1;
      *((_DWORD *)Buffer + 6) = dwGold;
    }
    else if ( lpItem )
    {
      v11 = lpItem->__vftable;
      nItemSize = 52;
      if ( !v11->SerializeOut(lpItem, Buffer + 32, &nItemSize) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientSendPacket::SendCharPickUp",
          aDWorkRylSource_39,
          199,
          aCid0x08x_196,
          dwCID);
      v10 = nItemSize;
      Buffer[30] = 0;
      *((_DWORD *)Buffer + 6) = v10;
    }
  }
  *(_QWORD *)(Buffer + 12) = nObjectID;
  *((Item::ItemPos *)Buffer + 14) = dstPos;
  *((_DWORD *)Buffer + 5) = dwCID;
  Buffer[31] = cNum;
  return CSendStream::WrapCrypt(SendStream, v10 + 32, 0x34u, 0, usError);
}
// 47ECB0: variable 'v8' is possibly undefined

//----- (0047ED90) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharAutoRouting(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned __int64 nObjectID,
        unsigned __int16 wItemID,
        unsigned __int8 cNum,
        unsigned __int8 cCmd)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 5) = dwCID;
  *(_QWORD *)(Buffer + 12) = nObjectID;
  *((_WORD *)Buffer + 12) = wItemID;
  Buffer[26] = cNum;
  Buffer[29] = cCmd;
  return CSendStream::WrapCrypt(SendStream, 0x1Eu, 0x72u, 0, 0);
}

//----- (0047EDF0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPullDown(
        CSendStream *SendStream,
        unsigned int dwCID,
        Item::ItemPos itemPos,
        CCell::ItemInfo *lpItemInfo,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  char *v6; // esi
  Item::CItem *lpItem; // eax
  int m_usProtoTypeID; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x25);
  v6 = Buffer;
  if ( !Buffer )
    return 0;
  *((_QWORD *)Buffer + 2) = lpItemInfo->UID.nUniqueID;
  Buffer[34] = lpItemInfo->cNum;
  lpItem = lpItemInfo->lpItem;
  if ( lpItem )
    m_usProtoTypeID = lpItem->m_ItemData.m_usProtoTypeID;
  else
    m_usProtoTypeID = 0;
  *((_DWORD *)v6 + 6) = m_usProtoTypeID;
  *((_WORD *)v6 + 14) = (unsigned __int64)lpItemInfo->Pos.m_fPointX;
  *((_WORD *)v6 + 15) = (unsigned __int64)lpItemInfo->Pos.m_fPointY;
  *((_WORD *)v6 + 16) = (unsigned __int64)lpItemInfo->Pos.m_fPointZ;
  if ( usError )
  {
    *((_DWORD *)v6 + 3) = 0;
    *(_WORD *)(v6 + 35) = 0;
    return CSendStream::WrapCrypt(SendStream, 0x25u, 0x35u, 0, usError);
  }
  else
  {
    *((_DWORD *)v6 + 3) = dwCID;
    *(Item::ItemPos *)(v6 + 35) = itemPos;
    return CSendStream::WrapCrypt(SendStream, 0x25u, 0x35u, 0, 0);
  }
}

//----- (0047EEB0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharDisappearItem(
        CSendStream *SendStream,
        unsigned int dwCID,
        Item::ItemPos itemPos,
        unsigned __int8 cNum,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x13);
  if ( !Buffer )
    return 0;
  *((Item::ItemPos *)Buffer + 8) = itemPos;
  *((_DWORD *)Buffer + 3) = dwCID;
  Buffer[18] = cNum;
  return CSendStream::WrapCrypt(SendStream, 0x13u, 0x6Eu, 0, usError);
}

//----- (0047EF00) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSplitItem(
        CSendStream *SendStream,
        unsigned int dwCID,
        Item::CItem *lpSplitItem,
        TakeType takeType,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  Item::CItem_vtbl *v6; // eax
  __int16 v7; // ax
  unsigned int nItemSize[3]; // [esp+8h] [ebp-Ch] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4A);
  if ( !Buffer )
    return 0;
  nItemSize[0] = 0;
  if ( lpSplitItem )
  {
    v6 = lpSplitItem->__vftable;
    nItemSize[0] = 52;
    if ( !v6->SerializeOut(lpSplitItem, Buffer + 22, nItemSize) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientSendPacket::SendCharSplitItem",
        aDWorkRylSource_39,
        314,
        aCid0x08x_196,
        dwCID);
  }
  if ( usError )
  {
    *(unsigned int *)((char *)&nItemSize[1] + 3) = 0;
    *((_DWORD *)Buffer + 4) = 0;
    *((_DWORD *)Buffer + 3) = 0;
    Buffer[20] = 0;
  }
  else
  {
    *((_DWORD *)Buffer + 4) = *(_DWORD *)&takeType.m_srcPos;
    *((_DWORD *)Buffer + 3) = dwCID;
    Buffer[20] = takeType.m_cNum;
  }
  v7 = nItemSize[0];
  Buffer[21] = nItemSize[0];
  return CSendStream::WrapCrypt(SendStream, v7 + 22, 0x3Fu, 0, usError);
}

//----- (0047EFE0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharTakeGold(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwGold,
        unsigned __int8 cSrcPos,
        unsigned __int8 cDstPos,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  if ( usError )
  {
    *((_DWORD *)Buffer + 3) = 0;
    *((_DWORD *)Buffer + 4) = 0;
    Buffer[20] = 0;
    return CSendStream::WrapCrypt(SendStream, 0x15u, 0x47u, 0, usError);
  }
  else
  {
    *((_DWORD *)Buffer + 3) = dwCID;
    *((_DWORD *)Buffer + 4) = dwGold;
    Buffer[20] = (16 * cDstPos) | cSrcPos & 0xF;
    return CSendStream::WrapCrypt(SendStream, 0x15u, 0x47u, 0, 0);
  }
}

//----- (0047F050) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharExchangeItem(
        CSendStream *SendStream,
        unsigned int dwOwnerCID,
        unsigned int dwGold,
        Item::CItem *lpItem,
        Item::ItemPos itemPos,
        bool bStack,
        bool bRemove)
{
  char *Buffer; // esi
  unsigned __int16 v8; // di
  __int16 v9; // ax
  Item::CItem_vtbl *v10; // edx
  unsigned int nItemSize; // [esp+Ch] [ebp-4h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4C);
  v8 = 0;
  if ( !Buffer )
    return 0;
  v9 = 0;
  nItemSize = 0;
  if ( lpItem )
  {
    if ( bStack )
    {
      *((_DWORD *)Buffer + 4) = 0;
      Buffer[23] = lpItem->m_ItemData.m_cNumOrDurability;
    }
    else
    {
      v10 = lpItem->__vftable;
      nItemSize = 52;
      if ( !v10->SerializeOut(lpItem, Buffer + 24, &nItemSize) )
      {
        v8 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientSendPacket::SendCharExchangeItem",
          aDWorkRylSource_39,
          394,
          aCid0x08x_196,
          dwOwnerCID);
      }
      v9 = nItemSize;
      *((_DWORD *)Buffer + 4) = nItemSize;
    }
    Buffer[22] = !bRemove ? 0 : 2;
  }
  else
  {
    Buffer[22] = 1;
    *((_DWORD *)Buffer + 4) = dwGold;
  }
  *((Item::ItemPos *)Buffer + 10) = itemPos;
  *((_DWORD *)Buffer + 3) = dwOwnerCID;
  return CSendStream::WrapCrypt(SendStream, v9 + 24, 0x48u, 0, v8);
}

//----- (0047F130) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharDepositCmd(
        CSendStream *SendStream,
        unsigned __int8 cCmd,
        char *szData,
        unsigned int nDataLength,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  unsigned int v6; // ecx

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1B);
  if ( !Buffer )
    return 0;
  Buffer[18] = cCmd;
  v6 = nDataLength;
  if ( nDataLength > 8 )
    v6 = 8;
  qmemcpy(Buffer + 19, szData, v6);
  return CSendStream::WrapCrypt(SendStream, 0x1Bu, 0x5Cu, 0, usError);
}

//----- (0047F190) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharDepositPasswordToDBAgent(
        CSendStream *SendStream,
        unsigned int dwUID,
        const char *szPassword,
        unsigned int nPasswordLen)
{
  char *Buffer; // eax
  unsigned int v5; // ecx

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1B);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 19) = dwUID;
  v5 = nPasswordLen;
  Buffer[18] = 7;
  if ( nPasswordLen > 4 )
    v5 = 4;
  qmemcpy(Buffer + 23, szPassword, v5);
  return CSendStream::WrapHeader(SendStream, 0x1Bu, 0x5Cu, 0, 0);
}

//----- (0047F1F0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharDepositGoldToDBAgent(
        CSendStream *SendStream,
        unsigned int dwUID,
        unsigned int dwGold)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1B);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 19) = dwUID;
  Buffer[18] = 8;
  *(_DWORD *)(Buffer + 23) = dwGold;
  return CSendStream::WrapHeader(SendStream, 0x1Bu, 0x5Cu, 0, 0);
}

//----- (0047F230) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharLotteryResult(
        CSendStream *SendStream,
        unsigned int dwCID,
        Item::ItemPos itemPos,
        Item::CItem *lpItem,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  Item::CItem_vtbl *v7; // edx
  unsigned int nItemSize; // [esp+8h] [ebp-4h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4A);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((Item::ItemPos *)Buffer + 10) = itemPos;
  nItemSize = 0;
  if ( lpItem )
  {
    v7 = lpItem->__vftable;
    nItemSize = 52;
    if ( !v7->SerializeOut(lpItem, Buffer + 22, &nItemSize) )
    {
      usError = 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientSendPacket::SendCharLotteryResult",
        aDWorkRylSource_39,
        499,
        aCid0x08x_196,
        dwCID);
    }
    *((_DWORD *)Buffer + 4) = nItemSize;
  }
  return CSendStream::WrapHeader(SendStream, nItemSize + 22, 0x7Bu, 0, usError);
}

//----- (0047F2E0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCastleInfo(CSendStream *SendStream)
{
  Castle::CCastleMgr *Instance; // eax

  Instance = Castle::CCastleMgr::GetInstance();
  return Castle::CCastleMgr::SendCastleInfo(Instance, SendStream);
}

//----- (0047F300) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSiegeTimeInfo(CSendStream *SendStream)
{
  Castle::CCastleMgr *Instance; // eax

  Instance = Castle::CCastleMgr::GetInstance();
  return Castle::CCastleMgr::SendSiegeTimeInfo(Instance, SendStream);
}

//----- (0047F320) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCampInfo(CSendStream *SendStream)
{
  CSiegeObjectMgr *Instance; // eax

  Instance = CSiegeObjectMgr::GetInstance();
  return CSiegeObjectMgr::SendCampInfo(Instance, SendStream);
}

//----- (0047F340) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCastleCmd(
        CSendStream *SendStream,
        unsigned int dwCastleID,
        unsigned int dwCastleObjectID,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x22);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 5) = dwCastleObjectID;
  *((_DWORD *)Buffer + 4) = dwCastleID;
  Buffer[33] = cSubCmd;
  return CSendStream::WrapCrypt(SendStream, 0x22u, 0xAAu, 0, wError);
}

//----- (0047F390) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCastleRight(
        CSendStream *SendStream,
        unsigned int dwCasltID,
        const CastleRight *castleRight,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwCasltID;
  *((_DWORD *)Buffer + 3) = 0;
  *((CastleRight *)Buffer + 2) = *castleRight;
  return CSendStream::WrapCrypt(SendStream, 0x1Eu, 0xADu, 0, wError);
}

//----- (0047F3F0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCampRight(
        CSendStream *SendStream,
        unsigned int dwCampID,
        const CampRight *campRight,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwCampID;
  *((_DWORD *)Buffer + 3) = 0;
  *((CampRight *)Buffer + 2) = *campRight;
  return CSendStream::WrapCrypt(SendStream, 0x1Eu, 0xAEu, 0, wError);
}

//----- (0047F450) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCampCmd(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwCampID,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1B);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwCampID;
  *((_DWORD *)Buffer + 3) = dwCID;
  Buffer[26] = cSubCmd;
  return CSendStream::WrapCrypt(SendStream, 0x1Bu, 0xABu, 0, wError);
}

//----- (0047F4A0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSiegeArmsCmd(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwArmsID,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwArmsID;
  *((_DWORD *)Buffer + 3) = dwCID;
  Buffer[29] = cSubCmd;
  return CSendStream::WrapCrypt(SendStream, 0x1Eu, 0xACu, 0, wError);
}

//----- (0047F4F0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCreateCamp(
        CSendStream *AgentSendStream,
        unsigned int dwGID,
        const Position *pos)
{
  char *Buffer; // esi
  CServerSetup *Instance; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x2B);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = 0;
  *((_DWORD *)Buffer + 5) = dwGID;
  *((_DWORD *)Buffer + 4) = 0;
  *((_DWORD *)Buffer + 6) = 0;
  Buffer[29] = 0;
  Buffer[30] = 0;
  Instance = CServerSetup::GetInstance();
  Buffer[28] = CServerSetup::GetServerZone(Instance);
  *(Position *)(Buffer + 31) = *pos;
  return CSendStream::WrapHeader(AgentSendStream, 0x2Bu, 0xA8u, 0, 0);
}

//----- (0047F560) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCreateSiegeArms(
        CSendStream *AgentSendStream,
        unsigned int dwOwnerID,
        unsigned int dwGID,
        unsigned __int16 wType,
        unsigned __int8 cUpgradeStep,
        const Position *pos)
{
  char *Buffer; // esi
  CServerSetup *Instance; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x2D);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwOwnerID;
  *((_DWORD *)Buffer + 3) = 0;
  *((_WORD *)Buffer + 10) = wType;
  *(_DWORD *)(Buffer + 22) = dwGID;
  *(_DWORD *)(Buffer + 26) = 0;
  Buffer[31] = 0;
  Buffer[32] = cUpgradeStep;
  Instance = CServerSetup::GetInstance();
  Buffer[30] = CServerSetup::GetServerZone(Instance);
  *(Position *)(Buffer + 33) = *pos;
  return CSendStream::WrapHeader(AgentSendStream, 0x2Du, 0xA9u, 0, 0);
}



//----- (0047F5F0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCastleCmdToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwCastleID,
        unsigned int dwCastleObjectID,
        unsigned int dwHP,
        unsigned int dwValue,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x22);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 5) = dwCastleObjectID;
  *((_DWORD *)Buffer + 4) = dwCastleID;
  *((_DWORD *)Buffer + 7) = dwValue;
  *((_DWORD *)Buffer + 6) = dwHP;
  Buffer[33] = cSubCmd;
  return CSendStream::WrapHeader(AgentSendStream, 0x22u, 0xAAu, 0, wError);
}

//----- (0047F640) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCampCmdToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwCID,
        unsigned int dwCampID,
        unsigned int dwHP,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x1B);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_DWORD *)Buffer + 4) = dwCampID;
  *((_DWORD *)Buffer + 5) = dwHP;
  Buffer[26] = cSubCmd;
  return CSendStream::WrapHeader(AgentSendStream, 0x1Bu, 0xABu, 0, wError);
}

//----- (0047F690) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwCID,
        unsigned int dwArmsID,
        unsigned int dwValue,
        unsigned __int8 cSubCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_DWORD *)Buffer + 4) = dwArmsID;
  *(_DWORD *)(Buffer + 25) = dwValue;
  Buffer[29] = cSubCmd;
  return CSendStream::WrapHeader(AgentSendStream, 0x1Eu, 0xACu, 0, wError);
}

//----- (0047F6E0) --------------------------------------------------------
CCreature::MutualType __thiscall CSiegeObject::IsEnemy(CSiegeObject *this, CAggresiveCreature *lpTarget)
{
  CCreature::MutualType result; // eax
  CCreatureManager *Instance; // eax
  CCharacter *Character; // ebx
  unsigned int m_dwCID; // eax
  CCreatureManager *v8; // eax
  unsigned __int8 IsAttackable; // al
  int v10; // ebp
  CParty *v11; // ebp
  unsigned __int8 CreatureType; // al
  unsigned int m_dwGID; // ebp
  unsigned __int8 v14; // bl
  bool v15; // zf
  unsigned __int8 v16; // [esp-8h] [ebp-1Ch]
  unsigned int v17; // [esp-4h] [ebp-18h]
  unsigned __int8 m_cAttackableCreatureType; // [esp-4h] [ebp-18h]
  CCellManager *lpTargeta; // [esp+18h] [ebp+4h]

  if ( !lpTarget )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::IsEnemy",
      aDWorkRylSource_42,
      153,
      aCid0x08x_121,
      this->m_dwCID);
    return 1;
  }
  v17 = this->m_dwRideCID[0];
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v17);
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::IsEnemy",
      aDWorkRylSource_42,
      160,
      aCid0x08x_60,
      this->m_dwCID,
      this->m_dwRideCID[0]);
    return 1;
  }
  m_dwCID = lpTarget->m_dwCID;
  if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
  {
    m_cAttackableCreatureType = lpTarget->m_cAttackableCreatureType;
    v16 = this->m_cAttackableCreatureType;
    v8 = CCreatureManager::GetInstance();
    IsAttackable = CCreatureManager::IsAttackable(v8, v16, m_cAttackableCreatureType);
    if ( !IsAttackable || IsAttackable == 2 && lpTarget[1].m_CellPos.m_cCellX != 4 )
      return 1;
  }
  lpTargeta = CCellManager::GetInstance();
  if ( Character->GetDuelOpponent(Character) == lpTarget )
    return 2;
  v10 = (int)Character->GetParty(Character);
  if ( v10 )
  {
    if ( lpTarget->GetParty(lpTarget) )
    {
      v11 = *(CParty **)(v10 + 356);
      if ( v11 == lpTarget->GetParty(lpTarget) )
        return 2;
    }
  }
  if ( !lpTarget->GetNation(lpTarget) )
  {
    CreatureType = Creature::GetCreatureType(lpTarget->m_dwCID);
    if ( CreatureType == 1 )
      return 0;
    if ( CreatureType == 2 )
      return 1;
  }
  if ( lpTarget->GetStatusFlag(lpTarget, 0x20000000u) )
    return 1;
  m_dwGID = this->m_dwGID;
  if ( m_dwGID && m_dwGID == lpTarget->GetGID(lpTarget) )
    return 0;
  if ( Character->IsPeaceMode(Character) )
    return !lpTarget->IsPeaceMode(lpTarget);
  v14 = Character->GetNation(Character);
  if ( v14 == lpTarget->GetNation(lpTarget) )
    return 0;
  if ( lpTarget->IsPeaceMode(lpTarget) )
    return 1;
  if ( CCellManager::IsSafetyZone(lpTargeta, this->m_CurrentPos) == 1 )
    return 1;
  v15 = CCellManager::IsSafetyZone(lpTargeta, lpTarget->m_CurrentPos) == 1;
  result = HOSTILITY;
  if ( v15 )
    return 1;
  return result;
}

//----- (0047F900) --------------------------------------------------------
char __thiscall CSiegeObject::IsRide(CSiegeObject *this, unsigned int dwCID)
{
  int v2; // eax
  unsigned int *i; // ecx

  v2 = 0;
  for ( i = this->m_dwRideCID; *i != dwCID; ++i )
  {
    if ( ++v2 >= 10 )
      return 0;
  }
  return 1;
}

//----- (0047F930) --------------------------------------------------------
bool __thiscall CSiegeObject::Ride(CSiegeObject *this, unsigned int dwRideCID)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // ecx
  bool result; // al
  int v7; // esi
  unsigned int *m_dwRideCID; // ebx
  CCreatureManager *v9; // eax
  CCharacter *v10; // eax

  switch ( this->m_wObjectType )
  {
    case 0x153Au:
    case 0x156Au:
    case 0x159Au:
    case 0x15CAu:
      if ( this->m_dwRideCID[0] )
        goto LABEL_10;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, dwRideCID);
      if ( !Character )
        goto LABEL_10;
      m_dwCID = this->m_dwCID;
      this->m_dwRideCID[0] = dwRideCID;
      Character->m_dwRideArmsCID = m_dwCID;
      result = 1;
      break;
    case 0x15FAu:
      if ( CSiegeObject::IsRide(this, dwRideCID) )
        goto LABEL_10;
      v7 = 0;
      m_dwRideCID = this->m_dwRideCID;
      while ( 1 )
      {
        if ( !*m_dwRideCID )
        {
          v9 = CCreatureManager::GetInstance();
          v10 = CCreatureManager::GetCharacter(v9, dwRideCID);
          if ( v10 )
            break;
        }
        ++v7;
        ++m_dwRideCID;
        if ( v7 >= 10 )
          goto LABEL_10;
      }
      this->m_dwRideCID[v7] = dwRideCID;
      v10->m_dwRideArmsCID = this->m_dwCID;
      result = 1;
      break;
    default:
LABEL_10:
      result = 0;
      break;
  }
  return result;
}

//----- (0047FAC0) --------------------------------------------------------
bool __thiscall CSiegeObject::GetOff(CSiegeObject *this, unsigned int dwRideCID)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  bool result; // al
  int v6; // esi
  unsigned int *m_dwRideCID; // edi
  CCreatureManager *v8; // eax
  CCharacter *v9; // eax
  unsigned int v10; // [esp-4h] [ebp-14h]

  switch ( this->m_wObjectType )
  {
    case 0x153Au:
    case 0x156Au:
    case 0x159Au:
    case 0x15CAu:
      if ( !this->m_dwRideCID[0] )
        goto LABEL_10;
      v10 = this->m_dwRideCID[0];
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, v10);
      if ( !Character )
        goto LABEL_10;
      this->m_dwRideCID[0] = 0;
      Character->m_dwRideArmsCID = 0;
      result = 1;
      break;
    case 0x15FAu:
      v6 = 0;
      m_dwRideCID = this->m_dwRideCID;
      while ( 1 )
      {
        if ( *m_dwRideCID )
        {
          if ( *m_dwRideCID == dwRideCID )
          {
            v8 = CCreatureManager::GetInstance();
            v9 = CCreatureManager::GetCharacter(v8, dwRideCID);
            if ( v9 )
              break;
          }
        }
        ++v6;
        ++m_dwRideCID;
        if ( v6 >= 10 )
          goto LABEL_10;
      }
      this->m_dwRideCID[v6] = 0;
      v9->m_dwRideArmsCID = 0;
      result = 1;
      break;
    default:
LABEL_10:
      result = 0;
      break;
  }
  return result;
}

//----- (0047FC40) --------------------------------------------------------
void __thiscall CSiegeObject::AllGetOff(CSiegeObject *this)
{
  unsigned int *m_dwRideCID; // edi
  int v3; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned int v7; // [esp-4h] [ebp-10h]

  m_dwRideCID = this->m_dwRideCID;
  v3 = 10;
  do
  {
    if ( *m_dwRideCID )
    {
      v7 = *m_dwRideCID;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, v7);
      if ( Character )
      {
        *m_dwRideCID = 0;
        m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
        Character->m_dwRideArmsCID = 0;
        if ( m_lpGameClientDispatch )
        {
          switch ( this->m_wObjectType )
          {
            case 0x153Au:
            case 0x156Au:
              GameClientSendPacket::SendCharCastleCmd(
                m_lpGameClientDispatch + 8,
                this->m_dwOwnerID,
                this->m_dwCID,
                0x18u,
                0);
              break;
            case 0x159Au:
            case 0x15CAu:
            case 0x15FAu:
              GameClientSendPacket::SendCharSiegeArmsCmd(
                m_lpGameClientDispatch + 8,
                Character->m_dwCID,
                this->m_dwCID,
                5u,
                0);
              break;
            default:
              break;
          }
        }
      }
    }
    ++m_dwRideCID;
    --v3;
  }
  while ( v3 );
}

//----- (0047FDC0) --------------------------------------------------------
char __thiscall CSiegeObject::DestroyCamp(CSiegeObject *this)
{
  unsigned int m_dwCID; // eax
  unsigned int m_dwOwnerID; // ecx
  CCreatureManager *Instance; // eax
  char szBuffer[27]; // [esp+0h] [ebp-20h] BYREF

  m_dwCID = this->m_dwCID;
  m_dwOwnerID = this->m_dwOwnerID;
  *(_DWORD *)&szBuffer[12] = m_dwCID;
  *(_DWORD *)&szBuffer[16] = m_dwOwnerID;
  *(_DWORD *)&szBuffer[20] = 0;
  szBuffer[24] = 7;
  szBuffer[25] = 0;
  szBuffer[26] = 11;
  if ( PacketWrap::WrapCrypt(szBuffer, 0x1Bu, 0xABu, 0, 0) != 1 )
    return 0;
  Instance = CCreatureManager::GetInstance();
  CCreatureManager::SendAllCharacter(Instance, szBuffer, 0x1Bu, 0xABu, 1);
  return 1;
}

//----- (0047FE50) --------------------------------------------------------
bool __thiscall CSiegeObject::CheckRight(CSiegeObject *this, unsigned __int8 cRightType, unsigned int dwCID)
{
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int m_dwGID; // [esp-4h] [ebp-8h]

  m_dwGID = this->m_dwGID;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
  return Guild && this->m_CampRight.m_aryCampRight[cRightType] >= Guild::CGuild::GetTitle(Guild, dwCID);
}

//----- (0047FE90) --------------------------------------------------------
unsigned int __thiscall CSiegeObject::GetUpgradeGold(CSiegeObject *this)
{
  unsigned __int64 v1; // rax

  switch ( this->m_wObjectType )
  {
    case 0x14A8u:
      v1 = (unsigned __int64)((double)(this->m_cUpgradeStep + 1) * 200000.0);
      break;
    case 0x150Au:
    case 0x153Au:
    case 0x156Au:
      v1 = (unsigned __int64)((double)(this->m_cUpgradeStep + 1) * 200000.0);
      break;
    default:
      LODWORD(v1) = 0;
      break;
  }
  return v1;
}

//----- (0047FFC0) --------------------------------------------------------
int __thiscall CSiegeObject::GetRepairGold(CSiegeObject *this)
{
  int v1; // eax
  int result; // eax

  switch ( this->m_wObjectType )
  {
    case 0x14A8u:
    case 0x150Au:
    case 0x153Au:
    case 0x156Au:
      v1 = (this->m_CreatureStatus.m_StatusInfo.m_nMaxHP - this->m_CreatureStatus.m_nNowHP) / 10;
      if ( (this->m_CreatureStatus.m_StatusInfo.m_nMaxHP - this->m_CreatureStatus.m_nNowHP) % 10 > 0 )
        ++v1;
      result = 100 * v1;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (004800E0) --------------------------------------------------------
int __thiscall CSiegeObject::GetRestoreGold(CSiegeObject *this)
{
  return this->m_wObjectType != 5288 ? 0 : 0xF4240;
}

//----- (00480100) --------------------------------------------------------
int __thiscall CSiegeObject::GetRepairHP(CSiegeObject *this)
{
  return this->m_CreatureStatus.m_StatusInfo.m_nMaxHP - this->m_CreatureStatus.m_nNowHP;
}

//----- (00480120) --------------------------------------------------------
int __thiscall CSiegeObject::GetRepairMaterialNum(CSiegeObject *this)
{
  int result; // eax

  result = (this->m_CreatureStatus.m_StatusInfo.m_nMaxHP - this->m_CreatureStatus.m_nNowHP) / 10;
  if ( (this->m_CreatureStatus.m_StatusInfo.m_nMaxHP - this->m_CreatureStatus.m_nNowHP) % 10 > 0 )
    ++result;
  return result;
}

//----- (00480140) --------------------------------------------------------
int __thiscall CSiegeObject::GetKID(CSiegeObject *this)
{
  unsigned __int16 m_wObjectType; // si
  int m_cUpgradeStep; // edx

  m_wObjectType = this->m_wObjectType;
  m_cUpgradeStep = this->m_cUpgradeStep;
  if ( m_wObjectType == 5000 )
    return m_cUpgradeStep + 6 * (this->m_cUpgradeType + 6 * this->m_cState) + 5000;
  else
    return m_cUpgradeStep + m_wObjectType + 6 * this->m_cState;
}

//----- (00480190) --------------------------------------------------------
void __thiscall CSiegeObject::SendToRadiusCell(
        CSiegeObject *this,
        char *szPacket,
        unsigned int dwPacketSize,
        unsigned __int8 cCMD_In)
{
  CCell *m_lpCell; // esi
  int v5; // edi
  CCell *ConnectCell; // eax
  int v7; // edi
  CCell *v8; // eax
  CCell *v9; // esi
  int j; // edi
  int nCellWidth; // [esp+4h] [ebp-10h]
  CCell *lpStartCell; // [esp+8h] [ebp-Ch]
  int nCellHeight; // [esp+Ch] [ebp-8h]
  int i; // [esp+10h] [ebp-4h]

  m_lpCell = this->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    nCellWidth = 9;
    nCellHeight = 9;
    v5 = 1;
    while ( 1 )
    {
      ConnectCell = CCell::GetConnectCell(m_lpCell, 3u);
      if ( !ConnectCell )
        break;
      ++v5;
      m_lpCell = ConnectCell;
      if ( v5 >= 5 )
        goto LABEL_7;
    }
    nCellWidth = v5 + 4;
LABEL_7:
    v7 = 1;
    while ( 1 )
    {
      v8 = CCell::GetConnectCell(m_lpCell, 1u);
      if ( !v8 )
        break;
      ++v7;
      m_lpCell = v8;
      if ( v7 >= 5 )
        goto LABEL_12;
    }
    nCellHeight = v7 + 4;
LABEL_12:
    lpStartCell = m_lpCell;
    for ( i = 0; i < nCellHeight; ++i )
    {
      v9 = lpStartCell;
      for ( j = 0; j < nCellWidth; ++j )
      {
        if ( !v9 )
          break;
        CCell::SendAllCharacter(v9, szPacket, dwPacketSize, cCMD_In);
        v9 = CCell::GetConnectCell(v9, 4u);
      }
      lpStartCell = CCell::GetConnectCell(lpStartCell, 2u);
      if ( !lpStartCell )
        break;
    }
  }
}

//----- (00480290) --------------------------------------------------------
char __thiscall CSiegeObject::AttackCID(
        CSiegeObject *this,
        CCharacter *lpRideChar,
        AtType attackType,
        AtNode *attackNode,
        unsigned __int16 *wError)
{
  CPerformanceCheck *Instance; // eax
  char v7; // bl
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CSkillMgr::ProtoTypeArray *v10; // esi
  signed int v11; // ecx
  CCreatureManager *v12; // eax
  CMsgProc *AggresiveCreature; // eax
  Skill::Target::Type m_eTargetType; // eax
  unsigned __int8 v15; // bl
  int v16; // ebp
  CCreatureManager *v17; // eax
  CMsgProc *v18; // eax
  CSiegeObject *v19; // esi
  CCreatureManager *v20; // eax
  double v21; // st5
  double v22; // st4
  double v23; // st7
  double v24; // st5
  CCreatureManager *v25; // eax
  CSkillMgr::ProtoTypeArray *v26; // eax
  Skill::ProtoType *v27; // ecx
  bool v28; // bl
  char v29; // bl
  __int64 fEstimateTime; // [esp+4h] [ebp-74h]
  __int64 fEstimateTimea; // [esp+4h] [ebp-74h]
  __int64 fEstimateTimeb; // [esp+4h] [ebp-74h]
  float fEstimateTimec; // [esp+4h] [ebp-74h]
  signed int fEstimateTime_4; // [esp+8h] [ebp-70h]
  signed int fEstimateTime_4a; // [esp+8h] [ebp-70h]
  char cTargetType; // [esp+1Ch] [ebp-5Ch]
  CPerformanceInstrument functionInstrument; // [esp+2Ch] [ebp-4Ch] BYREF
  CAggresiveCreature *lpAggresiveCreature[10]; // [esp+44h] [ebp-34h] BYREF
  int v39; // [esp+74h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CSiegeObject::AttackCID";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CSiegeObject::AttackCID", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  v39 = 0;
  if ( !lpRideChar || !this->m_CreatureStatus.m_nNowHP )
  {
    *wError = 2;
    v39 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 1;
  }
  LOBYTE(lpRideChar) = 10;
  if ( attackNode->m_wDefenserNum <= 0xAu )
    LOBYTE(lpRideChar) = attackNode->m_wDefenserNum;
  memset(lpAggresiveCreature, 0, sizeof(lpAggresiveCreature));
  cTargetType = 3;
  if ( !(_BYTE)lpRideChar )
  {
    v7 = ((int (__thiscall *)(_DWORD, _DWORD, _DWORD, _DWORD, _DWORD))this->Attack)(
           this,
           attackType,
           0,
           lpAggresiveCreature,
           attackNode->m_cDefenserJudge);
    v39 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v7;
  }
  if ( (attackType.m_wType & 0x8000) == 0 )
    goto LABEL_26;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  v10 = SkillProtoType;
  if ( !SkillProtoType )
  {
    HIDWORD(fEstimateTime) = attackType.m_wType;
    LODWORD(fEstimateTime) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::AttackCID",
      aDWorkRylSource_42,
      310,
      aCid0x08x_77,
      fEstimateTime);
    goto LABEL_51;
  }
  if ( 0.0 == SkillProtoType->m_ProtoTypes[0].m_EffectDistance
    && 0.0 == SkillProtoType->m_ProtoTypes[0].m_EffectExtent
    && this->m_dwCID != attackNode->m_dwDefenser[0] )
  {
    HIDWORD(fEstimateTimea) = attackType.m_wType;
    LODWORD(fEstimateTimea) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::AttackCID",
      aDWorkRylSource_42,
      319,
      aCid0x08x_53,
      fEstimateTimea);
    goto LABEL_51;
  }
  if ( Creature::GetCreatureType(attackNode->m_dwDefenser[0]) == 2 )
  {
    *wError = 8;
    goto LABEL_51;
  }
  fEstimateTime_4 = v11;
  if ( !this->m_CellPos.m_wMapIndex )
  {
    v20 = CCreatureManager::GetInstance();
    AggresiveCreature = CCreatureManager::GetAggresiveCreature(v20, fEstimateTime_4);
    goto LABEL_33;
  }
  v12 = CCreatureManager::GetInstance();
  AggresiveCreature = CCreatureManager::GetAggresiveCreature(v12, fEstimateTime_4);
  if ( AggresiveCreature && LOWORD(AggresiveCreature[101].__vftable) == this->m_CellPos.m_wMapIndex )
  {
LABEL_33:
    if ( AggresiveCreature )
    {
      v21 = this->m_CurrentPos.m_fPointX - *(float *)&AggresiveCreature[1].__vftable;
      v22 = this->m_CurrentPos.m_fPointZ - *(float *)&AggresiveCreature[3].__vftable;
      v23 = v21 * v21 + v22 * v22;
      v24 = v10->m_ProtoTypes[0].m_EffectDistance + 10.0;
      if ( v23 > v24 * v24 )
      {
        *wError = 3;
        goto LABEL_51;
      }
    }
  }
  m_eTargetType = v10->m_ProtoTypes[0].m_eTargetType;
  if ( m_eTargetType == MIDDLE_ADMIN
    || m_eTargetType == LEAVE_WAIT
    || m_eTargetType == 9
    || m_eTargetType == MAX_TITLE
    || m_eTargetType == 8
    || m_eTargetType == (COMMON|0x8) )
  {
    cTargetType = 2;
  }
LABEL_26:
  v15 = 0;
  do
  {
    v16 = v15;
    if ( (attackNode->m_dwDefenser[v16] & 0x80000000) != 0 )
      goto LABEL_42;
    fEstimateTime_4a = attackNode->m_dwDefenser[v16];
    if ( this->m_CellPos.m_wMapIndex )
    {
      v17 = CCreatureManager::GetInstance();
      v18 = CCreatureManager::GetAggresiveCreature(v17, fEstimateTime_4a);
      v19 = (CSiegeObject *)v18;
      if ( !v18 || LOWORD(v18[101].__vftable) != this->m_CellPos.m_wMapIndex )
        goto LABEL_42;
    }
    else
    {
      v25 = CCreatureManager::GetInstance();
      v19 = (CSiegeObject *)CCreatureManager::GetAggresiveCreature(v25, fEstimateTime_4a);
    }
    if ( v19 )
    {
      if ( cTargetType == 2 )
      {
        *wError = 9;
        goto LABEL_51;
      }
      if ( this->IsEnemy(this, v19) == HOSTILITY || v19 == this )
        lpAggresiveCreature[v15] = v19;
    }
LABEL_42:
    ++v15;
  }
  while ( v15 < (unsigned __int8)lpRideChar );
  if ( (attackType.m_wType & 0x8000) == 0 || !lpAggresiveCreature[0] )
    goto LABEL_53;
  v26 = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  if ( !v26 )
  {
    HIDWORD(fEstimateTimeb) = attackType.m_wType;
    LODWORD(fEstimateTimeb) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::AttackCID",
      aDWorkRylSource_42,
      429,
      aCid0x08x_77,
      fEstimateTimeb);
    goto LABEL_51;
  }
  v27 = &v26->m_ProtoTypes[*((unsigned __int8 *)&attackType + 2) >> 4];
  if ( 0.0 != v27->m_EffectExtent )
  {
    if ( v27->m_eTargetType == 8 )
    {
      *wError = 9;
LABEL_51:
      v39 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return 0;
    }
    else
    {
      fEstimateTimec = PI_27 + PI_27;
      v28 = CAggresiveCreature::MultiAttack(
              this,
              attackType,
              (int)lpRideChar,
              lpAggresiveCreature,
              (CCell *)attackNode->m_cDefenserJudge,
              lpAggresiveCreature[0]->m_CurrentPos,
              0.0,
              v27->m_EffectExtent,
              fEstimateTimec,
              cTargetType);
      v39 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return v28;
    }
  }
  else
  {
LABEL_53:
    v29 = ((int (__thiscall *)(_DWORD, _DWORD, _DWORD, _DWORD, _DWORD))this->Attack)(
            this,
            attackType,
            (unsigned __int8)lpRideChar,
            lpAggresiveCreature,
            attackNode->m_cDefenserJudge);
    v39 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v29;
  }
}
// 4804D2: conditional instruction was optimized away because %lpRideChar.1!=0
// 480472: variable 'v11' is possibly undefined

//----- (00480770) --------------------------------------------------------
char __thiscall CSiegeObject::Attack(
        CSiegeObject *this,
        AtType attackType,
        unsigned __int8 cDefenderNum,
        CAggresiveCreature **ppDefenders,
        unsigned __int8 *cDefenserJudges)
{
  unsigned __int8 v5; // bl
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned __int8 *p_cIndex; // eax
  unsigned __int8 v11; // dl
  CAggresiveCreature **v12; // esi
  CAggresiveCreature **v13; // ebx
  CAggresiveCreature *v14; // ecx
  int v15; // esi
  unsigned __int8 *v16; // ebp
  CAggresiveCreature *v17; // ecx
  CAggresiveCreature_vtbl *v18; // edx
  bool v19; // zf
  CAggresiveCreature *v20; // ecx
  char *v21; // esi
  unsigned __int16 m_nNowHP; // dx
  CAggresiveCreature *v23; // ecx
  CSpell *v24; // edx
  CSendStream *v25; // eax
  CAggresiveCreature *v26; // ebx
  __int16 v27; // cx
  unsigned __int16 v28; // dx
  unsigned __int16 m_nMaxMP; // cx
  AtType v30; // ecx
  unsigned int m_dwCID; // edx
  unsigned __int16 v32; // cx
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  CCell *m_lpCell; // ecx
  unsigned int v35; // [esp+12h] [ebp-E0h]
  unsigned __int8 cIndex; // [esp+20h] [ebp-D2h] BYREF
  unsigned __int8 cOffencerJudge; // [esp+21h] [ebp-D1h] BYREF
  int cDefender; // [esp+22h] [ebp-D0h]
  int wError; // [esp+26h] [ebp-CCh] BYREF
  unsigned __int16 wPrevAttackerHP; // [esp+2Ah] [ebp-C8h]
  CAggresiveCreature *pDefendCharacter; // [esp+2Eh] [ebp-C4h]
  unsigned __int16 nNowMP; // [esp+32h] [ebp-C0h]
  CCharacter *lpRideChar; // [esp+36h] [ebp-BCh]
  int nNowHP; // [esp+3Ah] [ebp-B8h]
  char szBuffer[176]; // [esp+3Eh] [ebp-B4h] BYREF

  v5 = cDefenderNum;
  if ( (attackType.m_wType & 0x8000u) == 0 && !cDefenderNum )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSiegeObject::Attack", aDWorkRylSource_42, 458, (char *)&byte_4D9178);
    return 0;
  }
  v35 = this->m_dwRideCID[0];
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v35);
  lpRideChar = Character;
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::Attack",
      aDWorkRylSource_42,
      465,
      aCid0x08x_60,
      this->m_dwCID,
      this->m_dwRideCID[0]);
    return 0;
  }
  CAffectedSpell::RemoveEnchantBySpellType(&Character->m_SpellMgr.m_AffectedInfo, 0x20000000u);
  cOffencerJudge = 0;
  wError = 0;
  cIndex = 5;
  p_cIndex = &cIndex;
  if ( v5 <= 5u )
    p_cIndex = &cDefenderNum;
  this->m_cConsumeMPCount = *p_cIndex;
  LOBYTE(p_cIndex) = 0;
  v11 = 0;
  LOBYTE(cDefender) = 0;
  cIndex = 0;
  if ( v5 )
  {
    do
    {
      v12 = ppDefenders;
      --this->m_cConsumeMPCount;
      v13 = &v12[v11];
      v14 = *v13;
      if ( *v13 && v14->m_CreatureStatus.m_nNowHP && (v14->m_dwCID & 0x80000000) == 0 )
      {
        v15 = (unsigned __int8)p_cIndex;
        v16 = &cDefenserJudges[(unsigned __int8)p_cIndex];
        *v16 = 0;
        v17 = *v13;
        v18 = (*v13)->__vftable;
        wPrevAttackerHP = this->m_CreatureStatus.m_nNowHP;
        p_cIndex = (unsigned __int8 *)((int (__thiscall *)(CAggresiveCreature *, AtType, CSiegeObject *, unsigned __int8 *, unsigned __int8 *, int *))v18->ApplyDamage)(
                                        v17,
                                        attackType,
                                        this,
                                        &cOffencerJudge,
                                        v16,
                                        &wError);
        v19 = this->m_CreatureStatus.m_nNowHP == 0;
        v20 = *v13;
        v21 = &szBuffer[15 * v15 + 26];
        *((_WORD *)v21 + 6) = (_WORD)p_cIndex;
        m_nNowHP = v20->m_CreatureStatus.m_nNowHP;
        LOWORD(v20) = v20->m_CreatureStatus.m_nNowMP;
        LOWORD(nNowHP) = m_nNowHP;
        nNowMP = (unsigned __int16)v20;
        if ( v19 )
        {
          this->m_CreatureStatus.m_nNowHP = wPrevAttackerHP;
          LOBYTE(p_cIndex) = cDefender;
          wError = 4;
          break;
        }
        v23 = *v13;
        v19 = ((*v13)->m_dwCID & 0xD0000000) == 0;
        pDefendCharacter = *v13;
        if ( v19 )
        {
          v24 = v23[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[4];
          if ( v24 )
          {
            v24->__vftable[8].Deactivate(v24, this, (unsigned int)p_cIndex);
            v23 = pDefendCharacter;
          }
          v23->CalculateEquipDurability(v23, (*v16 == 3) + 5);
          v25 = (CSendStream *)pDefendCharacter[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[3];
          if ( v25 )
            GameClientSendPacket::SendCharAttacked(
              v25 + 8,
              this,
              pDefendCharacter,
              attackType,
              this->m_MotionInfo.m_fDirection,
              *((_WORD *)v21 + 6),
              *v16,
              0);
        }
        v26 = *v13;
        p_cIndex = (unsigned __int8 *)v26->m_dwCID;
        v27 = nNowHP;
        v28 = nNowMP;
        *(_DWORD *)v21 = p_cIndex;
        *((_WORD *)v21 + 5) = v28;
        LOBYTE(v28) = *v16;
        *((_WORD *)v21 + 3) = v27;
        *((_WORD *)v21 + 2) = v26->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        m_nMaxMP = v26->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
        LOBYTE(p_cIndex) = cDefender + 1;
        v21[14] = v28;
        v11 = cIndex;
        *((_WORD *)v21 + 4) = m_nMaxMP;
        LOBYTE(cDefender) = (_BYTE)p_cIndex;
      }
      cIndex = ++v11;
    }
    while ( v11 < cDefenderNum );
  }
  v30 = attackType;
  if ( (attackType.m_wType & 0x8000u) != 0 && !(_BYTE)p_cIndex )
  {
    Skill::CFunctions::ConsumeMP(attackType, this);
    v30 = attackType;
    LOBYTE(p_cIndex) = cDefender;
  }
  m_dwCID = this->m_dwCID;
  *(AtType *)&szBuffer[16] = v30;
  v32 = this->m_CreatureStatus.m_nNowHP;
  *(_DWORD *)&szBuffer[12] = m_dwCID;
  LOWORD(m_dwCID) = this->m_CreatureStatus.m_nNowMP;
  *(_WORD *)&szBuffer[20] = v32;
  *(_WORD *)&szBuffer[22] = m_dwCID;
  szBuffer[24] = cOffencerJudge;
  szBuffer[25] = (char)p_cIndex;
  m_lpGameClientDispatch = lpRideChar->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
    return 0;
  LOWORD(p_cIndex) = (unsigned __int8)p_cIndex;
  if ( CSendStream::WrapCompress(
         &m_lpGameClientDispatch->m_SendStream,
         szBuffer,
         (char *)(15 * (_DWORD)p_cIndex + 26),
         0xEu,
         0,
         wError) != 1 )
    return 0;
  if ( (_WORD)wError )
    return 0;
  m_lpCell = this->m_CellPos.m_lpCell;
  if ( !m_lpCell )
    return 0;
  CCell::SendAttackInfo(m_lpCell, this->m_dwCID, &attackType, cDefender, (DefenserNode *)&szBuffer[26]);
  return 1;
}
// 480A8A: variable 'p_cIndex' is possibly undefined

//----- (00480AF0) --------------------------------------------------------
char __thiscall CSiegeObject::BuildCamp(CSiegeObject *this)
{
  unsigned int m_dwCID; // eax
  unsigned int m_dwOwnerID; // ecx
  PktCampCmd pktCC; // [esp+4h] [ebp-1Ch] BYREF

  if ( this->m_wObjectType != 5337 )
    return 0;
  this->m_cState = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_dwCID = this->m_dwCID;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwHP = this->m_CreatureStatus.m_nNowHP;
  pktCC.m_dwCID = m_dwCID;
  LOBYTE(m_dwCID) = this->m_cState;
  pktCC.m_dwCampID = m_dwOwnerID;
  LOBYTE(m_dwOwnerID) = this->m_cUpgradeStep;
  pktCC.m_cState = m_dwCID;
  pktCC.m_cUpgradeStep = m_dwOwnerID;
  pktCC.m_cSubCmd = 2;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x1Bu, 0xABu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x1Bu, 0xABu);
  return 1;
}

//----- (00480B90) --------------------------------------------------------
char __thiscall CSiegeObject::UpdateCampInfo(
        CSiegeObject *this,
        unsigned __int8 cState,
        unsigned __int8 cUpgradeStep,
        unsigned __int8 cSubCmd)
{
  unsigned int m_nNowHP; // ecx
  unsigned int m_dwOwnerID; // eax
  PktCampCmd pktCC; // [esp+4h] [ebp-1Ch] BYREF

  if ( this->m_wObjectType != 5337 )
    return 0;
  this->m_cUpgradeStep = cUpgradeStep;
  this->m_cState = cState;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCID = this->m_dwCID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwCampID = m_dwOwnerID;
  LOBYTE(m_dwOwnerID) = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_cUpgradeStep = m_dwOwnerID;
  pktCC.m_cSubCmd = cSubCmd;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x1Bu, 0xABu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x1Bu, 0xABu);
  return 1;
}

//----- (00480C40) --------------------------------------------------------
char __thiscall CSiegeObject::UpgradeCamp(CSiegeObject *this, unsigned __int8 cUpgradeStep)
{
  unsigned int m_dwCID; // ecx
  unsigned int m_dwOwnerID; // edx
  PktCampCmd pktCC; // [esp+4h] [ebp-1Ch] BYREF

  if ( this->m_wObjectType != 5337 )
    return 0;
  this->m_cState = 0;
  this->m_cUpgradeStep = cUpgradeStep;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_dwCID = this->m_dwCID;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwHP = this->m_CreatureStatus.m_nNowHP;
  pktCC.m_dwCID = m_dwCID;
  LOBYTE(m_dwCID) = this->m_cState;
  pktCC.m_dwCampID = m_dwOwnerID;
  LOBYTE(m_dwOwnerID) = this->m_cUpgradeStep;
  pktCC.m_cState = m_dwCID;
  pktCC.m_cUpgradeStep = m_dwOwnerID;
  pktCC.m_cSubCmd = 6;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x1Bu, 0xABu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x1Bu, 0xABu);
  return 1;
}

//----- (00480CF0) --------------------------------------------------------
char __thiscall CSiegeObject::RepairCamp(CSiegeObject *this, unsigned __int16 wRepairHP)
{
  unsigned int m_dwCID; // ecx
  unsigned int m_dwOwnerID; // edx
  PktCampCmd pktCC; // [esp+4h] [ebp-1Ch] BYREF

  if ( this->m_wObjectType != 5337 )
    return 0;
  this->m_cState = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  this->m_CreatureStatus.m_nNowHP += wRepairHP;
  m_dwCID = this->m_dwCID;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwHP = this->m_CreatureStatus.m_nNowHP;
  pktCC.m_dwCID = m_dwCID;
  LOBYTE(m_dwCID) = this->m_cState;
  pktCC.m_dwCampID = m_dwOwnerID;
  LOBYTE(m_dwOwnerID) = this->m_cUpgradeStep;
  pktCC.m_cState = m_dwCID;
  pktCC.m_cUpgradeStep = m_dwOwnerID;
  pktCC.m_cSubCmd = 8;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x1Bu, 0xABu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x1Bu, 0xABu);
  return 1;
}

//----- (00480DA0) --------------------------------------------------------
char __thiscall CSiegeObject::GiveBackSiegeMaterial(CSiegeObject *this)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  unsigned __int16 m_nMaxHP; // cx
  unsigned __int16 m_nNowHP; // ax
  Item::CItem *v5; // eax
  Item::CItem *v6; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v10; // esi
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int m_dwOwnerID; // [esp-Ch] [ebp-50h]
  unsigned __int8 cNum; // [esp+Bh] [ebp-39h]
  CCell::ItemInfo itemInfo; // [esp+14h] [ebp-30h] BYREF

  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, this->m_wObjectType);
  if ( !MonsterProtoType )
    return 1;
  m_nMaxHP = MonsterProtoType->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  if ( m_nNowHP <= m_nMaxHP )
    return 1;
  cNum = (unsigned __int64)((double)(unsigned __int16)(m_nNowHP - m_nMaxHP)
                          / ((double)this->m_cUpgradeStep
                           * (double)m_nMaxHP
                           * 0.2)
                          * (double)(10 * this->m_cUpgradeStep));
  if ( !cNum )
    return 1;
  Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, 0x26BAu);
  v6 = v5;
  if ( v5 )
  {
    v5->m_ItemData.m_cNumOrDurability = cNum;
    m_dwOwnerID = this->m_dwOwnerID;
    memset(&itemInfo, 0, 12);
    itemInfo.cNum = 0;
    memset(&itemInfo.UID, 0, 18);
    itemInfo.lpItem = 0;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwOwnerID);
    v10 = Character;
    if ( Character
      && (CCell::SetItem(
            Character->m_CellPos.m_lpCell,
            v6,
            &Character->m_CurrentPos,
            &itemInfo,
            Character->m_dwCID,
            Character->m_dwCID,
            0,
            1u,
            1),
          (m_lpGameClientDispatch = (CSendStream *)v10->m_lpGameClientDispatch) != 0) )
    {
      return GameClientSendPacket::SendCharAutoRouting(
               m_lpGameClientDispatch + 8,
               v10->m_dwCID,
               itemInfo.UID.nUniqueID,
               v6->m_ItemData.m_usProtoTypeID,
               1u,
               0);
    }
    else
    {
      return CCell::SetItem(this->m_CellPos.m_lpCell, v6, &this->m_CurrentPos, &itemInfo, 0, 0, 0, 1u, 0);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSiegeObject::GiveBackSiegeMaterial",
      aDWorkRylSource_42,
      823,
      (char *)&byte_4F0654);
    return 0;
  }
}
// 480E41: variable 'v5' is possibly undefined

//----- (00480F50) --------------------------------------------------------
char __thiscall CSiegeObject::UpdateEmblem(
        CSiegeObject *this,
        unsigned __int8 cState,
        unsigned int dwValue,
        unsigned __int8 cSubCmd)
{
  CMsgProcessMgr *Instance; // eax
  Castle::CCastle *Castle; // eax
  unsigned int v8; // ecx
  unsigned int m_dwCID; // edx
  unsigned int m_dwOwnerID; // [esp-8h] [ebp-30h]
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5000 )
    return 0;
  this->m_cState = cState;
  if ( cSubCmd == 3 )
  {
    this->m_cUpgradeType = dwValue;
    CSiegeObject::UpdateObjectInfo(this, 0);
  }
  else if ( cSubCmd == 5 )
  {
    m_dwOwnerID = this->m_dwOwnerID;
    Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
    Castle = (Castle::CCastle *)Castle::CCastleMgr::GetCastle(Instance, m_dwOwnerID);
    if ( Castle )
      Castle::CCastle::DegradeByEmblem(Castle);
    this->m_cSubState = dwValue;
    this->m_cUpgradeType = 0;
    this->m_cUpgradeStep = 0;
    CSiegeObject::UpdateObjectInfo(this, 1);
  }
  v8 = this->m_dwOwnerID;
  m_dwCID = this->m_dwCID;
  pktCC.m_dwHP = this->m_CreatureStatus.m_nNowHP;
  pktCC.m_dwCastleID = v8;
  LOBYTE(v8) = this->m_cState;
  pktCC.m_dwCastleObjectID = m_dwCID;
  pktCC.m_cState = v8;
  pktCC.m_dwValue = dwValue;
  pktCC.m_cSubCmd = cSubCmd;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481040) --------------------------------------------------------
char __thiscall CSiegeObject::UpgradeEmblem(CSiegeObject *this, unsigned __int8 cUpgradeStep)
{
  CMsgProcessMgr *Instance; // eax
  Castle::CCastle *Castle; // eax
  Castle::CCastle *v6; // edi
  unsigned int m_nNowHP; // eax
  unsigned int v8; // ecx
  unsigned int m_cUpgradeStep; // edx
  unsigned int m_dwOwnerID; // [esp-8h] [ebp-30h]
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5000 )
    return 0;
  m_dwOwnerID = this->m_dwOwnerID;
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = (Castle::CCastle *)Castle::CCastleMgr::GetCastle(Instance, m_dwOwnerID);
  v6 = Castle;
  if ( Castle )
    Castle::CCastle::DegradeByEmblem(Castle);
  this->m_cState = 0;
  this->m_cUpgradeStep = cUpgradeStep;
  CSiegeObject::UpdateObjectInfo(this, 0);
  if ( v6 )
    Castle::CCastle::UpgradeByEmblem(v6);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  v8 = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = v8;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 4;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481110) --------------------------------------------------------
void __thiscall CSiegeObject::Open(CSiegeObject *this)
{
  unsigned int m_dwCID; // edx
  unsigned int m_nNowHP; // ecx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType == 5288 )
  {
    m_dwCID = this->m_dwCID;
    pktCC.m_dwCastleID = this->m_dwOwnerID;
    m_nNowHP = this->m_CreatureStatus.m_nNowHP;
    this->m_cSubState = 1;
    pktCC.m_dwValue = 1;
    pktCC.m_dwCastleObjectID = m_dwCID;
    LOBYTE(m_dwCID) = this->m_cState;
    pktCC.m_dwHP = m_nNowHP;
    pktCC.m_cState = m_dwCID;
    pktCC.m_cSubCmd = 9;
    if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
      CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  }
}

//----- (00481190) --------------------------------------------------------
void __thiscall CSiegeObject::Close(CSiegeObject *this)
{
  unsigned int m_dwCID; // edx
  unsigned int m_nNowHP; // ecx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType == 5288 )
  {
    m_dwCID = this->m_dwCID;
    pktCC.m_dwCastleID = this->m_dwOwnerID;
    m_nNowHP = this->m_CreatureStatus.m_nNowHP;
    this->m_cSubState = 2;
    pktCC.m_dwValue = 2;
    pktCC.m_dwCastleObjectID = m_dwCID;
    LOBYTE(m_dwCID) = this->m_cState;
    pktCC.m_dwHP = m_nNowHP;
    pktCC.m_cState = m_dwCID;
    pktCC.m_cSubCmd = 10;
    if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
      CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  }
}

//----- (00481210) --------------------------------------------------------
char __thiscall CSiegeObject::UpdateGate(
        CSiegeObject *this,
        unsigned __int8 cState,
        unsigned int dwValue,
        unsigned __int8 cSubCmd)
{
  unsigned int m_nNowHP; // eax
  unsigned int m_dwCID; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5288 )
    return 0;
  this->m_cState = cState;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwCID = this->m_dwCID;
  pktCC.m_dwCastleID = this->m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwCastleObjectID = m_dwCID;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwValue = dwValue;
  pktCC.m_cSubCmd = cSubCmd;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (004812B0) --------------------------------------------------------
char __thiscall CSiegeObject::UpgradeGate(CSiegeObject *this, unsigned __int8 cUpgradeStep)
{
  unsigned int m_nNowHP; // eax
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_cUpgradeStep; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5288 )
    return 0;
  this->m_cState = 0;
  this->m_cUpgradeStep = cUpgradeStep;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 14;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481360) --------------------------------------------------------
char __thiscall CSiegeObject::RepairGate(CSiegeObject *this, unsigned __int16 wRepairHP)
{
  unsigned __int16 m_nNowHP; // ax
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_cUpgradeStep; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5288 )
    return 0;
  this->m_cState = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  this->m_CreatureStatus.m_nNowHP += wRepairHP;
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 16;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481410) --------------------------------------------------------
char __thiscall CSiegeObject::RestoreGate(CSiegeObject *this)
{
  unsigned int m_nNowHP; // edx
  unsigned int m_dwOwnerID; // eax
  unsigned int m_cUpgradeStep; // ecx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5288 )
    return 0;
  this->m_cState = 0;
  this->m_cUpgradeStep = 0;
  CSiegeObject::UpdateObjectInfo(this, 1);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 18;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (004814B0) --------------------------------------------------------
char __thiscall CSiegeObject::DestroyGate(CSiegeObject *this)
{
  unsigned int m_dwCID; // ecx
  unsigned __int8 m_cState; // dl
  unsigned int m_cUpgradeStep; // eax
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5288 )
    return 0;
  this->m_cState = 7;
  this->m_cUpgradeStep = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_dwCID = this->m_dwCID;
  m_cState = this->m_cState;
  pktCC.m_dwCastleID = this->m_dwOwnerID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwCastleObjectID = m_dwCID;
  this->m_CreatureStatus.m_nNowHP = 0;
  pktCC.m_dwHP = 0;
  pktCC.m_cState = m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 19;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481560) --------------------------------------------------------
char __thiscall CSiegeObject::UpdateCastleArms(
        CSiegeObject *this,
        unsigned __int8 cState,
        unsigned int dwValue,
        unsigned __int8 cSubCmd)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_dwCID; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType != 5385 && m_wObjectType != 5434 && m_wObjectType != 5482 && m_wObjectType != 5386 )
    return 0;
  this->m_cState = cState;
  if ( cSubCmd == 21 )
    CSiegeObject::UpdateObjectInfo(this, 1);
  else
    CSiegeObject::UpdateObjectInfo(this, 0);
  m_dwOwnerID = this->m_dwOwnerID;
  m_dwCID = this->m_dwCID;
  pktCC.m_dwHP = this->m_CreatureStatus.m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  LOBYTE(m_dwOwnerID) = this->m_cState;
  pktCC.m_dwCastleObjectID = m_dwCID;
  pktCC.m_cState = m_dwOwnerID;
  pktCC.m_dwValue = dwValue;
  pktCC.m_cSubCmd = cSubCmd;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481620) --------------------------------------------------------
char __thiscall CSiegeObject::ChangeCastleArms(CSiegeObject *this, unsigned __int16 wType)
{
  unsigned int m_nNowHP; // edx
  unsigned int m_dwOwnerID; // eax
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  if ( this->m_wObjectType != 5385 )
    return 0;
  this->m_wObjectType = wType;
  this->m_cState = 0;
  this->m_cUpgradeStep = 0;
  CSiegeObject::UpdateObjectInfo(this, 1);
  CSiegeObject::SetACTByObjectType(this);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = wType;
  pktCC.m_cSubCmd = 22;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (004816D0) --------------------------------------------------------
char __thiscall CSiegeObject::UpgradeCastleArms(CSiegeObject *this, unsigned __int8 cUpgradeStep)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_nNowHP; // eax
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_cUpgradeStep; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType != 5434 && m_wObjectType != 5482 && m_wObjectType != 5386 )
    return 0;
  this->m_cState = 0;
  this->m_cUpgradeStep = cUpgradeStep;
  CSiegeObject::UpdateObjectInfo(this, 0);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 26;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481780) --------------------------------------------------------
char __thiscall CSiegeObject::RepairCastleArms(CSiegeObject *this, unsigned __int16 wRepairHP)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned __int16 m_nNowHP; // ax
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_cUpgradeStep; // edx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType != 5434 && m_wObjectType != 5482 && m_wObjectType != 5386 )
    return 0;
  this->m_cState = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  this->m_CreatureStatus.m_nNowHP += wRepairHP;
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  m_cUpgradeStep = this->m_cUpgradeStep;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = m_cUpgradeStep;
  pktCC.m_cSubCmd = 28;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481840) --------------------------------------------------------
char __thiscall CSiegeObject::DestroyCastleArms(CSiegeObject *this)
{
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_nNowHP; // edx
  unsigned int m_dwOwnerID; // eax
  unsigned int v6; // ecx
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType != 5434 && m_wObjectType != 5482 && m_wObjectType != 5386 )
    return 0;
  this->m_wObjectType = 5385;
  this->m_cState = 7;
  this->m_cUpgradeStep = 0;
  CSiegeObject::UpdateObjectInfo(this, 0);
  this->m_CreatureStatus.m_nNowHP = 0;
  CSiegeObject::SetACTByObjectType(this);
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_dwOwnerID = this->m_dwOwnerID;
  pktCC.m_dwCastleObjectID = this->m_dwCID;
  v6 = this->m_wObjectType;
  pktCC.m_dwHP = m_nNowHP;
  pktCC.m_dwCastleID = m_dwOwnerID;
  pktCC.m_cState = this->m_cState;
  pktCC.m_dwValue = v6;
  pktCC.m_cSubCmd = 29;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
    CSiegeObject::SendToRadiusCell(this, (char *)&pktCC, 0x22u, 0xAAu);
  return 1;
}

//----- (00481910) --------------------------------------------------------
bool __thiscall CSiegeObject::UpdateSiegeArms(
        CSiegeObject *this,
        unsigned __int8 cState,
        unsigned int dwValue,
        unsigned __int8 cSubCmd)
{
  return 0;
}

//----- (00481920) --------------------------------------------------------
char __thiscall CSiegeObject::ChangeToStartKit(CSiegeObject *this)
{
  unsigned __int16 m_wObjectType; // ax
  Item::CItem *v3; // eax
  Item::CItem *v4; // ebp
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  CCreatureManager *v7; // eax
  CCharacter *Character; // eax
  CCharacter *v9; // esi
  CSendStream *m_lpGameClientDispatch; // eax
  int v12; // eax
  int v13; // eax
  unsigned __int16 v14; // ax
  Item::CItem *v15; // eax
  Item::CItem *v16; // ebp
  CCreatureManager *v17; // eax
  CCharacter *v18; // eax
  CCharacter *v19; // esi
  unsigned int m_dwGID; // [esp-4h] [ebp-84h]
  unsigned int m_dwCID; // [esp-4h] [ebp-84h]
  unsigned int m_dwOwnerID; // [esp-4h] [ebp-84h]
  unsigned __int16 lpDispatch; // [esp+10h] [ebp-70h]
  CGameClientDispatch *lpDispatcha; // [esp+10h] [ebp-70h]
  CCell::ItemInfo itemInfo; // [esp+14h] [ebp-6Ch] BYREF
  Guild::MemberInfo MasterInfo; // [esp+44h] [ebp-3Ch] BYREF

  m_wObjectType = this->m_wObjectType;
  if ( m_wObjectType == 5337 )
  {
    Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, 0x1BBCu);
    v4 = v3;
    if ( !v3 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSiegeObject::ChangeToStartKit",
        aDWorkRylSource_42,
        722,
        (char *)&byte_4F06B0);
      return 0;
    }
    m_dwGID = this->m_dwGID;
    memset(&itemInfo, 0, 12);
    itemInfo.cNum = 0;
    memset(&itemInfo.UID, 0, 18);
    itemInfo.lpItem = 0;
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
    if ( Guild
      && (Guild::CGuild::GetMaster(Guild, &MasterInfo),
          m_dwCID = MasterInfo.m_dwCID,
          v7 = CCreatureManager::GetInstance(),
          Character = CCreatureManager::GetCharacter(v7, m_dwCID),
          (v9 = Character) != 0)
      && (CCell::SetItem(
            Character->m_CellPos.m_lpCell,
            v4,
            &Character->m_CurrentPos,
            &itemInfo,
            Character->m_dwCID,
            Character->m_dwCID,
            0,
            1u,
            1),
          (m_lpGameClientDispatch = (CSendStream *)v9->m_lpGameClientDispatch) != 0) )
    {
      return GameClientSendPacket::SendCharAutoRouting(
               m_lpGameClientDispatch + 8,
               v9->m_dwCID,
               itemInfo.UID.nUniqueID,
               v4->m_ItemData.m_usProtoTypeID,
               1u,
               0);
    }
    else
    {
      return CCell::SetItem(this->m_CellPos.m_lpCell, v4, &this->m_CurrentPos, &itemInfo, 0, 0, 0, 1u, 0);
    }
  }
  else
  {
    if ( m_wObjectType != 5530 && m_wObjectType != 5578 && m_wObjectType != 5626 )
      return 0;
    v12 = m_wObjectType - 5530;
    if ( v12 )
    {
      v13 = v12 - 48;
      if ( v13 )
      {
        if ( v13 == 48 )
          v14 = 7103;
        else
          v14 = lpDispatch;
      }
      else
      {
        v14 = 7102;
      }
    }
    else
    {
      v14 = 7101;
    }
    Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v14);
    v16 = v15;
    if ( !v15 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSiegeObject::ChangeToStartKit",
        aDWorkRylSource_42,
        769,
        (char *)&byte_4F06B0);
      return 0;
    }
    m_dwOwnerID = this->m_dwOwnerID;
    memset(&itemInfo, 0, 12);
    itemInfo.cNum = 0;
    memset(&itemInfo.UID, 0, 18);
    itemInfo.lpItem = 0;
    v17 = CCreatureManager::GetInstance();
    v18 = CCreatureManager::GetCharacter(v17, m_dwOwnerID);
    v19 = v18;
    if ( v18
      && (CCell::SetItem(
            v18->m_CellPos.m_lpCell,
            v16,
            &v18->m_CurrentPos,
            &itemInfo,
            v18->m_dwCID,
            v18->m_dwCID,
            0,
            1u,
            1),
          (lpDispatcha = v19->m_lpGameClientDispatch) != 0) )
    {
      CSiegeObject::GiveBackSiegeMaterial(this);
      return GameClientSendPacket::SendCharAutoRouting(
               &lpDispatcha->m_SendStream,
               v19->m_dwCID,
               itemInfo.UID.nUniqueID,
               v16->m_ItemData.m_usProtoTypeID,
               1u,
               0);
    }
    else
    {
      CSiegeObject::GiveBackSiegeMaterial(this);
      return CCell::SetItem(this->m_CellPos.m_lpCell, v16, &this->m_CurrentPos, &itemInfo, 0, 0, 0, 1u, 0);
    }
  }
}
// 481953: variable 'v3' is possibly undefined
// 481A84: variable 'lpDispatch' is possibly undefined
// 481A94: variable 'v15' is possibly undefined

//----- (00481BC0) --------------------------------------------------------
void __thiscall CSiegeObject::SetRight(CSiegeObject *this, CampRight campRight)
{
  CampRight *p_m_CampRight; // esi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // ebx
  int v6; // ecx
  unsigned int m_dwCID; // eax
  int v8; // edx
  unsigned int m_dwGID; // [esp-4h] [ebp-34h]
  PktCampRight pktCR; // [esp+Ch] [ebp-24h] BYREF

  p_m_CampRight = &this->m_CampRight;
  this->m_CampRight = campRight;
  m_dwGID = this->m_dwGID;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
  if ( Guild )
  {
    v6 = *(_DWORD *)p_m_CampRight->m_aryCampRight;
    m_dwCID = this->m_dwCID;
    memset(&pktCR.m_CampRight, 1, sizeof(pktCR.m_CampRight));
    v8 = *(_DWORD *)&this->m_CampRight.m_aryCampRight[4];
    *(_DWORD *)pktCR.m_CampRight.m_aryCampRight = v6;
    pktCR.m_dwCampID = m_dwCID;
    LOWORD(m_dwCID) = *(_WORD *)&this->m_CampRight.m_aryCampRight[8];
    pktCR.m_dwCID = 0;
    *(_DWORD *)&pktCR.m_CampRight.m_aryCampRight[4] = v8;
    *(_WORD *)&pktCR.m_CampRight.m_aryCampRight[8] = m_dwCID;
    if ( PacketWrap::WrapCrypt((char *)&pktCR, 0x1Eu, 0xAEu, 0, 0) )
      Guild::CGuild::SendAllMember(Guild, (char *)&pktCR, 0x1Eu, 0xAEu);
  }
}

//----- (00481C80) --------------------------------------------------------
void __thiscall std::list<LogBuffer *>::~list<LogBuffer *>(std::list<CThread *> *this)
{
  std::list<IOPCode *>::clear(this);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
}

//----- (00481CA0) --------------------------------------------------------
void __thiscall CSiegeObject::~CSiegeObject(CSiegeObject *this)
{
  std::list<unsigned long> *p_m_ProtectCharList; // edi

  p_m_ProtectCharList = &this->m_ProtectCharList;
  this->__vftable = (CSiegeObject_vtbl *)&CSiegeObject::`vftable';
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_ProtectCharList);
  operator delete(p_m_ProtectCharList->_Myhead);
  p_m_ProtectCharList->_Myhead = 0;
  CMageMonster::~CMageMonster((CNamedMonster *)this);
}
// 4F06E0: using guessed type void *CSiegeObject::`vftable';

//----- (00481CE0) --------------------------------------------------------
void __thiscall CSiegeObject::DeleteProtectGate(CSiegeObject *this, unsigned int dwCID)
{
  std::_List_nod<unsigned long>::_Node *Myhead; // ecx
  std::_List_nod<unsigned long>::_Node *i; // eax

  Myhead = this->m_ProtectCharList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    if ( i->_Myval == dwCID )
      break;
  }
  if ( i != this->m_ProtectCharList._Myhead )
  {
    i->_Prev->_Next = i->_Next;
    i->_Next->_Prev = i->_Prev;
    operator delete(i);
    --this->m_ProtectCharList._Mysize;
  }
}

//----- (00481D40) --------------------------------------------------------
void __thiscall CSiegeObject::CSiegeObject(
        CSiegeObject *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        const CastleObjectInfo *CastleObject)
{
  bool v4; // zf

  CSkillMonster::CSkillMonster(this, MonsterCreate, 0);
  this->__vftable = (CSiegeObject_vtbl *)&CSiegeObject::`vftable';
  this->m_ProtectCharList._Myhead = (std::_List_nod<unsigned long>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_ProtectCharList);
  this->m_ProtectCharList._Mysize = 0;
  this->m_dwOwnerID = CastleObject->m_dwCastleID;
  this->m_dwGID = CastleObject->m_dwGID;
  this->m_wObjectType = CastleObject->m_wObjectType;
  this->m_cState = CastleObject->m_cState;
  this->m_cSubState = CastleObject->m_cSubState;
  this->m_cUpgradeStep = CastleObject->m_cUpgradeStep;
  this->m_cUpgradeType = CastleObject->m_cUpgradeType;
  this->m_fDefaultDir = CastleObject->m_fDefaultDir;
  *(_DWORD *)this->m_CampRight.m_aryCampRight = ********;
  *(_DWORD *)&this->m_CampRight.m_aryCampRight[4] = ********;
  *(_WORD *)&this->m_CampRight.m_aryCampRight[8] = 257;
  this->m_dwRideCID[0] = 0;
  this->m_dwRideCID[1] = 0;
  this->m_dwRideCID[2] = 0;
  this->m_dwRideCID[3] = 0;
  this->m_dwRideCID[4] = 0;
  this->m_dwRideCID[5] = 0;
  this->m_dwRideCID[6] = 0;
  this->m_dwRideCID[7] = 0;
  this->m_dwRideCID[8] = 0;
  this->m_dwRideCID[9] = 0;
  CMonster::InitMonster(this, &MonsterCreate->m_Pos, LOGINOUT);
  v4 = this->m_wObjectType == 5000;
  this->m_MotionInfo.m_fDirection = this->m_fDefaultDir;
  this->m_CreatureStatus.m_nNowHP = CastleObject->m_dwHP;
  if ( v4 )
    this->m_cSubState = 1;
  CSiegeObject::SetACTByObjectType(this);
}
// 4F06E0: using guessed type void *CSiegeObject::`vftable';

//----- (00481E90) --------------------------------------------------------
CSiegeObject *__thiscall CSiegeObject::`vector deleting destructor'(CSiegeObject *this, char a2)
{
  CSiegeObject::~CSiegeObject(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00481EB0) --------------------------------------------------------
void __thiscall CSiegeObject::CSiegeObject(
        CSiegeObject *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        unsigned int dwCampID,
        unsigned int dwGID,
        unsigned __int16 dwHP,
        unsigned __int8 cState,
        unsigned __int8 cUpgradeStep,
        bool bFullHP)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, 0);
  this->__vftable = (CSiegeObject_vtbl *)&CSiegeObject::`vftable';
  this->m_ProtectCharList._Myhead = (std::_List_nod<unsigned long>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_ProtectCharList);
  this->m_ProtectCharList._Mysize = 0;
  this->m_dwOwnerID = dwCampID;
  this->m_dwGID = dwGID;
  this->m_cState = cState;
  this->m_wObjectType = 5337;
  this->m_cSubState = 0;
  this->m_cUpgradeStep = cUpgradeStep;
  this->m_cUpgradeType = 0;
  this->m_fDefaultDir = 0.0;
  *(_DWORD *)this->m_CampRight.m_aryCampRight = ********;
  *(_DWORD *)&this->m_CampRight.m_aryCampRight[4] = ********;
  *(_WORD *)&this->m_CampRight.m_aryCampRight[8] = 257;
  this->m_dwRideCID[0] = 0;
  this->m_dwRideCID[1] = 0;
  this->m_dwRideCID[2] = 0;
  this->m_dwRideCID[3] = 0;
  this->m_dwRideCID[4] = 0;
  this->m_dwRideCID[5] = 0;
  this->m_dwRideCID[6] = 0;
  this->m_dwRideCID[7] = 0;
  this->m_dwRideCID[8] = 0;
  this->m_dwRideCID[9] = 0;
  CMonster::InitMonster(this, &MonsterCreate->m_Pos, LOGINOUT);
  if ( bFullHP )
    this->m_CreatureStatus.m_nNowHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  else
    this->m_CreatureStatus.m_nNowHP = dwHP;
  CSiegeObject::SetACTByObjectType(this);
}
// 4F06E0: using guessed type void *CSiegeObject::`vftable';

//----- (00481FF0) --------------------------------------------------------
void __thiscall CSiegeObject::CSiegeObject(
        CSiegeObject *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        unsigned int dwOwnerID,
        unsigned int dwGID,
        unsigned int dwHP,
        unsigned __int16 wObjectType,
        unsigned __int8 cState,
        unsigned __int8 cUpgradeStep)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, 0);
  this->__vftable = (CSiegeObject_vtbl *)&CSiegeObject::`vftable';
  this->m_ProtectCharList._Myhead = (std::_List_nod<unsigned long>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_ProtectCharList);
  this->m_ProtectCharList._Mysize = 0;
  this->m_dwOwnerID = dwOwnerID;
  this->m_dwGID = dwGID;
  this->m_wObjectType = wObjectType;
  this->m_cState = cState;
  this->m_cUpgradeStep = cUpgradeStep;
  this->m_cSubState = 0;
  this->m_cUpgradeType = 0;
  this->m_fDefaultDir = 0.0;
  *(_DWORD *)this->m_CampRight.m_aryCampRight = ********;
  *(_DWORD *)&this->m_CampRight.m_aryCampRight[4] = ********;
  *(_WORD *)&this->m_CampRight.m_aryCampRight[8] = 257;
  this->m_dwRideCID[0] = 0;
  this->m_dwRideCID[1] = 0;
  this->m_dwRideCID[2] = 0;
  this->m_dwRideCID[3] = 0;
  this->m_dwRideCID[4] = 0;
  this->m_dwRideCID[5] = 0;
  this->m_dwRideCID[6] = 0;
  this->m_dwRideCID[7] = 0;
  this->m_dwRideCID[8] = 0;
  this->m_dwRideCID[9] = 0;
  CMonster::InitMonster(this, &MonsterCreate->m_Pos, LOGINOUT);
  this->m_CreatureStatus.m_nNowHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  CSiegeObject::SetACTByObjectType(this);
}
// 4F06E0: using guessed type void *CSiegeObject::`vftable';

//----- (00482120) --------------------------------------------------------
void __thiscall std::list<unsigned long>::_Incsize(std::list<unsigned long> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004821C0) --------------------------------------------------------
void __thiscall CSiegeObject::AddProtectGate(CSiegeObject *this, unsigned int dwCID)
{
  std::_List_nod<unsigned long>::_Node *Myhead; // edx
  std::_List_nod<unsigned long>::_Node *i; // eax
  std::_List_nod<CThread *>::_Node *v4; // edi
  std::list<unsigned long> *p_m_ProtectCharList; // esi
  std::_List_nod<CThread *>::_Node *v6; // ebx

  Myhead = this->m_ProtectCharList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    if ( i->_Myval == dwCID )
      break;
  }
  if ( i == this->m_ProtectCharList._Myhead )
  {
    v4 = (std::_List_nod<CThread *>::_Node *)this->m_ProtectCharList._Myhead;
    p_m_ProtectCharList = &this->m_ProtectCharList;
    v6 = std::list<IOPCode *>::_Buynode(
           (std::list<CThread *> *)&this->m_ProtectCharList,
           v4,
           v4->_Prev,
           (CThread **)&dwCID);
    std::list<unsigned long>::_Incsize(p_m_ProtectCharList, 1u);
    v4->_Prev = v6;
    v6->_Prev->_Next = v6;
  }
}

//----- (00482220) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharStartQuest(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned int dwNPCID,
        unsigned __int16 wQuestID,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x16);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwNPCID;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((_WORD *)Buffer + 10) = wQuestID;
  return CSendStream::WrapCrypt(SendStream, 0x16u, 0x6Au, 0, wError);
}

//----- (00482270) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharOperateTrigger(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int16 wQuestID,
        unsigned __int8 cPhase,
        unsigned __int8 cTrigger,
        unsigned __int8 cCount,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[18] = cPhase;
  *((_WORD *)Buffer + 8) = wQuestID;
  Buffer[19] = cTrigger;
  if ( wError )
    ++cCount;
  Buffer[20] = cCount;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x6Bu, 0, wError);
}

//----- (004822D0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharQuestInfo(CSendStream *SendStream, CCharacter *lpCharacter)
{
  unsigned int m_dwUID; // eax
  int v3; // esi
  unsigned __int8 *p_m_cPhase; // ecx
  __int16 v5; // dx
  int v6; // eax
  char *v7; // eax
  __int16 v8; // dx
  int v9; // edx
  unsigned __int16 *v10; // eax
  __int16 v11; // cx
  __int16 v12; // cx
  unsigned __int16 v13; // cx
  unsigned __int16 v14; // cx
  unsigned __int16 v15; // cx
  char szBuffer[360]; // [esp+Ch] [ebp-16Ch] BYREF

  m_dwUID = lpCharacter->m_dwUID;
  *(_DWORD *)&szBuffer[22] = lpCharacter->m_dwCID;
  *(_DWORD *)&szBuffer[18] = m_dwUID;
  *(_WORD *)&szBuffer[26] = 0;
  *(_WORD *)&szBuffer[28] = 0;
  v3 = 0;
  p_m_cPhase = &lpCharacter->m_ExecutingQuest[0].m_cPhase;
  do
  {
    v5 = *((_WORD *)p_m_cPhase - 1);
    if ( !v5 )
      break;
    if ( !*(_DWORD *)(p_m_cPhase + 14) )
      break;
    v6 = *(unsigned __int16 *)&szBuffer[26];
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30] = v5;
    v7 = &szBuffer[v6 + 30];
    v7[2] = *p_m_cPhase;
    v7 += 3;
    *(_DWORD *)v7 = *(_DWORD *)(p_m_cPhase + 1);
    v8 = *(_WORD *)(p_m_cPhase + 9);
    *((_DWORD *)v7 + 1) = *(_DWORD *)(p_m_cPhase + 5);
    *((_WORD *)v7 + 4) = v8;
    *(_WORD *)&szBuffer[26] += 13;
    ++v3;
    p_m_cPhase += 20;
  }
  while ( v3 < 10 );
  v9 = 0;
  v10 = &lpCharacter->m_wHistoryQuest[2];
  do
  {
    v11 = *(v10 - 2);
    if ( !v11 )
      break;
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30 + *(unsigned __int16 *)&szBuffer[28]] = v11;
    v12 = *(v10 - 1);
    *(_WORD *)&szBuffer[28] += 2;
    if ( !v12 )
      break;
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30 + *(unsigned __int16 *)&szBuffer[28]] = v12;
    v13 = *v10;
    *(_WORD *)&szBuffer[28] += 2;
    if ( !v13 )
      break;
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30 + *(unsigned __int16 *)&szBuffer[28]] = v13;
    v14 = v10[1];
    *(_WORD *)&szBuffer[28] += 2;
    if ( !v14 )
      break;
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30 + *(unsigned __int16 *)&szBuffer[28]] = v14;
    v15 = v10[2];
    *(_WORD *)&szBuffer[28] += 2;
    if ( !v15 )
      break;
    *(_WORD *)&szBuffer[*(unsigned __int16 *)&szBuffer[26] + 30 + *(unsigned __int16 *)&szBuffer[28]] = v15;
    *(_WORD *)&szBuffer[28] += 2;
    v9 += 5;
    v10 += 5;
  }
  while ( v9 < 100 );
  return CSendStream::WrapCompress(
           SendStream,
           szBuffer,
           (char *)(*(_DWORD *)&szBuffer[28] + *(_DWORD *)&szBuffer[26] + 30),
           0x6Cu,
           0,
           0);
}

//----- (00482460) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharEndQuest(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int16 wQuestID,
        bool bSave)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x13);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[18] = bSave;
  *((_WORD *)Buffer + 8) = wQuestID;
  return CSendStream::WrapCrypt(SendStream, 0x13u, 0x6Du, 0, 0);
}

//----- (004824A0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCancelQuest(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int16 wQuestID,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x12);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((_WORD *)Buffer + 8) = wQuestID;
  return CSendStream::WrapCrypt(SendStream, 0x12u, 0x79u, 0, wError);
}

//----- (004824E0) --------------------------------------------------------
void __thiscall CTempCharacter::CTempCharacter(CTempCharacter *this)
{
  char *m_szCharacterName; // ecx

  this->m_dwUID = 0;
  this->m_dwCID = 0;
  this->m_nDataRequestCount = 0;
  this->m_cFlag = 0;
  this->m_cGroup = -1;
  m_szCharacterName = this->m_szCharacterName;
  *(_DWORD *)m_szCharacterName = 0;
  *((_DWORD *)m_szCharacterName + 1) = 0;
  *((_DWORD *)m_szCharacterName + 2) = 0;
  *((_DWORD *)m_szCharacterName + 3) = 0;
  this->m_CharInfoEX.Total = 0;
  this->m_CharInfoEX.ServerID = 0;
  memset(&this->m_Quest, 0, 0x84u);
  *(_WORD *)&this->m_Quest.Data[128] = 0;
  memset(&this->m_History, 0, sizeof(this->m_History));
  memset(&this->m_Config, 0, 0x34u);
  *(_WORD *)&this->m_Config.Data[48] = 0;
  *(_DWORD *)this->m_StoreInfo.Password = 0;
  *(_DWORD *)&this->m_StoreInfo.Password[4] = 0;
  *(unsigned int *)((char *)&this->m_StoreInfo.Flag + 3) = 0;
  HIBYTE(this->m_StoreInfo.Gold) = 0;
}

//----- (00482560) --------------------------------------------------------
boost::simple_segregated_storage<unsigned int> *__thiscall boost::pool<boost::default_user_allocator_new_delete>::ordered_malloc_need_resize(
        boost::pool<boost::default_user_allocator_new_delete> *this)
{
  unsigned int requested_size; // edi
  unsigned int v3; // ecx
  unsigned int v4; // eax
  int v5; // edx
  unsigned int v6; // esi
  unsigned int v7; // ebp
  unsigned int v8; // esi
  boost::simple_segregated_storage<unsigned int> *result; // eax
  char *v10; // edi
  char *ptr; // eax
  unsigned int sz; // ecx
  char *i; // edx
  int v14; // ebp
  char **v15; // edx
  unsigned int *v16; // eax
  unsigned int v17; // ecx

  requested_size = this->requested_size;
  v3 = 4;
  v4 = requested_size;
  do
  {
    v5 = v4 % v3;
    v6 = v3;
    v4 = v3;
    v3 = v5;
  }
  while ( v5 );
  v7 = 4 * (requested_size / v6);
  v8 = v7 * this->next_size + 8;
  result = (boost::simple_segregated_storage<unsigned int> *)operator new[](v8, &std::nothrow);
  v10 = (char *)result;
  if ( result )
  {
    this->next_size *= 2;
    boost::simple_segregated_storage<unsigned int>::add_block(this, result, v8 - 8, v7);
    ptr = this->list.ptr;
    if ( ptr && ptr <= v10 )
    {
      sz = this->list.sz;
      for ( i = *(char **)&ptr[sz - 8]; i; i = *(char **)&i[sz - 8] )
      {
        if ( i > v10 )
          break;
        sz = *(_DWORD *)&ptr[sz - 4];
        ptr = i;
      }
      v14 = *(_DWORD *)&ptr[sz - 8];
      v15 = (char **)&ptr[sz - 8];
      v16 = (unsigned int *)&ptr[sz - 4];
      v17 = *v16;
      *(_DWORD *)&v10[v8 - 8] = v14;
      *(_DWORD *)&v10[v8 - 4] = v17;
      *v15 = v10;
      *v16 = v8;
      result = (boost::simple_segregated_storage<unsigned int> *)this->first;
      this->first = *(void **)this->first;
    }
    else
    {
      *(_DWORD *)&v10[v8 - 8] = this->list.ptr;
      *(_DWORD *)&v10[v8 - 4] = this->list.sz;
      result = (boost::simple_segregated_storage<unsigned int> *)this->first;
      this->list.ptr = v10;
      this->list.sz = v8;
      this->first = result->first;
    }
  }
  return result;
}

//----- (00482630) --------------------------------------------------------
CTempCharacter *__thiscall boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::construct(
        boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete> *this)
{
  CTempCharacter *result; // eax
  CTempCharacter *v2; // esi

  result = (CTempCharacter *)this->first;
  if ( this->first )
    this->first = (void *)result->m_dwUID;
  else
    result = (CTempCharacter *)boost::pool<boost::default_user_allocator_new_delete>::ordered_malloc_need_resize(this);
  v2 = result;
  if ( result )
  {
    CTempCharacter::CTempCharacter(result);
    return v2;
  }
  return result;
}

//----- (00482660) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,CTempCharacter *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CTempCharacter *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0xFFFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CTempCharacter *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00482810) --------------------------------------------------------
char __thiscall CTempCharacterMgr::EraseChar(CTempCharacterMgr *this, unsigned int dwBattleCID)
{
  std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_mapTempChar; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v5; // ebp
  CTempCharacter **first; // ecx
  CTempCharacter *second; // edx
  CTempCharacter *i; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator v9; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *k; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // eax
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator> result; // [esp+10h] [ebp-8h] BYREF

  p_m_mapTempChar = &this->m_mapTempChar;
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    &this->m_mapTempChar,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&result,
    &dwBattleCID);
  Ptr = result.first._Ptr;
  v5 = result.second._Ptr;
  while ( Ptr != v5 )
  {
    first = (CTempCharacter **)this->m_tempCharPool.first;
    second = Ptr->_Myval.second;
    if ( this->m_tempCharPool.first && first <= (CTempCharacter **)second )
    {
      for ( i = *first; i; i = (CTempCharacter *)i->m_dwUID )
      {
        if ( i > second )
          break;
        first = (CTempCharacter **)i;
      }
      second->m_dwUID = (unsigned int)*first;
      *first = second;
    }
    else
    {
      second->m_dwUID = (unsigned int)first;
      this->m_tempCharPool.first = second;
    }
    v9._Ptr = Ptr;
    if ( !Ptr->_Isnil )
    {
      Right = Ptr->_Right;
      if ( Right->_Isnil )
      {
        for ( j = Ptr->_Parent; !j->_Isnil; j = j->_Parent )
        {
          if ( Ptr != j->_Right )
            break;
          Ptr = j;
        }
        Ptr = j;
      }
      else
      {
        Ptr = Ptr->_Right;
        for ( k = Right->_Left; !k->_Isnil; k = k->_Left )
          Ptr = k;
      }
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
      p_m_mapTempChar,
      (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *)&dwBattleCID,
      v9);
  }
  return 1;
}

//----- (004828E0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *result,
        const std::pair<unsigned long const ,CTempCharacter *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // esi
  unsigned int first; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *v7; // eax
  bool _Addleft; // [esp+8h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = Parent;
      _Addleft = first < Parent->_Myval.first;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
          this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *)&_Val,
          _Addleft,
          Myhead,
          _Val)->_Ptr;
  v7 = result;
  result->first._Ptr = Ptr;
  result->second = 1;
  return v7;
}

//----- (00482950) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Where,
        std::pair<unsigned long const ,CTempCharacter *> *_Val)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // eax
  const std::pair<unsigned long const ,CTempCharacter *> *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Right; // eax
  CTempCharacter *first; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> v11; // [esp+8h] [ebp-8h] BYREF

  if ( !this->_Mysize )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
      this,
      result,
      1,
      this->_Myhead,
      _Val);
    return result;
  }
  Myhead = this->_Myhead;
  v7 = _Val;
  if ( _Where._Ptr == Myhead->_Left )
  {
    if ( _Where._Ptr->_Myval.first >= _Val->first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        _Val);
      return result;
    }
    goto LABEL_22;
  }
  if ( _Where._Ptr == Myhead )
  {
    Right = Myhead->_Right;
    if ( _Val->first >= Right->_Myval.first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        0,
        Right,
        _Val);
      return result;
    }
    goto LABEL_22;
  }
  first = (CTempCharacter *)_Val->first;
  if ( _Where._Ptr->_Myval.first < _Val->first
    || (_Val = (std::pair<unsigned long const ,CTempCharacter *> *)_Where._Ptr,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val),
        first < _Val[1].second) )
  {
    if ( (unsigned int)first < _Where._Ptr->_Myval.first
      || (_Val = (std::pair<unsigned long const ,CTempCharacter *> *)_Where._Ptr,
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Val),
          _Val != (std::pair<unsigned long const ,CTempCharacter *> *)this->_Myhead)
      && _Val[1].second < first )
    {
LABEL_22:
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
              this,
              &v11,
              v7)->first._Ptr;
      v5 = result;
      result->_Ptr = Ptr;
      return v5;
    }
    if ( _Where._Ptr->_Right->_Isnil )
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        0,
        _Where._Ptr,
        v7);
    else
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        1,
        (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)_Val,
        v7);
    return result;
  }
  else
  {
    if ( *(_BYTE *)(_Val[1].first + 21) )
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        0,
        (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)_Val,
        v7);
    else
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        v7);
    return result;
  }
}

//----- (00482AC0) --------------------------------------------------------
CTempCharacter *__thiscall CTempCharacterMgr::GetCharacter(
        CTempCharacterMgr *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwBattleCID,
        unsigned __int8 cGroup)
{
  std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_mapTempChar; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // esi
  unsigned __int8 v6; // bl
  CTempCharacter *second; // eax
  CTempCharacter *v8; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v9; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator> result; // [esp+10h] [ebp-8h] BYREF

  p_m_mapTempChar = &this->m_mapTempChar;
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    &this->m_mapTempChar,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&result,
    (const unsigned int *)&dwBattleCID);
  Ptr = result.first._Ptr;
  v6 = cGroup;
  if ( result.first._Ptr == result.second._Ptr )
  {
LABEL_4:
    v8 = boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::construct(&this->m_tempCharPool);
    if ( v8 )
    {
      v9 = dwBattleCID;
      v8->m_cGroup = v6;
      result.first._Ptr = v9;
      result.second._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v8;
      std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
        p_m_mapTempChar,
        (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *)&cGroup,
        (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)Ptr,
        (std::pair<unsigned long const ,CTempCharacter *> *)&result);
    }
    return v8;
  }
  else
  {
    while ( 1 )
    {
      second = Ptr->_Myval.second;
      if ( v6 == second->m_cGroup )
        break;
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&result);
      Ptr = result.first._Ptr;
      if ( result.first._Ptr == result.second._Ptr )
        goto LABEL_4;
    }
  }
  return second;
}

//----- (00482B50) --------------------------------------------------------
void __thiscall CTempCharacterMgr::~CTempCharacterMgr(CTempCharacterMgr *this)
{
  CTempCharacterMgr *v1; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Left; // eax
  bool v4; // zf
  CTempCharacter **first; // ecx
  CTempCharacter *second; // edx
  CTempCharacter *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Parent; // ebp
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *p_m_mapTempChar; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v12; // ebp
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v13; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator v14; // [esp-8h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v15; // [esp-4h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator pos; // [esp+14h] [ebp-10h] BYREF
  int v18; // [esp+20h] [ebp-4h]

  v1 = this;
  Myhead = this->m_mapTempChar._Myhead;
  Left = Myhead->_Left;
  v4 = Myhead->_Left == Myhead;
  v18 = 1;
  pos._Ptr = Left;
  if ( !v4 )
  {
    do
    {
      first = (CTempCharacter **)v1->m_tempCharPool.first;
      second = Left->_Myval.second;
      if ( v1->m_tempCharPool.first && first <= (CTempCharacter **)second )
      {
        for ( i = *first; i; i = (CTempCharacter *)i->m_dwUID )
        {
          if ( i > second )
            break;
          first = (CTempCharacter **)i;
        }
        second->m_dwUID = (unsigned int)*first;
        *first = second;
      }
      else
      {
        second->m_dwUID = (unsigned int)first;
        v1->m_tempCharPool.first = second;
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
      Left = pos._Ptr;
      v1 = this;
    }
    while ( pos._Ptr != Myhead );
  }
  Parent = v1->m_mapTempChar._Myhead->_Parent;
  p_m_mapTempChar = &v1->m_mapTempChar;
  for ( j = Parent; !j->_Isnil; Parent = j )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      p_m_mapTempChar,
      j->_Right);
    j = j->_Left;
    if ( (__S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
    {
      __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
      InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 24;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
      atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
    }
    EnterCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
    Parent->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = Parent;
    LeaveCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
  }
  p_m_mapTempChar->_Myhead->_Parent = p_m_mapTempChar->_Myhead;
  v11 = p_m_mapTempChar->_Myhead;
  p_m_mapTempChar->_Mysize = 0;
  v11->_Left = v11;
  p_m_mapTempChar->_Myhead->_Right = p_m_mapTempChar->_Myhead;
  v15 = p_m_mapTempChar->_Myhead;
  v14._Ptr = v15->_Left;
  LOBYTE(v18) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
    p_m_mapTempChar,
    &pos,
    v14,
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)v15);
  v12 = p_m_mapTempChar->_Myhead;
  v13 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v13->mtx);
  v12->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v13->p.first;
  v13->p.first = v12;
  LeaveCriticalSection(&v13->mtx);
  p_m_mapTempChar->_Myhead = 0;
  p_m_mapTempChar->_Mysize = 0;
  boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::~object_pool<CTempCharacter,boost::default_user_allocator_new_delete>(&this->m_tempCharPool);
}
// 52D3F0: using guessed type int __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (00482D00) --------------------------------------------------------
void __thiscall CSingleton<CPartyMgr>::~CSingleton<CPartyMgr>(CSingleton<CPartyMgr> *this)
{
  CSingleton<CPartyMgr>::ms_pSingleton = 0;
}

//----- (00482D10) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00482D50) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00482D90) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Rarg,
        const std::pair<unsigned long const ,unsigned long> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)operator new((tagHeader *)0x18);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Right = _Rarg;
    result->_Parent = _Parg;
    result->_Myval = *_Val;
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (00482DD0) --------------------------------------------------------
void __thiscall CPartyMgr::SendPartyFind(CPartyMgr *this, CCharacter *lpCharacter)
{
  CPartyMgr *v2; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node **Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Ptr; // ecx
  bool v5; // zf
  char *v6; // esi
  CCharacter *second; // edi
  int m_nLevel; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v9; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Left; // eax
  CParty *v11; // edi
  int v12; // ebx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  int v15; // eax
  unsigned int m_dwLeaderID; // [esp-10h] [ebp-47Ch]
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+4h] [ebp-468h] BYREF
  int nPartyFindMemberNum; // [esp+8h] [ebp-464h]
  int nMemberFindPartyNum; // [esp+Ch] [ebp-460h]
  int nMyLevel; // [esp+10h] [ebp-45Ch]
  CPartyMgr *v21; // [esp+14h] [ebp-458h]
  CGameClientDispatch *lpDispatch; // [esp+18h] [ebp-454h]
  char szBuffer[1098]; // [esp+1Ch] [ebp-450h] BYREF

  v2 = this;
  v21 = this;
  if ( lpCharacter )
  {
    lpDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( lpDispatch )
    {
      nMyLevel = lpCharacter->m_CreatureStatus.m_nLevel;
      Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node **)this->m_MemberFindPartyMap._Myhead;
      nMemberFindPartyNum = 0;
      nPartyFindMemberNum = 0;
      Ptr = *Myhead;
      v5 = *Myhead == (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)Myhead;
      v6 = &szBuffer[18];
      it._Ptr = *Myhead;
      if ( !v5 )
      {
        do
        {
          if ( nMemberFindPartyNum >= 30 )
            break;
          second = (CCharacter *)Ptr->_Myval.second;
          if ( second )
          {
            if ( lpCharacter != second )
            {
              m_nLevel = second->m_CreatureStatus.m_nLevel;
              if ( (int)abs32(m_nLevel - nMyLevel) <= 10 )
              {
                strncpy(v6, second->m_DBData.m_Info.Name, 0x10u);
                v6[16] = m_nLevel;
                v6[17] = second->GetClass(second);
                v6 += 18;
                ++nMemberFindPartyNum;
              }
            }
          }
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&it);
          v2 = v21;
          Ptr = it._Ptr;
        }
        while ( it._Ptr != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)v21->m_MemberFindPartyMap._Myhead );
      }
      v9 = v2->m_PartyFindMemberMap._Myhead;
      Left = v9->_Left;
      it._Ptr = v9->_Left;
      if ( it._Ptr != v9 )
      {
        do
        {
          if ( nPartyFindMemberNum >= 30 )
            break;
          v11 = Left->_Myval.second;
          if ( v11 )
          {
            if ( !CParty::IsMember(v11, lpCharacter->m_dwCID) )
            {
              v12 = v11->GetLoggedMemberAverageLevel(v11);
              if ( (int)abs32(v12 - nMyLevel) <= 10 )
              {
                m_dwLeaderID = v11->m_Party.m_dwLeaderID;
                Instance = CCreatureManager::GetInstance();
                Character = CCreatureManager::GetCharacter(Instance, m_dwLeaderID);
                if ( Character )
                {
                  strncpy(v6, Character->m_DBData.m_Info.Name, 0x10u);
                  v15 = nPartyFindMemberNum;
                  v6[16] = v12;
                  v6[17] = v11->m_Party.m_cMemberNum;
                  v6 += 18;
                  nPartyFindMemberNum = v15 + 1;
                }
              }
            }
          }
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&it);
          Left = it._Ptr;
        }
        while ( it._Ptr != v21->m_PartyFindMemberMap._Myhead );
      }
      *(_DWORD *)&szBuffer[12] = lpCharacter->m_dwCID;
      szBuffer[17] = nPartyFindMemberNum;
      szBuffer[16] = nMemberFindPartyNum;
      CSendStream::WrapCompress(
        &lpDispatch->m_SendStream,
        szBuffer,
        (char *)(18 * (nMemberFindPartyNum + nPartyFindMemberNum + 1)),
        0x51u,
        0,
        0);
    }
  }
}

//----- (00482FA0) --------------------------------------------------------
void __thiscall CPartyMgr::DestoryPartyList(CPartyMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Left; // eax
  CParty *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Parent; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *i; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+Ch] [ebp-4h] BYREF

  Myhead = this->m_PartyMap._Myhead;
  Left = Myhead->_Left;
  it._Ptr = Myhead->_Left;
  if ( it._Ptr != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( second )
        ((void (__thiscall *)(CParty *, int))second->~CParty)(second, 1);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&it);
      Left = it._Ptr;
    }
    while ( it._Ptr != this->m_PartyMap._Myhead );
  }
  Parent = this->m_PartyMap._Myhead->_Parent;
  for ( i = Parent; !i->_Isnil; Parent = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Erase(
      &this->m_PartyMap,
      i->_Right);
    i = i->_Left;
    operator delete(Parent);
  }
  this->m_PartyMap._Myhead->_Parent = this->m_PartyMap._Myhead;
  v7 = this->m_PartyMap._Myhead;
  this->m_PartyMap._Mysize = 0;
  v7->_Left = v7;
  this->m_PartyMap._Myhead->_Right = this->m_PartyMap._Myhead;
}

//----- (00483020) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CCharacter *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CCharacter *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004831D0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00483290) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)_Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00483550) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *)this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00483610) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
          this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004836D0) --------------------------------------------------------
bool __thiscall CPartyMgr::AddParty(CPartyMgr *this, CParty *pParty)
{
  CParty *v2; // ebx
  CParty *m_dwPartyID; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+Ch] [ebp-10h] BYREF
  CParty *v7; // [esp+10h] [ebp-Ch]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator,bool> result; // [esp+14h] [ebp-8h] BYREF

  v2 = pParty;
  m_dwPartyID = (CParty *)pParty->m_Party.m_dwPartyID;
  pParty = m_dwPartyID;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    (const unsigned int *)&pParty);
  if ( it._Ptr != this->m_PartyMap._Myhead )
    return 0;
  it._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)m_dwPartyID;
  v7 = v2;
  return std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::insert(
           (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *)this,
           (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *)&result,
           (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)&it)->second;
}

//----- (00483730) --------------------------------------------------------
char __thiscall CPartyMgr::DeleteParty(CPartyMgr *this, unsigned int dwPartyUID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Ptr; // esi
  CParty *second; // ecx
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+8h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    &dwPartyUID);
  Ptr = it._Ptr;
  if ( it._Ptr == this->m_PartyMap._Myhead )
    return 0;
  second = it._Ptr->_Myval.second;
  if ( second )
    ((void (__thiscall *)(CParty *, int))second->~CParty)(second, 1);
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&dwPartyUID,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator)Ptr);
  return 1;
}

//----- (00483780) --------------------------------------------------------
bool __thiscall CPartyMgr::AddFindPartyList(
        CPartyMgr *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *dwCID)
{
  std::map<unsigned long,CCharacter *> *p_m_MemberFindPartyMap; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v4; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int v8; // [esp-4h] [ebp-1Ch]
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator it; // [esp+8h] [ebp-10h] BYREF
  CCharacter *v10; // [esp+Ch] [ebp-Ch]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator,bool> result; // [esp+10h] [ebp-8h] BYREF

  p_m_MemberFindPartyMap = &this->m_MemberFindPartyMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MemberFindPartyMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    (const unsigned int *)&dwCID);
  if ( it._Ptr != this->m_MemberFindPartyMap._Myhead )
    return 0;
  v4 = dwCID;
  v8 = (unsigned int)dwCID;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v8);
  if ( !Character )
    return 0;
  v10 = Character;
  it._Ptr = v4;
  return std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::insert(
           p_m_MemberFindPartyMap,
           (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *)&result,
           (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)&it)->second;
}

//----- (004837F0) --------------------------------------------------------
char __thiscall CPartyMgr::DeleteFindPartyList(CPartyMgr *this, unsigned int dwCID)
{
  std::map<unsigned long,CCharacter *> *p_m_MemberFindPartyMap; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator it; // [esp+8h] [ebp-4h] BYREF

  p_m_MemberFindPartyMap = &this->m_MemberFindPartyMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MemberFindPartyMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    &dwCID);
  if ( it._Ptr == this->m_MemberFindPartyMap._Myhead )
    return 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
    p_m_MemberFindPartyMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&dwCID,
    it);
  return 1;
}

//----- (00483830) --------------------------------------------------------
bool __thiscall CPartyMgr::AddFindMemberList(
        CPartyMgr *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *dwPartyUID)
{
  std::map<unsigned long,CParty *> *p_m_PartyFindMemberMap; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v4; // esi
  CPartyMgr *v5; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+8h] [ebp-10h] BYREF
  CParty *second; // [esp+Ch] [ebp-Ch]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator,bool> result; // [esp+10h] [ebp-8h] BYREF

  p_m_PartyFindMemberMap = &this->m_PartyFindMemberMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_PartyFindMemberMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    (const unsigned int *)&dwPartyUID);
  if ( it._Ptr != this->m_PartyFindMemberMap._Myhead )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CPartyMgr::AddFindMemberList",
      aDWorkRylSource_82,
      113,
      aPid0x08x_3,
      dwPartyUID);
    return 0;
  }
  v4 = dwPartyUID;
  v5 = CSingleton<CPartyMgr>::ms_pSingleton;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)CSingleton<CPartyMgr>::ms_pSingleton,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    (const unsigned int *)&dwPartyUID);
  if ( it._Ptr == v5->m_PartyMap._Myhead || !it._Ptr->_Myval.second )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CPartyMgr::AddFindMemberList", aDWorkRylSource_82, 120, aPid0x08x_6, v4);
    return 0;
  }
  second = it._Ptr->_Myval.second;
  it._Ptr = v4;
  return std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::insert(
           (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *)p_m_PartyFindMemberMap,
           (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *)&result,
           (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)&it)->second;
}

//----- (004838F0) --------------------------------------------------------
char __thiscall CPartyMgr::DeleteFindMemberList(CPartyMgr *this, unsigned int dwPartyUID)
{
  std::map<unsigned long,CParty *> *p_m_PartyFindMemberMap; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+8h] [ebp-4h] BYREF

  p_m_PartyFindMemberMap = &this->m_PartyFindMemberMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_PartyFindMemberMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    &dwPartyUID);
  if ( it._Ptr == this->m_PartyFindMemberMap._Myhead )
    return 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *)p_m_PartyFindMemberMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *)&dwPartyUID,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator)it._Ptr);
  return 1;
}

//----- (00483930) --------------------------------------------------------
void __thiscall std::map<unsigned long,CParty *>::~map<unsigned long,CParty *>(std::map<unsigned long,CParty *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00483960) --------------------------------------------------------
void __thiscall std::map<unsigned long,CCharacter *>::~map<unsigned long,CCharacter *>(
        std::map<unsigned long,CCharacter *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00483990) --------------------------------------------------------
void __thiscall CPartyMgr::~CPartyMgr(CPartyMgr *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator v2; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator v3; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator v4; // [esp-8h] [ebp-28h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *Myhead; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v6; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v7; // [esp-4h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator result; // [esp+10h] [ebp-10h] BYREF
  int v9; // [esp+1Ch] [ebp-4h]

  v9 = 3;
  CPartyMgr::DestoryPartyList(this);
  Myhead = this->m_PartyFindMemberMap._Myhead;
  v2._Ptr = Myhead->_Left;
  LOBYTE(v9) = 2;
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::erase(
    &this->m_PartyFindMemberMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator)Myhead);
  operator delete(this->m_PartyFindMemberMap._Myhead);
  this->m_PartyFindMemberMap._Myhead = 0;
  this->m_PartyFindMemberMap._Mysize = 0;
  v6 = this->m_MemberFindPartyMap._Myhead;
  v3._Ptr = v6->_Left;
  LOBYTE(v9) = 1;
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(
    &this->m_MemberFindPartyMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *)&result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator)v6);
  operator delete(this->m_MemberFindPartyMap._Myhead);
  this->m_MemberFindPartyMap._Myhead = 0;
  this->m_MemberFindPartyMap._Mysize = 0;
  v7 = this->m_PartyMap._Myhead;
  v4._Ptr = v7->_Left;
  LOBYTE(v9) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::erase(
    &this->m_PartyMap,
    &result,
    v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator)v7);
  operator delete(this->m_PartyMap._Myhead);
  this->m_PartyMap._Myhead = 0;
  this->m_PartyMap._Mysize = 0;
  CSingleton<CPartyMgr>::ms_pSingleton = 0;
}

//----- (00483A60) --------------------------------------------------------
void __thiscall CPartyMgr::CPartyMgr(CPartyMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v3; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v4; // eax

  CSingleton<CPartyMgr>::ms_pSingleton = this;
  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_PartyMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_PartyMap._Myhead->_Parent = this->m_PartyMap._Myhead;
  this->m_PartyMap._Myhead->_Left = this->m_PartyMap._Myhead;
  this->m_PartyMap._Myhead->_Right = this->m_PartyMap._Myhead;
  this->m_PartyMap._Mysize = 0;
  v3 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_MemberFindPartyMap);
  this->m_MemberFindPartyMap._Myhead = v3;
  v3->_Isnil = 1;
  this->m_MemberFindPartyMap._Myhead->_Parent = this->m_MemberFindPartyMap._Myhead;
  this->m_MemberFindPartyMap._Myhead->_Left = this->m_MemberFindPartyMap._Myhead;
  this->m_MemberFindPartyMap._Myhead->_Right = this->m_MemberFindPartyMap._Myhead;
  this->m_MemberFindPartyMap._Mysize = 0;
  v4 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_PartyFindMemberMap);
  this->m_PartyFindMemberMap._Myhead = v4;
  v4->_Isnil = 1;
  this->m_PartyFindMemberMap._Myhead->_Parent = this->m_PartyFindMemberMap._Myhead;
  this->m_PartyFindMemberMap._Myhead->_Left = this->m_PartyFindMemberMap._Myhead;
  this->m_PartyFindMemberMap._Myhead->_Right = this->m_PartyFindMemberMap._Myhead;
  this->m_PartyFindMemberMap._Mysize = 0;
  this->m_tempUID = 0;
}

//----- (00483B20) --------------------------------------------------------
void __thiscall Castle::CCastle::GetTaxIncome(Castle::CCastle *this, unsigned int dwTaxIncome)
{
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int m_dwGID; // [esp-4h] [ebp-2Ch]
  PktCastleCmd pktCC; // [esp+4h] [ebp-24h] BYREF

  pktCC.m_dwCastleID = this->m_dwCastleID;
  pktCC.m_dwCastleObjectID = 0;
  pktCC.m_dwHP = 0;
  pktCC.m_cState = 0;
  pktCC.m_dwValue = dwTaxIncome;
  pktCC.m_cSubCmd = 7;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
  {
    m_dwGID = this->m_dwGID;
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
    if ( Guild )
      Guild::CGuild::SendAllMember(Guild, (char *)&pktCC, 0x22u, 0xAAu);
  }
}

//----- (00483BA0) --------------------------------------------------------
void __thiscall Castle::CCastle::DecreaseDayValue(Castle::CCastle *this)
{
  unsigned __int8 m_cInvincibleDay; // al
  unsigned __int8 m_cTaxIncomeRemainDay; // al
  unsigned __int8 v3; // al

  m_cInvincibleDay = this->m_cInvincibleDay;
  if ( m_cInvincibleDay )
    this->m_cInvincibleDay = m_cInvincibleDay - 1;
  m_cTaxIncomeRemainDay = this->m_cTaxIncomeRemainDay;
  if ( m_cTaxIncomeRemainDay )
  {
    v3 = m_cTaxIncomeRemainDay - 1;
    this->m_cTaxIncomeRemainDay = v3;
    if ( !v3 )
    {
      this->m_dwTaxIncome = 0;
      this->m_cTaxIncomeRemainDay = 7;
    }
  }
}

//----- (00483BD0) --------------------------------------------------------
bool __thiscall Castle::CCastle::CheckRight(
        Castle::CCastle *this,
        unsigned __int8 cRightType,
        unsigned int dwCID,
        unsigned int dwGID)
{
  unsigned __int8 v5; // al
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  CPartyMgr *v9; // eax
  Guild::CGuild *v10; // eax
  unsigned int m_dwGID; // [esp-4h] [ebp-Ch]
  unsigned int v12; // [esp-4h] [ebp-Ch]

  if ( cRightType == 5 )
  {
    v5 = this->m_CastleRight.m_aryCastleRight[5];
    if ( v5 == 3 )
    {
      if ( this->m_dwGID == dwGID )
        return 1;
    }
    else if ( v5 == 1 )
    {
      m_dwGID = this->m_dwGID;
      Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
      if ( Guild )
        return Guild::CGuild::IsFriendlyGuild(Guild, dwGID);
    }
  }
  else
  {
    v12 = this->m_dwGID;
    v9 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    v10 = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v9, v12);
    if ( v10 && this->m_CastleRight.m_aryCastleRight[cRightType] >= Guild::CGuild::GetTitle(v10, dwCID) )
      return 1;
  }
  return 0;
}

//----- (00483C50) --------------------------------------------------------
void __thiscall Castle::CCastle::SetTax(Castle::CCastle *this, unsigned __int16 wTax)
{
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // edi
  unsigned int m_dwCastleID; // ecx
  unsigned int m_dwGID; // [esp-4h] [ebp-34h]
  PktCastleCmd pktCC; // [esp+Ch] [ebp-24h] BYREF

  m_dwGID = this->m_dwGID;
  this->m_wTax = wTax;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
  if ( Guild )
  {
    m_dwCastleID = this->m_dwCastleID;
    pktCC.m_dwCID = 0;
    pktCC.m_dwCastleObjectID = 0;
    pktCC.m_cState = 0;
    pktCC.m_dwHP = 0;
    pktCC.m_dwCastleID = m_dwCastleID;
    pktCC.m_dwValue = wTax;
    pktCC.m_cSubCmd = 1;
    if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
      Guild::CGuild::SendAllMember(Guild, (char *)&pktCC, 0x22u, 0xAAu);
  }
}

//----- (00483CE0) --------------------------------------------------------
void __thiscall Castle::CCastle::SetRight(Castle::CCastle *this, CastleRight castleRight)
{
  CastleRight *p_m_CastleRight; // esi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // ebx
  int v6; // ecx
  unsigned int m_dwCastleID; // eax
  int v8; // edx
  unsigned int m_dwGID; // [esp-4h] [ebp-34h]
  PktCastleRight pktCR; // [esp+Ch] [ebp-24h] BYREF

  p_m_CastleRight = &this->m_CastleRight;
  this->m_CastleRight = castleRight;
  m_dwGID = this->m_dwGID;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
  if ( Guild )
  {
    v6 = *(_DWORD *)p_m_CastleRight->m_aryCastleRight;
    m_dwCastleID = this->m_dwCastleID;
    memset(&pktCR.m_CastleRight, 3, sizeof(pktCR.m_CastleRight));
    v8 = *(_DWORD *)&this->m_CastleRight.m_aryCastleRight[4];
    *(_DWORD *)pktCR.m_CastleRight.m_aryCastleRight = v6;
    pktCR.m_dwCastleID = m_dwCastleID;
    LOWORD(m_dwCastleID) = *(_WORD *)&this->m_CastleRight.m_aryCastleRight[8];
    pktCR.m_dwCID = 0;
    *(_DWORD *)&pktCR.m_CastleRight.m_aryCastleRight[4] = v8;
    *(_WORD *)&pktCR.m_CastleRight.m_aryCastleRight[8] = m_dwCastleID;
    if ( PacketWrap::WrapCrypt((char *)&pktCR, 0x1Eu, 0xADu, 0, 0) )
      Guild::CGuild::SendAllMember(Guild, (char *)&pktCR, 0x1Eu, 0xADu);
  }
}

//----- (00483DA0) --------------------------------------------------------
void __thiscall Castle::CCastle::UpdateCastleInfo(
        Castle::CCastle *this,
        unsigned int dwGID,
        unsigned __int16 wTax,
        unsigned int dwTaxIncome,
        unsigned __int8 cInvincibleDay,
        unsigned __int8 cTaxMoneyRamainDay,
        CastleRight castleRight,
        const char *szCastleName)
{
  CastleRight *p_m_CastleRight; // edi
  unsigned int m_dwCastleID; // eax
  unsigned int m_dwGID; // ecx
  unsigned __int16 m_wTax; // dx
  int v13; // eax
  int v14; // ecx
  __int16 v15; // dx
  CCreatureManager *Instance; // eax
  PktCastleUpdate pktCU; // [esp+Ch] [ebp-5Ch] BYREF

  this->m_dwGID = dwGID;
  this->m_wTax = wTax;
  this->m_dwTaxIncome = dwTaxIncome;
  this->m_cInvincibleDay = cInvincibleDay;
  p_m_CastleRight = &this->m_CastleRight;
  this->m_cTaxIncomeRemainDay = cTaxMoneyRamainDay;
  this->m_CastleRight = castleRight;
  strncpy(this->m_szCastleName, szCastleName, 0x32u);
  m_dwCastleID = this->m_dwCastleID;
  m_dwGID = this->m_dwGID;
  memset(&pktCU.m_CastleRight, 3, sizeof(pktCU.m_CastleRight));
  m_wTax = this->m_wTax;
  pktCU.m_dwCastleID = m_dwCastleID;
  pktCU.m_dwTaxMoney = this->m_dwTaxIncome;
  v13 = *(_DWORD *)p_m_CastleRight->m_aryCastleRight;
  pktCU.m_dwGID = m_dwGID;
  LOBYTE(m_dwGID) = this->m_cInvincibleDay;
  pktCU.m_wTax = m_wTax;
  LOBYTE(m_wTax) = this->m_cTaxIncomeRemainDay;
  *(_DWORD *)pktCU.m_CastleRight.m_aryCastleRight = v13;
  pktCU.m_cInvincibleDay = m_dwGID;
  v14 = *(_DWORD *)&this->m_CastleRight.m_aryCastleRight[4];
  pktCU.m_cTaxMoneyRemainDay = m_wTax;
  v15 = *(_WORD *)&this->m_CastleRight.m_aryCastleRight[8];
  *(_DWORD *)&pktCU.m_CastleRight.m_aryCastleRight[4] = v14;
  *(_WORD *)&pktCU.m_CastleRight.m_aryCastleRight[8] = v15;
  strncpy(pktCU.m_szCastleName, this->m_szCastleName, 0x32u);
  if ( PacketWrap::WrapCrypt((char *)&pktCU, 0x58u, 0xB2u, 0, 0) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, (char *)&pktCU, 0x58u, 0xB2u, 1);
  }
}

//----- (00483EC0) --------------------------------------------------------
CSiegeObject *__thiscall Castle::CCastle::GetCastleEmblem(Castle::CCastle *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // ecx
  CSiegeObject *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax

  Myhead = this->m_CastleObjectMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left == Myhead )
    return 0;
  while ( 1 )
  {
    result = Left->_Myval.second;
    if ( result )
    {
      if ( result->m_wObjectType == 5000 )
        break;
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
    if ( Left == Myhead )
      return 0;
  }
  return result;
}

//----- (00483F40) --------------------------------------------------------
void __thiscall Castle::CCastle::UpgradeByEmblem(Castle::CCastle *this)
{
  CSiegeObject *CastleEmblem; // eax
  CSiegeObject *v3; // edi
  unsigned __int8 m_cUpgradeStep; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // eax
  unsigned __int8 v7; // bl
  unsigned __int8 v8; // bp
  CSiegeObject *second; // ecx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int m_dwGID; // [esp-4h] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator itr; // [esp+8h] [ebp-Ch] BYREF
  int cUpgradeType; // [esp+Ch] [ebp-8h]
  int cUpgradeStep; // [esp+10h] [ebp-4h]

  CastleEmblem = Castle::CCastle::GetCastleEmblem(this);
  v3 = CastleEmblem;
  if ( CastleEmblem )
  {
    m_cUpgradeStep = CastleEmblem->m_cUpgradeStep;
    if ( m_cUpgradeStep )
    {
      LOBYTE(cUpgradeType) = v3->m_cUpgradeType;
      Myhead = this->m_CastleObjectMap._Myhead;
      LOBYTE(cUpgradeStep) = m_cUpgradeStep;
      Left = Myhead->_Left;
      itr._Ptr = Myhead->_Left;
      if ( itr._Ptr != Myhead )
      {
        v7 = cUpgradeType;
        v8 = cUpgradeStep;
        do
        {
          second = Left->_Myval.second;
          if ( second && second->m_wObjectType != 5000 )
            CSiegeObject::UpgradeByEmblem(second, v7, v8);
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&itr);
          Left = itr._Ptr;
        }
        while ( itr._Ptr != this->m_CastleObjectMap._Myhead );
      }
      m_dwGID = this->m_dwGID;
      this->m_fBonusRate = v3->m_MonsterInfo.m_fBonusRate;
      Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
      if ( Guild )
        Guild::CGuild::UpgradeMemberRespawnSpeedByEmblem(Guild, v3->m_cUpgradeType, v3->m_cUpgradeStep);
    }
  }
}

//----- (00484000) --------------------------------------------------------
void __thiscall Castle::CCastle::DegradeByEmblem(Castle::CCastle *this)
{
  CSiegeObject *CastleEmblem; // eax
  unsigned __int8 m_cUpgradeStep; // cl
  unsigned __int8 m_cUpgradeType; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // eax
  unsigned __int8 v7; // bl
  unsigned __int8 v8; // di
  CSiegeObject *second; // ecx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int m_dwGID; // [esp-4h] [ebp-14h]
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator itr; // [esp+4h] [ebp-Ch] BYREF
  int cUpgradeType; // [esp+8h] [ebp-8h]
  int cUpgradeStep; // [esp+Ch] [ebp-4h]

  CastleEmblem = Castle::CCastle::GetCastleEmblem(this);
  if ( CastleEmblem )
  {
    m_cUpgradeStep = CastleEmblem->m_cUpgradeStep;
    if ( m_cUpgradeStep )
    {
      m_cUpgradeType = CastleEmblem->m_cUpgradeType;
      LOBYTE(cUpgradeStep) = m_cUpgradeStep;
      Myhead = this->m_CastleObjectMap._Myhead;
      LOBYTE(cUpgradeType) = m_cUpgradeType;
      Left = Myhead->_Left;
      itr._Ptr = Myhead->_Left;
      if ( itr._Ptr != Myhead )
      {
        v7 = cUpgradeStep;
        v8 = cUpgradeType;
        do
        {
          second = Left->_Myval.second;
          if ( second && second->m_wObjectType != 5000 )
            CSiegeObject::DegradeByEmblem(second, v8, v7);
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&itr);
          Left = itr._Ptr;
        }
        while ( itr._Ptr != this->m_CastleObjectMap._Myhead );
      }
      m_dwGID = this->m_dwGID;
      this->m_fBonusRate = 0.0;
      Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_dwGID);
      if ( Guild )
        Guild::CGuild::DegradeMemberRespawnSpeedByEmblem(Guild);
    }
  }
}

//----- (004840A0) --------------------------------------------------------
void __thiscall Castle::CCastle::AllRespawn(Castle::CCastle *this, unsigned int dwExceptGID)
{
  CSiegeObject *CastleEmblem; // eax
  CCell *m_lpCell; // esi
  int v4; // edi
  CCell *ConnectCell; // eax
  int v6; // edi
  CCell *v7; // eax
  CCell *v8; // ebx
  int v9; // edi
  CCell *j; // esi
  int nCellWidth; // [esp+0h] [ebp-Ch]
  int nCellHeight; // [esp+4h] [ebp-8h]
  int i; // [esp+8h] [ebp-4h]

  CastleEmblem = Castle::CCastle::GetCastleEmblem(this);
  if ( CastleEmblem )
  {
    m_lpCell = CastleEmblem->m_CellPos.m_lpCell;
    if ( m_lpCell )
    {
      nCellWidth = 9;
      nCellHeight = 9;
      v4 = 1;
      while ( 1 )
      {
        ConnectCell = CCell::GetConnectCell(m_lpCell, 3u);
        if ( !ConnectCell )
          break;
        ++v4;
        m_lpCell = ConnectCell;
        if ( v4 >= 5 )
          goto LABEL_8;
      }
      nCellWidth = v4 + 4;
LABEL_8:
      v6 = 1;
      while ( 1 )
      {
        v7 = CCell::GetConnectCell(m_lpCell, 1u);
        if ( !v7 )
          break;
        ++v6;
        m_lpCell = v7;
        if ( v6 >= 5 )
          goto LABEL_13;
      }
      nCellHeight = v6 + 4;
LABEL_13:
      v8 = m_lpCell;
      for ( i = 0; i < nCellHeight; ++i )
      {
        v9 = 0;
        for ( j = v8; v9 < nCellWidth; ++v9 )
        {
          if ( !j )
            break;
          CCell::RespawnAllCharacter(j, dwExceptGID);
          j = CCell::GetConnectCell(j, 4u);
        }
        v8 = CCell::GetConnectCell(v8, 2u);
        if ( !v8 )
          break;
      }
    }
  }
}

//----- (00484190) --------------------------------------------------------
char __thiscall Castle::CCastle::ChangeCastleMaster(Castle::CCastle *this, unsigned int dwGID)
{
  CastleRight *p_m_CastleRight; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // ebp
  CSiegeObject *second; // ecx
  unsigned __int16 m_wObjectType; // ax
  CSiegeObjectMgr *Instance; // eax
  CCreatureManager *v9; // eax
  unsigned int m_dwCastleID; // [esp-8h] [ebp-48h]
  PktCastleCmd pktCC; // [esp+1Ch] [ebp-24h] BYREF

  p_m_CastleRight = &this->m_CastleRight;
  *(_DWORD *)p_m_CastleRight->m_aryCastleRight = 50529027;
  *(_DWORD *)&p_m_CastleRight->m_aryCastleRight[4] = 50528515;
  *(_WORD *)&p_m_CastleRight->m_aryCastleRight[8] = 771;
  Myhead = this->m_CastleObjectMap._Myhead;
  this->m_dwGID = dwGID;
  this->m_dwTaxIncome = 0;
  this->m_wTax = 0;
  this->m_cInvincibleDay = 1;
  this->m_cTaxIncomeRemainDay = 7;
  Left = Myhead->_Left;
  if ( Myhead->_Left != Myhead )
  {
    while ( 1 )
    {
      do
        second = Left->_Myval.second;
      while ( !second );
      m_wObjectType = second->m_wObjectType;
      second->m_dwGID = dwGID;
      if ( m_wObjectType == 5434 || m_wObjectType == 5482 )
        CSiegeObject::AllGetOff(second);
    }
  }
  Castle::CCastle::AllRespawn(this, dwGID);
  m_dwCastleID = this->m_dwCastleID;
  Instance = CSiegeObjectMgr::GetInstance();
  CSiegeObjectMgr::SendChangeMaster(Instance, m_dwCastleID, dwGID);
  pktCC.m_dwCastleID = this->m_dwCastleID;
  pktCC.m_dwCastleObjectID = 0;
  pktCC.m_dwHP = 0;
  pktCC.m_cState = 0;
  pktCC.m_dwValue = dwGID;
  pktCC.m_cSubCmd = 6;
  if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
  {
    v9 = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(v9, (char *)&pktCC, 0x22u, 0xAAu, 1);
  }
  return 1;
}

//----- (00484280) --------------------------------------------------------
bool __thiscall Castle::CCastle::InsertCastleObject(Castle::CCastle *this, CSiegeObject *lpCastleObject)
{
  unsigned int m_dwCID; // edx
  std::pair<unsigned long const ,CSiegeObject *> _Val; // [esp+0h] [ebp-10h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> result; // [esp+8h] [ebp-8h] BYREF

  if ( !lpCastleObject )
    return 0;
  m_dwCID = lpCastleObject->m_dwCID;
  _Val.second = lpCastleObject;
  _Val.first = m_dwCID;
  return std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
           &this->m_CastleObjectMap,
           &result,
           (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)&_Val)->second;
}

//----- (004842C0) --------------------------------------------------------
void __thiscall Castle::CCastle::~CCastle(std::map<unsigned long,CSiegeObject *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004842F0) --------------------------------------------------------
void __thiscall Castle::CCastle::CCastle(Castle::CCastle *this, const CastleInfoDB *CastleInfo)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v3; // eax
  char *m_szCastleName; // eax
  char v6; // cl
  float fPointY; // ecx
  float fPointZ; // eax
  float v9; // eax
  float v10; // ecx
  float v11; // ecx
  float v12; // eax
  unsigned __int64 v13; // rax
  int Day; // ecx
  unsigned int v15; // ebp
  __int64 v16; // rax
  int Second; // [esp-8h] [ebp-3Ch]
  ATL::CTime lastSiegeTime; // [esp+14h] [ebp-20h] BYREF
  ATL::CTime nowTime; // [esp+1Ch] [ebp-18h]
  float v20; // [esp+24h] [ebp-10h]
  int v21; // [esp+30h] [ebp-4h]
  const CastleInfoDB *CastleInfoa; // [esp+38h] [ebp+4h]

  v3 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_CastleObjectMap._Myhead = v3;
  v3->_Isnil = 1;
  this->m_CastleObjectMap._Myhead->_Parent = this->m_CastleObjectMap._Myhead;
  this->m_CastleObjectMap._Myhead->_Left = this->m_CastleObjectMap._Myhead;
  this->m_CastleObjectMap._Myhead->_Right = this->m_CastleObjectMap._Myhead;
  this->m_CastleObjectMap._Mysize = 0;
  this->m_dwCastleID = CastleInfo->m_dwCastleID;
  this->m_dwGID = CastleInfo->m_dwGID;
  this->m_dwTaxIncome = CastleInfo->m_dwTaxMoney;
  this->m_wTax = CastleInfo->m_wTax;
  this->m_cInvincibleDay = CastleInfo->m_cInvincibleDay;
  this->m_cZone = CastleInfo->m_cZone;
  *(_DWORD *)this->m_CastleRight.m_aryCastleRight = 50529027;
  *(_DWORD *)&this->m_CastleRight.m_aryCastleRight[4] = 50529027;
  *(_WORD *)&this->m_CastleRight.m_aryCastleRight[8] = 771;
  this->m_CastleRight.m_aryCastleRight[5] = 1;
  this->m_BackDoorPos[0].m_fPointX = 0.0;
  this->m_BackDoorPos[0].m_fPointY = 0.0;
  this->m_BackDoorPos[0].m_fPointZ = 0.0;
  this->m_BackDoorPos[1].m_fPointX = 0.0;
  this->m_BackDoorPos[1].m_fPointY = 0.0;
  this->m_BackDoorPos[1].m_fPointZ = 0.0;
  this->m_fBonusRate = 0.0;
  this->m_CastleRight = *(CastleRight *)CastleInfo->m_szRight;
  m_szCastleName = CastleInfo->m_szCastleName;
  v21 = 0;
  CastleInfoa = (const CastleInfoDB *)((char *)this - CastleInfo->m_szCastleName + 39);
  do
  {
    v6 = *m_szCastleName;
    *((_BYTE *)&CastleInfoa->m_dwCastleID + (_DWORD)m_szCastleName) = *m_szCastleName;
    ++m_szCastleName;
  }
  while ( v6 );
  fPointY = CastleInfo->m_InPos.fPointY;
  *(float *)&nowTime.m_time = CastleInfo->m_InPos.fPointX;
  fPointZ = CastleInfo->m_InPos.fPointZ;
  *((float *)&nowTime.m_time + 1) = fPointY;
  this->m_BackDoorPos[0].m_fPointX = *(float *)&nowTime.m_time;
  v20 = fPointZ;
  this->m_BackDoorPos[0].m_fPointY = *((float *)&nowTime.m_time + 1);
  this->m_BackDoorPos[0].m_fPointZ = fPointZ;
  v9 = CastleInfo->m_OutPos.fPointY;
  v10 = CastleInfo->m_OutPos.fPointZ;
  *(float *)&nowTime.m_time = CastleInfo->m_OutPos.fPointX;
  *((float *)&nowTime.m_time + 1) = v9;
  v20 = v10;
  v11 = v9;
  this->m_BackDoorPos[1].m_fPointX = *(float *)&nowTime.m_time;
  v12 = v20;
  this->m_BackDoorPos[1].m_fPointY = v11;
  this->m_BackDoorPos[1].m_fPointZ = v12;
  v13 = _time64(0);
  Second = CastleInfo->m_LastSiegeTime.Second;
  Day = CastleInfo->m_LastSiegeTime.Day;
  HIDWORD(nowTime.m_time) = HIDWORD(v13);
  v15 = v13;
  ATL::CTime::CTime(
    &lastSiegeTime,
    CastleInfo->m_LastSiegeTime.Year,
    CastleInfo->m_LastSiegeTime.Month,
    Day,
    CastleInfo->m_LastSiegeTime.Hour,
    CastleInfo->m_LastSiegeTime.Minute,
    Second,
    -1);
  v16 = (__int64)(__PAIR64__(HIDWORD(nowTime.m_time), v15) - lastSiegeTime.m_time) / 86400;
  if ( v16 <= 7 )
    this->m_cTaxIncomeRemainDay = 7 - v16;
  else
    this->m_cTaxIncomeRemainDay = 0;
}

//----- (004844A0) --------------------------------------------------------
void __thiscall CCastingSpell::~CCastingSpell(CCastingSpell *this)
{
  CCastingSpell::ClearChant(this);
  CCastingSpell::ClearEnchant(this);
}

//----- (004844B0) --------------------------------------------------------
void __thiscall CSpellMgr::CSpellMgr(CSpellMgr *this)
{
  this->m_pOwner = 0;
  this->m_CastingInfo.m_pOwner = 0;
  this->m_CastingInfo.m_usInternalFlag = 0;
  this->m_CastingInfo.m_cEnchantNum = 0;
  this->m_CastingInfo.m_cChantNum = 0;
  this->m_AffectedInfo.m_cInternalFlag = 0;
  this->m_AffectedInfo.m_cChantNum = 0;
  this->m_AffectedInfo.m_cEnchantNum = 0;
}

//----- (004844E0) --------------------------------------------------------
void __thiscall CSpellMgr::~CSpellMgr(CSpellMgr *this)
{
  CCastingSpell *v1; // esi
  CAffectedSpell *p_m_AffectedInfo; // edi

  v1 = (CCastingSpell *)this;
  p_m_AffectedInfo = &this->m_AffectedInfo;
  CAffectedSpell::ClearChant(&this->m_AffectedInfo);
  CAffectedSpell::ClearEnchant(p_m_AffectedInfo);
  v1 = (CCastingSpell *)((char *)v1 + 4);
  CCastingSpell::ClearChant(v1);
  CCastingSpell::ClearEnchant(v1);
}

