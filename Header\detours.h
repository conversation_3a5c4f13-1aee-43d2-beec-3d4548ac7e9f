#pragma once
#ifndef _DETOURS_H_
#define _DETOURS_H_

#include <Windows.h>

// Detours error codes
#define DETOUR_SUCCESS 0
#define DETOUR_ERROR_INVALID_PARAMETER 1
#define DETOUR_ERROR_MEMORY_PROTECTION 2
#define DETOUR_ERROR_INVALID_ADDRESS 3

namespace Detours
{
    // Detour a function
    inline int DetourFunction(PBYTE src, const PBYTE dst, const int len)
    {
        if (!src || !dst || len < 5)
        {
            return DETOUR_ERROR_INVALID_PARAMETER;
        }

        // Check if the source address is valid
        if (IsBadReadPtr(src, len) || IsBadWritePtr(src, len))
        {
            return DETOUR_ERROR_INVALID_ADDRESS;
        }

        // Allocate memory for the trampoline
        PBYTE jmp = (PBYTE)VirtualAlloc(NULL, len + 5, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!jmp)
        {
            return DETOUR_ERROR_MEMORY_PROTECTION;
        }

        // Change memory protection
        DWORD oldProtect;
        if (!VirtualProtect(src, len, PAGE_EXECUTE_READWRITE, &oldProtect))
        {
            VirtualFree(jmp, 0, MEM_RELEASE);
            return DETOUR_ERROR_MEMORY_PROTECTION;
        }

        // Copy original bytes to the trampoline
        memcpy(jmp, src, len);

        // Add a jump back to the original function (after our detour)
        jmp[len] = 0xE9; // JMP opcode
        *(DWORD *)(jmp + len + 1) = (DWORD)((src + len) - (jmp + len + 5));

        // Write the detour
        src[0] = 0xE9; // JMP opcode
        *(DWORD *)(src + 1) = (DWORD)(dst - src - 5);

        // Fill the remaining bytes with NOPs
        for (int i = 5; i < len; i++)
        {
            src[i] = 0x90; // NOP opcode
        }

        // Restore memory protection
        VirtualProtect(src, len, oldProtect, &oldProtect);

        // Flush instruction cache to ensure the CPU picks up our changes
        FlushInstructionCache(GetCurrentProcess(), src, len);

        return DETOUR_SUCCESS;
    }

    // Remove a detour
    inline int RemoveDetour(PBYTE src, const PBYTE original, const int len)
    {
        if (!src || !original || len < 5)
        {
            return DETOUR_ERROR_INVALID_PARAMETER;
        }

        // Check if the source address is valid
        if (IsBadReadPtr(src, len) || IsBadWritePtr(src, len))
        {
            return DETOUR_ERROR_INVALID_ADDRESS;
        }

        // Change memory protection
        DWORD oldProtect;
        if (!VirtualProtect(src, len, PAGE_EXECUTE_READWRITE, &oldProtect))
        {
            return DETOUR_ERROR_MEMORY_PROTECTION;
        }

        // Restore original bytes
        memcpy(src, original, len);

        // Restore memory protection
        VirtualProtect(src, len, oldProtect, &oldProtect);

        // Flush instruction cache
        FlushInstructionCache(GetCurrentProcess(), src, len);

        return DETOUR_SUCCESS;
    }
}

#endif // _DETOURS_H_