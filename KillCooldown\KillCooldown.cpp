#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <Psapi.h>
#include "../Header/detours.h"
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <algorithm>
#include <iomanip>

// Forward declarations to avoid "identifier not found" errors
struct SaveEnemyInfo;

#pragma comment(lib, "Psapi.lib")

// Global variables
DWORD g_SaveEnemyCallAddress = 0;
DWORD g_AddToThreatListAddress = 0x0045C060; // CThreat::AddToThreatList address
DWORD g_LastCallerAddress = 0;
std::string g_CooldownFilePath;
std::string g_ConfigFilePath;
std::string g_ThreatLogFilePath;
std::string g_DebugLogFilePath;
std::string g_ThreatTimeoutLogPath;

// Configuration values
bool g_Enabled = true;
DWORD g_CooldownTime = 300000;             // 5 minutes in milliseconds
DWORD g_SaveInterval = 30000;              // 30 seconds in milliseconds
bool g_IgnoreLatestEnemy = true;           // Default to true to avoid crashes with m_LatestEnemy pointer
bool g_ProcessingCall = false;             // Flag to prevent recursive/concurrent calls
bool g_StrictConcurrencyProtection = true; // Enable strict concurrency protection by default

// Threat timeout configuration
bool g_EnableThreatTimeout = true;        // Enable threat timeout system
DWORD g_ThreatTimeoutMs = 10000;          // 10 seconds timeout
DWORD g_CleanupIntervalMs = 2000;         // Clean every 2 seconds
bool g_EnableThreatTimeoutLogging = true; // Enable threat timeout logging
bool g_EnableDebugLogging = true;         // Enable real-time debug logging

// Mutex for thread-safe data access
std::mutex g_CooldownMutex;
std::mutex g_CallMutex;          // New mutex for call protection
std::mutex g_ThreatTimeoutMutex; // Mutex for threat timeout data

// Global variables for thread synchronization
bool g_SaveRequested = false;
volatile bool g_ThreatTimeoutShutdown = false;
HANDLE g_ThreatTimeoutThread = NULL;

// Persistent cooldown storage - maps character ID to last kill timestamp
std::unordered_map<DWORD, DWORD> g_PersistentCooldowns;

// SaveEnemyInfo structure
struct SaveEnemyInfo
{
    DWORD m_dwCID;       // Character ID
    DWORD m_dwTickCount; // Timestamp
};

// Enhanced ThreatInfo structure with timestamp
struct ThreatInfoWithTime
{
    void *pCreature;     // Pointer to attacking creature
    int lThreatAmount;   // Threat amount
    DWORD dwTimestamp;   // When this threat was added
    void *pThreatObject; // Pointer to the CThreat object that owns this
};

// Threat timeout storage - maps CThreat object to its threat entries with timestamps
std::unordered_map<void *, std::vector<ThreatInfoWithTime>> g_ThreatTimeouts;

// Function pointer types
typedef char(__thiscall *tSaveEnemy)(void *pThis, unsigned int dwCID);
typedef void(__thiscall *tAddToThreatList)(void *pThis, void *pAttackCreature, int lThreatAmount);

tSaveEnemy oSaveEnemy = nullptr;
tAddToThreatList oAddToThreatList = nullptr;

// Function to read an integer value from an INI file
int ReadIniInt(const std::string &iniPath, const std::string &section, const std::string &key, int defaultValue)
{
    return GetPrivateProfileIntA(section.c_str(), key.c_str(), defaultValue, iniPath.c_str());
}

// Function to load configuration from INI file
void LoadConfiguration()
{
    try
    {
        // Get current directory for config file
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_ConfigFilePath = std::string(currentDir) + "\\cfg_killcooldown.ini";

        // Check if the config file exists
        if (GetFileAttributesA(g_ConfigFilePath.c_str()) == INVALID_FILE_ATTRIBUTES)
        {
            return;
        }

        // Read configuration values
        g_Enabled = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "Enabled", 1) != 0);
        g_CooldownTime = ReadIniInt(g_ConfigFilePath, "KillCooldown", "CooldownTime", 300000);
        g_SaveInterval = ReadIniInt(g_ConfigFilePath, "KillCooldown", "SaveInterval", 30000);
        g_IgnoreLatestEnemy = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "IgnoreLatestEnemy", 1) != 0);
        g_StrictConcurrencyProtection = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "StrictConcurrencyProtection", 1) != 0);

        // Read threat timeout configuration
        g_EnableThreatTimeout = (ReadIniInt(g_ConfigFilePath, "THREAT_TIMEOUT", "EnableThreatTimeout", 1) != 0);
        g_ThreatTimeoutMs = ReadIniInt(g_ConfigFilePath, "THREAT_TIMEOUT", "ThreatTimeoutMs", 10000);
        g_CleanupIntervalMs = ReadIniInt(g_ConfigFilePath, "THREAT_TIMEOUT", "CleanupIntervalMs", 2000);

        // Read logging configuration
        g_EnableThreatTimeoutLogging = (ReadIniInt(g_ConfigFilePath, "THREAT_TIMEOUT", "EnableThreatLogging", 1) != 0);
        g_EnableDebugLogging = (ReadIniInt(g_ConfigFilePath, "THREAT_TIMEOUT", "EnableDebugLogging", 1) != 0);

        // Set up log file paths
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        std::string logsDir = std::string(currentDir) + "\\logs";

        // Create logs directory if it doesn't exist
        CreateDirectoryA(logsDir.c_str(), NULL);

        g_ThreatLogFilePath = logsDir + "\\threat_timeout.txt";
        g_DebugLogFilePath = logsDir + "\\threat_debug.txt";

        // Validate timeout values
        if (g_ThreatTimeoutMs < 1000)
            g_ThreatTimeoutMs = 1000; // Minimum 1 second
        if (g_ThreatTimeoutMs > 300000)
            g_ThreatTimeoutMs = 300000; // Maximum 5 minutes
        if (g_CleanupIntervalMs < 500)
            g_CleanupIntervalMs = 500; // Minimum 0.5 seconds
        if (g_CleanupIntervalMs > 10000)
            g_CleanupIntervalMs = 10000; // Maximum 10 seconds
    }
    catch (...)
    {
        // Silently fail if configuration loading fails
    }
}

// Function to save cooldowns to file
void SaveCooldownsToFile()
{
    try
    {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);

        // Check if we have any cooldowns to save
        if (g_PersistentCooldowns.empty())
        {
            return;
        }

        std::ofstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open())
        {
            return;
        }

        // Count active cooldowns
        DWORD currentTime = GetTickCount();
        size_t activeCount = 0;
        for (const auto &entry : g_PersistentCooldowns)
        {
            DWORD age = currentTime - entry.second;
            if (age < g_CooldownTime)
            {
                activeCount++;
            }
        }

        // Write the number of entries
        size_t count = g_PersistentCooldowns.size();
        file.write(reinterpret_cast<const char *>(&count), sizeof(count));

        // Write each entry
        for (const auto &entry : g_PersistentCooldowns)
        {
            file.write(reinterpret_cast<const char *>(&entry.first), sizeof(entry.first));
            file.write(reinterpret_cast<const char *>(&entry.second), sizeof(entry.second));
        }

        file.close();
    }
    catch (...)
    {
        // Silently fail if saving fails
    }
}

// Function to load cooldowns from file
void LoadCooldownsFromFile()
{
    try
    {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);

        std::ifstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open())
        {
            return;
        }

        // Read the number of entries
        size_t count = 0;
        file.read(reinterpret_cast<char *>(&count), sizeof(count));

        if (count == 0)
        {
            file.close();
            return;
        }

        // Read each entry
        g_PersistentCooldowns.clear();
        size_t validCount = 0;
        DWORD currentTime = GetTickCount();

        for (size_t i = 0; i < count; i++)
        {
            DWORD characterId = 0;
            DWORD timestamp = 0;

            file.read(reinterpret_cast<char *>(&characterId), sizeof(characterId));
            file.read(reinterpret_cast<char *>(&timestamp), sizeof(timestamp));

            // Check if the cooldown is still valid
            DWORD age = currentTime - timestamp;
            if (age < g_CooldownTime)
            {
                g_PersistentCooldowns[characterId] = timestamp;
                validCount++;
            }
        }

        file.close();
    }
    catch (...)
    {
        // Silently fail if loading fails
    }
}

// Thread function to periodically save cooldowns
DWORD WINAPI CooldownSaveThread(LPVOID)
{
    while (true)
    {
        // Save cooldowns at the configured interval
        Sleep(g_SaveInterval);

        try
        {
            // Check if an immediate save was requested
            bool saveNeeded = false;
            {
                // Use a critical section to safely check and reset the flag
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                if (g_SaveRequested)
                {
                    saveNeeded = true;
                    g_SaveRequested = false;
                }
            }

            // Always save periodically or when requested
            SaveCooldownsToFile();
        }
        catch (...)
        {
            // Silently continue if saving fails
        }
    }
    return 0;
}

// Helper function to safely access memory
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL SafeReadMemory(LPVOID lpAddress, LPVOID lpBuffer, SIZE_T nSize)
{
    if (!lpAddress || !lpBuffer || nSize == 0)
    {
        return FALSE;
    }

    BOOL result = FALSE;

    __try
    {
        memcpy(lpBuffer, lpAddress, nSize);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely check if a pointer is valid
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL IsPtrValid(LPVOID ptr, SIZE_T size)
{
    if (!ptr)
        return FALSE;

    BOOL result = FALSE;

    __try
    {
        volatile BYTE temp = *((BYTE *)ptr);
        volatile BYTE temp2 = *(((BYTE *)ptr) + size - 1);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely read the m_LatestEnemy pointer
// This function is kept simple with no C++ objects to avoid unwinding issues
SaveEnemyInfo *ReadLatestEnemyPtr(void *pThis)
{
    if (!pThis)
        return nullptr;

    SaveEnemyInfo *latestEnemy = nullptr;

    __try
    {
        // Try different potential offsets for m_LatestEnemy
        // Common offsets in different game versions
        DWORD possibleOffsets[] = {0x8, 0xC, 0x10, 0x14, 0x18, 0x1C, 0x20};

        for (int i = 0; i < sizeof(possibleOffsets) / sizeof(DWORD); i++)
        {
            PBYTE pOffset = (PBYTE)pThis + possibleOffsets[i];

            // Read the pointer value
            SaveEnemyInfo *testPtr = nullptr;
            if (!SafeReadMemory(pOffset, &testPtr, sizeof(testPtr)) || !testPtr)
            {
                continue;
            }

            // Simple validation - just check if we can read the first byte
            if (IsPtrValid(testPtr, sizeof(SaveEnemyInfo) * 5))
            {
                latestEnemy = testPtr;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        return nullptr;
    }

    return latestEnemy;
}

// Threat timeout logging functions
void LogThreatActivity(const std::string &message)
{
    if (!g_EnableThreatTimeoutLogging)
        return;

    try
    {
        std::ofstream logFile(g_ThreatLogFilePath, std::ios::app);
        if (logFile.is_open())
        {
            // Get current time
            SYSTEMTIME st;
            GetLocalTime(&st);

            logFile << "[" << std::setfill('0') << std::setw(2) << st.wHour
                    << ":" << std::setw(2) << st.wMinute
                    << ":" << std::setw(2) << st.wSecond
                    << "." << std::setw(3) << st.wMilliseconds << "] "
                    << message << std::endl;
            logFile.close();
        }
    }
    catch (...)
    {
        // Silent exception handling
    }
}

void LogDebugRealtime(const std::string &message)
{
    if (!g_EnableDebugLogging)
        return;

    try
    {
        std::ofstream debugFile(g_DebugLogFilePath, std::ios::app);
        if (debugFile.is_open())
        {
            // Get current time with high precision
            SYSTEMTIME st;
            GetLocalTime(&st);

            debugFile << "[DEBUG " << std::setfill('0') << std::setw(2) << st.wHour
                      << ":" << std::setw(2) << st.wMinute
                      << ":" << std::setw(2) << st.wSecond
                      << "." << std::setw(3) << st.wMilliseconds << "] "
                      << message << std::endl;
            debugFile.close();
        }
    }
    catch (...)
    {
        // Silent exception handling
    }
}

void LogThreatListStatus(void *pThreatObject, const std::string &operation)
{
    if (!g_EnableDebugLogging)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatTimeoutMutex);
        auto it = g_ThreatTimeouts.find(pThreatObject);

        std::string message = "THREADLIST [" + operation + "] CThreat=0x" +
                              std::to_string(reinterpret_cast<uintptr_t>(pThreatObject));

        if (it != g_ThreatTimeouts.end())
        {
            message += " Count=" + std::to_string(it->second.size());

            // Log details of each threat
            for (size_t i = 0; i < it->second.size(); ++i)
            {
                const auto &threat = it->second[i];
                DWORD currentTime = GetTickCount();
                DWORD age = currentTime - threat.dwTimestamp;

                message += " [" + std::to_string(i) + ":Creature=0x" +
                           std::to_string(reinterpret_cast<uintptr_t>(threat.pCreature)) +
                           ",Amount=" + std::to_string(threat.lThreatAmount) +
                           ",Age=" + std::to_string(age) + "ms]";
            }
        }
        else
        {
            message += " Count=0 (Not found)";
        }

        LogDebugRealtime(message);
    }
    catch (...)
    {
        LogDebugRealtime("ERROR: Failed to log threat list status for " + operation);
    }
}

// Function to clear threat timeout data for a specific CThreat object (called on death)
void ClearThreatTimeoutData(void *pThreatObject, const std::string &reason)
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatTimeoutMutex);
        auto it = g_ThreatTimeouts.find(pThreatObject);

        if (it != g_ThreatTimeouts.end())
        {
            size_t count = it->second.size();
            LogThreatActivity("CLEAR_ON_DEATH: CThreat=0x" +
                              std::to_string(reinterpret_cast<uintptr_t>(pThreatObject)) +
                              " Reason=" + reason + " ClearedCount=" + std::to_string(count));
            LogThreatListStatus(pThreatObject, "BEFORE_DEATH_CLEAR");

            g_ThreatTimeouts.erase(it);

            LogDebugRealtime("DEATH_CLEAR: CThreat=0x" +
                             std::to_string(reinterpret_cast<uintptr_t>(pThreatObject)) + " cleared");
        }
    }
    catch (...)
    {
        LogThreatActivity("ERROR: Exception in ClearThreatTimeoutData");
    }
}

// Helper function to safely read m_LatestEnemy entries
// This function is kept simple with no C++ objects to avoid unwinding issues
void ReadLatestEnemyEntries(SaveEnemyInfo *latestEnemy)
{
    if (!latestEnemy)
        return;

    __try
    {
        // Just validate the entries but don't log them
        for (int i = 0; i < 5; i++)
        {
            IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo));
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Silent exception handling
    }
}

// Helper function to safely access the m_LatestEnemy field
SaveEnemyInfo *GetLatestEnemyArray(void *pThis)
{
    if (!pThis)
    {
        return nullptr;
    }

    // Call the exception-safe helper function
    SaveEnemyInfo *latestEnemy = nullptr;

    try
    {
        latestEnemy = ReadLatestEnemyPtr(pThis);

        if (latestEnemy)
        {
            // Call the helper function to read entries
            ReadLatestEnemyEntries(latestEnemy);
        }
    }
    catch (...)
    {
        return nullptr;
    }

    return latestEnemy;
}

// Helper function to safely check if CID exists in array
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL CheckCidInArray(SaveEnemyInfo *latestEnemy, DWORD dwCID, int *foundIndex)
{
    if (!latestEnemy || !foundIndex)
        return FALSE;

    BOOL result = FALSE;
    *foundIndex = -1;

    __try
    {
        for (int i = 0; i < 5; i++)
        {
            // Simple validation before access
            if (!IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo)))
            {
                break;
            }

            if (latestEnemy[i].m_dwCID == dwCID)
            {
                result = TRUE;
                *foundIndex = i;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely call the original SaveEnemy function
// This function is kept simple with no C++ objects to avoid unwinding issues
char CallOriginalSaveEnemy(void *pThis, DWORD dwCID)
{
    if (!oSaveEnemy || !pThis)
    {
        return 0;
    }

    char result = 0;

    __try
    {
        result = oSaveEnemy(pThis, dwCID);
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Exception caught - silent handling
    }

    return result;
}

// Threat timeout helper functions
void CleanExpiredThreats()
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatTimeoutMutex);
        DWORD currentTime = GetTickCount();
        int totalCleaned = 0;
        int totalObjects = 0;

        for (auto it = g_ThreatTimeouts.begin(); it != g_ThreatTimeouts.end();)
        {
            auto &threatList = it->second;
            size_t originalSize = threatList.size();
            totalObjects++;

            // Log before cleanup
            LogThreatListStatus(it->first, "BEFORE_CLEANUP");

            // Remove expired threats from this CThreat object
            // Handle GetTickCount() wraparound by checking if the difference is reasonable
            threatList.erase(
                std::remove_if(threatList.begin(), threatList.end(),
                               [currentTime](const ThreatInfoWithTime &threat)
                               {
                                   DWORD timeDiff = currentTime - threat.dwTimestamp;
                                   // Handle wraparound: if difference is too large, assume wraparound occurred
                                   if (timeDiff > 0x80000000)
                                   {
                                       // Wraparound case - calculate actual difference
                                       timeDiff = (0xFFFFFFFF - threat.dwTimestamp) + currentTime + 1;
                                   }
                                   return timeDiff > g_ThreatTimeoutMs;
                               }),
                threatList.end());

            size_t newSize = threatList.size();
            int cleaned = originalSize - newSize;
            totalCleaned += cleaned;

            if (cleaned > 0)
            {
                LogThreatActivity("EXPIRED_THREATS: CThreat=0x" +
                                  std::to_string(reinterpret_cast<uintptr_t>(it->first)) +
                                  " Removed=" + std::to_string(cleaned) +
                                  " Remaining=" + std::to_string(newSize));
            }

            // If no threats left for this CThreat object, remove the entry
            if (threatList.empty())
            {
                LogThreatActivity("CLEAR_EMPTY: CThreat=0x" +
                                  std::to_string(reinterpret_cast<uintptr_t>(it->first)) + " (All threats expired)");
                LogThreatListStatus(it->first, "CLEARED_EMPTY");
                it = g_ThreatTimeouts.erase(it);
            }
            else
            {
                ++it;
            }
        }

        // Log cleanup summary
        if (totalCleaned > 0 || totalObjects > 0)
        {
            LogThreatActivity("CLEANUP_SUMMARY: Objects=" + std::to_string(totalObjects) +
                              " TotalExpired=" + std::to_string(totalCleaned) +
                              " TimeoutMs=" + std::to_string(g_ThreatTimeoutMs));
        }
    }
    catch (...)
    {
        LogThreatActivity("ERROR: Exception in CleanExpiredThreats");
    }
}

// Thread function for cleaning expired threats
DWORD WINAPI ThreatTimeoutCleanupThread(LPVOID)
{
    while (!g_ThreatTimeoutShutdown)
    {
        try
        {
            CleanExpiredThreats();
            Sleep(g_CleanupIntervalMs);
        }
        catch (...)
        {
            // Continue running even if cleanup fails
            Sleep(g_CleanupIntervalMs);
        }
    }
    return 0;
}

// Hook function for CThreat::AddToThreatList
void __fastcall Hook_AddToThreatList(void *pThis, void *edx, void *pAttackCreature, int lThreatAmount)
{
    // Call original function first
    if (oAddToThreatList)
    {
        oAddToThreatList(pThis, pAttackCreature, lThreatAmount);
    }

    // Add to our timeout tracking if enabled
    if (g_EnableThreatTimeout && pThis && pAttackCreature && lThreatAmount > 0)
    {
        try
        {
            std::lock_guard<std::mutex> lock(g_ThreatTimeoutMutex);

            ThreatInfoWithTime threatInfo;
            threatInfo.pCreature = pAttackCreature;
            threatInfo.lThreatAmount = lThreatAmount;
            threatInfo.dwTimestamp = GetTickCount();
            threatInfo.pThreatObject = pThis;

            g_ThreatTimeouts[pThis].push_back(threatInfo);

            // Log the threat addition
            LogThreatActivity("ADD_THREAT: CThreat=0x" +
                              std::to_string(reinterpret_cast<uintptr_t>(pThis)) +
                              " Creature=0x" + std::to_string(reinterpret_cast<uintptr_t>(pAttackCreature)) +
                              " Amount=" + std::to_string(lThreatAmount) +
                              " TotalCount=" + std::to_string(g_ThreatTimeouts[pThis].size()));

            // Log detailed thread list status
            LogThreatListStatus(pThis, "AFTER_ADD");
        }
        catch (...)
        {
            LogThreatActivity("ERROR: Exception in Hook_AddToThreatList");
        }
    }
}

// Hook function for CThreat::SaveEnemy
char __fastcall Hook_SaveEnemy(void *pThis, void *edx, unsigned int dwCID)
{
    // Skip if plugin is disabled
    if (!g_Enabled)
    {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }

    // Safety check for null pointer
    if (!pThis)
    {
        return 0;
    }

    // Skip processing for invalid CIDs
    if (dwCID == 0)
    {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }

    // Use a lock to prevent concurrent/recursive calls if strict concurrency protection is enabled
    bool lockAcquired = false;
    if (g_StrictConcurrencyProtection)
    {
        lockAcquired = g_CallMutex.try_lock();
        if (!lockAcquired)
        {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end())
                {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }

            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        // Set processing flag to detect recursive calls
        if (g_ProcessingCall)
        {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end())
                {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }

            g_CallMutex.unlock();
            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        g_ProcessingCall = true;
    }

    // Declare variables at function start to avoid initialization issues
    DWORD startTime = 0;
    bool hasPersistentCooldown = false;
    bool cidFoundInArray = false;
    int foundIndex = -1;
    char result = 0; // Default to 0 (no cooldown) for new kills
    bool overrideResult = false;
    SaveEnemyInfo *latestEnemy = nullptr;
    bool cooldownUpdated = false;

    try
    {
        // Get current timestamp
        startTime = GetTickCount();

        // Check persistent cooldown
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            auto it = g_PersistentCooldowns.find(dwCID);
            if (it != g_PersistentCooldowns.end())
            {
                DWORD age = startTime - it->second;
                if (age < g_CooldownTime)
                {
                    hasPersistentCooldown = true;
                }
            }
        }

        // Only check latestEnemy if not ignoring it
        if (!g_IgnoreLatestEnemy)
        {
            // Get the array of SaveEnemyInfo from the CThreat object
            latestEnemy = GetLatestEnemyArray(pThis);

            if (latestEnemy)
            {
                // Use the exception-safe helper function
                if (CheckCidInArray(latestEnemy, dwCID, &foundIndex))
                {
                    cidFoundInArray = true;
                }
            }
        }

        // Determine what result we should return
        if (cidFoundInArray)
        {
            // CID is already in array, should return 1
            result = 1;
            overrideResult = true; // Always override when we know the state
        }
        else if (hasPersistentCooldown)
        {
            // CID has persistent cooldown but not in array (player relogged)
            // Override to 1 to prevent fame gain
            result = 1; // Return 1 to indicate cooldown
            overrideResult = true;
        }
        else
        {
            // No cooldown found, this is a new kill
            result = 0;
            overrideResult = false;
        }
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    // Call original function - outside of try block to avoid unwinding issues
    if (!overrideResult && oSaveEnemy && pThis)
    {
        try
        {
            result = CallOriginalSaveEnemy(pThis, dwCID);
        }
        catch (...)
        {
            result = 0; // Default to "new kill" if original function fails
        }
    }

    try
    {
        // Update the persistent cooldown timestamp when a new kill happens (result = 0)
        if (result == 0)
        {
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                g_PersistentCooldowns[dwCID] = startTime;
                cooldownUpdated = true;
            }

            // Request a save but don't do it immediately to avoid deadlock
            // We'll set a flag to save on the next timer tick
            {
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                g_SaveRequested = true;
            }
        }
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    // Reset processing flag and unlock mutex if we acquired it
    if (g_StrictConcurrencyProtection && lockAcquired)
    {
        g_ProcessingCall = false;
        g_CallMutex.unlock();
    }

    return result;
}

// Function to scan for calls to SaveEnemy in memory
BOOL FindSaveEnemyCalls()
{
    BOOL success = FALSE;

    try
    {
        HMODULE hModule = GetModuleHandleA(NULL);
        if (!hModule)
        {
            return FALSE;
        }

        // Try known addresses for the SaveEnemy function
        DWORD possibleAddresses[] = {
            0x0045A930, // From PDB
            0x0045A950,
            0x0045A910,
            0x0045A900,
            0x0045A940,
            0x0045A920};

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++)
        {
            DWORD address = possibleAddresses[i];

            // Store the original function address
            oSaveEnemy = (tSaveEnemy)address;

            // Try to hook the function
            DWORD oldProtect;
            if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
            {
                int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);

                if (result == DETOUR_SUCCESS)
                {
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                    success = TRUE;
                    break;
                }

                // Restore protection
                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
            }
        }

        // If none of the hardcoded addresses worked, try scanning for the function
        if (!success)
        {
            // Get module information
            MODULEINFO moduleInfo;
            if (GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo)))
            {
                PBYTE moduleStart = (PBYTE)moduleInfo.lpBaseOfDll;
                SIZE_T moduleSize = moduleInfo.SizeOfImage;

                // Simple signature for SaveEnemy function - adjust as needed
                BYTE signature[] = {0x55, 0x8B, 0xEC, 0x83, 0xEC};
                SIZE_T sigSize = sizeof(signature);

                // Scan through the module memory
                for (SIZE_T i = 0; i < moduleSize - sigSize; i++)
                {
                    bool found = true;
                    for (SIZE_T j = 0; j < sigSize; j++)
                    {
                        if (moduleStart[i + j] != signature[j])
                        {
                            found = false;
                            break;
                        }
                    }

                    if (found)
                    {
                        DWORD address = (DWORD)(moduleStart + i);

                        // Store the original function address
                        oSaveEnemy = (tSaveEnemy)address;

                        // Try to hook the function
                        DWORD oldProtect;
                        if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
                        {
                            int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);

                            if (result == DETOUR_SUCCESS)
                            {
                                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                                success = TRUE;
                                break;
                            }

                            // Restore protection
                            VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                        }
                    }
                }
            }
        }
    }
    catch (...)
    {
        success = FALSE;
    }

    return success;
}

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID)
{
    try
    {
        // Give the main application time to initialize
        Sleep(3000);

        // Create logs directory
        CreateDirectoryA("logs", NULL);

        // Initialize cooldown file path
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_CooldownFilePath = std::string(currentDir) + "\\logs\\killcooldowns.dat";

        // Load configuration
        LoadConfiguration();

        // Log plugin startup
        LogThreatActivity("=== KILLCOOLDOWN PLUGIN STARTED ===");
        LogThreatActivity("ThreatTimeout=" + std::string(g_EnableThreatTimeout ? "ENABLED" : "DISABLED") +
                          " TimeoutMs=" + std::to_string(g_ThreatTimeoutMs) +
                          " CleanupMs=" + std::to_string(g_CleanupIntervalMs));
        LogDebugRealtime("=== THREAT DEBUG LOGGING STARTED ===");

        // Skip further initialization if plugin is disabled
        if (!g_Enabled)
        {
            LogThreatActivity("Plugin disabled in configuration");
            return 0;
        }

        // Load persistent cooldowns
        LoadCooldownsFromFile();

        // Start cooldown save thread
        CreateThread(NULL, 0, CooldownSaveThread, NULL, 0, NULL);

        // Try to find and hook the SaveEnemy function
        BOOL hookSuccess = FindSaveEnemyCalls();

        // Hook CThreat::AddToThreatList for threat timeout system
        BOOL threatHookSuccess = FALSE;
        if (g_EnableThreatTimeout)
        {
            try
            {
                oAddToThreatList = (tAddToThreatList)g_AddToThreatListAddress;

                DWORD oldProtect;
                if (VirtualProtect((LPVOID)g_AddToThreatListAddress, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
                {
                    int result = Detours::DetourFunction((PBYTE)g_AddToThreatListAddress, (PBYTE)Hook_AddToThreatList, 6);

                    if (result == DETOUR_SUCCESS)
                    {
                        threatHookSuccess = TRUE;
                    }

                    VirtualProtect((LPVOID)g_AddToThreatListAddress, 6, oldProtect, &oldProtect);
                }

                // Start threat timeout cleanup thread
                if (threatHookSuccess)
                {
                    g_ThreatTimeoutThread = CreateThread(NULL, 0, ThreatTimeoutCleanupThread, NULL, 0, NULL);
                    if (g_ThreatTimeoutThread != NULL)
                    {
                        LogThreatActivity("THREAT_TIMEOUT_THREAD: Started successfully");
                        LogDebugRealtime("Threat timeout cleanup thread started");
                    }
                    else
                    {
                        LogThreatActivity("ERROR: Failed to start threat timeout cleanup thread");
                    }
                }
            }
            catch (...)
            {
                threatHookSuccess = FALSE;
            }
        }

// For debugging only - remove after confirming hook works
#ifdef _DEBUG
        if (hookSuccess && (!g_EnableThreatTimeout || threatHookSuccess))
        {
            MessageBoxA(NULL, "KillCooldown hooks installed successfully!", "KillCooldown Plugin", MB_OK | MB_ICONINFORMATION);
        }
        else
        {
            MessageBoxA(NULL, "KillCooldown hooks failed to install!", "KillCooldown Plugin", MB_OK | MB_ICONERROR);
        }
#endif
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
    switch (reason)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);

        try
        {
            // Initialize the licensing system
            RYL1Plugin::Initialize();

            // Check license before proceeding
            if (!RYL1Plugin::CheckLicense())
            {
                return FALSE; // This will never be reached due to crash in CheckLicense
            }
        }
        catch (...)
        {
// If license check fails, continue anyway for debugging purposes
#ifdef _DEBUG
            MessageBoxA(NULL, "License check failed but continuing for debug purposes", "KillCooldown Plugin", MB_OK | MB_ICONWARNING);
#endif
        }

        CreateThread(0, 0, MainThread, 0, 0, 0);
        break;

    case DLL_PROCESS_DETACH:
        try
        {
            // Signal threat timeout thread to shutdown
            g_ThreatTimeoutShutdown = true;

            // Wait for thread to exit gracefully
            if (g_ThreatTimeoutThread != NULL)
            {
                WaitForSingleObject(g_ThreatTimeoutThread, 1000); // Wait up to 1 second
                CloseHandle(g_ThreatTimeoutThread);
                g_ThreatTimeoutThread = NULL;
            }

            // Clear threat timeout data
            {
                std::lock_guard<std::mutex> lock(g_ThreatTimeoutMutex);
                g_ThreatTimeouts.clear();
            }

            if (g_Enabled && !g_CooldownFilePath.empty())
            {
                SaveCooldownsToFile();
            }

            // Cleanup the licensing system
            RYL1Plugin::Cleanup();
        }
        catch (...)
        {
            // Silently fail on shutdown
        }
        break;
    }

    return TRUE;
}