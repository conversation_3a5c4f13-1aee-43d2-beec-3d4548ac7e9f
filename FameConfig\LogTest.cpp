#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <filesystem>
#include <chrono>
#include <mutex>
#include <Windows.h>

// Log directory and file
const char* LOG_DIR = "logs";
const char* LOG_FILE = "logs/fame_medal_debug.txt";

// Debug logging levels
enum LogLevel {
    LOG_CRITICAL = 0,  // Critical errors
    LOG_EVENT = 1,     // Fame/medal events
    LOG_INFO = 2,      // General information
    LOG_DEBUG = 3      // Detailed debug info
};

// Current log level - only messages at this level or lower will be logged
LogLevel g_LogLevel = LOG_DEBUG;  // Enable all logging by default

// Mutex for thread-safe logging
std::mutex g_LogMutex;

// Helper function to get current timestamp for log
std::string GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::tm tm_time;
    localtime_s(&tm_time, &time);
    
    std::stringstream ss;
    ss << std::put_time(&tm_time, "%Y-%m-%d %H:%M:%S");
    
    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

// Function to get module directory (where DLL/EXE lives)
std::string GetConfigDirectory() {
    char path[MAX_PATH];
    GetModuleFileNameA(nullptr, path, MAX_PATH);
    std::filesystem::path p(path);
    return p.parent_path().string();
}

// Function to get log directory path
std::string GetLogDirectory() {
    return GetConfigDirectory() + "\\" + LOG_DIR;
}

// Function to get log file path
std::string GetLogFilePath() {
    return GetConfigDirectory() + "\\" + LOG_FILE;
}

// Ensure log directory exists
void EnsureLogDirectoryExists() {
    std::string logDir = GetLogDirectory();
    if (!std::filesystem::exists(logDir)) {
        try {
            std::filesystem::create_directory(logDir);
            std::cout << "Created log directory: " << logDir << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Failed to create log directory: " << e.what() << std::endl;
        }
    }
}

// Debug logging function
void DebugLog(LogLevel level, const char* format, ...) {
    // Check if this log level should be logged
    if (level > g_LogLevel) {
        return;
    }

    // Format the message
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    // Get log level prefix
    const char* levelStr = "UNKNOWN";
    switch (level) {
        case LOG_CRITICAL: levelStr = "CRITICAL"; break;
        case LOG_EVENT:    levelStr = "EVENT"; break;
        case LOG_INFO:     levelStr = "INFO"; break;
        case LOG_DEBUG:    levelStr = "DEBUG"; break;
    }

    // Format the final log message with timestamp and level
    std::string timestamp = GetTimestamp();
    std::string logMessage = timestamp + " [" + levelStr + "] " + buffer + "\n";

    // Log to file with mutex protection
    {
        std::lock_guard<std::mutex> lock(g_LogMutex);
        std::string logFilePath = GetLogFilePath();
        
        // Ensure directory exists
        EnsureLogDirectoryExists();
        
        // Append to log file
        std::ofstream logFile(logFilePath, std::ios::app);
        if (logFile.is_open()) {
            logFile << logMessage;
            logFile.close();
        }
    }

    // Also output to console
    std::cout << logMessage;
}

// Convenience functions for different log levels
void LogCritical(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    DebugLog(LOG_CRITICAL, "%s", buffer);
}

void LogEvent(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    DebugLog(LOG_EVENT, "%s", buffer);
}

void LogInfo(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    DebugLog(LOG_INFO, "%s", buffer);
}

void LogDebug(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    DebugLog(LOG_DEBUG, "%s", buffer);
}

// Fame gain logging function specifically for kill events
void LogFameGain(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // Special formatting for fame gain logs
    std::string timestamp = GetTimestamp();
    std::string logMessage = timestamp + " [FAME_GAIN] " + buffer + "\n";
    
    // Log to file with mutex protection
    {
        std::lock_guard<std::mutex> lock(g_LogMutex);
        std::string logFilePath = GetLogFilePath();
        
        // Ensure directory exists
        EnsureLogDirectoryExists();
        
        // Append to log file
        std::ofstream logFile(logFilePath, std::ios::app);
        if (logFile.is_open()) {
            logFile << logMessage;
            logFile.close();
        }
    }
    
    // Also output to console
    std::cout << logMessage;
}

// Simulate a PVP kill event
void SimulatePvpKill(int characterId, int currentFame, int currentMedal, int killFame, int medalPerKill) {
    LogDebug("=== SetFame Hook Called ===");
    LogDebug("Initial values: CID=%u, Fame=%u, Medal=%u", characterId, currentFame, currentMedal);
    LogDebug("Config values: KILL=%d, DIE=%d, MEDAL=%d", killFame, 10, medalPerKill);
    
    LogDebug("PVP kill detected: Fame increased by %d", killFame);
    LogDebug("Custom logic: isPvpKill=1, isDeath=0, fameChange=%d", killFame);
    
    int newFame = currentFame + killFame;
    int newMedal = currentMedal + medalPerKill;
    
    LogEvent("PVP kill: Fame %u + %d = %u, Medal %u + %d = %u",
        currentFame, killFame, newFame, currentMedal, medalPerKill, newMedal);
    
    LogDebug("Final values: CID=%u, Fame=%u->%u(%+d), Medal=%u->%u(%+d)",
        characterId, currentFame, newFame, killFame, currentMedal, newMedal, medalPerKill);
    
    LogFameGain("CID=%u, Race=%s, Class=%d, Fame=%u->%u(%+d), Medal=%u->%u(%+d), Guild=%u, Party=%u, Gold=%u, Exp=%d",
        characterId, "Human", 1, currentFame, newFame, killFame, currentMedal, newMedal, medalPerKill, 1000, 5000, 10000, 50000);
}

int main() {
    std::cout << "Fame/Medal Debug Log Test Program" << std::endl;
    std::cout << "--------------------------------" << std::endl;
    
    // Create logs directory
    EnsureLogDirectoryExists();
    
    // Initial log entry
    LogInfo("=== FameConfig Test Program Started ===");
    
    // Simulate some PVP kill events
    SimulatePvpKill(1001, 5000, 1000, 50, 30);
    Sleep(500); // Wait 500ms
    SimulatePvpKill(1002, 7500, 2500, 50, 30);
    Sleep(500); // Wait 500ms
    SimulatePvpKill(1001, 5050, 1030, 50, 30);
    
    LogInfo("=== FameConfig Test Program Completed ===");
    
    std::cout << "\nLog file created at: " << GetLogFilePath() << std::endl;
    
    return 0;
} 