; Listing generated by Microsoft (R) Optimizing Compiler Version 19.41.34120.0 

	TITLE	C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\disas\obj.X86\disasm.obj
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	_TestCodes
_BSS	SEGMENT
?value@@3HA DD	01H DUP (?)				; value
_BSS	ENDS
; Function compile flags: /Odtp
; File C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\disas\x86.cpp
_TEXT	SEGMENT
_TestCodes PROC

; 13   : {

$faraway$3:

; 14   :     __asm {
; 15   : // Each instruction is proceeded by an "int 3".
; 16   :       faraway:
; 17   :         int 3;

  00000	cc		 int	 3

; 18   :         nop;        // 1-byte NOP.

  00001	90		 npad	 1

; 19   :         int 3;

  00002	cc		 int	 3

; 20   :         _emit 0x66; // 2-byte NOP.

  00003	66		 DB	 102			; 00000066H

; 21   :         _emit 0x90;

  00004	90		 DB	 -112			; ffffff90H

; 22   :         int 3;

  00005	cc		 int	 3

; 23   :         _emit 0x0f; // 3-byte NOP.

  00006	0f		 DB	 15			; 0000000fH

; 24   :         _emit 0x1f;

  00007	1f		 DB	 31			; 0000001fH

; 25   :         _emit 0x00;

  00008	00		 DB	 0

; 26   :         int 3;

  00009	cc		 int	 3

; 27   :         _emit 0x0f; // 4-byte NOP.

  0000a	0f		 DB	 15			; 0000000fH

; 28   :         _emit 0x1f;

  0000b	1f		 DB	 31			; 0000001fH

; 29   :         _emit 0x40;

  0000c	40		 DB	 64			; 00000040H

; 30   :         _emit 0x00;

  0000d	00		 DB	 0

; 31   :         int 3;

  0000e	cc		 int	 3

; 32   :         _emit 0x0f; // 5-byte NOP.

  0000f	0f		 DB	 15			; 0000000fH

; 33   :         _emit 0x1f;

  00010	1f		 DB	 31			; 0000001fH

; 34   :         _emit 0x44;

  00011	44		 DB	 68			; 00000044H

; 35   :         _emit 0x00;

  00012	00		 DB	 0

; 36   :         _emit 0x00;

  00013	00		 DB	 0

; 37   :         int 3;

  00014	cc		 int	 3

; 38   :         _emit 0x66; // 6-byte NOP.

  00015	66		 DB	 102			; 00000066H

; 39   :         _emit 0x0f;

  00016	0f		 DB	 15			; 0000000fH

; 40   :         _emit 0x1f;

  00017	1f		 DB	 31			; 0000001fH

; 41   :         _emit 0x44;

  00018	44		 DB	 68			; 00000044H

; 42   :         _emit 0x00;

  00019	00		 DB	 0

; 43   :         _emit 0x00;

  0001a	00		 DB	 0

; 44   :         int 3;

  0001b	cc		 int	 3

; 45   :         _emit 0x0f; // 7-byte NOP.

  0001c	0f		 DB	 15			; 0000000fH

; 46   :         _emit 0x1f;

  0001d	1f		 DB	 31			; 0000001fH

; 47   :         _emit 0x80;

  0001e	80		 DB	 -128			; ffffff80H

; 48   :         _emit 0x00;

  0001f	00		 DB	 0

; 49   :         _emit 0x00;

  00020	00		 DB	 0

; 50   :         _emit 0x00;

  00021	00		 DB	 0

; 51   :         _emit 0x00;

  00022	00		 DB	 0

; 52   :         int 3;

  00023	cc		 int	 3

; 53   :         _emit 0x0f; // 8-byte NOP.

  00024	0f		 DB	 15			; 0000000fH

; 54   :         _emit 0x1f;

  00025	1f		 DB	 31			; 0000001fH

; 55   :         _emit 0x84;

  00026	84		 DB	 -124			; ffffff84H

; 56   :         _emit 0x00;

  00027	00		 DB	 0

; 57   :         _emit 0x00;

  00028	00		 DB	 0

; 58   :         _emit 0x00;

  00029	00		 DB	 0

; 59   :         _emit 0x00;

  0002a	00		 DB	 0

; 60   :         _emit 0x00;

  0002b	00		 DB	 0

; 61   :         int 3;

  0002c	cc		 int	 3

; 62   :         _emit 0x66; // 9-byte NOP.

  0002d	66		 DB	 102			; 00000066H

; 63   :         _emit 0x0f;

  0002e	0f		 DB	 15			; 0000000fH

; 64   :         _emit 0x1f;

  0002f	1f		 DB	 31			; 0000001fH

; 65   :         _emit 0x84;

  00030	84		 DB	 -124			; ffffff84H

; 66   :         _emit 0x00;

  00031	00		 DB	 0

; 67   :         _emit 0x00;

  00032	00		 DB	 0

; 68   :         _emit 0x00;

  00033	00		 DB	 0

; 69   :         _emit 0x00;

  00034	00		 DB	 0

; 70   :         _emit 0x00;

  00035	00		 DB	 0

; 71   :         int 3;

  00036	cc		 int	 3

; 72   :         mov     ecx, eax;

  00037	8b c8		 mov	 ecx, eax

; 73   :         int 3;

  00039	cc		 int	 3

; 74   :         mov     ebx, 0ffff000eh;

  0003a	bb 0e 00 ff ff	 mov	 ebx, -65522		; ffff000eH

; 75   :         int 3;

  0003f	cc		 int	 3

; 76   :         call    ebx;

  00040	ff d3		 call	 ebx

; 77   :         int 3;

  00042	cc		 int	 3

; 78   :         call    dword ptr [eax];

  00043	ff 10		 call	 DWORD PTR [eax]

; 79   :         int 3;

  00045	cc		 int	 3

; 80   :         call    dword ptr [ebx];

  00046	ff 13		 call	 DWORD PTR [ebx]

; 81   :         int 3;

  00048	cc		 int	 3

; 82   :         call    dword ptr [ecx];

  00049	ff 11		 call	 DWORD PTR [ecx]

; 83   :         int 3;

  0004b	cc		 int	 3

; 84   :         call    dword ptr [edx];

  0004c	ff 12		 call	 DWORD PTR [edx]

; 85   :         int 3;

  0004e	cc		 int	 3

; 86   :         jmp     dword ptr [eax];

  0004f	ff 20		 jmp	 DWORD PTR [eax]

; 87   :         int 3;

  00051	cc		 int	 3

; 88   :         jmp     dword ptr [ebx];

  00052	ff 23		 jmp	 DWORD PTR [ebx]

; 89   :         int 3;

  00054	cc		 int	 3

; 90   :         jmp     dword ptr [ecx];

  00055	ff 21		 jmp	 DWORD PTR [ecx]

; 91   :         int 3;

  00057	cc		 int	 3

; 92   :         jmp     dword ptr [edx];

  00058	ff 22		 jmp	 DWORD PTR [edx]

; 93   :         int 3;

  0005a	cc		 int	 3

; 94   :         call    ecx;

  0005b	ff d1		 call	 ecx

; 95   :         int 3;

  0005d	cc		 int	 3

; 96   :         call    eax;

  0005e	ff d0		 call	 eax

; 97   :         int 3;

  00060	cc		 int	 3

; 98   :         mov     ebx, 0ffff000eh;

  00061	bb 0e 00 ff ff	 mov	 ebx, -65522		; ffff000eH

; 99   :         int 3;

  00066	cc		 int	 3

; 100  :         push    eax;

  00067	50		 push	 eax

; 101  :         int 3;

  00068	cc		 int	 3

; 102  :         call    ebx;

  00069	ff d3		 call	 ebx

; 103  :         int 3;

  0006b	cc		 int	 3

; 104  :         cmp     ebx, 0;

  0006c	83 fb 00	 cmp	 ebx, 0

; 105  :         int 3;

  0006f	cc		 int	 3

; 106  :         cmp     byte ptr [value], 77h;

  00070	80 3d 00 00 00
	00 77		 cmp	 BYTE PTR ?value@@3HA, 119 ; 00000077H

; 107  :         int 3;

  00077	cc		 int	 3

; 108  :         cmp     dword ptr [value], 77h;

  00078	83 3d 00 00 00
	00 77		 cmp	 DWORD PTR ?value@@3HA, 119 ; 00000077H

; 109  :         int 3;

  0007f	cc		 int	 3

; 110  :         cmp     dword ptr [value], 77777777h;

  00080	81 3d 00 00 00
	00 77 77 77 77	 cmp	 DWORD PTR ?value@@3HA, 2004318071 ; 77777777H
$nearby$4:

; 111  :       nearby:
; 112  :         int 3

  0008a	cc		 int	 3

; 113  :         jo      nearby                                  ; 70xx

  0008b	70 fd		 jo	 SHORT $nearby$4

; 114  :         int 3

  0008d	cc		 int	 3

; 115  :         jno     nearby                                  ; 71xx

  0008e	71 fa		 jno	 SHORT $nearby$4

; 116  :         int 3

  00090	cc		 int	 3

; 117  :         jb      nearby                                  ; 72xx

  00091	72 f7		 jb	 SHORT $nearby$4

; 118  :         int 3

  00093	cc		 int	 3

; 119  :         jae     nearby                                  ; 73xx

  00094	73 f4		 jae	 SHORT $nearby$4

; 120  :         int 3

  00096	cc		 int	 3

; 121  :         je      nearby                                  ; 74xx

  00097	74 f1		 je	 SHORT $nearby$4

; 122  :         int 3

  00099	cc		 int	 3

; 123  :         jne     nearby                                  ; 75xx

  0009a	75 ee		 jne	 SHORT $nearby$4

; 124  :         int 3

  0009c	cc		 int	 3

; 125  :         jbe     nearby                                  ; 76xx

  0009d	76 eb		 jbe	 SHORT $nearby$4

; 126  :         int 3

  0009f	cc		 int	 3

; 127  :         ja      nearby                                  ; 77xx

  000a0	77 e8		 ja	 SHORT $nearby$4

; 128  :         int 3

  000a2	cc		 int	 3

; 129  :         js      nearby                                  ; 78xx

  000a3	78 e5		 js	 SHORT $nearby$4

; 130  :         int 3

  000a5	cc		 int	 3

; 131  :         jns     nearby                                  ; 79xx

  000a6	79 e2		 jns	 SHORT $nearby$4

; 132  :         int 3

  000a8	cc		 int	 3

; 133  :         jp      nearby                                  ; 7axx

  000a9	7a df		 jp	 SHORT $nearby$4

; 134  :         int 3

  000ab	cc		 int	 3

; 135  :         jnp     nearby                                  ; 7bxx

  000ac	7b dc		 jnp	 SHORT $nearby$4

; 136  :         int 3

  000ae	cc		 int	 3

; 137  :         jl      nearby                                  ; 7cxx

  000af	7c d9		 jl	 SHORT $nearby$4

; 138  :         int 3

  000b1	cc		 int	 3

; 139  :         jge     nearby                                  ; 7dxx

  000b2	7d d6		 jge	 SHORT $nearby$4

; 140  :         int 3

  000b4	cc		 int	 3

; 141  :         jle     nearby                                  ; 7exx

  000b5	7e d3		 jle	 SHORT $nearby$4

; 142  :         int 3

  000b7	cc		 int	 3

; 143  :         jg      nearby                                  ; 7fxx

  000b8	7f d0		 jg	 SHORT $nearby$4

; 144  : 
; 145  :         int 3

  000ba	cc		 int	 3

; 146  :         jo      faraway                                 ; 0f80xx

  000bb	0f 80 3f ff ff
	ff		 jo	 $faraway$3

; 147  :         int 3

  000c1	cc		 int	 3

; 148  :         jno     faraway                                 ; 0f81xx

  000c2	0f 81 38 ff ff
	ff		 jno	 $faraway$3

; 149  :         int 3

  000c8	cc		 int	 3

; 150  :         jb      faraway                                 ; 0f82xx

  000c9	0f 82 31 ff ff
	ff		 jb	 $faraway$3

; 151  :         int 3

  000cf	cc		 int	 3

; 152  :         jae     faraway                                 ; 0f83xx

  000d0	0f 83 2a ff ff
	ff		 jae	 $faraway$3

; 153  :         int 3

  000d6	cc		 int	 3

; 154  :         je      faraway                                 ; 0f84xx

  000d7	0f 84 23 ff ff
	ff		 je	 $faraway$3

; 155  :         int 3

  000dd	cc		 int	 3

; 156  :         jne     faraway                                 ; 0f85xx

  000de	0f 85 1c ff ff
	ff		 jne	 $faraway$3

; 157  :         int 3

  000e4	cc		 int	 3

; 158  :         jbe     faraway                                 ; 0f86xx

  000e5	0f 86 15 ff ff
	ff		 jbe	 $faraway$3

; 159  :         int 3

  000eb	cc		 int	 3

; 160  :         ja      faraway                                 ; 0f87xx

  000ec	0f 87 0e ff ff
	ff		 ja	 $faraway$3

; 161  :         int 3

  000f2	cc		 int	 3

; 162  :         js      faraway                                 ; 0f88xx

  000f3	0f 88 07 ff ff
	ff		 js	 $faraway$3

; 163  :         int 3

  000f9	cc		 int	 3

; 164  :         jns     faraway                                 ; 0f89xx

  000fa	0f 89 00 ff ff
	ff		 jns	 $faraway$3

; 165  :         int 3

  00100	cc		 int	 3

; 166  :         jp      faraway                                 ; 0f8axx

  00101	0f 8a f9 fe ff
	ff		 jp	 $faraway$3

; 167  :         int 3

  00107	cc		 int	 3

; 168  :         jnp     faraway                                 ; 0f8bxx

  00108	0f 8b f2 fe ff
	ff		 jnp	 $faraway$3

; 169  :         int 3

  0010e	cc		 int	 3

; 170  :         jl      faraway                                 ; 0f8cxx

  0010f	0f 8c eb fe ff
	ff		 jl	 $faraway$3

; 171  :         int 3

  00115	cc		 int	 3

; 172  :         jge     faraway                                 ; 0f8dxx

  00116	0f 8d e4 fe ff
	ff		 jge	 $faraway$3

; 173  :         int 3

  0011c	cc		 int	 3

; 174  :         jle     faraway                                 ; 0f8exx

  0011d	0f 8e dd fe ff
	ff		 jle	 $faraway$3

; 175  :         int 3

  00123	cc		 int	 3

; 176  :         jg      faraway                                 ; 0f8fxx

  00124	0f 8f d6 fe ff
	ff		 jg	 $faraway$3

; 177  : 
; 178  : // The list is terminated by two "int 3" in a row.
; 179  :         int 3;

  0012a	cc		 int	 3

; 180  :         int 3;

  0012b	cc		 int	 3

; 181  :         ret;

  0012c	c3		 ret	 0
_TestCodes ENDP
_TEXT	ENDS
END
