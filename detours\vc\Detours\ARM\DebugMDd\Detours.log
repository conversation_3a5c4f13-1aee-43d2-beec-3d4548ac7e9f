﻿  
  Microsoft (R) Program Maintenance Utility Version 14.41.34120.0
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main"
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\src"
   Created ..\include
   Created ..\lib.ARM
   Created ..\bin.ARM
   Created obj.ARM
  	cl /nologo /W4 /WX /we4777 /we4800 /Zi /MT /Gy /Gm- /Zl /Od /DDETOUR_DEBUG=0 /DWIN32_LEAN_AND_MEAN /D_WIN32_WINNT=0x501 /Fd..\lib.ARM\detours.pdb /Foobj.ARM\ /c detours.cpp modules.cpp disasm.cpp image.cpp creatwth.cpp disolx86.cpp disolx64.cpp disolia64.cpp disolarm.cpp disolarm64.cpp 
  detours.cpp
  modules.cpp
  disasm.cpp
  image.cpp
  creatwth.cpp
  disolx86.cpp
  disolx64.cpp
  disolia64.cpp
  disolarm.cpp
  disolarm64.cpp
  Generating Code...
  	link /lib /out:..\lib.ARM\detours.lib /nologo obj.ARM\detours.obj      obj.ARM\modules.obj      obj.ARM\disasm.obj       obj.ARM\image.obj        obj.ARM\creatwth.obj     obj.ARM\disolx86.obj     obj.ARM\disolx64.obj     obj.ARM\disolia64.obj    obj.ARM\disolarm.obj     obj.ARM\disolarm64.obj
  	copy detours.h ..\include\detours.h
          1 file(s) copied.
  	copy detver.h ..\include\detver.h
          1 file(s) copied.
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples"
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\syelog"
   Created obj.ARM
  	copy syelog.h ..\..\include\syelog.h
          1 file(s) copied.
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE /D_ARM_ /Fdobj.ARM\vc.pdb /Foobj.ARM\ /c syelog.cpp 
  syelog.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(8605): error C3861: '_ReadWriteBarrier': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(24581): error C3861: '__readfsdword': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9715): error C3861: '_InterlockedIncrement': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9724): error C3861: '_InterlockedIncrement': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9736): error C2065: 'InterlockedIncrement64': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9747): error C3861: '_InterlockedDecrement': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9756): error C3861: '_InterlockedDecrement': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9768): error C2065: 'InterlockedDecrement64': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9782): error C3861: '_InterlockedExchange': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9792): error C3861: '_InterlockedExchange': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9804): error C3861: 'InterlockedExchange64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9816): error C3861: '_InterlockedExchangeAdd': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9826): error C3861: '_InterlockedExchangeAdd': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9836): error C3861: '_InterlockedExchangeAdd': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9846): error C3861: '_InterlockedExchangeAdd': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9858): error C3861: 'InterlockedExchangeAdd64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9868): error C3861: 'InterlockedExchangeAdd64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9881): error C3861: '_InterlockedCompareExchange': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9892): error C3861: '_InterlockedCompareExchange': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9905): error C3861: '_InterlockedCompareExchange64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9915): error C3861: 'InterlockedAnd64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9925): error C3861: 'InterlockedOr64': identifier not found
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h(9935): error C3861: 'InterlockedXor64': identifier not found
NMAKE : fatal error U1077: 'cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE /D_ARM_ /Fdobj.ARM\vc.pdb /Foobj.ARM\ /c syelog.cpp ' : return code '0x2'
  Stop.
NMAKE : fatal error U1077: '"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.41.34120\bin\HostX86\x86\nmake.exe" /NOLOGO /L                 ' : return code '0x2'
  Stop.
NMAKE : fatal error U1077: '"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.41.34120\bin\HostX86\x86\nmake.exe" /NOLOGO /                  ' : return code '0x2'
  Stop.
C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: The command "SET DETOURS_TARGET_PROCESSOR=ARM
C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: cd ..
C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: nmake" exited with code 2.
