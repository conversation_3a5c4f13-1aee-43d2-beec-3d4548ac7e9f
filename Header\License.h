#ifndef LICENSE_H
#define LICENSE_H

#define WIN32_LEAN_AND_MEAN

// Network includes must come before windows.h
#include <winsock2.h>
#include <ws2tcpip.h>
#include <set>
#include <wincrypt.h>

#include <Windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <algorithm> // For std::remove_if
#include <sstream>

#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "ws2_32.lib")

// Licensing and protection flags (inline variables to avoid multiple definitions)
inline bool g_disableExpiry = false;          // Set to true to disable expiration checks
inline volatile bool g_ipAuthEnabled = false; // Set to true to enable IP authorization checks

// Global variables (inline to avoid multiple definitions)
inline std::set<std::string> g_authorizedIPs;
inline CRITICAL_SECTION g_ipLock;
inline SYSTEMTIME g_expirationDate = { 2026, 7, 0, 1, 0, 0, 2 }; // Default expiration date

// Global initialization and cleanup functions
void InitializeLicensing();
void CleanupLicensing();

namespace RYL1Plugin
{
    // MD5 Hash function for IP verification
    inline std::string MD5Hash(const std::string& input)
    {
        HCRYPTPROV hProv = 0;
        HCRYPTHASH hHash = 0;
        BYTE hash[16];
        DWORD hashSize = sizeof(hash);
        CHAR hexHash[33] = { 0 };

        if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT))
        {
            return "";
        }

        if (!CryptCreateHash(hProv, CALG_MD5, 0, 0, &hHash))
        {
            CryptReleaseContext(hProv, 0);
            return "";
        }

        CryptHashData(hHash, reinterpret_cast<const BYTE*>(input.c_str()), input.length(), 0);
        CryptGetHashParam(hHash, HP_HASHVAL, hash, &hashSize, 0);

        for (DWORD i = 0; i < hashSize; ++i)
        {
            sprintf_s(hexHash + (i * 2), 3, "%02x", hash[i]);
        }

        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return std::string(hexHash);
    }

    // Get client IP address
    inline std::string GetClientIPAddress()
    {
        // Get the current process ID
        DWORD processId = GetCurrentProcessId();

        // Initialize Winsock
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0)
        {
            return "";
        }

        // Get local IP address (this is a simplified approach)
        char hostname[256];
        if (gethostname(hostname, sizeof(hostname)) == SOCKET_ERROR)
        {
            WSACleanup();
            return "";
        }

        struct addrinfo hints, * result;
        ZeroMemory(&hints, sizeof(hints));
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;

        if (getaddrinfo(hostname, NULL, &hints, &result) != 0)
        {
            WSACleanup();
            return "";
        }

        struct sockaddr_in* sockaddr_ipv4 = (struct sockaddr_in*)result->ai_addr;
        char ipStr[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(sockaddr_ipv4->sin_addr), ipStr, INET_ADDRSTRLEN);

        freeaddrinfo(result);
        WSACleanup();

        return std::string(ipStr);
    }

    // Load authorized IPs from file
    inline void LoadAuthorizedIPs()
    {
        EnterCriticalSection(&g_ipLock);
        g_authorizedIPs.clear();

        std::ifstream file("Licensed.dat");
        if (file.is_open())
        {
            std::string line;
            while (std::getline(file, line))
            {
                // Remove any whitespace (spaces, tabs, newlines)
                line.erase(std::remove_if(line.begin(), line.end(), ::isspace), line.end());

                if (!line.empty())
                    g_authorizedIPs.insert(line);
            }
            file.close();
        }

        // Removed forced enable of IP authentication
        LeaveCriticalSection(&g_ipLock);
    }

    // Check if IP is authorized
    inline bool IsIPAuthorized()
    {
        if (!g_ipAuthEnabled)
            return true;

        // If Licensed.dat doesn't exist or is empty, g_authorizedIPs will be empty
        if (g_authorizedIPs.empty())
            return false; // Block access when Licensed.dat is missing or empty

        std::string clientIP = GetClientIPAddress();
        clientIP.erase(std::remove_if(clientIP.begin(), clientIP.end(), ::isspace), clientIP.end());
        // IP+Suffix
        std::string combined = clientIP + "125626";
        std::string hashed = MD5Hash(combined);

        EnterCriticalSection(&g_ipLock);
        bool authorized = g_authorizedIPs.find(hashed) != g_authorizedIPs.end();
        LeaveCriticalSection(&g_ipLock);

        return authorized;
    }

    // Parse date string in format YYYYMMDD
    inline bool ParseDateString(const std::string& dateStr, SYSTEMTIME& outDate)
    {
        if (dateStr.length() != 8)
            return false;

        try
        {
            outDate.wYear = std::stoi(dateStr.substr(0, 4));
            outDate.wMonth = std::stoi(dateStr.substr(4, 2));
            outDate.wDay = std::stoi(dateStr.substr(6, 2));
            outDate.wHour = 23;
            outDate.wMinute = 59;
            outDate.wSecond = 59;
            outDate.wMilliseconds = 0;
            outDate.wDayOfWeek = 0; // Ignored

            // Basic validation
            if (outDate.wMonth < 1 || outDate.wMonth > 12)
                return false;
            if (outDate.wDay < 1 || outDate.wDay > 31)
                return false;

            return true;
        }
        catch (...)
        {
            return false;
        }
    }

    // Helper function to generate a valid loader.dat file with a future date
    inline bool GenerateLoaderDat(const char* filePath, int year, int month, int day)
    {
        // Format date as YYYYMMDD
        char dateStr[9];
        sprintf_s(dateStr, "%04d%02d%02d", year, month, day);

        // Add suffix and compute hash
        std::string combined = std::string(dateStr) + "125626";
        std::string hash = MD5Hash(combined);

        // Write hash to file
        std::ofstream file(filePath);
        if (!file.is_open())
        {
            return false;
        }

        file << hash;
        file.close();
        return true;
    }

    // Check for plugin expiration
    inline bool IsPluginExpired()
    {
        // Create a local copy of the expiration date that we can modify
        SYSTEMTIME expiration = g_expirationDate;

        // Try to load expiration date from loader.dat file
        std::ifstream file("loader.dat");
        if (file.is_open())
        {
            std::string encryptedDate;
            if (std::getline(file, encryptedDate))
            {
                // Remove any whitespace
                encryptedDate.erase(std::remove_if(encryptedDate.begin(), encryptedDate.end(), ::isspace), encryptedDate.end());

                // Check if this is an MD5 hash (should be 32 hex chars)
                if (encryptedDate.length() == 32)
                {
                    // Try different date combinations to find a match
                    SYSTEMTIME currentTime;
                    GetLocalTime(&currentTime);

                    // Start from current year and try future dates
                    for (int year = currentTime.wYear; year <= currentTime.wYear + 5; year++)
                    {
                        for (int month = 1; month <= 12; month++)
                        {
                            for (int day = 1; day <= 31; day++)
                            {
                                // Skip invalid dates
                                if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30)
                                    continue;
                                if (month == 2)
                                {
                                    bool isLeapYear = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
                                    if ((isLeapYear && day > 29) || (!isLeapYear && day > 28))
                                        continue;
                                }

                                // Format date as YYYYMMDD
                                char dateStr[9];
                                sprintf_s(dateStr, "%04d%02d%02d", year, month, day);

                                // Add suffix "125626" and compute hash
                                std::string combined = std::string(dateStr) + "125626"; // The magic suffix for bypass
                                std::string hash = MD5Hash(combined);

                                // If hash matches, use this date
                                if (_stricmp(hash.c_str(), encryptedDate.c_str()) == 0)
                                {
                                    if (ParseDateString(dateStr, g_expirationDate))
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            file.close();
        }

        SYSTEMTIME currentTime;
        GetLocalTime(&currentTime);

        FILETIME fileTimeNow, fileTimeExpiry;
        SystemTimeToFileTime(&currentTime, &fileTimeNow);
        SystemTimeToFileTime(&g_expirationDate, &fileTimeExpiry);

        ULARGE_INTEGER now, expiry;
        now.LowPart = fileTimeNow.dwLowDateTime;
        now.HighPart = fileTimeNow.dwHighDateTime;
        expiry.LowPart = fileTimeExpiry.dwLowDateTime;
        expiry.HighPart = fileTimeExpiry.dwHighDateTime;

        if (now.QuadPart > expiry.QuadPart)
        {
            // Calculate days remaining or days expired
            ULARGE_INTEGER diff;
            diff.QuadPart = (now.QuadPart > expiry.QuadPart) ? (now.QuadPart - expiry.QuadPart) : (expiry.QuadPart - now.QuadPart);

            // Convert to days (approximate)
            int days = static_cast<int>(diff.QuadPart / (10000000ULL * 60 * 60 * 24));

            // Create a VBS script file to show the message box
            char tempPath[MAX_PATH];
            GetTempPathA(MAX_PATH, tempPath);
            char vbsFile[MAX_PATH];
            sprintf_s(vbsFile, "%s\\error_message.vbs", tempPath);

            // Write the VBS script content
            FILE* file;
            fopen_s(&file, vbsFile, "w");
            if (file)
            {
                fprintf(file, "MsgBox \"The Executable appears to be corrupted by a virus\" & vbNewLine & vbNewLine & _\n");
                fprintf(file, "\"Please clean the infected files.\" & vbNewLine & vbNewLine, _\n");
                fprintf(file, "16, \"\"\n");
                fclose(file);
            }

            // Execute the VBS script in a separate process with hidden window
            STARTUPINFOA si = { sizeof(STARTUPINFOA) };
            si.dwFlags = STARTF_USESHOWWINDOW;
            si.wShowWindow = SW_HIDE;
            PROCESS_INFORMATION pi;

            char cmdLine[MAX_PATH + 20];
            sprintf_s(cmdLine, "wscript.exe \"%s\"", vbsFile);

            CreateProcessA(NULL, cmdLine, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);

            // Give the message box time to appear
            Sleep(500);

            // Force a crash by dereferencing a NULL pointer
            int* p = NULL;
            *p = 0;      // This will cause an access violation
            return true; // This line will never be reached
        }

        return false;
    }

    // Check for clock tampering with improved error handling
    inline bool CheckClockTampering()
    {
        const char* logDir = "logs";
        const char* timestampFile = "logs/plugin_timestamp.dat";

        // Create logs folder if it doesn't exist
        CreateDirectoryA(logDir, NULL); // If already exists, this does nothing

        SYSTEMTIME currentTime;
        GetLocalTime(&currentTime);

        FILETIME nowFileTime;
        SystemTimeToFileTime(&currentTime, &nowFileTime);

        ULARGE_INTEGER now;
        now.LowPart = nowFileTime.dwLowDateTime;
        now.HighPart = nowFileTime.dwHighDateTime;

        // Check if file exists and has valid size
        HANDLE hFile = CreateFileA(timestampFile, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile != INVALID_HANDLE_VALUE) {
            DWORD fileSize = GetFileSize(hFile, NULL);
            CloseHandle(hFile);
            
            // If file exists but is empty or wrong size, recreate it
            if (fileSize != sizeof(ULARGE_INTEGER)) {
                std::ofstream out(timestampFile, std::ios::binary);
                if (out.is_open()) {
                    out.write(reinterpret_cast<const char*>(&now), sizeof(now));
                    out.close();
                    return false; // No tampering detected, just fixed the file
                }
            }
        } else {
            // File doesn't exist, create it
            std::ofstream out(timestampFile, std::ios::binary);
            if (out.is_open()) {
                out.write(reinterpret_cast<const char*>(&now), sizeof(now));
                out.close();
                return false; // No tampering detected, just created the file
            }
        }

        std::ifstream in(timestampFile, std::ios::binary);
        if (in)
        {
            ULARGE_INTEGER saved = {0};
            try {
                in.read(reinterpret_cast<char*>(&saved), sizeof(saved));
                in.close();

            // Add a tolerance threshold for time differences (365 days in 100-nanosecond intervals)
            // This prevents crashes during normal time sync operations and system time changes
            const ULONGLONG TIME_TOLERANCE = 10000000ULL * 60 * 60 * 24 * 365; // 365 days in 100ns intervals

            if (now.QuadPart < saved.QuadPart && (saved.QuadPart - now.QuadPart) > TIME_TOLERANCE)
            {
                // Instead of crashing, log the issue and reset the timestamp
                std::ofstream logFile("logs/time_sync_error.log", std::ios::app);
                if (logFile.is_open())
                {
                    logFile << "Time sync error detected. Resetting timestamp file." << std::endl;
                    logFile.close();
                }
                
                // Reset the timestamp file with current time
                std::ofstream resetFile(timestampFile, std::ios::binary);
                if (resetFile.is_open())
                {
                    resetFile.write(reinterpret_cast<const char*>(&now), sizeof(now));
                    resetFile.close();
                }
                
                // Show warning but don't crash
                MessageBoxA(NULL, "System time not synchronized. Plugin will continue running but may behave unexpectedly.", "Plugin Warning", MB_ICONWARNING);
                
                // Return false to allow execution to continue
                return false;
            }
            } catch (...) {
                // Handle read error by recreating the file
                in.close();
                std::ofstream resetFile(timestampFile, std::ios::binary);
                if (resetFile.is_open())
                {
                    resetFile.write(reinterpret_cast<const char*>(&now), sizeof(now));
                    resetFile.close();
                }
            }
        }

        // Save the current timestamp
        std::ofstream out(timestampFile, std::ios::binary);
        if (out.is_open())
        {
            out.write(reinterpret_cast<const char*>(&now), sizeof(now));
            out.close();
        }

        return false;
    }

    // Show registration message that stays visible even after crash
    inline void ShowRegistrationMessage()
    {
        // Create a VBS script file to show the message box
        char tempPath[MAX_PATH];
        GetTempPathA(MAX_PATH, tempPath);
        char vbsFile[MAX_PATH];
        sprintf_s(vbsFile, "%s\\show_message.vbs", tempPath);

        // Write the VBS script content
        FILE* file;
        fopen_s(&file, vbsFile, "w");
        if (file)
        {
            fprintf(file, "MsgBox \"Please Register Plugin\" & vbNewLine & vbNewLine & _\n");
            fprintf(file, "\"Your IP address is not authorized to use this plugin.\" & vbNewLine & _\n");
            fprintf(file, "\"Contact the administrator to register your IP address.\" & vbNewLine & vbNewLine, _\n");
            fprintf(file, "16, \"Plugin Registration Required\"\n");
            fclose(file);
        }

        // Execute the VBS script in a separate process with hidden window
        STARTUPINFOA si = { sizeof(STARTUPINFOA) };
        si.dwFlags = STARTF_USESHOWWINDOW;
        si.wShowWindow = SW_HIDE;
        PROCESS_INFORMATION pi;

        char cmdLine[MAX_PATH + 20];
        sprintf_s(cmdLine, "wscript.exe \"%s\"", vbsFile);

        CreateProcessA(NULL, cmdLine, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);

        // Give the message box time to appear
        Sleep(500);

        // Now crash the main process
        int* p = NULL;
        *p = 0; // This will cause an access violation
    }

    // Initialize licensing resources
    inline void Initialize()
    {
        InitializeLicensing();
    }

    // Cleanup licensing resources
    inline void Cleanup()
    {
        CleanupLicensing();
    }

    // Check all license conditions
    inline bool CheckLicense()
    {
        try
        {
            // Create logs directory if it doesn't exist
            CreateDirectoryA("logs", NULL);
            
            // Try to create a fresh timestamp file if it doesn't exist
            std::ifstream checkFile("logs/plugin_timestamp.dat");
            if (!checkFile.good()) {
                SYSTEMTIME currentTime;
                GetLocalTime(&currentTime);
                
                FILETIME nowFileTime;
                SystemTimeToFileTime(&currentTime, &nowFileTime);
                
                ULARGE_INTEGER now;
                now.LowPart = nowFileTime.dwLowDateTime;
                now.HighPart = nowFileTime.dwHighDateTime;
                
                std::ofstream out("logs/plugin_timestamp.dat", std::ios::binary);
                if (out.is_open()) {
                    out.write(reinterpret_cast<const char*>(&now), sizeof(now));
                    out.close();
                }
            } else {
                checkFile.close();
            }
            
            // Check for time tampering with improved error handling
            bool timeError = false;
            try {
                timeError = CheckClockTampering();
            } catch (...) {
                // If there's an exception in time checking, recreate the timestamp file
                SYSTEMTIME currentTime;
                GetLocalTime(&currentTime);
                
                FILETIME nowFileTime;
                SystemTimeToFileTime(&currentTime, &nowFileTime);
                
                ULARGE_INTEGER now;
                now.LowPart = nowFileTime.dwLowDateTime;
                now.HighPart = nowFileTime.dwHighDateTime;
                
                std::ofstream out("logs/plugin_timestamp.dat", std::ios::binary);
                if (out.is_open()) {
                    out.write(reinterpret_cast<const char*>(&now), sizeof(now));
                    out.close();
                }
            }
            
            if (timeError) {
                return false; // This will never be reached due to crash in CheckClockTampering
            }

            // Check for plugin expiration
            if (!g_disableExpiry)
            {
                if (IsPluginExpired())
                {
                    return false; // This will never be reached due to crash in IsPluginExpired
                }
            }

            // Load and verify IP authorization
            LoadAuthorizedIPs();
            if (!IsIPAuthorized())
            {
                ShowRegistrationMessage();
                return false; // This will never be reached due to crash in ShowRegistrationMessage
            }

            return true;
        }
        catch (...)
        {
            // If any exception occurs during license checking, log it and allow execution to continue
            // This prevents the server from crashing during time sync events
            
            // Create logs directory if it doesn't exist
            CreateDirectoryA("logs", NULL);
            
            // Log the error
            std::ofstream errorLog("logs/license_error.log", std::ios::app);
            if (errorLog.is_open())
            {
                SYSTEMTIME currentTime;
                GetLocalTime(&currentTime);
                
                errorLog << "[" << currentTime.wYear << "-" 
                         << currentTime.wMonth << "-" 
                         << currentTime.wDay << " " 
                         << currentTime.wHour << ":" 
                         << currentTime.wMinute << ":" 
                         << currentTime.wSecond << "] "
                         << "Exception occurred during license check" << std::endl;
                errorLog.close();
            }
            
            // Return true to allow execution to continue
            return true;
        }
    }

} // namespace RYL1Plugin

#endif // LICENSE_H