
//----- (00439350) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Rarg,
        const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *)operator new((tagHeader *)0x2C);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Right = _Rarg;
    result->_Parent = _Parg;
    qmemcpy(&result->_Myval, _Val, sizeof(result->_Myval));
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (004393A0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Erase(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (004393E0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::equal_range(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> *result,
        const int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v5; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v6; // eax
  std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> *v7; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( *_Keyval >= Parent->_Myval.first )
    {
      Parent = Parent->_Right;
    }
    else
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
  }
  v5 = this->_Myhead;
  v6 = v5->_Parent;
  while ( !v6->_Isnil )
  {
    if ( v6->_Myval.first >= *_Keyval )
    {
      v5 = v6;
      v6 = v6->_Left;
    }
    else
    {
      v6 = v6->_Right;
    }
  }
  v7 = result;
  result->first._Ptr = v5;
  result->second._Ptr = Myhead;
  return v7;
}

//----- (00439450) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *)operator new((tagHeader *)0x2C);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (00439490) --------------------------------------------------------
void __thiscall CMonsterShout::Shout(
        CMonsterShout *this,
        unsigned int dwMonsterCID,
        CPacketDispatch *nKID,
        unsigned __int16 usXPos,
        unsigned __int16 usZPos,
        CMonsterShout::Behavior eBehavior,
        const char *szName,
        unsigned __int16 usSkill_ID)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // esi
  char v9; // bl
  int m_nSkill_ID; // eax
  signed int v11; // eax
  CMonsterShout::ChatNode *m_lpNextNode; // edi
  signed int v13; // ecx
  int m_nPercentage; // ebx
  const char *v15; // ebx
  char *v16; // eax
  unsigned int v17; // kr00_4
  unsigned int v18; // ebx
  char *m_szMonsterChat; // esi
  unsigned __int16 m_usChatLength; // ax
  CSingleDispatch *DispatchTable; // eax
  std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> result; // [esp+10h] [ebp-3C8h] BYREF
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+18h] [ebp-3C0h] BYREF
  char szNameBuffer[184]; // [esp+20h] [ebp-3B8h] BYREF
  char szChatBuffer[360]; // [esp+D8h] [ebp-300h] BYREF
  CChatPacket chatPacket; // [esp+240h] [ebp-198h] BYREF
  int v27; // [esp+3D4h] [ebp-4h]

  StoragelpChatDispatch.m_lpDispatch = nKID;
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::equal_range(
    &this->m_ShoutMap,
    &result,
    (const int *)&StoragelpChatDispatch);
  Ptr = result.first._Ptr;
  if ( result.first._Ptr != result.second._Ptr )
  {
    while ( 1 )
    {
      v9 = 0;
      if ( Ptr->_Myval.second.m_nBehavior == eBehavior )
      {
        if ( eBehavior != RESPAWN && eBehavior != DEAD
          || (m_nSkill_ID = Ptr->_Myval.second.m_nSkill_ID, m_nSkill_ID == 0xFFFF)
          || m_nSkill_ID == usSkill_ID )
        {
          v9 = 1;
        }
        v11 = Math::Random::ComplexRandom(100, 1);
        if ( v9 )
        {
          if ( v11 < Ptr->_Myval.second.m_nTotalPercentage )
            break;
        }
      }
      std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(&result.first);
      if ( result.first._Ptr == result.second._Ptr )
        return;
      Ptr = result.first._Ptr;
    }
    m_lpNextNode = Ptr->_Myval.second.m_lpNextNode;
    v13 = 0;
    if ( m_lpNextNode )
    {
      while ( v13 > v11 || v11 > v13 + m_lpNextNode->m_nPercentage )
      {
        m_nPercentage = m_lpNextNode->m_nPercentage;
        m_lpNextNode = m_lpNextNode->m_lpNextNode;
        v13 += m_nPercentage;
        if ( !m_lpNextNode )
          return;
      }
      szChatBuffer[0] = 0;
      szNameBuffer[0] = 0;
      if ( szName )
        _snprintf(szNameBuffer, 0xB3u, "%s", szName);
      switch ( Ptr->_Myval.second.m_nBehavior )
      {
        case 0:
        case 2:
        case 4:
          v15 = "$DEFNAME$";
          goto LABEL_25;
        case 1:
        case 3:
        case 5:
          v15 = "$ATTNAME$";
          goto LABEL_25;
        case 7:
          v15 = "$KILLERNAME$";
LABEL_25:
          if ( !v15 )
            goto LABEL_28;
          v16 = strstr(m_lpNextNode->m_szMonsterChat, v15);
          if ( !v16 )
            goto LABEL_28;
          v17 = strlen(v15);
          v18 = v16 - (char *)m_lpNextNode - 10;
          qmemcpy(szChatBuffer, m_lpNextNode->m_szMonsterChat, v18);
          m_szMonsterChat = szChatBuffer;
          m_usChatLength = v18 + _snprintf(&szChatBuffer[v18], 0x167u, "%s%s", szNameBuffer, &v16[v17]);
          break;
        default:
LABEL_28:
          m_usChatLength = m_lpNextNode->m_usChatLength;
          m_szMonsterChat = m_lpNextNode->m_szMonsterChat;
          break;
      }
      CChatPacket::CChatPacket(
        &chatPacket,
        dwMonsterCID,
        m_szMonsterChat,
        m_usChatLength,
        (PktCt::PktCtCmd)result.first._Ptr->_Myval.second.m_nChatType,
        usXPos,
        usZPos);
      DispatchTable = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
      v27 = 0;
      if ( StoragelpChatDispatch.m_lpDispatch )
        CSendStream::PutBuffer(
          (CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8],
          chatPacket.m_dwPacketSize != 0 ? chatPacket.m_szBuffer : 0,
          chatPacket.m_dwPacketSize,
          0xDu);
      v27 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
    }
  }
}

//----- (00439700) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Insert(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode,
        const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<int const ,CMonsterShout::ShoutInfo> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x9249248 )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<int const ,CMonsterShout::ShoutInfo> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004398B0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
              this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
              this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
                this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00439B70) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::insert(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *result,
        const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // esi
  int first; // edi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *v7; // eax
  bool _Addleft; // [esp+8h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = Parent;
      _Addleft = first < Parent->_Myval.first;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  Ptr = std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Insert(
          this,
          (std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *)&_Val,
          _Addleft,
          Myhead,
          _Val)->_Ptr;
  v7 = result;
  result->first._Ptr = Ptr;
  result->second = 1;
  return v7;
}

//----- (00439BE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _First,
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00439CA0) --------------------------------------------------------
void __thiscall CMonsterShout::AddMonsterShout(
        CMonsterShout *this,
        CMonsterShout::ShoutInfo *shoutInfo,
        CMonsterShout::ChatNode *chatNode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // ebp
  int m_nKID; // esi
  CMonsterShout::ShoutInfo *m_lpNextNode; // esi
  CMonsterShout::ChatNode *v7; // eax
  CMonsterShout::ChatNode *v8; // eax
  int v9; // ecx
  int m_nBehavior; // eax
  int v11; // edx
  int m_nSkill_ID; // ecx
  int m_nChatType; // edx
  int m_nTotalPercentage; // eax
  CMonsterShout::ChatNode *v15; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> result; // [esp+14h] [ebp-24h] BYREF
  std::pair<int const ,CMonsterShout::ShoutInfo> _Val; // [esp+1Ch] [ebp-1Ch] BYREF
  CMonsterShout::ShoutInfo *shoutInfoa; // [esp+3Ch] [ebp+4h]

  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::equal_range(
    &this->m_ShoutMap,
    &result,
    &shoutInfo->m_nKID);
  Ptr = result.first._Ptr;
  if ( result.first._Ptr == result.second._Ptr )
    goto LABEL_17;
  m_nKID = shoutInfo->m_nKID;
  while ( Ptr->_Myval.second.m_nKID != m_nKID
       || Ptr->_Myval.second.m_nBehavior != shoutInfo->m_nBehavior
       || Ptr->_Myval.second.m_nSkill_ID != shoutInfo->m_nSkill_ID
       || Ptr->_Myval.second.m_nChatType != shoutInfo->m_nChatType )
  {
    std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(&result.first);
    Ptr = result.first._Ptr;
    if ( result.first._Ptr == result.second._Ptr )
      goto LABEL_17;
  }
  m_lpNextNode = (CMonsterShout::ShoutInfo *)Ptr->_Myval.second.m_lpNextNode;
  Ptr->_Myval.second.m_nTotalPercentage += chatNode->m_nPercentage;
  shoutInfoa = m_lpNextNode;
  if ( m_lpNextNode )
  {
    while ( m_lpNextNode->m_nKID )
    {
      shoutInfoa = (CMonsterShout::ShoutInfo *)m_lpNextNode->m_nKID;
      m_lpNextNode = (CMonsterShout::ShoutInfo *)m_lpNextNode->m_nKID;
    }
    v7 = (CMonsterShout::ChatNode *)operator new((tagHeader *)0x110);
    if ( v7 )
    {
      qmemcpy(v7, chatNode, sizeof(CMonsterShout::ChatNode));
      m_lpNextNode = shoutInfoa;
    }
    else
    {
      v7 = 0;
    }
    m_lpNextNode->m_nKID = (int)v7;
  }
  if ( Ptr == result.second._Ptr )
  {
LABEL_17:
    v8 = (CMonsterShout::ChatNode *)operator new((tagHeader *)0x110);
    if ( v8 )
      qmemcpy(v8, chatNode, sizeof(CMonsterShout::ChatNode));
    else
      v8 = 0;
    v9 = shoutInfo->m_nKID;
    shoutInfo->m_lpNextNode = v8;
    shoutInfo->m_nTotalPercentage = chatNode->m_nPercentage;
    m_nBehavior = shoutInfo->m_nBehavior;
    v11 = v9;
    _Val.first = v9;
    m_nSkill_ID = shoutInfo->m_nSkill_ID;
    _Val.second.m_nKID = v11;
    m_nChatType = shoutInfo->m_nChatType;
    _Val.second.m_nBehavior = m_nBehavior;
    m_nTotalPercentage = shoutInfo->m_nTotalPercentage;
    _Val.second.m_nSkill_ID = m_nSkill_ID;
    v15 = shoutInfo->m_lpNextNode;
    _Val.second.m_nChatType = m_nChatType;
    _Val.second.m_nTotalPercentage = m_nTotalPercentage;
    _Val.second.m_lpNextNode = v15;
    std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::insert(
      &this->m_ShoutMap,
      (std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *)&result,
      &_Val);
  }
}

//----- (00439DE0) --------------------------------------------------------
void __thiscall CMonsterShout::~CMonsterShout(CMonsterShout *this)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // ebx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Left; // eax
  CMonsterShout::ChatNode *m_lpNextNode; // esi
  CMonsterShout::ChatNode *v5; // eax
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator pos; // [esp+8h] [ebp-4h] BYREF

  Myhead = this->m_ShoutMap._Myhead;
  Left = Myhead->_Left;
  for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
  {
    m_lpNextNode = Left->_Myval.second.m_lpNextNode;
    while ( m_lpNextNode )
    {
      v5 = m_lpNextNode;
      m_lpNextNode = m_lpNextNode->m_lpNextNode;
      operator delete(v5);
    }
    std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(&pos);
  }
  std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(
    &this->m_ShoutMap,
    &pos,
    (std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator)this->m_ShoutMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator)this->m_ShoutMap._Myhead);
  operator delete(this->m_ShoutMap._Myhead);
  this->m_ShoutMap._Myhead = 0;
  this->m_ShoutMap._Mysize = 0;
}

//----- (00439E60) --------------------------------------------------------
CMonsterShout *__cdecl CMonsterShout::GetInstance()
{
  if ( (_S5_1 & 1) == 0 )
  {
    _S5_1 |= 1u;
    monsterShout.m_ShoutMap._Myhead = std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(&monsterShout.m_ShoutMap);
    monsterShout.m_ShoutMap._Myhead->_Isnil = 1;
    monsterShout.m_ShoutMap._Myhead->_Parent = monsterShout.m_ShoutMap._Myhead;
    monsterShout.m_ShoutMap._Myhead->_Left = monsterShout.m_ShoutMap._Myhead;
    monsterShout.m_ShoutMap._Myhead->_Right = monsterShout.m_ShoutMap._Myhead;
    monsterShout.m_ShoutMap._Mysize = 0;
    atexit(_E6_3);
  }
  return &monsterShout;
}

//----- (00439EF0) --------------------------------------------------------
void __cdecl SetShoutText(
        int nKID,
        int nBehavior,
        int nSkill_ID,
        int nChatType,
        int nPercentage,
        const char *szMessage)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CMonsterShout *Instance; // eax
  CMonsterShout::ShoutInfo shoutInfo; // [esp+8h] [ebp-130h] BYREF
  CMonsterShout::ChatNode chatNode; // [esp+20h] [ebp-118h] BYREF

  shoutInfo.m_nTotalPercentage = 0;
  memset(&chatNode, 0, sizeof(chatNode));
  shoutInfo.m_nSkill_ID = nSkill_ID;
  shoutInfo.m_lpNextNode = 0;
  chatNode.m_nPercentage = nPercentage;
  shoutInfo.m_nKID = nKID;
  shoutInfo.m_nBehavior = nBehavior;
  shoutInfo.m_nChatType = nChatType;
  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, nKID);
  if ( szMessage && MonsterProtoType )
  {
    if ( nBehavior == 6 )
      _snprintf(chatNode.m_szMonsterChat, 0x103u, "%s", szMessage);
    else
      _snprintf(chatNode.m_szMonsterChat, 0x103u, "%s : %s", MonsterProtoType->m_MonsterInfo.m_strName, szMessage);
    chatNode.m_szMonsterChat[259] = 0;
    chatNode.m_usChatLength = strlen(chatNode.m_szMonsterChat) + 1;
    Instance = CMonsterShout::GetInstance();
    CMonsterShout::AddMonsterShout(Instance, &shoutInfo, &chatNode);
  }
}

//----- (00439FF0) --------------------------------------------------------
char __thiscall CMonsterShout::LoadScript(CMonsterShout *this, char *szFileName)
{
  struct CVirtualMachine *v2; // eax
  struct CVirtualMachine *v3; // esi

  v2 = _SE_Create(szFileName);
  v3 = v2;
  if ( !v2 )
    return 0;
  _SE_RegisterFunction(v2, (char *)SetShoutText, T_VOID, "MonsterChat", 2, 2, 2, 2, 2, 4, 0);
  _SE_Execute(v3);
  _SE_Destroy(v3);
  return 1;
}

//----- (0043A040) --------------------------------------------------------
int __usercall Math::Convert::StrToHex16@<eax>(int a1@<eax>, int a2@<ecx>, int a3@<esi>, const char *szSrc)
{
  char v4; // al
  int v5; // ecx
  int v6; // eax
  char v7; // cl
  int v8; // esi

  v4 = szSrc[2];
  if ( v4 < 48 || v4 > 57 )
  {
    if ( v4 < 65 || v4 > 70 )
    {
      if ( v4 >= 97 && v4 <= 102 )
        v4 -= 87;
    }
    else
    {
      v4 -= 55;
    }
  }
  else
  {
    v4 -= 48;
  }
  LOWORD(a2) = v4;
  LOBYTE(a1) = szSrc[3];
  v5 = a2 << 12;
  if ( (char)a1 < 48 || (char)a1 > 57 )
  {
    if ( (char)a1 < 65 || (char)a1 > 70 )
    {
      if ( (char)a1 >= 97 && (char)a1 <= 102 )
        LOBYTE(a1) = a1 - 87;
    }
    else
    {
      LOBYTE(a1) = a1 - 55;
    }
  }
  else
  {
    LOBYTE(a1) = a1 - 48;
  }
  LOWORD(a1) = (char)a1;
  v6 = v5 + (a1 << 8);
  v7 = szSrc[4];
  if ( v7 < 48 || v7 > 57 )
  {
    if ( v7 < 65 || v7 > 70 )
    {
      if ( v7 >= 97 && v7 <= 102 )
        v7 -= 87;
    }
    else
    {
      v7 -= 55;
    }
  }
  else
  {
    v7 -= 48;
  }
  LOWORD(a3) = v7;
  LOBYTE(v5) = szSrc[5];
  v8 = 16 * a3;
  if ( (char)v5 < 48 || (char)v5 > 57 )
  {
    if ( (char)v5 < 65 || (char)v5 > 70 )
    {
      if ( (char)v5 >= 97 && (char)v5 <= 102 )
        LOBYTE(v5) = v5 - 87;
      LOWORD(v5) = (char)v5;
      return v8 + v5 + v6;
    }
    else
    {
      LOWORD(v5) = (char)(v5 - 55);
      return v8 + v5 + v6;
    }
  }
  else
  {
    LOWORD(v5) = (char)(v5 - 48);
    return v8 + v5 + v6;
  }
}
// 43A040: could not find valid save-restore pair for esi

//----- (0043A120) --------------------------------------------------------
CSkillMgr::ProtoTypeArray *__thiscall CSkillMgr::GetSkillProtoType(CSkillMgr *this, unsigned __int16 usSkill_ID)
{
  CSkillMgr::ProtoTypeArray *result; // eax
  unsigned int m_nSkillNum; // esi
  CSkillMgr::ProtoTypeArray *v4; // edi
  const Skill::ProtoType *m_ProtoTypes; // edx

  result = this->m_ProtoTypeArray;
  m_nSkillNum = this->m_nSkillNum;
  v4 = &this->m_ProtoTypeArray[m_nSkillNum];
  while ( m_nSkillNum )
  {
    m_ProtoTypes = result[m_nSkillNum >> 1].m_ProtoTypes;
    if ( m_ProtoTypes->m_usSkill_ID >= usSkill_ID )
    {
      m_nSkillNum >>= 1;
    }
    else
    {
      result = (CSkillMgr::ProtoTypeArray *)&m_ProtoTypes[5];
      m_nSkillNum += -1 - (m_nSkillNum >> 1);
    }
  }
  if ( result == v4 || usSkill_ID < result->m_ProtoTypes[0].m_usSkill_ID )
    return 0;
  return result;
}

//----- (0043A180) --------------------------------------------------------
unsigned __int8 __thiscall CSkillMgr::ReadStringToTypeValue(
        CSkillMgr *this,
        CDelimitedFile *DelimitedFile,
        const char *szColumn,
        const CTypeName *TypeArray,
        unsigned __int8 nMaxType)
{
  unsigned __int8 v6; // bl
  unsigned __int8 i; // dl
  int v8; // eax
  char szReadData[260]; // [esp+0h] [ebp-108h] BYREF

  if ( CDelimitedFile::ReadString(DelimitedFile, szColumn, szReadData, 0x104u) )
  {
    v6 = nMaxType;
    for ( i = 0; i < nMaxType; ++i )
    {
      v8 = strcmp(szReadData, TypeArray[i].m_lpszName);
      v6 = nMaxType;
      if ( !v8 )
        break;
    }
    if ( i == v6 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSkillMgr::ReadStringToTypeValue",
        aDWorkRylSource_19,
        409,
        (char *)&byte_4DD570);
      return v6;
    }
    else
    {
      return TypeArray[i].m_nType;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMgr::ReadStringToTypeValue",
      aDWorkRylSource_19,
      395,
      (char *)&byte_4DD63C);
    return nMaxType;
  }
}

//----- (0043A2C0) --------------------------------------------------------
char __thiscall CSkillMgr::CheckParentChildRule(CSkillMgr *this)
{
  CSkillMgr::ProtoTypeArray *m_ProtoTypeArray; // esi
  unsigned __int16 m_usParentSkill; // si
  CSkillMgr::ProtoTypeArray *SkillProtoType; // edi
  CSkillMgr::ProtoTypeArray *v6; // eax
  CSkillMgr::ProtoTypeArray *lpPointer; // [esp+10h] [ebp-210h]
  CSkillMgr::ProtoTypeArray *v8; // [esp+14h] [ebp-20Ch]
  Skill::ProtoType ProtoType; // [esp+18h] [ebp-208h] BYREF

  m_ProtoTypeArray = this->m_ProtoTypeArray;
  lpPointer = this->m_ProtoTypeArray;
  v8 = &this->m_ProtoTypeArray[this->m_nSkillNum];
  if ( this->m_ProtoTypeArray == v8 )
    return 1;
  while ( 1 )
  {
    qmemcpy((void *)&ProtoType, m_ProtoTypeArray, sizeof(ProtoType));
    m_usParentSkill = ProtoType.m_usParentSkill;
    SkillProtoType = CSkillMgr::GetSkillProtoType(this, ProtoType.m_usParentSkill);
    v6 = CSkillMgr::GetSkillProtoType(this, ProtoType.m_usChildSkill);
    if ( m_usParentSkill && !SkillProtoType )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSkillMgr::CheckParentChildRule",
        aDWorkRylSource_19,
        429,
        byte_4DD758,
        ProtoType.m_usSkill_ID);
      return 0;
    }
    if ( ProtoType.m_usChildSkill && !v6 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSkillMgr::CheckParentChildRule",
        aDWorkRylSource_19,
        436,
        byte_4DD700,
        ProtoType.m_usSkill_ID);
      return 0;
    }
    if ( SkillProtoType || v6 )
    {
      if ( ProtoType.m_eSkillType == MASTER )
      {
        if ( ProtoType.m_usChildSkill )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CSkillMgr::CheckParentChildRule",
            aDWorkRylSource_19,
            449,
            byte_4DD660,
            ProtoType.m_usSkill_ID);
          return 0;
        }
      }
      else if ( ProtoType.m_eSkillType == LEAVE_WAIT )
      {
        if ( m_usParentSkill )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CSkillMgr::CheckParentChildRule",
            aDWorkRylSource_19,
            460,
            byte_4DD6B0,
            ProtoType.m_usSkill_ID);
          return 0;
        }
        if ( ProtoType.m_usChildSkill && v6->m_ProtoTypes[0].m_eSkillType == LEAVE_WAIT )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CSkillMgr::CheckParentChildRule",
            aDWorkRylSource_19,
            469,
            (char *)&byte_4DD7D0,
            ProtoType.m_usSkill_ID);
          return 0;
        }
      }
    }
    if ( ++lpPointer == v8 )
      break;
    m_ProtoTypeArray = lpPointer;
  }
  return 1;
}

//----- (0043A460) --------------------------------------------------------
CSkillMgr::ProtoTypeArray *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::size(
        std::vector<CSkillMgr::ProtoTypeArray> *this)
{
  CSkillMgr::ProtoTypeArray *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (CSkillMgr::ProtoTypeArray *)(this->_Mylast - result);
  return result;
}

//----- (0043A480) --------------------------------------------------------
void __cdecl std::fill<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray>(
        CSkillMgr::ProtoTypeArray *_First,
        CSkillMgr::ProtoTypeArray *_Last,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  CSkillMgr::ProtoTypeArray *i; // eax
  CSkillMgr::ProtoTypeArray *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy(v4, _Val, sizeof(CSkillMgr::ProtoTypeArray));
  }
}

//----- (0043A4B0) --------------------------------------------------------
int __cdecl Math::Convert::Atos(char *szSrc)
{
  int v1; // ecx
  int v2; // eax

  _mbsnbcmp((unsigned __int8 *)szSrc, "0x", 2u);
  if ( v2 )
    return atoi(szSrc);
  else
    return (unsigned __int16)Math::Convert::StrToHex16(0, v1, (int)szSrc, szSrc);
}
// 43A4C8: variable 'v2' is possibly undefined
// 43A4CA: variable 'v1' is possibly undefined

//----- (0043A4F0) --------------------------------------------------------
void __thiscall CSkillMgr::CSkillMgr(CSkillMgr *this)
{
  CSingleton<CSkillMgr>::ms_pSingleton = this;
  this->m_ProtoTypeArray = 0;
  this->m_nSkillNum = 0;
  this->m_dwCRC32 = 0;
}

//----- (0043A510) --------------------------------------------------------
void __thiscall CSkillMgr::~CSkillMgr(CSkillMgr *this)
{
  if ( this->m_ProtoTypeArray )
  {
    operator delete[](this->m_ProtoTypeArray);
    this->m_ProtoTypeArray = 0;
  }
  CSingleton<CSkillMgr>::ms_pSingleton = 0;
}

//----- (0043A540) --------------------------------------------------------
CSkillMgr::ProtoTypeArray *__cdecl std::copy_backward<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *>(
        CSkillMgr::ProtoTypeArray *_First,
        CSkillMgr::ProtoTypeArray *_Last,
        CSkillMgr::ProtoTypeArray *_Dest)
{
  CSkillMgr::ProtoTypeArray *v3; // edx
  CSkillMgr::ProtoTypeArray *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy(--result, --v3, sizeof(CSkillMgr::ProtoTypeArray));
  return result;
}

//----- (0043A580) --------------------------------------------------------
void __cdecl std::iter_swap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Left,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Right)
{
  _BYTE v2[2560]; // [esp+8h] [ebp-A08h] BYREF

  qmemcpy(v2, _Left._Myptr, sizeof(v2));
  qmemcpy(_Left._Myptr, _Right._Myptr, sizeof(CSkillMgr::ProtoTypeArray));
  qmemcpy(_Right._Myptr, v2, sizeof(CSkillMgr::ProtoTypeArray));
}

//----- (0043A5E0) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  if ( _Mid._Myptr->m_ProtoTypes[0].m_usSkill_ID < _First._Myptr->m_ProtoTypes[0].m_usSkill_ID )
    std::iter_swap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      _Mid,
      _First);
  if ( _Last._Myptr->m_ProtoTypes[0].m_usSkill_ID < _Mid._Myptr->m_ProtoTypes[0].m_usSkill_ID )
    std::iter_swap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      _Last,
      _Mid);
  if ( _Mid._Myptr->m_ProtoTypes[0].m_usSkill_ID < _First._Myptr->m_ProtoTypes[0].m_usSkill_ID )
    std::iter_swap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      _Mid,
      _First);
}

//----- (0043A640) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        int _Hole,
        int _Top,
        CSkillMgr::ProtoTypeArray _Val)
{
  int v4; // ecx
  int i; // eax
  CSkillMgr::ProtoTypeArray *v6; // esi

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    if ( v6->m_ProtoTypes[0].m_usSkill_ID >= _Val.m_ProtoTypes[0].m_usSkill_ID )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (0043A6B0) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  int v3; // ecx
  int v4; // edi
  int v5; // ebx
  int v6; // edx
  int v7; // ebx
  CSkillMgr::ProtoTypeArray *v8; // edx
  CSkillMgr::ProtoTypeArray *v9; // eax
  std::vector<CSkillMgr::ProtoTypeArray>::iterator *p_First; // ecx
  CSkillMgr::ProtoTypeArray *Myptr; // ebx
  int v12; // eax
  const void **v13; // eax
  bool v14; // zf
  CSkillMgr::ProtoTypeArray *v15; // [esp+10h] [ebp-A28h]
  int v16; // [esp+14h] [ebp-A24h]
  std::vector<CSkillMgr::ProtoTypeArray>::iterator _Next; // [esp+18h] [ebp-A20h]
  int v18; // [esp+1Ch] [ebp-A1Ch]
  char *v19; // [esp+20h] [ebp-A18h] BYREF
  CSkillMgr::ProtoTypeArray *v20; // [esp+24h] [ebp-A14h] BYREF
  CSkillMgr::ProtoTypeArray *v21; // [esp+28h] [ebp-A10h] BYREF
  int _Shift; // [esp+2Ch] [ebp-A0Ch]
  CSkillMgr::ProtoTypeArray _Holeval; // [esp+30h] [ebp-A08h] BYREF

  v3 = _Mid._Myptr - _First._Myptr;
  _Shift = v3;
  v4 = _Last._Myptr - _First._Myptr;
  v5 = v3;
  if ( v3 )
  {
    do
    {
      v6 = v4 % v5;
      v4 = v5;
      v5 = v6;
    }
    while ( v6 );
  }
  if ( v4 < _Last._Myptr - _First._Myptr && v4 > 0 )
  {
    v7 = 2560 * v3;
    v8 = &_First._Myptr[v4];
    v16 = v3;
    v15 = v8;
    v18 = v4;
    while ( 1 )
    {
      qmemcpy(&_Holeval, v8, sizeof(_Holeval));
      v9 = v8;
      if ( (char *)v8 + v7 == (char *)_Last._Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v19 = (char *)v8 + v7;
        p_First = (std::vector<CSkillMgr::ProtoTypeArray>::iterator *)&v19;
      }
      Myptr = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          qmemcpy(v9, Myptr, sizeof(CSkillMgr::ProtoTypeArray));
          v12 = _Last._Myptr - Myptr;
          _Next._Myptr = Myptr;
          if ( _Shift >= v12 )
          {
            v21 = &_First._Myptr[_Shift - v12];
            v13 = (const void **)&v21;
          }
          else
          {
            v20 = &Myptr[v16];
            v13 = (const void **)&v20;
          }
          Myptr = (CSkillMgr::ProtoTypeArray *)*v13;
          v14 = *v13 == v15;
          v9 = _Next._Myptr;
        }
        while ( !v14 );
        v8 = v15;
      }
      --v8;
      v14 = v18 == 1;
      qmemcpy(v9, &_Holeval, sizeof(CSkillMgr::ProtoTypeArray));
      v15 = v8;
      --v18;
      if ( v14 )
        break;
      v7 = v16 * 2560;
    }
  }
}

//----- (0043A820) --------------------------------------------------------
CSkillMgr::ProtoTypeArray *__cdecl std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(
        CSkillMgr::ProtoTypeArray *_First,
        CSkillMgr::ProtoTypeArray *_Last,
        CSkillMgr::ProtoTypeArray *_Dest)
{
  CSkillMgr::ProtoTypeArray *v3; // edx
  CSkillMgr::ProtoTypeArray *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
      qmemcpy(result, v3, sizeof(CSkillMgr::ProtoTypeArray));
    ++v3;
  }
  return result;
}

//----- (0043A860) --------------------------------------------------------
void __cdecl std::_Median<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  int v3; // eax
  unsigned int v4; // edi
  unsigned int v5; // esi
  CSkillMgr::ProtoTypeArray *v7; // [esp-10h] [ebp-14h]
  CSkillMgr::ProtoTypeArray *_Firsta; // [esp+8h] [ebp+4h]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(_First, _Mid, _Last);
  }
  else
  {
    v4 = 5120 * ((v3 + 1) / 8);
    v5 = 2560 * ((v3 + 1) / 8);
    v7 = &_First._Myptr[v4 / 0xA00];
    _Firsta = &_First._Myptr[v5 / 0xA00];
    std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      _First,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)_Firsta,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v7);
    std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Mid._Myptr[v5 / 0xFFFFF600],
      _Mid,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Mid._Myptr[v5 / 0xA00]);
    std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Last._Myptr[v4 / 0xFFFFF600],
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Last._Myptr[v5 / 0xFFFFF600],
      _Last);
    std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)_Firsta,
      _Mid,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Last._Myptr[v5 / 0xFFFFF600]);
  }
}

//----- (0043A900) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        int _Hole,
        int _Bottom,
        CSkillMgr::ProtoTypeArray _Val)
{
  int v4; // edx
  int v5; // eax
  bool v6; // zf
  int v7; // edi
  CSkillMgr::ProtoTypeArray *v8; // esi
  CSkillMgr::ProtoTypeArray v9; // [esp-A00h] [ebp-A10h] BYREF

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  v6 = v5 == _Bottom;
  while ( v5 < _Bottom )
  {
    if ( _First._Myptr[v5].m_ProtoTypes[0].m_usSkill_ID < _First._Myptr[v5 - 1].m_ProtoTypes[0].m_usSkill_ID )
      --v5;
    v7 = v4;
    v4 = v5;
    v8 = &_First._Myptr[v5];
    v5 = 2 * v5 + 2;
    v6 = v5 == _Bottom;
    qmemcpy(&_First._Myptr[v7], v8, sizeof(_First._Myptr[v7]));
  }
  if ( v6 )
  {
    qmemcpy(&_First._Myptr[v4], &_First._Myptr[_Bottom - 1], sizeof(_First._Myptr[v4]));
    v4 = _Bottom - 1;
  }
  qmemcpy(&v9, &_Val, sizeof(v9));
  std::_Push_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(_First, v4, _Hole, v9);
}

//----- (0043A9B0) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CSkillMgr::ProtoTypeArray *,unsigned int,CSkillMgr::ProtoTypeArray,std::allocator<CSkillMgr::ProtoTypeArray>>(
        CSkillMgr::ProtoTypeArray *_First,
        unsigned int _Count,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        qmemcpy(_First, _Val, sizeof(CSkillMgr::ProtoTypeArray));
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (0043A9E0) --------------------------------------------------------
std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> *result,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  CSkillMgr::ProtoTypeArray *v3; // esi
  unsigned int v4; // ebx
  CSkillMgr::ProtoTypeArray *v5; // edx
  unsigned __int16 m_usSkill_ID; // ax
  unsigned __int16 v7; // cx
  unsigned __int16 v8; // cx
  unsigned __int16 v9; // ax
  CSkillMgr::ProtoTypeArray *v10; // eax
  unsigned __int16 v11; // cx
  unsigned __int16 v12; // si
  CSkillMgr::ProtoTypeArray *v13; // ebx
  CSkillMgr::ProtoTypeArray *v14; // edi
  bool v15; // zf
  CSkillMgr::ProtoTypeArray *v16; // ebx
  unsigned __int16 v17; // cx
  unsigned __int16 v18; // si
  bool v19; // cf
  CSkillMgr::ProtoTypeArray *v20; // ebx
  CSkillMgr::ProtoTypeArray *v21; // edi
  std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> *v22; // eax
  CSkillMgr::ProtoTypeArray *i; // [esp+Ch] [ebp-4614h]
  CSkillMgr::ProtoTypeArray *v24; // [esp+10h] [ebp-4610h]
  _BYTE v25[2560]; // [esp+18h] [ebp-4608h] BYREF
  _BYTE v26[2560]; // [esp+A18h] [ebp-3C08h] BYREF
  _BYTE v27[2560]; // [esp+1418h] [ebp-3208h] BYREF
  _BYTE v28[2560]; // [esp+1E18h] [ebp-2808h] BYREF
  _BYTE v29[2560]; // [esp+2818h] [ebp-1E08h] BYREF
  _BYTE v30[2560]; // [esp+3218h] [ebp-1408h] BYREF
  _BYTE v31[2560]; // [esp+3C18h] [ebp-A08h] BYREF

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
    _First,
    (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v3,
    (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&_Last._Myptr[-1]);
  v4 = (unsigned int)&v3[1];
  v5 = v3;
  for ( i = v3 + 1; _First._Myptr < v5; --v5 )
  {
    m_usSkill_ID = v5[-1].m_ProtoTypes[0].m_usSkill_ID;
    v7 = v5->m_ProtoTypes[0].m_usSkill_ID;
    if ( v7 > m_usSkill_ID )
      break;
    if ( v7 < m_usSkill_ID )
      break;
  }
  if ( v4 < (unsigned int)_Last._Myptr )
  {
    v8 = v5->m_ProtoTypes[0].m_usSkill_ID;
    do
    {
      v9 = *(_WORD *)(v4 + 472);
      if ( v8 > v9 )
        break;
      if ( v8 < v9 )
        break;
      v4 += 2560;
    }
    while ( v4 < (unsigned int)_Last._Myptr );
    i = (CSkillMgr::ProtoTypeArray *)v4;
  }
  v10 = (CSkillMgr::ProtoTypeArray *)v4;
  v24 = v5;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v10 < _Last._Myptr; ++v10 )
      {
        v11 = v5->m_ProtoTypes[0].m_usSkill_ID;
        v12 = v10->m_ProtoTypes[0].m_usSkill_ID;
        if ( v12 <= v11 )
        {
          if ( v12 < v11 )
            break;
          v13 = i++;
          qmemcpy(v30, v13, sizeof(v30));
          qmemcpy(v13, v10, sizeof(CSkillMgr::ProtoTypeArray));
          qmemcpy(v10, v30, sizeof(CSkillMgr::ProtoTypeArray));
        }
      }
      v14 = v24;
      v15 = v24 == _First._Myptr;
      if ( v24 > _First._Myptr )
      {
        v16 = v24 - 1;
        do
        {
          v17 = v16->m_ProtoTypes[0].m_usSkill_ID;
          v18 = v5->m_ProtoTypes[0].m_usSkill_ID;
          if ( v18 <= v17 )
          {
            if ( v18 < v17 )
              break;
            qmemcpy(v25, --v5, sizeof(v25));
            qmemcpy(v5, v16, sizeof(CSkillMgr::ProtoTypeArray));
            qmemcpy(v16, v25, sizeof(CSkillMgr::ProtoTypeArray));
          }
          v14 = v24 - 1;
          --v16;
          v19 = _First._Myptr < &v24[-1];
          --v24;
        }
        while ( v19 );
        v15 = v14 == _First._Myptr;
      }
      if ( v15 )
        break;
      v21 = v14 - 1;
      v24 = v21;
      if ( v10 == _Last._Myptr )
      {
        if ( v21 != --v5 )
        {
          qmemcpy(v27, v21, sizeof(v27));
          qmemcpy(v21, v5, sizeof(CSkillMgr::ProtoTypeArray));
          qmemcpy(v5, v27, sizeof(CSkillMgr::ProtoTypeArray));
        }
        --i;
        qmemcpy(v29, v5, sizeof(v29));
        qmemcpy(v5, i, sizeof(CSkillMgr::ProtoTypeArray));
        qmemcpy(i, v29, sizeof(CSkillMgr::ProtoTypeArray));
      }
      else
      {
        qmemcpy(v31, v10, sizeof(v31));
        qmemcpy(v10++, v21, sizeof(CSkillMgr::ProtoTypeArray));
        qmemcpy(v21, v31, sizeof(CSkillMgr::ProtoTypeArray));
      }
    }
    if ( v10 == _Last._Myptr )
      break;
    if ( i != v10 )
    {
      qmemcpy(v28, v5, sizeof(v28));
      qmemcpy(v5, i, sizeof(CSkillMgr::ProtoTypeArray));
      qmemcpy(i, v28, sizeof(CSkillMgr::ProtoTypeArray));
    }
    ++i;
    v20 = v10;
    qmemcpy(v26, v5, sizeof(v26));
    qmemcpy(v5++, v10++, sizeof(CSkillMgr::ProtoTypeArray));
    qmemcpy(v20, v26, sizeof(CSkillMgr::ProtoTypeArray));
  }
  v22 = result;
  result->second._Myptr = i;
  result->first._Myptr = v5;
  return v22;
}

//----- (0043ACF0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  int v2; // ebx
  CSkillMgr::ProtoTypeArray *i; // esi
  CSkillMgr::ProtoTypeArray v4; // [esp-A00h] [ebp-A10h] BYREF

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    for ( i = &_First._Myptr[v2]; ; --i )
    {
      --v2;
      qmemcpy(&v4, &i[-1], sizeof(v4));
      std::_Adjust_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        _First,
        v2,
        _Last._Myptr - _First._Myptr,
        v4);
      if ( v2 <= 0 )
        break;
    }
  }
}

//----- (0043AD70) --------------------------------------------------------
void __cdecl std::_Pop_heap_0<std::vector<CSkillMgr::ProtoTypeArray>::iterator,CSkillMgr::ProtoTypeArray>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  CSkillMgr::ProtoTypeArray v2; // [esp-A00h] [ebp-1410h] BYREF
  _BYTE v3[2560]; // [esp+10h] [ebp-A00h] BYREF

  qmemcpy(v3, &_Last._Myptr[-1], sizeof(v3));
  qmemcpy(&_Last._Myptr[-1], _First._Myptr, sizeof(_Last._Myptr[-1]));
  qmemcpy(&v2, v3, sizeof(v2));
  std::_Adjust_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
    _First,
    0,
    &_Last._Myptr[-1] - _First._Myptr,
    v2);
}

//----- (0043ADF0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  CSkillMgr::ProtoTypeArray *i; // esi
  unsigned __int16 m_usSkill_ID; // cx
  CSkillMgr::ProtoTypeArray *v4; // eax
  std::vector<CSkillMgr::ProtoTypeArray>::iterator v5; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_usSkill_ID = i->m_ProtoTypes[0].m_usSkill_ID;
      if ( m_usSkill_ID >= _First._Myptr->m_ProtoTypes[0].m_usSkill_ID )
      {
        v4 = i - 1;
        if ( m_usSkill_ID < i[-1].m_ProtoTypes[0].m_usSkill_ID )
        {
          do
            v5._Myptr = v4--;
          while ( m_usSkill_ID < v4->m_ProtoTypes[0].m_usSkill_ID );
          if ( v5._Myptr != i )
            std::_Rotate<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
              v5,
              (std::vector<CSkillMgr::ProtoTypeArray>::iterator)i,
              (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
          _First,
          (std::vector<CSkillMgr::ProtoTypeArray>::iterator)i,
          (std::vector<CSkillMgr::ProtoTypeArray>::iterator)&i[1]);
      }
    }
  }
}

//----- (0043AE90) --------------------------------------------------------
CSkillMgr::ProtoTypeArray *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::_Ufill(
        std::vector<CSkillMgr::ProtoTypeArray> *this,
        CSkillMgr::ProtoTypeArray *_Ptr,
        unsigned int _Count,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  std::_Uninit_fill_n<CSkillMgr::ProtoTypeArray *,unsigned int,CSkillMgr::ProtoTypeArray,std::allocator<CSkillMgr::ProtoTypeArray>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (0043AEC0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last)
{
  CSkillMgr::ProtoTypeArray *i; // esi

  for ( i = _Last._Myptr; i - _First._Myptr > 1; --i )
    std::_Pop_heap_0<std::vector<CSkillMgr::ProtoTypeArray>::iterator,CSkillMgr::ProtoTypeArray>(
      _First,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)i);
}

//----- (0043AF20) --------------------------------------------------------
void __thiscall __noreturn std::vector<CSkillMgr::ProtoTypeArray>::_Xlen(std::vector<CSkillMgr::ProtoTypeArray> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0043AF90) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int>(
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _First,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last,
        int _Ideal)
{
  CSkillMgr::ProtoTypeArray *Myptr; // ebx
  CSkillMgr::ProtoTypeArray *v4; // edi
  int v5; // eax
  CSkillMgr::ProtoTypeArray *v7; // ebp
  std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myptr,
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
        &_Mid,
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myptr,
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int>(
          _Mid.second,
          (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int>(
          (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myptr,
        (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v4);
    std::sort_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myptr,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v4);
  }
}
// 43B07A: conditional instruction was optimized away because eax.4>=21

//----- (0043B0C0) --------------------------------------------------------
void __thiscall std::vector<CSkillMgr::ProtoTypeArray>::_Insert_n(
        std::vector<CSkillMgr::ProtoTypeArray> *this,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Where,
        unsigned int _Count,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  CSkillMgr::ProtoTypeArray *Myfirst; // edi
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // edi
  CSkillMgr::ProtoTypeArray *v12; // eax
  CSkillMgr::ProtoTypeArray *v13; // ecx
  CSkillMgr::ProtoTypeArray *v14; // eax
  char *v15; // esi
  CSkillMgr::ProtoTypeArray *v16; // eax
  CSkillMgr::ProtoTypeArray *v17; // ecx
  CSkillMgr::ProtoTypeArray *Mylast; // ecx
  CSkillMgr::ProtoTypeArray *v20; // esi
  CSkillMgr::ProtoTypeArray *v21; // [esp-Ch] [ebp-A34h]
  unsigned int v22; // [esp-8h] [ebp-A30h]
  int v23; // [esp+0h] [ebp-A28h] BYREF
  CSkillMgr::ProtoTypeArray *_Ptr; // [esp+Ch] [ebp-A1Ch]
  CSkillMgr::ProtoTypeArray *_Newvec; // [esp+10h] [ebp-A18h]
  CSkillMgr::ProtoTypeArray _Tmp; // [esp+14h] [ebp-A14h] BYREF
  int *v27; // [esp+A18h] [ebp-10h]
  int v28; // [esp+A24h] [ebp-4h]
  CSkillMgr::ProtoTypeArray *_Wherea; // [esp+A30h] [ebp+8h]
  CSkillMgr::ProtoTypeArray *_Vala; // [esp+A38h] [ebp+10h]

  qmemcpy(&_Tmp, _Val, sizeof(_Tmp));
  Myfirst = this->_Myfirst;
  v27 = &v23;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - Myfirst;
    else
      v7 = 0;
    if ( 1677721 - v7 < _Count )
      std::vector<CSkillMgr::ProtoTypeArray>::_Xlen(this);
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *>(_Where._Myptr, _Wherea, _Vala);
        std::fill<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<CSkillMgr::ProtoTypeArray>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 1677721 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( Myfirst )
        v10 = this->_Mylast - Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<CSkillMgr::ProtoTypeArray>::size(this) + _Count;
      v11 = v9;
      v12 = (CSkillMgr::ProtoTypeArray *)operator new((tagHeader *)(2560 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<CSkillMgr::ProtoTypeArray *,unsigned int,CSkillMgr::ProtoTypeArray,std::allocator<CSkillMgr::ProtoTypeArray>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (CSkillMgr::ProtoTypeArray *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (0043B390) --------------------------------------------------------
std::vector<CSkillMgr::ProtoTypeArray>::iterator *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::insert(
        std::vector<CSkillMgr::ProtoTypeArray> *this,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator *result,
        std::vector<CSkillMgr::ProtoTypeArray>::iterator _Where,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  CSkillMgr::ProtoTypeArray *Myfirst; // esi
  int v6; // esi
  std::vector<CSkillMgr::ProtoTypeArray>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CSkillMgr::ProtoTypeArray>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (0043B400) --------------------------------------------------------
void __thiscall std::vector<CSkillMgr::ProtoTypeArray>::push_back(
        std::vector<CSkillMgr::ProtoTypeArray> *this,
        const CSkillMgr::ProtoTypeArray *_Val)
{
  CSkillMgr::ProtoTypeArray *Myfirst; // edi
  unsigned int v4; // ecx
  CSkillMgr::ProtoTypeArray *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<CSkillMgr::ProtoTypeArray *,unsigned int,CSkillMgr::ProtoTypeArray,std::allocator<CSkillMgr::ProtoTypeArray>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CSkillMgr::ProtoTypeArray>::insert(
      this,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator *)&_Val,
      (std::vector<CSkillMgr::ProtoTypeArray>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (0043B490) --------------------------------------------------------
char __thiscall CSkillMgr::LoadSkillsFromFile(CSkillMgr *this, char *szFileName)
{
  int v2; // ebx
  CSkillMgr::ProtoTypeArray *v3; // esi
  int v4; // edi
  CSkillMgr::ProtoTypeArray *v5; // esi
  const char *v6; // eax
  CSkillMgr::ProtoTypeArray *Myfirst; // ebp
  CSkillMgr::ProtoTypeArray *v9; // edi
  int v10; // ecx
  int v11; // eax
  unsigned __int16 v12; // ax
  CSkillMgr *v13; // ecx
  unsigned __int8 v14; // al
  unsigned __int8 v15; // al
  unsigned __int8 v16; // al
  unsigned __int8 v17; // al
  unsigned __int8 v18; // al
  int v19; // edi
  CSkillMgr::ProtoTypeArray *Mylast; // esi
  int v21; // edi
  CSkillMgr::ProtoTypeArray *v22; // eax
  CSkillMgr::ProtoTypeArray *v23; // ebx
  unsigned int v24; // esi
  CSkillMgr::ProtoTypeArray *v25; // ebp
  char *v26; // edi
  CSkillMgr::ProtoTypeArray *v27; // edx
  CSkillMgr::ProtoTypeArray *v28; // ebp
  CSkillMgr::ProtoTypeArray *v29; // eax
  int v30; // edx
  CSkillMgr::ProtoTypeArray *v31; // edi
  CSkillMgr::ProtoTypeArray *v32; // esi
  CSkillMgr *v33; // esi
  char iNumber; // [esp+Fh] [ebp-8A95h] BYREF
  std::vector<CSkillMgr::ProtoTypeArray> v35; // [esp+10h] [ebp-8A94h] BYREF
  CSkillMgr *v36; // [esp+20h] [ebp-8A84h]
  CSkillMgr::ProtoTypeArray *v37; // [esp+24h] [ebp-8A80h]
  char szString[32]; // [esp+28h] [ebp-8A7Ch] BYREF
  CSkillMgr::ProtoTypeArray v39; // [esp+48h] [ebp-8A5Ch] BYREF
  CDelimitedFile DelimitedFile; // [esp+A48h] [ebp-805Ch] BYREF
  int v41; // [esp+8AA0h] [ebp-4h]

  v36 = this;
  v2 = 0;
  v37 = 0;
  CDelimitedFile::CDelimitedFile(&DelimitedFile, "\t");
  v41 = 0;
  v3 = &v39;
  v4 = 5;
  do
  {
    Skill::ProtoType::ProtoType(v3->m_ProtoTypes);
    v3 = (CSkillMgr::ProtoTypeArray *)((char *)v3 + 512);
    --v4;
  }
  while ( v4 );
  v5 = (CSkillMgr::ProtoTypeArray *)szFileName;
  v6 = szFileName;
  if ( !szFileName )
    v6 = CSkillMgr::ms_szSkillScriptFileName;
  if ( !CDelimitedFile::Open(&DelimitedFile, v6, 1, 0) )
  {
    if ( !szFileName )
      v5 = (CSkillMgr::ProtoTypeArray *)CSkillMgr::ms_szSkillScriptFileName;
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSkillMgr::LoadSkillsFromFile", aDWorkRylSource_19, 94, aS_3, v5);
    v41 = -1;
    CDelimitedFile::~CDelimitedFile(&DelimitedFile);
    return 0;
  }
  Myfirst = 0;
  memset(&v35._Myfirst, 0, 12);
  LOBYTE(v41) = 1;
  if ( CDelimitedFile::ReadLine(&DelimitedFile) )
  {
    v9 = v37;
    while ( 1 )
    {
      v9 = (CSkillMgr::ProtoTypeArray *)((char *)v9 + 1);
      v37 = v9;
      if ( !CDelimitedFile::ReadString(&DelimitedFile, "ID", szString, 0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          105,
          (char *)&byte_4DDAEC,
          v9,
          "\"ID\"");
        goto LABEL_66;
      }
      _mbsnbcmp((unsigned __int8 *)szString, "0x", 2u);
      v12 = v11 ? atoi(szString) : Math::Convert::StrToHex16(0, v10, (int)v5, szString);
      v13 = v36;
      v5 = (CSkillMgr::ProtoTypeArray *)(v2 << 9);
      v39.m_ProtoTypes[v2].m_usSkill_ID = v12;
      v14 = CSkillMgr::ReadStringToTypeValue(v13, &DelimitedFile, "Type", Skill::Type::SkillTypes, 7u);
      v39.m_ProtoTypes[v2].m_eSkillType = v14;
      if ( v14 == 7 )
        break;
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "ClassSkill", &iNumber) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          111,
          (char *)&byte_4DDAEC,
          v9,
          "\"ClassSkill\"");
        if ( Myfirst )
          operator delete(Myfirst);
        goto LABEL_77;
      }
      v39.m_ProtoTypes[v2].m_bIsClassSkill = iNumber != 0;
      if ( !CDelimitedFile::ReadString(
              &DelimitedFile,
              "Name[5]",
              &v39.m_ProtoTypes[0].m_SpriteInfo.m_szName[(_DWORD)v5],
              0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          112,
          (char *)&byte_4DDAEC,
          v9,
          "\"Name[5]\"");
        if ( Myfirst )
          operator delete(Myfirst);
        goto LABEL_77;
      }
      v15 = CSkillMgr::ReadStringToTypeValue(v36, &DelimitedFile, "Limit", Skill::UseLimit::UseLimits, 0xFu);
      v39.m_ProtoTypes[v2].m_eUseLimit = v15;
      if ( v15 == 15 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          114,
          (char *)&byte_4DDAEC,
          v9,
          "\"Limit\"");
LABEL_110:
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v35);
        goto LABEL_77;
      }
      v16 = CSkillMgr::ReadStringToTypeValue(v36, &DelimitedFile, "Stat[0]", Skill::StatusLimit::StatusLimits, 6u);
      v39.m_ProtoTypes[v2].m_StatusLimitType[0] = v16;
      if ( v16 == 6 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          117,
          (char *)&byte_4DDAEC,
          v9,
          "\"Stat[0]\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "Value[0]", &v39.m_ProtoTypes[0].m_StatusLimitValue[(_DWORD)v5]) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          118,
          (char *)&byte_4DDAEC,
          v9,
          "\"Value[0]\"");
        goto LABEL_110;
      }
      v17 = CSkillMgr::ReadStringToTypeValue(v36, &DelimitedFile, "Stat[1]", Skill::StatusLimit::StatusLimits, 6u);
      v39.m_ProtoTypes[v2].m_StatusLimitType[1] = v17;
      if ( v17 == 6 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          121,
          (char *)&byte_4DDAEC,
          v9,
          "\"Stat[1]\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "Value[1]",
              &v39.m_ProtoTypes[0].m_StatusLimitValue[(_DWORD)v5 + 1]) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          122,
          (char *)&byte_4DDAEC,
          v9,
          "\"Value[1]\"");
        goto LABEL_110;
      }
      v18 = CSkillMgr::ReadStringToTypeValue(v36, &DelimitedFile, "Target", Skill::Target::TargetTypes, 0xCu);
      v39.m_ProtoTypes[v2].m_eTargetType = v18;
      if ( v18 == 12 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          125,
          (char *)&byte_4DDAEC,
          v9,
          "\"Target\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "Range",
              (float *)((char *)&v39.m_ProtoTypes[0].m_EffectDistance + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          127,
          (char *)&byte_4DDAEC,
          v9,
          "\"Range\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "Region",
              (float *)((char *)&v39.m_ProtoTypes[0].m_EffectExtent + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          128,
          (char *)&byte_4DDAEC,
          v9,
          "\"Region\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "StartMP",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_StartMP + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          129,
          (char *)&byte_4DDAEC,
          v9,
          "\"StartMP\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "LevelMP",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_LevelMP + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          130,
          (char *)&byte_4DDAEC,
          v9,
          "\"LevelMP\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "Starttick",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_StartTick + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          131,
          (char *)&byte_4DDAEC,
          v9,
          "\"Starttick\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "LevelTick",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_LevelTick + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          132,
          (char *)&byte_4DDAEC,
          v9,
          "\"LevelTick\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadString(
              &DelimitedFile,
              "CastingFlag",
              &v39.m_ProtoTypes[0].m_szCastingFileName[(_DWORD)v5],
              0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          133,
          (char *)&byte_4DDAEC,
          v9,
          "\"CastingFlag\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadString(&DelimitedFile, "EffectFlag", (char *)&v39 + (_DWORD)v5, 0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          134,
          (char *)&byte_4DDAEC,
          v9,
          "\"EffectFlag\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadString(
              &DelimitedFile,
              "HitFlag",
              &v39.m_ProtoTypes[0].m_szHitFileName[(_DWORD)v5],
              0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          135,
          (char *)&byte_4DDAEC,
          v9,
          "\"HitFlag\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "CoolDownTime",
              (unsigned int *)((char *)&v39.m_ProtoTypes[0].m_dwCoolDownTime + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          136,
          (char *)&byte_4DDAEC,
          v9,
          "\"CoolDownTime\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "EndCoolDown",
              (char *)&v39.m_ProtoTypes[0].m_cEndCoolDown + (_DWORD)v5) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          137,
          (char *)&byte_4DDAEC,
          v9,
          "\"EndCoolDown\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadString(
              &DelimitedFile,
              "dds",
              &v39.m_ProtoTypes[0].m_SpriteInfo.m_szSpriteName[(_DWORD)v5],
              0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          138,
          (char *)&byte_4DDAEC,
          v9,
          "\"dds\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "MinX",
              (unsigned __int16 *)&v39.m_ProtoTypes[0].m_SpriteInfo.m_szSpriteName[(_DWORD)v5 + 32]) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          139,
          (char *)&byte_4DDAEC,
          v9,
          "\"MinX\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "MinY",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_SpriteInfo.m_nSpriteMinY + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          140,
          (char *)&byte_4DDAEC,
          v9,
          "\"MinY\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "MaxX",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_SpriteInfo.m_nSpriteMaxX + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          141,
          (char *)&byte_4DDAEC,
          v9,
          "\"MaxX\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "MaxY",
              (unsigned __int16 *)((char *)&v39.m_ProtoTypes[0].m_SpriteInfo.m_nSpriteMaxY + (_DWORD)v5)) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          142,
          (char *)&byte_4DDAEC,
          v9,
          "\"MaxY\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "Hit", (char *)&v39.m_ProtoTypes[0].m_cStrikeNum + (_DWORD)v5) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          143,
          (char *)&byte_4DDAEC,
          v9,
          "\"Hit\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(
              &DelimitedFile,
              "EndScript",
              (char *)&v39.m_ProtoTypes[0].m_cEndScript + (_DWORD)v5) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          144,
          (char *)&byte_4DDAEC,
          v9,
          "\"EndScript\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "Protection", &iNumber) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          145,
          (char *)&byte_4DDAEC,
          v9,
          "\"Protection\"");
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_bProtection = iNumber != 0;
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "Interrupt", &iNumber) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          146,
          (char *)&byte_4DDAEC,
          v9,
          "\"Interrupt\"");
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_bInterrupt = iNumber != 0;
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "Gauge", &iNumber) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          147,
          (char *)&byte_4DDAEC,
          v9,
          "\"Gauge\"");
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_bGauge = iNumber != 0;
      if ( !CDelimitedFile::ReadData(&DelimitedFile, "DRC", (char *)&v39.m_ProtoTypes[0].m_cDRC + (_DWORD)v5) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          148,
          (char *)&byte_4DDAEC,
          v9,
          "\"DRC\"");
        goto LABEL_110;
      }
      if ( !CDelimitedFile::ReadString(&DelimitedFile, "Parent", szString, 0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          150,
          (char *)&byte_4DDAEC,
          v9,
          "\"Parent\"");
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_usParentSkill = Math::Convert::Atos(szString);
      if ( !CDelimitedFile::ReadString(&DelimitedFile, "Child", szString, 0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          152,
          (char *)&byte_4DDAEC,
          v9,
          "\"Child\"");
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_usChildSkill = Math::Convert::Atos(szString);
      if ( !CDelimitedFile::ReadString(
              &DelimitedFile,
              "Text",
              &v39.m_ProtoTypes[0].m_szSkillDescribe[(_DWORD)v5],
              0x100u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          155,
          (char *)&byte_4DDAEC,
          v9,
          "\"Text\"");
        goto LABEL_110;
      }
      if ( v39.m_ProtoTypes[0].m_usSkill_ID != v39.m_ProtoTypes[v2].m_usSkill_ID )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMgr::LoadSkillsFromFile",
          aDWorkRylSource_19,
          161,
          (char *)&byte_4DD8D8,
          v39.m_ProtoTypes[v2].m_usSkill_ID,
          v2);
        goto LABEL_110;
      }
      v39.m_ProtoTypes[v2].m_cSpell_LockCount = v2;
      if ( ++v2 == 5 || v39.m_ProtoTypes[0].m_bIsClassSkill )
      {
        std::vector<CSkillMgr::ProtoTypeArray>::push_back(&v35, &v39);
        if ( v2 > 0 )
        {
          v5 = &v39;
          v19 = v2;
          do
          {
            Skill::ProtoType::Initialize(v5->m_ProtoTypes);
            v5 = (CSkillMgr::ProtoTypeArray *)((char *)v5 + 512);
            --v19;
          }
          while ( v19 );
          v9 = v37;
        }
        Myfirst = v35._Myfirst;
        v2 = 0;
      }
      if ( !CDelimitedFile::ReadLine(&DelimitedFile) )
        goto LABEL_58;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMgr::LoadSkillsFromFile",
      aDWorkRylSource_19,
      109,
      (char *)&byte_4DDAEC,
      v9,
      "\"Type\"");
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_77;
  }
LABEL_58:
  Mylast = v35._Mylast;
  v21 = v35._Mylast - Myfirst;
  std::_Sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int>(
    (std::vector<CSkillMgr::ProtoTypeArray>::iterator)Myfirst,
    (std::vector<CSkillMgr::ProtoTypeArray>::iterator)v35._Mylast,
    v21);
  v22 = Myfirst;
  if ( Myfirst != &Mylast[-1] )
  {
    while ( v22->m_ProtoTypes[0].m_usSkill_ID != v22[1].m_ProtoTypes[0].m_usSkill_ID )
    {
      if ( ++v22 == &Mylast[-1] )
        goto LABEL_61;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMgr::LoadSkillsFromFile",
      aDWorkRylSource_19,
      188,
      (char *)&byte_4DD8A8,
      v22->m_ProtoTypes[0].m_usSkill_ID);
LABEL_66:
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_77;
  }
LABEL_61:
  v23 = v35._Myfirst;
  v24 = v35._Myfirst != 0 ? v21 : 0;
  v36->m_nSkillNum = v24;
  v25 = (CSkillMgr::ProtoTypeArray *)operator new[](2560 * v24);
  v37 = v25;
  LOBYTE(v41) = 2;
  if ( v25 )
  {
    v26 = (char *)v25;
    if ( (int)(v24 - 1) >= 0 )
    {
      do
      {
        `vector constructor iterator'(v26, 0x200u, 5, (void *(__thiscall *)(void *))Skill::ProtoType::ProtoType);
        v26 += 2560;
        --v24;
      }
      while ( v24 );
    }
    v27 = v25;
  }
  else
  {
    v27 = 0;
  }
  LOBYTE(v41) = 1;
  v36->m_ProtoTypeArray = v27;
  if ( !v27 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMgr::LoadSkillsFromFile",
      aDWorkRylSource_19,
      197,
      (char *)&byte_4DD88C);
    goto LABEL_122;
  }
  v28 = v35._Mylast;
  v29 = v23;
  if ( v23 != v35._Mylast )
  {
    v30 = (char *)v27 - (char *)v23;
    do
    {
      v31 = (CSkillMgr::ProtoTypeArray *)((char *)v29 + v30);
      v32 = v29++;
      qmemcpy(v31, v32, sizeof(CSkillMgr::ProtoTypeArray));
    }
    while ( v29 != v28 );
  }
  v33 = v36;
  if ( !CSkillMgr::CheckParentChildRule(v36) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSkillMgr::LoadSkillsFromFile", aDWorkRylSource_19, 205, byte_4DD854);
    goto LABEL_122;
  }
  if ( CCrc32Static::BufferCrc32((const char *)v33->m_ProtoTypeArray, 2560 * v33->m_nSkillNum, &v33->m_dwCRC32) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMgr::LoadSkillsFromFile",
      aDWorkRylSource_19,
      213,
      (char *)&byte_4DD828);
LABEL_122:
    if ( v23 )
      operator delete(v23);
LABEL_77:
    v41 = -1;
    CDelimitedFile::~CDelimitedFile(&DelimitedFile);
    return 0;
  }
  if ( v23 )
    operator delete(v23);
  v41 = -1;
  CDelimitedFile::~CDelimitedFile(&DelimitedFile);
  return 1;
}
// 43B5DB: variable 'v11' is possibly undefined
// 43B5E2: variable 'v10' is possibly undefined

//----- (0043C150) --------------------------------------------------------
unsigned int __thiscall CServerSetup::GetServerID(CServerSetup *this)
{
  return this->m_ServerID;
}

//----- (0043C160) --------------------------------------------------------
char __thiscall CServerSetup::GetServerGroup(CServerSetup *this)
{
  return BYTE1(this->m_ServerID);
}

//----- (0043C170) --------------------------------------------------------
unsigned int __thiscall CServerSetup::GetServerZone(CServerSetup *this)
{
  return HIBYTE(this->m_ServerID);
}

//----- (0043C180) --------------------------------------------------------
unsigned int __thiscall CServerSetup::GetServerChannel(CServerSetup *this)
{
  return HIWORD(this->m_ServerID);
}

//----- (0043C190) --------------------------------------------------------
char __thiscall CServerSetup::InitLoginServer(CServerSetup *this)
{
  char PingCheck[264]; // [esp+10h] [ebp-428h] BYREF
  char Alone[264]; // [esp+118h] [ebp-320h] BYREF
  char ServerID[264]; // [esp+220h] [ebp-218h] BYREF
  char LimitVer[268]; // [esp+328h] [ebp-110h] BYREF

  memset(ServerID, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Login", "ServerID", ServerID, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitLoginServer", aDWorkRylSource_11, 105, byte_4DDDB4);
    return 0;
  }
  this->m_ServerID = atoi(ServerID);
  memset(PingCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Login", "Ping", PingCheck, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitLoginServer", aDWorkRylSource_11, 114, byte_4DDD9C);
    return 0;
  }
  this->m_bPingCheck = atoi(PingCheck) == 1;
  memset(Alone, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Login", "Alone", Alone, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitLoginServer", aDWorkRylSource_11, 122, byte_4DDD84);
    return 0;
  }
  this->m_bAlone = atoi(Alone) == 1;
  memset(LimitVer, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Login", "LimitVer", LimitVer, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitLoginServer", aDWorkRylSource_11, 130, byte_4DDD5C);
    return 0;
  }
  this->m_dwClientVer = atoi(LimitVer);
  if ( !Registry::ReadString("DemonSetup.ini", "Login", "PatchAddr", this->m_chPatchAddr, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitLoginServer",
      aDWorkRylSource_11,
      137,
      (char *)&byte_4DDD38);
    return 0;
  }
  return 1;
}

//----- (0043C3A0) --------------------------------------------------------
char __thiscall CServerSetup::InitAuthServer(CServerSetup *this)
{
  char ServerType[264]; // [esp+10h] [ebp-848h] BYREF
  char szAgentAddr[264]; // [esp+118h] [ebp-740h] BYREF
  char PingCheck[264]; // [esp+220h] [ebp-638h] BYREF
  char szIsBattleServer[264]; // [esp+328h] [ebp-530h] BYREF
  char ServerID[264]; // [esp+430h] [ebp-428h] BYREF
  char szBattleAgeLimit[264]; // [esp+538h] [ebp-320h] BYREF
  char HanCheck[264]; // [esp+640h] [ebp-218h] BYREF
  char szSupressCharCreate[268]; // [esp+748h] [ebp-110h] BYREF

  memset(ServerID, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "ServerID", ServerID, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 149, byte_4DDEEC);
    return 0;
  }
  this->m_ServerID = atoi(ServerID);
  memset(PingCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "Ping", PingCheck, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 158, byte_4DDD9C);
    return 0;
  }
  this->m_bPingCheck = atoi(PingCheck) == 1;
  memset(HanCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "HanCheck", HanCheck, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 166, byte_4DDED0);
    return 0;
  }
  this->m_bHanCheck = atoi(HanCheck) == 1;
  memset(ServerType, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "ServerType", ServerType, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 174, byte_4DDEB0);
    return 0;
  }
  this->m_dwServerType = atoi(ServerType);
  memset(szAgentAddr, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "AgentAddr", szAgentAddr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 182, byte_4DDE88);
    return 0;
  }
  INET_Addr::set_addr(&this->m_ServerAddress[3], szAgentAddr, 0x277Fu);
  memset(szIsBattleServer, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "IsAuthBattleServer", szIsBattleServer, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAuthServer", aDWorkRylSource_11, 190, byte_4DDE4C);
    return 0;
  }
  this->m_bBattleAuth = atoi(szIsBattleServer) != 0;
  memset(szBattleAgeLimit, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Auth", "BattleAgeLimit", szBattleAgeLimit, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitAuthServer",
      aDWorkRylSource_11,
      198,
      (char *)&byte_4DDE1C);
    return 0;
  }
  this->m_nBattleAuthAgeLimit = atoi(szBattleAgeLimit);
  memset(szSupressCharCreate, 0, 260);
  Registry::ReadString("DemonSetup.ini", "Auth", "SupressCharCreate", szSupressCharCreate, 0x104u);
  this->m_bSupressCharCreate = atoi(szSupressCharCreate) != 0;
  return 1;
}

//----- (0043C720) --------------------------------------------------------
char __thiscall CServerSetup::InitAgentServer(CServerSetup *this)
{
  char szSrc[257]; // [esp+10h] [ebp-1088h] BYREF
  __int16 v4; // [esp+111h] [ebp-F87h]
  char v5; // [esp+113h] [ebp-F85h]
  char addr[257]; // [esp+118h] [ebp-F80h] BYREF
  __int16 v7; // [esp+219h] [ebp-E7Fh]
  char v8; // [esp+21Bh] [ebp-E7Dh]
  char nptr[257]; // [esp+220h] [ebp-E78h] BYREF
  __int16 v10; // [esp+321h] [ebp-D77h]
  char v11; // [esp+323h] [ebp-D75h]
  char v12[257]; // [esp+328h] [ebp-D70h] BYREF
  __int16 v13; // [esp+429h] [ebp-C6Fh]
  char v14; // [esp+42Bh] [ebp-C6Dh]
  char v15[257]; // [esp+430h] [ebp-C68h] BYREF
  __int16 v16; // [esp+531h] [ebp-B67h]
  char v17; // [esp+533h] [ebp-B65h]
  char v18[257]; // [esp+538h] [ebp-B60h] BYREF
  __int16 v19; // [esp+639h] [ebp-A5Fh]
  char v20; // [esp+63Bh] [ebp-A5Dh]
  char v21[257]; // [esp+640h] [ebp-A58h] BYREF
  __int16 v22; // [esp+741h] [ebp-957h]
  char v23; // [esp+743h] [ebp-955h]
  char v24[257]; // [esp+748h] [ebp-950h] BYREF
  __int16 v25; // [esp+849h] [ebp-84Fh]
  char v26; // [esp+84Bh] [ebp-84Dh]
  char Buffer_Out[257]; // [esp+850h] [ebp-848h] BYREF
  __int16 v28; // [esp+951h] [ebp-747h]
  char v29; // [esp+953h] [ebp-745h]
  char v30[257]; // [esp+958h] [ebp-740h] BYREF
  __int16 v31; // [esp+A59h] [ebp-63Fh]
  char v32; // [esp+A5Bh] [ebp-63Dh]
  char v33[257]; // [esp+A60h] [ebp-638h] BYREF
  __int16 v34; // [esp+B61h] [ebp-537h]
  char v35; // [esp+B63h] [ebp-535h]
  char v36[257]; // [esp+B68h] [ebp-530h] BYREF
  __int16 v37; // [esp+C69h] [ebp-42Fh]
  char v38; // [esp+C6Bh] [ebp-42Dh]
  char v39[257]; // [esp+C70h] [ebp-428h] BYREF
  __int16 v40; // [esp+D71h] [ebp-327h]
  char v41; // [esp+D73h] [ebp-325h]
  char v42[264]; // [esp+D78h] [ebp-320h] BYREF
  char v43[264]; // [esp+E80h] [ebp-218h] BYREF
  char v44[268]; // [esp+F88h] [ebp-110h] BYREF

  memset(Buffer_Out, 0, sizeof(Buffer_Out));
  v28 = 0;
  v29 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "ServerID", Buffer_Out, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 222, byte_4DE1C0);
    return 0;
  }
  this->m_ServerID = atoi(Buffer_Out);
  memset(nptr, 0, sizeof(nptr));
  v10 = 0;
  v11 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "ServerType", nptr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 230, byte_4DE1A4);
    return 0;
  }
  this->m_dwServerType = atoi(nptr);
  memset(v36, 0, sizeof(v36));
  v37 = 0;
  v38 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "Ping", v36, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 238, byte_4DDD9C);
    return 0;
  }
  this->m_bPingCheck = atoi(v36) == 1;
  memset(v15, 0, sizeof(v15));
  v16 = 0;
  v17 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "AdminIPCheck", v15, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 246, byte_4DE180);
    return 0;
  }
  this->m_bAdminIPCheck = atoi(v15) == 1;
  memset(v33, 0, sizeof(v33));
  v34 = 0;
  v35 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "Ver", v33, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 254, byte_4DDD5C);
    return 0;
  }
  this->m_dwClientVer = atoi(v33);
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "PatchAddr", this->m_chPatchAddr, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitAgentServer",
      aDWorkRylSource_11,
      261,
      (char *)&byte_4DDD38);
    return 0;
  }
  memset(v21, 0, sizeof(v21));
  v22 = 0;
  v23 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "LimitNum", v21, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 268, byte_4DE160);
    return 0;
  }
  this->m_dwUserLimit = atoi(v21);
  memset(v39, 0, sizeof(v39));
  v40 = 0;
  v41 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "BattleLimit", v39, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 276, byte_4DE130);
    return 0;
  }
  this->m_wBattleLimit = atoi(v39);
  memset(szSrc, 0, sizeof(szSrc));
  v4 = 0;
  v5 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "CheckSum", szSrc, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 284, byte_4DE110);
    return 0;
  }
  this->m_dwCheckSum = Math::Convert::Atoi(szSrc);
  memset(addr, 0, sizeof(addr));
  v7 = 0;
  v8 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "LoginAddr", addr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 292, byte_4DE0E8);
    return 0;
  }
  INET_Addr::set_addr(this->m_ServerAddress, addr, 0x2776u);
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "UIDAddr", v42, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 300, aUid);
    return 0;
  }
  INET_Addr::set_addr(&this->m_ServerAddress[4], v42, 0x277Cu);
  memset(v12, 0, sizeof(v12));
  v13 = 0;
  v14 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "StartSiegeTime", v12, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 308, byte_4DE09C);
    return 0;
  }
  this->m_cStartSiegeTime = Math::Convert::Atoi(v12);
  memset(v18, 0, sizeof(v18));
  v19 = 0;
  v20 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "EndSiegeTime", v18, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 316, byte_4DE074);
    return 0;
  }
  this->m_cEndSiegeTime = Math::Convert::Atoi(v18);
  memset(v24, 0, sizeof(v24));
  v25 = 0;
  v26 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "IsAgentBattleServer", v24, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 324, byte_4DE038);
    return 0;
  }
  this->m_bBattleAgent = atoi(v24) != 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "AgentServerType", v43, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 332, byte_4DE00C);
    return 0;
  }
  this->m_eAgentServerType = atoi(v43);
  memset(v30, 0, sizeof(v30));
  v31 = 0;
  v32 = 0;
  if ( !Registry::ReadString("DemonSetup.ini", "Agent", "AgentNationType", v30, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 340, byte_4DDFDC);
    return 0;
  }
  this->m_eNationType = atoi(v30);
  if ( this->m_eAgentServerType == Part2Unified )
  {
    if ( !Registry::ReadString("DemonSetup.ini", "Agent", "Part1UnifiedAgentAddr", v44, 0x104u) )
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitAgentServer", aDWorkRylSource_11, 351, byte_4DDFA0);
      return 0;
    }
    INET_Addr::set_addr(&this->m_Part1UnifiedAgentAddr, v44, 0x277Bu);
    Registry::ReadString(
      "DemonSetup.ini",
      "Agent",
      "Part1UnifiedDBAddr",
      this->m_stPart1UnifiedDBInfo.m_szDBAddr,
      0x100u);
    Registry::ReadString(
      "DemonSetup.ini",
      "Agent",
      "Part1UnifiedDBName",
      this->m_stPart1UnifiedDBInfo.m_szDBName,
      0x100u);
    Registry::ReadString(
      "DemonSetup.ini",
      "Agent",
      "Part1UnifiedDBAccount",
      this->m_stPart1UnifiedDBInfo.m_szDBAccount,
      0x100u);
    if ( !Registry::ReadString(
            "DemonSetup.ini",
            "Agent",
            "Part1UnifiedDBPass",
            this->m_stPart1UnifiedDBInfo.m_szDBPass,
            0x100u) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CServerSetup::InitAgentServer",
        aDWorkRylSource_11,
        365,
        (char *)&byte_4DDF30);
      return 0;
    }
  }
  return 1;
}

//----- (0043CE60) --------------------------------------------------------
char __thiscall CServerSetup::InitUIDServer(CServerSetup *this)
{
  unsigned int v2; // eax
  u_short Int; // ax
  char IgnoreFlag[264]; // [esp+10h] [ebp-428h] BYREF
  char ServerType[264]; // [esp+118h] [ebp-320h] BYREF
  char FreeCheck[264]; // [esp+220h] [ebp-218h] BYREF
  char HanUnitedBillingAddr[268]; // [esp+328h] [ebp-110h] BYREF

  memset(FreeCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Keeper", "FreeCheck", FreeCheck, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitUIDServer", aDWorkRylSource_11, 378, byte_4DE274);
    return 0;
  }
  this->m_bFreeCheck = atoi(FreeCheck) == 1;
  memset(IgnoreFlag, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Keeper", "IgnoreFlag", IgnoreFlag, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitUIDServer", aDWorkRylSource_11, 386, byte_4DE250);
    return 0;
  }
  this->m_bIgnoreFlag = atoi(IgnoreFlag) == 1;
  memset(ServerType, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Keeper", "ServerType", ServerType, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitUIDServer", aDWorkRylSource_11, 394, byte_4DDEB0);
    return 0;
  }
  v2 = atoi(ServerType);
  this->m_dwServerType = v2;
  if ( v2 == 3 )
  {
    memset(HanUnitedBillingAddr, 0, 260);
    if ( !Registry::ReadString("DemonSetup.ini", "Keeper", "HanUnitedBillingAddr", HanUnitedBillingAddr, 0x104u) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CServerSetup::InitUIDServer",
        aDWorkRylSource_11,
        404,
        (char *)&byte_4DE218);
      return 0;
    }
    Int = Registry::ReadInt("DemonSetup.ini", "Keeper", "HanUnitedBillingPort");
    INET_Addr::set_addr(&this->m_HanUnitedBillingAddr, HanUnitedBillingAddr, Int);
  }
  return 1;
}

//----- (0043D070) --------------------------------------------------------
char __thiscall CServerSetup::InitChatServer(CServerSetup *this)
{
  char szAgentServer[264]; // [esp+8h] [ebp-320h] BYREF
  char ChatServerID[264]; // [esp+110h] [ebp-218h] BYREF
  char ChatPingCheck[268]; // [esp+218h] [ebp-110h] BYREF

  memset(ChatServerID, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "ChatServer", "ServerID", ChatServerID, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitChatServer", aDWorkRylSource_11, 422, byte_4DE310);
    return 0;
  }
  this->m_ServerID = atoi(ChatServerID);
  memset(szAgentServer, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "ChatServer", "AgentAddress", szAgentServer, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitChatServer", aDWorkRylSource_11, 431, byte_4DDE88);
    return 0;
  }
  INET_Addr::set_addr(&this->m_ServerAddress[3], szAgentServer, 0x2784u);
  memset(ChatPingCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "ChatServer", "Ping", ChatPingCheck, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitChatServer", aDWorkRylSource_11, 439, byte_4DDD9C);
    return 0;
  }
  this->m_bPingCheck = atoi(ChatPingCheck) == 1;
  Registry::ReadString("DemonSetup.ini", "ChatServer", "AdminDBAddr", this->m_stAdminToolDBInfo.m_szDBAddr, 0x100u);
  Registry::ReadString("DemonSetup.ini", "ChatServer", "AdminDBName", this->m_stAdminToolDBInfo.m_szDBName, 0x100u);
  Registry::ReadString(
    "DemonSetup.ini",
    "ChatServer",
    "AdminDBAccount",
    this->m_stAdminToolDBInfo.m_szDBAccount,
    0x100u);
  if ( !Registry::ReadString(
          "DemonSetup.ini",
          "ChatServer",
          "AdminDBPass",
          this->m_stAdminToolDBInfo.m_szDBPass,
          0x100u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitChatServer",
      aDWorkRylSource_11,
      454,
      (char *)&byte_4DE2B8);
    return 0;
  }
  return 1;
}

//----- (0043D290) --------------------------------------------------------
const char *__cdecl CServerSetup::GetManageClientWindowName()
{
  return "ManageClient";
}

//----- (0043D2A0) --------------------------------------------------------
int __cdecl CServerSetup::GetGameServerTCPPort(signed int dwServerID)
{
  return (dwServerID >> 24) + 20 * (SBYTE2(dwServerID) + 510);
}

//----- (0043D2C0) --------------------------------------------------------
int __cdecl CServerSetup::GetGameServerUDPPort(signed int dwServerID)
{
  return (dwServerID >> 24) + 20 * (SBYTE2(dwServerID) + 520);
}

//----- (0043D2E0) --------------------------------------------------------
char __cdecl CServerSetup::GetZoneFromCmdLine()
{
  const char *CommandLineA; // eax
  const char *v2; // eax
  const char *v3; // eax
  char szCommandLineBuffer[260]; // [esp+4h] [ebp-108h] BYREF

  CommandLineA = GetCommandLineA();
  if ( _snprintf(szCommandLineBuffer, 0x103u, "%s", CommandLineA) < 0 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::GetZoneFromCmdLine",
      aDWorkRylSource_11,
      674,
      "CommandLine Parse failed - Zone");
    return 1;
  }
  v2 = strtok(szCommandLineBuffer, " \r\n\t");
  if ( !v2 )
    return 1;
  while ( 1 )
  {
    if ( !strcmp(v2, "-z") )
    {
      v3 = strtok(0, " \r\n\t");
      if ( v3 )
        break;
    }
    v2 = strtok(0, " \r\n\t");
    if ( !v2 )
      return 1;
  }
  return atoi(v3);
}

//----- (0043D3F0) --------------------------------------------------------
char __cdecl CServerSetup::GetChannelFromCmdLine()
{
  const char *CommandLineA; // eax
  const char *v2; // eax
  const char *v3; // eax
  char szCommandLineBuffer[260]; // [esp+4h] [ebp-108h] BYREF

  CommandLineA = GetCommandLineA();
  if ( _snprintf(szCommandLineBuffer, 0x103u, "%s", CommandLineA) < 0 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::GetChannelFromCmdLine",
      aDWorkRylSource_11,
      710,
      "CommandLine Parse failed - Channel");
    return 0;
  }
  v2 = strtok(szCommandLineBuffer, " \r\n\t");
  if ( !v2 )
    return 0;
  while ( 1 )
  {
    if ( !strcmp(v2, "-c") )
    {
      v3 = strtok(0, " \r\n\t");
      if ( v3 )
        break;
    }
    v2 = strtok(0, " \r\n\t");
    if ( !v2 )
      return 0;
  }
  return atoi(v3);
}

//----- (0043D500) --------------------------------------------------------
void __thiscall CServerSetup::~CServerSetup(CServerSetup *this)
{
  this->__vftable = (CServerSetup_vtbl *)&CServerSetup::`vftable';
  if ( this->m_vecAdminUID._Myfirst )
    operator delete(this->m_vecAdminUID._Myfirst);
  this->m_vecAdminUID._Myfirst = 0;
  this->m_vecAdminUID._Mylast = 0;
  this->m_vecAdminUID._Myend = 0;
  `eh vector destructor iterator'(
    (char *)this->m_ServerAddress,
    0x14u,
    8,
    (void (__thiscall *)(void *))CSymbolTable::Create);
}
// 4DE3E0: using guessed type void *CServerSetup::`vftable';

//----- (0043D560) --------------------------------------------------------
CServerSetup *__thiscall CServerSetup::`scalar deleting destructor'(CServerSetup *this, char a2)
{
  CServerSetup::~CServerSetup(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0043D580) --------------------------------------------------------
void __thiscall CServerSetup::CServerSetup(CServerSetup *this)
{
  this->__vftable = (CServerSetup_vtbl *)&CServerSetup::`vftable';
  this->m_dwLimitVer = 0;
  this->m_dwClientVer = 0;
  this->m_dwServerType = 0;
  this->m_eNationType = KOREA;
  this->m_bHanCheck = 0;
  this->m_bHackCheck = 0;
  this->m_bAdminIPCheck = 0;
  this->m_bDuelModeCheck = 0;
  this->m_bLotteryEvent = 0;
  this->m_bLevelUpEvent = 0;
  this->m_bBattleAuth = 0;
  this->m_bBattleGame = 0;
  this->m_bBattleAgent = 0;
  this->m_bSupressCharCreate = 0;
  this->m_dwUserLimit = 0;
  this->m_dwCheckSum = 0;
  this->m_dwExp = 0;
  this->m_dwDrop = 0;
  this->m_dwMinFame = 0;
  this->m_dwMaxFame = 0;
  this->m_wBattleLimit = 0;
  `eh vector constructor iterator'(
    (char *)this->m_ServerAddress,
    0x14u,
    8,
    (void (__thiscall *)(void *))INET_Addr::INET_Addr,
    (void (__thiscall *)(void *))CSymbolTable::Create);
  this->m_GameServerUDPAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_GameServerUDPAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_GameServerUDPAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_GameServerUDPAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_GameServerUDPAddr.m_SockAddr.sa_data[10] = 0;
  this->m_HanUnitedBillingAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_HanUnitedBillingAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_HanUnitedBillingAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_HanUnitedBillingAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_HanUnitedBillingAddr.m_SockAddr.sa_data[10] = 0;
  this->m_vecAdminUID._Myfirst = 0;
  this->m_vecAdminUID._Mylast = 0;
  this->m_vecAdminUID._Myend = 0;
  this->m_nBattleAuthAgeLimit = 15;
  this->m_Part1UnifiedAgentAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_Part1UnifiedAgentAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_Part1UnifiedAgentAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_Part1UnifiedAgentAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_Part1UnifiedAgentAddr.m_SockAddr.sa_data[10] = 0;
  this->m_ServerID = 0;
}
// 4DE3E0: using guessed type void *CServerSetup::`vftable';

//----- (0043D6D0) --------------------------------------------------------
CServerSetup *__cdecl CServerSetup::GetInstance()
{
  if ( (_S1_1 & 1) == 0 )
  {
    _S1_1 |= 1u;
    CServerSetup::CServerSetup(&serverSetup);
    atexit(_E2_4);
  }
  return &serverSetup;
}

//----- (0043D730) --------------------------------------------------------
char __thiscall CServerSetup::LoadAdminUID(CServerSetup *this)
{
  _iobuf *v2; // eax
  _iobuf *v3; // ebx
  std::vector<unsigned long> *p_m_vecAdminUID; // esi
  const char *v6; // eax
  CCellManager::SafetyZoneInfo *v7; // eax
  unsigned int *Myfirst; // edx
  unsigned int v9; // edi
  _DWORD *Mylast; // ecx
  CCellManager::SafetyZoneInfo *_Val; // [esp+8h] [ebp-108h] BYREF
  char szBuffer[256]; // [esp+Ch] [ebp-104h] BYREF

  v2 = fopen("./Script/Server/AdminUID.txt", "r");
  v3 = v2;
  if ( !v2 )
    return 0;
  if ( fgets(szBuffer, 256, v2) )
  {
    p_m_vecAdminUID = &this->m_vecAdminUID;
    do
    {
      v6 = strtok(szBuffer, "\n");
      v7 = (CCellManager::SafetyZoneInfo *)atoi(v6);
      Myfirst = p_m_vecAdminUID->_Myfirst;
      _Val = v7;
      if ( Myfirst )
        v9 = p_m_vecAdminUID->_Mylast - Myfirst;
      else
        v9 = 0;
      if ( Myfirst && v9 < p_m_vecAdminUID->_Myend - Myfirst )
      {
        Mylast = p_m_vecAdminUID->_Mylast;
        *Mylast = v7;
        p_m_vecAdminUID->_Mylast = Mylast + 1;
      }
      else
      {
        std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
          (std::vector<CCellManager::SafetyZoneInfo *> *)p_m_vecAdminUID,
          (std::vector<CCellManager::SafetyZoneInfo *>::iterator)p_m_vecAdminUID->_Mylast,
          1u,
          &_Val);
      }
    }
    while ( fgets(szBuffer, 256, v3) );
  }
  fclose(v3);
  return 1;
}

//----- (0043D830) --------------------------------------------------------
char __thiscall CServerSetup::InitGameServer(CServerSetup *this)
{
  char ZoneFromCmdLine; // al
  int v3; // edi
  u_short GameServerUDPPort; // ax
  int nChannelIndex; // [esp+Ch] [ebp-11BCh]
  int nZoneIndex; // [esp+10h] [ebp-11B8h]
  CTCPFactory tcpFactory; // [esp+14h] [ebp-11B4h] BYREF
  char szSection[264]; // [esp+28h] [ebp-11A0h] BYREF
  char Exp[264]; // [esp+130h] [ebp-1098h] BYREF
  char MaxFame[264]; // [esp+238h] [ebp-F90h] BYREF
  char PingCheck[264]; // [esp+340h] [ebp-E88h] BYREF
  char BattleLimit[264]; // [esp+448h] [ebp-D80h] BYREF
  char LotteryEvent[264]; // [esp+550h] [ebp-C78h] BYREF
  char szAgentServerAddr[264]; // [esp+658h] [ebp-B70h] BYREF
  char ServerID[264]; // [esp+760h] [ebp-A68h] BYREF
  char szLogServerAddr[264]; // [esp+868h] [ebp-960h] BYREF
  char MinFame[264]; // [esp+970h] [ebp-858h] BYREF
  char szChatServerAddr[264]; // [esp+A78h] [ebp-750h] BYREF
  char LevelUpEvent[264]; // [esp+B80h] [ebp-648h] BYREF
  char myAddress[264]; // [esp+C88h] [ebp-540h] BYREF
  char SpeedHack[264]; // [esp+D90h] [ebp-438h] BYREF
  char NationType[264]; // [esp+E98h] [ebp-330h] BYREF
  char Drop[264]; // [esp+FA0h] [ebp-228h] BYREF
  char szIsBattleServer[268]; // [esp+10A8h] [ebp-120h] BYREF
  int v26; // [esp+11C4h] [ebp-4h]

  ZoneFromCmdLine = CServerSetup::GetZoneFromCmdLine();
  v3 = ZoneFromCmdLine;
  nZoneIndex = ZoneFromCmdLine;
  nChannelIndex = CServerSetup::GetChannelFromCmdLine();
  if ( _snprintf(szSection, 0x103u, "Zone_%02d%02d", v3, nChannelIndex) <= 0 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      500,
      aZ02dC02d_1,
      v3,
      nChannelIndex);
    return 0;
  }
  memset(ServerID, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "ServerID", ServerID, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      507,
      aZ02dC02d_7,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_ServerID = atoi(ServerID);
  memset(PingCheck, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "Ping", PingCheck, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      515,
      aZ02dC02d_8,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_bPingCheck = atoi(PingCheck) == 1;
  memset(SpeedHack, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "SpeedHack", SpeedHack, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      523,
      aZ02dC02d_0,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_bHackCheck = atoi(SpeedHack) == 1;
  memset(LotteryEvent, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "LotteryEvent", LotteryEvent, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      531,
      aZ02dC02d_9,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_bLotteryEvent = atoi(LotteryEvent) == 1;
  memset(LevelUpEvent, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "LevelUpEvent", LevelUpEvent, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      539,
      aZ02dC02d_2,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_bLevelUpEvent = atoi(LevelUpEvent) == 1;
  memset(Exp, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "Exp", Exp, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      547,
      aZ02dC02d_3,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_dwExp = atoi(Exp);
  memset(Drop, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "Drop", Drop, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      555,
      aZ02dC02d,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_dwDrop = atoi(Drop);
  memset(MinFame, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "FameMin", MinFame, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      563,
      aZ02dC02d_5,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_dwMinFame = atoi(MinFame);
  memset(MaxFame, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "FameMax", MaxFame, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      571,
      aZ02dC02d_6,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_dwMaxFame = atoi(MaxFame);
  memset(BattleLimit, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "BattleLimit", BattleLimit, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      579,
      aZ02dC02d_4,
      nZoneIndex,
      nChannelIndex);
    return 0;
  }
  this->m_wBattleLimit = atoi(BattleLimit);
  memset(szAgentServerAddr, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "AgentAddr", szAgentServerAddr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 587, byte_4DDE88);
    return 0;
  }
  memset(szLogServerAddr, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "LogServerAddr", szLogServerAddr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 594, byte_4DE4D4);
    return 0;
  }
  memset(szChatServerAddr, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", szSection, "ChatServerAddr", szChatServerAddr, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 601, byte_4DE4A8);
    return 0;
  }
  INET_Addr::set_addr(&this->m_ServerAddress[3], szAgentServerAddr, 0x277Bu);
  INET_Addr::set_addr(&this->m_ServerAddress[6], szLogServerAddr, 0x2782u);
  INET_Addr::set_addr(&this->m_ServerAddress[7], szChatServerAddr, 0x2783u);
  CTCPFactory::CTCPFactory(&tcpFactory);
  memset(myAddress, 0, 260);
  v26 = 0;
  if ( !CINETFamilyFactory::GetNetworkInfo(&tcpFactory, myAddress, 260) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 614, byte_4DE488);
    return 0;
  }
  GameServerUDPPort = CServerSetup::GetGameServerUDPPort(this->m_ServerID);
  INET_Addr::set_addr(&this->m_GameServerUDPAddr, myAddress, GameServerUDPPort);
  if ( !CServerSetup::LoadAdminUID(this) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 623, byte_4DE45C);
    return 0;
  }
  memset(NationType, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Zone_ETC", "GameServerNation", NationType, 0x104u) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerSetup::InitGameServer", aDWorkRylSource_11, 630, byte_4DDEB0);
    return 0;
  }
  this->m_eNationType = atoi(NationType);
  memset(szIsBattleServer, 0, 260);
  if ( !Registry::ReadString("DemonSetup.ini", "Zone_ETC", "IsBattleServerGroup", szIsBattleServer, 0x104u) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerSetup::InitGameServer",
      aDWorkRylSource_11,
      638,
      (char *)&byte_4DE40C);
    return 0;
  }
  this->m_bBattleGame = atoi(szIsBattleServer) != 0;
  return 1;
}

//----- (0043E010) --------------------------------------------------------
char __thiscall CServerSetup::Initialize(CServerSetup *this, int Type)
{
  char result; // al

  switch ( Type )
  {
    case 0:
      result = CServerSetup::InitLoginServer(this);
      break;
    case 1:
      result = CServerSetup::InitAuthServer(this);
      break;
    case 2:
      result = CServerSetup::InitGameServer(this);
      break;
    case 3:
      result = CServerSetup::InitAgentServer(this);
      break;
    case 4:
      result = CServerSetup::InitUIDServer(this);
      break;
    case 7:
      result = CServerSetup::InitChatServer(this);
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (0043E080) --------------------------------------------------------
const Item::ItemInfo *__thiscall Item::CItemMgr::GetItemInfo(Item::CItemMgr *this, unsigned __int16 usProtoTypeID)
{
  const Item::ItemInfo *result; // eax
  unsigned int m_nItemNum; // esi
  const Item::ItemInfo *v4; // edi
  const Item::ItemInfo *v5; // ecx

  result = this->m_ItemInfoArray;
  m_nItemNum = this->m_nItemNum;
  v4 = &result[this->m_nItemNum];
  while ( m_nItemNum )
  {
    v5 = &result[m_nItemNum >> 1];
    if ( v5->m_usProtoTypeID >= usProtoTypeID )
    {
      m_nItemNum >>= 1;
    }
    else
    {
      result = v5 + 1;
      m_nItemNum += -1 - (m_nItemNum >> 1);
    }
  }
  if ( result == v4 || usProtoTypeID < result->m_usProtoTypeID )
    return 0;
  return result;
}


//----- (0043E0E0) --------------------------------------------------------
unsigned __int16 __thiscall Item::CItemMgr::GetItemIDFromSkillID(
        Item::CItemMgr *this,
        unsigned __int16 usSkill_ID,
        unsigned __int16 unSkill_LockCount)
{
  Item::ItemInfo *m_ItemInfoArray; // eax
  Item::ItemInfo *v4; // ecx

  m_ItemInfoArray = this->m_ItemInfoArray;
  v4 = &m_ItemInfoArray[this->m_nItemNum];
  if ( m_ItemInfoArray == v4 )
    return 0;
  while ( m_ItemInfoArray->m_UseItemInfo.m_usSkill_ID != usSkill_ID
       || m_ItemInfoArray->m_UseItemInfo.m_usSkill_LockCount != unSkill_LockCount )
  {
    if ( ++m_ItemInfoArray == v4 )
      return 0;
  }
  return m_ItemInfoArray->m_usProtoTypeID;
}

//----- (0043E130) --------------------------------------------------------
Item::ItemInfo *__thiscall std::vector<Item::ItemInfo>::size(std::vector<Item::ItemInfo> *this)
{
  Item::ItemInfo *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (Item::ItemInfo *)(this->_Mylast - result);
  return result;
}

//----- (0043E160) --------------------------------------------------------
Item::ChemicalInfo *__thiscall std::vector<Item::ChemicalInfo>::size(std::vector<Item::ChemicalInfo> *this)
{
  Item::ChemicalInfo *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (Item::ChemicalInfo *)(this->_Mylast - result);
  return result;
}

//----- (0043E180) --------------------------------------------------------
void __cdecl std::fill<Item::ItemInfo *,Item::ItemInfo>(
        Item::ItemInfo *_First,
        Item::ItemInfo *_Last,
        const Item::ItemInfo *_Val)
{
  Item::ItemInfo *i; // eax
  Item::ItemInfo *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy((void *)v4, _Val, sizeof(Item::ItemInfo));
  }
}

//----- (0043E1B0) --------------------------------------------------------
void __cdecl std::fill<Item::ChemicalInfo *,Item::ChemicalInfo>(
        Item::ChemicalInfo *_First,
        Item::ChemicalInfo *_Last,
        const Item::ChemicalInfo *_Val)
{
  Item::ChemicalInfo *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (0043E1F0) --------------------------------------------------------
Item::ChemicalInfo *__cdecl std::_Copy_backward_opt<Item::ChemicalInfo *,Item::ChemicalInfo *>(
        Item::ChemicalInfo *_First,
        Item::ChemicalInfo *_Last,
        Item::ChemicalInfo *_Dest)
{
  Item::ChemicalInfo *v3; // ecx
  Item::ChemicalInfo *result; // eax

  v3 = _Last;
  for ( result = _Dest; v3 != _First; *(_WORD *)&result->m_cResultItemNum = *(_WORD *)&v3->m_cResultItemNum )
  {
    --v3;
    --result;
    *(_DWORD *)&result->m_wPickkingItemID = *(_DWORD *)&v3->m_wPickkingItemID;
    *(_DWORD *)&result->m_wResultItemID = *(_DWORD *)&v3->m_wResultItemID;
  }
  return result;
}

//----- (0043E230) --------------------------------------------------------
void __thiscall Item::CItemMgr::CItemMgr(Item::CItemMgr *this)
{
  CSingleton<Item::CItemMgr>::ms_pSingleton = this;
  this->m_nItemNum = 0;
  this->m_ItemInfoArray = 0;
  this->m_nChemicalNum = 0;
  this->m_ChemicalArray = 0;
}

//----- (0043E250) --------------------------------------------------------
void __thiscall Item::CItemMgr::~CItemMgr(Item::CItemMgr *this)
{
  Item::ChemicalInfo *m_ChemicalArray; // eax

  if ( this->m_ItemInfoArray )
  {
    operator delete[]((void *)this->m_ItemInfoArray);
    this->m_ItemInfoArray = 0;
  }
  m_ChemicalArray = this->m_ChemicalArray;
  this->m_nItemNum = 0;
  if ( m_ChemicalArray )
  {
    operator delete[](m_ChemicalArray);
    this->m_ChemicalArray = 0;
  }
  this->m_nChemicalNum = 0;
  CSingleton<Item::CItemMgr>::ms_pSingleton = 0;
}

//----- (0043E290) --------------------------------------------------------
unsigned __int8 __thiscall Item::CItemMgr::GetChemicalResult(Item::CItemMgr *this, Item::ChemicalInfo *chemical)
{
  Item::ChemicalInfo *m_ChemicalArray; // esi
  Item::ChemicalInfo *v4; // eax
  unsigned __int8 m_cPickkingItemNum; // bl
  unsigned __int8 m_cTargetItemNum; // cl
  unsigned __int8 v8; // al
  unsigned __int8 v9; // bl
  unsigned __int8 v10; // cl
  unsigned __int8 v11; // al
  unsigned __int8 v12; // [esp+Fh] [ebp-1h]
  unsigned __int8 chemicala; // [esp+14h] [ebp+4h]

  m_ChemicalArray = this->m_ChemicalArray;
  v4 = &m_ChemicalArray[this->m_nChemicalNum];
  if ( v4 == m_ChemicalArray )
    return 2;
  do
  {
    if ( chemical->m_wPickkingItemID == m_ChemicalArray->m_wPickkingItemID
      && chemical->m_wTargetItemID == m_ChemicalArray->m_wTargetItemID )
    {
      break;
    }
    ++m_ChemicalArray;
  }
  while ( m_ChemicalArray != v4 );
  if ( v4 == m_ChemicalArray )
    return 2;
  m_cPickkingItemNum = m_ChemicalArray->m_cPickkingItemNum;
  chemicala = chemical->m_cPickkingItemNum;
  if ( m_cPickkingItemNum > chemicala )
    return 3;
  m_cTargetItemNum = chemical->m_cTargetItemNum;
  v8 = m_ChemicalArray->m_cTargetItemNum;
  v12 = m_cTargetItemNum;
  if ( v8 > m_cTargetItemNum )
    return 3;
  if ( m_cTargetItemNum % (int)v8 || chemicala % (int)m_cPickkingItemNum )
    return 4;
  chemical->m_wResultItemID = m_ChemicalArray->m_wResultItemID;
  v9 = chemicala / (__int16)m_ChemicalArray->m_cPickkingItemNum;
  v10 = m_cTargetItemNum / (__int16)m_ChemicalArray->m_cTargetItemNum;
  if ( v9 < v10 )
  {
    chemical->m_cPickkingItemNum = 0;
    chemical->m_cTargetItemNum = v12 - v9 * m_ChemicalArray->m_cTargetItemNum;
    v11 = v9 * m_ChemicalArray->m_cResultItemNum;
  }
  else
  {
    chemical->m_cPickkingItemNum = chemicala - v10 * m_ChemicalArray->m_cPickkingItemNum;
    chemical->m_cTargetItemNum = 0;
    v11 = v10 * m_ChemicalArray->m_cResultItemNum;
  }
  chemical->m_cResultItemNum = v11;
  return 0;
}

//----- (0043E3A0) --------------------------------------------------------
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this, const Item::ItemInfo *__that)
{
  this->m_usProtoTypeID = __that->m_usProtoTypeID;
  qmemcpy(&this->m_DetailData, &__that->m_DetailData, 0x38u);
  qmemcpy(&this->m_SpriteData, &__that->m_SpriteData, sizeof(this->m_SpriteData));
  qmemcpy(&this->m_StringData, &__that->m_StringData, sizeof(this->m_StringData));
  qmemcpy(&this->m_EquipAttribute, &__that->m_EquipAttribute, 0x4Du);
  this->m_UseItemInfo.m_Pos = __that->m_UseItemInfo.m_Pos;
  this->m_UseItemInfo.m_dwAmount = __that->m_UseItemInfo.m_dwAmount;
}

//----- (0043E480) --------------------------------------------------------
Item::ItemInfo *__cdecl std::copy_backward<Item::ItemInfo *,Item::ItemInfo *>(
        Item::ItemInfo *_First,
        Item::ItemInfo *_Last,
        Item::ItemInfo *_Dest)
{
  Item::ItemInfo *v3; // edx
  Item::ItemInfo *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy((void *)--result, --v3, sizeof(Item::ItemInfo));
  return result;
}

//----- (0043E4C0) --------------------------------------------------------
void __cdecl std::swap<Item::ItemInfo>(Item::ItemInfo *_Left, Item::ItemInfo *_Right)
{
  Item::ItemInfo _Tmp; // [esp+8h] [ebp-288h] BYREF

  Item::ItemInfo::ItemInfo(&_Tmp, _Left);
  qmemcpy((void *)_Left, _Right, sizeof(Item::ItemInfo));
  qmemcpy((void *)_Right, &_Tmp, sizeof(Item::ItemInfo));
}

//----- (0043E520) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Mid,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  bool v3; // cf
  int v4; // edx
  int v5; // esi
  bool v6; // cf
  int v7; // edx
  int v8; // edi
  bool v9; // cf
  int v10; // edx
  int v11; // esi
  __int16 v12; // di
  __int16 v13; // [esp+18h] [ebp-4h]
  __int16 v14; // [esp+18h] [ebp-4h]

  v3 = _Mid._Myptr->m_wPickkingItemID < _First._Myptr->m_wPickkingItemID;
  if ( _Mid._Myptr->m_wPickkingItemID == _First._Myptr->m_wPickkingItemID )
    v3 = _Mid._Myptr->m_wTargetItemID < _First._Myptr->m_wTargetItemID;
  if ( v3 )
  {
    v4 = *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID;
    v5 = *(_DWORD *)&_Mid._Myptr->m_wResultItemID;
    v13 = *(_WORD *)&_Mid._Myptr->m_cResultItemNum;
    *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID = *(_DWORD *)&_First._Myptr->m_wPickkingItemID;
    *(_DWORD *)&_Mid._Myptr->m_wResultItemID = *(_DWORD *)&_First._Myptr->m_wResultItemID;
    *(_WORD *)&_Mid._Myptr->m_cResultItemNum = *(_WORD *)&_First._Myptr->m_cResultItemNum;
    *(_DWORD *)&_First._Myptr->m_wPickkingItemID = v4;
    *(_DWORD *)&_First._Myptr->m_wResultItemID = v5;
    *(_WORD *)&_First._Myptr->m_cResultItemNum = v13;
  }
  v6 = _Last._Myptr->m_wPickkingItemID < _Mid._Myptr->m_wPickkingItemID;
  if ( _Last._Myptr->m_wPickkingItemID == _Mid._Myptr->m_wPickkingItemID )
    v6 = _Last._Myptr->m_wTargetItemID < _Mid._Myptr->m_wTargetItemID;
  if ( v6 )
  {
    v7 = *(_DWORD *)&_Last._Myptr->m_wPickkingItemID;
    v8 = *(_DWORD *)&_Last._Myptr->m_wResultItemID;
    v14 = *(_WORD *)&_Last._Myptr->m_cResultItemNum;
    *(_DWORD *)&_Last._Myptr->m_wPickkingItemID = *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID;
    *(_DWORD *)&_Last._Myptr->m_wResultItemID = *(_DWORD *)&_Mid._Myptr->m_wResultItemID;
    *(_WORD *)&_Last._Myptr->m_cResultItemNum = *(_WORD *)&_Mid._Myptr->m_cResultItemNum;
    *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID = v7;
    *(_DWORD *)&_Mid._Myptr->m_wResultItemID = v8;
    *(_WORD *)&_Mid._Myptr->m_cResultItemNum = v14;
  }
  v9 = _Mid._Myptr->m_wPickkingItemID < _First._Myptr->m_wPickkingItemID;
  if ( _Mid._Myptr->m_wPickkingItemID == _First._Myptr->m_wPickkingItemID )
    v9 = _Mid._Myptr->m_wTargetItemID < _First._Myptr->m_wTargetItemID;
  if ( v9 )
  {
    v10 = *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID;
    v11 = *(_DWORD *)&_Mid._Myptr->m_wResultItemID;
    v12 = *(_WORD *)&_Mid._Myptr->m_cResultItemNum;
    *(_DWORD *)&_Mid._Myptr->m_wPickkingItemID = *(_DWORD *)&_First._Myptr->m_wPickkingItemID;
    *(_DWORD *)&_Mid._Myptr->m_wResultItemID = *(_DWORD *)&_First._Myptr->m_wResultItemID;
    *(_WORD *)&_Mid._Myptr->m_cResultItemNum = *(_WORD *)&_First._Myptr->m_cResultItemNum;
    *(_DWORD *)&_First._Myptr->m_wPickkingItemID = v10;
    *(_DWORD *)&_First._Myptr->m_wResultItemID = v11;
    *(_WORD *)&_First._Myptr->m_cResultItemNum = v12;
  }
}

//----- (0043E620) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        std::vector<Item::ItemInfo>::iterator _First,
        int _Hole,
        int _Top,
        Item::ItemInfo _Val)
{
  int v4; // edi
  int i; // eax

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    if ( _First._Myptr[i].m_usProtoTypeID >= _Val.m_usProtoTypeID )
      break;
    qmemcpy((void *)&_First._Myptr[v4], &_First._Myptr[i], sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy((void *)&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (0043E690) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Mid,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  int v3; // ebp
  int v4; // eax
  int v5; // esi
  int v6; // edx
  int v7; // esi
  const Item::ItemInfo *v8; // edi
  std::vector<Item::ItemInfo>::iterator *p_First; // eax
  const Item::ItemInfo *Myptr; // ebx
  int v11; // eax
  const void **v12; // eax
  int v13; // eax
  const Item::ItemInfo *v14; // [esp+Ch] [ebp-2A0h]
  int v15; // [esp+14h] [ebp-298h]
  const Item::ItemInfo *v16; // [esp+18h] [ebp-294h] BYREF
  Item::ItemInfo *v17; // [esp+1Ch] [ebp-290h] BYREF
  const Item::ItemInfo *v18; // [esp+20h] [ebp-28Ch] BYREF
  Item::ItemInfo _Holeval; // [esp+24h] [ebp-288h] BYREF

  v3 = _Mid._Myptr - _First._Myptr;
  v4 = _Last._Myptr - _First._Myptr;
  v5 = v3;
  if ( v3 )
  {
    do
    {
      v6 = v4 % v5;
      v4 = v5;
      v5 = v6;
    }
    while ( v6 );
  }
  if ( v4 < _Last._Myptr - _First._Myptr && v4 > 0 )
  {
    v7 = v3;
    v8 = &_First._Myptr[v4];
    v14 = v8;
    v15 = v4;
    while ( 1 )
    {
      Item::ItemInfo::ItemInfo(&_Holeval, v8);
      if ( &v8[v7] == _Last._Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v16 = &v8[v7];
        p_First = (std::vector<Item::ItemInfo>::iterator *)&v16;
      }
      Myptr = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          qmemcpy((void *)v8, Myptr, sizeof(const Item::ItemInfo));
          v11 = _Last._Myptr - Myptr;
          v8 = Myptr;
          if ( v3 >= v11 )
          {
            v17 = &_First._Myptr[v3 - v11];
            v12 = (const void **)&v17;
          }
          else
          {
            v18 = &Myptr[v3];
            v12 = (const void **)&v18;
          }
          Myptr = (const Item::ItemInfo *)*v12;
        }
        while ( *v12 != v14 );
      }
      v13 = v15;
      qmemcpy((void *)v8, &_Holeval, sizeof(const Item::ItemInfo));
      --v14;
      --v15;
      if ( v13 == 1 )
        break;
      v7 = v3;
      v8 = v14;
    }
  }
}

//----- (0043E800) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        int _Hole,
        int _Top,
        Item::ChemicalInfo _Val)
{
  int v4; // esi
  int i; // eax
  unsigned __int16 m_wPickkingItemID; // dx
  bool v7; // cf
  Item::ChemicalInfo *v8; // ecx
  Item::ChemicalInfo *v9; // edx

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    m_wPickkingItemID = _First._Myptr[i].m_wPickkingItemID;
    v7 = m_wPickkingItemID < _Val.m_wPickkingItemID;
    v8 = &_First._Myptr[i];
    if ( m_wPickkingItemID == _Val.m_wPickkingItemID )
      v7 = v8->m_wTargetItemID < _Val.m_wTargetItemID;
    if ( !v7 )
      break;
    v9 = &_First._Myptr[v4];
    *(_DWORD *)&v9->m_wPickkingItemID = *(_DWORD *)&v8->m_wPickkingItemID;
    *(_DWORD *)&v9->m_wResultItemID = *(_DWORD *)&v8->m_wResultItemID;
    v4 = i;
    *(_WORD *)&v9->m_cResultItemNum = *(_WORD *)&v8->m_cResultItemNum;
  }
  _First._Myptr[v4] = _Val;
}

//----- (0043E890) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Mid,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  Item::ChemicalInfo *Myptr; // ebp
  int v4; // esi
  int v5; // eax
  int v6; // edi
  int v7; // edx
  Item::ChemicalInfo *v8; // edx
  Item::ChemicalInfo *v9; // ebx
  int v10; // ecx
  Item::ChemicalInfo *v11; // edi
  std::vector<Item::ChemicalInfo>::iterator *p_First; // eax
  Item::ChemicalInfo *v13; // ecx
  int v14; // eax
  Item::ChemicalInfo **v15; // eax
  int v16; // eax
  __int16 v17; // cx
  bool v18; // zf
  Item::ChemicalInfo *v19; // eax
  char *v20; // [esp+10h] [ebp-18h] BYREF
  char *v21; // [esp+14h] [ebp-14h] BYREF
  Item::ChemicalInfo *v22; // [esp+18h] [ebp-10h] BYREF
  Item::ChemicalInfo _Holeval; // [esp+1Ch] [ebp-Ch]

  Myptr = _Last._Myptr;
  v4 = _Mid._Myptr - _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  v6 = v4;
  if ( v4 )
  {
    do
    {
      v7 = v5 % v6;
      v5 = v6;
      v6 = v7;
    }
    while ( v7 );
  }
  if ( v5 < _Last._Myptr - _First._Myptr && v5 > 0 )
  {
    v8 = (Item::ChemicalInfo *)(10 * v4);
    _Mid._Myptr = (Item::ChemicalInfo *)(10 * v4);
    v9 = &_First._Myptr[v5];
    _Last._Myptr = (Item::ChemicalInfo *)v5;
    do
    {
      *(_DWORD *)&_Holeval.m_wPickkingItemID = *(_DWORD *)&v9->m_wPickkingItemID;
      v10 = *(_DWORD *)&v9->m_wResultItemID;
      *(_WORD *)&_Holeval.m_cResultItemNum = *(_WORD *)&v9->m_cResultItemNum;
      v11 = v9;
      *(_DWORD *)&_Holeval.m_wResultItemID = v10;
      if ( (Item::ChemicalInfo *)((char *)v8 + (_DWORD)v9) == Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v20 = (char *)v8 + (_DWORD)v9;
        p_First = (std::vector<Item::ChemicalInfo>::iterator *)&v20;
      }
      v13 = p_First->_Myptr;
      if ( p_First->_Myptr != v9 )
      {
        do
        {
          *(_DWORD *)&v11->m_wPickkingItemID = *(_DWORD *)&v13->m_wPickkingItemID;
          *(_DWORD *)&v11->m_wResultItemID = *(_DWORD *)&v13->m_wResultItemID;
          *(_WORD *)&v11->m_cResultItemNum = *(_WORD *)&v13->m_cResultItemNum;
          v14 = Myptr - v13;
          v11 = v13;
          if ( v4 >= v14 )
          {
            v22 = &_First._Myptr[v4 - v14];
            v15 = &v22;
          }
          else
          {
            v21 = (char *)v13 + (unsigned int)_Mid._Myptr;
            v15 = (Item::ChemicalInfo **)&v21;
          }
          v13 = *v15;
        }
        while ( *v15 != v9 );
        v8 = _Mid._Myptr;
      }
      v16 = *(_DWORD *)&_Holeval.m_wResultItemID;
      *(_DWORD *)&v11->m_wPickkingItemID = *(_DWORD *)&_Holeval.m_wPickkingItemID;
      v17 = *(_WORD *)&_Holeval.m_cResultItemNum;
      *(_DWORD *)&v11->m_wResultItemID = v16;
      --v9;
      v19 = (Item::ChemicalInfo *)((char *)_Last._Myptr - 1);
      v18 = _Last._Myptr == (Item::ChemicalInfo *)1;
      *(_WORD *)&v11->m_cResultItemNum = v17;
      _Last._Myptr = v19;
    }
    while ( !v18 );
  }
}

//----- (0043E9D0) --------------------------------------------------------
Item::ItemInfo *__cdecl std::copy<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo *>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last,
        Item::ItemInfo *_Dest)
{
  Item::ItemInfo *Myptr; // edx
  Item::ItemInfo *result; // eax
  Item::ItemInfo *v5; // esi
  Item::ItemInfo *v6; // edi

  Myptr = _First._Myptr;
  result = _Dest;
  while ( Myptr != _Last._Myptr )
  {
    v5 = Myptr;
    v6 = result;
    ++Myptr;
    ++result;
    qmemcpy((void *)v6, v5, sizeof(Item::ItemInfo));
  }
  return result;
}

//----- (0043EA10) --------------------------------------------------------
Item::ChemicalInfo *__cdecl std::_Copy_opt<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last,
        Item::ChemicalInfo *_Dest)
{
  Item::ChemicalInfo *Myptr; // ecx
  Item::ChemicalInfo *result; // eax
  Item::ChemicalInfo *v5; // edi
  __int16 v6; // si

  Myptr = _First._Myptr;
  for ( result = _Dest; Myptr != _Last._Myptr; *(_WORD *)&v5->m_cResultItemNum = v6 )
  {
    v5 = result;
    *(_DWORD *)&result->m_wPickkingItemID = *(_DWORD *)&Myptr->m_wPickkingItemID;
    *(_DWORD *)&result->m_wResultItemID = *(_DWORD *)&Myptr->m_wResultItemID;
    v6 = *(_WORD *)&Myptr->m_cResultItemNum;
    ++Myptr;
    ++result;
  }
  return result;
}

//----- (0043EA50) --------------------------------------------------------
Item::ItemInfo *__cdecl std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(
        Item::ItemInfo *_First,
        Item::ItemInfo *_Last,
        Item::ItemInfo *_Dest)
{
  Item::ItemInfo *v3; // esi
  Item::ItemInfo *v4; // edi

  v3 = _First;
  if ( _First == _Last )
    return _Dest;
  v4 = _Dest;
  do
  {
    if ( v4 )
      Item::ItemInfo::ItemInfo(v4, v3);
    ++v3;
    ++v4;
  }
  while ( v3 != _Last );
  return v4;
}

//----- (0043EA90) --------------------------------------------------------
Item::ChemicalInfo *__cdecl std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(
        Item::ChemicalInfo *_First,
        Item::ChemicalInfo *_Last,
        Item::ChemicalInfo *_Dest)
{
  Item::ChemicalInfo *v3; // ecx
  Item::ChemicalInfo *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
    {
      *(_DWORD *)&result->m_wPickkingItemID = *(_DWORD *)&v3->m_wPickkingItemID;
      *(_DWORD *)&result->m_wResultItemID = *(_DWORD *)&v3->m_wResultItemID;
      *(_WORD *)&result->m_cResultItemNum = *(_WORD *)&v3->m_cResultItemNum;
    }
    ++v3;
  }
  return result;
}

//----- (0043EAD0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<Item::ChemicalInfo>::iterator>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Mid,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  int v3; // eax
  unsigned int v4; // edi
  unsigned int v5; // esi
  Item::ChemicalInfo *v7; // [esp-10h] [ebp-14h]
  Item::ChemicalInfo *_Firsta; // [esp+8h] [ebp+4h]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(_First, _Mid, _Last);
  }
  else
  {
    v4 = 20 * ((v3 + 1) / 8);
    v5 = 10 * ((v3 + 1) / 8);
    v7 = &_First._Myptr[v4 / 0xA];
    _Firsta = &_First._Myptr[v5 / 0xA];
    std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(
      _First,
      (std::vector<Item::ChemicalInfo>::iterator)_Firsta,
      (std::vector<Item::ChemicalInfo>::iterator)v7);
    std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(
      (std::vector<Item::ChemicalInfo>::iterator)&_Mid._Myptr[v5 / 0xFFFFFFF6],
      _Mid,
      (std::vector<Item::ChemicalInfo>::iterator)&_Mid._Myptr[v5 / 0xA]);
    std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(
      (std::vector<Item::ChemicalInfo>::iterator)&_Last._Myptr[v4 / 0xFFFFFFF6],
      (std::vector<Item::ChemicalInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF6],
      _Last);
    std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(
      (std::vector<Item::ChemicalInfo>::iterator)_Firsta,
      _Mid,
      (std::vector<Item::ChemicalInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF6]);
  }
}

//----- (0043EB70) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        std::vector<Item::ItemInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Item::ItemInfo _Val)
{
  int v4; // edi
  int v5; // eax
  bool i; // zf
  Item::ItemInfo v7; // [esp-284h] [ebp-294h] BYREF

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5].m_usProtoTypeID < _First._Myptr[v5 - 1].m_usProtoTypeID )
      --v5;
    qmemcpy((void *)&_First._Myptr[v4], &_First._Myptr[v5], sizeof(_First._Myptr[v4]));
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    qmemcpy((void *)&_First._Myptr[v4], &_First._Myptr[_Bottom - 1], sizeof(_First._Myptr[v4]));
    v4 = _Bottom - 1;
  }
  Item::ItemInfo::ItemInfo(&v7, &_Val);
  std::_Push_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(_First, v4, _Hole, v7);
}

//----- (0043EC20) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Item::ChemicalInfo _Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf
  unsigned __int16 m_wPickkingItemID; // di
  unsigned __int16 v8; // bx
  bool v9; // cf
  Item::ChemicalInfo *v10; // edx
  Item::ChemicalInfo *v11; // ecx
  Item::ChemicalInfo *v12; // eax
  Item::ChemicalInfo *v13; // ecx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    m_wPickkingItemID = _First._Myptr[v5].m_wPickkingItemID;
    v8 = _First._Myptr[v5 - 1].m_wPickkingItemID;
    v9 = m_wPickkingItemID < v8;
    if ( m_wPickkingItemID == v8 )
      v9 = _First._Myptr[v5].m_wTargetItemID < _First._Myptr[v5 - 1].m_wTargetItemID;
    if ( v9 )
      --v5;
    v10 = &_First._Myptr[v5];
    v11 = &_First._Myptr[v4];
    *(_DWORD *)&v11->m_wPickkingItemID = *(_DWORD *)&v10->m_wPickkingItemID;
    *(_DWORD *)&v11->m_wResultItemID = *(_DWORD *)&v10->m_wResultItemID;
    *(_WORD *)&v11->m_cResultItemNum = *(_WORD *)&v10->m_cResultItemNum;
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v12 = &_First._Myptr[_Bottom - 1];
    v13 = &_First._Myptr[v4];
    *(_DWORD *)&v13->m_wPickkingItemID = *(_DWORD *)&v12->m_wPickkingItemID;
    *(_DWORD *)&v13->m_wResultItemID = *(_DWORD *)&v12->m_wResultItemID;
    *(_WORD *)&v13->m_cResultItemNum = *(_WORD *)&v12->m_cResultItemNum;
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(_First, v4, _Hole, _Val);
}

//----- (0043ECF0) --------------------------------------------------------
char __thiscall CParseDelimitedData::operator()(
        CParseDelimitedData *this,
        std::vector<ItemDataParser::ParseData> *ParserArray,
        Item::ItemInfo *itemInfo)
{
  ItemDataParser::ParseData *Myfirst; // esi
  char *StringValue; // eax

  Myfirst = ParserArray->_Myfirst;
  if ( Myfirst == ParserArray->_Mylast )
    return 1;
  while ( 1 )
  {
    StringValue = CTokenlizedFile::GetStringValue(this->m_TokenlizedFile, Myfirst->m_szColumnName);
    if ( !StringValue )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CParseDelimitedData::operator`()'",
        aDWorkRylSource_66,
        54,
        byte_4DE880,
        this->m_TokenlizedFile->m_nLine,
        Myfirst->m_szColumnName);
      return 0;
    }
    if ( !Myfirst->m_fnParseFunc(itemInfo, StringValue) )
      break;
    if ( ++Myfirst == ParserArray->_Mylast )
      return 1;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CParseDelimitedData::operator`()'",
    aDWorkRylSource_66,
    61,
    (char *)&byte_4DE84C,
    this->m_TokenlizedFile->m_nLine,
    Myfirst->m_szColumnName);
  return 0;
}

//----- (0043ED80) --------------------------------------------------------
Item::ChemicalInfo *__cdecl std::copy<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last,
        Item::ChemicalInfo *_Dest)
{
  return std::_Copy_opt<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(_First, _Last, _Dest);
}

//----- (0043EDA0) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<Item::ItemInfo *,unsigned int,Item::ItemInfo,std::allocator<Item::ItemInfo>>(
        Item::ItemInfo *_First,
        unsigned int _Count,
        const Item::ItemInfo *_Val)
{
  unsigned int v4; // edi

  if ( _Count )
  {
    v4 = _Count;
    do
    {
      if ( _First )
        Item::ItemInfo::ItemInfo(_First, _Val);
      ++_First;
      --v4;
    }
    while ( v4 );
  }
}

//----- (0043EDD0) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<Item::ChemicalInfo *,unsigned int,Item::ChemicalInfo,std::allocator<Item::ChemicalInfo>>(
        Item::ChemicalInfo *_First,
        unsigned int _Count,
        const Item::ChemicalInfo *_Val)
{
  unsigned int v3; // ecx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (0043EE10) --------------------------------------------------------
std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Item::ChemicalInfo>::iterator>(
        std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> *result,
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  Item::ChemicalInfo *v3; // esi
  unsigned int Myptr; // ebx
  unsigned int v5; // ecx
  Item::ChemicalInfo *v6; // esi
  unsigned __int16 m_wPickkingItemID; // di
  unsigned __int16 v8; // bp
  bool v9; // cf
  bool v10; // cf
  unsigned __int16 v11; // dx
  unsigned __int16 v12; // si
  bool v13; // cf
  bool v14; // cf
  unsigned int v15; // esi
  unsigned int v16; // edx
  unsigned __int16 v17; // si
  unsigned __int16 v18; // di
  bool v19; // cf
  bool v20; // cf
  int v21; // esi
  int v22; // edi
  bool v23; // zf
  unsigned int v24; // esi
  unsigned __int16 v25; // di
  unsigned __int16 v26; // bp
  bool v27; // cf
  bool v28; // cf
  int v29; // eax
  int v30; // eax
  unsigned int v31; // eax
  std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> *v32; // eax
  Item::ChemicalInfo *_Glast; // [esp+10h] [ebp-5Ch]
  std::vector<Item::ChemicalInfo>::iterator _Plast; // [esp+14h] [ebp-58h]
  int v35; // [esp+18h] [ebp-54h]
  int v36; // [esp+1Ch] [ebp-50h]
  __int16 v37; // [esp+20h] [ebp-4Ch]
  int v38; // [esp+24h] [ebp-48h]
  int v39; // [esp+28h] [ebp-44h]
  __int16 v40; // [esp+2Ch] [ebp-40h]
  int v41; // [esp+30h] [ebp-3Ch]
  int v42; // [esp+34h] [ebp-38h]
  __int16 v43; // [esp+38h] [ebp-34h]
  int v44; // [esp+3Ch] [ebp-30h]
  int v45; // [esp+40h] [ebp-2Ch]
  __int16 v46; // [esp+44h] [ebp-28h]
  int v47; // [esp+4Ch] [ebp-20h]
  __int16 v48; // [esp+50h] [ebp-1Ch]
  int v49; // [esp+58h] [ebp-14h]
  __int16 v50; // [esp+5Ch] [ebp-10h]
  __int16 v51; // [esp+68h] [ebp-4h]

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<Item::ChemicalInfo>::iterator>(
    _First,
    (std::vector<Item::ChemicalInfo>::iterator)v3,
    (std::vector<Item::ChemicalInfo>::iterator)&_Last._Myptr[-1]);
  Myptr = (unsigned int)&v3[1];
  v5 = (unsigned int)v3;
  _Plast._Myptr = v3 + 1;
  if ( _First._Myptr < v3 )
  {
    v6 = v3 - 1;
    do
    {
      m_wPickkingItemID = v6->m_wPickkingItemID;
      v8 = *(_WORD *)v5;
      v9 = v6->m_wPickkingItemID < *(_WORD *)v5;
      if ( v6->m_wPickkingItemID == *(_WORD *)v5 )
        v9 = v6->m_wTargetItemID < v6[1].m_wTargetItemID;
      if ( v9 )
        break;
      v10 = v8 < m_wPickkingItemID;
      if ( v8 == m_wPickkingItemID )
        v10 = v6[1].m_wTargetItemID < v6->m_wTargetItemID;
      if ( v10 )
        break;
      v5 -= 10;
      --v6;
    }
    while ( (unsigned int)_First._Myptr < v5 );
  }
  if ( Myptr < (unsigned int)_Last._Myptr )
  {
    v11 = *(_WORD *)v5;
    do
    {
      v12 = *(_WORD *)Myptr;
      v13 = *(_WORD *)Myptr < v11;
      if ( *(_WORD *)Myptr == v11 )
        v13 = *(_WORD *)(Myptr + 2) < *(_WORD *)(v5 + 2);
      if ( v13 )
        break;
      v14 = v11 < v12;
      if ( v11 == v12 )
        v14 = *(_WORD *)(v5 + 2) < *(_WORD *)(Myptr + 2);
      if ( v14 )
        break;
      Myptr += 10;
    }
    while ( Myptr < (unsigned int)_Last._Myptr );
    _Plast._Myptr = (Item::ChemicalInfo *)Myptr;
  }
  v15 = v5;
  v16 = Myptr;
  _Glast = (Item::ChemicalInfo *)v5;
  while ( 2 )
  {
    while ( 2 )
    {
      if ( v16 < (unsigned int)_Last._Myptr )
      {
        while ( 1 )
        {
          v17 = *(_WORD *)v5;
          v18 = *(_WORD *)v16;
          v19 = *(_WORD *)v5 < *(_WORD *)v16;
          if ( *(_WORD *)v5 == *(_WORD *)v16 )
            v19 = *(_WORD *)(v5 + 2) < *(_WORD *)(v16 + 2);
          if ( !v19 )
          {
            v20 = v18 < v17;
            if ( v18 == v17 )
              v20 = *(_WORD *)(v16 + 2) < *(_WORD *)(v5 + 2);
            if ( v20 )
            {
LABEL_30:
              v15 = (unsigned int)_Glast;
              break;
            }
            _Plast._Myptr = (Item::ChemicalInfo *)(Myptr + 10);
            v21 = *(_DWORD *)Myptr;
            v22 = *(_DWORD *)(Myptr + 4);
            v51 = *(_WORD *)(Myptr + 8);
            *(_DWORD *)Myptr = *(_DWORD *)v16;
            *(_DWORD *)(Myptr + 4) = *(_DWORD *)(v16 + 4);
            *(_WORD *)(Myptr + 8) = *(_WORD *)(v16 + 8);
            Myptr += 10;
            *(_DWORD *)v16 = v21;
            *(_DWORD *)(v16 + 4) = v22;
            *(_WORD *)(v16 + 8) = v51;
          }
          v16 += 10;
          if ( v16 >= (unsigned int)_Last._Myptr )
            goto LABEL_30;
        }
      }
      v23 = (Item::ChemicalInfo *)v15 == _First._Myptr;
      if ( v15 <= (unsigned int)_First._Myptr )
        goto LABEL_42;
      v24 = v15 - 10;
      do
      {
        v25 = *(_WORD *)v24;
        v26 = *(_WORD *)v5;
        v27 = *(_WORD *)v24 < *(_WORD *)v5;
        if ( *(_WORD *)v24 == *(_WORD *)v5 )
          v27 = *(_WORD *)(v24 + 2) < *(_WORD *)(v5 + 2);
        if ( v27 )
          goto LABEL_40;
        v28 = v26 < v25;
        if ( v26 == v25 )
          v28 = *(_WORD *)(v5 + 2) < *(_WORD *)(v24 + 2);
        if ( v28 )
          break;
        v5 -= 10;
        v29 = *(_DWORD *)v5;
        v48 = *(_WORD *)(v5 + 8);
        v47 = *(_DWORD *)(v5 + 4);
        *(_DWORD *)v5 = *(_DWORD *)v24;
        *(_DWORD *)(v5 + 4) = *(_DWORD *)(v24 + 4);
        *(_WORD *)(v5 + 8) = *(_WORD *)(v24 + 8);
        Myptr = (unsigned int)_Plast._Myptr;
        *(_DWORD *)v24 = v29;
        *(_DWORD *)(v24 + 4) = v47;
        *(_WORD *)(v24 + 8) = v48;
LABEL_40:
        v24 -= 10;
        v9 = _First._Myptr < &_Glast[-1];
        --_Glast;
      }
      while ( v9 );
      v15 = (unsigned int)_Glast;
      v23 = _Glast == _First._Myptr;
LABEL_42:
      if ( !v23 )
      {
        v15 -= 10;
        _Glast = (Item::ChemicalInfo *)v15;
        if ( (Item::ChemicalInfo *)v16 == _Last._Myptr )
        {
          v5 -= 10;
          if ( v15 != v5 )
          {
            v38 = *(_DWORD *)v15;
            v40 = *(_WORD *)(v15 + 8);
            v39 = *(_DWORD *)(v15 + 4);
            *(_DWORD *)v15 = *(_DWORD *)v5;
            *(_DWORD *)(v15 + 4) = *(_DWORD *)(v5 + 4);
            *(_WORD *)(v15 + 8) = *(_WORD *)(v5 + 8);
            *(_DWORD *)v5 = v38;
            *(_DWORD *)(v5 + 4) = v39;
            *(_WORD *)(v5 + 8) = v40;
          }
          v41 = *(_DWORD *)v5;
          v43 = *(_WORD *)(v5 + 8);
          Myptr -= 10;
          v42 = *(_DWORD *)(v5 + 4);
          *(_DWORD *)v5 = *(_DWORD *)Myptr;
          *(_DWORD *)(v5 + 4) = *(_DWORD *)(Myptr + 4);
          *(_WORD *)(v5 + 8) = *(_WORD *)(Myptr + 8);
          *(_DWORD *)Myptr = v41;
          *(_DWORD *)(Myptr + 4) = v42;
          _Plast._Myptr = (Item::ChemicalInfo *)Myptr;
          *(_WORD *)(Myptr + 8) = v43;
        }
        else
        {
          v44 = *(_DWORD *)v16;
          v46 = *(_WORD *)(v16 + 8);
          v45 = *(_DWORD *)(v16 + 4);
          *(_DWORD *)v16 = *(_DWORD *)v15;
          *(_DWORD *)(v16 + 4) = *(_DWORD *)(v15 + 4);
          *(_WORD *)(v16 + 8) = *(_WORD *)(v15 + 8);
          *(_DWORD *)v15 = v44;
          *(_DWORD *)(v15 + 4) = v45;
          v16 += 10;
          *(_WORD *)(v15 + 8) = v46;
        }
        continue;
      }
      break;
    }
    if ( (Item::ChemicalInfo *)v16 != _Last._Myptr )
    {
      if ( Myptr != v16 )
      {
        v30 = *(_DWORD *)v5;
        v50 = *(_WORD *)(v5 + 8);
        v49 = *(_DWORD *)(v5 + 4);
        *(_DWORD *)v5 = *(_DWORD *)Myptr;
        *(_DWORD *)(v5 + 4) = *(_DWORD *)(Myptr + 4);
        *(_WORD *)(v5 + 8) = *(_WORD *)(Myptr + 8);
        *(_DWORD *)Myptr = v30;
        *(_DWORD *)(Myptr + 4) = v49;
        *(_WORD *)(Myptr + 8) = v50;
      }
      v35 = *(_DWORD *)v5;
      v31 = v16;
      v37 = *(_WORD *)(v5 + 8);
      v36 = *(_DWORD *)(v5 + 4);
      *(_DWORD *)v5 = *(_DWORD *)v16;
      *(_DWORD *)(v5 + 4) = *(_DWORD *)(v16 + 4);
      *(_WORD *)(v5 + 8) = *(_WORD *)(v16 + 8);
      *(_DWORD *)v16 = v35;
      *(_DWORD *)(v16 + 4) = v36;
      Myptr += 10;
      v16 += 10;
      *(_WORD *)(v31 + 8) = v37;
      v15 = (unsigned int)_Glast;
      _Plast._Myptr = (Item::ChemicalInfo *)Myptr;
      v5 += 10;
      continue;
    }
    break;
  }
  v32 = result;
  result->second._Myptr = (Item::ChemicalInfo *)Myptr;
  result->first._Myptr = (Item::ChemicalInfo *)v5;
  return v32;
}

//----- (0043F1E0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<Item::ItemInfo>::iterator>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Mid,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  Item::ItemInfo *Myptr; // esi
  int v4; // eax
  int v5; // eax
  unsigned int v6; // edi
  Item::ItemInfo *v7; // ebx
  unsigned int v8; // ebp
  bool v9; // cf
  Item::ItemInfo *v10; // esi
  Item::ItemInfo *v11; // eax
  Item::ItemInfo *v12; // esi
  unsigned __int16 m_usProtoTypeID; // cx
  Item::ItemInfo *v14; // edi

  Myptr = _First._Myptr;
  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    if ( _Mid._Myptr->m_usProtoTypeID < _First._Myptr->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(_Mid._Myptr, _First._Myptr);
    if ( _Last._Myptr->m_usProtoTypeID < _Mid._Myptr->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(_Last._Myptr, _Mid._Myptr);
    if ( _Mid._Myptr->m_usProtoTypeID < _First._Myptr->m_usProtoTypeID )
      goto LABEL_31;
  }
  else
  {
    v5 = (v4 + 1) / 8;
    v6 = 644 * v5;
    v7 = &_First._Myptr[v5];
    v8 = 1288 * v5;
    v9 = v7->m_usProtoTypeID < _First._Myptr->m_usProtoTypeID;
    _First._Myptr = v7;
    if ( v9 )
      std::swap<Item::ItemInfo>(v7, Myptr);
    if ( Myptr[v8 / 0x284].m_usProtoTypeID < v7->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(&Myptr[v8 / 0x284], v7);
    if ( v7->m_usProtoTypeID < Myptr->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(v7, Myptr);
    v10 = &_Mid._Myptr[v6 / 0xFFFFFD7C];
    if ( _Mid._Myptr->m_usProtoTypeID < _Mid._Myptr[v6 / 0xFFFFFD7C].m_usProtoTypeID )
      std::swap<Item::ItemInfo>(_Mid._Myptr, &_Mid._Myptr[v6 / 0xFFFFFD7C]);
    if ( _Mid._Myptr[v6 / 0x284].m_usProtoTypeID < _Mid._Myptr->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(&_Mid._Myptr[v6 / 0x284], _Mid._Myptr);
    if ( _Mid._Myptr->m_usProtoTypeID < v10->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(_Mid._Myptr, v10);
    v11 = _Last._Myptr;
    v12 = &_Last._Myptr[v6 / 0xFFFFFD7C];
    m_usProtoTypeID = _Last._Myptr[v6 / 0xFFFFFD7C].m_usProtoTypeID;
    v14 = &_Last._Myptr[v8 / 0xFFFFFD7C];
    if ( m_usProtoTypeID < _Last._Myptr[v8 / 0xFFFFFD7C].m_usProtoTypeID )
    {
      std::swap<Item::ItemInfo>(v12, v14);
      v11 = _Last._Myptr;
    }
    if ( v11->m_usProtoTypeID < v12->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(v11, v12);
    if ( v12->m_usProtoTypeID < v14->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(v12, v14);
    if ( _Mid._Myptr->m_usProtoTypeID < v7->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(_Mid._Myptr, v7);
    if ( v12->m_usProtoTypeID < _Mid._Myptr->m_usProtoTypeID )
      std::swap<Item::ItemInfo>(v12, _Mid._Myptr);
    if ( _Mid._Myptr->m_usProtoTypeID < v7->m_usProtoTypeID )
LABEL_31:
      std::swap<Item::ItemInfo>(_Mid._Myptr, _First._Myptr);
  }
}

//----- (0043F370) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  int v2; // esi
  const Item::ItemInfo *v3; // ebx
  Item::ItemInfo v4; // [esp-284h] [ebp-294h] BYREF

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
    {
      --v3;
      --v2;
      Item::ItemInfo::ItemInfo(&v4, v3);
      std::_Adjust_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        _First,
        v2,
        _Last._Myptr - _First._Myptr,
        v4);
    }
    while ( v2 > 0 );
  }
}

//----- (0043F3E0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  int v2; // esi
  Item::ChemicalInfo *v3; // ebx

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
      std::_Adjust_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        _First,
        --v2,
        _Last._Myptr - _First._Myptr,
        *--v3);
    while ( v2 > 0 );
  }
}

//----- (0043F450) --------------------------------------------------------
void __cdecl std::_Pop_heap_0<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  Item::ItemInfo v2; // [esp-284h] [ebp-518h] BYREF
  Item::ItemInfo __that; // [esp+10h] [ebp-284h] BYREF

  Item::ItemInfo::ItemInfo(&__that, _Last._Myptr - 1);
  qmemcpy((void *)&_Last._Myptr[-1], _First._Myptr, sizeof(_Last._Myptr[-1]));
  Item::ItemInfo::ItemInfo(&v2, &__that);
  std::_Adjust_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
    _First,
    0,
    &_Last._Myptr[-1] - _First._Myptr,
    v2);
}

//----- (0043F4D0) --------------------------------------------------------
std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Item::ItemInfo>::iterator>(
        std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> *result,
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  Item::ItemInfo *v3; // ebx
  Item::ItemInfo *v4; // esi
  unsigned __int16 m_usProtoTypeID; // ax
  unsigned __int16 v6; // cx
  const Item::ItemInfo *v7; // edi
  Item::ItemInfo *v8; // ebp
  Item::ItemInfo *Myptr; // edx
  bool v10; // zf
  Item::ItemInfo *v11; // edi
  Item::ItemInfo *v12; // esi
  std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> *v13; // eax
  const Item::ItemInfo *v14; // [esp-4h] [ebp-11C0h]
  const Item::ItemInfo *v15; // [esp-4h] [ebp-11C0h]
  Item::ItemInfo *i; // [esp+10h] [ebp-11ACh]
  Item::ItemInfo *j; // [esp+14h] [ebp-11A8h]
  const Item::ItemInfo *v18; // [esp+18h] [ebp-11A4h]
  Item::ItemInfo *v19; // [esp+18h] [ebp-11A4h]
  Item::ItemInfo *v20; // [esp+18h] [ebp-11A4h]
  Item::ItemInfo v21; // [esp+1Ch] [ebp-11A0h] BYREF
  Item::ItemInfo v22; // [esp+2A0h] [ebp-F1Ch] BYREF
  Item::ItemInfo v23; // [esp+524h] [ebp-C98h] BYREF
  Item::ItemInfo v24; // [esp+7A8h] [ebp-A14h] BYREF
  Item::ItemInfo v25; // [esp+A2Ch] [ebp-790h] BYREF
  Item::ItemInfo v26; // [esp+CB0h] [ebp-50Ch] BYREF
  Item::ItemInfo v27; // [esp+F34h] [ebp-288h] BYREF

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<Item::ItemInfo>::iterator>(
    _First,
    (std::vector<Item::ItemInfo>::iterator)v3,
    (std::vector<Item::ItemInfo>::iterator)&_Last._Myptr[-1]);
  v4 = v3 + 1;
  for ( i = v3 + 1; _First._Myptr < v3; --v3 )
  {
    m_usProtoTypeID = v3[-1].m_usProtoTypeID;
    if ( v3->m_usProtoTypeID > m_usProtoTypeID )
      break;
    if ( v3->m_usProtoTypeID < m_usProtoTypeID )
      break;
  }
  if ( v4 < _Last._Myptr )
  {
    v6 = v3->m_usProtoTypeID;
    do
    {
      if ( v6 > v4->m_usProtoTypeID )
        break;
      if ( v6 < v4->m_usProtoTypeID )
        break;
      ++v4;
    }
    while ( v4 < _Last._Myptr );
    i = v4;
  }
  v7 = v3;
  v8 = v4;
  for ( j = v3; ; v7 = j )
  {
    while ( 1 )
    {
      for ( ; v8 < _Last._Myptr; ++v8 )
      {
        if ( v8->m_usProtoTypeID <= v3->m_usProtoTypeID )
        {
          if ( v8->m_usProtoTypeID < v3->m_usProtoTypeID )
            break;
          i = v4 + 1;
          Item::ItemInfo::ItemInfo(&v26, v4);
          qmemcpy((void *)v4, v8, sizeof(Item::ItemInfo));
          qmemcpy((void *)v8, &v26, sizeof(Item::ItemInfo));
          ++v4;
          v7 = j;
        }
      }
      Myptr = _First._Myptr;
      v10 = v7 == _First._Myptr;
      if ( v7 > _First._Myptr )
      {
        v18 = v7 - 1;
        do
        {
          if ( v3->m_usProtoTypeID <= v18->m_usProtoTypeID )
          {
            if ( v3->m_usProtoTypeID < v18->m_usProtoTypeID )
              break;
            Item::ItemInfo::ItemInfo(&v21, --v3);
            Myptr = _First._Myptr;
            qmemcpy((void *)v3, v18, sizeof(Item::ItemInfo));
            qmemcpy((void *)v18, &v21, sizeof(const Item::ItemInfo));
            v4 = i;
            v7 = j;
          }
          j = --v7;
          --v18;
        }
        while ( Myptr < v7 );
        v10 = v7 == Myptr;
      }
      if ( v10 )
        break;
      j = --v7;
      if ( v8 == _Last._Myptr )
      {
        if ( v7 != --v3 )
        {
          Item::ItemInfo::ItemInfo(&v23, v7);
          qmemcpy((void *)v7, v3, sizeof(const Item::ItemInfo));
          qmemcpy((void *)v3, &v23, sizeof(Item::ItemInfo));
          v4 = i;
        }
        i = v4 - 1;
        Item::ItemInfo::ItemInfo(&v25, v3);
        qmemcpy((void *)v3, &v4[-1], sizeof(Item::ItemInfo));
        qmemcpy((void *)&v4[-1], &v25, sizeof(Item::ItemInfo));
        --v4;
      }
      else
      {
        v15 = v8;
        v20 = v8++;
        Item::ItemInfo::ItemInfo(&v27, v15);
        qmemcpy((void *)v20, v7, sizeof(Item::ItemInfo));
        qmemcpy((void *)v7, &v27, sizeof(const Item::ItemInfo));
        v4 = i;
      }
    }
    if ( v8 == _Last._Myptr )
      break;
    if ( v4 != v8 )
    {
      Item::ItemInfo::ItemInfo(&v24, v3);
      qmemcpy((void *)v3, v4, sizeof(Item::ItemInfo));
      qmemcpy((void *)i, &v24, sizeof(Item::ItemInfo));
      v4 = i;
    }
    v11 = v3;
    i = v4 + 1;
    v12 = v8;
    v14 = v3;
    v19 = v8++;
    ++v3;
    Item::ItemInfo::ItemInfo(&v22, v14);
    qmemcpy((void *)v11, v12, sizeof(Item::ItemInfo));
    qmemcpy((void *)v19, &v22, sizeof(Item::ItemInfo));
    v4 = i;
  }
  v13 = result;
  result->second._Myptr = v4;
  result->first._Myptr = v3;
  return v13;
}

//----- (0043F7F0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<Item::ItemInfo>::iterator>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  Item::ItemInfo *i; // esi
  unsigned __int16 m_usProtoTypeID; // cx
  Item::ItemInfo *v4; // eax
  std::vector<Item::ItemInfo>::iterator v5; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_usProtoTypeID = i->m_usProtoTypeID;
      if ( i->m_usProtoTypeID >= _First._Myptr->m_usProtoTypeID )
      {
        v4 = i - 1;
        if ( m_usProtoTypeID < i[-1].m_usProtoTypeID )
        {
          do
            v5._Myptr = v4--;
          while ( m_usProtoTypeID < v4->m_usProtoTypeID );
          if ( v5._Myptr != i )
            std::_Rotate<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
              v5,
              (std::vector<Item::ItemInfo>::iterator)i,
              (std::vector<Item::ItemInfo>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
          _First,
          (std::vector<Item::ItemInfo>::iterator)i,
          (std::vector<Item::ItemInfo>::iterator)&i[1]);
      }
    }
  }
}

//----- (0043F880) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<Item::ChemicalInfo>::iterator>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  Item::ChemicalInfo *i; // esi
  unsigned __int16 m_wPickkingItemID; // dx
  bool v4; // cf
  std::vector<Item::ChemicalInfo>::iterator v5; // edi
  Item::ChemicalInfo *j; // eax
  unsigned __int16 v7; // cx
  bool v8; // cf

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_wPickkingItemID = i->m_wPickkingItemID;
      v4 = i->m_wPickkingItemID < _First._Myptr->m_wPickkingItemID;
      if ( i->m_wPickkingItemID == _First._Myptr->m_wPickkingItemID )
        v4 = i->m_wTargetItemID < _First._Myptr->m_wTargetItemID;
      if ( v4 )
      {
        if ( _First._Myptr != i && i != &i[1] )
          std::_Rotate<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
            _First,
            (std::vector<Item::ChemicalInfo>::iterator)i,
            (std::vector<Item::ChemicalInfo>::iterator)&i[1]);
      }
      else
      {
        v5._Myptr = i;
        for ( j = i; ; v5._Myptr = j )
        {
          v7 = j[-1].m_wPickkingItemID;
          --j;
          v8 = m_wPickkingItemID < v7;
          if ( m_wPickkingItemID == v7 )
            v8 = i->m_wTargetItemID < j->m_wTargetItemID;
          if ( !v8 )
            break;
        }
        if ( v5._Myptr != i )
          std::_Rotate<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
            v5,
            (std::vector<Item::ChemicalInfo>::iterator)i,
            (std::vector<Item::ChemicalInfo>::iterator)&i[1]);
      }
    }
  }
}

//----- (0043F920) --------------------------------------------------------
Item::ItemInfo *__thiscall std::vector<Item::ItemInfo>::_Ufill(
        std::vector<Item::ItemInfo> *this,
        Item::ItemInfo *_Ptr,
        unsigned int _Count,
        const Item::ItemInfo *_Val)
{
  std::_Uninit_fill_n<Item::ItemInfo *,unsigned int,Item::ItemInfo,std::allocator<Item::ItemInfo>>(_Ptr, _Count, _Val);
  return &_Ptr[_Count];
}

//----- (0043F950) --------------------------------------------------------
Item::ChemicalInfo *__thiscall std::vector<Item::ChemicalInfo>::_Ufill(
        std::vector<Item::ChemicalInfo> *this,
        Item::ChemicalInfo *_Ptr,
        unsigned int _Count,
        const Item::ChemicalInfo *_Val)
{
  std::_Uninit_fill_n<Item::ChemicalInfo *,unsigned int,Item::ChemicalInfo,std::allocator<Item::ChemicalInfo>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (0043F980) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Item::ItemInfo>::iterator>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  Item::ItemInfo *i; // esi

  for ( i = _Last._Myptr; i - _First._Myptr > 1; --i )
    std::_Pop_heap_0<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo>(
      _First,
      (std::vector<Item::ItemInfo>::iterator)i);
}

//----- (0043F9E0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Item::ChemicalInfo>::iterator>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  int i; // esi
  char *v3; // eax
  unsigned int v4; // ecx
  unsigned int v5; // edx
  Item::ChemicalInfo v6; // [esp-Ch] [ebp-28h]
  __int16 v7; // [esp+1Ah] [ebp-2h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 10 > 1; i -= 10 )
  {
    v3 = (char *)&_First._Myptr[-1] + i;
    v4 = *(_DWORD *)v3;
    v5 = *((_DWORD *)v3 + 1);
    v7 = *((_WORD *)v3 + 4);
    *(_DWORD *)v3 = *(_DWORD *)&_First._Myptr->m_wPickkingItemID;
    *((_DWORD *)v3 + 1) = *(_DWORD *)&_First._Myptr->m_wResultItemID;
    *((_WORD *)v3 + 4) = *(_WORD *)&_First._Myptr->m_cResultItemNum;
    *(_QWORD *)&v6.m_wPickkingItemID = __PAIR64__(v5, v4);
    *(_WORD *)&v6.m_cResultItemNum = v7;
    std::_Adjust_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(_First, 0, (i - 10) / 10, v6);
  }
}

//----- (0043FA90) --------------------------------------------------------
void __thiscall __noreturn std::vector<ItemDataParser::ParseData>::_Xlen(std::vector<Item::ChemicalInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0043FB00) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<Item::ItemInfo>::iterator,int>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last,
        int _Ideal)
{
  Item::ItemInfo *Myptr; // ebx
  Item::ItemInfo *v4; // edi
  int v5; // eax
  std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<Item::ItemInfo>::iterator>(
        (std::vector<Item::ItemInfo>::iterator)Myptr,
        (std::vector<Item::ItemInfo>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<Item::ItemInfo>::iterator>(
        &_Mid,
        (std::vector<Item::ItemInfo>::iterator)Myptr,
        (std::vector<Item::ItemInfo>::iterator)v4);
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<Item::ItemInfo>::iterator,int>(
          _Mid.second,
          (std::vector<Item::ItemInfo>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<Item::ItemInfo>::iterator,int>(
          (std::vector<Item::ItemInfo>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = _Mid.second._Myptr;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(
        (std::vector<Item::ItemInfo>::iterator)Myptr,
        (std::vector<Item::ItemInfo>::iterator)v4);
    std::sort_heap<std::vector<Item::ItemInfo>::iterator>(
      (std::vector<Item::ItemInfo>::iterator)Myptr,
      (std::vector<Item::ItemInfo>::iterator)v4);
  }
}
// 43FBF0: conditional instruction was optimized away because eax.4>=21

//----- (0043FC30) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<Item::ChemicalInfo>::iterator,int>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last,
        int _Ideal)
{
  Item::ChemicalInfo *Myptr; // ebx
  Item::ChemicalInfo *v4; // edi
  int v5; // eax
  Item::ChemicalInfo *v7; // ebp
  std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<Item::ChemicalInfo>::iterator>(
        (std::vector<Item::ChemicalInfo>::iterator)Myptr,
        (std::vector<Item::ChemicalInfo>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<Item::ChemicalInfo>::iterator>(
        &_Mid,
        (std::vector<Item::ChemicalInfo>::iterator)Myptr,
        (std::vector<Item::ChemicalInfo>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<Item::ChemicalInfo>::iterator,int>(
          _Mid.second,
          (std::vector<Item::ChemicalInfo>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<Item::ChemicalInfo>::iterator,int>(
          (std::vector<Item::ChemicalInfo>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(
        (std::vector<Item::ChemicalInfo>::iterator)Myptr,
        (std::vector<Item::ChemicalInfo>::iterator)v4);
    std::sort_heap<std::vector<Item::ChemicalInfo>::iterator>(
      (std::vector<Item::ChemicalInfo>::iterator)Myptr,
      (std::vector<Item::ChemicalInfo>::iterator)v4);
  }
}
// 43FD1A: conditional instruction was optimized away because eax.4>=21

//----- (0043FD60) --------------------------------------------------------
void __thiscall std::vector<Item::ItemInfo>::_Insert_n(
        std::vector<Item::ItemInfo> *this,
        std::vector<Item::ItemInfo>::iterator _Where,
        unsigned int _Count,
        const Item::ItemInfo *_Val)
{
  Item::ItemInfo *Myfirst; // ebx
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // ebx
  Item::ItemInfo *v12; // eax
  Item::ItemInfo *v13; // ecx
  Item::ItemInfo *v14; // eax
  char *v15; // edi
  Item::ItemInfo *v16; // eax
  Item::ItemInfo *v17; // edi
  Item::ItemInfo *Mylast; // ecx
  Item::ItemInfo *v20; // edx
  Item::ItemInfo *v21; // [esp-Ch] [ebp-2B8h]
  unsigned int v22; // [esp-8h] [ebp-2B4h]
  int v23; // [esp+0h] [ebp-2ACh] BYREF
  Item::ItemInfo *_Ptr; // [esp+Ch] [ebp-2A0h]
  Item::ItemInfo *_Newvec; // [esp+10h] [ebp-29Ch]
  Item::ItemInfo _Tmp; // [esp+14h] [ebp-298h] BYREF
  int *v27; // [esp+29Ch] [ebp-10h]
  int v28; // [esp+2A8h] [ebp-4h]
  Item::ItemInfo *_Wherea; // [esp+2B4h] [ebp+8h]
  Item::ItemInfo *_Vala; // [esp+2BCh] [ebp+10h]

  v27 = &v23;
  Item::ItemInfo::ItemInfo(&_Tmp, _Val);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - this->_Myfirst;
    else
      v7 = 0;
    if ( 6669203 - v7 < _Count )
      std::vector<ItemDataParser::ParseData>::_Xlen((std::vector<Item::ChemicalInfo> *)this);
    if ( this->_Myfirst )
      v8 = this->_Mylast - this->_Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<Item::ItemInfo *,Item::ItemInfo *>(_Where._Myptr, _Wherea, _Vala);
        std::fill<Item::ItemInfo *,Item::ItemInfo>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<Item::ItemInfo>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Item::ItemInfo *,Item::ItemInfo>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 6669203 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( this->_Myfirst )
        v10 = this->_Mylast - this->_Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<Item::ItemInfo>::size(this) + _Count;
      v11 = v9;
      v12 = (Item::ItemInfo *)operator new((tagHeader *)(644 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<Item::ItemInfo *,unsigned int,Item::ItemInfo,std::allocator<Item::ItemInfo>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (Item::ItemInfo *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete((void *)this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (00440050) --------------------------------------------------------
void __thiscall std::vector<Item::ChemicalInfo>::_Insert_n(
        std::vector<Item::ChemicalInfo> *this,
        std::vector<Item::ChemicalInfo>::iterator _Where,
        unsigned int _Count,
        const Item::ChemicalInfo *_Val)
{
  int v5; // ecx
  int v6; // edx
  Item::ChemicalInfo *Myfirst; // ebx
  __int16 v8; // ax
  unsigned int v9; // ecx
  int v11; // eax
  int v12; // eax
  unsigned int v13; // ecx
  int v14; // eax
  int v15; // ebx
  Item::ChemicalInfo *v16; // eax
  char *v17; // edi
  Item::ChemicalInfo *Mylast; // ecx
  Item::ChemicalInfo *v20; // edi
  Item::ChemicalInfo *v21; // [esp-18h] [ebp-40h]
  Item::ChemicalInfo *v22; // [esp-Ch] [ebp-34h]
  unsigned int v23; // [esp-8h] [ebp-30h]
  int v24; // [esp+0h] [ebp-28h] BYREF
  Item::ChemicalInfo _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  int *v26; // [esp+18h] [ebp-10h]
  int v27; // [esp+24h] [ebp-4h]
  Item::ChemicalInfo *_Wherea; // [esp+30h] [ebp+8h]
  Item::ChemicalInfo *_Counta; // [esp+34h] [ebp+Ch]
  Item::ChemicalInfo *_Newvec; // [esp+38h] [ebp+10h]
  Item::ChemicalInfo *_Newveca; // [esp+38h] [ebp+10h]

  v5 = *(_DWORD *)&_Val->m_wPickkingItemID;
  v6 = *(_DWORD *)&_Val->m_wResultItemID;
  Myfirst = this->_Myfirst;
  v8 = *(_WORD *)&_Val->m_cResultItemNum;
  v26 = &v24;
  *(_DWORD *)&_Tmp.m_wPickkingItemID = v5;
  *(_DWORD *)&_Tmp.m_wResultItemID = v6;
  *(_WORD *)&_Tmp.m_cResultItemNum = v8;
  if ( Myfirst )
    v9 = this->_Myend - Myfirst;
  else
    v9 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( 429496729 - v11 < _Count )
      std::vector<ItemDataParser::ParseData>::_Xlen(this);
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( v9 >= _Count + v12 )
    {
      Mylast = this->_Mylast;
      _Newveca = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::_Copy_backward_opt<Item::ChemicalInfo *,Item::ChemicalInfo *>(_Where._Myptr, _Wherea, _Newveca);
        std::fill<Item::ChemicalInfo *,Item::ChemicalInfo>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = this->_Mylast;
        v27 = 2;
        std::vector<Item::ChemicalInfo>::_Ufill(this, v22, v23, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Item::ChemicalInfo *,Item::ChemicalInfo>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 429496729 - (v9 >> 1) >= v9 )
        v13 = (v9 >> 1) + v9;
      else
        v13 = 0;
      if ( Myfirst )
        v14 = this->_Mylast - Myfirst;
      else
        v14 = 0;
      if ( v13 < _Count + v14 )
        v13 = (unsigned int)std::vector<Item::ChemicalInfo>::size(this) + _Count;
      v15 = v13;
      _Newvec = (Item::ChemicalInfo *)operator new((tagHeader *)(10 * v13));
      v21 = this->_Myfirst;
      v27 = 0;
      _Counta = std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(
                  v21,
                  _Where._Myptr,
                  _Newvec);
      std::_Uninit_fill_n<Item::ChemicalInfo *,unsigned int,Item::ChemicalInfo,std::allocator<Item::ChemicalInfo>>(
        _Counta,
        _Count,
        &_Tmp);
      std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(
        _Where._Myptr,
        this->_Mylast,
        &_Counta[_Count]);
      v16 = this->_Myfirst;
      if ( v16 )
        v16 = (Item::ChemicalInfo *)(this->_Mylast - v16);
      v17 = (char *)v16 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[v15];
      this->_Mylast = &_Newvec[(_DWORD)v17];
      this->_Myfirst = _Newvec;
    }
  }
}

//----- (00440310) --------------------------------------------------------
void __cdecl std::sort<std::vector<Item::ItemInfo>::iterator>(
        std::vector<Item::ItemInfo>::iterator _First,
        std::vector<Item::ItemInfo>::iterator _Last)
{
  std::_Sort<std::vector<Item::ItemInfo>::iterator,int>(_First, _Last, _Last._Myptr - _First._Myptr);
}

//----- (00440340) --------------------------------------------------------
void __cdecl std::sort<std::vector<Item::ChemicalInfo>::iterator>(
        std::vector<Item::ChemicalInfo>::iterator _First,
        std::vector<Item::ChemicalInfo>::iterator _Last)
{
  std::_Sort<std::vector<Item::ChemicalInfo>::iterator,int>(_First, _Last, _Last._Myptr - _First._Myptr);
}

//----- (00440370) --------------------------------------------------------
std::vector<Item::ItemInfo>::iterator *__thiscall std::vector<Item::ItemInfo>::insert(
        std::vector<Item::ItemInfo> *this,
        std::vector<Item::ItemInfo>::iterator *result,
        std::vector<Item::ItemInfo>::iterator _Where,
        const Item::ItemInfo *_Val)
{
  Item::ItemInfo *Myfirst; // esi
  int v6; // esi
  std::vector<Item::ItemInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<Item::ItemInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (004403E0) --------------------------------------------------------
std::vector<Item::ChemicalInfo>::iterator *__thiscall std::vector<Item::ChemicalInfo>::insert(
        std::vector<Item::ChemicalInfo> *this,
        std::vector<Item::ChemicalInfo>::iterator *result,
        std::vector<Item::ChemicalInfo>::iterator _Where,
        const Item::ChemicalInfo *_Val)
{
  Item::ChemicalInfo *Myfirst; // esi
  int v6; // esi
  std::vector<Item::ChemicalInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<Item::ChemicalInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (00440450) --------------------------------------------------------
void __thiscall std::vector<Item::ItemInfo>::push_back(std::vector<Item::ItemInfo> *this, const Item::ItemInfo *_Val)
{
  Item::ItemInfo *Myfirst; // ebx
  unsigned int v4; // esi
  Item::ItemInfo *Mylast; // esi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Item::ItemInfo *,unsigned int,Item::ItemInfo,std::allocator<Item::ItemInfo>>(Mylast, 1u, _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Item::ItemInfo>::insert(
      this,
      (std::vector<Item::ItemInfo>::iterator *)&_Val,
      (std::vector<Item::ItemInfo>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (004404E0) --------------------------------------------------------
void __thiscall std::vector<Item::ChemicalInfo>::push_back(
        std::vector<Item::ChemicalInfo> *this,
        const Item::ChemicalInfo *_Val)
{
  Item::ChemicalInfo *Myfirst; // edi
  unsigned int v4; // ecx
  Item::ChemicalInfo *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Item::ChemicalInfo *,unsigned int,Item::ChemicalInfo,std::allocator<Item::ChemicalInfo>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Item::ChemicalInfo>::insert(
      this,
      (std::vector<Item::ChemicalInfo>::iterator *)&_Val,
      (std::vector<Item::ChemicalInfo>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (00440570) --------------------------------------------------------
char __thiscall Item::CItemMgr::LoadItemProtoType(Item::CItemMgr *this, char *szFileName)
{
  Item::ItemInfo *m_ItemInfoArray; // eax
  char *v4; // eax
  Item::CItemType *v6; // esi
  Item::ItemInfo *Myfirst; // edi
  Item::ItemInfo *Mylast; // ebp
  unsigned __int16 *p_m_usProtoTypeID; // eax
  int v10; // esi
  CTokenlizedFile *v11; // eax
  Item::ItemInfo *v12; // ebp
  Item::ItemInfo *v13; // eax
  CParseDelimitedData v14; // [esp+8h] [ebp-8428h] BYREF
  std::vector<Item::ItemInfo> v15; // [esp+Ch] [ebp-8424h] BYREF
  std::vector<ItemDataParser::ParseData> v16; // [esp+1Ch] [ebp-8414h] BYREF
  std::vector<ItemDataParser::ParseData> parseDataArray; // [esp+2Ch] [ebp-8404h] BYREF
  std::vector<ItemDataParser::ParseData> v18; // [esp+3Ch] [ebp-83F4h] BYREF
  std::vector<ItemDataParser::ParseData> ParserArray; // [esp+4Ch] [ebp-83E4h] BYREF
  Item::CItemMgr *v20; // [esp+5Ch] [ebp-83D4h]
  Item::ItemInfo itemInfo; // [esp+60h] [ebp-83D0h] BYREF
  char Destination[260]; // [esp+2E4h] [ebp-814Ch] BYREF
  CTokenlizedFile v23; // [esp+3E8h] [ebp-8048h] BYREF
  int v24; // [esp+842Ch] [ebp-4h]

  m_ItemInfoArray = this->m_ItemInfoArray;
  v20 = this;
  if ( m_ItemInfoArray )
  {
    operator delete[]((void *)m_ItemInfoArray);
    this->m_ItemInfoArray = 0;
  }
  this->m_nItemNum = 0;
  Item::ItemInfo::ItemInfo(&itemInfo);
  CTokenlizedFile::CTokenlizedFile(&v23, "\t");
  v24 = 0;
  if ( CServerSetup::GetInstance()->m_bBattleGame )
    strncpy(Destination, Item::CItemMgr::ms_szBGItemScriptFileName, 0x104u);
  else
    strncpy(Destination, Item::CItemMgr::ms_szItemScriptFileName, 0x104u);
  v4 = szFileName;
  if ( !szFileName )
    v4 = Destination;
  if ( !CTokenlizedFile::Open(&v23, v4, "rt") )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "Item::CItemMgr::LoadItemProtoType", aDWorkRylSource_66, 128, byte_4DE9B0);
LABEL_12:
    v24 = -1;
    CTokenlizedFile::~CTokenlizedFile(&v23);
    return 0;
  }
  if ( !CTokenlizedFile::ReadColumn(&v23) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CItemMgr::LoadItemProtoType",
      aDWorkRylSource_66,
      134,
      (char *)&byte_4DE97C);
    goto LABEL_12;
  }
  memset(&parseDataArray._Myfirst, 0, 12);
  memset(&ParserArray._Myfirst, 0, 12);
  memset(&v16._Myfirst, 0, 12);
  memset(&v18._Myfirst, 0, 12);
  LOBYTE(v24) = 4;
  ItemDataParser::SetDefaultData(&parseDataArray);
  ItemDataParser::SetEquipData(&ParserArray);
  ItemDataParser::SetUseItemData(&v16);
  ItemDataParser::SetEtcItemData(&v18);
  v6 = CSingleton<Item::CItemType>::ms_pSingleton;
  Myfirst = 0;
  Mylast = 0;
  memset(&v15._Myfirst, 0, 12);
  LOBYTE(v24) = 5;
  v14.m_TokenlizedFile = &v23;
  if ( CTokenlizedFile::ReadLine(&v23) )
  {
    while ( 1 )
    {
      Item::ItemInfo::Initialize(&itemInfo);
      if ( !CParseDelimitedData::operator()(&v14, &parseDataArray, &itemInfo) )
        goto LABEL_40;
      if ( Item::CItemType::IsCorrectItemType(v6, EQUIP_TYPE, itemInfo.m_DetailData.m_cItemType) )
      {
        if ( !CParseDelimitedData::operator()(&v14, &ParserArray, &itemInfo) )
          goto LABEL_40;
        Item::CItemType::SetEquipTypeFlags(v6, &itemInfo);
      }
      else if ( Item::CItemType::IsCorrectItemType(v6, USE_ITEM_TYPE, itemInfo.m_DetailData.m_cItemType) )
      {
        if ( !CParseDelimitedData::operator()(&v14, &v16, &itemInfo) )
          goto LABEL_40;
        Item::CItemType::SetUseItemTypeFlags(v6, &itemInfo);
      }
      else
      {
        if ( !CParseDelimitedData::operator()(&v14, &v18, &itemInfo) )
          goto LABEL_40;
        Item::CItemType::SetEtcItemTypeFlags(v6, &itemInfo);
      }
      std::vector<Item::ItemInfo>::push_back(&v15, &itemInfo);
      if ( !CTokenlizedFile::ReadLine(&v23) )
      {
        Myfirst = v15._Myfirst;
        Mylast = v15._Mylast;
        break;
      }
    }
  }
  if ( !v23.m_lpFile || (v23.m_lpFile->_flag & 0x10) == 0 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "Item::CItemMgr::LoadItemProtoType", aDWorkRylSource_66, 198, byte_4DE924);
LABEL_40:
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v15);
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v18);
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v16);
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&ParserArray);
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&parseDataArray);
    v24 = -1;
    CTokenlizedFile::~CTokenlizedFile(&v23);
    return 0;
  }
  std::sort<std::vector<Item::ItemInfo>::iterator>(
    (std::vector<Item::ItemInfo>::iterator)Myfirst,
    (std::vector<Item::ItemInfo>::iterator)Mylast);
  p_m_usProtoTypeID = &Myfirst->m_usProtoTypeID;
  if ( Myfirst != &Mylast[-1] )
  {
    while ( *p_m_usProtoTypeID != p_m_usProtoTypeID[322] )
    {
      p_m_usProtoTypeID += 322;
      if ( p_m_usProtoTypeID == (unsigned __int16 *)&Mylast[-1] )
        goto LABEL_31;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CItemMgr::LoadItemProtoType",
      aDWorkRylSource_66,
      209,
      (char *)&byte_4DE8F8,
      *p_m_usProtoTypeID);
    goto LABEL_40;
  }
LABEL_31:
  if ( Myfirst )
    v10 = Mylast - Myfirst;
  else
    v10 = 0;
  v20->m_nItemNum = v10;
  v11 = (CTokenlizedFile *)operator new[](644 * v10);
  v12 = (Item::ItemInfo *)v11;
  v14.m_TokenlizedFile = v11;
  LOBYTE(v24) = 6;
  if ( v11 )
  {
    `vector constructor iterator'((char *)v11, 0x284u, v10, (void *(__thiscall *)(void *))Item::ItemInfo::ItemInfo);
    v13 = v12;
  }
  else
  {
    v13 = 0;
  }
  LOBYTE(v24) = 5;
  v20->m_ItemInfoArray = v13;
  if ( !v13 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CItemMgr::LoadItemProtoType",
      aDWorkRylSource_66,
      218,
      (char *)&byte_4DE8B8);
    goto LABEL_40;
  }
  std::copy<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo *>(
    (std::vector<Item::ItemInfo>::iterator)Myfirst,
    (std::vector<Item::ItemInfo>::iterator)v15._Mylast,
    v13);
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v15);
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v18);
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v16);
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&ParserArray);
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&parseDataArray);
  v24 = -1;
  CTokenlizedFile::~CTokenlizedFile(&v23);
  return 1;
}

//----- (00440A00) --------------------------------------------------------
char __thiscall Item::CItemMgr::LoadItemChemical(Item::CItemMgr *this, char *szFileName)
{
  char *v3; // edi
  const char *v4; // ebp
  const char *v5; // eax
  Item::ChemicalInfo *Mylast; // ebx
  unsigned int v8; // edi
  char *v9; // eax
  Item::ChemicalInfo *v10; // ebp
  char *v11; // edi
  unsigned __int16 m_wPickkingItemID; // bx
  const Item::ItemInfo *ItemInfo; // ebp
  const Item::ItemInfo *v14; // edi
  const Item::ItemInfo *v15; // eax
  Item::ChemicalInfo _Val; // [esp+Ch] [ebp-809Ch] BYREF
  std::vector<Item::ChemicalInfo> v17; // [esp+18h] [ebp-8090h] BYREF
  char *v18; // [esp+28h] [ebp-8080h]
  char szString[32]; // [esp+2Ch] [ebp-807Ch] BYREF
  CDelimitedFile v20; // [esp+4Ch] [ebp-805Ch] BYREF
  int v21; // [esp+80A4h] [ebp-4h]

  v3 = 0;
  CDelimitedFile::CDelimitedFile(&v20, "\t");
  v4 = szFileName;
  v21 = 0;
  v5 = szFileName;
  if ( !szFileName )
    v5 = Item::CItemMgr::ms_szChemicalScriptFileName;
  if ( CDelimitedFile::Open(&v20, v5, 2, 0) )
  {
    Mylast = 0;
    memset(&v17._Myfirst, 0, 12);
    LOBYTE(v21) = 1;
    Item::ChemicalInfo::ChemicalInfo(&_Val);
    if ( CDelimitedFile::ReadLine(&v20) )
    {
      while ( 1 )
      {
        v11 = v3 + 1;
        v18 = v11;
        if ( !CDelimitedFile::ReadString(&v20, szString, 0x20u) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            458,
            (char *)&byte_4DEB48,
            v11,
            "\"PickkingItem Name\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (__int16 *)&_Val) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            459,
            (char *)&byte_4DEB48,
            v11,
            "\"PickkingItem ID\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (char *)&_Val.m_cPickkingItemNum) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            460,
            (char *)&byte_4DEB48,
            v11,
            "\"PickkingItem Num\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadString(&v20, szString, 0x20u) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            462,
            (char *)&byte_4DEB48,
            v11,
            "\"TargetItem Name\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (__int16 *)&_Val.m_wTargetItemID) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            463,
            (char *)&byte_4DEB48,
            v11,
            "\"TargetItem ID\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (char *)&_Val.m_cTargetItemNum) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            464,
            (char *)&byte_4DEB48,
            v11,
            "\"TargetItem Num\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadString(&v20, szString, 0x20u) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            466,
            (char *)&byte_4DEB48,
            v11,
            "\"ResultItem Name\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (__int16 *)&_Val.m_wResultItemID) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            467,
            (char *)&byte_4DEB48,
            v11,
            "\"ResultItem ID\"");
          goto LABEL_28;
        }
        if ( !CDelimitedFile::ReadData(&v20, (char *)&_Val.m_cResultItemNum) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            468,
            (char *)&byte_4DEB48,
            v11,
            "\"ResultItem Num\"");
          goto LABEL_28;
        }
        m_wPickkingItemID = _Val.m_wPickkingItemID;
        ItemInfo = Item::CItemMgr::GetItemInfo(this, _Val.m_wPickkingItemID);
        v14 = Item::CItemMgr::GetItemInfo(this, _Val.m_wTargetItemID);
        v15 = Item::CItemMgr::GetItemInfo(this, _Val.m_wResultItemID);
        if ( !ItemInfo || !v14 || !v15 )
          break;
        if ( v14->m_DetailData.m_cXSize < v15->m_DetailData.m_cXSize
          || v14->m_DetailData.m_cYSize < v15->m_DetailData.m_cYSize )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CItemMgr::LoadItemChemical",
            aDWorkRylSource_66,
            488,
            (char *)&byte_4DEA50,
            _Val.m_wTargetItemID,
            v14->m_DetailData.m_cXSize,
            v14->m_DetailData.m_cYSize,
            _Val.m_wResultItemID,
            v15->m_DetailData.m_cXSize,
            v15->m_DetailData.m_cYSize);
          goto LABEL_28;
        }
        std::vector<Item::ChemicalInfo>::push_back(&v17, &_Val);
        if ( !CDelimitedFile::ReadLine(&v20) )
        {
          Mylast = v17._Mylast;
          goto LABEL_8;
        }
        v3 = v18;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CItemMgr::LoadItemChemical",
        aDWorkRylSource_66,
        478,
        (char *)&byte_4DE9E0,
        m_wPickkingItemID,
        _Val.m_wTargetItemID,
        _Val.m_wResultItemID);
    }
    else
    {
LABEL_8:
      std::sort<std::vector<Item::ChemicalInfo>::iterator>(
        (std::vector<Item::ChemicalInfo>::iterator)v17._Myfirst,
        (std::vector<Item::ChemicalInfo>::iterator)Mylast);
      v8 = (unsigned int)std::vector<Item::ChemicalInfo>::size(&v17);
      this->m_nChemicalNum = v8;
      v9 = (char *)operator new[](10 * v8);
      v10 = (Item::ChemicalInfo *)v9;
      v18 = v9;
      LOBYTE(v21) = 2;
      if ( v9 )
        `vector constructor iterator'(v9, 0xAu, v8, (void *(__thiscall *)(void *))Item::ChemicalInfo::ChemicalInfo);
      else
        v10 = 0;
      LOBYTE(v21) = 1;
      this->m_ChemicalArray = v10;
      if ( v10 )
      {
        std::copy<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(
          (std::vector<Item::ChemicalInfo>::iterator)v17._Myfirst,
          (std::vector<Item::ChemicalInfo>::iterator)Mylast,
          v10);
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v17);
        v21 = -1;
        CDelimitedFile::~CDelimitedFile(&v20);
        return 1;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CItemMgr::LoadItemChemical",
        aDWorkRylSource_66,
        501,
        (char *)&byte_4DD88C);
    }
LABEL_28:
    std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v17);
    v21 = -1;
    CDelimitedFile::~CDelimitedFile(&v20);
    return 0;
  }
  if ( !szFileName )
    v4 = Item::CItemMgr::ms_szChemicalScriptFileName;
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "Item::CItemMgr::LoadItemChemical", aDWorkRylSource_66, 447, aS_3, v4);
  v21 = -1;
  CDelimitedFile::~CDelimitedFile(&v20);
  return 0;
}

//----- (00440E90) --------------------------------------------------------
char __thiscall CGameEventMgr::Initialize(CGameEventMgr *this)
{
  if ( CLotteryEvent::Initialize(&this->m_LotteryEvent) )
    return 1;
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameEventMgr::Initialize", aDWorkRylSource_7, 22, (char *)&byte_4DEC74);
  return 0;
}

//----- (00440ED0) --------------------------------------------------------
void __thiscall CSingleton<CGameEventMgr>::~CSingleton<CGameEventMgr>(CSingleton<CGameEventMgr> *this)
{
  CSingleton<CGameEventMgr>::ms_pSingleton = 0;
}

//----- (00440EE0) --------------------------------------------------------
void __thiscall CGameEventMgr::CGameEventMgr(CGameEventMgr *this)
{
  CLotteryEvent *p_m_LotteryEvent; // ecx

  p_m_LotteryEvent = &this->m_LotteryEvent;
  CSingleton<CGameEventMgr>::ms_pSingleton = (CGameEventMgr *)&p_m_LotteryEvent[-1].m_aryItem._Myend;
  this->__vftable = (CGameEventMgr_vtbl *)&CGameEventMgr::`vftable';
  CLotteryEvent::CLotteryEvent(p_m_LotteryEvent);
  this->m_lstDropEventItem._Myhead = (std::_List_nod<unsigned short>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_lstDropEventItem);
  this->m_lstDropEventItem._Mysize = 0;
}
// 4DEC9C: using guessed type void *CGameEventMgr::`vftable';

//----- (00440F50) --------------------------------------------------------
void __thiscall CGameEventMgr::~CGameEventMgr(CGameEventMgr *this)
{
  std::list<unsigned short> *p_m_lstDropEventItem; // edi

  this->__vftable = (CGameEventMgr_vtbl *)&CGameEventMgr::`vftable';
  p_m_lstDropEventItem = &this->m_lstDropEventItem;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_lstDropEventItem);
  operator delete(p_m_lstDropEventItem->_Myhead);
  p_m_lstDropEventItem->_Myhead = 0;
  CLotteryEvent::~CLotteryEvent(&this->m_LotteryEvent);
  CSingleton<CGameEventMgr>::ms_pSingleton = 0;
}
// 4DEC9C: using guessed type void *CGameEventMgr::`vftable';

//----- (00440FC0) --------------------------------------------------------
CGameEventMgr *__thiscall CGameEventMgr::`vector deleting destructor'(CGameEventMgr *this, char a2)
{
  CGameEventMgr::~CGameEventMgr(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00440FE0) --------------------------------------------------------
int __thiscall CLogSaveThread::End(CLogSaveThread *this)
{
  InterlockedExchange(&this->m_bEnd, 1);
  SetEvent(this->m_hFlush);
  return 1;
}

//----- (00441000) --------------------------------------------------------
char __thiscall CLogSaveThread::Compress(CLogSaveThread *this)
{
  char *m_szLogFileName; // ebp
  HANDLE FileA; // edi
  int v4; // esi
  _DWORD *v5; // ebx
  DWORD LastError; // eax
  DWORD v7; // esi
  unsigned int NumberOfBytesRead; // [esp+8h] [ebp-1524h] BYREF
  _SYSTEMTIME SystemTime; // [esp+Ch] [ebp-1520h] BYREF
  char drive[4]; // [esp+1Ch] [ebp-1510h] BYREF
  char fname[256]; // [esp+20h] [ebp-150Ch] BYREF
  char dir[256]; // [esp+120h] [ebp-140Ch] BYREF
  char string[520]; // [esp+220h] [ebp-130Ch] BYREF
  char ext[256]; // [esp+428h] [ebp-1104h] BYREF
  unsigned __int8 Buffer[4096]; // [esp+528h] [ebp-1004h] BYREF

  m_szLogFileName = this->m_szLogFileName;
  FileA = CreateFileA(this->m_szLogFileName, 0x80000000, 1u, 0, 3u, 0x80u, 0);
  if ( FileA == (HANDLE)-1 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLogSaveThread::Compress", aDWorkRylSource_37, 121, (char *)&byte_4DEE48);
    return 0;
  }
  GetLocalTime(&SystemTime);
  v4 = 0;
  _splitpath(m_szLogFileName, drive, dir, fname, ext);
  _snprintf(
    string,
    0x208u,
    "%s\\%s-%04d%02d%02d-%02d%02d%02d-%04d.zip",
    dir,
    fname,
    SystemTime.wYear,
    SystemTime.wMonth,
    SystemTime.wDay,
    SystemTime.wHour,
    SystemTime.wMinute,
    SystemTime.wSecond,
    0);
  while ( GetFileAttributesA(string) != -1 )
    _snprintf(
      string,
      0x208u,
      "%s\\%s-%04d%02d%02d-%02d%02d%02d-%04d.zip",
      dir,
      fname,
      SystemTime.wYear,
      SystemTime.wMonth,
      SystemTime.wDay,
      SystemTime.wHour,
      SystemTime.wMinute,
      SystemTime.wSecond,
      ++v4);
  v5 = gzopen(string, "wb");
  if ( !v5 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLogSaveThread::Compress", aDWorkRylSource_37, 155, (char *)&byte_4DED54);
    CloseHandle(FileA);
    return 0;
  }
  NumberOfBytesRead = 0;
  if ( ReadFile(FileA, Buffer, 0x1000u, &NumberOfBytesRead, 0) )
  {
    while ( NumberOfBytesRead )
    {
      if ( !gzwrite(v5, Buffer, NumberOfBytesRead) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CLogSaveThread::Compress",
          aDWorkRylSource_37,
          172,
          (char *)&byte_4DED28);
        break;
      }
      if ( !ReadFile(FileA, Buffer, 0x1000u, &NumberOfBytesRead, 0) )
        break;
    }
  }
  gzclose(v5);
  CloseHandle(FileA);
  LastError = GetLastError();
  v7 = LastError;
  if ( LastError && LastError != 38 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLogSaveThread::Compress", aDWorkRylSource_37, 183, aD_0, LastError);
    return 0;
  }
  if ( !DeleteFileA(m_szLogFileName) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLogSaveThread::Compress", aDWorkRylSource_37, 189, aD_1, v7);
    return 0;
  }
  return 1;
}

//----- (004412E0) --------------------------------------------------------
std::_List_nod<CThread *>::_Node *__thiscall std::list<CModifyDummyCharacter *>::_Buynode(std::list<CThread *> *this)
{
  std::_List_nod<CThread *>::_Node *result; // eax

  result = (std::_List_nod<CThread *>::_Node *)operator new((tagHeader *)0xC);
  if ( result )
    result->_Next = result;
  if ( result != (std::_List_nod<CThread *>::_Node *)-4 )
    result->_Prev = result;
  return result;
}

//----- (00441300) --------------------------------------------------------
void __thiscall CLogSaveThread::~CLogSaveThread(CLogSaveThread *this)
{
  void *m_hFile; // eax

  m_hFile = this->m_hFile;
  this->__vftable = (CLogSaveThread_vtbl *)&CLogSaveThread::`vftable';
  if ( m_hFile != (void *)-1 )
  {
    CloseHandle(m_hFile);
    this->m_hFile = (void *)-1;
  }
  if ( this->m_hFlush )
  {
    CloseHandle(this->m_hFlush);
    this->m_hFlush = 0;
  }
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_WriteBufferList);
  operator delete(this->m_WriteBufferList._Myhead);
  this->m_WriteBufferList._Myhead = 0;
  this->__vftable = (CLogSaveThread_vtbl *)&CThread::`vftable';
}
// 4DEE68: using guessed type void *CLogSaveThread::`vftable';
// 500DF0: using guessed type void *CThread::`vftable';

//----- (00441360) --------------------------------------------------------
LogBuffer *__thiscall CGameLog::GetBuffer(CGameLog *this)
{
  LogBuffer *v2; // eax
  LogBuffer *Myval; // edi
  std::_List_nod<LogBuffer *>::_Node *Myhead; // ecx
  std::_List_nod<LogBuffer *>::_Node *Next; // eax

  EnterCriticalSection(&this->m_LogBufferLock.m_CSLock);
  if ( this->m_FreeList._Mysize )
  {
    Myval = this->m_FreeList._Myhead->_Next->_Myval;
    Myhead = this->m_FreeList._Myhead;
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      Next->_Prev->_Next = Next->_Next;
      Next->_Next->_Prev = Next->_Prev;
      operator delete(Next);
      --this->m_FreeList._Mysize;
    }
  }
  else
  {
    v2 = (LogBuffer *)operator new((tagHeader *)0xFDEC);
    if ( v2 )
    {
      v2->m_nUsage = 0;
      Myval = v2;
    }
    else
    {
      Myval = 0;
    }
  }
  if ( Myval )
  {
    Myval->m_nUsage = 0;
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
    return Myval;
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameLog::GetBuffer", aDWorkRylSource_37, 374, (char *)&byte_4DEE88);
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
    return 0;
  }
}

//----- (00441450) --------------------------------------------------------
void __thiscall CLogSaveThread::CLogSaveThread(CLogSaveThread *this)
{
  HANDLE EventA; // eax

  this->m_hThreadHandle = (void *)-1;
  this->__vftable = (CLogSaveThread_vtbl *)&CLogSaveThread::`vftable';
  EventA = CreateEventA(0, 1, 0, 0);
  this->m_hFile = (void *)-1;
  this->m_hFlush = EventA;
  this->m_dwTotalWritten = 0;
  this->m_WriteBufferList._Myhead = (std::_List_nod<LogBuffer *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_WriteBufferList);
  this->m_WriteBufferList._Mysize = 0;
  InterlockedExchange(&this->m_bEnd, 0);
}
// 4DEE68: using guessed type void *CLogSaveThread::`vftable';

//----- (004414D0) --------------------------------------------------------
CLogSaveThread *__thiscall CLogSaveThread::`vector deleting destructor'(CLogSaveThread *this, char a2)
{
  CLogSaveThread::~CLogSaveThread(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004414F0) --------------------------------------------------------
void __thiscall CGameLog::CGameLog(CGameLog *this)
{
  InitializeCriticalSection(&this->m_LogBufferLock.m_CSLock);
  this->m_FreeList._Myhead = (std::_List_nod<LogBuffer *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_FreeList);
  this->m_FreeList._Mysize = 0;
  this->m_FullList._Myhead = (std::_List_nod<LogBuffer *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_FullList);
  this->m_FullList._Mysize = 0;
  CLogSaveThread::CLogSaveThread(&this->m_LogSaveThread);
  this->m_lpLogBuffer = 0;
  memset(this->m_szLogFilePrefix, 0, sizeof(this->m_szLogFilePrefix));
}

//----- (00441580) --------------------------------------------------------
void __thiscall std::list<LogBuffer *>::_Incsize(std::list<LogBuffer *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00441620) --------------------------------------------------------
char __thiscall CGameLog::Initialize(CGameLog *this, const char *szLogFilePrefix)
{
  CGameLog *v2; // esi
  int v3; // ebp
  const char *v4; // eax
  std::_List_nod<CThread *>::_Node *Myhead; // edi
  std::list<CThread *> *p_m_FreeList; // esi
  std::_List_nod<CThread *>::_Node *v7; // ebx
  LogBuffer *v8; // eax

  v2 = this;
  if ( szLogFilePrefix )
  {
    _snprintf(this->m_szLogFilePrefix, 0x103u, "%s", szLogFilePrefix);
    v2->m_szLogFilePrefix[259] = 0;
  }
  else
  {
    DbgUtils::SetProgramName(this->m_szLogFilePrefix, 0x104u, 0);
  }
  v3 = 0;
  while ( 1 )
  {
    v4 = (const char *)operator new((tagHeader *)0xFDEC);
    if ( v4 )
      *(_DWORD *)v4 = 0;
    else
      v4 = 0;
    szLogFilePrefix = v4;
    if ( !v4 )
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameLog::Initialize", aDWorkRylSource_37, 281, byte_4DEEE4);
      return 0;
    }
    Myhead = (std::_List_nod<CThread *>::_Node *)v2->m_FreeList._Myhead;
    p_m_FreeList = (std::list<CThread *> *)&v2->m_FreeList;
    v7 = std::list<IOPCode *>::_Buynode(p_m_FreeList, Myhead, Myhead->_Prev, (CThread **)&szLogFilePrefix);
    std::list<LogBuffer *>::_Incsize((std::list<LogBuffer *> *)p_m_FreeList, 1u);
    Myhead->_Prev = v7;
    ++v3;
    v7->_Prev->_Next = v7;
    if ( v3 >= 100 )
      break;
    v2 = this;
  }
  if ( !this->m_lpLogBuffer )
  {
    v8 = (LogBuffer *)operator new((tagHeader *)0xFDEC);
    if ( v8 )
      v8->m_nUsage = 0;
    else
      v8 = 0;
    this->m_lpLogBuffer = v8;
    if ( !v8 )
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameLog::Initialize", aDWorkRylSource_37, 296, byte_4DEEE4);
      return 0;
    }
  }
  if ( CThreadMgr::Run(&this->m_LogSaveThread) == (HANDLE)-1 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameLog::Initialize", aDWorkRylSource_37, 302, aFlush_0);
    return 0;
  }
  return 1;
}

//----- (00441760) --------------------------------------------------------
void __thiscall CGameLog::PushFullBuffer(CGameLog *this, CThread **ppLogBuffer)
{
  std::_List_nod<CThread *>::_Node *Myhead; // ebx
  std::_List_nod<CThread *>::_Node *v4; // ebp

  if ( *ppLogBuffer )
  {
    EnterCriticalSection(&this->m_LogBufferLock.m_CSLock);
    if ( (*ppLogBuffer)->__vftable )
    {
      Myhead = (std::_List_nod<CThread *>::_Node *)this->m_FullList._Myhead;
      v4 = std::list<IOPCode *>::_Buynode((std::list<CThread *> *)&this->m_FullList, Myhead, Myhead->_Prev, ppLogBuffer);
      std::list<LogBuffer *>::_Incsize(&this->m_FullList, 1u);
    }
    else
    {
      Myhead = (std::_List_nod<CThread *>::_Node *)this->m_FreeList._Myhead;
      v4 = std::list<IOPCode *>::_Buynode((std::list<CThread *> *)&this->m_FreeList, Myhead, Myhead->_Prev, ppLogBuffer);
      std::list<LogBuffer *>::_Incsize(&this->m_FreeList, 1u);
    }
    Myhead->_Prev = v4;
    v4->_Prev->_Next = v4;
    *ppLogBuffer = 0;
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  }
}

//----- (00441820) --------------------------------------------------------
void __thiscall std::list<LogBuffer *>::_Splice(
        std::list<LogBuffer *> *this,
        std::list<LogBuffer *>::iterator _Where,
        std::list<LogBuffer *> *_Right,
        std::list<LogBuffer *>::iterator _First,
        std::list<LogBuffer *>::iterator _Last,
        unsigned int _Count)
{
  std::_List_nod<LogBuffer *>::_Node *Prev; // esi

  if ( this != _Right )
  {
    std::list<LogBuffer *>::_Incsize(this, _Count);
    _Right->_Mysize -= _Count;
  }
  _First._Ptr->_Prev->_Next = _Last._Ptr;
  _Last._Ptr->_Prev->_Next = _Where._Ptr;
  _Where._Ptr->_Prev->_Next = _First._Ptr;
  Prev = _Where._Ptr->_Prev;
  _Where._Ptr->_Prev = _Last._Ptr->_Prev;
  _Last._Ptr->_Prev = _First._Ptr->_Prev;
  _First._Ptr->_Prev = Prev;
}

//----- (00441870) --------------------------------------------------------
BOOL __thiscall CGameLog::Flush(CGameLog *this)
{
  CGameLog::PushFullBuffer(this, (CThread **)&this->m_lpLogBuffer);
  return PulseEvent(this->m_LogSaveThread.m_hFlush);
}

//----- (00441890) --------------------------------------------------------
char __thiscall CGameLog::Destroy(CGameLog *this)
{
  std::_List_nod<LogBuffer *>::_Node *Myhead; // eax
  std::_List_nod<LogBuffer *>::_Node *Next; // edi
  std::_List_nod<LogBuffer *>::_Node *v4; // ecx
  std::_List_nod<LogBuffer *>::_Node *v5; // eax
  bool v6; // zf
  std::_List_nod<LogBuffer *>::_Node *v7; // edi
  std::_List_nod<LogBuffer *>::_Node *v8; // eax
  std::_List_nod<LogBuffer *>::_Node *v9; // edi
  std::_List_nod<LogBuffer *>::_Node *v10; // ecx
  std::_List_nod<LogBuffer *>::_Node *v11; // eax
  std::_List_nod<LogBuffer *>::_Node *v12; // edi

  CGameLog::PushFullBuffer(this, (CThread **)&this->m_lpLogBuffer);
  PulseEvent(this->m_LogSaveThread.m_hFlush);
  CThreadMgr::Stop(&this->m_LogSaveThread, 0xFFFFFFFF);
  EnterCriticalSection(&this->m_LogBufferLock.m_CSLock);
  Myhead = this->m_FreeList._Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      operator delete(Next->_Myval);
      Next = Next->_Next;
    }
    while ( Next != this->m_FreeList._Myhead );
  }
  v4 = this->m_FreeList._Myhead;
  v5 = v4->_Next;
  v4->_Next = v4;
  this->m_FreeList._Myhead->_Prev = this->m_FreeList._Myhead;
  v6 = v5 == this->m_FreeList._Myhead;
  this->m_FreeList._Mysize = 0;
  if ( !v6 )
  {
    do
    {
      v7 = v5->_Next;
      operator delete(v5);
      v5 = v7;
    }
    while ( v7 != this->m_FreeList._Myhead );
  }
  v8 = this->m_FullList._Myhead;
  v9 = v8->_Next;
  if ( v8->_Next != v8 )
  {
    do
    {
      operator delete(v9->_Myval);
      v9 = v9->_Next;
    }
    while ( v9 != this->m_FullList._Myhead );
  }
  v10 = this->m_FullList._Myhead;
  v11 = v10->_Next;
  v10->_Next = v10;
  this->m_FullList._Myhead->_Prev = this->m_FullList._Myhead;
  v6 = v11 == this->m_FullList._Myhead;
  this->m_FullList._Mysize = 0;
  if ( !v6 )
  {
    do
    {
      v12 = v11->_Next;
      operator delete(v11);
      v11 = v12;
    }
    while ( v12 != this->m_FullList._Myhead );
  }
  LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  return 1;
}

//----- (00441970) --------------------------------------------------------
char *__thiscall CGameLog::ReserveBuffer(CGameLog *this, unsigned __int16 usReserve)
{
  CThread **p_m_lpLogBuffer; // esi
  CThread *v4; // esi

  p_m_lpLogBuffer = (CThread **)&this->m_lpLogBuffer;
  if ( !this->m_lpLogBuffer )
    *p_m_lpLogBuffer = (CThread *)CGameLog::GetBuffer(this);
  if ( (int)(*p_m_lpLogBuffer)->__vftable + usReserve > 65000 )
  {
    CGameLog::PushFullBuffer(this, p_m_lpLogBuffer);
    PulseEvent(this->m_LogSaveThread.m_hFlush);
    *p_m_lpLogBuffer = (CThread *)CGameLog::GetBuffer(this);
  }
  v4 = *p_m_lpLogBuffer;
  if ( v4 )
    return (char *)&v4->Run + (unsigned int)v4;
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CGameLog::ReserveBuffer", aDWorkRylSource_37, 349, (char *)&byte_4DEF28);
  return 0;
}

//----- (00441A00) --------------------------------------------------------
void __thiscall CGameLog::SpliceOutFullBuffer(CGameLog *this, std::list<LogBuffer *> *logBufferList)
{
  unsigned int Mysize; // edi

  EnterCriticalSection(&this->m_LogBufferLock.m_CSLock);
  if ( this->m_FullList._Mysize )
  {
    if ( logBufferList != &this->m_FullList )
    {
      Mysize = this->m_FullList._Mysize;
      if ( Mysize )
        std::list<LogBuffer *>::_Splice(
          logBufferList,
          (std::list<LogBuffer *>::iterator)logBufferList->_Myhead,
          &this->m_FullList,
          (std::list<LogBuffer *>::iterator)this->m_FullList._Myhead->_Next,
          (std::list<LogBuffer *>::iterator)this->m_FullList._Myhead,
          Mysize);
    }
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  }
  else
  {
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  }
}

//----- (00441A90) --------------------------------------------------------
void __thiscall CGameLog::SpliceInFreeBuffer(CGameLog *this, std::list<LogBuffer *> *logBufferList)
{
  unsigned int Mysize; // ebx

  EnterCriticalSection(&this->m_LogBufferLock.m_CSLock);
  Mysize = logBufferList->_Mysize;
  if ( Mysize )
  {
    if ( &this->m_FreeList != logBufferList )
      std::list<LogBuffer *>::_Splice(
        &this->m_FreeList,
        (std::list<LogBuffer *>::iterator)this->m_FreeList._Myhead,
        logBufferList,
        (std::list<LogBuffer *>::iterator)logBufferList->_Myhead->_Next,
        (std::list<LogBuffer *>::iterator)logBufferList->_Myhead,
        Mysize);
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  }
  else
  {
    LeaveCriticalSection(&this->m_LogBufferLock.m_CSLock);
  }
}

//----- (00441B20) --------------------------------------------------------
void __thiscall CGameLog::~CGameLog(CGameLog *this)
{
  CGameLog::Destroy(this);
  CLogSaveThread::~CLogSaveThread(&this->m_LogSaveThread);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_FullList);
  operator delete(this->m_FullList._Myhead);
  this->m_FullList._Myhead = 0;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_FreeList);
  operator delete(this->m_FreeList._Myhead);
  this->m_FreeList._Myhead = 0;
  DeleteCriticalSection(&this->m_LogBufferLock.m_CSLock);
}

//----- (00441BB0) --------------------------------------------------------
CGameLog *__cdecl CGameLog::GetInstance()
{
  if ( (_S5_2 & 1) == 0 )
  {
    _S5_2 |= 1u;
    CGameLog::CGameLog(&gameLog);
    atexit(_E6_7);
  }
  return &gameLog;
}

//----- (00441C10) --------------------------------------------------------
char __thiscall CLogSaveThread::SetLogFileName(CLogSaveThread *this)
{
  CGameLog *Instance; // eax
  int v4; // edi
  char *m_szLogFileName; // esi
  _SYSTEMTIME sysTime; // [esp+8h] [ebp-21Ch] BYREF
  char szProgramName[260]; // [esp+18h] [ebp-20Ch] BYREF
  char szLogFilePrefix[260]; // [esp+11Ch] [ebp-108h] BYREF

  DbgUtils::SetProgramName(szProgramName, 0x104u, 0);
  szProgramName[259] = 0;
  Instance = CGameLog::GetInstance();
  _snprintf(szLogFilePrefix, 0x103u, "%s", Instance->m_szLogFilePrefix);
  szLogFilePrefix[259] = 0;
  GetLocalTime(&sysTime);
  if ( GetFileAttributesA(szProgramName) == -1 && !CreateDirectoryA(szProgramName, 0) )
    return 0;
  v4 = 0;
  m_szLogFileName = this->m_szLogFileName;
  if ( _snprintf(
         m_szLogFileName,
         0x104u,
         "%s\\%sGameLog-%04d%02d%02d-%02d%02d%02d-%04d.log",
         szProgramName,
         szLogFilePrefix,
         sysTime.wYear,
         sysTime.wMonth,
         sysTime.wDay,
         sysTime.wHour,
         sysTime.wMinute,
         sysTime.wSecond,
         0) <= 0 )
  {
LABEL_7:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CLogSaveThread::SetLogFileName",
      aDWorkRylSource_37,
      234,
      (char *)&byte_4DEF60);
    return 0;
  }
  else
  {
    while ( GetFileAttributesA(m_szLogFileName) != -1 )
    {
      if ( _snprintf(
             m_szLogFileName,
             0x104u,
             "%s\\%sGameLog-%04d%02d%02d-%02d%02d%02d-%04d.log",
             szProgramName,
             szLogFilePrefix,
             sysTime.wYear,
             sysTime.wMonth,
             sysTime.wDay,
             sysTime.wHour,
             sysTime.wMinute,
             sysTime.wSecond,
             ++v4) <= 0 )
        goto LABEL_7;
    }
    return 1;
  }
}

//----- (00441DB0) --------------------------------------------------------
char __thiscall CLogSaveThread::WriteLog(CLogSaveThread *this)
{
  HANDLE FileA; // eax
  CGameLog *Instance; // eax
  std::_List_nod<LogBuffer *>::_Node *Myhead; // eax
  std::_List_nod<LogBuffer *>::_Node *Next; // ebx
  DWORD *Myval; // edi
  DWORD LastError; // eax
  CGameLog *v9; // eax
  void *m_hFile; // [esp-14h] [ebp-1Ch]
  DWORD v11; // [esp-10h] [ebp-18h]
  unsigned int dwWritten; // [esp+4h] [ebp-4h] BYREF

  if ( this->m_hFile == (void *)-1 )
  {
    CLogSaveThread::SetLogFileName(this);
    FileA = CreateFileA(this->m_szLogFileName, 0x40000000u, 1u, 0, 2u, 0x80u, 0);
    this->m_hFile = FileA;
    if ( FileA == (HANDLE)-1 )
      return 0;
  }
  Instance = CGameLog::GetInstance();
  CGameLog::SpliceOutFullBuffer(Instance, &this->m_WriteBufferList);
  if ( this->m_WriteBufferList._Mysize )
  {
    Myhead = this->m_WriteBufferList._Myhead;
    dwWritten = 0;
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      do
      {
        Myval = (DWORD *)Next->_Myval;
        if ( !WriteFile(this->m_hFile, Myval + 1, *Myval, &dwWritten, 0) )
        {
          v11 = *Myval;
          m_hFile = this->m_hFile;
          LastError = GetLastError();
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CLogSaveThread::WriteLog",
            aDWorkRylSource_37,
            95,
            (char *)&byte_4DEFE0,
            LastError,
            m_hFile,
            v11);
        }
        this->m_dwTotalWritten += dwWritten;
        Next = Next->_Next;
      }
      while ( Next != this->m_WriteBufferList._Myhead );
    }
    v9 = CGameLog::GetInstance();
    CGameLog::SpliceInFreeBuffer(v9, &this->m_WriteBufferList);
    if ( this->m_dwTotalWritten > 0x3200000 )
    {
      if ( this->m_hFile != (void *)-1 )
      {
        CloseHandle(this->m_hFile);
        this->m_hFile = (void *)-1;
      }
      CLogSaveThread::Compress(this);
      this->m_dwTotalWritten = 0;
    }
  }
  return 1;
}

//----- (00441ED0) --------------------------------------------------------
unsigned int __thiscall CLogSaveThread::Run(CLogSaveThread *this)
{
  volatile int *p_m_bEnd; // edi

  p_m_bEnd = &this->m_bEnd;
  while ( InterlockedCompareExchange(p_m_bEnd, 1, 1) != 1 )
  {
    if ( WaitForSingleObject(this->m_hFlush, 0x3E8u) != 258 )
      CLogSaveThread::WriteLog(this);
  }
  CLogSaveThread::WriteLog(this);
  return 0;
}

//----- (00441F30) --------------------------------------------------------
void __thiscall CPacketStatistics::PacketData::Clear(CPacketStatistics::PacketData *this)
{
  this->m_Count = 0.0;
  this->m_Bytes = 0.0;
}

//----- (00441F50) --------------------------------------------------------
void __thiscall CPacketStatistics::AddSendPacket(
        CPacketStatistics *this,
        unsigned __int8 cCmd,
        unsigned int dwPacketSize)
{
  CPacketStatistics::PacketData *v3; // eax

  v3 = &this->m_SendData[cCmd];
  v3->m_Bytes = (double)dwPacketSize + v3->m_Bytes;
  v3->m_Count = v3->m_Count + 1.0;
}

//----- (00441F90) --------------------------------------------------------
void __thiscall CPacketStatistics::AddRecvPacket(
        CPacketStatistics *this,
        unsigned __int8 cCmd,
        unsigned int dwPacketSize)
{
  CPacketStatistics::PacketData *v3; // eax

  v3 = &this->m_RecvData[cCmd];
  v3->m_Bytes = (double)dwPacketSize + v3->m_Bytes;
  v3->m_Count = v3->m_Count + 1.0;
}

//----- (00441FC0) --------------------------------------------------------
void __thiscall CPacketStatistics::SetUserMessageFunc(
        CPacketStatistics *this,
        void (__cdecl *fnPreFix)(_iobuf *),
        void (__cdecl *fnPostFix)(_iobuf *))
{
  this->m_fnPrefix = fnPreFix;
  this->m_fnPostfix = fnPostFix;
}

//----- (00441FE0) --------------------------------------------------------
char __thiscall CPacketStatistics::Log(CPacketStatistics *this)
{
  int v2; // ebx
  HANDLE i; // esi
  _iobuf *v4; // ebx
  int *v5; // esi
  void (__cdecl *v6)(_iobuf *); // eax
  int v7; // eax
  int v8; // edi
  double *v9; // esi
  int *v10; // edi
  double *v11; // esi
  int *v12; // edi
  void (__cdecl *v13)(_iobuf *); // eax
  unsigned __int8 cCmd; // [esp+3Fh] [ebp-281h]
  unsigned __int8 cCmda; // [esp+3Fh] [ebp-281h]
  double fTotalBytes; // [esp+40h] [ebp-280h]
  double fTotalBytesa; // [esp+40h] [ebp-280h]
  double tDiffTime; // [esp+48h] [ebp-278h] BYREF
  double fTotalCount; // [esp+50h] [ebp-270h]
  int *ptime; // [esp+58h] [ebp-268h]
  int tCurrent; // [esp+5Ch] [ebp-264h] BYREF
  tm tmCurrent; // [esp+60h] [ebp-260h] BYREF
  tm tmServerStart; // [esp+84h] [ebp-23Ch] BYREF
  char strLogPath[264]; // [esp+A8h] [ebp-218h] BYREF
  char strLogFileName[268]; // [esp+1B0h] [ebp-110h] BYREF

  ptime = &this->m_StartTime;
  DbgUtils::SetProgramName(strLogPath, 0x104u, 0);
  if ( GetFileAttributesA(strLogPath) == -1 && !CreateDirectoryA(strLogPath, 0) )
    return 0;
  v2 = 0;
  _snprintf(strLogFileName, 0x104u, "%s/Statistics-%s-%05d.log", strLogPath, strLogPath, 0);
  for ( i = CreateFileA(strLogFileName, 0x40000000u, 1u, 0, 3u, 0x80u, 0);
        i != (HANDLE)-1;
        i = CreateFileA(strLogFileName, 0x40000000u, 1u, 0, 3u, 0x80u, 0) )
  {
    LODWORD(tDiffTime) = 0;
    tCurrent = GetFileSize(i, (LPDWORD)&tDiffTime);
    CloseHandle(i);
    if ( !LODWORD(tDiffTime) && (unsigned int)tCurrent < 0x3200000 )
      break;
    _snprintf(strLogFileName, 0x104u, "%s/Statistics-%s-%05d.log", strLogPath, strLogPath, ++v2);
  }
  v4 = fopen(strLogFileName, "at");
  if ( v4 )
  {
    v5 = ptime;
    v6 = (void (__cdecl *)(_iobuf *))ptime[1];
    if ( v6 )
      v6(v4);
    v7 = time(0);
    v8 = *v5;
    tCurrent = v7;
    LODWORD(tDiffTime) = v7 - v8;
    qmemcpy(&tmServerStart, localtime((int)v5), sizeof(tmServerStart));
    qmemcpy(&tmCurrent, localtime((int)&tCurrent), sizeof(tmCurrent));
    fprintf(
      v4,
      "\t-- Server Packet Statistics -- \n"
      "\tLog Start Time    : %04dyear %02dmon %02dday %02dhour %02dmin %02dsec\n"
      "\tCurrent Log Time  : %04dyear %02dmon %02dday %02dhour %02dmin %02dsec\n"
      "\tLog Duration      : %02dhour %02dmin %02dsec\n"
      "\n",
      tmServerStart.tm_year + 1900,
      tmServerStart.tm_mon + 1,
      tmServerStart.tm_mday,
      tmServerStart.tm_hour,
      tmServerStart.tm_min,
      tmServerStart.tm_sec,
      tmCurrent.tm_year + 1900,
      tmCurrent.tm_mon,
      tmCurrent.tm_mday,
      tmCurrent.tm_hour,
      tmCurrent.tm_min,
      tmCurrent.tm_sec,
      SLODWORD(tDiffTime) / 3600,
      SLODWORD(tDiffTime) % 3600 / 60,
      SLODWORD(tDiffTime) % 3600 % 60);
    fTotalBytes = 0.0;
    v9 = (double *)(ptime + 4);
    v10 = ptime + 1024;
    fTotalCount = 0.0;
    cCmd = 0;
    if ( ptime + 4 != ptime + 1024 )
    {
      do
      {
        if ( 0.0 != v9[1] )
        {
          fprintf(
            v4,
            "\t %3d(0x%02X) : %16.4f : (%16.4fMb / %16.4fKb / %16.4fBytes) : %16.4fBytes/Sec\n",
            cCmd,
            cCmd,
            v9[1],
            *v9 * 0.00000095367431640625,
            *v9 * 0.0009765625,
            *v9,
            *v9 / (double)SLODWORD(tDiffTime));
          fTotalBytes = fTotalBytes + *v9;
          fTotalCount = fTotalCount + v9[1];
        }
        v9 += 2;
        ++cCmd;
      }
      while ( v9 != (double *)v10 );
    }
    tDiffTime = 1.0 / (double)SLODWORD(tDiffTime);
    fprintf(
      v4,
      "\n"
      " Total Data Recv : %16.4fMb / %16.4fKb / %16.4fBytes / %16.4fBytes/Sec\n"
      " Total Count     : %16.4f   / %16.4f(Count/Sec)\n"
      "\n",
      fTotalBytes * 0.00000095367431640625,
      fTotalBytes * 0.0009765625,
      fTotalBytes,
      tDiffTime * fTotalBytes,
      fTotalCount,
      tDiffTime * fTotalCount);
    v11 = (double *)v10;
    fTotalBytesa = 0.0;
    v12 = ptime + 2044;
    fTotalCount = 0.0;
    for ( cCmda = 0; v11 != (double *)v12; ++cCmda )
    {
      if ( 0.0 != v11[1] )
      {
        fprintf(
          v4,
          "\t %3d(0x%02X) : %16.4fCount : (%16.4fMb / %16.4fKb / %16.4fBytes) : %16.4fBytes/Sec\n",
          cCmda,
          cCmda,
          v11[1],
          *v11 * 0.00000095367431640625,
          *v11 * 0.0009765625,
          *v11,
          tDiffTime * *v11);
        fTotalBytesa = fTotalBytesa + *v11;
        fTotalCount = fTotalCount + v11[1];
      }
      v11 += 2;
    }
    fprintf(
      v4,
      "\n"
      " Total Data Send : %16.4fMb / %16.4fKb / %16.4fBytes / %16.4fBytes/Sec\n"
      " Total Count     : %16.4f   / %16.4f(Count/Sec)\n"
      "\n"
      "\n",
      fTotalBytesa * 0.00000095367431640625,
      fTotalBytesa * 0.0009765625,
      fTotalBytesa,
      tDiffTime * fTotalBytesa,
      fTotalCount,
      tDiffTime * fTotalCount);
    v13 = (void (__cdecl *)(_iobuf *))ptime[2];
    if ( v13 )
      v13(v4);
    fclose(v4);
  }
  return 1;
}

//----- (00442440) --------------------------------------------------------
void __thiscall CPacketStatistics::CPacketStatistics(CPacketStatistics *this)
{
  CPacketStatistics::PacketData *m_RecvData; // eax
  int v3; // ecx
  CPacketStatistics::PacketData *m_SendData; // eax
  int v5; // ecx

  *(_QWORD *)&this->m_StartTime = (unsigned int)time(0);
  this->m_fnPostfix = 0;
  m_RecvData = this->m_RecvData;
  v3 = 255;
  do
  {
    ++m_RecvData;
    --v3;
    m_RecvData[-1].m_Bytes = 0.0;
    m_RecvData[-1].m_Count = 0.0;
  }
  while ( v3 );
  m_SendData = this->m_SendData;
  v5 = 255;
  do
  {
    ++m_SendData;
    --v5;
    m_SendData[-1].m_Bytes = 0.0;
    m_SendData[-1].m_Count = 0.0;
  }
  while ( v5 );
}

//----- (004424B0) --------------------------------------------------------
CPacketStatistics *__cdecl CPacketStatistics::GetInstance()
{
  if ( (_S1_2 & 1) == 0 )
  {
    _S1_2 |= 1u;
    CPacketStatistics::CPacketStatistics(&packetStatistics);
    atexit(_E2_5);
  }
  return &packetStatistics;
}

//----- (004424F0) --------------------------------------------------------
void __thiscall CPacketStatistics::Clear(CPacketStatistics *this)
{
  CPacketStatistics::PacketData *m_RecvData; // edi
  CPacketStatistics::PacketData *i; // esi

  m_RecvData = this->m_RecvData;
  for ( i = this->m_SendData; m_RecvData != i; ++m_RecvData )
    CPacketStatistics::PacketData::Clear(m_RecvData);
  for ( ; i != (CPacketStatistics::PacketData *)&this[1]; ++i )
    CPacketStatistics::PacketData::Clear(i);
  this->m_StartTime = time(0);
}

//----- (00442540) --------------------------------------------------------
void __thiscall CChatDispatch::CChatDispatch(CChatDispatch *this, CSession *Session)
{
  CRylServerDispatch::CRylServerDispatch(this, Session, 0xAu);
  this->__vftable = (CChatDispatch_vtbl *)&CChatDispatch::`vftable';
}
// 4DF30C: using guessed type void *CChatDispatch::`vftable';

//----- (00442560) --------------------------------------------------------
void __thiscall CChatDispatch::~CChatDispatch(CChatDispatch *this)
{
  this->__vftable = (CChatDispatch_vtbl *)&CChatDispatch::`vftable';
  CRylServerDispatch::~CRylServerDispatch(this);
}
// 4DF30C: using guessed type void *CChatDispatch::`vftable';

//----- (00442570) --------------------------------------------------------
char __cdecl CChatDispatch::SendCharInfoChanged(CSendStream *SendStream, CCharacter *Character)
{
  char *Buffer; // eax
  char *v3; // edi
  unsigned int v4; // ebx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1F);
  v3 = Buffer;
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = Character->m_dwUID;
  *((_DWORD *)Buffer + 4) = Character->m_dwCID;
  *((_DWORD *)Buffer + 5) = Character->m_DBData.m_Info.Party;
  v4 = Character->GetGID(Character);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v4);
  if ( Guild )
  {
    if ( Guild::CGuild::GetTitle(Guild, Character->m_dwCID) == 5 )
      v4 |= 0x80000000;
  }
  *((_DWORD *)v3 + 6) = v4;
  *((_WORD *)v3 + 14) = Character->m_RejectOption.m_wReject;
  v3[30] = Character->m_PeaceMode.m_bPeace;
  return CSendStream::WrapHeader(SendStream, 0x1Fu, 0x80u, 0, 0);
}

//----- (00442610) --------------------------------------------------------
CSingleDispatch *__cdecl CChatDispatch::GetDispatchTable()
{
  if ( (_S5_3 & 1) == 0 )
  {
    _S5_3 |= 1u;
    CSingleDispatch::CSingleDispatch(&singleDispatch);
    atexit(_E6_8);
  }
  return &singleDispatch;
}

//----- (00442670) --------------------------------------------------------
CChatDispatch *__thiscall CChatDispatch::`scalar deleting destructor'(CChatDispatch *this, char a2)
{
  CChatDispatch::~CChatDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00442690) --------------------------------------------------------
void __thiscall CChatDispatch::Connected(CChatDispatch *this)
{
  CSendStream *p_m_SendStream; // edi
  char *Buffer; // esi
  CServerSetup *Instance; // eax

  p_m_SendStream = &this->m_SendStream;
  Buffer = CSendStream::GetBuffer(&this->m_SendStream, (char *)0x14);
  if ( Buffer )
  {
    Instance = CServerSetup::GetInstance();
    *((_DWORD *)Buffer + 3) = CServerSetup::GetServerID(Instance);
    *((_DWORD *)Buffer + 4) = 0;
    CSendStream::WrapHeader(p_m_SendStream, 0x14u, 0x28u, 0, 0);
  }
}

//----- (004426D0) --------------------------------------------------------
void __thiscall CChatDispatch::Disconnected(CChatDispatch *this)
{
  CSingleDispatch *DispatchTable; // eax

  DispatchTable = CChatDispatch::GetDispatchTable();
  CSingleDispatch::RemoveDispatch(DispatchTable, this);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CChatDispatch::Disconnected",
    aDWorkRylSource_62,
    44,
    "this:0x%p/ChatServer Disconnected",
    this);
}

//----- (00442710) --------------------------------------------------------
char __thiscall CChatDispatch::DispatchPacket(CChatDispatch *this, PktBase *lpPktBase)
{
  unsigned __int8 m_Cmd; // al
  CSingleDispatch *DispatchTable; // eax

  m_Cmd = lpPktBase->m_Cmd;
  if ( m_Cmd == 40 )
  {
    if ( !lpPktBase->m_SrvInfo.SrvState.wError )
    {
      DispatchTable = CChatDispatch::GetDispatchTable();
      CSingleDispatch::SetDispatch(DispatchTable, this);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CChatDispatch::DispatchPacket",
        aDWorkRylSource_62,
        57,
        "this:0x%p/ChatServer Connected",
        this);
      return 1;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CChatDispatch::DispatchPacket",
      aDWorkRylSource_62,
      73,
      "DP:0x%p/Cmd:0x%02x/Chatpacket Process failed",
      this,
      40);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CChatDispatch::DispatchPacket",
      aDWorkRylSource_62,
      65,
      "DP:0x%p/Cmd:0x%02x/Unknown Chatpacket",
      this,
      m_Cmd);
  }
  return 1;
}

//----- (00442790) --------------------------------------------------------
void __thiscall CSendStream::CSendStream(CSendStream *this, CSession *Session)
{
  this->m_lpBuffer = 0;
  this->m_Session = Session;
}

//----- (004427B0) --------------------------------------------------------
void __thiscall CSendStream::~CSendStream(CSendStream *this)
{
  if ( this->m_lpBuffer )
  {
    this->m_lpBuffer->bufferfactory_->Release(this->m_lpBuffer->bufferfactory_, this->m_lpBuffer);
    this->m_lpBuffer = 0;
  }
}

//----- (004427D0) --------------------------------------------------------
char *__thiscall CSendStream::GetBuffer(CSendStream *this, char *dwReserve_In)
{
  CBuffer *m_lpBuffer; // eax
  CBufferFactory *v4; // ecx
  char *wr_ptr; // edx
  CBufferFactory *m_lpBufferFactory; // ecx
  CBuffer *v7; // esi

  m_lpBuffer = this->m_lpBuffer;
  if ( this->m_lpBuffer )
  {
    wr_ptr = m_lpBuffer->wr_ptr_;
    if ( &m_lpBuffer->internal_buffer_[m_lpBuffer->buffer_size_ - (int)wr_ptr] < dwReserve_In )
    {
      if ( wr_ptr == m_lpBuffer->rd_ptr_ )
      {
        m_lpBuffer->bufferfactory_->Release(m_lpBuffer->bufferfactory_, m_lpBuffer);
        this->m_lpBuffer = 0;
      }
      else
      {
        CSession::SendPending(this->m_Session, m_lpBuffer);
      }
      m_lpBufferFactory = this->m_Session->m_SessionPolicy->m_lpBufferFactory;
      this->m_lpBuffer = m_lpBufferFactory->Create(m_lpBufferFactory, (unsigned int)dwReserve_In);
    }
  }
  else
  {
    v4 = this->m_Session->m_SessionPolicy->m_lpBufferFactory;
    this->m_lpBuffer = v4->Create(v4, (unsigned int)dwReserve_In);
  }
  v7 = this->m_lpBuffer;
  if ( v7 )
    return v7->wr_ptr_;
  else
    return 0;
}

//----- (00442850) --------------------------------------------------------
char __thiscall CSession::Send(CSession *this)
{
  char v2; // bl

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v2 = CSession::InternalSend(this);
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return v2;
}

//----- (004428A0) --------------------------------------------------------
char __thiscall CSendStream::ForceSend(CSendStream *this)
{
  CBuffer *m_lpBuffer; // esi
  CSession *m_Session; // ecx

  m_lpBuffer = this->m_lpBuffer;
  if ( this->m_lpBuffer )
  {
    if ( m_lpBuffer->wr_ptr_ == m_lpBuffer->rd_ptr_ )
      return 1;
    m_Session = this->m_Session;
    this->m_lpBuffer = 0;
    if ( CSession::SendPending(m_Session, m_lpBuffer) )
      return CSession::Send(this->m_Session);
    if ( m_lpBuffer )
      m_lpBuffer->bufferfactory_->Release(m_lpBuffer->bufferfactory_, m_lpBuffer);
  }
  return 0;
}

//----- (004428F0) --------------------------------------------------------
char __thiscall CSendStream::WrapHeader(
        CSendStream *this,
        unsigned __int16 usUsed_In,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  char *Buffer; // eax
  CPacketStatistics *Instance; // eax

  Buffer = CSendStream::GetBuffer(this, (char *)usUsed_In);
  if ( !Buffer || !PacketWrap::WrapHeader(Buffer, usUsed_In, cCMD_In, usState_In, usError_In) )
    return 0;
  this->m_lpBuffer->wr_ptr_ += usUsed_In;
  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::AddSendPacket(Instance, cCMD_In, usUsed_In);
  return CSendStream::ForceSend(this);
}

//----- (00442960) --------------------------------------------------------
char __thiscall CSendStream::WrapCrypt(
        CSendStream *this,
        unsigned __int16 usUsed_In,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  char *Buffer; // eax
  CPacketStatistics *Instance; // eax

  Buffer = CSendStream::GetBuffer(this, (char *)usUsed_In);
  if ( !Buffer || !PacketWrap::WrapCrypt(Buffer, usUsed_In, cCMD_In, usState_In, usError_In) )
    return 0;
  this->m_lpBuffer->wr_ptr_ += usUsed_In;
  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::AddSendPacket(Instance, cCMD_In, usUsed_In);
  return CSendStream::ForceSend(this);
}

//----- (004429D0) --------------------------------------------------------
char __thiscall CSendStream::WrapCompress(
        CSendStream *this,
        char *lpSourceData,
        char *usSourceLength,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  unsigned __int16 v6; // di
  PktBase *Buffer; // eax
  unsigned __int8 v9; // bl
  CPacketStatistics *Instance; // eax
  char *v12; // [esp-4h] [ebp-10h]

  v6 = (unsigned __int16)usSourceLength;
  usSourceLength = (char *)(((unsigned __int16)usSourceLength >> 6) + (unsigned __int16)usSourceLength + 19);
  Buffer = (PktBase *)CSendStream::GetBuffer(this, usSourceLength);
  if ( !Buffer )
    return 0;
  v9 = cCMD_In;
  if ( !PacketWrap::WrapCompress(
          Buffer,
          (unsigned int *)&usSourceLength,
          lpSourceData,
          v6,
          cCMD_In,
          usState_In,
          usError_In) )
    return 0;
  this->m_lpBuffer->wr_ptr_ += (unsigned int)usSourceLength;
  v12 = usSourceLength;
  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::AddSendPacket(Instance, v9, (unsigned int)v12);
  return CSendStream::ForceSend(this);
}

//----- (00442A60) --------------------------------------------------------
char __thiscall CSendStream::PutBuffer(
        CSendStream *this,
        char *szBuffer,
        unsigned int dwBufferSize,
        unsigned __int8 cCMD_In)
{
  unsigned __int8 *Buffer; // eax
  CPacketStatistics *Instance; // eax

  Buffer = (unsigned __int8 *)CSendStream::GetBuffer(this, (char *)dwBufferSize);
  if ( !Buffer )
    return 0;
  memmove(Buffer, (unsigned __int8 *)szBuffer, dwBufferSize);
  this->m_lpBuffer->wr_ptr_ += dwBufferSize;
  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::AddSendPacket(Instance, cCMD_In, dwBufferSize);
  return CSendStream::ForceSend(this);
}

//----- (00442AB0) --------------------------------------------------------
void __thiscall CSingleDispatch::CSingleDispatch(CSingleDispatch *this)
{
  InitializeCriticalSection(&this->m_DispatchLock.m_CSLock);
  this->m_lpDispatch = 0;
}

//----- (00442AD0) --------------------------------------------------------
void __thiscall CSingleDispatch::InternalRemoveDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch)
{
  CPacketDispatch *m_lpDispatch; // eax

  m_lpDispatch = this->m_lpDispatch;
  if ( lpDispatch == m_lpDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CSingleDispatch::InternalRemoveDispatch",
      aDWorkRylSource_24,
      65,
      "DP:0x%p/Reset dispatch",
      m_lpDispatch);
    CSession::Release(this->m_lpDispatch->m_Session);
    this->m_lpDispatch = 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSingleDispatch::InternalRemoveDispatch",
      aDWorkRylSource_24,
      72,
      "this:0x%p/Reset dispatch failed. Invalid dispatch(0x%p)",
      m_lpDispatch,
      lpDispatch);
  }
}

//----- (00442B40) --------------------------------------------------------
void __thiscall CSingleDispatch::Storage::~Storage(CMultiDispatch::Storage *this)
{
  if ( this->m_lpDispatch )
  {
    CSession::Release(this->m_lpDispatch->m_Session);
    this->m_lpDispatch = 0;
  }
}

//----- (00442B60) --------------------------------------------------------
void __thiscall CSingleDispatch::SetDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch)
{
  if ( lpDispatch )
  {
    EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
    if ( this->m_lpDispatch )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CSingleDispatch::SetDispatch",
        aDWorkRylSource_24,
        20,
        "this:0x%p/Already dispatch exist. close now",
        this);
      CSession::CloseSession(this->m_lpDispatch->m_Session);
      CSession::Release(this->m_lpDispatch->m_Session);
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CSingleDispatch::SetDispatch",
      aDWorkRylSource_24,
      25,
      "DP:0x%p/Setting new dispatch(DP:0x%p)",
      this->m_lpDispatch,
      lpDispatch);
    this->m_lpDispatch = lpDispatch;
    CSession::AddRef(lpDispatch->m_Session);
    LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  }
}

//----- (00442C20) --------------------------------------------------------
void __thiscall CSingleDispatch::RemoveDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch)
{
  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  CSingleDispatch::InternalRemoveDispatch(this, lpDispatch);
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
}

//----- (00442C80) --------------------------------------------------------
BOOL __thiscall CSingleDispatch::GetDispatchNum(CSingleDispatch *this)
{
  BOOL v2; // edi

  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  v2 = this->m_lpDispatch != 0;
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  return v2;
}

//----- (00442CB0) --------------------------------------------------------
void __thiscall CSingleDispatch::~CSingleDispatch(CSingleDispatch *this)
{
  CPacketDispatch *m_lpDispatch; // eax

  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  m_lpDispatch = this->m_lpDispatch;
  if ( m_lpDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSingleDispatch::~CSingleDispatch",
      aDWorkRylSource_24,
      56,
      "DP:0x%p/Dispatch is not removed until destroy",
      m_lpDispatch);
    CSingleDispatch::InternalRemoveDispatch(this, this->m_lpDispatch);
  }
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  DeleteCriticalSection(&this->m_DispatchLock.m_CSLock);
}

//----- (00442D40) --------------------------------------------------------
CPacketDispatch *__thiscall CSingleDispatch::GetDispatch(CSingleDispatch *this)
{
  CPacketDispatch *m_lpDispatch; // eax
  CPacketDispatch *v3; // edi

  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  m_lpDispatch = this->m_lpDispatch;
  if ( m_lpDispatch )
    CSession::AddRef(m_lpDispatch->m_Session);
  v3 = this->m_lpDispatch;
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  return v3;
}

//----- (00442DA0) --------------------------------------------------------
void __thiscall CSingleDispatch::Storage::Storage(CSingleDispatch::Storage *this, CSingleDispatch *singleDispatch)
{
  CPacketDispatch *Dispatch; // eax

  Dispatch = CSingleDispatch::GetDispatch(singleDispatch);
  this->m_SingleDispatch = singleDispatch;
  this->m_lpDispatch = Dispatch;
}

//----- (00442DC0) --------------------------------------------------------
char __cdecl PacketWrap::WrapHeader(
        char *lpBuffer_In,
        unsigned __int16 usUsed_In,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  lpBuffer_In[1] = cCMD_In;
  *((_WORD *)lpBuffer_In + 1) = usUsed_In;
  *((_WORD *)lpBuffer_In + 5) = usState_In;
  *lpBuffer_In = -1;
  *((_DWORD *)lpBuffer_In + 1) = 0;
  *((_WORD *)lpBuffer_In + 4) = usError_In;
  CXORCrypt::EncodeHeader(CSingleton<CXORCrypt>::ms_pSingleton, lpBuffer_In + 1, 11, 0, 0);
  return 1;
}

//----- (00442E10) --------------------------------------------------------
char __cdecl PacketWrap::WrapCrypt(
        char *lpBuffer_In,
        unsigned __int16 usUsed_In,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  CXORCrypt *v5; // ebx
  unsigned int CodePage; // ebp

  v5 = CSingleton<CXORCrypt>::ms_pSingleton;
  CodePage = CXORCrypt::GetCodePage(CSingleton<CXORCrypt>::ms_pSingleton);
  if ( !CXORCrypt::EncodePacket(v5, lpBuffer_In + 12, usUsed_In - 12, CodePage) )
    return 0;
  lpBuffer_In[1] = cCMD_In;
  *((_WORD *)lpBuffer_In + 5) = usState_In;
  *lpBuffer_In = -1;
  *((_WORD *)lpBuffer_In + 4) = usError_In;
  *((_DWORD *)lpBuffer_In + 1) = CodePage;
  *((_WORD *)lpBuffer_In + 1) = usUsed_In | 0x8000;
  CXORCrypt::EncodeHeader(v5, lpBuffer_In + 1, 11, 0, 0);
  return 1;
}

//----- (00442E90) --------------------------------------------------------
char __cdecl PacketWrap::WrapCrypt(
        char *lpBuffer_In,
        unsigned __int16 usUsed_In,
        unsigned __int8 cCMD_In,
        unsigned int dwTick)
{
  CXORCrypt *v4; // ebx
  unsigned int CodePage; // ebp

  v4 = CSingleton<CXORCrypt>::ms_pSingleton;
  CodePage = CXORCrypt::GetCodePage(CSingleton<CXORCrypt>::ms_pSingleton);
  if ( !CXORCrypt::EncodePacket(v4, lpBuffer_In + 12, usUsed_In - 12, CodePage) )
    return 0;
  *((_DWORD *)lpBuffer_In + 2) = dwTick;
  *lpBuffer_In = -1;
  lpBuffer_In[1] = cCMD_In;
  *((_DWORD *)lpBuffer_In + 1) = CodePage;
  *((_WORD *)lpBuffer_In + 1) = usUsed_In | 0x8000;
  CXORCrypt::EncodeHeader(v4, lpBuffer_In + 1, 11, 0, 0);
  return 1;
}

//----- (00442F00) --------------------------------------------------------
char __cdecl PacketWrap::WrapCompress(
        PktBase *lpDstBuffer,
        unsigned int *dwDstBufferSize_InOut,
        char *lpSourceData,
        unsigned __int16 usSourceLength,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  int v7; // eax
  unsigned int v9; // ebx
  unsigned int v10; // ebp

  if ( usSourceLength < 0xCu || usSourceLength > 0x3FFFu )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "PacketWrap::WrapCompress",
      aDWorkRylSource_29,
      111,
      "Cmd:0x%02x Invalid packet size, Can't compress now.",
      (unsigned __int8)lpSourceData[1]);
    return 0;
  }
  else
  {
    v7 = *dwDstBufferSize_InOut;
    if ( *dwDstBufferSize_InOut >= usSourceLength )
    {
      v9 = v7 - 12;
      *dwDstBufferSize_InOut = v7 - 12;
      if ( CMiniLZO::Compress(lpSourceData + 12, usSourceLength - 12, (char *)&lpDstBuffer[1], dwDstBufferSize_InOut) )
      {
        if ( v9 <= *dwDstBufferSize_InOut )
          CServerLog::DetailLog(
            &g_Log,
            LOG_SYSERR,
            "PacketWrap::WrapCompress",
            aDWorkRylSource_29,
            137,
            "Cmd:0x%02x Compressed size over than buffer size. buffer_size:%d, compressed_size:%d",
            (unsigned __int8)lpSourceData[1],
            v9,
            *dwDstBufferSize_InOut);
        v10 = *dwDstBufferSize_InOut + 12;
        *dwDstBufferSize_InOut = v10;
        PktBase::InitPtHead(lpDstBuffer, v10, cCMD_In, usState_In, usError_In);
        HIBYTE(lpDstBuffer->m_Len) |= 0x40u;
        CXORCrypt::EncodeHeader(CSingleton<CXORCrypt>::ms_pSingleton, (char *)&lpDstBuffer->m_Cmd, 11, 0, 0);
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_SYSERR,
          "PacketWrap::WrapCompress",
          aDWorkRylSource_29,
          130,
          "Cmd:0x%02x Packet compress failed. Packet length is %d.",
          (unsigned __int8)lpSourceData[1],
          usSourceLength);
        return 0;
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_SYSERR,
        "PacketWrap::WrapCompress",
        aDWorkRylSource_29,
        118,
        "Cmd:0x%02x Insufficient buffer size, Can't compress now.",
        (unsigned __int8)lpSourceData[1]);
      return 0;
    }
  }
}

//----- (00443060) --------------------------------------------------------
char __cdecl PacketWrap::WrapCompress(
        CBuffer *lpDstBuffer,
        char *lpSourceData,
        unsigned __int16 usSourceLength,
        unsigned __int8 cCMD_In,
        unsigned __int16 usState_In,
        unsigned __int16 usError_In)
{
  CBuffer *v6; // esi
  PktBase *wr_ptr; // eax

  v6 = lpDstBuffer;
  wr_ptr = (PktBase *)lpDstBuffer->wr_ptr_;
  lpDstBuffer = (CBuffer *)&lpDstBuffer->internal_buffer_[lpDstBuffer->buffer_size_ - (_DWORD)wr_ptr];
  if ( !PacketWrap::WrapCompress(
          wr_ptr,
          (unsigned int *)&lpDstBuffer,
          lpSourceData,
          usSourceLength,
          cCMD_In,
          usState_In,
          usError_In) )
    return 0;
  v6->wr_ptr_ += (unsigned int)lpDstBuffer;
  return 1;
}

//----- (004430C0) --------------------------------------------------------
void __thiscall CLogDispatch::CLogDispatch(CLogDispatch *this, CSession *Session)
{
  CRylServerDispatch::CRylServerDispatch(this, Session, 0xFFFFFFFF);
  this->__vftable = (CLogDispatch_vtbl *)&CLogDispatch::`vftable';
}
// 4DF824: using guessed type void *CLogDispatch::`vftable';

//----- (004430E0) --------------------------------------------------------
void __thiscall CLogDispatch::~CLogDispatch(CLogDispatch *this)
{
  this->__vftable = (CLogDispatch_vtbl *)&CLogDispatch::`vftable';
  CRylServerDispatch::~CRylServerDispatch(this);
}
// 4DF824: using guessed type void *CLogDispatch::`vftable';

//----- (004430F0) --------------------------------------------------------
void __thiscall CLogDispatch::Connected(CLogDispatch *this)
{
  CServerSetup *Instance; // eax
  unsigned int ServerID; // eax

  Instance = CServerSetup::GetInstance();
  ServerID = CServerSetup::GetServerID(Instance);
  SendLogPacket::ServerLogin(&this->m_SendStream, ServerID);
}

//----- (00443110) --------------------------------------------------------
CLogDispatch *__thiscall CLogDispatch::`vector deleting destructor'(CLogDispatch *this, char a2)
{
  CLogDispatch::~CLogDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00443130) --------------------------------------------------------
void __thiscall CLogDispatch::Disconnected(CLogDispatch *this)
{
  CSingleDispatch *DispatchTable; // eax

  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLogDispatch::Disconnected", aDWorkRylSource_95, 29, (char *)&byte_4DF8EC);
  DispatchTable = CLogDispatch::GetDispatchTable();
  CSingleDispatch::RemoveDispatch(DispatchTable, this);
}

//----- (00443170) --------------------------------------------------------
char __thiscall CLogDispatch::ProcessServerLoginAck(CLogDispatch *this, PktBase *lpPktBase)
{
  CSingleDispatch *DispatchTable; // eax

  if ( lpPktBase->m_SrvInfo.SrvState.wError )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CLogDispatch::ProcessServerLoginAck",
      aDWorkRylSource_95,
      72,
      (char *)&byte_4DF910);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CLogDispatch::ProcessServerLoginAck",
      aDWorkRylSource_95,
      67,
      (char *)&byte_4DF954);
    DispatchTable = CLogDispatch::GetDispatchTable();
    CSingleDispatch::SetDispatch(DispatchTable, this);
    return 1;
  }
}

//----- (004431E0) --------------------------------------------------------
char __thiscall CLogDispatch::DispatchPacket(CLogDispatch *this, PktBase *lpPktBase)
{
  unsigned __int8 m_Cmd; // al

  m_Cmd = lpPktBase->m_Cmd;
  if ( m_Cmd == 40 )
  {
    if ( !CLogDispatch::ProcessServerLoginAck(this, lpPktBase) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CLogDispatch::DispatchPacket",
        aDWorkRylSource_95,
        54,
        "DP:0x%p/Cmd:0x%02x/Logpacket Process failed",
        this,
        lpPktBase->m_Cmd);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CLogDispatch::DispatchPacket",
      aDWorkRylSource_95,
      46,
      "DP:0x%p/Cmd:0x%02x/Unknown Logpacket",
      this,
      m_Cmd);
  }
  return 1;
}

//----- (00443240) --------------------------------------------------------
void __thiscall CConsoleCMDFactory::StringCMD::StringCMD(
        CConsoleCMDFactory::StringCMD *this,
        const char *szCommand,
        CConsoleCommand *lpCMD)
{
  int v3; // edx
  unsigned int v4; // esi
  const char *i; // edi

  v3 = *(unsigned __int8 *)szCommand;
  v4 = 0;
  for ( i = szCommand; *i; v3 = *(unsigned __int8 *)i )
  {
    ++i;
    v4 = v3 + 65599 * v4;
  }
  this->m_szCommand = szCommand;
  this->m_dwHashValue = v4;
  this->m_lpCMD = lpCMD;
}

//----- (00443280) --------------------------------------------------------
CConsoleCMDFactory::StringCMD *__thiscall std::vector<CTokenlizedFile::ColumnInfo>::size(
        std::vector<CConsoleCMDFactory::StringCMD> *this)
{
  CConsoleCMDFactory::StringCMD *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (CConsoleCMDFactory::StringCMD *)(this->_Mylast - result);
  return result;
}

//----- (004432A0) --------------------------------------------------------
CConsoleCommand *__thiscall CConsoleCMDFactory::Create(
        CConsoleCMDFactory *this,
        char *szCommand,
        unsigned int nCommandLength)
{
  unsigned int i; // edi
  char v5; // al
  CConsoleCMDFactory::StringCMD *Myfirst; // esi
  int v7; // eax

  for ( i = 0; i < nCommandLength; ++i )
  {
    v5 = szCommand[i];
    if ( !v5 )
      break;
    if ( v5 == 32 )
      break;
    if ( v5 == 9 )
      break;
    if ( v5 == 10 )
      break;
  }
  Myfirst = this->m_CMDVector._Myfirst;
  if ( Myfirst == this->m_CMDVector._Mylast )
    return 0;
  while ( 1 )
  {
    _mbsnbcmp((unsigned __int8 *)Myfirst->m_szCommand, (unsigned __int8 *)szCommand, i);
    if ( !v7 )
      break;
    if ( ++Myfirst == this->m_CMDVector._Mylast )
      return 0;
  }
  return Myfirst->m_lpCMD->Clone(Myfirst->m_lpCMD, szCommand, nCommandLength);
}
// 4432E4: variable 'v7' is possibly undefined

//----- (00443310) --------------------------------------------------------
CConsoleCMDFactory::StringCMD *__thiscall std::vector<CConsoleCMDFactory::StringCMD>::_Ufill(
        std::vector<CConsoleCMDFactory::StringCMD> *this,
        CConsoleCMDFactory::StringCMD *_Ptr,
        unsigned int _Count,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (00443340) --------------------------------------------------------
void __thiscall CConsoleCMDFactory::~CConsoleCMDFactory(CConsoleCMDFactory *this)
{
  CConsoleCMDFactory::StringCMD *Mylast; // eax
  CConsoleCMDFactory::StringCMD *Myfirst; // edi

  Mylast = this->m_CMDVector._Mylast;
  Myfirst = this->m_CMDVector._Myfirst;
  this->__vftable = (CConsoleCMDFactory_vtbl *)&CConsoleCMDFactory::`vftable';
  if ( Myfirst != Mylast )
  {
    do
    {
      operator delete(Myfirst->m_lpCMD);
      ++Myfirst;
    }
    while ( Myfirst != this->m_CMDVector._Mylast );
  }
  if ( this->m_CMDVector._Myfirst )
    operator delete(this->m_CMDVector._Myfirst);
  this->m_CMDVector._Myfirst = 0;
  this->m_CMDVector._Mylast = 0;
  this->m_CMDVector._Myend = 0;
  this->m_CMDVector._Myfirst = 0;
  this->m_CMDVector._Mylast = 0;
  this->m_CMDVector._Myend = 0;
}
// 4DF9E4: using guessed type void *CConsoleCMDFactory::`vftable';

//----- (004433A0) --------------------------------------------------------
void __thiscall __noreturn std::vector<CConsoleCMDFactory::StringCMD>::_Xlen(
        std::vector<CConsoleCMDFactory::StringCMD> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00443410) --------------------------------------------------------
CConsoleCMDFactory *__thiscall CConsoleCMDFactory::`vector deleting destructor'(CConsoleCMDFactory *this, char a2)
{
  CConsoleCMDFactory::~CConsoleCMDFactory(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00443430) --------------------------------------------------------
void __thiscall std::vector<CConsoleCMDFactory::StringCMD>::_Insert_n(
        std::vector<CConsoleCMDFactory::StringCMD> *this,
        std::vector<CConsoleCMDFactory::StringCMD>::iterator _Where,
        unsigned int _Count,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  unsigned int m_dwHashValue; // ecx
  const char *m_szCommand; // edx
  CConsoleCMDFactory::StringCMD *Myfirst; // ebx
  CConsoleCommand *m_lpCMD; // eax
  unsigned int v9; // ecx
  int v11; // eax
  int v12; // eax
  unsigned int v13; // ecx
  int v14; // eax
  int v15; // ebx
  CConsoleCMDFactory::StringCMD *v16; // eax
  char *v17; // edi
  CConsoleCMDFactory::StringCMD *Mylast; // ecx
  CConsoleCMDFactory::StringCMD *v20; // edi
  CConsoleCMDFactory::StringCMD *v21; // [esp-18h] [ebp-40h]
  CConsoleCMDFactory::StringCMD *v22; // [esp-Ch] [ebp-34h]
  unsigned int v23; // [esp-8h] [ebp-30h]
  int v24; // [esp+0h] [ebp-28h] BYREF
  CConsoleCMDFactory::StringCMD _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  int *v26; // [esp+18h] [ebp-10h]
  int v27; // [esp+24h] [ebp-4h]
  CConsoleCMDFactory::StringCMD *_Wherea; // [esp+30h] [ebp+8h]
  CConsoleCMDFactory::StringCMD *_Counta; // [esp+34h] [ebp+Ch]
  CConsoleCMDFactory::StringCMD *_Newvec; // [esp+38h] [ebp+10h]
  CConsoleCMDFactory::StringCMD *_Newveca; // [esp+38h] [ebp+10h]

  m_dwHashValue = _Val->m_dwHashValue;
  m_szCommand = _Val->m_szCommand;
  Myfirst = this->_Myfirst;
  m_lpCMD = _Val->m_lpCMD;
  v26 = &v24;
  _Tmp.m_dwHashValue = m_dwHashValue;
  _Tmp.m_szCommand = m_szCommand;
  _Tmp.m_lpCMD = m_lpCMD;
  if ( Myfirst )
    v9 = this->_Myend - Myfirst;
  else
    v9 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( 357913941 - v11 < _Count )
      std::vector<CConsoleCMDFactory::StringCMD>::_Xlen(this);
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( v9 >= _Count + v12 )
    {
      Mylast = this->_Mylast;
      _Newveca = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::_Copy_backward_opt<CTokenlizedFile::ColumnInfo *,CTokenlizedFile::ColumnInfo *>(
          _Where._Myptr,
          _Wherea,
          _Newveca);
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = this->_Mylast;
        v27 = 2;
        std::vector<CConsoleCMDFactory::StringCMD>::_Ufill(this, v22, v23, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          _Where._Myptr,
          &v20[-_Count],
          &_Tmp);
      }
    }
    else
    {
      if ( 357913941 - (v9 >> 1) >= v9 )
        v13 = (v9 >> 1) + v9;
      else
        v13 = 0;
      if ( Myfirst )
        v14 = this->_Mylast - Myfirst;
      else
        v14 = 0;
      if ( v13 < _Count + v14 )
        v13 = (unsigned int)std::vector<CTokenlizedFile::ColumnInfo>::size(this) + _Count;
      v15 = v13;
      _Newvec = (CConsoleCMDFactory::StringCMD *)operator new((tagHeader *)(12 * v13));
      v21 = this->_Myfirst;
      v27 = 0;
      _Counta = std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                  v21,
                  _Where._Myptr,
                  _Newvec);
      std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        _Counta,
        _Count,
        &_Tmp);
      std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        _Where._Myptr,
        this->_Mylast,
        &_Counta[_Count]);
      v16 = this->_Myfirst;
      if ( v16 )
        v16 = (CConsoleCMDFactory::StringCMD *)(this->_Mylast - v16);
      v17 = (char *)v16 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[v15];
      this->_Mylast = &_Newvec[(_DWORD)v17];
      this->_Myfirst = _Newvec;
    }
  }
}

//----- (004436F0) --------------------------------------------------------
std::vector<CConsoleCMDFactory::StringCMD>::iterator *__thiscall std::vector<CConsoleCMDFactory::StringCMD>::insert(
        std::vector<CConsoleCMDFactory::StringCMD> *this,
        std::vector<CConsoleCMDFactory::StringCMD>::iterator *result,
        std::vector<CConsoleCMDFactory::StringCMD>::iterator _Where,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  CConsoleCMDFactory::StringCMD *Myfirst; // esi
  int v6; // esi
  std::vector<CConsoleCMDFactory::StringCMD>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CConsoleCMDFactory::StringCMD>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (00443760) --------------------------------------------------------
void __thiscall CConsoleCMDFactory::CConsoleCMDFactory(CConsoleCMDFactory *this)
{
  this->__vftable = (CConsoleCMDFactory_vtbl *)&CConsoleCMDFactory::`vftable';
  this->m_CMDVector._Myfirst = 0;
  this->m_CMDVector._Mylast = 0;
  this->m_CMDVector._Myend = 0;
}
// 4DF9E4: using guessed type void *CConsoleCMDFactory::`vftable';

//----- (00443780) --------------------------------------------------------
void __thiscall std::vector<CConsoleCMDFactory::StringCMD>::push_back(
        std::vector<CConsoleCMDFactory::StringCMD> *this,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  CConsoleCMDFactory::StringCMD *Myfirst; // edi
  unsigned int v4; // ecx
  CConsoleCMDFactory::StringCMD *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CConsoleCMDFactory::StringCMD>::insert(
      this,
      (std::vector<CConsoleCMDFactory::StringCMD>::iterator *)&_Val,
      (std::vector<CConsoleCMDFactory::StringCMD>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (00443800) --------------------------------------------------------
char __thiscall CConsoleCMDFactory::AddCommand(
        CConsoleCMDFactory *this,
        const char *szCommand,
        CConsoleCommand *lpConsoleCommand)
{
  const CConsoleCMDFactory::StringCMD *v5; // eax
  CConsoleCMDFactory::StringCMD v6; // [esp+4h] [ebp-Ch] BYREF

  if ( !lpConsoleCommand )
    return 0;
  CConsoleCMDFactory::StringCMD::StringCMD(&v6, szCommand, lpConsoleCommand);
  std::vector<CConsoleCMDFactory::StringCMD>::push_back(&this->m_CMDVector, v5);
  return 1;
}
// 44382A: variable 'v5' is possibly undefined

//----- (00443840) --------------------------------------------------------
void __thiscall CValidateConnection::~CValidateConnection(CValidateConnection *this)
{
  this->__vftable = (CValidateConnection_vtbl *)&CValidateConnection::`vftable';
}
// 4DF9E8: using guessed type void *CValidateConnection::`vftable';

//----- (00443850) --------------------------------------------------------
CValidateConnection *__thiscall CValidateConnection::`scalar deleting destructor'(CValidateConnection *this, char a2)
{
  this->__vftable = (CValidateConnection_vtbl *)&CValidateConnection::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4DF9E8: using guessed type void *CValidateConnection::`vftable';

//----- (00443870) --------------------------------------------------------
void __thiscall CLimitUserByIP::OperateMode(CLimitUserByIP *this, CLimitUserByIP::AllowMode_t eAllowMode)
{
  CCSLock *p_m_LimitLock; // edi

  p_m_LimitLock = &this->m_LimitLock;
  EnterCriticalSection(&this->m_LimitLock.m_CSLock);
  this->m_eAllowMode = eAllowMode;
  LeaveCriticalSection(&p_m_LimitLock->m_CSLock);
}

//----- (004438A0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<unsigned long>::iterator>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Mid,
        std::vector<unsigned long>::iterator _Last)
{
  int v4; // eax
  unsigned int v5; // esi
  int v6; // eax
  unsigned int v7; // ebx
  unsigned int v8; // eax
  unsigned int v9; // edx
  unsigned int v10; // edx
  unsigned int v11; // esi
  unsigned int v12; // edx
  unsigned int *v13; // esi
  unsigned int *v14; // esi
  unsigned int *v15; // edi
  unsigned int v16; // ebx
  unsigned int v17; // edi
  unsigned int v18; // ebx
  unsigned int v19; // edi
  unsigned int v20; // esi
  unsigned int v21; // edi
  unsigned int v22; // edx
  unsigned int v23; // edx
  unsigned int v24; // edx
  unsigned int _Lasta; // [esp+14h] [ebp+Ch]
  unsigned int _Lastb; // [esp+14h] [ebp+Ch]
  unsigned int _Lastc; // [esp+14h] [ebp+Ch]
  std::vector<unsigned long>::iterator _Lastd; // [esp+14h] [ebp+Ch]
  unsigned int _Laste; // [esp+14h] [ebp+Ch]

  v4 = _Last._Myptr - _First._Myptr;
  v5 = *_First._Myptr;
  if ( v4 <= 40 )
  {
    v22 = *_Mid._Myptr;
    if ( *_Mid._Myptr < v5 )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v22;
    }
    v23 = *_Last._Myptr;
    if ( *_Last._Myptr < *_Mid._Myptr )
    {
      *_Last._Myptr = *_Mid._Myptr;
      *_Mid._Myptr = v23;
    }
    v24 = *_Mid._Myptr;
    if ( *_Mid._Myptr < *_First._Myptr )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v24;
    }
  }
  else
  {
    v6 = (v4 + 1) / 8;
    v7 = 8 * v6;
    v8 = 4 * v6;
    v9 = _First._Myptr[v8 / 4];
    if ( v9 < v5 )
    {
      _First._Myptr[v8 / 4] = v5;
      *_First._Myptr = v9;
    }
    v10 = _First._Myptr[v7 / 4];
    v11 = _First._Myptr[v8 / 4];
    if ( v10 < v11 )
    {
      _First._Myptr[v7 / 4] = v11;
      _First._Myptr[v8 / 4] = v10;
    }
    v12 = _First._Myptr[v8 / 4];
    if ( v12 < *_First._Myptr )
    {
      _First._Myptr[v8 / 4] = *_First._Myptr;
      *_First._Myptr = v12;
    }
    v13 = &_Mid._Myptr[v8 / 0xFFFFFFFC];
    if ( *_Mid._Myptr < _Mid._Myptr[v8 / 0xFFFFFFFC] )
    {
      _Lasta = *_Mid._Myptr;
      *_Mid._Myptr = *v13;
      *v13 = _Lasta;
    }
    if ( _Mid._Myptr[v8 / 4] < *_Mid._Myptr )
    {
      _Lastb = _Mid._Myptr[v8 / 4];
      _Mid._Myptr[v8 / 4] = *_Mid._Myptr;
      *_Mid._Myptr = _Lastb;
    }
    if ( *_Mid._Myptr < *v13 )
    {
      _Lastc = *_Mid._Myptr;
      *_Mid._Myptr = *v13;
      *v13 = _Lastc;
    }
    v14 = &_Last._Myptr[v8 / 0xFFFFFFFC];
    v15 = &_Last._Myptr[v7 / 0xFFFFFFFC];
    if ( _Last._Myptr[v8 / 0xFFFFFFFC] < _Last._Myptr[v7 / 0xFFFFFFFC] )
    {
      _Lastd._Myptr = (unsigned int *)_Last._Myptr[v8 / 0xFFFFFFFC];
      *v14 = *v15;
      *v15 = (unsigned int)_Lastd._Myptr;
    }
    if ( *_Last._Myptr < *v14 )
    {
      _Laste = *_Last._Myptr;
      *_Last._Myptr = *v14;
      *v14 = _Laste;
    }
    v16 = *v14;
    if ( *v14 < *v15 )
    {
      *v14 = *v15;
      *v15 = v16;
    }
    v17 = *_Mid._Myptr;
    v18 = _First._Myptr[v8 / 4];
    if ( *_Mid._Myptr < v18 )
    {
      *_Mid._Myptr = v18;
      _First._Myptr[v8 / 4] = v17;
    }
    v19 = *v14;
    if ( *v14 < *_Mid._Myptr )
    {
      *v14 = *_Mid._Myptr;
      *_Mid._Myptr = v19;
    }
    v20 = *_Mid._Myptr;
    v21 = _First._Myptr[v8 / 4];
    if ( *_Mid._Myptr < v21 )
    {
      *_Mid._Myptr = v21;
      _First._Myptr[v8 / 4] = v20;
    }
  }
}

//----- (004439E0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<unsigned long>::iterator,int,unsigned long>(
        std::vector<unsigned long>::iterator _First,
        int _Hole,
        int _Bottom,
        unsigned int _Val)
{
  int v4; // esi
  int v5; // eax
  bool i; // zf
  int j; // eax
  unsigned int v8; // edx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5] < _First._Myptr[v5 - 1] )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  for ( j = (v4 - 1) / 2; _Hole < v4; j = (j - 1) / 2 )
  {
    v8 = _First._Myptr[j];
    if ( v8 >= _Val )
      break;
    _First._Myptr[v4] = v8;
    v4 = j;
  }
  _First._Myptr[v4] = _Val;
}

//----- (00443A60) --------------------------------------------------------
std::vector<unsigned long>::iterator *__cdecl std::_Lower_bound<std::vector<unsigned long>::iterator,unsigned long,int>(
        std::vector<unsigned long>::iterator *result,
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last,
        const unsigned int *_Val)
{
  unsigned int *Myptr; // esi
  int v5; // ecx
  int v6; // eax
  std::vector<unsigned long>::iterator *v7; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    v6 = v5 / 2;
    if ( Myptr[v5 / 2] >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v6 + 1;
      v5 += -1 - v6;
    }
  }
  v7 = result;
  result->_Myptr = Myptr;
  return v7;
}

//----- (00443AB0) --------------------------------------------------------
std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> *__cdecl std::_Unguarded_partition<std::vector<unsigned long>::iterator>(
        std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> *result,
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last)
{
  unsigned int *Myptr; // ebx
  unsigned int *v4; // ecx
  unsigned int *i; // esi
  unsigned int v6; // eax
  unsigned int v7; // edx
  unsigned int *v8; // eax
  unsigned int *v9; // ebp
  unsigned int v10; // edi
  bool v11; // zf
  unsigned int *v12; // edx
  unsigned int v13; // edi
  unsigned int v14; // edx
  unsigned int *v15; // edx
  unsigned int v16; // edx
  unsigned int v17; // edi
  unsigned int v18; // edx
  unsigned int v19; // edi
  std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> *v20; // eax
  unsigned int v21; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<unsigned long>::iterator>(
    _First,
    (std::vector<unsigned long>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<unsigned long>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    v6 = *(v4 - 1);
    if ( *v4 > v6 )
      break;
    if ( *v4 < v6 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v7 = *v4;
    do
    {
      if ( v7 > *i )
        break;
      if ( v7 < *i )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v8 = i;
  v9 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v8 < Myptr; ++v8 )
      {
        if ( *v8 <= *v4 )
        {
          if ( *v8 < *v4 )
            break;
          v10 = *i;
          *i = *v8;
          Myptr = _Last._Myptr;
          ++i;
          *v8 = v10;
        }
      }
      v11 = v9 == _First._Myptr;
      if ( v9 > _First._Myptr )
      {
        v12 = v9 - 1;
        do
        {
          if ( *v4 <= *v12 )
          {
            if ( *v4 < *v12 )
              break;
            v13 = *--v4;
            *v4 = *v12;
            *v12 = v13;
          }
          --v9;
          --v12;
        }
        while ( _First._Myptr < v9 );
        Myptr = _Last._Myptr;
        v11 = v9 == _First._Myptr;
      }
      if ( v11 )
        break;
      --v9;
      if ( v8 == Myptr )
      {
        if ( v9 != --v4 )
        {
          v16 = *v9;
          *v9 = *v4;
          *v4 = v16;
        }
        v17 = *(i - 1);
        v18 = *v4;
        --i;
        *v4 = v17;
        *i = v18;
      }
      else
      {
        v19 = *v8;
        *v8 = *v9;
        Myptr = _Last._Myptr;
        ++v8;
        *v9 = v19;
      }
    }
    if ( v8 == Myptr )
      break;
    if ( i != v8 )
    {
      v14 = *v4;
      *v4 = *i;
      *i = v14;
    }
    v15 = v8;
    v21 = *v4;
    *v4 = *v8;
    Myptr = _Last._Myptr;
    ++i;
    ++v8;
    ++v4;
    *v15 = v21;
  }
  v20 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v20;
}

//----- (00443C00) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<unsigned long>::iterator,int,unsigned long>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last)
{
  int i; // esi
  unsigned int v3; // eax

  for ( i = (_Last._Myptr - _First._Myptr) / 2;
        i > 0;
        std::_Adjust_heap<std::vector<unsigned long>::iterator,int,unsigned long>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          v3) )
  {
    v3 = _First._Myptr[--i];
  }
}

//----- (00443C40) --------------------------------------------------------
void __thiscall CLimitUserByIP::~CLimitUserByIP(CLimitUserByIP *this)
{
  this->__vftable = (CLimitUserByIP_vtbl *)&CLimitUserByIP::`vftable';
  if ( this->m_AllowIPList._Myfirst )
    operator delete(this->m_AllowIPList._Myfirst);
  this->m_AllowIPList._Myfirst = 0;
  this->m_AllowIPList._Mylast = 0;
  this->m_AllowIPList._Myend = 0;
  DeleteCriticalSection(&this->m_LimitLock.m_CSLock);
  this->__vftable = (CLimitUserByIP_vtbl *)&CValidateConnection::`vftable';
}
// 4DF9E8: using guessed type void *CValidateConnection::`vftable';
// 4DF9F0: using guessed type void *CLimitUserByIP::`vftable';

//----- (00443C80) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<unsigned long>::iterator>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last)
{
  unsigned int *i; // esi
  unsigned int v3; // ecx
  unsigned int *v4; // eax
  unsigned int v5; // ebp
  std::vector<unsigned long>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      v3 = *i;
      if ( *i >= *_First._Myptr )
      {
        v4 = i - 1;
        if ( v3 < *(i - 1) )
        {
          do
          {
            v5 = *(v4 - 1);
            v6._Myptr = v4--;
          }
          while ( v3 < v5 );
          if ( v6._Myptr != i )
            std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
              v6,
              (std::vector<unsigned long>::iterator)i,
              (std::vector<unsigned long>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          _First,
          (std::vector<unsigned long>::iterator)i,
          (std::vector<unsigned long>::iterator)(i + 1));
      }
    }
  }
}

//----- (00443D00) --------------------------------------------------------
CLimitUserByIP *__thiscall CLimitUserByIP::`vector deleting destructor'(CLimitUserByIP *this, char a2)
{
  CLimitUserByIP::~CLimitUserByIP(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00443D20) --------------------------------------------------------
BOOL __cdecl std::binary_search<std::vector<unsigned long>::iterator,unsigned long>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last,
        const unsigned int *_Val)
{
  unsigned int *Myptr; // esi
  const unsigned int *v4; // edi

  Myptr = _Last._Myptr;
  v4 = _Val;
  std::_Lower_bound<std::vector<unsigned long>::iterator,unsigned long,int>(&_Last, _First, _Last, _Val);
  return _Last._Myptr != Myptr && *v4 >= *_Last._Myptr;
}

//----- (00443D60) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<unsigned long>::iterator>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last)
{
  int i; // esi
  unsigned int v3; // [esp-4h] [ebp-Ch]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 2 > 1; i -= 4 )
  {
    v3 = *(unsigned int *)((char *)_First._Myptr + i - 4);
    *(unsigned int *)((char *)_First._Myptr + i - 4) = *_First._Myptr;
    std::_Adjust_heap<std::vector<unsigned long>::iterator,int,unsigned long>(_First, 0, (i - 4) >> 2, v3);
  }
}

//----- (00443DB0) --------------------------------------------------------
char __thiscall CLimitUserByIP::operator()(CLimitUserByIP *this, INET_Addr *localAddr, INET_Addr *remoteAddr)
{
  CCSLock *p_m_LimitLock; // edi
  CLimitUserByIP::AllowMode_t m_eAllowMode; // eax
  bool v7; // bl

  p_m_LimitLock = &this->m_LimitLock;
  EnterCriticalSection(&this->m_LimitLock.m_CSLock);
  m_eAllowMode = this->m_eAllowMode;
  if ( m_eAllowMode )
  {
    if ( m_eAllowMode == ALLOW_SOME )
    {
      v7 = std::binary_search<std::vector<unsigned long>::iterator,unsigned long>(
             (std::vector<unsigned long>::iterator)this->m_AllowIPList._Myfirst,
             (std::vector<unsigned long>::iterator)this->m_AllowIPList._Mylast,
             (const unsigned int *)&remoteAddr->m_SockAddr.sa_data[2]);
      LeaveCriticalSection(&p_m_LimitLock->m_CSLock);
      return v7;
    }
    else
    {
      LeaveCriticalSection(&p_m_LimitLock->m_CSLock);
      return 0;
    }
  }
  else
  {
    LeaveCriticalSection(&p_m_LimitLock->m_CSLock);
    return 1;
  }
}

//----- (00443E20) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<unsigned long>::iterator,int>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last,
        int _Ideal)
{
  unsigned int *Myptr; // ebx
  unsigned int *v4; // edi
  int v5; // eax
  unsigned int *v7; // ebp
  std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<unsigned long>::iterator>(
        (std::vector<unsigned long>::iterator)Myptr,
        (std::vector<unsigned long>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<unsigned long>::iterator>(
        &_Mid,
        (std::vector<unsigned long>::iterator)Myptr,
        (std::vector<unsigned long>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFC) >= (int)(((char *)v4
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFC) )
      {
        std::_Sort<std::vector<unsigned long>::iterator,int>(
          _Mid.second,
          (std::vector<unsigned long>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<unsigned long>::iterator,int>(
          (std::vector<unsigned long>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v4 - (char *)Myptr) & 0xFFFFFFFC) > 4 )
      std::_Make_heap<std::vector<unsigned long>::iterator,int,unsigned long>(
        (std::vector<unsigned long>::iterator)Myptr,
        (std::vector<unsigned long>::iterator)v4);
    std::sort_heap<std::vector<unsigned long>::iterator>(
      (std::vector<unsigned long>::iterator)Myptr,
      (std::vector<unsigned long>::iterator)v4);
  }
}
// 443EBD: conditional instruction was optimized away because eax.4>=21

//----- (00443EF0) --------------------------------------------------------
void __thiscall std::vector<unsigned long>::reserve(std::vector<unsigned long> *this, unsigned int _Count)
{
  unsigned int *Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  unsigned int *v6; // edi
  unsigned int *v7; // eax
  unsigned int *v8; // [esp-18h] [ebp-38h]
  unsigned int *Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  unsigned int *_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x3FFFFFFF )
    std::vector<unsigned long>::_Xlen((std::vector<CCellManager::SafetyZoneInfo *> *)this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(4 * _Count);
    v6 = (unsigned int *)operator new(_Counta);
    Mylast = this->_Mylast;
    v8 = this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
      (std::vector<unsigned long>::iterator)v8,
      (std::vector<unsigned long>::iterator)Mylast,
      v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (unsigned int *)((char *)&_Counta->bitvEntryHi + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (00443FC0) --------------------------------------------------------
char __thiscall CLimitUserByIP::LoadAllowIPList(CLimitUserByIP *this, const char *szFileName)
{
  _iobuf *v3; // edi
  unsigned int *Myfirst; // eax
  unsigned int v5; // eax
  unsigned int *v6; // edi
  unsigned int v7; // edx
  unsigned int *Mylast; // ecx
  _iobuf *lpFile; // [esp+Ch] [ebp-21Ch]
  unsigned int dwAddr; // [esp+10h] [ebp-218h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+14h] [ebp-214h]
  char szBuffer[512]; // [esp+18h] [ebp-210h] BYREF
  int v14; // [esp+224h] [ebp-4h]

  v3 = fopen(szFileName, "rt");
  lpFile = v3;
  if ( !v3 )
    return 0;
  sync.m_Lock = &this->m_LimitLock;
  EnterCriticalSection(&this->m_LimitLock.m_CSLock);
  Myfirst = this->m_AllowIPList._Myfirst;
  v14 = 0;
  if ( Myfirst )
    operator delete(Myfirst);
  this->m_AllowIPList._Myfirst = 0;
  this->m_AllowIPList._Mylast = 0;
  this->m_AllowIPList._Myend = 0;
  std::vector<unsigned long>::reserve(&this->m_AllowIPList, 0x100u);
  while ( fgets(szBuffer, 511, v3) )
  {
    szBuffer[511] = 0;
    v5 = inet_addr(szBuffer);
    dwAddr = v5;
    if ( v5 )
    {
      v6 = this->m_AllowIPList._Myfirst;
      if ( v6 )
        v7 = this->m_AllowIPList._Mylast - v6;
      else
        v7 = 0;
      if ( v6 && v7 < this->m_AllowIPList._Myend - v6 )
      {
        Mylast = this->m_AllowIPList._Mylast;
        *Mylast = v5;
        this->m_AllowIPList._Mylast = Mylast + 1;
      }
      else
      {
        std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
          (std::vector<CCellManager::SafetyZoneInfo *> *)&this->m_AllowIPList,
          (std::vector<CCellManager::SafetyZoneInfo *>::iterator)this->m_AllowIPList._Mylast,
          1u,
          (CCellManager::SafetyZoneInfo *const *)&dwAddr);
      }
      v3 = lpFile;
    }
  }
  std::_Sort<std::vector<unsigned long>::iterator,int>(
    (std::vector<unsigned long>::iterator)this->m_AllowIPList._Myfirst,
    (std::vector<unsigned long>::iterator)this->m_AllowIPList._Mylast,
    this->m_AllowIPList._Mylast - this->m_AllowIPList._Myfirst);
  fclose(v3);
  LeaveCriticalSection(&sync.m_Lock->m_CSLock);
  return 1;
}

//----- (00444140) --------------------------------------------------------
void __thiscall CLimitUserByIP::CLimitUserByIP(CLimitUserByIP *this, const char *szFileName)
{
  this->m_nRefCount = 1;
  this->__vftable = (CLimitUserByIP_vtbl *)&CLimitUserByIP::`vftable';
  InitializeCriticalSection(&this->m_LimitLock.m_CSLock);
  this->m_AllowIPList._Myfirst = 0;
  this->m_AllowIPList._Mylast = 0;
  this->m_AllowIPList._Myend = 0;
  this->m_eAllowMode = ALLOW_ALL;
  if ( szFileName )
  {
    if ( CLimitUserByIP::LoadAllowIPList(this, szFileName) )
      this->m_eAllowMode = ALLOW_SOME;
  }
}
// 4DF9F0: using guessed type void *CLimitUserByIP::`vftable';

//----- (004441E0) --------------------------------------------------------
void __thiscall CRegularAgentDispatch::CRegularAgentDispatch(CRegularAgentDispatch *this, CSession *Session)
{
  CRylServerDispatch::CRylServerDispatch(this, Session, 0xAu);
  this->__vftable = (CRegularAgentDispatch_vtbl *)&CRegularAgentDispatch::`vftable';
  this->m_cGroup = 0;
}
// 4DFA18: using guessed type void *CRegularAgentDispatch::`vftable';

//----- (00444200) --------------------------------------------------------
void __thiscall CRegularAgentDispatch::~CRegularAgentDispatch(CRegularAgentDispatch *this)
{
  this->__vftable = (CRegularAgentDispatch_vtbl *)&CRegularAgentDispatch::`vftable';
  CRylServerDispatch::~CRylServerDispatch(this);
}
// 4DFA18: using guessed type void *CRegularAgentDispatch::`vftable';

//----- (00444210) --------------------------------------------------------
char __cdecl CRegularAgentDispatch::Initialize()
{
  _iobuf *v0; // esi
  _iobuf *v1; // eax
  _iobuf *v2; // edi
  const char *v3; // eax
  int v4; // ebx
  int v5; // esi
  const char *v6; // eax
  const char *v7; // eax
  char *v8; // eax
  unsigned __int8 v9; // cl
  _iobuf *lpGroupFile; // [esp+10h] [ebp-420h]
  _iobuf *lpIPFile; // [esp+14h] [ebp-41Ch]
  char szGroupFileName[264]; // [esp+18h] [ebp-418h] BYREF
  char szIPFileName[264]; // [esp+120h] [ebp-310h] BYREF
  char szIPBuffer[256]; // [esp+228h] [ebp-208h] BYREF
  char szGroupBuffer[260]; // [esp+328h] [ebp-108h] BYREF

  strcpy(szGroupFileName, "./Script/Server/ServerGroup.txt");
  memset(&szGroupFileName[32], 0, 0xE4u);
  strcpy(szIPFileName, "./Script/Server/ServerIP.txt");
  memset(&szIPFileName[29], 0, 231);
  v0 = fopen(szGroupFileName, "rt");
  lpGroupFile = v0;
  v1 = fopen(szIPFileName, "rt");
  v2 = v1;
  lpIPFile = v1;
  if ( v0 && v1 )
  {
    do
    {
      if ( !fgets(szGroupBuffer, 256, v0) )
        break;
      if ( !fgets(szIPBuffer, 256, v2) )
        break;
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CRegularAgentDispatch::Initialize",
        aDWorkRylSource_5,
        85,
        "Read Address : %s",
        szIPBuffer);
      v3 = strtok(szIPBuffer, "\n:");
      v4 = atoi(v3);
      v5 = v4;
      CRegularAgentDispatch::ms_AgentServerInfo[v4].m_ServerUID.sID.Group = v4;
      v6 = strtok(0, "\n:");
      CRegularAgentDispatch::ms_AgentServerInfo[v5].m_ServerAddress.S_un.S_addr = inet_addr(v6);
      CRegularAgentDispatch::ms_AgentServerInfo[v5].m_dwChannelClientNum[1] = 2;
      CRegularAgentDispatch::ms_AgentServerInfo[v5].m_usChannelNum = 2;
      CRegularAgentDispatch::ms_AgentServerInfo[v5].m_dwChannelClientNum[0] = 1;
      v7 = strtok(szGroupBuffer, "\n");
      strncpy(CRegularAgentDispatch::ms_AgentServerInfo[v4].m_szGroupName, v7, 0x77u);
      CRegularAgentDispatch::ms_AgentServerInfo[v5].m_szGroupName[119] = 0;
      v8 = &CRegularAgentDispatch::ms_AgentServerInfo[v4].m_szGroupName[strlen(CRegularAgentDispatch::ms_AgentServerInfo[v4].m_szGroupName)
                                                                      + 1];
      v2 = lpIPFile;
      v9 = CRegularAgentDispatch::ms_cCurrentGroupNum + 1;
      CRegularAgentDispatch::ms_AgentServerInfo[v4].m_nGroupNameLen = v8
                                                                    - &CRegularAgentDispatch::ms_AgentServerInfo[v4].m_szGroupName[1];
      v0 = lpGroupFile;
      CRegularAgentDispatch::ms_cCurrentGroupNum = v9;
    }
    while ( v4 < 10 );
    fclose(v0);
    fclose(v2);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRegularAgentDispatch::Initialize",
      aDWorkRylSource_5,
      72,
      "Cannot load server group/IP file  : %s or %s",
      szGroupFileName,
      szIPFileName);
    return 0;
  }
}

//----- (00444440) --------------------------------------------------------
void __thiscall CRegularAgentDispatch::Connected(CRegularAgentDispatch *this)
{
  int v2; // ebx
  in_addr *p_m_ServerAddress; // esi
  char *Buffer; // esi
  SERVER_ID serverID; // [esp+Ch] [ebp-A8h]
  CTCPFactory tcpFactory; // [esp+10h] [ebp-A4h] BYREF
  char szAddress[128]; // [esp+24h] [ebp-90h] BYREF
  int v8; // [esp+B0h] [ebp-4h]

  v2 = 0;
  p_m_ServerAddress = &CRegularAgentDispatch::ms_AgentServerInfo[0].m_ServerAddress;
  do
  {
    if ( p_m_ServerAddress->S_un.S_addr == *(_DWORD *)&CRylServerDispatch::GetRemoteAddr(this)->m_SockAddr.sa_data[2] )
      break;
    p_m_ServerAddress += 63;
    ++v2;
  }
  while ( (int)p_m_ServerAddress < (int)&multiDispatch.m_DispatchLock.m_CSLock.LockCount );
  if ( v2 == 10 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRegularAgentDispatch::Connected",
      aDWorkRylSource_5,
      122,
      (char *)&byte_4DFBD8);
    CRylServerDispatch::Shutdown(this);
  }
  else
  {
    Buffer = CSendStream::GetBuffer(&this->m_SendStream, (char *)0x14);
    if ( Buffer )
    {
      CTCPFactory::CTCPFactory(&tcpFactory);
      v8 = 0;
      CINETFamilyFactory::GetNetworkInfo(&tcpFactory, szAddress, 128);
      *((_DWORD *)Buffer + 4) = inet_addr(szAddress);
      serverID.sID.Type = 5;
      serverID.sID.Group = v2;
      CServerSetup::GetInstance();
      serverID.sID.Channel = CServerSetup::GetChannelFromCmdLine() + 2;
      serverID.sID.ID = 7;
      *((SERVER_ID *)Buffer + 3) = serverID;
      if ( CSendStream::WrapHeader(&this->m_SendStream, 0x14u, 0x28u, 0, 0) )
        CServerLog::SimpleLog(&g_Log, 2, (char *)&byte_4DFB78, this, serverID.dwID);
    }
  }
}

//----- (00444590) --------------------------------------------------------
CMultiDispatch *__cdecl CRegularAgentDispatch::GetDispatchTable()
{
  if ( (_S7_13 & 1) == 0 )
  {
    _S7_13 |= 1u;
    CMultiDispatch::CMultiDispatch(&multiDispatch);
    atexit(_E8_2);
  }
  return &multiDispatch;
}

//----- (004445F0) --------------------------------------------------------
CRegularAgentDispatch *__thiscall CRegularAgentDispatch::`scalar deleting destructor'(
        CRegularAgentDispatch *this,
        char a2)
{
  CRegularAgentDispatch::~CRegularAgentDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00444610) --------------------------------------------------------
void __thiscall CRegularAgentDispatch::Disconnected(CRegularAgentDispatch *this)
{
  CMultiDispatch *DispatchTable; // eax
  unsigned int m_cGroup; // [esp-4h] [ebp-8h]

  CServerLog::SimpleLog(&g_Log, 2, (char *)&byte_4DFC08, this, this->m_cGroup);
  m_cGroup = this->m_cGroup;
  DispatchTable = CRegularAgentDispatch::GetDispatchTable();
  CMultiDispatch::RemoveDispatch(DispatchTable, m_cGroup);
}

//----- (00444640) --------------------------------------------------------
char __thiscall CRegularAgentDispatch::ParseServerLogin(CRegularAgentDispatch *this, PktBase *lpPktBase)
{
  int v2; // eax
  CMultiDispatch *DispatchTable; // eax
  struct in_addr *v5; // eax
  char *v6; // eax
  struct in_addr *RemoteAddr; // eax
  char *v9; // eax
  unsigned int v10; // [esp-8h] [ebp-Ch]

  v2 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  v10 = SBYTE1(v2);
  this->m_cGroup = BYTE1(v2);
  DispatchTable = CRegularAgentDispatch::GetDispatchTable();
  if ( CMultiDispatch::SetDispatch(DispatchTable, v10, this) )
  {
    RemoteAddr = (struct in_addr *)CRylServerDispatch::GetRemoteAddr(this);
    v9 = inet_ntoa(RemoteAddr[1]);
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CRegularAgentDispatch::ParseServerLogin",
      aDWorkRylSource_5,
      197,
      "DP:0x%p/IP:%s/Servergroup login : %d",
      this,
      v9,
      this->m_cGroup);
    return 1;
  }
  else
  {
    v5 = (struct in_addr *)CRylServerDispatch::GetRemoteAddr(this);
    v6 = inet_ntoa(v5[1]);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRegularAgentDispatch::ParseServerLogin",
      aDWorkRylSource_5,
      191,
      "DP:0x%p/IP:%s/Duplicated servergroup login : %d",
      this,
      v6,
      this->m_cGroup);
    return 0;
  }
}

//----- (004446F0) --------------------------------------------------------
char __cdecl RegularAgentPacketParse::SendGetCharData(
        unsigned int dwUID,
        unsigned int dwSlotCID,
        const char *szSlotName,
        unsigned int dwCID,
        unsigned __int8 cGroup)
{
  CMultiDispatch *DispatchTable; // eax
  CSendStream *v7; // edi
  char *Buffer; // esi
  CMultiDispatch::Storage StoragelpDispatch; // [esp+4h] [ebp-14h] BYREF
  int v10; // [esp+14h] [ebp-4h]

  DispatchTable = CRegularAgentDispatch::GetDispatchTable();
  CMultiDispatch::Storage::Storage(&StoragelpDispatch, DispatchTable, cGroup);
  v10 = 0;
  if ( StoragelpDispatch.m_lpDispatch )
  {
    v7 = (CSendStream *)&StoragelpDispatch.m_lpDispatch[8];
    Buffer = CSendStream::GetBuffer((CSendStream *)&StoragelpDispatch.m_lpDispatch[8], (char *)0x29);
    if ( Buffer
      && (*((_DWORD *)Buffer + 8) = dwUID,
          Buffer[40] = 20,
          *((_DWORD *)Buffer + 9) = dwSlotCID,
          strncpy(Buffer + 12, szSlotName, 0x10u),
          *((_DWORD *)Buffer + 7) = dwCID,
          CSendStream::WrapHeader(v7, 0x29u, 0x76u, 0, 0)) )
    {
      v10 = -1;
      CSingleDispatch::Storage::~Storage(&StoragelpDispatch);
      return 1;
    }
    else
    {
      v10 = -1;
      CSingleDispatch::Storage::~Storage(&StoragelpDispatch);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "RegularAgentPacketParse::SendGetCharData",
      aDWorkRylSource_5,
      449,
      aCid0x08x_220,
      dwCID,
      cGroup);
    v10 = -1;
    CSingleDispatch::Storage::~Storage(&StoragelpDispatch);
    return 0;
  }
}

//----- (00444820) --------------------------------------------------------
void __thiscall boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::~object_pool<CTempCharacter,boost::default_user_allocator_new_delete>(
        boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete> *this)
{
  char *ptr; // esi
  unsigned int v3; // ebx
  unsigned int requested_size; // eax
  char *v5; // edi
  unsigned int v6; // ecx
  int v7; // edx
  char *v8; // eax
  unsigned int v9; // edi
  int v10; // ebx
  CPacketDispatch *v11; // edi
  char *v12; // eax
  unsigned int sz; // ecx
  char *v14; // esi
  unsigned int v15; // edi
  unsigned int partition_size; // [esp+Ch] [ebp-30h]
  unsigned int partition_sizea; // [esp+Ch] [ebp-30h]
  CPacketDispatch *freed_iter; // [esp+10h] [ebp-2Ch]
  char *next; // [esp+18h] [ebp-24h]
  unsigned int next_4; // [esp+1Ch] [ebp-20h]
  char *v21; // [esp+20h] [ebp-1Ch]
  unsigned int v22; // [esp+24h] [ebp-18h]
  unsigned int iter_4; // [esp+2Ch] [ebp-10h]

  if ( this->list.ptr )
  {
    ptr = this->list.ptr;
    iter_4 = this->list.sz;
    v3 = iter_4;
    requested_size = this->requested_size;
    freed_iter = (CPacketDispatch *)this->first;
    v5 = ptr;
    v6 = 4;
    do
    {
      v7 = requested_size % v6;
      partition_size = v6;
      requested_size = v6;
      v6 = v7;
    }
    while ( v7 );
    partition_sizea = 4 * (this->requested_size / partition_size);
    while ( 1 )
    {
      v8 = *(char **)&v5[v3 - 8];
      v9 = *(_DWORD *)&v5[v3 - 4];
      v10 = (int)&ptr[iter_4 - 8];
      v22 = v9;
      next_4 = v9;
      v21 = v8;
      next = v8;
      v11 = (CPacketDispatch *)ptr;
      if ( ptr != (char *)v10 )
      {
        do
        {
          if ( v11 == freed_iter )
            freed_iter = (CPacketDispatch *)freed_iter->__vftable;
          else
            CSymbolTable::Create(v11);
          v11 = (CPacketDispatch *)((char *)v11 + partition_sizea);
        }
        while ( v11 != (CPacketDispatch *)v10 );
      }
      operator delete[](ptr);
      ptr = v21;
      iter_4 = v22;
      if ( !v21 )
        break;
      v5 = next;
      v3 = next_4;
    }
    this->list.ptr = 0;
  }
  v12 = this->list.ptr;
  sz = this->list.sz;
  if ( v12 )
  {
    do
    {
      v14 = *(char **)&v12[sz - 8];
      v15 = *(_DWORD *)&v12[sz - 4];
      operator delete[](v12);
      v12 = v14;
      sz = v15;
    }
    while ( v14 );
    this->list.ptr = 0;
    this->first = 0;
  }
}

//----- (00444950) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v17; // esi
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  v17 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v17->mtx);
  _Erasednode->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v17->p.first;
  v17->p.first = _Erasednode;
  LeaveCriticalSection(&v17->mtx);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v19 = result;
  result->_Ptr = _Where._Ptr;
  return v19;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00444C20) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00444C60) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00444D20) --------------------------------------------------------
void __thiscall std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00444D70) --------------------------------------------------------
void __thiscall CTempCharacterMgr::CTempCharacterMgr(CTempCharacterMgr *this)
{
  std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_mapTempChar; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v2; // eax

  this->m_tempCharPool.first = 0;
  this->m_tempCharPool.list.ptr = 0;
  this->m_tempCharPool.list.sz = 0;
  this->m_tempCharPool.requested_size = 444;
  this->m_tempCharPool.next_size = 32;
  p_m_mapTempChar = &this->m_mapTempChar;
  v2 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode(&this->m_mapTempChar);
  p_m_mapTempChar->_Myhead = v2;
  v2->_Isnil = 1;
  p_m_mapTempChar->_Myhead->_Parent = p_m_mapTempChar->_Myhead;
  p_m_mapTempChar->_Myhead->_Left = p_m_mapTempChar->_Myhead;
  p_m_mapTempChar->_Myhead->_Right = p_m_mapTempChar->_Myhead;
  p_m_mapTempChar->_Mysize = 0;
}

//----- (00444DF0) --------------------------------------------------------
CTempCharacterMgr *__cdecl CRegularAgentDispatch::GetTempCharacterMgr()
{
  if ( (_S9_3 & 1) == 0 )
  {
    _S9_3 |= 1u;
    CTempCharacterMgr::CTempCharacterMgr(&tempCharacterMgr);
    atexit(_E10_0);
  }
  return &tempCharacterMgr;
}

//----- (00444E50) --------------------------------------------------------
char __cdecl RegularAgentPacketParse::ParseGetCharSlot(PktBase *lpPktBase)
{
  PktBase *v1; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v2; // ebx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // edi
  CTempCharacterMgr *TempCharacterMgr; // eax
  CTempCharacter *v7; // esi
  CDBSingleObject *v8; // eax
  int v9; // edi
  _DWORD *v10; // eax
  PktBase *v11; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  CDBSingleObject *v13; // eax
  unsigned int m_dwUID; // [esp-Ch] [ebp-24h]
  unsigned __int8 cGroup; // [esp+Ch] [ebp-Ch]
  CCharacter *lpCharacter; // [esp+10h] [ebp-8h]

  v1 = lpPktBase;
  v2 = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node **)&lpPktBase[1].m_StartBit;
  cGroup = lpPktBase[1].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, (unsigned int)v2);
  lpCharacter = Character;
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "RegularAgentPacketParse::ParseGetCharSlot",
      aDWorkRylSource_5,
      215,
      aCid0x08x_297,
      v2);
    return 0;
  }
  TempCharacterMgr = CRegularAgentDispatch::GetTempCharacterMgr();
  v7 = CTempCharacterMgr::GetCharacter(TempCharacterMgr, v2, cGroup);
  if ( v7 )
  {
    m_dwUID = Character->m_dwUID;
    lpPktBase = 0;
    v8 = CDBSingleObject::GetInstance();
    if ( !CDBComponent::GetUIDFromBattleUID(v8, (int)v2, m_dwUID, (unsigned int *)&lpPktBase) )
    {
      v13 = CDBSingleObject::GetInstance();
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "RegularAgentPacketParse::ParseGetCharSlot",
        aDWorkRylSource_5,
        262,
        aUid10uCid10u_1,
        Character->m_dwUID,
        v2,
        v13->m_ErrorString);
      return 1;
    }
    v9 = 0;
    v10 = (unsigned int *)((char *)&v1[1].m_CodePage + 1);
    while ( !*v10 )
    {
      ++v9;
      ++v10;
      if ( v9 >= 3 )
        goto LABEL_11;
    }
    v7->m_dwUID = (unsigned int)lpPktBase;
    v7->m_dwCID = *(unsigned int *)((char *)&v1[1].m_CodePage + 4 * v9 + 1);
    strncpy(v7->m_szCharacterName, (const char *)&v1[2].m_CodePage + 16 * v9 + 1, 0x10u);
    v7->m_cFlag |= 1u;
    v11 = lpPktBase;
    LOBYTE(v7->m_CharInfoEX.Total) = 0;
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "RegularAgentPacketParse::ParseGetCharSlot",
      aDWorkRylSource_5,
      236,
      aUid10uCid10u,
      lpCharacter->m_dwUID,
      v2,
      v11,
      *(unsigned int *)((char *)&v1[1].m_CodePage + 4 * v9 + 1),
      cGroup);
    if ( RegularAgentPacketParse::SendGetCharData(
           (unsigned int)lpPktBase,
           *(unsigned int *)((char *)&v1[1].m_CodePage + 4 * v9 + 1),
           (const char *)&v1[2].m_CodePage + 16 * v9 + 1,
           (unsigned int)v2,
           cGroup) )
    {
      ++v7->m_nDataRequestCount;
    }
LABEL_11:
    if ( v9 == 3 )
    {
      m_lpGameClientDispatch = (CSendStream *)lpCharacter->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        GameClientSendPacket::SendCharBGServerMileageChange(
          m_lpGameClientDispatch + 8,
          (unsigned int)v2,
          cGroup,
          1u,
          0,
          lpCharacter->m_DBData.m_Info.Mileage,
          6u);
        return 1;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "RegularAgentPacketParse::ParseGetCharSlot",
      aDWorkRylSource_5,
      267,
      aUid10uCid10u_3,
      Character->m_dwUID,
      v2);
  }
  return 1;
}

//----- (00445060) --------------------------------------------------------
char __cdecl RegularAgentPacketParse::ParseGetCharData(PktBase *lpPktBase, unsigned __int8 cGroup)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v2; // ebx
  unsigned int m_CodePage; // esi
  ServerInfo m_SrvInfo; // edi
  unsigned __int8 *p_m_Cmd; // ebp
  CTempCharacterMgr *TempCharacterMgr; // eax
  CTempCharacter *Character; // eax
  _DWORD *v8; // ebp
  const void *v9; // esi
  int v10; // ecx
  int v11; // edx
  unsigned int Gold; // edi
  CCreatureManager *Instance; // eax
  CCharacter *v14; // eax
  CCharacter *v15; // esi
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int dwCID; // [esp+10h] [ebp-8h]
  unsigned int dwUID; // [esp+14h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+1Ch] [ebp+4h]

  v2 = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node **)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  m_SrvInfo = lpPktBase[1].m_SrvInfo;
  p_m_Cmd = &lpPktBase[2].m_Cmd;
  dwUID = m_CodePage;
  dwCID = m_SrvInfo.dwServerInfo;
  lpPktBasea = lpPktBase[2].m_StartBit;
  TempCharacterMgr = CRegularAgentDispatch::GetTempCharacterMgr();
  Character = CTempCharacterMgr::GetCharacter(TempCharacterMgr, v2, cGroup);
  if ( Character )
  {
    if ( lpPktBasea == 11 )
    {
      Character->m_dwUID = m_CodePage;
      Character->m_dwCID = m_SrvInfo.dwServerInfo;
      Character->m_CharInfoEX.Total = *(_DWORD *)p_m_Cmd;
      Character->m_CharInfoEX.ServerID = *((_DWORD *)p_m_Cmd + 1);
      v8 = p_m_Cmd + 8;
      qmemcpy(&Character->m_Quest, v8, sizeof(Character->m_Quest));
      v9 = (char *)v8 + 134;
      v8 = (_DWORD *)((char *)v8 + 338);
      qmemcpy(&Character->m_History, v9, sizeof(Character->m_History));
      qmemcpy(&Character->m_Config, v8, sizeof(Character->m_Config));
      v10 = *(_DWORD *)((char *)v8 + 54);
      v8 = (_DWORD *)((char *)v8 + 54);
      *(_DWORD *)Character->m_StoreInfo.Password = v10;
      *(_DWORD *)&Character->m_StoreInfo.Password[4] = v8[1];
      *(unsigned int *)((char *)&Character->m_StoreInfo.Flag + 3) = v8[2];
      HIBYTE(Character->m_StoreInfo.Gold) = *((_BYTE *)v8 + 12);
      v11 = Character->m_nDataRequestCount - 1;
      Character->m_cFlag |= 2u;
      Character->m_nDataRequestCount = v11;
      Gold = Character->m_StoreInfo.Gold;
      Instance = CCreatureManager::GetInstance();
      v14 = CCreatureManager::GetCharacter(Instance, (unsigned int)v2);
      v15 = v14;
      if ( v14 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "RegularAgentPacketParse::ParseGetCharData",
          aDWorkRylSource_5,
          322,
          aUid10uCid10u_0,
          v14->m_dwUID,
          v14->m_dwCID,
          dwUID,
          dwCID,
          cGroup,
          Gold);
        m_lpGameClientDispatch = (CSendStream *)v15->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          GameClientSendPacket::SendCharBGServerMileageChange(
            m_lpGameClientDispatch + 8,
            (unsigned int)v2,
            cGroup,
            1u,
            Gold,
            v15->m_DBData.m_Info.Mileage,
            0);
      }
    }
  }
  return 1;
}

//----- (004451C0) --------------------------------------------------------
char __cdecl RegularAgentPacketParse::SendGetCharSlot(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwCID,
        char cGroup)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // ebx
  CMultiDispatch *DispatchTable; // eax
  CTempCharacterMgr *TempCharacterMgr; // eax
  CTempCharacter *v7; // eax
  CTempCharacter *v8; // edi
  CDBSingleObject *v9; // eax
  CSendStream *p_m_SendStream; // edi
  char *Buffer; // eax
  char v12; // bl
  CDBSingleObject *v13; // eax
  unsigned int m_dwUID; // [esp-Ch] [ebp-34h]
  unsigned int dwTempUID; // [esp+Ch] [ebp-1Ch] BYREF
  CRegularAgentDispatch *lpDispatch; // [esp+10h] [ebp-18h]
  CMultiDispatch::Storage StoragelpDispatch; // [esp+14h] [ebp-14h] BYREF
  int v18; // [esp+24h] [ebp-4h]

  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, (unsigned int)dwCID);
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "RegularAgentPacketParse::SendGetCharSlot",
      aDWorkRylSource_5,
      344,
      aCid0x08x_297,
      dwCID);
    return 0;
  }
  DispatchTable = CRegularAgentDispatch::GetDispatchTable();
  CMultiDispatch::Storage::Storage(&StoragelpDispatch, DispatchTable, (unsigned __int8)cGroup);
  v18 = 0;
  lpDispatch = (CRegularAgentDispatch *)StoragelpDispatch.m_lpDispatch;
  if ( StoragelpDispatch.m_lpDispatch )
  {
    dwTempUID = 0;
    TempCharacterMgr = CRegularAgentDispatch::GetTempCharacterMgr();
    v7 = CTempCharacterMgr::GetCharacter(TempCharacterMgr, dwCID, cGroup);
    v8 = v7;
    if ( v7 )
    {
      if ( (v7->m_cFlag & 1) != 0 )
      {
        if ( RegularAgentPacketParse::SendGetCharData(
               v7->m_dwUID,
               v7->m_dwCID,
               v7->m_szCharacterName,
               (unsigned int)dwCID,
               cGroup) )
        {
          ++v8->m_nDataRequestCount;
        }
      }
      else
      {
        m_dwUID = Character->m_dwUID;
        v9 = CDBSingleObject::GetInstance();
        if ( CDBComponent::GetUIDFromBattleUID(v9, (int)Character, m_dwUID, &dwTempUID) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "RegularAgentPacketParse::SendGetCharSlot",
            aDWorkRylSource_5,
            416,
            aUid10uCid10u_2,
            Character->m_dwUID,
            dwCID,
            dwTempUID,
            (unsigned __int8)cGroup);
          p_m_SendStream = &lpDispatch->m_SendStream;
          Buffer = CSendStream::GetBuffer(&lpDispatch->m_SendStream, (char *)0x15);
          if ( Buffer )
          {
            *((_DWORD *)Buffer + 3) = dwCID;
            *((_DWORD *)Buffer + 4) = dwTempUID;
            Buffer[20] = cGroup;
            v12 = CSendStream::WrapHeader(p_m_SendStream, 0x15u, 0x9Cu, 0, 0);
            v18 = -1;
            CSingleDispatch::Storage::~Storage(&StoragelpDispatch);
            return v12;
          }
        }
        else
        {
          v13 = CDBSingleObject::GetInstance();
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "RegularAgentPacketParse::SendGetCharSlot",
            aDWorkRylSource_5,
            435,
            aUid10uCid10u_1,
            Character->m_dwUID,
            dwCID,
            v13->m_ErrorString);
        }
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "RegularAgentPacketParse::SendGetCharSlot",
      aDWorkRylSource_5,
      371,
      aCid0x08x_220,
      dwCID,
      (unsigned __int8)cGroup);
  }
  v18 = -1;
  CSingleDispatch::Storage::~Storage(&StoragelpDispatch);
  return 0;
}

//----- (004453E0) --------------------------------------------------------
char __cdecl RegularAgentPacketParse::SendSetCharData(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwCID,
        unsigned int dwMileage,
        unsigned __int8 cGroup)
{
  unsigned int v3; // esi
  CTempCharacterMgr *TempCharacterMgr; // eax
  CTempCharacter *Character; // ebx
  CCreatureManager *Instance; // eax
  CCharacter *v7; // eax
  const CCharacter *v8; // ebp
  unsigned int Flag; // eax
  CSendStream *m_lpGameClientDispatch; // edi
  CMultiDispatch *DispatchTable; // eax
  unsigned int Mileage; // ecx
  unsigned int Gold; // eax
  unsigned int v15; // edi
  int m_nCurrentUID_high; // eax
  unsigned int m_dwUID; // edx
  unsigned int m_dwCID; // eax
  char *Buffer; // eax
  char *v20; // eax
  char *v21; // eax
  CPacketDispatch *SendStream; // [esp+10h] [ebp-54h]
  CSendStream *SendStreama; // [esp+10h] [ebp-54h]
  unsigned __int8 m_cGroup; // [esp+14h] [ebp-50h]
  CGameClientDispatch *lpDispatch; // [esp+1Ch] [ebp-48h]
  unsigned int dwDstGold; // [esp+20h] [ebp-44h]
  unsigned int dwSrcGold; // [esp+28h] [ebp-3Ch]
  CMultiDispatch::Storage StoragelpAgentDispatch; // [esp+2Ch] [ebp-38h] BYREF
  PktAdminToolSetData baseData; // [esp+34h] [ebp-30h] BYREF
  int v30; // [esp+60h] [ebp-4h]

  v3 = (unsigned int)dwCID;
  TempCharacterMgr = CRegularAgentDispatch::GetTempCharacterMgr();
  Character = CTempCharacterMgr::GetCharacter(TempCharacterMgr, dwCID, cGroup);
  Instance = CCreatureManager::GetInstance();
  v7 = CCreatureManager::GetCharacter(Instance, (unsigned int)dwCID);
  v8 = v7;
  if ( Character && v7 && (Character->m_cFlag & 2) != 0 )
  {
    Flag = Character->m_StoreInfo.Flag;
    m_lpGameClientDispatch = (CSendStream *)v8->m_lpGameClientDispatch;
    lpDispatch = (CGameClientDispatch *)m_lpGameClientDispatch;
    if ( (Flag & 0x40000000) != 0 )
    {
      if ( Character->m_nDataRequestCount )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "RegularAgentPacketParse::SendSetCharData",
          aDWorkRylSource_5,
          525,
          aCid0x08xDatare_0,
          dwCID,
          Character->m_nDataRequestCount);
        if ( m_lpGameClientDispatch )
        {
          GameClientSendPacket::SendCharBGServerMileageChange(
            m_lpGameClientDispatch + 8,
            (unsigned int)dwCID,
            cGroup,
            3u,
            0,
            v8->m_DBData.m_Info.Mileage,
            5u);
          return 1;
        }
      }
      else
      {
        m_cGroup = Character->m_cGroup;
        DispatchTable = CRegularAgentDispatch::GetDispatchTable();
        CMultiDispatch::Storage::Storage(&StoragelpAgentDispatch, DispatchTable, m_cGroup);
        v30 = 0;
        SendStream = StoragelpAgentDispatch.m_lpDispatch;
        if ( StoragelpAgentDispatch.m_lpDispatch )
        {
          Mileage = v8->m_DBData.m_Info.Mileage;
          if ( Mileage >= dwMileage )
          {
            Gold = Character->m_StoreInfo.Gold;
            v15 = 10 * dwMileage;
            dwSrcGold = Gold;
            if ( -1 - 10 * dwMileage > Gold )
            {
              dwDstGold = Gold + v15;
              GAMELOG::LogTakeGold(v8, Gold, Gold + v15, v15, 9u, 9u, 7u, 0);
              v8->m_DBData.m_Info.Mileage -= dwMileage;
              memset(&baseData, 0, 32);
              Character->m_StoreInfo.Gold = dwDstGold;
              baseData.m_cType = 0;
              m_nCurrentUID_high = HIDWORD(CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID);
              LODWORD(baseData.m_dwSerial) = CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID;
              m_dwUID = Character->m_dwUID;
              HIDWORD(baseData.m_dwSerial) = m_nCurrentUID_high;
              m_dwCID = Character->m_dwCID;
              SendStreama = (CSendStream *)&SendStream[8];
              baseData.m_dwUID = m_dwUID;
              baseData.m_dwCID = m_dwCID;
              baseData.m_dwRequestKey = (unsigned int)dwCID;
              Buffer = CSendStream::GetBuffer(SendStreama, (char *)0x21);
              if ( Buffer )
              {
                qmemcpy(Buffer, &baseData, 0x20u);
                Buffer[32] = baseData.m_cType;
                Buffer[32] = 20;
                CSendStream::WrapHeader(SendStreama, 0x21u, 0x77u, 0, 0);
                v3 = (unsigned int)dwCID;
                v15 = 10 * dwMileage;
              }
              v20 = CSendStream::GetBuffer(SendStreama, (char *)0x1BE);
              if ( v20 )
              {
                *(_DWORD *)(v20 + 33) = Character->m_CharInfoEX.Total;
                *(_DWORD *)(v20 + 37) = Character->m_CharInfoEX.ServerID;
                qmemcpy(v20 + 41, &Character->m_Quest, 0x86u);
                qmemcpy(v20 + 175, &Character->m_History, 0xCCu);
                qmemcpy(v20 + 379, &Character->m_Config, 0x43u);
                qmemcpy(v20, &baseData, 0x20u);
                v20[32] = baseData.m_cType;
                v20[32] = 11;
                CSendStream::WrapHeader(SendStreama, 0x1BEu, 0x77u, 0, 0);
                v3 = (unsigned int)dwCID;
                v15 = 10 * dwMileage;
              }
              v21 = CSendStream::GetBuffer(SendStreama, (char *)0x21);
              if ( v21 )
              {
                qmemcpy(v21, &baseData, 0x20u);
                v21[32] = baseData.m_cType;
                v21[32] = 21;
                CSendStream::WrapHeader(SendStreama, 0x21u, 0x77u, 0, 0);
                v3 = (unsigned int)dwCID;
                v15 = 10 * dwMileage;
              }
              if ( lpDispatch )
              {
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_DETAIL,
                  "RegularAgentPacketParse::SendSetCharData",
                  aDWorkRylSource_5,
                  669,
                  aUid10uCid10u_4,
                  v8->m_dwUID,
                  v8->m_dwCID,
                  baseData.m_dwUID,
                  baseData.m_dwCID,
                  m_cGroup,
                  dwSrcGold,
                  dwDstGold,
                  v15,
                  dwMileage,
                  v8->m_DBData.m_Info.Mileage);
                GameClientSendPacket::SendCharBGServerMileageChange(
                  &lpDispatch->m_SendStream,
                  v3,
                  m_cGroup,
                  3u,
                  Character->m_StoreInfo.Gold,
                  v8->m_DBData.m_Info.Mileage,
                  0);
              }
            }
            else if ( lpDispatch )
            {
              GameClientSendPacket::SendCharBGServerMileageChange(
                &lpDispatch->m_SendStream,
                (unsigned int)dwCID,
                m_cGroup,
                3u,
                0,
                Mileage,
                4u);
            }
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "RegularAgentPacketParse::SendSetCharData",
              aDWorkRylSource_5,
              555,
              aCid0x08x_254,
              dwCID);
            if ( m_lpGameClientDispatch )
              GameClientSendPacket::SendCharBGServerMileageChange(
                m_lpGameClientDispatch + 8,
                (unsigned int)dwCID,
                m_cGroup,
                3u,
                0,
                v8->m_DBData.m_Info.Mileage,
                3u);
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "RegularAgentPacketParse::SendSetCharData",
            aDWorkRylSource_5,
            542,
            aCid0x08x_220,
            dwCID,
            m_cGroup);
          if ( m_lpGameClientDispatch )
            GameClientSendPacket::SendCharBGServerMileageChange(
              m_lpGameClientDispatch + 8,
              (unsigned int)dwCID,
              m_cGroup,
              3u,
              0,
              v8->m_DBData.m_Info.Mileage,
              1u);
        }
        v30 = -1;
        CSingleDispatch::Storage::~Storage(&StoragelpAgentDispatch);
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "RegularAgentPacketParse::SendSetCharData",
        aDWorkRylSource_5,
        511,
        aCid0x08xDatare,
        dwCID,
        Flag);
      if ( m_lpGameClientDispatch )
      {
        GameClientSendPacket::SendCharBGServerMileageChange(
          m_lpGameClientDispatch + 8,
          (unsigned int)dwCID,
          cGroup,
          3u,
          0,
          v8->m_DBData.m_Info.Mileage,
          2u);
        return 1;
      }
    }
  }
  return 1;
}

//----- (004458A0) --------------------------------------------------------
char __thiscall CRegularAgentDispatch::DispatchPacket(CRegularAgentDispatch *this, PktBase *lpPktBase)
{
  unsigned __int8 m_Cmd; // dl

  m_Cmd = lpPktBase->m_Cmd;
  switch ( m_Cmd )
  {
    case 0x28u:
      return CRegularAgentDispatch::ParseServerLogin(this, lpPktBase);
    case 0x76u:
      return RegularAgentPacketParse::ParseGetCharData(lpPktBase, this->m_cGroup);
    case 0x9Cu:
      return RegularAgentPacketParse::ParseGetCharSlot(lpPktBase);
  }
  CRylServerDispatch::LogErrorPacket(this, &byte_4E00E0, m_Cmd);
  return 1;
}

//----- (00445900) --------------------------------------------------------
void __cdecl SeedDecryptBlock(unsigned __int8 *a1, unsigned int *a2)
{
  unsigned __int16 v2; // dx
  int v3; // ecx
  unsigned __int16 v4; // bx
  int v5; // edx
  unsigned int v6; // edi
  int v7; // esi
  int v8; // ebx
  int v9; // edi
  int v10; // ecx
  int v11; // edi
  unsigned int v12; // ebx
  unsigned int v13; // edi
  int v14; // esi
  int v15; // ebx
  int v16; // edi
  int v17; // edx
  int v18; // edi
  unsigned int v19; // ebx
  unsigned int v20; // edi
  int v21; // esi
  int v22; // ebx
  int v23; // edi
  int v24; // ecx
  int v25; // edi
  unsigned int v26; // ebx
  unsigned int v27; // edi
  int v28; // esi
  int v29; // ebx
  int v30; // edi
  int v31; // edx
  int v32; // edi
  unsigned int v33; // ebx
  unsigned int v34; // edi
  int v35; // esi
  int v36; // ebx
  int v37; // edi
  int v38; // ecx
  int v39; // edi
  unsigned int v40; // ebx
  unsigned int v41; // edi
  int v42; // esi
  int v43; // ebx
  int v44; // edi
  int v45; // edx
  int v46; // edi
  unsigned int v47; // ebx
  unsigned int v48; // edi
  int v49; // esi
  int v50; // ebx
  int v51; // edi
  int v52; // ecx
  int v53; // edi
  unsigned int v54; // ebx
  unsigned int v55; // edi
  int v56; // esi
  int v57; // ebx
  int v58; // edi
  int v59; // edx
  int v60; // edi
  unsigned int v61; // ebx
  unsigned int v62; // edi
  int v63; // esi
  int v64; // ebx
  int v65; // edi
  int v66; // ecx
  int v67; // edi
  unsigned int v68; // ebx
  unsigned int v69; // edi
  int v70; // esi
  int v71; // ebx
  int v72; // edi
  int v73; // edx
  int v74; // edi
  unsigned int v75; // ebx
  unsigned int v76; // edi
  int v77; // esi
  int v78; // ebx
  int v79; // edi
  int v80; // ecx
  int v81; // edi
  unsigned int v82; // ebx
  unsigned int v83; // edi
  int v84; // esi
  int v85; // ebx
  int v86; // edi
  int v87; // edx
  int v88; // edi
  unsigned int v89; // ebx
  unsigned int v90; // edi
  int v91; // esi
  int v92; // ebx
  int v93; // edi
  int v94; // ecx
  int v95; // edi
  unsigned int v96; // ebx
  unsigned int v97; // edi
  int v98; // esi
  int v99; // ebx
  int v100; // edi
  int v101; // edx
  int v102; // edi
  unsigned int v103; // ebx
  unsigned int v104; // edi
  int v105; // esi
  int v106; // ebx
  int v107; // edi
  int v108; // ecx
  int v109; // eax
  unsigned int v110; // edi
  int v111; // esi
  int v112; // eax
  int v113; // edi
  int v114; // edx
  int v115; // [esp+10h] [ebp-Ch]
  int v116; // [esp+10h] [ebp-Ch]
  int v117; // [esp+10h] [ebp-Ch]
  int v118; // [esp+10h] [ebp-Ch]
  int v119; // [esp+10h] [ebp-Ch]
  int v120; // [esp+10h] [ebp-Ch]
  int v121; // [esp+10h] [ebp-Ch]
  int v122; // [esp+10h] [ebp-Ch]
  int v123; // [esp+10h] [ebp-Ch]
  int v124; // [esp+14h] [ebp-8h]
  int v125; // [esp+14h] [ebp-8h]
  int v126; // [esp+14h] [ebp-8h]
  int v127; // [esp+14h] [ebp-8h]
  int v128; // [esp+14h] [ebp-8h]
  int v129; // [esp+14h] [ebp-8h]
  int v130; // [esp+14h] [ebp-8h]
  int v131; // [esp+14h] [ebp-8h]
  int v132; // [esp+14h] [ebp-8h]

  LOBYTE(v2) = 0;
  HIBYTE(v2) = a1[2];
  v3 = a1[3] | v2 | (a1[1] << 16) | (*a1 << 24);
  LOBYTE(v2) = 0;
  HIBYTE(v2) = a1[6];
  v124 = a1[7] | v2 | (a1[5] << 16) | (a1[4] << 24);
  LOBYTE(v4) = 0;
  HIBYTE(v4) = a1[10];
  v5 = a1[11] | v4 | (a1[9] << 16) | (a1[8] << 24);
  HIBYTE(v4) = a1[14];
  LOBYTE(v4) = a1[15];
  v115 = v4 | (a1[13] << 16) | (a1[12] << 24);
  v6 = v115 ^ a2[31];
  v7 = dword_509AB8[(unsigned __int8)(v5 ^ v6 ^ *((_BYTE *)a2 + 120))] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v5 ^ v6 ^ *((_WORD *)a2 + 60)) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v5 ^ v6 ^ a2[30]) >> 16)] ^ dword_50A6B8[(v5 ^ v6 ^ a2[30]) >> 24];
  v8 = dword_509AB8[(unsigned __int8)(v7 + v6)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v7 + v6) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v7 + v6) >> 16)] ^ dword_50A6B8[(v7 + v6) >> 24];
  v9 = dword_509AB8[(unsigned __int8)(v8 + v7)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v8 + v7) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v8 + v7) >> 16)] ^ dword_50A6B8[(unsigned int)(v8 + v7) >> 24];
  v10 = v9 ^ v3;
  v11 = (v9 + v8) ^ v124;
  v12 = a2[28];
  v125 = v11;
  v13 = v11 ^ a2[29];
  v14 = dword_509AB8[(unsigned __int8)(v10 ^ v13 ^ v12)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v10 ^ v13 ^ v12) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v10 ^ v13 ^ v12) >> 16)] ^ dword_50A6B8[(v10 ^ v13 ^ v12) >> 24];
  v15 = dword_509AB8[(unsigned __int8)(v14 + v13)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v14 + v13) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v14 + v13) >> 16)] ^ dword_50A6B8[(v14 + v13) >> 24];
  v16 = dword_509AB8[(unsigned __int8)(v15 + v14)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v15 + v14) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v15 + v14) >> 16)] ^ dword_50A6B8[(unsigned int)(v15 + v14) >> 24];
  v17 = v16 ^ v5;
  v18 = (v16 + v15) ^ v115;
  v19 = a2[26];
  v116 = v18;
  v20 = v18 ^ a2[27];
  v21 = dword_509AB8[(unsigned __int8)(v17 ^ v20 ^ v19)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v17 ^ v20 ^ v19) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v17 ^ v20 ^ v19) >> 16)] ^ dword_50A6B8[(v17 ^ v20 ^ v19) >> 24];
  v22 = dword_509AB8[(unsigned __int8)(v21 + v20)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v21 + v20) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v21 + v20) >> 16)] ^ dword_50A6B8[(v21 + v20) >> 24];
  v23 = dword_509AB8[(unsigned __int8)(v22 + v21)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v22 + v21) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v22 + v21) >> 16)] ^ dword_50A6B8[(unsigned int)(v22 + v21) >> 24];
  v24 = v23 ^ v10;
  v25 = (v23 + v22) ^ v125;
  v26 = a2[24];
  v126 = v25;
  v27 = v25 ^ a2[25];
  v28 = dword_509AB8[(unsigned __int8)(v24 ^ v27 ^ v26)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v24 ^ v27 ^ v26) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v24 ^ v27 ^ v26) >> 16)] ^ dword_50A6B8[(v24 ^ v27 ^ v26) >> 24];
  v29 = dword_509AB8[(unsigned __int8)(v28 + v27)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v28 + v27) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v28 + v27) >> 16)] ^ dword_50A6B8[(v28 + v27) >> 24];
  v30 = dword_509AB8[(unsigned __int8)(v29 + v28)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v29 + v28) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v29 + v28) >> 16)] ^ dword_50A6B8[(unsigned int)(v29 + v28) >> 24];
  v31 = v30 ^ v17;
  v32 = (v30 + v29) ^ v116;
  v33 = a2[22];
  v117 = v32;
  v34 = v32 ^ a2[23];
  v35 = dword_509AB8[(unsigned __int8)(v31 ^ v34 ^ v33)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v31 ^ v34 ^ v33) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v31 ^ v34 ^ v33) >> 16)] ^ dword_50A6B8[(v31 ^ v34 ^ v33) >> 24];
  v36 = dword_509AB8[(unsigned __int8)(v35 + v34)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v35 + v34) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v35 + v34) >> 16)] ^ dword_50A6B8[(v35 + v34) >> 24];
  v37 = dword_509AB8[(unsigned __int8)(v36 + v35)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v36 + v35) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v36 + v35) >> 16)] ^ dword_50A6B8[(unsigned int)(v36 + v35) >> 24];
  v38 = v37 ^ v24;
  v39 = (v37 + v36) ^ v126;
  v40 = a2[20];
  v127 = v39;
  v41 = v39 ^ a2[21];
  v42 = dword_509AB8[(unsigned __int8)(v38 ^ v41 ^ v40)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v38 ^ v41 ^ v40) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v38 ^ v41 ^ v40) >> 16)] ^ dword_50A6B8[(v38 ^ v41 ^ v40) >> 24];
  v43 = dword_509AB8[(unsigned __int8)(v42 + v41)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v42 + v41) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v42 + v41) >> 16)] ^ dword_50A6B8[(v42 + v41) >> 24];
  v44 = dword_509AB8[(unsigned __int8)(v43 + v42)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v43 + v42) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v43 + v42) >> 16)] ^ dword_50A6B8[(unsigned int)(v43 + v42) >> 24];
  v45 = v44 ^ v31;
  v46 = (v44 + v43) ^ v117;
  v47 = a2[18];
  v118 = v46;
  v48 = v46 ^ a2[19];
  v49 = dword_509AB8[(unsigned __int8)(v45 ^ v48 ^ v47)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v45 ^ v48 ^ v47) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v45 ^ v48 ^ v47) >> 16)] ^ dword_50A6B8[(v45 ^ v48 ^ v47) >> 24];
  v50 = dword_509AB8[(unsigned __int8)(v49 + v48)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v49 + v48) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v49 + v48) >> 16)] ^ dword_50A6B8[(v49 + v48) >> 24];
  v51 = dword_509AB8[(unsigned __int8)(v50 + v49)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v50 + v49) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v50 + v49) >> 16)] ^ dword_50A6B8[(unsigned int)(v50 + v49) >> 24];
  v52 = v51 ^ v38;
  v53 = (v51 + v50) ^ v127;
  v54 = a2[16];
  v128 = v53;
  v55 = v53 ^ a2[17];
  v56 = dword_509AB8[(unsigned __int8)(v52 ^ v55 ^ v54)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v52 ^ v55 ^ v54) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v52 ^ v55 ^ v54) >> 16)] ^ dword_50A6B8[(v52 ^ v55 ^ v54) >> 24];
  v57 = dword_509AB8[(unsigned __int8)(v56 + v55)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v56 + v55) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v56 + v55) >> 16)] ^ dword_50A6B8[(v56 + v55) >> 24];
  v58 = dword_509AB8[(unsigned __int8)(v57 + v56)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v57 + v56) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v57 + v56) >> 16)] ^ dword_50A6B8[(unsigned int)(v57 + v56) >> 24];
  v59 = v58 ^ v45;
  v60 = (v58 + v57) ^ v118;
  v61 = a2[14];
  v119 = v60;
  v62 = v60 ^ a2[15];
  v63 = dword_509AB8[(unsigned __int8)(v59 ^ v62 ^ v61)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v59 ^ v62 ^ v61) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v59 ^ v62 ^ v61) >> 16)] ^ dword_50A6B8[(v59 ^ v62 ^ v61) >> 24];
  v64 = dword_509AB8[(unsigned __int8)(v63 + v62)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v63 + v62) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v63 + v62) >> 16)] ^ dword_50A6B8[(v63 + v62) >> 24];
  v65 = dword_509AB8[(unsigned __int8)(v64 + v63)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v64 + v63) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v64 + v63) >> 16)] ^ dword_50A6B8[(unsigned int)(v64 + v63) >> 24];
  v66 = v65 ^ v52;
  v67 = (v65 + v64) ^ v128;
  v68 = a2[12];
  v129 = v67;
  v69 = v67 ^ a2[13];
  v70 = dword_509AB8[(unsigned __int8)(v66 ^ v69 ^ v68)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v66 ^ v69 ^ v68) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v66 ^ v69 ^ v68) >> 16)] ^ dword_50A6B8[(v66 ^ v69 ^ v68) >> 24];
  v71 = dword_509AB8[(unsigned __int8)(v70 + v69)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v70 + v69) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v70 + v69) >> 16)] ^ dword_50A6B8[(v70 + v69) >> 24];
  v72 = dword_509AB8[(unsigned __int8)(v71 + v70)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v71 + v70) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v71 + v70) >> 16)] ^ dword_50A6B8[(unsigned int)(v71 + v70) >> 24];
  v73 = v72 ^ v59;
  v74 = (v72 + v71) ^ v119;
  v75 = a2[10];
  v120 = v74;
  v76 = v74 ^ a2[11];
  v77 = dword_509AB8[(unsigned __int8)(v73 ^ v76 ^ v75)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v73 ^ v76 ^ v75) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v73 ^ v76 ^ v75) >> 16)] ^ dword_50A6B8[(v73 ^ v76 ^ v75) >> 24];
  v78 = dword_509AB8[(unsigned __int8)(v77 + v76)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v77 + v76) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v77 + v76) >> 16)] ^ dword_50A6B8[(v77 + v76) >> 24];
  v79 = dword_509AB8[(unsigned __int8)(v78 + v77)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v78 + v77) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v78 + v77) >> 16)] ^ dword_50A6B8[(unsigned int)(v78 + v77) >> 24];
  v80 = v79 ^ v66;
  v81 = (v79 + v78) ^ v129;
  v82 = a2[8];
  v130 = v81;
  v83 = v81 ^ a2[9];
  v84 = dword_509AB8[(unsigned __int8)(v80 ^ v83 ^ v82)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v80 ^ v83 ^ v82) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v80 ^ v83 ^ v82) >> 16)] ^ dword_50A6B8[(v80 ^ v83 ^ v82) >> 24];
  v85 = dword_509AB8[(unsigned __int8)(v84 + v83)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v84 + v83) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v84 + v83) >> 16)] ^ dword_50A6B8[(v84 + v83) >> 24];
  v86 = dword_509AB8[(unsigned __int8)(v85 + v84)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v85 + v84) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v85 + v84) >> 16)] ^ dword_50A6B8[(unsigned int)(v85 + v84) >> 24];
  v87 = v86 ^ v73;
  v88 = (v86 + v85) ^ v120;
  v89 = a2[6];
  v121 = v88;
  v90 = v88 ^ a2[7];
  v91 = dword_509AB8[(unsigned __int8)(v87 ^ v90 ^ v89)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v87 ^ v90 ^ v89) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v87 ^ v90 ^ v89) >> 16)] ^ dword_50A6B8[(v87 ^ v90 ^ v89) >> 24];
  v92 = dword_509AB8[(unsigned __int8)(v91 + v90)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v91 + v90) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v91 + v90) >> 16)] ^ dword_50A6B8[(v91 + v90) >> 24];
  v93 = dword_509AB8[(unsigned __int8)(v92 + v91)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v92 + v91) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v92 + v91) >> 16)] ^ dword_50A6B8[(unsigned int)(v92 + v91) >> 24];
  v94 = v93 ^ v80;
  v95 = (v93 + v92) ^ v130;
  v96 = a2[4];
  v131 = v95;
  v97 = v95 ^ a2[5];
  v98 = dword_509AB8[(unsigned __int8)(v94 ^ v97 ^ v96)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v94 ^ v97 ^ v96) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v94 ^ v97 ^ v96) >> 16)] ^ dword_50A6B8[(v94 ^ v97 ^ v96) >> 24];
  v99 = dword_509AB8[(unsigned __int8)(v98 + v97)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v98 + v97) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v98 + v97) >> 16)] ^ dword_50A6B8[(v98 + v97) >> 24];
  v100 = dword_509AB8[(unsigned __int8)(v99 + v98)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v99 + v98) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v99 + v98) >> 16)] ^ dword_50A6B8[(unsigned int)(v99 + v98) >> 24];
  v101 = v100 ^ v87;
  v102 = (v100 + v99) ^ v121;
  v103 = a2[2];
  v122 = v102;
  v104 = v102 ^ a2[3];
  v105 = dword_509AB8[(unsigned __int8)(v101 ^ v104 ^ v103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v101 ^ v104 ^ v103) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v101 ^ v104 ^ v103) >> 16)] ^ dword_50A6B8[(v101 ^ v104 ^ v103) >> 24];
  v106 = dword_509AB8[(unsigned __int8)(v105 + v104)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v105 + v104) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v105 + v104) >> 16)] ^ dword_50A6B8[(v105 + v104) >> 24];
  v107 = dword_509AB8[(unsigned __int8)(v106 + v105)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v106 + v105) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v106 + v105) >> 16)] ^ dword_50A6B8[(unsigned int)(v106 + v105) >> 24];
  v108 = v107 ^ v94;
  v109 = *a2;
  v132 = (v107 + v106) ^ v131;
  v110 = v132 ^ a2[1];
  v111 = dword_509AB8[(unsigned __int8)(v108 ^ v110 ^ v109)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v108 ^ v110 ^ v109) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v108 ^ v110 ^ v109) >> 16)] ^ dword_50A6B8[(v108 ^ v110 ^ v109) >> 24];
  v112 = dword_509AB8[(unsigned __int8)(v111 + v110)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v111 + v110) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v111 + v110) >> 16)] ^ dword_50A6B8[(v111 + v110) >> 24];
  v113 = dword_509AB8[(unsigned __int8)(v112 + v111)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v112 + v111) >> 8)] ^ dword_50A2B8[(unsigned __int8)((unsigned int)(v112 + v111) >> 16)] ^ dword_50A6B8[(unsigned int)(v112 + v111) >> 24];
  v114 = v113 ^ v101;
  *a1 = HIBYTE(v114);
  v123 = (v113 + v112) ^ v122;
  a1[1] = BYTE2(v114);
  a1[2] = BYTE1(v114);
  a1[3] = v114;
  a1[4] = HIBYTE(v123);
  a1[5] = BYTE2(v123);
  a1[6] = BYTE1(v123);
  a1[7] = v123;
  a1[8] = HIBYTE(v108);
  a1[9] = BYTE2(v108);
  a1[10] = BYTE1(v108);
  a1[11] = v108;
  a1[12] = HIBYTE(v132);
  a1[13] = BYTE2(v132);
  a1[14] = BYTE1(v132);
  a1[15] = v132;
}
// 509AB8: using guessed type int dword_509AB8[256];
// 509EB8: using guessed type int dword_509EB8[256];
// 50A2B8: using guessed type int dword_50A2B8[256];
// 50A6B8: using guessed type int dword_50A6B8[256];

//----- (00446810) --------------------------------------------------------
int __cdecl SeedDecrypt(unsigned __int8 *a1, int a2, unsigned int *a3)
{
  int v3; // esi

  v3 = 0;
  if ( a2 <= 0 )
    return 0;
  do
  {
    SeedDecryptBlock(&a1[v3], a3);
    v3 += 16;
  }
  while ( v3 < a2 );
  return v3;
}

//----- (00446850) --------------------------------------------------------
void __cdecl SeedEncRoundKey(unsigned int *a1, char *a2)
{
  unsigned __int16 v2; // cx
  unsigned int v3; // esi
  unsigned int v4; // edi
  unsigned __int16 v5; // dx
  unsigned int v6; // ecx
  unsigned int v7; // ebp
  unsigned int v9; // edx
  unsigned int v10; // esi
  unsigned int v11; // edi
  unsigned int v12; // ecx
  unsigned int v13; // edx
  unsigned int v14; // edi
  unsigned int v15; // esi
  unsigned int v16; // ecx
  unsigned int v17; // edi
  unsigned int v18; // edx
  unsigned int v19; // esi
  unsigned int v20; // edi
  unsigned int v21; // ecx
  unsigned int v22; // edx
  unsigned int v23; // edi
  unsigned int v24; // esi
  unsigned int v25; // ecx
  unsigned int v26; // edi
  unsigned int v27; // edx
  int v28; // ebx
  unsigned int v29; // esi
  unsigned int v30; // edx
  unsigned int v31; // [esp+18h] [ebp+4h]
  unsigned int v32; // [esp+18h] [ebp+4h]
  unsigned int v33; // [esp+18h] [ebp+4h]
  unsigned int v34; // [esp+18h] [ebp+4h]
  unsigned int v35; // [esp+18h] [ebp+4h]
  unsigned int v36; // [esp+18h] [ebp+4h]
  unsigned int v37; // [esp+18h] [ebp+4h]
  int v38; // [esp+1Ch] [ebp+8h]

  LOBYTE(v2) = 0;
  HIBYTE(v2) = a2[2];
  v3 = (unsigned __int8)a2[3] | v2 | ((unsigned __int8)a2[1] << 16) | ((unsigned __int8)*a2 << 24);
  LOBYTE(v2) = 0;
  HIBYTE(v2) = a2[6];
  v4 = (unsigned __int8)a2[7] | v2 | ((unsigned __int8)a2[5] << 16) | ((unsigned __int8)a2[4] << 24);
  LOBYTE(v5) = 0;
  HIBYTE(v5) = a2[10];
  v6 = (unsigned __int8)a2[11] | v5 | ((unsigned __int8)a2[9] << 16) | ((unsigned __int8)a2[8] << 24);
  HIBYTE(v5) = a2[14];
  LOBYTE(v5) = a2[15];
  v7 = v5 | ((unsigned __int8)a2[13] << 16) | ((unsigned __int8)a2[12] << 24);
  *a1 = dword_509AB8[(unsigned __int8)(v6 + v3 + 71)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v6 + v3 - 31161) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v6 + v3 + 1640531527) >> 16)] ^ dword_50A6B8[(v6 + v3 + 1640531527) >> 24];
  a1[1] = dword_509AB8[(unsigned __int8)(v4 - v7 - 71)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v4 - v7 + 31161) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v4 - v7 - 1640531527) >> 16)] ^ dword_50A6B8[(v4 - v7 - 1640531527) >> 24];
  v9 = (v3 >> 8) ^ (v4 << 24);
  v31 = (v4 >> 8) ^ (v3 << 24);
  a1[2] = dword_509AB8[(unsigned __int8)(v6 + v9 - 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v6 + v9 + 3213) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v6 + v9 - 1013904243) >> 16)] ^ dword_50A6B8[(v6 + v9 - 1013904243) >> 24];
  a1[3] = dword_509AB8[(unsigned __int8)(v31 - v7 + 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v31 - v7 - 3213) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v31 - v7 + 1013904243) >> 16)] ^ dword_50A6B8[(v31 - v7 + 1013904243) >> 24];
  v10 = (v6 << 8) ^ HIBYTE(v7);
  v11 = (v7 << 8) ^ HIBYTE(v6);
  a1[4] = dword_509AB8[(unsigned __int8)(v10 + v9 + 26)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v10 + v9 + 6426) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v10 + v9 - 2027808486) >> 16)] ^ dword_50A6B8[(v10 + v9 - 2027808486) >> 24];
  a1[5] = dword_509AB8[(unsigned __int8)(v31 - v11 - 26)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v31 - v11 - 6426) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v31 - v11 + 2027808486) >> 16)] ^ dword_50A6B8[(v31 - v11 + 2027808486) >> 24];
  v12 = (v9 >> 8) ^ (v31 << 24);
  v32 = (v31 >> 8) ^ (v9 << 24);
  a1[6] = dword_509AB8[(unsigned __int8)(v10 + v12 + 52)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v10 + v12 + 12852) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v10 + v12 + 239350324) >> 16)] ^ dword_50A6B8[(v10 + v12 + 239350324) >> 24];
  a1[7] = dword_509AB8[(unsigned __int8)(v32 - v11 - 52)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v32 - v11 - 12852) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v32 - v11 - 239350324) >> 16)] ^ dword_50A6B8[(v32 - v11 - 239350324) >> 24];
  v13 = (v10 << 8) ^ HIBYTE(v11);
  v14 = (v11 << 8) ^ HIBYTE(v10);
  a1[8] = dword_509AB8[(unsigned __int8)(v13 + v12 + 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v13 + v12 + 25703) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v13 + v12 + 478700647) >> 16)] ^ dword_50A6B8[(v13 + v12 + 478700647) >> 24];
  a1[9] = dword_509AB8[(unsigned __int8)(v32 - v14 - 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v32 - v14 - 25703) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v32 - v14 - 478700647) >> 16)] ^ dword_50A6B8[(v32 - v14 - 478700647) >> 24];
  v15 = (v12 >> 8) ^ (v32 << 24);
  v33 = (v32 >> 8) ^ (v12 << 24);
  a1[10] = dword_509AB8[(unsigned __int8)(v13 + v15 - 51)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v13 + v15 - 14131) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v13 + v15 + 957401293) >> 16)] ^ dword_50A6B8[(v13 + v15 + 957401293) >> 24];
  a1[11] = dword_509AB8[(unsigned __int8)(v33 - v14 + 51)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v33 - v14 + 14131) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v33 - v14 - 957401293) >> 16)] ^ dword_50A6B8[(v33 - v14 - 957401293) >> 24];
  v16 = (v13 << 8) ^ HIBYTE(v14);
  v17 = (v14 << 8) ^ HIBYTE(v13);
  a1[12] = dword_509AB8[(unsigned __int8)(v16 + v15 - 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v16 + v15 - 28263) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v16 + v15 + 1914802585) >> 16)] ^ dword_50A6B8[(v16 + v15 + 1914802585) >> 24];
  a1[13] = dword_509AB8[(unsigned __int8)(v33 - v17 + 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v33 - v17 + 28263) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v33 - v17 - 1914802585) >> 16)] ^ dword_50A6B8[(v33 - v17 - 1914802585) >> 24];
  v18 = (v15 >> 8) ^ (v33 << 24);
  v34 = (v33 >> 8) ^ (v15 << 24);
  a1[14] = dword_509AB8[(unsigned __int8)(v16 + v18 + 49)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v16 + v18 + 9009) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v16 + v18 - 465362127) >> 16)] ^ dword_50A6B8[(v16 + v18 - 465362127) >> 24];
  a1[15] = dword_509AB8[(unsigned __int8)(v34 - v17 - 49)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v34 - v17 - 9009) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v34 - v17 + 465362127) >> 16)] ^ dword_50A6B8[(v34 - v17 + 465362127) >> 24];
  v19 = (v16 << 8) ^ HIBYTE(v17);
  v20 = (v17 << 8) ^ HIBYTE(v16);
  a1[16] = dword_509AB8[(unsigned __int8)(v19 + v18 + 98)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v19 + v18 + 18018) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v19 + v18 - 930724254) >> 16)] ^ dword_50A6B8[(v19 + v18 - 930724254) >> 24];
  a1[17] = dword_509AB8[(unsigned __int8)(v34 - v20 - 98)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v34 - v20 - 18018) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v34 - v20 + 930724254) >> 16)] ^ dword_50A6B8[(v34 - v20 + 930724254) >> 24];
  v21 = (v18 >> 8) ^ (v34 << 24);
  v35 = (v34 >> 8) ^ (v18 << 24);
  a1[18] = dword_509AB8[(unsigned __int8)(v19 + v21 - 60)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v19 + v21 - 29500) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v19 + v21 - 1861448508) >> 16)] ^ dword_50A6B8[(v19 + v21 - 1861448508) >> 24];
  a1[19] = dword_509AB8[(unsigned __int8)(v35 - v20 + 60)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v35 - v20 + 29500) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v35 - v20 + 1861448508) >> 16)] ^ dword_50A6B8[(v35 - v20 + 1861448508) >> 24];
  v22 = (v19 << 8) ^ HIBYTE(v20);
  v23 = (v20 << 8) ^ HIBYTE(v19);
  a1[20] = dword_509AB8[(unsigned __int8)(v22 + v21 - 120)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v22 + v21 + 6536) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v22 + v21 + 572070280) >> 16)] ^ dword_50A6B8[(v22 + v21 + 572070280) >> 24];
  a1[21] = dword_509AB8[(unsigned __int8)(v35 - v23 + 120)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v35 - v23 - 6536) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v35 - v23 - 572070280) >> 16)] ^ dword_50A6B8[(v35 - v23 - 572070280) >> 24];
  v24 = (v21 >> 8) ^ (v35 << 24);
  v36 = (v35 >> 8) ^ (v21 << 24);
  a1[22] = dword_509AB8[(unsigned __int8)(v22 + v24 + 15)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v22 + v24 + 13071) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v22 + v24 + 1144140559) >> 16)] ^ dword_50A6B8[(v22 + v24 + 1144140559) >> 24];
  a1[23] = dword_509AB8[(unsigned __int8)(v36 - v23 - 15)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v36 - v23 - 13071) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v36 - v23 - 1144140559) >> 16)] ^ dword_50A6B8[(v36 - v23 - 1144140559) >> 24];
  v25 = (v22 << 8) ^ HIBYTE(v23);
  v26 = (v23 << 8) ^ HIBYTE(v22);
  a1[24] = dword_509AB8[(unsigned __int8)(v25 + v24 + 29)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v25 + v24 + 26141) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v25 + v24 - 2006686179) >> 16)] ^ dword_50A6B8[(v25 + v24 - 2006686179) >> 24];
  a1[25] = dword_509AB8[(unsigned __int8)(v36 - v26 - 29)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v36 - v26 - 26141) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v36 - v26 + 2006686179) >> 16)] ^ dword_50A6B8[(v36 - v26 + 2006686179) >> 24];
  v27 = (v24 >> 8) ^ (v36 << 24);
  v37 = (v36 >> 8) ^ (v24 << 24);
  a1[26] = dword_509AB8[(unsigned __int8)(v25 + v27 + 58)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v25 + v27 - 13254) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v25 + v27 + 281594938) >> 16)] ^ dword_50A6B8[(v25 + v27 + 281594938) >> 24];
  a1[27] = dword_509AB8[(unsigned __int8)(v37 - v26 - 58)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v37 - v26 + 13254) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v37 - v26 - 281594938) >> 16)] ^ dword_50A6B8[(v37 - v26 - 281594938) >> 24];
  v28 = (v26 << 8) ^ HIBYTE(v25);
  v38 = (v25 << 8) ^ HIBYTE(v26);
  a1[28] = dword_509AB8[(unsigned __int8)(v38 + v27 + 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v38 + v27 - 26509) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v38 + v27 + 563189875) >> 16)] ^ dword_50A6B8[(v38 + v27 + 563189875) >> 24];
  a1[29] = dword_509AB8[(unsigned __int8)(v37 - v28 - 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v37 - v28 + 26509) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v37 - v28 - 563189875) >> 16)] ^ dword_50A6B8[(v37 - v28 - 563189875) >> 24];
  v29 = v38 + ((v27 >> 8) ^ (v37 << 24)) + 1126379749;
  v30 = ((v37 >> 8) ^ (v27 << 24)) - v28 - 1126379749;
  a1[30] = dword_509AB8[(unsigned __int8)v29] ^ dword_509EB8[BYTE1(v29)] ^ dword_50A2B8[BYTE2(v29)] ^ dword_50A6B8[HIBYTE(v29)];
  a1[31] = dword_509AB8[(unsigned __int8)v30] ^ dword_509EB8[BYTE1(v30)] ^ dword_50A2B8[BYTE2(v30)] ^ dword_50A6B8[HIBYTE(v30)];
}
// 509AB8: using guessed type int dword_509AB8[256];
// 509EB8: using guessed type int dword_509EB8[256];
// 50A2B8: using guessed type int dword_50A2B8[256];
// 50A6B8: using guessed type int dword_50A6B8[256];

//----- (00447450) --------------------------------------------------------
CCrc32 *sub_447450()
{
  return CCrc32::CCrc32((CCrc32 *)&unk_5273F8);
}

//----- (00447470) --------------------------------------------------------
void sub_447470()
{
  CCrc32::~CCrc32((CCrc32 *)&unk_5273F8);
}

//----- (00447480) --------------------------------------------------------
_iobuf *__cdecl LoadAuthTable(char *file)
{
  _iobuf *result; // eax
  int v2; // edi
  int v3; // ebp
  unsigned __int8 *v4; // esi
  _BYTE buffer[40]; // [esp+4h] [ebp-28h] BYREF

  memset(buffer, 0, sizeof(buffer));
  result = fopen(file, aRb);
  v2 = (int)result;
  if ( result )
  {
    v3 = 0;
    v4 = buffer;
    do
    {
      if ( fread(v4, 1u, 5u, (_iobuf *)v2) < 5 )
        break;
      if ( (*(_BYTE *)(v2 + 12) & 0x10) != 0 )
        break;
      if ( (char)*v4 >= 32 )
        break;
      ++v3;
      v4 += 5;
    }
    while ( v3 < 8 );
    fclose((_iobuf *)v2);
    memset(&unk_52787C, 0, 0x28u);
    qmemcpy(&unk_52787C, buffer, 5 * v3);
    return (_iobuf *)v3;
  }
  return result;
}

//----- (00447520) --------------------------------------------------------
_iobuf *__cdecl LoadAuthIndex(char *file)
{
  _iobuf *result; // eax
  _iobuf *v2; // esi

  strcpy(byte_527928, file);
  result = fopen(file, aRt_0);
  v2 = result;
  if ( result )
  {
    fscanf(result, "%lu", &dword_50AAB8);
    fclose(v2);
    return (_iobuf *)(dword_50AAB8 != -1 && (unsigned int)dword_50AAB8 < 8);
  }
  return result;
}
// 50AAB8: using guessed type int dword_50AAB8;

//----- (004475A0) --------------------------------------------------------
int __cdecl InitPacketProtect(char *a1, int a2)
{
  char v2; // cl
  int i; // eax
  char v4; // cl
  int j; // eax
  unsigned int v6; // eax
  char v8[20]; // [esp+Ch] [ebp-14h] BYREF

  strcpy(v8, byte_50AAF4);
  if ( a1 )
  {
    v2 = *a1;
    for ( i = 0; v2; ++i )
    {
      v8[i % 16] ^= v2;
      v2 = a1[i + 1];
    }
  }
  SeedEncRoundKey(&dword_5277FC, v8);
  strcpy(v8, &asc_50AAE0);
  if ( a1 )
  {
    v4 = *a1;
    for ( j = 0; v4; ++j )
    {
      v8[j % 16] ^= v4;
      v4 = a1[j + 1];
    }
  }
  SeedEncRoundKey(&dword_5278A4, v8);
  strcpy(v8, byte_50AACC);
  dword_527924 = a2;
  v6 = time(0);
  srand(v6);
  return 1;
}
// 50AAE0: using guessed type char asc_50AAE0;
// 527924: using guessed type int dword_527924;

//----- (004476C0) --------------------------------------------------------
CCSAuth *__thiscall CCSAuth::CCSAuth(CCSAuth *this)
{
  CCSAuth::Init(this);
  return this;
}

//----- (004476D0) --------------------------------------------------------
void __thiscall CCSAuth::Init(CCSAuth *this)
{
  int v2; // eax

  v2 = time(0);
  CCSAuth::np_srandom(this, (unsigned int)this + v2);
  this->m_dwAuthValue = 0;
  this->m_bAuth = 1;
  qmemcpy(this->m_table, &unk_52787C, sizeof(this->m_table));
  this->m_dwCurrIndex = dword_50AAB8;
  this->m_dwServerSequenceNumber = 0;
  this->m_dwClientSequenceNumber = 0;
  memset(this->m_adwLastSeqs, 0, sizeof(this->m_adwLastSeqs));
  this->m_dwPPLastError = 0;
  this->m_dwSeq = 0;
}
// 50AAB8: using guessed type int dword_50AAB8;

//----- (00447730) --------------------------------------------------------
unsigned int __thiscall CCSAuth::IsAuth(CCSAuth *this)
{
  return this->m_bAuth;
}

//----- (00447740) --------------------------------------------------------
void __thiscall CCSAuth::np_srandom(CCSAuth *this, unsigned int a2)
{
  this->m_dwSeed = a2;
}

//----- (00447750) --------------------------------------------------------
signed int __thiscall CCSAuth::np_random(CCSAuth *this)
{
  signed int v1; // edi

  v1 = 16807 * (this->m_dwSeed % 0x1F31D) - 2836 * (this->m_dwSeed / 0x1F31D);
  if ( v1 <= 0 )
    v1 += 0x7FFFFFFF;
  this->m_dwSeed = v1;
  return v1;
}

//----- (004477B0) --------------------------------------------------------
unsigned int __thiscall CCSAuth::GetAuthDword(CCSAuth *this)
{
  signed int v2; // esi
  unsigned int v3; // esi

  v2 = CCSAuth::np_random(this);
  v3 = ((this->m_dwCurrIndex & 0xF) << 16) | (CCSAuth::np_random(this) ^ v2) & 0xFFF0FFFF;
  this->m_bAuth = 0;
  this->m_dwAuthValue = v3;
  return v3;
}

//----- (004477F0) --------------------------------------------------------
unsigned int __thiscall CCSAuth::TransAuthDword(CCSAuth *this, unsigned int a2, int a3)
{
  signed int v4; // kr00_4
  unsigned int v5; // ecx
  unsigned int v6; // eax
  int v7; // edi
  unsigned int v8; // edx
  unsigned int v9; // esi
  unsigned __int64 v10; // kr08_8
  unsigned int v11; // ecx
  unsigned int v12; // edi
  unsigned int v13; // eax
  unsigned int v14; // eax
  unsigned int v15; // esi
  unsigned int v16; // edx
  unsigned int v17; // edi
  unsigned int v18; // ecx
  unsigned int v19; // esi
  unsigned int v20; // eax
  unsigned int v21; // edi
  unsigned int v22; // edx
  unsigned int v23; // esi
  unsigned int v24; // ecx
  unsigned int v25; // edi
  unsigned int v26; // eax
  unsigned int v27; // esi
  unsigned int v28; // edx
  unsigned int v29; // edi
  unsigned int v30; // ecx
  unsigned int v31; // esi
  unsigned int v32; // eax
  unsigned int v33; // edi
  unsigned int v34; // edx
  unsigned int v35; // esi
  int v36; // ebx
  unsigned int v37; // ecx
  int v39; // [esp+10h] [ebp-94h]
  signed int v40; // [esp+18h] [ebp-8Ch]
  signed int v41; // [esp+1Ch] [ebp-88h]
  signed int v42; // [esp+20h] [ebp-84h]
  _DWORD v43[32]; // [esp+24h] [ebp-80h]

  CCSAuth::np_srandom(this, a2);
  v4 = CCSAuth::np_random(this);
  v40 = CCSAuth::np_random(this);
  v41 = CCSAuth::np_random(this);
  v42 = CCSAuth::np_random(this);
  v5 = SHIBYTE(v4) | (SBYTE2(v4) << 8) | (SBYTE1(v4) << 16) | ((char)v4 << 24);
  v6 = SHIBYTE(v41) | (SBYTE2(v41) << 8) | (SBYTE1(v41) << 16) | ((char)v41 << 24);
  v7 = SHIBYTE(v42) | (SBYTE2(v42) << 8) | (SBYTE1(v42) << 16) | ((char)v42 << 24);
  v43[0] = dword_509AB8[(unsigned __int8)(HIBYTE(v41) + HIBYTE(v4) + 71)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)((SHIBYTE(v41) | (unsigned __int16)(SBYTE2(v41) << 8)) + (SHIBYTE(v4) | (unsigned __int16)(SBYTE2(v4) << 8)) - 31161) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v6 + v5 + 1640531527) >> 16)] ^ dword_50A6B8[(v6 + v5 + 1640531527) >> 24];
  v43[1] = dword_509AB8[(unsigned __int8)(HIBYTE(v40) - HIBYTE(v42) - 71)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)((SHIBYTE(v40) | (unsigned __int16)(SBYTE2(v40) << 8)) - (SHIBYTE(v42) | (unsigned __int16)(SBYTE2(v42) << 8)) + 31161) >> 8)] ^ dword_50A2B8[(unsigned __int8)(((SHIBYTE(v40) | (SBYTE2(v40) << 8) | (SBYTE1(v40) << 16) | (unsigned int)((char)v40 << 24)) - v7 - 1640531527) >> 16)] ^ dword_50A6B8[((SHIBYTE(v40) | (SBYTE2(v40) << 8) | (SBYTE1(v40) << 16) | (unsigned int)((char)v40 << 24)) - v7 - 1640531527) >> 24];
  v8 = (v5 >> 8) ^ ((SHIBYTE(v40) | (SBYTE2(v40) << 8) | (SBYTE1(v40) << 16) | ((char)v40 << 24)) << 24);
  v9 = ((SHIBYTE(v40) | (SBYTE2(v40) << 8) | (SBYTE1(v40) << 16) | (unsigned int)((char)v40 << 24)) >> 8) ^ (v5 << 24);
  v43[2] = dword_509AB8[(unsigned __int8)(HIBYTE(v41) + v8 - 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)((SHIBYTE(v41) | (unsigned __int16)(SBYTE2(v41) << 8)) + v8 + 3213) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v6 + v8 - 1013904243) >> 16)] ^ dword_50A6B8[(v6 + v8 - 1013904243) >> 24];
  v43[3] = dword_509AB8[(unsigned __int8)(v9 - HIBYTE(v42) + 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v9 - (SHIBYTE(v42) | (unsigned __int16)(SBYTE2(v42) << 8)) - 3213) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v9 - v7 + 1013904243) >> 16)] ^ dword_50A6B8[(v9 - v7 + 1013904243) >> 24];
  v10 = (unsigned __int64)(unsigned int)v7 << 8;
  v11 = (v6 << 8) ^ HIDWORD(v10);
  v12 = v10 ^ HIBYTE(v6);
  v13 = (((SHIBYTE(v40) | (SBYTE2(v40) << 8) | (SBYTE1(v40) << 16) | (unsigned int)((char)v40 << 24)) >> 8) ^ ((SHIBYTE(v4) | (SBYTE2(v4) << 8) | (SBYTE1(v4) << 16) | ((char)v4 << 24)) << 24))
      - v12
      + 2027808486;
  v43[4] = dword_509AB8[(unsigned __int8)(v11 + v8 + 26)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v11 + v8 + 6426) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v11 + v8 - 2027808486) >> 16)] ^ dword_50A6B8[(v11 + v8 - 2027808486) >> 24];
  v43[5] = dword_509AB8[(unsigned __int8)v13] ^ dword_509EB8[BYTE1(v13)] ^ dword_50A2B8[BYTE2(v13)] ^ dword_50A6B8[HIBYTE(v13)];
  v14 = (v8 >> 8) ^ (v9 << 24);
  v15 = (v9 >> 8) ^ (v8 << 24);
  v43[6] = dword_509AB8[(unsigned __int8)(v11 + v14 + 52)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v11 + v14 + 12852) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v11 + v14 + 239350324) >> 16)] ^ dword_50A6B8[(v11 + v14 + 239350324) >> 24];
  v43[7] = dword_509AB8[(unsigned __int8)(v15 - v12 - 52)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v15 - v12 - 12852) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v15 - v12 - 239350324) >> 16)] ^ dword_50A6B8[(v15 - v12 - 239350324) >> 24];
  v16 = (v11 << 8) ^ HIBYTE(v12);
  v17 = (v12 << 8) ^ HIBYTE(v11);
  v43[8] = dword_509AB8[(unsigned __int8)(v16 + v14 + 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v16 + v14 + 25703) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v16 + v14 + 478700647) >> 16)] ^ dword_50A6B8[(v16 + v14 + 478700647) >> 24];
  v43[9] = dword_509AB8[(unsigned __int8)(v15 - v17 - 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v15 - v17 - 25703) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v15 - v17 - 478700647) >> 16)] ^ dword_50A6B8[(v15 - v17 - 478700647) >> 24];
  v18 = (v14 >> 8) ^ (v15 << 24);
  v19 = (v15 >> 8) ^ (v14 << 24);
  v43[10] = dword_509AB8[(unsigned __int8)(v16 + v18 - 51)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v16 + v18 - 14131) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v16 + v18 + 957401293) >> 16)] ^ dword_50A6B8[(v16 + v18 + 957401293) >> 24];
  v43[11] = dword_509AB8[(unsigned __int8)(v19 - v17 + 51)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v19 - v17 + 14131) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v19 - v17 - 957401293) >> 16)] ^ dword_50A6B8[(v19 - v17 - 957401293) >> 24];
  v20 = (v16 << 8) ^ HIBYTE(v17);
  v21 = (v17 << 8) ^ HIBYTE(v16);
  v43[12] = dword_509AB8[(unsigned __int8)(v20 + v18 - 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v20 + v18 - 28263) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v20 + v18 + 1914802585) >> 16)] ^ dword_50A6B8[(v20 + v18 + 1914802585) >> 24];
  v43[13] = dword_509AB8[(unsigned __int8)(v19 - v21 + 103)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v19 - v21 + 28263) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v19 - v21 - 1914802585) >> 16)] ^ dword_50A6B8[(v19 - v21 - 1914802585) >> 24];
  v22 = (v18 >> 8) ^ (v19 << 24);
  v23 = (v19 >> 8) ^ (v18 << 24);
  v43[14] = dword_509AB8[(unsigned __int8)(v20 + v22 + 49)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v20 + v22 + 9009) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v20 + v22 - 465362127) >> 16)] ^ dword_50A6B8[(v20 + v22 - 465362127) >> 24];
  v43[15] = dword_509AB8[(unsigned __int8)(v23 - v21 - 49)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v23 - v21 - 9009) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v23 - v21 + 465362127) >> 16)] ^ dword_50A6B8[(v23 - v21 + 465362127) >> 24];
  v24 = (v20 << 8) ^ HIBYTE(v21);
  v25 = (v21 << 8) ^ HIBYTE(v20);
  v43[16] = dword_509AB8[(unsigned __int8)(v24 + v22 + 98)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v24 + v22 + 18018) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v24 + v22 - 930724254) >> 16)] ^ dword_50A6B8[(v24 + v22 - 930724254) >> 24];
  v43[17] = dword_509AB8[(unsigned __int8)(v23 - v25 - 98)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v23 - v25 - 18018) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v23 - v25 + 930724254) >> 16)] ^ dword_50A6B8[(v23 - v25 + 930724254) >> 24];
  v26 = (v22 >> 8) ^ (v23 << 24);
  v27 = (v23 >> 8) ^ (v22 << 24);
  v43[18] = dword_509AB8[(unsigned __int8)(v24 + v26 - 60)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v24 + v26 - 29500) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v24 + v26 - 1861448508) >> 16)] ^ dword_50A6B8[(v24 + v26 - 1861448508) >> 24];
  v43[19] = dword_509AB8[(unsigned __int8)(v27 - v25 + 60)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v27 - v25 + 29500) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v27 - v25 + 1861448508) >> 16)] ^ dword_50A6B8[(v27 - v25 + 1861448508) >> 24];
  v28 = (v24 << 8) ^ HIBYTE(v25);
  v29 = (v25 << 8) ^ HIBYTE(v24);
  v43[20] = dword_509AB8[(unsigned __int8)(v28 + v26 - 120)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v28 + v26 + 6536) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v28 + v26 + 572070280) >> 16)] ^ dword_50A6B8[(v28 + v26 + 572070280) >> 24];
  v43[21] = dword_509AB8[(unsigned __int8)(v27 - v29 + 120)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v27 - v29 - 6536) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v27 - v29 - 572070280) >> 16)] ^ dword_50A6B8[(v27 - v29 - 572070280) >> 24];
  v30 = (v26 >> 8) ^ (v27 << 24);
  v31 = (v27 >> 8) ^ (v26 << 24);
  v43[22] = dword_509AB8[(unsigned __int8)(v28 + v30 + 15)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v28 + v30 + 13071) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v28 + v30 + 1144140559) >> 16)] ^ dword_50A6B8[(v28 + v30 + 1144140559) >> 24];
  v43[23] = dword_509AB8[(unsigned __int8)(v31 - v29 - 15)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v31 - v29 - 13071) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v31 - v29 - 1144140559) >> 16)] ^ dword_50A6B8[(v31 - v29 - 1144140559) >> 24];
  v32 = (v28 << 8) ^ HIBYTE(v29);
  v33 = (v29 << 8) ^ HIBYTE(v28);
  v43[24] = dword_509AB8[(unsigned __int8)(v32 + v30 + 29)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v32 + v30 + 26141) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v32 + v30 - 2006686179) >> 16)] ^ dword_50A6B8[(v32 + v30 - 2006686179) >> 24];
  v43[25] = dword_509AB8[(unsigned __int8)(v31 - v33 - 29)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v31 - v33 - 26141) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v31 - v33 + 2006686179) >> 16)] ^ dword_50A6B8[(v31 - v33 + 2006686179) >> 24];
  v34 = (v30 >> 8) ^ (v31 << 24);
  v35 = (v31 >> 8) ^ (v30 << 24);
  v43[26] = dword_509AB8[(unsigned __int8)(v32 + v34 + 58)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v32 + v34 - 13254) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v32 + v34 + 281594938) >> 16)] ^ dword_50A6B8[(v32 + v34 + 281594938) >> 24];
  v43[27] = dword_509AB8[(unsigned __int8)(v35 - v33 - 58)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v35 - v33 + 13254) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v35 - v33 - 281594938) >> 16)] ^ dword_50A6B8[(v35 - v33 - 281594938) >> 24];
  v36 = (v32 << 8) ^ HIBYTE(v33);
  v39 = (v33 << 8) ^ HIBYTE(v32);
  v43[28] = dword_509AB8[(unsigned __int8)(v36 + v34 + 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v36 + v34 - 26509) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v36 + v34 + 563189875) >> 16)] ^ dword_50A6B8[(v36 + v34 + 563189875) >> 24];
  v43[29] = dword_509AB8[(unsigned __int8)(v35 - HIBYTE(v32) - 115)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v35 - v39 + 26509) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v35 - v39 - 563189875) >> 16)] ^ dword_50A6B8[(v35 - v39 - 563189875) >> 24];
  v37 = ((v35 >> 8) ^ (v34 << 24)) - v39 - 1126379749;
  v43[30] = dword_509AB8[(unsigned __int8)(v36 + BYTE1(v34) - 27)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)(v36 + (v34 >> 8) + 12517) >> 8)] ^ dword_50A2B8[(unsigned __int8)((v36 + ((v34 >> 8) ^ (v35 << 24)) + 1126379749) >> 16)] ^ dword_50A6B8[(v36 + ((v34 >> 8) ^ (v35 << 24)) + 1126379749) >> 24];
  v43[31] = dword_509AB8[(unsigned __int8)(BYTE1(v35) - HIBYTE(v32) + 27)] ^ dword_509EB8[(unsigned __int8)((unsigned __int16)((v35 >> 8) - v39 - 12517) >> 8)] ^ dword_50A2B8[BYTE2(v37)] ^ dword_50A6B8[HIBYTE(v37)];
  return v43[a3];
}
// 509AB8: using guessed type int dword_509AB8[256];
// 509EB8: using guessed type int dword_509EB8[256];
// 50A2B8: using guessed type int dword_50A2B8[256];
// 50A6B8: using guessed type int dword_50A6B8[256];

//----- (00448410) --------------------------------------------------------
unsigned int __thiscall CCSAuth::CheckAuthDword(CCSAuth *this, unsigned int a2)
{
  unsigned int m_dwCurrIndex; // eax
  char v4; // al
  unsigned int v5; // eax
  unsigned int v6; // ecx
  unsigned int result; // eax

  m_dwCurrIndex = this->m_dwCurrIndex;
  if ( m_dwCurrIndex > 8 )
    return 0;
  v4 = this->m_table[0][4 * m_dwCurrIndex + m_dwCurrIndex];
  if ( v4 >= 32 )
    return 0;
  v5 = CCSAuth::TransAuthDword(this, this->m_dwAuthValue, v4);
  v6 = this->m_dwCurrIndex;
  if ( a2 != (v5 ^ dword_509AB8[(unsigned __int8)this->m_table[0][4 * v6 + 1 + v6]] ^ dword_509EB8[(unsigned __int8)this->m_table[0][4 * v6 + 2 + v6]] ^ dword_50A2B8[(unsigned __int8)this->m_table[0][4 * v6 + 3 + v6]] ^ dword_50A6B8[(unsigned __int8)this->m_table[0][4 * v6 + 4 + v6]]) )
    return 0;
  result = 1;
  this->m_bAuth = 1;
  return result;
}
// 509AB8: using guessed type int dword_509AB8[256];
// 509EB8: using guessed type int dword_509EB8[256];
// 50A2B8: using guessed type int dword_50A2B8[256];
// 50A6B8: using guessed type int dword_50A6B8[256];

//----- (004484A0) --------------------------------------------------------
int __thiscall CCSAuth::DecryptPacket(CCSAuth *this, unsigned __int8 *a2, unsigned int a3)
{
  unsigned int v3; // esi
  unsigned __int16 v5; // cx
  unsigned int v6; // esi
  int v7; // ebp
  int v8; // edi
  int v9; // ecx
  unsigned __int16 v10; // dx
  unsigned int m_dwClientSequenceNumber; // eax
  int v13; // [esp-8h] [ebp-1Ch]
  unsigned __int8 *v14; // [esp+10h] [ebp-4h]

  v3 = a3;
  v13 = a3;
  this->m_dwPPLastError = 0;
  SeedDecrypt(a2, v13, &dword_5277FC);
  v14 = &a2[v3 - 16];
  LOBYTE(v5) = 0;
  HIBYTE(v5) = v14[2];
  v6 = v14[3] | v5 | (v14[1] << 16) | (*v14 << 24);
  LOBYTE(v5) = 0;
  HIBYTE(v5) = v14[6];
  v7 = v14[7] | v5 | (v14[5] << 16) | (v14[4] << 24);
  LOBYTE(v5) = 0;
  HIBYTE(v5) = v14[10];
  v8 = v14[11] | v5 | (v14[9] << 16) | (v14[8] << 24);
  v9 = (v14[13] << 16) | (v14[12] << 24);
  HIBYTE(v10) = v14[14];
  LOBYTE(v10) = v14[15];
  this->m_dwSeq = v6;
  if ( (v10 | v9) == (unsigned __int16)dword_50AABC )
  {
    if ( dword_527924
      && ((m_dwClientSequenceNumber = this->m_dwClientSequenceNumber) != 0
        ? (this->m_dwClientSequenceNumber = m_dwClientSequenceNumber + 1)
        : (this->m_dwClientSequenceNumber = v6),
          v6 && v6 != this->m_dwClientSequenceNumber) )
    {
      if ( CCSAuth::CheckLastPacket(this, v6) )
        this->m_dwPPLastError = 2;
      else
        this->m_dwPPLastError = 3;
      return 0;
    }
    else
    {
      a3 = 0;
      CCrc32::Crc32Mem((CCrc32 *)&unk_5273F8, a2, v8 - (v8 & 0xF) + 20, &a3);
      if ( v7 == a3 )
      {
        *(_DWORD *)v14 = 0;
        *((_DWORD *)v14 + 1) = 0;
        *((_DWORD *)v14 + 2) = 0;
        *((_DWORD *)v14 + 3) = 0;
        return v8;
      }
      else
      {
        this->m_dwPPLastError = 4;
        return 0;
      }
    }
  }
  else
  {
    this->m_dwPPLastError = 1;
    return 0;
  }
}
// 4485C3: conditional instruction was optimized away because esi.4>=FFFFFF9Cu
// 50AABC: using guessed type int dword_50AABC;
// 527924: using guessed type int dword_527924;

//----- (00448640) --------------------------------------------------------
unsigned int __thiscall CCSAuth::CheckLastPacket(CCSAuth *this, unsigned int a2)
{
  unsigned int *m_adwLastSeqs; // eax
  int v3; // edx
  unsigned int *v4; // esi
  int v5; // edx

  m_adwLastSeqs = this->m_adwLastSeqs;
  v3 = 0;
  v4 = this->m_adwLastSeqs;
  do
  {
    if ( *v4 == a2 )
      return 1;
    ++v3;
    ++v4;
  }
  while ( v3 < 12 );
  v5 = 11;
  do
  {
    *m_adwLastSeqs = m_adwLastSeqs[1];
    ++m_adwLastSeqs;
    --v5;
  }
  while ( v5 );
  this->m_adwLastSeqs[11] = a2;
  return 0;
}

//----- (00448680) --------------------------------------------------------
unsigned int __thiscall CCSAuth::PPGetLastError(CCSAuth *this)
{
  return this->m_dwPPLastError;
}

//----- (00448690) --------------------------------------------------------
void __thiscall CDBComponent::CDBComponent(CDBComponent *this)
{
  OleDB::OleDB(this);
  this->__vftable = (CDBComponent_vtbl *)&CDBComponent::`vftable';
  memset(&this->m_HanAuthParam, 0, sizeof(this->m_HanAuthParam));
  this->m_HanAuthParam.ColNum = 3;
  this->m_HanAuthParam.ColType[0] = 129;
  this->m_HanAuthParam.eParamIO[0] = 1;
  this->m_HanAuthParam.ColType[1] = 129;
  this->m_HanAuthParam.eParamIO[1] = 1;
  this->m_HanAuthParam.ColSize[0] = 16;
  this->m_HanAuthParam.ColSize[1] = 32;
  this->m_HanAuthParam.ColSize[2] = 8;
  this->m_HanAuthParam.ColType[2] = 20;
  this->m_HanAuthParam.eParamIO[2] = 2;
  memset(&this->m_HanRegParam, 0, sizeof(this->m_HanRegParam));
  this->m_HanRegParam.ColType[0] = 129;
  this->m_HanRegParam.eParamIO[0] = 1;
  this->m_HanRegParam.ColType[1] = 129;
  this->m_HanRegParam.eParamIO[1] = 1;
  this->m_HanRegParam.ColSize[0] = 16;
  this->m_HanRegParam.ColSize[1] = 3;
  this->m_HanRegParam.ColNum = 2;
}
// 4E012C: using guessed type void *CDBComponent::`vftable';

//----- (00448760) --------------------------------------------------------
void __thiscall CDBComponent::~CDBComponent(CDBSingleObject *this)
{
  this->__vftable = (CDBSingleObject_vtbl *)&CDBComponent::`vftable';
  OleDB::~OleDB(this);
}
// 4E012C: using guessed type void *CDBComponent::`vftable';

//----- (00448770) --------------------------------------------------------
char __userpurge CDBComponent::GetUIDFromBattleUID@<al>(
        CDBComponent *this@<ecx>,
        int a2@<ebx>,
        unsigned int dwBattleUID,
        unsigned int *lpUID)
{
  void *v6; // [esp+0h] [ebp-208h]
  char Query[512]; // [esp+4h] [ebp-204h] BYREF

  sprintf(Query, "USPGetUID %d", dwBattleUID);
  return OleDB::ExcuteQueryGetData(this, a2, (IUnknown *)Query, (IRowset *)lpUID, v6);
}
// 4487AE: variable 'v6' is possibly undefined

//----- (004487D0) --------------------------------------------------------
CDBComponent *__thiscall CDBSingleObject::`scalar deleting destructor'(CDBComponent *this, char a2)
{
  CDBComponent::~CDBComponent((CDBSingleObject *)this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004487F0) --------------------------------------------------------
char __thiscall CDBComponent::Connect(CDBComponent *this, int DBClass_In)
{
  CServerSetup::DBInfo *p_m_stAdminToolDBInfo; // eax
  const char *m_szDBPass; // edi
  char *UserID_In; // [esp+10h] [ebp-430h]
  char *ServerName_In; // [esp+14h] [ebp-42Ch]
  char DataSourceAccount[264]; // [esp+18h] [ebp-428h] BYREF
  char DataSourceName[264]; // [esp+120h] [ebp-320h] BYREF
  char DataSourcePass[264]; // [esp+228h] [ebp-218h] BYREF
  char DBServerName[268]; // [esp+330h] [ebp-110h] BYREF

  switch ( DBClass_In )
  {
    case 1:
      memset(DataSourceAccount, 0, 260);
      memset(DataSourceName, 0, 260);
      memset(DBServerName, 0, 260);
      memset(DataSourcePass, 0, 260);
      Registry::ReadString("DemonSetup.ini", "Auth", "DataSourceName", DataSourceAccount, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Auth", "DataSourceAccount", DataSourceName, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Auth", "DataSourcePass", DBServerName, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Auth", "ServerType", DataSourcePass, 0x104u);
      if ( !atoi(DataSourcePass) || atoi(DataSourcePass) == 3 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "CDBComponent::Connect",
          aDWorkRylSource_100,
          110,
          (char *)&byte_4E0230,
          DataSourceAccount,
          DataSourceName,
          DBServerName);
        if ( !OleDB::ConnectSQLServer(this, DataSourceAccount, 0, DataSourceName, DBServerName, ConnType_ORACLE) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CDBComponent::Connect",
            aDWorkRylSource_100,
            115,
            this->m_ErrorString);
          return 0;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "CDBComponent::Connect",
          aDWorkRylSource_100,
          121,
          (char *)&byte_4E0308,
          DataSourceAccount,
          DataSourceName,
          DBServerName);
        if ( !OleDB::ConnectSQLServer(this, DataSourceAccount, 0, DataSourceName, DBServerName, ConnType_ODBC) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CDBComponent::Connect",
            aDWorkRylSource_100,
            126,
            this->m_ErrorString);
          return 0;
        }
      }
      break;
    case 2:
      memset(DBServerName, 0, 260);
      memset(DataSourceName, 0, 260);
      memset(DataSourceAccount, 0, 260);
      memset(DataSourcePass, 0, 260);
      Registry::ReadString("DemonSetup.ini", "Agent", "DBServerName", DBServerName, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Agent", "DBName", DataSourceName, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Agent", "DBAccount", DataSourceAccount, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Agent", "DBPass", DataSourcePass, 0x104u);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CDBComponent::Connect",
        aDWorkRylSource_100,
        140,
        (char *)&byte_4E01B0,
        DBServerName,
        DataSourceName,
        DataSourceAccount,
        DataSourcePass);
      if ( !OleDB::ConnectSQLServer(
              this,
              DBServerName,
              DataSourceName,
              DataSourceAccount,
              DataSourcePass,
              ConnType_MSSQL) )
      {
        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CDBComponent::Connect", aDWorkRylSource_100, 144, this->m_ErrorString);
        return 0;
      }
      break;
    case 3:
      memset(DataSourceName, 0, 260);
      memset(DataSourceAccount, 0, 260);
      memset(DataSourcePass, 0, 260);
      Registry::ReadString("DemonSetup.ini", "Keeper", "DataSourceName", DataSourceName, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Keeper", "DataSourceAccount", DataSourceAccount, 0x104u);
      Registry::ReadString("DemonSetup.ini", "Keeper", "DataSourcePass", DataSourcePass, 0x104u);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CDBComponent::Connect",
        aDWorkRylSource_100,
        156,
        (char *)&byte_4E0170,
        DataSourceName,
        DataSourceAccount,
        DataSourcePass);
      if ( !OleDB::ConnectSQLServer(this, DataSourceName, 0, DataSourceAccount, DataSourcePass, ConnType_ODBC) )
      {
        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CDBComponent::Connect", aDWorkRylSource_100, 160, this->m_ErrorString);
        return 0;
      }
      break;
    case 4:
      p_m_stAdminToolDBInfo = &CServerSetup::GetInstance()->m_stAdminToolDBInfo;
      m_szDBPass = p_m_stAdminToolDBInfo->m_szDBPass;
      UserID_In = p_m_stAdminToolDBInfo->m_szDBAccount;
      ServerName_In = p_m_stAdminToolDBInfo->m_szDBName;
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CDBComponent::Connect",
        aDWorkRylSource_100,
        169,
        (char *)&byte_4E0130,
        p_m_stAdminToolDBInfo->m_szDBName,
        p_m_stAdminToolDBInfo->m_szDBAccount,
        p_m_stAdminToolDBInfo->m_szDBPass);
      if ( !OleDB::ConnectSQLServer(this, ServerName_In, 0, UserID_In, m_szDBPass, ConnType_ODBC) )
      {
        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CDBComponent::Connect", aDWorkRylSource_100, 173, this->m_ErrorString);
        return 0;
      }
      break;
  }
  return 1;
}



//----- (00448D50) --------------------------------------------------------
CDBSingleObject *__cdecl CDBSingleObject::GetInstance()
{
  if ( (_S1_3 & 1) == 0 )
  {
    _S1_3 |= 1u;
    CDBComponent::CDBComponent(&dbSingleObject);
    dbSingleObject.__vftable = (CDBSingleObject_vtbl *)&CDBComponent::`vftable';
    atexit(_E2_6);
  }
  return &dbSingleObject;
}
// 4E012C: using guessed type void *CDBComponent::`vftable';

//----- (00448DC0) --------------------------------------------------------
void __thiscall CDBAgentDispatch::CDBAgentDispatch(CDBAgentDispatch *this, CSession *Session)
{
  CRylServerDispatch::CRylServerDispatch(this, Session, 0xC8u);
  this->__vftable = (CDBAgentDispatch_vtbl *)&CDBAgentDispatch::`vftable';
}
// 4E03A0: using guessed type void *CDBAgentDispatch::`vftable';

//----- (00448DE0) --------------------------------------------------------
void __thiscall CDBAgentDispatch::~CDBAgentDispatch(CDBAgentDispatch *this)
{
  this->__vftable = (CDBAgentDispatch_vtbl *)&CDBAgentDispatch::`vftable';
  CRylServerDispatch::~CRylServerDispatch(this);
}
// 4E03A0: using guessed type void *CDBAgentDispatch::`vftable';

//----- (00448DF0) --------------------------------------------------------
void __thiscall CDBAgentDispatch::Connected(CDBAgentDispatch *this)
{
  CSendStream *p_m_SendStream; // edi
  char *Buffer; // esi
  CServerSetup *Instance; // eax
  CServerSetup *v5; // eax
  unsigned int ServerID; // eax
  HWND WindowA; // eax
  HWND v8; // esi
  CTCPFactory tcpFactory; // [esp+Ch] [ebp-A4h] BYREF
  char szAddress[128]; // [esp+20h] [ebp-90h] BYREF
  int v11; // [esp+ACh] [ebp-4h]

  p_m_SendStream = &this->m_SendStream;
  Buffer = CSendStream::GetBuffer(&this->m_SendStream, (char *)0x14);
  if ( Buffer )
  {
    CTCPFactory::CTCPFactory(&tcpFactory);
    v11 = 0;
    CINETFamilyFactory::GetNetworkInfo(&tcpFactory, szAddress, 128);
    *((_DWORD *)Buffer + 4) = inet_addr(szAddress);
    Instance = CServerSetup::GetInstance();
    *((_DWORD *)Buffer + 3) = CServerSetup::GetServerID(Instance);
    if ( CSendStream::WrapHeader(p_m_SendStream, 0x14u, 0x28u, 0, 0) )
    {
      v5 = CServerSetup::GetInstance();
      ServerID = CServerSetup::GetServerID(v5);
      CServerLog::SimpleLog(&g_Log, 2, (char *)&byte_4E0408, this, ServerID);
      WindowA = FindWindowA(0, "RYLMANAGERCLIENT");
      v8 = WindowA;
      if ( WindowA )
      {
        CServerLog::SimpleLog(&g_Log, 2, (char *)&byte_4E03BC, WindowA, "1234", 1234);
        SendMessageA(v8, 0x4D2u, 0, 0);
      }
    }
  }
}

//----- (00448F20) --------------------------------------------------------
char __thiscall CDBAgentDispatch::DispatchPacket(CDBAgentDispatch *this, PktAdmin *lpPktBase)
{
  char updated; // al

  switch ( lpPktBase->m_Cmd )
  {
    case 4u:
      updated = DBAgentPacketParse::ParseUserKill(this, lpPktBase);
      goto LABEL_3;
    case 0x26u:
      updated = DBAgentPacketParse::ParseUpdateDBData(this, lpPktBase);
      goto LABEL_3;
    case 0x27u:
      updated = DBAgentPacketParse::ParseAgentParty(this, lpPktBase);
      goto LABEL_3;
    case 0x28u:
      updated = DBAgentPacketParse::ParseSysServerLogin(this, lpPktBase);
      goto LABEL_3;
    case 0x4Eu:
      updated = DBAgentPacketParse::ParseSysServerVerUpdate(this, lpPktBase);
      goto LABEL_3;
    case 0x50u:
      updated = DBAgentPacketParse::ParseSysChannelUpdate(this, lpPktBase);
      goto LABEL_3;
    case 0x5Bu:
      updated = DBAgentPacketParse::ParseAgentZone(this, lpPktBase);
      goto LABEL_3;
    case 0x5Cu:
      updated = DBAgentPacketParse::ParseDepositCmd(this, lpPktBase);
      goto LABEL_3;
    case 0x5Du:
      updated = DBAgentPacketParse::ParseDepositUpdate(this, lpPktBase);
      goto LABEL_3;
    case 0x62u:
      updated = DBAgentPacketParse::ParseCharAdminCmd(this, lpPktBase);
      goto LABEL_3;
    case 0x68u:
      updated = DBAgentPacketParse::ParseFriendList(this, lpPktBase);
      goto LABEL_3;
    case 0x6Cu:
      updated = DBAgentPacketParse::ParseQuestDB(this, lpPktBase);
      goto LABEL_3;
    case 0x71u:
      updated = DBAgentPacketParse::ParseConfigInfoDB(this, lpPktBase);
      goto LABEL_3;
    case 0x73u:
      updated = DBAgentPacketParse::ParseSysRankingUpdate(this, lpPktBase);
      goto LABEL_3;
    case 0x75u:
      updated = DBAgentPacketParse::ParseBillingTimeoutNotify(this, lpPktBase);
      goto LABEL_3;
    case 0x78u:
      updated = DBAgentPacketParse::ParseEventDropItem(this, lpPktBase);
      goto LABEL_3;
    case 0x7Au:
      updated = DBAgentPacketParse::ParseBillingTimeCheckNotify(this, lpPktBase);
      goto LABEL_3;
    case 0x88u:
      updated = DBAgentPacketParse::ParseCreateGuild(this, lpPktBase);
      goto LABEL_3;
    case 0x89u:
      updated = DBAgentPacketParse::ParseGuildCmd(this, lpPktBase);
      goto LABEL_3;
    case 0x8Au:
      updated = DBAgentPacketParse::ParseGuildMark(this, lpPktBase);
      goto LABEL_3;
    case 0x8Bu:
      updated = DBAgentPacketParse::ParseGuildLevel(this, lpPktBase);
      goto LABEL_3;
    case 0x8Eu:
      updated = DBAgentPacketParse::ParseGuildDB(this, lpPktBase);
      goto LABEL_3;
    case 0x8Fu:
      updated = DBAgentPacketParse::ParseGuildRight(this, lpPktBase);
      goto LABEL_3;
    case 0x92u:
      updated = DBAgentPacketParse::ParseGuildSafe(this, lpPktBase);
      goto LABEL_3;
    case 0x93u:
      updated = DBAgentPacketParse::ParseGuildMemberInfoUpdate(this, lpPktBase);
      goto LABEL_3;
    case 0x95u:
      CRylServerDispatch::CloseSession(this);
      return 1;
    case 0xA3u:
      updated = DBAgentPacketParse::ParseGuildInclination(this, lpPktBase);
      goto LABEL_3;
    case 0xA5u:
      updated = DBAgentPacketParse::ParseCastleInfo(this, lpPktBase);
      goto LABEL_3;
    case 0xA6u:
      updated = DBAgentPacketParse::ParseCampInfo(this, lpPktBase);
      goto LABEL_3;
    case 0xA8u:
      updated = DBAgentPacketParse::ParseCreateCamp(this, lpPktBase);
      goto LABEL_3;
    case 0xA9u:
      updated = DBAgentPacketParse::ParseCreateSiegeArms(this, lpPktBase);
      goto LABEL_3;
    case 0xAAu:
      updated = DBAgentPacketParse::ParseCastleCmd(this, lpPktBase);
      goto LABEL_3;
    case 0xABu:
      updated = DBAgentPacketParse::ParseCampCmd(this, lpPktBase);
      goto LABEL_3;
    case 0xACu:
      updated = DBAgentPacketParse::ParseSiegeArmsCmd(this, lpPktBase);
      goto LABEL_3;
    case 0xADu:
      updated = DBAgentPacketParse::ParseCastleRight(this, lpPktBase);
      goto LABEL_3;
    case 0xAEu:
      updated = DBAgentPacketParse::ParseCampRight(this, lpPktBase);
      goto LABEL_3;
    case 0xB0u:
      updated = DBAgentPacketParse::ParseSiegeTimeInfo(this, lpPktBase);
      goto LABEL_3;
    case 0xB2u:
      updated = DBAgentPacketParse::ParseCastleUpdate(this, lpPktBase);
LABEL_3:
      if ( !updated )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CDBAgentDispatch::DispatchPacket",
          aDWorkRylSource_16,
          154,
          "DP:0x%p/Cmd:0x%02x/Agentpacket Process failed",
          this,
          lpPktBase->m_Cmd);
      break;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CDBAgentDispatch::DispatchPacket",
        aDWorkRylSource_16,
        146,
        "DP:0x%p/Cmd:0x%02x/Unknown Agentpacket",
        this,
        lpPktBase->m_Cmd);
      break;
  }
  return 1;
}

//----- (00449290) --------------------------------------------------------
CSingleDispatch *__cdecl CDBAgentDispatch::GetDispatchTable()
{
  if ( (_S5_4 & 1) == 0 )
  {
    _S5_4 |= 1u;
    CSingleDispatch::CSingleDispatch(&singleDispatch_0);
    atexit(_E6_9);
  }
  return &singleDispatch_0;
}

//----- (004492F0) --------------------------------------------------------
CDBAgentDispatch *__thiscall CDBAgentDispatch::`scalar deleting destructor'(CDBAgentDispatch *this, char a2)
{
  CDBAgentDispatch::~CDBAgentDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00449310) --------------------------------------------------------
void __thiscall CDBAgentDispatch::Disconnected(CDBAgentDispatch *this)
{
  HWND WindowA; // eax
  CSingleDispatch *DispatchTable; // eax

  WindowA = FindWindowA(0, "RYLMANAGERCLIENT");
  if ( WindowA )
    SendMessageA(WindowA, 0x4C9u, 0, 0);
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::RemoveDispatch(DispatchTable, this);
}

//----- (00449350) --------------------------------------------------------
void __thiscall CNullSpell::CNullSpell(CNullSpell *this)
{
  const Skill::ProtoType *v2; // eax
  CSpell::Spell_Info spell_Info; // [esp+4h] [ebp-210h] BYREF
  Skill::ProtoType v4; // [esp+14h] [ebp-200h] BYREF

  Skill::ProtoType::ProtoType(&v4);
  spell_Info.m_SkillProtoType = v2;
  memset(&spell_Info.m_lpCaster, 0, 11);
  CSpell::CSpell(this, &spell_Info, NONE, 0);
  this->__vftable = (CNullSpell_vtbl *)&CNullSpell::`vftable';
}
// 449362: variable 'v2' is possibly undefined
// 4E0568: using guessed type void *CNullSpell::`vftable';

//----- (004493A0) --------------------------------------------------------
char __thiscall CNullSpell::Activate(CNullSpell *this, CAggresiveCreature *pAffected, unsigned int dwOperateFlag)
{
  return 1;
}

//----- (004493B0) --------------------------------------------------------
CNullSpell *__thiscall CNullSpell::`scalar deleting destructor'(CNullSpell *this, char a2)
{
  CChantSpell::~CChantSpell((CChantSpell *)this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004493D0) --------------------------------------------------------
void __thiscall CGlobalSpellMgr::Process(CGlobalSpellMgr *this)
{
  CSpell *m_pNextSpell; // esi
  CNullSpell *p_m_HeadSpell; // ebx

  m_pNextSpell = this->m_HeadSpell.m_pNextSpell;
  p_m_HeadSpell = &this->m_HeadSpell;
  while ( m_pNextSpell )
  {
    if ( m_pNextSpell->m_cAffectedNum && CSpell::Operate(m_pNextSpell) )
    {
      p_m_HeadSpell = (CNullSpell *)m_pNextSpell;
      m_pNextSpell = m_pNextSpell->m_pNextSpell;
    }
    else
    {
      p_m_HeadSpell->m_pNextSpell = m_pNextSpell->m_pNextSpell;
      ((void (__thiscall *)(CSpell *, int))m_pNextSpell->~CSpell)(m_pNextSpell, 1);
      m_pNextSpell = p_m_HeadSpell->m_pNextSpell;
      --this->m_nSpellNum;
    }
  }
}

//----- (00449420) --------------------------------------------------------
void __thiscall CGlobalSpellMgr::Add(CGlobalSpellMgr *this, CSpell *pSpell)
{
  if ( pSpell )
  {
    pSpell->m_pNextSpell = this->m_HeadSpell.m_pNextSpell;
    this->m_HeadSpell.m_pNextSpell = pSpell;
    ++this->m_nSpellNum;
  }
}

//----- (00449440) --------------------------------------------------------
void __thiscall CGlobalSpellMgr::~CGlobalSpellMgr(CGlobalSpellMgr *this)
{
  CSpell *m_pNextSpell; // esi
  CSpell *v3; // ecx
  CSpell_vtbl *v4; // eax

  m_pNextSpell = this->m_HeadSpell.m_pNextSpell;
  while ( m_pNextSpell )
  {
    v3 = m_pNextSpell;
    v4 = m_pNextSpell->__vftable;
    m_pNextSpell = m_pNextSpell->m_pNextSpell;
    ((void (__thiscall *)(CSpell *, int))v4->~CSpell)(v3, 1);
  }
  this->m_nSpellNum = 0;
  this->m_HeadSpell.__vftable = (CNullSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(&this->m_HeadSpell);
}
// 4D8E10: using guessed type void *CSpell::`vftable';

//----- (004494B0) --------------------------------------------------------
CGlobalSpellMgr *__cdecl CGlobalSpellMgr::GetInstance()
{
  if ( (_S5_5 & 1) == 0 )
  {
    _S5_5 |= 1u;
    globalSpellMgr.m_nSpellNum = 0;
    CNullSpell::CNullSpell(&globalSpellMgr.m_HeadSpell);
    atexit(_E6_10);
  }
  return &globalSpellMgr;
}

//----- (00449510) --------------------------------------------------------
void __thiscall Item::CDepositContainer::SetTabFlag(Item::CDepositContainer *this, unsigned int dwTabFlag)
{
  this->m_dwTabFlag = dwTabFlag | 1;
  if ( memcmp((const char *)this->m_szPassword, "0000", 4) || (dwTabFlag & 0x40000000) != 0 )
    this->m_dwTabFlag = dwTabFlag | 0x40000001;
  else
    this->m_dwTabFlag = dwTabFlag & 0xBFFFFFFE | 1;
}

//----- (00449560) --------------------------------------------------------
void __thiscall CGameClientDispatch::SetMoveAddress(
        CGameClientDispatch *this,
        unsigned int dwMoveServerID,
        const INET_Addr *moveAddress)
{
  this->m_dwMoveServerID = dwMoveServerID;
  this->m_MoveAddress = *moveAddress;
}

//----- (004495A0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseDepositUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v2; // eax
  int v4; // ebp
  CCharacter *v5; // esi
  unsigned int m_dwCID; // eax
  CRylServerDispatch *m_lpGameClientDispatch; // edi
  bool v8; // al
  void *v9; // edx
  sockaddr_in *RemoteAddr; // ebx
  CServerRequest::Result result; // [esp+8h] [ebp-14h] BYREF
  int v12; // [esp+18h] [ebp-4h]

  v2 = lpPktBase->m_Len & 0x3FFF;
  if ( v2 < 0x22 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositUpdate",
      aDWorkRylSource_86,
      117,
      (char *)&byte_4E0800,
      34,
      lpPktBase->m_Len & 0x3FFF);
    return 0;
  }
  if ( HIWORD(lpPktBase[2].m_CodePage) + 34 != v2 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositUpdate",
      aDWorkRylSource_86,
      117,
      (char *)&byte_4E07C8,
      v2,
      HIWORD(lpPktBase[2].m_CodePage) + 34);
    return 0;
  }
  CServerRequest::Result::Result(
    &result,
    *(_DWORD *)&lpPktBase[1].m_StartBit,
    HIBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError));
  v4 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
  v12 = 0;
  if ( !result.m_lpSrcDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositUpdate",
      aDWorkRylSource_86,
      171,
      "UID:%d/CID:0x%08x/RequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      v4,
      *(_DWORD *)&lpPktBase[1].m_StartBit);
    goto LABEL_19;
  }
  v5 = (CCharacter *)result.m_lpSrcDispatch[10].__vftable;
  if ( !v5 )
  {
    m_dwCID = 0;
    goto LABEL_17;
  }
  m_dwCID = v5->m_dwCID;
  if ( v4 != m_dwCID )
  {
LABEL_17:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositUpdate",
      aDWorkRylSource_86,
      165,
      "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/CharacterCID:0x%08x/ Invalid CID",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      v4,
      v5,
      *(_DWORD *)&lpPktBase[1].m_StartBit,
      m_dwCID);
LABEL_19:
    v12 = -1;
    CServerRequest::Result::~Result(&result);
    return 1;
  }
  Item::CDepositContainer::SetTabFlag(&v5->m_Deposit, *(_DWORD *)&lpPktBase[2].m_Len);
  if ( !v5->m_Deposit.SerializeIn(
          &v5->m_Deposit,
          (const char *)&lpPktBase[2].m_SrvInfo.dwServerInfo + 2,
          HIWORD(lpPktBase[2].m_CodePage)) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositUpdate",
      aDWorkRylSource_86,
      140,
      aCid0x08x_119,
      v4);
  m_lpGameClientDispatch = v5->m_lpGameClientDispatch;
  if ( !HIBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError) || !m_lpGameClientDispatch )
    goto LABEL_19;
  v8 = v5->IsPeaceMode(v5);
  v9 = &unk_4E06CC;
  if ( !v8 )
    v9 = &unk_4E06C0;
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "DBAgentPacketParse::ParseDepositUpdate",
    aDWorkRylSource_86,
    151,
    aUidDCid0x08x0x_11,
    v5->m_dwUID,
    v4,
    v5,
    m_lpGameClientDispatch[1].m_Session,
    m_lpGameClientDispatch,
    v9);
  RemoteAddr = (sockaddr_in *)CRylServerDispatch::GetRemoteAddr(m_lpGameClientDispatch);
  CCharacter::Login(v5, MIDDLE_ADMIN);
  LOBYTE(RemoteAddr) = GameClientSendPacket::SendCharLogin(&m_lpGameClientDispatch->m_SendStream, v5, RemoteAddr, 0);
  v12 = -1;
  CServerRequest::Result::~Result(&result);
  return (char)RemoteAddr;
}

//----- (004497D0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseDepositCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  void (__thiscall *v3)(CPacketDispatch *); // edi
  CPacketDispatch_vtbl *v4; // eax
  void (__thiscall *Connected)(CPacketDispatch *); // ecx
  CServerRequest::Result result; // [esp+4h] [ebp-14h] BYREF
  int v7; // [esp+14h] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1B )
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(void (__thiscall **)(CPacketDispatch *))((char *)&lpPktBase[1].m_CodePage + 3);
  CServerRequest::Result::Result(&result, *(_DWORD *)&lpPktBase[1].m_StartBit, 0);
  v7 = 0;
  if ( result.m_lpSrcDispatch )
  {
    v4 = result.m_lpSrcDispatch[10].__vftable;
    if ( v4 )
    {
      Connected = v4[1].Connected;
      if ( v3 == Connected )
      {
        if ( BYTE2(lpPktBase[1].m_CodePage) == 7 )
        {
          v4[32].Dispatch = *(bool (__thiscall **)(CPacketDispatch *))((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 3);
        }
        else if ( BYTE2(lpPktBase[1].m_CodePage) == 8 )
        {
          Item::CDepositContainer::AddGold(
            (Item::CDepositContainer *)&v4[30].Connected,
            *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 3));
        }
        goto LABEL_13;
      }
    }
    else
    {
      Connected = 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositCmd",
      aDWorkRylSource_86,
      216,
      "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/CharacterCID:0x%08x/ Invalid CID",
      0,
      v3,
      v4,
      *(_DWORD *)&lpPktBase[1].m_StartBit,
      Connected);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseDepositCmd",
      aDWorkRylSource_86,
      222,
      "UID:%d/CID:0x%08x/RequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      0,
      v3,
      *(_DWORD *)&lpPktBase[1].m_StartBit);
  }
LABEL_13:
  v7 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}

//----- (00449900) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseConfigInfoDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v3; // edx
  int v4; // ebp
  int v5; // ebx
  CCharacter *v6; // edi
  unsigned int m_dwCID; // eax
  char *lpDataPointer; // [esp+10h] [ebp-18h]
  CServerRequest::Result result; // [esp+14h] [ebp-14h] BYREF
  int v11; // [esp+24h] [ebp-4h]
  unsigned __int16 lpPktBasea; // [esp+30h] [ebp+8h]

  v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  v4 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
  v5 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
  lpDataPointer = (char *)&lpPktBase[2].m_CodePage;
  lpPktBasea = lpPktBase[2].m_Len;
  CServerRequest::Result::Result(&result, v3, 0);
  v11 = 0;
  if ( !result.m_lpSrcDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseConfigInfoDB",
      aDWorkRylSource_86,
      275,
      "UID:%d/CID:0x%08x/RequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      v4,
      v5,
      *(_DWORD *)&lpPktBase[1].m_StartBit);
    goto LABEL_9;
  }
  v6 = (CCharacter *)result.m_lpSrcDispatch[10].__vftable;
  if ( !v6 )
  {
    m_dwCID = 0;
    goto LABEL_7;
  }
  m_dwCID = v6->m_dwCID;
  if ( v5 != m_dwCID )
  {
LABEL_7:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseConfigInfoDB",
      aDWorkRylSource_86,
      269,
      "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/CharacterCID:0x%08x/ Invalid CID",
      v4,
      v5,
      v6,
      *(_DWORD *)&lpPktBase[1].m_StartBit,
      m_dwCID);
    goto LABEL_9;
  }
  if ( lpPktBasea )
  {
    CCharacter::SetPeaceMode(v6, *(PeaceModeInfo *)lpDataPointer, 1);
    CCharacter::ControlOption(v6, *(RejectOption *)(lpDataPointer + 9), 1);
  }
LABEL_9:
  v11 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}

//----- (00449A30) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSysServerVerUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int dwServerInfo; // edi
  unsigned int v4; // esi

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x80 )
  {
    dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
    CServerSetup::GetInstance()->m_dwClientVer = dwServerInfo;
    v4 = *(_DWORD *)&lpPktBase[2].m_StartBit;
    CServerSetup::GetInstance()->m_dwCheckSum = v4;
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00449A80) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSysChannelUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CCreatureManager *Instance; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1B )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::CalculateEliteBonus(Instance, COERCE_FLOAT((PktBase *)((char *)lpPktBase + 17)));
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00449AC0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::SendUserKill(
        CDBAgentDispatch *DBAgentDispatch,
        unsigned int dwUserID,
        unsigned __int16 wError)
{
  char *Buffer; // esi
  CServerSetup *Instance; // eax

  Buffer = CSendStream::GetBuffer(&DBAgentDispatch->m_SendStream, (char *)0x18);
  if ( !Buffer )
    return 0;
  Instance = CServerSetup::GetInstance();
  *((_DWORD *)Buffer + 5) = CServerSetup::GetServerID(Instance);
  *((_DWORD *)Buffer + 3) = dwUserID;
  *((_DWORD *)Buffer + 4) = 0;
  return CSendStream::WrapHeader(&DBAgentDispatch->m_SendStream, 0x18u, 4u, 0, wError);
}

//----- (00449B10) --------------------------------------------------------
char __cdecl DBAgentPacketParse::SendServerLogout(CDBAgentDispatch *DBAgentDispatch)
{
  if ( CSendStream::GetBuffer(&DBAgentDispatch->m_SendStream, (char *)0xC) )
    return CSendStream::WrapHeader(&DBAgentDispatch->m_SendStream, 0xCu, 0x95u, 0, 0);
  else
    return 0;
}

//----- (00449B40) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseFriendList(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v2; // eax
  CPacketDispatch_vtbl *v4; // eax
  void (__thiscall *Connected)(CPacketDispatch *); // ecx
  void (__thiscall *v6)(CPacketDispatch *); // edx
  CServerRequest::Result result; // [esp+4h] [ebp-14h] BYREF
  int v8; // [esp+14h] [ebp-4h]

  v2 = lpPktBase->m_Len & 0x3FFF;
  if ( v2 < 0x27 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseFriendList",
      aDWorkRylSource_86,
      1083,
      (char *)&byte_4E0800,
      39,
      lpPktBase->m_Len & 0x3FFF);
    return 0;
  }
  if ( *(unsigned int *)((char *)&lpPktBase[2].m_SrvInfo.dwServerInfo + 2) + 39 != v2 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseFriendList",
      aDWorkRylSource_86,
      1083,
      (char *)&byte_4E07C8,
      v2,
      *(unsigned int *)((char *)&lpPktBase[2].m_SrvInfo.dwServerInfo + 2) + 39);
    return 0;
  }
  CServerRequest::Result::Result(&result, *(_DWORD *)&lpPktBase[1].m_StartBit, 0);
  v8 = 0;
  if ( result.m_lpSrcDispatch )
  {
    v4 = result.m_lpSrcDispatch[10].__vftable;
    if ( v4 )
    {
      Connected = v4[1].Connected;
      v6 = *(void (__thiscall **)(CPacketDispatch *))((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
      if ( v6 == Connected )
      {
        if ( LOBYTE(lpPktBase[3].m_Len) == 5 )
        {
          CFriendList::SerializeIn(
            (CFriendList *)&v4[38],
            (char *)&lpPktBase[3].m_Len + 1,
            *(unsigned int *)((char *)&lpPktBase[2].m_SrvInfo.dwServerInfo + 2));
        }
        else if ( LOBYTE(lpPktBase[3].m_Len) == 6 )
        {
          CBanList::SerializeIn(
            (CBanList *)&v4[39].ParsePacket,
            (const char *)&lpPktBase[3].m_Len + 1,
            *(unsigned int *)((char *)&lpPktBase[2].m_SrvInfo.dwServerInfo + 2));
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_SYSERR,
            "DBAgentPacketParse::ParseFriendList",
            aDWorkRylSource_86,
            1113,
            aCid0x08xDbagen,
            v6,
            LOBYTE(lpPktBase[3].m_Len));
        }
        goto LABEL_17;
      }
    }
    else
    {
      Connected = 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseFriendList",
      aDWorkRylSource_86,
      1122,
      "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/CharacterCID:0x%08x/ Invalid CID",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
      v4,
      *(_DWORD *)&lpPktBase[1].m_StartBit,
      Connected);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseFriendList",
      aDWorkRylSource_86,
      1128,
      "UID:%d/CID:0x%08x/RequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
      *(_DWORD *)&lpPktBase[1].m_StartBit);
  }
LABEL_17:
  v8 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}

//----- (00449CE0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseQuestDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  int v3; // ebp
  CCharacter *v4; // ecx
  unsigned int m_dwCID; // eax
  int v6; // ebx
  unsigned __int8 *p_m_SrvInfo; // edi
  _DWORD *v8; // edx
  _DWORD *v9; // eax
  int v10; // eax
  CGameClientDispatch *lpGameClientDispatch; // [esp+8h] [ebp-2Ch]
  CServerRequest::Result result; // [esp+Ch] [ebp-28h] BYREF
  Quest::ExecutingQuest v14; // [esp+14h] [ebp-20h] BYREF
  int v15; // [esp+30h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+3Ch] [ebp+8h]

  v3 = 0;
  CServerRequest::Result::Result(&result, *(_DWORD *)&lpPktBase[1].m_StartBit, 0);
  v15 = 0;
  lpGameClientDispatch = (CGameClientDispatch *)result.m_lpSrcDispatch;
  if ( !result.m_lpSrcDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseQuestDB",
      aDWorkRylSource_86,
      1195,
      "UID:%d/CID:0x%08x/RequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
      *(_DWORD *)&lpPktBase[1].m_StartBit);
    goto LABEL_15;
  }
  v4 = (CCharacter *)result.m_lpSrcDispatch[10].__vftable;
  lpPktBasea = (PktBase *)v4;
  if ( !v4 )
  {
    m_dwCID = 0;
    goto LABEL_13;
  }
  m_dwCID = v4->m_dwCID;
  if ( *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2) != m_dwCID )
  {
LABEL_13:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseQuestDB",
      aDWorkRylSource_86,
      1189,
      "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/CharacterCID:0x%08x/ Invalid CID",
      *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
      *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
      v4,
      *(_DWORD *)&lpPktBase[1].m_StartBit,
      m_dwCID);
    goto LABEL_15;
  }
  v6 = 0;
  if ( lpPktBase[2].m_Len )
  {
    p_m_SrvInfo = (unsigned __int8 *)&lpPktBase[2].m_SrvInfo;
    do
    {
      Quest::ExecutingQuest::ExecutingQuest(&v14, *((_WORD *)p_m_SrvInfo - 1), *p_m_SrvInfo, p_m_SrvInfo + 1);
      v8 = (unsigned int *)((char *)&lpPktBasea[83].m_CodePage + v3);
      *v8 = *v9;
      v8[1] = v9[1];
      v8[2] = v9[2];
      v8[3] = v9[3];
      v8[4] = v9[4];
      v6 += 13;
      p_m_SrvInfo += 13;
      v3 += 20;
    }
    while ( v6 < lpPktBase[2].m_Len );
    v4 = (CCharacter *)lpPktBasea;
  }
  if ( v6 < LOWORD(lpPktBase[2].m_CodePage) + lpPktBase[2].m_Len )
  {
    v10 = 0;
    do
    {
      v4->m_wHistoryQuest[v10] = *(_WORD *)((char *)&lpPktBase[2].m_CodePage + v6 + 2);
      v6 += 2;
      ++v10;
    }
    while ( v6 < LOWORD(lpPktBase[2].m_CodePage) + lpPktBase[2].m_Len );
  }
  GameClientSendPacket::SendCharQuestInfo(&lpGameClientDispatch->m_SendStream, v4);
LABEL_15:
  v15 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}
// 449D74: variable 'v9' is possibly undefined

//----- (00449E80) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseBillingTimeoutNotify(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSession *v3; // ebx
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  CSession *m_Session; // edx
  CSendStream *v9; // ebx
  char *Buffer; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1A )
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(CSession **)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, m_CodePage);
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseBillingTimeoutNotify",
      aDWorkRylSource_86,
      1214,
      aCid0x08x_338,
      m_CodePage);
    return 1;
  }
  m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseBillingTimeoutNotify",
      aDWorkRylSource_86,
      1223,
      aCid0x08xCgamec,
      m_CodePage);
    return 1;
  }
  m_Session = m_lpGameClientDispatch[10].m_Session;
  if ( v3 == m_Session )
  {
    v9 = m_lpGameClientDispatch + 8;
    Buffer = CSendStream::GetBuffer(m_lpGameClientDispatch + 8, (char *)0x1A);
    if ( Buffer )
    {
      qmemcpy(Buffer, lpPktBase, 0x1Au);
      CSendStream::WrapCrypt(v9, 0x1Au, 0x75u, 0, 0);
    }
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseBillingTimeoutNotify",
      aDWorkRylSource_86,
      1230,
      aUidDCid0x08xUi_0,
      m_Session,
      m_CodePage,
      v3,
      Character->m_dwUID);
    return 1;
  }
}


