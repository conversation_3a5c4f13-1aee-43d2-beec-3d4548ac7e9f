#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <detours.h>
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <filesystem>
#include <map>
#include <vector>
#include <chrono>
#include <atomic>
#include <cstdarg>
#include <mutex>

// Link required libraries
#pragma comment(lib, "detours.lib")
#pragma comment(lib, "advapi32.lib")

// Character structure offsets
#define OFFSET_CID       0x20    // Character ID
#define OFFSET_RACE      0x4C7   // Race (0 = Human, 1 = Akkhan)
#define OFFSET_CLASS     0x4C8   // Character class
#define OFFSET_FAME      0x4CA   // Fame value
#define OFFSET_MEDAL     0x4CE   // Medal value - Verified at offset 0x4CE
#define OFFSET_GUILD     0x4D2   // Guild ID
#define OFFSET_PARTY     0x4D6   // Party ID
#define OFFSET_GOLD      0x4DB   // Gold
#define OFFSET_EXP       0x4EF   // Experience

// Function prototypes
DWORD WINAPI MainThread(LPVOID);
DWORD WINAPI ConfigMonitorThread(LPVOID);

// Forward declarations
std::string GetConfigDirectory();
std::string GetConfigFilePath();
size_t ComputeConfigHash();

// Configuration file path
const char* CONFIG_FILE = "cfg_FameConfig.ini";

// Plugin is always enabled (removed enable/disable functionality)

// Fame and medal configuration values
int g_KillFame = 50;     // Fame gained per kill
int g_DieFame = 10;      // Fame lost on death
int g_MedalPerKill = 10; // Medal value per kill

// Fame cap configuration values
int g_WeekFame = 0;      // Base fame per week (0 = no cap)
int g_WeekNum = 1;       // Number of weeks multiplier
DWORD g_FameCap = 0;     // Calculated fame cap (WeekFame * WeekNum)

// Kill cooldown configuration removed

// Add mutex for thread-safe config access
std::mutex g_ConfigMutex;

// Configuration file monitoring
std::atomic<bool> g_ConfigReloadNeeded(false);

// Track recent calls to detect multiple calls for same character
struct RecentCall {
    DWORD characterId;
    DWORD timestamp;
    int fameChange;
    int medalChange;
    DWORD expectedMedal;  // Expected medal value after our processing
};
static RecentCall g_RecentCalls[10];
static int g_RecentCallIndex = 0;

// Medal monitoring thread
DWORD WINAPI MedalMonitorThread(LPVOID);
volatile bool g_MedalMonitorActive = false;

// Last modification time of config file
std::atomic<__int64> g_LastConfigModTime = 0;

// Configuration reload interval in milliseconds
const int CONFIG_RELOAD_INTERVAL = 1000; // Check every 1 second for changes

// Add config hash tracking for reliable change detection
std::atomic<size_t> g_LastConfigHash = 0;

// Flag to signal thread shutdown
volatile bool g_Shutdown = false;

// Flag to force configuration reload
volatile bool g_ForceReload = false;

// Function pointer types for hooking
// CCharacter::SetFame function pointer (takes absolute fame value)
using tAddFame = void(__thiscall*)(void* pThis, DWORD newFameValue);
tAddFame oAddFame = nullptr;

// Medal system control - we'll patch the ADD instruction to prevent extra medals
BYTE* g_MedalAddPatchAddress = (BYTE*)0x0045CC31;
BYTE* g_SoloMedalAddPatchAddress = (BYTE*)0x0045C9CB; // Solo medal gain address
BYTE g_OriginalMedalAddBytes[6]; // Store original bytes
BYTE g_OriginalSoloMedalAddBytes[2]; // Store original bytes for solo medal (ADD EDX,EBP is 2 bytes)
bool g_MedalAddPatched = false;
bool g_SoloMedalAddPatched = false;

// Note: The SetFame function at 0x0041A560 is called from ProcessAdminCmd case 0x12
// We hook this function directly to detect all fame changes

// Debug logging levels
enum LogLevel {
    LOG_CRITICAL = 0,  // Critical errors
    LOG_EVENT = 1,     // Fame/medal events
    LOG_INFO = 2,      // General information
    LOG_DEBUG = 3      // Detailed debug info
};

// Current log level - only messages at this level or lower will be logged
LogLevel g_LogLevel = (LogLevel)-1;  // Set to -1 to disable all logging

// Debug logging function
void DebugLog(LogLevel level, const char* format, ...) {
    // Disabled - do nothing
    return;
}

// Convenience functions for different log levels
void LogCritical(const char* format, ...) {
    // Disabled - do nothing
    return;
}

void LogEvent(const char* format, ...) {
    // Disabled - do nothing
    return;
}

void LogInfo(const char* format, ...) {
    // Disabled - do nothing
    return;
}

void LogDebug(const char* format, ...) {
    // Disabled - do nothing
    return;
}

// Fame gain logging function specifically for kill events
void LogFameGain(const char* format, ...) {
    // Disabled - do nothing
    return;
}

// Get the last modification time of the config file
__int64 GetConfigFileModTime() {
    try {
        std::filesystem::path configPath(CONFIG_FILE);
        if (!std::filesystem::exists(configPath)) {
            return 0;
        }

        auto lastWriteTime = std::filesystem::last_write_time(configPath);
        auto duration = lastWriteTime.time_since_epoch();
        auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();

        return nanoseconds;
    }
    catch (...) {
        return 0;
    }
}

// Function to read an integer value directly from the INI file
int ReadIntFromIni(const char* section, const char* key, int defaultValue) {
    // Get full path to config file
    std::string configFullPath = GetConfigFilePath();

    // Directly read the file instead of using Windows INI functions
    std::ifstream iniFile(configFullPath);
    if (!iniFile.is_open()) {
        LogCritical("ReadIntFromIni: Failed to open config file: %s", configFullPath.c_str());
        return defaultValue;
    }

    // Variables to track our position in the file
    bool inTargetSection = false;
    std::string line;
    std::string targetKey = key;
    std::string currentSection;
    int lineNumber = 0;

    LogDebug("ReadIntFromIni: Looking for [%s] %s = ? in file: %s", section, key, configFullPath.c_str());

    // Read the file line by line
    while (std::getline(iniFile, line)) {
        lineNumber++;
        std::string originalLine = line; // Keep original for debugging

        // Trim whitespace from the beginning and end of the line
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);

        // Skip empty lines and comments
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            LogDebug("ReadIntFromIni: Line %d: Skipping empty/comment line: '%s'", lineNumber, originalLine.c_str());
            continue;
        }

        // Check if this is a section header
        if (line[0] == '[' && line[line.length() - 1] == ']') {
            currentSection = line.substr(1, line.length() - 2);
            inTargetSection = (currentSection == section);
            LogDebug("ReadIntFromIni: Line %d: Found section [%s], inTargetSection=%d", lineNumber, currentSection.c_str(), inTargetSection);
            continue;
        }

        // If we're in the target section, look for our key
        if (inTargetSection) {
            size_t equalPos = line.find('=');
            if (equalPos != std::string::npos) {
                std::string lineKey = line.substr(0, equalPos);

                // Trim whitespace from the key
                lineKey.erase(0, lineKey.find_first_not_of(" \t"));
                lineKey.erase(lineKey.find_last_not_of(" \t") + 1);

                // If this is our key, extract the value
                if (lineKey == targetKey) {
                    std::string valueStr = line.substr(equalPos + 1);

                    // Trim whitespace from the value
                    valueStr.erase(0, valueStr.find_first_not_of(" \t"));
                    valueStr.erase(valueStr.find_last_not_of(" \t") + 1);

                    LogDebug("ReadIntFromIni: Line %d: Found key '%s' = '%s'",
                        lineNumber, lineKey.c_str(), valueStr.c_str());

                    try {
                        int result = std::stoi(valueStr);
                        LogDebug("ReadIntFromIni: Successfully parsed value %d for %s", result, key);
                        return result;
                    }
                    catch (...) {
                        LogCritical("ReadIntFromIni: Failed to parse value '%s' for key '%s', using default %d",
                            valueStr.c_str(), key, defaultValue);
                        return defaultValue;
                    }
                }
                else {
                    LogDebug("ReadIntFromIni: Line %d: Found key '%s' (not our target '%s')", lineNumber, lineKey.c_str(), targetKey.c_str());
                }
            }
            else {
                LogDebug("ReadIntFromIni: Line %d: No '=' found in line: '%s'", lineNumber, originalLine.c_str());
            }
        }
        else {
            LogDebug("ReadIntFromIni: Line %d: Not in target section, line: '%s'", lineNumber, originalLine.c_str());
        }
    }

    // Key not found
    LogCritical("ReadIntFromIni: Key '%s' not found in section [%s], using default %d", key, section, defaultValue);
    return defaultValue;
}

// Hook function for SetFame (absolute fame value, not relative)
void __fastcall Hook_AddFame(void* pThis, void*, DWORD newFameValue) {
    // If character pointer is invalid, return
    if (!pThis) {
        return;
    }

    // Get character information using the defined offsets
    DWORD characterId = *(DWORD*)((BYTE*)pThis + OFFSET_CID);
    BYTE race = *(BYTE*)((BYTE*)pThis + OFFSET_RACE);
    BYTE charClass = *(BYTE*)((BYTE*)pThis + OFFSET_CLASS);
    DWORD currentFame = *(DWORD*)((BYTE*)pThis + OFFSET_FAME);
    DWORD currentMedal = *(DWORD*)((BYTE*)pThis + OFFSET_MEDAL);
    WORD guildId = *(WORD*)((BYTE*)pThis + OFFSET_GUILD);
    WORD partyId = *(WORD*)((BYTE*)pThis + OFFSET_PARTY);
    DWORD gold = *(DWORD*)((BYTE*)pThis + OFFSET_GOLD);
    int exp = *(int*)((BYTE*)pThis + OFFSET_EXP);

    // Get config values safely with mutex
    int killFame, dieFame, medalPerKill;
    DWORD fameCap;
    {
        std::lock_guard<std::mutex> lock(g_ConfigMutex);
        killFame = g_KillFame;
        dieFame = g_DieFame;
        medalPerKill = g_MedalPerKill;
        fameCap = g_FameCap;
    }

    // Debug the initial values and function entry
    LogDebug("=== SetFame Hook Called ===");
    LogDebug("Initial values: CID=%u, Fame=%u, Medal=%u, NewFameValue=%u", characterId, currentFame, currentMedal, newFameValue);
    LogDebug("Config values: KILL=%d, DIE=%d, MEDAL=%d", killFame, dieFame, medalPerKill);

    // Get the return address to determine where this function was called from
    DWORD returnAddress = 0;
    __asm {
        mov eax, [ebp + 4]  // Get return address from stack
        mov returnAddress, eax
    }

    // Check if this is an admin command (declare early for use in duplicate check)
    bool isAdminCommand = (returnAddress >= 0x00487E97 && returnAddress <= 0x00488500);

    // Check for recent calls from the same character
    DWORD currentTime = GetTickCount();
    bool isRecentDuplicate = false;
    for (int i = 0; i < 10; i++) {
        if (g_RecentCalls[i].characterId == characterId &&
            (currentTime - g_RecentCalls[i].timestamp) < 500) { // Within 0.5 seconds
            LogDebug("WARNING: Multiple SetFame calls detected for CID=%u within 500ms!", characterId);
            LogDebug("Previous call: Fame change=%d, Medal change=%d, %dms ago",
                g_RecentCalls[i].fameChange, g_RecentCalls[i].medalChange,
                currentTime - g_RecentCalls[i].timestamp);
            isRecentDuplicate = true;
            break;
        }
    }

    // If this is a duplicate call within 500ms, just use original function to avoid double-processing
    if (isRecentDuplicate && !isAdminCommand) {
        LogDebug("Skipping duplicate call - using original function");
        oAddFame(pThis, newFameValue);
        return;
    }

    // Calculate the fame change (positive = gain, negative = loss)
    int fameChange = (int)newFameValue - (int)currentFame;

    LogDebug("Fame change calculation: Current=%u, New=%u, Change=%+d, RetAddr=0x%08X",
        currentFame, newFameValue, fameChange, returnAddress);

    // Determine event type based on fame change and return address
    bool isPvpKill = false;
    bool isDeath = false;

    if (!isAdminCommand) {
        // Determine event type based on fame change
        if (fameChange > 0) {
            // Fame increased - this is a kill
            isPvpKill = true;
            LogDebug("PVP kill detected: Fame increased by %d (RetAddr=0x%08X)", fameChange, returnAddress);
        }
        else if (fameChange < 0) {
            // Fame decreased - this is a death
            isDeath = true;
            LogDebug("Death detected: Fame decreased by %d (RetAddr=0x%08X)", -fameChange, returnAddress);
        }
        else {
            // No fame change - might be a medal-only change or other event
            LogDebug("No fame change detected: RetAddr=0x%08X", returnAddress);
        }
    }

    // Handle all cases with our custom logic to prevent original medal system
    if (isAdminCommand) {
        // Admin command: Set fame directly, don't change medals
        DWORD finalFameValue = newFameValue;

        // Check fame cap for admin commands too
        if (fameCap > 0 && finalFameValue > fameCap) {
            finalFameValue = fameCap;
            LogInfo("Admin command: Fame capped at %u (requested %u)", fameCap, newFameValue);
        }

        *(DWORD*)((BYTE*)pThis + OFFSET_FAME) = finalFameValue;
        LogInfo("Admin command: Set fame to %u, medals unchanged", finalFameValue);
    }
    else if (isPvpKill || isDeath) {
        LogDebug("Custom logic: isPvpKill=%d, isDeath=%d, fameChange=%d", isPvpKill, isDeath, fameChange);

        if (isPvpKill) {
            // PVP Kill: Add configured KILL fame and MEDAL medals
            DWORD newFame = currentFame + killFame;
            DWORD newMedal = currentMedal + medalPerKill;

            // Check fame cap if enabled
            if (fameCap > 0 && newFame > fameCap) {
                newFame = fameCap;
                LogEvent("PVP kill: Fame capped at %u (would have been %u)", fameCap, currentFame + killFame);
            }

            // Set the new values directly
            *(DWORD*)((BYTE*)pThis + OFFSET_FAME) = newFame;
            *(DWORD*)((BYTE*)pThis + OFFSET_MEDAL) = newMedal;

            if (fameCap > 0 && currentFame >= fameCap) {
                LogEvent("PVP kill: Already at fame cap %u, medals only: %u + %d = %u",
                    fameCap, currentMedal, medalPerKill, newMedal);
            }
            else {
                LogEvent("PVP kill: Fame %u + %d = %u, Medal %u + %d = %u",
                    currentFame, killFame, newFame, currentMedal, medalPerKill, newMedal);
            }
        }
        else if (isDeath) {
            // Death: Subtract configured DIE fame, keep medals unchanged
            DWORD newFame;
            if (currentFame >= dieFame) {
                newFame = currentFame - dieFame;
            }
            else {
                newFame = 0; // Prevent negative fame
            }

            // Set only the fame value, leave medals unchanged
            *(DWORD*)((BYTE*)pThis + OFFSET_FAME) = newFame;
            // Explicitly preserve medals (don't change them)
            *(DWORD*)((BYTE*)pThis + OFFSET_MEDAL) = currentMedal;

            LogEvent("Death: Fame %u - %d = %u, Medal %u (unchanged)",
                currentFame, dieFame, newFame, currentMedal);
        }

        // Don't call original function - we've handled everything ourselves
        LogDebug("Custom logic completed - NOT calling original function");
        // Return early to completely bypass the original function
    }
    else {
        // For unknown cases, just set the fame directly without calling original function
        *(DWORD*)((BYTE*)pThis + OFFSET_FAME) = newFameValue;
        LogInfo("Unknown case: Set fame to %u, medals unchanged (isPvpKill=%d, isDeath=%d)",
            newFameValue, isPvpKill, isDeath);
    }

    // Read the final values after processing
    DWORD finalFame = *(DWORD*)((BYTE*)pThis + OFFSET_FAME);
    DWORD finalMedal = *(DWORD*)((BYTE*)pThis + OFFSET_MEDAL);

    // Calculate changes (recalculate fameChange with final values)
    fameChange = (int)finalFame - (int)currentFame;
    int medalChange = (int)finalMedal - (int)currentMedal;

    // Additional debugging for unexpected changes
    if (isPvpKill && (fameChange != killFame || medalChange != medalPerKill)) {
        LogCritical("WARNING: PVP kill changes don't match config! Expected Fame+%d/Medal+%d, Got Fame%+d/Medal%+d",
            killFame, medalPerKill, fameChange, medalChange);
    }
    if (isDeath && (fameChange != -dieFame || medalChange != 0)) {
        LogCritical("WARNING: Death changes don't match config! Expected Fame-%d/Medal+0, Got Fame%+d/Medal%+d",
            dieFame, fameChange, medalChange);
    }

    // Check if medals changed unexpectedly (outside our control)
    if (medalChange != 0 && !isPvpKill && !isDeath) {
        LogCritical("WARNING: Unexpected medal change detected! Medal changed by %d without kill/death event", medalChange);
    }

    // Debug the final values
    LogDebug("Final values: CID=%u, Fame=%u->%u(%+d), Medal=%u->%u(%+d)",
        characterId, currentFame, finalFame, fameChange,
        currentMedal, finalMedal, medalChange);

    // Determine event type for logging
    const char* sourceInfo = "UNKNOWN";
    if (isAdminCommand) {
        // Check specifically for SetMileage command
        if (returnAddress == 0x00487EE1 || (returnAddress >= 0x00487EE0 && returnAddress <= 0x00487EF0)) {
            sourceInfo = "SETMILEAGE";
        }
        else {
            sourceInfo = "SETFAME";
        }
    }
    else if (isPvpKill) {
        sourceInfo = "PVP";
    }
    else if (isDeath) {
        sourceInfo = "DEATH";
    }
    else if (medalChange != 0) {
        sourceInfo = "MEDAL";
    }

    // Debug log for detection analysis
    LogDebug("Detection: newFameValue=%u, fameChange=%d, finalFame=%u, currentFame=%u, medalChange=%d, RetAddr=0x%08X, Source=%s",
        newFameValue, fameChange, finalFame, currentFame, medalChange, returnAddress, sourceInfo);

    // Log the fame event with actual final values and change calculation
    LogFameGain("CID=%u, Race=%s, Class=%d, Fame=%u->%u(%+d), Medal=%u->%u(%+d), Guild=%u, Party=%u, Gold=%u, Exp=%d",
        characterId, (race == 0 ? "Human" : "Akkhan"), charClass,
        currentFame, finalFame, fameChange,
        currentMedal, finalMedal, medalChange,
        guildId, partyId, gold, exp);

    // Record this call for duplicate detection and medal monitoring
    g_RecentCalls[g_RecentCallIndex].characterId = characterId;
    g_RecentCalls[g_RecentCallIndex].timestamp = currentTime;
    g_RecentCalls[g_RecentCallIndex].fameChange = fameChange;
    g_RecentCalls[g_RecentCallIndex].medalChange = medalChange;
    g_RecentCalls[g_RecentCallIndex].expectedMedal = finalMedal;
    g_RecentCallIndex = (g_RecentCallIndex + 1) % 10;

    // Start medal monitoring for PVP kills to prevent additional medal gains
    if (isPvpKill) {
        g_MedalMonitorActive = true;
        LogDebug("Medal monitoring activated for CID=%u, expected medal=%u", characterId, finalMedal);
    }
}

// Function to get module directory (where DLL/EXE lives)
std::string GetConfigDirectory() {
    char path[MAX_PATH];
    GetModuleFileNameA(nullptr, path, MAX_PATH);
    std::filesystem::path p(path);
    return p.parent_path().string();
}

// Function to get full config file path (uses module dir, never the cwd)
std::string GetConfigFilePath() {
    return GetConfigDirectory() + "\\" + CONFIG_FILE;
}

// Function to compute hash of config file content
size_t ComputeConfigHash() {
    std::string configPath = GetConfigFilePath();
    std::ifstream file(configPath, std::ios::binary);
    if (!file.is_open()) {
        return 0;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    // Remove whitespace and normalize line endings for more consistent hashing
    std::string normalizedContent;
    for (char c : content) {
        if (c != '\r' && c != '\t') { // Skip carriage returns and tabs
            normalizedContent += c;
        }
    }

    // Simple hash of normalized file content
    std::hash<std::string> hasher;
    return hasher(normalizedContent);
}



// Function to load configuration from INI file
bool LoadConfiguration(bool logChanges = true) {
    // Store old values for change detection
    int oldKillFame = g_KillFame;
    int oldDieFame = g_DieFame;
    int oldMedalPerKill = g_MedalPerKill;
    int oldWeekFame = g_WeekFame;
    int oldWeekNum = g_WeekNum;
    DWORD oldFameCap = g_FameCap;

    // Use std::lock_guard for RAII-style mutex locking
    std::lock_guard<std::mutex> lock(g_ConfigMutex);

    // Get full path to config file
    std::string configFullPath = GetConfigFilePath();

    // Check if the config file exists
    if (!std::filesystem::exists(configFullPath)) {
        LogCritical("Config file not found at: %s", configFullPath.c_str());
        return false;
    }

    // Don't log config file contents on every reload

    // Load fame and medal configuration values
    int newKillFame = ReadIntFromIni("FAME_CONFIG", "KILL", 50);
    int newDieFame = ReadIntFromIni("FAME_CONFIG", "DIE", 10);
    int newMedalPerKill = ReadIntFromIni("FAME_CONFIG", "MEDAL", 30);

    // Load fame cap configuration values
    int newWeekFame = ReadIntFromIni("FAME_CAP", "WeekFame", 0);
    int newWeekNum = ReadIntFromIni("FAME_CAP", "WeekNum", 1);

    // Apply the new values
    g_KillFame = newKillFame;
    g_DieFame = newDieFame;
    g_MedalPerKill = newMedalPerKill;
    g_WeekFame = newWeekFame;
    g_WeekNum = newWeekNum;

    // Calculate fame cap (0 means no cap)
    if (g_WeekFame > 0 && g_WeekNum > 0) {
        g_FameCap = g_WeekFame * g_WeekNum;
    }
    else {
        g_FameCap = 0; // No cap
    }

    // Kill cooldown patching removed

    bool hasChanges = (g_KillFame != oldKillFame ||
        g_DieFame != oldDieFame ||
        g_MedalPerKill != oldMedalPerKill ||
        g_WeekFame != oldWeekFame ||
        g_WeekNum != oldWeekNum ||
        g_FameCap != oldFameCap);

    // Debug log only if values changed
    if (hasChanges) {
        LogInfo("LoadConfiguration: KILL=%d->%d, DIE=%d->%d, MEDAL=%d->%d, WeekFame=%d->%d, WeekNum=%d->%d, FameCap=%u->%u",
            oldKillFame, g_KillFame, oldDieFame, g_DieFame, oldMedalPerKill, g_MedalPerKill,
            oldWeekFame, g_WeekFame, oldWeekNum, g_WeekNum, oldFameCap, g_FameCap);
    }
    else {
        LogDebug("LoadConfiguration: No changes detected");
    }

    // Only log if configuration actually changed
    if (hasChanges && logChanges) {
        // Show in-game notification only
        if (g_FameCap > 0) {
            LogEvent("★ Config Updated: KILL=%d, DIE=%d, MEDAL=%d, FameCap=%u (Week=%d×%d) ★",
                g_KillFame, g_DieFame, g_MedalPerKill, g_FameCap, g_WeekFame, g_WeekNum);
        }
        else {
            LogEvent("★ Config Updated: KILL=%d, DIE=%d, MEDAL=%d, FameCap=DISABLED ★",
                g_KillFame, g_DieFame, g_MedalPerKill);
        }
    }
    else if (!hasChanges && logChanges) {
        // File was reloaded but no changes detected - don't log anything
        LogDebug("Config reloaded but no changes detected");
    }

    return hasChanges;
}

// Simple approach: Add a delayed medal correction
DWORD WINAPI MedalMonitorThread(LPVOID) {
    LogInfo("Medal monitor thread started");

    while (!g_Shutdown) {
        Sleep(1000); // Check every second

        if (g_MedalMonitorActive) {
            DWORD currentTime = GetTickCount();

            // Check for recent kills that need medal correction
            for (int i = 0; i < 10; i++) {
                if (g_RecentCalls[i].characterId != 0 &&
                    g_RecentCalls[i].expectedMedal != 0 &&
                    (currentTime - g_RecentCalls[i].timestamp) > 500 && // After 500ms
                    (currentTime - g_RecentCalls[i].timestamp) < 5000) { // Within 5 seconds

                    LogDebug("Medal correction needed for CID=%u, expected=%u",
                        g_RecentCalls[i].characterId, g_RecentCalls[i].expectedMedal);

                    // Clear this entry after processing
                    g_RecentCalls[i].characterId = 0;
                    g_RecentCalls[i].expectedMedal = 0;
                }
            }
        }
    }

    LogInfo("Medal monitor thread stopped");
    return 0;
}

// Configuration monitor thread - Simple timer-based approach
DWORD WINAPI ConfigMonitorThread(LPVOID) {
    // Initial delay to let system stabilize
    Sleep(1000);

    // Get initial config hash
    g_LastConfigHash = ComputeConfigHash();

    // Force a first load
    LoadConfiguration(false);

    // Simple timer-based monitoring - check every second
    while (!g_Shutdown) {
        Sleep(CONFIG_RELOAD_INTERVAL);

        // Check if config file content has changed
        size_t currentHash = ComputeConfigHash();
        size_t lastHash = g_LastConfigHash.load();

        if (currentHash != lastHash && currentHash != 0) {
            // Update the hash first to prevent double-reload
            g_LastConfigHash = currentHash;

            // Reload configuration - don't log changes
            LoadConfiguration(false);
        }

        // Also check for force reload flag
        if (g_ForceReload) {
            g_LastConfigHash = ComputeConfigHash();
            LoadConfiguration(false);
            g_ForceReload = false;
        }
    }

    return 0;
}

// Function to force reload configuration
void ForceReloadConfiguration() {
    g_ForceReload = true;
}

// Function to patch the medal ADD instruction to prevent extra medals
bool PatchMedalAddInstruction() {
    bool success = true;
    
    // Patch party medal gain
    if (!g_MedalAddPatched) {
        // Change memory protection to allow writing
        DWORD oldProtect;
        if (!VirtualProtect(g_MedalAddPatchAddress, 6, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            LogCritical("Failed to change memory protection for medal patch");
            success = false;
        }
        else {
            // Save original bytes
            memcpy(g_OriginalMedalAddBytes, g_MedalAddPatchAddress, 6);

            // Replace with NOPs (0x90) to disable the ADD instruction
            // Original: 01B0 CE040000  |ADD DWORD PTR DS:[EAX+4CE],ESI  (6 bytes)
            // Replace with: 90 90 90 90 90 90  |NOP NOP NOP NOP NOP NOP
            memset(g_MedalAddPatchAddress, 0x90, 6);

            // Restore original memory protection
            VirtualProtect(g_MedalAddPatchAddress, 6, oldProtect, &oldProtect);

            g_MedalAddPatched = true;
            LogInfo("Party medal ADD instruction patched successfully at 0x%08X", (DWORD)g_MedalAddPatchAddress);
        }
    }
    
    // Patch solo medal gain
    if (!g_SoloMedalAddPatched) {
        // Change memory protection to allow writing
        DWORD oldProtect;
        if (!VirtualProtect(g_SoloMedalAddPatchAddress, 2, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            LogCritical("Failed to change memory protection for solo medal patch");
            success = false;
        }
        else {
            // Save original bytes
            memcpy(g_OriginalSoloMedalAddBytes, g_SoloMedalAddPatchAddress, 2);

            // Replace with NOPs (0x90) to disable the ADD instruction
            // Original: 03D5  |ADD EDX,EBP  (2 bytes)
            // Replace with: 90 90  |NOP NOP
            memset(g_SoloMedalAddPatchAddress, 0x90, 2);

            // Restore original memory protection
            VirtualProtect(g_SoloMedalAddPatchAddress, 2, oldProtect, &oldProtect);

            g_SoloMedalAddPatched = true;
            LogInfo("Solo medal ADD instruction patched successfully at 0x%08X", (DWORD)g_SoloMedalAddPatchAddress);
        }
    }
    
    return success;
}

// Function to restore the original medal ADD instruction
bool RestoreMedalAddInstruction() {
    bool success = true;
    
    // Restore party medal instruction
    if (g_MedalAddPatched) {
        // Change memory protection to allow writing
        DWORD oldProtect;
        if (!VirtualProtect(g_MedalAddPatchAddress, 6, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            LogCritical("Failed to change memory protection for medal restore");
            success = false;
        }
        else {
            // Restore original bytes
            memcpy(g_MedalAddPatchAddress, g_OriginalMedalAddBytes, 6);

            // Restore original memory protection
            VirtualProtect(g_MedalAddPatchAddress, 6, oldProtect, &oldProtect);

            g_MedalAddPatched = false;
            LogInfo("Party medal ADD instruction restored successfully");
        }
    }
    
    // Restore solo medal instruction
    if (g_SoloMedalAddPatched) {
        // Change memory protection to allow writing
        DWORD oldProtect;
        if (!VirtualProtect(g_SoloMedalAddPatchAddress, 2, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            LogCritical("Failed to change memory protection for solo medal restore");
            success = false;
        }
        else {
            // Restore original bytes
            memcpy(g_SoloMedalAddPatchAddress, g_OriginalSoloMedalAddBytes, 2);

            // Restore original memory protection
            VirtualProtect(g_SoloMedalAddPatchAddress, 2, oldProtect, &oldProtect);

            g_SoloMedalAddPatched = false;
            LogInfo("Solo medal ADD instruction restored successfully");
        }
    }
    
    return success;
}

// Kill cooldown functions removed

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID)
{
    // Give the main application time to initialize
    Sleep(1000);

    // Initialize licensing system
    InitializeLicensing();

    // Use shared licensing functions
    if (!RYL1Plugin::CheckLicense())
    {
        return 0;
    }

    // Check if config file exists, create it if not
    std::filesystem::path configPath(CONFIG_FILE);
    if (!std::filesystem::exists(configPath)) {
        std::ofstream configFile(CONFIG_FILE);
        if (configFile.is_open()) {
            // Create config file with default settings
            configFile << "[FAME_CONFIG]" << std::endl;
            configFile << "KILL=50" << std::endl;
            configFile << "DIE=10" << std::endl;
            configFile << "MEDAL=30" << std::endl;
            configFile << std::endl;
            configFile << "[FAME_CAP]" << std::endl;
            configFile << "WeekFame=200000" << std::endl;
            configFile << "WeekNum=1" << std::endl;

            configFile.close();
        }
    }

    // Start config monitor thread
    HANDLE hMonitorThread = CreateThread(NULL, 0, ConfigMonitorThread, NULL, 0, NULL);
    if (hMonitorThread) {
        LogInfo("Config monitor thread started successfully");
        CloseHandle(hMonitorThread);
    }
    else {
        LogCritical("Failed to start config monitor thread");
    }

    // Start medal monitor thread
    HANDLE hMedalMonitorThread = CreateThread(NULL, 0, MedalMonitorThread, NULL, 0, NULL);
    if (hMedalMonitorThread) {
        LogInfo("Medal monitor thread started successfully");
        CloseHandle(hMedalMonitorThread);
    }
    else {
        LogCritical("Failed to start medal monitor thread");
    }

    // Force initial reload
    ForceReloadConfiguration();
    LogInfo("Initial configuration loaded");



    // Install hook for fame/medal functions
    // Start a detour transaction
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());

    // Hook the CCharacter::SetFame function at address 0x0041A560
    oAddFame = (tAddFame)0x0041A560;
    LONG fameHookResult = DetourAttach(&(PVOID&)oAddFame, Hook_AddFame);
    if (fameHookResult == NO_ERROR) {
        LogInfo("SetFame hook installed successfully");
    }
    else {
        LogCritical("SetFame hook failed with error: %d", fameHookResult);
    }

    // Commit the detour transaction
    DetourTransactionCommit();

    // Patch the medal ADD instruction to prevent extra medal gains
    if (PatchMedalAddInstruction()) {
        LogInfo("Medal system patched - both party and solo medal gains disabled");
    }
    else {
        LogCritical("Failed to patch medal system - extra medals may still occur");
    }

    // Kill cooldown system removed

    // No need for background thread since we're now using gain-based system
    // Fame and medal gains will be applied when kills occur through the SetFame hook

    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
    switch (reason)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        CreateThread(0, 0, MainThread, 0, 0, 0);
        break;

    case DLL_PROCESS_DETACH:
        // Signal shutdown for the config monitor thread
        g_Shutdown = true;

        // No file change handle to clean up in timer-based approach

        // Restore original medal ADD instruction
        RestoreMedalAddInstruction();

        // Wait a bit for threads to finish
        Sleep(100);

        // Cleanup licensing
        CleanupLicensing();

        break;
    }

    return TRUE;
}
