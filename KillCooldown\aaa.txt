#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <Psapi.h>
#include "../Header/detours.h"
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <mutex>
#include <unordered_map>

// Forward declarations to avoid "identifier not found" errors
struct SaveEnemyInfo;

#pragma comment(lib, "Psapi.lib")

// Global variables
DWORD g_SaveEnemyCallAddress = 0;
DWORD g_LastCallerAddress = 0;
std::string g_CooldownFilePath;
std::string g_ConfigFilePath;

// Configuration values
bool g_Enabled = true;
DWORD g_CooldownTime = 300000; // 5 minutes in milliseconds
DWORD g_SaveInterval = 30000; // 30 seconds in milliseconds
bool g_IgnoreLatestEnemy = true; // Default to true to avoid crashes with m_LatestEnemy pointer
bool g_ProcessingCall = false; // Flag to prevent recursive/concurrent calls
bool g_StrictConcurrencyProtection = true; // Enable strict concurrency protection by default

// Mutex for thread-safe data access
std::mutex g_CooldownMutex;
std::mutex g_CallMutex; // New mutex for call protection

// Global variables for thread synchronization
bool g_SaveRequested = false;

// Persistent cooldown storage - maps character ID to last kill timestamp
std::unordered_map<DWORD, DWORD> g_PersistentCooldowns;

// SaveEnemyInfo structure
struct SaveEnemyInfo {
    DWORD m_dwCID;       // Character ID
    DWORD m_dwTickCount; // Timestamp
};

// Function pointer type for SaveEnemy
typedef char(__thiscall* tSaveEnemy)(void* pThis, unsigned int dwCID);
tSaveEnemy oSaveEnemy = nullptr;

// Function to read an integer value from an INI file
int ReadIniInt(const std::string& iniPath, const std::string& section, const std::string& key, int defaultValue) {
    return GetPrivateProfileIntA(section.c_str(), key.c_str(), defaultValue, iniPath.c_str());
}

// Function to load configuration from INI file
void LoadConfiguration() {
    try {
        // Get current directory for config file
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_ConfigFilePath = std::string(currentDir) + "\\cfg_killcooldown.ini";
        
        // Check if the config file exists
        if (GetFileAttributesA(g_ConfigFilePath.c_str()) == INVALID_FILE_ATTRIBUTES) {
            return;
        }
        
        // Read configuration values
        g_Enabled = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "Enabled", 1) != 0);
        g_CooldownTime = ReadIniInt(g_ConfigFilePath, "KillCooldown", "CooldownTime", 300000);
        g_SaveInterval = ReadIniInt(g_ConfigFilePath, "KillCooldown", "SaveInterval", 30000);
        g_IgnoreLatestEnemy = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "IgnoreLatestEnemy", 1) != 0);
        g_StrictConcurrencyProtection = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "StrictConcurrencyProtection", 1) != 0);
    }
    catch (...) {
        // Silently fail if configuration loading fails
    }
}

// Function to save cooldowns to file
void SaveCooldownsToFile() {
    try {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);
        
        // Check if we have any cooldowns to save
        if (g_PersistentCooldowns.empty()) {
            return;
        }
        
        std::ofstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open()) {
            return;
        }
        
        // Count active cooldowns
        DWORD currentTime = GetTickCount();
        size_t activeCount = 0;
        for (const auto& entry : g_PersistentCooldowns) {
            DWORD age = currentTime - entry.second;
            if (age < g_CooldownTime) {
                activeCount++;
            }
        }
        
        // Write the number of entries
        size_t count = g_PersistentCooldowns.size();
        file.write(reinterpret_cast<const char*>(&count), sizeof(count));
        
        // Write each entry
        for (const auto& entry : g_PersistentCooldowns) {
            file.write(reinterpret_cast<const char*>(&entry.first), sizeof(entry.first));
            file.write(reinterpret_cast<const char*>(&entry.second), sizeof(entry.second));
        }
        
        file.close();
    }
    catch (...) {
        // Silently fail if saving fails
    }
}

// Function to load cooldowns from file
void LoadCooldownsFromFile() {
    try {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);
        
        std::ifstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open()) {
            return;
        }
        
        // Read the number of entries
        size_t count = 0;
        file.read(reinterpret_cast<char*>(&count), sizeof(count));
        
        if (count == 0) {
            file.close();
            return;
        }
        
        // Read each entry
        g_PersistentCooldowns.clear();
        size_t validCount = 0;
        DWORD currentTime = GetTickCount();
        
        for (size_t i = 0; i < count; i++) {
            DWORD characterId = 0;
            DWORD timestamp = 0;
            
            file.read(reinterpret_cast<char*>(&characterId), sizeof(characterId));
            file.read(reinterpret_cast<char*>(&timestamp), sizeof(timestamp));
            
            // Check if the cooldown is still valid
            DWORD age = currentTime - timestamp;
            if (age < g_CooldownTime) {
                g_PersistentCooldowns[characterId] = timestamp;
                validCount++;
            }
        }
        
        file.close();
    }
    catch (...) {
        // Silently fail if loading fails
    }
}

// Thread function to periodically save cooldowns
DWORD WINAPI CooldownSaveThread(LPVOID) {
    while (true) {
        // Save cooldowns at the configured interval
        Sleep(g_SaveInterval);
        
        try {
            // Check if an immediate save was requested
            bool saveNeeded = false;
            {
                // Use a critical section to safely check and reset the flag
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                if (g_SaveRequested) {
                    saveNeeded = true;
                    g_SaveRequested = false;
                }
            }
            
            // Always save periodically or when requested
            SaveCooldownsToFile();
        }
        catch (...) {
            // Silently continue if saving fails
        }
    }
    return 0;
}

// Helper function to safely access memory
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL SafeReadMemory(LPVOID lpAddress, LPVOID lpBuffer, SIZE_T nSize) {
    if (!lpAddress || !lpBuffer || nSize == 0) {
        return FALSE;
    }
    
    BOOL result = FALSE;
    
    __try {
        memcpy(lpBuffer, lpAddress, nSize);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        result = FALSE;
    }
    
    return result;
}

// Helper function to safely check if a pointer is valid
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL IsPtrValid(LPVOID ptr, SIZE_T size) {
    if (!ptr) return FALSE;
    
    BOOL result = FALSE;
    
    __try {
        volatile BYTE temp = *((BYTE*)ptr);
        volatile BYTE temp2 = *(((BYTE*)ptr) + size - 1);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        result = FALSE;
    }
    
    return result;
}

// Helper function to safely read the m_LatestEnemy pointer
// This function is kept simple with no C++ objects to avoid unwinding issues
SaveEnemyInfo* ReadLatestEnemyPtr(void* pThis) {
    if (!pThis) return nullptr;
    
    SaveEnemyInfo* latestEnemy = nullptr;
    
    __try {
        // Try different potential offsets for m_LatestEnemy
        // Common offsets in different game versions
        DWORD possibleOffsets[] = { 0x8, 0xC, 0x10, 0x14, 0x18, 0x1C, 0x20 };
        
        for (int i = 0; i < sizeof(possibleOffsets) / sizeof(DWORD); i++) {
            PBYTE pOffset = (PBYTE)pThis + possibleOffsets[i];
            
            // Read the pointer value
            SaveEnemyInfo* testPtr = nullptr;
            if (!SafeReadMemory(pOffset, &testPtr, sizeof(testPtr)) || !testPtr) {
                continue;
            }
            
            // Simple validation - just check if we can read the first byte
            if (IsPtrValid(testPtr, sizeof(SaveEnemyInfo) * 5)) {
                latestEnemy = testPtr;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return nullptr;
    }
    
    return latestEnemy;
}

// Helper function to safely read m_LatestEnemy entries
// This function is kept simple with no C++ objects to avoid unwinding issues
void ReadLatestEnemyEntries(SaveEnemyInfo* latestEnemy) {
    if (!latestEnemy) return;
    
    __try {
        // Just validate the entries but don't log them
        for (int i = 0; i < 5; i++) {
            IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo));
        }
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        // Silent exception handling
    }
}

// Helper function to safely access the m_LatestEnemy field
SaveEnemyInfo* GetLatestEnemyArray(void* pThis) {
    if (!pThis) {
        return nullptr;
    }
    
    // Call the exception-safe helper function
    SaveEnemyInfo* latestEnemy = nullptr;
    
    try {
        latestEnemy = ReadLatestEnemyPtr(pThis);
        
        if (latestEnemy) {
            // Call the helper function to read entries
            ReadLatestEnemyEntries(latestEnemy);
        }
    }
    catch (...) {
        return nullptr;
    }
    
    return latestEnemy;
}

// Helper function to safely check if CID exists in array
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL CheckCidInArray(SaveEnemyInfo* latestEnemy, DWORD dwCID, int* foundIndex) {
    if (!latestEnemy || !foundIndex) return FALSE;
    
    BOOL result = FALSE;
    *foundIndex = -1;
    
    __try {
        for (int i = 0; i < 5; i++) {
            // Simple validation before access
            if (!IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo))) {
                break;
            }
            
            if (latestEnemy[i].m_dwCID == dwCID) {
                result = TRUE;
                *foundIndex = i;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        result = FALSE;
    }
    
    return result;
}

// Helper function to safely call the original SaveEnemy function
// This function is kept simple with no C++ objects to avoid unwinding issues
char CallOriginalSaveEnemy(void* pThis, DWORD dwCID) {
    if (!oSaveEnemy || !pThis) {
        return 0;
    }
    
    char result = 0;
    
    __try {
        result = oSaveEnemy(pThis, dwCID);
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        // Exception caught - silent handling
    }

    return result;
}

// Hook function for CThreat::SaveEnemy
char __fastcall Hook_SaveEnemy(void* pThis, void* edx, unsigned int dwCID) {
    // Skip if plugin is disabled
    if (!g_Enabled) {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }
    
    // Safety check for null pointer
    if (!pThis) {
        return 0;
    }
    
    // Skip processing for invalid CIDs
    if (dwCID == 0) {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }

    // Use a lock to prevent concurrent/recursive calls if strict concurrency protection is enabled
    bool lockAcquired = false;
    if (g_StrictConcurrencyProtection) {
        lockAcquired = g_CallMutex.try_lock();
        if (!lockAcquired) {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end()) {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }
            
            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        // Set processing flag to detect recursive calls
        if (g_ProcessingCall) {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end()) {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }
            
            g_CallMutex.unlock();
            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        g_ProcessingCall = true;
    }
    
    // Declare variables at function start to avoid initialization issues
    DWORD startTime = 0;
    bool hasPersistentCooldown = false;
    bool cidFoundInArray = false;
    int foundIndex = -1;
    char result = 0; // Default to 0 (no cooldown) for new kills
    bool overrideResult = false;
    SaveEnemyInfo* latestEnemy = nullptr;
    bool cooldownUpdated = false;
    
    try {
        // Get current timestamp
        startTime = GetTickCount();
        
        // Check persistent cooldown
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            auto it = g_PersistentCooldowns.find(dwCID);
            if (it != g_PersistentCooldowns.end()) {
                DWORD age = startTime - it->second;
                if (age < g_CooldownTime) {
                    hasPersistentCooldown = true;
                }
            }
        }

        // Only check latestEnemy if not ignoring it
        if (!g_IgnoreLatestEnemy) {
            // Get the array of SaveEnemyInfo from the CThreat object
            latestEnemy = GetLatestEnemyArray(pThis);

            if (latestEnemy) {
                // Use the exception-safe helper function
                if (CheckCidInArray(latestEnemy, dwCID, &foundIndex)) {
                    cidFoundInArray = true;
                }
            }
        }
        
        // Determine what result we should return
        if (cidFoundInArray) {
            // CID is already in array, should return 1
            result = 1;
            overrideResult = true; // Always override when we know the state
        } else if (hasPersistentCooldown) {
            // CID has persistent cooldown but not in array (player relogged)
            // Override to 1 to prevent fame gain
            result = 1; // Return 1 to indicate cooldown
            overrideResult = true;
        } else {
            // No cooldown found, this is a new kill
            result = 0;
            overrideResult = false;
        }
    }
    catch (...) {
        // Silently handle exceptions
    }

    // Call original function - outside of try block to avoid unwinding issues
    if (!overrideResult && oSaveEnemy && pThis) {
        try {
            result = CallOriginalSaveEnemy(pThis, dwCID);
        }
        catch (...) {
            result = 0; // Default to "new kill" if original function fails
        }
    }
    
    try {
        // Update the persistent cooldown timestamp when a new kill happens (result = 0)
        if (result == 0) {
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                g_PersistentCooldowns[dwCID] = startTime;
                cooldownUpdated = true;
            }
            
            // Request a save but don't do it immediately to avoid deadlock
            // We'll set a flag to save on the next timer tick
            {
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                g_SaveRequested = true;
            }
        }
    }
    catch (...) {
        // Silently handle exceptions
    }
    
    // Reset processing flag and unlock mutex if we acquired it
    if (g_StrictConcurrencyProtection && lockAcquired) {
        g_ProcessingCall = false;
        g_CallMutex.unlock();
    }
    
    return result;
}

// Function to scan for calls to SaveEnemy in memory
BOOL FindSaveEnemyCalls() {
    BOOL success = FALSE;
    
    try {
        HMODULE hModule = GetModuleHandleA(NULL);
        if (!hModule) {
            return FALSE;
        }
        
        // Try known addresses for the SaveEnemy function
        DWORD possibleAddresses[] = {
            0x0045A930, // From PDB
            0x0045A950,
            0x0045A910,
            0x0045A900,
            0x0045A940,
            0x0045A920
        };

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++) {
            DWORD address = possibleAddresses[i];
            
            // Store the original function address
            oSaveEnemy = (tSaveEnemy)address;
            
            // Try to hook the function
            DWORD oldProtect;
            if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect)) {
                int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);
                
                if (result == DETOUR_SUCCESS) {
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                    success = TRUE;
                    break;
                }
                
                // Restore protection
                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
            }
        }
        
        // If none of the hardcoded addresses worked, try scanning for the function
        if (!success) {
            // Get module information
            MODULEINFO moduleInfo;
            if (GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo))) {
                PBYTE moduleStart = (PBYTE)moduleInfo.lpBaseOfDll;
                SIZE_T moduleSize = moduleInfo.SizeOfImage;
                
                // Simple signature for SaveEnemy function - adjust as needed
                BYTE signature[] = { 0x55, 0x8B, 0xEC, 0x83, 0xEC };
                SIZE_T sigSize = sizeof(signature);
                
                // Scan through the module memory
                for (SIZE_T i = 0; i < moduleSize - sigSize; i++) {
                    bool found = true;
                    for (SIZE_T j = 0; j < sigSize; j++) {
                        if (moduleStart[i + j] != signature[j]) {
                            found = false;
                            break;
                        }
                    }
                    
                    if (found) {
                        DWORD address = (DWORD)(moduleStart + i);
                        
                        // Store the original function address
                        oSaveEnemy = (tSaveEnemy)address;
                        
                        // Try to hook the function
                        DWORD oldProtect;
                        if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect)) {
                            int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);
                            
                            if (result == DETOUR_SUCCESS) {
                                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                                success = TRUE;
                                break;
                            }
                            
                            // Restore protection
                            VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                        }
                    }
                }
            }
        }
    }
    catch (...) {
        success = FALSE;
    }
    
    return success;
}

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID) {
    try {
        // Give the main application time to initialize
        Sleep(3000);

        // Create logs directory
        CreateDirectoryA("logs", NULL);
        
        // Initialize cooldown file path
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_CooldownFilePath = std::string(currentDir) + "\\logs\\killcooldowns.dat";
        
        // Load configuration
        LoadConfiguration();
        
        // Skip further initialization if plugin is disabled
        if (!g_Enabled) {
            return 0;
        }
        
        // Load persistent cooldowns
        LoadCooldownsFromFile();

        // Start cooldown save thread
        CreateThread(NULL, 0, CooldownSaveThread, NULL, 0, NULL);
    
        // Try to find and hook the SaveEnemy function
        BOOL hookSuccess = FindSaveEnemyCalls();
        
        // For debugging only - remove after confirming hook works
        #ifdef _DEBUG
        if (hookSuccess) {
            MessageBoxA(NULL, "KillCooldown hook installed successfully!", "KillCooldown Plugin", MB_OK | MB_ICONINFORMATION);
        } else {
            MessageBoxA(NULL, "KillCooldown hook failed to install!", "KillCooldown Plugin", MB_OK | MB_ICONERROR);
        }
        #endif
    }
    catch (...) {
        // Silently handle exceptions
    }

    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved) {
    switch (reason) {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        
        try {
            // Initialize the licensing system
            RYL1Plugin::Initialize();
            
            // Check license before proceeding
            if (!RYL1Plugin::CheckLicense()) {
                return FALSE; // This will never be reached due to crash in CheckLicense
            }
        }
        catch (...) {
            // If license check fails, continue anyway for debugging purposes
            #ifdef _DEBUG
            MessageBoxA(NULL, "License check failed but continuing for debug purposes", "KillCooldown Plugin", MB_OK | MB_ICONWARNING);
            #endif
        }
        
        CreateThread(0, 0, MainThread, 0, 0, 0);
        break;

    case DLL_PROCESS_DETACH:
        try {
            if (g_Enabled && !g_CooldownFilePath.empty()) {
                SaveCooldownsToFile();
            }
            
            // Cleanup the licensing system
            RYL1Plugin::Cleanup();
        }
        catch (...) {
            // Silently fail on shutdown
        }
        break;
    }

    return TRUE;
} 