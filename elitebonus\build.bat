@echo off
echo Building Elitebonus.dll...

:: Try to find MSBuild in common locations
set MSBUILD_PATH=
for %%v in (2022 2019 2017) do (
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\%%v\Community\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Community\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\%%v\Professional\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Professional\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\%%v\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
        goto found
    )
)

:: Try older Visual Studio versions
if exist "C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe"
    goto found
)

:found
if "%MSBUILD_PATH%"=="" (
    echo MSBuild not found. Please build the project using Visual Studio.
    exit /b 1
)

echo Using MSBuild from: %MSBUILD_PATH%
"%MSBUILD_PATH%" elitebonus.vcxproj /p:Configuration=Release /p:Platform=Win32

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!
echo Copying DLL to Release folder...

if not exist "..\Release\Elitebonus" mkdir "..\Release\Elitebonus"
copy /Y "Release\Elitebonus\elitebonus.dll" "..\Release\Elitebonus\elitebonus.dll"
copy /Y "Release\Elitebonus\elitebonus.pdb" "..\Release\Elitebonus\elitebonus.pdb"

echo Done! 