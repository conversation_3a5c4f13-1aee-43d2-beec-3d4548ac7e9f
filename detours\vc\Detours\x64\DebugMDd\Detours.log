﻿  
  Microsoft (R) Program Maintenance Utility Version 14.41.34120.0
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main"
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\src"
   Created ..\lib.X64
   Created ..\bin.X64
   Created obj.X64
  	cl /nologo /W4 /WX /we4777 /we4800 /Zi /MT /Gy /Gm- /Zl /Od /DDETOUR_DEBUG=0 /DWIN32_LEAN_AND_MEAN /D_WIN32_WINNT=0x501 /Fd..\lib.X64\detours.pdb /Foobj.X64\ /c detours.cpp modules.cpp disasm.cpp image.cpp creatwth.cpp disolx86.cpp disolx64.cpp disolia64.cpp disolarm.cpp disolarm64.cpp 
  detours.cpp
  modules.cpp
  disasm.cpp
  image.cpp
  creatwth.cpp
  disolx86.cpp
  disolx64.cpp
  disolia64.cpp
  disolarm.cpp
  disolarm64.cpp
  Generating Code...
  	link /lib /out:..\lib.X64\detours.lib /nologo obj.X64\detours.obj      obj.X64\modules.obj      obj.X64\disasm.obj       obj.X64\image.obj        obj.X64\creatwth.obj     obj.X64\disolx86.obj     obj.X64\disolx64.obj     obj.X64\disolia64.obj    obj.X64\disolarm.obj     obj.X64\disolarm64.obj
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples"
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\syelog"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c syelog.cpp 
  syelog.cpp
  	link /lib /nologo /out:..\..\lib.X64\syelog.lib obj.X64\syelog.obj
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c syelogd.cpp 
  syelogd.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\syelogd.exe /Fd..\..\bin.X64\syelogd.pdb obj.X64\syelogd.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ws2_32.lib mswsock.lib advapi32.lib
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sltest.cpp 
  sltest.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sltest.exe /Fd..\..\bin.X64\sltest.pdb obj.X64\sltest.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sltestp.cpp 
  sltestp.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sltestp.exe /Fd..\..\bin.X64\sltestp.pdb obj.X64\sltestp.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\simple"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\simple.res /i..\..\include simple.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c simple.cpp 
  simple.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\simple64.dll /Fd..\..\bin.X64\simple64.pdb  obj.X64\simple.obj obj.X64\simple.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:TimedSleepEx  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\simple64.lib and object ..\..\bin.X64\simple64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sleep5.cpp 
  sleep5.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sleep5.exe /Fd..\..\bin.X64\sleep5.pdb obj.X64\sleep5.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\simple_safe"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\simple_safe.res /i..\..\include simple_safe.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /std:c++14 /Fdobj.X64\vc.pdb /Foobj.X64\ /c simple_safe.cpp 
  simple_safe.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /std:c++14 /Fe..\..\bin.X64\simple_safe64.dll /Fd..\..\bin.X64\simple_safe64.pdb  obj.X64\simple_safe.obj obj.X64\simple_safe.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:TimedSleepEx  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\simple_safe64.lib and object ..\..\bin.X64\simple_safe64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /std:c++14 /Fdobj.X64\vc.pdb /Foobj.X64\ /c sleep5.cpp 
  sleep5.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /std:c++14 /Fe..\..\bin.X64\sleep5.exe /Fd..\..\bin.X64\sleep5.pdb obj.X64\sleep5.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\slept"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\slept.res /i..\..\include slept.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c slept.cpp 
  slept.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\slept64.dll /Fd..\..\bin.X64\slept64.pdb  obj.X64\slept.obj obj.X64\slept.res /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:TimedSleepEx  /export:UntimedSleepEx  /export:GetSleptTicks  /export:TestTicks  /export:TestTicksEx  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\slept64.lib and object ..\..\bin.X64\slept64.exp
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\dslept.res /i..\..\include dslept.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c dslept.cpp 
  dslept.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\dslept64.dll /Fd..\..\bin.X64\dslept64.pdb  obj.X64\dslept.obj obj.X64\dslept.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:TimedSleepEx  /export:UntimedSleepEx  /export:GetSleptTicks  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\dslept64.lib and object ..\..\bin.X64\dslept64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sleepold.cpp 
  sleepold.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sleepold.exe /Fd..\..\bin.X64\sleepold.pdb obj.X64\sleepold.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sleepnew.cpp 
  sleepnew.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sleepnew.exe /Fd..\..\bin.X64\sleepnew.pdb obj.X64\sleepnew.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no ..\..\bin.X64\slept64.lib
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c sleepbed.cpp 
  sleepbed.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /O2 /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\sleepbed.exe /Fd..\..\bin.X64\sleepbed.pdb obj.X64\sleepbed.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\setdll"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c setdll.cpp 
  setdll.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\setdll.exe /Fd..\..\bin.X64\setdll.pdb obj.X64\setdll.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\withdll"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c withdll.cpp 
  withdll.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\withdll.exe /Fd..\..\bin.X64\withdll.pdb obj.X64\withdll.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\cping"
   Created obj.X64
  	midl /nologo /Oif /no_format_opt /x64 /out obj.X64 /prefix all iping_ /dlldata iping_d.c iping.idl
  64 bit Processing .\iping.idl
  iping.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.idl
  objidl.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.idl
  unknwn.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.idl
  wtypes.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.idl
  wtypesbase.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h
  basetsd.h
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h
  guiddef.h
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.idl
  oaidl.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.idl
  oleidl.idl
  64 bit Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.acf
  oaidl.acf
  	cl /nologo /Zi /MT /Gm- /W3 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_WIN32_WINNT=0x0400 /Fdobj.X64\vc.pdb  /DCONST_VTABLE  /DCOBJMACROS -DWIN32 -DNT -DENTRY_PREFIX=iping_ -DREGISTER_PROXY_DLL /Iobj.X64 /Foobj.X64\ /c obj.X64\iping_i.c
  iping_i.c
  	cl /nologo /Zi /MT /Gm- /W3 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_WIN32_WINNT=0x0400 /Fdobj.X64\vc.pdb  /DCONST_VTABLE  /DCOBJMACROS -DWIN32 -DNT -DENTRY_PREFIX=iping_ -DREGISTER_PROXY_DLL /Iobj.X64 /Foobj.X64\ /c obj.X64\iping_p.c
  iping_p.c
  	cl /nologo /Zi /MT /Gm- /W3 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_WIN32_WINNT=0x0400 /Fdobj.X64\vc.pdb  /DCONST_VTABLE  /DCOBJMACROS -DWIN32 -DNT -DENTRY_PREFIX=iping_ -DREGISTER_PROXY_DLL /Iobj.X64 /Foobj.X64\ /c obj.X64\iping_d.c
  iping_d.c
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_WIN32_WINNT=0x0400 /Fdobj.X64\vc.pdb  /DCONST_VTABLE  /DCOBJMACROS -DWIN32 -DNT  /Iobj.X64 /Foobj.X64\ /c cping.cpp 
  cping.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /D_WIN32_WINNT=0x0400 /Fdobj.X64\vc.pdb  /DCONST_VTABLE  /DCOBJMACROS -DWIN32 -DNT /Fe..\..\bin.X64\cping.exe obj.X64\cping.obj     obj.X64\iping_i.obj  obj.X64\iping_p.obj  obj.X64\iping_d.obj /link /release /incremental:no /profile /nodefaultlib:oldnames.lib  /subsystem:console ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib  kernel32.lib  user32.lib  shell32.lib  uuid.lib  ole32.lib  rpcrt4.lib  advapi32.lib  wsock32.lib
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\disas"
   Created obj.X64
  	ml64 /nologo /Zi /c /Fl /Foobj.X64\disasm.obj /Flobj.X64\x64.lst x64.asm
   Assembling: x64.asm
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c disas.cpp 
  disas.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\disas.exe /FAcs /Faobj.X64\disas.lst /Fd..\..\bin.X64\disas.pdb  obj.X64\disas.obj obj.X64\disasm.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /subsystem:console /entry:WinMainCRTStartup
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\dtest"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\dtarge.res /i..\..\include dtarge.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c dtarge.cpp 
  dtarge.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include  /Fe..\..\bin.X64\dtarge64.dll  /Fd..\..\bin.X64\dtarge64.pdb  obj.X64\dtarge.obj obj.X64\dtarge.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:Target0  /export:Target1  /export:Target2  /export:Target3  /export:Target4  /export:Target5  /export:Target6  /export:Target7  /export:Target8  /export:Target9  /export:Target10  /export:Target11  /export:Target12  /export:Target13  /export:Target14  /export:Target15  /export:Target16  /export:TargetV  /export:TargetR  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\dtarge64.lib and object ..\..\bin.X64\dtarge64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c dtest.cpp 
  dtest.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\dtest.exe /Fd..\..\bin.X64\dtest.pdb obj.X64\dtest.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ..\..\bin.X64\dtarge64.lib  /subsystem:console /entry:WinMainCRTStartup
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\dumpe"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c dumpe.cpp 
  dumpe.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\dumpe.exe /Fd..\..\bin.X64\dumpe.pdb obj.X64\dumpe.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\dumpi"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c dumpi.cpp 
  dumpi.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\dumpi.exe /Fd..\..\bin.X64\dumpi.pdb obj.X64\dumpi.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\echo"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\echofx.res /i..\..\include echofx.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c main.cpp echonul.cpp 
  main.cpp
  echonul.cpp
  Generating Code...
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Zl /Fe..\..\bin.X64\echonul.exe /Fd..\..\bin.X64\echonul.pdb  obj.X64\main.obj obj.X64\echonul.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib  /export:Echo  /subsystem:console
     Creating library ..\..\bin.X64\echonul.lib and object ..\..\bin.X64\echonul.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c echofx.cpp 
  echofx.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\echofx64.dll /Fd..\..\bin.X64\echofx64.pdb  obj.X64\echofx.obj obj.X64\echofx.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:Mine_Echo  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ..\..\bin.X64\echonul.lib
     Creating library ..\..\bin.X64\echofx64.lib and object ..\..\bin.X64\echofx64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\einst"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c edll1x.cpp 
  edll1x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\edll1x64.dll /Fd..\..\bin.X64\edll1x64.pdb  obj.X64\edll1x.obj /LD  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib user32.lib  /subsystem:windows  /base:0x7100000
     Creating library ..\..\bin.X64\edll1x64.lib and object ..\..\bin.X64\edll1x64.exp
LINK : warning LNK4281: undesirable base address 0x7100000 for x64 image; set base address above 4GB for best ASLR optimization
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c edll2x.cpp 
  edll2x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\edll2x64.dll /Fd..\..\bin.X64\edll2x64.pdb  obj.X64\edll2x.obj /LD  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib user32.lib  /subsystem:console  /base:0x7200000
     Creating library ..\..\bin.X64\edll2x64.lib and object ..\..\bin.X64\edll2x64.exp
LINK : warning LNK4281: undesirable base address 0x7200000 for x64 image; set base address above 4GB for best ASLR optimization
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c edll3x.cpp 
  edll3x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\edll3x64.dll /Fd..\..\bin.X64\edll3x64.pdb  obj.X64\edll3x.obj /LD  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib user32.lib  /subsystem:console  /base:0x7300000
     Creating library ..\..\bin.X64\edll3x64.lib and object ..\..\bin.X64\edll3x64.exp
LINK : warning LNK4281: undesirable base address 0x7300000 for x64 image; set base address above 4GB for best ASLR optimization
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c einst.cpp 
  einst.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\einst.exe /Fd..\..\bin.X64\einst.pdb obj.X64\einst.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib user32.lib  ..\..\bin.X64\edll1x64.lib ..\..\bin.X64\edll2x64.lib ..\..\bin.X64\edll3x64.lib  /subsystem:console /entry:WinMainCRTStartup
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\comeasy"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\wrotei.res /i..\..\include wrotei.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c wrotei.cpp 
  wrotei.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\wrotei64.dll /Fd..\..\bin.X64\wrotei64.pdb  obj.X64\wrotei.obj obj.X64\wrotei.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ole32.lib
     Creating library ..\..\bin.X64\wrotei64.lib and object ..\..\bin.X64\wrotei64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c comeasy.cpp 
  comeasy.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\comeasy.exe /Fd..\..\bin.X64\comeasy.pdb  obj.X64\comeasy.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ole32.lib  /subsystem:console /fixed:no
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\commem"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c commem.cpp 
  commem.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\commem.exe /Fd..\..\bin.X64\commem.pdb obj.X64\commem.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ole32.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\findfunc"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\target.res /i..\..\include target.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c target.cpp 
  target.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\target64.dll /Fd..\..\bin.X64\target64.pdb  obj.X64\target.obj obj.X64\target.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:Target  /base:0x1900000  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\target64.lib and object ..\..\bin.X64\target64.exp
LINK : warning LNK4281: undesirable base address 0x1900000 for x64 image; set base address above 4GB for best ASLR optimization
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\extend.res /i..\..\include extend.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c extend.cpp 
  extend.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\extend64.dll /Fd..\..\bin.X64\extend64.pdb  obj.X64\extend.obj obj.X64\extend.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /base:0x1a00000  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\extend64.lib and object ..\..\bin.X64\extend64.exp
LINK : warning LNK4281: undesirable base address 0x1A00000 for x64 image; set base address above 4GB for best ASLR optimization
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c findfunc.cpp 
  findfunc.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\findfunc.exe /Fd..\..\bin.X64\findfunc.pdb obj.X64\findfunc.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no ..\..\bin.X64\target64.lib
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c symtest.cpp 
  symtest.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\symtest.exe /Fd..\..\bin.X64\symtest.pdb obj.X64\symtest.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no ..\..\bin.X64\target64.lib
  	copy C:\Windows\System32\dbghelp.dll ..\..\bin.X64\dbghelp.dll
          1 file(s) copied.
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\member"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c member.cpp 
  member.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\member.exe /Fd..\..\bin.X64\member.pdb obj.X64\member.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\region"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c region.cpp 
  region.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\region.exe /Fd..\..\bin.X64\region.pdb obj.X64\region.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\talloc"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll1x.cpp 
  tdll1x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll1x64.dll /Fd..\..\bin.X64\tdll1x64.pdb  obj.X64\tdll1x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:windows  /noentry  /base:0x280000000
     Creating library ..\..\bin.X64\tdll1x64.lib and object ..\..\bin.X64\tdll1x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll2x.cpp 
  tdll2x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll2x64.dll /Fd..\..\bin.X64\tdll2x64.pdb  obj.X64\tdll2x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x380000000
     Creating library ..\..\bin.X64\tdll2x64.lib and object ..\..\bin.X64\tdll2x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll3x.cpp 
  tdll3x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll3x64.dll /Fd..\..\bin.X64\tdll3x64.pdb  obj.X64\tdll3x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x480050000
     Creating library ..\..\bin.X64\tdll3x64.lib and object ..\..\bin.X64\tdll3x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll4x.cpp 
  tdll4x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll4x64.dll /Fd..\..\bin.X64\tdll4x64.pdb  obj.X64\tdll4x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x580000000
     Creating library ..\..\bin.X64\tdll4x64.lib and object ..\..\bin.X64\tdll4x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll5x.cpp 
  tdll5x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll5x64.dll /Fd..\..\bin.X64\tdll5x64.pdb  obj.X64\tdll5x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x680000000
     Creating library ..\..\bin.X64\tdll5x64.lib and object ..\..\bin.X64\tdll5x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll6x.cpp 
  tdll6x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll6x64.dll /Fd..\..\bin.X64\tdll6x64.pdb  obj.X64\tdll6x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x680010000
     Creating library ..\..\bin.X64\tdll6x64.lib and object ..\..\bin.X64\tdll6x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll7x.cpp 
  tdll7x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll7x64.dll /Fd..\..\bin.X64\tdll7x64.pdb  obj.X64\tdll7x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x680020000
     Creating library ..\..\bin.X64\tdll7x64.lib and object ..\..\bin.X64\tdll7x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll8x.cpp 
  tdll8x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll8x64.dll /Fd..\..\bin.X64\tdll8x64.pdb  obj.X64\tdll8x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x680030000
     Creating library ..\..\bin.X64\tdll8x64.lib and object ..\..\bin.X64\tdll8x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tdll9x.cpp 
  tdll9x.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tdll9x64.dll /Fd..\..\bin.X64\tdll9x64.pdb  obj.X64\tdll9x.obj /LD  /link /release /incremental:no /fixed /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  /subsystem:console  /noentry  /base:0x680040000
     Creating library ..\..\bin.X64\tdll9x64.lib and object ..\..\bin.X64\tdll9x64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c talloc.cpp 
  talloc.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\talloc.exe /Fd..\..\bin.X64\talloc.pdb obj.X64\talloc.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib psapi.lib  ..\..\bin.X64\tdll1x64.lib  ..\..\bin.X64\tdll2x64.lib  ..\..\bin.X64\tdll3x64.lib  ..\..\bin.X64\tdll4x64.lib  ..\..\bin.X64\tdll5x64.lib  ..\..\bin.X64\tdll6x64.lib  ..\..\bin.X64\tdll7x64.lib  ..\..\bin.X64\tdll8x64.lib  ..\..\bin.X64\tdll9x64.lib  /subsystem:console /entry:WinMainCRTStartup
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\traceapi"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcapi.res /i..\..\include trcapi.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcapi.cpp 
  trcapi.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcapi64.dll /Fd..\..\bin.X64\trcapi64.pdb  obj.X64\trcapi.obj obj.X64\trcapi.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /release /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib gdi32.lib user32.lib shell32.lib advapi32.lib ole32.lib ws2_32.lib
     Creating library ..\..\bin.X64\trcapi64.lib and object ..\..\bin.X64\trcapi64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c testapi.cpp 
  testapi.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\testapi.exe /Fd..\..\bin.X64\testapi.pdb obj.X64\testapi.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib gdi32.lib user32.lib shell32.lib advapi32.lib ole32.lib ws2_32.lib  /subsystem:console /fixed:no
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracebld"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcbld.res /i..\..\include trcbld.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcbld.cpp 
  trcbld.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcbld64.dll /Fd..\..\bin.X64\trcbld64.pdb  obj.X64\trcbld.obj obj.X64\trcbld.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /release /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\trcbld64.lib and object ..\..\bin.X64\trcbld64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tracebld.cpp 
  tracebld.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tracebld.exe /Fd..\..\bin.X64\tracebld.pdb obj.X64\tracebld.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracemem"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcmem.res /i..\..\include trcmem.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcmem.cpp 
  trcmem.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcmem64.dll /Fd..\..\bin.X64\trcmem64.pdb  obj.X64\trcmem.obj obj.X64\trcmem.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\trcmem64.lib and object ..\..\bin.X64\trcmem64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracereg"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcreg.res /i..\..\include trcreg.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcreg.cpp 
  trcreg.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcreg64.dll /Fd..\..\bin.X64\trcreg64.pdb  obj.X64\trcreg.obj obj.X64\trcreg.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib advapi32.lib
     Creating library ..\..\bin.X64\trcreg64.lib and object ..\..\bin.X64\trcreg64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\traceser"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcser.res /i..\..\include trcser.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcser.cpp 
  trcser.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcser64.dll /Fd..\..\bin.X64\trcser64.pdb  obj.X64\trcser.obj obj.X64\trcser.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\trcser64.lib and object ..\..\bin.X64\trcser64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracessl"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trcssl.res /i..\..\include trcssl.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trcssl.cpp 
  trcssl.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trcssl64.dll /Fd..\..\bin.X64\trcssl64.pdb  obj.X64\trcssl.obj obj.X64\trcssl.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ws2_32.lib secur32.lib
     Creating library ..\..\bin.X64\trcssl64.lib and object ..\..\bin.X64\trcssl64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracetcp"
   Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trctcp.res /i..\..\include trctcp.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trctcp.cpp 
  trctcp.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trctcp64.dll /Fd..\..\bin.X64\trctcp64.pdb  obj.X64\trctcp.obj obj.X64\trctcp.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ws2_32.lib
     Creating library ..\..\bin.X64\trctcp64.lib and object ..\..\bin.X64\trctcp64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tracelnk"
  . Created obj.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\trclnk.res /i..\..\include trclnk.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c trclnk.cpp 
  trclnk.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\trclnk64.dll /Fd..\..\bin.X64\trclnk64.pdb  obj.X64\trclnk.obj obj.X64\trclnk.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\trclnk64.lib and object ..\..\bin.X64\trclnk64.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\tryman"
   Created ..\..\bin.X64
  	rc /nologo /DDETOURS_BITS=64 /foobj.X64\tstman.res /i..\..\include tstman.rc
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tstman.cpp 
  tstman.cpp
  	cl /LD /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tstman64.dll /Fd..\..\bin.X64\tstman64.pdb  obj.X64\tstman.obj obj.X64\tstman.res  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib /subsystem:console  /export:DetourFinishHelperProcess,@1,NONAME  /export:Test3264  ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib
     Creating library ..\..\bin.X64\tstman64.lib and object ..\..\bin.X64\tstman64.exp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c tryman.cpp 
  tryman.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\tryman64.exe /Fd..\..\bin.X64\tryman64.pdb obj.X64\tryman.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib ..\..\bin.X64\tstman64.lib  /subsystem:console
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c size.cpp 
  size.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\size64.exe /Fd..\..\bin.X64\size64.pdb obj.X64\size.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib  /subsystem:console /fixed:no
  	if not exist ..\..\bin.X64\key.snk sn -k ..\..\bin.X64\key.snk
  
  Microsoft (R) .NET Framework Strong Name Utility  Version 4.0.30319.0
  Copyright (c) Microsoft Corporation.  All rights reserved.
  
  Key pair written to ..\..\bin.X64\key.snk
  	csc /nologo /nowarn:1607 /unsafe- /optimize+ /debug+ /warnaserror /platform:x64 /keyfile:..\..\bin.X64\key.snk  /out:..\..\bin.X64\managed-x64.exe managed.cs
  	csc /nologo /nowarn:1607 /unsafe- /optimize+ /debug+ /warnaserror /platform:itanium /keyfile:..\..\bin.X64\key.snk  /out:..\..\bin.X64\managed-ia64.exe managed.cs
  	csc /nologo /nowarn:1607 /unsafe- /optimize+ /debug+ /warnaserror /platform:x86 /keyfile:..\..\bin.X64\key.snk  /out:..\..\bin.X64\managed-x86.exe managed.cs
  	csc /nologo /nowarn:1607 /unsafe- /optimize+ /debug+ /warnaserror /platform:anycpu /keyfile:..\..\bin.X64\key.snk  /out:..\..\bin.X64\managed-any.exe managed.cs
  	csc /nologo /nowarn:1607 /unsafe- /optimize+ /debug+ /warnaserror /platform:anycpu32bitpreferred /keyfile:..\..\bin.X64\key.snk  /out:..\..\bin.X64\managed-any32.exe managed.cs
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\impmunge"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fdobj.X64\vc.pdb /Foobj.X64\ /c impmunge.cpp 
  impmunge.cpp
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /Fe..\..\bin.X64\impmunge.exe /Fd..\..\bin.X64\impmunge.pdb obj.X64\impmunge.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib imagehlp.lib /subsystem:console
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\dynamic_alloc"
   Created obj.X64
  	ml64 /nologo /Zi /c /Fl /Flobj.X64\x64.lst /Foobj.X64\asm.obj x64.asm
   Assembling: x64.asm
  	cl  /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /EHsc /Fdobj.X64\vc.pdb /Foobj.X64\ /c main.cpp 
  main.cpp
  	link /SUBSYSTEM:CONSOLE /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib user32.lib /PDB:"..\..\bin.X64\dalloc.pdb" /OUT:"..\..\bin.X64\dalloc.exe" obj.X64\main.obj obj.X64\asm.obj ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib
  Microsoft (R) Incremental Linker Version 14.41.34120.0
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples\payload"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /EHsc /Fdobj.X64\vc.pdb /Foobj.X64\ /c payload.cpp 
  payload.cpp
  	link /SUBSYSTEM:CONSOLE /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /PDB:"..\..\bin.X64\payload.pdb" /OUT:"..\..\bin.X64\payload.exe" obj.X64\payload.obj ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib
  Microsoft (R) Incremental Linker Version 14.41.34120.0
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\..\include /EHsc /Fdobj.X64\vc.pdb /Foobj.X64\ /c payloadtarget.cpp 
  payloadtarget.cpp
  	link /SUBSYSTEM:CONSOLE /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib kernel32.lib /PDB:"..\..\bin.X64\payloadtarget.pdb" /OUT:"..\..\bin.X64\payloadtarget.exe" obj.X64\payloadtarget.obj ..\..\lib.X64\syelog.lib ..\..\lib.X64\detours.lib
  Microsoft (R) Incremental Linker Version 14.41.34120.0
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\samples"
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main\tests"
   Created obj.X64
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\include /EHsc /DCATCH_CONFIG_NO_WINDOWS_SEH /Fdobj.X64\vc.pdb /Foobj.X64\ /c main.cpp test_module_api.cpp test_image_api.cpp corruptor.cpp process_helpers.cpp payload.cpp 
  main.cpp
  test_module_api.cpp
  test_image_api.cpp
  corruptor.cpp
  process_helpers.cpp
  payload.cpp
  Generating Code...
  	cl /nologo /Zi /MT /Gm- /W4 /WX /we4777 /we4800 /Od /DDETOUR_DEBUG=0 /I..\include /EHsc /DCATCH_CONFIG_NO_WINDOWS_SEH /Fe..\bin.X64\unittests.exe /Fd..\bin.X64\unittests.pdb  obj.X64\main.obj  obj.X64\test_module_api.obj  obj.X64\test_image_api.obj  obj.X64\corruptor.obj  obj.X64\process_helpers.obj  obj.X64\payload.obj  /link /release /incremental:no /profile /nodefaultlib:oldnames.lib ..\lib.X64\detours.lib kernel32.lib rpcrt4.lib /subsystem:console
     Creating library ..\bin.X64\unittests.lib and object ..\bin.X64\unittests.exp
  	cd "C:\Users\<USER>\Downloads\Detours-main\Detours-main"
