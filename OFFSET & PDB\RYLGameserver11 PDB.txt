

//----- (00462190) --------------------------------------------------------
unsigned __int8 __thiscall Guild::CGuild::GetRelationState(
        Guild::CGuild *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator cRelationType,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator dwRelationGID)
{
  if ( LOBYTE(cRelationType._Ptr) )
  {
    if ( LOBYTE(cRelationType._Ptr) == 1 )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
        &this->m_FriendlyMap,
        &cRelationType,
        (const unsigned int *)&dwRelationGID);
      if ( cRelationType._Ptr != this->m_FriendlyMap._Myhead )
        return cRelationType._Ptr->_Myval.second.m_cState;
    }
    else if ( LOBYTE(cRelationType._Ptr) == 2 )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
        &this->m_HostilityMap,
        &cRelationType,
        (const unsigned int *)&dwRelationGID);
      if ( cRelationType._Ptr != this->m_HostilityMap._Myhead )
        return cRelationType._Ptr->_Myval.second.m_cState;
    }
  }
  else
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
      &this->m_NeutralityMap,
      &cRelationType,
      (const unsigned int *)&dwRelationGID);
    if ( cRelationType._Ptr != this->m_NeutralityMap._Myhead )
      return cRelationType._Ptr->_Myval.second.m_cState;
  }
  return 0;
}

//----- (00462230) --------------------------------------------------------
const GuildLargeInfoNode *__thiscall Guild::CGuild::GetLargeInfo(
        Guild::CGuild *this,
        GuildLargeInfoNode *result,
        unsigned __int8 cIndexOfPage,
        unsigned __int16 wRank,
        Guild::CGuild *lpRelationGuild)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *m_dwGID; // ebx
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo *Myfirst; // eax
  unsigned __int8 i; // bl
  Guild::MemberInfo *Master; // eax
  const GuildLargeInfoNode *v11; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v12; // [esp-8h] [ebp-240h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v13; // [esp-4h] [ebp-23Ch]
  unsigned __int8 cRelation; // [esp+Fh] [ebp-229h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v15; // [esp+10h] [ebp-228h] BYREF
  unsigned int _Keyval; // [esp+14h] [ebp-224h] BYREF
  Guild::MemberInfo v17; // [esp+18h] [ebp-220h] BYREF
  GuildLargeInfoNode tempNode; // [esp+50h] [ebp-1E8h] BYREF

  cRelation = 0;
  if ( !lpRelationGuild )
    goto LABEL_11;
  m_dwGID = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)this->m_dwGID;
  _Keyval = (unsigned int)m_dwGID;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &lpRelationGuild->m_FriendlyMap,
    &v15,
    &_Keyval);
  if ( v15._Ptr != lpRelationGuild->m_FriendlyMap._Myhead )
  {
    v13._Ptr = m_dwGID;
    cRelation = 1;
    v12._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)1;
LABEL_8:
    Guild::CGuild::GetRelationState(lpRelationGuild, v12, v13);
    goto LABEL_9;
  }
  v15._Ptr = m_dwGID;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &lpRelationGuild->m_HostilityMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Keyval,
    (const unsigned int *)&v15);
  if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)_Keyval != lpRelationGuild->m_HostilityMap._Myhead )
  {
    cRelation = 2;
    Guild::CGuild::GetRelationState(
      lpRelationGuild,
      (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)2,
      (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)m_dwGID);
    goto LABEL_9;
  }
  if ( Guild::CGuild::IsNeutralityGuild(lpRelationGuild, (unsigned int)m_dwGID) )
  {
    v13._Ptr = m_dwGID;
    cRelation = 0;
    v12._Ptr = 0;
    goto LABEL_8;
  }
LABEL_9:
  if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)lpRelationGuild->m_dwGID == m_dwGID )
    cRelation = 3;
LABEL_11:
  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  for ( i = 0; Myfirst != Mylast; ++Myfirst )
  {
    if ( Myfirst->m_MemberListInfo.m_cTitle != 5 )
      ++i;
  }
  Master = Guild::CGuild::GetMaster(this, &v17);
  GuildSmallInfoNode::GuildSmallInfoNode(
    &tempNode,
    this->m_dwGID,
    cIndexOfPage,
    this->m_cInclination,
    wRank,
    this->m_dwFame,
    this->m_cLevel,
    i,
    this->m_strName,
    Master->m_strName);
  tempNode.m_cRelation = cRelation;
  if ( this != (Guild::CGuild *)-19 )
    qmemcpy(tempNode.m_szMark, this->m_szMark, sizeof(tempNode.m_szMark));
  v11 = result;
  qmemcpy(result, &tempNode, 0x1D8u);
  *(_WORD *)&result->m_szMark[431] = *(_WORD *)&tempNode.m_szMark[431];
  result->m_cRelation = tempNode.m_cRelation;
  return v11;
}

//----- (00462390) --------------------------------------------------------
Guild::MemberInfo *__thiscall std::vector<Guild::MemberInfo>::_Ufill(
        std::vector<Guild::MemberInfo> *this,
        Guild::MemberInfo *_Ptr,
        unsigned int _Count,
        const Guild::MemberInfo *_Val)
{
  std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (004623C0) --------------------------------------------------------
char __thiscall Guild::CGuild::LeaveMember(Guild::CGuild *this, unsigned int dwCID)
{
  Guild::MemberInfo *Myfirst; // ebx
  Guild::MemberInfo *Mylast; // eax
  Guild::MemberInfo *v6; // edx
  Guild::MemberInfo *i; // eax
  Guild::MemberInfo *v8; // esi
  Guild::MemberInfo *v9; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  PktGuildCmd pktGuildCmd; // [esp+8h] [ebp-74h] BYREF
  Guild::MemberInfo memberInfo; // [esp+40h] [ebp-3Ch] BYREF

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    goto LABEL_5;
  do
  {
    if ( dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::LeaveMember",
      aDWorkRylSource_47,
      1532,
      aGid0x08x_4,
      this->m_dwGID,
      dwCID);
    return 0;
  }
  else
  {
    qmemcpy(&memberInfo, Myfirst, sizeof(memberInfo));
    pktGuildCmd.m_dwGID = this->m_dwGID;
    pktGuildCmd.m_dwSenderID = dwCID;
    pktGuildCmd.m_dwReferenceID = 0;
    strncpy(pktGuildCmd.m_szSenderName, memberInfo.m_strName, 0x10u);
    pktGuildCmd.m_wCmd = 5;
    if ( PacketWrap::WrapCrypt((char *)&pktGuildCmd, 0x35u, 0x89u, 0, 0) )
      Guild::CGuild::SendAllMember(this, (char *)&pktGuildCmd, 0x35u, 0x89u);
    v6 = this->m_MemberList._Mylast;
    for ( i = Myfirst + 1; i != v6; ++i )
    {
      v8 = i;
      v9 = Myfirst++;
      qmemcpy(v9, v8, sizeof(Guild::MemberInfo));
    }
    --this->m_MemberList._Mylast;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, dwCID);
    if ( Character )
      CCharacter::SetGID(Character, 0);
    return 1;
  }
}

//----- (00462520) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004627E0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,Guild::RelationInfo> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,Guild::RelationInfo> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0xAAAAAA9 )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,Guild::RelationInfo> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00462990) --------------------------------------------------------
void __thiscall __noreturn std::vector<Guild::MemberInfo>::_Xlen(std::vector<Guild::MemberInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00462A00) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00462AC0) --------------------------------------------------------
bool __thiscall Guild::CompareMemberName::operator()(
        Guild::CompareMemberName *this,
        const Guild::MemberInfo *lhs,
        const Guild::MemberInfo *rhs)
{
  std::string::_Bxty *Ptr; // eax
  std::string v5; // [esp+Ch] [ebp-44h] BYREF
  std::string v6; // [esp+28h] [ebp-28h] BYREF
  int v7; // [esp+4Ch] [ebp-4h]
  bool rhsa; // [esp+58h] [ebp+8h]

  v6._Myres = 15;
  v6._Mysize = 0;
  v6._Bx._Buf[0] = 0;
  std::string::assign(&v6, rhs->m_strName, strlen(rhs->m_strName));
  v7 = 0;
  v5._Myres = 15;
  v5._Mysize = 0;
  v5._Bx._Buf[0] = 0;
  std::string::assign(&v5, lhs->m_strName, strlen(lhs->m_strName));
  Ptr = (std::string::_Bxty *)v6._Bx._Ptr;
  LOBYTE(v7) = 1;
  if ( v6._Myres < 0x10 )
    Ptr = &v6._Bx;
  rhsa = std::string::compare(&v5, 0, v5._Mysize, Ptr->_Buf, v6._Mysize) < 0;
  if ( v5._Myres >= 0x10 )
    operator delete(v5._Bx._Ptr);
  v5._Mysize = 0;
  v5._Bx._Buf[0] = 0;
  v5._Myres = 15;
  if ( v6._Myres >= 0x10 )
    operator delete(v6._Bx._Ptr);
  return rhsa;
}

//----- (00462BD0) --------------------------------------------------------
char __thiscall Guild::CGuild::DeleteRelationList(
        Guild::CGuild *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator cRelationType,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator dwRelationGID)
{
  std::map<unsigned long,Guild::RelationInfo> *p_m_FriendlyMap; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // eax

  if ( LOBYTE(cRelationType._Ptr) )
  {
    if ( LOBYTE(cRelationType._Ptr) != 1 )
    {
      if ( LOBYTE(cRelationType._Ptr) == 2 )
      {
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
          &this->m_HostilityMap,
          &cRelationType,
          (const unsigned int *)&dwRelationGID);
        if ( cRelationType._Ptr != this->m_HostilityMap._Myhead )
        {
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
            &this->m_HostilityMap,
            &cRelationType,
            cRelationType);
          return 1;
        }
      }
      return 0;
    }
    p_m_FriendlyMap = &this->m_FriendlyMap;
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
      &this->m_FriendlyMap,
      &cRelationType,
      (const unsigned int *)&dwRelationGID);
    Ptr = cRelationType._Ptr;
    if ( cRelationType._Ptr == this->m_FriendlyMap._Myhead )
      return 0;
    goto LABEL_9;
  }
  p_m_FriendlyMap = &this->m_NeutralityMap;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &this->m_NeutralityMap,
    &cRelationType,
    (const unsigned int *)&dwRelationGID);
  Ptr = cRelationType._Ptr;
  if ( cRelationType._Ptr != this->m_NeutralityMap._Myhead )
LABEL_9:
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
      p_m_FriendlyMap,
      &cRelationType,
      (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)Ptr);
  return 1;
}

//----- (00462C80) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> *result,
        std::pair<unsigned long const ,Guild::RelationInfo> *_Val)
{
  const std::pair<unsigned long const ,Guild::RelationInfo> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int first; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      v7 = first < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (std::pair<unsigned long const ,Guild::RelationInfo> *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00462D40) --------------------------------------------------------
void __thiscall std::vector<Guild::MemberInfo>::_Insert_n(
        std::vector<Guild::MemberInfo> *this,
        std::vector<Guild::MemberInfo>::iterator _Where,
        unsigned int _Count,
        const Guild::MemberInfo *_Val)
{
  Guild::MemberInfo *Myfirst; // edi
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // edi
  Guild::MemberInfo *v12; // eax
  Guild::MemberInfo *v13; // ecx
  Guild::MemberInfo *v14; // eax
  char *v15; // esi
  Guild::MemberInfo *v16; // eax
  Guild::MemberInfo *v17; // esi
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo *v20; // edx
  Guild::MemberInfo *v21; // [esp-Ch] [ebp-6Ch]
  unsigned int v22; // [esp-8h] [ebp-68h]
  int v23; // [esp+0h] [ebp-60h] BYREF
  Guild::MemberInfo *_Ptr; // [esp+Ch] [ebp-54h]
  Guild::MemberInfo *_Newvec; // [esp+10h] [ebp-50h]
  Guild::MemberInfo _Tmp; // [esp+14h] [ebp-4Ch] BYREF
  int *v27; // [esp+50h] [ebp-10h]
  int v28; // [esp+5Ch] [ebp-4h]
  Guild::MemberInfo *_Wherea; // [esp+68h] [ebp+8h]
  Guild::MemberInfo *_Vala; // [esp+70h] [ebp+10h]

  qmemcpy(&_Tmp, _Val, sizeof(_Tmp));
  Myfirst = this->_Myfirst;
  v27 = &v23;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - this->_Myfirst;
    else
      v7 = 0;
    if ( 76695844 - v7 < _Count )
      std::vector<Guild::MemberInfo>::_Xlen(this);
    if ( this->_Myfirst )
      v8 = this->_Mylast - this->_Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<Guild::MemberInfo *,Guild::MemberInfo *>(_Where._Myptr, _Wherea, _Vala);
        std::fill<Guild::MemberInfo *,Guild::MemberInfo>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<Guild::MemberInfo>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Guild::MemberInfo *,Guild::MemberInfo>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 76695844 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( this->_Myfirst )
        v10 = this->_Mylast - this->_Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<Guild::MemberInfo>::size(this) + _Count;
      v11 = v9;
      v12 = (Guild::MemberInfo *)operator new((tagHeader *)(56 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (Guild::MemberInfo *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (00463000) --------------------------------------------------------
void std::_Insertion_sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        ...)
{
  Guild::MemberInfo *i; // esi
  const Guild::MemberInfo *v3; // edi
  std::vector<Guild::MemberInfo>::iterator v4; // ebx
  va_list va; // [esp+10h] [ebp+Ch] BYREF

  va_start(va, _Last);
  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, i, _First._Myptr) )
      {
        if ( _First._Myptr != i && i != &i[1] )
          std::_Rotate<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo>(
            _First,
            (std::vector<Guild::MemberInfo>::iterator)i,
            (std::vector<Guild::MemberInfo>::iterator)&i[1]);
      }
      else
      {
        v3 = i - 1;
        if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, i, i - 1) )
        {
          do
            v4._Myptr = (Guild::MemberInfo *)v3--;
          while ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, i, v3) );
          if ( v4._Myptr != i )
            std::_Rotate<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo>(
              v4,
              (std::vector<Guild::MemberInfo>::iterator)i,
              (std::vector<Guild::MemberInfo>::iterator)&i[1]);
        }
      }
    }
  }
}

//----- (00463090) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebp
  Guild::MemberInfo *v6; // esi
  unsigned __int8 m_cTitle; // al
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_cTitle = v6->m_MemberListInfo.m_cTitle;
    if ( m_cTitle == _Val.m_MemberListInfo.m_cTitle )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_cTitle < _Val.m_MemberListInfo.m_cTitle;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463110) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebp
  Guild::MemberInfo *v6; // esi
  unsigned __int8 m_cClass; // al
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_cClass = v6->m_MemberListInfo.m_cClass;
    if ( m_cClass == _Val.m_MemberListInfo.m_cClass )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_cClass < _Val.m_MemberListInfo.m_cClass;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463190) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebp
  Guild::MemberInfo *v6; // esi
  unsigned __int8 m_cLevel; // al
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_cLevel = v6->m_MemberListInfo.m_cLevel;
    if ( m_cLevel == _Val.m_MemberListInfo.m_cLevel )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_cLevel > _Val.m_MemberListInfo.m_cLevel;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463210) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebx
  Guild::MemberInfo *v6; // esi
  unsigned int m_dwFame; // eax
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_dwFame = v6->m_MemberDetailInfo.m_dwFame;
    if ( m_dwFame == _Val.m_MemberDetailInfo.m_dwFame )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_dwFame > _Val.m_MemberDetailInfo.m_dwFame;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463290) --------------------------------------------------------
void std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val,
        ...)
{
  int v4; // edi
  int i; // ebx
  va_list va; // [esp+58h] [ebp+48h] BYREF

  va_start(va, _Val);
  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    if ( !Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, &_First._Myptr[i], &_Val) )
      break;
    qmemcpy(&_First._Myptr[v4], &_First._Myptr[i], sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463300) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebx
  Guild::MemberInfo *v6; // esi
  unsigned int m_dwGold; // eax
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_dwGold = v6->m_MemberDetailInfo.m_dwGold;
    if ( m_dwGold == _Val.m_MemberDetailInfo.m_dwGold )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_dwGold > _Val.m_MemberDetailInfo.m_dwGold;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463380) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Top,
        Guild::MemberInfo _Val)
{
  int v4; // edi
  int i; // ebx
  Guild::MemberInfo *v6; // esi
  unsigned int m_dwServerID; // eax
  bool v8; // al

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    m_dwServerID = v6->m_dwServerID;
    if ( m_dwServerID == _Val.m_dwServerID )
      v8 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Top, &_First._Myptr[i], &_Val);
    else
      v8 = m_dwServerID < _Val.m_dwServerID;
    if ( !v8 )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00463400) --------------------------------------------------------
void std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Mid,
        std::vector<Guild::MemberInfo>::iterator _Last,
        ...)
{
  _BYTE v3[56]; // [esp+10h] [ebp-3Ch] BYREF
  va_list va; // [esp+5Ch] [ebp+10h] BYREF

  va_start(va, _Last);
  if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, _Mid._Myptr, _First._Myptr) )
  {
    qmemcpy(v3, _Mid._Myptr, sizeof(v3));
    qmemcpy(_Mid._Myptr, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(_First._Myptr, v3, sizeof(Guild::MemberInfo));
  }
  if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, _Last._Myptr, _Mid._Myptr) )
  {
    qmemcpy(v3, _Last._Myptr, sizeof(v3));
    qmemcpy(_Last._Myptr, _Mid._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(_Mid._Myptr, v3, sizeof(Guild::MemberInfo));
  }
  if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)va, _Mid._Myptr, _First._Myptr) )
  {
    qmemcpy(v3, _Mid._Myptr, sizeof(v3));
    qmemcpy(_Mid._Myptr, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(_First._Myptr, v3, sizeof(Guild::MemberInfo));
  }
}

//----- (004634D0) --------------------------------------------------------
bool __thiscall Guild::CGuild::InsertRelation(
        Guild::CGuild *this,
        unsigned int dwRelationGID,
        const Guild::RelationInfo *Info)
{
  int v4; // ecx
  int v5; // edx
  int v6; // ecx
  int v7; // edx
  __int16 v8; // ax
  int v10; // ecx
  int v11; // edx
  int v12; // ecx
  int v13; // edx
  __int16 MSecond_high; // ax
  std::pair<unsigned long,Guild::RelationInfo> *v15; // eax
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> result; // [esp+8h] [ebp-48h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> v17; // [esp+10h] [ebp-40h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> v18; // [esp+18h] [ebp-38h] BYREF
  std::pair<unsigned long const ,Guild::RelationInfo> _Val; // [esp+20h] [ebp-30h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> v20; // [esp+38h] [ebp-18h] BYREF

  if ( dwRelationGID )
  {
    if ( Info->m_cRelation )
    {
      if ( Info->m_cRelation == 1 )
      {
        v10 = *(_DWORD *)&Info->m_cRelation;
        _Val.first = dwRelationGID;
        v11 = *(_DWORD *)&Info->m_WaitTime.Month;
        *(_DWORD *)&_Val.second.m_cRelation = v10;
        v12 = *(_DWORD *)&Info->m_WaitTime.Hour;
        *(_DWORD *)&_Val.second.m_WaitTime.Month = v11;
        v13 = *(_DWORD *)&Info->m_WaitTime.Second;
        MSecond_high = HIWORD(Info->m_WaitTime.MSecond);
        *(_DWORD *)&_Val.second.m_WaitTime.Hour = v12;
        *(_DWORD *)&_Val.second.m_WaitTime.Second = v13;
        HIWORD(_Val.second.m_WaitTime.MSecond) = MSecond_high;
        return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
                 &this->m_FriendlyMap,
                 &v17,
                 &_Val)->second;
      }
      if ( Info->m_cRelation == 2 )
      {
        v4 = *(_DWORD *)&Info->m_cRelation;
        _Val.first = dwRelationGID;
        v5 = *(_DWORD *)&Info->m_WaitTime.Month;
        *(_DWORD *)&_Val.second.m_cRelation = v4;
        v6 = *(_DWORD *)&Info->m_WaitTime.Hour;
        *(_DWORD *)&_Val.second.m_WaitTime.Month = v5;
        v7 = *(_DWORD *)&Info->m_WaitTime.Second;
        v8 = HIWORD(Info->m_WaitTime.MSecond);
        *(_DWORD *)&_Val.second.m_WaitTime.Hour = v6;
        *(_DWORD *)&_Val.second.m_WaitTime.Second = v7;
        HIWORD(_Val.second.m_WaitTime.MSecond) = v8;
        return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
                 &this->m_HostilityMap,
                 &result,
                 &_Val)->second;
      }
    }
    else if ( Info->m_cState )
    {
      v15 = std::make_pair<unsigned long,Guild::RelationInfo>(&v20, dwRelationGID, *Info);
      std::pair<unsigned long const,Guild::RelationInfo>::pair<unsigned long const,Guild::RelationInfo>(&_Val, (int)v15);
      return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
               &this->m_NeutralityMap,
               &v18,
               &_Val)->second;
    }
  }
  return 0;
}

//----- (00463600) --------------------------------------------------------
char __thiscall Guild::CGuild::InsertRelationList(
        Guild::CGuild *this,
        unsigned __int8 cRelation,
        unsigned __int8 cState,
        unsigned int dwRelationGID)
{
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> result; // [esp+4h] [ebp-54h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> v7; // [esp+Ch] [ebp-4Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> v8; // [esp+14h] [ebp-44h] BYREF
  _SYSTEMTIME sysTime; // [esp+1Ch] [ebp-3Ch] BYREF
  Guild::RelationInfo Info; // [esp+2Ch] [ebp-2Ch]
  std::pair<unsigned long const ,Guild::RelationInfo> _Val; // [esp+40h] [ebp-18h] BYREF

  Info.m_cRelation = 0;
  Info.m_cState = 0;
  GetLocalTime(&sysTime);
  Info.m_WaitTime.Year = sysTime.wYear;
  Info.m_WaitTime.Day = sysTime.wDay;
  Info.m_WaitTime.Hour = sysTime.wHour;
  Info.m_WaitTime.Second = sysTime.wSecond;
  Info.m_WaitTime.Month = sysTime.wMonth;
  Info.m_WaitTime.Minute = sysTime.wMinute;
  if ( cRelation )
  {
    if ( cRelation == 1 )
    {
      Info.m_cRelation = cRelation;
      Info.m_cState = cState;
      _Val.first = dwRelationGID;
      _Val.second = Info;
      return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
               &this->m_FriendlyMap,
               &v7,
               &_Val)->second;
    }
    else if ( cRelation == 2 )
    {
      Info.m_cRelation = cRelation;
      Info.m_cState = cState;
      _Val.first = dwRelationGID;
      _Val.second = Info;
      return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
               &this->m_HostilityMap,
               &result,
               &_Val)->second;
    }
    else
    {
      return 0;
    }
  }
  else if ( cState )
  {
    Info.m_cRelation = 0;
    Info.m_cState = cState;
    _Val.first = dwRelationGID;
    _Val.second = Info;
    return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
             &this->m_NeutralityMap,
             &v8,
             &_Val)->second;
  }
  else
  {
    return 1;
  }
}

//----- (004637A0) --------------------------------------------------------
void __thiscall std::vector<Guild::MemberInfo>::_Construct_n(
        std::vector<Guild::MemberInfo> *this,
        unsigned int _Count,
        const Guild::MemberInfo *_Val)
{
  Guild::MemberInfo *v4; // ebx
  int v5; // [esp+0h] [ebp-20h] BYREF
  CBanList *v6; // [esp+Ch] [ebp-14h]
  int *v7; // [esp+10h] [ebp-10h]
  int v8; // [esp+1Ch] [ebp-4h]

  v7 = &v5;
  v6 = (CBanList *)this;
  this->_Myfirst = 0;
  this->_Mylast = 0;
  this->_Myend = 0;
  if ( _Count )
  {
    if ( _Count > 0x4924924 )
      std::vector<Guild::MemberInfo>::_Xlen(this);
    v4 = (Guild::MemberInfo *)operator new((tagHeader *)(56 * _Count));
    this->_Myend = &v4[_Count];
    this->_Myfirst = v4;
    this->_Mylast = v4;
    v8 = 0;
    std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(
      v4,
      _Count,
      _Val);
    this->_Mylast = &v4[_Count];
  }
}

//----- (00463850) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__thiscall std::vector<Guild::MemberInfo>::insert(
        std::vector<Guild::MemberInfo> *this,
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _Where,
        const Guild::MemberInfo *_Val)
{
  Guild::MemberInfo *Myfirst; // esi
  int v6; // esi
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<Guild::MemberInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (004638C0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  Guild::MemberInfo *v9; // esi
  unsigned __int8 m_cTitle; // al
  unsigned __int8 v11; // cl
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      v9 = &_First._Myptr[v7];
      m_cTitle = v9->m_MemberListInfo.m_cTitle;
      v11 = v9[-1].m_MemberListInfo.m_cTitle;
      if ( m_cTitle == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v9 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_cTitle < v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463980) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  Guild::MemberInfo *v9; // esi
  unsigned __int8 m_cClass; // al
  unsigned __int8 v11; // cl
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      v9 = &_First._Myptr[v7];
      m_cClass = v9->m_MemberListInfo.m_cClass;
      v11 = v9[-1].m_MemberListInfo.m_cClass;
      if ( m_cClass == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v9 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_cClass < v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463A40) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  Guild::MemberInfo *v9; // esi
  unsigned __int8 m_cLevel; // al
  unsigned __int8 v11; // cl
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      v9 = &_First._Myptr[v7];
      m_cLevel = v9->m_MemberListInfo.m_cLevel;
      v11 = v9[-1].m_MemberListInfo.m_cLevel;
      if ( m_cLevel == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v9 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_cLevel > v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463B00) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  unsigned int m_dwFame; // esi
  Guild::MemberInfo *v10; // eax
  unsigned int v11; // edi
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      m_dwFame = _First._Myptr[v7].m_MemberDetailInfo.m_dwFame;
      v10 = &_First._Myptr[v7];
      v11 = v10[-1].m_MemberDetailInfo.m_dwFame;
      if ( m_dwFame == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v10 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_dwFame > v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463BC0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // ebp
  int v7; // ebx
  bool v8; // zf
  Guild::MemberInfo *v9; // esi
  Guild::MemberInfo *v10; // edi
  int v11; // ecx
  Guild::MemberInfo *Myptr; // eax
  Guild::MemberInfo v13; // [esp-3Ch] [ebp-4Ch] BYREF
  int v14; // [esp-4h] [ebp-14h]

  v5 = _Bottom;
  v6 = _Hole;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  if ( v7 < _Bottom )
  {
    do
    {
      if ( Guild::CompareMemberName::operator()(
             (Guild::CompareMemberName *)&_Pred,
             &_First._Myptr[v7],
             &_First._Myptr[v7 - 1]) )
      {
        --v7;
      }
      v5 = _Bottom;
      v9 = &_First._Myptr[v7];
      v10 = &_First._Myptr[v6];
      v6 = v7;
      v7 = 2 * v7 + 2;
      qmemcpy(v10, v9, sizeof(Guild::MemberInfo));
    }
    while ( v7 < _Bottom );
    v8 = v7 == _Bottom;
  }
  if ( v8 )
  {
    v11 = v5;
    Myptr = _First._Myptr;
    qmemcpy(&_First._Myptr[v6], &_First._Myptr[v11 - 1], sizeof(_First._Myptr[v6]));
    v6 = _Bottom - 1;
  }
  else
  {
    Myptr = _First._Myptr;
  }
  v14 = _Pred;
  qmemcpy(&v13, &_Val, sizeof(v13));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
    (std::vector<Guild::MemberInfo>::iterator)Myptr,
    v6,
    _Hole,
    v13);
}

//----- (00463C80) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  unsigned int m_dwGold; // esi
  Guild::MemberInfo *v10; // eax
  unsigned int v11; // edi
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      m_dwGold = _First._Myptr[v7].m_MemberDetailInfo.m_dwGold;
      v10 = &_First._Myptr[v7];
      v11 = v10[-1].m_MemberDetailInfo.m_dwGold;
      if ( m_dwGold == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v10 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_dwGold > v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463D40) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Guild::MemberInfo _Val,
        int _Pred)
{
  int v5; // eax
  int v6; // edx
  int v7; // ebx
  bool v8; // zf
  unsigned int m_dwServerID; // esi
  Guild::MemberInfo *v10; // eax
  unsigned int v11; // edi
  bool v12; // al
  int v13; // edi
  Guild::MemberInfo *v14; // esi
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-50h] BYREF
  int v16; // [esp-4h] [ebp-18h]
  int _Top; // [esp+10h] [ebp-4h]

  v5 = _Hole;
  v6 = _Bottom;
  v7 = 2 * _Hole + 2;
  v8 = v7 == _Bottom;
  _Top = _Hole;
  if ( v7 < _Bottom )
  {
    do
    {
      m_dwServerID = _First._Myptr[v7].m_dwServerID;
      v10 = &_First._Myptr[v7];
      v11 = v10[-1].m_dwServerID;
      if ( m_dwServerID == v11 )
      {
        v12 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Hole, &_First._Myptr[v7], v10 - 1);
        v6 = _Bottom;
      }
      else
      {
        v12 = m_dwServerID < v11;
      }
      if ( v12 )
        --v7;
      v13 = _Hole;
      _Hole = v7;
      v14 = &_First._Myptr[v7];
      v7 = 2 * v7 + 2;
      v8 = v7 == v6;
      qmemcpy(&_First._Myptr[v13], v14, sizeof(_First._Myptr[v13]));
    }
    while ( v7 < v6 );
    v5 = _Hole;
  }
  if ( v8 )
  {
    qmemcpy(&_First._Myptr[v5], &_First._Myptr[v6 - 1], sizeof(_First._Myptr[v5]));
    _Hole = v6 - 1;
    v5 = v6 - 1;
  }
  v16 = _Pred;
  qmemcpy(&v15, &_Val, sizeof(v15));
  std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
    _First,
    v5,
    _Top,
    v15);
}

//----- (00463E00) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00463E70) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00463EE0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00463F50) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00463FC0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00464030) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (004640A0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v3; // ebx
  Guild::MemberInfo *v4; // ebp
  Guild::MemberInfo v5; // [esp-3Ch] [ebp-4Ch] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v3 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      qmemcpy(&v5, --v4, sizeof(v5));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        _First,
        --v3,
        _Lasta,
        v5,
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00464110) --------------------------------------------------------
void __cdecl std::_Median<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Mid,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int v4; // eax
  int v5; // esi
  unsigned int v6; // edi
  Guild::MemberInfo *v7; // [esp-38h] [ebp-40h]
  Guild::MemberInfo *v10; // [esp-10h] [ebp-18h]
  char *_Firsta; // [esp+Ch] [ebp+4h]
  char *_Lasta; // [esp+14h] [ebp+Ch]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(_First, _Mid, _Last, _Pred);
  }
  else
  {
    v5 = (v4 + 1) / 8;
    v6 = 112 * v5;
    v5 *= 56;
    v10 = &_First._Myptr[v6 / 0x38];
    _Firsta = (char *)_First._Myptr + v5;
    std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
      _First,
      (std::vector<Guild::MemberInfo>::iterator)_Firsta,
      (std::vector<Guild::MemberInfo>::iterator)v10,
      _Pred);
    std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
      (std::vector<Guild::MemberInfo>::iterator)((char *)_Mid._Myptr - v5),
      _Mid,
      (std::vector<Guild::MemberInfo>::iterator)((char *)_Mid._Myptr + v5),
      _Pred);
    v7 = &_Last._Myptr[v6 / 0xFFFFFFC8];
    _Lasta = (char *)_Last._Myptr - v5;
    std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)_Lasta,
      _Last,
      _Pred);
    std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
      (std::vector<Guild::MemberInfo>::iterator)_Firsta,
      _Mid,
      (std::vector<Guild::MemberInfo>::iterator)_Lasta,
      _Pred);
  }
}

//----- (004641C0) --------------------------------------------------------
void __thiscall std::map<unsigned long,Guild::RelationInfo>::~map<unsigned long,Guild::RelationInfo>(
        std::map<unsigned long,Guild::RelationInfo> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004641F0) --------------------------------------------------------
void __thiscall Guild::CGuild::~CGuild(Guild::CGuild *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v3; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v4; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v5; // [esp-8h] [ebp-28h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v6; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v7; // [esp-4h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator result; // [esp+10h] [ebp-10h] BYREF
  int v9; // [esp+1Ch] [ebp-4h]

  this->__vftable = &Guild::CGuild::`vftable';
  Myhead = this->m_NeutralityMap._Myhead;
  v3._Ptr = Myhead->_Left;
  v9 = 2;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
    &this->m_NeutralityMap,
    &result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)Myhead);
  operator delete(this->m_NeutralityMap._Myhead);
  this->m_NeutralityMap._Myhead = 0;
  this->m_NeutralityMap._Mysize = 0;
  v6 = this->m_HostilityMap._Myhead;
  v4._Ptr = v6->_Left;
  LOBYTE(v9) = 1;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
    &this->m_HostilityMap,
    &result,
    v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v6);
  operator delete(this->m_HostilityMap._Myhead);
  this->m_HostilityMap._Myhead = 0;
  this->m_HostilityMap._Mysize = 0;
  v7 = this->m_FriendlyMap._Myhead;
  v5._Ptr = v7->_Left;
  LOBYTE(v9) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
    &this->m_FriendlyMap,
    &result,
    v5,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v7);
  operator delete(this->m_FriendlyMap._Myhead);
  this->m_FriendlyMap._Myhead = 0;
  this->m_FriendlyMap._Mysize = 0;
  if ( this->m_MemberList._Myfirst )
    operator delete(this->m_MemberList._Myfirst);
  this->m_MemberList._Myfirst = 0;
  this->m_MemberList._Mylast = 0;
  this->m_MemberList._Myend = 0;
}
// 4E52E8: using guessed type void *Guild::CGuild::`vftable';

//----- (004642F0) --------------------------------------------------------
bool __thiscall Guild::CGuild::ChangeRelation(
        Guild::CGuild *this,
        unsigned int dwRelationGID,
        std::map<unsigned long,Guild::RelationInfo> *Info)
{
  unsigned int v3; // ebp
  const Guild::RelationInfo *v5; // esi
  unsigned __int8 v6; // bl
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // eax
  unsigned __int8 m_cState; // cl
  bool v10; // zf
  unsigned __int8 v11; // cl
  unsigned __int8 v12; // cl
  TIME *p_m_WaitTime; // eax
  unsigned __int8 v15; // al
  std::pair<unsigned long,Guild::RelationInfo> *v16; // eax
  unsigned __int8 cTime; // [esp+10h] [ebp-40h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator cCurrRelation; // [esp+14h] [ebp-3Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> v19; // [esp+18h] [ebp-38h] BYREF
  std::pair<unsigned long const ,Guild::RelationInfo> _Val; // [esp+20h] [ebp-30h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> result; // [esp+38h] [ebp-18h] BYREF

  v3 = dwRelationGID;
  if ( !dwRelationGID )
    return 0;
  cTime = 0;
  LOBYTE(cCurrRelation._Ptr) = Guild::CGuild::GetRelation(this, dwRelationGID);
  Guild::CGuild::GetRelationState(
    this,
    cCurrRelation,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v3);
  v5 = (const Guild::RelationInfo *)Info;
  v6 = (unsigned __int8)Info->comp.std::binary_function<unsigned long,unsigned long,bool>;
  if ( LOBYTE(cCurrRelation._Ptr) != *(_BYTE *)&Info->comp.std::binary_function<unsigned long,unsigned long,bool> )
  {
    Guild::CGuild::DeleteRelationList(
      this,
      cCurrRelation,
      (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v3);
    Guild::CGuild::InsertRelationList(this, v5->m_cRelation, v5->m_cState, v3);
    Guild::CGuild::SendGuildRelationToAllMember(this, v3, v5->m_cRelation, v5->m_cState, 0, 0);
    return 1;
  }
  if ( v6 )
  {
    if ( v6 == 1 )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
        &this->m_FriendlyMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&Info,
        &dwRelationGID);
      Myhead = this->m_FriendlyMap._Myhead;
      goto LABEL_9;
    }
    if ( v6 == 2 )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
        &this->m_HostilityMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&Info,
        &dwRelationGID);
      Myhead = this->m_HostilityMap._Myhead;
LABEL_9:
      Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)Info;
      if ( Info != (std::map<unsigned long,Guild::RelationInfo> *)Myhead )
      {
        m_cState = v5->m_cState;
        if ( BYTE1(Info[1]._Myhead) != m_cState )
        {
          BYTE1(Info[1]._Myhead) = m_cState;
          v10 = v5->m_cState == 3;
          goto LABEL_18;
        }
      }
    }
    return 0;
  }
  Info = &this->m_NeutralityMap;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &this->m_NeutralityMap,
    &cCurrRelation,
    &dwRelationGID);
  Ptr = cCurrRelation._Ptr;
  if ( cCurrRelation._Ptr != this->m_NeutralityMap._Myhead )
  {
    v11 = v5->m_cState;
    if ( cCurrRelation._Ptr->_Myval.second.m_cState != v11 )
    {
      if ( !v11 )
      {
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
          Info,
          (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&dwRelationGID,
          cCurrRelation);
        goto LABEL_20;
      }
      cCurrRelation._Ptr->_Myval.second.m_cState = v5->m_cState;
      v12 = v5->m_cState;
      if ( v12 == 6 )
      {
LABEL_19:
        p_m_WaitTime = &Ptr->_Myval.second.m_WaitTime;
        *(_DWORD *)&p_m_WaitTime->Year = *(_DWORD *)&v5->m_WaitTime.Year;
        *(_DWORD *)&p_m_WaitTime->Day = *(_DWORD *)&v5->m_WaitTime.Day;
        *(_DWORD *)&p_m_WaitTime->Minute = *(_DWORD *)&v5->m_WaitTime.Minute;
        p_m_WaitTime->MSecond = v5->m_WaitTime.MSecond;
        cTime = -96;
        goto LABEL_20;
      }
      v10 = v12 == 9;
LABEL_18:
      if ( v10 )
        goto LABEL_19;
LABEL_20:
      Guild::CGuild::SendGuildRelationToAllMember(this, v3, v5->m_cRelation, v5->m_cState, cTime, 0);
      return 1;
    }
    return 0;
  }
  v15 = v5->m_cState;
  if ( v15 == 6 || v15 == 9 )
    cTime = -96;
  Guild::CGuild::SendGuildRelationToAllMember(this, v3, 0, v15, cTime, 0);
  v16 = std::make_pair<unsigned long,Guild::RelationInfo>(&result, v3, *v5);
  std::pair<unsigned long const,Guild::RelationInfo>::pair<unsigned long const,Guild::RelationInfo>(&_Val, (int)v16);
  return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(
           Info,
           &v19,
           &_Val)->second;
}

//----- (004644F0) --------------------------------------------------------
void __thiscall std::vector<Guild::MemberInfo>::vector<Guild::MemberInfo>(
        std::vector<Guild::MemberInfo> *this,
        unsigned int _Count)
{
  Guild::MemberInfo _Val; // [esp+4h] [ebp-38h] BYREF

  memset(&_Val, 0, 45);
  _Val.m_MemberDetailInfo.m_dwFame = 0;
  _Val.m_MemberDetailInfo.m_dwGold = 0;
  std::vector<Guild::MemberInfo>::_Construct_n(this, _Count, &_Val);
}

//----- (00464560) --------------------------------------------------------
void __thiscall std::vector<Guild::MemberInfo>::push_back(
        std::vector<Guild::MemberInfo> *this,
        const Guild::MemberInfo *_Val)
{
  Guild::MemberInfo *Myfirst; // ebx
  unsigned int v4; // esi
  Guild::MemberInfo *Mylast; // esi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Guild::MemberInfo>::insert(
      this,
      (std::vector<Guild::MemberInfo>::iterator *)&_Val,
      (std::vector<Guild::MemberInfo>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (004645F0) --------------------------------------------------------
std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> *result,
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // esi
  Guild::MemberInfo *v5; // ebx
  Guild::MemberInfo *v6; // ebp
  unsigned int v7; // esi
  Guild::MemberInfo *v8; // eax
  Guild::MemberInfo *v9; // edi
  bool v10; // zf
  const Guild::MemberInfo *v11; // esi
  bool v12; // cf
  Guild::MemberInfo *v13; // eax
  Guild::MemberInfo *v14; // edi
  std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> *v15; // eax
  Guild::MemberInfo *_Plast; // [esp+10h] [ebp-198h]
  Guild::MemberInfo *_Glast; // [esp+14h] [ebp-194h]
  Guild::MemberInfo *v18; // [esp+18h] [ebp-190h]
  _BYTE v19[56]; // [esp+1Ch] [ebp-18Ch] BYREF
  _BYTE v20[56]; // [esp+54h] [ebp-154h] BYREF
  _BYTE v21[56]; // [esp+8Ch] [ebp-11Ch] BYREF
  _BYTE v22[56]; // [esp+C4h] [ebp-E4h] BYREF
  _BYTE v23[56]; // [esp+FCh] [ebp-ACh] BYREF
  _BYTE v24[56]; // [esp+134h] [ebp-74h] BYREF
  _BYTE v25[56]; // [esp+16Ch] [ebp-3Ch] BYREF

  Myptr = _Last._Myptr;
  v5 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
    _First,
    (std::vector<Guild::MemberInfo>::iterator)v5,
    (std::vector<Guild::MemberInfo>::iterator)&_Last._Myptr[-1],
    _Pred);
  v6 = v5 + 1;
  _Plast = v5 + 1;
  if ( _First._Myptr < v5 )
  {
    do
    {
      v7 = (unsigned int)&v5[-1];
      if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v5 - 1, v5) )
        break;
      if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v5, v5 - 1) )
        break;
      --v5;
    }
    while ( (unsigned int)_First._Myptr < v7 );
    Myptr = _Last._Myptr;
  }
  if ( v6 < Myptr )
  {
    do
    {
      if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v6, v5) )
        break;
      if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v5, v6) )
        break;
      ++v6;
    }
    while ( v6 < Myptr );
    _Plast = v6;
  }
  _Glast = v5;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v6 < _Last._Myptr; ++v6 )
      {
        if ( !Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v5, v6) )
        {
          if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v6, v5) )
            break;
          v8 = _Plast++;
          qmemcpy(v24, v8, sizeof(v24));
          qmemcpy(v8, v6, sizeof(Guild::MemberInfo));
          qmemcpy(v6, v24, sizeof(Guild::MemberInfo));
        }
      }
      v9 = _Glast;
      v10 = _Glast == _First._Myptr;
      if ( _Glast > _First._Myptr )
      {
        v11 = _Glast - 1;
        v18 = _Glast - 1;
        do
        {
          if ( !Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v11, v5) )
          {
            if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, v5, v11) )
              break;
            qmemcpy(v19, --v5, sizeof(v19));
            qmemcpy(v5, v18, sizeof(Guild::MemberInfo));
            qmemcpy(v18, v19, sizeof(Guild::MemberInfo));
            v11 = v18;
          }
          v9 = _Glast - 1;
          --v11;
          v12 = _First._Myptr < &_Glast[-1];
          --_Glast;
          v18 = (Guild::MemberInfo *)v11;
        }
        while ( v12 );
        v10 = v9 == _First._Myptr;
      }
      if ( v10 )
        break;
      v14 = v9 - 1;
      _Glast = v14;
      if ( v6 == _Last._Myptr )
      {
        if ( v14 != --v5 )
        {
          qmemcpy(v21, v14, sizeof(v21));
          qmemcpy(v14, v5, sizeof(Guild::MemberInfo));
          qmemcpy(v5, v21, sizeof(Guild::MemberInfo));
        }
        qmemcpy(v23, v5, sizeof(v23));
        qmemcpy(v5, &_Plast[-1], sizeof(Guild::MemberInfo));
        qmemcpy(--_Plast, v23, sizeof(Guild::MemberInfo));
      }
      else
      {
        qmemcpy(v25, v6, sizeof(v25));
        qmemcpy(v6++, v14, sizeof(Guild::MemberInfo));
        qmemcpy(v14, v25, sizeof(Guild::MemberInfo));
      }
    }
    if ( v6 == _Last._Myptr )
      break;
    if ( _Plast != v6 )
    {
      qmemcpy(v22, v5, sizeof(v22));
      qmemcpy(v5, _Plast, sizeof(Guild::MemberInfo));
      qmemcpy(_Plast, v22, sizeof(Guild::MemberInfo));
    }
    ++_Plast;
    v13 = v6;
    qmemcpy(v20, v5, sizeof(v20));
    qmemcpy(v5++, v6++, sizeof(Guild::MemberInfo));
    qmemcpy(v13, v20, sizeof(Guild::MemberInfo));
  }
  v15 = result;
  result->first._Myptr = v5;
  result->second._Myptr = _Plast;
  return v15;
}

//----- (00464920) --------------------------------------------------------
void __thiscall Guild::CGuild::CGuild(
        Guild::CGuild *this,
        unsigned int dwGID,
        unsigned __int8 cInclination,
        char *szName)
{
  GuildRight *p_m_GuildRight; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v6; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v7; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v8; // eax
  _BYTE v9[48]; // [esp+16h] [ebp-3Eh] BYREF
  __int16 v10; // [esp+46h] [ebp-Eh]
  int v11; // [esp+50h] [ebp-4h]

  this->m_dwGID = dwGID;
  this->m_cInclination = cInclination;
  this->__vftable = &Guild::CGuild::`vftable';
  this->m_cLevel = 0;
  p_m_GuildRight = &this->m_GuildRight;
  this->m_dwFame = 0;
  this->m_dwGold = 0;
  memset(&this->m_GuildRight, 0, 0x30u);
  *(_WORD *)&this->m_GuildRight.m_aryRight[48] = 0;
  this->m_GuildRight.m_aryRight[5] = 1;
  this->m_GuildRight.m_aryRight[0] = 1;
  this->m_GuildRight.m_aryRight[1] = 1;
  this->m_GuildRight.m_aryRight[2] = 1;
  this->m_GuildRight.m_aryRight[3] = 1;
  this->m_GuildRight.m_aryRight[4] = 1;
  this->m_GuildRight.m_aryRight[6] = 1;
  this->m_GuildRight.m_aryRight[7] = 1;
  this->m_GuildRight.m_aryRight[8] = 1;
  this->m_GuildRight.m_aryRight[9] = 1;
  this->m_GuildRight.m_aryRight[10] = 1;
  this->m_GuildRight.m_aryRight[11] = 1;
  this->m_GuildRight.m_aryRight[12] = 1;
  this->m_GuildRight.m_aryRight[13] = 1;
  this->m_MemberList._Myfirst = 0;
  this->m_MemberList._Mylast = 0;
  this->m_MemberList._Myend = 0;
  v11 = 0;
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_FriendlyMap);
  this->m_FriendlyMap._Myhead = v6;
  v6->_Isnil = 1;
  this->m_FriendlyMap._Myhead->_Parent = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Myhead->_Left = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Myhead->_Right = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Mysize = 0;
  LOBYTE(v11) = 1;
  v7 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_HostilityMap);
  this->m_HostilityMap._Myhead = v7;
  v7->_Isnil = 1;
  this->m_HostilityMap._Myhead->_Parent = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Myhead->_Left = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Myhead->_Right = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Mysize = 0;
  LOBYTE(v11) = 2;
  v8 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_NeutralityMap);
  this->m_NeutralityMap._Myhead = v8;
  v8->_Isnil = 1;
  this->m_NeutralityMap._Myhead->_Parent = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Myhead->_Left = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Myhead->_Right = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Mysize = 0;
  strncpy(this->m_strName, szName, 0xBu);
  memset(this->m_szMark, 0, 0x1B0u);
  this->m_szMark[432] = 0;
  *(_DWORD *)&this->m_tmLastLogout.Year = 0;
  *(_DWORD *)&this->m_tmLastLogout.Day = 0;
  *(_DWORD *)&this->m_tmLastLogout.Minute = 0;
  this->m_tmLastLogout.MSecond = 0;
  *(_DWORD *)&this->m_tmCheckMember.Year = 0;
  *(_DWORD *)&this->m_tmCheckMember.Day = 0;
  *(_DWORD *)&this->m_tmCheckMember.Minute = 0;
  this->m_tmCheckMember.MSecond = 0;
  *(_DWORD *)&this->m_tmGMLastLogout.Year = 0;
  *(_DWORD *)&this->m_tmGMLastLogout.Day = 0;
  *(_DWORD *)&this->m_tmGMLastLogout.Minute = 0;
  this->m_tmGMLastLogout.MSecond = 0;
  *(_DWORD *)&this->m_tmChangeInclination.Year = 0;
  *(_DWORD *)&this->m_tmChangeInclination.Day = 0;
  *(_DWORD *)&this->m_tmChangeInclination.Minute = 0;
  this->m_tmChangeInclination.MSecond = 0;
  memset(v9, 0, sizeof(v9));
  v10 = 0;
  memset(v9, 1, 14);
  qmemcpy(p_m_GuildRight, v9, 0x30u);
  *(_WORD *)&p_m_GuildRight->m_aryRight[48] = v10;
}
// 4E52E8: using guessed type void *Guild::CGuild::`vftable';

//----- (00464B40) --------------------------------------------------------
void __thiscall Guild::CGuild::CGuild(Guild::CGuild *this, GuildInfoDB *guildInfo)
{
  GuildRight *p_m_GuildRight; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v4; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v6; // eax

  this->__vftable = &Guild::CGuild::`vftable';
  p_m_GuildRight = &this->m_GuildRight;
  memset(&this->m_GuildRight, 0, 0x30u);
  *(_WORD *)&this->m_GuildRight.m_aryRight[48] = 0;
  this->m_GuildRight.m_aryRight[5] = 1;
  this->m_GuildRight.m_aryRight[0] = 1;
  this->m_GuildRight.m_aryRight[1] = 1;
  this->m_GuildRight.m_aryRight[2] = 1;
  this->m_GuildRight.m_aryRight[3] = 1;
  this->m_GuildRight.m_aryRight[4] = 1;
  this->m_GuildRight.m_aryRight[6] = 1;
  this->m_GuildRight.m_aryRight[7] = 1;
  this->m_GuildRight.m_aryRight[8] = 1;
  this->m_GuildRight.m_aryRight[9] = 1;
  this->m_GuildRight.m_aryRight[10] = 1;
  this->m_GuildRight.m_aryRight[11] = 1;
  this->m_GuildRight.m_aryRight[12] = 1;
  this->m_GuildRight.m_aryRight[13] = 1;
  this->m_MemberList._Myfirst = 0;
  this->m_MemberList._Mylast = 0;
  this->m_MemberList._Myend = 0;
  v4 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_FriendlyMap);
  this->m_FriendlyMap._Myhead = v4;
  v4->_Isnil = 1;
  this->m_FriendlyMap._Myhead->_Parent = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Myhead->_Left = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Myhead->_Right = this->m_FriendlyMap._Myhead;
  this->m_FriendlyMap._Mysize = 0;
  v5 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_HostilityMap);
  this->m_HostilityMap._Myhead = v5;
  v5->_Isnil = 1;
  this->m_HostilityMap._Myhead->_Parent = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Myhead->_Left = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Myhead->_Right = this->m_HostilityMap._Myhead;
  this->m_HostilityMap._Mysize = 0;
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *)&this->m_NeutralityMap);
  this->m_NeutralityMap._Myhead = v6;
  v6->_Isnil = 1;
  this->m_NeutralityMap._Myhead->_Parent = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Myhead->_Left = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Myhead->_Right = this->m_NeutralityMap._Myhead;
  this->m_NeutralityMap._Mysize = 0;
  this->m_dwGID = guildInfo->m_dwGID;
  strncpy(this->m_strName, guildInfo->m_strName, 0xBu);
  this->m_cInclination = guildInfo->m_cInclination;
  this->m_cLevel = guildInfo->m_cLevel;
  this->m_dwFame = guildInfo->m_dwFame;
  this->m_dwGold = guildInfo->m_dwGold;
  this->m_tmLastLogout = guildInfo->m_tmLastLogout;
  this->m_tmCheckMember = guildInfo->m_tmCheckMember;
  this->m_tmGMLastLogout = guildInfo->m_tmGMLastLogout;
  this->m_tmChangeInclination = guildInfo->m_tmChangeInclination;
  qmemcpy(this->m_szMark, guildInfo->m_szMark, sizeof(this->m_szMark));
  qmemcpy(p_m_GuildRight, guildInfo->m_szRight, sizeof(GuildRight));
}
// 4E52E8: using guessed type void *Guild::CGuild::`vftable';

//----- (00464D50) --------------------------------------------------------
bool __thiscall Guild::CGuild::SetRelation(
        Guild::CGuild *this,
        unsigned __int8 cType,
        unsigned int dwRelationGID,
        Guild::RelationInfo *Info)
{
  if ( !dwRelationGID )
    return 0;
  if ( cType == 1 )
    return Guild::CGuild::InsertRelation(this, dwRelationGID, Info);
  if ( cType != 2 )
    return 0;
  return Guild::CGuild::ChangeRelation(this, dwRelationGID, (std::map<unsigned long,Guild::RelationInfo> *)Info);
}

//----- (00464D90) --------------------------------------------------------
char __thiscall Guild::CGuild::JoinMember(Guild::CGuild *this, Guild::MemberInfo *memberInfo)
{
  Guild::MemberInfo *Myfirst; // eax
  Guild::MemberInfo *Mylast; // ecx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // [esp-4h] [ebp-Ch]

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    goto LABEL_6;
  do
  {
    if ( memberInfo->m_dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_6:
    std::vector<Guild::MemberInfo>::push_back(&this->m_MemberList, memberInfo);
    m_dwCID = memberInfo->m_dwCID;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
    if ( Character )
      CCharacter::SetGID(Character, this->m_dwGID);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::JoinMember",
      aDWorkRylSource_47,
      1396,
      aGid0x08x_8,
      this->m_dwGID);
    return 0;
  }
}

//----- (00464E20) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (00464EC0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (00464F60) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (00465000) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (004650A0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (00465140) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (004651E0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  int i; // ebx
  Guild::MemberInfo v4; // [esp-3Ch] [ebp-84h] BYREF
  int v5; // [esp-4h] [ebp-4Ch]
  _BYTE v6[56]; // [esp+10h] [ebp-38h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 56 > 1; i -= 56 )
  {
    qmemcpy(v6, (char *)&_First._Myptr[-1] + i, sizeof(v6));
    v5 = _Pred;
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(Guild::MemberInfo));
    qmemcpy(&v4, v6, sizeof(v4));
    std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
      _First,
      0,
      (i - 56) / 56,
      v4,
      v5);
  }
}

//----- (00465280) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned __int8 m_cTitle; // al
  unsigned __int8 v12; // cl
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_cTitle = Myptr->m_MemberListInfo.m_cTitle;
    v12 = v7->m_MemberListInfo.m_cTitle;
    if ( m_cTitle == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_cTitle < v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465370) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned __int8 m_cClass; // al
  unsigned __int8 v12; // cl
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_cClass = Myptr->m_MemberListInfo.m_cClass;
    v12 = v7->m_MemberListInfo.m_cClass;
    if ( m_cClass == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_cClass < v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465460) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned __int8 m_cLevel; // al
  unsigned __int8 v12; // cl
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_cLevel = Myptr->m_MemberListInfo.m_cLevel;
    v12 = v7->m_MemberListInfo.m_cLevel;
    if ( m_cLevel == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_cLevel > v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465550) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned int m_dwFame; // eax
  unsigned int v12; // ecx
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_dwFame = Myptr->m_MemberDetailInfo.m_dwFame;
    v12 = v7->m_MemberDetailInfo.m_dwFame;
    if ( m_dwFame == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_dwFame > v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465640) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // edi
  Guild::MemberInfo *v8; // esi
  int v9; // ebp
  std::vector<Guild::MemberInfo>::iterator *v10; // eax
  Guild::MemberInfo v11; // [esp-3Ch] [ebp-50h] BYREF
  int v12; // [esp-4h] [ebp-18h]
  std::vector<Guild::MemberInfo>::iterator _Mid2; // [esp+10h] [ebp-4h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  for ( _Mid2._Myptr = _First2._Myptr; Myptr != _Last1._Myptr; v7 = _Mid2._Myptr )
  {
    if ( v7 == _Last2._Myptr )
      break;
    qmemcpy(v7, Myptr++, sizeof(Guild::MemberInfo));
    ++_Mid2._Myptr;
  }
  v8 = _First2._Myptr;
  v9 = v7 - _First2._Myptr;
  if ( v9 > 1 )
  {
    v12 = 0;
    v11.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
      _First2,
      (std::vector<Guild::MemberInfo>::iterator)v7,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    if ( Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Pred, Myptr, v8) )
    {
      qmemcpy(&v11, Myptr, sizeof(v11));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        _First2,
        0,
        v9,
        v11,
        _Pred);
      v7 = _Mid2._Myptr;
    }
    v8 = _First2._Myptr;
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
    (std::vector<Guild::MemberInfo>::iterator)v8,
    (std::vector<Guild::MemberInfo>::iterator)v7,
    _Pred);
  v10 = result;
  result->_Myptr = v7;
  return v10;
}

//----- (00465720) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned int m_dwGold; // eax
  unsigned int v12; // ecx
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_dwGold = Myptr->m_MemberDetailInfo.m_dwGold;
    v12 = v7->m_MemberDetailInfo.m_dwGold;
    if ( m_dwGold == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_dwGold > v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465810) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  Guild::MemberInfo *v7; // ebp
  Guild::MemberInfo *v8; // edi
  Guild::MemberInfo *v9; // eax
  Guild::MemberInfo *v10; // edx
  unsigned int m_dwServerID; // eax
  unsigned int v12; // ecx
  bool v13; // al
  std::vector<Guild::MemberInfo>::iterator *v14; // eax
  Guild::MemberInfo v15; // [esp-3Ch] [ebp-4Ch] BYREF
  int v16; // [esp-4h] [ebp-14h]
  std::vector<Guild::MemberInfo>::iterator _First1a; // [esp+18h] [ebp+8h]

  Myptr = _First1._Myptr;
  v7 = _First2._Myptr;
  v8 = _First2._Myptr;
  if ( _First1._Myptr != _Last1._Myptr )
  {
    v9 = _Last2._Myptr;
    do
    {
      if ( v8 == v9 )
        break;
      v10 = _First2._Myptr;
      qmemcpy(v8, Myptr++, sizeof(Guild::MemberInfo));
      _First2._Myptr = v10 + 1;
      v8 = v10 + 1;
    }
    while ( Myptr != _Last1._Myptr );
  }
  _First1a._Myptr = (Guild::MemberInfo *)(v8 - v7);
  if ( (int)_First1a._Myptr > 1 )
  {
    v16 = 0;
    v15.m_MemberDetailInfo.m_dwGold = 0;
    std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
      (std::vector<Guild::MemberInfo>::iterator)v7,
      (std::vector<Guild::MemberInfo>::iterator)v8,
      _Pred);
  }
  for ( ; Myptr != _Last1._Myptr; ++Myptr )
  {
    m_dwServerID = Myptr->m_dwServerID;
    v12 = v7->m_dwServerID;
    if ( m_dwServerID == v12 )
      v13 = Guild::CompareMemberName::operator()((Guild::CompareMemberName *)&_Last1, Myptr, v7);
    else
      v13 = m_dwServerID < v12;
    if ( v13 )
    {
      qmemcpy(&v15, Myptr, sizeof(v15));
      std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
        (std::vector<Guild::MemberInfo>::iterator)v7,
        0,
        (int)_First1a._Myptr,
        v15,
        _Pred);
      v8 = _First2._Myptr;
    }
  }
  std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(
    (std::vector<Guild::MemberInfo>::iterator)v7,
    (std::vector<Guild::MemberInfo>::iterator)v8,
    _Pred);
  v14 = result;
  result->_Myptr = v8;
  return v14;
}

//----- (00465900) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<Guild::MemberInfo>::iterator,int,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Ideal,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ebx
  int v5; // ebp
  Guild::MemberInfo *v6; // edi
  int v7; // eax
  std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Pred;
  v6 = _Last._Myptr;
  v7 = _Last._Myptr - _First._Myptr;
  if ( v7 <= 32 )
  {
LABEL_7:
    if ( v7 > 1 )
      std::_Insertion_sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        (std::vector<Guild::MemberInfo>::iterator)Myptr,
        (std::vector<Guild::MemberInfo>::iterator)v6,
        v5);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        &_Mid,
        (std::vector<Guild::MemberInfo>::iterator)Myptr,
        (std::vector<Guild::MemberInfo>::iterator)v6,
        v5);
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      v5 = _Pred;
      if ( _Mid.first._Myptr - Myptr >= v6 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<Guild::MemberInfo>::iterator,int,Guild::CompareMemberName>(
          _Mid.second,
          (std::vector<Guild::MemberInfo>::iterator)v6,
          _Ideal,
          (Guild::CompareMemberName)_Pred);
        v6 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<Guild::MemberInfo>::iterator,int,Guild::CompareMemberName>(
          (std::vector<Guild::MemberInfo>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          (Guild::CompareMemberName)_Pred);
        Myptr = _Mid.second._Myptr;
      }
      v7 = v6 - Myptr;
      if ( v7 <= 32 )
        goto LABEL_7;
    }
    if ( v6 - Myptr > 1 )
      std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
        (std::vector<Guild::MemberInfo>::iterator)Myptr,
        (std::vector<Guild::MemberInfo>::iterator)v6,
        v5);
    std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
      (std::vector<Guild::MemberInfo>::iterator)Myptr,
      (std::vector<Guild::MemberInfo>::iterator)v6,
      v5);
  }
}
// 4659FB: conditional instruction was optimized away because eax.4>=21

//----- (00465A40) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465A90) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465AE0) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465B30) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465B80) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465BD0) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465C20) --------------------------------------------------------
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(
        std::vector<Guild::MemberInfo>::iterator *result,
        std::vector<Guild::MemberInfo>::iterator _First1,
        std::vector<Guild::MemberInfo>::iterator _Last1,
        std::vector<Guild::MemberInfo>::iterator _First2,
        std::vector<Guild::MemberInfo>::iterator _Last2,
        int _Pred)
{
  Guild::MemberInfo *Myptr; // ecx
  std::vector<Guild::MemberInfo>::iterator *v7; // eax

  if ( _First1._Myptr == _Last1._Myptr || _First2._Myptr == _Last2._Myptr )
  {
    v7 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(
              &_First1,
              _First1,
              _Last1,
              _First2,
              _Last2,
              _Pred)->_Myptr;
    v7 = result;
    result->_Myptr = Myptr;
  }
  return v7;
}

//----- (00465C70) --------------------------------------------------------
void __cdecl std::sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Last,
        int _Pred)
{
  std::_Sort<std::vector<Guild::MemberInfo>::iterator,int,Guild::CompareMemberName>(
    _First,
    _Last,
    _Last._Myptr - _First._Myptr,
    _Pred);
}

//----- (00465CB0) --------------------------------------------------------
unsigned __int8 __thiscall Guild::CGuild::SendMemberList(
        Guild::CGuild *this,
        CCharacter *lpCharacter,
        unsigned __int8 cSortCmd,
        unsigned __int8 cPage)
{
  CPerformanceCheck *Instance; // eax
  unsigned __int8 v6; // bl
  Guild::MemberInfo *Myfirst; // eax
  unsigned __int8 v8; // cl
  unsigned __int8 *p_cIndex; // eax
  Guild::MemberInfo *Mylast; // eax
  Guild::MemberInfo *v12; // ebx
  Guild::MemberInfo *v13; // ebp
  Guild::MemberInfo *v14; // eax
  Guild::MemberInfo *v15; // ebp
  Guild::MemberInfo *v16; // eax
  Guild::MemberInfo *v17; // ebp
  Guild::MemberInfo *v18; // eax
  Guild::MemberInfo *v19; // ebp
  Guild::MemberInfo *v20; // eax
  Guild::MemberInfo *v21; // ebp
  Guild::MemberInfo *v22; // eax
  Guild::MemberInfo *v23; // ebp
  Guild::MemberInfo *v24; // eax
  Guild::MemberInfo *v25; // ebp
  Guild::MemberInfo *i; // ebx
  Guild::MemberInfo *v27; // esi
  unsigned int m_dwCID; // ecx
  unsigned int v29; // esi
  char *v30; // ebp
  unsigned int m_dwServerID; // edx
  unsigned int m_dwGold; // ecx
  unsigned __int8 m_cTitle; // al
  unsigned __int8 v34; // al
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // ecx
  unsigned __int8 cIndex; // [esp+16h] [ebp-1FEh] BYREF
  unsigned __int8 cStartOfPage; // [esp+17h] [ebp-1FDh]
  unsigned __int64 result; // [esp+18h] [ebp-1FCh] BYREF
  char v40; // [esp+23h] [ebp-1F1h] BYREF
  std::vector<Guild::MemberInfo> sortVector; // [esp+24h] [ebp-1F0h] BYREF
  CPerformanceInstrument functionInstrument; // [esp+34h] [ebp-1E0h] BYREF
  CAutoInstrument autofunctionInstrument; // [esp+4Ch] [ebp-1C8h]
  Guild::MemberInfo memberInfo; // [esp+50h] [ebp-1C4h] BYREF
  char szBuffer[380]; // [esp+88h] [ebp-18Ch] BYREF
  int v46; // [esp+210h] [ebp-4h]

  functionInstrument.m_szfunctionName = "Guild::CGuild::SendMemberList";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "Guild::CGuild::SendMemberList", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  result = __rdtsc();
  functionInstrument.m_startTime.QuadPart = result;
  v6 = 10 * cPage;
  Myfirst = this->m_MemberList._Myfirst;
  v46 = 0;
  cStartOfPage = 10 * cPage;
  if ( Myfirst )
  {
    cIndex = this->m_MemberList._Mylast - Myfirst;
    v8 = cIndex;
  }
  else
  {
    v8 = 0;
    cIndex = 0;
  }
  if ( v8 >= v6 )
  {
    v40 = v6 + 10;
    p_cIndex = (unsigned __int8 *)&v40;
    if ( (unsigned __int8)(v6 + 10) >= v8 )
      p_cIndex = &cIndex;
    std::vector<Guild::MemberInfo>::vector<Guild::MemberInfo>(&sortVector, *p_cIndex);
    LOBYTE(v46) = 1;
    switch ( cSortCmd )
    {
      case 0u:
        Mylast = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v13 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v13,
          (std::vector<Guild::MemberInfo>::iterator)Mylast,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 1u:
        v14 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v15 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v15,
          (std::vector<Guild::MemberInfo>::iterator)v14,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 2u:
        v16 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v17 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v17,
          (std::vector<Guild::MemberInfo>::iterator)v16,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 3u:
        v18 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v19 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v19,
          (std::vector<Guild::MemberInfo>::iterator)v18,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 4u:
        v20 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v21 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v21,
          (std::vector<Guild::MemberInfo>::iterator)v20,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 5u:
        v22 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v23 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v23,
          (std::vector<Guild::MemberInfo>::iterator)v22,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 6u:
        v24 = this->m_MemberList._Mylast;
        v12 = sortVector._Myfirst;
        v25 = this->m_MemberList._Myfirst;
        LOBYTE(result) = 0;
        std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(
          (std::vector<Guild::MemberInfo>::iterator *)&result,
          (std::vector<Guild::MemberInfo>::iterator)v25,
          (std::vector<Guild::MemberInfo>::iterator)v24,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        break;
      case 7u:
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&sortVector);
        for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
        {
          qmemcpy(&memberInfo, i, sizeof(memberInfo));
          if ( memberInfo.m_MemberListInfo.m_cTitle == 5 )
            std::vector<Guild::MemberInfo>::push_back(&sortVector, &memberInfo);
        }
        v27 = sortVector._Mylast;
        v12 = sortVector._Myfirst;
        LOBYTE(result) = 0;
        std::sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Myfirst,
          (std::vector<Guild::MemberInfo>::iterator)sortVector._Mylast,
          result);
        if ( v12 )
          cIndex = v27 - v12;
        else
          cIndex = 0;
        break;
      default:
        v12 = sortVector._Myfirst;
        break;
    }
    m_dwCID = lpCharacter->m_dwCID;
    szBuffer[17] = cPage;
    v29 = cStartOfPage;
    *(_DWORD *)&szBuffer[12] = m_dwCID;
    LOBYTE(m_dwCID) = cIndex;
    cIndex = cStartOfPage;
    szBuffer[16] = cSortCmd;
    szBuffer[18] = m_dwCID;
    szBuffer[19] = 0;
    LODWORD(result) = cStartOfPage + 10;
    if ( cStartOfPage < (int)result )
    {
      v30 = &szBuffer[48];
      do
      {
        if ( !v12 )
          break;
        if ( v29 >= sortVector._Mylast - v12 )
          break;
        qmemcpy(&memberInfo, &v12[v29], sizeof(memberInfo));
        *((_DWORD *)v30 - 7) = memberInfo.m_dwCID;
        strncpy(v30 - 24, memberInfo.m_strName, 0x10u);
        m_dwServerID = memberInfo.m_dwServerID;
        m_dwGold = memberInfo.m_MemberDetailInfo.m_dwGold;
        *((_DWORD *)v30 - 2) = memberInfo.m_MemberDetailInfo.m_dwFame;
        m_cTitle = memberInfo.m_MemberListInfo.m_cTitle;
        *(_DWORD *)v30 = m_dwServerID;
        LOBYTE(m_dwServerID) = memberInfo.m_MemberListInfo.m_cRank;
        v30[5] = m_cTitle;
        v34 = cIndex;
        *((_DWORD *)v30 - 1) = m_dwGold;
        LOBYTE(m_dwGold) = memberInfo.m_MemberListInfo.m_cLevel;
        v30[4] = m_dwServerID;
        LOBYTE(m_dwServerID) = memberInfo.m_MemberListInfo.m_cClass;
        ++v34;
        v30[6] = m_dwGold;
        v30[7] = m_dwServerID;
        v29 = v34;
        cIndex = v34;
        v30 += 36;
        ++szBuffer[19];
      }
      while ( v34 < (int)result );
    }
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
      LOWORD(m_lpGameClientDispatch) = (unsigned __int8)szBuffer[19];
      cStartOfPage = CSendStream::WrapCompress(
                       p_m_SendStream,
                       szBuffer,
                       (char *)(36 * (_DWORD)m_lpGameClientDispatch + 20),
                       0x90u,
                       0,
                       0);
      if ( v12 )
        operator delete(v12);
      v46 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return cStartOfPage;
    }
    else
    {
      if ( v12 )
        operator delete(v12);
      v46 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return 1;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SendMemberList",
      aDWorkRylSource_47,
      1770,
      aCid0x08x_309,
      lpCharacter->m_dwCID,
      v8,
      cPage);
    v46 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
}

//----- (00466210) --------------------------------------------------------
bool __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::empty(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // eax

  Myfirst = this->_Myfirst;
  return !Myfirst || this->_Mylast - Myfirst == 0;
}

//----- (00466250) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (00466270) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (00466290) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Parent; // eax

  Ptr = this->_Ptr;
  if ( this->_Ptr->_Isnil )
  {
    this->_Ptr = Ptr->_Right;
  }
  else
  {
    Left = Ptr->_Left;
    if ( Ptr->_Left->_Isnil )
    {
      Parent = Ptr->_Parent;
      if ( !Parent->_Isnil )
      {
        do
        {
          if ( this->_Ptr != Parent->_Left )
            break;
          this->_Ptr = Parent;
          Parent = Parent->_Parent;
        }
        while ( !Parent->_Isnil );
        if ( !Parent->_Isnil )
          this->_Ptr = Parent;
      }
    }
    else
    {
      for ( i = Left->_Right; !i->_Isnil; i = i->_Right )
        Left = i;
      this->_Ptr = Left;
    }
  }
}

//----- (004662F0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // eax

  Ptr = this->_Ptr;
  if ( this->_Ptr->_Isnil )
  {
    this->_Ptr = Ptr->_Right;
  }
  else
  {
    Left = Ptr->_Left;
    if ( Ptr->_Left->_Isnil )
    {
      Parent = Ptr->_Parent;
      if ( !Parent->_Isnil )
      {
        do
        {
          if ( this->_Ptr != Parent->_Left )
            break;
          this->_Ptr = Parent;
          Parent = Parent->_Parent;
        }
        while ( !Parent->_Isnil );
        if ( !Parent->_Isnil )
          this->_Ptr = Parent;
      }
    }
    else
    {
      for ( i = Left->_Right; !i->_Isnil; i = i->_Right )
        Left = i;
      this->_Ptr = Left;
    }
  }
}

//----- (00466350) --------------------------------------------------------
void __cdecl std::fill<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo>>(
        std::pair<unsigned long,Guild::RelationInfo> *_First,
        std::pair<unsigned long,Guild::RelationInfo> *_Last,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  std::pair<unsigned long,Guild::RelationInfo> *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (004663A0) --------------------------------------------------------
void __cdecl std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
        std::pair<unsigned long,unsigned long> *_First,
        std::pair<unsigned long,unsigned long> *_Last,
        const std::pair<unsigned long,unsigned long> *_Val)
{
  std::pair<unsigned long,unsigned long> *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (004663D0) --------------------------------------------------------
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Copy_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(
        std::pair<unsigned long,Guild::RelationInfo> *_First,
        std::pair<unsigned long,Guild::RelationInfo> *_Last,
        std::pair<unsigned long,Guild::RelationInfo> *_Dest)
{
  std::pair<unsigned long,Guild::RelationInfo> *v3; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *result; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v5; // edi
  unsigned int v6; // esi

  v3 = _First;
  for ( result = _Dest; v3 != _Last; *(unsigned int *)((char *)&v5->second.m_WaitTime.MSecond + 2) = v6 )
  {
    v5 = result;
    result->first = v3->first;
    *(_DWORD *)&result->second.m_cRelation = *(_DWORD *)&v3->second.m_cRelation;
    *(_DWORD *)&result->second.m_WaitTime.Month = *(_DWORD *)&v3->second.m_WaitTime.Month;
    *(_DWORD *)&result->second.m_WaitTime.Hour = *(_DWORD *)&v3->second.m_WaitTime.Hour;
    *(_DWORD *)&result->second.m_WaitTime.Second = *(_DWORD *)&v3->second.m_WaitTime.Second;
    v6 = *(unsigned int *)((char *)&v3->second.m_WaitTime.MSecond + 2);
    ++v3;
    ++result;
  }
  return result;
}

//----- (00466420) --------------------------------------------------------
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Copy_backward_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(
        std::pair<unsigned long,Guild::RelationInfo> *_First,
        std::pair<unsigned long,Guild::RelationInfo> *_Last,
        std::pair<unsigned long,Guild::RelationInfo> *_Dest)
{
  std::pair<unsigned long,Guild::RelationInfo> *v3; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *result; // eax

  v3 = _Last;
  for ( result = _Dest;
        v3 != _First;
        *(unsigned int *)((char *)&result->second.m_WaitTime.MSecond + 2) = *(unsigned int *)((char *)&v3->second.m_WaitTime.MSecond
                                                                                            + 2) )
  {
    --v3;
    --result;
    result->first = v3->first;
    *(_DWORD *)&result->second.m_cRelation = *(_DWORD *)&v3->second.m_cRelation;
    *(_DWORD *)&result->second.m_WaitTime.Month = *(_DWORD *)&v3->second.m_WaitTime.Month;
    *(_DWORD *)&result->second.m_WaitTime.Hour = *(_DWORD *)&v3->second.m_WaitTime.Hour;
    *(_DWORD *)&result->second.m_WaitTime.Second = *(_DWORD *)&v3->second.m_WaitTime.Second;
  }
  return result;
}

//----- (00466470) --------------------------------------------------------
void __cdecl std::swap<Guild::RelationInfo>(Guild::RelationInfo *_Left, Guild::RelationInfo *_Right)
{
  int v2; // edx
  int v3; // esi
  int v4; // edi
  int _Tmp_12; // [esp+1Ch] [ebp-8h]
  __int16 _Tmp_16; // [esp+20h] [ebp-4h]

  v2 = *(_DWORD *)&_Left->m_cRelation;
  v3 = *(_DWORD *)&_Left->m_WaitTime.Month;
  v4 = *(_DWORD *)&_Left->m_WaitTime.Hour;
  _Tmp_12 = *(_DWORD *)&_Left->m_WaitTime.Second;
  _Tmp_16 = HIWORD(_Left->m_WaitTime.MSecond);
  *_Left = *_Right;
  *(_DWORD *)&_Right->m_cRelation = v2;
  *(_DWORD *)&_Right->m_WaitTime.Month = v3;
  *(_DWORD *)&_Right->m_WaitTime.Hour = v4;
  *(_DWORD *)&_Right->m_WaitTime.Second = _Tmp_12;
  HIWORD(_Right->m_WaitTime.MSecond) = _Tmp_16;
}

//----- (004664E0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (00466540) --------------------------------------------------------
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
        std::pair<unsigned long,Guild::RelationInfo> *_First,
        std::pair<unsigned long,Guild::RelationInfo> *_Last,
        std::pair<unsigned long,Guild::RelationInfo> *_Dest)
{
  std::pair<unsigned long,Guild::RelationInfo> *v3; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
    {
      result->first = v3->first;
      *(_DWORD *)&result->second.m_cRelation = *(_DWORD *)&v3->second.m_cRelation;
      *(_DWORD *)&result->second.m_WaitTime.Month = *(_DWORD *)&v3->second.m_WaitTime.Month;
      *(_DWORD *)&result->second.m_WaitTime.Hour = *(_DWORD *)&v3->second.m_WaitTime.Hour;
      *(_DWORD *)&result->second.m_WaitTime.Second = *(_DWORD *)&v3->second.m_WaitTime.Second;
      *(unsigned int *)((char *)&result->second.m_WaitTime.MSecond + 2) = *(unsigned int *)((char *)&v3->second.m_WaitTime.MSecond
                                                                                          + 2);
    }
    ++v3;
  }
  return result;
}

//----- (00466590) --------------------------------------------------------
std::pair<unsigned long,unsigned long> *__cdecl std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        std::pair<unsigned long,unsigned long> *_First,
        std::pair<unsigned long,unsigned long> *_Last,
        std::pair<unsigned long,unsigned long> *_Dest)
{
  std::pair<unsigned long,unsigned long> *v3; // ecx
  std::pair<unsigned long,unsigned long> *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
    {
      result->first = v3->first;
      result->second = v3->second;
    }
    ++v3;
  }
  return result;
}

//----- (004665C0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First,
        int _Hole,
        int _Top,
        std::pair<unsigned long,Guild::CGuild *> _Val)
{
  int v4; // esi
  int i; // eax

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    if ( _First._Myptr[i].second->m_dwFame <= _Val.second->m_dwFame )
      break;
    _First._Myptr[v4].first = _First._Myptr[i].first;
    _First._Myptr[v4].second = _First._Myptr[i].second;
    v4 = i;
  }
  _First._Myptr[v4] = _Val;
}

//----- (00466620) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        int _Hole,
        int _Top,
        std::pair<unsigned long const ,Guild::RelationInfo> _Val)
{
  int v4; // esi
  int i; // eax
  unsigned int *p_first; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *v7; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v8; // eax
  unsigned int v9; // [esp+24h] [ebp-4h]

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    p_first = &_First._Myptr[i].first;
    if ( (unsigned __int8)BYTE1(p_first[1]) >= _Val.second.m_cState )
      break;
    _First._Myptr[v4].first = *p_first;
    v7 = &_First._Myptr[v4];
    *(_DWORD *)&v7->second.m_cRelation = p_first[1];
    *(_DWORD *)&v7->second.m_WaitTime.Month = p_first[2];
    *(_DWORD *)&v7->second.m_WaitTime.Hour = p_first[3];
    *(_DWORD *)&v7->second.m_WaitTime.Second = p_first[4];
    v4 = i;
    *(unsigned int *)((char *)&v7->second.m_WaitTime.MSecond + 2) = p_first[5];
  }
  LOWORD(v9) = HIWORD(_Val.second.m_WaitTime.MSecond);
  v8 = &_First._Myptr[v4];
  v8->first = _Val.first;
  *(_DWORD *)&v8->second.m_cRelation = *(_DWORD *)&_Val.second.m_cRelation;
  *(_DWORD *)&v8->second.m_WaitTime.Month = *(_DWORD *)&_Val.second.m_WaitTime.Month;
  *(_DWORD *)&v8->second.m_WaitTime.Hour = *(_DWORD *)&_Val.second.m_WaitTime.Hour;
  *(_DWORD *)&v8->second.m_WaitTime.Second = *(_DWORD *)&_Val.second.m_WaitTime.Second;
  *(unsigned int *)((char *)&v8->second.m_WaitTime.MSecond + 2) = v9;
}
// 4666ED: variable 'v9' is possibly undefined

//----- (00466700) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        int _Hole,
        int _Top,
        std::pair<unsigned long,Guild::RelationInfo> _Val)
{
  int v4; // esi
  int i; // eax
  unsigned int *p_first; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *v7; // edx

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    p_first = &_First._Myptr[i].first;
    if ( (unsigned __int8)BYTE1(p_first[1]) >= _Val.second.m_cState )
      break;
    _First._Myptr[v4].first = *p_first;
    v7 = &_First._Myptr[v4];
    *(_DWORD *)&v7->second.m_cRelation = p_first[1];
    *(_DWORD *)&v7->second.m_WaitTime.Month = p_first[2];
    *(_DWORD *)&v7->second.m_WaitTime.Hour = p_first[3];
    *(_DWORD *)&v7->second.m_WaitTime.Second = p_first[4];
    v4 = i;
    *(unsigned int *)((char *)&v7->second.m_WaitTime.MSecond + 2) = p_first[5];
  }
  _First._Myptr[v4] = _Val;
}

//----- (00466800) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myptr; // ebp
  int v4; // edi
  int v5; // eax
  int v6; // esi
  int v7; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v8; // edx
  unsigned int *p_first; // ebx
  unsigned int v10; // esi
  std::pair<unsigned long,Guild::RelationInfo> *v11; // ecx
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *v12; // eax
  char *v13; // esi
  int v14; // eax
  std::pair<unsigned long,Guild::RelationInfo> **v15; // eax
  char *v16; // [esp+10h] [ebp-24h] BYREF
  char *v17; // [esp+14h] [ebp-20h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> *v18; // [esp+18h] [ebp-1Ch] BYREF
  std::pair<unsigned long,Guild::RelationInfo> _Holeval; // [esp+1Ch] [ebp-18h]

  Myptr = _Last._Myptr;
  v4 = _Mid._Myptr - _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  v6 = v4;
  if ( v4 )
  {
    do
    {
      v7 = v5 % v6;
      v5 = v6;
      v6 = v7;
    }
    while ( v7 );
  }
  if ( v5 < _Last._Myptr - _First._Myptr && v5 > 0 )
  {
    v8 = (std::pair<unsigned long,Guild::RelationInfo> *)(24 * v4);
    _Mid._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)(24 * v4);
    p_first = &_First._Myptr[v5].first;
    _Last._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)v5;
    do
    {
      _Holeval.first = *p_first;
      *(_DWORD *)&_Holeval.second.m_cRelation = p_first[1];
      *(_DWORD *)&_Holeval.second.m_WaitTime.Month = p_first[2];
      *(_DWORD *)&_Holeval.second.m_WaitTime.Hour = p_first[3];
      v10 = p_first[4];
      *(unsigned int *)((char *)&_Holeval.second.m_WaitTime.MSecond + 2) = p_first[5];
      v11 = (std::pair<unsigned long,Guild::RelationInfo> *)p_first;
      *(_DWORD *)&_Holeval.second.m_WaitTime.Second = v10;
      if ( (std::pair<unsigned long,Guild::RelationInfo> *)((char *)v8 + (_DWORD)p_first) == Myptr )
      {
        v12 = &_First;
      }
      else
      {
        v16 = (char *)v8 + (_DWORD)p_first;
        v12 = (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *)&v16;
      }
      v13 = (char *)v12->_Myptr;
      if ( (unsigned int *)v12->_Myptr != p_first )
      {
        do
        {
          v11->first = *(_DWORD *)v13;
          *(_DWORD *)&v11->second.m_cRelation = *((_DWORD *)v13 + 1);
          *(_DWORD *)&v11->second.m_WaitTime.Month = *((_DWORD *)v13 + 2);
          *(_DWORD *)&v11->second.m_WaitTime.Hour = *((_DWORD *)v13 + 3);
          *(_DWORD *)&v11->second.m_WaitTime.Second = *((_DWORD *)v13 + 4);
          *(unsigned int *)((char *)&v11->second.m_WaitTime.MSecond + 2) = *((_DWORD *)v13 + 5);
          v14 = ((char *)Myptr - v13) / 24;
          v11 = (std::pair<unsigned long,Guild::RelationInfo> *)v13;
          if ( v4 >= v14 )
          {
            v18 = &_First._Myptr[v4 - v14];
            v15 = &v18;
          }
          else
          {
            v17 = &v13[(unsigned int)_Mid._Myptr];
            v15 = (std::pair<unsigned long,Guild::RelationInfo> **)&v17;
          }
          v13 = (char *)*v15;
        }
        while ( *v15 != (std::pair<unsigned long,Guild::RelationInfo> *)p_first );
        v8 = _Mid._Myptr;
      }
      *v11 = _Holeval;
      p_first -= 6;
      --_Last._Myptr;
    }
    while ( _Last._Myptr );
  }
}

//----- (00466980) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (004669C0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)operator new((tagHeader *)0x30);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (00466A00) --------------------------------------------------------
Guild::FnDeleteSecond __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,Guild::FnDeleteSecond>(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last,
        Guild::FnDeleteSecond _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *i; // edi
  Guild::CGuild *second; // esi

  Ptr = _First._Ptr;
  for ( i = _Last._Ptr; _First._Ptr != i; Ptr = _First._Ptr )
  {
    second = Ptr->_Myval.second;
    if ( second )
    {
      Guild::CGuild::~CGuild(Ptr->_Myval.second);
      operator delete(second);
    }
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
  }
  return _Func;
}

//----- (00466A40) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<std::pair<unsigned long,Guild::RelationInfo> *,unsigned int,std::pair<unsigned long,Guild::RelationInfo>,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
        std::pair<unsigned long,Guild::RelationInfo> *_First,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  unsigned int v3; // ecx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (00466A90) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        int _Hole,
        int _Bottom,
        std::pair<unsigned long const ,Guild::RelationInfo> _Val)
{
  int v4; // edx
  int v5; // eax
  bool i; // zf
  unsigned int *p_first; // ecx
  int v8; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v9; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v10; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *v11; // eax

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr[v5].second.m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr[v5 - 1].second.m_cRelation) )
      --v5;
    p_first = &_First._Myptr[v5].first;
    v8 = v4;
    _First._Myptr[v8].first = *p_first;
    v9 = &_First._Myptr[v8];
    *(_DWORD *)&v9->second.m_cRelation = p_first[1];
    *(_DWORD *)&v9->second.m_WaitTime.Month = p_first[2];
    *(_DWORD *)&v9->second.m_WaitTime.Hour = p_first[3];
    *(_DWORD *)&v9->second.m_WaitTime.Second = p_first[4];
    *(unsigned int *)((char *)&v9->second.m_WaitTime.MSecond + 2) = p_first[5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v10 = &_First._Myptr[v4];
    v10->first = _First._Myptr[_Bottom - 1].first;
    v11 = &_First._Myptr[_Bottom - 1];
    *(_DWORD *)&v10->second.m_cRelation = *(_DWORD *)&_First._Myptr[_Bottom - 1].second.m_cRelation;
    *(_DWORD *)&v10->second.m_WaitTime.Month = *(_DWORD *)&v11->second.m_WaitTime.Month;
    *(_DWORD *)&v10->second.m_WaitTime.Hour = *(_DWORD *)&v11->second.m_WaitTime.Hour;
    *(_DWORD *)&v10->second.m_WaitTime.Second = *(_DWORD *)&v11->second.m_WaitTime.Second;
    *(unsigned int *)((char *)&v10->second.m_WaitTime.MSecond + 2) = *(unsigned int *)((char *)&v11->second.m_WaitTime.MSecond
                                                                                     + 2);
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
    _First,
    v4,
    _Hole,
    _Val);
}

//----- (00466BE0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First,
        int _Hole,
        int _Bottom,
        std::pair<unsigned long,Guild::CGuild *> _Val)
{
  int v4; // edx
  int v5; // eax
  bool i; // zf

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5].second->m_dwFame > _First._Myptr[v5 - 1].second->m_dwFame )
      --v5;
    _First._Myptr[v4].first = _First._Myptr[v5].first;
    _First._Myptr[v4].second = _First._Myptr[v5].second;
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4].first = _First._Myptr[_Bottom - 1].first;
    _First._Myptr[v4].second = _First._Myptr[_Bottom - 1].second;
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
    _First,
    v4,
    _Hole,
    _Val);
}

//----- (00466C70) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        int _Hole,
        int _Bottom,
        std::pair<unsigned long,Guild::RelationInfo> _Val)
{
  int v4; // edx
  int v5; // eax
  bool i; // zf
  unsigned int *p_first; // ecx
  int v8; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v9; // edx
  std::pair<unsigned long,Guild::RelationInfo> *v10; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *v11; // eax

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr[v5].second.m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr[v5 - 1].second.m_cRelation) )
      --v5;
    p_first = &_First._Myptr[v5].first;
    v8 = v4;
    _First._Myptr[v8].first = *p_first;
    v9 = &_First._Myptr[v8];
    *(_DWORD *)&v9->second.m_cRelation = p_first[1];
    *(_DWORD *)&v9->second.m_WaitTime.Month = p_first[2];
    *(_DWORD *)&v9->second.m_WaitTime.Hour = p_first[3];
    *(_DWORD *)&v9->second.m_WaitTime.Second = p_first[4];
    *(unsigned int *)((char *)&v9->second.m_WaitTime.MSecond + 2) = p_first[5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v10 = &_First._Myptr[v4];
    v10->first = _First._Myptr[_Bottom - 1].first;
    v11 = &_First._Myptr[_Bottom - 1];
    *(_DWORD *)&v10->second.m_cRelation = *(_DWORD *)&_First._Myptr[_Bottom - 1].second.m_cRelation;
    *(_DWORD *)&v10->second.m_WaitTime.Month = *(_DWORD *)&v11->second.m_WaitTime.Month;
    *(_DWORD *)&v10->second.m_WaitTime.Hour = *(_DWORD *)&v11->second.m_WaitTime.Hour;
    *(_DWORD *)&v10->second.m_WaitTime.Second = *(_DWORD *)&v11->second.m_WaitTime.Second;
    *(unsigned int *)((char *)&v10->second.m_WaitTime.MSecond + 2) = *(unsigned int *)((char *)&v11->second.m_WaitTime.MSecond
                                                                                     + 2);
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
    _First,
    v4,
    _Hole,
    _Val);
}

//----- (00466DC0) --------------------------------------------------------
void __cdecl std::_Pop_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Dest,
        std::pair<unsigned long,Guild::RelationInfo> _Val)
{
  _Dest._Myptr->first = _First._Myptr->first;
  *(_DWORD *)&_Dest._Myptr->second.m_cRelation = *(_DWORD *)&_First._Myptr->second.m_cRelation;
  *(_DWORD *)&_Dest._Myptr->second.m_WaitTime.Month = *(_DWORD *)&_First._Myptr->second.m_WaitTime.Month;
  *(_DWORD *)&_Dest._Myptr->second.m_WaitTime.Hour = *(_DWORD *)&_First._Myptr->second.m_WaitTime.Hour;
  *(_DWORD *)&_Dest._Myptr->second.m_WaitTime.Second = *(_DWORD *)&_First._Myptr->second.m_WaitTime.Second;
  *(unsigned int *)((char *)&_Dest._Myptr->second.m_WaitTime.MSecond + 2) = *(unsigned int *)((char *)&_First._Myptr->second.m_WaitTime.MSecond
                                                                                            + 2);
  std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
    _First,
    0,
    _Last._Myptr - _First._Myptr,
    _Val);
}

//----- (00466E50) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lbound(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        const std::string *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ebp
  unsigned int Mysize; // eax
  std::string::_Bxty *p_Bx; // ecx
  const char *Ptr; // edi
  unsigned int v7; // ebx
  int v8; // ecx
  const char *v9; // esi
  int v10; // edx
  bool v11; // sf
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode; // [esp+4h] [ebp-8h]
  unsigned int Myres; // [esp+8h] [ebp-4h]
  std::string::_Bxty *_Keyvala; // [esp+10h] [ebp+4h]

  result = this->_Myhead;
  Parent = result->_Parent;
  _Wherenode = result;
  if ( !Parent->_Isnil )
  {
    Mysize = _Keyval->_Mysize;
    p_Bx = &_Keyval->_Bx;
    Myres = _Keyval->_Myres;
    for ( _Keyvala = &_Keyval->_Bx; ; p_Bx = _Keyvala )
    {
      if ( Myres < 0x10 )
        Ptr = (const char *)p_Bx;
      else
        Ptr = p_Bx->_Ptr;
      v7 = Parent->_Myval.first._Mysize;
      if ( !v7 )
        goto LABEL_14;
      v8 = Parent->_Myval.first._Mysize;
      if ( v7 >= Mysize )
        v8 = Mysize;
      v9 = Parent->_Myval.first._Myres < 0x10 ? (const char *)&Parent->_Myval.first._Bx : Parent->_Myval.first._Bx._Ptr;
      v10 = memcmp(v9, Ptr, v8);
      v11 = v10 < 0;
      if ( !v10 )
      {
LABEL_14:
        if ( v7 < Mysize )
          goto LABEL_17;
        v11 = 0;
      }
      if ( v11 )
      {
LABEL_17:
        Parent = Parent->_Right;
        goto LABEL_19;
      }
      _Wherenode = Parent;
      Parent = Parent->_Left;
LABEL_19:
      if ( Parent->_Isnil )
        return _Wherenode;
    }
  }
  return result;
}

//----- (00466F10) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Ubound(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        const std::string *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Parent; // ebp
  const std::string *v4; // esi
  unsigned int i; // ecx
  unsigned int Mysize; // eax
  const char *Buf; // edi
  unsigned int v8; // ebx
  int v9; // ecx
  const char *v10; // esi
  int v11; // edx
  bool v12; // sf
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Wherenode; // [esp+4h] [ebp-Ch]

  result = this->_Myhead;
  Parent = result->_Parent;
  _Wherenode = result;
  if ( !Parent->_Isnil )
  {
    v4 = _Keyval;
    for ( i = _Keyval->_Mysize; ; i = _Keyval->_Mysize )
    {
      Mysize = Parent->_Myval.first._Mysize;
      if ( Parent->_Myval.first._Myres < 0x10 )
        Buf = Parent->_Myval.first._Bx._Buf;
      else
        Buf = Parent->_Myval.first._Bx._Ptr;
      v8 = _Keyval->_Mysize;
      if ( i < v8 )
        v8 = i;
      if ( !v8 )
        goto LABEL_16;
      v9 = v8;
      if ( v8 >= Mysize )
        v9 = Parent->_Myval.first._Mysize;
      v10 = v4->_Myres < 0x10 ? (const char *)&v4->_Bx : v4->_Bx._Ptr;
      v11 = memcmp(v10, Buf, v9);
      v12 = v11 < 0;
      v4 = _Keyval;
      if ( !v11 )
      {
LABEL_16:
        if ( v8 < Mysize )
          goto LABEL_19;
        v12 = 0;
      }
      if ( v12 )
      {
LABEL_19:
        _Wherenode = Parent;
        Parent = Parent->_Left;
        goto LABEL_21;
      }
      Parent = Parent->_Right;
LABEL_21:
      if ( Parent->_Isnil )
        return _Wherenode;
    }
  }
  return result;
}

//----- (00466FC0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  int v2; // esi
  std::pair<unsigned long,Guild::RelationInfo> *v3; // ebx

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
      std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        _First,
        --v2,
        _Last._Myptr - _First._Myptr,
        *--v3);
    while ( v2 > 0 );
  }
}

//----- (00467040) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  Guild::RelationInfo *p_second; // ebp
  Guild::RelationInfo *v4; // edi
  unsigned int first; // eax
  unsigned int v6; // ecx
  unsigned int v7; // eax

  p_second = &_First._Myptr->second;
  v4 = &_Mid._Myptr->second;
  if ( (unsigned __int8)BYTE1(*(_DWORD *)&_Mid._Myptr->second.m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr->second.m_cRelation) )
  {
    first = _Mid._Myptr->first;
    _Mid._Myptr->first = _First._Myptr->first;
    _First._Myptr->first = first;
    std::swap<Guild::RelationInfo>(v4, &_First._Myptr->second);
  }
  if ( (unsigned __int8)BYTE1(*(_DWORD *)&_Last._Myptr->second.m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&v4->m_cRelation) )
  {
    v6 = _Last._Myptr->first;
    _Last._Myptr->first = _Mid._Myptr->first;
    _Mid._Myptr->first = v6;
    std::swap<Guild::RelationInfo>(&_Last._Myptr->second, v4);
  }
  if ( (unsigned __int8)BYTE1(*(_DWORD *)&v4->m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&p_second->m_cRelation) )
  {
    v7 = _Mid._Myptr->first;
    _Mid._Myptr->first = _First._Myptr->first;
    _First._Myptr->first = v7;
    std::swap<Guild::RelationInfo>(v4, p_second);
  }
}

//----- (004671C0) --------------------------------------------------------
CParty *__thiscall Guild::CGuildMgr::GetGuild(CPartyMgr *this, unsigned int dwPartyUID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator it; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    &dwPartyUID);
  if ( it._Ptr == this->m_PartyMap._Myhead )
    return 0;
  else
    return it._Ptr->_Myval.second;
}

//----- (004671F0) --------------------------------------------------------
std::pair<unsigned long,Guild::RelationInfo> *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Ufill(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        std::pair<unsigned long,Guild::RelationInfo> *_Ptr,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  std::_Uninit_fill_n<std::pair<unsigned long,Guild::RelationInfo> *,unsigned int,std::pair<unsigned long,Guild::RelationInfo>,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (00467220) --------------------------------------------------------
void __cdecl std::advance<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::iterator,unsigned short>(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *_Where,
        unsigned __int16 _Off)
{
  int v2; // esi

  if ( _Off )
  {
    v2 = _Off;
    do
    {
      std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc(_Where);
      --v2;
    }
    while ( v2 );
  }
}

//----- (00467240) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  std::pair<unsigned long,Guild::RelationInfo> *i; // esi
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator v3; // edx
  std::pair<unsigned long,Guild::RelationInfo> *j; // ecx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      if ( (unsigned __int8)BYTE1(*(_DWORD *)&i->second.m_cRelation) >= (unsigned __int8)BYTE1(*(_DWORD *)&_First._Myptr->second.m_cRelation) )
      {
        v3._Myptr = i;
        for ( j = i; ; v3._Myptr = j )
        {
          --j;
          if ( (unsigned __int8)BYTE1(*(_DWORD *)&i->second.m_cRelation) >= (unsigned __int8)BYTE1(*(_DWORD *)&j->second.m_cRelation) )
            break;
        }
        if ( v3._Myptr != i )
          std::_Rotate<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>>(
            v3,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)i,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)&i[1]);
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>>(
          _First,
          (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)i,
          (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)&i[1]);
      }
    }
  }
}

//----- (00467380) --------------------------------------------------------
void __cdecl std::_Median<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  int v3; // eax
  unsigned int v4; // edi
  unsigned int v5; // esi
  std::pair<unsigned long,Guild::RelationInfo> *v6; // [esp-3Ch] [ebp-40h]
  std::pair<unsigned long,Guild::RelationInfo> *v9; // [esp-14h] [ebp-18h]
  std::pair<unsigned long,Guild::RelationInfo> *_Firsta; // [esp+8h] [ebp+4h]
  std::pair<unsigned long,Guild::RelationInfo> *_Lasta; // [esp+10h] [ebp+Ch]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      _First,
      _Mid,
      _Last);
  }
  else
  {
    v4 = 48 * ((v3 + 1) / 8);
    v5 = 24 * ((v3 + 1) / 8);
    v9 = &_First._Myptr[v4 / 0x18];
    _Firsta = &_First._Myptr[v5 / 0x18];
    std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      _First,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)_Firsta,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v9);
    std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)&_Mid._Myptr[v5 / 0xFFFFFFE8],
      _Mid,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)&_Mid._Myptr[v5 / 0x18]);
    v6 = &_Last._Myptr[v4 / 0xFFFFFFE8];
    _Lasta = &_Last._Myptr[v5 / 0xFFFFFFE8];
    std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v6,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)_Lasta,
      _Last);
    std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)_Firsta,
      _Mid,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)_Lasta);
  }
}

//----- (00467430) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::find(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *result,
        const std::string *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v5; // eax
  unsigned int Mysize; // ecx
  const char *Buf; // eax
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Keyvala; // [esp+14h] [ebp+8h]

  v5 = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lbound(
         this,
         _Keyval);
  _Keyvala = v5;
  if ( v5 == this->_Myhead
    || ((Mysize = v5->_Myval.first._Mysize, v5->_Myval.first._Myres < 0x10)
      ? (Buf = v5->_Myval.first._Bx._Buf)
      : (Buf = v5->_Myval.first._Bx._Ptr),
        std::string::compare(_Keyval, 0, _Keyval->_Mysize, Buf, Mysize) < 0) )
  {
    v8 = result;
    result->_Ptr = this->_Myhead;
  }
  else
  {
    v8 = result;
    result->_Ptr = _Keyvala;
  }
  return v8;
}

//----- (004674A0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,Guild::CompareGuildFame>(
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Last)
{
  int i; // esi
  std::pair<unsigned long,Guild::CGuild *> v3; // [esp-10h] [ebp-18h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 3 > 1; i -= 8 )
  {
    v3 = *(std::pair<unsigned long,Guild::CGuild *> *)((char *)&_First._Myptr[-1] + i);
    *(unsigned int *)((char *)&_First._Myptr[-1].first + i) = _First._Myptr->first;
    *(_DWORD *)((char *)_First._Myptr + i - 4) = _First._Myptr->second;
    std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
      _First,
      0,
      (i - 8) >> 3,
      v3);
  }
}

//----- (00467500) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  std::pair<unsigned long,Guild::RelationInfo> *v2; // esi

  if ( _Last._Myptr - _First._Myptr > 1 )
  {
    v2 = _Last._Myptr - 1;
    do
    {
      std::_Pop_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        _First,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v2,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v2,
        *v2);
      --v2;
    }
    while ( (int)((int)v2 + 24 - (unsigned int)_First._Myptr) / 24 > 1 );
  }
}

//----- (004675A0) --------------------------------------------------------
std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> *__cdecl std::_Unguarded_partition<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> *result,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last)
{
  std::pair<unsigned long,Guild::RelationInfo> *v3; // esi
  unsigned int v4; // ecx
  unsigned int v5; // edx
  Guild::RelationInfo *p_second; // edi
  Guild::RelationInfo *v7; // esi
  unsigned int Myptr; // ebp
  std::pair<unsigned long,Guild::RelationInfo> *v9; // edi
  Guild::RelationInfo *v10; // esi
  unsigned int v11; // eax
  unsigned int v12; // esi
  bool v13; // zf
  unsigned int v14; // esi
  int v15; // ebx
  int v16; // ebp
  int v17; // eax
  unsigned int v18; // esi
  unsigned int v19; // ebx
  Guild::RelationInfo *v20; // eax
  unsigned int v21; // eax
  unsigned int v22; // ebp
  int v23; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v24; // esi
  unsigned int first; // eax
  std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> *v26; // eax
  Guild::RelationInfo *v27; // [esp+10h] [ebp-164h]
  unsigned int v28; // [esp+10h] [ebp-164h]
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Glast; // [esp+14h] [ebp-160h]
  unsigned int v30; // [esp+18h] [ebp-15Ch]
  int v31; // [esp+1Ch] [ebp-158h]
  int v32; // [esp+20h] [ebp-154h]
  int v33; // [esp+24h] [ebp-150h]
  unsigned __int16 Second; // [esp+28h] [ebp-14Ch]
  int v35; // [esp+30h] [ebp-144h]
  int v36; // [esp+34h] [ebp-140h]
  int v37; // [esp+38h] [ebp-13Ch]
  int v38; // [esp+3Ch] [ebp-138h]
  __int16 v39; // [esp+40h] [ebp-134h]
  int v40; // [esp+48h] [ebp-12Ch]
  int v41; // [esp+4Ch] [ebp-128h]
  int v42; // [esp+50h] [ebp-124h]
  int v43; // [esp+54h] [ebp-120h]
  __int16 v44; // [esp+58h] [ebp-11Ch]
  int v45; // [esp+60h] [ebp-114h]
  int v46; // [esp+64h] [ebp-110h]
  int v47; // [esp+68h] [ebp-10Ch]
  int v48; // [esp+6Ch] [ebp-108h]
  __int16 v49; // [esp+70h] [ebp-104h]
  int v50; // [esp+78h] [ebp-FCh]
  int v51; // [esp+7Ch] [ebp-F8h]
  int v52; // [esp+80h] [ebp-F4h]
  int v53; // [esp+84h] [ebp-F0h]
  __int16 v54; // [esp+88h] [ebp-ECh]
  int v55; // [esp+8Ch] [ebp-E8h]
  int v56; // [esp+90h] [ebp-E4h]
  int v57; // [esp+94h] [ebp-E0h]
  int v58; // [esp+98h] [ebp-DCh]
  __int16 v59; // [esp+9Ch] [ebp-D8h]
  int v60; // [esp+A0h] [ebp-D4h]
  int v61; // [esp+A4h] [ebp-D0h]
  int v62; // [esp+A8h] [ebp-CCh]
  int v63; // [esp+ACh] [ebp-C8h]
  __int16 v64; // [esp+B0h] [ebp-C4h]

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
    _First,
    (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v3,
    (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)&_Last._Myptr[-1]);
  v4 = (unsigned int)v3;
  v5 = (unsigned int)&v3[1];
  if ( _First._Myptr < v3 )
  {
    p_second = &v3->second;
    do
    {
      if ( (unsigned __int8)BYTE1(*(_DWORD *)&p_second[-2].m_WaitTime.Second) < (unsigned __int8)BYTE1(*(_DWORD *)&p_second->m_cRelation) )
        break;
      if ( (unsigned __int8)BYTE1(*(_DWORD *)&p_second->m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)&p_second[-2].m_WaitTime.Second) )
        break;
      v4 -= 24;
      p_second = (Guild::RelationInfo *)((char *)p_second - 24);
    }
    while ( (unsigned int)_First._Myptr < v4 );
  }
  if ( v5 < (unsigned int)_Last._Myptr )
  {
    v7 = &v3[1].second;
    do
    {
      if ( (unsigned __int8)BYTE1(*(_DWORD *)&v7->m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)(v4 + 4)) )
        break;
      if ( (unsigned __int8)BYTE1(*(_DWORD *)(v4 + 4)) < (unsigned __int8)BYTE1(*(_DWORD *)&v7->m_cRelation) )
        break;
      v5 += 24;
      v7 = (Guild::RelationInfo *)((char *)v7 + 24);
    }
    while ( v5 < (unsigned int)_Last._Myptr );
  }
  Myptr = v4;
  v9 = (std::pair<unsigned long,Guild::RelationInfo> *)v5;
  _Glast._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)v4;
  while ( 1 )
  {
    while ( 1 )
    {
      if ( v9 < _Last._Myptr )
      {
        v10 = &v9->second;
        v27 = &v9->second;
        do
        {
          if ( (unsigned __int8)BYTE1(*(_DWORD *)(v4 + 4)) >= (unsigned __int8)BYTE1(*(_DWORD *)&v10->m_cRelation) )
          {
            if ( (unsigned __int8)BYTE1(*(_DWORD *)&v10->m_cRelation) < (unsigned __int8)BYTE1(*(_DWORD *)(v4 + 4)) )
              break;
            v11 = v5;
            v12 = *(_DWORD *)v5;
            *(_DWORD *)v5 = v9->first;
            v9->first = v12;
            v5 += 24;
            v11 += 4;
            v60 = *(_DWORD *)v11;
            v61 = *(_DWORD *)(v11 + 4);
            v62 = *(_DWORD *)(v11 + 8);
            v64 = *(_WORD *)(v11 + 16);
            v63 = *(_DWORD *)(v11 + 12);
            *(_DWORD *)v11 = *(_DWORD *)&v27->m_cRelation;
            *(_DWORD *)(v11 + 4) = *(_DWORD *)&v27->m_WaitTime.Month;
            *(_DWORD *)(v11 + 8) = *(_DWORD *)&v27->m_WaitTime.Hour;
            *(_DWORD *)(v11 + 12) = *(_DWORD *)&v27->m_WaitTime.Second;
            *(_WORD *)(v11 + 16) = HIWORD(v27->m_WaitTime.MSecond);
            v10 = v27;
            *(_DWORD *)&v27->m_cRelation = v60;
            *(_DWORD *)&v27->m_WaitTime.Month = v61;
            *(_DWORD *)&v27->m_WaitTime.Hour = v62;
            *(_DWORD *)&v27->m_WaitTime.Second = v63;
            HIWORD(v27->m_WaitTime.MSecond) = v64;
          }
          ++v9;
          v10 = (Guild::RelationInfo *)((char *)v10 + 24);
          v27 = v10;
        }
        while ( v9 < _Last._Myptr );
      }
      v13 = (std::pair<unsigned long,Guild::RelationInfo> *)Myptr == _First._Myptr;
      if ( Myptr > (unsigned int)_First._Myptr )
      {
        v28 = v4 + 4;
        v14 = Myptr - 20;
        do
        {
          if ( (unsigned __int8)BYTE1(*(_DWORD *)v14) >= (unsigned __int8)BYTE1(*(_DWORD *)v28) )
          {
            if ( (unsigned __int8)BYTE1(*(_DWORD *)v28) < (unsigned __int8)BYTE1(*(_DWORD *)v14) )
              break;
            v15 = *(_DWORD *)(v4 - 24);
            *(_DWORD *)(v4 - 24) = *(_DWORD *)(v14 - 4);
            *(_DWORD *)(v14 - 4) = v15;
            v4 -= 24;
            v50 = *(_DWORD *)(v28 - 24);
            v51 = *(_DWORD *)(v28 - 24 + 4);
            v52 = *(_DWORD *)(v28 - 24 + 8);
            v16 = *(_DWORD *)(v28 - 24 + 12);
            v54 = *(_WORD *)(v28 - 24 + 16);
            v28 -= 24;
            v53 = v16;
            *(_DWORD *)v28 = *(_DWORD *)v14;
            *(_DWORD *)(v28 + 4) = *(_DWORD *)(v14 + 4);
            *(_DWORD *)(v28 + 8) = *(_DWORD *)(v14 + 8);
            *(_DWORD *)(v28 + 12) = *(_DWORD *)(v14 + 12);
            Myptr = (unsigned int)_Glast._Myptr;
            *(_WORD *)(v28 + 16) = *(_WORD *)(v14 + 16);
            *(_DWORD *)v14 = v50;
            *(_DWORD *)(v14 + 4) = v51;
            *(_DWORD *)(v14 + 8) = v52;
            *(_DWORD *)(v14 + 12) = v53;
            *(_WORD *)(v14 + 16) = v54;
          }
          Myptr -= 24;
          v14 -= 24;
          _Glast._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)Myptr;
        }
        while ( (unsigned int)_First._Myptr < Myptr );
        v13 = (std::pair<unsigned long,Guild::RelationInfo> *)Myptr == _First._Myptr;
      }
      if ( v13 )
        break;
      Myptr -= 24;
      _Glast._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)Myptr;
      if ( v9 == _Last._Myptr )
      {
        v4 -= 24;
        if ( Myptr != v4 )
        {
          v21 = *(_DWORD *)Myptr;
          *(_DWORD *)Myptr = *(_DWORD *)v4;
          *(_DWORD *)v4 = v21;
          v22 = Myptr + 4;
          v40 = *(_DWORD *)v22;
          v41 = *(_DWORD *)(v22 + 4);
          v42 = *(_DWORD *)(v22 + 8);
          v44 = *(_WORD *)(v22 + 16);
          v43 = *(_DWORD *)(v22 + 12);
          *(_DWORD *)v22 = *(_DWORD *)(v4 + 4);
          *(_DWORD *)(v22 + 4) = *(_DWORD *)(v4 + 8);
          *(_DWORD *)(v22 + 8) = *(_DWORD *)(v4 + 12);
          *(_DWORD *)(v22 + 12) = *(_DWORD *)(v4 + 16);
          *(_WORD *)(v22 + 16) = *(_WORD *)(v4 + 20);
          *(_DWORD *)(v4 + 4) = v40;
          *(_DWORD *)(v4 + 8) = v41;
          *(_DWORD *)(v4 + 12) = v42;
          *(_DWORD *)(v4 + 16) = v43;
          *(_WORD *)(v4 + 20) = v44;
        }
        v23 = *(_DWORD *)v4;
        *(_DWORD *)v4 = *(_DWORD *)(v5 - 24);
        *(_DWORD *)(v5 - 24) = v23;
        v5 -= 24;
        v35 = *(_DWORD *)(v4 + 4);
        v36 = *(_DWORD *)(v4 + 8);
        v37 = *(_DWORD *)(v4 + 12);
        v39 = *(_WORD *)(v4 + 20);
        v38 = *(_DWORD *)(v4 + 16);
        *(_DWORD *)(v4 + 4) = *(_DWORD *)(v5 + 4);
        *(_DWORD *)(v4 + 8) = *(_DWORD *)(v5 + 8);
        *(_DWORD *)(v4 + 12) = *(_DWORD *)(v5 + 12);
        *(_DWORD *)(v4 + 16) = *(_DWORD *)(v5 + 16);
        Myptr = (unsigned int)_Glast._Myptr;
        *(_WORD *)(v4 + 20) = *(_WORD *)(v5 + 20);
        *(_DWORD *)(v5 + 4) = v35;
        *(_DWORD *)(v5 + 8) = v36;
        *(_DWORD *)(v5 + 12) = v37;
        *(_DWORD *)(v5 + 16) = v38;
        *(_WORD *)(v5 + 20) = v39;
      }
      else
      {
        v24 = v9;
        first = v9->first;
        v9->first = *(_DWORD *)Myptr;
        *(_DWORD *)Myptr = first;
        ++v9;
        v24 = (std::pair<unsigned long,Guild::RelationInfo> *)((char *)v24 + 4);
        v30 = v24->first;
        v31 = *(_DWORD *)&v24->second.m_cRelation;
        v32 = *(_DWORD *)&v24->second.m_WaitTime.Month;
        Second = v24->second.m_WaitTime.Second;
        v33 = *(_DWORD *)&v24->second.m_WaitTime.Hour;
        v24->first = *(_DWORD *)(Myptr + 4);
        *(_DWORD *)&v24->second.m_cRelation = *(_DWORD *)(Myptr + 8);
        *(_DWORD *)&v24->second.m_WaitTime.Month = *(_DWORD *)(Myptr + 12);
        *(_DWORD *)&v24->second.m_WaitTime.Hour = *(_DWORD *)(Myptr + 16);
        v24->second.m_WaitTime.Second = *(_WORD *)(Myptr + 20);
        *(_DWORD *)(Myptr + 4) = v30;
        *(_DWORD *)(Myptr + 8) = v31;
        *(_DWORD *)(Myptr + 12) = v32;
        *(_DWORD *)(Myptr + 16) = v33;
        *(_WORD *)(Myptr + 20) = Second;
      }
    }
    if ( v9 == _Last._Myptr )
      break;
    if ( (std::pair<unsigned long,Guild::RelationInfo> *)v5 != v9 )
    {
      v17 = *(_DWORD *)v4;
      *(_DWORD *)v4 = *(_DWORD *)v5;
      *(_DWORD *)v5 = v17;
      v55 = *(_DWORD *)(v4 + 4);
      v56 = *(_DWORD *)(v4 + 8);
      v57 = *(_DWORD *)(v4 + 12);
      v59 = *(_WORD *)(v4 + 20);
      v58 = *(_DWORD *)(v4 + 16);
      *(_DWORD *)(v4 + 4) = *(_DWORD *)(v5 + 4);
      *(_DWORD *)(v4 + 8) = *(_DWORD *)(v5 + 8);
      *(_DWORD *)(v4 + 12) = *(_DWORD *)(v5 + 12);
      *(_DWORD *)(v4 + 16) = *(_DWORD *)(v5 + 16);
      *(_WORD *)(v4 + 20) = *(_WORD *)(v5 + 20);
      *(_DWORD *)(v5 + 4) = v55;
      *(_DWORD *)(v5 + 8) = v56;
      *(_DWORD *)(v5 + 12) = v57;
      *(_DWORD *)(v5 + 16) = v58;
      *(_WORD *)(v5 + 20) = v59;
    }
    v18 = v4;
    v19 = *(_DWORD *)v4;
    *(_DWORD *)v4 = v9->first;
    v9->first = v19;
    v20 = &v9->second;
    v5 += 24;
    ++v9;
    v4 += 24;
    v18 += 4;
    v45 = *(_DWORD *)v18;
    v46 = *(_DWORD *)(v18 + 4);
    v47 = *(_DWORD *)(v18 + 8);
    v49 = *(_WORD *)(v18 + 16);
    v48 = *(_DWORD *)(v18 + 12);
    *(_DWORD *)v18 = *(_DWORD *)&v20->m_cRelation;
    *(_DWORD *)(v18 + 4) = *(_DWORD *)&v20->m_WaitTime.Month;
    *(_DWORD *)(v18 + 8) = *(_DWORD *)&v20->m_WaitTime.Hour;
    *(_DWORD *)(v18 + 12) = *(_DWORD *)&v20->m_WaitTime.Second;
    Myptr = (unsigned int)_Glast._Myptr;
    *(_WORD *)(v18 + 16) = HIWORD(v20->m_WaitTime.MSecond);
    *(_DWORD *)&v20->m_cRelation = v45;
    *(_DWORD *)&v20->m_WaitTime.Month = v46;
    *(_DWORD *)&v20->m_WaitTime.Hour = v47;
    *(_DWORD *)&v20->m_WaitTime.Second = v48;
    HIWORD(v20->m_WaitTime.MSecond) = v49;
  }
  v26 = result;
  result->first._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)v4;
  result->second._Myptr = (std::pair<unsigned long,Guild::RelationInfo> *)v5;
  return v26;
}

//----- (00467E70) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00468130) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004681F0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,Guild::CGuild *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,Guild::CGuild *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004683A0) --------------------------------------------------------
void __thiscall __noreturn std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00468410) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Insert_n(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Where,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  int v5; // edx
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // ebx
  int v7; // ecx
  int v8; // edx
  unsigned int v9; // ecx
  unsigned int v10; // edx
  unsigned int v11; // ecx
  int v13; // eax
  int v14; // eax
  unsigned int v15; // ecx
  int v16; // eax
  int v17; // ebx
  std::pair<unsigned long,Guild::RelationInfo> *v18; // eax
  char *v19; // edi
  std::pair<unsigned long,Guild::RelationInfo> *Mylast; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *v22; // edi
  std::pair<unsigned long,Guild::RelationInfo> *v23; // [esp-18h] [ebp-4Ch]
  std::pair<unsigned long,Guild::RelationInfo> *v24; // [esp-Ch] [ebp-40h]
  unsigned int v25; // [esp-8h] [ebp-3Ch]
  int v26; // [esp+0h] [ebp-34h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> _Tmp; // [esp+Ch] [ebp-28h] BYREF
  int *v28; // [esp+24h] [ebp-10h]
  int v29; // [esp+30h] [ebp-4h]
  std::pair<unsigned long,Guild::RelationInfo> *_Wherea; // [esp+3Ch] [ebp+8h]
  std::pair<unsigned long,Guild::RelationInfo> *_Counta; // [esp+40h] [ebp+Ch]
  std::pair<unsigned long,Guild::RelationInfo> *_Newvec; // [esp+44h] [ebp+10h]
  std::pair<unsigned long,Guild::RelationInfo> *_Newveca; // [esp+44h] [ebp+10h]

  v5 = *(_DWORD *)&_Val->second.m_cRelation;
  Myfirst = this->_Myfirst;
  _Tmp.first = _Val->first;
  v7 = *(_DWORD *)&_Val->second.m_WaitTime.Month;
  *(_DWORD *)&_Tmp.second.m_cRelation = v5;
  v8 = *(_DWORD *)&_Val->second.m_WaitTime.Hour;
  *(_DWORD *)&_Tmp.second.m_WaitTime.Month = v7;
  v9 = *(_DWORD *)&_Val->second.m_WaitTime.Second;
  *(_DWORD *)&_Tmp.second.m_WaitTime.Hour = v8;
  v10 = *(unsigned int *)((char *)&_Val->second.m_WaitTime.MSecond + 2);
  v28 = &v26;
  *(_QWORD *)&_Tmp.second.m_WaitTime.Second = __PAIR64__(v10, v9);
  if ( Myfirst )
    v11 = this->_Myend - Myfirst;
  else
    v11 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v13 = this->_Mylast - Myfirst;
    else
      v13 = 0;
    if ( 178956970 - v13 < _Count )
      std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen(this);
    if ( Myfirst )
      v14 = this->_Mylast - Myfirst;
    else
      v14 = 0;
    if ( v11 >= _Count + v14 )
    {
      Mylast = this->_Mylast;
      _Newveca = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::_Copy_backward_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(
          _Where._Myptr,
          _Wherea,
          _Newveca);
        std::fill<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo>>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v25 = _Count - (this->_Mylast - _Where._Myptr);
        v24 = this->_Mylast;
        v29 = 2;
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Ufill(this, v24, v25, &_Tmp);
        v22 = &this->_Mylast[_Count];
        this->_Mylast = v22;
        std::fill<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo>>(
          _Where._Myptr,
          &v22[-_Count],
          &_Tmp);
      }
    }
    else
    {
      if ( 178956970 - (v11 >> 1) >= v11 )
        v15 = (v11 >> 1) + v11;
      else
        v15 = 0;
      if ( Myfirst )
        v16 = this->_Mylast - Myfirst;
      else
        v16 = 0;
      if ( v15 < _Count + v16 )
        v15 = (unsigned int)std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size((std::vector<CLotteryEvent::LotteryEventItem> *)this)
            + _Count;
      v17 = v15;
      _Newvec = (std::pair<unsigned long,Guild::RelationInfo> *)operator new((tagHeader *)(24 * v15));
      v23 = this->_Myfirst;
      v29 = 0;
      _Counta = std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
                  v23,
                  _Where._Myptr,
                  _Newvec);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::RelationInfo> *,unsigned int,std::pair<unsigned long,Guild::RelationInfo>,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
        _Counta,
        _Count,
        &_Tmp);
      std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
        _Where._Myptr,
        this->_Mylast,
        &_Counta[_Count]);
      v18 = this->_Myfirst;
      if ( v18 )
        v18 = (std::pair<unsigned long,Guild::RelationInfo> *)(this->_Mylast - v18);
      v19 = (char *)v18 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[v17];
      this->_Mylast = &_Newvec[(_DWORD)v19];
      this->_Myfirst = _Newvec;
    }
  }
}

//----- (004686E0) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Insert_n(
        std::vector<std::pair<unsigned long,Guild::CGuild *>> *this,
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Where,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::CGuild *> *_Val)
{
  Guild::CGuild *second; // edx
  std::pair<unsigned long,Guild::CGuild *> *Myfirst; // ecx
  unsigned int v7; // eax
  int v9; // edx
  int v10; // edx
  unsigned int v11; // eax
  int v12; // edx
  int v13; // eax
  std::pair<unsigned long,Guild::CGuild *> *v14; // edi
  std::pair<unsigned long,Guild::CGuild *> *v15; // ecx
  int v16; // eax
  int v17; // ebx
  std::pair<unsigned long,unsigned long> *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  std::pair<unsigned long,Guild::CGuild *> *v22; // ebx
  std::pair<unsigned long,unsigned long> *v23; // ebx
  std::pair<unsigned long,unsigned long> *v24; // [esp-18h] [ebp-40h]
  std::pair<unsigned long,unsigned long> *v25; // [esp-Ch] [ebp-34h]
  unsigned int v26; // [esp-8h] [ebp-30h]
  int v27; // [esp+0h] [ebp-28h] BYREF
  std::pair<unsigned long,Guild::CGuild *> _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  std::pair<unsigned long,Guild::CGuild *> *_Newvec; // [esp+14h] [ebp-14h]
  int *v30; // [esp+18h] [ebp-10h]
  int v31; // [esp+24h] [ebp-4h]
  std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Wherea; // [esp+30h] [ebp+8h]
  unsigned int _Counta; // [esp+34h] [ebp+Ch]
  std::pair<unsigned long,unsigned long> *_Valb; // [esp+38h] [ebp+10h]
  std::pair<unsigned long,unsigned long> *_Vala; // [esp+38h] [ebp+10h]

  second = _Val->second;
  _Tmp.first = _Val->first;
  Myfirst = this->_Myfirst;
  v30 = &v27;
  _Tmp.second = second;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x1FFFFFFF - v9 < _Count )
      std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen((std::vector<std::pair<unsigned long,Guild::RelationInfo>> *)this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
      v20 = ((char *)Mylast - (char *)_Where._Myptr) >> 3 < _Count;
      v21 = 8 * _Count;
      _Wherea._Myptr = (std::pair<unsigned long,Guild::CGuild *> *)(8 * _Count);
      _Vala = Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          Mylast,
          (std::pair<unsigned long,unsigned long> *)&_Where._Myptr[v21 / 8]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
        v31 = 2;
        std::vector<std::pair<enum eStdFunc,int>>::_Ufill(
          (std::vector<std::pair<unsigned long,unsigned long>> *)this,
          v25,
          v26,
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
        v22 = (std::pair<unsigned long,Guild::CGuild *> *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)v22 - (char *)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
      else
      {
        v23 = &Mylast[v21 / 0xFFFFFFF8];
        this->_Mylast = (std::pair<unsigned long,Guild::CGuild *> *)std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                                                                      &Mylast[v21 / 0xFFFFFFF8],
                                                                      Mylast,
                                                                      Mylast);
        std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          v23,
          _Vala);
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
    }
    else
    {
      if ( 0x1FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      _Counta = v11;
      v14 = (std::pair<unsigned long,Guild::CGuild *> *)operator new((tagHeader *)(8 * v11));
      v24 = (std::pair<unsigned long,unsigned long> *)this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Valb = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                v24,
                (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
                (std::pair<unsigned long,unsigned long> *)v14);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        _Valb,
        _Count,
        (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
        (std::pair<unsigned long,unsigned long> *)this->_Mylast,
        &_Valb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[_Counta];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (00468930) --------------------------------------------------------
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Node::_Node(
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Rarg,
        const std::pair<std::string const ,Guild::CGuild *> *_Val,
        char _Carg)
{
  std::pair<std::string const ,Guild::CGuild *> *p_Myval; // edi

  this->_Parent = _Parg;
  p_Myval = &this->_Myval;
  this->_Left = _Larg;
  this->_Right = _Rarg;
  this->_Myval.first._Myres = 15;
  this->_Myval.first._Mysize = 0;
  this->_Myval.first._Bx._Buf[0] = 0;
  std::string::assign(&this->_Myval.first, &_Val->first, 0, 0xFFFFFFFF);
  p_Myval->second = _Val->second;
  this->_Color = _Carg;
  this->_Isnil = 0;
}

//----- (00468990) --------------------------------------------------------
std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *__cdecl std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long const,Guild::CGuild *>,Guild::CompareGuildFame>(
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First1,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last1,
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First2,
        std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Last2)
{
  std::pair<unsigned long,Guild::CGuild *> *Myptr; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // esi
  std::pair<unsigned long,Guild::CGuild *> *v7; // ebx
  std::pair<unsigned long,Guild::CGuild *> *v8; // edi
  unsigned int first; // eax
  Guild::CGuild *second; // esi
  int v11; // ebp
  int v12; // edi
  std::pair<unsigned long,Guild::CGuild *> *v13; // ebx
  std::pair<unsigned long,Guild::CGuild *> v14; // rax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *i; // eax
  std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *v18; // eax
  std::pair<unsigned long,Guild::CGuild *> *_Mid2; // [esp+10h] [ebp-4h]

  Myptr = _First2._Myptr;
  Ptr = _First1._Ptr;
  v7 = _First2._Myptr;
  _Mid2 = _First2._Myptr;
  if ( _First1._Ptr != _Last1._Ptr )
  {
    v8 = _Last2._Myptr;
    do
    {
      if ( v7 == v8 )
        break;
      first = Ptr->_Myval.first;
      second = Ptr->_Myval.second;
      v7->first = first;
      v7->second = second;
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First1);
      Ptr = _First1._Ptr;
      v7 = ++_Mid2;
      Myptr = _First2._Myptr;
    }
    while ( _First1._Ptr != _Last1._Ptr );
  }
  v11 = v7 - Myptr;
  if ( v11 > 1 )
  {
    v12 = v11 / 2;
    if ( v11 / 2 > 0 )
    {
      v13 = &Myptr[v12];
      do
      {
        v14 = v13[-1];
        --v13;
        std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
          (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)Myptr,
          --v12,
          v11,
          v14);
        Myptr = _First2._Myptr;
      }
      while ( v12 > 0 );
      v7 = _Mid2;
    }
  }
  while ( Ptr != _Last1._Ptr )
  {
    if ( Ptr->_Myval.second->m_dwFame > Myptr->second->m_dwFame )
    {
      std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(
        (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)Myptr,
        0,
        v11,
        (std::pair<unsigned long,Guild::CGuild *>)Ptr->_Myval);
      Myptr = _First2._Myptr;
      v7 = _Mid2;
    }
    if ( !Ptr->_Isnil )
    {
      Right = Ptr->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Ptr != i->_Right )
            break;
          Ptr = i;
        }
        Ptr = i;
      }
      else
      {
        Ptr = Ptr->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Ptr = j;
      }
    }
  }
  std::sort_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,Guild::CompareGuildFame>(
    (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)Myptr,
    (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)v7);
  v18 = result;
  result->_Myptr = _Mid2;
  return v18;
}

//----- (00468AE0) --------------------------------------------------------
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__cdecl std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First1,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last1,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First2,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last2)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // esi
  std::pair<unsigned long,Guild::RelationInfo> *i; // ebx
  unsigned int first; // eax
  Guild::RelationInfo *p_second; // ebp
  int v10; // ecx
  int v11; // edx
  int v12; // esi
  int v13; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *k; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *j; // eax
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *v17; // eax
  unsigned int v18; // [esp+24h] [ebp-4h]

  Myptr = _First2._Myptr;
  Ptr = _First1._Ptr;
  for ( i = _First2._Myptr; _First1._Ptr != _Last1._Ptr; ++i )
  {
    if ( i == _Last2._Myptr )
      break;
    first = Ptr->_Myval.first;
    p_second = &Ptr->_Myval.second;
    v10 = *(_DWORD *)&Ptr->_Myval.second.m_cRelation;
    v11 = *(_DWORD *)&Ptr->_Myval.second.m_WaitTime.Month;
    v12 = *(_DWORD *)&Ptr->_Myval.second.m_WaitTime.Hour;
    v13 = *(_DWORD *)&p_second->m_WaitTime.Second;
    LOWORD(v18) = HIWORD(p_second->m_WaitTime.MSecond);
    i->first = first;
    *(_DWORD *)&i->second.m_cRelation = v10;
    *(_DWORD *)&i->second.m_WaitTime.Month = v11;
    *(_DWORD *)&i->second.m_WaitTime.Hour = v12;
    *(_DWORD *)&i->second.m_WaitTime.Second = v13;
    *(unsigned int *)((char *)&i->second.m_WaitTime.MSecond + 2) = v18;
    std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::const_iterator *)&_First1);
    Ptr = _First1._Ptr;
    Myptr = _First2._Myptr;
  }
  if ( i - Myptr > 1 )
    std::_Make_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)i);
  while ( Ptr != _Last1._Ptr )
  {
    if ( Ptr->_Myval.second.m_cState < (unsigned __int8)BYTE1(*(_DWORD *)&Myptr->second.m_cRelation) )
      std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
        0,
        i - Myptr,
        Ptr->_Myval);
    if ( !Ptr->_Isnil )
    {
      Right = Ptr->_Right;
      if ( Right->_Isnil )
      {
        for ( j = Ptr->_Parent; !j->_Isnil; j = j->_Parent )
        {
          if ( Ptr != j->_Right )
            break;
          Ptr = j;
        }
        Ptr = j;
      }
      else
      {
        Ptr = Ptr->_Right;
        for ( k = Right->_Left; !k->_Isnil; k = k->_Left )
          Ptr = k;
      }
    }
  }
  std::sort_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
    (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
    (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)i);
  v17 = result;
  result->_Myptr = i;
  return v17;
}
// 468B3C: variable 'v18' is possibly undefined

//----- (00468C80) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last,
        int _Ideal,
        Guild::CompareRelationState _Pred)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myptr; // ebx
  std::pair<unsigned long,Guild::RelationInfo> *v5; // edi
  int v6; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v8; // ebp
  std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Last._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v5);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        &_Mid,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v5);
      v8 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v5 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,Guild::CompareRelationState>(
          _Mid.second,
          (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v5,
          _Ideal,
          _Pred);
        v5 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,Guild::CompareRelationState>(
          (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          _Pred);
        Myptr = v8;
      }
      v6 = v5 - Myptr;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( v5 - Myptr > 1 )
      std::_Make_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v5);
    std::sort_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)Myptr,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v5);
  }
}
// 468D7F: conditional instruction was optimized away because eax.4>=21

//----- (00468DD0) --------------------------------------------------------
Guild::CGuild *__thiscall Guild::CGuildMgr::GetGuild(Guild::CGuildMgr *this, char *szName)
{
  std::string _Keyval; // [esp+8h] [ebp-28h] BYREF
  int v5; // [esp+2Ch] [ebp-4h]

  _Keyval._Myres = 15;
  _Keyval._Mysize = 0;
  _Keyval._Bx._Buf[0] = 0;
  std::string::assign(&_Keyval, szName, strlen(szName));
  v5 = 0;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)&this->m_GuildNameMap,
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&szName,
    &_Keyval);
  if ( _Keyval._Myres >= 0x10 )
    operator delete(_Keyval._Bx._Ptr);
  if ( szName == (char *)this->m_GuildNameMap._Myhead )
    return 0;
  else
    return (Guild::CGuild *)*((_DWORD *)szName + 10);
}

//----- (00468E90) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00468F50) --------------------------------------------------------
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v4; // ebx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator> _Where; // [esp+Ch] [ebp-8h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&_Where,
    _Keyval);
  Ptr = _Where.second._Ptr;
  v4 = _Where.first._Ptr;
  _Keyval = 0;
  std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(
    _Where.first,
    _Where.second,
    (unsigned int *)&_Keyval);
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
    this,
    &_Where.first,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)Ptr);
  return _Keyval;
}

//----- (00468FB0) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::reserve(
        std::vector<std::pair<unsigned long,Guild::CGuild *>> *this,
        unsigned int _Count)
{
  std::pair<unsigned long,Guild::CGuild *> *Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  std::pair<unsigned long,Guild::CGuild *> *v6; // edi
  std::pair<unsigned long,Guild::CGuild *> *v7; // eax
  std::pair<unsigned long,unsigned long> *v8; // [esp-18h] [ebp-38h]
  std::pair<unsigned long,unsigned long> *Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  std::pair<unsigned long,Guild::CGuild *> *_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x1FFFFFFF )
    std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen((std::vector<std::pair<unsigned long,Guild::RelationInfo>> *)this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(8 * _Count);
    v6 = (std::pair<unsigned long,Guild::CGuild *> *)operator new(_Counta);
    Mylast = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
    v8 = (std::pair<unsigned long,unsigned long> *)this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
      v8,
      Mylast,
      (std::pair<unsigned long,unsigned long> *)v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (std::pair<unsigned long,Guild::CGuild *> *)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (00469080) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::reserve(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        std::pair<unsigned long,Guild::RelationInfo> *_Count)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // eax
  unsigned int v4; // ebx
  std::pair<unsigned long,Guild::RelationInfo> *v5; // ecx
  int v6; // edi
  std::pair<unsigned long,Guild::RelationInfo> *v7; // [esp-18h] [ebp-34h]
  std::pair<unsigned long,Guild::RelationInfo> *Mylast; // [esp-14h] [ebp-30h]
  _DWORD v9[7]; // [esp+0h] [ebp-1Ch] BYREF
  std::pair<unsigned long,Guild::RelationInfo> *_Ptr; // [esp+24h] [ebp+8h]

  v9[3] = v9;
  if ( (unsigned int)_Count > 0xAAAAAAA )
    std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen(this);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    Myfirst = (std::pair<unsigned long,Guild::RelationInfo> *)(this->_Myend - Myfirst);
  if ( Myfirst < _Count )
  {
    v4 = (unsigned int)_Count;
    _Ptr = (std::pair<unsigned long,Guild::RelationInfo> *)operator new((tagHeader *)(24 * (_DWORD)_Count));
    Mylast = this->_Mylast;
    v7 = this->_Myfirst;
    v9[6] = 0;
    std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
      v7,
      Mylast,
      _Ptr);
    v5 = this->_Myfirst;
    if ( v5 )
      v6 = this->_Mylast - v5;
    else
      v6 = 0;
    if ( v5 )
      operator delete(this->_Myfirst);
    this->_Myend = &_Ptr[v4];
    this->_Mylast = &_Ptr[v6];
    this->_Myfirst = _Ptr;
  }
}

//----- (00469170) --------------------------------------------------------
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::insert(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Where,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // esi
  int v6; // esi
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (004691E0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Rarg,
        const std::pair<std::string const ,Guild::CGuild *> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v6; // esi
  int v8; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v9; // [esp+Ch] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Wherenode; // [esp+10h] [ebp-14h]
  int *v11; // [esp+14h] [ebp-10h]
  int v12; // [esp+20h] [ebp-4h]

  v11 = &v8;
  v6 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)operator new((tagHeader *)0x30);
  _Wherenode = v6;
  v12 = 1;
  v9 = (CPacketDispatch *)v6;
  if ( v6 )
    std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Node::_Node(
      v6,
      _Larg,
      _Parg,
      _Rarg,
      _Val,
      _Carg);
  return v6;
}

//----- (00469270) --------------------------------------------------------
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__cdecl std::partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First1,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last1,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First2,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last2)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myptr; // ecx
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *v6; // eax

  if ( _First1._Ptr == _Last1._Ptr || _First2._Myptr == _Last2._Myptr )
  {
    v6 = result;
    result->_Myptr = _First2._Myptr;
  }
  else
  {
    Myptr = std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(
              (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *)&_First1,
              _First1,
              _Last1,
              _First2,
              _Last2)->_Myptr;
    v6 = result;
    result->_Myptr = Myptr;
  }
  return v6;
}

//----- (004692C0) --------------------------------------------------------
void __cdecl std::sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First,
        std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last,
        Guild::CompareRelationState _Pred)
{
  std::_Sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,Guild::CompareRelationState>(
    _First,
    _Last,
    _Last._Myptr - _First._Myptr,
    _Pred);
}

//----- (00469300) --------------------------------------------------------
void __thiscall std::map<unsigned long,Guild::CGuild *>::~map<unsigned long,Guild::CGuild *>(
        std::map<unsigned long,Guild::CGuild *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00469330) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::push_back(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // edi
  unsigned int v4; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<std::pair<unsigned long,Guild::RelationInfo> *,unsigned int,std::pair<unsigned long,Guild::RelationInfo>,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<std::pair<unsigned long,Guild::RelationInfo>>::insert(
      this,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *)&_Val,
      (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (004693C0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v4; // esi

  v2 = _Rootnode;
  v4 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)_Rootnode;
  if ( !_Rootnode->_Isnil )
  {
    do
    {
      std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Erase(
        this,
        v4->_Right);
      v4 = v4->_Left;
      std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v2);
      operator delete(v2);
      v2 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v4;
    }
    while ( !v4->_Isnil );
  }
}

//----- (00469400) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Wherenode,
        const std::pair<std::string const ,Guild::CGuild *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<std::string const ,Guild::CGuild *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x7FFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<std::string const ,Guild::CGuild *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004695B0) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Assign_n(
        std::vector<std::pair<unsigned long,Guild::CGuild *>> *this,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::CGuild *> *_Val)
{
  Guild::CGuild *second; // eax
  std::pair<unsigned long,Guild::CGuild *> *Mylast; // esi
  std::pair<unsigned long,Guild::CGuild *> *Myfirst; // edx
  std::pair<unsigned long,Guild::CGuild *> _Tmp; // [esp+4h] [ebp-8h] BYREF

  second = _Val->second;
  Mylast = this->_Mylast;
  _Tmp.first = _Val->first;
  Myfirst = this->_Myfirst;
  _Tmp.second = second;
  if ( Myfirst != Mylast )
    this->_Mylast = Myfirst;
  std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Insert_n(
    this,
    (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)this->_Myfirst,
    _Count,
    &_Tmp);
}
// 4695B0: could not find valid save-restore pair for edi

//----- (00469610) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Assign_n(
        std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this,
        unsigned int _Count,
        const std::pair<unsigned long,Guild::RelationInfo> *_Val)
{
  int v4; // edx
  int v5; // ecx
  int v6; // edx
  unsigned int v7; // ecx
  std::pair<unsigned long,Guild::RelationInfo> *Mylast; // eax
  std::pair<unsigned long,Guild::RelationInfo> *Myfirst; // ecx
  std::pair<unsigned long,Guild::RelationInfo> _Tmp; // [esp+4h] [ebp-18h] BYREF

  v4 = *(_DWORD *)&_Val->second.m_cRelation;
  _Tmp.first = _Val->first;
  v5 = *(_DWORD *)&_Val->second.m_WaitTime.Month;
  *(_DWORD *)&_Tmp.second.m_cRelation = v4;
  v6 = *(_DWORD *)&_Val->second.m_WaitTime.Hour;
  *(_DWORD *)&_Tmp.second.m_WaitTime.Month = v5;
  v7 = *(_DWORD *)&_Val->second.m_WaitTime.Second;
  *(_DWORD *)&_Tmp.second.m_WaitTime.Hour = v6;
  Mylast = this->_Mylast;
  *(_QWORD *)&_Tmp.second.m_WaitTime.Second = __PAIR64__(
                                                *(unsigned int *)((char *)&_Val->second.m_WaitTime.MSecond + 2),
                                                v7);
  Myfirst = this->_Myfirst;
  if ( Myfirst != Mylast )
    this->_Mylast = std::_Copy_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(
                      Mylast,
                      Mylast,
                      Myfirst);
  std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Insert_n(
    this,
    (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)this->_Myfirst,
    _Count,
    &_Tmp);
}

//----- (00469680) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *v15; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // eax
  bool v17; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *v19; // eax
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == Ptr )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == Ptr )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( Ptr->_Color == 1 )
  {
    v15 = (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this;
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              v15,
              Parent);
            Left = Parent->_Right;
            v15 = (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
                v15,
                Left);
              Left = Parent->_Right;
              v15 = (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              v15,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
              v15,
              Parent);
            Left = Parent->_Left;
            v15 = (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
                v15,
                Left);
              Left = Parent->_Left;
              v15 = (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
              v15,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v17 = Parent == v15->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v17 );
    }
    Right->_Color = 1;
  }
  if ( Ptr->_Myval.first._Myres >= 0x10 )
    operator delete(Ptr->_Myval.first._Bx._Ptr);
  Ptr->_Myval.first._Myres = 15;
  Ptr->_Myval.first._Mysize = 0;
  Ptr->_Myval.first._Bx._Buf[0] = 0;
  operator delete(Ptr);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v19 = result;
  result->_Ptr = _Where._Ptr;
  return v19;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00469960) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *result,
        std::pair<std::string const ,Guild::CGuild *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // ebx
  char v5; // al
  const std::pair<std::string const ,Guild::CGuild *> *v6; // edi
  unsigned int i; // ecx
  unsigned int Mysize; // ebx
  const char *Buf; // eax
  unsigned int v10; // edx
  int v11; // ecx
  const char *v12; // esi
  int v13; // eax
  bool v14; // sf
  int v15; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v16; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *v18; // eax
  const char *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v20; // edx
  bool _Addleft; // [esp+10h] [ebp-14h]
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *v22; // [esp+14h] [ebp-10h]
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Wherenode; // [esp+20h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  v5 = 1;
  v6 = _Val;
  v22 = this;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    for ( i = _Val->first._Mysize; ; i = _Val->first._Mysize )
    {
      Mysize = Parent->_Myval.first._Mysize;
      _Wherenode = Parent;
      if ( Parent->_Myval.first._Myres < 0x10 )
        Buf = Parent->_Myval.first._Bx._Buf;
      else
        Buf = Parent->_Myval.first._Bx._Ptr;
      v10 = _Val->first._Mysize;
      if ( i < v10 )
        v10 = i;
      if ( !v10 )
        goto LABEL_16;
      v11 = v10;
      if ( v10 >= Mysize )
        v11 = Parent->_Myval.first._Mysize;
      v12 = v6->first._Myres < 0x10 ? (const char *)&v6->first._Bx : v6->first._Bx._Ptr;
      v13 = memcmp(v12, Buf, v11);
      v14 = v13 < 0;
      v6 = _Val;
      if ( !v13 )
      {
LABEL_16:
        if ( v10 >= Mysize )
          v15 = v10 != Mysize;
        else
          v15 = -1;
        v14 = v15 < 0;
      }
      v5 = v14;
      _Addleft = v14;
      Parent = v14 ? Parent->_Left : Parent->_Right;
      if ( Parent->_Isnil )
        break;
    }
    Myhead = _Wherenode;
    this = v22;
  }
  v16 = Myhead;
  _Val = (std::pair<std::string const ,Guild::CGuild *> *)Myhead;
  if ( v5 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v6)->_Ptr;
      v18 = result;
      result->first._Ptr = Ptr;
      result->second = 1;
      return v18;
    }
    std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&_Val);
    v16 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)_Val;
  }
  if ( v6->first._Myres < 0x10 )
    v19 = v6->first._Bx._Buf;
  else
    v19 = v6->first._Bx._Ptr;
  if ( std::string::compare(&v16->_Myval.first, 0, v16->_Myval.first._Mysize, v19, v6->first._Mysize) >= 0 )
  {
    v18 = result;
    result->first._Ptr = v16;
    result->second = 0;
  }
  else
  {
    v20 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Insert(
            v22,
            (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v6)->_Ptr;
    v18 = result;
    result->first._Ptr = v20;
    result->second = 1;
  }
  return v18;
}

//----- (00469AE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Erase(
      this,
      (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00469BA0) --------------------------------------------------------
void __thiscall Guild::CGuildMgr::Destroy(Guild::CGuildMgr *this)
{
  Guild::CGuildMgr *v1; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v3; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v4; // eax

  v1 = this;
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,Guild::FnDeleteSecond>(
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)this->m_GuildMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)this->m_GuildMap._Myhead,
    0);
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Erase(
    &v1->m_GuildMap,
    v1->m_GuildMap._Myhead->_Parent);
  v1->m_GuildMap._Myhead->_Parent = v1->m_GuildMap._Myhead;
  Myhead = v1->m_GuildMap._Myhead;
  v1->m_GuildMap._Mysize = 0;
  Myhead->_Left = Myhead;
  v3 = v1->m_GuildMap._Myhead;
  v1 = (Guild::CGuildMgr *)((char *)v1 + 12);
  v3->_Right = v3;
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Erase(
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *)v1,
    (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v1->m_GuildMap._Myhead->_Parent);
  v1->m_GuildMap._Myhead->_Parent = v1->m_GuildMap._Myhead;
  v4 = v1->m_GuildMap._Myhead;
  v1->m_GuildMap._Mysize = 0;
  v4->_Left = v4;
  v1->m_GuildMap._Myhead->_Right = v1->m_GuildMap._Myhead;
}

//----- (00469C10) --------------------------------------------------------
char __thiscall Guild::CGuildMgr::CreateGuild(
        Guild::CGuildMgr *this,
        unsigned int dwMasterID,
        unsigned int dwGuildID,
        unsigned __int8 cInclination,
        char *szGuildName)
{
  Guild::CGuild *v6; // eax
  char *v7; // esi
  Guild::CGuild *v8; // eax
  Guild::CGuild *v9; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *v10; // ebp
  int v12; // edx
  __int16 v13; // cx
  CCreatureManager *Instance; // eax
  bool v15; // [esp+13h] [ebp-89h]
  std::pair<unsigned long const ,Guild::CGuild *> _Val; // [esp+14h] [ebp-88h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *p_m_GuildMap; // [esp+1Ch] [ebp-80h]
  std::string _Right; // [esp+20h] [ebp-7Ch] BYREF
  std::pair<std::string const ,Guild::CGuild *> v19; // [esp+3Ch] [ebp-60h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> result; // [esp+5Ch] [ebp-40h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> v21; // [esp+64h] [ebp-38h] BYREF
  PktCreateGuild pktCG; // [esp+6Ch] [ebp-30h] BYREF
  int v23; // [esp+98h] [ebp-4h]

  p_m_GuildMap = &this->m_GuildMap;
  v6 = (Guild::CGuild *)operator new((tagHeader *)0x278);
  _Val.first = (const unsigned int)v6;
  v7 = szGuildName;
  v23 = 0;
  if ( v6 )
  {
    Guild::CGuild::CGuild(v6, dwGuildID, cInclination, szGuildName);
    v9 = v8;
  }
  else
  {
    v9 = 0;
  }
  v23 = -1;
  _Val.first = dwGuildID;
  _Val.second = v9;
  if ( !std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::insert(
          &this->m_GuildMap,
          &result,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)&_Val)->second )
  {
LABEL_11:
    if ( v9 )
    {
      Guild::CGuild::~CGuild(v9);
      operator delete(v9);
    }
    return 0;
  }
  _Right._Myres = 15;
  _Right._Mysize = 0;
  _Right._Bx._Buf[0] = 0;
  std::string::assign(&_Right, v7, strlen(v7));
  v23 = 1;
  v19.first._Myres = 15;
  v19.first._Mysize = 0;
  v19.first._Bx._Buf[0] = 0;
  std::string::assign(&v19.first, &_Right, 0, 0xFFFFFFFF);
  v19.second = v9;
  v10 = p_m_GuildMap;
  LOBYTE(v23) = 2;
  v15 = !std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::insert(
           (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *)&p_m_GuildMap[1],
           &v21,
           &v19)->second;
  if ( v19.first._Myres >= 0x10 )
    operator delete(v19.first._Bx._Ptr);
  v19.first._Myres = 15;
  v19.first._Mysize = 0;
  v19.first._Bx._Buf[0] = 0;
  v23 = -1;
  if ( _Right._Myres >= 0x10 )
    operator delete(_Right._Bx._Ptr);
  _Right._Myres = 15;
  _Right._Mysize = 0;
  _Right._Bx._Buf[0] = 0;
  if ( v15 )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
      v10,
      &dwGuildID);
    goto LABEL_11;
  }
  v12 = *(_DWORD *)v7;
  pktCG.m_dwGID = dwGuildID;
  *(_DWORD *)&pktCG.m_szGuildName[4] = *((_DWORD *)v7 + 1);
  pktCG.m_cInclination = cInclination;
  v13 = *((_WORD *)v7 + 4);
  *(_DWORD *)pktCG.m_szGuildName = v12;
  LOBYTE(v12) = v7[10];
  pktCG.m_dwCID = 0;
  *(_WORD *)&pktCG.m_szGuildName[8] = v13;
  pktCG.m_szGuildName[10] = v12;
  if ( PacketWrap::WrapCrypt((char *)&pktCG, 0x20u, 0x88u, 0, 0) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, (char *)&pktCG, 0x20u, 0x88u, 1);
  }
  return 1;
}
// 469C7E: variable 'v8' is possibly undefined

//----- (00469E70) --------------------------------------------------------
char __thiscall Guild::CGuildMgr::GetSortingPageList(
        Guild::CGuildMgr *this,
        CCharacter *lpCharacter,
        unsigned __int8 cSortCmd,
        unsigned __int8 cPage,
        GuildLargeInfoNode *aryCurrentInfoList)
{
  CCharacter_vtbl *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // eax
  Guild::CGuild *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Mysize; // ebx
  GuildLargeInfoNode *v10; // eax
  int v11; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v12; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator v13; // ecx
  bool v14; // zf
  std::pair<unsigned long,Guild::CGuild *> *Mylast; // esi
  std::pair<unsigned long,Guild::CGuild *> *Myfirst; // edx
  int v17; // eax
  GuildLargeInfoNode *v18; // eax
  int v19; // ecx
  unsigned __int16 v20; // si
  std::pair<unsigned long,Guild::CGuild *> *v21; // edi
  Guild::CGuild *Guild; // eax
  const GuildLargeInfoNode *LargeInfo; // eax
  char v24; // dl
  unsigned int v26; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v27; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v28; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v29; // ecx
  GuildLargeInfoNode *v30; // eax
  int v31; // ecx
  CLotteryEvent::LotteryEventItem *v32; // eax
  unsigned __int16 v33; // si
  Guild::CGuild *v34; // eax
  const GuildLargeInfoNode *v35; // eax
  char v36; // dl
  unsigned int v37; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v38; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v39; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v40; // ecx
  GuildLargeInfoNode *v41; // eax
  int v42; // ecx
  CLotteryEvent::LotteryEventItem *v43; // eax
  unsigned __int16 v44; // si
  Guild::CGuild *v45; // eax
  const GuildLargeInfoNode *v46; // eax
  char v47; // dl
  unsigned int v48; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v49; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Left; // eax
  __int16 MSecond_high; // bx
  char *v52; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *v53; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v54; // edi
  const std::pair<unsigned long,Guild::RelationInfo> *v55; // eax
  std::pair<unsigned long,Guild::RelationInfo> *v56; // edi
  GuildLargeInfoNode *v57; // eax
  int v58; // ecx
  CLotteryEvent::LotteryEventItem *v59; // eax
  unsigned __int16 v60; // si
  Guild::CGuild *v61; // eax
  const GuildLargeInfoNode *v62; // eax
  char v63; // dl
  unsigned __int16 v64; // di
  std::pair<unsigned long,Guild::CGuild *> *v65; // esi
  unsigned __int8 v66; // bl
  Guild::CGuild *second; // ecx
  unsigned __int16 v68; // ax
  std::pair<unsigned long,Guild::CGuild *> *v69; // edx
  unsigned __int16 v70; // si
  unsigned __int8 CurrentMemberNum; // al
  unsigned __int8 *v72; // eax
  unsigned __int8 *v73; // esi
  std::pair<unsigned long,Guild::CGuild *> *v74; // eax
  std::pair<unsigned long,Guild::CGuild *> *v75; // esi
  int v76; // edx
  GuildLargeInfoNode *v77; // edi
  Guild::CGuild *v78; // ecx
  unsigned __int16 v79; // ax
  std::pair<unsigned long,Guild::CGuild *> *v80; // edx
  GuildLargeInfoNode *v81; // eax
  int v82; // ecx
  Guild::RelationInfo v83; // [esp-14h] [ebp-E08h]
  char *m_strName; // [esp-10h] [ebp-E04h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator wVectorSize; // [esp+10h] [ebp-DE4h] BYREF
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator it; // [esp+14h] [ebp-DE0h] BYREF
  int cIndex; // [esp+18h] [ebp-DDCh]
  CPartyMgr *v88; // [esp+1Ch] [ebp-DD8h]
  std::vector<std::pair<unsigned long,Guild::RelationInfo>> v89; // [esp+20h] [ebp-DD4h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator RelationIt; // [esp+30h] [ebp-DC4h] BYREF
  std::vector<std::pair<unsigned long,Guild::CGuild *>> sortVector; // [esp+34h] [ebp-DC0h] BYREF
  Guild::RelationInfo _Val2; // [esp+46h] [ebp-DAEh] BYREF
  std::pair<unsigned long,Guild::CGuild *> NullPair; // [esp+58h] [ebp-D9Ch] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v94; // [esp+60h] [ebp-D94h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v95; // [esp+64h] [ebp-D90h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> _Val; // [esp+68h] [ebp-D8Ch] BYREF
  std::pair<unsigned long,Guild::RelationInfo> v97; // [esp+80h] [ebp-D74h] BYREF
  std::pair<unsigned long,Guild::RelationInfo> v98; // [esp+98h] [ebp-D5Ch] BYREF
  Guild::MemberInfo v99; // [esp+B0h] [ebp-D44h] BYREF
  GuildLargeInfoNode v100; // [esp+EBh] [ebp-D09h] BYREF
  GuildLargeInfoNode result; // [esp+2C6h] [ebp-B2Eh] BYREF
  GuildLargeInfoNode v102; // [esp+4A1h] [ebp-953h] BYREF
  GuildLargeInfoNode v103; // [esp+67Ch] [ebp-778h] BYREF
  GuildLargeInfoNode v104; // [esp+857h] [ebp-59Dh] BYREF
  GuildLargeInfoNode v105; // [esp+A32h] [ebp-3C2h] BYREF
  GuildLargeInfoNode v106; // [esp+C0Dh] [ebp-1E7h] BYREF
  int v107; // [esp+DF0h] [ebp-4h]

  v5 = lpCharacter->__vftable;
  v88 = (CPartyMgr *)this;
  RelationIt._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)v5->GetGID(lpCharacter);
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    (const unsigned int *)&RelationIt);
  Myhead = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)this->m_GuildMap._Myhead;
  if ( it._Ptr == Myhead )
    Ptr = 0;
  else
    Ptr = (Guild::CGuild *)it._Ptr->_Myval.first._Bx._Ptr;
  Mysize = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)this->m_GuildMap._Mysize;
  LOWORD(Myhead) = cPage;
  RelationIt._Ptr = Mysize;
  cIndex = 10 * (_DWORD)Myhead;
  if ( (unsigned __int16)Mysize >= (unsigned __int16)(10 * cPage) )
  {
    memset(&sortVector._Myfirst, 0, 12);
    v107 = 0;
    NullPair.first = 0;
    NullPair.second = 0;
    std::vector<std::pair<unsigned long,Guild::CGuild *>>::reserve(&sortVector, (unsigned __int16)Mysize);
    std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Assign_n(&sortVector, (unsigned __int16)Mysize, &NullPair);
    v12 = this->m_GuildMap._Myhead;
    v13._Ptr = v12->_Left;
    v14 = v12->_Left == v12;
    Mylast = sortVector._Mylast;
    Myfirst = sortVector._Myfirst;
    LOBYTE(it._Ptr) = 0;
    if ( !v14 && sortVector._Myfirst != sortVector._Mylast )
    {
      std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long const,Guild::CGuild *>,Guild::CompareGuildFame>(
        (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *)&wVectorSize,
        v13,
        (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)v12,
        (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)sortVector._Myfirst,
        (std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator)sortVector._Mylast);
      Myfirst = sortVector._Myfirst;
    }
    if ( Myfirst
      && (v17 = Mylast - Myfirst,
          (wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v17) != 0) )
    {
      switch ( cSortCmd )
      {
        case 0u:
          v20 = cIndex;
          LOBYTE(it._Ptr) = 0;
          while ( 2 )
          {
            if ( v20 >= (unsigned __int16)v17 )
              goto LABEL_93;
            v21 = sortVector._Myfirst;
            Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v88, sortVector._Myfirst[v20].first);
            if ( !Guild )
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "Guild::CGuildMgr::GetSortingPageList",
                aDWorkRylSource_15,
                400,
                aGid0x08x_18,
                v21[v20].first);
              operator delete(sortVector._Myfirst);
              return 1;
            }
            LargeInfo = Guild::CGuild::GetLargeInfo(Guild, &result, (unsigned __int8)it._Ptr, v20 + 1, Ptr);
            v24 = (char)it._Ptr;
            qmemcpy(&aryCurrentInfoList[LOBYTE(it._Ptr)], LargeInfo, sizeof(GuildLargeInfoNode));
            LOBYTE(it._Ptr) = v24 + 1;
            ++v20;
            if ( (unsigned __int8)(v24 + 1) < 0xAu )
            {
              LOWORD(v17) = wVectorSize._Ptr;
              continue;
            }
            goto LABEL_93;
          }
        case 1u:
          if ( !Ptr )
            goto LABEL_93;
          v26 = Ptr->m_FriendlyMap._Mysize;
          memset(&v89._Myfirst, 0, 12);
          LOBYTE(v107) = 1;
          wVectorSize._Ptr = 0;
          _Val2.m_cRelation = 0;
          _Val2.m_cState = 0;
          std::pair<unsigned long,Guild::RelationInfo>::pair<unsigned long,Guild::RelationInfo>(
            &_Val,
            (const unsigned int *)&wVectorSize,
            &_Val2);
          std::vector<std::pair<unsigned long,Guild::RelationInfo>>::reserve(
            &v89,
            (std::pair<unsigned long,Guild::RelationInfo> *)(unsigned __int16)v26);
          std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Assign_n(&v89, (unsigned __int16)v26, &_Val);
          v27 = Ptr->m_FriendlyMap._Myhead;
          v28 = v89._Myfirst;
          v29._Ptr = v27->_Left;
          LOBYTE(wVectorSize._Ptr) = 0;
          std::partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *)&wVectorSize,
            v29,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v27,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Myfirst,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Mylast);
          if ( std::vector<std::pair<unsigned long,Guild::RelationInfo>>::empty(&v89) )
          {
            v30 = aryCurrentInfoList;
            v31 = 10;
            do
            {
              v30->m_dwGID = 0;
              ++v30;
              --v31;
            }
            while ( v31 );
            goto LABEL_43;
          }
          v32 = std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size((std::vector<CLotteryEvent::LotteryEventItem> *)&v89);
          v33 = cIndex;
          wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v32;
          LOBYTE(it._Ptr) = 0;
          while ( 1 )
          {
            if ( v33 >= LOWORD(wVectorSize._Ptr) )
              goto LABEL_37;
            v34 = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v88, v28[v33].first);
            if ( !v34 )
              break;
            v35 = Guild::CGuild::GetLargeInfo(v34, &v105, (unsigned __int8)it._Ptr, v33 + 1, Ptr);
            v36 = (char)it._Ptr;
            qmemcpy(&aryCurrentInfoList[LOBYTE(it._Ptr)], v35, sizeof(GuildLargeInfoNode));
            LOBYTE(it._Ptr) = v36 + 1;
            ++v33;
            if ( (unsigned __int8)(v36 + 1) >= 0xAu )
              goto LABEL_37;
            v28 = v89._Myfirst;
          }
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Guild::CGuildMgr::GetSortingPageList",
            aDWorkRylSource_15,
            441,
            aGid0x08x_18,
            v28[v33].first);
          goto LABEL_37;
        case 2u:
          if ( !Ptr )
            goto LABEL_93;
          v37 = Ptr->m_HostilityMap._Mysize;
          memset(&v89._Myfirst, 0, 12);
          LOBYTE(v107) = 2;
          wVectorSize._Ptr = 0;
          _Val2.m_cRelation = 0;
          _Val2.m_cState = 0;
          std::pair<unsigned long,Guild::RelationInfo>::pair<unsigned long,Guild::RelationInfo>(
            &_Val,
            (const unsigned int *)&wVectorSize,
            &_Val2);
          std::vector<std::pair<unsigned long,Guild::RelationInfo>>::reserve(
            &v89,
            (std::pair<unsigned long,Guild::RelationInfo> *)(unsigned __int16)v37);
          std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Assign_n(&v89, (unsigned __int16)v37, &_Val);
          v38 = Ptr->m_HostilityMap._Myhead;
          v39 = v89._Myfirst;
          v40._Ptr = v38->_Left;
          LOBYTE(wVectorSize._Ptr) = 0;
          std::partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *)&wVectorSize,
            v40,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v38,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Myfirst,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Mylast);
          if ( !std::vector<std::pair<unsigned long,Guild::RelationInfo>>::empty(&v89) )
          {
            v43 = std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size((std::vector<CLotteryEvent::LotteryEventItem> *)&v89);
            v44 = cIndex;
            wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v43;
            LOBYTE(it._Ptr) = 0;
            while ( 1 )
            {
              if ( v44 >= LOWORD(wVectorSize._Ptr) )
                goto LABEL_37;
              v45 = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v88, v39[v44].first);
              if ( !v45 )
                break;
              v46 = Guild::CGuild::GetLargeInfo(v45, &v103, (unsigned __int8)it._Ptr, v44 + 1, Ptr);
              v47 = (char)it._Ptr;
              qmemcpy(&aryCurrentInfoList[LOBYTE(it._Ptr)], v46, sizeof(GuildLargeInfoNode));
              LOBYTE(it._Ptr) = v47 + 1;
              ++v44;
              if ( (unsigned __int8)(v47 + 1) >= 0xAu )
                goto LABEL_37;
              v39 = v89._Myfirst;
            }
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "Guild::CGuildMgr::GetSortingPageList",
              aDWorkRylSource_15,
              483,
              aGid0x08x_18,
              v39[v44].first);
LABEL_37:
            std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v89);
            operator delete(sortVector._Myfirst);
            return 1;
          }
          v41 = aryCurrentInfoList;
          v42 = 10;
          do
          {
            v41->m_dwGID = 0;
            ++v41;
            --v42;
          }
          while ( v42 );
LABEL_43:
          std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v89);
          std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&sortVector);
          return 1;
        case 3u:
          v48 = this->m_GuildMap._Mysize;
          memset(&v89._Myfirst, 0, 12);
          LOBYTE(v107) = 3;
          std::vector<std::pair<unsigned long,Guild::RelationInfo>>::reserve(
            &v89,
            (std::pair<unsigned long,Guild::RelationInfo> *)(unsigned __int16)v48);
          v49 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)this->m_GuildMap._Myhead;
          Left = v49->_Left;
          it._Ptr = v49->_Left;
          if ( it._Ptr != v49 )
          {
            MSecond_high = HIWORD(_Val2.m_WaitTime.MSecond);
            do
            {
              v52 = Left->_Myval.first._Bx._Ptr;
              if ( !v52 )
                break;
              if ( Ptr )
              {
                wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)*((_DWORD *)v52 + 1);
                if ( std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                       &Ptr->m_FriendlyMap,
                       &v95,
                       (const unsigned int *)&wVectorSize)->_Ptr == Ptr->m_FriendlyMap._Myhead )
                {
                  wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)*((_DWORD *)v52 + 1);
                  if ( std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                         &Ptr->m_HostilityMap,
                         &v94,
                         (const unsigned int *)&wVectorSize)->_Ptr == Ptr->m_HostilityMap._Myhead
                    && v52 != (char *)Ptr )
                  {
                    wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)*((_DWORD *)v52 + 1);
                    v53 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                            &Ptr->m_NeutralityMap,
                            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&NullPair,
                            (const unsigned int *)&wVectorSize);
                    v54 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)*((_DWORD *)v52 + 1);
                    if ( v53->_Ptr == Ptr->m_NeutralityMap._Myhead )
                    {
                      _Val2.m_cRelation = 0;
                      _Val2.m_cState = 0;
                      *(_QWORD *)&v83.m_cRelation = *(_QWORD *)&_Val2.m_cRelation;
                      *(_QWORD *)&v83.m_WaitTime.Hour = *(_QWORD *)&_Val2.m_WaitTime.Hour;
                      HIWORD(v83.m_WaitTime.MSecond) = MSecond_high;
                      v55 = std::make_pair<unsigned long,Guild::RelationInfo>(&v98, (unsigned int)v54, v83);
                    }
                    else
                    {
                      wVectorSize._Ptr = v54;
                      std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                        &Ptr->m_NeutralityMap,
                        &RelationIt,
                        (const unsigned int *)&wVectorSize);
                      v55 = std::make_pair<unsigned long,Guild::RelationInfo>(
                              &v97,
                              (unsigned int)v54,
                              RelationIt._Ptr->_Myval.second);
                    }
                    std::vector<std::pair<unsigned long,Guild::RelationInfo>>::push_back(&v89, v55);
                  }
                }
              }
              std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&it);
              Left = it._Ptr;
            }
            while ( it._Ptr != (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)v88->m_PartyMap._Myhead );
          }
          v56 = v89._Myfirst;
          LOBYTE(wVectorSize._Ptr) = 0;
          std::sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Myfirst,
            (std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator)v89._Mylast,
            0);
          if ( std::vector<std::pair<unsigned long,Guild::RelationInfo>>::empty(&v89) )
          {
            v57 = aryCurrentInfoList;
            v58 = 10;
            do
            {
              v57->m_dwGID = 0;
              ++v57;
              --v58;
            }
            while ( v58 );
            if ( v56 )
              operator delete(v56);
            goto LABEL_93;
          }
          v59 = std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size((std::vector<CLotteryEvent::LotteryEventItem> *)&v89);
          v60 = cIndex;
          wVectorSize._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v59;
          LOBYTE(it._Ptr) = 0;
          while ( 2 )
          {
            if ( v60 < LOWORD(wVectorSize._Ptr) )
            {
              v61 = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v88, v56[v60].first);
              if ( v61 )
              {
                v62 = Guild::CGuild::GetLargeInfo(v61, &v100, (unsigned __int8)it._Ptr, v60 + 1, Ptr);
                v63 = (char)it._Ptr;
                qmemcpy(&aryCurrentInfoList[LOBYTE(it._Ptr)], v62, sizeof(GuildLargeInfoNode));
                v56 = v89._Myfirst;
                LOBYTE(it._Ptr) = v63 + 1;
                ++v60;
                if ( (unsigned __int8)(v63 + 1) >= 0xAu )
                  break;
                continue;
              }
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "Guild::CGuildMgr::GetSortingPageList",
                aDWorkRylSource_15,
                550,
                aGid0x08x_18,
                v56[v60].first);
            }
            break;
          }
          if ( !v56 )
            goto LABEL_93;
          operator delete(v56);
          operator delete(sortVector._Myfirst);
          return 1;
        case 4u:
          it._Ptr = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)v88->m_MemberFindPartyMap._Myhead->_Left;
          std::advance<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::iterator,unsigned short>(
            (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&it,
            cIndex);
          v64 = (unsigned __int16)RelationIt._Ptr;
          v65 = sortVector._Myfirst;
          v66 = 0;
          LOBYTE(cIndex) = 0;
          do
          {
            if ( it._Ptr == (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)v88->m_MemberFindPartyMap._Myhead )
              break;
            second = it._Ptr->_Myval.second;
            if ( !second )
              break;
            v68 = 0;
            if ( v64 )
            {
              while ( 1 )
              {
                v69 = &v65[v68++];
                if ( v69->second == second )
                  break;
                if ( v68 >= v64 )
                  goto LABEL_87;
              }
              qmemcpy(
                &aryCurrentInfoList[v66],
                Guild::CGuild::GetLargeInfo(second, &v102, cIndex, v68, Ptr),
                sizeof(GuildLargeInfoNode));
              v64 = (unsigned __int16)RelationIt._Ptr;
              v65 = sortVector._Myfirst;
            }
LABEL_87:
            LOBYTE(cIndex) = ++v66;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *)&it);
          }
          while ( v66 < 0xAu );
          goto LABEL_93;
        case 5u:
          if ( !Ptr )
            goto LABEL_93;
          v70 = 0;
          if ( !(_WORD)Mysize )
            goto LABEL_93;
          while ( Myfirst[v70].second != Ptr )
          {
            if ( ++v70 >= (unsigned __int16)Mysize )
              goto LABEL_93;
          }
          m_strName = Guild::CGuild::GetMaster(Ptr, &v99)->m_strName;
          CurrentMemberNum = Guild::CGuild::GetCurrentMemberNum(Ptr);
          GuildLargeInfoNode::GuildLargeInfoNode(
            &v104,
            Ptr->m_dwGID,
            0,
            Ptr->m_cInclination,
            v70 + 1,
            Ptr->m_dwFame,
            Ptr->m_cLevel,
            CurrentMemberNum,
            m_strName,
            Ptr->m_strName,
            Ptr->m_szMark,
            3u);
          v73 = v72;
          v74 = sortVector._Myfirst;
          qmemcpy(aryCurrentInfoList, v73, 0x1D8u);
          v73 += 472;
          *(_WORD *)&aryCurrentInfoList->m_szMark[431] = *(_WORD *)v73;
          aryCurrentInfoList->m_cRelation = v73[2];
          operator delete(v74);
          return 1;
        case 6u:
          v75 = sortVector._Myfirst;
          LOBYTE(cIndex) = 0;
          break;
        default:
          v81 = aryCurrentInfoList;
          v82 = 10;
          do
          {
            v81->m_dwGID = 0;
            ++v81;
            --v82;
          }
          while ( v82 );
          operator delete(sortVector._Myfirst);
          return 0;
      }
      do
      {
        v76 = (unsigned __int8)cIndex;
        v77 = &aryCurrentInfoList[v76];
        if ( !aryCurrentInfoList[v76].m_dwGID )
          break;
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
          (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)v88,
          (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&wVectorSize,
          &aryCurrentInfoList[v76].m_dwGID);
        if ( wVectorSize._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)v88->m_PartyMap._Myhead )
        {
          v77->m_dwGID = 0;
        }
        else
        {
          v78 = wVectorSize._Ptr->_Myval.second;
          if ( !v78 )
            break;
          v79 = 0;
          if ( (_WORD)Mysize )
          {
            while ( 1 )
            {
              v80 = &v75[v79++];
              if ( v80->second == v78 )
                break;
              if ( v79 >= (unsigned __int16)Mysize )
                goto LABEL_105;
            }
            qmemcpy(v77, Guild::CGuild::GetLargeInfo(v78, &v106, cIndex, v79, Ptr), sizeof(GuildLargeInfoNode));
            v75 = sortVector._Myfirst;
          }
        }
LABEL_105:
        LOBYTE(cIndex) = cIndex + 1;
      }
      while ( (unsigned __int8)cIndex < 0xAu );
    }
    else
    {
      v18 = aryCurrentInfoList;
      v19 = 10;
      do
      {
        v18->m_dwGID = 0;
        ++v18;
        --v19;
      }
      while ( v19 );
      if ( !Myfirst )
        return 1;
    }
LABEL_93:
    operator delete(sortVector._Myfirst);
    return 1;
  }
  else
  {
    if ( Mysize )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuildMgr::GetSortingPageList",
        aDWorkRylSource_15,
        360,
        aCid0x08x_203,
        lpCharacter->m_dwCID,
        (unsigned __int16)Mysize,
        cPage);
    v10 = aryCurrentInfoList;
    v11 = 10;
    do
    {
      v10->m_dwGID = 0;
      ++v10;
      --v11;
    }
    while ( v11 );
    return 0;
  }
}
// 46A851: variable 'v72' is possibly undefined

//----- (0046A9A0) --------------------------------------------------------
char __thiscall Guild::CGuildMgr::SerializeIn(
        Guild::CGuildMgr *this,
        char *lpBuffer_In,
        unsigned __int16 wBufferSize_In,
        unsigned int cTotalMemberNum,
        unsigned __int8 cFriendlyNum,
        unsigned __int8 cHostilityNum,
        unsigned __int8 cNeutralityNum)
{
  Guild::CGuild *v8; // eax
  Guild::CGuild *v9; // eax
  Guild::CGuild *v10; // esi
  unsigned int m_dwGID; // edx
  GuildMemberDB *v13; // edi
  int v14; // ebp
  int v15; // ebp
  unsigned int *p_m_dwCID; // edi
  unsigned int v17; // [esp-Ch] [ebp-88h]
  bool v18; // [esp+Fh] [ebp-6Dh]
  std::pair<unsigned long const ,Guild::CGuild *> _Val; // [esp+10h] [ebp-6Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> result; // [esp+18h] [ebp-64h] BYREF
  Guild::RelationInfo Info; // [esp+20h] [ebp-5Ch] BYREF
  std::string _Right; // [esp+34h] [ebp-48h] BYREF
  std::pair<std::string const ,Guild::CGuild *> v23; // [esp+50h] [ebp-2Ch] BYREF
  int v24; // [esp+78h] [ebp-4h]

  v8 = (Guild::CGuild *)operator new((tagHeader *)0x278);
  _Val.first = (const unsigned int)v8;
  v24 = 0;
  if ( v8 )
  {
    Guild::CGuild::CGuild(v8, (GuildInfoDB *)lpBuffer_In);
    v10 = v9;
  }
  else
  {
    v10 = 0;
  }
  m_dwGID = v10->m_dwGID;
  v24 = -1;
  _Val.first = m_dwGID;
  _Val.second = v10;
  if ( std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::insert(
         &this->m_GuildMap,
         &result,
         (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)&_Val)->second )
  {
    _Right._Myres = 15;
    _Right._Mysize = 0;
    _Right._Bx._Buf[0] = 0;
    std::string::assign(&_Right, v10->m_strName, strlen(v10->m_strName));
    v24 = 1;
    v23.first._Myres = 15;
    v23.first._Mysize = 0;
    v23.first._Bx._Buf[0] = 0;
    std::string::assign(&v23.first, &_Right, 0, 0xFFFFFFFF);
    v23.second = v10;
    LOBYTE(v24) = 2;
    v18 = !std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::insert(
             &this->m_GuildNameMap,
             (std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *)&result,
             &v23)->second;
    if ( v23.first._Myres >= 0x10 )
      operator delete(v23.first._Bx._Ptr);
    v23.first._Myres = 15;
    v23.first._Mysize = 0;
    v23.first._Bx._Buf[0] = 0;
    v24 = -1;
    if ( _Right._Myres >= 0x10 )
      operator delete(_Right._Bx._Ptr);
    if ( v18 )
    {
      cTotalMemberNum = v10->m_dwGID;
      std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
        &this->m_GuildMap,
        &cTotalMemberNum);
      Guild::CGuild::~CGuild(v10);
      operator delete(v10);
      return 0;
    }
    else
    {
      v13 = (GuildMemberDB *)(lpBuffer_In + 581);
      if ( (_BYTE)cTotalMemberNum )
      {
        v14 = (unsigned __int8)cTotalMemberNum;
        do
        {
          Guild::CGuild::JoinMemberDB(v10, v13++);
          --v14;
        }
        while ( v14 );
      }
      v15 = cHostilityNum + cFriendlyNum + cNeutralityNum;
      if ( v15 > 0 )
      {
        p_m_dwCID = &v13->m_dwCID;
        do
        {
          v17 = *p_m_dwCID;
          Info.m_cRelation = *((_BYTE *)p_m_dwCID + 4);
          Info.m_cState = 0;
          Guild::CGuild::SetRelation(v10, 1u, v17, &Info);
          p_m_dwCID = (unsigned int *)((char *)p_m_dwCID + 9);
          --v15;
        }
        while ( v15 );
      }
      return 1;
    }
  }
  else
  {
    Guild::CGuild::~CGuild(v10);
    operator delete(v10);
    return 0;
  }
}
// 46A9E7: variable 'v9' is possibly undefined

//----- (0046ABE0) --------------------------------------------------------
void __thiscall std::map<std::string,Guild::CGuild *>::~map<std::string,Guild::CGuild *>(
        std::map<std::string,Guild::CGuild *> *this)
{
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (0046AC10) --------------------------------------------------------
void __thiscall Guild::CGuildMgr::~CGuildMgr(Guild::CGuildMgr *this)
{
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator v2; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator v3; // [esp-8h] [ebp-28h]
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Myhead; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v5; // [esp-4h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator result; // [esp+10h] [ebp-10h] BYREF
  int v7; // [esp+1Ch] [ebp-4h]

  v7 = 1;
  Guild::CGuildMgr::Destroy(this);
  Myhead = this->m_GuildNameMap._Myhead;
  v2._Ptr = Myhead->_Left;
  LOBYTE(v7) = 0;
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
    &this->m_GuildNameMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator)Myhead);
  operator delete(this->m_GuildNameMap._Myhead);
  this->m_GuildNameMap._Myhead = 0;
  this->m_GuildNameMap._Mysize = 0;
  v5 = this->m_GuildMap._Myhead;
  v3._Ptr = v5->_Left;
  v7 = -1;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
    &this->m_GuildMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *)&result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)v5);
  operator delete(this->m_GuildMap._Myhead);
  this->m_GuildMap._Myhead = 0;
  this->m_GuildMap._Mysize = 0;
}

//----- (0046ACB0) --------------------------------------------------------
char __thiscall Guild::CGuildMgr::DissolveGuild(Guild::CGuildMgr *this, unsigned int dwGID)
{
  Guild::CGuild *second; // ebx
  Guild::MemberInfo *Mylast; // ebp
  unsigned int v6; // eax
  CCellManager::SafetyZoneInfo *const *Myfirst; // edi
  unsigned int *i; // esi
  unsigned int *j; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Parent; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Left; // eax
  Guild::CGuild *v13; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v14; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v15; // eax
  bool v16; // zf
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v17; // edi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v19; // edx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *Right; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *m; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *k; // ecx
  CCreatureManager *Instance; // eax
  unsigned int _Keyval; // [esp+4h] [ebp-74h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator RelationIt; // [esp+8h] [ebp-70h] BYREF
  std::vector<unsigned long> dissolveList; // [esp+Ch] [ebp-6Ch] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator tempIt; // [esp+1Ch] [ebp-5Ch] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator it; // [esp+20h] [ebp-58h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator v29; // [esp+24h] [ebp-54h] BYREF
  std::string v30; // [esp+28h] [ebp-50h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator result; // [esp+44h] [ebp-34h] BYREF
  PktCreateGuild pktCG; // [esp+48h] [ebp-30h] BYREF
  int v33; // [esp+74h] [ebp-4h]

  RelationIt._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)this;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&it,
    &dwGID);
  if ( it._Ptr == this->m_GuildMap._Myhead )
    return 0;
  second = it._Ptr->_Myval.second;
  Mylast = second->m_MemberList._Mylast;
  v6 = 0;
  Myfirst = (CCellManager::SafetyZoneInfo *const *)second->m_MemberList._Myfirst;
  memset(&dissolveList._Myfirst, 0, 12);
  v33 = 0;
  if ( Myfirst )
    v6 = ((char *)Mylast - (char *)Myfirst) / 56;
  std::vector<unsigned long>::reserve(&dissolveList, v6);
  for ( i = dissolveList._Mylast; Myfirst != (CCellManager::SafetyZoneInfo *const *)Mylast; Myfirst += 14 )
  {
    if ( dissolveList._Myfirst
      && i - dissolveList._Myfirst < (unsigned int)(dissolveList._Myend - dissolveList._Myfirst) )
    {
      *i++ = (unsigned int)*Myfirst;
      dissolveList._Mylast = i;
    }
    else
    {
      std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
        (std::vector<CCellManager::SafetyZoneInfo *> *)&dissolveList,
        (std::vector<CCellManager::SafetyZoneInfo *>::iterator)i,
        1u,
        Myfirst);
      i = dissolveList._Mylast;
    }
  }
  for ( j = dissolveList._Myfirst; j != i; ++j )
    Guild::CGuild::LeaveMember(second, *j);
  Ptr = RelationIt._Ptr;
  Parent = RelationIt._Ptr->_Parent;
  Left = Parent->_Left;
  RelationIt._Ptr = Parent->_Left;
  if ( RelationIt._Ptr != Parent )
  {
    do
    {
      v13 = Left->_Myval.second;
      if ( v13 && v13 != second )
      {
        _Keyval = second->m_dwGID;
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
          &v13->m_FriendlyMap,
          &tempIt,
          &_Keyval);
        if ( tempIt._Ptr != v13->m_FriendlyMap._Myhead )
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
            &v13->m_FriendlyMap,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Keyval,
            tempIt);
        _Keyval = second->m_dwGID;
        v14 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                &v13->m_HostilityMap,
                &result,
                &_Keyval)->_Ptr;
        if ( v14 != v13->m_HostilityMap._Myhead )
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
            &v13->m_HostilityMap,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Keyval,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v14);
        _Keyval = second->m_dwGID;
        v15 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
                &v13->m_NeutralityMap,
                &v29,
                &_Keyval)->_Ptr;
        v16 = v15 == v13->m_NeutralityMap._Myhead;
        tempIt._Ptr = v15;
        if ( !v16 )
          std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(
            &v13->m_NeutralityMap,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *)&_Keyval,
            (std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator)v15);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&RelationIt);
      Left = RelationIt._Ptr;
    }
    while ( RelationIt._Ptr != Ptr->_Parent );
  }
  v30._Myres = 15;
  v30._Mysize = 0;
  v30._Bx._Buf[0] = 0;
  std::string::assign(&v30, second->m_strName, strlen(second->m_strName));
  LOBYTE(v33) = 1;
  v17 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Ubound(
          (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *)&Ptr->_Myval,
          &v30);
  v18 = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lbound(
          (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)&Ptr->_Myval,
          &v30);
  v19 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)v18;
  if ( v18 != (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)v17 )
  {
    do
    {
      if ( !v19->_Isnil )
      {
        Right = v19->_Right;
        if ( Right->_Isnil )
        {
          for ( k = v19->_Parent; !k->_Isnil; k = k->_Parent )
          {
            if ( v19 != k->_Right )
              break;
            v19 = k;
          }
          v19 = k;
        }
        else
        {
          v19 = v19->_Right;
          for ( m = Right->_Left; !m->_Isnil; m = m->_Left )
            v19 = m;
        }
      }
    }
    while ( v19 != v17 );
  }
  std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *)&Ptr->_Myval,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *)&v29,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator)v18,
    (std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator)v17);
  LOBYTE(v33) = 0;
  if ( v30._Myres >= 0x10 )
    operator delete(v30._Bx._Ptr);
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *)Ptr,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *)&v29,
    it);
  if ( second )
  {
    Guild::CGuild::~CGuild(second);
    operator delete(second);
  }
  pktCG.m_dwGID = dwGID;
  if ( PacketWrap::WrapCrypt((char *)&pktCG, 0x20u, 0x88u, 0, 3u) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, (char *)&pktCG, 0x20u, 0x88u, 1);
  }
  if ( dissolveList._Myfirst )
    operator delete(dissolveList._Myfirst);
  return 1;
}

//----- (0046B010) --------------------------------------------------------
char __thiscall Guild::CGuildMgr::SendGuildList(
        Guild::CGuildMgr *this,
        unsigned int dwCID,
        unsigned __int8 cSortCmd,
        unsigned __int8 cPage,
        unsigned __int8 cNum,
        GuildCheckSumNode *lpNode)
{
  CPerformanceCheck *Instance; // eax
  CCreatureManager *v7; // eax
  _BYTE *v9; // ebp
  unsigned __int8 *p_m_cInclination; // edx
  int v11; // esi
  _DWORD *v12; // ecx
  _BYTE *v13; // edi
  GuildCheckSumNode *v14; // ebx
  int v15; // eax
  GuildLargeInfoNode *p_aryCurrentInfoList; // ecx
  unsigned int m_dwCheckSum; // edx
  int v18; // esi
  signed int LowPart; // edi
  GuildLargeInfoNode *v20; // ebp
  _BYTE *v21; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // ecx
  char v24; // bl
  Guild::CGuildMgr *v26; // [esp+10h] [ebp-2578h]
  _BYTE *v27; // [esp+14h] [ebp-2574h]
  unsigned int dwCrc32; // [esp+18h] [ebp-2570h] BYREF
  CCharacter *lpCharacter; // [esp+1Ch] [ebp-256Ch]
  _LARGE_INTEGER v30; // [esp+20h] [ebp-2568h]
  CPerformanceInstrument v31; // [esp+28h] [ebp-2560h] BYREF
  CPerformanceInstrument *v32; // [esp+40h] [ebp-2548h]
  char SourceData[12]; // [esp+44h] [ebp-2544h] BYREF
  unsigned int v34; // [esp+50h] [ebp-2538h]
  unsigned __int8 v35; // [esp+54h] [ebp-2534h]
  unsigned __int8 v36; // [esp+55h] [ebp-2533h]
  unsigned __int8 v37; // [esp+56h] [ebp-2532h]
  unsigned __int8 v38; // [esp+57h] [ebp-2531h]
  _BYTE v39[4752]; // [esp+58h] [ebp-2530h] BYREF
  GuildLargeInfoNode aryCurrentInfoList; // [esp+12E8h] [ebp-12A0h] BYREF
  _DWORD v41[118]; // [esp+14C3h] [ebp-10C5h] BYREF
  __int16 v42; // [esp+169Bh] [ebp-EEDh]
  char v43; // [esp+169Dh] [ebp-EEBh]
  _DWORD v44[118]; // [esp+169Eh] [ebp-EEAh] BYREF
  __int16 v45; // [esp+1876h] [ebp-D12h]
  char v46; // [esp+1878h] [ebp-D10h]
  _DWORD v47[118]; // [esp+1879h] [ebp-D0Fh] BYREF
  __int16 v48; // [esp+1A51h] [ebp-B37h]
  char v49; // [esp+1A53h] [ebp-B35h]
  _DWORD v50[118]; // [esp+1A54h] [ebp-B34h] BYREF
  __int16 v51; // [esp+1C2Ch] [ebp-95Ch]
  char v52; // [esp+1C2Eh] [ebp-95Ah]
  _DWORD v53[118]; // [esp+1C2Fh] [ebp-959h] BYREF
  __int16 v54; // [esp+1E07h] [ebp-781h]
  char v55; // [esp+1E09h] [ebp-77Fh]
  _DWORD v56[118]; // [esp+1E0Ah] [ebp-77Eh] BYREF
  __int16 v57; // [esp+1FE2h] [ebp-5A6h]
  char v58; // [esp+1FE4h] [ebp-5A4h]
  _DWORD v59[118]; // [esp+1FE5h] [ebp-5A3h] BYREF
  __int16 v60; // [esp+21BDh] [ebp-3CBh]
  char v61; // [esp+21BFh] [ebp-3C9h]
  _DWORD v62[118]; // [esp+21C0h] [ebp-3C8h] BYREF
  __int16 v63; // [esp+2398h] [ebp-1F0h]
  char v64; // [esp+239Ah] [ebp-1EEh]
  _DWORD v65[118]; // [esp+239Bh] [ebp-1EDh] BYREF
  __int16 v66; // [esp+2573h] [ebp-15h]
  char v67; // [esp+2575h] [ebp-13h]
  int v68; // [esp+2584h] [ebp-4h]

  v31.m_szfunctionName = "Guild::CGuildMgr::SendGuildList";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "Guild::CGuildMgr::SendGuildList", 0.0);
  v32 = &v31;
  v31.m_stopTime.QuadPart = 0LL;
  v30.QuadPart = __rdtsc();
  v31.m_startTime = v30;
  v68 = 0;
  v7 = CCreatureManager::GetInstance();
  lpCharacter = CCreatureManager::GetCharacter(v7, dwCID);
  if ( lpCharacter )
  {
    if ( cNum > 0xAu )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuildMgr::SendGuildList",
        aDWorkRylSource_15,
        233,
        aCid0x08x_56,
        dwCID,
        cNum);
      cNum = 10;
    }
    v9 = v39;
    v34 = dwCID;
    v36 = cPage;
    v27 = v39;
    v35 = cSortCmd;
    v37 = 0;
    v38 = 0;
    p_m_cInclination = &aryCurrentInfoList.m_cInclination;
    v11 = 10;
    do
    {
      *(_DWORD *)(p_m_cInclination - 5) = 0;
      *(p_m_cInclination - 1) = 0;
      *p_m_cInclination = 0;
      *(_WORD *)(p_m_cInclination + 1) = 0;
      *(_DWORD *)(p_m_cInclination + 3) = 0;
      p_m_cInclination[7] = 0;
      p_m_cInclination[8] = 0;
      *(_DWORD *)(p_m_cInclination + 9) = 0;
      *(_DWORD *)(p_m_cInclination + 13) = 0;
      *(_WORD *)(p_m_cInclination + 17) = 0;
      p_m_cInclination[19] = 0;
      v12 = p_m_cInclination + 20;
      v13 = p_m_cInclination + 36;
      p_m_cInclination += 475;
      --v11;
      *v12 = 0;
      v12[1] = 0;
      v12[2] = 0;
      v12[3] = 0;
      *(p_m_cInclination - 6) = 0;
      memset(v13, 0, 0x1B0u);
      v13[432] = 0;
    }
    while ( v11 );
    v14 = lpNode;
    if ( cSortCmd == 6 )
    {
      v15 = 0;
      if ( cNum )
      {
        p_aryCurrentInfoList = &aryCurrentInfoList;
        do
        {
          p_aryCurrentInfoList->m_dwGID = lpNode[v15++].m_dwGID;
          ++p_aryCurrentInfoList;
        }
        while ( v15 < cNum );
      }
    }
    Guild::CGuildMgr::GetSortingPageList(this, lpCharacter, cSortCmd, cPage, &aryCurrentInfoList);
    v18 = 0;
    v26 = 0;
    if ( cSortCmd != 6 )
    {
      LowPart = cNum;
      v30.LowPart = cNum;
      if ( cNum )
      {
        v20 = &aryCurrentInfoList;
        do
        {
          if ( v18 >= 10 )
            break;
          dwCrc32 = 0;
          CCrc32Static::BufferCrc32((const char *)v20, 0x29u, &dwCrc32);
          if ( v14 && v14->m_dwGID == v20->m_dwGID )
          {
            m_dwCheckSum = v14->m_dwCheckSum;
            if ( m_dwCheckSum != dwCrc32 )
            {
              qmemcpy(v27, v20, 0x29u);
              v18 = (int)v26;
              LowPart = v30.LowPart;
              v27 += 41;
              ++v37;
            }
            v20->m_dwGID = 0;
          }
          ++v14;
          ++v18;
          ++v20;
          v26 = (Guild::CGuildMgr *)v18;
        }
        while ( v18 < LowPart );
        v9 = v27;
      }
    }
    v21 = v9;
    if ( aryCurrentInfoList.m_dwGID )
    {
      qmemcpy(v9, &aryCurrentInfoList, 0x1D8u);
      *((_WORD *)v9 + 236) = *(_WORD *)&aryCurrentInfoList.m_szMark[431];
      v9[474] = aryCurrentInfoList.m_cRelation;
      v21 = v9 + 475;
      ++v38;
    }
    if ( v41[0] )
    {
      qmemcpy(v21, v41, 0x1D8u);
      *((_WORD *)v21 + 236) = v42;
      v21[474] = v43;
      v21 += 475;
      ++v38;
    }
    if ( v44[0] )
    {
      qmemcpy(v21, v44, 0x1D8u);
      *((_WORD *)v21 + 236) = v45;
      v21[474] = v46;
      v21 += 475;
      ++v38;
    }
    if ( v47[0] )
    {
      qmemcpy(v21, v47, 0x1D8u);
      *((_WORD *)v21 + 236) = v48;
      v21[474] = v49;
      v21 += 475;
      ++v38;
    }
    if ( v50[0] )
    {
      qmemcpy(v21, v50, 0x1D8u);
      *((_WORD *)v21 + 236) = v51;
      v21[474] = v52;
      v21 += 475;
      ++v38;
    }
    if ( v53[0] )
    {
      qmemcpy(v21, v53, 0x1D8u);
      *((_WORD *)v21 + 236) = v54;
      v21[474] = v55;
      v21 += 475;
      ++v38;
    }
    if ( v56[0] )
    {
      qmemcpy(v21, v56, 0x1D8u);
      *((_WORD *)v21 + 236) = v57;
      v21[474] = v58;
      v21 += 475;
      ++v38;
    }
    if ( v59[0] )
    {
      qmemcpy(v21, v59, 0x1D8u);
      *((_WORD *)v21 + 236) = v60;
      v21[474] = v61;
      v21 += 475;
      ++v38;
    }
    if ( v62[0] )
    {
      qmemcpy(v21, v62, 0x1D8u);
      *((_WORD *)v21 + 236) = v63;
      v21[474] = v64;
      v21 += 475;
      ++v38;
    }
    if ( v65[0] )
    {
      qmemcpy(v21, v65, 0x1D8u);
      *((_WORD *)v21 + 236) = v66;
      v21[474] = v67;
      ++v38;
    }
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      LOWORD(m_dwCheckSum) = v38;
      p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
      LOWORD(m_lpGameClientDispatch) = v37;
      v24 = CSendStream::WrapCompress(
              p_m_SendStream,
              SourceData,
              (char *)(475 * m_dwCheckSum + 41 * (_DWORD)m_lpGameClientDispatch + 20),
              0x8Du,
              0,
              0);
      v68 = -1;
      CPerformanceInstrument::Stop(&v31);
      return v24;
    }
    else
    {
      v68 = -1;
      CPerformanceInstrument::Stop(&v31);
      return 1;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuildMgr::SendGuildList",
      aDWorkRylSource_15,
      226,
      aCid0x08x_256,
      dwCID);
    v68 = -1;
    CPerformanceInstrument::Stop(&v31);
    return 0;
  }
}
// 46B010: could not find valid save-restore pair for ebp
// 46B010: could not find valid save-restore pair for edi
// 46B496: variable 'm_dwCheckSum' is possibly undefined

//----- (0046B510) --------------------------------------------------------
void __thiscall Guild::CGuildMgr::CGuildMgr(Guild::CGuildMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *v3; // eax

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_GuildMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_GuildMap._Myhead->_Parent = this->m_GuildMap._Myhead;
  this->m_GuildMap._Myhead->_Left = this->m_GuildMap._Myhead;
  this->m_GuildMap._Myhead->_Right = this->m_GuildMap._Myhead;
  this->m_GuildMap._Mysize = 0;
  v3 = (std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *)&this->m_GuildNameMap);
  this->m_GuildNameMap._Myhead = v3;
  v3->_Isnil = 1;
  this->m_GuildNameMap._Myhead->_Parent = this->m_GuildNameMap._Myhead;
  this->m_GuildNameMap._Myhead->_Left = this->m_GuildNameMap._Myhead;
  this->m_GuildNameMap._Myhead->_Right = this->m_GuildNameMap._Myhead;
  this->m_GuildNameMap._Mysize = 0;
}

//----- (0046B5A0) --------------------------------------------------------
Guild::CGuildMgr *__cdecl Guild::CGuildMgr::GetInstance()
{
  if ( (_S5_9 & 1) == 0 )
  {
    _S5_9 |= 1u;
    Guild::CGuildMgr::CGuildMgr(&guildMgr);
    atexit(_E6_14);
  }
  return &guildMgr;
}

//----- (0046B600) --------------------------------------------------------
void __thiscall Item::CEquipmentsContainer::CEquipmentsContainer(Item::CEquipmentsContainer *this)
{
  Item::CListContainer::CListContainer(this);
  this->__vftable = (Item::CEquipmentsContainer_vtbl *)&Item::CEquipmentsContainer::`vftable';
  this->m_cRightHand = 11;
  this->m_cLeftHand = 9;
}
// 4E55C0: using guessed type void *Item::CEquipmentsContainer::`vftable';

//----- (0046B620) --------------------------------------------------------
void __thiscall Item::CEquipmentsContainer::~CEquipmentsContainer(Item::CEquipmentsContainer *this)
{
  this->__vftable = (Item::CEquipmentsContainer_vtbl *)&Item::CEquipmentsContainer::`vftable';
  Item::CArrayContainer::~CArrayContainer(this);
}
// 4E55C0: using guessed type void *Item::CEquipmentsContainer::`vftable';

//----- (0046B630) --------------------------------------------------------
char __thiscall Item::CEquipmentsContainer::Initialize(
        Item::CEquipmentsContainer *this,
        CCharacter *lpCharacter,
        unsigned __int16 nMaxSize)
{
  this->m_lpOwner = lpCharacter;
  return Item::CItemContainer::Initialize(this, lpCharacter->m_dwCID, nMaxSize);
}

//----- (0046B660) --------------------------------------------------------
char __thiscall Item::CEquipmentsContainer::RemoveItem(Item::CEquipmentsContainer *this, Item::ItemPos itemPos)
{
  if ( Item::CListContainer::RemoveItem(this, *(_WORD *)&itemPos) )
  {
    CCharacter::CalculateStatusData(this->m_lpOwner, 0);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CEquipmentsContainer::RemoveItem",
      aDWorkRylSource_97,
      67,
      (char *)&byte_4E5690);
    return 0;
  }
}

//----- (0046B6B0) --------------------------------------------------------
bool __thiscall Item::CEquipmentsContainer::CheckEquipPos(
        Item::CEquipmentsContainer *this,
        unsigned __int16 itemPos,
        Item::CItem *lpItem)
{
  bool v3; // zf
  int v5; // eax
  char v6; // al
  int v7; // eax
  char v8; // al
  int v9; // eax
  char v10; // al
  int v11; // eax
  char v12; // al
  int v13; // eax
  char v14; // al
  int v15; // eax
  char v16; // al
  int v17; // eax
  int v18; // eax
  int v19; // eax
  int v20; // eax
  int v21; // eax
  int v22; // eax
  unsigned __int8 v23; // al
  int v24; // edx
  Item::CItem *v25; // eax
  unsigned __int8 m_cItemType; // al
  unsigned __int8 v27; // al
  Item::CItem *v28; // eax
  unsigned __int8 v29; // al
  Item::CItem *v30; // eax
  int v31; // eax
  __int16 v32; // ax

  switch ( lpItem->m_ItemInfo->m_DetailData.m_cItemType )
  {
    case 0u:
    case 0x1Du:
      v3 = (itemPos & 0xFFF0) == 0;
      goto LABEL_3;
    case 1u:
      v3 = (itemPos & 0xFFF0) == 16;
      goto LABEL_3;
    case 2u:
      v3 = (itemPos & 0xFFF0) == 32;
      goto LABEL_3;
    case 3u:
    case 0x1Eu:
      v3 = (itemPos & 0xFFF0) == 48;
      goto LABEL_3;
    case 4u:
    case 0x1Fu:
      v3 = (itemPos & 0xFFF0) == 64;
      goto LABEL_3;
    case 5u:
    case 0x20u:
      v3 = (itemPos & 0xFFF0) == 80;
      goto LABEL_3;
    case 6u:
    case 7u:
    case 8u:
      switch ( itemPos >> 4 )
      {
        case 9:
          v9 = *((_DWORD *)this->m_lppItems + 11);
          if ( !v9 )
            return 0;
          v10 = *(_BYTE *)(*(_DWORD *)(v9 + 4) + 4);
          if ( v10 == 6 || v10 == 7 || v10 == 8 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            136,
            byte_4E5A08);
          break;
        case 0xA:
          v11 = *((_DWORD *)this->m_lppItems + 12);
          if ( !v11 )
            return 0;
          v12 = *(_BYTE *)(*(_DWORD *)(v11 + 4) + 4);
          if ( v12 == 6 || v12 == 7 || v12 == 8 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            149,
            byte_4E5A08);
          break;
        case 0xB:
          v5 = *((_DWORD *)this->m_lppItems + 9);
          if ( !v5 )
            return 1;
          v6 = *(_BYTE *)(*(_DWORD *)(v5 + 4) + 4);
          if ( v6 != 15 && v6 != 45 && v6 != 46 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            109,
            byte_4E5A08);
          return 0;
        case 0xC:
          v7 = *((_DWORD *)this->m_lppItems + 10);
          if ( !v7 )
            return 1;
          v8 = *(_BYTE *)(*(_DWORD *)(v7 + 4) + 4);
          if ( v8 != 15 && v8 != 45 && v8 != 46 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            123,
            byte_4E5A08);
          return 0;
        default:
          return 1;
      }
      return 0;
    case 9u:
    case 0xAu:
    case 0xBu:
    case 0xEu:
      if ( itemPos >> 4 != 11 )
      {
        if ( itemPos >> 4 != 12 )
          return 0;
        if ( *((_DWORD *)this->m_lppItems + 10) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            247,
            byte_4E59B0);
          return 0;
        }
        return 1;
      }
      if ( !*((_DWORD *)this->m_lppItems + 9) )
        return 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CEquipmentsContainer::CheckEquipPos",
        aDWorkRylSource_97,
        239,
        byte_4E5958);
      return 0;
    case 0xCu:
      if ( itemPos >> 4 == 11 )
      {
        v20 = *((_DWORD *)this->m_lppItems + 9);
        if ( v20 && *(_BYTE *)(*(_DWORD *)(v20 + 4) + 4) != 45 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            271,
            byte_4E5898);
          return 0;
        }
      }
      else
      {
        if ( itemPos >> 4 != 12 )
          return 0;
        v19 = *((_DWORD *)this->m_lppItems + 10);
        if ( v19 && *(_BYTE *)(*(_DWORD *)(v19 + 4) + 4) != 45 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            285,
            byte_4E58F8);
          return 0;
        }
      }
      return 1;
    case 0xDu:
      if ( itemPos >> 4 == 11 )
      {
        v22 = *((_DWORD *)this->m_lppItems + 9);
        if ( v22 && *(_BYTE *)(*(_DWORD *)(v22 + 4) + 4) != 46 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            310,
            byte_4E57C8);
          return 0;
        }
      }
      else
      {
        if ( itemPos >> 4 != 12 )
          return 0;
        v21 = *((_DWORD *)this->m_lppItems + 10);
        if ( v21 && *(_BYTE *)(*(_DWORD *)(v21 + 4) + 4) != 46 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            324,
            byte_4E5830);
          return 0;
        }
      }
      return 1;
    case 0xFu:
      switch ( itemPos >> 4 )
      {
        case 9:
          v17 = *((_DWORD *)this->m_lppItems + 11);
          if ( !v17 )
            return 0;
          if ( *(_BYTE *)(*(_DWORD *)(v17 + 4) + 4) == 15 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            198,
            byte_4E5A08);
          break;
        case 0xA:
          v18 = *((_DWORD *)this->m_lppItems + 12);
          if ( !v18 )
            return 0;
          if ( *(_BYTE *)(*(_DWORD *)(v18 + 4) + 4) == 15 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            209,
            byte_4E5A08);
          break;
        case 0xB:
          v13 = *((_DWORD *)this->m_lppItems + 9);
          if ( !v13 )
            return 1;
          v14 = *(_BYTE *)(*(_DWORD *)(v13 + 4) + 4);
          if ( v14 != 6 && v14 != 7 && v14 != 8 && v14 != 45 && v14 != 46 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            171,
            byte_4E5A08);
          return 0;
        case 0xC:
          v15 = *((_DWORD *)this->m_lppItems + 10);
          if ( !v15 )
            return 1;
          v16 = *(_BYTE *)(*(_DWORD *)(v15 + 4) + 4);
          if ( v16 != 6 && v16 != 7 && v16 != 8 && v16 != 45 && v16 != 46 )
            return 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::CheckEquipPos",
            aDWorkRylSource_97,
            186,
            byte_4E5A08);
          return 0;
        default:
          return 1;
      }
      return 0;
    case 0x10u:
      if ( itemPos >> 4 == 9 )
      {
        v23 = 11;
      }
      else
      {
        if ( itemPos >> 4 != 10 )
          return 0;
        v23 = 12;
      }
      v24 = v23;
      v25 = this->m_lppItems[v23];
      if ( !v25 )
        return 1;
      m_cItemType = v25->m_ItemInfo->m_DetailData.m_cItemType;
      if ( m_cItemType < 9u || m_cItemType > 0xEu )
        return 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CEquipmentsContainer::CheckEquipPos",
        aDWorkRylSource_97,
        373,
        aD_3,
        v24);
      return 0;
    case 0x11u:
      v3 = (itemPos & 0xFFF0) == 208;
      goto LABEL_3;
    case 0x12u:
      if ( itemPos >> 4 == 7 )
        return 1;
      v3 = itemPos >> 4 == 8;
      goto LABEL_3;
    case 0x13u:
      v3 = (itemPos & 0xFFF0) == 96;
      goto LABEL_3;
    case 0x21u:
    case 0x22u:
    case 0x23u:
    case 0x24u:
    case 0x25u:
    case 0x26u:
      v3 = (itemPos & 0xFFF0) == 176;
      goto LABEL_3;
    case 0x27u:
      if ( (itemPos & 0xFFF0) != 0xB0 )
        return 0;
      if ( !*((_DWORD *)this->m_lppItems + 9) )
        return 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CEquipmentsContainer::CheckEquipPos",
        aDWorkRylSource_97,
        345,
        byte_4E5770);
      return 0;
    case 0x28u:
    case 0x29u:
    case 0x2Au:
    case 0x2Bu:
      if ( (itemPos & 0xFFF0) != 0x90 )
        return 0;
      v31 = *((_DWORD *)this->m_lppItems + 11);
      if ( !v31 || *(_BYTE *)(*(_DWORD *)(v31 + 4) + 4) != 39 )
        return 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CEquipmentsContainer::CheckEquipPos",
        aDWorkRylSource_97,
        439,
        (char *)&byte_4E56B0);
      return 0;
    case 0x2Cu:
      v32 = itemPos >> 4;
      return itemPos >> 4 == 6 || v32 == 7 || v32 == 8;
    case 0x2Du:
      if ( itemPos >> 4 == 9 )
      {
        v27 = 11;
      }
      else
      {
        if ( itemPos >> 4 != 10 )
          return 0;
        v27 = 12;
      }
      v28 = this->m_lppItems[v27];
      if ( !v28 )
        return 1;
      v3 = v28->m_ItemInfo->m_DetailData.m_cItemType == 12;
      goto LABEL_3;
    case 0x2Eu:
      if ( itemPos >> 4 == 9 )
      {
        v29 = 11;
      }
      else
      {
        if ( itemPos >> 4 != 10 )
          return 0;
        v29 = 12;
      }
      v30 = this->m_lppItems[v29];
      if ( v30 )
      {
        v3 = v30->m_ItemInfo->m_DetailData.m_cItemType == 13;
LABEL_3:
        if ( !v3 )
          return 0;
      }
      return 1;
    default:
      return 0;
  }
}

//----- (0046BCB0) --------------------------------------------------------
void __thiscall Item::CEquipmentsContainer::CalculateCharacterStatus(
        Item::CEquipmentsContainer *this,
        CCharacter *pCharacter)
{
  int i; // ebx
  Item::CItem *v4; // eax
  Item::CItem *v5; // esi

  for ( i = 0; i < 16; ++i )
  {
    v4 = this->m_lppItems[i];
    if ( v4 )
      v5 = (v4->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? v4 : 0;
    else
      v5 = 0;
    if ( CCharacter::CheckEquipable(pCharacter, v5) )
    {
      if ( v5->m_ItemData.m_cNumOrDurability )
      {
        pCharacter->m_CharacterStatus.m_nSTR += HIWORD(v5[2].m_dwStallPrice);
        pCharacter->m_CharacterStatus.m_nDEX += LOWORD(v5[2].m_dwPrice);
        pCharacter->m_CharacterStatus.m_nCON += HIWORD(v5[2].m_dwPrice);
        pCharacter->m_CharacterStatus.m_nINT += LOWORD(v5[3].__vftable);
        pCharacter->m_CharacterStatus.m_nWIS += HIWORD(v5[3].__vftable);
      }
    }
  }
}

//----- (0046BD30) --------------------------------------------------------
char __thiscall Item::CEquipmentsContainer::SetEquipmentsAttribute(Item::CEquipmentsContainer *this)
{
  CCharacter *m_lpOwner; // eax
  Item::CEquipment *v3; // ecx
  int v4; // edi
  unsigned __int8 m_cItemType; // cl
  int v6; // esi
  unsigned __int8 v7; // dl
  int i; // ebp
  Item::CItem *v9; // eax
  Item::CEquipment *v10; // esi
  CCharacter *v11; // edi
  const StatusInfo *StatusInfo; // eax
  StatusInfo *p_m_StatusInfo; // ecx
  CreatureStatus *p_m_CreatureStatus; // esi
  const StatusInfo *v15; // eax
  Item::EquipType::DoubleSwordType eDoubleSwordType; // [esp+Ch] [ebp-110h]
  Item::CEquipment *pLeftHand; // [esp+10h] [ebp-10Ch]
  Item::CEquipment *pRightHand; // [esp+14h] [ebp-108h]
  StatusInfo v20; // [esp+18h] [ebp-104h] BYREF
  StatusInfo result; // [esp+58h] [ebp-C4h] BYREF
  StatusInfo v22; // [esp+98h] [ebp-84h] BYREF
  StatusInfo WeaponStatusInfo; // [esp+D8h] [ebp-44h] BYREF

  m_lpOwner = this->m_lpOwner;
  v3 = (Item::CEquipment *)m_lpOwner->m_Equipments.m_lppItems[m_lpOwner->m_Equipments.m_cRightHand];
  v4 = 1;
  eDoubleSwordType = SoloSword;
  pRightHand = v3;
  pLeftHand = (Item::CEquipment *)m_lpOwner->m_Equipments.m_lppItems[m_lpOwner->m_Equipments.m_cLeftHand];
  if ( v3 && m_lpOwner->m_Equipments.m_lppItems[m_lpOwner->m_Equipments.m_cLeftHand] )
  {
    m_cItemType = v3->m_ItemInfo->m_DetailData.m_cItemType;
    v6 = 1;
    if ( m_cItemType == 6 || m_cItemType == 8 || m_cItemType == 7 )
      v6 = 2;
    v7 = pLeftHand->m_ItemInfo->m_DetailData.m_cItemType;
    if ( v7 == 6 || v7 == 8 || v7 == 7 )
      v4 = 2;
    if ( m_cItemType == 15 )
      v6 = 3;
    if ( v7 == 15 )
      v4 = 3;
    if ( v6 != 1 && v4 != 1 )
    {
      if ( v6 == v4 )
      {
        eDoubleSwordType = v6;
      }
      else
      {
        eDoubleSwordType = ErrorDoubleSword;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CEquipmentsContainer::SetEquipmentsAttribute",
          aDWorkRylSource_97,
          545,
          aCid0x08x_333,
          m_lpOwner->m_dwCID,
          v6,
          v4);
      }
    }
  }
  StatusInfo::StatusInfo(&WeaponStatusInfo);
  for ( i = 0; i < 16; ++i )
  {
    v9 = this->m_lppItems[i];
    if ( v9 )
      v10 = (v9->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? (Item::CEquipment *)v9 : 0;
    else
      v10 = 0;
    if ( CCharacter::CheckEquipable(this->m_lpOwner, v10) )
    {
      v11 = this->m_lpOwner;
      if ( i != (unsigned __int8)((v11->m_Equipments.m_cLeftHand == 9) + 9)
        && i != (unsigned __int8)((v11->m_Equipments.m_cRightHand == 11) + 11) )
      {
        if ( (v10->m_ItemInfo->m_DetailData.m_dwFlags & 0x80) == 0x80 )
        {
          StatusInfo = Item::CEquipment::GetStatusInfo(v10, &result, 2);
          p_m_StatusInfo = &v11->m_CreatureStatus.m_StatusInfo;
        }
        else if ( pRightHand == v10 || pLeftHand == v10 )
        {
          StatusInfo = Item::CEquipment::GetStatusInfo(v10, &v20, 3);
          p_m_StatusInfo = &WeaponStatusInfo;
        }
        else
        {
          StatusInfo = Item::CEquipment::GetStatusInfo(v10, &v22, 3);
          p_m_StatusInfo = &v11->m_CreatureStatus.m_StatusInfo;
        }
        StatusInfo::operator+=(p_m_StatusInfo, StatusInfo);
      }
    }
  }
  if ( eDoubleSwordType )
  {
    p_m_CreatureStatus = &this->m_lpOwner->m_CreatureStatus;
    v15 = StatusInfo::CalculateDoubleSword(&WeaponStatusInfo, &v20, eDoubleSwordType);
    StatusInfo::operator+=(&p_m_CreatureStatus->m_StatusInfo, v15);
  }
  return 1;
}

//----- (0046BF50) --------------------------------------------------------
Item::CEquipmentsContainer *__thiscall Item::CEquipmentsContainer::`scalar deleting destructor'(
        Item::CEquipmentsContainer *this,
        char a2)
{
  Item::CEquipmentsContainer::~CEquipmentsContainer(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0046BF70) --------------------------------------------------------
char __thiscall Item::CEquipmentsContainer::SetItem(
        Item::CEquipmentsContainer *this,
        Item::ItemPos itemPos,
        Item::CItem *lpItem)
{
  if ( (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 )
  {
    if ( Item::CEquipmentsContainer::CheckEquipPos(this, *(_WORD *)&itemPos, lpItem) )
    {
      if ( CCharacter::CheckEquipable(this->m_lpOwner, lpItem) )
      {
        if ( Item::CListContainer::SetItem(this, *(_WORD *)&itemPos, lpItem) )
        {
          CCharacter::CalculateStatusData(this->m_lpOwner, 0);
          return 1;
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CEquipmentsContainer::SetItem",
            aDWorkRylSource_97,
            55,
            (char *)&byte_4E5A9C);
          return 0;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CEquipmentsContainer::SetItem",
          aDWorkRylSource_97,
          49,
          aCid0x08x_195,
          this->m_lpOwner->m_dwCID,
          lpItem->m_ItemData.m_usProtoTypeID);
        return 0;
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CEquipmentsContainer::SetItem",
        aDWorkRylSource_97,
        42,
        aCid0x08x_318,
        this->m_lpOwner->m_dwCID,
        *(_WORD *)&lpItem->m_ItemData.m_ItemPos & 0xF,
        *(_WORD *)&lpItem->m_ItemData.m_ItemPos >> 4,
        lpItem->m_ItemData.m_usProtoTypeID);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CEquipmentsContainer::SetItem",
      aDWorkRylSource_97,
      35,
      (char *)&byte_4E5BA0,
      lpItem->m_ItemData.m_usProtoTypeID);
    return 0;
  }
}

//----- (0046C0A0) --------------------------------------------------------
void __thiscall Item::CExchangeContainer::CExchangeContainer(Item::CExchangeContainer *this)
{
  Item::CArrayContainer::CArrayContainer(this);
  this->m_lpOwner = 0;
  this->m_lpExchangeCharacter = 0;
  this->m_dwGold = 0;
  this->m_bLock = 0;
  this->m_bAccept = 0;
  this->__vftable = (Item::CExchangeContainer_vtbl *)&Item::CExchangeContainer::`vftable';
}
// 4E5BF8: using guessed type void *Item::CExchangeContainer::`vftable';

//----- (0046C0D0) --------------------------------------------------------
void __thiscall Item::CExchangeContainer::~CExchangeContainer(Item::CExchangeContainer *this)
{
  this->__vftable = (Item::CExchangeContainer_vtbl *)&Item::CExchangeContainer::`vftable';
  Item::CArrayContainer::~CArrayContainer((Item::CListContainer *)this);
}
// 4E5BF8: using guessed type void *Item::CExchangeContainer::`vftable';

//----- (0046C0E0) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::CheckLock(Item::CExchangeContainer *this)
{
  CCharacter *m_lpOwner; // ecx
  CCharacter *m_lpExchangeCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  CSendStream *v6; // edi

  if ( this->m_bLock )
    return 0;
  m_lpOwner = this->m_lpOwner;
  if ( m_lpOwner )
  {
    m_lpExchangeCharacter = this->m_lpExchangeCharacter;
    if ( m_lpExchangeCharacter )
    {
      if ( m_lpExchangeCharacter->m_Exchange.m_bLock )
      {
        m_lpGameClientDispatch = (CSendStream *)m_lpOwner->m_lpGameClientDispatch;
        v6 = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
        m_lpExchangeCharacter->m_Exchange.m_bLock = 0;
        if ( m_lpGameClientDispatch )
          GameClientSendPacket::SendCharExchangeCmd(
            m_lpGameClientDispatch + 8,
            this->m_lpExchangeCharacter->m_dwCID,
            this->m_dwCID,
            8u,
            0);
        if ( v6 )
          GameClientSendPacket::SendCharExchangeCmd(v6 + 8, this->m_lpExchangeCharacter->m_dwCID, this->m_dwCID, 8u, 0);
      }
    }
  }
  return 1;
}

//----- (0046C160) --------------------------------------------------------
void __thiscall Item::CExchangeContainer::ExchangeCancel(Item::CExchangeContainer *this)
{
  CCharacter *m_lpExchangeCharacter; // ebx
  CGameClientDispatch *m_lpGameClientDispatch; // ebp
  CSendStream *v4; // eax
  unsigned int m_dwCID; // edi
  CGameClientDispatch *lpExchangerDispatch; // [esp+8h] [ebp-4h]

  m_lpExchangeCharacter = this->m_lpExchangeCharacter;
  if ( m_lpExchangeCharacter )
  {
    m_lpGameClientDispatch = this->m_lpOwner->m_lpGameClientDispatch;
    v4 = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
    m_dwCID = m_lpExchangeCharacter->m_dwCID;
    lpExchangerDispatch = (CGameClientDispatch *)v4;
    if ( this->m_bAccept )
    {
      this->m_bAccept = 0;
      if ( m_lpGameClientDispatch )
      {
        GameClientSendPacket::SendCharExchangeCmd(&m_lpGameClientDispatch->m_SendStream, this->m_dwCID, m_dwCID, 4u, 0);
        v4 = (CSendStream *)lpExchangerDispatch;
      }
      if ( v4 )
      {
        GameClientSendPacket::SendCharExchangeCmd(v4 + 8, this->m_dwCID, m_dwCID, 4u, 0);
        v4 = (CSendStream *)lpExchangerDispatch;
      }
    }
    if ( m_lpExchangeCharacter->m_Exchange.m_bAccept )
    {
      m_lpExchangeCharacter->m_Exchange.m_bAccept = 0;
      if ( m_lpGameClientDispatch )
      {
        GameClientSendPacket::SendCharExchangeCmd(&m_lpGameClientDispatch->m_SendStream, m_dwCID, this->m_dwCID, 4u, 0);
        v4 = (CSendStream *)lpExchangerDispatch;
      }
      if ( v4 )
        GameClientSendPacket::SendCharExchangeCmd(v4 + 8, m_dwCID, this->m_dwCID, 4u, 0);
    }
  }
}

//----- (0046C220) --------------------------------------------------------
Item::CExchangeContainer *__thiscall Item::CExchangeContainer::`vector deleting destructor'(
        Item::CExchangeContainer *this,
        char a2)
{
  Item::CExchangeContainer::~CExchangeContainer(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0046C240) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::SetItem(
        Item::CExchangeContainer *this,
        unsigned int itemPos,
        Item::CItem *lpItem)
{
  CCharacter *m_lpExchangeCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // edi
  unsigned int m_dwCID; // ebx
  Item::CItem *v8; // eax

  if ( !Item::CExchangeContainer::CheckLock(this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::SetItem",
      aDWorkRylSource_26,
      37,
      aCid0x08x_280,
      this->m_dwCID);
    return 0;
  }
  if ( !Item::CArrayContainer::SetItem(this, itemPos, lpItem) )
    return 0;
  m_lpExchangeCharacter = this->m_lpExchangeCharacter;
  if ( m_lpExchangeCharacter )
  {
    m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      m_dwCID = this->m_lpOwner->m_dwCID;
      v8 = (Item::CItem *)((int (__thiscall *)(Item::CExchangeContainer *, unsigned int))this->GetItem)(this, itemPos);
      GameClientSendPacket::SendCharExchangeItem(
        m_lpGameClientDispatch + 8,
        m_dwCID,
        0,
        v8,
        (Item::ItemPos)itemPos,
        0,
        0);
    }
  }
  return 1;
}

//----- (0046C2D0) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::RemoveItem(Item::CExchangeContainer *this, unsigned int itemPos)
{
  CCharacter *m_lpExchangeCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // edi
  Item::CItem *v6; // eax

  if ( Item::CExchangeContainer::CheckLock(this) )
  {
    m_lpExchangeCharacter = this->m_lpExchangeCharacter;
    if ( m_lpExchangeCharacter )
    {
      m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        v6 = (Item::CItem *)((int (__thiscall *)(Item::CExchangeContainer *, unsigned int))this->GetItem)(this, itemPos);
        GameClientSendPacket::SendCharExchangeItem(
          m_lpGameClientDispatch + 8,
          this->m_dwCID,
          0,
          v6,
          (Item::ItemPos)itemPos,
          0,
          1);
      }
    }
    return Item::CArrayContainer::RemoveItem(this, itemPos);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::RemoveItem",
      aDWorkRylSource_26,
      64,
      aCid0x08x_13,
      this->m_dwCID);
    return 0;
  }
}

//----- (0046C350) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::AddGold(Item::CExchangeContainer *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax
  unsigned int v5; // ecx
  CCharacter *m_lpExchangeCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // eax

  if ( Item::CExchangeContainer::CheckLock(this) )
  {
    m_dwGold = this->m_dwGold;
    if ( m_dwGold >= -1 - dwGold )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CExchangeContainer::AddGold",
        aDWorkRylSource_26,
        106,
        aCid0x08x_80,
        this->m_dwCID,
        dwGold);
      return 0;
    }
    else
    {
      v5 = m_dwGold + dwGold;
      m_lpExchangeCharacter = this->m_lpExchangeCharacter;
      this->m_dwGold = v5;
      if ( m_lpExchangeCharacter )
      {
        m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          GameClientSendPacket::SendCharExchangeItem(m_lpGameClientDispatch + 8, this->m_dwCID, v5, 0, 0, 0, 0);
      }
      return 1;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::AddGold",
      aDWorkRylSource_26,
      86,
      aCid0x08x_291,
      this->m_dwCID);
    return 0;
  }
}

//----- (0046C400) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::DeductGold(Item::CExchangeContainer *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax
  unsigned int v5; // eax
  CCharacter *m_lpExchangeCharacter; // ecx
  CSendStream *m_lpGameClientDispatch; // ecx

  if ( !Item::CExchangeContainer::CheckLock(this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::DeductGold",
      aDWorkRylSource_26,
      117,
      aCid0x08x_2,
      this->m_dwCID);
    return 0;
  }
  m_dwGold = this->m_dwGold;
  if ( m_dwGold < dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::DeductGold",
      aDWorkRylSource_26,
      121,
      aCid0x08x_7,
      this->m_dwCID);
    return 0;
  }
  v5 = m_dwGold - dwGold;
  m_lpExchangeCharacter = this->m_lpExchangeCharacter;
  this->m_dwGold = v5;
  if ( m_lpExchangeCharacter )
  {
    m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharExchangeItem(m_lpGameClientDispatch + 8, this->m_dwCID, v5, 0, 0, 0, 0);
  }
  return 1;
}

//----- (0046C490) --------------------------------------------------------
bool __thiscall Item::CExchangeContainer::ExchangeOK(Item::CExchangeContainer *this, bool bOK)
{
  CCharacter *m_lpExchangeCharacter; // eax
  Item::CExchangeContainer *p_m_Exchange; // edi
  unsigned int m_dwCID; // ebp
  Item::CItem **m_lppItems; // eax
  unsigned int m_dwGold; // eax
  CCharacter *m_lpOwner; // esi
  bool bResult; // [esp+Bh] [ebp-1h]

  m_lpExchangeCharacter = this->m_lpExchangeCharacter;
  bResult = 1;
  if ( m_lpExchangeCharacter )
  {
    p_m_Exchange = &m_lpExchangeCharacter->m_Exchange;
    if ( bOK )
    {
      m_dwCID = m_lpExchangeCharacter->m_dwCID;
      GAMELOG::LogExchangeItem(this->m_lpOwner, m_dwCID, this, 0x18u);
      GAMELOG::LogExchangeItem(this->m_lpExchangeCharacter, this->m_dwCID, p_m_Exchange, 0x18u);
      m_lppItems = this->m_lppItems;
      this->m_lppItems = p_m_Exchange->m_lppItems;
      p_m_Exchange->m_lppItems = m_lppItems;
      m_dwGold = this->m_dwGold;
      this->m_dwGold = p_m_Exchange->m_dwGold;
      p_m_Exchange->m_dwGold = m_dwGold;
      GAMELOG::LogExchangeItem(this->m_lpOwner, m_dwCID, this, 0x19u);
      GAMELOG::LogExchangeItem(this->m_lpExchangeCharacter, this->m_dwCID, p_m_Exchange, 0x19u);
    }
    CCharacter::AddGold(this->m_lpExchangeCharacter, p_m_Exchange->m_dwGold, 0);
    p_m_Exchange->m_dwGold = 0;
    p_m_Exchange->m_lpExchangeCharacter = 0;
    p_m_Exchange->m_bLock = 0;
    p_m_Exchange->m_bAccept = 0;
    CCharacter::DBUpdate(this->m_lpExchangeCharacter, UPDATE);
  }
  else
  {
    bResult = 0;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CExchangeContainer::ExchangeOK",
      aDWorkRylSource_26,
      188,
      aCid0x08x_293,
      this->m_dwCID);
  }
  CCharacter::AddGold(this->m_lpOwner, this->m_dwGold, 0);
  this->m_dwGold = 0;
  this->m_lpExchangeCharacter = 0;
  this->m_bLock = 0;
  this->m_bAccept = 0;
  m_lpOwner = this->m_lpOwner;
  m_lpOwner->m_nDBUpdateCount = -1;
  CCharacter::DBUpdate(m_lpOwner, UPDATE);
  return bResult;
}

//----- (0046C5B0) --------------------------------------------------------
CFriendList::Rebind *__thiscall std::vector<CFriendList::Rebind>::size(std::vector<CFriendList::Rebind> *this)
{
  CFriendList::Rebind *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (CFriendList::Rebind *)(this->_Mylast - result);
  return result;
}

//----- (0046C5E0) --------------------------------------------------------
void __thiscall CSingleton<CXRefFriends>::~CSingleton<CXRefFriends>(CSingleton<CXRefFriends> *this)
{
  CSingleton<CXRefFriends>::ms_pSingleton = 0;
}

//----- (0046C5F0) --------------------------------------------------------
void __cdecl std::fill<CFriendList::Rebind *,CFriendList::Rebind>(
        CFriendList::Rebind *_First,
        CFriendList::Rebind *_Last,
        const CFriendList::Rebind *_Val)
{
  CFriendList::Rebind *i; // eax
  CFriendList::Rebind *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy(v4, _Val, sizeof(CFriendList::Rebind));
  }
}

//----- (0046C620) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0046C660) --------------------------------------------------------
CFriendList::Rebind *__cdecl std::copy_backward<CFriendList::Rebind *,CFriendList::Rebind *>(
        CFriendList::Rebind *_First,
        CFriendList::Rebind *_Last,
        CFriendList::Rebind *_Dest)
{
  CFriendList::Rebind *v3; // edx
  CFriendList::Rebind *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy(--result, --v3, sizeof(CFriendList::Rebind));
  return result;
}

//----- (0046C690) --------------------------------------------------------
CFriendList::Rebind *__cdecl std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(
        CFriendList::Rebind *_First,
        CFriendList::Rebind *_Last,
        CFriendList::Rebind *_Dest)
{
  CFriendList::Rebind *v3; // edx
  CFriendList::Rebind *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
      qmemcpy(result, v3, sizeof(CFriendList::Rebind));
    ++v3;
  }
  return result;
}


//----- (0046C6C0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)operator new((tagHeader *)0x18);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (0046C700) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CFriendList::Rebind *,unsigned int,CFriendList::Rebind,std::allocator<CFriendList::Rebind>>(
        CFriendList::Rebind *_First,
        unsigned int _Count,
        const CFriendList::Rebind *_Val)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        qmemcpy(_First, _Val, sizeof(CFriendList::Rebind));
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (0046C730) --------------------------------------------------------
std::vector<CFriendList::Rebind>::iterator *__cdecl std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(
        std::vector<CFriendList::Rebind>::iterator *result,
        std::vector<CFriendList::Rebind>::iterator _First,
        std::vector<CFriendList::Rebind>::iterator _Last,
        unsigned int *_Val)
{
  CFriendList::Rebind *Myptr; // edi
  int v5; // esi
  int v6; // ecx
  unsigned int m_dwCID; // edx
  CFriendList::Rebind *v8; // ecx
  std::vector<CFriendList::Rebind>::iterator *v9; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    v6 = v5 / 2;
    m_dwCID = Myptr[v6].m_friendInfo.m_dwCID;
    v8 = &Myptr[v6];
    if ( m_dwCID >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr = v8 + 1;
      v5 += -1 - v5 / 2;
    }
  }
  v9 = result;
  result->_Myptr = Myptr;
  return v9;
}

//----- (0046C7A0) --------------------------------------------------------
CFriendList::Rebind *__thiscall std::vector<CFriendList::Rebind>::_Ufill(
        std::vector<CFriendList::Rebind> *this,
        CFriendList::Rebind *_Ptr,
        unsigned int _Count,
        const CFriendList::Rebind *_Val)
{
  std::_Uninit_fill_n<CFriendList::Rebind *,unsigned int,CFriendList::Rebind,std::allocator<CFriendList::Rebind>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (0046C7D0) --------------------------------------------------------
CFriendList::Rebind *__thiscall CFriendList::GetFriend(CFriendList *this, unsigned int dwFriendCID)
{
  CFriendList::Rebind *Mylast; // esi
  CFriendList::Rebind *result; // eax
  std::vector<CFriendList::Rebind>::iterator finditr; // [esp+4h] [ebp-4h] BYREF

  Mylast = this->m_friendList._Mylast;
  std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(
    &finditr,
    (std::vector<CFriendList::Rebind>::iterator)this->m_friendList._Myfirst,
    (std::vector<CFriendList::Rebind>::iterator)Mylast,
    &dwFriendCID);
  result = finditr._Myptr;
  if ( finditr._Myptr == Mylast || dwFriendCID != finditr._Myptr->m_friendInfo.m_dwCID )
    return 0;
  return result;
}

//----- (0046C810) --------------------------------------------------------
std::vector<CFriendList::Rebind>::iterator *__thiscall std::vector<CFriendList::Rebind>::erase(
        std::vector<CFriendList::Rebind> *this,
        std::vector<CFriendList::Rebind>::iterator *result,
        std::vector<CFriendList::Rebind>::iterator _Where)
{
  CFriendList::Rebind *Mylast; // ebx
  CFriendList::Rebind *v4; // eax
  CFriendList::Rebind *Myptr; // edx
  CFriendList::Rebind *v6; // esi
  CFriendList::Rebind *v7; // edi
  std::vector<CFriendList::Rebind>::iterator *v8; // eax

  Mylast = this->_Mylast;
  v4 = _Where._Myptr + 1;
  if ( _Where._Myptr + 1 != Mylast )
  {
    Myptr = _Where._Myptr;
    do
    {
      v6 = v4;
      v7 = Myptr;
      ++v4;
      ++Myptr;
      qmemcpy(v7, v6, sizeof(CFriendList::Rebind));
    }
    while ( v4 != Mylast );
  }
  --this->_Mylast;
  v8 = result;
  result->_Myptr = _Where._Myptr;
  return v8;
}

//----- (0046C860) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,unsigned long> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,unsigned long> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0046CA10) --------------------------------------------------------
void __thiscall __noreturn std::vector<CFriendList::Rebind>::_Xlen(std::vector<CFriendList::Rebind> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0046CA80) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0046CD40) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> *result,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // esi
  unsigned int first; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> *v7; // eax
  bool _Addleft; // [esp+8h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = Parent;
      _Addleft = first < Parent->_Myval.first;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  Ptr = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Insert(
          this,
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *)&_Val,
          _Addleft,
          Myhead,
          _Val)->_Ptr;
  v7 = result;
  result->first._Ptr = Ptr;
  result->second = 1;
  return v7;
}

//----- (0046CDB0) --------------------------------------------------------
void __thiscall std::vector<CFriendList::Rebind>::_Insert_n(
        std::vector<CFriendList::Rebind> *this,
        std::vector<CFriendList::Rebind>::iterator _Where,
        unsigned int _Count,
        const CFriendList::Rebind *_Val)
{
  CFriendList::Rebind *Myfirst; // edi
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // edi
  CFriendList::Rebind *v12; // eax
  CFriendList::Rebind *v13; // ecx
  CFriendList::Rebind *v14; // eax
  char *v15; // esi
  CFriendList::Rebind *v16; // eax
  CFriendList::Rebind *v17; // esi
  CFriendList::Rebind *Mylast; // ecx
  CFriendList::Rebind *v20; // edx
  CFriendList::Rebind *v21; // [esp-Ch] [ebp-50h]
  unsigned int v22; // [esp-8h] [ebp-4Ch]
  int v23; // [esp+0h] [ebp-44h] BYREF
  CFriendList::Rebind *_Ptr; // [esp+Ch] [ebp-38h]
  CFriendList::Rebind *_Newvec; // [esp+10h] [ebp-34h]
  CFriendList::Rebind _Tmp; // [esp+14h] [ebp-30h] BYREF
  int *v27; // [esp+34h] [ebp-10h]
  int v28; // [esp+40h] [ebp-4h]
  CFriendList::Rebind *_Wherea; // [esp+4Ch] [ebp+8h]
  CFriendList::Rebind *_Vala; // [esp+54h] [ebp+10h]

  qmemcpy(&_Tmp, _Val, sizeof(_Tmp));
  Myfirst = this->_Myfirst;
  v27 = &v23;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - this->_Myfirst;
    else
      v7 = 0;
    if ( 153391689 - v7 < _Count )
      std::vector<CFriendList::Rebind>::_Xlen(this);
    if ( this->_Myfirst )
      v8 = this->_Mylast - this->_Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<CFriendList::Rebind *,CFriendList::Rebind *>(_Where._Myptr, _Wherea, _Vala);
        std::fill<CFriendList::Rebind *,CFriendList::Rebind>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<CFriendList::Rebind>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<CFriendList::Rebind *,CFriendList::Rebind>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 153391689 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( this->_Myfirst )
        v10 = this->_Mylast - this->_Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<CFriendList::Rebind>::size(this) + _Count;
      v11 = v9;
      v12 = (CFriendList::Rebind *)operator new((tagHeader *)(28 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<CFriendList::Rebind *,unsigned int,CFriendList::Rebind,std::allocator<CFriendList::Rebind>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (CFriendList::Rebind *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (0046D070) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0046D130) --------------------------------------------------------
void __thiscall CFriendList::Clear(CFriendList *this)
{
  CFriendList::Rebind *i; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  if ( this->m_lpXRefTable )
  {
    for ( i = this->m_friendList._Myfirst; i != this->m_friendList._Mylast; ++i )
      std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
        &this->m_lpXRefTable->m_XRefTable,
        &result,
        i->m_XRefItr);
  }
  if ( this->m_friendList._Myfirst )
    operator delete(this->m_friendList._Myfirst);
  this->m_friendList._Myfirst = 0;
  this->m_friendList._Mylast = 0;
  this->m_friendList._Myend = 0;
}

//----- (0046D190) --------------------------------------------------------
char __thiscall CFriendList::Remove(CFriendList *this, unsigned int dwFriendCID)
{
  CFriendList::Rebind *Mylast; // edi
  CFriendList::Rebind *Myptr; // ebx
  CXRefFriends *m_lpXRefTable; // ecx
  std::vector<CFriendList::Rebind>::iterator finditr; // [esp+Ch] [ebp-4h] BYREF

  Mylast = this->m_friendList._Mylast;
  std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(
    &finditr,
    (std::vector<CFriendList::Rebind>::iterator)this->m_friendList._Myfirst,
    (std::vector<CFriendList::Rebind>::iterator)Mylast,
    &dwFriendCID);
  Myptr = finditr._Myptr;
  if ( finditr._Myptr == Mylast || dwFriendCID != finditr._Myptr->m_friendInfo.m_dwCID )
    return 0;
  m_lpXRefTable = this->m_lpXRefTable;
  if ( m_lpXRefTable )
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
      &m_lpXRefTable->m_XRefTable,
      (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *)&dwFriendCID,
      finditr._Myptr->m_XRefItr);
  std::vector<CFriendList::Rebind>::erase(
    &this->m_friendList,
    (std::vector<CFriendList::Rebind>::iterator *)&dwFriendCID,
    (std::vector<CFriendList::Rebind>::iterator)Myptr);
  return 1;
}

//----- (0046D200) --------------------------------------------------------
std::vector<CFriendList::Rebind>::iterator *__thiscall std::vector<CFriendList::Rebind>::insert(
        std::vector<CFriendList::Rebind> *this,
        std::vector<CFriendList::Rebind>::iterator *result,
        std::vector<CFriendList::Rebind>::iterator _Where,
        const CFriendList::Rebind *_Val)
{
  CFriendList::Rebind *Myfirst; // esi
  int v6; // esi
  std::vector<CFriendList::Rebind>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CFriendList::Rebind>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (0046D270) --------------------------------------------------------
void __thiscall CFriendList::CFriendList(CFriendList *this, unsigned int dwOwnerCID, CXRefFriends *lpXRefTable)
{
  this->m_dwOwnerCID = dwOwnerCID;
  this->m_lpXRefTable = lpXRefTable;
  this->m_friendList._Myfirst = 0;
  this->m_friendList._Mylast = 0;
  this->m_friendList._Myend = 0;
}

//----- (0046D290) --------------------------------------------------------
void __thiscall CFriendList::~CFriendList(CFriendList *this)
{
  CFriendList::Clear(this);
  if ( this->m_friendList._Myfirst )
    operator delete(this->m_friendList._Myfirst);
  this->m_friendList._Myfirst = 0;
  this->m_friendList._Mylast = 0;
  this->m_friendList._Myend = 0;
}

//----- (0046D2F0) --------------------------------------------------------
char __thiscall CFriendList::Add(CFriendList *this, unsigned int dwFriendCID, const char *szCharacterName)
{
  std::vector<CFriendList::Rebind> *p_m_friendList; // ebp
  CFriendList::Rebind *Myfirst; // eax
  CXRefFriends *m_lpXRefTable; // ecx
  unsigned int v7; // ebx
  CFriendList::Rebind *Mylast; // eax
  std::vector<CFriendList::Rebind>::iterator lbound; // [esp+Ch] [ebp-34h] BYREF
  std::pair<unsigned long const ,unsigned long> _Val; // [esp+10h] [ebp-30h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> result; // [esp+18h] [ebp-28h] BYREF
  CFriendList::Rebind rebind; // [esp+20h] [ebp-20h] BYREF

  p_m_friendList = &this->m_friendList;
  Myfirst = this->m_friendList._Myfirst;
  if ( Myfirst && (unsigned int)(this->m_friendList._Mylast - Myfirst) >= 0x64 )
    return 0;
  std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(
    &lbound,
    (std::vector<CFriendList::Rebind>::iterator)this->m_friendList._Myfirst,
    (std::vector<CFriendList::Rebind>::iterator)this->m_friendList._Mylast,
    &dwFriendCID);
  m_lpXRefTable = this->m_lpXRefTable;
  v7 = dwFriendCID;
  memset(&rebind, 0, 12);
  if ( m_lpXRefTable )
  {
    _Val.second = this->m_dwOwnerCID;
    _Val.first = dwFriendCID;
    rebind.m_XRefItr._Ptr = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::insert(
                              &m_lpXRefTable->m_XRefTable,
                              &result,
                              &_Val)->first._Ptr;
  }
  rebind.m_friendInfo.m_dwCID = v7;
  strncpy(rebind.m_friendInfo.m_szName, szCharacterName, 0x10u);
  Mylast = this->m_friendList._Mylast;
  rebind.m_friendInfo.m_dwStatusFlag |= 1u;
  if ( lbound._Myptr == Mylast || v7 != lbound._Myptr->m_friendInfo.m_dwCID )
  {
    std::vector<CFriendList::Rebind>::insert(
      p_m_friendList,
      (std::vector<CFriendList::Rebind>::iterator *)&dwFriendCID,
      lbound,
      &rebind);
    return 1;
  }
  else
  {
    qmemcpy(lbound._Myptr, &rebind, sizeof(CFriendList::Rebind));
    return 1;
  }
}

//----- (0046D410) --------------------------------------------------------
char __thiscall CFriendList::SerializeIn(CFriendList *this, char *szBuffer_In, unsigned int dwBufferSize_In)
{
  unsigned int *v3; // esi
  CFriendList::Rebind *Mylast; // ebx
  CFriendList::Rebind *Myptr; // eax
  unsigned int v7; // edx
  unsigned int v8; // ecx
  unsigned int v9; // edx
  CXRefFriends *m_lpXRefTable; // ecx
  CFriendList::Rebind *Myfirst; // [esp-10h] [ebp-60h]
  unsigned int dwFriendCID; // [esp+10h] [ebp-40h] BYREF
  std::vector<CFriendList::Rebind>::iterator lbound; // [esp+14h] [ebp-3Ch] BYREF
  const char *i; // [esp+18h] [ebp-38h]
  const char *szBufferPastEnd; // [esp+1Ch] [ebp-34h]
  std::pair<unsigned long const ,unsigned long> _Val; // [esp+20h] [ebp-30h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> result; // [esp+28h] [ebp-28h] BYREF
  CFriendList::Rebind rebind; // [esp+30h] [ebp-20h] BYREF

  v3 = (unsigned int *)szBuffer_In;
  memset(&rebind, 0, 12);
  szBufferPastEnd = &szBuffer_In[dwBufferSize_In];
  for ( i = szBuffer_In + 24; i <= szBufferPastEnd; i += 24 )
  {
    Mylast = this->m_friendList._Mylast;
    Myfirst = this->m_friendList._Myfirst;
    dwFriendCID = *v3;
    std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(
      &lbound,
      (std::vector<CFriendList::Rebind>::iterator)Myfirst,
      (std::vector<CFriendList::Rebind>::iterator)Mylast,
      &dwFriendCID);
    Myptr = lbound._Myptr;
    if ( lbound._Myptr == Mylast || dwFriendCID != lbound._Myptr->m_friendInfo.m_dwCID )
    {
      v7 = *v3;
      rebind.m_friendInfo.m_dwStatusFlag = v3[1];
      *(_DWORD *)&rebind.m_friendInfo.m_szName[4] = v3[3];
      v8 = v3[5];
      rebind.m_friendInfo.m_dwCID = v7;
      v9 = v3[2];
      *(_DWORD *)&rebind.m_friendInfo.m_szName[12] = v8;
      m_lpXRefTable = this->m_lpXRefTable;
      *(_DWORD *)rebind.m_friendInfo.m_szName = v9;
      *(_DWORD *)&rebind.m_friendInfo.m_szName[8] = v3[4];
      if ( m_lpXRefTable )
      {
        _Val.second = this->m_dwOwnerCID;
        _Val.first = dwFriendCID;
        rebind.m_XRefItr._Ptr = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::insert(
                                  &m_lpXRefTable->m_XRefTable,
                                  &result,
                                  &_Val)->first._Ptr;
        Myptr = lbound._Myptr;
      }
      std::vector<CFriendList::Rebind>::_Insert_n(
        &this->m_friendList,
        (std::vector<CFriendList::Rebind>::iterator)Myptr,
        1u,
        &rebind);
    }
    v3 = (unsigned int *)i;
  }
  return 1;
}

//----- (0046D520) --------------------------------------------------------
void __thiscall CXRefFriends::CXRefFriends(CXRefFriends *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v2; // eax

  CSingleton<CXRefFriends>::ms_pSingleton = this;
  v2 = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode(&this->m_XRefTable);
  this->m_XRefTable._Myhead = v2;
  v2->_Isnil = 1;
  this->m_XRefTable._Myhead->_Parent = this->m_XRefTable._Myhead;
  this->m_XRefTable._Myhead->_Left = this->m_XRefTable._Myhead;
  this->m_XRefTable._Myhead->_Right = this->m_XRefTable._Myhead;
  this->m_XRefTable._Mysize = 0;
}

//----- (0046D590) --------------------------------------------------------
void __thiscall CXRefFriends::~CXRefFriends(CXRefFriends *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator v3; // [esp-8h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  Myhead = this->m_XRefTable._Myhead;
  v3._Ptr = Myhead->_Left;
  v5 = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(
    &this->m_XRefTable,
    &result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator)Myhead);
  operator delete(this->m_XRefTable._Myhead);
  this->m_XRefTable._Myhead = 0;
  this->m_XRefTable._Mysize = 0;
  CSingleton<CXRefFriends>::ms_pSingleton = 0;
}

//----- (0046D600) --------------------------------------------------------
BanInfo *__cdecl std::_Copy_opt<BanInfo *,BanInfo *>(BanInfo *_First, BanInfo *_Last, BanInfo *_Dest)
{
  BanInfo *v3; // ecx
  BanInfo *result; // eax
  BanInfo *v5; // edi
  int v6; // esi

  v3 = _First;
  for ( result = _Dest; v3 != _Last; *(_DWORD *)&v5->m_szName[12] = v6 )
  {
    v5 = result;
    result->m_dwCID = v3->m_dwCID;
    *(_DWORD *)result->m_szName = *(_DWORD *)v3->m_szName;
    *(_DWORD *)&result->m_szName[4] = *(_DWORD *)&v3->m_szName[4];
    *(_DWORD *)&result->m_szName[8] = *(_DWORD *)&v3->m_szName[8];
    v6 = *(_DWORD *)&v3->m_szName[12];
    ++v3;
    ++result;
  }
  return result;
}

//----- (0046D650) --------------------------------------------------------
BanInfo *__cdecl std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(
        BanInfo *_First,
        BanInfo *_Last,
        BanInfo *_Dest)
{
  BanInfo *v3; // ecx
  BanInfo *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
    {
      result->m_dwCID = v3->m_dwCID;
      *(_DWORD *)result->m_szName = *(_DWORD *)v3->m_szName;
      *(_DWORD *)&result->m_szName[4] = *(_DWORD *)&v3->m_szName[4];
      *(_DWORD *)&result->m_szName[8] = *(_DWORD *)&v3->m_szName[8];
      *(_DWORD *)&result->m_szName[12] = *(_DWORD *)&v3->m_szName[12];
    }
    ++v3;
  }
  return result;
}

//----- (0046D6A0) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<BanInfo *,unsigned int,BanInfo,std::allocator<BanInfo>>(
        BanInfo *_First,
        unsigned int _Count,
        const BanInfo *_Val)
{
  unsigned int v3; // ecx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (0046D6F0) --------------------------------------------------------
std::vector<BanInfo>::iterator *__cdecl std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
        std::vector<BanInfo>::iterator *result,
        std::vector<BanInfo>::iterator _First,
        std::vector<BanInfo>::iterator _Last,
        unsigned int *_Val)
{
  BanInfo *Myptr; // esi
  int v5; // ecx
  std::vector<BanInfo>::iterator *v6; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    if ( Myptr[v5 / 2].m_dwCID >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v5 / 2 + 1;
      v5 += -1 - v5 / 2;
    }
  }
  v6 = result;
  result->_Myptr = Myptr;
  return v6;
}

//----- (0046D750) --------------------------------------------------------
bool __thiscall CBanList::IsBan(CBanList *this, unsigned int dwBanCID, char *szCharacterName)
{
  BanInfo *Mylast; // esi
  int v4; // eax
  bool result; // al
  std::vector<BanInfo>::iterator finditr; // [esp+4h] [ebp-4h] BYREF

  Mylast = this->m_banList._Mylast;
  std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
    &finditr,
    (std::vector<BanInfo>::iterator)this->m_banList._Myfirst,
    (std::vector<BanInfo>::iterator)Mylast,
    &dwBanCID);
  result = 0;
  if ( finditr._Myptr != Mylast && dwBanCID == finditr._Myptr->m_dwCID )
  {
    strncmp((unsigned __int8 *)szCharacterName, (unsigned __int8 *)finditr._Myptr->m_szName, 0x10u);
    if ( !v4 )
      return 1;
  }
  return result;
}
// 46D794: variable 'v4' is possibly undefined

//----- (0046D7B0) --------------------------------------------------------
char *__thiscall CBanList::GetBanName(CBanList *this, unsigned int dwCID)
{
  BanInfo *Mylast; // esi
  std::vector<BanInfo>::iterator finditr; // [esp+4h] [ebp-4h] BYREF

  Mylast = this->m_banList._Mylast;
  std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
    &finditr,
    (std::vector<BanInfo>::iterator)this->m_banList._Myfirst,
    (std::vector<BanInfo>::iterator)Mylast,
    &dwCID);
  if ( finditr._Myptr == Mylast || dwCID != finditr._Myptr->m_dwCID )
    return 0;
  else
    return finditr._Myptr->m_szName;
}

//----- (0046D7F0) --------------------------------------------------------
char __thiscall CBanList::Remove(CBanList *this, unsigned int dwBanCID)
{
  BanInfo *Mylast; // edi
  std::vector<BanInfo>::iterator finditr; // [esp+8h] [ebp-4h] BYREF

  Mylast = this->m_banList._Mylast;
  std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
    &finditr,
    (std::vector<BanInfo>::iterator)this->m_banList._Myfirst,
    (std::vector<BanInfo>::iterator)Mylast,
    &dwBanCID);
  if ( finditr._Myptr == Mylast || dwBanCID != finditr._Myptr->m_dwCID )
    return 0;
  std::_Copy_opt<BanInfo *,BanInfo *>(finditr._Myptr + 1, Mylast, finditr._Myptr);
  --this->m_banList._Mylast;
  return 1;
}
