//----- (0041B6C0) --------------------------------------------------------
char __thiscall CCharacter::RedistributionSkill(CCharacter *this)
{
  char result; // al
  char *p_cLockCount; // edi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  char v5; // bl
  char v6; // cl
  unsigned __int8 *v7; // edx
  int v8; // ebp
  unsigned __int8 v9; // al
  bool v10; // cc
  int v11; // ecx
  int v12; // edx
  __int16 v13; // ax
  int v14; // edi
  int wSlotNum; // ebx
  char *p_cSkillLevel; // ecx
  __int16 v17; // dx
  __int16 v18; // bp
  __int16 v19; // bp
  unsigned __int16 v20; // ax
  int v21; // edi
  char v22; // [esp+7h] [ebp-9h]
  int nSlotIndex; // [esp+8h] [ebp-8h]
  unsigned __int8 *m_StatusLimitType; // [esp+Ch] [ebp-4h]

  result = CCharacter::CalculateStatusData(this, 0);
  if ( result )
  {
    nSlotIndex = 0;
    if ( this->m_DBData.m_Skill.wSlotNum )
    {
      p_cLockCount = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cLockCount;
      while ( 1 )
      {
        SkillProtoType = CSkillMgr::GetSkillProtoType(
                           CSingleton<CSkillMgr>::ms_pSingleton,
                           *((_WORD *)p_cLockCount - 1));
        if ( !SkillProtoType )
          break;
        m_StatusLimitType = SkillProtoType->m_ProtoTypes[0].m_StatusLimitType;
        while ( 1 )
        {
          v22 = *p_cLockCount;
          v5 = 0;
          v6 = p_cLockCount[1] + 6 * *p_cLockCount;
          v7 = m_StatusLimitType;
          v8 = 2;
          while ( 1 )
          {
            v9 = v6 * v7[2];
            switch ( *v7 )
            {
              case 1u:
                v10 = v9 <= this->m_CharacterStatus.m_nSTR;
                goto LABEL_14;
              case 2u:
                v10 = v9 <= this->m_CharacterStatus.m_nDEX;
                goto LABEL_14;
              case 3u:
                v10 = v9 <= this->m_CharacterStatus.m_nCON;
                goto LABEL_14;
              case 4u:
                v10 = v9 <= this->m_CharacterStatus.m_nINT;
                goto LABEL_14;
              case 5u:
                v10 = v9 <= this->m_CharacterStatus.m_nWIS;
LABEL_14:
                if ( !v10 )
                  v5 = 1;
                break;
              default:
                break;
            }
            ++v7;
            if ( !--v8 )
              break;
            v6 = p_cLockCount[1] + 6 * *p_cLockCount;
          }
          if ( v5 != 1 )
            break;
          if ( !p_cLockCount[1] )
          {
            if ( !v22 )
              break;
            *p_cLockCount = v22 - 1;
            p_cLockCount[1] = 6;
          }
          --p_cLockCount[1];
          --this->m_DBData.m_Skill.wSkillNum;
        }
        p_cLockCount += 4;
        if ( ++nSlotIndex >= this->m_DBData.m_Skill.wSlotNum )
          goto LABEL_23;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::RedistributionSkill",
        aDWorkRylSource_28,
        268,
        aCid0x08x_251,
        this->m_dwCID,
        this->m_DBData.m_Skill.SSlot[nSlotIndex].SKILLINFO.wSkill);
    }
    else
    {
LABEL_23:
      v11 = this->m_CharacterStatus.m_nINT - 20;
      v12 = (int)((unsigned __int64)(1717986919LL * v11) >> 32) >> 2;
      LOWORD(v12) = this->m_DBData.m_Info.Level;
      v13 = 0;
      v14 = v12 + v11 / 10;
      if ( this->m_DBData.m_Skill.wSlotNum )
      {
        wSlotNum = this->m_DBData.m_Skill.wSlotNum;
        p_cSkillLevel = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cSkillLevel;
        do
        {
          v17 = *(p_cSkillLevel - 1);
          v18 = *p_cSkillLevel;
          p_cSkillLevel += 4;
          v13 += v18 + 6 * v17;
          --wSlotNum;
        }
        while ( wSlotNum );
      }
      v19 = v13 - v14;
      if ( (__int16)(v13 - v14) <= 0 )
        return 1;
      while ( 1 )
      {
        v20 = this->m_DBData.m_Skill.wSlotNum;
        if ( !v20 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::RedistributionSkill",
            aDWorkRylSource_28,
            325,
            aCid0x08x_126,
            this->m_dwCID);
          return 0;
        }
        v21 = v20 - 1;
        if ( !CSkillMgr::GetSkillProtoType(
                CSingleton<CSkillMgr>::ms_pSingleton,
                this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.wSkill) )
          break;
        if ( !this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cSkillLevel )
        {
          --this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cLockCount;
          this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cSkillLevel = 6;
        }
        --this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cSkillLevel;
        --this->m_DBData.m_Skill.wSkillNum;
        --v19;
        if ( !this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cSkillLevel
          && !this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.cLockCount )
        {
          this->m_DBData.m_Skill.SSlot[v21].dwSkillSlot = 0;
          --this->m_DBData.m_Skill.wSlotNum;
        }
        if ( v19 <= 0 )
          return 1;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::RedistributionSkill",
        aDWorkRylSource_28,
        335,
        aCid0x08x_251,
        this->m_dwCID,
        this->m_DBData.m_Skill.SSlot[v21].SKILLINFO.wSkill);
    }
    return 0;
  }
  return result;
}

//----- (0041B980) --------------------------------------------------------
char __thiscall CCharacter::SkillCreate(CCharacter *this, Item::CUseItem *pUseItem)
{
  const Item::ItemInfo *m_ItemInfo; // eax
  unsigned __int16 wSlotNum; // di
  unsigned __int8 v5; // bl
  unsigned __int16 Skill; // bp
  int v8; // edx
  unsigned __int16 v9; // cx
  int v10; // eax
  char *p_cSkillLevel; // eax
  __int16 v12; // dx
  __int16 v13; // bp
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  unsigned __int16 v15; // dx
  char *v16; // eax
  __int16 v17; // cx
  __int16 v18; // di
  unsigned __int8 Index; // [esp+10h] [ebp-18h]
  __int16 wMaxSkillPoint; // [esp+14h] [ebp-14h]
  int SkillID; // [esp+18h] [ebp-10h]
  int v22; // [esp+1Ch] [ebp-Ch]
  unsigned __int16 SkillLockCount; // [esp+20h] [ebp-8h]
  char pUseItema; // [esp+2Ch] [ebp+4h]
  Item::CUseItem *pUseItemb; // [esp+2Ch] [ebp+4h]

  m_ItemInfo = pUseItem->m_ItemInfo;
  wSlotNum = this->m_DBData.m_Skill.wSlotNum;
  v5 = 0;
  Skill = 0;
  LOWORD(SkillID) = m_ItemInfo->m_UseItemInfo.m_usSkill_ID;
  SkillLockCount = m_ItemInfo->m_UseItemInfo.m_usSkill_LockCount;
  Index = 0;
  pUseItema = 0;
  if ( wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillCreate",
      aDWorkRylSource_28,
      373,
      aCid0x08x_144,
      this->m_dwCID,
      wSlotNum);
    return 0;
  }
  v8 = (int)((unsigned __int64)(1717986919LL * (this->m_CharacterStatus.m_nINT - 20)) >> 32) >> 2;
  LOWORD(v8) = this->m_DBData.m_Info.Level;
  v9 = 0;
  v10 = v8 + (this->m_CharacterStatus.m_nINT - 20) / 10;
  wMaxSkillPoint = v10;
  if ( wSlotNum )
  {
    p_cSkillLevel = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cSkillLevel;
    v22 = this->m_DBData.m_Skill.wSlotNum;
    do
    {
      v12 = *(p_cSkillLevel - 1);
      v13 = *p_cSkillLevel;
      p_cSkillLevel += 4;
      v9 += v13 + 6 * v12;
      --v22;
    }
    while ( v22 );
    Skill = 0;
    LOWORD(v10) = wMaxSkillPoint;
  }
  if ( v9 >= (unsigned __int16)v10 || wSlotNum == 20 )
  {
    Skill = 4;
  }
  else
  {
    CCharacter::CalculatePassiveSkill(
      this,
      (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
      (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand],
      Detach);
    v5 = 0;
    Index = 0;
    if ( this->m_DBData.m_Skill.wSlotNum )
    {
      while ( (_WORD)SkillID != this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.wSkill )
      {
        if ( ++v5 >= (unsigned int)this->m_DBData.m_Skill.wSlotNum )
        {
          Index = v5;
          goto LABEL_17;
        }
      }
      Index = v5;
      Skill = CCharacter::ReadSkill(this, this->m_DBData.m_Skill.SSlot[v5], SkillID, SkillLockCount);
      if ( !Skill )
      {
        ++this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.cSkillLevel;
        ++this->m_DBData.m_Skill.wSkillNum;
        if ( this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.cLockCount < 3
          && this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.cSkillLevel == 6 )
        {
          pUseItema = CCharacter::SkillLock(this, v5);
        }
      }
    }
LABEL_17:
    if ( v5 == this->m_DBData.m_Skill.wSlotNum )
    {
      Skill = CCharacter::ReadSkill(this, this->m_DBData.m_Skill.SSlot[v5], SkillID, SkillLockCount);
      if ( !Skill )
      {
        this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.wSkill = SkillID;
        this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.cLockCount = 0;
        this->m_DBData.m_Skill.SSlot[v5].SKILLINFO.cSkillLevel = 1;
        ++this->m_DBData.m_Skill.wSkillNum;
        ++this->m_DBData.m_Skill.wSlotNum;
      }
    }
    CCharacter::CalculatePassiveSkill(
      this,
      (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
      (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand],
      Attach);
    CCharacter::UpdateQuickSlotSkill(this, this->m_DBData.m_Skill.SSlot[v5]);
    LOWORD(v10) = wMaxSkillPoint;
    if ( pUseItema )
      goto LABEL_23;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    GameClientSendPacket::SendCharSkillCommand(
      &m_lpGameClientDispatch->m_SendStream,
      this->m_dwCID,
      0x17u,
      Index,
      SkillID,
      Skill);
    LOWORD(v10) = wMaxSkillPoint;
  }
LABEL_23:
  if ( !Skill )
    return 1;
  v15 = 0;
  if ( this->m_DBData.m_Skill.wSlotNum )
  {
    v16 = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cSkillLevel;
    pUseItemb = (Item::CUseItem *)this->m_DBData.m_Skill.wSlotNum;
    do
    {
      v17 = *(v16 - 1);
      v18 = *v16;
      v16 += 4;
      v15 += v18 + 6 * v17;
      pUseItemb = (Item::CUseItem *)((char *)pUseItemb - 1);
    }
    while ( pUseItemb );
    LOWORD(v10) = wMaxSkillPoint;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCharacter::SkillCreate",
    aDWorkRylSource_28,
    446,
    aCid0x08x_25,
    this->m_dwCID,
    v15,
    (unsigned __int16)v10,
    v5,
    this->m_DBData.m_Info.Level,
    this->m_CharacterStatus.m_nINT,
    Skill);
  LogSkillSlot(&this->m_DBData);
  return 0;
}
// 41BAF0: variable 'SkillID' is possibly undefined

//----- (0041BCB0) --------------------------------------------------------
char __thiscall CCharacter::SkillErase(CCharacter *this, unsigned __int8 Index_In)
{
  unsigned __int16 wSlotNum; // ax
  Item::CItem **m_lppItems; // eax
  Item::CEquipment *v6; // ecx
  unsigned __int16 wSkill; // di
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CSkillMgr::ProtoTypeArray *v9; // edi
  unsigned __int16 v10; // dx
  unsigned __int8 v11; // cl
  int v12; // eax
  char *v13; // eax
  unsigned __int16 v14; // ax
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  Item::CEquipment *v16; // [esp-18h] [ebp-30h]
  char bUnlockFlag; // [esp+5h] [ebp-13h]
  unsigned __int8 cDefenserJudge; // [esp+6h] [ebp-12h] BYREF
  unsigned __int8 cOffencerJudge; // [esp+7h] [ebp-11h] BYREF
  int wError; // [esp+8h] [ebp-10h] BYREF
  SKILLSLOT SkillSlot; // [esp+Ch] [ebp-Ch]
  int SkillID; // [esp+14h] [ebp-4h]

  wSlotNum = this->m_DBData.m_Skill.wSlotNum;
  wError = 0;
  bUnlockFlag = 0;
  if ( wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillErase",
      aDWorkRylSource_28,
      463,
      aCid0x08x_144,
      this->m_dwCID,
      wSlotNum);
    return 0;
  }
  if ( wSlotNum <= Index_In )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillErase",
      aDWorkRylSource_28,
      470,
      aCid0x08x_314,
      this->m_dwCID,
      wSlotNum,
      Index_In);
    return 0;
  }
  m_lppItems = this->m_Equipments.m_lppItems;
  v6 = (Item::CEquipment *)m_lppItems[this->m_Equipments.m_cLeftHand];
  wSkill = this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.wSkill;
  v16 = (Item::CEquipment *)m_lppItems[this->m_Equipments.m_cRightHand];
  SkillID = wSkill;
  CCharacter::CalculatePassiveSkill(this, v16, v6, Detach);
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, wSkill);
  v9 = SkillProtoType;
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillErase",
      aDWorkRylSource_28,
      483,
      aCid0x08x_211,
      this->m_dwCID,
      (unsigned __int16)SkillID);
    return 0;
  }
  if ( SkillProtoType->m_ProtoTypes[0].m_eSkillType == LEAVE_WAIT )
  {
    SkillSlot.SKILLINFO.wSkill = SkillID;
    HIWORD(SkillSlot.dwSkillSlot) = SkillSlot.SKILLINFO.cLockCount & 1;
    cOffencerJudge = 0;
    cDefenserJudge = 0;
    Skill::CProcessTable::UseSkill(
      CSingleton<Skill::CProcessTable>::ms_pSingleton,
      (AtType)SkillSlot,
      this,
      this,
      &cOffencerJudge,
      &cDefenserJudge,
      (unsigned __int16 *)&wError);
  }
  v10 = this->m_DBData.m_Skill.wSlotNum;
  if ( Index_In < v10 )
  {
    SkillSlot = this->m_DBData.m_Skill.SSlot[Index_In];
    if ( SkillSlot.SKILLINFO.cLockCount <= 0 )
    {
      if ( !SkillSlot.SKILLINFO.cSkillLevel )
      {
        wError = 3;
        goto LABEL_24;
      }
      if ( SkillSlot.SKILLINFO.cSkillLevel == 1 )
      {
        if ( v9->m_ProtoTypes[0].m_bIsClassSkill )
        {
          wError = 5;
        }
        else
        {
          v11 = Index_In;
          if ( Index_In < v10 - 1 )
          {
            v12 = Index_In;
            do
            {
              this->m_DBData.m_Skill.SSlot[v12].dwSkillSlot = this->m_DBData.m_Skill.SSlot[v12 + 1].dwSkillSlot;
              v12 = ++v11;
            }
            while ( v11 < this->m_DBData.m_Skill.wSlotNum - 1 );
          }
          v13 = (char *)&this->m_DBData.m_Skill + 4 * v11;
          *((_WORD *)v13 + 2) = 0;
          v13[6] = 0;
          v13[7] = 0;
          --this->m_DBData.m_Skill.wSkillNum;
          --this->m_DBData.m_Skill.wSlotNum;
        }
        goto LABEL_24;
      }
    }
    else if ( !SkillSlot.SKILLINFO.cSkillLevel )
    {
      bUnlockFlag = CCharacter::SkillUnLock(this, Index_In);
      goto LABEL_24;
    }
    --this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cSkillLevel;
    --this->m_DBData.m_Skill.wSkillNum;
    goto LABEL_24;
  }
  wError = 2;
LABEL_24:
  CCharacter::CalculatePassiveSkill(
    this,
    (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
    (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand],
    Attach);
  v14 = wError;
  if ( !(_WORD)wError )
  {
    CCharacter::UpdateQuickSlotSkill(this, this->m_DBData.m_Skill.SSlot[Index_In]);
    v14 = wError;
  }
  if ( !bUnlockFlag )
  {
    m_lpGameClientDispatch = this->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
    {
      GameClientSendPacket::SendCharSkillCommand(
        &m_lpGameClientDispatch->m_SendStream,
        this->m_dwCID,
        0x18u,
        Index_In,
        SkillID,
        v14);
      v14 = wError;
    }
  }
  if ( v14 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillErase",
      aDWorkRylSource_28,
      573,
      aCid0x08x_148,
      this->m_dwCID,
      Index_In,
      this->m_DBData.m_Skill.wSlotNum,
      v14);
    LogSkillSlot(&this->m_DBData);
  }
  return 1;
}

//----- (0041BFC0) --------------------------------------------------------
char __thiscall CCharacter::HasSkill(
        CCharacter *this,
        unsigned __int16 usSkillType,
        unsigned __int8 cLockCount,
        signed __int8 cLevel)
{
  unsigned __int16 m_usSkill_ID; // bx
  CSkillMgr::ProtoTypeArray *SkillProtoType; // esi
  unsigned __int16 m_usParentSkill; // ax
  int v8; // ebx
  int v10; // ebp
  char *i; // eax
  int v12; // ecx
  bool v13; // cc

  m_usSkill_ID = usSkillType;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, usSkillType);
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::HasSkill",
      aDWorkRylSource_28,
      712,
      aCid0x08x_77,
      this->m_dwCID,
      usSkillType);
    return 0;
  }
  m_usParentSkill = SkillProtoType->m_ProtoTypes[0].m_usParentSkill;
  if ( m_usParentSkill )
  {
    SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, m_usParentSkill);
    m_usSkill_ID = SkillProtoType->m_ProtoTypes[0].m_usSkill_ID;
    usSkillType = m_usSkill_ID;
  }
  if ( SkillProtoType->m_ProtoTypes[0].m_eSkillType == MAX_TITLE )
    return 1;
  if ( SkillProtoType->m_ProtoTypes[0].m_bIsClassSkill )
  {
    v8 = (SkillProtoType->m_ProtoTypes[0].m_usSkill_ID - 0x8000) >> 8;
    if ( this->m_DBData.m_Info.Class != (unsigned __int8)v8
      && CClass::GetPreviousJob(this->m_DBData.m_Info.Class) != (_BYTE)v8 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::HasSkill",
        aDWorkRylSource_28,
        736,
        aCid0x08x_146,
        this->m_dwCID,
        SkillProtoType->m_ProtoTypes[0].m_usSkill_ID,
        this->m_DBData.m_Info.Class);
      return 0;
    }
    return 1;
  }
  if ( this->m_DBData.m_Skill.wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::HasSkill",
      aDWorkRylSource_28,
      747,
      aCid0x08x_133,
      this->m_dwCID,
      this->m_DBData.m_Skill.wSlotNum);
    return 0;
  }
  v10 = 0;
  if ( !this->m_DBData.m_Skill.wSlotNum )
    return 0;
  for ( i = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cLockCount; m_usSkill_ID != *((_WORD *)i - 1); i += 4 )
  {
LABEL_22:
    if ( ++v10 >= this->m_DBData.m_Skill.wSlotNum )
      return 0;
  }
  v12 = *i;
  v13 = cLockCount < v12;
  if ( cLockCount == v12 )
  {
    if ( (unsigned __int8)cLevel <= i[1] )
      return 1;
    v13 = cLockCount < v12;
  }
  if ( !v13 )
  {
    m_usSkill_ID = usSkillType;
    goto LABEL_22;
  }
  return 1;
}

//----- (0041C140) --------------------------------------------------------
__int16 __thiscall CCharacter::GetSkillLockCount(CCharacter *this, unsigned __int16 usSkillType)
{
  unsigned __int16 v2; // di
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  unsigned __int16 m_usParentSkill; // ax
  int v7; // eax
  SKILLSLOT *i; // ecx

  v2 = usSkillType;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, usSkillType);
  if ( !SkillProtoType )
    return -1;
  m_usParentSkill = SkillProtoType->m_ProtoTypes[0].m_usParentSkill;
  if ( m_usParentSkill )
    v2 = m_usParentSkill;
  if ( this->m_DBData.m_Skill.wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::GetSkillLockCount",
      aDWorkRylSource_28,
      786,
      aCid0x08x_133,
      this->m_dwCID,
      this->m_DBData.m_Skill.wSlotNum);
    return -1;
  }
  v7 = 0;
  if ( !this->m_DBData.m_Skill.wSlotNum )
    return -1;
  for ( i = this->m_DBData.m_Skill.SSlot; v2 != i->SKILLINFO.wSkill; ++i )
  {
    if ( ++v7 >= this->m_DBData.m_Skill.wSlotNum )
      return -1;
  }
  return i->SKILLINFO.cLockCount;
}

//----- (0041C1D0) --------------------------------------------------------
__int16 __thiscall CCharacter::GetSkillLevel(CCharacter *this, unsigned __int16 usSkillType)
{
  unsigned __int16 v2; // di
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  unsigned __int16 m_usParentSkill; // ax
  int v7; // eax
  SKILLSLOT *i; // ecx

  v2 = usSkillType;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, usSkillType);
  if ( !SkillProtoType )
    return -1;
  m_usParentSkill = SkillProtoType->m_ProtoTypes[0].m_usParentSkill;
  if ( m_usParentSkill )
    v2 = m_usParentSkill;
  if ( this->m_DBData.m_Skill.wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::GetSkillLevel",
      aDWorkRylSource_28,
      818,
      aCid0x08x_133,
      this->m_dwCID,
      this->m_DBData.m_Skill.wSlotNum);
    return -1;
  }
  v7 = 0;
  if ( !this->m_DBData.m_Skill.wSlotNum )
    return -1;
  for ( i = this->m_DBData.m_Skill.SSlot; v2 != i->SKILLINFO.wSkill; ++i )
  {
    if ( ++v7 >= this->m_DBData.m_Skill.wSlotNum )
      return -1;
  }
  return i->SKILLINFO.cSkillLevel;
}

//----- (0041C260) --------------------------------------------------------
__int16 __thiscall CCharacter::GetSkillSlotIndex(CCharacter *this, unsigned __int16 usSkillType)
{
  unsigned __int16 v2; // di
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  unsigned __int16 m_usParentSkill; // ax
  int v6; // eax
  SKILLSLOT *SSlot; // ecx

  v2 = usSkillType;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, usSkillType);
  if ( !SkillProtoType )
    goto LABEL_10;
  m_usParentSkill = SkillProtoType->m_ProtoTypes[0].m_usParentSkill;
  if ( m_usParentSkill )
    v2 = m_usParentSkill;
  if ( this->m_DBData.m_Skill.wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::GetSkillSlotIndex",
      aDWorkRylSource_28,
      850,
      aCid0x08x_133,
      this->m_dwCID,
      this->m_DBData.m_Skill.wSlotNum);
    LOWORD(v6) = -1;
    return v6;
  }
  v6 = 0;
  if ( !this->m_DBData.m_Skill.wSlotNum )
  {
LABEL_10:
    LOWORD(v6) = -1;
    return v6;
  }
  SSlot = this->m_DBData.m_Skill.SSlot;
  while ( v2 != SSlot->SKILLINFO.wSkill )
  {
    ++v6;
    ++SSlot;
    if ( v6 >= this->m_DBData.m_Skill.wSlotNum )
      goto LABEL_10;
  }
  return v6;
}

//----- (0041C2F0) --------------------------------------------------------
void __thiscall CChantSpell::~CChantSpell(CChantSpell *this)
{
  this->__vftable = (CChantSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';

//----- (0041C300) --------------------------------------------------------
CSpell *__thiscall CSpell::`scalar deleting destructor'(CSpell *this, char a2)
{
  this->__vftable = (CSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D8E10: using guessed type void *CSpell::`vftable';

//----- (0041C330) --------------------------------------------------------
void __thiscall CSpell::Spell_Info::Spell_Info(
        CSpell::Spell_Info *this,
        const Skill::ProtoType *SkillProtoType,
        CAggresiveCreature *lpCaster,
        unsigned __int8 cSpellType,
        unsigned __int16 wSpellID,
        unsigned __int16 wSpellLevel,
        unsigned __int16 wDurationSec)
{
  this->m_SkillProtoType = SkillProtoType;
  this->m_lpCaster = lpCaster;
  this->m_wDurationSec = wDurationSec;
  this->m_wSpellID = wSpellID;
  this->m_wSpellLevel = wSpellLevel;
  this->m_cSpellType = cSpellType;
}

//----- (0041C370) --------------------------------------------------------
char __thiscall CAggresiveCreature::Dead(CAggresiveCreature *this, CAggresiveCreature *pOffencer)
{
  CAggresiveCreature *v2; // esi

  v2 = this;
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&v2->m_SpellMgr.m_AffectedInfo);
  v2 = (CAggresiveCreature *)((char *)v2 + 220);
  CCastingSpell::ClearEnchant((CCastingSpell *)v2);
  CCastingSpell::DisableChant((CCastingSpell *)v2, 0);
  return 1;
}

//----- (0041C3B0) --------------------------------------------------------
CInvincibleSpell *__thiscall CInvincibleSpell::`scalar deleting destructor'(CInvincibleSpell *this, char a2)
{
  CInvincibleSpell::~CInvincibleSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0041C3D0) --------------------------------------------------------
void __thiscall CInvincibleSpell::~CInvincibleSpell(CInvincibleSpell *this)
{
  this->__vftable = (CInvincibleSpell_vtbl *)&CInvincibleSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CInvincibleSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4D8E20: using guessed type void *CInvincibleSpell::`vftable';

//----- (0041C430) --------------------------------------------------------
int __thiscall CCharacter::CalculateFixLevelGap(CCharacter *this, CAggresiveCreature *pDefender)
{
  if ( (pDefender->m_dwCID & 0x80000000) == 0 )
    return CAggresiveCreature::CalculateLevelGap(this, (int)pDefender);
  else
    return -pDefender->CalculateFixLevelGap(pDefender, this);
}

//----- (0041C460) --------------------------------------------------------
void __thiscall CCharacter::Casting(CCharacter *this, AtType attackType, AtNode *attackNode)
{
  CAffectedSpell::RemoveEnchantBySpellType(&this->m_SpellMgr.m_AffectedInfo, 0x20000000u);
}

//----- (0041C480) --------------------------------------------------------
BOOL __thiscall CCharacter::Dead(CCharacter *this, CCharacter *pOffencer)
{
  CMonster *m_lpSummonee; // ecx
  CCharacter *v4; // edi
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  bool IsSummonee; // al
  CVirtualMonsterMgr *v9; // eax
  unsigned int m_nExp; // ebx
  int m_nLevel; // eax
  signed __int64 v12; // rax
  unsigned int m_nExp_high; // ecx
  unsigned int v14; // ebp
  unsigned int v15; // ecx
  unsigned int v16; // ebp
  VirtualArea::CVirtualAreaMgr *v17; // eax
  VirtualArea::CBGServerMap *v18; // eax
  VirtualArea::CBGServerMap *v19; // ebp
  unsigned __int16 v20; // bx
  char *MapTypeName; // eax
  CCastingSpell *p_m_CastingInfo; // esi
  unsigned int v24; // [esp-24h] [ebp-4Ch]
  unsigned int v25; // [esp-18h] [ebp-40h]
  int v26; // [esp-Ch] [ebp-34h]
  unsigned __int16 v27; // [esp-8h] [ebp-30h]
  int v28; // [esp-8h] [ebp-30h]
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-2Ch]
  unsigned int m_dwCID; // [esp-4h] [ebp-2Ch]
  unsigned int dwPrevExp; // [esp+Ch] [ebp-1Ch]
  unsigned int dwPrevExp_4; // [esp+10h] [ebp-18h]
  char szOffencerNation[8]; // [esp+14h] [ebp-14h] BYREF
  char szDefencerNation[8]; // [esp+1Ch] [ebp-Ch] BYREF
  unsigned __int8 pOffencera; // [esp+2Ch] [ebp+4h]

  m_lpSummonee = this->m_lpSummonee;
  if ( m_lpSummonee )
    m_lpSummonee->Dead(m_lpSummonee, 0);
  v4 = pOffencer;
  if ( pOffencer )
  {
    if ( pOffencer->m_CellPos.m_wMapIndex )
    {
      m_wMapIndex = pOffencer->m_CellPos.m_wMapIndex;
      Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
      if ( !VirtualArea )
        goto LABEL_11;
      m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
      if ( !m_pVirtualMonsterMgr )
        goto LABEL_11;
      IsSummonee = CVirtualMonsterMgr::IsSummonee(m_pVirtualMonsterMgr, pOffencer->m_dwCID);
    }
    else
    {
      m_dwCID = pOffencer->m_dwCID;
      v9 = (CVirtualMonsterMgr *)CCreatureManager::GetInstance();
      IsSummonee = CVirtualMonsterMgr::IsSummonee(v9, m_dwCID);
    }
    if ( IsSummonee )
      v4 = *(CCharacter **)&pOffencer->m_friendList.m_friendList._Alval.std::_Allocator_base<CFriendList::Rebind>;
  }
LABEL_11:
  m_nExp = this->m_CreatureStatus.m_nExp;
  pOffencera = 0;
  dwPrevExp = m_nExp;
  dwPrevExp_4 = HIDWORD(this->m_CreatureStatus.m_nExp);
  if ( v4 )
  {
    if ( (v4->m_dwCID & 0x80000000) == 0 )
    {
      if ( this->IsEnemy(this, v4) == HOSTILITY )
      {
        CThreat::DivisionFame(&this->m_Threat);
        if ( v4->m_CellPos.m_wMapIndex )
        {
          v27 = v4->m_CellPos.m_wMapIndex;
          v17 = VirtualArea::CVirtualAreaMgr::GetInstance();
          v18 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v17, v27);
          v19 = v18;
          if ( v18 )
          {
            if ( HIWORD(v18->m_dwVID) == 0x8000 && !v18->m_MapInfo.m_cMapType )
            {
              VirtualArea::CBGServerMap::KillChar(v18, this->m_dwCID, v4);
              if ( v4->m_DBData.m_Info.Nationality )
                strcpy(szOffencerNation, "AKHAN");
              else
                strcpy(szOffencerNation, "HUMAN");
              if ( this->m_DBData.m_Info.Nationality )
                strcpy(szDefencerNation, "AKHAN");
              else
                strcpy(szDefencerNation, "HUMAN");
              v28 = v19->m_MapInfo.m_wScore[1];
              v20 = v19->m_wMapIndex;
              v26 = v19->m_MapInfo.m_wScore[0];
              v25 = this->m_dwCID;
              v24 = v4->m_dwCID;
              MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(v19);
              CServerLog::DetailLog(
                &g_Log,
                LOG_DETAIL,
                "CCharacter::Dead",
                aDWorkRylSource_41,
                579,
                aBattleServerLo_4,
                v20 & 0x7FFF,
                MapTypeName,
                v24,
                v4->m_DBData.m_Info.Name,
                szOffencerNation,
                v25,
                this->m_DBData.m_Info.Name,
                szDefencerNation,
                v26,
                v28);
              m_nExp = dwPrevExp;
            }
          }
        }
      }
    }
    else if ( !CAffectedSpell::IsSpellOfEnemyCharacter(&this->m_SpellMgr.m_AffectedInfo) )
    {
      m_nLevel = this->m_CreatureStatus.m_nLevel;
      if ( m_nLevel >= 10 )
      {
        if ( (unsigned int)(m_nLevel - 1) > 0x62 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::Dead",
            aDWorkRylSource_41,
            531,
            aCid0x08x_92,
            this->m_dwCID,
            this->m_CreatureStatus.m_nLevel);
          pOffencera = 1;
        }
        else
        {
          v12 = (unsigned __int64)((double)*(__int64 *)&(&szNoRemove_6)[2 * m_nLevel] * 0.03);
          m_nExp_high = HIDWORD(this->m_CreatureStatus.m_nExp);
          v14 = this->m_CreatureStatus.m_nExp;
          if ( v12 >= __SPAIR64__(m_nExp_high, v14) )
          {
            v16 = 0;
            v15 = 0;
          }
          else
          {
            v15 = (__PAIR64__(m_nExp_high, v14) - v12) >> 32;
            v16 = v14 - v12;
          }
          LODWORD(this->m_DBData.m_Info.Exp) = v16;
          LODWORD(this->m_CreatureStatus.m_nExp) = v16;
          HIDWORD(this->m_CreatureStatus.m_nExp) = v15;
          HIDWORD(this->m_DBData.m_Info.Exp) = v15;
          this->m_FightInfo.m_nRestoreExp = (__int64)(__PAIR64__(dwPrevExp_4, m_nExp) - __PAIR64__(v15, v16)) / 2;
        }
      }
    }
  }
  CCharacter::DuelInit(this, 4u);
  GAMELOG::LogCharDead(
    this,
    __PAIR64__(dwPrevExp_4, m_nExp),
    this->m_CreatureStatus.m_nExp,
    v4,
    this->m_CreatureStatus.m_nLevel,
    pOffencera);
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&this->m_SpellMgr.m_AffectedInfo);
  p_m_CastingInfo = &this->m_SpellMgr.m_CastingInfo;
  CCastingSpell::ClearEnchant(p_m_CastingInfo);
  CCastingSpell::DisableChant(p_m_CastingInfo, 0);
  return pOffencera == 0;
}

//----- (0041C7A0) --------------------------------------------------------
char __thiscall CCharacter::AutoRespawn(CCharacter *this)
{
  CServerSetup *Instance; // eax
  unsigned __int8 Nationality; // al
  unsigned __int16 m_nMaxMP; // dx
  signed __int8 cZone; // [esp+4h] [ebp-1Ch]
  POS respawnPos[2]; // [esp+8h] [ebp-18h]

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) != 4 )
    return 0;
  Nationality = this->m_DBData.m_Info.Nationality;
  respawnPos[0].fPointX = 1403.0601;
  respawnPos[0].fPointY = 18.25;
  respawnPos[0].fPointZ = 1958.41;
  respawnPos[1].fPointX = 2014.29;
  respawnPos[1].fPointY = 63.5;
  respawnPos[1].fPointZ = 1648.47;
  if ( Nationality )
  {
    if ( Nationality != 1 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::AutoRespawn",
        aDWorkRylSource_41,
        735,
        aCid0x08x_111,
        this->m_dwCID,
        Nationality);
      return 0;
    }
    cZone = 2;
  }
  else
  {
    cZone = 1;
  }
  m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  this->m_CreatureStatus.m_nNowHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  this->m_CreatureStatus.m_nNowMP = m_nMaxMP;
  if ( CCharacter::MoveZone(this, respawnPos[Nationality], cZone, 0) )
    return 1;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCharacter::AutoRespawn",
    aDWorkRylSource_41,
    746,
    aCid0x08x_64,
    this->m_dwCID);
  return 0;
}

//----- (0041C8C0) --------------------------------------------------------
bool __thiscall CCharacter::UseAmmo(CCharacter *this, Item::CUseItem *pUseItem)
{
  Item::CItem *v2; // eax
  Item::CItem *v4; // eax
  Item::CItem *v5; // eax

  switch ( pUseItem->m_ItemInfo->m_DetailData.m_cItemType )
  {
    case 0x1Bu:
      v5 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand];
      return v5 && v5->m_ItemInfo->m_DetailData.m_cItemType == 42;
    case 0x2Du:
      v4 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
      if ( v4 && v4->m_ItemInfo->m_DetailData.m_cItemType == 12 )
        goto LABEL_6;
      return 0;
    case 0x2Eu:
      v2 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
      if ( v2 && v2->m_ItemInfo->m_DetailData.m_cItemType == 13 )
      {
LABEL_6:
        --pUseItem->m_ItemData.m_cNumOrDurability;
        return 1;
      }
      return 0;
  }
  return 1;
}

//----- (0041C950) --------------------------------------------------------
bool __thiscall CCharacter::IsPeaceMode(CCharacter *this)
{
  return this->m_PeaceMode.m_bPeace;
}

//----- (0041C960) --------------------------------------------------------
void __thiscall CCharacter::SetPeaceMode(CCharacter *this, PeaceModeInfo PeaceMode, bool bLogin)
{
  unsigned int v3; // edx
  char dwHighDateTime_high; // cl
  CSingleDispatch *DispatchTable; // eax

  v3 = *(unsigned int *)((char *)&PeaceMode.m_FileTime.dwLowDateTime + 3);
  *(_DWORD *)&this->m_PeaceMode.m_bPeace = *(_DWORD *)&PeaceMode.m_bPeace;
  dwHighDateTime_high = HIBYTE(PeaceMode.m_FileTime.dwHighDateTime);
  *(unsigned int *)((char *)&this->m_PeaceMode.m_FileTime.dwLowDateTime + 3) = v3;
  HIBYTE(this->m_PeaceMode.m_FileTime.dwHighDateTime) = dwHighDateTime_high;
  if ( bLogin )
  {
    DispatchTable = CChatDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage((CSingleDispatch::Storage *)&PeaceMode, DispatchTable);
    if ( *(_DWORD *)&PeaceMode.m_bPeace )
    {
      if ( !CChatDispatch::SendCharInfoChanged((CSendStream *)(*(_DWORD *)&PeaceMode.m_bPeace + 64), this) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SetPeaceMode",
          aDWorkRylSource_41,
          909,
          aUidDCidD,
          this->m_dwUID,
          this->m_dwCID);
    }
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&PeaceMode);
  }
}

//----- (0041CA20) --------------------------------------------------------
char __thiscall CCharacter::SetPeaceMode(CCharacter *this, bool bPeace)
{
  CSingleDispatch *DispatchTable; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-40h] BYREF
  PeaceModeInfo peaceModeInfo; // [esp+10h] [ebp-38h] BYREF
  _SYSTEMTIME NowSysTime; // [esp+1Ch] [ebp-2Ch] BYREF
  _SYSTEMTIME PreSysTime; // [esp+2Ch] [ebp-1Ch] BYREF
  int v10; // [esp+44h] [ebp-4h]

  if ( bPeace == this->m_PeaceMode.m_bPeace )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SetPeaceMode",
      aDWorkRylSource_41,
      921,
      aCid0x08x_298,
      this->m_dwCID);
    return -1;
  }
  else
  {
    FileTimeToSystemTime(&this->m_PeaceMode.m_FileTime, &PreSysTime);
    GetLocalTime(&NowSysTime);
    if ( PreSysTime.wYear == NowSysTime.wYear
      && PreSysTime.wMonth == NowSysTime.wMonth
      && (unsigned __int16)(NowSysTime.wHour + 24 * NowSysTime.wDay)
       - (unsigned __int16)(PreSysTime.wHour + 24 * PreSysTime.wDay) < 24 )
    {
      return LOBYTE(PreSysTime.wHour)
           + 24 * LOBYTE(PreSysTime.wDay)
           - (LOBYTE(NowSysTime.wHour)
            + 24 * LOBYTE(NowSysTime.wDay))
           + 24;
    }
    else
    {
      peaceModeInfo.m_FileTime.dwHighDateTime = 0;
      peaceModeInfo.m_FileTime.dwLowDateTime = 0;
      peaceModeInfo.m_bPeace = bPeace;
      SystemTimeToFileTime(&NowSysTime, &peaceModeInfo.m_FileTime);
      CCharacter::SetPeaceMode(this, peaceModeInfo, 0);
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
      v10 = 0;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
        GameClientSendPacket::SendConfigInfoDB((CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8], this);
      if ( bPeace )
      {
        this->m_DBData.m_Info.Fame = (unsigned __int64)((double)this->m_DBData.m_Info.Fame * 0.89999998);
        m_lpGameClientDispatch = this->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          GameClientSendPacket::SendCharFameInfo(
            &m_lpGameClientDispatch->m_SendStream,
            this,
            szLoseCharName,
            szLoseCharName,
            0);
      }
      v10 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return 0;
    }
  }
}

//----- (0041CBF0) --------------------------------------------------------
CCreature::MutualType __thiscall CCharacter::IsEnemy(CCharacter *this, CCharacter *lpTarget)
{
  CCreature::MutualType result; // eax
  unsigned int m_dwCID; // eax
  CCreatureManager *Instance; // eax
  unsigned __int8 IsAttackable; // al
  CCellManager *v7; // ebp
  CParty *m_dwPartyID; // ebx
  signed int v9; // eax
  unsigned int v10; // ebx
  unsigned __int8 v11; // bl
  bool v12; // zf
  unsigned __int8 v13; // [esp-8h] [ebp-1Ch]
  unsigned __int8 m_cAttackableCreatureType; // [esp-4h] [ebp-18h]

  if ( !lpTarget )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::IsEnemy",
      aDWorkRylSource_41,
      977,
      aCid0x08x_121,
      this->m_dwCID);
    return 1;
  }
  m_dwCID = lpTarget->m_dwCID;
  if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
  {
    m_cAttackableCreatureType = lpTarget->m_cAttackableCreatureType;
    v13 = this->m_cAttackableCreatureType;
    Instance = CCreatureManager::GetInstance();
    IsAttackable = CCreatureManager::IsAttackable(Instance, v13, m_cAttackableCreatureType);
    if ( !IsAttackable || IsAttackable == 2 && LOBYTE(lpTarget->m_wHistoryQuest[15]) != 4 )
      return 1;
  }
  v7 = CCellManager::GetInstance();
  if ( this->m_FightInfo.m_pDuelOpponent == lpTarget )
    return 2;
  if ( this->m_pParty )
  {
    if ( lpTarget->GetParty(lpTarget) )
    {
      m_dwPartyID = (CParty *)this->m_pParty[1].m_Party.m_dwPartyID;
      if ( m_dwPartyID == lpTarget->GetParty(lpTarget) )
        return 2;
    }
  }
  if ( !lpTarget->GetNation(lpTarget) )
  {
    v9 = lpTarget->m_dwCID;
    if ( v9 >= 0 && (v9 & 0x40000000) != 0 )
      return 0;
    if ( Creature::GetCreatureType(v9) == 2 )
      return 2;
  }
  if ( lpTarget->GetStatusFlag(lpTarget, 0x20000000u) )
    return 1;
  if ( this->GetGID(this) )
  {
    v10 = lpTarget->GetGID(lpTarget);
    if ( this->GetGID(this) == v10 )
      return 0;
  }
  if ( this->IsPeaceMode(this) )
    return !lpTarget->IsPeaceMode(lpTarget);
  v11 = lpTarget->GetNation(lpTarget);
  if ( this->GetNation(this) == v11 )
    return 0;
  if ( lpTarget->IsPeaceMode(lpTarget) )
    return 1;
  if ( CCellManager::IsSafetyZone(v7, this->m_CurrentPos) == 1 )
    return 1;
  v12 = CCellManager::IsSafetyZone(v7, lpTarget->m_CurrentPos) == 1;
  result = HOSTILITY;
  if ( v12 )
    return 1;
  return result;
}

//----- (0041CDC0) --------------------------------------------------------
char __thiscall CCharacter::GetEliteBonus(CCharacter *this)
{
  CCreatureManager *Instance; // eax
  char v3; // bl
  CServerSetup *v4; // eax
  unsigned int Fame; // esi
  char result; // al
  char cServerZone; // [esp+Bh] [ebp-1h]

  Instance = CCreatureManager::GetInstance();
  v3 = HIBYTE(*(_WORD *)&Instance->m_EliteBonus)
     * (2 * ((unsigned __int8)this->m_DBData.m_Info.Nationality == (char)*(_WORD *)&Instance->m_EliteBonus) - 1);
  v4 = CServerSetup::GetInstance();
  cServerZone = CServerSetup::GetServerZone(v4);
  if ( this->GetNation(this) == 1 && cServerZone == 5 || this->GetNation(this) == 2 && cServerZone == 4 )
    v3 -= 7;
  Fame = this->m_DBData.m_Info.Fame;
  if ( Fame > 0x7D0 )
  {
    if ( Fame > 0x2710 )
    {
      if ( Fame > 0x61A8 )
      {
        if ( Fame > 0xC350 )
        {
          if ( Fame > 0x124F8 )
          {
            if ( Fame > 0x186A0 )
            {
              if ( Fame > 0x249F0 )
              {
                if ( Fame > 0x3D090 )
                {
                  if ( Fame > 0x7A120 )
                    v3 += 9;
                  else
                    v3 += 8;
                }
                else
                {
                  v3 += 7;
                }
              }
              else
              {
                v3 += 6;
              }
            }
            else
            {
              v3 += 5;
            }
          }
          else
          {
            v3 += 4;
          }
        }
        else
        {
          v3 += 3;
        }
      }
      else
      {
        v3 += 2;
      }
    }
    else
    {
      ++v3;
    }
  }
  if ( v3 > 10 )
    return 10;
  result = -10;
  if ( v3 >= -10 )
    return v3;
  return result;
}





//----- (0041CEB0) --------------------------------------------------------
__int16 __cdecl Skill::CFunctions::ConsumeMP(AtType attackType, CAggresiveCreature *lpSkillUser)
{
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CSkillMgr::ProtoTypeArray *v4; // ebp
  __int16 v5; // ax
  signed __int16 v6; // si

  if ( (*((_BYTE *)&attackType + 2) & 0xF0u) >= 0x50 )
    return -1;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  v4 = SkillProtoType;
  if ( !SkillProtoType )
    return -1;
  if ( SkillProtoType->m_ProtoTypes[0].m_bIsClassSkill )
    v5 = 0;
  else
    v5 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
  v6 = v4->m_ProtoTypes[v5].m_StartMP
     + v4->m_ProtoTypes[*((unsigned __int8 *)&attackType + 2) >> 4].m_LevelMP * attackType.m_cSkillLevel;
  if ( lpSkillUser->m_CreatureStatus.m_nNowMP < v6 )
    return -1;
  if ( lpSkillUser->GetConsumeMPCount(lpSkillUser) <= 0 )
    lpSkillUser->m_CreatureStatus.m_nNowMP -= v6;
  return v6;
}

//----- (0041CF60) --------------------------------------------------------
char __thiscall CCharacter::AttackCID(
        CCharacter *this,
        AtType attackType,
        AtNode *attackNode,
        unsigned __int16 *wError)
{
  CPerformanceCheck *Instance; // eax
  bool m_bLogout; // zf
  char v8; // bl
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CSkillMgr::ProtoTypeArray *v11; // esi
  unsigned __int16 v12; // dx
  signed int v13; // ecx
  VirtualArea::CVirtualAreaMgr *v14; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CMsgProc *AggresiveCreature; // eax
  CCreatureManager *v18; // eax
  Skill::Target::Type m_eTargetType; // eax
  unsigned __int8 v20; // al
  CSkillMgr::ProtoTypeArray *v21; // eax
  CCreatureManager *v22; // eax
  double v23; // st5
  double v24; // st4
  double v25; // st7
  double v26; // st5
  int v27; // ebp
  VirtualArea::CVirtualAreaMgr *v28; // eax
  VirtualArea::CBGServerMap *v29; // eax
  CVirtualMonsterMgr *v30; // ecx
  CMsgProc *v31; // eax
  CCreatureManager *v32; // eax
  CMsgProc *v33; // eax
  CMsgProc *v34; // esi
  CCreatureManager *v35; // eax
  int v36; // eax
  CCreature::MutualType v37; // eax
  Skill::ProtoType *v38; // esi
  CParty *v39; // eax
  char v40; // bl
  bool v41; // bl
  char v42; // bl
  __int64 fEstimateTime; // [esp+0h] [ebp-78h]
  __int64 fEstimateTimea; // [esp+0h] [ebp-78h]
  __int64 fEstimateTimeb; // [esp+0h] [ebp-78h]
  float fEstimateTimec; // [esp+0h] [ebp-78h]
  unsigned __int16 fEstimateTime_4; // [esp+4h] [ebp-74h]
  signed int fEstimateTime_4a; // [esp+4h] [ebp-74h]
  signed int fEstimateTime_4b; // [esp+4h] [ebp-74h]
  unsigned __int16 fEstimateTime_4c; // [esp+4h] [ebp-74h]
  signed int fEstimateTime_4d; // [esp+4h] [ebp-74h]
  signed int fEstimateTime_4e; // [esp+4h] [ebp-74h]
  int cDefenderNum; // [esp+18h] [ebp-60h]
  char cTargetType; // [esp+1Ch] [ebp-5Ch]
  CPerformanceInstrument functionInstrument; // [esp+2Ch] [ebp-4Ch] BYREF
  CAggresiveCreature *lpAggresiveCreature[10]; // [esp+44h] [ebp-34h] BYREF
  int v57; // [esp+74h] [ebp-4h]
  unsigned __int8 attackNodea; // [esp+80h] [ebp+8h]

  functionInstrument.m_szfunctionName = "CCharacter::AttackCID";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CCharacter::AttackCID", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  m_bLogout = this->m_bLogout;
  v57 = 0;
  if ( m_bLogout || !this->m_CreatureStatus.m_nNowHP )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::AttackCID",
      aDWorkRylSource_41,
      73,
      a1,
      this->m_DBData.m_Info.Name);
    *wError = 2;
    v57 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 1;
  }
  LOBYTE(cDefenderNum) = 10;
  if ( attackNode->m_wDefenserNum <= 0xAu )
    LOBYTE(cDefenderNum) = attackNode->m_wDefenserNum;
  memset(lpAggresiveCreature, 0, sizeof(lpAggresiveCreature));
  cTargetType = 3;
  if ( !(_BYTE)cDefenderNum )
  {
    v8 = ((int (__thiscall *)(_DWORD, _DWORD, _DWORD, _DWORD, _DWORD))this->Attack)(
           this,
           attackType,
           0,
           lpAggresiveCreature,
           attackNode->m_cDefenserJudge);
    v57 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v8;
  }
  if ( (attackType.m_wType & 0x8000) == 0 )
    goto LABEL_28;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  v11 = SkillProtoType;
  if ( !SkillProtoType )
  {
    HIDWORD(fEstimateTime) = attackType.m_wType;
    LODWORD(fEstimateTime) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::AttackCID",
      aDWorkRylSource_41,
      96,
      aCid0x08x_77,
      fEstimateTime);
LABEL_33:
    v57 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
  if ( 0.0 == SkillProtoType->m_ProtoTypes[0].m_EffectDistance
    && 0.0 == SkillProtoType->m_ProtoTypes[0].m_EffectExtent
    && this->m_dwCID != attackNode->m_dwDefenser[0] )
  {
    HIDWORD(fEstimateTimea) = attackType.m_wType;
    LODWORD(fEstimateTimea) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::AttackCID",
      aDWorkRylSource_41,
      105,
      aCid0x08x_53,
      fEstimateTimea);
    goto LABEL_33;
  }
  if ( !this->m_CellPos.m_wMapIndex )
  {
    fEstimateTime_4b = attackNode->m_dwDefenser[0];
    v22 = CCreatureManager::GetInstance();
    AggresiveCreature = CCreatureManager::GetAggresiveCreature(v22, fEstimateTime_4b);
    goto LABEL_35;
  }
  if ( Creature::GetCreatureType(attackNode->m_dwDefenser[0]) == 2 )
  {
    fEstimateTime_4 = v12;
    v14 = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v14, fEstimateTime_4);
    if ( VirtualArea )
    {
      m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
      if ( m_pVirtualMonsterMgr )
      {
        AggresiveCreature = CVirtualMonsterMgr::GetAggresiveCreature(m_pVirtualMonsterMgr, attackNode->m_dwDefenser[0]);
        goto LABEL_35;
      }
    }
  }
  else
  {
    fEstimateTime_4a = v13;
    v18 = CCreatureManager::GetInstance();
    AggresiveCreature = CCreatureManager::GetAggresiveCreature(v18, fEstimateTime_4a);
    if ( AggresiveCreature && LOWORD(AggresiveCreature[101].__vftable) == this->m_CellPos.m_wMapIndex )
    {
LABEL_35:
      if ( AggresiveCreature )
      {
        v23 = this->m_CurrentPos.m_fPointX - *(float *)&AggresiveCreature[1].__vftable;
        v24 = this->m_CurrentPos.m_fPointZ - *(float *)&AggresiveCreature[3].__vftable;
        v25 = v23 * v23 + v24 * v24;
        v26 = v11->m_ProtoTypes[0].m_EffectDistance + 10.0;
        if ( v25 > v26 * v26 )
        {
          *wError = 3;
          goto LABEL_33;
        }
      }
    }
  }
  m_eTargetType = v11->m_ProtoTypes[0].m_eTargetType;
  if ( m_eTargetType == MIDDLE_ADMIN
    || m_eTargetType == LEAVE_WAIT
    || m_eTargetType == 9
    || m_eTargetType == MAX_TITLE
    || m_eTargetType == 8
    || m_eTargetType == (COMMON|0x8) )
  {
    cTargetType = 2;
  }
LABEL_28:
  v20 = 0;
  for ( attackNodea = 0; ; v20 = attackNodea )
  {
    v27 = v20;
    if ( this->m_CellPos.m_wMapIndex )
    {
      if ( (attackNode->m_dwDefenser[v27] & 0x80000000) == 0 )
      {
        fEstimateTime_4d = attackNode->m_dwDefenser[v27];
        v32 = CCreatureManager::GetInstance();
        v33 = CCreatureManager::GetAggresiveCreature(v32, fEstimateTime_4d);
        v34 = v33;
        if ( !v33 || LOWORD(v33[101].__vftable) != this->m_CellPos.m_wMapIndex )
          goto LABEL_60;
        goto LABEL_49;
      }
      fEstimateTime_4c = this->m_CellPos.m_wMapIndex;
      v28 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v29 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v28, fEstimateTime_4c);
      if ( !v29 )
        goto LABEL_60;
      v30 = v29->m_pVirtualMonsterMgr;
      if ( !v30 )
        goto LABEL_60;
      v31 = CVirtualMonsterMgr::GetAggresiveCreature(v30, attackNode->m_dwDefenser[v27]);
    }
    else
    {
      fEstimateTime_4e = attackNode->m_dwDefenser[v27];
      v35 = CCreatureManager::GetInstance();
      v31 = CCreatureManager::GetAggresiveCreature(v35, fEstimateTime_4e);
    }
    v34 = v31;
LABEL_49:
    if ( !v34 )
      goto LABEL_60;
    if ( cTargetType == 2 )
    {
      v36 = (int)v34[8].__vftable;
      if ( (v36 & 0xD0000000) == 0 )
      {
        if ( ((int)v34[103].__vftable & 0x40000000) != 0 )
          goto LABEL_60;
        if ( !((unsigned __int8 (__thiscall *)(CMsgProc *))v34->__vftable[2].operator())(v34) && this->IsPeaceMode(this) )
        {
          *wError = 5;
          goto LABEL_33;
        }
LABEL_58:
        if ( this->IsEnemy(this, (CCreature *)v34) )
          goto LABEL_60;
LABEL_59:
        lpAggresiveCreature[v27] = (CAggresiveCreature *)v34;
        goto LABEL_60;
      }
      if ( Creature::GetCreatureType(v36) == 5 )
      {
        *wError = 7;
        goto LABEL_33;
      }
      if ( LOBYTE(v34[155].__vftable) != 8 )
        goto LABEL_58;
    }
    else
    {
      v37 = this->IsEnemy(this, (CCreature *)v34);
      if ( v37 == HOSTILITY || v34 == (CMsgProc *)this )
        goto LABEL_59;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::AttackCID",
        aDWorkRylSource_41,
        252,
        a2,
        v37,
        this->m_DBData.m_Info.Name);
    }
LABEL_60:
    if ( ++attackNodea >= (unsigned __int8)cDefenderNum )
      break;
  }
  if ( (attackType.m_wType & 0x8000) == 0 || !lpAggresiveCreature[0] )
    goto LABEL_71;
  v21 = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  if ( !v21 )
  {
    HIDWORD(fEstimateTimeb) = attackType.m_wType;
    LODWORD(fEstimateTimeb) = this->m_dwCID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::AttackCID",
      aDWorkRylSource_41,
      268,
      aCid0x08x_77,
      fEstimateTimeb);
    goto LABEL_33;
  }
  v38 = &v21->m_ProtoTypes[*((unsigned __int8 *)&attackType + 2) >> 4];
  if ( 0.0 == v38->m_EffectExtent )
    goto LABEL_71;
  if ( v38->m_eTargetType == 8 )
  {
    v39 = lpAggresiveCreature[0]->GetParty(lpAggresiveCreature[0]);
    if ( v39 )
    {
      v40 = ((int (__thiscall *)(CParty *, AtType, CAggresiveCreature **, unsigned __int8 *, CCharacter *, _DWORD, char))v39->Attack)(
              v39,
              attackType,
              lpAggresiveCreature,
              attackNode->m_cDefenserJudge,
              this,
              LODWORD(v38->m_EffectExtent),
              cTargetType);
      v57 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return v40;
    }
LABEL_71:
    v42 = ((int (__thiscall *)(_DWORD, _DWORD, _BYTE, _DWORD, _DWORD))this->Attack)(
            this,
            attackType,
            cDefenderNum,
            lpAggresiveCreature,
            attackNode->m_cDefenserJudge);
    v57 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v42;
  }
  else
  {
    fEstimateTimec = PI_1 + PI_1;
    v41 = CAggresiveCreature::MultiAttack(
            this,
            attackType,
            cDefenderNum,
            lpAggresiveCreature,
            (CCell *)attackNode->m_cDefenserJudge,
            lpAggresiveCreature[0]->m_CurrentPos,
            0.0,
            v38->m_EffectExtent,
            fEstimateTimec,
            cTargetType);
    v57 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v41;
  }
}
// 41D19B: conditional instruction was optimized away because %cDefenderNum.1!=0
// 41D11B: variable 'v12' is possibly undefined
// 41D141: variable 'v13' is possibly undefined
// 41D4BA: variable 'cDefenderNum' is possibly undefined

//----- (0041D590) --------------------------------------------------------
char __thiscall CCharacter::Attack(
        CCharacter *this,
        AtType attackType,
        unsigned __int8 cDefenderNum,
        CAggresiveCreature **ppDefenders,
        unsigned __int8 *cDefenserJudges)
{
  unsigned __int8 v5; // bl
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  Skill::Target::Type m_eTargetType; // eax
  unsigned __int8 *p_cIndex; // eax
  unsigned __int8 v11; // cl
  CAggresiveCreature **v12; // edx
  CAggresiveCreature **v13; // ebx
  CAggresiveCreature *v14; // eax
  int v15; // edi
  unsigned __int8 *v16; // ebp
  int v17; // eax
  bool v18; // zf
  CAggresiveCreature *v19; // ecx
  char *v20; // edi
  unsigned __int16 m_nNowHP; // dx
  CAggresiveCreature *v22; // ecx
  CSpell *v23; // edx
  CSendStream *v24; // eax
  CAggresiveCreature *v25; // ebx
  __int16 v26; // cx
  unsigned __int16 v27; // dx
  char v28; // al
  AtType v29; // eax
  int v30; // edx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CCell *m_lpCell; // ecx
  unsigned __int8 cIndex; // [esp+21h] [ebp-D1h] BYREF
  __int16 bCheckDeadTarget; // [esp+22h] [ebp-D0h]
  unsigned __int8 nMonsterDefenderNum; // [esp+24h] [ebp-CEh]
  unsigned __int8 cOffencerJudge; // [esp+25h] [ebp-CDh] BYREF
  __int16 cDefender; // [esp+26h] [ebp-CCh]
  int wError; // [esp+2Ah] [ebp-C8h] BYREF
  unsigned __int16 wPrevAttackerHP; // [esp+2Eh] [ebp-C4h]
  CAggresiveCreature *pDefendCharacter; // [esp+32h] [ebp-C0h]
  int nNowHP; // [esp+36h] [ebp-BCh]
  unsigned __int16 nNowMP; // [esp+3Ah] [ebp-B8h]
  char szBuffer[176]; // [esp+3Eh] [ebp-B4h] BYREF

  v5 = cDefenderNum;
  if ( (attackType.m_wType & 0x8000) == 0 && !cDefenderNum )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacter::Attack", aDWorkRylSource_41, 302, (char *)&byte_4D9178);
    return 0;
  }
  bCheckDeadTarget = 257;
  if ( (attackType.m_wType & 0x8000) != 0 )
  {
    SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
    if ( !SkillProtoType )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Attack",
        aDWorkRylSource_41,
        314,
        aCid0x08x_77,
        this->m_dwCID,
        attackType.m_wType);
      return 0;
    }
    if ( attackType.m_wType == 0x8704
      || attackType.m_wType == 0x8805
      || attackType.m_wType == 0x9504
      || attackType.m_wType == 0x9804
      || attackType.m_wType == 0x8205
      || attackType.m_wType == 0x9805
      || SkillProtoType->m_ProtoTypes[0].m_eSkillType == LEAVE_WAIT )
    {
      HIBYTE(bCheckDeadTarget) = 0;
    }
    m_eTargetType = SkillProtoType->m_ProtoTypes[0].m_eTargetType;
    if ( m_eTargetType == LEAVE_WAIT || m_eTargetType == JOIN_WAIT )
      LOBYTE(bCheckDeadTarget) = 0;
  }
  CAffectedSpell::RemoveEnchantBySpellType(&this->m_SpellMgr.m_AffectedInfo, 0x20000000u);
  cOffencerJudge = 0;
  wError = 0;
  cIndex = 5;
  p_cIndex = &cIndex;
  if ( v5 <= 5u )
    p_cIndex = &cDefenderNum;
  v11 = 0;
  this->m_cConsumeMPCount = *p_cIndex;
  nMonsterDefenderNum = 0;
  LOBYTE(cDefender) = 0;
  cIndex = 0;
  if ( v5 )
  {
    do
    {
      v12 = ppDefenders;
      --this->m_cConsumeMPCount;
      v13 = &v12[v11];
      v14 = *v13;
      if ( *v13 && ((_BYTE)bCheckDeadTarget != 1 || v14->m_CreatureStatus.m_nNowHP) )
      {
        if ( (v14->m_dwCID & 0x80000000) == 0 || (++nMonsterDefenderNum, nMonsterDefenderNum <= 5u) )
        {
          v15 = (unsigned __int8)cDefender;
          v16 = &cDefenserJudges[(unsigned __int8)cDefender];
          *v16 = 0;
          wPrevAttackerHP = this->m_CreatureStatus.m_nNowHP;
          v17 = ((int (__thiscall *)(CAggresiveCreature *, AtType, CCharacter *, unsigned __int8 *, unsigned __int8 *, int *))(*v13)->ApplyDamage)(
                  *v13,
                  attackType,
                  this,
                  &cOffencerJudge,
                  v16,
                  &wError);
          v18 = this->m_CreatureStatus.m_nNowHP == 0;
          v19 = *v13;
          v20 = &szBuffer[15 * v15 + 26];
          *((_WORD *)v20 + 6) = v17;
          m_nNowHP = v19->m_CreatureStatus.m_nNowHP;
          LOWORD(v19) = v19->m_CreatureStatus.m_nNowMP;
          LOWORD(nNowHP) = m_nNowHP;
          nNowMP = (unsigned __int16)v19;
          if ( v18 )
          {
            this->m_CreatureStatus.m_nNowHP = wPrevAttackerHP;
            wError = 4;
            break;
          }
          v22 = *v13;
          v18 = ((*v13)->m_dwCID & 0xD0000000) == 0;
          pDefendCharacter = *v13;
          if ( v18 )
          {
            v23 = v22[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[4];
            if ( v23 )
            {
              v23->__vftable[8].Deactivate(v23, this, v17);
              v22 = pDefendCharacter;
            }
            v22->CalculateEquipDurability(v22, (*v16 == 3) + 5);
            v24 = (CSendStream *)pDefendCharacter[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[3];
            if ( v24 )
              GameClientSendPacket::SendCharAttacked(
                v24 + 8,
                this,
                pDefendCharacter,
                attackType,
                this->m_MotionInfo.m_fDirection,
                *((_WORD *)v20 + 6),
                *v16,
                0);
          }
          v25 = *v13;
          v26 = nNowHP;
          v27 = nNowMP;
          *(_DWORD *)v20 = v25->m_dwCID;
          *((_WORD *)v20 + 3) = v26;
          *((_WORD *)v20 + 5) = v27;
          LOBYTE(v27) = *v16;
          *((_WORD *)v20 + 2) = v25->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
          v28 = cDefender + 1;
          *((_WORD *)v20 + 4) = v25->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
          v11 = cIndex;
          v20[14] = v27;
          LOBYTE(cDefender) = v28;
        }
      }
      cIndex = ++v11;
    }
    while ( v11 < cDefenderNum );
  }
  if ( HIBYTE(bCheckDeadTarget) == 1 )
    CAffectedSpell::RemoveEnchantBySpellType(&this->m_SpellMgr.m_AffectedInfo, 0x10000u);
  v29 = attackType;
  if ( (attackType.m_wType & 0x8000u) != 0 && !(_BYTE)cDefender )
  {
    Skill::CFunctions::ConsumeMP(attackType, this);
    v29 = attackType;
  }
  this->CalculateEquipDurability(this, (unsigned __int16)v29);
  LOWORD(v30) = this->m_CreatureStatus.m_nNowHP;
  *(_DWORD *)&szBuffer[12] = this->m_dwCID;
  *(_WORD *)&szBuffer[22] = this->m_CreatureStatus.m_nNowMP;
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  *(AtType *)&szBuffer[16] = attackType;
  *(_WORD *)&szBuffer[20] = v30;
  szBuffer[24] = cOffencerJudge;
  szBuffer[25] = cDefender;
  if ( !m_lpGameClientDispatch )
    return 0;
  LOWORD(v30) = (unsigned __int8)cDefender;
  if ( CSendStream::WrapCompress(
         &m_lpGameClientDispatch->m_SendStream,
         szBuffer,
         (char *)(15 * v30 + 26),
         0xEu,
         0,
         wError) != 1 )
    return 0;
  if ( (_WORD)wError )
    return 0;
  m_lpCell = this->m_CellPos.m_lpCell;
  if ( !m_lpCell )
    return 0;
  CCell::SendAttackInfo(m_lpCell, this->m_dwCID, &attackType, cDefender, (DefenserNode *)&szBuffer[26]);
  return 1;
}
// 41D943: variable 'v30' is possibly undefined

//----- (0041D9B0) --------------------------------------------------------
char __thiscall CCharacter::AttackUsingBow(CCharacter *this, int wType)
{
  Item::CItem *v3; // esi
  Item::CEquipmentsContainer *p_m_Equipments; // ebx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned __int8 m_cItemType; // al

  v3 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand];
  p_m_Equipments = &this->m_Equipments;
  if ( v3 )
  {
    if ( (unsigned __int16)wType < 3u || (unsigned __int16)wType > 4u )
    {
      m_cItemType = v3->m_ItemInfo->m_DetailData.m_cItemType;
      if ( m_cItemType == 45 || m_cItemType == 46 )
        return 0;
    }
    else
    {
      if ( (_WORD)wType == 3 )
      {
        if ( v3->m_ItemInfo->m_DetailData.m_cItemType != 45 )
          return 0;
      }
      else if ( (_WORD)wType == 4 && v3->m_ItemInfo->m_DetailData.m_cItemType != 46 )
      {
        return 0;
      }
      if ( !CCharacter::UseAmmo(this, (Item::CUseItem *)v3) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::AttackUsingBow",
          aDWorkRylSource_41,
          781,
          aCid0x08x_248,
          this->m_dwCID);
        return 0;
      }
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharEquipDurability(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          *(_WORD *)&v3->m_ItemData.m_ItemPos,
          v3->m_ItemData.m_cNumOrDurability,
          0);
    }
    if ( !v3->m_ItemData.m_cNumOrDurability && (v3->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
    {
      CCharacter::CalculateStatusData(this, 0);
      LOWORD(wType) = v3->m_ItemData.m_ItemPos;
      if ( !((unsigned __int8 (__thiscall *)(Item::CEquipmentsContainer *, int))p_m_Equipments->RemoveItem)(
              &this->m_Equipments,
              wType) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::AttackUsingBow",
          aDWorkRylSource_41,
          809,
          aCid0x08x_98,
          this->m_dwCID);
    }
  }
  return 1;
}

//----- (0041DB00) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CInvincibleSpell>::operator()(
        Skill::CAddSpell<CInvincibleSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x20000000u);
      v7->__vftable = (CSpell_vtbl *)&CInvincibleSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4D8E20: using guessed type void *CInvincibleSpell::`vftable';

//----- (0041DC30) --------------------------------------------------------
char __thiscall CCharacter::Respawn(CCharacter *this, Position Pos, bool bDropExp)
{
  CCell *m_lpCell; // ecx
  CellPosition *p_m_CellPos; // ebx
  float fPointY; // edx
  float fPointZ; // eax
  Position *p_m_CurrentPos; // edi
  float v9; // eax
  float m_fPointZ; // edx
  CServerSetup *Instance; // eax
  float m_fPointY; // edx
  float v13; // ecx
  VirtualArea::CVirtualAreaMgr *v14; // eax
  VirtualArea::CBGServerMap *VirtualArea; // ebx
  unsigned __int8 MaxRespawnPos; // al
  unsigned int v17; // eax
  float v18; // edx
  float v19; // eax
  float v20; // eax
  POS *p_SavePoint; // ecx
  float v22; // edx
  POS *DefaultCharacterPos; // eax
  float fPointX; // ecx
  float v25; // edx
  float v26; // ebp
  float v27; // edx
  float v28; // eax
  unsigned int m_nExp; // eax
  float v30; // ecx
  unsigned int v31; // ebx
  __int64 v32; // kr08_8
  int m_nExp_high; // edx
  unsigned __int16 m_nMaxMP; // cx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // ebp
  char *Buffer; // eax
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-34h]
  Position result; // [esp+10h] [ebp-20h] BYREF
  int v41; // [esp+1Ch] [ebp-14h]
  Skill::CAddSpell<CInvincibleSpell> v42; // [esp+20h] [ebp-10h] BYREF

  m_lpCell = this->m_CellPos.m_lpCell;
  p_m_CellPos = &this->m_CellPos;
  if ( m_lpCell )
    CCell::DeleteCreature(
      m_lpCell,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)2);
  if ( 0.0 == Pos.m_fPointX && 0.0 == Pos.m_fPointY && 0.0 == Pos.m_fPointZ )
  {
    fPointY = this->m_DBData.m_Pos.SavePoint.fPointY;
    fPointZ = this->m_DBData.m_Pos.SavePoint.fPointZ;
    Pos.m_fPointX = this->m_DBData.m_Pos.SavePoint.fPointX;
    Pos.m_fPointY = fPointY;
    p_m_CurrentPos = &this->m_CurrentPos;
    Pos.m_fPointZ = fPointZ;
    v9 = fPointY;
    this->m_CurrentPos.m_fPointX = Pos.m_fPointX;
    m_fPointZ = Pos.m_fPointZ;
    this->m_CurrentPos.m_fPointY = v9;
    this->m_CurrentPos.m_fPointZ = m_fPointZ;
    Instance = CServerSetup::GetInstance();
    if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) != 11 )
    {
      LODWORD(Pos.m_fPointX) = Math::Random::ComplexRandom(41, 0);
      p_m_CurrentPos->m_fPointX = (double)LODWORD(Pos.m_fPointX) + p_m_CurrentPos->m_fPointX - 20.0;
      LODWORD(Pos.m_fPointX) = Math::Random::ComplexRandom(41, 0);
      this->m_CurrentPos.m_fPointZ = (double)LODWORD(Pos.m_fPointX) + this->m_CurrentPos.m_fPointZ - 20.0;
    }
  }
  else
  {
    m_fPointY = Pos.m_fPointY;
    p_m_CurrentPos = &this->m_CurrentPos;
    this->m_CurrentPos.m_fPointX = Pos.m_fPointX;
    v13 = Pos.m_fPointZ;
    this->m_CurrentPos.m_fPointY = m_fPointY;
    this->m_CurrentPos.m_fPointZ = v13;
  }
  CellPosition::MoveTo(&this->m_CellPos, p_m_CurrentPos);
  if ( !p_m_CellPos->m_lpCell )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Respawn",
      aDWorkRylSource_41,
      624,
      aCid0x08x_274,
      this->m_dwCID);
    if ( this->m_CellPos.m_wMapIndex )
    {
      m_wMapIndex = this->m_CellPos.m_wMapIndex;
      v14 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v14, m_wMapIndex);
      if ( !VirtualArea )
      {
LABEL_15:
        p_m_CellPos = &this->m_CellPos;
        CellPosition::MoveTo(&this->m_CellPos, p_m_CurrentPos);
        goto LABEL_16;
      }
      LOBYTE(Pos.m_fPointX) = this->m_DBData.m_Info.Nationality;
      MaxRespawnPos = VirtualArea::CVirtualArea::GetMaxRespawnPos(VirtualArea);
      v17 = Math::Random::ComplexRandom(MaxRespawnPos, 0);
      *p_m_CurrentPos = *VirtualArea::CVirtualArea::GetRespawnPosition(VirtualArea, &result, LOBYTE(Pos.m_fPointX), v17);
      v18 = this->m_CurrentPos.m_fPointY;
      v19 = this->m_CurrentPos.m_fPointZ;
      this->m_DBData.m_Pos.LastPoint.fPointX = p_m_CurrentPos->m_fPointX;
      this->m_DBData.m_Pos.LastPoint.fPointY = v18;
      this->m_DBData.m_Pos.LastPoint.fPointZ = v19;
      v20 = this->m_DBData.m_Pos.LastPoint.fPointY;
      p_SavePoint = &this->m_DBData.m_Pos.SavePoint;
      this->m_DBData.m_Pos.SavePoint.fPointX = this->m_DBData.m_Pos.LastPoint.fPointX;
      v22 = this->m_DBData.m_Pos.LastPoint.fPointZ;
    }
    else
    {
      DefaultCharacterPos = CharCreate::GetDefaultCharacterPos(
                              (POS *)&Pos,
                              (unsigned __int8)this->m_DBData.m_Info.Nationality,
                              0);
      fPointX = DefaultCharacterPos->fPointX;
      v25 = DefaultCharacterPos->fPointY;
      v26 = DefaultCharacterPos->fPointZ;
      this->m_DBData.m_Pos.LastPoint.fPointX = DefaultCharacterPos->fPointX;
      this->m_DBData.m_Pos.LastPoint.fPointY = v25;
      this->m_DBData.m_Pos.LastPoint.fPointZ = v26;
      this->m_DBData.m_Pos.SavePoint.fPointX = fPointX;
      this->m_DBData.m_Pos.SavePoint.fPointY = v25;
      this->m_DBData.m_Pos.SavePoint.fPointZ = v26;
      v27 = this->m_DBData.m_Pos.SavePoint.fPointY;
      v28 = this->m_DBData.m_Pos.SavePoint.fPointZ;
      Pos.m_fPointX = this->m_DBData.m_Pos.SavePoint.fPointX;
      Pos.m_fPointY = v27;
      p_SavePoint = (POS *)p_m_CurrentPos;
      Pos.m_fPointZ = v28;
      v20 = v27;
      p_m_CurrentPos->m_fPointX = Pos.m_fPointX;
      v22 = Pos.m_fPointZ;
    }
    p_SavePoint->fPointY = v20;
    p_SavePoint->fPointZ = v22;
    goto LABEL_15;
  }
LABEL_16:
  CCell::SetCreature(p_m_CellPos->m_lpCell, this->m_dwCID, this, RESPAWN);
  m_nExp = this->m_CreatureStatus.m_nExp;
  v30 = *((float *)&this->m_CreatureStatus.m_nExp + 1);
  v31 = m_nExp;
  Pos.m_fPointY = v30;
  if ( bDropExp )
  {
    m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
    this->m_CreatureStatus.m_nNowHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    this->m_CreatureStatus.m_nNowMP = m_nMaxMP;
    if ( !CServerSetup::GetInstance()->m_bBattleGame )
    {
      LODWORD(result.m_fPointZ) = 1966110;
      v42.m_Spell_Info.m_SkillProtoType = &Skill::CProcessTable::ProcessInfo::m_NullProtoType;
      LOWORD(v41) = 1;
      BYTE2(v41) = 1;
      v42.m_Spell_Info.m_lpCaster = this;
      *(_DWORD *)&v42.m_Spell_Info.m_wDurationSec = 1966110;
      *(_DWORD *)&v42.m_Spell_Info.m_wSpellLevel = v41;
      Skill::CAddSpell<CInvincibleSpell>::operator()(&v42, this);
    }
  }
  else
  {
    this->m_CreatureStatus.m_nNowHP = -1;
    this->m_CreatureStatus.m_nNowMP = -1;
    v32 = __PAIR64__(LODWORD(v30), m_nExp) + this->m_FightInfo.m_nRestoreExp;
    this->m_CreatureStatus.m_nExp = v32;
    m_nExp_high = HIDWORD(this->m_CreatureStatus.m_nExp);
    LODWORD(this->m_FightInfo.m_nRestoreExp) = 0;
    HIDWORD(this->m_FightInfo.m_nRestoreExp) = 0;
    LODWORD(this->m_DBData.m_Info.Exp) = v32;
    HIDWORD(this->m_DBData.m_Info.Exp) = m_nExp_high;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
    Buffer = CSendStream::GetBuffer(&m_lpGameClientDispatch->m_SendStream, (char *)0x2C);
    if ( Buffer )
    {
      *((_DWORD *)Buffer + 9) = this->m_dwCID;
      *((_DWORD *)Buffer + 3) = LODWORD(p_m_CurrentPos->m_fPointX);
      *((_DWORD *)Buffer + 4) = LODWORD(this->m_CurrentPos.m_fPointY);
      *((_DWORD *)Buffer + 5) = LODWORD(this->m_CurrentPos.m_fPointZ);
      *((_WORD *)Buffer + 20) = this->m_CreatureStatus.m_nNowHP;
      *((_WORD *)Buffer + 21) = this->m_CreatureStatus.m_nNowMP;
      *((_DWORD *)Buffer + 6) = this->m_CreatureStatus.m_nExp;
      *((_DWORD *)Buffer + 7) = HIDWORD(this->m_CreatureStatus.m_nExp);
      *((_DWORD *)Buffer + 8) = this->m_DBData.m_Info.Gold;
      CSendStream::WrapCrypt(p_m_SendStream, 0x2Cu, 0xAu, 0, 0);
    }
  }
  GAMELOG::LogCharRespawn(this, __PAIR64__(LODWORD(Pos.m_fPointY), v31), this->m_CreatureStatus.m_nExp, 0);
  CCastingSpell::EnableChant(&this->m_SpellMgr.m_CastingInfo, 0);
  return 1;
}

//----- (0041E030) --------------------------------------------------------
char __thiscall CCharacter::RegenHPAndMP(
        CCharacter *this,
        signed __int16 usAdditionalHP,
        signed __int16 usAdditionalMP,
        bool bAddDefaultRegenValue)
{
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  CAggresiveCreature::RegenHPAndMP(this, usAdditionalHP, usAdditionalMP, bAddDefaultRegenValue);
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharHPRegen(
             &m_lpGameClientDispatch->m_SendStream,
             this->m_dwCID,
             this->m_CreatureStatus.m_nNowHP,
             this->m_CreatureStatus.m_nNowMP);
  else
    return 1;
}

//----- (0041E090) --------------------------------------------------------
char __thiscall CCharacter::UpdateMasteryInfo(
        CCharacter *this,
        unsigned __int8 cMasteryType,
        unsigned __int8 cPassiveType)
{
  char v3; // dl
  char v4; // bl
  int v6; // eax

  if ( cPassiveType == 1 )
  {
    v3 = this->m_cUsingMastery[0];
    if ( v3 != cMasteryType )
    {
      v4 = this->m_cUsingMastery[1];
      if ( v4 != cMasteryType )
      {
        if ( !v3 )
        {
          this->m_cUsingMastery[0] = cMasteryType;
          return 1;
        }
        if ( !v4 )
        {
          this->m_cUsingMastery[1] = cMasteryType;
          return 1;
        }
      }
    }
    return 0;
  }
  if ( cPassiveType != 2 )
    return 0;
  v6 = 0;
  while ( this->m_cUsingMastery[v6] != cMasteryType )
  {
    if ( ++v6 >= 2 )
      return 0;
  }
  this->m_cUsingMastery[v6] = 0;
  return 1;
}

//----- (0041E120) --------------------------------------------------------
bool __thiscall CCharacter::CheckEquipable(CCharacter *this, Item::CItem *lpItem)
{
  bool result; // al
  const Item::ItemInfo *m_ItemInfo; // eax

  result = lpItem
        && lpItem->m_ItemData.m_cNumOrDurability
        && (m_ItemInfo = lpItem->m_ItemInfo, this->m_DBData.m_Info.STR >= m_ItemInfo->m_UseLimit.m_nStatusLimit[2])
        && this->m_DBData.m_Info.DEX >= m_ItemInfo->m_UseLimit.m_nStatusLimit[3]
        && this->m_DBData.m_Info.CON >= m_ItemInfo->m_UseLimit.m_nStatusLimit[4]
        && this->m_DBData.m_Info.INT >= m_ItemInfo->m_UseLimit.m_nStatusLimit[5]
        && this->m_DBData.m_Info.WIS >= m_ItemInfo->m_UseLimit.m_nStatusLimit[6]
        && ((1 << (this->m_DBData.m_Info.Class - 1)) & m_ItemInfo->m_UseLimit.m_dwClassLimit) != 0;
  return result;
}

//----- (0041E190) --------------------------------------------------------
void __thiscall CCharacter::DegradeRespawnSpeedByEmblem(CCharacter *this)
{
  this->m_dwRespawnSpeed = 100;
}

//----- (0041E1A0) --------------------------------------------------------
bool __thiscall CCharacter::CalculateStatus(CCharacter *this)
{
  int m_nDEX; // ecx
  __int16 m_nCON; // dx
  __int16 v4; // di
  __int16 m_nLevel; // ax
  __int16 *p_m_nWIS; // edi
  __int16 *p_m_nINT; // ebx
  __int16 v8; // dx
  int v9; // eax
  int v10; // kr8C_4
  int v11; // eax
  bool v12; // cc
  int v13; // edx
  __int16 v14; // di
  __int16 v15; // bp
  int v16; // kr08_4
  int v17; // eax
  int *v18; // eax
  __int16 v19; // ax
  int m_nWIS; // ebp
  __int16 v21; // dx
  unsigned __int16 v22; // dx
  __int16 v23; // dx
  __int16 v24; // di
  __int16 v25; // ax
  __int16 v26; // dx
  int v27; // eax
  int v28; // ebp
  int v29; // eax
  int *v30; // edx
  unsigned __int16 v31; // ax
  int v32; // ebp
  __int16 v33; // di
  int v34; // kr18_4
  int *v35; // edx
  int v36; // ebx
  __int16 v37; // di
  __int16 v38; // bp
  int v39; // kr24_4
  int v40; // eax
  int v41; // edx
  __int16 v42; // di
  int v43; // kr2C_4
  int v44; // eax
  int *v45; // eax
  __int16 v46; // dx
  int m_nINT; // eax
  int v48; // edx
  int v49; // ebp
  int v50; // eax
  int v51; // ebp
  __int16 v52; // dx
  int v53; // eax
  int v54; // edx
  int v55; // ebp
  int v56; // eax
  __int16 v57; // di
  __int16 v58; // dx
  unsigned __int16 v59; // dx
  int v60; // ebx
  __int16 v61; // di
  int v62; // kr3C_4
  int v63; // eax
  __int16 v64; // dx
  int v65; // eax
  __int64 v66; // rax
  int *v67; // edx
  __int16 v68; // di
  __int16 v69; // ax
  __int16 v70; // dx
  __int16 v71; // dx
  int v72; // eax
  int *v73; // edx
  int v74; // ebp
  int v75; // ebp
  __int16 v76; // di
  int v77; // kr60_4
  int v78; // eax
  int v79; // ebx
  int v80; // edx
  __int16 v81; // bp
  int v82; // eax
  int v83; // ebx
  int v84; // ebp
  __int16 v85; // di
  int v86; // kr70_4
  Item::CItem *v87; // eax
  unsigned __int8 m_cItemType; // al
  int m_nSTR; // kr7C_4
  __int16 v90; // ax
  __int16 v91; // cx
  __int16 *v92; // eax
  int v93; // ebp
  int v94; // edi
  int v95; // ecx
  int v96; // edx
  __int16 *m_nAttributeResistance; // esi
  int v98; // eax
  bool result; // al
  int v100; // [esp+10h] [ebp-8h] BYREF
  int v101; // [esp+14h] [ebp-4h] BYREF

  switch ( this->m_DBData.m_Info.Class )
  {
    case 1u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      m_nCON = this->m_CharacterStatus.m_nCON;
      v4 = this->m_CharacterStatus.m_nSTR / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v4 + this->m_CharacterStatus.m_nDEX / 5;
      m_nLevel = this->m_CreatureStatus.m_nLevel;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v4;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v8 = m_nLevel * (m_nCON + 20);
      v9 = this->m_CharacterStatus.m_nWIS - 20;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v8 + 300;
      v10 = this->m_CreatureStatus.m_nLevel * v9;
      v11 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v100 = v10 / 2;
      v12 = v11 < v10 / 2;
      goto LABEL_39;
    case 2u:
    case 0x12u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v60 = this->m_CreatureStatus.m_nLevel;
      v61 = this->m_CharacterStatus.m_nDEX / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v61 + this->m_CharacterStatus.m_nDEX / 5;
      v62 = v60 * (this->m_CharacterStatus.m_nCON - 20);
      v15 = 40 * v60;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v61;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v63 = v60 * (this->m_CharacterStatus.m_nWIS - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v62 / 2 + 40 * v60 + 300;
      v100 = v63 / 2;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v101 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v12 = v101 < v63 / 2;
      v18 = &v100;
      if ( !v12 )
        v18 = &v101;
      goto LABEL_20;
    case 3u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v13 = this->m_CreatureStatus.m_nLevel;
      v14 = this->m_CharacterStatus.m_nINT / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v14 + this->m_CharacterStatus.m_nDEX / 5;
      v15 = 40 * v13;
      v16 = v13 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v14;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v17 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nWIS - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v16 / 2 + 40 * v13 + 300;
      v100 = v17 / 2;
      v101 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20);
      v12 = v101 < v17 / 2;
      v18 = &v100;
      if ( v12 )
LABEL_20:
        v19 = v15 + *(_WORD *)v18;
      else
        v19 = v15 + v101;
      goto LABEL_42;
    case 4u:
      m_nWIS = this->m_CharacterStatus.m_nWIS;
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = this->m_CharacterStatus.m_nWIS / 2;
      v21 = this->m_CharacterStatus.m_nCON;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = (__int16)m_nDEX / 5;
      v22 = LOWORD(this->m_CreatureStatus.m_nLevel) * (v21 + 20) + 300;
      goto LABEL_15;
    case 5u:
    case 0x13u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v57 = this->m_CharacterStatus.m_nSTR / 4;
      v64 = this->m_CharacterStatus.m_nCON;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v57 + this->m_CharacterStatus.m_nDEX / 5;
      v59 = LOWORD(this->m_CreatureStatus.m_nLevel) * (v64 + 20) + 800;
      goto LABEL_22;
    case 6u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v23 = this->m_CharacterStatus.m_nCON;
      v24 = this->m_CharacterStatus.m_nSTR / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v24 + this->m_CharacterStatus.m_nDEX / 5;
      v25 = this->m_CreatureStatus.m_nLevel;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v24;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v26 = v25 * (v23 + 20);
      v27 = this->m_CharacterStatus.m_nWIS - 20;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v26 + 550;
      v28 = this->m_CreatureStatus.m_nLevel * v27 / 2;
      v29 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v101 = v28;
      v100 = v29;
      v30 = &v101;
      if ( v29 >= v28 )
        v30 = &v100;
      v31 = *(_WORD *)v30 + 40 * LOWORD(this->m_CreatureStatus.m_nLevel) + 550;
      goto LABEL_43;
    case 7u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v32 = this->m_CreatureStatus.m_nLevel;
      v33 = this->m_CharacterStatus.m_nDEX / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v33 + this->m_CharacterStatus.m_nDEX / 5;
      v34 = v32 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v33;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v34 / 2 + 40 * v32 + 300;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v100 = v32 * (this->m_CharacterStatus.m_nWIS - 20) / 2;
      v101 = v32 * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v35 = &v100;
      if ( v101 >= v100 )
        v35 = &v101;
      goto LABEL_33;
    case 8u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v36 = this->m_CreatureStatus.m_nLevel;
      v37 = this->m_CharacterStatus.m_nDEX / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v37 + this->m_CharacterStatus.m_nDEX / 5;
      v38 = 40 * v36;
      v39 = v36 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v37;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v40 = v36 * (this->m_CharacterStatus.m_nWIS - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v39 / 2 + 40 * v36 + 550;
      v100 = v40 / 2;
      goto LABEL_35;
    case 9u:
    case 0x16u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v75 = this->m_CreatureStatus.m_nLevel;
      v76 = this->m_CharacterStatus.m_nINT / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v76 + this->m_CharacterStatus.m_nDEX / 5;
      v77 = v75 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v76;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v77 / 2 + 40 * v75 + 300;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v78 = v75 * (this->m_CharacterStatus.m_nWIS - 20) / 2;
      v100 = v75 * (this->m_CharacterStatus.m_nINT - 20);
      v101 = v78;
      v35 = &v101;
      if ( v100 >= v78 )
        v35 = &v100;
      goto LABEL_33;
    case 0xAu:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v41 = this->m_CreatureStatus.m_nLevel;
      v42 = this->m_CharacterStatus.m_nINT / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v42 + this->m_CharacterStatus.m_nDEX / 5;
      v38 = 40 * v41;
      v43 = v41 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v42;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v44 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nWIS - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v43 / 2 + 40 * v41 + 550;
      v100 = v44 / 2;
      v101 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20);
      v12 = v101 < v44 / 2;
      v45 = &v100;
      if ( v12 )
        goto LABEL_37;
      v31 = v38 + v101 + 550;
      goto LABEL_43;
    case 0xBu:
      m_nWIS = this->m_CharacterStatus.m_nWIS;
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = this->m_CharacterStatus.m_nWIS / 2;
      v46 = this->m_CharacterStatus.m_nCON;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = (__int16)m_nDEX / 5;
      v22 = LOWORD(this->m_CreatureStatus.m_nLevel) * (v46 + 20) + 800;
LABEL_15:
      m_nINT = this->m_CharacterStatus.m_nINT;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v22;
      v48 = this->m_CreatureStatus.m_nLevel;
      v49 = v48 * (m_nWIS - 20);
      v50 = v48 * (m_nINT - 20);
      goto LABEL_23;
    case 0xCu:
      v51 = this->m_CharacterStatus.m_nWIS;
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = this->m_CharacterStatus.m_nWIS / 2;
      v52 = this->m_CharacterStatus.m_nCON;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = (__int16)m_nDEX / 5;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v53 = this->m_CharacterStatus.m_nINT;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = LOWORD(this->m_CreatureStatus.m_nLevel) * (v52 + 20) + 550;
      v54 = this->m_CreatureStatus.m_nLevel;
      v55 = v54 * (v51 - 20);
      v56 = v54 * (v53 - 20);
      goto LABEL_26;
    case 0x11u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v57 = this->m_CharacterStatus.m_nSTR / 4;
      v58 = this->m_CharacterStatus.m_nCON;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v57 + this->m_CharacterStatus.m_nDEX / 5;
      v59 = LOWORD(this->m_CreatureStatus.m_nLevel) * (v58 + 20) + 300;
LABEL_22:
      v65 = this->m_CharacterStatus.m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v57;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v59;
      v66 = this->m_CreatureStatus.m_nLevel * (v65 - 20);
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v49 = ((int)v66 - HIDWORD(v66)) >> 1;
      v50 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20);
LABEL_23:
      v101 = v49;
      v100 = v50 / 2;
      v67 = &v101;
      if ( v50 / 2 >= v49 )
        v67 = &v100;
      goto LABEL_41;
    case 0x14u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v68 = this->m_CharacterStatus.m_nSTR / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v68 + this->m_CharacterStatus.m_nDEX / 5;
      v69 = this->m_CreatureStatus.m_nLevel;
      v70 = this->m_CharacterStatus.m_nCON + 20;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v68;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v71 = v69 * v70;
      v72 = this->m_CharacterStatus.m_nWIS - 20;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v71 + 550;
      v55 = this->m_CreatureStatus.m_nLevel * v72 / 2;
      v56 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20);
LABEL_26:
      v101 = v55;
      v100 = v56 / 2;
      v73 = &v101;
      if ( v56 / 2 >= v55 )
        v73 = &v100;
      v31 = *(_WORD *)v73 + 40 * LOWORD(this->m_CreatureStatus.m_nLevel) + 550;
      goto LABEL_43;
    case 0x15u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = this->m_CharacterStatus.m_nDEX / 2;
      v74 = this->m_CreatureStatus.m_nLevel;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = (__int16)m_nDEX / 5;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v74 * (this->m_CharacterStatus.m_nCON - 20) / 2 + 40 * v74 + 300;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v100 = v74 * (this->m_CharacterStatus.m_nWIS - 20) / 2;
      v101 = v74 * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v35 = &v100;
      if ( v101 >= v100 )
        v35 = &v101;
LABEL_33:
      v31 = *(_WORD *)v35 + 8 * (5 * LOWORD(this->m_CreatureStatus.m_nLevel) + 100);
      goto LABEL_43;
    case 0x17u:
      v79 = this->m_CharacterStatus.m_nWIS;
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v80 = this->m_CreatureStatus.m_nLevel;
      v81 = this->m_CharacterStatus.m_nWIS / 2;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v81 + this->m_CharacterStatus.m_nDEX / 5;
      v82 = v80 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v81;
      v38 = 40 * v80;
      v83 = this->m_CreatureStatus.m_nLevel * (v79 - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v82 / 2 + 40 * v80 + 550;
      v100 = v83;
LABEL_35:
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      v101 = this->m_CreatureStatus.m_nLevel * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v45 = &v100;
      if ( v101 >= v100 )
        v45 = &v101;
LABEL_37:
      v31 = v38 + *(_WORD *)v45 + 550;
      goto LABEL_43;
    case 0x18u:
      m_nDEX = this->m_CharacterStatus.m_nDEX;
      v84 = this->m_CreatureStatus.m_nLevel;
      v85 = this->m_CharacterStatus.m_nDEX / 4;
      this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision = v85 + this->m_CharacterStatus.m_nDEX / 5;
      v86 = v84 * (this->m_CharacterStatus.m_nCON - 20);
      this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision = v85;
      p_m_nINT = &this->m_CharacterStatus.m_nINT;
      this->m_CreatureStatus.m_StatusInfo.m_nMaxHP = v86 / 2 + 8 * (5 * v84 + 100);
      p_m_nWIS = &this->m_CharacterStatus.m_nWIS;
      v100 = v84 * (this->m_CharacterStatus.m_nWIS - 20) / 2;
      v11 = v84 * (this->m_CharacterStatus.m_nINT - 20) / 2;
      v12 = v11 < v100;
LABEL_39:
      v101 = v11;
      v67 = &v100;
      if ( !v12 )
        v67 = &v101;
LABEL_41:
      v19 = *(_WORD *)v67 + 40 * LOWORD(this->m_CreatureStatus.m_nLevel);
LABEL_42:
      v31 = v19 + 300;
LABEL_43:
      this->m_CreatureStatus.m_StatusInfo.m_nMaxMP = v31;
      v87 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
      if ( v87 && ((m_cItemType = v87->m_ItemInfo->m_DetailData.m_cItemType, m_cItemType == 12) || m_cItemType == 13) )
      {
        this->m_CreatureStatus.m_StatusInfo.m_nMinDamage = m_nDEX / 2;
        this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage = m_nDEX / 2;
      }
      else
      {
        m_nSTR = this->m_CharacterStatus.m_nSTR;
        this->m_CreatureStatus.m_StatusInfo.m_nMinDamage = this->m_CharacterStatus.m_nSTR / 2;
        this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage = m_nSTR / 2;
        if ( CClass::GetPreviousJob(this->m_DBData.m_Info.Class) == 2 || this->m_DBData.m_Info.Class == 24 )
        {
          v90 = this->m_CharacterStatus.m_nDEX / 6;
          this->m_CreatureStatus.m_StatusInfo.m_nMinDamage += v90;
          this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += v90;
        }
      }
      v91 = this->m_CharacterStatus.m_nDEX / 5;
      this->m_CreatureStatus.m_StatusInfo.m_nDefence = 0;
      this->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage = 0;
      this->m_CreatureStatus.m_StatusInfo.m_fDRC = 0.0;
      this->m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage = v91;
      this->m_CreatureStatus.m_StatusInfo.m_nCriticalType = 0;
      v92 = p_m_nWIS;
      if ( *p_m_nINT >= *p_m_nWIS )
        v92 = p_m_nINT;
      v93 = this->m_CreatureStatus.m_nLevel;
      v94 = *p_m_nWIS;
      this->m_CreatureStatus.m_StatusInfo.m_nMagicPower = v93 / 2 + (*v92 - 20) / 2;
      this->m_CreatureStatus.m_StatusInfo.m_nMagicResistance = v93 / 4 + (v94 - 20) / 2;
      v95 = v93 * (this->m_CharacterStatus.m_nCON + 20);
      this->m_CreatureStatus.m_StatusInfo.m_nAttackSpeed = 0;
      this->m_CreatureStatus.m_StatusInfo.m_nMoveSpeed = 0;
      this->m_CreatureStatus.m_StatusInfo.m_nAttackRange = 0;
      this->m_CreatureStatus.m_StatusInfo.m_nRangeAttackDistance = 0;
      v96 = (unsigned __int64)(1374389535LL * (v95 + 300)) >> 32;
      LOWORD(v95) = v93 * (v94 + *p_m_nINT);
      this->m_CreatureStatus.m_StatusInfo.m_nHPRegenAmount = (v96 >> 4) + ((unsigned int)v96 >> 31);
      this->m_CreatureStatus.m_StatusInfo.m_nMPRegenAmount = (__int16)(v95 + 300) / 50;
      m_nAttributeResistance = this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance;
      v98 = 5;
      do
      {
        *(m_nAttributeResistance - 5) = 0;
        *m_nAttributeResistance++ = 0;
        --v98;
      }
      while ( v98 );
      result = 1;
      break;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::CalculateStatus",
        aDWorkRylSource_52,
        303,
        (char *)&byte_4D9828);
      result = 0;
      break;
  }
  return result;
}

//----- (0041EE00) --------------------------------------------------------
__int16 __thiscall CCharacter::EquipSkillArm(CCharacter *this, Item::EquipType::Type eEquipType)
{
  int m_cRightHand; // eax
  Item::CItem **m_lppItems; // ecx
  int m_cLeftHand; // edx
  Item::CEquipment *v6; // edi
  Item::CEquipment *v7; // ebp
  const StatusInfo *StatusInfo; // eax
  const StatusInfo *v10; // eax
  const StatusInfo *v11; // eax
  const StatusInfo *v12; // eax
  int v13; // eax
  int v14; // edx
  StatusInfo result; // [esp+Ch] [ebp-40h] BYREF

  m_cRightHand = this->m_Equipments.m_cRightHand;
  m_lppItems = this->m_Equipments.m_lppItems;
  m_cLeftHand = this->m_Equipments.m_cLeftHand;
  v6 = (Item::CEquipment *)m_lppItems[m_cRightHand];
  v7 = (Item::CEquipment *)m_lppItems[m_cLeftHand];
  if ( !v7 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::EquipSkillArm",
      aDWorkRylSource_52,
      912,
      aCid0x08x_269,
      this->m_dwCID);
    return 0;
  }
  if ( !CCharacter::CheckEquipable(this, m_lppItems[m_cLeftHand]) )
    return 0;
  if ( eEquipType == GuardArm )
    return v7->m_usAttribute[10];
  if ( v6 && v6->m_ItemData.m_cNumOrDurability )
  {
    if ( eEquipType == Attach )
    {
      StatusInfo = Item::CEquipment::GetStatusInfo(v6, &result, 1);
      StatusInfo::operator-=(&this->m_CreatureStatus.m_StatusInfo, StatusInfo);
      CCharacter::CalculatePassiveSkill(
        this,
        (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
        Detach);
      goto LABEL_13;
    }
    v10 = Item::CEquipment::GetStatusInfo(v6, &result, 1);
    StatusInfo::operator+=(&this->m_CreatureStatus.m_StatusInfo, v10);
    CCharacter::CalculatePassiveSkill(
      this,
      (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
      Attach);
  }
  if ( eEquipType )
  {
    v12 = Item::CEquipment::GetStatusInfo(v7, &result, 1);
    StatusInfo::operator-=(&this->m_CreatureStatus.m_StatusInfo, v12);
    goto LABEL_15;
  }
LABEL_13:
  v11 = Item::CEquipment::GetStatusInfo(v7, &result, 1);
  StatusInfo::operator+=(&this->m_CreatureStatus.m_StatusInfo, v11);
LABEL_15:
  if ( v7->m_ItemInfo->m_DetailData.m_cItemType == 43 )
  {
    v13 = (2 * (eEquipType == Attach) - 1) * (this->m_CharacterStatus.m_nDEX / 6);
    this->m_CreatureStatus.m_StatusInfo.m_nMinDamage += v13;
    this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += v13;
  }
  if ( v7->m_ItemInfo->m_DetailData.m_cItemType == 42 )
  {
    v14 = (2 * (eEquipType == Attach) - 1) * (this->m_CharacterStatus.m_nDEX / 2)
        - (2 * (eEquipType == Attach) - 1) * (this->m_CharacterStatus.m_nSTR / 3);
    this->m_CreatureStatus.m_StatusInfo.m_nMinDamage += v14;
    this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += v14;
  }
  return 1;
}

//----- (0041EFC0) --------------------------------------------------------
void __thiscall CCharacter::UpgradeRespawnSpeedByEmblem(
        CCharacter *this,
        unsigned __int8 cUpgradeType,
        unsigned __int8 cUpgradeStep)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax

  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                       CSingleton<CMonsterMgr>::ms_pSingleton,
                       cUpgradeStep + 6 * cUpgradeType + 5000);
  if ( MonsterProtoType )
    this->m_dwRespawnSpeed -= (unsigned __int64)((double)this->m_dwRespawnSpeed
                                               * MonsterProtoType->m_MonsterInfo.m_fRespawnSpeedUp);
}

//----- (0041F020) --------------------------------------------------------
char __thiscall CCharacter::CalculateStatusData(CCharacter *this, bool bFullHPandMP)
{
  CAffectedSpell *p_m_AffectedInfo; // ebx
  unsigned __int16 m_nMaxMP; // cx
  unsigned __int16 m_nMaxHP; // ax
  unsigned __int16 v7; // ax

  p_m_AffectedInfo = &this->m_SpellMgr.m_AffectedInfo;
  CAffectedSpell::DisableChant(&this->m_SpellMgr.m_AffectedInfo, 1u);
  CAffectedSpell::DisableEnchant(p_m_AffectedInfo, 1u);
  CharacterStatus::Init(&this->m_CharacterStatus, &this->m_DBData.m_Info);
  Item::CEquipmentsContainer::CalculateCharacterStatus(&this->m_Equipments, this);
  if ( !CCharacter::CalculateStatus(this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::CalculateStatusData",
      aDWorkRylSource_52,
      44,
      aCid0x08x_138,
      this->m_dwCID);
    return 0;
  }
  if ( !Item::CEquipmentsContainer::SetEquipmentsAttribute(&this->m_Equipments) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::CalculateStatusData",
      aDWorkRylSource_52,
      50,
      aCid0x08x_340,
      this->m_dwCID);
    return 0;
  }
  *(_WORD *)this->m_cUsingMastery = 0;
  CCharacter::CalculatePassiveSkill(
    this,
    (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand],
    (Item::CEquipment *)this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand],
    Attach);
  if ( bFullHPandMP )
  {
    m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
    this->m_CreatureStatus.m_nNowHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    this->m_CreatureStatus.m_nNowMP = m_nMaxMP;
  }
  CAffectedSpell::EnableChant(p_m_AffectedInfo, 1u);
  CAffectedSpell::EnableEnchant(p_m_AffectedInfo, 1u);
  m_nMaxHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  if ( this->m_CreatureStatus.m_nNowHP > m_nMaxHP )
    this->m_CreatureStatus.m_nNowHP = m_nMaxHP;
  v7 = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  if ( this->m_CreatureStatus.m_nNowMP > v7 )
    this->m_CreatureStatus.m_nNowMP = v7;
  return 1;
}

//----- (0041F140) --------------------------------------------------------
char __thiscall CCharacter::ChangeClass(CCharacter *this, unsigned __int8 cClassType)
{
  int v2; // edi
  int m_nExp; // eax
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int v9; // [esp-8h] [ebp-14h]

  v2 = 0;
  if ( cClassType >= 0x19u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::ChangeClass",
      aDWorkRylSource_52,
      376,
      aCid0x08x_165,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      cClassType);
    v2 = 1;
  }
  m_nExp = this->m_CreatureStatus.m_nExp;
  HIDWORD(this->m_DBData.m_Info.Exp) = HIDWORD(this->m_CreatureStatus.m_nExp);
  LODWORD(this->m_DBData.m_Info.Exp) = m_nExp;
  if ( !CClass::JobChange(&ClassTable[cClassType], v2, &this->m_DBData, cClassType) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::ChangeClass",
      aDWorkRylSource_52,
      384,
      aCid0x08x_165,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      cClassType);
    LOWORD(v2) = 1;
  }
  CreatureStatus::Init(&this->m_CreatureStatus, &this->m_DBData.m_Info);
  v9 = this->GetGID(this);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v9);
  if ( Guild )
    Guild::CGuild::UpdateMemberInfo(Guild, this->m_dwCID, cClassType, 1u);
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch
    && !GameClientSendPacket::SendCharClassUpgrade(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          &this->m_DBData,
          v2)
    || (_WORD)v2 )
  {
    return 0;
  }
  else
  {
    return CCharacter::CalculateStatusData(this, 1);
  }
}

//----- (0041F270) --------------------------------------------------------
char __thiscall CCharacter::InitLevel1Char(CCharacter *this, unsigned __int8 cClassType)
{
  int m_nExp_high; // edx
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  if ( cClassType >= 0x19u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::InitLevel1Char",
      aDWorkRylSource_52,
      417,
      aCid0x08x_165,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      cClassType);
    return 0;
  }
  m_nExp_high = HIDWORD(this->m_CreatureStatus.m_nExp);
  LODWORD(this->m_DBData.m_Info.Exp) = this->m_CreatureStatus.m_nExp;
  HIDWORD(this->m_DBData.m_Info.Exp) = m_nExp_high;
  if ( !CClass::InitializeClass(&ClassTable[cClassType], &this->m_DBData, cClassType) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::InitLevel1Char",
      aDWorkRylSource_52,
      425,
      aCid0x08x_76,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      cClassType);
    return 0;
  }
  CreatureStatus::Init(&this->m_CreatureStatus, &this->m_DBData.m_Info);
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch
    && !GameClientSendPacket::SendCharClassUpgrade(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          &this->m_DBData,
          0) )
  {
    return 0;
  }
  return CCharacter::CalculateStatusData(this, 1);
}

//----- (0041F370) --------------------------------------------------------
char __thiscall CCharacter::IncrementExp(CCharacter *this, unsigned int dwExp)
{
  int m_nLevel; // eax
  unsigned __int16 Class; // ax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  int m_nExp; // ecx
  int m_nExp_high; // eax
  bool v8; // cf
  int v9; // ecx
  unsigned int v10; // edx
  int v11; // eax
  const char *v12; // edi
  const char *v13; // ebx
  char v15; // bl
  int v16; // edx
  CClass *v17; // ecx
  unsigned __int16 m_nMaxHP; // ax
  CCharacter_vtbl *v19; // edx
  unsigned __int16 m_nMaxMP; // ax
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int AwardEquipment; // eax
  CGameClientDispatch *v24; // eax
  unsigned int v25; // [esp-Ch] [ebp-14h]

  m_nLevel = this->m_CreatureStatus.m_nLevel;
  if ( m_nLevel > 95 || m_nLevel <= 0 )
  {
    if ( m_nLevel == 96 )
      return 1;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::IncrementExp",
      aDWorkRylSource_52,
      530,
      aCid0x08x_224,
      this->m_dwCID,
      this->m_CreatureStatus.m_nLevel);
    return 0;
  }
  Class = this->m_DBData.m_Info.Class;
  if ( Class >= 0x19u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::IncrementExp",
      aDWorkRylSource_52,
      538,
      aCid0x08x_279,
      this->m_dwCID,
      Class);
    return 0;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    GameClientSendPacket::SendCharAward(&m_lpGameClientDispatch->m_SendStream, this->m_dwCID, dwExp);
  m_nExp = this->m_CreatureStatus.m_nExp;
  m_nExp_high = HIDWORD(this->m_CreatureStatus.m_nExp);
  v8 = __CFADD__(dwExp, m_nExp);
  LODWORD(this->m_CreatureStatus.m_nExp) = dwExp + m_nExp;
  v9 = this->m_CreatureStatus.m_nLevel;
  v10 = this->m_CreatureStatus.m_nExp;
  v11 = v8 + m_nExp_high;
  HIDWORD(this->m_CreatureStatus.m_nExp) = v11;
  v12 = (&szDelete_7)[2 * v9];
  v13 = (&szNoRemove_7)[2 * v9];
  if ( v11 <= (int)v12 && (v11 < (int)v12 || v10 < (unsigned int)v13) )
    return 1;
  if ( (__int64)(__PAIR64__(v11, v10) - __PAIR64__((unsigned int)v12, (unsigned int)v13)) >= __SPAIR64__(
                                                                                               HIDWORD(ExpTable_0[v9]),
                                                                                               ExpTable_0[v9]) )
    CServerLog::SimpleLog(&g_Log, 1, aCid0x08x_125, this->m_dwCID, v9);
  v15 = this->m_DBData.m_Info.Level + 1;
  v16 = this->m_CreatureStatus.m_nLevel + 1;
  v17 = &ClassTable[this->m_DBData.m_Info.Class];
  LODWORD(this->m_CreatureStatus.m_nExp) = 0;
  HIDWORD(this->m_CreatureStatus.m_nExp) = 0;
  LODWORD(this->m_DBData.m_Info.Exp) = 0;
  HIDWORD(this->m_DBData.m_Info.Exp) = 0;
  this->m_DBData.m_Info.Level = v15;
  this->m_CreatureStatus.m_nLevel = v16;
  CClass::LevelUp(v17, &this->m_DBData.m_Info);
  m_nMaxHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  v19 = this->__vftable;
  this->m_CreatureStatus.m_nNowHP = m_nMaxHP;
  this->m_DBData.m_Info.HP = m_nMaxHP;
  m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  this->m_CreatureStatus.m_nNowMP = m_nMaxMP;
  this->m_DBData.m_Info.MP = m_nMaxMP;
  v25 = v19->GetGID(this);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v25);
  if ( Guild )
    Guild::CGuild::UpdateMemberInfo(Guild, this->m_dwCID, this->m_CreatureStatus.m_nLevel, 0);
  GAMELOG::LogCharLevelUp(this, this->m_DBData.m_Info.IP, this->m_DBData.m_Info.Level, 0);
  if ( CServerSetup::GetInstance()->m_bLevelUpEvent && this->m_CellPos.m_lpCell )
  {
    AwardEquipment = AwardTable::CAward::GetAwardEquipment(
                       CSingleton<AwardTable::CAward>::ms_pSingleton,
                       this->m_DBData.m_Info.Level,
                       this->m_DBData.m_Info.Nationality,
                       this->m_DBData.m_Info.Class);
    CCell::SetItem(
      this->m_CellPos.m_lpCell,
      0,
      &this->m_CurrentPos,
      0,
      this->m_dwCID,
      this->m_dwCID,
      AwardEquipment,
      1u,
      0);
  }
  v24 = this->m_lpGameClientDispatch;
  if ( v24 )
    GameClientSendPacket::SendCharLevelUp(&v24->m_SendStream, this->m_dwCID, &this->m_DBData);
  this->m_nDBUpdateCount = -1;
  CCharacter::DBUpdate(this, UPDATE);
  return CCharacter::CalculateStatusData(this, 1);
}

//----- (0041F5E0) --------------------------------------------------------
unsigned __int16 __thiscall CCharacter::AddState(CCharacter *this, unsigned __int8 Type_In, ChState *ChState_Out)
{
  unsigned __int8 RequiredIP; // al
  unsigned __int16 IP; // cx
  unsigned __int16 v7; // cx

  RequiredIP = CClass::GetRequiredIP(this->m_DBData.m_Info.Class, Type_In);
  IP = this->m_DBData.m_Info.IP;
  if ( IP < RequiredIP )
    return 2;
  switch ( Type_In )
  {
    case 1u:
      ++this->m_DBData.m_Info.STR;
      break;
    case 2u:
      ++this->m_DBData.m_Info.DEX;
      break;
    case 3u:
      ++this->m_DBData.m_Info.CON;
      break;
    case 4u:
      ++this->m_DBData.m_Info.INT;
      break;
    case 5u:
      ++this->m_DBData.m_Info.WIS;
      break;
    default:
      break;
  }
  v7 = IP - RequiredIP;
  this->m_DBData.m_Info.IP = v7;
  ChState_Out->m_wIP = v7;
  ChState_Out->m_wSTR = this->m_DBData.m_Info.STR;
  ChState_Out->m_wDEX = this->m_DBData.m_Info.DEX;
  ChState_Out->m_wCON = this->m_DBData.m_Info.CON;
  ChState_Out->m_wINT = this->m_DBData.m_Info.INT;
  ChState_Out->m_wWIS = this->m_DBData.m_Info.WIS;
  CCharacter::CalculateStatusData(this, 0);
  return 0;
}

//----- (0041F6C0) --------------------------------------------------------
char __thiscall CCharacter::StateRedistribution(CCharacter *this, ChState *State)
{
  unsigned __int16 v3; // cx
  const void *v5; // eax
  const void *v6; // esi
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  _CHAR_INFOST tempInfoSt; // [esp+14h] [ebp-68h] BYREF

  if ( this->m_DBData.m_Info.Chance )
  {
    if ( !CClass::CheckState(&ClassTable[this->m_DBData.m_Info.Class], State, this->m_DBData.m_Info.Level) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::StateRedistribution",
        aDWorkRylSource_52,
        666,
        aCid0x08x_199,
        this->m_dwCID,
        this->m_DBData.m_Info.Class,
        this->m_DBData.m_Info.Level,
        State->m_wSTR,
        State->m_wDEX,
        State->m_wCON,
        State->m_wINT,
        State->m_wWIS,
        State->m_wIP);
      return 0;
    }
    qmemcpy(&tempInfoSt, &this->m_DBData, sizeof(tempInfoSt));
    *(ChState *)&this->m_DBData.m_Info.IP = *State;
    if ( !CCharacter::RedistributionSkill(this) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::StateRedistribution",
        aDWorkRylSource_52,
        682,
        aCid0x08x_31,
        this->m_dwCID);
      qmemcpy(&this->m_DBData, &tempInfoSt, 0x48u);
      return 0;
    }
    QUICK::QUICK((QUICK *)&tempInfoSt);
    v6 = v5;
    LOBYTE(v5) = this->m_DBData.m_Info.Chance;
    qmemcpy(&this->m_DBData.m_Quick, v6, sizeof(this->m_DBData.m_Quick));
    v3 = 0;
    this->m_DBData.m_Info.Chance = (_BYTE)v5 - 1;
  }
  else
  {
    v3 = 2;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch
    && !GameClientSendPacket::SendCharStateRedistribution(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          &this->m_DBData,
          v3) )
  {
    return 0;
  }
  return CCharacter::CalculateStatusData(this, 1);
}
// 41F83E: variable 'v5' is possibly undefined

//----- (0041F8B0) --------------------------------------------------------
char __thiscall CCharacter::StatusRetrain(CCharacter *this, ChState *State, int InvenPos)
{
  int v4; // eax
  unsigned __int16 v5; // ax
  unsigned int Gold; // ecx
  unsigned int v7; // eax
  unsigned __int8 m_cNumOrDurability; // al
  unsigned __int8 v9; // al
  unsigned int v10; // esi
  const void *v12; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned __int16 usError; // [esp+10h] [ebp-78h]
  unsigned int dwReduceGold; // [esp+14h] [ebp-74h]
  Item::CItem *lpItem; // [esp+1Ch] [ebp-6Ch]
  _CHAR_INFOST tempInfoSt; // [esp+20h] [ebp-68h] BYREF

  usError = 0;
  if ( State->m_wIP - this->m_DBData.m_Info.IP > 4 )
  {
    usError = 2;
    goto SEND_PACKET;
  }
  v4 = ((int (__thiscall *)(Item::CArrayContainer *, int))this->m_Inventory.GetItem)(&this->m_Inventory, InvenPos);
  lpItem = (Item::CItem *)v4;
  if ( !v4 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      721,
      byte_4D9D88,
      InvenPos & 0xF,
      (unsigned __int16)InvenPos >> 4);
LABEL_24:
    usError = 3;
    goto SEND_PACKET;
  }
  v5 = *(_WORD *)(v4 + 16);
  if ( v5 != 9914 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      730,
      (char *)&byte_4D9D38,
      v5);
    usError = 4;
    goto SEND_PACKET;
  }
  Gold = this->m_DBData.m_Info.Gold;
  v7 = 10000 * this->m_CreatureStatus.m_nLevel;
  dwReduceGold = v7;
  if ( v7 > Gold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      740,
      (char *)&byte_4D9CD8,
      v7,
      Gold);
    usError = 5;
    goto SEND_PACKET;
  }
  if ( !CClass::CheckState(&ClassTable[this->m_DBData.m_Info.Class], State, this->m_DBData.m_Info.Level) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      752,
      aCid0x08x_199,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      this->m_DBData.m_Info.Level,
      State->m_wSTR,
      State->m_wDEX,
      State->m_wCON,
      State->m_wINT,
      State->m_wWIS,
      State->m_wIP);
LABEL_11:
    usError = 6;
    goto SEND_PACKET;
  }
  if ( !CClass::CheckMinState(&ClassTable[this->m_DBData.m_Info.Class], State, this->m_DBData.m_Info.Level) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      764,
      aCid0x08x_199,
      this->m_dwCID,
      this->m_DBData.m_Info.Class,
      this->m_DBData.m_Info.Level,
      State->m_wSTR,
      State->m_wDEX,
      State->m_wCON,
      State->m_wINT,
      State->m_wWIS,
      State->m_wIP);
    goto LABEL_11;
  }
  qmemcpy(&tempInfoSt, &this->m_DBData, sizeof(tempInfoSt));
  m_cNumOrDurability = lpItem->m_ItemData.m_cNumOrDurability;
  if ( !m_cNumOrDurability )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      782,
      aCid0x08x_86,
      InvenPos & 0xF,
      (unsigned __int16)InvenPos >> 4);
    goto LABEL_24;
  }
  v9 = m_cNumOrDurability - 1;
  lpItem->m_ItemData.m_cNumOrDurability = v9;
  if ( !v9 )
  {
    if ( CCharacter::RemoveItem(this, InvenPos) != 1 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::StatusRetrain",
        aDWorkRylSource_52,
        797,
        aCid0x08x_271,
        InvenPos & 0xF,
        (unsigned __int16)InvenPos >> 4);
      this->m_Inventory.DumpItemInfo(&this->m_Inventory);
      usError = 3;
      goto SEND_PACKET;
    }
    ((void (__thiscall *)(Item::CItem *, int))lpItem->~Item::CItem)(lpItem, 1);
  }
  v10 = this->m_DBData.m_Info.Gold;
  CCharacter::DeductGold(this, dwReduceGold, 0);
  GAMELOG::LogTakeGold(this, v10, this->m_DBData.m_Info.Gold, dwReduceGold, 2u, 2u, 8u, 0);
  *(ChState *)&this->m_DBData.m_Info.IP = *State;
  if ( !CCharacter::RedistributionSkill(this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::StatusRetrain",
      aDWorkRylSource_52,
      823,
      aCid0x08x_31,
      this->m_dwCID);
    qmemcpy(&this->m_DBData, &tempInfoSt, 0x48u);
    return 0;
  }
  QUICK::QUICK((QUICK *)&tempInfoSt);
  qmemcpy(&this->m_DBData.m_Quick, v12, sizeof(this->m_DBData.m_Quick));
SEND_PACKET:
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch
    && !GameClientSendPacket::SendCharStatusRetrain(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          &this->m_DBData,
          (Item::ItemPos)InvenPos,
          this->m_DBData.m_Info.Gold,
          usError) )
  {
    return 0;
  }
  if ( usError )
    return 1;
  else
    return CCharacter::CalculateStatusData(this, 1);
}
// 41FC0E: variable 'v12' is possibly undefined

//----- (0041FCD0) --------------------------------------------------------
char __thiscall CCharacter::ChangeWeaponAndShield(CCharacter *this, unsigned __int8 cSelect)
{
  if ( cSelect && cSelect <= 2u )
  {
    if ( cSelect == 1 )
    {
      if ( this->m_Equipments.m_cRightHand != 11 )
      {
LABEL_5:
        this->m_cHandPos = cSelect - 1;
        this->m_Equipments.m_cRightHand = (this->m_Equipments.m_cRightHand == 11) + 11;
        this->m_Equipments.m_cLeftHand = (this->m_Equipments.m_cLeftHand == 9) + 9;
        CCharacter::CalculateStatusData(this, 0);
        return 1;
      }
    }
    else if ( this->m_Equipments.m_cRightHand != 12 )
    {
      goto LABEL_5;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::ChangeWeaponAndShield",
      aDWorkRylSource_52,
      991,
      aCid0x08x_84,
      this->m_dwCID,
      cSelect);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::ChangeWeaponAndShield",
      aDWorkRylSource_52,
      984,
      aCid0x08x_299,
      this->m_dwCID,
      cSelect);
    return 0;
  }
}
// 41FD2C: conditional instruction was optimized away because %cSelect.1==2

//----- (0041FDA0) --------------------------------------------------------
void __cdecl std::_Random_shuffle<unsigned char *,int>(unsigned __int8 *_First, unsigned __int8 *_Last)
{
  unsigned __int8 *v2; // ebx
  unsigned int v3; // edi
  unsigned int v4; // esi
  unsigned int i; // eax
  int v6; // edx
  unsigned __int8 v7; // al

  v2 = _First + 1;
  if ( _First + 1 != _Last )
  {
    v3 = 2;
    do
    {
      v4 = 0x7FFF;
      for ( i = rand() & 0x7FFF; v4 < v3; i = (i << 15) | 0x7FFF )
      {
        if ( v4 == -1 )
          break;
        v4 = (v4 << 15) | 0x7FFF;
      }
      v6 = i % v3;
      v7 = *v2;
      *v2 = _First[v6];
      _First[v6] = v7;
      ++v2;
      ++v3;
    }
    while ( v2 != _Last );
  }
}

//----- (0041FE10) --------------------------------------------------------
char __thiscall CCharacter::GetHuntingExp(
        CCharacter *this,
        unsigned int lpDeadCreature,
        signed int dwExp,
        int cMemberNum)
{
  unsigned __int8 v4; // bl
  int v5; // eax
  CServerSetup *Instance; // eax
  CAggresiveCreature *v8; // ebp
  CAggresiveCreature_vtbl *v9; // edx
  CMonsterParty *v10; // eax
  signed int v11; // edi
  double v12; // st7
  unsigned int *p_lpDeadCreature; // eax
  unsigned int v14; // edi
  bool v15; // al
  unsigned int v16; // edi
  bool v17; // al
  unsigned int m_dwCID; // ebx
  CServerSetup *v19; // eax
  char v20; // al
  int v22; // [esp+28h] [ebp-Ch]
  int v23; // [esp+2Ch] [ebp-8h]

  v4 = cMemberNum;
  v5 = (unsigned __int8)cMemberNum;
  cMemberNum = SLODWORD(FLOAT_1_4[(unsigned __int8)cMemberNum]);
  v23 = v5;
  if ( CServerSetup::GetInstance()->m_eNationType == THAILAND && v4 >= 2u )
    *(float *)&cMemberNum = *(float *)&cMemberNum * 1.4;
  if ( CServerSetup::GetInstance()->m_eNationType && this->m_DBData.m_Info.Level <= 60 || !this->IsPeaceMode(this) )
    v22 = 2;
  else
    v22 = 1;
  Instance = CServerSetup::GetInstance();
  v8 = (CAggresiveCreature *)lpDeadCreature;
  v9 = *(CAggresiveCreature_vtbl **)lpDeadCreature;
  *(float *)&cMemberNum = (double)*((unsigned int *)&ExpTable_0[98] + this->m_CreatureStatus.m_nLevel + 1)
                        * ((double)Instance->m_dwExp
                         * 0.**********)
                        * (double)v22
                        * *(float *)&cMemberNum;
  if ( v9->GetParty((struct CAggresiveCreature *)lpDeadCreature) )
  {
    v10 = (CMonsterParty *)v8->GetParty(v8);
    switch ( CMonsterParty::GetMemberTypeNum(v10) )
    {
      case 1u:
        v11 = dwExp;
        v12 = (double)(unsigned int)dwExp * 0.059999999;
        goto LABEL_16;
      case 2u:
        v11 = dwExp;
        v12 = (double)(unsigned int)dwExp * 0.12;
        goto LABEL_16;
      case 3u:
        v11 = dwExp;
        v12 = (double)(unsigned int)dwExp * 0.18000001;
        goto LABEL_16;
      case 4u:
        v11 = dwExp;
        v12 = (double)(unsigned int)dwExp * 0.23999999;
        goto LABEL_16;
      case 5u:
        v11 = dwExp;
        v12 = (double)(unsigned int)dwExp * 0.30000001;
LABEL_16:
        dwExp = (unsigned __int64)v12 + v11;
        break;
      default:
        break;
    }
  }
  lpDeadCreature = 1;
  p_lpDeadCreature = &lpDeadCreature;
  if ( dwExp )
    p_lpDeadCreature = (unsigned int *)&dwExp;
  v14 = *p_lpDeadCreature;
  if ( CServerSetup::GetInstance()->m_eNationType && this->m_DBData.m_Info.Level <= 60
    || (v15 = this->IsPeaceMode(this), dwExp = 1, !v15) )
  {
    dwExp = 2;
  }
  lpDeadCreature = CServerSetup::GetInstance()->m_dwExp;
  v16 = (unsigned __int64)((double)lpDeadCreature * 0.********** * (double)dwExp * (double)v14);
  dwExp = v16;
  if ( (double)v16 > *(float *)&cMemberNum )
  {
    if ( CServerSetup::GetInstance()->m_eNationType && this->m_DBData.m_Info.Level <= 60
      || (v17 = this->IsPeaceMode(this), dwExp = 1, !v17) )
    {
      dwExp = 2;
    }
    m_dwCID = v8->m_dwCID;
    v19 = CServerSetup::GetInstance();
    CServerLog::SimpleLog(
      &g_Log,
      1,
      aCid0x08x_178,
      this->m_dwCID,
      v23,
      dwExp * v19->m_dwExp,
      v16,
      *(float *)&cMemberNum,
      this->m_CreatureStatus.m_nLevel,
      this->m_DBData.m_Info.Class,
      m_dwCID,
      v8->m_CreatureStatus.m_nLevel);
    v16 = (unsigned __int64)*(float *)&cMemberNum;
  }
  v20 = this->GetEliteBonus(this);
  if ( v20 > 0 )
  {
    cMemberNum = v16 * *((__int16 *)&ms_szVirtualAreaScriptFileName_1 + v20 + 1);
    v16 = (unsigned __int64)((double)(unsigned int)cMemberNum * 0.**********);
  }
  if ( this->m_lpSummonee )
  {
    cMemberNum = v16;
    v16 = (unsigned __int64)((double)v16 * 0.89999998);
  }
  return CCharacter::IncrementExp(this, v16);
}
// 4D9F40: using guessed type const float FLOAT_1_4[];

//----- (00420160) --------------------------------------------------------
char __thiscall CCharacter::CalculateEquipDurability(CCharacter *this, unsigned int wAttackType)
{
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  int v5; // eax
  Item::CItem *v6; // esi
  unsigned __int8 m_cItemType; // al
  int v8; // ebx
  unsigned __int8 m_cNumOrDurability; // al
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  if ( (wAttackType & 0x8000) != 0 )
  {
    if ( Math::Random::ComplexRandom(180, 0) )
      return 1;
    SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, wAttackType);
    if ( !SkillProtoType )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::CalculateEquipDurability",
        aDWorkRylSource_52,
        1043,
        aCid0x08x_77,
        this->m_dwCID,
        (unsigned __int16)wAttackType);
      return 0;
    }
    v5 = SkillProtoType->m_ProtoTypes[0].m_cDRC - 1;
    if ( v5 )
    {
      if ( v5 != 1 )
        return 1;
      v6 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand];
    }
    else
    {
      v6 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
    }
    goto LABEL_23;
  }
  if ( (unsigned __int16)wAttackType != 5 )
  {
    if ( (unsigned __int16)wAttackType == 6 )
    {
      if ( Math::Random::ComplexRandom(30, 0) )
        return 1;
      v6 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cLeftHand];
      if ( !v6 || (m_cItemType = v6->m_ItemInfo->m_DetailData.m_cItemType, m_cItemType != 16) && m_cItemType != 40 )
      {
        v6 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
        if ( !v6 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::CalculateEquipDurability",
            aDWorkRylSource_52,
            1080,
            aCid0x08x_62,
            this->m_dwCID);
          return 0;
        }
        goto LABEL_24;
      }
    }
    else
    {
      if ( Math::Random::ComplexRandom(180, 0) )
        return 1;
      v6 = this->m_Equipments.m_lppItems[this->m_Equipments.m_cRightHand];
    }
LABEL_23:
    if ( v6 )
    {
LABEL_24:
      m_cNumOrDurability = v6->m_ItemData.m_cNumOrDurability;
      if ( m_cNumOrDurability )
        v6->m_ItemData.m_cNumOrDurability = m_cNumOrDurability - 1;
      if ( !v6->m_ItemData.m_cNumOrDurability )
        CCharacter::CalculateStatusData(this, 0);
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        LOWORD(wAttackType) = v6->m_ItemData.m_ItemPos;
        GameClientSendPacket::SendCharEquipDurability(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          wAttackType >> 4,
          v6->m_ItemData.m_cNumOrDurability,
          0);
      }
      return 1;
    }
    return 1;
  }
  if ( !Math::Random::ComplexRandom(30, 0) )
  {
    std::_Random_shuffle<unsigned char *,int>(aryArmourShuffleList, (unsigned __int8 *)usBonusExp_8);
    v8 = 0;
    while ( 1 )
    {
      LOWORD(wAttackType) = (16 * aryArmourShuffleList[v8]) | 1;
      v6 = (Item::CItem *)((int (__thiscall *)(Item::CEquipmentsContainer *, unsigned int))this->m_Equipments.GetItem)(
                            &this->m_Equipments,
                            wAttackType);
      if ( v6 )
        goto LABEL_24;
      if ( ++v8 >= 4 )
        goto LABEL_23;
    }
  }
  return 1;
}

//----- (00420390) --------------------------------------------------------
Item::CItemContainer *__thiscall CCharacter::GetItemContainer(CCharacter *this, unsigned __int8 cPos)
{
  Item::CItemContainer *result; // eax

  switch ( cPos )
  {
    case 1u:
      result = &this->m_Equipments;
      break;
    case 2u:
      result = &this->m_Inventory;
      break;
    case 6u:
    case 7u:
      result = &this->m_ExtraSpace;
      break;
    case 8u:
      result = &this->m_Exchange;
      break;
    case 9u:
      result = &this->m_Deposit;
      break;
    case 0xAu:
      result = &this->m_Stall;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (00420410) --------------------------------------------------------
unsigned int __thiscall CCharacter::RepairItem(CCharacter *this, unsigned int dwNPCID, int itemPos)
{
  CMsgProcessMgr *Instance; // eax
  CNPC *Castle; // ebp
  CServerSetup *v7; // eax
  int ServerZone; // edi
  VirtualArea::CVirtualAreaMgr *v9; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  Item::CEquipment *Item; // eax
  unsigned __int8 m_cMaxNumOrDurability; // cl
  unsigned __int8 m_cNumOrDurability; // dl
  Item::CEquipment *v14; // eax
  unsigned int v15; // edi
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-10h]

  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = (CNPC *)Castle::CCastleMgr::GetCastle(Instance, dwNPCID);
  if ( !Castle )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      28,
      aCid0x08xNpc,
      this->m_dwCID,
      dwNPCID);
    return 0;
  }
  v7 = CServerSetup::GetInstance();
  ServerZone = (char)CServerSetup::GetServerZone(v7);
  if ( this->m_CellPos.m_wMapIndex )
  {
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    v9 = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v9, m_wMapIndex);
    if ( VirtualArea )
      ServerZone = VirtualArea::CVirtualArea::GetVirtualZone(VirtualArea);
  }
  if ( Castle->m_nZone != ServerZone )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      43,
      aCid0x08xNpc_0,
      this->m_dwCID,
      ServerZone,
      Castle->m_nZone);
    return 0;
  }
  Item = (Item::CEquipment *)CCharacter::GetItem(this, itemPos);
  if ( !Item )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      50,
      aCid0x08x_232,
      this->m_dwCID,
      itemPos & 0xF,
      (unsigned __int16)itemPos >> 4);
    return 0;
  }
  if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      56,
      aCid0x08x_231,
      this->m_dwCID);
    return 0;
  }
  if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 1) != 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      62,
      aCid0x08x_278,
      this->m_dwCID);
    return 0;
  }
  m_cMaxNumOrDurability = Item->m_cMaxNumOrDurability;
  m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability;
  if ( m_cNumOrDurability < m_cMaxNumOrDurability )
  {
    v14 = Item::CEquipment::DowncastToEquipment(Item);
    v15 = CNPC::RepairItem(Castle, v14, &this->m_DBData.m_Info.Gold);
    if ( v15 )
      CCharacter::CalculateStatusData(this, 0);
    return v15;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairItem",
      aDWorkRylSource_89,
      70,
      aCid0x08x_123,
      this->m_dwCID,
      Item->m_ItemData.m_usProtoTypeID,
      m_cNumOrDurability,
      m_cMaxNumOrDurability);
    return 0;
  }
}

//----- (004205C0) --------------------------------------------------------
int __thiscall CCharacter::RepairAllItem(CCharacter *this, unsigned int dwNPCID)
{
  CMsgProcessMgr *Instance; // eax
  CMsgProc *Castle; // ebx
  CServerSetup *v6; // eax
  CMsgProc_vtbl *ServerZone; // esi
  VirtualArea::CVirtualAreaMgr *v8; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  int v10; // ebx
  int i; // ebp
  unsigned __int16 v12; // si
  Item::CEquipment *Item; // eax
  unsigned __int8 m_cMaxNumOrDurability; // cl
  unsigned __int8 m_cNumOrDurability; // dl
  Item::CEquipment *v16; // eax
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-14h]
  CNPC *pNPC; // [esp+Ch] [ebp-4h]

  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = Castle::CCastleMgr::GetCastle(Instance, dwNPCID);
  pNPC = (CNPC *)Castle;
  if ( Castle )
  {
    v6 = CServerSetup::GetInstance();
    ServerZone = (CMsgProc_vtbl *)(char)CServerSetup::GetServerZone(v6);
    if ( this->m_CellPos.m_wMapIndex )
    {
      m_wMapIndex = this->m_CellPos.m_wMapIndex;
      v8 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v8, m_wMapIndex);
      if ( VirtualArea )
        ServerZone = (CMsgProc_vtbl *)VirtualArea::CVirtualArea::GetVirtualZone(VirtualArea);
    }
    if ( Castle[10].__vftable == ServerZone )
    {
      v10 = 0;
      LOBYTE(dwNPCID) = 1;
      for ( i = 0; i < 16; ++i )
      {
        if ( this->m_DBData.m_Info.Nationality != 1 || i != 1 && i != 2 && i != 10 && i <= 12 )
        {
          v12 = dwNPCID & 0xF | (16 * i);
          LOWORD(dwNPCID) = v12;
          Item = (Item::CEquipment *)CCharacter::GetItem(this, dwNPCID);
          if ( Item )
          {
            if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "CCharacter::RepairAllItem",
                aDWorkRylSource_89,
                122,
                aCid0x08x_322,
                this->m_dwCID,
                v12 & 0xF,
                v12 >> 4);
            }
            else if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 )
            {
              m_cMaxNumOrDurability = Item->m_cMaxNumOrDurability;
              m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability;
              if ( m_cNumOrDurability < m_cMaxNumOrDurability )
              {
                v16 = Item::CEquipment::DowncastToEquipment(Item);
                v10 += CNPC::RepairItem(pNPC, v16, &this->m_DBData.m_Info.Gold);
              }
              else
              {
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "CCharacter::RepairAllItem",
                  aDWorkRylSource_89,
                  137,
                  aCid0x08x_123,
                  this->m_dwCID,
                  Item->m_ItemData.m_usProtoTypeID,
                  m_cNumOrDurability,
                  m_cMaxNumOrDurability);
              }
            }
            else
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "CCharacter::RepairAllItem",
                aDWorkRylSource_89,
                129,
                aCid0x08x_145,
                this->m_dwCID,
                v12 & 0xF,
                v12 >> 4);
            }
          }
        }
      }
      if ( v10 )
        CCharacter::CalculateStatusData(this, 0);
      return v10;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::RepairAllItem",
        aDWorkRylSource_89,
        104,
        aCid0x08xNpc_0,
        this->m_dwCID,
        ServerZone,
        Castle[10].__vftable);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RepairAllItem",
      aDWorkRylSource_89,
      89,
      aCid0x08xNpc,
      this->m_dwCID,
      dwNPCID);
    return 0;
  }
}

//----- (004207D0) --------------------------------------------------------
Item::CItem *__thiscall CCharacter::SellToCharacter(
        CCharacter *this,
        CCharacter *lpCustomer,
        unsigned __int16 wKindItem,
        __int64 takeType,
        unsigned int *dwPrice)
{
  Item::CItem *result; // eax
  Item::CItem *v7; // esi
  unsigned int m_dwStallPrice; // eax
  unsigned int Gold; // ecx
  unsigned int m_dwPrice; // ebx
  unsigned int v11; // eax
  unsigned int *v12; // ebx
  Item::CItemContainer *ItemContainer; // eax
  unsigned __int8 m_cNumOrDurability; // cl
  Item::CItem *v15; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int dwCustomerCID; // [esp+10h] [ebp-8h]
  unsigned __int16 usProtoTypeID; // [esp+14h] [ebp-4h]

  result = (Item::CItem *)((int (__thiscall *)(Item::CStallContainer *, _DWORD))this->m_Stall.GetItem)(
                            &this->m_Stall,
                            takeType);
  v7 = result;
  if ( result )
  {
    m_dwStallPrice = result->m_dwStallPrice;
    dwCustomerCID = lpCustomer->m_dwCID;
    Gold = lpCustomer->m_DBData.m_Info.Gold;
    if ( m_dwStallPrice )
      m_dwPrice = m_dwStallPrice;
    else
      m_dwPrice = v7->m_dwPrice;
    v11 = m_dwPrice * BYTE4(takeType);
    v12 = dwPrice;
    *dwPrice = v11;
    if ( v11 > Gold )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SellToCharacter",
        aDWorkRylSource_89,
        165,
        (char *)&byte_4DA418,
        v11,
        Gold);
      GAMELOG::LogTradeItem(this, dwCustomerCID, *dwPrice, v7, (Item::ItemPos)takeType, 2u, 4u);
      return 0;
    }
    if ( ((unsigned __int8 (__thiscall *)(Item::CArrayContainer *, _DWORD, _DWORD))lpCustomer->m_Inventory.TestItem)(
           &lpCustomer->m_Inventory,
           *(_DWORD *)((char *)&takeType + 2),
           v7->m_ItemData.m_usProtoTypeID) )
    {
      m_cNumOrDurability = v7->m_ItemData.m_cNumOrDurability;
      LOWORD(dwPrice) = v7->m_ItemData.m_ItemPos;
      if ( (v7->m_ItemInfo->m_DetailData.m_dwFlags & 8) != 8 )
        goto LABEL_27;
      if ( BYTE4(takeType) > m_cNumOrDurability )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SellToCharacter",
          aDWorkRylSource_89,
          240,
          (char *)&byte_4DA348,
          (unsigned __int8)takeType & 0xF,
          (unsigned __int16)takeType >> 4,
          m_cNumOrDurability,
          BYTE4(takeType));
        GAMELOG::LogTradeItem(this, dwCustomerCID, *v12, v7, (Item::ItemPos)takeType, 2u, 4u);
        return 0;
      }
      if ( BYTE4(takeType) == m_cNumOrDurability )
      {
LABEL_27:
        if ( !((unsigned __int8 (__thiscall *)(Item::CStallContainer *, _DWORD))this->m_Stall.RemoveItem)(
                &this->m_Stall,
                takeType) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::SellToCharacter",
            aDWorkRylSource_89,
            203,
            aCid0x08x_341,
            this->m_dwCID,
            (unsigned __int8)takeType & 0xF,
            (unsigned __int16)takeType >> 4);
          GAMELOG::LogTradeItem(this, dwCustomerCID, *v12, v7, (Item::ItemPos)takeType, 2u, 4u);
          return 0;
        }
        CCharacter::RemoveItem(this, (int)dwPrice);
      }
      else
      {
        v7->m_ItemData.m_cNumOrDurability = m_cNumOrDurability - BYTE4(takeType);
        Item::CStallContainer::SendRemoveItem(&this->m_Stall, (Item::ItemPos)takeType, BYTE4(takeType));
        usProtoTypeID = v7->m_ItemData.m_usProtoTypeID;
        Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, usProtoTypeID);
        v7 = v15;
        if ( !v15 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::SellToCharacter",
            aDWorkRylSource_89,
            221,
            (char *)&byte_4DA314,
            usProtoTypeID);
          GAMELOG::LogTradeItem(this, dwCustomerCID, *v12, 0, (Item::ItemPos)takeType, 2u, 4u);
          return 0;
        }
        v15->m_ItemData.m_cNumOrDurability = BYTE4(takeType);
      }
      CCharacter::AddGold(this, *v12, 0);
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharTradeItem(
          &m_lpGameClientDispatch->m_SendStream,
          this,
          lpCustomer->m_dwCID,
          0,
          (Item::ItemPos)dwPrice,
          BYTE4(takeType),
          0);
      GAMELOG::LogTradeItem(this, dwCustomerCID, *v12, v7, (Item::ItemPos)takeType, 2u, 0);
      return v7;
    }
    else
    {
      ItemContainer = CCharacter::GetItemContainer(lpCustomer, BYTE2(takeType) & 0xF);
      if ( ItemContainer )
        ItemContainer->DumpItemInfo(ItemContainer);
      else
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SellToCharacter",
          aDWorkRylSource_89,
          179,
          aCid0x08x_1,
          dwCustomerCID);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SellToCharacter",
        aDWorkRylSource_89,
        183,
        aCid0x08x_263,
        dwCustomerCID,
        v7->m_ItemData.m_usProtoTypeID,
        BYTE2(takeType) & 0xF,
        WORD1(takeType) >> 4);
      return 0;
    }
  }
  return result;
}
// 4209D4: variable 'v15' is possibly undefined

//----- (00420B00) --------------------------------------------------------
char __thiscall CCharacter::Login(CCharacter *this, CCharacter::CellLoginStatus eLoginStatus)
{
  unsigned __int16 HP; // ax
  unsigned __int16 MP; // cx
  CCellManager *Instance; // eax
  float fPointZ; // ecx
  float fPointY; // ecx
  float v8; // edx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CSendStream *p_m_SendStream; // edi
  char v11; // al
  Position m_CurrentPos; // [esp-Ch] [ebp-24h]

  if ( eLoginStatus == MIDDLE_ADMIN )
  {
    HP = this->m_DBData.m_Info.HP;
    MP = this->m_DBData.m_Info.MP;
    this->m_CreatureStatus.m_nNowHP = HP;
    this->m_CreatureStatus.m_nNowMP = MP;
  }
  m_CurrentPos = this->m_CurrentPos;
  Instance = CCellManager::GetInstance();
  if ( !CCellManager::CheckPositionInZone(Instance, m_CurrentPos) )
  {
    this->m_DBData.m_Pos.LastPoint.fPointX = this->m_DBData.m_Pos.SavePoint.fPointX;
    fPointZ = this->m_DBData.m_Pos.SavePoint.fPointZ;
    this->m_DBData.m_Pos.LastPoint.fPointY = this->m_DBData.m_Pos.SavePoint.fPointY;
    this->m_DBData.m_Pos.LastPoint.fPointZ = fPointZ;
    fPointY = this->m_DBData.m_Pos.LastPoint.fPointY;
    v8 = this->m_DBData.m_Pos.LastPoint.fPointZ;
    this->m_CurrentPos.m_fPointX = this->m_DBData.m_Pos.LastPoint.fPointX;
    this->m_CurrentPos.m_fPointY = fPointY;
    this->m_CurrentPos.m_fPointZ = v8;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  this->m_cOperationFlags |= 1u;
  this->m_eCellLoginStatus = eLoginStatus;
  if ( m_lpGameClientDispatch )
  {
    p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
    v11 = this->GetEliteBonus(this);
    GameClientSendPacket::SendCharEliteBonus(p_m_SendStream, v11);
    GameClientSendPacket::SendCharControlOption(p_m_SendStream, this->m_dwCID, &this->m_RejectOption);
  }
  return 1;
}

//----- (00420BF0) --------------------------------------------------------
bool __thiscall CCharacter::Logout(CCharacter *this, DBUpdateData::UpdateType eUpdateType)
{
  CServerSetup *Instance; // eax
  CCreatureManager *v4; // eax
  CCell *m_lpCell; // ecx
  unsigned __int16 v6; // bx
  VirtualArea::CVirtualAreaMgr *v7; // eax
  CMonster *m_lpSummonee; // ecx
  CCharacter *m_lpExchangeCharacter; // edi
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int Party; // eax
  CParty *m_pParty; // ecx
  CTempCharacterMgr *TempCharacterMgr; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebp
  CSendStream *v16; // edi
  CSiegeObjectMgr *v17; // eax
  CSiegeObject *SiegeObject; // eax
  CSiegeObject *v19; // edi
  unsigned __int16 m_wObjectType; // ax
  unsigned int v21; // eax
  unsigned int m_dwUID; // ecx
  int m_nCurrentUID_high; // eax
  unsigned int v24; // edx
  CCreatureManager *v25; // eax
  bool v26; // bl
  unsigned int m_dwCID; // [esp-4h] [ebp-1FECh]
  unsigned int m_dwRideArmsCID; // [esp-4h] [ebp-1FECh]
  unsigned int v30; // [esp-4h] [ebp-1FECh]
  unsigned int v31; // [esp-4h] [ebp-1FECh]
  int nBufferSize_InOut; // [esp+10h] [ebp-1FD8h] BYREF
  CSingleDispatch::Storage v33; // [esp+14h] [ebp-1FD4h] BYREF
  char szDest[64]; // [esp+1Ch] [ebp-1FCCh] BYREF
  char SourceData[12]; // [esp+5Ch] [ebp-1F8Ch] BYREF
  int m_nCurrentUID; // [esp+68h] [ebp-1F80h]
  int v37; // [esp+6Ch] [ebp-1F7Ch]
  unsigned int v38; // [esp+74h] [ebp-1F74h]
  unsigned int v39; // [esp+78h] [ebp-1F70h]
  int v40; // [esp+7Ch] [ebp-1F6Ch]
  int v41; // [esp+84h] [ebp-1F64h]
  unsigned __int16 UpdateLen[2]; // [esp+88h] [ebp-1F60h] BYREF
  int v43; // [esp+8Ch] [ebp-1F5Ch]
  int v44; // [esp+90h] [ebp-1F58h]
  int v45; // [esp+94h] [ebp-1F54h]
  char pBuffer[8000]; // [esp+98h] [ebp-1F50h] BYREF
  int v47; // [esp+1FE4h] [ebp-4h]

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 3 )
  {
    v4 = CCreatureManager::GetInstance();
    CCreatureManager::PopRespawnQueue(v4, this);
  }
  m_lpCell = this->m_CellPos.m_lpCell;
  v6 = 0;
  if ( m_lpCell )
  {
    CCell::DeleteCreature(
      m_lpCell,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    this->m_CellPos.m_lpCell = 0;
  }
  if ( this->m_CellPos.m_wMapIndex )
  {
    v7 = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea::CVirtualAreaMgr::LeaveVirtualArea(v7, this);
  }
  m_lpSummonee = this->m_lpSummonee;
  if ( m_lpSummonee )
    m_lpSummonee->Dead(m_lpSummonee, 0);
  m_lpExchangeCharacter = this->m_Exchange.m_lpExchangeCharacter;
  if ( m_lpExchangeCharacter )
  {
    Item::CExchangeContainer::ExchangeOK(&this->m_Exchange, 0);
    m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharExchangeCmd(
        m_lpGameClientDispatch + 8,
        this->m_dwCID,
        m_lpExchangeCharacter->m_dwCID,
        6u,
        0);
  }
  CCharacter::DuelInit(this, 3u);
  CPartyMgr::DeleteFindPartyList(CSingleton<CPartyMgr>::ms_pSingleton, this->m_dwCID);
  Party = this->m_DBData.m_Info.Party;
  if ( Party )
  {
    m_pParty = this->m_pParty;
    if ( m_pParty )
      m_pParty->PrepareLogout(m_pParty, this->m_dwCID);
    else
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Logout",
        aDWorkRylSource_1,
        275,
        aCid0x08x_324,
        this->m_dwCID,
        Party,
        0);
  }
  m_dwCID = this->m_dwCID;
  TempCharacterMgr = CRegularAgentDispatch::GetTempCharacterMgr();
  CTempCharacterMgr::EraseChar(TempCharacterMgr, m_dwCID);
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&v33, DispatchTable);
  v47 = 0;
  m_lpDispatch = v33.m_lpDispatch;
  if ( v33.m_lpDispatch )
  {
    v16 = (CSendStream *)&v33.m_lpDispatch[8];
    GameClientSendPacket::SendCharQuestInfo((CSendStream *)&v33.m_lpDispatch[8], this);
    GameClientSendPacket::SendConfigInfoDB(v16, this);
  }
  if ( this->m_dwRideArmsCID )
  {
    m_dwRideArmsCID = this->m_dwRideArmsCID;
    v17 = CSiegeObjectMgr::GetInstance();
    SiegeObject = CSiegeObjectMgr::GetSiegeObject(v17, m_dwRideArmsCID);
    v19 = SiegeObject;
    if ( SiegeObject )
    {
      v30 = this->m_dwCID;
      this->m_dwRideArmsCID = 0;
      CSiegeObject::GetOff(SiegeObject, v30);
      m_wObjectType = v19->m_wObjectType;
      if ( m_wObjectType == 5530 || m_wObjectType == 5578 || m_wObjectType == 5626 )
      {
        if ( m_lpDispatch )
          GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
            (CSendStream *)&m_lpDispatch[8],
            this->m_dwCID,
            v19->m_dwCID,
            0,
            5u,
            0);
      }
    }
  }
  nBufferSize_InOut = 8060;
  if ( CCharacter::GetCharacterInfo(this, pBuffer, &nBufferSize_InOut, UpdateLen) )
  {
    if ( m_lpDispatch )
    {
      if ( (this->m_cOperationFlags & 1) != 0 )
      {
        if ( !Item::CDepositContainer::DBUpdate(&this->m_Deposit, (CSendStream *)&m_lpDispatch[8]) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::Logout",
            aDWorkRylSource_1,
            347,
            aCid0x08x_200,
            this->m_dwCID);
        m_dwUID = this->m_dwUID;
        m_nCurrentUID = CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID;
        m_nCurrentUID_high = HIDWORD(CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID);
        v24 = this->m_dwCID;
        v38 = m_dwUID;
        v39 = v24;
        v37 = m_nCurrentUID_high;
        HIWORD(v41) = eUpdateType;
        v40 = 0;
        if ( CSendStream::WrapCompress(
               (CSendStream *)&m_lpDispatch[8],
               SourceData,
               (char *)(nBufferSize_InOut + 60),
               0x26u,
               0,
               0) )
        {
          Math::Convert::Hex64ToStr(szDest, this->m_DBData.m_Info.Exp);
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CCharacter::Logout",
            aDWorkRylSource_1,
            366,
            aUidDCid0x08x0x_5,
            this->m_dwUID,
            this->m_dwCID,
            this,
            this->m_lpGameClientDispatch,
            this->m_DBData.m_Info.Name,
            this->m_DBData.m_Info.Level,
            szDest);
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::Logout",
            aDWorkRylSource_1,
            370,
            aCid0x08xDb,
            this->m_dwCID);
        }
      }
      else
      {
        DBAgentPacketParse::SendAbnormalLogout(this->m_dwUID, this->m_dwCID, 0, this->m_lpGameClientDispatch);
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Logout",
        aDWorkRylSource_1,
        383,
        "CID:0x%08x/AgentSession:0x%p/ DBUpdate failed.",
        this->m_dwCID,
        0);
    }
  }
  else
  {
    v21 = this->m_dwCID;
    *(_DWORD *)UpdateLen = 0;
    v43 = 0;
    v44 = 0;
    nBufferSize_InOut = 0;
    v6 = 1;
    v45 = 0;
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacter::Logout", aDWorkRylSource_1, 335, aCid0x08x_75, v21);
  }
  GAMELOG::LogCharLoginOut(this->m_dwUID, this, &this->m_PublicAddress, pBuffer, nBufferSize_InOut, UpdateLen, 2u, v6);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::Logout",
    aDWorkRylSource_1,
    394,
    aUidDCid0x08x0x_4,
    this->m_dwUID,
    this->m_dwCID,
    this,
    this->m_lpGameClientDispatch);
  v31 = this->m_dwCID;
  v25 = CCreatureManager::GetInstance();
  v26 = CCreatureManager::DeleteCreature(v25, v31);
  v47 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v33);
  return v26;
}

//----- (00421050) --------------------------------------------------------
char __thiscall CCharacter::CharacterCellLogin(CCharacter *this)
{
  CCharacter::CellLoginStatus m_eCellLoginStatus; // eax
  __int32 v3; // eax
  Position *p_m_CurrentPos; // edi
  CellPosition *p_m_CellPos; // esi
  POS *DefaultCharacterPos; // eax
  float fPointX; // ecx
  float fPointY; // edx
  float fPointZ; // esi
  float v10; // edx
  float v11; // eax
  float v12; // eax
  float v13; // edx
  CCell *m_lpCell; // esi
  CServerSetup *Instance; // eax
  CCharacter_vtbl *v17; // eax
  CPartyMgr *v18; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v20; // ebx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CGameClientDispatch *v22; // eax
  Castle::CCastleMgr *v23; // eax
  Castle::CCastle *CastleByGID; // eax
  CSiegeObject *CastleEmblem; // eax
  CGameClientDispatch *v26; // eax
  unsigned int m_dwUID; // ecx
  unsigned int m_dwGold; // [esp-30h] [ebp-4C4h]
  GuildRight v29; // [esp-2Ch] [ebp-4C0h] BYREF
  int v30; // [esp+8h] [ebp-48Ch]
  int v31; // [esp+Ch] [ebp-488h]
  float v32; // [esp+20h] [ebp-474h]
  float v33; // [esp+24h] [ebp-470h]
  int v34; // [esp+28h] [ebp-46Ch]
  int v35; // [esp+2Ch] [ebp-468h]
  int cTitle; // [esp+30h] [ebp-464h]
  Skill::CAddSpell<CInvincibleSpell> v37; // [esp+34h] [ebp-460h] BYREF
  POS result; // [esp+44h] [ebp-450h] BYREF
  char szExp[64]; // [esp+50h] [ebp-444h] BYREF
  char szBuffer[1024]; // [esp+90h] [ebp-404h] BYREF

  m_eCellLoginStatus = this->m_eCellLoginStatus;
  if ( m_eCellLoginStatus == NONE )
    return 0;
  v3 = m_eCellLoginStatus - 1;
  if ( v3 )
  {
    if ( v3 == 1 )
    {
      p_m_CurrentPos = &this->m_CurrentPos;
      p_m_CellPos = &this->m_CellPos;
      CellPosition::MoveTo(&this->m_CellPos, &this->m_CurrentPos);
      if ( !p_m_CellPos->m_lpCell )
      {
        DefaultCharacterPos = CharCreate::GetDefaultCharacterPos(
                                &result,
                                (unsigned __int8)this->m_DBData.m_Info.Nationality,
                                0);
        fPointX = DefaultCharacterPos->fPointX;
        fPointY = DefaultCharacterPos->fPointY;
        fPointZ = DefaultCharacterPos->fPointZ;
        this->m_DBData.m_Pos.SavePoint.fPointX = DefaultCharacterPos->fPointX;
        this->m_DBData.m_Pos.SavePoint.fPointY = fPointY;
        this->m_DBData.m_Pos.SavePoint.fPointZ = fPointZ;
        this->m_DBData.m_Pos.LastPoint.fPointX = fPointX;
        this->m_DBData.m_Pos.LastPoint.fPointY = fPointY;
        this->m_DBData.m_Pos.LastPoint.fPointZ = fPointZ;
        v10 = this->m_DBData.m_Pos.LastPoint.fPointY;
        v11 = this->m_DBData.m_Pos.LastPoint.fPointZ;
        v32 = this->m_DBData.m_Pos.LastPoint.fPointX;
        v33 = v10;
        *(float *)&v34 = v11;
        v12 = v10;
        p_m_CurrentPos->m_fPointX = v32;
        v13 = *(float *)&v34;
        this->m_CurrentPos.m_fPointY = v12;
        this->m_CurrentPos.m_fPointZ = v13;
        p_m_CellPos = &this->m_CellPos;
        CellPosition::MoveTo(&this->m_CellPos, &this->m_CurrentPos);
      }
      m_lpCell = p_m_CellPos->m_lpCell;
      if ( !m_lpCell )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::CharacterCellLogin",
          aDWorkRylSource_1,
          113,
          aCid0x08x_304,
          this->m_dwCID,
          p_m_CurrentPos->m_fPointX,
          this->m_CurrentPos.m_fPointZ);
        return 0;
      }
      CCell::SetCreature(m_lpCell, this->m_dwCID, this, LOGINOUT);
      Instance = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 4 && this->m_CreatureStatus.m_nLevel < 0x5Fu )
      {
        while ( CCharacter::IncrementExp(this, (unsigned int)(&szNoRemove_9)[2 * this->m_CreatureStatus.m_nLevel])
             && this->m_CreatureStatus.m_nLevel < 0x5Fu )
          ;
      }
      v34 = 1966110;
      LOWORD(v35) = 1;
      BYTE2(v35) = 1;
      *(_DWORD *)&v37.m_Spell_Info.m_wDurationSec = 1966110;
      v37.m_Spell_Info.m_SkillProtoType = &Skill::CProcessTable::ProcessInfo::m_NullProtoType;
      v37.m_Spell_Info.m_lpCaster = this;
      *(_DWORD *)&v37.m_Spell_Info.m_wSpellLevel = v35;
      Skill::CAddSpell<CInvincibleSpell>::operator()(&v37, this);
      v17 = this->__vftable;
      this->m_eCellLoginStatus = MASTER;
      v31 = v17->GetGID(this);
      v18 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v18, v31);
      v20 = Guild;
      if ( Guild )
      {
        LOBYTE(cTitle) = Guild::CGuild::GetTitle(Guild, this->m_dwCID);
        m_lpGameClientDispatch = this->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
        {
          v31 = (_BYTE)cTitle == 0;
          v30 = cTitle;
          qmemcpy(&v29, &v20->m_GuildRight, 0x30u);
          m_dwGold = v20->m_dwGold;
          *(_WORD *)&v29.m_aryRight[48] = *(_WORD *)&v20->m_GuildRight.m_aryRight[48];
          GameClientSendPacket::SendCharMyGuildInfo(&m_lpGameClientDispatch->m_SendStream, m_dwGold, v29, cTitle, v31);
        }
      }
      v22 = this->m_lpGameClientDispatch;
      if ( v22 )
      {
        GameClientSendPacket::SendCharCastleInfo(&v22->m_SendStream);
        GameClientSendPacket::SendCharSiegeTimeInfo(&this->m_lpGameClientDispatch->m_SendStream);
        GameClientSendPacket::SendCharCampInfo(&this->m_lpGameClientDispatch->m_SendStream);
        if ( this->GetGID(this) )
        {
          v31 = this->GetGID(this);
          v23 = Castle::CCastleMgr::GetInstance();
          CastleByGID = Castle::CCastleMgr::GetCastleByGID(v23, v31);
          if ( CastleByGID )
          {
            CastleEmblem = Castle::CCastle::GetCastleEmblem(CastleByGID);
            if ( CastleEmblem )
            {
              if ( CastleEmblem->m_cUpgradeStep )
                CCharacter::UpgradeRespawnSpeedByEmblem(
                  this,
                  CastleEmblem->m_cUpgradeType,
                  CastleEmblem->m_cUpgradeStep);
            }
          }
        }
      }
    }
    Math::Convert::Hex64ToStr(szExp, this->m_DBData.m_Info.Exp);
    v26 = this->m_lpGameClientDispatch;
    if ( v26 )
      m_dwUID = v26->m_dwUID;
    else
      m_dwUID = 0;
    _snprintf(
      szBuffer,
      0x400u,
      aUidDCid0x08x0x_3,
      this->m_dwUID,
      this->m_dwCID,
      this,
      m_dwUID,
      v26,
      this->m_DBData.m_Info.Name,
      this->m_DBData.m_Info.Level,
      szExp,
      this->m_PublicAddress.sin_addr.S_un.S_un_b.s_b1,
      this->m_PublicAddress.sin_addr.S_un.S_un_b.s_b2,
      this->m_PublicAddress.sin_addr.S_un.S_un_b.s_b3,
      this->m_PublicAddress.sin_addr.S_un.S_un_b.s_b4,
      this->m_PublicAddress.sin_port);
    CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCharacter::CharacterCellLogin", aDWorkRylSource_1, 210, szBuffer);
  }
  return 1;
}

//----- (004213C0) --------------------------------------------------------
unsigned __int8 __usercall Creature::GetCreatureType@<al>(int dwCID@<eax>)
{
  if ( dwCID < 0 )
    return 2;
  if ( (dwCID & 0x40000000) != 0 )
    return 1;
  return (dwCID & 0x10000000) != 0 ? 5 : 0;
}

//----- (004213E0) --------------------------------------------------------
void __thiscall CalculateDamageInfo::CalculateDamageInfo(
        CalculateDamageInfo *this,
        bool bForceDRC,
        float fDRC,
        __int16 nOffenceRevision,
        __int16 nMinDamage,
        __int16 nMaxDamage)
{
  this->m_bForceDRC = bForceDRC;
  this->m_fDRC = fDRC;
  this->m_nOffenceRevision = nOffenceRevision;
  this->m_nMinDamage = nMinDamage;
  this->m_nMaxDamage = nMaxDamage;
}

//----- (00421410) --------------------------------------------------------
void __thiscall CCreature::~CCreature(CCreature *this)
{
  this->__vftable = (CCreature_vtbl *)&CCreature::`vftable';
}
// 4DABA8: using guessed type void *CCreature::`vftable';

//----- (00421420) --------------------------------------------------------
CCreature *__thiscall CCreature::`scalar deleting destructor'(CCreature *this, char a2)
{
  this->__vftable = (CCreature_vtbl *)&CCreature::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4DABA8: using guessed type void *CCreature::`vftable';

//----- (00421440) --------------------------------------------------------
bool __thiscall CellPosition::MoveTo(CellPosition *this, const Position *WorldPos)
{
  double m_fPointZ; // st7
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // edi
  int v6; // ebx
  unsigned __int16 v7; // ax
  CCellManager *v8; // eax
  CCell *Cell; // eax
  unsigned __int16 v11; // [esp-Ch] [ebp-24h]
  unsigned __int16 m_wMapIndex; // [esp-8h] [ebp-20h]
  float pos; // [esp+Ch] [ebp-Ch]
  float pos_8; // [esp+14h] [ebp-4h]
  int WorldPosa; // [esp+1Ch] [ebp+4h]

  m_fPointZ = WorldPos->m_fPointZ;
  pos_8 = WorldPos->m_fPointZ;
  pos = WorldPos->m_fPointX;
  if ( this->m_wMapIndex )
  {
    m_wMapIndex = this->m_wMapIndex;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
    pos = pos - (double)VirtualArea::CVirtualArea::GetStartX(VirtualArea);
    m_fPointZ = pos_8 - (double)VirtualArea::CVirtualArea::GetStartZ(VirtualArea);
  }
  v6 = (int)(unsigned __int64)pos >> 5;
  WorldPosa = (int)(unsigned __int64)m_fPointZ >> 5;
  this->m_wInX = (unsigned __int64)((pos - (double)(32 * v6)) * 32.0);
  this->m_wInZ = (unsigned __int64)((m_fPointZ - (double)(32 * WorldPosa)) * 32.0);
  this->m_cCellZ = WorldPosa;
  v7 = this->m_wMapIndex;
  this->m_cCellX = v6;
  v11 = v7;
  v8 = CCellManager::GetInstance();
  Cell = CCellManager::GetCell(v8, v11, v6, WorldPosa);
  this->m_lpCell = Cell;
  return Cell != 0;
}

//----- (00421540) --------------------------------------------------------
void __thiscall CAggresiveCreature::CAggresiveCreature(CAggresiveCreature *this, unsigned int dwCID)
{
  this->__vftable = (CAggresiveCreature_vtbl *)&CCreature::`vftable';
  this->m_CurrentPos.m_fPointX = 0.0;
  this->m_CurrentPos.m_fPointY = 0.0;
  this->m_CurrentPos.m_fPointZ = 0.0;
  MotionInfo::MotionInfo(&this->m_MotionInfo);
  this->m_dwCID = dwCID;
  this->m_cAttackableCreatureType = 12;
  this->__vftable = (CAggresiveCreature_vtbl *)&CAggresiveCreature::`vftable';
  this->m_dwLastTime = 0;
  this->m_pParty = 0;
  CThreat::CThreat(&this->m_Threat);
  CharacterStatus::CharacterStatus(&this->m_CharacterStatus);
  CreatureStatus::CreatureStatus(&this->m_CreatureStatus);
  CSpellMgr::CSpellMgr(&this->m_SpellMgr);
  this->m_CellPos.m_lpCell = 0;
  this->m_CellPos.m_wMapIndex = 0;
  this->m_CellPos.m_cCellX = 0;
  this->m_CellPos.m_cCellZ = 0;
  this->m_CellPos.m_wInX = 0;
  this->m_CellPos.m_wInZ = 0;
  this->m_dwStatusFlag = 0;
  this->m_bLogout = 0;
  this->m_bSitDown = 0;
  this->m_Threat.m_pOwner = this;
  this->m_SpellMgr.m_pOwner = this;
  this->m_SpellMgr.m_CastingInfo.m_pOwner = this;
  this->m_SpellMgr.m_AffectedInfo.m_pOwner = this;
}
// 4DABA8: using guessed type void *CCreature::`vftable';
// 4DABD0: using guessed type void *CAggresiveCreature::`vftable';

//----- (00421620) --------------------------------------------------------
char __thiscall CAggresiveCreature::CalculateEquipDurability(CAggresiveCreature *this, unsigned __int16 wType)
{
  return 1;
}

//----- (00421630) --------------------------------------------------------
__int16 __thiscall CAggresiveCreature::EquipSkillArm(CAggresiveCreature *this, Item::EquipType::Type eEquipType)
{
  return 1;
}

//----- (00421640) --------------------------------------------------------
char __thiscall CAggresiveCreature::HasSkill(
        CAggresiveCreature *this,
        unsigned __int16 usSkillType,
        unsigned __int8 cLockCount,
        unsigned __int8 cLevel)
{
  return 1;
}

//----- (00421650) --------------------------------------------------------
unsigned __int16 __thiscall CAggresiveCreature::GetClass(CAggresiveCreature *this)
{
  return 0;
}

//----- (00421660) --------------------------------------------------------
void __thiscall CAggresiveCreature::~CAggresiveCreature(CAggresiveCreature *this)
{
  CThreat *p_m_Threat; // edi

  this->__vftable = (CAggresiveCreature_vtbl *)&CAggresiveCreature::`vftable';
  p_m_Threat = &this->m_Threat;
  CThreat::ClearAll(&this->m_Threat);
  CSpellMgr::~CSpellMgr(&this->m_SpellMgr);
  CThreat::~CThreat(p_m_Threat);
  this->__vftable = (CAggresiveCreature_vtbl *)&CCreature::`vftable';
}
// 4DABA8: using guessed type void *CCreature::`vftable';
// 4DABD0: using guessed type void *CAggresiveCreature::`vftable';

//----- (004216D0) --------------------------------------------------------
char __thiscall CAggresiveCreature::RegenHPAndMP(
        CAggresiveCreature *this,
        signed __int16 usAdditionalHP,
        signed __int16 usAdditionalMP,
        bool bAddDefaultRegenValue)
{
  unsigned __int16 m_nNowHP; // di
  signed __int16 v6; // ax
  signed __int16 v7; // si
  int v8; // eax
  int m_nNowMP; // edx
  int v10; // eax

  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  if ( !m_nNowHP )
    return 0;
  v6 = usAdditionalHP;
  if ( bAddDefaultRegenValue )
    v6 = usAdditionalHP + this->m_CreatureStatus.m_StatusInfo.m_nHPRegenAmount * (4 * this->m_bSitDown + 1);
  v7 = usAdditionalMP;
  if ( bAddDefaultRegenValue )
    v7 = this->m_CreatureStatus.m_StatusInfo.m_nMPRegenAmount * (4 * this->m_bSitDown + 1) + usAdditionalMP;
  v8 = m_nNowHP + v6;
  if ( v8 >= this->m_CreatureStatus.m_StatusInfo.m_nMaxHP )
    LOWORD(v8) = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  m_nNowMP = this->m_CreatureStatus.m_nNowMP;
  this->m_CreatureStatus.m_nNowHP = v8;
  v10 = m_nNowMP + v7;
  if ( v10 >= this->m_CreatureStatus.m_StatusInfo.m_nMaxMP )
    LOWORD(v10) = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  this->m_CreatureStatus.m_nNowMP = v10;
  return 1;
}

//----- (00421780) --------------------------------------------------------
CAggresiveCreature *__thiscall CAggresiveCreature::`scalar deleting destructor'(CAggresiveCreature *this, char a2)
{
  CAggresiveCreature::~CAggresiveCreature(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004217A0) --------------------------------------------------------
int __thiscall CAggresiveCreature::MoveTo(CAggresiveCreature *this, const Position *NewPosition, bool bSitDown)
{
  CPerformanceCheck *Instance; // eax
  bool m_bLogout; // al
  CCell *m_lpCell; // ebx
  CellPosition *p_m_CellPos; // ebp
  CCell *v9; // ebp
  CPerformanceInstrument functionInstrument; // [esp+34h] [ebp-24h] BYREF
  int v11; // [esp+54h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CAggresiveCreature::MoveTo";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CAggresiveCreature::MoveTo", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  m_bLogout = this->m_bLogout;
  v11 = 0;
  if ( m_bLogout || (this->m_dwStatusFlag & 0x3000000) != 0 )
    goto LABEL_14;
  if ( CServerSetup::GetInstance()->m_bDuelModeCheck && this->GetDuelOpponent(this) )
  {
    this->m_CurrentPos = *NewPosition;
    this->m_bSitDown = bSitDown;
    v11 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 2;
  }
  this->m_CurrentPos.m_fPointX = NewPosition->m_fPointX;
  m_lpCell = this->m_CellPos.m_lpCell;
  this->m_CurrentPos.m_fPointY = NewPosition->m_fPointY;
  this->m_CurrentPos.m_fPointZ = NewPosition->m_fPointZ;
  p_m_CellPos = &this->m_CellPos;
  this->m_bSitDown = bSitDown;
  if ( !CellPosition::MoveTo(&this->m_CellPos, &this->m_CurrentPos) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CAggresiveCreature::MoveTo",
      aDWorkRylSource_88,
      106,
      aCid0x08x_107,
      this->m_dwCID,
      this->m_CurrentPos.m_fPointX,
      this->m_CurrentPos.m_fPointY,
      this->m_CurrentPos.m_fPointZ);
  if ( p_m_CellPos->m_lpCell == m_lpCell )
  {
    v11 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 2;
  }
  if ( m_lpCell )
    CCell::DeleteCreature(m_lpCell, this->m_dwCID, 0);
  v9 = p_m_CellPos->m_lpCell;
  if ( !v9 )
  {
LABEL_14:
    v11 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 3;
  }
  else
  {
    CCell::SetCreature(v9, this->m_dwCID, (CCharacter *)this, MOVE);
    v11 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 1;
  }
}
// 4217A0: could not find valid save-restore pair for ebx
// 4217A0: could not find valid save-restore pair for ebp

//----- (004219F0) --------------------------------------------------------
int __thiscall CAggresiveCreature::CalculateLevelGap(CAggresiveCreature *this, int nOffencerLevel, int nDefenderLevel)
{
  int *p_nOffencerLevel; // eax
  int *p_nDefenderLevel; // eax

  nOffencerLevel -= nDefenderLevel;
  nDefenderLevel = -20;
  p_nOffencerLevel = &nOffencerLevel;
  if ( nOffencerLevel <= -20 )
    p_nOffencerLevel = &nDefenderLevel;
  nOffencerLevel = *p_nOffencerLevel;
  nDefenderLevel = 20;
  p_nDefenderLevel = &nDefenderLevel;
  if ( nOffencerLevel <= 20 )
    p_nDefenderLevel = &nOffencerLevel;
  return *p_nDefenderLevel;
}

//----- (00421A40) --------------------------------------------------------
double __thiscall CAggresiveCreature::CalculateLevelGapAffect(CAggresiveCreature *this, CAggresiveCreature *pDefender)
{
  char v3; // al
  int v5; // [esp-4h] [ebp-Ch]

  if ( (this->m_dwCID & 0x80000000) != 0 || (pDefender->m_dwCID & 0x80000000) != 0 )
    return fHuntingAffect[this->CalculateFixLevelGap(this, pDefender) + 20];
  v5 = pDefender->m_CreatureStatus.m_nLevel + pDefender->GetEliteBonus(pDefender);
  v3 = this->GetEliteBonus(this);
  return fPvPAffect[CAggresiveCreature::CalculateLevelGap(this, this->m_CreatureStatus.m_nLevel + v3, v5) + 20];
}

//----- (00421AB0) --------------------------------------------------------
unsigned __int16 __thiscall CAggresiveCreature::CalculateDamage(
        CAggresiveCreature *this,
        int pOffencer,
        float AddEffectInfo,
        float cDefenserJudge)
{
  CPerformanceCheck *Instance; // eax
  const CalculateDamageInfo *v6; // ebx
  bool v7; // zf
  CAggresiveCreature *v8; // esi
  unsigned int v10; // eax
  unsigned __int16 v11; // cx
  bool v12; // cc
  _LARGE_INTEGER *p_pOffencer; // ecx
  _LARGE_INTEGER *v14; // ecx
  _LARGE_INTEGER *p_m_nCriticalPercentage; // eax
  __int16 LowPart; // bx
  double v17; // st7
  __int16 v18; // bx
  __int16 v19; // bx
  double v20; // st7
  __int16 v21; // bx
  double v22; // st7
  __int16 v23; // bx
  double v24; // st7
  __int16 v25; // ax
  double v26; // st7
  double v27; // st7
  unsigned __int16 *p_m_nMaxHP; // eax
  __int16 v29; // bx
  double v30; // st7
  int m_nLevel; // esi
  float *p_cDefenserJudge; // eax
  int v33; // esi
  _LARGE_INTEGER v34; // [esp+18h] [ebp-30h] BYREF
  CAutoInstrument autofunctionInstrument; // [esp+20h] [ebp-28h]
  CPerformanceInstrument functionInstrument; // [esp+24h] [ebp-24h] BYREF
  int v37; // [esp+44h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CAggresiveCreature::CalculateDamage";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CAggresiveCreature::CalculateDamage", 0.0);
  autofunctionInstrument.m_PerformanceInstrument = &functionInstrument;
  functionInstrument.m_stopTime.QuadPart = 0LL;
  v34.QuadPart = __rdtsc();
  functionInstrument.m_startTime = v34;
  v6 = (const CalculateDamageInfo *)LODWORD(AddEffectInfo);
  v7 = *(_BYTE *)LODWORD(AddEffectInfo) == 1;
  v8 = (CAggresiveCreature *)pOffencer;
  v37 = 0;
  if ( v7 || 0.0 == *(float *)(pOffencer + 164) )
    AddEffectInfo = *(float *)(LODWORD(AddEffectInfo) + 4);
  else
    AddEffectInfo = *(float *)(LODWORD(AddEffectInfo) + 4) * *(float *)(pOffencer + 164);
  if ( this->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage <= (__int16)Math::Random::ComplexRandom(200, 0) )
  {
    v10 = v8->m_CreatureStatus.m_StatusInfo.m_nMinDamage
        + v6->m_nMinDamage
        - this->m_CreatureStatus.m_StatusInfo.m_nDefence
        + Math::Random::ComplexRandom(
            v6->m_nMaxDamage
          + v8->m_CreatureStatus.m_StatusInfo.m_nMaxDamage
          - v6->m_nMinDamage
          - v8->m_CreatureStatus.m_StatusInfo.m_nMinDamage,
            0);
    v11 = v6->m_nOffenceRevision
        + v8->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision
        - this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision;
    v34.LowPart = 200;
    pOffencer = v11 + 100;
    v12 = (__int16)(v11 + 100) < 200;
    p_pOffencer = (_LARGE_INTEGER *)&pOffencer;
    if ( !v12 )
      p_pOffencer = &v34;
    pOffencer = SLOWORD(p_pOffencer->QuadPart);
    v34.LowPart = 5;
    v14 = (_LARGE_INTEGER *)&pOffencer;
    if ( (__int16)pOffencer <= 5 )
      v14 = &v34;
    v12 = v8->m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage <= 90;
    pOffencer = (int)(v10 * SLOWORD(v14->QuadPart)) / 100;
    p_m_nCriticalPercentage = (_LARGE_INTEGER *)&v8->m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage;
    v34.LowPart = 90;
    if ( !v12 )
      p_m_nCriticalPercentage = &v34;
    LowPart = p_m_nCriticalPercentage->LowPart;
    if ( LowPart > (__int16)Math::Random::ComplexRandom(100, 0) )
    {
      pOffencer = (unsigned __int64)((double)pOffencer * 1.5);
      *(_BYTE *)LODWORD(cDefenserJudge) = 4;
    }
    v17 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
    v18 = v8->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[0]
        - this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance[0];
    pOffencer = (unsigned __int64)(v17 * (double)pOffencer * AddEffectInfo);
    v19 = 2 * v18;
    if ( v19 > 0 )
    {
      v20 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      LODWORD(cDefenserJudge) = v19;
      pOffencer += (unsigned __int64)(v20 * (double)v19);
    }
    v21 = 2
        * (v8->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[1]
         - this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance[1]);
    if ( v21 > 0 )
    {
      v22 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      LODWORD(cDefenserJudge) = v21;
      pOffencer += (unsigned __int64)(v22 * (double)v21);
    }
    v23 = 2
        * (v8->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[2]
         - this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance[2]);
    if ( v23 > 0 )
    {
      v24 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      LODWORD(cDefenserJudge) = v23;
      pOffencer += (unsigned __int64)(v24 * (double)v23);
    }
    v25 = v8->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[3]
        - this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance[3];
    if ( v25 > 0 )
    {
      cDefenserJudge = (float)v25;
      v26 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      pOffencer += (unsigned __int64)(v26 * cDefenserJudge);
      v27 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      v8->m_CreatureStatus.m_nNowHP += (unsigned __int64)(v27 * cDefenserJudge);
      p_m_nMaxHP = &v8->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      if ( v8->m_CreatureStatus.m_StatusInfo.m_nMaxHP >= v8->m_CreatureStatus.m_nNowHP )
        p_m_nMaxHP = &v8->m_CreatureStatus.m_nNowHP;
      v8->m_CreatureStatus.m_nNowHP = *p_m_nMaxHP;
    }
    v29 = 2
        * (v8->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[4]
         - this->m_CreatureStatus.m_StatusInfo.m_nAttributeResistance[4]);
    if ( v29 > 0 )
    {
      v30 = CAggresiveCreature::CalculateLevelGapAffect(v8, this);
      LODWORD(cDefenserJudge) = v29;
      pOffencer += (unsigned __int64)(v30 * (double)v29);
    }
    m_nLevel = v8->m_CreatureStatus.m_nLevel;
    LODWORD(cDefenserJudge) = 3 * m_nLevel + 100;
    AddEffectInfo = *(float *)&m_nLevel;
    pOffencer = (unsigned __int64)((double)SLODWORD(cDefenserJudge) * (double)pOffencer * 0.**********);
    LODWORD(cDefenserJudge) = (unsigned __int64)((double)m_nLevel * 0.33333334 + 0.89999998);
    p_cDefenserJudge = &cDefenserJudge;
    if ( pOffencer >= SLODWORD(cDefenserJudge) )
      p_cDefenserJudge = (float *)&pOffencer;
    v33 = *(_DWORD *)p_cDefenserJudge;
    v37 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v33;
  }
  else
  {
    *(_BYTE *)LODWORD(cDefenserJudge) = 3;
    v37 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
}

//----- (00421E70) --------------------------------------------------------
double __thiscall CAggresiveCreature::CalcDir2D(
        CAggresiveCreature *this,
        float fSrcX,
        float fSrcY,
        float fDstX,
        float fDstY)
{
  double result; // st7
  double v7; // st7
  unsigned __int8 v8; // c0
  unsigned __int8 v9; // c3
  char v10; // fps^1
  char v11; // ah
  bool v12; // c0
  bool v13; // c3
  double v14; // st7
  double v16; // st6
  unsigned __int8 v17; // c0
  unsigned __int8 v18; // c3
  float fSrcXa; // [esp+4h] [ebp+4h]
  float fDstXa; // [esp+Ch] [ebp+Ch]

  fSrcXa = fDstX - fSrcX;
  fDstXa = fDstY - fSrcY;
  if ( fSrcXa != 0.0 )
    goto LABEL_8;
  if ( fDstXa == 0.0 )
    return 0.0;
  if ( fSrcXa == 0.0 )
  {
    v7 = PI_4;
    if ( v8 | v9 )
      return v7 * 1.5;
    else
      return v7 * 0.5;
  }
  else
  {
LABEL_8:
    v11 = v10;
    v12 = fSrcXa < 0.0;
    v13 = fSrcXa == 0.0;
    if ( __SETP__(v11 & 0x44, 0) )
    {
      v14 = fSrcXa;
      if ( v12 || v13 )
        v14 = v14 * -1.0;
      v16 = fDstXa;
      if ( v17 | v18 )
        v16 = v16 * -1.0;
      result = atan2(v16 / v14, 1.0);
      if ( fSrcXa >= 0.0 )
      {
        if ( fDstXa < 0.0 )
          return PI_4 + PI_4 - result;
      }
      else if ( fDstXa < 0.0 )
      {
        return result + PI_4;
      }
      else
      {
        return PI_4 - result;
      }
    }
    else if ( v12 || v13 )
    {
      return PI_4;
    }
    else
    {
      return 0.0;
    }
  }
  return result;
}
// 421EDC: variable 'v8' is possibly undefined
// 421EDC: variable 'v9' is possibly undefined
// 421F03: variable 'v10' is possibly undefined

//----- (00421FD0) --------------------------------------------------------
int __thiscall CAggresiveCreature::CalculateLevelGap(CAggresiveCreature *this, int pDefender)
{
  int *p_pDefender; // eax
  int *v3; // eax
  int v5; // [esp+0h] [ebp-4h] BYREF

  v5 = (int)this;
  pDefender = this->m_CreatureStatus.m_nLevel - *(_DWORD *)(pDefender + 144);
  v5 = -20;
  p_pDefender = &pDefender;
  if ( pDefender <= -20 )
    p_pDefender = &v5;
  pDefender = *p_pDefender;
  v5 = 20;
  v3 = &v5;
  if ( pDefender <= 20 )
    v3 = &pDefender;
  return *v3;
}

//----- (00422020) --------------------------------------------------------
unsigned __int16 __thiscall CAggresiveCreature::ApplyDamage(
        CAggresiveCreature *this,
        __int64 attackType,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  CPerformanceCheck *Instance; // eax
  unsigned __int16 m_nNowHP; // ax
  unsigned __int16 m_nNowMP; // cx
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  int v10; // ecx
  Skill::ProtoType *v11; // eax
  CSpell *Spell; // eax
  unsigned __int16 m_wSpellLevel; // cx
  unsigned __int16 v14; // cx
  CSpell *v15; // eax
  unsigned __int16 v16; // cx
  unsigned __int16 v17; // cx
  CCharacter *v19; // edi
  unsigned __int16 v20; // ax
  unsigned __int16 v21; // bp
  unsigned __int16 v22; // ax
  unsigned __int16 v23; // ax
  unsigned __int16 v24; // ax
  CMonster *v25; // ecx
  __int16 v26; // si
  bool bProtection; // [esp+1Bh] [ebp-4Dh]
  CAffectedSpell *p_m_AffectedInfo; // [esp+1Ch] [ebp-4Ch]
  unsigned __int16 nPreviousHP; // [esp+24h] [ebp-44h]
  unsigned __int16 nPreviousMP; // [esp+28h] [ebp-40h]
  CalculateDamageInfo AddEffectInfo; // [esp+34h] [ebp-34h] BYREF
  CPerformanceInstrument functionInstrument; // [esp+44h] [ebp-24h] BYREF
  int v33; // [esp+64h] [ebp-4h]
  float wErrora; // [esp+7Ch] [ebp+14h]

  functionInstrument.m_szfunctionName = "CAggresiveCreature::ApplyDamage";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CAggresiveCreature::ApplyDamage", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  m_nNowMP = this->m_CreatureStatus.m_nNowMP;
  v33 = 0;
  nPreviousHP = m_nNowHP;
  nPreviousMP = m_nNowMP;
  bProtection = 1;
  if ( (attackType & 0x8000) == 0 )
    goto LABEL_5;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType);
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CAggresiveCreature::ApplyDamage",
      aDWorkRylSource_88,
      337,
      aCid0x08x_77,
      *(_DWORD *)(HIDWORD(attackType) + 32),
      (unsigned __int16)attackType);
LABEL_19:
    v33 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return 0;
  }
  LOWORD(v10) = WORD1(attackType);
  v11 = &SkillProtoType->m_ProtoTypes[BYTE2(attackType) >> 4];
  bProtection = v11->m_bProtection;
  if ( v11->m_bInterrupt )
  {
LABEL_5:
    CAffectedSpell::RemoveEnchantBySpellType(&this->m_SpellMgr.m_AffectedInfo, 0x2000000u);
    LOWORD(v10) = WORD1(attackType);
  }
  if ( bProtection )
  {
    p_m_AffectedInfo = &this->m_SpellMgr.m_AffectedInfo;
    Spell = CAffectedSpell::GetSpell(&this->m_SpellMgr.m_AffectedInfo, 0x12u);
    if ( Spell )
    {
      m_wSpellLevel = Spell->m_wSpellLevel;
      if ( m_wSpellLevel >= 5u )
      {
        v14 = m_wSpellLevel - 5;
        Spell->m_wSpellLevel = v14;
        if ( v14 )
        {
LABEL_12:
          *cDefenserJudge = 12;
          goto LABEL_19;
        }
      }
      else
      {
        Spell->m_wSpellLevel = 0;
      }
      CAffectedSpell::RemoveEnchantBySpellType(p_m_AffectedInfo, 0x20000u);
      goto LABEL_12;
    }
    v15 = CAffectedSpell::GetSpell(p_m_AffectedInfo, 0x17u);
    if ( v15 )
    {
      v16 = v15->m_wSpellLevel;
      if ( v16 >= 5u )
      {
        v17 = v16 - 5;
        v15->m_wSpellLevel = v17;
        if ( v17 )
        {
LABEL_18:
          *cDefenserJudge = 10;
          goto LABEL_19;
        }
      }
      else
      {
        v15->m_wSpellLevel = 0;
      }
      CAffectedSpell::RemoveEnchantBySpellType(p_m_AffectedInfo, 0x400000u);
      goto LABEL_18;
    }
    LOWORD(v10) = WORD1(attackType);
  }
  if ( (attackType & 0x8000) != 0 )
  {
    v19 = (CCharacter *)HIDWORD(attackType);
    LOBYTE(v10) = (unsigned __int8)v10 >> 4;
    if ( !(*(unsigned __int8 (__thiscall **)(_DWORD, _DWORD, int, _DWORD))(*(_DWORD *)HIDWORD(attackType) + 68))(
            HIDWORD(attackType),
            attackType,
            v10,
            *(_DWORD *)((char *)&attackType + 3)) )
      goto LABEL_19;
    v20 = Skill::CProcessTable::UseSkill(
            CSingleton<Skill::CProcessTable>::ms_pSingleton,
            (AtType)attackType,
            (CAggresiveCreature *)HIDWORD(attackType),
            this,
            cOffencerJudge,
            cDefenserJudge,
            wError);
  }
  else
  {
    wErrora = 1.0;
    if ( (_WORD)attackType )
    {
      if ( (unsigned __int16)attackType == 1 )
      {
        wErrora = 0.89999998;
      }
      else if ( (unsigned __int16)attackType == 2 )
      {
        wErrora = 1.1;
      }
    }
    else
    {
      wErrora = 0.69999999;
    }
    v19 = (CCharacter *)HIDWORD(attackType);
    if ( !(*(unsigned __int8 (__thiscall **)(_DWORD, _DWORD))(*(_DWORD *)HIDWORD(attackType) + 48))(
            HIDWORD(attackType),
            attackType) )
      goto LABEL_19;
    AddEffectInfo.m_bForceDRC = 0;
    AddEffectInfo.m_fDRC = wErrora;
    AddEffectInfo.m_nOffenceRevision = 0;
    AddEffectInfo.m_nMinDamage = 0;
    AddEffectInfo.m_nMaxDamage = 0;
    v20 = CAggresiveCreature::CalculateDamage(
            this,
            SHIDWORD(attackType),
            COERCE_FLOAT(&AddEffectInfo),
            *(float *)&cDefenserJudge);
  }
  v21 = v20;
  if ( v20 )
  {
    v22 = this->m_CreatureStatus.m_nNowHP;
    if ( v22 >= v21 )
      v22 = v21;
    CThreat::AddToThreatList(&this->m_Threat, v19, v22);
    v23 = this->m_CreatureStatus.m_nNowHP;
    if ( v23 <= v21 )
      v24 = 0;
    else
      v24 = v23 - v21;
    this->m_CreatureStatus.m_nNowHP = v24;
    if ( (this->m_dwCID & 0x80000000) != 0 )
      this->__vftable[1].GetGID(this);
    if ( (attackType & 0x8000) == 0
      && !Creature::GetCreatureType(v19->m_dwCID)
      && v25
      && !GAMELOG::LogDamagePumping(v19, v25, v21, 0) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CAggresiveCreature::ApplyDamage",
        aDWorkRylSource_88,
        454,
        aCid0x08x_18,
        v19->m_dwCID);
    }
  }
  if ( *cDefenserJudge == 5 )
  {
    v26 = this->m_CreatureStatus.m_nNowHP - nPreviousHP;
LABEL_49:
    v33 = -1;
    CPerformanceInstrument::Stop(&functionInstrument);
    return v26;
  }
  if ( *cDefenserJudge == 6 )
  {
    v26 = this->m_CreatureStatus.m_nNowMP - nPreviousMP;
    goto LABEL_49;
  }
  if ( !this->m_CreatureStatus.m_nNowHP && v19 != this )
  {
    this->Dead(this, v19);
    CThreat::ClearAll(&this->m_Threat);
  }
  v33 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
  return v21;
}
// 42221F: variable 'v10' is possibly undefined
// 42232C: variable 'v25' is possibly undefined

//----- (00422400) --------------------------------------------------------
bool __thiscall CAggresiveCreature::MultiAttack(
        CAggresiveCreature *this,
        AtType attackType,
        int nDefenderNum,
        CAggresiveCreature **ppDefenders,
        CCell *cDefenserJudges,
        Position CenterPos,
        float fDir,
        float nRange,
        float fAngle,
        char cTargetType)
{
  CAggresiveCreature **v10; // ebp
  CDuelCellManager *v12; // esi
  CCellManager *Instance; // eax
  unsigned int v15; // eax
  CCell *ConnectCell; // eax
  CSiegeObject *i; // esi
  bool v18; // bl
  CCreature::MutualType v19; // eax
  int v20; // eax
  double v21; // st7
  double v22; // st6
  int v23; // edx
  double v24; // st7
  double v25; // st7
  unsigned __int8 *v26; // ecx
  unsigned __int16 m_wMapIndex; // [esp-10h] [ebp-24h]
  unsigned int m_fPointX; // [esp-Ch] [ebp-20h]
  unsigned int m_fPointY; // [esp-8h] [ebp-1Ch]
  unsigned int m_fPointZ; // [esp-4h] [ebp-18h]
  int nDirection; // [esp+Ch] [ebp-8h] BYREF
  CCell *lpConnectCell; // [esp+10h] [ebp-4h]

  v10 = ppDefenders;
  if ( this == *ppDefenders )
  {
    LOBYTE(nDefenderNum) = 0;
    *ppDefenders = 0;
  }
  if ( CServerSetup::GetInstance()->m_bDuelModeCheck && this->GetDuelOpponent(this) )
  {
    v12 = CSingleton<CDuelCellManager>::ms_pSingleton;
    ppDefenders = (CAggresiveCreature **)this->m_dwCID;
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
      (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)CSingleton<CDuelCellManager>::ms_pSingleton,
      (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&nDirection,
      (const unsigned int *)&ppDefenders);
    if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)nDirection == v12->m_CellData._Myhead )
    {
LABEL_9:
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CAggresiveCreature::MultiAttack", aDWorkRylSource_88, 230, aCid0x08x_16);
      return 0;
    }
    ppDefenders = *(CAggresiveCreature ***)(nDirection + 16);
  }
  else
  {
    m_fPointZ = (unsigned __int64)CenterPos.m_fPointZ;
    m_fPointY = (unsigned __int64)CenterPos.m_fPointY;
    m_fPointX = (unsigned __int64)CenterPos.m_fPointX;
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    Instance = CCellManager::GetInstance();
    ppDefenders = (CAggresiveCreature **)CCellManager::GetCell(Instance, m_wMapIndex, m_fPointX, m_fPointY, m_fPointZ);
  }
  if ( !ppDefenders )
    goto LABEL_9;
  v15 = 0;
  nDirection = 0;
  while ( (unsigned __int8)nDefenderNum < 0xAu )
  {
    ConnectCell = CCell::GetConnectCell((CCell *)ppDefenders, v15);
    lpConnectCell = ConnectCell;
    if ( ConnectCell )
    {
      for ( i = (CSiegeObject *)CCell::GetFirstAggresiveCreature(ConnectCell);
            i;
            i = CCell::GetNextAggresiveCreature(lpConnectCell) )
      {
        if ( (unsigned __int8)nDefenderNum >= 0xAu )
          break;
        v18 = 0;
        if ( (i->m_dwCID & 0xD0000000) == 0 )
          v18 = *(_DWORD *)&i[1].m_MonsterInfo.m_cSkillPattern != 0;
        v19 = this->IsEnemy(this, i);
        if ( v19 != FRIENDLY )
        {
          if ( cTargetType == 2 )
          {
            if ( v19 != HOSTILITY )
              goto LABEL_24;
          }
          else if ( cTargetType != 3 || v19 )
          {
            goto LABEL_24;
          }
        }
        v18 = 1;
LABEL_24:
        if ( (i->m_dwStatusFlag & 0x20000000) != 0 )
          v18 = 1;
        v20 = 0;
        if ( (_BYTE)nDefenderNum )
        {
          while ( v10[v20] != i )
          {
            if ( ++v20 >= (unsigned __int8)nDefenderNum )
              goto LABEL_29;
          }
        }
        else
        {
LABEL_29:
          if ( !v18 )
          {
            v21 = i->m_CurrentPos.m_fPointX - CenterPos.m_fPointX;
            v22 = i->m_CurrentPos.m_fPointZ - CenterPos.m_fPointZ;
            if ( nRange * nRange >= v22 * v22 + v21 * v21 )
            {
              v24 = CAggresiveCreature::CalcDir2D(
                      this,
                      CenterPos.m_fPointX,
                      CenterPos.m_fPointZ,
                      i->m_CurrentPos.m_fPointX,
                      i->m_CurrentPos.m_fPointZ);
              v25 = v24 < fDir ? fDir - v24 : v24 - fDir;
              if ( v25 <= fAngle && i->m_CreatureStatus.m_nNowHP )
              {
                v26 = (unsigned __int8 *)cDefenserJudges;
                v10[v23] = i;
                v26[v23] = 0;
                LOBYTE(nDefenderNum) = nDefenderNum + 1;
              }
            }
          }
        }
      }
    }
    v15 = ++nDirection;
    if ( nDirection >= 9 )
      break;
  }
  if ( (unsigned __int8)nDefenderNum > 0xAu )
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "CAggresiveCreature::MultiAttack",
      aDWorkRylSource_88,
      313,
      (char *)&byte_4DAECC);
  return ((int (__thiscall *)(_DWORD, _DWORD, _DWORD, _DWORD, _DWORD))this->Attack)(
           this,
           attackType,
           nDefenderNum,
           v10,
           (unsigned __int8 *)cDefenserJudges);
}
// 422607: variable 'v23' is possibly undefined

//----- (00422690) --------------------------------------------------------
void __thiscall CPacketDispatch::~CPacketDispatch(CPacketDispatch *this)
{
  this->__vftable = (CPacketDispatch_vtbl *)&CPacketDispatch::`vftable';
}
// 4DAF54: using guessed type void *CPacketDispatch::`vftable';

//----- (004226A0) --------------------------------------------------------
CPacketDispatch *__thiscall CPacketDispatch::`vector deleting destructor'(CPacketDispatch *this, char a2)
{
  this->__vftable = (CPacketDispatch_vtbl *)&CPacketDispatch::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4DAF54: using guessed type void *CPacketDispatch::`vftable';

//----- (004226C0) --------------------------------------------------------
void __thiscall CRylServerDispatch::CRylServerDispatch(
        CRylServerDispatch *this,
        CSession *Session,
        unsigned int dwMaxProcessPacketPerPulse)
{
  this->m_Session = Session;
  this->__vftable = (CRylServerDispatch_vtbl *)&CRylServerDispatch::`vftable';
  InitializeCriticalSection(&this->m_BufferQueueLock.m_CSLock);
  CBufferQueue::CBufferQueue(&this->m_ProcessQueue);
  CSendStream::CSendStream(&this->m_SendStream, Session);
  this->m_dwMaxProcessPacketPerPulse = dwMaxProcessPacketPerPulse;
  this->m_dwFlags = 0;
}
// 4DAF68: using guessed type void *CRylServerDispatch::`vftable';

//----- (00422740) --------------------------------------------------------
INET_Addr *__thiscall CRylServerDispatch::GetRemoteAddr(CRylServerDispatch *this)
{
  return &this->m_Session->m_RemoteAddr;
}

//----- (00422750) --------------------------------------------------------
bool __thiscall CRylServerDispatch::Shutdown(CRylServerDispatch *this)
{
  return CSession::Shutdown(this->m_Session);
}

//----- (00422760) --------------------------------------------------------
void __thiscall CRylServerDispatch::CloseSession(CRylServerDispatch *this)
{
  CSession::CloseSession(this->m_Session);
}

//----- (00422770) --------------------------------------------------------
void __thiscall CRylServerDispatch::LogErrorPacket(
        CRylServerDispatch *this,
        const char *szDetailText,
        unsigned __int8 cCmd)
{
  char *v4; // eax

  v4 = inet_ntoa(*(struct in_addr *)&this->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2]);
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_ERROR,
    "CRylServerDispatch::LogErrorPacket",
    aDWorkRylSource_40,
    71,
    "SP:0x%p/DP:0x%p/IP:%15s/PktCmd:0x%02x/%s",
    this->m_Session,
    this,
    v4,
    cCmd,
    szDetailText);
}

//----- (004227C0) --------------------------------------------------------
void __thiscall CRylServerDispatch::LogErrorPacketCreation(
        CRylServerDispatch *this,
        CRylServerDispatch::CreationResult eResult)
{
  const char *v3; // ecx
  char *v4; // eax
  const char *v5; // [esp-4h] [ebp-8h]

  switch ( eResult )
  {
    case E_INVALID_STARTBIT:
      v3 = "Invalid packet startbit";
      break;
    case E_ALLOCATE_BUFFER_FAILED:
      v3 = "Allocate packetbuffer failed";
      break;
    case E_DECOMPRESS_FAILED:
      v3 = "Decompress packet failed";
      break;
    default:
      v3 = "Unknown error occured";
      break;
  }
  if ( v3 )
  {
    v5 = v3;
    v4 = inet_ntoa(*(struct in_addr *)&this->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_ERROR,
      "CRylServerDispatch::LogErrorPacket",
      aDWorkRylSource_40,
      71,
      "SP:0x%p/DP:0x%p/IP:%15s/PktCmd:0x%02x/%s",
      this->m_Session,
      this,
      v4,
      0,
      v5);
  }
}

//----- (00422830) --------------------------------------------------------
BOOL __cdecl greator_second<std::pair<unsigned long,unsigned long>>(
        const std::pair<unsigned long,unsigned long> *lhspair,
        const std::pair<unsigned long,unsigned long> *rhspair)
{
  return rhspair->second < lhspair->second;
}

//----- (00422850) --------------------------------------------------------
void __thiscall CRylServerDispatch::~CRylServerDispatch(CRylServerDispatch *this)
{
  CCSLock *p_m_BufferQueueLock; // edi

  this->__vftable = (CRylServerDispatch_vtbl *)&CRylServerDispatch::`vftable';
  p_m_BufferQueueLock = &this->m_BufferQueueLock;
  EnterCriticalSection(&this->m_BufferQueueLock.m_CSLock);
  CBufferQueue::clear(&this->m_ProcessQueue);
  LeaveCriticalSection(&p_m_BufferQueueLock->m_CSLock);
  CSendStream::~CSendStream(&this->m_SendStream);
  CBufferQueue::~CBufferQueue(&this->m_ProcessQueue);
  DeleteCriticalSection(&p_m_BufferQueueLock->m_CSLock);
  this->__vftable = (CRylServerDispatch_vtbl *)&CPacketDispatch::`vftable';
}
// 4DAF54: using guessed type void *CPacketDispatch::`vftable';
// 4DAF68: using guessed type void *CRylServerDispatch::`vftable';

//----- (004228E0) --------------------------------------------------------
int __cdecl CRylServerDispatch::CreatePacket(
        CBufferFactory *bufferFactory,
        CBufferQueue *bufferQueue,
        int lpStream_In,
        unsigned int *dwStreamSize_InOut)
{
  unsigned int v4; // ebx
  bool v5; // cf
  char *v6; // ebp
  CXORCrypt *v7; // edi
  __int16 v9; // cx
  unsigned __int16 v10; // ax
  unsigned int v11; // esi
  CBufferFactory_vtbl *v12; // edx
  int v13; // eax
  CBuffer *v14; // ebx
  int v15; // edi
  int v16; // eax
  unsigned int v17; // eax
  unsigned int dwStreamSize; // [esp+10h] [ebp-Ch]
  CXORCrypt *Crypt; // [esp+14h] [ebp-8h]
  unsigned int v20; // [esp+18h] [ebp-4h]

  v4 = *dwStreamSize_InOut;
  v5 = *dwStreamSize_InOut < 0xC;
  v6 = (char *)lpStream_In;
  v7 = CSingleton<CXORCrypt>::ms_pSingleton;
  Crypt = CSingleton<CXORCrypt>::ms_pSingleton;
  dwStreamSize = *dwStreamSize_InOut;
  lpStream_In = 0;
  if ( v5 )
  {
    *dwStreamSize_InOut -= v4;
    return 0;
  }
  while ( 1 )
  {
    CXORCrypt::DecodeHeader(v7, v6 + 1, 11, 0, 0);
    v9 = *((_WORD *)v6 + 1);
    v10 = v9 & 0x3FFF;
    if ( *v6 != -1 )
      return 1;
    v11 = v10;
    v20 = v10;
    if ( v4 < v10 )
    {
      CXORCrypt::EncodeHeader(v7, v6 + 1, 11, 0, 0);
      *dwStreamSize_InOut -= v4;
      return 0;
    }
    if ( v9 < 0 )
      CXORCrypt::DecodePacket(v7, v6 + 12, v10 - 12, *((_DWORD *)v6 + 1));
    v12 = bufferFactory->__vftable;
    if ( (v6[3] & 0x40) == 0 )
      break;
    lpStream_In = 16371;
    v13 = ((int (__stdcall *)(int))v12->Create)(0x3FFF);
    v14 = (CBuffer *)v13;
    if ( !v13 )
      return 2;
    v15 = *(_DWORD *)(v13 + 8);
    *(_DWORD *)v15 = *(_DWORD *)v6;
    *(_DWORD *)(v15 + 4) = *((_DWORD *)v6 + 1);
    *(_DWORD *)(v15 + 8) = *((_DWORD *)v6 + 2);
    if ( !CMiniLZO::Decompress(v6 + 12, (unsigned __int8 *)(v11 - 12), (char *)(v15 + 12), (unsigned int *)&lpStream_In) )
    {
      v14->bufferfactory_->Release(v14->bufferfactory_, v14);
      return 3;
    }
    v14->wr_ptr_ += lpStream_In + 12;
    *(_WORD *)(v15 + 2) = (lpStream_In + 12) | *(_WORD *)(v15 + 2) & 0xC000;
LABEL_14:
    CBufferQueue::enqueue(bufferQueue, v14, 0);
    v17 = dwStreamSize - v11;
    v6 += v11;
    v5 = dwStreamSize - v11 < 0xC;
    dwStreamSize = v17;
    v4 = v17;
    if ( v5 )
    {
      *dwStreamSize_InOut -= v17;
      return 0;
    }
    v7 = Crypt;
  }
  v16 = ((int (__stdcall *)(unsigned int))v12->Create)(v11);
  v14 = (CBuffer *)v16;
  if ( v16 )
  {
    qmemcpy(*(void **)(v16 + 8), v6, v11);
    v11 = v20;
    goto LABEL_14;
  }
  return 2;
}

//----- (00422AB0) --------------------------------------------------------
char __thiscall CRylServerDispatch::ParsePacket(
        CRylServerDispatch *this,
        char *const lpStream_In,
        unsigned int *dwStreamSize_InOut)
{
  CBufferFactory *m_lpBufferFactory; // edi
  CRylServerDispatch::CreationResult v5; // eax
  CBufferQueue bufferQueue; // [esp+8h] [ebp-24h] BYREF
  int v8; // [esp+28h] [ebp-4h]

  m_lpBufferFactory = this->m_Session->m_SessionPolicy->m_lpBufferFactory;
  CBufferQueue::CBufferQueue(&bufferQueue);
  v8 = 0;
  v5 = CRylServerDispatch::CreatePacket(m_lpBufferFactory, &bufferQueue, (int)lpStream_In, dwStreamSize_InOut);
  if ( v5 )
  {
    CRylServerDispatch::LogErrorPacketCreation(this, v5);
    v8 = -1;
    CBufferQueue::~CBufferQueue(&bufferQueue);
    return 0;
  }
  else
  {
    EnterCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    LOBYTE(v8) = 1;
    CBufferQueue::splice(&this->m_ProcessQueue, &bufferQueue, 0);
    LeaveCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    v8 = -1;
    CBufferQueue::~CBufferQueue(&bufferQueue);
    return 1;
  }
}

//----- (00422B80) --------------------------------------------------------
char __thiscall CRylServerDispatch::Dispatch(CRylServerDispatch *this)
{
  unsigned int m_dwFlags; // eax
  CBuffer *v4; // eax
  CBuffer *v5; // ebp
  PktBase *rd_ptr; // edi
  CPacketStatistics *Instance; // eax
  bool v8; // bl
  unsigned __int8 m_Cmd; // [esp-Ch] [ebp-5Ch]
  unsigned int v10; // [esp-8h] [ebp-58h]
  unsigned int dwCount; // [esp+Ch] [ebp-44h]
  CBufferQueue ProcessQueue; // [esp+14h] [ebp-3Ch] BYREF
  CBufferQueue Processed; // [esp+2Ch] [ebp-24h] BYREF
  int v14; // [esp+4Ch] [ebp-4h]

  CBufferQueue::CBufferQueue(&ProcessQueue);
  v14 = 0;
  CBufferQueue::CBufferQueue(&Processed);
  EnterCriticalSection(&this->m_BufferQueueLock.m_CSLock);
  m_dwFlags = this->m_dwFlags;
  LOBYTE(v14) = 2;
  if ( (m_dwFlags & 1) != 0 )
  {
    LeaveCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    LOBYTE(v14) = 0;
    CBufferQueue::~CBufferQueue(&Processed);
    v14 = -1;
    CBufferQueue::~CBufferQueue(&ProcessQueue);
    return 1;
  }
  else
  {
    this->m_dwFlags = m_dwFlags | 1;
    CBufferQueue::splice(&ProcessQueue, &this->m_ProcessQueue, 0);
    LOBYTE(v14) = 1;
    LeaveCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    for ( dwCount = 0; dwCount < this->m_dwMaxProcessPacketPerPulse; ++dwCount )
    {
      v4 = CBufferQueue::dequeue(&ProcessQueue);
      v5 = v4;
      if ( !v4 )
        break;
      rd_ptr = (PktBase *)v4->rd_ptr_;
      v10 = rd_ptr->m_Len & 0x3FFF;
      m_Cmd = rd_ptr->m_Cmd;
      Instance = CPacketStatistics::GetInstance();
      CPacketStatistics::AddRecvPacket(Instance, m_Cmd, v10);
      v8 = this->DispatchPacket(this, rd_ptr);
      CBufferQueue::enqueue(&Processed, v5, 0);
      if ( !v8 )
      {
        LOBYTE(v14) = 0;
        CBufferQueue::~CBufferQueue(&Processed);
        v14 = -1;
        CBufferQueue::~CBufferQueue(&ProcessQueue);
        return 0;
      }
    }
    if ( ProcessQueue.m_bufferNum )
      this->ProcessTooManyPacket(this, &ProcessQueue);
    EnterCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    if ( ProcessQueue.m_bufferNum )
    {
      EnterCriticalSection(&this->m_BufferQueueLock.m_CSLock);
      LOBYTE(v14) = 4;
      CBufferQueue::splice(&this->m_ProcessQueue, &ProcessQueue, 1);
      LeaveCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    }
    this->m_dwFlags &= ~1u;
    LeaveCriticalSection(&this->m_BufferQueueLock.m_CSLock);
    LOBYTE(v14) = 0;
    CBufferQueue::~CBufferQueue(&Processed);
    v14 = -1;
    CBufferQueue::~CBufferQueue(&ProcessQueue);
    return 1;
  }
}

//----- (00422D60) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        int _Hole,
        int _Top,
        std::pair<unsigned long,unsigned long> _Val,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  int v5; // edi
  int i; // esi
  std::pair<unsigned long,unsigned long> *v7; // ebx
  unsigned int second; // ecx

  v5 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v5; i = (i - 1) / 2 )
  {
    v7 = &_First._Myptr[i];
    if ( !_Pred(v7, &_Val) )
      break;
    _First._Myptr[v5].first = v7->first;
    _First._Myptr[v5].second = v7->second;
    v5 = i;
  }
  second = _Val.second;
  _First._Myptr[v5].first = _Val.first;
  _First._Myptr[v5].second = second;
}

//----- (00422DD0) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last)
{
  std::pair<unsigned long,unsigned long> *Myptr; // ebx
  int v4; // esi
  int v5; // eax
  int v6; // edi
  int v7; // edx
  std::pair<unsigned long,unsigned long> *v8; // edi
  unsigned int first; // ebp
  std::pair<unsigned long,unsigned long> *v10; // edx
  std::vector<std::pair<unsigned long,unsigned long>>::iterator *p_First; // eax
  std::pair<unsigned long,unsigned long> *v12; // ecx
  int v13; // eax
  std::pair<unsigned long,unsigned long> **v14; // eax
  unsigned int second; // ecx
  bool v16; // zf
  std::pair<unsigned long,unsigned long> *v17; // eax
  std::pair<unsigned long,unsigned long> *v18; // [esp+10h] [ebp-10h] BYREF
  std::pair<unsigned long,unsigned long> *v19; // [esp+14h] [ebp-Ch] BYREF
  std::pair<unsigned long,unsigned long> _Holeval; // [esp+18h] [ebp-8h]

  Myptr = _Last._Myptr;
  v4 = _Mid._Myptr - _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  v6 = v4;
  if ( v4 )
  {
    do
    {
      v7 = v5 % v6;
      v5 = v6;
      v6 = v7;
    }
    while ( v7 );
  }
  if ( v5 < _Last._Myptr - _First._Myptr && v5 > 0 )
  {
    v8 = &_First._Myptr[v5];
    _Mid._Myptr = (std::pair<unsigned long,unsigned long> *)v5;
    do
    {
      first = v8->first;
      _Holeval.second = v8->second;
      v10 = v8;
      if ( &v8[v4] == Myptr )
      {
        p_First = &_First;
      }
      else
      {
        _Last._Myptr = &v8[v4];
        p_First = &_Last;
      }
      v12 = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          v10->first = v12->first;
          v10->second = v12->second;
          v13 = Myptr - v12;
          v10 = v12;
          if ( v4 >= v13 )
          {
            v19 = &_First._Myptr[v4 + 0x1FFFFFFF * v13];
            v14 = &v19;
          }
          else
          {
            v18 = &v12[v4];
            v14 = &v18;
          }
          v12 = *v14;
        }
        while ( *v14 != v8 );
      }
      second = _Holeval.second;
      --v8;
      v17 = (std::pair<unsigned long,unsigned long> *)((char *)_Mid._Myptr - 1);
      v16 = _Mid._Myptr == (std::pair<unsigned long,unsigned long> *)1;
      v10->first = first;
      v10->second = second;
      _Mid._Myptr = v17;
    }
    while ( !v16 );
  }
}

//----- (00422EB0) --------------------------------------------------------
CRylServerDispatch *__thiscall CRylServerDispatch::`vector deleting destructor'(CRylServerDispatch *this, char a2)
{
  CRylServerDispatch::~CRylServerDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00422ED0) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        std::pair<unsigned long,unsigned long> *_First,
        unsigned int _Count,
        const std::pair<unsigned long,unsigned long> *_Val)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (00422F00) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        int _Hole,
        int _Bottom,
        std::pair<unsigned long,unsigned long> _Val,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  int v5; // ebx
  int v6; // esi
  bool i; // zf

  v5 = _Hole;
  v6 = 2 * _Hole + 2;
  for ( i = v6 == _Bottom; v6 < _Bottom; i = v6 == _Bottom )
  {
    if ( _Pred(&_First._Myptr[v6], &_First._Myptr[v6 - 1]) )
      --v6;
    _First._Myptr[v5].first = _First._Myptr[v6].first;
    _First._Myptr[v5].second = _First._Myptr[v6].second;
    v5 = v6;
    v6 = 2 * v6 + 2;
  }
  if ( i )
  {
    _First._Myptr[v5].first = _First._Myptr[_Bottom - 1].first;
    _First._Myptr[v5].second = _First._Myptr[_Bottom - 1].second;
    v5 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
    _First,
    v5,
    _Hole,
    _Val,
    _Pred);
}

//----- (00422F90) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  int v3; // esi
  std::pair<unsigned long,unsigned long> *v4; // edi
  unsigned int second; // ecx
  unsigned int first; // edx

  v3 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v3 > 0 )
  {
    v4 = &_First._Myptr[v3];
    do
    {
      second = v4[-1].second;
      first = v4[-1].first;
      --v4;
      std::_Adjust_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        _First,
        --v3,
        _Last._Myptr - _First._Myptr,
        (std::pair<unsigned long,unsigned long>)__PAIR64__(second, first),
        _Pred);
    }
    while ( v3 > 0 );
  }
}

//----- (00422FE0) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  unsigned int first; // eax
  unsigned int second; // eax
  unsigned int v6; // eax
  unsigned int v7; // eax
  unsigned int v8; // eax
  unsigned int v9; // eax

  if ( _Pred(_Mid._Myptr, _First._Myptr) )
  {
    first = _Mid._Myptr->first;
    _Mid._Myptr->first = _First._Myptr->first;
    _First._Myptr->first = first;
    second = _Mid._Myptr->second;
    _Mid._Myptr->second = _First._Myptr->second;
    _First._Myptr->second = second;
  }
  if ( _Pred(_Last._Myptr, _Mid._Myptr) )
  {
    v6 = _Last._Myptr->first;
    _Last._Myptr->first = _Mid._Myptr->first;
    _Mid._Myptr->first = v6;
    v7 = _Last._Myptr->second;
    _Last._Myptr->second = _Mid._Myptr->second;
    _Mid._Myptr->second = v7;
  }
  if ( _Pred(_Mid._Myptr, _First._Myptr) )
  {
    v8 = _Mid._Myptr->first;
    _Mid._Myptr->first = _First._Myptr->first;
    _First._Myptr->first = v8;
    v9 = _Mid._Myptr->second;
    _Mid._Myptr->second = _First._Myptr->second;
    _First._Myptr->second = v9;
  }
}

//----- (00423060) --------------------------------------------------------
std::pair<unsigned long,unsigned long> *__thiscall std::vector<std::pair<enum eStdFunc,int>>::_Ufill(
        std::vector<std::pair<unsigned long,unsigned long>> *this,
        std::pair<unsigned long,unsigned long> *_Ptr,
        unsigned int _Count,
        const std::pair<unsigned long,unsigned long> *_Val)
{
  std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (00423090) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  std::pair<unsigned long,unsigned long> *Myptr; // ebx
  std::pair<unsigned long,unsigned long> *i; // esi
  const std::pair<unsigned long,unsigned long> *v5; // edi
  std::vector<std::pair<unsigned long,unsigned long>>::iterator v6; // ebx

  Myptr = _First._Myptr;
  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      if ( _Pred(i, Myptr) )
      {
        if ( Myptr != i && i != &i[1] )
          std::_Rotate<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>>(
            (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
            (std::vector<std::pair<unsigned long,unsigned long>>::iterator)i,
            (std::vector<std::pair<unsigned long,unsigned long>>::iterator)&i[1]);
      }
      else
      {
        v5 = i - 1;
        if ( _Pred(i, i - 1) )
        {
          do
            v6._Myptr = (std::pair<unsigned long,unsigned long> *)v5--;
          while ( _Pred(i, v5) );
          if ( v6._Myptr != i )
            std::_Rotate<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>>(
              v6,
              (std::vector<std::pair<unsigned long,unsigned long>>::iterator)i,
              (std::vector<std::pair<unsigned long,unsigned long>>::iterator)&i[1]);
          Myptr = _First._Myptr;
        }
      }
    }
  }
}

//----- (00423130) --------------------------------------------------------
void __cdecl std::_Median<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  int v4; // eax
  int v5; // eax
  unsigned int v6; // esi
  std::pair<unsigned long,unsigned long> *v7; // edx
  unsigned int v8; // edi
  std::pair<unsigned long,unsigned long> *v9; // [esp-40h] [ebp-40h]
  std::pair<unsigned long,unsigned long> *_Firsta; // [esp+4h] [ebp+4h]
  std::pair<unsigned long,unsigned long> *_Lasta; // [esp+Ch] [ebp+Ch]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      _First,
      _Mid,
      _Last,
      _Pred);
  }
  else
  {
    v5 = (v4 + 1) / 8;
    v6 = 16 * v5;
    v7 = &_First._Myptr[2 * v5];
    v8 = 8 * v5;
    _Firsta = &_First._Myptr[v5];
    std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      _First,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)_Firsta,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v7,
      _Pred);
    std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)&_Mid._Myptr[v8 / 0xFFFFFFF8],
      _Mid,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)&_Mid._Myptr[v8 / 8],
      _Pred);
    v9 = &_Last._Myptr[v6 / 0xFFFFFFF8];
    _Lasta = &_Last._Myptr[v8 / 0xFFFFFFF8];
    std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v9,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)_Lasta,
      _Last,
      _Pred);
    std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)_Firsta,
      _Mid,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)_Lasta,
      _Pred);
  }
}

//----- (004231D0) --------------------------------------------------------
std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> *__cdecl std::_Unguarded_partition<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> *result,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  std::pair<unsigned long,unsigned long> *v4; // esi
  std::pair<unsigned long,unsigned long> *v5; // ebp
  std::pair<unsigned long,unsigned long> *v6; // ebx
  std::pair<unsigned long,unsigned long> *v7; // edi
  unsigned int first; // ecx
  unsigned int second; // ecx
  bool v10; // zf
  std::pair<unsigned long,unsigned long> *v11; // ebx
  unsigned int v12; // eax
  unsigned int v13; // eax
  bool v14; // cf
  unsigned int v15; // eax
  unsigned int v16; // eax
  std::pair<unsigned long,unsigned long> *v17; // eax
  unsigned int v18; // eax
  unsigned int v19; // eax
  unsigned int v20; // eax
  unsigned int v21; // edx
  unsigned int v22; // eax
  unsigned int v23; // ecx
  unsigned int v24; // ecx
  std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> *v25; // eax
  const std::pair<unsigned long,unsigned long> *_Glast; // [esp+10h] [ebp-8h]
  std::pair<unsigned long,unsigned long> *_Glasta; // [esp+10h] [ebp-8h]
  unsigned int v28; // [esp+14h] [ebp-4h]
  unsigned int v29; // [esp+14h] [ebp-4h]

  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
    _First,
    (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v4,
    (std::vector<std::pair<unsigned long,unsigned long>>::iterator)&_Last._Myptr[-1],
    _Pred);
  v5 = v4 + 1;
  if ( _First._Myptr < v4 )
  {
    do
    {
      _Glast = v4 - 1;
      if ( _Pred(v4 - 1, v4) )
        break;
      if ( _Pred(v4, _Glast) )
        break;
      --v4;
    }
    while ( _First._Myptr < _Glast );
  }
  for ( ; v5 < _Last._Myptr; ++v5 )
  {
    if ( _Pred(v5, v4) )
      break;
    if ( _Pred(v4, v5) )
      break;
  }
  v6 = v4;
  v7 = v5;
  _Glasta = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v7 < _Last._Myptr; ++v7 )
      {
        if ( !_Pred(v4, v7) )
        {
          if ( _Pred(v7, v4) )
            break;
          first = v5->first;
          v5->first = v7->first;
          v7->first = first;
          second = v5->second;
          v5->second = v7->second;
          ++v5;
          v7->second = second;
        }
      }
      v10 = v6 == _First._Myptr;
      if ( v6 > _First._Myptr )
      {
        v11 = v6 - 1;
        do
        {
          if ( !_Pred(v11, v4) )
          {
            if ( _Pred(v4, v11) )
              break;
            v12 = v4[-1].first;
            --v4;
            v4->first = v11->first;
            v11->first = v12;
            v13 = v4->second;
            v4->second = v11->second;
            v11->second = v13;
          }
          --v11;
          v14 = _First._Myptr < &_Glasta[-1];
          --_Glasta;
        }
        while ( v14 );
        v6 = _Glasta;
        v10 = _Glasta == _First._Myptr;
      }
      if ( v10 )
        break;
      _Glasta = --v6;
      if ( v7 == _Last._Myptr )
      {
        if ( v6 != --v4 )
        {
          v18 = v6->first;
          v6->first = v4->first;
          v4->first = v18;
          v19 = v6->second;
          v6->second = v4->second;
          v4->second = v19;
        }
        v20 = v4->first;
        v21 = v5[-1].first;
        --v5;
        v4->first = v21;
        v5->first = v20;
        v22 = v4->second;
        v4->second = v5->second;
        v5->second = v22;
      }
      else
      {
        v23 = v7->first;
        v7->first = v6->first;
        v6->first = v23;
        v24 = v7->second;
        v7->second = v6->second;
        ++v7;
        v6->second = v24;
      }
    }
    if ( v7 == _Last._Myptr )
      break;
    if ( v5 != v7 )
    {
      v15 = v4->first;
      v4->first = v5->first;
      v5->first = v15;
      v16 = v4->second;
      v4->second = v5->second;
      v5->second = v16;
    }
    v17 = v7;
    v28 = v4->first;
    v4->first = v7->first;
    v7->first = v28;
    v29 = v4->second;
    v4->second = v7->second;
    ++v5;
    ++v7;
    ++v4;
    v17->second = v29;
  }
  v25 = result;
  result->first._Myptr = v4;
  result->second._Myptr = v5;
  return v25;
}

//----- (004233E0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  int i; // esi
  std::pair<unsigned long,unsigned long> v4; // [esp-10h] [ebp-18h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 3 > 1; i -= 8 )
  {
    v4 = *(std::pair<unsigned long,unsigned long> *)((char *)&_First._Myptr[-1] + i);
    *(unsigned int *)((char *)&_First._Myptr[-1].first + i) = _First._Myptr->first;
    *(_DWORD *)((char *)_First._Myptr + i - 4) = _First._Myptr->second;
    std::_Adjust_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      _First,
      0,
      (i - 8) >> 3,
      v4,
      _Pred);
  }
}

//----- (00423440) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _First,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last,
        int _Ideal,
        bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))
{
  std::pair<unsigned long,unsigned long> *Myptr; // ebx
  std::pair<unsigned long,unsigned long> *v5; // edi
  int v6; // eax
  std::pair<unsigned long,unsigned long> *v8; // ebp
  std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Last._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v5,
        _Pred);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        &_Mid,
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v5,
        _Pred);
      v8 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFF8) >= (int)(((char *)v5
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFF8) )
      {
        std::_Sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
          _Mid.second,
          (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v5,
          _Ideal,
          _Pred);
        v5 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
          (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          _Pred);
        Myptr = v8;
      }
      v6 = v5 - Myptr;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v5 - (char *)Myptr) & 0xFFFFFFF8) > 8 )
      std::_Make_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v5,
        _Pred);
    std::sort_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Myptr,
      (std::vector<std::pair<unsigned long,unsigned long>>::iterator)v5,
      _Pred);
  }
}
// 4234F6: conditional instruction was optimized away because eax.4>=21

//----- (00423530) --------------------------------------------------------
void __thiscall __noreturn std::vector<std::pair<unsigned long,unsigned long>>::_Xlen(
        std::vector<std::pair<unsigned long,unsigned long>> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004235A0) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,unsigned long>>::_Insert_n(
        std::vector<std::pair<unsigned long,unsigned long>> *this,
        std::vector<std::pair<unsigned long,unsigned long>>::iterator _Where,
        unsigned int _Count,
        const std::pair<unsigned long,unsigned long> *_Val)
{
  unsigned int second; // edx
  std::pair<unsigned long,unsigned long> *Myfirst; // ecx
  unsigned int v7; // eax
  int v9; // edx
  int v10; // edx
  unsigned int v11; // eax
  int v12; // edx
  int v13; // eax
  std::pair<unsigned long,unsigned long> *v14; // edi
  std::pair<unsigned long,unsigned long> *v15; // ecx
  int v16; // eax
  int v17; // ebx
  std::pair<unsigned long,unsigned long> *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  std::pair<unsigned long,unsigned long> *v22; // ebx
  std::pair<unsigned long,unsigned long> *v23; // ebx
  std::pair<unsigned long,unsigned long> *v24; // [esp-18h] [ebp-40h]
  std::pair<unsigned long,unsigned long> *v25; // [esp-Ch] [ebp-34h]
  unsigned int v26; // [esp-8h] [ebp-30h]
  int v27; // [esp+0h] [ebp-28h] BYREF
  std::pair<unsigned long,unsigned long> _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  std::pair<unsigned long,unsigned long> *_Newvec; // [esp+14h] [ebp-14h]
  int *v30; // [esp+18h] [ebp-10h]
  int v31; // [esp+24h] [ebp-4h]
  std::vector<std::pair<unsigned long,unsigned long>>::iterator _Wherea; // [esp+30h] [ebp+8h]
  unsigned int _Counta; // [esp+34h] [ebp+Ch]
  std::pair<unsigned long,unsigned long> *_Valb; // [esp+38h] [ebp+10h]
  std::pair<unsigned long,unsigned long> *_Vala; // [esp+38h] [ebp+10h]

  second = _Val->second;
  _Tmp.first = _Val->first;
  Myfirst = this->_Myfirst;
  v30 = &v27;
  _Tmp.second = second;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x1FFFFFFF - v9 < _Count )
      std::vector<std::pair<unsigned long,unsigned long>>::_Xlen(this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = this->_Mylast;
      v20 = Mylast - _Where._Myptr < _Count;
      v21 = 8 * _Count;
      _Wherea._Myptr = (std::pair<unsigned long,unsigned long> *)(8 * _Count);
      _Vala = Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[v21 / 8]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = this->_Mylast;
        v31 = 2;
        std::vector<std::pair<enum eStdFunc,int>>::_Ufill(this, v25, v26, &_Tmp);
        v22 = (std::pair<unsigned long,unsigned long> *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          _Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)v22 - (char *)_Wherea._Myptr),
          &_Tmp);
      }
      else
      {
        v23 = &Mylast[v21 / 0xFFFFFFF8];
        this->_Mylast = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                          &Mylast[v21 / 0xFFFFFFF8],
                          Mylast,
                          Mylast);
        std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(_Where._Myptr, v23, _Vala);
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          _Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          &_Tmp);
      }
    }
    else
    {
      if ( 0x1FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      _Counta = v11;
      v14 = (std::pair<unsigned long,unsigned long> *)operator new((tagHeader *)(8 * v11));
      v24 = this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Valb = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                v24,
                _Where._Myptr,
                v14);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        _Valb,
        _Count,
        &_Tmp);
      std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        _Where._Myptr,
        this->_Mylast,
        &_Valb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[_Counta];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (004237F0) --------------------------------------------------------
void __thiscall std::vector<std::pair<unsigned long,unsigned long>>::reserve(
        std::vector<std::pair<unsigned long,unsigned long>> *this,
        unsigned int _Count)
{
  std::pair<unsigned long,unsigned long> *Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  std::pair<unsigned long,unsigned long> *v6; // edi
  std::pair<unsigned long,unsigned long> *v7; // eax
  std::pair<unsigned long,unsigned long> *v8; // [esp-18h] [ebp-38h]
  std::pair<unsigned long,unsigned long> *Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  std::pair<unsigned long,unsigned long> *_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x1FFFFFFF )
    std::vector<std::pair<unsigned long,unsigned long>>::_Xlen(this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(8 * _Count);
    v6 = (std::pair<unsigned long,unsigned long> *)operator new(_Counta);
    Mylast = this->_Mylast;
    v8 = this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
      v8,
      Mylast,
      v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (std::pair<unsigned long,unsigned long> *)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (004238C0) --------------------------------------------------------
void __thiscall CRylServerDispatch::ProcessTooManyPacket(CRylServerDispatch *this, CBufferQueue *bufferQueue)
{
  int v2; // ebp
  CBuffer *m_lpHead; // edi
  std::pair<unsigned long,unsigned long> *Mylast; // esi
  std::pair<unsigned long,unsigned long> *i; // ebx
  unsigned __int8 v6; // cl
  std::pair<unsigned long,unsigned long> *v7; // eax
  std::pair<unsigned long,unsigned long> *j; // edi
  int v9; // eax
  const void **v10; // esi
  char *v11; // eax
  unsigned int m_dwMaxProcessPacketPerPulse; // [esp-8h] [ebp-C44h]
  unsigned int m_bufferNum; // [esp-4h] [ebp-C40h]
  unsigned int v14; // [esp-4h] [ebp-C40h]
  std::vector<std::pair<unsigned long,unsigned long>> statistics; // [esp+10h] [ebp-C2Ch] BYREF
  std::_Nonscalar_ptr_iterator_tag __formal[4]; // [esp+20h] [ebp-C1Ch]
  std::pair<unsigned long,unsigned long> _Val; // [esp+24h] [ebp-C18h] BYREF
  char szBuffer[3072]; // [esp+2Ch] [ebp-C10h] BYREF
  int v19; // [esp+C38h] [ebp-4h]

  m_bufferNum = bufferQueue->m_bufferNum;
  m_dwMaxProcessPacketPerPulse = this->m_dwMaxProcessPacketPerPulse;
  *(_DWORD *)__formal = this;
  v2 = _snprintf(
         szBuffer,
         0xC00u,
         "Processed:%d/Remain:%d/Remain packets(cmd:num)/",
         m_dwMaxProcessPacketPerPulse,
         m_bufferNum);
  memset(&statistics._Myfirst, 0, 12);
  v14 = bufferQueue->m_bufferNum;
  v19 = 0;
  std::vector<std::pair<unsigned long,unsigned long>>::reserve(&statistics, v14);
  m_lpHead = bufferQueue->m_lpHead;
  Mylast = statistics._Mylast;
  for ( i = statistics._Myfirst; m_lpHead; m_lpHead = m_lpHead->next_ )
  {
    v6 = m_lpHead->rd_ptr_[1];
    v7 = i;
    if ( i != Mylast )
    {
      while ( v7->first != v6 )
      {
        if ( ++v7 == Mylast )
          goto LABEL_7;
      }
      ++v7->second;
      if ( v7 != Mylast )
        continue;
    }
LABEL_7:
    _Val.first = v6;
    _Val.second = 1;
    if ( i && Mylast - i < (unsigned int)(statistics._Myend - i) )
    {
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        Mylast++,
        1u,
        &_Val);
      statistics._Mylast = Mylast;
    }
    else
    {
      std::vector<std::pair<unsigned long,unsigned long>>::_Insert_n(
        &statistics,
        (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Mylast,
        1u,
        &_Val);
      Mylast = statistics._Mylast;
      i = statistics._Myfirst;
    }
  }
  std::_Sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(
    (std::vector<std::pair<unsigned long,unsigned long>>::iterator)i,
    (std::vector<std::pair<unsigned long,unsigned long>>::iterator)Mylast,
    Mylast - i,
    (bool (__cdecl *)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *))greator_second<std::pair<unsigned long,unsigned long>>);
  for ( j = i; j != Mylast; ++j )
  {
    v9 = _snprintf(&szBuffer[v2], 3072 - v2, "0x%02x:%5d/", j->first, j->second);
    if ( v9 > 0 )
      v2 += v9;
  }
  v10 = *(const void ***)__formal;
  v11 = inet_ntoa(*(struct in_addr *)(*(_DWORD *)(*(_DWORD *)__formal + 4) + 48));
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_ERROR,
    "CRylServerDispatch::LogErrorPacket",
    aDWorkRylSource_40,
    71,
    "SP:0x%p/DP:0x%p/IP:%15s/PktCmd:0x%02x/%s",
    v10[1],
    v10,
    v11,
    0,
    szBuffer);
  if ( i )
    operator delete(i);
}

//----- (00423AB0) --------------------------------------------------------
int __thiscall CGameClientDispatchTable::Initialize(CGameClientDispatchTable *this)
{
  int result; // eax

  this->m_ProcessTable[17].m_dwCommand = 17;
  this->m_ProcessTable[17].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharTakeItem;
  this->m_ProcessTable[18].m_dwCommand = 18;
  this->m_ProcessTable[18].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSwapItem;
  this->m_ProcessTable[19].m_dwCommand = 19;
  this->m_ProcessTable[19].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharRepairItem;
  this->m_ProcessTable[159].m_dwCommand = 159;
  this->m_ProcessTable[159].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharRepairAllItem;
  this->m_ProcessTable[20].m_dwCommand = 20;
  this->m_ProcessTable[20].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharUseItem;
  this->m_ProcessTable[21].m_dwCommand = 21;
  this->m_ProcessTable[21].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharTradeItem;
  this->m_ProcessTable[52].m_dwCommand = 52;
  this->m_ProcessTable[52].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharPickUp;
  this->m_ProcessTable[53].m_dwCommand = 53;
  this->m_ProcessTable[53].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharPullDown;
  this->m_ProcessTable[63].m_dwCommand = 63;
  this->m_ProcessTable[63].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSplitItem;
  this->m_ProcessTable[71].m_dwCommand = 71;
  this->m_ProcessTable[71].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharTakeGold;
  this->m_ProcessTable[92].m_dwCommand = 92;
  this->m_ProcessTable[92].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharDepositCmd;
  this->m_ProcessTable[114].m_dwCommand = 114;
  this->m_ProcessTable[114].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharAutoRouting;
  this->m_ProcessTable[57].m_dwCommand = 57;
  this->m_ProcessTable[57].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharInstallSocket;
  this->m_ProcessTable[161].m_dwCommand = 161;
  this->m_ProcessTable[161].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharItemChemical;
  this->m_ProcessTable[60].m_dwCommand = 60;
  this->m_ProcessTable[60].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharUpgradeItem;
  this->m_ProcessTable[16].m_dwCommand = 16;
  this->m_ProcessTable[16].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))CConsoleCMDSingleton<CCMDReloadSetup>::Destroy;
  this->m_ProcessTable[24].m_dwCommand = 24;
  this->m_ProcessTable[24].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSkillErase;
  this->m_ProcessTable[14].m_dwCommand = 14;
  this->m_ProcessTable[14].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharAttack;
  this->m_ProcessTable[66].m_dwCommand = 66;
  this->m_ProcessTable[66].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSwitchHand;
  this->m_ProcessTable[10].m_dwCommand = 10;
  this->m_ProcessTable[10].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharRespawn;
  this->m_ProcessTable[132].m_dwCommand = 132;
  this->m_ProcessTable[132].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharRespawnWaitQueue;
  this->m_ProcessTable[12].m_dwCommand = 12;
  this->m_ProcessTable[12].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharMoveUpdate;
  this->m_ProcessTable[84].m_dwCommand = 84;
  this->m_ProcessTable[84].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharDuelCmd;
  this->m_ProcessTable[112].m_dwCommand = 112;
  this->m_ProcessTable[112].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharPeaceMode;
  this->m_ProcessTable[124].m_dwCommand = 124;
  this->m_ProcessTable[124].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSummonCmd;
  this->m_ProcessTable[11].m_dwCommand = 11;
  this->m_ProcessTable[11].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharMoveEx;
  this->m_ProcessTable[25].m_dwCommand = 25;
  this->m_ProcessTable[25].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharClassUpgrade;
  this->m_ProcessTable[27].m_dwCommand = 27;
  this->m_ProcessTable[27].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharIncreasePoint;
  this->m_ProcessTable[116].m_dwCommand = 116;
  this->m_ProcessTable[116].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStateRedistribution;
  this->m_ProcessTable[148].m_dwCommand = 148;
  this->m_ProcessTable[148].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStatusRetrain;
  this->m_ProcessTable[33].m_dwCommand = 33;
  this->m_ProcessTable[33].m_fnProcess = GameClientParsePacket::ParseCharExchangeCmd;
  this->m_ProcessTable[31].m_dwCommand = 31;
  this->m_ProcessTable[31].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharPartyCmd;
  this->m_ProcessTable[81].m_dwCommand = 81;
  this->m_ProcessTable[81].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharPartyFind;
  this->m_ProcessTable[94].m_dwCommand = 94;
  this->m_ProcessTable[94].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStallOpen;
  this->m_ProcessTable[95].m_dwCommand = 95;
  this->m_ProcessTable[95].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStallRegisterItem;
  this->m_ProcessTable[96].m_dwCommand = 96;
  this->m_ProcessTable[96].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStallEnter;
  this->m_ProcessTable[100].m_dwCommand = 100;
  this->m_ProcessTable[100].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharFriendAdd;
  this->m_ProcessTable[101].m_dwCommand = 101;
  this->m_ProcessTable[101].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharFriendRemove;
  this->m_ProcessTable[102].m_dwCommand = 102;
  this->m_ProcessTable[102].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharFriendEtc;
  this->m_ProcessTable[136].m_dwCommand = 136;
  this->m_ProcessTable[136].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCreateGuild;
  this->m_ProcessTable[137].m_dwCommand = 137;
  this->m_ProcessTable[137].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildCmd;
  this->m_ProcessTable[138].m_dwCommand = 138;
  this->m_ProcessTable[138].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildMark;
  this->m_ProcessTable[139].m_dwCommand = 139;
  this->m_ProcessTable[139].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildLevel;
  this->m_ProcessTable[140].m_dwCommand = 140;
  this->m_ProcessTable[140].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildRelation;
  this->m_ProcessTable[163].m_dwCommand = 163;
  this->m_ProcessTable[163].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildInclination;
  this->m_ProcessTable[141].m_dwCommand = 141;
  this->m_ProcessTable[141].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildList;
  this->m_ProcessTable[143].m_dwCommand = 143;
  this->m_ProcessTable[143].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildRight;
  this->m_ProcessTable[144].m_dwCommand = 144;
  this->m_ProcessTable[144].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildMemberList;
  this->m_ProcessTable[146].m_dwCommand = 146;
  this->m_ProcessTable[146].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharGuildSafe;
  this->m_ProcessTable[98].m_dwCommand = 98;
  this->m_ProcessTable[98].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharAdminCmd;
  this->m_ProcessTable[106].m_dwCommand = 106;
  this->m_ProcessTable[106].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharStartQuest;
  this->m_ProcessTable[107].m_dwCommand = 107;
  this->m_ProcessTable[107].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharOperateTrigger;
  this->m_ProcessTable[121].m_dwCommand = 121;
  this->m_ProcessTable[121].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCancelQuest;
  this->m_ProcessTable[9].m_dwCommand = 9;
  this->m_ProcessTable[9].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSuicide;
  this->m_ProcessTable[28].m_dwCommand = 28;
  this->m_ProcessTable[28].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBindPosition;
  this->m_ProcessTable[65].m_dwCommand = 65;
  this->m_ProcessTable[65].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharQuickSlotMove;
  this->m_ProcessTable[83].m_dwCommand = 83;
  this->m_ProcessTable[83].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharControlOption;
  this->m_ProcessTable[111].m_dwCommand = 111;
  this->m_ProcessTable[111].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharAuthorizePanel;
  this->m_ProcessTable[85].m_dwCommand = 85;
  this->m_ProcessTable[85].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharFameInfo;
  this->m_ProcessTable[115].m_dwCommand = 115;
  this->m_ProcessTable[115].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharRankingInfo;
  this->m_ProcessTable[89].m_dwCommand = 89;
  this->m_ProcessTable[89].m_fnProcess = 0;
  this->m_ProcessTable[152].m_dwCommand = 152;
  this->m_ProcessTable[152].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerMapList;
  this->m_ProcessTable[153].m_dwCommand = 153;
  this->m_ProcessTable[153].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerResultList;
  this->m_ProcessTable[154].m_dwCommand = 154;
  this->m_ProcessTable[154].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerMoveZone;
  this->m_ProcessTable[155].m_dwCommand = 155;
  this->m_ProcessTable[155].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerMileageChange;
  this->m_ProcessTable[156].m_dwCommand = 156;
  this->m_ProcessTable[156].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerCharSlot;
  this->m_ProcessTable[86].m_dwCommand = 86;
  this->m_ProcessTable[86].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharBGServerList;
  this->m_ProcessTable[170].m_dwCommand = 170;
  this->m_ProcessTable[170].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCastleCmd;
  this->m_ProcessTable[171].m_dwCommand = 171;
  this->m_ProcessTable[171].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCampCmd;
  this->m_ProcessTable[172].m_dwCommand = 172;
  this->m_ProcessTable[172].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharSiegeArmsCmd;
  this->m_ProcessTable[173].m_dwCommand = 173;
  result = 1;
  this->m_ProcessTable[173].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCastleRight;
  this->m_ProcessTable[174].m_dwCommand = 174;
  this->m_ProcessTable[174].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharCampRight;
  return result;
}

//----- (004240D0) --------------------------------------------------------
void __thiscall CGameClientDispatch::Disconnected(CGameClientDispatch *this)
{
  CCharacter *m_lpCharacter; // ecx

  m_lpCharacter = this->m_lpCharacter;
  if ( m_lpCharacter )
    CCharacter::SetDispatcher(m_lpCharacter, 0);
}

//----- (004240F0) --------------------------------------------------------
char __thiscall CGameClientDispatch::Dispatch(CGameClientDispatch *this)
{
  DWORD Time; // edi
  CCharacter *m_lpCharacter; // eax
  unsigned int m_dwCID; // ebp
  unsigned int dwLastPingRecvTime; // [esp+Ch] [ebp-114h] BYREF
  unsigned int dwFirstCheckTime; // [esp+10h] [ebp-110h] BYREF
  const char *szCharacterName; // [esp+14h] [ebp-10Ch]
  unsigned int dwPingCount; // [esp+18h] [ebp-108h] BYREF
  char szBuffer[256]; // [esp+1Ch] [ebp-104h] BYREF

  Time = timeGetTime();
  m_lpCharacter = this->m_lpCharacter;
  m_dwCID = 0;
  szCharacterName = "NONE";
  if ( m_lpCharacter )
  {
    m_dwCID = m_lpCharacter->m_dwCID;
    szCharacterName = m_lpCharacter->m_DBData.m_Info.Name;
  }
  dwPingCount = 0;
  dwLastPingRecvTime = 0;
  dwFirstCheckTime = 0;
  CCheckPing::GetPingData(&this->m_CheckPing, &dwPingCount, &dwLastPingRecvTime, &dwFirstCheckTime);
  if ( CCheckPing::CheckPing(&this->m_CheckPing, Time) )
    return CRylServerDispatch::Dispatch(this);
  _snprintf(
    szBuffer,
    0xFFu,
    "UID:%u/CID:%u/Name:%s/CurrentTime:%u/LastPingTime:%u/PingCount:%u/FirstCheckTime:%u/PingCheck failed. disconnect now.",
    this->m_dwUID,
    m_dwCID,
    szCharacterName,
    Time,
    dwLastPingRecvTime,
    dwPingCount,
    dwFirstCheckTime);
  CRylServerDispatch::LogErrorPacket(this, szBuffer, 0);
  return 0;
}

//----- (004241E0) --------------------------------------------------------
bool __cdecl LogFailDispatch(CGameClientDispatch *GameClientDispatch, const char *szDetailText, unsigned __int8 cCmd)
{
  unsigned int m_dwUID; // edi
  struct in_addr *RemoteAddr; // eax
  char *v5; // eax

  m_dwUID = GameClientDispatch->m_dwUID;
  RemoteAddr = (struct in_addr *)CRylServerDispatch::GetRemoteAddr(GameClientDispatch);
  v5 = inet_ntoa(RemoteAddr[1]);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "LogFailDispatch",
    aDWorkRylSource_45,
    342,
    "DP:0x%p/UID:%d/PacketCMD:0x%02x/IP:%15s/%s",
    GameClientDispatch,
    m_dwUID,
    cCmd,
    v5,
    szDetailText);
  return 0;
}

//----- (00424230) --------------------------------------------------------
void __thiscall CGameClientDispatch::PrintGameGuardError(CGameClientDispatch *this)
{
  switch ( CCSAuth::PPGetLastError(&this->m_CSAuth) )
  {
    case 1u:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CGameClientDispatch::PrintGameGuardError",
        aDWorkRylSource_45,
        398,
        aCid10u,
        this->m_lpCharacter->m_dwCID);
      break;
    case 2u:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CGameClientDispatch::PrintGameGuardError",
        aDWorkRylSource_45,
        403,
        aCid10u_1,
        this->m_lpCharacter->m_dwCID);
      break;
    case 3u:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CGameClientDispatch::PrintGameGuardError",
        aDWorkRylSource_45,
        408,
        aCid10u_0,
        this->m_lpCharacter->m_dwCID);
      break;
    case 4u:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CGameClientDispatch::PrintGameGuardError",
        aDWorkRylSource_45,
        413,
        aCid10u_2,
        this->m_lpCharacter->m_dwCID);
      break;
    default:
      return;
  }
}

//----- (004242C0) --------------------------------------------------------
void __thiscall CGameClientDispatch::SetCharacter(CGameClientDispatch *this, CCharacter *lpCharacter)
{
  this->m_lpCharacter = lpCharacter;
  this->m_CheckSpeedHack.m_lpCharacter = lpCharacter;
}

//----- (004242D0) --------------------------------------------------------
void __thiscall CCSLock::~CCSLock(CCheckPing *this)
{
  DeleteCriticalSection(&this->m_PingLock.m_CSLock);
}

//----- (004242E0) --------------------------------------------------------
char __thiscall CGameClientDispatch::DispatchPacket(CGameClientDispatch *this, PktMU *lpPktBase)
{
  DWORD Time; // eax
  char v6; // al
  bool (__cdecl *m_fnProcess)(CGameClientDispatch *, PktBase *); // eax
  unsigned __int8 lpPktBasea; // [esp+10h] [ebp+4h]

  lpPktBasea = lpPktBase->m_Cmd;
  switch ( lpPktBasea )
  {
    case 0xCu:
      v6 = CSpeedHackCheck::CheckMoveUpdate(&this->m_CheckSpeedHack, lpPktBase);
      break;
    case 0xEu:
      v6 = CSpeedHackCheck::CheckAttackReplay(&this->m_CheckSpeedHack, (PktAt *)lpPktBase);
      break;
    case 0x59u:
      Time = timeGetTime();
      CCheckPing::SetLastPingRecvTime(&this->m_CheckPing, Time);
      return 1;
    default:
      goto LABEL_9;
  }
  if ( !v6 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CGameClientDispatch::DispatchPacket",
      aDWorkRylSource_45,
      233,
      aDp0xPUidD,
      this,
      this->m_dwUID);
    return 0;
  }
LABEL_9:
  if ( HIWORD(lpPktBase->m_CodePage) != 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CGameClientDispatch::DispatchPacket",
      aDWorkRylSource_45,
      240,
      aDp0xPUidD_0,
      this,
      this->m_dwUID);
    return 0;
  }
  if ( lpPktBasea != 255 && (m_fnProcess = this->m_GameClientDispatchTable->m_ProcessTable[lpPktBasea].m_fnProcess) != 0 )
  {
    if ( !m_fnProcess(this, lpPktBase) )
      LogFailDispatch(this, "GameServer packet process failed.", lpPktBasea);
    return 1;
  }
  else
  {
    LogFailDispatch(this, "Unknown Gameserver Packet", lpPktBasea);
    return 1;
  }
}

//----- (004243E0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00424430) --------------------------------------------------------
void __thiscall std::deque<unsigned long>::_Tidy(std::deque<CSpeedHackCheck::SkillHistory> *this)
{
  unsigned int Mysize; // eax
  unsigned int v3; // eax
  unsigned int i; // edi
  CSpeedHackCheck::SkillHistory *v5; // eax

  while ( this->_Mysize )
  {
    Mysize = this->_Mysize;
    if ( Mysize )
    {
      v3 = Mysize - 1;
      this->_Mysize = v3;
      if ( !v3 )
        this->_Myoff = 0;
    }
  }
  for ( i = this->_Mapsize; i; --i )
  {
    v5 = this->_Map[i - 1];
    if ( v5 )
      operator delete(v5);
  }
  if ( this->_Map )
    operator delete(this->_Map);
  this->_Mapsize = 0;
  this->_Map = 0;
}

//----- (004244A0) --------------------------------------------------------
unsigned int __thiscall CGameClientDispatch::PopRequestKey(CGameClientDispatch *this)
{
  unsigned int m_dwUID; // edi
  unsigned int v3; // ebp
  unsigned int v4; // eax
  unsigned int v5; // ecx
  unsigned int Mapsize; // edx
  unsigned int Mysize; // eax
  unsigned int v8; // ecx
  unsigned int v9; // edx
  unsigned int v10; // eax
  CCharacter *m_lpCharacter; // eax
  const void *m_dwCID; // ebx
  struct in_addr *RemoteAddr; // eax
  char *v14; // eax

  m_dwUID = 0;
  v3 = 0;
  if ( this->m_DBRequestQueue._Mysize )
  {
    v4 = this->m_DBRequestQueue._Myoff >> 2;
    v5 = this->m_DBRequestQueue._Myoff - 4 * v4;
    Mapsize = this->m_DBRequestQueue._Mapsize;
    if ( Mapsize <= v4 )
      v4 -= Mapsize;
    v3 = this->m_DBRequestQueue._Map[v4][v5];
    Mysize = this->m_DBRequestQueue._Mysize;
    if ( Mysize )
    {
      v8 = this->m_DBRequestQueue._Myoff + 1;
      v9 = 4 * this->m_DBRequestQueue._Mapsize;
      this->m_DBRequestQueue._Myoff = v8;
      if ( v9 <= v8 )
        this->m_DBRequestQueue._Myoff = 0;
      v10 = Mysize - 1;
      this->m_DBRequestQueue._Mysize = v10;
      if ( !v10 )
        this->m_DBRequestQueue._Myoff = 0;
    }
  }
  m_lpCharacter = this->m_lpCharacter;
  m_dwCID = 0;
  if ( m_lpCharacter )
  {
    m_dwUID = m_lpCharacter->m_dwUID;
    m_dwCID = (const void *)m_lpCharacter->m_dwCID;
  }
  RemoteAddr = (struct in_addr *)CRylServerDispatch::GetRemoteAddr(this);
  v14 = inet_ntoa(RemoteAddr[1]);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CGameClientDispatch::PopRequestKey",
    aDWorkRylSource_45,
    368,
    "UID:%u/CID:0x%p(0x%p)/IP:%15s/DP:0x%p/DUID:%u/RequestKey:%d/ Pop RequestKey",
    m_dwUID,
    m_dwCID,
    this->m_lpCharacter,
    v14,
    this,
    this->m_dwUID,
    v3);
  return v3;
}

//----- (00424560) --------------------------------------------------------
void __thiscall __noreturn std::deque<unsigned long>::_Xlen(std::deque<unsigned long> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "deque<T> too long", 0x11u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004245D0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,unsigned long> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,unsigned long> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00424780) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00424A40) --------------------------------------------------------
void __thiscall CSpeedHackCheck::~CSpeedHackCheck(CSpeedHackCheck *this)
{
  std::deque<unsigned long>::_Tidy(&this->m_SkillHistoryList);
  if ( this->m_CoolDownInfo._Myfirst )
    operator delete(this->m_CoolDownInfo._Myfirst);
  this->m_CoolDownInfo._Myfirst = 0;
  this->m_CoolDownInfo._Mylast = 0;
  this->m_CoolDownInfo._Myend = 0;
  std::deque<unsigned long>::_Tidy((std::deque<CSpeedHackCheck::SkillHistory> *)&this->m_AttackTimeList);
}

//----- (00424A80) --------------------------------------------------------
void __thiscall CGameClientDispatch::~CGameClientDispatch(CGameClientDispatch *this)
{
  CCharacter *m_lpCharacter; // ecx

  this->__vftable = (CGameClientDispatch_vtbl *)&CGameClientDispatch::`vftable';
  m_lpCharacter = this->m_lpCharacter;
  if ( m_lpCharacter )
    CCharacter::SetDispatcher(m_lpCharacter, 0);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CGameClientDispatch::~CGameClientDispatch",
    aDWorkRylSource_45,
    44,
    "DP:0x%p/UID:%u/GameClientDispatch Destroy",
    this,
    this->m_dwUID);
  CCrc32::~CCrc32((CCrc32 *)&this->m_CSAuth);
  std::deque<unsigned long>::_Tidy(&this->m_CheckSpeedHack.m_SkillHistoryList);
  if ( this->m_CheckSpeedHack.m_CoolDownInfo._Myfirst )
    operator delete(this->m_CheckSpeedHack.m_CoolDownInfo._Myfirst);
  this->m_CheckSpeedHack.m_CoolDownInfo._Myfirst = 0;
  this->m_CheckSpeedHack.m_CoolDownInfo._Mylast = 0;
  this->m_CheckSpeedHack.m_CoolDownInfo._Myend = 0;
  std::deque<unsigned long>::_Tidy((std::deque<CSpeedHackCheck::SkillHistory> *)&this->m_CheckSpeedHack.m_AttackTimeList);
  DeleteCriticalSection(&this->m_CheckPing.m_PingLock.m_CSLock);
  std::deque<unsigned long>::_Tidy((std::deque<CSpeedHackCheck::SkillHistory> *)&this->m_DBRequestQueue);
  CRylServerDispatch::~CRylServerDispatch(this);
}
// 4DB660: using guessed type void *CGameClientDispatch::`vftable';

//----- (00424B70) --------------------------------------------------------
void __thiscall std::deque<unsigned long>::_Growmap(std::deque<unsigned long> *this, unsigned int _Count)
{
  unsigned int Mapsize; // eax
  unsigned int v4; // edi
  unsigned int v5; // ecx
  unsigned int v6; // ebp
  int v7; // esi
  unsigned __int8 *Map; // ecx
  int v9; // eax
  unsigned __int8 *v10; // eax
  int v11; // eax
  unsigned __int8 *v12; // edi
  unsigned int v13; // ecx
  unsigned int v14; // esi
  int v15; // eax
  unsigned int v16; // eax
  unsigned __int8 *_Newmap; // [esp+8h] [ebp-Ch]
  unsigned int v18; // [esp+10h] [ebp-4h]
  unsigned int v19; // [esp+10h] [ebp-4h]

  Mapsize = this->_Mapsize;
  v4 = _Count;
  if ( 0xFFFFFFF - Mapsize < _Count )
    std::deque<unsigned long>::_Xlen(this);
  v5 = Mapsize >> 1;
  if ( Mapsize >> 1 < 8 )
    v5 = 8;
  if ( _Count < v5 && Mapsize <= 0xFFFFFFF - v5 )
  {
    _Count = v5;
    v4 = v5;
  }
  v6 = this->_Myoff >> 2;
  v7 = 4 * v6;
  _Newmap = (unsigned __int8 *)operator new((tagHeader *)(4 * (v4 + Mapsize)));
  v18 = 4 * ((int)(4 * this->_Mapsize - 4 * v6) >> 2);
  memmove(&_Newmap[4 * v6], (unsigned __int8 *)&this->_Map[v6], v18);
  Map = (unsigned __int8 *)this->_Map;
  v10 = (unsigned __int8 *)(v18 + v9);
  if ( v6 > v4 )
  {
    memmove(v10, Map, 4 * ((int)(4 * v4) >> 2));
    v14 = 4 * ((int)(v7 - 4 * v4) >> 2);
    memmove(_Newmap, (unsigned __int8 *)&this->_Map[v4], v14);
    if ( !v4 )
      goto LABEL_16;
    v13 = v4;
    v12 = (unsigned __int8 *)(v14 + v15);
    goto LABEL_15;
  }
  v19 = 4 * (v7 >> 2);
  memmove(v10, Map, v19);
  if ( v4 != v6 )
  {
    memset((void *)(v19 + v11), 0, 4 * (v4 - v6));
    v4 = _Count;
  }
  if ( v6 )
  {
    v12 = _Newmap;
    v13 = v6;
LABEL_15:
    memset(v12, 0, 4 * v13);
    v4 = _Count;
  }
LABEL_16:
  if ( this->_Map )
    operator delete(this->_Map);
  v16 = v4 + this->_Mapsize;
  this->_Map = (unsigned int **)_Newmap;
  this->_Mapsize = v16;
}
// 424C0B: variable 'v9' is possibly undefined
// 424C32: variable 'v11' is possibly undefined
// 424C90: variable 'v15' is possibly undefined

//----- (00424CD0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> *result,
        std::pair<unsigned long const ,unsigned long> *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int first; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      v7 = first > Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( first <= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (std::pair<unsigned long const ,unsigned long> *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first <= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00424D90) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00424E50) --------------------------------------------------------
void __thiscall CGameClientDispatch::CGameClientDispatch(
        CGameClientDispatch *this,
        CSession *Session,
        CGameClientDispatchTable *GameClientDispatchTable)
{
  CRylServerDispatch::CRylServerDispatch(this, Session, 0xAu);
  this->__vftable = (CGameClientDispatch_vtbl *)&CGameClientDispatch::`vftable';
  this->m_lpCharacter = 0;
  this->m_dwUID = 0;
  this->m_GameClientDispatchTable = GameClientDispatchTable;
  this->m_DBRequestQueue._Map = 0;
  this->m_DBRequestQueue._Mapsize = 0;
  this->m_DBRequestQueue._Myoff = 0;
  this->m_DBRequestQueue._Mysize = 0;
  CCheckPing::CCheckPing(&this->m_CheckPing);
  CSpeedHackCheck::CSpeedHackCheck(&this->m_CheckSpeedHack);
  CCSAuth::CCSAuth(&this->m_CSAuth);
  this->m_MoveAddress.m_iAddrLen = 0;
  *(_DWORD *)&this->m_MoveAddress.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_MoveAddress.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_MoveAddress.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_MoveAddress.m_SockAddr.sa_data[10] = 0;
  this->m_dwMoveServerID = 0;
  CCSAuth::Init(&this->m_CSAuth);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CGameClientDispatch::CGameClientDispatch",
    aDWorkRylSource_45,
    35,
    "DP:0x%p/UID:%u/GameClientDispatch Created",
    this,
    this->m_dwUID);
}
// 4DB660: using guessed type void *CGameClientDispatch::`vftable';

//----- (00424F40) --------------------------------------------------------
CGameClientDispatch *__thiscall CGameClientDispatch::`vector deleting destructor'(CGameClientDispatch *this, char a2)
{
  CGameClientDispatch::~CGameClientDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00424F60) --------------------------------------------------------
void __thiscall std::deque<unsigned long>::push_back(std::deque<unsigned long> *this, unsigned int *_Val)
{
  unsigned int Mysize; // eax
  unsigned int Mapsize; // eax
  unsigned int v5; // edi
  unsigned int v6; // ebx
  unsigned int *v7; // eax

  Mysize = this->_Mysize;
  if ( (((_BYTE)Mysize + LOBYTE(this->_Myoff)) & 3) == 0 && this->_Mapsize <= (Mysize + 4) >> 2 )
    std::deque<unsigned long>::_Growmap(this, 1u);
  Mapsize = this->_Mapsize;
  v5 = this->_Mysize + this->_Myoff;
  v6 = v5 >> 2;
  if ( Mapsize <= v5 >> 2 )
    v6 -= Mapsize;
  if ( !this->_Map[v6] )
    this->_Map[v6] = (unsigned int *)operator new((tagHeader *)0x10);
  v7 = &this->_Map[v6][v5 & 3];
  if ( v7 )
    *v7 = *_Val;
  ++this->_Mysize;
}

//----- (00424FE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Where,
        std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // eax
  std::pair<unsigned long const ,unsigned long> *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Right; // eax
  unsigned int first; // ebp
  bool v10; // cc
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> v12; // [esp+8h] [ebp-8h] BYREF

  if ( !this->_Mysize )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
      this,
      result,
      1,
      this->_Myhead,
      _Val);
    return result;
  }
  Myhead = this->_Myhead;
  v7 = _Val;
  if ( _Where._Ptr == Myhead->_Left )
  {
    if ( _Val->first > _Where._Ptr->_Myval.first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  if ( _Where._Ptr == Myhead )
  {
    Right = Myhead->_Right;
    if ( Right->_Myval.first > _Val->first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
        this,
        result,
        0,
        Right,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  first = _Val->first;
  v10 = _Where._Ptr->_Myval.first <= _Val->first;
  if ( _Where._Ptr->_Myval.first < _Val->first )
  {
    _Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr;
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    if ( _Val[1].second > first )
    {
      if ( *(_BYTE *)(_Val[1].first + 21) )
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
          this,
          result,
          0,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)_Val,
          v7);
      else
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
          this,
          result,
          1,
          _Where._Ptr,
          v7);
      return result;
    }
    v10 = _Where._Ptr->_Myval.first <= first;
  }
  if ( v10
    || (_Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Val),
        _Val != (std::pair<unsigned long const ,unsigned long> *)this->_Myhead)
    && first <= _Val[1].second )
  {
LABEL_23:
    Ptr = std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(
            this,
            &v12,
            v7)->first._Ptr;
    v5 = result;
    result->_Ptr = Ptr;
    return v5;
  }
  if ( _Where._Ptr->_Right->_Isnil )
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
      this,
      result,
      0,
      _Where._Ptr,
      v7);
  else
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(
      this,
      result,
      1,
      (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)_Val,
      v7);
  return result;
}

//----- (00425150) --------------------------------------------------------
void __thiscall CGameClientDispatch::PushRequestKey(CGameClientDispatch *this, unsigned int dwRequestKey)
{
  CCharacter *m_lpCharacter; // eax
  unsigned int m_dwUID; // edi
  const void *m_dwCID; // ebx
  struct in_addr *RemoteAddr; // eax
  char *v7; // eax

  m_lpCharacter = this->m_lpCharacter;
  m_dwUID = 0;
  m_dwCID = 0;
  if ( m_lpCharacter )
  {
    m_dwUID = m_lpCharacter->m_dwUID;
    m_dwCID = (const void *)m_lpCharacter->m_dwCID;
  }
  RemoteAddr = (struct in_addr *)CRylServerDispatch::GetRemoteAddr(this);
  v7 = inet_ntoa(RemoteAddr[1]);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CGameClientDispatch::PushRequestKey",
    aDWorkRylSource_45,
    387,
    "UID:%u/CID:0x%p(0x%p)/IP:%15s/DP:0x%p/DUID:%u/RequestKey:%d/ Push RequestKey",
    m_dwUID,
    m_dwCID,
    this->m_lpCharacter,
    v7,
    this,
    this->m_dwUID,
    dwRequestKey);
  std::deque<unsigned long>::push_back(&this->m_DBRequestQueue, &dwRequestKey);
}

//----- (004251D0) --------------------------------------------------------
unsigned int *__thiscall std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>::operator[](
        std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> > > *this,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Parent; // eax
  std::pair<unsigned long const ,unsigned long> _Val; // [esp+Ch] [ebp-8h] BYREF

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first <= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead != this->_Myhead && *_Keyval <= Myhead->_Myval.first )
    return &Myhead->_Myval.second;
  _Val.first = *_Keyval;
  _Val.second = 0;
  return &std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *)&_Keyval,
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator)Myhead,
            &_Val)->_Ptr->_Myval.second;
}

//----- (00425250) --------------------------------------------------------
void __thiscall std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>::~map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>(
        std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> > > *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00425280) --------------------------------------------------------
void __thiscall CGameClientDispatch::ProcessTooManyPacket(CGameClientDispatch *this, CBufferQueue *bufferQueue)
{
  CCharacter *m_lpCharacter; // eax
  unsigned int m_dwCID; // ebp
  unsigned int m_dwMaxProcessPacketPerPulse; // ecx
  struct in_addr S_addr; // edx
  char *Name; // edi
  char *v8; // eax
  int v9; // eax
  CBuffer *m_lpHead; // esi
  int i; // edi
  unsigned int *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *Left; // eax
  unsigned int v14; // esi
  unsigned int v15; // [esp-10h] [ebp-238h]
  unsigned int m_bufferNum; // [esp-Ch] [ebp-234h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *v17; // [esp-8h] [ebp-230h]
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator itr; // [esp+8h] [ebp-220h] BYREF
  std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> > > packetStatistics; // [esp+Ch] [ebp-21Ch] BYREF
  char szBuffer[512]; // [esp+18h] [ebp-210h] BYREF
  int v21; // [esp+224h] [ebp-4h]

  if ( this->m_lpCharacter )
  {
    packetStatistics._Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&packetStatistics);
    packetStatistics._Myhead->_Isnil = 1;
    packetStatistics._Myhead->_Parent = packetStatistics._Myhead;
    packetStatistics._Myhead->_Left = packetStatistics._Myhead;
    packetStatistics._Myhead->_Right = packetStatistics._Myhead;
    packetStatistics._Mysize = 0;
    m_lpCharacter = this->m_lpCharacter;
    m_dwCID = m_lpCharacter->m_dwCID;
    m_bufferNum = bufferQueue->m_bufferNum;
    m_dwMaxProcessPacketPerPulse = this->m_dwMaxProcessPacketPerPulse;
    itr._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)m_lpCharacter->m_dwUID;
    S_addr = (struct in_addr)m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_addr;
    v15 = m_dwMaxProcessPacketPerPulse;
    v21 = 0;
    Name = m_lpCharacter->m_DBData.m_Info.Name;
    v8 = inet_ntoa(S_addr);
    v9 = _snprintf(szBuffer, 0x200u, aUidDCid0x08xNa_2, itr._Ptr, m_dwCID, Name, v8, v15, m_bufferNum);
    m_lpHead = bufferQueue->m_lpHead;
    for ( i = v9; m_lpHead; m_lpHead = m_lpHead->next_ )
    {
      itr._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *)*((unsigned __int8 *)m_lpHead->rd_ptr_ + 1);
      v12 = std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>::operator[](
              &packetStatistics,
              (const unsigned int *)&itr);
      ++*v12;
    }
    Left = packetStatistics._Myhead->_Left;
    v14 = 0;
    for ( itr._Ptr = packetStatistics._Myhead->_Left; itr._Ptr != packetStatistics._Myhead; ++v14 )
    {
      if ( v14 >= 5 )
        break;
      i += _snprintf(&szBuffer[i], 512 - i, "0x%02x:%5d/", Left->_Myval.first, Left->_Myval.second);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&itr);
      Left = itr._Ptr;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CGameClientDispatch::ProcessTooManyPacket",
      aDWorkRylSource_45,
      331,
      szBuffer);
    v17 = packetStatistics._Myhead->_Left;
    v21 = -1;
    std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(
      &packetStatistics,
      &itr,
      (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator)v17,
      (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator)packetStatistics._Myhead);
    operator delete(packetStatistics._Myhead);
  }
}

//----- (00425440) --------------------------------------------------------
BOOL __thiscall CDBRequest::IsValid(CDBRequest *this)
{
  return this->m_lpDBAgentSendStream && this->m_dwRequestKey;
}


//----- (00425460) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharLogin(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebx
  unsigned int dwServerInfo; // ebp
  unsigned int v5; // edi
  CCreatureManager *Instance; // eax
  in_addr *RemoteAddr; // eax
  sockaddr_in *v8; // eax
  CDBRequest DBRequest; // [esp+0h] [ebp-8h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x18 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  GameClientDispatch->m_dwUID = v5;
  Instance = CCreatureManager::GetInstance();
  if ( CCreatureManager::GetCharacter(Instance, m_CodePage) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "GameClientParsePacket::ParseCharLogin",
      aDWorkRylSource_35,
      38,
      aUidDCid0x08xSi_1,
      v5,
      m_CodePage,
      dwServerInfo);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "GameClientParsePacket::ParseCharLogin",
      aDWorkRylSource_35,
      43,
      aUidDCid0x08xSi,
      v5,
      m_CodePage,
      dwServerInfo);
    CDBRequest::CDBRequest(&DBRequest, GameClientDispatch, 0x258u, 0);
    if ( DBRequest.m_lpDBAgentSendStream && DBRequest.m_dwRequestKey )
    {
      RemoteAddr = (in_addr *)CRylServerDispatch::GetRemoteAddr(GameClientDispatch);
      if ( GameClientSendPacket::SendLoginToDBAgent(
             DBRequest.m_lpDBAgentSendStream,
             DBRequest.m_dwRequestKey,
             dwServerInfo,
             v5,
             m_CodePage,
             RemoteAddr[1]) )
      {
        CGameClientDispatch::PushRequestKey(GameClientDispatch, DBRequest.m_dwRequestKey);
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ParseCharLogin",
          aDWorkRylSource_35,
          57,
          aUidDCid0x08xSi_0,
          v5,
          m_CodePage,
          dwServerInfo,
          DBRequest.m_dwRequestKey,
          GameClientDispatch);
        return 1;
      }
      CDBRequest::CancelRequest(&DBRequest);
    }
  }
  v8 = (sockaddr_in *)CRylServerDispatch::GetRemoteAddr(GameClientDispatch);
  return GameClientSendPacket::SendCharLogin(&GameClientDispatch->m_SendStream, 0, v8, 0);
}

//----- (004255B0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharMoveZone(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1A )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      if ( CCharacter::MoveZone(m_lpCharacter, (POS)lpPktBase[1], lpPktBase[2].m_StartBit, lpPktBase[2].m_Cmd) )
      {
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharMoveZone",
          aDWorkRylSource_35,
          85,
          aMovezone);
        return GameClientSendPacket::SendCharMoveZone(&GameClientDispatch->m_SendStream, 0, 0, 0, 1u);
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00425670) --------------------------------------------------------
bool __cdecl GameClientParsePacket::ParseCharLogout(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x10 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      if ( *(_DWORD *)&lpPktBase[1].m_StartBit == m_lpCharacter->m_dwCID )
        CCharacter::SetDispatcher(m_lpCharacter, 0);
      return 0;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004256D0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseServerZone(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  CDBRequest DBRequest; // [esp+4h] [ebp-8h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) != 0xF )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  if ( (m_lpCharacter->m_cOperationFlags & 8) != 0 )
    return 1;
  CDBRequest::CDBRequest(&DBRequest, GameClientDispatch, 0x258u, 0);
  if ( DBRequest.m_lpDBAgentSendStream && DBRequest.m_dwRequestKey )
  {
    if ( GameClientSendPacket::SendServerZoneToDBAgent(
           DBRequest.m_lpDBAgentSendStream,
           DBRequest.m_dwRequestKey,
           GameClientDispatch->m_dwUID,
           lpPktBase[1].m_Len,
           lpPktBase[1].m_StartBit) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "GameClientParsePacket::ParseServerZone",
        aDWorkRylSource_35,
        136,
        aUidDCid0x08x0x_9,
        m_lpCharacter->m_dwUID,
        m_lpCharacter->m_dwCID,
        m_lpCharacter,
        DBRequest.m_dwRequestKey,
        GameClientDispatch);
      m_lpCharacter->m_cOperationFlags |= 8u;
      CGameClientDispatch::PushRequestKey(GameClientDispatch, DBRequest.m_dwRequestKey);
      return 1;
    }
    CDBRequest::CancelRequest(&DBRequest);
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "GameClientParsePacket::ParseServerZone",
    aDWorkRylSource_35,
    146,
    aServerzone);
  return GameClientSendPacket::SendCharMoveZone(&GameClientDispatch->m_SendStream, 0, 0, 0, 1u);
}

//----- (00425820) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCSAuth(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x14 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      if ( CCSAuth::CheckAuthDword(&GameClientDispatch->m_CSAuth, lpPktBase[1].m_CodePage) == 1 )
      {
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCSAuth",
          aDWorkRylSource_35,
          164,
          aCid0x08x_113,
          m_lpCharacter->m_dwCID);
        return 0;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004258B0) --------------------------------------------------------
void __thiscall CBattleSongSpell::CBattleSongSpell(
        CBattleSongSpell *this,
        CSpell::Spell_Info *spell_Info,
        __int16 nConsumeMPAmount)
{
  CSpell::CSpell(this, spell_Info, LEAVE_WAIT, 1u);
  this->m_cOperateTurn = 0;
  this->m_nConsumeMPAmount = nConsumeMPAmount;
  this->__vftable = (CBattleSongSpell_vtbl *)&CBattleSongSpell::`vftable';
}
// 4DBBE0: using guessed type void *CBattleSongSpell::`vftable';

//----- (004258E0) --------------------------------------------------------
CBattleSongSpell *__thiscall CBattleSongSpell::`vector deleting destructor'(CBattleSongSpell *this, char a2)
{
  CBattleSongSpell::~CBattleSongSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425900) --------------------------------------------------------
void __thiscall CBattleSongSpell::~CBattleSongSpell(CBattleSongSpell *this)
{
  this->__vftable = (CBattleSongSpell_vtbl *)&CBattleSongSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CBattleSongSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBBE0: using guessed type void *CBattleSongSpell::`vftable';

//----- (00425960) --------------------------------------------------------
void __thiscall CMaintenanceChantSpell::CMaintenanceChantSpell(
        CMaintenanceChantSpell *this,
        CSpell::Spell_Info *spell_Info,
        __int16 nConsumeMPAmount)
{
  CSpell::CSpell(this, spell_Info, LEAVE_WAIT, 2u);
  this->m_cOperateTurn = 0;
  this->m_nConsumeMPAmount = nConsumeMPAmount;
  this->__vftable = (CMaintenanceChantSpell_vtbl *)&CMaintenanceChantSpell::`vftable';
}
// 4DBBF0: using guessed type void *CMaintenanceChantSpell::`vftable';

//----- (00425990) --------------------------------------------------------
CMaintenanceChantSpell *__thiscall CMaintenanceChantSpell::`vector deleting destructor'(
        CMaintenanceChantSpell *this,
        char a2)
{
  CMaintenanceChantSpell::~CMaintenanceChantSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004259B0) --------------------------------------------------------
void __thiscall CMaintenanceChantSpell::~CMaintenanceChantSpell(CMaintenanceChantSpell *this)
{
  this->__vftable = (CMaintenanceChantSpell_vtbl *)&CMaintenanceChantSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CMaintenanceChantSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBBF0: using guessed type void *CMaintenanceChantSpell::`vftable';

//----- (00425A10) --------------------------------------------------------
void __thiscall CAccelerationChantSpell::CAccelerationChantSpell(
        CAccelerationChantSpell *this,
        CSpell::Spell_Info *spell_Info,
        __int16 nConsumeMPAmount)
{
  CSpell::CSpell(this, spell_Info, LEAVE_WAIT, 4u);
  this->m_cOperateTurn = 0;
  this->m_nConsumeMPAmount = nConsumeMPAmount;
  this->__vftable = (CAccelerationChantSpell_vtbl *)&CAccelerationChantSpell::`vftable';
}
// 4DBC00: using guessed type void *CAccelerationChantSpell::`vftable';

//----- (00425A40) --------------------------------------------------------
CAccelerationChantSpell *__thiscall CAccelerationChantSpell::`vector deleting destructor'(
        CAccelerationChantSpell *this,
        char a2)
{
  CAccelerationChantSpell::~CAccelerationChantSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425A60) --------------------------------------------------------
void __thiscall CAccelerationChantSpell::~CAccelerationChantSpell(CAccelerationChantSpell *this)
{
  this->__vftable = (CAccelerationChantSpell_vtbl *)&CAccelerationChantSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CAccelerationChantSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC00: using guessed type void *CAccelerationChantSpell::`vftable';

//----- (00425AC0) --------------------------------------------------------
void __thiscall CLifeAuraSpell::CLifeAuraSpell(
        CLifeAuraSpell *this,
        CSpell::Spell_Info *spell_Info,
        __int16 nConsumeMPAmount)
{
  CSpell::CSpell(this, spell_Info, LEAVE_WAIT, 8u);
  this->m_cOperateTurn = 0;
  this->m_nConsumeMPAmount = nConsumeMPAmount;
  this->__vftable = (CLifeAuraSpell_vtbl *)&CLifeAuraSpell::`vftable';
}
// 4DBC10: using guessed type void *CLifeAuraSpell::`vftable';

//----- (00425AF0) --------------------------------------------------------
CLifeAuraSpell *__thiscall CLifeAuraSpell::`vector deleting destructor'(CLifeAuraSpell *this, char a2)
{
  CLifeAuraSpell::~CLifeAuraSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425B10) --------------------------------------------------------
void __thiscall CLifeAuraSpell::~CLifeAuraSpell(CLifeAuraSpell *this)
{
  this->__vftable = (CLifeAuraSpell_vtbl *)&CLifeAuraSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CLifeAuraSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC10: using guessed type void *CLifeAuraSpell::`vftable';

//----- (00425B70) --------------------------------------------------------
CDefencePotionSpell *__thiscall CDefencePotionSpell::`scalar deleting destructor'(CDefencePotionSpell *this, char a2)
{
  CDefencePotionSpell::~CDefencePotionSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425B90) --------------------------------------------------------
void __thiscall CDefencePotionSpell::~CDefencePotionSpell(CDefencePotionSpell *this)
{
  this->__vftable = (CDefencePotionSpell_vtbl *)&CDefencePotionSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CDefencePotionSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC20: using guessed type void *CDefencePotionSpell::`vftable';

//----- (00425BF0) --------------------------------------------------------
CDisenchantPotionSpell *__thiscall CDisenchantPotionSpell::`vector deleting destructor'(
        CDisenchantPotionSpell *this,
        char a2)
{
  CDisenchantPotionSpell::~CDisenchantPotionSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425C10) --------------------------------------------------------
void __thiscall CDisenchantPotionSpell::~CDisenchantPotionSpell(CDisenchantPotionSpell *this)
{
  this->__vftable = (CDisenchantPotionSpell_vtbl *)&CDisenchantPotionSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CDisenchantPotionSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC30: using guessed type void *CDisenchantPotionSpell::`vftable';

//----- (00425C70) --------------------------------------------------------
CMagicPotionSpell *__thiscall CMagicPotionSpell::`vector deleting destructor'(CMagicPotionSpell *this, char a2)
{
  CMagicPotionSpell::~CMagicPotionSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425C90) --------------------------------------------------------
void __thiscall CMagicPotionSpell::~CMagicPotionSpell(CMagicPotionSpell *this)
{
  this->__vftable = (CMagicPotionSpell_vtbl *)&CMagicPotionSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CMagicPotionSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC40: using guessed type void *CMagicPotionSpell::`vftable';

//----- (00425CF0) --------------------------------------------------------
CLightningPotionSpell *__thiscall CLightningPotionSpell::`vector deleting destructor'(
        CLightningPotionSpell *this,
        char a2)
{
  CLightningPotionSpell::~CLightningPotionSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425D10) --------------------------------------------------------
void __thiscall CLightningPotionSpell::~CLightningPotionSpell(CLightningPotionSpell *this)
{
  this->__vftable = (CLightningPotionSpell_vtbl *)&CLightningPotionSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CLightningPotionSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC50: using guessed type void *CLightningPotionSpell::`vftable';

//----- (00425D70) --------------------------------------------------------
CRegenerationSpell *__thiscall CRegenerationSpell::`vector deleting destructor'(CRegenerationSpell *this, char a2)
{
  CRegenerationSpell::~CRegenerationSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425D90) --------------------------------------------------------
void __thiscall CRegenerationSpell::~CRegenerationSpell(CRegenerationSpell *this)
{
  this->__vftable = (CRegenerationSpell_vtbl *)&CRegenerationSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CRegenerationSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC60: using guessed type void *CRegenerationSpell::`vftable';

//----- (00425DF0) --------------------------------------------------------
CStrengthSpell *__thiscall CStrengthSpell::`scalar deleting destructor'(CStrengthSpell *this, char a2)
{
  CStrengthSpell::~CStrengthSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425E10) --------------------------------------------------------
void __thiscall CStrengthSpell::~CStrengthSpell(CStrengthSpell *this)
{
  this->__vftable = (CStrengthSpell_vtbl *)&CStrengthSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CStrengthSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC70: using guessed type void *CStrengthSpell::`vftable';

//----- (00425E70) --------------------------------------------------------
CBlazeSpell *__thiscall CBlazeSpell::`scalar deleting destructor'(CBlazeSpell *this, char a2)
{
  CBlazeSpell::~CBlazeSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425E90) --------------------------------------------------------
void __thiscall CBlazeSpell::~CBlazeSpell(CBlazeSpell *this)
{
  this->__vftable = (CBlazeSpell_vtbl *)&CBlazeSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CBlazeSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC80: using guessed type void *CBlazeSpell::`vftable';

//----- (00425EF0) --------------------------------------------------------
CChargingSpell *__thiscall CChargingSpell::`vector deleting destructor'(CChargingSpell *this, char a2)
{
  CChargingSpell::~CChargingSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425F10) --------------------------------------------------------
void __thiscall CChargingSpell::~CChargingSpell(CChargingSpell *this)
{
  this->__vftable = (CChargingSpell_vtbl *)&CChargingSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CChargingSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBC90: using guessed type void *CChargingSpell::`vftable';

//----- (00425F70) --------------------------------------------------------
CStealthSpell *__thiscall CStealthSpell::`vector deleting destructor'(CStealthSpell *this, char a2)
{
  CStealthSpell::~CStealthSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00425F90) --------------------------------------------------------
void __thiscall CStealthSpell::~CStealthSpell(CStealthSpell *this)
{
  this->__vftable = (CStealthSpell_vtbl *)&CStealthSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CStealthSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCA0: using guessed type void *CStealthSpell::`vftable';

//----- (00425FF0) --------------------------------------------------------
CManaShellSpell *__thiscall CManaShellSpell::`vector deleting destructor'(CManaShellSpell *this, char a2)
{
  CManaShellSpell::~CManaShellSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426010) --------------------------------------------------------
void __thiscall CManaShellSpell::~CManaShellSpell(CManaShellSpell *this)
{
  this->__vftable = (CManaShellSpell_vtbl *)&CManaShellSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CManaShellSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCB0: using guessed type void *CManaShellSpell::`vftable';

//----- (00426070) --------------------------------------------------------
CEncourageSpell *__thiscall CEncourageSpell::`vector deleting destructor'(CEncourageSpell *this, char a2)
{
  CEncourageSpell::~CEncourageSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426090) --------------------------------------------------------
void __thiscall CEncourageSpell::~CEncourageSpell(CEncourageSpell *this)
{
  this->__vftable = (CEncourageSpell_vtbl *)&CEncourageSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CEncourageSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCC0: using guessed type void *CEncourageSpell::`vftable';

//----- (004260F0) --------------------------------------------------------
CEnchantWeaponSpell *__thiscall CEnchantWeaponSpell::`vector deleting destructor'(CEnchantWeaponSpell *this, char a2)
{
  CEnchantWeaponSpell::~CEnchantWeaponSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426110) --------------------------------------------------------
void __thiscall CEnchantWeaponSpell::~CEnchantWeaponSpell(CEnchantWeaponSpell *this)
{
  this->__vftable = (CEnchantWeaponSpell_vtbl *)&CEnchantWeaponSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CEnchantWeaponSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCD0: using guessed type void *CEnchantWeaponSpell::`vftable';

//----- (00426170) --------------------------------------------------------
CBrightArmorSpell *__thiscall CBrightArmorSpell::`scalar deleting destructor'(CBrightArmorSpell *this, char a2)
{
  CBrightArmorSpell::~CBrightArmorSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426190) --------------------------------------------------------
void __thiscall CBrightArmorSpell::~CBrightArmorSpell(CBrightArmorSpell *this)
{
  this->__vftable = (CBrightArmorSpell_vtbl *)&CBrightArmorSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CBrightArmorSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCE0: using guessed type void *CBrightArmorSpell::`vftable';

//----- (004261F0) --------------------------------------------------------
CHardenSkinSpell *__thiscall CHardenSkinSpell::`scalar deleting destructor'(CHardenSkinSpell *this, char a2)
{
  CHardenSkinSpell::~CHardenSkinSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426210) --------------------------------------------------------
void __thiscall CHardenSkinSpell::~CHardenSkinSpell(CHardenSkinSpell *this)
{
  this->__vftable = (CHardenSkinSpell_vtbl *)&CHardenSkinSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CHardenSkinSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBCF0: using guessed type void *CHardenSkinSpell::`vftable';

//----- (00426270) --------------------------------------------------------
CFlexibilitySpell *__thiscall CFlexibilitySpell::`scalar deleting destructor'(CFlexibilitySpell *this, char a2)
{
  CFlexibilitySpell::~CFlexibilitySpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426290) --------------------------------------------------------
void __thiscall CFlexibilitySpell::~CFlexibilitySpell(CFlexibilitySpell *this)
{
  this->__vftable = (CFlexibilitySpell_vtbl *)&CFlexibilitySpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CFlexibilitySpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD00: using guessed type void *CFlexibilitySpell::`vftable';

//----- (004262F0) --------------------------------------------------------
void __thiscall CGuardSpell::CGuardSpell(CGuardSpell *this, CSpell::Spell_Info *spell_Info)
{
  CSpell::CSpell(this, spell_Info, JOIN_WAIT, 0x800000u);
  this->__vftable = (CGuardSpell_vtbl *)&CGuardSpell::`vftable';
  this->m_nOriginalBlocking = 0;
}
// 4DBD10: using guessed type void *CGuardSpell::`vftable';

//----- (00426320) --------------------------------------------------------
CGuardSpell *__thiscall CGuardSpell::`vector deleting destructor'(CGuardSpell *this, char a2)
{
  CGuardSpell::~CGuardSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426340) --------------------------------------------------------
void __thiscall CGuardSpell::~CGuardSpell(CGuardSpell *this)
{
  this->__vftable = (CGuardSpell_vtbl *)&CGuardSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CGuardSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD10: using guessed type void *CGuardSpell::`vftable';

//----- (004263A0) --------------------------------------------------------
CSlowSpell *__thiscall CSlowSpell::`scalar deleting destructor'(CSlowSpell *this, char a2)
{
  CSlowSpell::~CSlowSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004263C0) --------------------------------------------------------
void __thiscall CSlowSpell::~CSlowSpell(CSlowSpell *this)
{
  this->__vftable = (CSlowSpell_vtbl *)&CSlowSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CSlowSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD20: using guessed type void *CSlowSpell::`vftable';

//----- (00426420) --------------------------------------------------------
CArmorBrokenSpell *__thiscall CArmorBrokenSpell::`vector deleting destructor'(CArmorBrokenSpell *this, char a2)
{
  CArmorBrokenSpell::~CArmorBrokenSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426440) --------------------------------------------------------
void __thiscall CArmorBrokenSpell::~CArmorBrokenSpell(CArmorBrokenSpell *this)
{
  this->__vftable = (CArmorBrokenSpell_vtbl *)&CArmorBrokenSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CArmorBrokenSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD30: using guessed type void *CArmorBrokenSpell::`vftable';

//----- (004264A0) --------------------------------------------------------
CHoldSpell *__thiscall CHoldSpell::`scalar deleting destructor'(CHoldSpell *this, char a2)
{
  CHoldSpell::~CHoldSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004264C0) --------------------------------------------------------
void __thiscall CHoldSpell::~CHoldSpell(CHoldSpell *this)
{
  this->__vftable = (CHoldSpell_vtbl *)&CHoldSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CHoldSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD40: using guessed type void *CHoldSpell::`vftable';

//----- (00426520) --------------------------------------------------------
CStunSpell *__thiscall CStunSpell::`scalar deleting destructor'(CStunSpell *this, char a2)
{
  CStunSpell::~CStunSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426540) --------------------------------------------------------
void __thiscall CStunSpell::~CStunSpell(CStunSpell *this)
{
  this->__vftable = (CStunSpell_vtbl *)&CStunSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CStunSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD50: using guessed type void *CStunSpell::`vftable';

//----- (004265A0) --------------------------------------------------------
CFrozenSpell *__thiscall CFrozenSpell::`vector deleting destructor'(CFrozenSpell *this, char a2)
{
  CFrozenSpell::~CFrozenSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004265C0) --------------------------------------------------------
void __thiscall CFrozenSpell::~CFrozenSpell(CFrozenSpell *this)
{
  this->__vftable = (CFrozenSpell_vtbl *)&CFrozenSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CFrozenSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD60: using guessed type void *CFrozenSpell::`vftable';

//----- (00426620) --------------------------------------------------------
CPoisonedSpell *__thiscall CPoisonedSpell::`vector deleting destructor'(CPoisonedSpell *this, char a2)
{
  CPoisonedSpell::~CPoisonedSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00426640) --------------------------------------------------------
void __thiscall CPoisonedSpell::~CPoisonedSpell(CPoisonedSpell *this)
{
  this->__vftable = (CPoisonedSpell_vtbl *)&CPoisonedSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CPoisonedSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD70: using guessed type void *CPoisonedSpell::`vftable';

//----- (004266A0) --------------------------------------------------------
CLowerStrengthSpell *__thiscall CLowerStrengthSpell::`vector deleting destructor'(CLowerStrengthSpell *this, char a2)
{
  CLowerStrengthSpell::~CLowerStrengthSpell(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004266C0) --------------------------------------------------------
void __thiscall CLowerStrengthSpell::~CLowerStrengthSpell(CLowerStrengthSpell *this)
{
  this->__vftable = (CLowerStrengthSpell_vtbl *)&CLowerStrengthSpell::`vftable';
  CSpell::Destroy(this);
  this->__vftable = (CLowerStrengthSpell_vtbl *)&CSpell::`vftable';
  CSpell::Destroy(this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';
// 4DBD80: using guessed type void *CLowerStrengthSpell::`vftable';

//----- (00426720) --------------------------------------------------------
void __thiscall Skill::CProcessTable::ProcessInfo::ProcessInfo(Skill::CProcessTable::ProcessInfo *this)
{
  this->m_usSkill_ID = 0;
  this->m_fnProcess = 0;
  this->m_lpProtoType = &Skill::CProcessTable::ProcessInfo::m_NullProtoType;
}

//----- (00426740) --------------------------------------------------------
double __cdecl Skill::CFunctions::ResistanceFactor(CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim)
{
  return (double)(__int16)(100
                         * (99
                          * lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
                          / (lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance + 50)))
       * 0.**********;
}

//----- (00426770) --------------------------------------------------------
void __cdecl Skill::CFunctions::WeaponMastery(AtType attackType, CAggresiveCreature *lpSkillUser)
{
  int v2; // edx
  __int16 v3; // ax

  v2 = (*((_BYTE *)&attackType + 2) & 0xE) != 2 ? -1 : 1;
  lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision += v2
                                                                 * (`Skill::CFunctions::WeaponMastery'::`2'::nRevision[*((unsigned __int8 *)&attackType + 2) >> 4]
                                                                  + 2 * attackType.m_cSkillLevel);
  v3 = v2
     * (attackType.m_cSkillLevel
      + (`Skill::CFunctions::WeaponMastery'::`2'::nRevision[*((unsigned __int8 *)&attackType + 2) >> 4] >> 1));
  lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMinDamage += v3;
  lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += v3;
}

//----- (004267D0) --------------------------------------------------------
const Skill::CProcessTable::ProcessInfo *__thiscall Skill::CProcessTable::GetProcessInfo(
        Skill::CProcessTable *this,
        unsigned __int16 usSkill_ID)
{
  unsigned int m_usProcessInfo; // edx
  const Skill::CProcessTable::ProcessInfo *result; // eax
  const Skill::CProcessTable::ProcessInfo *v4; // ebx

  m_usProcessInfo = this->m_usProcessInfo;
  result = this->m_fnProcessTable;
  v4 = &result[m_usProcessInfo];
  while ( m_usProcessInfo )
  {
    if ( result[m_usProcessInfo >> 1].m_usSkill_ID >= usSkill_ID )
    {
      m_usProcessInfo >>= 1;
    }
    else
    {
      result += (m_usProcessInfo >> 1) + 1;
      m_usProcessInfo += -1 - (m_usProcessInfo >> 1);
    }
  }
  if ( result == v4 || usSkill_ID < result->m_usSkill_ID )
    return 0;
  return result;
}

//----- (00426820) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::SwordMastery(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::SwordMastery",
      aDWorkRylSource_68,
      349,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 1u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426890) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::AxeMastery(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::AxeMastery",
      aDWorkRylSource_68,
      362,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 2u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426900) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BluntMastery(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BluntMastery",
      aDWorkRylSource_68,
      472,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 3u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426970) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BloodyMana(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned int v6; // ecx
  unsigned __int16 v7; // ax
  unsigned __int16 v8; // si
  __int16 v9; // ax
  __int16 v10; // ax
  __int16 v11; // ax
  __int16 v12; // ax
  unsigned __int16 m_nNowHP; // cx
  unsigned __int16 m_nMaxMP; // ax

  if ( lpSkillUser && lpSkillUser == lpVictim )
  {
    v6 = HIWORD(attackType);
    v7 = 0;
    v8 = 0;
    switch ( BYTE2(attackType) >> 4 )
    {
      case 0:
        v8 = 80 * SBYTE1(v6);
        v7 = 30 * SBYTE1(v6);
        break;
      case 1:
        v9 = SBYTE1(v6) + 3;
        v8 = 160 * v9;
        v7 = 60 * v9;
        break;
      case 2:
        v10 = SBYTE1(v6) + 6;
        v8 = 240 * v10;
        v7 = 90 * v10;
        break;
      case 3:
        v11 = SBYTE1(v6) + 9;
        v8 = 320 * v11;
        v7 = 120 * v11;
        break;
      case 4:
        v12 = SBYTE1(v6) + 12;
        v8 = 400 * v12;
        v7 = 150 * v12;
        break;
      default:
        break;
    }
    m_nNowHP = lpSkillUser->m_CreatureStatus.m_nNowHP;
    if ( m_nNowHP > v7 )
    {
      lpSkillUser->m_CreatureStatus.m_nNowHP = m_nNowHP - v7;
      lpSkillUser->m_CreatureStatus.m_nNowMP += (unsigned __int64)((double)(Math::Random::ComplexRandom(50, 0)
                                                                          + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                                                          + 100)
                                                                 * 0.**********
                                                                 * (double)v8);
      m_nMaxMP = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      if ( lpSkillUser->m_CreatureStatus.m_nNowMP > m_nMaxMP )
        lpSkillUser->m_CreatureStatus.m_nNowMP = m_nMaxMP;
      *cDefenserJudge = 6;
      return 0;
    }
    else
    {
      lpSkillUser->m_CreatureStatus.m_nNowHP = 0;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BloodyMana",
      aDWorkRylSource_68,
      537,
      (char *)&byte_4DBEE8);
    return 0;
  }
}

//----- (00426AD0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DaggerMastery(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DaggerMastery",
      aDWorkRylSource_68,
      1271,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 4u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426B40) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Disenchant(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  if ( CAffectedSpell::Disenchant(
         &lpVictim->m_SpellMgr.m_AffectedInfo,
         MASTER,
         MIDDLE_ADMIN,
         MASTER,
         lpSkillUser->m_CreatureStatus.m_nLevel / 2,
         1u) )
  {
    CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
    if ( (lpVictim->m_dwCID & 0x80000000) != 0 )
      lpVictim->__vftable[1].GetGID(lpVictim);
    *cDefenserJudge = 9;
    return 0;
  }
  else
  {
    *cDefenserJudge = 9;
    return 0;
  }
}

//----- (00426BB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::CrushWeapon(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::CrushWeapon",
      aDWorkRylSource_68,
      2569,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 5u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426C20) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Blade(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Blade",
      aDWorkRylSource_68,
      2631,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 6u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426C90) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Toughness(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  unsigned int v4; // ecx
  __int16 v5; // ax
  __int16 v6; // ax

  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Toughness",
      aDWorkRylSource_68,
      2699,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
    return 0;
  }
  else
  {
    v4 = HIWORD(attackType);
    v5 = 0;
    switch ( BYTE2(attackType) >> 4 )
    {
      case 0:
        v6 = SBYTE1(v4);
        goto LABEL_9;
      case 1:
        v6 = SBYTE1(v4) + 6;
        goto LABEL_9;
      case 2:
        v6 = SBYTE1(v4) + 12;
        goto LABEL_9;
      case 3:
        v6 = SBYTE1(v4) + 18;
        goto LABEL_9;
      case 4:
        v6 = SBYTE1(v4) + 24;
LABEL_9:
        v5 = 80 * v6;
        break;
      default:
        break;
    }
    lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxHP += ((BYTE2(attackType) & 0xE) != 2 ? -1 : 1) * v5;
    return 0;
  }
}

//----- (00426D50) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::ClawMastery(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  if ( !lpSkillUser || lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ClawMastery",
      aDWorkRylSource_68,
      2825,
      (char *)&byte_4DBE38,
      lpSkillUser->m_dwCID,
      lpVictim->m_dwCID);
  }
  else if ( lpSkillUser->UpdateMasteryInfo(lpSkillUser, 7u, (*((_BYTE *)&attackType + 2) >> 1) & 7) )
  {
    Skill::CFunctions::WeaponMastery(attackType, lpSkillUser);
    return 0;
  }
  return 0;
}

//----- (00426DC0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::UseFood(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v7; // cx
  unsigned __int16 m_nMaxHP; // cx

  if ( lpSkillUser && lpVictim )
  {
    if ( (*((_BYTE *)&attackType + 2) & 0xF0u) < 0x30 )
    {
      v7 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v7 = 200;
          break;
        case 1:
          v7 = 400;
          break;
        case 2:
          v7 = 600;
          break;
        case 3:
        case 4:
          v7 = 0;
          break;
        default:
          break;
      }
      lpVictim->m_CreatureStatus.m_nNowHP += v7;
      m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      if ( lpVictim->m_CreatureStatus.m_nNowHP > m_nMaxHP )
        lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
      *cDefenserJudge = 5;
      return 0;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::UseFood",
        aDWorkRylSource_68,
        3597,
        aCid0x08x_268,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        *((unsigned __int8 *)&attackType + 2) >> 4);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::UseFood",
      aDWorkRylSource_68,
      3592,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00426EB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::UseFood2(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v7; // ax
  __int16 v8; // cx
  unsigned __int16 m_nMaxHP; // ax
  unsigned __int16 m_nMaxMP; // ax

  if ( lpSkillUser && lpVictim )
  {
    if ( (*((_BYTE *)&attackType + 2) & 0xF0u) < 0x30 )
    {
      v7 = 0;
      v8 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v7 = 3000;
          goto LABEL_10;
        case 1:
          v7 = 1500;
          v8 = 1500;
          goto LABEL_11;
        case 2:
          v7 = 0;
          v8 = 3000;
          *cDefenserJudge = 6;
          break;
        case 3:
        case 4:
          v7 = 0;
LABEL_10:
          v8 = 0;
LABEL_11:
          *cDefenserJudge = 5;
          break;
        default:
          break;
      }
      lpVictim->m_CreatureStatus.m_nNowHP += v7;
      m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      if ( lpVictim->m_CreatureStatus.m_nNowHP > m_nMaxHP )
        lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
      lpVictim->m_CreatureStatus.m_nNowMP += v8;
      m_nMaxMP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      if ( lpVictim->m_CreatureStatus.m_nNowMP > m_nMaxMP )
        lpVictim->m_CreatureStatus.m_nNowMP = m_nMaxMP;
      *cDefenserJudge = 5;
      return 0;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::UseFood2",
        aDWorkRylSource_68,
        3629,
        aCid0x08x_268,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        *((unsigned __int8 *)&attackType + 2) >> 4);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::UseFood2",
      aDWorkRylSource_68,
      3624,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00426FE0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::RefreshmentPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v6; // di
  unsigned __int64 v7; // rax
  double m_nMaxHP; // st7
  unsigned __int64 v9; // rax
  double v10; // st7
  double v11; // st7
  double v12; // st7
  double v13; // st7
  double v14; // st7
  unsigned __int16 v15; // cx
  unsigned __int16 m_nMaxMP; // ax

  if ( lpSkillUser && lpVictim )
  {
    v6 = 0;
    LOWORD(v7) = 0;
    switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
    {
      case 0:
        m_nMaxHP = (double)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v9 = (unsigned __int64)(0.1 * m_nMaxHP);
        v10 = m_nMaxHP * 0.050000001;
        goto LABEL_9;
      case 1:
        v11 = (double)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v9 = (unsigned __int64)(0.2 * v11);
        v10 = v11 * 0.1;
        goto LABEL_9;
      case 2:
        v12 = (double)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v9 = (unsigned __int64)(0.30000001 * v12);
        v10 = v12 * 0.15000001;
        goto LABEL_9;
      case 3:
        v13 = (double)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v9 = (unsigned __int64)(0.40000001 * v13);
        v10 = v13 * 0.2;
        goto LABEL_9;
      case 4:
        v14 = (double)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v9 = (unsigned __int64)(0.5 * v14);
        v10 = v14 * 0.25;
LABEL_9:
        v6 = v9;
        v7 = (unsigned __int64)v10;
        break;
      default:
        break;
    }
    lpVictim->m_CreatureStatus.m_nNowHP += v6;
    v15 = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    if ( lpVictim->m_CreatureStatus.m_nNowHP > v15 )
      lpVictim->m_CreatureStatus.m_nNowHP = v15;
    lpVictim->m_CreatureStatus.m_nNowMP += v7;
    m_nMaxMP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
    if ( lpVictim->m_CreatureStatus.m_nNowMP > m_nMaxMP )
      lpVictim->m_CreatureStatus.m_nNowMP = m_nMaxMP;
    *cDefenserJudge = 5;
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::RefreshmentPotion",
      aDWorkRylSource_68,
      3678,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00427170) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::MoonCake(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 m_nMaxHP; // cx
  unsigned __int16 m_nMaxMP; // cx

  if ( lpSkillUser && lpVictim )
  {
    lpVictim->m_CreatureStatus.m_nNowHP += 1200;
    m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    if ( lpVictim->m_CreatureStatus.m_nNowHP > m_nMaxHP )
      lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
    lpVictim->m_CreatureStatus.m_nNowMP += 500;
    m_nMaxMP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
    if ( lpVictim->m_CreatureStatus.m_nNowMP > m_nMaxMP )
      lpVictim->m_CreatureStatus.m_nNowMP = m_nMaxMP;
    *cDefenserJudge = 5;
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::MoonCake",
      aDWorkRylSource_68,
      3845,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00427200) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FireCracker(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  if ( lpSkillUser && lpVictim )
  {
    *cDefenserJudge = 14;
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FireCracker",
      aDWorkRylSource_68,
      3929,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00427250) --------------------------------------------------------
void __thiscall Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(
        Skill::CAddSpell<CPoisonedSpell> *this,
        const CSpell::Spell_Info *Spell_Info)
{
  this->m_Spell_Info = *Spell_Info;
}

//----- (00427280) --------------------------------------------------------
CConsoleCMDFactory::StringCMD *__cdecl std::_Copy_backward_opt<CTokenlizedFile::ColumnInfo *,CTokenlizedFile::ColumnInfo *>(
        CConsoleCMDFactory::StringCMD *_First,
        CConsoleCMDFactory::StringCMD *_Last,
        CConsoleCMDFactory::StringCMD *_Dest)
{
  CConsoleCMDFactory::StringCMD *v3; // ecx
  CConsoleCMDFactory::StringCMD *result; // eax

  v3 = _Last;
  for ( result = _Dest; v3 != _First; result->m_lpCMD = v3->m_lpCMD )
  {
    --v3;
    --result;
    result->m_dwHashValue = v3->m_dwHashValue;
    result->m_szCommand = v3->m_szCommand;
  }
  return result;
}

//----- (004272C0) --------------------------------------------------------
void __thiscall Skill::CProcessTable::CProcessTable(Skill::CProcessTable *this)
{
  CSingleton<Skill::CProcessTable>::ms_pSingleton = this;
  this->m_usProcessInfo = 0;
  this->m_fnProcessTable = 0;
}

//----- (004272E0) --------------------------------------------------------
void __thiscall Skill::CProcessTable::~CProcessTable(Skill::CProcessTable *this)
{
  if ( this->m_fnProcessTable )
  {
    operator delete[](this->m_fnProcessTable);
    this->m_fnProcessTable = 0;
  }
  CSingleton<Skill::CProcessTable>::ms_pSingleton = 0;
}

//----- (00427310) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Detection(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Detection",
      aDWorkRylSource_68,
      489,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, NONE, COMMON, NONE, 0, 0xFFu);
  if ( CAffectedSpell::RemoveEnchantBySpellType(&lpVictim->m_SpellMgr.m_AffectedInfo, 0x10000u) == 1 )
  {
    CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
    if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
      lpVictim->__vftable[1].GetGID(lpVictim);
  }
  *cDefenserJudge = 9;
  return 0;
}

//----- (004273B0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::NeedleSpit(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v4; // si
  double v5; // st7

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::NeedleSpit",
      aDWorkRylSource_68,
      510,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v4 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v4 = 40 * attackType.m_cSkillLevel;
      break;
    case 1:
      v4 = 80 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v4 = 120 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v4 = 160 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v4 = 200 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v5 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
  return ((signed __int64 (__usercall *)@<edx:eax>(_DWORD, _DWORD, _DWORD, double@<st0>))_ftol2)(
           ProtoType,
           attackType,
           v4,
           v5);
}
// 4C0ECC: using guessed type signed __int64 __usercall _ftol2@<edx:eax>(_DWORD, _DWORD, _DWORD, double@<st0>);

//----- (004274A0) --------------------------------------------------------
__int16 __cdecl Skill::CFunctions::VampiricTouch(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge)
{
  __int16 result; // ax
  __int16 v7; // ax
  __int16 v8; // si
  __int16 v9; // bp
  int v10; // eax
  __int16 v11; // si
  __int16 v12; // si
  __int16 v13; // si
  __int16 v14; // bp
  __int16 v15; // ax
  unsigned __int16 m_nMaxHP; // cx
  unsigned __int16 m_nMaxMP; // ax
  float v18; // [esp+4h] [ebp-10h]
  float v19; // [esp+8h] [ebp-Ch]
  __int16 lpSkillUsera; // [esp+20h] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      return 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v7 = *((_BYTE *)&attackType + 2) >> 4;
      if ( (unsigned __int16)v7 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::VampiricTouch",
          aDWorkRylSource_68,
          597,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v7);
        return 0;
      }
      else
      {
        v8 = 0;
        v9 = 1;
        lpSkillUsera = 1;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v10 = v7;
            v11 = nMultiplyLockCountBonus[v10] + 50;
            v9 = 2;
            goto LABEL_16;
          case 1:
            v10 = v7;
            v9 = 2;
            lpSkillUsera = 2;
            v12 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus[v10] + 100);
            goto LABEL_17;
          case 2:
            v10 = v7;
            v11 = nMultiplyLockCountBonus[v10] + 150;
            lpSkillUsera = 2;
            goto LABEL_15;
          case 3:
            v10 = v7;
            v11 = nMultiplyLockCountBonus[v10] + 200;
            v9 = 2;
            lpSkillUsera = 1;
            goto LABEL_16;
          case 4:
            v10 = v7;
            v11 = nMultiplyLockCountBonus[v10] + 250;
            lpSkillUsera = 1;
LABEL_15:
            v9 = 1;
LABEL_16:
            v12 = attackType.m_cSkillLevel * v11;
LABEL_17:
            v8 = nPlusLockCountBonus[v10] + v12;
            break;
          default:
            break;
        }
        v19 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
        v18 = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
        v13 = (unsigned __int64)((double)(v8
                                        * (Math::Random::ComplexRandom(50, 0)
                                         + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                         + 100))
                               * v18
                               * v19
                               * 0.000099999997);
        v14 = v13 / v9;
        if ( (*((_BYTE *)&attackType + 2) & 0xF0) != 0 )
          v15 = v13 / lpSkillUsera;
        else
          v15 = 0;
        lpSkillUser->m_CreatureStatus.m_nNowHP += v14;
        m_nMaxHP = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        if ( lpSkillUser->m_CreatureStatus.m_nNowHP > m_nMaxHP )
          lpSkillUser->m_CreatureStatus.m_nNowHP = m_nMaxHP;
        lpSkillUser->m_CreatureStatus.m_nNowMP += v15;
        m_nMaxMP = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
        if ( lpSkillUser->m_CreatureStatus.m_nNowMP > m_nMaxMP )
          lpSkillUser->m_CreatureStatus.m_nNowMP = m_nMaxMP;
        result = v13;
        *cOffencerJudge = 5;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::VampiricTouch",
      aDWorkRylSource_68,
      577,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  return result;
}

//----- (00427740) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Purification(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) != -1 )
    {
      CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, MASTER, COMMON, NONE, 0, 0xFFu);
      *cOffencerJudge = 9;
      *cDefenserJudge = 9;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Purification",
      aDWorkRylSource_68,
      823,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
  }
  return 0;
}

//----- (004277C0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FirstAid(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 v6; // cx
  unsigned __int64 v7; // rax
  unsigned __int16 m_nNowHP; // cx
  unsigned __int16 m_nMaxHP; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FirstAid",
      aDWorkRylSource_68,
      904,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v6 = 100 * attackType.m_cSkillLevel;
      break;
    case 1:
      v6 = 200 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v6 = 300 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v6 = 400 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v6 = 500 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v7 = (unsigned __int64)((double)(lpSkillUser->m_CharacterStatus.m_nWIS / 6 + 100) * 0.********** * (double)v6);
  m_nNowHP = lpVictim->m_CreatureStatus.m_nNowHP;
  if ( m_nNowHP < (unsigned __int16)v7 && m_nNowHP )
    lpVictim->m_CreatureStatus.m_nNowHP = v7;
  m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  if ( lpVictim->m_CreatureStatus.m_nNowHP > m_nMaxHP )
    lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
  CThreat::HealThreat(&lpVictim->m_Threat, lpSkillUser, (lpVictim->m_CreatureStatus.m_nNowHP - m_nNowHP) / 2);
  *cDefenserJudge = 5;
  return 0;
}

//----- (00427920) --------------------------------------------------------
__int16 __cdecl Skill::CFunctions::SharedPain(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v4; // si
  __int16 v5; // bp
  double v6; // st7
  unsigned __int16 m_nNowHP; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::SharedPain",
      aDWorkRylSource_68,
      1059,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v4 = 0;
  v5 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v4 = 10 * (attackType.m_cSkillLevel + 2);
      v6 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * (double)(30 * attackType.m_cSkillLevel);
      goto LABEL_10;
    case 1:
      v4 = 20 * (attackType.m_cSkillLevel + 4);
      v6 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
         * (double)(60 * (attackType.m_cSkillLevel + 3));
      goto LABEL_10;
    case 2:
      v4 = 30 * attackType.m_cSkillLevel + 200;
      v6 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
         * (double)(90 * (attackType.m_cSkillLevel + 6));
      goto LABEL_10;
    case 3:
      v4 = 40 * attackType.m_cSkillLevel + 380;
      v6 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
         * (double)(120 * (attackType.m_cSkillLevel + 9));
      goto LABEL_10;
    case 4:
      v4 = 50 * attackType.m_cSkillLevel + 620;
      v6 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
         * (double)(150 * (attackType.m_cSkillLevel + 12));
LABEL_10:
      v5 = (unsigned __int64)v6;
      break;
    default:
      break;
  }
  m_nNowHP = lpSkillUser->m_CreatureStatus.m_nNowHP;
  if ( m_nNowHP > v4 )
  {
    lpSkillUser->m_CreatureStatus.m_nNowHP = m_nNowHP - v4;
    CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v5, TAUNT);
    return v5;
  }
  else
  {
    lpSkillUser->m_CreatureStatus.m_nNowHP = 0;
    return 0;
  }
}

//----- (00427AF0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BattleSong(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature *v6; // edi
  __int16 v8; // si
  CAggresiveCreature *v9; // ebp
  CSpell::Spell_Info *v10; // eax
  CSpell *v11; // eax
  CSpell *v12; // esi
  CAggresiveCreature *v13; // ebx
  CAggresiveCreature_vtbl *v14; // edx
  int v15; // eax
  CGlobalSpellMgr *Instance; // eax
  CSpell::Spell_Info v17; // [esp+4h] [ebp-1Ch] BYREF
  int v18; // [esp+1Ch] [ebp-4h]

  v6 = lpSkillUser;
  if ( lpSkillUser && lpVictim )
  {
    if ( (*((_BYTE *)&attackType + 2) & 0xF0) != 0 || attackType.m_cSkillLevel )
    {
      v8 = Skill::CFunctions::ConsumeMP(attackType, lpSkillUser);
      if ( v8 != -1 )
      {
        v9 = (CAggresiveCreature *)operator new((tagHeader *)0x54);
        lpSkillUser = v9;
        v18 = 0;
        if ( v9 )
        {
          CSpell::Spell_Info::Spell_Info(
            &v17,
            ProtoType,
            v6,
            1u,
            0x81u,
            nSkillLevels[*((unsigned __int8 *)&attackType + 2) >> 4] + attackType.m_cSkillLevel,
            0xFFFFu);
          CBattleSongSpell::CBattleSongSpell((CBattleSongSpell *)v9, v10, v8);
          v12 = v11;
        }
        else
        {
          v12 = 0;
        }
        v13 = lpVictim;
        v14 = lpVictim->__vftable;
        v18 = -1;
        v15 = (int)v14->GetParty(lpVictim);
        if ( v15 )
        {
          CPartySpellMgr::AddAffectedToAllMember((CPartySpellMgr *)(v15 + 4), v12, v6->m_CellPos.m_wMapIndex);
        }
        else
        {
          lpSkillUser = 0;
          CSpell::AddAffected(v12, v13, (unsigned __int16 *)&lpSkillUser);
        }
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v12);
        *cDefenserJudge = 7;
      }
      return 0;
    }
    else
    {
      CCastingSpell::ClearChant(&lpSkillUser->m_SpellMgr.m_CastingInfo);
      *cDefenserJudge = 7;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BattleSong",
      aDWorkRylSource_68,
      1158,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 427BC2: variable 'v10' is possibly undefined
// 427BC7: variable 'v11' is possibly undefined
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00427C80) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BackStab(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  int v6; // eax
  double v7; // st7
  int v8; // edi
  unsigned __int16 result; // ax
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BackStab",
      aDWorkRylSource_68,
      1225,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = *((unsigned __int8 *)&attackType + 2) >> 4;
  v7 = (double)attackType.m_cSkillLevel * 0.050000001 + fSwordDRCs[v6];
  AddEffectInfo.m_nMinDamage = attackType.m_cSkillLevel + nSkillLevels[v6];
  AddEffectInfo.m_fDRC = v7;
  AddEffectInfo.m_nMaxDamage = AddEffectInfo.m_nMinDamage;
  AddEffectInfo.m_bForceDRC = 0;
  AddEffectInfo.m_nOffenceRevision = 0;
  v8 = CAggresiveCreature::CalculateDamage(
         lpVictim,
         (int)lpSkillUser,
         COERCE_FLOAT(&AddEffectInfo),
         *(float *)&cDefenserJudge);
  CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v8, DETAUNT);
  result = 4 * v8;
  if ( (lpSkillUser->m_dwStatusFlag & 0x10000) == 0 )
    return v8;
  return result;
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00427D70) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::AimedShot(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  int v6; // eax
  double v7; // st7
  unsigned __int16 result; // ax
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::AimedShot",
      aDWorkRylSource_68,
      1288,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = *((unsigned __int8 *)&attackType + 2) >> 4;
  v7 = (double)attackType.m_cSkillLevel * 0.050000001 + fSwordDRCs[v6];
  AddEffectInfo.m_nMinDamage = attackType.m_cSkillLevel + nSkillLevels[v6];
  AddEffectInfo.m_nMaxDamage = AddEffectInfo.m_nMinDamage;
  AddEffectInfo.m_fDRC = v7;
  AddEffectInfo.m_bForceDRC = 0;
  AddEffectInfo.m_nOffenceRevision = 0;
  result = CAggresiveCreature::CalculateDamage(
             lpVictim,
             (int)lpSkillUser,
             COERCE_FLOAT(&AddEffectInfo),
             *(float *)&cDefenserJudge);
  if ( (lpSkillUser->m_dwStatusFlag & 0x10000) != 0 )
    result *= 4;
  return result;
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00427E50) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DualShot(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  int v6; // eax
  double v7; // st7
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) != -1 )
    {
      v6 = *((unsigned __int8 *)&attackType + 2) >> 4;
      v7 = (double)attackType.m_cSkillLevel * 0.050000001 + fDRCs[v6];
      AddEffectInfo.m_nMinDamage = attackType.m_cSkillLevel + nSkillLevels[v6];
      AddEffectInfo.m_nMaxDamage = AddEffectInfo.m_nMinDamage;
      AddEffectInfo.m_fDRC = v7;
      AddEffectInfo.m_bForceDRC = 0;
      AddEffectInfo.m_nOffenceRevision = 0;
      return CAggresiveCreature::CalculateDamage(
               lpVictim,
               (int)lpSkillUser,
               COERCE_FLOAT(&AddEffectInfo),
               *(float *)&cDefenserJudge);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DualShot",
      aDWorkRylSource_68,
      1307,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
  }
  return 0;
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00427F20) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Recall(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CCharacter *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CCharacterParty *v6; // ecx

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Recall",
      aDWorkRylSource_68,
      1356,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  if ( lpVictim->GetParty(lpVictim) )
  {
    if ( !Creature::GetCreatureType(lpVictim->m_dwCID) )
      CCharacterParty::SendRecall(v6, lpVictim);
  }
  *cOffencerJudge = 14;
  *cDefenserJudge = 14;
  return 0;
}
// 427F5F: variable 'v6' is possibly undefined

//----- (00427FB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FireBolt(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int64 v5; // rax
  __int16 v6; // ax
  __int16 v7; // si
  int v8; // eax
  __int16 v9; // si
  __int16 v10; // si
  float v12; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v5) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v6 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v6 = *((_BYTE *)&attackType + 2) >> 4;
      if ( (unsigned __int16)v6 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::FireBolt",
          aDWorkRylSource_68,
          1398,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v6);
        LOWORD(v5) = 0;
      }
      else
      {
        v7 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 80;
            goto LABEL_15;
          case 1:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 160);
            goto LABEL_16;
          case 2:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 240;
            goto LABEL_15;
          case 3:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 320);
            goto LABEL_16;
          case 4:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 400;
LABEL_15:
            v10 = attackType.m_cSkillLevel * v9;
LABEL_16:
            v7 = nPlusLockCountBonus_0[v8] + v10;
            break;
          default:
            break;
        }
        v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
        lpSkillUsera = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
        return (unsigned __int64)((double)(v7
                                         * (Math::Random::ComplexRandom(50, 0)
                                          + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                          + 100))
                                * lpSkillUsera
                                * v12
                                * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FireBolt",
      aDWorkRylSource_68,
      1378,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v5) = 0;
  }
  return v5;
}

//----- (004281A0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DeathRay(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v5; // si
  __int64 v6; // rax
  float v8; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DeathRay",
      aDWorkRylSource_68,
      1627,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    goto LABEL_12;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
  {
LABEL_12:
    LOWORD(v6) = 0;
    return v6;
  }
  v5 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v5 = 80 * attackType.m_cSkillLevel;
      break;
    case 1:
      v5 = 160 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v5 = 240 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v5 = 320 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v5 = 400 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v8 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
  lpSkillUsera = (double)(__int16)(100
                                 * (99
                                  * lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
                                  / (lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance + 50)))
               * 0.**********;
  return (unsigned __int64)((double)(v5
                                   * (Math::Random::ComplexRandom(50, 0)
                                    + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                    + 100))
                          * (100.0 - lpSkillUsera)
                          * v8
                          * 0.000099999997);
}

//----- (00428300) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::SummonKindling(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CCharacter *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 v6; // si
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CCellManager *v10; // eax
  __int128 v12; // [esp-10h] [ebp-24h]
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-18h]
  Position DestPos; // [esp+8h] [ebp-Ch]

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::SummonKindling",
      aDWorkRylSource_68,
      1917,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) != -1 )
  {
    v6 = 0;
    switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
    {
      case 0:
        v6 = attackType.m_cSkillLevel + 999;
        break;
      case 1:
        v6 = attackType.m_cSkillLevel + 1006;
        break;
      case 2:
        v6 = attackType.m_cSkillLevel + 1013;
        break;
      case 3:
        v6 = attackType.m_cSkillLevel + 1020;
        break;
      case 4:
        v6 = attackType.m_cSkillLevel + 1027;
        break;
      default:
        break;
    }
    DestPos.m_fPointX = lpSkillUser->m_CurrentPos.m_fPointX
                      - cos(lpSkillUser->m_MotionInfo.m_fDirection) * ProtoType->m_EffectDistance;
    DestPos.m_fPointZ = sin(lpSkillUser->m_MotionInfo.m_fDirection) * ProtoType->m_EffectDistance
                      + lpSkillUser->m_CurrentPos.m_fPointZ;
    DestPos.m_fPointY = lpSkillUser->m_CurrentPos.m_fPointY + 1.0;
    if ( (lpSkillUser->m_dwCID & 0xD0000000) == 0 )
    {
      if ( lpSkillUser->m_CellPos.m_wMapIndex )
      {
        m_wMapIndex = lpSkillUser->m_CellPos.m_wMapIndex;
        Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
        VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
        if ( VirtualArea )
        {
          m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
          if ( !m_pVirtualMonsterMgr
            || (*(_QWORD *)&v12 = *(_QWORD *)&DestPos.m_fPointX,
                *((_QWORD *)&v12 + 1) = __PAIR64__((unsigned int)lpSkillUser, LODWORD(DestPos.m_fPointZ)),
                !CVirtualMonsterMgr::SummonMonster(m_pVirtualMonsterMgr, v6, v12)) )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "Skill::CFunctions::SummonKindling",
              aDWorkRylSource_68,
              1950,
              aCid0x08x_179,
              lpSkillUser->m_dwCID,
              v6);
            return 0;
          }
        }
      }
      else
      {
        v10 = CCellManager::GetInstance();
        if ( !CCellManager::SummonMonster(v10, v6, DestPos, lpSkillUser) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Skill::CFunctions::SummonKindling",
            aDWorkRylSource_68,
            1960,
            aCid0x08x_179,
            lpSkillUser->m_dwCID,
            v6);
          return 0;
        }
      }
    }
    *cOffencerJudge = 14;
    *cDefenserJudge = 14;
    return 0;
  }
  return 0;
}

//----- (004284D0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Resurrection(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CCharacter *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  CSendStream *v8; // edi

  if ( lpSkillUser && lpVictim )
  {
    if ( !lpVictim->m_CreatureStatus.m_nNowHP )
    {
      if ( (lpSkillUser->m_dwCID & 0xD0000000) != 0 || Creature::GetCreatureType(lpVictim->m_dwCID) )
        goto LABEL_11;
      if ( lpSkillUser->IsPeaceMode(lpSkillUser) && !lpVictim->IsPeaceMode(lpVictim) )
      {
        *wError = 5;
        return 0;
      }
      v8 = (CSendStream *)lpVictim[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[3];
      if ( v8 )
      {
        if ( GameClientSendPacket::SendCharAuthorizePanel(v8 + 8, lpSkillUser, 1u) )
        {
LABEL_11:
          if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) != -1 )
          {
            *cOffencerJudge = 14;
            *cDefenserJudge = 14;
            return 0;
          }
        }
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Resurrection",
      aDWorkRylSource_68,
      2070,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
  }
  return 0;
}

//----- (004285B0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::MaintenanceChant(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature *v6; // edi
  unsigned int v7; // ebx
  __int16 v9; // bp
  unsigned int v10; // esi
  int v11; // ecx
  int v12; // edx
  unsigned int v13; // edx
  CAggresiveCreature *v14; // ebx
  CSpell::Spell_Info *v15; // eax
  CSpell *v16; // eax
  CSpell *v17; // esi
  CAggresiveCreature *v18; // ebx
  CAggresiveCreature_vtbl *v19; // edx
  int v20; // eax
  CGlobalSpellMgr *Instance; // eax
  CSpell::Spell_Info v22; // [esp+4h] [ebp-1Ch] BYREF
  int v23; // [esp+1Ch] [ebp-4h]

  v6 = lpSkillUser;
  if ( lpSkillUser && lpVictim )
  {
    v7 = HIWORD(attackType);
    if ( (attackType & 0xF00000) != 0 || BYTE1(v7) )
    {
      v9 = Skill::CFunctions::ConsumeMP((AtType)attackType, lpSkillUser);
      if ( v9 == -1 )
      {
        return 0;
      }
      else
      {
        LOWORD(v10) = 0;
        switch ( BYTE2(attackType) >> 4 )
        {
          case 0:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7);
            goto LABEL_14;
          case 1:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 6;
            goto LABEL_14;
          case 2:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 12;
            goto LABEL_14;
          case 3:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 18;
            goto LABEL_14;
          case 4:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 24;
LABEL_14:
            v13 = (int)((unsigned __int64)(1374389535LL * v12 * v11) >> 32) >> 5;
            v10 = v13 + (v13 >> 31);
            break;
          default:
            break;
        }
        v14 = (CAggresiveCreature *)operator new((tagHeader *)0x54);
        lpSkillUser = v14;
        v23 = 0;
        if ( v14 )
        {
          CSpell::Spell_Info::Spell_Info(&v22, ProtoType, v6, 1u, 0x82u, v10, 0xFFFFu);
          CMaintenanceChantSpell::CMaintenanceChantSpell((CMaintenanceChantSpell *)v14, v15, v9);
          v17 = v16;
        }
        else
        {
          v17 = 0;
        }
        v18 = lpVictim;
        v19 = lpVictim->__vftable;
        v23 = -1;
        v20 = (int)v19->GetParty(lpVictim);
        if ( v20 )
        {
          CPartySpellMgr::AddAffectedToAllMember((CPartySpellMgr *)(v20 + 4), v17, v6->m_CellPos.m_wMapIndex);
        }
        else
        {
          lpSkillUser = 0;
          CSpell::AddAffected(v17, v18, (unsigned __int16 *)&lpSkillUser);
        }
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v17);
        *cDefenserJudge = 7;
        return 0;
      }
    }
    else
    {
      CCastingSpell::ClearChant(&lpSkillUser->m_SpellMgr.m_CastingInfo);
      *cDefenserJudge = 7;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::MaintenanceChant",
      aDWorkRylSource_68,
      2109,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 428743: variable 'v15' is possibly undefined
// 428748: variable 'v16' is possibly undefined

//----- (00428810) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::AccelerationChant(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature *v6; // edi
  unsigned int v7; // ebx
  __int16 v9; // bp
  unsigned int v10; // esi
  int v11; // ecx
  int v12; // edx
  unsigned int v13; // edx
  CAggresiveCreature *v14; // ebx
  CSpell::Spell_Info *v15; // eax
  CSpell *v16; // eax
  CSpell *v17; // esi
  CAggresiveCreature *v18; // ebx
  CAggresiveCreature_vtbl *v19; // edx
  int v20; // eax
  CGlobalSpellMgr *Instance; // eax
  CSpell::Spell_Info v22; // [esp+4h] [ebp-1Ch] BYREF
  int v23; // [esp+1Ch] [ebp-4h]

  v6 = lpSkillUser;
  if ( lpSkillUser && lpVictim )
  {
    v7 = HIWORD(attackType);
    if ( (attackType & 0xF00000) != 0 || BYTE1(v7) )
    {
      v9 = Skill::CFunctions::ConsumeMP((AtType)attackType, lpSkillUser);
      if ( v9 == -1 )
      {
        return 0;
      }
      else
      {
        LOWORD(v10) = 0;
        switch ( BYTE2(attackType) >> 4 )
        {
          case 0:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7);
            goto LABEL_14;
          case 1:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 6;
            goto LABEL_14;
          case 2:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 12;
            goto LABEL_14;
          case 3:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 18;
            goto LABEL_14;
          case 4:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 24;
LABEL_14:
            v13 = (int)((unsigned __int64)(1374389535LL * v12 * v11) >> 32) >> 5;
            v10 = v13 + (v13 >> 31);
            break;
          default:
            break;
        }
        v14 = (CAggresiveCreature *)operator new((tagHeader *)0x54);
        lpSkillUser = v14;
        v23 = 0;
        if ( v14 )
        {
          CSpell::Spell_Info::Spell_Info(&v22, ProtoType, v6, 1u, 0x83u, v10, 0xFFFFu);
          CAccelerationChantSpell::CAccelerationChantSpell((CAccelerationChantSpell *)v14, v15, v9);
          v17 = v16;
        }
        else
        {
          v17 = 0;
        }
        v18 = lpVictim;
        v19 = lpVictim->__vftable;
        v23 = -1;
        v20 = (int)v19->GetParty(lpVictim);
        if ( v20 )
        {
          CPartySpellMgr::AddAffectedToAllMember((CPartySpellMgr *)(v20 + 4), v17, v6->m_CellPos.m_wMapIndex);
        }
        else
        {
          lpSkillUser = 0;
          CSpell::AddAffected(v17, v18, (unsigned __int16 *)&lpSkillUser);
        }
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v17);
        *cDefenserJudge = 7;
        return 0;
      }
    }
    else
    {
      CCastingSpell::ClearChant(&lpSkillUser->m_SpellMgr.m_CastingInfo);
      *cDefenserJudge = 7;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::AccelerationChant",
      aDWorkRylSource_68,
      2155,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4289A3: variable 'v15' is possibly undefined
// 4289A8: variable 'v16' is possibly undefined

//----- (00428A70) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::ManaFlow(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v6; // si
  unsigned __int16 m_nMaxMP; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ManaFlow",
      aDWorkRylSource_68,
      2267,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v6 = 100 * attackType.m_cSkillLevel;
      break;
    case 1:
      v6 = 200 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v6 = 300 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v6 = 400 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v6 = 500 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  lpVictim->m_CreatureStatus.m_nNowMP += v6
                                       * (unsigned __int64)((double)(Math::Random::ComplexRandom(50, 0)
                                                                   + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                                                   + 100)
                                                          * 0.**********);
  m_nMaxMP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  if ( lpVictim->m_CreatureStatus.m_nNowMP > m_nMaxMP )
    lpVictim->m_CreatureStatus.m_nNowMP = m_nMaxMP;
  *cDefenserJudge = 6;
  return 0;
}

//----- (00428BB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::CureWounds(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v6; // ax
  unsigned __int16 v7; // si
  int v8; // eax
  __int16 v9; // si
  __int16 v10; // si
  unsigned __int64 v11; // rax
  unsigned __int16 m_nMaxHP; // dx
  unsigned __int16 m_nNowHP; // cx

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v6 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v6 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v6 <= 4u )
    {
      v7 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v8 = v6;
          v9 = nMultiplyLockCountBonus_0[v8] + 80;
          goto LABEL_14;
        case 1:
          v8 = v6;
          v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 160);
          goto LABEL_15;
        case 2:
          v8 = v6;
          v9 = nMultiplyLockCountBonus_0[v8] + 240;
          goto LABEL_14;
        case 3:
          v8 = v6;
          v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 320);
          goto LABEL_15;
        case 4:
          v8 = v6;
          v9 = nMultiplyLockCountBonus_0[v8] + 400;
LABEL_14:
          v10 = attackType.m_cSkillLevel * v9;
LABEL_15:
          v7 = nPlusLockCountBonus_0[v8] + v10;
          break;
        default:
          break;
      }
      v11 = (unsigned __int64)((double)(v7
                                      * (Math::Random::ComplexRandom(50, 0)
                                       + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                       + 100))
                             * 0.**********);
      m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      m_nNowHP = lpVictim->m_CreatureStatus.m_nNowHP;
      LOWORD(v11) = m_nNowHP + v11;
      lpVictim->m_CreatureStatus.m_nNowHP = v11;
      if ( (unsigned __int16)v11 > m_nMaxHP )
        lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
      CThreat::HealThreat(&lpVictim->m_Threat, lpSkillUser, (lpVictim->m_CreatureStatus.m_nNowHP - m_nNowHP) / 2);
      *cDefenserJudge = 5;
      if ( *((_BYTE *)&attackType + 2) >> 4 )
      {
        if ( *((_BYTE *)&attackType + 2) >> 4 != 2 )
          CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, MASTER, COMMON, MIDDLE_ADMIN, 0, 1u);
      }
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::CureWounds",
      aDWorkRylSource_68,
      2400,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v6);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::CureWounds",
      aDWorkRylSource_68,
      2380,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00428DE0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::WoundsCrafting(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 v6; // si
  unsigned __int16 v7; // di
  unsigned __int64 v8; // rax
  unsigned __int16 m_nNowHP; // cx
  unsigned __int16 v11; // cx

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    v6 = 0;
    v7 = 0;
    switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
    {
      case 0:
        v6 = 80 * attackType.m_cSkillLevel;
        v7 = 2 * (5 * attackType.m_cSkillLevel + 10);
        break;
      case 1:
        v6 = 32 * (5 * attackType.m_cSkillLevel + 15);
        v7 = 4 * (5 * attackType.m_cSkillLevel + 20);
        break;
      case 2:
        v6 = 240 * (attackType.m_cSkillLevel + 6);
        v7 = 30 * attackType.m_cSkillLevel + 200;
        break;
      case 3:
        v6 = (5 * attackType.m_cSkillLevel + 45) << 6;
        v7 = 40 * attackType.m_cSkillLevel + 380;
        break;
      case 4:
        v6 = 400 * (attackType.m_cSkillLevel + 12);
        v7 = 50 * attackType.m_cSkillLevel + 620;
        break;
      default:
        break;
    }
    v8 = (unsigned __int64)((double)(v6
                                   * (Math::Random::ComplexRandom(50, 0)
                                    + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                    + 100))
                          * 0.**********);
    m_nNowHP = lpSkillUser->m_CreatureStatus.m_nNowHP;
    if ( m_nNowHP <= v7 )
    {
      lpSkillUser->m_CreatureStatus.m_nNowHP = 0;
      return 0;
    }
    lpSkillUser->m_CreatureStatus.m_nNowHP = m_nNowHP - v7;
    WORD2(v8) = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    v11 = lpVictim->m_CreatureStatus.m_nNowHP;
    LOWORD(v8) = v11 + v8;
    lpVictim->m_CreatureStatus.m_nNowHP = v8;
    if ( (unsigned __int16)v8 > WORD2(v8) )
      lpVictim->m_CreatureStatus.m_nNowHP = WORD2(v8);
    CThreat::HealThreat(&lpVictim->m_Threat, lpSkillUser, (lpVictim->m_CreatureStatus.m_nNowHP - v11) / 2);
    *cDefenserJudge = 5;
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::WoundsCrafting",
      aDWorkRylSource_68,
      2456,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (00428F90) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::CureLight(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 v6; // si
  unsigned __int64 v7; // rax
  unsigned __int16 m_nNowHP; // dx
  unsigned __int16 *p_m_nMaxHP; // eax
  unsigned __int16 v10; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::CureLight",
      aDWorkRylSource_68,
      2513,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v6 = 50 * attackType.m_cSkillLevel;
      break;
    case 1:
      v6 = 100 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v6 = 150 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v6 = 200 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v6 = 250 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v7 = (unsigned __int64)((double)(v6
                                 * (Math::Random::ComplexRandom(50, 0)
                                  + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                  + 100))
                        * 0.**********);
  m_nNowHP = lpVictim->m_CreatureStatus.m_nNowHP;
  lpVictim->m_CreatureStatus.m_nNowHP = m_nNowHP + v7;
  p_m_nMaxHP = &lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  if ( lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP >= lpVictim->m_CreatureStatus.m_nNowHP )
    p_m_nMaxHP = &lpVictim->m_CreatureStatus.m_nNowHP;
  v10 = *p_m_nMaxHP;
  lpVictim->m_CreatureStatus.m_nNowHP = v10;
  CThreat::HealThreat(&lpVictim->m_Threat, lpSkillUser, (v10 - m_nNowHP) / 2);
  *cDefenserJudge = 5;
  return 0;
}

//----- (004290F0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::WoundsMake(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v5; // si
  __int64 v6; // rax
  float v8; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::WoundsMake",
      aDWorkRylSource_68,
      2544,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    goto LABEL_12;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
  {
LABEL_12:
    LOWORD(v6) = 0;
    return v6;
  }
  v5 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v5 = 40 * attackType.m_cSkillLevel;
      break;
    case 1:
      v5 = 80 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v5 = 120 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v5 = 160 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v5 = 200 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v8 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
  lpSkillUsera = (double)(__int16)(100
                                 * (99
                                  * lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
                                  / (lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance + 50)))
               * 0.**********;
  return (unsigned __int64)((double)(v5
                                   * (Math::Random::ComplexRandom(50, 0)
                                    + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                    + 100))
                          * (100.0 - lpSkillUsera)
                          * v8
                          * 0.000099999997);
}

//----- (00429250) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::SplitLife(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  unsigned __int16 v6; // ax
  unsigned __int16 v7; // cx
  __int16 m_cSkillLevel; // ax
  unsigned __int16 m_nNowHP; // dx
  unsigned __int16 m_nMaxHP; // dx
  unsigned __int16 v12; // cx
  unsigned __int16 v13; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::SplitLife",
      aDWorkRylSource_68,
      2644,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = 0;
  v7 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      m_cSkillLevel = attackType.m_cSkillLevel;
      goto LABEL_10;
    case 1:
      m_cSkillLevel = attackType.m_cSkillLevel + 6;
      goto LABEL_10;
    case 2:
      m_cSkillLevel = attackType.m_cSkillLevel + 12;
      goto LABEL_10;
    case 3:
      m_cSkillLevel = attackType.m_cSkillLevel + 18;
      goto LABEL_10;
    case 4:
      m_cSkillLevel = attackType.m_cSkillLevel + 24;
LABEL_10:
      v6 = 80 * m_cSkillLevel;
      v7 = v6;
      break;
    default:
      break;
  }
  m_nNowHP = lpSkillUser->m_CreatureStatus.m_nNowHP;
  if ( m_nNowHP > v7 )
  {
    lpSkillUser->m_CreatureStatus.m_nNowHP = m_nNowHP - v7;
    m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    v12 = lpVictim->m_CreatureStatus.m_nNowHP;
    v13 = v12 + v6;
    lpVictim->m_CreatureStatus.m_nNowHP = v13;
    if ( v13 > m_nMaxHP )
      lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
    CThreat::HealThreat(&lpVictim->m_Threat, lpSkillUser, (lpVictim->m_CreatureStatus.m_nNowHP - v12) / 2);
    *cDefenserJudge = 5;
    return 0;
  }
  else
  {
    lpSkillUser->m_CreatureStatus.m_nNowHP = 0;
    return 0;
  }
}

//----- (00429380) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Dispel(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Dispel",
      aDWorkRylSource_68,
      2724,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  if ( CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, MASTER, LEAVE_WAIT, NONE, 0, 0xFFu) )
  {
    CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
    if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
      lpVictim->__vftable[1].GetGID(lpVictim);
  }
  *cDefenserJudge = 9;
  return 0;
}

//----- (00429420) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::MagicMissile(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int64 v5; // rax
  __int16 v6; // ax
  __int16 v7; // si
  int v8; // eax
  __int16 v9; // si
  __int16 v10; // si
  float v12; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v5) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v6 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v6 = *((_BYTE *)&attackType + 2) >> 4;
      if ( (unsigned __int16)v6 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::MagicMissile",
          aDWorkRylSource_68,
          2766,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v6);
        LOWORD(v5) = 0;
      }
      else
      {
        v7 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_1[v8] + 70;
            goto LABEL_15;
          case 1:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v8] + 140);
            goto LABEL_16;
          case 2:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_1[v8] + 210;
            goto LABEL_15;
          case 3:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v8] + 280);
            goto LABEL_16;
          case 4:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_1[v8] + 350;
LABEL_15:
            v10 = attackType.m_cSkillLevel * v9;
LABEL_16:
            v7 = nPlusLockCountBonus_1[v8] + v10;
            break;
          default:
            break;
        }
        v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
        lpSkillUsera = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
        return (unsigned __int64)((double)(v7
                                         * (Math::Random::ComplexRandom(50, 0)
                                          + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                          + 100))
                                * lpSkillUsera
                                * v12
                                * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::MagicMissile",
      aDWorkRylSource_68,
      2746,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v5) = 0;
  }
  return v5;
}

//----- (00429610) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::LifeAura(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature *v6; // edi
  unsigned int v7; // ebx
  __int16 v9; // bp
  unsigned int v10; // esi
  int v11; // ecx
  int v12; // edx
  unsigned int v13; // edx
  CAggresiveCreature *v14; // ebx
  CSpell::Spell_Info *v15; // eax
  CSpell *v16; // eax
  CSpell *v17; // esi
  CAggresiveCreature *v18; // ebx
  CAggresiveCreature_vtbl *v19; // edx
  int v20; // eax
  CGlobalSpellMgr *Instance; // eax
  CSpell::Spell_Info v22; // [esp+4h] [ebp-1Ch] BYREF
  int v23; // [esp+1Ch] [ebp-4h]

  v6 = lpSkillUser;
  if ( lpSkillUser && lpVictim )
  {
    v7 = HIWORD(attackType);
    if ( (attackType & 0xF00000) != 0 || BYTE1(v7) )
    {
      v9 = Skill::CFunctions::ConsumeMP((AtType)attackType, lpSkillUser);
      if ( v9 == -1 )
      {
        return 0;
      }
      else
      {
        LOWORD(v10) = 0;
        switch ( BYTE2(attackType) >> 4 )
        {
          case 0:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7);
            goto LABEL_14;
          case 1:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 6;
            goto LABEL_14;
          case 2:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 12;
            goto LABEL_14;
          case 3:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 18;
            goto LABEL_14;
          case 4:
            v11 = v6->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 3 + 100;
            v12 = SBYTE1(v7) + 24;
LABEL_14:
            v13 = (int)((unsigned __int64)(1374389535LL * v12 * v11) >> 32) >> 5;
            v10 = v13 + (v13 >> 31);
            break;
          default:
            break;
        }
        v14 = (CAggresiveCreature *)operator new((tagHeader *)0x54);
        lpSkillUser = v14;
        v23 = 0;
        if ( v14 )
        {
          CSpell::Spell_Info::Spell_Info(&v22, ProtoType, v6, 1u, 0x84u, v10, 0xFFFFu);
          CLifeAuraSpell::CLifeAuraSpell((CLifeAuraSpell *)v14, v15, v9);
          v17 = v16;
        }
        else
        {
          v17 = 0;
        }
        v18 = lpVictim;
        v19 = lpVictim->__vftable;
        v23 = -1;
        v20 = (int)v19->GetParty(lpVictim);
        if ( v20 )
        {
          CPartySpellMgr::AddAffectedToAllMember((CPartySpellMgr *)(v20 + 4), v17, v6->m_CellPos.m_wMapIndex);
        }
        else
        {
          lpSkillUser = 0;
          CSpell::AddAffected(v17, v18, (unsigned __int16 *)&lpSkillUser);
        }
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v17);
        *cDefenserJudge = 7;
        return 0;
      }
    }
    else
    {
      CCastingSpell::ClearChant(&lpSkillUser->m_SpellMgr.m_CastingInfo);
      *cDefenserJudge = 7;
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::LifeAura",
      aDWorkRylSource_68,
      2838,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4297A3: variable 'v15' is possibly undefined
// 4297A8: variable 'v16' is possibly undefined

//----- (00429870) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::ManaConvert(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v6; // ax
  unsigned __int16 m_nMaxHP; // ax

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ManaConvert",
      aDWorkRylSource_68,
      3054,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v6 = 80 * attackType.m_cSkillLevel;
      break;
    case 1:
      v6 = 160 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v6 = 240 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v6 = 320 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v6 = 400 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  lpVictim->m_CreatureStatus.m_nNowHP += v6;
  m_nMaxHP = lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
  if ( lpVictim->m_CreatureStatus.m_nNowHP > m_nMaxHP )
    lpVictim->m_CreatureStatus.m_nNowHP = m_nMaxHP;
  *cDefenserJudge = 5;
  return 0;
}

//----- (00429970) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FireRing(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature_vtbl *v6; // edx
  int v7; // eax
  double v8; // st7
  int v9; // eax
  int v10; // eax
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FireRing",
      aDWorkRylSource_68,
      3153,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
LABEL_7:
    LOWORD(v10) = 0;
    return v10;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    goto LABEL_7;
  v6 = lpSkillUser->__vftable;
  v7 = *((unsigned __int8 *)&attackType + 2) >> 4;
  v8 = (double)attackType.m_cSkillLevel * 0.********** + fDRCs_0[v7];
  AddEffectInfo.m_nOffenceRevision = nSkillLevels[v7] + attackType.m_cSkillLevel;
  AddEffectInfo.m_bForceDRC = 1;
  AddEffectInfo.m_fDRC = v8;
  AddEffectInfo.m_nMinDamage = 0;
  AddEffectInfo.m_nMaxDamage = 0;
  if ( !v6->EquipSkillArm(lpSkillUser, Attach) )
    goto LABEL_7;
  LOWORD(v9) = CAggresiveCreature::CalculateDamage(
                 lpVictim,
                 (int)lpSkillUser,
                 COERCE_FLOAT(&AddEffectInfo),
                 *(float *)&cDefenserJudge);
  return lpSkillUser->EquipSkillArm(lpSkillUser, Detach) != 0 ? v9 : 0;
}
// 429A20: variable 'v9' is possibly undefined
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00429A60) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Blast(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  int v6; // eax
  __int16 v7; // cx
  CAggresiveCreature_vtbl *v8; // edx
  double v9; // st7
  unsigned __int16 v10; // di
  unsigned __int16 result; // ax
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Blast",
      aDWorkRylSource_68,
      3173,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = *((unsigned __int8 *)&attackType + 2) >> 4;
  v7 = nSkillLevels[v6] + attackType.m_cSkillLevel;
  v8 = lpSkillUser->__vftable;
  AddEffectInfo.m_bForceDRC = 1;
  AddEffectInfo.m_nMinDamage = 0;
  AddEffectInfo.m_nMaxDamage = 0;
  v9 = (double)attackType.m_cSkillLevel * 0.050000001 + fSwordDRCs[v6];
  AddEffectInfo.m_nOffenceRevision = 2 * v7;
  AddEffectInfo.m_fDRC = v9;
  if ( !v8->EquipSkillArm(lpSkillUser, Attach) )
    return 0;
  v10 = CAggresiveCreature::CalculateDamage(
          lpVictim,
          (int)lpSkillUser,
          COERCE_FLOAT(&AddEffectInfo),
          *(float *)&cDefenserJudge);
  if ( !lpSkillUser->EquipSkillArm(lpSkillUser, Detach) )
    return 0;
  result = v10;
  if ( (lpSkillUser->m_dwStatusFlag & 0x10000) != 0 )
    return 4 * v10;
  return result;
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00429B60) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Shock(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int64 v5; // rax
  __int16 v6; // ax
  __int16 v7; // si
  int v8; // eax
  __int16 v9; // si
  __int16 v10; // si
  float v12; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v5) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v6 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v6 = *((_BYTE *)&attackType + 2) >> 4;
      if ( (unsigned __int16)v6 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Shock",
          aDWorkRylSource_68,
          3312,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v6);
        LOWORD(v5) = 0;
      }
      else
      {
        v7 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 80;
            goto LABEL_15;
          case 1:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 160);
            goto LABEL_16;
          case 2:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 240;
            goto LABEL_15;
          case 3:
            v8 = v6;
            v10 = attackType.m_cSkillLevel * (nMultiplyLockCountBonus_0[v8] + 320);
            goto LABEL_16;
          case 4:
            v8 = v6;
            v9 = nMultiplyLockCountBonus_0[v8] + 400;
LABEL_15:
            v10 = attackType.m_cSkillLevel * v9;
LABEL_16:
            v7 = nPlusLockCountBonus_0[v8] + v10;
            break;
          default:
            break;
        }
        v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
        lpSkillUsera = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
        return (unsigned __int64)((double)(v7
                                         * (Math::Random::ComplexRandom(50, 0)
                                          + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                          + 100))
                                * lpSkillUsera
                                * v12
                                * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Shock",
      aDWorkRylSource_68,
      3292,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v5) = 0;
  }
  return v5;
}

//----- (00429D50) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Crevice(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v5; // si
  __int64 v6; // rax
  float v8; // [esp+8h] [ebp-8h]
  float lpSkillUsera; // [esp+1Ch] [ebp+Ch]

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Crevice",
      aDWorkRylSource_68,
      3438,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    goto LABEL_12;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
  {
LABEL_12:
    LOWORD(v6) = 0;
    return v6;
  }
  v5 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v5 = 80 * attackType.m_cSkillLevel;
      break;
    case 1:
      v5 = 160 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v5 = 240 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v5 = 320 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v5 = 400 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v8 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
  lpSkillUsera = (double)(__int16)(100
                                 * (99
                                  * lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
                                  / (lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance + 50)))
               * 0.**********;
  return (unsigned __int64)((double)(v5
                                   * (Math::Random::ComplexRandom(50, 0)
                                    + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                                    + 100))
                          * (100.0 - lpSkillUsera)
                          * v8
                          * 0.000099999997);
}

//----- (00429EB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::WoundsBlast(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  int v6; // ecx
  __int16 v7; // ax
  CAggresiveCreature_vtbl *v8; // edx
  double v9; // st7
  unsigned __int16 v10; // di
  unsigned __int16 result; // ax
  CalculateDamageInfo AddEffectInfo; // [esp+Ch] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::WoundsBlast",
      aDWorkRylSource_68,
      3547,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v6 = *((unsigned __int8 *)&attackType + 2) >> 4;
  v7 = nSkillLevels[v6] + attackType.m_cSkillLevel;
  v8 = lpSkillUser->__vftable;
  AddEffectInfo.m_bForceDRC = 1;
  AddEffectInfo.m_nMinDamage = 0;
  AddEffectInfo.m_nMaxDamage = 0;
  v9 = (double)attackType.m_cSkillLevel * 0.050000001 + fSwordDRCs[v6];
  AddEffectInfo.m_nOffenceRevision = 3 * v7;
  AddEffectInfo.m_fDRC = v9;
  if ( !v8->EquipSkillArm(lpSkillUser, Attach) )
    return 0;
  v10 = CAggresiveCreature::CalculateDamage(
          lpVictim,
          (int)lpSkillUser,
          COERCE_FLOAT(&AddEffectInfo),
          *(float *)&cDefenserJudge);
  if ( !lpSkillUser->EquipSkillArm(lpSkillUser, Detach) )
    return 0;
  CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v10, DETAUNT);
  result = 4 * v10;
  if ( (lpSkillUser->m_dwStatusFlag & 0x10000) == 0 )
    return v10;
  return result;
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (00429FC0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DaggerFire(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  __int16 v4; // si
  double v5; // st7

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DaggerFire",
      aDWorkRylSource_68,
      3568,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  v4 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      v4 = 40 * attackType.m_cSkillLevel;
      break;
    case 1:
      v4 = 80 * (attackType.m_cSkillLevel + 3);
      break;
    case 2:
      v4 = 120 * (attackType.m_cSkillLevel + 6);
      break;
    case 3:
      v4 = 160 * (attackType.m_cSkillLevel + 9);
      break;
    case 4:
      v4 = 200 * (attackType.m_cSkillLevel + 12);
      break;
    default:
      break;
  }
  v5 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
  return ((signed __int64 (__usercall *)@<edx:eax>(_DWORD, _DWORD, _DWORD, double@<st0>))_ftol2)(
           ProtoType,
           attackType,
           v4,
           v5);
}
// 4C0ECC: using guessed type signed __int64 __usercall _ftol2@<edx:eax>(_DWORD, _DWORD, _DWORD, double@<st0>);

//----- (0042A0B0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::HealingPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  CAggresiveCreature *v6; // esi
  unsigned __int64 v7; // rax
  double v8; // st7
  bool v9; // c0
  double v10; // st7
  double v11; // st7
  double v12; // st7
  double v13; // st7
  float *p_lpSkillUser; // eax
  unsigned __int16 m_nMaxHP; // ax
  int v17; // [esp+4h] [ebp-4h] BYREF

  v6 = lpVictim;
  if ( *(float *)&lpSkillUser == 0.0 || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::HealingPotion",
      aDWorkRylSource_68,
      3724,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  else
  {
    LOWORD(v7) = 0;
    switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
    {
      case 0:
        lpSkillUser = (CAggresiveCreature *)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v17 = 1133903872;
        v8 = (double)(int)lpSkillUser * 0.15000001;
        *(float *)&lpSkillUser = v8;
        v9 = v8 < 300.0;
        goto LABEL_9;
      case 1:
        lpSkillUser = (CAggresiveCreature *)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v17 = 1142292480;
        v10 = (double)(int)lpSkillUser * 0.30000001;
        *(float *)&lpSkillUser = v10;
        v9 = v10 < 600.0;
        goto LABEL_9;
      case 2:
        lpSkillUser = (CAggresiveCreature *)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v17 = 1147207680;
        v11 = (double)(int)lpSkillUser * 0.44999999;
        *(float *)&lpSkillUser = v11;
        v9 = v11 < 900.0;
        goto LABEL_9;
      case 3:
        lpSkillUser = (CAggresiveCreature *)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v17 = 1150681088;
        v12 = (double)(int)lpSkillUser * 0.60000002;
        *(float *)&lpSkillUser = v12;
        v9 = v12 < 1200.0;
        goto LABEL_9;
      case 4:
        lpSkillUser = (CAggresiveCreature *)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v17 = 1153138688;
        v13 = (double)(int)lpSkillUser * 0.75;
        *(float *)&lpSkillUser = v13;
        v9 = v13 < 1500.0;
LABEL_9:
        p_lpSkillUser = (float *)&v17;
        if ( !v9 )
          p_lpSkillUser = (float *)&lpSkillUser;
        v7 = (unsigned __int64)*p_lpSkillUser;
        break;
      default:
        break;
    }
    lpVictim->m_CreatureStatus.m_nNowHP += v7;
    m_nMaxHP = v6->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    if ( v6->m_CreatureStatus.m_nNowHP > m_nMaxHP )
      v6->m_CreatureStatus.m_nNowHP = m_nMaxHP;
    *cDefenserJudge = 5;
    return 0;
  }
}

//----- (0042A240) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::RedMoonCake(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v7; // cx
  unsigned __int16 *p_m_nMaxHP; // ecx

  if ( lpSkillUser && lpVictim )
  {
    if ( (*((_BYTE *)&attackType + 2) & 0xF0u) < 0x30 )
    {
      v7 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v7 = 1500;
          break;
        case 1:
          v7 = 3000;
          break;
        case 2:
          v7 = 5000;
          break;
        case 3:
        case 4:
          v7 = 0;
          break;
        default:
          break;
      }
      lpVictim->m_CreatureStatus.m_nNowHP += v7;
      p_m_nMaxHP = &lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      if ( lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxHP >= lpVictim->m_CreatureStatus.m_nNowHP )
        p_m_nMaxHP = &lpVictim->m_CreatureStatus.m_nNowHP;
      lpVictim->m_CreatureStatus.m_nNowHP = *p_m_nMaxHP;
      *cDefenserJudge = 5;
      return 0;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::RedMoonCake",
        aDWorkRylSource_68,
        3872,
        aCid0x08x_268,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        *((unsigned __int8 *)&attackType + 2) >> 4);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::RedMoonCake",
      aDWorkRylSource_68,
      3867,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (0042A340) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BlueMoonCake(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge)
{
  __int16 v7; // cx
  unsigned __int16 *p_m_nMaxMP; // ecx

  if ( lpSkillUser && lpVictim )
  {
    if ( (*((_BYTE *)&attackType + 2) & 0xF0u) < 0x30 )
    {
      v7 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v7 = 1000;
          break;
        case 1:
          v7 = 2000;
          break;
        case 2:
          v7 = 3000;
          break;
        case 3:
        case 4:
          v7 = 0;
          break;
        default:
          break;
      }
      lpVictim->m_CreatureStatus.m_nNowMP += v7;
      p_m_nMaxMP = &lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      if ( lpVictim->m_CreatureStatus.m_StatusInfo.m_nMaxMP >= lpVictim->m_CreatureStatus.m_nNowMP )
        p_m_nMaxMP = &lpVictim->m_CreatureStatus.m_nNowMP;
      lpVictim->m_CreatureStatus.m_nNowMP = *p_m_nMaxMP;
      *cDefenserJudge = 6;
      return 0;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::BlueMoonCake",
        aDWorkRylSource_68,
        3902,
        aCid0x08x_268,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        *((unsigned __int8 *)&attackType + 2) >> 4);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BlueMoonCake",
      aDWorkRylSource_68,
      3897,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}

//----- (0042A440) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CSlowSpell>::operator()(
        Skill::CAddSpell<CSlowSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x1000u);
      v7->__vftable = (CSpell_vtbl *)&CSlowSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD20: using guessed type void *CSlowSpell::`vftable';

//----- (0042A570) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CHoldSpell>::operator()(
        Skill::CAddSpell<CHoldSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x1000000u);
      v7->__vftable = (CSpell_vtbl *)&CHoldSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD40: using guessed type void *CHoldSpell::`vftable';

//----- (0042A6A0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CArmorBrokenSpell>::operator()(
        Skill::CAddSpell<CArmorBrokenSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x2000u);
      v7->__vftable = (CSpell_vtbl *)&CArmorBrokenSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD30: using guessed type void *CArmorBrokenSpell::`vftable';

//----- (0042A7D0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CStunSpell>::operator()(
        Skill::CAddSpell<CStunSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x2000000u);
      v7->__vftable = (CSpell_vtbl *)&CStunSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD50: using guessed type void *CStunSpell::`vftable';

//----- (0042A900) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CManaShellSpell>::operator()(
        Skill::CAddSpell<CManaShellSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x20000u);
      v7->__vftable = (CSpell_vtbl *)&CManaShellSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCB0: using guessed type void *CManaShellSpell::`vftable';

//----- (0042AA30) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CEncourageSpell>::operator()(
        Skill::CAddSpell<CEncourageSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x40000u);
      v7->__vftable = (CSpell_vtbl *)&CEncourageSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCC0: using guessed type void *CEncourageSpell::`vftable';


