[CRITICAL_VALUES]

; 1.0 = base value (200)
; 1.1 = 10% increase (220)
; 1.5 = 50% increase (300)
; 0.8 = 20% decrease (160)
; 0.0 = disable critical hits (0)

; Class specific multipliers

;Human
Fighter=1.0
Rogue=1.0
Mage=1.0
Acolyte=1.0
Defender=1.0
Warrior=1.0
Assassin=1.0
Archer=1.0
Sorcerer=1.0
Enchanter=1.0
Priest=1.0
Cleric=1.0

;<PERSON><PERSON><PERSON><PERSON>
Combatant=1.0
Officiator=1.0
Templar=1.0
Attacker=1.0
Gunner=1.0
RuneOff=1.0
LifeOff=1.0
ShadowOff=1.0 