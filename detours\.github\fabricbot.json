{"version": "1.0", "tasks": [{"taskType": "trigger", "capabilityId": "CodeFlowLink", "subCapability": "CodeFlowLink", "version": "1.0", "config": {"taskName": "Add a CodeFlow link to new pull requests"}, "id": "mZf4zgvu8AP"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestReviewResponder", "version": "1.0", "config": {"taskName": "Add needs author feedback label to pull requests when changes are requested", "conditions": {"operator": "and", "operands": [{"name": "isAction", "parameters": {"action": "submitted"}}, {"name": "isReviewState", "parameters": {"state": "changes_requested"}}]}, "actions": [{"name": "addLabel", "parameters": {"label": "needs-author-feedback"}}], "eventType": "pull_request", "eventNames": ["pull_request_review"]}, "id": "pW3Cj3UnZU9"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestResponder", "version": "1.0", "config": {"taskName": "Remove needs author feedback label when the author responds to a pull request", "conditions": {"operator": "and", "operands": [{"name": "isActivitySender", "parameters": {"user": {"type": "author"}}}, {"operator": "not", "operands": [{"name": "isAction", "parameters": {"action": "closed"}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}], "eventType": "pull_request", "eventNames": ["pull_request", "issues", "project_card"]}, "id": "04lHp8WyV-O"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestCommentResponder", "version": "1.0", "config": {"taskName": "Remove needs author feedback label when the author comments on a pull request", "conditions": {"operator": "and", "operands": [{"name": "isActivitySender", "parameters": {"user": {"type": "author"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}], "eventType": "pull_request", "eventNames": ["issue_comment"]}, "id": "omDISIYFQjj"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestReviewResponder", "version": "1.0", "config": {"taskName": "Remove needs author feedback label when the author responds to a pull request review comment", "conditions": {"operator": "and", "operands": [{"name": "isActivitySender", "parameters": {"user": {"type": "author"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}], "eventType": "pull_request", "eventNames": ["pull_request_review"]}, "id": "8I_QnyLPWeL"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestResponder", "version": "1.0", "config": {"taskName": "Remove no recent activity label from pull requests", "conditions": {"operator": "and", "operands": [{"operator": "not", "operands": [{"name": "isAction", "parameters": {"action": "closed"}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}], "eventType": "pull_request", "eventNames": ["pull_request", "issues", "project_card"]}, "id": "f7KfIhLMim7"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestCommentResponder", "version": "1.0", "config": {"taskName": "Remove no recent activity label when a pull request is commented on", "conditions": {"operator": "and", "operands": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}], "eventType": "pull_request", "eventNames": ["issue_comment"]}, "id": "mK4FccizO_L"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "PullRequestReviewResponder", "version": "1.0", "config": {"taskName": "Remove no recent activity label when a pull request is reviewed", "conditions": {"operator": "and", "operands": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}]}, "actions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}], "eventType": "pull_request", "eventNames": ["pull_request_review"]}, "id": "JMlMgr_Rmw3"}, {"taskType": "scheduled", "capabilityId": "ScheduledSearch", "subCapability": "ScheduledSearch", "version": "1.1", "config": {"taskName": "Close stale pull requests", "frequency": [{"weekDay": 0, "hours": [4, 10, 16, 22]}, {"weekDay": 1, "hours": [4, 10, 16, 22]}, {"weekDay": 2, "hours": [4, 10, 16, 22]}, {"weekDay": 3, "hours": [4, 10, 16, 22]}, {"weekDay": 4, "hours": [4, 10, 16, 22]}, {"weekDay": 5, "hours": [4, 10, 16, 22]}, {"weekDay": 6, "hours": [4, 10, 16, 22]}], "searchTerms": [{"name": "isPr", "parameters": {}}, {"name": "isOpen", "parameters": {}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}, {"name": "noActivitySince", "parameters": {"days": 7}}], "actions": [{"name": "closeIssue", "parameters": {}}]}, "id": "crcA-Vu_dbg", "disabled": true}, {"taskType": "scheduled", "capabilityId": "ScheduledSearch", "subCapability": "ScheduledSearch", "version": "1.1", "config": {"taskName": "Add no recent activity label to pull requests", "frequency": [{"weekDay": 0, "hours": [5, 11, 17, 23]}, {"weekDay": 1, "hours": [5, 11, 17, 23]}, {"weekDay": 2, "hours": [5, 11, 17, 23]}, {"weekDay": 3, "hours": [5, 11, 17, 23]}, {"weekDay": 4, "hours": [5, 11, 17, 23]}, {"weekDay": 5, "hours": [5, 11, 17, 23]}, {"weekDay": 6, "hours": [5, 11, 17, 23]}], "searchTerms": [{"name": "isPr", "parameters": {}}, {"name": "isOpen", "parameters": {}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}, {"name": "noActivitySince", "parameters": {"days": 7}}, {"name": "no<PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}], "actions": [{"name": "addLabel", "parameters": {"label": "status-no recent activity"}}, {"name": "addReply", "parameters": {"comment": "This pull request has been automatically marked as stale because it has been marked as requiring author feedback but has not had any activity for **7 days**."}}]}, "id": "EBJXSaQYFbd"}, {"taskType": "trigger", "capabilityId": "AutoMerge", "subCapability": "AutoMerge", "version": "1.0", "config": {"taskName": "Automatically merge pull requests", "label": "auto-merge", "silentMode": false, "minMinutesOpen": 480, "mergeType": "squash", "allowAutoMergeInstructionsWithoutLabel": true, "deleteBranches": true, "removeLabelOnPush": true, "usePrDescriptionAsCommitMessage": true, "requireAllStatuses": true}, "id": "Uqjc91BVjBv"}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "IssueCommentResponder", "version": "1.0", "config": {"conditions": {"operator": "and", "operands": [{"operator": "not", "operands": [{"name": "isOpen", "parameters": {}}]}, {"name": "isAction", "parameters": {"action": "created"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}, {"operator": "not", "operands": [{"name": "noActivitySince", "parameters": {"days": 7}}]}, {"operator": "not", "operands": [{"name": "isCloseAndComment", "parameters": {}}]}, {"name": "isActivitySender", "parameters": {"user": {"type": "author"}}}, {"name": "activitySenderHasPermissions", "parameters": {"permissions": "none"}}]}, "eventType": "issue", "eventNames": ["issue_comment"], "taskName": "For issues closed due to inactivity, re-open an issue if issue author posts a reply within 7 days.", "actions": [{"name": "reopenIssue", "parameters": {}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "status-no recent activity"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"label": "needs-author-feedback"}}, {"name": "addLabel", "parameters": {"label": "needs-attention :wave:"}}]}, "id": "HEHnNh8GHmo", "disabled": true}, {"taskType": "trigger", "capabilityId": "IssueResponder", "subCapability": "IssueCommentResponder", "version": "1.0", "config": {"conditions": {"operator": "and", "operands": [{"name": "isAction", "parameters": {"action": "created"}}, {"operator": "not", "operands": [{"name": "isOpen", "parameters": {}}]}, {"name": "activitySenderHasPermissions", "parameters": {"permissions": "none"}}, {"name": "noActivitySince", "parameters": {"days": 7}}, {"operator": "not", "operands": [{"name": "isCloseAndComment", "parameters": {}}]}]}, "eventType": "issue", "eventNames": ["issue_comment"], "taskName": "For issues closed with no activity over 7 days, ask non-contributor to consider opening a new issue instead.", "actions": [{"name": "addReply", "parameters": {"comment": "Hello lovely human, thank you for your comment on this issue. Because this issue has been closed for a period of time, please strongly consider opening a new issue linking to this issue instead to ensure better visibility of your comment. Thank you!"}}]}, "id": "dkq_8sNJxAv"}, {"taskType": "scheduled", "capabilityId": "ScheduledSearch", "subCapability": "ScheduledSearch", "version": "1.1", "config": {"frequency": [{"weekDay": 0, "hours": [0, 6, 12, 18]}, {"weekDay": 1, "hours": [0, 6, 12, 18]}, {"weekDay": 2, "hours": [0, 6, 12, 18]}, {"weekDay": 3, "hours": [0, 6, 12, 18]}, {"weekDay": 4, "hours": [0, 6, 12, 18]}, {"weekDay": 5, "hours": [0, 6, 12, 18]}, {"weekDay": 6, "hours": [0, 6, 12, 18]}], "searchTerms": [{"name": "isClosed", "parameters": {}}, {"name": "noActivitySince", "parameters": {"days": 30}}, {"name": "isUnlocked", "parameters": {}}, {"name": "isIssue", "parameters": {}}], "taskName": "Lock issues closed without activity for over 30 days", "actions": [{"name": "lockIssue", "parameters": {"reason": "resolved"}}]}, "id": "KXiF1YUIeDp"}], "userGroups": []}