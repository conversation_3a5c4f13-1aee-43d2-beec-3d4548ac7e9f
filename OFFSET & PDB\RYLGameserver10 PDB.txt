
//----- (00455B40) --------------------------------------------------------
std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareHP>(
        std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *result,
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster **Myptr; // ebx
  CMonster **v4; // ecx
  CMonster **i; // esi
  unsigned __int16 m_nNowHP; // ax
  unsigned __int16 v7; // dx
  unsigned __int16 v8; // dx
  unsigned __int16 v9; // ax
  C<PERSON>onster **v10; // eax
  CMonster **v11; // ebp
  unsigned __int16 v12; // dx
  unsigned __int16 v13; // di
  CMonster *v14; // edi
  bool v15; // zf
  CMonster **v16; // edx
  unsigned __int16 v17; // di
  unsigned __int16 v18; // bx
  CMonster *v19; // edi
  CMonster *v20; // edx
  CMonster **v21; // edx
  CMonster *v22; // edx
  CMonster *v23; // edi
  CMonster *v24; // edx
  CMonster *v25; // edi
  std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *v26; // eax
  CMonster *v27; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<CMonster *>::iterator,CompareHP>(
    _First,
    (std::vector<CMonster *>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<CMonster *>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    m_nNowHP = (*v4)->m_CreatureStatus.m_nNowHP;
    v7 = (*(v4 - 1))->m_CreatureStatus.m_nNowHP;
    if ( m_nNowHP > v7 )
      break;
    if ( m_nNowHP < v7 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v8 = (*v4)->m_CreatureStatus.m_nNowHP;
    do
    {
      v9 = (*i)->m_CreatureStatus.m_nNowHP;
      if ( v8 > v9 )
        break;
      if ( v8 < v9 )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v10 = i;
  v11 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v10 < Myptr; ++v10 )
      {
        v12 = (*v4)->m_CreatureStatus.m_nNowHP;
        v13 = (*v10)->m_CreatureStatus.m_nNowHP;
        if ( v13 <= v12 )
        {
          if ( v13 < v12 )
            break;
          v14 = *i;
          *i = *v10;
          Myptr = _Last._Myptr;
          ++i;
          *v10 = v14;
        }
      }
      v15 = v11 == _First._Myptr;
      if ( v11 > _First._Myptr )
      {
        v16 = v11 - 1;
        do
        {
          v17 = (*v4)->m_CreatureStatus.m_nNowHP;
          v18 = (*v16)->m_CreatureStatus.m_nNowHP;
          if ( v17 <= v18 )
          {
            if ( v17 < v18 )
              break;
            v19 = *--v4;
            *v4 = *v16;
            *v16 = v19;
          }
          --v11;
          --v16;
        }
        while ( _First._Myptr < v11 );
        Myptr = _Last._Myptr;
        v15 = v11 == _First._Myptr;
      }
      if ( v15 )
        break;
      --v11;
      if ( v10 == Myptr )
      {
        if ( v11 != --v4 )
        {
          v22 = *v11;
          *v11 = *v4;
          *v4 = v22;
        }
        v23 = *(i - 1);
        v24 = *v4;
        --i;
        *v4 = v23;
        *i = v24;
      }
      else
      {
        v25 = *v10;
        *v10 = *v11;
        Myptr = _Last._Myptr;
        ++v10;
        *v11 = v25;
      }
    }
    if ( v10 == Myptr )
      break;
    if ( i != v10 )
    {
      v20 = *v4;
      *v4 = *i;
      *i = v20;
    }
    v21 = v10;
    v27 = *v4;
    *v4 = *v10;
    Myptr = _Last._Myptr;
    ++i;
    ++v10;
    ++v4;
    *v21 = v27;
  }
  v26 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v26;
}

//----- (00455CE0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  int i; // esi
  CMonster *v3; // eax

  for ( i = (_Last._Myptr - _First._Myptr) / 2;
        i > 0;
        std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          v3) )
  {
    v3 = _First._Myptr[--i];
  }
}

//----- (00455D20) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  int i; // esi
  CMonster *v3; // eax

  for ( i = (_Last._Myptr - _First._Myptr) / 2;
        i > 0;
        std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          v3) )
  {
    v3 = _First._Myptr[--i];
  }
}

//----- (00455D60) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        const std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *_Right)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v4; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *i; // ecx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v7; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v8; // ecx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *j; // eax

  Myhead = this->_Myhead;
  Myhead->_Parent = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
                      this,
                      _Right->_Myhead->_Parent,
                      Myhead);
  this->_Mysize = _Right->_Mysize;
  v4 = this->_Myhead;
  Parent = v4->_Parent;
  if ( Parent->_Isnil )
  {
    v4->_Left = v4;
    this->_Myhead->_Right = this->_Myhead;
  }
  else
  {
    for ( i = Parent->_Left; !i->_Isnil; i = i->_Left )
      Parent = i;
    v4->_Left = Parent;
    v7 = this->_Myhead;
    v8 = v7->_Parent;
    for ( j = v8->_Right; !j->_Isnil; j = j->_Right )
      v8 = j;
    v7->_Right = v8;
  }
}

//----- (00455DF0) --------------------------------------------------------
CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const,CCharacter *>>>(
        CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // edi
  CStatueInfo *m_fnSecondProcess; // esi
  CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > *v7; // eax

  Ptr = _First._Ptr;
  v5 = _Last._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v7 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  else
  {
    m_fnSecondProcess = _Func.m_fnSecondProcess;
    do
    {
      CStatueInfo::operator()(m_fnSecondProcess, Ptr->_Myval.second);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
      Ptr = _First._Ptr;
    }
    while ( _First._Ptr != v5 );
    v7 = result;
    result->m_fnSecondProcess = m_fnSecondProcess;
  }
  return v7;
}

//----- (00455E40) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster **i; // esi
  int m_nLevel; // ecx
  unsigned int *v4; // eax
  int v5; // ebp
  std::vector<unsigned long>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_nLevel = (*i)->m_CreatureStatus.m_nLevel;
      if ( m_nLevel <= (*_First._Myptr)->m_CreatureStatus.m_nLevel )
      {
        v4 = (unsigned int *)(i - 1);
        if ( m_nLevel > (*(i - 1))->m_CreatureStatus.m_nLevel )
        {
          do
          {
            v5 = *(v4 - 1);
            v6._Myptr = v4--;
          }
          while ( m_nLevel > *(_DWORD *)(v5 + 144) );
          if ( (CMonster **)v6._Myptr != i )
            std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
              v6,
              (std::vector<unsigned long>::iterator)i,
              (std::vector<unsigned long>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          (std::vector<unsigned long>::iterator)_First._Myptr,
          (std::vector<unsigned long>::iterator)i,
          (std::vector<unsigned long>::iterator)(i + 1));
      }
    }
  }
}

//----- (00455ED0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster **i; // esi
  unsigned __int16 m_nNowHP; // cx
  unsigned int *v4; // eax
  int v5; // ebp
  std::vector<unsigned long>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_nNowHP = (*i)->m_CreatureStatus.m_nNowHP;
      if ( m_nNowHP >= (*_First._Myptr)->m_CreatureStatus.m_nNowHP )
      {
        v4 = (unsigned int *)(i - 1);
        if ( m_nNowHP < (*(i - 1))->m_CreatureStatus.m_nNowHP )
        {
          do
          {
            v5 = *(v4 - 1);
            v6._Myptr = v4--;
          }
          while ( m_nNowHP < *(_WORD *)(v5 + 148) );
          if ( (CMonster **)v6._Myptr != i )
            std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
              v6,
              (std::vector<unsigned long>::iterator)i,
              (std::vector<unsigned long>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          (std::vector<unsigned long>::iterator)_First._Myptr,
          (std::vector<unsigned long>::iterator)i,
          (std::vector<unsigned long>::iterator)(i + 1));
      }
    }
  }
}

//----- (00455F60) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<CMonster *>::iterator,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  int i; // esi
  CMonster *v3; // [esp-Ch] [ebp-14h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 2 > 1; i -= 4 )
  {
    v3 = *(CMonster **)((char *)_First._Myptr + i - 4);
    *(CMonster **)((char *)_First._Myptr + i - 4) = *_First._Myptr;
    std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(_First, 0, (i - 4) >> 2, v3);
  }
}

//----- (00455FB0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<CMonster *>::iterator,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  int i; // esi
  CMonster *v3; // [esp-Ch] [ebp-14h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 2 > 1; i -= 4 )
  {
    v3 = *(CMonster **)((char *)_First._Myptr + i - 4);
    *(CMonster **)((char *)_First._Myptr + i - 4) = *_First._Myptr;
    std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(_First, 0, (i - 4) >> 2, v3);
  }
}

//----- (00456000) --------------------------------------------------------
void __thiscall CStatue::GiveMileage(CStatue *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *Instance; // eax
  CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > result; // [esp+0h] [ebp-10h] BYREF
  int v3; // [esp+4h] [ebp-Ch]
  CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > _Func; // [esp+8h] [ebp-8h] BYREF
  int v5; // [esp+Ch] [ebp-4h]

  LOBYTE(v3) = 1;
  _Func.m_fnSecondProcess = (CStatueInfo *)this;
  v5 = v3;
  Instance = (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)CCreatureManager::GetInstance();
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const,CCharacter *>>>(
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Instance[10]._Ptr->_Left,
    Instance[10],
    (CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> >)&_Func);
}

//----- (00456040) --------------------------------------------------------
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Insert(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Wherenode,
        unsigned int *_Val)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // edx
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const unsigned int *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x3FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const unsigned int *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
          (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004561F0) --------------------------------------------------------
void __thiscall __noreturn std::vector<CMonster *>::_Xlen(std::vector<CMonster *> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00456260) --------------------------------------------------------
void __thiscall std::vector<CMonster *>::_Insert_n(
        std::vector<CMonster *> *this,
        std::vector<CMonster *>::iterator _Where,
        unsigned int _Count,
        CMonster *const *_Val)
{
  CMonster **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  CMonster **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  CMonster **v24; // ecx
  Quest::QuestNode **v25; // edi
  CMonster **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<CMonster *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (CMonster *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<CMonster *>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (CMonster **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (CMonster **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                       &Mylast[-v23],
                                       (int)Mylast,
                                       Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (CMonster **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (CMonster **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 456344: variable 'v15' is possibly undefined

//----- (00456440) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last,
        int _Ideal,
        CompareLevel _Pred)
{
  CMonster **Myptr; // ebx
  CMonster **v5; // edi
  int v6; // eax
  CMonster **v8; // ebp
  std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Last._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareLevel>(
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareLevel>(
        &_Mid,
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
      v8 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFC) >= (int)(((char *)v5
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFC) )
      {
        std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(
          _Mid.second,
          (std::vector<CMonster *>::iterator)v5,
          _Ideal,
          _Pred);
        v5 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(
          (std::vector<CMonster *>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          _Pred);
        Myptr = v8;
      }
      v6 = v5 - Myptr;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v5 - (char *)Myptr) & 0xFFFFFFFC) > 4 )
      std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
    std::sort_heap<std::vector<CMonster *>::iterator,CompareLevel>(
      (std::vector<CMonster *>::iterator)Myptr,
      (std::vector<CMonster *>::iterator)v5);
  }
}
// 4564F6: conditional instruction was optimized away because eax.4>=21

//----- (00456530) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<CMonster *>::iterator,int,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last,
        int _Ideal,
        CompareHP _Pred)
{
  CMonster **Myptr; // ebx
  CMonster **v5; // edi
  int v6; // eax
  CMonster **v8; // ebp
  std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v5 = _Last._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  if ( v6 <= 32 )
  {
LABEL_7:
    if ( v6 > 1 )
      std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareHP>(
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareHP>(
        &_Mid,
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
      v8 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFC) >= (int)(((char *)v5
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFC) )
      {
        std::_Sort<std::vector<CMonster *>::iterator,int,CompareHP>(
          _Mid.second,
          (std::vector<CMonster *>::iterator)v5,
          _Ideal,
          _Pred);
        v5 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<CMonster *>::iterator,int,CompareHP>(
          (std::vector<CMonster *>::iterator)Myptr,
          _Mid.first,
          _Ideal,
          _Pred);
        Myptr = v8;
      }
      v6 = v5 - Myptr;
      if ( v6 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v5 - (char *)Myptr) & 0xFFFFFFFC) > 4 )
      std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(
        (std::vector<CMonster *>::iterator)Myptr,
        (std::vector<CMonster *>::iterator)v5);
    std::sort_heap<std::vector<CMonster *>::iterator,CompareHP>(
      (std::vector<CMonster *>::iterator)Myptr,
      (std::vector<CMonster *>::iterator)v5);
  }
}
// 4565E6: conditional instruction was optimized away because eax.4>=21

//----- (00456620) --------------------------------------------------------
std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *result,
        unsigned int *_Val)
{
  unsigned int *v3; // ebp
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int v8; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    v8 = *_Val;
    do
    {
      v7 = v8 < Parent->_Myval;
      Myhead = Parent;
      _Addleft = v7;
      if ( v8 >= Parent->_Myval )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (unsigned int *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec((std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)_Val;
  }
  if ( v9->_Myval >= *v3 )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (004566E0) --------------------------------------------------------
void __thiscall std::vector<CMonster *>::reserve(std::vector<CMonster *> *this, unsigned int _Count)
{
  CMonster **Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  CMonster **v6; // edi
  CMonster **v7; // eax
  CMonster **v8; // [esp-18h] [ebp-38h]
  CMonster **Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  CMonster **_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x3FFFFFFF )
    std::vector<CMonster *>::_Xlen(this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(4 * _Count);
    v6 = (CMonster **)operator new(_Counta);
    Mylast = this->_Mylast;
    v8 = this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
      (std::vector<unsigned long>::iterator)v8,
      (std::vector<unsigned long>::iterator)Mylast,
      (unsigned int *)v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (CMonster **)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (004567B0) --------------------------------------------------------
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _First,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00456870) --------------------------------------------------------
void __thiscall std::vector<CMonster *>::push_back(std::vector<CMonster *> *this, CMonster **_Val)
{
  CMonster **Myfirst; // esi
  unsigned int v3; // edx
  CMonster **Mylast; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v3 = this->_Mylast - Myfirst;
  else
    v3 = 0;
  if ( Myfirst && v3 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    *Mylast = *_Val;
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CMonster *>::_Insert_n(this, (std::vector<CMonster *>::iterator)this->_Mylast, 1u, _Val);
  }
}

//----- (004568C0) --------------------------------------------------------
CMonster *__thiscall CSkillMonster::FindHighestLVMember(CSkillMonster *this)
{
  CParty *m_pParty; // ebx
  int v2; // ebp
  CMonster **Myfirst; // esi
  CMonster **Mylast; // edx
  CMonster *v6; // edi
  double m_fPointZ; // st7
  double v8; // st6
  CMonster *v9; // edi
  CMonster **v10; // [esp+8h] [ebp-28h]
  CMonster *lpMonster; // [esp+10h] [ebp-20h] BYREF
  std::vector<CMonster *> targetVector; // [esp+14h] [ebp-1Ch] BYREF
  int v14; // [esp+2Ch] [ebp-4h]

  m_pParty = this->m_pParty;
  v2 = 0;
  if ( !m_pParty )
    return 0;
  v14 = 0;
  memset(&targetVector._Myfirst, 0, 12);
  std::vector<CMonster *>::reserve(&targetVector, 0xAu);
  Myfirst = targetVector._Myfirst;
  Mylast = targetVector._Mylast;
  if ( m_pParty->m_Party.m_cMemberNum )
  {
    v10 = (CMonster **)&m_pParty[1];
    do
    {
      if ( (unsigned int)v2 >= 0xA )
        v6 = 0;
      else
        v6 = *v10;
      m_fPointZ = this->m_CurrentPos.m_fPointZ;
      lpMonster = v6;
      v8 = this->m_CurrentPos.m_fPointX - v6->m_CurrentPos.m_fPointX;
      if ( v8 * v8 + (m_fPointZ - v6->m_CurrentPos.m_fPointZ) * (m_fPointZ - v6->m_CurrentPos.m_fPointZ) <= 32.0 )
      {
        if ( Myfirst && Mylast - Myfirst < (unsigned int)(targetVector._Myend - Myfirst) )
        {
          *Mylast++ = v6;
          targetVector._Mylast = Mylast;
        }
        else
        {
          std::vector<CMonster *>::_Insert_n(&targetVector, (std::vector<CMonster *>::iterator)Mylast, 1u, &lpMonster);
          Mylast = targetVector._Mylast;
          Myfirst = targetVector._Myfirst;
        }
      }
      ++v10;
      ++v2;
    }
    while ( v2 < m_pParty->m_Party.m_cMemberNum );
  }
  if ( !Myfirst )
    return 0;
  if ( !(Mylast - Myfirst) )
  {
    operator delete(Myfirst);
    return 0;
  }
  LOBYTE(lpMonster) = 0;
  std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(
    (std::vector<CMonster *>::iterator)Myfirst,
    (std::vector<CMonster *>::iterator)Mylast,
    Mylast - Myfirst,
    0);
  v9 = *Myfirst;
  operator delete(Myfirst);
  return v9;
}

//----- (00456A20) --------------------------------------------------------
CSkillMonster *__thiscall CSkillMonster::FindHighestLVMember(CSkillMonster *this, const AtType *attackType)
{
  CMonster **Myfirst; // edi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CParty *m_pParty; // ebx
  double v7; // st7
  unsigned __int8 m_cMemberNum; // al
  int v9; // esi
  CMonster *v10; // eax
  double v11; // st7
  double v12; // st6
  CMonster *v13; // esi
  float fSquareEffectDistance; // [esp+8h] [ebp-24h]
  CMonster *lpMonster; // [esp+Ch] [ebp-20h] BYREF
  std::vector<CMonster *> targetVector; // [esp+10h] [ebp-1Ch] BYREF
  int v17; // [esp+28h] [ebp-4h]
  const AtType *attackTypea; // [esp+30h] [ebp+4h]

  Myfirst = 0;
  if ( !this->m_pParty )
    return (this->m_dwStatusFlag & this->m_MonsterInfo.m_dwEnchantSpellType) == 0 ? this : 0;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType->m_wType);
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMonster::FindHighestLVMember",
      aDWorkRylSource_51,
      1201,
      aCid0x08x_77,
      this->m_dwCID,
      attackType->m_wType);
    return 0;
  }
  m_pParty = this->m_pParty;
  v7 = SkillProtoType->m_ProtoTypes[*((unsigned __int8 *)attackType + 2) >> 4].m_EffectDistance + 10.0;
  fSquareEffectDistance = v7 * v7;
  m_cMemberNum = m_pParty->m_Party.m_cMemberNum;
  v9 = 0;
  v17 = 0;
  memset(&targetVector._Myfirst, 0, 12);
  if ( !m_cMemberNum )
    return 0;
  attackTypea = (const AtType *)&m_pParty[1];
  do
  {
    if ( (unsigned int)v9 >= 0xA )
      v10 = 0;
    else
      v10 = (CMonster *)*attackTypea;
    lpMonster = v10;
    if ( v10 && (v10->m_dwStatusFlag & this->m_MonsterInfo.m_dwEnchantSpellType) == 0 )
    {
      v11 = this->m_CurrentPos.m_fPointZ - v10->m_CurrentPos.m_fPointZ;
      v12 = this->m_CurrentPos.m_fPointX - v10->m_CurrentPos.m_fPointX;
      if ( v12 * v12 + v11 * v11 <= fSquareEffectDistance )
      {
        std::vector<CMonster *>::push_back(&targetVector, &lpMonster);
        Myfirst = targetVector._Myfirst;
      }
    }
    ++v9;
    ++attackTypea;
  }
  while ( v9 < m_pParty->m_Party.m_cMemberNum );
  if ( !Myfirst )
    return 0;
  if ( !(targetVector._Mylast - Myfirst) )
  {
    operator delete(Myfirst);
    return 0;
  }
  std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(
    (std::vector<CMonster *>::iterator)Myfirst,
    (std::vector<CMonster *>::iterator)targetVector._Mylast,
    targetVector._Mylast - Myfirst,
    0);
  v13 = *Myfirst;
  operator delete(Myfirst);
  return (CSkillMonster *)v13;
}

//----- (00456BF0) --------------------------------------------------------
CSkillMonster *__thiscall CSkillMonster::FindLowestHPMember(
        CSkillMonster *this,
        const AtType *attackType,
        bool bRegin,
        float fRemainHP)
{
  CMonster **Myfirst; // edi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  CParty *m_pParty; // ebp
  double v9; // st7
  unsigned __int8 m_cMemberNum; // al
  int v11; // esi
  CMonster *v12; // ecx
  double v13; // st7
  double v14; // st6
  CMonster *v15; // esi
  float fSquareEffectDistance; // [esp+Ch] [ebp-24h]
  CMonster *lpMonster; // [esp+10h] [ebp-20h] BYREF
  std::vector<CMonster *> targetVector; // [esp+14h] [ebp-1Ch] BYREF
  int v19; // [esp+2Ch] [ebp-4h]
  const AtType *attackTypea; // [esp+34h] [ebp+4h]

  Myfirst = 0;
  if ( this->m_pParty )
  {
    SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType->m_wType);
    if ( !SkillProtoType )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSkillMonster::FindLowestHPMember",
        aDWorkRylSource_51,
        1272,
        aCid0x08x_77,
        this->m_dwCID,
        attackType->m_wType);
      return 0;
    }
    m_pParty = this->m_pParty;
    v9 = SkillProtoType->m_ProtoTypes[*((unsigned __int8 *)attackType + 2) >> 4].m_EffectDistance + 10.0;
    fSquareEffectDistance = v9 * v9;
    m_cMemberNum = m_pParty->m_Party.m_cMemberNum;
    v11 = 0;
    v19 = 0;
    memset(&targetVector._Myfirst, 0, 12);
    if ( m_cMemberNum )
    {
      attackTypea = (const AtType *)&m_pParty[1];
      do
      {
        if ( (unsigned int)v11 >= 0xA )
          v12 = 0;
        else
          v12 = (CMonster *)*attackTypea;
        lpMonster = v12;
        if ( v12 )
        {
          if ( (double)v12->m_CreatureStatus.m_nNowHP <= (double)v12->m_CreatureStatus.m_StatusInfo.m_nMaxHP * fRemainHP )
          {
            if ( !bRegin
              || (v13 = this->m_CurrentPos.m_fPointZ - v12->m_CurrentPos.m_fPointZ,
                  v14 = this->m_CurrentPos.m_fPointX - v12->m_CurrentPos.m_fPointX,
                  v14 * v14 + v13 * v13 <= fSquareEffectDistance) )
            {
              std::vector<CMonster *>::push_back(&targetVector, &lpMonster);
              Myfirst = targetVector._Myfirst;
            }
          }
        }
        ++v11;
        ++attackTypea;
      }
      while ( v11 < m_pParty->m_Party.m_cMemberNum );
      if ( Myfirst )
      {
        if ( targetVector._Mylast - Myfirst )
        {
          std::_Sort<std::vector<CMonster *>::iterator,int,CompareHP>(
            (std::vector<CMonster *>::iterator)Myfirst,
            (std::vector<CMonster *>::iterator)targetVector._Mylast,
            targetVector._Mylast - Myfirst,
            0);
          v15 = *Myfirst;
          operator delete(Myfirst);
          return (CSkillMonster *)v15;
        }
        operator delete(Myfirst);
      }
    }
    return 0;
  }
  if ( (double)this->m_CreatureStatus.m_nNowHP > (double)this->m_CreatureStatus.m_StatusInfo.m_nMaxHP * fRemainHP )
    return 0;
  else
    return this;
}

//----- (00456E20) --------------------------------------------------------
void __thiscall std::set<unsigned long>::~set<unsigned long>(std::set<unsigned long> *this)
{
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00456E50) --------------------------------------------------------
char __thiscall CDefenderMonster::SkillAttack(CDefenderMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // ax
  CAggresiveCreature *m_lpTarget; // ecx
  double v9; // st6
  double v10; // st4
  unsigned __int16 v11; // ax
  unsigned __int16 v12; // ax
  CAggresiveCreature *v13; // eax
  AtType attackType; // [esp+4h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+Ch] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[3];
        if ( !v7 )
        {
          v6 = 1;
          continue;
        }
        m_lpTarget = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 3;
        attackType.m_wType = v7;
        if ( !m_lpTarget
          || (v9 = this->m_CurrentPos.m_fPointX - m_lpTarget->m_CurrentPos.m_fPointX,
              v10 = this->m_CurrentPos.m_fPointZ - m_lpTarget->m_CurrentPos.m_fPointZ,
              sqrt(v9 * v9 + v10 * v10) < 5.0) )
        {
          v6 = 1;
          continue;
        }
        ppAggresiveCreature[0] = m_lpTarget;
        goto LABEL_30;
      case 1:
        v11 = this->m_MonsterInfo.m_wSkillID[2];
        if ( v11 && (this->m_MonsterInfo.m_dwEnchantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 2;
          attackType.m_wType = v11;
          ppAggresiveCreature[0] = this;
          goto LABEL_30;
        }
        v6 = 2;
        continue;
      case 2:
        v12 = this->m_MonsterInfo.m_wSkillID[1];
        if ( !v12 )
        {
          v6 = 3;
          continue;
        }
        LOBYTE(nSkillPattern) = 1;
        attackType.m_wType = v12;
        ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
        if ( !ppAggresiveCreature[0] )
        {
          v6 = 3;
          continue;
        }
LABEL_30:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 4 )
            return 0;
          continue;
        }
        return 1;
      case 3:
        if ( !this->m_MonsterInfo.m_wSkillID[0] )
          return 0;
        attackType.m_wType = this->m_MonsterInfo.m_wSkillID[0];
        v13 = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 0;
        ppAggresiveCreature[0] = v13;
        goto LABEL_30;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CDefenderMonster::SkillAttack",
          aDWorkRylSource_51,
          1552,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457050) --------------------------------------------------------
char __thiscall CWarriorMonster::SkillAttack(CWarriorMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // ax
  CAggresiveCreature *m_lpTarget; // ecx
  double v9; // st6
  double v10; // st4
  unsigned __int16 v11; // ax
  unsigned __int16 v12; // ax
  CAggresiveCreature *v13; // eax
  AtType attackType; // [esp+4h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+Ch] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[3];
        if ( !v7 )
        {
          v6 = 1;
          continue;
        }
        m_lpTarget = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 3;
        attackType.m_wType = v7;
        if ( !m_lpTarget
          || (v9 = this->m_CurrentPos.m_fPointX - m_lpTarget->m_CurrentPos.m_fPointX,
              v10 = this->m_CurrentPos.m_fPointZ - m_lpTarget->m_CurrentPos.m_fPointZ,
              sqrt(v9 * v9 + v10 * v10) < 5.0) )
        {
          v6 = 1;
          continue;
        }
        ppAggresiveCreature[0] = m_lpTarget;
        goto LABEL_30;
      case 1:
        v11 = this->m_MonsterInfo.m_wSkillID[2];
        if ( v11 && (this->m_MonsterInfo.m_dwEnchantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 2;
          attackType.m_wType = v11;
          ppAggresiveCreature[0] = this;
          goto LABEL_30;
        }
        v6 = 2;
        continue;
      case 2:
        v12 = this->m_MonsterInfo.m_wSkillID[1];
        if ( !v12 )
        {
          v6 = 3;
          continue;
        }
        LOBYTE(nSkillPattern) = 1;
        attackType.m_wType = v12;
        ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
        if ( !ppAggresiveCreature[0] )
        {
          v6 = 3;
          continue;
        }
LABEL_30:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 4 )
            return 0;
          continue;
        }
        return 1;
      case 3:
        if ( !this->m_MonsterInfo.m_wSkillID[0] )
          return 0;
        attackType.m_wType = this->m_MonsterInfo.m_wSkillID[0];
        v13 = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 0;
        ppAggresiveCreature[0] = v13;
        goto LABEL_30;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CWarriorMonster::SkillAttack",
          aDWorkRylSource_51,
          1723,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457250) --------------------------------------------------------
char __thiscall CAcolyteMonster::SkillAttack(CAcolyteMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // ax
  unsigned __int16 v8; // ax
  CAggresiveCreature *m_lpTarget; // edx
  AtType attackType; // [esp+4h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+Ch] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        if ( !this->m_MonsterInfo.m_wSkillID[1] )
        {
          v6 = 1;
          continue;
        }
        attackType.m_wType = this->m_MonsterInfo.m_wSkillID[1];
        LOBYTE(nSkillPattern) = 1;
        ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
        if ( ppAggresiveCreature[0] )
          goto LABEL_27;
        v6 = 1;
        continue;
      case 1:
        v7 = this->m_MonsterInfo.m_wSkillID[3];
        if ( v7 )
        {
          LOBYTE(nSkillPattern) = 3;
          attackType.m_wType = v7;
          ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.69999999);
          if ( ppAggresiveCreature[0] )
            goto LABEL_27;
          v6 = 2;
        }
        else
        {
          v6 = 2;
        }
        continue;
      case 2:
        v8 = this->m_MonsterInfo.m_wSkillID[0];
        if ( !v8 )
        {
          v6 = 3;
          continue;
        }
        m_lpTarget = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 0;
        attackType.m_wType = v8;
        ppAggresiveCreature[0] = m_lpTarget;
LABEL_27:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 4 )
            return 0;
          continue;
        }
        return 1;
      case 3:
        if ( this->m_MonsterInfo.m_wSkillID[2] )
        {
          attackType.m_wType = this->m_MonsterInfo.m_wSkillID[2];
          LOBYTE(nSkillPattern) = 2;
          ppAggresiveCreature[0] = CSkillMonster::FindHighestLVMember(this, &attackType);
          if ( ppAggresiveCreature[0] )
            goto LABEL_27;
        }
        return 0;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CAcolyteMonster::SkillAttack",
          aDWorkRylSource_51,
          1906,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457430) --------------------------------------------------------
char __thiscall CChiefMonster::SkillAttack(CChiefMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // ax
  CAggresiveCreature *m_lpTarget; // ecx
  CAggresiveCreature *v9; // edx
  AtType attackType; // [esp+4h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+Ch] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[4];
        if ( v7 && (this->m_MonsterInfo.m_dwChantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 4;
          ppAggresiveCreature[0] = this;
          goto LABEL_31;
        }
        v6 = 1;
        continue;
      case 1:
        v7 = this->m_MonsterInfo.m_wSkillID[2];
        if ( !v7 || (this->m_MonsterInfo.m_dwEnchantSpellType & this->m_dwStatusFlag) != 0 )
        {
          v6 = 2;
          continue;
        }
        LOBYTE(nSkillPattern) = 2;
        ppAggresiveCreature[0] = this;
LABEL_31:
        attackType.m_wType = v7;
LABEL_32:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 5 )
            return 0;
          continue;
        }
        return 1;
      case 2:
        if ( this->m_MonsterInfo.m_wSkillID[1] )
        {
          attackType.m_wType = this->m_MonsterInfo.m_wSkillID[1];
          LOBYTE(nSkillPattern) = 1;
          ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
          if ( ppAggresiveCreature[0] )
            goto LABEL_32;
          v6 = 3;
        }
        else
        {
          v6 = 3;
        }
        continue;
      case 3:
        v7 = this->m_MonsterInfo.m_wSkillID[0];
        if ( v7 )
        {
          m_lpTarget = this->m_lpTarget;
          LOBYTE(nSkillPattern) = 0;
          ppAggresiveCreature[0] = m_lpTarget;
          goto LABEL_31;
        }
        v6 = 4;
        continue;
      case 4:
        v7 = this->m_MonsterInfo.m_wSkillID[3];
        if ( !v7 )
          return 0;
        v9 = this->m_lpTarget;
        LOBYTE(nSkillPattern) = 3;
        ppAggresiveCreature[0] = v9;
        goto LABEL_31;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CChiefMonster::SkillAttack",
          aDWorkRylSource_51,
          2465,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457630) --------------------------------------------------------
char __thiscall CNamedMonster::SkillAttack(CNamedMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // ebp
  unsigned __int16 v7; // ax
  unsigned __int16 v8; // ax
  unsigned __int16 v9; // ax
  unsigned __int16 v10; // ax
  unsigned __int16 v11; // ax
  unsigned __int16 m_wType; // di
  const char *v13; // eax
  CMonsterShout *Instance; // eax
  unsigned int m_dwCID; // [esp-24h] [ebp-58h]
  CPacketDispatch *m_dwKID; // [esp-20h] [ebp-54h]
  int m_fPointX; // [esp-1Ch] [ebp-50h]
  unsigned __int16 m_fPointZ; // [esp-18h] [ebp-4Ch]
  const char *v19; // [esp-10h] [ebp-44h]
  AtType attackType; // [esp+4h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+Ch] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[4];
        if ( v7 && (this->m_MonsterInfo.m_dwChantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 4;
          attackType.m_wType = v7;
          ppAggresiveCreature[0] = this;
          goto LABEL_34;
        }
        v6 = 1;
        continue;
      case 1:
        v8 = this->m_MonsterInfo.m_wSkillID[0];
        if ( !v8 )
        {
          v6 = 2;
          continue;
        }
        LOBYTE(nSkillPattern) = 0;
        attackType.m_wType = v8;
        ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
        if ( !ppAggresiveCreature[0] )
        {
          v6 = 2;
          continue;
        }
LABEL_33:
        ppAggresiveCreature[0] = this->m_lpTarget;
LABEL_34:
        m_wType = attackType.m_wType;
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 5 )
            return 0;
          continue;
        }
        v13 = 0;
        if ( ppAggresiveCreature[0] )
        {
          if ( (ppAggresiveCreature[0]->m_dwCID & 0xD0000000) == 0 )
            v13 = (const char *)&ppAggresiveCreature[0][2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1];
        }
        v19 = v13;
        m_fPointZ = (unsigned __int64)this->m_CurrentPos.m_fPointZ;
        m_fPointX = (unsigned __int64)this->m_CurrentPos.m_fPointX;
        m_dwKID = (CPacketDispatch *)this->m_MonsterInfo.m_dwKID;
        m_dwCID = this->m_dwCID;
        Instance = CMonsterShout::GetInstance();
        CMonsterShout::Shout(Instance, m_dwCID, m_dwKID, m_fPointX, m_fPointZ, RESPAWN, v19, m_wType);
        return 1;
      case 2:
        v9 = this->m_MonsterInfo.m_wSkillID[1];
        if ( v9 )
        {
          LOBYTE(nSkillPattern) = 1;
          attackType.m_wType = v9;
          ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.69999999);
          if ( ppAggresiveCreature[0] )
            goto LABEL_29;
          v6 = 3;
        }
        else
        {
          v6 = 3;
        }
        continue;
      case 3:
        v10 = this->m_MonsterInfo.m_wSkillID[2];
        if ( v10 && (this->m_MonsterInfo.m_dwEnchantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 2;
          attackType.m_wType = v10;
LABEL_29:
          ppAggresiveCreature[0] = this;
          goto LABEL_34;
        }
        v6 = 4;
        continue;
      case 4:
        v11 = this->m_MonsterInfo.m_wSkillID[3];
        if ( !v11 )
          return 0;
        LOBYTE(nSkillPattern) = 3;
        attackType.m_wType = v11;
        goto LABEL_33;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CNamedMonster::SkillAttack",
          aDWorkRylSource_51,
          2685,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}


//----- (004578A0) --------------------------------------------------------
CAggresiveCreature *__thiscall CSkillMonster::FindEnemy(
        CSkillMonster *this,
        AtType *attackType,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator cType1,
        unsigned __int8 cType2)
{
  const AtType *v4; // esi
  CCreature *v6; // edi
  CParty *m_pParty; // eax
  const std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v9; // esi
  CAggresiveCreature *m_lpTarget; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Left; // ecx
  bool v13; // zf
  unsigned __int8 v14; // bl
  CCreatureManager *Instance; // eax
  CCreature *AggresiveCreature; // eax
  CCreature *v17; // esi
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator v18; // ecx
  signed int Myval; // [esp-8h] [ebp-34h]
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // [esp-4h] [ebp-30h]
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator end; // [esp+Ch] [ebp-20h] BYREF
  std::set<unsigned long> targetSet; // [esp+14h] [ebp-18h] BYREF
  int v23; // [esp+28h] [ebp-4h]

  v4 = attackType;
  v6 = 0;
  if ( !CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType->m_wType) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMonster::FindEnemy",
      aDWorkRylSource_51,
      1333,
      aCid0x08x_77,
      this->m_dwCID,
      v4->m_wType);
    return 0;
  }
  targetSet._Myhead = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(&targetSet);
  targetSet._Myhead->_Isnil = 1;
  targetSet._Myhead->_Parent = targetSet._Myhead;
  targetSet._Myhead->_Left = targetSet._Myhead;
  targetSet._Myhead->_Right = targetSet._Myhead;
  targetSet._Mysize = 0;
  Parent = targetSet._Myhead->_Parent;
  v23 = 0;
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Erase(
    &targetSet,
    Parent);
  targetSet._Myhead->_Parent = targetSet._Myhead;
  targetSet._Mysize = 0;
  targetSet._Myhead->_Left = targetSet._Myhead;
  targetSet._Myhead->_Right = targetSet._Myhead;
  m_pParty = this->m_pParty;
  if ( m_pParty )
  {
    v9 = (const std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)&m_pParty[1].m_PartySpellMgr.m_pPartyMember[9];
    if ( &targetSet != (std::set<unsigned long> *)&m_pParty[1].m_PartySpellMgr.m_pPartyMember[9] )
    {
      std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
        &targetSet,
        (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *)&attackType,
        (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)targetSet._Myhead->_Left,
        (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)targetSet._Myhead);
      std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
        &targetSet,
        v9);
    }
  }
  m_lpTarget = this->m_lpTarget;
  if ( m_lpTarget )
  {
    attackType = (AtType *)m_lpTarget->m_dwCID;
    std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(
      &targetSet,
      (std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *)&end,
      (unsigned int *)&attackType);
  }
  Myhead = targetSet._Myhead;
  Left = targetSet._Myhead->_Left;
  v13 = targetSet._Myhead->_Left == targetSet._Myhead;
  attackType = (AtType *)targetSet._Myhead->_Left;
  end._Ptr = targetSet._Myhead;
  if ( !v13 )
  {
    v14 = cType2;
    while ( 1 )
    {
      Myval = Left->_Myval;
      Instance = CCreatureManager::GetInstance();
      AggresiveCreature = (CCreature *)CCreatureManager::GetAggresiveCreature(Instance, Myval);
      v17 = AggresiveCreature;
      if ( !AggresiveCreature || this->IsEnemy(this, AggresiveCreature) != HOSTILITY )
        goto LABEL_27;
      if ( !v6 )
        goto LABEL_26;
      if ( LOBYTE(cType1._Ptr) )
      {
        if ( LOBYTE(cType1._Ptr) == 1 )
        {
          if ( v14 )
          {
            if ( v14 != 1 || LOWORD(v6[3].m_MotionInfo.m_dwFrame) <= LOWORD(v17[3].m_MotionInfo.m_dwFrame) )
              goto LABEL_27;
            goto LABEL_26;
          }
          if ( LOWORD(v6[3].m_MotionInfo.m_dwFrame) < LOWORD(v17[3].m_MotionInfo.m_dwFrame) )
            goto LABEL_26;
        }
      }
      else
      {
        if ( v14 )
        {
          if ( v14 != 1 || *(_DWORD *)&v6[3].m_MotionInfo.m_wAction <= *(_DWORD *)&v17[3].m_MotionInfo.m_wAction )
            goto LABEL_27;
LABEL_26:
          v6 = v17;
          goto LABEL_27;
        }
        if ( *(_DWORD *)&v6[3].m_MotionInfo.m_wAction < *(_DWORD *)&v17[3].m_MotionInfo.m_wAction )
          goto LABEL_26;
      }
LABEL_27:
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *)&attackType);
      Left = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)attackType;
      if ( attackType == (AtType *)end._Ptr )
      {
        Myhead = targetSet._Myhead;
        break;
      }
    }
  }
  v18._Ptr = Myhead->_Left;
  v23 = -1;
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
    &targetSet,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *)&cType2,
    v18,
    (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)Myhead);
  operator delete(targetSet._Myhead);
  return (CAggresiveCreature *)v6;
}

//----- (00457AD0) --------------------------------------------------------
char __thiscall CMageMonster::SkillAttack(CMageMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // cx
  int m_nMaxMP; // edx
  double v9; // st7
  unsigned __int16 v10; // ax
  unsigned __int16 v11; // ax
  AtType attackType; // [esp+4h] [ebp-34h] BYREF
  int nSkillPattern; // [esp+8h] [ebp-30h]
  int m_nNowMP; // [esp+Ch] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+10h] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[1];
        if ( v7 )
        {
          m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
          m_nNowMP = this->m_CreatureStatus.m_nNowMP;
          v9 = (double)m_nNowMP;
          m_nNowMP = m_nMaxMP;
          if ( (double)m_nMaxMP * 0.30000001 >= v9 )
          {
            LOBYTE(nSkillPattern) = 1;
            attackType.m_wType = v7;
            ppAggresiveCreature[0] = this;
            goto LABEL_28;
          }
        }
        v6 = 1;
        continue;
      case 1:
        v10 = this->m_MonsterInfo.m_wSkillID[2];
        if ( v10 && (this->m_MonsterInfo.m_dwEnchantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 2;
          attackType.m_wType = v10;
          ppAggresiveCreature[0] = this;
          goto LABEL_28;
        }
        v6 = 2;
        continue;
      case 2:
        v11 = this->m_MonsterInfo.m_wSkillID[0];
        if ( !v11 )
        {
          v6 = 3;
          continue;
        }
        LOBYTE(nSkillPattern) = 0;
        attackType.m_wType = v11;
        ppAggresiveCreature[0] = CSkillMonster::FindEnemy(
                                   this,
                                   &attackType,
                                   (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)1,
                                   1u);
        if ( !ppAggresiveCreature[0] )
        {
          v6 = 3;
          continue;
        }
LABEL_28:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 4 )
            return 0;
          continue;
        }
        return 1;
      case 3:
        if ( this->m_MonsterInfo.m_wSkillID[3] )
        {
          attackType.m_wType = this->m_MonsterInfo.m_wSkillID[3];
          LOBYTE(nSkillPattern) = 3;
          ppAggresiveCreature[0] = CSkillMonster::FindEnemy(
                                     this,
                                     &attackType,
                                     (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)1,
                                     1u);
          if ( ppAggresiveCreature[0] )
            goto LABEL_28;
        }
        return 0;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CMageMonster::SkillAttack",
          aDWorkRylSource_51,
          2079,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457CD0) --------------------------------------------------------
char __thiscall CBossMonster::SkillAttack(CBossMonster *this)
{
  __int16 v2; // ax
  __int16 v3; // cx
  bool v4; // zf
  int v6; // edi
  unsigned __int16 v7; // ax
  unsigned __int16 v8; // ax
  unsigned __int16 v9; // ax
  CAggresiveCreature *m_lpTarget; // ecx
  double v11; // st5
  double v12; // st7
  double v13; // st5
  unsigned __int16 v14; // ax
  AtType attackType; // [esp+8h] [ebp-30h] BYREF
  int nSkillPattern; // [esp+Ch] [ebp-2Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+10h] [ebp-28h] BYREF

  v2 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v3 = this->m_MonsterInfo.m_cSkillLevel % 6;
  if ( (unsigned __int8)v2 >= 4u )
  {
    LOBYTE(v2) = 3;
LABEL_4:
    if ( (_BYTE)v3 != 3 )
    {
      if ( (unsigned __int8)v3 < 3u )
        LOBYTE(v2) = v2 - 1;
      LOBYTE(v3) = 3;
    }
    v4 = (_BYTE)v2 == 0;
    goto LABEL_9;
  }
  v4 = (_BYTE)v2 == 0;
  if ( (_BYTE)v2 )
    goto LABEL_4;
LABEL_9:
  if ( v4 && !(_BYTE)v3 )
    return 0;
  v6 = 0;
  memset(ppAggresiveCreature, 0, sizeof(ppAggresiveCreature));
  *((_BYTE *)&attackType + 2) = (16 * v2) | *((_BYTE *)&attackType + 2) & 0xF;
  attackType.m_cSkillLevel = v3;
  while ( 2 )
  {
    switch ( v6 )
    {
      case 0:
        v7 = this->m_MonsterInfo.m_wSkillID[4];
        if ( v7 && (this->m_MonsterInfo.m_dwChantSpellType & this->m_dwStatusFlag) == 0 )
        {
          LOBYTE(nSkillPattern) = 4;
          attackType.m_wType = v7;
          ppAggresiveCreature[0] = this;
          goto LABEL_34;
        }
        v6 = 1;
        continue;
      case 1:
        v8 = this->m_MonsterInfo.m_wSkillID[0];
        if ( v8 )
        {
          LOBYTE(nSkillPattern) = 0;
          attackType.m_wType = v8;
          ppAggresiveCreature[0] = CSkillMonster::FindLowestHPMember(this, &attackType, 1, 0.5);
          if ( ppAggresiveCreature[0] )
            goto LABEL_34;
          v6 = 2;
        }
        else
        {
          v6 = 2;
        }
        continue;
      case 2:
        v9 = this->m_MonsterInfo.m_wSkillID[0];
        if ( v9 )
        {
          m_lpTarget = this->m_lpTarget;
          LOBYTE(nSkillPattern) = 0;
          attackType.m_wType = v9;
          if ( m_lpTarget )
          {
            v11 = this->m_CurrentPos.m_fPointZ - m_lpTarget->m_CurrentPos.m_fPointZ;
            v12 = v11 * v11;
            v13 = this->m_CurrentPos.m_fPointX - m_lpTarget->m_CurrentPos.m_fPointX;
            if ( sqrt(v12 + v13 * v13) >= 9.0 )
            {
              ppAggresiveCreature[0] = m_lpTarget;
              goto LABEL_34;
            }
          }
          v6 = 3;
        }
        else
        {
          v6 = 3;
        }
        continue;
      case 3:
        if ( !this->m_MonsterInfo.m_wSkillID[3] )
        {
          v6 = 4;
          continue;
        }
        attackType.m_wType = this->m_MonsterInfo.m_wSkillID[3];
        LOBYTE(nSkillPattern) = 3;
        ppAggresiveCreature[0] = CSkillMonster::FindEnemy(
                                   this,
                                   &attackType,
                                   (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)1,
                                   0);
        if ( !ppAggresiveCreature[0] )
        {
          v6 = 4;
          continue;
        }
LABEL_34:
        if ( CSkillMonster::UseSkill(this, attackType, ppAggresiveCreature, nSkillPattern) != 1 )
        {
          if ( ++v6 >= 5 )
            return 0;
          continue;
        }
        return 1;
      case 4:
        v14 = this->m_MonsterInfo.m_wSkillID[1];
        if ( v14 )
        {
          LOBYTE(nSkillPattern) = 1;
          attackType.m_wType = v14;
          ppAggresiveCreature[0] = CSkillMonster::FindEnemy(
                                     this,
                                     &attackType,
                                     (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator)1,
                                     0);
          if ( ppAggresiveCreature[0] )
            goto LABEL_34;
        }
        return 0;
      default:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CBossMonster::SkillAttack",
          aDWorkRylSource_51,
          2282,
          aCid0x08x_284,
          this->m_dwCID);
        return 0;
    }
  }
}

//----- (00457F30) --------------------------------------------------------
void __thiscall EliteBonus::EliteBonusData::EliteBonusData(EliteBonus::EliteBonusData *this)
{
  this->m_cNation = 2;
  this->m_cLevel = 0;
}

//----- (00457F40) --------------------------------------------------------
void __thiscall EliteBonus::EliteBonusData::EliteBonusData(
        EliteBonus::EliteBonusData *this,
        char cNation,
        unsigned __int8 cLevel)
{
  this->m_cNation = cNation;
  this->m_cLevel = cLevel;
}

//----- (00457F60) --------------------------------------------------------
void __thiscall MotionInfo::MotionInfo(MotionInfo *this)
{
  this->m_fDirection = 0.0;
  this->m_fVelocity = 0.0;
  this->m_wAction = 0;
  this->m_dwFrame = 0;
}

//----- (00457F80) --------------------------------------------------------
void __thiscall CharacterStatus::CharacterStatus(CharacterStatus *this)
{
  this->m_nSTR = 0;
  this->m_nDEX = 0;
  this->m_nCON = 0;
  this->m_nINT = 0;
  this->m_nWIS = 0;
}

//----- (00457FA0) --------------------------------------------------------
void __thiscall CharacterStatus::Init(CharacterStatus *this, _CHAR_INFOST *characterDBData)
{
  *this = *(CharacterStatus *)&characterDBData->STR;
}

//----- (00457FD0) --------------------------------------------------------
void __thiscall StatusInfo::operator+=(StatusInfo *this, const StatusInfo *rhs)
{
  this->m_nCriticalType += rhs->m_nCriticalType;
  this->m_nCriticalPercentage += rhs->m_nCriticalPercentage;
  this->m_nMinDamage += rhs->m_nMinDamage;
  this->m_nMaxDamage += rhs->m_nMaxDamage;
  this->m_nOffenceRevision += rhs->m_nOffenceRevision;
  this->m_fDRC = rhs->m_fDRC + this->m_fDRC;
  this->m_nDefence += rhs->m_nDefence;
  this->m_nDefenceRevision += rhs->m_nDefenceRevision;
  this->m_nBlockingPercentage += rhs->m_nBlockingPercentage;
  this->m_nMagicPower += rhs->m_nMagicPower;
  this->m_nMagicResistance += rhs->m_nMagicResistance;
  this->m_nAttackSpeed += rhs->m_nAttackSpeed;
  this->m_nMoveSpeed += rhs->m_nMoveSpeed;
  this->m_nAttackRange += rhs->m_nAttackRange;
  this->m_nRangeAttackDistance += rhs->m_nRangeAttackDistance;
  this->m_nMaxHP += rhs->m_nMaxHP;
  this->m_nMaxMP += rhs->m_nMaxMP;
  this->m_nHPRegenAmount += rhs->m_nHPRegenAmount;
  this->m_nMPRegenAmount += rhs->m_nMPRegenAmount;
  this->m_nWeaponAttributeLevel[0] += rhs->m_nWeaponAttributeLevel[0];
  this->m_nAttributeResistance[0] += rhs->m_nAttributeResistance[0];
  this->m_nWeaponAttributeLevel[1] += rhs->m_nWeaponAttributeLevel[1];
  this->m_nAttributeResistance[1] += rhs->m_nAttributeResistance[1];
  this->m_nWeaponAttributeLevel[2] += rhs->m_nWeaponAttributeLevel[2];
  this->m_nAttributeResistance[2] += rhs->m_nAttributeResistance[2];
  this->m_nWeaponAttributeLevel[3] += rhs->m_nWeaponAttributeLevel[3];
  this->m_nAttributeResistance[3] += rhs->m_nAttributeResistance[3];
  this->m_nWeaponAttributeLevel[4] += rhs->m_nWeaponAttributeLevel[4];
  this->m_nAttributeResistance[4] += rhs->m_nAttributeResistance[4];
}

//----- (004580C0) --------------------------------------------------------
void __thiscall StatusInfo::operator-=(StatusInfo *this, const StatusInfo *rhs)
{
  double m_fDRC; // st7
  bool v3; // sf
  __int16 *m_nAttributeResistance; // esi
  __int16 *m_nWeaponAttributeLevel; // eax
  int v6; // edx
  int v7; // ecx

  m_fDRC = this->m_fDRC;
  this->m_nCriticalType -= rhs->m_nCriticalType;
  this->m_nCriticalPercentage -= rhs->m_nCriticalPercentage;
  this->m_nMinDamage -= rhs->m_nMinDamage;
  this->m_nMaxDamage -= rhs->m_nMaxDamage;
  this->m_nOffenceRevision -= rhs->m_nOffenceRevision;
  v3 = this->m_nMaxDamage < 0;
  this->m_fDRC = m_fDRC - rhs->m_fDRC;
  if ( v3 )
    this->m_nMaxDamage = 0;
  if ( this->m_nMinDamage < 0 )
    this->m_nMinDamage = 0;
  if ( this->m_nOffenceRevision < 0 )
    this->m_nOffenceRevision = 0;
  this->m_nDefence -= rhs->m_nDefence;
  this->m_nDefenceRevision -= rhs->m_nDefenceRevision;
  this->m_nBlockingPercentage -= rhs->m_nBlockingPercentage;
  if ( this->m_nDefence < 0 )
    this->m_nDefence = 0;
  if ( this->m_nDefenceRevision < 0 )
    this->m_nDefenceRevision = 0;
  if ( this->m_nBlockingPercentage < 0 )
    this->m_nBlockingPercentage = 0;
  this->m_nMagicPower -= rhs->m_nMagicPower;
  this->m_nMagicResistance -= rhs->m_nMagicResistance;
  if ( this->m_nMagicPower < 0 )
    this->m_nMagicPower = 0;
  if ( this->m_nMagicResistance < 0 )
    this->m_nMagicResistance = 0;
  this->m_nAttackSpeed -= rhs->m_nAttackSpeed;
  this->m_nMoveSpeed -= rhs->m_nMoveSpeed;
  this->m_nAttackRange -= rhs->m_nAttackRange;
  this->m_nRangeAttackDistance -= rhs->m_nRangeAttackDistance;
  if ( this->m_nAttackSpeed < 0 )
    this->m_nAttackSpeed = 0;
  if ( this->m_nMoveSpeed < 0 )
    this->m_nMoveSpeed = 0;
  if ( this->m_nAttackRange < 0 )
    this->m_nAttackRange = 0;
  if ( this->m_nRangeAttackDistance < 0 )
    this->m_nRangeAttackDistance = 0;
  this->m_nMaxHP -= rhs->m_nMaxHP;
  this->m_nMaxMP -= rhs->m_nMaxMP;
  this->m_nHPRegenAmount -= rhs->m_nHPRegenAmount;
  this->m_nMPRegenAmount -= rhs->m_nMPRegenAmount;
  if ( this->m_nHPRegenAmount < 0 )
    this->m_nHPRegenAmount = 0;
  if ( this->m_nMPRegenAmount < 0 )
    this->m_nMPRegenAmount = 0;
  m_nAttributeResistance = rhs->m_nAttributeResistance;
  m_nWeaponAttributeLevel = this->m_nWeaponAttributeLevel;
  v6 = (char *)rhs - (char *)this;
  v7 = 5;
  do
  {
    *m_nWeaponAttributeLevel -= *(__int16 *)((char *)m_nWeaponAttributeLevel + v6);
    m_nWeaponAttributeLevel[5] -= *m_nAttributeResistance;
    ++m_nWeaponAttributeLevel;
    ++m_nAttributeResistance;
    --v7;
  }
  while ( v7 );
}

//----- (00458220) --------------------------------------------------------
StatusInfo *__thiscall StatusInfo::CalculateDoubleSword(
        StatusInfo *this,
        StatusInfo *result,
        Item::EquipType::DoubleSwordType eDoubleSwordType)
{
  int m_nRangeAttackDistance; // kr34_4
  StatusInfo *v4; // eax

  if ( eDoubleSwordType != SoloSword )
  {
    this->m_nCriticalType /= 2;
    this->m_nCriticalPercentage /= 2;
    this->m_nMinDamage /= 2;
    this->m_nMaxDamage /= 2;
    this->m_nOffenceRevision /= 2;
    this->m_nDefence /= 2;
    this->m_nDefenceRevision /= 2;
    this->m_nBlockingPercentage /= 2;
    this->m_nMagicPower /= 2;
    this->m_nMagicResistance /= 2;
    this->m_nAttackSpeed /= 2;
    this->m_nMoveSpeed /= 2;
    this->m_nAttackRange /= 2;
    m_nRangeAttackDistance = this->m_nRangeAttackDistance;
    this->m_nMaxHP >>= 1;
    this->m_nMaxMP >>= 1;
    this->m_nRangeAttackDistance = m_nRangeAttackDistance / 2;
    this->m_nHPRegenAmount /= 2;
    this->m_nMPRegenAmount /= 2;
    if ( eDoubleSwordType == WarriorDoubleSword )
    {
      v4 = result;
      this->m_fDRC = 1.1;
      qmemcpy(result, this, sizeof(StatusInfo));
      return v4;
    }
    if ( eDoubleSwordType == AssasinDoubleDagger )
      this->m_fDRC = 1.2;
  }
  v4 = result;
  qmemcpy(result, this, sizeof(StatusInfo));
  return v4;
}

//----- (00458350) --------------------------------------------------------
void __thiscall CreatureStatus::Init(CreatureStatus *this, _CHAR_INFOST *characterDBData)
{
  this->m_nLevel = characterDBData->Level;
  this->m_nExp = characterDBData->Exp;
  this->m_nNowHP = characterDBData->HP;
  this->m_nNowMP = characterDBData->MP;
}

//----- (00458380) --------------------------------------------------------
void __thiscall StatusInfo::StatusInfo(StatusInfo *this)
{
  this->m_nCriticalType = 0;
  this->m_nCriticalPercentage = 0;
  this->m_nMinDamage = 0;
  this->m_nMaxDamage = 0;
  this->m_nOffenceRevision = 0;
  this->m_fDRC = 0.0;
  this->m_nDefence = 0;
  this->m_nDefenceRevision = 0;
  this->m_nBlockingPercentage = 0;
  this->m_nMagicPower = 0;
  this->m_nMagicResistance = 0;
  this->m_nAttackSpeed = 0;
  this->m_nMoveSpeed = 0;
  this->m_nAttackRange = 0;
  this->m_nRangeAttackDistance = 0;
  this->m_nMaxHP = 0;
  this->m_nMaxMP = 0;
  this->m_nHPRegenAmount = 0;
  this->m_nMPRegenAmount = 0;
  *(_DWORD *)this->m_nWeaponAttributeLevel = 0;
  *(_DWORD *)&this->m_nWeaponAttributeLevel[2] = 0;
  this->m_nWeaponAttributeLevel[4] = 0;
  *(_DWORD *)this->m_nAttributeResistance = 0;
  *(_DWORD *)&this->m_nAttributeResistance[2] = 0;
  this->m_nAttributeResistance[4] = 0;
}

//----- (004583F0) --------------------------------------------------------
void __thiscall CreatureStatus::CreatureStatus(CreatureStatus *this)
{
  this->m_nExp = 0LL;
  this->m_nLevel = 0;
  this->m_nNowHP = 0;
  this->m_nNowMP = 0;
  StatusInfo::StatusInfo(&this->m_StatusInfo);
}

//----- (00458420) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (00458440) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (004584A0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (00458500) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)operator new((tagHeader *)0x28);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}



//----- (00458540) --------------------------------------------------------
void __thiscall std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Node::~_Node(
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *this)
{
  std::list<CTimerProcMgr::InternalProcessData> *p_m_timerProcessList; // esi

  p_m_timerProcessList = &this->_Myval.second.m_timerProcessList;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->_Myval.second.m_timerProcessList);
  operator delete(p_m_timerProcessList->_Myhead);
  p_m_timerProcessList->_Myhead = 0;
}

//----- (00458560) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *v15; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Left; // eax
  bool v17; // zf
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v18; // ecx
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *Next; // eax
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v20; // ecx
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v21; // esi
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *v23; // eax
  std::string _Message; // [esp+10h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+2Ch] [ebp-34h] BYREF
  int v27; // [esp+5Ch] [ebp-4h]

  Ptr = _Where._Ptr;
  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v27 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == Ptr )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == Ptr )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( Ptr->_Color == 1 )
  {
    v15 = this;
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
              v15,
              Parent);
            Left = Parent->_Right;
            v15 = this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
                v15,
                Left);
              Left = Parent->_Right;
              v15 = this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
              v15,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
              v15,
              Parent);
            Left = Parent->_Left;
            v15 = this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(
                v15,
                Left);
              Left = Parent->_Left;
              v15 = this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(
              v15,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v17 = Parent == v15->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v17 );
    }
    Right->_Color = 1;
  }
  v18 = Ptr->_Myval.second.m_timerProcessList._Myhead;
  Next = v18->_Next;
  v18->_Next = v18;
  Ptr->_Myval.second.m_timerProcessList._Myhead->_Prev = Ptr->_Myval.second.m_timerProcessList._Myhead;
  v20 = Ptr->_Myval.second.m_timerProcessList._Myhead;
  Ptr->_Myval.second.m_timerProcessList._Mysize = 0;
  if ( Next != v20 )
  {
    do
    {
      v21 = Next->_Next;
      operator delete(Next);
      Next = v21;
    }
    while ( v21 != Ptr->_Myval.second.m_timerProcessList._Myhead );
  }
  operator delete(Ptr->_Myval.second.m_timerProcessList._Myhead);
  Ptr->_Myval.second.m_timerProcessList._Myhead = 0;
  operator delete(Ptr);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v23 = result;
  result->_Ptr = _Where._Ptr;
  return v23;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00458860) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Node::~_Node(v2);
    operator delete(v2);
  }
}

//----- (004588A0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00458960) --------------------------------------------------------
void __thiscall CTimerProcMgr::ClearAll(CTimerProcMgr *this)
{
  CTimerProcMgr *v1; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Left; // esi
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v4; // ebp
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *i; // edi
  CTimerProc *m_lpTimerProc; // ecx
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v7; // ecx
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *Next; // eax
  bool v9; // zf
  std::_List_nod<CTimerProcMgr::InternalProcessData>::_Node *v10; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *k; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v14; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v15; // [esp+8h] [ebp-8h]

  v1 = this;
  Myhead = this->m_TimerMap._Myhead;
  Left = Myhead->_Left;
  v15 = Myhead;
  if ( Myhead->_Left != Myhead )
  {
    do
    {
      KillTimer(Left->_Myval.second.m_hOwnerWnd, Left->_Myval.first);
      v4 = Left->_Myval.second.m_timerProcessList._Myhead;
      for ( i = v4->_Next; i != v4; i = i->_Next )
      {
        m_lpTimerProc = i->_Myval.m_lpTimerProc;
        if ( m_lpTimerProc )
          ((void (__thiscall *)(CTimerProc *, int))m_lpTimerProc->~CTimerProc)(m_lpTimerProc, 1);
      }
      v7 = Left->_Myval.second.m_timerProcessList._Myhead;
      Next = v7->_Next;
      v7->_Next = v7;
      Left->_Myval.second.m_timerProcessList._Myhead->_Prev = Left->_Myval.second.m_timerProcessList._Myhead;
      v9 = Next == Left->_Myval.second.m_timerProcessList._Myhead;
      Left->_Myval.second.m_timerProcessList._Mysize = 0;
      if ( !v9 )
      {
        do
        {
          v10 = Next->_Next;
          operator delete(Next);
          Next = v10;
        }
        while ( v10 != Left->_Myval.second.m_timerProcessList._Myhead );
      }
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( j = Left->_Parent; !j->_Isnil; j = j->_Parent )
          {
            if ( Left != j->_Right )
              break;
            Left = j;
          }
          Left = j;
        }
        else
        {
          Left = Left->_Right;
          for ( k = Right->_Left; !k->_Isnil; k = k->_Left )
            Left = k;
        }
      }
    }
    while ( Left != v15 );
    v1 = this;
  }
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Erase(
    &v1->m_TimerMap,
    v1->m_TimerMap._Myhead->_Parent);
  v1->m_TimerMap._Myhead->_Parent = v1->m_TimerMap._Myhead;
  v14 = v1->m_TimerMap._Myhead;
  v1->m_TimerMap._Mysize = 0;
  v14->_Left = v14;
  v1->m_TimerMap._Myhead->_Right = v1->m_TimerMap._Myhead;
}

//----- (00458A60) --------------------------------------------------------
void __thiscall std::map<unsigned int,CTimerProcMgr::InternalTimerData>::~map<unsigned int,CTimerProcMgr::InternalTimerData>(
        std::map<unsigned int,CTimerProcMgr::InternalTimerData> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00458A90) --------------------------------------------------------
void __thiscall CTimerProcMgr::~CTimerProcMgr(CTimerProcMgr *this)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator v2; // [esp-8h] [ebp-1Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Myhead; // [esp-4h] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator result; // [esp+4h] [ebp-10h] BYREF
  int v5; // [esp+10h] [ebp-4h]

  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *)this;
  v5 = 0;
  CTimerProcMgr::ClearAll(this);
  Myhead = this->m_TimerMap._Myhead;
  v2._Ptr = Myhead->_Left;
  v5 = -1;
  std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(
    &this->m_TimerMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator)Myhead);
  operator delete(this->m_TimerMap._Myhead);
  this->m_TimerMap._Myhead = 0;
  this->m_TimerMap._Mysize = 0;
}

//----- (00458B00) --------------------------------------------------------
void __thiscall CTimerProcMgr::CTimerProcMgr(CTimerProcMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *v2; // eax

  v2 = std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode(&this->m_TimerMap);
  this->m_TimerMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_TimerMap._Myhead->_Parent = this->m_TimerMap._Myhead;
  this->m_TimerMap._Myhead->_Left = this->m_TimerMap._Myhead;
  this->m_TimerMap._Myhead->_Right = this->m_TimerMap._Myhead;
  this->m_TimerMap._Mysize = 0;
  this->m_nProcIDCounter = 1;
  this->m_nTimerIDCounter = 1;
}

//----- (00458B40) --------------------------------------------------------
void __thiscall CSysTray::CSysTray(CSysTray *this, HWND__ *hWnd, HINSTANCE__ *hInstance)
{
  this->m_hWnd = hWnd;
  this->m_hInstance = hInstance;
  this->m_uIconCount = 0;
  memset(this, 0, 0x58u);
}

//----- (00458B70) --------------------------------------------------------
void __thiscall CSysTray::ShowPopupMenu(CSysTray *this, HWND__ *hWnd, HMENU__ *hMenu)
{
  tagPOINT mouse; // [esp+8h] [ebp-8h] BYREF

  GetCursorPos(&mouse);
  SetForegroundWindow(hWnd);
  TrackPopupMenu(hMenu, 2u, mouse.x, mouse.y, 0, hWnd, 0);
  SetForegroundWindow(hWnd);
}

//----- (00458BC0) --------------------------------------------------------
UINT __cdecl CSysTray::GetSysTrayNotifyMsg()
{
  UINT result; // eax

  if ( (_S1_5 & 1) != 0 )
    return s_MsgID;
  _S1_5 |= 1u;
  result = RegisterWindowMessageA("CSysTrayNotifyMessage");
  s_MsgID = result;
  return result;
}

//----- (00458C00) --------------------------------------------------------
void __thiscall CSysTray::~CSysTray(CSysTray *this)
{
  if ( Shell_NotifyIconA(2u, &this->m_IconData) )
  {
    if ( this->m_IconData.hIcon )
      DestroyIcon(this->m_IconData.hIcon);
    --this->m_uIconCount;
    memset(this, 0, 0x58u);
  }
}

//----- (00458C30) --------------------------------------------------------
BOOL __thiscall CSysTray::AddIcon(CSysTray *this, const char *lpToolTip, HICON__ *hIcon, unsigned int uID)
{
  bool v5; // zf
  BOOL result; // eax

  if ( this->m_IconData.cbSize && Shell_NotifyIconA(2u, &this->m_IconData) )
  {
    if ( this->m_IconData.hIcon )
      DestroyIcon(this->m_IconData.hIcon);
    --this->m_uIconCount;
    memset(this, 0, 0x58u);
  }
  this->m_IconData.hWnd = this->m_hWnd;
  this->m_IconData.uID = uID;
  v5 = (_S1_5 & 1) == 0;
  this->m_IconData.cbSize = 88;
  this->m_IconData.uFlags = 7;
  this->m_IconData.hIcon = hIcon;
  if ( v5 )
  {
    _S1_5 |= 1u;
    s_MsgID = RegisterWindowMessageA("CSysTrayNotifyMessage");
  }
  this->m_IconData.uCallbackMessage = s_MsgID;
  if ( lpToolTip )
    _snprintf(this->m_IconData.szTip, 0x40u, "%s", lpToolTip);
  else
    _snprintf(this->m_IconData.szTip, 0x40u, "Temp Application");
  result = Shell_NotifyIconA(0, &this->m_IconData);
  if ( result )
    ++this->m_uIconCount;
  return result;
}

//----- (00458D10) --------------------------------------------------------
int __thiscall  __thiscall `vcall'{0,{flat}}(int (__thiscall ***this)(_DWORD))
{
  return (**this)(this);
}

//----- (00458D20) --------------------------------------------------------
int __thiscall  __thiscall `vcall'{4,{flat}}(void *this)
{
  return (*(int (__thiscall **)(void *))(*(_DWORD *)this + 4))(this);
}

//----- (00458D30) --------------------------------------------------------
std::mem_fun_t<bool,CCommand> *__cdecl std::for_each<std::list<CCommand *>::iterator,std::mem_fun_t<bool,CCommand>>(
        std::mem_fun_t<bool,CCommand> *result,
        std::list<CCommand *>::iterator _First,
        std::list<CCommand *>::iterator _Last,
        std::mem_fun_t<bool,CCommand> _Func)
{
  std::_List_nod<CCommand *>::_Node *Ptr; // esi
  std::mem_fun_t<bool,CCommand> *v5; // eax

  Ptr = _First._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v5 = result;
    result->_Pmemfun = _Func._Pmemfun;
  }
  else
  {
    do
    {
      _Func._Pmemfun(Ptr->_Myval);
      Ptr = Ptr->_Next;
    }
    while ( Ptr != _Last._Ptr );
    v5 = result;
    result->_Pmemfun = _Func._Pmemfun;
  }
  return v5;
}

//----- (00458D70) --------------------------------------------------------
void __thiscall CCommandProcess::ClearAll(CCommandProcess *this)
{
  unsigned int Mysize; // eax
  std::mem_fun_t<bool,CCommand> result; // [esp+8h] [ebp-10h] BYREF
  int v4; // [esp+14h] [ebp-4h]

  EnterCriticalSection(&this->m_CMDLock.m_CSLock);
  Mysize = this->m_CMDList._Mysize;
  v4 = 0;
  if ( Mysize )
  {
    std::for_each<std::list<CCommand *>::iterator,std::mem_fun_t<bool,CCommand>>(
      &result,
      (std::list<CCommand *>::iterator)this->m_CMDList._Myhead->_Next,
      (std::list<CCommand *>::iterator)this->m_CMDList._Myhead,
      (std::mem_fun_t<bool,CCommand>) __thiscall `vcall'{4,{flat}});
    std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CMDList);
  }
  LeaveCriticalSection(&this->m_CMDLock.m_CSLock);
}

//----- (00458DE0) --------------------------------------------------------
void __thiscall CCommandProcess::CCommandProcess(CCommandProcess *this)
{
  InitializeCriticalSection(&this->m_CMDLock.m_CSLock);
  this->m_CMDList._Myhead = (std::_List_nod<CCommand *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_CMDList);
  this->m_CMDList._Mysize = 0;
}

//----- (00458E40) --------------------------------------------------------
void __thiscall CCommandProcess::~CCommandProcess(CCommandProcess *this)
{
  CCommandProcess::ClearAll(this);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CMDList);
  operator delete(this->m_CMDList._Myhead);
  this->m_CMDList._Myhead = 0;
  DeleteCriticalSection(&this->m_CMDLock.m_CSLock);
}

//----- (00458EA0) --------------------------------------------------------
void __thiscall std::list<CCommand *>::_Splice(
        std::list<CCommand *> *this,
        std::list<CCommand *>::iterator _Where,
        std::list<CCommand *> *_Right,
        std::list<CCommand *>::iterator _First,
        std::list<CCommand *>::iterator _Last,
        unsigned int _Count)
{
  std::_List_nod<CCommand *>::_Node *Prev; // esi

  if ( this != _Right )
  {
    std::list<CCommand *>::_Incsize(this, _Count);
    _Right->_Mysize -= _Count;
  }
  _First._Ptr->_Prev->_Next = _Last._Ptr;
  _Last._Ptr->_Prev->_Next = _Where._Ptr;
  _Where._Ptr->_Prev->_Next = _First._Ptr;
  Prev = _Where._Ptr->_Prev;
  _Where._Ptr->_Prev = _Last._Ptr->_Prev;
  _Last._Ptr->_Prev = _First._Ptr->_Prev;
  _First._Ptr->_Prev = Prev;
}

//----- (00458EF0) --------------------------------------------------------
void __thiscall CCommandProcess::ProcessAll(CCommandProcess *this)
{
  std::_List_nod<CCommand *>::_Node *Myhead; // esi
  std::_List_nod<CCommand *>::_Node *i; // edi
  std::_List_nod<CCommand *>::_Node *j; // edi
  std::_List_nod<CCommand *>::_Node *Next; // eax
  bool v6; // zf
  std::_List_nod<CCommand *>::_Node *v7; // edi
  std::list<CCommand *> ProcessList; // [esp+Ch] [ebp-18h] BYREF
  int v9; // [esp+20h] [ebp-4h]

  Myhead = (std::_List_nod<CCommand *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&ProcessList);
  ProcessList._Myhead = Myhead;
  ProcessList._Mysize = 0;
  v9 = 0;
  EnterCriticalSection(&this->m_CMDLock.m_CSLock);
  if ( &ProcessList != &this->m_CMDList && this->m_CMDList._Mysize )
  {
    std::list<CCommand *>::_Splice(
      &ProcessList,
      (std::list<CCommand *>::iterator)Myhead,
      &this->m_CMDList,
      (std::list<CCommand *>::iterator)this->m_CMDList._Myhead->_Next,
      (std::list<CCommand *>::iterator)this->m_CMDList._Myhead,
      this->m_CMDList._Mysize);
    Myhead = ProcessList._Myhead;
  }
  LeaveCriticalSection(&this->m_CMDLock.m_CSLock);
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
     __thiscall `vcall'{0,{flat}}(i->_Myval);
  for ( j = Myhead->_Next; j != Myhead; j = j->_Next )
     __thiscall `vcall'{4,{flat}}(j->_Myval);
  Next = Myhead->_Next;
  v6 = Myhead->_Next == Myhead;
  Myhead->_Next = Myhead;
  Myhead->_Prev = Myhead;
  if ( !v6 )
  {
    do
    {
      v7 = Next->_Next;
      operator delete(Next);
      Next = v7;
    }
    while ( v7 != Myhead );
  }
  operator delete(Myhead);
}

//----- (00458FC0) --------------------------------------------------------
void __thiscall CServerRequest::Result::~Result(CServerRequest::Result *this)
{
  if ( this->m_lpSrcDispatch )
  {
    if ( this->m_bRemove )
      CSession::Release(this->m_lpSrcDispatch->m_Session);
  }
}

//----- (00458FE0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (00459000) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (00459020) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (00459080) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (004590E0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // eax

  Ptr = this->_Ptr;
  if ( this->_Ptr->_Isnil )
  {
    this->_Ptr = Ptr->_Right;
  }
  else
  {
    Left = Ptr->_Left;
    if ( Ptr->_Left->_Isnil )
    {
      Parent = Ptr->_Parent;
      if ( !Parent->_Isnil )
      {
        do
        {
          if ( this->_Ptr != Parent->_Left )
            break;
          this->_Ptr = Parent;
          Parent = Parent->_Parent;
        }
        while ( !Parent->_Isnil );
        if ( !Parent->_Isnil )
          this->_Ptr = Parent;
      }
    }
    else
    {
      for ( i = Left->_Right; !i->_Isnil; i = i->_Right )
        Left = i;
      this->_Ptr = Left;
    }
  }
}

//----- (00459140) --------------------------------------------------------
void __thiscall CServerRequest::RequestOff(CServerRequest *this)
{
  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  this->m_dwRequestFlags |= 1u;
  LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
}

//----- (00459160) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (004591C0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00459200) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Rarg,
        const std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)operator new((tagHeader *)0x24);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Parent = _Parg;
    result->_Right = _Rarg;
    result->_Myval = *_Val;
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (00459260) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::find(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *v5; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead == this->_Myhead || *_Keyval < Myhead->_Myval.first )
  {
    v5 = result;
    result->_Ptr = this->_Myhead;
  }
  else
  {
    v5 = result;
    result->_Ptr = Myhead;
  }
  return v5;
}

//----- (004592D0) --------------------------------------------------------
void __thiscall CServerRequest::RemoveAllRequest(CServerRequest *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // esi
  bool v4; // zf
  void (__cdecl *m_lpTimeoutRequest)(CPacketDispatch *); // eax
  CPacketDispatch *m_lpDstDispatch; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator pos; // [esp+10h] [ebp-14h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+14h] [ebp-10h]
  int v12; // [esp+20h] [ebp-4h]

  sync.m_Lock = &this->m_RequestLock;
  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  Myhead = this->m_RequestMap._Myhead;
  Left = Myhead->_Left;
  v4 = Myhead->_Left == Myhead;
  v12 = 0;
  pos._Ptr = Left;
  if ( !v4 )
  {
    do
    {
      if ( Left->_Myval.second.m_lpSrcDispatch )
      {
        m_lpTimeoutRequest = Left->_Myval.second.m_lpTimeoutRequest;
        if ( m_lpTimeoutRequest )
          m_lpTimeoutRequest(Left->_Myval.second.m_lpSrcDispatch);
        CSession::Release(Left->_Myval.second.m_lpSrcDispatch->m_Session);
      }
      m_lpDstDispatch = Left->_Myval.second.m_lpDstDispatch;
      if ( m_lpDstDispatch )
        CSession::Release(m_lpDstDispatch->m_Session);
      std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Inc(&pos);
      Left = pos._Ptr;
    }
    while ( pos._Ptr != Myhead );
  }
  Parent = this->m_RequestMap._Myhead->_Parent;
  for ( i = Parent; !i->_Isnil; Parent = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Erase(
      &this->m_RequestMap,
      i->_Right);
    i = i->_Left;
    operator delete(Parent);
  }
  this->m_RequestMap._Myhead->_Parent = this->m_RequestMap._Myhead;
  v9 = this->m_RequestMap._Myhead;
  this->m_RequestMap._Mysize = 0;
  v9->_Left = v9;
  this->m_RequestMap._Myhead->_Right = this->m_RequestMap._Myhead;
  LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
}

//----- (004593C0) --------------------------------------------------------
CPacketDispatch *__thiscall CServerRequest::GetRequest(CServerRequest *this, unsigned int dwRequestKey)
{
  CPacketDispatch *m_lpSrcDispatch; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator pos; // [esp+8h] [ebp-4h] BYREF

  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::find(
    &this->m_RequestMap,
    &pos,
    &dwRequestKey);
  if ( pos._Ptr == this->m_RequestMap._Myhead )
  {
    LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
    return 0;
  }
  else
  {
    m_lpSrcDispatch = pos._Ptr->_Myval.second.m_lpSrcDispatch;
    LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
    return m_lpSrcDispatch;
  }
}

//----- (00459410) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0xCCCCCCB )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CServerRequest::RequestInfo> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004595C0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> *result,
        std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val)
{
  const std::pair<unsigned long const ,CServerRequest::RequestInfo> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int first; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      v7 = first < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (std::pair<unsigned long const ,CServerRequest::RequestInfo> *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}


//----- (00459680) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
              this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
              this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(
                this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00459940) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00459A00) --------------------------------------------------------
unsigned int __thiscall CServerRequest::AddRequest(
        CServerRequest *this,
        CPacketDispatch *lpSrcDispatch,
        CPacketDispatch *lpDstDispatch,
        unsigned int dwDurationSec,
        void (__cdecl *lpTimeoutRequest)(CPacketDispatch *))
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  char m_dwRequestFlags; // cl
  unsigned int m_dwRequestCounter; // edi
  struct in_addr *m_Session; // ebp
  char *v11; // eax
  CSession *DstSession; // [esp+0h] [ebp-30h]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> result; // [esp+8h] [ebp-28h] BYREF
  std::pair<unsigned long const ,CServerRequest::RequestInfo> _Val; // [esp+10h] [ebp-20h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v16; // [esp+24h] [ebp-Ch]
  void *v17; // [esp+28h] [ebp-8h]
  int v18; // [esp+2Ch] [ebp-4h]
  CSession *lpSrcDispatcha; // [esp+34h] [ebp+4h]

  v18 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v17 = &_ehhandler__AddRequest_CServerRequest__QAEKPAVCPacketDispatch__0KP6AX0_Z_Z;
  v16 = ExceptionList;
  if ( lpSrcDispatch && lpDstDispatch && dwDurationSec )
  {
    EnterCriticalSection(&this->m_RequestLock.m_CSLock);
    m_dwRequestFlags = this->m_dwRequestFlags;
    v18 = 0;
    if ( (m_dwRequestFlags & 1) == 0 )
    {
      lpSrcDispatcha = lpSrcDispatch->m_Session;
      DstSession = lpDstDispatch->m_Session;
      if ( !this->m_dwRequestCounter )
        this->m_dwRequestCounter = 1;
      m_dwRequestCounter = this->m_dwRequestCounter;
      this->m_dwRequestCounter = m_dwRequestCounter + 1;
      _Val.second.m_lpTimeoutRequest = lpTimeoutRequest;
      _Val.second.m_dwDurationSec = dwDurationSec;
      _Val.first = m_dwRequestCounter;
      _Val.second.m_lpSrcDispatch = lpSrcDispatch;
      _Val.second.m_lpDstDispatch = lpDstDispatch;
      if ( std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::insert(
             &this->m_RequestMap,
             &result,
             &_Val)->second )
      {
        CSession::AddRef(lpSrcDispatcha);
        CSession::AddRef(DstSession);
        m_Session = (struct in_addr *)lpSrcDispatch->m_Session;
        v11 = inet_ntoa(m_Session[12]);
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "CServerRequest::AddRequest",
          aDWorkRylSource_46,
          105,
          "SS:0x%p/DP:0x%p/IP:%15s/Request:%d/DstDP:0x%p/Request Added",
          m_Session,
          lpSrcDispatch,
          v11,
          m_dwRequestCounter,
          lpDstDispatch);
        LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
        return m_dwRequestCounter;
      }
    }
    LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
  }
  return 0;
}

//----- (00459B40) --------------------------------------------------------
void __thiscall CServerRequest::RemoveRequest(CServerRequest *this, unsigned int dwRequestKey)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // eax
  CPacketDispatch *m_lpSrcDispatch; // esi
  unsigned int m_dwDurationSec; // ecx
  void (__cdecl *m_lpTimeoutRequest)(CPacketDispatch *); // edx
  char *v8; // eax
  struct in_addr v9; // [esp-4h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator pos; // [esp+10h] [ebp-14h] BYREF
  CServerRequest::RequestInfo info; // [esp+14h] [ebp-10h]

  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::find(
    &this->m_RequestMap,
    &pos,
    &dwRequestKey);
  Ptr = pos._Ptr;
  Myhead = this->m_RequestMap._Myhead;
  m_lpSrcDispatch = 0;
  info.m_lpDstDispatch = 0;
  info.m_lpTimeoutRequest = 0;
  if ( pos._Ptr != Myhead )
  {
    m_lpSrcDispatch = pos._Ptr->_Myval.second.m_lpSrcDispatch;
    m_dwDurationSec = pos._Ptr->_Myval.second.m_dwDurationSec;
    m_lpTimeoutRequest = pos._Ptr->_Myval.second.m_lpTimeoutRequest;
    info.m_lpDstDispatch = pos._Ptr->_Myval.second.m_lpDstDispatch;
    v9 = *(struct in_addr *)&m_lpSrcDispatch->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2];
    info.m_dwDurationSec = m_dwDurationSec;
    info.m_lpTimeoutRequest = m_lpTimeoutRequest;
    v8 = inet_ntoa(v9);
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CServerRequest::RemoveRequest",
      aDWorkRylSource_46,
      131,
      "SS:0x%p/DP:0x%p/IP:%15s/Request:%d/DstDP:0x%p/Request Removed(By RequestKey)",
      m_lpSrcDispatch->m_Session,
      m_lpSrcDispatch,
      v8,
      Ptr->_Myval.first,
      info.m_lpDstDispatch);
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
      &this->m_RequestMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *)&dwRequestKey,
      (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)Ptr);
  }
  LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
  if ( m_lpSrcDispatch )
  {
    if ( info.m_lpTimeoutRequest )
      info.m_lpTimeoutRequest(m_lpSrcDispatch);
    CSession::Release(m_lpSrcDispatch->m_Session);
  }
  if ( info.m_lpDstDispatch )
    CSession::Release(info.m_lpDstDispatch->m_Session);
}

//----- (00459C20) --------------------------------------------------------
void __thiscall CServerRequest::RemoveTimeoutRequest(CServerRequest *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Left; // esi
  bool v4; // zf
  char *v5; // eax
  void (__cdecl *m_lpTimeoutRequest)(CPacketDispatch *); // ecx
  CPacketDispatch *m_lpDstDispatch; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator pos; // [esp+Ch] [ebp-18h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+10h] [ebp-14h]
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator result; // [esp+14h] [ebp-10h] BYREF
  int v11; // [esp+20h] [ebp-4h]

  sync.m_Lock = &this->m_RequestLock;
  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  Myhead = this->m_RequestMap._Myhead;
  Left = Myhead->_Left;
  v4 = Myhead->_Left == Myhead;
  v11 = 0;
  pos._Ptr = Left;
  if ( !v4 )
  {
    do
    {
      v4 = Left->_Myval.second.m_dwDurationSec-- == 1;
      if ( v4 )
      {
        v5 = inet_ntoa(*(struct in_addr *)&Left->_Myval.second.m_lpSrcDispatch->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2]);
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "CServerRequest::RemoveTimeoutRequest",
          aDWorkRylSource_46,
          187,
          "SS:0x%p/DP:0x%p/IP:%15s/Request:%d/DstDP:0x%p/Request Removed(By Timeout)",
          Left->_Myval.second.m_lpSrcDispatch->m_Session,
          Left->_Myval.second.m_lpSrcDispatch,
          v5,
          Left->_Myval.first,
          Left->_Myval.second.m_lpDstDispatch);
        if ( Left->_Myval.second.m_lpSrcDispatch )
        {
          m_lpTimeoutRequest = Left->_Myval.second.m_lpTimeoutRequest;
          if ( m_lpTimeoutRequest )
            m_lpTimeoutRequest(Left->_Myval.second.m_lpSrcDispatch);
          CSession::Release(Left->_Myval.second.m_lpSrcDispatch->m_Session);
        }
        m_lpDstDispatch = Left->_Myval.second.m_lpDstDispatch;
        if ( m_lpDstDispatch )
          CSession::Release(m_lpDstDispatch->m_Session);
        Left = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
                 &this->m_RequestMap,
                 &result,
                 (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)Left)->_Ptr;
        pos._Ptr = Left;
      }
      else
      {
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Inc(&pos);
        Left = pos._Ptr;
      }
    }
    while ( Left != Myhead );
  }
  LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
}

//----- (00459D30) --------------------------------------------------------
CPacketDispatch *__thiscall CServerRequest::PopRequest(CServerRequest *this, unsigned int dwRequestKey)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Ptr; // edi
  CPacketDispatch *m_lpSrcDispatch; // esi
  CPacketDispatch *m_lpDstDispatch; // ebp
  void (__cdecl *m_lpTimeoutRequest)(CPacketDispatch *); // edx
  char *v7; // eax
  struct in_addr v9; // [esp-Ch] [ebp-38h]
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator pos; // [esp+8h] [ebp-24h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+Ch] [ebp-20h]
  CServerRequest::RequestInfo info; // [esp+10h] [ebp-1Ch]
  int v13; // [esp+28h] [ebp-4h]

  sync.m_Lock = &this->m_RequestLock;
  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  v13 = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::find(
    &this->m_RequestMap,
    &pos,
    &dwRequestKey);
  Ptr = pos._Ptr;
  if ( pos._Ptr == this->m_RequestMap._Myhead )
  {
    LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
    return 0;
  }
  else
  {
    m_lpSrcDispatch = pos._Ptr->_Myval.second.m_lpSrcDispatch;
    m_lpDstDispatch = pos._Ptr->_Myval.second.m_lpDstDispatch;
    m_lpTimeoutRequest = pos._Ptr->_Myval.second.m_lpTimeoutRequest;
    v9 = *(struct in_addr *)&m_lpSrcDispatch->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2];
    info.m_dwDurationSec = pos._Ptr->_Myval.second.m_dwDurationSec;
    info.m_lpTimeoutRequest = m_lpTimeoutRequest;
    v7 = inet_ntoa(v9);
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CServerRequest::PopRequest",
      aDWorkRylSource_46,
      264,
      "SS:0x%p/DP:0x%p/IP:%15s/Request:%d/DstDP:0x%p/Request Removed(By Pop Request)",
      m_lpSrcDispatch->m_Session,
      m_lpSrcDispatch,
      v7,
      Ptr->_Myval.first,
      m_lpDstDispatch);
    std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
      &this->m_RequestMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *)&dwRequestKey,
      (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)Ptr);
    LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
    return m_lpSrcDispatch;
  }
}

//----- (00459E20) --------------------------------------------------------
void __thiscall std::map<unsigned long,CServerRequest::RequestInfo>::~map<unsigned long,CServerRequest::RequestInfo>(
        std::map<unsigned long,CServerRequest::RequestInfo> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00459E50) --------------------------------------------------------
void __thiscall CServerRequest::~CServerRequest(CServerRequest *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator v2; // [esp-8h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *Myhead; // [esp-4h] [ebp-20h]
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  v5 = 1;
  EnterCriticalSection(&this->m_RequestLock.m_CSLock);
  this->m_dwRequestFlags |= 1u;
  LeaveCriticalSection(&this->m_RequestLock.m_CSLock);
  CServerRequest::RemoveAllRequest(this);
  Myhead = this->m_RequestMap._Myhead;
  v2._Ptr = Myhead->_Left;
  LOBYTE(v5) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(
    &this->m_RequestMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator)Myhead);
  operator delete(this->m_RequestMap._Myhead);
  this->m_RequestMap._Myhead = 0;
  this->m_RequestMap._Mysize = 0;
  DeleteCriticalSection(&this->m_RequestLock.m_CSLock);
}

//----- (00459EF0) --------------------------------------------------------
void __thiscall CServerRequest::CServerRequest(CServerRequest *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v2; // eax

  InitializeCriticalSection(&this->m_RequestLock.m_CSLock);
  v2 = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(&this->m_RequestMap);
  this->m_RequestMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_RequestMap._Myhead->_Parent = this->m_RequestMap._Myhead;
  this->m_RequestMap._Myhead->_Left = this->m_RequestMap._Myhead;
  this->m_RequestMap._Myhead->_Right = this->m_RequestMap._Myhead;
  this->m_RequestMap._Mysize = 0;
}

//----- (00459F60) --------------------------------------------------------
CServerRequest *__cdecl CServerRequest::GetInstance()
{
  if ( (_S1_6 & 1) == 0 )
  {
    _S1_6 |= 1u;
    CServerRequest::CServerRequest(&serverRequest);
    atexit(_E2_8);
  }
  return &serverRequest;
}

//----- (00459FC0) --------------------------------------------------------
void __thiscall CServerRequest::Result::Result(CServerRequest::Result *this, unsigned int dwRequestKey, bool bRemove)
{
  CServerRequest *Instance; // eax

  this->m_bRemove = bRemove;
  Instance = CServerRequest::GetInstance();
  if ( bRemove )
    this->m_lpSrcDispatch = CServerRequest::PopRequest(Instance, dwRequestKey);
  else
    this->m_lpSrcDispatch = CServerRequest::GetRequest(Instance, dwRequestKey);
}

//----- (0045A000) --------------------------------------------------------
unsigned int __cdecl BroadcastInfo::GetObjectSize(
        BroadcastInfo::DataType::Type dataType,
        BroadcastInfo::ObjectPhase::Type objectPhase)
{
  unsigned int result; // eax

  switch ( dataType )
  {
    case OBJECT:
      result = 16;
      break;
    case AGGRESIVE_CREATURE:
    case MONSTER:
      result = 22;
      break;
    case CHARACTER:
      switch ( objectPhase )
      {
        case CHAR_BASEINFO:
          result = 56;
          break;
        case CHAR_EQUIPMENTINFO:
          result = 54;
          break;
        case CHAR_COMMUNITYINFO:
          result = 38;
          break;
        default:
          goto LABEL_11;
      }
      break;
    case SUMMON_MONSTER:
      result = 26;
      break;
    default:
LABEL_11:
      result = 0;
      break;
  }
  return result;
}

//----- (0045A060) --------------------------------------------------------
char __cdecl BroadcastInfo::SerializeOutAggresiveCreatureInfo(
        CAggresiveCreature *AggresiveCreature,
        BroadcastInfo::DataType::Type eDataType,
        BroadcastInfo::ObjectPhase::Type ePhase,
        char *szBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  unsigned int ObjectSize; // eax
  char *v6; // edi
  unsigned int m_dwFrame; // eax
  float fVel; // [esp+8h] [ebp-8h]
  unsigned int dwObjectSize; // [esp+Ch] [ebp-4h]

  ObjectSize = BroadcastInfo::GetObjectSize(eDataType, ePhase);
  dwObjectSize = ObjectSize;
  if ( ObjectSize && ObjectSize <= *dwBufferSize_InOut )
  {
    if ( szBuffer_Out )
    {
      *(_DWORD *)szBuffer_Out = AggresiveCreature->m_dwCID;
      *((_DWORD *)szBuffer_Out + 1) = 0;
      *((_WORD *)szBuffer_Out + 4) = 0;
      *((_WORD *)szBuffer_Out + 5) = 0;
      *((_WORD *)szBuffer_Out + 6) = 0;
      szBuffer_Out[14] = 0;
      szBuffer_Out[15] = 0;
      v6 = szBuffer_Out;
    }
    else
    {
      v6 = 0;
    }
    m_dwFrame = AggresiveCreature->m_MotionInfo.m_dwFrame;
    if ( m_dwFrame )
    {
      fVel = AggresiveCreature->m_MotionInfo.m_fVelocity / (double)m_dwFrame;
      CNetworkPos::Initialize(
        (CNetworkPos *)v6 + 1,
        AggresiveCreature->m_CurrentPos.m_fPointX,
        AggresiveCreature->m_CurrentPos.m_fPointY,
        AggresiveCreature->m_CurrentPos.m_fPointZ,
        AggresiveCreature->m_MotionInfo.m_fDirection,
        fVel);
    }
    else
    {
      CNetworkPos::Initialize(
        (CNetworkPos *)v6 + 1,
        AggresiveCreature->m_CurrentPos.m_fPointX,
        AggresiveCreature->m_CurrentPos.m_fPointY,
        AggresiveCreature->m_CurrentPos.m_fPointZ,
        AggresiveCreature->m_MotionInfo.m_fDirection,
        0.0);
    }
    *((_DWORD *)v6 + 1) = (eDataType << 6) | (8 * ePhase) & 0xFFFFFE3F | *((_DWORD *)v6 + 1) & 0xFFFFFE01 | 1;
    if ( eDataType > OBJECT && eDataType <= SUMMON_MONSTER )
    {
      *((_DWORD *)v6 + 4) = AggresiveCreature->m_dwStatusFlag;
      *((_WORD *)v6 + 10) = AggresiveCreature->m_CreatureStatus.m_nNowHP;
    }
    *dwBufferSize_InOut = dwObjectSize;
    return 1;
  }
  else
  {
    *dwBufferSize_InOut = 0;
    return 0;
  }
}

//----- (0045A170) --------------------------------------------------------
char __cdecl BroadcastInfo::SerializeOutCharacterInfo(
        CCharacter *Character,
        BroadcastInfo::DataType::Type eDataType,
        BroadcastInfo::ObjectPhase::Type ePhase,
        char *szBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  unsigned int v5; // ebx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int v8; // eax
  int v9; // edx
  int v11; // ebp
  int v12; // eax
  unsigned __int16 v13; // cx
  int v14; // ecx
  int v15; // ebx
  int v16; // eax

  if ( !BroadcastInfo::SerializeOutAggresiveCreatureInfo(Character, eDataType, ePhase, szBuffer_Out, dwBufferSize_InOut) )
    return 0;
  if ( eDataType != CHARACTER )
  {
LABEL_12:
    *((_DWORD *)szBuffer_Out + 1) = *((_DWORD *)szBuffer_Out + 1) & 0xFFFFFFF8 | 3;
    return 1;
  }
  if ( ePhase == CHAR_BASEINFO )
  {
    strncpy(szBuffer_Out + 22, Character->m_DBData.m_Info.Name, 0x10u);
    *(_DWORD *)(szBuffer_Out + 38) = Character->m_PublicAddress.sin_addr.S_un.S_addr;
    *((_WORD *)szBuffer_Out + 23) = Character->m_PublicAddress.sin_port;
    *(_DWORD *)(szBuffer_Out + 42) = Character->m_PrivateAddress.sin_addr.S_un.S_addr;
    *((_WORD *)szBuffer_Out + 24) = Character->m_PrivateAddress.sin_port;
    v11 = ((unsigned __int8)*(_DWORD *)(szBuffer_Out + 50) ^ Character->m_DBData.m_Info.Hair) & 0xF ^ *(_DWORD *)(szBuffer_Out + 50);
    *(_DWORD *)(szBuffer_Out + 50) = v11;
    v12 = v11 ^ ((unsigned __int8)v11 ^ (unsigned __int8)(16 * Character->m_DBData.m_Info.Face)) & 0xF0;
    *(_DWORD *)(szBuffer_Out + 50) = v12;
    LOBYTE(v13) = 0;
    HIBYTE(v13) = Character->m_DBData.m_Info.Nationality;
    v14 = v12 ^ ((unsigned __int16)v12 ^ v13) & 0x700;
    *(_DWORD *)(szBuffer_Out + 50) = v14;
    *(_DWORD *)(szBuffer_Out + 50) = v14 ^ ((unsigned __int16)v14 ^ (unsigned __int16)(((unsigned __int8)Character->m_DBData.m_Info.Sex
                                                                                      - 1) << 14)) & 0x4000;
    v15 = ((unsigned __int16)*(_DWORD *)(szBuffer_Out + 50) ^ (unsigned __int16)(Character->IsPeaceMode(Character) << 15)) & 0x8000 ^ *(_DWORD *)(szBuffer_Out + 50);
    *(_DWORD *)(szBuffer_Out + 50) = v15;
    v16 = v15 ^ (v15 ^ ((unsigned __int8)Character->m_cHandPos << 16)) & 0x10000;
    *(_DWORD *)(szBuffer_Out + 50) = v16;
    *(_DWORD *)(szBuffer_Out + 50) = v16 ^ ((unsigned __int16)v16 ^ (unsigned __int16)(Character->m_DBData.m_cAdminLevel << 11)) & 0x3800;
    szBuffer_Out[54] = Character->GetClass(Character);
    szBuffer_Out[55] = Character->m_CreatureStatus.m_nLevel;
    goto LABEL_12;
  }
  if ( ePhase == CHAR_EQUIPMENTINFO )
  {
    Item::CEquipmentsContainer::GetEquipmentView(&Character->m_Equipments, (unsigned __int16 *)szBuffer_Out + 11, 0, 16);
    *((_DWORD *)szBuffer_Out + 1) = *((_DWORD *)szBuffer_Out + 1) & 0xFFFFFFF8 | 3;
    return 1;
  }
  if ( ePhase != CHAR_COMMUNITYINFO )
    goto LABEL_12;
  v5 = Character->GetGID(Character);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v5);
  if ( Guild )
  {
    if ( Guild::CGuild::GetTitle(Guild, Character->m_dwCID) == 5 )
      v5 |= 0x80000000;
  }
  *(_DWORD *)(szBuffer_Out + 22) = v5;
  *(_DWORD *)(szBuffer_Out + 26) = Character->m_DBData.m_Info.Party;
  v8 = Character->GetFame(Character);
  v9 = *((_DWORD *)szBuffer_Out + 1);
  *(_DWORD *)(szBuffer_Out + 30) = v8;
  *(_DWORD *)(szBuffer_Out + 34) = Character->m_DBData.m_Info.Mileage;
  *((_DWORD *)szBuffer_Out + 1) = v9 & 0xFFFFFFF8 | 3;
  return 1;
}

//----- (0045A360) --------------------------------------------------------
char __cdecl BroadcastInfo::SerializeOutMonsterInfo(
        CMonster *Monster,
        BroadcastInfo::DataType::Type eDataType,
        BroadcastInfo::ObjectPhase::Type ePhase,
        char *szBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  if ( !BroadcastInfo::SerializeOutAggresiveCreatureInfo(Monster, eDataType, ePhase, szBuffer_Out, dwBufferSize_InOut) )
    return 0;
  *((_DWORD *)szBuffer_Out + 1) = *((_DWORD *)szBuffer_Out + 1) & 0xFFFFFFF8 | 2;
  return 1;
}

//----- (0045A3A0) --------------------------------------------------------
char __cdecl BroadcastInfo::SerializeOutSummonMonsterInfo(
        CSummonMonster *SummonMonster,
        BroadcastInfo::DataType::Type eDataType,
        BroadcastInfo::ObjectPhase::Type ePhase,
        char *szBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  CCharacter *m_lpMaster; // eax

  if ( BroadcastInfo::SerializeOutAggresiveCreatureInfo(
         SummonMonster,
         eDataType,
         ePhase,
         szBuffer_Out,
         dwBufferSize_InOut) )
  {
    *((_DWORD *)szBuffer_Out + 1) = *((_DWORD *)szBuffer_Out + 1) & 0xFFFFFFF8 | 2;
    if ( eDataType == SUMMON_MONSTER )
    {
      m_lpMaster = SummonMonster->m_lpMaster;
      if ( !m_lpMaster )
        return 1;
      *(_DWORD *)(szBuffer_Out + 22) = m_lpMaster->m_dwCID;
    }
    *((_DWORD *)szBuffer_Out + 1) = *((_DWORD *)szBuffer_Out + 1) & 0xFFFFFFF8 | 4;
    return 1;
  }
  return 0;
}

//----- (0045A410) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::ProcessAllMonster(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // esi
  CMonster *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Myhead = this->m_MonsterMap._Myhead;
  Left = Myhead->_Left;
  while ( Left != Myhead )
  {
    second = Left->_Myval.second;
    if ( second )
      CMonster::Process(second);
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
  }
}

//----- (0045A480) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::ProcessMonsterRegenHPAndMP(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // esi
  CMonster *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Myhead = this->m_MonsterMap._Myhead;
  Left = Myhead->_Left;
  while ( Left != Myhead )
  {
    second = Left->_Myval.second;
    if ( second )
      second->RegenHPAndMP(second, 0, 0, 1);
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
  }
}

//----- (0045A500) --------------------------------------------------------
CMsgProc *__thiscall CVirtualMonsterMgr::GetAggresiveCreature(CVirtualMonsterMgr *this, signed int dwCID)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  if ( dwCID >= 0 )
    return 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
    (const unsigned int *)&dwCID);
  if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_MonsterMap._Myhead )
    return 0;
  else
    return result._Ptr->_Myval.second;
}

//----- (0045A540) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::ProcessSummonMonsterDead(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // esi
  CNPC *second; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  Myhead = this->m_MonsterMap._Myhead;
  Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Myhead->_Left;
  while ( Left != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Myhead )
  {
    second = Left->_Myval.second;
    if ( second && CMonster::IsDeadSummonMonster((CMonster *)Left->_Myval.second) )
    {
      Left = std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
               (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
               &result,
               (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Left)->_Ptr;
      ((void (__thiscall *)(CNPC *, int))second->~CNPC)(second, 1);
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
  }
}

//----- (0045A5D0) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::DestroyMonsterList(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax

  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnLeaveParty>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead,
    0);
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead,
    0);
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_MonsterMap,
    this->m_MonsterMap._Myhead->_Parent);
  this->m_MonsterMap._Myhead->_Parent = this->m_MonsterMap._Myhead;
  Myhead = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_MonsterMap._Myhead->_Right = this->m_MonsterMap._Myhead;
}

//----- (0045A630) --------------------------------------------------------
bool __thiscall CVirtualMonsterMgr::AddMonster(CVirtualMonsterMgr *this, CMonster *lpMonster)
{
  bool v2; // al
  std::pair<unsigned long const ,CNPC *> _Val; // [esp+4h] [ebp-10h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> result; // [esp+Ch] [ebp-8h] BYREF

  v2 = 0;
  if ( lpMonster )
  {
    if ( (lpMonster->m_dwCID & 0x80000000) != 0 )
    {
      _Val.first = lpMonster->m_dwCID;
      _Val.second = (CNPC *)lpMonster;
      return std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
               (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
               &result,
               &_Val)->second;
    }
  }
  return v2;
}

//----- (0045A670) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __thiscall CVirtualMonsterMgr::SummonMonster(CVirtualMonsterMgr *this, unsigned int nKID, __int128 Pos)
{
  int v4; // edi
  CCharacter *v6; // ebp
  int v7; // ecx
  CSummonMonster *v8; // eax
  CMonster *v9; // eax
  CMonster *v10; // edi
  unsigned int m_dwCID; // [esp-Ch] [ebp-48h]
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+Ch] [ebp-30h] BYREF
  CMonster::MonsterCreateInfo tempInfo; // [esp+10h] [ebp-2Ch] BYREF
  int v14; // [esp+38h] [ebp-4h]

  v4 = nKID;
  tempInfo.m_dwCID = nKID + ((this->m_usSummonCount + 40960) << 16);
  nKID = tempInfo.m_dwCID;
  memset(&tempInfo, 0, 12);
  memset(&tempInfo.m_dwPID, 0, 13);
  tempInfo.m_wRespawnArea = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
    &nKID);
  if ( result._Ptr != (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_MonsterMap._Myhead
    && result._Ptr->_Myval.second )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CVirtualMonsterMgr::SummonMonster",
      aDWorkRylSource_56,
      159,
      (char *)&byte_4DCE54);
    return 0;
  }
  v6 = (CCharacter *)HIDWORD(Pos);
  tempInfo.m_nKID = v4;
  tempInfo.m_Pos = (Position)Pos;
  if ( HIDWORD(Pos) )
  {
    v7 = *(_DWORD *)(HIDWORD(Pos) + 1508);
    if ( v7 )
      (*(void (__thiscall **)(int, _DWORD))(*(_DWORD *)v7 + 64))(v7, 0);
  }
  v8 = (CSummonMonster *)operator new((tagHeader *)0x308);
  nKID = (unsigned int)v8;
  v14 = 0;
  if ( v8 )
  {
    CSummonMonster::CSummonMonster(v8, &tempInfo, v6);
    v10 = v9;
  }
  else
  {
    v10 = 0;
  }
  v14 = -1;
  if ( !v10 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CVirtualMonsterMgr::SummonMonster",
      aDWorkRylSource_56,
      179,
      (char *)&byte_4DCE54);
    return 0;
  }
  v10->m_CellPos.m_wMapIndex = v6->m_CellPos.m_wMapIndex;
  if ( !CMonster::InitMonster(v10, &tempInfo.m_Pos, LOGINOUT) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CVirtualMonsterMgr::SummonMonster", aDWorkRylSource_56, 186, byte_4DCE10);
    return 0;
  }
  CVirtualMonsterMgr::AddMonster(this, v10);
  if ( ++this->m_usSummonCount == 0x3FFF )
    this->m_usSummonCount = 0;
  m_dwCID = v6->m_dwCID;
  v6->m_lpSummonee = v10;
  GameClientSendPacket::SendCharSummon(m_dwCID, v10);
  return 1;
}
// 45A670: variables would overlap: ^60.16 and stkvar "Pos" ^60.12(has user info),stkvar "lpMaster" ^6C.4(has user info)

//----- (0045A850) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::~CVirtualMonsterMgr(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v4; // [esp-8h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // [esp-4h] [ebp-20h]
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v7; // [esp+18h] [ebp-4h]

  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)this;
  v7 = 0;
  CVirtualMonsterMgr::DestroyMonsterList(this);
  Myhead = this->m_MonsterMap._Myhead;
  v4._Ptr = Myhead->_Left;
  v7 = -1;
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_MonsterMap,
    &result,
    v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead);
  v2 = this->m_MonsterMap._Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  v2->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = v2;
  LeaveCriticalSection(&v3->mtx);
  this->m_MonsterMap._Myhead = 0;
  this->m_MonsterMap._Mysize = 0;
}

//----- (0045A8E0) --------------------------------------------------------
void __thiscall CVirtualMonsterMgr::CVirtualMonsterMgr(CVirtualMonsterMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // eax

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this);
  this->m_MonsterMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_MonsterMap._Myhead->_Parent = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Myhead->_Left = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Myhead->_Right = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Mysize = 0;
  this->m_usSummonCount = 0;
}

//----- (0045A910) --------------------------------------------------------
double __thiscall CThreat::GetAggravation(CThreat *this, CAggresiveCreature *pCreature)
{
  return `CThreat::GetAggravation'::`2'::aryAggravation[CAggresiveCreature::CalculateLevelGap(
                                                          this->m_pOwner,
                                                          (int)pCreature)
                                                      + 20];
}

//----- (0045A930) --------------------------------------------------------
char __thiscall CThreat::SaveEnemy(CThreat *this, unsigned int dwCID)
{
  int v3; // edx
  char *v4; // eax
  CThreat::SaveEnemyInfo *m_LatestEnemy; // ecx
  unsigned int m_dwTickCount; // esi
  int v7; // ecx
  CThreat::SaveEnemyInfo *i; // edx
  unsigned int dwLastTickCount; // [esp+10h] [ebp-Ch]
  int nOldestIndex; // [esp+14h] [ebp-8h]

  nOldestIndex = 0;
  dwLastTickCount = GetTickCount();
  v3 = 0;
  v4 = 0;
  m_LatestEnemy = this->m_LatestEnemy;
  do
  {
    if ( m_LatestEnemy->m_dwCID )
    {
      m_dwTickCount = m_LatestEnemy->m_dwTickCount;
      if ( dwLastTickCount - m_dwTickCount < 0x493E0 )
      {
        if ( dwCID == m_LatestEnemy->m_dwCID )
        {
          this->m_LatestEnemy[v3].m_dwTickCount = dwLastTickCount;
          return 1;
        }
        if ( m_dwTickCount < *(unsigned int *)((char *)&this->m_LatestEnemy[0].m_dwTickCount + (_DWORD)v4) )
        {
          nOldestIndex = v3;
          v4 = (char *)m_LatestEnemy - 32 - (_DWORD)this;
        }
      }
      else
      {
        m_LatestEnemy->m_dwCID = 0;
        m_LatestEnemy->m_dwTickCount = 0;
      }
    }
    ++v3;
    ++m_LatestEnemy;
  }
  while ( v3 < 5 );
  v7 = 0;
  for ( i = this->m_LatestEnemy; i->m_dwCID; ++i )
  {
    if ( ++v7 >= 5 )
    {
      this->m_LatestEnemy[nOldestIndex].m_dwCID = dwCID;
      this->m_LatestEnemy[nOldestIndex].m_dwTickCount = dwLastTickCount;
      return 0;
    }
  }
  this->m_LatestEnemy[v7].m_dwTickCount = dwLastTickCount;
  this->m_LatestEnemy[v7].m_dwCID = dwCID;
  return 0;
}

//----- (0045AA10) --------------------------------------------------------
void __thiscall std::list<IOPCode *>::clear(std::list<CThread *> *this)
{
  std::_List_nod<CThread *>::_Node *Myhead; // ecx
  std::_List_nod<CThread *>::_Node *Next; // eax
  bool v4; // zf
  std::_List_nod<CThread *>::_Node *v5; // edi

  Myhead = this->_Myhead;
  Next = Myhead->_Next;
  Myhead->_Next = Myhead;
  this->_Myhead->_Prev = this->_Myhead;
  v4 = Next == this->_Myhead;
  this->_Mysize = 0;
  if ( !v4 )
  {
    do
    {
      v5 = Next->_Next;
      operator delete(Next);
      Next = v5;
    }
    while ( v5 != this->_Myhead );
  }
}

//----- (0045AA50) --------------------------------------------------------
std::_List_nod<CThreat::ThreatInfo>::_Node *__thiscall std::list<CThreat::ThreatInfo>::_Buynode(
        std::list<CThreat::ThreatInfo> *this)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *result; // eax

  result = (std::_List_nod<CThreat::ThreatInfo>::_Node *)operator new((tagHeader *)0x10);
  if ( result )
    result->_Next = result;
  if ( result != (std::_List_nod<CThreat::ThreatInfo>::_Node *)-4 )
    result->_Prev = result;
  return result;
}

//----- (0045AA70) --------------------------------------------------------
std::_List_nod<CThreat::ThreatInfo>::_Node *__thiscall std::list<CThreat::ThreatInfo>::_Buynode(
        std::list<CThreat::ThreatInfo> *this,
        std::_List_nod<CThreat::ThreatInfo>::_Node *_Next,
        std::_List_nod<CThreat::ThreatInfo>::_Node *_Prev,
        const CThreat::ThreatInfo *_Val)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *result; // eax

  result = (std::_List_nod<CThreat::ThreatInfo>::_Node *)operator new((tagHeader *)0x10);
  if ( result )
  {
    result->_Next = _Next;
    result->_Prev = _Prev;
    result->_Myval = *_Val;
  }
  return result;
}

//----- (0045AAA0) --------------------------------------------------------
char __thiscall CThreat::DeleteThreatened(CThreat *this, CAggresiveCreature *pDefendCreature)
{
  std::_List_nod<CAggresiveCreature *>::_Node *Myhead; // ecx
  std::_List_nod<CAggresiveCreature *>::_Node *i; // eax

  Myhead = this->m_ThreatenedList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    if ( i->_Myval == pDefendCreature )
      break;
  }
  if ( i == this->m_ThreatenedList._Myhead )
    return 0;
  i->_Prev->_Next = i->_Next;
  i->_Next->_Prev = i->_Prev;
  operator delete(i);
  --this->m_ThreatenedList._Mysize;
  return 1;
}

//----- (0045AAF0) --------------------------------------------------------
char __thiscall CThreat::DeleteThreat(CThreat *this, CAggresiveCreature *pAttackCreature)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // eax

  Myhead = this->m_ThreatList._Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next == Myhead )
    return 0;
  while ( pAttackCreature != Next->_Myval.m_pCreature )
  {
    Next = Next->_Next;
    if ( Next == this->m_ThreatList._Myhead )
      return 0;
  }
  if ( Next != Myhead )
  {
    Next->_Prev->_Next = Next->_Next;
    Next->_Next->_Prev = Next->_Prev;
    operator delete(Next);
    --this->m_ThreatList._Mysize;
  }
  return 1;
}

//----- (0045AB40) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::pop_front(std::list<CThreat::ThreatInfo> *this)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // eax

  Myhead = this->_Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next != Myhead )
  {
    Next->_Prev->_Next = Next->_Next;
    Next->_Next->_Prev = Next->_Prev;
    operator delete(Next);
    --this->_Mysize;
  }
}

//----- (0045AB70) --------------------------------------------------------
void __thiscall CThreat::~CThreat(CThreat *this)
{
  std::list<CAggresiveCreature *> *p_m_ThreatenedList; // edi

  p_m_ThreatenedList = &this->m_ThreatenedList;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_ThreatenedList);
  operator delete(p_m_ThreatenedList->_Myhead);
  p_m_ThreatenedList->_Myhead = 0;
  std::list<IOPCode *>::clear((std::list<CThread *> *)this);
  operator delete(this->m_ThreatList._Myhead);
  this->m_ThreatList._Myhead = 0;
}

//----- (0045ABB0) --------------------------------------------------------
void __thiscall CThreat::ClearThreatList(CThreat *this)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *i; // ebx
  CAggresiveCreature *m_pCreature; // esi
  void **p_Next; // ecx
  void **v6; // eax
  CThreat *p_m_Threat; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // eax
  bool v10; // zf
  std::_List_nod<CThreat::ThreatInfo>::_Node *v11; // esi

  Myhead = this->m_ThreatList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    m_pCreature = i->_Myval.m_pCreature;
    p_Next = (void **)&m_pCreature->m_Threat.m_ThreatenedList._Myhead->_Next;
    v6 = (void **)*p_Next;
    p_m_Threat = &m_pCreature->m_Threat;
    if ( *p_Next != p_Next )
    {
      do
      {
        if ( v6[2] == this->m_pOwner )
          break;
        v6 = (void **)*v6;
      }
      while ( v6 != p_Next );
    }
    if ( v6 != (void **)p_m_Threat->m_ThreatenedList._Myhead )
    {
      *(_DWORD *)v6[1] = *v6;
      *((_DWORD *)*v6 + 1) = v6[1];
      operator delete(v6);
      --p_m_Threat->m_ThreatenedList._Mysize;
    }
  }
  v8 = this->m_ThreatList._Myhead;
  Next = v8->_Next;
  v8->_Next = v8;
  this->m_ThreatList._Myhead->_Prev = this->m_ThreatList._Myhead;
  v10 = Next == this->m_ThreatList._Myhead;
  this->m_ThreatList._Mysize = 0;
  if ( !v10 )
  {
    do
    {
      v11 = Next->_Next;
      operator delete(Next);
      Next = v11;
    }
    while ( v11 != this->m_ThreatList._Myhead );
  }
}

//----- (0045AC40) --------------------------------------------------------
void __thiscall CThreat::ClearThreatenedList(CThreat *this)
{
  std::_List_nod<CAggresiveCreature *>::_Node *Myhead; // ebp
  std::_List_nod<CAggresiveCreature *>::_Node *i; // ebx
  CAggresiveCreature *Myval; // esi
  void **p_Next; // ecx
  void **v6; // eax
  CThreat *p_m_Threat; // esi
  std::_List_nod<CAggresiveCreature *>::_Node *v8; // ecx
  std::_List_nod<CAggresiveCreature *>::_Node *Next; // eax
  bool v10; // zf
  std::_List_nod<CAggresiveCreature *>::_Node *v11; // esi

  Myhead = this->m_ThreatenedList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    Myval = i->_Myval;
    p_Next = (void **)&Myval->m_Threat.m_ThreatList._Myhead->_Next;
    v6 = (void **)*p_Next;
    p_m_Threat = &Myval->m_Threat;
    if ( *p_Next != p_Next )
    {
      while ( this->m_pOwner != v6[2] )
      {
        v6 = (void **)*v6;
        if ( v6 == (void **)p_m_Threat->m_ThreatList._Myhead )
          goto LABEL_8;
      }
      if ( v6 != p_Next )
      {
        *(_DWORD *)v6[1] = *v6;
        *((_DWORD *)*v6 + 1) = v6[1];
        operator delete(v6);
        --p_m_Threat->m_ThreatList._Mysize;
      }
    }
LABEL_8:
    ;
  }
  v8 = this->m_ThreatenedList._Myhead;
  Next = v8->_Next;
  v8->_Next = v8;
  this->m_ThreatenedList._Myhead->_Prev = this->m_ThreatenedList._Myhead;
  v10 = Next == this->m_ThreatenedList._Myhead;
  this->m_ThreatenedList._Mysize = 0;
  if ( !v10 )
  {
    do
    {
      v11 = Next->_Next;
      operator delete(Next);
      Next = v11;
    }
    while ( v11 != this->m_ThreatenedList._Myhead );
  }
}

//----- (0045ACD0) --------------------------------------------------------
void __thiscall CThreat::DivisionExp(CThreat *this)
{
  unsigned int Mysize; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // eax
  int v5; // ebx
  int m_lThreatAmount; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v7; // eax
  unsigned __int8 m_nLevel; // dl
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // edi
  int v10; // edx
  CThreat::AwardInfo *v11; // ebp
  CAggresiveCreature *m_pCreature; // ecx
  CAggresiveCreature_vtbl *v13; // eax
  int v14; // eax
  CAggresiveCreature *m_pOwner; // ecx
  double v16; // st7
  unsigned __int64 v17; // rax
  CThreat::AwardInfo *v18; // ebp
  CAggresiveCreature *v19; // edi
  unsigned int m_dwCID; // ebx
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  bool IsSummonee; // al
  CVirtualMonsterMgr *v25; // eax
  CAggresiveCreature *v26; // ecx
  int v27; // eax
  CThreat::AwardInfo *v28; // edx
  CAggresiveCreature_vtbl *v29; // eax
  int v30; // ebx
  int v31; // edi
  CThreat::AwardInfo *v32; // ebp
  int v33; // eax
  CParty **p_m_pParty; // edx
  CParty *m_pParty; // ecx
  CThreat::AwardInfo *v36; // edx
  int m_lAward; // eax
  float v38; // ebp
  int v39; // eax
  _DWORD *v40; // ecx
  _DWORD *v41; // eax
  int v42; // edx
  int v43; // edi
  int v44; // edx
  CCharacter *v45; // edi
  CCharacterParty *v46; // ecx
  unsigned int v47; // eax
  CThreat *v48; // ebx
  int v49; // eax
  unsigned __int64 v50; // rax
  unsigned __int16 m_wMapIndex; // [esp-10h] [ebp-130h]
  unsigned int v52; // [esp-10h] [ebp-130h]
  unsigned __int8 cHighestLevel; // [esp+7h] [ebp-119h]
  int nNumber; // [esp+8h] [ebp-118h]
  int nNumbera; // [esp+8h] [ebp-118h]
  int m_nMaxHP; // [esp+Ch] [ebp-114h]
  float *p_lIndividualExp; // [esp+Ch] [ebp-114h]
  int v58; // [esp+Ch] [ebp-114h]
  int v59; // [esp+Ch] [ebp-114h]
  unsigned int lSumOfThreatAmount; // [esp+10h] [ebp-110h]
  unsigned int lSumOfThreatAmounta; // [esp+10h] [ebp-110h]
  float v62; // [esp+14h] [ebp-10Ch] BYREF
  int lIndividualExp; // [esp+18h] [ebp-108h] BYREF
  CThreat *v64; // [esp+1Ch] [ebp-104h]
  CAggresiveCreature *pHighestThreatCreature; // [esp+20h] [ebp-100h]
  __int64 v66; // [esp+24h] [ebp-FCh]
  int v67; // [esp+2Ch] [ebp-F4h]
  CThreat::AwardInfo aryIndividualAwardInfo[10]; // [esp+30h] [ebp-F0h] BYREF
  CThreat::AwardInfo aryPartyAwardInfo[10]; // [esp+A8h] [ebp-78h] BYREF

  Mysize = this->m_ThreatList._Mysize;
  v64 = this;
  if ( !Mysize )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::DivisionExp", aDWorkRylSource_70, 448, byte_4E3368);
    return;
  }
  if ( !this->m_pOwner )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::DivisionExp", aDWorkRylSource_70, 454, (char *)&byte_4E334C);
    return;
  }
  Myhead = this->m_ThreatList._Myhead;
  Next = Myhead->_Next;
  *(float *)&v5 = 0.0;
  lSumOfThreatAmount = 0;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      m_lThreatAmount = Next->_Myval.m_lThreatAmount;
      Next = Next->_Next;
      v5 += m_lThreatAmount;
    }
    while ( Next != Myhead );
    lSumOfThreatAmount = v5;
  }
  v7 = this->m_ThreatList._Myhead;
  m_nLevel = v7->_Prev->_Myval.m_pCreature->m_CreatureStatus.m_nLevel;
  pHighestThreatCreature = v7->_Prev->_Myval.m_pCreature;
  v9 = v7->_Next;
  cHighestLevel = m_nLevel;
  v10 = 0;
  memset(aryIndividualAwardInfo, 0, sizeof(aryIndividualAwardInfo));
  nNumber = 0;
  if ( v9 != Myhead )
  {
    v11 = aryIndividualAwardInfo;
    while ( v10 < 10 )
    {
      m_pCreature = v9->_Myval.m_pCreature;
      v13 = m_pCreature->__vftable;
      v11->m_pCreature = m_pCreature;
      v14 = ((int (*)(void))v13->GetParty)();
      m_pOwner = v64->m_pOwner;
      m_nMaxHP = m_pOwner->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      lIndividualExp = v5;
      v11->m_pParty = (CParty *)v14;
      v62 = (float)m_nMaxHP;
      v16 = (double)lIndividualExp;
      if ( v5 < 0 )
        v16 = v16 + 4294967300.0;
      *(float *)&lIndividualExp = v16;
      if ( v16 >= v62 )
        p_lIndividualExp = (float *)&lIndividualExp;
      else
        p_lIndividualExp = &v62;
      v66 = v9->_Myval.m_lThreatAmount * m_pOwner->m_CreatureStatus.m_nExp;
      v17 = (unsigned __int64)((double)v66 / *p_lIndividualExp);
      HIDWORD(v17) = v11->m_pCreature->m_CreatureStatus.m_nLevel;
      v11->m_lAward = v17;
      if ( (int)abs32(cHighestLevel - HIDWORD(v17)) > 14 )
        v11->m_lAward = 1;
      v9 = v9->_Next;
      v10 = ++nNumber;
      ++v11;
      if ( v9 == v64->m_ThreatList._Myhead )
        break;
      v5 = lSumOfThreatAmount;
    }
  }
  if ( v10 > 0 )
  {
    v18 = aryIndividualAwardInfo;
    v58 = v10;
    while ( 1 )
    {
      v19 = v18->m_pCreature;
      m_dwCID = v18->m_pCreature->m_dwCID;
      if ( !v18->m_pCreature->m_CellPos.m_wMapIndex )
        break;
      m_wMapIndex = v18->m_pCreature->m_CellPos.m_wMapIndex;
      Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
      if ( VirtualArea )
      {
        m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
        if ( m_pVirtualMonsterMgr )
        {
          IsSummonee = CVirtualMonsterMgr::IsSummonee(m_pVirtualMonsterMgr, m_dwCID);
          goto LABEL_28;
        }
      }
LABEL_40:
      ++v18;
      if ( !--v58 )
        goto LABEL_41;
    }
    v52 = v18->m_pCreature->m_dwCID;
    v25 = (CVirtualMonsterMgr *)CCreatureManager::GetInstance();
    IsSummonee = CVirtualMonsterMgr::IsSummonee(v25, v52);
LABEL_28:
    if ( IsSummonee )
    {
      v26 = (CAggresiveCreature *)v19[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
      if ( v26 )
      {
        if ( pHighestThreatCreature == v19 )
          pHighestThreatCreature = (CAggresiveCreature *)v19[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
        v27 = 0;
        v28 = aryIndividualAwardInfo;
        while ( v28->m_pCreature != v26 )
        {
          ++v27;
          ++v28;
          if ( v27 >= nNumber )
            goto LABEL_37;
        }
        aryIndividualAwardInfo[v27].m_lAward += v18->m_lAward;
        v28 = 0;
        v18->m_pCreature = 0;
        v18->m_pParty = 0;
        v18->m_lAward = 0;
LABEL_37:
        if ( v27 == nNumber )
        {
          v29 = v26->__vftable;
          v18->m_pCreature = v26;
          v18->m_pParty = (CParty *)((int (__fastcall *)(CAggresiveCreature *, CThreat::AwardInfo *))v29->GetParty)(
                                      v26,
                                      v28);
        }
      }
      else
      {
        v18->m_pCreature = 0;
        v18->m_pParty = 0;
        v18->m_lAward = 0;
      }
    }
    goto LABEL_40;
  }
LABEL_41:
  v30 = 0;
  memset(aryPartyAwardInfo, 0, sizeof(aryPartyAwardInfo));
  v31 = 0;
  v32 = aryPartyAwardInfo;
  do
  {
    if ( aryIndividualAwardInfo[v31].m_pCreature )
    {
      v33 = 0;
      if ( v30 <= 0 )
      {
LABEL_48:
        m_pParty = aryIndividualAwardInfo[v31].m_pParty;
        v36 = v32;
        v32->m_pCreature = aryIndividualAwardInfo[v31].m_pCreature;
        m_lAward = aryIndividualAwardInfo[v31].m_lAward;
        ++v30;
        v32->m_pParty = m_pParty;
        ++v32;
        v36->m_lAward = m_lAward;
        if ( v30 > 10 )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CThreat::DivisionExp",
            aDWorkRylSource_70,
            594,
            (char *)&byte_4E3270,
            v30);
      }
      else
      {
        p_m_pParty = &aryPartyAwardInfo[0].m_pParty;
        while ( !*p_m_pParty || *p_m_pParty != aryIndividualAwardInfo[v31].m_pParty )
        {
          ++v33;
          p_m_pParty += 3;
          if ( v33 >= v30 )
            goto LABEL_48;
        }
        aryPartyAwardInfo[v33].m_lAward += aryIndividualAwardInfo[v31].m_lAward;
      }
    }
    ++v31;
  }
  while ( v31 < 10 );
  v38 = 0.0;
  v39 = 1;
  nNumbera = v30;
  v62 = 0.0;
  for ( lSumOfThreatAmounta = 1; ; v39 = lSumOfThreatAmounta )
  {
    if ( v39 < v30 )
    {
      v40 = (CAggresiveCreature **)((char *)&aryPartyAwardInfo[1].m_pCreature + LODWORD(v38));
      v59 = v30 - v39;
      do
      {
        if ( *(int *)((char *)&aryPartyAwardInfo[0].m_lAward + LODWORD(v38)) < v40[2] )
        {
          v41 = (CAggresiveCreature **)((char *)&aryPartyAwardInfo[0].m_pCreature + LODWORD(v38));
          v42 = *(int *)((char *)&aryPartyAwardInfo[0].m_pCreature + LODWORD(v38));
          v43 = *(int *)((char *)&aryPartyAwardInfo[0].m_pParty + LODWORD(v38));
          v67 = *(int *)((char *)&aryPartyAwardInfo[0].m_lAward + LODWORD(v38));
          *v41 = *v40;
          v41[1] = v40[1];
          v38 = v62;
          v41[2] = v40[2];
          v30 = nNumbera;
          *v40 = v42;
          v44 = v67;
          v40[1] = v43;
          v40[2] = v44;
        }
        v40 += 3;
        --v59;
      }
      while ( v59 );
    }
    v45 = *(CCharacter **)((char *)&aryPartyAwardInfo[0].m_pCreature + LODWORD(v38));
    if ( !v45 )
      break;
    v46 = *(CCharacterParty **)((char *)&aryPartyAwardInfo[0].m_pParty + LODWORD(v38));
    v47 = *(unsigned int *)((char *)&aryPartyAwardInfo[0].m_lAward + LODWORD(v38));
    lIndividualExp = v47;
    if ( v46 )
    {
      CCharacterParty::SendDivisionExp(v46, v45, v64->m_pOwner, v47, cHighestLevel);
    }
    else
    {
      v48 = v64;
      v49 = CAggresiveCreature::CalculateLevelGap(v64->m_pOwner, (int)v45);
      v50 = (unsigned __int64)((double)lIndividualExp * `CThreat::GetAggravation'::`2'::aryAggravation[v49 + 20]);
      if ( pHighestThreatCreature != v45 )
        LODWORD(v50) = (int)v50 / 2;
      CCharacter::GetHuntingExp(v45, (unsigned int)v48->m_pOwner, v50, 1);
      v30 = nNumbera;
    }
    LODWORD(v38) += 12;
    ++lSumOfThreatAmounta;
    v62 = v38;
    if ( (int)(lSumOfThreatAmounta - 1) >= 3 )
      break;
  }
}

//----- (0045B270) --------------------------------------------------------
unsigned int *__thiscall CThreat::GetAward(CThreat *this, unsigned int *dwItemKind, unsigned int *dwOwnerID)
{
  signed int v5; // ebx
  CAggresiveCreature *m_pCreature; // esi
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CCharacter *v10; // ecx
  CVirtualMonsterMgr *v11; // eax
  int v12; // edi
  unsigned __int16 v13; // si
  char v14; // al
  unsigned int m_dwDrop; // edi
  int v16; // edi
  int i; // esi
  int v18; // eax
  signed int v19; // edi
  int j; // esi
  unsigned int Award; // eax
  unsigned __int16 m_wMapIndex; // [esp-Ch] [ebp-24h]
  unsigned int m_dwCID; // [esp-Ch] [ebp-24h]
  CCharacter *lpCharacter; // [esp+4h] [ebp-14h]
  int nDropItemIndex; // [esp+8h] [ebp-10h]
  CAggresiveCreature *HighestThreatInfo; // [esp+Ch] [ebp-Ch]
  int nLevelDifferenceForArray; // [esp+14h] [ebp-4h]
  unsigned int *dwOwnerIDa; // [esp+20h] [ebp+8h]

  if ( !this->m_ThreatList._Mysize || (this->m_pOwner->m_dwCID & 0x80000000) == 0 )
    return 0;
  v5 = 0;
  nLevelDifferenceForArray = CAggresiveCreature::CalculateLevelGap(
                               this->m_pOwner,
                               this->m_pOwner->m_CreatureStatus.m_nLevel,
                               this->m_cMaxLevel)
                           + 20;
  m_pCreature = this->m_ThreatList._Myhead->_Prev->_Myval.m_pCreature;
  HighestThreatInfo = this->m_pOwner;
  if ( m_pCreature->m_CellPos.m_wMapIndex )
  {
    m_wMapIndex = m_pCreature->m_CellPos.m_wMapIndex;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
    if ( !VirtualArea )
    {
LABEL_14:
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::GetAward", aDWorkRylSource_70, 963, (char *)&byte_4E33F4);
      return 0;
    }
    m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
    if ( m_pVirtualMonsterMgr && CVirtualMonsterMgr::IsSummonee(m_pVirtualMonsterMgr, m_pCreature->m_dwCID) )
    {
      v10 = (CCharacter *)m_pCreature[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
      lpCharacter = v10;
      goto LABEL_13;
    }
    goto LABEL_11;
  }
  m_dwCID = m_pCreature->m_dwCID;
  v11 = (CVirtualMonsterMgr *)CCreatureManager::GetInstance();
  if ( !CVirtualMonsterMgr::IsSummonee(v11, m_dwCID) )
  {
LABEL_11:
    lpCharacter = (CCharacter *)m_pCreature;
    goto LABEL_12;
  }
  lpCharacter = (CCharacter *)m_pCreature[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
LABEL_12:
  v10 = lpCharacter;
LABEL_13:
  if ( !v10 )
    goto LABEL_14;
  v12 = 0;
  *dwOwnerID = v10->m_dwCID;
  nDropItemIndex = 0;
  dwOwnerIDa = 0;
  while ( 1 )
  {
    v13 = 0;
    dwItemKind[(_DWORD)dwOwnerIDa] = 0;
    if ( v12 >= 10 )
    {
      v14 = v10->GetEliteBonus(v10);
      if ( v14 > 0 )
        v13 = *(unsigned __int8 *)(v14 + v12 + 2 * v14 + 5287499);
    }
    else
    {
      v13 = aryItemDropRate[nLevelDifferenceForArray][v12];
    }
    m_dwDrop = CServerSetup::GetInstance()->m_dwDrop;
    if ( Math::Random::ComplexRandom(10000, 0) <= m_dwDrop * v13 / 0xA )
    {
      v16 = 0;
      for ( i = 0; i < 13; ++i )
      {
        if ( i != 12 || CServerSetup::GetInstance()->m_bLotteryEvent )
          v18 = *((unsigned __int16 *)&HighestThreatInfo[1].m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage + i);
        else
          v18 = 0;
        v16 += v18;
      }
      v19 = Math::Random::ComplexRandom(v16, 0);
      for ( j = 0; j < 13; ++j )
      {
        if ( j != 12 || CServerSetup::GetInstance()->m_bLotteryEvent )
        {
          if ( *(&HighestThreatInfo[1].m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage + j) )
          {
            v5 += *((unsigned __int16 *)&HighestThreatInfo[1].m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage + j);
            if ( v5 > v19 )
            {
              Award = AwardTable::CAward::GetAward(
                        CSingleton<AwardTable::CAward>::ms_pSingleton,
                        j,
                        (CMonster *)this->m_pOwner,
                        lpCharacter);
              dwItemKind[(_DWORD)dwOwnerIDa] = Award;
              if ( Award )
                dwOwnerIDa = (unsigned int *)((char *)dwOwnerIDa + 1);
              else
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "CThreat::GetAward",
                  aDWorkRylSource_70,
                  1013,
                  (char *)&byte_4E33B0,
                  j);
              j = 13;
              v5 = 0;
            }
          }
        }
      }
    }
    if ( ++nDropItemIndex >= 13 )
      break;
    v12 = nDropItemIndex;
    v10 = lpCharacter;
  }
  return dwOwnerIDa;
}

//----- (0045B4F0) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::list<CThreat::ThreatInfo>(std::list<CThreat::ThreatInfo> *this)
{
  this->_Myhead = std::list<CThreat::ThreatInfo>::_Buynode(this);
  this->_Mysize = 0;
}

//----- (0045B510) --------------------------------------------------------
void __thiscall CThreat::ClearAll(CThreat *this)
{
  this->m_cMaxLevel = 0;
  CThreat::ClearThreatList(this);
  CThreat::ClearThreatenedList(this);
}

//----- (0045B530) --------------------------------------------------------
void __thiscall CThreat::CThreat(CThreat *this)
{
  this->m_ThreatList._Myhead = std::list<CThreat::ThreatInfo>::_Buynode(&this->m_ThreatList);
  this->m_ThreatList._Mysize = 0;
  this->m_ThreatenedList._Myhead = (std::_List_nod<CAggresiveCreature *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_ThreatenedList);
  this->m_ThreatenedList._Mysize = 0;
  this->m_pOwner = 0;
  this->m_LatestEnemy[0].m_dwCID = 0;
  this->m_LatestEnemy[0].m_dwTickCount = 0;
  this->m_LatestEnemy[1].m_dwCID = 0;
  this->m_LatestEnemy[1].m_dwTickCount = 0;
  this->m_LatestEnemy[2].m_dwCID = 0;
  this->m_LatestEnemy[2].m_dwTickCount = 0;
  this->m_LatestEnemy[3].m_dwCID = 0;
  this->m_LatestEnemy[3].m_dwTickCount = 0;
  this->m_LatestEnemy[4].m_dwCID = 0;
  this->m_LatestEnemy[4].m_dwTickCount = 0;
  this->m_cMaxLevel = 0;
  CThreat::ClearThreatList(this);
  CThreat::ClearThreatenedList(this);
}

//----- (0045B5D0) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::_Incsize(std::list<CThreat::ThreatInfo> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x1FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0045B670) --------------------------------------------------------
void __thiscall std::list<CAggresiveCreature *>::_Incsize(std::list<CAggresiveCreature *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0045B710) --------------------------------------------------------
void __thiscall CThreat::AddToThreatenedList(CThreat *this, CAggresiveCreature *pDefendCreature)
{
  std::_List_nod<CAggresiveCreature *>::_Node *Myhead; // edx
  std::_List_nod<CAggresiveCreature *>::_Node *i; // eax
  std::_List_nod<CThread *>::_Node *v4; // edi
  std::list<CAggresiveCreature *> *p_m_ThreatenedList; // esi
  std::_List_nod<CThread *>::_Node *v6; // ebx

  Myhead = this->m_ThreatenedList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    if ( i->_Myval == pDefendCreature )
      break;
  }
  if ( i == this->m_ThreatenedList._Myhead )
  {
    v4 = (std::_List_nod<CThread *>::_Node *)this->m_ThreatenedList._Myhead;
    p_m_ThreatenedList = &this->m_ThreatenedList;
    v6 = std::list<IOPCode *>::_Buynode(
           (std::list<CThread *> *)&this->m_ThreatenedList,
           v4,
           v4->_Prev,
           (CThread **)&pDefendCreature);
    std::list<CAggresiveCreature *>::_Incsize(p_m_ThreatenedList, 1u);
    v4->_Prev = v6;
    v6->_Prev->_Next = v6;
  }
}

//----- (0045B770) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::_Splice(
        std::list<CThreat::ThreatInfo> *this,
        std::list<CThreat::ThreatInfo>::iterator _Where,
        std::list<CThreat::ThreatInfo> *_Right,
        std::list<CThreat::ThreatInfo>::iterator _First,
        std::list<CThreat::ThreatInfo>::iterator _Last,
        unsigned int _Count)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // esi

  if ( this != _Right )
  {
    std::list<CThreat::ThreatInfo>::_Incsize(this, _Count);
    _Right->_Mysize -= _Count;
  }
  _First._Ptr->_Prev->_Next = _Last._Ptr;
  _Last._Ptr->_Prev->_Next = _Where._Ptr;
  _Where._Ptr->_Prev->_Next = _First._Ptr;
  Prev = _Where._Ptr->_Prev;
  _Where._Ptr->_Prev = _Last._Ptr->_Prev;
  _Last._Ptr->_Prev = _First._Ptr->_Prev;
  _First._Ptr->_Prev = Prev;
}

//----- (0045B7C0) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareAmount>(
        std::list<CThreat::ThreatInfo> *this,
        std::list<CThreat::ThreatInfo> *_Right,
        int a3)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v5; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // ebx
  std::list<CThreat::ThreatInfo>::iterator v7; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  std::list<CThreat::ThreatInfo> *v10; // [esp+4h] [ebp-8h]
  std::_List_nod<CThreat::ThreatInfo>::_Node *v11; // [esp+8h] [ebp-4h]
  std::list<CThreat::ThreatInfo> *_Righta; // [esp+10h] [ebp+4h]

  v10 = this;
  if ( _Right != this )
  {
    Myhead = this->_Myhead;
    v5 = _Right->_Myhead;
    Next = Myhead->_Next;
    v7._Ptr = v5->_Next;
    v11 = Myhead;
    _Righta = (std::list<CThreat::ThreatInfo> *)v5;
    if ( Myhead->_Next == Myhead )
    {
LABEL_8:
      if ( v7._Ptr != v5 )
        std::list<CThreat::ThreatInfo>::_Splice(
          this,
          (std::list<CThreat::ThreatInfo>::iterator)Myhead,
          _Right,
          v7,
          (std::list<CThreat::ThreatInfo>::iterator)v5,
          _Right->_Mysize);
    }
    else
    {
      while ( v7._Ptr != v5 )
      {
        if ( v7._Ptr->_Myval.m_lThreatAmount >= Next->_Myval.m_lThreatAmount )
        {
          Next = Next->_Next;
        }
        else
        {
          v8 = v7._Ptr->_Next;
          std::list<CThreat::ThreatInfo>::_Incsize(v10, 1u);
          --_Right->_Mysize;
          v7._Ptr->_Prev->_Next = v8;
          v8->_Prev->_Next = Next;
          Next->_Prev->_Next = v7._Ptr;
          Prev = Next->_Prev;
          Next->_Prev = v8->_Prev;
          this = v10;
          v8->_Prev = v7._Ptr->_Prev;
          v5 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)_Righta;
          v7._Ptr->_Prev = Prev;
          Myhead = v11;
          v7._Ptr = v8;
        }
        if ( Next == Myhead )
          goto LABEL_8;
      }
    }
  }
}

//----- (0045B860) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareLevel>(
        std::list<CThreat::ThreatInfo> *this,
        std::list<CThreat::ThreatInfo> *_Right,
        int a3)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v5; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // ebx
  std::list<CThreat::ThreatInfo>::iterator v7; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  std::list<CThreat::ThreatInfo> *v10; // [esp+4h] [ebp-8h]
  std::_List_nod<CThreat::ThreatInfo>::_Node *v11; // [esp+8h] [ebp-4h]
  std::list<CThreat::ThreatInfo> *_Righta; // [esp+10h] [ebp+4h]

  v10 = this;
  if ( _Right != this )
  {
    Myhead = this->_Myhead;
    v5 = _Right->_Myhead;
    Next = Myhead->_Next;
    v7._Ptr = v5->_Next;
    v11 = Myhead;
    _Righta = (std::list<CThreat::ThreatInfo> *)v5;
    if ( Myhead->_Next == Myhead )
    {
LABEL_9:
      if ( v7._Ptr != v5 )
        std::list<CThreat::ThreatInfo>::_Splice(
          this,
          (std::list<CThreat::ThreatInfo>::iterator)Myhead,
          _Right,
          v7,
          (std::list<CThreat::ThreatInfo>::iterator)v5,
          _Right->_Mysize);
    }
    else
    {
      while ( v7._Ptr != v5 )
      {
        if ( v7._Ptr->_Myval.m_pCreature->m_CreatureStatus.m_nLevel >= Next->_Myval.m_pCreature->m_CreatureStatus.m_nLevel )
        {
          Next = Next->_Next;
        }
        else
        {
          v8 = v7._Ptr->_Next;
          std::list<CThreat::ThreatInfo>::_Incsize(v10, 1u);
          --_Right->_Mysize;
          v7._Ptr->_Prev->_Next = v8;
          v8->_Prev->_Next = Next;
          Next->_Prev->_Next = v7._Ptr;
          Prev = Next->_Prev;
          Next->_Prev = v8->_Prev;
          v5 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)_Righta;
          v8->_Prev = v7._Ptr->_Prev;
          v7._Ptr->_Prev = Prev;
          Myhead = v11;
          v7._Ptr = v8;
        }
        if ( Next == Myhead )
        {
          this = v10;
          goto LABEL_9;
        }
      }
    }
  }
}

//----- (0045B910) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareFame>(
        std::list<CThreat::ThreatInfo> *this,
        std::list<CThreat::ThreatInfo> *_Right,
        int a3)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v5; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // ebx
  std::list<CThreat::ThreatInfo>::iterator v7; // esi
  CAggresiveCreature *m_pCreature; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  std::list<CThreat::ThreatInfo> *v11; // [esp+4h] [ebp-Ch]
  std::_List_nod<CThreat::ThreatInfo>::_Node *v12; // [esp+8h] [ebp-8h]
  unsigned int v13; // [esp+Ch] [ebp-4h]
  std::list<CThreat::ThreatInfo> *_Righta; // [esp+14h] [ebp+4h]

  v11 = this;
  if ( _Right != this )
  {
    Myhead = this->_Myhead;
    v5 = _Right->_Myhead;
    Next = Myhead->_Next;
    v7._Ptr = v5->_Next;
    v12 = Myhead;
    _Righta = (std::list<CThreat::ThreatInfo> *)v5;
    if ( Myhead->_Next == Myhead )
    {
LABEL_9:
      if ( v7._Ptr != v5 )
        std::list<CThreat::ThreatInfo>::_Splice(
          this,
          (std::list<CThreat::ThreatInfo>::iterator)Myhead,
          _Right,
          v7,
          (std::list<CThreat::ThreatInfo>::iterator)v5,
          _Right->_Mysize);
    }
    else
    {
      while ( v7._Ptr != v5 )
      {
        m_pCreature = v7._Ptr->_Myval.m_pCreature;
        v13 = Next->_Myval.m_pCreature->GetFame(Next->_Myval.m_pCreature);
        if ( m_pCreature->GetFame(m_pCreature) >= v13 )
        {
          Next = Next->_Next;
        }
        else
        {
          v9 = v7._Ptr->_Next;
          std::list<CThreat::ThreatInfo>::_Incsize(v11, 1u);
          --_Right->_Mysize;
          v7._Ptr->_Prev->_Next = v9;
          v9->_Prev->_Next = Next;
          Next->_Prev->_Next = v7._Ptr;
          Prev = Next->_Prev;
          Next->_Prev = v9->_Prev;
          v9->_Prev = v7._Ptr->_Prev;
          v7._Ptr->_Prev = Prev;
          v7._Ptr = v9;
        }
        v5 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)_Righta;
        if ( Next == v12 )
        {
          this = v11;
          Myhead = v12;
          goto LABEL_9;
        }
      }
    }
  }
}

//----- (0045B9D0) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareAmount>(std::list<CThreat::ThreatInfo> *this, int _Pred)
{
  std::list<CThreat::ThreatInfo> *v2; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v3; // edi
  unsigned int v4; // ebx
  bool v5; // zf
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // ebx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  unsigned int v11; // ebp
  unsigned int *p_Mysize; // esi
  unsigned int Mysize; // edx
  unsigned int v14; // eax
  unsigned int v15; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v16; // ecx
  std::list<CThreat::ThreatInfo> *v17; // eax
  unsigned int v18; // ecx
  std::list<CThreat::ThreatInfo> *v19; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v20; // edx
  std::list<CThreat::ThreatInfo> *v21; // eax
  unsigned int v22; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v23; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v24; // esi
  unsigned int _Maxbin; // [esp+4h] [ebp-158h]
  unsigned int _Maxbina; // [esp+4h] [ebp-158h]
  std::list<CThreat::ThreatInfo> _Templist; // [esp+Ch] [ebp-150h] BYREF
  std::list<CThreat::ThreatInfo> _Binlist[26]; // [esp+18h] [ebp-144h] BYREF
  int v30; // [esp+158h] [ebp-4h]

  v2 = this;
  if ( this->_Mysize >= 2 )
  {
    v3 = std::list<CThreat::ThreatInfo>::_Buynode(&_Templist);
    v4 = 0;
    _Templist._Myhead = v3;
    _Templist._Mysize = 0;
    v30 = 0;
    `eh vector constructor iterator'(
      (char *)_Binlist,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<CThreat::ThreatInfo>::list<CThreat::ThreatInfo>,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v5 = v2->_Mysize == 0;
    LOBYTE(v30) = 1;
    _Maxbin = 0;
    if ( !v5 )
    {
      do
      {
        Myhead = v2->_Myhead;
        Next = Myhead->_Next;
        v8 = v3->_Next;
        if ( Myhead->_Next == Myhead )
          goto LABEL_11;
        v9 = Next->_Next;
        if ( &_Templist == this )
        {
          if ( v8 == Next || v8 == v9 )
            goto LABEL_10;
        }
        else
        {
          std::list<CThreat::ThreatInfo>::_Incsize(&_Templist, 1u);
          v3 = _Templist._Myhead;
          --this->_Mysize;
        }
        Next->_Prev->_Next = v9;
        v9->_Prev->_Next = v8;
        v8->_Prev->_Next = Next;
        Prev = v8->_Prev;
        v8->_Prev = v9->_Prev;
        v9->_Prev = Next->_Prev;
        Next->_Prev = Prev;
LABEL_10:
        v4 = _Maxbin;
LABEL_11:
        v11 = 0;
        if ( !v4 )
          goto LABEL_17;
        p_Mysize = &_Binlist[0]._Mysize;
        do
        {
          if ( !*p_Mysize )
            break;
          std::list<CThreat::ThreatInfo>::merge<CompareAmount>(
            (std::list<CThreat::ThreatInfo> *)(p_Mysize - 2),
            &_Templist,
            _Pred);
          v3 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*(p_Mysize - 1);
          Mysize = _Templist._Mysize;
          *(p_Mysize - 1) = (unsigned int)_Templist._Myhead;
          v14 = *p_Mysize;
          *p_Mysize = Mysize;
          ++v11;
          p_Mysize += 3;
          _Templist._Myhead = v3;
          _Templist._Mysize = v14;
        }
        while ( v11 < v4 );
        if ( v11 == 25 )
        {
          std::list<CThreat::ThreatInfo>::merge<CompareAmount>(&_Binlist[24], &_Templist, _Pred);
          v3 = _Templist._Myhead;
        }
        else
        {
LABEL_17:
          v15 = _Templist._Mysize;
          v16 = _Binlist[v11]._Myhead;
          v17 = &_Binlist[v11];
          v17->_Myhead = v3;
          v3 = v16;
          v18 = v17->_Mysize;
          _Templist._Myhead = v3;
          v17->_Mysize = v15;
          _Templist._Mysize = v18;
          if ( v11 == v4 )
            _Maxbin = ++v4;
        }
        v2 = this;
      }
      while ( this->_Mysize );
    }
    if ( v4 > 1 )
    {
      v19 = &_Binlist[1];
      _Maxbina = v4 - 1;
      do
      {
        std::list<CThreat::ThreatInfo>::merge<CompareAmount>(v19, v19 - 1, _Pred);
        ++v19;
        --_Maxbina;
      }
      while ( _Maxbina );
    }
    v20 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*((_DWORD *)&_Templist._Myhead + 3 * v4);
    v21 = &_Templist + v4;
    v21->_Myhead = v2->_Myhead;
    v2->_Myhead = v20;
    v22 = v21->_Mysize;
    v21->_Mysize = v2->_Mysize;
    v2->_Mysize = v22;
    LOBYTE(v30) = 0;
    `eh vector destructor iterator'(
      (char *)_Binlist,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v23 = v3->_Next;
    v5 = v3->_Next == v3;
    v3->_Next = v3;
    v3->_Prev = v3;
    if ( !v5 )
    {
      do
      {
        v24 = v23->_Next;
        operator delete(v23);
        v23 = v24;
      }
      while ( v24 != v3 );
    }
    operator delete(v3);
  }
}

//----- (0045BC00) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareLevel>(std::list<CThreat::ThreatInfo> *this, int a2)
{
  std::list<CThreat::ThreatInfo> *v2; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v3; // edi
  unsigned int v4; // ebx
  bool v5; // zf
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // ebx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  unsigned int v11; // ebp
  unsigned int *v12; // esi
  unsigned int Mysize; // edx
  unsigned int v14; // eax
  unsigned int v15; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v16; // ecx
  char *v17; // eax
  unsigned int v18; // ecx
  std::list<CThreat::ThreatInfo> *v19; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v20; // edx
  std::list<CThreat::ThreatInfo> *v21; // eax
  unsigned int v22; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v23; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v24; // esi
  unsigned int v25; // [esp+4h] [ebp-158h]
  unsigned int v26; // [esp+4h] [ebp-158h]
  std::list<CThreat::ThreatInfo> _Right; // [esp+Ch] [ebp-150h] BYREF
  char ptr[8]; // [esp+18h] [ebp-144h] BYREF
  char v30; // [esp+20h] [ebp-13Ch] BYREF
  char v31; // [esp+24h] [ebp-138h] BYREF
  std::list<CThreat::ThreatInfo> v32[2]; // [esp+138h] [ebp-24h] BYREF
  int v33; // [esp+158h] [ebp-4h]

  v2 = this;
  if ( this->_Mysize >= 2 )
  {
    v3 = std::list<CThreat::ThreatInfo>::_Buynode(&_Right);
    v4 = 0;
    _Right._Myhead = v3;
    _Right._Mysize = 0;
    v33 = 0;
    `eh vector constructor iterator'(
      ptr,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<CThreat::ThreatInfo>::list<CThreat::ThreatInfo>,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v5 = v2->_Mysize == 0;
    LOBYTE(v33) = 1;
    v25 = 0;
    if ( !v5 )
    {
      do
      {
        Myhead = v2->_Myhead;
        Next = Myhead->_Next;
        v8 = v3->_Next;
        if ( Myhead->_Next == Myhead )
          goto LABEL_11;
        v9 = Next->_Next;
        if ( &_Right == this )
        {
          if ( v8 == Next || v8 == v9 )
            goto LABEL_10;
        }
        else
        {
          std::list<CThreat::ThreatInfo>::_Incsize(&_Right, 1u);
          v3 = _Right._Myhead;
          --this->_Mysize;
        }
        Next->_Prev->_Next = v9;
        v9->_Prev->_Next = v8;
        v8->_Prev->_Next = Next;
        Prev = v8->_Prev;
        v8->_Prev = v9->_Prev;
        v9->_Prev = Next->_Prev;
        Next->_Prev = Prev;
LABEL_10:
        v4 = v25;
LABEL_11:
        v11 = 0;
        if ( !v4 )
          goto LABEL_17;
        v12 = (unsigned int *)&v30;
        do
        {
          if ( !*v12 )
            break;
          std::list<CThreat::ThreatInfo>::merge<CompareLevel>((std::list<CThreat::ThreatInfo> *)(v12 - 2), &_Right, a2);
          v3 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*(v12 - 1);
          Mysize = _Right._Mysize;
          *(v12 - 1) = (unsigned int)_Right._Myhead;
          v14 = *v12;
          *v12 = Mysize;
          ++v11;
          v12 += 3;
          _Right._Myhead = v3;
          _Right._Mysize = v14;
        }
        while ( v11 < v4 );
        if ( v11 == 25 )
        {
          std::list<CThreat::ThreatInfo>::merge<CompareLevel>(v32, &_Right, a2);
          v3 = _Right._Myhead;
        }
        else
        {
LABEL_17:
          v15 = _Right._Mysize;
          v16 = *(std::_List_nod<CThreat::ThreatInfo>::_Node **)&ptr[12 * v11 + 4];
          v17 = &ptr[12 * v11];
          *((_DWORD *)v17 + 1) = v3;
          v3 = v16;
          v18 = *((_DWORD *)v17 + 2);
          _Right._Myhead = v3;
          *((_DWORD *)v17 + 2) = v15;
          _Right._Mysize = v18;
          if ( v11 == v4 )
            v25 = ++v4;
        }
        v2 = this;
      }
      while ( this->_Mysize );
    }
    if ( v4 > 1 )
    {
      v19 = (std::list<CThreat::ThreatInfo> *)&v31;
      v26 = v4 - 1;
      do
      {
        std::list<CThreat::ThreatInfo>::merge<CompareLevel>(v19, v19 - 1, a2);
        ++v19;
        --v26;
      }
      while ( v26 );
    }
    v20 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*((_DWORD *)&_Right._Myhead + 3 * v4);
    v21 = &_Right + v4;
    v21->_Myhead = v2->_Myhead;
    v2->_Myhead = v20;
    v22 = v21->_Mysize;
    v21->_Mysize = v2->_Mysize;
    v2->_Mysize = v22;
    LOBYTE(v33) = 0;
    `eh vector destructor iterator'(
      ptr,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v23 = v3->_Next;
    v5 = v3->_Next == v3;
    v3->_Next = v3;
    v3->_Prev = v3;
    if ( !v5 )
    {
      do
      {
        v24 = v23->_Next;
        operator delete(v23);
        v23 = v24;
      }
      while ( v24 != v3 );
    }
    operator delete(v3);
  }
}

//----- (0045BE30) --------------------------------------------------------
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareFame>(std::list<CThreat::ThreatInfo> *this, int a2)
{
  std::list<CThreat::ThreatInfo> *v2; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v3; // edi
  unsigned int v4; // ebx
  bool v5; // zf
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v8; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // ebx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // eax
  unsigned int v11; // ebp
  unsigned int *v12; // esi
  unsigned int Mysize; // edx
  unsigned int v14; // eax
  unsigned int v15; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v16; // ecx
  char *v17; // eax
  unsigned int v18; // ecx
  std::list<CThreat::ThreatInfo> *v19; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v20; // edx
  std::list<CThreat::ThreatInfo> *v21; // eax
  unsigned int v22; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v23; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v24; // esi
  unsigned int v25; // [esp+4h] [ebp-158h]
  unsigned int v26; // [esp+4h] [ebp-158h]
  std::list<CThreat::ThreatInfo> _Right; // [esp+Ch] [ebp-150h] BYREF
  char ptr[8]; // [esp+18h] [ebp-144h] BYREF
  char v30; // [esp+20h] [ebp-13Ch] BYREF
  char v31; // [esp+24h] [ebp-138h] BYREF
  std::list<CThreat::ThreatInfo> v32[2]; // [esp+138h] [ebp-24h] BYREF
  int v33; // [esp+158h] [ebp-4h]

  v2 = this;
  if ( this->_Mysize >= 2 )
  {
    v3 = std::list<CThreat::ThreatInfo>::_Buynode(&_Right);
    v4 = 0;
    _Right._Myhead = v3;
    _Right._Mysize = 0;
    v33 = 0;
    `eh vector constructor iterator'(
      ptr,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<CThreat::ThreatInfo>::list<CThreat::ThreatInfo>,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v5 = v2->_Mysize == 0;
    LOBYTE(v33) = 1;
    v25 = 0;
    if ( !v5 )
    {
      do
      {
        Myhead = v2->_Myhead;
        Next = Myhead->_Next;
        v8 = v3->_Next;
        if ( Myhead->_Next == Myhead )
          goto LABEL_11;
        v9 = Next->_Next;
        if ( &_Right == this )
        {
          if ( v8 == Next || v8 == v9 )
            goto LABEL_10;
        }
        else
        {
          std::list<CThreat::ThreatInfo>::_Incsize(&_Right, 1u);
          v3 = _Right._Myhead;
          --this->_Mysize;
        }
        Next->_Prev->_Next = v9;
        v9->_Prev->_Next = v8;
        v8->_Prev->_Next = Next;
        Prev = v8->_Prev;
        v8->_Prev = v9->_Prev;
        v9->_Prev = Next->_Prev;
        Next->_Prev = Prev;
LABEL_10:
        v4 = v25;
LABEL_11:
        v11 = 0;
        if ( !v4 )
          goto LABEL_17;
        v12 = (unsigned int *)&v30;
        do
        {
          if ( !*v12 )
            break;
          std::list<CThreat::ThreatInfo>::merge<CompareFame>((std::list<CThreat::ThreatInfo> *)(v12 - 2), &_Right, a2);
          v3 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*(v12 - 1);
          Mysize = _Right._Mysize;
          *(v12 - 1) = (unsigned int)_Right._Myhead;
          v14 = *v12;
          *v12 = Mysize;
          ++v11;
          v12 += 3;
          _Right._Myhead = v3;
          _Right._Mysize = v14;
        }
        while ( v11 < v4 );
        if ( v11 == 25 )
        {
          std::list<CThreat::ThreatInfo>::merge<CompareFame>(v32, &_Right, a2);
          v3 = _Right._Myhead;
        }
        else
        {
LABEL_17:
          v15 = _Right._Mysize;
          v16 = *(std::_List_nod<CThreat::ThreatInfo>::_Node **)&ptr[12 * v11 + 4];
          v17 = &ptr[12 * v11];
          *((_DWORD *)v17 + 1) = v3;
          v3 = v16;
          v18 = *((_DWORD *)v17 + 2);
          _Right._Myhead = v3;
          *((_DWORD *)v17 + 2) = v15;
          _Right._Mysize = v18;
          if ( v11 == v4 )
            v25 = ++v4;
        }
        v2 = this;
      }
      while ( this->_Mysize );
    }
    if ( v4 > 1 )
    {
      v19 = (std::list<CThreat::ThreatInfo> *)&v31;
      v26 = v4 - 1;
      do
      {
        std::list<CThreat::ThreatInfo>::merge<CompareFame>(v19, v19 - 1, a2);
        ++v19;
        --v26;
      }
      while ( v26 );
    }
    v20 = (std::_List_nod<CThreat::ThreatInfo>::_Node *)*((_DWORD *)&_Right._Myhead + 3 * v4);
    v21 = &_Right + v4;
    v21->_Myhead = v2->_Myhead;
    v2->_Myhead = v20;
    v22 = v21->_Mysize;
    v21->_Mysize = v2->_Mysize;
    v2->_Mysize = v22;
    LOBYTE(v33) = 0;
    `eh vector destructor iterator'(
      ptr,
      0xCu,
      26,
      (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
    v23 = v3->_Next;
    v5 = v3->_Next == v3;
    v3->_Next = v3;
    v3->_Prev = v3;
    if ( !v5 )
    {
      do
      {
        v24 = v23->_Next;
        operator delete(v23);
        v23 = v24;
      }
      while ( v24 != v3 );
    }
    operator delete(v3);
  }
}

//----- (0045C060) --------------------------------------------------------
void __thiscall CThreat::AddToThreatList(CThreat *this, CAggresiveCreature *pAttackCreature, int lThreatAmount)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v6; // eax
  unsigned int Mysize; // eax
  int v8; // ecx
  int m_nLevel; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v10; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v11; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v12; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v13; // [esp-10h] [ebp-20h]
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // [esp-Ch] [ebp-1Ch]
  CThreat::ThreatInfo _Val; // [esp+8h] [ebp-8h] BYREF

  if ( pAttackCreature && lThreatAmount )
  {
    Myhead = this->m_ThreatList._Myhead;
    Next = Myhead->_Next;
    v6 = Myhead->_Next;
    if ( Myhead->_Next == Myhead )
    {
LABEL_6:
      if ( lThreatAmount < 0 )
        return;
      Mysize = this->m_ThreatList._Mysize;
      if ( Mysize > 0xA )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CThreat::AddToThreatList",
          aDWorkRylSource_70,
          142,
          aCid0x08x_66,
          this->m_pOwner->m_dwCID,
          this->m_ThreatList._Mysize);
        return;
      }
      if ( Mysize == 10 )
      {
        if ( lThreatAmount < Next->_Myval.m_lThreatAmount )
          return;
        CThreat::DeleteThreatened(&Next->_Myval.m_pCreature->m_Threat, this->m_pOwner);
        std::list<CThreat::ThreatInfo>::pop_front(&this->m_ThreatList);
      }
      m_nLevel = pAttackCreature->m_CreatureStatus.m_nLevel;
      if ( m_nLevel > this->m_cMaxLevel )
        this->m_cMaxLevel = m_nLevel;
      CThreat::AddToThreatenedList(&pAttackCreature->m_Threat, this->m_pOwner);
      v10 = this->m_ThreatList._Myhead;
      _Val.m_pCreature = pAttackCreature;
      v11 = v10->_Next;
      Prev = v10->_Next->_Prev;
      v13 = v10->_Next;
      _Val.m_lThreatAmount = lThreatAmount;
      v12 = std::list<CThreat::ThreatInfo>::_Buynode(&this->m_ThreatList, v13, Prev, &_Val);
      std::list<CThreat::ThreatInfo>::_Incsize(&this->m_ThreatList, 1u);
      v11->_Prev = v12;
      LOBYTE(pAttackCreature) = 0;
      v12->_Prev->_Next = v12;
      std::list<CThreat::ThreatInfo>::sort<CompareAmount>(&this->m_ThreatList, (int)pAttackCreature);
    }
    else
    {
      while ( pAttackCreature != v6->_Myval.m_pCreature )
      {
        v6 = v6->_Next;
        if ( v6 == this->m_ThreatList._Myhead )
          goto LABEL_6;
      }
      v8 = lThreatAmount + v6->_Myval.m_lThreatAmount;
      v6->_Myval.m_lThreatAmount = v8;
      if ( v8 <= 0 )
      {
        CThreat::DeleteThreatened(&pAttackCreature->m_Threat, this->m_pOwner);
        CThreat::DeleteThreat(this, pAttackCreature);
      }
      LOBYTE(pAttackCreature) = 0;
      std::list<CThreat::ThreatInfo>::sort<CompareAmount>(&this->m_ThreatList, (int)pAttackCreature);
    }
  }
}

//----- (0045C1B0) --------------------------------------------------------
void __thiscall CThreat::AffectThreat(
        CThreat *this,
        CAggresiveCreature *pTauntCreature,
        int lDamage,
        CThreat::AffectThreatType eType)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // ecx
  unsigned __int16 v6; // bx
  int v7; // edx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // eax
  int m_lThreatAmount; // ecx
  int v10; // ecx

  if ( this->m_pOwner->m_CreatureStatus.m_nNowHP > lDamage )
  {
    Myhead = this->m_ThreatList._Myhead;
    v6 = 0;
    v7 = lDamage * (2 * (eType == TAUNT) - 1);
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      do
      {
        m_lThreatAmount = Next->_Myval.m_lThreatAmount;
        if ( m_lThreatAmount <= v7 )
          v10 = 0;
        else
          v10 = m_lThreatAmount - v7;
        Next->_Myval.m_lThreatAmount = v10;
        Next = Next->_Next;
        ++v6;
      }
      while ( Next != this->m_ThreatList._Myhead );
      if ( v6 > 0xAu )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CThreat::AffectThreat",
          aDWorkRylSource_70,
          273,
          (char *)&byte_4E34B0,
          v6);
    }
    CThreat::AddToThreatList(this, pTauntCreature, lDamage * (2 * (eType == TAUNT) - 1) * v6);
  }
}

//----- (0045C250) --------------------------------------------------------
CAggresiveCreature *__thiscall CThreat::GetTarget(CThreat *this)
{
  std::_List_nod<CThreat::ThreatInfo>::_Node *v3; // ebx
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // edi
  bool v6; // zf
  CAggresiveCreature *m_pCreature; // esi
  int m_lThreatAmount; // eax
  CAggresiveCreature *m_pOwner; // eax
  double v10; // st6
  double v11; // st4
  double v12; // st5
  std::_List_nod<CThreat::ThreatInfo>::_Node **p_Prev; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v14; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v15; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v16; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v17; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v18; // eax
  CAggresiveCreature *v19; // ebx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v20; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *it; // [esp+Ch] [ebp-3Ch]
  float fAttackRange; // [esp+10h] [ebp-38h]
  CAggresiveCreature *_Pred; // [esp+14h] [ebp-34h]
  CThreat::ThreatInfo TempTargetInfo; // [esp+18h] [ebp-30h] BYREF
  std::list<CThreat::ThreatInfo> TargetThreatList; // [esp+20h] [ebp-28h] BYREF
  MotionInfo Motion; // [esp+2Ch] [ebp-1Ch] BYREF
  int v27; // [esp+44h] [ebp-4h]

  if ( !this->m_ThreatList._Mysize )
    return 0;
  _Pred = this->m_pOwner;
  if ( (_Pred->m_dwCID & 0x80000000) == 0 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CThreat::GetTarget",
      aDWorkRylSource_70,
      350,
      aCid0x08x_40,
      _Pred->m_dwCID);
    return 0;
  }
  v3 = std::list<CThreat::ThreatInfo>::_Buynode(&TargetThreatList);
  TargetThreatList._Myhead = v3;
  TargetThreatList._Mysize = 0;
  Myhead = this->m_ThreatList._Myhead;
  Next = Myhead->_Next;
  v6 = Myhead->_Next == Myhead;
  v27 = 0;
  it = Next;
  if ( v6 )
    goto LABEL_18;
  while ( 1 )
  {
    m_lThreatAmount = Next->_Myval.m_lThreatAmount;
    TempTargetInfo.m_pCreature = Next->_Myval.m_pCreature;
    m_pCreature = TempTargetInfo.m_pCreature;
    TempTargetInfo.m_lThreatAmount = m_lThreatAmount;
    if ( TempTargetInfo.m_pCreature
      && (TempTargetInfo.m_pCreature->m_dwStatusFlag & 0x10000) == 0
      && this->m_pOwner->IsEnemy(this->m_pOwner, TempTargetInfo.m_pCreature) == HOSTILITY )
    {
      fAttackRange = (double)this->m_pOwner->m_CreatureStatus.m_StatusInfo.m_nAttackRange * 0.0099999998;
      MotionInfo::MotionInfo(&Motion);
      ((void (__thiscall *)(CAggresiveCreature *, int, MotionInfo *))_Pred->__vftable[1].~CAggresiveCreature)(
        _Pred,
        8,
        &Motion);
      m_pOwner = this->m_pOwner;
      v10 = m_pCreature->m_CurrentPos.m_fPointZ - m_pOwner->m_CurrentPos.m_fPointZ;
      v11 = m_pCreature->m_CurrentPos.m_fPointX - m_pOwner->m_CurrentPos.m_fPointX;
      v12 = sqrt(v10 * v10 + v11 * v11);
      if ( v12 >= fAttackRange )
      {
        if ( v12 < Motion.m_fVelocity + fAttackRange )
          TempTargetInfo.m_lThreatAmount *= 3;
      }
      else
      {
        TempTargetInfo.m_lThreatAmount *= 6;
      }
      p_Prev = &v3->_Prev;
      v14 = std::list<CThreat::ThreatInfo>::_Buynode(&TargetThreatList, v3, v3->_Prev, &TempTargetInfo);
      std::list<CThreat::ThreatInfo>::_Incsize(&TargetThreatList, 1u);
      v3 = TargetThreatList._Myhead;
      *p_Prev = v14;
      Next = it;
      v14->_Prev->_Next = v14;
    }
    it = Next->_Next;
    if ( Next->_Next == this->m_ThreatList._Myhead )
      break;
    Next = Next->_Next;
  }
  if ( TargetThreatList._Mysize )
  {
    LOBYTE(_Pred) = 0;
    std::list<CThreat::ThreatInfo>::sort<CompareAmount>(&TargetThreatList, (int)_Pred);
    v17 = TargetThreatList._Myhead;
    v18 = TargetThreatList._Myhead->_Next;
    v6 = TargetThreatList._Myhead->_Next == TargetThreatList._Myhead;
    v19 = TargetThreatList._Myhead->_Prev->_Myval.m_pCreature;
    TargetThreatList._Myhead->_Next = TargetThreatList._Myhead;
    v17->_Prev = v17;
    if ( !v6 )
    {
      do
      {
        v20 = v18->_Next;
        operator delete(v18);
        v18 = v20;
      }
      while ( v20 != v17 );
    }
    operator delete(v17);
    return v19;
  }
  else
  {
LABEL_18:
    v15 = v3->_Next;
    v6 = v3->_Next == v3;
    v3->_Next = v3;
    v3->_Prev = v3;
    if ( !v6 )
    {
      do
      {
        v16 = v15->_Next;
        operator delete(v15);
        v15 = v16;
      }
      while ( v16 != v3 );
    }
    operator delete(v3);
    return 0;
  }
}

//----- (0045C4A0) --------------------------------------------------------
char __thiscall CThreat::DivisionFame(CThreat *this)
{
  unsigned int Mysize; // eax
  CAggresiveCreature *m_pOwner; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v5; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *Myhead; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Next; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node **p_Prev; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v9; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v10; // edi
  CAggresiveCreature *m_pCreature; // eax
  signed int m_dwCID; // ebp
  unsigned __int16 m_wMapIndex; // ax
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CAggresiveCreature *v17; // ecx
  CVirtualMonsterMgr *v18; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v19; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *Prev; // edx
  int m_lThreatAmount; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node **v22; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v23; // esi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v24; // ebp
  std::_List_nod<CThreat::ThreatInfo>::_Node *v25; // eax
  bool v26; // zf
  std::_List_nod<CThreat::ThreatInfo>::_Node *v27; // edi
  CAggresiveCreature *v28; // ebp
  CCharacter *v29; // ebx
  CSendStream *v30; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *j; // edi
  CCharacter *v32; // eax
  CSendStream *v33; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v34; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v35; // edi
  CAggresiveCreature *v36; // edi
  CAggresiveCreature *v37; // esi
  int v38; // esi
  int v39; // eax
  int v40; // esi
  int *p_lReduceFame; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v42; // edi
  int v43; // ebx
  CCharacter *v44; // esi
  signed int m_nMaxHP; // ecx
  signed int v46; // eax
  CAggresiveCreature *v47; // ebp
  int *p_MasterThreatInfo; // eax
  int v49; // ebp
  int v50; // eax
  unsigned int Mileage; // edx
  CSendStream *m_lpGameClientDispatch; // eax
  signed int v53; // eax
  CCharacter *v54; // edi
  signed int v55; // esi
  signed int v56; // eax
  signed int m_dwMaxFame; // eax
  int v58; // eax
  CSendStream *v59; // eax
  int v60; // ebp
  CCharacterParty *m_pParty; // esi
  unsigned __int8 v62; // bl
  int v63; // esi
  CParty **v64; // ebp
  CParty *v65; // ecx
  CParty **v66; // esi
  CParty_vtbl *v67; // edx
  int i; // edi
  signed int v69; // ecx
  int m_lAward; // eax
  double v71; // st7
  CAggresiveCreature *v72; // esi
  CThreat::ThreatInfo *p_p_m_pParty; // eax
  CAggresiveCreature *v74; // esi
  unsigned int v75; // eax
  CCharacter *v76; // eax
  CSendStream *v77; // ecx
  std::_List_nod<CThreat::ThreatInfo>::_Node *v78; // edi
  std::_List_nod<CThreat::ThreatInfo>::_Node *v79; // eax
  std::_List_nod<CThreat::ThreatInfo>::_Node *v80; // esi
  unsigned __int16 v81; // [esp-Ch] [ebp-ECh]
  unsigned int v82; // [esp-Ch] [ebp-ECh]
  int lReduceFame; // [esp+8h] [ebp-D8h] BYREF
  unsigned __int8 cHighestLevel; // [esp+Fh] [ebp-D1h] BYREF
  unsigned int dwAward; // [esp+10h] [ebp-D0h] BYREF
  CThreat::ThreatInfo MasterThreatInfo; // [esp+14h] [ebp-CCh] BYREF
  std::list<CThreat::ThreatInfo> copyThreatList; // [esp+1Ch] [ebp-C4h] BYREF
  CParty **p_m_pParty; // [esp+28h] [ebp-B8h] BYREF
  CCharacter *lpLoserCharacter; // [esp+2Ch] [ebp-B4h]
  int wStandardConstant; // [esp+30h] [ebp-B0h] BYREF
  CCharacter *aryNearMemberList[10]; // [esp+34h] [ebp-ACh] BYREF
  CThreat::AwardInfo aryPartyAwardInfo[10]; // [esp+5Ch] [ebp-84h] BYREF
  int v93; // [esp+DCh] [ebp-4h]

  Mysize = this->m_ThreatList._Mysize;
  lReduceFame = (int)this;
  if ( !Mysize )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::DivisionFame", aDWorkRylSource_70, 644, byte_4E35B8);
    return 0;
  }
  m_pOwner = this->m_pOwner;
  if ( !m_pOwner )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::DivisionFame", aDWorkRylSource_70, 650, (char *)&byte_4E334C);
    return 0;
  }
  if ( (m_pOwner->m_dwCID & 0x80000000) != 0 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CThreat::DivisionFame", aDWorkRylSource_70, 656, (char *)&byte_4E3588);
    return 0;
  }
  v5 = std::list<CThreat::ThreatInfo>::_Buynode(&copyThreatList);
  copyThreatList._Myhead = v5;
  copyThreatList._Mysize = 0;
  Myhead = this->m_ThreatList._Myhead;
  v93 = 0;
  Next = Myhead->_Next;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      p_Prev = &v5->_Prev;
      v9 = std::list<CThreat::ThreatInfo>::_Buynode(&copyThreatList, v5, v5->_Prev, &Next->_Myval);
      std::list<CThreat::ThreatInfo>::_Incsize(&copyThreatList, 1u);
      *p_Prev = v9;
      v9->_Prev->_Next = v9;
      Next = Next->_Next;
      v5 = copyThreatList._Myhead;
    }
    while ( Next != this->m_ThreatList._Myhead );
  }
  v10 = v5->_Next;
  if ( v5->_Next != v5 )
  {
    while ( 1 )
    {
      m_pCreature = v10->_Myval.m_pCreature;
      m_dwCID = m_pCreature->m_dwCID;
      if ( m_dwCID < 0 )
        break;
      v10 = v10->_Next;
LABEL_30:
      if ( v10 == v5 )
        goto LABEL_31;
    }
    m_wMapIndex = m_pCreature->m_CellPos.m_wMapIndex;
    if ( m_wMapIndex )
    {
      v81 = m_wMapIndex;
      Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, v81);
      if ( VirtualArea )
      {
        m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
        if ( m_pVirtualMonsterMgr )
        {
          if ( CVirtualMonsterMgr::IsSummonee(m_pVirtualMonsterMgr, m_dwCID) )
          {
            v17 = (CAggresiveCreature *)v10->_Myval.m_pCreature[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
LABEL_19:
            if ( v17 )
            {
              v19 = v5->_Next;
              if ( v5->_Next == v5 )
                goto LABEL_25;
              while ( v19->_Myval.m_pCreature != v17 )
              {
                v19 = v19->_Next;
                if ( v19 == v5 )
                  goto LABEL_25;
              }
              v19->_Myval.m_lThreatAmount += v10->_Myval.m_lThreatAmount;
              if ( v19 == v5 )
              {
LABEL_25:
                Prev = v5->_Prev;
                m_lThreatAmount = v10->_Myval.m_lThreatAmount;
                MasterThreatInfo.m_pCreature = v17;
                v22 = &v5->_Prev;
                MasterThreatInfo.m_lThreatAmount = m_lThreatAmount;
                v23 = std::list<CThreat::ThreatInfo>::_Buynode(&copyThreatList, v5, Prev, &MasterThreatInfo);
                std::list<CThreat::ThreatInfo>::_Incsize(&copyThreatList, 1u);
                *v22 = v23;
                v23->_Prev->_Next = v23;
                v5 = copyThreatList._Myhead;
              }
            }
          }
        }
      }
    }
    else
    {
      v18 = (CVirtualMonsterMgr *)CCreatureManager::GetInstance();
      if ( CVirtualMonsterMgr::IsSummonee(v18, m_dwCID) )
      {
        v17 = (CAggresiveCreature *)v10->_Myval.m_pCreature[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
        goto LABEL_19;
      }
    }
    v24 = v10->_Next;
    if ( v10 != v5 )
    {
      v10->_Prev->_Next = v24;
      v10->_Next->_Prev = v10->_Prev;
      operator delete(v10);
      --copyThreatList._Mysize;
    }
    v10 = v24;
    goto LABEL_30;
  }
LABEL_31:
  if ( copyThreatList._Mysize )
  {
    v28 = v5->_Prev->_Myval.m_pCreature;
    v29 = (CCharacter *)this->m_pOwner;
    v82 = v28->m_dwCID;
    lpLoserCharacter = v29;
    if ( CThreat::SaveEnemy((CThreat *)lReduceFame, v82) != 1 )
    {
      LOBYTE(lReduceFame) = 0;
      std::list<CThreat::ThreatInfo>::sort<CompareLevel>(&copyThreatList, lReduceFame);
      v36 = copyThreatList._Myhead->_Prev->_Myval.m_pCreature;
      LOBYTE(lReduceFame) = 0;
      std::list<CThreat::ThreatInfo>::sort<CompareFame>(&copyThreatList, lReduceFame);
      v37 = copyThreatList._Myhead->_Prev->_Myval.m_pCreature;
      LOBYTE(lReduceFame) = 0;
      std::list<CThreat::ThreatInfo>::sort<CompareAmount>(&copyThreatList, lReduceFame);
      v38 = v37->GetFame(v37);
      v39 = v29->GetFame(v29);
      lReduceFame = v29->m_CreatureStatus.m_nLevel - v36->m_CreatureStatus.m_nLevel + 4;
      v40 = (unsigned __int64)((double)(v39 - v38) * 0.00050000002 + (double)lReduceFame);
      wStandardConstant = v40;
      lReduceFame = SLOWORD(CServerSetup::GetInstance()->m_dwMinFame);
      p_lReduceFame = &lReduceFame;
      if ( (__int16)v40 >= (__int16)lReduceFame )
        p_lReduceFame = &wStandardConstant;
      v42 = copyThreatList._Myhead->_Next;
      v43 = 0;
      v26 = copyThreatList._Myhead->_Next == copyThreatList._Myhead;
      LOWORD(wStandardConstant) = *(_WORD *)p_lReduceFame;
      lReduceFame = 0;
      memset(aryPartyAwardInfo, 0, sizeof(aryPartyAwardInfo));
      if ( !v26 )
      {
        p_m_pParty = &aryPartyAwardInfo[0].m_pParty;
        do
        {
          if ( v43 >= 10 )
            break;
          v44 = (CCharacter *)v42->_Myval.m_pCreature;
          if ( v44->m_DBData.m_Info.Nationality != lpLoserCharacter->m_DBData.m_Info.Nationality )
          {
            lReduceFame += v42->_Myval.m_lThreatAmount;
            if ( v44->GetParty(v44) )
            {
              v63 = 0;
              if ( v43 <= 0 )
              {
LABEL_76:
                v65 = (CParty *)v42->_Myval.m_pCreature;
                v66 = p_m_pParty;
                v67 = v65->__vftable;
                *(p_m_pParty - 1) = v65;
                *v66 = (CParty *)((int (*)(void))v67->Login)();
                v66[1] = (CParty *)v42->_Myval.m_lThreatAmount;
                ++v43;
                p_m_pParty = v66 + 3;
              }
              else
              {
                v64 = &aryPartyAwardInfo[0].m_pParty;
                while ( *v64 != v42->_Myval.m_pCreature->GetParty(v42->_Myval.m_pCreature) )
                {
                  ++v63;
                  v64 += 3;
                  if ( v63 >= v43 )
                    goto LABEL_76;
                }
                aryPartyAwardInfo[v63].m_lAward += v42->_Myval.m_lThreatAmount;
              }
            }
            else
            {
              m_nMaxHP = lpLoserCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
              v46 = v42->_Myval.m_lThreatAmount;
              dwAward = m_nMaxHP;
              if ( v46 >= m_nMaxHP )
                v46 = m_nMaxHP;
              MasterThreatInfo.m_pCreature = (CAggresiveCreature *)(v46 * (__int16)wStandardConstant);
              v47 = (CAggresiveCreature *)(unsigned __int64)((double)(int)MasterThreatInfo.m_pCreature
                                                           / (double)(int)dwAward
                                                           / ((double)(int)MasterThreatInfo.m_pCreature
                                                            / (double)(int)dwAward
                                                            + 9.0)
                                                           * 10.0);
              dwAward = (unsigned int)v47;
              MasterThreatInfo.m_pCreature = (CAggresiveCreature *)CServerSetup::GetInstance()->m_dwMinFame;
              p_MasterThreatInfo = (int *)&MasterThreatInfo;
              if ( v47 >= MasterThreatInfo.m_pCreature )
                p_MasterThreatInfo = (int *)&dwAward;
              v49 = *p_MasterThreatInfo;
              v50 = v44->GetFame(v44);
              CCharacter::SetFame(v44, v49 + v50);
              Mileage = v44->m_DBData.m_Info.Mileage;
              m_lpGameClientDispatch = (CSendStream *)v44->m_lpGameClientDispatch;
              v44->m_DBData.m_Info.Mileage = Mileage + 20;
              if ( Mileage != -20 )
                GameClientSendPacket::SendCharFameInfo(
                  m_lpGameClientDispatch + 8,
                  v44,
                  szLoseCharName,
                  szLoseCharName,
                  0);
            }
          }
          v42 = v42->_Next;
        }
        while ( v42 != copyThreatList._Myhead );
      }
      v53 = lpLoserCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      dwAward = v53;
      if ( lReduceFame < v53 )
        v53 = lReduceFame;
      MasterThreatInfo.m_pCreature = (CAggresiveCreature *)((__int16)wStandardConstant * v53);
      v54 = lpLoserCharacter;
      v55 = (unsigned __int64)((double)(int)MasterThreatInfo.m_pCreature / (double)(int)dwAward * 5.0);
      v56 = lpLoserCharacter->GetFame(lpLoserCharacter);
      if ( v56 < v55 )
        v55 = v56;
      m_dwMaxFame = CServerSetup::GetInstance()->m_dwMaxFame;
      if ( m_dwMaxFame < v55 )
        v55 = m_dwMaxFame;
      v58 = v54->GetFame(v54);
      CCharacter::SetFame(v54, v58 - v55);
      v59 = (CSendStream *)v54->m_lpGameClientDispatch;
      if ( v59 )
        GameClientSendPacket::SendCharFameInfo(v59 + 8, v54, szLoseCharName, szLoseCharName, 0);
      if ( v43 > 0 )
      {
        v60 = 0;
        lReduceFame = v43;
        do
        {
          m_pParty = (CCharacterParty *)aryPartyAwardInfo[v60].m_pParty;
          memset(aryNearMemberList, 0, sizeof(aryNearMemberList));
          v62 = 0;
          if ( !m_pParty
            || (cHighestLevel = 0,
                (v62 = CCharacterParty::GetNearMemberList(
                         m_pParty,
                         aryPartyAwardInfo[v60].m_pCreature->m_CellPos.m_lpCell,
                         0,
                         aryNearMemberList,
                         &cHighestLevel)) != 0) )
          {
            for ( i = 0; i < 10; ++i )
            {
              if ( !aryNearMemberList[i] )
                break;
              v69 = lpLoserCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
              m_lAward = aryPartyAwardInfo[v60].m_lAward;
              dwAward = v69;
              if ( m_lAward >= v69 )
                m_lAward = v69;
              MasterThreatInfo.m_pCreature = (CAggresiveCreature *)((__int16)wStandardConstant * m_lAward);
              v71 = (double)(int)MasterThreatInfo.m_pCreature;
              MasterThreatInfo.m_pCreature = (CAggresiveCreature *)v62;
              v72 = (CAggresiveCreature *)(unsigned __int64)(v71
                                                           / (double)(int)dwAward
                                                           * 1.0
                                                           / (v71 / (double)(int)dwAward * 1.0 + 1.0)
                                                           * 11.0
                                                           * (double)v62);
              p_m_pParty = (CParty **)v72;
              MasterThreatInfo.m_pCreature = (CAggresiveCreature *)CServerSetup::GetInstance()->m_dwMinFame;
              p_p_m_pParty = &MasterThreatInfo;
              if ( v72 >= MasterThreatInfo.m_pCreature )
                p_p_m_pParty = (CThreat::ThreatInfo *)&p_m_pParty;
              v74 = p_p_m_pParty->m_pCreature;
              v75 = aryNearMemberList[i]->GetFame(aryNearMemberList[i]);
              CCharacter::SetFame(aryNearMemberList[i], (unsigned int)v74 + v75);
              v76 = aryNearMemberList[i];
              v76->m_DBData.m_Info.Mileage += 20;
              v77 = (CSendStream *)v76->m_lpGameClientDispatch;
              if ( v77 )
                GameClientSendPacket::SendCharFameInfo(v77 + 8, v76, szLoseCharName, szLoseCharName, 0);
            }
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CThreat::DivisionFame",
              aDWorkRylSource_70,
              871,
              aPid0x08x_0,
              m_pParty->m_Party.m_dwPartyID);
          }
          ++v60;
          --lReduceFame;
        }
        while ( lReduceFame );
      }
      v78 = copyThreatList._Myhead;
      v79 = copyThreatList._Myhead->_Next;
      v26 = copyThreatList._Myhead->_Next == copyThreatList._Myhead;
      copyThreatList._Myhead->_Next = copyThreatList._Myhead;
      v78->_Prev = v78;
      if ( !v26 )
      {
        do
        {
          v80 = v79->_Next;
          operator delete(v79);
          v79 = v80;
        }
        while ( v80 != v78 );
      }
      operator delete(v78);
      return 1;
    }
    v30 = (CSendStream *)v29->m_lpGameClientDispatch;
    if ( v30 )
      GameClientSendPacket::SendCharFameInfo(
        v30 + 8,
        v29,
        (const char *)&v28[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1],
        v29->m_DBData.m_Info.Name,
        3u);
    for ( j = v5->_Next; j != v5; j = j->_Next )
    {
      v32 = (CCharacter *)j->_Myval.m_pCreature;
      v33 = (CSendStream *)v32->m_lpGameClientDispatch;
      if ( v33 )
        GameClientSendPacket::SendCharFameInfo(
          v33 + 8,
          v32,
          (const char *)&v28[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1],
          v29->m_DBData.m_Info.Name,
          2u);
    }
    v34 = v5->_Next;
    v26 = v5->_Next == v5;
    v5->_Next = v5;
    v5->_Prev = v5;
    if ( !v26 )
    {
      do
      {
        v35 = v34->_Next;
        operator delete(v34);
        v34 = v35;
      }
      while ( v35 != v5 );
    }
  }
  else
  {
    v25 = v5->_Next;
    v26 = v5->_Next == v5;
    v5->_Next = v5;
    v5->_Prev = v5;
    if ( !v26 )
    {
      do
      {
        v27 = v25->_Next;
        operator delete(v25);
        v25 = v27;
      }
      while ( v27 != v5 );
    }
  }
  operator delete(v5);
  return 1;
}

//----- (0045CCD0) --------------------------------------------------------
void __thiscall CThreat::HealThreat(CThreat *this, CAggresiveCreature *pHealCreature, int lThreatAmount)
{
  std::_List_nod<CAggresiveCreature *>::_Node *Myhead; // ecx
  std::_List_nod<CAggresiveCreature *>::_Node *Next; // eax
  unsigned int v5; // edx
  CAggresiveCreature **i; // esi
  CAggresiveCreature *Myval; // edi
  CAggresiveCreature **j; // edi
  CAggresiveCreature *lpHealThreaten[30]; // [esp+10h] [ebp-78h] BYREF

  Myhead = this->m_ThreatenedList._Myhead;
  Next = Myhead->_Next;
  v5 = 0;
  for ( i = lpHealThreaten; Next != Myhead; ++v5 )
  {
    if ( v5 >= 0x1E )
      break;
    Myval = Next->_Myval;
    Next = Next->_Next;
    *i++ = Myval;
  }
  for ( j = lpHealThreaten; j != i; ++j )
  {
    if ( *j )
      CThreat::AddToThreatList(&(*j)->m_Threat, pHealCreature, lThreatAmount);
  }
}

//----- (0045CD40) --------------------------------------------------------
char __thiscall Item::CEquipment::AddSocket(Item::CEquipment *this, unsigned __int8 cSocket)
{
  unsigned __int8 m_cSocketNum; // al
  unsigned __int8 v3; // dl
  unsigned __int8 *v4; // eax

  m_cSocketNum = this->m_cSocketNum;
  if ( m_cSocketNum >= this->m_cMaxSocket )
    return 0;
  v3 = this->m_cSocket[m_cSocketNum];
  v4 = &this->m_cSocket[m_cSocketNum];
  if ( v3 )
    return 0;
  *v4 = cSocket;
  ++this->m_cSocketNum;
  return 1;
}

//----- (0045CD80) --------------------------------------------------------
void __thiscall Item::CEquipment::InitializeAttribute(Item::CEquipment *this)
{
  int v1; // ebx
  __int16 *v2; // eax
  int v3; // [esp+10h] [ebp-Ch]

  v1 = 508 - (_DWORD)this;
  v2 = &this->m_usAttribute[1];
  v3 = 7;
  while ( 1 )
  {
    *(v2 - 1) = *(__int16 *)((char *)v2 + (unsigned int)this->m_ItemInfo + 502 - (_DWORD)this);
    *v2 = *(__int16 *)((char *)v2 + (unsigned int)this->m_ItemInfo + 504 - (_DWORD)this);
    v2[1] = *(__int16 *)((char *)v2 + (unsigned int)this->m_ItemInfo + 506 - (_DWORD)this);
    v2[2] = *(__int16 *)((char *)v2 + (unsigned int)this->m_ItemInfo + v1);
    v2[3] = *(unsigned __int16 *)((char *)&this->m_ItemInfo->m_usProtoTypeID + 510 - (_DWORD)this + (unsigned int)v2);
    v2 += 5;
    if ( !--v3 )
      break;
    v1 = 508 - (_DWORD)this;
  }
}

//----- (0045CE20) --------------------------------------------------------
void __thiscall Item::CEquipment::ApplyGemAttribute(Item::CEquipment *this, Item::CEquipment::ApplyType eApplyType)
{
  Item::CEquipment *i; // edx
  __int16 v3; // ax
  __int16 *v4; // esi

  for ( i = (Item::CEquipment *)this->m_SocketAttribute;
        i != (Item::CEquipment *)this->m_UpgradeAttribute;
        *v4 += eApplyType * (v3 >> 6) )
  {
    v3 = (__int16)i->__vftable;
    i = (Item::CEquipment *)((char *)i + 2);
    v4 = &this->m_usAttribute[v3 & 0x3F];
  }
}

//----- (0045CE60) --------------------------------------------------------
void __thiscall Item::CEquipment::ApplyUpgradeAttribute(Item::CEquipment *this, Item::CEquipment::ApplyType eApplyType)
{
  Item::CEquipment *i; // edx
  __int16 v3; // ax
  __int16 *v4; // esi

  for ( i = (Item::CEquipment *)this->m_UpgradeAttribute;
        i != (Item::CEquipment *)&this->m_usRuneSocket;
        *v4 += eApplyType * (v3 >> 6) )
  {
    v3 = (__int16)i->__vftable;
    i = (Item::CEquipment *)((char *)i + 2);
    v4 = &this->m_usAttribute[v3 & 0x3F];
  }
}

//----- (0045CEA0) --------------------------------------------------------
void __thiscall Item::CItem::CItem(Item::CItem *this, unsigned __int64 dwItemUID, const Item::ItemInfo *itemInfo)
{
  const Item::ItemInfo *m_ItemInfo; // ecx

  this->m_ItemInfo = itemInfo;
  this->__vftable = (Item::CItem_vtbl *)&Item::CItem::`vftable';
  this->m_ItemData.m_ItemPos = 0;
  this->m_ItemData.m_cNumOrDurability = 0;
  this->m_ItemData.m_usProtoTypeID = 0;
  this->m_ItemData.m_dwUID = 0LL;
  this->m_ItemData.m_cItemSize = 14;
  this->m_itemPos_Real = 0;
  this->m_ItemData.m_dwUID = dwItemUID;
  this->m_ItemData.m_usProtoTypeID = itemInfo->m_usProtoTypeID;
  m_ItemInfo = this->m_ItemInfo;
  this->m_ItemData.m_cItemSize = 14;
  this->m_ItemData.m_ItemPos = 0;
  this->m_ItemData.m_cNumOrDurability = m_ItemInfo->m_DetailData.m_cDefaultDurabilityOrStack;
  this->m_cMaxNumOrDurability = m_ItemInfo->m_DetailData.m_cMaxDurabilityOrStack;
  this->m_dwStallPrice = 0;
  this->m_dwPrice = m_ItemInfo->m_DetailData.m_dwPrice;
}
// 4E3AC8: using guessed type void *Item::CItem::`vftable';

//----- (0045CF10) --------------------------------------------------------
char __thiscall Item::CItem::SerializeOut(
        Item::CItem *this,
        char *lpSerializeItem_Out,
        unsigned int *nBufferLength_InOut)
{
  if ( *nBufferLength_InOut < 0xE )
  {
    *nBufferLength_InOut = 0;
    return 0;
  }
  else
  {
    this->m_ItemData.m_cItemSize = 14;
    *nBufferLength_InOut = 14;
    *(Item::ItemData *)lpSerializeItem_Out = this->m_ItemData;
    return 1;
  }
}

//----- (0045CF60) --------------------------------------------------------
char __thiscall Item::CItem::SerializeIn(
        Item::CItem *this,
        const char *lpSerializeItem_In,
        unsigned int *nBufferLength_InOut)
{
  if ( *nBufferLength_InOut >= 0xE
    && (*nBufferLength_InOut = *((unsigned __int8 *)lpSerializeItem_In + 12),
        *((_WORD *)lpSerializeItem_In + 4) == this->m_ItemInfo->m_usProtoTypeID) )
  {
    this->m_ItemData = *(Item::ItemData *)lpSerializeItem_In;
    this->m_itemPos_Real = this->m_ItemData.m_ItemPos;
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CItem::SerializeIn",
      aDWorkRylSource_65,
      90,
      aSerializein,
      14,
      4,
      *((unsigned __int16 *)lpSerializeItem_In + 4),
      this->m_ItemInfo->m_usProtoTypeID);
    return 0;
  }
}

//----- (0045CFE0) --------------------------------------------------------
char __thiscall Item::CEquipment::SerializeOut(
        Item::CEquipment *this,
        char *lpSerializeItem_Out,
        unsigned int *nBufferLength_InOut)
{
  char *v3; // edi
  unsigned int v4; // edx
  unsigned int v5; // ebp
  unsigned __int8 v6; // al
  Item::CEquipment *m_SocketAttribute; // edx
  __int16 v8; // ax
  Item::CEquipment *i; // edx
  __int16 v10; // ax
  unsigned int v11; // eax
  int v12; // edx
  char *v13; // esi
  __int16 *v14; // ebp
  char *v15; // ebx
  char *v16; // ebx
  char *v17; // ebx
  unsigned int v18; // edx
  char *v19; // ebx
  Item::ItemAttribute *v20; // ebx
  Item::ItemAttribute *j; // esi
  __int16 v22; // dx
  unsigned __int16 *k; // ebp
  __int16 v24; // dx
  unsigned __int8 v25; // dl
  unsigned int nIndex; // [esp+0h] [ebp-1Ch]
  char nSocketIndex; // [esp+4h] [ebp-18h]
  int v29; // [esp+14h] [ebp-8h]

  if ( *nBufferLength_InOut < 0x34 )
  {
    *nBufferLength_InOut = 0;
    return 0;
  }
  else
  {
    v3 = lpSerializeItem_Out;
    *(Item::ItemData *)lpSerializeItem_Out = this->m_ItemData;
    v4 = 0;
    v5 = 0;
    do
    {
      if ( v5 >= this->m_cMaxSocket )
        break;
      v6 = this->m_cSocket[v4];
      if ( v6 )
        lpSerializeItem_Out[v5++ + 20] = v6;
      ++v4;
    }
    while ( v4 < 8 );
    m_SocketAttribute = (Item::CEquipment *)this->m_SocketAttribute;
    for ( nSocketIndex = v5;
          m_SocketAttribute != (Item::CEquipment *)this->m_UpgradeAttribute;
          this->m_usAttribute[v8 & 0x3F] -= v8 >> 6 )
    {
      v8 = (__int16)m_SocketAttribute->__vftable;
      m_SocketAttribute = (Item::CEquipment *)((char *)m_SocketAttribute + 2);
    }
    for ( i = (Item::CEquipment *)this->m_UpgradeAttribute;
          i != (Item::CEquipment *)&this->m_usRuneSocket;
          this->m_usAttribute[v10 & 0x3F] -= v10 >> 6 )
    {
      v10 = (__int16)i->__vftable;
      i = (Item::CEquipment *)((char *)i + 2);
    }
    v29 = 508 - (_DWORD)this;
    v11 = 0;
    v12 = 502 - (_DWORD)this;
    v13 = &lpSerializeItem_Out[v5 + 20];
    nIndex = 0;
    v14 = &this->m_usAttribute[1];
    while ( v11 < this->m_cMaxAttribute )
    {
      v15 = (char *)this->m_ItemInfo + v12;
      if ( *(v14 - 1) != *(__int16 *)((char *)v14 + (_DWORD)v15) )
      {
        *(_WORD *)v13 = ((*(v14 - 1) - *(__int16 *)((char *)v14 + (_DWORD)v15)) << 6) | nIndex & 0x3F;
        ++v11;
        v13 += 2;
      }
      if ( v11 >= this->m_cMaxAttribute )
        break;
      v16 = (char *)this->m_ItemInfo + 504 - (_DWORD)this;
      if ( *v14 != *(__int16 *)((char *)v14 + (_DWORD)v16) )
      {
        *(_WORD *)v13 = ((*v14 - *(__int16 *)((char *)v14 + (_DWORD)v16)) << 6) | ((_BYTE)nIndex + 1) & 0x3F;
        ++v11;
        v13 += 2;
      }
      if ( v11 >= this->m_cMaxAttribute )
        break;
      v17 = (char *)this->m_ItemInfo + 506 - (_DWORD)this;
      if ( v14[1] != *(__int16 *)((char *)v14 + (_DWORD)v17) )
      {
        *(_WORD *)v13 = ((v14[1] - *(__int16 *)((char *)v14 + (_DWORD)v17)) << 6) | ((_BYTE)nIndex + 2) & 0x3F;
        ++v11;
        v13 += 2;
      }
      if ( v11 >= this->m_cMaxAttribute )
        break;
      v18 = nIndex;
      if ( v14[2] != *(unsigned __int16 *)((char *)&this->m_ItemInfo->m_usProtoTypeID + v29 + (unsigned int)v14) )
      {
        *(_WORD *)v13 = ((v14[2]
                        - *(unsigned __int16 *)((char *)&this->m_ItemInfo->m_usProtoTypeID + v29 + (unsigned int)v14)) << 6) | ((_BYTE)nIndex + 3) & 0x3F;
        ++v11;
        v13 += 2;
      }
      if ( v11 >= this->m_cMaxAttribute )
        goto LABEL_29;
      v19 = (char *)this->m_ItemInfo + 510 - (_DWORD)this;
      if ( v14[3] != *(__int16 *)((char *)v14 + (_DWORD)v19) )
      {
        *(_WORD *)v13 = ((v14[3] - *(__int16 *)((char *)v14 + (_DWORD)v19)) << 6) | ((_BYTE)nIndex + 4) & 0x3F;
        ++v11;
        v13 += 2;
      }
      v14 += 5;
      nIndex += 5;
      if ( v18 + 5 >= 0x23 )
      {
LABEL_29:
        v3 = lpSerializeItem_Out;
        break;
      }
      v3 = lpSerializeItem_Out;
      v12 = 502 - (_DWORD)this;
    }
    v20 = this->m_SocketAttribute;
    for ( j = this->m_UpgradeAttribute; v20 != j; this->m_usAttribute[v22 & 0x3F] += v22 >> 6 )
      v22 = (__int16)*v20++;
    for ( k = &this->m_usRuneSocket; j != (Item::ItemAttribute *)k; this->m_usAttribute[v24 & 0x3F] += v24 >> 6 )
      v24 = (__int16)*j++;
    v25 = nSocketIndex + 2 * (v11 + 10);
    v3[12] = v25;
    this->m_ItemData.m_cItemSize = v25;
    *((_WORD *)v3 + 7) ^= (*k ^ *((_WORD *)v3 + 7)) & 0x7FF;
    *((_WORD *)v3 + 7) = *((_WORD *)v3 + 7) & 0x7FF ^ ((this->m_cMaterialType
                                                      - (unsigned __int16)this->m_ItemInfo->m_DetailData.m_cMaterialType) << 11);
    v3[16] ^= (v3[16] ^ (this->m_cMaxSocket - this->m_ItemInfo->m_DetailData.m_cMaxSocketNum)) & 0x1F;
    v3[17] = nSocketIndex & 0xF | (16 * v11);
    v3[16] &= 0x1Fu;
    v3[18] = this->m_cMaxNumOrDurability - this->m_ItemInfo->m_DetailData.m_cMaxDurabilityOrStack;
    v3[19] = this->m_cReserved2;
    *nBufferLength_InOut = this->m_ItemData.m_cItemSize;
    return 1;
  }
}

//----- (0045D350) --------------------------------------------------------
StatusInfo *__thiscall Item::CEquipment::GetStatusInfo(Item::CEquipment *this, StatusInfo *result, char cTrend)
{
  char v4; // al
  __int16 v5; // dx
  char v6; // cl
  StatusInfo *v7; // eax
  StatusInfo statusInfo; // [esp+8h] [ebp-44h] BYREF

  StatusInfo::StatusInfo(&statusInfo);
  v4 = cTrend & 2;
  v5 = 0;
  if ( (cTrend & 2) != 0 )
    statusInfo.m_nCriticalType = this->m_usAttribute[1];
  else
    statusInfo.m_nCriticalType = 0;
  if ( v4 )
    statusInfo.m_nCriticalPercentage = this->m_usAttribute[2];
  else
    statusInfo.m_nCriticalPercentage = 0;
  v6 = cTrend & 1;
  if ( (cTrend & 1) != 0 )
    statusInfo.m_nMinDamage = this->m_usAttribute[3];
  else
    statusInfo.m_nMinDamage = 0;
  if ( v6 )
    statusInfo.m_nMaxDamage = this->m_usAttribute[4];
  else
    statusInfo.m_nMaxDamage = 0;
  if ( v6 )
    statusInfo.m_nOffenceRevision = this->m_usAttribute[6];
  else
    statusInfo.m_nOffenceRevision = 0;
  if ( v6 )
    statusInfo.m_fDRC = (double)this->m_usAttribute[5] * 0.1;
  else
    statusInfo.m_fDRC = 0.0;
  if ( v4 )
    statusInfo.m_nDefence = this->m_usAttribute[7];
  else
    statusInfo.m_nDefence = 0;
  if ( v4 )
    statusInfo.m_nDefenceRevision = this->m_usAttribute[8];
  else
    statusInfo.m_nDefenceRevision = 0;
  if ( v4 )
    statusInfo.m_nBlockingPercentage = this->m_usAttribute[10];
  else
    statusInfo.m_nBlockingPercentage = 0;
  if ( v6 )
    statusInfo.m_nMagicPower = this->m_usAttribute[34];
  else
    statusInfo.m_nMagicPower = 0;
  if ( v4 )
    statusInfo.m_nMagicResistance = this->m_usAttribute[9];
  else
    statusInfo.m_nMagicResistance = 0;
  if ( v4 )
    statusInfo.m_nAttackSpeed = this->m_usAttribute[11];
  else
    statusInfo.m_nAttackSpeed = 0;
  if ( v4 )
    statusInfo.m_nMoveSpeed = this->m_usAttribute[12];
  else
    statusInfo.m_nMoveSpeed = 0;
  if ( v4 )
    statusInfo.m_nAttackRange = this->m_usAttribute[13];
  else
    statusInfo.m_nAttackRange = 0;
  if ( v4 )
    statusInfo.m_nRangeAttackDistance = this->m_usAttribute[14];
  else
    statusInfo.m_nRangeAttackDistance = 0;
  if ( v4 )
    statusInfo.m_nMaxHP = 50 * this->m_usAttribute[15];
  else
    statusInfo.m_nMaxHP = 0;
  if ( v4 )
    statusInfo.m_nMaxMP = 50 * this->m_usAttribute[16];
  else
    statusInfo.m_nMaxMP = 0;
  if ( v4 )
    statusInfo.m_nHPRegenAmount = this->m_usAttribute[17];
  else
    statusInfo.m_nHPRegenAmount = 0;
  if ( v4 )
    statusInfo.m_nMPRegenAmount = this->m_usAttribute[18];
  else
    statusInfo.m_nMPRegenAmount = 0;
  if ( v6 )
    statusInfo.m_nWeaponAttributeLevel[0] = this->m_usAttribute[19];
  else
    statusInfo.m_nWeaponAttributeLevel[0] = 0;
  if ( v6 )
    statusInfo.m_nWeaponAttributeLevel[1] = this->m_usAttribute[20];
  else
    statusInfo.m_nWeaponAttributeLevel[1] = 0;
  if ( v6 )
    statusInfo.m_nWeaponAttributeLevel[2] = this->m_usAttribute[21];
  else
    statusInfo.m_nWeaponAttributeLevel[2] = 0;
  if ( v6 )
    statusInfo.m_nWeaponAttributeLevel[3] = this->m_usAttribute[22];
  else
    statusInfo.m_nWeaponAttributeLevel[3] = 0;
  if ( v6 )
    statusInfo.m_nWeaponAttributeLevel[4] = this->m_usAttribute[23];
  else
    statusInfo.m_nWeaponAttributeLevel[4] = 0;
  if ( v4 )
    statusInfo.m_nAttributeResistance[0] = this->m_usAttribute[24];
  else
    statusInfo.m_nAttributeResistance[0] = 0;
  if ( v4 )
    statusInfo.m_nAttributeResistance[1] = this->m_usAttribute[25];
  else
    statusInfo.m_nAttributeResistance[1] = 0;
  if ( v4 )
    statusInfo.m_nAttributeResistance[2] = this->m_usAttribute[26];
  else
    statusInfo.m_nAttributeResistance[2] = 0;
  if ( v4 )
    statusInfo.m_nAttributeResistance[3] = this->m_usAttribute[27];
  else
    statusInfo.m_nAttributeResistance[3] = 0;
  if ( v4 )
    v5 = this->m_usAttribute[28];
  v7 = result;
  statusInfo.m_nAttributeResistance[4] = v5;
  qmemcpy(result, &statusInfo, sizeof(StatusInfo));
  return v7;
}

//----- (0045D5F0) --------------------------------------------------------
int __thiscall Item::CEquipment::CheckPassiveType(Item::CEquipment *this, unsigned __int16 usSkillType)
{
  int result; // eax

  if ( usSkillType == 0x9107 )
    return 0;
  switch ( this->m_ItemInfo->m_DetailData.m_cItemType )
  {
    case 6u:
    case 0xBu:
      if ( usSkillType != 0x8102 )
        goto LABEL_17;
      result = 1;
      break;
    case 7u:
    case 9u:
      if ( usSkillType != 0x8105 )
        goto LABEL_17;
      result = 1;
      break;
    case 8u:
    case 0xAu:
      if ( usSkillType != 0x8103 )
        goto LABEL_17;
      result = 1;
      break;
    case 0xFu:
      if ( usSkillType != 0x8705 )
        goto LABEL_17;
      result = 1;
      break;
    case 0x21u:
    case 0x23u:
    case 0x24u:
      if ( usSkillType != 0x9102 )
        goto LABEL_17;
      result = 1;
      break;
    case 0x22u:
    case 0x25u:
      if ( usSkillType != 0x9105 )
        goto LABEL_17;
      result = 1;
      break;
    case 0x26u:
      result = 1;
      if ( usSkillType != 0x9207 )
        goto LABEL_17;
      break;
    default:
LABEL_17:
      result = 2;
      break;
  }
  return result;
}

//----- (0045D6D0) --------------------------------------------------------
void __thiscall Item::CUseItem::CUseItem(
        Item::CUseItem *this,
        unsigned __int64 dwItemUID,
        const Item::ItemInfo *itemInfo)
{
  Item::CItem::CItem(this, dwItemUID, itemInfo);
  this->__vftable = (Item::CUseItem_vtbl *)&Item::CUseItem::`vftable';
}
// 4E3BF8: using guessed type void *Item::CUseItem::`vftable';

//----- (0045D700) --------------------------------------------------------
void __thiscall Item::CUseItem::~CUseItem(Item::CUseItem *this)
{
  this->__vftable = (Item::CUseItem_vtbl *)&Item::CItem::`vftable';
}
// 4E3AC8: using guessed type void *Item::CItem::`vftable';

//----- (0045D710) --------------------------------------------------------
char __thiscall Item::CUseItem::UsePotion(Item::CUseItem *this, CCharacter *lpCharacter)
{
  const Item::ItemInfo *m_ItemInfo; // eax
  char v3; // dl
  CCharacter_vtbl *v4; // edx
  AtType v5; // edi
  unsigned __int16 v6; // ax
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  unsigned __int8 cOffencerJudge; // [esp+Fh] [ebp-Dh] BYREF
  AtType attackType; // [esp+10h] [ebp-Ch]
  int wError; // [esp+14h] [ebp-8h] BYREF
  unsigned __int8 cDefenserJudge[4]; // [esp+18h] [ebp-4h] BYREF

  m_ItemInfo = this->m_ItemInfo;
  v3 = *((_BYTE *)&attackType + 2) & 0xF | (16 * LOBYTE(m_ItemInfo->m_UseItemInfo.m_usSkill_LockCount));
  attackType.m_wType = m_ItemInfo->m_UseItemInfo.m_usSkill_ID;
  *((_BYTE *)&attackType + 2) = v3;
  v4 = lpCharacter->__vftable;
  attackType.m_cSkillLevel = 0;
  v5 = attackType;
  cOffencerJudge = 0;
  cDefenserJudge[0] = 0;
  wError = 0;
  v6 = v4->ApplyDamage(
         lpCharacter,
         attackType,
         lpCharacter,
         &cOffencerJudge,
         cDefenserJudge,
         (unsigned __int16 *)&wError);
  m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    GameClientSendPacket::SendCharAttacked(
      &m_lpGameClientDispatch->m_SendStream,
      lpCharacter,
      lpCharacter,
      v5,
      0.0,
      v6,
      cDefenserJudge[0],
      wError);
  return 1;
}

//----- (0045D7A0) --------------------------------------------------------
double __cdecl _Pow_int<float>(float _X, int _Y)
{
  unsigned int v2; // eax
  double result; // st7
  double i; // st6

  v2 = _Y;
  if ( _Y < 0 )
    v2 = -_Y;
  result = 1.0;
  for ( i = _X; ; i = i * i )
  {
    if ( (v2 & 1) != 0 )
      result = result * i;
    v2 >>= 1;
    if ( !v2 )
      break;
  }
  if ( _Y < 0 )
    return 1.0 / result;
  return result;
}

//----- (0045D7E0) --------------------------------------------------------
void __thiscall Item::CEquipment::InitializeGemAttribute(Item::CEquipment *this)
{
  *(_DWORD *)this->m_SocketAttribute = 0;
  *(_DWORD *)&this->m_SocketAttribute[2] = 0;
  *(_DWORD *)&this->m_SocketAttribute[4] = 0;
  *(_DWORD *)&this->m_SocketAttribute[6] = 0;
  *(_DWORD *)&this->m_SocketAttribute[8] = 0;
  *(_DWORD *)&this->m_SocketAttribute[10] = 0;
  *(_DWORD *)&this->m_SocketAttribute[12] = 0;
  *(_DWORD *)&this->m_SocketAttribute[14] = 0;
  Item::CItemType::GetInstallGemAttribute(
    this->m_cSocket,
    this->m_SocketAttribute,
    (Item::ItemType::Type)this->m_ItemInfo->m_DetailData.m_cItemType,
    this->m_cSocketNum,
    0x10u);
}

//----- (0045D830) --------------------------------------------------------
void __thiscall Item::CEquipment::InitializeUpgradeAttribute(Item::CEquipment *this)
{
  *(_DWORD *)this->m_UpgradeAttribute = 0;
  Item::CItemType::GetUpgradeItemAttribute(
    (Item::ItemType::Type)this->m_ItemInfo->m_DetailData.m_cItemType,
    this->m_UpgradeAttribute,
    this->m_cMaterialType);
}

//----- (0045D860) --------------------------------------------------------
Item::CItem *__thiscall Item::CItem::`vector deleting destructor'(Item::CItem *this, char a2)
{
  this->__vftable = (Item::CItem_vtbl *)&Item::CItem::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4E3AC8: using guessed type void *Item::CItem::`vftable';

//----- (0045D880) --------------------------------------------------------
void __thiscall Item::CEquipment::CEquipment(
        Item::CEquipment *this,
        unsigned __int64 dwItemUID,
        const Item::ItemInfo *itemInfo)
{
  Item::ItemAttribute *m_SocketAttribute; // eax
  int v5; // edx
  const Item::ItemInfo *m_ItemInfo; // eax
  unsigned __int8 m_cMaxSocket; // al

  Item::CItem::CItem(this, dwItemUID, itemInfo);
  this->__vftable = (Item::CEquipment_vtbl *)&Item::CEquipment::`vftable';
  m_SocketAttribute = this->m_SocketAttribute;
  v5 = 16;
  do
  {
    *m_SocketAttribute++ = 0;
    --v5;
  }
  while ( v5 );
  this->m_UpgradeAttribute[0] = 0;
  this->m_UpgradeAttribute[1] = 0;
  m_ItemInfo = this->m_ItemInfo;
  this->m_usRuneSocket = 0;
  this->m_cMaterialType = itemInfo->m_DetailData.m_cMaterialType;
  this->m_cSocketNum = 0;
  this->m_cReserved = 0;
  this->m_cReserved2 = 0;
  if ( m_ItemInfo->m_DetailData.m_cXSize == 1 && m_ItemInfo->m_DetailData.m_cYSize == 1 )
  {
    this->m_cMaxSocket = 6;
    this->m_cMaxAttribute = 6;
  }
  else
  {
    this->m_cMaxSocket = 8;
    this->m_cMaxAttribute = 12;
  }
  m_cMaxSocket = this->m_cMaxSocket;
  if ( itemInfo->m_DetailData.m_cMaxSocketNum < m_cMaxSocket )
    m_cMaxSocket = itemInfo->m_DetailData.m_cMaxSocketNum;
  this->m_cMaxSocket = m_cMaxSocket;
  this->m_ItemData.m_cItemSize += 6;
  *(_DWORD *)this->m_cSocket = 0;
  *(_DWORD *)&this->m_cSocket[4] = 0;
  Item::CEquipment::InitializeAttribute(this);
}
// 4E3C04: using guessed type void *Item::CEquipment::`vftable';

//----- (0045D950) --------------------------------------------------------
Item::CUseItem *__thiscall Item::CEquipment::`vector deleting destructor'(Item::CUseItem *this, char a2)
{
  Item::CUseItem::~CUseItem(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0045D970) --------------------------------------------------------
int __thiscall Item::CEquipment::InstallSocket(Item::CEquipment *this, Item::CItem *Gem)
{
  unsigned __int8 m_cMaxSocket; // al
  unsigned __int16 m_usProtoTypeID; // bx

  m_cMaxSocket = this->m_cMaxSocket;
  if ( m_cMaxSocket >= 8u || m_cMaxSocket <= this->m_cSocketNum )
    return 8;
  m_usProtoTypeID = Gem->m_ItemData.m_usProtoTypeID;
  if ( m_usProtoTypeID < 0x76Du || m_usProtoTypeID > 0x77Bu )
    return 5;
  if ( !Gem->m_ItemData.m_cNumOrDurability )
    return 3;
  Item::CEquipment::ApplyGemAttribute(this, REMOVE);
  Item::CEquipment::AddSocket(this, m_usProtoTypeID - 108);
  Item::CEquipment::InitializeGemAttribute(this);
  Item::CEquipment::ApplyGemAttribute(this, APPLY);
  --Gem->m_ItemData.m_cNumOrDurability;
  return 0;
}

//----- (0045DA00) --------------------------------------------------------
int __thiscall Item::CEquipment::UpgradeItem(
        Item::CEquipment *this,
        Item::CItem *Mineral_InOut,
        unsigned int dwCurrentGold_In,
        unsigned int *dwUsedGold_Out)
{
  unsigned __int64 v7; // rax
  unsigned int v8; // ecx
  __int16 v9; // di
  int v10; // ecx
  unsigned __int8 v11; // al
  double m_dwPrice; // st7
  unsigned __int8 v13; // bl
  const Item::ItemInfo *m_ItemInfo; // edi
  int m_cMaterialType; // [esp-8h] [ebp-14h]
  bool dwUsedGold_Outa; // [esp+18h] [ebp+Ch]

  *dwUsedGold_Out = 0;
  if ( this->m_cMaterialType >= 0xFu )
    return 12;
  dwUsedGold_Outa = Item::CItemType::IsCorrectItemType(
                      CSingleton<Item::CItemType>::ms_pSingleton,
                      UPGRADE_SOCKET_ONLY,
                      this->m_ItemInfo->m_DetailData.m_cItemType);
  if ( dwUsedGold_Outa && this->m_cMaterialType >= 5u )
    return 12;
  v7 = (unsigned __int64)((double)this->m_cMaxNumOrDurability * 0.0099999998 * (double)this->m_dwPrice * 0.5);
  if ( dwCurrentGold_In < (unsigned int)v7 )
    return 13;
  switch ( Mineral_InOut->m_ItemData.m_usProtoTypeID )
  {
    case 0x7D1u:
      v8 = 7 * this->m_cMaterialType;
      v9 = s_UpgradeTable[v8 / 7][0];
      break;
    case 0x7D2u:
      v8 = 7 * this->m_cMaterialType;
      v9 = word_4E3C1A[v8];
      break;
    case 0x7D3u:
      v8 = 7 * this->m_cMaterialType;
      v9 = word_4E3C1C[v8];
      break;
    default:
      return 6;
  }
  if ( !v9 )
    return 6;
  v10 = Mineral_InOut->m_ItemData.m_cNumOrDurability - (unsigned __int16)word_4E3C24[v8];
  if ( v10 < 0 )
    return 7;
  Mineral_InOut->m_ItemData.m_cNumOrDurability = v10;
  *dwUsedGold_Out = v7;
  v11 = Math::Random::ComplexRandom(100, 0);
  if ( (unsigned __int16)word_4E3C20[7 * this->m_cMaterialType] > v11 )
    return 14;
  if ( (unsigned __int16)v9 < (unsigned __int8)(100 - v11) )
  {
    Item::CEquipment::ApplyUpgradeAttribute(this, REMOVE);
    this->m_cMaterialType = 0;
    Item::CEquipment::InitializeUpgradeAttribute(this);
    Item::CEquipment::ApplyGemAttribute(this, REMOVE);
    this->m_cSocketNum = 0;
    *(_DWORD *)this->m_cSocket = 0;
    *(_DWORD *)&this->m_cSocket[4] = 0;
    Item::CEquipment::InitializeGemAttribute(this);
    m_ItemInfo = this->m_ItemInfo;
    m_cMaterialType = this->m_cMaterialType;
    this->m_cMaxSocket = m_ItemInfo->m_DetailData.m_cMaxSocketNum;
    this->m_dwPrice = (unsigned __int64)(_Pow_int<float>(1.1, m_cMaterialType)
                                       * (double)m_ItemInfo->m_DetailData.m_dwPrice);
    return 0;
  }
  else
  {
    m_dwPrice = (double)this->m_dwPrice;
    v13 = this->m_cMaterialType + 1;
    this->m_cMaterialType = v13;
    this->m_dwPrice = (unsigned __int64)(m_dwPrice * 1.1);
    if ( (v13 & 1) == 0 || dwUsedGold_Outa )
    {
      ++this->m_cMaxSocket;
      return 0;
    }
    else
    {
      Item::CEquipment::ApplyUpgradeAttribute(this, REMOVE);
      Item::CEquipment::InitializeUpgradeAttribute(this);
      Item::CEquipment::ApplyUpgradeAttribute(this, APPLY);
      return 0;
    }
  }
}
// 4E3C1A: using guessed type __int16 word_4E3C1A[];
// 4E3C1C: using guessed type __int16 word_4E3C1C[];
// 4E3C20: using guessed type __int16 word_4E3C20[];
// 4E3C24: using guessed type __int16 word_4E3C24[];

//----- (0045DC40) --------------------------------------------------------
void __thiscall Item::CEquipment::AddRandomOption(Item::CEquipment *this, unsigned __int8 cBaseLevel, bool bBadOption)
{
  unsigned __int8 v4; // bl
  const Item::ItemInfo *m_ItemInfo; // eax
  unsigned __int8 m_cOptionLimit; // dl
  unsigned __int8 *p_m_cOptionLimit; // eax
  unsigned int v8; // eax
  int v9; // edi
  int v10; // esi
  int v11; // ecx
  int v12; // ebx
  unsigned __int8 v13; // al
  char v14; // al
  int v15; // ecx
  unsigned __int8 m_cItemType; // al
  int v17; // eax
  __int16 v18; // bx
  int v19; // edi
  int v20; // esi
  unsigned int v21; // eax
  __int16 *v22; // ecx
  int nAttributeTypeIndex; // [esp+8h] [ebp-8h]
  int v24; // [esp+Ch] [ebp-4h]

  v4 = Item::CItemType::ConvertRandomOptionType(
         CSingleton<Item::CItemType>::ms_pSingleton,
         this->m_ItemInfo->m_DetailData.m_cItemType);
  if ( v4 != 7 )
  {
    m_ItemInfo = this->m_ItemInfo;
    m_cOptionLimit = m_ItemInfo->m_DetailData.m_cOptionLimit;
    p_m_cOptionLimit = &m_ItemInfo->m_DetailData.m_cOptionLimit;
    if ( m_cOptionLimit >= cBaseLevel )
      p_m_cOptionLimit = &cBaseLevel;
    cBaseLevel = *p_m_cOptionLimit;
    if ( !bBadOption || (v8 = Math::Random::ComplexRandom(100, 0), bBadOption = 1, v8 >= 0x46) )
      bBadOption = 0;
    v9 = v4;
    v10 = v4;
    v11 = aryEquipAdditionalValue[0][v10][5];
    v12 = aryEquipAdditionalValue[0][v10][0];
    v24 = v9;
    if ( bBadOption )
    {
      v11 += -50 * v11 / 100;
      v12 += -50 * v12 / 100;
    }
    v13 = Math::Random::ComplexRandom(v12 + v11 + 1, 0) - v12 + this->m_cMaxNumOrDurability;
    this->m_cMaxNumOrDurability = v13;
    v14 = Math::Random::ComplexRandom(v13, 0);
    v15 = 0;
    this->m_ItemData.m_cNumOrDurability = *(_BYTE *)(v10 * 12 + 5125764) + v14;
    nAttributeTypeIndex = 0;
    while ( 1 )
    {
      if ( v15 != 9
        || (m_cItemType = this->m_ItemInfo->m_DetailData.m_cItemType, m_cItemType != 12) && m_cItemType != 13 )
      {
        v17 = v9 + 7 * v15;
        v18 = aryEquipAdditionalValue[0][v17][5];
        if ( v18 )
        {
          v19 = v18 + cBaseLevel * aryEquipAdditionalValue[0][v17][3] / aryEquipAdditionalValue[0][v17][4];
          v20 = aryEquipAdditionalValue[0][v17][0]
              + cBaseLevel * aryEquipAdditionalValue[0][v17][1] / aryEquipAdditionalValue[0][v17][2];
          if ( bBadOption )
          {
            v19 += -50 * v19 / 100;
            v20 += -50 * v20 / 100;
          }
          v21 = Math::Random::ComplexRandom(v20 + v19 + 1, 0) - v20;
          switch ( nAttributeTypeIndex )
          {
            case 2:
              Item::CEquipment::AddAttribute(this, COMMON, v21);
              break;
            case 3:
              Item::CEquipment::AddAttribute(this, LEAVE_WAIT, v21);
              break;
            case 4:
              Item::CEquipment::AddAttribute(this, MAX_TITLE, v21);
              break;
            case 5:
              Item::CEquipment::AddAttribute(this, MAX_TITLE|MASTER, v21);
              break;
            case 6:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)8, v21);
              break;
            case 7:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)9, v21);
              break;
            case 8:
              Item::CEquipment::AddAttribute(this, MIDDLE_ADMIN, v21);
              break;
            case 9:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)10, v21);
              break;
            case 10:
              Item::CEquipment::AddAttribute(this, MAX_TITLE|MASTER|0x8, v21);
              break;
            case 11:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)16, v21);
              break;
            case 12:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)17, v21);
              break;
            case 13:
              Item::CEquipment::AddAttribute(this, (Item::Attribute::Type)18, v21);
              break;
            default:
              break;
          }
        }
      }
      v15 = ++nAttributeTypeIndex;
      if ( nAttributeTypeIndex >= 14 )
        break;
      v9 = v24;
    }
    v22 = &this->m_usAttribute[3];
    if ( this->m_usAttribute[3] < this->m_usAttribute[4] )
      v22 = &this->m_usAttribute[4];
    this->m_usAttribute[4] = *v22;
  }
}

//----- (0045DEB0) --------------------------------------------------------
bool __thiscall Item::CUseItem::Use(Item::CUseItem *this, CCharacter *lpCharacter)
{
  const Item::ItemInfo *m_ItemInfo; // eax
  void *v4; // eax
  unsigned int Gold; // ebx
  unsigned __int8 m_cNumOrDurability; // al
  bool result; // al

  if ( !lpCharacter->m_CreatureStatus.m_nNowHP )
    return 0;
  m_ItemInfo = this->m_ItemInfo;
  switch ( m_ItemInfo->m_DetailData.m_cItemType )
  {
    case 0x14u:
    case 0x34u:
      if ( Item::CUseItem::UsePotion(this, lpCharacter) )
        goto $L107543;
      v4 = &unk_4E3DF8;
      goto LABEL_27;
    case 0x15u:
    case 0x16u:
      goto $L107543;
    case 0x17u:
      if ( CCharacter::SkillCreate(lpCharacter, this) )
        goto $L107543;
      v4 = &unk_4E3DF0;
      goto LABEL_27;
    case 0x1Bu:
      if ( CCharacter::UseAmmo(lpCharacter, this) )
        goto $L107543;
      v4 = &unk_4E3DE8;
      goto LABEL_27;
    case 0x2Fu:
      if ( CCharacter::MovePos(lpCharacter, m_ItemInfo->m_UseItemInfo.m_Pos, m_ItemInfo->m_UseItemInfo.m_cZone, 0) )
        goto $L107543;
      v4 = &unk_4E3DE0;
      goto LABEL_27;
    case 0x31u:
      if ( CCharacter::UseLottery(lpCharacter, this->m_ItemData.m_usProtoTypeID, *(_WORD *)&this->m_ItemData.m_ItemPos) )
        goto $L107543;
      v4 = &unk_4E3DD8;
      goto LABEL_27;
    case 0x32u:
      if ( CCharacter::IncrementExp(lpCharacter, m_ItemInfo->m_UseItemInfo.m_dwAmount) )
        goto $L107543;
      v4 = &unk_4E3DCC;
      goto LABEL_27;
    case 0x33u:
      Gold = lpCharacter->m_DBData.m_Info.Gold;
      if ( CCharacter::AddGold(lpCharacter, m_ItemInfo->m_UseItemInfo.m_dwAmount, 1) )
      {
        GAMELOG::LogTakeGold(
          lpCharacter,
          Gold,
          lpCharacter->m_DBData.m_Info.Gold,
          this->m_ItemInfo->m_UseItemInfo.m_dwAmount,
          2u,
          2u,
          3u,
          0);
      }
      else
      {
        v4 = &unk_4E3DC4;
LABEL_27:
        if ( v4 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CUseItem::Use",
            aDWorkRylSource_65,
            813,
            aCid0x08xS,
            lpCharacter->m_dwCID,
            v4,
            this->m_ItemData.m_usProtoTypeID,
            this->m_ItemInfo->m_UseItemInfo.m_usSkill_ID,
            this->m_ItemInfo->m_UseItemInfo.m_usSkill_LockCount);
          return 0;
        }
      }
$L107543:
      m_cNumOrDurability = this->m_ItemData.m_cNumOrDurability;
      if ( m_cNumOrDurability )
        this->m_ItemData.m_cNumOrDurability = m_cNumOrDurability - 1;
      result = 1;
      break;
    case 0x35u:
      if ( CCharacter::UseStartKit(lpCharacter, 0x14D9u) )
        goto $L107543;
      v4 = &unk_4E3DB4;
      goto LABEL_27;
    case 0x36u:
      if ( CCharacter::UseStartKit(lpCharacter, 0x159Au) )
        goto $L107543;
      v4 = &unk_4E3D98;
      goto LABEL_27;
    case 0x37u:
      if ( CCharacter::UseStartKit(lpCharacter, 0x15CAu) )
        goto $L107543;
      v4 = &unk_4E3D7C;
      goto LABEL_27;
    case 0x38u:
      if ( CCharacter::UseStartKit(lpCharacter, 0x15FAu) )
        goto $L107543;
      v4 = &unk_4E3D6C;
      goto LABEL_27;
    default:
      v4 = &unk_4E3D64;
      goto LABEL_27;
  }
  return result;
}

//----- (0045E130) --------------------------------------------------------
char __thiscall Item::CEquipment::SerializeIn(
        Item::CEquipment *this,
        const char *lpSerializeItem_In,
        unsigned int *nBufferLength_InOut)
{
  const Item::ItemInfo *m_ItemInfo; // eax
  int m_cSocketNum; // ebx
  const char *v6; // ebx
  Item::CEquipment *m_UpgradeAttribute; // ebp
  const char *v8; // edi
  __int16 *v9; // ecx
  __int16 v10; // ax
  Item::ItemAttribute *i; // ecx
  __int16 v12; // ax
  __int16 v13; // ax
  unsigned int m_cMaterialType; // eax
  double v15; // st7
  double j; // st6

  if ( !Item::CItem::SerializeIn(this, lpSerializeItem_In, nBufferLength_InOut) )
    return 0;
  m_ItemInfo = this->m_ItemInfo;
  this->m_usRuneSocket = *((_WORD *)lpSerializeItem_In + 7) & 0x7FF;
  this->m_cMaterialType = m_ItemInfo->m_DetailData.m_cMaterialType + (*((__int16 *)lpSerializeItem_In + 7) >> 11);
  this->m_cMaxSocket = m_ItemInfo->m_DetailData.m_cMaxSocketNum + ((char)(8 * lpSerializeItem_In[16]) >> 3);
  this->m_cSocketNum = lpSerializeItem_In[17] & 0xF;
  m_cSocketNum = this->m_cSocketNum;
  this->m_cReserved = (unsigned __int8)lpSerializeItem_In[16] >> 5;
  this->m_cMaxNumOrDurability = lpSerializeItem_In[18] + m_ItemInfo->m_DetailData.m_cMaxDurabilityOrStack;
  this->m_cReserved2 = lpSerializeItem_In[19];
  v6 = &lpSerializeItem_In[m_cSocketNum + 20];
  memmove(this->m_cSocket, (unsigned __int8 *)lpSerializeItem_In + 20, v6 - (lpSerializeItem_In + 20));
  Item::CEquipment::InitializeAttribute(this);
  *(_DWORD *)this->m_SocketAttribute = 0;
  *(_DWORD *)&this->m_SocketAttribute[2] = 0;
  *(_DWORD *)&this->m_SocketAttribute[4] = 0;
  *(_DWORD *)&this->m_SocketAttribute[6] = 0;
  *(_DWORD *)&this->m_SocketAttribute[8] = 0;
  *(_DWORD *)&this->m_SocketAttribute[10] = 0;
  *(_DWORD *)&this->m_SocketAttribute[12] = 0;
  *(_DWORD *)&this->m_SocketAttribute[14] = 0;
  Item::CItemType::GetInstallGemAttribute(
    this->m_cSocket,
    this->m_SocketAttribute,
    (Item::ItemType::Type)this->m_ItemInfo->m_DetailData.m_cItemType,
    this->m_cSocketNum,
    0x10u);
  m_UpgradeAttribute = (Item::CEquipment *)this->m_UpgradeAttribute;
  *(_DWORD *)this->m_UpgradeAttribute = 0;
  Item::CItemType::GetUpgradeItemAttribute(
    (Item::ItemType::Type)this->m_ItemInfo->m_DetailData.m_cItemType,
    this->m_UpgradeAttribute,
    this->m_cMaterialType);
  v8 = &v6[2 * (*((unsigned __int8 *)lpSerializeItem_In + 17) >> 4)];
  v9 = (__int16 *)v6;
  if ( v6 != v8 )
  {
    do
    {
      v10 = *v9++;
      this->m_usAttribute[v10 & 0x3F] += v10 >> 6;
    }
    while ( v9 != (__int16 *)v8 );
  }
  for ( i = this->m_SocketAttribute;
        i != (Item::ItemAttribute *)m_UpgradeAttribute;
        this->m_usAttribute[v12 & 0x3F] += v12 >> 6 )
  {
    v12 = (__int16)*i++;
  }
  for ( ; m_UpgradeAttribute != (Item::CEquipment *)&this->m_usRuneSocket; this->m_usAttribute[v13 & 0x3F] += v13 >> 6 )
  {
    v13 = (__int16)m_UpgradeAttribute->__vftable;
    m_UpgradeAttribute = (Item::CEquipment *)((char *)m_UpgradeAttribute + 2);
  }
  m_cMaterialType = this->m_cMaterialType;
  v15 = 1.0;
  for ( j = 1.1; ; j = j * j )
  {
    if ( (m_cMaterialType & 1) != 0 )
      v15 = v15 * j;
    m_cMaterialType >>= 1;
    if ( !m_cMaterialType )
      break;
  }
  this->m_dwPrice = (unsigned __int64)((double)this->m_ItemInfo->m_DetailData.Item::CItem::m_dwPrice * v15);
  return 1;
}
// 45E2FC: conditional instruction was optimized away because ecx.4<100u

//----- (0045E340) --------------------------------------------------------
unsigned int __cdecl Math::Random::ComplexRandom(int nEndExtent, int nBeginExtent)
{
  unsigned int v2; // ecx
  unsigned int v3; // eax
  unsigned int v4; // ecx
  int v5; // ebx
  unsigned int v6; // eax

  if ( nEndExtent < 1 || nEndExtent <= nBeginExtent )
    return 1;
  v2 = (unsigned __int16)x * (unsigned __int16)a;
  v3 = (((unsigned __int16)x * HIWORD(a)) << 16) + v2 + c;
  v4 = (v3++ < ((unsigned __int16)x * (unsigned int)HIWORD(a)) << 16)
     + (((unsigned __int16)x * (unsigned int)HIWORD(a)) >> 16)
     + (v2 + c < v2);
  v5 = -(v3 == 0);
  v6 = (((unsigned __int16)word_50AEBA * (unsigned __int16)a) << 16) + v3;
  c = (v6 < ((unsigned __int16)word_50AEBA * (unsigned int)(unsigned __int16)a) << 16)
    + v4
    + (unsigned __int16)word_50AEBA * HIWORD(a)
    - v5
    + (((unsigned __int16)word_50AEBA * (unsigned int)(unsigned __int16)a) >> 16);
  x = v6;
  return nBeginExtent + v6 % (nEndExtent - nBeginExtent);
}
// 50AEBA: using guessed type __int16 word_50AEBA;

//----- (0045E410) --------------------------------------------------------
int __cdecl Math::Random::SimpleRandom(unsigned int dwSeed, int nEndExtent, int nBeginExtent)
{
  return nBeginExtent + (((214013 * dwSeed + 2531011) >> 16) & 0x7FFFuLL) % (unsigned int)(nEndExtent - nBeginExtent);
}

//----- (0045E440) --------------------------------------------------------
void __thiscall Item::CDepositContainer::CDepositContainer(Item::CDepositContainer *this)
{
  Item::CItemContainer::CItemContainer(this);
  this->m_lpArrayContainer = 0;
  this->m_lpOwner = 0;
  this->m_dwTabFlag = 0;
  this->m_dwGold = 0;
  this->m_nXSize = 0;
  this->m_nYSize = 0;
  this->m_nTabNum = 0;
  this->m_bLoginSuccess = 0;
  this->__vftable = (Item::CDepositContainer_vtbl *)&Item::CDepositContainer::`vftable';
  *(_DWORD *)this->m_szPassword = 0;
}
// 4E4028: using guessed type void *Item::CDepositContainer::`vftable';

//----- (0045E470) --------------------------------------------------------
bool __thiscall CParty::Destory(CParty *this, unsigned int dwTargetID, bool bSwitch)
{
  return 0;
}

//----- (0045E480) --------------------------------------------------------
char __thiscall Item::CDepositContainer::Initialize(
        Item::CDepositContainer *this,
        CCharacter *lpCharacter,
        unsigned __int8 nXSize,
        unsigned __int8 nYSize,
        unsigned __int8 nTabNum)
{
  char *v6; // eax
  Item::CArrayContainer *v7; // ebp
  int v8; // ebp
  int i; // edi

  this->m_lpOwner = lpCharacter;
  this->m_dwCID = lpCharacter->m_dwCID;
  this->m_nYSize = nYSize;
  this->m_nXSize = nXSize;
  this->m_nTabNum = nTabNum;
  v6 = (char *)operator new[](24 * nTabNum + 4);
  v7 = 0;
  if ( v6 )
  {
    v7 = (Item::CArrayContainer *)(v6 + 4);
    *(_DWORD *)v6 = nTabNum;
    `eh vector constructor iterator'(
      v6 + 4,
      0x18u,
      nTabNum,
      (void (__thiscall *)(void *))Item::CArrayContainer::CArrayContainer,
      (void (__thiscall *)(void *))Item::CArrayContainer::~CArrayContainer);
  }
  this->m_lpArrayContainer = v7;
  if ( v7 )
  {
    v8 = 0;
    if ( !this->m_nTabNum )
      return 1;
    for ( i = 0; Item::CArrayContainer::Initialize(&this->m_lpArrayContainer[i], this->m_dwCID, nXSize, nYSize, 1u); ++i )
    {
      if ( ++v8 >= this->m_nTabNum )
        return 1;
    }
  }
  return 0;
}

//----- (0045E570) --------------------------------------------------------
unsigned __int8 *__thiscall Item::CArrayContainer::`vector deleting destructor'(Item::CArrayContainer *this, char a2)
{
  if ( (a2 & 2) != 0 )
  {
    `eh vector destructor iterator'(
      (char *)this,
      0x18u,
      *(_DWORD *)&this[-1].m_nXSize,
      (void (__thiscall *)(void *))Item::CArrayContainer::~CArrayContainer);
    if ( (a2 & 1) != 0 )
      operator delete[](&this[-1].m_nXSize);
    return &this[-1].m_nXSize;
  }
  else
  {
    Item::CArrayContainer::~CArrayContainer((Item::CListContainer *)this);
    if ( (a2 & 1) != 0 )
      operator delete(this);
    return (unsigned __int8 *)this;
  }
}

//----- (0045E5D0) --------------------------------------------------------
Item::CItem *__thiscall Item::CDepositContainer::GetItem(Item::CDepositContainer *this, unsigned int itemPos)
{
  unsigned __int8 v2; // al
  Item::CArrayContainer *m_lpArrayContainer; // edx

  v2 = (itemPos >> 12) & 0xF;
  if ( v2 >= this->m_nTabNum )
    return 0;
  m_lpArrayContainer = this->m_lpArrayContainer;
  if ( !m_lpArrayContainer )
    return 0;
  BYTE1(itemPos) &= 0xFu;
  return (Item::CItem *)((int (__thiscall *)(Item::CArrayContainer *, unsigned int))m_lpArrayContainer[v2].GetItem)(
                          &m_lpArrayContainer[v2],
                          itemPos);
}

//----- (0045E610) --------------------------------------------------------
char __thiscall Item::CDepositContainer::SetItem(
        Item::CDepositContainer *this,
        unsigned int itemPos,
        Item::CItem *lpItem)
{
  unsigned __int8 v3; // bl
  char lpItema; // [esp+14h] [ebp+8h]

  v3 = (itemPos >> 12) & 0xF;
  if ( this->m_lpArrayContainer && lpItem && v3 < this->m_nTabNum && ((1 << v3) & this->m_dwTabFlag) != 0 )
  {
    BYTE1(itemPos) &= 0xFu;
    lpItema = ((int (__thiscall *)(Item::CArrayContainer *, unsigned int, Item::CItem *))this->m_lpArrayContainer[v3].SetItem)(
                &this->m_lpArrayContainer[v3],
                itemPos,
                lpItem);
    Item::CItem::MoveItem(lpItem, (Item::ItemPos)(itemPos & 0xF ^ ((itemPos & 0xFF0) + (v3 << 12))));
    return lpItema;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CDepositContainer::SetItem",
      aDWorkRylSource_43,
      108,
      aCid0x08x_240,
      this->m_dwCID,
      this->m_dwTabFlag,
      itemPos & 0xF,
      (unsigned __int16)itemPos >> 4);
    return 0;
  }
}

//----- (0045E6E0) --------------------------------------------------------
bool __thiscall Item::CDepositContainer::TestItem(
        Item::CDepositContainer *this,
        unsigned int itemPos,
        int usProtoTypeID)
{
  __int16 v4; // cx

  if ( !this->m_lpArrayContainer )
    return 0;
  if ( (unsigned __int8)((itemPos >> 12) & 0xF) >= this->m_nTabNum )
    return 0;
  v4 = (unsigned __int16)itemPos >> 12;
  if ( ((1 << ((unsigned __int16)itemPos >> 12)) & this->m_dwTabFlag) == 0 )
    return 0;
  BYTE1(itemPos) &= 0xFu;
  return ((int (__thiscall *)(Item::CArrayContainer *, unsigned int, int))this->m_lpArrayContainer[(unsigned __int8)v4].TestItem)(
           &this->m_lpArrayContainer[(unsigned __int8)v4],
           itemPos,
           usProtoTypeID);
}

//----- (0045E720) --------------------------------------------------------
bool __thiscall Item::CDepositContainer::RemoveItem(Item::CDepositContainer *this, unsigned int itemPos)
{
  __int16 v3; // cx

  if ( !this->m_lpArrayContainer )
    return 0;
  if ( (unsigned __int8)((itemPos >> 12) & 0xF) >= this->m_nTabNum )
    return 0;
  v3 = (unsigned __int16)itemPos >> 12;
  if ( ((1 << ((unsigned __int16)itemPos >> 12)) & this->m_dwTabFlag) == 0 )
    return 0;
  BYTE1(itemPos) &= 0xFu;
  return ((int (__thiscall *)(Item::CArrayContainer *, unsigned int))this->m_lpArrayContainer[(unsigned __int8)v3].RemoveItem)(
           &this->m_lpArrayContainer[(unsigned __int8)v3],
           itemPos);
}

//----- (0045E760) --------------------------------------------------------
char __thiscall Item::CDepositContainer::Update(Item::CDepositContainer *this, CSendStream *SendStream)
{
  unsigned int m_dwCID; // edx
  unsigned int m_dwUID; // ecx
  unsigned __int8 m_nTabNum; // al
  unsigned int v6; // ebx
  int v7; // ebp
  char *v8; // ecx
  int v9; // edi
  unsigned int v10; // ebx
  int v11; // ebp
  Item::CArrayContainer *m_lpArrayContainer; // ecx
  unsigned int v13; // eax
  int v15; // [esp+10h] [ebp-1F70h]
  unsigned int v16; // [esp+10h] [ebp-1F70h]
  int v17; // [esp+14h] [ebp-1F6Ch] BYREF
  char SourceData[18]; // [esp+18h] [ebp-1F68h] BYREF
  unsigned int v19; // [esp+2Ah] [ebp-1F56h]
  unsigned int v20; // [esp+2Eh] [ebp-1F52h]
  unsigned int m_dwTabFlag; // [esp+32h] [ebp-1F4Eh]
  __int16 v22; // [esp+36h] [ebp-1F4Ah]
  char v23; // [esp+38h] [ebp-1F48h]
  bool v24; // [esp+39h] [ebp-1F47h]
  Item::ItemData v25; // [esp+3Ah] [ebp-1F46h] BYREF

  m_dwCID = this->m_dwCID;
  m_dwUID = this->m_lpOwner->m_dwUID;
  m_dwTabFlag = this->m_dwTabFlag;
  m_nTabNum = this->m_nTabNum;
  v6 = 0;
  v19 = m_dwUID;
  v20 = m_dwCID;
  if ( !m_nTabNum )
    return 1;
  v15 = 1;
  v7 = 0;
  while ( 1 )
  {
    v8 = (char *)this->m_lpArrayContainer + v7;
    v17 = 8000;
    v9 = 0;
    if ( (*(unsigned __int8 (__thiscall **)(char *, Item::ItemData *, int *))(*(_DWORD *)v8 + 20))(v8, &v25, &v17) )
      v9 = v17;
    else
      Item::ItemData::DumpInfo(&v25, this->m_dwCID, szExtraString);
    v10 = v6 + 1;
    v11 = v7 + 24;
    v16 = v15 + 1;
    if ( v10 < this->m_nTabNum )
    {
      m_lpArrayContainer = this->m_lpArrayContainer;
      v17 = 8000 - v9;
      if ( (*(Item::CArrayContainer_vtbl **)((char *)&m_lpArrayContainer->__vftable + v11))->SerializeOut(
             (Item::CArrayContainer *)((char *)m_lpArrayContainer + v11),
             (char *)&v25 + v9,
             (unsigned int *)&v17) )
      {
        v9 += v17;
      }
      else
      {
        Item::ItemData::DumpInfo(&v25, this->m_dwCID, aItemCdepositco_4);
      }
    }
    v13 = this->m_nTabNum;
    v22 = v9;
    v23 = v10 >> 1;
    v24 = v16 >= v13;
    if ( !CSendStream::WrapCompress(SendStream, SourceData, (char *)(v9 + 34), 0x5Du, 0, 0) )
      break;
    v6 = v10 + 1;
    v7 = v11 + 24;
    v15 = v16 + 1;
    if ( v6 >= this->m_nTabNum )
      return 1;
  }
  return 0;
}

//----- (0045E8C0) --------------------------------------------------------
char __thiscall Item::CDepositContainer::ChangePassword(
        Item::CDepositContainer *this,
        const char *szCurrentPassword,
        unsigned int nCurrentPasswordLength,
        const char *szChangePassword,
        unsigned int nChangePasswordLength)
{
  int v5; // eax
  unsigned __int8 *m_szPassword; // edx
  unsigned int v7; // ecx

  v5 = nCurrentPasswordLength;
  if ( nCurrentPasswordLength > 4 )
    v5 = 4;
  m_szPassword = this->m_szPassword;
  if ( memcmp((const char *)this->m_szPassword, szCurrentPassword, v5) )
    return 0;
  v7 = nChangePasswordLength;
  if ( nChangePasswordLength > 4 )
    v7 = 4;
  qmemcpy(m_szPassword, szChangePassword, v7);
  return 1;
}

//----- (0045E920) --------------------------------------------------------
char __thiscall Item::CDepositContainer::AddGold(Item::CDepositContainer *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax

  m_dwGold = this->m_dwGold;
  if ( m_dwGold >= -1 - dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CDepositContainer::AddGold",
      aDWorkRylSource_43,
      294,
      aCid0x08x_137,
      this->m_dwCID,
      dwGold);
    return 0;
  }
  else
  {
    this->m_dwGold = dwGold + m_dwGold;
    return 1;
  }
}

//----- (0045E970) --------------------------------------------------------
char __thiscall Item::CDepositContainer::DeductGold(Item::CDepositContainer *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax

  m_dwGold = this->m_dwGold;
  if ( dwGold > m_dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CDepositContainer::DeductGold",
      aDWorkRylSource_43,
      306,
      aCid0x08x_221,
      this->m_dwCID);
    return 0;
  }
  else
  {
    this->m_dwGold = m_dwGold - dwGold;
    return 1;
  }
}

//----- (0045E9C0) --------------------------------------------------------
void __thiscall Item::CDepositContainer::DumpItemInfo(Item::CDepositContainer *this)
{
  int v2; // edi
  int v3; // ebx

  v2 = 0;
  if ( this->m_nTabNum )
  {
    v3 = 0;
    do
    {
      this->m_lpArrayContainer[v3].DumpItemInfo(&this->m_lpArrayContainer[v3]);
      ++v2;
      ++v3;
    }
    while ( v2 < this->m_nTabNum );
  }
}

//----- (0045E9F0) --------------------------------------------------------
char __thiscall Item::CDepositContainer::LogUpdate(
        Item::CDepositContainer *this,
        char *szLogBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  unsigned __int8 m_nTabNum; // al
  int v5; // esi
  int v6; // ebp
  int v7; // ebx
  int v8; // edx
  unsigned int dwRemain; // [esp+10h] [ebp-4h] BYREF

  m_nTabNum = this->m_nTabNum;
  v5 = 0;
  v6 = 0;
  dwRemain = *dwBufferSize_InOut;
  if ( m_nTabNum )
  {
    v7 = 0;
    while ( this->m_lpArrayContainer[v7].SerializeOut(&this->m_lpArrayContainer[v7], &szLogBuffer_Out[v5], &dwRemain) )
    {
      v8 = this->m_nTabNum;
      v5 += dwRemain;
      ++v6;
      ++v7;
      dwRemain = *dwBufferSize_InOut - v5;
      if ( v6 >= v8 )
        goto LABEL_5;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CDepositContainer::LogUpdate",
      aDWorkRylSource_43,
      332,
      aCid0x08x_166,
      this->m_dwCID);
    *dwBufferSize_InOut = v5;
    return 0;
  }
  else
  {
LABEL_5:
    *dwBufferSize_InOut = v5;
    return 1;
  }
}

//----- (0045EAA0) --------------------------------------------------------
void __thiscall Item::CDepositContainer::~CDepositContainer(Item::CDepositContainer *this)
{
  Item::CArrayContainer *m_lpArrayContainer; // ecx

  this->__vftable = (Item::CDepositContainer_vtbl *)&Item::CDepositContainer::`vftable';
  m_lpArrayContainer = this->m_lpArrayContainer;
  if ( m_lpArrayContainer )
  {
    if ( *(_DWORD *)&m_lpArrayContainer[-1].m_nXSize )
      ((void (__thiscall *)(Item::CArrayContainer *, int))m_lpArrayContainer->~Item::CArrayContainer)(
        m_lpArrayContainer,
        3);
    else
      operator delete[](&m_lpArrayContainer[-1].m_nXSize);
    this->m_lpArrayContainer = 0;
  }
  Item::CArrayContainer::~CArrayContainer((Item::CListContainer *)this);
}
// 4E4028: using guessed type void *Item::CDepositContainer::`vftable';

//----- (0045EB20) --------------------------------------------------------
char __thiscall Item::CDepositContainer::SerializeIn(
        Item::CDepositContainer *this,
        const char *szItemBuffer_In,
        unsigned int dwBufferSize_In)
{
  int v4; // ebx
  const char *v5; // ebp
  Item::CItem *v6; // eax
  Item::CItem *v7; // esi
  unsigned int m_dwTabFlag; // ecx
  unsigned int m_dwCID; // [esp-Ch] [ebp-20h]
  unsigned int nBufferSize; // [esp+8h] [ebp-Ch]
  Item::CItemFactory *ItemFactory; // [esp+Ch] [ebp-8h]

  v4 = 0;
  nBufferSize = dwBufferSize_In;
  ItemFactory = CSingleton<Item::CItemFactory>::ms_pSingleton;
  if ( dwBufferSize_In )
  {
    v5 = szItemBuffer_In;
    do
    {
      dwBufferSize_In = nBufferSize;
      v6 = Item::CItemFactory::CreateItem(ItemFactory, &v5[v4], &dwBufferSize_In);
      v7 = v6;
      if ( v6 )
      {
        LOWORD(szItemBuffer_In) = v6->m_ItemData.m_ItemPos;
        if ( !Item::CDepositContainer::SetItem(this, (unsigned int)szItemBuffer_In, v6) )
        {
          m_dwTabFlag = this->m_dwTabFlag;
          dwBufferSize_In = (unsigned __int8)v5[v4 + 12];
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Item::CDepositContainer::SerializeIn",
            aDWorkRylSource_43,
            166,
            aCid0x08x_218,
            this->m_dwCID,
            *(_WORD *)&v7->m_ItemData.m_ItemPos & 0xF,
            *(_WORD *)&v7->m_ItemData.m_ItemPos >> 4,
            m_dwTabFlag);
          ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
        }
      }
      else
      {
        m_dwCID = this->m_dwCID;
        dwBufferSize_In = (unsigned __int8)v5[v4 + 12];
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CDepositContainer::SerializeIn",
          aDWorkRylSource_43,
          160,
          aCid0x08x_58,
          m_dwCID);
      }
      v4 += dwBufferSize_In;
      nBufferSize -= dwBufferSize_In;
    }
    while ( nBufferSize );
  }
  return 1;
}

//----- (0045EC40) --------------------------------------------------------
char __thiscall Item::CDepositContainer::ClientUpdate(Item::CDepositContainer *this, CSendStream *ClientSendStream)
{
  bool v3; // cl
  char szData[8]; // [esp+8h] [ebp-Ch] BYREF

  v3 = (this->m_dwTabFlag & 0x80000000) != 0;
  *(_DWORD *)szData = this->m_dwGold;
  szData[4] = v3;
  if ( GameClientSendPacket::SendCharDepositCmd(ClientSendStream, 6u, szData, 8u, 0) )
    return Item::CDepositContainer::Update(this, ClientSendStream);
  else
    return 0;
}

//----- (0045ECC0) --------------------------------------------------------
char __thiscall Item::CDepositContainer::DBUpdate(Item::CDepositContainer *this, CSendStream *AgentSendStream)
{
  if ( GameClientSendPacket::SendCharDepositGoldToDBAgent(AgentSendStream, this->m_lpOwner->m_dwUID, this->m_dwGold) )
    return Item::CDepositContainer::Update(this, AgentSendStream);
  else
    return 0;
}

//----- (0045ED00) --------------------------------------------------------
Item::CDepositContainer *__thiscall Item::CDepositContainer::`scalar deleting destructor'(
        Item::CDepositContainer *this,
        char a2)
{
  Item::CDepositContainer::~CDepositContainer(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0045ED20) --------------------------------------------------------
void __thiscall Item::ItemData::DumpInfo(Item::ItemData *this, unsigned int dwCID, const char *szExtraString)
{
  char szItemUID[32]; // [esp+4h] [ebp-24h] BYREF

  Math::Convert::Hex64ToStr(szItemUID, this->m_dwUID);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "Item::ItemData::DumpInfo",
    aDWorkRylSource_23,
    23,
    "CID:0x%08x / ItemUID:%s / ProtoTypeID:%d / ItemPos(%d, 0x%04x) / ItemSize:%d / NumOrDurability:%d %s",
    dwCID,
    szItemUID,
    this->m_usProtoTypeID,
    *(_WORD *)&this->m_ItemPos & 0xF,
    *(_WORD *)&this->m_ItemPos >> 4,
    this->m_cItemSize,
    this->m_cNumOrDurability,
    szExtraString);
}

//----- (0045EDA0) --------------------------------------------------------
void __thiscall Item::SpriteData::Initialize(Item::SpriteData *this)
{
  memset(this, 0, 0x40u);
  *(_DWORD *)this->m_szSpriteName = 0;
  *(_DWORD *)&this->m_szSpriteName[4] = 0;
  *(_DWORD *)&this->m_szSpriteName[8] = 0;
  *(_DWORD *)&this->m_szSpriteName[12] = 0;
  *(_DWORD *)&this->m_szSpriteName[16] = 0;
  *(_DWORD *)&this->m_szSpriteName[20] = 0;
  *(_DWORD *)&this->m_szSpriteName[24] = 0;
  *(_DWORD *)&this->m_szSpriteName[28] = 0;
  this->m_nSpriteMaxY = 0;
  this->m_nSpriteMaxX = 0;
  this->m_nSpriteMinY = 0;
  this->m_nSpriteMinX = 0;
}

//----- (0045EDE0) --------------------------------------------------------
void __thiscall Item::StringData::Initialize(Item::StringData *this)
{
  char *m_szEquipModelName; // ecx

  *(_DWORD *)this->m_szFieldModelName = 0;
  *(_DWORD *)&this->m_szFieldModelName[4] = 0;
  *(_DWORD *)&this->m_szFieldModelName[8] = 0;
  *(_DWORD *)&this->m_szFieldModelName[12] = 0;
  *(_DWORD *)&this->m_szFieldModelName[16] = 0;
  *(_DWORD *)&this->m_szFieldModelName[20] = 0;
  *(_DWORD *)&this->m_szFieldModelName[24] = 0;
  *(_DWORD *)&this->m_szFieldModelName[28] = 0;
  m_szEquipModelName = this->m_szEquipModelName;
  *(_DWORD *)m_szEquipModelName = 0;
  *((_DWORD *)m_szEquipModelName + 1) = 0;
  *((_DWORD *)m_szEquipModelName + 2) = 0;
  *((_DWORD *)m_szEquipModelName + 3) = 0;
  *((_DWORD *)m_szEquipModelName + 4) = 0;
  *((_DWORD *)m_szEquipModelName + 5) = 0;
  *((_DWORD *)m_szEquipModelName + 6) = 0;
  *((_DWORD *)m_szEquipModelName + 7) = 0;
  *(_DWORD *)this->m_szEffectSoundName = 0;
  *(_DWORD *)&this->m_szEffectSoundName[4] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[8] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[12] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[16] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[20] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[24] = 0;
  *(_DWORD *)&this->m_szEffectSoundName[28] = 0;
  *(_DWORD *)this->m_szTypeName = 0;
  *(_DWORD *)&this->m_szTypeName[4] = 0;
  *(_DWORD *)&this->m_szTypeName[8] = 0;
  *(_DWORD *)&this->m_szTypeName[12] = 0;
  *(_DWORD *)&this->m_szTypeName[16] = 0;
  *(_DWORD *)&this->m_szTypeName[20] = 0;
  *(_DWORD *)&this->m_szTypeName[24] = 0;
  *(_DWORD *)&this->m_szTypeName[28] = 0;
  memset(this->m_szItemDescribe, 0, sizeof(this->m_szItemDescribe));
}

//----- (0045EE60) --------------------------------------------------------
void __thiscall Item::ItemGarbage::ItemGarbage(Item::ItemGarbage *this, Item::CItem *lpItem, unsigned int dwRemainNum)
{
  this->m_lpItem = lpItem;
  this->m_dwRemainNum = dwRemainNum;
}

//----- (0045EE80) --------------------------------------------------------
void __thiscall Item::ChemicalInfo::ChemicalInfo(Item::ChemicalInfo *this)
{
  this->m_wPickkingItemID = 0;
  this->m_wTargetItemID = 0;
  this->m_wResultItemID = 0;
  this->m_cPickkingItemNum = 0;
  this->m_cTargetItemNum = 0;
  this->m_cResultItemNum = 0;
}

//----- (0045EEA0) --------------------------------------------------------
void __thiscall Item::SpriteData::SpriteData(Item::SpriteData *this)
{
  memset(this, 0, 0x40u);
  *(_DWORD *)this->m_szSpriteName = 0;
  *(_DWORD *)&this->m_szSpriteName[4] = 0;
  *(_DWORD *)&this->m_szSpriteName[8] = 0;
  *(_DWORD *)&this->m_szSpriteName[12] = 0;
  *(_DWORD *)&this->m_szSpriteName[16] = 0;
  *(_DWORD *)&this->m_szSpriteName[20] = 0;
  *(_DWORD *)&this->m_szSpriteName[24] = 0;
  *(_DWORD *)&this->m_szSpriteName[28] = 0;
  this->m_nSpriteMaxY = 0;
  this->m_nSpriteMaxX = 0;
  this->m_nSpriteMinY = 0;
  this->m_nSpriteMinX = 0;
}

//----- (0045EEE0) --------------------------------------------------------
void __thiscall Item::ItemInfo::Initialize(Item::ItemInfo *this)
{
  this->m_usProtoTypeID = 0;
  this->m_DetailData.m_ItemGrade = HELM;
  this->m_DetailData.m_dwCraftExp = 0;
  this->m_DetailData.m_dwMedalPrice = 0;
  this->m_DetailData.m_dwBlackPrice = 0;
  this->m_DetailData.m_dwPrice = 0;
  this->m_DetailData.m_dwFlags = 0;
  this->m_DetailData.m_cMaxSocketNum = 0;
  this->m_DetailData.m_cMaterialType = 0;
  this->m_DetailData.m_cMaxDurabilityOrStack = 0;
  this->m_DetailData.m_cDefaultDurabilityOrStack = 0;
  this->m_DetailData.m_cYSize = 0;
  this->m_DetailData.m_cXSize = 0;
  this->m_DetailData.m_cItemType = 0;
  this->m_DetailData.m_cOptionLimit = 0;
  *(_DWORD *)this->m_UseLimit.m_nStatusLimit = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[2] = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[4] = 0;
  this->m_UseLimit.m_nStatusLimit[6] = 0;
  this->m_UseLimit.m_cLevelLimit = 0;
  this->m_UseLimit.m_cSkillLevel = 0;
  this->m_UseLimit.m_nSkillType = 0;
  this->m_UseLimit.m_dwClassLimit = 0;
  memset(&this->m_SpriteData, 0, 0x40u);
  *(_DWORD *)this->m_SpriteData.m_szSpriteName = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[4] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[8] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[12] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[16] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[20] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[24] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[28] = 0;
  this->m_SpriteData.m_nSpriteMaxY = 0;
  this->m_SpriteData.m_nSpriteMaxX = 0;
  this->m_SpriteData.m_nSpriteMinY = 0;
  this->m_SpriteData.m_nSpriteMinX = 0;
  Item::StringData::Initialize(&this->m_StringData);
  this->m_EquipAttribute.m_nAttibuteNum = 0;
  *(_DWORD *)this->m_EquipAttribute.m_usAttributeValue = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[2] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[4] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[6] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[8] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[10] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[12] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[14] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[16] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[18] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[20] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[22] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[24] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[26] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[28] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[30] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[32] = 0;
  this->m_EquipAttribute.m_usAttributeValue[34] = 0;
  this->m_UseItemInfo.m_usSkill_ID = 0;
  this->m_UseItemInfo.m_usSkill_LockCount = 0;
  this->m_UseItemInfo.m_cZone = 0;
  this->m_UseItemInfo.m_dwAmount = 0;
}

//----- (0045F010) --------------------------------------------------------
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this)
{
  this->m_DetailData.m_ItemGrade = HELM;
  this->m_DetailData.m_dwCraftExp = 0;
  this->m_DetailData.m_dwMedalPrice = 0;
  this->m_DetailData.m_dwBlackPrice = 0;
  this->m_DetailData.m_dwPrice = 0;
  this->m_DetailData.m_dwFlags = 0;
  this->m_DetailData.m_cMaxSocketNum = 0;
  this->m_DetailData.m_cMaterialType = 0;
  this->m_DetailData.m_cMaxDurabilityOrStack = 0;
  this->m_DetailData.m_cDefaultDurabilityOrStack = 0;
  this->m_DetailData.m_cYSize = 0;
  this->m_DetailData.m_cXSize = 0;
  this->m_DetailData.m_cItemType = 0;
  this->m_DetailData.m_cOptionLimit = 0;
  *(_DWORD *)this->m_UseLimit.m_nStatusLimit = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[2] = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[4] = 0;
  this->m_UseLimit.m_nStatusLimit[6] = 0;
  this->m_UseLimit.m_cLevelLimit = 0;
  this->m_UseLimit.m_cSkillLevel = 0;
  this->m_UseLimit.m_nSkillType = 0;
  this->m_UseLimit.m_dwClassLimit = 0;
  memset(&this->m_SpriteData, 0, 0x40u);
  *(_DWORD *)this->m_SpriteData.m_szSpriteName = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[4] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[8] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[12] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[16] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[20] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[24] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[28] = 0;
  this->m_SpriteData.m_nSpriteMaxY = 0;
  this->m_SpriteData.m_nSpriteMaxX = 0;
  this->m_SpriteData.m_nSpriteMinY = 0;
  this->m_SpriteData.m_nSpriteMinX = 0;
  Item::StringData::Initialize(&this->m_StringData);
  this->m_EquipAttribute.m_nAttibuteNum = 0;
  *(_DWORD *)this->m_EquipAttribute.m_usAttributeValue = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[2] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[4] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[6] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[8] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[10] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[12] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[14] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[16] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[18] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[20] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[22] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[24] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[26] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[28] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[30] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[32] = 0;
  this->m_EquipAttribute.m_usAttributeValue[34] = 0;
  this->m_UseItemInfo.m_Pos.m_fPointX = 0.0;
  this->m_UseItemInfo.m_Pos.m_fPointY = 0.0;
  this->m_UseItemInfo.m_Pos.m_fPointZ = 0.0;
  this->m_UseItemInfo.m_usSkill_ID = 0;
  this->m_UseItemInfo.m_usSkill_LockCount = 0;
  this->m_UseItemInfo.m_cZone = 0;
  this->m_UseItemInfo.m_dwAmount = 0;
  Item::ItemInfo::Initialize(this);
}

//----- (0045F160) --------------------------------------------------------
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this, unsigned __int16 usProtoTypeID)
{
  this->m_DetailData.m_ItemGrade = HELM;
  this->m_DetailData.m_dwCraftExp = 0;
  this->m_DetailData.m_dwMedalPrice = 0;
  this->m_DetailData.m_dwBlackPrice = 0;
  this->m_DetailData.m_dwPrice = 0;
  this->m_DetailData.m_dwFlags = 0;
  this->m_DetailData.m_cMaxSocketNum = 0;
  this->m_DetailData.m_cMaterialType = 0;
  this->m_DetailData.m_cMaxDurabilityOrStack = 0;
  this->m_DetailData.m_cDefaultDurabilityOrStack = 0;
  this->m_DetailData.m_cYSize = 0;
  this->m_DetailData.m_cXSize = 0;
  this->m_DetailData.m_cItemType = 0;
  this->m_DetailData.m_cOptionLimit = 0;
  *(_DWORD *)this->m_UseLimit.m_nStatusLimit = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[2] = 0;
  *(_DWORD *)&this->m_UseLimit.m_nStatusLimit[4] = 0;
  this->m_UseLimit.m_nStatusLimit[6] = 0;
  this->m_UseLimit.m_cLevelLimit = 0;
  this->m_UseLimit.m_cSkillLevel = 0;
  this->m_UseLimit.m_nSkillType = 0;
  this->m_UseLimit.m_dwClassLimit = 0;
  memset(&this->m_SpriteData, 0, 0x40u);
  *(_DWORD *)this->m_SpriteData.m_szSpriteName = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[4] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[8] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[12] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[16] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[20] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[24] = 0;
  *(_DWORD *)&this->m_SpriteData.m_szSpriteName[28] = 0;
  this->m_SpriteData.m_nSpriteMaxY = 0;
  this->m_SpriteData.m_nSpriteMaxX = 0;
  this->m_SpriteData.m_nSpriteMinY = 0;
  this->m_SpriteData.m_nSpriteMinX = 0;
  Item::StringData::Initialize(&this->m_StringData);
  this->m_EquipAttribute.m_nAttibuteNum = 0;
  *(_DWORD *)this->m_EquipAttribute.m_usAttributeValue = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[2] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[4] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[6] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[8] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[10] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[12] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[14] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[16] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[18] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[20] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[22] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[24] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[26] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[28] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[30] = 0;
  *(_DWORD *)&this->m_EquipAttribute.m_usAttributeValue[32] = 0;
  this->m_EquipAttribute.m_usAttributeValue[34] = 0;
  this->m_UseItemInfo.m_Pos.m_fPointX = 0.0;
  this->m_UseItemInfo.m_Pos.m_fPointY = 0.0;
  this->m_UseItemInfo.m_Pos.m_fPointZ = 0.0;
  this->m_UseItemInfo.m_usSkill_ID = 0;
  this->m_UseItemInfo.m_usSkill_LockCount = 0;
  this->m_UseItemInfo.m_cZone = 0;
  this->m_UseItemInfo.m_dwAmount = 0;
  Item::ItemInfo::Initialize(this);
  this->m_usProtoTypeID = usProtoTypeID;
}

//----- (0045F2B0) --------------------------------------------------------
char __thiscall Item::CExchangeContainer::Initialize(
        Item::CStallContainer *this,
        CCharacter *lpCharacter,
        unsigned __int8 cStallWidth,
        unsigned __int8 cStallHeight)
{
  this->m_lpOwner = lpCharacter;
  return Item::CArrayContainer::Initialize(this, lpCharacter->m_dwCID, cStallWidth, cStallHeight, 1u);
}

//----- (0045F2D0) --------------------------------------------------------
void __thiscall Item::CStallContainer::Destroy(Item::CStallContainer *this)
{
  if ( this->m_lppItems )
  {
    operator delete[](this->m_lppItems);
    this->m_lppItems = 0;
  }
}

//----- (0045F300) --------------------------------------------------------
char __thiscall Item::CStallContainer::StallPriceOut(
        Item::CStallContainer *this,
        unsigned int *szStallPriceBuffer_Out,
        unsigned __int8 *cItemNum_Out)
{
  Item::CItem **m_lppItems; // esi
  Item::CItem **i; // ebx
  Item::CItem *v5; // eax
  unsigned int m_dwStallPrice; // eax

  m_lppItems = this->m_lppItems;
  for ( i = &m_lppItems[this->m_nMaxSize]; m_lppItems != i; ++m_lppItems )
  {
    v5 = *m_lppItems;
    if ( *m_lppItems && this->m_lpNullItem != v5 )
    {
      if ( v5->m_dwStallPrice )
        m_dwStallPrice = v5->m_dwStallPrice;
      else
        m_dwStallPrice = v5->m_dwPrice;
      szStallPriceBuffer_Out[(*cItemNum_Out)++] = m_dwStallPrice;
    }
  }
  return 1;
}

//----- (0045F360) --------------------------------------------------------
char __thiscall Item::CStallContainer::Open(Item::CStallContainer *this, char *strStallName)
{
  char *m_strStallName; // ebp

  m_strStallName = this->m_strStallName;
  if ( strcmp(this->m_strStallName, szLoseCharName) || !this->m_lpOwner )
    return 0;
  strncpy(m_strStallName, strStallName, 0x20u);
  GAMELOG::LogStallOpenClose(this->m_lpOwner, m_strStallName, 1);
  return 1;
}

//----- (0045F3C0) --------------------------------------------------------
char __thiscall Item::CStallContainer::Leave(Item::CStallContainer *this, CCharacter *lpCustomer)
{
  CCharacter *m_lpOwner; // ecx
  int v4; // ebx
  CCharacter **i; // eax

  if ( !lpCustomer )
    return 0;
  m_lpOwner = this->m_lpOwner;
  if ( !m_lpOwner )
    return 0;
  v4 = 0;
  for ( i = this->m_lpCustomer; *i != lpCustomer; ++i )
  {
    if ( ++v4 >= 10 )
      return 0;
  }
  GAMELOG::LogStallEnterLeave(m_lpOwner, lpCustomer->m_dwCID, 0);
  lpCustomer->m_Stall.m_lpOtherOwner = 0;
  if ( v4 < 9 )
    qmemcpy(&this->m_lpCustomer[v4], &this->m_lpCustomer[v4 + 1], 4 * (9 - v4));
  this->m_lpCustomer[9] = 0;
  return 1;
}

//----- (0045F440) --------------------------------------------------------
char __thiscall Item::CStallContainer::SendCharStallOpen(Item::CStallContainer *this, char *strStallName)
{
  CCharacter *m_lpOwner; // eax
  CCell *m_lpCell; // esi
  PktStO pktStO; // [esp+4h] [ebp-34h] BYREF

  m_lpOwner = this->m_lpOwner;
  m_lpCell = m_lpOwner->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    pktStO.m_dwCharID = m_lpOwner->m_dwCID;
    strncpy(pktStO.m_StallName, strStallName, 0x20u);
    if ( PacketWrap::WrapCrypt((char *)&pktStO, 0x30u, 0x5Eu, 0, 0) )
      CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktStO, 0x30u, 0x5Eu);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CStallContainer::SendCharStallOpen",
      aDWorkRylSource_67,
      279,
      aCid0x08x_157,
      m_lpOwner->m_dwCID);
    return 0;
  }
}

//----- (0045F4F0) --------------------------------------------------------
char __thiscall Item::CStallContainer::SendAllCustomer(
        Item::CStallContainer *this,
        char *szPacket,
        unsigned int dwPacketSize,
        bool bIncludeOwner,
        unsigned __int8 cCMD_In)
{
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  int v7; // edi
  CCharacter **m_lpCustomer; // esi
  CSendStream *v9; // eax

  if ( bIncludeOwner )
  {
    m_lpGameClientDispatch = this->m_lpOwner->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      CSendStream::PutBuffer(&m_lpGameClientDispatch->m_SendStream, szPacket, dwPacketSize, cCMD_In);
  }
  v7 = 0;
  m_lpCustomer = this->m_lpCustomer;
  do
  {
    if ( !*m_lpCustomer )
      break;
    v9 = (CSendStream *)(*m_lpCustomer)->m_lpGameClientDispatch;
    if ( v9 )
      CSendStream::PutBuffer(v9 + 8, szPacket, dwPacketSize, cCMD_In);
    ++v7;
    ++m_lpCustomer;
  }
  while ( v7 < 10 );
  return 1;
}

//----- (0045F560) --------------------------------------------------------
void __thiscall Item::CStallContainer::~CStallContainer(Item::CStallContainer *this)
{
  Item::CItem **m_lppItems; // eax

  m_lppItems = this->m_lppItems;
  this->__vftable = (Item::CStallContainer_vtbl *)&Item::CStallContainer::`vftable';
  if ( m_lppItems )
  {
    operator delete[](m_lppItems);
    this->m_lppItems = 0;
  }
  Item::CArrayContainer::~CArrayContainer((Item::CListContainer *)this);
}
// 4E4528: using guessed type void *Item::CStallContainer::`vftable';

//----- (0045F590) --------------------------------------------------------
void __thiscall Item::CStallContainer::RollBackAllItem(Item::CStallContainer *this)
{
  unsigned __int16 i; // bx
  unsigned __int16 v2; // di
  int v3; // ebp
  int v4; // eax
  int m_nYSize; // edx

  for ( i = 0; i < this->m_nYSize; ++i )
  {
    v2 = 0;
    if ( this->m_nXSize )
    {
      v3 = (unsigned __int16)(i * this->m_nXSize);
      do
      {
        v4 = *((_DWORD *)&this->m_lppItems[v3] + v2);
        if ( v4 && this->m_lpNullItem != (Item::CItem *)v4 )
        {
          if ( (*(_WORD *)(v4 + 18) & 0xF) != 6 )
            *(_WORD *)(v4 + 22) = *(_WORD *)(v4 + 18);
          *(_DWORD *)(v4 + 28) = 0;
        }
        ++v2;
      }
      while ( v2 < this->m_nXSize );
    }
  }
  m_nYSize = this->m_nYSize;
  if ( m_nYSize * this->m_nXSize > 0 )
    memset(this->m_lppItems, 0, 4 * m_nYSize * this->m_nXSize);
}

//----- (0045F630) --------------------------------------------------------
char __thiscall Item::CStallContainer::SetItem(Item::CStallContainer *this, unsigned int itemPos, Item::CItem *lpItem)
{
  Item::CArrayContainer *p_m_Inventory; // ecx
  CCharacter *m_lpOwner; // eax
  Item::CArrayContainer_vtbl *v6; // edx
  Item::ItemPos m_ItemPos; // ax
  Item::CItem_vtbl *v9; // edx
  unsigned int v10; // ebx
  Item::ItemPos v11; // ax
  unsigned int m_dwStallPrice; // eax
  int v13; // eax
  char *v14; // ecx
  CCharacter **m_lpCustomer; // edx
  unsigned int nItemSize; // [esp+10h] [ebp-80h] BYREF
  int v17; // [esp+14h] [ebp-7Ch]
  char szBuffer[113]; // [esp+18h] [ebp-78h] BYREF

  p_m_Inventory = &this->m_lpOwner->m_Inventory;
  LOWORD(nItemSize) = lpItem->m_ItemData.m_ItemPos;
  if ( !((unsigned __int8 (__thiscall *)(Item::CArrayContainer *, unsigned int, Item::CItem *))p_m_Inventory->SetItem)(
          p_m_Inventory,
          nItemSize,
          lpItem) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "Item::CStallContainer::SetItem",
      aDWorkRylSource_67,
      86,
      aCid0x08x_191,
      this->m_lpOwner->m_dwCID);
    return 0;
  }
  if ( !Item::CArrayContainer::SetItem(this, itemPos, lpItem) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "Item::CStallContainer::SetItem",
      aDWorkRylSource_67,
      92,
      aCid0x08x_136,
      this->m_lpOwner->m_dwCID);
    m_lpOwner = this->m_lpOwner;
    v6 = m_lpOwner->m_Inventory.__vftable;
    LOWORD(v17) = lpItem->m_ItemData.m_ItemPos;
    if ( !((unsigned __int8 (__thiscall *)(Item::CArrayContainer *, int))v6->RemoveItem)(&m_lpOwner->m_Inventory, v17) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_SYSERR,
        "Item::CStallContainer::SetItem",
        aDWorkRylSource_67,
        96,
        aCid0x08x_230,
        this->m_lpOwner->m_dwCID);
    return 0;
  }
  m_ItemPos = lpItem->m_ItemData.m_ItemPos;
  lpItem->m_ItemData.m_ItemPos = lpItem->m_itemPos_Real;
  lpItem->m_itemPos_Real = m_ItemPos;
  v9 = lpItem->__vftable;
  nItemSize = 52;
  v9->SerializeOut(lpItem, &szBuffer[57], &nItemSize);
  v10 = nItemSize;
  if ( !nItemSize )
    return 0;
  v11 = lpItem->m_ItemData.m_ItemPos;
  lpItem->m_ItemData.m_ItemPos = lpItem->m_itemPos_Real;
  lpItem->m_itemPos_Real = v11;
  m_dwStallPrice = lpItem->m_dwStallPrice;
  if ( !m_dwStallPrice )
    m_dwStallPrice = lpItem->m_dwPrice;
  *(_DWORD *)&szBuffer[v10 + 57] = m_dwStallPrice;
  v13 = 0;
  memset(&szBuffer[12], 0, 40);
  v14 = &szBuffer[12];
  m_lpCustomer = this->m_lpCustomer;
  do
  {
    if ( !*m_lpCustomer )
      break;
    *(_DWORD *)v14 = (*m_lpCustomer)->m_dwCID;
    ++v13;
    ++m_lpCustomer;
    v14 += 4;
  }
  while ( v13 < 10 );
  *(_DWORD *)&szBuffer[52] = v10;
  szBuffer[56] = 1;
  if ( PacketWrap::WrapCrypt(szBuffer, v10 + 61, 0x61u, 0, 0) )
    return Item::CStallContainer::SendAllCustomer(this, szBuffer, nItemSize + 61, 0, 0x61u);
  else
    return 0;
}

//----- (0045F830) --------------------------------------------------------
void __thiscall Item::CStallContainer::SwapPosAllItem(Item::CStallContainer *this)
{
  Item::CItem **m_lppItems; // edx
  Item::CItem **i; // edi
  Item::CItem *v3; // eax
  Item::ItemPos m_ItemPos; // si

  m_lppItems = this->m_lppItems;
  for ( i = &m_lppItems[this->m_nMaxSize]; m_lppItems != i; ++m_lppItems )
  {
    v3 = *m_lppItems;
    if ( *m_lppItems )
    {
      if ( this->m_lpNullItem != v3 )
      {
        m_ItemPos = v3->m_ItemData.m_ItemPos;
        v3->m_ItemData.m_ItemPos = v3->m_itemPos_Real;
        v3->m_itemPos_Real = m_ItemPos;
      }
    }
  }
}

//----- (0045F870) --------------------------------------------------------
char __thiscall Item::CStallContainer::Close(Item::CStallContainer *this)
{
  Item::CStallContainer *v1; // eax
  char *m_strStallName; // ebx
  CCharacter **m_lpCustomer; // esi
  int v4; // ebp
  CCharacter *v5; // edi
  CSendStream *m_lpGameClientDispatch; // eax
  CCharacter **v8; // [esp+Ch] [ebp-8h]

  v1 = this;
  m_strStallName = this->m_strStallName;
  if ( !strcmp(this->m_strStallName, szLoseCharName) || !this->m_lpOwner )
    return 0;
  m_lpCustomer = this->m_lpCustomer;
  v4 = 0;
  v8 = this->m_lpCustomer;
  while ( 1 )
  {
    v5 = *v8;
    if ( !*v8 )
      break;
    Item::CStallContainer::Leave(v1, *v8);
    m_lpGameClientDispatch = (CSendStream *)v5->m_lpGameClientDispatch;
    if ( !m_lpGameClientDispatch
      || (GameClientSendPacket::SendCharStallEnter(m_lpGameClientDispatch + 8, v5->m_dwCID, 0, 0), ++v4, ++v8, v4 >= 10) )
    {
      v1 = this;
      break;
    }
    v1 = this;
  }
  GAMELOG::LogStallOpenClose(v1->m_lpOwner, m_strStallName, 0);
  Item::CStallContainer::RollBackAllItem(this);
  *m_lpCustomer = 0;
  m_lpCustomer[1] = 0;
  m_lpCustomer[2] = 0;
  m_lpCustomer[3] = 0;
  m_lpCustomer[4] = 0;
  m_lpCustomer[5] = 0;
  m_lpCustomer[6] = 0;
  m_lpCustomer[7] = 0;
  m_lpCustomer[8] = 0;
  m_lpCustomer[9] = 0;
  *(_DWORD *)m_strStallName = 0;
  *((_DWORD *)m_strStallName + 1) = 0;
  *((_DWORD *)m_strStallName + 2) = 0;
  *((_DWORD *)m_strStallName + 3) = 0;
  *((_DWORD *)m_strStallName + 4) = 0;
  *((_DWORD *)m_strStallName + 5) = 0;
  *((_DWORD *)m_strStallName + 6) = 0;
  *((_DWORD *)m_strStallName + 7) = 0;
  return 1;
}

//----- (0045F970) --------------------------------------------------------
char __thiscall Item::CStallContainer::SendCharStallItemInfo(Item::CStallContainer *this, CCharacter *pCustomer)
{
  Item::CStallContainer_vtbl *v3; // edx
  int v4; // eax
  _DWORD *v5; // ecx
  CCharacter **m_lpCustomer; // esi
  CCharacter *m_dwCID; // edx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned __int8 cItemNum_Out; // [esp+9h] [ebp-11C5h] BYREF
  int v11; // [esp+Ah] [ebp-11C4h] BYREF
  char SourceData[12]; // [esp+Eh] [ebp-11C0h] BYREF
  _DWORD v13[11]; // [esp+1Ah] [ebp-11B4h] BYREF
  unsigned __int8 v14; // [esp+46h] [ebp-1188h]
  char szStallPriceBuffer_Out[4483]; // [esp+47h] [ebp-1187h] BYREF

  Item::CStallContainer::SwapPosAllItem(this);
  v3 = this->__vftable;
  v11 = 52 * this->m_nXSize * this->m_nYSize;
  v3->SerializeOut(this, szStallPriceBuffer_Out, (unsigned int *)&v11);
  Item::CStallContainer::SwapPosAllItem(this);
  cItemNum_Out = 0;
  Item::CStallContainer::StallPriceOut(this, (unsigned int *)&szStallPriceBuffer_Out[v11], &cItemNum_Out);
  memset(v13, 0, 40);
  v4 = 0;
  v5 = v13;
  m_lpCustomer = this->m_lpCustomer;
  do
  {
    m_dwCID = *m_lpCustomer;
    if ( !*m_lpCustomer )
      break;
    m_dwCID = (CCharacter *)m_dwCID->m_dwCID;
    *v5 = m_dwCID;
    ++v4;
    ++m_lpCustomer;
    ++v5;
  }
  while ( v4 < 10 );
  m_lpGameClientDispatch = pCustomer->m_lpGameClientDispatch;
  v13[10] = v11;
  v14 = cItemNum_Out;
  if ( !m_lpGameClientDispatch )
    return 0;
  LOWORD(m_dwCID) = cItemNum_Out;
  return CSendStream::WrapCompress(
           &m_lpGameClientDispatch->m_SendStream,
           SourceData,
           (char *)(v11 + 4 * (_DWORD)m_dwCID + 57),
           0x61u,
           0,
           0);
}

//----- (0045FA90) --------------------------------------------------------
char __thiscall Item::CStallContainer::SendRemoveItem(
        Item::CStallContainer *this,
        Item::ItemPos itemPos,
        unsigned __int8 cNum)
{
  unsigned int m_dwCID; // edx
  int v6; // [esp+7h] [ebp-21h]
  PktStRI pktStRI; // [esp+Ch] [ebp-1Ch] BYREF

  memset(&pktStRI.m_TakeType, 0, 9);
  m_dwCID = this->m_lpOwner->m_dwCID;
  LOWORD(v6) = itemPos;
  HIWORD(v6) = itemPos;
  pktStRI.m_TakeType.m_cNum = cNum;
  pktStRI.m_dwCharID = m_dwCID;
  *(_DWORD *)&pktStRI.m_TakeType.m_srcPos = v6;
  pktStRI.m_cCmd = 2;
  if ( PacketWrap::WrapCrypt((char *)&pktStRI, 0x1Au, 0x5Fu, 0, 0) )
    return Item::CStallContainer::SendAllCustomer(this, (char *)&pktStRI, 0x1Au, 1, 0x5Fu);
  else
    return 0;
}

//----- (0045FB30) --------------------------------------------------------
char __thiscall Item::CStallContainer::SendCharStallEnter(
        Item::CStallContainer *this,
        unsigned int dwCustomerID,
        unsigned int dwOwnerID)
{
  PktStE pktStE; // [esp+4h] [ebp-14h] BYREF

  pktStE.m_dwCustomerID = dwCustomerID;
  pktStE.m_dwOwnerID = dwOwnerID;
  if ( PacketWrap::WrapCrypt((char *)&pktStE, 0x14u, 0x60u, 0, 0) )
    return Item::CStallContainer::SendAllCustomer(this, (char *)&pktStE, 0x14u, 1, 0x60u);
  else
    return 0;
}

//----- (0045FB90) --------------------------------------------------------
void __thiscall Item::CStallContainer::CStallContainer(Item::CStallContainer *this)
{
  Item::CArrayContainer::CArrayContainer(this);
  this->__vftable = (Item::CStallContainer_vtbl *)&Item::CStallContainer::`vftable';
  this->m_lpOwner = 0;
  this->m_lpOtherOwner = 0;
  this->m_lpCustomer[0] = 0;
  this->m_lpCustomer[1] = 0;
  this->m_lpCustomer[2] = 0;
  this->m_lpCustomer[3] = 0;
  this->m_lpCustomer[4] = 0;
  this->m_lpCustomer[5] = 0;
  this->m_lpCustomer[6] = 0;
  this->m_lpCustomer[7] = 0;
  this->m_lpCustomer[8] = 0;
  this->m_lpCustomer[9] = 0;
  *(_DWORD *)this->m_strStallName = 0;
  *(_DWORD *)&this->m_strStallName[4] = 0;
  *(_DWORD *)&this->m_strStallName[8] = 0;
  *(_DWORD *)&this->m_strStallName[12] = 0;
  *(_DWORD *)&this->m_strStallName[16] = 0;
  *(_DWORD *)&this->m_strStallName[20] = 0;
  *(_DWORD *)&this->m_strStallName[24] = 0;
  *(_DWORD *)&this->m_strStallName[28] = 0;
}
// 4E4528: using guessed type void *Item::CStallContainer::`vftable';

//----- (0045FBF0) --------------------------------------------------------
Item::CStallContainer *__thiscall Item::CStallContainer::`vector deleting destructor'(
        Item::CStallContainer *this,
        char a2)
{
  Item::CStallContainer::~CStallContainer(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0045FC10) --------------------------------------------------------
char __thiscall Item::CStallContainer::RemoveItem(Item::CStallContainer *this, unsigned int itemPos)
{
  int v3; // eax
  int v4; // esi
  unsigned __int8 v5; // al

  v3 = ((int (__thiscall *)(Item::CStallContainer *, unsigned int))this->GetItem)(this, itemPos);
  v4 = v3;
  if ( !v3 )
    return 0;
  v5 = (*(_BYTE *)(*(_DWORD *)(v3 + 4) + 32) & 8) == 8 ? *(_BYTE *)(v3 + 21) : 1;
  if ( Item::CStallContainer::SendRemoveItem(this, (Item::ItemPos)itemPos, v5) != 1 )
    return 0;
  *(_DWORD *)(v4 + 28) = 0;
  if ( Item::CArrayContainer::RemoveItem(this, itemPos) != 1 )
    return 0;
  if ( (*(_WORD *)(v4 + 18) & 0xF) != 6 )
    *(_WORD *)(v4 + 22) = *(_WORD *)(v4 + 18);
  return 1;
}

//----- (0045FC90) --------------------------------------------------------
char __thiscall Item::CStallContainer::Enter(Item::CStallContainer *this, CCharacter *lpCustomer)
{
  int v3; // eax
  CCharacter **i; // ecx

  if ( !lpCustomer || !this->m_lpOwner )
    return 0;
  v3 = 0;
  for ( i = this->m_lpCustomer; *i; ++i )
  {
    if ( ++v3 >= 10 )
      return 0;
  }
  this->m_lpCustomer[v3] = lpCustomer;
  lpCustomer->m_Stall.m_lpOtherOwner = this->m_lpOwner;
  Item::CStallContainer::SendCharStallItemInfo(this, lpCustomer);
  GAMELOG::LogStallEnterLeave(this->m_lpOwner, lpCustomer->m_dwCID, 1);
  return 1;
}

//----- (0045FCF0) --------------------------------------------------------
unsigned __int16 __thiscall Guild::CGuild::SetMark(
        Guild::CGuild *this,
        unsigned int dwSenderID,
        char *szMark,
        unsigned int dwGold)
{
  CCreatureManager *Instance; // eax
  PktGuildMark pktGM; // [esp+10h] [ebp-1D8h] BYREF

  qmemcpy(this->m_szMark, szMark, sizeof(this->m_szMark));
  pktGM.m_dwCID = dwSenderID;
  this->m_dwGold = dwGold;
  pktGM.m_dwGID = this->m_dwGID;
  qmemcpy(pktGM.m_szMark, this->m_szMark, sizeof(pktGM.m_szMark));
  pktGM.m_dwGold = dwGold;
  if ( PacketWrap::WrapCrypt((char *)&pktGM, 0x1C9u, 0x8Au, 0, 0) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, (char *)&pktGM, 0x1C9u, 0x8Au, 1);
  }
  return 0;
}

//----- (0045FDA0) --------------------------------------------------------
char __thiscall Guild::CGuild::SetInclination(Guild::CGuild *this, unsigned __int8 cInclination)
{
  unsigned __int16 wMonth; // cx
  unsigned __int16 wDay; // dx
  unsigned int wMilliseconds; // eax
  unsigned __int16 wMinute; // cx
  unsigned __int16 wSecond; // dx
  unsigned int m_dwGID; // ecx
  CCreatureManager *Instance; // eax
  _SYSTEMTIME systemTime; // [esp+4h] [ebp-24h] BYREF
  PktGuildInclination pktGI; // [esp+14h] [ebp-14h] BYREF

  memset(&systemTime, 0, 12);
  this->m_cInclination = cInclination;
  *(_DWORD *)&systemTime.wSecond = 0;
  GetLocalTime(&systemTime);
  wMonth = systemTime.wMonth;
  wDay = systemTime.wDay;
  this->m_tmChangeInclination.Year = systemTime.wYear;
  this->m_tmChangeInclination.Hour = systemTime.wHour;
  wMilliseconds = systemTime.wMilliseconds;
  this->m_tmChangeInclination.Month = wMonth;
  wMinute = systemTime.wMinute;
  this->m_tmChangeInclination.Day = wDay;
  wSecond = systemTime.wSecond;
  this->m_tmChangeInclination.MSecond = wMilliseconds;
  this->m_tmChangeInclination.Minute = wMinute;
  m_dwGID = this->m_dwGID;
  this->m_tmChangeInclination.Second = wSecond;
  LOBYTE(wSecond) = this->m_cInclination;
  pktGI.m_dwUID = m_dwGID;
  pktGI.m_cInclination = wSecond;
  if ( PacketWrap::WrapCrypt((char *)&pktGI, 0x11u, 0xA3u, 0, 0) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, (char *)&pktGI, 0x11u, 0xA3u, 1);
  }
  return 1;
}

//----- (0045FE70) --------------------------------------------------------
char __thiscall Guild::CGuild::AddGold(Guild::CGuild *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax

  m_dwGold = this->m_dwGold;
  if ( m_dwGold >= -1 - dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::AddGold",
      aDWorkRylSource_47,
      938,
      aGid0x08x_2,
      this->m_dwGID,
      dwGold);
    return 0;
  }
  else
  {
    this->m_dwGold = dwGold + m_dwGold;
    return 1;
  }
}

//----- (0045FEC0) --------------------------------------------------------
char __thiscall Guild::CGuild::DeductGold(Guild::CGuild *this, unsigned int dwGold)
{
  unsigned int m_dwGold; // eax

  m_dwGold = this->m_dwGold;
  if ( dwGold > m_dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::DeductGold",
      aDWorkRylSource_47,
      950,
      aGid0x08x_15,
      this->m_dwGID,
      dwGold);
    return 0;
  }
  else
  {
    this->m_dwGold = m_dwGold - dwGold;
    return 1;
  }
}

//----- (0045FF10) --------------------------------------------------------
Guild::MemberInfo *__thiscall std::vector<Guild::MemberInfo>::size(std::vector<Guild::MemberInfo> *this)
{
  Guild::MemberInfo *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (Guild::MemberInfo *)(this->_Mylast - result);
  return result;
}

//----- (0045FF40) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (0045FF60) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (0045FFC0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Parent; // eax

  Ptr = this->_Ptr;
  if ( this->_Ptr->_Isnil )
  {
    this->_Ptr = Ptr->_Right;
  }
  else
  {
    Left = Ptr->_Left;
    if ( Ptr->_Left->_Isnil )
    {
      Parent = Ptr->_Parent;
      if ( !Parent->_Isnil )
      {
        do
        {
          if ( this->_Ptr != Parent->_Left )
            break;
          this->_Ptr = Parent;
          Parent = Parent->_Parent;
        }
        while ( !Parent->_Isnil );
        if ( !Parent->_Isnil )
          this->_Ptr = Parent;
      }
    }
    else
    {
      for ( i = Left->_Right; !i->_Isnil; i = i->_Right )
        Left = i;
      this->_Ptr = Left;
    }
  }
}

//----- (00460020) --------------------------------------------------------
_DWORD *__thiscall std::pair<unsigned long const,Guild::RelationInfo>::pair<unsigned long const,Guild::RelationInfo>(
        _DWORD *this,
        int a2)
{
  _DWORD *result; // eax

  result = this;
  *this = *(_DWORD *)a2;
  this[1] = *(_DWORD *)(a2 + 4);
  this[2] = *(_DWORD *)(a2 + 8);
  this[3] = *(_DWORD *)(a2 + 12);
  this[4] = *(_DWORD *)(a2 + 16);
  *((_WORD *)this + 10) = *(_WORD *)(a2 + 20);
  return result;
}

//----- (00460060) --------------------------------------------------------
void __cdecl std::fill<Guild::MemberInfo *,Guild::MemberInfo>(
        Guild::MemberInfo *_First,
        Guild::MemberInfo *_Last,
        const Guild::MemberInfo *_Val)
{
  Guild::MemberInfo *i; // eax
  Guild::MemberInfo *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy(v4, _Val, sizeof(Guild::MemberInfo));
  }
}

//----- (00460090) --------------------------------------------------------
void __thiscall std::pair<unsigned long,Guild::RelationInfo>::pair<unsigned long,Guild::RelationInfo>(
        std::pair<unsigned long,Guild::RelationInfo> *this,
        const unsigned int *_Val1,
        const Guild::RelationInfo *_Val2)
{
  *this = *(std::pair<unsigned long,Guild::RelationInfo> *)_Val1;
}

//----- (004600D0) --------------------------------------------------------
void __thiscall ATL::CTime::CTime(
        ATL::CTime *this,
        int nYear,
        int nMonth,
        int nDay,
        int nHour,
        int nMin,
        int nSec,
        int nDST)
{
  int v9; // eax
  int v10; // edx
  int v11; // edx
  tm atm; // [esp+4h] [ebp-24h] BYREF

  atm.tm_min = nMin;
  atm.tm_sec = nSec;
  atm.tm_hour = nHour;
  atm.tm_mon = nMonth - 1;
  atm.tm_mday = nDay;
  atm.tm_year = nYear - 1900;
  atm.tm_isdst = nDST;
  v9 = _mktime64(&atm);
  HIDWORD(this->m_time) = v10;
  v11 = HIDWORD(this->m_time) & v9;
  LODWORD(this->m_time) = v9;
  if ( v11 == -1 )
    ATL::AtlThrow(-2147024809);
}
// 46011F: variable 'v10' is possibly undefined

//----- (00460150) --------------------------------------------------------
void __thiscall GuildSmallInfoNode::GuildSmallInfoNode(
        GuildSmallInfoNode *this,
        unsigned int dwGID,
        unsigned __int8 cIndex,
        unsigned __int8 cInclination,
        unsigned __int16 wRank,
        unsigned int dwFame,
        unsigned __int8 cLevel,
        unsigned __int8 cCurrentMemberNum,
        const char *szGuildName,
        const char *szMasterName)
{
  this->m_dwGID = dwGID;
  this->m_wRank = wRank;
  this->m_cCurrentMemberNum = cCurrentMemberNum;
  this->m_cIndex = cIndex;
  this->m_cInclination = cInclination;
  this->m_dwFame = dwFame;
  this->m_cLevel = cLevel;
  if ( szGuildName )
    strncpy(this->m_szName, szGuildName, 0xBu);
  if ( szMasterName )
    strncpy(this->m_szMasterName, szMasterName, 0x10u);
}

//----- (004601C0) --------------------------------------------------------
void __thiscall GuildLargeInfoNode::GuildLargeInfoNode(
        GuildLargeInfoNode *this,
        unsigned int dwGID,
        unsigned __int8 cIndex,
        unsigned __int8 cInclination,
        unsigned __int16 wRank,
        unsigned int dwFame,
        unsigned __int8 cLevel,
        unsigned __int8 cCurrentMemberNum,
        const char *szMasterName,
        const char *szGuildName,
        const char *szMark,
        unsigned __int8 cRelation)
{
  GuildSmallInfoNode::GuildSmallInfoNode(
    this,
    dwGID,
    cIndex,
    cInclination,
    wRank,
    dwFame,
    cLevel,
    cCurrentMemberNum,
    szGuildName,
    szMasterName);
  this->m_cRelation = cRelation;
  if ( szMark )
    qmemcpy(this->m_szMark, szMark, sizeof(this->m_szMark));
}

//----- (00460220) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00460260) --------------------------------------------------------
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::make_pair<unsigned long,Guild::RelationInfo>(
        std::pair<unsigned long,Guild::RelationInfo> *result,
        unsigned int _Val1,
        Guild::RelationInfo _Val2)
{
  std::pair<unsigned long,Guild::RelationInfo> *v3; // eax

  v3 = result;
  result->first = _Val1;
  result->second = _Val2;
  return v3;
}

//----- (004602A0) --------------------------------------------------------
Guild::MemberInfo *__cdecl std::copy_backward<Guild::MemberInfo *,Guild::MemberInfo *>(
        Guild::MemberInfo *_First,
        Guild::MemberInfo *_Last,
        Guild::MemberInfo *_Dest)
{
  Guild::MemberInfo *v3; // edx
  Guild::MemberInfo *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy(--result, --v3, sizeof(Guild::MemberInfo));
  return result;
}

//----- (004602D0) --------------------------------------------------------
Guild::MemberInfo *__cdecl std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(
        Guild::MemberInfo *_First,
        Guild::MemberInfo *_Last,
        Guild::MemberInfo *_Dest)
{
  Guild::MemberInfo *v3; // edx
  Guild::MemberInfo *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
      qmemcpy(result, v3, sizeof(Guild::MemberInfo));
    ++v3;
  }
  return result;
}

//----- (00460300) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo>(
        std::vector<Guild::MemberInfo>::iterator _First,
        std::vector<Guild::MemberInfo>::iterator _Mid,
        std::vector<Guild::MemberInfo>::iterator _Last)
{
  int v3; // ebp
  int v4; // esi
  int v5; // edi
  int v6; // edx
  int v7; // ebx
  Guild::MemberInfo *v8; // edx
  Guild::MemberInfo *v9; // eax
  std::vector<Guild::MemberInfo>::iterator *p_First; // ecx
  Guild::MemberInfo *Myptr; // ebx
  int v12; // eax
  Guild::MemberInfo **v13; // eax
  bool v14; // zf
  std::vector<Guild::MemberInfo>::iterator _Next; // [esp+Ch] [ebp-54h]
  int v16; // [esp+14h] [ebp-4Ch]
  Guild::MemberInfo *v17; // [esp+18h] [ebp-48h] BYREF
  Guild::MemberInfo *v18; // [esp+1Ch] [ebp-44h] BYREF
  Guild::MemberInfo *v19; // [esp+20h] [ebp-40h] BYREF
  Guild::MemberInfo _Holeval; // [esp+24h] [ebp-3Ch] BYREF

  v3 = _Mid._Myptr - _First._Myptr;
  v4 = _Last._Myptr - _First._Myptr;
  v5 = v3;
  if ( v3 )
  {
    do
    {
      v6 = v4 % v5;
      v4 = v5;
      v5 = v6;
    }
    while ( v6 );
  }
  if ( v4 < _Last._Myptr - _First._Myptr && v4 > 0 )
  {
    v7 = v3;
    v8 = &_First._Myptr[v4];
    _Mid._Myptr = v8;
    v16 = v4;
    while ( 1 )
    {
      qmemcpy(&_Holeval, v8, sizeof(_Holeval));
      v9 = v8;
      if ( &v8[v7] == _Last._Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v17 = &v8[v7];
        p_First = (std::vector<Guild::MemberInfo>::iterator *)&v17;
      }
      Myptr = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          qmemcpy(v9, Myptr, sizeof(Guild::MemberInfo));
          v12 = _Last._Myptr - Myptr;
          _Next._Myptr = Myptr;
          if ( v3 >= v12 )
          {
            v18 = &_First._Myptr[v3 - v12];
            v13 = &v18;
          }
          else
          {
            v19 = &Myptr[v3];
            v13 = &v19;
          }
          Myptr = *v13;
          v14 = *v13 == _Mid._Myptr;
          v9 = _Next._Myptr;
        }
        while ( !v14 );
        v8 = _Mid._Myptr;
      }
      --v8;
      v14 = v16 == 1;
      qmemcpy(v9, &_Holeval, sizeof(Guild::MemberInfo));
      _Mid._Myptr = v8;
      --v16;
      if ( v14 )
        break;
      v7 = v3;
    }
  }
}

//----- (00460460) --------------------------------------------------------
int __thiscall GuildRight::IsValid(GuildRight *this)
{
  int result; // eax
  int v2; // ecx
  GuildRight MaxRight; // [esp+4h] [ebp-38h] BYREF

  memset(&MaxRight, 0, sizeof(MaxRight));
  MaxRight.m_aryRight[5] = 3;
  MaxRight.m_aryRight[0] = 3;
  MaxRight.m_aryRight[1] = 3;
  MaxRight.m_aryRight[2] = 3;
  MaxRight.m_aryRight[3] = 3;
  MaxRight.m_aryRight[4] = 3;
  MaxRight.m_aryRight[6] = 2;
  MaxRight.m_aryRight[7] = 2;
  MaxRight.m_aryRight[8] = 2;
  MaxRight.m_aryRight[9] = 2;
  MaxRight.m_aryRight[10] = 1;
  MaxRight.m_aryRight[11] = 1;
  MaxRight.m_aryRight[12] = 1;
  MaxRight.m_aryRight[13] = 1;
  result = 0;
  v2 = (char *)this - (char *)&MaxRight;
  do
  {
    if ( MaxRight.m_aryRight[v2 + result] > MaxRight.m_aryRight[result] )
      break;
    ++result;
  }
  while ( result < 50 );
  return result;
}

//----- (00460500) --------------------------------------------------------
int __thiscall Guild::CGuild::GetCurrentMemberNum(Guild::CGuild *this)
{
  Guild::MemberInfo *Mylast; // edx
  Guild::MemberInfo *Myfirst; // ecx
  int result; // eax

  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  for ( result = 0; Myfirst != Mylast; ++Myfirst )
  {
    if ( Myfirst->m_MemberListInfo.m_cTitle != 5 )
      ++result;
  }
  return result;
}

//----- (00460530) --------------------------------------------------------
char __thiscall Guild::CGuild::JoinMember(
        Guild::CGuild *this,
        unsigned int dwCID,
        unsigned __int8 cTitle,
        unsigned __int16 *wError)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo *Myfirst; // eax
  unsigned __int8 i; // dl
  CSingleDispatch *DispatchTable; // eax
  char v12; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v14; // [esp+18h] [ebp-4h]

  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, dwCID);
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::JoinMember",
      aDWorkRylSource_47,
      1319,
      aGid0x08x_24,
      this->m_dwGID,
      dwCID);
    *wError = 1;
    return 0;
  }
  if ( Character->GetGID(Character) )
  {
    *wError = 4;
    return 0;
  }
  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  for ( i = 0; Myfirst != Mylast; ++Myfirst )
  {
    if ( Myfirst->m_MemberListInfo.m_cTitle != 5 )
      ++i;
  }
  if ( ms_aryMaxMemberNum[this->m_cLevel] > i )
  {
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v14 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      v12 = GameClientSendPacket::SendCharGuildCmd(
              (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
              this->m_dwGID,
              dwCID,
              cTitle,
              &szGuildName,
              this->m_strName,
              1u,
              0);
      v14 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return v12;
    }
    *wError = 1;
    v14 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
  else
  {
    *wError = 5;
  }
  return 0;
}

//----- (004606B0) --------------------------------------------------------
bool __thiscall Guild::CGuild::JoinMemberDB(Guild::CGuild *this, GuildMemberDB *guildMemberDB)
{
  Guild::MemberInfo *Mylast; // esi
  Guild::MemberInfo *Myfirst; // eax
  unsigned __int8 i; // cl
  GuildMemberDB *v7; // esi
  unsigned __int8 m_cLevel; // cl
  char m_dwTitle; // al
  unsigned int m_dwFame; // ebp
  unsigned int m_dwCID; // eax
  unsigned int m_dwGID; // eax
  unsigned int m_dwRank; // edx
  unsigned int v14; // eax
  int (__thiscall **v15)(Guild::CGuild *, Guild::MemberInfo *); // eax
  unsigned int memberDetailInfo_4; // [esp+10h] [ebp-40h]
  Guild::MemberInfo memberInfo; // [esp+14h] [ebp-3Ch] BYREF
  GuildMemberDB *guildMemberDBa; // [esp+54h] [ebp+4h]

  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  for ( i = 0; Myfirst != Mylast; ++Myfirst )
  {
    if ( Myfirst->m_MemberListInfo.m_cTitle != 5 )
      ++i;
  }
  if ( i < ms_aryMaxMemberNum[this->m_cLevel] )
  {
    v7 = guildMemberDB;
    m_cLevel = guildMemberDB->m_cLevel;
    m_dwTitle = guildMemberDB->m_dwTitle;
    LOBYTE(guildMemberDBa) = guildMemberDB->m_dwRank;
    BYTE2(guildMemberDBa) = m_cLevel;
    HIBYTE(guildMemberDBa) = v7->m_wClass;
    memset(memberInfo.m_strName, 0, 41);
    m_dwFame = v7->m_dwFame;
    BYTE1(guildMemberDBa) = m_dwTitle;
    memberDetailInfo_4 = v7->m_dwGold;
    m_dwCID = v7->m_dwCID;
    memberInfo.m_MemberDetailInfo.m_dwFame = 0;
    memberInfo.m_MemberDetailInfo.m_dwGold = 0;
    memberInfo.m_dwCID = m_dwCID;
    strncpy(memberInfo.m_strName, v7->m_strName, 0x10u);
    v7 = (GuildMemberDB *)((char *)v7 + 43);
    m_dwGID = v7->m_dwGID;
    m_dwRank = v7->m_dwRank;
    *(_DWORD *)&memberInfo.m_LeaveGuildTime.Day = v7->m_dwCID;
    *(_DWORD *)&memberInfo.m_LeaveGuildTime.Year = m_dwGID;
    v14 = *(_DWORD *)v7->m_strName;
    memberInfo.m_MemberListInfo = (Guild::MemberListInfo)guildMemberDBa;
    *(_DWORD *)&memberInfo.m_LeaveGuildTime.Minute = m_dwRank;
    memberInfo.m_LeaveGuildTime.MSecond = v14;
    v15 = (int (__thiscall **)(Guild::CGuild *, Guild::MemberInfo *))this->__vftable;
    memberInfo.m_dwServerID = 0;
    memberInfo.m_MemberDetailInfo.m_dwFame = m_dwFame;
    memberInfo.m_MemberDetailInfo.m_dwGold = memberDetailInfo_4;
    return v15[1](this, &memberInfo);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::JoinMemberDB",
      aDWorkRylSource_47,
      1361,
      aGid0x08x_1,
      this->m_dwGID);
    return 0;
  }
}

//----- (00460820) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Rarg,
        const std::pair<unsigned long const ,Guild::RelationInfo> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *)operator new((tagHeader *)0x28);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Parent = _Parg;
    result->_Right = _Rarg;
    result->_Myval = *_Val;
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (00460880) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(
        Guild::MemberInfo *_First,
        unsigned int _Count,
        const Guild::MemberInfo *_Val)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        qmemcpy(_First, _Val, sizeof(Guild::MemberInfo));
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (004608B0) --------------------------------------------------------
char __thiscall Guild::CGuild::SetTitle(
        Guild::CGuild *this,
        unsigned int dwSuperior,
        unsigned int dwFollower,
        unsigned __int8 cTitle,
        unsigned __int16 *wError)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo *Myfirst; // eax
  Guild::MemberInfo *v9; // edi
  Guild::MemberInfo *v10; // ebp
  unsigned int v11; // ebx
  unsigned __int8 m_cTitle; // al
  CSingleDispatch *DispatchTable; // eax
  char v14; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+0h] [ebp-14h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v17; // [esp+8h] [ebp-Ch]
  void *v18; // [esp+Ch] [ebp-8h]
  int v19; // [esp+10h] [ebp-4h]

  v19 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v18 = &_ehhandler__UpdateMemberInfo_CGuild_Guild__QAE_NKKE_Z;
  v17 = ExceptionList;
  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  v9 = Mylast;
  v10 = Mylast;
  if ( Myfirst == Mylast )
  {
    v11 = dwFollower;
  }
  else
  {
    do
    {
      if ( dwSuperior == Myfirst->m_dwCID )
        v9 = Myfirst;
      v11 = dwFollower;
      if ( dwFollower == Myfirst->m_dwCID )
        v10 = Myfirst;
      ++Myfirst;
    }
    while ( Myfirst != Mylast );
  }
  if ( v9 == Mylast || v10 == Mylast )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetTitle",
      aDWorkRylSource_47,
      427,
      aGid0x08x_9,
      this->m_dwGID,
      dwSuperior,
      v11);
    goto LABEL_26;
  }
  m_cTitle = v10->m_MemberListInfo.m_cTitle;
  if ( m_cTitle == 2 && v9->m_MemberListInfo.m_cTitle != 1 && dwSuperior != v11 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetTitle",
      aDWorkRylSource_47,
      441,
      aCid0x08x_323,
      v9->m_dwCID,
      v10->m_dwCID);
LABEL_26:
    *wError = 1;
    return 0;
  }
  if ( cTitle == 2 && v9->m_MemberListInfo.m_cTitle != 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetTitle",
      aDWorkRylSource_47,
      452,
      aCid0x08x_330,
      v9->m_dwCID);
LABEL_21:
    *wError = 1;
    return 0;
  }
  if ( m_cTitle == 5 && v9->m_MemberListInfo.m_cTitle > this->m_GuildRight.m_aryRight[4] )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetTitle",
      aDWorkRylSource_47,
      463,
      aCid0x08x_95,
      v9->m_dwCID);
    goto LABEL_21;
  }
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v19 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v14 = GameClientSendPacket::SendCharGuildCmd(
            (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
            this->m_dwGID,
            dwFollower,
            cTitle,
            &byte_4E4920,
            this->m_strName,
            6u,
            0);
    v19 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return v14;
  }
  *wError = 1;
  v19 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  return 0;
}

//----- (00460A90) --------------------------------------------------------
Guild::MemberInfo *__thiscall Guild::CGuild::GetMaster(Guild::CGuild *this, Guild::MemberInfo *result)
{
  Guild::MemberInfo *Myfirst; // eax
  Guild::MemberInfo *v3; // eax
  Guild::MemberInfo *v4; // esi
  Guild::MemberInfo *Mylast; // eax

  Myfirst = this->m_MemberList._Myfirst;
  if ( Myfirst && this->m_MemberList._Mylast - Myfirst )
  {
    v4 = this->m_MemberList._Myfirst;
    Mylast = this->m_MemberList._Mylast;
    if ( v4 == Mylast )
    {
LABEL_7:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuild::GetMaster",
        aDWorkRylSource_47,
        874,
        aGid0x08x_16,
        this->m_dwGID);
      v3 = result;
      result->m_dwCID = 0;
      result->m_dwServerID = 0;
      result->m_cUpdateCount = 0;
      result->m_MemberListInfo.m_cRank = 0;
      result->m_MemberListInfo.m_cTitle = 0;
      result->m_MemberListInfo.m_cLevel = 0;
      result->m_MemberListInfo.m_cClass = 0;
      result->m_MemberDetailInfo.m_dwFame = 0;
      result->m_MemberDetailInfo.m_dwGold = 0;
      *(_DWORD *)result->m_strName = 0;
      *(_DWORD *)&result->m_strName[4] = 0;
      *(_DWORD *)&result->m_strName[8] = 0;
      *(_DWORD *)&result->m_strName[12] = 0;
      *(_DWORD *)&result->m_LeaveGuildTime.Year = 0;
      *(_DWORD *)&result->m_LeaveGuildTime.Day = 0;
      *(_DWORD *)&result->m_LeaveGuildTime.Minute = 0;
      result->m_LeaveGuildTime.MSecond = 0;
    }
    else
    {
      while ( v4->m_MemberListInfo.m_cTitle != 1 )
      {
        if ( ++v4 == Mylast )
          goto LABEL_7;
      }
      v3 = result;
      qmemcpy(result, v4, sizeof(Guild::MemberInfo));
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::GetMaster",
      aDWorkRylSource_47,
      860,
      aGid0x08x_11,
      this->m_dwGID);
    v3 = result;
    result->m_dwCID = 0;
    result->m_dwServerID = 0;
    result->m_cUpdateCount = 0;
    result->m_MemberListInfo.m_cRank = 0;
    result->m_MemberListInfo.m_cTitle = 0;
    result->m_MemberListInfo.m_cLevel = 0;
    result->m_MemberListInfo.m_cClass = 0;
    result->m_MemberDetailInfo.m_dwFame = 0;
    result->m_MemberDetailInfo.m_dwGold = 0;
    *(_DWORD *)result->m_strName = 0;
    *(_DWORD *)&result->m_strName[4] = 0;
    *(_DWORD *)&result->m_strName[8] = 0;
    *(_DWORD *)&result->m_strName[12] = 0;
    *(_DWORD *)&result->m_LeaveGuildTime.Year = 0;
    *(_DWORD *)&result->m_LeaveGuildTime.Day = 0;
    *(_DWORD *)&result->m_LeaveGuildTime.Minute = 0;
    result->m_LeaveGuildTime.MSecond = 0;
  }
  return v3;
}

//----- (00460BD0) --------------------------------------------------------
unsigned __int8 __thiscall Guild::CGuild::GetTitle(Guild::CGuild *this, unsigned int dwCID)
{
  Guild::MemberInfo *Myfirst; // esi
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    return 6;
  while ( dwCID != Myfirst->m_dwCID )
  {
    if ( ++Myfirst == Mylast )
      return 6;
  }
  qmemcpy(&memberInfo, Myfirst, sizeof(memberInfo));
  return memberInfo.m_MemberListInfo.m_cTitle;
}

//----- (00460C30) --------------------------------------------------------
void __thiscall Guild::CGuild::ReleaseGold(Guild::CGuild *this, unsigned int dwGold)
{
  Guild::MemberInfo *i; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // [esp-Ch] [ebp-50h]
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
  {
    if ( i->m_MemberListInfo.m_cTitle != 5 )
    {
      qmemcpy(&memberInfo, i, sizeof(memberInfo));
      m_dwCID = memberInfo.m_dwCID;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
      if ( Character )
        CCharacter::AddGold(Character, dwGold, 0);
    }
  }
}

//----- (00460CB0) --------------------------------------------------------
void __thiscall Guild::CGuild::UpgradeMemberRespawnSpeedByEmblem(
        Guild::CGuild *this,
        unsigned __int8 cUpgradeType,
        unsigned __int8 cUpgradeStep)
{
  Guild::MemberInfo *i; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // [esp-Ch] [ebp-50h]
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
  {
    qmemcpy(&memberInfo, i, sizeof(memberInfo));
    if ( memberInfo.m_MemberListInfo.m_cTitle != 5 )
    {
      m_dwCID = memberInfo.m_dwCID;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
      if ( Character )
        CCharacter::UpgradeRespawnSpeedByEmblem(Character, cUpgradeType, cUpgradeStep);
    }
  }
}

//----- (00460D30) --------------------------------------------------------
void __thiscall Guild::CGuild::DegradeMemberRespawnSpeedByEmblem(Guild::CGuild *this)
{
  Guild::MemberInfo *i; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // [esp-Ch] [ebp-50h]
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
  {
    qmemcpy(&memberInfo, i, sizeof(memberInfo));
    if ( memberInfo.m_MemberListInfo.m_cTitle != 5 )
    {
      m_dwCID = memberInfo.m_dwCID;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
      if ( Character )
        CCharacter::DegradeRespawnSpeedByEmblem(Character);
    }
  }
}

//----- (00460DA0) --------------------------------------------------------
char __thiscall Guild::CGuild::InviteMember(
        Guild::CGuild *this,
        unsigned int dwMember,
        unsigned int dwGuest,
        unsigned __int16 *wError)
{
  Guild::MemberInfo *Mylast; // eax
  Guild::MemberInfo *Myfirst; // esi
  unsigned int v7; // edx
  unsigned __int8 v9; // al
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v12; // edi
  Guild::MemberInfo *v13; // esi
  Guild::MemberInfo *v14; // eax
  unsigned __int8 i; // cl
  CSendStream *m_lpGameClientDispatch; // eax
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  if ( Myfirst == Mylast )
  {
    v7 = dwMember;
  }
  else
  {
    do
    {
      v7 = dwMember;
      if ( dwMember == Myfirst->m_dwCID )
        break;
      ++Myfirst;
    }
    while ( Myfirst != Mylast );
  }
  if ( Myfirst == Mylast )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::InviteMember",
      aDWorkRylSource_47,
      1259,
      aGid0x08x_19,
      this->m_dwGID,
      v7);
    *wError = 1;
    return 0;
  }
  v9 = this->m_GuildRight.m_aryRight[3];
  qmemcpy(&memberInfo, Myfirst, sizeof(memberInfo));
  if ( memberInfo.m_MemberListInfo.m_cTitle > v9 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::InviteMember",
      aDWorkRylSource_47,
      1268,
      aGid0x08x_21,
      this->m_dwGID,
      v7,
      memberInfo.m_MemberListInfo.m_cTitle,
      v9);
    *wError = 1;
    return 0;
  }
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, dwGuest);
  v12 = Character;
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::InviteMember",
      aDWorkRylSource_47,
      1277,
      aGid0x08x_26,
      this->m_dwGID,
      dwMember,
      dwGuest);
LABEL_12:
    *wError = 1;
    return 0;
  }
  if ( Character->GetGID(Character) )
  {
    *wError = 4;
    return 0;
  }
  else
  {
    v13 = this->m_MemberList._Mylast;
    v14 = this->m_MemberList._Myfirst;
    for ( i = 0; v14 != v13; ++v14 )
    {
      if ( v14->m_MemberListInfo.m_cTitle != 5 )
        ++i;
    }
    if ( ms_aryMaxMemberNum[this->m_cLevel] > i )
    {
      m_lpGameClientDispatch = (CSendStream *)v12->m_lpGameClientDispatch;
      if ( !m_lpGameClientDispatch )
        goto LABEL_12;
      return GameClientSendPacket::SendCharGuildCmd(
               m_lpGameClientDispatch + 8,
               this->m_dwGID,
               dwGuest,
               dwMember,
               v12->m_DBData.m_Info.Name,
               this->m_strName,
               0,
               0);
    }
    else
    {
      *wError = 5;
      return 0;
    }
  }
}

//----- (00460FB0) --------------------------------------------------------
char __thiscall Guild::CGuild::KickMember(
        Guild::CGuild *this,
        unsigned int dwSuperior,
        unsigned int dwFollower,
        unsigned __int16 *wError)
{
  Guild::MemberInfo *Mylast; // ecx
  Guild::MemberInfo *Myfirst; // eax
  unsigned int v9; // ebp
  Guild::MemberInfo *v10; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v13; // esi
  int v14; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  CSingleDispatch *DispatchTable; // eax
  char v17; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-4Ch] BYREF
  Guild::MemberInfo result; // [esp+10h] [ebp-44h] BYREF
  int v20; // [esp+50h] [ebp-4h]
  unsigned int dwFollowera; // [esp+5Ch] [ebp+8h]

  if ( dwFollower == Guild::CGuild::GetMaster(this, &result)->m_dwCID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::KickMember",
      aDWorkRylSource_47,
      1416,
      aGid0x08x_25,
      this->m_dwGID,
      dwSuperior,
      dwFollower);
    *wError = 1;
    return 0;
  }
  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  v9 = dwSuperior;
  v10 = Mylast;
  dwFollowera = (unsigned int)Mylast;
  if ( Myfirst != Mylast )
  {
    do
    {
      if ( dwSuperior == Myfirst->m_dwCID )
        v10 = Myfirst;
      if ( dwFollower == Myfirst->m_dwCID )
        dwFollowera = (unsigned int)Myfirst;
      ++Myfirst;
    }
    while ( Myfirst != Mylast );
    v9 = dwSuperior;
  }
  if ( v10 == Mylast || (Guild::MemberInfo *)dwFollowera == Mylast )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::KickMember",
      aDWorkRylSource_47,
      1440,
      aGid0x08x_7,
      this->m_dwGID,
      v9,
      dwFollower);
    goto LABEL_27;
  }
  if ( v10->m_MemberListInfo.m_cTitle > this->m_GuildRight.m_aryRight[8] )
  {
    if ( v9 != dwFollower )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuild::KickMember",
        aDWorkRylSource_47,
        1453,
        aGid0x08x_23,
        this->m_dwGID,
        v9,
        dwFollower);
      *wError = 1;
      return 0;
    }
    if ( CServerSetup::GetInstance()->m_eNationType == CHINA )
    {
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, dwFollower);
      v13 = Character;
      if ( Character )
      {
        if ( Character->GetFame(Character) < 0x3E8 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Guild::CGuild::KickMember",
            aDWorkRylSource_47,
            1468,
            aGid0x08x_6,
            this->m_dwGID,
            dwFollower);
          *wError = 1;
          return 0;
        }
        v14 = v13->GetFame(v13);
        CCharacter::SetFame(v13, v14 - 1000);
        m_lpGameClientDispatch = (CSendStream *)v13->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          GameClientSendPacket::SendCharFameInfo(m_lpGameClientDispatch + 8, v13, szLoseCharName, szLoseCharName, 0);
      }
      goto LABEL_21;
    }
    if ( *(_BYTE *)(dwFollowera + 42) != 5 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuild::KickMember",
        aDWorkRylSource_47,
        1489,
        aGid0x08x_0,
        this->m_dwGID,
        v9,
        dwFollower);
LABEL_27:
      *wError = 1;
      return 0;
    }
  }
LABEL_21:
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v20 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v17 = GameClientSendPacket::SendCharGuildCmd(
            (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
            this->m_dwGID,
            dwFollower,
            v9,
            &byte_4E4C48,
            this->m_strName,
            5u,
            0);
    v20 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return v17;
  }
  *wError = 1;
  v20 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  return 0;
}

//----- (00461250) --------------------------------------------------------
char __thiscall Guild::CGuild::LogInOutMember(Guild::CGuild *this, unsigned int dwCID, unsigned int dwServerID)
{
  Guild::MemberInfo *Myfirst; // eax
  Guild::MemberInfo *Mylast; // edx

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    goto LABEL_5;
  do
  {
    if ( dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::LogInOutMember",
      aDWorkRylSource_47,
      1588,
      aGid0x08x_10,
      this->m_dwGID,
      dwCID,
      dwServerID);
    return 0;
  }
  else
  {
    Myfirst->m_dwServerID = dwServerID;
    return 1;
  }
}

//----- (004612C0) --------------------------------------------------------
char __thiscall Guild::CGuild::UpdateMemberInfo(
        Guild::CGuild *this,
        unsigned int dwCID,
        unsigned int dwValue,
        unsigned __int8 cCmd)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  Guild::MemberInfo *Mylast; // eax
  Guild::MemberInfo *Myfirst; // esi
  CSingleDispatch *DispatchTable; // eax
  unsigned int m_dwGID; // edx
  char updated; // bl
  Guild::MemberInfo v12; // [esp-4Ch] [ebp-60h] BYREF
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+0h] [ebp-14h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v14; // [esp+8h] [ebp-Ch]
  void *v15; // [esp+Ch] [ebp-8h]
  int v16; // [esp+10h] [ebp-4h]

  v16 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v15 = &_ehhandler__UpdateMemberInfo_CGuild_Guild__QAE_NKKE_Z;
  v14 = ExceptionList;
  Mylast = this->m_MemberList._Mylast;
  Myfirst = this->m_MemberList._Myfirst;
  if ( Myfirst == Mylast )
    goto LABEL_5;
  do
  {
    if ( dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::UpdateMemberInfo",
      aDWorkRylSource_47,
      1611,
      aGid0x08x_3,
      this->m_dwGID,
      dwCID);
    return 0;
  }
  switch ( cCmd )
  {
    case 0u:
      Myfirst->m_MemberListInfo.m_cLevel = dwValue;
      Myfirst->m_cUpdateCount = 10;
      break;
    case 1u:
      Myfirst->m_MemberListInfo.m_cClass = dwValue;
      Myfirst->m_cUpdateCount = 10;
      break;
    case 2u:
      Myfirst->m_MemberDetailInfo.m_dwFame = dwValue;
      goto LABEL_11;
    case 3u:
      Myfirst->m_MemberDetailInfo.m_dwGold = dwValue;
LABEL_11:
      ++Myfirst->m_cUpdateCount;
      break;
    default:
      break;
  }
  if ( Myfirst->m_cUpdateCount == 10 )
  {
    Myfirst->m_cUpdateCount = 0;
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v16 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      m_dwGID = this->m_dwGID;
      qmemcpy(&v12, Myfirst, sizeof(v12));
      updated = GameClientSendPacket::SendCharUpdateGuildMemberInfo(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  m_dwGID,
                  dwCID,
                  v12,
                  0);
      v16 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return updated;
    }
    v16 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
  return 1;
}

//----- (00461430) --------------------------------------------------------
bool __thiscall Guild::CGuild::UpdateMemberInfo(
        Guild::CGuild *this,
        unsigned int dwCID,
        Guild::MemberListInfo memberListInfo,
        Guild::MemberDetailInfo memberDetailInfo)
{
  Guild::MemberInfo *Myfirst; // eax
  Guild::MemberInfo *Mylast; // edx
  bool result; // al
  Guild::MemberInfo *v7; // edi
  Guild::MemberInfo *v8; // esi
  Guild::MemberInfo *v9; // edx
  unsigned int i; // ebp
  Guild::MemberInfo *v11; // edx
  unsigned __int8 v12; // bl

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    goto LABEL_5;
  do
  {
    if ( dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != this->m_MemberList._Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::UpdateMemberInfo",
      aDWorkRylSource_47,
      1680,
      aGid0x08x_22,
      this->m_dwGID,
      dwCID);
    return 0;
  }
  else
  {
    Myfirst->m_MemberListInfo = memberListInfo;
    Myfirst->m_MemberDetailInfo = memberDetailInfo;
    v7 = this->m_MemberList._Myfirst;
    v8 = this->m_MemberList._Mylast;
    v9 = v7;
    for ( i = 0; v9 != v8; ++v9 )
    {
      if ( v9->m_MemberListInfo.m_cTitle != 5 )
        i += v9->m_MemberDetailInfo.m_dwFame;
    }
    v11 = this->m_MemberList._Myfirst;
    v12 = 0;
    if ( v7 == v8 )
      goto LABEL_16;
    do
    {
      if ( v11->m_MemberListInfo.m_cTitle != 5 )
        ++v12;
      ++v11;
    }
    while ( v11 != v8 );
    if ( !v12 )
    {
LABEL_16:
      result = 1;
      this->m_dwFame = 0;
    }
    else
    {
      this->m_dwFame = i / v12;
      return 1;
    }
  }
  return result;
}

//----- (00461510) --------------------------------------------------------
char __thiscall Guild::CGuild::SendGuildSafe(
        Guild::CGuild *this,
        unsigned int dwCID,
        char *szCharName,
        unsigned __int8 cCmd)
{
  Guild::MemberInfo *i; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned int m_dwCID; // [esp-Ch] [ebp-50h]
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
  {
    qmemcpy(&memberInfo, i, sizeof(memberInfo));
    m_dwCID = memberInfo.m_dwCID;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
    if ( Character )
    {
      m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharGuildSafe(
          m_lpGameClientDispatch + 8,
          dwCID,
          this->m_dwGID,
          this->m_dwGold,
          Character->m_DBData.m_Info.Gold,
          cCmd,
          szCharName,
          0);
    }
  }
  return 1;
}

//----- (004615B0) --------------------------------------------------------
void __thiscall Guild::CGuild::SendAllMember(
        Guild::CGuild *this,
        char *szPacket,
        unsigned int dwPacketSize,
        unsigned __int8 cCMD_In)
{
  Guild::MemberInfo *i; // ebx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int m_dwCID; // [esp-10h] [ebp-54h]
  Guild::MemberInfo memberInfo; // [esp+8h] [ebp-3Ch] BYREF

  for ( i = this->m_MemberList._Myfirst; i != this->m_MemberList._Mylast; ++i )
  {
    qmemcpy(&memberInfo, i, sizeof(memberInfo));
    m_dwCID = memberInfo.m_dwCID;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
    if ( Character )
    {
      m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        CSendStream::PutBuffer(m_lpGameClientDispatch + 8, szPacket, dwPacketSize, cCMD_In);
    }
  }
}

//----- (00461650) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *v5; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead == this->_Myhead || *_Keyval < Myhead->_Myval.first )
  {
    v5 = result;
    result->_Ptr = this->_Myhead;
  }
  else
  {
    v5 = result;
    result->_Ptr = Myhead;
  }
  return v5;
}

//----- (004616C0) --------------------------------------------------------
unsigned __int16 __thiscall Guild::CGuild::SetMark(Guild::CGuild *this, unsigned int dwSenderID, char *szMark)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-4Ch] BYREF
  Guild::MemberInfo result; // [esp+10h] [ebp-44h] BYREF
  int v10; // [esp+50h] [ebp-4h]

  if ( szMark )
  {
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, dwSenderID);
    if ( Character )
    {
      if ( Character->m_DBData.m_cAdminLevel || dwSenderID == Guild::CGuild::GetMaster(this, &result)->m_dwCID )
      {
        if ( this->m_dwGold >= (unsigned int)&_L20577 + 2 )
        {
          DispatchTable = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
          v10 = 0;
          if ( StoragelpDBAgentDispatch.m_lpDispatch
            && GameClientSendPacket::SendCharGuildMark(
                 (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                 dwSenderID,
                 this->m_dwGID,
                 szMark,
                 (unsigned int)&_L20577 + 2,
                 0) == 1 )
          {
            v10 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
            return 0;
          }
          else
          {
            v10 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
            return 1;
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "Guild::CGuild::SetMark",
            aDWorkRylSource_47,
            107,
            aGid0x08x_14,
            this->m_dwGID);
          return 6;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Guild::CGuild::SetMark",
          aDWorkRylSource_47,
          100,
          aCid0x08x_37,
          dwSenderID);
        return 5;
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuild::SetMark",
        aDWorkRylSource_47,
        91,
        aCid0x08x_28,
        dwSenderID);
      return 4;
    }
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "Guild::CGuild::SetMark", aDWorkRylSource_47, 84, aCid0x08x_9, dwSenderID);
    return 3;
  }
}

//----- (004618A0) --------------------------------------------------------
char __thiscall Guild::CGuild::SetLevel(Guild::CGuild *this, unsigned int dwMaster, unsigned __int8 cLevel)
{
  unsigned int v5; // edi
  CSingleDispatch *DispatchTable; // eax
  char v7; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-4Ch] BYREF
  Guild::MemberInfo result; // [esp+Ch] [ebp-44h] BYREF
  int v10; // [esp+4Ch] [ebp-4h]

  if ( dwMaster != Guild::CGuild::GetMaster(this, &result)->m_dwCID )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "Guild::CGuild::SetLevel", aDWorkRylSource_47, 166, aCid0x08x_73, dwMaster);
    return 0;
  }
  if ( cLevel >= 5u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetLevel",
      aDWorkRylSource_47,
      172,
      aGid0x08x_20,
      this->m_dwGID);
    return 0;
  }
  if ( ms_arySetLevelFame[cLevel] <= this->m_dwFame )
  {
    if ( CServerSetup::GetInstance()->m_eNationType == CHINA )
      v5 = ms_arySetLevelGoldForChina[cLevel];
    else
      v5 = ms_arySetLevelGold[cLevel];
    if ( v5 <= this->m_dwGold )
    {
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
      v10 = 0;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v7 = GameClientSendPacket::SendCharGuildLevel(
               (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
               this->m_dwGID,
               cLevel,
               v5,
               0);
        v10 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
        return v7;
      }
      v10 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Guild::CGuild::SetLevel",
        aDWorkRylSource_47,
        188,
        aGid0x08x,
        this->m_dwGID);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetLevel",
      aDWorkRylSource_47,
      178,
      aGid0x08x_12,
      this->m_dwGID);
  }
  return 0;
}

//----- (00461A50) --------------------------------------------------------
char __thiscall Guild::CGuild::SetLevel(Guild::CGuild *this, unsigned __int8 cLevel, unsigned int dwGold)
{
  unsigned int m_dwGID; // edx
  PktGuildLevel pktGL; // [esp+4h] [ebp-18h] BYREF

  m_dwGID = this->m_dwGID;
  this->m_cLevel = cLevel;
  pktGL.m_cLevel = cLevel;
  this->m_dwGold = dwGold;
  pktGL.m_dwUID = m_dwGID;
  pktGL.m_dwGold = dwGold;
  if ( PacketWrap::WrapCrypt((char *)&pktGL, 0x15u, 0x8Bu, 0, 0) )
    Guild::CGuild::SendAllMember(this, (char *)&pktGL, 0x15u, 0x8Bu);
  return 1;
}

//----- (00461AC0) --------------------------------------------------------
char __thiscall Guild::CGuild::SetInclination(
        Guild::CGuild *this,
        unsigned int dwMaster,
        unsigned __int8 cInclination,
        unsigned __int16 *wError)
{
  unsigned __int64 v6; // rax
  unsigned int v7; // edi
  unsigned __int16 Year; // ax
  unsigned int v9; // ebp
  CSingleDispatch *DispatchTable; // eax
  char v11; // bl
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-54h] BYREF
  ATL::CTime GuildTime; // [esp+Ch] [ebp-4Ch] BYREF
  Guild::MemberInfo result; // [esp+14h] [ebp-44h] BYREF
  int v15; // [esp+54h] [ebp-4h]

  if ( dwMaster != Guild::CGuild::GetMaster(this, &result)->m_dwCID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetInclination",
      aDWorkRylSource_47,
      243,
      aCid0x08x_47,
      dwMaster);
    *wError = 1;
    return 0;
  }
  if ( (cInclination & 3) == 2 && (cInclination & 0x1C) != 0x10 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetInclination",
      aDWorkRylSource_47,
      251,
      aCid0x08x_247,
      cInclination);
    *wError = 1;
    return 0;
  }
  v6 = _time64(0);
  v7 = v6;
  Year = this->m_tmChangeInclination.Year;
  v9 = HIDWORD(v6);
  if ( Year
    && (ATL::CTime::CTime(
          &GuildTime,
          Year,
          this->m_tmChangeInclination.Month,
          this->m_tmChangeInclination.Day,
          this->m_tmChangeInclination.Hour,
          this->m_tmChangeInclination.Minute,
          this->m_tmChangeInclination.Second,
          -1),
        (__int64)(__PAIR64__(v9, v7) - GuildTime.m_time) / 60 > 1440) )
  {
    *wError = 2;
  }
  else
  {
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v15 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      v11 = GameClientSendPacket::SendCharGuildInclination(
              (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
              this->m_dwGID,
              cInclination,
              0);
      v15 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return v11;
    }
    v15 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
  return 0;
}

//----- (00461CA0) --------------------------------------------------------
char __thiscall Guild::CGuild::SetRight(Guild::CGuild *this, unsigned int dwMaster, GuildRight guildRight)
{
  int IsValid; // eax
  CSingleDispatch *DispatchTable; // eax
  GuildRight *p_guildRight; // ecx
  char *v7; // eax
  int v8; // edx
  unsigned int m_dwGID; // edx
  char v10; // bl
  GuildRight v12; // [esp-38h] [ebp-19Ch] BYREF
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+10h] [ebp-154h] BYREF
  Guild::MemberInfo result; // [esp+18h] [ebp-14Ch] BYREF
  char szTempBuf[260]; // [esp+50h] [ebp-114h] BYREF
  int v16; // [esp+160h] [ebp-4h]

  if ( dwMaster != Guild::CGuild::GetMaster(this, &result)->m_dwCID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetRight",
      aDWorkRylSource_47,
      333,
      aCid0x08x_105,
      dwMaster);
    return 0;
  }
  IsValid = GuildRight::IsValid(&guildRight);
  if ( IsValid != 50 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetRight",
      aDWorkRylSource_47,
      341,
      aCid0x08x_223,
      dwMaster,
      IsValid,
      guildRight.m_aryRight[IsValid]);
    return 0;
  }
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v16 = 0;
  if ( !StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v16 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return 0;
  }
  p_guildRight = &guildRight;
  v7 = szTempBuf;
  v8 = 50;
  do
  {
    *(_WORD *)v7 = Math::Convert::m_FastHeToBi[p_guildRight->m_aryRight[0]];
    v7[2] = 0;
    p_guildRight = (GuildRight *)((char *)p_guildRight + 1);
    v7 += 2;
    --v8;
  }
  while ( v8 );
  m_dwGID = this->m_dwGID;
  qmemcpy(&v12, &guildRight, 0x30u);
  *(_WORD *)&v12.m_aryRight[48] = *(_WORD *)&guildRight.m_aryRight[48];
  v10 = GameClientSendPacket::SendCharGuildRight(
          (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
          m_dwGID,
          v12,
          0);
  v16 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  return v10;
}

//----- (00461E20) --------------------------------------------------------
char __thiscall Guild::CGuild::SetRight(Guild::CGuild *this, GuildRight guildRight)
{
  unsigned int v2; // eax
  GuildRight *p_guildRight; // ecx
  char *v5; // eax
  int v6; // edx
  PktGuildRight pktGR; // [esp+Ch] [ebp-14Ch] BYREF
  char szTempBuf[260]; // [esp+50h] [ebp-108h] BYREF
  unsigned int cookie; // [esp+154h] [ebp-4h]

  v2 = __security_cookie;
  qmemcpy(&this->m_GuildRight, &guildRight, 0x30u);
  *(_WORD *)&this->m_GuildRight.m_aryRight[48] = *(_WORD *)&guildRight.m_aryRight[48];
  cookie = v2;
  memset(&pktGR.m_GuildRight, 0, sizeof(pktGR.m_GuildRight));
  pktGR.m_dwUID = this->m_dwGID;
  qmemcpy(&pktGR.m_GuildRight, &guildRight, 0x30u);
  *(_WORD *)&pktGR.m_GuildRight.m_aryRight[48] = *(_WORD *)&guildRight.m_aryRight[48];
  if ( PacketWrap::WrapCrypt((char *)&pktGR, 0x42u, 0x8Fu, 0, 0) )
  {
    p_guildRight = &guildRight;
    v5 = szTempBuf;
    v6 = 50;
    do
    {
      *(_WORD *)v5 = Math::Convert::m_FastHeToBi[p_guildRight->m_aryRight[0]];
      v5[2] = 0;
      p_guildRight = (GuildRight *)((char *)p_guildRight + 1);
      v5 += 2;
      --v6;
    }
    while ( v6 );
    Guild::CGuild::SendAllMember(this, (char *)&pktGR, 0x42u, 0x8Fu);
  }
  return 1;
}

//----- (00461F00) --------------------------------------------------------
char __thiscall Guild::CGuild::SetTitle(Guild::CGuild *this, unsigned int dwCID, unsigned __int8 cTitle)
{
  Guild::MemberInfo *Myfirst; // esi
  Guild::MemberInfo *Mylast; // eax
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int m_dwCID; // [esp-8h] [ebp-50h]
  PktGuildCmd pktGC; // [esp+Ch] [ebp-3Ch] BYREF

  Myfirst = this->m_MemberList._Myfirst;
  Mylast = this->m_MemberList._Mylast;
  if ( Myfirst == Mylast )
    goto LABEL_5;
  do
  {
    if ( dwCID == Myfirst->m_dwCID )
      break;
    ++Myfirst;
  }
  while ( Myfirst != Mylast );
  if ( Myfirst == Mylast )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Guild::CGuild::SetTitle",
      aDWorkRylSource_47,
      504,
      aGid0x08x_5,
      this->m_dwGID,
      dwCID);
    return 0;
  }
  else
  {
    m_dwCID = Myfirst->m_dwCID;
    Myfirst->m_MemberListInfo.m_cTitle = cTitle;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
    if ( Character )
      CCharacter::SetGID(Character, this->m_dwGID);
    pktGC.m_dwGID = this->m_dwGID;
    pktGC.m_dwSenderID = dwCID;
    pktGC.m_dwReferenceID = cTitle;
    strncpy(pktGC.m_szGuildName, this->m_strName, 0xBu);
    strncpy(pktGC.m_szSenderName, Myfirst->m_strName, 0x10u);
    pktGC.m_wCmd = 6;
    if ( PacketWrap::WrapCrypt((char *)&pktGC, 0x35u, 0x89u, 0, 0) )
      Guild::CGuild::SendAllMember(this, (char *)&pktGC, 0x35u, 0x89u);
    return 1;
  }
}

//----- (00462020) --------------------------------------------------------
void __thiscall Guild::CGuild::SendGuildRelationToAllMember(
        Guild::CGuild *this,
        unsigned int dwRelationGID,
        char cRelation,
        unsigned __int8 cState,
        unsigned __int8 cWaitTime,
        unsigned __int8 cSubCmd)
{
  PktGuildRelation pktGR; // [esp+4h] [ebp-18h] BYREF

  pktGR.m_dwCID = this->m_dwGID;
  pktGR.m_dwGID = dwRelationGID;
  pktGR.m_cRelation = cRelation;
  if ( PacketWrap::WrapCrypt((char *)&pktGR, 0x15u, 0x8Cu, 0, 0) )
    Guild::CGuild::SendAllMember(this, (char *)&pktGR, 0x15u, 0x8Cu);
}

//----- (00462080) --------------------------------------------------------
bool __thiscall Guild::CGuild::IsFriendlyGuild(Guild::CGuild *this, unsigned int dwGID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator itr; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &this->m_FriendlyMap,
    &itr,
    &dwGID);
  return itr._Ptr != this->m_FriendlyMap._Myhead;
}

//----- (004620B0) --------------------------------------------------------
bool __thiscall Guild::CGuild::IsNeutralityGuild(Guild::CGuild *this, unsigned int dwGID)
{
  unsigned int v2; // edi
  bool v4; // al
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator result; // [esp+8h] [ebp-4h] BYREF

  v2 = dwGID;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &this->m_FriendlyMap,
    &result,
    &dwGID);
  v4 = 0;
  if ( result._Ptr == this->m_FriendlyMap._Myhead )
  {
    dwGID = v2;
    std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
      &this->m_HostilityMap,
      &result,
      &dwGID);
    if ( result._Ptr == this->m_HostilityMap._Myhead )
      return 1;
  }
  return v4;
}

//----- (00462120) --------------------------------------------------------
unsigned __int8 __thiscall Guild::CGuild::GetRelation(Guild::CGuild *this, unsigned int dwRelationGID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator itr; // [esp+4h] [ebp-4h] BYREF

  if ( this->m_dwGID == dwRelationGID )
    return 3;
  std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
    &this->m_FriendlyMap,
    &itr,
    &dwRelationGID);
  if ( itr._Ptr == this->m_FriendlyMap._Myhead )
    return std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(
             &this->m_HostilityMap,
             &itr,
             &dwRelationGID)->_Ptr == this->m_HostilityMap._Myhead
         ? 0
         : 2;
  else
    return 1;
}
