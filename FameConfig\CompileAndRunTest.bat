@echo off
echo Compiling LogTest.cpp...

REM Try to find Visual Studio installation
set "VCVARSALL="
for %%v in (2022 2019 2017) do (
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Community\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files\Microsoft Visual Studio\%%v\Community\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Community\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Community\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files\Microsoft Visual Studio\%%v\Professional\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Professional\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
    if exist "C:\Program Files\Microsoft Visual Studio\%%v\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files\Microsoft Visual Studio\%%v\Enterprise\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\%%v\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VCVARSALL=C:\Program Files (x86)\Microsoft Visual Studio\%%v\Enterprise\VC\Auxiliary\Build\vcvarsall.bat"
        goto :found
    )
)

:found
if "%VCVARSALL%"=="" (
    echo ERROR: Could not find Visual Studio installation.
    echo Please compile LogTest.cpp manually using your C++ compiler.
    goto :eof
)

echo Found Visual Studio: %VCVARSALL%
call "%VCVARSALL%" x86

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Compile the test program
cl.exe /EHsc /std:c++17 LogTest.cpp /Fe:LogTest.exe

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo Running LogTest.exe...
echo.
LogTest.exe

echo.
echo Press any key to exit...
pause > nul 