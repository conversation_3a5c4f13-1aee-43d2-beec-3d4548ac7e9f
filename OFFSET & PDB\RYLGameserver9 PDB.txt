
//----- (00449F80) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseBillingTimeCheckNotify(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSession *v3; // esi
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v7; // ebx
  CSendStream *m_lpGameClientDispatch; // ebp
  CSession *m_Session; // eax
  char *Buffer; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1A )
  {
    v3 = *(CSession **)&lpPktBase[1].m_StartBit;
    m_CodePage = lpPktBase[1].m_CodePage;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseBillingTimeCheckNotify",
      aDWorkRylSource_86,
      1258,
      aCid0x08x_82,
      m_CodePage);
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_CodePage);
    v7 = Character;
    if ( Character )
    {
      m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseBillingTimeCheckNotify",
          aDWorkRylSource_86,
          1277,
          aCid0x08x_189,
          m_CodePage);
        m_Session = m_lpGameClientDispatch[10].m_Session;
        if ( v3 == m_Session )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseBillingTimeCheckNotify",
            aDWorkRylSource_86,
            1287,
            aCid0x08x_69,
            m_CodePage);
          Buffer = CSendStream::GetBuffer(m_lpGameClientDispatch + 8, (char *)0x1A);
          if ( Buffer )
          {
            qmemcpy(Buffer, lpPktBase, 0x1Au);
            CSendStream::WrapCrypt(m_lpGameClientDispatch + 8, 0x1Au, 0x7Au, 0, 0);
          }
          return 1;
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseBillingTimeCheckNotify",
            aDWorkRylSource_86,
            1282,
            aUidDCid0x08xUi_0,
            m_Session,
            m_CodePage,
            v3,
            v7->m_dwUID);
          return 1;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseBillingTimeCheckNotify",
          aDWorkRylSource_86,
          1273,
          aCid0x08xCgamec,
          m_CodePage);
        return 1;
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "DBAgentPacketParse::ParseBillingTimeCheckNotify",
        aDWorkRylSource_86,
        1264,
        aCid0x08x_338,
        m_CodePage);
      return 1;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0044A110) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCharAdminCmd(CDBAgentDispatch *DBAgentDispatch, PktAdmin *lpPktBase)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x34 )
  {
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, lpPktBase->m_stName);
    if ( Character || lpPktBase->m_usCmd == 20 )
      return GameClientParsePacket::ProcessAdminCmd(0, Character, lpPktBase);
    else
      return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0044A170) --------------------------------------------------------
bool __thiscall CCharacter::MoveZoneProcess(CCharacter *this)
{
  char v1; // dl

  v1 = this->m_cOperationFlags | 2;
  this->m_bLogout = 1;
  this->m_cOperationFlags = v1;
  return CCharacter::Logout(this, ZONEMOVE);
}

//----- (0044A190) --------------------------------------------------------
char __cdecl DBAgentPacketParse::SendAbnormalLogout(
        unsigned int dwUID,
        unsigned int dwCID,
        unsigned int dwRequestKey,
        CGameClientDispatch *lpGameClientDispatch)
{
  CSingleDispatch *DispatchTable; // eax
  CSendStream *v5; // esi
  char *Buffer; // eax
  int m_nCurrentUID; // edx
  char v8; // bl
  unsigned int m_dwUID; // eax
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-14h] BYREF
  int v12; // [esp+14h] [ebp-4h]

  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v12 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch
    && (v5 = (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
        (Buffer = CSendStream::GetBuffer((CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8], (char *)0x3C)) != 0) )
  {
    *((_DWORD *)Buffer + 11) = 0;
    *((_DWORD *)Buffer + 12) = 0;
    *((_DWORD *)Buffer + 13) = 0;
    *((_DWORD *)Buffer + 14) = 0;
    m_nCurrentUID = CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID;
    *((_DWORD *)Buffer + 4) = HIDWORD(CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID);
    *((_DWORD *)Buffer + 3) = m_nCurrentUID;
    *((_DWORD *)Buffer + 7) = dwCID;
    *((_DWORD *)Buffer + 6) = dwUID;
    *((_WORD *)Buffer + 21) = 5;
    *((_DWORD *)Buffer + 8) = 0;
    v8 = CSendStream::WrapHeader(v5, 0x3Cu, 0x26u, 0, 0);
    v12 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return v8;
  }
  else
  {
    m_dwUID = 0;
    if ( lpGameClientDispatch )
      m_dwUID = lpGameClientDispatch->m_dwUID;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::SendAbnormalLogout",
      aDWorkRylSource_86,
      89,
      "UID:%d/CID:0x%08x/DP:0x%p/RequestKey:%d/DPUID:%d/DBRequest Failed - Login failed. But cannot send abnormal logout packet",
      dwUID,
      dwCID,
      lpGameClientDispatch,
      dwRequestKey,
      m_dwUID);
    v12 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    return 0;
  }
}

//----- (0044A2B0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseUpdateDBData(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v2; // edi
  PktBase *v4; // ecx
  int i; // esi
  int v6; // ebx
  unsigned int v7; // edi
  unsigned int m_CodePage; // esi
  unsigned __int16 wError; // bx
  CGameClientDispatch *m_lpSrcDispatch; // ebp
  unsigned int v11; // eax
  char v12; // bl
  CCreatureManager *Instance; // eax
  CCharacter *v14; // eax
  CCreatureManager *v15; // eax
  CCreatureManager *v16; // eax
  CCharacter *m_lpCharacter; // [esp-1Ch] [ebp-40h]
  int v18; // [esp-1Ch] [ebp-40h]
  int v19; // [esp-1Ch] [ebp-40h]
  char *v20; // [esp-18h] [ebp-3Ch]
  char *v21; // [esp-18h] [ebp-3Ch]
  unsigned int v22; // [esp-14h] [ebp-38h]
  unsigned int v23; // [esp-14h] [ebp-38h]
  unsigned int v24; // [esp-10h] [ebp-34h]
  unsigned int v25; // [esp-10h] [ebp-34h]
  CCharacter *lpCharacter; // [esp+4h] [ebp-20h]
  CCharacter *lpCharactera; // [esp+4h] [ebp-20h]
  ServerInfo dwRequestKey; // [esp+8h] [ebp-1Ch]
  unsigned __int16 usTypeCode; // [esp+Ch] [ebp-18h]
  CServerRequest::Result result; // [esp+10h] [ebp-14h] BYREF
  int v31; // [esp+20h] [ebp-4h]

  v2 = lpPktBase->m_Len & 0x3FFF;
  if ( v2 < 0x3C )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      306,
      (char *)&byte_4E0800,
      60,
      lpPktBase->m_Len & 0x3FFF);
    return 0;
  }
  v4 = (PktBase *)((char *)lpPktBase + 44);
  for ( i = 0; v4 != &lpPktBase[5]; i += v6 )
  {
    v6 = *(unsigned __int16 *)&v4->m_StartBit;
    v4 = (PktBase *)((char *)v4 + 2);
  }
  if ( i + 60 != v2 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      306,
      (char *)&byte_4E07C8,
      v2,
      i + 60);
    return 0;
  }
  v7 = *(_DWORD *)&lpPktBase[2].m_StartBit;
  m_CodePage = lpPktBase[2].m_CodePage;
  wError = lpPktBase->m_SrvInfo.SrvState.wError;
  dwRequestKey = lpPktBase[2].m_SrvInfo;
  usTypeCode = HIWORD(lpPktBase[3].m_CodePage);
  CServerRequest::Result::Result(&result, dwRequestKey.dwServerInfo, 0);
  v31 = 0;
  m_lpSrcDispatch = (CGameClientDispatch *)result.m_lpSrcDispatch;
  if ( !result.m_lpSrcDispatch )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      328,
      "UID:%d/CID:0x%08x/DP:0x%p/RequestKey:%d/ DBRequest Failed - Invalid GameClientDispatch",
      v7,
      m_CodePage,
      0,
      dwRequestKey.dwServerInfo);
LABEL_18:
    wError = 1;
    goto LABEL_19;
  }
  v11 = CGameClientDispatch::PopRequestKey((CGameClientDispatch *)result.m_lpSrcDispatch);
  m_lpCharacter = m_lpSrcDispatch->m_lpCharacter;
  lpCharacter = (CCharacter *)v11;
  if ( dwRequestKey.dwServerInfo == v11 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      350,
      "UID:%d/CID:0x%08x(0x%p)/DP:0x%p/RequestKey:%d/DPRequestKey:%d/ DBRequest Success - Requestkey is same",
      v7,
      m_CodePage,
      m_lpCharacter,
      m_lpSrcDispatch,
      dwRequestKey.dwServerInfo,
      v11);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      341,
      "UID:%d/CID:0x%08x(0x%p)/DP:0x%p/RequestKey:%d/DPRequestKey:%d/ DBRequest Failed - Requestkey is invalid",
      v7,
      m_CodePage,
      m_lpCharacter,
      m_lpSrcDispatch,
      dwRequestKey.dwServerInfo,
      v11);
    wError = 1;
  }
  if ( v7 != m_lpSrcDispatch->m_dwUID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      359,
      "UID:%d/CID:0x%08x(0x%p)/DP:0x%p/RequestKey:%d/DPRequestKey:%d/DPUID:%d DBRequest Failed - UID is Invalid",
      v7,
      m_CodePage,
      m_lpSrcDispatch->m_lpCharacter,
      m_lpSrcDispatch,
      dwRequestKey.dwServerInfo,
      lpCharacter,
      m_lpSrcDispatch->m_dwUID);
    goto LABEL_18;
  }
  if ( wError )
  {
LABEL_19:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      375,
      aUidDCid0x08x_0,
      v7,
      m_CodePage,
      wError);
    goto LABEL_20;
  }
  if ( usTypeCode && usTypeCode != 3 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      368,
      aCid0x08xDbagen_1,
      m_CodePage,
      usTypeCode,
      m_lpSrcDispatch);
    goto LABEL_18;
  }
  Instance = CCreatureManager::GetInstance();
  CCreatureManager::CreateCharacter(Instance, m_CodePage);
  lpCharactera = v14;
  if ( !v14 )
  {
    v24 = m_CodePage;
    v22 = v7;
    v20 = "UID:%d/CID:0x%08x/Character create failed.";
    v18 = 384;
LABEL_28:
    wError = 1;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      v18,
      v20,
      v22,
      v24);
    goto LABEL_20;
  }
  if ( !CCharacter::Initialize(v14, m_lpSrcDispatch) )
  {
    v24 = m_CodePage;
    v22 = v7;
    v20 = "UID:%d/CID:0x%08x/Character initialize failed.";
    v18 = 389;
    goto LABEL_28;
  }
  if ( usTypeCode == 3 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "DBAgentPacketParse::ParseUpdateDBData",
      aDWorkRylSource_86,
      397,
      aUidDCid0x08xAd,
      v7,
      m_CodePage,
      LOBYTE(lpPktBase[3].m_CodePage));
    lpCharactera->m_DBData.m_cAdminLevel = lpPktBase[3].m_CodePage;
  }
  if ( CCharacter::SetCharacterInfo(
         lpCharactera,
         (float *)&lpPktBase[5].m_StartBit,
         (unsigned __int16 *)&lpPktBase[3].m_SrvInfo) )
  {
    v15 = CCreatureManager::GetInstance();
    if ( CCreatureManager::AddCreature(v15, (CNPC *)lpCharactera) )
      goto LABEL_36;
    v25 = m_CodePage;
    v23 = v7;
    v21 = "UID:%d/CID:0x%08x/CCreatureManager register failed.";
    v19 = 413;
  }
  else
  {
    v25 = m_CodePage;
    v23 = v7;
    v21 = "UID:%d/CID:0x%08x/CharacterInfo setting failed.";
    v19 = 406;
  }
  wError = 1;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "DBAgentPacketParse::ParseUpdateDBData",
    aDWorkRylSource_86,
    v19,
    v21,
    v23,
    v25);
  CGameClientDispatch::SetCharacter(m_lpSrcDispatch, 0);
  v16 = CCreatureManager::GetInstance();
  CCreatureManager::DeleteCharacter(v16, lpCharactera);
LABEL_20:
  if ( !lpPktBase->m_SrvInfo.SrvState.wError )
    DBAgentPacketParse::SendAbnormalLogout(v7, m_CodePage, dwRequestKey.dwServerInfo, m_lpSrcDispatch);
  if ( m_lpSrcDispatch )
  {
    v12 = GameClientSendPacket::SendCharLogin(&m_lpSrcDispatch->m_SendStream, 0, 0, wError);
    v31 = -1;
    CServerRequest::Result::~Result(&result);
    return v12;
  }
LABEL_36:
  v31 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}
// 44A4F1: conditional instruction was optimized away because bx.2!=0
// 44A562: variable 'v14' is possibly undefined

//----- (0044A6A0) --------------------------------------------------------
bool __cdecl DBAgentPacketParse::ParseAgentParty(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int16 m_Len; // ax
  bool result; // al
  CCharacterParty *v4; // eax
  int v5; // eax
  unsigned int v6; // edi
  CParty *Guild; // eax
  CCharacterParty *v8; // eax
  int v9; // eax
  unsigned int v10; // ebx
  unsigned int v11; // edi
  unsigned int v12; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CParty *v15; // eax
  CServerSetup *v16; // eax
  CSingleDispatch *DispatchTable; // eax
  CSendStream *v18; // esi
  char *Buffer; // eax
  unsigned int v20; // ebp
  PktBase *v21; // ebx
  unsigned int v22; // esi
  CParty *v23; // eax
  CParty *v24; // edi
  int v25; // eax
  unsigned int v26; // edi
  unsigned int v27; // ebx
  CParty *v28; // eax
  CParty *v29; // eax
  unsigned int v30; // edi
  unsigned int v31; // esi
  CParty *v32; // eax
  unsigned int v33; // edi
  unsigned int v34; // esi
  CParty *v35; // eax
  unsigned int v36; // edi
  unsigned int v37; // esi
  CParty *v38; // eax
  unsigned int v39; // edi
  unsigned int v40; // ebx
  CCharacterParty *v41; // eax
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v43; // [esp+18h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+24h] [ebp+8h]

  m_Len = lpPktBase->m_Len;
  if ( (m_Len & 0x3FFFu) >= 0x12 )
  {
    switch ( LOWORD(lpPktBase[1].m_CodePage) )
    {
      case '1':
        if ( (m_Len & 0x3FFF) != 0x10B )
          goto LABEL_17;
        v4 = (CCharacterParty *)operator new((tagHeader *)0x190);
        v43 = 0;
        if ( v4 )
          CCharacterParty::CCharacterParty(v4, (const PARTY *)((char *)&lpPktBase[1].m_CodePage + 2), 1);
        else
          v5 = 0;
        v43 = -1;
        if ( v5 )
          goto LABEL_68;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseAgentParty",
          aDWorkRylSource_86,
          473,
          aPid0x08x_5,
          *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2));
        goto LABEL_66;
      case '2':
        if ( (m_Len & 0x3FFF) != 0x16 )
          goto LABEL_17;
        v6 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        Guild = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v6);
        if ( !Guild )
          goto LABEL_68;
        if ( !Guild->Destory(Guild, 0, 0) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            496,
            aPid0x08x_4,
            v6);
          goto LABEL_66;
        }
        CPartyMgr::DeleteParty(
          CSingleton<CPartyMgr>::ms_pSingleton,
          *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2));
        return 1;
      case '3':
        if ( (m_Len & 0x3FFF) != 0x10B )
          goto LABEL_17;
        v8 = (CCharacterParty *)operator new((tagHeader *)0x190);
        v43 = 1;
        if ( v8 )
          CCharacterParty::CCharacterParty(v8, (const PARTY *)((char *)&lpPktBase[1].m_CodePage + 2), 0);
        else
          v9 = 0;
        v43 = -1;
        if ( v9 )
          goto LABEL_68;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseAgentParty",
          aDWorkRylSource_86,
          518,
          aPid0x08x_5,
          *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2));
        goto LABEL_66;
      case '4':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v26 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v27 = *(_DWORD *)&lpPktBase[2].m_Len;
        v28 = Guild::CGuildMgr::GetGuild(
                CSingleton<CPartyMgr>::ms_pSingleton,
                *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2));
        if ( !v28 )
          goto LABEL_68;
        v28->Join(v28, v26, v27, (const char *)&lpPktBase[2].m_CodePage + 2, 0);
        return 1;
      case '5':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v20 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v21 = *(PktBase **)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v22 = *(_DWORD *)&lpPktBase[2].m_Len;
        v29 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v20);
        v24 = v29;
        if ( !v29 )
          goto LABEL_68;
        lpPktBasea = (PktBase *)v29->m_Party.m_dwLeaderID;
        v25 = v29->Leave(v29, (unsigned int)v21, v22, 0);
        goto LABEL_45;
      case '6':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v10 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v11 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v12 = *(_DWORD *)&lpPktBase[2].m_Len;
        if ( lpPktBase->m_SrvInfo.SrvState.wError == 2 )
        {
          Instance = CCreatureManager::GetInstance();
          Character = CCreatureManager::GetCharacter(Instance, v11);
          if ( Character )
            CCharacter::SetPID(Character, 0);
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            546,
            aCid0x08x_342,
            v11);
          goto LABEL_66;
        }
        v15 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v10);
        if ( v15 )
        {
          v15->Login(v15, v11, v12);
          return 1;
        }
        v16 = CServerSetup::GetInstance();
        if ( CServerSetup::GetServerID(v16) != v12 )
          goto LABEL_68;
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v43 = 2;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          v18 = (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8];
          Buffer = CSendStream::GetBuffer((CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8], (char *)0x1A);
          if ( Buffer )
          {
            *((_WORD *)Buffer + 8) = 51;
            *(_DWORD *)(Buffer + 18) = v11;
            *(_DWORD *)(Buffer + 22) = v10;
            CSendStream::WrapHeader(v18, 0x1Au, 0x27u, 0, 0);
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            566,
            aDbagentDispatc);
        }
        v43 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
        return 1;
      case '7':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v20 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v21 = *(PktBase **)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v22 = *(_DWORD *)&lpPktBase[2].m_Len;
        v23 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v20);
        v24 = v23;
        if ( !v23 )
          goto LABEL_68;
        lpPktBasea = (PktBase *)v23->m_Party.m_dwLeaderID;
        v25 = v23->Logout(v23, (unsigned int)v21, v22);
LABEL_45:
        if ( v25 == 1 )
        {
          CPartyMgr::DeleteParty(CSingleton<CPartyMgr>::ms_pSingleton, v20);
          return 1;
        }
        if ( lpPktBasea != v21 )
          goto LABEL_68;
        v24->TransferLeader(v24, v22);
        return 1;
      case '8':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v30 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v31 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v32 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v30);
        if ( !v32 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            695,
            aPid0x08x_1,
            v30);
          goto LABEL_66;
        }
        v32->TransferLeader(v32, v31);
        return 1;
      case '9':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v33 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v34 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v35 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v33);
        if ( !v35 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            717,
            aPid0x08x_1,
            v33);
          goto LABEL_66;
        }
        if ( v35->AdjustAutoRouting(v35, v34, 1) )
          goto LABEL_68;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseAgentParty",
          aDWorkRylSource_86,
          723,
          aCid0x08x_172,
          v34);
        goto LABEL_66;
      case ':':
        if ( (m_Len & 0x3FFF) != 0x2E )
          goto LABEL_17;
        v36 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
        v37 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
        v38 = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v36);
        if ( !v38 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "DBAgentPacketParse::ParseAgentParty",
            aDWorkRylSource_86,
            740,
            aPid0x08x_1,
            v36);
          goto LABEL_66;
        }
        if ( v38->AdjustAutoRouting(v38, v37, 0) )
          goto LABEL_68;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "DBAgentPacketParse::ParseAgentParty",
          aDWorkRylSource_86,
          746,
          aCid0x08x_117,
          v37);
        goto LABEL_66;
      case ';':
        if ( (m_Len & 0x3FFF) == 0x3A )
        {
          v39 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2);
          v40 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
          v41 = (CCharacterParty *)Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v39);
          if ( v41 )
          {
            CCharacterParty::SendPartyAddress(
              v41,
              v40,
              (const sockaddr_in *)&lpPktBase[2].m_Len,
              (const sockaddr_in *)((char *)&lpPktBase[3].m_CodePage + 2));
LABEL_68:
            result = 1;
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "DBAgentPacketParse::ParseAgentParty",
              aDWorkRylSource_86,
              763,
              aPid0x08x_1,
              v39);
LABEL_66:
            result = 1;
          }
        }
        else
        {
LABEL_17:
          CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
          result = 0;
        }
        break;
      default:
        goto LABEL_68;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseAgentParty",
      aDWorkRylSource_86,
      455,
      (char *)&byte_4E0F5C);
    return 0;
  }
  return result;
}
// 44A78B: variable 'v5' is possibly undefined
// 44A879: variable 'v9' is possibly undefined

//----- (0044AC80) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseAgentZone(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int v3; // eax
  unsigned int v5; // ebx
  CPacketDispatch_vtbl *v6; // edi
  void (__thiscall *Connected)(CPacketDispatch *); // edx
  CPacketDispatch *m_lpSrcDispatch; // esi
  char v9; // al
  u_short GameServerTCPPort; // ax
  char *Buffer; // edi
  unsigned int v12; // [esp-10h] [ebp-4Ch]
  bool (__thiscall *ParsePacket)(CPacketDispatch *, char *const, unsigned int *); // [esp+4h] [ebp-38h]
  unsigned __int16 wError; // [esp+8h] [ebp-34h]
  CDBRequest DBRequest; // [esp+Ch] [ebp-30h] BYREF
  CServerRequest::Result result; // [esp+14h] [ebp-28h] BYREF
  INET_Addr moveAddress; // [esp+1Ch] [ebp-20h] BYREF
  int v18; // [esp+38h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+44h] [ebp+8h]

  v3 = lpPktBase->m_Len & 0x3FFF;
  if ( v3 < 0x12 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseAgentZone",
      aDWorkRylSource_86,
      783,
      (char *)&byte_4E10F4,
      v3,
      18);
    return 0;
  }
  v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  wError = lpPktBase->m_SrvInfo.SrvState.wError;
  CServerRequest::Result::Result(&result, v5, 1);
  v6 = result.m_lpSrcDispatch[10].__vftable;
  Connected = 0;
  v18 = 0;
  m_lpSrcDispatch = result.m_lpSrcDispatch;
  DBRequest.m_dwRequestKey = (unsigned int)&result.m_lpSrcDispatch[10];
  if ( v6 )
    ParsePacket = v6[74].ParsePacket;
  else
    ParsePacket = 0;
  if ( v6 )
    Connected = v6[1].Connected;
  lpPktBasea = (PktBase *)Connected;
  v12 = CGameClientDispatch::PopRequestKey((CGameClientDispatch *)result.m_lpSrcDispatch);
  if ( v5 == v12 )
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "DBAgentPacketParse::ParseAgentZone",
      aDWorkRylSource_86,
      808,
      aUidDCid0x08x0x_10,
      ParsePacket,
      lpPktBasea,
      v6,
      m_lpSrcDispatch,
      v5,
      v12);
  else
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseAgentZone",
      aDWorkRylSource_86,
      803,
      aUidDCid0x08x0x_7,
      ParsePacket,
      lpPktBasea,
      v6,
      m_lpSrcDispatch,
      v5,
      v12);
  switch ( LOWORD(lpPktBase[1].m_CodePage) )
  {
    case 'C':
      if ( !wError && *(_DWORD *)DBRequest.m_dwRequestKey )
        CCharacter::MoveZoneProcess(*(CCharacter **)DBRequest.m_dwRequestKey);
      GameServerTCPPort = CServerSetup::GetGameServerTCPPort(*(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2));
      INET_Addr::set_addr(
        &moveAddress,
        *(in_addr *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2),
        GameServerTCPPort);
      CGameClientDispatch::SetMoveAddress(
        (CGameClientDispatch *)m_lpSrcDispatch,
        *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 2),
        &moveAddress);
      if ( !wError )
      {
        Buffer = CSendStream::GetBuffer(&DBAgentDispatch->m_SendStream, (char *)0x12);
        if ( Buffer )
        {
          CDBRequest::CDBRequest(&DBRequest, (CGameClientDispatch *)m_lpSrcDispatch, 0x258u, 0);
          if ( CDBRequest::IsValid(&DBRequest) )
          {
            *((_DWORD *)Buffer + 3) = DBRequest.m_dwRequestKey;
            *((_WORD *)Buffer + 8) = 69;
            if ( CSendStream::WrapHeader(&DBAgentDispatch->m_SendStream, 0x12u, 0x5Bu, 0, 0) )
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_DETAIL,
                "DBAgentPacketParse::ParseAgentZone",
                aDWorkRylSource_86,
                870,
                "SS:0x%p/DP:0x%p/sCmd:0x%02x/RequestKey:%d/DBRequest Send success.",
                m_lpSrcDispatch->m_Session,
                m_lpSrcDispatch,
                69,
                DBRequest.m_dwRequestKey);
              CGameClientDispatch::PushRequestKey((CGameClientDispatch *)m_lpSrcDispatch, DBRequest.m_dwRequestKey);
              goto LABEL_32;
            }
            CDBRequest::CancelRequest(&DBRequest);
          }
        }
      }
      v9 = GameClientSendPacket::SendServerZone(
             (CSendStream *)&m_lpSrcDispatch[8],
             (unsigned int)m_lpSrcDispatch[49].__vftable,
             (const sockaddr_in *)&m_lpSrcDispatch[46].m_Session,
             wError);
LABEL_30:
      if ( !v9 )
        CGameClientDispatch::Disconnect((CGameClientDispatch *)m_lpSrcDispatch);
      goto LABEL_32;
    case 'D':
      if ( wError && v6 )
        HIBYTE(v6[76].ParsePacket) &= ~4u;
      v9 = GameClientSendPacket::SendCharMoveZone(
             (CSendStream *)&m_lpSrcDispatch[8],
             (unsigned __int8 *)&lpPktBase[1].m_CodePage + 3,
             5u,
             BYTE2(lpPktBase[1].m_CodePage),
             wError);
      goto LABEL_30;
    case 'E':
      v9 = GameClientSendPacket::SendServerZone(
             (CSendStream *)&m_lpSrcDispatch[8],
             (unsigned int)m_lpSrcDispatch[49].__vftable,
             (const sockaddr_in *)&m_lpSrcDispatch[46].m_Session,
             0);
      goto LABEL_30;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "DBAgentPacketParse::ParseAgentZone",
    aDWorkRylSource_86,
    899,
    aThis0xPDb,
    m_lpSrcDispatch,
    LOWORD(lpPktBase[1].m_CodePage));
LABEL_32:
  v18 = -1;
  CServerRequest::Result::~Result(&result);
  return 1;
}

//----- (0044AF50) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSysServerLogin(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  Item::CItemFactory *v3; // eax
  unsigned int m_CodePage; // ecx
  unsigned int v5; // edx
  unsigned int v6; // edi
  unsigned int v7; // esi
  CSingleDispatch *DispatchTable; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x84 )
  {
    v3 = CSingleton<Item::CItemFactory>::ms_pSingleton;
    m_CodePage = lpPktBase[1].m_CodePage;
    v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    if ( CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID < __PAIR64__(m_CodePage, v5) )
    {
      LODWORD(CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID) = v5;
      HIDWORD(v3->m_nCurrentUID) = m_CodePage;
    }
    v6 = *(_DWORD *)&lpPktBase[2].m_StartBit;
    CServerSetup::GetInstance()->m_dwClientVer = v6;
    v7 = lpPktBase[2].m_CodePage;
    CServerSetup::GetInstance()->m_dwCheckSum = v7;
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::SetDispatch(DispatchTable, DBAgentDispatch);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0044AFD0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseSysRankingUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int16 m_CodePage_high; // cx
  const RankingNode *p_m_SrvInfo; // esi
  int v4; // edi

  m_CodePage_high = HIWORD(lpPktBase[1].m_CodePage);
  p_m_SrvInfo = (const RankingNode *)&lpPktBase[1].m_SrvInfo;
  if ( m_CodePage_high )
  {
    v4 = m_CodePage_high;
    do
    {
      CRankingMgr::UpdateRanking(CSingleton<CRankingMgr>::ms_pSingleton, p_m_SrvInfo++);
      --v4;
    }
    while ( v4 );
  }
  return 1;
}

//----- (0044B010) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseUserKill(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebx
  unsigned int v4; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // edi
  unsigned int m_dwUID; // ecx

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x18 )
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, m_CodePage);
  if ( !Character )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "DBAgentPacketParse::ParseUserKill",
      aDWorkRylSource_86,
      984,
      aUidUCid0x08x,
      v4,
      m_CodePage);
    DBAgentPacketParse::SendUserKill(DBAgentDispatch, v4, 2u);
    return 1;
  }
  m_lpGameClientDispatch = Character->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    m_dwUID = m_lpGameClientDispatch->m_dwUID;
    if ( v4 != m_dwUID )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "DBAgentPacketParse::ParseUserKill",
        aDWorkRylSource_86,
        1006,
        aUidDCid0x08xUi,
        m_dwUID,
        m_CodePage,
        v4,
        Character->m_dwUID);
      DBAgentPacketParse::SendUserKill(DBAgentDispatch, v4, 2u);
      return 1;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "DBAgentPacketParse::ParseUserKill",
      aDWorkRylSource_86,
      1012,
      aUidDCid0x08x,
      v4,
      m_CodePage);
    CGameClientDispatch::Disconnect(m_lpGameClientDispatch);
    m_lpGameClientDispatch->Disconnected(m_lpGameClientDispatch);
  }
  else if ( Character->m_eCellLoginStatus == MASTER && !Character->m_bLogout )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "DBAgentPacketParse::ParseUserKill",
      aDWorkRylSource_86,
      1000,
      aUidUCid0x08xCg,
      v4,
      m_CodePage);
    return 1;
  }
  return 1;
}

//----- (0044B160) --------------------------------------------------------
std::_List_nod<unsigned short>::_Node *__thiscall std::list<unsigned short>::_Buynode(
        std::list<unsigned short> *this,
        std::_List_nod<unsigned short>::_Node *_Next,
        std::_List_nod<unsigned short>::_Node *_Prev,
        unsigned __int16 *_Val)
{
  std::_List_nod<unsigned short>::_Node *result; // eax

  result = (std::_List_nod<unsigned short>::_Node *)operator new((tagHeader *)0xC);
  if ( result )
  {
    result->_Next = _Next;
    result->_Prev = _Prev;
    result->_Myval = *_Val;
  }
  return result;
}

//----- (0044B190) --------------------------------------------------------
void __thiscall std::list<unsigned short>::_Incsize(std::list<unsigned short> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x7FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0044B230) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseEventDropItem(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  std::_List_nod<unsigned short>::_Node *Myhead; // edi
  std::list<unsigned short> *p_m_lstDropEventItem; // esi
  std::_List_nod<unsigned short>::_Node *v5; // ebx
  std::_List_nod<unsigned short>::_Node *Prev; // [esp-14h] [ebp-14h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0xE )
  {
    Myhead = CSingleton<CGameEventMgr>::ms_pSingleton->m_lstDropEventItem._Myhead;
    p_m_lstDropEventItem = &CSingleton<CGameEventMgr>::ms_pSingleton->m_lstDropEventItem;
    Prev = Myhead->_Prev;
    lpPktBase = (PktBase *)*(unsigned __int16 *)&lpPktBase[1].m_StartBit;
    v5 = std::list<unsigned short>::_Buynode(
           &CSingleton<CGameEventMgr>::ms_pSingleton->m_lstDropEventItem,
           Myhead,
           Prev,
           (unsigned __int16 *)&lpPktBase);
    std::list<unsigned short>::_Incsize(p_m_lstDropEventItem, 1u);
    Myhead->_Prev = v5;
    v5->_Prev->_Next = v5;
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(DBAgentDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0044B2A0) --------------------------------------------------------
void __thiscall VirtualArea::ProtoType::ProtoType(VirtualArea::ProtoType *this)
{
  this->m_StartPos[0].m_fPointX = 0.0;
  this->m_StartPos[0].m_fPointY = 0.0;
  this->m_StartPos[0].m_fPointZ = 0.0;
  this->m_StartPos[1].m_fPointX = 0.0;
  this->m_StartPos[1].m_fPointY = 0.0;
  this->m_StartPos[1].m_fPointZ = 0.0;
  *(_QWORD *)&this->m_RespawnPos[0][0].m_fPointX = 0LL;
  *(_QWORD *)&this->m_RespawnPos[0][0].m_fPointZ = 0LL;
  *(_QWORD *)&this->m_RespawnPos[0][1].m_fPointY = 0LL;
  *(_QWORD *)&this->m_RespawnPos[0][2].m_fPointX = 0LL;
  this->m_RespawnPos[0][2].m_fPointZ = 0.0;
  *(_QWORD *)&this->m_RespawnPos[1][0].m_fPointX = 0LL;
  *(_QWORD *)&this->m_RespawnPos[1][0].m_fPointZ = 0LL;
  *(_QWORD *)&this->m_RespawnPos[1][1].m_fPointY = 0LL;
  *(_QWORD *)&this->m_RespawnPos[1][2].m_fPointX = 0LL;
  this->m_RespawnPos[1][2].m_fPointZ = 0.0;
}

//----- (0044B310) --------------------------------------------------------
VirtualArea::CBGServerMap *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualArea(
        VirtualArea::CVirtualAreaMgr *this,
        unsigned __int16 wMapIndex)
{
  if ( (wMapIndex & 0x8000) == 0x8000 )
    return VirtualArea::CBGServerMgr::GetVirtualArea(this->m_BGServerMgr, wMapIndex);
  else
    return 0;
}

//----- (0044B340) --------------------------------------------------------
const VirtualArea::ProtoType *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(
        VirtualArea::CVirtualAreaMgr *this,
        char *szMapType)
{
  int v3; // ebx
  int i; // edi
  int v5; // eax

  v3 = 0;
  if ( !this->m_VirtualAreaProtoTypeNum )
    return 0;
  for ( i = 0; ; ++i )
  {
    strncmp((unsigned __int8 *)szMapType, (unsigned __int8 *)this->m_VirtualAreaProtoTypeArray[i].m_szMapType, 0x20u);
    if ( !v5 )
      break;
    if ( ++v3 >= this->m_VirtualAreaProtoTypeNum )
      return 0;
  }
  return &this->m_VirtualAreaProtoTypeArray[v3];
}
// 44B378: variable 'v5' is possibly undefined

//----- (0044B3B0) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::EnterVirtualArea(
        VirtualArea::CVirtualAreaMgr *this,
        CCharacter *lpCharacter,
        unsigned __int16 wMapIndex,
        int cMoveType)
{
  if ( !lpCharacter )
    return 0;
  if ( (wMapIndex & 0x8000) == 0x8000 && VirtualArea::CBGServerMgr::GetVirtualArea(this->m_BGServerMgr, wMapIndex) )
    return VirtualArea::CBGServerMgr::Enter(this->m_BGServerMgr, lpCharacter, wMapIndex, cMoveType);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualAreaMgr::EnterVirtualArea",
    aDWorkRylSource_8,
    265,
    aCid0x08x_43,
    lpCharacter->m_dwCID,
    wMapIndex);
  return 0;
}

//----- (0044B430) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::LeaveVirtualArea(
        VirtualArea::CVirtualAreaMgr *this,
        CCharacter *lpCharacter)
{
  unsigned __int16 m_wMapIndex; // si

  if ( !lpCharacter )
    return 0;
  m_wMapIndex = lpCharacter->m_CellPos.m_wMapIndex;
  if ( (m_wMapIndex & 0x8000) == 0x8000 && VirtualArea::CBGServerMgr::GetVirtualArea(this->m_BGServerMgr, m_wMapIndex) )
    return VirtualArea::CBGServerMgr::Leave(this->m_BGServerMgr, lpCharacter);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualAreaMgr::LeaveVirtualArea",
    aDWorkRylSource_8,
    295,
    aCid0x08x_257,
    lpCharacter->m_dwCID,
    m_wMapIndex);
  return 0;
}

//----- (0044B4B0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllVirtualArea(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::Process(this->m_BGServerMgr);
}

//----- (0044B4D0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllMonster(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessAllMonster(this->m_BGServerMgr);
}

//----- (0044B4F0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessMonsterRegenHPAndMP(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessMonsterRegenHPAndMP(this->m_BGServerMgr);
}

//----- (0044B510) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessSummonMonsterDead(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessSummonMonsterDead(this->m_BGServerMgr);
}

//----- (0044B530) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllCellPrepareBroadCast(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessAllCellPrepareBroadCast(this->m_BGServerMgr);
  return 1;
}

//----- (0044B550) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllCellBroadCast(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessAllCellBroadCast(this->m_BGServerMgr);
  return 1;
}

//----- (0044B570) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessDeleteItem(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    VirtualArea::CBGServerMgr::ProcessDeleteItem(this->m_BGServerMgr);
}

//----- (0044B590) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::CreateBGServer(VirtualArea::CVirtualAreaMgr *this)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    return VirtualArea::CBGServerMgr::CreateBGServerMap(this->m_BGServerMgr);
  else
    return 0;
}

//----- (0044B5B0) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::SendBGServerMapList(
        VirtualArea::CVirtualAreaMgr *this,
        CCharacter *lpCharacter)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    return VirtualArea::CBGServerMgr::SendMapList(this->m_BGServerMgr, lpCharacter);
  else
    return 0;
}

//----- (0044B5E0) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::SendBGServerResultList(
        VirtualArea::CVirtualAreaMgr *this,
        CCharacter *lpCharacter)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
    return VirtualArea::CBGServerMgr::SendResultList(this->m_BGServerMgr, lpCharacter);
  else
    return 0;
}

//----- (0044B610) --------------------------------------------------------
VirtualArea::ProtoType *__thiscall std::vector<VirtualArea::ProtoType>::size(std::vector<VirtualArea::ProtoType> *this)
{
  VirtualArea::ProtoType *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (VirtualArea::ProtoType *)(this->_Mylast - result);
  return result;
}

//----- (0044B630) --------------------------------------------------------
void __thiscall VirtualArea::ProtoType::ProtoType(VirtualArea::ProtoType *this, const VirtualArea::ProtoType *__that)
{
  Position *m_StartPos; // esi
  float *p_m_fPointZ; // ecx
  int v5; // edi
  int v6; // ebx
  Position (*m_RespawnPos)[3]; // esi
  float *v8; // ecx
  int v9; // edx

  this->m_dwVID = __that->m_dwVID;
  this->m_cMapType = __that->m_cMapType;
  this->m_cZone = __that->m_cZone;
  this->m_wStartX = __that->m_wStartX;
  this->m_wStartZ = __that->m_wStartZ;
  this->m_wWidth = __that->m_wWidth;
  this->m_wHeight = __that->m_wHeight;
  this->m_cMaxRespawnPos = __that->m_cMaxRespawnPos;
  qmemcpy(this->m_szArrangementFileName, __that->m_szArrangementFileName, sizeof(this->m_szArrangementFileName));
  qmemcpy(this->m_szMapType, __that->m_szMapType, sizeof(this->m_szMapType));
  m_StartPos = this->m_StartPos;
  p_m_fPointZ = &__that->m_StartPos[0].m_fPointZ;
  v5 = (char *)this - (char *)__that;
  v6 = 2;
  do
  {
    m_StartPos->m_fPointX = *(p_m_fPointZ - 2);
    m_StartPos->m_fPointY = *(p_m_fPointZ - 1);
    *(float *)((char *)p_m_fPointZ + v5) = *p_m_fPointZ;
    ++m_StartPos;
    p_m_fPointZ += 3;
    --v6;
  }
  while ( v6 );
  m_RespawnPos = this->m_RespawnPos;
  v8 = &__that->m_RespawnPos[0][0].m_fPointZ;
  v9 = 6;
  do
  {
    (*m_RespawnPos)[0].m_fPointX = *(v8 - 2);
    (*m_RespawnPos)[0].m_fPointY = *(v8 - 1);
    *(float *)((char *)v8 + v5) = *v8;
    m_RespawnPos = (Position (*)[3])((char *)m_RespawnPos + 12);
    v8 += 3;
    --v9;
  }
  while ( v9 );
}

//----- (0044B700) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (0044B720) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (0044B780) --------------------------------------------------------
void __cdecl std::fill<VirtualArea::ProtoType *,VirtualArea::ProtoType>(
        VirtualArea::ProtoType *_First,
        VirtualArea::ProtoType *_Last,
        const VirtualArea::ProtoType *_Val)
{
  VirtualArea::ProtoType *i; // eax
  VirtualArea::ProtoType *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy(v4, _Val, sizeof(VirtualArea::ProtoType));
  }
}

//----- (0044B7B0) --------------------------------------------------------
int __thiscall std::string::compare(
        std::string *this,
        unsigned int _Off,
        unsigned int _N0,
        const char *_Ptr,
        unsigned int _Count)
{
  unsigned int v6; // edx
  int v7; // ecx
  std::string::_Bxty *v8; // eax
  int result; // eax

  if ( this->_Mysize < _Off )
    std::_String_base::_Xran((std::_String_base *)this);
  v6 = _N0;
  if ( this->_Mysize - _Off < _N0 )
    v6 = this->_Mysize - _Off;
  if ( !v6 )
    goto LABEL_12;
  v7 = v6;
  if ( v6 >= _Count )
    v7 = _Count;
  v8 = this->_Myres < 0x10 ? &this->_Bx : (std::string::_Bxty *)this->_Bx._Ptr;
  result = memcmp(&v8->_Buf[_Off], _Ptr, v7);
  if ( !result )
  {
LABEL_12:
    if ( v6 >= _Count )
      return v6 != _Count;
    else
      return -1;
  }
  return result;
}

//----- (0044B830) --------------------------------------------------------
void __cdecl std::swap<VirtualArea::ProtoType>(VirtualArea::ProtoType *_Left, VirtualArea::ProtoType *_Right)
{
  VirtualArea::ProtoType _Tmp; // [esp+8h] [ebp-114h] BYREF

  VirtualArea::ProtoType::ProtoType(&_Tmp, _Left);
  qmemcpy(_Left, _Right, sizeof(VirtualArea::ProtoType));
  qmemcpy(_Right, &_Tmp, sizeof(VirtualArea::ProtoType));
}

//----- (0044B890) --------------------------------------------------------
const VirtualArea::ProtoType *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(
        VirtualArea::CVirtualAreaMgr *this,
        unsigned int dwVID)
{
  const VirtualArea::ProtoType *result; // eax
  const VirtualArea::ProtoType *i; // ecx

  result = 0;
  if ( dwVID )
  {
    result = this->m_VirtualAreaProtoTypeArray;
    for ( i = &result[this->m_VirtualAreaProtoTypeNum]; result != i; ++result )
    {
      if ( dwVID == result->m_dwVID )
        break;
    }
  }
  return result;
}

//----- (0044B8C0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (0044B920) --------------------------------------------------------
VirtualArea::ProtoType *__cdecl std::copy_backward<VirtualArea::ProtoType *,VirtualArea::ProtoType *>(
        VirtualArea::ProtoType *_First,
        VirtualArea::ProtoType *_Last,
        VirtualArea::ProtoType *_Dest)
{
  VirtualArea::ProtoType *v3; // edx
  VirtualArea::ProtoType *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy(--result, --v3, sizeof(VirtualArea::ProtoType));
  return result;
}

//----- (0044B960) --------------------------------------------------------
VirtualArea::ProtoType *__cdecl std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
        VirtualArea::ProtoType *_First,
        VirtualArea::ProtoType *_Last,
        VirtualArea::ProtoType *_Dest)
{
  VirtualArea::ProtoType *v3; // esi
  VirtualArea::ProtoType *v4; // edi

  v3 = _First;
  if ( _First == _Last )
    return _Dest;
  v4 = _Dest;
  do
  {
    if ( v4 )
      VirtualArea::ProtoType::ProtoType(v4, v3);
    ++v3;
    ++v4;
  }
  while ( v3 != _Last );
  return v4;
}

//----- (0044B9A0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        int _Hole,
        int _Top,
        VirtualArea::ProtoType _Val)
{
  int v4; // edi
  int i; // eax
  VirtualArea::ProtoType *v6; // esi

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    if ( v6->m_dwVID >= _Val.m_dwVID )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (0044BA10) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Mid,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  int v3; // ebp
  int v4; // eax
  int v5; // esi
  int v6; // edx
  int v7; // esi
  VirtualArea::ProtoType *v8; // edi
  std::vector<VirtualArea::ProtoType>::iterator *p_First; // eax
  VirtualArea::ProtoType *Myptr; // ebx
  int v11; // eax
  const void **v12; // eax
  int v13; // eax
  VirtualArea::ProtoType *v14; // [esp+Ch] [ebp-12Ch]
  int v15; // [esp+14h] [ebp-124h]
  VirtualArea::ProtoType *v16; // [esp+18h] [ebp-120h] BYREF
  VirtualArea::ProtoType *v17; // [esp+1Ch] [ebp-11Ch] BYREF
  VirtualArea::ProtoType *v18; // [esp+20h] [ebp-118h] BYREF
  VirtualArea::ProtoType _Holeval; // [esp+24h] [ebp-114h] BYREF

  v3 = _Mid._Myptr - _First._Myptr;
  v4 = _Last._Myptr - _First._Myptr;
  v5 = v3;
  if ( v3 )
  {
    do
    {
      v6 = v4 % v5;
      v4 = v5;
      v5 = v6;
    }
    while ( v6 );
  }
  if ( v4 < _Last._Myptr - _First._Myptr && v4 > 0 )
  {
    v7 = v3;
    v8 = &_First._Myptr[v4];
    v14 = v8;
    v15 = v4;
    while ( 1 )
    {
      VirtualArea::ProtoType::ProtoType(&_Holeval, v8);
      if ( &v8[v7] == _Last._Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v16 = &v8[v7];
        p_First = (std::vector<VirtualArea::ProtoType>::iterator *)&v16;
      }
      Myptr = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          qmemcpy(v8, Myptr, sizeof(VirtualArea::ProtoType));
          v11 = _Last._Myptr - Myptr;
          v8 = Myptr;
          if ( v3 >= v11 )
          {
            v17 = &_First._Myptr[v3 - v11];
            v12 = (const void **)&v17;
          }
          else
          {
            v18 = &Myptr[v3];
            v12 = (const void **)&v18;
          }
          Myptr = (VirtualArea::ProtoType *)*v12;
        }
        while ( *v12 != v14 );
      }
      v13 = v15;
      qmemcpy(v8, &_Holeval, sizeof(VirtualArea::ProtoType));
      --v14;
      --v15;
      if ( v13 == 1 )
        break;
      v7 = v3;
      v8 = v14;
    }
  }
}

//----- (0044BB80) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<VirtualArea::ProtoType *,unsigned int,VirtualArea::ProtoType,std::allocator<VirtualArea::ProtoType>>(
        VirtualArea::ProtoType *_First,
        unsigned int _Count,
        const VirtualArea::ProtoType *_Val)
{
  unsigned int v4; // edi

  if ( _Count )
  {
    v4 = _Count;
    do
    {
      if ( _First )
        VirtualArea::ProtoType::ProtoType(_First, _Val);
      ++_First;
      --v4;
    }
    while ( v4 );
  }
}

//----- (0044BBB0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<VirtualArea::ProtoType>::iterator>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Mid,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  VirtualArea::ProtoType *Myptr; // esi
  int v4; // eax
  unsigned int m_dwVID; // ecx
  int v6; // eax
  unsigned int v7; // edi
  VirtualArea::ProtoType *v8; // ebx
  unsigned int v9; // ebp
  VirtualArea::ProtoType *v10; // esi
  VirtualArea::ProtoType *v11; // eax
  VirtualArea::ProtoType *v12; // esi
  unsigned int v13; // ecx
  VirtualArea::ProtoType *v14; // edi

  Myptr = _First._Myptr;
  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    if ( _Mid._Myptr->m_dwVID < _First._Myptr->m_dwVID )
      std::swap<VirtualArea::ProtoType>(_Mid._Myptr, _First._Myptr);
    if ( _Last._Myptr->m_dwVID < _Mid._Myptr->m_dwVID )
      std::swap<VirtualArea::ProtoType>(_Last._Myptr, _Mid._Myptr);
    if ( _Mid._Myptr->m_dwVID < _First._Myptr->m_dwVID )
      goto LABEL_31;
  }
  else
  {
    m_dwVID = _First._Myptr->m_dwVID;
    v6 = (v4 + 1) / 8;
    v7 = 272 * v6;
    v8 = &_First._Myptr[v6];
    v9 = 544 * v6;
    _First._Myptr = v8;
    if ( v8->m_dwVID < m_dwVID )
      std::swap<VirtualArea::ProtoType>(v8, Myptr);
    if ( Myptr[v9 / 0x110].m_dwVID < v8->m_dwVID )
      std::swap<VirtualArea::ProtoType>(&Myptr[v9 / 0x110], v8);
    if ( v8->m_dwVID < Myptr->m_dwVID )
      std::swap<VirtualArea::ProtoType>(v8, Myptr);
    v10 = &_Mid._Myptr[v7 / 0xFFFFFEF0];
    if ( _Mid._Myptr->m_dwVID < _Mid._Myptr[v7 / 0xFFFFFEF0].m_dwVID )
      std::swap<VirtualArea::ProtoType>(_Mid._Myptr, &_Mid._Myptr[v7 / 0xFFFFFEF0]);
    if ( _Mid._Myptr[v7 / 0x110].m_dwVID < _Mid._Myptr->m_dwVID )
      std::swap<VirtualArea::ProtoType>(&_Mid._Myptr[v7 / 0x110], _Mid._Myptr);
    if ( _Mid._Myptr->m_dwVID < v10->m_dwVID )
      std::swap<VirtualArea::ProtoType>(_Mid._Myptr, v10);
    v11 = _Last._Myptr;
    v12 = &_Last._Myptr[v7 / 0xFFFFFEF0];
    v13 = _Last._Myptr[v7 / 0xFFFFFEF0].m_dwVID;
    v14 = &_Last._Myptr[v9 / 0xFFFFFEF0];
    if ( v13 < _Last._Myptr[v9 / 0xFFFFFEF0].m_dwVID )
    {
      std::swap<VirtualArea::ProtoType>(v12, v14);
      v11 = _Last._Myptr;
    }
    if ( v11->m_dwVID < v12->m_dwVID )
      std::swap<VirtualArea::ProtoType>(v11, v12);
    if ( v12->m_dwVID < v14->m_dwVID )
      std::swap<VirtualArea::ProtoType>(v12, v14);
    if ( _Mid._Myptr->m_dwVID < v8->m_dwVID )
      std::swap<VirtualArea::ProtoType>(_Mid._Myptr, v8);
    if ( v12->m_dwVID < _Mid._Myptr->m_dwVID )
      std::swap<VirtualArea::ProtoType>(v12, _Mid._Myptr);
    if ( _Mid._Myptr->m_dwVID < v8->m_dwVID )
LABEL_31:
      std::swap<VirtualArea::ProtoType>(_Mid._Myptr, _First._Myptr);
  }
}

//----- (0044BD20) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        int _Hole,
        int _Bottom,
        VirtualArea::ProtoType _Val)
{
  int v4; // edi
  int v5; // eax
  bool i; // zf
  VirtualArea::ProtoType v7; // [esp-110h] [ebp-120h] BYREF

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5].m_dwVID < _First._Myptr[v5 - 1].m_dwVID )
      --v5;
    qmemcpy(&_First._Myptr[v4], &_First._Myptr[v5], sizeof(_First._Myptr[v4]));
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    qmemcpy(&_First._Myptr[v4], &_First._Myptr[_Bottom - 1], sizeof(_First._Myptr[v4]));
    v4 = _Bottom - 1;
  }
  VirtualArea::ProtoType::ProtoType(&v7, &_Val);
  std::_Push_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(_First, v4, _Hole, v7);
}

//----- (0044BDD0) --------------------------------------------------------
std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> *__cdecl std::_Unguarded_partition<std::vector<VirtualArea::ProtoType>::iterator>(
        std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> *result,
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  VirtualArea::ProtoType *v3; // ebx
  VirtualArea::ProtoType *v4; // esi
  unsigned int m_dwVID; // eax
  unsigned int v6; // ecx
  VirtualArea::ProtoType *Myptr; // edi
  VirtualArea::ProtoType *v8; // ebp
  VirtualArea::ProtoType *v9; // edx
  bool v10; // zf
  VirtualArea::ProtoType *v11; // edi
  VirtualArea::ProtoType *v12; // esi
  std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> *v13; // eax
  const VirtualArea::ProtoType *v14; // [esp-4h] [ebp-794h]
  const VirtualArea::ProtoType *v15; // [esp-4h] [ebp-794h]
  VirtualArea::ProtoType *_Plast; // [esp+10h] [ebp-780h]
  std::vector<VirtualArea::ProtoType>::iterator _Glast; // [esp+14h] [ebp-77Ch]
  VirtualArea::ProtoType *v18; // [esp+18h] [ebp-778h]
  VirtualArea::ProtoType *v19; // [esp+18h] [ebp-778h]
  VirtualArea::ProtoType *v20; // [esp+18h] [ebp-778h]
  VirtualArea::ProtoType v21; // [esp+1Ch] [ebp-774h] BYREF
  VirtualArea::ProtoType v22; // [esp+12Ch] [ebp-664h] BYREF
  VirtualArea::ProtoType v23; // [esp+23Ch] [ebp-554h] BYREF
  VirtualArea::ProtoType v24; // [esp+34Ch] [ebp-444h] BYREF
  VirtualArea::ProtoType v25; // [esp+45Ch] [ebp-334h] BYREF
  VirtualArea::ProtoType v26; // [esp+56Ch] [ebp-224h] BYREF
  VirtualArea::ProtoType v27; // [esp+67Ch] [ebp-114h] BYREF

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<VirtualArea::ProtoType>::iterator>(
    _First,
    (std::vector<VirtualArea::ProtoType>::iterator)v3,
    (std::vector<VirtualArea::ProtoType>::iterator)&_Last._Myptr[-1]);
  v4 = v3 + 1;
  for ( _Plast = v3 + 1; _First._Myptr < v3; --v3 )
  {
    m_dwVID = v3[-1].m_dwVID;
    if ( v3->m_dwVID > m_dwVID )
      break;
    if ( v3->m_dwVID < m_dwVID )
      break;
  }
  if ( v4 < _Last._Myptr )
  {
    v6 = v3->m_dwVID;
    do
    {
      if ( v6 > v4->m_dwVID )
        break;
      if ( v6 < v4->m_dwVID )
        break;
      ++v4;
    }
    while ( v4 < _Last._Myptr );
    _Plast = v4;
  }
  Myptr = v3;
  v8 = v4;
  for ( _Glast._Myptr = v3; ; Myptr = _Glast._Myptr )
  {
    while ( 1 )
    {
      for ( ; v8 < _Last._Myptr; ++v8 )
      {
        if ( v8->m_dwVID <= v3->m_dwVID )
        {
          if ( v8->m_dwVID < v3->m_dwVID )
            break;
          _Plast = v4 + 1;
          VirtualArea::ProtoType::ProtoType(&v26, v4);
          qmemcpy(v4, v8, sizeof(VirtualArea::ProtoType));
          qmemcpy(v8, &v26, sizeof(VirtualArea::ProtoType));
          ++v4;
          Myptr = _Glast._Myptr;
        }
      }
      v9 = _First._Myptr;
      v10 = Myptr == _First._Myptr;
      if ( Myptr > _First._Myptr )
      {
        v18 = Myptr - 1;
        do
        {
          if ( v3->m_dwVID <= v18->m_dwVID )
          {
            if ( v3->m_dwVID < v18->m_dwVID )
              break;
            VirtualArea::ProtoType::ProtoType(&v21, --v3);
            v9 = _First._Myptr;
            qmemcpy(v3, v18, sizeof(VirtualArea::ProtoType));
            qmemcpy(v18, &v21, sizeof(VirtualArea::ProtoType));
            v4 = _Plast;
            Myptr = _Glast._Myptr;
          }
          _Glast._Myptr = --Myptr;
          --v18;
        }
        while ( v9 < Myptr );
        v10 = Myptr == v9;
      }
      if ( v10 )
        break;
      _Glast._Myptr = --Myptr;
      if ( v8 == _Last._Myptr )
      {
        if ( Myptr != --v3 )
        {
          VirtualArea::ProtoType::ProtoType(&v23, Myptr);
          qmemcpy(Myptr, v3, sizeof(VirtualArea::ProtoType));
          qmemcpy(v3, &v23, sizeof(VirtualArea::ProtoType));
          v4 = _Plast;
        }
        _Plast = v4 - 1;
        VirtualArea::ProtoType::ProtoType(&v25, v3);
        qmemcpy(v3, &v4[-1], sizeof(VirtualArea::ProtoType));
        qmemcpy(&v4[-1], &v25, sizeof(VirtualArea::ProtoType));
        --v4;
      }
      else
      {
        v15 = v8;
        v20 = v8++;
        VirtualArea::ProtoType::ProtoType(&v27, v15);
        qmemcpy(v20, Myptr, sizeof(VirtualArea::ProtoType));
        qmemcpy(Myptr, &v27, sizeof(VirtualArea::ProtoType));
        v4 = _Plast;
      }
    }
    if ( v8 == _Last._Myptr )
      break;
    if ( v4 != v8 )
    {
      VirtualArea::ProtoType::ProtoType(&v24, v3);
      qmemcpy(v3, v4, sizeof(VirtualArea::ProtoType));
      qmemcpy(_Plast, &v24, sizeof(VirtualArea::ProtoType));
      v4 = _Plast;
    }
    v11 = v3;
    _Plast = v4 + 1;
    v12 = v8;
    v14 = v3;
    v19 = v8++;
    ++v3;
    VirtualArea::ProtoType::ProtoType(&v22, v14);
    qmemcpy(v11, v12, sizeof(VirtualArea::ProtoType));
    qmemcpy(v19, &v22, sizeof(VirtualArea::ProtoType));
    v4 = _Plast;
  }
  v13 = result;
  result->second._Myptr = v4;
  result->first._Myptr = v3;
  return v13;
}

//----- (0044C0F0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  int v2; // esi
  const VirtualArea::ProtoType *v3; // ebx
  VirtualArea::ProtoType v4; // [esp-110h] [ebp-120h] BYREF

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
    {
      --v3;
      --v2;
      VirtualArea::ProtoType::ProtoType(&v4, v3);
      std::_Adjust_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        _First,
        v2,
        _Last._Myptr - _First._Myptr,
        v4);
    }
    while ( v2 > 0 );
  }
}

//----- (0044C160) --------------------------------------------------------
void __cdecl std::_Pop_heap_0<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  VirtualArea::ProtoType v2; // [esp-110h] [ebp-230h] BYREF
  VirtualArea::ProtoType __that; // [esp+10h] [ebp-110h] BYREF

  VirtualArea::ProtoType::ProtoType(&__that, (const VirtualArea::ProtoType *)_Last._Myptr - 1);
  qmemcpy(&_Last._Myptr[-1], _First._Myptr, sizeof(_Last._Myptr[-1]));
  VirtualArea::ProtoType::ProtoType(&v2, &__that);
  std::_Adjust_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
    _First,
    0,
    &_Last._Myptr[-1] - _First._Myptr,
    v2);
}

//----- (0044C1E0) --------------------------------------------------------
VirtualArea::ProtoType *__thiscall std::vector<VirtualArea::ProtoType>::_Ufill(
        std::vector<VirtualArea::ProtoType> *this,
        VirtualArea::ProtoType *_Ptr,
        unsigned int _Count,
        const VirtualArea::ProtoType *_Val)
{
  std::_Uninit_fill_n<VirtualArea::ProtoType *,unsigned int,VirtualArea::ProtoType,std::allocator<VirtualArea::ProtoType>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (0044C210) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<VirtualArea::ProtoType>::iterator>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  VirtualArea::ProtoType *i; // esi
  unsigned int m_dwVID; // ecx
  VirtualArea::ProtoType *v4; // eax
  unsigned int v5; // ebp
  std::vector<VirtualArea::ProtoType>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_dwVID = i->m_dwVID;
      if ( i->m_dwVID >= _First._Myptr->m_dwVID )
      {
        v4 = i - 1;
        if ( m_dwVID < i[-1].m_dwVID )
        {
          do
          {
            v5 = v4[-1].m_dwVID;
            v6._Myptr = v4--;
          }
          while ( m_dwVID < v5 );
          if ( v6._Myptr != i )
            std::_Rotate<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
              v6,
              (std::vector<VirtualArea::ProtoType>::iterator)i,
              (std::vector<VirtualArea::ProtoType>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
          _First,
          (std::vector<VirtualArea::ProtoType>::iterator)i,
          (std::vector<VirtualArea::ProtoType>::iterator)&i[1]);
      }
    }
  }
}

//----- (0044C2A0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<VirtualArea::ProtoType>::iterator>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last)
{
  VirtualArea::ProtoType *i; // esi

  for ( i = _Last._Myptr; i - _First._Myptr > 1; --i )
    std::_Pop_heap_0<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType>(
      _First,
      (std::vector<VirtualArea::ProtoType>::iterator)i);
}

//----- (0044C300) --------------------------------------------------------
void __thiscall __noreturn std::vector<VirtualArea::ProtoType>::_Xlen(std::vector<VirtualArea::ProtoType> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0044C370) --------------------------------------------------------
void __thiscall std::vector<VirtualArea::ProtoType>::_Insert_n(
        std::vector<VirtualArea::ProtoType> *this,
        std::vector<VirtualArea::ProtoType>::iterator _Where,
        unsigned int _Count,
        const VirtualArea::ProtoType *_Val)
{
  VirtualArea::ProtoType *Myfirst; // ebx
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // ebx
  VirtualArea::ProtoType *v12; // eax
  VirtualArea::ProtoType *v13; // ecx
  VirtualArea::ProtoType *v14; // eax
  char *v15; // edi
  VirtualArea::ProtoType *v16; // eax
  VirtualArea::ProtoType *v17; // edi
  VirtualArea::ProtoType *Mylast; // ecx
  VirtualArea::ProtoType *v20; // edi
  VirtualArea::ProtoType *v21; // [esp-Ch] [ebp-144h]
  unsigned int v22; // [esp-8h] [ebp-140h]
  int v23; // [esp+0h] [ebp-138h] BYREF
  VirtualArea::ProtoType *_Ptr; // [esp+Ch] [ebp-12Ch]
  VirtualArea::ProtoType *_Newvec; // [esp+10h] [ebp-128h]
  VirtualArea::ProtoType _Tmp; // [esp+14h] [ebp-124h] BYREF
  int *v27; // [esp+128h] [ebp-10h]
  int v28; // [esp+134h] [ebp-4h]
  VirtualArea::ProtoType *_Wherea; // [esp+140h] [ebp+8h]
  VirtualArea::ProtoType *_Vala; // [esp+148h] [ebp+10h]

  v27 = &v23;
  VirtualArea::ProtoType::ProtoType(&_Tmp, _Val);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - Myfirst;
    else
      v7 = 0;
    if ( 15790320 - v7 < _Count )
      std::vector<VirtualArea::ProtoType>::_Xlen(this);
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<VirtualArea::ProtoType *,VirtualArea::ProtoType *>(_Where._Myptr, _Wherea, _Vala);
        std::fill<VirtualArea::ProtoType *,VirtualArea::ProtoType>(_Where._Myptr, &_Where._Myptr[_Count], &_Tmp);
      }
      else
      {
        std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<VirtualArea::ProtoType>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<VirtualArea::ProtoType *,VirtualArea::ProtoType>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 15790320 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( Myfirst )
        v10 = this->_Mylast - Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<VirtualArea::ProtoType>::size(this) + _Count;
      v11 = v9;
      v12 = (VirtualArea::ProtoType *)operator new((tagHeader *)(272 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<VirtualArea::ProtoType *,unsigned int,VirtualArea::ProtoType,std::allocator<VirtualArea::ProtoType>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (VirtualArea::ProtoType *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (0044C640) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<VirtualArea::ProtoType>::iterator,int>(
        std::vector<VirtualArea::ProtoType>::iterator _First,
        std::vector<VirtualArea::ProtoType>::iterator _Last,
        int _Ideal)
{
  VirtualArea::ProtoType *Myptr; // ebx
  VirtualArea::ProtoType *v4; // edi
  int v5; // eax
  VirtualArea::ProtoType *v7; // ebp
  std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<VirtualArea::ProtoType>::iterator>(
        (std::vector<VirtualArea::ProtoType>::iterator)Myptr,
        (std::vector<VirtualArea::ProtoType>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<VirtualArea::ProtoType>::iterator>(
        &_Mid,
        (std::vector<VirtualArea::ProtoType>::iterator)Myptr,
        (std::vector<VirtualArea::ProtoType>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<VirtualArea::ProtoType>::iterator,int>(
          _Mid.second,
          (std::vector<VirtualArea::ProtoType>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<VirtualArea::ProtoType>::iterator,int>(
          (std::vector<VirtualArea::ProtoType>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(
        (std::vector<VirtualArea::ProtoType>::iterator)Myptr,
        (std::vector<VirtualArea::ProtoType>::iterator)v4);
    std::sort_heap<std::vector<VirtualArea::ProtoType>::iterator>(
      (std::vector<VirtualArea::ProtoType>::iterator)Myptr,
      (std::vector<VirtualArea::ProtoType>::iterator)v4);
  }
}
// 44C72A: conditional instruction was optimized away because eax.4>=21

//----- (0044C770) --------------------------------------------------------
void __thiscall std::vector<VirtualArea::ProtoType>::reserve(
        std::vector<VirtualArea::ProtoType> *this,
        VirtualArea::ProtoType *_Count)
{
  VirtualArea::ProtoType *Myfirst; // eax
  unsigned int v4; // ebx
  VirtualArea::ProtoType *v5; // ecx
  int v6; // edi
  VirtualArea::ProtoType *v7; // [esp-18h] [ebp-34h]
  VirtualArea::ProtoType *Mylast; // [esp-14h] [ebp-30h]
  _DWORD v9[7]; // [esp+0h] [ebp-1Ch] BYREF
  VirtualArea::ProtoType *_Ptr; // [esp+24h] [ebp+8h]

  v9[3] = v9;
  if ( (unsigned int)_Count > 0xF0F0F0 )
    std::vector<VirtualArea::ProtoType>::_Xlen(this);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    Myfirst = (VirtualArea::ProtoType *)(this->_Myend - Myfirst);
  if ( Myfirst < _Count )
  {
    v4 = (unsigned int)_Count;
    _Ptr = (VirtualArea::ProtoType *)operator new((tagHeader *)(272 * (_DWORD)_Count));
    Mylast = this->_Mylast;
    v7 = this->_Myfirst;
    v9[6] = 0;
    std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(
      v7,
      Mylast,
      _Ptr);
    v5 = this->_Myfirst;
    if ( v5 )
      v6 = this->_Mylast - v5;
    else
      v6 = 0;
    if ( v5 )
      operator delete(this->_Myfirst);
    this->_Myend = &_Ptr[v4];
    this->_Mylast = &_Ptr[v6];
    this->_Myfirst = _Ptr;
  }
}

//----- (0044C860) --------------------------------------------------------
std::vector<VirtualArea::ProtoType>::iterator *__thiscall std::vector<VirtualArea::ProtoType>::insert(
        std::vector<VirtualArea::ProtoType> *this,
        std::vector<VirtualArea::ProtoType>::iterator *result,
        std::vector<VirtualArea::ProtoType>::iterator _Where,
        const VirtualArea::ProtoType *_Val)
{
  VirtualArea::ProtoType *Myfirst; // esi
  int v6; // esi
  std::vector<VirtualArea::ProtoType>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<VirtualArea::ProtoType>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (0044C8D0) --------------------------------------------------------
void __thiscall std::vector<VirtualArea::ProtoType>::push_back(
        std::vector<VirtualArea::ProtoType> *this,
        const VirtualArea::ProtoType *_Val)
{
  VirtualArea::ProtoType *Myfirst; // edi
  unsigned int v4; // ecx
  VirtualArea::ProtoType *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<VirtualArea::ProtoType *,unsigned int,VirtualArea::ProtoType,std::allocator<VirtualArea::ProtoType>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<VirtualArea::ProtoType>::insert(
      this,
      (std::vector<VirtualArea::ProtoType>::iterator *)&_Val,
      (std::vector<VirtualArea::ProtoType>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (0044C960) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *v15; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Left; // eax
  bool v17; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *v19; // eax
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == Ptr )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == Ptr )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( Ptr->_Color == 1 )
  {
    v15 = this;
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              v15,
              Parent);
            Left = Parent->_Right;
            v15 = this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
                v15,
                Left);
              Left = Parent->_Right;
              v15 = this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
              v15,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
              v15,
              Parent);
            Left = Parent->_Left;
            v15 = this;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(
                v15,
                Left);
              Left = Parent->_Left;
              v15 = this;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(
              v15,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v17 = Parent == v15->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v17 );
    }
    Right->_Color = 1;
  }
  if ( Ptr->_Myval.first._Myres >= 0x10 )
    operator delete(Ptr->_Myval.first._Bx._Ptr);
  Ptr->_Myval.first._Myres = 15;
  Ptr->_Myval.first._Mysize = 0;
  Ptr->_Myval.first._Bx._Buf[0] = 0;
  operator delete(Ptr);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v19 = result;
  result->_Ptr = _Where._Ptr;
  return v19;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0044CC40) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v2);
    operator delete(v2);
  }
}

//----- (0044CC80) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType(
        VirtualArea::CVirtualAreaMgr *this,
        char *szFileName)
{
  int v3; // esi
  const char *v4; // edi
  const char *v5; // eax
  int v6; // eax
  unsigned int v7; // eax
  int v8; // eax
  int v9; // eax
  char v10; // cl
  float *p_m_fPointZ; // ecx
  int v12; // edx
  VirtualArea::ProtoType *Mylast; // esi
  VirtualArea::ProtoType *Myfirst; // ebp
  int v15; // edi
  VirtualArea::ProtoType *v16; // eax
  unsigned int v17; // esi
  char *v18; // ebx
  unsigned int v19; // eax
  char *v20; // esi
  unsigned int v21; // edi
  VirtualArea::ProtoType *v22; // edx
  VirtualArea::ProtoType *v24; // eax
  int v25; // edx
  VirtualArea::ProtoType *v26; // ebx
  char *v27; // edi
  VirtualArea::ProtoType *v28; // esi
  std::vector<VirtualArea::ProtoType> v29; // [esp+10h] [ebp-82A8h] BYREF
  VirtualArea::CVirtualAreaMgr *v30; // [esp+20h] [ebp-8298h]
  std::string _Keyval; // [esp+24h] [ebp-8294h] BYREF
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator result; // [esp+40h] [ebp-8278h] BYREF
  char *v33; // [esp+44h] [ebp-8274h]
  VirtualArea::ProtoType _Val; // [esp+48h] [ebp-8270h] BYREF
  char szString[260]; // [esp+158h] [ebp-8160h] BYREF
  CDelimitedFile v36; // [esp+25Ch] [ebp-805Ch] BYREF
  int v37; // [esp+82B4h] [ebp-4h]

  v30 = this;
  v3 = 0;
  CDelimitedFile::CDelimitedFile(&v36, "\t");
  v37 = 1;
  memset(&v29._Myfirst, 0, 12);
  std::vector<VirtualArea::ProtoType>::reserve(&v29, (VirtualArea::ProtoType *)0xA);
  VirtualArea::ProtoType::ProtoType(&_Val);
  v4 = szFileName;
  v5 = szFileName;
  if ( !szFileName )
    v5 = ms_szVirtualAreaScriptFileName_7;
  if ( !CDelimitedFile::Open(&v36, v5, -1, 0) )
  {
    if ( !szFileName )
      v4 = ms_szVirtualAreaScriptFileName_7;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
      aDWorkRylSource_8,
      99,
      aS_3,
      v4);
    if ( !v29._Myfirst )
      goto LABEL_85;
    goto LABEL_7;
  }
  if ( CDelimitedFile::ReadLine(&v36) )
  {
    while ( 1 )
    {
      ++v3;
      if ( !CDelimitedFile::ReadString(&v36, szString, 0x104u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          108,
          aVirtualArea,
          v3,
          "\"VID\"");
        goto LABEL_65;
      }
      _mbsnbcmp((unsigned __int8 *)szString, "0x", 2u);
      if ( v6 )
        v7 = atol(szString);
      else
        v7 = Math::Convert::StrToHex32(szString);
      _Val.m_dwVID = v7;
      if ( !CDelimitedFile::ReadString(&v36, _Val.m_szMapType, 0x20u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          111,
          aVirtualArea,
          v3,
          "\"MapType\"");
        goto LABEL_65;
      }
      _Keyval._Myres = 15;
      _Keyval._Mysize = 0;
      _Keyval._Bx._Buf[0] = 0;
      std::string::assign(&_Keyval, _Val.m_szMapType, strlen(_Val.m_szMapType));
      LOBYTE(v37) = 2;
      _Val.m_cMapType = std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::find(
                          &this->m_MapTypeMatching.m_matchMap,
                          &result,
                          &_Keyval)->_Ptr->_Myval.second;
      LOBYTE(v37) = 1;
      if ( _Keyval._Myres >= 0x10 )
        operator delete(_Keyval._Bx._Ptr);
      _Keyval._Myres = 15;
      _Keyval._Mysize = 0;
      _Keyval._Bx._Buf[0] = 0;
      if ( !CDelimitedFile::ReadData(&v36, (char *)&_Val.m_cZone) )
        break;
      if ( !CDelimitedFile::ReadData(&v36, (__int16 *)&_Val.m_wStartX) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          115,
          aVirtualArea,
          v3,
          "\"StartX\"");
        goto LABEL_65;
      }
      if ( !CDelimitedFile::ReadData(&v36, (__int16 *)&_Val.m_wStartZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          116,
          aVirtualArea,
          v3,
          "\"StartZ\"");
LABEL_71:
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&v29);
        goto LABEL_85;
      }
      if ( !CDelimitedFile::ReadData(&v36, (__int16 *)&_Val.m_wWidth) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          117,
          aVirtualArea,
          v3,
          "\"Width\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, (__int16 *)&_Val.m_wHeight) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          118,
          aVirtualArea,
          v3,
          "\"Height\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadString(&v36, szString, 0x104u) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          120,
          aVirtualArea,
          v3,
          "\"ArrangementFile\"");
        goto LABEL_71;
      }
      _stricmp(szString, "N/A");
      if ( v8 )
      {
        v9 = 0;
        do
        {
          v10 = szString[v9];
          _Val.m_szArrangementFileName[v9++] = v10;
        }
        while ( v10 );
      }
      else
      {
        _Val.m_szArrangementFileName[0] = 0;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[0].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[0].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[0].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          130,
          aVirtualArea,
          v3,
          "\"StartPosition(HUMAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[1].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[1].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_StartPos[1].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          131,
          aVirtualArea,
          v3,
          "\"StartPosition(AKHAN)\"");
        goto LABEL_71;
      }
      _Val.m_cMaxRespawnPos = 1;
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][0].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][0].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][0].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          134,
          aVirtualArea,
          v3,
          "\"RespawnPos1(HUMAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][1].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][1].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][1].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          135,
          aVirtualArea,
          v3,
          "\"RespawnPos2(HUMAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][2].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][2].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[0][2].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          136,
          aVirtualArea,
          v3,
          "\"RespawnPos3(HUMAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][0].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][0].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][0].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          137,
          aVirtualArea,
          v3,
          "\"RespawnPos1(AKHAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][1].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][1].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][1].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          138,
          aVirtualArea,
          v3,
          "\"RespawnPos2(AKHAN)\"");
        goto LABEL_71;
      }
      if ( !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][2].m_fPointX)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][2].m_fPointY)
        || !CDelimitedFile::ReadData(&v36, &_Val.m_RespawnPos[1][2].m_fPointZ) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
          aDWorkRylSource_8,
          139,
          aVirtualArea,
          v3,
          "\"RespawnPos3(AKHAN)\"");
        goto LABEL_71;
      }
      p_m_fPointZ = &_Val.m_RespawnPos[0][1].m_fPointZ;
      v12 = 2;
      do
      {
        if ( *(p_m_fPointZ - 2) != 0.0 && *p_m_fPointZ != 0.0 )
          ++_Val.m_cMaxRespawnPos;
        p_m_fPointZ += 3;
        --v12;
      }
      while ( v12 );
      std::vector<VirtualArea::ProtoType>::push_back(&v29, &_Val);
      if ( !CDelimitedFile::ReadLine(&v36) )
        goto LABEL_56;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
      aDWorkRylSource_8,
      114,
      aVirtualArea,
      v3,
      "\"Zone\"");
LABEL_65:
    if ( !v29._Myfirst )
      goto LABEL_85;
LABEL_7:
    operator delete(v29._Myfirst);
LABEL_85:
    v37 = -1;
    CDelimitedFile::~CDelimitedFile(&v36);
    return 0;
  }
LABEL_56:
  Mylast = v29._Mylast;
  Myfirst = v29._Myfirst;
  v15 = v29._Mylast - v29._Myfirst;
  std::_Sort<std::vector<VirtualArea::ProtoType>::iterator,int>(
    (std::vector<VirtualArea::ProtoType>::iterator)v29._Myfirst,
    (std::vector<VirtualArea::ProtoType>::iterator)v29._Mylast,
    v15);
  v16 = Myfirst;
  if ( Myfirst != &Mylast[-1] )
  {
    while ( v16->m_dwVID != v16[1].m_dwVID )
    {
      if ( ++v16 == &Mylast[-1] )
        goto LABEL_59;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
      aDWorkRylSource_8,
      160,
      (char *)&byte_4E1410,
      v16->m_dwVID);
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_85;
  }
LABEL_59:
  v17 = Myfirst != 0 ? v15 : 0;
  v30->m_VirtualAreaProtoTypeNum = v17;
  v18 = (char *)operator new[](272 * v17);
  v33 = v18;
  LOBYTE(v37) = 3;
  if ( v18 )
  {
    v19 = v17 - 1;
    if ( (int)(v17 - 1) >= 0 )
    {
      v20 = v18 + 200;
      v21 = v19 + 1;
      do
      {
        `vector constructor iterator'(v20 - 24, 0xCu, 2, (void *(__thiscall *)(void *))Position::Position);
        `vector constructor iterator'(v20, 0xCu, 6, (void *(__thiscall *)(void *))Position::Position);
        v20 += 272;
        --v21;
      }
      while ( v21 );
    }
    v22 = (VirtualArea::ProtoType *)v18;
  }
  else
  {
    v22 = 0;
  }
  LOBYTE(v37) = 1;
  v30->m_VirtualAreaProtoTypeArray = v22;
  if ( !v22 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType",
      aDWorkRylSource_8,
      169,
      aVirtualarea);
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_85;
  }
  v24 = Myfirst;
  if ( Myfirst != v29._Mylast )
  {
    v25 = (char *)v22 - (char *)Myfirst;
    v26 = v29._Mylast;
    do
    {
      v27 = (char *)v24 + v25;
      v28 = v24++;
      qmemcpy(v27, v28, 0x110u);
    }
    while ( v24 != v26 );
  }
  if ( Myfirst )
    operator delete(Myfirst);
  v37 = -1;
  CDelimitedFile::~CDelimitedFile(&v36);
  return 1;
}
// 44CDBB: variable 'v6' is possibly undefined
// 44CF3F: variable 'v8' is possibly undefined

//----- (0044D5D0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0044D690) --------------------------------------------------------
void __thiscall std::map<std::string,unsigned char>::~map<std::string,unsigned char>(
        std::map<std::string,unsigned char> *this)
{
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (0044D6C0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualAreaMgr::~CVirtualAreaMgr(VirtualArea::CVirtualAreaMgr *this)
{
  VirtualArea::ProtoType *m_VirtualAreaProtoTypeArray; // eax
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *Myhead; // eax
  const VirtualArea::MapTypeMatching *p_m_MapTypeMatching; // esi
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator result; // [esp+8h] [ebp-4h] BYREF

  m_VirtualAreaProtoTypeArray = this->m_VirtualAreaProtoTypeArray;
  this->m_VirtualAreaProtoTypeNum = 0;
  if ( m_VirtualAreaProtoTypeArray )
  {
    operator delete[](m_VirtualAreaProtoTypeArray);
    this->m_VirtualAreaProtoTypeArray = 0;
  }
  Myhead = this->m_MapTypeMatching.m_matchMap._Myhead;
  p_m_MapTypeMatching = &this->m_MapTypeMatching;
  std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(
    &p_m_MapTypeMatching->m_matchMap,
    &result,
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator)Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator)Myhead);
  operator delete(p_m_MapTypeMatching->m_matchMap._Myhead);
  p_m_MapTypeMatching->m_matchMap._Myhead = 0;
  p_m_MapTypeMatching->m_matchMap._Mysize = 0;
}

//----- (0044D710) --------------------------------------------------------
VirtualArea::CVirtualAreaMgr *__cdecl VirtualArea::CVirtualAreaMgr::GetInstance()
{
  if ( (_S5_6 & 1) == 0 )
  {
    _S5_6 |= 1u;
    ms_this.m_BGServerMgr = VirtualArea::CBGServerMgr::GetInstance();
    ms_this.m_DuelMgr = VirtualArea::CDuelMgr::GetInstance();
    ms_this.m_DungeonMgr = VirtualArea::CDungeonMgr::GetInstance();
    ms_this.m_VirtualAreaProtoTypeArray = 0;
    ms_this.m_VirtualAreaProtoTypeNum = 0;
    VirtualArea::MapTypeMatching::MapTypeMatching(&ms_this.m_MapTypeMatching);
    atexit(_E6_11);
  }
  return &ms_this;
}

//----- (0044D7A0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (0044D7C0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (0044D820) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0044D860) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::ExistBuildingCamp(CSiegeObjectMgr *this, unsigned int dwGID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // ecx
  CSiegeObject *second; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax

  Myhead = this->m_CampMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left == Myhead )
    return 0;
  while ( 1 )
  {
    second = Left->_Myval.second;
    if ( second )
    {
      if ( second->m_dwGID == dwGID && second->m_cState == 4 )
        break;
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
    if ( Left == Myhead )
      return 0;
  }
  return 1;
}

//----- (0044D8E0) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::ExistCampInRadius(CSiegeObjectMgr *this, const Position *Pos)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // ecx
  CSiegeObject *second; // eax
  double v5; // st6
  double v6; // st4
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax

  Myhead = this->m_CampMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left == Myhead )
    return 0;
  while ( 1 )
  {
    second = Left->_Myval.second;
    if ( second )
    {
      v5 = Pos->m_fPointZ - second->m_CurrentPos.m_fPointZ;
      v6 = Pos->m_fPointX - second->m_CurrentPos.m_fPointX;
      if ( sqrt(v5 * v5 + v6 * v6) <= 256.0 )
        break;
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
    if ( Left == Myhead )
      return 0;
  }
  return 1;
}

//----- (0044D980) --------------------------------------------------------
void __thiscall CSiegeObjectMgr::ProcessAllSiegeObject(CSiegeObjectMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // esi
  CMonster *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax

  Myhead = this->m_SiegeObjectMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( second )
        CMonster::Process(second);
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Left != i->_Right )
              break;
            Left = i;
          }
          Left = i;
        }
        else
        {
          Left = Left->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Left = j;
        }
      }
    }
    while ( Left != this->m_SiegeObjectMap._Myhead );
  }
}

//----- (0044D9F0) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::SendCampInfo(CSiegeObjectMgr *this, CSendStream *SendStream)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // ebx
  CSiegeObject *second; // edi
  char *Buffer; // eax
  char *v6; // esi
  CServerSetup *Instance; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v12; // [esp+0h] [ebp-4h]

  Myhead = this->m_CampMap._Myhead;
  Left = Myhead->_Left;
  v12 = Myhead;
  while ( Left != v12 )
  {
    second = Left->_Myval.second;
    if ( second )
    {
      Buffer = CSendStream::GetBuffer(SendStream, (char *)0x2B);
      v6 = Buffer;
      if ( Buffer )
      {
        *((_DWORD *)Buffer + 3) = second->m_dwCID;
        *((_DWORD *)Buffer + 4) = second->m_dwOwnerID;
        *((_DWORD *)Buffer + 5) = second->m_dwGID;
        *((_DWORD *)Buffer + 6) = second->m_CreatureStatus.m_nNowHP;
        Instance = CServerSetup::GetInstance();
        v6[28] = CServerSetup::GetServerZone(Instance);
        v6[29] = second->m_cState;
        v6[30] = second->m_cUpgradeStep;
        *(float *)(v6 + 31) = second->m_CurrentPos.m_fPointX;
        *(float *)(v6 + 35) = second->m_CurrentPos.m_fPointY;
        *(float *)(v6 + 39) = second->m_CurrentPos.m_fPointZ;
        CSendStream::WrapCrypt(SendStream, 0x2Bu, 0xA8u, 0, 0);
      }
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
  }
  return 1;
}

//----- (0044DAF0) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::SendChangeMaster(
        CSiegeObjectMgr *this,
        unsigned int dwCastleID,
        unsigned int dwNewGID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // edi
  CSiegeObject *second; // esi
  unsigned int m_nNowHP; // ecx
  unsigned __int8 m_cState; // dl
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax
  PktCastleCmd pktCC; // [esp+8h] [ebp-24h] BYREF

  Myhead = this->m_SiegeObjectMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( second )
      {
        if ( second->m_dwOwnerID == dwCastleID )
        {
          m_nNowHP = second->m_CreatureStatus.m_nNowHP;
          m_cState = second->m_cState;
          pktCC.m_dwCastleObjectID = second->m_dwCID;
          second->m_dwGID = dwNewGID;
          pktCC.m_dwCastleID = dwCastleID;
          pktCC.m_dwHP = m_nNowHP;
          pktCC.m_cState = m_cState;
          pktCC.m_dwValue = dwNewGID;
          pktCC.m_cSubCmd = 30;
          if ( PacketWrap::WrapCrypt((char *)&pktCC, 0x22u, 0xAAu, 0, 0) )
            CSiegeObject::SendToRadiusCell(second, (char *)&pktCC, 0x22u, 0xAAu);
        }
      }
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Left != i->_Right )
              break;
            Left = i;
          }
          Left = i;
        }
        else
        {
          Left = Left->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Left = j;
        }
      }
    }
    while ( Left != this->m_SiegeObjectMap._Myhead );
  }
  return 1;
}

//----- (0044DBF0) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::BroadCast(CSiegeObjectMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // edi
  CSiegeObject *second; // esi
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_dwGID; // edx
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_nNowHP; // ecx
  unsigned int m_nMaxHP; // edx
  unsigned int m_dwFrame; // eax
  float m_fDefaultDir; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax
  float fVel; // [esp+8h] [ebp-38h]
  PktSiegeBroadCast pktSiegeBC; // [esp+Ch] [ebp-34h] BYREF

  Myhead = this->m_SiegeObjectMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( second )
      {
        m_dwOwnerID = second->m_dwOwnerID;
        m_dwGID = second->m_dwGID;
        pktSiegeBC.m_dwCID = second->m_dwCID;
        m_wObjectType = second->m_wObjectType;
        pktSiegeBC.m_dwOwnerID = m_dwOwnerID;
        m_nNowHP = second->m_CreatureStatus.m_nNowHP;
        pktSiegeBC.m_wObjectType = m_wObjectType;
        LOBYTE(m_wObjectType) = second->m_cState;
        pktSiegeBC.m_dwGID = m_dwGID;
        m_nMaxHP = second->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        pktSiegeBC.m_cState = m_wObjectType;
        LOBYTE(m_wObjectType) = second->m_cUpgradeType;
        pktSiegeBC.m_dwNowHP = m_nNowHP;
        LOBYTE(m_nNowHP) = second->m_cSubState;
        pktSiegeBC.m_cUpgradeType = m_wObjectType;
        m_dwFrame = second->m_MotionInfo.m_dwFrame;
        pktSiegeBC.m_dwMaxHP = m_nMaxHP;
        LOBYTE(m_nMaxHP) = second->m_cUpgradeStep;
        pktSiegeBC.m_cSubState = m_nNowHP;
        m_fDefaultDir = second->m_fDefaultDir;
        pktSiegeBC.m_NetworkPos.m_usXPos = 0;
        pktSiegeBC.m_NetworkPos.m_usYPos = 0;
        pktSiegeBC.m_NetworkPos.m_usZPos = 0;
        pktSiegeBC.m_NetworkPos.m_cDirection = 0;
        pktSiegeBC.m_NetworkPos.m_cVelocity = 0;
        pktSiegeBC.m_cUpgradeStep = m_nMaxHP;
        pktSiegeBC.m_fDefaultDir = m_fDefaultDir;
        if ( m_dwFrame )
        {
          fVel = second->m_MotionInfo.m_fVelocity / (double)m_dwFrame;
          CNetworkPos::Initialize(
            &pktSiegeBC.m_NetworkPos,
            second->m_CurrentPos.m_fPointX,
            second->m_CurrentPos.m_fPointY,
            second->m_CurrentPos.m_fPointZ,
            second->m_MotionInfo.m_fDirection,
            fVel);
        }
        else
        {
          CNetworkPos::Initialize(
            &pktSiegeBC.m_NetworkPos,
            second->m_CurrentPos.m_fPointX,
            second->m_CurrentPos.m_fPointY,
            second->m_CurrentPos.m_fPointZ,
            second->m_MotionInfo.m_fDirection,
            0.0);
        }
        if ( PacketWrap::WrapCrypt((char *)&pktSiegeBC, 0x32u, 0xAFu, 0, 0) )
          CSiegeObject::SendToRadiusCell(second, (char *)&pktSiegeBC, 0x32u, 0xAFu);
      }
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Left != i->_Right )
              break;
            Left = i;
          }
          Left = i;
        }
        else
        {
          Left = Left->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Left = j;
        }
      }
    }
    while ( Left != this->m_SiegeObjectMap._Myhead );
  }
  return 1;
}

//----- (0044DD70) --------------------------------------------------------
CSiegeObject *__thiscall CSiegeObjectMgr::GetCamp(CSiegeObjectMgr *this, unsigned int dwCampID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  if ( !this->m_CampMap._Mysize )
    return 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CampMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
    &dwCampID);
  if ( pos._Ptr == this->m_CampMap._Myhead )
    return 0;
  else
    return pos._Ptr->_Myval.second;
}

//----- (0044DDB0) --------------------------------------------------------
CSiegeObject *__thiscall CSiegeObjectMgr::GetSiegeObject(CSiegeObjectMgr *this, unsigned int dwObjectID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  if ( !this->m_SiegeObjectMap._Mysize )
    return 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
    &dwObjectID);
  if ( pos._Ptr == this->m_SiegeObjectMap._Myhead )
    return 0;
  else
    return pos._Ptr->_Myval.second;
}



//----- (0044DDF0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CSiegeObject *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CSiegeObject *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0044DFA0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (0044E060) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0044E320) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0044E3E0) --------------------------------------------------------
void __thiscall CSiegeObjectMgr::DestroyAllCamp(CSiegeObjectMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // eax
  CSiegeObject *second; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v5; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator itr; // [esp+4h] [ebp-Ch] BYREF
  unsigned int _Keyval; // [esp+8h] [ebp-8h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator pos; // [esp+Ch] [ebp-4h] BYREF

  if ( this->m_CampMap._Mysize )
  {
    Myhead = this->m_CampMap._Myhead;
    Left = Myhead->_Left;
    for ( itr._Ptr = Myhead->_Left; itr._Ptr != Myhead; Left = itr._Ptr )
    {
      second = Left->_Myval.second;
      if ( second )
      {
        _Keyval = second->m_dwCID;
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
          (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
          &_Keyval);
        if ( pos._Ptr != this->m_SiegeObjectMap._Myhead )
          std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
            &this->m_SiegeObjectMap,
            (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&_Keyval,
            pos);
        ((void (__thiscall *)(CSiegeObject *, int))second->~CAggresiveCreature)(second, 1);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&itr);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(
      &this->m_CampMap,
      this->m_CampMap._Myhead->_Parent);
    this->m_CampMap._Myhead->_Parent = this->m_CampMap._Myhead;
    v5 = this->m_CampMap._Myhead;
    this->m_CampMap._Mysize = 0;
    v5->_Left = v5;
    this->m_CampMap._Myhead->_Right = this->m_CampMap._Myhead;
  }
}

//----- (0044E480) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::DeleteCamp(CSiegeObjectMgr *this, unsigned int dwCampID)
{
  std::map<unsigned long,CSiegeObject *> *p_m_CampMap; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Ptr; // ebx
  CSiegeObject *second; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator pos; // [esp+Ch] [ebp-4h] BYREF

  p_m_CampMap = &this->m_CampMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CampMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
    &dwCampID);
  Ptr = pos._Ptr;
  if ( pos._Ptr != this->m_CampMap._Myhead )
  {
    second = pos._Ptr->_Myval.second;
    if ( second )
    {
      dwCampID = second->m_dwCID;
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
        &dwCampID);
      if ( pos._Ptr != this->m_SiegeObjectMap._Myhead )
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
          &this->m_SiegeObjectMap,
          (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&dwCampID,
          pos);
      ((void (__thiscall *)(CSiegeObject *, int))second->~CAggresiveCreature)(second, 1);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
      p_m_CampMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&dwCampID,
      (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)Ptr);
  }
  return 1;
}

//----- (0044E500) --------------------------------------------------------
char __thiscall CSiegeObjectMgr::DeleteSiegeObject(CSiegeObjectMgr *this, unsigned int dwCID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Ptr; // edi
  CSiegeObject *second; // ecx
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator itr; // [esp+8h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&itr,
    &dwCID);
  Ptr = itr._Ptr;
  if ( itr._Ptr != this->m_SiegeObjectMap._Myhead )
  {
    second = itr._Ptr->_Myval.second;
    if ( second )
    {
      if ( second->m_wObjectType == 5337 )
        return CSiegeObjectMgr::DeleteCamp(this, second->m_dwOwnerID);
      ((void (__thiscall *)(CSiegeObject *, int))second->~CAggresiveCreature)(second, 1);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
      &this->m_SiegeObjectMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *)&dwCID,
      (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)Ptr);
  }
  return 1;
}

//----- (0044E560) --------------------------------------------------------
void __thiscall CSiegeObjectMgr::Destroy(CSiegeObjectMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Left; // eax
  CSiegeObject *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v5; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator itr; // [esp+4h] [ebp-4h] BYREF

  CSiegeObjectMgr::DestroyAllCamp(this);
  if ( this->m_SiegeObjectMap._Mysize )
  {
    Myhead = this->m_SiegeObjectMap._Myhead;
    Left = Myhead->_Left;
    for ( itr._Ptr = Myhead->_Left; itr._Ptr != Myhead; Left = itr._Ptr )
    {
      second = Left->_Myval.second;
      if ( second )
        ((void (__thiscall *)(CSiegeObject *, int))second->~CAggresiveCreature)(second, 1);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&itr);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(
      &this->m_SiegeObjectMap,
      this->m_SiegeObjectMap._Myhead->_Parent);
    this->m_SiegeObjectMap._Myhead->_Parent = this->m_SiegeObjectMap._Myhead;
    v5 = this->m_SiegeObjectMap._Myhead;
    this->m_SiegeObjectMap._Mysize = 0;
    v5->_Left = v5;
    this->m_SiegeObjectMap._Myhead->_Right = this->m_SiegeObjectMap._Myhead;
  }
}

//----- (0044E5D0) --------------------------------------------------------
CSiegeObject *__thiscall CSiegeObjectMgr::CreateSiegeObject(CSiegeObjectMgr *this, CastleObjectInfo *CastleObject)
{
  unsigned __int16 m_wObjectType; // dx
  int m_cUpgradeStep; // ecx
  int v5; // eax
  float fPointY; // ecx
  float fPointZ; // edx
  float fPointX; // eax
  CSiegeObject *v9; // eax
  CSiegeObject *v10; // eax
  CSiegeObject *v11; // esi
  std::pair<unsigned long const ,CSiegeObject *> _Val; // [esp+Ch] [ebp-40h] BYREF
  float v14; // [esp+14h] [ebp-38h]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> result; // [esp+18h] [ebp-34h] BYREF
  CMonster::MonsterCreateInfo MonsterCreate; // [esp+20h] [ebp-2Ch] BYREF
  int v17; // [esp+48h] [ebp-4h]

  m_wObjectType = CastleObject->m_wObjectType;
  m_cUpgradeStep = CastleObject->m_cUpgradeStep;
  if ( m_wObjectType == 5000 )
    v5 = m_cUpgradeStep + 6 * (CastleObject->m_cUpgradeType + 6 * CastleObject->m_cState) + 5000;
  else
    v5 = m_cUpgradeStep + m_wObjectType + 6 * CastleObject->m_cState;
  fPointY = CastleObject->m_Pos.fPointY;
  MonsterCreate.m_dwCID = CastleObject->m_dwCID;
  fPointZ = CastleObject->m_Pos.fPointZ;
  MonsterCreate.m_nKID = v5;
  fPointX = CastleObject->m_Pos.fPointX;
  MonsterCreate.m_dwPID = 0;
  MonsterCreate.m_nMovingPattern = 1;
  MonsterCreate.m_wRespawnArea = 0;
  MonsterCreate.m_bScout = 0;
  *(float *)&_Val.first = fPointX;
  *(float *)&_Val.second = fPointY;
  v14 = fPointZ;
  MonsterCreate.m_Pos.m_fPointX = fPointX;
  MonsterCreate.m_Pos.m_fPointY = fPointY;
  MonsterCreate.m_Pos.m_fPointZ = fPointZ;
  v9 = (CSiegeObject *)operator new((tagHeader *)0x380);
  v17 = 0;
  if ( v9 )
  {
    CSiegeObject::CSiegeObject(v9, &MonsterCreate, CastleObject);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  v17 = -1;
  if ( !v11 )
    return 0;
  _Val.first = v11->m_dwCID;
  _Val.second = v11;
  if ( !std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
          &this->m_SiegeObjectMap,
          &result,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)&_Val)->second )
  {
    CSiegeObjectMgr::DeleteSiegeObject(this, v11->m_dwCID);
    return 0;
  }
  return v11;
}
// 44E68E: variable 'v10' is possibly undefined

//----- (0044E700) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
CSiegeObject *__thiscall CSiegeObjectMgr::CreateCamp(
        CSiegeObjectMgr *this,
        unsigned int dwCID,
        unsigned int dwCampID,
        unsigned int dwGID,
        unsigned __int16 dwHP,
        unsigned __int8 cState,
        unsigned __int8 cUpgradeStep,
        __int128 Pos)
{
  CSiegeObject *v9; // eax
  int v10; // eax
  int v11; // esi
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> result; // [esp+Ch] [ebp-3Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> v14; // [esp+14h] [ebp-34h] BYREF
  CMonster::MonsterCreateInfo MonsterCreate; // [esp+1Ch] [ebp-2Ch] BYREF
  int v16; // [esp+44h] [ebp-4h]

  MonsterCreate.m_dwCID = dwCID;
  MonsterCreate.m_nKID = cUpgradeStep + 6 * cState + 5337;
  MonsterCreate.m_dwPID = 0;
  MonsterCreate.m_nMovingPattern = 1;
  MonsterCreate.m_wRespawnArea = 0;
  MonsterCreate.m_bScout = 0;
  MonsterCreate.m_Pos = (Position)Pos;
  v9 = (CSiegeObject *)operator new((tagHeader *)0x380);
  v16 = 0;
  if ( v9 )
  {
    CSiegeObject::CSiegeObject(v9, &MonsterCreate, dwCampID, dwGID, dwHP, cState, cUpgradeStep, SBYTE12(Pos));
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  v16 = -1;
  if ( !v11 )
    return 0;
  LODWORD(Pos) = *(_DWORD *)(v11 + 32);
  DWORD1(Pos) = v11;
  if ( !std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
          &this->m_SiegeObjectMap,
          &result,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)&Pos)->second )
  {
    CSiegeObjectMgr::DeleteSiegeObject(this, *(_DWORD *)(v11 + 32));
    return 0;
  }
  LODWORD(Pos) = *(_DWORD *)(v11 + 820);
  DWORD1(Pos) = v11;
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
    &this->m_CampMap,
    &v14,
    (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)&Pos);
  return (CSiegeObject *)v11;
}
// 44E700: variables would overlap: ^80.16 and stkvar "Pos" ^80.12(has user info),stkvar "bFullHP" ^8C.4(has user info)

//----- (0044E840) --------------------------------------------------------
CSiegeObject *__userpurge CSiegeObjectMgr::CreateSiegeArms@<eax>(
        CSiegeObjectMgr *this@<ecx>,
        unsigned int dwCID,
        unsigned int dwOwnerID,
        unsigned int dwGID,
        unsigned int dwHP,
        unsigned __int16 wObjectType,
        unsigned __int8 cState,
        unsigned __int8 cUpgradeStep,
        __int128 Pos)
{
  int v10; // eax
  CSiegeObject *v11; // eax
  int v12; // eax
  int v13; // esi
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> result; // [esp+Ch] [ebp-34h] BYREF
  CMonster::MonsterCreateInfo MonsterCreate; // [esp+14h] [ebp-2Ch] BYREF
  int v17; // [esp+3Ch] [ebp-4h]

  if ( wObjectType == 5000 )
    v10 = cUpgradeStep + 36 * cState + 5000;
  else
    v10 = cUpgradeStep + wObjectType + 6 * cState;
  MonsterCreate.m_dwCID = dwCID;
  MonsterCreate.m_nKID = v10;
  MonsterCreate.m_dwPID = 0;
  MonsterCreate.m_nMovingPattern = 3;
  MonsterCreate.m_wRespawnArea = 0;
  MonsterCreate.m_bScout = 0;
  MonsterCreate.m_Pos = (Position)Pos;
  v11 = (CSiegeObject *)operator new((tagHeader *)0x380);
  v17 = 0;
  if ( v11 )
  {
    CSiegeObject::CSiegeObject(v11, &MonsterCreate, dwOwnerID, dwGID, dwHP, wObjectType, cState, cUpgradeStep);
    v13 = v12;
  }
  else
  {
    v13 = 0;
  }
  v17 = -1;
  if ( !v13 )
    return 0;
  LODWORD(Pos) = *(_DWORD *)(v13 + 32);
  DWORD1(Pos) = v13;
  if ( !std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(
          &this->m_SiegeObjectMap,
          &result,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)&Pos)->second )
  {
    CSiegeObjectMgr::DeleteSiegeObject(this, *(_DWORD *)(v13 + 32));
    return 0;
  }
  return (CSiegeObject *)v13;
}
// 44E90A: variable 'v12' is possibly undefined

//----- (0044E980) --------------------------------------------------------
void __thiscall CSiegeObjectMgr::~CSiegeObjectMgr(CSiegeObjectMgr *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator v2; // [esp-8h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator v3; // [esp-8h] [ebp-28h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *Myhead; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v5; // [esp-4h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator result; // [esp+10h] [ebp-10h] BYREF
  int v7; // [esp+1Ch] [ebp-4h]

  v7 = 1;
  CSiegeObjectMgr::Destroy(this);
  Myhead = this->m_CampMap._Myhead;
  v2._Ptr = Myhead->_Left;
  LOBYTE(v7) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
    &this->m_CampMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)Myhead);
  operator delete(this->m_CampMap._Myhead);
  this->m_CampMap._Myhead = 0;
  this->m_CampMap._Mysize = 0;
  v5 = this->m_SiegeObjectMap._Myhead;
  v3._Ptr = v5->_Left;
  v7 = -1;
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(
    &this->m_SiegeObjectMap,
    &result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator)v5);
  operator delete(this->m_SiegeObjectMap._Myhead);
  this->m_SiegeObjectMap._Myhead = 0;
  this->m_SiegeObjectMap._Mysize = 0;
}

//----- (0044EA20) --------------------------------------------------------
void __thiscall CSiegeObjectMgr::CSiegeObjectMgr(CSiegeObjectMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *v3; // eax

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_SiegeObjectMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_SiegeObjectMap._Myhead->_Parent = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Myhead->_Left = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Myhead->_Right = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Mysize = 0;
  v3 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_CampMap);
  this->m_CampMap._Myhead = v3;
  v3->_Isnil = 1;
  this->m_CampMap._Myhead->_Parent = this->m_CampMap._Myhead;
  this->m_CampMap._Myhead->_Left = this->m_CampMap._Myhead;
  this->m_CampMap._Myhead->_Right = this->m_CampMap._Myhead;
  this->m_CampMap._Mysize = 0;
}

//----- (0044EAB0) --------------------------------------------------------
CSiegeObjectMgr *__cdecl CSiegeObjectMgr::GetInstance()
{
  if ( (_S5_7 & 1) == 0 )
  {
    _S5_7 |= 1u;
    CSiegeObjectMgr::CSiegeObjectMgr(&ms_this_0);
    atexit(_E6_12);
  }
  return &ms_this_0;
}

//----- (0044EB10) --------------------------------------------------------
Item::CItem *__thiscall CMonster::SellToCharacter(
        CMonster *this,
        CCharacter *lpCustomer,
        unsigned __int16 wKindItem,
        TakeType takeType,
        unsigned int *dwPrice)
{
  return 0;
}

//----- (0044EB30) --------------------------------------------------------
char __thiscall CMonster::GetNation(CMonster *this)
{
  return this->m_MonsterInfo.m_cNation;
}

//----- (0044EB40) --------------------------------------------------------
void __thiscall CMonster::~CMonster(CMonster *this)
{
  this->__vftable = (CMonster_vtbl *)&CMonster::`vftable';
  CAggresiveCreature::~CAggresiveCreature(this);
}
// 4E1618: using guessed type void *CMonster::`vftable';

//----- (0044EB50) --------------------------------------------------------
char __thiscall CMonster::InitMonster(CMonster *this, Position *Pos, CCell::CellMoveType eMoveType)
{
  CellPosition *p_m_CellPos; // ebx
  unsigned __int16 m_nMaxHP; // ax
  unsigned __int16 m_nMaxMP; // cx
  unsigned __int16 m_wDefaultSearchRange; // cx
  unsigned int m_dwLastTick; // eax
  MotionInfo *p_m_MotionInfo; // esi

  p_m_CellPos = &this->m_CellPos;
  CellPosition::MoveTo(&this->m_CellPos, Pos);
  if ( p_m_CellPos->m_lpCell )
  {
    if ( eMoveType == DEAD )
    {
      this->m_nCurrentState = 5;
      this->m_CreatureStatus.m_nNowHP = 0;
      this->m_CreatureStatus.m_nNowMP = 0;
    }
    else
    {
      CCell::SetCreature(p_m_CellPos->m_lpCell, this->m_dwCID, (CCharacter *)this, eMoveType);
      m_nMaxHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      this->m_nCurrentState = 1;
      this->m_CreatureStatus.m_nNowHP = m_nMaxHP;
      this->m_CreatureStatus.m_nNowMP = m_nMaxMP;
    }
    this->m_CurrentPos.m_fPointX = Pos->m_fPointX;
    this->m_CurrentPos.m_fPointY = Pos->m_fPointY;
    m_wDefaultSearchRange = this->m_wDefaultSearchRange;
    this->m_CurrentPos.m_fPointZ = Pos->m_fPointZ;
    this->m_wSearchRange = m_wDefaultSearchRange;
    this->m_lpTarget = 0;
    this->m_lCurrentFrame = 0;
    this->m_bAttacking = 0;
    m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
    this->m_dwLastTime = m_dwLastTick;
    this->m_dwLastBehaviorTick = m_dwLastTick;
    p_m_MotionInfo = &this->m_MotionInfo;
    p_m_MotionInfo->m_fDirection = 0.0;
    p_m_MotionInfo->m_fVelocity = 0.0;
    *(_DWORD *)&p_m_MotionInfo->m_wAction = 0;
    p_m_MotionInfo->m_dwFrame = 0;
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::InitMonster",
      aDWorkRylSource_103,
      111,
      aCid0x08x_14,
      this->m_dwCID,
      Pos->m_fPointX,
      Pos->m_fPointY,
      Pos->m_fPointZ);
    return 0;
  }
}

//----- (0044EC70) --------------------------------------------------------
bool __thiscall CMonster::GetMotion(CMonster *this, unsigned int MotionID, MotionInfo *Motion)
{
  int v3; // eax
  unsigned __int16 m_wAction; // si
  char *v5; // eax
  bool result; // al

  switch ( MotionID )
  {
    case 1u:
      v3 = 1;
      goto LABEL_6;
    case 2u:
      v3 = 2;
      goto LABEL_6;
    case 8u:
      v3 = 0;
      goto LABEL_6;
    case 0x18u:
      v3 = 3;
LABEL_6:
      m_wAction = this->m_MonsterInfo.m_MonsterMotions[v3].m_wAction;
      v5 = (char *)this + 16 * v3;
      Motion->m_wAction = m_wAction;
      Motion->m_dwFrame = *((_DWORD *)v5 + 109);
      Motion->m_fVelocity = *((float *)v5 + 107);
      result = 1;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (0044ED00) --------------------------------------------------------
void __thiscall CMonster::UpdateBehavior(CMonster *this, unsigned int dwTick)
{
  if ( (this->m_dwStatusFlag & 0x2000000) != 0 )
  {
    this->m_lCurrentFrame = 30;
  }
  else
  {
    switch ( this->m_nCurrentState )
    {
      case 1:
        this->NormalBehavior(this, dwTick);
        break;
      case 2:
        this->AttackBehavior(this, dwTick);
        break;
      case 3:
        this->ReturnBehavior(this, dwTick);
        break;
      case 4:
        this->EscapeBehavior(this);
        break;
      case 5:
        this->DeadBehavior(this, dwTick);
        break;
      default:
        return;
    }
  }
}

//----- (0044ED60) --------------------------------------------------------
char __thiscall CMonster::Process(CMonster *this)
{
  unsigned int m_dwLastTick; // ebp
  unsigned int v4; // edi

  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  if ( m_dwLastTick - this->m_dwLastBehaviorTick < 0x1E )
    return 0;
  if ( (int)((this->m_dwCID + 0x80000000) >> 16) % 3 != CPulse::GetInstance()->m_dwPulse % 3 )
    return 0;
  v4 = (m_dwLastTick - this->m_dwLastBehaviorTick) / 0x1E;
  if ( this->m_lCurrentFrame <= 0 )
  {
    if ( this->m_bAttacking )
      this->m_bAttacking = 0;
    goto LABEL_10;
  }
  if ( this->m_bAttacking )
LABEL_10:
    CMonster::UpdateBehavior(this, m_dwLastTick);
  this->m_dwLastBehaviorTick = m_dwLastTick;
  this->m_lCurrentFrame -= v4 / ((unsigned int)((this->m_dwStatusFlag & 0x4000000) != 0) + 1);
  return 1;
}
// 44EDDD: conditional instruction was optimized away because eax.4>=1

//----- (0044EE30) --------------------------------------------------------
int __thiscall CMonster::IsEnemy(CMonster *this, CCreature *lpTarget)
{
  signed int m_dwCID; // eax

  m_dwCID = lpTarget->m_dwCID;
  if ( m_dwCID >= 0 || (m_dwCID & 0xA0000000) == 0xA0000000 )
    return 2;
  else
    return 0;
}

//----- (0044EE60) --------------------------------------------------------
int __thiscall CMonster::CalculateFixLevelGap(CMonster *this, CAggresiveCreature *pDefender)
{
  int result; // eax

  if ( this->m_MonsterInfo.m_bFixLevelGap
    && (LOBYTE(result) = this->m_MonsterInfo.m_cFixLevelGap,
        (unsigned __int8)(LOBYTE(this->m_CreatureStatus.m_nLevel) - result) < pDefender->m_CreatureStatus.m_nLevel) )
  {
    return (unsigned __int8)result;
  }
  else
  {
    return CAggresiveCreature::CalculateLevelGap(this, (int)pDefender);
  }
}

//----- (0044EEA0) --------------------------------------------------------
char __userpurge CMonster::Attack@<al>(
        CMonster *this@<ecx>,
        int a2@<ebx>,
        AtType attackType,
        unsigned __int8 cDefenderNum,
        CAggresiveCreature **ppDefenders,
        unsigned __int8 *cDefenderJudges)
{
  CMonster *v6; // ebp
  bool v7; // zf
  unsigned __int8 v9; // dl
  CAggresiveCreature **v10; // ecx
  DefenserNode *v11; // esi
  CAggresiveCreature *v12; // edi
  unsigned int m_dwCID; // eax
  CSpell *v14; // ebx
  CAggresiveCreature *v15; // ecx
  int v16; // ebp
  unsigned __int8 *v17; // ebx
  CSendStream *v18; // edi
  int v19; // ecx
  CAggresiveCreature *v20; // eax
  CCell *m_lpCell; // ecx
  unsigned __int8 cOffencerJudge; // [esp+1Fh] [ebp-B1h] BYREF
  unsigned __int8 *v24; // [esp+20h] [ebp-B0h]
  CAggresiveCreature **v25; // [esp+24h] [ebp-ACh]
  CAggresiveCreature *pAttackCreature; // [esp+28h] [ebp-A8h]
  int nDefenserCount; // [esp+2Ch] [ebp-A4h]
  int wError; // [esp+30h] [ebp-A0h] BYREF
  int v29; // [esp+34h] [ebp-9Ch]
  DefenserNode Defenser[10]; // [esp+38h] [ebp-98h] BYREF

  v6 = this;
  v7 = this->m_CreatureStatus.m_nNowHP == 0;
  pAttackCreature = this;
  if ( v7 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonster::Attack", aDWorkRylSource_103, 622, aCid0x08x_22, this->m_dwCID);
    return 0;
  }
  v9 = cDefenderNum;
  if ( cDefenderNum > 0xAu )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::Attack",
      aDWorkRylSource_103,
      629,
      aCid0x08x_285,
      this->m_dwCID,
      cDefenderNum);
    v9 = 10;
  }
  memset(Defenser, 0, sizeof(Defenser));
  nDefenserCount = 0;
  if ( v9 )
  {
    v10 = ppDefenders;
    v11 = Defenser;
    v24 = cDefenderJudges;
    v25 = ppDefenders;
    v29 = v9;
    do
    {
      v12 = *v10;
      if ( *v10 )
      {
        m_dwCID = v12->m_dwCID;
        v14 = 0;
        if ( (m_dwCID & 0xD0000000) == 0 )
        {
          v14 = v12[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[4];
          goto LABEL_12;
        }
        if ( (m_dwCID & 0xA0000000) == 0xA0000000 )
        {
          v12 = (CAggresiveCreature *)v12[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
LABEL_12:
          if ( v12 )
          {
            v15 = *v10;
            cOffencerJudge = 0;
            wError = 0;
            v16 = ((int (__thiscall *)(CAggresiveCreature *, AtType, CMonster *, unsigned __int8 *, unsigned __int8 *, int *, int))v15->ApplyDamage)(
                    v15,
                    attackType,
                    v6,
                    &cOffencerJudge,
                    v24,
                    &wError,
                    a2);
            if ( v14 )
              v14->__vftable[8].Deactivate(v14, (CAggresiveCreature *)nDefenserCount, v16);
            v17 = (unsigned __int8 *)v25;
            a2 = (*(_BYTE *)v25 == 3) + 5;
            ((void (__thiscall *)(CAggresiveCreature *))v12->CalculateEquipDurability)(v12);
            v18 = (CSendStream *)v12[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[3];
            if ( v18 )
              GameClientSendPacket::SendCharAttacked(
                v18 + 8,
                pAttackCreature,
                *v25,
                attackType,
                pAttackCreature->m_MotionInfo.m_fDirection,
                v16,
                *v17,
                wError);
            v19 = nDefenserCount;
            v20 = ppDefenders[nDefenserCount];
            v11->m_cJudge = cDefenderJudges[nDefenserCount];
            v11->m_dwCharID = v20->m_dwCID;
            v11->m_wMaxHP = v20->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
            v11->m_wMaxMP = v20->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
            v11->m_sCurrHP = v20->m_CreatureStatus.m_nNowHP;
            LOWORD(v20) = v20->m_CreatureStatus.m_nNowMP;
            v11->m_wDamage = v16;
            v6 = (CMonster *)pAttackCreature;
            v11->m_sCurrMP = (unsigned __int16)v20;
            nDefenserCount = v19 + 1;
            v10 = v25;
            ++v11;
          }
        }
      }
      ++v10;
      v7 = v29 == 1;
      ++v24;
      v25 = v10;
      --v29;
    }
    while ( !v7 );
  }
  m_lpCell = v6->m_CellPos.m_lpCell;
  if ( m_lpCell )
    CCell::SendAttackInfo(m_lpCell, v6->m_dwCID, &attackType, nDefenserCount, Defenser);
  return 1;
}
// 44EEA0: could not find valid save-restore pair for ebx

//----- (0044F100) --------------------------------------------------------
bool __thiscall CMonster::IsDeadSummonMonster(CMonster *this)
{
  return this->m_bAdminCmdSummon && this->m_nCurrentState == 5;
}

//----- (0044F120) --------------------------------------------------------
unsigned __int16 __thiscall CMonster::ApplyDamage(
        CMonster *this,
        AtType attackType,
        CAggresiveCreature *pOffencer,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v7; // ax
  unsigned __int16 m_wType; // cx
  int v9; // eax
  const char *v10; // edx
  CMonsterShout *Instance; // eax
  unsigned int m_dwCID; // [esp-1Ch] [ebp-2Ch]
  CPacketDispatch *m_dwKID; // [esp-18h] [ebp-28h]
  unsigned __int16 m_fPointX; // [esp-14h] [ebp-24h]
  unsigned __int16 m_fPointZ; // [esp-10h] [ebp-20h]
  CMonsterShout::Behavior v17; // [esp-Ch] [ebp-1Ch]
  const char *v18; // [esp-8h] [ebp-18h]
  unsigned __int16 v19; // [esp-4h] [ebp-14h]
  unsigned __int16 wErrora; // [esp+24h] [ebp+14h]

  v7 = CAggresiveCreature::ApplyDamage(
         this,
         __SPAIR64__((unsigned int)pOffencer, *(_DWORD *)&attackType),
         cOffencerJudge,
         cDefenserJudge,
         wError);
  m_wType = 0;
  wErrora = v7;
  v9 = 0;
  if ( (attackType.m_wType & 0x8000) != 0 )
  {
    v9 = 3;
    m_wType = attackType.m_wType;
  }
  else if ( *cDefenserJudge == 4 )
  {
    v9 = 5;
  }
  v10 = 0;
  if ( pOffencer && (pOffencer->m_dwCID & 0xD0000000) == 0 )
    v10 = (const char *)&pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1];
  v19 = m_wType;
  v18 = v10;
  v17 = v9;
  m_fPointZ = (unsigned __int64)this->m_CurrentPos.m_fPointZ;
  m_fPointX = (unsigned __int64)this->m_CurrentPos.m_fPointX;
  m_dwKID = (CPacketDispatch *)this->m_MonsterInfo.m_dwKID;
  m_dwCID = this->m_dwCID;
  Instance = CMonsterShout::GetInstance();
  CMonsterShout::Shout(Instance, m_dwCID, m_dwKID, m_fPointX, m_fPointZ, v17, v18, v19);
  return wErrora;
}

//----- (0044F1C0) --------------------------------------------------------
void __cdecl std::fill_n<unsigned char *,short,enum ClientConstants::Judge>(
        unsigned __int8 *_First,
        unsigned __int16 _Count,
        const ClientConstants::Judge *_Val)
{
  int v3; // ecx

  if ( (__int16)_Count > 0 )
  {
    v3 = _Count;
    do
    {
      *_First++ = *(_BYTE *)_Val;
      --v3;
    }
    while ( v3 );
  }
}

//----- (0044F1E0) --------------------------------------------------------
void __thiscall CMonster::Attacked(CMonster *this)
{
  int m_nCurrentState; // eax

  m_nCurrentState = this->m_nCurrentState;
  this->m_bLongRangeAttacked = 1;
  if ( m_nCurrentState == 1 || m_nCurrentState == 3 )
    this->m_lCurrentFrame = 0;
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 105);
}

//----- (0044F220) --------------------------------------------------------
CMonster *__thiscall CMonster::`vector deleting destructor'(CMonster *this, char a2)
{
  CMonster::~CMonster(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0044F240) --------------------------------------------------------
void __thiscall CMonster::CMonster(CMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon)
{
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax

  CAggresiveCreature::CAggresiveCreature(this, MonsterCreate->m_dwCID);
  this->__vftable = (CMonster_vtbl *)&CMonster::`vftable';
  MonsterInfo::MonsterInfo(&this->m_MonsterInfo);
  this->m_OriginalPosition = MonsterCreate->m_Pos;
  this->m_dwLastBehaviorTick = 0;
  this->m_lpTarget = 0;
  this->m_lCurrentFrame = 0;
  this->m_nNormalMovingDelay = 0;
  this->m_nLeaveMovingNum = 0;
  this->m_nMovingPattern = MonsterCreate->m_nMovingPattern;
  this->m_nCurrentState = 0;
  this->m_wSearchRange = 0;
  this->m_bLongRangeAttacked = 0;
  this->m_bScout = MonsterCreate->m_bScout;
  this->m_bAttacking = 0;
  this->m_wRespawnArea = MonsterCreate->m_wRespawnArea;
  this->m_bAvoid = 0;
  this->m_bAdminCmdSummon = bAdminCmdSummon;
  this->m_dwPID = MonsterCreate->m_dwPID;
  this->m_cAttackableCreatureType = 1;
  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, MonsterCreate->m_nKID);
  if ( MonsterProtoType )
  {
    qmemcpy(&this->m_CreatureStatus, &MonsterProtoType->m_CreatureStatus, sizeof(this->m_CreatureStatus));
    qmemcpy(&this->m_MonsterInfo, MonsterProtoType, sizeof(this->m_MonsterInfo));
    this->m_wDefaultSearchRange = 8;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::CMonster",
      aDWorkRylSource_103,
      76,
      (char *)&byte_4E1850,
      MonsterCreate->m_nKID);
  }
}
// 4E1618: using guessed type void *CMonster::`vftable';

//----- (0044F390) --------------------------------------------------------
bool __thiscall CMonster::MultiAttack(CMonster *this)
{
  CAggresiveCreature *m_lpTarget; // eax
  float m_fPointZ; // ecx
  float nRange; // [esp+0h] [ebp-54h]
  AtType attackType; // [esp+10h] [ebp-44h]
  float fDir; // [esp+18h] [ebp-3Ch]
  CAggresiveCreature *ppAggresiveCreature[10]; // [esp+1Ch] [ebp-38h] BYREF
  unsigned __int8 nDefenserJudges[10]; // [esp+44h] [ebp-10h] BYREF

  memset(nDefenserJudges, 0, sizeof(nDefenserJudges));
  m_lpTarget = this->m_lpTarget;
  m_fPointZ = this->m_CurrentPos.m_fPointZ;
  ppAggresiveCreature[0] = m_lpTarget;
  fDir = CAggresiveCreature::CalcDir2D(
           this,
           this->m_CurrentPos.m_fPointX,
           m_fPointZ,
           m_lpTarget->m_CurrentPos.m_fPointX,
           m_lpTarget->m_CurrentPos.m_fPointZ);
  attackType.m_wType = 0;
  nRange = (double)this->m_CreatureStatus.m_StatusInfo.m_nAttackRange * 0.0099999998;
  return CAggresiveCreature::MultiAttack(
           this,
           attackType,
           1,
           ppAggresiveCreature,
           (CCell *)nDefenserJudges,
           this->m_CurrentPos,
           fDir,
           nRange,
           this->m_MonsterInfo.m_fAttackAngle,
           3);
}
// 44F42A: variable 'attackType' is possibly undefined

//----- (0044F440) --------------------------------------------------------
void __thiscall CMonster::Respawn(CMonster *this, unsigned int dwTick)
{
  float m_fPointX; // eax
  float m_fPointY; // ecx
  float m_fPointZ; // edx
  bool v6; // zf
  int m_wRespawnArea; // edi
  unsigned int m_dwPID; // eax
  CParty *Guild; // eax
  int m_wMapIndex; // ecx
  Position RespawnPos; // [esp+4h] [ebp-Ch] BYREF

  m_fPointX = this->m_OriginalPosition.m_fPointX;
  m_fPointY = this->m_OriginalPosition.m_fPointY;
  m_fPointZ = this->m_OriginalPosition.m_fPointZ;
  RespawnPos.m_fPointX = m_fPointX;
  v6 = this->m_nMovingPattern == 1;
  RespawnPos.m_fPointY = m_fPointY;
  RespawnPos.m_fPointZ = m_fPointZ;
  if ( !v6 )
  {
    m_wRespawnArea = this->m_wRespawnArea;
    RespawnPos.m_fPointX = (double)(Math::Random::SimpleRandom(dwTick, 2 * m_wRespawnArea, 0) - m_wRespawnArea)
                         + RespawnPos.m_fPointX;
    RespawnPos.m_fPointY = RespawnPos.m_fPointY + this->m_OriginalPosition.m_fPointY;
    RespawnPos.m_fPointZ = (double)(Math::Random::SimpleRandom(dwTick, 2 * m_wRespawnArea, 0) - this->m_wRespawnArea)
                         + RespawnPos.m_fPointZ;
  }
  CMonster::InitMonster(this, &RespawnPos, RESPAWN);
  m_dwPID = this->m_dwPID;
  this->m_nCurrentState = 1;
  Guild = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, m_dwPID);
  if ( Guild )
  {
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    this->m_pParty = Guild;
    Guild->Join(Guild, this->m_dwCID, 0, 0, m_wMapIndex);
  }
}

//----- (0044F530) --------------------------------------------------------
int __thiscall CMonster::SendMove(CMonster *this, unsigned __int16 nAniNum)
{
  int result; // eax
  unsigned int m_dwFrame; // eax
  unsigned __int8 m_wAction; // dl
  CCell *m_lpCell; // ecx
  CNetworkPos v7; // [esp+24h] [ebp-24h] BYREF
  PktMM pktMM; // [esp+2Ch] [ebp-1Ch] BYREF

  if ( (this->m_dwStatusFlag & 0x3000000) != 0 )
    this->m_MotionInfo.m_fVelocity = 0.0;
  if ( !this->m_CellPos.m_lpCell )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonster::SendMove", aDWorkRylSource_103, 526, (char *)&byte_4E18E8);
    return 0;
  }
  pktMM.m_dwMonID = this->m_dwCID;
  m_dwFrame = this->m_MotionInfo.m_dwFrame;
  if ( m_dwFrame )
  {
    *(_DWORD *)&v7.m_usXPos = this->m_MotionInfo.m_dwFrame;
    *(float *)&v7.m_usXPos = this->m_MotionInfo.m_fVelocity / (double)m_dwFrame;
  }
  else
  {
    *(_DWORD *)&v7.m_usXPos = 0;
  }
  CNetworkPos::Initialize(
    &v7,
    this->m_CurrentPos.m_fPointX,
    this->m_CurrentPos.m_fPointY,
    this->m_CurrentPos.m_fPointZ,
    this->m_MotionInfo.m_fDirection,
    *(float *)&v7.m_usXPos);
  m_wAction = this->m_MotionInfo.m_wAction;
  pktMM.m_NetworkPos = v7;
  pktMM.m_cAct = m_wAction;
  pktMM.m_cAniNum = nAniNum;
  if ( PacketWrap::WrapCrypt((char *)&pktMM, 0x1Au, 0x2Fu, 0, 0) )
  {
    m_lpCell = this->m_CellPos.m_lpCell;
    if ( m_lpCell )
    {
      CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktMM, 0x1Au, 0x2Fu);
      result = 1;
      this->m_nLeaveMovingNum = nAniNum;
      return result;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonster::SendMove",
      aDWorkRylSource_103,
      552,
      aCid0x08x_302,
      this->m_dwCID,
      this->m_CurrentPos.m_fPointX,
      this->m_CurrentPos.m_fPointY,
      this->m_CurrentPos.m_fPointZ);
  }
  result = 0;
  this->m_nLeaveMovingNum = nAniNum;
  return result;
}

//----- (0044F690) --------------------------------------------------------
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::find(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result,
        unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *v5; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead == this->_Myhead || *_Keyval < Myhead->_Myval )
  {
    v5 = result;
    result->_Ptr = this->_Myhead;
  }
  else
  {
    v5 = result;
    result->_Ptr = Myhead;
  }
  return v5;
}

//----- (0044F700) --------------------------------------------------------
char __thiscall CMonster::Dead(CMonster *this, CCharacter *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // eax
  int v5; // edi
  CAggresiveCreature *Award; // eax
  unsigned int v7; // ebp
  float m_fPointZ; // ecx
  CCell *m_lpCell; // ecx
  CAggresiveCreature *m_pCreature; // eax
  double v11; // st7
  double m_nMaxHP; // st6
  CAggresiveCreature *v13; // ecx
  CAggresiveCreature *v14; // eax
  CParty *v15; // eax
  CCastingSpell *p_m_CastingInfo; // esi
  unsigned int dwOwnerID; // [esp+18h] [ebp-48h] BYREF
  int nDropIndex; // [esp+1Ch] [ebp-44h]
  Position SetPosition; // [esp+20h] [ebp-40h] BYREF
  unsigned int dwItemKind[13]; // [esp+2Ch] [ebp-34h] BYREF
  CAggresiveCreature *pOffencera; // [esp+64h] [ebp+4h]
  CAggresiveCreature *pOffencerb; // [esp+64h] [ebp+4h]

  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  if ( (pOffencer->m_dwCID & 0xD0000000) == 0 )
    CCharacter::CheckTrigger(pOffencer, 4u, this->m_MonsterInfo.m_dwKID, this->m_CurrentPos, 1);
  v5 = 0;
  dwOwnerID = 0;
  Award = (CAggresiveCreature *)CThreat::GetAward(&this->m_Threat, dwItemKind, &dwOwnerID);
  pOffencera = Award;
  memset(&SetPosition, 0, sizeof(SetPosition));
  nDropIndex = 0;
  if ( (int)Award > 0 )
  {
    do
    {
      v7 = dwItemKind[v5];
      if ( v7 )
      {
        SetPosition.m_fPointX = this->m_CurrentPos.m_fPointX;
        m_fPointZ = this->m_CurrentPos.m_fPointZ;
        SetPosition.m_fPointY = this->m_CurrentPos.m_fPointY;
        SetPosition.m_fPointX = (double)nDropIndex + SetPosition.m_fPointX;
        SetPosition.m_fPointZ = m_fPointZ;
        nDropIndex = (int)Award / 2;
        if ( (int)Award / 2 <= v5 )
        {
          SetPosition.m_fPointX = SetPosition.m_fPointX - (double)nDropIndex;
          SetPosition.m_fPointZ = SetPosition.m_fPointZ + 1.0;
        }
        m_lpCell = this->m_CellPos.m_lpCell;
        if ( m_lpCell )
          CCell::SetItem(m_lpCell, 0, &SetPosition, 0, this->m_dwCID, dwOwnerID, v7, 1u, 0);
        Award = pOffencera;
      }
      nDropIndex = ++v5;
    }
    while ( v5 < (int)Award );
  }
  if ( this->m_Threat.m_ThreatList._Mysize )
    pOffencerb = (CAggresiveCreature *)this->m_Threat.m_ThreatList._Myhead->_Prev->_Myval.m_lThreatAmount;
  else
    pOffencerb = 0;
  CThreat::DivisionExp(&this->m_Threat);
  CCell::DeleteCreature(
    this->m_CellPos.m_lpCell,
    this->m_dwCID,
    (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
  if ( this->m_Threat.m_ThreatList._Mysize )
  {
    m_pCreature = this->m_Threat.m_ThreatList._Myhead->_Prev->_Myval.m_pCreature;
    if ( m_pCreature )
    {
      if ( this->m_CreatureStatus.m_nLevel - m_pCreature->m_CreatureStatus.m_nLevel >= 3 )
      {
        v11 = (double)(int)pOffencerb;
        m_nMaxHP = (double)this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        if ( v11 > 0.80000001 * m_nMaxHP )
        {
          if ( this->m_Threat.m_ThreatList._Mysize )
            v13 = this->m_Threat.m_ThreatList._Myhead->_Prev->_Myval.m_pCreature;
          else
            v13 = 0;
          if ( this->m_Threat.m_ThreatList._Mysize )
            v14 = this->m_Threat.m_ThreatList._Myhead->_Prev->_Myval.m_pCreature;
          else
            v14 = 0;
          CServerLog::SimpleLog(
            &g_Log,
            1,
            (char *)&byte_4E1928,
            this->m_dwCID,
            this->m_CreatureStatus.m_nLevel,
            v14->m_dwCID,
            v13->m_CreatureStatus.m_nLevel,
            v11 * 100.0 / m_nMaxHP);
        }
      }
    }
  }
  v15 = this->GetParty(this);
  if ( v15 )
    v15->Leave(v15, this->m_dwCID, this->m_CellPos.m_wMapIndex, 0);
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&this->m_SpellMgr.m_AffectedInfo);
  p_m_CastingInfo = &this->m_SpellMgr.m_CastingInfo;
  CCastingSpell::ClearEnchant(p_m_CastingInfo);
  CCastingSpell::DisableChant(p_m_CastingInfo, 0);
  return 1;
}

//----- (0044F990) --------------------------------------------------------
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result,
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
                (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
                (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0044FC50) --------------------------------------------------------
void __thiscall CMonster::CancelTarget(CMonster *this)
{
  CAggresiveCreature *m_lpTarget; // eax
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v3; // edi
  int m_nCurrentState; // edx
  unsigned int _Keyval; // [esp+4h] [ebp-8h] BYREF
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator itr; // [esp+8h] [ebp-4h] BYREF

  m_lpTarget = this->m_lpTarget;
  if ( m_lpTarget )
  {
    CThreat::DeleteThreatened(&m_lpTarget->m_Threat, this);
    CThreat::DeleteThreat(&this->m_Threat, this->m_lpTarget);
    v3 = (std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *)this->GetParty(this);
    if ( v3 )
    {
      _Keyval = this->m_lpTarget->m_dwCID;
      std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::find(
        v3 + 29,
        &itr,
        &_Keyval);
      if ( itr._Ptr != v3[29]._Myhead )
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(
          v3 + 29,
          &itr,
          itr);
    }
  }
  m_nCurrentState = this->m_nCurrentState;
  this->m_lpTarget = 0;
  this->m_bAttacking = 0;
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 103);
}

//----- (0044FD00) --------------------------------------------------------
char __thiscall Castle::CCastleMgr::SendSiegeTimeInfo(Castle::CCastleMgr *this, CSendStream *SendStream)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0xD);
  if ( !Buffer )
    return 0;
  Buffer[12] = this->m_bIsSiegeTime;
  return CSendStream::WrapCrypt(SendStream, 0xDu, 0xB0u, 0, 0);
}

//----- (0044FD40) --------------------------------------------------------
void __thiscall Castle::CCastleMgr::SetSiegeTime(Castle::CCastleMgr *this, char bIsSiegeTime)
{
  CCreatureManager *Instance; // eax
  char Buffer_In[16]; // [esp+0h] [ebp-10h] BYREF

  this->m_bIsSiegeTime = bIsSiegeTime;
  Buffer_In[12] = bIsSiegeTime;
  if ( PacketWrap::WrapCrypt(Buffer_In, 0xDu, 0xB0u, 0, 0) )
  {
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::SendAllCharacter(Instance, Buffer_In, 0xDu, 0xB0u, 1);
  }
}

//----- (0044FD90) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0044FDD0) --------------------------------------------------------
void __thiscall Castle::CCastleMgr::Destroy(Castle::CCastleMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Left; // eax
  Castle::CCastle *second; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v5; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator CastleItr; // [esp+4h] [ebp-4h] BYREF

  if ( this->m_CastleMap._Mysize )
  {
    Myhead = this->m_CastleMap._Myhead;
    Left = Myhead->_Left;
    for ( CastleItr._Ptr = Myhead->_Left; CastleItr._Ptr != Myhead; Left = CastleItr._Ptr )
    {
      second = Left->_Myval.second;
      if ( second )
      {
        Castle::CCastle::~CCastle(&Left->_Myval.second->m_CastleObjectMap);
        operator delete(second);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&CastleItr);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Erase(
      &this->m_CastleMap,
      this->m_CastleMap._Myhead->_Parent);
    this->m_CastleMap._Myhead->_Parent = this->m_CastleMap._Myhead;
    v5 = this->m_CastleMap._Myhead;
    this->m_CastleMap._Mysize = 0;
    v5->_Left = v5;
    this->m_CastleMap._Myhead->_Right = this->m_CastleMap._Myhead;
  }
}

//----- (0044FE50) --------------------------------------------------------
Castle::CCastle *__thiscall Castle::CCastleMgr::GetCastleByGID(Castle::CCastleMgr *this, unsigned int dwGID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Left; // ecx
  Castle::CCastle *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // eax

  Myhead = this->m_CastleMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left == Myhead )
    return 0;
  while ( 1 )
  {
    result = Left->_Myval.second;
    if ( result )
    {
      if ( result->m_dwGID == dwGID )
        break;
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
    if ( Left == Myhead )
      return 0;
  }
  return result;
}

//----- (0044FEC0) --------------------------------------------------------
char __thiscall Castle::CCastleMgr::ExistCastleInRadius(Castle::CCastleMgr *this, const Position *Pos)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Left; // esi
  Castle::CCastle *second; // ecx
  CSiegeObject *CastleEmblem; // eax
  double v7; // st6
  double v8; // st4
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // eax

  Myhead = this->m_CastleMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left == Myhead )
    return 0;
  while ( 1 )
  {
    second = Left->_Myval.second;
    if ( second )
    {
      CastleEmblem = Castle::CCastle::GetCastleEmblem(second);
      if ( CastleEmblem )
      {
        v7 = Pos->m_fPointZ - CastleEmblem->m_CurrentPos.m_fPointZ;
        v8 = Pos->m_fPointX - CastleEmblem->m_CurrentPos.m_fPointX;
        if ( sqrt(v7 * v7 + v8 * v8) <= 256.0 )
          break;
      }
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
    if ( Left == this->m_CastleMap._Myhead )
      return 0;
  }
  return 1;
}

//----- (0044FF80) --------------------------------------------------------
char __thiscall Castle::CCastleMgr::SendCastleInfo(Castle::CCastleMgr *this, CSendStream *SendStream)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Left; // edi
  Castle::CCastle *second; // esi
  char *Buffer; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v10; // [esp+8h] [ebp-4h]

  Myhead = this->m_CastleMap._Myhead;
  Left = Myhead->_Left;
  v10 = Myhead;
  while ( Left != Myhead )
  {
    second = Left->_Myval.second;
    if ( second )
    {
      Buffer = CSendStream::GetBuffer(SendStream, (char *)0x59);
      if ( Buffer )
      {
        *((_DWORD *)Buffer + 3) = second->m_dwCastleID;
        *((_DWORD *)Buffer + 4) = second->m_dwGID;
        Buffer[20] = second->m_cZone;
        *(_WORD *)(Buffer + 21) = second->m_wTax;
        *(_DWORD *)(Buffer + 23) = second->m_dwTaxIncome;
        Buffer[28] = second->m_cInvincibleDay;
        Buffer[27] = second->m_cTaxIncomeRemainDay;
        *(_DWORD *)(Buffer + 29) = *(_DWORD *)second->m_CastleRight.m_aryCastleRight;
        *(_DWORD *)(Buffer + 33) = *(_DWORD *)&second->m_CastleRight.m_aryCastleRight[4];
        *(_WORD *)(Buffer + 37) = *(_WORD *)&second->m_CastleRight.m_aryCastleRight[8];
        strncpy(Buffer + 39, second->m_szCastleName, 0x32u);
        CSendStream::WrapCrypt(SendStream, 0x59u, 0xA7u, 0, 0);
        Myhead = v10;
      }
    }
    if ( !Left->_Isnil )
    {
      Right = Left->_Right;
      if ( Right->_Isnil )
      {
        for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( Left != i->_Right )
            break;
          Left = i;
        }
        Left = i;
      }
      else
      {
        Left = Left->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          Left = j;
      }
    }
  }
  return 1;
}

//----- (00450080) --------------------------------------------------------
void __thiscall Castle::CCastleMgr::ProcessEmblemRegenHPAndMP(Castle::CCastleMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Left; // esi
  Castle::CCastle *second; // edi
  CSiegeObject *CastleEmblem; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // eax

  Myhead = this->m_CastleMap._Myhead;
  Left = Myhead->_Left;
  if ( Myhead->_Left != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( second && Castle::CCastle::GetCastleEmblem(Left->_Myval.second) )
      {
        CastleEmblem = Castle::CCastle::GetCastleEmblem(second);
        CastleEmblem->RegenHPAndMP(CastleEmblem, 0, 0, 1);
      }
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Left != i->_Right )
              break;
            Left = i;
          }
          Left = i;
        }
        else
        {
          Left = Left->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Left = j;
        }
      }
    }
    while ( Left != this->m_CastleMap._Myhead );
  }
}

//----- (00450110) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,Castle::CCastle *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,Castle::CCastle *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004502C0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00450580) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}



//----- (00450640) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00450700) --------------------------------------------------------
char __thiscall Castle::CCastleMgr::SerializeIn(
        Castle::CCastleMgr *this,
        char *lpBuffer_In,
        unsigned __int16 wBufferSize_In,
        unsigned __int8 cObjectNum)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *v5; // eax
  int v6; // eax
  int v7; // edi
  unsigned __int8 v9; // bl
  CServerSetup *Instance; // eax
  CastleObjectInfo *v11; // esi
  int v12; // ebx
  CSiegeObjectMgr *v13; // eax
  CSiegeObject *SiegeObject; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node _Val; // [esp+Ch] [ebp-1Ch] BYREF
  int v16; // [esp+24h] [ebp-4h]

  v5 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)operator new((tagHeader *)0x78);
  _Val._Left = v5;
  v16 = 0;
  if ( v5 )
  {
    Castle::CCastle::CCastle((Castle::CCastle *)v5, (const CastleInfoDB *)lpBuffer_In);
    v7 = v6;
  }
  else
  {
    v7 = 0;
  }
  v16 = -1;
  if ( !v7 )
    return 0;
  _Val._Left = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node **)(v7 + 12);
  _Val._Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)v7;
  if ( !std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::insert(
          &this->m_CastleMap,
          (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *)&_Val._Right,
          &_Val)->second )
  {
    Castle::CCastle::~CCastle((std::map<unsigned long,CSiegeObject *> *)v7);
    operator delete((void *)v7);
    return 0;
  }
  v9 = *(_BYTE *)(v7 + 27);
  Instance = CServerSetup::GetInstance();
  if ( v9 == (char)CServerSetup::GetServerZone(Instance) )
  {
    v11 = (CastleObjectInfo *)(lpBuffer_In + 117);
    if ( cObjectNum )
    {
      v12 = cObjectNum;
      do
      {
        v13 = CSiegeObjectMgr::GetInstance();
        SiegeObject = CSiegeObjectMgr::CreateSiegeObject(v13, v11);
        if ( SiegeObject )
          Castle::CCastle::InsertCastleObject((Castle::CCastle *)v7, SiegeObject);
        ++v11;
        --v12;
      }
      while ( v12 );
    }
    Castle::CCastle::UpgradeByEmblem((Castle::CCastle *)v7);
  }
  return 1;
}
// 450743: variable 'v6' is possibly undefined

//----- (00450810) --------------------------------------------------------
void __thiscall std::map<unsigned long,Castle::CCastle *>::~map<unsigned long,Castle::CCastle *>(
        std::map<unsigned long,Castle::CCastle *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00450840) --------------------------------------------------------
void __thiscall Castle::CCastleMgr::~CCastleMgr(Castle::CCastleMgr *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator v2; // [esp-8h] [ebp-1Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *Myhead; // [esp-4h] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator result; // [esp+4h] [ebp-10h] BYREF
  int v5; // [esp+10h] [ebp-4h]

  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)this;
  v5 = 0;
  Castle::CCastleMgr::Destroy(this);
  Myhead = this->m_CastleMap._Myhead;
  v2._Ptr = Myhead->_Left;
  v5 = -1;
  std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(
    &this->m_CastleMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator)Myhead);
  operator delete(this->m_CastleMap._Myhead);
  this->m_CastleMap._Myhead = 0;
  this->m_CastleMap._Mysize = 0;
}

//----- (004508B0) --------------------------------------------------------
Castle::CCastleMgr *__cdecl Castle::CCastleMgr::GetInstance()
{
  if ( (_S5_8 & 1) == 0 )
  {
    _S5_8 |= 1u;
    ms_this_1.m_CastleMap._Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&ms_this_1);
    ms_this_1.m_CastleMap._Myhead->_Isnil = 1;
    ms_this_1.m_CastleMap._Myhead->_Parent = ms_this_1.m_CastleMap._Myhead;
    ms_this_1.m_CastleMap._Myhead->_Left = ms_this_1.m_CastleMap._Myhead;
    ms_this_1.m_CastleMap._Myhead->_Right = ms_this_1.m_CastleMap._Myhead;
    ms_this_1.m_CastleMap._Mysize = 0;
    ms_this_1.m_bIsSiegeTime = 0;
    atexit(_E6_13);
  }
  return &ms_this_1;
}

//----- (00450940) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendServerZone(
        CSendStream *SendStream,
        unsigned int dwServerID,
        const sockaddr_in *gameServerTCPAddress,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x20);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwServerID;
  *((sockaddr_in *)Buffer + 1) = *gameServerTCPAddress;
  return CSendStream::WrapCrypt(SendStream, 0x20u, 0x29u, 0, usError);
}

//----- (00450990) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCSAuth(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwAuthCode,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x14);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_DWORD *)Buffer + 4) = dwAuthCode;
  return CSendStream::WrapCrypt(SendStream, 0x14u, 0xA0u, 0, usError);
}

//----- (004509D0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendMoveZoneToDBAgent(
        CSendStream *AgentSendStream,
        POS *newPos,
        unsigned int dwRequestKey,
        unsigned int dwUserID,
        unsigned __int8 cZone,
        char cChannel)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x24);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwRequestKey;
  *(_DWORD *)(Buffer + 18) = dwUserID;
  *((_WORD *)Buffer + 8) = 68;
  *((POS *)Buffer + 2) = *newPos;
  Buffer[22] = cZone;
  Buffer[23] = cChannel;
  return CSendStream::WrapHeader(AgentSendStream, 0x24u, 0x5Bu, 0, 0);
}

//----- (00450A40) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendServerZoneToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwRequestKey,
        unsigned int dwUserID,
        unsigned __int8 cZone,
        unsigned __int8 cChannel)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x18);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwRequestKey;
  *(_DWORD *)(Buffer + 18) = dwUserID;
  Buffer[22] = cZone;
  *((_WORD *)Buffer + 8) = 67;
  Buffer[23] = cChannel;
  return CSendStream::WrapHeader(AgentSendStream, 0x18u, 0x5Bu, 0, 0);
}

//----- (00450A90) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharLogin(
        CSendStream *SendStream,
        CCharacter *lpCharacter,
        sockaddr_in *lpSockAddr_In,
        unsigned __int16 usError)
{
  unsigned int m_dwUID; // ebx
  unsigned __int16 v5; // di
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  struct in_addr *v8; // eax
  char *v9; // eax
  CServerSetup *v10; // eax
  DWORD TickCount; // eax
  CCharacter_vtbl *v12; // edx
  unsigned int v14; // [esp-4h] [ebp-1FC4h]
  int nBufferSize_InOut; // [esp+10h] [ebp-1FB0h] BYREF
  INET_Addr v16; // [esp+14h] [ebp-1FACh] BYREF
  char SourceData[73]; // [esp+28h] [ebp-1F98h] BYREF
  char pBuffer[34]; // [esp+71h] [ebp-1F4Fh] BYREF
  int v19; // [esp+93h] [ebp-1F2Dh]

  memset(SourceData, 0, sizeof(SourceData));
  m_dwUID = 0;
  v5 = usError;
  nBufferSize_InOut = 8073;
  if ( !lpCharacter )
  {
    if ( !usError )
      v5 = 1;
    goto LABEL_11;
  }
  if ( usError )
  {
LABEL_11:
    memset(&SourceData[57], 0, 16);
    nBufferSize_InOut = 0;
    goto LABEL_12;
  }
  if ( !CCharacter::GetCharacterInfo(lpCharacter, pBuffer, &nBufferSize_InOut, (unsigned __int16 *)&SourceData[57]) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharLogin",
      aDWorkRylSource_50,
      47,
      (char *)&byte_4E1A90);
    v5 = 2;
  }
  v14 = lpCharacter->GetGID(lpCharacter);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v14);
  if ( Guild && Guild::CGuild::GetTitle(Guild, lpCharacter->m_dwCID) == 5 )
    v19 |= 0x80000000;
  v8 = (struct in_addr *)CServerSetup::GetInstance();
  *(struct in_addr *)&SourceData[12] = v8[123];
  *(struct in_addr *)&SourceData[16] = v8[124];
  *(struct in_addr *)&SourceData[20] = v8[125];
  *(struct in_addr *)&SourceData[24] = v8[126];
  v9 = inet_ntoa(v8[119]);
  INET_Addr::set_addr(&v16, v9, 0x2777u);
  m_dwUID = lpCharacter->m_dwUID;
  *(_DWORD *)&SourceData[28] = *(_DWORD *)&v16.m_SockAddr.sa_family;
  *(_DWORD *)&SourceData[32] = *(_DWORD *)&v16.m_SockAddr.sa_data[2];
  *(_DWORD *)&SourceData[36] = *(_DWORD *)&v16.m_SockAddr.sa_data[6];
  *(_DWORD *)&SourceData[40] = *(_DWORD *)&v16.m_SockAddr.sa_data[10];
  *(_DWORD *)&SourceData[44] = m_dwUID;
  v10 = CServerSetup::GetInstance();
  *(_DWORD *)&SourceData[48] = CServerSetup::GetServerID(v10);
  TickCount = GetTickCount();
  v12 = lpCharacter->__vftable;
  *(_DWORD *)&SourceData[52] = TickCount;
  SourceData[56] = v12->IsPeaceMode(lpCharacter);
LABEL_12:
  if ( !v5 )
    GAMELOG::LogCharLoginOut(
      m_dwUID,
      lpCharacter,
      lpSockAddr_In,
      pBuffer,
      nBufferSize_InOut,
      (const unsigned __int16 *)&SourceData[57],
      1u,
      0);
  if ( !CSendStream::WrapCompress(SendStream, SourceData, (char *)(nBufferSize_InOut + 73), 8u, 0, v5) )
    return 0;
  if ( lpCharacter )
    return Item::CDepositContainer::ClientUpdate(&lpCharacter->m_Deposit, SendStream);
  return 1;
}

//----- (00450CA0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendLoginToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwRequestKey,
        unsigned int dwSessionID,
        unsigned int dwUserID,
        unsigned int dwCharID,
        in_addr remoteAddress)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x3C);
  if ( !Buffer )
    return 0;
  *(Item::CItemFactory *)(Buffer + 12) = (Item::CItemFactory)CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID;
  *((_DWORD *)Buffer + 5) = dwSessionID;
  *((_DWORD *)Buffer + 6) = dwUserID;
  *((_DWORD *)Buffer + 7) = dwCharID;
  *((_DWORD *)Buffer + 8) = dwRequestKey;
  *((in_addr *)Buffer + 9) = remoteAddress;
  *((_WORD *)Buffer + 21) = 0;
  return CSendStream::WrapHeader(AgentSendStream, 0x3Cu, 0x26u, 0, 0);
}

//----- (00450D10) --------------------------------------------------------
unsigned __int16 *__cdecl std::copy<unsigned short *,unsigned short *>(
        unsigned __int8 *_First,
        unsigned __int16 *_Last,
        unsigned __int8 *_Dest)
{
  int v3; // eax

  memmove(_Dest, _First, 2 * (((char *)_Last - (char *)_First) >> 1));
  return (unsigned __int16 *)(2 * (((char *)_Last - (char *)_First) >> 1) + v3);
}
// 450D2F: variable 'v3' is possibly undefined

//----- (00450D40) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharMoveZone(
        CSendStream *SendStream,
        unsigned __int8 *lpChannelNums,
        unsigned __int8 cChannelNum,
        unsigned __int8 cZone,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  char *p_cChannelNum; // ecx
  char v8; // [esp+7h] [ebp-1h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x17);
  if ( !Buffer )
    return 0;
  Buffer[22] = cZone;
  if ( lpChannelNums )
  {
    v8 = 5;
    p_cChannelNum = &v8;
    if ( cChannelNum <= 5u )
      p_cChannelNum = (char *)&cChannelNum;
    memmove((unsigned __int8 *)Buffer + 12, lpChannelNums, 2 * ((2 * (unsigned __int8)*p_cChannelNum) >> 1));
  }
  return CSendStream::WrapCrypt(SendStream, 0x17u, 0x5Au, 0, usError);
}

//----- (00450DB0) --------------------------------------------------------
void __thiscall CCharacter::SaveToDBData(CCharacter *this)
{
  int m_nExp_high; // edx
  char m_nLevel; // al
  unsigned __int16 m_nNowMP; // ax
  float m_fPointX; // edx
  float m_fPointY; // eax
  float m_fPointZ; // edx

  m_nExp_high = HIDWORD(this->m_CreatureStatus.m_nExp);
  LODWORD(this->m_DBData.m_Info.Exp) = this->m_CreatureStatus.m_nExp;
  m_nLevel = this->m_CreatureStatus.m_nLevel;
  HIDWORD(this->m_DBData.m_Info.Exp) = m_nExp_high;
  LOWORD(m_nExp_high) = this->m_CreatureStatus.m_nNowHP;
  this->m_DBData.m_Info.Level = m_nLevel;
  m_nNowMP = this->m_CreatureStatus.m_nNowMP;
  this->m_DBData.m_Info.HP = m_nExp_high;
  m_fPointX = this->m_CurrentPos.m_fPointX;
  this->m_DBData.m_Info.MP = m_nNowMP;
  m_fPointY = this->m_CurrentPos.m_fPointY;
  this->m_DBData.m_Pos.LastPoint.fPointX = m_fPointX;
  m_fPointZ = this->m_CurrentPos.m_fPointZ;
  this->m_DBData.m_Pos.LastPoint.fPointY = m_fPointY;
  this->m_DBData.m_Pos.LastPoint.fPointZ = m_fPointZ;
}

//----- (00450E10) --------------------------------------------------------
char __thiscall CCharacter::GetCharacterInfo(
        CCharacter *this,
        char *pBuffer,
        int *nBufferSize_InOut,
        unsigned __int16 *lpUpdateLen)
{
  CCharacter *v4; // esi
  char m_cOperationFlags; // al
  int v8; // ecx
  Item::CEquipmentsContainer_vtbl *v9; // edx
  Item::CArrayContainer_vtbl *v10; // edx
  Item::CListContainer_vtbl *v11; // edx
  Item::CExchangeContainer_vtbl *v12; // edx
  unsigned __int16 v13; // ax
  unsigned int v14; // edi
  bool v15; // cc
  unsigned int dwSize; // [esp+8h] [ebp-10h] BYREF
  CCharacter *v17; // [esp+Ch] [ebp-Ch]
  int usTotalSize; // [esp+10h] [ebp-8h]
  int nCount; // [esp+14h] [ebp-4h]

  v4 = this;
  m_cOperationFlags = this->m_cOperationFlags;
  v17 = this;
  dwSize = 0;
  usTotalSize = 0;
  if ( (m_cOperationFlags & 1) != 0 )
  {
    CCharacter::SaveToDBData(this);
    v8 = 0;
    nCount = 0;
    do
    {
      switch ( v8 )
      {
        case 0:
          qmemcpy(pBuffer, &v4->m_DBData, 0x48u);
          v4 = v17;
          *lpUpdateLen = 72;
          break;
        case 1:
          *(float *)pBuffer = v4->m_DBData.m_Pos.LastPoint.fPointX;
          *((_DWORD *)pBuffer + 1) = LODWORD(v4->m_DBData.m_Pos.LastPoint.fPointY);
          *((_DWORD *)pBuffer + 2) = LODWORD(v4->m_DBData.m_Pos.LastPoint.fPointZ);
          *((_DWORD *)pBuffer + 3) = LODWORD(v4->m_DBData.m_Pos.SavePoint.fPointX);
          *((_DWORD *)pBuffer + 4) = LODWORD(v4->m_DBData.m_Pos.SavePoint.fPointY);
          *((_DWORD *)pBuffer + 5) = LODWORD(v4->m_DBData.m_Pos.SavePoint.fPointZ);
          lpUpdateLen[1] = 24;
          break;
        case 2:
          qmemcpy(pBuffer, &v4->m_DBData.m_Skill, 0x54u);
          v4 = v17;
          lpUpdateLen[2] = 84;
          break;
        case 3:
          qmemcpy(pBuffer, &v4->m_DBData.m_Quick, 0x64u);
          v4 = v17;
          lpUpdateLen[3] = 100;
          break;
        case 4:
          v9 = v4->m_Equipments.__vftable;
          dwSize = *nBufferSize_InOut;
          v9->SerializeOut(&v4->m_Equipments, pBuffer, &dwSize);
          lpUpdateLen[4] = dwSize;
          break;
        case 5:
          v10 = v4->m_Inventory.__vftable;
          dwSize = *nBufferSize_InOut;
          v10->SerializeOut(&v4->m_Inventory, pBuffer, &dwSize);
          lpUpdateLen[5] = dwSize;
          break;
        case 6:
          v11 = v4->m_ExtraSpace.__vftable;
          dwSize = *nBufferSize_InOut;
          v11->SerializeOut(&v4->m_ExtraSpace, pBuffer, &dwSize);
          lpUpdateLen[6] = dwSize;
          break;
        case 7:
          v12 = v4->m_Exchange.__vftable;
          dwSize = *nBufferSize_InOut;
          v12->SerializeOut(&v4->m_Exchange, pBuffer, &dwSize);
          lpUpdateLen[7] = dwSize;
          break;
        default:
          break;
      }
      v13 = lpUpdateLen[nCount];
      pBuffer += v13;
      v14 = *nBufferSize_InOut;
      usTotalSize += v13;
      v8 = nCount + 1;
      v15 = nCount + 1 < 8;
      *nBufferSize_InOut = v14 - dwSize;
      nCount = v8;
    }
    while ( v15 );
    *nBufferSize_InOut = (unsigned __int16)usTotalSize;
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::GetCharacterInfo",
      aDWorkRylSource_0,
      126,
      aCid0x08x_114,
      this->m_dwCID);
    return 0;
  }
}

//----- (00451030) --------------------------------------------------------
char __thiscall CCharacter::SetCharacterInfo(CCharacter *this, float *pBuffer, unsigned __int16 *usUpdateLen)
{
  int v5; // ecx
  char *v6; // eax
  void *p_m_Equipments; // ecx
  float fPointY; // edx
  float fPointZ; // eax
  int nCount; // [esp+10h] [ebp-4h]
  char *pBuffera; // [esp+18h] [ebp+4h]

  v5 = 0;
  for ( nCount = 0; nCount < 8; ++nCount )
  {
    v6 = (char *)usUpdateLen[v5];
    pBuffera = v6;
    switch ( v5 )
    {
      case 0:
        qmemcpy(&this->m_DBData, pBuffer, 0x48u);
        break;
      case 1:
        this->m_DBData.m_Pos.LastPoint.fPointX = *pBuffer;
        this->m_DBData.m_Pos.LastPoint.fPointY = pBuffer[1];
        this->m_DBData.m_Pos.LastPoint.fPointZ = pBuffer[2];
        this->m_DBData.m_Pos.SavePoint.fPointX = pBuffer[3];
        this->m_DBData.m_Pos.SavePoint.fPointY = pBuffer[4];
        this->m_DBData.m_Pos.SavePoint.fPointZ = pBuffer[5];
        break;
      case 2:
        qmemcpy(&this->m_DBData.m_Skill, pBuffer, sizeof(this->m_DBData.m_Skill));
        break;
      case 3:
        qmemcpy(&this->m_DBData.m_Quick, pBuffer, sizeof(this->m_DBData.m_Quick));
        break;
      case 4:
        p_m_Equipments = &this->m_Equipments;
        goto LABEL_11;
      case 5:
        p_m_Equipments = &this->m_Inventory;
        goto LABEL_11;
      case 6:
        p_m_Equipments = &this->m_ExtraSpace;
        goto LABEL_11;
      case 7:
        p_m_Equipments = &this->m_Exchange;
LABEL_11:
        (*(void (__thiscall **)(void *, float *, char *))(*(_DWORD *)p_m_Equipments + 24))(p_m_Equipments, pBuffer, v6);
        v6 = pBuffera;
        break;
      default:
        break;
    }
    pBuffer = (float *)((int)pBuffer + (_DWORD)v6);
    v5 = nCount + 1;
  }
  CreatureStatus::Init(&this->m_CreatureStatus, &this->m_DBData.m_Info);
  fPointY = this->m_DBData.m_Pos.LastPoint.fPointY;
  fPointZ = this->m_DBData.m_Pos.LastPoint.fPointZ;
  this->m_CurrentPos.m_fPointX = this->m_DBData.m_Pos.LastPoint.fPointX;
  this->m_CurrentPos.m_fPointY = fPointY;
  this->m_CurrentPos.m_fPointZ = fPointZ;
  return CCharacter::CalculateStatusData(this, 0);
}

//----- (00451160) --------------------------------------------------------
char __thiscall CCharacter::DBUpdate(CCharacter *this, DBUpdateData::UpdateType eUpdateType)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  void *v3; // esp
  int v5; // ebx
  int v6; // eax
  unsigned __int16 v8; // di
  CSingleDispatch *DispatchTable; // eax
  CSendStream *v10; // ebp
  int m_nCurrentUID_high; // edx
  unsigned int m_dwCID; // ecx
  const char *v13; // ebp
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int v15; // edi
  int nBufferSize_InOut; // [esp+0h] [ebp-1FDCh] BYREF
  unsigned __int8 cCMD[4]; // [esp+4h] [ebp-1FD8h]
  CSingleDispatch::Storage v18; // [esp+8h] [ebp-1FD4h] BYREF
  char szDest[64]; // [esp+10h] [ebp-1FCCh] BYREF
  char SourceData[12]; // [esp+50h] [ebp-1F8Ch] BYREF
  int m_nCurrentUID; // [esp+5Ch] [ebp-1F80h]
  int v22; // [esp+60h] [ebp-1F7Ch]
  unsigned int m_dwUID; // [esp+68h] [ebp-1F74h]
  unsigned int v24; // [esp+6Ch] [ebp-1F70h]
  int v25; // [esp+70h] [ebp-1F6Ch]
  __int16 v26; // [esp+7Ah] [ebp-1F62h]
  unsigned __int16 UpdateLen[8]; // [esp+7Ch] [ebp-1F60h] BYREF
  char pBuffer[8000]; // [esp+8Ch] [ebp-1F50h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v29; // [esp+1FD0h] [ebp-Ch]
  void *v30; // [esp+1FD4h] [ebp-8h]
  int v31; // [esp+1FD8h] [ebp-4h]

  v31 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v30 = &_ehhandler__DBUpdate_CCharacter__QAE_NW4UpdateType_DBUpdateData___Z;
  v29 = ExceptionList;
  v3 = alloca(8144);
  if ( (this->m_cOperationFlags & 1) == 0 || this->m_bLogout && eUpdateType != LOGOUT )
    return 0;
  v5 = 0;
  v6 = this->m_nDBUpdateCount - 1;
  this->m_nDBUpdateCount = v6;
  if ( v6 > 0 )
    return 0;
  v8 = 0;
  cCMD[0] = eUpdateType != LOGOUT ? 5 : 2;
  this->m_nDBUpdateCount = 180;
  nBufferSize_InOut = 8060;
  if ( CCharacter::GetCharacterInfo(this, pBuffer, &nBufferSize_InOut, UpdateLen) )
  {
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&v18, DispatchTable);
    v31 = 0;
    v5 = nBufferSize_InOut;
    if ( v18.m_lpDispatch )
    {
      v10 = (CSendStream *)&v18.m_lpDispatch[8];
      if ( !Item::CDepositContainer::DBUpdate(&this->m_Deposit, (CSendStream *)&v18.m_lpDispatch[8]) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::DBUpdate",
          aDWorkRylSource_0,
          66,
          aCid0x08x_200,
          this->m_dwCID);
      m_nCurrentUID = CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID;
      m_nCurrentUID_high = HIDWORD(CSingleton<Item::CItemFactory>::ms_pSingleton->m_nCurrentUID);
      m_dwCID = this->m_dwCID;
      m_dwUID = this->m_dwUID;
      v24 = m_dwCID;
      v22 = m_nCurrentUID_high;
      v26 = eUpdateType;
      v25 = 0;
      if ( !CSendStream::WrapCompress(v10, SourceData, (char *)(v5 + 60), 0x26u, 0, 0) )
      {
        v8 = 3;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::DBUpdate",
          aDWorkRylSource_0,
          79,
          aCid0x08xDbupda_0,
          this->m_dwCID);
      }
    }
    else
    {
      v8 = 2;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::DBUpdate",
        aDWorkRylSource_0,
        56,
        aCid0x08xDbupda,
        this->m_dwCID);
    }
    v31 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v18);
  }
  else
  {
    v8 = 1;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::DBUpdate",
      aDWorkRylSource_0,
      46,
      aCid0x08xDbupda_1,
      this->m_dwCID);
  }
  if ( eUpdateType == LOGOUT || v8 )
    GAMELOG::LogCharLoginOut(this->m_dwUID, this, &this->m_PublicAddress, pBuffer, v5, UpdateLen, cCMD[0], v8);
  v13 = "Unknown";
  switch ( eUpdateType )
  {
    case LOGIN:
      v13 = "Login";
      break;
    case LOGOUT:
      v13 = "Logout";
      break;
    case UPDATE:
      v13 = "Update";
      break;
    case ADMIN_LOGIN:
      v13 = "AdminLogin";
      break;
    case ZONEMOVE:
      v13 = "ZoneMove";
      break;
    default:
      break;
  }
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    v15 = m_lpGameClientDispatch->m_dwUID;
  else
    v15 = 0;
  Math::Convert::Hex64ToStr(szDest, this->m_DBData.m_Info.Exp);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::DBUpdate",
    aDWorkRylSource_0,
    112,
    aUidDCid0x08x0x_12,
    this->m_dwUID,
    this->m_dwCID,
    this,
    v15,
    v13,
    this->m_DBData.m_Info.Name,
    this->m_DBData.m_Info.Level,
    szDest);
  return 1;
}

//----- (00451480) --------------------------------------------------------
char __thiscall CCharacter::ItemDump(CCharacter *this, char *pBuffer, char **nBufferSize_InOut)
{
  char **v3; // esi
  char *v4; // edi
  int v6; // eax
  char *v7; // ecx
  char *v8; // ebx
  Item::CEquipmentsContainer_vtbl *v9; // eax
  Item::CArrayContainer_vtbl *v10; // edx
  Item::CListContainer_vtbl *v11; // edx
  Item::CExchangeContainer_vtbl *v12; // edx
  int v13; // ecx
  unsigned __int16 v14; // dx
  bool v15; // cc
  int nCount; // [esp+10h] [ebp-8h]
  int usTotalSize; // [esp+14h] [ebp-4h]

  v3 = nBufferSize_InOut;
  v4 = pBuffer;
  v6 = 0;
  v7 = pBuffer;
  *(_DWORD *)pBuffer = 0;
  v8 = v7 + 8;
  *((_DWORD *)v7 + 1) = 0;
  usTotalSize = 8;
  nCount = 0;
  do
  {
    switch ( nCount )
    {
      case 0:
        v9 = this->m_Equipments.__vftable;
        pBuffer = *v3;
        v9->SerializeOut(&this->m_Equipments, v8, (unsigned int *)&pBuffer);
        v6 = (int)pBuffer;
        *(_WORD *)v4 = (_WORD)pBuffer;
        break;
      case 1:
        v10 = this->m_Inventory.__vftable;
        pBuffer = *v3;
        v10->SerializeOut(&this->m_Inventory, v8, (unsigned int *)&pBuffer);
        v6 = (int)pBuffer;
        *((_WORD *)v4 + 1) = (_WORD)pBuffer;
        break;
      case 2:
        v11 = this->m_ExtraSpace.__vftable;
        pBuffer = *v3;
        v11->SerializeOut(&this->m_ExtraSpace, v8, (unsigned int *)&pBuffer);
        v6 = (int)pBuffer;
        *((_WORD *)v4 + 2) = (_WORD)pBuffer;
        break;
      case 3:
        v12 = this->m_Exchange.__vftable;
        pBuffer = *v3;
        v12->SerializeOut(&this->m_Exchange, v8, (unsigned int *)&pBuffer);
        v6 = (int)pBuffer;
        *((_WORD *)v4 + 3) = (_WORD)pBuffer;
        break;
      default:
        break;
    }
    v13 = *(unsigned __int16 *)&v4[2 * nCount];
    v8 += (unsigned __int16)v13;
    v14 = v13 + usTotalSize;
    *v3 -= v6;
    v15 = nCount + 1 < 4;
    usTotalSize += v13;
    ++nCount;
  }
  while ( v15 );
  *v3 = (char *)v14;
  return 1;
}

//----- (004515B0) --------------------------------------------------------
char __thiscall CConsoleWindow::Destroy(CConsoleWindow *this)
{
  CCSLock *p_m_ConsoleWindowLock; // ebp

  p_m_ConsoleWindowLock = &this->m_ConsoleWindowLock;
  EnterCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
  if ( this->m_hFont )
  {
    DeleteObject(this->m_hFont);
    this->m_hFont = 0;
  }
  if ( this->m_hWndInfo )
  {
    DestroyWindow(this->m_hWndInfo);
    this->m_hWndInfo = 0;
  }
  if ( this->m_hWndInput )
  {
    DestroyWindow(this->m_hWndInput);
    this->m_hWndInput = 0;
  }
  if ( this->m_hWndOutput )
  {
    DestroyWindow(this->m_hWndOutput);
    this->m_hWndOutput = 0;
  }
  if ( this->m_hWnd )
  {
    DestroyWindow(this->m_hWnd);
    this->m_hWnd = 0;
  }
  UnregisterClassA(WINCONSOLE_NAME, this->m_hInstance);
  LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
  return 1;
}

//----- (00451630) --------------------------------------------------------
void __thiscall CConsoleWindow::CConsoleWindow(
        CConsoleWindow *this,
        HINSTANCE__ *hInstance,
        HWND__ *hParentWnd,
        CConsoleCMDFactory *CMDFactory,
        CCommandProcess *CMDProcess)
{
  boost::pool<boost::default_user_allocator_new_delete> *v6; // eax

  this->__vftable = (CConsoleWindow_vtbl *)&CConsoleWindow::`vftable';
  InitializeCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
  this->m_hParentWnd = hParentWnd;
  this->m_hWnd = 0;
  this->m_hWndInfo = 0;
  this->m_hWndInput = 0;
  this->m_hWndOutput = 0;
  this->m_hFont = 0;
  this->m_hInstance = hInstance;
  this->m_fOldProc = 0;
  this->m_CMDProcess = CMDProcess;
  this->m_CMDFactory = CMDFactory;
  v6 = (boost::pool<boost::default_user_allocator_new_delete> *)operator new((tagHeader *)0x14);
  if ( v6 )
  {
    v6->first = 0;
    v6->list.ptr = 0;
    v6->list.sz = 0;
    v6->requested_size = 0x8000;
    v6->next_size = 32;
    this->m_lpMsgPool = v6;
  }
  else
  {
    this->m_lpMsgPool = 0;
  }
}
// 4E1DBC: using guessed type void *CConsoleWindow::`vftable';

//----- (004516F0) --------------------------------------------------------
HBRUSH __stdcall CConsoleWindow::ConsoleWindowProc(HWND__ *hWnd, UINT msg, char *wParam, LPARAM lParam)
{
  char *PropA; // esi
  HWND v6; // edi
  _RTL_CRITICAL_SECTION *v7; // edi
  char **v8; // esi
  HWND v9; // edi
  char **v10; // esi
  DWORD SysColor; // eax
  unsigned int msga; // [esp+10h] [ebp+8h]

  PropA = (char *)GetPropA(hWnd, CConsoleWindow::ms_this);
  if ( !PropA )
    return (HBRUSH)DefWindowProcA(hWnd, msg, (WPARAM)wParam, lParam);
  if ( msg == CConsoleWindow::ms_PrintOutputMsg && wParam )
  {
    v6 = (HWND)*((_DWORD *)PropA + 13);
    msga = SendMessageA(v6, 0x18Bu, 0, 0);
    if ( msga > 0x64 )
      SendMessageA(v6, 0x182u, 0, 0);
    SendMessageA(v6, 0x180u, 0, (LPARAM)wParam);
    SendMessageA(v6, 0x197u, msga - 1, 0);
    v7 = (_RTL_CRITICAL_SECTION *)(PropA + 4);
    EnterCriticalSection((LPCRITICAL_SECTION)(PropA + 4));
    v8 = (char **)*((_DWORD *)PropA + 19);
    if ( v8 )
    {
      *(_DWORD *)wParam = *v8;
      *v8 = wParam;
    }
LABEL_9:
    LeaveCriticalSection(v7);
    return 0;
  }
  if ( msg == CConsoleWindow::ms_PrintInfoMsg && wParam )
  {
    v9 = (HWND)*((_DWORD *)PropA + 11);
    SetWindowTextA(v9, wParam);
    UpdateWindow(v9);
    v7 = (_RTL_CRITICAL_SECTION *)(PropA + 4);
    EnterCriticalSection((LPCRITICAL_SECTION)(PropA + 4));
    v10 = (char **)*((_DWORD *)PropA + 19);
    if ( v10 )
    {
      *(_DWORD *)wParam = *v10;
      *v10 = wParam;
    }
    goto LABEL_9;
  }
  if ( msg == 16 )
  {
    SetWindowLongA(*((HWND *)PropA + 12), -4, *((_DWORD *)PropA + 16));
    PostQuitMessage(0);
    return 0;
  }
  else if ( msg == 308 )
  {
    SysColor = GetSysColor(15);
    SetBkColor((HDC)wParam, SysColor);
    return GetSysColorBrush(15);
  }
  else
  {
    return (HBRUSH)DefWindowProcA(hWnd, msg, (WPARAM)wParam, lParam);
  }
}

//----- (00451850) --------------------------------------------------------
void __thiscall CConsoleWindow::PrintOutput(CConsoleWindow *this, const char *lpText, int nTextLen)
{
  CCSLock *p_m_ConsoleWindowLock; // ebx
  boost::pool<boost::default_user_allocator_new_delete> *m_lpMsgPool; // ecx
  char *first; // esi
  int v7; // eax

  p_m_ConsoleWindowLock = &this->m_ConsoleWindowLock;
  EnterCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
  if ( this->m_hWnd )
  {
    m_lpMsgPool = this->m_lpMsgPool;
    if ( m_lpMsgPool )
    {
      first = (char *)m_lpMsgPool->first;
      if ( m_lpMsgPool->first )
        m_lpMsgPool->first = *(void **)first;
      else
        first = (char *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpMsgPool);
      if ( first )
      {
        v7 = _snprintf(first, 0x7FFFu, "%s", lpText);
        if ( v7 > 0 )
        {
          first[v7] = 0;
          PostMessageA(this->m_hWnd, CConsoleWindow::ms_PrintOutputMsg, (WPARAM)first, 0);
        }
      }
    }
  }
  LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
}

//----- (004518D0) --------------------------------------------------------
void __thiscall CConsoleWindow::PrintInfo(CConsoleWindow *this, const char *lpText, int nTextLen)
{
  CCSLock *p_m_ConsoleWindowLock; // ebx
  boost::pool<boost::default_user_allocator_new_delete> *m_lpMsgPool; // ecx
  char *first; // esi
  int v7; // eax

  p_m_ConsoleWindowLock = &this->m_ConsoleWindowLock;
  EnterCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
  if ( this->m_hWnd )
  {
    m_lpMsgPool = this->m_lpMsgPool;
    if ( m_lpMsgPool )
    {
      first = (char *)m_lpMsgPool->first;
      if ( m_lpMsgPool->first )
        m_lpMsgPool->first = *(void **)first;
      else
        first = (char *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpMsgPool);
      if ( first )
      {
        v7 = _snprintf(first, 0x7FFFu, "%s", lpText);
        if ( v7 > 0 )
        {
          first[v7] = 0;
          PostMessageA(this->m_hWnd, CConsoleWindow::ms_PrintInfoMsg, (WPARAM)first, 0);
        }
      }
    }
  }
  LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
}

//----- (00451950) --------------------------------------------------------
void __thiscall CConsoleWindow::~CConsoleWindow(CConsoleWindow *this)
{
  boost::pool<boost::default_user_allocator_new_delete> *m_lpMsgPool; // edi

  this->__vftable = (CConsoleWindow_vtbl *)&CConsoleWindow::`vftable';
  CConsoleWindow::Destroy(this);
  m_lpMsgPool = this->m_lpMsgPool;
  if ( m_lpMsgPool )
  {
    boost::pool<boost::default_user_allocator_new_delete>::purge_memory(this->m_lpMsgPool);
    operator delete(m_lpMsgPool);
  }
  this->m_lpMsgPool = 0;
  DeleteCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
}
// 4E1DBC: using guessed type void *CConsoleWindow::`vftable';

//----- (00451990) --------------------------------------------------------
CConsoleWindow *__thiscall CConsoleWindow::`scalar deleting destructor'(CConsoleWindow *this, char a2)
{
  CConsoleWindow::~CConsoleWindow(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004519B0) --------------------------------------------------------
void __thiscall CConsoleWindow::CreateCommand(CConsoleWindow *this)
{
  signed int WindowTextA; // eax
  int v3; // edi
  CConsoleCommand *v4; // ebx
  char szCommand[512]; // [esp+8h] [ebp-204h] BYREF

  WindowTextA = GetWindowTextA(this->m_hWndInput, szCommand, 512);
  v3 = WindowTextA;
  if ( WindowTextA > 0 )
  {
    v4 = CConsoleCMDFactory::Create(this->m_CMDFactory, szCommand, WindowTextA);
    if ( v4 )
    {
      CConsoleWindow::PrintOutput(this, szCommand, v3);
      CCommandProcess::Add(this->m_CMDProcess, v4);
    }
    else
    {
      CConsoleWindow::PrintOutput(
        this,
        "Can't recognize command. Please reinput.",
        strlen("Can't recognize command. Please reinput."));
    }
  }
  SetWindowTextA(this->m_hWndInput, szLoseCharName);
}

//----- (00451A50) --------------------------------------------------------
LRESULT __stdcall CConsoleWindow::InputWindowProc(
        HWND__ *hWnd,
        unsigned __int16 msg,
        unsigned __int16 wParam,
        LPARAM lParam)
{
  CConsoleWindow *PropA; // eax
  int (__stdcall *m_fOldProc)(HWND__ *, unsigned int, unsigned int, int); // edx

  PropA = (CConsoleWindow *)GetPropA(hWnd, CConsoleWindow::ms_this);
  if ( !PropA )
    return DefWindowProcA(hWnd, msg, wParam, lParam);
  m_fOldProc = (int (__stdcall *)(HWND__ *, unsigned int, unsigned int, int))PropA->m_fOldProc;
  if ( !m_fOldProc )
    return DefWindowProcA(hWnd, msg, wParam, lParam);
  if ( msg != 258 || wParam != 13 )
    return CallWindowProcA(m_fOldProc, hWnd, msg, wParam, lParam);
  CConsoleWindow::CreateCommand(PropA);
  return 0;
}

//----- (00451AD0) --------------------------------------------------------
char __thiscall CConsoleWindow::Initialize(CConsoleWindow *this)
{
  CCSLock *p_m_ConsoleWindowLock; // ebp
  HWND__ *Window; // eax
  HWND__ *v5; // eax
  HWND__ *v6; // eax
  HWND__ *v7; // eax
  HFONT FontA; // eax
  tagWNDCLASSA wc; // [esp+8h] [ebp-28h] BYREF

  p_m_ConsoleWindowLock = &this->m_ConsoleWindowLock;
  EnterCriticalSection(&this->m_ConsoleWindowLock.m_CSLock);
  if ( this->m_hWnd )
  {
    LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
    return 1;
  }
  else
  {
    wc.cbClsExtra = 0;
    wc.cbWndExtra = 0;
    wc.hbrBackground = (HBRUSH__ *)GetStockObject(2);
    wc.hCursor = LoadCursorA(0, (LPCSTR)0x7F00);
    wc.hIcon = LoadIconA(0, (LPCSTR)0x7F00);
    wc.hInstance = this->m_hInstance;
    wc.lpfnWndProc = (int (__stdcall *)(HWND__ *, unsigned int, unsigned int, int))CConsoleWindow::ConsoleWindowProc;
    wc.lpszClassName = WINCONSOLE_NAME;
    wc.lpszMenuName = 0;
    wc.style = 3;
    if ( RegisterClassA(&wc) )
    {
      Window = CreateWindowExA(
                 0x200u,
                 WINCONSOLE_NAME,
                 WINCONSOLE_NAME,
                 0x10000000u,
                 WINCONSOLE_X,
                 20,
                 480,
                 640,
                 this->m_hParentWnd,
                 0,
                 this->m_hInstance,
                 0);
      this->m_hWnd = Window;
      if ( !Window )
        goto LABEL_6;
      if ( !SetPropA(Window, CConsoleWindow::ms_this, this) )
        goto LABEL_6;
      v5 = CreateWindowExA(
             0x200u,
             "EDIT",
             "information ====================================",
             0x50200804u,
             0,
             0,
             470,
             180,
             this->m_hWnd,
             0,
             this->m_hInstance,
             0);
      this->m_hWndInfo = v5;
      if ( !v5 )
        goto LABEL_6;
      v6 = CreateWindowExA(
             0x200u,
             "LISTBOX",
             szLoseCharName,
             0x50204000u,
             0,
             180,
             470,
             416,
             this->m_hWnd,
             0,
             this->m_hInstance,
             0);
      this->m_hWndOutput = v6;
      if ( v6
        && (v7 = CreateWindowExA(
                   0x200u,
                   "EDIT",
                   szLoseCharName,
                   0x50000000u,
                   0,
                   580,
                   466,
                   30,
                   this->m_hWnd,
                   0,
                   this->m_hInstance,
                   0),
            (this->m_hWndInput = v7) != 0)
        && (this->m_fOldProc = SetWindowLongA(v7, -4, (LONG)CConsoleWindow::InputWindowProc),
            SetPropA(this->m_hWndInput, CConsoleWindow::ms_this, this))
        && (FontA = CreateFontA(-11, 0, 0, 0, 400, 0, 0, 0, 1u, 0, 0, 0, 1u, WINCONSOLE_FONTNAME),
            (this->m_hFont = FontA) != 0) )
      {
        PostMessageA(this->m_hWndInfo, 0x30u, (WPARAM)FontA, 0);
        PostMessageA(this->m_hWndOutput, 0x30u, (WPARAM)this->m_hFont, 0);
        PostMessageA(this->m_hWndInput, 0x30u, (WPARAM)this->m_hFont, 0);
        LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
        return 1;
      }
      else
      {
LABEL_6:
        LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
        return 0;
      }
    }
    else
    {
      LeaveCriticalSection(&p_m_ConsoleWindowLock->m_CSLock);
      return 0;
    }
  }
}

//----- (00451D30) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00451D70) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *v5; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead == this->_Myhead || *_Keyval < Myhead->_Myval.first )
  {
    v5 = result;
    result->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)this->_Myhead;
  }
  else
  {
    v5 = result;
    result->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)Myhead;
  }
  return v5;
}

//----- (00451DE0) --------------------------------------------------------
void __thiscall CMsgProcessMgr::Clear(CMsgProcessMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Left; // eax
  CMsgProc *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Parent; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *i; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator pos; // [esp+Ch] [ebp-4h] BYREF

  Myhead = this->m_MessageProcessMap._Myhead;
  Left = Myhead->_Left;
  for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
  {
    second = Left->_Myval.second;
    if ( second )
      ((void (__thiscall *)(CMsgProc *, int))second->~CMsgProc)(second, 1);
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
  }
  Parent = this->m_MessageProcessMap._Myhead->_Parent;
  for ( i = Parent; !i->_Isnil; Parent = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Erase(
      &this->m_MessageProcessMap,
      i->_Right);
    i = i->_Left;
    operator delete(Parent);
  }
  this->m_MessageProcessMap._Myhead->_Parent = this->m_MessageProcessMap._Myhead;
  v7 = this->m_MessageProcessMap._Myhead;
  this->m_MessageProcessMap._Mysize = 0;
  v7->_Left = v7;
  this->m_MessageProcessMap._Myhead->_Right = this->m_MessageProcessMap._Myhead;
}

//----- (00451E60) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00452120) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004521E0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned int const ,CMsgProc *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned int const ,CMsgProc *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00452390) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00452450) --------------------------------------------------------
void __thiscall std::map<unsigned int,CMsgProc *>::~map<unsigned int,CMsgProc *>(
        std::map<unsigned int,CMsgProc *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00452480) --------------------------------------------------------
void __thiscall CMsgProcessMgr::~CMsgProcessMgr(CMsgProcessMgr *this)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator v2; // [esp-8h] [ebp-1Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // [esp-4h] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+4h] [ebp-10h] BYREF
  int v5; // [esp+10h] [ebp-4h]

  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this;
  v5 = 0;
  CMsgProcessMgr::Clear(this);
  Myhead = this->m_MessageProcessMap._Myhead;
  v2._Ptr = Myhead->_Left;
  v5 = -1;
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(
    &this->m_MessageProcessMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator)Myhead);
  operator delete(this->m_MessageProcessMap._Myhead);
  this->m_MessageProcessMap._Myhead = 0;
  this->m_MessageProcessMap._Mysize = 0;
}

//----- (004524F0) --------------------------------------------------------
bool __thiscall CMsgProcessMgr::Register(CMsgProcessMgr *this, unsigned int uMsg, CMsgProc *lpMsgProcessProc)
{
  std::pair<unsigned int const ,CMsgProc *> _Val; // [esp+0h] [ebp-10h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> result; // [esp+8h] [ebp-8h] BYREF

  _Val.first = uMsg;
  _Val.second = lpMsgProcessProc;
  return std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::insert(
           &this->m_MessageProcessMap,
           &result,
           (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)&_Val)->second;
}

//----- (00452520) --------------------------------------------------------
void __thiscall CMsgProcessMgr::CMsgProcessMgr(CMsgProcessMgr *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v2; // eax

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_MessageProcessMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_MessageProcessMap._Myhead->_Parent = this->m_MessageProcessMap._Myhead;
  this->m_MessageProcessMap._Myhead->_Left = this->m_MessageProcessMap._Myhead;
  this->m_MessageProcessMap._Myhead->_Right = this->m_MessageProcessMap._Myhead;
  this->m_MessageProcessMap._Mysize = 0;
}

//----- (00452550) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharExchangeCmd(
        CSendStream *SendStream,
        unsigned int dwSenderID_In,
        unsigned int dwRecverID_In,
        unsigned __int8 cCmd_In,
        unsigned __int16 usError_In)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwRecverID_In;
  *((_DWORD *)Buffer + 3) = dwSenderID_In;
  Buffer[20] = cCmd_In;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x21u, 0, usError_In);
}

//----- (00452590) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPartyCmd(
        CSendStream *SendStream,
        unsigned int dwPartyID_In,
        const AddressInfo *AddressInfo_In,
        const char *szSenderName_In,
        unsigned int dwSenderID_In,
        unsigned int dwReceiverID_In,
        unsigned __int16 usCmd_In,
        unsigned __int16 usError_In)
{
  char *Buffer; // ebx

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x4D);
  if ( !Buffer )
    return 0;
  qmemcpy(Buffer + 12, AddressInfo_In, 0x24u);
  if ( szSenderName_In )
  {
    strncpy(Buffer + 48, szSenderName_In, 0x10u);
  }
  else
  {
    *((_DWORD *)Buffer + 12) = 0;
    *((_DWORD *)Buffer + 13) = 0;
    *((_DWORD *)Buffer + 14) = 0;
    *((_DWORD *)Buffer + 15) = 0;
  }
  *((_DWORD *)Buffer + 17) = dwSenderID_In;
  *((_DWORD *)Buffer + 16) = dwPartyID_In;
  *((_DWORD *)Buffer + 18) = dwReceiverID_In;
  Buffer[76] = usCmd_In;
  return CSendStream::WrapCrypt(SendStream, 0x4Du, 0x1Fu, 0, usError_In);
}

//----- (00452620) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPartyCreateToDBAgent(
        CSendStream *AgentSendStream,
        unsigned int dwLeaderID,
        unsigned int dwMemberID)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x1A);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 18) = dwLeaderID;
  *((_WORD *)Buffer + 8) = 49;
  *(_DWORD *)(Buffer + 22) = dwMemberID;
  return CSendStream::WrapHeader(AgentSendStream, 0x1Au, 0x27u, 0, 0);
}

//----- (00452660) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPartyDestroyToDBAgent(CSendStream *AgentSendStream, unsigned int dwPartyID)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x16);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 18) = dwPartyID;
  *((_WORD *)Buffer + 8) = 50;
  return CSendStream::WrapHeader(AgentSendStream, 0x16u, 0x27u, 0, 0);
}

//----- (004526A0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendPartyMemberData(
        CSendStream *SendStream,
        unsigned int dwPartyID,
        unsigned int dwSenderID,
        unsigned int dwReferenceID,
        const char *strSenderName,
        unsigned __int16 usCmd)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x2E);
  if ( !Buffer )
    return 0;
  *((_WORD *)Buffer + 8) = usCmd;
  *(_DWORD *)(Buffer + 18) = dwPartyID;
  *(_DWORD *)(Buffer + 22) = dwSenderID;
  *(_DWORD *)(Buffer + 26) = dwReferenceID;
  strncpy(Buffer + 30, strSenderName, 0x10u);
  return CSendStream::WrapHeader(SendStream, 0x2Eu, 0x27u, 0, 0);
}

//----- (00452700) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendPartyAddress(
        CSendStream *SendStream,
        unsigned int dwPartyID,
        unsigned int dwSenderID,
        const sockaddr_in *PublicAddress,
        const sockaddr_in *PrivateAddress,
        unsigned __int16 usCmd)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x3A);
  if ( !Buffer )
    return 0;
  *((_WORD *)Buffer + 8) = usCmd;
  *(_DWORD *)(Buffer + 18) = dwPartyID;
  *(_DWORD *)(Buffer + 22) = dwSenderID;
  *(sockaddr_in *)(Buffer + 26) = *PublicAddress;
  *(sockaddr_in *)(Buffer + 42) = *PrivateAddress;
  return CSendStream::WrapHeader(SendStream, 0x3Au, 0x27u, 0, 0);
}

//----- (00452780) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharStallRegisterItem(
        CSendStream *SendStream,
        unsigned int dwCharID,
        TakeType takeType,
        unsigned int dwPrice,
        unsigned __int8 cCmd,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1A);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *(TakeType *)(Buffer + 16) = takeType;
  *(_DWORD *)(Buffer + 21) = dwPrice;
  Buffer[25] = cCmd;
  return CSendStream::WrapCrypt(SendStream, 0x1Au, 0x5Fu, 0, usError);
}

//----- (004527D0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharStallEnter(
        CSendStream *SendStream,
        unsigned int dwCustomerID,
        unsigned int dwOwnerID,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x14);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCustomerID;
  *((_DWORD *)Buffer + 4) = dwOwnerID;
  return CSendStream::WrapCrypt(SendStream, 0x14u, 0x60u, 0, usError);
}

//----- (00452810) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharFriendAck(
        CSendStream *SendStream,
        unsigned __int8 cAckCmd,
        unsigned int dwCID,
        const char *szFriendName,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  _DWORD *v6; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x21);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  Buffer[32] = cAckCmd;
  if ( szFriendName )
  {
    strncpy(Buffer + 16, szFriendName, 0x10u);
  }
  else
  {
    v6 = Buffer + 16;
    *v6 = 0;
    v6[1] = 0;
    v6[2] = 0;
    v6[3] = 0;
  }
  return CSendStream::WrapCrypt(SendStream, 0x21u, 0x67u, 0, usError);
}

//----- (00452870) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharFriendAdded(CSendStream *SendStream, unsigned int dwCID, const char *szName)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x21);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = *(_DWORD *)szName;
  *((_DWORD *)Buffer + 4) = *((_DWORD *)szName + 1);
  *((_DWORD *)Buffer + 5) = *((_DWORD *)szName + 2);
  *((_DWORD *)Buffer + 6) = *((_DWORD *)szName + 3);
  *((_DWORD *)Buffer + 7) = dwCID;
  Buffer[32] = 2;
  return CSendStream::WrapCrypt(SendStream, 0x21u, 0x64u, 0, 0);
}

//----- (004528D0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendFriendListChangeToDB(
        CSendStream *AgentSendStream,
        unsigned int dwUID,
        unsigned int dwCID,
        unsigned int dwReferenceUID,
        unsigned int dwReferenceCID,
        unsigned int dwData,
        unsigned __int8 cChangeType)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(AgentSendStream, (char *)0x27);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 18) = dwUID;
  *(_DWORD *)(Buffer + 22) = dwCID;
  *(_DWORD *)(Buffer + 26) = dwReferenceUID;
  *(_DWORD *)(Buffer + 30) = dwReferenceCID;
  *(_DWORD *)(Buffer + 34) = dwData;
  Buffer[38] = cChangeType;
  return CSendStream::WrapCrypt(AgentSendStream, 0x27u, 0x68u, 0, 0);
}

//----- (00452930) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharCreateGuild(
        CSendStream *SendStream,
        unsigned int dwMasterID,
        unsigned int dwGuildID,
        unsigned __int8 cInclination,
        char *szGuildName,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x20);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwMasterID;
  *((_DWORD *)Buffer + 4) = dwGuildID;
  Buffer[20] = cInclination;
  strncpy(Buffer + 21, szGuildName, 0xBu);
  return CSendStream::WrapCrypt(SendStream, 0x20u, 0x88u, 0, wError);
}

//----- (00452990) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildCmd(
        CSendStream *SendStream,
        unsigned int dwGID,
        unsigned int dwSenderID,
        unsigned int dwReceiverID,
        const char *szGuildName,
        const char *szSenderName,
        unsigned __int16 wCmd,
        unsigned __int16 wError)
{
  char *Buffer; // eax
  char *v9; // esi

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x35);
  v9 = Buffer;
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwGID;
  *((_DWORD *)Buffer + 4) = dwSenderID;
  *((_DWORD *)Buffer + 5) = dwReceiverID;
  strncpy(Buffer + 24, szGuildName, 0xBu);
  strncpy(v9 + 35, szSenderName, 0x10u);
  *(_WORD *)(v9 + 51) = wCmd;
  return CSendStream::WrapCrypt(SendStream, 0x35u, 0x89u, 0, wError);
}

//----- (00452A10) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildMark(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwGID,
        char *szMark,
        unsigned int dwGold,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1C9);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_DWORD *)Buffer + 4) = dwGID;
  qmemcpy(Buffer + 20, szMark, 0x1B1u);
  return CSendStream::WrapCrypt(SendStream, 0x1C9u, 0x8Au, 0, wError);
}

//----- (00452A70) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildLevel(
        CSendStream *SendStream,
        unsigned int dwUID,
        unsigned __int8 cLevel,
        unsigned int dwGold,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwUID;
  Buffer[16] = cLevel;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x8Bu, 0, wError);
}

//----- (00452AB0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildRelation(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwGID,
        unsigned __int8 cRelation,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwGID;
  *((_DWORD *)Buffer + 3) = dwCID;
  Buffer[20] = cRelation;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x8Cu, 0, wError);
}

//----- (00452B00) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildInclination(
        CSendStream *SendStream,
        unsigned int dwUID,
        unsigned __int8 cInclination,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x11);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwUID;
  Buffer[16] = cInclination;
  return CSendStream::WrapCrypt(SendStream, 0x11u, 0xA3u, 0, wError);
}

//----- (00452B40) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildRight(
        CSendStream *SendStream,
        unsigned int dwGID,
        GuildRight guildRight,
        unsigned __int16 wError)
{
  char *Buffer; // eax
  unsigned __int16 v5; // dx

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x42);
  if ( !Buffer )
    return 0;
  v5 = wError;
  *((_DWORD *)Buffer + 3) = dwGID;
  qmemcpy(Buffer + 16, &guildRight, 0x30u);
  *((_WORD *)Buffer + 32) = *(_WORD *)&guildRight.m_aryRight[48];
  return CSendStream::WrapCrypt(SendStream, 0x42u, 0x8Fu, 0, v5);
}

//----- (00452B90) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharMyGuildInfo(
        CSendStream *SendStream,
        unsigned int dwGold,
        GuildRight guildRight,
        unsigned __int8 cTitle,
        unsigned __int16 wError)
{
  char *Buffer; // eax
  unsigned __int8 v6; // dl
  unsigned __int16 v8; // [esp-Ch] [ebp-10h]

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x43);
  if ( !Buffer )
    return 0;
  v6 = cTitle;
  *((_DWORD *)Buffer + 3) = dwGold;
  qmemcpy(Buffer + 16, &guildRight, 0x30u);
  Buffer[66] = v6;
  v8 = wError;
  *((_WORD *)Buffer + 32) = *(_WORD *)&guildRight.m_aryRight[48];
  return CSendStream::WrapCrypt(SendStream, 0x43u, 0x91u, 0, v8);
}

//----- (00452BE0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharGuildSafe(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned int dwGID,
        unsigned int dwSafeGold,
        unsigned int dwCharGold,
        unsigned __int8 cCmd,
        const char *szCharName,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x2D);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_DWORD *)Buffer + 4) = dwGID;
  *((_DWORD *)Buffer + 5) = dwSafeGold;
  *((_DWORD *)Buffer + 6) = dwCharGold;
  Buffer[28] = cCmd;
  strncpy(Buffer + 29, szCharName, 0x10u);
  return CSendStream::WrapCrypt(SendStream, 0x2Du, 0x92u, 0, wError);
}

//----- (00452C50) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharUpdateGuildMemberInfo(
        CSendStream *SendStream,
        unsigned int dwGID,
        unsigned int dwCID,
        Guild::MemberInfo memberInfo,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x20);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwCID;
  *((_DWORD *)Buffer + 3) = dwGID;
  *((_DWORD *)Buffer + 6) = memberInfo.m_MemberDetailInfo.m_dwFame;
  *((_DWORD *)Buffer + 5) = memberInfo.m_MemberListInfo;
  *((_DWORD *)Buffer + 7) = memberInfo.m_MemberDetailInfo.m_dwGold;
  return CSendStream::WrapCrypt(SendStream, 0x20u, 0x93u, 0, wError);
}

//----- (00452CA0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharChatBan(
        CSendStream *SendStream,
        unsigned int dwAdminCID,
        unsigned int dwTargetCID,
        unsigned int dwMinutes)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x18);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwAdminCID;
  *((_DWORD *)Buffer + 5) = dwMinutes;
  *((_DWORD *)Buffer + 4) = dwTargetCID;
  return CSendStream::WrapCrypt(SendStream, 0x18u, 0xC2u, 0, 0);
}

//----- (00452CE0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharBindPosition(
        CSendStream *SendStream,
        unsigned int dwNPCID,
        unsigned __int8 cCmd,
        Position Pos,
        char cZone,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1E);
  if ( !Buffer )
    return 0;
  *(_DWORD *)(Buffer + 25) = dwNPCID;
  Buffer[29] = cCmd;
  *((Position *)Buffer + 1) = Pos;
  Buffer[24] = cZone;
  return CSendStream::WrapCrypt(SendStream, 0x1Eu, 0x1Cu, 0, usError);
}

//----- (00452D40) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharHPRegen(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned __int16 nNowHP,
        unsigned __int16 nNowMP)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x14);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCID;
  *((_WORD *)Buffer + 9) = nNowMP;
  *((_WORD *)Buffer + 8) = nNowHP;
  return CSendStream::WrapCrypt(SendStream, 0x14u, 0x3Du, 0, 0);
}

//----- (00452D80) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharQuickSlotMove(
        CSendStream *SendStream,
        TakeType *takeType,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x13);
  if ( !Buffer )
    return 0;
  *(TakeType *)(Buffer + 12) = *takeType;
  return CSendStream::WrapCrypt(SendStream, 0x13u, 0x41u, 0, usError);
}

//----- (00452DC0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharControlOption(
        CSendStream *SendStream,
        unsigned int dwCharID,
        RejectOption *Reject)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x12);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((RejectOption *)Buffer + 8) = (RejectOption)Reject->Reject;
  return CSendStream::WrapCrypt(SendStream, 0x12u, 0x53u, 0, 0);
}

//----- (00452E00) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharAuthorizePanel(
        CSendStream *SendStream,
        CCharacter *lpCaster,
        unsigned __int8 cCmd)
{
  char *Buffer; // esi

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x21);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = lpCaster->m_dwCID;
  strncpy(Buffer + 16, lpCaster->m_DBData.m_Info.Name, 0x10u);
  Buffer[32] = cCmd;
  return CSendStream::WrapCrypt(SendStream, 0x21u, 0x6Fu, 0, 0);
}

//----- (00452E60) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendConfigInfoDB(CSendStream *SendStream, CCharacter *lpCharacter)
{
  unsigned int m_dwCID; // edx
  char dwHighDateTime_high; // cl
  char szBuffer[78]; // [esp+0h] [ebp-54h] BYREF

  m_dwCID = lpCharacter->m_dwCID;
  *(_DWORD *)&szBuffer[18] = lpCharacter->m_dwUID;
  *(_DWORD *)&szBuffer[22] = m_dwCID;
  *(_DWORD *)&szBuffer[28] = *(_DWORD *)&lpCharacter->m_PeaceMode.m_bPeace;
  dwHighDateTime_high = HIBYTE(lpCharacter->m_PeaceMode.m_FileTime.dwHighDateTime);
  *(_DWORD *)&szBuffer[32] = *(unsigned int *)((char *)&lpCharacter->m_PeaceMode.m_FileTime.dwLowDateTime + 3);
  LOWORD(m_dwCID) = lpCharacter->m_RejectOption.m_wReject;
  szBuffer[36] = dwHighDateTime_high;
  *(_WORD *)&szBuffer[37] = m_dwCID;
  *(_WORD *)&szBuffer[26] = 11;
  return CSendStream::WrapCompress(SendStream, szBuffer, (char *)0x27, 0x71u, 0, 0);
}

//----- (00452EE0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharEliteBonus(CSendStream *SendStream, char cEliteBonus)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0xD);
  if ( !Buffer )
    return 0;
  Buffer[12] = cEliteBonus;
  return CSendStream::WrapCrypt(SendStream, 0xDu, 0x69u, 0, 0);
}

//----- (00452F10) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharFameInfo(
        CSendStream *SendStream,
        CCharacter *lpRequestCharacter,
        const char *szWinCharName,
        const char *szLoseCharName,
        unsigned __int16 usError)
{
  char *Buffer; // esi
  CRankingMgr *v7; // ebp
  unsigned __int8 v8; // al

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x3A);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = lpRequestCharacter->m_dwCID;
  *((_DWORD *)Buffer + 4) = lpRequestCharacter->GetFame(lpRequestCharacter);
  *((_DWORD *)Buffer + 5) = lpRequestCharacter->m_DBData.m_Info.Mileage;
  Buffer[24] = CRankingMgr::GetRank(CSingleton<CRankingMgr>::ms_pSingleton, lpRequestCharacter->m_DBData.m_Info.Name, 0);
  v7 = CSingleton<CRankingMgr>::ms_pSingleton;
  v8 = lpRequestCharacter->GetClass(lpRequestCharacter);
  Buffer[25] = CRankingMgr::GetRank(v7, lpRequestCharacter->m_DBData.m_Info.Name, v8);
  strncpy(Buffer + 26, szWinCharName, 0x10u);
  strncpy(Buffer + 42, szLoseCharName, 0x10u);
  return CSendStream::WrapCrypt(SendStream, 0x3Au, 0x55u, 0, usError);
}

//----- (00452FC0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharDuelCmd(
        CSendStream *SendStream,
        unsigned int dwSenderID,
        unsigned int dwRecverID,
        unsigned __int8 cCmd,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x15);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 4) = dwRecverID;
  *((_DWORD *)Buffer + 3) = dwSenderID;
  Buffer[20] = cCmd;
  return CSendStream::WrapCrypt(SendStream, 0x15u, 0x54u, 0, usError);
}

//----- (00453000) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharAttacked(
        CSendStream *SendStream,
        CAggresiveCreature *pAttackCreature,
        CAggresiveCreature *pDefendCharacter,
        AtType attackType,
        float fDir,
        unsigned __int16 wDamage,
        unsigned __int8 cDefenserJudge,
        unsigned __int16 usError)
{
  char *Buffer; // eax
  unsigned __int16 v10; // bx
  float v11; // ecx
  unsigned __int8 v12; // dl
  unsigned int m_dwCID; // eax
  CCell *m_lpCell; // ecx
  DefenserNode Node; // [esp+4h] [ebp-10h] BYREF

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x2D);
  if ( !Buffer )
    return 0;
  v10 = usError;
  *((_DWORD *)Buffer + 7) = pAttackCreature->m_dwCID;
  *((_DWORD *)Buffer + 8) = pDefendCharacter->m_dwCID;
  *((AtType *)Buffer + 9) = attackType;
  *((_DWORD *)Buffer + 3) = LODWORD(pAttackCreature->m_CurrentPos.m_fPointX);
  *((_DWORD *)Buffer + 4) = LODWORD(pAttackCreature->m_CurrentPos.m_fPointY);
  v11 = fDir;
  *((_DWORD *)Buffer + 5) = LODWORD(pAttackCreature->m_CurrentPos.m_fPointZ);
  *((float *)Buffer + 6) = v11;
  *((_WORD *)Buffer + 20) = pDefendCharacter->m_CreatureStatus.m_nNowHP;
  v12 = cDefenserJudge;
  *((_WORD *)Buffer + 21) = pDefendCharacter->m_CreatureStatus.m_nNowMP;
  Buffer[44] = v12;
  if ( CSendStream::WrapCrypt(SendStream, 0x2Du, 0x31u, 0, v10) == 1 && !v10 )
  {
    m_dwCID = pAttackCreature->m_dwCID;
    if ( (m_dwCID & 0xD0000000) == 0 && pAttackCreature != pDefendCharacter )
      return 1;
    m_lpCell = pDefendCharacter->m_CellPos.m_lpCell;
    if ( m_lpCell )
    {
      Node.m_dwCharID = pDefendCharacter->m_dwCID;
      Node.m_wMaxHP = pDefendCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      Node.m_sCurrHP = pDefendCharacter->m_CreatureStatus.m_nNowHP;
      Node.m_wMaxMP = pDefendCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      Node.m_sCurrMP = pDefendCharacter->m_CreatureStatus.m_nNowMP;
      Node.m_wDamage = wDamage;
      Node.m_cJudge = cDefenserJudge;
      CCell::SendAttackInfo(m_lpCell, m_dwCID, &attackType, 1u, &Node);
      return 1;
    }
  }
  return 0;
}

//----- (00453120) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharEquipDurability(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int8 cIndex,
        unsigned __int8 cValue,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x12);
  if ( !Buffer )
    return 0;
  Buffer[16] = cIndex;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[17] = cValue;
  return CSendStream::WrapCrypt(SendStream, 0x12u, 0x87u, 0, usError);
}

//----- (00453170) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharPeaceMode(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int8 cLeftTime,
        bool bPeace,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x12);
  if ( !Buffer )
    return 0;
  Buffer[16] = cLeftTime;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[17] = bPeace;
  return CSendStream::WrapCrypt(SendStream, 0x12u, 0x70u, 0, usError);
}

//----- (004531B0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSummonCmd(
        CCharacter *lpCharacter,
        CMonster *lpSummonee,
        unsigned __int8 cCmd,
        unsigned int dwTargetID,
        unsigned __int16 usError)
{
  CCell *m_lpCell; // ecx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  PktSummonCmd pktSummonCmd; // [esp+8h] [ebp-18h] BYREF

  pktSummonCmd.m_dwCharID = lpCharacter->m_dwCID;
  pktSummonCmd.m_cCmd = cCmd;
  pktSummonCmd.m_dwTargetID = dwTargetID;
  if ( !PacketWrap::WrapCrypt((char *)&pktSummonCmd, 0x15u, 0x7Cu, 0, usError) )
    return 1;
  if ( cCmd != 2 )
  {
    m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      CSendStream::PutBuffer(&m_lpGameClientDispatch->m_SendStream, (char *)&pktSummonCmd, 0x15u, 0x7Cu);
    return 1;
  }
  m_lpCell = lpSummonee->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktSummonCmd, 0x15u, 0x7Cu);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharSummonCmd",
      aDWorkRylSource_20,
      175,
      aCid0x08x_157,
      lpSummonee->m_dwCID);
    return 0;
  }
}

//----- (00453270) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharBattleGroundRespawn(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int16 wTurn,
        unsigned __int16 wWaitNum,
        unsigned __int16 wLeftTime,
        unsigned __int16 wHumanNum,
        unsigned __int16 wAkhanNum,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1A);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  *((_WORD *)Buffer + 8) = wTurn;
  *((_WORD *)Buffer + 9) = wWaitNum;
  *((_WORD *)Buffer + 10) = wLeftTime;
  *((_WORD *)Buffer + 11) = wHumanNum;
  *((_WORD *)Buffer + 12) = wAkhanNum;
  return CSendStream::WrapCrypt(SendStream, 0x1Au, 0x83u, 0, usError);
}

//----- (004532E0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSummon(unsigned int dwCharID, CAggresiveCreature *lpSummonee)
{
  CCell *m_lpCell; // esi
  unsigned int m_dwCID; // edx
  float m_fPointX; // [esp-14h] [ebp-38h]
  float m_fPointY; // [esp-10h] [ebp-34h]
  float m_fPointZ; // [esp-Ch] [ebp-30h]
  PktSummon pktSummon; // [esp+8h] [ebp-1Ch] BYREF

  m_lpCell = lpSummonee->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    m_dwCID = lpSummonee->m_dwCID;
    pktSummon.m_dwCharID = dwCharID;
    m_fPointZ = lpSummonee->m_CurrentPos.m_fPointZ;
    pktSummon.m_dwTargetID = m_dwCID;
    m_fPointY = lpSummonee->m_CurrentPos.m_fPointY;
    m_fPointX = lpSummonee->m_CurrentPos.m_fPointX;
    pktSummon.m_NetworkPos.m_usXPos = 0;
    pktSummon.m_NetworkPos.m_usYPos = 0;
    pktSummon.m_NetworkPos.m_usZPos = 0;
    pktSummon.m_NetworkPos.m_cDirection = 0;
    pktSummon.m_NetworkPos.m_cVelocity = 0;
    CNetworkPos::Initialize(&pktSummon.m_NetworkPos, m_fPointX, m_fPointY, m_fPointZ, 0.0, 0.0);
    if ( PacketWrap::WrapCrypt((char *)&pktSummon, 0x1Cu, 0x81u, 0, 0) )
      CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktSummon, 0x1Cu, 0x81u);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharSummon",
      aDWorkRylSource_20,
      136,
      aCid0x08x_157,
      lpSummonee->m_dwCID);
    return 0;
  }
}

//----- (004533A0) --------------------------------------------------------
unsigned int __thiscall CPulse::CheckSleep(CPulse *this)
{
  DWORD Time; // eax
  int m_nTicksPerPulse; // ecx
  int v4; // eax
  DWORD v5; // edx
  unsigned int v6; // eax
  unsigned int result; // eax
  unsigned int v8; // edx

  Time = timeGetTime();
  m_nTicksPerPulse = this->m_nTicksPerPulse;
  v4 = Time - this->m_dwLastTick;
  if ( v4 >= m_nTicksPerPulse )
  {
    v8 = this->m_dwHeavyTrafficCount + 1;
    this->m_dwHeavyTrafficCount = v8;
    if ( v8 > 5 || v4 > 2 * m_nTicksPerPulse )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CPulse::CheckSleep",
        aDWorkRylSource_55,
        57,
        "Time Overflow : Elapsed time is %u, (TPP is %d), HeavyTrafficCount is %u",
        v4,
        m_nTicksPerPulse,
        v8);
      this->m_bTPPOverTwoTime = 1;
      this->m_dwLastTick = timeGetTime();
      result = this->m_dwPulse + 1;
      this->m_dwPulse = result;
    }
    else
    {
      CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CPulse::CheckSleep", aDWorkRylSource_55, 63, "Traffic is %u", v8);
      this->m_bTPPOverTwoTime = 0;
      this->m_dwLastTick = timeGetTime();
      result = this->m_dwPulse + 1;
      this->m_dwPulse = result;
    }
  }
  else
  {
    v5 = m_nTicksPerPulse - v4 - 1;
    v6 = this->m_nTicksPerPulse;
    if ( m_nTicksPerPulse < 0 )
      v6 = 0;
    if ( v6 < v5 )
      v5 = 0;
    Sleep(v5);
    this->m_dwHeavyTrafficCount = 0;
    this->m_bTPPOverTwoTime = 0;
    this->m_dwLastTick = timeGetTime();
    result = this->m_dwPulse + 1;
    this->m_dwPulse = result;
  }
  return result;
}

//----- (00453470) --------------------------------------------------------
CPulse *__cdecl CPulse::GetInstance()
{
  if ( (_S1_4 & 1) == 0 )
  {
    _S1_4 |= 1u;
    Pulse.m_dwPulse = 0;
    Pulse.m_dwLastTick = timeGetTime();
    Pulse.m_dwHeavyTrafficCount = 0;
    Pulse.m_nTicksPerPulse = 100;
    Pulse.m_bTPPOverTwoTime = 0;
    atexit(_E2_7);
  }
  return &Pulse;
}

//----- (004534D0) --------------------------------------------------------
void __thiscall CSummonMonster::SetGuard(CSummonMonster *this, bool bGuard)
{
  this->m_bGuard = bGuard;
}

//----- (004534E0) --------------------------------------------------------
unsigned __int8 __thiscall CSummonMonster::GetNation(CSummonMonster *this)
{
  if ( this->m_lpMaster )
    return this->m_lpMaster->GetNation(this->m_lpMaster);
  else
    return 0;
}

//----- (00453500) --------------------------------------------------------
void __thiscall CSummonMonster::CSummonMonster(
        CSummonMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreateInfo,
        CCharacter *lpMaster)
{
  CMonster::CMonster(this, MonsterCreateInfo, 1);
  this->__vftable = (CSummonMonster_vtbl *)&CSummonMonster::`vftable';
  this->m_lpMaster = lpMaster;
  this->m_bGuard = 0;
  if ( lpMaster )
  {
    this->m_CreatureStatus.m_nLevel = lpMaster->m_CreatureStatus.m_nLevel;
    this->m_lCurrentFrame = 120;
  }
}
// 4E20E0: using guessed type void *CSummonMonster::`vftable';

//----- (00453550) --------------------------------------------------------
void __thiscall CSummonMonster::~CSummonMonster(CSummonMonster *this)
{
  this->__vftable = (CSummonMonster_vtbl *)&CSummonMonster::`vftable';
  CMonster::~CMonster(this);
}
// 4E20E0: using guessed type void *CSummonMonster::`vftable';

//----- (00453560) --------------------------------------------------------
void __thiscall CSummonMonster::GuardMe(CSummonMonster *this, CAggresiveCreature *lpTarget, int wThreat)
{
  if ( this->m_bGuard )
    this->AttackCmd(this, lpTarget, wThreat);
}

//----- (00453580) --------------------------------------------------------
char __thiscall CSummonMonster::Attack(
        CSummonMonster *this,
        AtType attackType,
        unsigned __int8 cDefenderNum,
        CAggresiveCreature **ppDefenders,
        unsigned __int8 *cDefenderJudges)
{
  CSummonMonster *v5; // ebx
  CCharacter *m_lpMaster; // eax
  CAggresiveCreature **v8; // edx
  _DWORD *v9; // ebp
  int v10; // edi
  CAggresiveCreature *v11; // ecx
  unsigned int m_dwCID; // eax
  unsigned __int8 *v13; // esi
  AtType v14; // eax
  int v15; // eax
  CAggresiveCreature *v16; // ecx
  int v17; // ebx
  unsigned __int8 v18; // dl
  CSendStream *v19; // ebp
  bool v20; // zf
  unsigned __int16 m_nNowHP; // cx
  unsigned __int16 m_nNowMP; // dx
  CCharacter *v23; // edx
  CSendStream *m_lpGameClientDispatch; // ecx
  CCell *m_lpCell; // ecx
  unsigned __int8 cOffencerJudge; // [esp+21h] [ebp-CDh] BYREF
  CAggresiveCreature **v27; // [esp+22h] [ebp-CCh]
  int cDefender; // [esp+26h] [ebp-C8h]
  CAggresiveCreature *pAttackCreature; // [esp+2Ah] [ebp-C4h]
  int wError; // [esp+2Eh] [ebp-C0h] BYREF
  CMonster *lpSummonee; // [esp+32h] [ebp-BCh]
  int v32; // [esp+36h] [ebp-B8h]
  char szBuffer[176]; // [esp+3Ah] [ebp-B4h] BYREF

  v5 = this;
  m_lpMaster = this->m_lpMaster;
  pAttackCreature = this;
  if ( !m_lpMaster )
    return CMonster::Attack(this, (int)this, attackType, cDefenderNum, ppDefenders, cDefenderJudges);
  LOBYTE(m_lpMaster) = 0;
  cOffencerJudge = 0;
  wError = 0;
  LOBYTE(cDefender) = 0;
  if ( !cDefenderNum )
    return 1;
  v8 = ppDefenders;
  v27 = ppDefenders;
  v32 = cDefenderNum;
  do
  {
    v9 = 0;
    if ( *v27 && (*v27)->m_CreatureStatus.m_nNowHP )
    {
      v10 = (unsigned __int8)m_lpMaster;
      v11 = v8[(unsigned __int8)m_lpMaster];
      m_dwCID = v11->m_dwCID;
      lpSummonee = 0;
      if ( (m_dwCID & 0xD0000000) != 0 )
      {
        if ( (m_dwCID & 0xA0000000) == 0xA0000000 )
          v9 = &v11[1].m_SpellMgr.m_AffectedInfo.m_pChant[8]->__vftable;
      }
      else
      {
        v9 = &v11->__vftable;
        lpSummonee = (CMonster *)v11[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[4];
      }
      v13 = &cDefenderJudges[v10];
      v14 = attackType;
      *v13 = 0;
      v15 = ((int (__thiscall *)(CAggresiveCreature *, AtType, CSummonMonster *, unsigned __int8 *, unsigned __int8 *, int *))v8[v10]->ApplyDamage)(
              v8[v10],
              v14,
              v5,
              &cOffencerJudge,
              v13,
              &wError);
      v16 = *v27;
      v17 = v15;
      m_lpMaster = (CCharacter *)&szBuffer[15 * v10 + 26];
      m_lpMaster->__vftable = (CCharacter_vtbl *)(*v27)->m_dwCID;
      HIWORD(m_lpMaster->m_CurrentPos.m_fPointX) = v16->m_CreatureStatus.m_nNowHP;
      HIWORD(m_lpMaster->m_CurrentPos.m_fPointY) = v16->m_CreatureStatus.m_nNowMP;
      LOWORD(m_lpMaster->m_CurrentPos.m_fPointX) = v16->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      v18 = *v13;
      LOWORD(m_lpMaster->m_CurrentPos.m_fPointY) = v16->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      LOWORD(m_lpMaster->m_CurrentPos.m_fPointZ) = v17;
      BYTE2(m_lpMaster->m_CurrentPos.m_fPointZ) = v18;
      if ( v9 )
      {
        if ( lpSummonee )
          lpSummonee->GuardMe(lpSummonee, pAttackCreature, v17);
        m_lpMaster = (CCharacter *)(*(int (__thiscall **)(_DWORD *, int))(*v9 + 40))(v9, (*v13 == 3) + 5);
        v19 = (CSendStream *)v9[376];
        if ( v19 )
          GameClientSendPacket::SendCharAttacked(
            v19 + 8,
            pAttackCreature,
            ppDefenders[v10],
            attackType,
            pAttackCreature->m_MotionInfo.m_fDirection,
            v17,
            *v13,
            0);
      }
      v8 = ppDefenders;
      v5 = (CSummonMonster *)pAttackCreature;
      LOBYTE(m_lpMaster) = cDefender + 1;
      LOBYTE(cDefender) = cDefender + 1;
    }
    v20 = v32 == 1;
    ++v27;
    --v32;
  }
  while ( !v20 );
  if ( !(_BYTE)m_lpMaster )
    return 1;
  *(_DWORD *)&szBuffer[12] = v5->m_dwCID;
  m_nNowHP = v5->m_CreatureStatus.m_nNowHP;
  *(AtType *)&szBuffer[16] = attackType;
  m_nNowMP = v5->m_CreatureStatus.m_nNowMP;
  *(_WORD *)&szBuffer[20] = m_nNowHP;
  *(_WORD *)&szBuffer[22] = m_nNowMP;
  v23 = v5->m_lpMaster;
  szBuffer[24] = cOffencerJudge;
  szBuffer[25] = (char)m_lpMaster;
  m_lpGameClientDispatch = (CSendStream *)v23->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    LOWORD(m_lpMaster) = (unsigned __int8)m_lpMaster;
    if ( CSendStream::WrapCompress(
           m_lpGameClientDispatch + 8,
           szBuffer,
           (char *)(15 * (_DWORD)m_lpMaster + 26),
           0xEu,
           0,
           wError) == 1
      && !(_WORD)wError )
    {
      m_lpCell = v5->m_CellPos.m_lpCell;
      if ( m_lpCell )
      {
        CCell::SendAttackInfo(m_lpCell, v5->m_dwCID, &attackType, cDefender, (DefenserNode *)&szBuffer[26]);
        return 1;
      }
    }
  }
  return 0;
}
// 4537DA: variable 'm_lpMaster' is possibly undefined

//----- (00453850) --------------------------------------------------------
CCreature::MutualType __thiscall CSummonMonster::IsEnemy(CSummonMonster *this, CCreature *lpTarget)
{
  if ( lpTarget->GetStatusFlag(lpTarget, 0x20000000u) )
    return 1;
  if ( this->m_lpMaster )
    return this->m_lpMaster->IsEnemy(this->m_lpMaster, lpTarget);
  return CMonster::IsEnemy(this, lpTarget);
}

//----- (004538A0) --------------------------------------------------------
char __thiscall CSummonMonster::GetMotion(CSummonMonster *this, unsigned int MotionID, MotionInfo *Motion)
{
  char result; // al

  if ( !this->m_lpMaster )
    return CMonster::GetMotion(this, MotionID, Motion);
  result = CMonster::GetMotion(this, MotionID, Motion);
  if ( result )
  {
    if ( MotionID == 8 || MotionID == 1 )
      Motion->m_fVelocity = ((double)(this->m_lpMaster->m_CharacterStatus.m_nDEX - 20) * 0.029999999 + 1.0)
                          * 4.9000001
                          * (double)Motion->m_dwFrame
                          * 0.033333335;
    return 1;
  }
  return result;
}

//----- (00453940) --------------------------------------------------------
void __thiscall CStatue::CStatue(CStatue *this, CMonster::MonsterCreateInfo *MonsterCreateInfo, CStatue *lpParent)
{
  CMsgProcessMgr *Instance; // eax
  unsigned int v5; // [esp-4h] [ebp-1Ch]

  CMonster::CMonster(this, MonsterCreateInfo, 0);
  this->m_lpParent = lpParent;
  this->__vftable = (CStatue_vtbl *)&CStatue::`vftable';
  this->m_dwDuration = 0;
  this->m_wBonusTurn = 0;
  this->m_nMovingPattern = 1;
  v5 = (MonsterCreateInfo->m_dwCID + 0x80000000) | 0x50000000;
  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  this->m_lpLinkNPC = (CNPC *)Castle::CCastleMgr::GetCastle(Instance, v5);
}
// 4E2180: using guessed type void *CStatue::`vftable';

//----- (004539E0) --------------------------------------------------------
void __thiscall CStatue::~CStatue(CStatue *this)
{
  this->__vftable = (CStatue_vtbl *)&CStatue::`vftable';
  CMonster::~CMonster(this);
}
// 4E2180: using guessed type void *CStatue::`vftable';

//----- (004539F0) --------------------------------------------------------
int __thiscall CStatue::IsEnemy(CStatue *this, CCreature *lpTarget)
{
  unsigned __int8 v3; // bl

  v3 = lpTarget->GetNation(lpTarget);
  return this->GetNation(this) != v3 ? 2 : 0;
}

//----- (00453A20) --------------------------------------------------------
char __thiscall CStatue::CreateLinkStatue(CStatue *this, unsigned __int16 wKind)
{
  unsigned __int16 v2; // ax
  int v4; // edi
  unsigned __int16 m_wMapIndex; // cx
  unsigned int v6; // eax
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // eax
  CCreatureManager *v10; // eax
  float m_fPointX; // ecx
  float m_fPointY; // edx
  float m_fPointZ; // eax
  CStatue *v14; // eax
  CMonster *v15; // eax
  CMonster *v16; // edi
  unsigned __int16 v18; // si
  VirtualArea::CVirtualAreaMgr *v19; // eax
  VirtualArea::CBGServerMap *v20; // eax
  CVirtualMonsterMgr *v21; // eax
  CCreatureManager *v22; // eax
  unsigned __int16 v23; // [esp-4h] [ebp-3Ch]
  signed int v24; // [esp-4h] [ebp-3Ch]
  CMonster::MonsterCreateInfo tempInfo; // [esp+Ch] [ebp-2Ch] BYREF
  int v26; // [esp+34h] [ebp-4h]

  v2 = wKind;
  if ( this->m_MonsterInfo.m_dwKID == wKind )
    v2 = this->m_CellPos.m_wMapIndex != 0 ? 1039 : 1034;
  v4 = v2;
  m_wMapIndex = this->m_CellPos.m_wMapIndex;
  v6 = v2 | this->m_dwCID & 0xFFFF0000;
  memset(&tempInfo, 0, 12);
  memset(&tempInfo.m_dwPID, 0, 13);
  tempInfo.m_wRespawnArea = 0;
  tempInfo.m_dwCID = v6;
  if ( m_wMapIndex )
  {
    v23 = m_wMapIndex;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, v23);
    if ( VirtualArea )
    {
      m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
      if ( m_pVirtualMonsterMgr )
      {
        if ( CVirtualMonsterMgr::GetAggresiveCreature(m_pVirtualMonsterMgr, tempInfo.m_dwCID) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CStatue::CreateLinkStatue",
            aDWorkRylSource_51,
            509,
            (char *)&byte_4E22B4);
          return 0;
        }
      }
    }
  }
  else
  {
    v24 = v6;
    v10 = CCreatureManager::GetInstance();
    if ( CCreatureManager::GetCreature(v10, v24) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CStatue::CreateLinkStatue",
        aDWorkRylSource_51,
        518,
        (char *)&byte_4E22B4);
      return 0;
    }
  }
  m_fPointX = this->m_OriginalPosition.m_fPointX;
  m_fPointY = this->m_OriginalPosition.m_fPointY;
  m_fPointZ = this->m_OriginalPosition.m_fPointZ;
  tempInfo.m_nKID = v4;
  tempInfo.m_Pos.m_fPointX = m_fPointX;
  tempInfo.m_Pos.m_fPointY = m_fPointY;
  tempInfo.m_Pos.m_fPointZ = m_fPointZ;
  v14 = (CStatue *)operator new((tagHeader *)0x310);
  v26 = 0;
  if ( v14 )
  {
    CStatue::CStatue(v14, &tempInfo, this);
    v16 = v15;
  }
  else
  {
    v16 = 0;
  }
  v26 = -1;
  if ( !v16 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CStatue::CreateLinkStatue", aDWorkRylSource_51, 529, (char *)&byte_4E22B4);
    return 0;
  }
  v16->m_CellPos.m_wMapIndex = this->m_CellPos.m_wMapIndex;
  if ( !CMonster::InitMonster(v16, &tempInfo.m_Pos, DEAD) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CStatue::CreateLinkStatue", aDWorkRylSource_51, 536, (char *)&byte_4E22B4);
    return 0;
  }
  v18 = this->m_CellPos.m_wMapIndex;
  if ( v18 )
  {
    v19 = VirtualArea::CVirtualAreaMgr::GetInstance();
    v20 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v19, v18);
    if ( v20 )
    {
      v21 = v20->m_pVirtualMonsterMgr;
      if ( v21 )
        CVirtualMonsterMgr::AddMonster(v21, v16);
    }
  }
  else
  {
    v22 = CCreatureManager::GetInstance();
    CCreatureManager::AddCreature(v22, (CNPC *)v16);
  }
  return 1;
}
// 453B4B: variable 'v15' is possibly undefined

//----- (00453C20) --------------------------------------------------------
CStatue *__thiscall CStatue::GetLinkStatue(CStatue *this, unsigned __int16 wKind)
{
  CStatue *m_lpParent; // esi
  unsigned __int16 m_wMapIndex; // cx
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CMsgProcessMgr *m_pVirtualMonsterMgr; // ecx
  CCreatureManager *v8; // eax
  unsigned __int16 v9; // [esp-4h] [ebp-8h]
  unsigned int v10; // [esp-4h] [ebp-8h]

  m_lpParent = this;
  if ( this->m_lpParent )
    m_lpParent = this->m_lpParent;
  m_wMapIndex = this->m_CellPos.m_wMapIndex;
  if ( m_wMapIndex )
  {
    v9 = m_wMapIndex;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, v9);
    if ( VirtualArea && (m_pVirtualMonsterMgr = (CMsgProcessMgr *)VirtualArea->m_pVirtualMonsterMgr) != 0 )
      return (CStatue *)Castle::CCastleMgr::GetCastle(m_pVirtualMonsterMgr, wKind | m_lpParent->m_dwCID & 0xFFFF0000);
    else
      return 0;
  }
  else
  {
    v10 = wKind | m_lpParent->m_dwCID & 0xFFFF0000;
    v8 = CCreatureManager::GetInstance();
    return (CStatue *)CCreatureManager::GetMonster(v8, v10);
  }
}

//----- (00453CA0) --------------------------------------------------------
Item::CItem *__thiscall CStatue::SellToCharacter(
        CStatue *this,
        CCharacter *lpCustomer,
        int wKindItem,
        TakeType takeType,
        unsigned int *dwPrice)
{
  CNPC *m_lpLinkNPC; // eax
  unsigned __int8 m_cNum; // [esp-8h] [ebp-10h]

  m_lpLinkNPC = this->m_lpLinkNPC;
  if ( !m_lpLinkNPC )
    return 0;
  m_cNum = takeType.m_cNum;
  return (Item::CItem *)((int (__thiscall *)(CNPC *, CCharacter *, int, _DWORD, unsigned __int8, unsigned int *))m_lpLinkNPC->SellToCharacter)(
                          m_lpLinkNPC,
                          lpCustomer,
                          wKindItem,
                          *(_DWORD *)&takeType.m_srcPos,
                          m_cNum,
                          dwPrice);
}

//----- (00453CE0) --------------------------------------------------------
void __thiscall CStatueInfo::CStatueInfo(CStatueInfo *this, CStatue *lpStatue, bool bBonusTurn)
{
  this->m_lpStatue = lpStatue;
  this->m_bBonusTurn = bBonusTurn;
}

//----- (00453D00) --------------------------------------------------------
bool __thiscall CAggresiveCreature::GetConsumeMPCount(CSiegeObject *this)
{
  return 0;
}

//----- (00453D10) --------------------------------------------------------
char __thiscall CSkillMonster::Dead(CSkillMonster *this, CCharacter *pOffencer)
{
  char *Name; // eax
  CMonsterShout *Instance; // eax
  unsigned int m_dwCID; // [esp-1Ch] [ebp-24h]
  CPacketDispatch *m_dwKID; // [esp-18h] [ebp-20h]
  int m_fPointX; // [esp-14h] [ebp-1Ch]
  unsigned __int16 m_fPointZ; // [esp-10h] [ebp-18h]
  const char *v10; // [esp-8h] [ebp-10h]

  Name = 0;
  this->m_bCasting = 0;
  if ( pOffencer && (pOffencer->m_dwCID & 0xD0000000) == 0 )
    Name = pOffencer->m_DBData.m_Info.Name;
  v10 = Name;
  m_fPointZ = (unsigned __int64)this->m_CurrentPos.m_fPointZ;
  m_fPointX = (unsigned __int64)this->m_CurrentPos.m_fPointX;
  m_dwKID = (CPacketDispatch *)this->m_MonsterInfo.m_dwKID;
  m_dwCID = this->m_dwCID;
  Instance = CMonsterShout::GetInstance();
  CMonsterShout::Shout(Instance, m_dwCID, m_dwKID, m_fPointX, m_fPointZ, DEAD|0x4, v10, 0);
  return CMonster::Dead(this, pOffencer);
}

//----- (00453D70) --------------------------------------------------------
void __thiscall CMageMonster::~CMageMonster(CNamedMonster *this)
{
  this->__vftable = (CNamedMonster_vtbl *)&CSkillMonster::`vftable';
  CMonster::~CMonster(this);
}
// 4E22D0: using guessed type void *CSkillMonster::`vftable';

//----- (00453D80) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Left; // edx
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *i; // eax
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Parent; // eax

  Ptr = this->_Ptr;
  if ( this->_Ptr->_Isnil )
  {
    this->_Ptr = Ptr->_Right;
  }
  else
  {
    Left = Ptr->_Left;
    if ( Ptr->_Left->_Isnil )
    {
      Parent = Ptr->_Parent;
      if ( !Parent->_Isnil )
      {
        do
        {
          if ( this->_Ptr != Parent->_Left )
            break;
          this->_Ptr = Parent;
          Parent = Parent->_Parent;
        }
        while ( !Parent->_Isnil );
        if ( !Parent->_Isnil )
          this->_Ptr = Parent;
      }
    }
    else
    {
      for ( i = Left->_Right; !i->_Isnil; i = i->_Right )
        Left = i;
      this->_Ptr = Left;
    }
  }
}

//----- (00453DE0) --------------------------------------------------------
CSummonMonster *__thiscall CSummonMonster::`scalar deleting destructor'(CSummonMonster *this, char a2)
{
  CSummonMonster::~CSummonMonster(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00453E00) --------------------------------------------------------
void __thiscall CSummonMonster::AttackCmd(CSummonMonster *this, CAggresiveCreature *lpTarget, unsigned __int16 wThreat)
{
  this->m_bLongRangeAttacked = 1;
  CThreat::AddToThreatList(&this->m_Threat, lpTarget, wThreat);
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, this->m_nCurrentState, 106);
}

//----- (00453E40) --------------------------------------------------------
void __thiscall CSummonMonster::Attacked(CSummonMonster *this)
{
  int m_nCurrentState; // eax

  if ( this->m_bGuard )
  {
    m_nCurrentState = this->m_nCurrentState;
    this->m_bLongRangeAttacked = 1;
    if ( m_nCurrentState == 1 || m_nCurrentState == 3 )
      this->m_lCurrentFrame = 0;
    this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 105);
  }
}

//----- (00453E90) --------------------------------------------------------
char __thiscall CSummonMonster::Dead(CSummonMonster *this, CAggresiveCreature *pOffencer)
{
  CCharacter *m_lpMaster; // eax
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // edx
  int v6; // eax
  CCell *m_lpCell; // ecx
  CCastingSpell *p_m_CastingInfo; // esi

  m_lpMaster = this->m_lpMaster;
  if ( m_lpMaster )
  {
    m_lpMaster->m_lpSummonee = 0;
    GameClientSendPacket::SendCharSummonCmd(this->m_lpMaster, this, 2u, this->m_dwCID, 0);
    this->m_lpMaster = 0;
  }
  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  v6 = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  m_lpCell = this->m_CellPos.m_lpCell;
  this->m_nCurrentState = v6;
  if ( m_lpCell )
  {
    CCell::DeleteCreature(
      m_lpCell,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    this->m_CellPos.m_lpCell = 0;
  }
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&this->m_SpellMgr.m_AffectedInfo);
  p_m_CastingInfo = &this->m_SpellMgr.m_CastingInfo;
  CCastingSpell::ClearEnchant(p_m_CastingInfo);
  CCastingSpell::DisableChant(p_m_CastingInfo, 0);
  return 1;
}

//----- (00453F50) --------------------------------------------------------
CStatue *__thiscall CStatue::`scalar deleting destructor'(CStatue *this, char a2)
{
  CStatue::~CStatue(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00453F70) --------------------------------------------------------
char __thiscall CStatue::Dead(CStatue *this, CAggresiveCreature *pOffencer)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // ecx
  unsigned int m_dwCID; // edx
  unsigned __int16 m_nMaxMP; // cx
  unsigned __int16 m_nMaxHP; // ax
  CCell *m_lpCell; // ecx
  unsigned __int16 m_nNowMP; // ax
  CCell *v11; // ecx
  CStatue *m_lpParent; // eax
  unsigned int v13; // eax
  CSpell *v14; // eax
  int v15; // eax
  CStatue *LinkStatue; // eax
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  VirtualArea::CBGServerMap *v19; // edi
  int v20; // edx
  unsigned int v21; // eax
  char *MapTypeName; // eax
  unsigned __int16 v23; // bx
  char *v24; // eax
  CCastingSpell *p_m_CastingInfo; // esi
  unsigned int v26; // [esp-14h] [ebp-CCh]
  int v27; // [esp-8h] [ebp-C0h]
  int v28; // [esp-8h] [ebp-C0h]
  unsigned __int16 m_dwKID; // [esp-4h] [ebp-BCh]
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-BCh]
  int v31; // [esp-4h] [ebp-BCh]
  int v32; // [esp-4h] [ebp-BCh]
  AtType attackType; // [esp+Ch] [ebp-ACh] BYREF
  DefenserNode Defenser[10]; // [esp+10h] [ebp-A8h] BYREF
  char szOffencerNation[8]; // [esp+ACh] [ebp-Ch] BYREF

  if ( this->m_nCurrentState == 5 )
    return 0;
  *(_WORD *)szOffencerNation = this->m_CreatureStatus.m_nNowHP;
  this->m_CreatureStatus.m_nNowHP = 0;
  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  this->m_nCurrentState = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  if ( !pOffencer )
  {
    m_dwCID = this->m_dwCID;
    memset(&Defenser[0].m_wMaxHP, 0, 0x90u);
    m_nMaxMP = this->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
    *(unsigned __int16 *)((char *)&Defenser[9].m_wDamage + 1) = 0;
    m_nMaxHP = this->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
    Defenser[0].m_wMaxMP = m_nMaxMP;
    Defenser[0].m_wDamage = *(_WORD *)szOffencerNation;
    m_lpCell = this->m_CellPos.m_lpCell;
    Defenser[0].m_dwCharID = m_dwCID;
    LOWORD(m_dwCID) = this->m_CreatureStatus.m_nNowHP;
    Defenser[0].m_wMaxHP = m_nMaxHP;
    m_nNowMP = this->m_CreatureStatus.m_nNowMP;
    attackType.m_wType = 0;
    Defenser[0].m_cJudge = 0;
    Defenser[0].m_sCurrHP = m_dwCID;
    Defenser[0].m_sCurrMP = m_nNowMP;
    if ( m_lpCell )
      CCell::SendAttackInfo(m_lpCell, 0, &attackType, 1u, Defenser);
  }
  v11 = this->m_CellPos.m_lpCell;
  if ( v11 )
  {
    CCell::DeleteCreature(
      v11,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    this->m_CellPos.m_lpCell = 0;
  }
  if ( !pOffencer )
  {
    switch ( this->m_MonsterInfo.m_dwKID )
    {
      case 0x40Bu:
        m_dwKID = 1036;
        break;
      case 0x40Du:
        m_dwKID = 1038;
        break;
      case 0x410u:
        m_dwKID = 1041;
        break;
      case 0x412u:
        m_dwKID = 1043;
        break;
      default:
        m_lpParent = this->m_lpParent;
        if ( !m_lpParent )
          m_lpParent = this;
        m_dwKID = m_lpParent->m_MonsterInfo.m_dwKID;
        break;
    }
    goto LABEL_38;
  }
  v13 = pOffencer->m_dwCID;
  if ( (v13 & 0xD0000000) == 0 )
  {
    if ( HIBYTE(pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[5]) )
    {
      if ( HIBYTE(pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[5]) != 1 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CStatue::Dead",
          aDWorkRylSource_51,
          366,
          aCid0x08x_104,
          this->m_dwCID,
          HIBYTE(pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[5]));
        goto LABEL_26;
      }
LABEL_29:
      if ( pOffencer->m_CellPos.m_wMapIndex )
        LinkStatue = CStatue::GetLinkStatue(this, 0x412u);
      else
        LinkStatue = CStatue::GetLinkStatue(this, 0x40Du);
      goto LABEL_39;
    }
    goto LABEL_32;
  }
  if ( (v13 & 0xA0000000) != 0xA0000000 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CStatue::Dead",
      aDWorkRylSource_51,
      401,
      aCid0x08x_245,
      this->m_dwCID,
      pOffencer->m_dwCID);
    if ( pOffencer->m_CellPos.m_wMapIndex )
      m_dwKID = 1039;
    else
      m_dwKID = 1034;
LABEL_38:
    LinkStatue = CStatue::GetLinkStatue(this, m_dwKID);
    goto LABEL_39;
  }
  v14 = pOffencer[1].m_SpellMgr.m_AffectedInfo.m_pChant[8];
  if ( v14 )
  {
    v15 = HIBYTE(v14[15].m_pAffected[2]);
    if ( v15 )
    {
      if ( v15 != 1 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CStatue::Dead",
          aDWorkRylSource_51,
          392,
          aCid0x08x_104,
          this->m_dwCID,
          v15);
LABEL_26:
        if ( pOffencer->m_CellPos.m_wMapIndex )
          LinkStatue = CStatue::GetLinkStatue(this, 0x40Fu);
        else
          LinkStatue = CStatue::GetLinkStatue(this, 0x40Au);
        goto LABEL_39;
      }
      goto LABEL_29;
    }
LABEL_32:
    if ( pOffencer->m_CellPos.m_wMapIndex )
      LinkStatue = CStatue::GetLinkStatue(this, 0x410u);
    else
      LinkStatue = CStatue::GetLinkStatue(this, 0x40Bu);
LABEL_39:
    if ( LinkStatue )
      LinkStatue->Respawn(LinkStatue, this->m_dwLastTime);
  }
  if ( this->m_CellPos.m_wMapIndex )
  {
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
    v19 = VirtualArea;
    if ( VirtualArea )
    {
      if ( HIWORD(VirtualArea->m_dwVID) == 0x8000 && VirtualArea->m_MapInfo.m_cMapType == 1 )
      {
        VirtualArea::CBGServerMap::CalculateScore(VirtualArea);
        VirtualArea::CBGServerMap::SendMapInfo(v19);
        if ( pOffencer )
        {
          if ( HIBYTE(pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[5]) )
            *(_DWORD *)szOffencerNation = *(_DWORD *)aAkha;
          else
            *(_DWORD *)szOffencerNation = *(_DWORD *)aHuma;
          strcpy(&szOffencerNation[4], "N");
          v20 = v19->m_MapInfo.m_wScore[1];
          v21 = pOffencer->m_dwCID;
          attackType.m_wType = v19->m_wMapIndex;
          v31 = v20;
          v27 = v19->m_MapInfo.m_wScore[0];
          v26 = v21;
          MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(v19);
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CStatue::Dead",
            aDWorkRylSource_51,
            435,
            aBattleServerLo_1,
            attackType.m_wType & 0x7FFF,
            MapTypeName,
            v26,
            &pOffencer[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1],
            szOffencerNation,
            v27,
            v31);
        }
        else
        {
          v23 = v19->m_wMapIndex;
          v32 = v19->m_MapInfo.m_wScore[1];
          v28 = v19->m_MapInfo.m_wScore[0];
          v24 = VirtualArea::CVirtualArea::GetMapTypeName(v19);
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CStatue::Dead",
            aDWorkRylSource_51,
            441,
            aBattleServerLo_2,
            v23 & 0x7FFF,
            v24,
            v28,
            v32);
        }
      }
    }
  }
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&this->m_SpellMgr.m_AffectedInfo);
  p_m_CastingInfo = &this->m_SpellMgr.m_CastingInfo;
  CCastingSpell::ClearEnchant(p_m_CastingInfo);
  CCastingSpell::DisableChant(p_m_CastingInfo, 0);
  return 1;
}

//----- (004543D0) --------------------------------------------------------
char __thiscall CStatue::Rest(CStatue *this)
{
  unsigned int m_dwLastTick; // eax
  int m_nCurrentState; // eax
  int v5; // eax
  CCell *m_lpCell; // ecx
  CCastingSpell *p_m_CastingInfo; // esi

  if ( this->m_nCurrentState == 5 )
    return 0;
  this->m_wBonusTurn = 0;
  this->m_CreatureStatus.m_nNowHP = 0;
  m_dwLastTick = CPulse::GetInstance()->m_dwLastTick;
  this->m_dwLastTime = m_dwLastTick;
  this->m_dwLastBehaviorTick = m_dwLastTick;
  m_nCurrentState = this->m_nCurrentState;
  this->m_lCurrentFrame = 30;
  this->m_bAttacking = 0;
  v5 = CFSM::StateTransition(CSingleton<CFSM>::ms_pSingleton, m_nCurrentState, 102);
  m_lpCell = this->m_CellPos.m_lpCell;
  this->m_nCurrentState = v5;
  if ( m_lpCell )
  {
    CCell::DeleteCreature(
      m_lpCell,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    this->m_CellPos.m_lpCell = 0;
  }
  this->m_dwLastTime = CPulse::GetInstance()->m_dwLastTick;
  CAffectedSpell::ClearEnchant(&this->m_SpellMgr.m_AffectedInfo);
  p_m_CastingInfo = &this->m_SpellMgr.m_CastingInfo;
  CCastingSpell::ClearEnchant(p_m_CastingInfo);
  CCastingSpell::DisableChant(p_m_CastingInfo, 0);
  return 1;
}

//----- (00454480) --------------------------------------------------------
char __thiscall CStatueInfo::operator()(CStatueInfo *this, CCharacter *lpCharacter)
{
  CStatue *m_lpStatue; // eax
  unsigned int m_dwCID; // edx
  CCreatureManager *Instance; // eax
  unsigned __int8 BonusTurn; // al
  CStatue *v7; // edi
  unsigned int v8; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // esi
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-28h]
  PktStatueInfo pktSI; // [esp+8h] [ebp-1Ch] BYREF

  if ( !lpCharacter || lpCharacter->m_CellPos.m_wMapIndex != this->m_lpStatue->m_CellPos.m_wMapIndex )
    return 0;
  pktSI.m_dwMileage = 0;
  pktSI.m_wHumanNum = CCreatureManager::GetInstance()->m_wCharacterNum[0];
  pktSI.m_wAkhanNum = CCreatureManager::GetInstance()->m_wCharacterNum[1];
  m_lpStatue = this->m_lpStatue;
  pktSI.m_wNowHP = this->m_lpStatue->m_CreatureStatus.m_nNowHP;
  pktSI.m_wKID = m_lpStatue->m_dwCID;
  pktSI.m_cState = m_lpStatue->m_nCurrentState;
  m_dwCID = m_lpStatue->m_dwCID;
  LOBYTE(m_lpStatue) = this->m_bBonusTurn;
  pktSI.m_cIndex = (m_dwCID + 0x80000000) >> 16;
  if ( (_BYTE)m_lpStatue == 1 )
  {
    m_wMapIndex = lpCharacter->m_CellPos.m_wMapIndex;
    Instance = CCreatureManager::GetInstance();
    BonusTurn = CCreatureManager::GetBonusTurn(Instance, m_wMapIndex);
    if ( lpCharacter->m_CreatureStatus.m_nNowHP )
    {
      v7 = this->m_lpStatue;
      if ( v7->m_CreatureStatus.m_nNowHP )
      {
        if ( lpCharacter->m_DBData.m_Info.Nationality == BonusTurn
          && (unsigned int)Position::GetDistance(&v7->m_CurrentPos, &lpCharacter->m_CurrentPos) < 0x64 )
        {
          v8 = lpCharacter->m_DBData.m_Info.Mileage + 20;
          lpCharacter->m_DBData.m_Info.Mileage = v8;
          pktSI.m_dwMileage = v8;
        }
      }
    }
  }
  m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch && PacketWrap::WrapCrypt((char *)&pktSI, 0x1Au, 0x85u, 0, 0) )
    return CSendStream::PutBuffer(&m_lpGameClientDispatch->m_SendStream, (char *)&pktSI, 0x1Au, 0x85u);
  else
    return 0;
}

//----- (004545B0) --------------------------------------------------------
void __thiscall CSkillMonster::CSkillMonster(
        CSkillMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CMonster::CMonster(this, MonsterCreate, bAdminCmdSummon);
  this->m_cConsumeMPCount = 0;
  this->m_bCasting = 0;
  this->m_nCastingCount = 0;
  this->m_dwTargetCID = 0;
  this->m_cSkillPattern = 0;
  this->__vftable = (CSkillMonster_vtbl *)&CSkillMonster::`vftable';
  this->m_lastCastTime[0] = 0;
  this->m_lastCastTime[1] = 0;
  this->m_lastCastTime[2] = 0;
  this->m_lastCastTime[3] = 0;
  this->m_lastCastTime[4] = 0;
  this->m_attackType = 0;
}
// 4E22D0: using guessed type void *CSkillMonster::`vftable';

//----- (00454620) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __thiscall CSkillMonster::Attack(
        CSkillMonster *this,
        AtType attackType,
        unsigned __int8 cDefenderNum,
        CAggresiveCreature **ppDefenders,
        unsigned __int8 *cDefenderJudges)
{
  unsigned __int8 v7; // bl
  char *p_cDefenderNum; // eax
  unsigned __int8 v9; // cl
  CAggresiveCreature **v10; // edx
  CCharacter **v11; // ebp
  int v12; // ebx
  unsigned __int8 *v13; // esi
  CCharacter *v14; // ebp
  CMonster *m_lpSummonee; // ecx
  CSendStream *m_lpGameClientDispatch; // eax
  CAggresiveCreature **v17; // edx
  int v18; // eax
  CAggresiveCreature *v19; // ecx
  int v20; // eax
  unsigned __int16 m_wType; // dx
  char *Name; // ecx
  CMonsterShout *Instance; // eax
  CCell *m_lpCell; // ecx
  unsigned int m_dwCID; // [esp-Ah] [ebp-DCh]
  CPacketDispatch *m_dwKID; // [esp-6h] [ebp-D8h]
  unsigned __int16 m_fPointX; // [esp-2h] [ebp-D4h]
  unsigned __int16 m_fPointZ; // [esp+2h] [ebp-D0h]
  CMonsterShout::Behavior v29; // [esp+6h] [ebp-CCh]
  const char *v30; // [esp+Ah] [ebp-C8h]
  unsigned __int16 v31; // [esp+Eh] [ebp-C4h]
  int v32; // [esp+1Eh] [ebp-B4h] BYREF
  __int16 cDefender; // [esp+22h] [ebp-B0h]
  unsigned int nCriticalCount; // [esp+26h] [ebp-ACh]
  int wDamage; // [esp+2Ah] [ebp-A8h] OVERLAPPED
  CCharacter *lpDefendShoutCharacter; // [esp+2Eh] [ebp-A4h]
  int wError; // [esp+32h] [ebp-A0h] BYREF
  unsigned __int16 wPrevAttackerHP; // [esp+36h] [ebp-9Ch]
  DefenserNode Defenser[10]; // [esp+3Ah] [ebp-98h] BYREF

  if ( this->m_CreatureStatus.m_nNowHP )
  {
    v7 = cDefenderNum;
    if ( cDefenderNum > 0xAu )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CSkillMonster::Attack",
        aDWorkRylSource_51,
        704,
        aCid0x08x_285,
        this->m_dwCID,
        cDefenderNum);
      cDefenderNum = 10;
      v7 = 10;
    }
    if ( (attackType.m_wType & 0x8000) != 0 || v7 )
    {
      if ( (attackType.m_wType & 0x8000) == 0
        || CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType) )
      {
        wError = 0;
        HIWORD(v32) = 5;
        p_cDefenderNum = (char *)&v32 + 2;
        if ( v7 <= 5u )
          p_cDefenderNum = (char *)&cDefenderNum;
        v9 = 0;
        this->m_cConsumeMPCount = *p_cDefenderNum;
        LOBYTE(cDefender) = 0;
        BYTE2(v32) = 0;
        nCriticalCount = 0;
        for ( lpDefendShoutCharacter = 0; v9 < v7; BYTE2(v32) = ++v9 )
        {
          v10 = ppDefenders;
          --this->m_cConsumeMPCount;
          v11 = (CCharacter **)&v10[v9];
          if ( *v11 && (*v11)->m_CreatureStatus.m_nNowHP )
          {
            v12 = (unsigned __int8)cDefender;
            v13 = &cDefenderJudges[(unsigned __int8)cDefender];
            *v13 = 0;
            wPrevAttackerHP = this->m_CreatureStatus.m_nNowHP;
            wDamage = ((int (__thiscall *)(CCharacter *, AtType, CSkillMonster *, char *, unsigned __int8 *, int *))(*v11)->ApplyDamage)(
                        *v11,
                        attackType,
                        this,
                        (char *)&v32 + 3,
                        v13,
                        &wError);
            if ( *v13 == 4 )
              ++nCriticalCount;
            if ( !this->m_CreatureStatus.m_nNowHP )
            {
              this->m_CreatureStatus.m_nNowHP = wPrevAttackerHP;
              wError = 4;
              break;
            }
            v14 = *v11;
            if ( (v14->m_dwCID & 0xD0000000) == 0 )
            {
              if ( !lpDefendShoutCharacter || !((int)rand() % 3) )
                lpDefendShoutCharacter = v14;
              m_lpSummonee = v14->m_lpSummonee;
              if ( m_lpSummonee )
                m_lpSummonee->GuardMe(m_lpSummonee, this, wDamage);
              v14->CalculateEquipDurability(v14, (*v13 == 3) + 5);
              m_lpGameClientDispatch = (CSendStream *)v14->m_lpGameClientDispatch;
              if ( m_lpGameClientDispatch )
                GameClientSendPacket::SendCharAttacked(
                  m_lpGameClientDispatch + 8,
                  this,
                  v14,
                  attackType,
                  this->m_MotionInfo.m_fDirection,
                  wDamage,
                  *v13,
                  0);
            }
            v17 = ppDefenders;
            v18 = v12;
            Defenser[v18].m_cJudge = *v13;
            v19 = v17[v12];
            v7 = cDefenderNum;
            Defenser[v18].m_dwCharID = v19->m_dwCID;
            Defenser[v18].m_wMaxHP = v19->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
            Defenser[v18].m_wMaxMP = v19->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
            Defenser[v18].m_sCurrHP = v19->m_CreatureStatus.m_nNowHP;
            LOWORD(v17) = wDamage;
            Defenser[v18].m_sCurrMP = v19->m_CreatureStatus.m_nNowMP;
            v9 = BYTE2(v32);
            Defenser[v18].m_wDamage = (unsigned __int16)v17;
            LOBYTE(cDefender) = cDefender + 1;
          }
        }
        v20 = 0;
        m_wType = 0;
        if ( (attackType.m_wType & 0x8000u) == 0 )
        {
          if ( nCriticalCount )
            v20 = 4;
        }
        else
        {
          if ( !(_BYTE)cDefender )
            Skill::CFunctions::ConsumeMP(attackType, this);
          m_wType = attackType.m_wType;
          v20 = 2;
        }
        Name = 0;
        if ( lpDefendShoutCharacter )
          Name = lpDefendShoutCharacter->m_DBData.m_Info.Name;
        v31 = m_wType;
        v30 = Name;
        v29 = v20;
        m_fPointZ = (unsigned __int64)this->m_CurrentPos.m_fPointZ;
        m_fPointX = (unsigned __int64)this->m_CurrentPos.m_fPointX;
        m_dwKID = (CPacketDispatch *)this->m_MonsterInfo.m_dwKID;
        m_dwCID = this->m_dwCID;
        Instance = CMonsterShout::GetInstance();
        CMonsterShout::Shout(Instance, m_dwCID, m_dwKID, m_fPointX, m_fPointZ, v29, v30, v31);
        m_lpCell = this->m_CellPos.m_lpCell;
        if ( m_lpCell )
          CCell::SendAttackInfo(m_lpCell, this->m_dwCID, &attackType, cDefender, Defenser);
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSkillMonster::Attack",
          aDWorkRylSource_51,
          720,
          aCid0x08x_77,
          this->m_dwCID,
          attackType.m_wType);
        return 0;
      }
    }
    else
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSkillMonster::Attack", aDWorkRylSource_51, 710, (char *)&byte_4D9178);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMonster::Attack",
      aDWorkRylSource_51,
      697,
      aCid0x08x_22,
      this->m_dwCID);
    return 0;
  }
}
// 454620: variables would overlap: ^3C.4 and stkvar "wDamage" ^3C.2(has user info)

//----- (004549E0) --------------------------------------------------------
char __thiscall CSkillMonster::UseSkill(
        CSkillMonster *this,
        AtType attackType,
        CAggresiveCreature **ppDefenders,
        char cSkillPattern)
{
  CSkillMgr::ProtoTypeArray *SkillProtoType; // ebx
  char result; // al
  __int16 v7; // ax
  __int16 v8; // dx
  unsigned __int8 v9; // cl
  int v10; // edx
  unsigned __int16 v11; // di
  char *v12; // ebp
  DWORD Time; // eax
  bool m_bIsClassSkill; // dl
  unsigned int v15; // ecx
  unsigned int m_dwCoolDownTime; // eax
  unsigned int v17; // ecx
  CAggresiveCreature *v18; // edi
  double v19; // st5
  double v20; // st4
  double v21; // st7
  double v22; // st5
  Skill::Target::Type m_eTargetType; // eax
  CCell *m_lpCell; // ecx
  CAggresiveCreature *v25; // eax
  double m_fPointZ; // st7
  bool v27; // dl
  CAggresiveCreature **v28; // edi
  CParty *v29; // eax
  float nRange; // [esp+0h] [ebp-50h]
  char cTargetType; // [esp+1Ch] [ebp-34h]
  float fDir; // [esp+20h] [ebp-30h] BYREF
  unsigned int dwCurrentTime; // [esp+24h] [ebp-2Ch]
  unsigned int *v34; // [esp+28h] [ebp-28h]
  CastObject castObject; // [esp+2Ch] [ebp-24h] BYREF
  unsigned __int8 nDefenserJudges[10]; // [esp+40h] [ebp-10h] BYREF

  if ( !*ppDefenders )
    return 0;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMonster::UseSkill",
      aDWorkRylSource_51,
      880,
      aCid0x08x_77,
      this->m_dwCID,
      attackType.m_wType);
    return 0;
  }
  v7 = this->m_MonsterInfo.m_cSkillLevel / 6;
  v8 = this->m_MonsterInfo.m_cSkillLevel % 6;
  cTargetType = 3;
  v9 = v8;
  if ( (unsigned __int8)v7 < 4u )
  {
    if ( !(_BYTE)v7 )
      goto LABEL_12;
  }
  else
  {
    LOBYTE(v7) = 3;
  }
  if ( (_BYTE)v8 != 3 )
  {
    if ( (unsigned __int8)v8 < 3u )
      LOBYTE(v7) = v7 - 1;
    v9 = 3;
  }
LABEL_12:
  v10 = (unsigned __int8)v7 << 9;
  v11 = *(unsigned __int16 *)((char *)&SkillProtoType->m_ProtoTypes[0].m_StartMP + v10)
      + *(unsigned __int16 *)((char *)&SkillProtoType->m_ProtoTypes[0].m_LevelMP + v10) * v9;
  v12 = (char *)SkillProtoType + v10;
  Time = timeGetTime();
  m_bIsClassSkill = SkillProtoType->m_ProtoTypes[0].m_bIsClassSkill;
  v15 = Time;
  m_dwCoolDownTime = *((_DWORD *)v12 + 122);
  dwCurrentTime = v15;
  if ( m_bIsClassSkill )
    m_dwCoolDownTime = SkillProtoType->m_ProtoTypes[0].m_dwCoolDownTime;
  if ( this->m_CreatureStatus.m_nNowMP < v11 )
    return 0;
  v17 = v15 - this->m_lastCastTime[cSkillPattern];
  v34 = &this->m_lastCastTime[cSkillPattern];
  if ( v17 < m_dwCoolDownTime || Math::Random::ComplexRandom(100, 0) > this->m_MonsterInfo.m_wSkillUseRate )
    return 0;
  v18 = *ppDefenders;
  if ( *ppDefenders )
  {
    v19 = this->m_CurrentPos.m_fPointX - v18->m_CurrentPos.m_fPointX;
    v20 = this->m_CurrentPos.m_fPointZ - v18->m_CurrentPos.m_fPointZ;
    v21 = v19 * v19 + v20 * v20;
    v22 = *((float *)v12 + 123) + 10.0;
    if ( v21 > v22 * v22 )
      return 0;
    m_eTargetType = SkillProtoType->m_ProtoTypes[0].m_eTargetType;
    if ( m_eTargetType == MIDDLE_ADMIN
      || m_eTargetType == LEAVE_WAIT
      || m_eTargetType == 9
      || m_eTargetType == MAX_TITLE
      || m_eTargetType == 8
      || m_eTargetType == (COMMON|0x8) )
    {
      cTargetType = 2;
    }
  }
  fDir = 0.0;
  std::fill_n<unsigned char *,short,enum ClientConstants::Judge>(
    nDefenserJudges,
    0xAu,
    (const ClientConstants::Judge *)&fDir);
  fDir = CAggresiveCreature::CalcDir2D(
           this,
           this->m_CurrentPos.m_fPointX,
           this->m_CurrentPos.m_fPointZ,
           v18->m_CurrentPos.m_fPointX,
           v18->m_CurrentPos.m_fPointZ);
  if ( SkillProtoType->m_ProtoTypes[0].m_eSkillType == COMMON )
  {
    result = 1;
    this->m_attackType = attackType;
    this->m_bCasting = 1;
    this->m_dwTargetCID = (*ppDefenders)->m_dwCID;
    this->m_cSkillPattern = cSkillPattern;
    return result;
  }
  *v34 = dwCurrentTime;
  m_lpCell = this->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    v25 = *ppDefenders;
    castObject.m_wTypeID = attackType.m_wType;
    castObject.m_DstPos.fPointX = v25->m_CurrentPos.m_fPointX * 100.0;
    castObject.m_DstPos.fPointY = v25->m_CurrentPos.m_fPointY * 100.0;
    m_fPointZ = v25->m_CurrentPos.m_fPointZ;
    castObject.m_cObjectLevel = *((_BYTE *)&attackType + 2) >> 4;
    v27 = SkillProtoType->m_ProtoTypes[0].m_bIsClassSkill;
    castObject.m_cObjectType = 6;
    castObject.m_DstPos.fPointZ = m_fPointZ * 100.0;
    if ( v27 )
      castObject.m_cObjectLevel = 0;
    castObject.m_dwTargetID = v25->m_dwCID;
    CCell::SendCastObjectInfo(m_lpCell, this->m_dwCID, v25->m_dwCID, &castObject);
  }
  if ( 0.0 == *((float *)v12 + 124) )
  {
    v28 = ppDefenders;
  }
  else
  {
    if ( *((_DWORD *)v12 + 115) != 8 )
    {
      nRange = *((float *)v12 + 123) + 10.0;
      return CAggresiveCreature::MultiAttack(
               this,
               attackType,
               1,
               ppDefenders,
               (CCell *)nDefenserJudges,
               this->m_CurrentPos,
               fDir,
               nRange,
               this->m_MonsterInfo.m_fAttackAngle,
               cTargetType);
    }
    v28 = ppDefenders;
    v29 = (*ppDefenders)->GetParty(*ppDefenders);
    if ( v29 )
      return ((int (__thiscall *)(CParty *, AtType, CAggresiveCreature **, unsigned __int8 *, CSkillMonster *, _DWORD, char))v29->Attack)(
               v29,
               attackType,
               ppDefenders,
               nDefenserJudges,
               this,
               LODWORD(SkillProtoType->m_ProtoTypes[*((unsigned __int8 *)&attackType + 2) >> 4].m_EffectExtent),
               cTargetType);
  }
  return ((int (__thiscall *)(_DWORD, _DWORD, _DWORD, _DWORD, _DWORD))this->Attack)(
           this,
           attackType,
           1u,
           v28,
           nDefenserJudges);
}

//----- (00454D80) --------------------------------------------------------
bool __thiscall CSkillMonster::UseCastedSkill(CSkillMonster *this)
{
  CCreatureManager *Instance; // eax
  CMsgProc *Creature; // eax
  CAggresiveCreature *v4; // edi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // ebp
  __int64 v7; // rax
  unsigned __int8 v8; // bl
  double m_fPointZ; // st7
  Skill::ProtoType *v10; // ebx
  double v11; // st5
  double v12; // st4
  double v13; // st7
  double v14; // st5
  Skill::Target::Type m_eTargetType; // eax
  CCell *m_lpCell; // ecx
  double v17; // st7
  unsigned __int16 m_wType; // dx
  bool m_bIsClassSkill; // al
  double v20; // st7
  unsigned int m_dwCID; // eax
  CParty *v22; // eax
  float nRange; // [esp+0h] [ebp-70h]
  unsigned int m_dwTargetCID; // [esp+8h] [ebp-68h]
  float fDir; // [esp+1Ch] [ebp-54h] BYREF
  int cTargetType; // [esp+20h] [ebp-50h]
  CastObject castObject; // [esp+24h] [ebp-4Ch] BYREF
  CAggresiveCreature *ppDefenders[10]; // [esp+38h] [ebp-38h] BYREF
  unsigned __int8 nDefenserJudges[10]; // [esp+60h] [ebp-10h] BYREF

  m_dwTargetCID = this->m_dwTargetCID;
  Instance = CCreatureManager::GetInstance();
  Creature = CCreatureManager::GetCreature(Instance, m_dwTargetCID);
  v4 = (CAggresiveCreature *)Creature;
  if ( !Creature || LOWORD(Creature[101].__vftable) != this->m_CellPos.m_wMapIndex )
    return 0;
  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, this->m_attackType.m_wType);
  if ( !SkillProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSkillMonster::UseCastedSkill",
      aDWorkRylSource_51,
      1026,
      aCid0x08x_77,
      this->m_dwCID,
      this->m_attackType.m_wType);
    return 0;
  }
  v7 = 715827883LL * this->m_MonsterInfo.m_cSkillLevel;
  LOBYTE(cTargetType) = 3;
  v8 = BYTE4(v7) + (v7 < 0);
  this->m_lastCastTime[this->m_cSkillPattern] = timeGetTime();
  m_fPointZ = this->m_CurrentPos.m_fPointZ;
  memset(&ppDefenders[1], 0, 36);
  ppDefenders[0] = v4;
  v10 = &SkillProtoType->m_ProtoTypes[v8];
  v11 = this->m_CurrentPos.m_fPointX - v4->m_CurrentPos.m_fPointX;
  v12 = m_fPointZ - v4->m_CurrentPos.m_fPointZ;
  v13 = v11 * v11 + v12 * v12;
  v14 = v10->m_EffectDistance + 10.0;
  if ( v13 > v14 * v14 )
    return 0;
  m_eTargetType = SkillProtoType->m_ProtoTypes[0].m_eTargetType;
  if ( m_eTargetType == MIDDLE_ADMIN
    || m_eTargetType == LEAVE_WAIT
    || m_eTargetType == 9
    || m_eTargetType == MAX_TITLE
    || m_eTargetType == 8
    || m_eTargetType == (COMMON|0x8) )
  {
    LOBYTE(cTargetType) = 2;
  }
  fDir = 0.0;
  std::fill_n<unsigned char *,short,enum ClientConstants::Judge>(
    nDefenserJudges,
    0xAu,
    (const ClientConstants::Judge *)&fDir);
  fDir = CAggresiveCreature::CalcDir2D(
           this,
           this->m_CurrentPos.m_fPointX,
           this->m_CurrentPos.m_fPointZ,
           v4->m_CurrentPos.m_fPointX,
           v4->m_CurrentPos.m_fPointZ);
  m_lpCell = this->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    v17 = v4->m_CurrentPos.m_fPointX * 100.0;
    m_wType = this->m_attackType.m_wType;
    castObject.m_cObjectLevel = *((_BYTE *)&this->m_attackType + 2) >> 4;
    m_bIsClassSkill = SkillProtoType->m_ProtoTypes[0].m_bIsClassSkill;
    castObject.m_DstPos.fPointX = v17;
    v20 = v4->m_CurrentPos.m_fPointY * 100.0;
    castObject.m_wTypeID = m_wType;
    castObject.m_cObjectType = 6;
    castObject.m_DstPos.fPointY = v20;
    castObject.m_DstPos.fPointZ = v4->m_CurrentPos.m_fPointZ * 100.0;
    if ( m_bIsClassSkill )
      castObject.m_cObjectLevel = 0;
    m_dwCID = this->m_dwCID;
    castObject.m_dwTargetID = v4->m_dwCID;
    CCell::SendCastObjectInfo(m_lpCell, m_dwCID, castObject.m_dwTargetID, &castObject);
  }
  if ( 0.0 != v10->m_EffectExtent )
  {
    if ( v10->m_eTargetType != 8 )
    {
      nRange = v10->m_EffectDistance + 10.0;
      return CAggresiveCreature::MultiAttack(
               this,
               this->m_attackType,
               1,
               ppDefenders,
               (CCell *)nDefenserJudges,
               this->m_CurrentPos,
               fDir,
               nRange,
               this->m_MonsterInfo.m_fAttackAngle,
               cTargetType);
    }
    v22 = ppDefenders[0]->GetParty(ppDefenders[0]);
    if ( v22 )
      return ((int (__thiscall *)(CParty *, _DWORD, CAggresiveCreature **, unsigned __int8 *, CSkillMonster *, _DWORD, int))v22->Attack)(
               v22,
               *(_DWORD *)&this->m_attackType,
               ppDefenders,
               nDefenserJudges,
               this,
               LODWORD(SkillProtoType->m_ProtoTypes[*((unsigned __int8 *)&this->m_attackType + 2) >> 4].m_EffectExtent),
               cTargetType);
  }
  return ((int (__thiscall *)(CSkillMonster *, _DWORD, int, CAggresiveCreature **, unsigned __int8 *))this->Attack)(
           this,
           *(_DWORD *)&this->m_attackType,
           1,
           ppDefenders,
           nDefenserJudges);
}

//----- (00455090) --------------------------------------------------------
void __thiscall CDefenderMonster::CDefenderMonster(
        CDefenderMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CDefenderMonster_vtbl *)&CDefenderMonster::`vftable';
}
// 4E2530: using guessed type void *CDefenderMonster::`vftable';

//----- (004550B0) --------------------------------------------------------
void __thiscall CWarriorMonster::CWarriorMonster(
        CWarriorMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CWarriorMonster_vtbl *)&CWarriorMonster::`vftable';
}
// 4E25C8: using guessed type void *CWarriorMonster::`vftable';

//----- (004550D0) --------------------------------------------------------
void __thiscall CAcolyteMonster::CAcolyteMonster(
        CAcolyteMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CAcolyteMonster_vtbl *)&CAcolyteMonster::`vftable';
  this->m_wDefaultSearchRange = 18;
}
// 4E2660: using guessed type void *CAcolyteMonster::`vftable';

//----- (00455100) --------------------------------------------------------
CNamedMonster *__thiscall CAcolyteMonster::`vector deleting destructor'(CNamedMonster *this, char a2)
{
  CMageMonster::~CMageMonster(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00455120) --------------------------------------------------------
void __thiscall CMageMonster::CMageMonster(
        CMageMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CMageMonster_vtbl *)&CMageMonster::`vftable';
  this->m_wDefaultSearchRange = 18;
}
// 4E26F8: using guessed type void *CMageMonster::`vftable';

//----- (00455150) --------------------------------------------------------
void __thiscall CBossMonster::CBossMonster(
        CBossMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CBossMonster_vtbl *)&CBossMonster::`vftable';
  this->m_wDefaultSearchRange = 15;
}
// 4E2790: using guessed type void *CBossMonster::`vftable';

//----- (00455180) --------------------------------------------------------
void __thiscall CChiefMonster::CChiefMonster(
        CChiefMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CChiefMonster_vtbl *)&CChiefMonster::`vftable';
  this->m_wDefaultSearchRange = 15;
}
// 4E2828: using guessed type void *CChiefMonster::`vftable';

//----- (004551B0) --------------------------------------------------------
void __thiscall CNamedMonster::CNamedMonster(
        CNamedMonster *this,
        CMonster::MonsterCreateInfo *MonsterCreate,
        bool bAdminCmdSummon)
{
  int m_wRespawnArea; // edx
  DWORD TickCount; // eax
  DWORD v6; // eax
  int v7; // [esp-8h] [ebp-28h]
  int v8; // [esp-8h] [ebp-28h]

  CSkillMonster::CSkillMonster(this, MonsterCreate, bAdminCmdSummon);
  this->__vftable = (CNamedMonster_vtbl *)&CNamedMonster::`vftable';
  this->m_RespawnPosition.m_fPointX = 0.0;
  this->m_RespawnPosition.m_fPointY = 0.0;
  this->m_RespawnPosition.m_fPointZ = 0.0;
  this->m_RespawnPosition.m_fPointX = MonsterCreate->m_Pos.m_fPointX;
  m_wRespawnArea = this->m_wRespawnArea;
  this->m_RespawnPosition.m_fPointY = MonsterCreate->m_Pos.m_fPointY;
  v7 = 2 * m_wRespawnArea;
  this->m_RespawnPosition.m_fPointZ = MonsterCreate->m_Pos.m_fPointZ;
  TickCount = GetTickCount();
  MonsterCreate->m_Pos.m_fPointX = (double)(Math::Random::SimpleRandom(TickCount, v7, 0) - this->m_wRespawnArea)
                                 + MonsterCreate->m_Pos.m_fPointX;
  v8 = 2 * this->m_wRespawnArea;
  v6 = GetTickCount();
  MonsterCreate->m_Pos.m_fPointZ = (double)(Math::Random::SimpleRandom(v6, v8, 0) - this->m_wRespawnArea)
                                 + MonsterCreate->m_Pos.m_fPointZ;
  this->m_OriginalPosition = MonsterCreate->m_Pos;
  this->m_wDefaultSearchRange = 15;
}
// 4E28C0: using guessed type void *CNamedMonster::`vftable';

//----- (004552B0) --------------------------------------------------------
void __thiscall CNamedMonster::Respawn(CNamedMonster *this, unsigned int dwTick)
{
  Position *p_m_OriginalPosition; // edi
  float m_fPointZ; // eax
  double v5; // st7
  double v6; // st7
  CMonsterShout *Instance; // eax
  CParty *Guild; // eax
  int m_wMapIndex; // ecx
  unsigned int m_dwCID; // [esp-1Ch] [ebp-28h]
  CPacketDispatch *m_dwKID; // [esp-18h] [ebp-24h]
  unsigned __int16 m_fPointX; // [esp-14h] [ebp-20h]
  int m_wRespawnArea; // [esp+8h] [ebp-4h]

  p_m_OriginalPosition = &this->m_OriginalPosition;
  this->m_OriginalPosition.m_fPointX = this->m_RespawnPosition.m_fPointX;
  m_fPointZ = this->m_RespawnPosition.m_fPointZ;
  this->m_OriginalPosition.m_fPointY = this->m_RespawnPosition.m_fPointY;
  this->m_OriginalPosition.m_fPointZ = m_fPointZ;
  v5 = (double)Math::Random::ComplexRandom(2 * this->m_wRespawnArea, 0);
  m_wRespawnArea = this->m_wRespawnArea;
  p_m_OriginalPosition->m_fPointX = v5 - (double)m_wRespawnArea + p_m_OriginalPosition->m_fPointX;
  this->m_OriginalPosition.m_fPointZ = (double)Math::Random::ComplexRandom(2 * m_wRespawnArea, 0)
                                     - (double)this->m_wRespawnArea
                                     + this->m_OriginalPosition.m_fPointZ;
  CMonster::InitMonster(this, p_m_OriginalPosition, RESPAWN);
  v6 = this->m_CurrentPos.m_fPointZ;
  this->m_nCurrentState = 1;
  m_fPointX = (unsigned __int64)this->m_CurrentPos.m_fPointX;
  m_dwKID = (CPacketDispatch *)this->m_MonsterInfo.m_dwKID;
  m_dwCID = this->m_dwCID;
  Instance = CMonsterShout::GetInstance();
  CMonsterShout::Shout(Instance, m_dwCID, m_dwKID, m_fPointX, (unsigned __int64)v6, (CMonsterShout::Behavior)6, 0, 0);
  Guild = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, this->m_dwPID);
  if ( Guild )
  {
    m_wMapIndex = this->m_CellPos.m_wMapIndex;
    this->m_pParty = Guild;
    Guild->Join(Guild, this->m_dwCID, 0, 0, m_wMapIndex);
  }
}

//----- (004553D0) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Mid,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster *v3; // edx
  CMonster *v4; // esi
  CMonster *v5; // edx

  if ( (*_Mid._Myptr)->m_CreatureStatus.m_nLevel > (*_First._Myptr)->m_CreatureStatus.m_nLevel )
  {
    v3 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v3;
  }
  if ( (*_Last._Myptr)->m_CreatureStatus.m_nLevel > (*_Mid._Myptr)->m_CreatureStatus.m_nLevel )
  {
    v4 = *_Last._Myptr;
    *_Last._Myptr = *_Mid._Myptr;
    *_Mid._Myptr = v4;
  }
  if ( (*_Mid._Myptr)->m_CreatureStatus.m_nLevel > (*_First._Myptr)->m_CreatureStatus.m_nLevel )
  {
    v5 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v5;
  }
}

//----- (00455430) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Mid,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster *v3; // edx
  CMonster *v4; // esi
  CMonster *v5; // edx

  if ( (*_Mid._Myptr)->m_CreatureStatus.m_nNowHP < (*_First._Myptr)->m_CreatureStatus.m_nNowHP )
  {
    v3 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v3;
  }
  if ( (*_Last._Myptr)->m_CreatureStatus.m_nNowHP < (*_Mid._Myptr)->m_CreatureStatus.m_nNowHP )
  {
    v4 = *_Last._Myptr;
    *_Last._Myptr = *_Mid._Myptr;
    *_Mid._Myptr = v4;
  }
  if ( (*_Mid._Myptr)->m_CreatureStatus.m_nNowHP < (*_First._Myptr)->m_CreatureStatus.m_nNowHP )
  {
    v5 = *_Mid._Myptr;
    *_Mid._Myptr = *_First._Myptr;
    *_First._Myptr = v5;
  }
}

//----- (004554A0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        int _Hole,
        int _Top,
        CMonster *_Val)
{
  int v4; // ecx
  int i; // eax

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    if ( _First._Myptr[i]->m_CreatureStatus.m_nLevel <= _Val->m_CreatureStatus.m_nLevel )
      break;
    _First._Myptr[v4] = _First._Myptr[i];
    v4 = i;
  }
  _First._Myptr[v4] = _Val;
}

//----- (004554F0) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Mid,
        std::vector<unsigned long>::iterator _Last)
{
  unsigned int *Myptr; // ebx
  unsigned int *v4; // ebp
  int v5; // esi
  int v6; // eax
  int v7; // edi
  int v8; // edx
  unsigned int *v9; // edx
  unsigned int *v10; // edi
  std::vector<unsigned long>::iterator *p_First; // eax
  unsigned int *v12; // ecx
  int v13; // eax
  unsigned int **v14; // eax
  bool v15; // zf
  unsigned int *v16; // eax
  unsigned int *v17; // [esp+10h] [ebp-Ch] BYREF
  unsigned int *v18; // [esp+14h] [ebp-8h] BYREF
  unsigned int _Holeval; // [esp+18h] [ebp-4h]

  Myptr = _Last._Myptr;
  v4 = _First._Myptr;
  v5 = _Mid._Myptr - _First._Myptr;
  v6 = _Last._Myptr - _First._Myptr;
  v7 = v5;
  if ( v5 )
  {
    do
    {
      v8 = v6 % v7;
      v6 = v7;
      v7 = v8;
    }
    while ( v8 );
  }
  if ( v6 < _Last._Myptr - _First._Myptr && v6 > 0 )
  {
    v9 = &_First._Myptr[v6];
    _Mid._Myptr = (unsigned int *)v6;
    do
    {
      _Holeval = *v9;
      v10 = v9;
      if ( &v9[v5] == Myptr )
      {
        p_First = &_First;
      }
      else
      {
        _Last._Myptr = &v9[v5];
        p_First = &_Last;
      }
      v12 = p_First->_Myptr;
      if ( p_First->_Myptr != v9 )
      {
        do
        {
          *v10 = *v12;
          v13 = Myptr - v12;
          v10 = v12;
          if ( v5 >= v13 )
          {
            v18 = &v4[v5 + 0x3FFFFFFF * v13];
            v14 = &v18;
          }
          else
          {
            v17 = &v12[v5];
            v14 = &v17;
          }
          v12 = *v14;
        }
        while ( *v14 != v9 );
      }
      --v9;
      v16 = (unsigned int *)((char *)_Mid._Myptr - 1);
      v15 = _Mid._Myptr == (unsigned int *)1;
      *v10 = _Holeval;
      _Mid._Myptr = v16;
    }
    while ( !v15 );
  }
}

//----- (004555B0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        int _Hole,
        int _Top,
        CMonster *_Val)
{
  int v4; // ecx
  int i; // eax

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    if ( _First._Myptr[i]->m_CreatureStatus.m_nNowHP >= _Val->m_CreatureStatus.m_nNowHP )
      break;
    _First._Myptr[v4] = _First._Myptr[i];
    v4 = i;
  }
  _First._Myptr[v4] = _Val;
}

//----- (00455600) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Erase(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00455640) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rarg,
        unsigned int *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *)operator new((tagHeader *)0x14);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Parent = _Parg;
    result->_Right = _Rarg;
    result->_Myval = *_Val;
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (00455680) --------------------------------------------------------
unsigned int *__cdecl std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last,
        unsigned int *_Dest)
{
  unsigned int *Myptr; // ecx
  unsigned int *result; // eax

  Myptr = _First._Myptr;
  for ( result = _Dest; Myptr != _Last._Myptr; ++result )
  {
    if ( result )
      *result = *Myptr;
    ++Myptr;
  }
  return result;
}

//----- (004556B0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<CMonster *>::iterator,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Mid,
        std::vector<CMonster *>::iterator _Last)
{
  int v4; // eax
  int v5; // eax
  unsigned int v6; // esi
  CMonster **v7; // [esp+4h] [ebp-4h]
  std::vector<CMonster *>::iterator _Firsta; // [esp+Ch] [ebp+4h]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(_First, _Mid, _Last);
  }
  else
  {
    v5 = (v4 + 1) / 8;
    _Firsta._Myptr = (CMonster **)(8 * v5);
    v6 = 4 * v5;
    v7 = &_First._Myptr[v5];
    std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(
      _First,
      (std::vector<CMonster *>::iterator)v7,
      (std::vector<CMonster *>::iterator)&_First._Myptr[2 * v5]);
    std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(
      (std::vector<CMonster *>::iterator)&_Mid._Myptr[v6 / 0xFFFFFFFC],
      _Mid,
      (std::vector<CMonster *>::iterator)&_Mid._Myptr[v6 / 4]);
    std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(
      (std::vector<CMonster *>::iterator)((char *)_Last._Myptr - (char *)_Firsta._Myptr),
      (std::vector<CMonster *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC],
      _Last);
    std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(
      (std::vector<CMonster *>::iterator)v7,
      _Mid,
      (std::vector<CMonster *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC]);
  }
}

//----- (00455760) --------------------------------------------------------
void __cdecl std::_Median<std::vector<CMonster *>::iterator,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Mid,
        std::vector<CMonster *>::iterator _Last)
{
  int v4; // eax
  int v5; // eax
  unsigned int v6; // esi
  CMonster **v7; // [esp+4h] [ebp-4h]
  std::vector<CMonster *>::iterator _Firsta; // [esp+Ch] [ebp+4h]

  v4 = _Last._Myptr - _First._Myptr;
  if ( v4 <= 40 )
  {
    std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(_First, _Mid, _Last);
  }
  else
  {
    v5 = (v4 + 1) / 8;
    _Firsta._Myptr = (CMonster **)(8 * v5);
    v6 = 4 * v5;
    v7 = &_First._Myptr[v5];
    std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(
      _First,
      (std::vector<CMonster *>::iterator)v7,
      (std::vector<CMonster *>::iterator)&_First._Myptr[2 * v5]);
    std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(
      (std::vector<CMonster *>::iterator)&_Mid._Myptr[v6 / 0xFFFFFFFC],
      _Mid,
      (std::vector<CMonster *>::iterator)&_Mid._Myptr[v6 / 4]);
    std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(
      (std::vector<CMonster *>::iterator)((char *)_Last._Myptr - (char *)_Firsta._Myptr),
      (std::vector<CMonster *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC],
      _Last);
    std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(
      (std::vector<CMonster *>::iterator)v7,
      _Mid,
      (std::vector<CMonster *>::iterator)&_Last._Myptr[v6 / 0xFFFFFFFC]);
  }
}

//----- (00455810) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(
        std::vector<CMonster *>::iterator _First,
        int _Hole,
        int _Bottom,
        CMonster *_Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5]->m_CreatureStatus.m_nLevel > _First._Myptr[v5 - 1]->m_CreatureStatus.m_nLevel )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(_First, v4, _Hole, _Val);
}

//----- (00455880) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(
        std::vector<CMonster *>::iterator _First,
        int _Hole,
        int _Bottom,
        CMonster *_Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5]->m_CreatureStatus.m_nNowHP < _First._Myptr[v5 - 1]->m_CreatureStatus.m_nNowHP )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(_First, v4, _Hole, _Val);
}

//----- (004558F0) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
        std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rootnode,
        std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Wherenode)
{
  char Isnil; // al
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *v5; // edi
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *Left; // [esp-8h] [ebp-2Ch]
  int v8; // [esp+0h] [ebp-24h] BYREF
  std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *v9; // [esp+Ch] [ebp-18h]
  std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Newroot; // [esp+10h] [ebp-14h]
  int *v11; // [esp+14h] [ebp-10h]
  int v12; // [esp+20h] [ebp-4h]

  _Newroot = this->_Myhead;
  Isnil = _Rootnode->_Isnil;
  v11 = &v8;
  v9 = this;
  if ( !Isnil )
  {
    v5 = std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(
           this,
           this->_Myhead,
           _Wherenode,
           this->_Myhead,
           &_Rootnode->_Myval,
           _Rootnode->_Color);
    if ( _Newroot->_Isnil )
      _Newroot = v5;
    Left = _Rootnode->_Left;
    v12 = 0;
    v5->_Left = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
                  this,
                  Left,
                  v5);
    v5->_Right = std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(
                   this,
                   _Rootnode->_Right,
                   v5);
  }
  return _Newroot;
}

//----- (004559A0) --------------------------------------------------------
std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareLevel>(
        std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *result,
        std::vector<CMonster *>::iterator _First,
        std::vector<CMonster *>::iterator _Last)
{
  CMonster **Myptr; // ebx
  CMonster **v4; // ecx
  CMonster **i; // esi
  int m_nLevel; // eax
  int v7; // edx
  int v8; // edx
  int v9; // eax
  CMonster **v10; // eax
  CMonster **v11; // ebp
  int v12; // edx
  int v13; // edi
  CMonster *v14; // edi
  bool v15; // zf
  CMonster **v16; // edx
  int v17; // edi
  int v18; // ebx
  CMonster *v19; // edi
  CMonster *v20; // edx
  CMonster **v21; // edx
  CMonster *v22; // edx
  CMonster *v23; // edi
  CMonster *v24; // edx
  CMonster *v25; // edi
  std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *v26; // eax
  CMonster *v27; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<CMonster *>::iterator,CompareLevel>(
    _First,
    (std::vector<CMonster *>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<CMonster *>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    m_nLevel = (*v4)->m_CreatureStatus.m_nLevel;
    v7 = (*(v4 - 1))->m_CreatureStatus.m_nLevel;
    if ( m_nLevel < v7 )
      break;
    if ( m_nLevel > v7 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v8 = (*v4)->m_CreatureStatus.m_nLevel;
    do
    {
      v9 = (*i)->m_CreatureStatus.m_nLevel;
      if ( v8 < v9 )
        break;
      if ( v8 > v9 )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v10 = i;
  v11 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v10 < Myptr; ++v10 )
      {
        v12 = (*v4)->m_CreatureStatus.m_nLevel;
        v13 = (*v10)->m_CreatureStatus.m_nLevel;
        if ( v13 >= v12 )
        {
          if ( v13 > v12 )
            break;
          v14 = *i;
          *i = *v10;
          Myptr = _Last._Myptr;
          ++i;
          *v10 = v14;
        }
      }
      v15 = v11 == _First._Myptr;
      if ( v11 > _First._Myptr )
      {
        v16 = v11 - 1;
        do
        {
          v17 = (*v4)->m_CreatureStatus.m_nLevel;
          v18 = (*v16)->m_CreatureStatus.m_nLevel;
          if ( v17 >= v18 )
          {
            if ( v17 > v18 )
              break;
            v19 = *--v4;
            *v4 = *v16;
            *v16 = v19;
          }
          --v11;
          --v16;
        }
        while ( _First._Myptr < v11 );
        Myptr = _Last._Myptr;
        v15 = v11 == _First._Myptr;
      }
      if ( v15 )
        break;
      --v11;
      if ( v10 == Myptr )
      {
        if ( v11 != --v4 )
        {
          v22 = *v11;
          *v11 = *v4;
          *v4 = v22;
        }
        v23 = *(i - 1);
        v24 = *v4;
        --i;
        *v4 = v23;
        *i = v24;
      }
      else
      {
        v25 = *v10;
        *v10 = *v11;
        Myptr = _Last._Myptr;
        ++v10;
        *v11 = v25;
      }
    }
    if ( v10 == Myptr )
      break;
    if ( i != v10 )
    {
      v20 = *v4;
      *v4 = *i;
      *i = v20;
    }
    v21 = v10;
    v27 = *v4;
    *v4 = *v10;
    Myptr = _Last._Myptr;
    ++i;
    ++v10;
    ++v4;
    *v21 = v27;
  }
  v26 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v26;
}


