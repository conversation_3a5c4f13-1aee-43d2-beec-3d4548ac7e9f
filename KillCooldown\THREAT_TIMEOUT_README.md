# KillCooldown Plugin - Enhanced with 10-Second Threat Timeout

## Overview
The KillCooldown plugin has been enhanced with a **10-second threat timeout system** that automatically expires threat records after 10 seconds, while maintaining the existing death-based clearing behavior.

## Features Added

### 1. **Threat Timeout System**
- ✅ **Automatic expiration**: Threat records expire after 10 seconds (configurable)
- ✅ **Background cleanup**: Expired threats are cleaned every 2 seconds (configurable)
- ✅ **Death clearing preserved**: Existing `CThreat::ClearAll` behavior maintained
- ✅ **Thread-safe**: Uses mutexes for safe concurrent access
- ✅ **Configurable**: All timeout values can be adjusted via INI file

### 2. **Hook Implementation**
- **CThreat::SaveEnemy** (existing): Kill cooldown management
- **CThreat::AddToThreatList** (new): Threat timeout tracking at `0x0045C060`

### 3. **Configuration Options**
```ini
[THREAT_TIMEOUT]
EnableThreatTimeout=1      ; Enable/disable threat timeout (1/0)
ThreatTimeoutMs=10000      ; Timeout in milliseconds (10 seconds)
CleanupIntervalMs=2000     ; Cleanup frequency (2 seconds)
```

## Technical Implementation

### Data Structures
```cpp
struct ThreatInfoWithTime {
    void* pCreature;        // Attacking creature pointer
    int lThreatAmount;      // Threat amount
    DWORD dwTimestamp;      // When threat was added
    void* pThreatObject;    // CThreat object owner
};

// Maps CThreat objects to their threat entries with timestamps
std::unordered_map<void*, std::vector<ThreatInfoWithTime>> g_ThreatTimeouts;
```

### Key Functions
1. **Hook_AddToThreatList**: Intercepts threat additions and adds timestamps
2. **CleanExpiredThreats**: Removes threats older than timeout value
3. **ThreatTimeoutCleanupThread**: Background thread for periodic cleanup

### Thread Safety
- `g_ThreatTimeoutMutex`: Protects threat timeout data
- `g_CooldownMutex`: Protects existing cooldown data
- `g_CallMutex`: Protects hook call processing

## Behavior

### Normal Operation
1. **Attack occurs** → `CThreat::AddToThreatList` called
2. **Hook intercepts** → Calls original function + adds timestamp
3. **Background cleanup** → Removes threats older than 10 seconds
4. **Death occurs** → `CThreat::ClearAll` clears everything (preserved)

### Configuration Validation
- **ThreatTimeoutMs**: 1 second minimum, 5 minutes maximum
- **CleanupIntervalMs**: 0.5 seconds minimum, 10 seconds maximum
- **EnableThreatTimeout**: Can be disabled without affecting cooldown system

## Compatibility

### With Existing Plugins
- ✅ **FameConfig**: Works with fame/medal system
- ✅ **EliteBonus**: Compatible with elite bonus calculations
- ✅ **BlockReducer**: No conflicts with critical hit system
- ✅ **SkillModifier**: No interference with skill modifications

### With Game Systems
- ✅ **Preserves original threat behavior**: Only adds timeout functionality
- ✅ **Maintains death clearing**: `CThreat::ClearAll` still works
- ✅ **No performance impact**: Efficient cleanup with minimal overhead

## Installation

1. **Compile**: Build the enhanced KillCooldown plugin
2. **Deploy**: Copy to `Release/KillCooldown/` folder
3. **Configure**: Edit `cfg_killcooldown.ini` with desired timeout values
4. **Test**: Verify hooks install successfully (debug mode shows message)

## Debugging

### Debug Mode Messages
- "KillCooldown hooks installed successfully!" - Both hooks working
- "KillCooldown hooks failed to install!" - Hook installation failed

### Log Locations
- Cooldown data: `logs/killcooldowns.dat`
- Threat timeout data: In-memory only (clears on restart)

## Performance Notes

- **Memory usage**: Minimal - only stores active threats with timestamps
- **CPU usage**: Low - cleanup runs every 2 seconds by default
- **Thread overhead**: One additional background thread for cleanup
- **Hook overhead**: Minimal - simple timestamp addition to existing flow

## Future Enhancements

Potential improvements for future versions:
- Logging of threat timeout events
- Per-creature timeout values
- Integration with other threat-based plugins
- Threat timeout statistics and monitoring
