
//----- (0046D850) --------------------------------------------------------
void __thiscall __noreturn std::vector<BanInfo>::_Xlen(std::vector<BanInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0046D8C0) --------------------------------------------------------
void __thiscall std::vector<BanInfo>::_Insert_n(
        std::vector<BanInfo> *this,
        std::vector<BanInfo>::iterator _Where,
        unsigned int _Count,
        const BanInfo *_Val)
{
  int v5; // edx
  BanInfo *Myfirst; // ebx
  int v7; // ecx
  int v8; // edx
  int v9; // eax
  unsigned int v10; // ecx
  int v11; // eax
  int v12; // eax
  unsigned int v13; // ecx
  int v14; // eax
  unsigned int v15; // ebx
  BanInfo *v16; // eax
  BanInfo *v17; // ecx
  BanInfo *v18; // eax
  char *v19; // edi
  BanInfo *v20; // eax
  BanInfo *v21; // edx
  BanInfo *Mylast; // ecx
  BanInfo *v24; // edi
  BanInfo *v25; // [esp-Ch] [ebp-44h]
  unsigned int v26; // [esp-8h] [ebp-40h]
  int v27; // [esp+0h] [ebp-38h] BYREF
  BanInfo *_Newvec; // [esp+Ch] [ebp-2Ch]
  BanInfo _Tmp; // [esp+10h] [ebp-28h] BYREF
  int *v30; // [esp+28h] [ebp-10h]
  int v31; // [esp+34h] [ebp-4h]
  BanInfo *_Wherea; // [esp+40h] [ebp+8h]
  BanInfo *_Valb; // [esp+48h] [ebp+10h]
  BanInfo *_Vala; // [esp+48h] [ebp+10h]

  v5 = *(_DWORD *)_Val->m_szName;
  Myfirst = this->_Myfirst;
  _Tmp.m_dwCID = _Val->m_dwCID;
  v7 = *(_DWORD *)&_Val->m_szName[4];
  *(_DWORD *)_Tmp.m_szName = v5;
  v8 = *(_DWORD *)&_Val->m_szName[8];
  v9 = *(_DWORD *)&_Val->m_szName[12];
  v30 = &v27;
  *(_DWORD *)&_Tmp.m_szName[4] = v7;
  *(_DWORD *)&_Tmp.m_szName[8] = v8;
  *(_DWORD *)&_Tmp.m_szName[12] = v9;
  if ( Myfirst )
    v10 = this->_Myend - Myfirst;
  else
    v10 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( 214748364 - v11 < _Count )
      std::vector<BanInfo>::_Xlen(this);
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( v10 >= _Count + v12 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(_Wherea, Mylast, Mylast);
        std::_Copy_backward_opt<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo *>(
          _Where._Myptr,
          _Wherea,
          _Vala);
        std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(_Where._Myptr, Mylast, &_Where._Myptr[_Count]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = this->_Mylast;
        v31 = 2;
        std::vector<CSpeedHackCheck::CoolDownInfo>::_Ufill(this, v25, v26, &_Tmp);
        v24 = &this->_Mylast[_Count];
        this->_Mylast = v24;
        std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(_Where._Myptr, &v24[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 214748364 - (v10 >> 1) >= v10 )
        v13 = (v10 >> 1) + v10;
      else
        v13 = 0;
      if ( Myfirst )
        v14 = this->_Mylast - Myfirst;
      else
        v14 = 0;
      if ( v13 < _Count + v14 )
        v13 = (unsigned int)std::vector<CSpeedHackCheck::CoolDownInfo>::size(this) + _Count;
      v15 = v13;
      v16 = (BanInfo *)operator new((tagHeader *)(20 * v13));
      v17 = this->_Myfirst;
      _Newvec = v16;
      v31 = 0;
      _Valb = std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(v17, _Where._Myptr, v16);
      std::_Uninit_fill_n<BanInfo *,unsigned int,BanInfo,std::allocator<BanInfo>>(_Valb, _Count, &_Tmp);
      std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(_Where._Myptr, this->_Mylast, &_Valb[_Count]);
      v18 = this->_Myfirst;
      if ( v18 )
        v18 = (BanInfo *)(this->_Mylast - v18);
      v19 = (char *)v18 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v20 = _Newvec;
      v21 = &_Newvec[(_DWORD)v19];
      this->_Myend = &_Newvec[v15];
      this->_Mylast = v21;
      this->_Myfirst = v20;
    }
  }
}

//----- (0046DB80) --------------------------------------------------------
std::vector<BanInfo>::iterator *__thiscall std::vector<BanInfo>::insert(
        std::vector<BanInfo> *this,
        std::vector<BanInfo>::iterator *result,
        std::vector<BanInfo>::iterator _Where,
        const BanInfo *_Val)
{
  BanInfo *Myfirst; // esi
  int v6; // esi
  std::vector<BanInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<BanInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (0046DBF0) --------------------------------------------------------
void __thiscall std::vector<enum Item::ItemType::Type>::vector<enum Item::ItemType::Type>(CBanList *this)
{
  this->m_banList._Myfirst = 0;
  this->m_banList._Mylast = 0;
  this->m_banList._Myend = 0;
}

//----- (0046DC00) --------------------------------------------------------
char __thiscall CBanList::Add(CBanList *this, unsigned int dwBanCID, const char *szCharacterName)
{
  BanInfo *Myfirst; // edi
  unsigned int v5; // edi
  BanInfo *Myptr; // eax
  std::vector<BanInfo>::iterator lbound; // [esp+8h] [ebp-1Ch] BYREF
  BanInfo banInfo; // [esp+Ch] [ebp-18h] BYREF

  Myfirst = this->m_banList._Myfirst;
  if ( Myfirst && (unsigned int)(this->m_banList._Mylast - Myfirst) >= 0x64 )
    return 0;
  std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
    &lbound,
    (std::vector<BanInfo>::iterator)Myfirst,
    (std::vector<BanInfo>::iterator)this->m_banList._Mylast,
    &dwBanCID);
  v5 = dwBanCID;
  banInfo.m_dwCID = dwBanCID;
  strncpy(banInfo.m_szName, szCharacterName, 0x10u);
  Myptr = lbound._Myptr;
  if ( lbound._Myptr == this->m_banList._Mylast || v5 != lbound._Myptr->m_dwCID )
  {
    std::vector<BanInfo>::insert(&this->m_banList, (std::vector<BanInfo>::iterator *)&dwBanCID, lbound, &banInfo);
    return 1;
  }
  else
  {
    lbound._Myptr->m_dwCID = banInfo.m_dwCID;
    *(_DWORD *)Myptr->m_szName = *(_DWORD *)banInfo.m_szName;
    *(_DWORD *)&Myptr->m_szName[4] = *(_DWORD *)&banInfo.m_szName[4];
    *(_DWORD *)&Myptr->m_szName[8] = *(_DWORD *)&banInfo.m_szName[8];
    *(_DWORD *)&Myptr->m_szName[12] = *(_DWORD *)&banInfo.m_szName[12];
    return 1;
  }
}

//----- (0046DCF0) --------------------------------------------------------
char __thiscall CBanList::SerializeIn(CBanList *this, const char *szBuffer_In, unsigned int dwBufferSize_In)
{
  const char *v3; // esi
  bool v5; // cc
  BanInfo *Mylast; // edi
  unsigned int v7; // ebp
  unsigned int v8; // ecx
  int v9; // edx
  int v10; // ecx
  BanInfo *Myfirst; // [esp-18h] [ebp-40h]
  const char *szBufferPastEnd; // [esp+8h] [ebp-20h]
  std::vector<BanInfo>::iterator lbound; // [esp+Ch] [ebp-1Ch] BYREF
  BanInfo banInfo; // [esp+10h] [ebp-18h] BYREF
  const char *szBuffer_Ina; // [esp+2Ch] [ebp+4h]

  v3 = szBuffer_In;
  v5 = szBuffer_In + 20 <= &szBuffer_In[dwBufferSize_In];
  szBufferPastEnd = &szBuffer_In[dwBufferSize_In];
  szBuffer_Ina = szBuffer_In + 20;
  if ( v5 )
  {
    do
    {
      Mylast = this->m_banList._Mylast;
      v7 = *(_DWORD *)v3;
      Myfirst = this->m_banList._Myfirst;
      dwBufferSize_In = *(_DWORD *)v3;
      std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(
        &lbound,
        (std::vector<BanInfo>::iterator)Myfirst,
        (std::vector<BanInfo>::iterator)Mylast,
        &dwBufferSize_In);
      if ( lbound._Myptr == Mylast || v7 != lbound._Myptr->m_dwCID )
      {
        v8 = *(_DWORD *)v3;
        *(_DWORD *)banInfo.m_szName = *((_DWORD *)v3 + 1);
        v9 = *((_DWORD *)v3 + 3);
        banInfo.m_dwCID = v8;
        v10 = *((_DWORD *)v3 + 2);
        *(_DWORD *)&banInfo.m_szName[8] = v9;
        *(_DWORD *)&banInfo.m_szName[4] = v10;
        *(_DWORD *)&banInfo.m_szName[12] = *((_DWORD *)v3 + 4);
        std::vector<BanInfo>::_Insert_n(&this->m_banList, lbound, 1u, &banInfo);
      }
      v3 = szBuffer_Ina;
      szBuffer_Ina += 20;
    }
    while ( szBuffer_Ina <= szBufferPastEnd );
  }
  return 1;
}

//----- (0046DDB0) --------------------------------------------------------
void __thiscall GAMELOG::sLogBase::InitLogBase(
        GAMELOG::sLogBase *this,
        unsigned int dwUID,
        unsigned int dwCID,
        const Position *Pos,
        int time,
        unsigned __int8 cCmd,
        unsigned __int8 cErr)
{
  this->m_dwUID = dwUID;
  this->m_dwCID = dwCID;
  this->m_usXPos = (unsigned __int64)Pos->m_fPointX;
  this->m_usYPos = (unsigned __int64)Pos->m_fPointY;
  this->m_usZPos = (unsigned __int64)Pos->m_fPointZ;
  this->m_time = time;
  this->m_cCmd = cCmd;
  this->m_cErr = cErr;
}

//----- (0046DE10) --------------------------------------------------------
void __thiscall GAMELOG::sCharLoginOut::InitCharLog(
        GAMELOG::sCharLoginOut *this,
        const sockaddr_in *lpSockAddr_In,
        unsigned __int8 cAdmin,
        const unsigned __int16 *usDataSize,
        unsigned __int16 usDepositData,
        unsigned int dwDepositMoney)
{
  if ( lpSockAddr_In )
  {
    this->m_nIP = lpSockAddr_In->sin_addr.S_un.S_addr;
    this->m_usPort = lpSockAddr_In->sin_port;
  }
  else
  {
    this->m_nIP = 0;
    this->m_usPort = 0;
  }
  this->m_cAdmin = cAdmin;
  *(_DWORD *)this->m_usDataSize = *(_DWORD *)usDataSize;
  *(_DWORD *)&this->m_usDataSize[2] = *((_DWORD *)usDataSize + 1);
  *(_DWORD *)&this->m_usDataSize[4] = *((_DWORD *)usDataSize + 2);
  *(_DWORD *)&this->m_usDataSize[6] = *((_DWORD *)usDataSize + 3);
  this->m_usDepositData = usDepositData;
  this->m_dwDepositMoney = dwDepositMoney;
}

//----- (0046DE70) --------------------------------------------------------
void __cdecl GAMELOG::LogCharLoginOut(
        unsigned int dwUID,
        CCharacter *lpCharacter_In,
        const sockaddr_in *lpSockAddr,
        const char *lpCharacterInfo,
        int nTotalSize,
        const unsigned __int16 *usUpdates,
        unsigned __int8 cCMD,
        const unsigned __int16 eError)
{
  CGameLog *Instance; // esi
  GAMELOG::sCharLoginOut *v9; // ebp
  __int16 v10; // di
  const char *v11; // eax
  unsigned int dwDepositSize; // [esp+Ch] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+10h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v9 = (GAMELOG::sCharLoginOut *)CGameLog::ReserveBuffer(Instance, nTotalSize + 16049);
  if ( v9 && lpCharacter_In )
  {
    GAMELOG::sLogBase::InitLogBase(
      v9,
      dwUID,
      lpCharacter_In->m_dwCID,
      &lpCharacter_In->m_CurrentPos,
      Instance->m_time,
      cCMD,
      eError);
    qmemcpy(&v9[1], lpCharacterInfo, nTotalSize);
    dwDepositSize = 16000;
    Item::CDepositContainer::LogUpdate(&lpCharacter_In->m_Deposit, (char *)&v9[1] + nTotalSize, &dwDepositSize);
    v10 = dwDepositSize;
    GAMELOG::sCharLoginOut::InitCharLog(
      v9,
      lpSockAddr,
      lpCharacter_In->m_DBData.m_cAdminLevel != 0,
      usUpdates,
      dwDepositSize,
      lpCharacter_In->m_Deposit.m_dwGold);
    gameLog->m_lpLogBuffer->m_nUsage += (unsigned __int16)(nTotalSize + v10 + 49);
  }
  else
  {
    switch ( cCMD )
    {
      case 1u:
        v11 = (const char *)&unk_4E600C;
        break;
      case 2u:
        v11 = (const char *)&unk_4E6014;
        break;
      case 5u:
        v11 = "DBUpdate";
        break;
      default:
        v11 = (const char *)&unk_4E602C;
        break;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogCharLoginOut",
      aDWorkRylSource_81,
      57,
      aCid0x08x0xP,
      0,
      lpCharacter_In,
      v11,
      cCMD);
  }
}

//----- (0046DFB0) --------------------------------------------------------
void __cdecl GAMELOG::LogCharLevelUp(
        const CCharacter *character,
        unsigned __int16 usIP,
        unsigned __int8 cLevel,
        unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  char *v5; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v5 = CGameLog::ReserveBuffer(Instance, 0x17u);
  if ( v5 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = character->m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    *((_WORD *)v5 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v5 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v5 + 2) = m_time;
    v5[20] = cLevel;
    v5[18] = 6;
    v5[19] = eError;
    *(_WORD *)(v5 + 21) = usIP;
    Instance->m_lpLogBuffer->m_nUsage += 23;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogCharLevelUp",
      aDWorkRylSource_81,
      110,
      aCid0x08x_294,
      character->m_dwCID);
  }
}

//----- (0046E070) --------------------------------------------------------
void __cdecl GAMELOG::LogCharBindPos(const CCharacter *character, unsigned int dwNPCID, unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  char *v4; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v4 = CGameLog::ReserveBuffer(Instance, 0x18u);
  if ( v4 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v4 = character->m_dwUID;
    *((_DWORD *)v4 + 1) = m_dwCID;
    *((_WORD *)v4 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v4 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v4 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v4 + 2) = m_time;
    v4[18] = 7;
    v4[19] = eError;
    *((_DWORD *)v4 + 5) = dwNPCID;
    Instance->m_lpLogBuffer->m_nUsage += 24;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogCharBindPos",
      aDWorkRylSource_81,
      134,
      aCid0x08x_250,
      character->m_dwCID);
  }
}

//----- (0046E120) --------------------------------------------------------
void __cdecl GAMELOG::LogCharDead(
        const CCharacter *character,
        unsigned __int64 dwPrevExp,
        unsigned __int64 dwNextExp,
        const CAggresiveCreature *lpOffencer_In,
        unsigned __int8 cLevel,
        unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  char *v7; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  const CAggresiveCreature *lpOffencer_Ina; // [esp+20h] [ebp+18h]

  Instance = CGameLog::GetInstance();
  v7 = CGameLog::ReserveBuffer(Instance, 0x29u);
  if ( lpOffencer_In )
    lpOffencer_Ina = (const CAggresiveCreature *)lpOffencer_In->m_dwCID;
  else
    lpOffencer_Ina = 0;
  if ( v7 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v7 = character->m_dwUID;
    *((_DWORD *)v7 + 1) = m_dwCID;
    *((_WORD *)v7 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v7 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v7 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v7[19] = eError;
    *(_QWORD *)(v7 + 20) = dwPrevExp;
    *((_DWORD *)v7 + 7) = dwNextExp;
    *((_DWORD *)v7 + 2) = m_time;
    *((_DWORD *)v7 + 9) = lpOffencer_Ina;
    v7[18] = 8;
    *((_DWORD *)v7 + 8) = HIDWORD(dwNextExp);
    v7[40] = cLevel;
    Instance->m_lpLogBuffer->m_nUsage += 41;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogCharDead",
      aDWorkRylSource_81,
      161,
      aCid0x08x_34,
      character->m_dwCID);
  }
}

//----- (0046E210) --------------------------------------------------------
void __cdecl GAMELOG::LogCharRespawn(
        const CCharacter *character,
        unsigned __int64 dwPrevExp,
        unsigned __int64 dwNextExp,
        unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  char *v5; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v5 = CGameLog::ReserveBuffer(Instance, 0x24u);
  if ( v5 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = character->m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    *((_WORD *)v5 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v5 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v5[19] = eError;
    *((_DWORD *)v5 + 5) = dwPrevExp;
    *((_DWORD *)v5 + 2) = m_time;
    *((_DWORD *)v5 + 6) = HIDWORD(dwPrevExp);
    v5[18] = 9;
    *(_QWORD *)(v5 + 28) = dwNextExp;
    Instance->m_lpLogBuffer->m_nUsage += 36;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogCharRespawn",
      aDWorkRylSource_81,
      187,
      aCid0x08x_296,
      character->m_dwCID);
  }
}

//----- (0046E2D0) --------------------------------------------------------
void __cdecl GAMELOG::LogZoneMove(
        const CCharacter *character,
        unsigned __int8 cZone,
        unsigned __int8 cChannel,
        unsigned __int16 usError)
{
  CGameLog *Instance; // ebx
  char *v5; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v5 = CGameLog::ReserveBuffer(Instance, 0x16u);
  if ( v5 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = character->m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    *((_WORD *)v5 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v5 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v5 + 2) = m_time;
    v5[21] = cChannel;
    v5[18] = 57;
    v5[19] = usError;
    v5[20] = cZone;
    Instance->m_lpLogBuffer->m_nUsage += 22;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogZoneMove",
      aDWorkRylSource_81,
      213,
      aCid0x08x_252,
      character->m_dwCID);
  }
}

//----- (0046E390) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualArea::CreateCell(
        VirtualArea::CVirtualArea *this,
        unsigned __int16 wWidth,
        unsigned __int16 wHeight,
        unsigned __int16 wMapIndex)
{
  int v4; // ebp
  int v5; // esi
  int v6; // edi
  char *v8; // eax
  CCell *v9; // eax
  int v11; // edx
  int v12; // edi
  int v13; // eax
  int v14; // ecx
  boost::fast_pool_allocator<std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> *v15; // eax
  int v16; // eax
  int v17; // ebp
  int v18; // edi
  int v19; // esi
  int v20; // esi
  CCell *v21; // [esp+10h] [ebp-14h]
  int v22; // [esp+10h] [ebp-14h]
  int v23; // [esp+10h] [ebp-14h]
  int wMapIndexa; // [esp+30h] [ebp+Ch]

  v4 = wHeight;
  v5 = wWidth;
  v6 = wWidth * wHeight;
  v8 = (char *)operator new[](116 * v6 + 4);
  if ( v8 )
  {
    *(_DWORD *)v8 = v6;
    v21 = (CCell *)(v8 + 4);
    `eh vector constructor iterator'(
      v8 + 4,
      0x74u,
      v6,
      (void (__thiscall *)(void *))CCell::CCell,
      (void (__thiscall *)(void *))CCell::~CCell);
    v9 = v21;
  }
  else
  {
    v9 = 0;
  }
  this->m_CellData = v9;
  if ( v9 )
  {
    v11 = 0;
    if ( wHeight )
    {
      v12 = 0;
      v13 = 116 * wWidth;
      v22 = 0;
      do
      {
        v14 = 0;
        if ( wWidth )
        {
          do
          {
            v15 = &this->m_CellData->m_lstItem._Alnod + v12;
            v15[106] = (boost::fast_pool_allocator<std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>)v14;
            v15[107] = (boost::fast_pool_allocator<std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>)v11;
            *(unsigned __int16 *)((char *)&this->m_CellData->m_wMapIndex + v12) = wMapIndex;
            ++v14;
            v12 += 116;
          }
          while ( v14 < wWidth );
          v13 = 116 * wWidth;
        }
        v4 = wHeight;
        ++v11;
        v12 = v13 + v22;
        v22 += v13;
      }
      while ( v11 < wHeight );
    }
    v16 = 0;
    wMapIndexa = 0;
    if ( v4 > 0 )
    {
      do
      {
        v17 = 0;
        if ( v5 > 0 )
        {
          v18 = v16 * v5;
          v23 = v5 - 1;
          do
          {
            if ( v16 > 0 )
            {
              v19 = v17 + wWidth * (v16 - 1);
              CCell::SetConnectCell(&this->m_CellData[v18], 1u, &this->m_CellData[v19]);
              if ( v17 > 0 )
                CCell::SetConnectCell(&this->m_CellData[v18], 5u, &this->m_CellData[v19 - 1]);
              if ( v17 < v23 )
                CCell::SetConnectCell(&this->m_CellData[v18], 6u, &this->m_CellData[v19 + 1]);
              v16 = wMapIndexa;
            }
            if ( v16 < v23 )
            {
              v20 = v17 + wWidth * (v16 + 1);
              CCell::SetConnectCell(&this->m_CellData[v18], 2u, &this->m_CellData[v20]);
              if ( v17 > 0 )
                CCell::SetConnectCell(&this->m_CellData[v18], 7u, &this->m_CellData[v20 - 1]);
              if ( v17 < v23 )
                CCell::SetConnectCell(&this->m_CellData[v18], 8u, &this->m_CellData[v20 + 1]);
            }
            if ( v17 > 0 )
              CCell::SetConnectCell(&this->m_CellData[v18], 3u, &this->m_CellData[v18 - 1]);
            if ( v17 < v23 )
              CCell::SetConnectCell(&this->m_CellData[v18], 4u, &this->m_CellData[v18 + 1]);
            CCell::SetConnectCell(&this->m_CellData[v18], 0, &this->m_CellData[v18]);
            v5 = wWidth;
            v16 = wMapIndexa;
            ++v17;
            ++v18;
          }
          while ( v17 < wWidth );
        }
        wMapIndexa = ++v16;
      }
      while ( v16 < wHeight );
    }
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualArea::CreateCell",
      aDWorkRylSource_4,
      52,
      (char *)&byte_4DCDF0);
    return 0;
  }
}

//----- (0046E600) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::CreateVirtualMonsterManager(VirtualArea::CVirtualArea *this)
{
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // edi
  CVirtualMonsterMgr *v3; // eax
  CVirtualMonsterMgr *v4; // eax

  m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
  if ( m_pVirtualMonsterMgr )
  {
    CVirtualMonsterMgr::~CVirtualMonsterMgr(this->m_pVirtualMonsterMgr);
    operator delete(m_pVirtualMonsterMgr);
    this->m_pVirtualMonsterMgr = 0;
  }
  v3 = (CVirtualMonsterMgr *)operator new((tagHeader *)0x10);
  if ( v3 )
  {
    CVirtualMonsterMgr::CVirtualMonsterMgr(v3);
    this->m_pVirtualMonsterMgr = v4;
  }
  else
  {
    this->m_pVirtualMonsterMgr = 0;
  }
}
// 46E65A: variable 'v4' is possibly undefined

//----- (0046E690) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::ProcessAllMonster(VirtualArea::CVirtualArea *this)
{
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx

  m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
  if ( m_pVirtualMonsterMgr )
    CVirtualMonsterMgr::ProcessAllMonster(m_pVirtualMonsterMgr);
}

//----- (0046E6A0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::ProcessMonsterRegenHPAndMP(VirtualArea::CVirtualArea *this)
{
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx

  m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
  if ( m_pVirtualMonsterMgr )
    CVirtualMonsterMgr::ProcessMonsterRegenHPAndMP(m_pVirtualMonsterMgr);
}

//----- (0046E6B0) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::ProcessSummonMonsterDead(VirtualArea::CVirtualArea *this)
{
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx

  m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
  if ( m_pVirtualMonsterMgr )
    CVirtualMonsterMgr::ProcessSummonMonsterDead(m_pVirtualMonsterMgr);
}

//----- (0046E6C0) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetStartX(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_wStartX;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetStartX",
    aDWorkRylSource_4,
    217,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 0;
}

//----- (0046E710) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetStartZ(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_wStartZ;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetStartZ",
    aDWorkRylSource_4,
    230,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 0;
}

//----- (0046E760) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetWidth(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_wWidth;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetWidth",
    aDWorkRylSource_4,
    243,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 0;
}

//----- (0046E7B0) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetHeight(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_wHeight;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetHeight",
    aDWorkRylSource_4,
    255,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 0;
}

//----- (0046E800) --------------------------------------------------------
unsigned __int8 __thiscall VirtualArea::CVirtualArea::GetVirtualZone(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_cZone;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetVirtualZone",
    aDWorkRylSource_4,
    267,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 0;
}

//----- (0046E850) --------------------------------------------------------
Position *__thiscall VirtualArea::CVirtualArea::GetStartPosition(
        VirtualArea::CVirtualArea *this,
        Position *result,
        unsigned __int8 cNation)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  Position *v6; // eax
  float m_fPointX; // edx
  int v8; // ecx
  unsigned int m_dwVID; // [esp-4h] [ebp-Ch]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( !VirtualAreaProtoType )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualArea::GetStartPosition",
      aDWorkRylSource_4,
      279,
      aVid0xXVirtuala,
      this->m_dwVID);
LABEL_5:
    v6 = result;
    result->m_fPointX = 0.0;
    result->m_fPointY = 0.0;
    result->m_fPointZ = 0.0;
    return v6;
  }
  if ( cNation >= 2u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualArea::GetStartPosition",
      aDWorkRylSource_4,
      285,
      aVirtualareaSta,
      cNation);
    goto LABEL_5;
  }
  m_fPointX = VirtualAreaProtoType->m_StartPos[cNation].m_fPointX;
  v8 = (int)&VirtualAreaProtoType->m_StartPos[cNation];
  v6 = result;
  result->m_fPointX = m_fPointX;
  result->m_fPointY = *(float *)(v8 + 4);
  result->m_fPointZ = *(float *)(v8 + 8);
  return v6;
}

//----- (0046E8F0) --------------------------------------------------------
Position *__thiscall VirtualArea::CVirtualArea::GetRespawnPosition(
        VirtualArea::CVirtualArea *this,
        Position *result,
        unsigned __int8 cNation,
        unsigned int nIndex)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  Position *v7; // eax
  Position *v8; // ecx
  unsigned int m_dwVID; // [esp-4h] [ebp-Ch]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
  {
    if ( cNation >= 2u || nIndex > 2 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "VirtualArea::CVirtualArea::GetRespawnPosition",
        aDWorkRylSource_4,
        305,
        aVirtualareaRes,
        cNation,
        nIndex,
        3);
      v7 = result;
      result->m_fPointX = 0.0;
      result->m_fPointY = 0.0;
      result->m_fPointZ = 0.0;
    }
    else
    {
      v8 = &VirtualAreaProtoType->m_RespawnPos[0][2 * cNation + cNation + nIndex];
      v7 = result;
      *result = *v8;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "VirtualArea::CVirtualArea::GetRespawnPosition",
      aDWorkRylSource_4,
      298,
      aVid0xXVirtuala,
      this->m_dwVID);
    v7 = result;
    result->m_fPointX = 0.0;
    result->m_fPointY = 0.0;
    result->m_fPointZ = 0.0;
  }
  return v7;
}

//----- (0046E9C0) --------------------------------------------------------
unsigned __int8 __thiscall VirtualArea::CVirtualArea::GetMaxRespawnPos(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_cMaxRespawnPos;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetMaxRespawnPos",
    aDWorkRylSource_4,
    317,
    aVid0xXVirtuala,
    this->m_dwVID);
  return 1;
}

//----- (0046EA10) --------------------------------------------------------
char *__thiscall VirtualArea::CVirtualArea::GetMapTypeName(VirtualArea::CVirtualArea *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  const VirtualArea::ProtoType *VirtualAreaProtoType; // eax
  unsigned int m_dwVID; // [esp-4h] [ebp-8h]

  m_dwVID = this->m_dwVID;
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualAreaProtoType = VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(Instance, m_dwVID);
  if ( VirtualAreaProtoType )
    return VirtualAreaProtoType->m_szMapType;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "VirtualArea::CVirtualArea::GetMapTypeName",
    aDWorkRylSource_4,
    329,
    aVid0xXVirtuala,
    this->m_dwVID);
  return (char *)&unk_4E6520;
}

//----- (0046EA60) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::ProcessDeleteItem(VirtualArea::CVirtualArea *this)
{
  CCell *m_CellData; // edi
  int Height; // esi
  CCell *i; // esi

  if ( this->m_CellData )
  {
    m_CellData = this->m_CellData;
    Height = VirtualArea::CVirtualArea::GetHeight(this);
    for ( i = &m_CellData[VirtualArea::CVirtualArea::GetWidth(this) * Height]; m_CellData != i; ++m_CellData )
      CCell::CheckDeleteItem(m_CellData);
  }
}

//----- (0046EAB0) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualArea::ProcessAllCellPrepareBroadCast(VirtualArea::CVirtualArea *this)
{
  CCell *m_CellData; // edi
  int Height; // esi
  CCell *i; // esi

  if ( !this->m_CellData )
    return 0;
  m_CellData = this->m_CellData;
  Height = VirtualArea::CVirtualArea::GetHeight(this);
  for ( i = &m_CellData[VirtualArea::CVirtualArea::GetWidth(this) * Height]; m_CellData != i; ++m_CellData )
    CCell::PrepareBroadCast(m_CellData);
  return 1;
}

//----- (0046EB00) --------------------------------------------------------
char __thiscall VirtualArea::CVirtualArea::ProcessAllCellBroadCast(VirtualArea::CVirtualArea *this)
{
  CCell *m_CellData; // edi
  int Height; // esi
  CCell *i; // esi

  if ( !this->m_CellData )
    return 0;
  m_CellData = this->m_CellData;
  Height = VirtualArea::CVirtualArea::GetHeight(this);
  for ( i = &m_CellData[VirtualArea::CVirtualArea::GetWidth(this) * Height]; m_CellData != i; ++m_CellData )
    CCell::BroadCast(m_CellData);
  return 1;
}

//----- (0046EB50) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::~CVirtualArea(VirtualArea::CVirtualArea *this)
{
  char *m_CellData; // eax
  char *v3; // edi
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ebp

  this->__vftable = (VirtualArea::CVirtualArea_vtbl *)&VirtualArea::CVirtualArea::`vftable';
  m_CellData = (char *)this->m_CellData;
  if ( m_CellData )
  {
    v3 = m_CellData - 4;
    `eh vector destructor iterator'(
      m_CellData,
      0x74u,
      *((_DWORD *)m_CellData - 1),
      (void (__thiscall *)(void *))CCell::~CCell);
    operator delete[](v3);
    this->m_CellData = 0;
  }
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CharacterList);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_SpectatorList);
  m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
  if ( m_pVirtualMonsterMgr )
  {
    CVirtualMonsterMgr::~CVirtualMonsterMgr(this->m_pVirtualMonsterMgr);
    operator delete(m_pVirtualMonsterMgr);
    this->m_pVirtualMonsterMgr = 0;
  }
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_SpectatorList);
  operator delete(this->m_SpectatorList._Myhead);
  this->m_SpectatorList._Myhead = 0;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CharacterList);
  operator delete(this->m_CharacterList._Myhead);
  this->m_CharacterList._Myhead = 0;
}
// 4E6558: using guessed type void *VirtualArea::CVirtualArea::`vftable';

//----- (0046EC20) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::Enter(
        VirtualArea::CVirtualArea *this,
        CCharacter *lpCharacter,
        unsigned __int8 cMoveType)
{
  return 0;
}

//----- (0046EC30) --------------------------------------------------------
bool __thiscall CParty::ReLogin(VirtualArea::CVirtualArea *this, CCharacter *lpCharacter)
{
  return 0;
}

//----- (0046EC40) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CVirtualArea::AddCharacter(
        VirtualArea::CVirtualArea *this,
        CCharacter *lpSpectator)
{
  return 0;
}

//----- (0046EC50) --------------------------------------------------------
void __thiscall VirtualArea::CVirtualArea::CVirtualArea(
        VirtualArea::CVirtualArea *this,
        const VirtualArea::ProtoType *lpProtoType,
        unsigned __int16 wMapIndex)
{
  std::list<CThread *> *p_m_CharacterList; // edi

  p_m_CharacterList = (std::list<CThread *> *)&this->m_CharacterList;
  this->__vftable = (VirtualArea::CVirtualArea_vtbl *)&VirtualArea::CVirtualArea::`vftable';
  this->m_CellData = 0;
  this->m_dwVID = 0;
  this->m_wMapIndex = wMapIndex;
  this->m_CharacterList._Myhead = (std::_List_nod<CCharacter *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_CharacterList);
  p_m_CharacterList->_Mysize = 0;
  this->m_SpectatorList._Myhead = (std::_List_nod<CCharacter *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_SpectatorList);
  this->m_SpectatorList._Mysize = 0;
  this->m_pVirtualMonsterMgr = 0;
  std::list<IOPCode *>::clear(p_m_CharacterList);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_SpectatorList);
  if ( lpProtoType )
  {
    this->m_dwVID = lpProtoType->m_dwVID;
    VirtualArea::CVirtualArea::CreateCell(this, lpProtoType->m_wWidth, lpProtoType->m_wHeight, wMapIndex);
  }
}
// 4E6558: using guessed type void *VirtualArea::CVirtualArea::`vftable';

//----- (0046ED00) --------------------------------------------------------
VirtualArea::CVirtualArea *__thiscall VirtualArea::CVirtualArea::`scalar deleting destructor'(
        VirtualArea::CVirtualArea *this,
        char a2)
{
  VirtualArea::CVirtualArea::~CVirtualArea(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0046ED20) --------------------------------------------------------
void __thiscall Quest::ExecutingQuest::ExecutingQuest(Quest::ExecutingQuest *this)
{
  this->m_wQuestID = 0;
  this->m_cPhase = 0;
  this->m_QuestNode = 0;
  *(_DWORD *)this->m_cTriggerCount = 0;
  *(_DWORD *)&this->m_cTriggerCount[4] = 0;
  *(_WORD *)&this->m_cTriggerCount[8] = 0;
}

//----- (0046ED40) --------------------------------------------------------
void __thiscall Quest::ExecutingQuest::ExecutingQuest(
        Quest::ExecutingQuest *this,
        unsigned __int16 wQuestID,
        unsigned __int8 cPhase,
        unsigned __int8 *aryTriggerCount)
{
  this->m_wQuestID = wQuestID;
  this->m_cPhase = cPhase;
  memmove(this->m_cTriggerCount, aryTriggerCount, 0xAu);
  this->m_QuestNode = CQuestMgr::GetQuestNode(CSingleton<CQuestMgr>::ms_pSingleton, this->m_wQuestID);
}

//----- (0046ED80) --------------------------------------------------------
void __thiscall CSingleton<CDuelCellManager>::~CSingleton<CDuelCellManager>(CSingleton<CDuelCellManager> *this)
{
  CSingleton<CDuelCellManager>::ms_pSingleton = 0;
}

//----- (0046ED90) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (0046EDF0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0046EE30) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CCell *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CCell *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0046EFE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0046F2A0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (0046F360) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0046F420) --------------------------------------------------------
CCell *__thiscall CDuelCellManager::CreateCell(
        CDuelCellManager *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *dwCID)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v3; // eax
  int v4; // eax
  int v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node _Val; // [esp+8h] [ebp-1Ch] BYREF
  int v8; // [esp+20h] [ebp-4h]

  v3 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)operator new((tagHeader *)0x74);
  _Val._Left = v3;
  v8 = 0;
  if ( v3 )
  {
    CCell::CCell((CCell *)v3);
    v5 = v4;
  }
  else
  {
    v5 = 0;
  }
  v8 = -1;
  if ( !v5 )
    return 0;
  *(_BYTE *)(v5 + 106) = 0;
  *(_BYTE *)(v5 + 107) = 0;
  _Val._Left = dwCID;
  _Val._Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)v5;
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::insert(
    &this->m_CellData,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *)&_Val._Right,
    &_Val);
  return (CCell *)v5;
}
// 46F45D: variable 'v4' is possibly undefined

//----- (0046F4C0) --------------------------------------------------------
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Ptr; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v4; // ebx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator> _Where; // [esp+Ch] [ebp-8h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&_Where,
    _Keyval);
  Ptr = _Where.second._Ptr;
  v4 = _Where.first._Ptr;
  _Keyval = 0;
  std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.first._Ptr,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.second._Ptr,
    (unsigned int *)&_Keyval);
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
    this,
    &_Where.first,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator)v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator)Ptr);
  return _Keyval;
}

//----- (0046F520) --------------------------------------------------------
char __thiscall CDuelCellManager::DestroyCell(CDuelCellManager *this, unsigned int dwCID)
{
  CCell *second; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator itr; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&itr,
    &dwCID);
  if ( itr._Ptr == this->m_CellData._Myhead )
    return 0;
  second = itr._Ptr->_Myval.second;
  if ( second )
  {
    CCell::~CCell(itr._Ptr->_Myval.second);
    operator delete(second);
  }
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
    &this->m_CellData,
    &dwCID);
  return 1;
}

//----- (0046F580) --------------------------------------------------------
void __thiscall CDuelCellManager::~CDuelCellManager(CDuelCellManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *Myhead; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator v3; // [esp-8h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  Myhead = this->m_CellData._Myhead;
  v3._Ptr = Myhead->_Left;
  v5 = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(
    &this->m_CellData,
    &result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator)Myhead);
  operator delete(this->m_CellData._Myhead);
  this->m_CellData._Myhead = 0;
  this->m_CellData._Mysize = 0;
  CSingleton<CDuelCellManager>::ms_pSingleton = 0;
}

//----- (0046F5F0) --------------------------------------------------------
void __thiscall CDuelCellManager::CDuelCellManager(CDuelCellManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *v2; // eax

  CSingleton<CDuelCellManager>::ms_pSingleton = this;
  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->m_CellData._Myhead = v2;
  v2->_Isnil = 1;
  this->m_CellData._Myhead->_Parent = this->m_CellData._Myhead;
  this->m_CellData._Myhead->_Left = this->m_CellData._Myhead;
  this->m_CellData._Myhead->_Right = this->m_CellData._Myhead;
  this->m_CellData._Mysize = 0;
}

//----- (0046F660) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharSkillCommand(
        CSendStream *SendStream,
        unsigned int dwCharID,
        unsigned __int8 Cmd,
        unsigned __int8 cIndex,
        unsigned __int16 usSkillID,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x13);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = dwCharID;
  Buffer[18] = cIndex;
  *((_WORD *)Buffer + 8) = usSkillID;
  return CSendStream::WrapCrypt(SendStream, 0x13u, Cmd, 0, usError);
}

//----- (0046F6B0) --------------------------------------------------------
void __thiscall CCharacter::UpdateQuickSlotSkill(CCharacter *this, SKILLSLOT Slot)
{
  char *p_nSkillLevel; // eax
  int v3; // ecx

  p_nSkillLevel = &this->m_DBData.m_Quick.Slots[0].nSkillLevel;
  v3 = 20;
  do
  {
    if ( *(p_nSkillLevel - 2) == 1 && *(_WORD *)(p_nSkillLevel + 1) == Slot.SKILLINFO.wSkill )
      *(_WORD *)(p_nSkillLevel - 1) = HIWORD(Slot.dwSkillSlot);
    p_nSkillLevel += 5;
    --v3;
  }
  while ( v3 );
}

//----- (0046F6E0) --------------------------------------------------------
void __cdecl std::swap<QUICKSLOT>(QUICKSLOT *_Left, QUICKSLOT *_Right)
{
  int v2; // esi
  char wID_high; // dl

  v2 = *(_DWORD *)&_Left->nType;
  wID_high = HIBYTE(_Left->wID);
  *_Left = *_Right;
  *(_DWORD *)&_Right->nType = v2;
  HIBYTE(_Right->wID) = wID_high;
}

//----- (0046F710) --------------------------------------------------------
bool __thiscall CCharacter::MoveQuickSlot(CCharacter *this, TakeType takeType, unsigned __int16 usSkillID)
{
  Item::ItemPos m_dstPos; // bx
  bool result; // al
  char *v6; // edx
  int v7; // ebp
  Item::CItem *Item; // eax
  char *v9; // ecx
  SKILLSLOT v10; // esi
  char *v11; // edx
  int v12; // esi
  int v13; // esi
  char *v14; // eax
  int takeTypea; // [esp+14h] [ebp+4h]
  __int64 takeTypeb; // [esp+14h] [ebp+4h]
  int takeTypec; // [esp+14h] [ebp+4h]

  m_dstPos = takeType.m_dstPos;
  if ( (*(_WORD *)&takeType.m_dstPos & 0xFFF0u) < 0x140 )
  {
    if ( usSkillID && (*(_BYTE *)&takeType.m_srcPos & 0xF) == 4 && (*(_BYTE *)&takeType.m_dstPos & 0xF) == 3 )
    {
      result = 1;
      v6 = (char *)&this->m_DBData.m_Quick
         + 4 * (*(_WORD *)&takeType.m_dstPos >> 4)
         + (*(_WORD *)&takeType.m_dstPos >> 4);
      HIBYTE(takeTypea) = usSkillID;
      LOWORD(takeTypea) = 1;
      BYTE2(takeTypea) = 1;
      *(_DWORD *)v6 = takeTypea;
      v6[4] = HIBYTE(usSkillID);
    }
    else
    {
      v7 = *(_BYTE *)&takeType.m_srcPos & 0xF;
      switch ( *(_BYTE *)&takeType.m_srcPos & 0xF )
      {
        case 2:
        case 6:
          Item = CCharacter::GetItem(this, *(int *)&takeType.m_srcPos);
          if ( !Item )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCharacter::MoveQuickSlot",
              aDWorkRylSource_74,
              53,
              aCid0x08x_335,
              this->m_dwCID,
              v7,
              *(_WORD *)&takeType.m_srcPos >> 4);
            goto LABEL_14;
          }
          if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 4) != 4 )
            goto LABEL_14;
          *(_WORD *)((char *)&takeTypeb + 3) = Item->m_ItemData.m_usProtoTypeID;
          v9 = (char *)&this->m_DBData.m_Quick
             + 4 * (*(unsigned __int16 *)&m_dstPos >> 4)
             + (*(unsigned __int16 *)&m_dstPos >> 4);
          LOWORD(takeTypeb) = 2;
          BYTE2(takeTypeb) = 0;
          *(_DWORD *)v9 = takeTypeb;
          v9[4] = BYTE4(takeTypeb);
          result = 1;
          break;
        case 3:
          v12 = *(_WORD *)&takeType.m_srcPos >> 4;
          if ( (*(_WORD *)&takeType.m_srcPos & 0xFFF0u) < 0x140 )
          {
            v13 = v12 + 276;
            if ( (*(_BYTE *)&takeType.m_dstPos & 0xF) == 3 )
            {
              std::swap<QUICKSLOT>(
                (QUICKSLOT *)((char *)&this->__vftable + 4 * v13 + v13),
                (QUICKSLOT *)((char *)this->m_DBData.m_Quick.Slots
                            + 4 * (*(_WORD *)&takeType.m_dstPos >> 4)
                            + (*(_WORD *)&takeType.m_dstPos >> 4)));
            }
            else
            {
              v14 = (char *)&this->__vftable + 4 * v13 + v13;
              *(_DWORD *)v14 = 0;
              v14[4] = 0;
            }
            result = 1;
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCharacter::MoveQuickSlot",
              aDWorkRylSource_74,
              87,
              aCid0x08x_54,
              this->m_dwCID,
              v12);
            result = 0;
          }
          break;
        case 4:
          if ( (*(_WORD *)&takeType.m_srcPos & 0xFFF0u) >= 0x140 )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCharacter::MoveQuickSlot",
              aDWorkRylSource_74,
              69,
              aCid0x08x_175,
              this->m_dwCID,
              v7,
              *(_WORD *)&takeType.m_srcPos >> 4);
            goto LABEL_14;
          }
          if ( (*(_BYTE *)&takeType.m_dstPos & 0xF) != 3 )
            goto LABEL_14;
          v10 = this->m_DBData.m_Skill.SSlot[*(_WORD *)&takeType.m_srcPos >> 4];
          *(_WORD *)((char *)&takeTypec + 1) = HIWORD(v10.dwSkillSlot);
          v11 = (char *)&this->m_DBData.m_Quick
              + 4 * (*(unsigned __int16 *)&m_dstPos >> 4)
              + (*(unsigned __int16 *)&m_dstPos >> 4);
          result = 1;
          HIBYTE(takeTypec) = v10.SKILLINFO.wSkill;
          LOBYTE(takeTypec) = 1;
          *(_DWORD *)v11 = takeTypec;
          v11[4] = HIBYTE(v10.SKILLINFO.wSkill);
          break;
        default:
LABEL_14:
          result = 0;
          break;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::MoveQuickSlot",
      aDWorkRylSource_74,
      30,
      aCid0x08x_290,
      this->m_dwCID,
      *(_BYTE *)&takeType.m_dstPos & 0xF,
      *(_WORD *)&takeType.m_dstPos >> 4);
    return 0;
  }
  return result;
}

//----- (0046F9C0) --------------------------------------------------------
void __thiscall CSpell::ClearAffected(CSpell *this)
{
  CAggresiveCreature **m_pAffected; // edi
  char v3; // bl
  CAggresiveCreature **ppPastEnd; // [esp+Ch] [ebp-4h]

  m_pAffected = this->m_pAffected;
  v3 = 0;
  ppPastEnd = &this->m_pAffected[this->m_cAffectedNum];
  if ( this->m_pAffected != ppPastEnd )
  {
    do
    {
      if ( 1 << v3 == ((1 << v3) & this->m_dwActivateStatus) && this->Deactivate(this, *m_pAffected, 0) )
        this->m_dwActivateStatus &= ~(1 << v3);
      CAffectedSpell::Remove(&(*m_pAffected++)->m_SpellMgr.m_AffectedInfo, this);
      ++v3;
    }
    while ( m_pAffected != ppPastEnd );
  }
  this->m_cAffectedNum = 0;
}

//----- (0046FA40) --------------------------------------------------------
Skill::SpellTarget::Type __thiscall CSpell::GetSpellTarget(CSpell *this)
{
  Skill::Type::SkillType m_eSkillType; // eax
  Skill::SpellTarget::Type result; // eax

  m_eSkillType = this->m_eSkillType;
  if ( m_eSkillType == LEAVE_WAIT )
    return 1;
  if ( m_eSkillType != JOIN_WAIT )
    return 0;
  switch ( this->m_ProtoType->m_eTargetType )
  {
    case 1:
    case 3:
    case 5:
    case 7:
      result = COMMON;
      break;
    case 2:
    case 4:
    case 6:
    case 8:
    case 9:
      result = MIDDLE_ADMIN;
      break;
    default:
      return 0;
  }
  return result;
}

//----- (0046FAA0) --------------------------------------------------------
void __thiscall CSpell::Disable(CSpell *this, unsigned int dwOperateFlag)
{
  CAggresiveCreature **m_pAffected; // ebp
  char v4; // bl
  CAggresiveCreature **ppPastEnd; // [esp+8h] [ebp-4h]

  if ( (this->m_cInternalFlags & 1) == 0 )
  {
    m_pAffected = this->m_pAffected;
    v4 = 0;
    ppPastEnd = &this->m_pAffected[this->m_cAffectedNum];
    if ( this->m_pAffected != ppPastEnd )
    {
      do
      {
        if ( 1 << v4 == (this->m_dwActivateStatus & (1 << v4)) && this->Deactivate(this, *m_pAffected, dwOperateFlag) )
          this->m_dwActivateStatus &= ~(1 << v4);
        ++m_pAffected;
        ++v4;
      }
      while ( m_pAffected != ppPastEnd );
    }
    this->m_cInternalFlags |= 1u;
  }
}

//----- (0046FB20) --------------------------------------------------------
void __thiscall CSpell::SendSpellInfo(
        CSpell *this,
        CAggresiveCreature *lpAffected,
        unsigned __int8 cSpellType,
        unsigned __int16 nEnchantLevel,
        bool bOnOff)
{
  CCell *m_lpCell; // esi
  unsigned int m_dwCID; // eax
  PktSpInfo pktSpInfo; // [esp+4h] [ebp-14h] BYREF

  m_lpCell = lpAffected->m_CellPos.m_lpCell;
  if ( m_lpCell )
  {
    m_dwCID = lpAffected->m_dwCID;
    pktSpInfo.m_cSpellType = cSpellType;
    pktSpInfo.m_dwCharID = m_dwCID;
    pktSpInfo.m_nEnchantLevel = nEnchantLevel;
    pktSpInfo.m_bOnOff = bOnOff;
    if ( PacketWrap::WrapCrypt((char *)&pktSpInfo, 0x14u, 0x57u, 0, 0) )
      CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktSpInfo, 0x14u, 0x57u);
  }
}

//----- (0046FB90) --------------------------------------------------------
void __thiscall CSpell::CSpell(
        CSpell *this,
        CSpell::Spell_Info *spell_Info,
        Skill::Type::SkillType eSpellType,
        unsigned int dwEffectFlag)
{
  CAggresiveCreature *m_lpCaster; // eax

  this->__vftable = (CSpell_vtbl *)&CSpell::`vftable';
  this->m_pNextSpell = 0;
  this->m_ProtoType = spell_Info->m_SkillProtoType;
  this->m_eSkillType = eSpellType;
  this->m_pCaster = 0;
  this->m_dwActivateStatus = 0;
  this->m_dwEffectFlag = dwEffectFlag;
  this->m_wSpellID = spell_Info->m_wSpellID;
  this->m_wSpellLevel = spell_Info->m_wSpellLevel;
  this->m_wDurationSec = spell_Info->m_wDurationSec;
  this->m_cSpellType = spell_Info->m_cSpellType;
  this->m_cAffectedNum = 0;
  this->m_cInternalFlags = 0;
  m_lpCaster = spell_Info->m_lpCaster;
  this->m_pCaster = m_lpCaster;
  if ( m_lpCaster )
    CCastingSpell::Add(&m_lpCaster->m_SpellMgr.m_CastingInfo, this);
}
// 4D8E10: using guessed type void *CSpell::`vftable';

//----- (0046FC00) --------------------------------------------------------
void __thiscall CSpell::ClearAll(CSpell *this)
{
  CAggresiveCreature *m_pCaster; // eax

  m_pCaster = this->m_pCaster;
  if ( m_pCaster )
  {
    CCastingSpell::Remove(&m_pCaster->m_SpellMgr.m_CastingInfo, this);
    this->m_pCaster = 0;
  }
  CSpell::ClearAffected(this);
}

//----- (0046FC30) --------------------------------------------------------
void __thiscall CSpell::Destroy(CSpell *this)
{
  CAggresiveCreature *m_pCaster; // eax

  if ( (this->m_cInternalFlags & 2) == 0 )
  {
    m_pCaster = this->m_pCaster;
    if ( m_pCaster )
    {
      CCastingSpell::Remove(&m_pCaster->m_SpellMgr.m_CastingInfo, this);
      this->m_pCaster = 0;
    }
    CSpell::ClearAffected(this);
    this->m_cInternalFlags |= 2u;
  }
}

//----- (0046FC60) --------------------------------------------------------
void __thiscall CSpell::CheckRange(CSpell *this)
{
  CAggresiveCreature *m_pCaster; // eax
  CAggresiveCreature **m_pAffected; // ebx
  double v4; // st6
  double v5; // st5
  double v6; // st3
  int v7; // ebp
  CSpell *Spell; // eax
  CSpell *v9; // edi
  unsigned int v10; // eax
  unsigned __int8 cIndex; // [esp+9h] [ebp-11h]
  CAggresiveCreature *v12; // [esp+Ah] [ebp-10h]
  float fRange; // [esp+Eh] [ebp-Ch]
  CAggresiveCreature **ppPastEnd; // [esp+12h] [ebp-8h]

  m_pCaster = this->m_pCaster;
  v12 = m_pCaster;
  if ( m_pCaster )
  {
    if ( (this->m_cInternalFlags & 1) == 0 )
    {
      m_pAffected = this->m_pAffected;
      fRange = this->m_ProtoType->m_EffectExtent;
      ppPastEnd = &this->m_pAffected[this->m_cAffectedNum];
      cIndex = 0;
      if ( this->m_pAffected != ppPastEnd )
      {
        while ( 1 )
        {
          v6 = m_pCaster->m_CurrentPos.m_fPointZ - (*m_pAffected)->m_CurrentPos.m_fPointZ;
          v5 = m_pCaster->m_CurrentPos.m_fPointX - (*m_pAffected)->m_CurrentPos.m_fPointX;
          v4 = m_pCaster->m_CurrentPos.m_fPointY - (*m_pAffected)->m_CurrentPos.m_fPointY;
          if ( (double)(unsigned int)(unsigned __int64)sqrt(v5 * v5 + v4 * v4 + v6 * v6) > fRange )
          {
            if ( 1 << cIndex == (this->m_dwActivateStatus & (1 << cIndex)) && this->Deactivate(this, *m_pAffected, 0) )
            {
              v10 = ~(1 << cIndex) & this->m_dwActivateStatus;
              goto LABEL_18;
            }
          }
          else
          {
            v7 = 1 << cIndex;
            if ( v7 == (this->m_dwActivateStatus & v7) )
              goto LABEL_19;
            Spell = CAffectedSpell::GetSpell(&(*m_pAffected)->m_SpellMgr.m_AffectedInfo, this->m_wSpellID);
            v9 = Spell;
            if ( !Spell )
            {
              if ( !this->Activate(this, *m_pAffected, 0) )
                goto LABEL_19;
              v10 = v7 | this->m_dwActivateStatus;
              goto LABEL_18;
            }
            if ( Spell->m_wSpellLevel < this->m_wSpellLevel && Spell->Deactivate(Spell, *m_pAffected, 0) )
            {
              v9->m_dwActivateStatus &= ~v7;
              if ( this->Activate(this, *m_pAffected, 0) )
              {
                v10 = v7 | this->m_dwActivateStatus;
LABEL_18:
                this->m_dwActivateStatus = v10;
              }
            }
          }
LABEL_19:
          ++m_pAffected;
          ++cIndex;
          if ( m_pAffected == ppPastEnd )
            return;
          m_pCaster = v12;
        }
      }
    }
  }
}

//----- (0046FDE0) --------------------------------------------------------
void __thiscall CSpell::Enable(CSpell *this, unsigned int dwOperateFlag)
{
  unsigned __int8 m_cInternalFlags; // al
  CAggresiveCreature **m_pAffected; // ebp
  char v5; // bl
  CAggresiveCreature **ppPastEnd; // [esp+8h] [ebp-4h]

  m_cInternalFlags = this->m_cInternalFlags;
  if ( (m_cInternalFlags & 1) != 0 )
  {
    this->m_cInternalFlags = m_cInternalFlags & 0xFE;
    if ( this->m_eSkillType == LEAVE_WAIT )
    {
      CSpell::CheckRange(this);
    }
    else
    {
      m_pAffected = this->m_pAffected;
      v5 = 0;
      ppPastEnd = &this->m_pAffected[this->m_cAffectedNum];
      if ( this->m_pAffected != ppPastEnd )
      {
        do
        {
          if ( 1 << v5 != ((1 << v5) & this->m_dwActivateStatus) && this->Activate(this, *m_pAffected, dwOperateFlag) )
            this->m_dwActivateStatus |= 1 << v5;
          ++m_pAffected;
          ++v5;
        }
        while ( m_pAffected != ppPastEnd );
      }
    }
  }
}

//----- (0046FE60) --------------------------------------------------------
char __thiscall CSpell::AddAffected(CSpell *this, CAggresiveCreature *lpAffected, unsigned __int16 *wError)
{
  unsigned __int8 m_cAffectedNum; // cl
  CAggresiveCreature **m_pAffected; // eax
  CSpell *v6; // ecx

  if ( !lpAffected )
    return 0;
  m_cAffectedNum = this->m_cAffectedNum;
  if ( m_cAffectedNum >= 0xAu )
    return 0;
  m_pAffected = this->m_pAffected;
  v6 = (CSpell *)&this->m_pAffected[m_cAffectedNum];
  if ( this->m_pAffected != (CAggresiveCreature **)v6 )
  {
    while ( *m_pAffected != lpAffected )
    {
      if ( ++m_pAffected == (CAggresiveCreature **)v6 )
        goto LABEL_6;
    }
    return 0;
  }
LABEL_6:
  v6->__vftable = (CSpell_vtbl *)lpAffected;
  if ( CAffectedSpell::Add(&lpAffected->m_SpellMgr.m_AffectedInfo, this, wError) != 1 )
    return 0;
  if ( (this->m_cInternalFlags & 1) == 0 )
  {
    if ( this->m_eSkillType == LEAVE_WAIT )
    {
      CSpell::CheckRange(this);
      ++this->m_cAffectedNum;
      return 1;
    }
    if ( this->Activate(this, lpAffected, 0) )
      this->m_dwActivateStatus |= 1 << this->m_cAffectedNum;
  }
  ++this->m_cAffectedNum;
  return 1;
}

//----- (0046FEF0) --------------------------------------------------------
char __thiscall CSpell::RemoveAffected(CSpell *this, CAggresiveCreature *pRemoved)
{
  CSpell *v3; // edi
  unsigned __int8 *m_pAffected; // ebx
  char v5; // al
  int v8; // ebp
  unsigned int v9; // edx
  char pRemoveda; // [esp+10h] [ebp+4h]

  v3 = (CSpell *)&this->m_pAffected[this->m_cAffectedNum];
  m_pAffected = (unsigned __int8 *)this->m_pAffected;
  v5 = 0;
  if ( this->m_pAffected == (CAggresiveCreature **)v3 )
    return 0;
  while ( *(CAggresiveCreature **)m_pAffected != pRemoved )
  {
    m_pAffected += 4;
    ++v5;
    if ( m_pAffected == (unsigned __int8 *)v3 )
      return 0;
  }
  v8 = 1 << v5;
  pRemoveda = v5;
  if ( 1 << v5 == ((1 << v5) & this->m_dwActivateStatus) && this->Deactivate(this, pRemoved, 0) )
    this->m_dwActivateStatus &= ~v8;
  CAffectedSpell::Remove((CAffectedSpell *)(*(_DWORD *)m_pAffected + 312), this);
  memmove(m_pAffected, m_pAffected + 4, 4 * (((char *)v3 - (char *)(m_pAffected + 4)) >> 2));
  v9 = this->m_dwActivateStatus & (v8 - 1) | (this->m_dwActivateStatus >> 1) & ((unsigned int)(-1 << (pRemoveda + 1)) >> 1);
  --this->m_cAffectedNum;
  this->m_dwActivateStatus = v9;
  return 1;
}

//----- (0046FFB0) --------------------------------------------------------
char __thiscall CSpell::Operate(CSpell *this)
{
  unsigned __int16 m_wDurationSec; // ax
  CAggresiveCreature **m_pAffected; // edi
  CSpell *v5; // ebp
  char v6; // bl

  m_wDurationSec = this->m_wDurationSec;
  if ( m_wDurationSec != 0xFFFF )
  {
    if ( !m_wDurationSec )
      return 0;
    this->m_wDurationSec = m_wDurationSec - 1;
  }
  if ( this->m_eSkillType == LEAVE_WAIT && (this->m_cInternalFlags & 1) == 0 )
    CSpell::CheckRange(this);
  m_pAffected = this->m_pAffected;
  v5 = (CSpell *)&this->m_pAffected[this->m_cAffectedNum];
  v6 = 0;
  if ( this->m_pAffected != (CAggresiveCreature **)v5 )
  {
    do
    {
      if ( 1 << v6 == ((1 << v6) & this->m_dwActivateStatus) )
        (*((void (__thiscall **)(CSpell *, CAggresiveCreature *))&this->~CSpell + 1))(this, *m_pAffected);
      ++m_pAffected;
      ++v6;
    }
    while ( m_pAffected != (CAggresiveCreature **)v5 );
  }
  return 1;
}

//----- (00470020) --------------------------------------------------------
void __thiscall CCastingSpell::ClearChant(CCastingSpell *this)
{
  CSpell **v1; // esi
  CSpell **i; // edi

  v1 = &this->m_pEnchantCasting[this->m_cChantNum + 19];
  for ( i = &this->m_pEnchantCasting[19]; v1 != i; --v1 )
    CSpell::ClearAll(*v1);
}

//----- (00470050) --------------------------------------------------------
void __thiscall CCastingSpell::ClearEnchant(CCastingSpell *this)
{
  CSpell **v1; // esi
  unsigned __int16 *i; // edi

  v1 = &this->m_pEnchantCasting[this->m_cEnchantNum - 1];
  for ( i = &this[-1].m_usInternalFlag; v1 != (CSpell **)i; --v1 )
    CSpell::ClearAll(*v1);
}

//----- (00470080) --------------------------------------------------------
void __thiscall CCastingSpell::EnableChant(CCastingSpell *this, unsigned int dwOperateFlag)
{
  CSpell **m_pChantCasting; // esi
  CCastingSpell *v3; // edi

  m_pChantCasting = this->m_pChantCasting;
  v3 = (CCastingSpell *)&this->m_pChantCasting[this->m_cChantNum];
  if ( this->m_pChantCasting != (CSpell **)v3 )
  {
    do
      CSpell::Enable(*m_pChantCasting++, dwOperateFlag);
    while ( m_pChantCasting != (CSpell **)v3 );
  }
}

//----- (004700B0) --------------------------------------------------------
void __thiscall CCastingSpell::DisableChant(CCastingSpell *this, unsigned int dwOperateFlag)
{
  CSpell **m_pChantCasting; // esi
  CCastingSpell *v3; // edi

  m_pChantCasting = this->m_pChantCasting;
  v3 = (CCastingSpell *)&this->m_pChantCasting[this->m_cChantNum];
  if ( this->m_pChantCasting != (CSpell **)v3 )
  {
    do
      CSpell::Disable(*m_pChantCasting++, dwOperateFlag);
    while ( m_pChantCasting != (CSpell **)v3 );
  }
}

//----- (004700E0) --------------------------------------------------------
char __thiscall CCastingSpell::Add(CCastingSpell *this, CSpell *pSpell)
{
  unsigned __int8 m_cEnchantNum; // cl
  CSpell *v5; // edi
  unsigned __int8 v6; // al
  unsigned __int8 m_cChantNum; // dl
  CSpell *v8; // edi
  unsigned __int8 v9; // al

  if ( pSpell->m_eSkillType == LEAVE_WAIT )
  {
    m_cChantNum = this->m_cChantNum;
    if ( m_cChantNum )
    {
      v8 = this->m_pChantCasting[0];
      memmove(
        (unsigned __int8 *)this->m_pChantCasting,
        (unsigned __int8 *)&this->m_pOwner,
        4 * ((4 * m_cChantNum - 4) >> 2));
      v9 = this->m_cChantNum - 1;
      this->m_cChantNum = v9;
      this->m_pChantCasting[v9] = 0;
      CSpell::ClearAll(v8);
    }
    this->m_pChantCasting[this->m_cChantNum++] = pSpell;
    return 1;
  }
  else if ( pSpell->m_eSkillType == JOIN_WAIT )
  {
    m_cEnchantNum = this->m_cEnchantNum;
    if ( m_cEnchantNum >= 0x14u )
    {
      v5 = this->m_pEnchantCasting[0];
      memmove(
        (unsigned __int8 *)this,
        (unsigned __int8 *)&this->m_pEnchantCasting[1],
        4 * ((4 * m_cEnchantNum - 4) >> 2));
      v6 = this->m_cEnchantNum - 1;
      this->m_cEnchantNum = v6;
      this->m_pEnchantCasting[v6] = 0;
      CSpell::ClearAll(v5);
    }
    this->m_pEnchantCasting[this->m_cEnchantNum++] = pSpell;
    return 1;
  }
  else
  {
    return 0;
  }
}

//----- (004701C0) --------------------------------------------------------
char __thiscall CCastingSpell::Remove(CCastingSpell *this, CSpell *pSpell)
{
  CCastingSpell *v2; // esi
  CCastingSpell *v3; // eax
  unsigned __int8 v5; // al
  unsigned __int8 *m_pChantCasting; // ecx
  int v7; // eax
  unsigned __int8 v8; // al

  v2 = this;
  if ( pSpell->m_eSkillType != LEAVE_WAIT )
  {
    if ( pSpell->m_eSkillType == JOIN_WAIT )
    {
      v3 = (CCastingSpell *)((char *)this + 4 * this->m_cEnchantNum);
      if ( this != v3 )
      {
        while ( this->m_pEnchantCasting[0] != pSpell )
        {
          this = (CCastingSpell *)((char *)this + 4);
          if ( this == v3 )
            return 0;
        }
        memmove(
          (unsigned __int8 *)this,
          (unsigned __int8 *)&this->m_pEnchantCasting[1],
          4 * (((char *)v3 - (char *)&this->m_pEnchantCasting[1]) >> 2));
        v5 = v2->m_cEnchantNum - 1;
        v2->m_cEnchantNum = v5;
        v2->m_pEnchantCasting[v5] = 0;
        return 1;
      }
    }
    return 0;
  }
  m_pChantCasting = (unsigned __int8 *)this->m_pChantCasting;
  v7 = (int)&v2->m_pChantCasting[v2->m_cChantNum];
  if ( v2->m_pChantCasting == (CSpell **)v7 )
    return 0;
  while ( *(CSpell **)m_pChantCasting != pSpell )
  {
    m_pChantCasting += 4;
    if ( m_pChantCasting == (unsigned __int8 *)v7 )
      return 0;
  }
  memmove(m_pChantCasting, m_pChantCasting + 4, 4 * ((v7 - (int)(m_pChantCasting + 4)) >> 2));
  v8 = v2->m_cChantNum - 1;
  v2->m_cChantNum = v8;
  v2->m_pChantCasting[v8] = 0;
  return 1;
}

//----- (00470270) --------------------------------------------------------
void __thiscall CAffectedSpell::ClearChant(CAffectedSpell *this)
{
  CSpell **v2; // esi
  unsigned __int8 *i; // ebx

  v2 = &this->m_pChant[this->m_cChantNum - 1];
  for ( i = &this[-1].m_cInternalFlag; v2 != (CSpell **)i; --v2 )
    CSpell::RemoveAffected(*v2, this->m_pOwner);
}

//----- (004702A0) --------------------------------------------------------
void __thiscall CAffectedSpell::ClearEnchant(CAffectedSpell *this)
{
  CSpell **v2; // esi
  CSpell **i; // ebx

  v2 = &this->m_pChant[this->m_cEnchantNum + 9];
  for ( i = &this->m_pChant[9]; v2 != i; --v2 )
    CSpell::RemoveAffected(*v2, this->m_pOwner);
}

//----- (004702D0) --------------------------------------------------------
void __thiscall CAffectedSpell::EnableChant(CAffectedSpell *this, unsigned int dwOperateFlag)
{
  CSpell **v2; // esi
  unsigned __int8 *i; // edi

  v2 = &this->m_pChant[this->m_cChantNum - 1];
  for ( i = &this[-1].m_cInternalFlag; v2 != (CSpell **)i; --v2 )
    CSpell::Enable(*v2, dwOperateFlag);
}

//----- (00470300) --------------------------------------------------------
void __thiscall CAffectedSpell::EnableEnchant(CAffectedSpell *this, unsigned int dwOperateFlag)
{
  CSpell **v2; // esi
  CSpell **i; // edi

  v2 = &this->m_pChant[this->m_cEnchantNum + 9];
  for ( i = &this->m_pChant[9]; v2 != i; --v2 )
    CSpell::Enable(*v2, dwOperateFlag);
}

//----- (00470330) --------------------------------------------------------
void __thiscall CAffectedSpell::DisableChant(CAffectedSpell *this, unsigned int dwOperateFlag)
{
  CSpell **v2; // esi
  unsigned __int8 *i; // edi

  v2 = &this->m_pChant[this->m_cChantNum - 1];
  for ( i = &this[-1].m_cInternalFlag; v2 != (CSpell **)i; --v2 )
    CSpell::Disable(*v2, dwOperateFlag);
}

//----- (00470360) --------------------------------------------------------
void __thiscall CAffectedSpell::DisableEnchant(CAffectedSpell *this, unsigned int dwOperateFlag)
{
  CSpell **v2; // esi
  CSpell **i; // edi

  v2 = &this->m_pChant[this->m_cEnchantNum + 9];
  for ( i = &this->m_pChant[9]; v2 != i; --v2 )
    CSpell::Disable(*v2, dwOperateFlag);
}

//----- (00470390) --------------------------------------------------------
void __thiscall CAffectedSpell::ApplyPartyChant(CAffectedSpell *this, CAggresiveCreature *pAffected)
{
  CAffectedSpell *v2; // edi
  CAffectedSpell *i; // esi
  CSpell *v4; // ecx
  int wError; // [esp+8h] [ebp-4h] BYREF

  v2 = (CAffectedSpell *)((char *)this + 4 * this->m_cChantNum);
  wError = 0;
  for ( i = this; i != v2; i = (CAffectedSpell *)((char *)i + 4) )
  {
    v4 = i->m_pChant[0];
    if ( i->m_pChant[0] )
    {
      if ( v4->m_ProtoType->m_eTargetType == 8 )
        CSpell::AddAffected(v4, pAffected, (unsigned __int16 *)&wError);
    }
  }
}

//----- (004703E0) --------------------------------------------------------
void __thiscall CAffectedSpell::RemoveChantByCaster(CAffectedSpell *this, CAggresiveCreature *pCaster)
{
  CAffectedSpell *v3; // ebx
  CAffectedSpell *i; // esi
  CSpell *v5; // ecx

  v3 = (CAffectedSpell *)((char *)this + 4 * this->m_cChantNum);
  for ( i = this; i != v3; i = (CAffectedSpell *)((char *)i + 4) )
  {
    v5 = i->m_pChant[0];
    if ( i->m_pChant[0] )
    {
      if ( pCaster == v5->m_pCaster )
        CSpell::RemoveAffected(v5, this->m_pOwner);
    }
  }
}

//----- (00470420) --------------------------------------------------------
char __thiscall CAffectedSpell::RemoveEnchantBySpellType(CAffectedSpell *this, unsigned int dwStatusFlag)
{
  CSpell **m_pEnchant; // eax
  CAffectedSpell *v3; // esi

  m_pEnchant = this->m_pEnchant;
  v3 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
  if ( this->m_pEnchant == (CSpell **)v3 )
    return 0;
  while ( !*m_pEnchant || dwStatusFlag != (dwStatusFlag & (*m_pEnchant)->m_dwEffectFlag) )
  {
    if ( ++m_pEnchant == (CSpell **)v3 )
      return 0;
  }
  CSpell::RemoveAffected(*m_pEnchant, this->m_pOwner);
  return 1;
}

//----- (00470470) --------------------------------------------------------
char __thiscall CAffectedSpell::IsSpellOfEnemyCharacter(CAffectedSpell *this)
{
  CSpell **m_pEnchant; // esi
  CAffectedSpell *v3; // ebx

  m_pEnchant = this->m_pEnchant;
  v3 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
  if ( this->m_pEnchant == (CSpell **)v3 )
    return 0;
  while ( ((*m_pEnchant)->m_pCaster->m_dwCID & 0x80000000) != 0
       || this->m_pOwner->IsEnemy(this->m_pOwner, (*m_pEnchant)->m_pCaster) != HOSTILITY )
  {
    if ( ++m_pEnchant == (CSpell **)v3 )
      return 0;
  }
  return 1;
}

//----- (004704C0) --------------------------------------------------------
char __thiscall CAffectedSpell::IsSpellThisTargetType(CAffectedSpell *this, Skill::SpellTarget::Type eTargetType)
{
  CSpell **m_pEnchant; // esi
  CAffectedSpell *v3; // edi

  m_pEnchant = this->m_pEnchant;
  v3 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
  if ( this->m_pEnchant == (CSpell **)v3 )
    return 0;
  while ( CSpell::GetSpellTarget(*m_pEnchant) != eTargetType )
  {
    if ( ++m_pEnchant == (CSpell **)v3 )
      return 0;
  }
  return 1;
}

//----- (00470500) --------------------------------------------------------
unsigned __int8 __thiscall CAffectedSpell::Disenchant(
        CAffectedSpell *this,
        Skill::SpellType::Type eSpellType,
        Skill::SpellTarget::Type eTargetType,
        Skill::Disenchant::Type eDisenchantType,
        unsigned __int16 usSkillLevel,
        unsigned __int8 cNum)
{
  CSpell *v6; // edi
  CSpell **m_pEnchant; // esi
  CAffectedSpell *v9; // ebp
  CSpell *v10; // ecx
  unsigned __int16 m_wDurationSec; // ax
  unsigned __int16 m_wSpellLevel; // ax
  unsigned __int8 cResult; // [esp+12h] [ebp-Ah]
  unsigned __int8 nIndex; // [esp+13h] [ebp-9h]
  unsigned __int16 usMaxDuration; // [esp+14h] [ebp-8h]
  unsigned __int16 usMaxLevel; // [esp+18h] [ebp-4h]

  v6 = 0;
  usMaxLevel = 0;
  usMaxDuration = 0;
  cResult = 0;
  if ( cNum == 0xFF )
    cNum = this->m_cEnchantNum;
  nIndex = 0;
  if ( !cNum )
    return cResult;
  do
  {
    m_pEnchant = this->m_pEnchant;
    v9 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
    if ( this->m_pEnchant == (CSpell **)v9 )
      return cResult;
    while ( 1 )
    {
      if ( eTargetType != LEAVE_WAIT && CSpell::GetSpellTarget(*m_pEnchant) != eTargetType
        || eSpellType && (*m_pEnchant)->m_cSpellType != eSpellType )
      {
        goto LABEL_19;
      }
      if ( eDisenchantType == NONE )
        break;
      if ( eDisenchantType == MASTER )
      {
        v10 = *m_pEnchant;
        m_wSpellLevel = (*m_pEnchant)->m_wSpellLevel;
        if ( m_wSpellLevel >= usSkillLevel || m_wSpellLevel <= usMaxLevel )
          goto LABEL_19;
        usMaxLevel = (*m_pEnchant)->m_wSpellLevel;
      }
      else
      {
        if ( eDisenchantType != MIDDLE_ADMIN )
          goto LABEL_19;
        v10 = *m_pEnchant;
        m_wDurationSec = (*m_pEnchant)->m_wDurationSec;
        if ( m_wDurationSec == 0xFFFF || m_wDurationSec <= usMaxDuration )
          goto LABEL_19;
        usMaxDuration = (*m_pEnchant)->m_wDurationSec;
      }
      v6 = v10;
LABEL_19:
      if ( ++m_pEnchant == (CSpell **)v9 )
        goto LABEL_22;
    }
    v6 = *m_pEnchant;
LABEL_22:
    if ( !v6 )
      break;
    CSpell::RemoveAffected(v6, this->m_pOwner);
    v6 = 0;
    ++cResult;
    ++nIndex;
  }
  while ( nIndex < cNum );
  return cResult;
}

//----- (00470610) --------------------------------------------------------
bool __thiscall CSpell::IsActivate(CSpell *this, CAggresiveCreature *lpAffected)
{
  CAggresiveCreature **v3; // edx
  CAggresiveCreature **m_pAffected; // eax
  char v5; // cl

  v3 = &this->m_pAffected[this->m_cAffectedNum];
  m_pAffected = this->m_pAffected;
  v5 = 0;
  if ( m_pAffected == v3 )
    return 0;
  while ( *m_pAffected != lpAffected )
  {
    ++m_pAffected;
    ++v5;
    if ( m_pAffected == v3 )
      return 0;
  }
  return 1 << v5 == ((1 << v5) & this->m_dwActivateStatus);
}

//----- (00470660) --------------------------------------------------------
CSpell *__thiscall CAffectedSpell::GetSpell(CAffectedSpell *this, unsigned __int16 usSpellID)
{
  CAffectedSpell *v3; // ebx
  CAffectedSpell *v4; // edi
  CSpell *v5; // ecx
  CSpell **m_pEnchant; // eax
  CAffectedSpell *v7; // edx

  v3 = (CAffectedSpell *)((char *)this + 4 * this->m_cChantNum);
  v4 = this;
  if ( this == v3 )
  {
LABEL_6:
    m_pEnchant = this->m_pEnchant;
    v7 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
    if ( this->m_pEnchant == (CSpell **)v7 )
    {
      return 0;
    }
    else
    {
      while ( !*m_pEnchant || (*m_pEnchant)->m_wSpellID != usSpellID )
      {
        if ( ++m_pEnchant == (CSpell **)v7 )
          return 0;
      }
      return *m_pEnchant;
    }
  }
  else
  {
    while ( 1 )
    {
      v5 = v4->m_pChant[0];
      if ( v4->m_pChant[0] )
      {
        if ( v5->m_wSpellID == usSpellID && CSpell::IsActivate(v5, this->m_pOwner) )
          return v4->m_pChant[0];
      }
      v4 = (CAffectedSpell *)((char *)v4 + 4);
      if ( v4 == v3 )
        goto LABEL_6;
    }
  }
}

//----- (004706E0) --------------------------------------------------------
char __thiscall CAffectedSpell::RemoveOverlappedSpell(CAffectedSpell *this, CSpell *pSpell)
{
  unsigned __int16 m_wSpellID; // bp
  CSpell **m_pEnchant; // esi
  CAffectedSpell *v4; // ebx
  CSpell *v5; // ecx
  unsigned __int16 m_wSpellLevel; // ax
  unsigned __int16 v7; // dx

  m_wSpellID = pSpell->m_wSpellID;
  if ( pSpell->m_eSkillType == JOIN_WAIT )
  {
    m_pEnchant = this->m_pEnchant;
    v4 = (CAffectedSpell *)&this->m_pEnchant[this->m_cEnchantNum];
    if ( this->m_pEnchant == (CSpell **)v4 )
      return 1;
    while ( 1 )
    {
      v5 = *m_pEnchant;
      if ( *m_pEnchant && m_wSpellID == v5->m_wSpellID )
      {
        m_wSpellLevel = v5->m_wSpellLevel;
        v7 = pSpell->m_wSpellLevel;
        if ( v7 <= m_wSpellLevel && (v7 != m_wSpellLevel || pSpell->m_wDurationSec < v5->m_wDurationSec) )
          return 0;
        CSpell::ClearAll(v5);
      }
      if ( ++m_pEnchant == (CSpell **)v4 )
        return 1;
    }
  }
  return 1;
}

//----- (00470750) --------------------------------------------------------
char __thiscall CAffectedSpell::Add(CAffectedSpell *this, CSpell *pSpell, unsigned __int16 *wError)
{
  unsigned __int8 m_cEnchantNum; // al
  unsigned __int8 m_cChantNum; // al

  if ( CAffectedSpell::RemoveOverlappedSpell(this, pSpell) == 1 )
  {
    if ( pSpell->m_eSkillType == LEAVE_WAIT )
    {
      m_cChantNum = this->m_cChantNum;
      if ( m_cChantNum < 0xAu )
      {
        this->m_pChant[m_cChantNum] = pSpell;
        ++this->m_cChantNum;
        return 1;
      }
    }
    else if ( pSpell->m_eSkillType == JOIN_WAIT )
    {
      if ( CSpell::GetSpellTarget(pSpell) == COMMON )
        CAffectedSpell::RemoveEnchantBySpellType(this, 0x10000u);
      if ( pSpell->m_wSpellID == 17 && CAffectedSpell::IsSpellThisTargetType(this, COMMON) == 1 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CAffectedSpell::Add",
          aDWorkRylSource_60,
          37,
          aCid0x08x_327,
          this->m_pOwner->m_dwCID);
        *wError = 2;
        return 0;
      }
      m_cEnchantNum = this->m_cEnchantNum;
      if ( m_cEnchantNum < 0xAu )
      {
        this->m_pEnchant[m_cEnchantNum] = pSpell;
        ++this->m_cEnchantNum;
        return 1;
      }
    }
  }
  *wError = 1;
  return 0;
}



//----- (00470820) --------------------------------------------------------
char __thiscall CAffectedSpell::Remove(CAffectedSpell *this, CSpell *pSpell)
{
  CAffectedSpell *v2; // esi
  unsigned __int8 *m_pEnchant; // ecx
  int v4; // eax
  unsigned __int8 v6; // al
  CAffectedSpell *v7; // eax
  unsigned __int8 v8; // al

  v2 = this;
  if ( pSpell->m_eSkillType != LEAVE_WAIT )
  {
    if ( pSpell->m_eSkillType == JOIN_WAIT )
    {
      m_pEnchant = (unsigned __int8 *)this->m_pEnchant;
      v4 = (int)&v2->m_pEnchant[v2->m_cEnchantNum];
      if ( v2->m_pEnchant != (CSpell **)v4 )
      {
        while ( pSpell != *(CSpell **)m_pEnchant )
        {
          m_pEnchant += 4;
          if ( m_pEnchant == (unsigned __int8 *)v4 )
            return 0;
        }
        memmove(m_pEnchant, m_pEnchant + 4, 4 * ((v4 - (int)(m_pEnchant + 4)) >> 2));
        v6 = v2->m_cEnchantNum - 1;
        v2->m_cEnchantNum = v6;
        v2->m_pEnchant[v6] = 0;
        return 1;
      }
    }
    return 0;
  }
  v7 = (CAffectedSpell *)((char *)this + 4 * this->m_cChantNum);
  if ( this == v7 )
    return 0;
  while ( pSpell != this->m_pChant[0] )
  {
    this = (CAffectedSpell *)((char *)this + 4);
    if ( this == v7 )
      return 0;
  }
  memmove(
    (unsigned __int8 *)this,
    (unsigned __int8 *)&this->m_pChant[1],
    4 * (((char *)v7 - (char *)&this->m_pChant[1]) >> 2));
  v8 = v2->m_cChantNum - 1;
  v2->m_cChantNum = v8;
  v2->m_pChant[v8] = 0;
  return 1;
}

//----- (004708D0) --------------------------------------------------------
void __thiscall CChantSpell::Operate(CChantSpell *this, CAggresiveCreature *lpAffected)
{
  char v2; // dl
  __int16 m_nConsumeMPAmount; // si
  unsigned __int16 m_nNowMP; // ax
  CCastingSpell *p_m_CastingInfo; // ecx

  if ( lpAffected == this->m_pCaster )
  {
    v2 = this->m_cOperateTurn + 1;
    this->m_cOperateTurn = v2;
    if ( v2 == 255 )
      this->m_cOperateTurn = 0;
    if ( !(this->m_cOperateTurn % 5) )
    {
      m_nConsumeMPAmount = this->m_nConsumeMPAmount;
      m_nNowMP = lpAffected->m_CreatureStatus.m_nNowMP;
      p_m_CastingInfo = &lpAffected->m_SpellMgr.m_CastingInfo;
      if ( m_nConsumeMPAmount <= (int)m_nNowMP )
      {
        lpAffected->m_CreatureStatus.m_nNowMP = m_nNowMP - m_nConsumeMPAmount;
        CCastingSpell::EnableChant(p_m_CastingInfo, 0);
      }
      else
      {
        lpAffected->m_CreatureStatus.m_nNowMP = 0;
        CCastingSpell::DisableChant(p_m_CastingInfo, 0);
      }
    }
  }
}

//----- (00470960) --------------------------------------------------------
char __thiscall CBattleSongSpell::Activate(CBattleSongSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 1u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004709B0) --------------------------------------------------------
char __thiscall CBattleSongSpell::Deactivate(
        CBattleSongSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 1u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470A00) --------------------------------------------------------
void __thiscall CMaintenanceChantSpell::Operate(CMaintenanceChantSpell *this, CAggresiveCreature *lpAffected)
{
  if ( !(this->m_cOperateTurn % 2) )
    lpAffected->RegenHPAndMP(lpAffected, this->m_wSpellLevel, 0, 0);
  CChantSpell::Operate(this, lpAffected);
}

//----- (00470A40) --------------------------------------------------------
char __thiscall CMaintenanceChantSpell::Activate(
        CMaintenanceChantSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 2u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470AA0) --------------------------------------------------------
char __thiscall CMaintenanceChantSpell::Deactivate(
        CMaintenanceChantSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 2u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470B10) --------------------------------------------------------
void __thiscall CAccelerationChantSpell::Operate(CAccelerationChantSpell *this, CAggresiveCreature *lpAffected)
{
  if ( !(this->m_cOperateTurn % 2) )
    lpAffected->RegenHPAndMP(lpAffected, 0, this->m_wSpellLevel, 0);
  CChantSpell::Operate(this, lpAffected);
}

//----- (00470B50) --------------------------------------------------------
char __thiscall CAccelerationChantSpell::Activate(
        CAccelerationChantSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 3u, this->m_wSpellLevel, 1);
  return 1;
}


//----- (00470BD0) --------------------------------------------------------
char __thiscall CAccelerationChantSpell::Deactivate(
        CAccelerationChantSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 3u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470C60) --------------------------------------------------------
void __thiscall CLifeAuraSpell::Operate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected)
{
  if ( !(this->m_cOperateTurn % 2) )
    lpAffected->RegenHPAndMP(lpAffected, 2 * this->m_wSpellLevel, 2 * this->m_wSpellLevel, 0);
  CChantSpell::Operate(this, lpAffected);
}

//----- (00470CA0) --------------------------------------------------------
char __thiscall CLifeAuraSpell::Activate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 4u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470CE0) --------------------------------------------------------
char __thiscall CLifeAuraSpell::Deactivate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 4u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470D20) --------------------------------------------------------
char __thiscall CDefencePotionSpell::Activate(
        CDefencePotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 7u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470D70) --------------------------------------------------------
char __thiscall CDefencePotionSpell::Deactivate(
        CDefencePotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 7u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470DC0) --------------------------------------------------------
char __thiscall CDisenchantPotionSpell::Activate(
        CDisenchantPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 8u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470E00) --------------------------------------------------------
char __thiscall CDisenchantPotionSpell::Deactivate(
        CDisenchantPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 8u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470E40) --------------------------------------------------------
char __thiscall CMagicPotionSpell::Activate(
        CMagicPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 9u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470E80) --------------------------------------------------------
char __thiscall CMagicPotionSpell::Deactivate(
        CMagicPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 9u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470EC0) --------------------------------------------------------
char __thiscall CLightningPotionSpell::Activate(
        CLightningPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xAu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470F10) --------------------------------------------------------
char __thiscall CLightningPotionSpell::Deactivate(
        CLightningPotionSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xAu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00470F70) --------------------------------------------------------
void __thiscall CRegenerationSpell::Operate(CRegenerationSpell *this, CAggresiveCreature *lpAffected)
{
  int m_wSpellLevel; // ecx

  if ( (this->m_wDurationSec & 0x80000001) == 0 )
  {
    m_wSpellLevel = this->m_wSpellLevel;
    LOWORD(m_wSpellLevel) = 2 * m_wSpellLevel;
    lpAffected->RegenHPAndMP(lpAffected, m_wSpellLevel, 0, 0);
  }
}

//----- (00470FA0) --------------------------------------------------------
char __thiscall CRegenerationSpell::Activate(
        CRegenerationSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xBu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00470FE0) --------------------------------------------------------
char __thiscall CRegenerationSpell::Deactivate(
        CRegenerationSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xBu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471020) --------------------------------------------------------
char __thiscall CStrengthSpell::Activate(CStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += 6 * this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += 6 * this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xCu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471070) --------------------------------------------------------
char __thiscall CStrengthSpell::Deactivate(CStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += -6 * this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += -6 * this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xCu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004710C0) --------------------------------------------------------
char __thiscall CBlazeSpell::Activate(CBlazeSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance += 40;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += 10;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xFu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471120) --------------------------------------------------------
char __thiscall CBlazeSpell::Deactivate(CBlazeSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance -= 40;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= 10;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xFu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471180) --------------------------------------------------------
char __thiscall CChargingSpell::Activate(CChargingSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance += 30;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x10u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004711D0) --------------------------------------------------------
char __thiscall CChargingSpell::Deactivate(CChargingSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= 10;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance -= 30;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x10u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471220) --------------------------------------------------------
char __thiscall CStealthSpell::Activate(CStealthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x11u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471260) --------------------------------------------------------
char __thiscall CStealthSpell::Deactivate(CStealthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x11u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004712A0) --------------------------------------------------------
char __thiscall CManaShellSpell::Activate(CManaShellSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x12u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004712E0) --------------------------------------------------------
char __thiscall CManaShellSpell::Deactivate(CManaShellSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x12u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471320) --------------------------------------------------------
char __thiscall CEncourageSpell::Activate(CEncourageSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x13u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004713D0) --------------------------------------------------------
char __thiscall CEncourageSpell::Deactivate(CEncourageSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x13u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471480) --------------------------------------------------------
char __thiscall CEnchantWeaponSpell::Activate(
        CEnchantWeaponSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x14u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004714D0) --------------------------------------------------------
char __thiscall CEnchantWeaponSpell::Deactivate(
        CEnchantWeaponSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x14u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471520) --------------------------------------------------------
char __thiscall CBrightArmorSpell::Activate(
        CBrightArmorSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x15u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471560) --------------------------------------------------------
char __thiscall CBrightArmorSpell::Deactivate(
        CBrightArmorSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x15u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004715B0) --------------------------------------------------------
char __thiscall CHardenSkinSpell::Activate(CHardenSkinSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x16u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004715F0) --------------------------------------------------------
char __thiscall CHardenSkinSpell::Deactivate(
        CHardenSkinSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x16u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471640) --------------------------------------------------------
char __thiscall CFlexibilitySpell::Activate(
        CFlexibilitySpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x17u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471680) --------------------------------------------------------
char __thiscall CFlexibilitySpell::Deactivate(
        CFlexibilitySpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x17u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004716C0) --------------------------------------------------------
char __thiscall CGuardSpell::Activate(CGuardSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  this->m_nOriginalBlocking = lpAffected->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage = this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x18u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471710) --------------------------------------------------------
char __thiscall CGuardSpell::Deactivate(CGuardSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage = this->m_nOriginalBlocking;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMagicResistance -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x18u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471760) --------------------------------------------------------
char __thiscall CSlowSpell::Activate(CSlowSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xDu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004717A0) --------------------------------------------------------
char __thiscall CSlowSpell::Deactivate(CSlowSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xDu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004717E0) --------------------------------------------------------
char __thiscall CArmorBrokenSpell::Activate(
        CArmorBrokenSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xEu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471820) --------------------------------------------------------
char __thiscall CArmorBrokenSpell::Deactivate(
        CArmorBrokenSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += this->m_wSpellLevel;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0xEu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471870) --------------------------------------------------------
char __thiscall CHoldSpell::Activate(CHoldSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x19u, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004718B0) --------------------------------------------------------
char __thiscall CHoldSpell::Deactivate(CHoldSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x19u, this->m_wSpellLevel, 0);
  return 1;
}

//----- (004718F0) --------------------------------------------------------
char __thiscall CStunSpell::Activate(CStunSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  CCastingSpell::ClearChant(&lpAffected->m_SpellMgr.m_CastingInfo);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Au, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471940) --------------------------------------------------------
char __thiscall CStunSpell::Deactivate(CStunSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Au, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471980) --------------------------------------------------------
char __thiscall CFrozenSpell::Activate(CFrozenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Bu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (004719C0) --------------------------------------------------------
char __thiscall CFrozenSpell::Deactivate(CFrozenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Bu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471A00) --------------------------------------------------------
void __thiscall CPoisonedSpell::Operate(CPoisonedSpell *this, CAggresiveCreature *lpAffected)
{
  unsigned __int16 v3; // cx
  bool v4; // zf
  int m_nNowHP; // eax
  int v6; // ebp
  int v7; // eax
  unsigned __int16 v8; // ax
  unsigned __int16 m_nNowMP; // cx
  unsigned __int16 m_nMaxMP; // ax
  unsigned __int16 m_nMaxHP; // dx
  CAggresiveCreature *m_pCaster; // ecx
  unsigned int m_dwCID; // edx
  CCell *m_lpCell; // ecx
  CCharacter *FirstCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  CAggresiveCreature *v17; // edi
  CCell *v18; // ecx
  CSendStream *v19; // eax
  AtType attackType; // [esp+4h] [ebp-BCh] BYREF
  AtType v21; // [esp+8h] [ebp-B8h]
  char szBuffer[176]; // [esp+Ch] [ebp-B4h] BYREF

  v3 = 7 * this->m_wSpellLevel;
  v4 = (this->m_wDurationSec & 0x80000001) == 0;
  attackType = (AtType)v3;
  if ( v4 )
  {
    m_nNowHP = lpAffected->m_CreatureStatus.m_nNowHP;
    v6 = (__int16)v3;
    if ( m_nNowHP >= (__int16)v3 )
      LOWORD(m_nNowHP) = v3;
    CThreat::AddToThreatList(&lpAffected->m_Threat, this->m_pCaster, (__int16)m_nNowHP);
    v7 = lpAffected->m_CreatureStatus.m_nNowHP;
    if ( v7 <= v6 )
      v8 = 0;
    else
      v8 = v7 - v6;
    lpAffected->m_CreatureStatus.m_nNowHP = v8;
    if ( !v8 )
    {
      lpAffected->Dead(lpAffected, this->m_pCaster);
      CThreat::ClearAll(&lpAffected->m_Threat);
    }
    if ( (lpAffected->m_dwCID & 0x80000000) == 0 )
    {
      v19 = (CSendStream *)lpAffected[3].m_SpellMgr.m_CastingInfo.m_pEnchantCasting[3];
      v21.m_wType = 0;
      if ( v19 )
        GameClientSendPacket::SendCharAttacked(v19 + 8, lpAffected, lpAffected, v21, 0.0, attackType.m_wType, 0xDu, 0);
    }
    else
    {
      m_nNowMP = lpAffected->m_CreatureStatus.m_nNowMP;
      *(_DWORD *)&szBuffer[26] = lpAffected->m_dwCID;
      *(_WORD *)&szBuffer[32] = lpAffected->m_CreatureStatus.m_nNowHP;
      m_nMaxMP = lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      *(_WORD *)&szBuffer[38] = attackType.m_wType;
      m_nMaxHP = lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      *(_WORD *)&szBuffer[36] = m_nNowMP;
      m_pCaster = this->m_pCaster;
      *(_WORD *)&szBuffer[34] = m_nMaxMP;
      *(_WORD *)&szBuffer[30] = m_nMaxHP;
      szBuffer[40] = 0;
      m_dwCID = m_pCaster->m_dwCID;
      attackType.m_wType = 0;
      *(AtType *)&szBuffer[16] = attackType;
      *(_DWORD *)&szBuffer[12] = m_dwCID;
      *(_WORD *)&szBuffer[20] = m_pCaster->m_CreatureStatus.m_nNowHP;
      LOWORD(m_dwCID) = m_pCaster->m_CreatureStatus.m_nNowMP;
      m_lpCell = lpAffected->m_CellPos.m_lpCell;
      *(_WORD *)&szBuffer[22] = m_dwCID;
      szBuffer[24] = 0;
      szBuffer[25] = 1;
      if ( m_lpCell )
      {
        FirstCharacter = CCell::GetFirstCharacter(m_lpCell);
        if ( FirstCharacter )
        {
          m_lpGameClientDispatch = (CSendStream *)FirstCharacter->m_lpGameClientDispatch;
          if ( m_lpGameClientDispatch )
          {
            if ( CSendStream::WrapCompress(m_lpGameClientDispatch + 8, szBuffer, (char *)0x29, 0xEu, 0, 0) == 1 )
            {
              v17 = this->m_pCaster;
              v18 = v17->m_CellPos.m_lpCell;
              if ( v18 )
                CCell::SendAttackInfo(v18, v17->m_dwCID, &attackType, 1u, (DefenserNode *)&szBuffer[26]);
            }
          }
        }
      }
    }
  }
}

//----- (00471BE0) --------------------------------------------------------
char __thiscall CPoisonedSpell::Activate(CPoisonedSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Cu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471C40) --------------------------------------------------------
char __thiscall CPoisonedSpell::Deactivate(CPoisonedSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Cu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471CB0) --------------------------------------------------------
char __thiscall CLowerStrengthSpell::Activate(
        CLowerStrengthSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage -= this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence -= (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Du, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471D30) --------------------------------------------------------
char __thiscall CLowerStrengthSpell::Deactivate(
        CLowerStrengthSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMinDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nMaxDamage += this->m_wSpellLevel;
  lpAffected->m_CreatureStatus.m_StatusInfo.m_nDefence += (unsigned __int64)((double)this->m_wSpellLevel * 0.5 + 0.5);
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Du, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471DB0) --------------------------------------------------------
char __thiscall CInvincibleSpell::Activate(CInvincibleSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag |= this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Eu, this->m_wSpellLevel, 1);
  return 1;
}

//----- (00471DF0) --------------------------------------------------------
char __thiscall CInvincibleSpell::Deactivate(
        CInvincibleSpell *this,
        CAggresiveCreature *lpAffected,
        char dwOperateFlag)
{
  lpAffected->m_dwStatusFlag &= ~this->m_dwEffectFlag;
  if ( (dwOperateFlag & 1) != 1 )
    CSpell::SendSpellInfo(this, lpAffected, 0x1Eu, this->m_wSpellLevel, 0);
  return 1;
}

//----- (00471E30) --------------------------------------------------------
char __thiscall CCharacter::DropItem(CCharacter *this, unsigned __int16 usProtoTypeID, unsigned __int8 cNum)
{
  CCell *m_lpCell; // ecx
  unsigned int m_dwCID; // esi

  m_lpCell = this->m_CellPos.m_lpCell;
  if ( !m_lpCell )
    return 1;
  m_dwCID = this->m_dwCID;
  if ( CCell::SetItem(m_lpCell, 0, &this->m_CurrentPos, 0, m_dwCID, m_dwCID, usProtoTypeID, cNum, 0) )
    return 1;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCharacter::DropItem",
    aDWorkRylSource_83,
    32,
    pFormat,
    m_dwCID,
    usProtoTypeID);
  return 0;
}

//----- (00471EA0) --------------------------------------------------------
char __thiscall CCharacter::MovePos(CCharacter *this, Position Pos, char cZone, bool bSitDown)
{
  char v5; // al
  char v7; // bl

  v5 = CAggresiveCreature::MoveTo(this, &Pos, bSitDown);
  if ( v5 != 1 && v5 != 2 )
    return 0;
  v7 = cZone;
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::MovePos",
    aDWorkRylSource_83,
    47,
    aCid0x08x_170,
    this->m_dwCID,
    Pos.m_fPointX,
    Pos.m_fPointY,
    Pos.m_fPointZ,
    cZone);
  return GameClientSendPacket::SendCharBindPosition(&this->m_lpGameClientDispatch->m_SendStream, 0, 1u, Pos, v7, 0);
}

//----- (00471F50) --------------------------------------------------------
char __thiscall CCharacter::MoveZone(CCharacter *this, POS NewPos, signed __int8 cZone, char Channel)
{
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  signed __int8 v7; // bl
  void *v8; // edi
  CGameClientDispatch *v9; // ecx
  unsigned int m_dwRequestKey; // [esp+20h] [ebp-18h]
  CDBRequest DBRequest; // [esp+30h] [ebp-8h] BYREF

  if ( (this->m_cOperationFlags & 4) != 0 )
    return 1;
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
    return 0;
  CDBRequest::CDBRequest(&DBRequest, m_lpGameClientDispatch, 0x258u, 0);
  if ( !DBRequest.m_lpDBAgentSendStream || !DBRequest.m_dwRequestKey )
    return 0;
  v7 = Channel;
  if ( !GameClientSendPacket::SendMoveZoneToDBAgent(
          DBRequest.m_lpDBAgentSendStream,
          &NewPos,
          DBRequest.m_dwRequestKey,
          this->m_dwUID,
          cZone,
          Channel) )
  {
    CDBRequest::CancelRequest(&DBRequest);
    return 0;
  }
  v8 = &unk_4E06CC;
  if ( !this->IsPeaceMode(this) )
    v8 = &unk_4E06C0;
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::MoveZone",
    aDWorkRylSource_83,
    90,
    aUidDCid0x08x0x_6,
    this->m_dwUID,
    this->m_dwCID,
    this,
    DBRequest.m_dwRequestKey,
    this->m_lpGameClientDispatch);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::MoveZone",
    aDWorkRylSource_83,
    93,
    aUidDCid0x08x_1,
    this->m_dwUID,
    this->m_dwCID,
    NewPos.fPointX,
    NewPos.fPointY,
    NewPos.fPointZ,
    cZone,
    v7,
    v8);
  GAMELOG::LogZoneMove(this, cZone, v7, 0);
  v9 = this->m_lpGameClientDispatch;
  m_dwRequestKey = DBRequest.m_dwRequestKey;
  this->m_cOperationFlags |= 4u;
  CGameClientDispatch::PushRequestKey(v9, m_dwRequestKey);
  return 1;
}

//----- (004720C0) --------------------------------------------------------
char __thiscall CCharacter::Kill(CCharacter *this, CCharacter *lpAttacker)
{
  CCharacter *v3; // ecx
  unsigned __int16 m_nNowHP; // dx
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  if ( !this->Dead(this, 0) )
    return 0;
  v3 = lpAttacker;
  m_nNowHP = this->m_CreatureStatus.m_nNowHP;
  this->m_CreatureStatus.m_nNowHP = 0;
  if ( !lpAttacker )
    v3 = this;
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharAttacked(
             &m_lpGameClientDispatch->m_SendStream,
             v3,
             this,
             (AtType)1,
             0.0,
             m_nNowHP,
             0,
             0);
  else
    return 1;
}

//----- (00472130) --------------------------------------------------------
char __thiscall CCharacter::NotifyInfo(CCharacter *this, unsigned int dwAdminCID)
{
  int m_nOffenceRevision; // edx
  double m_fPointZ; // st7
  CServerSetup *Instance; // eax
  char ServerZone; // al
  int v7; // esi
  CSingleDispatch *DispatchTable; // eax
  char v9; // bl
  double m_fPointX; // [esp+0h] [ebp-1DCh]
  double m_fPointY; // [esp+8h] [ebp-1D4h]
  int m_nMinDamage; // [esp+18h] [ebp-1C4h]
  int m_nMaxDamage; // [esp+1Ch] [ebp-1C0h]
  int v15; // [esp+20h] [ebp-1BCh]
  int m_nDefenceRevision; // [esp+24h] [ebp-1B8h]
  int m_nDefence; // [esp+28h] [ebp-1B4h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+3Ch] [ebp-1A0h] BYREF
  char szMessage[378]; // [esp+44h] [ebp-198h] BYREF
  int v20; // [esp+1D8h] [ebp-4h]

  m_nOffenceRevision = this->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision;
  m_fPointZ = this->m_CurrentPos.m_fPointZ;
  memset(szMessage, 0, sizeof(szMessage));
  m_nDefence = this->m_CreatureStatus.m_StatusInfo.m_nDefence;
  m_nDefenceRevision = this->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision;
  v15 = m_nOffenceRevision;
  m_nMaxDamage = this->m_CreatureStatus.m_StatusInfo.m_nMaxDamage;
  m_nMinDamage = this->m_CreatureStatus.m_StatusInfo.m_nMinDamage;
  m_fPointY = this->m_CurrentPos.m_fPointY;
  m_fPointX = this->m_CurrentPos.m_fPointX;
  Instance = CServerSetup::GetInstance();
  ServerZone = CServerSetup::GetServerZone(Instance);
  v7 = _snprintf(
         &szMessage[18],
         0xB4u,
         "Name:%s, Class:%d, HP:%3d%%, MP:%3d%%, Zone:%d, X:%.1f, Y:%.1f, Z:%.1f, MinD:%d, MaxD:%d, OR:%d, DR:%d, DF:%d",
         this->m_DBData.m_Info.Name,
         this->m_DBData.m_Info.Class,
         100 * this->m_CreatureStatus.m_nNowHP / this->m_CreatureStatus.m_StatusInfo.m_nMaxHP,
         100 * this->m_CreatureStatus.m_nNowMP / this->m_CreatureStatus.m_StatusInfo.m_nMaxMP,
         ServerZone,
         m_fPointX,
         m_fPointY,
         m_fPointZ,
         m_nMinDamage,
         m_nMaxDamage,
         v15,
         m_nDefenceRevision,
         m_nDefence);
  if ( v7 > 0 )
  {
    szMessage[179] = 0;
    *(_DWORD *)&szMessage[12] = dwAdminCID;
    *(_WORD *)&szMessage[16] = 0;
    DispatchTable = CChatDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
    v20 = 0;
    if ( StoragelpChatDispatch.m_lpDispatch )
    {
      v9 = CSendStream::WrapCompress(
             (CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8],
             szMessage,
             (char *)(v7 + 19),
             0xDu,
             0,
             0);
      v20 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
      return v9;
    }
    v20 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
  }
  return 0;
}

//----- (004722C0) --------------------------------------------------------
char __thiscall CCharacter::DuelInit(CCharacter *this, unsigned __int8 cCmd)
{
  CCharacter *v3; // edi
  CSendStream *m_lpGameClientDispatch; // eax
  CGameClientDispatch *v5; // eax
  CCharacterParty *m_pParty; // ecx

  v3 = (CCharacter *)this->GetDuelOpponent(this);
  if ( v3 )
  {
    if ( cCmd == 3 )
      CThreat::DivisionFame(&this->m_Threat);
    m_lpGameClientDispatch = (CSendStream *)v3->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharDuelCmd(m_lpGameClientDispatch + 8, this->m_dwCID, v3->m_dwCID, cCmd, 0);
    v5 = this->m_lpGameClientDispatch;
    if ( v5 )
      GameClientSendPacket::SendCharDuelCmd(&v5->m_SendStream, this->m_dwCID, v3->m_dwCID, cCmd, 0);
    CCharacter::SetDuelOpponent(v3, 0);
    CCharacter::SetDuelOpponent(this, 0);
  }
  m_pParty = (CCharacterParty *)this->m_pParty;
  if ( m_pParty
    && m_pParty->m_pHostileParty
    && (cCmd == 8 || !CCharacterParty::DropMember(m_pParty, this, (PktDuC::DuelCmd)cCmd)) )
  {
    CCharacterParty::EndTeamBattle((CCharacterParty *)this->m_pParty[1].m_Party.m_dwPartyID);
    CCharacterParty::EndTeamBattle((CCharacterParty *)this->m_pParty);
  }
  return 1;
}

//----- (00472380) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CBGServerMap::Enter(
        VirtualArea::CBGServerMap *this,
        CCharacter *lpCharacter,
        unsigned __int8 cMoveType)
{
  if ( this->m_cStatus != 1 )
    return 1;
  if ( !cMoveType )
    return this->AddCharacter(this, lpCharacter);
  if ( cMoveType != 1 )
    return 1;
  return this->AddSpectator(this, lpCharacter);
}

//----- (004723C0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::RuleCheck(VirtualArea::CBGServerMap *this, bool bTimeout)
{
  char v3; // bl
  unsigned __int16 v4; // ax
  unsigned __int16 v5; // cx
  unsigned __int16 m_wTargetScore; // ax

  v3 = 2;
  if ( bTimeout )
  {
    v4 = this->m_MapInfo.m_wScore[0];
    v5 = this->m_MapInfo.m_wScore[1];
    if ( v4 <= v5 )
    {
      if ( v4 < v5 )
        goto LABEL_8;
LABEL_9:
      this->m_cStatus = 3;
      this->m_dwRemainTime = timeGetTime() + 10000;
      return v3;
    }
LABEL_6:
    v3 = 0;
    goto LABEL_9;
  }
  m_wTargetScore = this->m_MapInfo.m_wTargetScore;
  if ( this->m_MapInfo.m_wScore[0] >= m_wTargetScore )
    goto LABEL_6;
  if ( this->m_MapInfo.m_wScore[1] >= m_wTargetScore )
  {
LABEL_8:
    v3 = 1;
    goto LABEL_9;
  }
  return v3;
}

//----- (00472410) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::DeleteAllItem(VirtualArea::CBGServerMap *this)
{
  CCell *m_CellData; // edi
  int Height; // esi
  CCell *i; // esi

  if ( this->m_CellData )
  {
    m_CellData = this->m_CellData;
    Height = VirtualArea::CVirtualArea::GetHeight(this);
    for ( i = &m_CellData[VirtualArea::CVirtualArea::GetWidth(this) * Height]; m_CellData != i; ++m_CellData )
      CCell::DeleteAllItem(m_CellData);
  }
}

//----- (00472460) --------------------------------------------------------
std::list<CCharacter *>::iterator *__thiscall std::list<CCharacter *>::erase(
        std::list<CCharacter *> *this,
        std::list<CCharacter *>::iterator *result,
        std::list<CCharacter *>::iterator _Where)
{
  std::_List_nod<CCharacter *>::_Node *Next; // edi
  std::list<CCharacter *>::iterator *v5; // eax

  Next = _Where._Ptr->_Next;
  if ( _Where._Ptr != this->_Myhead )
  {
    _Where._Ptr->_Prev->_Next = _Where._Ptr->_Next;
    _Where._Ptr->_Next->_Prev = _Where._Ptr->_Prev;
    operator delete(_Where._Ptr);
    --this->_Mysize;
  }
  v5 = result;
  result->_Ptr = Next;
  return v5;
}

//----- (004724A0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (004724E0) --------------------------------------------------------
bool __thiscall VirtualArea::CBGServerMap::IsPlayer(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter)
{
  std::_List_nod<CCharacter *>::_Node *Myhead; // edx
  std::_List_nod<CCharacter *>::_Node *i; // eax

  if ( !lpCharacter )
    return 0;
  Myhead = this->m_CharacterList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    if ( i->_Myval == lpCharacter )
      break;
  }
  return i != this->m_CharacterList._Myhead;
}

//----- (00472510) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::AllRespawn(VirtualArea::CBGServerMap *this)
{
  std::_List_nod<CCharacter *>::_Node *Myhead; // ebp
  std::_List_nod<CCharacter *>::_Node *Next; // esi
  char v4; // bl
  CCharacter *Myval; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int v7; // eax
  float fPointX; // ecx
  float fPointY; // edx
  float fPointZ; // eax
  DWORD TickCount; // eax
  int v12; // eax
  DWORD v13; // eax
  int v14; // eax
  CCharacter *v15; // ecx
  std::_List_nod<CCharacter *>::_Node *v16; // ebp
  std::_List_nod<CCharacter *>::_Node *v17; // esi
  CCharacter *v18; // eax
  CSendStream *v19; // eax
  unsigned int v20; // eax
  float v21; // ecx
  float v22; // edx
  float v23; // eax
  DWORD v24; // eax
  int v25; // eax
  DWORD v26; // eax
  int v27; // eax
  CCharacter *v28; // ecx
  unsigned __int8 Nationality; // [esp+3h] [ebp-15h]
  unsigned __int8 v30; // [esp+3h] [ebp-15h]
  VirtualArea::CBGServerMap *v31; // [esp+4h] [ebp-14h]
  Position RespawnPos; // [esp+Ch] [ebp-Ch] BYREF

  v31 = this;
  if ( !this->m_CharacterList._Mysize && !this->m_SpectatorList._Mysize )
    return 1;
  Myhead = this->m_CharacterList._Myhead;
  Next = Myhead->_Next;
  v4 = 1;
  while ( Next != Myhead )
  {
    Myval = Next->_Myval;
    if ( Myval )
    {
      m_lpGameClientDispatch = (CSendStream *)Myval->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        if ( !GameClientSendPacket::SendCharBGServerMoveZone(m_lpGameClientDispatch + 8, 0xBu, 0, 0) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "VirtualArea::CBGServerMap::AllRespawn",
            aDWorkRylSource_104,
            208,
            aCid0x08x_118,
            Next->_Myval->m_dwCID,
            Next->_Myval->m_CellPos.m_wMapIndex);
          v4 = 0;
        }
        Nationality = Next->_Myval->m_DBData.m_Info.Nationality;
        v7 = Math::Random::ComplexRandom(2, 0) + 2 * Nationality;
        fPointX = ::RespawnPos[0][v7].fPointX;
        fPointY = ::RespawnPos[0][v7].fPointY;
        fPointZ = ::RespawnPos[0][v7].fPointZ;
        RespawnPos.m_fPointX = fPointX;
        RespawnPos.m_fPointY = fPointY;
        RespawnPos.m_fPointZ = fPointZ;
        TickCount = GetTickCount();
        v12 = Math::Random::SimpleRandom(TickCount, 20, 0);
        RespawnPos.m_fPointX = (double)(v12 - 10) + RespawnPos.m_fPointX;
        v13 = GetTickCount();
        v14 = Math::Random::SimpleRandom(v13, 20, 0);
        v15 = Next->_Myval;
        RespawnPos.m_fPointZ = (double)(v14 - 10) + RespawnPos.m_fPointZ;
        v15->m_CellPos.m_wMapIndex = 0;
        Next->_Myval->m_dwStatusFlag &= ~0x40000000u;
        CAggresiveCreature::MoveTo(Next->_Myval, &RespawnPos, 0);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CBGServerMap::AllRespawn",
          aDWorkRylSource_104,
          222,
          aCid0x08x_118,
          Next->_Myval->m_dwCID,
          Next->_Myval->m_CellPos.m_wMapIndex);
        v4 = 0;
      }
      Next = Next->_Next;
      this = v31;
    }
  }
  v16 = this->m_SpectatorList._Myhead;
  v17 = v16->_Next;
  while ( v17 != v16 )
  {
    v18 = v17->_Myval;
    if ( v18 )
    {
      v19 = (CSendStream *)v18->m_lpGameClientDispatch;
      if ( v19 )
      {
        if ( !GameClientSendPacket::SendCharBGServerMoveZone(v19 + 8, 0xBu, 0, 0) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "VirtualArea::CBGServerMap::AllRespawn",
            aDWorkRylSource_104,
            242,
            aCid0x08x_118,
            v17->_Myval->m_dwCID,
            v17->_Myval->m_CellPos.m_wMapIndex);
          v4 = 0;
        }
        v30 = v17->_Myval->m_DBData.m_Info.Nationality;
        v20 = Math::Random::ComplexRandom(2, 0) + 2 * v30;
        v21 = ::RespawnPos[0][v20].fPointX;
        v22 = ::RespawnPos[0][v20].fPointY;
        v23 = ::RespawnPos[0][v20].fPointZ;
        RespawnPos.m_fPointX = v21;
        RespawnPos.m_fPointY = v22;
        RespawnPos.m_fPointZ = v23;
        v24 = GetTickCount();
        v25 = Math::Random::SimpleRandom(v24, 20, 0);
        RespawnPos.m_fPointX = (double)(v25 - 10) + RespawnPos.m_fPointX;
        v26 = GetTickCount();
        v27 = Math::Random::SimpleRandom(v26, 20, 0);
        v28 = v17->_Myval;
        RespawnPos.m_fPointZ = (double)(v27 - 10) + RespawnPos.m_fPointZ;
        v28->m_CellPos.m_wMapIndex = 0;
        v17->_Myval->m_dwStatusFlag &= ~0x40000000u;
        CAggresiveCreature::MoveTo(v17->_Myval, &RespawnPos, 0);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "VirtualArea::CBGServerMap::AllRespawn",
          aDWorkRylSource_104,
          256,
          aCid0x08x_118,
          v17->_Myval->m_dwCID,
          v17->_Myval->m_CellPos.m_wMapIndex);
        v4 = 0;
      }
      v17 = v17->_Next;
      this = v31;
    }
  }
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CharacterList);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&v31->m_SpectatorList);
  return v4;
}

//----- (00472830) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::SendMapInfo(VirtualArea::CBGServerMap *this)
{
  unsigned __int8 m_cStatus; // al
  unsigned __int8 m_cMapType; // dl
  unsigned __int8 m_cMaxCharNumOfNation; // cl
  unsigned __int16 m_wTargetScore; // dx
  unsigned __int8 m_cRemainPlayMin; // al
  unsigned __int8 v7; // cl
  unsigned __int16 v8; // dx
  unsigned __int16 v9; // ax
  std::_List_nod<CCharacter *>::_Node *Myhead; // ebx
  std::_List_nod<CCharacter *>::_Node *Next; // edi
  CCharacter *Myval; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  std::_List_nod<CCharacter *>::_Node *v14; // esi
  std::_List_nod<CCharacter *>::_Node *v15; // edi
  CCharacter *v16; // eax
  CSendStream *v17; // eax
  char szBuffer[26]; // [esp+4h] [ebp-20h] BYREF

  if ( this->m_CharacterList._Mysize || this->m_SpectatorList._Mysize )
  {
    m_cStatus = this->m_cStatus;
    m_cMapType = this->m_MapInfo.m_cMapType;
    szBuffer[14] = m_cStatus == 1;
    m_cMaxCharNumOfNation = this->m_MapInfo.m_cMaxCharNumOfNation;
    szBuffer[15] = m_cMapType;
    m_wTargetScore = this->m_MapInfo.m_wTargetScore;
    szBuffer[12] = 0;
    szBuffer[13] = 1;
    szBuffer[16] = m_cMaxCharNumOfNation;
    *(_WORD *)&szBuffer[18] = m_wTargetScore;
    if ( m_cStatus == 1 )
      m_cRemainPlayMin = this->m_MapInfo.m_cRemainPlayMin;
    else
      m_cRemainPlayMin = 0;
    v7 = this->m_MapInfo.m_cCurrentCharNum[1];
    v8 = this->m_MapInfo.m_wScore[0];
    szBuffer[17] = m_cRemainPlayMin;
    szBuffer[20] = this->m_MapInfo.m_cCurrentCharNum[0];
    v9 = this->m_MapInfo.m_wScore[1];
    Myhead = this->m_CharacterList._Myhead;
    szBuffer[21] = v7;
    *(_WORD *)&szBuffer[22] = v8;
    *(_WORD *)&szBuffer[24] = v9;
    Next = Myhead->_Next;
    while ( Next != Myhead )
    {
      Myval = Next->_Myval;
      if ( Myval )
      {
        m_lpGameClientDispatch = (CSendStream *)Myval->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          CSendStream::WrapCompress(m_lpGameClientDispatch + 8, szBuffer, (char *)0x1A, 0x98u, 0, 0);
        Next = Next->_Next;
      }
    }
    v14 = this->m_SpectatorList._Myhead;
    v15 = v14->_Next;
    while ( v15 != v14 )
    {
      v16 = v15->_Myval;
      if ( v16 )
      {
        v17 = (CSendStream *)v16->m_lpGameClientDispatch;
        if ( v17 )
          CSendStream::WrapCompress(v17 + 8, szBuffer, (char *)0x1A, 0x98u, 0, 0);
        v15 = v15->_Next;
      }
    }
  }
  return 1;
}

//----- (00472940) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *result,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v5; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v6; // eax
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *v7; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( *_Keyval >= Parent->_Myval.first )
    {
      Parent = Parent->_Right;
    }
    else
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
  }
  v5 = this->_Myhead;
  v6 = v5->_Parent;
  while ( !v6->_Isnil )
  {
    if ( v6->_Myval.first >= *_Keyval )
    {
      v5 = v6;
      v6 = v6->_Left;
    }
    else
    {
      v6 = v6->_Right;
    }
  }
  v7 = result;
  result->first._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)v5;
  result->second._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)Myhead;
  return v7;
}

//----- (004729B0) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::UpdateKillInfo(
        VirtualArea::CBGServerMap *this,
        unsigned int dwDeadCID,
        unsigned int dwKillerCID)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *p_m_PersonalInfoMap; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Ptr; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator pos; // [esp+8h] [ebp-4h] BYREF

  p_m_PersonalInfoMap = (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MapInfo.m_PersonalInfoMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MapInfo.m_PersonalInfoMap,
    &pos,
    &dwDeadCID);
  if ( pos._Ptr != this->m_MapInfo.m_PersonalInfoMap._Myhead )
    ++pos._Ptr->_Myval.second.m_cKilled;
  Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
          p_m_PersonalInfoMap,
          (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&dwDeadCID,
          &dwKillerCID)->_Ptr;
  if ( Ptr != this->m_MapInfo.m_PersonalInfoMap._Myhead )
    ++Ptr->_Myval.second.m_cKill;
}

//----- (00472A00) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::CalculateScore(VirtualArea::CBGServerMap *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  CMonster *second; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  if ( this->m_MapInfo.m_cMapType == 1 )
  {
    if ( this->m_pVirtualMonsterMgr )
    {
      *(_DWORD *)this->m_MapInfo.m_wScore = 0;
      Myhead = this->m_pVirtualMonsterMgr->m_MonsterMap._Myhead;
      Left = Myhead->_Left;
      for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
      {
        second = Left->_Myval.second;
        if ( second && second->m_nCurrentState != 5 )
        {
          switch ( (unsigned __int16)second->m_dwCID )
          {
            case 0x410u:
              ++this->m_MapInfo.m_wScore[0];
              break;
            case 0x411u:
              this->m_MapInfo.m_wScore[0] += 2;
              break;
            case 0x412u:
              ++this->m_MapInfo.m_wScore[1];
              break;
            case 0x413u:
              this->m_MapInfo.m_wScore[1] += 2;
              break;
            default:
              break;
          }
        }
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
      }
    }
  }
}

//----- (00472AA0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::SendResultInfo(VirtualArea::CBGServerMap *this)
{
  unsigned __int16 v2; // cx
  unsigned __int16 v3; // dx
  __int16 m_cLimitMin; // ax
  std::_List_nod<CCharacter *>::_Node *Myhead; // ecx
  std::_List_nod<CCharacter *>::_Node *Next; // edi
  unsigned __int16 v7; // bp
  unsigned __int16 v8; // bx
  CCharacter *Myval; // eax
  unsigned __int8 v10; // al
  unsigned __int8 m_cWinNation; // cl
  CCharacter *v12; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  std::_List_nod<CCharacter *>::_Node *v14; // esi
  std::_List_nod<CCharacter *>::_Node *v15; // edi
  CCharacter *v16; // eax
  CSendStream *v17; // eax
  CGameClientDispatch *lpDispatch; // [esp+10h] [ebp-2Ch]
  unsigned int _Keyval; // [esp+14h] [ebp-28h] BYREF
  std::_List_nod<CCharacter *>::_Node *v21; // [esp+18h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator which; // [esp+1Ch] [ebp-20h] BYREF
  char szBuffer[24]; // [esp+20h] [ebp-1Ch] BYREF

  v2 = this->m_ResultInfo.m_wScore[0];
  v3 = this->m_ResultInfo.m_wScore[1];
  szBuffer[14] = this->m_ResultInfo.m_cWinNation;
  m_cLimitMin = this->m_MapInfo.m_cLimitMin;
  *(_WORD *)&szBuffer[20] = v2;
  Myhead = this->m_CharacterList._Myhead;
  szBuffer[12] = 0;
  szBuffer[13] = 1;
  *(_WORD *)&szBuffer[22] = v3;
  Next = Myhead->_Next;
  v21 = Myhead;
  if ( szBuffer[14] == 2 )
    v7 = 10 * m_cLimitMin;
  else
    v7 = 20 * m_cLimitMin;
  v8 = m_cLimitMin - this->m_MapInfo.m_cRemainPlayMin;
  if ( !v8 )
    v8 = 1;
  while ( Next != Myhead )
  {
    Myval = Next->_Myval;
    if ( Myval )
    {
      lpDispatch = Myval->m_lpGameClientDispatch;
      if ( lpDispatch )
      {
        _Keyval = Myval->m_dwCID;
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
          (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MapInfo.m_PersonalInfoMap,
          &which,
          &_Keyval);
        if ( which._Ptr != this->m_MapInfo.m_PersonalInfoMap._Myhead )
        {
          v10 = which._Ptr->_Myval.second.m_cEnteringMin - this->m_MapInfo.m_cRemainPlayMin;
          szBuffer[15] = v10;
          szBuffer[16] = which._Ptr->_Myval.second.m_cKill;
          szBuffer[17] = which._Ptr->_Myval.second.m_cKilled;
          m_cWinNation = this->m_ResultInfo.m_cWinNation;
          if ( m_cWinNation == 2 || Next->_Myval->m_DBData.m_Info.Nationality == m_cWinNation )
          {
            *(_WORD *)&szBuffer[18] = v7 * v10 / v8;
            Next->_Myval->m_DBData.m_Info.Mileage += *(unsigned __int16 *)&szBuffer[18];
            v12 = Next->_Myval;
            m_lpGameClientDispatch = (CSendStream *)v12->m_lpGameClientDispatch;
            if ( m_lpGameClientDispatch )
              GameClientSendPacket::SendCharFameInfo(m_lpGameClientDispatch + 8, v12, szLoseCharName, szLoseCharName, 0);
          }
          else
          {
            *(_WORD *)&szBuffer[18] = 0;
          }
          CSendStream::WrapCompress(&lpDispatch->m_SendStream, szBuffer, (char *)0x18, 0x99u, 0, 0);
        }
      }
      Next = Next->_Next;
      Myhead = v21;
    }
  }
  v14 = this->m_SpectatorList._Myhead;
  szBuffer[15] = 0;
  *(_WORD *)&szBuffer[18] = 0;
  szBuffer[16] = 0;
  szBuffer[17] = 0;
  v15 = v14->_Next;
  while ( v15 != v14 )
  {
    v16 = v15->_Myval;
    if ( v16 )
    {
      v17 = (CSendStream *)v16->m_lpGameClientDispatch;
      if ( v17 )
        CSendStream::WrapCompress(v17 + 8, szBuffer, (char *)0x18, 0x99u, 0, 0);
      v15 = v15->_Next;
    }
  }
  return 1;
}

//----- (00472C60) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::ResetEnteringMin(VirtualArea::CBGServerMap *this, unsigned __int8 cMin)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Left; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator pos; // [esp+0h] [ebp-4h] BYREF

  pos._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)this;
  if ( this->m_MapInfo.m_PersonalInfoMap._Mysize )
  {
    Myhead = this->m_MapInfo.m_PersonalInfoMap._Myhead;
    Left = Myhead->_Left;
    for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
    {
      Left->_Myval.second.m_cEnteringMin = cMin;
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
    }
  }
}

//----- (00472CA0) --------------------------------------------------------
void __cdecl std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last,
        unsigned int *_Off)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *Ptr; // edi
  unsigned int *v4; // esi

  Ptr = _Last._Ptr;
  if ( _First._Ptr != _Last._Ptr )
  {
    v4 = _Off;
    do
    {
      ++*v4;
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
    }
    while ( _First._Ptr != Ptr );
  }
}

//----- (00472CE0) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::InitializeGameObject(VirtualArea::CBGServerMap *this)
{
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  CMonster *second; // ecx
  CStatue *v6; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v7; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  bool v9; // zf
  int v10; // edi
  CMonster *v11; // ecx
  CStatue *v12; // eax
  int v13; // esi
  CStatue *LinkStatue; // eax
  CStatue *v15; // eax
  char *MapTypeName; // eax
  int v18; // [esp-10h] [ebp-1Ch]
  int v19; // [esp-Ch] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+8h] [ebp-4h] BYREF

  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_CharacterList);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_SpectatorList);
  VirtualArea::MapInfo::Initialize(&this->m_MapInfo);
  VirtualArea::ResultInfo::Initialize(&this->m_ResultInfo);
  if ( this->m_MapInfo.m_cMapType == 1 )
  {
    m_pVirtualMonsterMgr = this->m_pVirtualMonsterMgr;
    if ( m_pVirtualMonsterMgr )
    {
      Myhead = m_pVirtualMonsterMgr->m_MonsterMap._Myhead;
      Left = Myhead->_Left;
      pos._Ptr = Myhead->_Left;
      if ( pos._Ptr != Myhead )
      {
        do
        {
          second = Left->_Myval.second;
          if ( second )
          {
            v6 = CMonster::DowncastToStatue(second);
            if ( v6 )
              CStatue::Rest(v6);
            std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
            Left = pos._Ptr;
          }
        }
        while ( Left != Myhead );
      }
      v7 = this->m_pVirtualMonsterMgr->m_MonsterMap._Myhead;
      Ptr = v7->_Left;
      v9 = v7->_Left == v7;
      v10 = 100;
      pos._Ptr = v7->_Left;
      if ( !v9 )
      {
        do
        {
          v11 = Ptr->_Myval.second;
          if ( v11 )
          {
            v12 = CMonster::DowncastToStatue(v11);
            if ( v12 )
            {
              v13 = HIWORD(v12->m_dwCID) & 0x7FFF;
              if ( v10 != v13 )
              {
                switch ( HIWORD(v12->m_dwCID) & 0x7FFF )
                {
                  case 0:
                    LinkStatue = CStatue::GetLinkStatue(v12, 0x411u);
                    goto LABEL_17;
                  case 1:
                  case 3:
                  case 4:
                    v15 = CStatue::GetLinkStatue(v12, 0x40Fu);
                    CMonster::InitMonster(v15, &v15->m_OriginalPosition, LOGINOUT);
                    break;
                  case 2:
                    LinkStatue = CStatue::GetLinkStatue(v12, 0x413u);
LABEL_17:
                    CMonster::InitMonster(LinkStatue, &LinkStatue->m_OriginalPosition, LOGINOUT);
                    break;
                  default:
                    break;
                }
                v10 = v13;
              }
            }
            std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
            Ptr = pos._Ptr;
          }
        }
        while ( Ptr != v7 );
      }
      VirtualArea::CBGServerMap::CalculateScore(this);
      v19 = this->m_MapInfo.m_wScore[1];
      v18 = this->m_MapInfo.m_wScore[0];
      MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(this);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "VirtualArea::CBGServerMap::InitializeGameObject",
        aDWorkRylSource_104,
        464,
        aBattleServerLo,
        this->m_wMapIndex & 0x7FFF,
        MapTypeName,
        v18,
        v19);
    }
  }
  return 1;
}

//----- (00472E50) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::KillChar(
        VirtualArea::CBGServerMap *this,
        unsigned int dwDeadCID,
        CCharacter *lpOffencer)
{
  unsigned __int8 Nationality; // al

  if ( lpOffencer )
  {
    Nationality = lpOffencer->m_DBData.m_Info.Nationality;
    if ( Nationality < 0x19u )
      ++this->m_MapInfo.m_wScore[Nationality];
    VirtualArea::CBGServerMap::UpdateKillInfo(this, dwDeadCID, lpOffencer->m_dwCID);
    VirtualArea::CBGServerMap::SendMapInfo(this);
  }
}

//----- (00472E90) --------------------------------------------------------
bool __thiscall VirtualArea::CBGServerMap::GameStart(VirtualArea::CBGServerMap *this)
{
  bool result; // al

  this->m_dwRemainTime = 60000 * this->m_MapInfo.m_cLimitMin + timeGetTime();
  VirtualArea::CBGServerMap::InitializeGameObject(this);
  result = 1;
  this->m_cStatus = 1;
  return result;
}

//----- (00472EC0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00473070) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00473330) --------------------------------------------------------
void __thiscall std::list<CCharacter *>::_Incsize(std::list<CCharacter *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';



//----- (004733D0) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::Process(VirtualArea::CBGServerMap *this)
{
  DWORD Time; // ecx
  unsigned int m_dwRemainTime; // eax
  signed int v4; // eax
  unsigned int v5; // eax
  signed int v6; // et2
  unsigned __int16 *m_wScore; // edi
  unsigned __int8 v8; // bl
  unsigned __int16 v9; // cx
  unsigned __int16 m_wTargetScore; // ax

  Time = timeGetTime();
  m_dwRemainTime = this->m_dwRemainTime;
  if ( m_dwRemainTime >= Time )
  {
    v5 = m_dwRemainTime - Time;
    v6 = v5 % 0xEA60;
    v4 = v5 / 0xEA60;
    if ( v6 > 0 )
      ++v4;
  }
  else
  {
    v4 = 0;
  }
  switch ( this->m_cStatus )
  {
    case 1u:
      if ( v4 < this->m_MapInfo.m_cRemainPlayMin )
      {
        this->m_MapInfo.m_cRemainPlayMin = v4;
        VirtualArea::CBGServerMap::SendMapInfo(this);
      }
      m_wScore = this->m_MapInfo.m_wScore;
      v8 = 2;
      if ( !this->m_MapInfo.m_cRemainPlayMin )
      {
        v9 = this->m_MapInfo.m_wScore[1];
        if ( *m_wScore <= v9 )
        {
          if ( *m_wScore < v9 )
            v8 = 1;
        }
        else
        {
          v8 = 0;
        }
        this->m_cStatus = 3;
        this->m_dwRemainTime = timeGetTime() + 10000;
        this->m_ResultInfo.m_cWinNation = v8;
        std::copy<unsigned short *,unsigned short *>(
          (unsigned __int8 *)this->m_MapInfo.m_wScore,
          (unsigned __int16 *)&this->m_MapInfo.m_cLimitMin,
          (unsigned __int8 *)&this->m_ResultInfo);
        VirtualArea::CBGServerMap::SendResultInfo(this);
        return;
      }
      m_wTargetScore = this->m_MapInfo.m_wTargetScore;
      if ( *m_wScore < m_wTargetScore )
      {
        if ( this->m_MapInfo.m_wScore[1] < m_wTargetScore )
          goto LABEL_19;
        v8 = 1;
      }
      else
      {
        v8 = 0;
      }
      this->m_cStatus = 3;
      this->m_dwRemainTime = timeGetTime() + 10000;
LABEL_19:
      this->m_ResultInfo.m_cWinNation = v8;
      if ( v8 != 2 )
      {
        std::copy<unsigned short *,unsigned short *>(
          (unsigned __int8 *)this->m_MapInfo.m_wScore,
          (unsigned __int16 *)&this->m_MapInfo.m_cLimitMin,
          (unsigned __int8 *)&this->m_ResultInfo);
        VirtualArea::CBGServerMap::SendResultInfo(this);
      }
      return;
    case 2u:
      this->m_MapInfo.m_cRemainRestMin = v4;
      if ( !(_BYTE)v4 )
        VirtualArea::CBGServerMap::GameStart(this);
      return;
    case 3u:
      if ( this->m_dwRemainTime <= timeGetTime() )
      {
        this->m_cStatus = 2;
        this->m_dwRemainTime = 60000 * this->m_MapInfo.m_cRestMin + timeGetTime();
        VirtualArea::CBGServerMap::AllRespawn(this);
        VirtualArea::CBGServerMap::DeleteAllItem(this);
        VirtualArea::MapInfo::Initialize(&this->m_MapInfo);
      }
      return;
    default:
      return;
  }
}

//----- (00473510) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (004735D0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00473690) --------------------------------------------------------
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Ptr; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *v4; // ebx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> _Where; // [esp+Ch] [ebp-8h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this,
    &_Where,
    _Keyval);
  Ptr = _Where.second._Ptr;
  v4 = _Where.first._Ptr;
  _Keyval = 0;
  std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.first._Ptr,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.second._Ptr,
    (unsigned int *)&_Keyval);
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
    this,
    &_Where.first,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)Ptr);
  return _Keyval;
}

//----- (004736F0) --------------------------------------------------------
void __thiscall std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo>::~map<unsigned long,VirtualArea::MapInfo::PersonalInfo>(
        std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00473720) --------------------------------------------------------
char __thiscall VirtualArea::CBGServerMap::Leave(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter)
{
  CCharacter *v2; // edi
  unsigned __int8 Nationality; // dl
  int v5; // eax
  std::_List_nod<CCharacter *>::_Node *Myhead; // ecx
  std::_List_nod<CCharacter *>::_Node *i; // eax
  std::_List_nod<CCharacter *>::_Node *v8; // ecx
  std::_List_nod<CCharacter *>::_Node *j; // eax
  char *MapTypeName; // [esp-10h] [ebp-24h]
  char *v12; // [esp-10h] [ebp-24h]
  unsigned int m_dwCID; // [esp-Ch] [ebp-20h]
  unsigned int v14; // [esp-Ch] [ebp-20h]
  char szNation[8]; // [esp+8h] [ebp-Ch] BYREF

  v2 = lpCharacter;
  if ( lpCharacter )
  {
    Nationality = lpCharacter->m_DBData.m_Info.Nationality;
    if ( Nationality )
      v5 = *(_DWORD *)aAkha;
    else
      v5 = *(_DWORD *)aHuma;
    strcpy(&szNation[4], "N");
    Myhead = this->m_CharacterList._Myhead;
    *(_DWORD *)szNation = v5;
    for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
    {
      if ( i->_Myval == lpCharacter )
        break;
    }
    if ( i != this->m_CharacterList._Myhead )
    {
      --this->m_MapInfo.m_cCurrentCharNum[Nationality];
      if ( i != this->m_CharacterList._Myhead )
      {
        i->_Prev->_Next = i->_Next;
        i->_Next->_Prev = i->_Prev;
        operator delete(i);
        --this->m_CharacterList._Mysize;
      }
      lpCharacter = (CCharacter *)v2->m_dwCID;
      std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
        &this->m_MapInfo.m_PersonalInfoMap,
        (const unsigned int *)&lpCharacter);
      m_dwCID = v2->m_dwCID;
      MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(this);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "VirtualArea::CBGServerMap::Leave",
        aDWorkRylSource_104,
        79,
        aBattleServerLo_5,
        this->m_wMapIndex & 0x7FFF,
        MapTypeName,
        m_dwCID,
        v2->m_DBData.m_Info.Name,
        szNation);
      return 1;
    }
    v8 = this->m_SpectatorList._Myhead;
    for ( j = v8->_Next; j != v8; j = j->_Next )
    {
      if ( j->_Myval == lpCharacter )
        break;
    }
    if ( j != this->m_SpectatorList._Myhead )
    {
      std::list<CCharacter *>::erase(
        &this->m_SpectatorList,
        (std::list<CCharacter *>::iterator *)&lpCharacter,
        (std::list<CCharacter *>::iterator)j);
      v14 = v2->m_dwCID;
      v12 = VirtualArea::CVirtualArea::GetMapTypeName(this);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "VirtualArea::CBGServerMap::Leave",
        aDWorkRylSource_104,
        92,
        aBattleServerLo_5,
        this->m_wMapIndex & 0x7FFF,
        v12,
        v14,
        v2->m_DBData.m_Info.Name,
        szNation);
      return 1;
    }
  }
  return 0;
}

//----- (00473880) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
unsigned __int16 __thiscall VirtualArea::CBGServerMap::AddCharacter(
        VirtualArea::CBGServerMap *this,
        CCharacter *lpCharacter)
{
  unsigned __int8 Nationality; // dl
  unsigned __int8 *v5; // ecx
  double v6; // st7
  double v7; // st7
  std::_List_nod<CThread *>::_Node *Myhead; // ebx
  std::_List_nod<CThread *>::_Node *v9; // ebp
  CCharacter *v10; // edi
  unsigned int m_dwCID; // edx
  char *MapTypeName; // eax
  unsigned int v13; // [esp-18h] [ebp-38h]
  int personalInfo; // [esp+4h] [ebp-1Ch] OVERLAPPED BYREF
  unsigned int _Val; // [esp+Ch] [ebp-14h] BYREF
  __int16 _Val_4; // [esp+10h] [ebp-10h]
  char _Val_6; // [esp+12h] [ebp-Eh]
  char szNation[8]; // [esp+14h] [ebp-Ch] BYREF

  if ( !lpCharacter )
    return 1;
  Nationality = lpCharacter->m_DBData.m_Info.Nationality;
  v5 = &this->m_MapInfo.m_cCurrentCharNum[Nationality];
  if ( this->m_MapInfo.m_cMaxCharNumOfNation <= *v5 )
    return 2;
  if ( *v5 >= 0xAu )
  {
    if ( !Nationality )
    {
      personalInfo = this->m_MapInfo.m_cCurrentCharNum[0];
      v6 = (double)personalInfo;
      personalInfo = this->m_MapInfo.m_cCurrentCharNum[1];
      if ( (double)personalInfo * 1.5 <= v6 )
        return 3;
    }
    if ( Nationality == 1 )
    {
      v7 = (double)this->m_MapInfo.m_cCurrentCharNum[1];
      personalInfo = this->m_MapInfo.m_cCurrentCharNum[0];
      if ( (double)personalInfo * 1.5 <= v7 )
        return 3;
    }
  }
  ++*v5;
  Myhead = (std::_List_nod<CThread *>::_Node *)this->m_CharacterList._Myhead;
  v9 = std::list<IOPCode *>::_Buynode(
         (std::list<CThread *> *)&this->m_CharacterList,
         Myhead,
         Myhead->_Prev,
         (CThread **)&lpCharacter);
  std::list<CCharacter *>::_Incsize(&this->m_CharacterList, 1u);
  v10 = lpCharacter;
  Myhead->_Prev = v9;
  v9->_Prev->_Next = v9;
  m_dwCID = v10->m_dwCID;
  LOWORD(personalInfo) = this->m_MapInfo.m_cRemainPlayMin;
  _Val = m_dwCID;
  _Val_6 = 0;
  _Val_4 = (unsigned __int8)personalInfo;
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::insert(
    &this->m_MapInfo.m_PersonalInfoMap,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *)&personalInfo,
    (std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *)&_Val);
  if ( v10->m_DBData.m_Info.Nationality )
    *(_DWORD *)szNation = *(_DWORD *)aAkha;
  else
    *(_DWORD *)szNation = *(_DWORD *)aHuma;
  strcpy(&szNation[4], "N");
  v13 = v10->m_dwCID;
  MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(this);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "VirtualArea::CBGServerMap::AddCharacter",
    aDWorkRylSource_104,
    143,
    aBattleServerLo_0,
    this->m_wMapIndex & 0x7FFF,
    MapTypeName,
    v13,
    v10->m_DBData.m_Info.Name,
    szNation);
  return 0;
}
// 473880: variables would overlap: ^3C.4 and stkvar "personalInfo" ^3C.3(has user info)

//----- (00473A50) --------------------------------------------------------
unsigned __int16 __thiscall VirtualArea::CBGServerMap::AddSpectator(
        VirtualArea::CBGServerMap *this,
        CCharacter *lpSpectator)
{
  std::_List_nod<CThread *>::_Node *Myhead; // edi
  std::_List_nod<CThread *>::_Node *v5; // ebx
  CCharacter *v6; // eax
  int v7; // ecx
  char *MapTypeName; // eax
  unsigned int m_dwCID; // [esp-Ch] [ebp-1Ch]
  char *Name; // [esp-8h] [ebp-18h]
  char szNation[8]; // [esp+4h] [ebp-Ch] BYREF

  if ( !lpSpectator )
    return 1;
  Myhead = (std::_List_nod<CThread *>::_Node *)this->m_SpectatorList._Myhead;
  v5 = std::list<IOPCode *>::_Buynode(
         (std::list<CThread *> *)&this->m_SpectatorList,
         Myhead,
         Myhead->_Prev,
         (CThread **)&lpSpectator);
  std::list<CCharacter *>::_Incsize(&this->m_SpectatorList, 1u);
  v6 = lpSpectator;
  Myhead->_Prev = v5;
  v5->_Prev->_Next = v5;
  if ( v6->m_DBData.m_Info.Nationality )
    v7 = *(_DWORD *)aAkha;
  else
    v7 = *(_DWORD *)aHuma;
  strcpy(&szNation[4], "N");
  *(_DWORD *)szNation = v7;
  Name = v6->m_DBData.m_Info.Name;
  m_dwCID = v6->m_dwCID;
  MapTypeName = VirtualArea::CVirtualArea::GetMapTypeName(this);
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "VirtualArea::CBGServerMap::AddSpectator",
    aDWorkRylSource_104,
    165,
    aBattleServerLo_3,
    this->m_wMapIndex & 0x7FFF,
    MapTypeName,
    m_dwCID,
    Name,
    szNation);
  return 0;
}

//----- (00473B40) --------------------------------------------------------
void __thiscall VirtualArea::MapInfo::~MapInfo(VirtualArea::MapInfo *this)
{
  std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo> *p_m_PersonalInfoMap; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  p_m_PersonalInfoMap = &this->m_PersonalInfoMap;
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
    &this->m_PersonalInfoMap,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)this->m_PersonalInfoMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)this->m_PersonalInfoMap._Myhead);
  operator delete(p_m_PersonalInfoMap->_Myhead);
  p_m_PersonalInfoMap->_Myhead = 0;
  p_m_PersonalInfoMap->_Mysize = 0;
}

//----- (00473B70) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::~CBGServerMap(VirtualArea::CBGServerMap *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *Myhead; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator v3; // [esp-8h] [ebp-24h]
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  this->__vftable = (VirtualArea::CBGServerMap_vtbl *)&VirtualArea::CBGServerMap::`vftable';
  Myhead = this->m_MapInfo.m_PersonalInfoMap._Myhead;
  v3._Ptr = Myhead->_Left;
  v5 = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(
    &this->m_MapInfo.m_PersonalInfoMap,
    &result,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator)Myhead);
  operator delete(this->m_MapInfo.m_PersonalInfoMap._Myhead);
  this->m_MapInfo.m_PersonalInfoMap._Myhead = 0;
  this->m_MapInfo.m_PersonalInfoMap._Mysize = 0;
  v5 = -1;
  VirtualArea::CVirtualArea::~CVirtualArea(this);
}
// 4E6F2C: using guessed type void *VirtualArea::CBGServerMap::`vftable';

//----- (00473BF0) --------------------------------------------------------
void __thiscall VirtualArea::CBGServerMap::CBGServerMap(
        VirtualArea::CBGServerMap *this,
        const VirtualArea::ProtoType *lpProtoType,
        unsigned __int16 wMapNumber)
{
  VirtualArea::CVirtualArea::CVirtualArea(this, lpProtoType, wMapNumber | 0x8000);
  this->__vftable = (VirtualArea::CBGServerMap_vtbl *)&VirtualArea::CBGServerMap::`vftable';
  VirtualArea::MapInfo::MapInfo(&this->m_MapInfo, lpProtoType->m_cMapType);
  VirtualArea::ResultInfo::ResultInfo(&this->m_ResultInfo);
  this->m_cStatus = 0;
}
// 4E6F2C: using guessed type void *VirtualArea::CBGServerMap::`vftable';

//----- (00473C70) --------------------------------------------------------
VirtualArea::CBGServerMap *__thiscall VirtualArea::CBGServerMap::`scalar deleting destructor'(
        VirtualArea::CBGServerMap *this,
        char a2)
{
  VirtualArea::CBGServerMap::~CBGServerMap(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00473C90) --------------------------------------------------------
const CMonsterMgr::MonsterProtoType *__thiscall CMonsterMgr::GetMonsterProtoType(CMonsterMgr *this, unsigned int dwKID)
{
  const CMonsterMgr::MonsterProtoType *result; // eax
  unsigned int m_nMonsterNum; // esi
  const CMonsterMgr::MonsterProtoType *v4; // edi
  unsigned int v5; // ecx
  unsigned int m_dwKID; // ebp
  const CMonsterMgr::MonsterProtoType *v7; // ecx

  result = this->m_ProtoTypeArray;
  m_nMonsterNum = this->m_nMonsterNum;
  v4 = &this->m_ProtoTypeArray[m_nMonsterNum];
  while ( m_nMonsterNum )
  {
    v5 = m_nMonsterNum >> 1;
    m_dwKID = result[v5].m_MonsterInfo.m_dwKID;
    v7 = &result[v5];
    if ( m_dwKID >= dwKID )
    {
      m_nMonsterNum >>= 1;
    }
    else
    {
      result = v7 + 1;
      m_nMonsterNum += -1 - (m_nMonsterNum >> 1);
    }
  }
  if ( result == v4 || dwKID < result->m_MonsterInfo.m_dwKID )
    return 0;
  return result;
}

//----- (00473D00) --------------------------------------------------------
CMonsterMgr::MonsterProtoType *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::size(
        std::vector<CMonsterMgr::MonsterProtoType> *this)
{
  CMonsterMgr::MonsterProtoType *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (CMonsterMgr::MonsterProtoType *)(this->_Mylast - result);
  return result;
}

//----- (00473D30) --------------------------------------------------------
void __cdecl std::fill<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType>(
        CMonsterMgr::MonsterProtoType *_First,
        CMonsterMgr::MonsterProtoType *_Last,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  CMonsterMgr::MonsterProtoType *i; // eax
  CMonsterMgr::MonsterProtoType *v4; // edi

  for ( i = _First; i != _Last; ++i )
  {
    v4 = i;
    qmemcpy(v4, _Val, sizeof(CMonsterMgr::MonsterProtoType));
  }
}

//----- (00473D60) --------------------------------------------------------
void __thiscall CMonsterMgr::CMonsterMgr(CMonsterMgr *this)
{
  CSingleton<CMonsterMgr>::ms_pSingleton = this;
  this->m_ProtoTypeArray = 0;
  this->m_nMonsterNum = 0;
}

//----- (00473D80) --------------------------------------------------------
void __thiscall CMonsterMgr::~CMonsterMgr(CMonsterMgr *this)
{
  if ( this->m_ProtoTypeArray )
    operator delete[](this->m_ProtoTypeArray);
  CSingleton<CMonsterMgr>::ms_pSingleton = 0;
}

//----- (00473DA0) --------------------------------------------------------
CMonsterMgr::MonsterProtoType *__cdecl std::copy_backward<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType *>(
        CMonsterMgr::MonsterProtoType *_First,
        CMonsterMgr::MonsterProtoType *_Last,
        CMonsterMgr::MonsterProtoType *_Dest)
{
  CMonsterMgr::MonsterProtoType *v3; // edx
  CMonsterMgr::MonsterProtoType *result; // eax

  v3 = _Last;
  result = _Dest;
  while ( v3 != _First )
    qmemcpy(--result, --v3, sizeof(CMonsterMgr::MonsterProtoType));
  return result;
}

//----- (00473DE0) --------------------------------------------------------
void __cdecl std::iter_swap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Left,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Right)
{
  _BYTE v2[368]; // [esp+8h] [ebp-178h] BYREF

  qmemcpy(v2, _Left._Myptr, sizeof(v2));
  qmemcpy(_Left._Myptr, _Right._Myptr, sizeof(CMonsterMgr::MonsterProtoType));
  qmemcpy(_Right._Myptr, v2, sizeof(CMonsterMgr::MonsterProtoType));
}

//----- (00473E40) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  if ( _Mid._Myptr->m_MonsterInfo.m_dwKID < _First._Myptr->m_MonsterInfo.m_dwKID )
    std::iter_swap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      _Mid,
      _First);
  if ( _Last._Myptr->m_MonsterInfo.m_dwKID < _Mid._Myptr->m_MonsterInfo.m_dwKID )
    std::iter_swap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      _Last,
      _Mid);
  if ( _Mid._Myptr->m_MonsterInfo.m_dwKID < _First._Myptr->m_MonsterInfo.m_dwKID )
    std::iter_swap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      _Mid,
      _First);
}

//----- (00473EA0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        int _Hole,
        int _Top,
        CMonsterMgr::MonsterProtoType _Val)
{
  int v4; // edi
  int i; // eax
  CMonsterMgr::MonsterProtoType *v6; // esi

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    if ( v6->m_MonsterInfo.m_dwKID >= _Val.m_MonsterInfo.m_dwKID )
      break;
    qmemcpy(&_First._Myptr[v4], v6, sizeof(_First._Myptr[v4]));
    v4 = i;
  }
  qmemcpy(&_First._Myptr[v4], &_Val, sizeof(_First._Myptr[v4]));
}

//----- (00473F10) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  int v3; // ecx
  int v4; // edi
  int v5; // ebx
  int v6; // edx
  int v7; // ebx
  CMonsterMgr::MonsterProtoType *v8; // edx
  CMonsterMgr::MonsterProtoType *v9; // eax
  std::vector<CMonsterMgr::MonsterProtoType>::iterator *p_First; // ecx
  CMonsterMgr::MonsterProtoType *Myptr; // ebx
  int v12; // eax
  const void **v13; // eax
  bool v14; // zf
  CMonsterMgr::MonsterProtoType *v15; // [esp+10h] [ebp-198h]
  int v16; // [esp+14h] [ebp-194h]
  std::vector<CMonsterMgr::MonsterProtoType>::iterator _Next; // [esp+18h] [ebp-190h]
  int v18; // [esp+1Ch] [ebp-18Ch]
  char *v19; // [esp+20h] [ebp-188h] BYREF
  CMonsterMgr::MonsterProtoType *v20; // [esp+24h] [ebp-184h] BYREF
  CMonsterMgr::MonsterProtoType *v21; // [esp+28h] [ebp-180h] BYREF
  int _Shift; // [esp+2Ch] [ebp-17Ch]
  CMonsterMgr::MonsterProtoType _Holeval; // [esp+30h] [ebp-178h] BYREF

  v3 = _Mid._Myptr - _First._Myptr;
  _Shift = v3;
  v4 = _Last._Myptr - _First._Myptr;
  v5 = v3;
  if ( v3 )
  {
    do
    {
      v6 = v4 % v5;
      v4 = v5;
      v5 = v6;
    }
    while ( v6 );
  }
  if ( v4 < _Last._Myptr - _First._Myptr && v4 > 0 )
  {
    v7 = 368 * v3;
    v8 = &_First._Myptr[v4];
    v16 = v3;
    v15 = v8;
    v18 = v4;
    while ( 1 )
    {
      qmemcpy(&_Holeval, v8, sizeof(_Holeval));
      v9 = v8;
      if ( (char *)v8 + v7 == (char *)_Last._Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v19 = (char *)v8 + v7;
        p_First = (std::vector<CMonsterMgr::MonsterProtoType>::iterator *)&v19;
      }
      Myptr = p_First->_Myptr;
      if ( p_First->_Myptr != v8 )
      {
        do
        {
          qmemcpy(v9, Myptr, sizeof(CMonsterMgr::MonsterProtoType));
          v12 = _Last._Myptr - Myptr;
          _Next._Myptr = Myptr;
          if ( _Shift >= v12 )
          {
            v21 = &_First._Myptr[_Shift - v12];
            v13 = (const void **)&v21;
          }
          else
          {
            v20 = &Myptr[v16];
            v13 = (const void **)&v20;
          }
          Myptr = (CMonsterMgr::MonsterProtoType *)*v13;
          v14 = *v13 == v15;
          v9 = _Next._Myptr;
        }
        while ( !v14 );
        v8 = v15;
      }
      --v8;
      v14 = v18 == 1;
      qmemcpy(v9, &_Holeval, sizeof(CMonsterMgr::MonsterProtoType));
      v15 = v8;
      --v18;
      if ( v14 )
        break;
      v7 = v16 * 368;
    }
  }
}

//----- (00474080) --------------------------------------------------------
CMonsterMgr::MonsterProtoType *__cdecl std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
        CMonsterMgr::MonsterProtoType *_First,
        CMonsterMgr::MonsterProtoType *_Last,
        CMonsterMgr::MonsterProtoType *_Dest)
{
  CMonsterMgr::MonsterProtoType *v3; // edx
  CMonsterMgr::MonsterProtoType *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
      qmemcpy(result, v3, sizeof(CMonsterMgr::MonsterProtoType));
    ++v3;
  }
  return result;
}

//----- (004740C0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  int v3; // eax
  int v4; // esi
  unsigned int v5; // edi
  CMonsterMgr::MonsterProtoType *v7; // [esp-Ch] [ebp-14h]
  char *_Firsta; // [esp+Ch] [ebp+4h]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(_First, _Mid, _Last);
  }
  else
  {
    v4 = (v3 + 1) / 8;
    v5 = 736 * v4;
    v4 *= 368;
    v7 = &_First._Myptr[v5 / 0x170];
    _Firsta = (char *)_First._Myptr + v4;
    std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      _First,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)_Firsta,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v7);
    std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)((char *)_Mid._Myptr - v4),
      _Mid,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)((char *)_Mid._Myptr + v4));
    std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)&_Last._Myptr[v5 / 0xFFFFFE90],
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)((char *)_Last._Myptr - v4),
      _Last);
    std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)_Firsta,
      _Mid,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)((char *)_Last._Myptr - v4));
  }
}

//----- (00474160) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        int _Hole,
        int _Bottom,
        CMonsterMgr::MonsterProtoType _Val)
{
  int v4; // edx
  int v5; // eax
  bool v6; // zf
  int v7; // edi
  CMonsterMgr::MonsterProtoType *v8; // esi
  CMonsterMgr::MonsterProtoType v9; // [esp-170h] [ebp-180h] BYREF

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  v6 = v5 == _Bottom;
  while ( v5 < _Bottom )
  {
    if ( _First._Myptr[v5].m_MonsterInfo.m_dwKID < _First._Myptr[v5 - 1].m_MonsterInfo.m_dwKID )
      --v5;
    v7 = v4;
    v4 = v5;
    v8 = &_First._Myptr[v5];
    v5 = 2 * v5 + 2;
    v6 = v5 == _Bottom;
    qmemcpy(&_First._Myptr[v7], v8, sizeof(_First._Myptr[v7]));
  }
  if ( v6 )
  {
    qmemcpy(&_First._Myptr[v4], &_First._Myptr[_Bottom - 1], sizeof(_First._Myptr[v4]));
    v4 = _Bottom - 1;
  }
  qmemcpy(&v9, &_Val, sizeof(v9));
  std::_Push_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
    _First,
    v4,
    _Hole,
    v9);
}

//----- (00474220) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CMonsterMgr::MonsterProtoType *,unsigned int,CMonsterMgr::MonsterProtoType,std::allocator<CMonsterMgr::MonsterProtoType>>(
        CMonsterMgr::MonsterProtoType *_First,
        unsigned int _Count,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        qmemcpy(_First, _Val, sizeof(CMonsterMgr::MonsterProtoType));
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (00474250) --------------------------------------------------------
std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> *result,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  CMonsterMgr::MonsterProtoType *v3; // esi
  unsigned int v4; // ebx
  CMonsterMgr::MonsterProtoType *v5; // edx
  unsigned int m_dwKID; // eax
  unsigned int v7; // ecx
  unsigned int v8; // ecx
  unsigned int v9; // eax
  CMonsterMgr::MonsterProtoType *v10; // eax
  unsigned int v11; // ecx
  unsigned int v12; // esi
  CMonsterMgr::MonsterProtoType *v13; // ebx
  CMonsterMgr::MonsterProtoType *v14; // edi
  bool v15; // zf
  CMonsterMgr::MonsterProtoType *v16; // ebx
  unsigned int v17; // ecx
  unsigned int v18; // esi
  bool v19; // cf
  CMonsterMgr::MonsterProtoType *v20; // ebx
  CMonsterMgr::MonsterProtoType *v21; // edi
  std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> *v22; // eax
  CMonsterMgr::MonsterProtoType *_Plast; // [esp+Ch] [ebp-A24h]
  CMonsterMgr::MonsterProtoType *_Glast; // [esp+10h] [ebp-A20h]
  _BYTE v25[368]; // [esp+18h] [ebp-A18h] BYREF
  _BYTE v26[368]; // [esp+188h] [ebp-8A8h] BYREF
  _BYTE v27[368]; // [esp+2F8h] [ebp-738h] BYREF
  _BYTE v28[368]; // [esp+468h] [ebp-5C8h] BYREF
  _BYTE v29[368]; // [esp+5D8h] [ebp-458h] BYREF
  _BYTE v30[368]; // [esp+748h] [ebp-2E8h] BYREF
  _BYTE v31[368]; // [esp+8B8h] [ebp-178h] BYREF

  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  std::_Median<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
    _First,
    (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v3,
    (std::vector<CMonsterMgr::MonsterProtoType>::iterator)&_Last._Myptr[-1]);
  v4 = (unsigned int)&v3[1];
  v5 = v3;
  for ( _Plast = v3 + 1; _First._Myptr < v5; --v5 )
  {
    m_dwKID = v5[-1].m_MonsterInfo.m_dwKID;
    v7 = v5->m_MonsterInfo.m_dwKID;
    if ( v7 > m_dwKID )
      break;
    if ( v7 < m_dwKID )
      break;
  }
  if ( v4 < (unsigned int)_Last._Myptr )
  {
    v8 = v5->m_MonsterInfo.m_dwKID;
    do
    {
      v9 = *(_DWORD *)(v4 + 180);
      if ( v8 > v9 )
        break;
      if ( v8 < v9 )
        break;
      v4 += 368;
    }
    while ( v4 < (unsigned int)_Last._Myptr );
    _Plast = (CMonsterMgr::MonsterProtoType *)v4;
  }
  v10 = (CMonsterMgr::MonsterProtoType *)v4;
  _Glast = v5;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v10 < _Last._Myptr; ++v10 )
      {
        v11 = v5->m_MonsterInfo.m_dwKID;
        v12 = v10->m_MonsterInfo.m_dwKID;
        if ( v12 <= v11 )
        {
          if ( v12 < v11 )
            break;
          v13 = _Plast++;
          qmemcpy(v30, v13, sizeof(v30));
          qmemcpy(v13, v10, sizeof(CMonsterMgr::MonsterProtoType));
          qmemcpy(v10, v30, sizeof(CMonsterMgr::MonsterProtoType));
        }
      }
      v14 = _Glast;
      v15 = _Glast == _First._Myptr;
      if ( _Glast > _First._Myptr )
      {
        v16 = _Glast - 1;
        do
        {
          v17 = v16->m_MonsterInfo.m_dwKID;
          v18 = v5->m_MonsterInfo.m_dwKID;
          if ( v18 <= v17 )
          {
            if ( v18 < v17 )
              break;
            qmemcpy(v25, --v5, sizeof(v25));
            qmemcpy(v5, v16, sizeof(CMonsterMgr::MonsterProtoType));
            qmemcpy(v16, v25, sizeof(CMonsterMgr::MonsterProtoType));
          }
          v14 = _Glast - 1;
          --v16;
          v19 = _First._Myptr < &_Glast[-1];
          --_Glast;
        }
        while ( v19 );
        v15 = v14 == _First._Myptr;
      }
      if ( v15 )
        break;
      v21 = v14 - 1;
      _Glast = v21;
      if ( v10 == _Last._Myptr )
      {
        if ( v21 != --v5 )
        {
          qmemcpy(v27, v21, sizeof(v27));
          qmemcpy(v21, v5, sizeof(CMonsterMgr::MonsterProtoType));
          qmemcpy(v5, v27, sizeof(CMonsterMgr::MonsterProtoType));
        }
        --_Plast;
        qmemcpy(v29, v5, sizeof(v29));
        qmemcpy(v5, _Plast, sizeof(CMonsterMgr::MonsterProtoType));
        qmemcpy(_Plast, v29, sizeof(CMonsterMgr::MonsterProtoType));
      }
      else
      {
        qmemcpy(v31, v10, sizeof(v31));
        qmemcpy(v10++, v21, sizeof(CMonsterMgr::MonsterProtoType));
        qmemcpy(v21, v31, sizeof(CMonsterMgr::MonsterProtoType));
      }
    }
    if ( v10 == _Last._Myptr )
      break;
    if ( _Plast != v10 )
    {
      qmemcpy(v28, v5, sizeof(v28));
      qmemcpy(v5, _Plast, sizeof(CMonsterMgr::MonsterProtoType));
      qmemcpy(_Plast, v28, sizeof(CMonsterMgr::MonsterProtoType));
    }
    ++_Plast;
    v20 = v10;
    qmemcpy(v26, v5, sizeof(v26));
    qmemcpy(v5++, v10++, sizeof(CMonsterMgr::MonsterProtoType));
    qmemcpy(v20, v26, sizeof(CMonsterMgr::MonsterProtoType));
  }
  v22 = result;
  result->second._Myptr = _Plast;
  result->first._Myptr = v5;
  return v22;
}

//----- (00474560) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  int v2; // ebx
  CMonsterMgr::MonsterProtoType *v3; // ebp
  CMonsterMgr::MonsterProtoType v4; // [esp-170h] [ebp-180h] BYREF
  int _Lasta; // [esp+18h] [ebp+8h]

  _Lasta = _Last._Myptr - _First._Myptr;
  v2 = _Lasta / 2;
  if ( _Lasta / 2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
    {
      --v3;
      --v2;
      qmemcpy(&v4, v3, sizeof(v4));
      std::_Adjust_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        _First,
        v2,
        _Lasta,
        v4);
    }
    while ( v2 > 0 );
  }
}

//----- (004745E0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  CMonsterMgr::MonsterProtoType *i; // esi
  unsigned int m_dwKID; // ecx
  CMonsterMgr::MonsterProtoType *v4; // eax
  unsigned int v5; // ebp
  std::vector<CMonsterMgr::MonsterProtoType>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_dwKID = i->m_MonsterInfo.m_dwKID;
      if ( m_dwKID >= _First._Myptr->m_MonsterInfo.m_dwKID )
      {
        v4 = i - 1;
        if ( m_dwKID < i[-1].m_MonsterInfo.m_dwKID )
        {
          do
          {
            v5 = v4[-1].m_MonsterInfo.m_dwKID;
            v6._Myptr = v4--;
          }
          while ( m_dwKID < v5 );
          if ( v6._Myptr != i )
            std::_Rotate<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
              v6,
              (std::vector<CMonsterMgr::MonsterProtoType>::iterator)i,
              (std::vector<CMonsterMgr::MonsterProtoType>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
          _First,
          (std::vector<CMonsterMgr::MonsterProtoType>::iterator)i,
          (std::vector<CMonsterMgr::MonsterProtoType>::iterator)&i[1]);
      }
    }
  }
}

//----- (00474680) --------------------------------------------------------
CMonsterMgr::MonsterProtoType *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::_Ufill(
        std::vector<CMonsterMgr::MonsterProtoType> *this,
        CMonsterMgr::MonsterProtoType *_Ptr,
        unsigned int _Count,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  std::_Uninit_fill_n<CMonsterMgr::MonsterProtoType *,unsigned int,CMonsterMgr::MonsterProtoType,std::allocator<CMonsterMgr::MonsterProtoType>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (004746B0) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last)
{
  int i; // ebx
  CMonsterMgr::MonsterProtoType v3; // [esp-170h] [ebp-2F0h] BYREF
  _BYTE v4[368]; // [esp+10h] [ebp-170h] BYREF

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 368 > 1; i -= 368 )
  {
    qmemcpy(v4, (char *)&_First._Myptr[-1] + i, sizeof(v4));
    qmemcpy((char *)&_First._Myptr[-1] + i, _First._Myptr, sizeof(CMonsterMgr::MonsterProtoType));
    qmemcpy(&v3, v4, sizeof(v3));
    std::_Adjust_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
      _First,
      0,
      (i - 368) / 368,
      v3);
  }
}

//----- (00474770) --------------------------------------------------------
void __thiscall __noreturn std::vector<CMonsterMgr::MonsterProtoType>::_Xlen(
        std::vector<CMonsterMgr::MonsterProtoType> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004747E0) --------------------------------------------------------
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::_Insert_n(
        std::vector<CMonsterMgr::MonsterProtoType> *this,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Where,
        unsigned int _Count,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  CMonsterMgr::MonsterProtoType *Myfirst; // edi
  unsigned int v6; // ecx
  int v7; // eax
  int v8; // eax
  unsigned int v9; // ecx
  int v10; // eax
  unsigned int v11; // edi
  CMonsterMgr::MonsterProtoType *v12; // eax
  CMonsterMgr::MonsterProtoType *v13; // ecx
  CMonsterMgr::MonsterProtoType *v14; // eax
  char *v15; // esi
  CMonsterMgr::MonsterProtoType *v16; // eax
  CMonsterMgr::MonsterProtoType *v17; // esi
  CMonsterMgr::MonsterProtoType *Mylast; // ecx
  CMonsterMgr::MonsterProtoType *v20; // edx
  CMonsterMgr::MonsterProtoType *v21; // [esp-Ch] [ebp-1A4h]
  unsigned int v22; // [esp-8h] [ebp-1A0h]
  int v23; // [esp+0h] [ebp-198h] BYREF
  CMonsterMgr::MonsterProtoType *_Ptr; // [esp+Ch] [ebp-18Ch]
  CMonsterMgr::MonsterProtoType *_Newvec; // [esp+10h] [ebp-188h]
  CMonsterMgr::MonsterProtoType _Tmp; // [esp+14h] [ebp-184h] BYREF
  int *v27; // [esp+188h] [ebp-10h]
  int v28; // [esp+194h] [ebp-4h]
  CMonsterMgr::MonsterProtoType *_Wherea; // [esp+1A0h] [ebp+8h]
  CMonsterMgr::MonsterProtoType *_Vala; // [esp+1A8h] [ebp+10h]

  qmemcpy(&_Tmp, _Val, sizeof(_Tmp));
  Myfirst = this->_Myfirst;
  v27 = &v23;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v7 = this->_Mylast - this->_Myfirst;
    else
      v7 = 0;
    if ( 11671106 - v7 < _Count )
      std::vector<CMonsterMgr::MonsterProtoType>::_Xlen(this);
    if ( this->_Myfirst )
      v8 = this->_Mylast - this->_Myfirst;
    else
      v8 = 0;
    if ( v6 >= _Count + v8 )
    {
      Mylast = this->_Mylast;
      _Vala = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::copy_backward<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType *>(
          _Where._Myptr,
          _Wherea,
          _Vala);
        std::fill<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v22 = _Count - (this->_Mylast - _Where._Myptr);
        v21 = this->_Mylast;
        v28 = 2;
        std::vector<CMonsterMgr::MonsterProtoType>::_Ufill(this, v21, v22, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType>(_Where._Myptr, &v20[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 11671106 - (v6 >> 1) >= v6 )
        v9 = (v6 >> 1) + v6;
      else
        v9 = 0;
      if ( this->_Myfirst )
        v10 = this->_Mylast - this->_Myfirst;
      else
        v10 = 0;
      if ( v9 < _Count + v10 )
        v9 = (unsigned int)std::vector<CMonsterMgr::MonsterProtoType>::size(this) + _Count;
      v11 = v9;
      v12 = (CMonsterMgr::MonsterProtoType *)operator new((tagHeader *)(368 * v9));
      v13 = this->_Myfirst;
      _Newvec = v12;
      v28 = 0;
      _Ptr = std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
               v13,
               _Where._Myptr,
               v12);
      std::_Uninit_fill_n<CMonsterMgr::MonsterProtoType *,unsigned int,CMonsterMgr::MonsterProtoType,std::allocator<CMonsterMgr::MonsterProtoType>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v14 = this->_Myfirst;
      if ( v14 )
        v14 = (CMonsterMgr::MonsterProtoType *)(this->_Mylast - v14);
      v15 = (char *)v14 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      v16 = _Newvec;
      v17 = &_Newvec[(_DWORD)v15];
      this->_Myend = &_Newvec[v11];
      this->_Mylast = v17;
      this->_Myfirst = v16;
    }
  }
}

//----- (00474AD0) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int>(
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _First,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last,
        int _Ideal)
{
  CMonsterMgr::MonsterProtoType *Myptr; // ebx
  CMonsterMgr::MonsterProtoType *v4; // edi
  int v5; // eax
  std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)Myptr,
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
        &_Mid,
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)Myptr,
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v4);
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int>(
          _Mid.second,
          (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int>(
          (std::vector<CMonsterMgr::MonsterProtoType>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = _Mid.second._Myptr;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)Myptr,
        (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v4);
    std::sort_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)Myptr,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)v4);
  }
}
// 474BC0: conditional instruction was optimized away because eax.4>=21

//----- (00474C00) --------------------------------------------------------
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::reserve(
        std::vector<CMonsterMgr::MonsterProtoType> *this,
        CMonsterMgr::MonsterProtoType *_Count)
{
  CMonsterMgr::MonsterProtoType *Myfirst; // eax
  unsigned int v4; // ebx
  CMonsterMgr::MonsterProtoType *v5; // eax
  int v6; // edi
  CMonsterMgr::MonsterProtoType *v7; // [esp-18h] [ebp-34h]
  CMonsterMgr::MonsterProtoType *Mylast; // [esp-14h] [ebp-30h]
  _DWORD v9[7]; // [esp+0h] [ebp-1Ch] BYREF
  CMonsterMgr::MonsterProtoType *_Ptr; // [esp+24h] [ebp+8h]

  v9[3] = v9;
  if ( (unsigned int)_Count > 0xB21642 )
    std::vector<CMonsterMgr::MonsterProtoType>::_Xlen(this);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    Myfirst = (CMonsterMgr::MonsterProtoType *)(this->_Myend - Myfirst);
  if ( Myfirst < _Count )
  {
    v4 = (unsigned int)_Count;
    _Ptr = (CMonsterMgr::MonsterProtoType *)operator new((tagHeader *)(368 * (_DWORD)_Count));
    Mylast = this->_Mylast;
    v7 = this->_Myfirst;
    v9[6] = 0;
    std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(
      v7,
      Mylast,
      _Ptr);
    v5 = this->_Myfirst;
    if ( v5 )
      v6 = this->_Mylast - v5;
    else
      v6 = 0;
    if ( this->_Myfirst )
      operator delete(this->_Myfirst);
    this->_Myend = &_Ptr[v4];
    this->_Mylast = &_Ptr[v6];
    this->_Myfirst = _Ptr;
  }
}

//----- (00474D00) --------------------------------------------------------
std::vector<CMonsterMgr::MonsterProtoType>::iterator *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::insert(
        std::vector<CMonsterMgr::MonsterProtoType> *this,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator *result,
        std::vector<CMonsterMgr::MonsterProtoType>::iterator _Where,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  CMonsterMgr::MonsterProtoType *Myfirst; // esi
  int v6; // esi
  std::vector<CMonsterMgr::MonsterProtoType>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CMonsterMgr::MonsterProtoType>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (00474D70) --------------------------------------------------------
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::push_back(
        std::vector<CMonsterMgr::MonsterProtoType> *this,
        const CMonsterMgr::MonsterProtoType *_Val)
{
  CMonsterMgr::MonsterProtoType *Myfirst; // ebx
  unsigned int v4; // esi
  CMonsterMgr::MonsterProtoType *Mylast; // esi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<CMonsterMgr::MonsterProtoType *,unsigned int,CMonsterMgr::MonsterProtoType,std::allocator<CMonsterMgr::MonsterProtoType>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CMonsterMgr::MonsterProtoType>::insert(
      this,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator *)&_Val,
      (std::vector<CMonsterMgr::MonsterProtoType>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (00474E00) --------------------------------------------------------
char __thiscall CMonsterMgr::LoadMonstersFromFile(CMonsterMgr *this, const char *szFileName)
{
  int v2; // ebx
  const char *v3; // esi
  const char *v4; // eax
  int v5; // esi
  float *m_fHitBox; // edi
  int v7; // esi
  __int16 *m_nWeaponAttributeLevel; // edi
  int v9; // esi
  __int16 *m_nAttributeResistance; // edi
  unsigned int v11; // eax
  char *v12; // edi
  unsigned __int16 v14; // ax
  int v15; // ecx
  int v16; // esi
  __int16 *m_aryAwardItem; // edi
  int v18; // esi
  __int16 *m_aryDropRate; // edi
  CMonsterMgr::MonsterProtoType *Mylast; // esi
  CMonsterMgr::MonsterProtoType *Myfirst; // ebx
  int v22; // edi
  CMonsterMgr::MonsterProtoType *v23; // eax
  unsigned int v24; // esi
  CMonsterMgr::MonsterProtoType *v25; // eax
  int v26; // esi
  CMonsterMgr::MonsterProtoType *v27; // edi
  int v28; // esi
  CMonsterMgr::MonsterProtoType *v29; // edx
  CMonsterMgr::MonsterProtoType *v31; // eax
  int v32; // edx
  std::vector<CMonsterMgr::MonsterProtoType> monsterProtoTypeVector; // [esp+10h] [ebp-8400h] BYREF
  int nIndex; // [esp+20h] [ebp-83F0h]
  CMonsterMgr *v35; // [esp+24h] [ebp-83ECh]
  CMonsterMgr::MonsterProtoType tempProtoType; // [esp+28h] [ebp-83E8h] BYREF
  char strTemp[263]; // [esp+198h] [ebp-8278h] BYREF
  char v38; // [esp+29Fh] [ebp-8171h] BYREF
  char strString[264]; // [esp+2A0h] [ebp-8170h] BYREF
  CDelimitedFile DelimitedFile; // [esp+3A8h] [ebp-8068h] BYREF
  int v41; // [esp+840Ch] [ebp-4h]

  v35 = this;
  v2 = 0;
  CDelimitedFile::CDelimitedFile(&DelimitedFile, "\t");
  v41 = 1;
  memset(&monsterProtoTypeVector._Myfirst, 0, 12);
  std::vector<CMonsterMgr::MonsterProtoType>::reserve(&monsterProtoTypeVector, (CMonsterMgr::MonsterProtoType *)0x3E8);
  MonsterInfo::MonsterInfo(&tempProtoType.m_MonsterInfo);
  CreatureStatus::CreatureStatus(&tempProtoType.m_CreatureStatus);
  v3 = szFileName;
  v4 = szFileName;
  if ( !szFileName )
    v4 = CMonsterMgr::m_szMonsterScriptFileName;
  if ( !CDelimitedFile::Open(&DelimitedFile, v4, -1, 0) )
  {
    if ( !szFileName )
      v3 = CMonsterMgr::m_szMonsterScriptFileName;
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 100, aS_3, v3);
    if ( !monsterProtoTypeVector._Myfirst )
      goto LABEL_170;
LABEL_7:
    operator delete(monsterProtoTypeVector._Myfirst);
LABEL_170:
    v41 = -1;
    CDelimitedFile::~CDelimitedFile(&DelimitedFile);
    return 0;
  }
  if ( CDelimitedFile::ReadLine(&DelimitedFile) )
  {
    while ( 2 )
    {
      ++v2;
      if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_dwKID) )
      {
        if ( CDelimitedFile::ReadString(&DelimitedFile, tempProtoType.m_MonsterInfo.m_strName, 0x20u) )
        {
          if ( CDelimitedFile::ReadString(&DelimitedFile, tempProtoType.m_MonsterInfo.m_strModelingFlag, 0x20u) )
          {
            if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_cNation) )
            {
              if ( CDelimitedFile::ReadString(&DelimitedFile, strTemp, 0x104u) )
              {
                tempProtoType.m_MonsterInfo.m_cSkillPattern = MonsterInfo::GetMonsterPattern(strTemp);
                if ( CDelimitedFile::ReadString(&DelimitedFile, strTemp, 0x104u) )
                {
                  tempProtoType.m_MonsterInfo.m_bIgnoreEnchant = strcmp(strTemp, "O") == 0;
                  tempProtoType.m_MonsterInfo.m_MonsterMotions[0].m_wAction = 8;
                  if ( CDelimitedFile::ReadData(
                         &DelimitedFile,
                         &tempProtoType.m_MonsterInfo.m_MonsterMotions[0].m_dwFrame) )
                  {
                    if ( CDelimitedFile::ReadData(
                           &DelimitedFile,
                           &tempProtoType.m_MonsterInfo.m_MonsterMotions[0].m_fVelocity) )
                    {
                      tempProtoType.m_MonsterInfo.m_MonsterMotions[1].m_wAction = 1;
                      tempProtoType.m_MonsterInfo.m_MonsterMotions[0].m_fVelocity = tempProtoType.m_MonsterInfo.m_MonsterMotions[0].m_fVelocity
                                                                                  * 0.0099999998;
                      if ( CDelimitedFile::ReadData(
                             &DelimitedFile,
                             &tempProtoType.m_MonsterInfo.m_MonsterMotions[1].m_dwFrame) )
                      {
                        if ( CDelimitedFile::ReadData(
                               &DelimitedFile,
                               &tempProtoType.m_MonsterInfo.m_MonsterMotions[1].m_fVelocity) )
                        {
                          tempProtoType.m_MonsterInfo.m_MonsterMotions[2].m_wAction = 2;
                          tempProtoType.m_MonsterInfo.m_MonsterMotions[1].m_fVelocity = tempProtoType.m_MonsterInfo.m_MonsterMotions[1].m_fVelocity
                                                                                      * 0.0099999998;
                          if ( CDelimitedFile::ReadData(
                                 &DelimitedFile,
                                 &tempProtoType.m_MonsterInfo.m_MonsterMotions[2].m_dwFrame) )
                          {
                            tempProtoType.m_MonsterInfo.m_MonsterMotions[3].m_wAction = 24;
                            if ( CDelimitedFile::ReadData(
                                   &DelimitedFile,
                                   &tempProtoType.m_MonsterInfo.m_MonsterMotions[3].m_dwFrame) )
                            {
                              v5 = 0;
                              m_fHitBox = tempProtoType.m_MonsterInfo.m_fHitBox;
                              do
                              {
                                if ( !CDelimitedFile::ReadData(&DelimitedFile, m_fHitBox) )
                                {
                                  CServerLog::DetailLog(
                                    &g_Log,
                                    LOG_ERROR,
                                    "CMonsterMgr::LoadMonstersFromFile",
                                    aDWorkRylSource_79,
                                    140,
                                    (char *)&byte_4E73B8,
                                    v2,
                                    asc_4E7304);
                                  goto LABEL_106;
                                }
                                ++v5;
                                ++m_fHitBox;
                              }
                              while ( v5 < 4 );
                              if ( CDelimitedFile::ReadData(
                                     &DelimitedFile,
                                     &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nAttackRange) )
                              {
                                if ( CDelimitedFile::ReadData(
                                       &DelimitedFile,
                                       &tempProtoType.m_MonsterInfo.m_fAttackAngle) )
                                {
                                  if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_CreatureStatus.m_nExp) )
                                  {
                                    if ( CDelimitedFile::ReadData(
                                           &DelimitedFile,
                                           &tempProtoType.m_CreatureStatus.m_nLevel) )
                                    {
                                      if ( CDelimitedFile::ReadData(
                                             &DelimitedFile,
                                             &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMinDamage) )
                                      {
                                        if ( CDelimitedFile::ReadData(
                                               &DelimitedFile,
                                               &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMaxDamage) )
                                        {
                                          if ( CDelimitedFile::ReadData(
                                                 &DelimitedFile,
                                                 &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nOffenceRevision) )
                                          {
                                            if ( CDelimitedFile::ReadData(
                                                   &DelimitedFile,
                                                   &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nDefence) )
                                            {
                                              if ( CDelimitedFile::ReadData(
                                                     &DelimitedFile,
                                                     &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nDefenceRevision) )
                                              {
                                                if ( CDelimitedFile::ReadData(
                                                       &DelimitedFile,
                                                       &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage) )
                                                {
                                                  if ( CDelimitedFile::ReadData(
                                                         &DelimitedFile,
                                                         &tempProtoType.m_CreatureStatus.m_StatusInfo.m_fDRC) )
                                                  {
                                                    if ( CDelimitedFile::ReadData(
                                                           &DelimitedFile,
                                                           &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage) )
                                                    {
                                                      if ( CDelimitedFile::ReadData(
                                                             &DelimitedFile,
                                                             &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nCriticalType) )
                                                      {
                                                        if ( CDelimitedFile::ReadData(
                                                               &DelimitedFile,
                                                               &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMagicPower) )
                                                        {
                                                          if ( CDelimitedFile::ReadData(
                                                                 &DelimitedFile,
                                                                 &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMagicResistance) )
                                                          {
                                                            v7 = 0;
                                                            m_nWeaponAttributeLevel = tempProtoType.m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel;
                                                            do
                                                            {
                                                              if ( !CDelimitedFile::ReadData(
                                                                      &DelimitedFile,
                                                                      m_nWeaponAttributeLevel) )
                                                              {
                                                                CServerLog::DetailLog(
                                                                  &g_Log,
                                                                  LOG_ERROR,
                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                  aDWorkRylSource_79,
                                                                  158,
                                                                  (char *)&byte_4E73B8,
                                                                  v2,
                                                                  asc_4E7240);
                                                                goto LABEL_106;
                                                              }
                                                              ++v7;
                                                              ++m_nWeaponAttributeLevel;
                                                            }
                                                            while ( v7 < 5 );
                                                            v9 = 0;
                                                            m_nAttributeResistance = tempProtoType.m_CreatureStatus.m_StatusInfo.m_nAttributeResistance;
                                                            do
                                                            {
                                                              if ( !CDelimitedFile::ReadData(
                                                                      &DelimitedFile,
                                                                      m_nAttributeResistance) )
                                                              {
                                                                CServerLog::DetailLog(
                                                                  &g_Log,
                                                                  LOG_ERROR,
                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                  aDWorkRylSource_79,
                                                                  159,
                                                                  (char *)&byte_4E73B8,
                                                                  v2,
                                                                  asc_4E7234);
                                                                goto LABEL_106;
                                                              }
                                                              ++v9;
                                                              ++m_nAttributeResistance;
                                                            }
                                                            while ( v9 < 5 );
                                                            if ( CDelimitedFile::ReadData(
                                                                   &DelimitedFile,
                                                                   &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nAttackSpeed) )
                                                            {
                                                              if ( CDelimitedFile::ReadData(
                                                                     &DelimitedFile,
                                                                     &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMoveSpeed) )
                                                              {
                                                                if ( CDelimitedFile::ReadData(
                                                                       &DelimitedFile,
                                                                       (__int16 *)&tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMaxHP) )
                                                                {
                                                                  if ( CDelimitedFile::ReadData(
                                                                         &DelimitedFile,
                                                                         (__int16 *)&tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMaxMP) )
                                                                  {
                                                                    if ( CDelimitedFile::ReadData(
                                                                           &DelimitedFile,
                                                                           &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nHPRegenAmount) )
                                                                    {
                                                                      if ( CDelimitedFile::ReadData(
                                                                             &DelimitedFile,
                                                                             &tempProtoType.m_CreatureStatus.m_StatusInfo.m_nMPRegenAmount) )
                                                                      {
                                                                        if ( CDelimitedFile::ReadData(
                                                                               &DelimitedFile,
                                                                               &tempProtoType.m_MonsterInfo.m_fSize) )
                                                                        {
                                                                          if ( CDelimitedFile::ReadData(
                                                                                 &DelimitedFile,
                                                                                 (char *)&tempProtoType.m_MonsterInfo.m_cSkillLevel) )
                                                                          {
                                                                            if ( CDelimitedFile::ReadString(
                                                                                   &DelimitedFile,
                                                                                   strTemp,
                                                                                   0x104u) )
                                                                            {
                                                                              tempProtoType.m_MonsterInfo.m_bCollision = strcmp(strTemp, "O") == 0;
                                                                              if ( CDelimitedFile::ReadString(
                                                                                     &DelimitedFile,
                                                                                     strTemp,
                                                                                     0x104u) )
                                                                              {
                                                                                tempProtoType.m_MonsterInfo.m_bStealth = strcmp(strTemp, "O") == 0;
                                                                                if ( CDelimitedFile::ReadString(
                                                                                       &DelimitedFile,
                                                                                       strTemp,
                                                                                       0x104u) )
                                                                                {
                                                                                  tempProtoType.m_MonsterInfo.m_bFirstAttack = strcmp(strTemp, "O") == 0;
                                                                                  if ( CDelimitedFile::ReadString(
                                                                                         &DelimitedFile,
                                                                                         strTemp,
                                                                                         0x104u) )
                                                                                  {
                                                                                    tempProtoType.m_MonsterInfo.m_bEscape = strcmp(strTemp, "O") == 0;
                                                                                    if ( CDelimitedFile::ReadString(
                                                                                           &DelimitedFile,
                                                                                           strTemp,
                                                                                           0x104u) )
                                                                                    {
                                                                                      tempProtoType.m_MonsterInfo.m_bReturnPosition = strcmp(strTemp, "O") == 0;
                                                                                      if ( CDelimitedFile::ReadString(
                                                                                             &DelimitedFile,
                                                                                             strTemp,
                                                                                             0x104u) )
                                                                                      {
                                                                                        tempProtoType.m_MonsterInfo.m_bFixLevelGap = strcmp(strTemp, "O") == 0;
                                                                                        if ( CDelimitedFile::ReadData(
                                                                                               &DelimitedFile,
                                                                                               (char *)&tempProtoType.m_MonsterInfo.m_cFixLevelGap) )
                                                                                        {
                                                                                          if ( CDelimitedFile::ReadData(
                                                                                                 &DelimitedFile,
                                                                                                 (__int16 *)&tempProtoType.m_MonsterInfo.m_wSkillUseRate) )
                                                                                          {
                                                                                            nIndex = 0;
                                                                                            do
                                                                                            {
                                                                                              if ( !CDelimitedFile::ReadString(&DelimitedFile, strTemp, 0x104u) )
                                                                                              {
                                                                                                CServerLog::DetailLog(
                                                                                                  &g_Log,
                                                                                                  LOG_ERROR,
                                                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                                                  aDWorkRylSource_79,
                                                                                                  181,
                                                                                                  (char *)&byte_4E73B8,
                                                                                                  v2,
                                                                                                  asc_4E7140);
                                                                                                goto LABEL_106;
                                                                                              }
                                                                                              strcpy(
                                                                                                strString,
                                                                                                "0x0000");
                                                                                              memset(
                                                                                                &strString[7],
                                                                                                0,
                                                                                                253);
                                                                                              v11 = strlen(&strTemp[2]) + 1;
                                                                                              v12 = &v38;
                                                                                              while ( *++v12 )
                                                                                                ;
                                                                                              qmemcpy(
                                                                                                v12,
                                                                                                &strTemp[2],
                                                                                                v11);
                                                                                              v14 = Math::Convert::Atoi(strString);
                                                                                              v15 = nIndex;
                                                                                              tempProtoType.m_MonsterInfo.m_wSkillID[nIndex] = v14;
                                                                                              nIndex = v15 + 1;
                                                                                            }
                                                                                            while ( v15 + 1 < 5 );
                                                                                            if ( CDelimitedFile::ReadData(
                                                                                                   &DelimitedFile,
                                                                                                   &tempProtoType.m_MonsterInfo.m_dwEnchantSpellType) )
                                                                                            {
                                                                                              if ( CDelimitedFile::ReadData(
                                                                                                     &DelimitedFile,
                                                                                                     &tempProtoType.m_MonsterInfo.m_dwChantSpellType) )
                                                                                              {
                                                                                                tempProtoType.m_MonsterInfo.m_dwEnchantSpellType = 1 << (LOBYTE(tempProtoType.m_MonsterInfo.m_dwEnchantSpellType) - 1);
                                                                                                tempProtoType.m_MonsterInfo.m_dwChantSpellType = 1 << (LOBYTE(tempProtoType.m_MonsterInfo.m_dwChantSpellType) - 1);
                                                                                                if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fSkillEffectSize) )
                                                                                                {
                                                                                                  if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_dwRespawnTime) )
                                                                                                  {
                                                                                                    v16 = 0;
                                                                                                    m_aryAwardItem = (__int16 *)tempProtoType.m_MonsterInfo.m_aryAwardItem;
                                                                                                    do
                                                                                                    {
                                                                                                      if ( !CDelimitedFile::ReadData(&DelimitedFile, m_aryAwardItem) )
                                                                                                      {
                                                                                                        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 189, (char *)&byte_4E73B8, v2, asc_4E70E0);
                                                                                                        goto LABEL_106;
                                                                                                      }
                                                                                                      ++v16;
                                                                                                      ++m_aryAwardItem;
                                                                                                    }
                                                                                                    while ( v16 < 5 );
                                                                                                    v18 = 0;
                                                                                                    m_aryDropRate = (__int16 *)tempProtoType.m_MonsterInfo.m_aryDropRate;
                                                                                                    do
                                                                                                    {
                                                                                                      if ( !CDelimitedFile::ReadData(&DelimitedFile, m_aryDropRate) )
                                                                                                      {
                                                                                                        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 190, (char *)&byte_4E73B8, v2, asc_4E70D0);
                                                                                                        goto LABEL_106;
                                                                                                      }
                                                                                                      ++v18;
                                                                                                      ++m_aryDropRate;
                                                                                                    }
                                                                                                    while ( v18 < 13 );
                                                                                                    if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_dwDevelopGold) )
                                                                                                    {
                                                                                                      if ( CDelimitedFile::ReadData(&DelimitedFile, (char *)&tempProtoType.m_MonsterInfo.m_cDevelopSpeed) )
                                                                                                      {
                                                                                                        if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_dwUpgradeGold) )
                                                                                                        {
                                                                                                          if ( CDelimitedFile::ReadData(&DelimitedFile, (char *)&tempProtoType.m_MonsterInfo.m_cUpgradeSpeed) )
                                                                                                          {
                                                                                                            if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fDevelopGoldDown) )
                                                                                                            {
                                                                                                              if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fDevelopSpeedUp) )
                                                                                                              {
                                                                                                                if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fUpgradeGoldDown) )
                                                                                                                {
                                                                                                                  if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fUpgradeSpeedUp) )
                                                                                                                  {
                                                                                                                    if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fDefenseUp) )
                                                                                                                    {
                                                                                                                      if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fOffenseUp) )
                                                                                                                      {
                                                                                                                        if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fHPUp) )
                                                                                                                        {
                                                                                                                          if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fBonusRate) )
                                                                                                                          {
                                                                                                                            if ( CDelimitedFile::ReadData(&DelimitedFile, &tempProtoType.m_MonsterInfo.m_fRespawnSpeedUp) )
                                                                                                                            {
                                                                                                                              std::vector<CMonsterMgr::MonsterProtoType>::push_back(&monsterProtoTypeVector, &tempProtoType);
                                                                                                                              if ( CDelimitedFile::ReadLine(&DelimitedFile) )
                                                                                                                                continue;
                                                                                                                              goto LABEL_90;
                                                                                                                            }
                                                                                                                            CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 206, (char *)&byte_4E73B8, v2, aPc);
                                                                                                                          }
                                                                                                                          else
                                                                                                                          {
                                                                                                                            CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 205, (char *)&byte_4E73B8, v2, asc_4E6FF8);
                                                                                                                          }
                                                                                                                        }
                                                                                                                        else
                                                                                                                        {
                                                                                                                          CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 204, (char *)&byte_4E73B8, v2, aHp_0);
                                                                                                                        }
                                                                                                                      }
                                                                                                                      else
                                                                                                                      {
                                                                                                                        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 203, (char *)&byte_4E73B8, v2, asc_4E7018);
                                                                                                                      }
                                                                                                                    }
                                                                                                                    else
                                                                                                                    {
                                                                                                                      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 202, (char *)&byte_4E73B8, v2, asc_4E7028);
                                                                                                                    }
                                                                                                                  }
                                                                                                                  else
                                                                                                                  {
                                                                                                                    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 201, (char *)&byte_4E73B8, v2, asc_4E7038);
                                                                                                                  }
                                                                                                                }
                                                                                                                else
                                                                                                                {
                                                                                                                  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 200, (char *)&byte_4E73B8, v2, asc_4E7050);
                                                                                                                }
                                                                                                              }
                                                                                                              else
                                                                                                              {
                                                                                                                CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 199, (char *)&byte_4E73B8, v2, asc_4E7068);
                                                                                                              }
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                              CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 198, (char *)&byte_4E73B8, v2, asc_4E707C);
                                                                                                            }
                                                                                                          }
                                                                                                          else
                                                                                                          {
                                                                                                            CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 196, (char *)&byte_4E73B8, v2, asc_4E7090);
                                                                                                          }
                                                                                                        }
                                                                                                        else
                                                                                                        {
                                                                                                          CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 195, (char *)&byte_4E73B8, v2, asc_4E70A4);
                                                                                                        }
                                                                                                      }
                                                                                                      else
                                                                                                      {
                                                                                                        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 194, (char *)&byte_4E73B8, v2, asc_4E70B8);
                                                                                                      }
                                                                                                    }
                                                                                                    else
                                                                                                    {
                                                                                                      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 193, (char *)&byte_4E73B8, v2, asc_4E70C4);
                                                                                                    }
                                                                                                  }
                                                                                                  else
                                                                                                  {
                                                                                                    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterMgr::LoadMonstersFromFile", aDWorkRylSource_79, 188, (char *)&byte_4E73B8, v2, asc_4E70F4);
                                                                                                  }
                                                                                                }
                                                                                                else
                                                                                                {
                                                                                                  CServerLog::DetailLog(
                                                                                                    &g_Log,
                                                                                                    LOG_ERROR,
                                                                                                    "CMonsterMgr::LoadMonstersFromFile",
                                                                                                    aDWorkRylSource_79,
                                                                                                    186,
                                                                                                    (char *)&byte_4E73B8,
                                                                                                    v2,
                                                                                                    asc_4E7104);
                                                                                                }
                                                                                              }
                                                                                              else
                                                                                              {
                                                                                                CServerLog::DetailLog(
                                                                                                  &g_Log,
                                                                                                  LOG_ERROR,
                                                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                                                  aDWorkRylSource_79,
                                                                                                  183,
                                                                                                  (char *)&byte_4E73B8,
                                                                                                  v2,
                                                                                                  asc_4E7118);
                                                                                              }
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                              CServerLog::DetailLog(
                                                                                                &g_Log,
                                                                                                LOG_ERROR,
                                                                                                "CMonsterMgr::LoadMonstersFromFile",
                                                                                                aDWorkRylSource_79,
                                                                                                182,
                                                                                                (char *)&byte_4E73B8,
                                                                                                v2,
                                                                                                asc_4E712C);
                                                                                            }
                                                                                          }
                                                                                          else
                                                                                          {
                                                                                            CServerLog::DetailLog(
                                                                                              &g_Log,
                                                                                              LOG_ERROR,
                                                                                              "CMonsterMgr::LoadMonstersFromFile",
                                                                                              aDWorkRylSource_79,
                                                                                              180,
                                                                                              (char *)&byte_4E73B8,
                                                                                              v2,
                                                                                              asc_4E7150);
                                                                                          }
                                                                                        }
                                                                                        else
                                                                                        {
                                                                                          CServerLog::DetailLog(
                                                                                            &g_Log,
                                                                                            LOG_ERROR,
                                                                                            "CMonsterMgr::LoadMonstersFromFile",
                                                                                            aDWorkRylSource_79,
                                                                                            178,
                                                                                            (char *)&byte_4E73B8,
                                                                                            v2,
                                                                                            asc_4E7160);
                                                                                        }
                                                                                      }
                                                                                      else
                                                                                      {
                                                                                        CServerLog::DetailLog(
                                                                                          &g_Log,
                                                                                          LOG_ERROR,
                                                                                          "CMonsterMgr::LoadMonstersFromFile",
                                                                                          aDWorkRylSource_79,
                                                                                          177,
                                                                                          (char *)&byte_4E73B8,
                                                                                          v2,
                                                                                          asc_4E7170);
                                                                                      }
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                      CServerLog::DetailLog(
                                                                                        &g_Log,
                                                                                        LOG_ERROR,
                                                                                        "CMonsterMgr::LoadMonstersFromFile",
                                                                                        aDWorkRylSource_79,
                                                                                        175,
                                                                                        (char *)&byte_4E73B8,
                                                                                        v2,
                                                                                        asc_4E7188);
                                                                                    }
                                                                                  }
                                                                                  else
                                                                                  {
                                                                                    CServerLog::DetailLog(
                                                                                      &g_Log,
                                                                                      LOG_ERROR,
                                                                                      "CMonsterMgr::LoadMonstersFromFile",
                                                                                      aDWorkRylSource_79,
                                                                                      174,
                                                                                      (char *)&byte_4E73B8,
                                                                                      v2,
                                                                                      asc_4E7194);
                                                                                  }
                                                                                }
                                                                                else
                                                                                {
                                                                                  CServerLog::DetailLog(
                                                                                    &g_Log,
                                                                                    LOG_ERROR,
                                                                                    "CMonsterMgr::LoadMonstersFromFile",
                                                                                    aDWorkRylSource_79,
                                                                                    173,
                                                                                    (char *)&byte_4E73B8,
                                                                                    v2,
                                                                                    asc_4E71A0);
                                                                                }
                                                                              }
                                                                              else
                                                                              {
                                                                                CServerLog::DetailLog(
                                                                                  &g_Log,
                                                                                  LOG_ERROR,
                                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                                  aDWorkRylSource_79,
                                                                                  172,
                                                                                  (char *)&byte_4E73B8,
                                                                                  v2,
                                                                                  asc_4E71B0);
                                                                              }
                                                                            }
                                                                            else
                                                                            {
                                                                              CServerLog::DetailLog(
                                                                                &g_Log,
                                                                                LOG_ERROR,
                                                                                "CMonsterMgr::LoadMonstersFromFile",
                                                                                aDWorkRylSource_79,
                                                                                171,
                                                                                (char *)&byte_4E73B8,
                                                                                v2,
                                                                                asc_4E71C4);
                                                                            }
                                                                          }
                                                                          else
                                                                          {
                                                                            CServerLog::DetailLog(
                                                                              &g_Log,
                                                                              LOG_ERROR,
                                                                              "CMonsterMgr::LoadMonstersFromFile",
                                                                              aDWorkRylSource_79,
                                                                              169,
                                                                              (char *)&byte_4E73B8,
                                                                              v2,
                                                                              asc_4E71D4);
                                                                          }
                                                                        }
                                                                        else
                                                                        {
                                                                          CServerLog::DetailLog(
                                                                            &g_Log,
                                                                            LOG_ERROR,
                                                                            "CMonsterMgr::LoadMonstersFromFile",
                                                                            aDWorkRylSource_79,
                                                                            168,
                                                                            (char *)&byte_4E73B8,
                                                                            v2,
                                                                            asc_4E71E0);
                                                                        }
                                                                      }
                                                                      else
                                                                      {
                                                                        CServerLog::DetailLog(
                                                                          &g_Log,
                                                                          LOG_ERROR,
                                                                          "CMonsterMgr::LoadMonstersFromFile",
                                                                          aDWorkRylSource_79,
                                                                          165,
                                                                          (char *)&byte_4E73B8,
                                                                          v2,
                                                                          aMp);
                                                                      }
                                                                    }
                                                                    else
                                                                    {
                                                                      CServerLog::DetailLog(
                                                                        &g_Log,
                                                                        LOG_ERROR,
                                                                        "CMonsterMgr::LoadMonstersFromFile",
                                                                        aDWorkRylSource_79,
                                                                        164,
                                                                        (char *)&byte_4E73B8,
                                                                        v2,
                                                                        aHp);
                                                                    }
                                                                  }
                                                                  else
                                                                  {
                                                                    CServerLog::DetailLog(
                                                                      &g_Log,
                                                                      LOG_ERROR,
                                                                      "CMonsterMgr::LoadMonstersFromFile",
                                                                      aDWorkRylSource_79,
                                                                      163,
                                                                      (char *)&byte_4E73B8,
                                                                      v2,
                                                                      "\"MP Max\"");
                                                                  }
                                                                }
                                                                else
                                                                {
                                                                  CServerLog::DetailLog(
                                                                    &g_Log,
                                                                    LOG_ERROR,
                                                                    "CMonsterMgr::LoadMonstersFromFile",
                                                                    aDWorkRylSource_79,
                                                                    162,
                                                                    (char *)&byte_4E73B8,
                                                                    v2,
                                                                    "\"HP Max\"");
                                                                }
                                                              }
                                                              else
                                                              {
                                                                CServerLog::DetailLog(
                                                                  &g_Log,
                                                                  LOG_ERROR,
                                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                                  aDWorkRylSource_79,
                                                                  161,
                                                                  (char *)&byte_4E73B8,
                                                                  v2,
                                                                  asc_4E721C);
                                                              }
                                                            }
                                                            else
                                                            {
                                                              CServerLog::DetailLog(
                                                                &g_Log,
                                                                LOG_ERROR,
                                                                "CMonsterMgr::LoadMonstersFromFile",
                                                                aDWorkRylSource_79,
                                                                160,
                                                                (char *)&byte_4E73B8,
                                                                v2,
                                                                asc_4E7228);
                                                            }
                                                          }
                                                          else
                                                          {
                                                            CServerLog::DetailLog(
                                                              &g_Log,
                                                              LOG_ERROR,
                                                              "CMonsterMgr::LoadMonstersFromFile",
                                                              aDWorkRylSource_79,
                                                              157,
                                                              (char *)&byte_4E73B8,
                                                              v2,
                                                              asc_4E724C);
                                                          }
                                                        }
                                                        else
                                                        {
                                                          CServerLog::DetailLog(
                                                            &g_Log,
                                                            LOG_ERROR,
                                                            "CMonsterMgr::LoadMonstersFromFile",
                                                            aDWorkRylSource_79,
                                                            156,
                                                            (char *)&byte_4E73B8,
                                                            v2,
                                                            asc_4E7258);
                                                        }
                                                      }
                                                      else
                                                      {
                                                        CServerLog::DetailLog(
                                                          &g_Log,
                                                          LOG_ERROR,
                                                          "CMonsterMgr::LoadMonstersFromFile",
                                                          aDWorkRylSource_79,
                                                          155,
                                                          (char *)&byte_4E73B8,
                                                          v2,
                                                          asc_4E7264);
                                                      }
                                                    }
                                                    else
                                                    {
                                                      CServerLog::DetailLog(
                                                        &g_Log,
                                                        LOG_ERROR,
                                                        "CMonsterMgr::LoadMonstersFromFile",
                                                        aDWorkRylSource_79,
                                                        154,
                                                        (char *)&byte_4E73B8,
                                                        v2,
                                                        asc_4E7274);
                                                    }
                                                  }
                                                  else
                                                  {
                                                    CServerLog::DetailLog(
                                                      &g_Log,
                                                      LOG_ERROR,
                                                      "CMonsterMgr::LoadMonstersFromFile",
                                                      aDWorkRylSource_79,
                                                      153,
                                                      (char *)&byte_4E73B8,
                                                      v2,
                                                      "\"DRC\"");
                                                  }
                                                }
                                                else
                                                {
                                                  CServerLog::DetailLog(
                                                    &g_Log,
                                                    LOG_ERROR,
                                                    "CMonsterMgr::LoadMonstersFromFile",
                                                    aDWorkRylSource_79,
                                                    152,
                                                    (char *)&byte_4E73B8,
                                                    v2,
                                                    asc_4E7284);
                                                }
                                              }
                                              else
                                              {
                                                CServerLog::DetailLog(
                                                  &g_Log,
                                                  LOG_ERROR,
                                                  "CMonsterMgr::LoadMonstersFromFile",
                                                  aDWorkRylSource_79,
                                                  151,
                                                  (char *)&byte_4E73B8,
                                                  v2,
                                                  asc_4E7290);
                                              }
                                            }
                                            else
                                            {
                                              CServerLog::DetailLog(
                                                &g_Log,
                                                LOG_ERROR,
                                                "CMonsterMgr::LoadMonstersFromFile",
                                                aDWorkRylSource_79,
                                                150,
                                                (char *)&byte_4E73B8,
                                                v2,
                                                asc_4E729C);
                                            }
                                          }
                                          else
                                          {
                                            CServerLog::DetailLog(
                                              &g_Log,
                                              LOG_ERROR,
                                              "CMonsterMgr::LoadMonstersFromFile",
                                              aDWorkRylSource_79,
                                              149,
                                              (char *)&byte_4E73B8,
                                              v2,
                                              asc_4E72A8);
                                          }
                                        }
                                        else
                                        {
                                          CServerLog::DetailLog(
                                            &g_Log,
                                            LOG_ERROR,
                                            "CMonsterMgr::LoadMonstersFromFile",
                                            aDWorkRylSource_79,
                                            148,
                                            (char *)&byte_4E73B8,
                                            v2,
                                            asc_4E72B4);
                                        }
                                      }
                                      else
                                      {
                                        CServerLog::DetailLog(
                                          &g_Log,
                                          LOG_ERROR,
                                          "CMonsterMgr::LoadMonstersFromFile",
                                          aDWorkRylSource_79,
                                          147,
                                          (char *)&byte_4E73B8,
                                          v2,
                                          asc_4E72C4);
                                      }
                                    }
                                    else
                                    {
                                      CServerLog::DetailLog(
                                        &g_Log,
                                        LOG_ERROR,
                                        "CMonsterMgr::LoadMonstersFromFile",
                                        aDWorkRylSource_79,
                                        146,
                                        (char *)&byte_4E73B8,
                                        v2,
                                        asc_4E72D4);
                                    }
                                  }
                                  else
                                  {
                                    CServerLog::DetailLog(
                                      &g_Log,
                                      LOG_ERROR,
                                      "CMonsterMgr::LoadMonstersFromFile",
                                      aDWorkRylSource_79,
                                      145,
                                      (char *)&byte_4E73B8,
                                      v2,
                                      asc_4E72DC);
                                  }
                                }
                                else
                                {
                                  CServerLog::DetailLog(
                                    &g_Log,
                                    LOG_ERROR,
                                    "CMonsterMgr::LoadMonstersFromFile",
                                    aDWorkRylSource_79,
                                    142,
                                    (char *)&byte_4E73B8,
                                    v2,
                                    asc_4E72EC);
                                }
                              }
                              else
                              {
                                CServerLog::DetailLog(
                                  &g_Log,
                                  LOG_ERROR,
                                  "CMonsterMgr::LoadMonstersFromFile",
                                  aDWorkRylSource_79,
                                  141,
                                  (char *)&byte_4E73B8,
                                  v2,
                                  asc_4E72F8);
                              }
                            }
                            else
                            {
                              CServerLog::DetailLog(
                                &g_Log,
                                LOG_ERROR,
                                "CMonsterMgr::LoadMonstersFromFile",
                                aDWorkRylSource_79,
                                138,
                                (char *)&byte_4E73B8,
                                v2,
                                asc_4E7310);
                            }
                          }
                          else
                          {
                            CServerLog::DetailLog(
                              &g_Log,
                              LOG_ERROR,
                              "CMonsterMgr::LoadMonstersFromFile",
                              aDWorkRylSource_79,
                              135,
                              (char *)&byte_4E73B8,
                              v2,
                              asc_4E7320);
                          }
                        }
                        else
                        {
                          CServerLog::DetailLog(
                            &g_Log,
                            LOG_ERROR,
                            "CMonsterMgr::LoadMonstersFromFile",
                            aDWorkRylSource_79,
                            131,
                            (char *)&byte_4E73B8,
                            v2,
                            asc_4E7330);
                        }
                      }
                      else
                      {
                        CServerLog::DetailLog(
                          &g_Log,
                          LOG_ERROR,
                          "CMonsterMgr::LoadMonstersFromFile",
                          aDWorkRylSource_79,
                          129,
                          (char *)&byte_4E73B8,
                          v2,
                          asc_4E7340);
                      }
                    }
                    else
                    {
                      CServerLog::DetailLog(
                        &g_Log,
                        LOG_ERROR,
                        "CMonsterMgr::LoadMonstersFromFile",
                        aDWorkRylSource_79,
                        125,
                        (char *)&byte_4E73B8,
                        v2,
                        asc_4E7350);
                    }
                  }
                  else
                  {
                    CServerLog::DetailLog(
                      &g_Log,
                      LOG_ERROR,
                      "CMonsterMgr::LoadMonstersFromFile",
                      aDWorkRylSource_79,
                      123,
                      (char *)&byte_4E73B8,
                      v2,
                      asc_4E735C);
                  }
                }
                else
                {
                  CServerLog::DetailLog(
                    &g_Log,
                    LOG_ERROR,
                    "CMonsterMgr::LoadMonstersFromFile",
                    aDWorkRylSource_79,
                    119,
                    (char *)&byte_4E73B8,
                    v2,
                    asc_4E736C);
                }
              }
              else
              {
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "CMonsterMgr::LoadMonstersFromFile",
                  aDWorkRylSource_79,
                  116,
                  (char *)&byte_4E73B8,
                  v2,
                  asc_4E7380);
              }
LABEL_106:
              std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&monsterProtoTypeVector);
              goto LABEL_170;
            }
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CMonsterMgr::LoadMonstersFromFile",
              aDWorkRylSource_79,
              114,
              (char *)&byte_4E73B8,
              v2,
              "\"Nation\"");
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CMonsterMgr::LoadMonstersFromFile",
              aDWorkRylSource_79,
              112,
              (char *)&byte_4E73B8,
              v2,
              asc_4E7398);
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CMonsterMgr::LoadMonstersFromFile",
            aDWorkRylSource_79,
            111,
            (char *)&byte_4E73B8,
            v2,
            asc_4E73B0);
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CMonsterMgr::LoadMonstersFromFile",
          aDWorkRylSource_79,
          109,
          (char *)&byte_4E73B8,
          v2,
          "\"KID\"");
      }
      break;
    }
    if ( !monsterProtoTypeVector._Myfirst )
      goto LABEL_170;
    goto LABEL_7;
  }
LABEL_90:
  Mylast = monsterProtoTypeVector._Mylast;
  Myfirst = monsterProtoTypeVector._Myfirst;
  v22 = monsterProtoTypeVector._Mylast - monsterProtoTypeVector._Myfirst;
  std::_Sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int>(
    (std::vector<CMonsterMgr::MonsterProtoType>::iterator)monsterProtoTypeVector._Myfirst,
    (std::vector<CMonsterMgr::MonsterProtoType>::iterator)monsterProtoTypeVector._Mylast,
    v22);
  v23 = Myfirst;
  if ( Myfirst != &Mylast[-1] )
  {
    while ( v23->m_MonsterInfo.m_dwKID != v23[1].m_MonsterInfo.m_dwKID )
    {
      if ( ++v23 == &Mylast[-1] )
        goto LABEL_93;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonsterMgr::LoadMonstersFromFile",
      aDWorkRylSource_79,
      218,
      (char *)&byte_4E6FB4,
      v23->m_MonsterInfo.m_dwKID);
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_170;
  }
LABEL_93:
  v24 = Myfirst != 0 ? v22 : 0;
  v35->m_nMonsterNum = v24;
  v25 = (CMonsterMgr::MonsterProtoType *)operator new[](368 * v24);
  nIndex = (int)v25;
  LOBYTE(v41) = 2;
  if ( v25 )
  {
    v26 = v24 - 1;
    v27 = v25;
    if ( v26 >= 0 )
    {
      v28 = v26 + 1;
      do
      {
        MonsterInfo::MonsterInfo(&v27->m_MonsterInfo);
        CreatureStatus::CreatureStatus(&v27->m_CreatureStatus);
        ++v27;
        --v28;
      }
      while ( v28 );
      v25 = (CMonsterMgr::MonsterProtoType *)nIndex;
    }
    v29 = v25;
  }
  else
  {
    v29 = 0;
  }
  LOBYTE(v41) = 1;
  v35->m_ProtoTypeArray = v29;
  if ( !v29 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonsterMgr::LoadMonstersFromFile",
      aDWorkRylSource_79,
      227,
      (char *)&byte_4E6F88);
    if ( Myfirst )
      operator delete(Myfirst);
    goto LABEL_170;
  }
  v31 = Myfirst;
  if ( Myfirst != monsterProtoTypeVector._Mylast )
  {
    v32 = (char *)v29 - (char *)Myfirst;
    do
    {
      qmemcpy((char *)v31 + v32, v31, sizeof(CMonsterMgr::MonsterProtoType));
      ++v31;
    }
    while ( v31 != monsterProtoTypeVector._Mylast );
  }
  if ( Myfirst )
    operator delete(Myfirst);
  v41 = -1;
  CDelimitedFile::~CDelimitedFile(&DelimitedFile);
  return 1;
}
// 4E6FF8: using guessed type char asc_4E6FF8;
// 4E7018: using guessed type char asc_4E7018;
// 4E7028: using guessed type char asc_4E7028;
// 4E7038: using guessed type char asc_4E7038;
// 4E7050: using guessed type char asc_4E7050;
// 4E7068: using guessed type char asc_4E7068;
// 4E707C: using guessed type char asc_4E707C;
// 4E7090: using guessed type char asc_4E7090;
// 4E70A4: using guessed type char asc_4E70A4;
// 4E70B8: using guessed type char asc_4E70B8;
// 4E70C4: using guessed type char asc_4E70C4;
// 4E70D0: using guessed type char asc_4E70D0;
// 4E70E0: using guessed type char asc_4E70E0;
// 4E70F4: using guessed type char asc_4E70F4;
// 4E7104: using guessed type char asc_4E7104;
// 4E7118: using guessed type char asc_4E7118;
// 4E712C: using guessed type char asc_4E712C;
// 4E7140: using guessed type char asc_4E7140;
// 4E7150: using guessed type char asc_4E7150;
// 4E7160: using guessed type char asc_4E7160;
// 4E7170: using guessed type char asc_4E7170;
// 4E7188: using guessed type char asc_4E7188;
// 4E7194: using guessed type char asc_4E7194;
// 4E71A0: using guessed type char asc_4E71A0;
// 4E71B0: using guessed type char asc_4E71B0;
// 4E71C4: using guessed type char asc_4E71C4;
// 4E71D4: using guessed type char asc_4E71D4;
// 4E71E0: using guessed type char asc_4E71E0;
// 4E721C: using guessed type char asc_4E721C;
// 4E7228: using guessed type char asc_4E7228;
// 4E7234: using guessed type char asc_4E7234;
// 4E7240: using guessed type char asc_4E7240;
// 4E724C: using guessed type char asc_4E724C;
// 4E7258: using guessed type char asc_4E7258;
// 4E7264: using guessed type char asc_4E7264;
// 4E7274: using guessed type char asc_4E7274;
// 4E7284: using guessed type char asc_4E7284;
// 4E7290: using guessed type char asc_4E7290;
// 4E729C: using guessed type char asc_4E729C;
// 4E72A8: using guessed type char asc_4E72A8;
// 4E72B4: using guessed type char asc_4E72B4;
// 4E72C4: using guessed type char asc_4E72C4;
// 4E72D4: using guessed type char asc_4E72D4;
// 4E72DC: using guessed type char asc_4E72DC;
// 4E72EC: using guessed type char asc_4E72EC;
// 4E72F8: using guessed type char asc_4E72F8;
// 4E7304: using guessed type char asc_4E7304;
// 4E7310: using guessed type char asc_4E7310;
// 4E7320: using guessed type char asc_4E7320;
// 4E7330: using guessed type char asc_4E7330;
// 4E7340: using guessed type char asc_4E7340;
// 4E7350: using guessed type char asc_4E7350;
// 4E735C: using guessed type char asc_4E735C;
// 4E736C: using guessed type char asc_4E736C;
// 4E7380: using guessed type char asc_4E7380;
// 4E7398: using guessed type char asc_4E7398;
// 4E73B0: using guessed type char asc_4E73B0;

//----- (00476020) --------------------------------------------------------
POS *__cdecl CharCreate::GetDefaultCharacterPos(POS *result, unsigned int dwRace, unsigned int dwNationPlayerNum)
{
  unsigned int v3; // esi
  unsigned int v4; // eax
  int v5; // ecx
  float fPointX; // esi
  POS *v7; // eax
  unsigned int v8; // eax
  float fPointY; // esi
  DWORD TickCount; // eax
  DWORD v11; // eax
  double v12; // st7
  float StartPos; // [esp+8h] [ebp-Ch]
  float StartPosa; // [esp+8h] [ebp-Ch]
  float StartPos_8; // [esp+10h] [ebp-4h]
  float StartPos_8a; // [esp+10h] [ebp-4h]

  if ( CServerSetup::GetInstance()->m_bBattleAuth || CServerSetup::GetInstance()->m_bBattleGame )
  {
    v8 = Math::Random::ComplexRandom(2, 0);
    fPointY = BGServerStartPos[dwRace][v8].fPointY;
    StartPos = BGServerStartPos[dwRace][v8].fPointX;
    StartPos_8 = BGServerStartPos[dwRace][v8].fPointZ;
    TickCount = GetTickCount();
    StartPosa = (double)(Math::Random::SimpleRandom(TickCount, 20, 0) - 10) + StartPos;
    v11 = GetTickCount();
    v12 = (double)(Math::Random::SimpleRandom(v11, 20, 0) - 10);
    v7 = result;
    result->fPointX = StartPosa;
    result->fPointY = fPointY;
    StartPos_8a = v12 + StartPos_8;
    result->fPointZ = StartPos_8a;
  }
  else
  {
    v3 = dwRace;
    v4 = 0;
    if ( dwRace >= 2 )
      v3 = 0;
    while ( dwNationPlayerNum >= StartPointVariation[v4] )
    {
      if ( ++v4 >= 6 )
        goto LABEL_10;
    }
    if ( v4 )
      v4 = Math::Random::ComplexRandom(v4 + 1, 0);
LABEL_10:
    v5 = 12 * (v4 + 6 * v3) + 5141704;
    fPointX = StartPosNum[v3][v4].fPointX;
    v7 = result;
    result->fPointX = fPointX;
    result->fPointY = *(float *)(v5 + 4);
    result->fPointZ = *(float *)(v5 + 8);
  }
  return v7;
}

//----- (00476140) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharClassUpgrade(
        CSendStream *SendStream,
        unsigned int dwCharID,
        CharacterDBData *DBData,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x22);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 6) = dwCharID;
  Buffer[33] = DBData->m_Info.Class;
  Buffer[32] = DBData->m_Info.Level;
  *((_DWORD *)Buffer + 7) = DBData->m_Info.Fame;
  *((_WORD *)Buffer + 6) = DBData->m_Info.IP;
  *((_WORD *)Buffer + 7) = DBData->m_Info.STR;
  *((_WORD *)Buffer + 8) = DBData->m_Info.DEX;
  *((_WORD *)Buffer + 9) = DBData->m_Info.CON;
  *((_WORD *)Buffer + 10) = DBData->m_Info.INT;
  *((_WORD *)Buffer + 11) = DBData->m_Info.WIS;
  return CSendStream::WrapCrypt(SendStream, 0x22u, 0x19u, 0, usError);
}

//----- (004761C0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharIncreasePoint(
        CSendStream *SendStream,
        unsigned int dwCharID,
        ChState State,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1C);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 6) = dwCharID;
  *((ChState *)Buffer + 1) = State;
  return CSendStream::WrapCrypt(SendStream, 0x1Cu, 0x1Bu, 0, usError);
}

//----- (00476210) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharLevelUp(
        CSendStream *SendStream,
        unsigned int dwCharID,
        CharacterDBData *DBData)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1D);
  if ( Buffer )
  {
    *((_DWORD *)Buffer + 6) = dwCharID;
    Buffer[28] = DBData->m_Info.Level;
    *((_WORD *)Buffer + 6) = DBData->m_Info.IP;
    *((_WORD *)Buffer + 7) = DBData->m_Info.STR;
    *((_WORD *)Buffer + 8) = DBData->m_Info.DEX;
    *((_WORD *)Buffer + 9) = DBData->m_Info.CON;
    *((_WORD *)Buffer + 10) = DBData->m_Info.INT;
    *((_WORD *)Buffer + 11) = DBData->m_Info.WIS;
    return CSendStream::WrapCrypt(SendStream, 0x1Du, 0x3Au, 0, 0);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharLevelUp",
      aDWorkRylSource_102,
      62,
      aCid0x08x_129,
      dwCharID);
    return 0;
  }
}

//----- (004762A0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharAward(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwExp)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x14);
  if ( Buffer )
  {
    *((_DWORD *)Buffer + 3) = dwCharID;
    *((_DWORD *)Buffer + 4) = dwExp;
    return CSendStream::WrapCrypt(SendStream, 0x14u, 0x32u, 0, 0);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientSendPacket::SendCharAward",
      aDWorkRylSource_102,
      87,
      aCid0x08x_129,
      dwCharID);
    return 0;
  }
}

//----- (00476300) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharStateRedistribution(
        CSendStream *SendStream,
        unsigned int dwCharID,
        CharacterDBData *DBData,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x6C);
  if ( !Buffer )
    return 0;
  *((_WORD *)Buffer + 6) = DBData->m_Info.IP;
  *((_WORD *)Buffer + 7) = DBData->m_Info.STR;
  *((_WORD *)Buffer + 8) = DBData->m_Info.DEX;
  *((_WORD *)Buffer + 9) = DBData->m_Info.CON;
  *((_WORD *)Buffer + 10) = DBData->m_Info.INT;
  *((_WORD *)Buffer + 11) = DBData->m_Info.WIS;
  qmemcpy(Buffer + 24, &DBData->m_Skill, 0x54u);
  return CSendStream::WrapCrypt(SendStream, 0x6Cu, 0x74u, 0, usError);
}

//----- (00476370) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharStatusRetrain(
        CSendStream *SendStream,
        unsigned int dwCharID,
        CharacterDBData *DBData,
        Item::ItemPos InvenPos,
        unsigned int dwGold,
        unsigned __int16 usError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x72);
  if ( !Buffer )
    return 0;
  *((_WORD *)Buffer + 6) = DBData->m_Info.IP;
  *((_WORD *)Buffer + 7) = DBData->m_Info.STR;
  *((_WORD *)Buffer + 8) = DBData->m_Info.DEX;
  *((_WORD *)Buffer + 9) = DBData->m_Info.CON;
  *((_WORD *)Buffer + 10) = DBData->m_Info.INT;
  *((_WORD *)Buffer + 11) = DBData->m_Info.WIS;
  qmemcpy(Buffer + 24, &DBData->m_Skill, 0x54u);
  *((Item::ItemPos *)Buffer + 54) = InvenPos;
  *(_DWORD *)(Buffer + 110) = dwGold;
  return CSendStream::WrapCrypt(SendStream, 0x72u, 0x94u, 0, usError);
}

//----- (00476400) --------------------------------------------------------
void __thiscall AwardTable::CAward::CAward(AwardTable::CAward *this)
{
  CSingleton<AwardTable::CAward>::ms_pSingleton = this;
}

//----- (00476410) --------------------------------------------------------
void __thiscall AwardTable::CAward::~CAward(AwardTable::CAward *this)
{
  CSingleton<AwardTable::CAward>::ms_pSingleton = 0;
}

//----- (00476420) --------------------------------------------------------
unsigned int __thiscall AwardTable::CAward::GetAwardCoin(
        AwardTable::CAward *this,
        unsigned int nDeadMonsterLevel,
        unsigned int bPeaceMode)
{
  unsigned int m_dwDrop; // esi
  unsigned int *p_bPeaceMode; // eax

  m_dwDrop = CServerSetup::GetInstance()->m_dwDrop;
  nDeadMonsterLevel = (unsigned __int64)((double)Math::Random::ComplexRandom(
                                                   6 * nDeadMonsterLevel + 21,
                                                   3 * nDeadMonsterLevel)
                                       * ((double)m_dwDrop
                                        * 0.0099999998)
                                       * (double)(((_BYTE)bPeaceMode == 0) + 1));
  bPeaceMode = 0xFFFFFFF;
  p_bPeaceMode = &bPeaceMode;
  if ( nDeadMonsterLevel <= 0xFFFFFFF )
    p_bPeaceMode = &nDeadMonsterLevel;
  return *p_bPeaceMode + 0x80000000;
}

//----- (004764B0) --------------------------------------------------------
unsigned int __thiscall AwardTable::CAward::GetAwardGem(AwardTable::CAward *this, int nDeadMonsterLevel)
{
  int v2; // esi
  int v3; // eax
  const __int16 *v4; // edi
  const __int16 *v5; // ecx
  int i; // eax
  int v7; // edx
  signed int v8; // eax
  signed int v9; // ecx
  int v10; // esi

  v2 = 0;
  v3 = (nDeadMonsterLevel - 1) / 10;
  v4 = aryGemDropTable[v3];
  v5 = aryGemDropTable[v3];
  for ( i = v3 * 6 + 5142350; v5 != (const __int16 *)i; v2 += v7 )
    v7 = *v5++;
  v8 = Math::Random::ComplexRandom(v2, 0);
  v9 = 0;
  v10 = 0;
  while ( 1 )
  {
    v9 += *v4;
    if ( v9 > v8 )
      break;
    ++v10;
    ++v4;
    if ( v10 >= 3 )
      return 0;
  }
  return Math::Random::ComplexRandom(5, 0) + 5 * v10 + 1901;
}

//----- (00476540) --------------------------------------------------------
int __thiscall AwardTable::CAward::GetAwardMetal(AwardTable::CAward *this, int nDeadMonsterLevel)
{
  int v2; // esi
  int v3; // eax
  const __int16 *v4; // edi
  const __int16 *v5; // ecx
  int i; // eax
  int v7; // edx
  signed int v8; // eax
  signed int v9; // edx
  int v10; // ecx

  v2 = 0;
  v3 = (nDeadMonsterLevel - 1) / 10;
  v4 = aryMetalDropTable[v3];
  v5 = aryMetalDropTable[v3];
  for ( i = v3 * 6 + 5142410; v5 != (const __int16 *)i; v2 += v7 )
    v7 = *v5++;
  v8 = Math::Random::ComplexRandom(v2, 0);
  v9 = 0;
  v10 = 0;
  while ( 1 )
  {
    v9 += *v4;
    if ( v9 > v8 )
      break;
    ++v10;
    ++v4;
    if ( v10 >= 3 )
      return 0;
  }
  return v10 + 2001;
}

//----- (004765C0) --------------------------------------------------------
int __thiscall AwardTable::CAward::GetAwardPotion(AwardTable::CAward *this, int nDeadMonsterLevel)
{
  int v2; // esi
  int v3; // eax
  const __int16 *v4; // edi
  const __int16 *v5; // ecx
  int i; // eax
  int v7; // edx
  signed int v8; // eax
  signed int v9; // edx
  int v10; // ecx

  v2 = 0;
  v3 = (nDeadMonsterLevel - 1) / 10;
  v4 = aryPotionDropTable[v3];
  v5 = aryPotionDropTable[v3];
  for ( i = v3 * 6 + 5142470; v5 != (const __int16 *)i; v2 += v7 )
    v7 = *v5++;
  v8 = Math::Random::ComplexRandom(v2, 0);
  v9 = 0;
  v10 = 0;
  while ( 1 )
  {
    v9 += *v4;
    if ( v9 > v8 )
      break;
    ++v10;
    ++v4;
    if ( v10 >= 3 )
      return 0;
  }
  return v10 + 3020;
}

//----- (00476640) --------------------------------------------------------
unsigned int __thiscall AwardTable::CAward::GetAwardLottery(AwardTable::CAward *this)
{
  if ( CServerSetup::GetInstance()->m_bLotteryEvent )
    return CLotteryEvent::GetLottery(&CSingleton<CGameEventMgr>::ms_pSingleton->m_LotteryEvent);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "AwardTable::CAward::GetAwardLottery",
    aDWorkRylSource_72,
    273,
    (char *)&byte_4EDAA8);
  return 0;
}

//----- (00476690) --------------------------------------------------------
void __cdecl std::_Random_shuffle<int *,int>(int *_First, int *_Last)
{
  int *v2; // ebx
  unsigned int i; // edi
  unsigned int v4; // esi
  unsigned int j; // eax
  int v6; // edx
  int v7; // ecx
  int *v8; // eax

  v2 = _First + 1;
  for ( i = 2; v2 != _Last; *v8 = v7 )
  {
    v4 = 0x7FFF;
    for ( j = rand() & 0x7FFF; v4 < i; j = (j << 15) | 0x7FFF )
    {
      if ( v4 == -1 )
        break;
      v4 = (v4 << 15) | 0x7FFF;
    }
    v6 = j % i;
    v7 = *v2++;
    ++i;
    v8 = &_First[v6];
    *(v2 - 1) = *v8;
  }
}

//----- (00476700) --------------------------------------------------------
unsigned int __thiscall AwardTable::CAward::GetAwardSkill(
        AwardTable::CAward *this,
        __int16 nDeadMonsterLevel,
        char cAttackCharacterNation)
{
  int v3; // esi
  __int16 v4; // ax
  unsigned __int16 v5; // dx
  BOOL v6; // ebx
  int *v7; // ecx
  const unsigned __int16 *v8; // eax
  int v9; // ebx
  unsigned __int16 v10; // bp
  int v11; // esi
  int arySkillShuffleList[69]; // [esp+14h] [ebp-114h] BYREF
  _UNKNOWN *retaddr; // [esp+128h] [ebp+0h] BYREF

  v3 = (__int16)(nDeadMonsterLevel - 1) / 10;
  v4 = Math::Random::ComplexRandom(3 * arySkillBookDropTable[v3], 0);
  LOWORD(v3) = arySkillBookDropTable[v3];
  v5 = v4 % (__int16)v3;
  v6 = v4 < (__int16)v3;
  v7 = arySkillShuffleList;
  v8 = aryDropableSkillbookList;
  v9 = v6 + 1;
  v10 = v5;
  do
    *v7++ = *v8++;
  while ( v8 != (const unsigned __int16 *)&unk_4E789A );
  std::_Random_shuffle<int *,int>(arySkillShuffleList, (int *)&retaddr);
  v11 = 0;
  while ( (unsigned __int8)(BYTE1(arySkillShuffleList[v11]) & 0x7F) >> 4 != cAttackCharacterNation
       || CClass::GetJobLevel(BYTE1(arySkillShuffleList[v11]) & 0x7F) != v9 )
  {
    if ( ++v11 >= 69 )
      return 0;
  }
  return Item::CItemMgr::GetItemIDFromSkillID(CSingleton<Item::CItemMgr>::ms_pSingleton, arySkillShuffleList[v11], v10);
}

//----- (00476800) --------------------------------------------------------
int __thiscall AwardTable::CAward::GetAwardEquipment(
        AwardTable::CAward *this,
        int nDeadMonsterLevel,
        char cAttackCharacterNation,
        unsigned __int8 cAttackCharacterClass)
{
  int v4; // ebp
  int *p_nDeadMonsterLevel; // eax
  int *v6; // eax
  int v7; // esi
  unsigned __int8 v8; // al
  unsigned __int8 v9; // bl
  int v10; // eax
  const Item::ItemInfo *ItemInfo; // eax
  const Item::ItemInfo *v12; // edi
  unsigned __int8 m_cItemType; // al
  int i; // ecx
  unsigned __int8 v15; // dl
  int v16; // edx
  unsigned __int8 v17; // cl
  char v18; // dl
  char v19; // dl
  unsigned __int8 cDropItemOrder; // [esp+Bh] [ebp-45h]
  int v22; // [esp+Ch] [ebp-44h] BYREF
  int aryShuffleList[16]; // [esp+10h] [ebp-40h] BYREF
  _UNKNOWN *retaddr; // [esp+50h] [ebp+0h] BYREF

  v4 = 0;
  v22 = 0;
  p_nDeadMonsterLevel = &nDeadMonsterLevel;
  if ( nDeadMonsterLevel <= 0 )
    p_nDeadMonsterLevel = &v22;
  nDeadMonsterLevel = *p_nDeadMonsterLevel;
  v22 = 99;
  v6 = &v22;
  if ( nDeadMonsterLevel <= 99 )
    v6 = &nDeadMonsterLevel;
  v7 = *v6;
  nDeadMonsterLevel = *v6;
  v8 = Math::Random::ComplexRandom(100, 0);
  cDropItemOrder = 3;
  if ( v8 < 0x50u )
    cDropItemOrder = 2;
  if ( v8 < 0x32u )
    cDropItemOrder = 1;
  if ( !cAttackCharacterNation )
  {
    memmove(
      (unsigned __int8 *)aryShuffleList,
      (unsigned __int8 *)((v7 << 6) + 5154480),
      4 * ((int)&aryDropableHumanEquipList[-80539][4] >> 2));
LABEL_13:
    std::_Random_shuffle<int *,int>(aryShuffleList, (int *)&retaddr);
    v9 = cAttackCharacterClass;
    while ( 1 )
    {
      v10 = aryShuffleList[v4];
      if ( v10 )
      {
        ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, v10);
        v12 = ItemInfo;
        if ( ItemInfo )
        {
          m_cItemType = ItemInfo->m_DetailData.m_cItemType;
          for ( i = 0; i < 8; ++i )
          {
            v15 = aryFirstDropableEquipList[v9][i];
            if ( v15 == 57 )
              break;
            if ( v15 == m_cItemType )
            {
              v19 = 1;
              goto LABEL_26;
            }
          }
          v16 = 0;
          while ( 1 )
          {
            v17 = arySecondDropableEquipList[v9][v16];
            if ( v17 == 57 )
            {
LABEL_24:
              v18 = 3;
              goto LABEL_27;
            }
            if ( v17 == m_cItemType )
              break;
            if ( ++v16 >= 8 )
              goto LABEL_24;
          }
          v19 = 2;
LABEL_26:
          v18 = ((v12->m_UseLimit.m_dwClassLimit & (1 << (v9 - 1))) == 0) + v19;
LABEL_27:
          if ( cDropItemOrder == v18 )
            return aryShuffleList[v4] + ((nDeadMonsterLevel + 0x4000) << 16);
          v7 = nDeadMonsterLevel;
        }
      }
      if ( ++v4 >= 16 )
        return aryShuffleList[0] + ((v7 + 0x4000) << 16);
    }
  }
  if ( cAttackCharacterNation == 1 )
  {
    memmove(
      (unsigned __int8 *)aryShuffleList,
      (unsigned __int8 *)((v7 << 6) + 5160816),
      4 * ((int)&aryDropableAkhanEquipList[-80638][4] >> 2));
    goto LABEL_13;
  }
  return 0;
}

//----- (004769B0) --------------------------------------------------------
unsigned __int16 __thiscall AwardTable::CAward::GetBlackMarketItem(
        AwardTable::CAward *this,
        unsigned __int16 usMainItemID)
{
  int v2; // eax
  int aryShuffleList[13]; // [esp+Ch] [ebp-34h] BYREF
  _UNKNOWN *retaddr; // [esp+40h] [ebp+0h] BYREF

  memmove(
    (unsigned __int8 *)aryShuffleList,
    (unsigned __int8 *)aryBlackMarketItemList[113],
    4 * ((5142740 - (int)aryBlackMarketItemList) >> 2));
  std::_Random_shuffle<int *,int>(aryShuffleList, (int *)&retaddr);
  v2 = 0;
  while ( !aryShuffleList[v2] )
  {
    if ( ++v2 >= 13 )
      return 0;
  }
  return usMainItemID;
}

//----- (00476A60) --------------------------------------------------------
unsigned __int16 __thiscall CGameEventMgr::PopDropEventItem(CGameEventMgr *this)
{
  unsigned __int16 result; // ax
  std::_List_nod<unsigned short>::_Node *Myhead; // eax
  unsigned __int16 Myval; // di
  std::_List_nod<unsigned short>::_Node *Next; // eax

  result = 0;
  if ( this->m_lstDropEventItem._Mysize )
  {
    Myhead = this->m_lstDropEventItem._Myhead;
    Myval = Myhead->_Next->_Myval;
    Next = Myhead->_Next;
    if ( Next != this->m_lstDropEventItem._Myhead )
    {
      Next->_Prev->_Next = Next->_Next;
      Next->_Next->_Prev = Next->_Prev;
      operator delete(Next);
      --this->m_lstDropEventItem._Mysize;
    }
    return Myval;
  }
  return result;
}

//----- (00476AB0) --------------------------------------------------------
unsigned int __thiscall AwardTable::CAward::GetAward(
        AwardTable::CAward *this,
        int nItemKind,
        CMonster *pDeadMonster,
        CCharacter *pAttackCharacter)
{
  CCharacter *v6; // esi
  unsigned int result; // eax
  int m_nLevel; // edi
  bool v9; // al
  unsigned __int8 cAttackCharacterClass; // [esp+Ch] [ebp-4h]
  char pDeadMonstera; // [esp+18h] [ebp+8h]

  if ( pDeadMonster && (v6 = pAttackCharacter) != 0 )
  {
    LOWORD(result) = CGameEventMgr::PopDropEventItem(CSingleton<CGameEventMgr>::ms_pSingleton);
    if ( (_WORD)result )
    {
      return (unsigned __int16)result;
    }
    else
    {
      m_nLevel = pDeadMonster->m_CreatureStatus.m_nLevel;
      v9 = pAttackCharacter->IsPeaceMode(pAttackCharacter);
      pDeadMonstera = pAttackCharacter->m_DBData.m_Info.Nationality;
      LOBYTE(pAttackCharacter) = v9;
      cAttackCharacterClass = v6->GetClass(v6);
      switch ( nItemKind )
      {
        case 0:
        case 1:
        case 2:
        case 3:
        case 4:
          result = pDeadMonster->m_MonsterInfo.m_aryAwardItem[nItemKind];
          break;
        case 5:
          result = AwardTable::CAward::GetAwardCoin(this, m_nLevel, (unsigned int)pAttackCharacter);
          break;
        case 6:
          result = AwardTable::CAward::GetAwardGem(this, m_nLevel);
          break;
        case 7:
          result = AwardTable::CAward::GetAwardMetal(this, m_nLevel);
          break;
        case 8:
          result = AwardTable::CAward::GetAwardPotion(this, m_nLevel);
          break;
        case 9:
          result = AwardTable::CAward::GetAwardSkill(this, m_nLevel, pDeadMonstera);
          break;
        case 10:
          result = AwardTable::CAward::GetAwardEquipment(this, m_nLevel, pDeadMonstera, cAttackCharacterClass);
          break;
        case 11:
          result = 9914;
          break;
        case 12:
          result = AwardTable::CAward::GetAwardLottery(this);
          break;
        default:
          result = 0;
          break;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "AwardTable::CAward::GetAward",
      aDWorkRylSource_72,
      27,
      (char *)&byte_4EDB04);
    return 0;
  }
  return result;
}

//----- (00476C30) --------------------------------------------------------
void __thiscall GAMELOG::sMinItemInfo::InitMinItemInfo(GAMELOG::sMinItemInfo *this, const Item::CItem *lpItem_In)
{
  if ( lpItem_In )
  {
    this->m_dwItemUID = lpItem_In->m_ItemData.m_dwUID;
    this->m_usProtoTypeID = lpItem_In->m_ItemData.m_usProtoTypeID;
    this->m_cNowDurability = lpItem_In->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_dwItemUID = 0LL;
    this->m_usProtoTypeID = 0;
    this->m_cNowDurability = 0;
  }
}

//----- (00476C70) --------------------------------------------------------
void __thiscall GAMELOG::sSwapItemLog::InitSwapItemLog(
        GAMELOG::sSwapItemLog *this,
        TakeType srcTake,
        TakeType dstTake,
        const Item::CItem *lpSrcItem,
        const Item::CItem *lpDstItem)
{
  this->m_srcTake = srcTake;
  this->m_dstTake = dstTake;
  if ( lpSrcItem )
  {
    this->m_srcItemInfo.m_dwItemUID = lpSrcItem->m_ItemData.m_dwUID;
    this->m_srcItemInfo.m_usProtoTypeID = lpSrcItem->m_ItemData.m_usProtoTypeID;
    this->m_srcItemInfo.m_cNowDurability = lpSrcItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_srcItemInfo.m_dwItemUID = 0LL;
    this->m_srcItemInfo.m_usProtoTypeID = 0;
    this->m_srcItemInfo.m_cNowDurability = 0;
  }
  if ( lpDstItem )
  {
    this->m_dstItemInfo.m_dwItemUID = lpDstItem->m_ItemData.m_dwUID;
    this->m_dstItemInfo.m_usProtoTypeID = lpDstItem->m_ItemData.m_usProtoTypeID;
    this->m_dstItemInfo.m_cNowDurability = lpDstItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_dstItemInfo.m_dwItemUID = 0LL;
    this->m_dstItemInfo.m_usProtoTypeID = 0;
    this->m_dstItemInfo.m_cNowDurability = 0;
  }
}

//----- (00476D00) --------------------------------------------------------
void __thiscall GAMELOG::sSplitItemLog::InitSplitItemLog(
        GAMELOG::sSplitItemLog *this,
        TakeType splitTake,
        const Item::CItem *lpPrevItem,
        const Item::CItem *lpSplitItem)
{
  this->m_splitTake = splitTake;
  if ( lpPrevItem )
  {
    this->m_prevItem.m_dwItemUID = lpPrevItem->m_ItemData.m_dwUID;
    this->m_prevItem.m_usProtoTypeID = lpPrevItem->m_ItemData.m_usProtoTypeID;
    this->m_prevItem.m_cNowDurability = lpPrevItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_prevItem.m_dwItemUID = 0LL;
    this->m_prevItem.m_usProtoTypeID = 0;
    this->m_prevItem.m_cNowDurability = 0;
  }
  if ( lpSplitItem )
  {
    this->m_splitItem.m_dwItemUID = lpSplitItem->m_ItemData.m_dwUID;
    this->m_splitItem.m_usProtoTypeID = lpSplitItem->m_ItemData.m_usProtoTypeID;
    this->m_splitItem.m_cNowDurability = lpSplitItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_splitItem.m_dwItemUID = 0LL;
    this->m_splitItem.m_usProtoTypeID = 0;
    this->m_splitItem.m_cNowDurability = 0;
  }
}

//----- (00476D80) --------------------------------------------------------
void __thiscall GAMELOG::sRepairItemLog::InitRepairItemLog(
        GAMELOG::sRepairItemLog *this,
        unsigned int dwUsed,
        const Item::CItem *lpItem,
        unsigned __int8 cPreRepairDurability)
{
  this->m_dwUsed = dwUsed;
  if ( lpItem )
  {
    this->m_RepairedItem.m_dwItemUID = lpItem->m_ItemData.m_dwUID;
    this->m_RepairedItem.m_usProtoTypeID = lpItem->m_ItemData.m_usProtoTypeID;
    this->m_RepairedItem.m_cNowDurability = lpItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_RepairedItem.m_dwItemUID = 0LL;
    this->m_RepairedItem.m_usProtoTypeID = 0;
    this->m_RepairedItem.m_cNowDurability = 0;
  }
  this->m_cPreRepairDurability = cPreRepairDurability;
}

//----- (00476DD0) --------------------------------------------------------
void __thiscall GAMELOG::sStallRegisterRemoveItemLog::InitStallRegisterRemoveItemLog(
        GAMELOG::sStallRegisterRemoveItemLog *this,
        GAMELOG::sStallRegisterRemoveItemLog::StallMode eStallMode,
        const Item::CItem *lpItem,
        TakeType takeType)
{
  if ( lpItem )
  {
    this->m_itemInfo.m_dwItemUID = lpItem->m_ItemData.m_dwUID;
    this->m_itemInfo.m_usProtoTypeID = lpItem->m_ItemData.m_usProtoTypeID;
    this->m_itemInfo.m_cNowDurability = lpItem->m_ItemData.m_cNumOrDurability;
  }
  else
  {
    this->m_itemInfo.m_dwItemUID = 0LL;
    this->m_itemInfo.m_usProtoTypeID = 0;
    this->m_itemInfo.m_cNowDurability = 0;
  }
  this->m_takeType = takeType;
  if ( lpItem )
    this->m_dwStallPrice = lpItem->m_dwStallPrice;
  else
    this->m_dwStallPrice = 0;
  this->m_cMode = eStallMode;
}

//----- (00476E40) --------------------------------------------------------
void __cdecl GAMELOG::LogMoveItem(
        CCharacter *character,
        TakeType takeType,
        const GAMELOG::sMinItemInfo *minItemInfo,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v5; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  double m_fPointX; // st7
  int m_dwItemUID; // ecx
  int m_dwItemUID_high; // ebp
  unsigned __int16 v11; // cx
  int nBufferSize; // [esp+8h] [ebp-14h] BYREF
  CGameLog *gameLog; // [esp+Ch] [ebp-10h]
  __int16 v14; // [esp+18h] [ebp-4h]
  unsigned __int8 m_cNowDurability; // [esp+1Ah] [ebp-2h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v5 = CGameLog::ReserveBuffer(Instance, 0x20D0u);
  if ( v5 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = character->m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    m_fPointX = character->m_CurrentPos.m_fPointX;
    nBufferSize = 36;
    *((_WORD *)v5 + 6) = (unsigned __int64)m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v5 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v5 + 2) = m_time;
    v5[18] = 16;
    v5[19] = eError;
    m_dwItemUID = minItemInfo->m_dwItemUID;
    m_dwItemUID_high = HIDWORD(minItemInfo->m_dwItemUID);
    LOWORD(m_time) = minItemInfo->m_usProtoTypeID;
    m_cNowDurability = minItemInfo->m_cNowDurability;
    v14 = m_time;
    *((TakeType *)v5 + 4) = takeType;
    *(_DWORD *)(v5 + 25) = m_dwItemUID;
    LOWORD(m_dwItemUID) = v14;
    *(_DWORD *)(v5 + 29) = m_dwItemUID_high;
    *(_WORD *)(v5 + 33) = m_dwItemUID;
    v5[35] = m_cNowDurability;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v5 + 36, (char **)&nBufferSize);
      v11 = nBufferSize + 36;
    }
    else
    {
      v11 = nBufferSize;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v11;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogMoveItem",
      aDWorkRylSource_33,
      43,
      aCid0x08x_0,
      character->m_dwCID);
  }
}

//----- (00476F70) --------------------------------------------------------
void __cdecl GAMELOG::LogSwapItem(
        CCharacter *character,
        TakeType srcTake,
        TakeType dstTake,
        Item::CItem *lpSrcItem,
        Item::CItem *lpDstItem,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v7; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v10; // bp
  int nBufferSize; // [esp+10h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+14h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v7 = CGameLog::ReserveBuffer(Instance, 0x20E0u);
  if ( v7 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v7 = character->m_dwUID;
    *((_DWORD *)v7 + 1) = m_dwCID;
    v10 = 52;
    *((_WORD *)v7 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v7 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v7 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v7 + 2) = m_time;
    v7[18] = 17;
    v7[19] = eError;
    GAMELOG::sSwapItemLog::InitSwapItemLog((GAMELOG::sSwapItemLog *)v7, srcTake, dstTake, lpSrcItem, lpDstItem);
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v7 + 52, (char **)&nBufferSize);
      v10 = nBufferSize + 52;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v10;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogSwapItem",
      aDWorkRylSource_33,
      76,
      aCid0x08x_162,
      character->m_dwCID);
  }
}

//----- (00477090) --------------------------------------------------------
void __cdecl GAMELOG::LogUseItem(
        CCharacter *character,
        Item::ItemPos usePos,
        Item::CItem *lpUseItem,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v5; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  unsigned __int16 v8; // bx
  int nBufferSize; // [esp+8h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+Ch] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v5 = CGameLog::ReserveBuffer(Instance, 0x20CDu);
  if ( v5 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = character->m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    v8 = 33;
    *((_WORD *)v5 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v5 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v5 + 2) = m_time;
    v5[18] = 18;
    v5[19] = eError;
    *((Item::ItemPos *)v5 + 10) = usePos;
    if ( lpUseItem )
    {
      *(_QWORD *)(v5 + 22) = lpUseItem->m_ItemData.m_dwUID;
      *((_WORD *)v5 + 15) = lpUseItem->m_ItemData.m_usProtoTypeID;
      v5[32] = lpUseItem->m_ItemData.m_cNumOrDurability;
    }
    else
    {
      *(_DWORD *)(v5 + 22) = 0;
      *(_DWORD *)(v5 + 26) = 0;
      *((_WORD *)v5 + 15) = 0;
      v5[32] = 0;
    }
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v5 + 33, (char **)&nBufferSize);
      v8 = nBufferSize + 33;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v8;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogUseItem",
      aDWorkRylSource_33,
      110,
      aCid0x08x_180,
      character->m_dwCID);
  }
}

//----- (004771C0) --------------------------------------------------------
void __cdecl GAMELOG::LogSplitItem(
        CCharacter *character,
        TakeType splitTake,
        Item::CItem *lpPrevItem,
        Item::CItem *lpSplitItem,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v6; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v9; // bp
  int nBufferSize; // [esp+10h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+14h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v6 = CGameLog::ReserveBuffer(Instance, 0x20DBu);
  if ( v6 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v6 = character->m_dwUID;
    *((_DWORD *)v6 + 1) = m_dwCID;
    v9 = 47;
    *((_WORD *)v6 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v6 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v6 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v6 + 2) = m_time;
    v6[18] = 19;
    v6[19] = eError;
    GAMELOG::sSplitItemLog::InitSplitItemLog((GAMELOG::sSplitItemLog *)v6, splitTake, lpPrevItem, lpSplitItem);
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v6 + 47, (char **)&nBufferSize);
      v9 = nBufferSize + 47;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v9;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogSplitItem",
      aDWorkRylSource_33,
      144,
      aCid0x08x_68,
      character->m_dwCID);
  }
}

//----- (004772D0) --------------------------------------------------------
void __cdecl GAMELOG::LogPickupItem(
        CCharacter *character,
        Item::ItemPos pickupPos,
        Item::CItem *lpItem,
        unsigned int dwGold,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v6; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v9; // bp
  unsigned __int64 m_fPointZ; // rax
  unsigned int v11; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-Ch] BYREF
  int nBufferSize; // [esp+Ch] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+10h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v6 = CGameLog::ReserveBuffer(Instance, 0x20FAu);
  if ( v6 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v6 = character->m_dwUID;
    *((_DWORD *)v6 + 1) = m_dwCID;
    v9 = 26;
    *((_WORD *)v6 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v6 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v6 + 2) = m_time;
    *((_WORD *)v6 + 8) = m_fPointZ;
    v6[18] = 20;
    v6[19] = eError;
    nItemSize = 52;
    if ( eError || dwGold || !lpItem )
    {
      v11 = 0;
      nItemSize = 0;
    }
    else
    {
      if ( !lpItem->SerializeOut(lpItem, v6 + 26, &nItemSize) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GAMELOG::LogPickupItem",
          aDWorkRylSource_33,
          169,
          aCid0x08x_196,
          character->m_dwCID);
      v11 = nItemSize;
      v9 = nItemSize + 26;
    }
    *((_DWORD *)v6 + 5) = dwGold;
    *((Item::ItemPos *)v6 + 12) = pickupPos;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, &v6[v11 + 26], (char **)&nBufferSize);
      v9 += nBufferSize;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v9;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogPickupItem",
      aDWorkRylSource_33,
      193,
      aCid0x08x_315,
      character->m_dwCID);
  }
}

//----- (00477430) --------------------------------------------------------
void __cdecl GAMELOG::LogDropItem(
        CCharacter *character,
        Item::ItemPos dropFrom,
        Item::CItem *lpItem,
        unsigned int dwGold,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v6; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v9; // bp
  unsigned __int64 m_fPointZ; // rax
  unsigned int v11; // eax
  unsigned int nItemSize; // [esp+8h] [ebp-Ch] BYREF
  int nBufferSize; // [esp+Ch] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+10h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v6 = CGameLog::ReserveBuffer(Instance, 0x20FAu);
  if ( v6 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v6 = character->m_dwUID;
    *((_DWORD *)v6 + 1) = m_dwCID;
    v9 = 26;
    *((_WORD *)v6 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v6 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v6 + 2) = m_time;
    *((_WORD *)v6 + 8) = m_fPointZ;
    v6[18] = 21;
    v6[19] = eError;
    nItemSize = 52;
    if ( eError || dwGold || !lpItem )
    {
      v11 = 0;
      nItemSize = 0;
    }
    else
    {
      if ( !lpItem->SerializeOut(lpItem, v6 + 26, &nItemSize) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GAMELOG::LogDropItem",
          aDWorkRylSource_33,
          219,
          aCid0x08x_196,
          character->m_dwCID);
      v11 = nItemSize;
      v9 = nItemSize + 26;
    }
    *((_DWORD *)v6 + 5) = dwGold;
    *((Item::ItemPos *)v6 + 12) = dropFrom;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, &v6[v11 + 26], (char **)&nBufferSize);
      v9 += nBufferSize;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v9;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogDropItem",
      aDWorkRylSource_33,
      244,
      aCid0x08x_20,
      character->m_dwCID);
  }
}

//----- (00477590) --------------------------------------------------------
void __cdecl GAMELOG::LogTradeItem(
        CCharacter *character,
        unsigned int dwTraderCID,
        unsigned int dwGold,
        Item::CItem *lpItem,
        Item::ItemPos itemPos,
        unsigned __int8 cCmd,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v8; // esi
  char v9; // bl
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  unsigned __int64 m_fPointZ; // rax
  unsigned int v13; // eax
  unsigned __int16 v14; // bp
  unsigned __int8 LogCmd; // [esp+Bh] [ebp-Dh]
  unsigned int nItemSize; // [esp+Ch] [ebp-Ch] BYREF
  int nBufferSize; // [esp+10h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+14h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v8 = CGameLog::ReserveBuffer(Instance, 0x20FEu);
  if ( v8 )
  {
    nItemSize = 52;
    nBufferSize = 30;
    if ( cCmd )
    {
      if ( cCmd == 1 )
      {
        v9 = 40;
      }
      else if ( cCmd == 2 )
      {
        v9 = 23;
      }
      else
      {
        v9 = LogCmd;
      }
    }
    else
    {
      v9 = 22;
    }
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v8 = character->m_dwUID;
    *((_DWORD *)v8 + 1) = m_dwCID;
    *((_WORD *)v8 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v8 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v8[18] = v9;
    *((_WORD *)v8 + 8) = m_fPointZ;
    *((_DWORD *)v8 + 2) = m_time;
    v8[19] = eError;
    if ( eError || !lpItem )
    {
      v14 = nBufferSize;
      v13 = 0;
      nItemSize = 0;
    }
    else
    {
      if ( !lpItem->SerializeOut(lpItem, v8 + 30, &nItemSize) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GAMELOG::LogTradeItem",
          aDWorkRylSource_33,
          278,
          aCid0x08x_196,
          character->m_dwCID);
      v13 = nItemSize;
      v14 = nItemSize + 30;
    }
    *((_DWORD *)v8 + 5) = dwTraderCID;
    *((_DWORD *)v8 + 6) = dwGold;
    *((Item::ItemPos *)v8 + 14) = itemPos;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, &v8[v13 + 30], (char **)&nBufferSize);
      v14 += nBufferSize;
    }
    gameLog->m_lpLogBuffer->m_nUsage += v14;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogTradeItem",
      aDWorkRylSource_33,
      302,
      aCid0x08x_317,
      character->m_dwCID);
  }
}
// 4775E4: variable 'LogCmd' is possibly undefined

//----- (00477710) --------------------------------------------------------
void __cdecl GAMELOG::LogExchangeItem(
        const CCharacter *character,
        unsigned int dwDstCID,
        Item::CExchangeContainer *Exchange,
        unsigned __int8 cLogCMD)
{
  CGameLog *Instance; // ebx
  char *v5; // esi
  Item::CExchangeContainer_vtbl *v6; // eax
  __int16 v7; // bp
  unsigned int m_dwUID; // ecx
  unsigned int m_dwCID; // eax
  unsigned __int64 m_fPointZ; // rax
  unsigned __int8 cErr; // [esp+Bh] [ebp-9h]
  unsigned int dwItemSize; // [esp+Ch] [ebp-8h] BYREF
  int m_time; // [esp+10h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  v5 = CGameLog::ReserveBuffer(Instance, 0x51Eu);
  if ( v5 )
  {
    v6 = Exchange->__vftable;
    cErr = 0;
    dwItemSize = 1280;
    if ( v6->SerializeOut(Exchange, v5 + 30, &dwItemSize) )
    {
      v7 = dwItemSize;
    }
    else
    {
      v7 = 0;
      cErr = 1;
      dwItemSize = 0;
    }
    m_dwUID = character->m_dwUID;
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v5 = m_dwUID;
    *((_DWORD *)v5 + 1) = m_dwCID;
    *((_WORD *)v5 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v5 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    HIDWORD(m_fPointZ) = m_time;
    *((_WORD *)v5 + 8) = m_fPointZ;
    *((_DWORD *)v5 + 2) = HIDWORD(m_fPointZ);
    v5[19] = cErr;
    v5[18] = cLogCMD;
    LODWORD(m_fPointZ) = Exchange->m_dwGold;
    *((_WORD *)v5 + 14) = v7;
    *((_DWORD *)v5 + 6) = dwDstCID;
    *((_DWORD *)v5 + 5) = m_fPointZ;
    Instance->m_lpLogBuffer->m_nUsage += (unsigned __int16)(v7 + 30);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogExchangeItem",
      aDWorkRylSource_33,
      338,
      aCid0x08x_177,
      character->m_dwCID);
  }
}

//----- (00477820) --------------------------------------------------------
void __cdecl GAMELOG::LogInstallSocket(
        CCharacter *character,
        TakeType GemAndEquip,
        Item::CItem *lpGemItem,
        Item::CItem *lpEquipItem,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v6; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v9; // bp
  char *v10; // ebx
  Item::CItem_vtbl *v11; // edx
  Item::CItem_vtbl *v12; // edx
  char v13; // cl
  char v14; // al
  CGameLog *v15; // edx
  unsigned int nGemItemSize; // [esp+Ch] [ebp-10h] BYREF
  unsigned int nEquipItemSize; // [esp+10h] [ebp-Ch] BYREF
  int nBufferSize; // [esp+14h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+18h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v6 = CGameLog::ReserveBuffer(Instance, 0x212Fu);
  if ( v6 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v6 = character->m_dwUID;
    *((_DWORD *)v6 + 1) = m_dwCID;
    v9 = 27;
    *((_WORD *)v6 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v6 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v6 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v6 + 2) = m_time;
    v6[18] = 32;
    v6[19] = eError;
    nGemItemSize = 0;
    nEquipItemSize = 0;
    v10 = v6 + 27;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v6 + 27, (char **)&nBufferSize);
      v9 = nBufferSize + 27;
    }
    else
    {
      if ( lpGemItem )
      {
        v11 = lpGemItem->__vftable;
        nGemItemSize = 52;
        if ( !v11->SerializeOut(lpGemItem, v6 + 27, &nGemItemSize) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GAMELOG::LogInstallSocket",
            aDWorkRylSource_33,
            371,
            aCid0x08x_196,
            character->m_dwCID);
        v9 = nGemItemSize + 27;
        v10 += nGemItemSize;
      }
      if ( lpEquipItem )
      {
        v12 = lpEquipItem->__vftable;
        nEquipItemSize = 52;
        if ( !v12->SerializeOut(lpEquipItem, v10, &nEquipItemSize) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GAMELOG::LogInstallSocket",
            aDWorkRylSource_33,
            383,
            aCid0x08x_196,
            character->m_dwCID);
        v9 += nEquipItemSize;
      }
    }
    *((_DWORD *)v6 + 5) = *(_DWORD *)&GemAndEquip.m_srcPos;
    v13 = nEquipItemSize;
    v6[24] = GemAndEquip.m_cNum;
    v14 = nGemItemSize;
    v15 = gameLog;
    v6[26] = v13;
    v6[25] = v14;
    v15->m_lpLogBuffer->m_nUsage += v9;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogInstallSocket",
      aDWorkRylSource_33,
      405,
      aCid0x08x_265,
      character->m_dwCID);
  }
}

//----- (004779D0) --------------------------------------------------------
void __cdecl GAMELOG::LogUpgradeItem(
        CCharacter *character,
        unsigned int dwCurrentGold,
        unsigned int dwUsedGold,
        Item::CItem *lpResult,
        Item::CItem *lpMineral,
        unsigned __int16 eError)
{
  CGameLog *Instance; // edi
  char *v7; // esi
  int m_time; // ebx
  unsigned int m_dwCID; // eax
  unsigned __int16 v10; // bp
  char *v11; // ebx
  Item::CItem_vtbl *v12; // edx
  Item::CItem_vtbl *v13; // edx
  char v14; // dl
  char v15; // al
  CGameLog *v16; // ecx
  unsigned int nMineralSize; // [esp+Ch] [ebp-10h] BYREF
  unsigned int nResultSize; // [esp+10h] [ebp-Ch] BYREF
  int nBufferSize; // [esp+14h] [ebp-8h] BYREF
  CGameLog *gameLog; // [esp+18h] [ebp-4h]

  Instance = CGameLog::GetInstance();
  gameLog = Instance;
  v7 = CGameLog::ReserveBuffer(Instance, 0x86u);
  if ( v7 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v7 = character->m_dwUID;
    *((_DWORD *)v7 + 1) = m_dwCID;
    v10 = 30;
    *((_WORD *)v7 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v7 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v7 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v7 + 2) = m_time;
    v7[18] = 36;
    v7[19] = eError;
    nResultSize = 0;
    nMineralSize = 0;
    v11 = v7 + 30;
    if ( eError )
    {
      nBufferSize = 8364;
      CCharacter::ItemDump(character, v7 + 30, (char **)&nBufferSize);
      v10 = nBufferSize + 30;
    }
    else
    {
      if ( lpMineral )
      {
        v12 = lpMineral->__vftable;
        nMineralSize = 52;
        if ( !v12->SerializeOut(lpMineral, v7 + 30, &nMineralSize) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GAMELOG::LogUpgradeItem",
            aDWorkRylSource_33,
            438,
            aCid0x08x_196,
            character->m_dwCID);
        v10 = nMineralSize + 30;
        v11 += nMineralSize;
      }
      if ( lpResult )
      {
        v13 = lpResult->__vftable;
        nResultSize = 52;
        if ( !v13->SerializeOut(lpResult, v11, &nResultSize) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GAMELOG::LogUpgradeItem",
            aDWorkRylSource_33,
            450,
            aCid0x08x_196,
            character->m_dwCID);
        v10 += nResultSize;
      }
    }
    v14 = nMineralSize;
    *((_DWORD *)v7 + 5) = dwCurrentGold;
    v15 = nResultSize;
    *((_DWORD *)v7 + 6) = dwUsedGold;
    v16 = gameLog;
    v7[28] = v14;
    v7[29] = v15;
    v16->m_lpLogBuffer->m_nUsage += v10;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogUpgradeItem",
      aDWorkRylSource_33,
      473,
      aCid0x08x_154,
      character->m_dwCID);
  }
}

//----- (00477B80) --------------------------------------------------------
void __cdecl GAMELOG::LogRepairItem(
        const CCharacter *character,
        Item::CItem *lpRepairedItem,
        unsigned int dwRepairPrice,
        unsigned __int8 cPreRepairDurability,
        unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  GAMELOG::sRepairItemLog *v6; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v6 = (GAMELOG::sRepairItemLog *)CGameLog::ReserveBuffer(Instance, 0x24u);
  if ( v6 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    v6->m_dwUID = character->m_dwUID;
    v6->m_dwCID = m_dwCID;
    v6->m_usXPos = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    v6->m_usYPos = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    v6->m_usZPos = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v6->m_cErr = eError;
    v6->m_time = m_time;
    v6->m_cCmd = 33;
    GAMELOG::sRepairItemLog::InitRepairItemLog(v6, dwRepairPrice, lpRepairedItem, cPreRepairDurability);
    Instance->m_lpLogBuffer->m_nUsage += 36;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogRepairItem",
      aDWorkRylSource_33,
      499,
      aCid0x08x_101,
      character->m_dwCID);
  }
}

//----- (00477C40) --------------------------------------------------------
void __cdecl GAMELOG::LogChangeWeapon(const CCharacter *character, unsigned __int8 cCurrentHand)
{
  CGameLog *Instance; // ebx
  char *v3; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  unsigned __int64 m_fPointZ; // rax

  Instance = CGameLog::GetInstance();
  v3 = CGameLog::ReserveBuffer(Instance, 0x15u);
  if ( v3 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v3 = character->m_dwUID;
    *((_DWORD *)v3 + 1) = m_dwCID;
    *((_WORD *)v3 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v3 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v3 + 2) = m_time;
    *((_WORD *)v3 + 8) = m_fPointZ;
    v3[18] = 34;
    v3[19] = 0;
    v3[20] = cCurrentHand;
    Instance->m_lpLogBuffer->m_nUsage += 21;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogChangeWeapon",
      aDWorkRylSource_33,
      523,
      aCid0x08x_206,
      character->m_dwCID);
  }
}

//----- (00477CF0) --------------------------------------------------------
void __cdecl GAMELOG::LogTakeGold(
        const CCharacter *character,
        unsigned int dwSrcGold,
        unsigned int dwDstGold,
        unsigned int dwMoveGold,
        unsigned __int8 cSrcPos,
        unsigned __int8 cDstPos,
        unsigned __int8 cPurpose,
        unsigned __int16 eError)
{
  CGameLog *Instance; // ebx
  char *v9; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax

  Instance = CGameLog::GetInstance();
  v9 = CGameLog::ReserveBuffer(Instance, 0x23u);
  if ( v9 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v9 = character->m_dwUID;
    *((_DWORD *)v9 + 1) = m_dwCID;
    *((_WORD *)v9 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v9 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v9 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v9[19] = eError;
    *((_DWORD *)v9 + 6) = dwDstGold;
    *((_DWORD *)v9 + 5) = dwSrcGold;
    *((_DWORD *)v9 + 7) = dwMoveGold;
    *((_DWORD *)v9 + 2) = m_time;
    v9[33] = cDstPos;
    v9[18] = 41;
    v9[32] = cSrcPos;
    v9[34] = cPurpose;
    Instance->m_lpLogBuffer->m_nUsage += 35;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogTakeGold",
      aDWorkRylSource_33,
      554,
      aCid0x08x_215,
      character->m_dwCID);
  }
}

//----- (00477DD0) --------------------------------------------------------
void __cdecl GAMELOG::LogStallOpenClose(const CCharacter *character, const char *szStallName, bool bOpen)
{
  CGameLog *Instance; // ebx
  char *v4; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  _DWORD *v7; // esi

  Instance = CGameLog::GetInstance();
  v4 = CGameLog::ReserveBuffer(Instance, 0x35u);
  if ( v4 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v4 = character->m_dwUID;
    *((_DWORD *)v4 + 1) = m_dwCID;
    *((_WORD *)v4 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v4 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    *((_WORD *)v4 + 8) = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v4 + 2) = m_time;
    v4[18] = 37;
    v4[19] = 0;
    v4[20] = !bOpen;
    if ( szStallName )
    {
      strncpy(v4 + 21, szStallName, 0x20u);
    }
    else
    {
      v7 = v4 + 21;
      *v7 = 0;
      v7[1] = 0;
      v7[2] = 0;
      v7[3] = 0;
      v7[4] = 0;
      v7[5] = 0;
      v7[6] = 0;
      v7[7] = 0;
    }
    Instance->m_lpLogBuffer->m_nUsage += 53;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogStallOpenClose",
      aDWorkRylSource_33,
      580,
      aCid0x08x_181,
      character->m_dwCID);
  }
}

//----- (00477ED0) --------------------------------------------------------
void __cdecl GAMELOG::LogStallEnterLeave(const CCharacter *character, unsigned int dwCustomerCID, bool bEnter)
{
  CGameLog *Instance; // ebx
  char *v4; // esi
  int m_time; // ebp
  unsigned int m_dwCID; // eax
  unsigned __int64 m_fPointZ; // rax

  Instance = CGameLog::GetInstance();
  v4 = CGameLog::ReserveBuffer(Instance, 0x19u);
  if ( v4 )
  {
    m_time = Instance->m_time;
    m_dwCID = character->m_dwCID;
    *(_DWORD *)v4 = character->m_dwUID;
    *((_DWORD *)v4 + 1) = m_dwCID;
    *((_WORD *)v4 + 6) = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    *((_WORD *)v4 + 7) = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    m_fPointZ = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    *((_DWORD *)v4 + 5) = dwCustomerCID;
    *((_WORD *)v4 + 8) = m_fPointZ;
    *((_DWORD *)v4 + 2) = m_time;
    v4[18] = 38;
    v4[19] = 0;
    v4[24] = !bEnter;
    Instance->m_lpLogBuffer->m_nUsage += 25;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogStallEnterLeave",
      aDWorkRylSource_33,
      605,
      aCid0x08x_227,
      character->m_dwCID);
  }
}

//----- (00477F90) --------------------------------------------------------
void __cdecl GAMELOG::LogStallRegisterRemoveItem(
        const CCharacter *character,
        const Item::CItem *lpItem,
        TakeType takeType,
        unsigned __int8 cPktStRICMD,
        unsigned __int16 usError)
{
  CGameLog *Instance; // ebx
  GAMELOG::sStallRegisterRemoveItemLog *v6; // esi
  unsigned int m_dwCID; // eax
  int m_time; // ebp
  GAMELOG::sStallRegisterRemoveItemLog::StallMode v9; // ecx

  Instance = CGameLog::GetInstance();
  v6 = (GAMELOG::sStallRegisterRemoveItemLog *)CGameLog::ReserveBuffer(Instance, 0x29u);
  if ( v6 )
  {
    m_dwCID = character->m_dwCID;
    m_time = Instance->m_time;
    v6->m_dwUID = character->m_dwUID;
    v6->m_dwCID = m_dwCID;
    v6->m_usXPos = (unsigned __int64)character->m_CurrentPos.m_fPointX;
    v6->m_usYPos = (unsigned __int64)character->m_CurrentPos.m_fPointY;
    v6->m_usZPos = (unsigned __int64)character->m_CurrentPos.m_fPointZ;
    v9 = STALL_UNKNOWN;
    v6->m_time = m_time;
    v6->m_cCmd = 39;
    v6->m_cErr = usError;
    if ( cPktStRICMD )
    {
      if ( cPktStRICMD == 1 )
      {
        v9 = STALL_CHANGEPRICE;
      }
      else if ( cPktStRICMD == 2 )
      {
        v9 = STALL_REMOVEITEM;
      }
    }
    else
    {
      v9 = STALL_REGISTERITEM;
    }
    GAMELOG::sStallRegisterRemoveItemLog::InitStallRegisterRemoveItemLog(v6, v9, lpItem, takeType);
    Instance->m_lpLogBuffer->m_nUsage += 41;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GAMELOG::LogStallRegisterRemoveItem",
      aDWorkRylSource_33,
      640,
      aCid0x08x_147,
      character->m_dwCID);
  }
}

//----- (00478080) --------------------------------------------------------
char __thiscall CCharacter::AddGold(CCharacter *this, unsigned int dwGold, bool bNotice)
{
  unsigned int Gold; // eax
  CCharacter_vtbl *v5; // edx
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int v10; // [esp-4h] [ebp-Ch]

  Gold = this->m_DBData.m_Info.Gold;
  if ( Gold >= -1 - dwGold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::AddGold",
      aDWorkRylSource_101,
      54,
      aCid0x08x_134,
      this->m_dwCID,
      dwGold);
    return 0;
  }
  else
  {
    v5 = this->__vftable;
    this->m_DBData.m_Info.Gold = dwGold + Gold;
    v10 = v5->GetGID(this);
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v10);
    if ( Guild )
      Guild::CGuild::UpdateMemberInfo(Guild, this->m_dwCID, this->m_DBData.m_Info.Gold, 3u);
    if ( bNotice )
    {
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharTakeGold(&m_lpGameClientDispatch->m_SendStream, this->m_dwCID, dwGold, 0, 2u, 0);
    }
    return 1;
  }
}

//----- (00478130) --------------------------------------------------------
char __thiscall CCharacter::DeductGold(CCharacter *this, unsigned int dwGold, bool bNotice)
{
  unsigned int Gold; // eax
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  unsigned int v9; // [esp-4h] [ebp-Ch]

  Gold = this->m_DBData.m_Info.Gold;
  if ( dwGold > Gold )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::DeductGold",
      aDWorkRylSource_101,
      83,
      aCid0x08x_258,
      this->m_dwCID,
      dwGold);
    return 0;
  }
  else
  {
    this->m_DBData.m_Info.Gold = Gold - dwGold;
    v9 = this->GetGID(this);
    Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v9);
    if ( Guild )
      Guild::CGuild::UpdateMemberInfo(Guild, this->m_dwCID, this->m_DBData.m_Info.Gold, 3u);
    if ( bNotice )
    {
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharTakeGold(&m_lpGameClientDispatch->m_SendStream, this->m_dwCID, dwGold, 2u, 0, 0);
    }
    return 1;
  }
}

//----- (004781D0) --------------------------------------------------------
unsigned int __thiscall CCharacter::GetGold(CCharacter *this, unsigned __int8 cPos)
{
  unsigned int result; // eax

  result = 0;
  switch ( cPos )
  {
    case 2u:
      return this->m_DBData.m_Info.Gold;
    case 8u:
      return this->m_Exchange.m_dwGold;
    case 9u:
      return this->m_Deposit.m_dwGold;
  }
  return result;
}

//----- (00478200) --------------------------------------------------------
char __thiscall CCharacter::MoveGold(
        CCharacter *this,
        unsigned int dwGold,
        unsigned __int8 cSrcPos,
        unsigned __int8 cDstPos,
        unsigned __int16 *usError)
{
  unsigned int v6; // edi
  char v7; // al
  char v9; // al

  *usError = 0;
  switch ( cSrcPos )
  {
    case 2u:
      v6 = dwGold;
      v7 = CCharacter::DeductGold(this, dwGold, 0);
      break;
    case 8u:
      v6 = dwGold;
      v7 = Item::CExchangeContainer::DeductGold(&this->m_Exchange, dwGold);
      break;
    case 9u:
      v6 = dwGold;
      v7 = Item::CDepositContainer::DeductGold(&this->m_Deposit, dwGold);
      break;
    default:
LABEL_8:
      *usError = 2;
      return 0;
  }
  if ( !v7 )
    goto LABEL_8;
  switch ( cDstPos )
  {
    case 2u:
      v9 = CCharacter::AddGold(this, v6, 0);
      break;
    case 8u:
      v9 = Item::CExchangeContainer::AddGold(&this->m_Exchange, v6);
      break;
    case 9u:
      v9 = Item::CDepositContainer::AddGold(&this->m_Deposit, v6);
      break;
    default:
      goto LABEL_18;
  }
  if ( v9 && !*usError )
    return 1;
LABEL_18:
  *usError = 3;
  switch ( cSrcPos )
  {
    case 2u:
      CCharacter::AddGold(this, v6, 0);
      break;
    case 8u:
      Item::CExchangeContainer::AddGold(&this->m_Exchange, v6);
      return 0;
    case 9u:
      Item::CDepositContainer::AddGold(&this->m_Deposit, v6);
      return 0;
  }
  return 0;
}

//----- (00478310) --------------------------------------------------------
char __thiscall CCharacter::Pickup(CCharacter *this, Item::CItem *lpItem, int dstPos)
{
  Item::CArrayContainer *p_m_Inventory; // esi
  int v6; // eax
  unsigned __int16 v7; // cx
  unsigned __int16 m_usProtoTypeID; // dx
  unsigned __int8 m_cNumOrDurability; // cl

  if ( !lpItem )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Pickup",
      aDWorkRylSource_101,
      468,
      aCid0x08x_96,
      this->m_dwCID);
    return 0;
  }
  p_m_Inventory = &this->m_Inventory;
  v6 = ((int (__thiscall *)(Item::CArrayContainer *, int))this->m_Inventory.GetItem)(&this->m_Inventory, dstPos);
  if ( v6 )
  {
    if ( (*(_BYTE *)(*(_DWORD *)(v6 + 4) + 32) & 8) != 8 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Pickup",
        aDWorkRylSource_101,
        491,
        aCid0x08x_103,
        this->m_dwCID,
        lpItem->m_ItemData.m_usProtoTypeID,
        *(unsigned __int16 *)(v6 + 16));
      goto LABEL_8;
    }
    v7 = *(_WORD *)(v6 + 16);
    m_usProtoTypeID = lpItem->m_ItemData.m_usProtoTypeID;
    if ( v7 != m_usProtoTypeID )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Pickup",
        aDWorkRylSource_101,
        499,
        aCid0x08x_35,
        this->m_dwCID,
        m_usProtoTypeID,
        v7);
      p_m_Inventory->DumpItemInfo(&this->m_Inventory);
      return 0;
    }
    m_cNumOrDurability = lpItem->m_ItemData.m_cNumOrDurability;
    if ( *(unsigned __int8 *)(v6 + 21) + m_cNumOrDurability > *(unsigned __int8 *)(v6 + 24) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Pickup",
        aDWorkRylSource_101,
        507,
        aCid0x08x_49,
        this->m_dwCID);
      p_m_Inventory->DumpItemInfo(&this->m_Inventory);
      return 0;
    }
    *(_BYTE *)(v6 + 21) += m_cNumOrDurability;
    lpItem->m_ItemData.m_cNumOrDurability = 0;
  }
  else if ( !CCharacter::SetItem(this, dstPos, lpItem) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Pickup",
      aDWorkRylSource_101,
      480,
      aCid0x08x_94,
      this->m_dwCID,
      dstPos & 0xF,
      (unsigned __int16)dstPos >> 4);
LABEL_8:
    p_m_Inventory->DumpItemInfo(&this->m_Inventory);
    return 0;
  }
  return 1;
}

//----- (00478490) --------------------------------------------------------
char __thiscall CCharacter::UseStartKit(CCharacter *this, unsigned __int16 wObjectType)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v7; // edi
  Guild::MemberInfo *Master; // eax
  unsigned int v9; // ecx
  CSiegeObjectMgr *v10; // eax
  Castle::CCastleMgr *v11; // eax
  CSiegeObjectMgr *v12; // eax
  CSingleDispatch *v13; // eax
  CPacketDispatch *v14; // ebx
  CSingleDispatch::Storage *p_StoragelpDBAgentDispatch; // ecx
  unsigned int v16; // eax
  char SiegeArms; // al
  CSingleDispatch::Storage *v18; // ecx
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebx
  unsigned int m_dwCID; // edi
  unsigned int v22; // eax
  char v23; // bl
  unsigned int v24; // [esp-14h] [ebp-68h]
  unsigned int v25; // [esp-14h] [ebp-68h]
  unsigned int v26; // [esp-14h] [ebp-68h]
  unsigned int m_dwGID; // [esp-14h] [ebp-68h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+0h] [ebp-54h] BYREF
  CSingleDispatch::Storage v29; // [esp+8h] [ebp-4Ch] BYREF
  Guild::MemberInfo result; // [esp+10h] [ebp-44h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v31; // [esp+48h] [ebp-Ch]
  void *v32; // [esp+4Ch] [ebp-8h]
  int v33; // [esp+50h] [ebp-4h]

  v33 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v32 = &_ehhandler__UseStartKit_CCharacter__QAE_NG_Z;
  v31 = ExceptionList;
  if ( wObjectType > 0x15CAu )
  {
    if ( wObjectType != 5626 )
      return 0;
    goto LABEL_25;
  }
  if ( wObjectType == 5578 )
  {
LABEL_25:
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&v29, DispatchTable);
    m_lpDispatch = v29.m_lpDispatch;
    v33 = 1;
    if ( !v29.m_lpDispatch )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::UseStartKit",
        aDWorkRylSource_101,
        669,
        (char *)&byte_4EE5AC);
      p_StoragelpDBAgentDispatch = &v29;
      goto LABEL_27;
    }
    m_dwCID = this->m_dwCID;
    v22 = this->GetGID(this);
    SiegeArms = GameClientSendPacket::SendCharCreateSiegeArms(
                  (CSendStream *)&m_lpDispatch[8],
                  m_dwCID,
                  v22,
                  wObjectType,
                  0,
                  &this->m_CurrentPos);
    v18 = &v29;
    goto LABEL_30;
  }
  if ( wObjectType != 5337 )
  {
    if ( wObjectType != 5530 )
      return 0;
    goto LABEL_25;
  }
  if ( !this->GetGID(this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      614,
      aCid0x08x_281,
      this->m_dwCID);
    return 0;
  }
  v24 = this->GetGID(this);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v24);
  v7 = Guild;
  if ( !Guild )
  {
    v25 = this->GetGID(this);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      621,
      aCid0x08xGid0x0_0,
      this->m_dwCID,
      v25);
    return 0;
  }
  Master = Guild::CGuild::GetMaster(Guild, &result);
  v9 = this->m_dwCID;
  if ( v9 != Master->m_dwCID )
  {
    v26 = this->GetGID(this);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      627,
      aCid0x08xGid0x0,
      this->m_dwCID,
      v26);
    return 0;
  }
  if ( v7->m_dwFame < 0xC350 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      633,
      aCid0x08xFameD,
      v9,
      v7->m_dwFame);
    return 0;
  }
  m_dwGID = v7->m_dwGID;
  v10 = CSiegeObjectMgr::GetInstance();
  if ( CSiegeObjectMgr::ExistBuildingCamp(v10, m_dwGID) == 1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      639,
      aCid0x08x_328,
      this->m_dwCID);
    return 0;
  }
  v11 = Castle::CCastleMgr::GetInstance();
  if ( Castle::CCastleMgr::ExistCastleInRadius(v11, &this->m_CurrentPos) == 1
    || (v12 = CSiegeObjectMgr::GetInstance(), CSiegeObjectMgr::ExistCampInRadius(v12, &this->m_CurrentPos) == 1) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::UseStartKit",
      aDWorkRylSource_101,
      646,
      aCid0x08x_130,
      this->m_dwCID);
    return 0;
  }
  v13 = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v13);
  v14 = StoragelpDBAgentDispatch.m_lpDispatch;
  v33 = 0;
  if ( !StoragelpDBAgentDispatch.m_lpDispatch )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacter::UseStartKit", aDWorkRylSource_101, 653, (char *)&byte_4EE5AC);
    p_StoragelpDBAgentDispatch = &StoragelpDBAgentDispatch;
LABEL_27:
    v33 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)p_StoragelpDBAgentDispatch);
    return 0;
  }
  v16 = this->GetGID(this);
  SiegeArms = GameClientSendPacket::SendCharCreateCamp((CSendStream *)&v14[8], v16, &this->m_CurrentPos);
  v18 = &StoragelpDBAgentDispatch;
LABEL_30:
  v33 = -1;
  v23 = SiegeArms;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)v18);
  return v23;
}

//----- (00478760) --------------------------------------------------------
char __thiscall CCharacter::MoveItem(CCharacter *this, __int64 takeType)
{
  CPerformanceCheck *Instance; // eax
  Item::CItemContainer *ItemContainer; // eax
  Item::CItem *Item; // edi
  Item::CItem *v6; // eax
  unsigned __int16 m_usProtoTypeID; // cx
  unsigned __int16 v8; // dx
  unsigned __int8 m_cNumOrDurability; // cl
  CCharacter *m_lpExchangeCharacter; // ecx
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned __int8 m_cMaxNumOrDurability; // [esp+1Ah] [ebp-3Eh]
  unsigned __int8 v14; // [esp+1Bh] [ebp-3Dh]
  Item::CItemContainer *lpSrcContainer; // [esp+1Ch] [ebp-3Ch]
  Item::CItemContainer *lpDstContainer; // [esp+28h] [ebp-30h]
  CPerformanceInstrument functionInstrument; // [esp+34h] [ebp-24h] BYREF
  int v18; // [esp+54h] [ebp-4h]

  functionInstrument.m_szfunctionName = "CCharacter::MoveItem";
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, "CCharacter::MoveItem", 0.0);
  functionInstrument.m_stopTime.QuadPart = 0LL;
  functionInstrument.m_startTime.QuadPart = __rdtsc();
  v18 = 0;
  lpSrcContainer = CCharacter::GetItemContainer(this, takeType & 0xF);
  ItemContainer = CCharacter::GetItemContainer(this, BYTE2(takeType) & 0xF);
  lpDstContainer = ItemContainer;
  if ( !lpSrcContainer || !ItemContainer )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::MoveItem",
      aDWorkRylSource_101,
      159,
      aCid0x08xSrcpos,
      this->m_dwCID,
      (unsigned __int8)takeType & 0xF,
      BYTE2(takeType) & 0xF);
    goto LABEL_34;
  }
  Item = CCharacter::GetItem(this, takeType);
  if ( Item )
  {
    if ( !CCharacter::RemoveItem(this, takeType) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::MoveItem",
        aDWorkRylSource_101,
        178,
        aCid0x08x_38,
        this->m_dwCID,
        (unsigned __int8)takeType & 0xF,
        (unsigned __int16)takeType >> 4);
      lpSrcContainer->DumpItemInfo(lpSrcContainer);
      goto LABEL_34;
    }
    v6 = CCharacter::GetItem(this, *(int *)((char *)&takeType + 2));
    if ( !v6 )
    {
      if ( !CCharacter::SetItem(this, *(int *)((char *)&takeType + 2), Item) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::MoveItem",
          aDWorkRylSource_101,
          193,
          aCid0x08x_246,
          this->m_dwCID,
          BYTE2(takeType) & 0xF,
          WORD1(takeType) >> 4,
          Item->m_ItemData.m_usProtoTypeID,
          Item->m_ItemData.m_cNumOrDurability);
        goto MOVE_ROLLBACK;
      }
LABEL_29:
      v18 = -1;
      CPerformanceInstrument::Stop(&functionInstrument);
      return 1;
    }
    if ( (BYTE2(takeType) & 0xF) == 0xA )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::MoveItem",
        aDWorkRylSource_101,
        202,
        aCid0x08x_204,
        this->m_dwCID,
        (unsigned __int8)takeType & 0xF,
        (unsigned __int16)takeType >> 4,
        BYTE2(takeType) & 0xF,
        WORD1(takeType) >> 4);
    }
    else if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 && (v6->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
    {
      m_usProtoTypeID = Item->m_ItemData.m_usProtoTypeID;
      v8 = v6->m_ItemData.m_usProtoTypeID;
      if ( m_usProtoTypeID == v8 )
      {
        m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability;
        if ( m_cNumOrDurability >= BYTE4(takeType) )
        {
          v14 = v6->m_ItemData.m_cNumOrDurability;
          m_cMaxNumOrDurability = v6->m_cMaxNumOrDurability;
          if ( BYTE4(takeType) + v14 <= m_cMaxNumOrDurability )
          {
            Item->m_ItemData.m_cNumOrDurability = m_cNumOrDurability - BYTE4(takeType);
            v6->m_ItemData.m_cNumOrDurability += BYTE4(takeType);
            if ( (BYTE2(takeType) & 0xF) == 8 )
            {
              m_lpExchangeCharacter = this->m_Exchange.m_lpExchangeCharacter;
              if ( m_lpExchangeCharacter )
              {
                m_lpGameClientDispatch = (CSendStream *)m_lpExchangeCharacter->m_lpGameClientDispatch;
                if ( m_lpGameClientDispatch )
                  GameClientSendPacket::SendCharExchangeItem(
                    m_lpGameClientDispatch + 8,
                    this->m_dwCID,
                    0,
                    v6,
                    *(Item::ItemPos *)((char *)&takeType + 2),
                    1,
                    0);
              }
            }
            if ( Item->m_ItemData.m_cNumOrDurability )
            {
              if ( !CCharacter::SetItem(this, takeType, Item) )
              {
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_SYSERR,
                  "CCharacter::MoveItem",
                  aDWorkRylSource_101,
                  264,
                  aCid0x08x_81,
                  this->m_dwCID,
                  (unsigned __int8)takeType & 0xF,
                  (unsigned __int16)takeType >> 4);
                lpSrcContainer->DumpItemInfo(lpSrcContainer);
                CServerLog::DetailLog(&g_Log, LOG_SYSERR, "CCharacter::MoveItem", aDWorkRylSource_101, 267, aSrc);
                ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
              }
            }
            else
            {
              ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
            }
            goto LABEL_29;
          }
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::MoveItem",
            aDWorkRylSource_101,
            231,
            aCid0x08x_198,
            this->m_dwCID,
            v14,
            BYTE4(takeType),
            m_cMaxNumOrDurability);
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CCharacter::MoveItem",
            aDWorkRylSource_101,
            223,
            aCid0x08x_202,
            this->m_dwCID,
            m_cNumOrDurability,
            BYTE4(takeType));
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::MoveItem",
          aDWorkRylSource_101,
          216,
          aCid0x08x_65,
          this->m_dwCID,
          m_usProtoTypeID,
          v8);
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::MoveItem",
        aDWorkRylSource_101,
        209,
        aCid0x08x_209,
        this->m_dwCID,
        Item->m_ItemData.m_usProtoTypeID,
        v6->m_ItemData.m_usProtoTypeID);
    }
MOVE_ROLLBACK:
    lpDstContainer->DumpItemInfo(lpDstContainer);
    if ( !CCharacter::SetItem(this, takeType, Item) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_SYSERR,
        "CCharacter::MoveItem",
        aDWorkRylSource_101,
        282,
        aCid0x08x_81,
        this->m_dwCID,
        (unsigned __int8)takeType & 0xF,
        (unsigned __int16)takeType >> 4);
      lpSrcContainer->DumpItemInfo(lpSrcContainer);
      CServerLog::DetailLog(&g_Log, LOG_SYSERR, "CCharacter::MoveItem", aDWorkRylSource_101, 285, aSrc);
      ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
    }
    goto LABEL_34;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCharacter::MoveItem",
    aDWorkRylSource_101,
    168,
    aCid0x08x_90,
    this->m_dwCID,
    (unsigned __int8)takeType & 0xF,
    (unsigned __int16)takeType >> 4);
  lpSrcContainer->DumpItemInfo(lpSrcContainer);
LABEL_34:
  v18 = -1;
  CPerformanceInstrument::Stop(&functionInstrument);
  return 0;
}

//----- (00478C50) --------------------------------------------------------
char __thiscall CCharacter::SwapItem(CCharacter *this, __int64 SrcTake, TakeType DstTake)
{
  Item::CListContainer *p_m_ExtraSpace; // edi
  Item::CItem *v5; // ebp
  __int64 v7; // [esp-8h] [ebp-1Ch]
  __int64 v8; // [esp-8h] [ebp-1Ch]
  __int64 v9; // [esp-8h] [ebp-1Ch]
  __int64 v10; // [esp-8h] [ebp-1Ch]
  __int64 v11; // [esp-8h] [ebp-1Ch]
  int SrcTakea; // [esp+18h] [ebp+4h]
  int SrcTakeb; // [esp+18h] [ebp+4h]

  if ( (*(_BYTE *)&DstTake.m_dstPos & 0xF) != 6 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SwapItem",
      aDWorkRylSource_101,
      382,
      aCid0x08xDsttak,
      this->m_dwCID);
    return 0;
  }
  p_m_ExtraSpace = &this->m_ExtraSpace;
  v5 = (Item::CItem *)((int (__thiscall *)(Item::CListContainer *, __int16))this->m_ExtraSpace.GetItem)(
                        &this->m_ExtraSpace,
                        38);
  if ( v5 )
  {
    if ( (SrcTake & 0xF) != 6 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SwapItem",
        aDWorkRylSource_101,
        417,
        aCid0x08x_292,
        this->m_dwCID);
      return 0;
    }
    if ( !((unsigned __int8 (__thiscall *)(Item::CListContainer *, __int16))p_m_ExtraSpace->RemoveItem)(
            &this->m_ExtraSpace,
            38) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SwapItem",
        aDWorkRylSource_101,
        424,
        aCid0x08x_120,
        this->m_dwCID);
      return 0;
    }
    *(TakeType *)&v10 = DstTake;
    if ( !CCharacter::MoveItem(this, v10) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SwapItem",
        aDWorkRylSource_101,
        431,
        aCid0x08x_99,
        this->m_dwCID);
      if ( !((unsigned __int8 (__thiscall *)(Item::CListContainer *, __int16, Item::CItem *))p_m_ExtraSpace->SetItem)(
              &this->m_ExtraSpace,
              38,
              v5) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SwapItem",
          aDWorkRylSource_101,
          435,
          aCid0x08x_143,
          this->m_dwCID);
      return 0;
    }
    if ( !CCharacter::SetItem(this, *(int *)((char *)&SrcTake + 2), v5) )
    {
      HIDWORD(v11) = this->m_dwCID;
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacter::SwapItem", aDWorkRylSource_101, 444, aCid0x08x_27);
      HIWORD(SrcTakeb) = DstTake.m_srcPos;
      LOWORD(SrcTakeb) = DstTake.m_dstPos;
      LODWORD(v11) = SrcTakeb;
      BYTE4(v11) = DstTake.m_cNum;
      if ( !CCharacter::MoveItem(this, v11) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SwapItem",
          aDWorkRylSource_101,
          448,
          aCid0x08x_163,
          this->m_dwCID);
      if ( !((unsigned __int8 (__thiscall *)(Item::CListContainer *, __int16, Item::CItem *))p_m_ExtraSpace->SetItem)(
              &this->m_ExtraSpace,
              38,
              v5) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SwapItem",
          aDWorkRylSource_101,
          453,
          aCid0x08x_143,
          this->m_dwCID);
      return 0;
    }
  }
  else
  {
    *(TakeType *)&v7 = DstTake;
    if ( !CCharacter::MoveItem(this, v7) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SwapItem",
        aDWorkRylSource_101,
        394,
        aCid0x08x_99,
        this->m_dwCID);
      return 0;
    }
    LODWORD(v8) = SrcTake;
    BYTE4(v8) = BYTE4(SrcTake);
    if ( !CCharacter::MoveItem(this, v8) )
    {
      HIDWORD(v9) = this->m_dwCID;
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCharacter::SwapItem", aDWorkRylSource_101, 401, aCid0x08xSrctak);
      LOWORD(SrcTakea) = DstTake.m_dstPos;
      HIWORD(SrcTakea) = DstTake.m_srcPos;
      LODWORD(v9) = SrcTakea;
      BYTE4(v9) = DstTake.m_cNum;
      if ( !CCharacter::MoveItem(this, v9) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SwapItem",
          aDWorkRylSource_101,
          406,
          aCid0x08x_163,
          this->m_dwCID);
      return 0;
    }
  }
  return 1;
}
// 478CB4: variable 'v7' is possibly undefined
// 478CE4: variable 'v8' is possibly undefined
// 478DA7: variable 'v10' is possibly undefined

//----- (00478EE0) --------------------------------------------------------
Item::CItem *__thiscall CCharacter::Drop(CCharacter *this, int SrcPos, unsigned __int8 cNum)
{
  Item::CItemContainer *ItemContainer; // ebp
  Item::CItem *Item; // eax
  Item::CItem *v7; // esi
  unsigned __int8 m_cNumOrDurability; // al
  unsigned __int16 m_usProtoTypeID; // bx
  Item::CItem *v10; // eax

  ItemContainer = CCharacter::GetItemContainer(this, SrcPos & 0xF);
  if ( !ItemContainer )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Drop",
      aDWorkRylSource_101,
      526,
      aCid0x08xSrcpos_0,
      this->m_dwCID,
      SrcPos & 0xF);
    return 0;
  }
  Item = CCharacter::GetItem(this, SrcPos);
  v7 = Item;
  if ( !Item )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Drop",
      aDWorkRylSource_101,
      534,
      aCid0x08x_39,
      this->m_dwCID,
      SrcPos & 0xF,
      (unsigned __int16)SrcPos >> 4);
LABEL_5:
    ItemContainer->DumpItemInfo(ItemContainer);
    return 0;
  }
  if ( (Item->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8
    && (m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability, cNum < m_cNumOrDurability) )
  {
    m_usProtoTypeID = v7->m_ItemData.m_usProtoTypeID;
    v7->m_ItemData.m_cNumOrDurability = m_cNumOrDurability - cNum;
    Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, m_usProtoTypeID);
    v7 = v10;
    if ( !v10 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::Drop",
        aDWorkRylSource_101,
        550,
        aCid0x08x_176,
        this->m_dwCID,
        m_usProtoTypeID);
      return 0;
    }
    v10->m_ItemData.m_cNumOrDurability = cNum;
  }
  else if ( !CCharacter::RemoveItem(this, SrcPos) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::Drop",
      aDWorkRylSource_101,
      562,
      aCid0x08x_24,
      this->m_dwCID,
      SrcPos & 0xF,
      (unsigned __int16)SrcPos >> 4);
    goto LABEL_5;
  }
  return v7;
}
// 478FB5: variable 'v10' is possibly undefined

//----- (00479030) --------------------------------------------------------
char __thiscall CCharacter::UseLottery(CCharacter *this, unsigned __int16 usItemID, int itemPos)
{
  Item::CItem *v4; // eax
  Item::CItem *v5; // edi
  CGameClientDispatch *v6; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  CLotteryEvent::PrizeLottery(&CSingleton<CGameEventMgr>::ms_pSingleton->m_LotteryEvent, usItemID);
  v5 = v4;
  if ( v4 )
  {
    if ( CCharacter::SetItem(this, itemPos, v4) )
    {
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharLotteryResult(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          (Item::ItemPos)itemPos,
          v5,
          0);
      return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::UseLottery",
        aDWorkRylSource_101,
        592,
        aCid0x08x_72,
        this->m_dwCID,
        usItemID,
        v5->m_ItemData.m_usProtoTypeID,
        itemPos & 0xF,
        (unsigned __int16)itemPos >> 4);
      return 0;
    }
  }
  else
  {
    v6 = this->m_lpGameClientDispatch;
    if ( v6 )
      GameClientSendPacket::SendCharLotteryResult(&v6->m_SendStream, this->m_dwCID, (Item::ItemPos)itemPos, 0, 2u);
    return 1;
  }
}
// 479048: variable 'v4' is possibly undefined

//----- (00479100) --------------------------------------------------------
Item::CItem *__thiscall CCharacter::GetItem(CCharacter *this, int SrcPos)
{
  int v2; // eax
  Item::CItem *result; // eax

  v2 = SrcPos;
  switch ( SrcPos & 0xF )
  {
    case 1:
      result = (Item::CItem *)((int (__thiscall *)(Item::CEquipmentsContainer *, int))this->m_Equipments.GetItem)(
                                &this->m_Equipments,
                                SrcPos);
      break;
    case 2:
      result = (Item::CItem *)((int (__thiscall *)(Item::CArrayContainer *, int))this->m_Inventory.GetItem)(
                                &this->m_Inventory,
                                SrcPos);
      break;
    case 6:
      LOWORD(SrcPos) = SrcPos & 0xF | 0x20;
      v2 = SrcPos;
      goto $L100238;
    case 7:
$L100238:
      result = (Item::CItem *)((int (__thiscall *)(Item::CListContainer *, int))this->m_ExtraSpace.GetItem)(
                                &this->m_ExtraSpace,
                                v2);
      break;
    case 8:
      result = (Item::CItem *)((int (__thiscall *)(Item::CExchangeContainer *, int))this->m_Exchange.GetItem)(
                                &this->m_Exchange,
                                SrcPos);
      break;
    case 9:
      result = (Item::CItem *)((int (__thiscall *)(Item::CDepositContainer *, int))this->m_Deposit.GetItem)(
                                &this->m_Deposit,
                                SrcPos);
      break;
    case 0xA:
      result = (Item::CItem *)((int (__thiscall *)(Item::CStallContainer *, int))this->m_Stall.GetItem)(
                                &this->m_Stall,
                                SrcPos);
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (004791C0) --------------------------------------------------------
char __thiscall CCharacter::RemoveItem(CCharacter *this, int SrcPos)
{
  int v2; // edi
  Item::CItem *Item; // eax
  Item::CItem *v5; // ebx
  char result; // al
  __int16 v7; // bp
  const Item::ItemInfo *m_ItemInfo; // eax
  int v9; // eax
  Position v10[2]; // [esp-10h] [ebp-24h] BYREF
  int v11; // [esp+10h] [ebp-4h]

  v2 = SrcPos;
  Item = CCharacter::GetItem(this, SrcPos);
  v5 = Item;
  if ( Item )
  {
    v7 = SrcPos & 0xF;
    if ( v7 != 10 && (*(_WORD *)&Item->m_itemPos_Real & 0xF) == 0xA )
    {
      LOWORD(v11) = Item->m_itemPos_Real;
      ((void (__thiscall *)(Item::CStallContainer *, _WORD))this->m_Stall.RemoveItem)(&this->m_Stall, v11);
    }
    switch ( SrcPos & 0xF )
    {
      case 1:
        result = ((int (__thiscall *)(Item::CEquipmentsContainer *, int))this->m_Equipments.RemoveItem)(
                   &this->m_Equipments,
                   SrcPos);
        break;
      case 2:
        m_ItemInfo = v5->m_ItemInfo;
        if ( (m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
        {
          LOWORD(m_ItemInfo) = v5->m_ItemData.m_cNumOrDurability;
          v9 = -(int)m_ItemInfo;
        }
        else
        {
          LOWORD(v9) = -1;
        }
        memset(v10, 0, 12);
        CCharacter::CheckTrigger(this, 5u, v5->m_ItemData.m_usProtoTypeID, v10[0], v9);
        result = ((int (__thiscall *)(Item::CArrayContainer *, int))this->m_Inventory.RemoveItem)(
                   &this->m_Inventory,
                   SrcPos);
        break;
      case 6:
        LOWORD(SrcPos) = v7 | 0x20;
        v2 = SrcPos;
        goto $L100264;
      case 7:
$L100264:
        result = ((int (__thiscall *)(Item::CListContainer *, int))this->m_ExtraSpace.RemoveItem)(
                   &this->m_ExtraSpace,
                   v2);
        break;
      case 8:
        result = ((int (__thiscall *)(Item::CExchangeContainer *, int))this->m_Exchange.RemoveItem)(
                   &this->m_Exchange,
                   SrcPos);
        break;
      case 9:
        result = ((int (__thiscall *)(Item::CDepositContainer *, int))this->m_Deposit.RemoveItem)(
                   &this->m_Deposit,
                   SrcPos);
        break;
      case 0xA:
        result = ((int (__thiscall *)(Item::CStallContainer *, int))this->m_Stall.RemoveItem)(&this->m_Stall, SrcPos);
        break;
      default:
        result = 1;
        break;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::RemoveItem",
      aDWorkRylSource_17,
      36,
      aCid0x08x_319,
      this->m_dwCID,
      SrcPos & 0xF,
      (unsigned __int16)SrcPos >> 4);
    return 0;
  }
  return result;
}

//----- (00479370) --------------------------------------------------------
bool __thiscall CCharacter::SetItem(CCharacter *this, int SrcPos, Item::CItem *lpItem)
{
  int v3; // edi
  bool result; // al
  __int16 m_cNumOrDurability; // ax
  Position v7[2]; // [esp-10h] [ebp-1Ch] BYREF

  v3 = SrcPos;
  switch ( SrcPos & 0xF )
  {
    case 1:
      result = ((int (__thiscall *)(Item::CEquipmentsContainer *, int, Item::CItem *))this->m_Equipments.SetItem)(
                 &this->m_Equipments,
                 SrcPos,
                 lpItem);
      break;
    case 2:
      if ( (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
        m_cNumOrDurability = lpItem->m_ItemData.m_cNumOrDurability;
      else
        m_cNumOrDurability = 1;
      memset(v7, 0, 12);
      CCharacter::CheckTrigger(this, 5u, lpItem->m_ItemData.m_usProtoTypeID, v7[0], m_cNumOrDurability);
      result = ((int (__thiscall *)(Item::CArrayContainer *, int, Item::CItem *))this->m_Inventory.SetItem)(
                 &this->m_Inventory,
                 SrcPos,
                 lpItem);
      break;
    case 6:
      LOWORD(SrcPos) = SrcPos & 0xF | 0x20;
      v3 = SrcPos;
      goto $L100283;
    case 7:
$L100283:
      result = ((int (__thiscall *)(Item::CListContainer *, int, Item::CItem *))this->m_ExtraSpace.SetItem)(
                 &this->m_ExtraSpace,
                 v3,
                 lpItem);
      break;
    case 8:
      result = ((int (__thiscall *)(Item::CExchangeContainer *, int, Item::CItem *))this->m_Exchange.SetItem)(
                 &this->m_Exchange,
                 SrcPos,
                 lpItem);
      break;
    case 9:
      result = ((int (__thiscall *)(Item::CDepositContainer *, int, Item::CItem *))this->m_Deposit.SetItem)(
                 &this->m_Deposit,
                 SrcPos,
                 lpItem);
      break;
    case 0xA:
      result = ((int (__thiscall *)(Item::CStallContainer *, int, Item::CItem *))this->m_Stall.SetItem)(
                 &this->m_Stall,
                 SrcPos,
                 lpItem);
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (004794B0) --------------------------------------------------------
void __thiscall AddressInfo::AddressInfo(
        AddressInfo *this,
        const sockaddr_in *PublicAddress,
        const sockaddr_in *PrivateAddress,
        unsigned int dwCharID)
{
  this->m_PublicAddress = *PublicAddress;
  this->m_PrivateAddress = *PrivateAddress;
  this->m_dwCharID = dwCharID;
}

//----- (00479500) --------------------------------------------------------
bool __thiscall CParty::Join(
        CParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        const char *strSenderName,
        unsigned __int16 wMapIndex)
{
  return 0;
}

//----- (00479510) --------------------------------------------------------
int __thiscall CParty::Leave(
        CParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        unsigned __int16 wMapIndex)
{
  return 0;
}

//----- (********) --------------------------------------------------------
int __thiscall CParty::Logout(CParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID)
{
  return 0;
}

//----- (********) --------------------------------------------------------
bool __thiscall CParty::AutoRouting(
        CParty *this,
        AtType attackType,
        CAggresiveCreature **pDefenders,
        unsigned __int8 *cDefenserJudges,
        CAggresiveCreature *lpOffencer,
        float fDistance,
        unsigned __int8 cTargetType)
{
  return 0;
}

//----- (********) --------------------------------------------------------
char __thiscall CParty::IsMember(CParty *this, unsigned int dwMemberCID)
{
  int m_cMemberNum; // edx
  int v4; // eax
  unsigned int *i; // ecx

  if ( dwMemberCID )
  {
    m_cMemberNum = this->m_Party.m_cMemberNum;
    v4 = 0;
    if ( this->m_Party.m_cMemberNum )
    {
      for ( i = this->m_Party.MemberCID; *i != dwMemberCID; ++i )
      {
        if ( ++v4 >= m_cMemberNum )
          return 0;
      }
      return 1;
    }
    else
    {
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CParty::IsMember", FileName, 66, (char *)&byte_4EF23C);
    return 0;
  }
}

//----- (004795B0) --------------------------------------------------------
char __thiscall CMonsterParty::Join(
        CMonsterParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        const char *strSenderName,
        unsigned __int16 wMapIndex)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CMsgProcessMgr *m_pVirtualMonsterMgr; // ecx
  CMonster *Castle; // eax
  CCreatureManager *v11; // eax
  CMonster *v12; // edi
  int v13; // eax
  unsigned int *MemberCID; // ecx
  unsigned __int8 v15; // cl
  int v16; // edi
  int v17; // eax
  CMonster **m_pMemberPointer; // ecx

  if ( this->m_Party.m_cMemberNum >= 0xAu )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterParty::Join", FileName, 166, (char *)&byte_4EF2F0);
    return 0;
  }
  if ( wMapIndex )
  {
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, wMapIndex);
    if ( !VirtualArea )
      goto LABEL_9;
    m_pVirtualMonsterMgr = (CMsgProcessMgr *)VirtualArea->m_pVirtualMonsterMgr;
    if ( !m_pVirtualMonsterMgr )
      goto LABEL_9;
    Castle = (CMonster *)Castle::CCastleMgr::GetCastle(m_pVirtualMonsterMgr, dwSenderCID);
  }
  else
  {
    v11 = CCreatureManager::GetInstance();
    Castle = CCreatureManager::GetMonster(v11, dwSenderCID);
  }
  v12 = Castle;
  if ( !Castle )
  {
LABEL_9:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonsterParty::Join",
      FileName,
      188,
      (char *)&byte_4EF290,
      this->m_Party.m_dwPartyID,
      dwSenderCID);
    return 0;
  }
  v13 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    MemberCID = this->m_Party.MemberCID;
    while ( *MemberCID != dwSenderCID )
    {
      ++v13;
      ++MemberCID;
      if ( v13 >= this->m_Party.m_cMemberNum )
        goto LABEL_14;
    }
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CMonsterParty::Join", FileName, 196, (char *)&byte_4EF250);
    return 0;
  }
  else
  {
LABEL_14:
    this->m_Party.MemberCID[this->m_Party.m_cMemberNum] = dwSenderCID;
    this->m_Party.ServerID[this->m_Party.m_cMemberNum] = dwReferenceID;
    LogChantBug(v12, this, &szMessage, "CMonsterParty::Join", FileName, 204);
    this->m_pMemberPointer[this->m_Party.m_cMemberNum] = v12;
    CPartySpellMgr::AddMember(&this->m_PartySpellMgr, v12);
    v15 = this->m_Party.m_cMemberNum + 1;
    v16 = v15;
    v17 = 0;
    this->m_Party.m_cMemberNum = v15;
    this->m_nAvgLevel = 0;
    if ( v15 )
    {
      m_pMemberPointer = this->m_pMemberPointer;
      do
      {
        this->m_nAvgLevel += LOWORD((*m_pMemberPointer)->m_CreatureStatus.m_nLevel);
        ++v17;
        ++m_pMemberPointer;
      }
      while ( v17 < this->m_Party.m_cMemberNum );
    }
    this->m_nAvgLevel /= v16;
    return 1;
  }
}

//----- (00479760) --------------------------------------------------------
int __thiscall CMonsterParty::Leave(
        CMonsterParty *this,
        unsigned int dwSenderCID,
        unsigned int dwReferenceID,
        unsigned __int16 wMapIndex)
{
  int v5; // ebp
  unsigned int *MemberCID; // eax
  VirtualArea::CVirtualAreaMgr *v8; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CMsgProcessMgr *m_pVirtualMonsterMgr; // ecx
  CMonster *Monster; // eax
  CCreatureManager *Instance; // eax
  CAggresiveCreature *v13; // edi
  CSpell *v14; // eax
  unsigned __int8 v15; // al
  int v16; // ecx
  unsigned int *v17; // eax
  unsigned __int8 m_cMemberNum; // cl
  int v19; // edi
  int v20; // eax
  CMonster **m_pMemberPointer; // ecx

  v5 = 0;
  if ( !this->m_Party.m_cMemberNum )
  {
LABEL_5:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonsterParty::Leave",
      FileName,
      308,
      (char *)&byte_4EF3B0,
      this->m_Party.m_dwPartyID,
      dwSenderCID);
    return -1;
  }
  MemberCID = this->m_Party.MemberCID;
  while ( *MemberCID != dwSenderCID )
  {
    ++v5;
    ++MemberCID;
    if ( v5 >= this->m_Party.m_cMemberNum )
      goto LABEL_5;
  }
  if ( !wMapIndex )
  {
    Instance = CCreatureManager::GetInstance();
    Monster = CCreatureManager::GetMonster(Instance, dwSenderCID);
LABEL_11:
    v13 = Monster;
    if ( Monster )
    {
      LogChantBug(Monster, this, &byte_4EF370, "CMonsterParty::Leave", FileName, 261);
      CPartySpellMgr::RemoveMember(&this->m_PartySpellMgr, v13);
      v14 = v13[1].m_SpellMgr.m_AffectedInfo.m_pChant[7];
      if ( v14 == (CSpell *)this->m_Party.m_dwPartyID )
        v13->m_pParty = 0;
      else
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CMonsterParty::Leave",
          FileName,
          268,
          aCid0x08x_29,
          v13->m_dwCID,
          v14,
          this->m_Party.m_dwPartyID);
    }
    goto LABEL_15;
  }
  v8 = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v8, wMapIndex);
  if ( VirtualArea )
  {
    m_pVirtualMonsterMgr = (CMsgProcessMgr *)VirtualArea->m_pVirtualMonsterMgr;
    if ( m_pVirtualMonsterMgr )
    {
      Monster = (CMonster *)Castle::CCastleMgr::GetCastle(m_pVirtualMonsterMgr, dwSenderCID);
      goto LABEL_11;
    }
  }
LABEL_15:
  v15 = this->m_Party.m_cMemberNum - 1;
  this->m_Party.m_cMemberNum = v15;
  v16 = v5;
  if ( v5 < v15 )
  {
    v17 = &this->m_Party.MemberCID[v5];
    do
    {
      *v17 = v17[1];
      v17[10] = v17[11];
      *(unsigned int *)((char *)v17 + 83) = *(unsigned int *)((char *)v17 + 87);
      ++v16;
      ++v17;
    }
    while ( v16 < this->m_Party.m_cMemberNum );
  }
  this->m_Party.MemberCID[this->m_Party.m_cMemberNum] = 0;
  this->m_Party.ServerID[this->m_Party.m_cMemberNum] = 0;
  this->m_pMemberPointer[this->m_Party.m_cMemberNum] = 0;
  m_cMemberNum = this->m_Party.m_cMemberNum;
  this->m_nAvgLevel = 0;
  if ( m_cMemberNum )
  {
    v19 = m_cMemberNum;
    v20 = 0;
    m_pMemberPointer = this->m_pMemberPointer;
    do
    {
      this->m_nAvgLevel += LOWORD((*m_pMemberPointer)->m_CreatureStatus.m_nLevel);
      ++v20;
      ++m_pMemberPointer;
    }
    while ( v20 < this->m_Party.m_cMemberNum );
    this->m_nAvgLevel /= v19;
  }
  return 0;
}
// 4798D3: conditional instruction was optimized away because cl.1!=0

//----- (00479920) --------------------------------------------------------
char __thiscall CMonsterParty::Destory(CMonsterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID)
{
  CMonster **m_pMemberPointer; // esi
  int v5; // ebx
  CMonster *v6; // eax

  LogChantBug(0, this, &byte_4EF468, "CMonsterParty::Destory", FileName, 328);
  CPartySpellMgr::ClearMember(&this->m_PartySpellMgr);
  m_pMemberPointer = this->m_pMemberPointer;
  v5 = 2;
  do
  {
    v6 = *m_pMemberPointer;
    if ( *m_pMemberPointer )
    {
      if ( v6->m_dwPID == this->m_Party.m_dwPartyID )
      {
        v6->m_pParty = 0;
        (*m_pMemberPointer)->m_dwPID = 0;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CMonsterParty::Destory",
          FileName,
          339,
          aCid0x08x_100,
          v6->m_dwCID,
          v6->m_dwPID,
          this->m_Party.m_dwPartyID);
      }
    }
    ++m_pMemberPointer;
    --v5;
  }
  while ( v5 );
  return 1;
}

//----- (004799C0) --------------------------------------------------------
void __thiscall CCharacterParty::PrepareLogout(CCharacterParty *this, unsigned int dwMemberID)
{
  int m_cMemberNum; // ecx
  int v4; // esi
  unsigned int *i; // eax
  CCharacter *v6; // eax

  m_cMemberNum = this->m_Party.m_cMemberNum;
  v4 = 0;
  if ( m_cMemberNum )
  {
    for ( i = this->m_Party.MemberCID; *i != dwMemberID; ++i )
    {
      if ( ++v4 >= m_cMemberNum )
        return;
    }
    v6 = this->m_pMemberPointer[v4];
    if ( v6 )
    {
      LogChantBug(v6, this, &byte_4EF4A4, "CCharacterParty::PrepareLogout", FileName, 762);
      CPartySpellMgr::RemoveMember(&this->m_PartySpellMgr, this->m_pMemberPointer[v4]);
    }
    this->m_pMemberPointer[v4] = 0;
  }
}

//----- (00479A40) --------------------------------------------------------
unsigned __int8 __thiscall CCharacterParty::GetLoggedMemberAverageLevel(CCharacterParty *this)
{
  int m_cMemberNum; // eax
  unsigned __int8 v2; // bl
  unsigned __int16 v3; // dx
  CCharacter **m_pMemberPointer; // ecx
  int v5; // esi
  int v6; // eax

  m_cMemberNum = this->m_Party.m_cMemberNum;
  v2 = 0;
  if ( !this->m_Party.m_cMemberNum )
    goto LABEL_7;
  v3 = 0;
  m_pMemberPointer = this->m_pMemberPointer;
  v5 = m_cMemberNum;
  do
  {
    if ( *m_pMemberPointer )
    {
      v3 += LOWORD((*m_pMemberPointer)->m_CreatureStatus.m_nLevel);
      ++v2;
    }
    ++m_pMemberPointer;
    --v5;
  }
  while ( v5 );
  if ( v2 )
  {
    return v3 / (int)v2;
  }
  else
  {
LABEL_7:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacterParty::GetLoggedMemberAverageLevel",
      FileName,
      839,
      (char *)&byte_4EF518);
    LOBYTE(v6) = 0;
  }
  return v6;
}

//----- (00479AE0) --------------------------------------------------------
char __thiscall CCharacterParty::MakeTeamBattleInfo(
        CCharacterParty *this,
        char *szPacket,
        unsigned __int16 *dwPacketSize,
        CCharacter *pChallenger,
        char cCmd)
{
  CServerSetup *Instance; // eax
  int v7; // ebp
  unsigned int *ServerID; // ebx
  int v9; // edx
  char *v10; // ecx
  unsigned int cCmda; // [esp+24h] [ebp+10h]

  *dwPacketSize = 34;
  if ( pChallenger )
  {
    *((_DWORD *)szPacket + 3) = pChallenger->m_dwCID;
    strncpy(szPacket + 16, pChallenger->m_DBData.m_Info.Name, 0x10u);
  }
  szPacket[32] = cCmd;
  szPacket[33] = 0;
  Instance = CServerSetup::GetInstance();
  cCmda = CServerSetup::GetServerID(Instance);
  v7 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    ServerID = this->m_Party.ServerID;
    do
    {
      v9 = *(unsigned int *)((char *)ServerID + 43);
      if ( v9 && *ServerID == cCmda )
      {
        v10 = &szPacket[4 * (unsigned __int8)szPacket[33] + (unsigned __int8)szPacket[33]];
        *(_DWORD *)(v10 + 34) = *(_DWORD *)(v9 + 32);
        v10[38] = *(_BYTE *)(v9 + 144);
        ++szPacket[33];
        *dwPacketSize += 5;
      }
      ++v7;
      ++ServerID;
    }
    while ( v7 < this->m_Party.m_cMemberNum );
  }
  return PacketWrap::WrapCrypt(szPacket, *dwPacketSize, 0x63u, 0, 0);
}

//----- (00479BA0) --------------------------------------------------------
void __thiscall CCharacterParty::MovePos(CCharacterParty *this, POS NewPos, char cZone, bool bSitDown)
{
  int v5; // edi
  CCharacter **m_pMemberPointer; // esi

  v5 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    m_pMemberPointer = this->m_pMemberPointer;
    do
    {
      if ( *m_pMemberPointer )
        CCharacter::MovePos(*m_pMemberPointer, (Position)NewPos, cZone, bSitDown);
      ++v5;
      ++m_pMemberPointer;
    }
    while ( v5 < this->m_Party.m_cMemberNum );
  }
}

//----- (00479C00) --------------------------------------------------------
void __thiscall CCharacterParty::MoveZone(CCharacterParty *this, POS NewPos, char Zone, char Channel)
{
  CCharacterParty *v4; // eax
  int v5; // edi
  CCharacter **m_pMemberPointer; // esi

  v4 = this;
  v5 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    m_pMemberPointer = this->m_pMemberPointer;
    do
    {
      if ( *m_pMemberPointer )
      {
        CCharacter::MoveZone(*m_pMemberPointer, NewPos, Zone, Channel);
        v4 = this;
      }
      ++v5;
      ++m_pMemberPointer;
    }
    while ( v5 < v4->m_Party.m_cMemberNum );
  }
}

//----- (00479C60) --------------------------------------------------------
void __thiscall CCharacterParty::SendPartyCmdInfo(
        CCharacterParty *this,
        PktPC::PartyCmd Command,
        CCharacter *lpCharacter)
{
  CCell *m_lpCell; // esi
  unsigned int m_dwPartyID; // ecx
  unsigned int m_dwCID; // edx
  PktPCInfo pktPCInfo; // [esp+0h] [ebp-18h] BYREF

  if ( lpCharacter )
  {
    m_lpCell = lpCharacter->m_CellPos.m_lpCell;
    if ( m_lpCell )
    {
      m_dwPartyID = this->m_Party.m_dwPartyID;
      pktPCInfo.m_cCmd = Command;
      m_dwCID = lpCharacter->m_dwCID;
      pktPCInfo.m_dwPartyID = m_dwPartyID;
      pktPCInfo.m_dwMemberID = m_dwCID;
      if ( PacketWrap::WrapCrypt((char *)&pktPCInfo, 0x15u, 0x4Bu, 0, 0) )
        CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktPCInfo, 0x15u, 0x4Bu);
    }
  }
}

//----- (00479CC0) --------------------------------------------------------
void __thiscall CCharacterParty::SendAllLoggedMember(
        CCharacterParty *this,
        char *szPacket,
        unsigned int dwPacketSize,
        unsigned int dwExclusion,
        unsigned __int8 cCMD_In)
{
  CServerSetup *Instance; // eax
  unsigned int ServerID; // ebx
  int v8; // ebp
  unsigned int *v9; // esi
  int v10; // ecx
  CSendStream *v11; // eax

  Instance = CServerSetup::GetInstance();
  ServerID = CServerSetup::GetServerID(Instance);
  v8 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    v9 = this->m_Party.ServerID;
    do
    {
      if ( !dwExclusion || dwExclusion != *(v9 - 10) )
      {
        v10 = *(unsigned int *)((char *)v9 + 43);
        if ( v10 )
        {
          if ( *v9 == ServerID )
          {
            v11 = *(CSendStream **)(v10 + 1504);
            if ( v11 )
              CSendStream::PutBuffer(v11 + 8, szPacket, dwPacketSize, cCMD_In);
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCharacterParty::SendAllLoggedMember",
              FileName,
              1392,
              (char *)&byte_4EF578,
              this->m_Party.m_dwPartyID,
              *(v9 - 10),
              *v9,
              ServerID);
          }
        }
      }
      ++v8;
      ++v9;
    }
    while ( v8 < this->m_Party.m_cMemberNum );
  }
}

//----- (00479D60) --------------------------------------------------------
void __thiscall CCharacterParty::SendDropMember(CCharacterParty *this, CCharacter *pDropMember, PktDuC::DuelCmd eCmd)
{
  int v3; // edi
  CCharacter **m_pFightingMember; // esi
  CCharacter *v5; // eax
  CSendStream *m_lpGameClientDispatch; // ecx

  v3 = 0;
  m_pFightingMember = this->m_pFightingMember;
  do
  {
    v5 = *m_pFightingMember;
    if ( !*m_pFightingMember )
      break;
    m_lpGameClientDispatch = (CSendStream *)v5->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharDuelCmd(m_lpGameClientDispatch + 8, pDropMember->m_dwCID, v5->m_dwCID, eCmd, 0);
    ++v3;
    ++m_pFightingMember;
  }
  while ( v3 < 10 );
}

//----- (00479DB0) --------------------------------------------------------
void __thiscall CCharacterParty::SendRecall(CCharacterParty *this, CCharacter *lpCaster)
{
  CServerSetup *Instance; // eax
  unsigned int ServerID; // ebp
  unsigned int *v5; // edi
  CCharacter *v6; // esi
  CSendStream *m_lpGameClientDispatch; // esi
  int nMemberIndex; // [esp+4h] [ebp-2Ch]
  PktAPAck pktAPAck; // [esp+8h] [ebp-28h] BYREF

  pktAPAck.m_dwCasterID = lpCaster->m_dwCID;
  strncpy(pktAPAck.m_szCasterName, lpCaster->m_DBData.m_Info.Name, 0x10u);
  pktAPAck.m_cCmd = 2;
  if ( PacketWrap::WrapCrypt((char *)&pktAPAck, 0x21u, 0x6Fu, 0, 0) )
  {
    Instance = CServerSetup::GetInstance();
    ServerID = CServerSetup::GetServerID(Instance);
    nMemberIndex = 0;
    if ( this->m_Party.m_cMemberNum )
    {
      v5 = this->m_Party.ServerID;
      do
      {
        v6 = *(CCharacter **)((char *)v5 + 43);
        if ( v6
          && lpCaster != v6
          && (v6->m_dwStatusFlag & 0x40000000) == 0
          && !((int (__stdcall *)(_DWORD))lpCaster->IsEnemy)(*(unsigned int *)((char *)v5 + 43))
          && lpCaster->m_CellPos.m_wMapIndex == v6->m_CellPos.m_wMapIndex )
        {
          if ( *v5 == ServerID )
          {
            m_lpGameClientDispatch = (CSendStream *)v6->m_lpGameClientDispatch;
            if ( m_lpGameClientDispatch )
              CSendStream::PutBuffer(m_lpGameClientDispatch + 8, (char *)&pktAPAck, 0x21u, 0x6Fu);
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCharacterParty::SendRecall",
              FileName,
              1514,
              (char *)&byte_4EF578,
              this->m_Party.m_dwPartyID,
              *(v5 - 10),
              *v5,
              ServerID);
          }
        }
        ++v5;
        ++nMemberIndex;
      }
      while ( nMemberIndex < this->m_Party.m_cMemberNum );
    }
  }
}

//----- (00479EF0) --------------------------------------------------------
void __thiscall CCharacterParty::SendPartyAddress(
        CCharacterParty *this,
        unsigned int dwCharID,
        const sockaddr_in *PublicAddress,
        const sockaddr_in *PrivateAddress)
{
  int v5; // edx
  unsigned int S_addr; // eax
  int v7; // edx
  int v8; // eax
  int v9; // edx
  unsigned int v10; // eax
  int v11; // edx
  int v12; // eax
  PktPAD lpPktPAD; // [esp+8h] [ebp-40h] BYREF

  v5 = *(_DWORD *)&PublicAddress->sin_family;
  lpPktPAD.m_dwPartyID = this->m_Party.m_dwPartyID;
  S_addr = PublicAddress->sin_addr.S_un.S_addr;
  *(_DWORD *)&lpPktPAD.m_PublicAddr.sin_family = v5;
  v7 = *(_DWORD *)PublicAddress->sin_zero;
  lpPktPAD.m_PublicAddr.sin_addr.S_un.S_addr = S_addr;
  v8 = *(_DWORD *)&PublicAddress->sin_zero[4];
  *(_DWORD *)lpPktPAD.m_PublicAddr.sin_zero = v7;
  v9 = *(_DWORD *)&PrivateAddress->sin_family;
  *(_DWORD *)&lpPktPAD.m_PublicAddr.sin_zero[4] = v8;
  v10 = PrivateAddress->sin_addr.S_un.S_addr;
  *(_DWORD *)&lpPktPAD.m_PrivateAddr.sin_family = v9;
  v11 = *(_DWORD *)PrivateAddress->sin_zero;
  lpPktPAD.m_PrivateAddr.sin_addr.S_un.S_addr = v10;
  v12 = *(_DWORD *)&PrivateAddress->sin_zero[4];
  lpPktPAD.m_dwSenderID = dwCharID;
  *(_DWORD *)lpPktPAD.m_PrivateAddr.sin_zero = v11;
  *(_DWORD *)&lpPktPAD.m_PrivateAddr.sin_zero[4] = v12;
  if ( PacketWrap::WrapCrypt((char *)&lpPktPAD, 0x3Au, 0x96u, 0, 0) )
    CCharacterParty::SendAllLoggedMember(this, (char *)&lpPktPAD, 0x3Au, dwCharID, 0x96u);
}

//----- (00479F90) --------------------------------------------------------
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(
        std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *result; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (00479FB0) --------------------------------------------------------
void __thiscall PARTY::PARTY(PARTY *this)
{
  this->m_dwPartyID = 0;
  this->m_dwLeaderID = 0;
  this->m_cMemberNum = 0;
  this->MemberCID[0] = 0;
  this->MemberCID[1] = 0;
  this->MemberCID[2] = 0;
  this->MemberCID[3] = 0;
  this->MemberCID[4] = 0;
  this->MemberCID[5] = 0;
  this->MemberCID[6] = 0;
  this->MemberCID[7] = 0;
  this->MemberCID[8] = 0;
  this->MemberCID[9] = 0;
  this->ServerID[0] = 0;
  this->ServerID[1] = 0;
  this->ServerID[2] = 0;
  this->ServerID[3] = 0;
  this->ServerID[4] = 0;
  this->ServerID[5] = 0;
  this->ServerID[6] = 0;
  this->ServerID[7] = 0;
  this->ServerID[8] = 0;
  this->ServerID[9] = 0;
}

//----- (0047A040) --------------------------------------------------------
void __thiscall PARTY_EX::PARTY_EX(PARTY_EX *this)
{
  PARTY::PARTY(this);
  *(_DWORD *)this->m_wClass = 0;
  *(_DWORD *)&this->m_wClass[2] = 0;
  *(_DWORD *)&this->m_wClass[4] = 0;
  *(_DWORD *)&this->m_wClass[6] = 0;
  *(_DWORD *)&this->m_wClass[8] = 0;
  *(_DWORD *)this->m_cLevel = 0;
  *(_DWORD *)&this->m_cLevel[4] = 0;
  *(_WORD *)&this->m_cLevel[8] = 0;
  *(_DWORD *)this->m_bAutoRouting = 0;
  *(_DWORD *)&this->m_bAutoRouting[4] = 0;
  *(_WORD *)&this->m_bAutoRouting[8] = 0;
  this->m_Position[0].fPointX = 0.0;
  this->m_Position[0].fPointY = 0.0;
  this->m_Position[0].fPointZ = 0.0;
  this->m_Position[1].fPointX = 0.0;
  this->m_Position[1].fPointY = 0.0;
  this->m_Position[1].fPointZ = 0.0;
  this->m_Position[2].fPointX = 0.0;
  this->m_Position[2].fPointY = 0.0;
  this->m_Position[2].fPointZ = 0.0;
  this->m_Position[3].fPointX = 0.0;
  this->m_Position[3].fPointY = 0.0;
  this->m_Position[3].fPointZ = 0.0;
  this->m_Position[4].fPointX = 0.0;
  this->m_Position[4].fPointY = 0.0;
  this->m_Position[4].fPointZ = 0.0;
  this->m_Position[5].fPointX = 0.0;
  this->m_Position[5].fPointY = 0.0;
  this->m_Position[5].fPointZ = 0.0;
  this->m_Position[6].fPointX = 0.0;
  this->m_Position[6].fPointY = 0.0;
  this->m_Position[6].fPointZ = 0.0;
  this->m_Position[7].fPointX = 0.0;
  this->m_Position[7].fPointY = 0.0;
  this->m_Position[7].fPointZ = 0.0;
  this->m_Position[8].fPointX = 0.0;
  this->m_Position[8].fPointY = 0.0;
  this->m_Position[8].fPointZ = 0.0;
  this->m_Position[9].fPointX = 0.0;
  this->m_Position[9].fPointY = 0.0;
  this->m_Position[9].fPointZ = 0.0;
}

//----- (0047A150) --------------------------------------------------------
void __thiscall PARTY_EX::PARTY_EX(PARTY_EX *this, PARTY party)
{
  PARTY_EX v3; // [esp+7h] [ebp-199h] BYREF

  PARTY::PARTY(this);
  this->PARTY = party;
  PARTY_EX::PARTY_EX(&v3);
}

//----- (0047A440) --------------------------------------------------------
void __thiscall CParty::~CParty(CParty *this)
{
  this->__vftable = (CParty_vtbl *)&CParty::`vftable';
  CPartyMgr::DeleteFindMemberList(CSingleton<CPartyMgr>::ms_pSingleton, this->m_Party.m_dwPartyID);
  CPartySpellMgr::~CPartySpellMgr(&this->m_PartySpellMgr);
}
// 4EF170: using guessed type void *CParty::`vftable';

//----- (0047A4A0) --------------------------------------------------------
int __thiscall CMonsterParty::Attack(
        CMonsterParty *this,
        AtType attackType,
        CAggresiveCreature **pDefenders,
        unsigned __int8 *cDefenserJudges,
        CAggresiveCreature *lpOffencer,
        float fDistance,
        unsigned __int8 cTargetType)
{
  unsigned __int8 v7; // bl
  int v8; // ebp
  CAggresiveCreature *v9; // esi
  double v10; // st6
  double v11; // st5
  double v12; // st3
  int v13; // eax
  char cDefenderNum; // [esp+Ch] [ebp-Ch]
  CMonster **i; // [esp+10h] [ebp-8h]

  v7 = 0;
  v8 = 0;
  cDefenderNum = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    for ( i = this->m_pMemberPointer; ; ++i )
    {
      v9 = *i;
      if ( *i )
      {
        v12 = (*pDefenders)->m_CurrentPos.m_fPointZ - v9->m_CurrentPos.m_fPointZ;
        v11 = (*pDefenders)->m_CurrentPos.m_fPointX - v9->m_CurrentPos.m_fPointX;
        v10 = (*pDefenders)->m_CurrentPos.m_fPointY - v9->m_CurrentPos.m_fPointY;
        if ( (double)(unsigned int)(unsigned __int64)sqrt(v11 * v11 + v10 * v10 + v12 * v12) < fDistance )
        {
          v13 = v7++;
          pDefenders[v13] = v9;
          if ( v7 > 0xAu )
            break;
        }
      }
      if ( ++v8 >= this->m_Party.m_cMemberNum )
      {
        cDefenderNum = v7;
        return ((int (__thiscall *)(_DWORD, _DWORD, _BYTE, _DWORD, _DWORD))lpOffencer->Attack)(
                 lpOffencer,
                 attackType,
                 cDefenderNum,
                 pDefenders,
                 cDefenserJudges);
      }
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMonsterParty::Attack",
      FileName,
      370,
      aPid0x08x_7,
      this->m_Party.m_dwPartyID,
      v7);
    cDefenderNum = 10;
  }
  return ((int (__thiscall *)(_DWORD, _DWORD, _BYTE, _DWORD, _DWORD))lpOffencer->Attack)(
           lpOffencer,
           attackType,
           cDefenderNum,
           pDefenders,
           cDefenserJudges);
}

//----- (0047A5B0) --------------------------------------------------------
void __thiscall CCharacterParty::~CCharacterParty(CCharacterParty *this)
{
  this->__vftable = (CCharacterParty_vtbl *)&CCharacterParty::`vftable';
  CParty::~CParty(this);
}
// 4EF654: using guessed type void *CCharacterParty::`vftable';

//----- (0047A5C0) --------------------------------------------------------
int __thiscall CCharacterParty::GetNearMemberList(
        CCharacterParty *this,
        CCell *pCell,
        bool bAutoRouting,
        CCharacter **aryNearCharacterList,
        unsigned __int8 *cHighestLevel)
{
  int v6; // ebx
  int v7; // ebp
  unsigned __int8 *v8; // edi
  CCharacter **m_pMemberPointer; // esi
  CCharacter *v10; // eax
  CCharacter *v11; // eax
  unsigned __int8 v12; // cl
  unsigned __int8 **p_cHighestLevel; // eax
  CCharacterParty *v14; // [esp+0h] [ebp-4h]

  v14 = this;
  if ( !pCell )
    return 0;
  v6 = 0;
  v7 = 0;
  if ( this->m_Party.m_cMemberNum )
  {
    v8 = cHighestLevel;
    m_pMemberPointer = this->m_pMemberPointer;
    do
    {
      v10 = *m_pMemberPointer;
      if ( *m_pMemberPointer && v10->m_CreatureStatus.m_nNowHP && (!bAutoRouting || this->m_bAutoRouting[v7]) )
      {
        if ( CCell::IsNearCell(pCell, v10->m_CellPos.m_lpCell) )
        {
          v11 = *m_pMemberPointer;
          aryNearCharacterList[v6] = *m_pMemberPointer;
          v12 = *v8;
          LOBYTE(cHighestLevel) = v11->m_CreatureStatus.m_nLevel;
          p_cHighestLevel = &cHighestLevel;
          if ( v12 >= (unsigned __int8)cHighestLevel )
            p_cHighestLevel = (unsigned __int8 **)v8;
          *v8 = *(_BYTE *)p_cHighestLevel;
          ++v6;
        }
        this = v14;
      }
      ++v7;
      ++m_pMemberPointer;
    }
    while ( v7 < this->m_Party.m_cMemberNum );
  }
  return v6;
}

