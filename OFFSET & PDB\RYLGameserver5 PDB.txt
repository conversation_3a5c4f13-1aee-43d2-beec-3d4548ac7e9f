//----- (00411B40) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v17; // esi
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  v17 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v17->mtx);
  _Erasednode->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v17->p.first;
  v17->p.first = _Erasednode;
  LeaveCriticalSection(&v17->mtx);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v19 = result;
  result->_Ptr = _Where._Ptr;
  return v19;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00411E10) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rarg,
        const std::pair<unsigned short const ,unsigned short> *_Val,
        const char *_Carg)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v6; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  char v9; // dl
  exception pExceptionObject; // [esp+8h] [ebp-Ch] BYREF

  v6 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v6->mtx);
  first = (void **)v6->p.first;
  p_p = &v6->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v6->mtx);
  if ( !first )
  {
    _Carg = "bad allocation";
    exception::exception(&pExceptionObject, &_Carg);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = _Larg;
  first[2] = _Rarg;
  v9 = (char)_Carg;
  first[1] = _Parg;
  first[3] = (void *)*_Val;
  *((_BYTE *)first + 16) = v9;
  *((_BYTE *)first + 17) = 0;
  return (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00411EB0) --------------------------------------------------------
std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Next,
        std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Prev,
        CCharacter *const *_Val)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v4; // esi
  void **first; // eax
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  _DWORD *v7; // edi
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v8; // edx
  CCharacter *const *v9; // ecx
  exception pExceptionObject; // [esp+8h] [ebp-Ch] BYREF

  v4 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v4->mtx);
  first = (void **)v4->p.first;
  p_p = &v4->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  v7 = first;
  LeaveCriticalSection(&v4->mtx);
  if ( !v7 )
  {
    _Next = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)"bad allocation";
    exception::exception(&pExceptionObject, (const char **)&_Next);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  v8 = _Next;
  v9 = _Val;
  v7[1] = _Prev;
  *v7 = v8;
  v7[2] = *v9;
  return (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v7;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00411F40) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  char *what; // [esp+8h] [ebp-10h] BYREF
  exception pExceptionObject; // [esp+Ch] [ebp-Ch] BYREF

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  first = (void **)v1->p.first;
  p_p = &v1->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v1->mtx);
  if ( !first )
  {
    what = "bad allocation";
    exception::exception(&pExceptionObject, (const char **)&what);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = 0;
  if ( first != (void **)-4 )
    first[1] = 0;
  if ( first != (void **)-8 )
    first[2] = 0;
  *((_BYTE *)first + 16) = 1;
  *((_BYTE *)first + 17) = 0;
  return (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00411FE0) --------------------------------------------------------
char __thiscall CCreatureManager::CancelLogout(CCreatureManager *this, CCharacter *lpCharacter)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ecx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax

  Myhead = this->m_LogoutWaitList._Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next == Myhead )
    return 0;
  while ( Next->_Myval != lpCharacter )
  {
    Next = Next->_Next;
    if ( Next == Myhead )
      return 0;
  }
  if ( Next == Myhead )
    return 0;
  Next->_Myval->m_nLogoutCount = 5;
  if ( Next != this->m_LogoutWaitList._Myhead )
  {
    Next->_Prev->_Next = Next->_Next;
    Next->_Next->_Prev = Next->_Prev;
    boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&Next->_Next);
    --this->m_LogoutWaitList._Mysize;
  }
  return 1;
}

//----- (00412040) --------------------------------------------------------
void __thiscall CCreatureManager::ProcessSummonMonsterDead(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // esi
  CMonster *second; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+Ch] [ebp-8h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+10h] [ebp-4h] BYREF

  Myhead = this->m_MonsterMap._Myhead;
  Left = Myhead->_Left;
  pos._Ptr = Myhead->_Left;
  if ( pos._Ptr != Myhead )
  {
    do
    {
      second = Left->_Myval.second;
      if ( CMonster::IsDeadSummonMonster(second) )
      {
        Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase((std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_MonsterMap, &result, (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Left)->_Ptr;
        pos._Ptr = Left;
        if ( second )
          ((void (__thiscall *)(CMonster *, int))second->~CAggresiveCreature)(second, 1);
      }
      else
      {
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
        Left = pos._Ptr;
      }
    }
    while ( Left != Myhead );
  }
}

//----- (004120B0) --------------------------------------------------------
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ecx
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v7; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // esi

  Ptr = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Next && _Last._Ptr == Myhead )
  {
    std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(this);
    v7 = result;
    result->_Ptr = _Last._Ptr;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        Next = Ptr->_Next;
        if ( Ptr != this->_Myhead )
        {
          Ptr->_Prev->_Next = Next;
          Ptr->_Next->_Prev = Ptr->_Prev;
          boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&Ptr->_Next);
          --this->_Mysize;
        }
        Ptr = Next;
      }
      while ( Next != _Last._Ptr );
    }
    v7 = result;
    result->_Ptr = _Last._Ptr;
  }
  return v7;
}

//----- (00412120) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004121E0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,CNPC *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CNPC *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0xFFFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)this->_Myhead, (const std::pair<unsigned long const ,CTempCharacter *> *)_Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CNPC *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00412390) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Wherenode,
        const std::pair<unsigned short const ,unsigned short> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned short const ,unsigned short> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0xFFFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned short const ,unsigned short> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
            (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
          (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00412540) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *result,
        const std::pair<unsigned long const ,CNPC *> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  unsigned int first; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *v7; // eax
  bool _Addleft; // [esp+8h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)this->_Myhead;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Parent;
      _Addleft = first < Parent->_Myval.first;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_Val,
          _Addleft,
          Myhead,
          _Val)->_Ptr;
  v7 = result;
  result->first._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)Ptr;
  result->second = 1;
  return v7;
}

//----- (004125B0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00412670) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00412730) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004127F0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (004128B0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00412970) --------------------------------------------------------
void __thiscall CCreatureManager::DestoryCharacterList(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  CCharacter *second; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Parent; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v7; // eax
  std::multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_CharacterNameMap; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v9; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v11; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+10h] [ebp-4h] BYREF

  Myhead = this->m_CharacterMap._Myhead;
  Left = Myhead->_Left;
  for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
  {
    second = Left->_Myval.second;
    ((void (__thiscall *)(CCharacter *, _DWORD))second->~CAggresiveCreature)(second, 0);
    second->__vftable = (CCharacter_vtbl *)this->m_CharacterPool.first;
    this->m_CharacterPool.first = second;
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
  }
  Parent = this->m_CharacterMap._Myhead->_Parent;
  for ( i = Parent; !i->_Isnil; Parent = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      &this->m_CharacterMap,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&Parent->_Left);
  }
  this->m_CharacterMap._Myhead->_Parent = this->m_CharacterMap._Myhead;
  v7 = this->m_CharacterMap._Myhead;
  this->m_CharacterMap._Mysize = 0;
  v7->_Left = v7;
  this->m_CharacterMap._Myhead->_Right = this->m_CharacterMap._Myhead;
  p_m_CharacterNameMap = &this->m_CharacterNameMap;
  v9 = this->m_CharacterNameMap._Myhead->_Parent;
  for ( j = v9; !j->_Isnil; v9 = j )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      p_m_CharacterNameMap,
      j->_Right);
    j = j->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v9->_Left);
  }
  p_m_CharacterNameMap->_Myhead->_Parent = p_m_CharacterNameMap->_Myhead;
  v11 = p_m_CharacterNameMap->_Myhead;
  p_m_CharacterNameMap->_Mysize = 0;
  v11->_Left = v11;
  p_m_CharacterNameMap->_Myhead->_Right = p_m_CharacterNameMap->_Myhead;
}

//----- (00412A40) --------------------------------------------------------
void __thiscall CCreatureManager::DestoryMonsterList(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v3; // eax

  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnLeaveParty>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead,
    0);
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_MonsterMap._Myhead,
    0);
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_MonsterMap,
    this->m_MonsterMap._Myhead->_Parent);
  this->m_MonsterMap._Myhead->_Parent = this->m_MonsterMap._Myhead;
  Myhead = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_MonsterMap._Myhead->_Right = this->m_MonsterMap._Myhead;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_AdminMonsterUIDMap,
    this->m_AdminMonsterUIDMap._Myhead->_Parent);
  this->m_AdminMonsterUIDMap._Myhead->_Parent = this->m_AdminMonsterUIDMap._Myhead;
  v3 = this->m_AdminMonsterUIDMap._Myhead;
  this->m_AdminMonsterUIDMap._Mysize = 0;
  v3->_Left = v3;
  this->m_AdminMonsterUIDMap._Myhead->_Right = this->m_AdminMonsterUIDMap._Myhead;
}

//----- (00412AD0) --------------------------------------------------------
void __thiscall CCreatureManager::DestoryNPCList(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax

  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_NPCMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_NPCMap._Myhead,
    0);
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_NPCMap,
    this->m_NPCMap._Myhead->_Parent);
  this->m_NPCMap._Myhead->_Parent = this->m_NPCMap._Myhead;
  Myhead = this->m_NPCMap._Myhead;
  this->m_NPCMap._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_NPCMap._Myhead->_Right = this->m_NPCMap._Myhead;
}

//----- (00412B20) --------------------------------------------------------
void __thiscall CCreatureManager::DestroyAll(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v3; // eax

  CCreatureManager::DestoryCharacterList(this);
  CCreatureManager::DestoryMonsterList(this);
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_SiegeObjectMap,
    this->m_SiegeObjectMap._Myhead->_Parent);
  this->m_SiegeObjectMap._Myhead->_Parent = this->m_SiegeObjectMap._Myhead;
  Myhead = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_SiegeObjectMap._Myhead->_Right = this->m_SiegeObjectMap._Myhead;
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_NPCMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_NPCMap._Myhead,
    0);
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
    &this->m_NPCMap,
    this->m_NPCMap._Myhead->_Parent);
  this->m_NPCMap._Myhead->_Parent = this->m_NPCMap._Myhead;
  v3 = this->m_NPCMap._Myhead;
  this->m_NPCMap._Mysize = 0;
  v3->_Left = v3;
  this->m_NPCMap._Myhead->_Right = this->m_NPCMap._Myhead;
}

//----- (00412BA0) --------------------------------------------------------
char __thiscall CCreatureManager::ProcessCharacterLogout(CCreatureManager *this)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // edi
  std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > v4; // [esp-1Ch] [ebp-28h]
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator LogoutItr; // [esp+4h] [ebp-8h] BYREF

  if ( !this->m_LogoutWaitList._Mysize )
    return 0;
  std::partition<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::mem_fun_t<bool,CCharacter>>(
    &LogoutItr,
    (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_LogoutWaitList._Myhead->_Next,
    (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_LogoutWaitList._Myhead,
    (std::mem_fun_t<bool,CCharacter>)CCharacter::StillAlive);
  Ptr = LogoutItr._Ptr;
  v4.value = LOGOUT;
  v4.op._Pmemfun = CCharacter::Logout;
  std::for_each<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>>(
    (std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *)&LogoutItr,
    LogoutItr,
    (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_LogoutWaitList._Myhead,
    v4);
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
    &this->m_LogoutWaitList,
    &LogoutItr,
    (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)Ptr,
    (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_LogoutWaitList._Myhead);
  return 1;
}

//----- (00412C10) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *result,
        std::pair<unsigned long const ,CNPC *> *_Val)
{
  const std::pair<unsigned long const ,CNPC *> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int first; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      v7 = first < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (std::pair<unsigned long const ,CNPC *> *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00412CD0) --------------------------------------------------------
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v4; // ebx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator> _Where; // [esp+Ch] [ebp-8h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&_Where,
    _Keyval);
  Ptr = _Where.second._Ptr;
  v4 = _Where.first._Ptr;
  _Keyval = 0;
  std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.first._Ptr,
    (std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator)_Where.second._Ptr,
    (unsigned int *)&_Keyval);
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &_Where.first,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v4,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Ptr);
  return _Keyval;
}

//----- (00412D30) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *result,
        std::pair<unsigned short const ,unsigned short> *_Val)
{
  const std::pair<unsigned short const ,unsigned short> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned __int16 first; // dx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = _Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = Parent;
      v7 = first < Parent->_Myval.first;
      _Addleft = v7;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = (std::pair<unsigned short const ,unsigned short> *)Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec((std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *)&_Val);
    v9 = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (00412DF0) --------------------------------------------------------
void __thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        CCharacter *const *_Val)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edi
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v4; // ebx

  Myhead = this->_Myhead;
  v4 = std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
         this,
         Myhead,
         Myhead->_Prev,
         _Val);
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
    this,
    1u);
  Myhead->_Prev = v4;
  v4->_Prev->_Next = v4;
}

//----- (00412E30) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where,
        std::pair<unsigned short const ,unsigned short> *_Val)
{
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::pair<unsigned short const ,unsigned short> *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  unsigned __int16 first; // bp
  bool v10; // cf
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> v12; // [esp+8h] [ebp-8h] BYREF

  if ( !this->_Mysize )
  {
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
      this,
      result,
      1,
      this->_Myhead,
      _Val);
    return result;
  }
  Myhead = this->_Myhead;
  v7 = _Val;
  if ( _Where._Ptr == Myhead->_Left )
  {
    if ( _Val->first < _Where._Ptr->_Myval.first )
    {
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  if ( _Where._Ptr == Myhead )
  {
    Right = Myhead->_Right;
    if ( Right->_Myval.first < _Val->first )
    {
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
        this,
        result,
        0,
        Right,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  first = _Val->first;
  v10 = _Where._Ptr->_Myval.first < _Val->first;
  if ( _Where._Ptr->_Myval.first > _Val->first )
  {
    _Val = (std::pair<unsigned short const ,unsigned short> *)_Where._Ptr;
    std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec((std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *)&_Val);
    if ( _Val[3].first < first )
    {
      if ( *(_BYTE *)(*(_DWORD *)&_Val[2] + 17) )
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
          this,
          result,
          0,
          (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Val,
          v7);
      else
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
          this,
          result,
          1,
          _Where._Ptr,
          v7);
      return result;
    }
    v10 = _Where._Ptr->_Myval.first < first;
  }
  if ( !v10
    || (_Val = (std::pair<unsigned short const ,unsigned short> *)_Where._Ptr,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *)&_Val),
        _Val != (std::pair<unsigned short const ,unsigned short> *)this->_Myhead)
    && first >= _Val[3].first )
  {
LABEL_23:
    Ptr = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
            this,
            &v12,
            v7)->first._Ptr;
    v5 = result;
    result->_Ptr = Ptr;
    return v5;
  }
  if ( _Where._Ptr->_Right->_Isnil )
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
      this,
      result,
      0,
      _Where._Ptr,
      v7);
  else
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(
      this,
      result,
      1,
      (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Val,
      v7);
  return result;
}

//----- (00412FB0) --------------------------------------------------------
void __thiscall std::map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00413000) --------------------------------------------------------
bool __thiscall CCreatureManager::AddCreature(CCreatureManager *this, CNPC *lpCreature)
{
  CNPC *v2; // ebp
  signed int m_dwCID; // esi
  bool second; // bl
  char v6; // al
  bool v7; // al
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v8; // [esp+10h] [ebp-34h] BYREF
  std::pair<unsigned long const ,CNPC *> _Val; // [esp+14h] [ebp-30h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> result; // [esp+1Ch] [ebp-28h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> v11; // [esp+24h] [ebp-20h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> v12; // [esp+2Ch] [ebp-18h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> v13; // [esp+34h] [ebp-10h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> v14; // [esp+3Ch] [ebp-8h] BYREF

  v2 = lpCreature;
  m_dwCID = lpCreature->m_dwCID;
  second = 0;
  if ( m_dwCID >= 0 )
  {
    if ( (m_dwCID & 0x40000000) != 0 )
      v6 = 1;
    else
      v6 = (m_dwCID & 0x10000000) != 0 ? 5 : 0;
  }
  else
  {
    v6 = 2;
  }
  switch ( v6 )
  {
    case 0:
      _Val.first = lpCreature->m_dwCID;
      _Val.second = lpCreature;
      second = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
                 (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_CharacterMap,
                 &result,
                 &_Val)->second;
      if ( !second )
        goto LABEL_14;
      _Val.first = Math::HashFunc::sdbmHash((const unsigned __int8 *)&v2[14].m_MotionInfo.m_dwFrame);
      _Val.second = v2;
      std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(
        &this->m_CharacterNameMap,
        &v11,
        &_Val);
      ++this->m_wCharacterNum[HIBYTE(v2[14].m_nJob)];
      v7 = 1;
      break;
    case 1:
      _Val.first = lpCreature->m_dwCID;
      _Val.second = lpCreature;
      v7 = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
             &this->m_NPCMap,
             &v13,
             &_Val)->second;
      break;
    case 2:
      _Val.first = lpCreature->m_dwCID;
      _Val.second = lpCreature;
      second = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
                 (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_MonsterMap,
                 &v12,
                 &_Val)->second;
      lpCreature = (CNPC *)m_dwCID;
      if ( !second
        || this->m_AdminMonsterUIDMap._Myhead != std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::find(
                                                   &this->m_AdminMonsterUIDMap,
                                                   &v8,
                                                   (const unsigned __int16 *)&lpCreature)->_Ptr )
      {
        goto LABEL_14;
      }
      LOWORD(lpCreature) = m_dwCID;
      HIWORD(lpCreature) = -28674;
      std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
        &this->m_AdminMonsterUIDMap,
        (std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *)&v11,
        (std::pair<unsigned short const ,unsigned short> *)&lpCreature);
      v7 = second;
      break;
    case 5:
      _Val.first = lpCreature->m_dwCID;
      _Val.second = lpCreature;
      second = std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
                 (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_SiegeObjectMap,
                 &v14,
                 &_Val)->second;
      goto LABEL_14;
    default:
LABEL_14:
      v7 = second;
      break;
  }
  return v7;
}

//----- (00413190) --------------------------------------------------------
bool __thiscall CCreatureManager::DeleteCreature(CCreatureManager *this, signed int dwCID)
{
  signed int v2; // edi
  char v4; // al
  bool v5; // al
  CCharacter *second; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // ebx
  int v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *v9; // ebx
  CMsgProc *v10; // ecx
  CMsgProc *v11; // ecx
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+Ch] [ebp-10h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+10h] [ebp-Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator> findpair; // [esp+14h] [ebp-8h] BYREF

  v2 = dwCID;
  if ( dwCID >= 0 )
  {
    if ( (dwCID & 0x40000000) != 0 )
      v4 = 1;
    else
      v4 = (dwCID & 0x10000000) != 0 ? 5 : 0;
  }
  else
  {
    v4 = 2;
  }
  switch ( v4 )
  {
    case 0:
      result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)&this->m_CharacterMap;
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CharacterMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
        (const unsigned int *)&dwCID);
      if ( pos._Ptr == this->m_CharacterMap._Myhead )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCreatureManager::DeleteCreature",
          aDWorkRylSource_10,
          239,
          aCid0x08x_229,
          v2);
        goto LABEL_9;
      }
      second = pos._Ptr->_Myval.second;
      dwCID = Math::HashFunc::sdbmHash((const unsigned __int8 *)second->m_DBData.m_Info.Name);
      std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
        (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_CharacterNameMap,
        (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&findpair,
        (const unsigned int *)&dwCID);
      Ptr = findpair.first._Ptr;
      if ( findpair.first._Ptr != findpair.second._Ptr )
      {
        while ( 1 )
        {
          strncmp(
            (unsigned __int8 *)Ptr->_Myval.second->m_DBData.m_Info.Name,
            (unsigned __int8 *)second->m_DBData.m_Info.Name,
            0x10u);
          if ( !v8 )
            break;
          std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&findpair);
          Ptr = findpair.first._Ptr;
          if ( findpair.first._Ptr == findpair.second._Ptr )
            goto LABEL_15;
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_CharacterNameMap,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&dwCID,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Ptr);
      }
LABEL_15:
      --this->m_wCharacterNum[(unsigned __int8)second->m_DBData.m_Info.Nationality];
      CCreatureManager::DeleteCharacter(this, second);
      std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)result._Ptr,
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&dwCID,
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)pos._Ptr);
      v5 = 1;
      break;
    case 1:
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
        (const unsigned int *)&dwCID);
      if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_NPCMap._Myhead )
        goto LABEL_9;
      v11 = result._Ptr->_Myval.second;
      if ( v11 )
        ((void (__thiscall *)(CMsgProc *, int))v11->~CMsgProc)(v11, 1);
      std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        &this->m_NPCMap,
        (const unsigned int *)&dwCID);
      v5 = 1;
      break;
    case 2:
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MonsterMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
        (const unsigned int *)&dwCID);
      v9 = result._Ptr;
      if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_MonsterMap._Myhead )
        goto LABEL_9;
      v10 = result._Ptr->_Myval.second;
      if ( v10 )
        ((void (__thiscall *)(CMsgProc *, int))v10->~CMsgProc)(v10, 1);
      std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_MonsterMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&dwCID,
        (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v9);
      v5 = 1;
      break;
    case 5:
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_SiegeObjectMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
        (const unsigned int *)&dwCID);
      if ( result._Ptr != (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_SiegeObjectMap._Myhead )
      {
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *)&this->m_SiegeObjectMap,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&dwCID,
          (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)result._Ptr);
        goto LABEL_26;
      }
LABEL_9:
      v5 = 0;
      break;
    default:
LABEL_26:
      v5 = 1;
      break;
  }
  return v5;
}
// 413268: variable 'v8' is possibly undefined

//----- (004133B0) --------------------------------------------------------
void __thiscall CCreatureManager::EnqueueLogout(CCreatureManager *this, CCharacter *lpCharacter)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edi
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_LogoutWaitList; // esi
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v4; // ebx

  Myhead = this->m_LogoutWaitList._Myhead;
  p_m_LogoutWaitList = &this->m_LogoutWaitList;
  v4 = std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
         &this->m_LogoutWaitList,
         Myhead,
         Myhead->_Prev,
         &lpCharacter);
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
    p_m_LogoutWaitList,
    1u);
  Myhead->_Prev = v4;
  v4->_Prev->_Next = v4;
}

//----- (004133F0) --------------------------------------------------------
unsigned __int16 *__thiscall std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](
        std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        const unsigned __int16 *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+8h] [ebp-4h] BYREF

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead != this->_Myhead && *_Keyval >= Myhead->_Myval.first )
    return &Myhead->_Myval.second;
  _Keyval = (const unsigned __int16 *)*_Keyval;
  return &std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
            this,
            &result,
            (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead,
            (std::pair<unsigned short const ,unsigned short> *)&_Keyval)->_Ptr->_Myval.second;
}

//----- (00413460) --------------------------------------------------------
void __thiscall std::map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004134B0) --------------------------------------------------------
void __thiscall std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00413500) --------------------------------------------------------
void __thiscall std::map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (00413550) --------------------------------------------------------
void __thiscall std::multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004135A0) --------------------------------------------------------
void __thiscall std::map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->_Myhead);
  Myhead = this->_Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004135F0) --------------------------------------------------------
void __thiscall CCreatureManager::~CCreatureManager(CCreatureManager *this)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ebp
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v3; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ecx
  _RTL_CRITICAL_SECTION *v5; // eax
  _RTL_CRITICAL_SECTION *v6; // eax
  _RTL_CRITICAL_SECTION *v7; // eax
  _RTL_CRITICAL_SECTION *v8; // eax
  _RTL_CRITICAL_SECTION *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v10; // [esp-8h] [ebp-30h]
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator v11; // [esp-8h] [ebp-30h]
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v12; // [esp-8h] [ebp-30h]
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v13; // [esp-8h] [ebp-30h]
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v14; // [esp-8h] [ebp-30h]
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v15; // [esp-8h] [ebp-30h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v16; // [esp-4h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v17; // [esp-4h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v18; // [esp-4h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v19; // [esp-4h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v20; // [esp-4h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v21; // [esp-4h] [ebp-2Ch]
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *lpCriticalSection; // [esp+10h] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *lpCriticalSectiona; // [esp+10h] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *lpCriticalSectionb; // [esp+10h] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *lpCriticalSectionc; // [esp+10h] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *lpCriticalSectiond; // [esp+10h] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *lpCriticalSectione; // [esp+10h] [ebp-18h]
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator result; // [esp+14h] [ebp-14h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator v29; // [esp+18h] [ebp-10h] BYREF
  int v30; // [esp+24h] [ebp-4h]

  v29._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)this;
  v30 = 7;
  CCreatureManager::DestroyAll(this);
  `eh vector destructor iterator'(
    (char *)this->m_lstRespawn,
    0xCu,
    6,
    (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
  boost::pool<boost::default_user_allocator_new_delete>::purge_memory(&this->m_CharacterPool);
  std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(&this->m_LogoutWaitList);
  Myhead = this->m_LogoutWaitList._Myhead;
  v3 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v3->mtx);
  Myhead->_Next = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v3->p.first;
  v3->p.first = Myhead;
  LeaveCriticalSection(&v3->mtx);
  this->m_LogoutWaitList._Myhead = 0;
  v16 = this->m_SiegeObjectMap._Myhead;
  v10._Ptr = v16->_Left;
  LOBYTE(v30) = 4;
  std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_SiegeObjectMap,
    &result,
    v10,
    (std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v16);
  result._Ptr = this->m_SiegeObjectMap._Myhead;
  lpCriticalSection = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&lpCriticalSection->mtx);
  Ptr = result._Ptr;
  result._Ptr->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)lpCriticalSection->p.first;
  lpCriticalSection->p.first = Ptr;
  LeaveCriticalSection(&lpCriticalSection->mtx);
  this->m_SiegeObjectMap._Myhead = 0;
  this->m_SiegeObjectMap._Mysize = 0;
  v17 = this->m_CharacterNameMap._Myhead;
  v11._Ptr = v17->_Left;
  LOBYTE(v30) = 3;
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(
    &this->m_CharacterNameMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *)&result,
    v11,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator)v17);
  lpCriticalSectiona = this->m_CharacterNameMap._Myhead;
  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection((LPCRITICAL_SECTION)result._Ptr);
  v5 = (_RTL_CRITICAL_SECTION *)result._Ptr;
  lpCriticalSectiona->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)result._Ptr[1]._Left;
  v5[1].DebugInfo = (_RTL_CRITICAL_SECTION_DEBUG *)lpCriticalSectiona;
  LeaveCriticalSection(v5);
  this->m_CharacterNameMap._Myhead = 0;
  this->m_CharacterNameMap._Mysize = 0;
  v18 = this->m_CharacterMap._Myhead;
  v12._Ptr = v18->_Left;
  LOBYTE(v30) = 2;
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_CharacterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&result,
    v12,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v18);
  lpCriticalSectionb = this->m_CharacterMap._Myhead;
  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection((LPCRITICAL_SECTION)result._Ptr);
  v6 = (_RTL_CRITICAL_SECTION *)result._Ptr;
  lpCriticalSectionb->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)result._Ptr[1]._Left;
  v6[1].DebugInfo = (_RTL_CRITICAL_SECTION_DEBUG *)lpCriticalSectionb;
  LeaveCriticalSection(v6);
  this->m_CharacterMap._Myhead = 0;
  this->m_CharacterMap._Mysize = 0;
  v19 = this->m_AdminMonsterUIDMap._Myhead;
  v13._Ptr = v19->_Left;
  LOBYTE(v30) = 1;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_AdminMonsterUIDMap,
    (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&result,
    v13,
    (std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v19);
  lpCriticalSectionc = this->m_AdminMonsterUIDMap._Myhead;
  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection((LPCRITICAL_SECTION)result._Ptr);
  v7 = (_RTL_CRITICAL_SECTION *)result._Ptr;
  lpCriticalSectionc->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)result._Ptr[1]._Left;
  v7[1].DebugInfo = (_RTL_CRITICAL_SECTION_DEBUG *)lpCriticalSectionc;
  LeaveCriticalSection(v7);
  this->m_AdminMonsterUIDMap._Myhead = 0;
  this->m_AdminMonsterUIDMap._Mysize = 0;
  v20 = this->m_MonsterMap._Myhead;
  v14._Ptr = v20->_Left;
  LOBYTE(v30) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_MonsterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&result,
    v14,
    (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v20);
  lpCriticalSectiond = this->m_MonsterMap._Myhead;
  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection((LPCRITICAL_SECTION)result._Ptr);
  v8 = (_RTL_CRITICAL_SECTION *)result._Ptr;
  lpCriticalSectiond->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)result._Ptr[1]._Left;
  v8[1].DebugInfo = (_RTL_CRITICAL_SECTION_DEBUG *)lpCriticalSectiond;
  LeaveCriticalSection(v8);
  this->m_MonsterMap._Myhead = 0;
  this->m_MonsterMap._Mysize = 0;
  v21 = this->m_NPCMap._Myhead;
  v15._Ptr = v21->_Left;
  v30 = -1;
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
    &this->m_NPCMap,
    &v29,
    v15,
    (std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v21);
  lpCriticalSectione = this->m_NPCMap._Myhead;
  result._Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection((LPCRITICAL_SECTION)result._Ptr);
  v9 = (_RTL_CRITICAL_SECTION *)result._Ptr;
  lpCriticalSectione->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)result._Ptr[1]._Left;
  v9[1].DebugInfo = (_RTL_CRITICAL_SECTION_DEBUG *)lpCriticalSectione;
  LeaveCriticalSection(v9);
  this->m_NPCMap._Myhead = 0;
  this->m_NPCMap._Mysize = 0;
}

//----- (00413830) --------------------------------------------------------
unsigned __int16 __thiscall CCreatureManager::GetAvailableMonsterUID(CCreatureManager *this, unsigned __int16 wKindID)
{
  std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *p_m_AdminMonsterUIDMap; // esi
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v4; // eax
  unsigned __int16 v5; // bx
  unsigned __int16 *v7; // eax
  std::pair<unsigned short const ,unsigned short> _Val; // [esp+Ch] [ebp-Ch] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> result; // [esp+10h] [ebp-8h] BYREF

  p_m_AdminMonsterUIDMap = &this->m_AdminMonsterUIDMap;
  v4 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::find(
         &this->m_AdminMonsterUIDMap,
         &result.first,
         &wKindID);
  v5 = wKindID;
  if ( this->m_AdminMonsterUIDMap._Myhead == v4->_Ptr )
  {
    _Val.first = wKindID;
    _Val.second = -28674;
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(
      p_m_AdminMonsterUIDMap,
      &result,
      &_Val);
  }
  _Val.first = v5;
  _Val.second = *std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](
                   p_m_AdminMonsterUIDMap,
                   &wKindID);
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MonsterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
    (const unsigned int *)&_Val);
  if ( result.first._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)this->m_MonsterMap._Myhead
    || !*(_DWORD *)&result.first._Ptr->_Color )
  {
    v7 = std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](
           p_m_AdminMonsterUIDMap,
           &wKindID);
    return (*v7)--;
  }
  else if ( *std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](
               p_m_AdminMonsterUIDMap,
               &wKindID) == 0x8FFE )
  {
    return -28673;
  }
  else
  {
    *std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](
       p_m_AdminMonsterUIDMap,
       &wKindID) = -28675;
    return -28674;
  }
}

//----- (00413920) --------------------------------------------------------
void __thiscall CCreatureManager::CCreatureManager(CCreatureManager *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v3; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v4; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v6; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v7; // eax

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)this);
  this->m_NPCMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_NPCMap._Myhead->_Parent = this->m_NPCMap._Myhead;
  this->m_NPCMap._Myhead->_Left = this->m_NPCMap._Myhead;
  this->m_NPCMap._Myhead->_Right = this->m_NPCMap._Myhead;
  this->m_NPCMap._Mysize = 0;
  v3 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_MonsterMap);
  this->m_MonsterMap._Myhead = v3;
  v3->_Isnil = 1;
  this->m_MonsterMap._Myhead->_Parent = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Myhead->_Left = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Myhead->_Right = this->m_MonsterMap._Myhead;
  this->m_MonsterMap._Mysize = 0;
  v4 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(&this->m_AdminMonsterUIDMap);
  this->m_AdminMonsterUIDMap._Myhead = v4;
  v4->_Isnil = 1;
  this->m_AdminMonsterUIDMap._Myhead->_Parent = this->m_AdminMonsterUIDMap._Myhead;
  this->m_AdminMonsterUIDMap._Myhead->_Left = this->m_AdminMonsterUIDMap._Myhead;
  this->m_AdminMonsterUIDMap._Myhead->_Right = this->m_AdminMonsterUIDMap._Myhead;
  this->m_AdminMonsterUIDMap._Mysize = 0;
  v5 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_CharacterMap);
  this->m_CharacterMap._Myhead = v5;
  v5->_Isnil = 1;
  this->m_CharacterMap._Myhead->_Parent = this->m_CharacterMap._Myhead;
  this->m_CharacterMap._Myhead->_Left = this->m_CharacterMap._Myhead;
  this->m_CharacterMap._Myhead->_Right = this->m_CharacterMap._Myhead;
  this->m_CharacterMap._Mysize = 0;
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_CharacterNameMap);
  this->m_CharacterNameMap._Myhead = v6;
  v6->_Isnil = 1;
  this->m_CharacterNameMap._Myhead->_Parent = this->m_CharacterNameMap._Myhead;
  this->m_CharacterNameMap._Myhead->_Left = this->m_CharacterNameMap._Myhead;
  this->m_CharacterNameMap._Myhead->_Right = this->m_CharacterNameMap._Myhead;
  this->m_CharacterNameMap._Mysize = 0;
  v7 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_SiegeObjectMap);
  this->m_SiegeObjectMap._Myhead = v7;
  v7->_Isnil = 1;
  this->m_SiegeObjectMap._Myhead->_Parent = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Myhead->_Left = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Myhead->_Right = this->m_SiegeObjectMap._Myhead;
  this->m_SiegeObjectMap._Mysize = 0;
  this->m_LogoutWaitList._Myhead = std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(&this->m_LogoutWaitList);
  this->m_LogoutWaitList._Mysize = 0;
  this->m_CharacterPool.first = 0;
  this->m_CharacterPool.list.ptr = 0;
  this->m_CharacterPool.list.sz = 0;
  this->m_CharacterPool.requested_size = 1536;
  this->m_CharacterPool.next_size = 32;
  EliteBonus::EliteBonusData::EliteBonusData(&this->m_EliteBonus);
  this->m_bAutoBalance = 1;
  this->m_bRest = 0;
  `eh vector constructor iterator'(
    (char *)this->m_lstRespawn,
    0xCu,
    6,
    (void (__thiscall *)(void *))std::list<CCreatureManager::BattleGroundRespawnInfo>::list<CCreatureManager::BattleGroundRespawnInfo>,
    (void (__thiscall *)(void *))std::list<LogBuffer *>::~list<LogBuffer *>);
  this->m_RespawnPoint[0].m_fPointX = 0.0;
  this->m_RespawnPoint[0].m_fPointY = 0.0;
  this->m_RespawnPoint[0].m_fPointZ = 0.0;
  this->m_RespawnPoint[1].m_fPointX = 0.0;
  this->m_RespawnPoint[1].m_fPointY = 0.0;
  this->m_RespawnPoint[1].m_fPointZ = 0.0;
  this->m_RespawnPoint[2].m_fPointX = 0.0;
  this->m_RespawnPoint[2].m_fPointY = 0.0;
  this->m_RespawnPoint[2].m_fPointZ = 0.0;
  this->m_RespawnPoint[3].m_fPointX = 0.0;
  this->m_RespawnPoint[3].m_fPointY = 0.0;
  this->m_RespawnPoint[3].m_fPointZ = 0.0;
  this->m_RespawnPoint[4].m_fPointX = 0.0;
  this->m_RespawnPoint[4].m_fPointY = 0.0;
  this->m_RespawnPoint[4].m_fPointZ = 0.0;
  this->m_RespawnPoint[5].m_fPointX = 0.0;
  this->m_RespawnPoint[5].m_fPointY = 0.0;
  this->m_RespawnPoint[5].m_fPointZ = 0.0;
  this->m_RespawnPoint[0].m_fPointX = 1133.0;
  this->m_RespawnPoint[0].m_fPointY = 57.0;
  this->m_RespawnPoint[0].m_fPointZ = 1429.0;
  this->m_RespawnPoint[1].m_fPointX = 1217.0;
  this->m_RespawnPoint[1].m_fPointY = 48.0;
  this->m_RespawnPoint[1].m_fPointZ = 1827.0;
  this->m_dwLastUpdateTime = 0;
  this->m_RespawnPoint[2].m_fPointX = 1526.0;
  this->m_RespawnPoint[2].m_fPointY = 33.0;
  this->m_RespawnPoint[2].m_fPointZ = 1685.0;
  this->m_RespawnPoint[3].m_fPointX = 1310.0;
  this->m_RespawnPoint[3].m_fPointY = 28.0;
  this->m_RespawnPoint[3].m_fPointZ = 1168.0;
  this->m_RespawnPoint[4].m_fPointX = 1619.0;
  this->m_RespawnPoint[4].m_fPointY = 49.0;
  this->m_RespawnPoint[4].m_fPointZ = 998.0;
  this->m_RespawnPoint[5].m_fPointX = 1724.0;
  this->m_RespawnPoint[5].m_fPointY = 53.0;
  this->m_RespawnPoint[5].m_fPointZ = 1444.0;
  *(_DWORD *)this->m_wCharacterNum = 0;
}

//----- (00413C20) --------------------------------------------------------
CCreatureManager *__cdecl CCreatureManager::GetInstance()
{
  if ( (_S5 & 1) == 0 )
  {
    _S5 |= 1u;
    CCreatureManager::CCreatureManager(&creatureManager);
    atexit(_E6);
  }
  return &creatureManager;
}

//----- (00413C80) --------------------------------------------------------
void __thiscall PktBase::InitPtHead(
        PktBase *this,
        unsigned __int16 Len_In,
        unsigned __int8 Cmd_In,
        unsigned __int16 State_In,
        unsigned __int16 Error_In)
{
  this->m_Cmd = Cmd_In;
  this->m_Len = Len_In;
  this->m_StartBit = -1;
  this->m_CodePage = 0;
  this->m_SrvInfo.SrvState.wSrvState = State_In;
  this->m_SrvInfo.SrvState.wError = Error_In;
}

//----- (00413CB0) --------------------------------------------------------
int __thiscall CSendDataToManageClient::operator()(
        CSendDataToManageClient *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        unsigned int wParam,
        int lParam)
{
  const char *ManageClientWindowName; // eax
  HWND WindowA; // eax
  unsigned int v7; // edx
  tagCOPYDATASTRUCT copyStruct; // [esp+4h] [ebp-Ch] BYREF

  if ( lParam )
  {
    ManageClientWindowName = CServerSetup::GetManageClientWindowName();
    WindowA = FindWindowA(ManageClientWindowName, ManageClientWindowName);
    if ( WindowA )
    {
      v7 = *(_DWORD *)(lParam + 8) - *(_DWORD *)(lParam + 4);
      copyStruct.lpData = *(void **)(lParam + 4);
      copyStruct.dwData = 0;
      copyStruct.cbData = v7;
      SendMessageA(WindowA, 0x4Au, 0, (LPARAM)&copyStruct);
    }
    (*(void (__thiscall **)(_DWORD, int))(**(_DWORD **)(lParam + 44) + 8))(*(_DWORD *)(lParam + 44), lParam);
  }
  return 0;
}

//----- (00413D10) --------------------------------------------------------
int __stdcall CServerWindowFramework::ServerWindowFrameworkProc(HWND__ *hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
  CMsgProcessMgr **PropA; // eax
  CMsgProcessMgr **v5; // esi
  CMsgProcessMgr *v7; // ecx
  CMsgProc *Castle; // eax
  CSysTray *v9; // ebx
  HMENU MenuA; // eax
  HMENU v11; // edi
  HMENU SubMenu; // eax
  HMENU v13; // esi

  PropA = (CMsgProcessMgr **)GetPropA(hWnd, CServerWindowFramework::ms_this);
  v5 = PropA;
  if ( uMsg == 2 )
  {
    PostQuitMessage(0);
    return DefWindowProcA(hWnd, uMsg, wParam, lParam);
  }
  if ( !PropA )
    return DefWindowProcA(hWnd, uMsg, wParam, lParam);
  v7 = PropA[11];
  if ( v7 )
  {
    Castle = Castle::CCastleMgr::GetCastle(v7, uMsg);
    if ( Castle )
      return Castle->operator()(Castle, hWnd, uMsg, wParam, lParam);
  }
  if ( uMsg != CSysTray::GetSysTrayNotifyMsg() )
    return DefWindowProcA(hWnd, uMsg, wParam, lParam);
  if ( lParam == 517 )
  {
    v9 = (CSysTray *)v5[6];
    if ( v9 )
    {
      MenuA = LoadMenuA((HINSTANCE)v5[1], (LPCSTR)*((unsigned __int16 *)v5 + 6));
      v11 = MenuA;
      if ( MenuA )
      {
        SubMenu = GetSubMenu(MenuA, 0);
        v13 = SubMenu;
        if ( SubMenu )
        {
          CSysTray::ShowPopupMenu(v9, hWnd, SubMenu);
          DestroyMenu(v13);
        }
        DestroyMenu(v11);
      }
    }
  }
  return 0;
}

//----- (00413DE0) --------------------------------------------------------
void __thiscall CServerWindowFramework::PrintOutput(CServerWindowFramework *this, const char *szText, int nLength)
{
  CConsoleWindow *m_lpConsoleWindow; // ecx

  m_lpConsoleWindow = this->m_lpConsoleWindow;
  if ( m_lpConsoleWindow )
    CConsoleWindow::PrintOutput(m_lpConsoleWindow, szText, nLength);
}

//----- (00413DF0) --------------------------------------------------------
void __thiscall CServerWindowFramework::PrintInfo(CServerWindowFramework *this, const char *szText, int nLength)
{
  CConsoleWindow *m_lpConsoleWindow; // ecx

  m_lpConsoleWindow = this->m_lpConsoleWindow;
  if ( m_lpConsoleWindow )
    CConsoleWindow::PrintInfo(m_lpConsoleWindow, szText, nLength);
}

//----- (00413E00) --------------------------------------------------------
char __thiscall CServerWindowFramework::SendManageClientPacket(
        CServerWindowFramework *this,
        const void *lpData,
        unsigned __int16 usLength)
{
  CBufferFactory *m_lpBufferFactory; // eax
  LPARAM v4; // eax
  LPARAM v5; // ebx

  m_lpBufferFactory = this->m_lpBufferFactory;
  if ( m_lpBufferFactory )
  {
    v4 = (LPARAM)m_lpBufferFactory->Create(m_lpBufferFactory, usLength);
    v5 = v4;
    if ( v4 )
    {
      qmemcpy(*(void **)(v4 + 8), lpData, usLength);
      *(_DWORD *)(v4 + 8) += usLength;
      if ( PostMessageA(this->m_hWnd, CServerWindowFramework::ms_SendDataMsg, 0, v4) )
        return 1;
      (*(void (__thiscall **)(_DWORD, LPARAM))(**(_DWORD **)(v5 + 44) + 8))(*(_DWORD *)(v5 + 44), v5);
    }
  }
  return 0;
}

//----- (00413E80) --------------------------------------------------------
void __thiscall CProcessThread::CProcessThread(
        CProcessThread *this,
        CServerWindowFramework *ServerWindowFramework,
        int nProcessTick)
{
  this->m_hThreadHandle = (void *)-1;
  this->__vftable = (CProcessThread_vtbl *)&CProcessThread::`vftable';
  this->m_bExit = 0;
  this->m_nProcessTick = nProcessTick;
  this->m_ServerWindowFramework = ServerWindowFramework;
}
// 4D6EC8: using guessed type void *CProcessThread::`vftable';

//----- (00413EB0) --------------------------------------------------------
void __thiscall CProcessThread::~CProcessThread(CProcessThread *this)
{
  this->__vftable = (CProcessThread_vtbl *)&CProcessThread::`vftable';
  CThreadMgr::Stop(this, 0xFFFFFFFF);
  this->__vftable = (CProcessThread_vtbl *)&CThread::`vftable';
}
// 4D6EC8: using guessed type void *CProcessThread::`vftable';
// 500DF0: using guessed type void *CThread::`vftable';

//----- (00413F00) --------------------------------------------------------
char __thiscall CServerWindowFramework::InitializeFramework(
        CServerWindowFramework *this,
        HINSTANCE__ *hInstance,
        const char *szWndApplicationName,
        unsigned int nICON_ID,
        int nMenu_ID)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  HWND__ *Window; // eax
  CSysTray *v9; // eax
  CSysTray *v10; // eax
  CConsoleWindow *v11; // eax
  CConsoleWindow *v12; // eax
  bool v13; // zf
  HICON IconA; // eax
  CMsgProc *v15; // eax
  CMsgProc *v16; // eax
  CCommandProcess *m_lpCommandProcess; // ecx
  CConsoleCMDFactory *m_lpCommandFactory; // edx
  DWORD LastError; // [esp-14h] [ebp-48h]
  DWORD v20; // [esp-14h] [ebp-48h]
  tagWNDCLASSA wndClass; // [esp+0h] [ebp-34h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v22; // [esp+28h] [ebp-Ch]
  void *v23; // [esp+2Ch] [ebp-8h]
  int v24; // [esp+30h] [ebp-4h]

  v24 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v23 = &_ehhandler__InitializeFramework_CServerWindowFramework__IAE_NPAUHINSTANCE____PBDHH_Z;
  v22 = ExceptionList;
  if ( (this->m_dwInternalFlags & 1) == 0 )
  {
    _snprintf(this->m_szAppName, 0x103u, "%s", szWndApplicationName);
    this->m_szAppName[259] = 0;
    this->m_hInstance = hInstance;
    this->m_nMenuID = nMenu_ID;
    wndClass.hInstance = hInstance;
    wndClass.hIcon = LoadIconA(hInstance, (LPCSTR)(unsigned __int16)nICON_ID);
    wndClass.lpszClassName = this->m_szAppName;
    wndClass.style = 0;
    wndClass.lpfnWndProc = CServerWindowFramework::ServerWindowFrameworkProc;
    wndClass.cbClsExtra = 0;
    wndClass.cbWndExtra = 0;
    wndClass.hCursor = 0;
    wndClass.hbrBackground = (HBRUSH__ *)6;
    wndClass.lpszMenuName = 0;
    if ( !RegisterClassA(&wndClass) )
    {
      LastError = GetLastError();
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CServerWindowFramework::InitializeFramework",
        aDWorkRylSource_6,
        260,
        "RegisterClass failed. ErrorCode:%d.",
        LastError);
      return 0;
    }
    Window = CreateWindowExA(
               0,
               wndClass.lpszClassName,
               this->m_szAppName,
               0x20CF0000u,
               0,
               0,
               0,
               0,
               0,
               0,
               wndClass.hInstance,
               0);
    this->m_hWnd = Window;
    if ( !Window )
    {
      v20 = GetLastError();
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CServerWindowFramework::InitializeFramework",
        aDWorkRylSource_6,
        269,
        "CreateWindow failed. ErrorCode:%d",
        v20);
      return 0;
    }
    if ( !SetPropA(Window, CServerWindowFramework::ms_this, this) )
      return 0;
    v9 = (CSysTray *)operator new((tagHeader *)0x64);
    v24 = 0;
    if ( v9 )
      CSysTray::CSysTray(v9, this->m_hWnd, wndClass.hInstance);
    else
      v10 = 0;
    v24 = -1;
    this->m_lpSysTray = v10;
    v11 = (CConsoleWindow *)operator new((tagHeader *)0x50);
    v24 = 1;
    if ( v11 )
      CConsoleWindow::CConsoleWindow(
        v11,
        wndClass.hInstance,
        this->m_hWnd,
        this->m_lpCommandFactory,
        this->m_lpCommandProcess);
    else
      v12 = 0;
    v13 = this->m_lpSysTray == 0;
    v24 = -1;
    this->m_lpConsoleWindow = v12;
    if ( v13 )
      return 0;
    if ( !v12 )
      return 0;
    IconA = LoadIconA(wndClass.hInstance, (LPCSTR)(unsigned __int16)nICON_ID);
    if ( !CSysTray::AddIcon(this->m_lpSysTray, szWndApplicationName, IconA, nICON_ID) )
      return 0;
    v15 = (CMsgProc *)operator new((tagHeader *)4);
    if ( v15 )
      v15->__vftable = (CMsgProc_vtbl *)&CSendDataToManageClient::`vftable';
    else
      v15 = 0;
    if ( !CMsgProcessMgr::Register(this->m_lpMsgProcessMgr, CServerWindowFramework::ms_SendDataMsg, v15) )
      return 0;
    v16 = (CMsgProc *)operator new((tagHeader *)0x10);
    if ( v16 )
    {
      m_lpCommandProcess = this->m_lpCommandProcess;
      m_lpCommandFactory = this->m_lpCommandFactory;
      v16->__vftable = (CMsgProc_vtbl *)&CRecvCommandFromManageClient::`vftable';
      v16[1].__vftable = (CMsgProc_vtbl *)this;
      v16[2].__vftable = (CMsgProc_vtbl *)m_lpCommandFactory;
      v16[3].__vftable = (CMsgProc_vtbl *)m_lpCommandProcess;
    }
    else
    {
      v16 = 0;
    }
    if ( !CMsgProcessMgr::Register(this->m_lpMsgProcessMgr, 0x4Au, v16) || !CIOCPNet::Initialize(this->m_lpIOCPNet) )
      return 0;
    this->m_dwInternalFlags |= 1u;
  }
  return 1;
}
// 414065: variable 'v10' is possibly undefined
// 4140A3: variable 'v12' is possibly undefined
// 4D6EC0: using guessed type void *CSendDataToManageClient::`vftable';
// 4D6ED8: using guessed type void *CRecvCommandFromManageClient::`vftable';

//----- (00414170) --------------------------------------------------------
CSendDataToManageClient *__thiscall CSendDataToManageClient::`vector deleting destructor'(
        CSendDataToManageClient *this,
        char a2)
{
  CSendDataToManageClient::~CSendDataToManageClient(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00414190) --------------------------------------------------------
void __thiscall CSendDataToManageClient::~CSendDataToManageClient(CSendDataToManageClient *this)
{
  this->__vftable = (CSendDataToManageClient_vtbl *)&CMsgProc::`vftable';
}
// 500C28: using guessed type void *CMsgProc::`vftable';

//----- (004141A0) --------------------------------------------------------
void __thiscall CServerWindowFramework::Destroy(CServerWindowFramework *this)
{
  unsigned int m_dwInternalFlags; // eax
  CIOCPNet *m_lpIOCPNet; // edi
  CMsgProcessMgr *m_lpMsgProcessMgr; // edi
  CTimerProcMgr *m_lpTimerProcessMgr; // edi
  CSysTray *m_lpSysTray; // edi
  HWND__ *m_hWnd; // eax
  CConsoleWindow *m_lpConsoleWindow; // ecx
  CCommandProcess *m_lpCommandProcess; // edi
  CConsoleCMDFactory *m_lpCommandFactory; // ecx
  CBufferFactory *m_lpBufferFactory; // ecx

  m_dwInternalFlags = this->m_dwInternalFlags;
  if ( (m_dwInternalFlags & 2) == 0 )
  {
    m_lpIOCPNet = this->m_lpIOCPNet;
    this->m_dwInternalFlags = m_dwInternalFlags | 2;
    if ( m_lpIOCPNet )
    {
      CIOCPNet::~CIOCPNet(m_lpIOCPNet);
      operator delete(m_lpIOCPNet);
    }
    m_lpMsgProcessMgr = this->m_lpMsgProcessMgr;
    this->m_lpIOCPNet = 0;
    if ( m_lpMsgProcessMgr )
    {
      CMsgProcessMgr::~CMsgProcessMgr(m_lpMsgProcessMgr);
      operator delete(m_lpMsgProcessMgr);
    }
    m_lpTimerProcessMgr = this->m_lpTimerProcessMgr;
    this->m_lpMsgProcessMgr = 0;
    if ( m_lpTimerProcessMgr )
    {
      CTimerProcMgr::~CTimerProcMgr(m_lpTimerProcessMgr);
      operator delete(m_lpTimerProcessMgr);
    }
    m_lpSysTray = this->m_lpSysTray;
    this->m_lpTimerProcessMgr = 0;
    if ( m_lpSysTray )
    {
      CSysTray::~CSysTray(m_lpSysTray);
      operator delete(m_lpSysTray);
    }
    m_hWnd = this->m_hWnd;
    this->m_lpSysTray = 0;
    if ( m_hWnd )
    {
      DestroyWindow(m_hWnd);
      this->m_hWnd = 0;
    }
    m_lpConsoleWindow = this->m_lpConsoleWindow;
    if ( m_lpConsoleWindow )
      ((void (__thiscall *)(CConsoleWindow *, int))m_lpConsoleWindow->~CConsoleWindow)(m_lpConsoleWindow, 1);
    m_lpCommandProcess = this->m_lpCommandProcess;
    this->m_lpConsoleWindow = 0;
    if ( m_lpCommandProcess )
    {
      CCommandProcess::~CCommandProcess(m_lpCommandProcess);
      operator delete(m_lpCommandProcess);
    }
    m_lpCommandFactory = this->m_lpCommandFactory;
    this->m_lpCommandProcess = 0;
    if ( m_lpCommandFactory )
      ((void (__thiscall *)(CConsoleCMDFactory *, int))m_lpCommandFactory->~CConsoleCMDFactory)(m_lpCommandFactory, 1);
    m_lpBufferFactory = this->m_lpBufferFactory;
    this->m_lpCommandFactory = 0;
    if ( m_lpBufferFactory )
      ((void (__thiscall *)(CBufferFactory *, int))m_lpBufferFactory->~CBufferFactory)(m_lpBufferFactory, 1);
    this->m_lpBufferFactory = 0;
  }
}

//----- (00414280) --------------------------------------------------------
void __thiscall CServerWindowFramework::SendManageClientPing(CServerWindowFramework *this)
{
  const char *CommandLineA; // eax
  ServerManage::PktManagePing managePing; // [esp+8h] [ebp-42Ch] BYREF

  managePing.m_dwPID = GetCurrentProcessId();
  managePing.m_dwStatusFlag = 0;
  managePing.m_dwSubCommand = 0;
  GetModuleFileNameA(0, managePing.m_szAppFullPathName, 0x103u);
  managePing.m_szAppFullPathName[259] = 0;
  _snprintf(managePing.m_szWindowName, 0x103u, "%s", this->m_szAppName);
  managePing.m_szWindowName[259] = 0;
  CommandLineA = GetCommandLineA();
  _snprintf(managePing.m_szCommandLine, 0x207u, "%s", CommandLineA);
  managePing.m_dwStatusFlag = this->m_dwServerStatus;
  managePing.m_szCommandLine[519] = 0;
  managePing.m_StartBit = -1;
  managePing.m_Cmd = 48;
  managePing.m_Len = 1064;
  managePing.m_CodePage = 0;
  managePing.m_SrvInfo.dwServerInfo = 0;
  CServerWindowFramework::SendManageClientPacket(this, &managePing, 0x428u);
}

//----- (00414360) --------------------------------------------------------
CProcessThread *__thiscall CProcessThread::`vector deleting destructor'(CProcessThread *this, char a2)
{
  CProcessThread::~CProcessThread(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00414380) --------------------------------------------------------
unsigned int __thiscall CProcessThread::Run(CProcessThread *this)
{
  CServerWindowFramework *m_ServerWindowFramework; // eax
  unsigned int m_dwInternalFlags; // ecx
  CPulse *Instance; // edi
  unsigned int v6; // eax
  int m_nTicksPerPulse; // ecx
  unsigned int v8; // ebp
  unsigned int v9; // ebx
  CCommandProcess *CommandProcess; // [esp+4h] [ebp-Ch]
  CIOCPNet *IOCPNetwork; // [esp+8h] [ebp-8h]
  CServerRequest *ServerRequest; // [esp+Ch] [ebp-4h]

  m_ServerWindowFramework = this->m_ServerWindowFramework;
  m_dwInternalFlags = m_ServerWindowFramework->m_dwInternalFlags;
  if ( (m_dwInternalFlags & 1) == 0 || (m_dwInternalFlags & 2) != 0 )
    return -1;
  CommandProcess = m_ServerWindowFramework->m_lpCommandProcess;
  IOCPNetwork = m_ServerWindowFramework->m_lpIOCPNet;
  ServerRequest = CServerRequest::GetInstance();
  Instance = CPulse::GetInstance();
  Instance->m_nTicksPerPulse = this->m_nProcessTick;
  while ( !InterlockedCompareExchange(&this->m_bExit, 1, 1) )
  {
    v6 = CPulse::CheckSleep(Instance);
    m_nTicksPerPulse = Instance->m_nTicksPerPulse;
    v8 = v6;
    this->m_nProcessTick = m_nTicksPerPulse;
    v9 = 1000 / m_nTicksPerPulse;
    CCommandProcess::ProcessAll(CommandProcess);
    CIOCPNet::Process(IOCPNetwork);
    this->InternalRun(this, Instance);
    if ( !(v8 % v9) )
      CServerRequest::RemoveTimeoutRequest(ServerRequest);
    if ( !(v8 % (5 * v9)) )
      CServerWindowFramework::SendManageClientPing(this->m_ServerWindowFramework);
  }
  return 0;
}

//----- (00414450) --------------------------------------------------------
char __thiscall CServerWindowFramework::Initialize(
        CServerWindowFramework *this,
        HINSTANCE__ *hInstance,
        const char *szWndApplicationName,
        const char *szCmdLine,
        unsigned int nICON_ID,
        int nMenu_ID)
{
  char *v7; // eax
  timecaps_tag *p_tc; // eax
  int v10; // [esp+4h] [ebp-Ch] BYREF
  timecaps_tag tc; // [esp+8h] [ebp-8h] BYREF

  tc.wPeriodMin = 0;
  tc.wPeriodMax = 0;
  if ( this->m_lpCommandProcess
    && this->m_lpCommandFactory
    && this->m_lpIOCPNet
    && this->m_lpMsgProcessMgr
    && this->m_lpTimerProcessMgr
    && this->m_lpBufferFactory )
  {
    if ( timeGetDevCaps(&tc, 8u) )
    {
      v7 = "this:0x%p/ServerWindowFramework initialize failed - Get timer resolution failed";
    }
    else
    {
      v10 = 1;
      p_tc = (timecaps_tag *)&v10;
      if ( tc.wPeriodMin )
        p_tc = &tc;
      if ( tc.wPeriodMax < p_tc->wPeriodMin )
        p_tc = (timecaps_tag *)&tc.wPeriodMax;
      if ( timeBeginPeriod(p_tc->wPeriodMin) )
      {
        v7 = "this:0x%p/ServerWindowFramework initialize failed - Set timer resolution failed";
      }
      else if ( CServerWindowFramework::InitializeFramework(this, hInstance, szWndApplicationName, nICON_ID, nMenu_ID) )
      {
        if ( this->ApplicationSpecificInit(this, szCmdLine) )
          goto LABEL_22;
        v7 = "this:0x%p/ServerWindowFramework initialize failed - ApplicationSpecificInit failed";
      }
      else
      {
        v7 = "this:0x%p/ServerWindowFramework initialize failed - InitialzeFramework failed";
      }
    }
  }
  else
  {
    v7 = "this:0x%p/ServerWindowFramework initialize failed - Create defaultobject failed";
  }
  if ( v7 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CServerWindowFramework::Initialize", aDWorkRylSource_6, 214, v7, this);
    return 0;
  }
LABEL_22:
  CServerWindowFramework::SendManageClientPing(this);
  return 1;
}

//----- (00414570) --------------------------------------------------------
void __thiscall CServerWindowFramework::ProcessMessage(CServerWindowFramework *this)
{
  CServerRequest *Instance; // eax
  CServerRequest *v3; // eax
  std::_List_nod<CThread *>::_Node *Myhead; // ebx
  std::_List_nod<CThread *>::_Node *i; // edi
  std::_List_nod<CThread *>::_Node *v6; // ebx
  std::_List_nod<CThread *>::_Node *j; // edi
  CThread *Myval; // ecx
  std::_List_nod<CThread *>::_Node *v9; // ecx
  std::_List_nod<CThread *>::_Node *Next; // eax
  bool v11; // zf
  std::_List_nod<CThread *>::_Node *v12; // edi
  tagMSG message; // [esp+Ch] [ebp-1Ch] BYREF

  while ( GetMessageA(&message, 0, 0, 0) )
  {
    if ( !IsDialogMessageA(this->m_hWnd, &message) )
    {
      TranslateMessage(&message);
      DispatchMessageA(&message);
    }
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_SYSERR,
    "CServerWindowFramework::ProcessMessage",
    aDWorkRylSource_6,
    367,
    "this:0x%p/finish server",
    this);
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_SYSERR,
    "CServerWindowFramework::ProcessMessage",
    aDWorkRylSource_6,
    368,
    "this:0x%p/finish server",
    this);
  Instance = CServerRequest::GetInstance();
  CServerRequest::RequestOff(Instance);
  v3 = CServerRequest::GetInstance();
  CServerRequest::RemoveAllRequest(v3);
  Myhead = this->m_ProcessThreadList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
    CThreadMgr::Stop(i->_Myval, 0x7D0u);
  CServerLog::DetailLog(
    &g_Log,
    LOG_SYSERR,
    "CServerWindowFramework::ProcessMessage",
    aDWorkRylSource_6,
    385,
    "this:0x%p/finish process threads",
    this);
  v6 = this->m_ProcessThreadList._Myhead;
  for ( j = v6->_Next; j != v6; j = j->_Next )
  {
    Myval = j->_Myval;
    if ( Myval )
      ((void (__thiscall *)(CThread *, int))Myval->~CThread)(Myval, 1);
  }
  v9 = this->m_ProcessThreadList._Myhead;
  Next = v9->_Next;
  v9->_Next = v9;
  this->m_ProcessThreadList._Myhead->_Prev = this->m_ProcessThreadList._Myhead;
  v11 = Next == this->m_ProcessThreadList._Myhead;
  this->m_ProcessThreadList._Mysize = 0;
  if ( !v11 )
  {
    do
    {
      v12 = Next->_Next;
      operator delete(Next);
      Next = v12;
    }
    while ( v12 != this->m_ProcessThreadList._Myhead );
  }
  CServerWindowFramework::Destroy(this);
}

//----- (004146E0) --------------------------------------------------------
void __thiscall CServerWindowFramework::~CServerWindowFramework(CServerWindowFramework *this)
{
  std::list<CThread *> *v1; // esi

  v1 = (std::list<CThread *> *)this;
  this->__vftable = (CServerWindowFramework_vtbl *)&CServerWindowFramework::`vftable';
  CServerWindowFramework::Destroy(this);
  v1 = (std::list<CThread *> *)((char *)v1 + 56);
  std::list<IOPCode *>::clear(v1);
  operator delete(v1->_Myhead);
  v1->_Myhead = 0;
}
// 4D7214: using guessed type void *CServerWindowFramework::`vftable';

//----- (00414740) --------------------------------------------------------
void __thiscall CServerWindowFramework::CServerWindowFramework(CServerWindowFramework *this)
{
  CCommandProcess *v2; // eax
  CCommandProcess *v3; // eax
  CConsoleCMDFactory *v4; // eax
  CConsoleCMDFactory *v5; // eax
  CIOCPNet *v6; // eax
  CIOCPNet *v7; // eax
  CMsgProcessMgr *v8; // eax
  CMsgProcessMgr *v9; // eax
  CTimerProcMgr *v10; // eax
  CTimerProcMgr *v11; // eax
  CPoolBufferFactory *v12; // eax
  CBufferFactory *v13; // eax

  this->__vftable = (CServerWindowFramework_vtbl *)&CServerWindowFramework::`vftable';
  this->m_hInstance = 0;
  this->m_hWnd = 0;
  this->m_nMenuID = 0;
  this->m_dwInternalFlags = 0;
  this->m_dwServerStatus = 0;
  this->m_lpSysTray = 0;
  this->m_lpConsoleWindow = 0;
  v2 = (CCommandProcess *)operator new((tagHeader *)0x24);
  if ( v2 )
    CCommandProcess::CCommandProcess(v2);
  else
    v3 = 0;
  this->m_lpCommandProcess = v3;
  v4 = (CConsoleCMDFactory *)operator new((tagHeader *)0x14);
  if ( v4 )
    CConsoleCMDFactory::CConsoleCMDFactory(v4);
  else
    v5 = 0;
  this->m_lpCommandFactory = v5;
  v6 = (CIOCPNet *)operator new((tagHeader *)0x48);
  if ( v6 )
    CIOCPNet::CIOCPNet(v6);
  else
    v7 = 0;
  this->m_lpIOCPNet = v7;
  v8 = (CMsgProcessMgr *)operator new((tagHeader *)0xC);
  if ( v8 )
    CMsgProcessMgr::CMsgProcessMgr(v8);
  else
    v9 = 0;
  this->m_lpMsgProcessMgr = v9;
  v10 = (CTimerProcMgr *)operator new((tagHeader *)0x14);
  if ( v10 )
    CTimerProcMgr::CTimerProcMgr(v10);
  else
    v11 = 0;
  this->m_lpTimerProcessMgr = v11;
  v12 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
  if ( v12 )
    CPoolBufferFactory::CPoolBufferFactory(v12);
  else
    v13 = 0;
  this->m_lpBufferFactory = v13;
  this->m_ProcessThreadList._Myhead = std::list<CModifyDummyCharacter *>::_Buynode(&this->m_ProcessThreadList);
  this->m_ProcessThreadList._Mysize = 0;
}
// 4147A5: variable 'v3' is possibly undefined
// 4147D2: variable 'v5' is possibly undefined
// 4147FF: variable 'v7' is possibly undefined
// 41482C: variable 'v9' is possibly undefined
// 414859: variable 'v11' is possibly undefined
// 41488C: variable 'v13' is possibly undefined
// 4D7214: using guessed type void *CServerWindowFramework::`vftable';

//----- (004148B0) --------------------------------------------------------
CServerWindowFramework *__thiscall CServerWindowFramework::`vector deleting destructor'(
        CServerWindowFramework *this,
        char a2)
{
  CServerWindowFramework::~CServerWindowFramework(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004148D0) --------------------------------------------------------
void __thiscall std::list<CThread *>::_Incsize(std::list<CThread *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00414970) --------------------------------------------------------
int __thiscall CRecvCommandFromManageClient::operator()(
        CRecvCommandFromManageClient *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        unsigned int wParam,
        int lParam)
{
  int v6; // eax
  signed int v7; // eax
  int v8; // edi
  CConsoleCommand *v9; // eax
  CConsoleWindow *m_lpConsoleWindow; // ecx
  char szCmdBuffer[512]; // [esp+4h] [ebp-204h] BYREF

  if ( lParam )
  {
    v6 = *(_DWORD *)(lParam + 8);
    if ( *(_BYTE *)(v6 + 1) == 9 )
    {
      v7 = _snprintf(szCmdBuffer, 0x1FFu, "%s", (const char *)(v6 + 32));
      v8 = v7;
      szCmdBuffer[511] = 0;
      if ( v7 > 0 && (v9 = CConsoleCMDFactory::Create(this->m_CommandFactory, szCmdBuffer, v7)) != 0 )
      {
        CCommandProcess::Add(this->m_CommandProcess, v9);
        m_lpConsoleWindow = this->m_ServerWindowFramework->m_lpConsoleWindow;
        if ( m_lpConsoleWindow )
          CConsoleWindow::PrintOutput(m_lpConsoleWindow, szCmdBuffer, v8);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CRecvCommandFromManageClient::operator`()'",
          aDWorkRylSource_6,
          134,
          "Command create failed from ManageTool. CMD:%s",
          szCmdBuffer);
      }
    }
  }
  return 0;
}

//----- (00414A40) --------------------------------------------------------
char __thiscall CServerWindowFramework::AddProcessThread(CServerWindowFramework *this, CThread *lpProcessThread)
{
  CThread *v2; // edi
  std::_List_nod<CThread *>::_Node *Myhead; // edi
  std::list<CThread *> *p_m_ProcessThreadList; // esi
  std::_List_nod<CThread *>::_Node *v7; // ebx

  v2 = lpProcessThread;
  if ( !lpProcessThread )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerWindowFramework::AddProcessThread",
      aDWorkRylSource_6,
      494,
      "this:0x%p/Process thread pointer is 0",
      0);
    return 0;
  }
  if ( CThreadMgr::Run(lpProcessThread) == (HANDLE)-1 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CServerWindowFramework::AddProcessThread",
      aDWorkRylSource_6,
      494,
      "this:0x%p/Process thread start failed",
      v2);
    return 0;
  }
  Myhead = this->m_ProcessThreadList._Myhead;
  p_m_ProcessThreadList = &this->m_ProcessThreadList;
  v7 = std::list<IOPCode *>::_Buynode(p_m_ProcessThreadList, Myhead, Myhead->_Prev, &lpProcessThread);
  std::list<CThread *>::_Incsize(p_m_ProcessThreadList, 1u);
  Myhead->_Prev = v7;
  v7->_Prev->_Next = v7;
  return 1;
}

//----- (00414AC0) --------------------------------------------------------
void __thiscall CNetworkPos::Initialize(
        CNetworkPos *this,
        float fXPos,
        float fYPos,
        float fZPos,
        float fDir,
        float fVel)
{
  double i; // st7
  unsigned __int64 v7; // rax

  this->m_usXPos = (unsigned __int64)(10.0 * fXPos);
  this->m_usYPos = (unsigned __int64)(10.0 * fYPos);
  this->m_usZPos = (unsigned __int64)(10.0 * fZPos);
  for ( i = 57.295792 * fDir; i >= 360.0; i = i - 360.0 )
    ;
  this->m_cDirection = (unsigned __int64)(i * 0.5);
  v7 = (unsigned __int64)(1000.0 * fVel);
  if ( (unsigned int)v7 > 0xFF )
    LOBYTE(v7) = -1;
  this->m_cVelocity = v7;
}

//----- (00414B70) --------------------------------------------------------
void __thiscall Item::CEquipment::AddAttribute(
        Item::CEquipment *this,
        Item::Attribute::Type eAttributeType,
        unsigned __int16 nAttributeValue)
{
  bool v3; // sf

  v3 = (__int16)(nAttributeValue + this->m_usAttribute[eAttributeType]) < 0;
  this->m_usAttribute[eAttributeType] += nAttributeValue;
  if ( v3 )
    this->m_usAttribute[eAttributeType] = 0;
  else
    this->m_usAttribute[eAttributeType] = this->m_usAttribute[eAttributeType];
}

//----- (00414BA0) --------------------------------------------------------
Item::CEquipment *__cdecl Item::CEquipment::DowncastToEquipment(Item::CEquipment *lpItem)
{
  Item::CEquipment *result; // eax

  result = lpItem;
  if ( lpItem )
    return (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? lpItem : 0;
  return result;
}

//----- (00414BC0) --------------------------------------------------------
CCell *__thiscall CCell::GetConnectCell(CCell *this, unsigned int nDir)
{
  if ( nDir <= 9 )
    return this->m_lpConnectCell[nDir];
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCell::GetConnectCell",
    aDWorkRylSource_30,
    1713,
    "Cannot get incorrect direction cell : %d",
    nDir);
  return 0;
}

//----- (00414C00) --------------------------------------------------------
void __thiscall CCell::SetConnectCell(CCell *this, unsigned int nDir, CCell *lpConnectedCell)
{
  if ( nDir > 9 )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCell::SetConnectCell",
      aDWorkRylSource_30,
      1725,
      "Cannot set incorrect direction cell : %d",
      nDir);
  else
    this->m_lpConnectCell[nDir] = lpConnectedCell;
}

//----- (00414C40) --------------------------------------------------------
char __thiscall CCell::IsNearCell(CCell *this, CCell *lpNearCell)
{
  CCell **m_lpConnectCell; // eax

  m_lpConnectCell = this->m_lpConnectCell;
  if ( this->m_lpConnectCell == (CCell **)&this->m_lpBroadCastBuffer )
    return 0;
  while ( *m_lpConnectCell != lpNearCell )
  {
    if ( ++m_lpConnectCell == (CCell **)&this->m_lpBroadCastBuffer )
      return 0;
  }
  return 1;
}

//----- (00414C70) --------------------------------------------------------
unsigned int __thiscall CCell::GetNearCellCharacterNum(CCell *this)
{
  CBuffer **p_m_lpBroadCastBuffer; // esi
  CCell **m_lpConnectCell; // ecx
  unsigned int result; // eax

  p_m_lpBroadCastBuffer = &this->m_lpBroadCastBuffer;
  m_lpConnectCell = this->m_lpConnectCell;
  for ( result = 0; m_lpConnectCell != (CCell **)p_m_lpBroadCastBuffer; ++m_lpConnectCell )
  {
    if ( *m_lpConnectCell )
      result += (*m_lpConnectCell)->m_lstCharacter._Mysize;
  }
  return result;
}

//----- (00414CA0) --------------------------------------------------------
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First,
        std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last,
        CFindCreatureFromCID _Pred)
{
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // ecx
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v5; // eax

  Ptr = _First._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v5 = result;
    result->_Ptr = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)_First._Ptr;
  }
  else
  {
    do
    {
      if ( _Pred.m_dwCID == Ptr->_Myval->m_dwCID )
        break;
      Ptr = Ptr->_Next;
    }
    while ( Ptr != _Last._Ptr );
    v5 = result;
    result->_Ptr = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)Ptr;
  }
  return v5;
}

//----- (00414CD0) --------------------------------------------------------
std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::find_if<std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindItemInfoFromUID>(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First,
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last,
        CFindItemInfoFromUID _Pred)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // ecx
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v5; // eax

  Ptr = _First._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v5 = result;
    result->_Ptr = _First._Ptr;
  }
  else
  {
    do
    {
      if ( _Pred.m_nUID == Ptr->_Myval.UID.nUniqueID )
        break;
      Ptr = Ptr->_Next;
    }
    while ( Ptr != _Last._Ptr );
    v5 = result;
    result->_Ptr = Ptr;
  }
  return v5;
}

//----- (00414D10) --------------------------------------------------------
CSiegeObject *__thiscall CCell::GetFirstAirShip(CCell *this)
{
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  bool v2; // zf
  CSiegeObject *result; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax

  Myhead = this->m_lstSiegeObject._Myhead;
  v2 = Myhead->_Next == Myhead;
  this->m_SiegeObjectIt._Ptr = Myhead->_Next;
  if ( v2 )
    return 0;
  while ( 1 )
  {
    result = this->m_SiegeObjectIt._Ptr->_Myval;
    if ( result )
    {
      if ( result->m_wObjectType == 5626 )
        break;
    }
    Next = this->m_SiegeObjectIt._Ptr->_Next;
    this->m_SiegeObjectIt._Ptr = Next;
    if ( Next == this->m_lstSiegeObject._Myhead )
      return 0;
  }
  return result;
}

//----- (00414D50) --------------------------------------------------------
CSiegeObject *__thiscall CCell::GetNextAirShip(CCell *this)
{
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  CSiegeObject *result; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v4; // edx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v5; // eax

  Myhead = this->m_lstSiegeObject._Myhead;
  Next = this->m_SiegeObjectIt._Ptr->_Next;
  this->m_SiegeObjectIt._Ptr = Next;
  if ( Next == Myhead )
    return 0;
  while ( 1 )
  {
    result = this->m_SiegeObjectIt._Ptr->_Myval;
    if ( result )
    {
      if ( result->m_wObjectType == 5626 )
        break;
    }
    v4 = this->m_lstSiegeObject._Myhead;
    v5 = this->m_SiegeObjectIt._Ptr->_Next;
    this->m_SiegeObjectIt._Ptr = v5;
    if ( v5 == v4 )
      return 0;
  }
  return result;
}

//----- (00414D90) --------------------------------------------------------
CSiegeObject *__thiscall CCell::GetFirstSiegeObject(CCell *this)
{
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  bool v2; // zf
  CSiegeObject *result; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // edx

  Myhead = this->m_lstSiegeObject._Myhead;
  v2 = Myhead->_Next == Myhead;
  this->m_SiegeObjectIt._Ptr = Myhead->_Next;
  if ( v2 )
    return 0;
  while ( 1 )
  {
    result = this->m_SiegeObjectIt._Ptr->_Myval;
    if ( result )
      break;
    Next = this->m_SiegeObjectIt._Ptr->_Next;
    this->m_SiegeObjectIt._Ptr = Next;
    if ( Next == this->m_lstSiegeObject._Myhead )
      return 0;
  }
  return result;
}

//----- (00414DC0) --------------------------------------------------------
CSiegeObject *__thiscall CCell::GetNextSiegeObject(CCell *this)
{
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  CSiegeObject *result; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v4; // edx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v5; // eax

  Myhead = this->m_lstSiegeObject._Myhead;
  Next = this->m_SiegeObjectIt._Ptr->_Next;
  this->m_SiegeObjectIt._Ptr = Next;
  if ( Next == Myhead )
    return 0;
  while ( 1 )
  {
    result = this->m_SiegeObjectIt._Ptr->_Myval;
    if ( result )
      break;
    v4 = this->m_lstSiegeObject._Myhead;
    v5 = this->m_SiegeObjectIt._Ptr->_Next;
    this->m_SiegeObjectIt._Ptr = v5;
    if ( v5 == v4 )
      return 0;
  }
  return result;
}

//----- (00414DF0) --------------------------------------------------------
CCharacter *__thiscall CCell::GetFirstCharacter(CCell *this)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax

  if ( !this->m_lstCharacter._Mysize )
    return 0;
  Next = this->m_lstCharacter._Myhead->_Next;
  this->m_CharacterIt._Ptr = Next;
  return Next->_Myval;
}

//----- (00414E10) --------------------------------------------------------
CMonster *__thiscall CCell::GetFirstAggresiveCreature(CCell *this)
{
  unsigned int Mysize; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v2; // eax
  CMonster *result; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  bool v5; // zf
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // edx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v7; // edx
  unsigned int v8; // eax
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v9; // edx
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax

  Mysize = this->m_lstCharacter._Mysize;
  this->m_cTurnOfGetAggresiveCreature = 0;
  if ( !Mysize
    || (v2 = this->m_lstCharacter._Myhead->_Next, this->m_CharacterIt._Ptr = v2, (result = (CMonster *)v2->_Myval) == 0) )
  {
    Myhead = this->m_lstSiegeObject._Myhead;
    this->m_cTurnOfGetAggresiveCreature = 1;
    v5 = Myhead->_Next == Myhead;
    this->m_SiegeObjectIt._Ptr = Myhead->_Next;
    if ( v5 )
    {
LABEL_6:
      v8 = this->m_lstMonster._Mysize;
      this->m_cTurnOfGetAggresiveCreature = 2;
      if ( v8 )
      {
        v9 = this->m_lstMonster._Myhead;
        Next = v9->_Next;
        this->m_MonsterIt._Ptr = v9->_Next;
        return Next->_Myval;
      }
      else
      {
        return 0;
      }
    }
    else
    {
      while ( 1 )
      {
        Ptr = this->m_SiegeObjectIt._Ptr;
        result = Ptr->_Myval;
        if ( result )
          break;
        v7 = Ptr->_Next;
        this->m_SiegeObjectIt._Ptr = v7;
        if ( v7 == this->m_lstSiegeObject._Myhead )
          goto LABEL_6;
      }
    }
  }
  return result;
}

//----- (00414E80) --------------------------------------------------------
CSiegeObject *__thiscall CCell::GetNextAggresiveCreature(CCell *this)
{
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edx
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  CSiegeObject *result; // eax
  int v4; // ecx
  int v5; // eax
  int *v6; // edx
  int v7; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v8; // edx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v9; // eax
  int v10; // ecx
  int v11; // eax
  int v12; // eax

  if ( this->m_cTurnOfGetAggresiveCreature )
  {
    if ( this->m_cTurnOfGetAggresiveCreature == 1 )
    {
      result = CCell::GetNextSiegeObject(this);
      if ( result )
        return result;
      v5 = *(_DWORD *)(v4 + 20);
      *(_BYTE *)(v4 + 113) = 2;
      if ( v5 )
      {
        v6 = *(int **)(v4 + 16);
        v7 = *v6;
        *(_DWORD *)(v4 + 48) = *v6;
        return *(CSiegeObject **)(v7 + 8);
      }
    }
    else if ( this->m_cTurnOfGetAggresiveCreature == 2 )
    {
      Myhead = this->m_lstMonster._Myhead;
      Next = this->m_MonsterIt._Ptr->_Next;
      this->m_MonsterIt._Ptr = Next;
      if ( Next != Myhead )
        return (CSiegeObject *)Next->_Myval;
    }
    return 0;
  }
  v8 = this->m_lstCharacter._Myhead;
  v9 = this->m_CharacterIt._Ptr->_Next;
  this->m_CharacterIt._Ptr = v9;
  if ( v9 == v8 || (result = (CSiegeObject *)v9->_Myval) == 0 )
  {
    this->m_cTurnOfGetAggresiveCreature = 1;
    result = CCell::GetFirstSiegeObject(this);
    if ( !result )
    {
      v11 = *(_DWORD *)(v10 + 20);
      *(_BYTE *)(v10 + 113) = 2;
      if ( v11 )
      {
        v12 = **(_DWORD **)(v10 + 16);
        *(_DWORD *)(v10 + 48) = v12;
        return *(CSiegeObject **)(v12 + 8);
      }
      return 0;
    }
  }
  return result;
}
// 414EAD: variable 'v4' is possibly undefined
// 414EE9: variable 'v10' is possibly undefined

//----- (00414F10) --------------------------------------------------------
CCreature *__thiscall CCell::GetCreature(CCell *this, signed int dwCID)
{
  unsigned __int8 v3; // al
  int v4; // eax

  if ( dwCID >= 0 )
  {
    if ( (dwCID & 0x40000000) != 0 )
      v3 = 1;
    else
      v3 = (dwCID & 0x10000000) != 0 ? 5 : 0;
  }
  else
  {
    v3 = 2;
  }
  if ( v3 )
  {
    v4 = v3 - 2;
    if ( v4 )
    {
      if ( v4 == 3 )
      {
        std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
          (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
          (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstSiegeObject._Myhead->_Next,
          (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstSiegeObject._Myhead,
          (CFindCreatureFromCID)dwCID);
        if ( (std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID != this->m_lstSiegeObject._Myhead )
          return *(CCreature **)(dwCID + 8);
      }
    }
    else
    {
      std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
        (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
        (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstMonster._Myhead->_Next,
        (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstMonster._Myhead,
        (CFindCreatureFromCID)dwCID);
      if ( (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID != this->m_lstMonster._Myhead )
        return *(CCreature **)(dwCID + 8);
    }
  }
  else
  {
    std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
      (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstCharacter._Myhead->_Next,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstCharacter._Myhead,
      (CFindCreatureFromCID)dwCID);
    if ( (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID != this->m_lstCharacter._Myhead )
      return *(CCreature **)(dwCID + 8);
  }
  return 0;
}

//----- (00414FD0) --------------------------------------------------------
void __thiscall CCell::PrepareBroadCast(CCell *this)
{
  CBuffer *m_lpBroadCastBuffer; // eax
  unsigned int v3; // edi
  CCellManager *Instance; // eax
  CBuffer *v5; // eax
  char *internal_buffer; // ecx
  unsigned __int8 v7; // dl
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ebx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *i; // ebp
  CCharacter *Myval; // edi
  float m_fPointX; // eax
  CSingleDispatch *DispatchTable; // eax
  CBuffer *v13; // eax
  unsigned int v14; // ebx
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v15; // eax
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // ebp
  bool v17; // zf
  CSummonMonster *v18; // edi
  CBuffer *v19; // eax
  int v20; // ecx
  char *v21; // edx
  unsigned __int16 m_wMapIndex; // ax
  VirtualArea::CVirtualAreaMgr *v23; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // ecx
  CVirtualMonsterMgr *v26; // eax
  bool IsSummonee; // al
  float m_fPointY; // [esp-10h] [ebp-70h]
  CBuffer *v29; // [esp-8h] [ebp-68h]
  char *wr_ptr; // [esp-8h] [ebp-68h]
  unsigned __int16 v31; // [esp-4h] [ebp-64h]
  unsigned int m_dwCID; // [esp-4h] [ebp-64h]
  unsigned int nBroadCastNum; // [esp+10h] [ebp-50h] BYREF
  unsigned int dwBufferSize; // [esp+14h] [ebp-4Ch] BYREF
  float m_fPointZ; // [esp+18h] [ebp-48h]
  float v36; // [esp+1Ch] [ebp-44h]
  float v37; // [esp+20h] [ebp-40h]
  CNetworkPos v38; // [esp+24h] [ebp-3Ch] BYREF
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+2Ch] [ebp-34h] BYREF
  PktMVEx pktMVEx; // [esp+34h] [ebp-2Ch] BYREF
  int v41; // [esp+5Ch] [ebp-4h]

  m_lpBroadCastBuffer = this->m_lpBroadCastBuffer;
  v3 = 56 * (this->m_lstMonster._Mysize + this->m_lstCharacter._Mysize);
  nBroadCastNum = 0;
  this->m_nBroadCastNum = 0;
  if ( !m_lpBroadCastBuffer || m_lpBroadCastBuffer->buffer_size_ < v3 )
  {
    v29 = m_lpBroadCastBuffer;
    Instance = CCellManager::GetInstance();
    this->m_lpBroadCastBuffer = CCellManager::ReallocateBuffer(Instance, v29, v3);
  }
  v5 = this->m_lpBroadCastBuffer;
  if ( !v5 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCell::PrepareBroadCast",
      aDWorkRylSource_30,
      965,
      "Broadcast buffer allocate failed. Reserve size is : %u",
      v3);
    return;
  }
  internal_buffer = v5->internal_buffer_;
  v5->rd_ptr_ = v5->internal_buffer_;
  this->m_lpBroadCastBuffer->wr_ptr_ = internal_buffer;
  if ( this->m_lstCharacter._Mysize || this->m_lstMonster._Mysize )
  {
    v7 = this->m_cBroadcastPhase + 1;
    this->m_cBroadcastPhase = v7;
    if ( v7 >= 3u )
      this->m_cBroadcastPhase = 0;
    pktMVEx.m_NetworkPos.m_usXPos = 0;
    pktMVEx.m_NetworkPos.m_usYPos = 0;
    pktMVEx.m_NetworkPos.m_usZPos = 0;
    Myhead = this->m_lstCharacter._Myhead;
    pktMVEx.m_NetworkPos.m_cDirection = 0;
    pktMVEx.m_NetworkPos.m_cVelocity = 0;
    for ( i = Myhead->_Next; i != Myhead; ++nBroadCastNum )
    {
      Myval = i->_Myval;
      if ( Myval && (Myval->m_cOperationFlags & 1) != 0 )
      {
        pktMVEx.m_dwCID = Myval->m_dwCID;
        m_fPointX = Myval->m_CurrentPos.m_fPointX;
        m_fPointY = Myval->m_CurrentPos.m_fPointY;
        m_fPointZ = Myval->m_CurrentPos.m_fPointZ;
        v36 = m_fPointY;
        v37 = m_fPointX;
        CNetworkPos::Initialize(&v38, m_fPointX, m_fPointY, m_fPointZ, 0.0, 0.0);
        pktMVEx.m_NetworkPos = v38;
        pktMVEx.m_cUAct = 0;
        pktMVEx.m_cLAct = 0;
        if ( PacketWrap::WrapCrypt((char *)&pktMVEx, 0x1Au, 0xBu, Myval->m_dwCID) )
        {
          DispatchTable = CChatDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
          v41 = 0;
          if ( StoragelpChatDispatch.m_lpDispatch )
            CSendStream::PutBuffer((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], (char *)&pktMVEx, 0x1Au, 0xBu);
          v41 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
        }
        if ( !Myval->m_dwRideArmsCID )
        {
          v13 = this->m_lpBroadCastBuffer;
          dwBufferSize = (unsigned int)&v13->internal_buffer_[v13->buffer_size_ - (unsigned int)v13->wr_ptr_];
          if ( !BroadcastInfo::SerializeOutCharacterInfo(
                  Myval,
                  CHARACTER,
                  s_CharacterPhase[this->m_cBroadcastPhase],
                  v13->wr_ptr_,
                  &dwBufferSize) )
          {
            v14 = nBroadCastNum;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CCell::PrepareBroadCast",
              aDWorkRylSource_30,
              1027,
              "Prepare cell broadcasting buffer overflow. Num : %d, (X:%d, Z:%d)",
              nBroadCastNum,
              this->m_cIndexX,
              this->m_cIndexZ);
LABEL_22:
            v15 = this->m_lstMonster._Myhead;
            Next = v15->_Next;
            v17 = v15->_Next == v15;
            v37 = *(float *)&v15;
            if ( v17 )
              goto LABEL_41;
            while ( 1 )
            {
              v18 = (CSummonMonster *)Next->_Myval;
              if ( v18 )
              {
                v19 = this->m_lpBroadCastBuffer;
                v20 = v19->buffer_size_ - (unsigned int)v19->wr_ptr_;
                v21 = v19->internal_buffer_;
                m_wMapIndex = v18->m_CellPos.m_wMapIndex;
                nBroadCastNum = (unsigned int)&v21[v20];
                if ( m_wMapIndex )
                {
                  v31 = m_wMapIndex;
                  v23 = VirtualArea::CVirtualAreaMgr::GetInstance();
                  VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v23, v31);
                  if ( VirtualArea )
                  {
                    m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
                    if ( m_pVirtualMonsterMgr && CVirtualMonsterMgr::IsSummonee(m_pVirtualMonsterMgr, v18->m_dwCID) )
                    {
                      if ( !BroadcastInfo::SerializeOutSummonMonsterInfo(
                              v18,
                              SUMMON_MONSTER,
                              NONE_PHASE,
                              this->m_lpBroadCastBuffer->wr_ptr_,
                              &nBroadCastNum) )
                      {
                        CServerLog::DetailLog(
                          &g_Log,
                          LOG_ERROR,
                          "CCell::PrepareBroadCast",
                          aDWorkRylSource_30,
                          1063,
                          "Prepare cell broadcasting buffer overflow. Num : %d, (X:%d, Z:%d)",
                          v14,
                          this->m_cIndexX,
                          this->m_cIndexZ);
                        goto LABEL_41;
                      }
                    }
                    else if ( !BroadcastInfo::SerializeOutMonsterInfo(
                                 v18,
                                 MONSTER,
                                 NONE_PHASE,
                                 this->m_lpBroadCastBuffer->wr_ptr_,
                                 &nBroadCastNum) )
                    {
                      CServerLog::DetailLog(
                        &g_Log,
                        LOG_ERROR,
                        "CCell::PrepareBroadCast",
                        aDWorkRylSource_30,
                        1075,
                        "Prepare cell broadcasting buffer overflow. Num : %d, (X:%d, Z:%d)",
                        v14,
                        this->m_cIndexX,
                        this->m_cIndexZ);
                      goto LABEL_41;
                    }
                  }
                }
                else
                {
                  m_dwCID = v18->m_dwCID;
                  v26 = (CVirtualMonsterMgr *)CCreatureManager::GetInstance();
                  IsSummonee = CVirtualMonsterMgr::IsSummonee(v26, m_dwCID);
                  wr_ptr = this->m_lpBroadCastBuffer->wr_ptr_;
                  if ( IsSummonee )
                  {
                    if ( !BroadcastInfo::SerializeOutSummonMonsterInfo(
                            v18,
                            SUMMON_MONSTER,
                            NONE_PHASE,
                            wr_ptr,
                            &nBroadCastNum) )
                    {
                      CServerLog::DetailLog(
                        &g_Log,
                        LOG_ERROR,
                        "CCell::PrepareBroadCast",
                        aDWorkRylSource_30,
                        1091,
                        "Prepare cell broadcasting buffer overflow. Num : %d, (X:%d, Z:%d)",
                        v14,
                        this->m_cIndexX,
                        this->m_cIndexZ);
                      goto LABEL_41;
                    }
                  }
                  else if ( !BroadcastInfo::SerializeOutMonsterInfo(v18, MONSTER, NONE_PHASE, wr_ptr, &nBroadCastNum) )
                  {
                    CServerLog::DetailLog(
                      &g_Log,
                      LOG_ERROR,
                      "CCell::PrepareBroadCast",
                      aDWorkRylSource_30,
                      1103,
                      "Prepare cell broadcasting buffer overflow. Num : %d, (X:%d, Z:%d)",
                      v14,
                      this->m_cIndexX,
                      this->m_cIndexZ);
LABEL_41:
                    this->m_nBroadCastNum = v14;
                    return;
                  }
                }
                this->m_lpBroadCastBuffer->wr_ptr_ += nBroadCastNum;
                v15 = (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)LODWORD(v37);
              }
              Next = Next->_Next;
              ++v14;
              if ( Next == v15 )
                goto LABEL_41;
            }
          }
          this->m_lpBroadCastBuffer->wr_ptr_ += dwBufferSize;
        }
      }
      i = i->_Next;
    }
    v14 = nBroadCastNum;
    goto LABEL_22;
  }
}

//----- (004153B0) --------------------------------------------------------
void __thiscall CCell::SendCellInfo(CCell *this, CCharacter *lpCharacter)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // edi
  unsigned int v4; // ebp
  char *v5; // esi
  int v6; // eax
  CCell **m_lpConnectCell; // edx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v8; // ebx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *i; // edi
  CCharacter *Myval; // eax
  unsigned __int8 cStallNum; // [esp+7h] [ebp-Dh]
  CCell **lppCell; // [esp+8h] [ebp-Ch]
  CBuffer **lppCellPastEnd; // [esp+Ch] [ebp-8h]
  CGameClientDispatch *lpDispatch; // [esp+10h] [ebp-4h]
  char lpCharactera; // [esp+18h] [ebp+4h]

  if ( lpCharacter )
  {
    lpDispatch = lpCharacter->m_lpGameClientDispatch;
    if ( lpDispatch )
    {
      Myhead = this->m_lstItem._Myhead;
      Next = Myhead->_Next;
      v4 = 0;
      v5 = &szBuffer[14];
      lpCharactera = 0;
      cStallNum = 0;
      if ( Myhead->_Next != Myhead )
      {
        do
        {
          if ( v4 >= 0x3AA6 )
            break;
          *(_DWORD *)v5 = Next->_Myval.UID.Field.dwUID;
          *((_DWORD *)v5 + 1) = *(_DWORD *)&Next->_Myval.UID.Field.wMapIndex;
          *((_WORD *)v5 + 6) = (unsigned __int64)Next->_Myval.Pos.m_fPointX;
          *((_WORD *)v5 + 7) = (unsigned __int64)Next->_Myval.Pos.m_fPointY;
          *((_WORD *)v5 + 8) = (unsigned __int64)Next->_Myval.Pos.m_fPointZ;
          v5[18] = Next->_Myval.cNum;
          v6 = Next->_Myval.dwKindID ? Next->_Myval.dwKindID : Next->_Myval.lpItem->m_ItemData.m_usProtoTypeID;
          *((_DWORD *)v5 + 2) = v6;
          Next = Next->_Next;
          v5 += 19;
          v4 += 19;
          ++lpCharactera;
        }
        while ( Next != this->m_lstItem._Myhead );
      }
      m_lpConnectCell = this->m_lpConnectCell;
      lppCell = this->m_lpConnectCell;
      for ( lppCellPastEnd = &this->m_lpBroadCastBuffer;
            m_lpConnectCell != (CCell **)lppCellPastEnd;
            lppCell = m_lpConnectCell )
      {
        if ( v4 + 36 > 0x3AA6 )
          break;
        if ( *m_lpConnectCell )
        {
          v8 = (*m_lpConnectCell)->m_lstCharacter._Myhead;
          for ( i = v8->_Next; i != v8; i = i->_Next )
          {
            if ( v4 > 0x3A82 )
              break;
            Myval = i->_Myval;
            if ( Myval->m_Stall.m_strStallName[0] )
            {
              *(_DWORD *)v5 = Myval->m_dwCID;
              strncpy(v5 + 4, Myval->m_Stall.m_strStallName, 0x20u);
              m_lpConnectCell = lppCell;
              v5 += 36;
              v4 += 36;
              ++cStallNum;
            }
          }
        }
        ++m_lpConnectCell;
      }
      szBuffer[12] = lpCharactera;
      szBuffer[13] = cStallNum;
      CSendStream::WrapCompress(&lpDispatch->m_SendStream, szBuffer, (char *)(v4 + 14), 0x2Au, 0, 0);
    }
  }
}

//----- (00415520) --------------------------------------------------------
void __thiscall CCell::SendAllCharacter(
        CCell *this,
        char *szPacket,
        unsigned int dwPacketSize,
        unsigned __int8 cCMD_In)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // esi
  CSendStream *m_lpGameClientDispatch; // eax
  CCell *v7; // [esp+4h] [ebp-4h]

  Myhead = this->m_lstCharacter._Myhead;
  Next = Myhead->_Next;
  v7 = this;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      m_lpGameClientDispatch = (CSendStream *)Next->_Myval->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        CSendStream::PutBuffer(m_lpGameClientDispatch + 8, szPacket, dwPacketSize, cCMD_In);
        this = v7;
      }
      Next = Next->_Next;
    }
    while ( Next != this->m_lstCharacter._Myhead );
  }
}

//----- (00415570) --------------------------------------------------------
void __thiscall CCell::RespawnAllCharacter(CCell *this, unsigned int dwExcepGID)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // edi
  CCharacter *Myval; // esi
  Position v6[2]; // [esp-10h] [ebp-20h] BYREF

  if ( this->m_lstCharacter._Mysize )
  {
    Myhead = this->m_lstCharacter._Myhead;
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      do
      {
        Myval = Next->_Myval;
        if ( Myval )
        {
          if ( dwExcepGID != Myval->GetGID(Next->_Myval) )
          {
            memset(v6, 0, 12);
            CCharacter::Respawn(Myval, v6[0], 1);
          }
        }
        Next = Next->_Next;
      }
      while ( Next != this->m_lstCharacter._Myhead );
    }
  }
}

//----- (004155C0) --------------------------------------------------------
void __thiscall CCell::BroadCast(CCell *this)
{
  unsigned int v2; // esi
  CCell **m_lpConnectCell; // eax
  CBuffer **p_m_lpBroadCastBuffer; // ebx
  char *v5; // edi
  CCell *v6; // edx
  CCell *m_lpBroadCastBuffer; // ecx
  CCellManager *Instance; // eax
  CBuffer *BroadCastBuffer; // ebx
  CCellManager *v10; // eax
  CBuffer *BroadCastCompressBuffer; // eax
  CBuffer *v12; // edi
  char *internal_buffer; // eax
  char *v14; // ecx
  char v15; // dl
  CBuffer **v16; // eax
  CBuffer *v17; // ecx
  char *rd_ptr; // eax
  unsigned int v19; // edx
  unsigned int v20; // eax
  unsigned int v21; // eax
  unsigned int nRequiredBufferSize; // [esp+8h] [ebp-30h]
  CBuffer **nRequiredBufferSizea; // [esp+8h] [ebp-30h]
  CBuffer *lpCompressBuffer; // [esp+Ch] [ebp-2Ch]
  unsigned int nNearBroadCastNum; // [esp+10h] [ebp-28h]
  CBuffer *lpNearBuffers[9]; // [esp+14h] [ebp-24h] BYREF
  _UNKNOWN *retaddr; // [esp+38h] [ebp+0h] BYREF

  v2 = 0;
  if ( !this->m_lstCharacter._Mysize )
    return;
  m_lpConnectCell = this->m_lpConnectCell;
  p_m_lpBroadCastBuffer = &this->m_lpBroadCastBuffer;
  nRequiredBufferSize = 0;
  nNearBroadCastNum = 0;
  if ( this->m_lpConnectCell != (CCell **)&this->m_lpBroadCastBuffer )
  {
    v5 = (char *)((char *)lpNearBuffers - (char *)m_lpConnectCell);
    do
    {
      v6 = *m_lpConnectCell;
      if ( *m_lpConnectCell )
      {
        m_lpBroadCastBuffer = (CCell *)v6->m_lpBroadCastBuffer;
        v2 += v6->m_nBroadCastNum;
        *(CCell **)((char *)m_lpConnectCell + (_DWORD)v5) = m_lpBroadCastBuffer;
        if ( m_lpBroadCastBuffer )
          nRequiredBufferSize += m_lpBroadCastBuffer->m_lstItem._Mysize
                               - (unsigned int)m_lpBroadCastBuffer->m_lstItem._Myhead;
      }
      else
      {
        *(CCell **)((char *)m_lpConnectCell + (_DWORD)v5) = 0;
      }
      ++m_lpConnectCell;
    }
    while ( m_lpConnectCell != (CCell **)p_m_lpBroadCastBuffer );
    nNearBroadCastNum = v2;
  }
  Instance = CCellManager::GetInstance();
  BroadCastBuffer = CCellManager::GetBroadCastBuffer(Instance, nRequiredBufferSize + 15);
  v10 = CCellManager::GetInstance();
  BroadCastCompressBuffer = CCellManager::GetBroadCastCompressBuffer(v10, nRequiredBufferSize + 15);
  v12 = BroadCastCompressBuffer;
  lpCompressBuffer = BroadCastCompressBuffer;
  if ( !BroadCastBuffer || !BroadCastCompressBuffer )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCell::BroadCast",
      aDWorkRylSource_30,
      1183,
      "Broadcast buffer allocate (from CellManager) failed. Reserve size is : %u",
      nRequiredBufferSize);
    return;
  }
  internal_buffer = BroadCastCompressBuffer->internal_buffer_;
  v12->wr_ptr_ = v12->internal_buffer_;
  v12->rd_ptr_ = internal_buffer;
  v14 = BroadCastBuffer->internal_buffer_;
  BroadCastBuffer->wr_ptr_ = BroadCastBuffer->internal_buffer_;
  BroadCastBuffer->rd_ptr_ = v14;
  v15 = s_CharacterPhase[this->m_cBroadcastPhase];
  *(_WORD *)(v14 + 13) = nRequiredBufferSize;
  v14[12] = v15;
  BroadCastBuffer->wr_ptr_ += 15;
  v16 = lpNearBuffers;
  nRequiredBufferSizea = lpNearBuffers;
  while ( 1 )
  {
    v17 = *v16;
    if ( !*v16 )
      goto LABEL_17;
    rd_ptr = v17->rd_ptr_;
    v19 = v17->wr_ptr_ - rd_ptr;
    if ( !v19 )
      goto LABEL_17;
    if ( v19 + BroadCastBuffer->wr_ptr_ - BroadCastBuffer->rd_ptr_ >= 0x3BFF )
      break;
    qmemcpy(BroadCastBuffer->wr_ptr_, rd_ptr, v19);
    v2 = nNearBroadCastNum;
    v12 = lpCompressBuffer;
    BroadCastBuffer->wr_ptr_ += v19;
LABEL_17:
    v16 = ++nRequiredBufferSizea;
    if ( nRequiredBufferSizea == (CBuffer **)&retaddr )
      goto LABEL_20;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCell::BroadCast",
    aDWorkRylSource_30,
    1229,
    "Total cell broadcasting buffer is overflow(%uBytes). Current:%uBytes, (X:%d Z:%d), TotalBroadCastNum:%u, CurrentBroadCastNum:%u",
    15359,
    BroadCastBuffer->wr_ptr_ - BroadCastBuffer->rd_ptr_,
    this->m_cIndexX,
    this->m_cIndexZ,
    v2,
    this->m_nBroadCastNum);
  v12 = lpCompressBuffer;
LABEL_20:
  v20 = BroadCastBuffer->wr_ptr_ - BroadCastBuffer->rd_ptr_;
  if ( v20 > 0x2000 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CCell::BroadCast",
      aDWorkRylSource_30,
      1243,
      "Total cell broadcasting buffer is over than %uBytes. Current:%uBytes, (X:%d Z:%d), TotalBroadCastNum:%u, CurrentBroadCastNum:%u",
      0x2000,
      v20,
      this->m_cIndexX,
      this->m_cIndexZ,
      v2,
      this->m_nBroadCastNum);
    v12 = lpCompressBuffer;
  }
  if ( PacketWrap::WrapCompress(
         v12,
         BroadCastBuffer->rd_ptr_,
         LOWORD(BroadCastBuffer->wr_ptr_) - (unsigned int)BroadCastBuffer->rd_ptr_,
         0x49u,
         0,
         0) )
  {
    v21 = lpCompressBuffer->wr_ptr_ - lpCompressBuffer->rd_ptr_;
    if ( v21 > 0x2000 )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCell::BroadCast",
        aDWorkRylSource_30,
        1256,
        "Compressed cell broadcasting buffer is overflow(%uBytes). Current:%uBytes, (X:%d Z:%d), TotalBroadCastNum:%u, Cu"
        "rrentBroadCastNum:%u",
        0x2000,
        v21,
        this->m_cIndexX,
        this->m_cIndexZ,
        v2,
        this->m_nBroadCastNum);
    CCell::SendAllCharacter(
      this,
      lpCompressBuffer->rd_ptr_,
      lpCompressBuffer->wr_ptr_ - lpCompressBuffer->rd_ptr_,
      0x49u);
  }
}

//----- (00415830) --------------------------------------------------------
void __thiscall CCell::SendAllNearCellCharacter(
        CCell *this,
        char *szPacket,
        unsigned int dwPacketSize,
        unsigned __int8 cCMD_In)
{
  CBuffer **p_m_lpBroadCastBuffer; // edi
  CCell **i; // esi

  p_m_lpBroadCastBuffer = &this->m_lpBroadCastBuffer;
  for ( i = this->m_lpConnectCell; i != (CCell **)p_m_lpBroadCastBuffer; ++i )
  {
    if ( *i )
      CCell::SendAllCharacter(*i, szPacket, dwPacketSize, cCMD_In);
  }
}


//----- (00415870) --------------------------------------------------------
void __thiscall CCell::SendPullDownInfo(CCell *this, unsigned int dwPreOwnerID, CCell::ItemInfo *lpPullDownItem)
{
  double m_fPointX; // st7
  unsigned __int64 v5; // rax
  double m_fPointY; // st7
  unsigned __int64 v7; // rax
  double m_fPointZ; // st7
  CServerSetup *Instance; // eax
  unsigned int dwKindID; // eax
  int v11; // edx
  PktPDInfo pktPDInfo; // [esp+8h] [ebp-24h] BYREF

  if ( lpPullDownItem )
  {
    m_fPointX = lpPullDownItem->Pos.m_fPointX;
    pktPDInfo.m_dwCharID = dwPreOwnerID;
    v5 = (unsigned __int64)m_fPointX;
    m_fPointY = lpPullDownItem->Pos.m_fPointY;
    pktPDInfo.m_FieldObject.m_usXPos = v5;
    v7 = (unsigned __int64)m_fPointY;
    m_fPointZ = lpPullDownItem->Pos.m_fPointZ;
    pktPDInfo.m_FieldObject.m_usYPos = v7;
    pktPDInfo.m_FieldObject.m_usZPos = (unsigned __int64)m_fPointZ;
    Instance = CServerSetup::GetInstance();
    CServerSetup::GetServerZone(Instance);
    dwKindID = lpPullDownItem->dwKindID;
    v11 = *(_DWORD *)&lpPullDownItem->UID.Field.wMapIndex;
    LODWORD(pktPDInfo.m_FieldObject.m_nOID) = lpPullDownItem->UID.Field.dwUID;
    HIDWORD(pktPDInfo.m_FieldObject.m_nOID) = v11;
    if ( dwKindID )
    {
      if ( (dwKindID & 0x40000000) != 0 )
        dwKindID = (unsigned __int16)dwKindID;
      pktPDInfo.m_FieldObject.m_dwTypeID = dwKindID;
    }
    else
    {
      pktPDInfo.m_FieldObject.m_dwTypeID = lpPullDownItem->lpItem->m_ItemData.m_usProtoTypeID;
    }
    pktPDInfo.m_FieldObject.m_cNum = lpPullDownItem->cNum;
    if ( PacketWrap::WrapCrypt((char *)&pktPDInfo, 0x23u, 0x37u, 0, 0) )
      CCell::SendAllNearCellCharacter(this, (char *)&pktPDInfo, 0x23u, 0x37u);
  }
}

//----- (00415930) --------------------------------------------------------
void __thiscall CCell::SendPickUpInfo(CCell *this, unsigned int dwCreatureID, unsigned __int64 nItemInfoID)
{
  PktPUInfo pktPUInfo; // [esp+4h] [ebp-18h] BYREF

  pktPUInfo.m_dwCharID = dwCreatureID;
  pktPUInfo.m_nObjectID = nItemInfoID;
  if ( PacketWrap::WrapCrypt((char *)&pktPUInfo, 0x18u, 0x36u, 0, 0) )
    CCell::SendAllNearCellCharacter(this, (char *)&pktPUInfo, 0x18u, 0x36u);
}

//----- (00415980) --------------------------------------------------------
void __thiscall CCell::SendAttackInfo(
        CCell *this,
        unsigned int AttackerID_In,
        const AtType *AtType_In,
        unsigned __int8 DefenserNum_In,
        DefenserNode *lpNode_In)
{
  unsigned __int16 v6; // bx

  dword_523694 = AttackerID_In;
  dword_523698 = (int)*AtType_In;
  byte_52369C = DefenserNum_In;
  qmemcpy(&unk_52369D, lpNode_In, 15 * DefenserNum_In);
  v6 = 15 * DefenserNum_In + 21;
  if ( PacketWrap::WrapCrypt(szBuffer_0, v6, 0x30u, 0, 0) )
    CCell::SendAllNearCellCharacter(this, szBuffer_0, v6, 0x30u);
}
// 523694: using guessed type int dword_523694;
// 523698: using guessed type int dword_523698;
// 52369C: using guessed type char byte_52369C;

//----- (00415A00) --------------------------------------------------------
void __thiscall CCell::SendCastObjectInfo(
        CCell *this,
        unsigned int SenderID,
        unsigned int ReceiverID,
        CastObject *CastObject_In)
{
  float fPointX; // eax
  float fPointY; // ecx
  float fPointZ; // eax
  unsigned int m_dwTargetID; // ecx
  int v9; // edx
  PktCOInfo pktCOInfo; // [esp+4h] [ebp-28h] BYREF

  pktCOInfo.m_dwSenderID = SenderID;
  fPointX = CastObject_In->m_DstPos.fPointX;
  pktCOInfo.m_dwReceiverID = ReceiverID;
  fPointY = CastObject_In->m_DstPos.fPointY;
  pktCOInfo.m_sCastObject.m_DstPos.fPointX = fPointX;
  fPointZ = CastObject_In->m_DstPos.fPointZ;
  pktCOInfo.m_sCastObject.m_DstPos.fPointY = fPointY;
  m_dwTargetID = CastObject_In->m_dwTargetID;
  v9 = *(_DWORD *)&CastObject_In->m_wTypeID;
  pktCOInfo.m_sCastObject.m_DstPos.fPointZ = fPointZ;
  pktCOInfo.m_sCastObject.m_dwTargetID = m_dwTargetID;
  *(_DWORD *)&pktCOInfo.m_sCastObject.m_wTypeID = v9;
  if ( PacketWrap::WrapCrypt((char *)&pktCOInfo, 0x28u, 0x38u, 0, 0) )
    CCell::SendAllNearCellCharacter(this, (char *)&pktCOInfo, 0x28u, 0x38u);
}

//----- (00415A70) --------------------------------------------------------
void __thiscall CCell::KillAll(CCell *this, CCharacter *lpAttacker)
{
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // esi
  unsigned __int8 v4; // bl
  bool v5; // zf
  CMonster *Myval; // edi
  unsigned __int16 m_nNowHP; // bp
  CMonster **v8; // edx
  char *v9; // eax
  CMonster *v10; // edx
  CMonster *v11; // esi
  unsigned __int16 v12; // dx
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  int v14; // ecx
  CCell *m_lpCell; // ecx
  int nIndex; // [esp+4h] [ebp-E4h] BYREF
  unsigned __int8 cDefenderNum; // [esp+8h] [ebp-E0h]
  CMonster *lpDeadMonster[10]; // [esp+Ch] [ebp-DCh] BYREF
  char szBuffer[176]; // [esp+34h] [ebp-B4h] BYREF

  if ( this->m_lstMonster._Mysize )
  {
    Myhead = this->m_lstMonster._Myhead;
    Next = Myhead->_Next;
    v4 = 0;
    v5 = Myhead->_Next == Myhead;
    memset(lpDeadMonster, 0, sizeof(lpDeadMonster));
    cDefenderNum = 0;
    if ( !v5 )
    {
      do
      {
        if ( v4 >= 0xAu )
          break;
        Myval = Next->_Myval;
        m_nNowHP = Myval->m_CreatureStatus.m_nNowHP;
        v8 = &lpDeadMonster[v4];
        Myval->m_CreatureStatus.m_nNowHP = 0;
        v9 = &szBuffer[15 * v4 + 26];
        *v8 = Myval;
        *((_WORD *)v9 + 6) = m_nNowHP;
        *(_DWORD *)v9 = Myval->m_dwCID;
        *((_WORD *)v9 + 3) = (*v8)->m_CreatureStatus.m_nNowHP;
        *((_WORD *)v9 + 5) = (*v8)->m_CreatureStatus.m_nNowMP;
        LOWORD(Myval) = (*v8)->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        v10 = *v8;
        *((_WORD *)v9 + 2) = (_WORD)Myval;
        *((_WORD *)v9 + 4) = v10->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
        v9[14] = 0;
        Next = Next->_Next;
        ++v4;
      }
      while ( Next != this->m_lstMonster._Myhead );
      cDefenderNum = v4;
    }
    nIndex = 0;
    if ( v4 )
    {
      do
      {
        v11 = lpDeadMonster[nIndex];
        if ( !v11 )
          break;
        CThreat::AddToThreatList(&v11->m_Threat, lpAttacker, 1);
        v11->Dead(v11, lpAttacker);
        CThreat::ClearAll(&v11->m_Threat);
        ++nIndex;
      }
      while ( nIndex < v4 );
    }
    v12 = lpAttacker->m_CreatureStatus.m_nNowHP;
    *(_DWORD *)&szBuffer[12] = lpAttacker->m_dwCID;
    *(_WORD *)&szBuffer[22] = lpAttacker->m_CreatureStatus.m_nNowMP;
    m_lpGameClientDispatch = lpAttacker->m_lpGameClientDispatch;
    LOWORD(nIndex) = 0;
    HIWORD(v14) = HIWORD(nIndex);
    *(_DWORD *)&szBuffer[16] = nIndex;
    *(_WORD *)&szBuffer[20] = v12;
    szBuffer[24] = 0;
    szBuffer[25] = v4;
    if ( m_lpGameClientDispatch )
    {
      LOWORD(v14) = v4;
      if ( CSendStream::WrapCompress(
             &m_lpGameClientDispatch->m_SendStream,
             szBuffer,
             (char *)(15 * v14 + 26),
             0xEu,
             0,
             0) == 1 )
      {
        m_lpCell = lpAttacker->m_CellPos.m_lpCell;
        if ( m_lpCell )
          CCell::SendAttackInfo(
            m_lpCell,
            lpAttacker->m_dwCID,
            (const AtType *)&nIndex,
            cDefenderNum,
            (DefenserNode *)&szBuffer[26]);
      }
    }
  }
}

//----- (00415C40) --------------------------------------------------------
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
        std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( -1 - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00415CE0) --------------------------------------------------------
boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance()
{
  if ( (__S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
  {
    __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
    InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 56;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
    atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
  }
  return &`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj;
}
// 523764: using guessed type int __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (00415D40) --------------------------------------------------------
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(
        void **ptr)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // esi

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  *ptr = v1->p.first;
  v1->p.first = ptr;
  LeaveCriticalSection(&v1->mtx);
}

//----- (00415D70) --------------------------------------------------------
void __thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ecx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  bool v4; // zf
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v5; // edi

  Myhead = this->_Myhead;
  Next = Myhead->_Next;
  Myhead->_Next = Myhead;
  this->_Myhead->_Prev = this->_Myhead;
  v4 = Next == this->_Myhead;
  this->_Mysize = 0;
  if ( !v4 )
  {
    do
    {
      v5 = Next->_Next;
      boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&Next->_Next);
      Next = v5;
    }
    while ( v5 != this->_Myhead );
  }
}

//----- (00415DB0) --------------------------------------------------------
std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Where)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // edi
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v5; // eax

  Next = _Where._Ptr->_Next;
  if ( _Where._Ptr != this->_Myhead )
  {
    _Where._Ptr->_Prev->_Next = _Where._Ptr->_Next;
    _Where._Ptr->_Next->_Prev = _Where._Ptr->_Prev;
    boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&_Where._Ptr->_Next);
    --this->_Mysize;
  }
  v5 = result;
  result->_Ptr = Next;
  return v5;
}

//----- (00415DF0) --------------------------------------------------------
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ecx
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  bool v4; // zf
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v5; // edi

  Myhead = this->_Myhead;
  Next = Myhead->_Next;
  Myhead->_Next = Myhead;
  this->_Myhead->_Prev = this->_Myhead;
  v4 = Next == this->_Myhead;
  this->_Mysize = 0;
  if ( !v4 )
  {
    do
    {
      v5 = Next->_Next;
      boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&Next->_Next);
      Next = v5;
    }
    while ( v5 != this->_Myhead );
  }
}

//----- (00415E30) --------------------------------------------------------
std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Where)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // edi
  std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v5; // eax

  Next = _Where._Ptr->_Next;
  if ( _Where._Ptr != this->_Myhead )
  {
    _Where._Ptr->_Prev->_Next = _Where._Ptr->_Next;
    _Where._Ptr->_Next->_Prev = _Where._Ptr->_Prev;
    boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&_Where._Ptr->_Next);
    --this->_Mysize;
  }
  v5 = result;
  result->_Ptr = (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)Next;
  return v5;
}

//----- (00415E70) --------------------------------------------------------
std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  char *what; // [esp+8h] [ebp-10h] BYREF
  exception pExceptionObject; // [esp+Ch] [ebp-Ch] BYREF

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  first = (void **)v1->p.first;
  p_p = &v1->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v1->mtx);
  if ( !first )
  {
    what = "bad allocation";
    exception::exception(&pExceptionObject, (const char **)&what);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = first;
  if ( first != (void **)-4 )
    first[1] = first;
  return (std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00415EF0) --------------------------------------------------------
std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  char *what; // [esp+8h] [ebp-10h] BYREF
  exception pExceptionObject; // [esp+Ch] [ebp-Ch] BYREF

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  first = (void **)v1->p.first;
  p_p = &v1->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v1->mtx);
  if ( !first )
  {
    what = "bad allocation";
    exception::exception(&pExceptionObject, (const char **)&what);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = first;
  if ( first != (void **)-4 )
    first[1] = first;
  return (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00415F70) --------------------------------------------------------
std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Next,
        std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Prev,
        const CCell::ItemInfo *_Val)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v4; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v7; // ecx
  const CCell::ItemInfo *v8; // eax
  exception pExceptionObject; // [esp+8h] [ebp-Ch] BYREF

  v4 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v4->mtx);
  first = (void **)v4->p.first;
  p_p = &v4->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v4->mtx);
  if ( !first )
  {
    _Next = (std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)"bad allocation";
    exception::exception(&pExceptionObject, (const char **)&_Next);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  v7 = _Prev;
  *first = _Next;
  v8 = _Val;
  first[1] = v7;
  first[2] = (void *)LODWORD(v8->Pos.m_fPointX);
  first[3] = (void *)LODWORD(v8->Pos.m_fPointY);
  first[4] = (void *)LODWORD(v8->Pos.m_fPointZ);
  *(($5E149B4D9F4FA959233837F62A6C99EA *)first + 3) = ($5E149B4D9F4FA959233837F62A6C99EA)v8->UID.nUniqueID;
  first[8] = (void *)v8->dwKindID;
  first[9] = (void *)v8->dwOwnerID;
  *((_WORD *)first + 20) = v8->wPulse;
  first[11] = v8->lpItem;
  *((_BYTE *)first + 48) = v8->cNum;
  return (std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00416030) --------------------------------------------------------
void __thiscall CCell::DeleteCreature(
        CCell *this,
        signed int dwCID,
        std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator eCellMoveType)
{
  signed int v3; // edi
  unsigned __int8 v5; // al
  int v6; // eax

  v3 = dwCID;
  if ( dwCID >= 0 )
  {
    if ( (dwCID & 0x40000000) != 0 )
      v5 = 1;
    else
      v5 = (dwCID & 0x10000000) != 0 ? 5 : 0;
  }
  else
  {
    v5 = 2;
  }
  if ( v5 )
  {
    v6 = v5 - 2;
    if ( v6 )
    {
      if ( v6 == 3 )
      {
        std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
          (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
          (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstSiegeObject._Myhead->_Next,
          (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstSiegeObject._Myhead,
          (CFindCreatureFromCID)dwCID);
        if ( (std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID == this->m_lstSiegeObject._Myhead )
          CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::DeleteCreature", aDWorkRylSource_30, 538, byte_4D7750, v3);
        else
          std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
            (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)&this->m_lstSiegeObject,
            (std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
            (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)dwCID);
      }
    }
    else
    {
      std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
        (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
        (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstMonster._Myhead->_Next,
        (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstMonster._Myhead,
        (CFindCreatureFromCID)dwCID);
      if ( (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID == this->m_lstMonster._Myhead )
        CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::DeleteCreature", aDWorkRylSource_30, 523, byte_4D7718, v3);
      else
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
          (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)&this->m_lstMonster,
          (std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
          (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)dwCID);
    }
  }
  else
  {
    std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(
      (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstCharacter._Myhead->_Next,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)this->m_lstCharacter._Myhead,
      (CFindCreatureFromCID)dwCID);
    if ( (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)dwCID == this->m_lstCharacter._Myhead )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCell::DeleteCreature",
        aDWorkRylSource_30,
        508,
        (char *)&byte_4D76E0,
        v3);
    else
      std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
        &this->m_lstCharacter,
        (std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwCID,
        (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)dwCID);
  }
}

//----- (00416150) --------------------------------------------------------
CCell::ErrorCode __thiscall CCell::GetItem(
        CCell *this,
        unsigned int dwCreatureID,
        CFindItemInfoFromUID nItemInfoID,
        Item::CItem **lppItem,
        unsigned int *dwMoney_Out)
{
  CCell *v5; // ebx
  CCreatureManager *Instance; // eax
  CAggresiveCreature *AggresiveCreature; // ebp
  unsigned int Mysize; // eax
  CCell::ErrorCode result; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Ptr; // edi
  unsigned int dwOwnerID; // eax
  CCreatureManager *v12; // eax
  CMsgProc *v13; // eax
  int v14; // esi
  Item::CItem *lpItem; // esi
  int dwKindID; // eax
  Item::CEquipment *v17; // eax
  Item::CEquipment *v18; // eax
  Item::CItem *v19; // eax
  const Item::ItemInfo *m_ItemInfo; // eax
  char *v21; // ebp
  int v22; // eax
  unsigned int v23; // ebx
  CCreatureManager *v24; // eax
  int v25; // eax
  unsigned int v26; // edx
  unsigned int v27; // [esp+0h] [ebp-F0h]
  unsigned __int16 v28; // [esp+0h] [ebp-F0h]
  CCell::ErrorCode eErrorCode; // [esp+14h] [ebp-DCh]
  unsigned int dwOwnerID_Out; // [esp+18h] [ebp-D8h] BYREF
  CAggresiveCreature *lpPickkingCreature; // [esp+1Ch] [ebp-D4h]
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator itr; // [esp+20h] [ebp-D0h] BYREF
  char szBuffer[198]; // [esp+24h] [ebp-CCh] BYREF

  v5 = this;
  *lppItem = 0;
  dwOwnerID_Out = (unsigned int)this;
  *dwMoney_Out = 0;
  eErrorCode = S_SUCCESS;
  Instance = CCreatureManager::GetInstance();
  AggresiveCreature = (CAggresiveCreature *)CCreatureManager::GetAggresiveCreature(Instance, dwCreatureID);
  Mysize = v5->m_lstItem._Mysize;
  lpPickkingCreature = AggresiveCreature;
  if ( !Mysize )
    return 3;
  std::find_if<std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindItemInfoFromUID>(
    &itr,
    (std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)v5->m_lstItem._Myhead->_Next,
    (std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)v5->m_lstItem._Myhead,
    nItemInfoID);
  Ptr = itr._Ptr;
  if ( itr._Ptr == v5->m_lstItem._Myhead )
    return 3;
  dwOwnerID = itr._Ptr->_Myval.dwOwnerID;
  if ( dwOwnerID )
  {
    if ( dwCreatureID != dwOwnerID )
    {
      if ( itr._Ptr->_Myval.lpItem
        || (v27 = itr._Ptr->_Myval.dwOwnerID,
            v12 = CCreatureManager::GetInstance(),
            (v13 = CCreatureManager::GetAggresiveCreature(v12, v27)) != 0)
        && AggresiveCreature
        && ((v14 = ((int (__thiscall *)(CMsgProc *))v13->__vftable[2].~CMsgProc)(v13)) == 0
         || (CParty *)v14 != AggresiveCreature->GetParty(AggresiveCreature)) )
      {
        eErrorCode = E_GET_EQUIP_FAILED;
      }
    }
  }
  result = eErrorCode;
  lpItem = 0;
  if ( eErrorCode == S_SUCCESS )
  {
    dwKindID = Ptr->_Myval.dwKindID;
    if ( !dwKindID )
    {
      lpItem = Ptr->_Myval.lpItem;
      if ( (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 && !lpItem->m_ItemData.m_cNumOrDurability )
        return 3;
LABEL_37:
      *lppItem = lpItem;
      std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(
        &v5->m_lstItem,
        (std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *)&dwOwnerID_Out,
        (std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)Ptr);
      CCell::SendPickUpInfo(v5, dwCreatureID, nItemInfoID.m_nUID);
      return eErrorCode;
    }
    if ( dwKindID < 0 )
    {
      *dwMoney_Out = dwKindID & 0xFFFFFFF;
      goto LABEL_33;
    }
    v28 = Ptr->_Myval.dwKindID;
    if ( (dwKindID & 0x40000000) != 0 )
    {
      Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v28);
      lpItem = v17;
      v18 = Item::CEquipment::DowncastToEquipment(v17);
      if ( v18 )
      {
        Item::CEquipment::AddRandomOption(v18, BYTE2(Ptr->_Myval.dwKindID), 0);
        goto LABEL_27;
      }
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::GetItem", aDWorkRylSource_30, 765, (char *)&byte_4D783C);
    }
    else
    {
      Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v28);
      lpItem = v19;
      if ( v19 )
      {
        v19->m_ItemData.m_cNumOrDurability = Ptr->_Myval.cNum;
        goto LABEL_27;
      }
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::GetItem", aDWorkRylSource_30, 780, byte_4D7808);
    }
    eErrorCode = E_NOT_EQUIP;
LABEL_27:
    if ( lpItem )
    {
      if ( !Creature::GetCreatureType(AggresiveCreature->m_dwCID) )
      {
        m_ItemInfo = lpItem->m_ItemInfo;
        if ( m_ItemInfo->m_DetailData.m_cItemType == 48 )
        {
          v21 = (char *)&AggresiveCreature[2].m_SpellMgr.m_AffectedInfo.m_pEnchant[1];
          v22 = _snprintf(&szBuffer[18], 0xB3u, asc_4D77D0, v21, &m_ItemInfo->m_SpriteData);
          v23 = v22 + 19;
          szBuffer[197] = 0;
          *(_DWORD *)&szBuffer[12] = 0;
          *(_WORD *)&szBuffer[16] = 255;
          if ( PacketWrap::WrapCrypt(szBuffer, v22 + 19, 0xDu, 0, 0) == 1 )
          {
            v24 = CCreatureManager::GetInstance();
            CCreatureManager::SendAllCharacter(v24, szBuffer, v23, 0xDu, 1);
          }
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CCell::GetItem",
            aDWorkRylSource_30,
            814,
            aCid0x08x_59,
            lpPickkingCreature->m_dwCID,
            v21,
            &lpItem->m_ItemInfo->m_SpriteData);
          AggresiveCreature = lpPickkingCreature;
          v5 = (CCell *)dwOwnerID_Out;
        }
      }
    }
LABEL_33:
    v25 = (int)AggresiveCreature->GetParty(AggresiveCreature);
    if ( v25
      && (*(unsigned __int8 (__thiscall **)(int, CAggresiveCreature *, unsigned int, _DWORD, Item::CItem *, unsigned int *, unsigned int *))(*(_DWORD *)v25 + 52))(
           v25,
           AggresiveCreature,
           Ptr->_Myval.UID.Field.dwUID,
           *(_DWORD *)&Ptr->_Myval.UID.Field.wMapIndex,
           lpItem,
           dwMoney_Out,
           &dwOwnerID_Out) == 1 )
    {
      v26 = dwOwnerID_Out;
      Ptr->_Myval.dwKindID = 0;
      Ptr->_Myval.dwOwnerID = v26;
      Ptr->_Myval.lpItem = lpItem;
      return 5;
    }
    if ( eErrorCode )
      return eErrorCode;
    goto LABEL_37;
  }
  return result;
}
// 4162B2: variable 'v17' is possibly undefined
// 416304: variable 'v19' is possibly undefined

//----- (00416490) --------------------------------------------------------
void __thiscall CCell::CheckDeleteItem(CCell *this)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // esi
  unsigned __int16 wPulse; // ax
  unsigned int dwUID; // eax
  int v6; // ecx
  CCell **i; // edi
  Item::CItem *lpItem; // ecx
  void **p_Next; // eax
  bool v10; // zf
  char Buffer_In[12]; // [esp+8h] [ebp-18h] BYREF
  unsigned int v12; // [esp+14h] [ebp-Ch]
  int v13; // [esp+18h] [ebp-8h]
  int v14; // [esp+1Ch] [ebp-4h]

  if ( this->m_lstItem._Mysize )
  {
    Myhead = this->m_lstItem._Myhead;
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      do
      {
        Next->_Myval.wPulse -= 100;
        wPulse = Next->_Myval.wPulse;
        if ( wPulse <= 0x5DCu )
          Next->_Myval.dwOwnerID = 0;
        if ( wPulse )
        {
          Next = Next->_Next;
        }
        else
        {
          dwUID = Next->_Myval.UID.Field.dwUID;
          v6 = *(_DWORD *)&Next->_Myval.UID.Field.wMapIndex;
          v14 = 0;
          v12 = dwUID;
          v13 = v6;
          if ( PacketWrap::WrapCrypt(Buffer_In, 0x18u, 0x36u, 0, 0) )
          {
            for ( i = this->m_lpConnectCell; i != (CCell **)&this->m_lpBroadCastBuffer; ++i )
            {
              if ( *i )
                CCell::SendAllCharacter(*i, Buffer_In, 0x18u, 0x36u);
            }
          }
          if ( !Next->_Myval.dwKindID )
          {
            lpItem = Next->_Myval.lpItem;
            if ( lpItem )
            {
              ((void (__thiscall *)(Item::CItem *, int))lpItem->~Item::CItem)(lpItem, 1);
              Next->_Myval.lpItem = 0;
            }
          }
          p_Next = (void **)&Next->_Next;
          v10 = Next == this->m_lstItem._Myhead;
          Next = Next->_Next;
          if ( !v10 )
          {
            *(_DWORD *)p_Next[1] = *p_Next;
            *((_DWORD *)*p_Next + 1) = p_Next[1];
            boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(p_Next);
            --this->m_lstItem._Mysize;
          }
        }
      }
      while ( Next != this->m_lstItem._Myhead );
    }
  }
}

//----- (00416590) --------------------------------------------------------
void __thiscall CCell::DeleteAllItem(CCell *this)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // esi
  unsigned int dwUID; // eax
  int v5; // ecx
  CCell **i; // edi
  Item::CItem *lpItem; // ecx
  void **p_Next; // eax
  bool v9; // zf
  char Buffer_In[12]; // [esp+8h] [ebp-18h] BYREF
  unsigned int v11; // [esp+14h] [ebp-Ch]
  int v12; // [esp+18h] [ebp-8h]
  int v13; // [esp+1Ch] [ebp-4h]

  if ( this->m_lstItem._Mysize )
  {
    Myhead = this->m_lstItem._Myhead;
    Next = Myhead->_Next;
    if ( Myhead->_Next != Myhead )
    {
      do
      {
        dwUID = Next->_Myval.UID.Field.dwUID;
        v5 = *(_DWORD *)&Next->_Myval.UID.Field.wMapIndex;
        Next->_Myval.dwOwnerID = 0;
        v13 = 0;
        v11 = dwUID;
        v12 = v5;
        if ( PacketWrap::WrapCrypt(Buffer_In, 0x18u, 0x36u, 0, 0) )
        {
          for ( i = this->m_lpConnectCell; i != (CCell **)&this->m_lpBroadCastBuffer; ++i )
          {
            if ( *i )
              CCell::SendAllCharacter(*i, Buffer_In, 0x18u, 0x36u);
          }
        }
        if ( !Next->_Myval.dwKindID )
        {
          lpItem = Next->_Myval.lpItem;
          if ( lpItem )
          {
            ((void (__thiscall *)(Item::CItem *, int))lpItem->~Item::CItem)(lpItem, 1);
            Next->_Myval.lpItem = 0;
          }
        }
        p_Next = (void **)&Next->_Next;
        v9 = Next == this->m_lstItem._Myhead;
        Next = Next->_Next;
        if ( !v9 )
        {
          *(_DWORD *)p_Next[1] = *p_Next;
          *((_DWORD *)*p_Next + 1) = p_Next[1];
          boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(p_Next);
          --this->m_lstItem._Mysize;
        }
      }
      while ( Next != this->m_lstItem._Myhead );
    }
  }
}

//----- (00416660) --------------------------------------------------------
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(this);
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&this->_Myhead->_Next);
  this->_Myhead = 0;
}

//----- (00416680) --------------------------------------------------------
void __thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this)
{
  std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(this);
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&this->_Myhead->_Next);
  this->_Myhead = 0;
}

//----- (004166A0) --------------------------------------------------------
void __thiscall CCell::CCell(CCell *this)
{
  this->m_lstItem._Myhead = std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(&this->m_lstItem);
  this->m_lstItem._Mysize = 0;
  this->m_lstMonster._Myhead = (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode((std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)&this->m_lstMonster);
  this->m_lstMonster._Mysize = 0;
  this->m_lstCharacter._Myhead = std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(&this->m_lstCharacter);
  this->m_lstCharacter._Mysize = 0;
  this->m_lstSiegeObject._Myhead = (std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode((std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)&this->m_lstSiegeObject);
  this->m_lstSiegeObject._Mysize = 0;
  this->m_MonsterIt._Ptr = this->m_lstMonster._Myhead;
  this->m_CharacterIt._Ptr = this->m_lstCharacter._Myhead;
  this->m_SiegeObjectIt._Ptr = this->m_lstSiegeObject._Myhead;
  this->m_lpBroadCastBuffer = 0;
  this->m_nBroadCastNum = 0;
  this->m_wMapIndex = 0;
  this->m_cIndexX = 0;
  this->m_cIndexZ = 0;
  this->m_dwItemUIDCount = 0;
  this->m_cBroadcastPhase = 0;
  this->m_cTurnOfGetAggresiveCreature = 0;
  this->m_lpConnectCell[0] = 0;
  this->m_lpConnectCell[1] = 0;
  this->m_lpConnectCell[2] = 0;
  this->m_lpConnectCell[3] = 0;
  this->m_lpConnectCell[4] = 0;
  this->m_lpConnectCell[5] = 0;
  this->m_lpConnectCell[6] = 0;
  this->m_lpConnectCell[7] = 0;
  this->m_lpConnectCell[8] = 0;
  this->m_lpConnectCell[0] = this;
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(&this->m_lstItem);
}

//----- (00416780) --------------------------------------------------------
void __thiscall CCell::~CCell(CCell *this)
{
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // ebx
  Item::CItem *lpItem; // ecx
  CBuffer *m_lpBroadCastBuffer; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v6; // eax
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v7; // ebx
  bool v8; // zf
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v9; // ebx
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v10; // eax
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v11; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v12; // ebx
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v13; // eax
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v14; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v15; // ebx
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v16; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v17; // ebx
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v18; // ebx
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v19; // [esp+10h] [ebp-10h]
  std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v20; // [esp+10h] [ebp-10h]
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v21; // [esp+10h] [ebp-10h]
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v22; // [esp+10h] [ebp-10h]
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v23; // [esp+10h] [ebp-10h]
  std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v24; // [esp+10h] [ebp-10h]
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v25; // [esp+10h] [ebp-10h]
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v26; // [esp+10h] [ebp-10h]

  Myhead = this->m_lstItem._Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      if ( !Next->_Myval.dwKindID )
      {
        lpItem = Next->_Myval.lpItem;
        if ( lpItem )
          ((void (__thiscall *)(Item::CItem *, int))lpItem->~Item::CItem)(lpItem, 1);
      }
      Next = Next->_Next;
    }
    while ( Next != this->m_lstItem._Myhead );
  }
  m_lpBroadCastBuffer = this->m_lpBroadCastBuffer;
  if ( m_lpBroadCastBuffer )
  {
    m_lpBroadCastBuffer->bufferfactory_->Release(m_lpBroadCastBuffer->bufferfactory_, this->m_lpBroadCastBuffer);
    this->m_lpBroadCastBuffer = 0;
  }
  v6 = this->m_lstSiegeObject._Myhead;
  v7 = v6->_Next;
  v6->_Next = v6;
  this->m_lstSiegeObject._Myhead->_Prev = this->m_lstSiegeObject._Myhead;
  v8 = v7 == this->m_lstSiegeObject._Myhead;
  this->m_lstSiegeObject._Mysize = 0;
  if ( !v8 )
  {
    do
    {
      v19 = v7->_Next;
      if ( (__S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
      {
        __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
        InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 12;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
        atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
      }
      EnterCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v7->_Next = (std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = v7;
      LeaveCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v7 = v19;
    }
    while ( v19 != this->m_lstSiegeObject._Myhead );
  }
  v20 = this->m_lstSiegeObject._Myhead;
  v9 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v9->mtx);
  v20->_Next = (std::_List_nod<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v9->p.first;
  v9->p.first = v20;
  LeaveCriticalSection(&v9->mtx);
  this->m_lstSiegeObject._Myhead = 0;
  v10 = this->m_lstCharacter._Myhead;
  v11 = v10->_Next;
  v10->_Next = v10;
  this->m_lstCharacter._Myhead->_Prev = this->m_lstCharacter._Myhead;
  v8 = v11 == this->m_lstCharacter._Myhead;
  this->m_lstCharacter._Mysize = 0;
  if ( !v8 )
  {
    do
    {
      v21 = v11->_Next;
      if ( (__S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
      {
        __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
        InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 12;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
        atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
      }
      EnterCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v11->_Next = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = v11;
      LeaveCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v11 = v21;
    }
    while ( v21 != this->m_lstCharacter._Myhead );
  }
  v22 = this->m_lstCharacter._Myhead;
  v12 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v12->mtx);
  v22->_Next = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v12->p.first;
  v12->p.first = v22;
  LeaveCriticalSection(&v12->mtx);
  this->m_lstCharacter._Myhead = 0;
  v13 = this->m_lstMonster._Myhead;
  v14 = v13->_Next;
  v13->_Next = v13;
  this->m_lstMonster._Myhead->_Prev = this->m_lstMonster._Myhead;
  v8 = v14 == this->m_lstMonster._Myhead;
  this->m_lstMonster._Mysize = 0;
  if ( !v8 )
  {
    do
    {
      v23 = v14->_Next;
      if ( (__S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
      {
        __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
        InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 12;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
        atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
      }
      EnterCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v14->_Next = (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = v14;
      LeaveCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v14 = v23;
    }
    while ( v23 != this->m_lstMonster._Myhead );
  }
  v24 = this->m_lstMonster._Myhead;
  v15 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v15->mtx);
  v24->_Next = (std::_List_nod<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v15->p.first;
  v15->p.first = v24;
  LeaveCriticalSection(&v15->mtx);
  this->m_lstMonster._Myhead = 0;
  v16 = this->m_lstItem._Myhead;
  v17 = v16->_Next;
  v16->_Next = v16;
  this->m_lstItem._Myhead->_Prev = this->m_lstItem._Myhead;
  v8 = v17 == this->m_lstItem._Myhead;
  this->m_lstItem._Mysize = 0;
  if ( !v8 )
  {
    do
    {
      v25 = v17->_Next;
      if ( (__S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
      {
        __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
        InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 56;
        `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
        atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
      }
      EnterCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v17->_Next = (std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first;
      `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = v17;
      LeaveCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
      v17 = v25;
    }
    while ( v25 != this->m_lstItem._Myhead );
  }
  v26 = this->m_lstItem._Myhead;
  v18 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v18->mtx);
  v26->_Next = (std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)v18->p.first;
  v18->p.first = v26;
  LeaveCriticalSection(&v18->mtx);
  this->m_lstItem._Myhead = 0;
}
// 523764: using guessed type int __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;
// 523768: using guessed type int __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (00416B30) --------------------------------------------------------
void __thiscall std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(
        std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        CCharacter *const *_Val)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // edi
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v4; // ebx

  Myhead = (std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *)this->_Myhead;
  v4 = std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
         (std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)this,
         Myhead,
         Myhead->_Prev,
         _Val);
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
    this,
    1u);
  Myhead->_Prev = v4;
  v4->_Prev->_Next = v4;
}

//----- (00416B70) --------------------------------------------------------
void __thiscall CCell::SetCreature(
        CCell *this,
        signed int dwCID,
        CCharacter *lpCreature,
        CCell::CellMoveType eCellMoveType)
{
  unsigned __int8 v5; // al
  int v6; // eax
  CCharacter *v7; // edi

  if ( CCell::GetCreature(this, dwCID) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::SetCreature", aDWorkRylSource_30, 386, (char *)&byte_4D7874, dwCID);
  }
  else
  {
    if ( dwCID >= 0 )
    {
      if ( (dwCID & 0x40000000) != 0 )
        v5 = 1;
      else
        v5 = (dwCID & 0x10000000) != 0 ? 5 : 0;
    }
    else
    {
      v5 = 2;
    }
    if ( v5 )
    {
      v6 = v5 - 2;
      if ( v6 )
      {
        if ( v6 == 3 )
          std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(
            &this->m_lstSiegeObject,
            &lpCreature);
      }
      else
      {
        std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(
          (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)&this->m_lstMonster,
          &lpCreature);
      }
    }
    else
    {
      v7 = lpCreature;
      std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(
        &this->m_lstCharacter,
        &lpCreature);
      if ( this->m_lstItem._Mysize || this->m_lstCharacter._Mysize )
        CCell::SendCellInfo(this, v7);
    }
  }
}

//----- (00416C50) --------------------------------------------------------
char __thiscall CCell::SetItem(
        CCell *this,
        Item::CItem *lpItemBase,
        const Position *Pos,
        CCell::ItemInfo *lpCellItemInfo,
        unsigned int dwPreOwnerID,
        unsigned int dwOwnerID,
        unsigned int dwKindID,
        unsigned __int8 cItemNum,
        bool bAutoRouting)
{
  CCell::ItemInfo *p_Local_ItemInfo; // esi
  unsigned int m_dwItemUIDCount; // eax
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Myhead; // ebp
  unsigned int v14; // ecx
  unsigned int i; // edx
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *Next; // eax
  const Item::ItemInfo *ItemInfo; // eax
  char v18; // al
  unsigned __int8 m_cMaxDurabilityOrStack; // dl
  char *p_m_cMaxDurabilityOrStack; // eax
  unsigned __int8 m_cNumOrDurability; // cl
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v22; // ebp
  std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *v23; // ebx
  bool v24; // zf
  CCell::ItemInfo Local_ItemInfo; // [esp+Ch] [ebp-30h] BYREF

  p_Local_ItemInfo = lpCellItemInfo;
  memset(&Local_ItemInfo, 0, 12);
  Local_ItemInfo.cNum = 0;
  memset(&Local_ItemInfo.UID, 0, 18);
  Local_ItemInfo.lpItem = 0;
  if ( !lpCellItemInfo )
    p_Local_ItemInfo = &Local_ItemInfo;
  if ( this->m_lstItem._Mysize >= 0xFF )
  {
    CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCell::SetItem", aDWorkRylSource_30, 580, (char *)&byte_4D78DC);
    return 0;
  }
  m_dwItemUIDCount = this->m_dwItemUIDCount;
  Myhead = this->m_lstItem._Myhead;
  v14 = m_dwItemUIDCount + 1;
  for ( i = m_dwItemUIDCount; ; i = v14 )
  {
    this->m_dwItemUIDCount = v14;
    Next = Myhead->_Next;
    if ( Myhead == Myhead->_Next )
      break;
    do
    {
      if ( i == Next->_Myval.UID.Field.dwUID && !*(_DWORD *)&Next->_Myval.UID.Field.wMapIndex )
        break;
      Next = Next->_Next;
    }
    while ( Next != Myhead );
    if ( Myhead == Next )
      break;
    ++v14;
  }
  p_Local_ItemInfo->UID.Field.dwUID = v14;
  p_Local_ItemInfo->UID.Field.wMapIndex = this->m_wMapIndex;
  p_Local_ItemInfo->UID.Field.cCellX = this->m_cIndexX;
  p_Local_ItemInfo->UID.Field.cCellZ = this->m_cIndexZ;
  p_Local_ItemInfo->dwKindID = dwKindID;
  ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, dwKindID);
  if ( ItemInfo )
  {
    m_cMaxDurabilityOrStack = ItemInfo->m_DetailData.m_cMaxDurabilityOrStack;
    p_m_cMaxDurabilityOrStack = (char *)&ItemInfo->m_DetailData.m_cMaxDurabilityOrStack;
    if ( cItemNum < m_cMaxDurabilityOrStack )
      p_m_cMaxDurabilityOrStack = (char *)&cItemNum;
    v18 = *p_m_cMaxDurabilityOrStack;
  }
  else
  {
    v18 = 1;
  }
  p_Local_ItemInfo->cNum = v18;
  if ( dwKindID )
  {
    p_Local_ItemInfo->dwOwnerID = dwOwnerID;
    p_Local_ItemInfo->wPulse = 1800;
    p_Local_ItemInfo->lpItem = 0;
  }
  else
  {
    if ( !lpItemBase )
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCell::SetItem", aDWorkRylSource_30, 607, (char *)&byte_4D78A8);
      return 0;
    }
    m_cNumOrDurability = lpItemBase->m_ItemData.m_cNumOrDurability;
    p_Local_ItemInfo->dwOwnerID = dwPreOwnerID;
    p_Local_ItemInfo->wPulse = 1600;
    p_Local_ItemInfo->lpItem = lpItemBase;
    if ( (lpItemBase->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 && m_cNumOrDurability > 1u )
      p_Local_ItemInfo->cNum = m_cNumOrDurability;
  }
  p_Local_ItemInfo->Pos = *Pos;
  v22 = this->m_lstItem._Myhead->_Next;
  v23 = std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(
          &this->m_lstItem,
          v22,
          v22->_Prev,
          p_Local_ItemInfo);
  std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
    (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *)this,
    1u);
  v24 = !bAutoRouting;
  v22->_Prev = v23;
  v23->_Prev->_Next = v23;
  if ( v24 )
    CCell::SendPullDownInfo(this, dwPreOwnerID, p_Local_ItemInfo);
  return 1;
}

//----- (00416E30) --------------------------------------------------------
void __thiscall CClass::IncrementByType(
        CClass *this,
        _CHAR_INFOST *InfoSt,
        unsigned __int8 cTypeIndex,
        unsigned __int8 cIncrementIndex)
{
  switch ( this->m_eIncrementType[cTypeIndex] )
  {
    case STR:
      InfoSt->STR += this->m_cIncrementValue[cIncrementIndex];
      break;
    case DEX:
      InfoSt->DEX += this->m_cIncrementValue[cIncrementIndex];
      break;
    case CON:
      InfoSt->CON += this->m_cIncrementValue[cIncrementIndex];
      break;
    case INT:
      InfoSt->INT += this->m_cIncrementValue[cIncrementIndex];
      break;
    case WIS:
      InfoSt->WIS += this->m_cIncrementValue[cIncrementIndex];
      break;
    default:
      return;
  }
}

//----- (00416ED0) --------------------------------------------------------
unsigned __int8 __cdecl CClass::GetRequiredIP(unsigned __int16 usClass, unsigned __int8 cType)
{
  unsigned __int8 result; // al
  CClass *v3; // eax

  if ( usClass >= 0x19u || cType >= 6u )
    return -1;
  if ( dword_50938C[8 * usClass] == 2 )
  {
    if ( ClassTable[usClass].m_eIncrementType[0] == cType )
      return 2;
    result = 1;
    if ( dword_50937C[8 * usClass] == cType )
      return 2;
  }
  else
  {
    v3 = ClassTable;
    while ( v3->m_ePrevJobType != usClass || v3->m_eIncrementType[0] != cType && v3->m_eIncrementType[1] != cType )
    {
      if ( (int)++v3 >= (int)&PI_0 )
        return 1;
    }
    return 2;
  }
  return result;
}
// 50937C: using guessed type int dword_50937C[];
// 50938C: using guessed type int dword_50938C[];

//----- (00416F50) --------------------------------------------------------
bool __userpurge CClass::UpgradeClass@<al>(
        CClass *this@<ecx>,
        int a2@<edi>,
        CharacterDBData *DBData,
        unsigned __int8 cClassType)
{
  int Class; // eax
  char Level; // bl
  double v6; // st7
  unsigned __int64 v7; // rax
  double v8; // st7
  unsigned __int16 CON; // bp
  int v10; // edi
  __int16 v11; // bp
  __int16 v12; // ax
  __int16 v13; // cx
  double v14; // st7
  unsigned __int64 v15; // rax
  double v16; // st7
  unsigned __int16 STR; // bp
  double v18; // st7
  unsigned __int64 v19; // rax
  double v20; // st7
  unsigned __int16 DEX; // bp
  double v22; // st7
  unsigned __int64 v23; // rax
  double v24; // st7
  unsigned __int16 v25; // bp
  double v26; // st7
  unsigned __int64 v27; // rax
  double v28; // st7
  unsigned __int16 v29; // bp
  __int16 v30; // dx
  double v31; // st7
  unsigned __int64 v32; // rax
  double v33; // st7
  unsigned __int16 v34; // bp
  __int16 v35; // dx
  __int16 v36; // dx
  double v37; // st7
  unsigned __int16 v38; // bp
  bool result; // al
  float cClassTypea; // [esp+10h] [ebp+8h]

  Class = DBData->m_Info.Class;
  if ( Class != dword_509388[8 * cClassType] || dword_50938C[8 * Class] != 1 )
    return 0;
  switch ( cClassType )
  {
    case 5u:
      Level = DBData->m_Info.Level;
      v6 = (double)(Level - 1);
      DBData->m_Info.STR += (unsigned __int64)(0.5 * v6);
      LOWORD(a2) = DBData->m_Info.STR;
      v7 = (unsigned __int64)(0.5 * v6 + 0.5);
      v8 = v6 * 1.5;
      DBData->m_Info.CON += v7;
      CON = DBData->m_Info.CON;
      v10 = a2 - (unsigned __int64)v8 - 20;
      DBData->m_Info.Class = 5;
      v11 = CON - (unsigned __int64)(v8 + 0.5);
      v12 = DBData->m_Info.DEX + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_19;
    case 6u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 6;
      DBData->m_Info.STR += Level - 1;
      LOWORD(v10) = DBData->m_Info.STR + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.CON - Level - 19;
      v12 = DBData->m_Info.DEX + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_20;
    case 7u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 7;
      DBData->m_Info.DEX += Level - 1;
      LOWORD(v10) = DBData->m_Info.DEX + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.STR - Level - 19;
      v12 = DBData->m_Info.CON + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_20;
    case 8u:
      Level = DBData->m_Info.Level;
      v14 = (double)(Level - 1);
      DBData->m_Info.DEX += (unsigned __int64)(0.5 * v14);
      LOWORD(a2) = DBData->m_Info.DEX;
      v15 = (unsigned __int64)(0.5 * v14 + 0.5);
      v16 = v14 * 1.5;
      DBData->m_Info.STR += v15;
      STR = DBData->m_Info.STR;
      v10 = a2 - (unsigned __int64)v16 - 20;
      DBData->m_Info.Class = 8;
      v11 = STR - (unsigned __int64)(v16 + 0.5);
      goto LABEL_18;
    case 9u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 9;
      DBData->m_Info.INT += Level - 1;
      LOWORD(v10) = DBData->m_Info.INT + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.DEX - Level - 19;
      v12 = DBData->m_Info.STR + DBData->m_Info.CON + DBData->m_Info.WIS;
      goto LABEL_20;
    case 0xAu:
      Level = DBData->m_Info.Level;
      v18 = (double)(Level - 1);
      DBData->m_Info.INT += (unsigned __int64)(0.5 * v18);
      LOWORD(a2) = DBData->m_Info.INT;
      v19 = (unsigned __int64)(0.5 * v18 + 0.5);
      v20 = v18 * 1.5;
      DBData->m_Info.DEX += v19;
      DEX = DBData->m_Info.DEX;
      v10 = a2 - (unsigned __int64)v20 - 20;
      DBData->m_Info.Class = 10;
      v11 = DEX - (unsigned __int64)(v20 + 0.5);
      v12 = DBData->m_Info.STR + DBData->m_Info.CON + DBData->m_Info.WIS;
      goto LABEL_19;
    case 0xBu:
      Level = DBData->m_Info.Level;
      v22 = (double)(Level - 1);
      DBData->m_Info.WIS += (unsigned __int64)(0.5 * v22);
      LOWORD(a2) = DBData->m_Info.WIS;
      v23 = (unsigned __int64)(0.5 * v22 + 0.5);
      v24 = v22 * 1.5;
      DBData->m_Info.CON += v23;
      v25 = DBData->m_Info.CON;
      v10 = a2 - (unsigned __int64)v24 - 20;
      DBData->m_Info.Class = 11;
      v11 = v25 - (unsigned __int64)(v24 + 0.5);
      v12 = DBData->m_Info.STR + DBData->m_Info.DEX + DBData->m_Info.INT;
      goto LABEL_19;
    case 0xCu:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 12;
      DBData->m_Info.WIS += Level - 1;
      LOWORD(v10) = DBData->m_Info.WIS + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.CON - Level - 19;
      v12 = DBData->m_Info.STR + DBData->m_Info.DEX + DBData->m_Info.INT;
      goto LABEL_20;
    case 0x13u:
      Level = DBData->m_Info.Level;
      v26 = (double)(Level - 1);
      DBData->m_Info.STR += (unsigned __int64)(1.5 * v26);
      LOWORD(a2) = DBData->m_Info.STR;
      v27 = (unsigned __int64)(0.5 * v26 + 0.5);
      v28 = v26 * 1.5;
      DBData->m_Info.CON += v27;
      v29 = DBData->m_Info.CON;
      DBData->m_Info.IP += 2 * Level - 2;
      v10 = a2 - (unsigned __int64)v28 - 20;
      DBData->m_Info.Class = 19;
      v11 = v29 - (unsigned __int64)(v28 + 0.5);
      v12 = DBData->m_Info.DEX + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_19;
    case 0x14u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 20;
      v30 = 2 * (Level - 1);
      DBData->m_Info.STR += v30;
      DBData->m_Info.IP += v30;
      LOWORD(v10) = DBData->m_Info.STR + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.CON - Level - 19;
      v12 = DBData->m_Info.DEX + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_20;
    case 0x15u:
      Level = DBData->m_Info.Level;
      v31 = (double)(Level - 1);
      DBData->m_Info.DEX += (unsigned __int64)(1.5 * v31);
      LOWORD(a2) = DBData->m_Info.DEX;
      v32 = (unsigned __int64)(0.5 * v31 + 0.5);
      v33 = v31 * 1.5;
      DBData->m_Info.CON += v32;
      v34 = DBData->m_Info.CON;
      DBData->m_Info.IP += 2 * Level - 2;
      v10 = a2 - (unsigned __int64)v33 - 20;
      DBData->m_Info.Class = 21;
      v11 = v34 - (unsigned __int64)(v33 + 0.5);
      v12 = DBData->m_Info.STR + DBData->m_Info.INT + DBData->m_Info.WIS;
      goto LABEL_19;
    case 0x16u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 22;
      v35 = 2 * (Level - 1);
      DBData->m_Info.INT += v35;
      DBData->m_Info.IP += v35;
      LOWORD(v10) = DBData->m_Info.INT + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.DEX - Level - 19;
      v12 = DBData->m_Info.STR + DBData->m_Info.CON + DBData->m_Info.WIS;
      goto LABEL_20;
    case 0x17u:
      Level = DBData->m_Info.Level;
      DBData->m_Info.Class = 23;
      v36 = 2 * (Level - 1);
      DBData->m_Info.WIS += v36;
      DBData->m_Info.IP += v36;
      LOWORD(v10) = DBData->m_Info.WIS + 2 * (32758 - (Level - 1));
      v13 = DBData->m_Info.DEX - Level - 19;
      v12 = DBData->m_Info.STR + DBData->m_Info.CON + DBData->m_Info.INT;
      goto LABEL_20;
    case 0x18u:
      Level = DBData->m_Info.Level;
      v37 = (double)(Level - 1);
      cClassTypea = 1.5 * v37;
      DBData->m_Info.STR += (unsigned __int64)(1.5 * v37 + 0.5);
      LOWORD(a2) = DBData->m_Info.STR;
      DBData->m_Info.DEX += (unsigned __int64)(v37 * 0.5);
      v38 = DBData->m_Info.DEX;
      DBData->m_Info.IP += 2 * Level - 2;
      DBData->m_Info.Class = 24;
      v10 = a2 - (unsigned __int64)cClassTypea - 20;
      v11 = v38 - (unsigned __int64)(1.5 * v37 + 0.5);
LABEL_18:
      v12 = DBData->m_Info.CON + DBData->m_Info.INT + DBData->m_Info.WIS;
LABEL_19:
      v13 = v11 - 20;
LABEL_20:
      DBData->m_Info.IP = 2 * (Level - v13 - v10) + 8 - (v12 - 60);
      result = 1;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}
// 509388: using guessed type int dword_509388[];
// 50938C: using guessed type int dword_50938C[];

//----- (00417560) --------------------------------------------------------
bool __thiscall CClass::CheckState(CClass *this, ChState *State, unsigned __int8 cLevel)
{
  unsigned __int16 m_wIP; // di
  bool v4; // cc
  unsigned __int8 v5; // al
  __int16 v6; // dx
  unsigned __int8 v7; // cl
  __int16 v8; // dx
  __int16 v9; // dx
  __int16 v10; // dx
  bool result; // al
  int v12; // ebx
  __int16 v13; // cx

  m_wIP = State->m_wIP;
  switch ( this->m_eJobType )
  {
    case Fighter:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wDEX
                            + State->m_wINT
                            + State->m_wWIS
                            + 2 * (State->m_wSTR + State->m_wCON + 2 * (16350 - cLevel))) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Rouge:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wCON
                            + State->m_wINT
                            + State->m_wWIS
                            + 2 * (State->m_wSTR + State->m_wDEX + 2 * (16350 - cLevel))) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Mage:
      v5 = cLevel;
      v6 = State->m_wCON + State->m_wWIS + 2 * (State->m_wDEX + State->m_wINT + 2 * (16350 - cLevel));
      goto LABEL_5;
    case Acolyte:
      v5 = cLevel;
      v6 = State->m_wDEX + State->m_wINT + 2 * (State->m_wCON + State->m_wWIS + 2 * (16350 - cLevel));
LABEL_5:
      v4 = (unsigned __int16)(m_wIP + State->m_wSTR + v6) <= 2 * v5 + 8;
      goto LABEL_28;
    case Defender:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wDEX
                            + State->m_wINT
                            + State->m_wWIS
                            + 2
                            * (State->m_wSTR
                             + State->m_wCON
                             + (unsigned __int64)((double)(cLevel - 1) * -1.5)
                             - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
                            - 140) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Warrior:
      v7 = cLevel;
      v8 = State->m_wDEX + State->m_wINT + State->m_wWIS + 2 * (State->m_wSTR + State->m_wCON - 3 * cLevel) - 134;
      goto LABEL_27;
    case Assasin:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wCON
                            + State->m_wINT
                            + State->m_wWIS
                            + 2 * (State->m_wSTR + State->m_wDEX - 3 * cLevel)
                            - 134) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Archer:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wCON
                            + State->m_wINT
                            + State->m_wWIS
                            + 2
                            * (State->m_wSTR
                             + State->m_wDEX
                             + (unsigned __int64)((double)(cLevel - 1) * -1.5)
                             - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
                            - 140) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Sorcerer:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wSTR
                            + State->m_wCON
                            + State->m_wWIS
                            + 2 * (State->m_wDEX + State->m_wINT - 3 * cLevel)
                            - 134) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Enchanter:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wSTR
                            + State->m_wCON
                            + State->m_wWIS
                            + 2
                            * (State->m_wDEX
                             + State->m_wINT
                             + (unsigned __int64)((double)(cLevel - 1) * -1.5)
                             - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
                            - 140) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Priest:
      v4 = (unsigned __int16)(m_wIP
                            + State->m_wSTR
                            + State->m_wDEX
                            + State->m_wINT
                            + 2
                            * (State->m_wCON
                             + State->m_wWIS
                             + (unsigned __int64)((double)(cLevel - 1) * -1.5)
                             - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
                            - 140) <= 2 * cLevel + 8;
      goto LABEL_28;
    case Cleric:
      v7 = cLevel;
      v9 = State->m_wDEX + State->m_wINT + 2 * (State->m_wCON + State->m_wWIS - 3 * cLevel) - 134;
      goto LABEL_26;
    case Combatant:
      v10 = State->m_wDEX + State->m_wINT + State->m_wWIS + 2 * (State->m_wCON - cLevel) - 118;
      goto LABEL_16;
    case Officetor:
      v10 = State->m_wCON + State->m_wINT + State->m_wWIS + 2 * (State->m_wDEX - cLevel) - 118;
LABEL_16:
      if ( (unsigned __int16)(m_wIP + State->m_wSTR + v10) <= 0xAu )
        goto LABEL_33;
      return 0;
    case Templar:
      v12 = cLevel;
      v13 = State->m_wDEX
          + State->m_wINT
          + State->m_wWIS
          + 2
          * (State->m_wSTR
           + State->m_wCON
           + (unsigned __int64)((double)(cLevel - 1) * -1.5)
           - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
          - 140;
      goto LABEL_23;
    case Attacker:
      if ( (unsigned __int16)(m_wIP
                            + State->m_wDEX
                            + State->m_wINT
                            + State->m_wWIS
                            + 2 * (State->m_wSTR + State->m_wCON - 3 * cLevel)
                            - 134) <= 2 * cLevel + 8 )
        goto LABEL_33;
      return 0;
    case Gunner:
      v12 = cLevel;
      v13 = State->m_wSTR
          + State->m_wINT
          + State->m_wWIS
          + 2
          * (State->m_wDEX
           + State->m_wCON
           + (unsigned __int64)((double)(cLevel - 1) * -1.5)
           - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
          - 140;
LABEL_23:
      if ( (unsigned __int16)(m_wIP + v13) > 2 * v12 + 8 )
        goto LABEL_24;
      goto LABEL_33;
    case RuneOff:
      v7 = cLevel;
      v9 = State->m_wCON + State->m_wWIS + 2 * (State->m_wDEX + State->m_wINT - 3 * cLevel) - 134;
LABEL_26:
      v8 = State->m_wSTR + v9;
LABEL_27:
      v4 = (unsigned __int16)(m_wIP + v8) <= 2 * v7 + 8;
LABEL_28:
      if ( v4 )
        goto LABEL_33;
      result = 0;
      break;
    case LifeOff:
      if ( (unsigned __int16)(m_wIP
                            + State->m_wSTR
                            + State->m_wCON
                            + State->m_wINT
                            + 2 * (State->m_wDEX + State->m_wWIS - 3 * cLevel)
                            - 134) <= 2 * cLevel + 8 )
        goto LABEL_33;
      result = 0;
      break;
    case ShadowOff:
      if ( (unsigned __int16)(m_wIP
                            + State->m_wCON
                            + State->m_wINT
                            + State->m_wWIS
                            + 2
                            * (State->m_wSTR
                             + State->m_wDEX
                             + (unsigned __int64)((double)(cLevel - 1) * -1.5)
                             - (unsigned __int64)(1.5 * (double)(cLevel - 1) + 0.5))
                            - 140) <= 2 * cLevel + 8 )
        goto LABEL_33;
LABEL_24:
      result = 0;
      break;
    default:
LABEL_33:
      result = 1;
      break;
  }
  return result;
}

//----- (00417B70) --------------------------------------------------------
bool __thiscall CClass::CheckMinState(CClass *this, ChState *State, unsigned __int8 cLevel)
{
  int v3; // eax
  int v4; // ecx
  ChState *v5; // eax
  bool result; // al
  int v7; // ecx
  ChState *v8; // ecx
  int v9; // eax
  ChState *v10; // ecx
  int v11; // eax
  double v12; // st7
  ChState *v13; // edi
  bool v14; // cc
  int v15; // ecx
  int v16; // ecx
  double v17; // st7
  double v18; // st7
  double v19; // st7
  double v20; // st7
  int v21; // ecx
  double v22; // st7
  double v23; // st7
  float cLevela; // [esp+10h] [ebp+8h]
  float cLevelb; // [esp+10h] [ebp+8h]
  float cLevelc; // [esp+10h] [ebp+8h]
  float cLeveld; // [esp+10h] [ebp+8h]

  LOBYTE(v3) = cLevel - 1;
  switch ( this->m_eJobType )
  {
    case Fighter:
      v4 = (unsigned __int8)v3 + 20;
      v5 = State;
      if ( State->m_wSTR < v4 || State->m_wDEX < 0x14u )
        goto LABEL_100;
      goto LABEL_4;
    case Rouge:
      v7 = (unsigned __int8)v3 + 20;
      v5 = State;
      if ( State->m_wSTR < v7 || State->m_wDEX < v7 || State->m_wCON < 0x14u )
        goto LABEL_100;
      if ( State->m_wINT >= 0x14u )
        goto LABEL_78;
      return 0;
    case Mage:
      v8 = State;
      if ( State->m_wSTR < 0x14u )
        goto LABEL_100;
      v9 = (unsigned __int8)v3 + 20;
      if ( State->m_wDEX < v9 || State->m_wCON < 0x14u )
        goto LABEL_100;
      if ( State->m_wINT >= v9 )
        goto LABEL_90;
      return 0;
    case Acolyte:
      v10 = State;
      if ( State->m_wSTR < 0x14u )
        goto LABEL_100;
      if ( State->m_wDEX < 0x14u )
        goto LABEL_100;
      v11 = (unsigned __int8)v3 + 20;
      if ( State->m_wCON < v11 )
        goto LABEL_100;
      if ( State->m_wINT >= 0x14u )
        goto LABEL_97;
      return 0;
    case Defender:
      v12 = (double)(unsigned __int8)v3 * 1.5;
      v13 = State;
      if ( State->m_wSTR < (unsigned __int16)(unsigned __int64)v12 + 20 || State->m_wDEX < 0x14u )
        goto LABEL_100;
      cLevela = v12;
      v14 = State->m_wCON < (unsigned __int16)(unsigned __int64)(cLevela + 0.5) + 20;
      goto LABEL_25;
    case Warrior:
      v15 = (unsigned __int8)v3;
      v5 = State;
      if ( State->m_wSTR >= 2 * v15 + 20 && State->m_wDEX >= 0x14u )
      {
        v4 = v15 + 20;
LABEL_4:
        if ( v5->m_wCON >= v4 )
          goto LABEL_5;
      }
      goto LABEL_100;
    case Assasin:
      v16 = (unsigned __int8)v3;
      v5 = State;
      if ( State->m_wSTR < v16 + 20 || State->m_wDEX < 2 * v16 + 20 || State->m_wCON < 0x14u )
        goto LABEL_100;
LABEL_5:
      if ( v5->m_wINT < 0x14u )
        return 0;
LABEL_78:
      if ( v5->m_wWIS >= 0x14u )
        goto LABEL_105;
      return 0;
    case Archer:
      v17 = (double)(unsigned __int8)v3 * 1.5;
      v13 = State;
      if ( State->m_wSTR < (unsigned __int16)(unsigned __int64)(v17 + 0.5) + 20
        || State->m_wDEX < (unsigned __int16)(unsigned __int64)v17 + 20
        || State->m_wCON < 0x14u )
      {
        goto LABEL_100;
      }
      if ( State->m_wINT >= 0x14u )
        goto LABEL_84;
      return 0;
    case Sorcerer:
      v8 = State;
      if ( State->m_wSTR < 0x14u || State->m_wDEX < (unsigned __int8)v3 + 20 || State->m_wCON < 0x14u )
        goto LABEL_100;
      if ( State->m_wINT >= 2 * (unsigned __int8)v3 + 20 )
        goto LABEL_90;
      return 0;
    case Enchanter:
      v13 = State;
      if ( State->m_wSTR < 0x14u )
        goto LABEL_100;
      v18 = (double)(unsigned __int8)v3 * 1.5;
      if ( State->m_wDEX < (unsigned __int16)(unsigned __int64)(v18 + 0.5) + 20 || State->m_wCON < 0x14u )
        goto LABEL_100;
      cLevelb = v18;
      if ( State->m_wINT >= (unsigned __int16)(unsigned __int64)cLevelb + 20 )
        goto LABEL_84;
      return 0;
    case Priest:
      if ( State->m_wSTR < 0x14u )
        goto LABEL_100;
      if ( State->m_wDEX < 0x14u )
        goto LABEL_100;
      v19 = (double)(unsigned __int8)v3 * 1.5;
      if ( State->m_wCON < (unsigned __int16)(unsigned __int64)(v19 + 0.5) + 20 || State->m_wINT < 0x14u )
        goto LABEL_100;
      cLevelc = v19;
      if ( State->m_wWIS >= (unsigned __int16)(unsigned __int64)cLevelc + 20 )
        goto LABEL_105;
      return 0;
    case Cleric:
      v10 = State;
      if ( State->m_wSTR < 0x14u )
        goto LABEL_100;
      if ( State->m_wDEX < 0x14u )
        goto LABEL_100;
      v3 = (unsigned __int8)v3;
      if ( State->m_wCON < (unsigned __int8)v3 + 20 )
        goto LABEL_100;
      if ( State->m_wINT < 0x14u )
        return 0;
LABEL_96:
      v11 = 2 * v3 + 20;
LABEL_97:
      if ( v10->m_wWIS >= v11 )
        goto LABEL_105;
      return 0;
    case Combatant:
      v8 = State;
      if ( State->m_wSTR < 0x14u || State->m_wDEX < 0x14u || State->m_wCON < (unsigned __int8)v3 + 20 )
        goto LABEL_100;
      if ( State->m_wINT >= 0x14u )
        goto LABEL_90;
      return 0;
    case Officetor:
      v8 = State;
      if ( State->m_wSTR < 0x14u || State->m_wDEX < (unsigned __int8)v3 + 20 || State->m_wCON < 0x14u )
        goto LABEL_100;
      if ( State->m_wINT < 0x14u )
        return 0;
LABEL_90:
      if ( v8->m_wWIS >= 0x14u )
        goto LABEL_105;
      result = 0;
      break;
    case Templar:
      v20 = (double)(unsigned __int8)v3 * 1.5;
      v13 = State;
      if ( State->m_wSTR < (unsigned __int16)(unsigned __int64)v20 + 20 || State->m_wDEX < 0x14u )
        goto LABEL_100;
      cLeveld = v20;
      v14 = State->m_wCON < (unsigned __int16)(unsigned __int64)(cLeveld + 0.5) + 20;
LABEL_25:
      if ( v14 )
      {
LABEL_100:
        result = 0;
      }
      else if ( v13->m_wINT >= 0x14u )
      {
LABEL_84:
        if ( v13->m_wWIS >= 0x14u )
LABEL_105:
          result = 1;
        else
          result = 0;
      }
      else
      {
        result = 0;
      }
      break;
    case Attacker:
      v21 = (unsigned __int8)v3;
      v5 = State;
      if ( State->m_wSTR >= 2 * v21 + 20
        && State->m_wDEX >= 0x14u
        && State->m_wCON >= v21 + 20
        && State->m_wINT >= 0x14u )
      {
        goto LABEL_78;
      }
      goto LABEL_100;
    case Gunner:
      v13 = State;
      if ( State->m_wSTR >= 0x14u )
      {
        v22 = (double)(unsigned __int8)v3 * 1.5;
        if ( State->m_wDEX >= (unsigned __int16)(unsigned __int64)v22 + 20
          && State->m_wCON >= (unsigned __int16)(unsigned __int64)(v22 + 0.5) + 20
          && State->m_wINT >= 0x14u )
        {
          goto LABEL_84;
        }
      }
      goto LABEL_100;
    case RuneOff:
      v8 = State;
      if ( State->m_wSTR >= 0x14u
        && State->m_wDEX >= (unsigned __int8)v3 + 20
        && State->m_wCON >= 0x14u
        && State->m_wINT >= 2 * (unsigned __int8)v3 + 20 )
      {
        goto LABEL_90;
      }
      goto LABEL_100;
    case LifeOff:
      v10 = State;
      if ( State->m_wSTR >= 0x14u )
      {
        v3 = (unsigned __int8)v3;
        if ( State->m_wDEX >= (unsigned __int8)v3 + 20 && State->m_wCON >= 0x14u && State->m_wINT >= 0x14u )
          goto LABEL_96;
      }
      goto LABEL_100;
    case ShadowOff:
      v23 = (double)(unsigned __int8)v3 * 1.5;
      if ( State->m_wSTR >= (unsigned __int16)(unsigned __int64)(v23 + 0.5) + 20
        && State->m_wDEX >= (unsigned __int16)(unsigned __int64)v23 + 20
        && State->m_wCON >= 0x14u
        && State->m_wINT >= 0x14u
        && State->m_wWIS >= 0x14u )
      {
        goto LABEL_105;
      }
      goto LABEL_100;
    default:
      goto LABEL_105;
  }
  return result;
}

//----- (00418230) --------------------------------------------------------
int __cdecl CClass::GetJobLevel(unsigned __int8 cClass)
{
  if ( cClass < 0x19u )
    return dword_50938C[8 * cClass];
  else
    return 1;
}
// 50938C: using guessed type int dword_50938C[];

//----- (00418250) --------------------------------------------------------
unsigned __int8 __cdecl CClass::GetPreviousJob(unsigned __int8 cClass)
{
  if ( cClass < 0x19u )
    return dword_509388[8 * cClass];
  else
    return 0;
}
// 509388: using guessed type int dword_509388[];

//----- (00418270) --------------------------------------------------------
int __cdecl CClass::GetRace(unsigned __int8 cClass)
{
  if ( cClass < 0x19u )
    return dword_509390[8 * cClass];
  else
    return 2;
}
// 509390: using guessed type int dword_509390[];

//----- (00418290) --------------------------------------------------------
void __thiscall CClass::LevelUp(CClass *this, _CHAR_INFOST *InfoSt)
{
  bool m_bLevelSwap; // al
  unsigned __int8 Class; // al
  unsigned __int8 cIndex2; // [esp+8h] [ebp-4h]
  unsigned __int8 InfoSta; // [esp+10h] [ebp+4h]

  m_bLevelSwap = this->m_bLevelSwap;
  InfoSta = m_bLevelSwap && InfoSt->Level % 2 != 1;
  cIndex2 = !m_bLevelSwap || InfoSt->Level % 2 == 1;
  CClass::IncrementByType(this, InfoSt, 0, InfoSta);
  CClass::IncrementByType(this, InfoSt, 1u, cIndex2);
  Class = InfoSt->Class;
  if ( Class >= 0x19u || dword_509390[8 * Class] != 1 || dword_50938C[8 * Class] == 2 )
    InfoSt->IP += 2;
}
// 41831E: conditional instruction was optimized away because al.1<19u
// 50938C: using guessed type int dword_50938C[];
// 509390: using guessed type int dword_509390[];

//----- (00418340) --------------------------------------------------------
char __thiscall CClass::DowngradeClass(CClass *this, CharacterDBData *DBData, unsigned __int8 cClassType)
{
  char Level; // bl
  unsigned __int64 v4; // rax
  bool v5; // cc
  char v6; // al
  const void *v7; // eax
  const void *v8; // eax
  QUICK v10; // [esp+4h] [ebp-64h] BYREF

  if ( DBData->m_Info.Class != 21 || cClassType != dword_509628 || dword_50962C != 2 )
    return 0;
  Level = DBData->m_Info.Level;
  v4 = (unsigned __int64)((double)Level * 0.1);
  v5 = (char)(Level - v4) <= 10;
  DBData->m_Info.Level = Level - v4;
  v6 = Level - v4;
  if ( v5 )
    v6 = 10;
  DBData->m_Info.Exp = 0LL;
  DBData->m_Info.Fame = 0;
  DBData->m_Info.Level = v6;
  if ( cClassType != 17 )
    return 0;
  DBData->m_Info.STR = 20;
  DBData->m_Info.DEX = 20;
  DBData->m_Info.INT = 20;
  DBData->m_Info.WIS = 20;
  DBData->m_Info.CON = v6 + 19;
  DBData->m_Info.Class = 17;
  DBData->m_Info.IP = 10;
  SKILL::SKILL((SKILL *)&v10);
  qmemcpy(&DBData->m_Skill, v7, sizeof(DBData->m_Skill));
  QUICK::QUICK(&v10);
  qmemcpy(&DBData->m_Quick, v8, sizeof(DBData->m_Quick));
  return 1;
}
// 4183FA: variable 'v7' is possibly undefined
// 418412: variable 'v8' is possibly undefined
// 509628: using guessed type int dword_509628;
// 50962C: using guessed type int dword_50962C;

//----- (00418430) --------------------------------------------------------
char __thiscall CClass::InitializeClass(CClass *this, CharacterDBData *DBData, unsigned __int8 cClassType)
{
  const void *v4; // eax
  const void *v5; // eax
  QUICK v6; // [esp+0h] [ebp-64h] BYREF

  if ( dword_50938C[8 * cClassType] != 1 )
    return 0;
  DBData->m_Info.Exp = 0LL;
  DBData->m_Info.Fame = 0;
  DBData->m_Info.Class = cClassType;
  DBData->m_Info.Level = 1;
  DBData->m_Info.STR = 20;
  DBData->m_Info.DEX = 20;
  DBData->m_Info.CON = 20;
  DBData->m_Info.INT = 20;
  DBData->m_Info.WIS = 20;
  DBData->m_Info.IP = 10;
  SKILL::SKILL((SKILL *)&v6);
  qmemcpy(&DBData->m_Skill, v4, sizeof(DBData->m_Skill));
  QUICK::QUICK(&v6);
  qmemcpy(&DBData->m_Quick, v5, sizeof(DBData->m_Quick));
  return 1;
}
// 4184A0: variable 'v4' is possibly undefined
// 4184B8: variable 'v5' is possibly undefined
// 50938C: using guessed type int dword_50938C[];

//----- (004184D0) --------------------------------------------------------
char __userpurge CClass::JobChange@<al>(
        CClass *this@<ecx>,
        int a2@<edi>,
        CharacterDBData *DBData,
        unsigned __int8 cClassType)
{
  if ( DBData->m_Info.Level < 10 )
    return 0;
  if ( this->m_eJobLevel == DEFAULT_CLASS )
    return CClass::DowngradeClass(this, DBData, cClassType);
  if ( this->m_eJobLevel != JOB_CHANGE_1ST )
    return 0;
  return CClass::UpgradeClass(this, a2, DBData, cClassType);
}

//----- (00418500) --------------------------------------------------------
void __cdecl Math::Convert::Hex64ToStr(char *szDest, unsigned __int64 hex)
{
  *((_WORD *)szDest + 7) = Math::Convert::m_FastHeToBi[(unsigned __int8)hex];
  *((_WORD *)szDest + 6) = Math::Convert::m_FastHeToBi[BYTE1(hex)];
  *((_WORD *)szDest + 5) = Math::Convert::m_FastHeToBi[BYTE2(hex)];
  *((_WORD *)szDest + 4) = Math::Convert::m_FastHeToBi[BYTE3(hex)];
  *((_WORD *)szDest + 3) = Math::Convert::m_FastHeToBi[BYTE4(hex)];
  *((_WORD *)szDest + 2) = Math::Convert::m_FastHeToBi[BYTE5(hex)];
  *((_WORD *)szDest + 1) = Math::Convert::m_FastHeToBi[BYTE6(hex)];
  *(_WORD *)szDest = Math::Convert::m_FastHeToBi[HIBYTE(hex)];
  szDest[16] = 0;
}

//----- (004185A0) --------------------------------------------------------
void __thiscall Item::CItem::MoveItem(Item::CItem *this, Item::ItemPos itemPos)
{
  __int16 v2; // ax

  v2 = *(_BYTE *)&itemPos & 0xF;
  if ( v2 == 10 || (this->m_ItemData.m_ItemPos = itemPos, v2 != 6) )
    this->m_itemPos_Real = itemPos;
}

//----- (004185C0) --------------------------------------------------------
unsigned int __cdecl LogErrorItem(unsigned int dwCID, const char *szDetail, const char *szBuffer)
{
  char szItemUID[64]; // [esp+4h] [ebp-44h] BYREF

  Math::Convert::Hex64ToStr(szItemUID, *(_QWORD *)szBuffer);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "LogErrorItem",
    aDWorkRylSource_99,
    45,
    "CID:0x%08x %s. UID:%s, ProtoTypeID:%d",
    dwCID,
    szDetail,
    szItemUID,
    *((unsigned __int16 *)szBuffer + 4));
  return *((unsigned __int8 *)szBuffer + 12);
}

//----- (00418630) --------------------------------------------------------
void __thiscall Item::CItemContainer::DumpItemInfo(Item::CItemContainer *this)
{
  Item::CItem **m_lppItems; // ebx
  Item::CItem **i; // ebp
  Item::CItem *v4; // esi
  Item::ItemPos itemPos; // [esp+Ch] [ebp-128h]
  char szUID[32]; // [esp+10h] [ebp-124h] BYREF
  char szBuffer[256]; // [esp+30h] [ebp-104h] BYREF

  m_lppItems = this->m_lppItems;
  for ( i = &m_lppItems[this->m_nMaxSize]; m_lppItems != i; ++m_lppItems )
  {
    v4 = *m_lppItems;
    if ( *m_lppItems )
    {
      if ( this->m_lpNullItem != v4 )
      {
        Math::Convert::Hex64ToStr(szUID, v4->m_ItemData.m_dwUID);
        itemPos = v4->m_ItemData.m_ItemPos;
        _snprintf(
          szBuffer,
          0x100u,
          aCid0x08x_182,
          this->m_dwCID,
          szUID,
          v4->m_ItemData.m_usProtoTypeID,
          *(unsigned __int16 *)&itemPos >> 4,
          *(_BYTE *)&itemPos >> 4,
          *((_BYTE *)&itemPos + 1) & 0xF,
          *(unsigned __int16 *)&itemPos >> 12,
          v4->m_ItemInfo->m_DetailData.m_cXSize,
          v4->m_ItemInfo->m_DetailData.m_cXSize,
          v4->m_ItemData.m_cNumOrDurability);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CItemContainer::DumpItemInfo",
          aDWorkRylSource_99,
          147,
          "%s",
          szBuffer);
      }
    }
  }
}

//----- (00418740) --------------------------------------------------------
char __thiscall Item::CItemContainer::SerializeOut(
        Item::CItemContainer *this,
        char *szItemBuffer_Out,
        unsigned int *dwBufferSize_InOut)
{
  unsigned int v4; // ebx
  Item::CItem **m_lppItems; // esi
  Item::CItem **v6; // eax
  unsigned int v7; // edi
  Item::CItem *v8; // ecx
  Item::CItem_vtbl *v9; // edx
  unsigned int nItemSize; // [esp+10h] [ebp-8h] BYREF
  Item::CItem **lppItemPastEnd; // [esp+14h] [ebp-4h]

  v4 = *dwBufferSize_InOut;
  m_lppItems = this->m_lppItems;
  v6 = &m_lppItems[this->m_nMaxSize];
  v7 = 0;
  lppItemPastEnd = v6;
  if ( m_lppItems == v6 )
  {
LABEL_7:
    *dwBufferSize_InOut = v7;
    return 1;
  }
  while ( 1 )
  {
    v8 = *m_lppItems;
    if ( !*m_lppItems || this->m_lpNullItem == v8 )
      goto LABEL_6;
    v9 = v8->__vftable;
    nItemSize = v4;
    if ( !v9->SerializeOut(v8, &szItemBuffer_Out[v7], &nItemSize) )
      break;
    v7 += nItemSize;
    v4 -= nItemSize;
    v6 = lppItemPastEnd;
LABEL_6:
    if ( ++m_lppItems == v6 )
      goto LABEL_7;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "Item::CItemContainer::SerializeOut",
    aDWorkRylSource_99,
    170,
    aCid0x08x_131,
    this->m_dwCID,
    v7,
    v4);
  return 0;
}

//----- (004187F0) --------------------------------------------------------
Item::CItem *__thiscall Item::CArrayContainer::GetItem(Item::CArrayContainer *this, unsigned int itemPos)
{
  char v2; // al
  unsigned int v3; // edx
  unsigned __int8 m_nXSize; // bl
  unsigned __int8 v5; // al
  unsigned __int8 v6; // dl
  Item::CItem *v7; // eax
  unsigned __int8 itemPosa; // [esp+10h] [ebp+4h]

  v2 = (unsigned __int8)itemPos >> 4;
  v3 = itemPos >> 12;
  itemPosa = BYTE1(itemPos) & 0xF;
  m_nXSize = this->m_nXSize;
  v5 = v2 & 0xF;
  v6 = v3 & 0xF;
  if ( m_nXSize <= v5 || this->m_nYSize <= itemPosa || this->m_nTabNum <= v6 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::GetItem",
      aDWorkRylSource_99,
      218,
      aCid0x08x_188,
      this->m_dwCID,
      v5,
      m_nXSize,
      itemPosa,
      this->m_nYSize,
      v6,
      this->m_nTabNum);
    return 0;
  }
  else
  {
    v7 = (Item::CItem *)*((_DWORD *)&this->m_lppItems[v5] + itemPosa * m_nXSize + v6 * this->m_nSizePerTab);
    return v7 == this->m_lpNullItem ? 0 : v7;
  }
}

//----- (004188B0) --------------------------------------------------------
char __thiscall Item::CArrayContainer::SetItem(Item::CArrayContainer *this, unsigned int itemPos, Item::CItem *lpItem)
{
  const Item::ItemInfo *m_ItemInfo; // esi
  unsigned int m_nXSize; // ebp
  int v6; // edi
  unsigned int v7; // ebx
  int v8; // eax
  Item::CItem **m_lppItems; // esi
  Item::CItem **v10; // ebx
  int v11; // ebp
  Item::CItem **v12; // edx
  Item::CItem **v13; // esi
  Item::CItem **v14; // eax
  Item::CItem **i; // edx
  Item::CItem **v16; // esi
  Item::CItem **v17; // eax
  __int16 v18; // ax
  unsigned __int8 cY; // [esp+6h] [ebp-Eh]
  unsigned __int8 cXSize; // [esp+7h] [ebp-Dh]
  int v21; // [esp+8h] [ebp-Ch]
  unsigned int v22; // [esp+Ch] [ebp-8h]

  if ( lpItem )
  {
    m_ItemInfo = lpItem->m_ItemInfo;
    cY = BYTE1(itemPos) & 0xF;
    m_nXSize = this->m_nXSize;
    cXSize = m_ItemInfo->m_DetailData.m_cXSize;
    v6 = (unsigned __int8)itemPos >> 4;
    v22 = v6 + cXSize;
    if ( m_nXSize < v22
      || (v7 = cY + m_ItemInfo->m_DetailData.m_cYSize, this->m_nYSize < v7)
      || this->m_nTabNum <= (unsigned __int8)((itemPos >> 12) & 0xF) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CArrayContainer::SetItem",
        aDWorkRylSource_99,
        246,
        aCid0x08x_194,
        this->m_dwCID,
        (unsigned __int8)itemPos >> 4,
        m_nXSize,
        cY,
        this->m_nYSize,
        (unsigned __int16)itemPos >> 12,
        this->m_nTabNum,
        cXSize,
        m_ItemInfo->m_DetailData.m_cYSize);
      return 0;
    }
    else
    {
      v8 = ((unsigned __int16)itemPos >> 12) * this->m_nSizePerTab;
      v21 = (unsigned __int16)itemPos >> 12;
      m_lppItems = this->m_lppItems;
      v10 = &m_lppItems[v8] + v7 * this->m_nXSize;
      v11 = cY;
      v12 = &m_lppItems[v8] + cY * this->m_nXSize;
      if ( v12 == v10 )
      {
LABEL_11:
        for ( i = &this->m_lppItems[this->m_nXSize * cY] + v21 * this->m_nSizePerTab; i != v10; i += this->m_nXSize )
        {
          v16 = &i[v22];
          v17 = &i[v6];
          if ( v17 != v16 )
          {
            do
              *v17++ = this->m_lpNullItem;
            while ( v17 != v16 );
            v11 = cY;
          }
        }
        *((_DWORD *)&this->m_lppItems[v6] + v21 * this->m_nSizePerTab + v11 * this->m_nXSize) = lpItem;
        v18 = itemPos & 0xF;
        if ( v18 == 10 || (lpItem->m_ItemData.m_ItemPos = (Item::ItemPos)itemPos, v18 != 6) )
          lpItem->m_itemPos_Real = (Item::ItemPos)itemPos;
        return 1;
      }
      else
      {
        while ( 1 )
        {
          v13 = &v12[v22];
          v14 = &v12[v6];
          if ( v14 != v13 )
            break;
LABEL_10:
          v12 += this->m_nXSize;
          if ( v12 == v10 )
            goto LABEL_11;
        }
        while ( !*v14 )
        {
          if ( ++v14 == v13 )
            goto LABEL_10;
        }
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CArrayContainer::SetItem",
          aDWorkRylSource_99,
          264,
          aCid0x08xDDD,
          this->m_dwCID,
          (unsigned __int8)itemPos >> 4,
          cY,
          v21);
        return 0;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::SetItem",
      aDWorkRylSource_99,
      231,
      aCid0x08x_96,
      this->m_dwCID);
    return 0;
  }
}

//----- (00418AE0) --------------------------------------------------------
bool __thiscall Item::CArrayContainer::TestItem(Item::CArrayContainer *this, Item::CItem *lpItem)
{
  const Item::ItemInfo *m_ItemInfo; // eax
  unsigned __int8 m_cXSize; // cl
  unsigned __int8 m_cYSize; // dl
  unsigned __int8 v7; // al
  unsigned __int8 v8; // cl
  unsigned int v9; // ebx
  int v10; // ebp
  int v11; // edi
  int v12; // ecx
  Item::CItem **m_lppItems; // edx
  int m_nXSize; // ebp
  int v15; // eax
  Item::CItem **v16; // ebx
  Item::CItem **v17; // ecx
  Item::CItem **v18; // edx
  Item::CItem **v19; // eax
  bool v20; // cf
  bool v21; // zf
  unsigned __int8 cY; // [esp+4h] [ebp-1Ch]
  bool bItemExist; // [esp+5h] [ebp-1Bh]
  unsigned __int8 cYSize; // [esp+6h] [ebp-1Ah]
  unsigned __int8 cXSize; // [esp+7h] [ebp-19h]
  int v26; // [esp+8h] [ebp-18h]
  int v27; // [esp+Ch] [ebp-14h]
  int v28; // [esp+14h] [ebp-Ch]
  unsigned int v29; // [esp+1Ch] [ebp-4h]
  unsigned __int8 lpItema; // [esp+24h] [ebp+4h]

  if ( lpItem )
  {
    m_ItemInfo = lpItem->m_ItemInfo;
    m_cXSize = m_ItemInfo->m_DetailData.m_cXSize;
    m_cYSize = m_ItemInfo->m_DetailData.m_cYSize;
    v7 = 0;
    cXSize = m_cXSize;
    v8 = 0;
    cYSize = m_cYSize;
    lpItema = 0;
    cY = 0;
    v26 = 0;
    v28 = 3;
    do
    {
      do
      {
        v9 = cYSize + v8;
        v27 = v8;
        v29 = v9;
        if ( this->m_nYSize < v9 )
          break;
        v10 = cXSize;
        while ( 1 )
        {
          v11 = v7;
          if ( this->m_nXSize < (unsigned int)v7 + v10 )
            break;
          v12 = v26 * this->m_nSizePerTab;
          m_lppItems = this->m_lppItems;
          m_nXSize = this->m_nXSize;
          v15 = v12 + v27 * m_nXSize;
          v16 = &m_lppItems[v12] + v9 * m_nXSize;
          v10 = cXSize;
          v17 = &m_lppItems[v15];
          bItemExist = 0;
          if ( v17 != v16 )
          {
            while ( 1 )
            {
              v18 = &v17[v11] + cXSize;
              v19 = &v17[v11];
              if ( v19 != v18 )
                break;
LABEL_12:
              v17 += this->m_nXSize;
              if ( v17 == v16 )
                goto LABEL_15;
            }
            while ( !*v19 )
            {
              if ( ++v19 == v18 )
                goto LABEL_12;
            }
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "Item::CArrayContainer::TestItem",
              aDWorkRylSource_99,
              379,
              aCid0x08xDDD,
              this->m_dwCID,
              v11,
              v27,
              v26);
            bItemExist = 1;
          }
LABEL_15:
          v7 = ++lpItema;
          if ( lpItema >= 6u )
            break;
          v9 = v29;
        }
        v7 = 0;
        v8 = cY + 1;
        v20 = (unsigned __int8)(cY + 1) < 6u;
        lpItema = 0;
        ++cY;
      }
      while ( v20 );
      v7 = 0;
      v8 = 0;
      v21 = v28 == 1;
      lpItema = 0;
      cY = 0;
      ++v26;
      --v28;
    }
    while ( !v21 );
    return !bItemExist;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::TestItem",
      aDWorkRylSource_99,
      342,
      aCid0x08x_110,
      this->m_dwCID);
    return 0;
  }
}
// 418C8A: variable 'bItemExist' is possibly undefined

//----- (00418CA0) --------------------------------------------------------
char __thiscall Item::CArrayContainer::RemoveItem(Item::CArrayContainer *this, unsigned int itemPos)
{
  char v2; // al
  unsigned int v3; // edx
  unsigned __int8 v4; // al
  unsigned __int8 v5; // dl
  int v6; // esi
  int v7; // ebx
  unsigned int m_nXSize; // ebp
  Item::CItem *v9; // eax
  const Item::ItemInfo *m_ItemInfo; // eax
  int m_cXSize; // edx
  int v12; // edx
  Item::CItem **m_lppItems; // edi
  int v14; // ebp
  int v15; // eax
  Item::CItem **v16; // ebp
  Item::CItem **i; // edx
  Item::CItem **v18; // esi
  Item::CItem **j; // eax
  unsigned __int8 cXSize; // [esp+Bh] [ebp-Dh]
  int v22; // [esp+10h] [ebp-8h]
  unsigned __int8 itemPosa; // [esp+1Ch] [ebp+4h]
  unsigned __int8 itemPosb; // [esp+1Ch] [ebp+4h]

  v2 = (unsigned __int8)itemPos >> 4;
  v3 = itemPos >> 12;
  itemPosa = BYTE1(itemPos) & 0xF;
  v4 = v2 & 0xF;
  v5 = v3 & 0xF;
  cXSize = v5;
  if ( this->m_nXSize <= v4 || this->m_nYSize <= itemPosa || this->m_nTabNum <= v5 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::RemoveItem",
      aDWorkRylSource_99,
      409,
      aCid0x08x_23,
      this->m_dwCID,
      v4,
      this->m_nXSize,
      itemPosa,
      this->m_nYSize,
      v5,
      this->m_nTabNum);
    return 0;
  }
  else
  {
    v6 = itemPosa;
    v7 = v4;
    m_nXSize = this->m_nXSize;
    v9 = (Item::CItem *)*((_DWORD *)&this->m_lppItems[v4] + v5 * this->m_nSizePerTab + m_nXSize * itemPosa);
    if ( !v9 || this->m_lpNullItem == v9 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CArrayContainer::RemoveItem",
        aDWorkRylSource_99,
        416,
        aCid0x08x_311,
        this->m_dwCID,
        v7,
        itemPosa,
        v5);
      return 0;
    }
    else
    {
      m_ItemInfo = v9->m_ItemInfo;
      m_cXSize = m_ItemInfo->m_DetailData.m_cXSize;
      itemPosb = m_ItemInfo->m_DetailData.m_cYSize;
      v22 = m_cXSize + v7;
      if ( m_nXSize < m_cXSize + v7 || this->m_nYSize < (unsigned int)itemPosb + v6 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CArrayContainer::RemoveItem",
          aDWorkRylSource_99,
          428,
          aCid0x08x_194,
          this->m_dwCID,
          v7,
          m_nXSize,
          v6,
          this->m_nYSize,
          cXSize,
          this->m_nTabNum,
          m_cXSize,
          itemPosb);
        return 0;
      }
      else
      {
        v12 = cXSize * this->m_nSizePerTab;
        m_lppItems = this->m_lppItems;
        v14 = this->m_nXSize;
        v15 = v12 + v6 * v14;
        v16 = &m_lppItems[v12] + (itemPosb + v6) * v14;
        for ( i = &m_lppItems[v15]; i != v16; i += this->m_nXSize )
        {
          v18 = &i[v22];
          for ( j = &i[v7]; j != v18; ++j )
            *j = 0;
        }
        return 1;
      }
    }
  }
}

//----- (00418E90) --------------------------------------------------------
__int16 __thiscall Item::CArrayContainer::GetItemNum(Item::CArrayContainer *this, unsigned __int16 usProtoTypeID)
{
  __int16 result; // ax
  unsigned __int8 m_nXSize; // al
  int v4; // ebp
  Item::CItem **v5; // esi
  int v6; // edi
  Item::CItem *v7; // edx
  __int16 m_cNumOrDurability; // dx
  bool v9; // zf
  int v10; // [esp+0h] [ebp-10h]
  __int16 wCount; // [esp+4h] [ebp-Ch]
  int m_nYSize; // [esp+8h] [ebp-8h]
  int m_nTabNum; // [esp+Ch] [ebp-4h]

  result = 0;
  wCount = 0;
  if ( this->m_nTabNum )
  {
    v10 = 0;
    m_nTabNum = this->m_nTabNum;
    do
    {
      if ( this->m_nYSize )
      {
        m_nXSize = this->m_nXSize;
        v4 = 0;
        m_nYSize = this->m_nYSize;
        do
        {
          if ( m_nXSize )
          {
            v5 = &this->m_lppItems[v4 * m_nXSize] + v10 * this->m_nSizePerTab;
            v6 = m_nXSize;
            do
            {
              v7 = *v5;
              if ( *v5 && usProtoTypeID == v7->m_ItemData.m_usProtoTypeID )
              {
                if ( (v7->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
                  m_cNumOrDurability = v7->m_ItemData.m_cNumOrDurability;
                else
                  m_cNumOrDurability = 1;
                wCount += m_cNumOrDurability;
              }
              ++v5;
              --v6;
            }
            while ( v6 );
          }
          ++v4;
          --m_nYSize;
        }
        while ( m_nYSize );
      }
      v9 = m_nTabNum == 1;
      ++v10;
      --m_nTabNum;
    }
    while ( !v9 );
    return wCount;
  }
  return result;
}

//----- (00418F60) --------------------------------------------------------
void __thiscall Item::CArrayContainer::DumpItemInfo(Item::CArrayContainer *this)
{
  int v2; // edi
  int v3; // ebp
  int v4; // ebx
  unsigned __int8 m_nXSize; // al
  unsigned __int16 i; // di
  int v7; // eax
  int v8; // eax
  unsigned __int16 nHeight; // [esp+8h] [ebp-40Ch]
  int nTab; // [esp+Ch] [ebp-408h]
  char szLine[1024]; // [esp+10h] [ebp-404h] BYREF

  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "Item::CArrayContainer::DumpItemInfo",
    aDWorkRylSource_99,
    593,
    aCid0x08x_336,
    this->m_dwCID);
  v2 = 0;
  for ( nTab = 0; (unsigned __int16)v2 < this->m_nTabNum; nTab = ++v2 )
  {
    v3 = (unsigned __int16)v2;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::DumpItemInfo",
      aDWorkRylSource_99,
      597,
      aCid0x08x_61,
      this->m_dwCID,
      (unsigned __int16)v2);
    nHeight = 0;
    if ( this->m_nYSize )
    {
      do
      {
        v4 = _snprintf(szLine, 0x400u, aCid0x08x_234, this->m_dwCID);
        m_nXSize = this->m_nXSize;
        for ( i = 0; i < m_nXSize; ++i )
        {
          v7 = *((_DWORD *)&this->m_lppItems[i] + nHeight * m_nXSize + v3 * this->m_nSizePerTab);
          if ( v7 )
            v8 = *(unsigned __int16 *)(v7 + 16);
          else
            v8 = 0;
          v4 += _snprintf(&szLine[v4], 0x400u, " %5d ", v8);
          m_nXSize = this->m_nXSize;
        }
        CServerLog::DetailLog(&g_Log, LOG_ERROR, "Item::CArrayContainer::DumpItemInfo", aDWorkRylSource_99, 611, szLine);
        ++nHeight;
      }
      while ( nHeight < this->m_nYSize );
      v2 = nTab;
    }
  }
  Item::CItemContainer::DumpItemInfo(this);
}

//----- (004190E0) --------------------------------------------------------
Item::CItem *__thiscall Item::CListContainer::GetItem(Item::CListContainer *this, unsigned __int16 itemPos)
{
  Item::CItem *v2; // eax

  if ( (unsigned int)(itemPos >> 4) >= this->m_nMaxSize )
    return 0;
  v2 = this->m_lppItems[itemPos >> 4];
  return v2 == this->m_lpNullItem ? 0 : v2;
}

//----- (00419120) --------------------------------------------------------
char __thiscall Item::CListContainer::SetItem(
        Item::CListContainer *this,
        unsigned __int16 itemPos,
        Item::CItem *lpItem)
{
  Item::CItem **v3; // esi
  __int16 v4; // cx

  if ( (unsigned int)(itemPos >> 4) >= this->m_nMaxSize )
    return 0;
  v3 = &this->m_lppItems[itemPos >> 4];
  if ( *v3 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CListContainer::SetItem",
      aDWorkRylSource_99,
      688,
      aCid0x08x_45,
      this->m_dwCID);
    return 0;
  }
  v4 = itemPos & 0xF;
  *v3 = lpItem;
  if ( v4 == 10 || (lpItem->m_ItemData.m_ItemPos = (Item::ItemPos)itemPos, v4 != 6) )
    lpItem->m_itemPos_Real = (Item::ItemPos)itemPos;
  return 1;
}

//----- (004191A0) --------------------------------------------------------
bool __thiscall Item::CListContainer::TestItem(
        Item::CListContainer *this,
        unsigned __int16 itemPos,
        unsigned __int16 usProtoTypeID)
{
  return (unsigned int)(itemPos >> 4) < this->m_nMaxSize && !this->m_lppItems[itemPos >> 4];
}

//----- (004191D0) --------------------------------------------------------
char __thiscall Item::CListContainer::RemoveItem(Item::CListContainer *this, unsigned __int16 itemPos)
{
  Item::CItem **v2; // eax

  if ( (unsigned int)(itemPos >> 4) < this->m_nMaxSize )
  {
    v2 = &this->m_lppItems[itemPos >> 4];
    if ( *v2 )
    {
      *v2 = 0;
      return 1;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CListContainer::RemoveItem",
      aDWorkRylSource_99,
      721,
      aCid0x08x_183,
      this->m_dwCID);
  }
  return 0;
}

//----- (00419230) --------------------------------------------------------
void __thiscall Item::CNullItem::~CNullItem(Item::CNullItem *this)
{
  this->__vftable = (Item::CNullItem_vtbl *)&Item::CNullItem::`vftable';
  CSingleton<Item::CNullItem>::ms_pSingleton = 0;
  Item::CUseItem::~CUseItem((Item::CUseItem *)this);
}
// 4D7EAC: using guessed type void *Item::CNullItem::`vftable';

//----- (00419250) --------------------------------------------------------
void __thiscall Item::CNullItem::CNullItem(Item::CNullItem *this, const Item::ItemInfo *itemInfo)
{
  Item::CItem::CItem(this, 0LL, itemInfo);
  CSingleton<Item::CNullItem>::ms_pSingleton = this;
  this->__vftable = (Item::CNullItem_vtbl *)&Item::CNullItem::`vftable';
}
// 4D7EAC: using guessed type void *Item::CNullItem::`vftable';

//----- (00419280) --------------------------------------------------------
Item::CNullItem *__thiscall Item::CNullItem::`vector deleting destructor'(Item::CNullItem *this, char a2)
{
  Item::CNullItem::~CNullItem(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004192A0) --------------------------------------------------------
void __thiscall Item::CItemContainer::CItemContainer(Item::CItemContainer *this)
{
  this->__vftable = (Item::CItemContainer_vtbl *)&Item::CItemContainer::`vftable';
  this->m_lppItems = 0;
  this->m_lpNullItem = CSingleton<Item::CNullItem>::ms_pSingleton;
  this->m_dwCID = 0;
  this->m_nMaxSize = 0;
  this->m_usFlags = 0;
}
// 4D7EB8: using guessed type void *Item::CItemContainer::`vftable';

//----- (004192D0) --------------------------------------------------------
char __thiscall Item::CItemContainer::Initialize(
        Item::CItemContainer *this,
        unsigned int dwCID,
        unsigned __int16 nMaxSize)
{
  Item::CItem **v4; // edi
  unsigned __int16 m_nMaxSize; // si

  this->Destroy(this);
  this->m_dwCID = dwCID;
  this->m_nMaxSize = nMaxSize;
  v4 = (Item::CItem **)operator new[](4 * nMaxSize);
  this->m_lppItems = v4;
  if ( !v4 )
    return 0;
  m_nMaxSize = this->m_nMaxSize;
  if ( m_nMaxSize )
    memset(v4, 0, 4 * m_nMaxSize);
  return 1;
}

//----- (00419320) --------------------------------------------------------
void __thiscall Item::CItemContainer::ClearItems(Item::CItemContainer *this)
{
  Item::CItem **m_lppItems; // eax
  Item::CItem **v3; // esi
  Item::CItem **v4; // ebx
  Item::CItem *v5; // ecx
  unsigned __int16 m_nMaxSize; // ax
  Item::CItem **v7; // edi

  m_lppItems = this->m_lppItems;
  if ( m_lppItems )
  {
    v3 = this->m_lppItems;
    v4 = &m_lppItems[this->m_nMaxSize];
    if ( m_lppItems != v4 )
    {
      do
      {
        v5 = *v3;
        if ( *v3 && this->m_lpNullItem != v5 )
          ((void (__thiscall *)(Item::CItem *, int))v5->~Item::CItem)(v5, 1);
        ++v3;
      }
      while ( v3 != v4 );
    }
    m_nMaxSize = this->m_nMaxSize;
    v7 = this->m_lppItems;
    if ( m_nMaxSize )
      memset(v7, 0, 4 * m_nMaxSize);
  }
}

//----- (00419370) --------------------------------------------------------
void __thiscall Item::CItemContainer::Destroy(Item::CItemContainer *this)
{
  if ( this->m_lppItems )
  {
    Item::CItemContainer::ClearItems(this);
    operator delete[](this->m_lppItems);
    this->m_lppItems = 0;
  }
}

//----- (004193A0) --------------------------------------------------------
char __thiscall Item::CArrayContainer::Initialize(
        Item::CArrayContainer *this,
        unsigned int dwCID,
        unsigned __int8 nXSize,
        unsigned __int8 nYSize,
        unsigned __int8 nTabNum)
{
  Item::CArrayContainer_vtbl *v6; // edx
  unsigned __int16 v7; // ax
  Item::CItem **v8; // edi
  unsigned __int16 m_nMaxSize; // si

  v6 = this->__vftable;
  this->m_nTabNum = nTabNum;
  this->m_nYSize = nYSize;
  this->m_dwCID = dwCID;
  this->m_nXSize = nXSize;
  this->m_nSizePerTab = nYSize * nXSize;
  v6->Destroy(this);
  this->m_dwCID = dwCID;
  v7 = nTabNum * nYSize * nXSize;
  this->m_nMaxSize = v7;
  v8 = (Item::CItem **)operator new[](4 * v7);
  this->m_lppItems = v8;
  if ( !v8 )
    return 0;
  m_nMaxSize = this->m_nMaxSize;
  if ( m_nMaxSize )
    memset(v8, 0, 4 * m_nMaxSize);
  return 1;
}

//----- (00419430) --------------------------------------------------------
char __thiscall Item::CArrayContainer::TestItem(
        Item::CArrayContainer *this,
        unsigned int itemPos,
        unsigned __int16 usProtoTypeID)
{
  const Item::ItemInfo *ItemInfo; // eax
  int m_cXSize; // edi
  int v7; // ebp
  unsigned __int8 v8; // bl
  unsigned __int8 v9; // cl
  int v10; // edx
  int v11; // ecx
  int m_nXSize; // edi
  Item::CItem **m_lppItems; // edx
  int v14; // eax
  Item::CItem **v15; // edi
  Item::CItem **v16; // ecx
  Item::CItem **v17; // edx
  Item::CItem **v18; // eax
  int v19; // [esp+10h] [ebp-4h]
  unsigned int itemPosa; // [esp+18h] [ebp+4h]
  unsigned __int8 cXSize; // [esp+1Ch] [ebp+8h]
  int cXSizea; // [esp+1Ch] [ebp+8h]

  ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, usProtoTypeID);
  if ( ItemInfo )
  {
    m_cXSize = ItemInfo->m_DetailData.m_cXSize;
    v7 = (unsigned __int8)itemPos >> 4;
    cXSize = ItemInfo->m_DetailData.m_cYSize;
    v8 = BYTE1(itemPos) & 0xF;
    v9 = (itemPos >> 12) & 0xF;
    v19 = m_cXSize + v7;
    if ( this->m_nXSize < (unsigned int)(m_cXSize + v7)
      || (itemPosa = cXSize + v8, this->m_nYSize < itemPosa)
      || this->m_nTabNum <= v9 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Item::CArrayContainer::TestItem",
        aDWorkRylSource_99,
        310,
        aCid0x08x_194,
        this->m_dwCID,
        v7,
        this->m_nXSize,
        v8,
        this->m_nYSize,
        v9,
        this->m_nTabNum,
        m_cXSize,
        cXSize);
      return 0;
    }
    else
    {
      v10 = v9;
      v11 = v9 * this->m_nSizePerTab;
      m_nXSize = this->m_nXSize;
      cXSizea = v10;
      m_lppItems = this->m_lppItems;
      v14 = v11 + v8 * m_nXSize;
      v15 = &m_lppItems[v11] + itemPosa * m_nXSize;
      v16 = &m_lppItems[v14];
      if ( v16 == v15 )
      {
        return 1;
      }
      else
      {
        while ( 1 )
        {
          v17 = &v16[v19];
          v18 = &v16[v7];
          if ( v18 != v17 )
            break;
LABEL_10:
          v16 += this->m_nXSize;
          if ( v16 == v15 )
            return 1;
        }
        while ( !*v18 )
        {
          if ( ++v18 == v17 )
            goto LABEL_10;
        }
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Item::CArrayContainer::TestItem",
          aDWorkRylSource_99,
          328,
          aCid0x08xDDD,
          this->m_dwCID,
          v7,
          v8,
          cXSizea);
        return 0;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CArrayContainer::TestItem",
      aDWorkRylSource_99,
      295,
      aCid0x08x_79,
      this->m_dwCID,
      usProtoTypeID);
    return 0;
  }
}

//----- (004195E0) --------------------------------------------------------
Item::ItemPos *__thiscall Item::CArrayContainer::GetBlankPos(
        Item::CArrayContainer *this,
        Item::ItemPos *result,
        unsigned __int16 wProtoTypeID)
{
  Item::CArrayContainer *v3; // esi
  const Item::ItemInfo *ItemInfo; // eax
  unsigned __int8 m_cXSize; // cl
  unsigned __int8 m_cYSize; // dl
  unsigned __int8 m_nTabNum; // al
  unsigned __int8 m_nYSize; // dl
  unsigned __int8 v9; // cl
  int v10; // ebp
  unsigned __int8 m_nXSize; // al
  unsigned __int8 v12; // bl
  int v13; // edi
  int m_nSizePerTab; // ecx
  Item::CItem **m_lppItems; // esi
  int v16; // ecx
  int v17; // edx
  unsigned __int8 v18; // dl
  int v19; // eax
  unsigned __int8 v20; // bl
  Item::ItemPos *v21; // eax
  unsigned __int8 cHeight; // [esp+10h] [ebp-24h]
  unsigned __int8 cWidth; // [esp+11h] [ebp-23h]
  unsigned __int8 v24; // [esp+12h] [ebp-22h]
  unsigned __int8 cYSize; // [esp+13h] [ebp-21h]
  unsigned __int8 cXSize; // [esp+14h] [ebp-20h]
  unsigned __int8 v27; // [esp+15h] [ebp-1Fh]
  unsigned __int8 v28; // [esp+16h] [ebp-1Eh]
  unsigned __int8 v29; // [esp+17h] [ebp-1Dh]
  int v30; // [esp+18h] [ebp-1Ch]
  int v32; // [esp+20h] [ebp-14h]
  int v33; // [esp+28h] [ebp-Ch]
  int v34; // [esp+30h] [ebp-4h]
  unsigned __int8 wProtoTypeIDa; // [esp+3Ch] [ebp+8h]

  v3 = this;
  ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, wProtoTypeID);
  if ( ItemInfo )
  {
    m_cXSize = ItemInfo->m_DetailData.m_cXSize;
    m_cYSize = ItemInfo->m_DetailData.m_cYSize;
    m_nTabNum = v3->m_nTabNum;
    cXSize = m_cXSize;
    cYSize = m_cYSize;
    wProtoTypeIDa = 0;
    v29 = m_nTabNum;
    if ( m_nTabNum )
    {
      m_nYSize = v3->m_nYSize;
      v28 = m_nYSize;
      do
      {
        v9 = 0;
        cHeight = 0;
        if ( m_nYSize )
        {
          v33 = m_nYSize;
          do
          {
            v10 = v9;
            v32 = v9;
            v34 = v9 + cYSize;
            if ( v34 <= v33 )
            {
              m_nXSize = v3->m_nXSize;
              v12 = 0;
              cWidth = 0;
              v27 = m_nXSize;
              if ( m_nXSize )
              {
                v13 = m_nXSize;
                do
                {
                  v30 = cXSize + v12;
                  if ( v30 <= v13 )
                  {
                    m_nSizePerTab = v3->m_nSizePerTab;
                    m_lppItems = v3->m_lppItems;
                    v16 = wProtoTypeIDa * m_nSizePerTab;
                    v17 = (int)*(&m_lppItems[v12] + v16 + v10 * v13);
                    if ( v17 )
                    {
                      v12 += *(_BYTE *)(*(_DWORD *)(v17 + 4) + 5) - 1;
                    }
                    else
                    {
                      v18 = v12;
                      v24 = v12;
                      if ( v12 >= v30 )
                      {
LABEL_19:
                        v21 = result;
                        *result = (Item::ItemPos)((16 * (v12 + 16 * (cHeight + 16 * wProtoTypeIDa))) | 2);
                        return v21;
                      }
                      while ( 1 )
                      {
                        v19 = v32;
                        v20 = cHeight;
                        if ( v32 < v34 )
                          break;
LABEL_17:
                        v24 = ++v18;
                        if ( v18 >= v30 )
                        {
                          v12 = cWidth;
                          goto LABEL_19;
                        }
                      }
                      while ( !*(&m_lppItems[v16] + v18 + v13 * v19) )
                      {
                        v19 = ++v20;
                        if ( v20 >= v34 )
                        {
                          v18 = v24;
                          goto LABEL_17;
                        }
                      }
                      v12 = cWidth;
                      v10 = v32;
                    }
                    v3 = this;
                  }
                  cWidth = ++v12;
                }
                while ( v12 < v27 );
                v9 = cHeight;
                m_nYSize = v28;
              }
            }
            cHeight = ++v9;
          }
          while ( v9 < m_nYSize );
          m_nTabNum = v29;
        }
        ++wProtoTypeIDa;
      }
      while ( wProtoTypeIDa < m_nTabNum );
    }
  }
  v21 = result;
  *result = 0;
  return v21;
}

//----- (004197C0) --------------------------------------------------------
char __thiscall Item::CArrayContainer::SerializeIn(
        Item::CArrayContainer *this,
        const char *szItemBuffer_In,
        unsigned int dwBufferSize_In)
{
  int v4; // ebp
  const char *v5; // esi
  Item::CItem *v6; // eax
  Item::CItem *v7; // edi
  unsigned int v8; // eax
  unsigned int nBufferSize; // [esp+8h] [ebp-50h]
  Item::CItemFactory *ItemFactory; // [esp+Ch] [ebp-4Ch]
  unsigned int itemPos; // [esp+10h] [ebp-48h]
  char szDest[64]; // [esp+14h] [ebp-44h] BYREF

  v4 = 0;
  nBufferSize = dwBufferSize_In;
  for ( ItemFactory = CSingleton<Item::CItemFactory>::ms_pSingleton; nBufferSize; nBufferSize -= v8 )
  {
    v5 = &szItemBuffer_In[v4];
    dwBufferSize_In = nBufferSize;
    v6 = Item::CItemFactory::CreateItem(ItemFactory, &szItemBuffer_In[v4], &dwBufferSize_In);
    v7 = v6;
    if ( v6 )
    {
      LOWORD(itemPos) = v6->m_ItemData.m_ItemPos;
      if ( !Item::CArrayContainer::SetItem(this, itemPos, v6) )
      {
        dwBufferSize_In = LogErrorItem(this->m_dwCID, &szDetail, v5);
        ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
      }
      v8 = dwBufferSize_In;
    }
    else
    {
      Math::Convert::Hex64ToStr(szDest, *(_QWORD *)v5);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "LogErrorItem",
        aDWorkRylSource_99,
        45,
        "CID:0x%08x %s. UID:%s, ProtoTypeID:%d",
        this->m_dwCID,
        byte_4D7F30,
        szDest,
        *((unsigned __int16 *)v5 + 4));
      v8 = *((unsigned __int8 *)v5 + 12);
    }
    v4 += v8;
  }
  return 1;
}
// 41986F: variable 'itemPos' is possibly undefined

//----- (004198D0) --------------------------------------------------------
char __thiscall Item::CListContainer::SerializeIn(
        Item::CListContainer *this,
        const char *szItemBuffer_In,
        unsigned int dwBufferSize_In)
{
  int v4; // ebp
  const char *v5; // esi
  Item::CItem *v6; // eax
  Item::CItem *v7; // edi
  unsigned int v8; // eax
  unsigned int nBufferSize; // [esp+8h] [ebp-50h]
  Item::CItemFactory *ItemFactory; // [esp+Ch] [ebp-4Ch]
  char szDest[64]; // [esp+14h] [ebp-44h] BYREF

  v4 = 0;
  nBufferSize = dwBufferSize_In;
  for ( ItemFactory = CSingleton<Item::CItemFactory>::ms_pSingleton; nBufferSize; nBufferSize -= v8 )
  {
    v5 = &szItemBuffer_In[v4];
    dwBufferSize_In = nBufferSize;
    v6 = Item::CItemFactory::CreateItem(ItemFactory, &szItemBuffer_In[v4], &dwBufferSize_In);
    v7 = v6;
    if ( v6 )
    {
      if ( !Item::CListContainer::SetItem(this, *(_WORD *)&v6->m_ItemData.m_ItemPos, v6) )
      {
        dwBufferSize_In = LogErrorItem(this->m_dwCID, &szDetail, v5);
        ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
      }
      v8 = dwBufferSize_In;
    }
    else
    {
      Math::Convert::Hex64ToStr(szDest, *(_QWORD *)v5);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "LogErrorItem",
        aDWorkRylSource_99,
        45,
        "CID:0x%08x %s. UID:%s, ProtoTypeID:%d",
        this->m_dwCID,
        byte_4D7F30,
        szDest,
        *((unsigned __int16 *)v5 + 4));
      v8 = *((unsigned __int8 *)v5 + 12);
    }
    v4 += v8;
  }
  return 1;
}

//----- (004199E0) --------------------------------------------------------
void __thiscall Item::CArrayContainer::~CArrayContainer(Item::CListContainer *this)
{
  Item::CItem **m_lppItems; // eax

  m_lppItems = this->m_lppItems;
  this->__vftable = (Item::CListContainer_vtbl *)&Item::CItemContainer::`vftable';
  if ( m_lppItems )
  {
    Item::CItemContainer::ClearItems(this);
    operator delete[](this->m_lppItems);
    this->m_lppItems = 0;
  }
}
// 4D7EB8: using guessed type void *Item::CItemContainer::`vftable';

//----- (00419A10) --------------------------------------------------------
void __thiscall Item::CArrayContainer::CArrayContainer(Item::CArrayContainer *this)
{
  this->__vftable = (Item::CArrayContainer_vtbl *)&Item::CItemContainer::`vftable';
  this->m_lppItems = 0;
  this->m_lpNullItem = CSingleton<Item::CNullItem>::ms_pSingleton;
  this->m_dwCID = 0;
  this->m_nMaxSize = 0;
  this->m_usFlags = 0;
  this->__vftable = (Item::CArrayContainer_vtbl *)&Item::CArrayContainer::`vftable';
}
// 4D7EB8: using guessed type void *Item::CItemContainer::`vftable';
// 4D7F74: using guessed type void *Item::CArrayContainer::`vftable';

//----- (00419A40) --------------------------------------------------------
void __thiscall Item::CListContainer::CListContainer(Item::CListContainer *this)
{
  this->__vftable = (Item::CListContainer_vtbl *)&Item::CItemContainer::`vftable';
  this->m_lppItems = 0;
  this->m_lpNullItem = CSingleton<Item::CNullItem>::ms_pSingleton;
  this->m_dwCID = 0;
  this->m_nMaxSize = 0;
  this->m_usFlags = 0;
  this->__vftable = (Item::CListContainer_vtbl *)&Item::CListContainer::`vftable';
}
// 4D7EB8: using guessed type void *Item::CItemContainer::`vftable';
// 4D7FA8: using guessed type void *Item::CListContainer::`vftable';

//----- (00419A70) --------------------------------------------------------
Item::CItemContainer *__thiscall Item::CItemContainer::`vector deleting destructor'(
        Item::CItemContainer *this,
        char a2)
{
  Item::CItem **m_lppItems; // eax

  m_lppItems = this->m_lppItems;
  this->__vftable = (Item::CItemContainer_vtbl *)&Item::CItemContainer::`vftable';
  if ( m_lppItems )
  {
    Item::CItemContainer::ClearItems(this);
    operator delete[](this->m_lppItems);
    this->m_lppItems = 0;
  }
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D7EB8: using guessed type void *Item::CItemContainer::`vftable';

//----- (00419AB0) --------------------------------------------------------
Item::CListContainer *__thiscall Item::CListContainer::`vector deleting destructor'(
        Item::CListContainer *this,
        char a2)
{
  Item::CArrayContainer::~CArrayContainer(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00419AD0) --------------------------------------------------------
void __thiscall __noreturn std::vector<Item::ItemGarbage>::_Xlen(std::vector<Item::ItemGarbage> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00419B40) --------------------------------------------------------
void __thiscall std::vector<Item::ItemGarbage>::_Insert_n(
        std::vector<Item::ItemGarbage> *this,
        std::vector<Item::ItemGarbage>::iterator _Where,
        unsigned int _Count,
        const Item::ItemGarbage *_Val)
{
  unsigned int m_dwRemainNum; // edx
  Item::ItemGarbage *Myfirst; // ecx
  unsigned int v7; // eax
  int v9; // edx
  int v10; // edx
  unsigned int v11; // eax
  int v12; // edx
  int v13; // eax
  Item::ItemGarbage *v14; // edi
  Item::ItemGarbage *v15; // ecx
  int v16; // eax
  int v17; // ebx
  Item::ItemGarbage *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  Item::ItemGarbage *v22; // ebx
  std::pair<unsigned long,unsigned long> *v23; // ebx
  Item::ItemGarbage *v24; // [esp-18h] [ebp-40h]
  Item::ItemGarbage *v25; // [esp-Ch] [ebp-34h]
  unsigned int v26; // [esp-8h] [ebp-30h]
  int v27; // [esp+0h] [ebp-28h] BYREF
  Item::ItemGarbage _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  Item::ItemGarbage *_Newvec; // [esp+14h] [ebp-14h]
  int *v30; // [esp+18h] [ebp-10h]
  int v31; // [esp+24h] [ebp-4h]
  std::vector<Item::ItemGarbage>::iterator _Wherea; // [esp+30h] [ebp+8h]
  unsigned int _Counta; // [esp+34h] [ebp+Ch]
  std::pair<unsigned long,unsigned long> *_Valb; // [esp+38h] [ebp+10h]
  std::pair<unsigned long,unsigned long> *_Vala; // [esp+38h] [ebp+10h]

  m_dwRemainNum = _Val->m_dwRemainNum;
  _Tmp.m_lpItem = _Val->m_lpItem;
  Myfirst = this->_Myfirst;
  v30 = &v27;
  _Tmp.m_dwRemainNum = m_dwRemainNum;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x1FFFFFFF - v9 < _Count )
      std::vector<Item::ItemGarbage>::_Xlen(this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = this->_Mylast;
      v20 = Mylast - _Where._Myptr < _Count;
      v21 = 8 * _Count;
      _Wherea._Myptr = (Item::ItemGarbage *)(8 * _Count);
      _Vala = (std::pair<unsigned long,unsigned long> *)Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)Mylast,
          (std::pair<unsigned long,unsigned long> *)&_Where._Myptr[v21 / 8]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = this->_Mylast;
        v31 = 2;
        std::vector<std::pair<enum eStdFunc,int>>::_Ufill(
          (std::vector<std::pair<unsigned long,unsigned long>> *)this,
          (std::pair<unsigned long,unsigned long> *)v25,
          v26,
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
        v22 = (Item::ItemGarbage *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)v22 - (char *)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
      else
      {
        v23 = (std::pair<unsigned long,unsigned long> *)&Mylast[v21 / 0xFFFFFFF8];
        this->_Mylast = (Item::ItemGarbage *)std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                                               (std::pair<unsigned long,unsigned long> *)&Mylast[v21 / 0xFFFFFFF8],
                                               (std::pair<unsigned long,unsigned long> *)Mylast,
                                               (std::pair<unsigned long,unsigned long> *)Mylast);
        std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          v23,
          _Vala);
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
    }
    else
    {
      if ( 0x1FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      _Counta = v11;
      v14 = (Item::ItemGarbage *)operator new((tagHeader *)(8 * v11));
      v24 = this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Valb = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                (std::pair<unsigned long,unsigned long> *)v24,
                (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
                (std::pair<unsigned long,unsigned long> *)v14);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        _Valb,
        _Count,
        (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
        (std::pair<unsigned long,unsigned long> *)this->_Mylast,
        &_Valb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[_Counta];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (00419D90) --------------------------------------------------------
void __thiscall std::vector<Item::ItemGarbage>::push_back(
        std::vector<Item::ItemGarbage> *this,
        const Item::ItemGarbage *_Val)
{
  Item::ItemGarbage *Myfirst; // edx
  unsigned int v4; // ecx
  Item::ItemGarbage *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
      (std::pair<unsigned long,unsigned long> *)Mylast,
      1u,
      (const std::pair<unsigned long,unsigned long> *)_Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Item::ItemGarbage>::_Insert_n(this, (std::vector<Item::ItemGarbage>::iterator)this->_Mylast, 1u, _Val);
  }
}

//----- (00419E00) --------------------------------------------------------
char __thiscall Item::CArrayContainer::DisappearItem(
        Item::CArrayContainer *this,
        int wItemID,
        unsigned __int16 wItemNum,
        std::vector<Item::ItemGarbage> *vecItemGarbage)
{
  unsigned __int8 m_nXSize; // al
  unsigned __int16 v6; // bx
  Item::CItem *v7; // edi
  __int16 m_cNumOrDurability; // ax
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  const Item::ItemGarbage *v11; // eax
  const Item::ItemGarbage *v13; // eax
  const Item::ItemGarbage *v14; // eax
  unsigned int m_dwCID; // [esp-4h] [ebp-24h]
  __int16 itemNum; // [esp+Ch] [ebp-14h]
  unsigned __int16 nHeight; // [esp+10h] [ebp-10h]
  unsigned __int16 nTab; // [esp+14h] [ebp-Ch]
  Item::ItemGarbage v19; // [esp+18h] [ebp-8h] BYREF

  if ( wItemNum > this->GetItemNum(this, wItemID) )
    return 0;
  itemNum = wItemNum;
  nTab = 0;
  if ( !this->m_nTabNum )
    return 0;
  while ( 1 )
  {
    nHeight = 0;
    if ( this->m_nYSize )
      break;
LABEL_18:
    if ( ++nTab >= this->m_nTabNum )
      return 0;
  }
  while ( 1 )
  {
    m_nXSize = this->m_nXSize;
    v6 = 0;
    if ( m_nXSize )
      break;
LABEL_17:
    if ( ++nHeight >= this->m_nYSize )
      goto LABEL_18;
  }
  while ( 1 )
  {
    v7 = (Item::CItem *)*((_DWORD *)&this->m_lppItems[v6] + nTab * this->m_nSizePerTab + nHeight * m_nXSize);
    if ( !v7 || (_WORD)wItemID != v7->m_ItemData.m_usProtoTypeID )
      goto LABEL_16;
    if ( (v7->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8 )
      m_cNumOrDurability = v7->m_ItemData.m_cNumOrDurability;
    else
      m_cNumOrDurability = 1;
    m_dwCID = this->m_dwCID;
    itemNum -= m_cNumOrDurability;
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, m_dwCID);
    if ( !Character || !Character->m_lpGameClientDispatch )
      return 0;
    if ( itemNum <= 0 )
      break;
    Item::ItemGarbage::ItemGarbage(&v19, v7, 0);
    std::vector<Item::ItemGarbage>::push_back(vecItemGarbage, v11);
LABEL_16:
    m_nXSize = this->m_nXSize;
    if ( ++v6 >= m_nXSize )
      goto LABEL_17;
  }
  if ( itemNum )
  {
    Item::ItemGarbage::ItemGarbage(&v19, v7, (unsigned __int8)-(char)itemNum);
    std::vector<Item::ItemGarbage>::push_back(vecItemGarbage, v14);
    return 1;
  }
  else
  {
    Item::ItemGarbage::ItemGarbage(&v19, v7, 0);
    std::vector<Item::ItemGarbage>::push_back(vecItemGarbage, v13);
    return 1;
  }
}
// 419EFC: conditional instruction was optimized away because %itemNum.2<0
// 419EF0: variable 'v11' is possibly undefined
// 419F58: variable 'v13' is possibly undefined
// 419F80: variable 'v14' is possibly undefined

//----- (00419F90) --------------------------------------------------------
void __thiscall Item::CItemFactory::CreateItem(Item::CItemFactory *this, const Item::ItemInfo *itemInfo)
{
  Item::CEquipment *v3; // eax
  int v4; // edi
  Item::CUseItem *v5; // eax
  int v6; // edi
  Item::CItem *v7; // eax
  int v8; // edi
  unsigned __int64 m_nCurrentUID; // [esp-Ch] [ebp-28h]
  unsigned __int64 v10; // [esp-Ch] [ebp-28h]
  unsigned __int64 v11; // [esp-Ch] [ebp-28h]

  if ( (itemInfo->m_DetailData.m_dwFlags & 1) == 1 )
  {
    v3 = (Item::CEquipment *)operator new((tagHeader *)0xA0);
    if ( v3 )
    {
      m_nCurrentUID = this->m_nCurrentUID;
      v4 = __CFADD__(LODWORD(this->m_nCurrentUID)++, 1) + HIDWORD(this->m_nCurrentUID);
      HIDWORD(this->m_nCurrentUID) = v4;
      Item::CEquipment::CEquipment(v3, m_nCurrentUID, itemInfo);
    }
  }
  else if ( (itemInfo->m_DetailData.m_dwFlags & 2) == 2 )
  {
    v5 = (Item::CUseItem *)operator new((tagHeader *)0x24);
    if ( v5 )
    {
      v10 = this->m_nCurrentUID;
      v6 = __CFADD__(LODWORD(this->m_nCurrentUID)++, 1) + HIDWORD(this->m_nCurrentUID);
      HIDWORD(this->m_nCurrentUID) = v6;
      Item::CUseItem::CUseItem(v5, v10, itemInfo);
    }
  }
  else
  {
    v7 = (Item::CItem *)operator new((tagHeader *)0x24);
    if ( v7 )
    {
      v11 = this->m_nCurrentUID;
      v8 = __CFADD__(LODWORD(this->m_nCurrentUID)++, 1) + HIDWORD(this->m_nCurrentUID);
      HIDWORD(this->m_nCurrentUID) = v8;
      Item::CItem::CItem(v7, v11, itemInfo);
    }
  }
}

//----- (0041A0D0) --------------------------------------------------------
void __thiscall Item::CItemFactory::CItemFactory(Item::CItemFactory *this)
{
  CSingleton<Item::CItemFactory>::ms_pSingleton = this;
  this->m_nCurrentUID = 0LL;
}

//----- (0041A0F0) --------------------------------------------------------
void __thiscall Item::CItemFactory::~CItemFactory(Item::CItemFactory *this)
{
  CSingleton<Item::CItemFactory>::ms_pSingleton = 0;
}

//----- (0041A100) --------------------------------------------------------
void __thiscall Item::CItemFactory::CreateItem(Item::CItemFactory *this, unsigned __int16 usProtoTypeID)
{
  const Item::ItemInfo *ItemInfo; // eax

  ItemInfo = Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, usProtoTypeID);
  if ( ItemInfo )
    Item::CItemFactory::CreateItem(this, ItemInfo);
  else
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Item::CItemFactory::CreateItem",
      aDWorkRylSource_27,
      55,
      (char *)&byte_4D8088,
      usProtoTypeID);
}

//----- (0041A150) --------------------------------------------------------
Item::CItem *__thiscall Item::CItemFactory::CreateItem(
        Item::CItemFactory *this,
        const char *lpSerializedItem_In,
        unsigned int *nParseLength_InOut)
{
  Item::CItem *result; // eax
  void (__thiscall ***v4)(_DWORD, int); // eax
  void (__thiscall ***v5)(_DWORD, int); // esi

  result = 0;
  if ( lpSerializedItem_In )
  {
    Item::CItemFactory::CreateItem(this, *((_WORD *)lpSerializedItem_In + 4));
    v5 = v4;
    if ( v4 )
    {
      if ( !((unsigned __int8 (__thiscall *)(void (__thiscall ***)(_DWORD, int), const char *, unsigned int *))(*v4)[2])(
              v4,
              lpSerializedItem_In,
              nParseLength_InOut) )
      {
        (**v5)(v5, 1);
        return 0;
      }
    }
    return (Item::CItem *)v5;
  }
  return result;
}
// 41A166: variable 'v4' is possibly undefined

//----- (0041A190) --------------------------------------------------------
void __thiscall Item::CEquipmentsContainer::GetEquipmentView(
        Item::CEquipmentsContainer *this,
        unsigned __int16 *usProtoTypeArray,
        signed int nStartPos,
        int nCopyNum)
{
  signed int v4; // eax
  signed int v5; // esi
  Item::CItem *v7; // edx

  v4 = nStartPos;
  v5 = nStartPos + nCopyNum;
  if ( nStartPos + nCopyNum > 16 )
    v5 = 16;
  if ( nStartPos < v5 )
  {
    do
    {
      v7 = this->m_lppItems[v4];
      if ( v7 )
        *usProtoTypeArray = v7->m_ItemData.m_usProtoTypeID;
      else
        *usProtoTypeArray = 0;
      ++v4;
      ++usProtoTypeArray;
    }
    while ( v4 < v5 );
  }
}

//----- (0041A1E0) --------------------------------------------------------
void __thiscall CCharacter::SetDispatcher(CCharacter *this, CGameClientDispatch *lpGameClientDispatch)
{
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  CCharacter *m_lpOtherOwner; // eax
  Item::CStallContainer *p_m_Stall; // esi
  CCreatureManager *Instance; // eax

  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::SetDispatcher",
    aDWorkRylSource_96,
    96,
    aUidDCid0x08x0x_8,
    this->m_dwUID,
    this->m_dwCID,
    this,
    this->m_lpGameClientDispatch,
    lpGameClientDispatch);
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    CGameClientDispatch::SetCharacter(m_lpGameClientDispatch, 0);
    if ( (this->m_cOperationFlags & 2) == 0 )
      CGameClientDispatch::Disconnect(this->m_lpGameClientDispatch);
  }
  this->m_lpGameClientDispatch = lpGameClientDispatch;
  if ( lpGameClientDispatch )
  {
    this->m_bLogout = 0;
    this->m_dwUID = lpGameClientDispatch->m_dwUID;
    CGameClientDispatch::SetCharacter(lpGameClientDispatch, this);
  }
  else if ( !this->m_bLogout )
  {
    if ( strcmp(this->m_Stall.m_strStallName, szLoseCharName) )
    {
      Item::CStallContainer::Close(&this->m_Stall);
      Item::CStallContainer::SendCharStallOpen(&this->m_Stall, (char *)szLoseCharName);
    }
    m_lpOtherOwner = this->m_Stall.m_lpOtherOwner;
    if ( m_lpOtherOwner )
    {
      p_m_Stall = &m_lpOtherOwner->m_Stall;
      Item::CStallContainer::Leave(&m_lpOtherOwner->m_Stall, this);
      Item::CStallContainer::SendCharStallEnter(p_m_Stall, this->m_dwCID, 0);
    }
    this->m_bLogout = 1;
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::EnqueueLogout(Instance, this);
  }
}

//----- (0041A2F0) --------------------------------------------------------
char __thiscall CCharacter::ControlOption(CCharacter *this, RejectOption Reject, bool bLogin)
{
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-14h] BYREF
  int v7; // [esp+14h] [ebp-4h]

  this->m_RejectOption = Reject;
  if ( !bLogin )
  {
    DispatchTable = CChatDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
    v7 = 0;
    if ( StoragelpChatDispatch.m_lpDispatch
      && !CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], this) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::ControlOption",
        aDWorkRylSource_96,
        215,
        aUidDCidD,
        this->m_dwUID,
        this->m_dwCID);
    }
    v7 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
  }
  return 1;
}

//----- (0041A3B0) --------------------------------------------------------
unsigned __int8 __thiscall CCharacter::GetNation(CCharacter *this)
{
  if ( !this->m_DBData.m_Info.Nationality )
    return 1;
  if ( this->m_DBData.m_Info.Nationality == 1 )
    return 2;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCharacter::GetNation",
    aDWorkRylSource_96,
    244,
    aCid0x08x_46,
    this->m_dwCID,
    this->m_DBData.m_Info.Nationality);
  return 3;
}

//----- (0041A400) --------------------------------------------------------
void __thiscall CCharacter::SetPID(CCharacter *this, unsigned int dwPID)
{
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-14h] BYREF
  int v5; // [esp+14h] [ebp-4h]

  this->m_DBData.m_Info.Party = dwPID;
  DispatchTable = CChatDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
  v5 = 0;
  if ( StoragelpChatDispatch.m_lpDispatch
    && !CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SetPID",
      aDWorkRylSource_96,
      260,
      aUidDCidD,
      this->m_dwUID,
      this->m_dwCID);
  }
  v5 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
}

//----- (0041A4B0) --------------------------------------------------------
void __thiscall CCharacter::SetGID(CCharacter *this, unsigned int dwGID)
{
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-14h] BYREF
  int v5; // [esp+14h] [ebp-4h]

  this->m_DBData.m_Info.Guild = dwGID;
  DispatchTable = CChatDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
  v5 = 0;
  if ( StoragelpChatDispatch.m_lpDispatch
    && !CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], this) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SetGID",
      aDWorkRylSource_96,
      277,
      aUidDCidD,
      this->m_dwUID,
      this->m_dwCID);
  }
  v5 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
}

//----- (0041A560) --------------------------------------------------------
void __thiscall CCharacter::SetFame(CCharacter *this, unsigned int dwFame)
{
  char v3; // al
  CCharacter_vtbl *v4; // edx
  char v5; // bl
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // eax
  unsigned int v9; // [esp-4h] [ebp-Ch]
  char dwFamea; // [esp+Ch] [ebp+4h]

  v3 = this->GetEliteBonus(this);
  v4 = this->__vftable;
  this->m_DBData.m_Info.Fame = dwFame;
  v5 = v3;
  dwFamea = v4->GetEliteBonus(this);
  if ( dwFamea != v5 )
  {
    m_lpGameClientDispatch = this->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendCharEliteBonus(&m_lpGameClientDispatch->m_SendStream, dwFamea);
  }
  v9 = this->GetGID(this);
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v9);
  if ( Guild )
    Guild::CGuild::UpdateMemberInfo(Guild, this->m_dwCID, this->m_DBData.m_Info.Fame, 2u);
}

//----- (0041A5D0) --------------------------------------------------------
unsigned __int64 __thiscall Position::GetDistance(Position *this, const Position *rhs)
{
  double v2; // st6
  double v3; // st5
  double v4; // st3

  v2 = this->m_fPointY - rhs->m_fPointY;
  v3 = this->m_fPointX - rhs->m_fPointX;
  v4 = this->m_fPointZ - rhs->m_fPointZ;
  return (unsigned __int64)sqrt(v3 * v3 + v2 * v2 + v4 * v4);
}

//----- (0041A610) --------------------------------------------------------
void __thiscall CCharacter::~CCharacter(CCharacter *this)
{
  int v2; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // ecx
  bool m_bLogout; // al
  CCharacter *m_lpOtherOwner; // eax
  Item::CStallContainer *p_m_Stall; // esi
  CCreatureManager *Instance; // eax

  this->__vftable = (CCharacter_vtbl *)&CCharacter::`vftable';
  v2 = 0;
  while ( this->m_bPadding[v2] == 101 )
  {
    if ( ++v2 >= 2 )
      goto LABEL_6;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_SYSERR,
    "CCharacter::~CCharacter",
    aDWorkRylSource_96,
    67,
    aCid0x08x_26,
    this->m_dwCID);
LABEL_6:
  *(_WORD *)this->m_bPadding = 26471;
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CCharacter::SetDispatcher",
    aDWorkRylSource_96,
    96,
    aUidDCid0x08x0x_8,
    this->m_dwUID,
    this->m_dwCID,
    this,
    this->m_lpGameClientDispatch,
    0);
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    CGameClientDispatch::SetCharacter(m_lpGameClientDispatch, 0);
    if ( (this->m_cOperationFlags & 2) == 0 )
      CGameClientDispatch::Disconnect(this->m_lpGameClientDispatch);
  }
  m_bLogout = this->m_bLogout;
  this->m_lpGameClientDispatch = 0;
  if ( !m_bLogout )
  {
    if ( strcmp(this->m_Stall.m_strStallName, szLoseCharName) )
    {
      Item::CStallContainer::Close(&this->m_Stall);
      Item::CStallContainer::SendCharStallOpen(&this->m_Stall, (char *)szLoseCharName);
    }
    m_lpOtherOwner = this->m_Stall.m_lpOtherOwner;
    if ( m_lpOtherOwner )
    {
      p_m_Stall = &m_lpOtherOwner->m_Stall;
      Item::CStallContainer::Leave(&m_lpOtherOwner->m_Stall, this);
      Item::CStallContainer::SendCharStallEnter(p_m_Stall, this->m_dwCID, 0);
    }
    this->m_bLogout = 1;
    Instance = CCreatureManager::GetInstance();
    CCreatureManager::EnqueueLogout(Instance, this);
  }
  std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>(&this->m_banList);
  CFriendList::~CFriendList(&this->m_friendList);
  Item::CStallContainer::~CStallContainer(&this->m_Stall);
  Item::CDepositContainer::~CDepositContainer(&this->m_Deposit);
  Item::CExchangeContainer::~CExchangeContainer(&this->m_Exchange);
  Item::CArrayContainer::~CArrayContainer(&this->m_ExtraSpace);
  Item::CEquipmentsContainer::~CEquipmentsContainer(&this->m_Equipments);
  Item::CArrayContainer::~CArrayContainer((Item::CListContainer *)&this->m_Inventory);
  CAggresiveCreature::~CAggresiveCreature(this);
}
// 4D8328: using guessed type void *CCharacter::`vftable';

//----- (0041A810) --------------------------------------------------------
bool __thiscall CCharacter::Initialize(CCharacter *this, CGameClientDispatch *lpGameClientDispatch)
{
  CCharacter::SetDispatcher(this, lpGameClientDispatch);
  return Item::CArrayContainer::Initialize(&this->m_Inventory, this->m_dwCID, 6u, 6u, 3u)
      && Item::CEquipmentsContainer::Initialize(&this->m_Equipments, this, 0x10u)
      && Item::CItemContainer::Initialize(&this->m_ExtraSpace, this->m_dwCID, 0xEu)
      && Item::CExchangeContainer::Initialize((Item::CStallContainer *)&this->m_Exchange, this, 8u, 4u)
      && Item::CDepositContainer::Initialize(&this->m_Deposit, this, 8u, 0xCu, 4u)
      && Item::CExchangeContainer::Initialize(&this->m_Stall, this, 0xAu, 8u) != 0;
}

//----- (0041A8B0) --------------------------------------------------------
BOOL __thiscall CCharacter::BindPositionToNPC(CCharacter *this, unsigned int dwNPCID)
{
  unsigned __int8 v3; // bl
  CMsgProcessMgr *Instance; // eax
  CMsgProc *Castle; // eax
  CMsgProc *v6; // edi
  double v7; // st6
  double v8; // st5
  double v9; // st3
  CServerSetup *v10; // eax
  CMsgProc_vtbl *ServerZone; // ebp
  VirtualArea::CVirtualAreaMgr *v12; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  float m_fPointY; // edx
  float m_fPointZ; // eax
  unsigned __int16 m_wMapIndex; // [esp-8h] [ebp-14h]

  v3 = 0;
  Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
  Castle = Castle::CCastleMgr::GetCastle(Instance, dwNPCID);
  v6 = Castle;
  if ( Castle
    && LOBYTE(Castle[12].__vftable) == 1
    && (v7 = *(float *)&Castle[2].__vftable - this->m_CurrentPos.m_fPointY,
        v8 = *(float *)&Castle[1].__vftable - this->m_CurrentPos.m_fPointX,
        v9 = *(float *)&Castle[3].__vftable - this->m_CurrentPos.m_fPointZ,
        (unsigned int)(unsigned __int64)sqrt(v8 * v8 + v7 * v7 + v9 * v9) < 0xF) )
  {
    v10 = CServerSetup::GetInstance();
    ServerZone = (CMsgProc_vtbl *)(char)CServerSetup::GetServerZone(v10);
    if ( this->m_CellPos.m_wMapIndex )
    {
      m_wMapIndex = this->m_CellPos.m_wMapIndex;
      v12 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v12, m_wMapIndex);
      if ( VirtualArea )
        ServerZone = (CMsgProc_vtbl *)VirtualArea::CVirtualArea::GetVirtualZone(VirtualArea);
    }
    if ( v6[10].__vftable == ServerZone )
    {
      m_fPointY = this->m_CurrentPos.m_fPointY;
      m_fPointZ = this->m_CurrentPos.m_fPointZ;
      this->m_DBData.m_Pos.SavePoint.fPointX = this->m_CurrentPos.m_fPointX;
      this->m_DBData.m_Pos.SavePoint.fPointY = m_fPointY;
      this->m_DBData.m_Pos.SavePoint.fPointZ = m_fPointZ;
    }
  }
  else
  {
    v3 = 1;
  }
  GAMELOG::LogCharBindPos(this, dwNPCID, v3);
  return v3 == 0;
}

//----- (0041A990) --------------------------------------------------------
void __thiscall CCharacter::CCharacter(CCharacter *this, unsigned int dwCID)
{
  Quest::ExecutingQuest *m_ExecutingQuest; // edi
  unsigned int dwCIDa; // [esp+24h] [ebp+4h]

  CAggresiveCreature::CAggresiveCreature(this, dwCID);
  this->__vftable = (CCharacter_vtbl *)&CCharacter::`vftable';
  CharacterFightInfo::CharacterFightInfo(&this->m_FightInfo);
  this->m_PeaceMode.m_bPeace = 0;
  this->m_PeaceMode.m_FileTime.dwHighDateTime = 0;
  this->m_PeaceMode.m_FileTime.dwLowDateTime = 0;
  Item::CArrayContainer::CArrayContainer(&this->m_Inventory);
  Item::CEquipmentsContainer::CEquipmentsContainer(&this->m_Equipments);
  Item::CListContainer::CListContainer(&this->m_ExtraSpace);
  Item::CExchangeContainer::CExchangeContainer(&this->m_Exchange);
  Item::CDepositContainer::CDepositContainer(&this->m_Deposit);
  Item::CStallContainer::CStallContainer(&this->m_Stall);
  CFriendList::CFriendList(&this->m_friendList, dwCID, CSingleton<CXRefFriends>::ms_pSingleton);
  std::vector<enum Item::ItemType::Type>::vector<enum Item::ItemType::Type>(&this->m_banList);
  m_ExecutingQuest = this->m_ExecutingQuest;
  dwCIDa = 10;
  do
  {
    Quest::ExecutingQuest::ExecutingQuest(m_ExecutingQuest++);
    --dwCIDa;
  }
  while ( dwCIDa );
  SKILL::SKILL(&this->m_DBData.m_Skill);
  QUICK::QUICK(&this->m_DBData.m_Quick);
  this->m_RejectOption.m_wReject = 0;
  this->m_dwUID = 0;
  this->m_eCellLoginStatus = NONE;
  this->m_lpGameClientDispatch = 0;
  this->m_lpSummonee = 0;
  this->m_dwLastUpdateExTime = 0;
  this->m_dwRideArmsCID = 0;
  this->m_cConsumeMPCount = 0;
  this->m_cOperationFlags = 0;
  this->m_cHandPos = 0;
  this->m_cMoveUpdateExCount = 0;
  this->m_cAttackableCreatureType = 0;
  this->m_nLogoutCount = 5;
  this->m_nDBUpdateCount = 180;
  this->m_dwRespawnSpeed = 100;
  *(_DWORD *)&this->m_PublicAddress.sin_family = 0;
  this->m_PublicAddress.sin_addr.S_un.S_addr = 0;
  *(_DWORD *)this->m_PublicAddress.sin_zero = 0;
  *(_DWORD *)&this->m_PublicAddress.sin_zero[4] = 0;
  *(_DWORD *)&this->m_PrivateAddress.sin_family = 0;
  this->m_PrivateAddress.sin_addr.S_un.S_addr = 0;
  *(_DWORD *)this->m_PrivateAddress.sin_zero = 0;
  *(_DWORD *)&this->m_PrivateAddress.sin_zero[4] = 0;
  this->m_FightInfo.m_pDuelOpponent = 0;
  this->m_FightInfo.m_Pos.fPointX = 0.0;
  this->m_FightInfo.m_Pos.fPointY = 0.0;
  this->m_FightInfo.m_Pos.fPointZ = 0.0;
  this->m_FightInfo.m_dwCellID = 0;
  *(&this->m_FightInfo.m_dwCellID + 1) = 0;
  LODWORD(this->m_FightInfo.m_nRestoreExp) = 0;
  HIDWORD(this->m_FightInfo.m_nRestoreExp) = 0;
  memset(&this->m_DBData, 0, 0x118u);
  this->m_DBData.m_cAdminLevel = 0;
  *(_DWORD *)&this->m_CharacterStatus.m_nSTR = 0;
  *(_DWORD *)&this->m_CharacterStatus.m_nCON = 0;
  this->m_CharacterStatus.m_nWIS = 0;
  memset(this->m_wHistoryQuest, 0, sizeof(this->m_wHistoryQuest));
  *(_WORD *)this->m_cUsingMastery = 0;
  *(_WORD *)this->m_bPadding = 25957;
}
// 4D8328: using guessed type void *CCharacter::`vftable';

//----- (0041ABA0) --------------------------------------------------------
CCharacter *__thiscall CCharacter::`scalar deleting destructor'(CCharacter *this, char a2)
{
  CCharacter::~CCharacter(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0041ABC0) --------------------------------------------------------
CMsgProc *__thiscall Castle::CCastleMgr::GetCastle(CMsgProcessMgr *this, unsigned int uMsg)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator find; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    &this->m_MessageProcessMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&find,
    &uMsg);
  if ( find._Ptr == this->m_MessageProcessMap._Myhead )
    return 0;
  else
    return find._Ptr->_Myval.second;
}

//----- (0041ABF0) --------------------------------------------------------
char __thiscall CCharacter::SetDuelOpponent(CCharacter *this, CCharacter *lpCharacter)
{
  CDuelCellManager *v3; // edi
  CCharacter *m_dwCellID; // ebp
  CCell *second; // edi
  CCell *m_lpCell; // ecx
  CServerSetup *Instance; // eax
  char ServerZone; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *m_dwCID; // ebp
  CCell *Castle; // edi
  float fPointX; // edx
  float fPointY; // eax
  float fPointZ; // ecx
  CCell *v15; // ecx
  unsigned int v16; // [esp-Ch] [ebp-40h]
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+Ch] [ebp-28h] BYREF
  POS DuelPos; // [esp+10h] [ebp-24h]
  POS DuelPos1; // [esp+1Ch] [ebp-18h]
  POS DuelPos2; // [esp+28h] [ebp-Ch]

  DuelPos1.fPointX = 3099.0;
  DuelPos1.fPointY = 1137.0;
  DuelPos1.fPointZ = 3215.0;
  DuelPos2.fPointX = 3156.0;
  DuelPos2.fPointY = 1137.0;
  DuelPos2.fPointZ = 3209.0;
  if ( !CServerSetup::GetInstance()->m_bDuelModeCheck )
  {
    this->m_FightInfo.m_pDuelOpponent = lpCharacter;
    return 1;
  }
  if ( !lpCharacter )
  {
    v3 = CSingleton<CDuelCellManager>::ms_pSingleton;
    m_dwCellID = (CCharacter *)this->m_FightInfo.m_dwCellID;
    lpCharacter = m_dwCellID;
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
      (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)CSingleton<CDuelCellManager>::ms_pSingleton,
      (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
      (const unsigned int *)&lpCharacter);
    if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)v3->m_CellData._Myhead
      || (second = (CCell *)result._Ptr->_Myval.second) == 0 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SetDuelOpponent",
        aDWorkRylSource_96,
        325,
        aCid0x08x_228,
        this->m_dwCID);
      return 0;
    }
    CCell::DeleteCreature(
      second,
      this->m_dwCID,
      (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    if ( !second->m_lstCharacter._Mysize )
      CDuelCellManager::DestroyCell(CSingleton<CDuelCellManager>::ms_pSingleton, (unsigned int)m_dwCellID);
    this->m_FightInfo.m_pDuelOpponent = 0;
    this->m_FightInfo.m_dwCellID = 0;
    CellPosition::MoveTo(&this->m_CellPos, &this->m_CurrentPos);
    m_lpCell = this->m_CellPos.m_lpCell;
    if ( !m_lpCell )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SetDuelOpponent",
        aDWorkRylSource_96,
        345,
        aCid0x08x_270,
        this->m_dwCID);
      return 0;
    }
    CCell::SetCreature(m_lpCell, this->m_dwCID, this, LOGINOUT);
    if ( this->m_lpGameClientDispatch )
    {
      Instance = CServerSetup::GetInstance();
      ServerZone = CServerSetup::GetServerZone(Instance);
      GameClientSendPacket::SendCharBindPosition(
        &this->m_lpGameClientDispatch->m_SendStream,
        0,
        1u,
        (Position)this->m_DBData.m_Pos.LastPoint,
        ServerZone,
        0);
      return 1;
    }
    return 1;
  }
  m_dwCID = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)lpCharacter->m_FightInfo.m_dwCellID;
  if ( m_dwCID )
  {
    Castle = (CCell *)Castle::CCastleMgr::GetCastle(
                        (CMsgProcessMgr *)CSingleton<CDuelCellManager>::ms_pSingleton,
                        lpCharacter->m_FightInfo.m_dwCellID);
    if ( !Castle )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SetDuelOpponent",
        aDWorkRylSource_96,
        379,
        (char *)&byte_4D83AC);
      return 0;
    }
    fPointX = DuelPos2.fPointX;
    fPointY = DuelPos2.fPointY;
    fPointZ = DuelPos2.fPointZ;
  }
  else
  {
    m_dwCID = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *)this->m_dwCID;
    Castle = CDuelCellManager::CreateCell(CSingleton<CDuelCellManager>::ms_pSingleton, m_dwCID);
    if ( !Castle )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SetDuelOpponent",
        aDWorkRylSource_96,
        368,
        (char *)&byte_4D83AC);
      return 0;
    }
    fPointX = DuelPos1.fPointX;
    fPointY = DuelPos1.fPointY;
    fPointZ = DuelPos1.fPointZ;
  }
  DuelPos.fPointZ = fPointZ;
  v15 = this->m_CellPos.m_lpCell;
  DuelPos.fPointX = fPointX;
  DuelPos.fPointY = fPointY;
  if ( !v15 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SetDuelOpponent",
      aDWorkRylSource_96,
      389,
      aCid0x08x_270,
      this->m_dwCID);
    return 0;
  }
  CCell::DeleteCreature(
    v15,
    this->m_dwCID,
    (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
  v16 = this->m_dwCID;
  this->m_CellPos.m_lpCell = 0;
  CCell::SetCreature(Castle, v16, this, LOGINOUT);
  if ( this->m_lpGameClientDispatch )
    GameClientSendPacket::SendCharBindPosition(
      &this->m_lpGameClientDispatch->m_SendStream,
      0,
      1u,
      (Position)DuelPos,
      100,
      0);
  this->m_FightInfo.m_pDuelOpponent = lpCharacter;
  this->m_FightInfo.m_dwCellID = (unsigned int)m_dwCID;
  return 1;
}

//----- (0041AEB0) --------------------------------------------------------
unsigned __int16 __thiscall Skill::CProcessTable::UseSkill(
        Skill::CProcessTable *this,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  const Skill::CProcessTable::ProcessInfo *ProcessInfo; // eax
  unsigned int m_dwCID; // eax

  ProcessInfo = Skill::CProcessTable::GetProcessInfo(this, attackType.m_wType);
  if ( (*((_BYTE *)&attackType + 2) & 0xF0u) >= 0x50 || attackType.m_cSkillLevel > 6 )
  {
    if ( lpSkillUser )
      m_dwCID = lpSkillUser->m_dwCID;
    else
      m_dwCID = 0;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CProcessTable::UseSkill",
      ".//Skill/SkillTable.h",
      78,
      aCid0x08x_44,
      m_dwCID,
      *((unsigned __int8 *)&attackType + 2) >> 4,
      attackType.m_cSkillLevel);
  }
  else if ( ProcessInfo )
  {
    return ProcessInfo->m_fnProcess(
             &ProcessInfo->m_lpProtoType[*((unsigned __int8 *)&attackType + 2) >> 4],
             attackType,
             lpSkillUser,
             lpVictim,
             cOffencerJudge,
             cDefenserJudge,
             wError);
  }
  return 0;
}

//----- (0041AF50) --------------------------------------------------------
void __cdecl LogSkillSlot(const CharacterDBData *DBData)
{
  unsigned __int16 wSlotNum; // ax
  int v2; // ebx
  int v3; // edi
  char *p_cLockCount; // esi

  wSlotNum = DBData->m_Skill.wSlotNum;
  v2 = wSlotNum;
  if ( wSlotNum >= 0x14u )
    v2 = 20;
  v3 = 0;
  if ( v2 > 0 )
  {
    p_cLockCount = &DBData->m_Skill.SSlot[0].SKILLINFO.cLockCount;
    do
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "LogSkillSlot",
        aDWorkRylSource_28,
        40,
        aCid0x08x_261,
        DBData->m_Info.UID,
        v3++,
        *((unsigned __int16 *)p_cLockCount - 1),
        *p_cLockCount,
        p_cLockCount[1]);
      p_cLockCount += 4;
    }
    while ( v3 < v2 );
  }
}

//----- (0041AFC0) --------------------------------------------------------
char __thiscall CCharacter::SkillLock(CCharacter *this, unsigned __int8 Index_In)
{
  unsigned __int16 wSlotNum; // ax
  unsigned __int16 wSkill; // bx
  unsigned __int16 v6; // bp
  char cLockCount; // al
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  wSlotNum = this->m_DBData.m_Skill.wSlotNum;
  if ( wSlotNum <= 0x14u )
  {
    if ( wSlotNum > Index_In )
    {
      wSkill = this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.wSkill;
      v6 = 0;
      if ( wSkill )
      {
        if ( this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cSkillLevel == 6 )
        {
          cLockCount = this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cLockCount;
          if ( cLockCount == 4 )
          {
            v6 = 3;
          }
          else
          {
            this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cLockCount = cLockCount + 1;
            this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cSkillLevel = 0;
            CCharacter::UpdateQuickSlotSkill(this, this->m_DBData.m_Skill.SSlot[Index_In]);
          }
        }
        else
        {
          v6 = 4;
        }
      }
      else
      {
        v6 = 2;
      }
      m_lpGameClientDispatch = this->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        GameClientSendPacket::SendCharSkillCommand(
          &m_lpGameClientDispatch->m_SendStream,
          this->m_dwCID,
          0x16u,
          Index_In,
          wSkill,
          v6);
      if ( v6 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCharacter::SkillLock",
          aDWorkRylSource_28,
          639,
          aCid0x08x_332,
          this->m_dwCID,
          Index_In,
          v6);
        LogSkillSlot(&this->m_DBData);
      }
      return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::SkillLock",
        aDWorkRylSource_28,
        593,
        aCid0x08x_85,
        this->m_dwCID,
        wSlotNum,
        Index_In);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillLock",
      aDWorkRylSource_28,
      586,
      aCid0x08x_144,
      this->m_dwCID,
      wSlotNum);
    return 0;
  }
}

//----- (0041B120) --------------------------------------------------------
char __thiscall CCharacter::SkillUnLock(CCharacter *this, unsigned __int8 Index_In)
{
  unsigned __int16 wSlotNum; // ax
  unsigned __int16 wSkill; // bp
  SKILLSLOT *v6; // edx
  unsigned __int16 v7; // di
  char cLockCount; // cl
  CGameClientDispatch *m_lpGameClientDispatch; // eax

  wSlotNum = this->m_DBData.m_Skill.wSlotNum;
  if ( wSlotNum > 0x14u )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillUnLock",
      aDWorkRylSource_28,
      653,
      aCid0x08x_144,
      this->m_dwCID,
      wSlotNum);
    return 0;
  }
  if ( wSlotNum <= Index_In )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillUnLock",
      aDWorkRylSource_28,
      660,
      aCid0x08x_276,
      this->m_dwCID,
      wSlotNum,
      Index_In);
    return 0;
  }
  wSkill = this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.wSkill;
  v6 = &this->m_DBData.m_Skill.SSlot[Index_In];
  v7 = 0;
  if ( !wSkill )
  {
    v7 = 2;
    goto LABEL_9;
  }
  cLockCount = this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cLockCount;
  if ( cLockCount <= 0 )
  {
    v7 = 3;
LABEL_9:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::SkillUnLock",
      aDWorkRylSource_28,
      692,
      aCid0x08x_207,
      this->m_dwCID,
      Index_In,
      v7);
    LogSkillSlot(&this->m_DBData);
    goto LABEL_10;
  }
  this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cLockCount = cLockCount - 1;
  --this->m_DBData.m_Skill.wSkillNum;
  this->m_DBData.m_Skill.SSlot[Index_In].SKILLINFO.cSkillLevel = 5;
  CCharacter::UpdateQuickSlotSkill(this, *v6);
LABEL_10:
  m_lpGameClientDispatch = this->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    GameClientSendPacket::SendCharSkillCommand(
      &m_lpGameClientDispatch->m_SendStream,
      this->m_dwCID,
      0x58u,
      Index_In,
      wSkill,
      v7);
  return 1;
}

//----- (0041B260) --------------------------------------------------------
void __thiscall CCharacter::CalculatePassiveSkill(
        CCharacter *this,
        Item::CEquipment *pEquipment,
        Item::EquipType::Type eEquipType)
{
  char v4; // bl
  Item::EquipType::Type v5; // ebp
  char *p_cLockCount; // esi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  unsigned __int16 m_usChildSkill; // cx
  CSkillMgr::ProtoTypeArray *v9; // eax
  int v10; // eax
  char v11; // bl
  unsigned __int8 cOffencerJudge; // [esp+7h] [ebp-Dh] BYREF
  AtType attackType; // [esp+8h] [ebp-Ch]
  int wSlotNum; // [esp+Ch] [ebp-8h]
  int wError; // [esp+10h] [ebp-4h] BYREF

  if ( CCharacter::CheckEquipable(this, pEquipment) )
  {
    if ( this->m_DBData.m_Skill.wSlotNum > 0x14u )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCharacter::CalculatePassiveSkill",
        aDWorkRylSource_28,
        60,
        aCid0x08x_168,
        this->m_dwCID,
        this->m_DBData.m_Skill.wSlotNum);
      return;
    }
    if ( this->m_DBData.m_Skill.wSlotNum )
    {
      v4 = *((_BYTE *)&attackType + 2);
      v5 = eEquipType;
      p_cLockCount = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cLockCount;
      wSlotNum = this->m_DBData.m_Skill.wSlotNum;
      do
      {
        attackType.m_wType = *((_WORD *)p_cLockCount - 1);
        SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
        if ( SkillProtoType )
        {
          m_usChildSkill = SkillProtoType->m_ProtoTypes[0].m_usChildSkill;
          if ( m_usChildSkill )
          {
            v9 = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, m_usChildSkill);
            if ( v9->m_ProtoTypes[0].m_eSkillType == MASTER )
            {
              attackType.m_wType = v9->m_ProtoTypes[0].m_usSkill_ID;
LABEL_11:
              v10 = Item::CEquipment::CheckPassiveType(pEquipment, attackType.m_wType);
              if ( v10 != 2 )
              {
                if ( v10 )
                {
                  v11 = (16 * *p_cLockCount) | v4 & 0xF;
                  attackType.m_cSkillLevel = p_cLockCount[1];
                  v4 = (v11 ^ (2 * ((v5 != Attach) + 1))) & 0xE ^ v11;
                  *((_BYTE *)&attackType + 2) = v4;
                  cOffencerJudge = 0;
                  LOBYTE(eEquipType) = 0;
                  wError = 0;
                  Skill::CProcessTable::UseSkill(
                    CSingleton<Skill::CProcessTable>::ms_pSingleton,
                    attackType,
                    this,
                    0,
                    &cOffencerJudge,
                    (unsigned __int8 *)&eEquipType,
                    (unsigned __int16 *)&wError);
                }
              }
            }
          }
          else if ( SkillProtoType->m_ProtoTypes[0].m_eSkillType == MASTER )
          {
            goto LABEL_11;
          }
        }
        p_cLockCount += 4;
        --wSlotNum;
      }
      while ( wSlotNum );
    }
  }
}

//----- (0041B3C0) --------------------------------------------------------
void __thiscall CCharacter::CalculatePassiveSkill(
        CCharacter *this,
        Item::CEquipment *pRightEquipment,
        Item::CEquipment *pLeftEquipment,
        Item::EquipType::Type eEquipType)
{
  int wSlotNum; // eax
  char v5; // bl
  char *p_cLockCount; // edi
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  unsigned __int16 m_usChildSkill; // cx
  CSkillMgr::ProtoTypeArray *v9; // eax
  int v10; // esi
  int v11; // ebp
  char v12; // bl
  unsigned __int8 cDefenserJudge; // [esp+2h] [ebp-12h] BYREF
  unsigned __int8 cOffencerJudge; // [esp+3h] [ebp-11h] BYREF
  AtType attackType; // [esp+4h] [ebp-10h]
  int v16; // [esp+8h] [ebp-Ch]
  int wError; // [esp+Ch] [ebp-8h] BYREF
  CAggresiveCreature *lpSkillUser; // [esp+10h] [ebp-4h]

  wSlotNum = this->m_DBData.m_Skill.wSlotNum;
  lpSkillUser = this;
  if ( wSlotNum > 20 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCharacter::CalculatePassiveSkill",
      aDWorkRylSource_28,
      121,
      aCid0x08x_168,
      this->m_dwCID,
      wSlotNum);
    return;
  }
  if ( wSlotNum > 0 )
  {
    v5 = *((_BYTE *)&attackType + 2);
    p_cLockCount = &this->m_DBData.m_Skill.SSlot[0].SKILLINFO.cLockCount;
    v16 = wSlotNum;
    do
    {
      attackType.m_wType = *((_WORD *)p_cLockCount - 1);
      SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, attackType.m_wType);
      if ( SkillProtoType )
      {
        m_usChildSkill = SkillProtoType->m_ProtoTypes[0].m_usChildSkill;
        if ( m_usChildSkill )
        {
          v9 = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, m_usChildSkill);
          if ( v9->m_ProtoTypes[0].m_eSkillType == MASTER )
          {
            attackType.m_wType = v9->m_ProtoTypes[0].m_usSkill_ID;
LABEL_10:
            v10 = 2;
            v11 = 2;
            if ( pRightEquipment && pRightEquipment->m_ItemData.m_cNumOrDurability )
              v10 = Item::CEquipment::CheckPassiveType(pRightEquipment, attackType.m_wType);
            if ( pLeftEquipment && pLeftEquipment->m_ItemData.m_cNumOrDurability )
              v11 = Item::CEquipment::CheckPassiveType(pLeftEquipment, attackType.m_wType);
            if ( v10 != 2 || v11 != 2 )
            {
              v12 = (16 * *p_cLockCount) | v5 & 0xF;
              attackType.m_cSkillLevel = p_cLockCount[1];
              v5 = (v12 ^ (2 * ((eEquipType != Attach) + 1))) & 0xE ^ v12;
              *((_BYTE *)&attackType + 2) = v5;
              cOffencerJudge = 0;
              cDefenserJudge = 0;
              wError = 0;
              Skill::CProcessTable::UseSkill(
                CSingleton<Skill::CProcessTable>::ms_pSingleton,
                attackType,
                lpSkillUser,
                0,
                &cOffencerJudge,
                &cDefenserJudge,
                (unsigned __int16 *)&wError);
            }
          }
        }
        else if ( SkillProtoType->m_ProtoTypes[0].m_eSkillType == MASTER )
        {
          goto LABEL_10;
        }
      }
      p_cLockCount += 4;
      --v16;
    }
    while ( v16 );
  }
}

//----- (0041B550) --------------------------------------------------------
unsigned __int16 __thiscall CCharacter::ReadSkill(
        CCharacter *this,
        SKILLSLOT SkillSlot,
        int wSkillID,
        unsigned __int16 wSkillLockCount)
{
  CSkillMgr::ProtoTypeArray *SkillProtoType; // edi
  unsigned __int16 result; // ax
  int v7; // ebx
  unsigned __int8 v8; // al
  unsigned __int8 *m_StatusLimitType; // ecx
  unsigned __int8 v10; // al
  bool v11; // cc

  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, wSkillID);
  if ( !SkillProtoType )
    return 2;
  v7 = (wSkillID - 0x8000) >> 8;
  if ( CClass::GetJobLevel(v7) == 1 )
  {
    v8 = this->GetClass(this);
    if ( CClass::GetJobLevel(v8) == 1 && this->GetClass(this) != (unsigned __int8)v7 )
      return 5;
  }
  else if ( CClass::GetJobLevel(v7) == 2 && this->GetClass(this) != (unsigned __int8)v7 )
  {
    return 5;
  }
  if ( wSkillLockCount != SkillSlot.SKILLINFO.cLockCount )
    return 3;
  if ( SkillSlot.SKILLINFO.cSkillLevel >= 6 )
    return 2;
  m_StatusLimitType = SkillProtoType->m_ProtoTypes[0].m_StatusLimitType;
  while ( 2 )
  {
    v10 = (SkillSlot.SKILLINFO.cSkillLevel + 6 * SkillSlot.SKILLINFO.cLockCount + 1) * m_StatusLimitType[2];
    switch ( *m_StatusLimitType )
    {
      case 0u:
        goto $L109276;
      case 1u:
        v11 = v10 <= this->m_CharacterStatus.m_nSTR;
        goto LABEL_19;
      case 2u:
        v11 = v10 <= this->m_CharacterStatus.m_nDEX;
        goto LABEL_19;
      case 3u:
        v11 = v10 <= this->m_CharacterStatus.m_nCON;
        goto LABEL_19;
      case 4u:
        v11 = v10 <= this->m_CharacterStatus.m_nINT;
        goto LABEL_19;
      case 5u:
        v11 = v10 <= this->m_CharacterStatus.m_nWIS;
LABEL_19:
        if ( v11 )
          goto LABEL_20;
        return 6;
      default:
LABEL_20:
        if ( (int)&(++m_StatusLimitType)[-468 - (_DWORD)SkillProtoType] < 2 )
          continue;
$L109276:
        result = 0;
        break;
    }
    return result;
  }
}

