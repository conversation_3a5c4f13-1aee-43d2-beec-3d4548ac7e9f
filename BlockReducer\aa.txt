#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <detours.h>
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <filesystem>
#include <map>
#include <chrono>
#include <atomic>

// Link required libraries
#pragma comment(lib, "detours.lib")
#pragma comment(lib, "advapi32.lib")

// Function prototypes
DWORD WINAPI MainThread(LPVOID);
DWORD WINAPI ConfigMonitorThread(LPVOID);

// The address we want to hook
const DWORD CRITICAL_HIT_ADDRESS = 0x00421B64;

// The original bytes at the hook location
unsigned char g_OriginalBytes[5] = { 0x68, 0xC8, 0x00, 0x00, 0x00 }; // PUSH 0C8

// Base critical value
const int BASE_CRITICAL_VALUE = 200; // 0xC8

// Configuration file path
const char* CONFIG_FILE = "cfg_BlockReducer.ini";

// Store class-specific critical multipliers
std::map<int, float> g_ClassCriticalMultipliers;

// Critical section for thread-safe access to multipliers
CRITICAL_SECTION g_MultipliersLock;

// Last modification time of config file
std::atomic<__int64> g_LastConfigModTime = 0;

// Configuration reload interval in milliseconds
const int CONFIG_RELOAD_INTERVAL = 1000; // Check every 1 second for changes

// Flag to signal thread shutdown
volatile bool g_Shutdown = false;

// Flag to force configuration reload
volatile bool g_ForceReload = false;

// Job/Class names - 1-based index according to the game's mapping
const char* g_JobNames[] = {
    "Unknown",      // 0 (not used)
    "Fighter",      // 1
    "Rogue",        // 2
    "Mage",         // 3
    "Acolyte",      // 4
    "Defender",     // 5
    "Warrior",      // 6
    "Assassin",     // 7
    "Archer",       // 8
    "Sorcerer",     // 9
    "Enchanter",    // 10
    "Priest",       // 11
    "Cleric",       // 12
    "Unknown",      // 13 (gap)
    "Unknown",      // 14 (gap)
    "Unknown",      // 15 (gap)
    "Unknown",      // 16 (gap)
    "Combatant",    // 17
    "Officiator",   // 18
    "Templar",      // 19
    "Attacker",     // 20
    "Gunner",       // 21
    "RuneOff",      // 22
    "LifeOff",      // 23
    "ShadowOff",    // 24
    "Unknown"       // For any values beyond the known classes
};

// Debug logging function - now disabled completely
void DebugLog(const char* format, ...) {
    // Do nothing - all debug logging disabled
}

// Get the last modification time of the config file
__int64 GetConfigFileModTime() {
    try {
        std::filesystem::path configPath(CONFIG_FILE);
        if (!std::filesystem::exists(configPath)) {
            return 0;
        }
        
        auto lastWriteTime = std::filesystem::last_write_time(configPath);
        auto duration = lastWriteTime.time_since_epoch();
        auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
        
        return nanoseconds;
    } catch (...) {
        return 0;
    }
}

// Function to read a float value directly from the INI file
float ReadFloatFromIni(const char* section, const char* key, float defaultValue) {
    try {
        // Directly read the file instead of using Windows INI functions
        std::ifstream iniFile(CONFIG_FILE);
        if (!iniFile.is_open()) {
            return defaultValue;
        }
        
        // Variables to track our position in the file
        bool inTargetSection = false;
        std::string line;
        std::string targetKey = key;
        std::string currentSection;
        
        // Read the file line by line
        while (std::getline(iniFile, line)) {
            // Trim whitespace from the beginning and end of the line
            line.erase(0, line.find_first_not_of(" \t"));
            line.erase(line.find_last_not_of(" \t") + 1);
            
            // Skip empty lines and comments
            if (line.empty() || line[0] == ';' || line[0] == '#') {
                continue;
            }
            
            // Check if this is a section header
            if (line[0] == '[' && line[line.length() - 1] == ']') {
                currentSection = line.substr(1, line.length() - 2);
                inTargetSection = (currentSection == section);
                continue;
            }
            
            // If we're in the target section, look for our key
            if (inTargetSection) {
                size_t equalPos = line.find('=');
                if (equalPos != std::string::npos) {
                    std::string lineKey = line.substr(0, equalPos);
                    
                    // Trim whitespace from the key
                    lineKey.erase(0, lineKey.find_first_not_of(" \t"));
                    lineKey.erase(lineKey.find_last_not_of(" \t") + 1);
                    
                    // If this is our key, extract the value
                    if (lineKey == targetKey) {
                        std::string valueStr = line.substr(equalPos + 1);
                        
                        // Trim whitespace from the value
                        valueStr.erase(0, valueStr.find_first_not_of(" \t"));
                        valueStr.erase(valueStr.find_last_not_of(" \t") + 1);
                        
                        try {
                            float result = std::stof(valueStr);
                            iniFile.close();
                            return result;
                        } catch (...) {
                            iniFile.close();
                            return defaultValue;
                        }
                    }
                }
            }
        }
        
        iniFile.close();
        // Key not found
        return defaultValue;
    } catch (...) {
        // Return default value on any exception
        return defaultValue;
    }
}

// Function to load configuration from INI file
bool LoadConfiguration(bool logChanges = true) {
    try {
        // Store old values for change detection
        std::map<int, float> oldMultipliers;
        
        EnterCriticalSection(&g_MultipliersLock);
        oldMultipliers = g_ClassCriticalMultipliers;
        g_ClassCriticalMultipliers.clear();
        
        // First check if the config file exists
        std::filesystem::path configPath(CONFIG_FILE);
        if (!std::filesystem::exists(configPath)) {
            LeaveCriticalSection(&g_MultipliersLock);
            return false;
        }
        
        // Load values for all valid classes
        for (int i = 1; i <= 24; i++) {
            if (i < sizeof(g_JobNames)/sizeof(g_JobNames[0]) && strcmp(g_JobNames[i], "Unknown") != 0) {
                // Try by class name first
                float multiplier = ReadFloatFromIni("BLOCK_ATTACK_REDUCER", g_JobNames[i], 1.0f);
                
                // Store the multiplier
                g_ClassCriticalMultipliers[i] = multiplier;
            }
        }
        
        bool hasChanges = false;
        
        // Check if any values have changed
        for (int i = 1; i <= 24; i++) {
            if (i < sizeof(g_JobNames)/sizeof(g_JobNames[0]) && strcmp(g_JobNames[i], "Unknown") != 0) {
                float oldValue = 1.0f;
                if (oldMultipliers.find(i) != oldMultipliers.end()) {
                    oldValue = oldMultipliers[i];
                }
                
                float newValue = 1.0f;
                if (g_ClassCriticalMultipliers.find(i) != g_ClassCriticalMultipliers.end()) {
                    newValue = g_ClassCriticalMultipliers[i];
                }
                
                if (fabs(oldValue - newValue) > 0.001f) {
                    hasChanges = true;
                    break;
                }
            }
        }
        
        LeaveCriticalSection(&g_MultipliersLock);
        
        // If changes detected and logging is enabled, log to critical_hits.txt
        /*if (hasChanges && logChanges) {
            // Get current timestamp
            auto now = std::time(nullptr);
            char timestamp[64];
            std::tm tm;
            localtime_s(&tm, &now);
            std::strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &tm);
            
            // Log to critical_hits.txt to show the reload
            std::ofstream hitsLog("logs/critical_hits.txt", std::ios::app);
            if (hitsLog.is_open()) {
                hitsLog << "[" << timestamp << "] Configuration reloaded - changes applied" << std::endl;
                hitsLog.close();
            }
        }*/
        
        return hasChanges;
    } catch (...) {
        // Make sure to release the critical section on exception
        if (g_MultipliersLock.RecursionCount > 0) {
            LeaveCriticalSection(&g_MultipliersLock);
        }
        return false;
    }
}

// Configuration monitor thread
DWORD WINAPI ConfigMonitorThread(LPVOID) {
    try {
        // Initial delay to let system stabilize
        Sleep(2000);
        
        // Store the initial modification time
        g_LastConfigModTime = GetConfigFileModTime();
        
        // Force a first load
        LoadConfiguration(true);
        
        // Keep checking for changes
        while (!g_Shutdown) {
            try {
                if (g_ForceReload) {
                    LoadConfiguration(true);
                    g_ForceReload = false;
                }
                
                // Check if the file has been modified
                __int64 currentModTime = GetConfigFileModTime();
                if (currentModTime > 0 && currentModTime != g_LastConfigModTime.load()) {
                    // File has changed, wait a moment to ensure file writing is complete
                    Sleep(100);
                    
                    // Reload configuration and log the changes
                    LoadConfiguration(true);
                    
                    // Update the last modification time
                    g_LastConfigModTime = currentModTime;
                }
                
                // Wait for a while before checking again
                Sleep(CONFIG_RELOAD_INTERVAL);
            } catch (...) {
                // If an exception occurs in the loop, continue running
                Sleep(CONFIG_RELOAD_INTERVAL);
            }
        }
    } catch (...) {
        // Thread exits on unhandled exception
    }
    
    return 0;
}

// Function declaration for our C function
extern "C" void __cdecl LogCriticalHit(WORD criticalValue, BYTE jobClass, DWORD cid);

// Function to get critical value based on class ID
int GetClassCriticalValue(BYTE classId) {
    // Validate class ID first
    if (classId == 0 || classId > 24) {
        return BASE_CRITICAL_VALUE; // Return default for invalid classes
    }
    
    // Always read directly from the INI file for the most up-to-date value
    float multiplier = 1.0f;
    
    if (classId >= 1 && classId <= 24 && strcmp(g_JobNames[classId], "Unknown") != 0) {
        // Read directly from INI, bypassing any cache
        multiplier = ReadFloatFromIni("CRITICAL_VALUES", g_JobNames[classId], 1.0f);
        
        // Special handling for zero values
        if (multiplier <= 0.001f) {
            // Update the stored value for logging purposes
            EnterCriticalSection(&g_MultipliersLock);
            g_ClassCriticalMultipliers[classId] = 0.0f;
            LeaveCriticalSection(&g_MultipliersLock);
            
            // Return 0 directly for zero multipliers
            return 0;
        }
        
        // Update the stored value for logging purposes
        EnterCriticalSection(&g_MultipliersLock);
        g_ClassCriticalMultipliers[classId] = multiplier;
        LeaveCriticalSection(&g_MultipliersLock);
    }
    
    // Calculate the actual value
    return (int)(BASE_CRITICAL_VALUE * multiplier);
}

// Return address after our hook
static DWORD g_ReturnAddress = 0x00421B69; // Address after the PUSH 0C8 instruction

// Our detoured function
__declspec(naked) void CriticalHitHook()
{
    __asm
    {
        // Save all registers
        pushad
        pushfd
        
        // First, validate that esi is not NULL and points to valid memory
        test esi, esi         // Check if esi is NULL
        jz skip_everything    // If NULL, skip everything
        
        // Try to safely check if this is a player (not a monster)
        // Use structured exception handling by checking if memory is readable
        push esi
        add esi, 0x20        // Try to access the CID offset
        
        // Check if the address is in a valid range (simple bounds check)
        cmp esi, 0x10000     // Check if address is too low (likely invalid)
        jb restore_and_skip  // If below 0x10000, it's likely invalid
        
        pop esi              // Restore original esi
        
        // Now try to read the CID with the original esi
        mov eax, [esi + 0x20] // Get attacker CID (assuming esi points to attacker)
        test eax, 0x80000000  // Test bit 31
        jnz skip_logging      // Skip if monster
        
        // Additional validation: check if job class offset is accessible
        push esi
        add esi, 0x4C8       // Try to access the job class offset
        cmp esi, 0x10000     // Check if address is too low
        jb restore_and_skip2 // If invalid, skip
        pop esi              // Restore original esi
        
        // Get job/class byte with additional bounds checking
        xor ecx, ecx         // Clear ecx first
        mov cl, byte ptr [esi + 0x4C8] // Get job class byte
        
        // Validate class ID is within expected range (1-24)
        cmp cl, 0            // Check if class is 0 or negative
        jle skip_logging     // Skip if invalid class
        cmp cl, 24           // Check if class is beyond max
        ja skip_logging      // Skip if invalid class
        
        // Save CID for logging
        mov eax, [esi + 0x20] // Get attacker CID again
        push eax              // Save attacker CID
        
        // Get custom critical value for this class
        push ecx              // Save class for GetClassCriticalValue
        call GetClassCriticalValue
        add esp, 4           // Clean up stack
        mov edx, eax         // Put critical value in edx
        
        // Save job class for LogCriticalHit
        xor ecx, ecx         // Clear upper bits
        mov cl, byte ptr [esi + 0x4C8] // Get job class byte again
        push ecx             // Save job class
        
        // Save critical value for logging and the game function
        push edx             // Save critical value
        
        // Call our logging function
        call LogCriticalHit
        add esp, 12          // Clean up stack (3 parameters * 4 bytes)
        
        // Get critical value again for the game function
        xor ecx, ecx         // Clear upper bits
        mov cl, byte ptr [esi + 0x4C8] // Get job class byte again
        push ecx             // Save class for GetClassCriticalValue
        call GetClassCriticalValue
        add esp, 4           // Clean up stack
        jmp continue_normal
        
    restore_and_skip2:
        pop esi              // Restore esi if we jumped here
        jmp skip_logging
        
    restore_and_skip:
        pop esi              // Restore esi if we jumped here
        
    skip_logging:
        // For invalid cases, use default value
        mov eax, BASE_CRITICAL_VALUE
        
    continue_normal:
        // Restore registers
        popfd
        popad
        
        // Special handling based on whether we got a valid value
        test esi, esi        // Check if esi is still valid
        jz use_default       // If not, use default
        
        // Try to read job class one more time with validation
        push ecx
        push esi
        add esi, 0x4C8       // Check if we can access job class offset
        cmp esi, 0x10000     // Bounds check
        jb use_default_pop   // If invalid, use default
        pop esi              // Restore esi
        
        xor ecx, ecx         // Clear upper bits
        mov cl, byte ptr [esi + 0x4C8] // Get job class byte
        
        // Validate class range again
        cmp cl, 0
        jle use_default_pop2
        cmp cl, 24
        ja use_default_pop2
        
        push ecx             // Save class for GetClassCriticalValue
        call GetClassCriticalValue
        add esp, 4           // Clean up stack
        pop ecx              // Restore ecx
        
        // Special handling for zero critical values
        test eax, eax        // Check if critical value is zero
        jnz normal_critical  // If not zero, proceed normally
        
        // For zero critical values, push 1 instead of 0
        push 1
        jmp [g_ReturnAddress]
        
    use_default_pop2:
        pop ecx              // Clean up stack
        jmp use_default
        
    use_default_pop:
        pop esi              // Clean up stack
        pop ecx              // Clean up stack
        
    use_default:
        // Use default critical value
        push BASE_CRITICAL_VALUE
        jmp [g_ReturnAddress]
        
    normal_critical:
        // Push the custom critical value
        push eax
        
        // Jump back to where we left off (after the original push instruction)
        jmp [g_ReturnAddress]
        
    skip_everything:
        // Restore registers and use default value
        popfd
        popad
        push BASE_CRITICAL_VALUE
        jmp [g_ReturnAddress]
    }
}

// Function to log critical hit information
extern "C" void __cdecl LogCriticalHit(WORD criticalValue, BYTE jobClass, DWORD cid)
{
    // Function intentionally empty - logging disabled
    /*
    try
    {
        // Create logs directory if it doesn't exist
        static bool logsDirectoryCreated = false;
        if (!logsDirectoryCreated) {
            CreateDirectoryA("logs", NULL);
            logsDirectoryCreated = true;
        }
        
        // Get current timestamp
        auto now = std::time(nullptr);
        char timestamp[64];
        std::tm tm;
        localtime_s(&tm, &now);
        std::strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &tm);
        
        // Determine job name - check if class ID is within valid range
        const char* jobName = "Unknown";
        if (jobClass >= 1 && jobClass <= 24) {
            jobName = g_JobNames[jobClass];
        }
        
        // Get the multiplier for this class - direct from INI for guaranteed freshness
        float multiplier = 1.0f;
        if (jobClass >= 1 && jobClass <= 24 && strcmp(g_JobNames[jobClass], "Unknown") != 0) {
            // Force a fresh read from the INI file
            multiplier = ReadFloatFromIni("CRITICAL_VALUES", g_JobNames[jobClass], 1.0f);
            
            // Special handling for zero values
            if (multiplier <= 0.001f) {
                multiplier = 0.0f;
            }
            
            // Update the stored value
            EnterCriticalSection(&g_MultipliersLock);
            g_ClassCriticalMultipliers[jobClass] = multiplier;
            LeaveCriticalSection(&g_MultipliersLock);
        }
        
        // Open log file
        std::ofstream logFile("logs/critical_hits.txt", std::ios::app);
        if (logFile.is_open())
        {
            // Log the critical hit information
            logFile << "[" << timestamp << "] "
                    << "CID: 0x" << std::hex << cid << std::dec
                    << " | Critical Value: " << criticalValue;
            
            // For zero multipliers, add special note
            if (multiplier <= 0.001f) {
                logFile << " (DISABLED)";
            } else {
                logFile << " (x" << std::fixed << std::setprecision(2) << multiplier << ")";
            }
            
            logFile << " | Class: " << jobName
                    << std::endl;
                
            logFile.close();
        }
    }
    catch (...) 
    {
        // Silently fail if logging encounters an error
    }
    */
}

// Function to force reload configuration
void ForceReloadConfiguration() {
    g_ForceReload = true;
}

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID)
{
    // Give the main application time to initialize
    Sleep(1000);
    
    // Create logs directory
    CreateDirectoryA("logs", NULL);
    
    // Initialize licensing system
    InitializeLicensing();
    
    // Use shared licensing functions
    if (!RYL1Plugin::CheckLicense())
    {
        return 0;
    }
    
    // Initialize critical section
    InitializeCriticalSection(&g_MultipliersLock);
    
    // Check if config file exists, create it if not
    std::filesystem::path configPath(CONFIG_FILE);
    if (!std::filesystem::exists(configPath)) {
        try {
            std::ofstream configFile(CONFIG_FILE);
            if (configFile.is_open()) {
                configFile << "[CRITICAL_VALUES]" << std::endl << std::endl;
                configFile << "; 1.0 = base value (200)" << std::endl;
                configFile << "; 1.1 = 10% increase (220)" << std::endl;
                configFile << "; 1.5 = 50% increase (300)" << std::endl;
                configFile << "; 0.8 = 20% decrease (160)" << std::endl;
                configFile << "; 0.0 = disable critical hits (0)" << std::endl << std::endl;
                configFile << "; Class specific multipliers" << std::endl << std::endl;
                
                // Human classes
                configFile << ";Human" << std::endl;
                for (int i = 1; i <= 12; i++) {
                    if (i < sizeof(g_JobNames)/sizeof(g_JobNames[0]) && strcmp(g_JobNames[i], "Unknown") != 0) {
                        configFile << g_JobNames[i] << "=1.0" << std::endl;
                    }
                }
                
                configFile << std::endl << ";Akkhan" << std::endl;
                for (int i = 17; i <= 24; i++) {
                    if (i < sizeof(g_JobNames)/sizeof(g_JobNames[0]) && strcmp(g_JobNames[i], "Unknown") != 0) {
                        configFile << g_JobNames[i] << "=1.0" << std::endl;
                    }
                }
                
                configFile.close();
            }
        } catch (...) {
            // Continue even if config creation fails
        }
    }
    
    // Log startup
    /*std::ofstream logFile("logs/critical_hits.txt", std::ios::app);
    if (logFile.is_open())
    {
        auto now = std::time(nullptr);
        char timestamp[64];
        std::tm tm;
        localtime_s(&tm, &now);
        std::strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &tm);
        
        logFile << "[" << timestamp << "] HitLogger plugin initialized with live multiplier-based critical values" << std::endl;
        logFile.close();
    }*/
    
    // Start config monitor thread
    HANDLE hMonitorThread = CreateThread(NULL, 0, ConfigMonitorThread, NULL, 0, NULL);
    if (hMonitorThread) {
        CloseHandle(hMonitorThread);
    }
    
    // Apply hook
    DWORD oldProtect;
    if (VirtualProtect((LPVOID)CRITICAL_HIT_ADDRESS, 5, PAGE_EXECUTE_READWRITE, &oldProtect))
    {
        // Create a jump to our hook function
        unsigned char jumpCode[5];
        jumpCode[0] = 0xE9; // JMP instruction
        
        // Calculate the relative address for the jump
        DWORD relativeAddress = (DWORD)CriticalHitHook - CRITICAL_HIT_ADDRESS - 5;
        memcpy(&jumpCode[1], &relativeAddress, 4);
        
        // Write the jump instruction to the address
        memcpy((LPVOID)CRITICAL_HIT_ADDRESS, jumpCode, 5);
        
        // Restore protection
        VirtualProtect((LPVOID)CRITICAL_HIT_ADDRESS, 5, oldProtect, &oldProtect);
    }
    
    // Force initial reload
    ForceReloadConfiguration();
    
    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
    switch (reason)
    {
        case DLL_PROCESS_ATTACH:
            DisableThreadLibraryCalls(hModule);
            CreateThread(0, 0, MainThread, 0, 0, 0);
            break;
            
        case DLL_PROCESS_DETACH:
            // Signal shutdown for the config monitor thread
            g_Shutdown = true;
            
            // Cleanup critical section
            DeleteCriticalSection(&g_MultipliersLock);
            
            // Cleanup licensing
            CleanupLicensing();
            
            // Restore original bytes if possible
            DWORD oldProtect;
            if (VirtualProtect((LPVOID)CRITICAL_HIT_ADDRESS, 5, PAGE_EXECUTE_READWRITE, &oldProtect))
            {
                memcpy((LPVOID)CRITICAL_HIT_ADDRESS, g_OriginalBytes, 5);
                VirtualProtect((LPVOID)CRITICAL_HIT_ADDRESS, 5, oldProtect, &oldProtect);
            }
            break;
    }
    
    return TRUE;
} 