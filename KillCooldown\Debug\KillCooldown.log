﻿  License.cpp
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(23,13): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(24,22): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(27,30): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(28,25): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(29,19): error C7525: inline variables require at least '/std:c++17'
  KillCooldown.cpp
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(23,13): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(24,22): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(27,30): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(28,25): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\Header\License.h(29,19): error C7525: inline variables require at least '/std:c++17'
E:\! DETOUR WORK\.RYL1 Plugin Server\KillCooldown\KillCooldown.cpp(45,13): error C2086: 'std::string g_ThreatLogFilePath': redefinition
  Generating Code...
