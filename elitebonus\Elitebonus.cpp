﻿// Define WIN32_LEAN_AND_MEAN to avoid winsock2.h conflicts
#define WIN32_LEAN_AND_MEAN
// Include winsock2.h before windows.h to avoid redefinition errors
#include <winsock2.h>
#include <windows.h>
#include <detours.h>
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <vector>
#include <iomanip>
#include <thread>
#include <sstream>
#include <algorithm> // For std::min/max
#include <ctime>     // For timestamp in logs

// Link required libraries
#pragma comment(lib, "detours.lib")
// Additional libraries needed for licensing functionality
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "ws2_32.lib")

// Removed reference to g_disableLicense

bool g_disableFakeOnline = false; // 👈 true = disable, false = allow
volatile bool g_eliteBonusEnabled = true;
volatile bool g_disable_damage = true;
volatile bool g_disableNegative = false; // 👈 true = disable negative elite bonus, false = allow

// --- IP Authentication ---
// These variables are now defined in License.cpp and declared as extern in License.h
// ------------------------

// --- Fame Data ---
std::vector<int> g_fameThresholds(12, 0); // 11 tiers + 1 dummy
const char *g_fameKeys[12] = {"", "E1", "E2", "E3", "E4", "E5", "E6", "E7", "E8", "E9", "EX", "Ex1"};
// -----------------

int imbalanceThreshold = 10;

// --- Power Level Data ---
std::vector<float> g_powerLevelMap(30, 1.0f); // Map for integer power levels to float multipliers
// ------------------------

// --- Function Pointers ---
using tGetEliteBonusForIcon = char(__thiscall *)(void *pThis);
tGetEliteBonusForIcon oGetEliteBonusForIcon = (tGetEliteBonusForIcon)0x0041CDC0;

using tGetEliteBonusForPower = float(__thiscall *)(void *pThis, void *pOpponent);
tGetEliteBonusForPower oGetEliteBonusForPower = (tGetEliteBonusForPower)0x00421A40;

using tGetMgr = void *(__cdecl *)();
tGetMgr pGetMgr = (tGetMgr)0x00413C20;

using tApplyBonus = int(__thiscall *)(void *pThis, int value, int race);
tApplyBonus ApplyBonus = (tApplyBonus)0x00457F40;

using tUpdateClients = void(__thiscall *)(void *pThis, int result);
tUpdateClients UpdateClients = (tUpdateClients)0x00410CD0;

using tStatueOperator = char(__thiscall *)(void *pThis, void *lpCharacter);
tStatueOperator oStatueOperator = (tStatueOperator)0x00454480;
// -------------------------

// --- Thread-safe bonus storage ---
struct BonusData
{
    int human = 0;
    int akkhan = 0;
};
BonusData g_currentBonus;
BonusData g_pendingBonus;
volatile bool g_bBonusNeedsUpdate = false;
// Keep g_bonusLock as it's specific to Elitebonus.cpp
CRITICAL_SECTION g_bonusLock;
volatile bool g_bonusLockInitialized = false; // Flag to track if the lock is initialized
// --------------------------------

int g_LastImbalance = 0;

WORD *GetCharacterNumArray()
{
    void *mgr = pGetMgr();
    if (!mgr)
        return nullptr;
    return (WORD *)((BYTE *)mgr + 0x100);
}

int g_CurrentZone = 0;

// --- Debug Logging Functions ---
std::string GetTimestamp()
{
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::tm tm;
    localtime_s(&tm, &time);

    std::stringstream ss;
    ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// Structure to hold entity information for logging
struct EntityInfo
{
    void *entity;
    bool isMonster;
    DWORD cid;
    std::string race;
    int fameTier;
    int balanceBonus;
    int totalPower;
    float powerMultiplier;
    int fame;
    std::string additionalInfo;
};

// Helper function to get entity information
EntityInfo GetEntityInfo(void *entity, bool isMonster)
{
    EntityInfo info;
    info.entity = entity;
    info.isMonster = isMonster;
    info.cid = entity ? *(DWORD *)((BYTE *)entity + 0x20) : 0;
    info.fameTier = 0;
    info.balanceBonus = 0;
    info.totalPower = 0;
    info.powerMultiplier = 1.0f;
    info.fame = 0;
    info.additionalInfo = "";

    if (entity && !isMonster)
    {
        int race = *(BYTE *)((BYTE *)entity + 0x4C7);
        info.race = (race == 0) ? "Human" : "Akkhan";
        info.fame = *(int *)((BYTE *)entity + 0x4CA);

        // Determine fame tier
        for (int i = 11; i >= 1; i--)
        {
            if (g_fameThresholds[i] > 0 && info.fame >= g_fameThresholds[i])
            {
                info.fameTier = i;
                break;
            }
        }

        // Get balance bonus - safely check if lock is initialized
        if (g_bonusLockInitialized)
        {
            EnterCriticalSection(&g_bonusLock);
            info.balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
            LeaveCriticalSection(&g_bonusLock);
        }
        else
        {
            // If lock isn't initialized, just read the value directly (may be slightly unsafe but better than crashing)
            info.balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
        }

        // Calculate adjusted balance bonus
        int adjustedBalanceBonus = 0;
        if (info.balanceBonus < 0)
        {
            adjustedBalanceBonus = abs(info.balanceBonus);
        }
        else if (info.balanceBonus > 0)
        {
            adjustedBalanceBonus = -info.balanceBonus;
        }

        // Calculate total power
        info.totalPower = info.fameTier + adjustedBalanceBonus;

        // Ensure within limits
        if (info.totalPower > 11)
            info.totalPower = 11;
        if (info.totalPower < -11)
            info.totalPower = -11;

        // Get power multiplier
        info.powerMultiplier = g_powerLevelMap[info.totalPower + 15];

        // Additional info
        info.additionalInfo = "Fame: " + std::to_string(info.fame) + " | Race: " + info.race;
    }

    return info;
}

void LogAttackCalculation(const std::string &logType, void *attacker, void *defender, float powerMultiplier,
                          bool isAttackerMonster, bool isDefenderMonster, int fameTier = 0, int balanceBonus = 0,
                          int totalPower = 0, const std::string &additionalInfo = "")
{
    // Get detailed entity information
    EntityInfo attackerInfo = GetEntityInfo(attacker, isAttackerMonster);
    EntityInfo defenderInfo = GetEntityInfo(defender, isDefenderMonster);

    // For attacker, use the provided power calculation details if available
    if (fameTier != 0 || balanceBonus != 0 || totalPower != 0)
    {
        attackerInfo.fameTier = fameTier;
        attackerInfo.balanceBonus = balanceBonus;
        attackerInfo.totalPower = totalPower;
        attackerInfo.powerMultiplier = powerMultiplier;
    }

    if (!additionalInfo.empty())
    {
        attackerInfo.additionalInfo = additionalInfo;
    }

    // Create logs directory if it doesn't exist
    CreateDirectoryA("logs", NULL);

    //    std::ofstream log("logs/attack_power_calculation.txt", std::ios::app);
    //    if (!log.is_open()) return;
    //
    //    // Log in the requested format
    //    log << "---------------------------------------------------" << std::endl;
    //    log << "[" << GetTimestamp() << "] " << logType << std::endl;
    //
    //    // Log attacker information
    //    log << "  Attacker: " << (attackerInfo.isMonster ? "Monster" : "Player")
    //        << " (CID: 0x" << std::hex << attackerInfo.cid << std::dec << ")";
    //
    //    if (!attackerInfo.isMonster) {
    //        log << " Race: " << attackerInfo.race << std::endl;
    //        log << "  Fame Tier: " << attackerInfo.fameTier << std::endl;
    //        log << "  Balance Bonus: " << attackerInfo.balanceBonus << std::endl;
    //        log << "  Total Power Level: " << attackerInfo.totalPower << std::endl;
    //        log << "  Power Multiplier: " << attackerInfo.powerMultiplier << std::endl;
    //        log << "  Additional Info: " << attackerInfo.additionalInfo << std::endl;
    //    } else {
    //        log << std::endl;
    //    }
    //
    //    // Log defender information
    //    log << std::endl << " Defender: " << (defenderInfo.isMonster ? "Monster" : "Player")
    //        << " (CID: 0x" << std::hex << defenderInfo.cid << std::dec << ")";
    //
    //    if (!defenderInfo.isMonster) {
    //        log << " Race: " << defenderInfo.race << std::endl;
    //        log << "  Fame Tier: " << defenderInfo.fameTier << std::endl;
    //        log << "  Balance Bonus: " << defenderInfo.balanceBonus << std::endl;
    //        log << "  Total Power Level: " << defenderInfo.totalPower << std::endl;
    //        log << "  Power Multiplier: " << defenderInfo.powerMultiplier << std::endl;
    //        log << "  Additional Info: " << defenderInfo.additionalInfo << std::endl;
    //    } else {
    //        log << std::endl;
    //    }
    //
    //    // Calculate and log total multiplier
    //    float totalMultiplier = attackerInfo.powerMultiplier;
    //    log << std::endl << "  Total Multiplier: " << totalMultiplier << " " << std::endl << std::endl;
    //
    //    log << "----------------------------------------------------" << std::endl;
    //    log.close();
}
// -----------------------------

// No need for function declaration as it's now in License.h

void LoadConfig()
{
    // Load authorized IPs using the function from RYL1Plugin namespace
    RYL1Plugin::LoadAuthorizedIPs();

    char iniPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, iniPath);
    strcat_s(iniPath, "\\cfg_elitebonusadjust.ini");

    // Flush the INI cache to ensure we read the latest values from disk
    WritePrivateProfileStringA(NULL, NULL, NULL, iniPath);

    // Load Fame Thresholds
    for (int i = 1; i <= 11; i++)
    {
        g_fameThresholds[i] = GetPrivateProfileIntA("config", g_fameKeys[i], 0, iniPath);
    }

    imbalanceThreshold = GetPrivateProfileIntA("gap", "Other", 10, iniPath);

    // Set default hard-coded Power Level to Multiplier Mapping
    // E11=1.80, E10=1.76, E9=1.70, E8=1.60, E7=1.56, E6=1.46, E5=1.40, E4=1.33, E3=1.23, E2=1.16, E1=1.07, E0=1.0
    // E-1=0.98, E-2=0.96, E-3=0.94, E-4=0.92, E-5=0.90, E-6=0.88, E-7=0.86, E-8=0.84, E-9=0.82, E-10=0.80
    g_powerLevelMap[26] = 1.80f; // E11
    g_powerLevelMap[25] = 1.76f; // E10
    g_powerLevelMap[24] = 1.70f; // E9
    g_powerLevelMap[23] = 1.60f; // E8
    g_powerLevelMap[22] = 1.56f; // E7
    g_powerLevelMap[21] = 1.46f; // E6
    g_powerLevelMap[20] = 1.40f; // E5
    g_powerLevelMap[19] = 1.33f; // E4
    g_powerLevelMap[18] = 1.23f; // E3
    g_powerLevelMap[17] = 1.16f; // E2
    g_powerLevelMap[16] = 1.07f; // E1
    g_powerLevelMap[15] = 1.00f; // E0
    g_powerLevelMap[14] = 0.98f; // E-1
    g_powerLevelMap[13] = 0.96f; // E-2
    g_powerLevelMap[12] = 0.94f; // E-3
    g_powerLevelMap[11] = 0.92f; // E-4
    g_powerLevelMap[10] = 0.90f; // E-5
    g_powerLevelMap[9] = 0.88f;  // E-6
    g_powerLevelMap[8] = 0.86f;  // E-7
    g_powerLevelMap[7] = 0.84f;  // E-8
    g_powerLevelMap[6] = 0.82f;  // E-9
    g_powerLevelMap[5] = 0.80f;  // E-10
    g_powerLevelMap[4] = 0.80f;  // E-11 (same as E-10)

    // Now try to read integer values from INI and convert them to multipliers
    // Format: E11=11, E10=10, E9=9, etc.
    for (int i = -11; i <= 11; i++)
    {
        char key[8];
        sprintf_s(key, "E%d", i);

        // Read integer value from INI, default to i itself
        int intValue = GetPrivateProfileIntA("FAME_REVISED", key, i, iniPath);

        // Only override if a value was found in the INI (non-default)
        if (intValue != i || i == 0)
        {
            // Convert integer to float multiplier based on actual values
            float multiplier;

            // Use a lookup table approach to match the exact values
            switch (intValue)
            {
                // Positive values
            case 11:
                multiplier = 1.80f;
                break;
            case 10:
                multiplier = 1.76f;
                break;
            case 9:
                multiplier = 1.70f;
                break;
            case 8:
                multiplier = 1.60f;
                break;
            case 7:
                multiplier = 1.56f;
                break;
            case 6:
                multiplier = 1.46f;
                break;
            case 5:
                multiplier = 1.40f;
                break;
            case 4:
                multiplier = 1.33f;
                break;
            case 3:
                multiplier = 1.23f;
                break;
            case 2:
                multiplier = 1.16f;
                break;
            case 1:
                multiplier = 1.07f;
                break;
            case 0:
                multiplier = 1.00f;
                break;
                // Negative values
            case -1:
                multiplier = 0.98f;
                break;
            case -2:
                multiplier = 0.96f;
                break;
            case -3:
                multiplier = 0.94f;
                break;
            case -4:
                multiplier = 0.92f;
                break;
            case -5:
                multiplier = 0.90f;
                break;
            case -6:
                multiplier = 0.88f;
                break;
            case -7:
                multiplier = 0.86f;
                break;
            case -8:
                multiplier = 0.84f;
                break;
            case -9:
                multiplier = 0.82f;
                break;
            case -10:
                multiplier = 0.80f;
                break;
            case -11:
                multiplier = 0.80f;
                break; // Same as -10
            default:
                // For values outside our range, use a formula
                if (intValue > 11)
                {
                    multiplier = 1.80f; // Cap at E11 value
                }
                else if (intValue < -11)
                {
                    multiplier = 0.80f; // Cap at E-10 value
                }
                else
                {
                    multiplier = 1.00f; // Fallback
                }
                break;
            }

            // Apply the multiplier, overriding the default
            g_powerLevelMap[i + 15] = multiplier;
        }
    }

    // Read percentage values from INI and convert them to multipliers
    // Format: 0=0%, 1=7%, 2=16%, etc.
    for (int i = -11; i <= 11; i++)
    {
        char key[8];
        sprintf_s(key, "%d", i);

        char percentBuffer[16] = {0};
        GetPrivateProfileStringA("PERCENTAGE", key, "", percentBuffer, sizeof(percentBuffer), iniPath);

        // If we got a value, try to parse it
        if (percentBuffer[0] != '\0')
        {
            // Remove the % sign if present
            char *percentSign = strchr(percentBuffer, '%');
            if (percentSign)
                *percentSign = '\0';

            // Try to convert to float
            float percentValue = (float)atof(percentBuffer);

            // Convert percentage to multiplier (e.g., 7% -> 1.07f)
            float multiplier = 1.0f + (percentValue / 100.0f);

            // Apply the multiplier, overriding the default
            g_powerLevelMap[i + 15] = multiplier;
        }
    }

    // Check zone disable list
    g_eliteBonusEnabled = true;

    // Check if negative elite bonus should be disabled
    g_disableNegative = GetPrivateProfileIntA("config", "disablenegative", 0, iniPath) == 1;

    char zoneBuffer[256] = {0};
    GetPrivateProfileStringA("disable_elitebonus", "Zone", "", zoneBuffer, sizeof(zoneBuffer), iniPath);
    std::string zonesStr(zoneBuffer);
    std::istringstream iss(zonesStr);
    std::string token;
    while (std::getline(iss, token, ','))
    {
        if (atoi(token.c_str()) == g_CurrentZone)
        {
            g_eliteBonusEnabled = false;
            break;
        }
    }

    char damageZoneBuffer[256] = {0};
    GetPrivateProfileStringA("disable_damage", "Zone", "", damageZoneBuffer, sizeof(damageZoneBuffer), iniPath);
    std::string damageZonesStr(damageZoneBuffer);
    std::istringstream damageIss(damageZonesStr);
    std::string damageToken;
    while (std::getline(damageIss, damageToken, ','))
    {
        if (atoi(damageToken.c_str()) == g_CurrentZone)
        {
            g_disable_damage = false;
            break;
        }
    }
}
// Licensing functionality is now handled by License.cpp through the RYL1Plugin namespace

void DetectZoneFromArgs()
{
    char fullCmdLine[MAX_PATH];
    GetModuleFileNameA(NULL, fullCmdLine, MAX_PATH);
    strcat_s(fullCmdLine, " ");
    strcat_s(fullCmdLine, GetCommandLineA());
    std::string cmd(fullCmdLine);
    size_t pos = cmd.find("-z ");
    if (pos != std::string::npos)
    {
        g_CurrentZone = atoi(&cmd[pos + 3]);
    }
}

// This hook returns the visual tier for the client icon, combining fame and population balance.
char __fastcall Hook_GetEliteBonusForIcon(void *pThis, void *)
{
    // Check IP authorization
    if (!RYL1Plugin::IsIPAuthorized())
    {
        return 0; // Deny access
    }

    if (!g_eliteBonusEnabled)
        return 0;

    if (!g_disable_damage)
        return 0;

    // Check if this is a monster (CID & 0x80000000)
    DWORD cid = *(DWORD *)((BYTE *)pThis + 0x20);
    bool isMonster = (cid & 0x80000000) != 0;

    // If this is a monster, don't show any icon
    if (isMonster)
    {
        return 0;
    }

    int race = *(BYTE *)((BYTE *)pThis + 0x4C7);
    int fame = *(int *)((BYTE *)pThis + 0x4CA);

    // 1. Determine the player's fame tier
    int fameTier = 0;
    for (int i = 11; i >= 1; i--)
    {
        if (g_fameThresholds[i] > 0 && fame >= g_fameThresholds[i])
        {
            fameTier = i;
            break;
        }
    }

    // 2. Get the balance bonus - safely check if lock is initialized
    int balanceBonus = 0;
    if (g_bonusLockInitialized)
    {
        EnterCriticalSection(&g_bonusLock);
        balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
        LeaveCriticalSection(&g_bonusLock);
    }
    else
    {
        // If lock isn't initialized, just read the value directly
        balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
    }

    // 3. Calculate total tier - COMBINE fame and population balance
    int displayTier = fameTier;
    int adjustedBalanceBonus = 0;

    // For minority races (negative bonus), convert to positive for power calculation
    if (balanceBonus < 0)
    {
        // Convert negative bonus to positive for power boost
        adjustedBalanceBonus = abs(balanceBonus);
    }
    // For majority races (positive bonus), convert to negative for power penalty
    else if (balanceBonus > 0)
    {
        // Convert positive bonus to negative for power penalty
        adjustedBalanceBonus = -balanceBonus;
    }

    displayTier += adjustedBalanceBonus;

    // Ensure we stay within limits
    if (displayTier > 11)
        displayTier = 11;
    if (displayTier < -11)
        displayTier = -11;

    // Cap at E0 if negative elite bonus is disabled
    if (g_disableNegative && displayTier < 0)
        displayTier = 0;

    // Log the icon calculation in a more concise format
    //   std::ofstream log("logs/icon_calculation.txt", std::ios::app);
    // log << "[" << GetTimestamp() << "] [ICON] Race: " << (race == 0 ? "H" : "A")
    //      << " | Tier: " << fameTier
    //      << " | Balance Bonus: " << balanceBonus
    //      << " | Adjusted Balance: " << (balanceBonus < 0 ? abs(balanceBonus) : -balanceBonus)
    //      << " | Total: " << displayTier << std::endl;
    //  log.close();

    return displayTier;
}

// This hook calculates the actual power level used by the server.
float __fastcall Hook_GetEliteBonusForPower(void *pThis, void *, void *pOpponent)
{
    // Check IP authorization
    if (!RYL1Plugin::IsIPAuthorized())
    {
        return 0.0f; // Deny access
    }

    if (!g_eliteBonusEnabled)
        return 1.0f;

    if (!g_disable_damage)
        return 0.0f;

    // Check if this is a monster (CID & 0x80000000)
    DWORD thisCID = *(DWORD *)((BYTE *)pThis + 0x20);
    DWORD opponentCID = pOpponent ? *(DWORD *)((BYTE *)pOpponent + 0x20) : 0;

    bool isThisMonster = (thisCID & 0x80000000) != 0;
    bool isOpponentMonster = (opponentCID & 0x80000000) != 0;

    // If either entity is a monster in PvE combat, call the original function
    // This ensures monsters maintain their original defense
    if (isThisMonster || isOpponentMonster)
    {
        // IMPORTANT: For combat involving monsters, call the original function
        // but SKIP the EliteBonus calculation by detaching our hook temporarily

        // Temporarily detach our hook
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        DetourDetach((void **)&oGetEliteBonusForPower, Hook_GetEliteBonusForPower);
        DetourTransactionCommit();

        // Call the original function directly
        float result = oGetEliteBonusForPower(pThis, pOpponent);

        // Log the monster combat calculation
        std::string combatType;
        if (isThisMonster && isOpponentMonster)
        {
            combatType = "MONSTER VS MONSTER";
        }
        else if (isThisMonster)
        {
            combatType = "MONSTER ATTACKING PLAYER";
        }
        else
        {
            combatType = "PLAYER ATTACKING MONSTER";
        }

        // Get detailed entity information
        EntityInfo attackerInfo = GetEntityInfo(pThis, isThisMonster);
        EntityInfo defenderInfo = GetEntityInfo(pOpponent, isOpponentMonster);

        // For monster calculations, use the original result
        attackerInfo.powerMultiplier = result;
        attackerInfo.additionalInfo = "Using original power calculation";

        // Create logs directory if it doesn't exist
        CreateDirectoryA("logs", NULL);

        // Re-attach our hook
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        DetourAttach((void **)&oGetEliteBonusForPower, Hook_GetEliteBonusForPower);
        DetourTransactionCommit();

        return result;
    }

    // For players (PvP), we need to properly apply the fame revised multiplier
    int race = *(BYTE *)((BYTE *)pThis + 0x4C7);
    int fame = *(int *)((BYTE *)pThis + 0x4CA);

    // Get defender race and fame
    int defenderRace = *(BYTE *)((BYTE *)pOpponent + 0x4C7);
    int defenderFame = *(int *)((BYTE *)pOpponent + 0x4CA);

    // 1. Determine the player's fame tier
    int fameTier = 0;
    for (int i = 11; i >= 1; i--)
    {
        if (g_fameThresholds[i] > 0 && fame >= g_fameThresholds[i])
        {
            fameTier = i;
            break;
        }
    }

    // Determine the defender's fame tier
    int defenderFameTier = 0;
    for (int i = 11; i >= 1; i--)
    {
        if (g_fameThresholds[i] > 0 && defenderFame >= g_fameThresholds[i])
        {
            defenderFameTier = i;
            break;
        }
    }

    // Apply the mapping from FAME_REVISED to the fame tier
    char iniPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, iniPath);
    strcat_s(iniPath, "\\cfg_elitebonusadjust.ini");

    char key[8];
    sprintf_s(key, "E%d", fameTier);

    // Read mapped value from INI, default to fameTier itself
    int mappedFameTier = GetPrivateProfileIntA("FAME_REVISED", key, fameTier, iniPath);

    // Do the same for defender
    char defenderKey[8];
    sprintf_s(defenderKey, "E%d", defenderFameTier);
    int defenderMappedFameTier = GetPrivateProfileIntA("FAME_REVISED", defenderKey, defenderFameTier, iniPath);

    // 2. Get the fame bonus from the mapped tier number
    int fameBonus = mappedFameTier;
    int defenderFameBonus = defenderMappedFameTier;

    // 3. Get the balance bonus - ensure we're using the latest values
    int balanceBonus = 0;
    int defenderBalanceBonus = 0;
    
    if (g_bonusLockInitialized)
    {
        EnterCriticalSection(&g_bonusLock);
        balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
        defenderBalanceBonus = (defenderRace == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
        LeaveCriticalSection(&g_bonusLock);
    }
    else
    {
        // If lock isn't initialized, just read the values directly
        balanceBonus = (race == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
        defenderBalanceBonus = (defenderRace == 0) ? g_currentBonus.human : g_currentBonus.akkhan;
    }

    // 4. Calculate total power - COMBINE fame and population bonuses
    int adjustedBalanceBonus = 0;
    int adjustedDefenderBalanceBonus = 0;

    // For minority races (negative bonus), convert to positive for power calculation
    if (balanceBonus < 0)
    {
        // Convert negative bonus to positive for power boost
        adjustedBalanceBonus = abs(balanceBonus);
    }
    // For majority races (positive bonus), convert to negative for power penalty
    else if (balanceBonus > 0)
    {
        // Convert positive bonus to negative for power penalty
        adjustedBalanceBonus = -balanceBonus;
    }

    // Same for defender
    if (defenderBalanceBonus < 0)
    {
        // Convert negative bonus to positive for power boost
        adjustedDefenderBalanceBonus = abs(defenderBalanceBonus);
    }
    else if (defenderBalanceBonus > 0)
    {
        // Convert positive bonus to negative for power penalty
        adjustedDefenderBalanceBonus = -defenderBalanceBonus;
    }

    int totalPower = fameBonus + adjustedBalanceBonus;
    int defenderTotalPower = defenderFameBonus + adjustedDefenderBalanceBonus;

    // Ensure we stay within limits
    if (totalPower > 11)
        totalPower = 11;
    if (totalPower < -11)
        totalPower = -11;

    if (defenderTotalPower > 11)
        defenderTotalPower = 11;
    if (defenderTotalPower < -11)
        defenderTotalPower = -11;

    // Cap at E0 if negative elite bonus is disabled
    if (g_disableNegative && totalPower < 0)
        totalPower = 0;

    if (g_disableNegative && defenderTotalPower < 0)
        defenderTotalPower = 0;

    // Apply the mapping from FAME_REVISED for both attacker and defender power levels
    sprintf_s(key, "E%d", totalPower);
    sprintf_s(defenderKey, "E%d", defenderTotalPower);

    // Read mapped value from INI, default to totalPower itself
    int mappedPower = GetPrivateProfileIntA("FAME_REVISED", key, totalPower, iniPath);
    int defenderMappedPower = GetPrivateProfileIntA("FAME_REVISED", defenderKey, defenderTotalPower, iniPath);

    // 5. Look up the final multiplier from our custom map using the mapped power
    float powerMultiplier = g_powerLevelMap[mappedPower + 15]; // Use +15 as offset
    float defenderPowerMultiplier = g_powerLevelMap[defenderMappedPower + 15]; // Use +15 as offset

    // Get detailed entity information for PvP combat
    EntityInfo attackerInfo = GetEntityInfo(pThis, isThisMonster);
    EntityInfo defenderInfo = GetEntityInfo(pOpponent, isOpponentMonster);

    // Update attacker info with calculated values
    attackerInfo.fameTier = fameTier;
    attackerInfo.balanceBonus = balanceBonus;
    attackerInfo.totalPower = totalPower;
    attackerInfo.powerMultiplier = powerMultiplier;

    // Update defender info with calculated values
    defenderInfo.fameTier = defenderFameTier;
    defenderInfo.balanceBonus = defenderBalanceBonus;
    defenderInfo.totalPower = defenderTotalPower;
    defenderInfo.powerMultiplier = defenderPowerMultiplier;

    // The additionalInfo fields
        attackerInfo.additionalInfo = "Fame: " + std::to_string(fame) +
                                      " | Race: " + (race == 0 ? "Human" : "Akkhan") +
                                  " | Tier: " + std::to_string(fameTier) +
                                  " | Mapped: " + std::to_string(mappedFameTier) +
                                  " | Total Power: " + std::to_string(totalPower) +
                                  " | Power Multiplier: " + std::to_string(powerMultiplier);

    defenderInfo.additionalInfo = "Fame: " + std::to_string(defenderFame) +
                                  " | Race: " + (defenderRace == 0 ? "Human" : "Akkhan") +
                                  " | Tier: " + std::to_string(defenderFameTier) +
                                  " | Mapped: " + std::to_string(defenderMappedFameTier) +
                                  " | Total Power: " + std::to_string(defenderTotalPower) +
                                  " | Power Multiplier: " + std::to_string(defenderPowerMultiplier);

    // Create logs directory if it doesn't exist
    CreateDirectoryA("logs", NULL);

    // Now directly apply the defender's elite bonus to calculate final multiplier
    // We'll extract the defender's bonus percentage and adjust the attacker's power accordingly
    
    // For defenders with positive elite bonus (defense boost):
    // - If defender has +56% (1.56), we need to reduce attacker's damage by 56%
    // - If defender has -5% (0.95), we need to increase attacker's damage by 5%
    
    float finalMultiplier = powerMultiplier;
    
    // Apply defender's elite bonus directly to the attacker's power
    if (defenderPowerMultiplier > 1.0f)
    {
        // Positive bonus - reduce damage based on defender's bonus
        // For example: defender with +56% (1.56) reduces damage by 56%
        float defenderBonus = defenderPowerMultiplier - 1.0f;
        finalMultiplier = powerMultiplier * (1.0f - defenderBonus);
    }
    else if (defenderPowerMultiplier < 1.0f)
    {
        // Negative bonus - increase damage based on defender's penalty
        // For example: defender with -5% (0.95) increases damage by 5%
        float defenderPenalty = 1.0f - defenderPowerMultiplier;
        finalMultiplier = powerMultiplier * (1.0f + defenderPenalty);
    }
    
    // Ensure the multiplier doesn't go below a minimum value
    float minMultiplier = 0.2f;
    if (finalMultiplier < minMultiplier)
        finalMultiplier = minMultiplier;

    // Write a detailed log entry about this calculation
   // std::ofstream log("logs/pvp_power_calculation.txt", std::ios::app);
  //  if (log.is_open()) {
    //    log << "[" << GetTimestamp() << "] PVP POWER CALCULATION\n";
     //   log << "  Attacker Power: " << powerMultiplier << " (Elite " << totalPower << ")\n";
     //   log << "  Defender Power: " << defenderPowerMultiplier << " (Elite " << defenderTotalPower << ")\n";
     //   if (defenderPowerMultiplier > 1.0f) {
     //       log << "  Defender Bonus: +" << (defenderPowerMultiplier - 1.0f) * 100.0f << "% (Damage reduced by this amount)\n";
    //    } else if (defenderPowerMultiplier < 1.0f) {
     //       log << "  Defender Penalty: -" << (1.0f - defenderPowerMultiplier) * 100.0f << "% (Damage increased by this amount)\n";
     //   }
     //   log << "  Final Multiplier: " << finalMultiplier << "\n";
     //   log << "----------------------------------------------------\n";
    //    log.close();
  //  }

    return finalMultiplier;
}

void BonusCalculationLoop()
{
    while (true)
    {
        Sleep(500); // Check more frequently (500ms instead of 1000ms)
        LoadConfig();

        if (!g_eliteBonusEnabled)
            continue;

        if (!g_disable_damage)
            continue;

        WORD *arr = GetCharacterNumArray();
        if (arr)
        {
            int realHuman_val = arr[0];
            int realAkkhan_val = arr[1];
            int fakeHuman_val = 0, fakeAkkhan_val = 0;

            char iniPath[MAX_PATH];
            GetCurrentDirectoryA(MAX_PATH, iniPath);
            strcat_s(iniPath, "\\cfg_fakeonline.ini");
            char zoneSection[32];
            sprintf_s(zoneSection, "zone%d", g_CurrentZone);
            fakeHuman_val = GetPrivateProfileIntA(zoneSection, "human", -1, iniPath);
            fakeAkkhan_val = GetPrivateProfileIntA(zoneSection, "akkhan", -1, iniPath);
            if (fakeHuman_val == -1)
                fakeHuman_val = GetPrivateProfileIntA("zone0", "human", 0, iniPath);
            if (fakeAkkhan_val == -1)
                fakeAkkhan_val = GetPrivateProfileIntA("zone0", "akkhan", 0, iniPath);
            int totalHuman = realHuman_val;
            int totalAkkhan = realAkkhan_val;

            if (!g_disableFakeOnline)
            {
                totalHuman += fakeHuman_val;
                totalAkkhan += fakeAkkhan_val;
            }

            int imbalance = totalAkkhan - totalHuman;

            // Calculate raw bonus based on imbalance - ALWAYS calculate even if small
            int rawBonus = 0;
            if (imbalanceThreshold > 0)
            {
                // Calculate raw bonus based on imbalance
                rawBonus = abs(imbalance) / imbalanceThreshold;

                // Ensure minimum bonus of 1 if there's any imbalance above threshold
                if (abs(imbalance) >= imbalanceThreshold)
                {
                    if (rawBonus < 1)
                        rawBonus = 1; // At least 1 if above threshold
                }
            }

            // Only apply bonus if imbalance exceeds threshold
            if (abs(imbalance) < imbalanceThreshold)
            {
                rawBonus = 0; // no bonus yet
            }

            BonusData newBonus;
            if (imbalance > 0)
            {
                newBonus.human = -rawBonus;
                newBonus.akkhan = +rawBonus;
            }
            else if (imbalance < 0)
            {
                newBonus.human = +rawBonus;
                newBonus.akkhan = -rawBonus;
            }
            else
            {
                newBonus.human = 0;
                newBonus.akkhan = 0;
            } // Explicitly set to 0 if balanced

            // Always update the bonus - safely check if lock is initialized
            if (g_bonusLockInitialized)
            {
                EnterCriticalSection(&g_bonusLock);
                g_pendingBonus = newBonus;
                g_currentBonus = newBonus; // Update current bonus immediately
                g_bBonusNeedsUpdate = true;
                LeaveCriticalSection(&g_bonusLock);
            }
            else
            {
                // If lock isn't initialized, just update the values directly
                g_pendingBonus = newBonus;
                g_currentBonus = newBonus;
                g_bBonusNeedsUpdate = true;
            }

            // Update the last imbalance
            g_LastImbalance = imbalance;
        }
    }
}

char __fastcall Hook_StatueOperator(void *pThis, void *, void *lpCharacter)
{
    // Always apply the bonus on every statue operation
    BonusData bonusToApply;
    
    if (g_bonusLockInitialized)
    {
        EnterCriticalSection(&g_bonusLock);
        bonusToApply = g_pendingBonus;
        g_currentBonus = g_pendingBonus;
        g_bBonusNeedsUpdate = false;
        LeaveCriticalSection(&g_bonusLock);
    }
    else
    {
        // If lock isn't initialized, just read the values directly
        bonusToApply = g_pendingBonus;
        g_currentBonus = g_pendingBonus;
        g_bBonusNeedsUpdate = false;
    }

    void *mgr = pGetMgr();
    if (mgr && g_eliteBonusEnabled)
    {
        if (bonusToApply.human != 0 || bonusToApply.akkhan != 0)
        {
            ApplyBonus(mgr, bonusToApply.human, 0);
            ApplyBonus(mgr, bonusToApply.akkhan, 1);
            UpdateClients(mgr, 1);
        }
    }

    if (mgr && g_disable_damage)
    {
        if (bonusToApply.human != 0 || bonusToApply.akkhan != 0)
        {
            ApplyBonus(mgr, bonusToApply.human, 0);
            ApplyBonus(mgr, bonusToApply.akkhan, 1);
            UpdateClients(mgr, 1);
        }
    }

    // The fake online count logic must remain
    WORD *arr = GetCharacterNumArray();
    if (!arr)
        return oStatueOperator(pThis, lpCharacter);
    WORD realHuman = arr[0], realAkkhan = arr[1];
    int fakeHuman = 0, fakeAkkhan = 0;
    char iniPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, iniPath);
    strcat_s(iniPath, "\\cfg_fakeonline.ini");
    char zoneSection[32];
    sprintf_s(zoneSection, "zone%d", g_CurrentZone);
    fakeHuman = GetPrivateProfileIntA(zoneSection, "human", -1, iniPath);
    fakeAkkhan = GetPrivateProfileIntA(zoneSection, "akkhan", -1, iniPath);
    if (fakeHuman == -1)
        fakeHuman = GetPrivateProfileIntA("zone0", "human", 0, iniPath);
    if (fakeAkkhan == -1)
        fakeAkkhan = GetPrivateProfileIntA("zone0", "akkhan", 0, iniPath);
    if (!g_disableFakeOnline)
    {
        arr[0] = realHuman + fakeHuman;
        arr[1] = realAkkhan + fakeAkkhan;
    }
    else
    {
        arr[0] = realHuman;
        arr[1] = realAkkhan;
    }
    char result = oStatueOperator(pThis, lpCharacter);
    arr[0] = realHuman;
    arr[1] = realAkkhan;
    return result;
}

// Licensing functionality is now handled by License.cpp through the RYL1Plugin namespace

DWORD WINAPI MainThread(LPVOID)
{
    Sleep(1000);
    
    // Add a try-catch block to handle any exceptions during initialization
    try
    {
        // Initialize licensing system first
        InitializeLicensing();
        DetectZoneFromArgs();
        
        // Initialize critical section before using it - do this after licensing check
        // Use a safer approach without __try/__except
        BOOL initResult = FALSE;
        initResult = InitializeCriticalSectionAndSpinCount(&g_bonusLock, 0x1000);
        g_bonusLockInitialized = (initResult != 0); // Mark as initialized only if successful

        // Use the new CheckLicense function which handles all license checks
        if (!RYL1Plugin::CheckLicense())
        {
            return 0; // This will never be reached due to crash in CheckLicense
        }

        // Create logs directory
        CreateDirectoryA("logs", NULL);

        CreateThread(0, 0, (LPTHREAD_START_ROUTINE)BonusCalculationLoop, 0, 0, 0);

        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        DetourAttach((void **)&oGetEliteBonusForIcon, Hook_GetEliteBonusForIcon);
        DetourAttach((void **)&oGetEliteBonusForPower, Hook_GetEliteBonusForPower);
        DetourAttach((void **)&oStatueOperator, Hook_StatueOperator);
        DetourTransactionCommit();
    }
    catch (const std::exception& e)
    {
        // If a standard exception occurs during initialization, log it and return gracefully
        // This prevents the server from crashing during time sync events
        
        // Create logs directory if it doesn't exist
        CreateDirectoryA("logs", NULL);
        
        // Log the error
        std::ofstream errorLog("logs/elitebonus_error.log", std::ios::app);
        if (errorLog.is_open())
        {
            errorLog << "[" << GetTimestamp() << "] Standard exception: " << e.what() << std::endl;
            errorLog.close();
        }
    }
    catch (...)
    {
        // If any other exception occurs during initialization, log it and return gracefully
        // This prevents the server from crashing during time sync events
        
        // Create logs directory if it doesn't exist
        CreateDirectoryA("logs", NULL);
        
        // Log the error
        std::ofstream errorLog("logs/elitebonus_error.log", std::ios::app);
        if (errorLog.is_open())
        {
            errorLog << "[" << GetTimestamp() << "] Unknown exception occurred during initialization" << std::endl;
            errorLog.close();
        }
    }

    return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID)
{
    if (reason == DLL_PROCESS_ATTACH)
    {
        DisableThreadLibraryCalls(hModule);
        CreateThread(0, 0, MainThread, 0, 0, 0);
    }
    else if (reason == DLL_PROCESS_DETACH)
    {
        // Only delete the critical section if it was initialized
        if (g_bonusLockInitialized)
        {
            // No need for try/except here - DeleteCriticalSection is safe to call
            // if the critical section was properly initialized
            DeleteCriticalSection(&g_bonusLock);
            g_bonusLockInitialized = false;
        }
        
        // Cleanup licensing system
        CleanupLicensing();
    }
    return TRUE;
}
