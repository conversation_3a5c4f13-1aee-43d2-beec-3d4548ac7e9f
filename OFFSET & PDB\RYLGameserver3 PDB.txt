  16,
  -86,
  96,
  100,
  -40,
  105,
  -57,
  13,
  43,
  40,
  -90,
  -70,
  1,
  74,
  -18,
  40,
  101,
  -60,
  -99,
  65,
  -115,
  -111,
  108,
  -111,
  126,
  128,
  -61,
  -47,
  -82,
  -74,
  -110,
  65,
  102,
  19,
  114,
  32,
  38,
  -95,
  114,
  5,
  41,
  8,
  -120,
  48,
  64,
  109,
  90,
  65,
  1,
  122,
  -37,
  44,
  -18,
  -61,
  92,
  3,
  56,
  -40,
  -107,
  -25,
  -76,
  103,
  48,
  81,
  33,
  104,
  120,
  -119,
  104,
  11,
  -29,
  -80,
  40,
  -77,
  -87,
  56,
  24,
  -28,
  89,
  67,
  -55,
  82,
  117,
  4,
  21,
  7,
  -105,
  20,
  7,
  39,
  -38,
  -27,
  -39,
  -37,
  -37,
  8,
  39,
  -93,
  100,
  -36,
  66,
  -29,
  61,
  13,
  38,
  -94,
  -61,
  94,
  62,
  -89,
  71,
  -28,
  28,
  115,
  19,
  -103,
  -98,
  -70,
  -45,
  8,
  115,
  -120,
  3,
  1,
  36,
  46,
  9,
  -67,
  58,
  110,
  60,
  -74,
  -94,
  34,
  -25,
  39,
  96,
  32,
  -123,
  -38,
  -22,
  -124,
  -122,
  65,
  103,
  28,
  -125,
  -66,
  122,
  97,
  103,
  1,
  24,
  48,
  -58,
  55,
  -68,
  81,
  -68,
  120,
  -95,
  83,
  83,
  88,
  -101,
  50,
  5,
  103,
  107,
  -57,
  58,
  124,
  -88,
  -27,
  112,
  16,
  41,
  -120,
  -108,
  -64,
  -18,
  -115,
  82,
  32,
  -39,
  -61,
  60,
  -77,
  67,
  116,
  -125,
  -56,
  -59,
  -86,
  -112,
  88,
  12,
  -48,
  -68,
  42,
  -19,
  4,
  5,
  -114,
  39,
  -34,
  -100,
  55,
  87,
  42,
  -109,
  99,
  27,
  -98,
  -61,
  82,
  -37,
  -23,
  99,
  -102,
  -121,
  24,
  109,
  -66,
  27,
  55,
  106,
  -22,
  1,
  2,
  1,
  -75,
  116,
  113,
  -91,
  -102,
  -102,
  58,
  17,
  -117,
  98,
  -41,
  -80,
  6,
  12,
  -96,
  16,
  9,
  -105,
  90,
  -21,
  -22,
  24,
  -72
}; // weak
const char aDWorkRylSource_53[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F7B50 = '\xC6'; // idb
const char aDWorkRylSource_32[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aGid0x08xComman[] = "GID:0x%08x/Command:0x%02x/Type:%d/SrcCID:0x%08x/DstCID:0x%08x/"; // idb
const char aDWorkRylSource_61[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F7E04 = '\xC7'; // idb
const char byte_4F7E40 = '\xBD'; // idb
void *std::ifstream::`vftable' = &std::ifstream::`vector deleting destructor'; // weak
_UNKNOWN std::ifstream::`vbtable'; // weak
const char byte_4F7EC8 = '\xB0'; // idb
const char byte_4F7F18 = '\xC1'; // idb
const char byte_4F85B0 = '\xC5'; // idb
const char aErrorLineD_0[] = "Error(line %d) : "; // idb
const config_s configuration_table[10] =
{
  { 0u, 0u, 0u, 0u, &deflate_stored },
  { 4u, 4u, 8u, 4u, &deflate_fast },
  { 4u, 5u, 16u, 8u, &deflate_fast },
  { 4u, 6u, 32u, 32u, &deflate_fast },
  { 4u, 4u, 16u, 16u, &deflate_slow },
  { 8u, 16u, 32u, 32u, &deflate_slow },
  { 8u, 16u, 128u, 128u, &deflate_slow },
  { 8u, 32u, 128u, 256u, &deflate_slow },
  { 32u, 128u, 258u, 1024u, &deflate_slow },
  { 32u, 258u, 258u, 4096u, &deflate_slow }
}; // idb
const unsigned int crc_table[256] =
{
  0u,
  1996959894u,
  3993919788u,
  2567524794u,
  124634137u,
  1886057615u,
  3915621685u,
  2657392035u,
  249268274u,
  2044508324u,
  3772115230u,
  2547177864u,
  162941995u,
  2125561021u,
  3887607047u,
  2428444049u,
  498536548u,
  1789927666u,
  4089016648u,
  2227061214u,
  450548861u,
  1843258603u,
  4107580753u,
  2211677639u,
  325883990u,
  1684777152u,
  4251122042u,
  2321926636u,
  335633487u,
  1661365465u,
  4195302755u,
  2366115317u,
  997073096u,
  1281953886u,
  3579855332u,
  2724688242u,
  1006888145u,
  1258607687u,
  3524101629u,
  2768942443u,
  901097722u,
  1119000684u,
  3686517206u,
  2898065728u,
  853044451u,
  1172266101u,
  3705015759u,
  2882616665u,
  651767980u,
  1373503546u,
  3369554304u,
  3218104598u,
  565507253u,
  1454621731u,
  3485111705u,
  3099436303u,
  671266974u,
  1594198024u,
  3322730930u,
  2970347812u,
  795835527u,
  1483230225u,
  3244367275u,
  3060149565u,
  1994146192u,
  31158534u,
  2563907772u,
  4023717930u,
  1907459465u,
  112637215u,
  2680153253u,
  3904427059u,
  2013776290u,
  251722036u,
  2517215374u,
  3775830040u,
  2137656763u,
  141376813u,
  2439277719u,
  3865271297u,
  1802195444u,
  476864866u,
  2238001368u,
  4066508878u,
  1812370925u,
  453092731u,
  2181625025u,
  4111451223u,
  1706088902u,
  314042704u,
  2344532202u,
  4240017532u,
  1658658271u,
  366619977u,
  2362670323u,
  4224994405u,
  1303535960u,
  984961486u,
  2747007092u,
  3569037538u,
  1256170817u,
  1037604311u,
  2765210733u,
  3554079995u,
  1131014506u,
  879679996u,
  2909243462u,
  3663771856u,
  1141124467u,
  855842277u,
  2852801631u,
  3708648649u,
  1342533948u,
  654459306u,
  3188396048u,
  3373015174u,
  1466479909u,
  544179635u,
  3110523913u,
  3462522015u,
  1591671054u,
  702138776u,
  2966460450u,
  3352799412u,
  1504918807u,
  783551873u,
  3082640443u,
  3233442989u,
  3988292384u,
  2596254646u,
  62317068u,
  1957810842u,
  3939845945u,
  2647816111u,
  81470997u,
  1943803523u,
  3814918930u,
  2489596804u,
  225274430u,
  2053790376u,
  3826175755u,
  2466906013u,
  167816743u,
  2097651377u,
  4027552580u,
  2265490386u,
  503444072u,
  1762050814u,
  4150417245u,
  2154129355u,
  426522225u,
  1852507879u,
  4275313526u,
  2312317920u,
  282753626u,
  1742555852u,
  4189708143u,
  2394877945u,
  397917763u,
  1622183637u,
  3604390888u,
  2714866558u,
  953729732u,
  1340076626u,
  3518719985u,
  2797360999u,
  1068828381u,
  1219638859u,
  3624741850u,
  2936675148u,
  906185462u,
  1090812512u,
  3747672003u,
  2825379669u,
  829329135u,
  1181335161u,
  3412177804u,
  3160834842u,
  628085408u,
  1382605366u,
  3423369109u,
  3138078467u,
  570562233u,
  1426400815u,
  3317316542u,
  2998733608u,
  733239954u,
  1555261956u,
  3268935591u,
  3050360625u,
  752459403u,
  1541320221u,
  2607071920u,
  3965973030u,
  1969922972u,
  40735498u,
  2617837225u,
  3943577151u,
  1913087877u,
  83908371u,
  2512341634u,
  3803740692u,
  2075208622u,
  213261112u,
  2463272603u,
  3855990285u,
  2094854071u,
  198958881u,
  2262029012u,
  4057260610u,
  1759359992u,
  534414190u,
  2176718541u,
  4139329115u,
  1873836001u,
  414664567u,
  2282248934u,
  4279200368u,
  1711684554u,
  285281116u,
  2405801727u,
  4167216745u,
  1634467795u,
  376229701u,
  2685067896u,
  3608007406u,
  1308918612u,
  956543938u,
  2808555105u,
  3495958263u,
  1231636301u,
  1047427035u,
  2932959818u,
  3654703836u,
  1088359270u,
  936918000u,
  2847714899u,
  3736837829u,
  1202900863u,
  817233897u,
  3183342108u,
  3401237130u,
  1404277552u,
  615818150u,
  3134207493u,
  3453421203u,
  1423857449u,
  601450431u,
  3009837614u,
  3294710456u,
  1567103746u,
  711928724u,
  3020668471u,
  3272380065u,
  1510334235u,
  755167117u
}; // idb
const char byte_4F8B40 = '\xC1'; // idb
char byte_4F8B88[312] =
{
  '\0',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x02',
  '\x01',
  '\x02',
  '\x03',
  '\x04',
  '\x05',
  '\x06',
  '\a',
  '\b',
  '\t',
  '\n',
  '\v',
  '\f',
  '\r',
  '\x0E',
  '\x0F',
  '\x10',
  '\x11',
  '\x12',
  '\x13',
  '\x14',
  '\x15',
  '\x16',
  '\x17',
  '\x18',
  '\x19',
  '\x1A',
  '\x1B',
  '\x1C',
  '\x1D',
  '\x1E',
  '\x1F',
  ' ',
  '!',
  '\"',
  '#',
  '$',
  '%',
  '&',
  '\'',
  '(',
  ')',
  '*',
  '+',
  ',',
  '-',
  '.',
  '/',
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8'
}; // weak
__int16 word_4F8CC0[] = { 0 }; // weak
_WORD word_4F8D98[340] =
{
  58,
  0,
  58,
  59,
  0,
  0,
  60,
  0,
  61,
  0,
  62,
  0,
  66,
  0,
  70,
  0,
  1,
  23,
  0,
  71,
  0,
  84,
  0,
  28,
  58,
  29,
  0,
  23,
  0,
  86,
  23,
  0,
  15,
  24,
  86,
  25,
  59,
  0,
  15,
  24,
  86,
  25,
  59,
  16,
  59,
  0,
  17,
  24,
  86,
  25,
  28,
  63,
  65,
  29,
  0,
  64,
  63,
  0,
  64,
  0,
  18,
  85,
  32,
  58,
  0,
  19,
  32,
  58,
  0,
  0,
  13,
  24,
  67,
  25,
  59,
  0,
  14,
  24,
  86,
  25,
  59,
  0,
  68,
  69,
  23,
  69,
  0,
  61,
  0,
  71,
  0,
  86,
  0,
  0,
  21,
  23,
  0,
  20,
  23,
  0,
  22,
  69,
  23,
  0,
  72,
  75,
  23,
  0,
  12,
  0,
  8,
  0,
  9,
  0,
  10,
  0,
  11,
  0,
  3,
  0,
  3,
  0,
  76,
  0,
  75,
  31,
  76,
  0,
  74,
  33,
  87,
  0,
  74,
  0,
  3,
  26,
  27,
  77,
  0,
  3,
  26,
  85,
  27,
  77,
  0,
  3,
  26,
  85,
  27,
  0,
  33,
  28,
  78,
  29,
  0,
  87,
  0,
  87,
  31,
  78,
  0,
  80,
  0,
  80,
  31,
  79,
  0,
  0,
  72,
  74,
  0,
  72,
  0,
  3,
  0,
  23,
  0,
  28,
  0,
  72,
  81,
  24,
  79,
  25,
  83,
  58,
  29,
  0,
  72,
  81,
  24,
  79,
  25,
  82,
  0,
  4,
  0,
  5,
  0,
  6,
  0,
  7,
  0,
  87,
  0,
  87,
  31,
  86,
  0,
  88,
  0,
  73,
  33,
  87,
  0,
  73,
  34,
  87,
  0,
  73,
  35,
  87,
  0,
  73,
  36,
  87,
  0,
  73,
  37,
  87,
  0,
  73,
  38,
  87,
  0,
  89,
  0,
  88,
  40,
  89,
  0,
  90,
  0,
  89,
  39,
  90,
  0,
  91,
  0,
  90,
  45,
  91,
  0,
  90,
  46,
  91,
  0,
  92,
  0,
  91,
  41,
  92,
  0,
  91,
  43,
  92,
  0,
  91,
  42,
  92,
  0,
  91,
  44,
  92,
  0,
  93,
  0,
  92,
  47,
  93,
  0,
  92,
  48,
  93,
  0,
  94,
  0,
  93,
  49,
  94,
  0,
  93,
  50,
  94,
  0,
  93,
  51,
  94,
  0,
  95,
  0,
  55,
  73,
  0,
  56,
  73,
  0,
  52,
  94,
  0,
  96,
  0,
  73,
  26,
  86,
  27,
  0,
  3,
  24,
  86,
  25,
  0,
  3,
  24,
  25,
  0,
  73,
  55,
  0,
  73,
  56,
  0,
  85,
  0,
  24,
  86,
  25,
  0,
  73,
  0
}; // weak
__int16 word_4F9040[108] =
{
  0,
  120,
  125,
  126,
  130,
  131,
  132,
  133,
  134,
  135,
  136,
  137,
  141,
  146,
  147,
  151,
  153,
  155,
  161,
  162,
  166,
  171,
  172,
  176,
  178,
  183,
  188,
  189,
  193,
  194,
  198,
  199,
  200,
  205,
  211,
  212,
  214,
  216,
  218,
  223,
  231,
  239,
  240,
  244,
  245,
  246,
  248,
  250,
  255,
  260,
  261,
  266,
  267,
  269,
  273,
  274,
  278,
  283,
  287,
  291,
  300,
  306,
  307,
  308,
  309,
  313,
  314,
  318,
  319,
  321,
  323,
  325,
  327,
  329,
  334,
  335,
  340,
  341,
  346,
  347,
  349,
  354,
  355,
  357,
  359,
  361,
  366,
  367,
  369,
  374,
  375,
  377,
  379,
  384,
  385,
  387,
  389,
  393,
  394,
  396,
  403,
  410,
  411,
  415,
  416,
  418,
  0,
  0
}; // weak
char *off_4F9118[97] =
{
  "$",
  "error",
  "$undefined.",
  "TOKEN_ID",
  "TOKEN_INTEGERVALUE",
  "TOKEN_FLOATVALUE",
  "TOKEN_BOOLVALUE",
  "TOKEN_STRINGVALUE",
  "TOKEN_INT",
  "TOKEN_FLOAT",
  "TOKEN_BOOL",
  "TOKEN_STRING",
  "TOKEN_VOID",
  "TOKEN_FOR",
  "TOKEN_WHILE",
  "TOKEN_IF",
  "TOKEN_ELSE",
  "TOKEN_SWITCH",
  "TOKEN_CASE",
  "TOKEN_DEFAULT",
  "TOKEN_CONTINUE",
  "TOKEN_BREAK",
  "TOKEN_RETURN",
  "TOKEN_ENDSTATEMENT",
  "TOKEN_LEFTPARENTHESIS",
  "TOKEN_RIGHTPARENTHESIS",
  "TOKEN_LEFTBRACE",
  "TOKEN_RIGHTBRACE",
  "TOKEN_LEFTBRACKET",
  "TOKEN_RIGHTBRACKET",
  "TOKEN_ERROR",
  "TOKEN_COMMA",
  "TOKEN_COLON",
  "TOKEN_ASSIGNMENT",
  "TOKEN_COMPOUNDADDITION",
  "TOKEN_COMPOUNDSUBTRACTION",
  "TOKEN_COMPOUNDMULTIPLICATION",
  "TOKEN_COMPOUNDDIVISION",
  "TOKEN_COMPOUNDREMINDER",
  "TOKEN_AND",
  "TOKEN_OR",
  "TOKEN_LESSTHAN",
  "TOKEN_LESSTHANOREQUAL",
  "TOKEN_MORETHAN",
  "TOKEN_MORETHANOREQUAL",
  "TOKEN_EQUALITY",
  "TOKEN_NOTEQUAL",
  "TOKEN_ADDITION",
  "TOKEN_SUBTRACTION",
  "TOKEN_MULTIPLICATION",
  "TOKEN_DIVISION",
  "TOKEN_REMINDER",
  "TOKEN_NOT",
  "PREFIXINCREMENT",
  "PREFIXDECREMENT",
  "TOKEN_INCREMENT",
  "TOKEN_DECREMENT",
  "program",
  "statement_list",
  "statement",
  "compound_statement",
  "expression_statement",
  "selection_statement",
  "cases",
  "case_one",
  "default",
  "iteration_statement",
  "for_expression",
  "for_init_statement",
  "optional_expression",
  "jump_statement",
  "declaration",
  "decl_specifiers",
  "variable",
  "new_variable",
  "declarator_list",
  "init_declarator",
  "array_initializer",
  "initializer_list",
  "argument_declaration_list",
  "argument_declaration",
  "function_name",
  "function_decl_end",
  "function_def_start",
  "function_definition",
  "constant_expression",
  "expression",
  "assignment_expression",
  "logical_or_expression",
  "logical_and_expression",
  "equality_expression",
  "relational_expression",
  "additive_expression",
  "multiplicative_expression",
  "unary_expression",
  "postfix_expression",
  "primary_expression"
}; // weak
__int16 word_4F92A0[108] =
{
  0,
  57,
  58,
  58,
  59,
  59,
  59,
  59,
  59,
  59,
  59,
  59,
  60,
  61,
  61,
  62,
  62,
  62,
  63,
  63,
  64,
  65,
  65,
  66,
  66,
  67,
  68,
  68,
  69,
  69,
  70,
  70,
  70,
  71,
  72,
  72,
  72,
  72,
  72,
  73,
  74,
  75,
  75,
  76,
  76,
  76,
  76,
  76,
  77,
  78,
  78,
  79,
  79,
  79,
  80,
  80,
  81,
  82,
  83,
  84,
  84,
  85,
  85,
  85,
  85,
  86,
  86,
  87,
  87,
  87,
  87,
  87,
  87,
  87,
  88,
  88,
  89,
  89,
  90,
  90,
  90,
  91,
  91,
  91,
  91,
  91,
  92,
  92,
  92,
  93,
  93,
  93,
  93,
  94,
  94,
  94,
  94,
  95,
  95,
  95,
  95,
  95,
  95,
  96,
  96,
  96,
  0,
  0
}; // weak
__int16 word_4F9378[108] =
{
  0,
  1,
  2,
  0,
  1,
  1,
  1,
  1,
  1,
  2,
  1,
  1,
  3,
  1,
  2,
  5,
  7,
  8,
  2,
  1,
  4,
  3,
  0,
  5,
  5,
  4,
  1,
  1,
  1,
  0,
  2,
  2,
  3,
  3,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  3,
  3,
  1,
  4,
  5,
  4,
  4,
  1,
  3,
  1,
  3,
  0,
  2,
  1,
  1,
  1,
  1,
  8,
  6,
  1,
  1,
  1,
  1,
  1,
  3,
  1,
  3,
  3,
  3,
  3,
  3,
  3,
  1,
  3,
  1,
  3,
  1,
  3,
  3,
  1,
  3,
  3,
  3,
  3,
  1,
  3,
  3,
  1,
  3,
  3,
  3,
  1,
  2,
  2,
  2,
  1,
  4,
  4,
  3,
  2,
  2,
  1,
  3,
  1,
  0,
  0
}; // weak
__int16 word_4F9450[135] =
{
  3,
  0,
  0,
  39,
  61,
  62,
  63,
  64,
  35,
  36,
  37,
  38,
  34,
  0,
  0,
  0,
  0,
  0,
  0,
  29,
  13,
  0,
  3,
  0,
  0,
  0,
  2,
  4,
  5,
  6,
  7,
  8,
  10,
  0,
  105,
  11,
  103,
  0,
  65,
  67,
  74,
  76,
  78,
  81,
  86,
  89,
  93,
  97,
  9,
  0,
  0,
  0,
  0,
  0,
  31,
  30,
  0,
  28,
  0,
  0,
  105,
  96,
  39,
  94,
  95,
  40,
  44,
  0,
  41,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  101,
  102,
  14,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  100,
  0,
  26,
  0,
  29,
  27,
  0,
  0,
  0,
  0,
  32,
  104,
  12,
  0,
  0,
  33,
  0,
  53,
  0,
  68,
  69,
  70,
  71,
  72,
  73,
  66,
  75,
  77,
  79,
  80,
  82,
  84,
  83,
  85,
  87,
  88,
  90,
  91,
  92,
  99,
  0
}; // weak
__int16 word_4F955E[97] =
{
  0,
  40,
  0,
  0,
  0,
  0,
  0,
  43,
  42,
  55,
  0,
  51,
  98,
  23,
  29,
  24,
  15,
  0,
  0,
  45,
  47,
  40,
  54,
  0,
  53,
  25,
  0,
  0,
  22,
  19,
  0,
  46,
  57,
  58,
  60,
  3,
  52,
  16,
  0,
  0,
  0,
  18,
  0,
  49,
  0,
  3,
  3,
  17,
  48,
  0,
  59,
  0,
  0,
  50,
  0,
  0,
  0,
  189,
  1,
  26,
  27,
  28,
  29,
  163,
  164,
  175,
  30,
  97,
  98,
  56,
  31,
  32,
  33,
  34,
  66,
  67,
  68,
  154,
  177,
  145,
  146,
  69,
  169,
  170,
  35,
  36,
  37,
  38,
  39,
  40,
  41,
  42,
  43,
  44,
  45,
  46,
  47
}; // weak
__int16 word_4F9620[135] =
{
  32768,
  115,
  -2,
  8,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  27,
  42,
  49,
  73,
  78,
  80,
  50,
  32768,
  50,
  32768,
  50,
  101,
  101,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  130,
  400,
  32768,
  32768,
  111,
  109,
  102,
  110,
  -34,
  17,
  -1,
  63,
  32768,
  32768,
  32768,
  32768,
  12,
  369,
  50,
  50,
  50,
  32768,
  32768,
  118,
  32768,
  125,
  227,
  -13,
  32768,
  32768,
  32768,
  32768,
  14,
  119,
  -9,
  32768,
  127,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  32768,
  32768,
  32768,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  50,
  32768,
  128,
  32768,
  129,
  50,
  32768,
  152,
  131,
  132,
  135,
  32768,
  32768,
  32768,
  23,
  50,
  32768,
  152,
  67,
  134,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  110,
  -34,
  17,
  17,
  -1,
  -1,
  -1,
  -1,
  63,
  63,
  32768,
  32768,
  32768,
  32768,
  395
}; // weak
__int16 word_4F972E[97] =
{
  139,
  137,
  395,
  395,
  136,
  133,
  138,
  32768,
  32768,
  165,
  144,
  142,
  32768,
  32768,
  50,
  32768,
  180,
  169,
  170,
  32768,
  133,
  32768,
  32768,
  16,
  67,
  32768,
  395,
  103,
  178,
  169,
  50,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  32768,
  172,
  173,
  174,
  32768,
  177,
  176,
  283,
  32768,
  32768,
  32768,
  32768,
  50,
  32768,
  171,
  339,
  32768,
  201,
  202,
  32768,
  32768,
  -22,
  -89,
  32768,
  158,
  32768,
  45,
  32768,
  32768,
  32768,
  32768,
  32768,
  -78,
  32768,
  160,
  -48,
  0,
  68,
  32768,
  104,
  56,
  29,
  57,
  32768,
  32768,
  32768,
  32768,
  32768,
  -97,
  -18,
  -67,
  32768,
  140,
  143,
  -14,
  59,
  10,
  3,
  32768,
  32768
}; // weak
__int16 word_4F97F0[460] =
{
  59,
  57,
  100,
  58,
  113,
  114,
  115,
  116,
  117,
  118,
  141,
  83,
  84,
  70,
  109,
  3,
  4,
  5,
  6,
  7,
  135,
  48,
  110,
  60,
  63,
  64,
  61,
  4,
  5,
  6,
  7,
  95,
  49,
  101,
  102,
  103,
  21,
  94,
  -56,
  167,
  107,
  142,
  77,
  78,
  168,
  148,
  89,
  90,
  150,
  151,
  140,
  50,
  112,
  3,
  4,
  5,
  6,
  7,
  85,
  86,
  87,
  88,
  119,
  144,
  23,
  173,
  51,
  24,
  25,
  122,
  123,
  160,
  172,
  52,
  21,
  8,
  9,
  10,
  11,
  12,
  57,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  60,
  130,
  131,
  132,
  53,
  178,
  128,
  129,
  54,
  23,
  55,
  62,
  24,
  25,
  4,
  5,
  6,
  7,
  144,
  91,
  92,
  93,
  -1,
  2,
  178,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  57,
  16,
  65,
  79,
  17,
  18,
  19,
  20,
  21,
  80,
  104,
  81,
  22,
  124,
  125,
  126,
  127,
  179,
  82,
  105,
  111,
  108,
  133,
  134,
  136,
  137,
  138,
  186,
  187,
  139,
  147,
  149,
  107,
  152,
  155,
  153,
  23,
  156,
  158,
  24,
  25,
  2,
  159,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  162,
  16,
  -20,
  -20,
  17,
  18,
  19,
  20,
  21,
  161,
  174,
  165,
  22,
  -20,
  190,
  191,
  182,
  180,
  181,
  183,
  184,
  96,
  176,
  99,
  166,
  157,
  188,
  143,
  0,
  171,
  0,
  0,
  0,
  0,
  120,
  0,
  23,
  0,
  121,
  24,
  25,
  2,
  0,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  16,
  0,
  0,
  17,
  18,
  19,
  20,
  21,
  0,
  0,
  0,
  22,
  106,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  23,
  0,
  0,
  24,
  25,
  2,
  0,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  16,
  0,
  0,
  17,
  18,
  19,
  20,
  21,
  0,
  0,
  0,
  22,
  185,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  23,
  0,
  0,
  24,
  25,
  2,
  0,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  16,
  0,
  0,
  17,
  18,
  19,
  20,
  21,
  0,
  0,
  0,
  22,
  -21,
  0,
  0,
  0,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  23,
  20,
  21,
  24,
  25,
  2,
  0,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  16,
  0,
  0,
  17,
  18,
  19,
  20,
  21,
  0,
  23,
  0,
  22,
  24,
  25,
  70,
  0,
  0,
  0,
  0,
  0,
  0,
  71,
  72,
  73,
  74,
  75,
  76,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  23,
  0,
  0,
  24,
  25,
  0,
  0,
  0,
  77,
  78,
  0,
  0,
  0
}; // weak
__int16 word_4F9B88[458] =
{
  22,
  19,
  50,
  21,
  71,
  72,
  73,
  74,
  75,
  76,
  107,
  45,
  46,
  26,
  23,
  3,
  4,
  5,
  6,
  7,
  98,
  23,
  31,
  23,
  24,
  25,
  23,
  4,
  5,
  6,
  7,
  49,
  24,
  51,
  52,
  53,
  24,
  25,
  24,
  23,
  26,
  108,
  55,
  56,
  28,
  134,
  47,
  48,
  137,
  138,
  27,
  24,
  70,
  3,
  4,
  5,
  6,
  7,
  41,
  42,
  43,
  44,
  80,
  111,
  52,
  162,
  24,
  55,
  56,
  83,
  84,
  149,
  161,
  24,
  24,
  8,
  9,
  10,
  11,
  12,
  98,
  81,
  82,
  83,
  84,
  85,
  86,
  87,
  88,
  89,
  90,
  91,
  92,
  93,
  91,
  92,
  93,
  24,
  165,
  89,
  90,
  23,
  52,
  23,
  3,
  55,
  56,
  4,
  5,
  6,
  7,
  159,
  49,
  50,
  51,
  0,
  1,
  184,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  149,
  17,
  3,
  23,
  20,
  21,
  22,
  23,
  24,
  31,
  23,
  40,
  28,
  85,
  86,
  87,
  88,
  170,
  39,
  25,
  24,
  33,
  25,
  25,
  3,
  25,
  25,
  180,
  181,
  25,
  27,
  23,
  26,
  28,
  27,
  33,
  52,
  3,
  25,
  55,
  56,
  1,
  31,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  18,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  16,
  19,
  28,
  28,
  29,
  0,
  0,
  29,
  32,
  32,
  29,
  31,
  50,
  164,
  50,
  155,
  144,
  184,
  110,
  -1,
  159,
  -1,
  -1,
  -1,
  -1,
  81,
  -1,
  52,
  -1,
  82,
  55,
  56,
  1,
  -1,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  -1,
  17,
  -1,
  -1,
  20,
  21,
  22,
  23,
  24,
  -1,
  -1,
  -1,
  28,
  29,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  52,
  -1,
  -1,
  55,
  56,
  1,
  -1,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  -1,
  17,
  -1,
  -1,
  20,
  21,
  22,
  23,
  24,
  -1,
  -1,
  -1,
  28,
  29,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  52,
  -1,
  -1,
  55,
  56,
  1,
  -1,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  -1,
  17,
  -1,
  -1,
  20,
  21,
  22,
  23,
  24,
  -1,
  -1,
  -1,
  28,
  29,
  -1,
  -1,
  -1,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  52,
  23,
  24,
  55,
  56,
  1,
  -1,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  -1,
  17,
  -1,
  -1,
  20,
  21,
  22,
  23,
  24,
  -1,
  52,
  -1,
  28,
  55,
  56,
  26,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  33,
  34,
  35,
  36,
  37,
  38,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  52,
  -1,
  -1,
  55,
  56,
  -1,
  -1,
  -1,
  55,
  56,
  0
}; // weak
const char aErrorLineD[] = "Error(line %d) : "; // idb
const char byte_4FA608 = '\xB0'; // idb
const char byte_4FA62C = '\xC0'; // idb
__int16 word_4FA7E8[168] =
{
  0,
  0,
  0,
  61,
  59,
  58,
  57,
  35,
  59,
  59,
  28,
  59,
  45,
  46,
  26,
  24,
  51,
  25,
  27,
  4,
  4,
  52,
  44,
  36,
  29,
  38,
  23,
  47,
  48,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  49,
  59,
  50,
  58,
  41,
  0,
  7,
  0,
  0,
  34,
  54,
  32,
  42,
  30,
  43,
  31,
  56,
  55,
  33,
  0,
  4,
  0,
  37,
  40,
  39,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  16,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  53,
  0,
  0,
  5,
  3,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  23,
  13,
  9,
  23,
  23,
  23,
  23,
  23,
  23,
  0,
  0,
  11,
  23,
  19,
  23,
  23,
  17,
  23,
  23,
  23,
  23,
  23,
  6,
  8,
  23,
  0,
  0,
  15,
  23,
  23,
  10,
  23,
  23,
  23,
  14,
  0,
  0,
  23,
  23,
  22,
  12,
  18,
  0,
  0,
  23,
  20,
  0,
  0,
  21,
  2,
  0,
  0,
  0,
  0,
  2,
  0,
  1,
  2,
  0,
  0,
  2,
  0,
  2,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0
}; // weak
char byte_4FA938[] = { '\0' }; // weak
char byte_4FAD38[] = { '\0' }; // weak
__int16 word_4FAE08[180] =
{
  0,
  0,
  0,
  247,
  248,
  244,
  248,
  223,
  239,
  19,
  221,
  234,
  248,
  248,
  219,
  40,
  248,
  39,
  43,
  40,
  52,
  248,
  248,
  218,
  217,
  216,
  0,
  248,
  248,
  22,
  31,
  204,
  197,
  39,
  37,
  202,
  29,
  192,
  192,
  196,
  248,
  182,
  248,
  228,
  248,
  224,
  248,
  195,
  187,
  248,
  248,
  248,
  248,
  248,
  248,
  248,
  248,
  248,
  248,
  57,
  64,
  0,
  248,
  248,
  248,
  0,
  185,
  192,
  181,
  183,
  188,
  178,
  181,
  178,
  176,
  0,
  173,
  172,
  173,
  177,
  168,
  175,
  174,
  248,
  176,
  178,
  66,
  0,
  169,
  178,
  173,
  161,
  175,
  170,
  159,
  172,
  0,
  0,
  155,
  162,
  154,
  164,
  164,
  156,
  157,
  154,
  0,
  154,
  0,
  154,
  145,
  0,
  156,
  144,
  145,
  146,
  154,
  0,
  0,
  151,
  143,
  137,
  0,
  137,
  134,
  0,
  132,
  136,
  129,
  0,
  125,
  125,
  111,
  108,
  0,
  0,
  0,
  149,
  105,
  100,
  0,
  130,
  83,
  0,
  84,
  85,
  126,
  87,
  114,
  89,
  110,
  248,
  0,
  111,
  90,
  0,
  92,
  109,
  106,
  97,
  95,
  93,
  99,
  104,
  248,
  117,
  120,
  123,
  128,
  134,
  141,
  147,
  153,
  156,
  162,
  168,
  174,
  0,
  0,
  0
}; // weak
__int16 word_4FAF70[] = { 0 }; // weak
__int16 word_4FB0D8[300] =
{
  0,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  4,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  26,
  26,
  27,
  28,
  26,
  29,
  30,
  31,
  32,
  33,
  26,
  26,
  34,
  26,
  26,
  26,
  26,
  35,
  36,
  37,
  26,
  38,
  39,
  40,
  41,
  42,
  47,
  52,
  54,
  56,
  59,
  48,
  60,
  60,
  57,
  68,
  55,
  53,
  66,
  67,
  58,
  61,
  59,
  72,
  60,
  60,
  75,
  69,
  78,
  86,
  86,
  79,
  76,
  73,
  59,
  74,
  60,
  60,
  86,
  86,
  145,
  147,
  145,
  146,
  147,
  146,
  147,
  156,
  148,
  156,
  161,
  148,
  160,
  148,
  156,
  157,
  156,
  157,
  158,
  164,
  158,
  156,
  157,
  161,
  157,
  158,
  160,
  158,
  153,
  157,
  151,
  153,
  158,
  45,
  45,
  45,
  45,
  45,
  45,
  45,
  65,
  65,
  65,
  87,
  87,
  144,
  151,
  141,
  143,
  144,
  144,
  149,
  149,
  142,
  149,
  149,
  149,
  150,
  150,
  150,
  150,
  150,
  150,
  150,
  152,
  152,
  141,
  140,
  152,
  152,
  154,
  139,
  138,
  137,
  154,
  154,
  155,
  155,
  155,
  159,
  136,
  159,
  159,
  159,
  159,
  162,
  135,
  134,
  133,
  162,
  162,
  163,
  132,
  163,
  163,
  163,
  163,
  131,
  130,
  129,
  128,
  127,
  126,
  125,
  117,
  124,
  123,
  122,
  121,
  120,
  119,
  118,
  117,
  116,
  115,
  114,
  113,
  112,
  111,
  110,
  109,
  108,
  107,
  106,
  105,
  104,
  103,
  102,
  101,
  100,
  99,
  98,
  97,
  96,
  95,
  94,
  93,
  92,
  91,
  90,
  89,
  88,
  85,
  84,
  46,
  43,
  83,
  82,
  81,
  80,
  77,
  71,
  70,
  64,
  63,
  62,
  51,
  50,
  49,
  46,
  44,
  43,
  164,
  3,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  0
}; // weak
__int16 word_4FB330[] = { 0 }; // weak
__int16 word_4FB332[299] =
{
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  9,
  15,
  17,
  18,
  19,
  9,
  19,
  19,
  18,
  30,
  17,
  15,
  29,
  29,
  18,
  19,
  20,
  33,
  20,
  20,
  34,
  30,
  36,
  59,
  59,
  36,
  34,
  33,
  60,
  33,
  60,
  60,
  86,
  86,
  142,
  144,
  145,
  142,
  147,
  145,
  149,
  154,
  144,
  156,
  161,
  147,
  160,
  149,
  159,
  154,
  162,
  156,
  154,
  160,
  156,
  163,
  159,
  158,
  162,
  159,
  157,
  162,
  153,
  163,
  150,
  148,
  163,
  165,
  165,
  165,
  165,
  165,
  165,
  165,
  166,
  166,
  166,
  167,
  167,
  168,
  146,
  141,
  139,
  168,
  168,
  169,
  169,
  138,
  169,
  169,
  169,
  170,
  170,
  170,
  170,
  170,
  170,
  170,
  171,
  171,
  137,
  133,
  171,
  171,
  172,
  132,
  131,
  130,
  172,
  172,
  173,
  173,
  173,
  174,
  128,
  174,
  174,
  174,
  174,
  175,
  127,
  126,
  124,
  175,
  175,
  176,
  123,
  176,
  176,
  176,
  176,
  121,
  120,
  119,
  116,
  115,
  114,
  113,
  112,
  110,
  109,
  107,
  105,
  104,
  103,
  102,
  101,
  100,
  99,
  98,
  95,
  94,
  93,
  92,
  91,
  90,
  89,
  88,
  85,
  84,
  82,
  81,
  80,
  79,
  78,
  77,
  76,
  74,
  73,
  72,
  71,
  70,
  69,
  68,
  67,
  66,
  48,
  47,
  45,
  43,
  41,
  39,
  38,
  37,
  35,
  32,
  31,
  25,
  24,
  23,
  14,
  11,
  10,
  8,
  7,
  5,
  3,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  164,
  0
}; // weak
void *IOPCode::`vftable' = &IOPCode::`vector deleting destructor'; // weak
void *COP_int3::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_nop::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_ret::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fnstsw_ax::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
const char byte_4FB6F4 = '\xB0'; // idb
void *COP_call::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_mov::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_add::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_sub::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_imul::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_idiv::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_push::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_pop::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_jmp::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_jcc::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_jmpmark::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_cmp::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
const char aSetcc[] = "setcc "; // idb
void *COP_setcc::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fld::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fadd::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fsub::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fmul::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fdiv::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fstp::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_fcomp::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_test_ah::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_itoa::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_ftoa::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_malloc::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_free::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_strcpy::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_strcmp::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
void *COP_strlen::`vftable' = &COP_test_ah::`scalar deleting destructor'; // weak
const char byte_4FBBC0 = '\xC1'; // idb
const int extra_lbits[29] =
{
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  1,
  1,
  1,
  1,
  2,
  2,
  2,
  2,
  3,
  3,
  3,
  3,
  4,
  4,
  4,
  4,
  5,
  5,
  5,
  5,
  0
}; // idb
const int extra_dbits[30] =
{
  0,
  0,
  0,
  0,
  1,
  1,
  2,
  2,
  3,
  3,
  4,
  4,
  5,
  5,
  6,
  6,
  7,
  7,
  8,
  8,
  9,
  9,
  10,
  10,
  11,
  11,
  12,
  12,
  13,
  13
}; // idb
const unsigned __int8 bl_order[19] =
{
  16u,
  17u,
  18u,
  0u,
  8u,
  7u,
  9u,
  6u,
  10u,
  5u,
  11u,
  4u,
  12u,
  3u,
  13u,
  2u,
  14u,
  1u,
  15u
}; // idb
const ct_data_s static_ltree[288] =
{
  { { 12u }, { 8u } },
  { { 140u }, { 8u } },
  { { 76u }, { 8u } },
  { { 204u }, { 8u } },
  { { 44u }, { 8u } },
  { { 172u }, { 8u } },
  { { 108u }, { 8u } },
  { { 236u }, { 8u } },
  { { 28u }, { 8u } },
  { { 156u }, { 8u } },
  { { 92u }, { 8u } },
  { { 220u }, { 8u } },
  { { 60u }, { 8u } },
  { { 188u }, { 8u } },
  { { 124u }, { 8u } },
  { { 252u }, { 8u } },
  { { 2u }, { 8u } },
  { { 130u }, { 8u } },
  { { 66u }, { 8u } },
  { { 194u }, { 8u } },
  { { 34u }, { 8u } },
  { { 162u }, { 8u } },
  { { 98u }, { 8u } },
  { { 226u }, { 8u } },
  { { 18u }, { 8u } },
  { { 146u }, { 8u } },
  { { 82u }, { 8u } },
  { { 210u }, { 8u } },
  { { 50u }, { 8u } },
  { { 178u }, { 8u } },
  { { 114u }, { 8u } },
  { { 242u }, { 8u } },
  { { 10u }, { 8u } },
  { { 138u }, { 8u } },
  { { 74u }, { 8u } },
  { { 202u }, { 8u } },
  { { 42u }, { 8u } },
  { { 170u }, { 8u } },
  { { 106u }, { 8u } },
  { { 234u }, { 8u } },
  { { 26u }, { 8u } },
  { { 154u }, { 8u } },
  { { 90u }, { 8u } },
  { { 218u }, { 8u } },
  { { 58u }, { 8u } },
  { { 186u }, { 8u } },
  { { 122u }, { 8u } },
  { { 250u }, { 8u } },
  { { 6u }, { 8u } },
  { { 134u }, { 8u } },
  { { 70u }, { 8u } },
  { { 198u }, { 8u } },
  { { 38u }, { 8u } },
  { { 166u }, { 8u } },
  { { 102u }, { 8u } },
  { { 230u }, { 8u } },
  { { 22u }, { 8u } },
  { { 150u }, { 8u } },
  { { 86u }, { 8u } },
  { { 214u }, { 8u } },
  { { 54u }, { 8u } },
  { { 182u }, { 8u } },
  { { 118u }, { 8u } },
  { { 246u }, { 8u } },
  { { 14u }, { 8u } },
  { { 142u }, { 8u } },
  { { 78u }, { 8u } },
  { { 206u }, { 8u } },
  { { 46u }, { 8u } },
  { { 174u }, { 8u } },
  { { 110u }, { 8u } },
  { { 238u }, { 8u } },
  { { 30u }, { 8u } },
  { { 158u }, { 8u } },
  { { 94u }, { 8u } },
  { { 222u }, { 8u } },
  { { 62u }, { 8u } },
  { { 190u }, { 8u } },
  { { 126u }, { 8u } },
  { { 254u }, { 8u } },
  { { 1u }, { 8u } },
  { { 129u }, { 8u } },
  { { 65u }, { 8u } },
  { { 193u }, { 8u } },
  { { 33u }, { 8u } },
  { { 161u }, { 8u } },
  { { 97u }, { 8u } },
  { { 225u }, { 8u } },
  { { 17u }, { 8u } },
  { { 145u }, { 8u } },
  { { 81u }, { 8u } },
  { { 209u }, { 8u } },
  { { 49u }, { 8u } },
  { { 177u }, { 8u } },
  { { 113u }, { 8u } },
  { { 241u }, { 8u } },
  { { 9u }, { 8u } },
  { { 137u }, { 8u } },
  { { 73u }, { 8u } },
  { { 201u }, { 8u } },
  { { 41u }, { 8u } },
  { { 169u }, { 8u } },
  { { 105u }, { 8u } },
  { { 233u }, { 8u } },
  { { 25u }, { 8u } },
  { { 153u }, { 8u } },
  { { 89u }, { 8u } },
  { { 217u }, { 8u } },
  { { 57u }, { 8u } },
  { { 185u }, { 8u } },
  { { 121u }, { 8u } },
  { { 249u }, { 8u } },
  { { 5u }, { 8u } },
  { { 133u }, { 8u } },
  { { 69u }, { 8u } },
  { { 197u }, { 8u } },
  { { 37u }, { 8u } },
  { { 165u }, { 8u } },
  { { 101u }, { 8u } },
  { { 229u }, { 8u } },
  { { 21u }, { 8u } },
  { { 149u }, { 8u } },
  { { 85u }, { 8u } },
  { { 213u }, { 8u } },
  { { 53u }, { 8u } },
  { { 181u }, { 8u } },
  { { 117u }, { 8u } },
  { { 245u }, { 8u } },
  { { 13u }, { 8u } },
  { { 141u }, { 8u } },
  { { 77u }, { 8u } },
  { { 205u }, { 8u } },
  { { 45u }, { 8u } },
  { { 173u }, { 8u } },
  { { 109u }, { 8u } },
  { { 237u }, { 8u } },
  { { 29u }, { 8u } },
  { { 157u }, { 8u } },
  { { 93u }, { 8u } },
  { { 221u }, { 8u } },
  { { 61u }, { 8u } },
  { { 189u }, { 8u } },
  { { 125u }, { 8u } },
  { { 253u }, { 8u } },
  { { 19u }, { 9u } },
  { { 275u }, { 9u } },
  { { 147u }, { 9u } },
  { { 403u }, { 9u } },
  { { 83u }, { 9u } },
  { { 339u }, { 9u } },
  { { 211u }, { 9u } },
  { { 467u }, { 9u } },
  { { 51u }, { 9u } },
  { { 307u }, { 9u } },
  { { 179u }, { 9u } },
  { { 435u }, { 9u } },
  { { 115u }, { 9u } },
  { { 371u }, { 9u } },
  { { 243u }, { 9u } },
  { { 499u }, { 9u } },
  { { 11u }, { 9u } },
  { { 267u }, { 9u } },
  { { 139u }, { 9u } },
  { { 395u }, { 9u } },
  { { 75u }, { 9u } },
  { { 331u }, { 9u } },
  { { 203u }, { 9u } },
  { { 459u }, { 9u } },
  { { 43u }, { 9u } },
  { { 299u }, { 9u } },
  { { 171u }, { 9u } },
  { { 427u }, { 9u } },
  { { 107u }, { 9u } },
  { { 363u }, { 9u } },
  { { 235u }, { 9u } },
  { { 491u }, { 9u } },
  { { 27u }, { 9u } },
  { { 283u }, { 9u } },
  { { 155u }, { 9u } },
  { { 411u }, { 9u } },
  { { 91u }, { 9u } },
  { { 347u }, { 9u } },
  { { 219u }, { 9u } },
  { { 475u }, { 9u } },
  { { 59u }, { 9u } },
  { { 315u }, { 9u } },
  { { 187u }, { 9u } },
  { { 443u }, { 9u } },
  { { 123u }, { 9u } },
  { { 379u }, { 9u } },
  { { 251u }, { 9u } },
  { { 507u }, { 9u } },
  { { 7u }, { 9u } },
  { { 263u }, { 9u } },
  { { 135u }, { 9u } },
  { { 391u }, { 9u } },
  { { 71u }, { 9u } },
  { { 327u }, { 9u } },
  { { 199u }, { 9u } },
  { { 455u }, { 9u } },
  { { 39u }, { 9u } },
  { { 295u }, { 9u } },
  { { 167u }, { 9u } },
  { { 423u }, { 9u } },
  { { 103u }, { 9u } },
  { { 359u }, { 9u } },
  { { 231u }, { 9u } },
  { { 487u }, { 9u } },
  { { 23u }, { 9u } },
  { { 279u }, { 9u } },
  { { 151u }, { 9u } },
  { { 407u }, { 9u } },
  { { 87u }, { 9u } },
  { { 343u }, { 9u } },
  { { 215u }, { 9u } },
  { { 471u }, { 9u } },
  { { 55u }, { 9u } },
  { { 311u }, { 9u } },
  { { 183u }, { 9u } },
  { { 439u }, { 9u } },
  { { 119u }, { 9u } },
  { { 375u }, { 9u } },
  { { 247u }, { 9u } },
  { { 503u }, { 9u } },
  { { 15u }, { 9u } },
  { { 271u }, { 9u } },
  { { 143u }, { 9u } },
  { { 399u }, { 9u } },
  { { 79u }, { 9u } },
  { { 335u }, { 9u } },
  { { 207u }, { 9u } },
  { { 463u }, { 9u } },
  { { 47u }, { 9u } },
  { { 303u }, { 9u } },
  { { 175u }, { 9u } },
  { { 431u }, { 9u } },
  { { 111u }, { 9u } },
  { { 367u }, { 9u } },
  { { 239u }, { 9u } },
  { { 495u }, { 9u } },
  { { 31u }, { 9u } },
  { { 287u }, { 9u } },
  { { 159u }, { 9u } },
  { { 415u }, { 9u } },
  { { 95u }, { 9u } },
  { { 351u }, { 9u } },
  { { 223u }, { 9u } },
  { { 479u }, { 9u } },
  { { 63u }, { 9u } },
  { { 319u }, { 9u } },
  { { 191u }, { 9u } },
  { { 447u }, { 9u } },
  { { 127u }, { 9u } },
  { { 383u }, { 9u } },
  { { 255u }, { 9u } },
  { { 511u }, { 9u } },
  { { 0u }, { 7u } },
  { { 64u }, { 7u } },
  { { 32u }, { 7u } },
  { { 96u }, { 7u } },
  { { 16u }, { 7u } },
  { { 80u }, { 7u } },
  { { 48u }, { 7u } },
  { { 112u }, { 7u } },
  { { 8u }, { 7u } },
  { { 72u }, { 7u } },
  { { 40u }, { 7u } },
  { { 104u }, { 7u } },
  { { 24u }, { 7u } },
  { { 88u }, { 7u } },
  { { 56u }, { 7u } },
  { { 120u }, { 7u } },
  { { 4u }, { 7u } },
  { { 68u }, { 7u } },
  { { 36u }, { 7u } },
  { { 100u }, { 7u } },
  { { 20u }, { 7u } },
  { { 84u }, { 7u } },
  { { 52u }, { 7u } },
  { { 116u }, { 7u } },
  { { 3u }, { 8u } },
  { { 131u }, { 8u } },
  { { 67u }, { 8u } },
  { { 195u }, { 8u } },
  { { 35u }, { 8u } },
  { { 163u }, { 8u } },
  { { 99u }, { 8u } },
  { { 227u }, { 8u } }
}; // idb
const ct_data_s static_dtree[30] =
{
  { { 0u }, { 5u } },
  { { 16u }, { 5u } },
  { { 8u }, { 5u } },
  { { 24u }, { 5u } },
  { { 4u }, { 5u } },
  { { 20u }, { 5u } },
  { { 12u }, { 5u } },
  { { 28u }, { 5u } },
  { { 2u }, { 5u } },
  { { 18u }, { 5u } },
  { { 10u }, { 5u } },
  { { 26u }, { 5u } },
  { { 6u }, { 5u } },
  { { 22u }, { 5u } },
  { { 14u }, { 5u } },
  { { 30u }, { 5u } },
  { { 1u }, { 5u } },
  { { 17u }, { 5u } },
  { { 9u }, { 5u } },
  { { 25u }, { 5u } },
  { { 5u }, { 5u } },
  { { 21u }, { 5u } },
  { { 13u }, { 5u } },
  { { 29u }, { 5u } },
  { { 3u }, { 5u } },
  { { 19u }, { 5u } },
  { { 11u }, { 5u } },
  { { 27u }, { 5u } },
  { { 7u }, { 5u } },
  { { 23u }, { 5u } }
}; // idb
const unsigned __int8 _dist_code[512] =
{
  0u,
  1u,
  2u,
  3u,
  4u,
  4u,
  5u,
  5u,
  6u,
  6u,
  6u,
  6u,
  7u,
  7u,
  7u,
  7u,
  8u,
  8u,
  8u,
  8u,
  8u,
  8u,
  8u,
  8u,
  9u,
  9u,
  9u,
  9u,
  9u,
  9u,
  9u,
  9u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  10u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  11u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  12u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  13u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  14u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  15u,
  0u,
  0u,
  16u,
  17u,
  18u,
  18u,
  19u,
  19u,
  20u,
  20u,
  20u,
  20u,
  21u,
  21u,
  21u,
  21u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  28u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u,
  29u
}; // idb
char byte_4FC340[] = { '\0' }; // weak
const unsigned __int8 _length_code[256] =
{
  0u,
  1u,
  2u,
  3u,
  4u,
  5u,
  6u,
  7u,
  8u,
  8u,
  9u,
  9u,
  10u,
  10u,
  11u,
  11u,
  12u,
  12u,
  12u,
  12u,
  13u,
  13u,
  13u,
  13u,
  14u,
  14u,
  14u,
  14u,
  15u,
  15u,
  15u,
  15u,
  16u,
  16u,
  16u,
  16u,
  16u,
  16u,
  16u,
  16u,
  17u,
  17u,
  17u,
  17u,
  17u,
  17u,
  17u,
  17u,
  18u,
  18u,
  18u,
  18u,
  18u,
  18u,
  18u,
  18u,
  19u,
  19u,
  19u,
  19u,
  19u,
  19u,
  19u,
  19u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  20u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  21u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  22u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  23u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  24u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  25u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  26u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  27u,
  28u
}; // idb
const int base_length[29] =
{
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  10,
  12,
  14,
  16,
  20,
  24,
  28,
  32,
  40,
  48,
  56,
  64,
  80,
  96,
  112,
  128,
  160,
  192,
  224,
  0
}; // idb
const int base_dist[30] =
{
  0,
  1,
  2,
  3,
  4,
  6,
  8,
  12,
  16,
  24,
  32,
  48,
  64,
  96,
  128,
  192,
  256,
  384,
  512,
  768,
  1024,
  1536,
  2048,
  3072,
  4096,
  6144,
  8192,
  12288,
  16384,
  24576
}; // idb
void *std::logic_error::`vftable' = &std::out_of_range::`vector deleting destructor'; // weak
void *std::length_error::`vftable' = &std::out_of_range::`vector deleting destructor'; // weak
void *std::out_of_range::`vftable' = &std::out_of_range::`vector deleting destructor'; // weak
const int std::_BADOFF = -1; // idb
void *std::ios_base::`vftable' = &std::ios_base::`vector deleting destructor'; // weak
void *std::runtime_error::`vftable' = &std::runtime_error::`vector deleting destructor'; // weak
void *std::ios_base::failure::`vftable' = &std::ios_base::failure::`scalar deleting destructor'; // weak
void *std::locale::facet::`vftable' = &std::locale::facet::`scalar deleting destructor'; // weak
void *std::locale::_Locimp::`vftable' = &std::locale::_Locimp::`scalar deleting destructor'; // weak
const int valid[15] = { 1, 2, 18, 10, 33, 34, 50, 42, 3, 19, 11, 35, 51, 43, 0 }; // idb
int dword_4FC9D0[15] = { 2, 18, 10, 33, 34, 50, 42, 3, 19, 11, 35, 51, 43, 0, 5248232 }; // weak
void *std::ostream::`vftable' = &std::ostream::`vector deleting destructor'; // weak
void *std::ios::`vftable' = &std::ios::`scalar deleting destructor'; // weak
void *std::ctype<char>::`vftable' = &std::ctype<char>::`vector deleting destructor'; // weak
void *std::streambuf::`vftable' = &std::streambuf::`vector deleting destructor'; // weak
void *std::filebuf::`vftable' = &std::filebuf::`scalar deleting destructor'; // weak
void *std::codecvt<char,char,int>::`vftable' = &std::codecvt<char,char,int>::`vector deleting destructor'; // weak
void *std::bad_alloc::`vftable' = &std::bad_alloc::`vector deleting destructor'; // weak
void *exception::`vftable' = &exception::`vector deleting destructor'; // weak
void *bad_cast::`vftable' = &bad_cast::`vector deleting destructor'; // weak
void *type_info::`vftable' = &type_info::`scalar deleting destructor'; // weak
EHExceptionRecord ExceptionTemplate = { 3765269347u, 1u, NULL, NULL, 3u, { 429065504u, NULL, NULL } }; // idb
const WCHAR FLOAT_0_0 = 0u; // idb
wchar_t asc_4FD0C8[33] = L"         (((((                  H"; // weak
const __int16 ctype_loc_style[127] =
{
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  104,
  40,
  40,
  40,
  40,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  32,
  72,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  132,
  132,
  132,
  132,
  132,
  132,
  132,
  132,
  132,
  132,
  16,
  16,
  16,
  16,
  16,
  16,
  16,
  385,
  385,
  385,
  385,
  385,
  385,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  257,
  16,
  16,
  16,
  16,
  16,
  16,
  386,
  386,
  386,
  386,
  386,
  386,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  258,
  16,
  16,
  16,
  16,
  32
}; // idb
const char first_127char[127] =
{
  '\x01',
  '\x02',
  '\x03',
  '\x04',
  '\x05',
  '\x06',
  '\a',
  '\b',
  '\t',
  '\n',
  '\v',
  '\f',
  '\r',
  '\x0E',
  '\x0F',
  '\x10',
  '\x11',
  '\x12',
  '\x13',
  '\x14',
  '\x15',
  '\x16',
  '\x17',
  '\x18',
  '\x19',
  '\x1A',
  '\x1B',
  '\x1C',
  '\x1D',
  '\x1E',
  '\x1F',
  ' ',
  '!',
  '\"',
  '#',
  '$',
  '%',
  '&',
  '\'',
  '(',
  ')',
  '*',
  '+',
  ',',
  '-',
  '.',
  '/',
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  ':',
  ';',
  '<',
  '=',
  '>',
  '?',
  '@',
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
  '[',
  '\\',
  ']',
  '^',
  '_',
  '`',
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'q',
  'r',
  's',
  't',
  'u',
  'v',
  'w',
  'x',
  'y',
  'z',
  '{',
  '|',
  '}',
  '~',
  '\x7F'
}; // idb
_SCOPETABLE_ENTRY stru_4FD6B0 = { -1, NULL, &_L20434 }; // weak
char __lookuptable[92] =
{
  '\x06',
  '\0',
  '\0',
  '\x06',
  '\0',
  '\x01',
  '\0',
  '\0',
  '\x10',
  '\0',
  '\x03',
  '\x06',
  '\0',
  '\x06',
  '\x02',
  '\x10',
  '\x04',
  'E',
  'E',
  'E',
  '\x05',
  '\x05',
  '\x05',
  '\x05',
  '\x05',
  '5',
  '0',
  '\0',
  'P',
  '\0',
  '\0',
  '\0',
  '\0',
  ' ',
  '(',
  '8',
  'P',
  'X',
  '\a',
  '\b',
  '\0',
  '7',
  '0',
  '0',
  'W',
  'P',
  '\a',
  '\0',
  '\0',
  ' ',
  ' ',
  '\b',
  '\0',
  '\0',
  '\0',
  '\0',
  '\b',
  '`',
  'h',
  '`',
  '`',
  '`',
  '`',
  '\0',
  '\0',
  'p',
  'p',
  'x',
  'x',
  'x',
  'x',
  '\b',
  '\a',
  '\b',
  '\0',
  '\0',
  '\a',
  '\0',
  '\b',
  '\b',
  '\b',
  '\0',
  '\0',
  '\b',
  '\0',
  '\b',
  '\0',
  '\a',
  '\b',
  '\0',
  '\0',
  '\0'
}; // weak
const tagRGLOCINFO __rgLocInfo[27] =
{
  {
    1034u,
    { '0', '4', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish - Traditional Sort",
    { 'E', 'S', 'P', '\0' },
    "Spain",
    { 'E', 'S', 'P', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    1035u,
    { '0', '4', '0', 'b', '\0', '\0', '\0', '\0' },
    "Finnish",
    { 'F', 'I', 'N', '\0' },
    "Finland",
    { 'F', 'I', 'N', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    1036u,
    { '0', '4', '0', 'c', '\0', '\0', '\0', '\0' },
    "French",
    { 'F', 'R', 'A', '\0' },
    "France",
    { 'F', 'R', 'A', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    1039u,
    { '0', '4', '0', 'f', '\0', '\0', '\0', '\0' },
    "Icelandic",
    { 'I', 'S', 'L', '\0' },
    "Iceland",
    { 'I', 'S', 'L', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    1053u,
    { '0', '4', '1', 'd', '\0', '\0', '\0', '\0' },
    "Swedish",
    { 'S', 'V', 'E', '\0' },
    "Sweden",
    { 'S', 'W', 'E', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    1069u,
    { '0', '4', '2', 'd', '\0', '\0', '\0', '\0' },
    "Basque",
    { 'E', 'U', 'Q', '\0' },
    "Spain",
    { 'E', 'S', 'P', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    2058u,
    { '0', '8', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'M', '\0' },
    "Mexico",
    { 'M', 'E', 'X', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    2060u,
    { '0', '8', '0', 'c', '\0', '\0', '\0', '\0' },
    "French",
    { 'F', 'R', 'B', '\0' },
    "Belgium",
    { 'B', 'E', 'L', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    3079u,
    { '0', 'c', '0', '7', '\0', '\0', '\0', '\0' },
    "German",
    { 'D', 'E', 'A', '\0' },
    "Austria",
    { 'A', 'U', 'T', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    3081u,
    { '0', 'c', '0', '9', '\0', '\0', '\0', '\0' },
    "English",
    { 'E', 'N', 'A', '\0' },
    "Australia",
    { 'A', 'U', 'S', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    3082u,
    { '0', 'c', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish - Modern Sort",
    { 'E', 'S', 'N', '\0' },
    "Spain",
    { 'E', 'S', 'P', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    3084u,
    { '0', 'c', '0', 'c', '\0', '\0', '\0', '\0' },
    "French",
    { 'F', 'R', 'C', '\0' },
    "Canada",
    { 'C', 'A', 'N', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    4106u,
    { '1', '0', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'G', '\0' },
    "Guatemala",
    { 'G', 'T', 'M', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    4108u,
    { '1', '0', '0', 'c', '\0', '\0', '\0', '\0' },
    "French",
    { 'F', 'R', 'S', '\0' },
    "Switzerland",
    { 'C', 'H', 'E', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    5130u,
    { '1', '4', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'C', '\0' },
    "Costa Rica",
    { 'C', 'R', 'I', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    5132u,
    { '1', '4', '0', 'c', '\0', '\0', '\0', '\0' },
    "French",
    { 'F', 'R', 'L', '\0' },
    "Luxembourg",
    { 'L', 'U', 'X', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    6154u,
    { '1', '8', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'A', '\0' },
    "Panama",
    { 'P', 'A', 'N', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    7177u,
    { '1', 'c', '0', '9', '\0', '\0', '\0', '\0' },
    "English",
    { 'E', 'N', 'S', '\0' },
    "South Africa",
    { 'Z', 'A', 'F', '\0' },
    { '4', '3', '7', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    7178u,
    { '1', 'c', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'D', '\0' },
    "Dominican Republic",
    { 'D', 'O', 'M', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    8202u,
    { '2', '0', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'V', '\0' },
    "Venezuela",
    { 'V', 'E', 'N', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    9226u,
    { '2', '4', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'O', '\0' },
    "Colombia",
    { 'C', 'O', 'L', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    10250u,
    { '2', '8', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'R', '\0' },
    "Peru",
    { 'P', 'E', 'R', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    11274u,
    { '2', 'c', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'S', '\0' },
    "Argentina",
    { 'A', 'R', 'G', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    12298u,
    { '3', '0', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'F', '\0' },
    "Ecuador",
    { 'E', 'C', 'U', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    13322u,
    { '3', '4', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'L', '\0' },
    "Chile",
    { 'C', 'H', 'L', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    14346u,
    { '3', '8', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'Y', '\0' },
    "Uruguay",
    { 'U', 'R', 'Y', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  },
  {
    15370u,
    { '3', 'c', '0', 'a', '\0', '\0', '\0', '\0' },
    "Spanish",
    { 'E', 'S', 'Z', '\0' },
    "Paraguay",
    { 'P', 'R', 'Y', '\0' },
    { '8', '5', '0', '\0', '\0', '\0', '\0', '\0' },
    { '1', '2', '5', '2', '\0', '\0', '\0', '\0' }
  }
}; // idb
char a040a[5] = "040a"; // weak
char *off_4FDF24 = "Spanish - Traditional Sort"; // weak
_UNKNOWN *off_4FDF28 = (_UNKNOWN *)0x505345; // weak
char *off_4FDF2C = "Spain"; // weak
_UNKNOWN *off_4FDF30 = (_UNKNOWN *)0x505345; // weak
_UNKNOWN unk_4FDF34; // weak
char a1252[5] = "1252"; // weak
const unsigned __int16 __rglangidNotDefault[10] = { 3084u, 3098u, 4103u, 1078u, 2060u, 1069u, 1027u, 4108u, 2064u, 2077u }; // idb
const tagLOCALETAB __rg_country[23] =
{
  { "america", { 'U', 'S', 'A', '\0' } },
  { "britain", { 'G', 'B', 'R', '\0' } },
  { "china", { 'C', 'H', 'N', '\0' } },
  { "czech", { 'C', 'Z', 'E', '\0' } },
  { "england", { 'G', 'B', 'R', '\0' } },
  { "great britain", { 'G', 'B', 'R', '\0' } },
  { "holland", { 'N', 'L', 'D', '\0' } },
  { "hong-kong", { 'H', 'K', 'G', '\0' } },
  { "new-zealand", { 'N', 'Z', 'L', '\0' } },
  { "nz", { 'N', 'Z', 'L', '\0' } },
  { "pr china", { 'C', 'H', 'N', '\0' } },
  { "pr-china", { 'C', 'H', 'N', '\0' } },
  { "puerto-rico", { 'P', 'R', 'I', '\0' } },
  { "slovak", { 'S', 'V', 'K', '\0' } },
  { "south africa", { 'Z', 'A', 'F', '\0' } },
  { "south korea", { 'K', 'O', 'R', '\0' } },
  { "south-africa", { 'Z', 'A', 'F', '\0' } },
  { "south-korea", { 'K', 'O', 'R', '\0' } },
  { "trinidad & tobago", { 'T', 'T', 'O', '\0' } },
  { "uk", { 'G', 'B', 'R', '\0' } },
  { "united-kingdom", { 'G', 'B', 'R', '\0' } },
  { "united-states", { 'U', 'S', 'A', '\0' } },
  { "us", { 'U', 'S', 'A', '\0' } }
}; // idb
const tagLOCALETAB __rg_language[65] =
{
  { "american", { 'E', 'N', 'U', '\0' } },
  { "american english", { 'E', 'N', 'U', '\0' } },
  { "american-english", { 'E', 'N', 'U', '\0' } },
  { "australian", { 'E', 'N', 'A', '\0' } },
  { "belgian", { 'N', 'L', 'B', '\0' } },
  { "canadian", { 'E', 'N', 'C', '\0' } },
  { "chh", { 'Z', 'H', 'H', '\0' } },
  { "chi", { 'Z', 'H', 'I', '\0' } },
  { "chinese", { 'C', 'H', 'S', '\0' } },
  { "chinese-hongkong", { 'Z', 'H', 'H', '\0' } },
  { "chinese-simplified", { 'C', 'H', 'S', '\0' } },
  { "chinese-singapore", { 'Z', 'H', 'I', '\0' } },
  { "chinese-traditional", { 'C', 'H', 'T', '\0' } },
  { "dutch-belgian", { 'N', 'L', 'B', '\0' } },
  { "english-american", { 'E', 'N', 'U', '\0' } },
  { "english-aus", { 'E', 'N', 'A', '\0' } },
  { "english-belize", { 'E', 'N', 'L', '\0' } },
  { "english-can", { 'E', 'N', 'C', '\0' } },
  { "english-caribbean", { 'E', 'N', 'B', '\0' } },
  { "english-ire", { 'E', 'N', 'I', '\0' } },
  { "english-jamaica", { 'E', 'N', 'J', '\0' } },
  { "english-nz", { 'E', 'N', 'Z', '\0' } },
  { "english-south africa", { 'E', 'N', 'S', '\0' } },
  { "english-trinidad y tobago", { 'E', 'N', 'T', '\0' } },
  { "english-uk", { 'E', 'N', 'G', '\0' } },
  { "english-us", { 'E', 'N', 'U', '\0' } },
  { "english-usa", { 'E', 'N', 'U', '\0' } },
  { "french-belgian", { 'F', 'R', 'B', '\0' } },
  { "french-canadian", { 'F', 'R', 'C', '\0' } },
  { "french-luxembourg", { 'F', 'R', 'L', '\0' } },
  { "french-swiss", { 'F', 'R', 'S', '\0' } },
  { "german-austrian", { 'D', 'E', 'A', '\0' } },
  { "german-lichtenstein", { 'D', 'E', 'C', '\0' } },
  { "german-luxembourg", { 'D', 'E', 'L', '\0' } },
  { "german-swiss", { 'D', 'E', 'S', '\0' } },
  { "irish-english", { 'E', 'N', 'I', '\0' } },
  { "italian-swiss", { 'I', 'T', 'S', '\0' } },
  { "norwegian", { 'N', 'O', 'R', '\0' } },
  { "norwegian-bokmal", { 'N', 'O', 'R', '\0' } },
  { "norwegian-nynorsk", { 'N', 'O', 'N', '\0' } },
  { "portuguese-brazilian", { 'P', 'T', 'B', '\0' } },
  { "spanish-argentina", { 'E', 'S', 'S', '\0' } },
  { "spanish-bolivia", { 'E', 'S', 'B', '\0' } },
  { "spanish-chile", { 'E', 'S', 'L', '\0' } },
  { "spanish-colombia", { 'E', 'S', 'O', '\0' } },
  { "spanish-costa rica", { 'E', 'S', 'C', '\0' } },
  { "spanish-dominican republic", { 'E', 'S', 'D', '\0' } },
  { "spanish-ecuador", { 'E', 'S', 'F', '\0' } },
  { "spanish-el salvador", { 'E', 'S', 'E', '\0' } },
  { "spanish-guatemala", { 'E', 'S', 'G', '\0' } },
  { "spanish-honduras", { 'E', 'S', 'H', '\0' } },
  { "spanish-mexican", { 'E', 'S', 'M', '\0' } },
  { "spanish-modern", { 'E', 'S', 'N', '\0' } },
  { "spanish-nicaragua", { 'E', 'S', 'I', '\0' } },
  { "spanish-panama", { 'E', 'S', 'A', '\0' } },
  { "spanish-paraguay", { 'E', 'S', 'Z', '\0' } },
  { "spanish-peru", { 'E', 'S', 'R', '\0' } },
  { "spanish-puerto rico", { 'E', 'S', 'U', '\0' } },
  { "spanish-uruguay", { 'E', 'S', 'Y', '\0' } },
  { "spanish-venezuela", { 'E', 'S', 'V', '\0' } },
  { "swedish-finland", { 'S', 'V', 'F', '\0' } },
  { "swiss", { 'D', 'E', 'S', '\0' } },
  { "uk", { 'E', 'N', 'G', '\0' } },
  { "us", { 'E', 'N', 'U', '\0' } },
  { "usa", { 'E', 'N', 'U', '\0' } }
}; // idb
GUID GUID_ATLVer70 = { 961297888u, 15471u, 4562u, { 129u, 123u, 0u, 192u, 79u, 121u, 122u, 183u } };
GUID IID_IAccessor = { 208878220u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IRowset = { 208878204u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_ICommandText = { 208878119u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IColumnsInfo = { 208878097u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IDBCreateCommand = { 208878109u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IDBCreateSession = { 208878173u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IDBProperties = { 208878218u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
GUID IID_IDBInitialize = { 208878219u, 10780u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
void *CCMDAutoBalance::`vftable' = &CCMDAutoBalance::DoProcess; // weak
void *CCMDDropItem::`vftable' = &CCMDDropItem::DoProcess; // weak
const char pFileName[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char pFormat[] = "CID:0x%08x %d"; // idb
void *CCMDDropItemList::`vftable' = &CCMDDropItemList::DoProcess; // weak
const char aDWorkRylSource_36[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aS_10[] = "%s "; // idb
const char szLoseCharName[5] = { '\0', '\0', '\0', '\0', '\0' }; // idb
void *CModifyDummyCharacter::`vftable' = &CModifyDummyCharacter::`vector deleting destructor'; // weak
const char aDummies_10[] = "./Dummies/"; // idb
const char aDummies_9[] = "./Dummies/"; // idb
const char aDummies_8[] = "./Dummies/"; // idb
const char aDummies_4[] = "./Dummies/"; // idb
const char aDummies_7[] = "./Dummies/"; // idb
const char aDummies_6[] = "./Dummies/"; // idb
const char aDummies_0[] = "./Dummies/"; // idb
const char aDummies_5[] = "./Dummies/"; // idb
const char aDummies_11[] = "./Dummies/"; // idb
const char aDummies_3[] = "./Dummies/"; // idb
const char aDummies_2[] = "./Dummies/"; // idb
const char aDummies_12[] = "./Dummies/"; // idb
const char aDummies[] = "./Dummies/"; // idb
const char aDummies_1[] = "./Dummies/"; // idb
const char szFileName[] = "./Dummies/CON"; // idb
void *CFieldGameClientDispatch::`vftable' = &CFieldGameClientDispatch::`vector deleting destructor'; // weak
void *CFieldGameClientDispatchTable::`vftable' = &CFieldGameClientDispatchTable::Initialize; // weak
void *CGameClientDispatchTable::`vftable' = &CGameClientDispatchTable::Initialize; // weak
void *CCMDLotteryEvent::`vftable' = &CCMDLotteryEvent::DoProcess; // weak
const char byte_4FF900 = '\xB8'; // idb
char byte_4FF92C[40] =
{
  '\xB0',
  '\xD4',
  '\xC0',
  '\xD3',
  ' ',
  '\xC0',
  '\xCC',
  '\xBA',
  '\xA5',
  '\xC6',
  '\xAE',
  ' ',
  '\xC3',
  '\xCA',
  '\xB1',
  '\xE2',
  '\xC8',
  '\xAD',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xCF',
  '\xBF',
  '\xB4',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4FF954[44] =
{
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xC3',
  '\xB3',
  '\xB8',
  '\xAE',
  ' ',
  '\xC5',
  '\xD7',
  '\xC0',
  '\xCC',
  '\xBA',
  '\xED',
  '\xC0',
  '\xBB',
  ' ',
  '\xC3',
  '\xCA',
  '\xB1',
  '\xE2',
  '\xC8',
  '\xAD',
  ' ',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4FF980[36] =
{
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xD0',
  '\xC0',
  '\xBB',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4FF9A4[44] =
{
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  ' ',
  '\xC7',
  '\xD5',
  '\xBC',
  '\xBA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xD0',
  '\xC0',
  '\xBB',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4FF9D0[36] =
{
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xD0',
  '\xC0',
  '\xBB',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0'
}; // weak
char byte_4FF9F4[40] =
{
  '\xB0',
  '\xD4',
  '\xC0',
  '\xD3',
  ' ',
  '\xB7',
  '\xCE',
  '\xB1',
  '\xD7',
  '\xB8',
  '\xA6',
  ' ',
  '\xC3',
  '\xCA',
  '\xB1',
  '\xE2',
  '\xC8',
  '\xAD',
  '\xC7',
  '\xCF',
  '\xB4',
  '\xC2',
  '\xB5',
  '\xA5',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0'
}; // weak
const char aDWorkRylSource_12[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
char byte_4FFABC[48] =
{
  '\xC6',
  '\xD0',
  '\xC5',
  '\xB6',
  ' ',
  '\xC3',
  '\xB3',
  '\xB8',
  '\xAE',
  ' ',
  '\xC5',
  '\xD7',
  '\xC0',
  '\xCC',
  '\xBA',
  '\xED',
  '\xC0',
  '\xBB',
  ' ',
  '\xC3',
  '\xCA',
  '\xB1',
  '\xE2',
  '\xC8',
  '\xAD',
  '\xC7',
  '\xCF',
  '\xB4',
  '\xC2',
  '\xB5',
  '\xA5',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
void *CCMDNotify::`vftable' = &CCMDNotify::DoProcess; // weak
char asc_4FFBF4 = '['; // weak
void *CPoolDispatchFactory<CRegularAgentDispatch>::`vftable' = &CPoolDispatchFactory<CRegularAgentDispatch>::`vector deleting destructor'; // weak
void *CPoolDispatchFactory<CFieldGameClientDispatch>::`vftable' = &CPoolDispatchFactory<CFieldGameClientDispatch>::`vector deleting destructor'; // weak
void *CRylGameServer::`vftable' = &CRylGameServer::`vector deleting destructor'; // weak
void *CPoolDispatchFactory<CLogDispatch>::`vftable' = &CPoolDispatchFactory<CLogDispatch>::`vector deleting destructor'; // weak
void *CPoolDispatchFactory<CChatDispatch>::`vftable' = &CPoolDispatchFactory<CChatDispatch>::`vector deleting destructor'; // weak
void *CDispatchFactory::`vftable' = &_purecall; // weak
void *CPoolDispatchFactory<CDBAgentDispatch>::`vftable' = &CPoolDispatchFactory<CDBAgentDispatch>::`scalar deleting destructor'; // weak
const char byte_4FFEBC = '\xC1'; // idb
const char aDWorkRylSource_94[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
char byte_5000E0[16] =
{
  '\xC6',
  '\xD0',
  '\xC5',
  '\xB6',
  '\xB0',
  '\xD4',
  '\xC0',
  '\xD3',
  '\xB0',
  '\xA1',
  '\xB5',
  '\xE5',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
void *CCMDStatClear::`vftable' = &CCMDStatClear::DoProcess; // weak
void *CCMDClearDummyCharacters::`vftable' = &CCMDClearDummyCharacters::DoProcess; // weak
void *CCMDDummyCharacters::`vftable' = &CCMDDummyCharacters::DoProcess; // weak
void *CCMDShowStatistics::`vftable' = &CCMDShowStatistics::DoProcess; // weak
void *CCMDReloadSetup::`vftable' = &CCMDReloadSetup::DoProcess; // weak
void *CCMDConnect::`vftable' = &CCMDConnect::DoProcess; // weak
void *CCMDFlushLog::`vftable' = &CCMDFlushLog::DoProcess; // weak
void *CCMDStartServer::`vftable' = &CCMDStartServer::DoProcess; // weak
void *CCMDPrintLog::`vftable' = &CCMDPrintLog::DoProcess; // weak
void *CCMDStatLog::`vftable' = &CCMDStatLog::DoProcess; // weak
const char aDWorkRylSource_64[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_76[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CGameServerProcessThread::`vftable' = &CGameServerProcessThread::`scalar deleting destructor'; // weak
const char aDWorkRylSource_84[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_192[] = "CID:0x%08x "; // idb
void *CProcessRYLGAME_QUIT::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
void *CProcessGAME_CONNECTTOAGENT::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
void *CMsgProc::`vftable' = &_purecall; // weak
void *CProcessRYLGAME_AUTOSTART::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
void *CProcessCOMMAND::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
const char byte_500C64 = '\xB0'; // idb
const char byte_500CC8 = '\xB0'; // idb
const char aDWorkRylSource_73[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CThread::`vftable' = &CThread::`vector deleting destructor'; // weak
void *CUDPWish::`vftable' = &CUDPWish::`scalar deleting destructor'; // weak
void *CSocketFactory::`vftable' = &CSocketFactory::`vector deleting destructor'; // weak
const char byte_500E18 = '\xC6'; // idb
const char aDWorkRylSource_71[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08xSIpSD[] = "CID:0x%08X : %s : IP : %s:%d "; // idb
void (__cdecl *const __rtc_izz[1])() = { NULL }; // idb
void (__cdecl *const __rtc_tzz[1])() = { NULL }; // idb
const _s__ThrowInfo _TI1_AVCAtlException_ATL__ = { 0u, NULL, NULL, &_CTA1_AVCAtlException_ATL__ }; // idb
const _s__ThrowInfo _TI2_AVbad_alloc_std__ = { 0u, &std::bad_alloc::~bad_alloc, NULL, &_CTA2_AVbad_alloc_std__ }; // idb
const _s__ThrowInfo _TI2_AVbad_cast__ = { 0u, &bad_cast::~bad_cast, NULL, &_CTA2_AVbad_cast__ }; // idb
const _s__ThrowInfo _TI3_AVfailure_ios_base_std__ = { 0u, &std::ios_base::failure::~failure, NULL, &_CTA3_AVfailure_ios_base_std__ }; // idb
const _s__ThrowInfo _TI3_AVlength_error_std__ = { 0u, &std::length_error::~length_error, NULL, &_CTA3_AVlength_error_std__ }; // idb
const _s__ThrowInfo _TI3_AVout_of_range_std__ = { 0u, &std::length_error::~length_error, NULL, &_CTA3_AVout_of_range_std__ }; // idb
void (__cdecl *__xc_a[1])() = { NULL }; // idb
void (__cdecl *__xc_z[1])() = { NULL }; // idb
int (__cdecl *__xi_a[1])() = { NULL }; // idb
int (__cdecl *__xi_z[1])() = { NULL }; // idb
void (__cdecl *__xp_a[1])() = { NULL }; // idb
void (__cdecl *__xp_z[1])() = { NULL }; // idb
void (__cdecl *__xt_a[1])() = { NULL }; // idb
void (__cdecl *__xt_z[1])() = { NULL }; // idb
const char *s_LogType[8] = { "NOR", "RUL", "INF", "DET", "SER", "ERR", "DBG", "WRN" }; // idb
unsigned __int8 CCreatureManager::m_aryNormalAttackable[13][13] =
{
  { 1u, 1u, 0u, 0u, 2u, 0u, 0u, 0u, 1u, 1u, 1u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u }
}; // idb
unsigned __int8 CCreatureManager::m_arySiegeTimeAttackable[13][13] =
{
  { 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 1u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 1u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 1u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 1u, 0u, 0u, 0u, 0u, 1u, 1u, 1u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
  { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u }
}; // idb
LPCSTR CServerWindowFramework::ms_this = "CServerWindowFramework"; // idb
unsigned __int8 CCell::ms_CellSize = 128u; // idb
CClass ClassTable[25] =
{
  {
    { NONE_STAT, NONE_STAT },
    { 0u, 0u },
    NONE_JOB,
    NONE_JOB,
    DEFAULT_CLASS,
    MAX_RACE,
    false
  },
  { { STR, CON }, { 1u, 1u }, Fighter, Fighter, DEFAULT_CLASS, HUMAN, false },
  { { DEX, STR }, { 1u, 1u }, Rouge, Rouge, DEFAULT_CLASS, HUMAN, false },
  { { INT, DEX }, { 1u, 1u }, Mage, Mage, DEFAULT_CLASS, HUMAN, false },
  { { WIS, CON }, { 1u, 1u }, Acolyte, Acolyte, DEFAULT_CLASS, HUMAN, false },
  { { STR, CON }, { 2u, 1u }, Defender, Fighter, JOB_CHANGE_1ST, HUMAN, true },
  { { STR, CON }, { 2u, 1u }, Warrior, Fighter, JOB_CHANGE_1ST, HUMAN, false },
  { { DEX, STR }, { 2u, 1u }, Assasin, Rouge, JOB_CHANGE_1ST, HUMAN, false },
  { { DEX, STR }, { 2u, 1u }, Archer, Rouge, JOB_CHANGE_1ST, HUMAN, true },
  { { INT, DEX }, { 2u, 1u }, Sorcerer, Mage, JOB_CHANGE_1ST, HUMAN, false },
  { { INT, DEX }, { 2u, 1u }, Enchanter, Mage, JOB_CHANGE_1ST, HUMAN, true },
  { { WIS, CON }, { 2u, 1u }, Priest, Acolyte, JOB_CHANGE_1ST, HUMAN, true },
  { { WIS, CON }, { 2u, 1u }, Cleric, Acolyte, JOB_CHANGE_1ST, HUMAN, false },
  {
    { NONE_STAT, NONE_STAT },
    { 0u, 0u },
    NONE_JOB,
    NONE_JOB,
    DEFAULT_CLASS,
    MAX_RACE,
    false
  },
  {
    { NONE_STAT, NONE_STAT },
    { 0u, 0u },
    NONE_JOB,
    NONE_JOB,
    DEFAULT_CLASS,
    MAX_RACE,
    false
  },
  {
    { NONE_STAT, NONE_STAT },
    { 0u, 0u },
    NONE_JOB,
    NONE_JOB,
    DEFAULT_CLASS,
    MAX_RACE,
    false
  },
  {
    { NONE_STAT, NONE_STAT },
    { 0u, 0u },
    NONE_JOB,
    NONE_JOB,
    DEFAULT_CLASS,
    MAX_RACE,
    false
  },
  {
    { CON, NONE_STAT },
    { 1u, 0u },
    Combatant,
    Combatant,
    DEFAULT_CLASS,
    AKHAN,
    false
  },
  {
    { DEX, NONE_STAT },
    { 1u, 0u },
    Officetor,
    Officetor,
    DEFAULT_CLASS,
    AKHAN,
    false
  },
  { { STR, CON }, { 2u, 1u }, Templar, Combatant, JOB_CHANGE_1ST, AKHAN, true },
  {
    { STR, CON },
    { 2u, 1u },
    Attacker,
    Combatant,
    JOB_CHANGE_1ST,
    AKHAN,
    false
  },
  { { DEX, CON }, { 2u, 1u }, Gunner, Combatant, JOB_CHANGE_1ST, AKHAN, true },
  { { INT, DEX }, { 2u, 1u }, RuneOff, Officetor, JOB_CHANGE_1ST, AKHAN, false },
  { { WIS, DEX }, { 2u, 1u }, LifeOff, Officetor, JOB_CHANGE_1ST, AKHAN, false },
  {
    { DEX, STR },
    { 2u, 1u },
    ShadowOff,
    Officetor,
    JOB_CHANGE_1ST,
    AKHAN,
    true
  }
}; // idb
int dword_50937C[] = { 0 }; // weak
int dword_509388[] = { 0 }; // weak
int dword_50938C[] = { 1 }; // weak
int dword_509390[] = { 2 }; // weak
int dword_509628 = 17; // weak
int dword_50962C = 2; // weak
float PI_0 = 3.1415901; // idb
float PI_1 = 3.1415901; // idb
const char *ms_szVirtualAreaScriptFileName_1 = "./Script/Game/VirtualAreaScript.txt"; // idb
unsigned __int8 aryArmourShuffleList[4] = { 0u, 5u, 4u, 3u }; // idb
__int16 usBonusExp_8[10] = { 100, 100, 100, 100, 100, 100, 100, 100, 100, 100 }; // idb
float PI_4 = 3.1415901; // idb
const char *CMonsterShout::ms_DefaultFileName = "./Script/Game/MonsterChat.gsf"; // idb
const char *CSkillMgr::ms_szSkillScriptFileName = "./Script/Game/SkillScript.txt"; // idb
const char *Item::CItemMgr::ms_szItemScriptFileName = "./Script/Game/ItemScript.txt"; // idb
const char *Item::CItemMgr::ms_szBGItemScriptFileName = "./Script/Game/BGItemScript.txt"; // idb
const char *Item::CItemMgr::ms_szChemicalScriptFileName = "./Script/Game/Chemical.txt"; // idb
int dword_509AB8[256] =
{
  696885672,
  92635524,
  382128852,
  331600848,
  340021332,
  487395612,
  747413676,
  621093156,
  491606364,
  54739776,
  403181592,
  504238620,
  289493328,
  1020063996,
  181060296,
  591618912,
  671621160,
  71581764,
  536879136,
  495817116,
  549511392,
  583197408,
  147374280,
  386339604,
  629514660,
  261063564,
  50529024,
  994800504,
  999011256,
  318968592,
  314757840,
  785310444,
  809529456,
  210534540,
  1057960764,
  680042664,
  839004720,
  500027868,
  919007988,
  876900468,
  751624428,
  361075092,
  185271048,
  390550356,
  474763356,
  457921368,
  1032696252,
  16843008,
  604250148,
  470552604,
  860058480,
  411603096,
  268439568,
  214745292,
  851636976,
  432656856,
  738992172,
  667411428,
  843215472,
  58950528,
  462132120,
  297914832,
  109478532,
  164217288,
  541089888,
  272650320,
  595829664,
  734782440,
  218956044,
  914797236,
  512660124,
  256852812,
  931640244,
  441078360,
  113689284,
  944271480,
  646357668,
  302125584,
  797942700,
  365285844,
  557932896,
  63161280,
  881111220,
  21053760,
  306336336,
  1028485500,
  227377548,
  134742024,
  521081628,
  428446104,
  0,
  420024600,
  67371012,
  323179344,
  935850996,
  566354400,
  1036907004,
  910586484,
  789521196,
  654779172,
  813740208,
  193692552,
  235799052,
  730571688,
  578986656,
  776888940,
  327390096,
  223166796,
  692674920,
  1011642492,
  151585032,
  168428040,
  1066382268,
  802153452,
  868479984,
  96846276,
  126321540,
  335810580,
  1053750012,
  608460900,
  516870876,
  772678188,
  189481800,
  436867608,
  101057028,
  553722144,
  726360936,
  642146916,
  33686016,
  902164980,
  310547088,
  176849544,
  202113036,
  864269232,
  1045328508,
  281071824,
  977957496,
  122110788,
  377918100,
  633725412,
  637936164,
  8421504,
  764256684,
  533713884,
  562143648,
  805318704,
  923218740,
  781099692,
  906375732,
  352653588,
  570565152,
  940060728,
  885321972,
  663200676,
  88424772,
  206323788,
  25264512,
  701096424,
  75792516,
  394761108,
  889532724,
  197903304,
  248431308,
  1007431740,
  826372464,
  285282576,
  130532292,
  160006536,
  893743476,
  1003222008,
  449499864,
  952692984,
  344232084,
  424235352,
  42107520,
  80003268,
  1070593020,
  155795784,
  956903736,
  658989924,
  12632256,
  265274316,
  398971860,
  948482232,
  252642060,
  244220556,
  37896768,
  587408160,
  293704080,
  743202924,
  466342872,
  612671652,
  872689716,
  834793968,
  138952776,
  46318272,
  793731948,
  1024274748,
  755835180,
  4210752,
  1049539260,
  1041117756,
  1015853244,
  29475264,
  713728680,
  982168248,
  240009804,
  356864340,
  990589752,
  483184860,
  675831912,
  1062171516,
  478974108,
  415813848,
  172638792,
  373707348,
  927429492,
  545300640,
  768467436,
  105267780,
  897954228,
  722150184,
  625303908,
  986379000,
  600040416,
  965325240,
  830583216,
  529503132,
  508449372,
  969535992,
  650568420,
  847426224,
  822161712,
  717939432,
  760045932,
  525292380,
  616882404,
  817950960,
  231588300,
  143163528,
  369496596,
  973746744,
  407392344,
  348442836,
  574775904,
  688464168,
  117900036,
  855847728,
  684253416,
  453710616,
  84214020,
  961114488,
  276861072,
  709517928,
  705307176,
  445289112
}; // weak
int dword_509EB8[256] =
{
  943196208,
  -399980320,
  741149985,
  -1540979038,
  -871379005,
  -601960750,
  -1338801229,
  -1204254544,
  -1406169181,
  1612726368,
  1410680145,
  -1006123069,
  1141130304,
  1815039843,
  1747667811,
  1478183763,
  -1073495101,
  1612857954,
  808649523,
  -1271560783,
  673777953,
  -1608482656,
  -534592798,
  -1540913245,
  -804011053,
  -1877900911,
  269549841,
  67503618,
  471600144,
  -1136882512,
  875955762,
  1208699715,
  -332410909,
  -2012706688,
  1814842464,
  -1473738592,
  337053459,
  -1006320448,
  336987666,
  -197868304,
  -1073560894,
  1141196097,
  -534658591,
  -736704814,
  1010765619,
  1010634033,
  -1945203070,
  -1743222640,
  673712160,
  1276005954,
  -197736718,
  1010699826,
  -1541044831,
  -130430479,
  202181889,
  -601894957,
  -669464368,
  673909539,
  1680229986,
  2017086066,
  606537507,
  741281571,
  -265174543,
  1882342002,
  1073889858,
  -736836400,
  1073824065,
  -1073692480,
  1882407795,
  1680295779,
  -1406366560,
  -2012509309,
  -197670925,
  -1406300767,
  -2147450752,
  471797523,
  -938816830,
  741084192,
  -1473607006,
  875824176,
  -804076846,
  134941443,
  -332476702,
  -399914527,
  1545424209,
  -1810594672,
  404228112,
  -130496272,
  1410811731,
  -1406234974,
  134744064,
  -1006254655,
  269681427,
  -871510591,
  -2079947134,
  -1204188751,
  -62926861,
  2084392305,
  -1073626687,
  808517937,
  -197802511,
  -2012575102,
  1747602018,
  -1338932815,
  -804142639,
  538968096,
  -736639021,
  131586,
  539099682,
  67372032,
  1747470432,
  1882276209,
  67569411,
  -669266989,
  -1675784815,
  -1743156847,
  1612792161,
  -1136750926,
  -467220766,
  1478052177,
  -602026543,
  1343308113,
  -1877966704,
  -602092336,
  -1743091054,
  -1608285277,
  -1473541213,
  -804208432,
  -2147384959,
  202313475,
  1141327683,
  404359698,
  -534527005,
  -332608288,
  -1945268863,
  -1136685133,
  -1810463086,
  2017151859,
  1545358416,
  -1608351070,
  -1608416863,
  1612923747,
  539165475,
  1275940161,
  -938948416,
  -1675719022,
  -1675850608,
  943327794,
  202116096,
  741215778,
  -1204122958,
  1814974050,
  -1675653229,
  1478117970,
  -265108750,
  -1877835118,
  -265042957,
  1208568129,
  2016954480,
  -871576384,
  336921873,
  -130298893,
  1882210416,
  1949648241,
  2084523891,
  875889969,
  269484048,
  197379,
  1680098400,
  1814908257,
  -1006188862,
  1949582448,
  -736770607,
  -1271626576,
  -399848734,
  134809857,
  1949714034,
  404293905,
  -62992654,
  1073758272,
  269615634,
  -534724384,
  -1136816719,
  67437825,
  -130364686,
  65793,
  -265240336,
  673843746,
  1545490002,
  -1473672799,
  1410745938,
  1073955651,
  -2080012927,
  336856080,
  -2012640895,
  -1743025261,
  -1338998608,
  -467286559,
  1208502336,
  2017020273,
  -1810397293,
  -63124240,
  471731730,
  -2147319166,
  539033889,
  -1945334656,
  404425491,
  1545555795,
  1949779827,
  1410614352,
  -1338867022,
  471665937,
  606405921,
  1276071747,
  0,
  1141261890,
  -332542495,
  1477986384,
  1343373906,
  -399782941,
  2084458098,
  -669332782,
  -938882623,
  -63058447,
  808452144,
  -1810528879,
  1680164193,
  1010568240,
  -1271494990,
  -467352352,
  -1204057165,
  2084326512,
  202247682,
  1343242320,
  943262001,
  606471714,
  808583730,
  -2080078720,
  1747536225,
  -1877769325,
  876021555,
  -467154973,
  606340128,
  -1541110624,
  -938751037,
  1343439699,
  134875650,
  -2079881341,
  -669398575,
  1275874368,
  -2147253373,
  -1945137277,
  -871444798,
  943393587,
  1208633922,
  -1271429197
}; // weak
int dword_50A2B8[256] =
{
  -1582814839,
  -2122054267,
  -757852474,
  -741338173,
  1347687492,
  287055117,
  -1599329140,
  556016901,
  1364991309,
  1128268611,
  270014472,
  303832590,
  1364201793,
  -251904820,
  -1027077430,
  1667244867,
  539502600,
  1078199364,
  538976256,
  -1852039795,
  -522182464,
  -488627518,
  -1060632376,
  320083719,
  -1583078011,
  -2087972977,
  50332419,
  1937259339,
  -1279771765,
  319820547,
  -758115646,
  -487838002,
  1886400576,
  -2138305396,
  859586319,
  -1599592312,
  842019330,
  -774103603,
  -218876218,
  1886663748,
  -521392948,
  -1852566139,
  50858763,
  1398019911,
  1348213836,
  1398283083,
  -1313063539,
  16777473,
  539239428,
  270277644,
  1936732995,
  -1869080440,
  269488128,
  -1060369204,
  -219139390,
  -774366775,
  539765772,
  -471586873,
  1919955522,
  -2088762493,
  -1818748021,
  -774893119,
  -2105276794,
  -1043854903,
  1616912448,
  1347424320,
  -1549786237,
  -471323701,
  17566989,
  -1296812410,
  -1835262322,
  1129058127,
  -1280034937,
  1381505610,
  -1027340602,
  1886926920,
  -1566300538,
  303043074,
  -1548996721,
  -774629947,
  1633689921,
  -1010826301,
  -1330367356,
  1094713665,
  1380979266,
  1903967565,
  -2121527923,
  526344,
  320610063,
  -1852302967,
  0,
  286791945,
  263172,
  1397756739,
  -202098745,
  -505404991,
  -235127347,
  1920218694,
  590098191,
  589571847,
  -1330630528,
  -2088236149,
  34344462,
  -1549259893,
  -1566563710,
  1651256910,
  -1819274365,
  1095503181,
  1634216265,
  1887190092,
  17303817,
  34081290,
  -1279508593,
  -471060529,
  -202361917,
  -1044118075,
  -2088499321,
  269751300,
  -218349874,
  1617175620,
  -757326130,
  573320718,
  1128794955,
  303569418,
  33818118,
  555753729,
  1667771211,
  1650730566,
  33554946,
  -235653691,
  -1836051838,
  -2105013622,
  789516,
  -1280298109,
  1920745038,
  -791670592,
  1920481866,
  1128531783,
  -1835788666,
  -505141819,
  572794374,
  -2139094912,
  -1582551667,
  -740548657,
  -1583341183,
  808464384,
  859059975,
  -1565774194,
  842282502,
  286528773,
  572531202,
  808990728,
  -252431164,
  -1549523065,
  1094976837,
  1078725708,
  -2122317439,
  -504878647,
  -2138831740,
  -1819011193,
  825505029,
  -1010299957,
  -1026814258,
  809253900,
  1903178049,
  286265601,
  -1010563129,
  -2121791095,
  1903441221,
  -201835573,
  -757589302,
  -252167992,
  -1869343612,
  1364728137,
  -2105539966,
  -1060895548,
  -201572401,
  1095240009,
  825768201,
  1667508039,
  -1061158720,
  -1010036785,
  -741075001,
  -1330104184,
  51121935,
  -2104750450,
  1111491138,
  589308675,
  -1852829311,
  1617701964,
  -740811829,
  -1599855484,
  808727556,
  -235916863,
  1078462536,
  -1027603774,
  1668034383,
  826031373,
  556543245,
  1077936192,
  -1296286066,
  842808846,
  -1329841012,
  -1044381247,
  -1566037366,
  -1296549238,
  1112280654,
  1364464965,
  859323147,
  -790881076,
  1617438792,
  1937522511,
  -1868817268,
  -791144248,
  1112017482,
  1381242438,
  1936996167,
  -1600118656,
  -504615475,
  1111754310,
  -1313589883,
  589835019,
  1633953093,
  -218613046,
  -471850045,
  -1313326711,
  -1313853055,
  -1818484849,
  1381768782,
  -235390519,
  -488364346,
  -1297075582,
  825241857,
  -488101174,
  1634479437,
  1398546255,
  -521919292,
  -252694336,
  -1043591731,
  -2138568568,
  303306246,
  842545674,
  1347950664,
  -791407420,
  1650467394,
  556280073,
  50595591,
  858796803,
  -521656120,
  320346891,
  17040645,
  1903704393,
  -1869606784,
  1650993738,
  573057546,
  -1835525494
}; // weak
int dword_50A6B8[256] =
{
  137377848,
  -924784600,
  220277805,
  -2036161498,
  -809251825,
  -825041890,
  -2085375949,
  -2001684424,
  -1885098961,
  1080057888,
  1162957845,
  -943471609,
  1145062404,
  1331915823,
  1264805931,
  1263753243,
  -1010581501,
  1113743394,
  53686323,
  -2051951563,
  153167913,
  -2136956896,
  -1025318878,
  -2019318745,
  -1009528813,
  -2121166831,
  17895441,
  100795398,
  202382364,
  -1934574532,
  103953462,
  1262700555,
  -807146449,
  -2004842488,
  1281387564,
  -2002737112,
  118690839,
  -993999868,
  101848086,
  -990841804,
  -1027424254,
  1161905157,
  -1042161631,
  -959261674,
  255015999,
  221330493,
  -1904047090,
  -2003789800,
  136325160,
  1312967694,
  -957156298,
  238173246,
  -2053004251,
  -906889159,
  218172429,
  -808199137,
  -925837288,
  186853419,
  1180853286,
  1249015866,
  119743527,
  253963311,
  -1041108943,
  1114796082,
  1111638018,
  -992947180,
  1094795265,
  -1061109760,
  1131638835,
  1197696039,
  -1935627220,
  -1954314229,
  -940313545,
  -1918784467,
  -2139062272,
  252910623,
  -893204470,
  203435052,
  -1969051606,
  70267956,
  -1026371566,
  184748043,
  -823989202,
  -907941847,
  1297177629,
  -2070899692,
  135272472,
  -923731912,
  1196643351,
  -1901941714,
  134219784,
  -977157115,
  51580947,
  -842937331,
  -2038266874,
  -1984841671,
  -806093761,
  1299283005,
  -1044267007,
  20000817,
  -973999051,
  -1971156982,
  1247963178,
  -2119061455,
  -1043214319,
  2105376,
  -942418921,
  33685506,
  35790882,
  67109892,
  1214277672,
  1097953329,
  117638151,
  -875309029,
  -1919837155,
  -1986947047,
  1096900641,
  -1900889026,
  -958208986,
  1230067737,
  -841884643,
  1095847953,
  -2138009584,
  -858727396,
  -1970104294,
  -2086428637,
  -1952208853,
  -1060057072,
  -2122219519,
  251857935,
  1195590663,
  168957978,
  -1008476125,
  -857674708,
  -1920889843,
  -1884046273,
  -2037214186,
  1265858619,
  1280334876,
  -2103271390,
  -2120114143,
  1130586147,
  52633635,
  1296124941,
  -926889976,
  -1902994402,
  -1936679908,
  171063354,
  201329676,
  237120558,
  -1967998918,
  1315073070,
  -1886151649,
  1246910490,
  -1024266190,
  -2104324078,
  -1007423437,
  1229015049,
  1215330360,
  -859780084,
  85005333,
  -873203653,
  1081110576,
  1165063221,
  1332968511,
  87110709,
  1052688,
  50528259,
  1147167780,
  1298230317,
  -960314362,
  1148220468,
  -976104427,
  -2068794316,
  -891099094,
  151062537,
  1181905974,
  152115225,
  -822936514,
  1077952512,
  34738194,
  -1059004384,
  -1917731779,
  83952645,
  -890046406,
  16842753,
  -1057951696,
  170010666,
  1314020382,
  -1985894359,
  1179800598,
  1128480771,
  -2055109627,
  68162580,
  -1987999735,
  -1953261541,
  -2135904208,
  -975051739,
  1212172296,
  1232173113,
  -2020371433,
  -856622020,
  236067870,
  -2105376766,
  18948129,
  -1937732596,
  185800731,
  1330863135,
  1198748727,
  1146115092,
  -2102218702,
  219225117,
  86058021,
  1329810447,
  0,
  1178747910,
  -840831955,
  1213224984,
  1112690706,
  -874256341,
  1316125758,
  -892151782,
  -910047223,
  -839779267,
  3158064,
  -2054056939,
  1164010533,
  204487740,
  -2035108810,
  -991894492,
  -1951156165,
  1282440252,
  235015182,
  1079005200,
  154220601,
  102900774,
  36843570,
  -2071952380,
  1231120425,
  -2087481325,
  120796215,
  -941366233,
  69215268,
  -2069847004,
  -876361717,
  1129533459,
  167905290,
  -2021424121,
  -908994535,
  1279282188,
  -2088534013,
  -1887204337,
  -826094578,
  187906107,
  1245857802,
  -2018266057
}; // weak
int dword_50AAB8 = -1; // weak
int dword_50AABC = 3997782; // weak
char aRb[] = "rb"; // idb
char aRt_0[] = "rt"; // idb
char byte_50AACC[20] =
{
  '\xEB',
  '\xB6',
  '\xDC',
  '\x11',
  '\xAA',
  '\xD8',
  '0',
  '|',
  '/',
  '\xE6',
  '\xBD',
  '\xC7',
  'X',
  '\xFC',
  'n',
  ']',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char asc_50AAE0 = '\x1B'; // weak
char byte_50AAF4[20] =
{
  '\xA3',
  '\xFE',
  '\x91',
  '\x8F',
  '\xEB',
  '\xE1',
  '\xDC',
  '\\',
  'A',
  '\xE0',
  ' ',
  '@',
  '\xAE',
  '\xA6',
  '\xAB',
  '\xA7',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char *ms_szVirtualAreaScriptFileName_7 = "./Script/Game/VirtualAreaScript.txt"; // idb
LPCSTR WINCONSOLE_NAME = "window console"; // idb
LPCSTR WINCONSOLE_FONTNAME = &unk_4E1DA4; // idb
LPCSTR CConsoleWindow::ms_this = "this"; // idb
unsigned int x = 1u; // idb
__int16 word_50AEBA = 0; // weak
unsigned int a = 2083801278u; // idb
const char *CMonsterMgr::m_szMonsterScriptFileName = "./Script/Game/MonsterProtoType.txt"; // idb
const char *szQuestScriptFileName = "./Script/Game/Quest.mcf"; // idb
const char *szNPCScriptFileName = "./Script/Game/NPCScript.mcf"; // idb
float PI_27 = 3.1415901; // idb
const CTypeName Skill::Type::SkillTypes[7] =
{
  { 0u, "NONE" },
  { 1u, "PASSIVE" },
  { 2u, "INSTANCE" },
  { 3u, "CAST" },
  { 4u, "CHANT" },
  { 5u, "ENCHANT" },
  { 6u, "ITEM" }
}; // idb
const CTypeName Skill::UseLimit::UseLimits[15] =
{
  { 0u, "NONE" },
  { 1u, "FORFIGHTER" },
  { 2u, "WITHSHIELD" },
  { 3u, "FORFIGHTERTWOHAND" },
  { 4u, "FORALLONEHAND" },
  { 5u, "WITHDAGGER" },
  { 6u, "WITHTHROWINGDAGGER" },
  { 7u, "RANGED" },
  { 8u, "LEFTARM" },
  { 9u, "GUARDARM" },
  { 10u, "WITHWEAPON" },
  { 11u, "ATTACKARM" },
  { 12u, "GUNARM" },
  { 13u, "KNIFEARM" },
  { 14u, "WITHCLOW" }
}; // idb
const CTypeName Skill::StatusLimit::StatusLimits[6] =
{
  { 0u, "NONE" },
  { 1u, "STR" },
  { 2u, "DEX" },
  { 3u, "CON" },
  { 4u, "INT" },
  { 5u, "WIS" }
}; // idb
const CTypeName Skill::Target::TargetTypes[12] =
{
  { 0u, "NONE" },
  { 1u, "MELEE" },
  { 2u, "FRIEND" },
  { 3u, "ENEMY" },
  { 4u, "FRDEAD" },
  { 5u, "ENDEAD" },
  { 6u, "FROBJ" },
  { 7u, "ENOBJ" },
  { 8u, "PARTY" },
  { 9u, "FREXME" },
  { 10u, "ENLINE" },
  { 11u, "SUMMON" }
}; // idb
unsigned int CCrc32Static::s_arrdwCrc32Table[256] =
{
  0u,
  1996959894u,
  3993919788u,
  2567524794u,
  124634137u,
  1886057615u,
  3915621685u,
  2657392035u,
  249268274u,
  2044508324u,
  3772115230u,
  2547177864u,
  162941995u,
  2125561021u,
  3887607047u,
  2428444049u,
  498536548u,
  1789927666u,
  4089016648u,
  2227061214u,
  450548861u,
  1843258603u,
  4107580753u,
  2211677639u,
  325883990u,
  1684777152u,
  4251122042u,
  2321926636u,
  335633487u,
  1661365465u,
  4195302755u,
  2366115317u,
  997073096u,
  1281953886u,
  3579855332u,
  2724688242u,
  1006888145u,
  1258607687u,
  3524101629u,
  2768942443u,
  901097722u,
  1119000684u,
  3686517206u,
  2898065728u,
  853044451u,
  1172266101u,
  3705015759u,
  2882616665u,
  651767980u,
  1373503546u,
  3369554304u,
  3218104598u,
  565507253u,
  1454621731u,
  3485111705u,
  3099436303u,
  671266974u,
  1594198024u,
  3322730930u,
  2970347812u,
  795835527u,
  1483230225u,
  3244367275u,
  3060149565u,
  1994146192u,
  31158534u,
  2563907772u,
  4023717930u,
  1907459465u,
  112637215u,
  2680153253u,
  3904427059u,
  2013776290u,
  251722036u,
  2517215374u,
  3775830040u,
  2137656763u,
  141376813u,
  2439277719u,
  3865271297u,
  1802195444u,
  476864866u,
  2238001368u,
  4066508878u,
  1812370925u,
  453092731u,
  2181625025u,
  4111451223u,
  1706088902u,
  314042704u,
  2344532202u,
  4240017532u,
  1658658271u,
  366619977u,
  2362670323u,
  4224994405u,
  1303535960u,
  984961486u,
  2747007092u,
  3569037538u,
  1256170817u,
  1037604311u,
  2765210733u,
  3554079995u,
  1131014506u,
  879679996u,
  2909243462u,
  3663771856u,
  1141124467u,
  855842277u,
  2852801631u,
  3708648649u,
  1342533948u,
  654459306u,
  3188396048u,
  3373015174u,
  1466479909u,
  544179635u,
  3110523913u,
  3462522015u,
  1591671054u,
  702138776u,
  2966460450u,
  3352799412u,
  1504918807u,
  783551873u,
  3082640443u,
  3233442989u,
  3988292384u,
  2596254646u,
  62317068u,
  1957810842u,
  3939845945u,
  2647816111u,
  81470997u,
  1943803523u,
  3814918930u,
  2489596804u,
  225274430u,
  2053790376u,
  3826175755u,
  2466906013u,
  167816743u,
  2097651377u,
  4027552580u,
  2265490386u,
  503444072u,
  1762050814u,
  4150417245u,
  2154129355u,
  426522225u,
  1852507879u,
  4275313526u,
  2312317920u,
  282753626u,
  1742555852u,
  4189708143u,
  2394877945u,
  397917763u,
  1622183637u,
  3604390888u,
  2714866558u,
  953729732u,
  1340076626u,
  3518719985u,
  2797360999u,
  1068828381u,
  1219638859u,
  3624741850u,
  2936675148u,
  906185462u,
  1090812512u,
  3747672003u,
  2825379669u,
  829329135u,
  1181335161u,
  3412177804u,
  3160834842u,
  628085408u,
  1382605366u,
  3423369109u,
  3138078467u,
  570562233u,
  1426400815u,
  3317316542u,
  2998733608u,
  733239954u,
  1555261956u,
  3268935591u,
  3050360625u,
  752459403u,
  1541320221u,
  2607071920u,
  3965973030u,
  1969922972u,
  40735498u,
  2617837225u,
  3943577151u,
  1913087877u,
  83908371u,
  2512341634u,
  3803740692u,
  2075208622u,
  213261112u,
  2463272603u,
  3855990285u,
  2094854071u,
  198958881u,
  2262029012u,
  4057260610u,
  1759359992u,
  534414190u,
  2176718541u,
  4139329115u,
  1873836001u,
  414664567u,
  2282248934u,
  4279200368u,
  1711684554u,
  285281116u,
  2405801727u,
  4167216745u,
  1634467795u,
  376229701u,
  2685067896u,
  3608007406u,
  1308918612u,
  956543938u,
  2808555105u,
  3495958263u,
  1231636301u,
  1047427035u,
  2932959818u,
  3654703836u,
  1088359270u,
  936918000u,
  2847714899u,
  3736837829u,
  1202900863u,
  817233897u,
  3183342108u,
  3401237130u,
  1404277552u,
  615818150u,
  3134207493u,
  3453421203u,
  1423857449u,
  601450431u,
  3009837614u,
  3294710456u,
  1567103746u,
  711928724u,
  3020668471u,
  3272380065u,
  1510334235u,
  755167117u
}; // idb
const CTypeName Item::Grade::Grades[4] = { { 0u, "ETC" }, { 1u, "NORMAL" }, { 2u, "ENHANCED" }, { 3u, "RARE" } }; // idb
const Item::ItemAttribute GemAttribute1[5][4] =
{
  { { 19u, 1 }, { 24u, 1 }, { 24u, 1 }, { 29u, 3 } },
  { { 23u, 1 }, { 28u, 1 }, { 28u, 1 }, { 31u, 3 } },
  { { 21u, 1 }, { 26u, 1 }, { 26u, 1 }, { 32u, 3 } },
  { { 20u, 1 }, { 25u, 1 }, { 25u, 1 }, { 33u, 3 } },
  { { 22u, 1 }, { 27u, 1 }, { 27u, 1 }, { 30u, 3 } }
}; // idb
const Item::ItemAttribute GemAttribute2[5][4] =
{
  { { 4u, 1 }, { 8u, 1 }, { 8u, 1 }, { 29u, 3 } },
  { { 17u, 2 }, { 15u, 1 }, { 15u, 1 }, { 31u, 3 } },
  { { 18u, 2 }, { 16u, 1 }, { 9u, 2 }, { 32u, 3 } },
  { { 2u, 1 }, { 9u, 2 }, { 10u, 1 }, { 30u, 3 } },
  { { 6u, 1 }, { 7u, 1 }, { 7u, 1 }, { 30u, 3 } }
}; // idb
const char *CLotteryEvent::ms_szEventScriptFileName = "./Script/Game/LotteryEvent.txt"; // idb
int gz_magic[2] = { 31, 139 }; // idb
float PI_35 = 3.1415901; // idb
void (__cdecl *g_pfuncCompilerMessage)(const char *) = &DefaultMessageFunction; // weak
const char *my_version = "1.1.4"; // idb
const char *z_errmsg[10] =
{
  "need dictionary",
  "stream end",
  &szLoseCharName,
  "file error",
  "stream error",
  "data error",
  "insufficient memory",
  "buffer error",
  "incompatible version",
  &szLoseCharName
}; // idb
const char **DataTypeString = &aVoid; // weak
int dword_50C570 = 1; // weak
int lineno = 1; // weak
void (__cdecl __high **g_GenCodeFunctions)(struct SNode *, enum eRegister) = &GenCode_TwoChild_Generate; // weak
char *off_50C670[15] =
{
  "EAX",
  "ECX",
  "EDX",
  "EBX",
  "ESP",
  "EBP",
  "ESI",
  "EDI",
  "NONE",
  "G",
  "GE",
  "L",
  "LE",
  "E",
  "NE"
}; // weak
char *off_50C694[6] = { "G", "GE", "L", "LE", "E", "NE" }; // weak
unsigned __int8 *arrReg4 = (unsigned __int8 *)0xD8D0C8C0; // weak
static_tree_desc_s static_l_desc = { &static_ltree, &extra_lbits, 257, 286, 15 }; // idb
static_tree_desc_s static_d_desc = { &static_dtree, &extra_dbits, 0, 30, 15 }; // idb
static_tree_desc_s static_bl_desc = { NULL, &extra_blbits, 0, 19, 7 }; // idb
LONG init = -1; // idb
const char *mods[15] =
{
  "r",
  "w",
  "w",
  "a",
  "rb",
  "wb",
  "wb",
  "ab",
  "r+",
  "w+",
  "a+",
  "r+b",
  "w+b",
  "a+b",
  NULL
}; // idb
unsigned int atcount = 10u; // idb
void (__cdecl *_aexit_rtn)(int) = &_exit; // idb
int __app_type = 2; // idb
_NLG_INFO _NLG_Destination = { 429065504u, 0u, 0u, 0u }; // idb
unsigned int __security_cookie = 3141592654u; // idb
void (__cdecl *_FPinit)(int) = &_fpmath; // idb
void (__cdecl *_FPmtinit)() = &_fpclear; // idb
void (__cdecl *_FPmtterm)() = &_fpclear; // idb
_iobuf _iob[] = ; // idb
_UNKNOWN unk_50DB98; // weak
_UNKNOWN unk_50DBA8; // weak
_iobuf str = { NULL, 0, NULL, 2, 2, 0, 0, NULL }; // idb
_DWORD dword_50DBF8[124] =
{
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0
}; // weak
_iobuf stru_50DDE8 = { NULL, 0, NULL, 0, 0, 0, 0, NULL }; // weak
_UNKNOWN unk_50DE08; // weak
lconv __lconv_c =
{
  ".",
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  &__lconv_static_null,
  '\x7F',
  '\x7F',
  '\x7F',
  '\x7F',
  '\x7F',
  '\x7F',
  '\x7F',
  '\x7F'
}; // idb
lconv *__lconv = &__lconv_c; // idb
errentry errtable[45] =
{
  { 1u, 22 },
  { 2u, 2 },
  { 3u, 2 },
  { 4u, 24 },
  { 5u, 13 },
  { 6u, 9 },
  { 7u, 12 },
  { 8u, 12 },
  { 9u, 12 },
  { 10u, 7 },
  { 11u, 8 },
  { 12u, 22 },
  { 13u, 22 },
  { 15u, 2 },
  { 16u, 13 },
  { 17u, 18 },
  { 18u, 2 },
  { 33u, 13 },
  { 53u, 2 },
  { 65u, 13 },
  { 67u, 2 },
  { 80u, 17 },
  { 82u, 13 },
  { 83u, 13 },
  { 87u, 22 },
  { 89u, 11 },
  { 108u, 13 },
  { 109u, 32 },
  { 112u, 28 },
  { 114u, 9 },
  { 6u, 22 },
  { 128u, 10 },
  { 129u, 10 },
  { 130u, 9 },
  { 131u, 22 },
  { 132u, 13 },
  { 145u, 41 },
  { 158u, 13 },
  { 161u, 2 },
  { 164u, 11 },
  { 167u, 13 },
  { 183u, 17 },
  { 206u, 2 },
  { 215u, 11 },
  { 1816u, 12 }
}; // idb
const unsigned __int16 *_pctype = &asc_4FD0C8; // idb
LPCRITICAL_SECTION locktable = NULL; // idb
char clocalestr[2] = "C"; // idb
threadlocaleinfostruct __initiallocinfo =
{
  1,
  0u,
  0u,
  { 0u, 0u, 0u, 0u, 0u, 0u },
  1,
  1,
  NULL,
  NULL,
  NULL,
  &__lconv_c,
  NULL,
  NULL,
  NULL,
  &asc_4FD0C8,
  &__lc_time_c,
  NULL
}; // idb
threadlocaleinfostruct *__ptlocinfo = &__initiallocinfo; // idb
_is_ctype_compatible Lcid_c[5] = { { 0u, 1 }, { 0u, 0 }, { 0u, 0 }, { 0u, 0 }, { 0u, 0 } }; // idb
int dword_50E154[7] = { 1, 0, 0, 0, 0, 0, 0 }; // weak
int dword_50E170 = 0; // weak
int dword_50E174 = 0; // weak
char cachein[131] =
{
  'C',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // idb
char cacheout[131] =
{
  'C',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // idb
$CCE4C80AD10999675C882A48A2A518E7 __lc_category[6] =
{
  { "LC_ALL", NULL, &__init_collate },
  { "LC_COLLATE", "C", &__init_collate },
  { "LC_CTYPE", "C", &__init_ctype },
  { "LC_MONETARY", "C", &__init_monetary },
  { "LC_NUMERIC", "C", &__init_numeric },
  { "LC_TIME", "C", &__init_time }
}; // idb
char *__nullstring = "(null)"; // idb
unsigned __int16 *__wnullstring = &aNull; // idb
unsigned int __tlsindex = 4294967295u; // idb
void (__cdecl *__pInconsistency)() = &terminate; // idb
rterrmsgs rterrs[19] =
{
  { 2, "R6002\r\n- floating point not loaded\r\n" },
  { 8, "R6008\r\n- not enough space for arguments\r\n" },
  { 9, "R6009\r\n- not enough space for environment\r\n" },
  {
    10,
    "\r\nThis application has requested the Runtime to terminate it in an unusual way.\nPlease contact the application's support team for more information.\r\n"
  },
  { 16, "R6016\r\n- not enough space for thread data\r\n" },
  { 17, "R6017\r\n- unexpected multithread lock error\r\n" },
  { 18, "R6018\r\n- unexpected heap error\r\n" },
  { 19, "R6019\r\n- unable to open console device\r\n" },
  { 24, "R6024\r\n- not enough space for _onexit/atexit table\r\n" },
  { 25, "R6025\r\n- pure virtual function call\r\n" },
  { 26, "R6026\r\n- not enough space for stdio initialization\r\n" },
  { 27, "R6027\r\n- not enough space for lowio initialization\r\n" },
  { 28, "R6028\r\n- unable to initialize heap\r\n" },
  {
    29,
    "R6029\r\n- This application cannot run using the active version of the Microsoft .NET Runtime\nPlease contact the application's support team for more information.\r\n"
  },
  { 120, "DOMAIN error\r\n" },
  { 121, "SING error\r\n" },
  { 122, "TLOSS error\r\n" },
  { 252, "\r\n" },
  { 255, "runtime error " }
}; // idb
_XCPT_ACTION _XcptActTab[] =
{
  { 3221225477u, 11, NULL },
  { 3221225501u, 4, NULL },
  { 3221225622u, 4, NULL },
  { 3221225613u, 8, NULL },
  { 3221225614u, 8, NULL },
  { 3221225615u, 8, NULL },
  { 3221225616u, 8, NULL },
  { 3221225617u, 8, NULL },
  { 3221225618u, 8, NULL },
  { 3221225619u, 8, NULL }
}; // idb
int _First_FPE_Indx = 3; // idb
int _Num_FPE = 7; // idb
int _XcptActTabCount = 10; // idb
ioinfo __badioinfo = { -1, '\x80', '\n', 0, { NULL, 0, 0, NULL, NULL, 0u } }; // idb
void (__cdecl *_cfltcvt_tab[6])() = { &_fptrap, &_fptrap, &_fptrap, &_fptrap, &_fptrap, &_fptrap }; // idb
void (__noreturn *off_50E434[5])() = { &_fptrap, &_fptrap, &_fptrap, &_fptrap, &_fptrap }; // weak
void (__noreturn *off_50E438)() = &_fptrap; // weak
void (__noreturn *off_50E43C[3])() = { &_fptrap, &_fptrap, &_fptrap }; // weak
void (__noreturn *off_50E440[2])() = { &_fptrap, &_fptrap }; // weak
void (__noreturn *off_50E444)() = &_fptrap; // weak
char _rgctypeflag[4] = { '\x01', '\x02', '\x04', '\b' }; // idb
code_page_info _rgcode_page_info[5] =
{
  {
    932,
    { 33376u, 33401u, 33u, 0u, 0u, 0u },
    {
      { 166u, 223u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 161u, 165u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 129u, 159u, 224u, 252u, 0u, 0u, 0u, 0u },
      { 64u, 126u, 128u, 252u, 0u, 0u, 0u, 0u }
    }
  },
  {
    936,
    { 41921u, 41946u, 32u, 0u, 0u, 0u },
    {
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 129u, 254u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 64u, 254u, 0u, 0u, 0u, 0u, 0u, 0u }
    }
  },
  {
    949,
    { 41921u, 41946u, 32u, 0u, 0u, 0u },
    {
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 129u, 254u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 65u, 254u, 0u, 0u, 0u, 0u, 0u, 0u }
    }
  },
  {
    950,
    { 41679u, 41700u, 26u, 41701u, 41704u, 91u },
    {
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 129u, 254u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 64u, 126u, 161u, 254u, 0u, 0u, 0u, 0u }
    }
  },
  {
    1361,
    { 55889u, 55902u, 32u, 55903u, 55914u, 50u },
    {
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u },
      { 129u, 211u, 216u, 222u, 224u, 249u, 0u, 0u },
      { 49u, 126u, 129u, 254u, 0u, 0u, 0u, 0u }
    }
  }
}; // idb
int _timezone = 28800; // idb
int _daylight = 1; // idb
int _dstbias = -3600; // idb
char *_tzname[2] = { &tzstd, &tzdst }; // idb
LPSTR Destination = &tzdst; // idb
transitiondate dststart = { -1, 0, 0 }; // idb
transitiondate dstend = { -1, 0, 0 }; // idb
int _lpdays[] = { -1 }; // idb
int dword_50E620[] = { 365 }; // weak
int _days[15] = { -1, 30, 58, 89, 119, 150, 180, 211, 242, 272, 303, 333, 364, 0, 0 }; // weak
int __lc_clike = 1; // idb
unsigned int __mb_cur_max = 1u; // idb
char __decimal_point[] = { '.' }; // idb
unsigned int __decimal_point_length = 1u; // idb
__lc_time_data *__lc_time_curr = &__lc_time_c; // idb
__lc_time_data __lc_time_c =
{
  { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" },
  {
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
  },
  {
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  },
  {
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  },
  { "AM", "PM" },
  "MM/dd/yy",
  "dddd, MMMM dd, yyyy",
  "HH:mm:ss",
  1033u,
  1,
  0
}; // idb
FpFormatDescriptor DoubleFormat = { 1024, -1023, 53, 11, 64, 1023 }; // idb
FpFormatDescriptor FloatFormat = { 128, -127, 24, 8, 32, 127 }; // idb
_LDBL12 _pow10pos[] =
{
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 160u, 2u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 200u, 5u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 250u, 8u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 64u, 156u, 12u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 80u, 195u, 15u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 36u, 244u, 18u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 128u, 150u, 152u, 22u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 32u, 188u, 190u, 25u, 64u } },
  { { 0u, 0u, 0u, 0u, 0u, 4u, 191u, 201u, 27u, 142u, 52u, 64u } },
  { { 0u, 0u, 0u, 161u, 237u, 204u, 206u, 27u, 194u, 211u, 78u, 64u } },
  { { 32u, 240u, 158u, 181u, 112u, 43u, 168u, 173u, 197u, 157u, 105u, 64u } },
  { { 208u, 93u, 253u, 37u, 229u, 26u, 142u, 79u, 25u, 235u, 131u, 64u } },
  { { 113u, 150u, 215u, 149u, 67u, 14u, 5u, 141u, 41u, 175u, 158u, 64u } },
  { { 249u, 191u, 160u, 68u, 237u, 129u, 18u, 143u, 129u, 130u, 185u, 64u } },
  { { 191u, 60u, 213u, 166u, 207u, 255u, 73u, 31u, 120u, 194u, 211u, 64u } },
  { { 111u, 198u, 224u, 140u, 233u, 128u, 201u, 71u, 186u, 147u, 168u, 65u } },
  { { 188u, 133u, 107u, 85u, 39u, 57u, 141u, 247u, 112u, 224u, 124u, 66u } },
  { { 188u, 221u, 142u, 222u, 249u, 157u, 251u, 235u, 126u, 170u, 81u, 67u } },
  { { 161u, 230u, 118u, 227u, 204u, 242u, 41u, 47u, 132u, 129u, 38u, 68u } },
  { { 40u, 16u, 23u, 170u, 248u, 174u, 16u, 227u, 197u, 196u, 250u, 68u } },
  { { 235u, 167u, 212u, 243u, 247u, 235u, 225u, 74u, 122u, 149u, 207u, 69u } },
  { { 101u, 204u, 199u, 145u, 14u, 166u, 174u, 160u, 25u, 227u, 163u, 70u } },
  { { 13u, 101u, 23u, 12u, 117u, 129u, 134u, 117u, 118u, 201u, 72u, 77u } },
  { { 88u, 66u, 228u, 167u, 147u, 57u, 59u, 53u, 184u, 178u, 237u, 83u } },
  { { 77u, 167u, 229u, 93u, 61u, 197u, 93u, 59u, 139u, 158u, 146u, 90u } },
  { { 255u, 93u, 166u, 240u, 161u, 32u, 192u, 84u, 165u, 140u, 55u, 97u } },
  { { 209u, 253u, 139u, 90u, 139u, 216u, 37u, 93u, 137u, 249u, 219u, 103u } },
  { { 170u, 149u, 248u, 243u, 39u, 191u, 162u, 200u, 93u, 221u, 128u, 110u } },
  { { 76u, 201u, 155u, 151u, 32u, 138u, 2u, 82u, 96u, 196u, 37u, 117u } }
}; // idb
_LDBL12 _pow10neg[] =
{
  { { 205u, 204u, 205u, 204u, 204u, 204u, 204u, 204u, 204u, 204u, 251u, 63u } },
  { { 113u, 61u, 10u, 215u, 163u, 112u, 61u, 10u, 215u, 163u, 248u, 63u } },
  { { 90u, 100u, 59u, 223u, 79u, 141u, 151u, 110u, 18u, 131u, 245u, 63u } },
  { { 195u, 211u, 44u, 101u, 25u, 226u, 88u, 23u, 183u, 209u, 241u, 63u } },
  { { 208u, 15u, 35u, 132u, 71u, 27u, 71u, 172u, 197u, 167u, 238u, 63u } },
  { { 64u, 166u, 182u, 105u, 108u, 175u, 5u, 189u, 55u, 134u, 235u, 63u } },
  { { 51u, 61u, 188u, 66u, 122u, 229u, 213u, 148u, 191u, 214u, 231u, 63u } },
  { { 194u, 253u, 253u, 206u, 97u, 132u, 17u, 119u, 204u, 171u, 228u, 63u } },
  { { 47u, 76u, 91u, 225u, 77u, 196u, 190u, 148u, 149u, 230u, 201u, 63u } },
  { { 146u, 196u, 83u, 59u, 117u, 68u, 205u, 20u, 190u, 154u, 175u, 63u } },
  { { 222u, 103u, 186u, 148u, 57u, 69u, 173u, 30u, 177u, 207u, 148u, 63u } },
  { { 36u, 35u, 198u, 226u, 188u, 186u, 59u, 49u, 97u, 139u, 122u, 63u } },
  { { 97u, 85u, 89u, 193u, 126u, 177u, 83u, 124u, 18u, 187u, 95u, 63u } },
  { { 215u, 238u, 47u, 141u, 6u, 190u, 146u, 133u, 21u, 251u, 68u, 63u } },
  { { 36u, 63u, 165u, 233u, 57u, 165u, 39u, 234u, 127u, 168u, 42u, 63u } },
  { { 125u, 172u, 161u, 228u, 188u, 100u, 124u, 70u, 208u, 221u, 85u, 62u } },
  { { 99u, 123u, 6u, 204u, 35u, 84u, 119u, 131u, 255u, 145u, 129u, 61u } },
  { { 145u, 250u, 58u, 25u, 122u, 99u, 37u, 67u, 49u, 192u, 172u, 60u } },
  { { 33u, 137u, 209u, 56u, 130u, 71u, 151u, 184u, 0u, 253u, 215u, 59u } },
  { { 220u, 136u, 88u, 8u, 27u, 177u, 232u, 227u, 134u, 166u, 3u, 59u } },
  { { 198u, 132u, 69u, 66u, 7u, 182u, 153u, 117u, 55u, 219u, 46u, 58u } },
  { { 51u, 113u, 28u, 210u, 35u, 219u, 50u, 238u, 73u, 144u, 90u, 57u } },
  { { 166u, 135u, 190u, 192u, 87u, 218u, 165u, 130u, 166u, 162u, 181u, 50u } },
  { { 226u, 104u, 178u, 17u, 167u, 82u, 159u, 68u, 89u, 183u, 16u, 44u } },
  { { 37u, 73u, 228u, 45u, 54u, 52u, 79u, 83u, 174u, 206u, 107u, 37u } },
  { { 143u, 89u, 4u, 164u, 192u, 222u, 194u, 125u, 251u, 232u, 198u, 30u } },
  { { 158u, 231u, 136u, 90u, 87u, 145u, 60u, 191u, 80u, 131u, 34u, 24u } },
  { { 78u, 75u, 101u, 98u, 253u, 131u, 143u, 175u, 6u, 148u, 125u, 17u } },
  { { 228u, 45u, 222u, 159u, 206u, 210u, 200u, 4u, 221u, 166u, 216u, 10u } }
}; // idb
const InstrumentInfo g_InstrumentInfo[12] =
{
  { 0u, "INSTRUMENT_TOTAL_LOOP" },
  { 1u, "INSTRUMENT_CHECK_DELETE_ITEM" },
  { 2u, "INSTRUMENT_REGEN_HP_MP" },
  { 3u, "INSTRUMENT_DBUPDATE" },
  { 4u, "INSTRUMENT_CELLBROADCASTING" },
  { 5u, "INSTRUMENT_GLOBALSPELLMGR" },
  { 6u, "INSTRUMENT_PROCESS_ALL_MONSTER" },
  { 7u, "INSTRUMENT_UDP_WISH_PROCESS" },
  { 8u, "INSTRUMENT_CHARACTER_LOGOUT" },
  { 9u, "INSTRUMENT_PROCESS_RESPAWN_QUEUE" },
  { 10u, "INSTRUMENT_AUTO_RESPAWN" },
  { 11u, "INSTRUMENT_VIRTUALAREA" }
}; // idb
CServerLog g_GuildLog = { NULL, { , { NULL, 0, 0, NULL, NULL, 0u } }, NULL, 0u, 0u, 0u, "", "", NULL }; // idb
CServerLog g_Log = { NULL, { , { NULL, 0, 0, NULL, NULL, 0u } }, NULL, 0u, 0u, 0u, "", {  } }; // idb
CServerLog g_SessionLog; // idb
CPerformanceCheck performanceCheck; // idb
unsigned int _S1; // idb
CExceptionReport exceptionReport; // idb
unsigned int _S1_0; // idb
bool ATL::CAtlBaseModule::m_bInitFailed; // idb
boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj; // idb
int __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj; // idb
int __S11__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
CCreatureManager creatureManager; // idb
unsigned int _S5; // idb
UINT CServerWindowFramework::ms_SendDataMsg; // idb
char szBuffer[15014]; // idb
char szBuffer_0[176]; // idb
int dword_523694; // weak
int dword_523698; // weak
char byte_52369C; // weak
_UNKNOWN unk_52369D; // weak
boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj; // idb
int __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0DI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
int __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
Item::CNullItem *CSingleton<Item::CNullItem>::ms_pSingleton; // idb
Item::ItemInfo Item::CNullItem::ms_thisiteminfo; // idb
Item::CNullItem Item::CNullItem::ms_this; // idb
Item::CItemFactory Item::CItemFactory::ms_this; // idb
CXRefFriends *CSingleton<CXRefFriends>::ms_pSingleton; // idb
CMonsterMgr *CSingleton<CMonsterMgr>::ms_pSingleton; // idb
AwardTable::CAward *CSingleton<AwardTable::CAward>::ms_pSingleton; // idb
CPartyMgr *CSingleton<CPartyMgr>::ms_pSingleton; // idb
CXORCrypt *CSingleton<CXORCrypt>::ms_pSingleton; // idb
Skill::ProtoType Skill::CProcessTable::ProcessInfo::m_NullProtoType; // idb
Skill::CProcessTable Skill::CProcessTable::ms_this; // idb
CFSM *CSingleton<CFSM>::ms_pSingleton; // idb
int std::fpos<int>::_Stz; // idb
int `std::filebuf::_Init'::`2'::_Stinit; // weak
const std::locale::facet *std::_Facetptr<std::ctype<char>>::_Psave; // idb
const std::locale::facet *std::_Facetptr<std::num_put<char,std::ostreambuf_iterator<char>>>::_Psave; // idb
const std::locale::facet *std::_Facetptr<std::codecvt<char,char,int>>::_Psave; // idb
const std::locale::facet *std::_Facetptr<std::numpunct<char>>::_Psave; // idb
CCellManager cellManager; // idb
unsigned int _S5_0; // idb
std::locale::id std::codecvt<char,char,int>::id; // idb
std::locale::id std::num_put<char,std::ostreambuf_iterator<char>>::id; // idb
std::locale::id std::numpunct<char>::id; // idb
CMonsterShout monsterShout; // idb
unsigned int _S5_1; // idb
CSkillMgr CSkillMgr::ms_this; // idb
CServerSetup serverSetup; // idb
unsigned int _S1_1; // idb
Item::CItemType *CSingleton<Item::CItemType>::ms_pSingleton; // idb
Item::CItemMgr Item::CItemMgr::ms_this; // idb
CGameEventMgr CGameEventMgr::ms_this; // idb
CGameLog gameLog; // idb
unsigned int _S5_2; // idb
CPacketStatistics packetStatistics; // idb
unsigned int _S1_2; // idb
CSingleDispatch singleDispatch; // idb
unsigned int _S5_3; // idb
unsigned __int8 CRegularAgentDispatch::ms_cCurrentGroupNum; // idb
RylServerInfo CRegularAgentDispatch::ms_AgentServerInfo[10]; // idb
CMultiDispatch multiDispatch; // idb
unsigned int _S7_13; // idb
CTempCharacterMgr tempCharacterMgr; // idb
unsigned int _S9_3; // idb
_UNKNOWN unk_5273F8; // weak
unsigned int dword_5277FC; // idb
_UNKNOWN unk_52787C; // weak
unsigned int dword_5278A4; // idb
int dword_527924; // weak
char byte_527928[264]; // weak
CDBSingleObject dbSingleObject; // idb
unsigned int _S1_3; // idb
CSingleDispatch singleDispatch_0; // idb
unsigned int _S5_4; // idb
CGlobalSpellMgr globalSpellMgr; // idb
unsigned int _S5_5; // idb
CRankingMgr *CSingleton<CRankingMgr>::ms_pSingleton; // idb
VirtualArea::CVirtualAreaMgr ms_this; // idb
unsigned int _S5_6; // idb
CSiegeObjectMgr ms_this_0; // idb
unsigned int _S5_7; // idb
int CMonster::ms_NormalBehaviorSendCount; // idb
int CMonster::ms_AttackBehaviorSendCount; // idb
int CMonster::ms_ReturnBehaviorSendCount; // idb
int CMonster::ms_EscapeBehaviorSendCount; // idb
Castle::CCastleMgr ms_this_1; // idb
unsigned int _S5_8; // idb
UINT CConsoleWindow::ms_PrintInfoMsg; // idb
const unsigned int WINCONSOLE_X; // idb
UINT CConsoleWindow::ms_PrintOutputMsg; // idb
CPulse Pulse; // idb
unsigned int _S1_4; // idb
const unsigned int s_MsgID; // idb
unsigned int _S1_5; // idb
CServerRequest serverRequest; // idb
unsigned int _S1_6; // idb
int __S5__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
Item::ItemInfo NullProtoType; // idb
unsigned int c; // idb
Guild::CGuildMgr guildMgr; // idb
unsigned int _S5_9; // idb
CXRefFriends CXRefFriends::ms_this; // idb
CQuestMgr *CSingleton<CQuestMgr>::ms_pSingleton; // idb
CDuelCellManager CDuelCellManager::ms_this; // idb
CMonsterMgr CMonsterMgr::ms_this; // idb
AwardTable::CAward AwardTable::CAward::ms_this; // idb
struct ScriptFunc CNPC::ms_scQuestScript; // idb
CXORCrypt CXORCrypt::ms_this; // idb
CFSM CFSM::ms_this; // idb
Item::CItemType Item::CItemType::ms_this; // idb
CRankingMgr CRankingMgr::ms_this; // idb
unsigned int s_dwLastTime; // idb
VirtualArea::CBGServerMgr ms_this_2; // idb
unsigned int _S5_10; // idb
VirtualArea::CDungeonMgr ms_this_3; // idb
VirtualArea::CDuelMgr ms_this_4; // idb
MonsterInfo::GetMonsterPattern::__l2::TypeAndName monsterTypeName[11]; // idb
int dword_53B0B4; // weak
int dword_53B0B8; // weak
int dword_53B0BC; // weak
int dword_53B0C0; // weak
int dword_53B0C4; // weak
int dword_53B0C8; // weak
int dword_53B0CC; // weak
int dword_53B0D0; // weak
int dword_53B0D4; // weak
int dword_53B0D8; // weak
int dword_53B0DC; // weak
int dword_53B0E0; // weak
int dword_53B0E4; // weak
int dword_53B0E8; // weak
int dword_53B0EC; // weak
int dword_53B0F0; // weak
int dword_53B0F4; // weak
int dword_53B0F8; // weak
int dword_53B0FC; // weak
int dword_53B100; // weak
int dword_53B104; // weak
unsigned int _S5_11; // idb
CQuestMgr CQuestMgr::ms_this; // idb
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *pAllocatedPtrs; // idb
CSyntaxTree *g_pSynTree; // idb
CSymbolTable *g_pSymbolTable; // idb
int dword_53C140; // weak
char byte_53C148[127]; // idb
char byte_53C1C7[]; // weak
char yylval[4096]; // weak
int yydebug; // weak
int yychar; // weak
_UNKNOWN yylloc; // weak
int dword_53D1D4; // weak
int dword_53D1D8; // weak
int dword_53D1DC; // weak
int dword_53D1E0; // weak
int dword_53D1E4; // weak
int yynerrs; // weak
char byte_53D1EC; // weak
int dword_53D1F0; // weak
void *yytext; // idb
unsigned int yyleng; // idb
int dword_53D1FC; // weak
int dword_53D200; // weak
int dword_53D204; // weak
struct yy_buffer_state *dword_53D208; // idb
int dword_53D20C; // weak
int dword_53D210; // weak
_iobuf *yyin; // idb
_iobuf *yyout; // idb
std::list<CThread *> *dword_53D21C; // idb
int dword_53D220; // weak
CSymbolTable *dword_53D224; // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *dword_53D228; // idb
struct _FUNC_ *dword_53D22C; // idb
struct _VAR_ *SWITCHTEMPVAR; // idb
struct _VAR_ *FLOATUNIT; // idb
struct _VAR_ *FLOATTEMP; // idb
int STRINGTRUE; // weak
int STRINGFALSE; // weak
int CONVERSIONBUFFER; // weak
CSymbolTable *dword_53D248; // idb
int dword_53D24C; // weak
CRelocTable *dword_53D250; // idb
std::nothrow_t std::nothrow; // idb
__int64 std::_Fpz; // idb
std::ios_base *stdstr[10]; // idb
char stdopens[10]; // idb
_RTL_CRITICAL_SECTION mtx[4]; // idb
std::_Init_locks initlocks; // idb
std::_Fac_node *Fac_head; // idb
std::locale::_Locimp *global_locale; // idb
std::locale::_Locimp *std::locale::_Locimp::_Clocptr; // idb
int std::locale::id::_Id_cnt; // idb
std::locale classic_locale; // idb
std::locale::id std::ctype<char>::id; // idb
bool registered; // idb
std::filebuf fout; // idb
std::_Init_locks initlocks_0; // idb
std::_Init_cout init_cout; // idb
std::ostream std::cout; // idb
std::bad_alloc nomem; // idb
unsigned int _S1_7; // idb
std::istream *std::_Ptr_cin; // idb
std::ostream *std::_Ptr_cout; // idb
std::ostream *std::_Ptr_cerr; // idb
std::ostream *std::_Ptr_clog; // idb
void (__cdecl *atfuns[10])(); // idb
std::_Init_locks initlocks_1; // idb
_Init_atexit init_atexit; // idb
void (__cdecl *__pPurecall)(); // idb
int _umaskval; // idb
unsigned int _osplatform; // idb
unsigned int _osver; // idb
unsigned int _winver; // idb
unsigned int _winmajor; // idb
unsigned int _winminor; // idb
int __argc; // idb
char **__argv; // idb
char **_environ; // idb
char **__initenv; // idb
unsigned __int16 **_wenviron; // idb
char *_pgmptr; // idb
char _exitflag; // idb
int _C_Termination_Done; // idb
int _C_Exit_Done; // idb
void (__cdecl *user_handler)(int, void *); // idb
char *_aenvptr; // idb
int __error_mode; // idb
int _adjust_fdiv; // idb
int _cflush; // idb
unsigned int _tls_index; // idb
int (__cdecl *_pnhHeap)(unsigned int); // idb
int f_use; // idb
_RTL_CRITICAL_SECTION lclcritsects[14]; // idb
tagLC_ID cacheid; // idb
unsigned __int8 cachecp; // idb
unsigned int (__stdcall *gpFlsAlloc)(void (__stdcall *)(void *)); // idb
void *(__stdcall *gpFlsGetValue)(unsigned int); // idb
int (__stdcall *gpFlsSetValue)(unsigned int, void *); // idb
int (__stdcall *gpFlsFree)(unsigned int); // idb
void *_stdbuf[2]; // idb
int (__stdcall *pfnMessageBoxA)(HWND__ *, const char *, const char *, unsigned int); // idb
HWND__ *(__stdcall *pfnGetActiveWindow)(); // idb
HWND__ *(__stdcall *pfnGetLastActivePopup)(HWND__ *); // idb
HWINSTA__ *(__stdcall *pfnGetProcessWindowStation)(); // idb
int (__stdcall *pfnGetUserObjectInformationA)(void *, int, void *, unsigned int, unsigned int *); // idb
void (__cdecl *_adbgmsg)(); // idb
char pgmname[261]; // idb
int f_use_0; // idb
int nValidPages; // idb
void *rgValidPages[16]; // idb
LONG lModifying; // idb
LPTOP_LEVEL_EXCEPTION_FILTER pOldExceptFilter; // idb
int fSystemSet; // idb
int _newmode; // idb
_TIME_ZONE_INFORMATION tzinfo; // idb
int tzapiused; // idb
char *lastTZ; // idb
int first_time; // idb
tm tb; // idb
tm tb_0; // idb
int _commode; // idb
unsigned int __lc_handle[]; // idb
int lcid; // idb
int dword_53D8A0; // weak
int dword_53D8A4; // weak
int dword_53D8A8; // weak
UINT __lc_codepage; // idb
unsigned int __lc_collate_cp; // idb
int f_use_1; // idb
wchar_t wcbuffer[4]; // idb
tagLC_ID __lc_id[]; // idb
int (__stdcall *_crtInitCritSecAndSpinCount)(_RTL_CRITICAL_SECTION *, unsigned int); // idb
__lc_time_data *__lc_time_intl; // idb
lconv *__lconv_intl; // idb
int iLcidState; // idb
LCID lcidLanguage; // idb
unsigned int lcidCountry; // idb
int bAbbrevLanguage; // idb
int bAbbrevCountry; // idb
char *pchCountry; // idb
unsigned int iPrimaryLen; // idb
char *pchLanguage; // idb
int (__stdcall *pfnGetLocaleInfoA)(unsigned int, unsigned int, char *, int); // idb
void (__cdecl *ctrlc_action)(int); // idb
void (__cdecl *ctrlbreak_action)(int); // idb
void (__cdecl *abort_action)(int); // idb
void (__cdecl *term_action)(int); // idb
int f_use_2; // idb
int f_use_3; // idb
int _fmode; // idb
int f_use_4; // idb
ATL::CAtlBaseModule ATL::_AtlBaseModule; // idb
Item::CItemFactory *CSingleton<Item::CItemFactory>::ms_pSingleton; // idb
CGameEventMgr *CSingleton<CGameEventMgr>::ms_pSingleton; // idb
Item::CItemMgr *CSingleton<Item::CItemMgr>::ms_pSingleton; // idb
Skill::CProcessTable *CSingleton<Skill::CProcessTable>::ms_pSingleton; // idb
CSkillMgr *CSingleton<CSkillMgr>::ms_pSingleton; // idb
CDuelCellManager *CSingleton<CDuelCellManager>::ms_pSingleton; // idb
CSingleDispatch `CLogDispatch::GetDispatchTable'::`2'::singleDispatch; // idb
int __S1__1__GetDispatchTable_CLogDispatch__SAAAVCSingleDispatch__XZ_4IA; // weak
CFieldGameClientDispatchTable fieldGameClientDispatchTable; // idb
unsigned int _S1_8; // idb
CCMDClearDummyCharacters `CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::`local static guard'; // weak
CCMDDummyCharacters `CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::`local static guard'; // weak
CCMDReloadSetup `CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::`local static guard'; // weak
CCMDFlushLog `CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::`local static guard'; // weak
CCMDPrintLog `CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::`local static guard'; // weak
CCMDShowStatistics `CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::`local static guard'; // weak
CCMDConnect `CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::`local static guard'; // weak
CCMDStartServer `CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::`local static guard'; // weak
CCMDStatLog `CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::`local static guard'; // weak
CCMDStatClear `CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::Instance; // idb
int `CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::`local static guard'; // weak
CRylGameServer rylGameServer; // idb
CRylGameServer *_S3_6; // idb
boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj; // idb
int __S1__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA; // weak
CDummyCharacterList dummyCharacterList; // idb
CDummyCharacterList *_S1_9; // idb
int *__lconv_mon_refcount; // idb
void *__lconv_intl_refcount; // idb
int *__lconv_num_refcount; // idb
tagHeader *__sbh_pHeaderDefer; // idb
int __sbh_cntHeaderList; // idb
tagHeader *__sbh_pHeaderList; // idb
unsigned int __sbh_threshold; // idb
tagHeader *__sbh_pHeaderScan; // idb
int __sbh_sizeHeaderList; // idb
int __sbh_indGroupDefer; // idb
unsigned int __mblcid; // idb
threadmbcinfostruct *__ptmbcinfo; // idb
int __ismbcodepage; // idb
unsigned __int8 _mbctype[]; // idb
char byte_53E401[259]; // weak
UINT __mbcodepage; // idb
unsigned __int16 __mbulinfo[6]; // idb
unsigned __int8 _mbcasemap[256]; // idb
HANDLE _crtheap; // idb
int __active_heap; // idb
UINT _nhandle; // idb
ioinfo *__pioinfo[]; // idb
_UNKNOWN unk_53E644; // weak
int __setlc_active; // idb
int __unguarded_readlc_active; // idb
unsigned __int16 *__ctype1; // idb
int *__ctype1_refcount; // idb
void **__piob; // idb
unsigned int _nstream; // idb
char *_acmdln; // idb
int __env_initialized; // idb
void (__cdecl **__onexitend)(); // idb
void (__cdecl **__onexitbegin)(); // idb
int __mbctype_initialized; // idb
_UNKNOWN locret_542269; // weak



//----- (00401000) --------------------------------------------------------
void __stdcall `vector constructor iterator'(char *__t, unsigned int __s, int __n, void *(__thiscall *__f)(void *))
{
  int v5; // edi

  if ( __n - 1 >= 0 )
  {
    v5 = __n;
    do
    {
      __f(__t);
      __t += __s;
      --v5;
    }
    while ( v5 );
  }
}

//----- (00401030) --------------------------------------------------------
CConsoleCommand *__thiscall CCMDAutoBalance::Clone(
        CCMDAutoBalance *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  _DWORD *v3; // eax
  _DWORD *v4; // ebx
  const char *v5; // eax
  char szBuffer[260]; // [esp+4h] [ebp-108h] BYREF

  _snprintf(szBuffer, 0x100u, "%s", szCommand);
  szBuffer[256] = 0;
  strtok(szBuffer, " \t\r\n");
  v3 = operator new((tagHeader *)8);
  v4 = v3;
  if ( !v3 )
    return 0;
  *v3 = &CCMDAutoBalance::`vftable';
  v5 = strtok(0, " \t\r\n");
  if ( v5 )
  {
    if ( !strcmp("on", v5) )
      *((_BYTE *)v4 + 4) = 2;
    else
      *((_BYTE *)v4 + 4) = strcmp("off", v5) != 0 ? 0 : 3;
    return (CConsoleCommand *)v4;
  }
  else
  {
    *((_BYTE *)v4 + 4) = 1;
    return (CConsoleCommand *)v4;
  }
}
// 4FF3AC: using guessed type void *CCMDAutoBalance::`vftable';

//----- (00401140) --------------------------------------------------------
char __thiscall CCMDAutoBalance::DoProcess(CCMDAutoBalance *this)
{
  char *v1; // ecx
  CCreatureManager *Instance; // eax
  CRylGameServer *v3; // eax
  const char *v5; // [esp-Ch] [ebp-Ch]
  int v6; // [esp-8h] [ebp-8h]

  switch ( this->m_cCmd )
  {
    case 1u:
      Instance = CCreatureManager::GetInstance();
      v1 = "AutoBalance is now On";
      if ( !Instance->m_bAutoBalance )
        v1 = "AutoBalance is now Off";
      break;
    case 2u:
      CCreatureManager::GetInstance()->m_bAutoBalance = 1;
      v1 = "Turn On AutoBalance";
      break;
    case 3u:
      CCreatureManager::GetInstance()->m_bAutoBalance = 0;
      v1 = "Turn Off AutoBalance";
      break;
    default:
      v1 = "Invalid autobalance command";
      break;
  }
  if ( v1 )
  {
    v6 = strlen(v1);
    v5 = v1;
    v3 = CRylGameServer::GetInstance((CRylGameServer *)v1);
    CServerWindowFramework::PrintOutput(v3, v5, v6);
  }
  return 1;
}

//----- (004011B0) --------------------------------------------------------
CConsoleCommand *__thiscall CCMDDropItem::Clone(CCMDDropItem *this, const char *szCommand, unsigned int nCommandLength)
{
  char *v3; // eax
  char *v4; // esi
  const char *v5; // eax
  const char *v6; // eax
  char szBuffer[260]; // [esp+4h] [ebp-108h] BYREF

  _snprintf(szBuffer, 0x100u, "%s", szCommand);
  szBuffer[256] = 0;
  strtok(szBuffer, " \t\r\n");
  v3 = (char *)operator new((tagHeader *)0x18);
  v4 = v3;
  if ( !v3 )
    return 0;
  *(_DWORD *)v3 = &CCMDDropItem::`vftable';
  v5 = strtok(0, " \t\r\n");
  _snprintf(v4 + 4, 0x10u, "%s", v5);
  v4[20] = 0;
  v6 = strtok(0, " \t\r\n");
  if ( v6 )
    LOWORD(v6) = atoi(v6);
  *((_WORD *)v4 + 11) = (_WORD)v6;
  return (CConsoleCommand *)v4;
}
// 4FF444: using guessed type void *CCMDDropItem::`vftable';

//----- (00401280) --------------------------------------------------------
char __thiscall CCMDDropItem::DoProcess(CCMDDropItem *this)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCell *m_lpCell; // ecx
  unsigned __int16 m_usItemProtoTypeID; // dx
  unsigned int m_dwCID; // esi
  char *m_szName; // [esp-4h] [ebp-8h]

  m_szName = this->m_szName;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, m_szName);
  if ( !Character )
    return 1;
  m_lpCell = Character->m_CellPos.m_lpCell;
  if ( !m_lpCell )
    return 1;
  m_usItemProtoTypeID = this->m_usItemProtoTypeID;
  m_dwCID = Character->m_dwCID;
  if ( !m_usItemProtoTypeID
    || !CCell::SetItem(m_lpCell, 0, &Character->m_CurrentPos, 0, m_dwCID, m_dwCID, m_usItemProtoTypeID, 1u, 0) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCMDDropItem::DoProcess",
      pFileName,
      50,
      pFormat,
      m_dwCID,
      this->m_usItemProtoTypeID);
  }
  return 1;
}

//----- (00401300) --------------------------------------------------------
boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance()
{
  if ( (__S1__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
  {
    __S1__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
    InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 12;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
    atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
  }
  return &`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj;
}
// 53E3A0: using guessed type int __S1__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0M_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (00401360) --------------------------------------------------------
void __thiscall boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(
        boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  ptr = this->p.list.ptr;
  sz = this->p.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->p.list.ptr = 0;
    this->p.first = 0;
  }
  DeleteCriticalSection(&this->mtx);
}

//----- (004013A0) --------------------------------------------------------
void __thiscall boost::pool<boost::default_user_allocator_new_delete>::~pool<boost::default_user_allocator_new_delete>(
        boost::pool<boost::default_user_allocator_new_delete> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  ptr = this->list.ptr;
  sz = this->list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->list.ptr = 0;
    this->first = 0;
  }
}

//----- (004013E0) --------------------------------------------------------
char __thiscall boost::pool<boost::default_user_allocator_new_delete>::purge_memory(
        boost::pool<boost::default_user_allocator_new_delete> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v5; // esi
  unsigned int v6; // edi

  ptr = this->list.ptr;
  sz = this->list.sz;
  if ( !ptr )
    return 0;
  do
  {
    v5 = *(char **)&ptr[sz - 8];
    v6 = *(_DWORD *)&ptr[sz - 4];
    operator delete[](ptr);
    ptr = v5;
    sz = v6;
  }
  while ( v5 );
  this->list.ptr = 0;
  this->first = 0;
  return 1;
}

//----- (00401420) --------------------------------------------------------
char __thiscall CCMDNotify::Destroy(CCMDDropItemList *this)
{
  operator delete(this);
  return 1;
}

//----- (00401430) --------------------------------------------------------
CConsoleCommand *__thiscall CCMDDropItemList::Clone(
        CCMDDropItemList *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  char *v3; // eax
  char *v4; // esi
  const char *v5; // eax
  const char *v6; // eax
  _iobuf *v7; // eax
  _iobuf *v8; // edi
  char FileName[20]; // [esp+8h] [ebp-220h] BYREF
  char szBuffer[260]; // [esp+1Ch] [ebp-20Ch] BYREF
  char szFileBuffer[260]; // [esp+120h] [ebp-108h] BYREF

  _snprintf(szBuffer, 0x100u, "%s", szCommand);
  szBuffer[256] = 0;
  strtok(szBuffer, " \t\r\n");
  memset(FileName, 0, 17);
  v3 = (char *)operator new((tagHeader *)0x98);
  v4 = v3;
  if ( !v3 )
    return 0;
  *(_DWORD *)v3 = &CCMDDropItemList::`vftable';
  v5 = strtok(0, " \t\r\n");
  _snprintf(v4 + 6, 0x10u, "%s", v5);
  v4[22] = 0;
  v6 = strtok(0, " \t\r\n");
  _snprintf(FileName, 0x10u, "%s", v6);
  FileName[16] = 0;
  *((_WORD *)v4 + 2) = 0;
  v7 = fopen(FileName, "rt");
  v8 = v7;
  if ( v7 )
  {
    if ( fgets(szFileBuffer, 256, v7) )
    {
      do
      {
        if ( *((_WORD *)v4 + 2) >= 0x40u )
          break;
        *(_WORD *)&v4[2 * (unsigned __int16)(*((_WORD *)v4 + 2))++ + 24] = atoi(szFileBuffer);
      }
      while ( fgets(szFileBuffer, 256, v8) );
    }
    fclose(v8);
  }
  return (CConsoleCommand *)v4;
}
// 4FF514: using guessed type void *CCMDDropItemList::`vftable';

//----- (004015A0) --------------------------------------------------------
char __thiscall CCMDDropItemList::DoProcess(CCMDDropItemList *this)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v4; // esi
  CCell *m_lpCell; // ebp
  unsigned __int16 v6; // bx
  float m_fPointY; // ecx
  float m_fPointZ; // edx
  unsigned __int16 v9; // cx
  char *m_szName; // [esp-4h] [ebp-28h]
  int v12; // [esp+10h] [ebp-14h]
  char *v13; // [esp+14h] [ebp-10h]
  Position Pos; // [esp+18h] [ebp-Ch] BYREF

  m_szName = this->m_szName;
  v13 = this->m_szName;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, m_szName);
  v4 = Character;
  if ( !Character )
    return 1;
  m_lpCell = Character->m_CellPos.m_lpCell;
  if ( !m_lpCell )
    return 1;
  v6 = 0;
  if ( !this->m_nItemNum )
    return 1;
  while ( 1 )
  {
    m_fPointY = v4->m_CurrentPos.m_fPointY;
    m_fPointZ = v4->m_CurrentPos.m_fPointZ;
    Pos.m_fPointX = v4->m_CurrentPos.m_fPointX;
    Pos.m_fPointY = m_fPointY;
    Pos.m_fPointZ = m_fPointZ;
    Pos.m_fPointX = (double)((int)rand() % 10) + Pos.m_fPointX;
    v12 = (int)rand() % 10;
    v9 = this->m_usItemProtoTypeID[v6];
    Pos.m_fPointZ = (double)v12 + Pos.m_fPointZ;
    if ( !v9 || !CCell::SetItem(m_lpCell, 0, &Pos, 0, v4->m_dwCID, v4->m_dwCID, v9, 1u, 0) )
      break;
    if ( ++v6 >= this->m_nItemNum )
      return 1;
  }
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "CCMDDropItemList::DoProcess",
    aDWorkRylSource_36,
    77,
    aS_10,
    v13,
    this->m_usItemProtoTypeID[v6]);
  return 0;
}

//----- (004016B0) --------------------------------------------------------
void __thiscall std::logic_error::logic_error(std::logic_error *this, const std::string *_Message)
{
  exception::exception(this);
  this->__vftable = (std::logic_error_vtbl *)&std::logic_error::`vftable';
  this->_Str._Mysize = 0;
  this->_Str._Myres = 15;
  this->_Str._Bx._Buf[0] = 0;
  std::string::assign(&this->_Str, _Message, 0, 0xFFFFFFFF);
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';

//----- (00401710) --------------------------------------------------------
std::string::_Bxty *__thiscall std::logic_error::what(std::logic_error *this)
{
  if ( this->_Str._Myres < 0x10 )
    return &this->_Str._Bx;
  else
    return (std::string::_Bxty *)this->_Str._Bx._Ptr;
}

//----- (00401720) --------------------------------------------------------
void __thiscall std::length_error::~length_error(std::length_error *this)
{
  this->__vftable = (std::length_error_vtbl *)&std::logic_error::`vftable';
  if ( this->_Str._Myres >= 0x10 )
    operator delete(this->_Str._Bx._Ptr);
  this->_Str._Myres = 15;
  this->_Str._Mysize = 0;
  this->_Str._Bx._Buf[0] = 0;
  exception::~exception(this);
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';

//----- (00401760) --------------------------------------------------------
std::length_error *__thiscall std::out_of_range::`vector deleting destructor'(std::length_error *this, char a2)
{
  std::length_error::~length_error(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00401780) --------------------------------------------------------
void __thiscall SKILL::SKILL(SKILL *this)
{
  this->wSkillNum = 0;
  this->wSlotNum = 0;
  this->SSlot[0].dwSkillSlot = 0;
  this->SSlot[1].dwSkillSlot = 0;
  this->SSlot[2].dwSkillSlot = 0;
  this->SSlot[3].dwSkillSlot = 0;
  this->SSlot[4].dwSkillSlot = 0;
  this->SSlot[5].dwSkillSlot = 0;
  this->SSlot[6].dwSkillSlot = 0;
  this->SSlot[7].dwSkillSlot = 0;
  this->SSlot[8].dwSkillSlot = 0;
  this->SSlot[9].dwSkillSlot = 0;
  this->SSlot[10].dwSkillSlot = 0;
  this->SSlot[11].dwSkillSlot = 0;
  this->SSlot[12].dwSkillSlot = 0;
  this->SSlot[13].dwSkillSlot = 0;
  this->SSlot[14].dwSkillSlot = 0;
  this->SSlot[15].dwSkillSlot = 0;
  this->SSlot[16].dwSkillSlot = 0;
  this->SSlot[17].dwSkillSlot = 0;
  this->SSlot[18].dwSkillSlot = 0;
  this->SSlot[19].dwSkillSlot = 0;
  this->SSlot[0].dwSkillSlot = 0;
  this->SSlot[1].dwSkillSlot = 0;
  this->SSlot[2].dwSkillSlot = 0;
  this->SSlot[3].dwSkillSlot = 0;
  this->SSlot[4].dwSkillSlot = 0;
  this->SSlot[5].dwSkillSlot = 0;
  this->SSlot[6].dwSkillSlot = 0;
  this->SSlot[7].dwSkillSlot = 0;
  this->SSlot[8].dwSkillSlot = 0;
  this->SSlot[9].dwSkillSlot = 0;
  this->SSlot[10].dwSkillSlot = 0;
  this->SSlot[11].dwSkillSlot = 0;
  this->SSlot[12].dwSkillSlot = 0;
  this->SSlot[13].dwSkillSlot = 0;
  this->SSlot[14].dwSkillSlot = 0;
  this->SSlot[15].dwSkillSlot = 0;
  this->SSlot[16].dwSkillSlot = 0;
  this->SSlot[17].dwSkillSlot = 0;
  this->SSlot[18].dwSkillSlot = 0;
  this->SSlot[19].dwSkillSlot = 0;
}

//----- (00401810) --------------------------------------------------------
void __thiscall QUICK::QUICK(QUICK *this)
{
  char *p_nSkillLevel; // ecx
  int v3; // esi

  p_nSkillLevel = &this->Slots[0].nSkillLevel;
  v3 = 20;
  do
  {
    *(p_nSkillLevel - 2) = 0;
    *(p_nSkillLevel - 1) = 0;
    *p_nSkillLevel = 0;
    *(_WORD *)(p_nSkillLevel + 1) = 0;
    p_nSkillLevel += 5;
    --v3;
  }
  while ( v3 );
  *(_DWORD *)&this->Slots[0].nType = 0;
  HIBYTE(this->Slots[0].wID) = 0;
  qmemcpy(&this->Slots[1], this, 0x5Fu);
}

//----- (00401870) --------------------------------------------------------
CParty *__thiscall CSymbolTable::GetStringBufferSize(CAggresiveCreature *this)
{
  return this->m_pParty;
}

//----- (00401880) --------------------------------------------------------
char __thiscall CCharacter::GetConsumeMPCount(CCharacter *this)
{
  return this->m_cConsumeMPCount;
}

//----- (00401890) --------------------------------------------------------
unsigned __int16 __thiscall CCharacter::GetClass(CCharacter *this)
{
  return this->m_DBData.m_Info.Class;
}

//----- (004018A0) --------------------------------------------------------
unsigned int __thiscall CCharacter::GetGID(CCharacter *this)
{
  return this->m_DBData.m_Info.Guild;
}

//----- (004018B0) --------------------------------------------------------
unsigned int __thiscall CCharacter::GetFame(CCharacter *this)
{
  return this->m_DBData.m_Info.Fame;
}

//----- (004018C0) --------------------------------------------------------
CCharacter *__thiscall CCharacter::GetDuelOpponent(CCharacter *this)
{
  return this->m_FightInfo.m_pDuelOpponent;
}

//----- (004018D0) --------------------------------------------------------
void __thiscall CModifyDummyCharacter::~CModifyDummyCharacter(CModifyDummyCharacter *this)
{
  this->__vftable = (CModifyDummyCharacter_vtbl *)&CModifyDummyCharacter::`vftable';
  CCharacter::~CCharacter(this);
}
// 4FF608: using guessed type void *CModifyDummyCharacter::`vftable';

//----- (004018E0) --------------------------------------------------------
CModifyDummyCharacter *__thiscall CModifyDummyCharacter::`vector deleting destructor'(
        CModifyDummyCharacter *this,
        char a2)
{
  CModifyDummyCharacter::~CModifyDummyCharacter(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00401900) --------------------------------------------------------
void __usercall CModifyDummyCharacter::ModifyEquipmentData(
        CModifyDummyCharacter *this@<eax>,
        CDummyCharacterList::EquipmentData *equipmentData@<ebx>)
{
  Item::CEquipmentsContainer *p_m_Equipments; // edi
  int i; // esi
  unsigned __int16 v4; // ax
  Item::CItem *v5; // eax

  p_m_Equipments = &this->m_Equipments;
  Item::CItemContainer::ClearItems(&this->m_Equipments);
  for ( i = 0; i < 16; ++i )
  {
    v4 = equipmentData->m_usEquipments[i];
    if ( v4 )
    {
      Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v4);
      if ( v5 )
        Item::CListContainer::SetItem(p_m_Equipments, (16 * i) | 1, v5);
    }
  }
}
// 40192D: variable 'v5' is possibly undefined

//----- (00401960) --------------------------------------------------------
CDummyCharacterList *__fastcall CDummyCharacterList::GetInstance(CDummyCharacterList *a1)
{
  LOBYTE(a1) = (_BYTE)_S1_9;
  if ( ((unsigned __int8)_S1_9 & 1) == 0 )
  {
    _S1_9 = (CDummyCharacterList *)((unsigned int)_S1_9 | 1);
    CDummyCharacterList::CDummyCharacterList(a1, &dummyCharacterList);
    atexit((void (__cdecl *)())_E2);
  }
  return &dummyCharacterList;
}

//----- (004019C0) --------------------------------------------------------
void __thiscall CDummyCharacterList::CDummyCharacterList(CDummyCharacterList *this, CDummyCharacterList *thisa)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *v2; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *v3; // eax

  thisa->m_DummyCharacterList._Myhead = (std::_List_nod<CModifyDummyCharacter *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)this);
  thisa->m_DummyCharacterList._Mysize = 0;
  v3 = std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(v2);
  thisa->m_EquipmentClassSet._Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)v3;
  v3->_Isnil = 1;
  thisa->m_EquipmentClassSet._Myhead->_Parent = thisa->m_EquipmentClassSet._Myhead;
  thisa->m_EquipmentClassSet._Myhead->_Left = thisa->m_EquipmentClassSet._Myhead;
  thisa->m_EquipmentClassSet._Myhead->_Right = thisa->m_EquipmentClassSet._Myhead;
  thisa->m_EquipmentClassSet._Mysize = 0;
}
// 4019F1: variable 'v2' is possibly undefined

//----- (00401A30) --------------------------------------------------------
void __usercall std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>>::~map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>>(
        std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>> *this@<ecx>,
        int a2@<eax>)
{
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase(
    **(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > ***)(a2 + 4),
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)a2,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)(*(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator **)(a2 + 4))->_Ptr,
    *(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *)(a2 + 4));
  operator delete(*(void **)(a2 + 4));
  *(_DWORD *)(a2 + 4) = 0;
  *(_DWORD *)(a2 + 8) = 0;
}

//----- (00401A60) --------------------------------------------------------
void __thiscall CDummyCharacterList::~CDummyCharacterList(CDummyCharacterList *this, CDummyCharacterList *thisa)
{
  std::list<CModifyDummyCharacter *> *v2; // ecx
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator result; // [esp+8h] [ebp-10h] BYREF
  int v4; // [esp+14h] [ebp-4h]

  v4 = 0;
  CDummyCharacterList::Destroy(this, thisa);
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase(
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)thisa->m_EquipmentClassSet._Myhead->_Left,
    &thisa->m_EquipmentClassSet,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)thisa->m_EquipmentClassSet._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)thisa->m_EquipmentClassSet._Myhead);
  operator delete(thisa->m_EquipmentClassSet._Myhead);
  thisa->m_EquipmentClassSet._Myhead = 0;
  thisa->m_EquipmentClassSet._Mysize = 0;
  std::list<CModifyDummyCharacter *>::_Tidy(v2, (int)thisa);
}
// 401ABD: variable 'v2' is possibly undefined

//----- (00401AE0) --------------------------------------------------------
char __thiscall CDummyCharacterList::Initialize(CDummyCharacterList *this, CDummyCharacterList *thisa)
{
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v2; // eax
  std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>> *p_m_EquipmentClassSet; // ebp
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v4; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v5; // eax
  std::vector<CDummyCharacterList::EquipmentData> *Ptr; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v8; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v9; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v10; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v11; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v12; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v13; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v14; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v15; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v16; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v17; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v18; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v19; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v20; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v21; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v22; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v23; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v24; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v25; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v26; // esi
  std::vector<CDummyCharacterList::EquipmentData> *v27; // ecx
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v28; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v29; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v30; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v31; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v32; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v33; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v34; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v35; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v36; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v37; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v38; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v39; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v40; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v41; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v42; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v43; // esi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v44; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v45; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v46; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v47; // edi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v48; // ecx
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v49; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v50; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v51; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v52; // edi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v53; // ecx
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v54; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v55; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v56; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v57; // edi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v58; // ecx
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v59; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v60; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v61; // eax
  std::vector<CDummyCharacterList::EquipmentData> *v62; // edi
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *v63; // ecx
  char DummyChars; // bl
  std::vector<CDummyCharacterList::EquipmentData> *v66; // ecx
  CClass::JobType v67; // [esp-10h] [ebp-74h] BYREF
  std::vector<CDummyCharacterList::EquipmentData> v68; // [esp-Ch] [ebp-70h]
  _DWORD v69[2]; // [esp+10h] [ebp-54h] BYREF
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> insertResult; // [esp+18h] [ebp-4Ch]
  std::vector<CDummyCharacterList::EquipmentData> emptyVector; // [esp+20h] [ebp-44h] BYREF
  std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > result; // [esp+30h] [ebp-34h] BYREF
  _BYTE v73[8]; // [esp+44h] [ebp-20h] BYREF
  void *p; // [esp+4Ch] [ebp-18h]
  int v75; // [esp+60h] [ebp-4h]

  memset(&emptyVector._Myfirst, 0, 12);
  v75 = 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v2 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
         (int)v73,
         (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)5,
         v67,
         v68);
  LOBYTE(v75) = 1;
  result.first = v2->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v2->second,
    &result.second);
  p_m_EquipmentClassSet = &thisa->m_EquipmentClassSet;
  LOBYTE(v75) = 2;
  v5 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
         v4,
         &thisa->m_EquipmentClassSet,
         (int)v69,
         &result,
         (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  Ptr = (std::vector<CDummyCharacterList::EquipmentData> *)v5->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v5->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(szFileName, Ptr + 1) )
    return 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v7 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
         (int)v73,
         (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)6,
         v67,
         v68);
  LOBYTE(v75) = 3;
  result.first = v7->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v7->second,
    &result.second);
  LOBYTE(v75) = 4;
  v9 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
         v8,
         p_m_EquipmentClassSet,
         (int)v69,
         &result,
         (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v10 = (std::vector<CDummyCharacterList::EquipmentData> *)v9->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v9->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_1, v10 + 1) )
    return 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v11 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)7,
          v67,
          v68);
  LOBYTE(v75) = 5;
  result.first = v11->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v11->second,
    &result.second);
  LOBYTE(v75) = 6;
  v13 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v12,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v14 = (std::vector<CDummyCharacterList::EquipmentData> *)v13->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v13->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies, v14 + 1) )
    return 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v15 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)8,
          v67,
          v68);
  LOBYTE(v75) = 7;
  result.first = v15->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v15->second,
    &result.second);
  LOBYTE(v75) = 8;
  v17 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v16,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v18 = (std::vector<CDummyCharacterList::EquipmentData> *)v17->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v17->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_12, v18 + 1) )
    return 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v19 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)9,
          v67,
          v68);
  LOBYTE(v75) = 9;
  result.first = v19->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v19->second,
    &result.second);
  LOBYTE(v75) = 10;
  v21 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v20,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v22 = (std::vector<CDummyCharacterList::EquipmentData> *)v21->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v21->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_2, v22 + 1) )
    return 0;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v23 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0xA,
          v67,
          v68);
  LOBYTE(v75) = 11;
  result.first = v23->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v23->second,
    &result.second);
  LOBYTE(v75) = 12;
  v25 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v24,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v26 = (std::vector<CDummyCharacterList::EquipmentData> *)v25->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v25->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_3, v26 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v28 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0xB,
          v67,
          v68);
  LOBYTE(v75) = 13;
  result.first = v28->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v28->second,
    &result.second);
  LOBYTE(v75) = 14;
  v30 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v29,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v31 = (std::vector<CDummyCharacterList::EquipmentData> *)v30->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v30->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_11, v31 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v32 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0xC,
          v67,
          v68);
  LOBYTE(v75) = 15;
  result.first = v32->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v32->second,
    &result.second);
  LOBYTE(v75) = 16;
  v34 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v33,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v35 = (std::vector<CDummyCharacterList::EquipmentData> *)v34->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v34->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_5, v35 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v36 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x13,
          v67,
          v68);
  LOBYTE(v75) = 17;
  result.first = v36->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v36->second,
    &result.second);
  LOBYTE(v75) = 18;
  v38 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v37,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v39 = (std::vector<CDummyCharacterList::EquipmentData> *)v38->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v38->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_0, v39 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v40 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x14,
          v67,
          v68);
  LOBYTE(v75) = 19;
  result.first = v40->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v40->second,
    &result.second);
  LOBYTE(v75) = 20;
  v42 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v41,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v43 = (std::vector<CDummyCharacterList::EquipmentData> *)v42->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v42->second;
  if ( result.second._Myfirst )
    operator delete(result.second._Myfirst);
  LOBYTE(v75) = 0;
  if ( p )
    operator delete(p);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_6, v43 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v44 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x15,
          v67,
          v68);
  LOBYTE(v75) = 21;
  result.first = v44->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v44->second,
    &result.second);
  LOBYTE(v75) = 22;
  v46 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v45,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v47 = (std::vector<CDummyCharacterList::EquipmentData> *)v46->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v46->second;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    *(std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > **)&insertResult.second,
    (int)&result);
  LOBYTE(v75) = 0;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    v48,
    (int)v73);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_7, v47 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v49 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x16,
          v67,
          v68);
  LOBYTE(v75) = 23;
  result.first = v49->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v49->second,
    &result.second);
  LOBYTE(v75) = 24;
  v51 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v50,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v52 = (std::vector<CDummyCharacterList::EquipmentData> *)v51->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v51->second;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    *(std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > **)&insertResult.second,
    (int)&result);
  LOBYTE(v75) = 0;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    v53,
    (int)v73);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_4, v52 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v54 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x17,
          v67,
          v68);
  LOBYTE(v75) = 25;
  result.first = v54->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v54->second,
    &result.second);
  LOBYTE(v75) = 26;
  v56 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v55,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v57 = (std::vector<CDummyCharacterList::EquipmentData> *)v56->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v56->second;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    *(std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > **)&insertResult.second,
    (int)&result);
  LOBYTE(v75) = 0;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    v58,
    (int)v73);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_8, v57 + 1) )
    goto LABEL_69;
  v69[0] = &v67;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &emptyVector,
    (const std::vector<CDummyCharacterList::EquipmentData> *)&v67);
  v59 = std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
          (int)v73,
          (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)0x18,
          v67,
          v68);
  LOBYTE(v75) = 27;
  result.first = v59->first;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    &v59->second,
    &result.second);
  LOBYTE(v75) = 28;
  v61 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert(
          v60,
          p_m_EquipmentClassSet,
          (int)v69,
          &result,
          (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)v68._Myend);
  v62 = (std::vector<CDummyCharacterList::EquipmentData> *)v61->first._Ptr;
  *(_DWORD *)&insertResult.second = *(_DWORD *)&v61->second;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    *(std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > **)&insertResult.second,
    (int)&result);
  LOBYTE(v75) = 0;
  std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
    v63,
    (int)v73);
  if ( insertResult.second && !CDummyCharacterList::LoadEquipments(aDummies_9, v62 + 1) )
  {
LABEL_69:
    std::vector<CDummyCharacterList::EquipmentData>::~vector<CDummyCharacterList::EquipmentData>(v27, (int)&emptyVector);
    return 0;
  }
  DummyChars = CDummyCharacterList::LoadDummyChars(aDummies_10, p_m_EquipmentClassSet, &thisa->m_DummyCharacterList);
  std::vector<CDummyCharacterList::EquipmentData>::~vector<CDummyCharacterList::EquipmentData>(v66, (int)&emptyVector);
  return DummyChars;
}
// 401B5E: variable 'v4' is possibly undefined
// 401BFA: variable 'v8' is possibly undefined
// 401C96: variable 'v12' is possibly undefined
// 401D32: variable 'v16' is possibly undefined
// 401DCE: variable 'v20' is possibly undefined
// 401E6A: variable 'v24' is possibly undefined
// 401F06: variable 'v29' is possibly undefined
// 401FA2: variable 'v33' is possibly undefined
// 40203E: variable 'v37' is possibly undefined
// 4020DA: variable 'v41' is possibly undefined
// 402176: variable 'v45' is possibly undefined
// 402195: variable 'v48' is possibly undefined
// 402202: variable 'v50' is possibly undefined
// 402221: variable 'v53' is possibly undefined
// 40228E: variable 'v55' is possibly undefined
// 4022AD: variable 'v58' is possibly undefined
// 40231A: variable 'v60' is possibly undefined
// 402339: variable 'v63' is possibly undefined
// 40235D: variable 'v27' is possibly undefined
// 402392: variable 'v66' is possibly undefined

//----- (004023B0) --------------------------------------------------------
void __usercall std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(
        std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *this@<ecx>,
        int a2@<esi>)
{
  if ( *(_DWORD *)(a2 + 8) )
    operator delete(*(void **)(a2 + 8));
  *(_DWORD *)(a2 + 8) = 0;
  *(_DWORD *)(a2 + 12) = 0;
  *(_DWORD *)(a2 + 16) = 0;
}

//----- (004023E0) --------------------------------------------------------
char __thiscall CDummyCharacterList::Destroy(CDummyCharacterList *this, CDummyCharacterList *thisa)
{
  std::_List_nod<CModifyDummyCharacter *>::_Node *Myhead; // ebx
  std::_List_nod<CModifyDummyCharacter *>::_Node *i; // edi
  CModifyDummyCharacter *Myval; // esi
  CCell *m_lpCell; // ecx
  std::_List_nod<CModifyDummyCharacter *>::_Node *v6; // ecx
  std::_List_nod<CModifyDummyCharacter *>::_Node *Next; // eax
  std::_List_nod<CModifyDummyCharacter *>::_Node *v8; // ecx
  std::_List_nod<CModifyDummyCharacter *>::_Node *v9; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *j; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v13; // eax

  Myhead = thisa->m_DummyCharacterList._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    Myval = i->_Myval;
    m_lpCell = Myval->m_CellPos.m_lpCell;
    if ( m_lpCell )
      CCell::DeleteCreature(
        m_lpCell,
        Myval->m_dwCID,
        (std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator)1);
    Myval->m_bLogout = 1;
    ((void (__thiscall *)(CModifyDummyCharacter *, int))Myval->~CAggresiveCreature)(Myval, 1);
  }
  v6 = thisa->m_DummyCharacterList._Myhead;
  Next = v6->_Next;
  v6->_Next = v6;
  thisa->m_DummyCharacterList._Myhead->_Prev = thisa->m_DummyCharacterList._Myhead;
  v8 = thisa->m_DummyCharacterList._Myhead;
  thisa->m_DummyCharacterList._Mysize = 0;
  if ( Next != v8 )
  {
    do
    {
      v9 = Next->_Next;
      operator delete(Next);
      Next = v9;
    }
    while ( v9 != thisa->m_DummyCharacterList._Myhead );
  }
  Parent = thisa->m_EquipmentClassSet._Myhead->_Parent;
  for ( j = Parent; !j->_Isnil; Parent = j )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Erase(
      &thisa->m_EquipmentClassSet,
      j->_Right);
    j = j->_Left;
    std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Node::~_Node(
      v12,
      (int)Parent);
    operator delete(Parent);
  }
  thisa->m_EquipmentClassSet._Myhead->_Parent = thisa->m_EquipmentClassSet._Myhead;
  v13 = thisa->m_EquipmentClassSet._Myhead;
  thisa->m_EquipmentClassSet._Mysize = 0;
  v13->_Left = v13;
  thisa->m_EquipmentClassSet._Myhead->_Right = thisa->m_EquipmentClassSet._Myhead;
  return 1;
}
// 40246D: variable 'v12' is possibly undefined

//----- (004024B0) --------------------------------------------------------
char __cdecl CDummyCharacterList::LoadEquipments(
        const char *szFileName,
        std::vector<CDummyCharacterList::EquipmentData> *equipmentVector)
{
  _iobuf *v2; // eax
  _iobuf *v3; // ebx
  const char *v4; // eax
  const char *v5; // eax
  const char *v6; // eax
  const char *v7; // eax
  const char *v8; // eax
  const char *v9; // eax
  CDummyCharacterList::EquipmentData *Myfirst; // edx
  unsigned int v11; // ecx
  CDummyCharacterList::EquipmentData *Mylast; // esi
  _iobuf *lpFile; // [esp+8h] [ebp-448h]
  CDummyCharacterList::EquipmentData equipmentData; // [esp+Ch] [ebp-444h] BYREF
  CDummyCharacterList::EquipmentData oldEquipmentData; // [esp+2Ch] [ebp-424h] BYREF
  char szBuffer[1024]; // [esp+4Ch] [ebp-404h] BYREF

  v2 = fopen(szFileName, "rt");
  v3 = v2;
  lpFile = v2;
  if ( !v2 )
    return 0;
  if ( fgets(szBuffer, 1024, v2) )
  {
    do
    {
      memset(&equipmentData, 0, sizeof(equipmentData));
      v4 = strtok(szBuffer, " \t\r\n");
      if ( v4 )
        equipmentData.m_usEquipments[11] = atoi(v4);
      v5 = strtok(0, " \t\r\n");
      if ( v5 )
        equipmentData.m_usEquipments[9] = atoi(v5);
      v6 = strtok(0, " \t\r\n");
      if ( v6 )
        equipmentData.m_usEquipments[0] = atoi(v6);
      v7 = strtok(0, " \t\r\n");
      if ( v7 )
        equipmentData.m_usEquipments[3] = atoi(v7);
      v8 = strtok(0, " \t\r\n");
      if ( v8 )
        equipmentData.m_usEquipments[4] = atoi(v8);
      v9 = strtok(0, " \t\r\n");
      if ( v9 )
        equipmentData.m_usEquipments[5] = atoi(v9);
      if ( memcmp((const char *)&oldEquipmentData, (const char *)&equipmentData, 32) )
      {
        Myfirst = equipmentVector->_Myfirst;
        if ( Myfirst )
          v11 = equipmentVector->_Mylast - Myfirst;
        else
          v11 = 0;
        if ( Myfirst && v11 < equipmentVector->_Myend - Myfirst )
        {
          Mylast = equipmentVector->_Mylast;
          std::_Uninit_fill_n<CDummyCharacterList::EquipmentData *,unsigned int,CDummyCharacterList::EquipmentData,std::allocator<CDummyCharacterList::EquipmentData>>(
            Mylast,
            1u,
            &equipmentData);
          v3 = lpFile;
          equipmentVector->_Mylast = Mylast + 1;
        }
        else
        {
          std::vector<CDummyCharacterList::EquipmentData>::_Insert_n(
            v11,
            &equipmentData,
            equipmentVector,
            (std::vector<CDummyCharacterList::EquipmentData>::iterator)equipmentVector->_Mylast,
            1u);
        }
      }
      qmemcpy(&oldEquipmentData, &equipmentData, sizeof(oldEquipmentData));
    }
    while ( fgets(szBuffer, 1024, v3) );
  }
  fclose(v3);
  return 1;
}

//----- (004026E0) --------------------------------------------------------
char __cdecl CDummyCharacterList::LoadDummyChars(
        const char *szFileName,
        std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>> *equipmentSet,
        std::list<CModifyDummyCharacter *> *dummyCharList)
{
  void *v3; // esp
  _iobuf *v4; // edi
  const char *v5; // eax
  const char *v6; // eax
  const char *v7; // eax
  const char *v8; // eax
  const char *v9; // eax
  const char *v10; // eax
  const char *v11; // eax
  const char *v12; // eax
  const char *v13; // edi
  const char *v14; // eax
  const char *v15; // edi
  const char *v16; // eax
  const char *v17; // edi
  const char *v18; // eax
  const char *v19; // eax
  const char *v20; // eax
  CCharacter *v21; // eax
  CModifyDummyCharacter *v22; // ebx
  CCreatureManager *Instance; // eax
  __int16 v24; // ax
  unsigned int Mysize; // edx
  std::_List_nod<CThread *>::_Node *Myhead; // ecx
  int v27; // ecx
  unsigned __int16 v28; // ax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v29; // ecx
  unsigned int v30; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v32; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **v33; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v34; // edi
  CDummyCharacterList::EquipmentData *Myfirst; // eax
  unsigned int v36; // esi
  unsigned int v37; // eax
  std::_List_nod<CThread *>::_Node *v38; // esi
  std::_List_nod<CThread *>::_Node *v39; // edi
  CModifyDummyCharacter *v41; // [esp+0h] [ebp-2164h]
  std::list<CThread *> _Val; // [esp+4h] [ebp-2160h] BYREF
  int v43; // [esp+10h] [ebp-2154h]
  _iobuf *str; // [esp+14h] [ebp-2150h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v45; // [esp+18h] [ebp-214Ch] BYREF
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v46; // [esp+1Ch] [ebp-2148h] BYREF
  Position NewPosition; // [esp+20h] [ebp-2144h] BYREF
  _BYTE dwCID[280]; // [esp+2Ch] [ebp-2138h] BYREF
  unsigned __int8 v49; // [esp+144h] [ebp-2020h]
  char string[8196]; // [esp+14Ch] [ebp-2018h] BYREF
  int v51; // [esp+2160h] [ebp-4h]

  v3 = alloca(8532);
  v4 = fopen(szFileName, "rt");
  str = v4;
  if ( !v4 )
    return 0;
  SKILL::SKILL((SKILL *)&dwCID[96]);
  QUICK::QUICK((QUICK *)&dwCID[180]);
  if ( fgets(string, 0x1FFF, v4) )
  {
    do
    {
      memset(dwCID, 0, sizeof(dwCID));
      v49 = 0;
      v43 = 0;
      _Val._Myhead = 0;
      _Val._Mysize = 0;
      string[0x1FFF] = 0;
      v5 = strtok(string, " \t\r\n");
      if ( v5 )
        *(_DWORD *)dwCID = atol(v5);
      v6 = strtok(0, " \t\r\n");
      if ( v6 )
        strncpy(&dwCID[4], v6, 0x10u);
      v7 = strtok(0, " \t\r\n");
      if ( v7 )
        dwCID[42] = atoi(v7);
      v8 = strtok(0, " \t\r\n");
      if ( v8 )
      {
        *(_WORD *)&dwCID[24] = atoi(v8);
        dwCID[23] = CClass::GetRace(dwCID[24]);
      }
      v9 = strtok(0, " \t\r\n");
      if ( v9 )
        dwCID[21] = atoi(v9);
      v10 = strtok(0, " \t\r\n");
      if ( v10 )
        dwCID[22] = atoi(v10);
      v11 = strtok(0, " \t\r\n");
      if ( v11 )
        dwCID[20] = atoi(v11);
      v12 = strtok(0, " \t\r\n");
      v13 = v12;
      if ( v12 )
      {
        *(float *)&dwCID[72] = (float)atoi(v12);
        *(float *)&dwCID[84] = (float)atoi(v13);
      }
      v14 = strtok(0, " \t\r\n");
      v15 = v14;
      if ( v14 )
      {
        *(float *)&dwCID[76] = (float)atoi(v14);
        *(float *)&dwCID[88] = (float)atoi(v15);
      }
      v16 = strtok(0, " \t\r\n");
      v17 = v16;
      if ( v16 )
      {
        *(float *)&dwCID[80] = (float)atoi(v16);
        *(float *)&dwCID[92] = (float)atoi(v17);
      }
      v18 = strtok(0, " \t\r\n");
      if ( v18 )
        _Val._Myhead = (std::_List_nod<CThread *>::_Node *)atoi(v18);
      v19 = strtok(0, " \t\r\n");
      if ( v19 )
        _Val._Mysize = atoi(v19);
      v20 = strtok(0, " \t\r\n");
      if ( v20 )
        v43 = atoi(v20);
      v21 = (CCharacter *)operator new((tagHeader *)0x610);
      v22 = (CModifyDummyCharacter *)v21;
      *(_DWORD *)&_Val._Alnod.std::_Allocator_base<std::_List_nod<CThread *>::_Node> = v21;
      v51 = 0;
      if ( v21 )
      {
        CCharacter::CCharacter(v21, *(unsigned int *)dwCID);
        v22->__vftable = (CModifyDummyCharacter_vtbl *)&CModifyDummyCharacter::`vftable';
        v22->m_DummyData.m_dwDressChangeFreq = 0;
        v22->m_DummyData.m_dwAttackFreq = 0;
        v22->m_DummyData.m_dwSkillFreq = 0;
        v41 = v22;
      }
      else
      {
        v41 = 0;
        v22 = 0;
      }
      v51 = -1;
      *(_DWORD *)&_Val._Alnod.std::_Allocator_base<std::_List_nod<CThread *>::_Node> = v22;
      if ( v22 )
      {
        CCharacter::Initialize(v22, 0);
        Instance = CCreatureManager::GetInstance();
        CCreatureManager::CancelLogout(Instance, v22);
        *(_DWORD *)&dwCID[26] = rand();
        strcpy(&dwCID[59], "d");
        strcpy(&dwCID[61], "d");
        *(_WORD *)&dwCID[49] = rand();
        *(_WORD *)&dwCID[51] = rand();
        *(_WORD *)&dwCID[53] = rand();
        *(_WORD *)&dwCID[55] = rand();
        v24 = rand();
        Mysize = _Val._Mysize;
        *(_WORD *)&dwCID[57] = v24;
        v22->m_bLogout = 0;
        qmemcpy(&v22->m_DBData, dwCID, 0x118u);
        Myhead = _Val._Myhead;
        v22->m_DBData.m_cAdminLevel = v49;
        v22->m_DummyData.m_dwDressChangeFreq = (unsigned int)Myhead;
        v27 = v43;
        v22->m_DummyData.m_dwAttackFreq = Mysize;
        v22->m_DummyData.m_dwSkillFreq = v27;
        v28 = v22->GetClass(v22);
        v29 = equipmentSet->_Myhead;
        v30 = v28;
        Parent = v29->_Parent;
        while ( !Parent->_Isnil )
        {
          if ( Parent->_Myval.first >= v30 )
          {
            v29 = Parent;
            Parent = Parent->_Left;
          }
          else
          {
            Parent = Parent->_Right;
          }
        }
        v32 = equipmentSet->_Myhead;
        v45 = v29;
        if ( v29 == v32 || v30 < v29->_Myval.first )
        {
          v46 = v32;
          v33 = &v46;
        }
        else
        {
          v33 = &v45;
        }
        v34 = *v33;
        if ( *v33 != v32 )
        {
          Myfirst = v34->_Myval.second._Myfirst;
          if ( Myfirst )
          {
            v36 = v34->_Myval.second._Mylast - Myfirst;
            if ( v36 )
            {
              v37 = rand();
              CModifyDummyCharacter::ModifyEquipmentData(v41, &v34->_Myval.second._Myfirst[v37 % v36]);
              v22 = v41;
            }
          }
        }
        v38 = (std::_List_nod<CThread *>::_Node *)dummyCharList->_Myhead;
        v39 = std::list<IOPCode *>::_Buynode(&_Val, v38, v38->_Prev, (CThread **)&_Val);
        std::list<CModifyDummyCharacter *>::_Incsize((std::list<CModifyDummyCharacter *> *)1, (int)dummyCharList);
        v38->_Prev = v39;
        v39->_Prev->_Next = v39;
        NewPosition = *(Position *)&dwCID[72];
        CCharacter::Login(v22, MIDDLE_ADMIN);
        CAggresiveCreature::MoveTo(v22, &NewPosition, 0);
      }
    }
    while ( fgets(string, 0x1FFF, str) );
    v4 = str;
  }
  fclose(v4);
  return 1;
}
// 4FF608: using guessed type void *CModifyDummyCharacter::`vftable';

//----- (00402BE0) --------------------------------------------------------
void __thiscall std::string::string(std::string *this, const std::string *_Right)
{
  this->_Mysize = 0;
  this->_Myres = 15;
  this->_Bx._Buf[0] = 0;
  std::string::assign(this, _Right, 0, 0xFFFFFFFF);
}

//----- (00402C10) --------------------------------------------------------
void __thiscall std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>(
        std::pair<std::string const ,unsigned char> *this)
{
  if ( this->first._Myres >= 0x10 )
    operator delete(this->first._Bx._Ptr);
  this->first._Myres = 15;
  this->first._Mysize = 0;
  this->first._Bx._Buf[0] = 0;
}

//----- (00402C50) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ecx>,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *a2@<eax>,
        int a3@<edi>,
        const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *result,
        const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val)
{
  const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *v5; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *first; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *v10; // eax
  const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Addleft; // [esp+Ch] [ebp-4h]

  v5 = result;
  Myhead = a2->_Myhead;
  Parent = Myhead->_Parent;
  LOBYTE(this) = 1;
  LOBYTE(_Addleft) = 1;
  if ( !Parent->_Isnil )
  {
    first = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)result->first;
    do
    {
      LOBYTE(this) = (unsigned int)first < Parent->_Myval.first;
      Myhead = Parent;
      LOBYTE(_Addleft) = (_BYTE)this;
      if ( (unsigned int)first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v10 = (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *)Myhead;
  result = (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)Myhead;
  if ( (_BYTE)this )
  {
    if ( Myhead == a2->_Myhead->_Left )
    {
      *(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *)a3 = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Insert(a2, Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **)&result, (const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *)1, v5)->_Ptr;
      *(_BYTE *)(a3 + 4) = 1;
      return (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *)a3;
    }
    std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Dec(
      (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *)this,
      (int)&result);
    v10 = (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *)result;
  }
  if ( *(_DWORD *)&v10[1].second >= v5->first )
  {
    *(_DWORD *)a3 = v10;
    *(_BYTE *)(a3 + 4) = 0;
  }
  else
  {
    *(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *)a3 = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Insert(a2, Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **)&result, _Addleft, v5)->_Ptr;
    *(_BYTE *)(a3 + 4) = 1;
  }
  return (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *)a3;
}
// 402CE0: variable '_Addleft' is possibly undefined

//----- (00402D10) --------------------------------------------------------
void __thiscall std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
        std::vector<CDummyCharacterList::EquipmentData> *this,
        const std::vector<CDummyCharacterList::EquipmentData> *_Right)
{
  CDummyCharacterList::EquipmentData *Myfirst; // ecx
  unsigned int v4; // eax
  int v5; // edi
  CDummyCharacterList::EquipmentData *v6; // eax
  _DWORD v7[7]; // [esp+0h] [ebp-1Ch] BYREF

  Myfirst = this->_Myfirst;
  v7[3] = v7;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  _Right->_Myfirst = 0;
  _Right->_Mylast = 0;
  _Right->_Myend = 0;
  if ( v4 )
  {
    if ( v4 > 0x7FFFFFF )
      std::vector<CDummyCharacterList::EquipmentData>::_Xlen((std::vector<CDummyCharacterList::EquipmentData> *)Myfirst);
    v5 = v4;
    v6 = (CDummyCharacterList::EquipmentData *)operator new((tagHeader *)(32 * v4));
    v7[6] = 0;
    _Right->_Myfirst = v6;
    _Right->_Mylast = v6;
    _Right->_Myend = &v6[v5];
    _Right->_Mylast = std::_Uninit_copy<std::vector<CDummyCharacterList::EquipmentData>::const_iterator,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>(
                        v6,
                        (std::vector<CDummyCharacterList::EquipmentData>::const_iterator)this->_Myfirst,
                        (std::vector<CDummyCharacterList::EquipmentData>::const_iterator)this->_Mylast);
  }
}

//----- (00402DC0) --------------------------------------------------------
void __usercall std::vector<CDummyCharacterList::EquipmentData>::~vector<CDummyCharacterList::EquipmentData>(
        std::vector<CDummyCharacterList::EquipmentData> *this@<ecx>,
        int a2@<esi>)
{
  if ( *(_DWORD *)(a2 + 4) )
    operator delete(*(void **)(a2 + 4));
  *(_DWORD *)(a2 + 4) = 0;
  *(_DWORD *)(a2 + 8) = 0;
  *(_DWORD *)(a2 + 12) = 0;
}

//----- (00402DF0) --------------------------------------------------------
void __thiscall std::out_of_range::out_of_range(std::out_of_range *this, const std::out_of_range *__that)
{
  std::logic_error::logic_error(this, __that);
  this->__vftable = (std::out_of_range_vtbl *)&std::out_of_range::`vftable';
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00402E10) --------------------------------------------------------
void __thiscall std::logic_error::logic_error(std::logic_error *this, const std::logic_error *__that)
{
  exception::exception(this, __that);
  this->__vftable = (std::logic_error_vtbl *)&std::logic_error::`vftable';
  this->_Str._Myres = 15;
  this->_Str._Mysize = 0;
  this->_Str._Bx._Buf[0] = 0;
  std::string::assign(&this->_Str, &__that->_Str, 0, 0xFFFFFFFF);
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';

//----- (00402E80) --------------------------------------------------------
void __thiscall std::string::string(std::string *this, const char *_Ptr)
{
  this->_Myres = 15;
  this->_Mysize = 0;
  this->_Bx._Buf[0] = 0;
  std::string::assign(this, _Ptr, strlen(_Ptr));
}

//----- (00402EC0) --------------------------------------------------------
std::string *__thiscall std::string::assign(
        std::string *this,
        const std::string *_Right,
        unsigned int _Roff,
        unsigned int _Count)
{
  unsigned int v5; // ebp
  unsigned int Myres; // eax
  bool v8; // zf
  std::string::_Bxty *p_Bx; // edx
  std::string::_Bxty *v10; // eax
  std::string::_Bxty *Ptr; // edi
  bool v12; // cf

  if ( _Right->_Mysize < _Roff )
    std::_String_base::_Xran((std::_String_base *)this);
  v5 = _Right->_Mysize - _Roff;
  if ( _Count < v5 )
    v5 = _Count;
  if ( this == _Right )
  {
    std::string::erase(this, _Roff + v5, 0xFFFFFFFF);
    std::string::erase(this, 0, _Roff);
    return this;
  }
  if ( v5 == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  Myres = this->_Myres;
  if ( Myres >= v5 )
  {
    v8 = v5 == 0;
    if ( !v5 )
    {
      this->_Mysize = 0;
      if ( Myres < 0x10 )
        this->_Bx._Buf[0] = 0;
      else
        *this->_Bx._Ptr = 0;
      return this;
    }
  }
  else
  {
    std::string::_Copy(this, v5, this->_Mysize);
    v8 = v5 == 0;
  }
  if ( !v8 )
  {
    if ( _Right->_Myres < 0x10 )
      p_Bx = &_Right->_Bx;
    else
      p_Bx = (std::string::_Bxty *)_Right->_Bx._Ptr;
    v10 = &this->_Bx;
    if ( this->_Myres < 0x10 )
      Ptr = &this->_Bx;
    else
      Ptr = (std::string::_Bxty *)v10->_Ptr;
    qmemcpy(Ptr, &p_Bx->_Buf[_Roff], v5);
    v12 = this->_Myres < 0x10;
    this->_Mysize = v5;
    if ( !v12 )
      v10 = (std::string::_Bxty *)v10->_Ptr;
    v10->_Buf[v5] = 0;
  }
  return this;
}

//----- (00402FB0) --------------------------------------------------------
void __thiscall std::string::_Tidy(std::string *this, bool _Built, unsigned int _Newsize)
{
  char *Ptr; // eax

  if ( _Built && this->_Myres >= 0x10 )
  {
    Ptr = this->_Bx._Ptr;
    if ( _Newsize )
      qmemcpy(&this->_Bx, Ptr, _Newsize);
    operator delete(Ptr);
  }
  this->_Mysize = _Newsize;
  this->_Myres = 15;
  this->_Bx._Buf[_Newsize] = 0;
}

//----- (00403010) --------------------------------------------------------
void __usercall std::list<CModifyDummyCharacter *>::_Tidy(std::list<CModifyDummyCharacter *> *this@<ecx>, int a2@<esi>)
{
  _DWORD **v2; // ecx
  _DWORD *v3; // eax
  bool v4; // zf
  _DWORD *v5; // edi

  v2 = *(_DWORD ***)(a2 + 4);
  v3 = *v2;
  *v2 = v2;
  *(_DWORD *)(*(_DWORD *)(a2 + 4) + 4) = *(_DWORD *)(a2 + 4);
  v4 = v3 == *(_DWORD **)(a2 + 4);
  *(_DWORD *)(a2 + 8) = 0;
  if ( !v4 )
  {
    do
    {
      v5 = (_DWORD *)*v3;
      operator delete(v3);
      v3 = v5;
    }
    while ( v5 != *(_DWORD **)(a2 + 4) );
  }
  operator delete(*(void **)(a2 + 4));
  *(_DWORD *)(a2 + 4) = 0;
}

//----- (00403060) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v2; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *i; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v5; // ecx

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Node::~_Node(
      v5,
      (int)v2);
    operator delete(v2);
  }
}
// 40307F: variable 'v5' is possibly undefined

//----- (004030A0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Insert@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ebx>,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Wherenode@<ecx>,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **_Addleft,
        const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val,
        const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Vala)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v6; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v8; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v9; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **p_Parent; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **v12; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Left; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v15; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v16; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v17; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v18; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v19; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v21; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v22; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v23; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v24; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v25; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v26; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v27; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v28; // edi
  char v30; // [esp+0h] [ebp-58h]
  std::string _Right; // [esp+8h] [ebp-50h] BYREF
  exception pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  std::string v33; // [esp+30h] [ebp-28h] BYREF
  int v34; // [esp+54h] [ebp-4h]

  if ( this->_Mysize >= 0xCCCCCCB )
  {
    _Right._Myres = 15;
    _Right._Mysize = 0;
    _Right._Bx._Buf[0] = 0;
    std::string::assign(&_Right, "map/set<T> too long", 0x13u);
    v34 = 0;
    exception::exception(&pExceptionObject);
    LOBYTE(v34) = 1;
    pExceptionObject.__vftable = (exception_vtbl *)&std::logic_error::`vftable';
    v33._Myres = 15;
    v33._Mysize = 0;
    v33._Bx._Buf[0] = 0;
    std::string::assign(&v33, &_Right, 0, 0xFFFFFFFF);
    LOBYTE(v34) = 0;
    pExceptionObject.__vftable = (exception_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Buynode(
         _Vala,
         (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)this->_Myhead,
         _Wherenode,
         this->_Myhead,
         0,
         v30);
  ++this->_Mysize;
  Myhead = this->_Myhead;
  v8 = v6;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    v9 = this->_Myhead;
LABEL_9:
    v9->_Right = v6;
    goto LABEL_10;
  }
  if ( !(_BYTE)_Val )
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode != v9->_Right )
      goto LABEL_10;
    goto LABEL_9;
  }
  _Wherenode->_Left = v6;
  v10 = this->_Myhead;
  if ( _Wherenode == v10->_Left )
    v10->_Left = v6;
LABEL_10:
  p_Parent = &v6->_Parent;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **)*p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v8 == v12[2] )
          {
            v8 = *p_Parent;
            v15 = v12[2];
            (*p_Parent)->_Right = v15->_Left;
            if ( !v15->_Left->_Isnil )
              v15->_Left->_Parent = v8;
            p_Parent = &v8->_Parent;
            v15->_Parent = v8->_Parent;
            v16 = this->_Myhead;
            if ( v8 == v16->_Parent )
            {
              v16->_Parent = v15;
            }
            else
            {
              v17 = *p_Parent;
              if ( v8 == (*p_Parent)->_Left )
                v17->_Left = v15;
              else
                v17->_Right = v15;
            }
            v15->_Left = v8;
            *p_Parent = v15;
          }
          (*p_Parent)->_Color = 1;
          (*p_Parent)->_Parent->_Color = 0;
          v18 = (*p_Parent)->_Parent;
          v19 = v18->_Left;
          v18->_Left = v18->_Left->_Right;
          Right = v19->_Right;
          if ( !Right->_Isnil )
            Right->_Parent = v18;
          v19->_Parent = v18->_Parent;
          v21 = this->_Myhead;
          if ( v18 == v21->_Parent )
          {
            v21->_Parent = v19;
            v19->_Right = v18;
          }
          else
          {
            v22 = v18->_Parent;
            if ( v18 == v22->_Right )
              v22->_Right = v19;
            else
              v22->_Left = v19;
            v19->_Right = v18;
          }
LABEL_49:
          v18->_Parent = v19;
          goto LABEL_50;
        }
      }
      else if ( Left->_Color )
      {
        if ( v8 == *v12 )
        {
          v8 = *p_Parent;
          v23 = *v12;
          (*p_Parent)->_Left = v23->_Right;
          v24 = v23->_Right;
          if ( !v24->_Isnil )
            v24->_Parent = v8;
          p_Parent = &v8->_Parent;
          v23->_Parent = v8->_Parent;
          v25 = this->_Myhead;
          if ( v8 == v25->_Parent )
          {
            v25->_Parent = v23;
          }
          else
          {
            v26 = *p_Parent;
            if ( v8 == (*p_Parent)->_Right )
              v26->_Right = v23;
            else
              v26->_Left = v23;
          }
          v23->_Right = v8;
          *p_Parent = v23;
        }
        (*p_Parent)->_Color = 1;
        (*p_Parent)->_Parent->_Color = 0;
        v18 = (*p_Parent)->_Parent;
        v19 = v18->_Right;
        v18->_Right = v19->_Left;
        if ( !v19->_Left->_Isnil )
          v19->_Left->_Parent = v18;
        v19->_Parent = v18->_Parent;
        v27 = this->_Myhead;
        if ( v18 == v27->_Parent )
        {
          v27->_Parent = v19;
        }
        else
        {
          v28 = v18->_Parent;
          if ( v18 == v28->_Left )
            v28->_Left = v19;
          else
            v28->_Right = v19;
        }
        v19->_Left = v18;
        goto LABEL_49;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v8 = (*p_Parent)->_Parent;
LABEL_50:
      p_Parent = &v8->_Parent;
      if ( v8->_Parent->_Color )
      {
        v8 = v6;
        break;
      }
    }
  }
  this->_Myhead->_Parent->_Color = 1;
  *_Addleft = v8;
  return (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *)_Addleft;
}
// 40314A: variable 'v30' is possibly undefined
// 4FC8A4: using guessed type void *std::logic_error::`vftable';
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00403350) --------------------------------------------------------
void __thiscall std::length_error::length_error(std::length_error *this, const std::length_error *__that)
{
  std::logic_error::logic_error(this, __that);
  this->__vftable = (std::length_error_vtbl *)&std::length_error::`vftable';
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00403370) --------------------------------------------------------
std::string *__thiscall std::string::assign(std::string *this, const char *_Ptr)
{
  return std::string::assign(this, _Ptr, strlen(_Ptr));
}

//----- (004033A0) --------------------------------------------------------
std::string *__thiscall std::string::erase(std::string *this, unsigned int _Off, unsigned int _Count)
{
  unsigned int v4; // edi
  unsigned int v5; // eax
  unsigned int Myres; // ecx
  std::string::_Bxty *p_Bx; // ebx
  std::string::_Bxty *Ptr; // edx
  std::string::_Bxty *v9; // ecx
  unsigned int v10; // ecx
  unsigned int v11; // eax

  if ( this->_Mysize < _Off )
    std::_String_base::_Xran((std::_String_base *)this);
  v4 = _Count;
  v5 = this->_Mysize - _Off;
  if ( v5 < _Count )
    v4 = this->_Mysize - _Off;
  if ( v4 )
  {
    Myres = this->_Myres;
    p_Bx = &this->_Bx;
    if ( Myres < 0x10 )
      Ptr = &this->_Bx;
    else
      Ptr = (std::string::_Bxty *)p_Bx->_Ptr;
    if ( Myres < 0x10 )
      v9 = &this->_Bx;
    else
      v9 = (std::string::_Bxty *)p_Bx->_Ptr;
    memmove((unsigned __int8 *)&v9->_Buf[_Off], (unsigned __int8 *)&Ptr->_Buf[_Off + v4], v5 - v4);
    v10 = this->_Myres;
    v11 = this->_Mysize - v4;
    this->_Mysize = v11;
    if ( v10 >= 0x10 )
      p_Bx = (std::string::_Bxty *)p_Bx->_Ptr;
    p_Bx->_Buf[v11] = 0;
  }
  return this;
}

//----- (00403420) --------------------------------------------------------
BOOL __thiscall std::string::_Grow(std::string *this, unsigned int _Newsize, bool _Trim)
{
  unsigned int v3; // esi
  unsigned int Myres; // eax
  unsigned int Mysize; // ebx
  char *Ptr; // eax

  v3 = _Newsize;
  if ( _Newsize == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  Myres = this->_Myres;
  if ( Myres < _Newsize )
  {
    std::string::_Copy(this, _Newsize, this->_Mysize);
    return _Newsize != 0;
  }
  if ( _Trim && _Newsize < 0x10 )
  {
    Mysize = this->_Mysize;
    if ( _Newsize < Mysize )
      Mysize = _Newsize;
    if ( Myres >= 0x10 )
    {
      Ptr = this->_Bx._Ptr;
      if ( Mysize )
      {
        qmemcpy(&this->_Bx, Ptr, Mysize);
        v3 = _Newsize;
      }
      operator delete(Ptr);
    }
    this->_Mysize = Mysize;
    this->_Myres = 15;
    this->_Bx._Buf[Mysize] = 0;
    return v3 != 0;
  }
  else
  {
    if ( !_Newsize )
    {
      this->_Mysize = 0;
      if ( Myres >= 0x10 )
      {
        *this->_Bx._Ptr = 0;
        return 0;
      }
      this->_Bx._Buf[0] = 0;
    }
    return _Newsize != 0;
  }
}

//----- (004034F0) --------------------------------------------------------
void __fastcall std::list<CModifyDummyCharacter *>::_Incsize(std::list<CModifyDummyCharacter *> *this, int a2)
{
  int v2; // eax
  std::string _Right; // [esp+4h] [ebp-50h] BYREF
  exception pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  std::string v5; // [esp+2Ch] [ebp-28h] BYREF
  int v6; // [esp+50h] [ebp-4h]

  v2 = *(_DWORD *)(a2 + 8);
  if ( 0x3FFFFFFF - v2 < (unsigned int)this )
  {
    _Right._Myres = 15;
    _Right._Mysize = 0;
    _Right._Bx._Buf[0] = 0;
    std::string::assign(&_Right, "list<T> too long", 0x10u);
    v6 = 0;
    exception::exception(&pExceptionObject);
    LOBYTE(v6) = 1;
    pExceptionObject.__vftable = (exception_vtbl *)&std::logic_error::`vftable';
    v5._Myres = 15;
    v5._Mysize = 0;
    v5._Bx._Buf[0] = 0;
    std::string::assign(&v5, &_Right, 0, 0xFFFFFFFF);
    LOBYTE(v6) = 0;
    pExceptionObject.__vftable = (exception_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  *(_DWORD *)(a2 + 8) = (char *)this + v2;
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004035B0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ecx>,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *a2@<edi>,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Ptr; // ebx
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *v7; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v8; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v11; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *i; // eax

  Myhead = a2->_Myhead;
  Ptr = _Last._Ptr;
  v7 = result;
  v8 = _First._Ptr;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Erase(
      a2,
      Myhead->_Parent);
    a2->_Myhead->_Parent = a2->_Myhead;
    v9 = a2->_Myhead;
    a2->_Mysize = 0;
    v9->_Left = v9;
    a2->_Myhead->_Right = a2->_Myhead;
    v7->_Ptr = a2->_Myhead->_Left;
    return v7;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v11 = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)v8;
        if ( !v8->_Isnil )
        {
          Right = v8->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v8->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v8 != i->_Right )
                break;
              v8 = i;
            }
            v8 = i;
          }
          else
          {
            v8 = v8->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v8 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase(
          v11,
          a2,
          (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)&result,
          (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator)v11);
      }
      while ( v8 != Ptr );
    }
    v7->_Ptr = v8;
    return v7;
  }
}

//----- (00403670) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Mysize; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v4; // edx

  Mysize = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this->_Mysize;
  this->_Mysize = (unsigned int)Mysize->_Left;
  if ( !Mysize->_Left->_Isnil )
    Mysize->_Left->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
  Mysize->_Parent = this->_Myhead;
  Myhead = _Wherenode->_Myhead;
  if ( this == (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Myhead->_Parent )
  {
    Myhead->_Parent = Mysize;
    Mysize->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
    this->_Myhead = Mysize;
  }
  else
  {
    v4 = this->_Myhead;
    if ( this == (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)v4->_Left )
      v4->_Left = Mysize;
    else
      v4->_Right = Mysize;
    Mysize->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
    this->_Myhead = Mysize;
  }
}

//----- (004036D0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v2; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v5; // edx

  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >;
  this->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > = *(std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)(*(_DWORD *)&this->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > + 8);
  Right = v2->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
  v2->_Parent = this->_Myhead;
  Myhead = _Wherenode->_Myhead;
  if ( this == (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Myhead->_Parent )
  {
    Myhead->_Parent = v2;
    v2->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
    this->_Myhead = v2;
  }
  else
  {
    v5 = this->_Myhead;
    if ( this == (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)v5->_Right )
      v5->_Right = v2;
    else
      v5->_Left = v2;
    v2->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
    this->_Myhead = v2;
  }
}

//----- (00403730) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Buynode@<eax>(
        const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val@<ecx>,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Rarg,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v7; // esi
  int v9; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v10; // [esp+Ch] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Wherenode; // [esp+10h] [ebp-14h]
  int *v12; // [esp+14h] [ebp-10h]
  int v13; // [esp+20h] [ebp-4h]

  v12 = &v9;
  v7 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)operator new((tagHeader *)0x24);
  v13 = 1;
  _Wherenode = v7;
  v10 = (CPacketDispatch *)v7;
  if ( v7 )
  {
    v7->_Right = _Parg;
    v7->_Parent = _Larg;
    v7->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)this;
    v7->_Myval.first = _Val->first;
    std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
      &_Val->second,
      &v7->_Myval.second);
    v7->_Color = (char)_Rarg;
    v7->_Isnil = 0;
  }
  return v7;
}

//----- (004037D0) --------------------------------------------------------
void __fastcall std::vector<CDummyCharacterList::EquipmentData>::_Insert_n(
        int a1,
        const CDummyCharacterList::EquipmentData *_Val,
        std::vector<CDummyCharacterList::EquipmentData> *this,
        std::vector<CDummyCharacterList::EquipmentData>::iterator _Where,
        unsigned int _Count)
{
  CDummyCharacterList::EquipmentData *Myfirst; // ecx
  unsigned int v7; // eax
  int v8; // edx
  int v9; // edx
  unsigned int v10; // eax
  int v11; // edx
  int v12; // eax
  CDummyCharacterList::EquipmentData *v13; // eax
  CDummyCharacterList::EquipmentData *v14; // edx
  CDummyCharacterList::EquipmentData *v15; // ecx
  int v16; // eax
  int v17; // esi
  CDummyCharacterList::EquipmentData *v18; // eax
  CDummyCharacterList::EquipmentData *v19; // esi
  CDummyCharacterList::EquipmentData *Mylast; // ebx
  CDummyCharacterList::EquipmentData *v21; // edx
  CDummyCharacterList::EquipmentData *v22; // edx
  int v23; // [esp+0h] [ebp-48h] BYREF
  CDummyCharacterList::EquipmentData *_Ptr; // [esp+Ch] [ebp-3Ch]
  CDummyCharacterList::EquipmentData *_Newvec; // [esp+10h] [ebp-38h]
  CDummyCharacterList::EquipmentData _Tmp; // [esp+14h] [ebp-34h] BYREF
  int *v27; // [esp+38h] [ebp-10h]
  int v28; // [esp+44h] [ebp-4h]
  std::vector<CDummyCharacterList::EquipmentData> *thisa; // [esp+50h] [ebp+8h]

  qmemcpy(&_Tmp, _Val, sizeof(_Tmp));
  Myfirst = this->_Myfirst;
  v27 = &v23;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x7FFFFFF - v8 < _Count )
      std::vector<CDummyCharacterList::EquipmentData>::_Xlen((std::vector<CDummyCharacterList::EquipmentData> *)Myfirst);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v7 >= _Count + v9 )
    {
      Mylast = this->_Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        this->_Mylast = std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>(
                          &Mylast[-_Count],
                          Mylast,
                          Mylast);
        std::copy_backward<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *>(
          _Where._Myptr,
          &Mylast[-_Count],
          Mylast);
        v22 = &_Where._Myptr[_Count];
      }
      else
      {
        std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v28 = 2;
        std::_Uninit_fill_n<CDummyCharacterList::EquipmentData *,unsigned int,CDummyCharacterList::EquipmentData,std::allocator<CDummyCharacterList::EquipmentData>>(
          this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          &_Tmp);
        v21 = &this->_Mylast[_Count];
        this->_Mylast = v21;
        v22 = &v21[-_Count];
      }
      std::fill<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData>(_Where._Myptr, v22, &_Tmp);
    }
    else
    {
      if ( 0x7FFFFFF - (v7 >> 1) >= v7 )
        v10 = (v7 >> 1) + v7;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      thisa = (std::vector<CDummyCharacterList::EquipmentData> *)(32 * v10);
      v13 = (CDummyCharacterList::EquipmentData *)operator new((tagHeader *)(32 * v10));
      v28 = 0;
      v14 = this->_Myfirst;
      _Newvec = v13;
      _Ptr = std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>(
               v14,
               _Where._Myptr,
               v13);
      std::_Uninit_fill_n<CDummyCharacterList::EquipmentData *,unsigned int,CDummyCharacterList::EquipmentData,std::allocator<CDummyCharacterList::EquipmentData>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      v18 = _Newvec;
      v19 = &_Newvec[v17];
      this->_Myend = (CDummyCharacterList::EquipmentData *)(&thisa->_Alval.std::_Allocator_base<CDummyCharacterList::EquipmentData>
                                                          + (_DWORD)_Newvec);
      this->_Mylast = v19;
      this->_Myfirst = v18;
    }
  }
}

//----- (00403A00) --------------------------------------------------------
void __thiscall __noreturn std::vector<CDummyCharacterList::EquipmentData>::_Xlen(
        std::vector<CDummyCharacterList::EquipmentData> *this)
{
  std::string _Right; // [esp+4h] [ebp-50h] BYREF
  exception pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  std::string v3; // [esp+2Ch] [ebp-28h] BYREF
  const std::vector<CDummyCharacterList::EquipmentData> *thisa; // [esp+50h] [ebp-4h]

  _Right._Myres = 15;
  _Right._Mysize = 0;
  _Right._Bx._Buf[0] = 0;
  std::string::assign(&_Right, "vector<T> too long", 0x12u);
  thisa = 0;
  exception::exception(&pExceptionObject);
  LOBYTE(thisa) = 1;
  pExceptionObject.__vftable = (exception_vtbl *)&std::logic_error::`vftable';
  v3._Myres = 15;
  v3._Mysize = 0;
  v3._Bx._Buf[0] = 0;
  std::string::assign(&v3, &_Right, 0, 0xFFFFFFFF);
  LOBYTE(thisa) = 0;
  pExceptionObject.__vftable = (exception_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00403AA0) --------------------------------------------------------
std::string *__thiscall std::string::assign(std::string *this, const char *_Ptr, unsigned int _Num)
{
  unsigned int Myres; // edx
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v6; // eax
  std::string::_Bxty *v7; // ecx
  unsigned int v9; // eax
  bool v10; // zf
  std::string::_Bxty *Ptr; // edi
  bool v12; // cf

  Myres = this->_Myres;
  if ( Myres < 0x10 )
    p_Bx = &this->_Bx;
  else
    p_Bx = (std::string::_Bxty *)this->_Bx._Ptr;
  if ( _Ptr >= (const char *)p_Bx )
  {
    v6 = &this->_Bx;
    v7 = Myres < 0x10 ? &this->_Bx : (std::string::_Bxty *)v6->_Ptr;
    if ( &v7->_Buf[this->_Mysize] > _Ptr )
    {
      if ( Myres >= 0x10 )
        v6 = (std::string::_Bxty *)v6->_Ptr;
      return std::string::assign(this, this, _Ptr - (const char *)v6, _Num);
    }
  }
  if ( _Num == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  v9 = this->_Myres;
  if ( v9 >= _Num )
  {
    v10 = _Num == 0;
    if ( !_Num )
    {
      this->_Mysize = 0;
      if ( v9 < 0x10 )
        this->_Bx._Buf[0] = 0;
      else
        *this->_Bx._Ptr = 0;
      return this;
    }
  }
  else
  {
    std::string::_Copy(this, _Num, this->_Mysize);
    v10 = _Num == 0;
  }
  if ( !v10 )
  {
    if ( this->_Myres < 0x10 )
      Ptr = &this->_Bx;
    else
      Ptr = (std::string::_Bxty *)this->_Bx._Ptr;
    qmemcpy(Ptr, _Ptr, _Num);
    v12 = this->_Myres < 0x10;
    this->_Mysize = _Num;
    if ( !v12 )
    {
      this->_Bx._Ptr[_Num] = 0;
      return this;
    }
    this->_Bx._Buf[_Num] = 0;
  }
  return this;
}

//----- (00403B90) --------------------------------------------------------
void __thiscall std::string::_Copy(std::string *this, unsigned int _Newsize, unsigned int _Oldlen)
{
  unsigned int v3; // ebx
  std::string *v4; // esi
  unsigned int Myres; // edi
  unsigned int v6; // ecx
  char *v7; // edx
  unsigned int v8; // ecx
  std::string::_Bxty *p_Bx; // esi
  std::string::_Bxty *v10; // eax
  int v11; // [esp+0h] [ebp-24h] BYREF
  std::string *v12; // [esp+10h] [ebp-14h]
  int *v13; // [esp+14h] [ebp-10h]
  int v14; // [esp+20h] [ebp-4h]
  char *_Ptr; // [esp+2Ch] [ebp+8h]

  v3 = _Newsize | 0xF;
  v4 = this;
  v13 = &v11;
  v12 = this;
  if ( (_Newsize | 0xF) == 0xFFFFFFFF )
  {
    v3 = _Newsize;
  }
  else
  {
    Myres = this->_Myres;
    v6 = Myres >> 1;
    if ( v3 / 3 < Myres >> 1 && Myres <= -2 - v6 )
      v3 = v6 + Myres;
  }
  v14 = 0;
  v7 = (char *)operator new((tagHeader *)(v3 + 1));
  _Ptr = v7;
  v8 = _Oldlen;
  if ( _Oldlen )
  {
    if ( v4->_Myres < 0x10 )
      p_Bx = &v4->_Bx;
    else
      p_Bx = (std::string::_Bxty *)v4->_Bx._Ptr;
    qmemcpy(v7, p_Bx, _Oldlen);
    v4 = v12;
    v8 = _Oldlen;
  }
  if ( v4->_Myres >= 0x10 )
  {
    operator delete(v4->_Bx._Ptr);
    v7 = _Ptr;
    v8 = _Oldlen;
  }
  v10 = &v4->_Bx;
  v4->_Bx._Buf[0] = 0;
  v4->_Bx._Ptr = v7;
  v4->_Myres = v3;
  v4->_Mysize = v8;
  if ( v3 >= 0x10 )
    v10 = (std::string::_Bxty *)v7;
  v10->_Buf[v8] = 0;
}

//----- (00403CD0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Where,
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Wherea)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Ptr; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Parent; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *v8; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v10; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v12; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v13; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v14; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v15; // eax
  char Color; // al
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *Left; // ecx
  bool v18; // zf
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *Mysize; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *v20; // eax
  std::string _Right; // [esp+8h] [ebp-50h] BYREF
  exception pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  std::string v23; // [esp+30h] [ebp-28h] BYREF
  int v24; // [esp+54h] [ebp-4h]

  LOBYTE(this) = _Wherea._Ptr->_Isnil;
  if ( (_BYTE)this )
  {
    _Right._Myres = 15;
    _Right._Mysize = 0;
    _Right._Bx._Buf[0] = 0;
    std::string::assign(&_Right, "invalid map/set<T> iterator", 0x1Bu);
    v24 = 0;
    exception::exception(&pExceptionObject);
    LOBYTE(v24) = 1;
    pExceptionObject.__vftable = (exception_vtbl *)&std::logic_error::`vftable';
    v23._Myres = 15;
    v23._Mysize = 0;
    v23._Bx._Buf[0] = 0;
    std::string::assign(&v23, &_Right, 0, 0xFFFFFFFF);
    LOBYTE(v24) = 0;
    pExceptionObject.__vftable = (exception_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Wherea._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Inc(
    (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *)this,
    (int *)&_Wherea);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    v8 = result;
    Myhead = result->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v10 = result->_Myhead;
    if ( v10->_Left == Ptr )
    {
      if ( Right->_Isnil )
      {
        v11 = Parent;
      }
      else
      {
        v11 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Min(Right);
        v8 = result;
      }
      v10->_Left = v11;
    }
    v12 = v8->_Myhead;
    if ( v12->_Right == Ptr )
    {
      if ( Right->_Isnil )
      {
        v12->_Right = Parent;
      }
      else
      {
        v13 = std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Max(Right);
        v8 = result;
        v12->_Right = v13;
      }
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Wherea._Ptr;
  Right = _Wherea._Ptr->_Right;
  if ( _Wherea._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Wherea._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v14 = result->_Myhead;
  if ( v14->_Parent == Ptr )
  {
    v14->_Parent = v6;
  }
  else
  {
    v15 = Ptr->_Parent;
    if ( v15->_Left == Ptr )
      v15->_Left = v6;
    else
      v15->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  v8 = result;
  Ptr->_Color = Color;
LABEL_35:
  if ( Ptr->_Color == 1 )
  {
    if ( Right != v8->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Right;
          if ( !LOBYTE(Left[2]._Mysize) )
          {
            LOBYTE(Left[2]._Mysize) = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent,
              v8);
            Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Right;
            v8 = result;
          }
          if ( BYTE1(Left[2]._Mysize) )
            goto LABEL_53;
          if ( *(_BYTE *)(*(_DWORD *)&Left->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >
                        + 32) != 1
            || *(_BYTE *)(Left->_Mysize + 32) != 1 )
          {
            if ( *(_BYTE *)(Left->_Mysize + 32) == 1 )
            {
              *(_BYTE *)(*(_DWORD *)&Left->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >
                       + 32) = 1;
              LOBYTE(Left[2]._Mysize) = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Rrotate(
                Left,
                v8);
              Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Right;
              v8 = result;
            }
            LOBYTE(Left[2]._Mysize) = Parent->_Color;
            Parent->_Color = 1;
            *(_BYTE *)(Left->_Mysize + 32) = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent,
              v8);
            break;
          }
        }
        else
        {
          if ( !LOBYTE(Left[2]._Mysize) )
          {
            LOBYTE(Left[2]._Mysize) = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent,
              v8);
            Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Left;
            v8 = result;
          }
          if ( BYTE1(Left[2]._Mysize) )
            goto LABEL_53;
          if ( *(_BYTE *)(Left->_Mysize + 32) != 1
            || *(_BYTE *)(*(_DWORD *)&Left->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >
                        + 32) != 1 )
          {
            if ( *(_BYTE *)(*(_DWORD *)&Left->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >
                          + 32) == 1 )
            {
              *(_BYTE *)(Left->_Mysize + 32) = 1;
              LOBYTE(Left[2]._Mysize) = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Lrotate(
                Left,
                v8);
              Left = (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent->_Left;
              v8 = result;
            }
            LOBYTE(Left[2]._Mysize) = Parent->_Color;
            Parent->_Color = 1;
            *(_BYTE *)(*(_DWORD *)&Left->std::_Tree_val<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >
                     + 32) = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *)Parent,
              v8);
            break;
          }
        }
        LOBYTE(Left[2]._Mysize) = 0;
LABEL_53:
        Right = Parent;
        v18 = Parent == v8->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v18 );
    }
    Right->_Color = 1;
  }
  if ( Ptr->_Myval.second._Myfirst )
    operator delete(Ptr->_Myval.second._Myfirst);
  Ptr->_Myval.second._Myfirst = 0;
  Ptr->_Myval.second._Mylast = 0;
  Ptr->_Myval.second._Myend = 0;
  operator delete(Ptr);
  Mysize = (std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *)result->_Mysize;
  if ( Mysize )
    result->_Mysize = (unsigned int)(&Mysize[-1]._Isnil + 2);
  v20 = _Where._Ptr;
  _Where._Ptr->_Left = _Wherea._Ptr;
  return v20;
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00403FE0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)operator new((tagHeader *)0x24);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (00404020) --------------------------------------------------------
void __fastcall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Dec(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *this,
        int a2)
{
  _DWORD *v2; // eax
  int v3; // ecx
  int i; // eax
  int v5; // eax

  v2 = *(_DWORD **)a2;
  if ( *(_BYTE *)(*(_DWORD *)a2 + 33) )
  {
    *(_DWORD *)a2 = v2[2];
  }
  else
  {
    v3 = *v2;
    if ( *(_BYTE *)(*v2 + 33) )
    {
      v5 = v2[1];
      if ( !*(_BYTE *)(v5 + 33) )
      {
        do
        {
          if ( *(_DWORD *)a2 != *(_DWORD *)v5 )
            break;
          *(_DWORD *)a2 = v5;
          v5 = *(_DWORD *)(v5 + 4);
        }
        while ( !*(_BYTE *)(v5 + 33) );
        if ( !*(_BYTE *)(v5 + 33) )
          *(_DWORD *)a2 = v5;
      }
    }
    else
    {
      for ( i = *(_DWORD *)(v3 + 8); !*(_BYTE *)(i + 33); i = *(_DWORD *)(i + 8) )
        v3 = i;
      *(_DWORD *)a2 = v3;
    }
  }
}

//----- (00404080) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__usercall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Max@<eax>(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Pnode@<eax>)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *i; // ecx

  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    _Pnode = i;
  return _Pnode;
}

//----- (004040A0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__usercall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Min@<eax>(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Pnode@<eax>)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *i; // ecx

  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    _Pnode = i;
  return _Pnode;
}

//----- (004040C0) --------------------------------------------------------
void __fastcall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *this,
        int *a2)
{
  int v2; // eax
  int **v3; // ecx
  int *j; // eax
  int i; // eax

  v2 = *a2;
  if ( !*(_BYTE *)(*a2 + 33) )
  {
    v3 = *(int ***)(v2 + 8);
    if ( *((_BYTE *)v3 + 33) )
    {
      for ( i = *(_DWORD *)(v2 + 4); !*(_BYTE *)(i + 33); i = *(_DWORD *)(i + 4) )
      {
        if ( *a2 != *(_DWORD *)(i + 8) )
          break;
        *a2 = i;
      }
      *a2 = i;
    }
    else
    {
      for ( j = *v3; !*((_BYTE *)j + 33); j = (int *)*j )
        v3 = (int **)j;
      *a2 = (int)v3;
    }
  }
}

//----- (00404120) --------------------------------------------------------
std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *__usercall std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>@<eax>(
        int a1@<esi>,
        std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *result,
        CClass::JobType _Val1,
        std::vector<CDummyCharacterList::EquipmentData> _Val2)
{
  *(_DWORD *)a1 = result;
  std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(
    (std::vector<CDummyCharacterList::EquipmentData> *)&_Val1,
    (const std::vector<CDummyCharacterList::EquipmentData> *)(a1 + 4));
  if ( *(_DWORD *)&_Val2._Alval.std::_Allocator_base<CDummyCharacterList::EquipmentData> )
    operator delete(*(void **)&_Val2._Alval.std::_Allocator_base<CDummyCharacterList::EquipmentData>);
  return (std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *)a1;
}

//----- (00404180) --------------------------------------------------------
void __usercall std::fill<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData>(
        CDummyCharacterList::EquipmentData *_First@<eax>,
        CDummyCharacterList::EquipmentData *_Last@<edx>,
        const CDummyCharacterList::EquipmentData *_Val@<ebx>)
{
  CDummyCharacterList::EquipmentData *v3; // edi

  while ( _First != _Last )
  {
    v3 = _First++;
    qmemcpy(v3, _Val, sizeof(CDummyCharacterList::EquipmentData));
  }
}

//----- (004041A0) --------------------------------------------------------
CDummyCharacterList::EquipmentData *__usercall std::copy_backward<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *>@<eax>(
        CDummyCharacterList::EquipmentData *_First@<ebx>,
        CDummyCharacterList::EquipmentData *_Last@<edx>,
        CDummyCharacterList::EquipmentData *_Dest@<eax>)
{
  while ( _Last != _First )
    qmemcpy(--_Dest, --_Last, sizeof(CDummyCharacterList::EquipmentData));
  return _Dest;
}

//----- (004041C0) --------------------------------------------------------
void __usercall std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Node::~_Node(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *this@<ecx>,
        int a2@<esi>)
{
  if ( *(_DWORD *)(a2 + 20) )
    operator delete(*(void **)(a2 + 20));
  *(_DWORD *)(a2 + 20) = 0;
  *(_DWORD *)(a2 + 24) = 0;
  *(_DWORD *)(a2 + 28) = 0;
}

//----- (004041F0) --------------------------------------------------------
void __usercall std::_Uninit_fill_n<CDummyCharacterList::EquipmentData *,unsigned int,CDummyCharacterList::EquipmentData,std::allocator<CDummyCharacterList::EquipmentData>>(
        CDummyCharacterList::EquipmentData *_First@<eax>,
        unsigned int _Count@<ecx>,
        const CDummyCharacterList::EquipmentData *_Val@<ebx>)
{
  unsigned int v3; // edx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        qmemcpy(_First, _Val, sizeof(CDummyCharacterList::EquipmentData));
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (00404210) --------------------------------------------------------
CDummyCharacterList::EquipmentData *__usercall std::_Uninit_copy<std::vector<CDummyCharacterList::EquipmentData>::const_iterator,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>@<eax>(
        CDummyCharacterList::EquipmentData *_Dest@<eax>,
        std::vector<CDummyCharacterList::EquipmentData>::const_iterator _First,
        std::vector<CDummyCharacterList::EquipmentData>::const_iterator _Last)
{
  CDummyCharacterList::EquipmentData *i; // edx

  for ( i = _First._Myptr; i != _Last._Myptr; ++_Dest )
  {
    if ( _Dest )
      qmemcpy(_Dest, i, sizeof(CDummyCharacterList::EquipmentData));
    ++i;
  }
  return _Dest;
}

//----- (00404240) --------------------------------------------------------
CDummyCharacterList::EquipmentData *__usercall std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>@<eax>(
        CDummyCharacterList::EquipmentData *_First@<edx>,
        CDummyCharacterList::EquipmentData *_Last@<ebx>,
        CDummyCharacterList::EquipmentData *_Dest@<eax>)
{
  for ( ; _First != _Last; ++_Dest )
  {
    if ( _Dest )
      qmemcpy(_Dest, _First, sizeof(CDummyCharacterList::EquipmentData));
    ++_First;
  }
  return _Dest;
}

//----- (00404270) --------------------------------------------------------
CGameClientDispatchTable *__thiscall CGameClientDispatchTable::`scalar deleting destructor'(
        CGameClientDispatchTable *this,
        char a2)
{
  this->__vftable = (CGameClientDispatchTable_vtbl *)&CGameClientDispatchTable::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4FF8C0: using guessed type void *CGameClientDispatchTable::`vftable';

//----- (00404290) --------------------------------------------------------
CFieldGameClientDispatchTable *__cdecl CFieldGameClientDispatchTable::GetInstance()
{
  CGameClientDispatchTable::Process *m_ProcessTable; // eax
  int v1; // ecx

  if ( (_S1_8 & 1) == 0 )
  {
    _S1_8 |= 1u;
    m_ProcessTable = fieldGameClientDispatchTable.m_ProcessTable;
    v1 = 255;
    do
    {
      m_ProcessTable->m_dwCommand = 0;
      m_ProcessTable->m_fnProcess = 0;
      ++m_ProcessTable;
      --v1;
    }
    while ( v1 );
    fieldGameClientDispatchTable.__vftable = (CFieldGameClientDispatchTable_vtbl *)&CFieldGameClientDispatchTable::`vftable';
    atexit((void (__cdecl *)())_E2_0);
  }
  return &fieldGameClientDispatchTable;
}
// 4FF8B8: using guessed type void *CFieldGameClientDispatchTable::`vftable';

//----- (004042E0) --------------------------------------------------------
void __usercall CFieldGameClientDispatchTable::~CFieldGameClientDispatchTable(
        CFieldGameClientDispatchTable *this@<ecx>,
        _DWORD *a2@<eax>)
{
  *a2 = &CGameClientDispatchTable::`vftable';
}
// 4FF8C0: using guessed type void *CGameClientDispatchTable::`vftable';

//----- (004042F0) --------------------------------------------------------
CFieldGameClientDispatchTable *__thiscall CFieldGameClientDispatchTable::`scalar deleting destructor'(
        CFieldGameClientDispatchTable *this,
        char a2)
{
  CFieldGameClientDispatchTable::~CFieldGameClientDispatchTable(this, this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00404310) --------------------------------------------------------
int __thiscall CFieldGameClientDispatchTable::Initialize(CFieldGameClientDispatchTable *this)
{
  this->m_ProcessTable[8].m_dwCommand = 8;
  this->m_ProcessTable[8].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharLogin;
  this->m_ProcessTable[36].m_dwCommand = 36;
  this->m_ProcessTable[36].m_fnProcess = GameClientParsePacket::ParseCharLogout;
  this->m_ProcessTable[90].m_dwCommand = 90;
  this->m_ProcessTable[90].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCharMoveZone;
  this->m_ProcessTable[41].m_dwCommand = 41;
  this->m_ProcessTable[41].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseServerZone;
  this->m_ProcessTable[160].m_dwCommand = 160;
  this->m_ProcessTable[160].m_fnProcess = (bool (__cdecl *)(CGameClientDispatch *, PktBase *))GameClientParsePacket::ParseCSAuth;
  return CGameClientDispatchTable::Initialize(this);
}

//----- (00404380) --------------------------------------------------------
CFieldGameClientDispatch *__thiscall CFieldGameClientDispatch::`vector deleting destructor'(
        CFieldGameClientDispatch *this,
        char a2)
{
  CFieldGameClientDispatch::~CFieldGameClientDispatch(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004043B0) --------------------------------------------------------
CConsoleCommand *__thiscall CCMDLotteryEvent::Clone(
        CCMDLotteryEvent *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  _DWORD *v3; // eax
  _DWORD *v4; // ebx
  const char *v5; // eax
  char szBuffer[260]; // [esp+4h] [ebp-108h] BYREF

  _snprintf(szBuffer, 0x100u, "%s", szCommand);
  szBuffer[256] = 0;
  strtok(szBuffer, " \t\r\n");
  v3 = operator new((tagHeader *)8);
  v4 = v3;
  if ( !v3 )
    return 0;
  *v3 = &CCMDLotteryEvent::`vftable';
  v5 = strtok(0, " \t\r\n");
  if ( v5 )
  {
    if ( !strcmp("on", v5) )
      *((_BYTE *)v4 + 4) = 2;
    else
      *((_BYTE *)v4 + 4) = strcmp("off", v5) != 0 ? 0 : 3;
    return (CConsoleCommand *)v4;
  }
  else
  {
    *((_BYTE *)v4 + 4) = 1;
    return (CConsoleCommand *)v4;
  }
}
// 4FF8C8: using guessed type void *CCMDLotteryEvent::`vftable';

//----- (004044C0) --------------------------------------------------------
char __thiscall CCMDLotteryEvent::DoProcess(CCMDLotteryEvent *this)
{
  CRylGameServer *Instance; // eax
  int v3; // [esp-4h] [ebp-4h]

  v3 = strlen("Use server setup for modify lottery event.");
  Instance = CRylGameServer::GetInstance((CRylGameServer *)this);
  CServerWindowFramework::PrintOutput(Instance, "Use server setup for modify lottery event.", v3);
  return 1;
}
// 4044D7: variable 'this' is possibly undefined

//----- (004044F0) --------------------------------------------------------
void __cdecl PrePerformanceMsg(_iobuf *lpFile)
{
  CServerSetup *Instance; // eax
  DWORD CurrentProcessId; // eax
  unsigned int ServerID; // [esp-28h] [ebp-3Ch]
  int ZoneFromCmdLine; // [esp-24h] [ebp-38h]
  int ChannelFromCmdLine; // [esp-20h] [ebp-34h]
  int wYear; // [esp-1Ch] [ebp-30h]
  int wMonth; // [esp-18h] [ebp-2Ch]
  int wDay; // [esp-14h] [ebp-28h]
  int wHour; // [esp-10h] [ebp-24h]
  int wMinute; // [esp-Ch] [ebp-20h]
  int wSecond; // [esp-8h] [ebp-1Ch]
  int wMilliseconds; // [esp-4h] [ebp-18h]
  _SYSTEMTIME currentTime; // [esp+4h] [ebp-10h] BYREF

  if ( lpFile )
  {
    GetLocalTime(&currentTime);
    wMilliseconds = currentTime.wMilliseconds;
    wSecond = currentTime.wSecond;
    wMinute = currentTime.wMinute;
    wHour = currentTime.wHour;
    wDay = currentTime.wDay;
    wMonth = currentTime.wMonth;
    wYear = currentTime.wYear;
    ChannelFromCmdLine = CServerSetup::GetChannelFromCmdLine();
    ZoneFromCmdLine = CServerSetup::GetZoneFromCmdLine();
    Instance = CServerSetup::GetInstance();
    ServerID = CServerSetup::GetServerID(Instance);
    CurrentProcessId = GetCurrentProcessId();
    fprintf(
      lpFile,
      "\n"
      "------------------------------------------------------------------------\n"
      "\tPID:0x%08x/ServerID:0x%08x/ServerZone:%02d/ServerChannel:%02d/\n"
      "\tGameServer PerformanceCheck. %04d-%02d-%02d %02d:%02d:%02d:%04d\n"
      "\n",
      CurrentProcessId,
      ServerID,
      ZoneFromCmdLine,
      ChannelFromCmdLine,
      wYear,
      wMonth,
      wDay,
      wHour,
      wMinute,
      wSecond,
      wMilliseconds);
  }
}

//----- (00404570) --------------------------------------------------------
char __thiscall CRylGameServer::InitializeGameObject(CRylGameServer *this, CRylGameServer *thisa)
{
  CServerSetup *Instance; // eax
  CServerSetup *v3; // eax
  char ServerZone; // al
  CServerSetup *v5; // eax
  CServerSetup *v6; // eax
  char v7; // al
  CPacketStatistics *v8; // eax
  CPerformanceCheck *v9; // eax
  CFieldGameClientDispatchTable *v10; // eax
  CGameLog *v11; // eax
  CCellManager *v12; // esi
  CMonsterShout *v13; // eax
  char *v15; // [esp-8h] [ebp-318h]
  int ServerChannel; // [esp-4h] [ebp-314h]
  int v17; // [esp-4h] [ebp-314h]
  char szProgramName[260]; // [esp+0h] [ebp-310h] BYREF
  char szLogFilePrefixName[260]; // [esp+104h] [ebp-20Ch] BYREF
  char szSessionLogFilePrefixName[260]; // [esp+208h] [ebp-108h] BYREF

  DbgUtils::SetProgramName(szProgramName, 0x104u, 0);
  szProgramName[259] = 0;
  Instance = CServerSetup::GetInstance();
  ServerChannel = (char)CServerSetup::GetServerChannel(Instance);
  v3 = CServerSetup::GetInstance();
  ServerZone = CServerSetup::GetServerZone(v3);
  _snprintf(szLogFilePrefixName, 0x103u, "%sZ%02dC%02d-", szProgramName, ServerZone, ServerChannel);
  v5 = CServerSetup::GetInstance();
  v17 = (char)CServerSetup::GetServerChannel(v5);
  v6 = CServerSetup::GetInstance();
  v7 = CServerSetup::GetServerZone(v6);
  _snprintf(szSessionLogFilePrefixName, 0x103u, "SessionLogZ%02dC%02d-", v7, v17);
  CServerLog::SetLogFileName(&g_Log, szLogFilePrefixName, szProgramName);
  CServerLog::SetLogFileName(&g_SessionLog, szSessionLogFilePrefixName, szProgramName);
  v8 = CPacketStatistics::GetInstance();
  CPacketStatistics::SetUserMessageFunc(v8, PrePerformanceMsg, 0);
  v9 = CPerformanceCheck::GetInstance();
  CPerformanceCheck::SetUserMessageFunc(v9, PrePerformanceMsg, 0);
  v10 = CFieldGameClientDispatchTable::GetInstance();
  if ( !v10->Initialize(v10) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      78,
      byte_4FFABC);
    return 0;
  }
  v11 = CGameLog::GetInstance();
  if ( !CGameLog::Initialize(v11, szLogFilePrefixName) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      85,
      byte_4FF9F4);
    return 0;
  }
  if ( !Item::CItemMgr::LoadItemProtoType(CSingleton<Item::CItemMgr>::ms_pSingleton, 0) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      92,
      byte_4FF9D0);
    return 0;
  }
  if ( !Item::CItemMgr::LoadItemChemical(CSingleton<Item::CItemMgr>::ms_pSingleton, 0) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      99,
      byte_4FF9A4);
    return 0;
  }
  if ( !CSkillMgr::LoadSkillsFromFile(CSingleton<CSkillMgr>::ms_pSingleton, 0) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      106,
      byte_4FF980);
    return 0;
  }
  if ( !Skill::CProcessTable::Initialize(CSingleton<Skill::CProcessTable>::ms_pSingleton) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      113,
      byte_4FF954);
    return 0;
  }
  if ( !CGameEventMgr::Initialize(CSingleton<CGameEventMgr>::ms_pSingleton) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      120,
      byte_4FF92C);
    return 0;
  }
  v12 = CCellManager::GetInstance();
  if ( !v12->m_bLoadComplete )
  {
    CCellManager::Load(v12);
    v12->m_bMoving = 1;
    v12->m_wNumMoving = 20;
  }
  v15 = (char *)CMonsterShout::ms_DefaultFileName;
  v13 = CMonsterShout::GetInstance();
  if ( !CMonsterShout::LoadScript(v13, v15) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::InitializeGameObject",
      aDWorkRylSource_12,
      136,
      (char *)&byte_4FF900);
    return 0;
  }
  thisa->m_bInitialized = 1;
  return 1;
}

//----- (004047D0) --------------------------------------------------------
CConsoleCommand *__thiscall CCMDNotify::Clone(CCMDNotify *this, const char *szCommand, unsigned int nCommandLength)
{
  unsigned int i; // edi
  char v4; // al
  unsigned int v5; // edi
  CConsoleCommand *result; // eax
  _DWORD *v7; // eax
  LPVOID v8; // esi
  int v9; // eax

  for ( i = 0; i < nCommandLength; ++i )
  {
    v4 = szCommand[i];
    if ( !v4 )
      break;
    if ( v4 == 32 )
      break;
    if ( v4 == 9 )
      break;
    if ( v4 == 10 )
      break;
  }
  v5 = i + 1;
  result = 0;
  if ( v5 < nCommandLength )
  {
    v7 = operator new((tagHeader *)0x7D8);
    if ( v7 )
    {
      *v7 = &CCMDNotify::`vftable';
      v7[501] = 0;
      *((_BYTE *)v7 + 4) = 0;
      v8 = v7;
    }
    else
    {
      v8 = 0;
    }
    if ( !v8 )
      return (CConsoleCommand *)v8;
    v9 = CServerSetup::GetInstance()->m_eNationType
       ? _snprintf((char *)v8 + 22, 0xB3u, "%s", &szCommand[v5])
       : _snprintf((char *)v8 + 22, 0xB3u, asc_4FFBF4, &szCommand[v5]);
    *((_BYTE *)v8 + 201) = 0;
    *((_DWORD *)v8 + 4) = 0;
    *((_WORD *)v8 + 10) = 255;
    *((_DWORD *)v8 + 501) = v9 + 19;
    if ( PacketWrap::WrapCrypt((char *)v8 + 4, *((_WORD *)v8 + 1002), 0xDu, 0, 0) )
    {
      return (CConsoleCommand *)v8;
    }
    else
    {
      operator delete(v8);
      return 0;
    }
  }
  return result;
}
// 4FFBE8: using guessed type void *CCMDNotify::`vftable';
// 4FFBF4: using guessed type char asc_4FFBF4;

//----- (004048D0) --------------------------------------------------------
char __thiscall CCMDNotify::DoProcess(CCMDNotify *this)
{
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-14h] BYREF
  int v5; // [esp+14h] [ebp-4h]

  if ( this->m_nLength )
  {
    DispatchTable = CChatDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
    v5 = 0;
    if ( StoragelpChatDispatch.m_lpDispatch )
      CSendStream::PutBuffer(
        (CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8],
        this->m_szBuffer,
        this->m_nLength,
        0xDu);
    v5 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
  }
  return 1;
}

//----- (00404950) --------------------------------------------------------
CSingleDispatch *__cdecl CLogDispatch::GetDispatchTable()
{
  if ( (__S1__1__GetDispatchTable_CLogDispatch__SAAAVCSingleDispatch__XZ_4IA & 1) == 0 )
  {
    __S1__1__GetDispatchTable_CLogDispatch__SAAAVCSingleDispatch__XZ_4IA |= 1u;
    CSingleDispatch::CSingleDispatch(&`CLogDispatch::GetDispatchTable'::`2'::singleDispatch);
    atexit(`CLogDispatch::GetDispatchTable'::`2'::singleDispatch);
  }
  return &`CLogDispatch::GetDispatchTable'::`2'::singleDispatch;
}
// 53D9B0: using guessed type int __S1__1__GetDispatchTable_CLogDispatch__SAAAVCSingleDispatch__XZ_4IA;

//----- (004049B0) --------------------------------------------------------
void __thiscall CCommandProcess::Add(CCommandProcess *this, CCommand *lpNewCMD)
{
  std::_List_nod<CThread *>::_Node *Myhead; // ebx
  std::_List_nod<CThread *>::_Node *v4; // ebp

  if ( lpNewCMD )
  {
    EnterCriticalSection(&this->m_CMDLock.m_CSLock);
    Myhead = (std::_List_nod<CThread *>::_Node *)this->m_CMDList._Myhead;
    v4 = std::list<IOPCode *>::_Buynode(
           (std::list<CThread *> *)&this->m_CMDList,
           Myhead,
           Myhead->_Prev,
           (CThread **)&lpNewCMD);
    std::list<CCommand *>::_Incsize(&this->m_CMDList, 1u);
    Myhead->_Prev = v4;
    v4->_Prev->_Next = v4;
    LeaveCriticalSection(&this->m_CMDLock.m_CSLock);
  }
}

//----- (00404A30) --------------------------------------------------------
CRylGameServer *__fastcall CRylGameServer::GetInstance(CRylGameServer *a1)
{
  LOBYTE(a1) = (_BYTE)_S3_6;
  if ( ((unsigned __int8)_S3_6 & 1) == 0 )
  {
    _S3_6 = (CRylGameServer *)((unsigned int)_S3_6 | 1);
    CRylGameServer::CRylGameServer(a1, &rylGameServer);
    atexit((void (__cdecl *)())_E4_2);
  }
  return &rylGameServer;
}

//----- (00404A90) --------------------------------------------------------
void __thiscall CRylGameServer::CRylGameServer(CRylGameServer *this, CRylGameServer *thisa)
{
  CUDPWish *v2; // eax
  CUDPWish *v3; // ecx
  CUDPWish *v4; // eax
  CLimitUserByIP *v5; // eax
  CLimitUserByIP *v6; // eax

  CServerWindowFramework::CServerWindowFramework(thisa);
  thisa->__vftable = (CRylGameServer_vtbl *)&CRylGameServer::`vftable';
  v2 = (CUDPWish *)operator new((tagHeader *)0x80);
  if ( v2 )
    CUDPWish::CUDPWish(v3, v2, 0x14u);
  else
    v4 = 0;
  thisa->m_lpUDPWish = v4;
  thisa->m_lpClientPolicy = CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CFieldGameClientDispatch>,CStreamOverlappedFactory>();
  thisa->m_lpRegularAgentPolicy = CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CRegularAgentDispatch>,CStreamOverlappedFactory>();
  thisa->m_lpAgentPolicy = CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CDBAgentDispatch>,CStreamOverlappedFactory>();
  thisa->m_lpChatPolicy = CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CChatDispatch>,CStreamOverlappedFactory>();
  thisa->m_lpLogPolicy = CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CLogDispatch>,CStreamOverlappedFactory>();
  v5 = (CLimitUserByIP *)operator new((tagHeader *)0x34);
  if ( v5 )
    CLimitUserByIP::CLimitUserByIP(v5, 0);
  else
    v6 = 0;
  thisa->m_lpClientLimit = v6;
  thisa->m_bStartServer = 0;
  thisa->m_bInitialized = 0;
}
// 404ADC: variable 'v3' is possibly undefined
// 404AE9: variable 'v4' is possibly undefined
// 404B4D: variable 'v6' is possibly undefined
// 4FFC20: using guessed type void *CRylGameServer::`vftable';

//----- (00404B70) --------------------------------------------------------
CRylGameServer *__thiscall CRylGameServer::`vector deleting destructor'(CRylGameServer *this, char a2)
{
  CRylGameServer::~CRylGameServer(this, this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00404B90) --------------------------------------------------------
void __thiscall CRylGameServer::~CRylGameServer(CRylGameServer *this, CRylGameServer *thisa)
{
  CSessionPolicy *m_lpClientPolicy; // ecx
  CSessionPolicy *m_lpAgentPolicy; // ecx
  CSessionPolicy *m_lpRegularAgentPolicy; // ecx
  CSessionPolicy *m_lpChatPolicy; // ecx
  CSessionPolicy *m_lpLogPolicy; // ecx

  thisa->__vftable = (CRylGameServer_vtbl *)&CRylGameServer::`vftable';
  m_lpClientPolicy = thisa->m_lpClientPolicy;
  if ( m_lpClientPolicy )
  {
    CSessionPolicy::Release(m_lpClientPolicy);
    thisa->m_lpClientPolicy = 0;
  }
  m_lpAgentPolicy = thisa->m_lpAgentPolicy;
  if ( m_lpAgentPolicy )
  {
    CSessionPolicy::Release(m_lpAgentPolicy);
    thisa->m_lpAgentPolicy = 0;
  }
  m_lpRegularAgentPolicy = thisa->m_lpRegularAgentPolicy;
  if ( m_lpRegularAgentPolicy )
  {
    CSessionPolicy::Release(m_lpRegularAgentPolicy);
    thisa->m_lpRegularAgentPolicy = 0;
  }
  m_lpChatPolicy = thisa->m_lpChatPolicy;
  if ( m_lpChatPolicy )
  {
    CSessionPolicy::Release(m_lpChatPolicy);
    thisa->m_lpChatPolicy = 0;
  }
  m_lpLogPolicy = thisa->m_lpLogPolicy;
  if ( m_lpLogPolicy )
  {
    CSessionPolicy::Release(m_lpLogPolicy);
    thisa->m_lpLogPolicy = 0;
  }
  CServerWindowFramework::~CServerWindowFramework(thisa);
}
// 4FFC20: using guessed type void *CRylGameServer::`vftable';

//----- (00404C50) --------------------------------------------------------
char __thiscall CRylGameServer::ApplicationSpecificInit(CRylGameServer *this, const char *szCmdLine)
{
  const char *v3; // ebx
  CServerSetup *Instance; // eax
  CRylGameServer *v5; // ecx
  CRylGameServer *v6; // ecx
  CRylGameServer *v7; // ecx
  CUDPWish *v8; // ecx
  CDBSingleObject *v9; // eax
  CConsoleCMDFactory *m_lpCommandFactory; // ecx
  CCommandProcess *m_lpCommandProcess; // edi
  CConsoleCommand *v13; // eax

  v3 = 0;
  if ( this->m_lpUDPWish
    && this->m_lpClientPolicy
    && this->m_lpClientLimit
    && this->m_lpRegularAgentPolicy
    && this->m_lpAgentPolicy
    && this->m_lpChatPolicy
    && this->m_lpLogPolicy )
  {
    Instance = CServerSetup::GetInstance();
    if ( CServerSetup::Initialize(Instance, 2) )
    {
      if ( CRylGameServer::InitializeMsgProc(v5, (int)this) )
      {
        if ( CRylGameServer::InitializeCommand(v6, (int)this) )
        {
          if ( CRylGameServer::InitializeGameObject(v7, this) )
          {
            if ( CUDPWish::Initialize(v8, &this->m_lpUDPWish->__vftable) )
            {
              if ( CServerWindowFramework::AddProcessThread(this, this->m_lpUDPWish) )
              {
                if ( CRylGameServer::AddGameProcessThread(this) )
                {
                  if ( LoadAuthTable("./Script/Server/CSAuth.tab") )
                  {
                    if ( LoadAuthIndex("./Script/Server/CSAuth.idx") )
                    {
                      if ( !InitPacketProtect(byte_5000E0, 0) )
                        v3 = "RylGameServer initialize failed : GameGuard InitPacketProtect failed";
                    }
                    else
                    {
                      v3 = "RylGameServer initialize failed : GameGuard LoadAuthIndex failed";
                    }
                  }
                  else
                  {
                    v3 = "RylGameServer initialize failed : GameGuard LoadAuthTable failed";
                  }
                }
                else
                {
                  v3 = "RylGameServer initialize failed : Add game process thread failed";
                }
              }
              else
              {
                v3 = "RylGameServer initialize failed : Add address recv thread failed";
              }
            }
            else
            {
              v3 = "RylGameServer initialize failed : Address recv thread initialize failed";
            }
          }
          else
          {
            v3 = "RylGameServer initialize failed : Gameobjects load failed";
          }
        }
        else
        {
          v3 = "RylGameServer initialize failed : Command add failed";
        }
      }
      else
      {
        v3 = "RylGameServer initialize failed : Message proc add failed";
      }
    }
    else
    {
      v3 = "RylGameServer initialize failed : Serversetup load failed";
    }
  }
  else
  {
    v3 = "RylGameServer initialize failed : Internal object creation error";
  }
  if ( CServerSetup::GetInstance()->m_bBattleGame )
  {
    v9 = CDBSingleObject::GetInstance();
    if ( !CDBComponent::Connect(v9, 2) )
      v3 = "BattleGround DB connect failed.";
  }
  if ( v3 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::ApplicationSpecificInit",
      aDWorkRylSource_94,
      129,
      "this:0x%p/%s",
      this,
      v3);
    return 0;
  }
  else
  {
    CLimitUserByIP::OperateMode(this->m_lpClientLimit, ALLOW_ALL);
    m_lpCommandFactory = this->m_lpCommandFactory;
    m_lpCommandProcess = this->m_lpCommandProcess;
    v13 = CConsoleCMDFactory::Create(m_lpCommandFactory, "startserver", 0xBu);
    CCommandProcess::Add(m_lpCommandProcess, v13);
    return 1;
  }
}
// 404CD7: variable 'v5' is possibly undefined
// 404CEC: variable 'v6' is possibly undefined
// 404D00: variable 'v7' is possibly undefined
// 404D19: variable 'v8' is possibly undefined

//----- (00404E30) --------------------------------------------------------
void __usercall CRylGameServer::StartServer(CRylGameServer *this@<ecx>, CRylGameServer *a2@<esi>)
{
  CServerSetup *Instance; // eax
  CRylGameServer *v3; // ecx
  CRylGameServer *v4; // ecx
  CServerSetup *v5; // eax
  signed int ServerID; // eax
  CIOCPNet *m_lpIOCPNet; // edi
  u_short GameServerTCPPort; // ax
  int Error; // eax

  if ( a2->m_bStartServer )
  {
    CServerWindowFramework::PrintOutput(a2, "Already started server.", strlen("Already started server."));
  }
  else
  {
    Instance = CServerSetup::GetInstance();
    if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
      CRylGameServer::ConnectToRegularAgent(v3, a2);
    CRylGameServer::ConnectToAgent(v3, a2);
    CRylGameServer::ConnectToChatServer(v4, a2);
    if ( a2->m_lpIOCPNet )
    {
      v5 = CServerSetup::GetInstance();
      ServerID = CServerSetup::GetServerID(v5);
      m_lpIOCPNet = a2->m_lpIOCPNet;
      GameServerTCPPort = CServerSetup::GetGameServerTCPPort(ServerID);
      if ( CIOCPNet::AddListener(
             m_lpIOCPNet,
             a2->m_lpClientPolicy,
             0,
             GameServerTCPPort,
             (_RTL_CRITICAL_SECTION *)0xA,
             0) )
      {
        a2->m_bStartServer = 1;
      }
      else
      {
        Error = WSAGetLastError();
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CRylGameServer::StartServer",
          aDWorkRylSource_94,
          167,
          "this:0x%p/Err:%d/GameServer initialize failed : Client Listener create failed.",
          a2,
          Error);
      }
    }
  }
}
// 404E4F: variable 'v3' is possibly undefined
// 404E5B: variable 'v4' is possibly undefined

//----- (00404F00) --------------------------------------------------------
void __thiscall CRylGameServer::ConnectToRegularAgent(CRylGameServer *this, CRylGameServer *thisa)
{
  int v2; // ebx
  int v3; // esi
  char *v4; // eax
  int Error; // [esp-14h] [ebp-18h]
  CIOCPNet *lpIOCPnet; // [esp+0h] [ebp-4h]

  CRegularAgentDispatch::Initialize();
  lpIOCPnet = thisa->m_lpIOCPNet;
  if ( lpIOCPnet )
  {
    v2 = 0;
    if ( CRegularAgentDispatch::ms_cCurrentGroupNum )
    {
      do
      {
        v3 = (unsigned __int8)v2;
        v4 = inet_ntoa(CRegularAgentDispatch::ms_AgentServerInfo[v3].m_ServerAddress);
        if ( CIOCPNet::Connect(lpIOCPnet, thisa->m_lpRegularAgentPolicy, v4, (_RTL_CRITICAL_SECTION *)0x2780) )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CRylGameServer::ConnectToRegularAgent",
            aDWorkRylSource_94,
            198,
            (char *)&byte_4FFEBC,
            CRegularAgentDispatch::ms_AgentServerInfo[v3].m_szGroupName);
        }
        else
        {
          Error = WSAGetLastError();
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CRylGameServer::ConnectToRegularAgent",
            aDWorkRylSource_94,
            192,
            "Agent Session create failed. ErrorCode:%d",
            Error);
        }
        ++v2;
      }
      while ( v2 < CRegularAgentDispatch::ms_cCurrentGroupNum );
    }
  }
}

//----- (00404FD0) --------------------------------------------------------
void __thiscall CRylGameServer::ConnectToAgent(CRylGameServer *this, CRylGameServer *thisa)
{
  CIOCPNet *m_lpIOCPNet; // ebx
  CSingleDispatch *DispatchTable; // eax
  struct in_addr *Instance; // eax
  struct in_addr *v5; // esi
  char *v6; // edi
  _RTL_CRITICAL_SECTION *v7; // eax
  int Error; // eax

  m_lpIOCPNet = thisa->m_lpIOCPNet;
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  if ( !CSingleDispatch::GetDispatchNum(DispatchTable) && m_lpIOCPNet )
  {
    Instance = (struct in_addr *)CServerSetup::GetInstance();
    v5 = Instance + 98;
    v6 = inet_ntoa(Instance[99]);
    LOWORD(v7) = ntohs(v5->S_un.S_un_w.s_w2);
    if ( !CIOCPNet::Connect(m_lpIOCPNet, thisa->m_lpAgentPolicy, v6, v7) )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CRylGameServer::ConnectToAgent",
        aDWorkRylSource_94,
        215,
        "Agent Session create failed. ErrorCode:%d",
        Error);
    }
  }
}
// 405023: variable 'v7' is possibly undefined

//----- (00405060) --------------------------------------------------------
void __thiscall CRylGameServer::ConnectToChatServer(CRylGameServer *this, CRylGameServer *thisa)
{
  CIOCPNet *m_lpIOCPNet; // ebx
  CSingleDispatch *DispatchTable; // eax
  struct in_addr *Instance; // eax
  struct in_addr *v5; // esi
  char *v6; // edi
  _RTL_CRITICAL_SECTION *v7; // eax
  int Error; // eax

  m_lpIOCPNet = thisa->m_lpIOCPNet;
  DispatchTable = CChatDispatch::GetDispatchTable();
  if ( !CSingleDispatch::GetDispatchNum(DispatchTable) && m_lpIOCPNet )
  {
    Instance = (struct in_addr *)CServerSetup::GetInstance();
    v5 = Instance + 118;
    v6 = inet_ntoa(Instance[119]);
    LOWORD(v7) = ntohs(v5->S_un.S_un_w.s_w2);
    if ( !CIOCPNet::Connect(m_lpIOCPNet, thisa->m_lpChatPolicy, v6, v7) )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CRylGameServer::ConnectToChatServer",
        aDWorkRylSource_94,
        251,
        "Chat Session create failed. ErrorCode:%d",
        Error);
    }
  }
}
// 4050B3: variable 'v7' is possibly undefined

//----- (004050F0) --------------------------------------------------------
void __usercall CRylGameServer::PrintStatistics(CRylGameServer *this@<ecx>, CServerWindowFramework *a2@<edi>)
{
  CIOCPNet *m_lpIOCPNet; // esi
  int AcceptPendingNum; // eax
  int v4; // eax
  unsigned int SessionNum; // [esp-8h] [ebp-1010h]
  int Mysize_low; // [esp-4h] [ebp-100Ch]
  char string[4096]; // [esp+4h] [ebp-1004h] BYREF

  m_lpIOCPNet = a2->m_lpIOCPNet;
  if ( m_lpIOCPNet )
  {
    Mysize_low = LOWORD(CCreatureManager::GetInstance()->m_CharacterMap._Mysize);
    SessionNum = CIOCPNet::GetSessionNum(m_lpIOCPNet);
    AcceptPendingNum = CIOCPNet::GetAcceptPendingNum(m_lpIOCPNet);
    v4 = _snprintf(
           string,
           0x1000u,
           "Accept pending : %d, Current session : %d, Current Character : %d",
           AcceptPendingNum,
           SessionNum,
           Mysize_low);
    if ( v4 > 0 )
      CServerWindowFramework::PrintOutput(a2, string, v4);
  }
}

//----- (00405170) --------------------------------------------------------
void __thiscall CRylGameServer::PrintServerInfo(CRylGameServer *this, CServerWindowFramework *a2)
{
  CServerSetup *Instance; // eax
  unsigned int ServerID; // ebx
  CSingleDispatch *DispatchTable; // eax
  const char *v5; // edi
  CSingleDispatch *v6; // eax
  const char *v7; // esi
  CSingleDispatch *v8; // eax
  BOOL DispatchNum; // eax
  const char *v10; // ecx
  const char *v11; // eax
  int v12; // eax
  char string[4096]; // [esp+14h] [ebp-1004h] BYREF

  Instance = CServerSetup::GetInstance();
  ServerID = CServerSetup::GetServerID(Instance);
  DispatchTable = CChatDispatch::GetDispatchTable();
  v5 = "Connected";
  if ( !CSingleDispatch::GetDispatchNum(DispatchTable) )
    v5 = "Disconnected";
  v6 = CLogDispatch::GetDispatchTable();
  v7 = "Connected";
  if ( !CSingleDispatch::GetDispatchNum(v6) )
    v7 = "Disconnected";
  v8 = CDBAgentDispatch::GetDispatchTable();
  DispatchNum = CSingleDispatch::GetDispatchNum(v8);
  v10 = "Connected";
  if ( !DispatchNum )
    v10 = "Disconnected";
  v11 = "Started Server";
  if ( !LOBYTE(a2[1].m_lpConsoleWindow) )
    v11 = "Closed Server";
  v12 = _snprintf(
          string,
          0x1000u,
          "Server ID      : 0x%08x\r\n"
          "Server Group   : %d\r\n"
          "Server Zone    : %d\r\n"
          "Server Channel : %d\r\n"
          "Server Status  : %s\r\n"
          "Connect with DBAgentServer : %s\r\n"
          "Connect with LogServer     : %s\r\n"
          "Connect with ChatServer    : %s\r\n",
          ServerID,
          SBYTE1(ServerID),
          SHIBYTE(ServerID),
          SBYTE2(ServerID),
          v11,
          v10,
          v7,
          v5);
  if ( v12 > 0 )
    CServerWindowFramework::PrintInfo(a2, string, v12);
}

//----- (00405260) --------------------------------------------------------
void __thiscall CRylGameServer::ReloadSetup(CRylGameServer *this, CRylGameServer *thisa)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( CServerSetup::Initialize(Instance, 2) )
  {
    CServerWindowFramework::PrintOutput(thisa, "Serversetup reloaded", strlen("Serversetup reloaded"));
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CRylGameServer::ReloadSetup",
      aDWorkRylSource_94,
      325,
      "Serversetup reloaded");
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::ReloadSetup",
      aDWorkRylSource_94,
      318,
      "Serversetup reload failed");
  }
  if ( !LoadAuthTable("./Script/Server/CSAuth.tab") )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::ReloadSetup",
      aDWorkRylSource_94,
      330,
      "GameGuard LoadAuthTable reload failed");
  if ( !LoadAuthIndex("./Script/Server/CSAuth.idx") )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CRylGameServer::ReloadSetup",
      aDWorkRylSource_94,
      335,
      "GameGuard LoadAuthIndex reload failed");
}

//----- (00405340) --------------------------------------------------------
void __thiscall CLock<CCSLock>::Syncronize::Syncronize(CLock<CCSLock>::Syncronize *this, CCSLock *LockClass)
{
  this->m_Lock = LockClass;
  EnterCriticalSection(&LockClass->m_CSLock);
}

//----- (00405360) --------------------------------------------------------
void __thiscall CLock<CCSLock>::Syncronize::~Syncronize(CLock<CCSLock>::Syncronize *this)
{
  LeaveCriticalSection(&this->m_Lock->m_CSLock);
}

//----- (00405370) --------------------------------------------------------
std::_List_nod<CThread *>::_Node *__thiscall std::list<IOPCode *>::_Buynode(
        std::list<CThread *> *this,
        std::_List_nod<CThread *>::_Node *_Next,
        std::_List_nod<CThread *>::_Node *_Prev,
        CThread **_Val)
{
  std::_List_nod<CThread *>::_Node *result; // eax

  result = (std::_List_nod<CThread *>::_Node *)operator new((tagHeader *)0xC);
  if ( result )
  {
    result->_Next = _Next;
    result->_Prev = _Prev;
    result->_Myval = *_Val;
  }
  return result;
}

//----- (004053A0) --------------------------------------------------------
void __thiscall std::list<CCommand *>::_Incsize(std::list<CCommand *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Right; // [esp+4h] [ebp-50h] BYREF
  exception pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  std::string v5; // [esp+2Ch] [ebp-28h] BYREF
  int v6; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Right._Myres = 15;
    _Right._Mysize = 0;
    _Right._Bx._Buf[0] = 0;
    std::string::assign(&_Right, "list<T> too long", 0x10u);
    v6 = 0;
    exception::exception(&pExceptionObject);
    LOBYTE(v6) = 1;
    pExceptionObject.__vftable = (exception_vtbl *)&std::logic_error::`vftable';
    v5._Myres = 15;
    v5._Mysize = 0;
    v5._Bx._Buf[0] = 0;
    std::string::assign(&v5, &_Right, 0, 0xFFFFFFFF);
    LOBYTE(v6) = 0;
    pExceptionObject.__vftable = (exception_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8A4: using guessed type void *std::logic_error::`vftable';
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00405460) --------------------------------------------------------
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CFieldGameClientDispatch>,CStreamOverlappedFactory>()
{
  CStreamOverlappedFactory *v0; // eax
  unsigned int v1; // eax
  unsigned int v2; // ebp
  _DWORD *v3; // eax
  unsigned int v4; // edi
  CPoolBufferFactory *v5; // eax
  AtType v6; // eax
  AtType v7; // esi
  CTCPFactory *v8; // eax
  const Skill::ProtoType *v9; // eax
  CSessionPolicy *v10; // eax
  CSessionPolicy *v11; // esi
  CSpeedHackCheck::CoolDownInfo *v13; // [esp+8h] [ebp-14h]

  v13 = (CSpeedHackCheck::CoolDownInfo *)operator new((tagHeader *)0x14);
  if ( v13 )
  {
    v0 = (CStreamOverlappedFactory *)operator new((tagHeader *)0x28);
    if ( v0 )
    {
      CStreamOverlappedFactory::CStreamOverlappedFactory(v0);
      v2 = v1;
    }
    else
    {
      v2 = 0;
    }
    v3 = operator new((tagHeader *)0x18);
    if ( v3 )
    {
      *v3 = &CPoolDispatchFactory<CFieldGameClientDispatch>::`vftable';
      v3[1] = 0;
      v3[2] = 0;
      v3[3] = 0;
      v3[4] = 396;
      v3[5] = 32;
      v4 = (unsigned int)v3;
    }
    else
    {
      v4 = 0;
    }
    v5 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
    if ( v5 )
    {
      CPoolBufferFactory::CPoolBufferFactory(v5);
      v7 = v6;
    }
    else
    {
      v7 = 0;
    }
    v8 = (CTCPFactory *)operator new((tagHeader *)0x14);
    if ( v8 )
      CTCPFactory::CTCPFactory(v8);
    else
      v9 = 0;
    CSpeedHackCheck::CoolDownInfo::CoolDownInfo(v13, v9, v7, v4, v2);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  if ( !v11 )
    return 0;
  if ( !v11->m_lpBufferFactory || !v11->m_lpSocketFactory || !v11->m_lpDispatchFactory || !v11->m_lpOverlappedFactory )
  {
    CSessionPolicy::~CSessionPolicy(v11);
    operator delete(v11);
    return 0;
  }
  return v11;
}
// 4054BB: variable 'v1' is possibly undefined
// 405514: variable 'v6' is possibly undefined
// 40554C: variable 'v9' is possibly undefined
// 405552: variable 'v10' is possibly undefined
// 4FFC14: using guessed type void *CPoolDispatchFactory<CFieldGameClientDispatch>::`vftable';

//----- (004055B0) --------------------------------------------------------
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CRegularAgentDispatch>,CStreamOverlappedFactory>()
{
  CStreamOverlappedFactory *v0; // eax
  unsigned int v1; // eax
  unsigned int v2; // ebp
  _DWORD *v3; // eax
  unsigned int v4; // edi
  CPoolBufferFactory *v5; // eax
  AtType v6; // eax
  AtType v7; // esi
  CTCPFactory *v8; // eax
  const Skill::ProtoType *v9; // eax
  CSessionPolicy *v10; // eax
  CSessionPolicy *v11; // esi
  CSpeedHackCheck::CoolDownInfo *v13; // [esp+8h] [ebp-14h]

  v13 = (CSpeedHackCheck::CoolDownInfo *)operator new((tagHeader *)0x14);
  if ( v13 )
  {
    v0 = (CStreamOverlappedFactory *)operator new((tagHeader *)0x28);
    if ( v0 )
    {
      CStreamOverlappedFactory::CStreamOverlappedFactory(v0);
      v2 = v1;
    }
    else
    {
      v2 = 0;
    }
    v3 = operator new((tagHeader *)0x18);
    if ( v3 )
    {
      *v3 = &CPoolDispatchFactory<CRegularAgentDispatch>::`vftable';
      v3[1] = 0;
      v3[2] = 0;
      v3[3] = 0;
      v3[4] = 84;
      v3[5] = 32;
      v4 = (unsigned int)v3;
    }
    else
    {
      v4 = 0;
    }
    v5 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
    if ( v5 )
    {
      CPoolBufferFactory::CPoolBufferFactory(v5);
      v7 = v6;
    }
    else
    {
      v7 = 0;
    }
    v8 = (CTCPFactory *)operator new((tagHeader *)0x14);
    if ( v8 )
      CTCPFactory::CTCPFactory(v8);
    else
      v9 = 0;
    CSpeedHackCheck::CoolDownInfo::CoolDownInfo(v13, v9, v7, v4, v2);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  if ( !v11 )
    return 0;
  if ( !v11->m_lpBufferFactory || !v11->m_lpSocketFactory || !v11->m_lpDispatchFactory || !v11->m_lpOverlappedFactory )
  {
    CSessionPolicy::~CSessionPolicy(v11);
    operator delete(v11);
    return 0;
  }
  return v11;
}
// 40560B: variable 'v1' is possibly undefined
// 405664: variable 'v6' is possibly undefined
// 40569C: variable 'v9' is possibly undefined
// 4056A2: variable 'v10' is possibly undefined
// 4FFC08: using guessed type void *CPoolDispatchFactory<CRegularAgentDispatch>::`vftable';

//----- (00405700) --------------------------------------------------------
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CDBAgentDispatch>,CStreamOverlappedFactory>()
{
  CStreamOverlappedFactory *v0; // eax
  unsigned int v1; // eax
  unsigned int v2; // ebp
  _DWORD *v3; // eax
  unsigned int v4; // edi
  CPoolBufferFactory *v5; // eax
  AtType v6; // eax
  AtType v7; // esi
  CTCPFactory *v8; // eax
  const Skill::ProtoType *v9; // eax
  CSessionPolicy *v10; // eax
  CSessionPolicy *v11; // esi
  CSpeedHackCheck::CoolDownInfo *v13; // [esp+8h] [ebp-14h]

  v13 = (CSpeedHackCheck::CoolDownInfo *)operator new((tagHeader *)0x14);
  if ( v13 )
  {
    v0 = (CStreamOverlappedFactory *)operator new((tagHeader *)0x28);
    if ( v0 )
    {
      CStreamOverlappedFactory::CStreamOverlappedFactory(v0);
      v2 = v1;
    }
    else
    {
      v2 = 0;
    }
    v3 = operator new((tagHeader *)0x18);
    if ( v3 )
    {
      *v3 = &CPoolDispatchFactory<CDBAgentDispatch>::`vftable';
      v3[1] = 0;
      v3[2] = 0;
      v3[3] = 0;
      v3[4] = 80;
      v3[5] = 32;
      v4 = (unsigned int)v3;
    }
    else
    {
      v4 = 0;
    }
    v5 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
    if ( v5 )
    {
      CPoolBufferFactory::CPoolBufferFactory(v5);
      v7 = v6;
    }
    else
    {
      v7 = 0;
    }
    v8 = (CTCPFactory *)operator new((tagHeader *)0x14);
    if ( v8 )
      CTCPFactory::CTCPFactory(v8);
    else
      v9 = 0;
    CSpeedHackCheck::CoolDownInfo::CoolDownInfo(v13, v9, v7, v4, v2);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  if ( !v11 )
    return 0;
  if ( !v11->m_lpBufferFactory || !v11->m_lpSocketFactory || !v11->m_lpDispatchFactory || !v11->m_lpOverlappedFactory )
  {
    CSessionPolicy::~CSessionPolicy(v11);
    operator delete(v11);
    return 0;
  }
  return v11;
}
// 40575B: variable 'v1' is possibly undefined
// 4057B4: variable 'v6' is possibly undefined
// 4057EC: variable 'v9' is possibly undefined
// 4057F2: variable 'v10' is possibly undefined
// 4FFC4C: using guessed type void *CPoolDispatchFactory<CDBAgentDispatch>::`vftable';

//----- (00405850) --------------------------------------------------------
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CChatDispatch>,CStreamOverlappedFactory>()
{
  CStreamOverlappedFactory *v0; // eax
  unsigned int v1; // eax
  unsigned int v2; // ebp
  _DWORD *v3; // eax
  unsigned int v4; // edi
  CPoolBufferFactory *v5; // eax
  AtType v6; // eax
  AtType v7; // esi
  CTCPFactory *v8; // eax
  const Skill::ProtoType *v9; // eax
  CSessionPolicy *v10; // eax
  CSessionPolicy *v11; // esi
  CSpeedHackCheck::CoolDownInfo *v13; // [esp+8h] [ebp-14h]

  v13 = (CSpeedHackCheck::CoolDownInfo *)operator new((tagHeader *)0x14);
  if ( v13 )
  {
    v0 = (CStreamOverlappedFactory *)operator new((tagHeader *)0x28);
    if ( v0 )
    {
      CStreamOverlappedFactory::CStreamOverlappedFactory(v0);
      v2 = v1;
    }
    else
    {
      v2 = 0;
    }
    v3 = operator new((tagHeader *)0x18);
    if ( v3 )
    {
      *v3 = &CPoolDispatchFactory<CChatDispatch>::`vftable';
      v3[1] = 0;
      v3[2] = 0;
      v3[3] = 0;
      v3[4] = 80;
      v3[5] = 32;
      v4 = (unsigned int)v3;
    }
    else
    {
      v4 = 0;
    }
    v5 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
    if ( v5 )
    {
      CPoolBufferFactory::CPoolBufferFactory(v5);
      v7 = v6;
    }
    else
    {
      v7 = 0;
    }
    v8 = (CTCPFactory *)operator new((tagHeader *)0x14);
    if ( v8 )
      CTCPFactory::CTCPFactory(v8);
    else
      v9 = 0;
    CSpeedHackCheck::CoolDownInfo::CoolDownInfo(v13, v9, v7, v4, v2);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  if ( !v11 )
    return 0;
  if ( !v11->m_lpBufferFactory || !v11->m_lpSocketFactory || !v11->m_lpDispatchFactory || !v11->m_lpOverlappedFactory )
  {
    CSessionPolicy::~CSessionPolicy(v11);
    operator delete(v11);
    return 0;
  }
  return v11;
}
// 4058AB: variable 'v1' is possibly undefined
// 405904: variable 'v6' is possibly undefined
// 40593C: variable 'v9' is possibly undefined
// 405942: variable 'v10' is possibly undefined
// 4FFC34: using guessed type void *CPoolDispatchFactory<CChatDispatch>::`vftable';

//----- (004059A0) --------------------------------------------------------
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CLogDispatch>,CStreamOverlappedFactory>()
{
  CStreamOverlappedFactory *v0; // eax
  unsigned int v1; // eax
  unsigned int v2; // ebp
  _DWORD *v3; // eax
  unsigned int v4; // edi
  CPoolBufferFactory *v5; // eax
  AtType v6; // eax
  AtType v7; // esi
  CTCPFactory *v8; // eax
  const Skill::ProtoType *v9; // eax
  CSessionPolicy *v10; // eax
  CSessionPolicy *v11; // esi
  CSpeedHackCheck::CoolDownInfo *v13; // [esp+8h] [ebp-14h]

  v13 = (CSpeedHackCheck::CoolDownInfo *)operator new((tagHeader *)0x14);
  if ( v13 )
  {
    v0 = (CStreamOverlappedFactory *)operator new((tagHeader *)0x28);
    if ( v0 )
    {
      CStreamOverlappedFactory::CStreamOverlappedFactory(v0);
      v2 = v1;
    }
    else
    {
      v2 = 0;
    }
    v3 = operator new((tagHeader *)0x18);
    if ( v3 )
    {
      *v3 = &CPoolDispatchFactory<CLogDispatch>::`vftable';
      v3[1] = 0;
      v3[2] = 0;
      v3[3] = 0;
      v3[4] = 80;
      v3[5] = 32;
      v4 = (unsigned int)v3;
    }
    else
    {
      v4 = 0;
    }
    v5 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
    if ( v5 )
    {
      CPoolBufferFactory::CPoolBufferFactory(v5);
      v7 = v6;
    }
    else
    {
      v7 = 0;
    }
    v8 = (CTCPFactory *)operator new((tagHeader *)0x14);
    if ( v8 )
      CTCPFactory::CTCPFactory(v8);
    else
      v9 = 0;
    CSpeedHackCheck::CoolDownInfo::CoolDownInfo(v13, v9, v7, v4, v2);
    v11 = v10;
  }
  else
  {
    v11 = 0;
  }
  if ( !v11 )
    return 0;
  if ( !v11->m_lpBufferFactory || !v11->m_lpSocketFactory || !v11->m_lpDispatchFactory || !v11->m_lpOverlappedFactory )
  {
    CSessionPolicy::~CSessionPolicy(v11);
    operator delete(v11);
    return 0;
  }
  return v11;
}
// 4059FB: variable 'v1' is possibly undefined
// 405A54: variable 'v6' is possibly undefined
// 405A8C: variable 'v9' is possibly undefined
// 405A92: variable 'v10' is possibly undefined
// 4FFC28: using guessed type void *CPoolDispatchFactory<CLogDispatch>::`vftable';

//----- (00405AF0) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::~CPoolDispatchFactory<CFieldGameClientDispatch>(
        CPoolDispatchFactory<CFieldGameClientDispatch> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  this->__vftable = (CPoolDispatchFactory<CFieldGameClientDispatch>_vtbl *)&CPoolDispatchFactory<CFieldGameClientDispatch>::`vftable';
  ptr = this->m_DispatchPool.list.ptr;
  sz = this->m_DispatchPool.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->m_DispatchPool.list.ptr = 0;
    this->m_DispatchPool.first = 0;
  }
  this->__vftable = (CPoolDispatchFactory<CFieldGameClientDispatch>_vtbl *)&CDispatchFactory::`vftable';
}
// 4FFC14: using guessed type void *CPoolDispatchFactory<CFieldGameClientDispatch>::`vftable';
// 4FFC40: using guessed type void *CDispatchFactory::`vftable';

//----- (00405B30) --------------------------------------------------------
CGameClientDispatch *__thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::CreateDispatch(
        CPoolDispatchFactory<CFieldGameClientDispatch> *this,
        CSession *Session)
{
  CGameClientDispatch *first; // eax
  boost::pool<boost::default_user_allocator_new_delete> *p_m_DispatchPool; // ecx
  CGameClientDispatch *v4; // esi
  CFieldGameClientDispatchTable *Instance; // eax

  first = (CGameClientDispatch *)this->m_DispatchPool.first;
  p_m_DispatchPool = &this->m_DispatchPool;
  if ( first )
    p_m_DispatchPool->first = first->__vftable;
  else
    first = (CGameClientDispatch *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_DispatchPool);
  v4 = first;
  if ( !first )
    return 0;
  Instance = CFieldGameClientDispatchTable::GetInstance();
  CGameClientDispatch::CGameClientDispatch(v4, Session, Instance);
  v4->__vftable = (CGameClientDispatch_vtbl *)&CFieldGameClientDispatch::`vftable';
  return v4;
}
// 4FF89C: using guessed type void *CFieldGameClientDispatch::`vftable';

//----- (00405BC0) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CRegularAgentDispatch>::~CPoolDispatchFactory<CRegularAgentDispatch>(
        CPoolDispatchFactory<CRegularAgentDispatch> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  this->__vftable = (CPoolDispatchFactory<CRegularAgentDispatch>_vtbl *)&CPoolDispatchFactory<CRegularAgentDispatch>::`vftable';
  ptr = this->m_DispatchPool.list.ptr;
  sz = this->m_DispatchPool.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->m_DispatchPool.list.ptr = 0;
    this->m_DispatchPool.first = 0;
  }
  this->__vftable = (CPoolDispatchFactory<CRegularAgentDispatch>_vtbl *)&CDispatchFactory::`vftable';
}
// 4FFC08: using guessed type void *CPoolDispatchFactory<CRegularAgentDispatch>::`vftable';
// 4FFC40: using guessed type void *CDispatchFactory::`vftable';

//----- (00405C00) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CRegularAgentDispatch>::CreateDispatch(
        CPoolDispatchFactory<CRegularAgentDispatch> *this,
        CSession *Session)
{
  boost::pool<boost::default_user_allocator_new_delete> *p_m_DispatchPool; // ecx
  CRegularAgentDispatch *first; // eax

  p_m_DispatchPool = &this->m_DispatchPool;
  first = (CRegularAgentDispatch *)p_m_DispatchPool->first;
  if ( p_m_DispatchPool->first )
    p_m_DispatchPool->first = first->__vftable;
  else
    first = (CRegularAgentDispatch *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_DispatchPool);
  if ( first )
    CRegularAgentDispatch::CRegularAgentDispatch(first, Session);
}

//----- (00405C70) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CDBAgentDispatch>::~CPoolDispatchFactory<CDBAgentDispatch>(
        CPoolDispatchFactory<CDBAgentDispatch> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  this->__vftable = (CPoolDispatchFactory<CDBAgentDispatch>_vtbl *)&CPoolDispatchFactory<CDBAgentDispatch>::`vftable';
  ptr = this->m_DispatchPool.list.ptr;
  sz = this->m_DispatchPool.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->m_DispatchPool.list.ptr = 0;
    this->m_DispatchPool.first = 0;
  }
  this->__vftable = (CPoolDispatchFactory<CDBAgentDispatch>_vtbl *)&CDispatchFactory::`vftable';
}
// 4FFC40: using guessed type void *CDispatchFactory::`vftable';
// 4FFC4C: using guessed type void *CPoolDispatchFactory<CDBAgentDispatch>::`vftable';

//----- (00405CB0) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CDBAgentDispatch>::CreateDispatch(
        CPoolDispatchFactory<CDBAgentDispatch> *this,
        CSession *Session)
{
  boost::pool<boost::default_user_allocator_new_delete> *p_m_DispatchPool; // ecx
  CDBAgentDispatch *first; // eax

  p_m_DispatchPool = &this->m_DispatchPool;
  first = (CDBAgentDispatch *)p_m_DispatchPool->first;
  if ( p_m_DispatchPool->first )
    p_m_DispatchPool->first = first->__vftable;
  else
    first = (CDBAgentDispatch *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_DispatchPool);
  if ( first )
    CDBAgentDispatch::CDBAgentDispatch(first, Session);
}

//----- (00405D20) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CChatDispatch>::~CPoolDispatchFactory<CChatDispatch>(
        CPoolDispatchFactory<CChatDispatch> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  this->__vftable = (CPoolDispatchFactory<CChatDispatch>_vtbl *)&CPoolDispatchFactory<CChatDispatch>::`vftable';
  ptr = this->m_DispatchPool.list.ptr;
  sz = this->m_DispatchPool.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->m_DispatchPool.list.ptr = 0;
    this->m_DispatchPool.first = 0;
  }
  this->__vftable = (CPoolDispatchFactory<CChatDispatch>_vtbl *)&CDispatchFactory::`vftable';
}
// 4FFC34: using guessed type void *CPoolDispatchFactory<CChatDispatch>::`vftable';
// 4FFC40: using guessed type void *CDispatchFactory::`vftable';

//----- (00405D60) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CChatDispatch>::CreateDispatch(
        CPoolDispatchFactory<CChatDispatch> *this,
        CSession *Session)
{
  boost::pool<boost::default_user_allocator_new_delete> *p_m_DispatchPool; // ecx
  CChatDispatch *first; // eax

  p_m_DispatchPool = &this->m_DispatchPool;
  first = (CChatDispatch *)p_m_DispatchPool->first;
  if ( p_m_DispatchPool->first )
    p_m_DispatchPool->first = first->__vftable;
  else
    first = (CChatDispatch *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_DispatchPool);
  if ( first )
    CChatDispatch::CChatDispatch(first, Session);
}

//----- (00405DD0) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CLogDispatch>::~CPoolDispatchFactory<CLogDispatch>(
        CPoolDispatchFactory<CLogDispatch> *this)
{
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v4; // esi
  unsigned int v5; // edi

  this->__vftable = (CPoolDispatchFactory<CLogDispatch>_vtbl *)&CPoolDispatchFactory<CLogDispatch>::`vftable';
  ptr = this->m_DispatchPool.list.ptr;
  sz = this->m_DispatchPool.list.sz;
  if ( ptr )
  {
    do
    {
      v4 = *(char **)&ptr[sz - 8];
      v5 = *(_DWORD *)&ptr[sz - 4];
      operator delete[](ptr);
      ptr = v4;
      sz = v5;
    }
    while ( v4 );
    this->m_DispatchPool.list.ptr = 0;
    this->m_DispatchPool.first = 0;
  }
  this->__vftable = (CPoolDispatchFactory<CLogDispatch>_vtbl *)&CDispatchFactory::`vftable';
}
// 4FFC28: using guessed type void *CPoolDispatchFactory<CLogDispatch>::`vftable';
// 4FFC40: using guessed type void *CDispatchFactory::`vftable';

//----- (00405E10) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CLogDispatch>::CreateDispatch(
        CPoolDispatchFactory<CLogDispatch> *this,
        CSession *Session)
{
  boost::pool<boost::default_user_allocator_new_delete> *p_m_DispatchPool; // ecx
  CLogDispatch *first; // eax

  p_m_DispatchPool = &this->m_DispatchPool;
  first = (CLogDispatch *)p_m_DispatchPool->first;
  if ( p_m_DispatchPool->first )
    p_m_DispatchPool->first = first->__vftable;
  else
    first = (CLogDispatch *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_DispatchPool);
  if ( first )
    CLogDispatch::CLogDispatch(first, Session);
}

//----- (00405E80) --------------------------------------------------------
void __thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::DeleteDispatch(
        CPoolDispatchFactory<CFieldGameClientDispatch> *this,
        CPacketDispatch *lpDispatch)
{
  ((void (__thiscall *)(CPacketDispatch *, _DWORD))lpDispatch->~CPacketDispatch)(lpDispatch, 0);
  lpDispatch->__vftable = (CPacketDispatch_vtbl *)this->m_DispatchPool.first;
  this->m_DispatchPool.first = lpDispatch;
}

//----- (00405EA0) --------------------------------------------------------
CPoolDispatchFactory<CFieldGameClientDispatch> *__thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::`vector deleting destructor'(
        CPoolDispatchFactory<CFieldGameClientDispatch> *this,
        char a2)
{
  CPoolDispatchFactory<CFieldGameClientDispatch>::~CPoolDispatchFactory<CFieldGameClientDispatch>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00405EC0) --------------------------------------------------------
CPoolDispatchFactory<CRegularAgentDispatch> *__thiscall CPoolDispatchFactory<CRegularAgentDispatch>::`vector deleting destructor'(
        CPoolDispatchFactory<CRegularAgentDispatch> *this,
        char a2)
{
  CPoolDispatchFactory<CRegularAgentDispatch>::~CPoolDispatchFactory<CRegularAgentDispatch>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00405EE0) --------------------------------------------------------
CPoolDispatchFactory<CDBAgentDispatch> *__thiscall CPoolDispatchFactory<CDBAgentDispatch>::`scalar deleting destructor'(
        CPoolDispatchFactory<CDBAgentDispatch> *this,
        char a2)
{
  CPoolDispatchFactory<CDBAgentDispatch>::~CPoolDispatchFactory<CDBAgentDispatch>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00405F00) --------------------------------------------------------
CPoolDispatchFactory<CChatDispatch> *__thiscall CPoolDispatchFactory<CChatDispatch>::`vector deleting destructor'(
        CPoolDispatchFactory<CChatDispatch> *this,
        char a2)
{
  CPoolDispatchFactory<CChatDispatch>::~CPoolDispatchFactory<CChatDispatch>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00405F20) --------------------------------------------------------
CPoolDispatchFactory<CLogDispatch> *__thiscall CPoolDispatchFactory<CLogDispatch>::`vector deleting destructor'(
        CPoolDispatchFactory<CLogDispatch> *this,
        char a2)
{
  CPoolDispatchFactory<CLogDispatch>::~CPoolDispatchFactory<CLogDispatch>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00405F40) --------------------------------------------------------
void **__thiscall boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(
        boost::pool<boost::default_user_allocator_new_delete> *this)
{
  unsigned int requested_size; // edi
  unsigned int v3; // ecx
  unsigned int v4; // eax
  int v5; // edx
  unsigned int v6; // esi
  unsigned int v7; // esi
  unsigned int v8; // edi
  void **result; // eax
  void **v10; // ecx
  void **v11; // eax
  void **i; // edx

  requested_size = this->requested_size;
  v3 = 4;
  v4 = requested_size;
  do
  {
    v5 = v4 % v3;
    v6 = v3;
    v4 = v3;
    v3 = v5;
  }
  while ( v5 );
  v7 = 4 * (requested_size / v6);
  v8 = v7 * this->next_size + 8;
  result = (void **)operator new[](v8, &std::nothrow);
  v10 = result;
  if ( result )
  {
    this->next_size *= 2;
    v11 = (void **)((char *)result + v7 * ((v8 - v7 - 8) / v7));
    *v11 = this->first;
    if ( v11 != v10 )
    {
      for ( i = &v11[v7 / 0xFFFFFFFC]; i != v10; i = (void **)((char *)i - v7) )
      {
        *i = v11;
        v11 = i;
      }
      *v10 = v11;
    }
    this->first = v10;
    *(void **)((char *)v10 + v8 - 8) = this->list.ptr;
    *(void **)((char *)v10 + v8 - 4) = (void *)this->list.sz;
    result = (void **)this->first;
    this->list.sz = v8;
    this->list.ptr = (char *)v10;
    this->first = *result;
  }
  return result;
}

//----- (00405FE0) --------------------------------------------------------
void __thiscall boost::simple_segregated_storage<unsigned int>::add_block(
        boost::simple_segregated_storage<unsigned int> *this,
        boost::simple_segregated_storage<unsigned int> *block,
        unsigned int nsz,
        unsigned int npartition_sz)
{
  boost::simple_segregated_storage<unsigned int> *v4; // eax
  boost::simple_segregated_storage<unsigned int> **i; // edx

  v4 = (boost::simple_segregated_storage<unsigned int> *)((char *)block
                                                        + npartition_sz * ((nsz - npartition_sz) / npartition_sz));
  v4->first = this->first;
  if ( v4 != block )
  {
    for ( i = (boost::simple_segregated_storage<unsigned int> **)((char *)v4 - npartition_sz);
          i != (boost::simple_segregated_storage<unsigned int> **)block;
          i = (boost::simple_segregated_storage<unsigned int> **)((char *)i - npartition_sz) )
    {
      *i = v4;
      v4 = (boost::simple_segregated_storage<unsigned int> *)i;
    }
    block->first = v4;
  }
  this->first = block;
}

//----- (00406030) --------------------------------------------------------
char __thiscall CCMDStatClear::DoProcess(CCMDStatClear *this)
{
  CPacketStatistics *Instance; // eax

  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::Clear(Instance);
  return 1;
}

//----- (00406040) --------------------------------------------------------
char __thiscall CCMDStatLog::DoProcess(CCMDStatLog *this)
{
  CPacketStatistics *Instance; // eax

  Instance = CPacketStatistics::GetInstance();
  CPacketStatistics::Log(Instance);
  return 1;
}

//----- (00406050) --------------------------------------------------------
char __thiscall CCMDStartServer::DoProcess(CCMDStartServer *this)
{
  CRylGameServer *Instance; // eax
  CRylGameServer *v2; // ecx

  Instance = CRylGameServer::GetInstance((CRylGameServer *)this);
  CRylGameServer::StartServer(v2, Instance);
  return 1;
}
// 406058: variable 'v2' is possibly undefined

//----- (00406070) --------------------------------------------------------
char __thiscall CCMDConnect::DoProcess(CCMDConnect *this)
{
  CRylGameServer *Instance; // eax
  CRylGameServer *v2; // ecx
  CRylGameServer *v3; // ecx
  CRylGameServer *v4; // ecx
  CRylGameServer *v5; // eax
  CRylGameServer *v6; // ecx

  Instance = CRylGameServer::GetInstance((CRylGameServer *)this);
  CRylGameServer::ConnectToAgent(v2, Instance);
  CRylGameServer::GetInstance(v3);
  v5 = CRylGameServer::GetInstance(v4);
  CRylGameServer::ConnectToChatServer(v6, v5);
  return 1;
}
// 406076: variable 'v2' is possibly undefined
// 40607B: variable 'v3' is possibly undefined
// 406080: variable 'v4' is possibly undefined
// 406086: variable 'v6' is possibly undefined

//----- (00406090) --------------------------------------------------------
char __thiscall CCMDShowStatistics::DoProcess(CCMDShowStatistics *this)
{
  CRylGameServer *Instance; // edi
  CRylGameServer *v2; // ecx
  CRylGameServer *v3; // ecx

  Instance = CRylGameServer::GetInstance((CRylGameServer *)this);
  CCreatureManager::GetInstance();
  CRylGameServer::PrintStatistics(v2, Instance);
  CRylGameServer::PrintServerInfo(v3, Instance);
  return 1;
}
// 40609D: variable 'v2' is possibly undefined
// 4060A3: variable 'v3' is possibly undefined

//----- (004060B0) --------------------------------------------------------
char __thiscall CCMDPrintLog::DoProcess(CCMDPrintLog *this)
{
  CPerformanceCheck *Instance; // eax
  CCellManager *v2; // eax

  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::PrintAllTime(Instance, "RylGameServer", 1);
  v2 = CCellManager::GetInstance();
  CCellManager::CheckCellStatus(v2);
  return 1;
}

//----- (004060E0) --------------------------------------------------------
char __thiscall CCMDFlushLog::DoProcess(CCMDFlushLog *this)
{
  CGameLog *Instance; // eax

  Instance = CGameLog::GetInstance();
  CGameLog::Flush(Instance);
  CServerLog::DetailLog(&g_Log, LOG_SYSERR, "CCMDFlushLog::DoProcess", aDWorkRylSource_64, 95, "Flush log.");
  CServerLog::DetailLog(&g_SessionLog, LOG_SYSERR, "CCMDFlushLog::DoProcess", aDWorkRylSource_64, 96, "Flush log.");
  return 1;
}

//----- (00406130) --------------------------------------------------------
char __thiscall CCMDReloadSetup::DoProcess(CCMDReloadSetup *this)
{
  CRylGameServer *Instance; // eax
  CRylGameServer *v2; // ecx

  Instance = CRylGameServer::GetInstance((CRylGameServer *)this);
  CRylGameServer::ReloadSetup(v2, Instance);
  return 1;
}
// 406136: variable 'v2' is possibly undefined

//----- (00406140) --------------------------------------------------------
char __thiscall CCMDDummyCharacters::DoProcess(CCMDDummyCharacters *this)
{
  CDummyCharacterList *Instance; // eax
  CDummyCharacterList *v2; // ecx

  Instance = CDummyCharacterList::GetInstance((CDummyCharacterList *)this);
  CDummyCharacterList::Initialize(v2, Instance);
  return 1;
}
// 406146: variable 'v2' is possibly undefined

//----- (00406150) --------------------------------------------------------
char __thiscall CCMDClearDummyCharacters::DoProcess(CCMDClearDummyCharacters *this)
{
  CDummyCharacterList *Instance; // eax
  CDummyCharacterList *v2; // ecx

  Instance = CDummyCharacterList::GetInstance((CDummyCharacterList *)this);
  CDummyCharacterList::Destroy(v2, Instance);
  return 1;
}
// 406156: variable 'v2' is possibly undefined

//----- (00406160) --------------------------------------------------------
char __usercall CRylGameServer::InitializeCommand@<al>(CRylGameServer *this@<ecx>, int a2@<esi>)
{
  CConsoleCommand *v2; // eax
  char *v3; // eax
  CConsoleCommand *v4; // eax
  CConsoleCommand *v5; // eax
  CConsoleCommand *v6; // eax
  CConsoleCommand *v7; // eax
  CConsoleCommand *v8; // eax
  CConsoleCommand *v9; // eax
  CConsoleCommand *v10; // eax
  CConsoleCommand *v11; // eax
  CConsoleCommand *v12; // eax
  CConsoleCommand *v13; // eax
  CConsoleCommand *v14; // eax
  CConsoleCommand *v15; // eax
  CConsoleCommand *v16; // eax
  CConsoleCommand *v17; // eax

  v2 = (CConsoleCommand *)operator new((tagHeader *)4);
  if ( v2 )
    v2->__vftable = (CConsoleCommand_vtbl *)&CCMDStartServer::`vftable';
  else
    v2 = 0;
  if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "startserver", v2)
    || (v3 = "Command create failed - startserver") == 0 )
  {
    v4 = (CConsoleCommand *)operator new((tagHeader *)4);
    if ( v4 )
      v4->__vftable = (CConsoleCommand_vtbl *)&CCMDConnect::`vftable';
    else
      v4 = 0;
    if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "connect", v4)
      || (v3 = "Command create failed - connect") == 0 )
    {
      v5 = (CConsoleCommand *)operator new((tagHeader *)4);
      if ( v5 )
        v5->__vftable = (CConsoleCommand_vtbl *)&CCMDShowStatistics::`vftable';
      else
        v5 = 0;
      if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "pool", v5)
        || (v3 = "Command create failed - pool") == 0 )
      {
        v6 = (CConsoleCommand *)operator new((tagHeader *)4);
        if ( v6 )
          v6->__vftable = (CConsoleCommand_vtbl *)&CCMDPrintLog::`vftable';
        else
          v6 = 0;
        if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "log", v6)
          || (v3 = "Command create failed - log") == 0 )
        {
          v7 = (CConsoleCommand *)operator new((tagHeader *)4);
          if ( v7 )
            v7->__vftable = (CConsoleCommand_vtbl *)&CCMDReloadSetup::`vftable';
          else
            v7 = 0;
          if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "reloadsetup", v7)
            || (v3 = "Command create failed - reloadsetup") == 0 )
          {
            v8 = (CConsoleCommand *)operator new((tagHeader *)4);
            if ( v8 )
              v8->__vftable = (CConsoleCommand_vtbl *)&CCMDFlushLog::`vftable';
            else
              v8 = 0;
            if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "flush", v8)
              || (v3 = "Command create failed - flush") == 0 )
            {
              v9 = (CConsoleCommand *)operator new((tagHeader *)0x18);
              if ( v9 )
                v9->__vftable = (CConsoleCommand_vtbl *)&CCMDDropItem::`vftable';
              else
                v9 = 0;
              if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "itemdrop", v9)
                || (v3 = "Command create failed - itemdrop") == 0 )
              {
                v10 = (CConsoleCommand *)operator new((tagHeader *)0x98);
                if ( v10 )
                  v10->__vftable = (CConsoleCommand_vtbl *)&CCMDDropItemList::`vftable';
                else
                  v10 = 0;
                if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "itemdroplist", v10)
                  || (v3 = "Command create failed - itemdroplist") == 0 )
                {
                  v11 = (CConsoleCommand *)operator new((tagHeader *)0x7D8);
                  if ( v11 )
                  {
                    v11->__vftable = (CConsoleCommand_vtbl *)&CCMDNotify::`vftable';
                    v11[501].__vftable = 0;
                    LOBYTE(v11[1].__vftable) = 0;
                  }
                  else
                  {
                    v11 = 0;
                  }
                  if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "notify", v11)
                    || (v3 = "Command create failed - notify") == 0 )
                  {
                    v12 = (CConsoleCommand *)operator new((tagHeader *)8);
                    if ( v12 )
                      v12->__vftable = (CConsoleCommand_vtbl *)&CCMDAutoBalance::`vftable';
                    else
                      v12 = 0;
                    if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "autobalance", v12)
                      || (v3 = "Command create failed - autobalance") == 0 )
                    {
                      v13 = (CConsoleCommand *)operator new((tagHeader *)8);
                      if ( v13 )
                        v13->__vftable = (CConsoleCommand_vtbl *)&CCMDLotteryEvent::`vftable';
                      else
                        v13 = 0;
                      if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "lotteryevent", v13)
                        || (v3 = "Command create failed - lotteryevent") == 0 )
                      {
                        v14 = (CConsoleCommand *)operator new((tagHeader *)4);
                        if ( v14 )
                          v14->__vftable = (CConsoleCommand_vtbl *)&CCMDStatClear::`vftable';
                        else
                          v14 = 0;
                        if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "statclear", v14)
                          || (v3 = "Command create failed - statclear") == 0 )
                        {
                          v15 = (CConsoleCommand *)operator new((tagHeader *)4);
                          if ( v15 )
                            v15->__vftable = (CConsoleCommand_vtbl *)&CCMDStatLog::`vftable';
                          else
                            v15 = 0;
                          if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "statlog", v15)
                            || (v3 = "Command create failed - statlog") == 0 )
                          {
                            v16 = (CConsoleCommand *)operator new((tagHeader *)4);
                            if ( v16 )
                              v16->__vftable = (CConsoleCommand_vtbl *)&CCMDDummyCharacters::`vftable';
                            else
                              v16 = 0;
                            if ( CConsoleCMDFactory::AddCommand(*(CConsoleCMDFactory **)(a2 + 36), "setdummies", v16)
                              || (v3 = "Command create failed - setdummies") == 0 )
                            {
                              v17 = (CConsoleCommand *)operator new((tagHeader *)4);
                              if ( v17 )
                                v17->__vftable = (CConsoleCommand_vtbl *)&CCMDClearDummyCharacters::`vftable';
                              else
                                v17 = 0;
                              if ( CConsoleCMDFactory::AddCommand(
                                     *(CConsoleCMDFactory **)(a2 + 36),
                                     "resetdummies",
                                     v17) )
                              {
                                return 1;
                              }
                              v3 = "Command create failed - resetdummies";
                              if ( !"Command create failed - resetdummies" )
                                return 1;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CRylGameServer::InitializeCommand", aDWorkRylSource_64, 165, v3);
  return 0;
}
// 4FF3AC: using guessed type void *CCMDAutoBalance::`vftable';
// 4FF444: using guessed type void *CCMDDropItem::`vftable';
// 4FF514: using guessed type void *CCMDDropItemList::`vftable';
// 4FF8C8: using guessed type void *CCMDLotteryEvent::`vftable';
// 4FFBE8: using guessed type void *CCMDNotify::`vftable';
// 5003BC: using guessed type void *CCMDStatClear::`vftable';
// 5003C8: using guessed type void *CCMDClearDummyCharacters::`vftable';
// 5003D4: using guessed type void *CCMDDummyCharacters::`vftable';
// 5003E0: using guessed type void *CCMDShowStatistics::`vftable';
// 5003EC: using guessed type void *CCMDReloadSetup::`vftable';
// 5003F8: using guessed type void *CCMDConnect::`vftable';
// 500404: using guessed type void *CCMDFlushLog::`vftable';
// 500410: using guessed type void *CCMDStartServer::`vftable';
// 50041C: using guessed type void *CCMDPrintLog::`vftable';
// 500428: using guessed type void *CCMDStatLog::`vftable';

//----- (004064F0) --------------------------------------------------------
CCMDStatClear *__thiscall CConsoleCMDSingleton<CCMDStatClear>::Clone(
        CConsoleCMDSingleton<CCMDStatClear> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::Instance.__vftable = (CCMDStatClear_vtbl *)&CCMDStatClear::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::Instance;
}
// 5003BC: using guessed type void *CCMDStatClear::`vftable';
// 53E204: using guessed type int `CConsoleCMDSingleton<CCMDStatClear>::Clone'::`2'::`local static guard';

//----- (00406520) --------------------------------------------------------
CCMDStatLog *__thiscall CConsoleCMDSingleton<CCMDStatLog>::Clone(
        CConsoleCMDSingleton<CCMDStatLog> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::Instance.__vftable = (CCMDStatLog_vtbl *)&CCMDStatLog::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::Instance;
}
// 500428: using guessed type void *CCMDStatLog::`vftable';
// 53E1FC: using guessed type int `CConsoleCMDSingleton<CCMDStatLog>::Clone'::`2'::`local static guard';

//----- (00406550) --------------------------------------------------------
CCMDStartServer *__thiscall CConsoleCMDSingleton<CCMDStartServer>::Clone(
        CConsoleCMDSingleton<CCMDStartServer> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::Instance.__vftable = (CCMDStartServer_vtbl *)&CCMDStartServer::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::Instance;
}
// 500410: using guessed type void *CCMDStartServer::`vftable';
// 53E1F4: using guessed type int `CConsoleCMDSingleton<CCMDStartServer>::Clone'::`2'::`local static guard';

//----- (00406580) --------------------------------------------------------
CCMDConnect *__thiscall CConsoleCMDSingleton<CCMDConnect>::Clone(
        CConsoleCMDSingleton<CCMDConnect> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::Instance.__vftable = (CCMDConnect_vtbl *)&CCMDConnect::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::Instance;
}
// 5003F8: using guessed type void *CCMDConnect::`vftable';
// 53E1EC: using guessed type int `CConsoleCMDSingleton<CCMDConnect>::Clone'::`2'::`local static guard';

//----- (004065B0) --------------------------------------------------------
CCMDShowStatistics *__thiscall CConsoleCMDSingleton<CCMDShowStatistics>::Clone(
        CConsoleCMDSingleton<CCMDShowStatistics> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::Instance.__vftable = (CCMDShowStatistics_vtbl *)&CCMDShowStatistics::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::Instance;
}
// 5003E0: using guessed type void *CCMDShowStatistics::`vftable';
// 53E1E4: using guessed type int `CConsoleCMDSingleton<CCMDShowStatistics>::Clone'::`2'::`local static guard';

//----- (004065E0) --------------------------------------------------------
CCMDPrintLog *__thiscall CConsoleCMDSingleton<CCMDPrintLog>::Clone(
        CConsoleCMDSingleton<CCMDPrintLog> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::Instance.__vftable = (CCMDPrintLog_vtbl *)&CCMDPrintLog::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::Instance;
}
// 50041C: using guessed type void *CCMDPrintLog::`vftable';
// 53E1DC: using guessed type int `CConsoleCMDSingleton<CCMDPrintLog>::Clone'::`2'::`local static guard';

//----- (00406610) --------------------------------------------------------
CCMDFlushLog *__thiscall CConsoleCMDSingleton<CCMDFlushLog>::Clone(
        CConsoleCMDSingleton<CCMDFlushLog> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::Instance.__vftable = (CCMDFlushLog_vtbl *)&CCMDFlushLog::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::Instance;
}
// 500404: using guessed type void *CCMDFlushLog::`vftable';
// 53E1D4: using guessed type int `CConsoleCMDSingleton<CCMDFlushLog>::Clone'::`2'::`local static guard';

//----- (00406640) --------------------------------------------------------
CCMDReloadSetup *__thiscall CConsoleCMDSingleton<CCMDReloadSetup>::Clone(
        CConsoleCMDSingleton<CCMDReloadSetup> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::Instance.__vftable = (CCMDReloadSetup_vtbl *)&CCMDReloadSetup::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::Instance;
}
// 5003EC: using guessed type void *CCMDReloadSetup::`vftable';
// 53E1CC: using guessed type int `CConsoleCMDSingleton<CCMDReloadSetup>::Clone'::`2'::`local static guard';

//----- (00406670) --------------------------------------------------------
CCMDDummyCharacters *__thiscall CConsoleCMDSingleton<CCMDDummyCharacters>::Clone(
        CConsoleCMDSingleton<CCMDDummyCharacters> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::Instance.__vftable = (CCMDDummyCharacters_vtbl *)&CCMDDummyCharacters::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::Instance;
}
// 5003D4: using guessed type void *CCMDDummyCharacters::`vftable';
// 53E1C4: using guessed type int `CConsoleCMDSingleton<CCMDDummyCharacters>::Clone'::`2'::`local static guard';

//----- (004066A0) --------------------------------------------------------
CCMDClearDummyCharacters *__thiscall CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone(
        CConsoleCMDSingleton<CCMDClearDummyCharacters> *this,
        const char *szCommand,
        unsigned int nCommandLength)
{
  if ( (`CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::`local static guard' & 1) == 0 )
  {
    `CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::`local static guard' |= 1u;
    `CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::Instance.__vftable = (CCMDClearDummyCharacters_vtbl *)&CCMDClearDummyCharacters::`vftable';
  }
  return &`CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::Instance;
}
// 5003C8: using guessed type void *CCMDClearDummyCharacters::`vftable';
// 53E1BC: using guessed type int `CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone'::`2'::`local static guard';

//----- (004066D0) --------------------------------------------------------
void __cdecl __noreturn report_failure()
{
  CServerLog::DetailLog(
    &g_Log,
    LOG_SYSERR,
    "report_failure",
    aDWorkRylSource_76,
    16,
    "Stack buffer overrun occured. shutdown server.");
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_SYSERR,
    "report_failure",
    aDWorkRylSource_76,
    17,
    "Stack buffer overrun occured. shutdown server.");
  exit(1u);
}

//----- (00406790) --------------------------------------------------------
int __stdcall ExceptionUserFunc(char *szBuffer, int nBufferSize)
{
  CGameLog *Instance; // eax

  CServerLog::DetailLog(&g_Log, LOG_SYSERR, "ExceptionUserFunc", aDWorkRylSource_76, 38, "Flush log");
  CServerLog::DetailLog(&g_SessionLog, LOG_SYSERR, "ExceptionUserFunc", aDWorkRylSource_76, 39, "Flush log");
  Instance = CGameLog::GetInstance();
  CGameLog::Flush(Instance);
  return _snprintf(szBuffer, nBufferSize, "Userdata flush completed.");
}

//----- (00406800) --------------------------------------------------------
int __stdcall WinMain(HINSTANCE__ *hInstance, HINSTANCE__ *hPrevInstance, char *lpCmdLine, int nCmdShow)
{
  char ZoneFromCmdLine; // al
  HANDLE MutexA; // esi
  CExceptionReport *Instance; // eax
  CRylGameServer *v7; // ecx
  CRylGameServer *v8; // edi
  char v9; // al
  int v11; // [esp-8h] [ebp-218h]
  int ChannelFromCmdLine; // [esp-4h] [ebp-214h]
  char szProgramName[260]; // [esp+4h] [ebp-20Ch] BYREF
  char szWindowName[260]; // [esp+108h] [ebp-108h] BYREF

  CServerSetup::GetInstance();
  CServerSetup::GetInstance();
  ChannelFromCmdLine = CServerSetup::GetChannelFromCmdLine();
  ZoneFromCmdLine = CServerSetup::GetZoneFromCmdLine();
  _snprintf(szProgramName, 0x103u, "%s%02d%02d", "RylGameServer", ZoneFromCmdLine, ChannelFromCmdLine);
  szProgramName[259] = 0;
  MutexA = CreateMutexA(0, 1, szProgramName);
  if ( GetLastError() == 183 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "WinMain",
      aDWorkRylSource_76,
      63,
      "GameServer already operating now. please shutdown and restart");
  }
  else
  {
    _set_security_error_handler((void (__cdecl *)(int, void *))report_failure);
    Instance = CExceptionReport::GetInstance();
    CExceptionReport::Enable(Instance, 7u);
    CExceptionReport::GetInstance()->m_lpUserFunc = (int (__stdcall *)(char *, const int))ExceptionUserFunc;
    CExceptionReport::GetInstance()->m_eMiniDumpType = MiniDumpWithFullMemory;
    v8 = CRylGameServer::GetInstance(v7);
    CServerSetup::GetInstance();
    CServerSetup::GetInstance();
    v11 = CServerSetup::GetChannelFromCmdLine();
    v9 = CServerSetup::GetZoneFromCmdLine();
    sprintf(szWindowName, "RylGameServer Zone #%d Channel #%d", v9, v11);
    if ( CServerWindowFramework::Initialize(v8, hInstance, szWindowName, lpCmdLine, 0x6Fu, 101) )
      CServerWindowFramework::ProcessMessage(v8);
  }
  if ( MutexA )
    CloseHandle(MutexA);
  return 0;
}
// 4068CD: variable 'v7' is possibly undefined

//----- (00406960) --------------------------------------------------------
void __thiscall CPerformanceInstrument::CPerformanceInstrument(CPerformanceInstrument *this)
{
  this->m_szfunctionName = 0;
  this->m_startTime.QuadPart = 0LL;
}

//----- (00406970) --------------------------------------------------------
void __thiscall CPerformanceInstrument::Stop(CPerformanceInstrument *this)
{
  CPerformanceCheck *Instance; // eax
  char *m_szfunctionName; // [esp-4h] [ebp-14h]
  double fEstimateTime; // [esp+0h] [ebp-10h]
  unsigned __int64 SnapShotstopTime; // [esp+8h] [ebp-8h]

  SnapShotstopTime = __rdtsc();
  this->m_stopTime.QuadPart = SnapShotstopTime;
  if ( !SnapShotstopTime )
    this->m_stopTime.QuadPart = 1LL;
  fEstimateTime = (double)(this->m_stopTime.QuadPart - this->m_startTime.QuadPart);
  m_szfunctionName = this->m_szfunctionName;
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::AddTime(Instance, m_szfunctionName, fEstimateTime);
}

//----- (004069D0) --------------------------------------------------------
void __thiscall CAutoInstrument::~CAutoInstrument(CAutoInstrument *this)
{
  CPerformanceInstrument::Stop(this->m_PerformanceInstrument);
}

//----- (004069E0) --------------------------------------------------------
char __thiscall CCharacter::DBUpdateForce(CCharacter *this, DBUpdateData::UpdateType eUpdateType)
{
  this->m_nDBUpdateCount = -1;
  return CCharacter::DBUpdate(this, UPDATE);
}

//----- (00406A00) --------------------------------------------------------
int __thiscall CProcessThread::End(CProcessThread *this)
{
  InterlockedExchange(&this->m_bExit, 1);
  return 1;
}

//----- (00406A20) --------------------------------------------------------
char __usercall FnCSAuth::operator()@<al>(CCharacter *lpCharacter@<eax>, FnCSAuth *this)
{
  CGameClientDispatch *m_lpGameClientDispatch; // esi
  unsigned int m_dwCID; // edi
  unsigned int AuthDword; // eax

  if ( !lpCharacter )
    return 1;
  m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
    return 1;
  if ( CCSAuth::IsAuth(&m_lpGameClientDispatch->m_CSAuth) || !lpCharacter->m_DBData.m_cAdminLevel )
  {
    m_dwCID = lpCharacter->m_dwCID;
    AuthDword = CCSAuth::GetAuthDword(&m_lpGameClientDispatch->m_CSAuth);
    return GameClientSendPacket::SendCSAuth(&m_lpGameClientDispatch->m_SendStream, m_dwCID, AuthDword, 0);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "FnCSAuth::operator`()'",
      aDWorkRylSource_84,
      81,
      aCid0x08x_192,
      lpCharacter->m_dwCID);
    CGameClientDispatch::Disconnect(m_lpGameClientDispatch);
    return 1;
  }
}

//----- (00406AB0) --------------------------------------------------------
char __thiscall CRylGameServer::AddGameProcessThread(CRylGameServer *this)
{
  CGameServerProcessThread *v2; // eax
  CThread *v3; // eax

  v2 = (CGameServerProcessThread *)operator new((tagHeader *)0x168);
  if ( v2 )
    CGameServerProcessThread::CGameServerProcessThread(v2);
  else
    v3 = 0;
  return CServerWindowFramework::AddProcessThread(this, v3);
}
// 406AFB: variable 'v3' is possibly undefined

//----- (00406B10) --------------------------------------------------------
void __stdcall CGameServerProcessThread::CGameServerProcessThread(CGameServerProcessThread *this)
{
  CRylGameServer *RylGameServer; // edi
  char **p_m_szfunctionName; // ecx
  unsigned int i; // eax

  CProcessThread::CProcessThread(this, RylGameServer, 100);
  this->__vftable = (CGameServerProcessThread_vtbl *)&CGameServerProcessThread::`vftable';
  this->m_RylGameServer = RylGameServer;
  this->m_CreatureManager = CCreatureManager::GetInstance();
  this->m_CellManager = CCellManager::GetInstance();
  this->m_DuelCellManager = CSingleton<CDuelCellManager>::ms_pSingleton;
  this->m_GlobalSpellMgr = CGlobalSpellMgr::GetInstance();
  this->m_GameLog = CGameLog::GetInstance();
  this->m_lpUDPWish = RylGameServer->m_lpUDPWish;
  this->m_VirtualAreaMgr = VirtualArea::CVirtualAreaMgr::GetInstance();
  this->m_CastleMgr = Castle::CCastleMgr::GetInstance();
  this->m_SiegeObjectMgr = CSiegeObjectMgr::GetInstance();
  this->m_processMonster._Pmemfun = (bool (__thiscall *)(CMonster *))CMonster::Process;
  this->m_processPrepareBroadCast._Pmemfun = CCell::PrepareBroadCast;
  this->m_processBroadCast._Pmemfun = CCell::BroadCast;
  `eh vector constructor iterator'(
    (char *)this->m_Instruments,
    0x18u,
    12,
    (void (__thiscall *)(void *))CPerformanceInstrument::CPerformanceInstrument,
    (void (__thiscall *)(void *))CSymbolTable::Create);
  p_m_szfunctionName = &this->m_Instruments[0].m_szfunctionName;
  for ( i = 0; i < 12; ++i )
  {
    *p_m_szfunctionName = g_InstrumentInfo[i].m_szName;
    p_m_szfunctionName += 6;
  }
}
// 406B2F: variable 'RylGameServer' is possibly undefined
// 500928: using guessed type void *CGameServerProcessThread::`vftable';

//----- (00406BF0) --------------------------------------------------------
CGameServerProcessThread *__thiscall CGameServerProcessThread::`scalar deleting destructor'(
        CGameServerProcessThread *this,
        char a2)
{
  CGameServerProcessThread::~CGameServerProcessThread(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00406C10) --------------------------------------------------------
void __thiscall CGameServerProcessThread::~CGameServerProcessThread(CGameServerProcessThread *this)
{
  CSingleDispatch *DispatchTable; // eax
  CIOCPNet *m_lpIOCPNet; // edi
  unsigned int v4; // ebx
  CSingleDispatch *v5; // eax
  CSingleDispatch *v6; // eax
  CSingleDispatch *v7; // eax
  bool v8; // zf
  const char *v9; // eax
  CPerformanceCheck *Instance; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // [esp-14h] [ebp-44h]
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last; // [esp+8h] [ebp-28h] BYREF
  CGameServerProcessThread *v13; // [esp+Ch] [ebp-24h]
  CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > result; // [esp+10h] [ebp-20h] BYREF
  CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > _Func; // [esp+14h] [ebp-1Ch] BYREF
  int v16; // [esp+18h] [ebp-18h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+1Ch] [ebp-14h] BYREF
  int v18; // [esp+2Ch] [ebp-4h]

  v13 = this;
  this->__vftable = (CGameServerProcessThread_vtbl *)&CGameServerProcessThread::`vftable';
  v18 = 1;
  CLimitUserByIP::OperateMode(this->m_RylGameServer->m_lpClientLimit, DENY_ALL);
  LOBYTE(_Last._Ptr) = 0;
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const,CCharacter *>>>(
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&result,
    this->m_CreatureManager->m_CharacterMap._Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_CreatureManager->m_CharacterMap._Myhead,
    (CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const ,CCharacter *> >)&_Last);
  _Func.m_fnSecondProcess = (std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *)CCharacter::DBUpdateForce;
  Myhead = this->m_CreatureManager->m_CharacterMap._Myhead;
  v16 = 1;
  std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>,std::pair<unsigned long const,CCharacter *>>>(
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead,
    (CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> >)&_Func);
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  LOBYTE(v18) = 2;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    DBAgentPacketParse::SendServerLogout((CDBAgentDispatch *)StoragelpDBAgentDispatch.m_lpDispatch);
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "CGameServerProcessThread::~CGameServerProcessThread",
      aDWorkRylSource_84,
      221,
      "this:0x%p/try client logout process",
      this);
    m_lpIOCPNet = this->m_RylGameServer->m_lpIOCPNet;
    if ( m_lpIOCPNet )
    {
      v4 = 0;
      v5 = CDBAgentDispatch::GetDispatchTable();
      if ( CSingleDispatch::GetDispatchNum(v5) )
      {
        do
        {
          if ( v4 >= 0x64 )
            break;
          CIOCPNet::Process(m_lpIOCPNet);
          Sleep(0x64u);
          ++v4;
          v6 = CDBAgentDispatch::GetDispatchTable();
        }
        while ( CSingleDispatch::GetDispatchNum(v6) );
      }
    }
    v7 = CDBAgentDispatch::GetDispatchTable();
    v8 = !CSingleDispatch::GetDispatchNum(v7);
    v9 = "Finish complete";
    if ( !v8 )
      v9 = "Timeout";
    CServerLog::DetailLog(
      &g_Log,
      LOG_SYSERR,
      "CGameServerProcessThread::~CGameServerProcessThread",
      aDWorkRylSource_84,
      243,
      "this:0x%p/client logout process finish (%s)",
      this,
      v9);
  }
  Instance = CPerformanceCheck::GetInstance();
  CPerformanceCheck::PrintAllTime(Instance, "RylGameServer", 1);
  LOBYTE(v18) = 1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  LOBYTE(v18) = 0;
  `eh vector destructor iterator'(
    (char *)this->m_Instruments,
    0x18u,
    12,
    (void (__thiscall *)(void *))CSymbolTable::Create);
  v18 = -1;
  CProcessThread::~CProcessThread(this);
}
// 500928: using guessed type void *CGameServerProcessThread::`vftable';

//----- (00406DD0) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __thiscall CGameServerProcessThread::InternalRun(CGameServerProcessThread *this, CPerformanceInstrument *pulse)
{
  unsigned int LowPart; // edi
  CCell *m_CellData; // eax
  CPulse *v5; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v6; // eax
  unsigned int v7; // ebp
  CCell *v8; // ebp
  CCell *i; // edi
  CCell *v10; // ebp
  CCell *j; // edi
  CServerSetup *Instance; // eax
  CUDPWish *HighPart; // ecx
  CRylGameServer *v14; // ecx
  CPerformanceCheck *v15; // eax
  unsigned int v16; // edi
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // [esp-8h] [ebp-4Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v21; // [esp-8h] [ebp-4Ch]
  unsigned int dwCurrentPulse; // [esp+10h] [ebp-34h]
  _LARGE_INTEGER INSTRUMENT_CELLBROADCASTING; // [esp+14h] [ebp-30h] OVERLAPPED BYREF
  _LARGE_INTEGER INSTRUMENT_REGEN_HP_MP; // [esp+1Ch] [ebp-28h] OVERLAPPED BYREF
  _LARGE_INTEGER INSTRUMENT_PROCESS_ALL_MONSTER; // [esp+24h] [ebp-20h] OVERLAPPED BYREF
  CAutoInstrument INSTRUMENT_TOTAL_LOOP; // [esp+2Ch] [ebp-18h]
  _LARGE_INTEGER _Func; // [esp+30h] [ebp-14h] BYREF
  int v28; // [esp+40h] [ebp-4h]

  LowPart = pulse->m_startTime.LowPart;
  dwCurrentPulse = pulse->m_startTime.LowPart;
  time(&this->m_GameLog->m_time);
  INSTRUMENT_TOTAL_LOOP.m_PerformanceInstrument = this->m_Instruments;
  this->m_Instruments[0].m_stopTime.LowPart = 0;
  this->m_Instruments[0].m_stopTime.HighPart = 0;
  INSTRUMENT_PROCESS_ALL_MONSTER.QuadPart = __rdtsc();
  this->m_Instruments[0].m_startTime = INSTRUMENT_PROCESS_ALL_MONSTER;
  v28 = 0;
  if ( LowPart % 0x64 == 1 )
  {
    INSTRUMENT_PROCESS_ALL_MONSTER.LowPart = (unsigned int)&this->m_Instruments[1];
    this->m_Instruments[1].m_stopTime.LowPart = 0;
    this->m_Instruments[1].m_stopTime.HighPart = 0;
    INSTRUMENT_REGEN_HP_MP.QuadPart = __rdtsc();
    this->m_Instruments[1].m_startTime = INSTRUMENT_REGEN_HP_MP;
    LOBYTE(v28) = 1;
    m_CellData = this->m_CellManager->m_CellData;
    if ( m_CellData )
    {
      v5 = (CPulse *)&m_CellData[CCell::ms_CellSize * CCell::ms_CellSize];
      pulse = (CPerformanceInstrument *)this->m_CellManager->m_CellData;
      if ( m_CellData != (CCell *)v5 )
      {
        while ( 1 )
        {
          CCell::CheckDeleteItem(m_CellData);
          pulse = (CPerformanceInstrument *)((char *)pulse + 116);
          if ( pulse == (CPerformanceInstrument *)v5 )
            break;
          m_CellData = (CCell *)pulse;
        }
      }
    }
    VirtualArea::CVirtualAreaMgr::ProcessDeleteItem(this->m_VirtualAreaMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[1]);
    LOBYTE(LowPart) = dwCurrentPulse;
  }
  if ( (LowPart & 1) == 1 )
  {
    INSTRUMENT_PROCESS_ALL_MONSTER.LowPart = (unsigned int)&this->m_Instruments[6];
    this->m_Instruments[6].m_stopTime.LowPart = 0;
    this->m_Instruments[6].m_stopTime.HighPart = 0;
    INSTRUMENT_REGEN_HP_MP.QuadPart = __rdtsc();
    this->m_Instruments[6].m_startTime = INSTRUMENT_REGEN_HP_MP;
    LOBYTE(v28) = 2;
    Myhead = this->m_CreatureManager->m_MonsterMap._Myhead;
    pulse = (CPerformanceInstrument *)this->m_processMonster._Pmemfun;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const,CMonster *>>>(
      (CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > *)&INSTRUMENT_REGEN_HP_MP,
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead,
      (CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> >)&pulse);
    CSiegeObjectMgr::ProcessAllSiegeObject(this->m_SiegeObjectMgr);
    VirtualArea::CVirtualAreaMgr::ProcessAllMonster(this->m_VirtualAreaMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[6]);
  }
  INSTRUMENT_PROCESS_ALL_MONSTER.LowPart = dwCurrentPulse % 0x32;
  if ( dwCurrentPulse % 0x32 == 2 )
  {
    INSTRUMENT_REGEN_HP_MP.LowPart = (unsigned int)&this->m_Instruments[2];
    this->m_Instruments[2].m_stopTime.LowPart = 0;
    this->m_Instruments[2].m_stopTime.HighPart = 0;
    INSTRUMENT_CELLBROADCASTING.QuadPart = __rdtsc();
    this->m_Instruments[2].m_startTime = INSTRUMENT_CELLBROADCASTING;
    LOBYTE(v28) = 3;
    v6 = this->m_CreatureManager->m_MonsterMap._Myhead;
    LOBYTE(pulse) = 0;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const,CMonster *>>>(
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&INSTRUMENT_CELLBROADCASTING,
      v6->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v6,
      (CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const ,CMonster *> >)&pulse);
    LOBYTE(pulse) = 0;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const,CMonster *>>>(
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&INSTRUMENT_CELLBROADCASTING,
      (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)this->m_CreatureManager->m_CharacterMap._Myhead->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)this->m_CreatureManager->m_CharacterMap._Myhead,
      (CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const ,CMonster *> >)&pulse);
    VirtualArea::CVirtualAreaMgr::ProcessMonsterRegenHPAndMP(this->m_VirtualAreaMgr);
    Castle::CCastleMgr::ProcessEmblemRegenHPAndMP(this->m_CastleMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[2]);
  }
  v7 = dwCurrentPulse % 0xA;
  INSTRUMENT_REGEN_HP_MP.LowPart = dwCurrentPulse % 0xA;
  if ( dwCurrentPulse % 0xA == 3 )
  {
    pulse = &this->m_Instruments[3];
    this->m_Instruments[3].m_stopTime.LowPart = 0;
    this->m_Instruments[3].m_stopTime.HighPart = 0;
    INSTRUMENT_CELLBROADCASTING.QuadPart = __rdtsc();
    this->m_Instruments[3].m_startTime = INSTRUMENT_CELLBROADCASTING;
    LOBYTE(v28) = 4;
    _Func.LowPart = (unsigned int)CCharacter::DBUpdate;
    v21 = this->m_CreatureManager->m_CharacterMap._Myhead;
    _Func.HighPart = 2;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>,std::pair<unsigned long const,CCharacter *>>>(
      (CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > *)&INSTRUMENT_CELLBROADCASTING,
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v21->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v21,
      (CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> >)&_Func);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[3]);
  }
  if ( dwCurrentPulse % 5 == 3 )
  {
    pulse = &this->m_Instruments[8];
    this->m_Instruments[8].m_stopTime.LowPart = 0;
    this->m_Instruments[8].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    this->m_Instruments[8].m_startTime = _Func;
    LOBYTE(v28) = 5;
    CCreatureManager::ProcessCharacterLogout(this->m_CreatureManager);
    CCreatureManager::ProcessSummonMonsterDead(this->m_CreatureManager);
    VirtualArea::CVirtualAreaMgr::ProcessSummonMonsterDead(this->m_VirtualAreaMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[8]);
  }
  if ( dwCurrentPulse % 0x258 == 3 )
  {
    pulse = &this->m_Instruments[10];
    this->m_Instruments[10].m_stopTime.LowPart = 0;
    this->m_Instruments[10].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    this->m_Instruments[10].m_startTime = _Func;
    LOBYTE(v28) = 6;
    CCreatureManager::ProcessBattleGround(this->m_CreatureManager);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[10]);
  }
  if ( dwCurrentPulse % 0x14 == 2 )
  {
    pulse = &this->m_Instruments[11];
    this->m_Instruments[11].m_stopTime.LowPart = 0;
    this->m_Instruments[11].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    this->m_Instruments[11].m_startTime = _Func;
    LOBYTE(v28) = 7;
    VirtualArea::CVirtualAreaMgr::ProcessAllVirtualArea(this->m_VirtualAreaMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[11]);
  }
  if ( v7 == 4 )
  {
    INSTRUMENT_CELLBROADCASTING.LowPart = (unsigned int)&this->m_Instruments[4];
    this->m_Instruments[4].m_stopTime.LowPart = 0;
    this->m_Instruments[4].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    this->m_Instruments[4].m_startTime = _Func;
    LOBYTE(v28) = 8;
    v8 = this->m_CellManager->m_CellData;
    pulse = (CPerformanceInstrument *)this->m_processPrepareBroadCast._Pmemfun;
    if ( v8 )
    {
      for ( i = &v8[CCell::ms_CellSize * CCell::ms_CellSize]; v8 != i; ++v8 )
        ((void (__thiscall *)(CCell *))pulse)(v8);
    }
    v10 = this->m_CellManager->m_CellData;
    pulse = (CPerformanceInstrument *)this->m_processBroadCast._Pmemfun;
    if ( v10 )
    {
      for ( j = &v10[CCell::ms_CellSize * CCell::ms_CellSize]; v10 != j; ++v10 )
        ((void (__thiscall *)(CCell *))pulse)(v10);
    }
    CDuelCellManager::ProcessAllCell<std::mem_fun_ref_t<void,CCell>>(
      (int)this->m_DuelCellManager,
      (void (__thiscall *)(_DWORD))this->m_processPrepareBroadCast._Pmemfun);
    CDuelCellManager::ProcessAllCell<std::mem_fun_ref_t<void,CCell>>(
      (int)this->m_DuelCellManager,
      (void (__thiscall *)(_DWORD))this->m_processBroadCast._Pmemfun);
    VirtualArea::CVirtualAreaMgr::ProcessAllCellPrepareBroadCast(this->m_VirtualAreaMgr);
    VirtualArea::CVirtualAreaMgr::ProcessAllCellBroadCast(this->m_VirtualAreaMgr);
    CSiegeObjectMgr::BroadCast(this->m_SiegeObjectMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop((CPerformanceInstrument *)INSTRUMENT_CELLBROADCASTING.LowPart);
    v7 = INSTRUMENT_REGEN_HP_MP.LowPart;
  }
  if ( !v7 )
  {
    pulse = &this->m_Instruments[5];
    this->m_Instruments[5].m_stopTime.LowPart = 0;
    this->m_Instruments[5].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    this->m_Instruments[5].m_startTime = _Func;
    LOBYTE(v28) = 9;
    CGlobalSpellMgr::Process(this->m_GlobalSpellMgr);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[5]);
  }
  if ( v7 == 7 )
  {
    Instance = CServerSetup::GetInstance();
    if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 3 )
    {
      pulse = &this->m_Instruments[9];
      this->m_Instruments[9].m_stopTime.LowPart = 0;
      this->m_Instruments[9].m_stopTime.HighPart = 0;
      _Func.QuadPart = __rdtsc();
      this->m_Instruments[9].m_startTime = _Func;
      LOBYTE(v28) = 10;
      CCreatureManager::ProcessRespawnQueue(this->m_CreatureManager);
      LOBYTE(v28) = 0;
      CPerformanceInstrument::Stop(&this->m_Instruments[9]);
    }
  }
  if ( this->m_lpUDPWish )
  {
    pulse = &this->m_Instruments[7];
    this->m_Instruments[7].m_stopTime.LowPart = 0;
    this->m_Instruments[7].m_stopTime.HighPart = 0;
    _Func.QuadPart = __rdtsc();
    HighPart = (CUDPWish *)_Func.HighPart;
    this->m_Instruments[7].m_startTime = _Func;
    LOBYTE(v28) = 11;
    CUDPWish::Process(HighPart, (int)this->m_lpUDPWish);
    LOBYTE(v28) = 0;
    CPerformanceInstrument::Stop(&this->m_Instruments[7]);
  }
  if ( !(dwCurrentPulse % 0x1E) )
  {
    CRylGameServer::PrintServerInfo((CRylGameServer *)0x1E, this->m_RylGameServer);
    CRylGameServer::PrintStatistics(v14, this->m_RylGameServer);
  }
  if ( !(dwCurrentPulse % 0x8CA0) )
  {
    v15 = CPerformanceCheck::GetInstance();
    CPerformanceCheck::PrintAllTime(v15, "RylGameServer", 1);
  }
  if ( !INSTRUMENT_PROCESS_ALL_MONSTER.LowPart )
  {
    v16 = 0;
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    if ( CSingleDispatch::GetDispatchNum(DispatchTable) )
      v16 = 8;
    v18 = CChatDispatch::GetDispatchTable();
    if ( CSingleDispatch::GetDispatchNum(v18) )
      v16 |= 0x80u;
    this->m_RylGameServer->m_dwServerStatus = v16;
  }
  if ( !(dwCurrentPulse % 0x708) && CServerSetup::GetInstance()->m_bHackCheck )
  {
    v19 = this->m_CreatureManager->m_CharacterMap._Myhead;
    LOBYTE(pulse) = 0;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const,CCharacter *>>>(
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)&INSTRUMENT_PROCESS_ALL_MONSTER,
      v19->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)v19,
      (CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const ,CCharacter *> >)&pulse);
  }
  v28 = -1;
  CPerformanceInstrument::Stop(INSTRUMENT_TOTAL_LOOP.m_PerformanceInstrument);
}
// 406DD0: variables would overlap: ^2C.8 and stkvar "INSTRUMENT_CELLBROADCASTING" ^2C.4(has user info)
// 406DD0: variables would overlap: ^34.8 and stkvar "INSTRUMENT_REGEN_HP_MP" ^34.4(has user info)
// 406DD0: variables would overlap: ^3C.8 and stkvar "INSTRUMENT_PROCESS_ALL_MONSTER" ^3C.4(has user info)

//----- (004073F0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (00407450) --------------------------------------------------------
char __userpurge CDuelCellManager::ProcessAllCell<std::mem_fun_ref_t<void,CCell>>@<al>(
        int a1@<edi>,
        void (__thiscall *a2)(_DWORD))
{
  _DWORD *v3; // eax
  _DWORD *v4; // esi
  int **v5; // eax
  int *j; // eax
  int i; // eax

  if ( !*(_DWORD *)(a1 + 8) )
    return 0;
  v3 = *(_DWORD **)(a1 + 4);
  v4 = (_DWORD *)*v3;
  if ( (_DWORD *)*v3 != v3 )
  {
    do
    {
      a2(v4[4]);
      if ( !*((_BYTE *)v4 + 21) )
      {
        v5 = (int **)v4[2];
        if ( *((_BYTE *)v5 + 21) )
        {
          for ( i = v4[1]; !*(_BYTE *)(i + 21); i = *(_DWORD *)(i + 4) )
          {
            if ( v4 != *(_DWORD **)(i + 8) )
              break;
            v4 = (_DWORD *)i;
          }
          v4 = (_DWORD *)i;
        }
        else
        {
          v4 = (_DWORD *)v4[2];
          for ( j = *v5; !*((_BYTE *)j + 21); j = (int *)*j )
            v4 = j;
        }
      }
    }
    while ( v4 != *(_DWORD **)(a1 + 4) );
  }
  return 1;
}

//----- (004074D0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const,CCharacter *>>>@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const ,CCharacter *> > _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v4; // esi
  CCharacter *second; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  v4 = result;
  if ( result == _First._Ptr )
  {
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
  else
  {
    do
    {
      second = v4->_Myval.second;
      if ( second )
      {
        m_lpGameClientDispatch = second->m_lpGameClientDispatch;
        if ( m_lpGameClientDispatch )
          CGameClientDispatch::Disconnect(m_lpGameClientDispatch);
      }
      if ( !v4->_Isnil )
      {
        Right = v4->_Right;
        if ( Right->_Isnil )
        {
          for ( i = v4->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( v4 != i->_Right )
              break;
            v4 = i;
          }
          v4 = i;
        }
        else
        {
          v4 = v4->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            v4 = j;
        }
      }
    }
    while ( v4 != _First._Ptr );
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
}

//----- (00407560) --------------------------------------------------------
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>,std::pair<unsigned long const,CCharacter *>>>(
        CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _First._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
    return result;
  }
  else
  {
    do
    {
      _Func.m_fnSecondProcess->op._Pmemfun(Ptr->_Myval.second, _Func.m_fnSecondProcess->value);
      if ( !Ptr->_Isnil )
      {
        Right = Ptr->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Ptr != i->_Right )
              break;
            Ptr = i;
          }
          Ptr = i;
        }
        else
        {
          Ptr = Ptr->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Ptr = j;
        }
      }
    }
    while ( Ptr != _Last._Ptr );
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
    return result;
  }
}

//----- (004075F0) --------------------------------------------------------
CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const,CMonster *>>>(
        CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  Ptr = _First._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
    return result;
  }
  else
  {
    do
    {
      _Func.m_fnSecondProcess->_Pmemfun(Ptr->_Myval.second);
      if ( !Ptr->_Isnil )
      {
        Right = Ptr->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Ptr != i->_Right )
              break;
            Ptr = i;
          }
          Ptr = i;
        }
        else
        {
          Ptr = Ptr->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Ptr = j;
        }
      }
    }
    while ( Ptr != _Last._Ptr );
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
    return result;
  }
}
