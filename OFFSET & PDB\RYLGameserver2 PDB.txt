char __thiscall CCharacterParty::MakeTeamBattleInfo(CCharacterParty *this, char *szPacket, unsigned __int16 *dwPacketSize, CCharacter *p<PERSON>hallenger, char cCmd);
void __thiscall CCharacterParty::MovePos(CCharacterParty *this, POS NewPos, char cZone, bool bSitDown);
void __thiscall CCharacterParty::MoveZone(CCharacterParty *this, POS NewPos, char Zone, char Channel); // idb
void __thiscall CCharacterParty::SendPartyCmdInfo(CCharacterParty *this, PktPC::PartyCmd Command, CCharacter *lpCharacter); // idb
void __thiscall CCharacterParty::SendAllLoggedMember(CCharacterParty *this, char *szPacket, unsigned int dwPacketSize, unsigned int dwExclusion, unsigned __int8 cCMD_In);
void __thiscall CCharacterParty::SendDropMember(CCharacterParty *this, CCharacter *pDropMember, PktDuC::DuelCmd eCmd); // idb
void __thiscall CCharacterParty::SendRecall(CCharacterParty *this, CCharacter *lpCaster); // idb
void __thiscall CCharacterParty::SendPartyAddress(CCharacterParty *this, unsigned int dwCharID, const sockaddr_in *PublicAddress, const sockaddr_in *PrivateAddress); // idb
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Pnode); // idb
void __thiscall PARTY::PARTY(PARTY *this); // idb
void __thiscall PARTY_EX::PARTY_EX(PARTY_EX *this); // idb
void __thiscall PARTY_EX::PARTY_EX(PARTY_EX *this, PARTY party); // idb
void __thiscall CParty::~CParty(CParty *this); // idb
int __thiscall CMonsterParty::Attack(CMonsterParty *this, AtType attackType, CAggresiveCreature **pDefenders, unsigned __int8 *cDefenserJudges, CAggresiveCreature *lpOffencer, float fDistance, unsigned __int8 cTargetType);
void __thiscall CCharacterParty::~CCharacterParty(CCharacterParty *this); // idb
int __thiscall CCharacterParty::GetNearMemberList(CCharacterParty *this, CCell *pCell, bool bAutoRouting, CCharacter **aryNearCharacterList, unsigned __int8 *cHighestLevel); // idb
char __thiscall CCharacterParty::AutoRouting(CCharacterParty *this, CCharacter *lpPickkingCreature, unsigned __int64 nObjectID, Item::CItem *lpItem, unsigned int *dwGold, unsigned int *dwOwnerID);
int __thiscall CCharacterParty::Attack(CCharacterParty *this, AtType attackType, CAggresiveCreature **pDefenders, unsigned __int8 *cDefenserJudges, CAggresiveCreature *lpOffencer, float fDistance, unsigned __int8 cTargetType);
char __thiscall CCharacterParty::StartTeamBattle(CCharacterParty *this, CCharacterParty *pHostileParty);
int __thiscall CCharacterParty::DropMember(CCharacterParty *this, CCharacter *pDropMember, PktDuC::DuelCmd eCmd); // idb
void __thiscall CCharacterParty::EndTeamBattle(CCharacterParty *this); // idb
void __thiscall CCharacterParty::SendPartyCommand(CCharacterParty *this, PktPC::PartyCmd Command, const char *SenderName_In, unsigned int dwSenderCID, unsigned int dwReferenceID, const AddressInfo *Address); // idb
void __thiscall CCharacterParty::SendPartyInfo(CCharacterParty *this, CCharacter *lpCharacter); // idb
void __thiscall CCharacterParty::SendDivisionExp(CCharacterParty *this, CCharacter *lpCharacter, CAggresiveCreature *lpDeadCreature, unsigned int dwExp, int nStandardLevel); // idb
void __thiscall CCharacterParty::SendAutoRouting(CCharacterParty *this, unsigned int dwCharID, unsigned __int16 wItemID, unsigned __int8 cCmd); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode); // idb
CParty *__thiscall CParty::`vector deleting destructor'(CParty *this, char a2);
void __thiscall CCharacterParty::CCharacterParty(CCharacterParty *this, const PARTY *PartyInfo, bool bCreate); // idb
CCharacterParty *__thiscall CCharacterParty::`scalar deleting destructor'(CCharacterParty *this, char a2);
char __thiscall CCharacterParty::Join(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, const char *strSenderName, unsigned __int16 wMapIndex);
int __thiscall CCharacterParty::Leave(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, unsigned __int16 wMapIndex); // idb
char __thiscall CCharacterParty::Login(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID);
char __thiscall CCharacterParty::ReLogin(CCharacterParty *this, CCharacter *lpCharacter);
int __thiscall CCharacterParty::Logout(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID); // idb
char __thiscall CCharacterParty::TransferLeader(CCharacterParty *this, unsigned int dwLeaderCID);
char __thiscall CCharacterParty::Destory(CCharacterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID);
char __thiscall CCharacterParty::AdjustAutoRouting(CCharacterParty *this, unsigned int dwTargetID, bool bSwitch);
void __thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Insert(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode, unsigned int *_Val);
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::insert(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::pair<std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator,bool> *result, unsigned int *_Val);
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator *result, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _First, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::iterator _Last); // idb
void __thiscall CMonsterParty::~CMonsterParty(CMonsterParty *this); // idb
void __thiscall std::set<int>::~set<int>(std::set<int> *this); // idb
CMonsterParty *__thiscall CMonsterParty::`vector deleting destructor'(CMonsterParty *this, char a2);
void __thiscall CMonsterParty::CMonsterParty(CMonsterParty *this); // idb
unsigned int __thiscall CMonsterParty::GetMemberTypeNum(CMonsterParty *this);
void __cdecl CNPC::ScriptErrorMessage(const char *msg); // idb
void __cdecl CNPC::SetPosition(unsigned int nUID, float fDirection, float fPosX, float fPosY, float fPosZ);
void __thiscall CSymbolTable::Create(CPacketDispatch *this); // idb
unsigned int __thiscall CNPC::RepairItem(CNPC *this, Item::CEquipment *lpEquipment, unsigned int *dwCurrentGold); // idb
void __cdecl std::fill<unsigned short *,unsigned short>(unsigned __int16 *_First, unsigned __int16 *_Last, unsigned __int16 *_Val);
void __cdecl CNPC::QuestCompleteSave(bool bSave); // idb
void __cdecl CNPC::Else(); // idb
void __cdecl CNPC::EventDisappear(unsigned int nAmount, unsigned int nItemID, unsigned int nGold);
void __cdecl CNPC::EventGet(unsigned int nAmount, unsigned int nItemID);
void __cdecl CNPC::EventSpawn(unsigned int nMonsterID, float fPosX, float fPosY, float fPosZ);
void __cdecl CNPC::EventMonsterDrop(unsigned int nAmount, unsigned int nItemID);
void __cdecl CNPC::EventAward(unsigned int nExp, unsigned int nGold);
void __cdecl CNPC::EventMsgBox();
void __cdecl CNPC::EventPhase(unsigned int nPhaseNumber);
void __cdecl CNPC::EventEnd(); // idb
void __cdecl std::copy_backward<unsigned short *,unsigned short *>(unsigned __int8 *_First, unsigned __int16 *_Last, unsigned __int16 *_Dest);
void __cdecl std::_Rotate<std::vector<unsigned short>::iterator,int,unsigned short>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Mid, std::vector<unsigned short>::iterator _Last);
int __stdcall std::vector<unsigned short>::_Ucopy<unsigned short *>(unsigned __int8 *src, int a2, unsigned __int8 *dst); // idb
void __cdecl std::_Median<std::vector<unsigned short>::iterator>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Mid, std::vector<unsigned short>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<unsigned short>::iterator,int,unsigned short>(std::vector<unsigned short>::iterator _First, int _Hole, int _Bottom, unsigned __int16 _Val); // idb
unsigned __int16 *__thiscall std::vector<unsigned short>::_Ufill(std::vector<unsigned short> *this, unsigned __int16 *_Ptr, unsigned int _Count, const unsigned __int16 *_Val); // idb
std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> *__cdecl std::_Unguarded_partition<std::vector<unsigned short>::iterator>(std::pair<std::vector<unsigned short>::iterator,std::vector<unsigned short>::iterator> *result, std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last); // idb
std::vector<unsigned short>::iterator *__cdecl std::_Lower_bound<std::vector<unsigned short>::iterator,unsigned short,int>(std::vector<unsigned short>::iterator *result, std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last, const unsigned __int16 *_Val);
void __cdecl std::_Make_heap<std::vector<unsigned short>::iterator,int,unsigned short>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last);
CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const,CNPC *>>>(CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CNPC>,std::pair<unsigned long const ,CNPC *> > _Func); // idb
void __cdecl std::_Insertion_sort<std::vector<unsigned short>::iterator>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last); // idb
void __thiscall Quest::TriggerNode::~TriggerNode(Quest::TriggerNode *this); // idb
void __thiscall CNPC::~CNPC(CNPC *this); // idb
int __thiscall CNPC::IsEnemy(CNPC *this, CCreature *lpTarget);
CParty *__thiscall std::streambuf::showmanyc(CNPC *this); // idb
void __cdecl std::sort_heap<std::vector<unsigned short>::iterator>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last); // idb
CNPC *__thiscall CNPC::`scalar deleting destructor'(CNPC *this, char a2);
void __thiscall CNPC::SellToCharacter(CNPC *this, CCharacter *lpCustomer, unsigned __int16 wKindItem, TakeType takeType, unsigned int *dwPrice);
char __thiscall CNPC::GetQuest(CNPC *this, CCharacter *lpCharacter, unsigned __int16 wQuestID, Quest::QuestNode **lppQuestNode);
void __thiscall __noreturn std::vector<unsigned short>::_Xlen(std::vector<unsigned short> *this);
void __cdecl std::_Sort<std::vector<unsigned short>::iterator,int>(std::vector<unsigned short>::iterator _First, std::vector<unsigned short>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<unsigned short>::_Insert_n(std::vector<unsigned short> *this, std::vector<unsigned short>::iterator _Where, unsigned int _Count, const unsigned __int16 *_Val); // idb
char __thiscall CNPC::SortGoods(CNPC *this);
char __thiscall CNPC::SortQuests(CNPC *this);
void __thiscall Quest::TriggerNode::TriggerNode(Quest::TriggerNode *this, unsigned int dwTriggerKind, unsigned int dwTriggerID, unsigned int dwZoneID, unsigned int dwMaxCount, float fPosX, float fPosY, float fPosZ, float fDistance); // idb
void __cdecl CNPC::AddPhase(unsigned int nZoneID, unsigned int nPhaseNumber);
void __cdecl CNPC::TriggerStart(); // idb
void __cdecl CNPC::TriggerPuton(unsigned int nItemID, unsigned int nZoneID, float fPosX, float fPosY, float fPosZ, float fDistance);
void __cdecl CNPC::TriggerGeton(unsigned int nItemID, unsigned int nZoneID, float fPosX, float fPosY, float fPosZ, float fDistance);
void __cdecl CNPC::TriggerTalk(unsigned int nNpcID);
void __cdecl CNPC::TriggerKill(unsigned int nCount, unsigned int nMonsterID);
void __cdecl CNPC::TriggerPick(unsigned int nCount, unsigned int nItemID);
void __thiscall CNPC::CNPC(CNPC *this, unsigned int dwCID, int nZone, int nJob, bool bBindable, bool bRepairable, bool bBlackMarketeer, bool bMustSellWithMileage); // idb
void __thiscall CNPC::AddGoodsToNPC(CNPC *this, unsigned __int16 wKindItem); // idb
void __thiscall CNPC::AddQuestsToNPC(CNPC *this, unsigned __int16 wQuestID); // idb
void __cdecl CNPC::SetNPC(int nZone, unsigned int nUID, int nJob);
void __cdecl CNPC::AddItem(unsigned int nUID, unsigned __int16 nKindItem);
void __cdecl CNPC::AddQuest(unsigned int nUID, unsigned __int16 nQuestID, unsigned __int16 nMinLevel, unsigned __int16 nMaxLevel, unsigned int nClass, unsigned int nCompleteClass, char *strQuestFunc);
char __cdecl CNPC::LoadNPCInfo();
char __cdecl GameClientSendPacket::SendCharTakeItem(CSendStream *SendStream, unsigned int dwCharID, TakeType takeType, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharSwapItem(CSendStream *SendStream, unsigned int dwCharID, TakeType takeSrc, TakeType takeDst, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharRepairItem(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwRepairGold, Item::ItemPos itemPos, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharRepairAllItem(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwRepairGold, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharUseItem(CSendStream *SendStream, unsigned int dwSender, unsigned int dwReceiver, Item::ItemPos itemPos, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharTradeItem(CSendStream *SendStream, CCharacter *lpCharacter, unsigned int dwNPCID, Item::CItem *lpItem, Item::ItemPos itemPos, unsigned __int8 cNum, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharPickUp(CSendStream *SendStream, unsigned int dwCID, unsigned __int64 nObjectID, unsigned int dwGold, Item::CItem *lpItem, Item::ItemPos dstPos, unsigned __int8 cNum, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharAutoRouting(CSendStream *SendStream, unsigned int dwCID, unsigned __int64 nObjectID, unsigned __int16 wItemID, unsigned __int8 cNum, unsigned __int8 cCmd);
char __cdecl GameClientSendPacket::SendCharPullDown(CSendStream *SendStream, unsigned int dwCID, Item::ItemPos itemPos, CCell::ItemInfo *lpItemInfo, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharDisappearItem(CSendStream *SendStream, unsigned int dwCID, Item::ItemPos itemPos, unsigned __int8 cNum, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharSplitItem(CSendStream *SendStream, unsigned int dwCID, Item::CItem *lpSplitItem, TakeType takeType, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharTakeGold(CSendStream *SendStream, unsigned int dwCID, unsigned int dwGold, unsigned __int8 cSrcPos, unsigned __int8 cDstPos, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharExchangeItem(CSendStream *SendStream, unsigned int dwOwnerCID, unsigned int dwGold, Item::CItem *lpItem, Item::ItemPos itemPos, bool bStack, bool bRemove);
char __cdecl GameClientSendPacket::SendCharDepositCmd(CSendStream *SendStream, unsigned __int8 cCmd, char *szData, unsigned int nDataLength, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharDepositPasswordToDBAgent(CSendStream *SendStream, unsigned int dwUID, const char *szPassword, unsigned int nPasswordLen);
char __cdecl GameClientSendPacket::SendCharDepositGoldToDBAgent(CSendStream *SendStream, unsigned int dwUID, unsigned int dwGold);
char __cdecl GameClientSendPacket::SendCharLotteryResult(CSendStream *SendStream, unsigned int dwCID, Item::ItemPos itemPos, Item::CItem *lpItem, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharCastleInfo(CSendStream *SendStream);
char __cdecl GameClientSendPacket::SendCharSiegeTimeInfo(CSendStream *SendStream);
char __cdecl GameClientSendPacket::SendCharCampInfo(CSendStream *SendStream);
char __cdecl GameClientSendPacket::SendCharCastleCmd(CSendStream *SendStream, unsigned int dwCastleID, unsigned int dwCastleObjectID, unsigned __int8 cSubCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharCastleRight(CSendStream *SendStream, unsigned int dwCasltID, const CastleRight *castleRight, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharCampRight(CSendStream *SendStream, unsigned int dwCampID, const CampRight *campRight, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharCampCmd(CSendStream *SendStream, unsigned int dwCID, unsigned int dwCampID, unsigned __int8 cSubCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharSiegeArmsCmd(CSendStream *SendStream, unsigned int dwCID, unsigned int dwArmsID, unsigned __int8 cSubCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharCreateCamp(CSendStream *AgentSendStream, unsigned int dwGID, const Position *pos);
char __cdecl GameClientSendPacket::SendCharCreateSiegeArms(CSendStream *AgentSendStream, unsigned int dwOwnerID, unsigned int dwGID, unsigned __int16 wType, unsigned __int8 cUpgradeStep, const Position *pos);
char __cdecl GameClientSendPacket::SendCharCastleCmdToDBAgent(CSendStream *AgentSendStream, unsigned int dwCastleID, unsigned int dwCastleObjectID, unsigned int dwHP, unsigned int dwValue, unsigned __int8 cSubCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharCampCmdToDBAgent(CSendStream *AgentSendStream, unsigned int dwCID, unsigned int dwCampID, unsigned int dwHP, unsigned __int8 cSubCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(CSendStream *AgentSendStream, unsigned int dwCID, unsigned int dwArmsID, unsigned int dwValue, unsigned __int8 cSubCmd, unsigned __int16 wError);
CCreature::MutualType __thiscall CSiegeObject::IsEnemy(CSiegeObject *this, CAggresiveCreature *lpTarget);
char __thiscall CSiegeObject::IsRide(CSiegeObject *this, unsigned int dwCID);
bool __thiscall CSiegeObject::Ride(CSiegeObject *this, unsigned int dwRideCID); // idb
bool __thiscall CSiegeObject::GetOff(CSiegeObject *this, unsigned int dwRideCID); // idb
void __thiscall CSiegeObject::AllGetOff(CSiegeObject *this); // idb
char __thiscall CSiegeObject::DestroyCamp(CSiegeObject *this);
bool __thiscall CSiegeObject::CheckRight(CSiegeObject *this, unsigned __int8 cRightType, unsigned int dwCID); // idb
unsigned int __thiscall CSiegeObject::GetUpgradeGold(CSiegeObject *this); // idb
int __thiscall CSiegeObject::GetRepairGold(CSiegeObject *this);
int __thiscall CSiegeObject::GetRestoreGold(CSiegeObject *this);
int __thiscall CSiegeObject::GetRepairHP(CSiegeObject *this);
int __thiscall CSiegeObject::GetRepairMaterialNum(CSiegeObject *this);
int __thiscall CSiegeObject::GetKID(CSiegeObject *this); // idb
void __thiscall CSiegeObject::SendToRadiusCell(CSiegeObject *this, char *szPacket, unsigned int dwPacketSize, unsigned __int8 cCMD_In);
char __thiscall CSiegeObject::AttackCID(CSiegeObject *this, CCharacter *lpRideChar, AtType attackType, AtNode *attackNode, unsigned __int16 *wError);
char __thiscall CSiegeObject::Attack(CSiegeObject *this, AtType attackType, unsigned __int8 cDefenderNum, CAggresiveCreature **ppDefenders, unsigned __int8 *cDefenserJudges);
char __thiscall CSiegeObject::BuildCamp(CSiegeObject *this);
char __thiscall CSiegeObject::UpdateCampInfo(CSiegeObject *this, unsigned __int8 cState, unsigned __int8 cUpgradeStep, unsigned __int8 cSubCmd);
char __thiscall CSiegeObject::UpgradeCamp(CSiegeObject *this, unsigned __int8 cUpgradeStep);
char __thiscall CSiegeObject::RepairCamp(CSiegeObject *this, unsigned __int16 wRepairHP);
char __thiscall CSiegeObject::GiveBackSiegeMaterial(CSiegeObject *this);
char __thiscall CSiegeObject::UpdateEmblem(CSiegeObject *this, unsigned __int8 cState, unsigned int dwValue, unsigned __int8 cSubCmd);
char __thiscall CSiegeObject::UpgradeEmblem(CSiegeObject *this, unsigned __int8 cUpgradeStep);
void __thiscall CSiegeObject::Open(CSiegeObject *this); // idb
void __thiscall CSiegeObject::Close(CSiegeObject *this); // idb
char __thiscall CSiegeObject::UpdateGate(CSiegeObject *this, unsigned __int8 cState, unsigned int dwValue, unsigned __int8 cSubCmd);
char __thiscall CSiegeObject::UpgradeGate(CSiegeObject *this, unsigned __int8 cUpgradeStep);
char __thiscall CSiegeObject::RepairGate(CSiegeObject *this, unsigned __int16 wRepairHP);
char __thiscall CSiegeObject::RestoreGate(CSiegeObject *this);
char __thiscall CSiegeObject::DestroyGate(CSiegeObject *this);
char __thiscall CSiegeObject::UpdateCastleArms(CSiegeObject *this, unsigned __int8 cState, unsigned int dwValue, unsigned __int8 cSubCmd);
char __thiscall CSiegeObject::ChangeCastleArms(CSiegeObject *this, unsigned __int16 wType);
char __thiscall CSiegeObject::UpgradeCastleArms(CSiegeObject *this, unsigned __int8 cUpgradeStep);
char __thiscall CSiegeObject::RepairCastleArms(CSiegeObject *this, unsigned __int16 wRepairHP);
char __thiscall CSiegeObject::DestroyCastleArms(CSiegeObject *this);
bool __thiscall CSiegeObject::UpdateSiegeArms(CSiegeObject *this, unsigned __int8 cState, unsigned int dwValue, unsigned __int8 cSubCmd); // idb
char __thiscall CSiegeObject::ChangeToStartKit(CSiegeObject *this);
void __thiscall CSiegeObject::SetRight(CSiegeObject *this, CampRight campRight); // idb
void __thiscall std::list<LogBuffer *>::~list<LogBuffer *>(std::list<CThread *> *this); // idb
void __thiscall CSiegeObject::~CSiegeObject(CSiegeObject *this); // idb
void __thiscall CSiegeObject::DeleteProtectGate(CSiegeObject *this, unsigned int dwCID); // idb
void __thiscall CSiegeObject::CSiegeObject(CSiegeObject *this, CMonster::MonsterCreateInfo *MonsterCreate, const CastleObjectInfo *CastleObject); // idb
CSiegeObject *__thiscall CSiegeObject::`vector deleting destructor'(CSiegeObject *this, char a2);
void __thiscall CSiegeObject::CSiegeObject(CSiegeObject *this, CMonster::MonsterCreateInfo *MonsterCreate, unsigned int dwCampID, unsigned int dwGID, unsigned __int16 dwHP, unsigned __int8 cState, unsigned __int8 cUpgradeStep, bool bFullHP);
void __thiscall CSiegeObject::CSiegeObject(CSiegeObject *this, CMonster::MonsterCreateInfo *MonsterCreate, unsigned int dwOwnerID, unsigned int dwGID, unsigned int dwHP, unsigned __int16 wObjectType, unsigned __int8 cState, unsigned __int8 cUpgradeStep); // idb
void __thiscall std::list<unsigned long>::_Incsize(std::list<unsigned long> *this, unsigned int _Count); // idb
void __thiscall CSiegeObject::AddProtectGate(CSiegeObject *this, unsigned int dwCID); // idb
char __cdecl GameClientSendPacket::SendCharStartQuest(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwNPCID, unsigned __int16 wQuestID, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharOperateTrigger(CSendStream *SendStream, unsigned int dwCharID, unsigned __int16 wQuestID, unsigned __int8 cPhase, unsigned __int8 cTrigger, unsigned __int8 cCount, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharQuestInfo(CSendStream *SendStream, CCharacter *lpCharacter);
char __cdecl GameClientSendPacket::SendCharEndQuest(CSendStream *SendStream, unsigned int dwCharID, unsigned __int16 wQuestID, bool bSave);
char __cdecl GameClientSendPacket::SendCharCancelQuest(CSendStream *SendStream, unsigned int dwCharID, unsigned __int16 wQuestID, unsigned __int16 wError);
void __thiscall CTempCharacter::CTempCharacter(CTempCharacter *this); // idb
boost::simple_segregated_storage<unsigned int> *__thiscall boost::pool<boost::default_user_allocator_new_delete>::ordered_malloc_need_resize(boost::pool<boost::default_user_allocator_new_delete> *this);
CTempCharacter *__thiscall boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::construct(boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete> *this); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Wherenode, const std::pair<unsigned long const ,CTempCharacter *> *_Val); // idb
char __thiscall CTempCharacterMgr::EraseChar(CTempCharacterMgr *this, unsigned int dwBattleCID);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *result, const std::pair<unsigned long const ,CTempCharacter *> *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Where, std::pair<unsigned long const ,CTempCharacter *> *_Val); // idb
CTempCharacter *__thiscall CTempCharacterMgr::GetCharacter(CTempCharacterMgr *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwBattleCID, unsigned __int8 cGroup);
void __thiscall CTempCharacterMgr::~CTempCharacterMgr(CTempCharacterMgr *this); // idb
void __thiscall CSingleton<CPartyMgr>::~CSingleton<CPartyMgr>(CSingleton<CPartyMgr> *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *_Rootnode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Rarg, const std::pair<unsigned long const ,unsigned long> *_Val, char _Carg); // idb
void __thiscall CPartyMgr::SendPartyFind(CPartyMgr *this, CCharacter *lpCharacter); // idb
void __thiscall CPartyMgr::DestoryPartyList(CPartyMgr *this); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _Where);
std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CParty *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::iterator _Last); // idb
bool __thiscall CPartyMgr::AddParty(CPartyMgr *this, CParty *pParty); // idb
char __thiscall CPartyMgr::DeleteParty(CPartyMgr *this, unsigned int dwPartyUID);
bool __thiscall CPartyMgr::AddFindPartyList(CPartyMgr *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *dwCID);
char __thiscall CPartyMgr::DeleteFindPartyList(CPartyMgr *this, unsigned int dwCID);
bool __thiscall CPartyMgr::AddFindMemberList(CPartyMgr *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *dwPartyUID);
char __thiscall CPartyMgr::DeleteFindMemberList(CPartyMgr *this, unsigned int dwPartyUID);
void __thiscall std::map<unsigned long,CParty *>::~map<unsigned long,CParty *>(std::map<unsigned long,CParty *> *this); // idb
void __thiscall std::map<unsigned long,CCharacter *>::~map<unsigned long,CCharacter *>(std::map<unsigned long,CCharacter *> *this); // idb
void __thiscall CPartyMgr::~CPartyMgr(CPartyMgr *this); // idb
void __thiscall CPartyMgr::CPartyMgr(CPartyMgr *this); // idb
void __thiscall Castle::CCastle::GetTaxIncome(Castle::CCastle *this, unsigned int dwTaxIncome); // idb
void __thiscall Castle::CCastle::DecreaseDayValue(Castle::CCastle *this); // idb
bool __thiscall Castle::CCastle::CheckRight(Castle::CCastle *this, unsigned __int8 cRightType, unsigned int dwCID, unsigned int dwGID); // idb
void __thiscall Castle::CCastle::SetTax(Castle::CCastle *this, unsigned __int16 wTax); // idb
void __thiscall Castle::CCastle::SetRight(Castle::CCastle *this, CastleRight castleRight); // idb
void __thiscall Castle::CCastle::UpdateCastleInfo(Castle::CCastle *this, unsigned int dwGID, unsigned __int16 wTax, unsigned int dwTaxIncome, unsigned __int8 cInvincibleDay, unsigned __int8 cTaxMoneyRamainDay, CastleRight castleRight, const char *szCastleName); // idb
CSiegeObject *__thiscall Castle::CCastle::GetCastleEmblem(Castle::CCastle *this); // idb
void __thiscall Castle::CCastle::UpgradeByEmblem(Castle::CCastle *this); // idb
void __thiscall Castle::CCastle::DegradeByEmblem(Castle::CCastle *this); // idb
void __thiscall Castle::CCastle::AllRespawn(Castle::CCastle *this, unsigned int dwExceptGID); // idb
char __thiscall Castle::CCastle::ChangeCastleMaster(Castle::CCastle *this, unsigned int dwGID);
bool __thiscall Castle::CCastle::InsertCastleObject(Castle::CCastle *this, CSiegeObject *lpCastleObject); // idb
void __thiscall Castle::CCastle::~CCastle(std::map<unsigned long,CSiegeObject *> *this); // idb
void __thiscall Castle::CCastle::CCastle(Castle::CCastle *this, const CastleInfoDB *CastleInfo); // idb
void __thiscall CCastingSpell::~CCastingSpell(CCastingSpell *this); // idb
void __thiscall CSpellMgr::CSpellMgr(CSpellMgr *this); // idb
void __thiscall CSpellMgr::~CSpellMgr(CSpellMgr *this); // idb
char __cdecl GAMELOG::LogDamagePumping(CCharacter *AttackCharacter_In, CMonster *DefendMonster_In, __int16 nDamage_In, bool bWriteForce_In);
void __thiscall CXORCrypt::XORF(CXORCrypt *this, char *Start_In, int Length_In, unsigned __int16 PageVer, unsigned __int8 PageNum1, unsigned __int8 PageNum2); // idb
void __thiscall CXORCrypt::XORB(CXORCrypt *this, char *Start_In, int Length_In, unsigned __int16 PageVer, unsigned __int8 PageNum1, unsigned __int8 PageNum2); // idb
void __thiscall CXORCrypt::InitCodePage(CXORCrypt *this); // idb
int __thiscall CXORCrypt::GetCodePage(CXORCrypt *this);
char __thiscall CXORCrypt::EncodePacket(CXORCrypt *this, char *Start_In, int Length_In, unsigned int CodePage_In);
char __thiscall CXORCrypt::DecodePacket(CXORCrypt *this, char *Start_In, int Length_In, unsigned int CodePage_In);
void __thiscall CXORCrypt::EncodeHeader(CXORCrypt *this, char *Start_In, int Length_In, unsigned __int16 PageVer_In, unsigned __int8 PageNum_In); // idb
void __thiscall CXORCrypt::DecodeHeader(CXORCrypt *this, char *Start_In, int Length_In, unsigned __int16 PageVer_In, unsigned __int8 PageNum_In); // idb
void __thiscall CXORCrypt::CXORCrypt(CXORCrypt *this); // idb
void __thiscall CXORCrypt::~CXORCrypt(CXORCrypt *this); // idb
BOOL __cdecl CMiniLZO::Compress(const char *in, unsigned int in_len, char *out, unsigned int *lp_out_len);
BOOL __cdecl CMiniLZO::Decompress(const char *in, unsigned __int8 *in_len, char *out, unsigned int *buffersize_in_out_len);
char __cdecl GameClientParsePacket::ParseCharCastleCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharCastleRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharCampRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharCampCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
bool __thiscall std::vector<Item::ItemGarbage>::empty(std::vector<Item::ItemGarbage> *this); // idb
char __cdecl GameClientParsePacket::ParseCharSiegeArmsCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerMapList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerResultList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerMoveZone(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerMileageChange(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerCharSlot(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBGServerList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSuicide(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharBindPosition(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharQuickSlotMove(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharControlOption(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharAuthorizePanel(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharFameInfo(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharRankingInfo(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
bool __cdecl GameClientParsePacket::ParseCharStartQuest(CGameClientDispatch *GameClientDispatch, Quest::QuestNode *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharOperateTrigger(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharCancelQuest(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
int __thiscall  __thiscall `vcall'{64,{flat}}(void *this);
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>,std::pair<unsigned long const,CMonster *>>>(CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > _Func); // idb
CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const,CCharacter *>>>(CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > _Func); // idb
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *__thiscall CCreatureManager::ProcessAllMonster<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>>(int this, CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > _Func, int a3);
CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *__thiscall CCreatureManager::ProcessAllCharacter<Respawn>(int this, CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > _Func);
char __cdecl GameClientParsePacket::ProcessAdminCmd(CCharacter *lpAdmin, CCharacter *lpTarget, PktAdmin *lpPktAdmin);
char __cdecl GameClientParsePacket::ParseCharAdminCmd(CGameClientDispatch *GameClientDispatch, PktAdmin *lpPktBase);
BOOL __thiscall CFriendList::Rebind::IsFriend(CFriendList::Rebind *this, char *szName);
char __thiscall CFriendList::Rebind::SetGroup(CFriendList::Rebind *this, unsigned int dwGroup);
bool __cdecl GameClientParsePacket::ParseCharExchangeCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase); // idb
char __cdecl GameClientParsePacket::ParseCharStallOpen(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharStallRegisterItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharStallEnter(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharFriendRemove(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharFriendEtc(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharCreateGuild(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildCmd(CGameClientDispatch *GameClientDispatch, int lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildMark(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildLevel(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildRelation(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildInclination(CGameClientDispatch *GameClientDispatch, int lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildMemberList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharGuildSafe(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
CFriendList::Rebind *__thiscall CFriendList::GetFriendNum(CFriendList *this);
char __cdecl GameClientParsePacket::ParseCharPartyCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharPartyFind(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharFriendAdd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharClassUpgrade(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharIncreasePoint(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharStateRedistribution(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharStatusRetrain(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharAttack(CGameClientDispatch *GameClientDispatch, int lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSwitchHand(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharRespawn(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharRespawnWaitQueue(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharMoveUpdate(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharDuelCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharPeaceMode(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSummonCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharMoveEx(CGameClientDispatch *GameClientDispatch, CSiegeObject *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSkillErase(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharInstallSocket(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharItemChemical(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharUpgradeItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
unsigned int __thiscall Item::CItem::GetSellPrice(Item::CItem *this); // idb
bool __thiscall Item::CDepositContainer::Login(Item::CDepositContainer *this, const char *szPassword, unsigned int nPasswordLength, char bSavePassword); // idb
char __thiscall Item::CDepositContainer::BuyTab(Item::CDepositContainer *this, unsigned __int8 cTabNum);
BOOL __cdecl IsPutDeposit(unsigned __int8 cSrc, unsigned __int8 cDst);
char __cdecl GameClientParsePacket::ParseCharTakeItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSwapItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharRepairItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharRepairAllItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharUseItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharPullDown(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharTakeGold(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharDepositCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ProcessItemSell(CGameClientDispatch *GameClientDispatch, PktTr *lpPktTr);
char __cdecl GameClientParsePacket::ProcessItemBuy(CGameClientDispatch *GameClientDispatch, PktTr *lpPktTr);
char __cdecl GameClientParsePacket::ProcessItemPickUp(CGameClientDispatch *GameClientDispatch, CFindItemInfoFromUID nObjectID, int itemPos, Item::CItem **lppItem, unsigned __int16 *usError);
char __cdecl GameClientParsePacket::ParseCharTradeItem(CGameClientDispatch *GameClientDispatch, PktTr *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharPickUp(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharSplitItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharAutoRouting(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
void __thiscall CCheckPing::CCheckPing(CCheckPing *this); // idb
char __thiscall CCheckPing::CheckPing(CCheckPing *this, unsigned int dwCurrentTime);
void __thiscall CCheckPing::SetLastPingRecvTime(CCheckPing *this, unsigned int dwCurrentTime); // idb
void __thiscall CCheckPing::GetPingData(CCheckPing *this, unsigned int *dwPingCount, unsigned int *dwLastPingRecvTime, unsigned int *dwFirstCheckTime); // idb
void __thiscall CSpeedHackCheck::CoolDownInfo::CoolDownInfo(CSpeedHackCheck::CoolDownInfo *this, const Skill::ProtoType *lpSkillProtoType, AtType attackType, unsigned int dwServerLastCastingTime, unsigned int dwClientLastCastingTime); // idb
void __thiscall CSpeedHackCheck::LogSpeedHackMoveUpdate(CSpeedHackCheck *this); // idb
char __thiscall CSpeedHackCheck::CheckSpeedHack(CSpeedHackCheck *this, unsigned int CurrentServer, unsigned int CurrentClient);
BanInfo *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::size(std::vector<BanInfo> *this);
void __cdecl std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(BanInfo *_First, BanInfo *_Last, const BanInfo *_Val); // idb
BanInfo *__cdecl std::_Copy_backward_opt<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo *>(BanInfo *_First, BanInfo *_Last, BanInfo *_Dest);
char __thiscall CSpeedHackCheck::CheckMoveUpdate(CSpeedHackCheck *this, PktMU *lpPktMU);
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::pop_front(std::deque<CSpeedHackCheck::SkillHistory> *this); // idb
void __thiscall CSpeedHackCheck::LogAttackHack(CSpeedHackCheck *this, const char *szDetailText); // idb
void __thiscall CSpeedHackCheck::LogSkillCoolDownTime(CSpeedHackCheck *this, const CSpeedHackCheck::CoolDownInfo *coolDownInfo, int nMinCoolDownTime, int nServerCoolDownTimeInterval, int nClientCoolDownTimeInterval); // idb
std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *__cdecl std::_Lower_bound<std::vector<CSpeedHackCheck::CoolDownInfo>::iterator,unsigned short,int>(std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *result, std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _First, std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Last, unsigned __int16 *_Val);
BanInfo *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::_Ufill(std::vector<BanInfo> *this, BanInfo *_Ptr, unsigned int _Count, const BanInfo *_Val); // idb
void __thiscall __noreturn std::vector<CSpeedHackCheck::CoolDownInfo>::_Xlen(std::vector<CSpeedHackCheck::CoolDownInfo> *this);
void __thiscall __noreturn std::deque<CSpeedHackCheck::AttackTime>::_Xlen(std::deque<CSpeedHackCheck::SkillHistory> *this);
void __thiscall std::deque<CSpeedHackCheck::AttackTime>::_Growmap(std::deque<CSpeedHackCheck::AttackTime> *this, unsigned int _Count); // idb
void __thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::_Insert_n(std::vector<CSpeedHackCheck::CoolDownInfo> *this, std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Where, unsigned int _Count, const CSpeedHackCheck::CoolDownInfo *_Val); // idb
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::_Growmap(std::deque<CSpeedHackCheck::SkillHistory> *this, unsigned int _Count); // idb
void __thiscall std::deque<CSpeedHackCheck::AttackTime>::push_back(std::deque<CSpeedHackCheck::AttackTime> *this, const CSpeedHackCheck::AttackTime *_Val); // idb
std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::insert(std::vector<CSpeedHackCheck::CoolDownInfo> *this, std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *result, std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Where, const CSpeedHackCheck::CoolDownInfo *_Val); // idb
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::push_back(std::deque<CSpeedHackCheck::SkillHistory> *this, const CSpeedHackCheck::SkillHistory *_Val); // idb
void __thiscall CSpeedHackCheck::CSpeedHackCheck(CSpeedHackCheck *this); // idb
char __thiscall CSpeedHackCheck::CheckAttackHack(CSpeedHackCheck *this, signed int lpPktAt, int dwCurrentServerTime);
char __thiscall CSpeedHackCheck::CheckAttackReplay(CSpeedHackCheck *this, PktAt *lpPktAt);
void __thiscall CDBRequest::CDBRequest(CDBRequest *this, CGameClientDispatch *GameClientDispatch, unsigned int dwDurationSec, void (__cdecl *lpTimeoutRequest)(CPacketDispatch *)); // idb
void __thiscall CDBRequest::CancelRequest(CDBRequest *this); // idb
void __thiscall CPartySpellMgr::AddAffectedToAllMember(CPartySpellMgr *this, CSpell *pSpell, unsigned __int16 wMapIndex); // idb
void __cdecl LogChantBug(CAggresiveCreature *lpCreature, CParty *lpParty, const char *szMessage, const char *lpRtn, const char *lpFileName, int nLine); // idb
void __thiscall CPartySpellMgr::CPartySpellMgr(CPartySpellMgr *this); // idb
char __thiscall CPartySpellMgr::AddMember(CPartySpellMgr *this, CAggresiveCreature *lpNewMember);
void __thiscall CPartySpellMgr::ClearMember(CPartySpellMgr *this); // idb
void __thiscall CPartySpellMgr::~CPartySpellMgr(CPartySpellMgr *this); // idb
char __thiscall CPartySpellMgr::RemoveMember(CPartySpellMgr *this, CAggresiveCreature *lpRemoveMember);
void __thiscall Skill::ProtoType::Initialize(Skill::ProtoType *this); // idb
void __thiscall Skill::ProtoType::ProtoType(Skill::ProtoType *this); // idb
void __thiscall CFSMState::~CFSMState(CFSMState *this); // idb
void __thiscall CFSMState::AddTransition(CFSMState *this, int Input, int OutputID); // idb
int __thiscall CFSMState::GetOutput(CFSMState *this, int Input); // idb
void __thiscall CFSMState::CFSMState(CFSMState *this, int StateID, int Transitions); // idb
char __thiscall CFSM::AddState(CFSM *this, CFSMState *lpNewState);
void __thiscall CSingleton<CFSM>::~CSingleton<CFSM>(CSingleton<CFSM> *this); // idb
void __thiscall CFSM::CFSM(CFSM *this); // idb
void __thiscall CFSM::~CFSM(CFSM *this); // idb
int __thiscall CFSM::StateTransition(CFSM *this, int nCrurrentState, int Input); // idb
char __thiscall CDelimitedFile::ReadLine(CDelimitedFile *this);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, bool *bNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, float *fNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, unsigned int *fNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, int *iNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, __int16 *iNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, char *iNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, __int64 *i64Number);
char __thiscall CDelimitedFile::ReadString(CDelimitedFile *this, char *szString, size_t dwSize);
char __thiscall CDelimitedFile::GotoColumn(CDelimitedFile *this, unsigned int nColumn);
std::string *__thiscall std::string::replace(std::string *this, unsigned int _Off, unsigned int _N0, const std::string *_Right, unsigned int _Roff, unsigned int _Count); // idb
void __thiscall CDelimitedFile::Close(CDelimitedFile *this); // idb
unsigned int __thiscall CDelimitedFile::GetSection(CDelimitedFile *this, std::string *szSectionName); // idb
void __thiscall CDelimitedFile::SectionInfo::~SectionInfo(CDelimitedFile::SectionInfo *this); // idb
unsigned int __thiscall CDelimitedFile::FindColumn(CDelimitedFile *this, const char *szField);
_BYTE *__thiscall std::string::_Construct<std::string::iterator>(std::string *this, char *a2, char *a3, int a4);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, float *fNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, unsigned int *fNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, unsigned __int16 *iNumber);
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, char *iNumber);
char __thiscall CDelimitedFile::ReadString(CDelimitedFile *this, const char *szField, char *szString, size_t dwSize);
void __thiscall std::vector<char *>::_Insert_n(std::vector<char *> *this, std::vector<char *>::iterator _Where, unsigned int _Count, char *const *_Val); // idb
void __thiscall __noreturn std::vector<char *>::_Xlen(std::vector<CDelimitedFile::SectionInfo> *this);
CDelimitedFile::SectionInfo *__cdecl std::_Copy_backward_opt<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *>(CDelimitedFile::SectionInfo *_First, CDelimitedFile::SectionInfo *_Last, CDelimitedFile::SectionInfo *_Dest);
CDelimitedFile::SectionInfo *__cdecl std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(CDelimitedFile::SectionInfo *_First, CDelimitedFile::SectionInfo *_Last, CDelimitedFile::SectionInfo *_Dest);
std::string *__thiscall std::string::_Replace<std::string::iterator>(std::string *this, unsigned int _Off, int a3, char *a4, char *a5, int a6);
void __thiscall std::vector<char *>::reserve(std::vector<char *> *this, unsigned int _Count); // idb
void __cdecl std::fill<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo>(CDelimitedFile::SectionInfo *_First, CDelimitedFile::SectionInfo *_Last, const CDelimitedFile::SectionInfo *_Val); // idb
void __cdecl std::_Uninit_fill_n<CDelimitedFile::SectionInfo *,unsigned int,CDelimitedFile::SectionInfo,std::allocator<CDelimitedFile::SectionInfo>>(CDelimitedFile::SectionInfo *_First, unsigned int _Count, const CDelimitedFile::SectionInfo *_Val);
char __thiscall CDelimitedFile::Open(CDelimitedFile *this, const char *szFilename, int nHeadLine, unsigned int nOpenFlags);
CDelimitedFile::SectionInfo *__thiscall std::vector<CDelimitedFile::SectionInfo>::_Ufill(std::vector<CDelimitedFile::SectionInfo> *this, CDelimitedFile::SectionInfo *_Ptr, unsigned int _Count, const CDelimitedFile::SectionInfo *_Val); // idb
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Destroy(std::vector<CDelimitedFile::SectionInfo> *this, CDelimitedFile::SectionInfo *_First, CDelimitedFile::SectionInfo *_Last); // idb
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Insert_n(std::vector<CDelimitedFile::SectionInfo> *this, std::vector<CDelimitedFile::SectionInfo>::iterator _Where, unsigned int _Count, const CDelimitedFile::SectionInfo *_Val); // idb
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Tidy(std::vector<CDelimitedFile::SectionInfo> *this); // idb
void __thiscall std::vector<CDelimitedFile::SectionInfo>::push_back(std::vector<CDelimitedFile::SectionInfo> *this, const CDelimitedFile::SectionInfo *_Val); // idb
void __thiscall CDelimitedFile::CDelimitedFile(CDelimitedFile *this, const char *pszDelimiter); // idb
void __thiscall CDelimitedFile::~CDelimitedFile(CDelimitedFile *this); // idb
char __thiscall CDelimitedFile::ReadSection(CDelimitedFile *this);
void __thiscall CChatPacket::CChatPacket(CChatPacket *this, unsigned int dwCID, const char *szMessage, unsigned __int16 usLength, PktCt::PktCtCmd ctCmd, unsigned __int16 usState, unsigned __int16 usError); // idb
void __cdecl _SE_Execute(struct CVirtualMachine *a1); // idb
void _SE_RegisterFunction(struct CVirtualMachine *a1, char *a2, enum eDataType a3, char *_Ptr, ...);
int *_SE_GetScriptFunction(int *a1, CVirtualMachine *a2, char *a3, char *_Ptr, ...);
void __cdecl _SE_SetMessageFunction(void (__cdecl *)(const char *)); // idb
union AnyData __cdecl _SE_CallScriptFunction(struct CVirtualMachine *a1, struct ScriptFunc a2, unsigned int a3);
struct CVirtualMachine *__cdecl _SE_Create(char *file);
void __cdecl _SE_Destroy(struct CVirtualMachine *p); // idb
unsigned int __cdecl CCrc32Static::BufferCrc32(const char *szBuffer, unsigned int size, unsigned int *dwCrc32); // idb
UINT __cdecl Registry::ReadInt(const char *FileName_In, const char *Section_In, const char *KeyName_In);
BOOL __cdecl Registry::ReadString(const char *FileName_In, const char *Section_In, const char *KeyName_In, char *Buffer_Out, DWORD nBufferSize);
void __thiscall std::ostream::_Sentry_base::~_Sentry_base(std::istream::sentry *this); // idb
int __thiscall std::streambuf::snextc(std::streambuf *this); // idb
char __thiscall std::istream::_Ipfx(std::istream *this, std::locale _Noskip);
void __thiscall std::istream::sentry::sentry(std::istream::sentry *this, std::istream *_Istr, std::locale _Noskip);
char *__cdecl trim_string(char *szString); // idb
void __cdecl std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(CConsoleCMDFactory::StringCMD *_First, CConsoleCMDFactory::StringCMD *_Last, const CConsoleCMDFactory::StringCMD *_Val); // idb
void __cdecl std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last); // idb
void __cdecl std::_Push_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, int _Hole, int _Top, CTokenlizedFile::ColumnInfo _Val); // idb
void __cdecl std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last);
void __cdecl std::_Median<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last); // idb
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__cdecl std::_Lower_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(std::vector<CTokenlizedFile::ColumnInfo>::iterator *result, std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last, const CTokenlizedFile::ColumnInfo *_Val);
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__cdecl std::_Upper_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(std::vector<CTokenlizedFile::ColumnInfo>::iterator *result, std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last, const CTokenlizedFile::ColumnInfo *_Val);
void __cdecl std::_Adjust_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, int _Hole, int _Bottom, CTokenlizedFile::ColumnInfo _Val); // idb
void __cdecl std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(CConsoleCMDFactory::StringCMD *_First, unsigned int _Count, const CConsoleCMDFactory::StringCMD *_Val);
std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *result, std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last);
std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *__cdecl std::_Equal_range<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *result, std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last, const CTokenlizedFile::ColumnInfo *_Val);
void __cdecl std::_Insertion_sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last); // idb
void __cdecl std::sort_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last); // idb
void __thiscall CTokenlizedFile::~CTokenlizedFile(CTokenlizedFile *this); // idb
void __thiscall CTokenlizedFile::Close(CTokenlizedFile *this); // idb
bool __thiscall CTokenlizedFile::Open(CTokenlizedFile *this, const char *szFilename, const char *szOpenMode); // idb
char *__thiscall CTokenlizedFile::GetStringValue(CTokenlizedFile *this, const char *szColumnName);
void __thiscall __noreturn std::vector<CTokenlizedFile::ColumnInfo>::_Xlen(std::vector<CTokenlizedFile::ColumnInfo> *this);
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::_Insert_n(std::vector<CTokenlizedFile::ColumnInfo> *this, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Where, unsigned int _Count, const CTokenlizedFile::ColumnInfo *_Val); // idb
void __cdecl std::_Sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int>(std::vector<CTokenlizedFile::ColumnInfo>::iterator _First, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::reserve(std::vector<CTokenlizedFile::ColumnInfo> *this, CTokenlizedFile::ColumnInfo *_Count);
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__thiscall std::vector<CTokenlizedFile::ColumnInfo>::insert(std::vector<CTokenlizedFile::ColumnInfo> *this, std::vector<CTokenlizedFile::ColumnInfo>::iterator *result, std::vector<CTokenlizedFile::ColumnInfo>::iterator _Where, const CTokenlizedFile::ColumnInfo *_Val); // idb
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::push_back(std::vector<CTokenlizedFile::ColumnInfo> *this, const CConsoleCMDFactory::StringCMD *_Val);
void __thiscall CTokenlizedFile::CTokenlizedFile(CTokenlizedFile *this, const char *lpszDelimiter); // idb
char __thiscall CTokenlizedFile::ReadColumn(CTokenlizedFile *this);
char __thiscall CTokenlizedFile::ReadLine(CTokenlizedFile *this);
void __thiscall Item::CItemType::SetUseItemTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo); // idb
void __thiscall Item::CItemType::SetEtcItemTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo); // idb
__int16 __cdecl GetEquipType(Item::ItemType::Type eItemType); // idb
void __cdecl Item::CItemType::GetInstallGemAttribute(const unsigned __int8 *cSockets_In, Item::ItemAttribute *cAttributes_Out, Item::ItemType::Type eItemType, unsigned __int8 cSocketNum, unsigned __int8 cMaxAttributeNum); // idb
void __cdecl Item::CItemType::GetUpgradeItemAttribute(Item::ItemType::Type eItemType, Item::ItemAttribute *cAttributes_Out, __int16 cUpgradeLevel);
void __thiscall CSingleton<Item::CItemType>::~CSingleton<Item::CItemType>(CSingleton<Item::CItemType> *this); // idb
void __cdecl std::_Median<std::vector<enum Item::ItemType::Type>::iterator>(std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Mid, std::vector<enum Item::ItemType::Type>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(std::vector<enum Item::ItemType::Type>::iterator _First, int _Hole, int _Bottom, Item::ItemType::Type _Val); // idb
std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> *__cdecl std::_Unguarded_partition<std::vector<enum Item::ItemType::Type>::iterator>(std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> *result, std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last); // idb
std::vector<enum Item::ItemType::Type>::iterator *__cdecl std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(std::vector<enum Item::ItemType::Type>::iterator *result, std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last, const unsigned __int8 *_Val);
void __cdecl std::_Make_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last);
void __cdecl std::_Insertion_sort<std::vector<enum Item::ItemType::Type>::iterator>(std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last); // idb
void __cdecl std::sort_heap<std::vector<enum Item::ItemType::Type>::iterator>(std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last); // idb
void __thiscall Item::CItemType::~CItemType(Item::CItemType *this); // idb
BOOL __thiscall Item::CItemType::IsCorrectItemType(Item::CItemType *this, Item::CItemType::ArrayType itemType, unsigned __int8 cEquipType);
unsigned __int8 __thiscall Item::CItemType::ConvertRandomOptionType(Item::CItemType *this, unsigned __int8 cEquipType); // idb
void __thiscall Item::CItemType::SetEquipTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo); // idb
void __thiscall __noreturn std::vector<enum Item::ItemType::Type>::_Xlen(std::vector<enum Item::ItemType::Type> *this);
void __thiscall std::vector<enum Item::ItemType::Type>::_Insert_n(std::vector<enum Item::ItemType::Type> *this, std::vector<enum Item::ItemType::Type>::iterator _Where, unsigned int _Count, const Item::ItemType::Type **_Val);
void __cdecl std::_Sort<std::vector<enum Item::ItemType::Type>::iterator,int>(std::vector<enum Item::ItemType::Type>::iterator _First, std::vector<enum Item::ItemType::Type>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<enum Item::ItemType::Type>::reserve(std::vector<enum Item::ItemType::Type> *this, unsigned int _Count); // idb
void __thiscall std::vector<enum Item::ItemType::Type>::push_back(std::vector<enum Item::ItemType::Type> *this, Item::ItemType::Type *_Val); // idb
void __thiscall Item::CItemType::CItemType(Item::CItemType *this); // idb
char __cdecl ReadString(char *szBuffer_Out, int nBufferLen, const char *szValue_In);
char __cdecl ReadStringToTypeValue(unsigned __int8 *cVaule_Out, const CTypeName *lpTypeArray, unsigned __int8 nMaxType, const char *szValue_In);
char __cdecl ReadID(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadItemName(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadFieldModelName(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadAttachedModelName(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSpriteDDS(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMinX(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMinY(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaxX(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaxY(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSizeX(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSizeY(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadEffectSound(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadItemLevel(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadTypeName(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPrice(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadBlackPrice(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMedalPrice(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadOptionLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDurability(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaxDurability(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadClassLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSkillLimitType(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSkillLimitLevel(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadLevelLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSTRLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDEXLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadCONLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadINTLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadWISLimit(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadCraftExp(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadCriticalType(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadCriticalPercentage(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMinDamage(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaxDamage(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDRC(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadOffenceRevision(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDefence(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDefenceRevision(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadBlockingPercentage(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadAttackSpeed(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMoveSpeed(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadAttackRange(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadRangedAttack(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadHpMaxPlus(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMpMaxPlus(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadHPRegen(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMPRegen(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadFireAttribute(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadLightingAttribute(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadColdAttribute(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDrainAttribute(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPoisonAttribute(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadFireResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadLightningResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadColdResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDrainResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPoisonResistance(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSTRAdd(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadDEXAdd(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadCONAdd(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadINTAdd(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadWISAdd(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaterialValue(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadMaxSocketNum(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSkill_Level(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadZone(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPositionX(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPositionY(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadPositionZ(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadAmount(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadItemDescribe(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadItemType(Item::ItemInfo *itemInfo, const char *szValue);
char __cdecl ReadSkill_ID(Item::ItemInfo *itemInfo, char *szValue);
std::pair<unsigned long,unsigned long> *__cdecl std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, std::pair<unsigned long,unsigned long> *_Dest); // idb
void __thiscall std::vector<ItemDataParser::ParseData>::_Insert_n(std::vector<ItemDataParser::ParseData> *this, std::vector<ItemDataParser::ParseData>::iterator _Where, unsigned int _Count, const ItemDataParser::ParseData *_Val); // idb
void __thiscall std::vector<ItemDataParser::ParseData>::reserve(std::vector<ItemDataParser::ParseData> *this, unsigned int _Count); // idb
void __thiscall std::vector<ItemDataParser::ParseData>::push_back(std::vector<ItemDataParser::ParseData> *this, const std::pair<unsigned long,unsigned long> *_Val);
char __cdecl ItemDataParser::SetDefaultData(std::vector<ItemDataParser::ParseData> *parseDataArray);
char __cdecl ItemDataParser::SetEquipData(std::vector<ItemDataParser::ParseData> *parseDataArray);
char __cdecl ItemDataParser::SetUseItemData(std::vector<ItemDataParser::ParseData> *parseDataArray);
char __cdecl ItemDataParser::SetEtcItemData(std::vector<ItemDataParser::ParseData> *parseDataArray);
CLotteryEvent::LotteryEventItem *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size(std::vector<CLotteryEvent::LotteryEventItem> *this);
CLotteryEvent::LotteryEventPrize *__cdecl std::copy_backward<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(CLotteryEvent::LotteryEventPrize *_First, CLotteryEvent::LotteryEventPrize *_Last, CLotteryEvent::LotteryEventPrize *_Dest); // idb
CLotteryEvent::LotteryEventPrize *__cdecl std::copy<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(CLotteryEvent::LotteryEventPrize *_First, CLotteryEvent::LotteryEventPrize *_Last, CLotteryEvent::LotteryEventPrize *_Dest); // idb
unsigned __int16 __thiscall CLotteryEvent::GetLottery(CLotteryEvent *this); // idb
void __thiscall CLotteryEvent::PrizeLottery(CLotteryEvent *this, unsigned __int16 usLotteryID);
void __cdecl std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(CLotteryEvent::LotteryEventPrize *_First, unsigned int _Count, const CLotteryEvent::LotteryEventPrize *_Val);
unsigned int *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Ucopy<CLotteryEvent::LotteryEventPrize *>(std::allocator<unsigned long> *_Al, std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last, unsigned int *_Dest);
CLotteryEvent::LotteryEventPrize *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Ufill(std::vector<CLotteryEvent::LotteryEventPrize> *this, CLotteryEvent::LotteryEventPrize *_Ptr, unsigned int _Count, const CLotteryEvent::LotteryEventPrize *_Val); // idb
void __thiscall __noreturn std::vector<CLotteryEvent::LotteryEventItem>::_Xlen(std::vector<CLotteryEvent::LotteryEventItem> *this);
void __thiscall Quest::PhaseNode::~PhaseNode(Quest::PhaseNode *this); // idb
char __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Buy(std::vector<CLotteryEvent::LotteryEventPrize> *this, unsigned int _Capacity);
void __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Insert_n(std::vector<CLotteryEvent::LotteryEventPrize> *this, std::vector<CLotteryEvent::LotteryEventPrize>::iterator _Where, unsigned int _Count, const CLotteryEvent::LotteryEventPrize **_Val);
void __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::vector<CLotteryEvent::LotteryEventPrize>(std::vector<CLotteryEvent::LotteryEventPrize> *this, const std::vector<CLotteryEvent::LotteryEventPrize> *_Right); // idb
std::allocator<unsigned long> *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::operator=(std::allocator<unsigned long> *_Al, int __formal);
CLotteryEvent::LotteryEventItem *__cdecl std::_Copy_backward_opt<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *>(CLotteryEvent::LotteryEventItem *_First, CLotteryEvent::LotteryEventItem *_Last, CLotteryEvent::LotteryEventItem *_Dest);
void __cdecl std::fill<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem>(CLotteryEvent::LotteryEventItem *_First, CLotteryEvent::LotteryEventItem *_Last, const CLotteryEvent::LotteryEventItem *_Val); // idb
CLotteryEvent::LotteryEventItem *__cdecl std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(CLotteryEvent::LotteryEventItem *_First, CLotteryEvent::LotteryEventItem *_Last, CLotteryEvent::LotteryEventItem *_Dest);
void __cdecl std::_Uninit_fill_n<CLotteryEvent::LotteryEventItem *,unsigned int,CLotteryEvent::LotteryEventItem,std::allocator<CLotteryEvent::LotteryEventItem>>(CLotteryEvent::LotteryEventItem *_First, unsigned int _Count, const CLotteryEvent::LotteryEventItem *_Val);
CLotteryEvent::LotteryEventItem *__thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Ufill(std::vector<CLotteryEvent::LotteryEventItem> *this, CLotteryEvent::LotteryEventItem *_Ptr, unsigned int _Count, const CLotteryEvent::LotteryEventItem *_Val); // idb
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Destroy(std::vector<CLotteryEvent::LotteryEventItem> *this, CLotteryEvent::LotteryEventItem *_First, CLotteryEvent::LotteryEventItem *_Last); // idb
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Insert_n(std::vector<CLotteryEvent::LotteryEventItem> *this, std::vector<CLotteryEvent::LotteryEventItem>::iterator _Where, unsigned int _Count, const CLotteryEvent::LotteryEventItem *_Val); // idb
std::vector<CLotteryEvent::LotteryEventItem>::iterator *__thiscall std::vector<CLotteryEvent::LotteryEventItem>::insert(std::vector<CLotteryEvent::LotteryEventItem> *this, std::vector<CLotteryEvent::LotteryEventItem>::iterator *result, std::vector<CLotteryEvent::LotteryEventItem>::iterator _Where, const CLotteryEvent::LotteryEventItem *_Val); // idb
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Tidy(std::vector<CLotteryEvent::LotteryEventItem> *this); // idb
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::push_back(std::vector<CLotteryEvent::LotteryEventItem> *this, const CLotteryEvent::LotteryEventItem *_Val); // idb
void __thiscall CLotteryEvent::CLotteryEvent(CLotteryEvent *this); // idb
void __thiscall CLotteryEvent::~CLotteryEvent(CLotteryEvent *this); // idb
char __thiscall CLotteryEvent::Initialize(CLotteryEvent *this);
CLotteryEvent *__thiscall CLotteryEvent::`vector deleting destructor'(CLotteryEvent *this, char a2);
// int __usercall get_byte@<eax>(gz_stream *s@<esi>); idb
// void __usercall check_header(gz_stream *s@<eax>);
// int __usercall destroy@<eax>(tagEntry *s@<esi>);
int __cdecl gzwrite(void *file, const unsigned __int8 *buf, unsigned int len);
// int __usercall do_flush@<eax>(void *file@<eax>, unsigned int flush);
// void __usercall putLong(_iobuf *file@<ebx>, unsigned int x@<eax>);
int __cdecl gzclose(void *file); // idb
_DWORD *__cdecl gz_open(const char *path, const char *mode, int fd);
_DWORD *__cdecl gzopen(const char *path, const char *mode);
char __cdecl SendLogPacket::ServerLogin(CSendStream *LogSendStream, unsigned int dwServerID);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Rootnode); // idb
CPacketDispatch *__thiscall CMultiDispatch::GetDispatch(CMultiDispatch *this, unsigned int dispatchKey); // idb
void __thiscall CMultiDispatch::Storage::Storage(CMultiDispatch::Storage *this, CMultiDispatch *multiDispatch, unsigned int dispatchKey); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Last); // idb
void __thiscall CMultiDispatch::InternalRemoveDispatch(CMultiDispatch *this, unsigned int dispatchKey); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Where, std::pair<unsigned long const ,unsigned long> *_Val);
char __thiscall CMultiDispatch::SetDispatch(CMultiDispatch *this, unsigned int dispatchKey, CPacketDispatch *lpDispatch);
void __thiscall CMultiDispatch::RemoveDispatch(CMultiDispatch *this, unsigned int dispatchKey); // idb
void __thiscall std::map<unsigned long,CPacketDispatch *>::~map<unsigned long,CPacketDispatch *>(std::map<unsigned long,CPacketDispatch *> *this); // idb
void __thiscall CMultiDispatch::~CMultiDispatch(CMultiDispatch *this); // idb
void __thiscall CMultiDispatch::CMultiDispatch(CMultiDispatch *this); // idb
char __cdecl GameClientSendPacket::SendCharBGServerMoveZone(CSendStream *SendStream, unsigned __int8 cZone, unsigned __int8 cMoveType, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharBGServerMileageChange(CSendStream *SendStream, unsigned int dwCID, unsigned __int8 cGroup, unsigned __int8 cCmd, unsigned int dwGold, unsigned int dwMileage, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendRegularServerList(CSendStream *SendStream);
CCrc32 *__thiscall CCrc32::CCrc32(CCrc32 *this); // idb
void __thiscall CCrc32::~CCrc32(CCrc32 *__hidden this); // idb
char __thiscall CCrc32::Crc32Mem(CCrc32 *this, unsigned __int8 *a2, unsigned int a3, unsigned int *a4);
char __thiscall CCrc32::Crc32MemCont(CCrc32 *this, unsigned __int8 *a2, unsigned int a3, unsigned int *a4);
void __thiscall OleDB::OleDB(OleDB *this); // idb
void __thiscall OleDB::~OleDB(OleDB *this); // idb
char __thiscall OleDB::HandleError(OleDB *this, int ErrorLine_In, HRESULT hResult_In, char *Buffer_In);
char __thiscall OleDB::CreateSession(OleDB *this);
char __thiscall OleDB::DBCreateCommand(OleDB *this);
char __thiscall OleDB::AllocResultCols(OleDB *this, IUnknown *lpIUnknown_In, OleDB::_RESULT_COLS *Rsult_Cols);
char __thiscall OleDB::ReleaseResultCols(OleDB *this, IUnknown *lpIUnknown_In, OleDB::_RESULT_COLS *Rsult_Cols);
char __thiscall OleDB::SetConnectionProperties(OleDB *this, const char *ServerName_In, const char *DataBaseName_In, const char *UserID_In, const char *UserPass_In);
tagDBBINDING *__thiscall OleDB::AllocBindGetData(OleDB *this, int ColsNum_In, tagDBBINDING *pDBColumnInfo_In);
OleDB *__thiscall OleDB::`scalar deleting destructor'(OleDB *this, char a2);
char __thiscall OleDB::ConnectSQLServer(OleDB *this, const char *ServerName_In, const char *DataBaseName_In, const char *UserID_In, const char *UserPass_In, OleDB::ConnType ConnType_In);
// char __userpurge OleDB::ExcuteQueryGetData@<al>(OleDB *this@<ecx>, int a2@<ebx>, IUnknown *Query_In, IRowset *Buffer_Out, void *a5);
char __cdecl DBAgentPacketParse::ParseCastleInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCampInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCreateCamp(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCreateSiegeArms(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
bool __cdecl DBAgentPacketParse::ParseCastleCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase); // idb
bool __cdecl DBAgentPacketParse::ParseCampCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase); // idb
char __cdecl DBAgentPacketParse::ParseSiegeArmsCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCastleRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCampRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseSiegeTimeInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCastleUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCreateGuild(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildMark(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildLevel(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildRight(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildInclination(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildSafe(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseGuildMemberInfoUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
unsigned __int8 __thiscall CRankingMgr::GetRank(CRankingMgr *this, const char *szCharName, unsigned __int8 cClass); // idb
void __cdecl std::_Push_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(RankingNode *_First, int _Hole, int _Top, RankingNode _Val);
void __cdecl std::swap<RankingNode>(RankingNode *_Left, RankingNode *_Right); // idb
void __cdecl std::_Rotate<RankingNode *,int,RankingNode>(RankingNode *_First, RankingNode *_Mid, RankingNode *_Last);
void __thiscall CRankingMgr::CRankingMgr(CRankingMgr *this); // idb
void __thiscall CRankingMgr::~CRankingMgr(CRankingMgr *this); // idb
void __cdecl std::_Adjust_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(RankingNode *_First, int _Hole, int _Bottom, RankingNode _Val);
void __cdecl std::_Make_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last);
void __cdecl std::_Pop_heap<RankingNode *,int,RankingNode,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last, RankingNode *_Dest, RankingNode _Val);
void __cdecl std::_Median<RankingNode *,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Mid, RankingNode *_Last);
std::pair<RankingNode *,RankingNode *> *__cdecl std::_Unguarded_partition<RankingNode *,RankingNode::ComparePoint>(std::pair<RankingNode *,RankingNode *> *result, RankingNode *_First, RankingNode *_Last);
void __cdecl std::_Insertion_sort<RankingNode *,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last);
void __cdecl std::sort_heap<RankingNode *,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last);
void __cdecl std::_Sort<RankingNode *,int,RankingNode::ComparePoint>(RankingNode *_First, RankingNode *_Last, int _Ideal, RankingNode::ComparePoint _Pred); // idb
RankingNode *__cdecl std::_Partial_sort_copy<RankingNode *,RankingNode *,int,RankingNode,RankingNode::ComparePoint>(RankingNode *_First1, RankingNode *_Last1, RankingNode *_First2, RankingNode *_Last2);
RankingNode *__cdecl std::partial_sort_copy<RankingNode *,RankingNode *,RankingNode::ComparePoint>(RankingNode *_First1, RankingNode *_Last1, RankingNode *_First2, RankingNode *_Last2);
void __thiscall CRankingMgr::UpdateRanking(CRankingMgr *this, const RankingNode *node); // idb
char __thiscall CRankingMgr::SendRankingInfo(CRankingMgr *this, unsigned int dwCID, unsigned __int8 cClass, unsigned __int8 cPage);
char __thiscall VirtualArea::CBGServerMgr::Enter(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter, unsigned __int16 wMapIndex, int cMoveType);
char __thiscall VirtualArea::CBGServerMgr::Leave(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter);
VirtualArea::CBGServerMap *__thiscall VirtualArea::CBGServerMgr::GetVirtualArea(VirtualArea::CBGServerMgr *this, unsigned __int16 wMapIndex);
char __thiscall VirtualArea::CBGServerMgr::SendMapList(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter);
char __thiscall VirtualArea::CBGServerMgr::SendResultList(VirtualArea::CBGServerMgr *this, CCharacter *lpCharacter);
char __thiscall VirtualArea::CBGServerMgr::SendMapListToAllCharacter(VirtualArea::CBGServerMgr *this);
char __thiscall VirtualArea::CBGServerMgr::LoginAllMonster(VirtualArea::CBGServerMgr *this);
void __thiscall VirtualArea::CBGServerMgr::ProcessAllMonster(VirtualArea::CBGServerMgr *this); // idb
void __thiscall VirtualArea::CBGServerMgr::ProcessMonsterRegenHPAndMP(VirtualArea::CBGServerMgr *this); // idb
void __thiscall VirtualArea::CBGServerMgr::ProcessSummonMonsterDead(VirtualArea::CBGServerMgr *this); // idb
void __thiscall VirtualArea::CBGServerMgr::ProcessDeleteItem(VirtualArea::CBGServerMgr *this); // idb
char __thiscall VirtualArea::CBGServerMgr::ProcessAllCellPrepareBroadCast(VirtualArea::CBGServerMgr *this);
char __thiscall VirtualArea::CBGServerMgr::ProcessAllCellBroadCast(VirtualArea::CBGServerMgr *this);
Quest::QuestNode **__thiscall std::vector<Quest::EventNode *>::_Ufill(std::vector<Quest::QuestNode *> *this, Quest::QuestNode **_Ptr, unsigned int _Count, Quest::QuestNode *const *_Val); // idb
void __thiscall VirtualArea::CBGServerMgr::Process(VirtualArea::CBGServerMgr *this); // idb
void __thiscall VirtualArea::CBGServerMgr::DestroyBGServerMap(VirtualArea::CBGServerMgr *this); // idb
void __thiscall VirtualArea::CBGServerMgr::~CBGServerMgr(VirtualArea::CBGServerMgr *this); // idb
void __thiscall __noreturn std::vector<VirtualArea::CBGServerMap *>::_Xlen(std::vector<VirtualArea::CBGServerMap *> *this);
void __thiscall std::vector<VirtualArea::CBGServerMap *>::_Insert_n(std::vector<VirtualArea::CBGServerMap *> *this, std::vector<VirtualArea::CBGServerMap *>::iterator _Where, unsigned int _Count, VirtualArea::CBGServerMap *const *_Val); // idb
VirtualArea::CBGServerMgr *__cdecl VirtualArea::CBGServerMgr::GetInstance(); // idb
char __thiscall VirtualArea::CBGServerMgr::CreateBGServerMap(VirtualArea::CBGServerMgr *this);
void __thiscall VirtualArea::ResultInfo::Initialize(VirtualArea::ResultInfo *this); // idb
void __thiscall VirtualArea::ResultInfo::ResultInfo(VirtualArea::ResultInfo *this); // idb
void __thiscall VirtualArea::MapInfo::Initialize(VirtualArea::MapInfo *this); // idb
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Node::_Node(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rarg, const std::pair<std::string const ,unsigned char> *_Val, char _Carg); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rarg, const std::pair<std::string const ,unsigned char> *_Val, char _Carg); // idb
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode, const std::pair<std::string const ,unsigned char> *_Val); // idb
std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::insert(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator,bool> *result, std::pair<std::string const ,unsigned char> *_Val); // idb
void __thiscall VirtualArea::MapInfo::MapInfo(VirtualArea::MapInfo *this, unsigned __int8 cMapType); // idb
void __thiscall VirtualArea::MapTypeMatching::MapTypeMatching(VirtualArea::MapTypeMatching *this); // idb
VirtualArea::CDungeonMgr *__cdecl VirtualArea::CDungeonMgr::GetInstance(); // idb
VirtualArea::CDuelMgr *__cdecl VirtualArea::CDuelMgr::GetInstance(); // idb
void __thiscall CMonster::AttackAction(CMonster *this); // idb
bool __thiscall CMonster::IsReturn(CMonster *this); // idb
void __thiscall CSkillMonster::CastingAttackAction(CSkillMonster *this); // idb
void __thiscall CSkillMonster::SkillAttackAction(CSkillMonster *this); // idb
Position *__thiscall CMonster::CalculateCoor(CMonster *this, Position *result); // idb
void __thiscall CMonster::ReturnBehavior(CMonster *this, unsigned int dwTick); // idb
void __thiscall CMonster::EscapeBehavior(CMonster *this); // idb
void __thiscall CMonster::DeadBehavior(CMonster *this, unsigned int dwTick); // idb
void __thiscall CSummonMonster::ReturnBehavior(CSummonMonster *this, unsigned int dwTick); // idb
void __thiscall CStatue::AttackBehavior(CStatue *this, unsigned int dwTick); // idb
void __thiscall CStatue::ReturnBehavior(CStatue *this, unsigned int dwTick); // idb
void __thiscall CMonster::WalkAttackAction(CMonster *this, float fVelocity); // idb
void __thiscall CMonster::RunAction(CMonster *this, float fDistance, float fDstX, float fDstZ); // idb
void __thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *_Rootnode); // idb
void __thiscall CMonster::SearchPlayer(CMonster *this); // idb
void __thiscall CStatue::NormalBehavior(CStatue *this, unsigned int dwTick); // idb
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::insert(std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator,bool> *result, std::pair<float const ,CAggresiveCreature *> *_Val); // idb
std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const,CAggresiveCreature *>>,0>>::erase(std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> > *this, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<float,CAggresiveCreature *,std::less<float>,std::allocator<std::pair<float const ,CAggresiveCreature *> >,0> >::iterator _Last); // idb
void __thiscall CMonster::AttackBehavior(CMonster *this, unsigned int dwTick); // idb
void __thiscall CSummonMonster::AttackBehavior(CSummonMonster *this, unsigned int dwTick); // idb
void __thiscall CSkillMonster::AttackBehavior(CSkillMonster *this, unsigned int dwTick); // idb
void __thiscall CMageMonster::AttackBehavior(CMageMonster *this, unsigned int dwTick); // idb
void __thiscall CAcolyteMonster::AttackBehavior(CAcolyteMonster *this, unsigned int dwTick); // idb
void __thiscall std::map<float,CAggresiveCreature *>::~map<float,CAggresiveCreature *>(std::map<float,CAggresiveCreature *> *this); // idb
char __thiscall CMonster::CheckPartyTarget(CMonster *this);
void __thiscall CMonster::NormalBehavior(CMonster *this, unsigned int dwTick); // idb
void __thiscall CSummonMonster::NormalBehavior(CSummonMonster *this, unsigned int dwTick); // idb
void __thiscall CSkillMonster::NormalBehavior(CSkillMonster *this, unsigned int dwTick); // idb
void __cdecl std::fill_n<float *,float,float>(float *_First, float _Count, const float *_Val); // idb
void __thiscall MonsterInfo::MonsterInfo(MonsterInfo *this); // idb
unsigned int __cdecl MonsterInfo::GetMonsterPattern(const char *szMonsterType);
char __thiscall CCharacter::HasQuest(CCharacter *this, unsigned __int16 wQuestID);
char __thiscall CCharacter::GiveQuest(CCharacter *this, Quest::QuestNode *lpQuestNode);
char __thiscall CCharacter::CancelQuest(CCharacter *this, unsigned __int16 wQuestID);
void __thiscall CCharacter::EventAward(CCharacter *this, unsigned int dwExp, unsigned int dwGold); // idb
char __thiscall CCharacter::HasExecutingQuest(CCharacter *this, unsigned __int16 wQuestID);
char __thiscall CCharacter::HasHistoryQuest(CCharacter *this, unsigned __int16 wQuestID);
char __thiscall CCharacter::InsertHistoryQuest(CCharacter *this, unsigned __int16 wQuestID);
void __thiscall CCharacter::EventEnd(CCharacter *this, unsigned __int8 cQuestIndex); // idb
char __thiscall CCharacter::DeleteHistoryQuest(CCharacter *this, unsigned __int16 wQuestID);
char __thiscall CCharacter::ClearGarbage(CCharacter *this, const std::vector<Item::ItemGarbage> *vecItemGarbage);
void __thiscall CCharacter::CheckTrigger(CCharacter *this, unsigned __int8 cTriggerKind, unsigned __int16 wReferenceID, Position Pos, __int16 wCount); // idb
char __thiscall CCharacter::StartPhase(CCharacter *this, unsigned __int16 wQuestID, char cPhase);
int __thiscall CCharacter::ExecuteEvent(CCharacter *this, unsigned __int8 cQuestIndex, Quest::TriggerNode *triggerNode, Position Pos);
unsigned __int16 __thiscall CCharacter::OperateTrigger(CCharacter *this, unsigned __int16 wQuestID, unsigned __int8 cPhase, unsigned __int8 cTrigger, unsigned __int8 cCount, Position Pos); // idb
void __thiscall CSingleton<CQuestMgr>::~CSingleton<CQuestMgr>(CSingleton<CQuestMgr> *this); // idb
void __cdecl std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(Quest::QuestNode **_First, Quest::QuestNode **_Last, Quest::QuestNode **_Val);
void __cdecl std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(Quest::QuestNode **_First, Quest::QuestNode **_Last, Quest::QuestNode **_Dest);
void __cdecl std::_Med3<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Mid, std::vector<Quest::QuestNode *>::iterator _Last);
int __stdcall std::vector<unsigned long>::_Ucopy<unsigned long *>(unsigned __int8 *src, int a2, unsigned __int8 *dst); // idb
void __cdecl std::_Median<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Mid, std::vector<Quest::QuestNode *>::iterator _Last);
void __cdecl std::_Adjust_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, int _Hole, int _Bottom, Quest::QuestNode *_Val);
std::vector<Quest::QuestNode *>::iterator *__cdecl std::_Lower_bound<std::vector<Quest::QuestNode *>::iterator,unsigned short,int,CompareQuestIDForSearch>(std::vector<Quest::QuestNode *>::iterator *result, std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last, unsigned __int16 *_Val);
std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(std::pair<std::vector<Quest::QuestNode *>::iterator,std::vector<Quest::QuestNode *>::iterator> *result, std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last);
void __cdecl std::_Make_heap<std::vector<Quest::QuestNode *>::iterator,int,Quest::QuestNode *,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last);
void __thiscall Quest::QuestNode::~QuestNode(Quest::QuestNode *this); // idb
void __cdecl std::_Insertion_sort<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last);
Quest::QuestNode *__thiscall CQuestMgr::GetQuestNode(CQuestMgr *this, unsigned __int16 wQuestID); // idb
void __cdecl std::sort_heap<std::vector<Quest::QuestNode *>::iterator,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last);
void __thiscall CQuestMgr::ClearQuest(CQuestMgr *this); // idb
void __thiscall __noreturn std::vector<Quest::QuestNode *>::_Xlen(std::vector<Quest::QuestNode *> *this);
void __cdecl std::_Sort<std::vector<Quest::QuestNode *>::iterator,int,CompareQuestIDForSort>(std::vector<Quest::QuestNode *>::iterator _First, std::vector<Quest::QuestNode *>::iterator _Last, int _Ideal, CompareQuestIDForSort _Pred); // idb
void __thiscall CQuestMgr::~CQuestMgr(CQuestMgr *this); // idb
void __thiscall std::vector<Quest::EventNode *>::_Insert_n(std::vector<Quest::PhaseNode *> *this, std::vector<Quest::PhaseNode *>::iterator _Where, unsigned int _Count, Quest::PhaseNode *const *_Val); // idb
void __thiscall std::vector<Quest::QuestNode *>::_Insert_n(std::vector<Quest::QuestNode *> *this, std::vector<Quest::QuestNode *>::iterator _Where, unsigned int _Count, Quest::QuestNode *const *_Val); // idb
void __thiscall CQuestMgr::SortQuests(CQuestMgr *this); // idb
void __thiscall CQuestMgr::CQuestMgr(CQuestMgr *this); // idb
void __thiscall std::vector<Quest::TriggerNode *>::push_back(std::vector<Quest::PhaseNode *> *this, Quest::PhaseNode **_Val);
char __thiscall CQuestMgr::AddQuest(CQuestMgr *this, Quest::QuestNode *questNode);
char __thiscall CQuestMgr::AddPhase(CQuestMgr *this, Quest::PhaseNode *phaseNode);
char __thiscall CQuestMgr::AddTrigger(CQuestMgr *this, Quest::TriggerNode *triggerNode);
char __thiscall CQuestMgr::AddEvent(CQuestMgr *this, Quest::EventNode *eventNode);
char __thiscall CSiegeObject::SkillAttack(CSiegeObject *this);
void __thiscall CSiegeObject::SetACTByObjectType(CSiegeObject *this); // idb
char __thiscall CSiegeObject::CastleArmsDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
char __thiscall CSiegeObject::SiegeArmsDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
void __thiscall CSiegeObject::AttackBehavior(CSiegeObject *this, unsigned int dwTick); // idb
void __thiscall CSiegeObject::UpdateObjectInfo(CSiegeObject *this, bool bFullHP); // idb
void __thiscall CSiegeObject::UpgradeByEmblem(CSiegeObject *this, unsigned __int8 cUpgradeType, unsigned __int8 cUpgradeStep); // idb
void __thiscall CSiegeObject::DegradeByEmblem(CSiegeObject *this, unsigned __int8 cUpgradeType, unsigned __int8 cUpgradeStep); // idb
void __thiscall CSiegeObject::SearchAirShip(CSiegeObject *this); // idb
char __thiscall CSiegeObject::CampDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
char __thiscall CSiegeObject::EmblemDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
char __thiscall CSiegeObject::GateDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
char __thiscall CSiegeObject::GuardDead(CSiegeObject *this, CAggresiveCreature *pOffencer);
char __thiscall CSiegeObject::Dead(CSiegeObject *this, CAggresiveCreature *pOffencer);
void __thiscall CSiegeObject::SearchEnemyPlayer(CSiegeObject *this); // idb
void __thiscall CSiegeObject::NormalBehavior(CSiegeObject *this, unsigned int dwTick); // idb
// int __usercall lzo1x_1_do_compress@<eax>(unsigned int in_len@<eax>, const unsigned __int8 *in, unsigned __int8 *out, unsigned int *out_len, _DWORD *wrkmem);
int __cdecl lzo1x_1_compress(const unsigned __int8 *in, unsigned int in_len, unsigned __int8 *out, unsigned int *out_len, _DWORD *wrkmem);
int __cdecl lzo1x_decompress_safe(const unsigned __int8 *in, unsigned __int8 *in_len, unsigned __int8 *out, unsigned int *out_len);
char __cdecl GameClientSendPacket::SendCharAdminCmdToDBAgent(CSendStream *AgentSendStream, PktBase *lpPktBase);
void __cdecl LogGuild(unsigned __int8 cCommand, unsigned __int8 cType, unsigned int dwGID, unsigned int dwSrcCID, unsigned int dwDstCID, const char *szExtraData, unsigned __int16 usExtraDataSize, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildCreate(unsigned __int8 cType, unsigned int dwGID, unsigned int dwCreateCID, unsigned int dwCreateGold, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildJoin(unsigned __int8 cType, unsigned int dwGID, unsigned int dwJoinCID, unsigned int dwFirstTitle, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildLeave(unsigned __int8 cType, unsigned int dwGID, unsigned int dwPermitterCID, unsigned int dwLeaveCID, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildMemberLevelAdjust(unsigned __int8 cType, unsigned int dwGID, unsigned int dwPermitterCID, unsigned int dwMemberCID, unsigned int dwGuildMemberLevel, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildRightsChange(unsigned __int8 cType, unsigned int dwGID, unsigned int dwPermitterCID, const char *cGuildRights, unsigned __int16 usGuildRightsSize, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildLevel(unsigned __int8 cType, unsigned int dwGID, unsigned int dwPermitterCID, unsigned int dwGuildLevel, unsigned int dwPreChangeGold, unsigned int dwPostChangeGold, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildMarkChange(unsigned __int8 cType, unsigned int dwGID, unsigned int dwChangerCID, unsigned int dwPreChangeGold, unsigned int dwPostChangeGold, const char *szGuildMarkData, unsigned __int16 usGuildMarkDataSize, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildStoreGoldChange(unsigned __int8 cType, unsigned int dwGID, unsigned int dwChangerCID, unsigned int dwPreChangeGold, unsigned int dwPostChangeGold, unsigned __int16 usErrorCode); // idb
void __cdecl GAMELOG::LogGuildDispose(unsigned __int8 cType, unsigned int dwGID, char *szGuildDestroyFileName, int nGuildDestroyLine, unsigned __int16 usErrorCode);
char __cdecl GameClientSendPacket::SendCharInstallSocket(CSendStream *SendStream, unsigned int dwCharID, Item::ItemPos EquipPos, Item::ItemPos GemPos, Item::CEquipment *lpEquipment, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharItemChemical(CSendStream *SendStream, unsigned int dwCharID, Item::ItemPos PickkingPos, Item::ItemPos TargetPos, unsigned __int16 wPickkingID, char cPickkingNum, Item::CItem *lpResultItem, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharUpgradeItem(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwCurrentGold, Item::CItem *lpItem, unsigned __int8 cCurrentMineralNum, unsigned __int16 usError);
void __thiscall CVirtualMachine::SetSysVars(CVirtualMachine *this); // idb
void __thiscall CVirtualMachine::Execute(CVirtualMachine *this); // idb
int __thiscall CVirtualMachine::CallScriptFunction(CVirtualMachine *this, struct ScriptFunc a2);
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *this); // idb
_DWORD *__cdecl std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Min(_DWORD *a1);
int *__cdecl MakeFuncType(int *a1, int a2, int a3);
_BYTE *__thiscall std::istream::seekg(_BYTE *this, int a2, int a3);
void __thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *_Wherenode); // idb
char __stdcall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Erase(void **p);
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Buynode(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this); // idb
_DWORD *std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode();
_DWORD *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::equal_range(_DWORD *this, _DWORD *a2, _DWORD *a3);
void __cdecl std::_Distance<std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::iterator,unsigned int>(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a1, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *a2, _DWORD *a3);
std::istream *__thiscall std::istream::read(std::istream *_Istr, int a2, int a3);
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *this); // idb
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::_Insert(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **a2, char a3, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Parg, unsigned int *_Val);
std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *a2, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a3);
int __cdecl GetFuncPtr(_DWORD *a1, char *_Ptr, int a3);
void __thiscall CVirtualMachine::RegisterFunction(CVirtualMachine *this, char *a2, enum eDataType a3, char *_Ptr, char *a5);
int *__thiscall CVirtualMachine::GetScriptFunction(CVirtualMachine *this, int *a2, const char *a3, char *_Ptr, int a5);
int __thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::insert(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, int a2, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator a3); // idb
char *__thiscall std::ifstream::ifstream(char *this, char *_Filename, int a3, int _Prot, int a5);
int __thiscall std::ifstream::~ifstream<char,std::char_traits<char>>(std::filebuf *this);
std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node **a2, std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator a3, std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *a4);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(_DWORD *this, int **a2, int *p);
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Buynode(int a1, int a2, int a3, const std::string *_Right, char a5);
void __cdecl RegisterAllocatedMemory(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator a1); // idb
void __thiscall std::ifstream::`vbase destructor'(int this);
_DWORD *__thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::erase(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this, _DWORD *a2);
bool *__thiscall std::ifstream::`vector deleting destructor'(std::filebuf *this, char a2);
char __stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Erase(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1);
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Insert(_DWORD *this, CPacketDispatch **a2, char a3, CPacketDispatch **a4, const std::string *_Right);
void __cdecl UnregisterAllocatedMemory(void *a1); // idb
int __thiscall std::set<void *>::~set<void *>(std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *this); // idb
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::insert(_DWORD *this, int a2, const std::string *_Right);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::erase(_DWORD *this, int **a2, int *p, int *a4);
void __thiscall CVirtualMachine::Destroy(CVirtualMachine *this); // idb
void __thiscall CVirtualMachine::Create(CVirtualMachine *this, int __formal, unsigned int a3);
void __thiscall CVirtualMachine::Create(CVirtualMachine *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a2, CAggresiveCreature *a3); // idb
int __thiscall std::multimap<std::string,std::pair<SFuncType,void *>>::~multimap<std::string,std::pair<SFuncType,void *>>(int this);
void __thiscall CVirtualMachine::Create(CVirtualMachine *this, char *_Filename);
CVirtualMachine *__thiscall CVirtualMachine::CVirtualMachine(CVirtualMachine *this); // idb
void __thiscall CVirtualMachine::~CVirtualMachine(CVirtualMachine *this); // idb
void CompilerMessage2(char *format, ...);
void __cdecl SetCompilerMessageFunction(void (__cdecl *a1)(const char *)); // idb
void __cdecl __noreturn ErrorMessage(int a1, const char *a2); // idb
void __noreturn ErrorMessage2(int a1, char *format, ...);
void __noreturn ErrorMessage2(char *format, ...);
void WarningMessage2(int a1, char *format, ...);
void __cdecl ScriptSystemError(const char *a1); // idb
void __cdecl DefaultMessageFunction(char *_Val);
void __cdecl yyerror(char *); // idb
int __thiscall std::codecvt_base::do_encoding(std::codecvt_base *this); // idb
char __thiscall SNode::SemanticCheck(SNode *this, struct CSymbolTable *a2);
void __thiscall CSyntaxTree::Create(CSyntaxTree *this, char *file);
int *__thiscall CSyntaxTree::Insert(struct CSymbolTable **this, int a2, int a3, int a4, int a5, int a6);
SNode *__thiscall CSyntaxTree::Insert(struct CSymbolTable **this, int a2, int a3, int a4, int a5, int a6, int a7);
void __thiscall CSyntaxTree::Destroy(CSyntaxTree *this); // idb
void __thiscall CSyntaxTree::SetRoot(CSyntaxTree *this, int a2); // idb
CSyntaxTree *__thiscall CSyntaxTree::CSyntaxTree(CSyntaxTree *this); // idb
void __thiscall CSyntaxTree::~CSyntaxTree(CSymbolTable **this);
void __thiscall CIntermediateCode::Create(CIntermediateCode *this, CThread *_Val);
char __stdcall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Erase(void **p);
char __stdcall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Erase(void **p);
_DWORD *__stdcall CIntermediateCode::DestroyIMCodes(int a1);
void __thiscall CIntermediateCode::Destroy(CIntermediateCode *this); // idb
int __thiscall CIntermediateCode::Addressing(CIntermediateCode *this, int a2); // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *__thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *a2, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3);
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2, char a3, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Parg, std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *a4);
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator a3, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *a4);
int __thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, int a2, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator a3); // idb
struct CRelocTable *__thiscall CIntermediateCode::ToMachineCode(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **this, char *a2, struct CRelocTable *a3);
int __thiscall std::map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>::~map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this); // idb
int __thiscall std::map<int,long>::~map<int,long>(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this); // idb
CIntermediateCode *__thiscall CIntermediateCode::CIntermediateCode(CIntermediateCode *this); // idb
void __thiscall CIntermediateCode::~CIntermediateCode(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **this);
// void __usercall putShortMSB(internal_state *s@<eax>, __int16 b@<cx>);
// void __usercall flush_pending(z_stream_s *strm@<eax>);
int __cdecl deflate(z_stream_s *strm, unsigned int flush);
unsigned int __cdecl deflateEnd(z_stream_s *strm);
void __fastcall lm_init(int a1, internal_state *s);
// unsigned int __usercall longest_match@<eax>(internal_state *s@<esi>, unsigned int cur_match@<eax>); idb
// void __usercall fill_window(internal_state *s@<ebx>);
int __cdecl deflate_stored(internal_state *s, int flush);
int __cdecl deflate_fast(internal_state *s, int flush);
int __cdecl deflate_slow(internal_state *s, int flush);
int __cdecl deflateReset(z_stream_s *strm); // idb
int __cdecl deflateInit2_(z_stream_s *strm, unsigned int level, int method, int windowBits, int memLevel, unsigned int strategy, const char *version, int stream_size);
int __cdecl inflateReset(z_stream_s *z); // idb
int __cdecl inflateEnd(z_stream_s *z); // idb
int __cdecl inflateInit2_(z_stream_s *z, int w, const char *version, int stream_size); // idb
int __cdecl crc32(unsigned int crc, const unsigned __int8 *buf, unsigned int len);
void *__cdecl zcalloc(void *opaque, unsigned int items, unsigned int size); // idb
void __cdecl zcfree(void *opaque, tagEntry *ptr);
void __thiscall CRelocTable::Relocate(CRelocTable *this, void *a2, void *a3, char *a4);
void __thiscall CRelocTable::Destroy(CRelocTable *this); // idb
void __thiscall __noreturn std::vector<int>::_Xlen(_DWORD); // weak
void __thiscall std::vector<int>::_Insert_n(std::vector<Quest::QuestNode *> *this, unsigned __int8 *src, unsigned int _Count, Quest::QuestNode *_Val);
void __thiscall std::vector<std::pair<enum eStdFunc,int>>::_Insert_n(std::vector<std::pair<unsigned long,unsigned long>> *_Al, std::pair<unsigned long,unsigned long> *_Last, unsigned int _Count, std::pair<unsigned long,unsigned long> *_Dest);
CRelocTable *__thiscall CRelocTable::CRelocTable(CRelocTable *this); // idb
void __thiscall CRelocTable::~CRelocTable(CRelocTable *this); // idb
void __thiscall std::vector<std::pair<enum eStdFunc,int>>::push_back(std::vector<std::pair<unsigned long,unsigned long>> *_Al, std::pair<unsigned long,unsigned long> *_Val);
// unsigned int *__userpurge CRelocTable::Create@<eax>(CRelocTable *this@<ecx>, Quest::QuestNode __formal);
// void __userpurge CRelocTable::AddGlobalVar(CRelocTable *this@<ecx>, Quest::QuestNode _Val);
// void __userpurge CRelocTable::AddConstString(CRelocTable *this@<ecx>, Quest::QuestNode _Val);
void __thiscall CRelocTable::AddFuncBind(_DWORD *this, unsigned int a2, unsigned int a3);
void __thiscall CharacterFightInfo::CharacterFightInfo(CharacterFightInfo *this); // idb
SFuncType *__thiscall SFuncType::SFuncType(SFuncType *this, unsigned int a2); // idb
enum eDataType __thiscall CSymbolTable::GetTypeOfVar(CSymbolTable *this, int a2); // idb
int __thiscall CSymbolTable::GetOffsetOfConst(CSymbolTable *this, int a2); // idb
int __thiscall CSymbolTable::GetOffsetOfVar(CSymbolTable *this, int a2); // idb
_DWORD *__stdcall CSymbolTable::GetTypeOfFunc(_DWORD *a1, int a2);
void __thiscall CSymbolTable::EndArgument(CSymbolTable *this); // idb
int __cdecl std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Max(int a1);
_DWORD *__cdecl std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Min(_DWORD *a1);
int __cdecl std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Max(int a1);
int __thiscall SFuncType::GetArgCount(SFuncType *this); // idb
char *__thiscall SFuncType::ToString(SFuncType *this, const char *a2);
_DWORD *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(_DWORD *this, int a2);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(_DWORD *this, _DWORD *a2);
_DWORD *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(_DWORD *this, int a2);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(_DWORD *this, _DWORD *a2);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::const_iterator::_Dec(void *this);
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::const_iterator::_Inc(int *this);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Dec(void *this);
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Inc(int *this);
const char *__thiscall CSymbolTable::GetNameOfVar(CSymbolTable *this, int a2); // idb
char *__thiscall CSymbolTable::GetTypeStringOfFunc(CSymbolTable *this, int a2);
_DWORD *std::list<SLocalVarInfo>::_Buynode();
_DWORD *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode();
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lbound(_DWORD *this, int a2);
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lbound(_DWORD *this, int a2);
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Ubound(_DWORD *this, int a2);
int __thiscall CSymbolTable::GetLocalVarSize(CSymbolTable *this, int a2); // idb
int __thiscall CSymbolTable::GetGlobalVarSize(CSymbolTable *this); // idb
void __thiscall CSymbolTable::StringBuffer(CSymbolTable *this, char *a2);
std::string **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::find(_DWORD *this, std::string **a2, std::string *a3);
std::string **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::find(_DWORD *this, std::string **a2, std::string *a3);
int __thiscall std::list<SLocalVarInfo>::_Incsize(_DWORD *this, unsigned int a2);
_DWORD *__thiscall std::_Tree_nod<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Node::_Node(_DWORD *this, int a2, int a3, int a4, const std::string *_Right, char a6);
char *__thiscall std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Node::_Node(char *this, int a2, int a3, int a4, const std::string *_Right, char a6);
const char *__thiscall CSymbolTable::FindConst(CSymbolTable *this, char *_Ptr);
char **__thiscall CSymbolTable::FindVar(CSymbolTable *this, char *_Ptr);
int *__thiscall CSymbolTable::FindFunc(_DWORD *this, char *_Ptr, int a3);
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode(int a1, int a2, int a3, const std::string *_Right, char a5);
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Buynode(int a1, int a2, int a3, const std::string *_Right, char a5);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::erase(_DWORD *this, int **a2, int *p);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(_DWORD *this, int **a2, int *p);
char __stdcall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Erase(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1);
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Insert(_DWORD *this, CPacketDispatch **a2, char a3, CPacketDispatch **a4, const std::string *_Right);
char __stdcall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Erase(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1);
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Insert(_DWORD *this, CPacketDispatch **a2, char a3, CPacketDispatch **a4, const std::string *_Right);
char __stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Erase(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1);
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Copy(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **this, int *a2, int a3);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::insert(_DWORD *this, int a2, const std::string *_Right);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::insert(_DWORD *this, int a2, const std::string *_Right);
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::insert(_DWORD *this, int a2, const std::string *_Right);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::erase(_DWORD *this, int **a2, int *p, int *a4);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(_DWORD *this, int **a2, int *p, int *a4);
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::erase(_DWORD *this, int **a2, int *p, int *a4);
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Copy(int this, int a2);
const char *__thiscall CSymbolTable::AddConst(CSymbolTable *this, char *_Ptr, enum eDataType a3);
int __thiscall CSymbolTable::AddArrVar(CSymbolTable *this, char *_Ptr, enum eDataType a3, int a4);
int __thiscall std::map<std::string,VarInfo>::~map<std::string,VarInfo>(int this);
_DWORD *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>(_DWORD *this, int a2);
int *__thiscall CSymbolTable::AddFunc(_DWORD *this, char a2, char *_Ptr, int a4);
int __thiscall std::map<std::string,ConstInfo>::~map<std::string,ConstInfo>(int this);
int __thiscall std::multimap<std::string,std::pair<SFuncType,bool>>::~multimap<std::string,std::pair<SFuncType,bool>>(int this);
void __thiscall SLocalVarInfo::~SLocalVarInfo(SLocalVarInfo *this); // idb
SLocalVarInfo *__thiscall SLocalVarInfo::SLocalVarInfo(SLocalVarInfo *this, const struct SLocalVarInfo *a2); // idb
void __thiscall std::_List_nod<SLocalVarInfo>::_Node::~_Node(int this);
SLocalVarInfo *__thiscall SLocalVarInfo::SLocalVarInfo(SLocalVarInfo *this); // idb
CSymbolTable *__thiscall CSymbolTable::CSymbolTable(CSymbolTable *this); // idb
CPacketDispatch *__stdcall std::list<SLocalVarInfo>::_Buynode(CPacketDispatch_vtbl *a1, CSession *a2, struct SLocalVarInfo *a3);
_DWORD *__thiscall std::list<SLocalVarInfo>::clear(_DWORD *this);
void __thiscall CSymbolTable::Destroy(CSymbolTable *this); // idb
void __thiscall CSymbolTable::BeginLocalNameSpace(CSymbolTable *this); // idb
void __thiscall CSymbolTable::EndLocalNameSpace(CSymbolTable *this, int a2); // idb
void __thiscall CSymbolTable::~CSymbolTable(CSymbolTable *this); // idb
int __thiscall CSymbolTable::AddArrVar(enum eDataType *this, char *_Ptr, int a3);
int __thiscall CSymbolTable::AddVar(enum eDataType *this, char *_Ptr);
enum eDataType __cdecl GetType(struct SNode *a1); // idb
int *__cdecl GetFunctionType(int *a1, int a2, CharacterFightInfo a3);
int __cdecl GetFunctionType(_DWORD, _DWORD, _DWORD); // weak
_DWORD *__cdecl GetFuncCallType(_DWORD *a1, CharacterFightInfo a2);
int __cdecl CountArrayElements(int a1); // idb
int __cdecl GetConstantValue(int a1); // idb
int __cdecl yyparse(); // idb
int sub_4AFDA0();
// int __usercall sub_4AFE70@<eax>(int a1@<eax>);
void __cdecl yy_flush_buffer(struct yy_buffer_state *a1); // idb
void __cdecl yy_init_buffer(struct yy_buffer_state *a1, _iobuf *stream); // idb
struct yy_buffer_state *__cdecl yy_create_buffer(_iobuf *stream, int a2); // idb
void __cdecl yyrestart(_iobuf *stream); // idb
int sub_4B00E0();
int sub_4B02E0();
void __cdecl EatCComment(); // idb
int __cdecl yylex(); // idb
void __cdecl __noreturn GenCode_Error_Statement(int *a1);
IOPCode *__thiscall IOPCode::`vector deleting destructor'(IOPCode *this, char a2);
void __cdecl GenCode_FirstChild_Generate(int a1);
void __cdecl GenCode_SecondChild_Generate(int a1);
void __cdecl GenCode_TwoChild_Generate(int a1);
COP_test_ah *__thiscall COP_test_ah::`scalar deleting destructor'(COP_test_ah *this, char a2);
void __thiscall COP_itoa::~COP_itoa(COP_itoa *this); // idb
COP_jmp *__cdecl HandleMARK(COP_jmp *a1, COP_jmp *a2, struct IOPCode *a3, int a4);
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **__thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **a2, char a3, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Parg, std::pair<unsigned long const ,unsigned long> *_Val);
int __thiscall std::list<IOPCode *>::_Incsize(_DWORD *this, unsigned int a2);
int __thiscall std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, int a2, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator a3); // idb
int __thiscall std::list<IOPCode *>::push_back(std::list<CThread *> *this, CThread **_Val); // idb
std::_List_nod<CThread *>::_Node *__cdecl GenCode_DataTypeToString(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_If_Statement(CPacketDispatch *a1);
int __cdecl GenCode_If_Else_Statement(CPacketDispatch *a1);
COP_jmp *__cdecl GenCode_Switch_Statement(CThread *a1);
COP_jmp *__cdecl GenCode_For_Statement(CThread *a1);
COP_jmp *__cdecl GenCode_While_Statement(CThread *a1);
std::_List_nod<CThread *>::_Node *__thiscall GenCode_Break_Statement(CThread *this);
std::_List_nod<CThread *>::_Node *__thiscall GenCode_Continue_Statement(CThread *this);
int __cdecl GenCode_Return_Statement(CThread *_Val);
void __cdecl GenCode_Variable(int a1, CThread *_Val);
int __cdecl GenCode_Init_Declaration(CThread *a1);
void __cdecl GenCode_Normal_Declaration(CThread *_Val);
void __cdecl GenCode_Array_Init(struct SNode *a1, int a2); // idb
void __cdecl GenCode_Array_Initialize(struct SNode *a1);
void __cdecl GenCode_Array_Initialize2(struct SNode *a1);
int __cdecl GenCode_Function_Declaration(CThread *_Val);
COP_jmp *__cdecl GenCode_Function_Definition(CThread *_Val);
int __cdecl GenCode_Constant_Expression(int a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Assignment_Expression(CThread *a1, int a2);
int __cdecl GenCode_OR_Expression(CPacketDispatch *a1, int a2);
int __cdecl GenCode_AND_Expression(CPacketDispatch *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_NOT_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Equality_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_NotEqual_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_LessThan_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_MoreThan_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_LessThanEqual_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_MoreThanEqual_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Addition_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Subtraction_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Multiplication_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Division_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Reminder_Expression(CThread *_Val, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Compound_Addition(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Compound_Subtraction(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Compound_Multiplication(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Compound_Division(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Compound_Reminder(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_PrefixIncrement(CThread *a1, int a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_PrefixDecrement(CThread *a1, int a2);
int __cdecl GenCode_Array_Indexing(int a1, CThread *a2);
std::_List_nod<CThread *>::_Node *__cdecl GenCode_Function_Call(CThread *a1, int a2);
int __cdecl GenCode_PostfixIncrement(CThread *a1, CThread *_Val);
int __cdecl GenCode_PostfixDecrement(CThread *a1, CThread *_Val);
int __cdecl GenerateCode(CThread *_Val, CThread *a2, int a3); // idb
int __cdecl SetFuncTable(int a1);
void __cdecl SetRelocTable(struct CRelocTable *a1); // idb
Conversion *__thiscall Conversion::Conversion(Conversion *__hidden this, struct _CONST_ *, int); // idb
int __cdecl Code_RM32_Offset(unsigned __int8 *, char, int, int, int, int); // idb
int __cdecl Code_RM32(unsigned __int8 *a1, char a2, int a3, int a4, int a5, int a6); // idb
int __cdecl Code_RM32_CodeSize_Offset(int a1, int a2); // idb
int __thiscall COP_jmp::Addressing(COP_jmp *this, int a2); // idb
void __thiscall COP_call::Show(COP_call *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_mov::ToMachineCode(COP_mov *__hidden this, unsigned __int8 *); // idb
int __thiscall COP_mov::Addressing(COP_mov *this, int a2); // idb
void __thiscall COP_mov::Show(COP_mov *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_nop::ToMachineCode(COP_nop *this, unsigned __int8 *a2); // idb
void __thiscall COP_nop::Show(COP_nop *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_add::ToMachineCode(COP_add *this, unsigned __int8 *a2); // idb
void __thiscall COP_add::Show(COP_add *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_sub::ToMachineCode(COP_sub *this, unsigned __int8 *a2); // idb
int __thiscall COP_add::Addressing(COP_add *this, int a2); // idb
void __thiscall COP_sub::Show(COP_sub *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_imul::ToMachineCode(COP_imul *this, unsigned __int8 *a2); // idb
void __thiscall COP_imul::Show(COP_imul *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_idiv::ToMachineCode(COP_idiv *this, unsigned __int8 *a2); // idb
void __thiscall COP_idiv::Show(COP_idiv *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_push::ToMachineCode(COP_push *this, unsigned __int8 *a2); // idb
void __thiscall COP_push::Show(COP_push *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_pop::ToMachineCode(COP_pop *this, unsigned __int8 *a2); // idb
void __thiscall COP_pop::Show(COP_pop *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_jmp::ToMachineCode(COP_jmp *this, unsigned __int8 *a2); // idb
void __thiscall COP_jmp::Show(COP_jmp *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_jcc::ToMachineCode(COP_jcc *this, unsigned __int8 *a2); // idb
int __thiscall COP_jcc::Addressing(COP_jcc *this, int a2); // idb
void __thiscall COP_jcc::Show(COP_jcc *this, void (*a2)(const char *, ...)); // idb
unsigned int __thiscall COP_jmpmark::ToMachineCode(CCreature *this, unsigned int dwStatusFlag); // idb
int __thiscall COP_jmpmark::Addressing(COP_jmpmark *this, int a2); // idb
void __thiscall COP_jmpmark::Show(COP_jmpmark *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_cmp::ToMachineCode(COP_cmp *__hidden this, unsigned __int8 *); // idb
int __thiscall COP_cmp::Addressing(COP_cmp *this, int a2); // idb
void __thiscall COP_cmp::Show(COP_cmp *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_ret::ToMachineCode(COP_ret *this, unsigned __int8 *a2); // idb
void __thiscall COP_ret::Show(COP_ret *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_setcc::ToMachineCode(COP_setcc *this, unsigned __int8 *a2); // idb
int __thiscall COP_setcc::Addressing(COP_setcc *this, int a2); // idb
void __thiscall COP_setcc::Show(COP_setcc *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_int3::ToMachineCode(COP_int3 *this, unsigned __int8 *a2); // idb
int __thiscall COP_nop::Addressing(COP_nop *this, int a2); // idb
void __thiscall COP_int3::Show(COP_int3 *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fld::ToMachineCode(COP_fld *this, unsigned __int8 *a2); // idb
int __thiscall COP_fcomp::Addressing(COP_fcomp *this, int a2); // idb
void __thiscall COP_fld::Show(COP_fld *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fadd::ToMachineCode(COP_fadd *this, unsigned __int8 *a2); // idb
void __thiscall COP_fadd::Show(COP_fadd *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fsub::ToMachineCode(COP_fsub *this, unsigned __int8 *a2); // idb
void __thiscall COP_fsub::Show(COP_fsub *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fmul::ToMachineCode(COP_fmul *this, unsigned __int8 *a2); // idb
void __thiscall COP_fmul::Show(COP_fmul *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fdiv::ToMachineCode(COP_fdiv *this, unsigned __int8 *a2); // idb
void __thiscall COP_fdiv::Show(COP_fdiv *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fstp::ToMachineCode(COP_fstp *this, unsigned __int8 *a2); // idb
void __thiscall COP_fstp::Show(COP_fstp *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fcomp::ToMachineCode(COP_fcomp *this, unsigned __int8 *a2); // idb
void __thiscall COP_fcomp::Show(COP_fcomp *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_fnstsw_ax::ToMachineCode(COP_fnstsw_ax *this, unsigned __int8 *a2); // idb
int __thiscall COP_fnstsw_ax::Addressing(COP_fnstsw_ax *this, int a2); // idb
void __thiscall COP_fnstsw_ax::Show(COP_fnstsw_ax *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_test_ah::ToMachineCode(COP_test_ah *this, unsigned __int8 *a2); // idb
int __thiscall COP_idiv::Addressing(COP_idiv *this, int a2); // idb
void __thiscall COP_test_ah::Show(COP_test_ah *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_itoa::ToMachineCode(COP_itoa *__hidden this, unsigned __int8 *); // idb
int __thiscall COP_itoa::Addressing(COP_itoa *this, int a2); // idb
void __thiscall COP_itoa::Show(COP_itoa *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_ftoa::ToMachineCode(COP_ftoa *__hidden this, unsigned __int8 *); // idb
int __thiscall COP_ftoa::Addressing(COP_ftoa *this, int a2); // idb
void __thiscall COP_ftoa::Show(COP_ftoa *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_malloc::ToMachineCode(COP_malloc *this, unsigned __int8 *a2); // idb
int __thiscall COP_malloc::Addressing(COP_malloc *this, int a2); // idb
void __thiscall COP_malloc::Show(COP_malloc *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_free::ToMachineCode(COP_free *this, unsigned __int8 *a2); // idb
int __thiscall COP_free::Addressing(COP_free *this, int a2); // idb
void __thiscall COP_free::Show(COP_free *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_strcpy::ToMachineCode(COP_strcpy *this, unsigned __int8 *a2); // idb
int __thiscall COP_strcpy::Addressing(COP_strcpy *this, int a2); // idb
void __thiscall COP_strcpy::Show(COP_strcpy *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_strcmp::ToMachineCode(COP_strcmp *this, unsigned __int8 *a2); // idb
int __thiscall COP_strcmp::Addressing(COP_strcmp *this, int a2); // idb
void __thiscall COP_strcmp::Show(COP_strcmp *this, void (*a2)(const char *, ...)); // idb
int __thiscall COP_strlen::ToMachineCode(COP_strlen *this, unsigned __int8 *a2); // idb
int __thiscall COP_strlen::Addressing(COP_strlen *this, int a2); // idb
void __thiscall COP_strlen::Show(COP_strlen *this, void (*a2)(const char *, ...)); // idb
COP_call *__thiscall COP_call::COP_call(COP_call *this, struct _FUNC_ *a2, int a3); // idb
_BYTE *__thiscall COP_mov::COP_mov(_BYTE *this, int a2, int a3, int a4);
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3, int a4);
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3);
COP_mov *__thiscall COP_mov::COP_mov(COP_mov *this, struct _FUNC_ *a2, struct _VAR_ *a3, float a4); // idb
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3, int a4, int a5);
_DWORD *__thiscall COP_mov::COP_mov(_DWORD *this, int a2, int a3, int a4, int a5);
_BYTE *__thiscall COP_add::COP_add(_BYTE *this, int a2, int a3);
_BYTE *__thiscall COP_add::COP_add(_BYTE *this, int a2, int a3);
_BYTE *__thiscall COP_sub::COP_sub(_BYTE *this, int a2, int a3);
_BYTE *__thiscall COP_sub::COP_sub(_BYTE *this, int a2, int a3);
_DWORD *__thiscall COP_imul::COP_imul(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_idiv::COP_idiv(_DWORD *this, int a2);
_DWORD *__thiscall COP_push::COP_push(_DWORD *this, int a2);
_DWORD *__thiscall COP_pop::COP_pop(_DWORD *this, int a2);
COP_jmp *__thiscall COP_jmp::COP_jmp(COP_jmp *this); // idb
COP_jmp *__thiscall COP_jmp::COP_jmp(COP_jmp *this, struct IOPCode *a2); // idb
_DWORD *__thiscall COP_jcc::COP_jcc(_DWORD *this, int a2);
COP_jmpmark *__thiscall COP_jmpmark::COP_jmpmark(COP_jmpmark *this); // idb
COP_jmpmark *__thiscall COP_jmpmark::COP_jmpmark(COP_jmpmark *this, struct COP_jmp *a2); // idb
COP_cmp *__thiscall COP_cmp::COP_cmp(COP_cmp *this, struct _FUNC_ *a2, struct _VAR_ *a3, struct _CONST_ *a4); // idb
_BYTE *__thiscall COP_cmp::COP_cmp(_BYTE *this, int a2, char a3);
_BYTE *__thiscall COP_cmp::COP_cmp(_BYTE *this, int a2, int a3);
_DWORD *__thiscall COP_setcc::COP_setcc(_DWORD *this, int a2, int a3);
COP_fld *__thiscall COP_fld::COP_fld(COP_fld *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_fadd *__thiscall COP_fadd::COP_fadd(COP_fadd *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_fsub *__thiscall COP_fsub::COP_fsub(COP_fsub *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_fmul *__thiscall COP_fmul::COP_fmul(COP_fmul *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_fdiv *__thiscall COP_fdiv::COP_fdiv(COP_fdiv *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_fstp *__thiscall COP_fstp::COP_fstp(COP_fstp *this, struct _FUNC_ *a2, struct _VAR_ *a3, bool a4); // idb
COP_fcomp *__thiscall COP_fcomp::COP_fcomp(COP_fcomp *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
COP_test_ah *__thiscall COP_test_ah::COP_test_ah(COP_test_ah *this, unsigned __int8 a2); // idb
_DWORD *__thiscall COP_itoa::COP_itoa(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_ftoa::COP_ftoa(_DWORD *this, int a2, int a3, int a4);
_DWORD *__thiscall COP_malloc::COP_malloc(_DWORD *this, int a2);
COP_free *__thiscall COP_free::COP_free(COP_free *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
_DWORD *__thiscall COP_strcpy::COP_strcpy(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_strcpy::COP_strcpy(_DWORD *this, int a2, int a3, int a4);
_DWORD *__thiscall COP_strcmp::COP_strcmp(_DWORD *this, int a2, int a3);
_DWORD *__thiscall COP_strlen::COP_strlen(_DWORD *this, int a2);
COP_strlen *__thiscall COP_strlen::COP_strlen(COP_strlen *this, struct _FUNC_ *a2, struct _VAR_ *a3); // idb
int **__thiscall std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::find(_DWORD *this, int **a2, _DWORD *a3);
int __thiscall COP_call::ToMachineCode(COP_call *this, unsigned __int8 *a2); // idb
int __cdecl adler32(unsigned int adler, const unsigned __int8 *buf, unsigned int len);
void __fastcall init_block(int a1, internal_state *s);
// void __usercall pqdownheap(internal_state *s@<eax>, ct_data_s *tree@<edi>, int k);
// void __usercall gen_bitlen(internal_state *s@<eax>, tree_desc_s *desc@<ecx>);
// void __usercall scan_tree(ct_data_s *tree@<eax>, int max_code@<ecx>, internal_state *s);
// void __usercall send_tree(internal_state *s@<eax>, ct_data_s *tree@<ecx>, int max_code);
// void __usercall send_all_trees(internal_state *s@<eax>, int lcodes, int dcodes, int blcodes);
// void __usercall compress_block(internal_state *s@<eax>, ct_data_s *ltree, ct_data_s *dtree);
void __thiscall set_data_type(internal_state *s);
// void __usercall bi_flush(internal_state *s@<eax>);
// void __usercall bi_windup(internal_state *s@<eax>);
// void __usercall copy_block(internal_state *s@<eax>, char *buf@<edx>, int len@<ecx>, int header);
void __cdecl _tr_init(internal_state *s); // idb
// void __usercall gen_codes(ct_data_s *tree@<edi>, int max_code@<ebx>, char *bl_count@<edx>);
// void __usercall build_tree(internal_state *s@<esi>, tree_desc_s *desc);
// int __usercall build_bl_tree@<eax>(internal_state *s@<eax>); idb
void __cdecl _tr_stored_block(internal_state *s, char *buf, unsigned int stored_len, int eof); // idb
void __cdecl _tr_align(internal_state *s); // idb
void __cdecl _tr_flush_block(internal_state *s, char *buf, unsigned int stored_len, int eof); // idb
void __cdecl inflate_blocks_reset(inflate_blocks_state *s, z_stream_s *z, unsigned int *c); // idb
inflate_blocks_state *__cdecl inflate_blocks_new(z_stream_s *z, unsigned int (__cdecl *c)(unsigned int, const unsigned __int8 *, unsigned int), unsigned int w); // idb
int __cdecl inflate_blocks_free(inflate_blocks_state *s, z_stream_s *z); // idb
void __cdecl inflate_codes_free(inflate_codes_state *c, z_stream_s *z); // idb
LPVOID __cdecl operator new(tagHeader *size);
void __thiscall __noreturn std::_String_base::_Xran(std::_String_base *this);
void __thiscall __noreturn std::_String_base::_Xlen(std::_String_base *this);
void *__cdecl operator new[](unsigned int count); // idb
void __thiscall std::ios_base::_Callfns(std::ios_base *this, std::ios_base::event ev); // idb
void __thiscall std::ios_base::_Addstd(std::ios_base *this); // idb
void __thiscall std::ios_base::_Tidy(std::ios_base *this); // idb
void __thiscall std::ios_base::~ios_base(std::ios_base *this); // idb
void __thiscall std::runtime_error::~runtime_error(std::runtime_error *this); // idb
std::string::_Bxty *__thiscall std::runtime_error::what(std::runtime_error *this);
std::runtime_error *__thiscall std::runtime_error::`vector deleting destructor'(std::runtime_error *this, char a2);
void __thiscall std::runtime_error::runtime_error(std::runtime_error *this, const std::string *_Message); // idb
std::ios_base::failure *__thiscall std::ios_base::failure::`scalar deleting destructor'(std::ios_base::failure *this, char a2);
void __thiscall std::ios_base::failure::~failure(std::ios_base::failure *this); // idb
void __thiscall std::ios_base::clear(std::ios_base *this, char state, bool reraise);
void __thiscall std::runtime_error::runtime_error(std::runtime_error *this, const std::runtime_error *__that); // idb
void __thiscall std::ios_base::failure::failure(std::ios_base::failure *this, const std::ios_base::failure *__that); // idb
void __thiscall std::ios_base::_Init(std::ios_base *this); // idb
void __thiscall std::_Mutex::_Mutex(std::_Mutex *this); // idb
void __thiscall std::_Mutex::~_Mutex(std::_Mutex *this); // idb
void __thiscall std::_Mutex::_Lock(std::_Mutex *this); // idb
void __thiscall std::_Mutex::_Unlock(std::_Mutex *this); // idb
bool __cdecl std::uncaught_exception(); // idb
int __cdecl _Tolower_lk(int c, const _Ctypevec *ploc); // idb
_Ctypevec *__cdecl _Getctype(_Ctypevec *result); // idb
int __cdecl _Tolower(int c, const _Ctypevec *ploc); // idb
_Cvtvec __cdecl _Getcvt(); // idb
void __thiscall std::_Init_locks::_Init_locks(std::_Init_locks *this); // idb
void __thiscall std::_Init_locks::~_Init_locks(std::_Init_locks *this); // idb
void __thiscall std::_Lockit::_Lockit(std::_Lockit *this, char kind);
void __thiscall std::_Lockit::~_Lockit(std::_Lockit *this); // idb
void __thiscall std::_Fac_node::~_Fac_node(std::_Fac_node *this); // idb
void __cdecl _Deletegloballocale(std::locale::facet **ptr);
void __thiscall tidy_global(void *this);
void __cdecl _Setgloballocale(std::locale::_Locimp *ptr);
const std::locale::facet *__thiscall std::locale::_Getfacet(std::locale *this, unsigned int id); // idb
void __thiscall _Fac_tidy(void *this);
void __thiscall std::locale::facet::_Register(std::locale::facet *this); // idb
void __thiscall std::locale::_Locimp::~_Locimp(std::locale::_Locimp *this); // idb
void __thiscall std::_Locinfo::~_Locinfo(std::_Locinfo *this); // idb
std::locale::_Locimp *__thiscall std::locale::_Locimp::`scalar deleting destructor'(std::locale::_Locimp *this, char a2);
void __thiscall std::locale::_Locimp::_Locimp(std::locale::_Locimp *this, bool transparent); // idb
void __thiscall std::_Locinfo::_Locinfo(std::_Locinfo *this, char *locname);
std::locale::_Locimp *__cdecl std::locale::_Init(); // idb
void __thiscall std::locale::locale(std::locale *this); // idb
int __cdecl _Toupper_lk(int c, const _Ctypevec *ploc); // idb
int __cdecl _Toupper(int c, const _Ctypevec *ploc); // idb
_iobuf *__cdecl std::_Fiopen(const char *filename, int mode);
void __thiscall std::_Init_cout::_Init_cout(std::_Init_cout *this); // idb
void __cdecl __noreturn std::_Nomemory(); // idb
void __cdecl _Mtxinit(_RTL_CRITICAL_SECTION *_Mtx); // idb
void __cdecl _Mtxdst(_RTL_CRITICAL_SECTION *_Mtx); // idb
void __cdecl _Mtxlock(_RTL_CRITICAL_SECTION *_Mtx); // idb
void __cdecl _Mtxunlock(_RTL_CRITICAL_SECTION *_Mtx); // idb
void __cdecl _Atexit(void (__cdecl *pf)()); // idb
void __thiscall _Init_atexit::~_Init_atexit(_Init_atexit *this); // idb
int _snprintf(char *string, unsigned int count, char *format, ...);
char *__cdecl strtok(char *string, const char *control); // idb
void __noreturn _purecall(); // weak
void __cdecl operator delete(void *p); // idb
int __cdecl atol(const char *nptr); // idb
int __cdecl atoi(const char *nptr); // idb
unsigned __int64 __cdecl _atoi64(const char *nptr);
// void (__cdecl **__usercall onexit_lk@<eax>(void (__cdecl *func)()@<edi>))();
int __cdecl __onexitinit(); // idb
void (__cdecl **__cdecl _onexit(void (__cdecl *func)()))();
int __cdecl atexit(void (__cdecl *func)()); // idb
void __cdecl operator delete[](void *p); // idb
char *__cdecl fgets(char *string, int count, _iobuf *str); // idb
_iobuf *__cdecl _fsopen(const char *file, const char *mode, int shflag); // idb
_iobuf *__cdecl fopen(const char *file, const char *mode); // idb
void __cdecl srand(unsigned int seed); // idb
unsigned int __cdecl rand();
int __cdecl _fclose_lk(_iobuf *str); // idb
int __cdecl fclose(_iobuf *stream); // idb
void __cdecl memmove(unsigned __int8 *dst, unsigned __int8 *src, unsigned int count); // idb
char *__cdecl strncpy(char *Destination, const char *Source, size_t Count);
void __thiscall exception::exception(exception *this); // idb
void __thiscall exception::exception(exception *this, const char **what);
void __thiscall exception::exception(exception *this, const exception *that); // idb
void __thiscall exception::~exception(exception *this); // idb
const char *__thiscall exception::what(exception *this); // idb
void __thiscall bad_cast::bad_cast(bad_cast *this, const char *_Message); // idb
void __thiscall bad_cast::bad_cast(bad_cast *this, const bad_cast *that); // idb
void __thiscall bad_cast::~bad_cast(bad_cast *this); // idb
exception *__thiscall exception::`vector deleting destructor'(exception *this, char a2);
bad_cast *__thiscall bad_cast::`vector deleting destructor'(bad_cast *this, char a2);
void __thiscall type_info::~type_info(type_info *this); // idb
type_info *__thiscall type_info::`scalar deleting destructor'(type_info *this, char a2);
int fprintf(_iobuf *str, char *format, ...);
void *__cdecl operator new[](unsigned int count, const std::nothrow_t *x); // idb
void __cdecl operator delete(void *ptr);
LPVOID __cdecl operator new(tagHeader *count);
int sprintf(char *string, char *format, ...);
void __cdecl __noreturn __crtExitProcess(UINT status);
void _lockexit();
void _unlockexit();
// void __usercall initterm(void (__cdecl **pfbegin)()@<eax>, void (__cdecl **pfend)());
int __cdecl _cinit(int initFloatingPrecision); // idb
void __cdecl doexit(UINT code, int quick, int retcaller);
void __cdecl __noreturn exit(UINT code);
void __cdecl __noreturn _exit(UINT code);
void __cdecl _cexit();
void __cdecl _c_exit();
void __cdecl __noreturn __security_error_handler(int code, void *data);
void (__cdecl *__cdecl _set_security_error_handler(void (__cdecl *handler)(int, void *)))(int, void *); // idb
void __stdcall __ArrayUnwind(char *ptr, unsigned int size, int count, void (*pDtor)(void));
void __stdcall `eh vector destructor iterator'(char *ptr, unsigned int size, int count, void (__thiscall *pDtor)(void *));
void __stdcall `eh vector constructor iterator'(char *ptr, unsigned int size, int count, void (__thiscall *pCtor)(void *), void (__thiscall *pDtor)(void *));
unsigned __int64 __cdecl time(int *timeptr);
void __cdecl __noreturn _amsg_exit(unsigned int rterrnum);
void __cdecl __noreturn fast_error_exit(unsigned int rterrnum);
UINT __cdecl WinMainCRTStartup();
// void __stdcall _JumpToContinuation(void *target, EHRegistrationNode *pRN); idb
void __stdcall _CallMemberFunction1(void *pthis, void *pmfn, void *pthat, void *val2);
// void __stdcall _UnwindNestedFrames(EHRegistrationNode *pRN, EHExceptionRecord *pExcept); idb
int __cdecl CatchGuardHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext);
int __cdecl _CallSETranslator(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext, void *pDC, const _s_FuncInfo *pFuncInfo, int CatchDepth, EHRegistrationNode *pMarkerRN);
int __cdecl TranslatorGuardHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext);
// const _s_TryBlockMapEntry *__cdecl _GetRangeOfTrysToCheck(const _s_FuncInfo *pFuncInfo, int CatchDepth, int curState, unsigned int *pStart, unsigned int *pEnd); idb
FrameInfo *__cdecl _CreateFrameInfo(FrameInfo *pFrameInfo, void *pExceptionObject); // idb
int __cdecl IsExceptionObjectToBeDestroyed(void *pExceptionObject); // idb
void __cdecl _FindAndUnlinkFrame(FrameInfo *pFrameInfo); // idb
void __cdecl _CallCatchBlock2(EHRegistrationNode *pRN, const _s_FuncInfo *pFuncInfo, void *handlerAddress, int CatchDepth, unsigned int NLGCode);
void __cdecl _global_unwind2(PVOID TargetFrame);
int __cdecl ___00003(int a1, int a2, int a3, _DWORD *a4);
// int __usercall _local_unwind2@<eax>(unsigned int a1@<ebp>, int a2, int a3);
int __cdecl _abnormal_termination();
void __stdcall _NLG_Notify1(int a1);
// void __userpurge _NLG_Notify(unsigned int a1@<eax>, unsigned int a2@<ebp>, unsigned int dwInCode);
unsigned int __cdecl _fwrite_lk(unsigned __int8 *buffer, unsigned int size, unsigned int num, _iobuf *stream);
unsigned int __cdecl fwrite(unsigned __int8 *buffer, unsigned int size, unsigned int count, _iobuf *stream);
int __cdecl _except_handler3(int a1, _EH3_EXCEPTION_REGISTRATION *pRN, int a3); // idb
int __stdcall _seh_longjmp_unwind(int a1);
int __cdecl _vsnprintf(char *string, unsigned int count, char *format, char *ap);
_DWORD _fpclear(); // weak
void (__cdecl *_cfltcvt_init())(DOUBLE *arg, char *buffer, int format, char *precision, int caps);
void __cdecl _fpmath(int initPrecision); // idb
void __stdcall __noreturn _CxxThrowException(void *pExceptionObject, const _s__ThrowInfo *pThrowInfo);
char *__cdecl _mbsnbcpy(unsigned __int8 *dst, const unsigned __int8 *src, size_t cnt);
void __cdecl _mbsrchr(unsigned __int8 *str, unsigned int c);
void __cdecl _splitpath(char *path, char *drive, char *dir, char *fname, char *ext);
_iobuf *__cdecl __iob_func(); // idb
int __cdecl __initstdio(); // idb
int __endstdio();
void __cdecl _lock_file(_iobuf *pf);
void __cdecl _lock_file2(int i, char *s);
void __cdecl _unlock_file(_iobuf *pf);
void __cdecl _unlock_file2(int i, char *s);
// void __usercall xtoa(unsigned int val@<eax>, char *buf@<ecx>, unsigned int radix, int is_neg);
char *__cdecl itoa(int val, char *buf, unsigned int radix);
// void __userpurge x64toa(char *buf@<eax>, signed __int64 val, unsigned int radix, int is_neg);
char *__cdecl _ui64toa(unsigned __int64 val, char *buf, unsigned int radix);
int __cdecl isupper(int c); // idb
int __cdecl islower(int c); // idb
int __cdecl isdigit(int c); // idb
int __cdecl isxdigit(int c); // idb
int __cdecl isspace(int c); // idb
int __cdecl isprint(int c); // idb
int __cdecl _flush(_iobuf *str); // idb
int __cdecl _fflush_lk(_iobuf *str); // idb
int __cdecl flsall(int flushflag); // idb
int __cdecl fflush(_iobuf *stream); // idb
int __cdecl _flushall(); // idb
int __cdecl fputs(char *string, _iobuf *stream);
void __cdecl __noreturn _endthreadex(DWORD retcode);
void __stdcall __noreturn threadstartex(tagEntry *ptd);
HANDLE __cdecl _beginthreadex(_SECURITY_ATTRIBUTES *security, SIZE_T stacksize, unsigned int (__stdcall *initialcode)(void *), tagEntry *argument, DWORD createflag, unsigned int *thrdaddr);
double __cdecl difftime(int b, int a);
LPVOID __cdecl _heap_alloc(tagHeader *size);
LPVOID __cdecl _nh_malloc(tagHeader *size, int nhFlag);
LPVOID __cdecl malloc(tagHeader *size);
void __cdecl free(tagEntry *pBlock);
BOOL __cdecl _resetstkoflw();
void __cdecl strncmp(unsigned __int8 *first, unsigned __int8 *last, unsigned int count); // idb
// signed __int64 __usercall _ftol2@<edx:eax>(_DWORD, _DWORD, _DWORD, double@<st0>); weak
void __cdecl _mbsnbcmp(unsigned __int8 *s1, unsigned __int8 *s2, unsigned int n);
int __cdecl setvbuf(_iobuf *str, char *buffer, int type, signed int size);
int __cdecl _ungetc_lk(int ch, _iobuf *str); // idb
int __cdecl ungetc(int ch, _iobuf *stream); // idb
int __cdecl getc(_iobuf *stream); // idb
lconv *__cdecl localeconv(); // idb
int __cdecl fputc(unsigned __int8 ch, _iobuf *str);
int __cdecl fgetpos(_iobuf *stream, __int64 *pos); // idb
int __cdecl _fseek_lk(_iobuf *str, LONG offset, unsigned int whence);
int __cdecl fseek(_iobuf *stream, LONG offset, unsigned int whence);
int __cdecl fsetpos(_iobuf *stream, __int64 *pos);
void *__cdecl memchr(const void *Buf, int Val, size_t MaxCount);
void __cdecl strcspn(unsigned __int8 *string, unsigned __int8 *control); // idb
unsigned int __cdecl _fread_lk(unsigned __int8 *buffer, unsigned int size, unsigned int num, _iobuf *stream);
unsigned int __cdecl fread(unsigned __int8 *buffer, unsigned int size, unsigned int count, _iobuf *stream);
char *__cdecl strstr(const char *Str, const char *SubStr);
tm *__cdecl localtime(int ptime);
int fscanf(_iobuf *stream, const char *format, ...); // idb
// int __usercall make__time64_t@<eax>(tm *tb@<eax>, int ultflag);
int __cdecl _mktime64(tm *tb);
unsigned __int64 __cdecl _time64(__int64 *timeptr);
int __fastcall found_bx(int a1, int a2);
char *__cdecl strchr(const char *Str, int Val);
long double __cdecl atof(const char *nptr); // idb
void __cdecl rewind(_iobuf *str); // idb
int *__cdecl _errno(); // idb
unsigned int *__cdecl __doserrno(); // idb
void __cdecl _dosmaperr(unsigned int oserrno); // idb
int __cdecl vsprintf(char *string, char *format, char *ap);
int __cdecl _ftell_lk(_iobuf *str); // idb
int __cdecl ftell(_iobuf *stream); // idb
_iobuf *__cdecl _fdopen(UINT filedes, const char *mode);
int __cdecl wcslen(const wchar_t *wcs);
char *__cdecl strncat(char *Destination, const char *Source, size_t Count);
void *__cdecl calloc(unsigned int num, unsigned int size); // idb
size_t __cdecl strlen(const char *Str);
int __cdecl strcmp(const char *Str1, const char *Str2);
char *__cdecl strcpy(char *Destination, const char *Source);
char *__cdecl strcat(char *Destination, const char *Source);
int __cdecl strtoxl(const char *nptr, const char **endptr, unsigned int ibase, int flags);
int __cdecl strtol(const char *nptr, char **endptr, unsigned int ibase);
LPVOID __cdecl realloc(tagEntry *pBlock, tagHeader *newsize);
BOOL __cdecl _callnewh(unsigned int size);
void __cdecl memcpy(unsigned __int8 *dst, unsigned __int8 *src, unsigned int count); // idb
// BOOL __usercall TypeMatch@<eax>(const _s_HandlerType *pCatch@<esi>, const _s_CatchableType *pCatchable@<edi>, const _s_ThrowInfo *pThrow);
// int __usercall FrameUnwindFilter@<eax>(_EXCEPTION_POINTERS *pExPtrs@<eax>); idb
void __cdecl __FrameUnwindToState(EHRegistrationNode *pRN, void *pDC, const _s_FuncInfo *pFuncInfo, int targetState); // idb
void __cdecl __DestructExceptionObject(EHExceptionRecord *pExcept);
// char *__usercall AdjustPointer@<eax>(char *pThis@<eax>, const PMD *pmd@<ecx>);
bool __cdecl __uncaught_exception(); // idb
void *__cdecl CallCatchBlock(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext, const _s_FuncInfo *pFuncInfo, int CatchDepth, unsigned int NLGCode);
void __fastcall BuildCatchObject(const _s_CatchableType *pConv, char **pRN, EHExceptionRecord *pExcept, const _s_HandlerType *pCatch);
// void __usercall CatchIt(EHRegistrationNode *pRN@<esi>, const _s_HandlerType *pCatch@<ebx>, const _s_CatchableType *pConv@<ecx>, const _s_TryBlockMapEntry *pEntry@<edi>, EHExceptionRecord *pExcept, _CONTEXT *pContext, void *pDC, const _s_FuncInfo *pFuncInfo, int CatchDepth, EHRegistrationNode *pMarkerRN);
void __cdecl FindHandlerForForeignException(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext, void *pDC, const _s_FuncInfo *pFuncInfo, int curState, int CatchDepth, EHRegistrationNode *pMarkerRN); // idb
void __cdecl FindHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext, void *pDC, const _s_FuncInfo *pFuncInfo, unsigned __int8 recursive, int CatchDepth, EHRegistrationNode *pMarkerRN); // idb
int __cdecl __InternalCxxFrameHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext, void *pDC, const _s_FuncInfo *pFuncInfo, int CatchDepth, EHRegistrationNode *pMarkerRN, unsigned __int8 recursive);
int __cdecl __crtLCMapStringA(LCID Locale, DWORD dwMapFlags, const char *lpSrcStr, int cchSrc, char *lpDestStr, int cchDest, UINT code_page, int bError);
const unsigned __int16 *__cdecl __pctype_func(); // idb
int __cdecl __init_ctype(); // idb
unsigned int __cdecl ___lc_codepage_func(); // idb
unsigned int *__cdecl ___lc_handle_func(); // idb
int __cdecl _mtinitlocks(); // idb
void _mtdeletelocks();
void __cdecl _unlock(int locknum); // idb
int __cdecl _mtinitlocknum(int locknum); // idb
void __cdecl _lock(int locknum); // idb
int __cdecl ___setlc_active_func(); // idb
int *__cdecl ___unguarded_readlc_active_add_func(); // idb
void __cdecl __freetlocinfo(threadlocaleinfostruct *ptloci); // idb
threadlocaleinfostruct *__cdecl _updatetlocinfo_lk(); // idb
int __cdecl __init_collate(); // idb
void _strcats(char *outstr, int n, ...); // idb
int __cdecl __lc_strtolc(tagLC_STRINGS *names, char *locale);
void __cdecl __lc_lctostr(char *locale, const tagLC_STRINGS *names); // idb
threadlocaleinfostruct *__cdecl __updatetlocinfo(); // idb
char *__cdecl setlocale_get_all(); // idb
char *__cdecl _expandlocale(char *expr, char *output, tagLC_ID *id, unsigned __int8 *cp);
// char *__usercall setlocale_set_cat@<eax>(int category@<esi>, char *locale);
char *__fastcall setlocale_lk(char *_locale, int _category);
char *__cdecl setlocale(unsigned int _category, char *_locale);
int __cdecl memcmp(const void *Buf1, const void *Buf2, size_t Size);
void *__cdecl memset(void *, int Val, size_t Size);
void __cdecl __noreturn abort();
int __cdecl _flsbuf(unsigned __int8 ch, _iobuf *str);
// void __usercall write_char(int ch@<eax>, _iobuf *f@<ecx>, int *pnumwritten@<esi>);
// void __usercall write_multi_char(int *pnumwritten@<eax>, char ch, int num, _iobuf *f);
// void __usercall write_string(char *string@<ecx>, _iobuf *f@<edi>, int *pnumwritten@<eax>, int len);
int __cdecl _output(_iobuf *stream, char *format, char *argptr);
DWORD __stdcall __crtTlsAlloc(void (__stdcall *lpCallBack)(void *));
void _mtterm();
void __cdecl _initptd(_tiddata *ptd); // idb
_tiddata *__cdecl _getptd(); // idb
void __stdcall _freefls(tagEntry **data);
void __cdecl _freeptd(tagEntry **ptd);
int __cdecl _mtinit(); // idb
int __cdecl __isctype_mt(threadlocaleinfostruct *ptloci, int c, int mask); // idb
SIZE_T __cdecl _msize(void *pblock);
int __cdecl _filbuf(_iobuf *str); // idb
_iobuf *__cdecl _openfile(const char *filename, const char *mode, int shflag, _iobuf *str); // idb
_iobuf *__cdecl _getstream(); // idb
int __cdecl _close_lk(int fh); // idb
int __cdecl _close(int fh); // idb
void __cdecl _freebuf(_iobuf *stream); // idb
int __cdecl _stbuf(_iobuf *str); // idb
void __cdecl _ftbuf(int flag, _iobuf *str); // idb
void __cdecl _RTC_Initialize();
void __cdecl _RTC_Terminate();
int __cdecl __crtMessageBoxA(const char *lpText, const char *lpCaption, unsigned int uType); // idb
void __cdecl __noreturn terminate(); // idb
void __cdecl __noreturn _inconsistency(); // idb
void __cdecl _NMSG_WRITE(unsigned int rterrnum);
void _FF_MSGBANNER();
LONG __cdecl _XcptFilter(unsigned int xcptnum, _EXCEPTION_POINTERS *pxcptinfoptrs);
const char *__cdecl _wincmdln();
int __cdecl _setenvp(); // idb
// void __usercall parse_cmdline(char *cmdstart@<eax>, char *args@<ecx>, int *numchars@<esi>, char **argv, int *numargs);
int __cdecl _setargv(); // idb
unsigned __int8 *__cdecl __crtGetEnvironmentStringsA();
int __cdecl _ioinit(); // idb
int __cdecl __heap_select(); // idb
int __cdecl _heap_init(int mtflag); // idb
void __stdcall _CallSettingFrame(unsigned int funclet, unsigned int pRN, unsigned int dwInCode); // idb
void __cdecl __security_init_cookie();
int __cdecl _write_lk(int fh, char *buf, DWORD cnt);
int __cdecl _write(int fh, char *buf, DWORD cnt);
int __cdecl _ValidateEH3RN(_EH3_EXCEPTION_REGISTRATION *pRN); // idb
void __cdecl _forcdecpt(char *buffer); // idb
void __cdecl _cropzeros(char *buf); // idb
BOOL __cdecl _positive(long double *arg);
void __cdecl _fassign(FLOAT flag, char *argument, char *number);
// void __usercall shift(char *s@<eax>, int dist@<edi>);
// char *__usercall cftoe2@<eax>(char *buf@<ebx>, _strflt *pflt@<eax>, int ndec, int caps, char g_fmt);
char *__cdecl _cftoe(DOUBLE *pvalue, char *buf, int ndec, int caps);
// char *__usercall cftof2@<eax>(_strflt *pflt@<eax>, char *buf, int ndec, char g_fmt);
char *__cdecl _cftof(DOUBLE *pvalue, char *buf, int ndec);
char *__cdecl _cftog(DOUBLE *pvalue, char *buf, char *ndec, int caps);
void __cdecl _cfltcvt(DOUBLE *arg, char *buffer, int format, char *precision, int caps);
unsigned int _setdefaultprecision();
BOOL __cdecl _ms_p5_test_fdiv();
int __cdecl _ms_p5_mp_test_fdiv(); // idb
int __stdcall __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *pPtrs); // idb
int __cdecl __CxxSetUnhandledExceptionFilter(); // idb
void __cdecl __CxxRestoreUnhandledExceptionFilter(); // idb
// int __usercall CPtoLCID@<eax>(int codepage@<eax>); idb
int setSBCS();
unsigned int setSBUpLow();
tagEntry *__cdecl __updatetmbcinfo();
int __cdecl setmbcp_lk(int codepage); // idb
int __cdecl _setmbcp(UINT codepage);
int __cdecl __initmbctable(); // idb
void __cdecl strrchr(unsigned __int8 *string, unsigned __int8 chr); // idb
int __cdecl _fcloseall(); // idb
DWORD __cdecl _commit(int filedes);
int __cdecl __sbh_heap_init(unsigned int threshold); // idb
tagHeader *__cdecl __sbh_find_block(void *pvAlloc); // idb
void __cdecl __sbh_free_block(tagHeader *pHeader, tagEntry *pvAlloc);
tagHeader *__cdecl __sbh_alloc_new_region(); // idb
int __cdecl __sbh_alloc_new_group(tagHeader *pHeader); // idb
int __cdecl __sbh_resize_block(tagHeader *pHeader, char *pvAlloc, tagEntry *intNew);
_DWORD *__cdecl __sbh_alloc_block(tagHeader *intSize);
void __cdecl _getbuf(_iobuf *str); // idb
int __cdecl _ftelli64_lk(_iobuf *str);
__int64 __cdecl _ftelli64(_iobuf *stream); // idb
DWORD __cdecl _lseek_lk(int fh, LONG pos, DWORD mthd);
DWORD __cdecl _lseek(int fh, LONG pos, DWORD mthd);
int __cdecl _fseeki64_lk(_iobuf *str, __int64 offset, unsigned int whence);
int __cdecl _fseeki64(_iobuf *stream, __int64 offset, unsigned int whence);
int __cdecl _read_lk(int fh, char *buf, char *cnt);
int __cdecl _read(int fh, char *buf, char *cnt);
LPSTR tzset_lk();
// void __usercall cvtdate(int month@<eax>, int hour@<ecx>, int trantype, int datetype, int year, int week, int dayofweek, int date, int min, int sec, int msec);
// BOOL __usercall isindst_lk@<eax>(tm *tb@<ebx>);
void __tzset();
BOOL __cdecl _isindst(tm *tb);
tm *__cdecl gmtime(int *timp);
int __fastcall inc(int a1, _iobuf *fileptr);
int __cdecl _input(_iobuf *stream, const unsigned __int8 *format, char *arglist); // idb
tm *__cdecl _gmtime64(const __int64 *timp); // idb
tm *__cdecl _localtime64(const __int64 *ptime); // idb
_flt *__cdecl _fltin2(_flt *flt, const char *str);
BOOL __cdecl _ValidateRead(const void *data, UINT_PTR size);
BOOL __cdecl _ValidateWrite(void *data, UINT_PTR size);
BOOL __cdecl _ValidateExecute(int (__stdcall *code)());
int __cdecl __ansicp(LCID lcid);
char *__cdecl __convertcp(UINT fromCP, UINT toCP, const char *lpSrcStr, int *pcchSrc, char *lpDestStr, int cchDest);
BOOL __cdecl __crtGetStringTypeA(DWORD dwInfoType, char *lpSrcStr, int cchSrc, unsigned __int16 *lpCharType, UINT code_page, LCID lcid, int bError);
int __cdecl __getlocaleinfo(int lc_type, LCID localehandle, LCTYPE fieldtype, char **address);
int __stdcall _crtInitCritSecNoSpinCount(_RTL_CRITICAL_SECTION *lpCriticalSection, unsigned int dwSpinCount); // idb
BOOL __cdecl __crtInitCritSecAndSpinCount(_RTL_CRITICAL_SECTION *lpCriticalSection, DWORD dwSpinCount);
// int __usercall get_lc_time@<eax>(__lc_time_data *lc_time@<esi>); idb
void __cdecl __free_lc_time(__lc_time_data *lc_time); // idb
int __cdecl __init_time(); // idb
void __cdecl __free_lconv_num(lconv *l); // idb
int __cdecl __init_numeric(); // idb
void __cdecl __free_lconv_mon(lconv *l); // idb
int __cdecl __init_monetary(); // idb
BOOL __cdecl TranslateName(const tagLOCALETAB *lpTable, int high, char **ppchName);
LCID GetLcidFromDefault();
int __thiscall ProcessCodePage(char *lpCodePageStr);
int __cdecl TestDefaultCountry(__int16 lcid);
int __stdcall crtGetLocaleInfoA(LCID lcid, LCTYPE lctype, char *lpdata, int cchdata);
unsigned int __fastcall LcidFromHexString(int a1, char *lpHexString);
int __fastcall GetPrimaryLen(int a1, char *pchLanguage);
BOOL __thiscall CountryEnumProc(void *this, char *lpLcidString);
BOOL __cdecl TestDefaultLanguage(unsigned int lcid, int bTestPrimary);
BOOL __thiscall LangCountryEnumProc(void *this, char *lpLcidString);
BOOL __thiscall LanguageEnumProc(void *this, char *lpLcidString);
BOOL GetLcidFromCountry();
int GetLcidFromLangCountry();
BOOL GetLcidFromLanguage();
int __cdecl __get_qualified_locale(tagLC_STRINGS *const lpInStr, tagLC_ID *lpOutId, tagLC_STRINGS *lpOutStr); // idb
void __cdecl strpbrk(unsigned __int8 *string, unsigned __int8 *control); // idb
// _XCPT_ACTION *__usercall siglookup@<eax>(int signum@<esi>, _XCPT_ACTION *pxcptacttab@<edx>); idb
int __cdecl raise(int signum); // idb
int __cdecl _isatty(int fh); // idb
int __cdecl __wctomb_mt(threadlocaleinfostruct *ptloci, char *s, wchar_t wchar); // idb
int __cdecl wctomb(char *s, wchar_t wchar); // idb
int __cdecl tsopen_lk(int *punlock_flag, int *pfh, const char *path, __int16 oflag, char pmode);
int _sopen(const char *path, __int16 oflag, int shflag, char pmode, ...);
int __cdecl _set_osfhnd(int fh, void *value);
int __cdecl _free_osfhnd(int fh); // idb
int __cdecl _get_osfhandle(int fh); // idb
int __cdecl _lock_fhandle(int fh); // idb
void __cdecl _unlock_fhandle(int fh); // idb
int __cdecl _alloc_osfhnd(); // idb
int __cdecl x_ismbbtype(unsigned __int8 tst, int cmask, unsigned __int8 kmask);
int __cdecl _ismbblead(unsigned __int8 tst);
doubleint __cdecl _lseeki64_lk(int fh, __int64 pos, DWORD mthd);
int __cdecl _lseeki64(int fh, __int64 pos, DWORD mthd);
int __cdecl __tolower_mt(threadlocaleinfostruct *ptloci, unsigned int c);
int __cdecl tolower(unsigned int c);
int __cdecl _ZeroTail(unsigned int *man, int nbit); // idb
int __cdecl _IncMan(unsigned int *man, int nbit); // idb
int __cdecl _RoundMan(unsigned int *man, int precision); // idb
void __cdecl _CopyMan(unsigned int *dest, unsigned int *src); // idb
int __cdecl _IsZeroMan(unsigned int *man); // idb
void __cdecl _ShrMan(unsigned int *man, int n); // idb
INTRNCVT_STATUS __cdecl _ld12cvt(_LDBL12 *pld12, unsigned int *d, FpFormatDescriptor *format);
INTRNCVT_STATUS __cdecl _ld12tod(_LDBL12 *pld12, DOUBLE *d); // idb
INTRNCVT_STATUS __cdecl _ld12tof(_LDBL12 *pld12, FLOAT *f); // idb
void __cdecl _atodbl(DOUBLE *d, char *str); // idb
void __cdecl _atoflt(FLOAT *f, char *str); // idb
void __cdecl _fptostr(char *buf, char *digits, _strflt *pflt);
void __cdecl __dtold(_LDOUBLE *pld, long double *px); // idb
_strflt *__cdecl _fltout2(DOUBLE x, _strflt *flt, char *resultstr); // idb
void __noreturn _fptrap(); // weak
// unsigned int __usercall abstract_cw@<eax>(unsigned __int16 cw@<bx>); idb
// int __usercall hw_cw@<eax>(unsigned int abstr@<ebx>);
unsigned int __cdecl _control87(unsigned int newctrl, unsigned int mask); // idb
unsigned int __cdecl _controlfp(unsigned int newctrl, unsigned int mask); // idb
char *__cdecl _getenv_lk(char *option);
int __cdecl __mbtowc_mt(threadlocaleinfostruct *ptloci, unsigned __int16 *pwc, const char *s, signed int n);
int __cdecl mbtowc(unsigned __int16 *pwc, const char *s, unsigned int n); // idb
unsigned int __cdecl __strgtold12(_LDBL12 *pld12, const char **p_end_ptr, const char *str, unsigned int mult12, int scale, int decpt, int implicit_E);
int __cdecl __crtGetLocaleInfoW(LCID Locale, LCTYPE LCType, unsigned __int16 *lpLCData, int cchData, UINT code_page);
int __cdecl __crtGetLocaleInfoA(LCID Locale, LCTYPE LCType, char *lpLCData, int cchData, UINT code_page);
void __cdecl __ascii_stricmp(char *dst, char *src);
void __cdecl _stricmp(char *dst, char *src);
void __cdecl _strnicmp(char *dst, char *src, unsigned int count);
int __cdecl _chsize_lk(int filedes, LONG size);
int __cdecl __addl(unsigned int x, unsigned int y, unsigned int *sum); // idb
void __cdecl __add_12(_LDBL12 *x, _LDBL12 *y); // idb
void __cdecl __shl_12(_LDBL12 *p); // idb
void __cdecl __shr_12(_LDBL12 *p); // idb
void __cdecl __mtold12(char *manptr, _LDBL12 *manlen, _LDBL12 *ld12);
int __cdecl _I10_OUTPUT(_LDOUBLE ld, int ndigits, char output_flags, _FloatOutStruct *fos);
int __cdecl _mbsnbicoll(char *s1, char *s2, unsigned int n);
int __cdecl __wtomb_environ(); // idb
void __cdecl __ld12mul(_LDBL12 *px, _LDBL12 *py); // idb
void __cdecl __multtenpow12(_LDBL12 *pld12, int pow, unsigned int mult12); // idb
void __cdecl __ascii_strnicmp(unsigned __int8 *first, unsigned __int8 *last, unsigned int count); // idb
int __cdecl _setmode_lk(int fh, int mode); // idb
// int __usercall strncnt@<eax>(const char *string@<eax>, int cnt); idb
int __cdecl __crtCompareStringA(LCID Locale, DWORD dwCmpFlags, char *lpString1, int cchCount1, char *lpString2, int cchCount2, UINT code_page);
// int __usercall findenv@<eax>(unsigned int len@<edi>, char *name);
// char **__usercall copy_environ@<eax>(char **oldenviron@<edi>); idb
int __cdecl __crtsetenv(char **poption, int primary);
char *__cdecl _strdup(const char *string); // idb
char *__cdecl _mbschr(const char *string, int c);
// BOOL __stdcall AcceptEx(SOCKET sListenSocket, SOCKET sAcceptSocket, PVOID lpOutputBuffer, DWORD dwReceiveDataLength, DWORD dwLocalAddressLength, DWORD dwRemoteAddressLength, LPDWORD lpdwBytesReceived, LPOVERLAPPED lpOverlapped);
// void __stdcall GetAcceptExSockaddrs(PVOID lpOutputBuffer, DWORD dwReceiveDataLength, DWORD dwLocalAddressLength, DWORD dwRemoteAddressLength, struct sockaddr **LocalSockaddr, LPINT LocalSockaddrLength, struct sockaddr **RemoteSockaddr, LPINT RemoteSockaddrLength);
void __thiscall ATL::CSimpleArray<HINSTANCE__ *,ATL::CSimpleArrayEqualHelper<HINSTANCE__ *>>::RemoveAll(ATL::CSimpleArray<HINSTANCE__ *,ATL::CSimpleArrayEqualHelper<HINSTANCE__ *> > *this); // idb
void __thiscall ATL::_ATL_BASE_MODULE70::_ATL_BASE_MODULE70(ATL::_ATL_BASE_MODULE70 *this); // idb
void __thiscall ATL::CAtlBaseModule::~CAtlBaseModule(ATL::CAtlBaseModule *this); // idb
void __thiscall ATL::CAtlBaseModule::CAtlBaseModule(ATL::CAtlBaseModule *this); // idb
// void __stdcall RtlUnwind(PVOID TargetFrame, PVOID TargetIp, PEXCEPTION_RECORD ExceptionRecord, PVOID ReturnValue);
char *__cdecl strlwr(char *string); // idb
char *__cdecl gcvt(DOUBLE value, int ndec, char *buf);
int __cdecl fileno(_iobuf *stream); // idb
int _E1();
int _E4();
int _E7();
UINT _E1_0();
void _E5();
int _E7_0();
int _E5_0();
int _E5_1();
void _E8();
int _E5_2();
int _E5_3();
int _E5_4();
unsigned int _E5_5();
int _E1_1();
UINT _E3_0();
UINT _E5_6();
void _E5_7();
int _E1_2();
int _E5_8();
int _E5_9();
int _E5_10();
int _E5_11();
int _E1_3();
int _E5_12();
int _E5_13();
int _E5_14();
int _E5_15();
int _E1_4();
int _E1_5();
int _E1_6();
int _E4_0();
int _E7_1();
void _E10();
int _E1_7();
int _E4_1();
int _E5_16();
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj(); // idb
void __thiscall _E2(CDummyCharacterList *this);
void __thiscall _E2_0(CFieldGameClientDispatchTable *this);
void __cdecl `CLogDispatch::GetDispatchTable'::`2'::singleDispatch(); // idb
void __thiscall _E4_2(CRylGameServer *this);
void __cdecl _E2_1(); // idb
void __cdecl _E5_17(); // idb
void __cdecl _E8_0(); // idb
void __cdecl _E2_2(); // idb
void __cdecl _E2_3(); // idb
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj(); // idb
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj(); // idb
void __cdecl _E6(); // idb
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj(); // idb
void __cdecl _E8_1(); // idb
void __cdecl _E6_0(); // idb
void __cdecl _E6_1(); // idb
void __cdecl _E6_2(); // idb
void __cdecl _E6_3(); // idb
void __cdecl _E6_4(); // idb
void __cdecl _E2_4(); // idb
void __cdecl _E6_5(); // idb
void __cdecl _E6_6(); // idb
void __cdecl _E6_7(); // idb
void __cdecl _E2_5(); // idb
void __cdecl _E6_8(); // idb
void __cdecl _E8_2(); // idb
void __cdecl _E10_0(); // idb
void __cdecl _E2_6(); // idb
void __cdecl _E6_9(); // idb
void __cdecl _E6_10(); // idb
void __cdecl _E6_11(); // idb
void __cdecl _E6_12(); // idb
void __cdecl _E6_13(); // idb
void __cdecl _E2_7(); // idb
void __cdecl _E2_8(); // idb
void __cdecl _E6_14(); // idb
void __cdecl _E2_9(); // idb
void __cdecl _E6_15(); // idb
void __cdecl _E6_16(); // idb
void __cdecl _E6_17(); // idb
void __cdecl _E6_18(); // idb
void __cdecl _E2_10(); // idb
void __cdecl _E6_19(); // idb
void __cdecl _E6_20(); // idb
void __cdecl _E6_21(); // idb
void __cdecl _E6_22(); // idb
void __cdecl _E6_23(); // idb
void __cdecl _E2_11(); // idb
void __cdecl _E2_12(); // idb
void __cdecl _E2_13(); // idb
void __cdecl _E8_3(); // idb
void __cdecl _E5_18(); // idb
void __cdecl _E2_14(); // idb
void __cdecl _E2_15(); // idb
void __cdecl _E5_19(); // idb
void __cdecl _E6_24(); // idb
int start();
unsigned int __cdecl sub_545539(int a1, int a2, unsigned int a3, int a4);
unsigned int __thiscall sub_545615(_DWORD *this, int a2);
int __thiscall sub_545680(_DWORD *this, int a2, int a3);
char __thiscall sub_5456A5(_DWORD *this, int a2);
int __thiscall sub_545821(_DWORD *this);
int __thiscall sub_5458F5(_DWORD *this, int a2);
char __thiscall sub_545973(_DWORD *this, int a2, int a3);
char __thiscall sub_5459D4(int this);
void sub_545B63();
void sub_545B69();
char __thiscall sub_545B71(unsigned int *this, unsigned int a2, unsigned int *a3);

//-------------------------------------------------------------------------
// Data declarations

_UNKNOWN loc_41656C; // weak
_UNKNOWN ExceptionContinuation; // weak
_UNKNOWN gu_return; // weak
_UNKNOWN _L20577; // weak
_UNKNOWN _ehhandler__AddTime_CPerformanceCheck__QAEXPBDN_Z; // weak
_UNKNOWN _ehhandler__InitializeFramework_CServerWindowFramework__IAE_NPAUHINSTANCE____PBDHH_Z; // weak
_UNKNOWN _ehhandler__LoginMonster_CCellManager__AAE_NPBDG_Z; // weak
_UNKNOWN _ehhandler__DBUpdate_CCharacter__QAE_NW4UpdateType_DBUpdateData___Z; // weak
_UNKNOWN _ehhandler__AddRequest_CServerRequest__QAEKPAVCPacketDispatch__0KP6AX0_Z_Z; // weak
_UNKNOWN _ehhandler__UpdateMemberInfo_CGuild_Guild__QAE_NKKE_Z; // weak
_UNKNOWN _ehhandler__UseStartKit_CCharacter__QAE_NG_Z; // weak
_UNKNOWN _ehhandler__SetDispatch_CMultiDispatch__QAE_NKPAVCPacketDispatch___Z; // weak
_UNKNOWN _ehhandler__StartPhase_CCharacter__QAE_NGE_Z; // weak
_UNKNOWN loc_4D2713; // weak
_UNKNOWN loc_4D2753; // weak
// extern BOOL (__stdcall *GetUserNameA)(LPSTR lpBuffer, LPDWORD pcbBuffer);
// extern HGDIOBJ (__stdcall *GetStockObject)(int i);
// extern HFONT (__stdcall *CreateFontA)(int cHeight, int cWidth, int cEscapement, int cOrientation, int cWeight, DWORD bItalic, DWORD bUnderline, DWORD bStrikeOut, DWORD iCharSet, DWORD iOutPrecision, DWORD iClipPrecision, DWORD iQuality, DWORD iPitchAndFamily, LPCSTR pszFaceName);
// extern BOOL (__stdcall *DeleteObject)(HGDIOBJ ho);
// extern COLORREF (__stdcall *SetBkColor)(HDC hdc, COLORREF color);
// extern LONG (__stdcall *UnhandledExceptionFilter)(struct _EXCEPTION_POINTERS *ExceptionInfo);
// extern HANDLE (__stdcall *CreateMutexA)(LPSECURITY_ATTRIBUTES lpMutexAttributes, BOOL bInitialOwner, LPCSTR lpName);
// extern BOOL (__stdcall *CloseHandle)(HANDLE hObject);
// extern void (__stdcall *Sleep)(DWORD dwMilliseconds);
// extern DWORD (__stdcall *GetLastError)();
// extern BOOL (__stdcall *SetEvent)(HANDLE hEvent);
// extern HANDLE (__stdcall *CreateEventA)(LPSECURITY_ATTRIBUTES lpEventAttributes, BOOL bManualReset, BOOL bInitialState, LPCSTR lpName);
// extern DWORD (__stdcall *WaitForMultipleObjects)(DWORD nCount, const HANDLE *lpHandles, BOOL bWaitAll, DWORD dwMilliseconds);
// extern void (__stdcall *EnterCriticalSection)(LPCRITICAL_SECTION lpCriticalSection);
// extern void (__stdcall *LeaveCriticalSection)(LPCRITICAL_SECTION lpCriticalSection);
// extern DWORD (__stdcall *GetCurrentProcessId)();
// extern void (__stdcall *GetLocalTime)(LPSYSTEMTIME lpSystemTime);
// extern void (__stdcall *DeleteCriticalSection)(LPCRITICAL_SECTION lpCriticalSection);
// extern LONG (__stdcall *InterlockedExchange)(volatile LONG *Target, LONG Value);
// extern void (__stdcall *InitializeCriticalSection)(LPCRITICAL_SECTION lpCriticalSection);
// extern BOOL (__stdcall *SetEnvironmentVariableA)(LPCSTR lpName, LPCSTR lpValue);
// extern BOOL (__stdcall *SetEndOfFile)(HANDLE hFile);
// extern int (__stdcall *GetLocaleInfoW)(LCID Locale, LCTYPE LCType, LPWSTR lpLCData, int cchData);
// extern BOOL (__stdcall *SetStdHandle)(DWORD nStdHandle, HANDLE hHandle);
// extern BOOL (__stdcall *IsValidCodePage)(UINT CodePage);
// extern BOOL (__stdcall *IsValidLocale)(LCID Locale, DWORD dwFlags);
// extern BOOL (__stdcall *EnumSystemLocalesA)(LOCALE_ENUMPROCA lpLocaleEnumProc, DWORD dwFlags);
// extern LCID (__stdcall *GetUserDefaultLCID)();
// extern BOOL (__stdcall *GetStringTypeW)(DWORD dwInfoType, LPCWCH lpSrcStr, int cchSrc, LPWORD lpCharType);
// extern BOOL (__stdcall *GetStringTypeA)(LCID Locale, DWORD dwInfoType, LPCSTR lpSrcStr, int cchSrc, LPWORD lpCharType);
// extern BOOL (__stdcall *IsBadCodePtr)(FARPROC lpfn);
// extern BOOL (__stdcall *IsBadReadPtr)(const void *lp, UINT_PTR ucb);
// extern DWORD (__stdcall *GetTimeZoneInformation)(LPTIME_ZONE_INFORMATION lpTimeZoneInformation);
// extern DWORD (__stdcall *SetFilePointer)(HANDLE hFile, LONG lDistanceToMove, PLONG lpDistanceToMoveHigh, DWORD dwMoveMethod);
// extern BOOL (__stdcall *IsBadWritePtr)(LPVOID lp, UINT_PTR ucb);
// extern BOOL (__stdcall *FlushFileBuffers)(HANDLE hFile);
// extern UINT (__stdcall *GetOEMCP)();
// extern BOOL (__stdcall *QueryPerformanceCounter)(LARGE_INTEGER *lpPerformanceCount);
// extern BOOL (__stdcall *VirtualFree)(LPVOID lpAddress, SIZE_T dwSize, DWORD dwFreeType);
// extern HANDLE (__stdcall *HeapCreate)(DWORD flOptions, SIZE_T dwInitialSize, SIZE_T dwMaximumSize);
// extern BOOL (__stdcall *HeapDestroy)(HANDLE hHeap);
// extern DWORD (__stdcall *GetFileType)(HANDLE hFile);
// extern UINT (__stdcall *SetHandleCount)(UINT uNumber);
// extern LPWCH (__stdcall *GetEnvironmentStringsW)();
// extern HANDLE (__stdcall *CreateFileA)(LPCSTR lpFileName, DWORD dwDesiredAccess, DWORD dwShareMode, LPSECURITY_ATTRIBUTES lpSecurityAttributes, DWORD dwCreationDisposition, DWORD dwFlagsAndAttributes, HANDLE hTemplateFile);
// extern BOOL (__stdcall *CreateDirectoryA)(LPCSTR lpPathName, LPSECURITY_ATTRIBUTES lpSecurityAttributes);
// extern DWORD (__stdcall *GetFileAttributesA)(LPCSTR lpFileName);
// extern BOOL (__stdcall *WriteFile)(HANDLE hFile, LPCVOID lpBuffer, DWORD nNumberOfBytesToWrite, LPDWORD lpNumberOfBytesWritten, LPOVERLAPPED lpOverlapped);
// extern DWORD (__stdcall *GetFileSize)(HANDLE hFile, LPDWORD lpFileSizeHigh);
// extern BOOL (__stdcall *FreeLibrary)(HMODULE hLibModule);
// extern DWORD (__stdcall *FormatMessageA)(DWORD dwFlags, LPCVOID lpSource, DWORD dwMessageId, DWORD dwLanguageId, LPSTR lpBuffer, DWORD nSize, va_list *Arguments);
// extern DWORD (__stdcall *GetModuleFileNameA)(HMODULE hModule, LPSTR lpFilename, DWORD nSize);
// extern LONG (__stdcall *InterlockedIncrement)(volatile LONG *lpAddend);
// extern LONG (__stdcall *InterlockedDecrement)(volatile LONG *lpAddend);
// extern HANDLE (__stdcall *CreateIoCompletionPort)(HANDLE FileHandle, HANDLE ExistingCompletionPort, ULONG_PTR CompletionKey, DWORD NumberOfConcurrentThreads);
// extern void (__stdcall *GetSystemInfo)(LPSYSTEM_INFO lpSystemInfo);
// extern FARPROC (__stdcall *GetProcAddress)(HMODULE hModule, LPCSTR lpProcName);
// extern HMODULE (__stdcall *LoadLibraryA)(LPCSTR lpLibFileName);
// extern LPTOP_LEVEL_EXCEPTION_FILTER (__stdcall *SetUnhandledExceptionFilter)(LPTOP_LEVEL_EXCEPTION_FILTER lpTopLevelExceptionFilter);
// extern HMODULE (__stdcall *GetModuleHandleA)(LPCSTR lpModuleName);
// extern SIZE_T (__stdcall *VirtualQuery)(LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength);
// extern BOOL (__stdcall *IsBadStringPtrA)(LPCSTR lpsz, UINT_PTR ucchMax);
// extern BOOL (__stdcall *GetComputerNameA)(LPSTR lpBuffer, LPDWORD nSize);
// extern HLOCAL (__stdcall *LocalFree)(HLOCAL hMem);
// extern HANDLE (__stdcall *GetCurrentProcess)();
// extern HANDLE (__stdcall *GetCurrentThread)();
// extern DWORD (__stdcall *GetCurrentThreadId)();
// extern DWORD (__stdcall *WaitForSingleObject)(HANDLE hHandle, DWORD dwMilliseconds);
// extern BOOL (__stdcall *GetQueuedCompletionStatus)(HANDLE CompletionPort, LPDWORD lpNumberOfBytesTransferred, PULONG_PTR lpCompletionKey, LPOVERLAPPED *lpOverlapped, DWORD dwMilliseconds);
// extern BOOL (__stdcall *PostQueuedCompletionStatus)(HANDLE CompletionPort, DWORD dwNumberOfBytesTransferred, ULONG_PTR dwCompletionKey, LPOVERLAPPED lpOverlapped);
// extern UINT (__stdcall *GetACP)();
// extern int (__stdcall *GetLocaleInfoA)(LCID Locale, LCTYPE LCType, LPSTR lpLCData, int cchData);
// extern BOOL (__stdcall *GetVersionExA)(LPOSVERSIONINFOA lpVersionInformation);
// extern int (__stdcall *MultiByteToWideChar)(UINT CodePage, DWORD dwFlags, LPCCH lpMultiByteStr, int cbMultiByte, LPWSTR lpWideCharStr, int cchWideChar);
// extern int (__stdcall *WideCharToMultiByte)(UINT CodePage, DWORD dwFlags, LPCWCH lpWideCharStr, int cchWideChar, LPSTR lpMultiByteStr, int cbMultiByte, LPCCH lpDefaultChar, LPBOOL lpUsedDefaultChar);
// extern void (__stdcall *RaiseException)(DWORD dwExceptionCode, DWORD dwExceptionFlags, DWORD nNumberOfArguments, const ULONG_PTR *lpArguments);
// extern int (__stdcall *CompareStringA)(LCID Locale, DWORD dwCmpFlags, PCNZCH lpString1, int cchCount1, PCNZCH lpString2, int cchCount2);
// extern int (__stdcall *CompareStringW)(LCID Locale, DWORD dwCmpFlags, PCNZWCH lpString1, int cchCount1, PCNZWCH lpString2, int cchCount2);
// extern LONG (__stdcall *InterlockedCompareExchange)(volatile LONG *Destination, LONG Exchange, LONG Comperand);
// extern LPSTR (__stdcall *GetCommandLineA)();
// extern BOOL (__stdcall *SystemTimeToFileTime)(const SYSTEMTIME *lpSystemTime, LPFILETIME lpFileTime);
// extern BOOL (__stdcall *FileTimeToSystemTime)(const FILETIME *lpFileTime, LPSYSTEMTIME lpSystemTime);
// extern BOOL (__stdcall *ReadFile)(HANDLE hFile, LPVOID lpBuffer, DWORD nNumberOfBytesToRead, LPDWORD lpNumberOfBytesRead, LPOVERLAPPED lpOverlapped);
// extern BOOL (__stdcall *PulseEvent)(HANDLE hEvent);
// extern BOOL (__stdcall *DeleteFileA)(LPCSTR lpFileName);
// extern DWORD (__stdcall *GetTickCount)();
// extern UINT (__stdcall *GetPrivateProfileIntA)(LPCSTR lpAppName, LPCSTR lpKeyName, INT nDefault, LPCSTR lpFileName);
// extern DWORD (__stdcall *GetPrivateProfileStringA)(LPCSTR lpAppName, LPCSTR lpKeyName, LPCSTR lpDefault, LPSTR lpReturnedString, DWORD nSize, LPCSTR lpFileName);
// extern void (__stdcall __noreturn *ExitProcess)(UINT uExitCode);
// extern BOOL (__stdcall *TerminateProcess)(HANDLE hProcess, UINT uExitCode);
// extern void (__stdcall *GetSystemTimeAsFileTime)(LPFILETIME lpSystemTimeAsFileTime);
// extern void (__stdcall *GetStartupInfoA)(LPSTARTUPINFOA lpStartupInfo);
// extern void (__stdcall __noreturn *ExitThread)(DWORD dwExitCode);
// extern HANDLE (__stdcall *CreateThread)(LPSECURITY_ATTRIBUTES lpThreadAttributes, SIZE_T dwStackSize, LPTHREAD_START_ROUTINE lpStartAddress, LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD lpThreadId);
// extern LPVOID (__stdcall *HeapAlloc)(HANDLE hHeap, DWORD dwFlags, SIZE_T dwBytes);
// extern BOOL (__stdcall *HeapFree)(HANDLE hHeap, DWORD dwFlags, LPVOID lpMem);
// extern BOOL (__stdcall *VirtualProtect)(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);
// extern LPVOID (__stdcall *VirtualAlloc)(LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
// extern LPVOID (__stdcall *HeapReAlloc)(HANDLE hHeap, DWORD dwFlags, LPVOID lpMem, SIZE_T dwBytes);
// extern int (__stdcall *LCMapStringA)(LCID Locale, DWORD dwMapFlags, LPCSTR lpSrcStr, int cchSrc, LPSTR lpDestStr, int cchDest);
// extern int (__stdcall *LCMapStringW)(LCID Locale, DWORD dwMapFlags, LPCWSTR lpSrcStr, int cchSrc, LPWSTR lpDestStr, int cchDest);
// extern BOOL (__stdcall *GetCPInfo)(UINT CodePage, LPCPINFO lpCPInfo);
// extern DWORD (__stdcall *TlsAlloc)();
// extern void (__stdcall *SetLastError)(DWORD dwErrCode);
// extern BOOL (__stdcall *TlsFree)(DWORD dwTlsIndex);
// extern BOOL (__stdcall *TlsSetValue)(DWORD dwTlsIndex, LPVOID lpTlsValue);
// extern LPVOID (__stdcall *TlsGetValue)(DWORD dwTlsIndex);
// extern SIZE_T (__stdcall *HeapSize)(HANDLE hHeap, DWORD dwFlags, LPCVOID lpMem);
// extern HANDLE (__stdcall *GetStdHandle)(DWORD nStdHandle);
// extern BOOL (__stdcall *FreeEnvironmentStringsA)(LPCH penv);
// extern LPCH (__stdcall *GetEnvironmentStrings)();
// extern BOOL (__stdcall *FreeEnvironmentStringsW)(LPWCH penv);
// extern BSTR (__stdcall *SysAllocString)(const OLECHAR *psz);
// extern void (__stdcall *SysFreeString)(BSTR bstrString);
// extern HRESULT (__stdcall *GetErrorInfo)(ULONG dwReserved, IErrorInfo **pperrinfo);
// extern BOOL (__stdcall *Shell_NotifyIconA)(DWORD dwMessage, PNOTIFYICONDATAA lpData);
// extern LRESULT (__stdcall *DispatchMessageA)(const MSG *lpMsg);
// extern BOOL (__stdcall *PostMessageA)(HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam);
// extern LRESULT (__stdcall *SendMessageA)(HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam);
// extern int (__stdcall *MessageBoxA)(HWND hWnd, LPCSTR lpText, LPCSTR lpCaption, UINT uType);
// extern BOOL (__stdcall *GetCursorPos)(LPPOINT lpPoint);
// extern BOOL (__stdcall *SetForegroundWindow)(HWND hWnd);
// extern BOOL (__stdcall *TrackPopupMenu)(HMENU hMenu, UINT uFlags, int x, int y, int nReserved, HWND hWnd, const RECT *prcRect);
// extern BOOL (__stdcall *DestroyIcon)(HICON hIcon);
// extern BOOL (__stdcall *KillTimer)(HWND hWnd, UINT_PTR uIDEvent);
// extern int (__stdcall *GetSystemMetrics)(int nIndex);
// extern HCURSOR (__stdcall *LoadCursorA)(HINSTANCE hInstance, LPCSTR lpCursorName);
// extern LRESULT (__stdcall *CallWindowProcA)(WNDPROC lpPrevWndFunc, HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam);
// extern int (__stdcall *GetWindowTextA)(HWND hWnd, LPSTR lpString, int nMaxCount);
// extern BOOL (__stdcall *SetWindowTextA)(HWND hWnd, LPCSTR lpString);
// extern BOOL (__stdcall *UpdateWindow)(HWND hWnd);
// extern DWORD (__stdcall *GetSysColor)(int nIndex);
// extern int (*_wsprintfA)(LPSTR, LPCSTR, ...);
// extern BOOL (__stdcall *UnregisterClassA)(LPCSTR lpClassName, HINSTANCE hInstance);
// extern HANDLE (__stdcall *GetPropA)(HWND hWnd, LPCSTR lpString);
// extern HWND (__stdcall *FindWindowA)(LPCSTR lpClassName, LPCSTR lpWindowName);
// extern BOOL (__stdcall *DestroyMenu)(HMENU hMenu);
// extern HMENU (__stdcall *GetSubMenu)(HMENU hMenu, int nPos);
// extern HMENU (__stdcall *LoadMenuA)(HINSTANCE hInstance, LPCSTR lpMenuName);
// extern LRESULT (__stdcall *DefWindowProcA)(HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam);
// extern void (__stdcall *PostQuitMessage)(int nExitCode);
// extern BOOL (__stdcall *SetPropA)(HWND hWnd, LPCSTR lpString, HANDLE hData);
// extern HWND (__stdcall *CreateWindowExA)(DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName, DWORD dwStyle, int X, int Y, int nWidth, int nHeight, HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam);
// extern HBRUSH (__stdcall *GetSysColorBrush)(int nIndex);
// extern HICON (__stdcall *LoadIconA)(HINSTANCE hInstance, LPCSTR lpIconName);
// extern BOOL (__stdcall *DestroyWindow)(HWND hWnd);
// extern ATOM (__stdcall *RegisterClassA)(const WNDCLASSA *lpWndClass);
// extern BOOL (__stdcall *TranslateMessage)(const MSG *lpMsg);
// extern BOOL (__stdcall *IsDialogMessageA)(HWND hDlg, LPMSG lpMsg);
// extern BOOL (__stdcall *GetMessageA)(LPMSG lpMsg, HWND hWnd, UINT wMsgFilterMin, UINT wMsgFilterMax);
// extern UINT (__stdcall *RegisterWindowMessageA)(LPCSTR lpString);
// extern LONG (__stdcall *SetWindowLongA)(HWND hWnd, int nIndex, LONG dwNewLong);
// extern MMRESULT (__stdcall *timeGetDevCaps)(LPTIMECAPS ptc, UINT cbtc);
// extern MMRESULT (__stdcall *timeBeginPeriod)(UINT uPeriod);
// extern DWORD (__stdcall *timeGetTime)();
// extern int (__stdcall *closesocket)(SOCKET s);
// extern int (__stdcall *WSAEventSelect)(SOCKET s, HANDLE hEventObject, int lNetworkEvents);
// extern int (__stdcall *WSARecvFrom)(SOCKET s, LPWSABUF lpBuffers, DWORD dwBufferCount, LPDWORD lpNumberOfBytesRecvd, LPDWORD lpFlags, struct sockaddr *lpFrom, LPINT lpFromlen, LPWSAOVERLAPPED lpOverlapped, LPWSAOVERLAPPED_COMPLETION_ROUTINE lpCompletionRoutine);
// extern int (__stdcall *WSAEnumNetworkEvents)(SOCKET s, HANDLE hEventObject, LPWSANETWORKEVENTS lpNetworkEvents);
// extern SOCKET (__stdcall *WSASocketA)(int af, int type, int protocol, LPWSAPROTOCOL_INFOA lpProtocolInfo, GROUP g, DWORD dwFlags);
// extern char *(__stdcall *inet_ntoa)(struct in_addr in);
// extern int (__stdcall *bind)(SOCKET s, const struct sockaddr *name, int namelen);
// extern int (__stdcall *setsockopt)(SOCKET s, int level, int optname, const char *optval, int optlen);
// extern int (__stdcall *listen)(SOCKET s, int backlog);
// extern u_long (__stdcall *htonl)(u_long hostlong);
// extern unsigned int (__stdcall *inet_addr)(const char *cp);
// extern int (__stdcall *WSAGetLastError)();
// extern int (__stdcall *WSARecv)(SOCKET s, LPWSABUF lpBuffers, DWORD dwBufferCount, LPDWORD lpNumberOfBytesRecvd, LPDWORD lpFlags, LPWSAOVERLAPPED lpOverlapped, LPWSAOVERLAPPED_COMPLETION_ROUTINE lpCompletionRoutine);
// extern u_short (__stdcall *ntohs)(u_short netshort);
// extern int (__stdcall *connect)(SOCKET s, const struct sockaddr *name, int namelen);
// extern u_short (__stdcall *htons)(u_short hostshort);
// extern struct hostent *(__stdcall *gethostbyname)(const char *name);
// extern int (__stdcall *gethostname)(char *name, int namelen);
// extern int (__stdcall *WSAStartup)(WORD wVersionRequested, LPWSADATA lpWSAData);
// extern int (__stdcall *WSACleanup)();
// extern int (__stdcall *WSASendTo)(SOCKET s, LPWSABUF lpBuffers, DWORD dwBufferCount, LPDWORD lpNumberOfBytesSent, DWORD dwFlags, const struct sockaddr *lpTo, int iTolen, LPWSAOVERLAPPED lpOverlapped, LPWSAOVERLAPPED_COMPLETION_ROUTINE lpCompletionRoutine);
// extern int (__stdcall *WSASend)(SOCKET s, LPWSABUF lpBuffers, DWORD dwBufferCount, LPDWORD lpNumberOfBytesSent, DWORD dwFlags, LPWSAOVERLAPPED lpOverlapped, LPWSAOVERLAPPED_COMPLETION_ROUTINE lpCompletionRoutine);
// extern int (__stdcall *shutdown)(SOCKET s, int how);
// extern HRESULT (__stdcall *CoGetMalloc)(DWORD dwMemContext, LPMALLOC *ppMalloc);
// extern void (__stdcall *CoUninitialize)();
// extern HRESULT (__stdcall *CoInitializeEx)(LPVOID pvReserved, DWORD dwCoInit);
// extern HRESULT (__stdcall *CoCreateInstance)(const IID *const rclsid, LPUNKNOWN pUnkOuter, DWORD dwClsContext, const IID *const riid, LPVOID *ppv);
void *CServerLog::`vftable' = &CServerLog::`scalar deleting destructor'; // weak
const char byte_4D53E8 = '\xB7'; // idb
void *COverlappedFactory::`vftable' = &COverlappedFactory::`scalar deleting destructor'; // weak
void *COverlapped::`vftable' = &COverlapped::`scalar deleting destructor'; // weak
void *CSendOverlapped::`vftable' = &CAcceptOverlapped::`vector deleting destructor'; // weak
void *CStreamRecvOverlapped::`vftable' = &CAcceptOverlapped::`vector deleting destructor'; // weak
void *CAcceptOverlapped::`vftable' = &CAcceptOverlapped::`vector deleting destructor'; // weak
void *CStreamOverlappedFactory::`vftable' = &CStreamOverlappedFactory::`vector deleting destructor'; // weak
void *CBufferFactory::`vftable' = &CBufferFactory::`scalar deleting destructor'; // weak
void *CPoolBufferFactory::`vftable' = &CPoolBufferFactory::`vector deleting destructor'; // weak
void *CTCPFactory::`vftable' = &CUDPFactory::`scalar deleting destructor'; // weak
const char aDWorkRylSource_92[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CDLLModule::`vftable' = &CDLLModule::`vector deleting destructor'; // weak
void *CDBGFuncClass::`vftable' = &CDBGFuncClass::`vector deleting destructor'; // weak
void *CThreadMgr::`vftable' = &CThreadMgr::`scalar deleting destructor'; // weak
void *CBufferQueue::`vftable' = &CBufferQueue::`scalar deleting destructor'; // weak
const char aDWorkRylSource_75[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_13[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CListener::`vftable' = &CListener::`vector deleting destructor'; // weak
void *CIOWorker::`vftable' = &CIOWorker::`scalar deleting destructor'; // weak
void *CCompletionHandler::`vftable' = &CCompletionHandler::`scalar deleting destructor'; // weak
const char aDWorkRylSource_10[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_229[] = "CID:0x%08x "; // idb
void *CSendDataToManageClient::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
void *CProcessThread::`vftable' = &CProcessThread::`vector deleting destructor'; // weak
void *CRecvCommandFromManageClient::`vftable' = &CSendDataToManageClient::`vector deleting destructor'; // weak
const char aDWorkRylSource_6[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CServerWindowFramework::`vftable' = &CServerWindowFramework::`vector deleting destructor'; // weak
const BroadcastInfo::ObjectPhase::Type s_CharacterPhase[3] = { CHAR_BASEINFO, CHAR_EQUIPMENTINFO, CHAR_COMMUNITYINFO }; // idb
const char aDWorkRylSource_30[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4D76E0 = '\xC0'; // idb
char byte_4D7718[56] =
{
  '\xC0',
  '\xCC',
  ' ',
  '\xBC',
  '\xBF',
  '\xBF',
  '\xA1',
  '\xB4',
  '\xC2',
  ' ',
  '\xC0',
  '\xCC',
  ' ',
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  '\xB0',
  '\xA1',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'C',
  'I',
  'D',
  ':',
  ' ',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4D7750[72] =
{
  '\xC0',
  '\xCC',
  ' ',
  '\xBC',
  '\xBF',
  '\xBF',
  '\xA1',
  '\xB4',
  '\xC2',
  ' ',
  '\xC0',
  '\xCC',
  ' ',
  '\xC7',
  '\xD8',
  '\xB4',
  '\xE7',
  ' ',
  '\xB0',
  '\xF8',
  '\xBC',
  '\xBA',
  '\xB0',
  '\xFC',
  '\xB7',
  '\xC3',
  ' ',
  '\xBF',
  '\xC0',
  '\xBA',
  '\xEA',
  '\xC1',
  '\xA7',
  '\xC6',
  '\xAE',
  '\xB0',
  '\xA1',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'C',
  'I',
  'D',
  ':',
  ' ',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char aCid0x08x_59[] = "CID:0x%08x "; // idb
const char asc_4D77D0[] = "["; // idb
char byte_4D7808[36] =
{
  '\xB1',
  '\xE2',
  '\xC5',
  '\xB8',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  '\xC0',
  '\xBB',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4D783C = '\xC0'; // idb
const char byte_4D7874 = '\xC0'; // idb
const char byte_4D78A8 = '\xC0'; // idb
const char byte_4D78DC = '\xC7'; // idb
const char aDWorkRylSource_99[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_182[] = "CID:0x%08x "; // idb
const char aCid0x08x_131[] = "CID:0x%08x "; // idb
const char aCid0x08x_188[] = "CID:0x%08x "; // idb
const char aCid0x08x_194[] = "CID:0x%08x "; // idb
const char aCid0x08xDDD[] = "CID:0x%08x (%d,%d,%d) "; // idb
const char aCid0x08x_96[] = "CID:0x%08x "; // idb
const char aCid0x08x_110[] = "CID:0x%08x "; // idb
const char aCid0x08x_23[] = "CID:0x%08x "; // idb
const char aCid0x08x_311[] = "CID:0x%08x "; // idb
const char aCid0x08x_234[] = "CID:0x%08x "; // idb
const char aCid0x08x_61[] = "CID:0x%08x "; // idb
const char aCid0x08x_336[] = "CID:0x%08x "; // idb
const char aCid0x08x_45[] = "CID:0x%08x "; // idb
const char aCid0x08x_183[] = "CID:0x%08x "; // idb
void *Item::CNullItem::`vftable' = &Item::CNullItem::`vector deleting destructor'; // weak
void *Item::CItemContainer::`vftable' = &Item::CItemContainer::`vector deleting destructor'; // weak
const char aCid0x08x_79[] = "CID:0x%08x "; // idb
const char szDetail = '\xBE'; // idb
char byte_4D7F30[28] =
{
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '\0',
  '\0'
}; // weak
const char aCid0x08x_58[] = "CID:0x%08x "; // idb
void *Item::CArrayContainer::`vftable' = &Item::CArrayContainer::`vector deleting destructor'; // weak
void *Item::CListContainer::`vftable' = &Item::CListContainer::`vector deleting destructor'; // weak
const char aDWorkRylSource_27[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4D8088 = '\xBE'; // idb
const char aDWorkRylSource_96[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aUidDCid0x08x0x_8[] = "UID:%d/CID:0x%08x(0x%p)/DispatchPointer:0x%p"; // idb
const char aUidDCidD[] = "UID:%d/CID:%d "; // idb
const char aCid0x08x_46[] = "CID:0x%08x "; // idb
const char aCid0x08x_26[] = "CID:0x%08x "; // idb
void *CCharacter::`vftable' = &CCharacter::`scalar deleting destructor'; // weak
const char byte_4D83AC = '\xB5'; // idb
const char aCid0x08x_270[] = "(CID:0x%08x) "; // idb
char aCid0x08x_228[13] = "(CID:0x%08x) "; // weak
const char aCid0x08x_44[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_28[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_261[] = "CID:0x%08x, "; // idb
const char aCid0x08x_332[] = "CID:0x%08x "; // idb
const char aCid0x08x_85[] = "CID:0x%08x "; // idb
const char aCid0x08x_144[] = "CID:0x%08x "; // idb
const char aCid0x08x_207[] = "CID:0x%08x "; // idb
const char aCid0x08x_276[] = "CID:0x%08x "; // idb
const char aCid0x08x_168[] = "CID:0x%08x "; // idb
const char aCid0x08x_126[] = "CID:0x%08x "; // idb
const char aCid0x08x_251[] = "CID:0x%08x "; // idb
const char aCid0x08x_25[] = "CID:0x%08x "; // idb
const char aCid0x08x_148[] = "CID:0x%08x "; // idb
const char aCid0x08x_211[] = "CID:0x%08x "; // idb
const char aCid0x08x_314[] = "CID:0x%08x "; // idb
const char aCid0x08x_133[] = "CID:0x%08x "; // idb
const char aCid0x08x_146[] = "CID:0x%08x "; // idb
const char aCid0x08x_77[] = "CID:0x%08x "; // idb
const char *const szNoRemove_6 = "NoRemove"; // idb
void *CSpell::`vftable' = &CSpell::`scalar deleting destructor'; // weak
void *CInvincibleSpell::`vftable' = &CInvincibleSpell::`scalar deleting destructor'; // weak
const char aBattleServerLo_4[] = "Battle Server Log :: (Channel : %d, %s) - CID : 0x%08x "; // idb
char aHuma[4] = "HUMA"; // weak
char aAkha[4] = "AKHA"; // weak
const char aDWorkRylSource_41[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_92[] = "CID:0x%08x "; // idb
const char aCid0x08x_64[] = "CID:0x%08x "; // idb
const char aCid0x08x_111[] = "CID:0x%08x "; // idb
const char aCid0x08x_298[] = "CID:0x%08x "; // idb
const char aCid0x08x_121[] = "CID:0x%08x "; // idb
const char a1[] = "#1 "; // idb
const char a2[] = "#2 "; // idb
char aCid0x08x_53[11] = "CID:0x%08x "; // weak
const char byte_4D9178 = '\xBD'; // idb
const char aCid0x08x_98[] = "CID:0x%08x "; // idb
const char aCid0x08x_248[] = "CID:0x%08x "; // idb
const char aCid0x08x_274[] = "CID : 0x%08x, "; // idb
const char *const szNoRemove_7 = "NoRemove"; // idb
const char *const szDelete_7 = "Delete"; // idb
const __int64 ExpTable_0[99] =
{
  100LL,
  230LL,
  480LL,
  950LL,
  1700LL,
  2800LL,
  4600LL,
  7100LL,
  10500LL,
  20200LL,
  29000LL,
  40100LL,
  54200LL,
  71800LL,
  93100LL,
  118000LL,
  149000LL,
  185000LL,
  228000LL,
  295000LL,
  375000LL,
  468000LL,
  576000LL,
  701000LL,
  843000LL,
  1005000LL,
  1188000LL,
  1394000LL,
  1625000LL,
  1986000LL,
  2394000LL,
  2852000LL,
  3321000LL,
  3765000LL,
  4239000LL,
  4746000LL,
  5287000LL,
  5860000LL,
  6643000LL,
  7477000LL,
  8363000LL,
  9303000LL,
  10290000LL,
  11350000LL,
  12460000LL,
  13630000LL,
  14860000LL,
  16150000LL,
  17510000LL,
  19200000LL,
  20980000LL,
  22840000LL,
  24790000LL,
  26840000LL,
  28970000LL,
  31210000LL,
  33540000LL,
  35960000LL,
  39910000LL,
  44060000LL,
  48390000LL,
  52920000LL,
  57650000LL,
  62580000LL,
  67730000LL,
  73080000LL,
  78650000LL,
  84440000LL,
  90460000LL,
  100560000LL,
  111100000LL,
  122070000LL,
  133490000LL,
  145370000LL,
  157710000LL,
  170520000LL,
  183810000LL,
  197580000LL,
  211840000LL,
  236460000LL,
  262020000LL,
  288570000LL,
  316110000LL,
  344660000LL,
  374220000LL,
  404820000LL,
  436480000LL,
  469200000LL,
  503000000LL,
  734430000LL,
  980070000LL,
  1240400000LL,
  1516100000LL,
  1807600000LL,
  2115300000LL,
  2440000000LL,
  2782100000LL,
  3142100000LL,
  3520700000LL
}; // idb
const char aDWorkRylSource_52[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4D9828 = '\xC5'; // idb
const char aCid0x08x_269[] = "CID:0x%08x "; // idb
const char aCid0x08x_340[] = "CID:0x%08x "; // idb
char aCid0x08x_138[11] = "CID:0x%08x "; // weak
const char aCid0x08x_165[] = "CID:0x%08x "; // idb
const char aCid0x08x_76[] = "CID:0x%08x "; // idb
const char aCid0x08x_224[] = "CID:0x%08x "; // idb
const char aCid0x08x_125[] = "CID:0x%08x "; // idb
char aCid0x08x_279[11] = "CID:0x%08x "; // weak
const char aCid0x08x_31[] = "CID:0x%08x "; // idb
const char aCid0x08x_199[] = "CID:0x%08x "; // idb
const char aCid0x08x_86[] = "CID:0x%08x "; // idb
const char aCid0x08x_271[] = "CID:0x%08x "; // idb
const char byte_4D9CD8 = '\xBD'; // idb
const char byte_4D9D38 = '\xBD'; // idb
char byte_4D9D88[76] =
{
  '\xBD',
  '\xBA',
  '\xC5',
  '\xD7',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xCD',
  '\xBD',
  '\xBA',
  ' ',
  '\xC0',
  '\xE7',
  '\xC8',
  '\xC6',
  '\xB7',
  '\xC3',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xBF',
  '\xE4',
  '\xC3',
  '\xBB',
  '\xC7',
  '\xD1',
  ' ',
  '\xC0',
  '\xA7',
  '\xC4',
  '\xA1',
  '\xBF',
  '\xA1',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  '\xC0',
  '\xCC',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'P',
  'o',
  's',
  ':',
  '%',
  'd',
  ',',
  ' ',
  'I',
  'n',
  'd',
  'e',
  'x',
  ':',
  '%',
  'd',
  '\0',
  '\0'
}; // weak
const char aCid0x08x_299[] = "CID:0x%08x "; // idb
const char aCid0x08x_84[] = "CID:0x%08x "; // idb
const char aCid0x08x_178[] = "CID:0x%08x "; // idb
const float FLOAT_1_4[] = { 1.4 }; // weak
const char aCid0x08x_62[] = "CID:0x%08x "; // idb
const char aCid0x08x_123[] = "CID:0x%08x "; // idb
const char aCid0x08x_278[] = "CID:0x%08x "; // idb
char aCid0x08x_231[11] = "CID:0x%08x "; // weak
char aCid0x08x_232[11] = "CID:0x%08x "; // weak
const char aCid0x08xNpc_0[] = "CID:0x%08x NPC"; // idb
const char aDWorkRylSource_89[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08xNpc[] = "CID:0x%08x NPC"; // idb
const char aCid0x08x_145[] = "CID:0x%08x "; // idb
char aCid0x08x_322[11] = "CID:0x%08x "; // weak
const char aCid0x08x_341[] = "CID:0x%08x "; // idb
const char byte_4DA314 = '\xB3'; // idb
const char byte_4DA348 = '\xB3'; // idb
const char aCid0x08x_263[] = "CID:0x%08x "; // idb
const char aCid0x08x_1[] = "CID:0x%08x "; // idb
const char byte_4DA418 = '\xB3'; // idb
const char *const szNoRemove_9 = "NoRemove"; // idb
const char aUidDCid0x08x0x_4[] = "UID:%d/CID:0x%08x(0x%p)/DispatchPointer:0x%p "; // idb
const char aCid0x08xDb[] = "CID:0x%08x DB"; // idb
const char aUidDCid0x08x0x_5[] = "UID:%d/CID:0x%08x(0x%p)/DispatchPointer:0x%p "; // idb
const char aCid0x08x_200[] = "CID:0x%08x "; // idb
char aCid0x08x_75[11] = "CID:0x%08x "; // weak
const char aDWorkRylSource_1[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_324[] = "CID:0x%08x "; // idb
const char aUidDCid0x08x0x_3[] = "UID:%d/CID:0x%08x(0x%p)/DispatchUID:%d/DispatchPointer:0x%p "; // idb
const char aCid0x08x_304[] = "CID:0x%08x "; // idb
void *CCreature::`vftable' = &CCreature::`scalar deleting destructor'; // weak
void *CAggresiveCreature::`vftable' = &CAggresiveCreature::`scalar deleting destructor'; // weak
const char aDWorkRylSource_88[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_107[] = "CID:0x%08x "; // idb
const float fHuntingAffect[41] =
{
  0.2,
  0.23999999,
  0.28,
  0.31999999,
  0.36000001,
  0.40000001,
  0.44,
  0.47999999,
  0.51999998,
  0.56,
  0.60000002,
  0.63999999,
  0.68000001,
  0.72000003,
  0.75999999,
  0.80000001,
  0.83999997,
  0.88,
  0.92000002,
  0.95999998,
  1.0,
  1.08,
  1.16,
  1.24,
  1.3200001,
  1.4,
  1.48,
  1.5599999,
  1.64,
  1.72,
  1.8,
  1.88,
  1.96,
  2.04,
  2.1199999,
  2.2,
  2.28,
  2.3599999,
  2.4400001,
  2.52,
  2.5999999
}; // idb
const float fPvPAffect[41] =
{
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.60000002,
  0.63999999,
  0.68000001,
  0.72000003,
  0.75999999,
  0.80000001,
  0.83999997,
  0.88,
  0.92000002,
  0.95999998,
  1.0,
  1.08,
  1.16,
  1.24,
  1.3200001,
  1.4,
  1.48,
  1.5599999,
  1.64,
  1.72,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8,
  1.8
}; // idb
const char aCid0x08x_18[] = "CID:0x%08x "; // idb
const char byte_4DAECC = '\xBD'; // idb
const char aCid0x08x_16[] = "CID:0x%08x "; // idb
void *CPacketDispatch::`vftable' = &CPacketDispatch::`vector deleting destructor'; // weak
void *CRylServerDispatch::`vftable' = &CRylServerDispatch::`vector deleting destructor'; // weak
const char aDWorkRylSource_40[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_45[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid10u_2[] = "CID:%10u "; // idb
char aCid10u_0[9] = "CID:%10u "; // weak
char aCid10u_1[9] = "CID:%10u "; // weak
char aCid10u[9] = "CID:%10u "; // weak
const char aDp0xPUidD_0[] = "DP:0x%p/UID:%d "; // idb
char aDp0xPUidD[15] = "DP:0x%p/UID:%d "; // weak
void *CGameClientDispatch::`vftable' = &CGameClientDispatch::`vector deleting destructor'; // weak
const char aUidDCid0x08xNa_2[] = "/UID:%d/CID:0x%08x/Name:%s/IP:%15s/"; // idb
const char aUidDCid0x08xSi_0[] = "UID:%d/CID:0x%08x/SID:0x%08x/RequestKey:%d/DispatchPointer:0x%p "; // idb
const char aUidDCid0x08xSi[] = "UID:%d/CID:0x%08x/SID:0x%08x "; // idb
const char aDWorkRylSource_35[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aUidDCid0x08xSi_1[] = "UID:%d/CID:0x%08x/SID:0x%08x "; // idb
const char byte_4DB9F0 = '\xB1'; // idb
const char aMovezone[] = "MoveZone"; // idb
const char byte_4DBA94 = '\xC4'; // idb
const char aServerzone[] = "ServerZone "; // idb
const char aUidDCid0x08x0x_9[] = "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/DispatchPointer:0x%p ServerZone"; // idb
const char aCid0x08x_113[] = "CID:0x%08x "; // idb
void *CBattleSongSpell::`vftable' = &CBattleSongSpell::`vector deleting destructor'; // weak
void *CMaintenanceChantSpell::`vftable' = &CMaintenanceChantSpell::`vector deleting destructor'; // weak
void *CAccelerationChantSpell::`vftable' = &CAccelerationChantSpell::`vector deleting destructor'; // weak
void *CLifeAuraSpell::`vftable' = &CLifeAuraSpell::`vector deleting destructor'; // weak
void *CDefencePotionSpell::`vftable' = &CDefencePotionSpell::`scalar deleting destructor'; // weak
void *CDisenchantPotionSpell::`vftable' = &CDisenchantPotionSpell::`vector deleting destructor'; // weak
void *CMagicPotionSpell::`vftable' = &CMagicPotionSpell::`vector deleting destructor'; // weak
void *CLightningPotionSpell::`vftable' = &CLightningPotionSpell::`vector deleting destructor'; // weak
void *CRegenerationSpell::`vftable' = &CRegenerationSpell::`vector deleting destructor'; // weak
void *CStrengthSpell::`vftable' = &CStrengthSpell::`scalar deleting destructor'; // weak
void *CBlazeSpell::`vftable' = &CBlazeSpell::`scalar deleting destructor'; // weak
void *CChargingSpell::`vftable' = &CChargingSpell::`vector deleting destructor'; // weak
void *CStealthSpell::`vftable' = &CStealthSpell::`vector deleting destructor'; // weak
void *CManaShellSpell::`vftable' = &CManaShellSpell::`vector deleting destructor'; // weak
void *CEncourageSpell::`vftable' = &CEncourageSpell::`vector deleting destructor'; // weak
void *CEnchantWeaponSpell::`vftable' = &CEnchantWeaponSpell::`vector deleting destructor'; // weak
void *CBrightArmorSpell::`vftable' = &CBrightArmorSpell::`scalar deleting destructor'; // weak
void *CHardenSkinSpell::`vftable' = &CHardenSkinSpell::`scalar deleting destructor'; // weak
void *CFlexibilitySpell::`vftable' = &CFlexibilitySpell::`scalar deleting destructor'; // weak
void *CGuardSpell::`vftable' = &CGuardSpell::`vector deleting destructor'; // weak
void *CSlowSpell::`vftable' = &CSlowSpell::`scalar deleting destructor'; // weak
void *CArmorBrokenSpell::`vftable' = &CArmorBrokenSpell::`vector deleting destructor'; // weak
void *CHoldSpell::`vftable' = &CHoldSpell::`scalar deleting destructor'; // weak
void *CStunSpell::`vftable' = &CStunSpell::`scalar deleting destructor'; // weak
void *CFrozenSpell::`vftable' = &CFrozenSpell::`vector deleting destructor'; // weak
void *CPoisonedSpell::`vftable' = &CPoisonedSpell::`vector deleting destructor'; // weak
void *CLowerStrengthSpell::`vftable' = &CLowerStrengthSpell::`vector deleting destructor'; // weak
const __int16 `Skill::CFunctions::WeaponMastery'::`2'::nRevision[5] = { 0, 12, 24, 36, 48 }; // idb
const char aDWorkRylSource_68[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DBE38 = '\xBD'; // idb
const char byte_4DBEE8 = '\xC0'; // idb
const char byte_4DBFA8 = '\xBD'; // idb
const char aCid0x08x_268[] = "CID:0x%08x "; // idb
const char aCid0x08x_89[] = "CID:0x%08x "; // idb
const __int16 nPlusLockCountBonus[5] = { 0, 300, 900, 1800, 3000 }; // idb
const __int16 nMultiplyLockCountBonus[5] = { 0, 50, 100, 150, 200 }; // idb
const float fDRCs[5] = { 1.3, 1.6, 1.9, 2.2, 2.5 }; // idb
const __int16 nPlusLockCountBonus_0[5] = { 0, 480, 1440, 2880, 4800 }; // idb
const __int16 nMultiplyLockCountBonus_0[5] = { 0, 80, 160, 240, 320 }; // idb
char aCid0x08x_179[11] = "CID:0x%08x "; // weak
const __int16 nPlusLockCountBonus_1[5] = { 0, 420, 1260, 2520, 3960 }; // idb
const __int16 nMultiplyLockCountBonus_1[5] = { 0, 70, 140, 210, 280 }; // idb
const float fDRCs_0[5] = { 0.60000002, 0.64999998, 0.69999999, 0.75999999, 0.81999999 }; // idb
__int16 nSkillLevels[6] = { 0, 6, 12, 18, 24, 0 }; // weak
const float fSwordDRCs[5] = { 1.4, 1.7, 2.0, 2.3, 2.5999999 }; // idb
const char aCid0x08x_267[] = "CID:0x%08x "; // idb
const unsigned __int16 nLockCountTickBonus[5] = { 0u, 120u, 360u, 720u, 1200u }; // idb
const float fDRCs_1[5] = { 1.8, 2.0999999, 2.4000001, 2.7, 3.0 }; // idb
const __int16 nPlusLockCountBonus_2[5] = { 0, 240, 720, 1440, 2400 }; // idb
const __int16 nMultiplyLockCountBonus_2[5] = { 0, 40, 80, 120, 160 }; // idb
const __int16 nResistanceBonus[5] = { 0, 10, 20, 30, 40 }; // idb
__int16 nLockCountBonus[6] = { 0, 4, 8, 12, 16, 0 }; // weak
__int16 nLevelBonus[6] = { 8, 14, 20, 26, 32, 0 }; // weak
__int16 nTickBonus[6] = { 1, 2, 3, 4, 5, 0 }; // weak
const char aSkillCfunction_83[] = "Skill::CFunctions::FastH"; // idb
float flt_4DCA70[] = { ?flt }; // weak
const float fDRCs_3[5] = { 1.2, 1.5, 1.8, 2.0999999, 2.4000001 }; // idb
const float fDRCs_4[5] = { 1.4, 1.6, 1.8, 2.0, 2.2 }; // idb
__int16 nTickPlusCountBonus2nd[6] = { 0, 2, 4, 6, 8, 0 }; // weak
__int16 nTickPlusCountBonus1st[6] = { 0, 1, 2, 3, 4, 0 }; // weak
const __int16 nPlusLockCountBonus_3[5] = { 0, 420, 1260, 2520, 4200 }; // idb
__int16 nResistanceBonus_0[6] = { 20, 40, 60, 80, 100, 0 }; // weak
__int16 nEnchantTick[6] = { 0, 20, 40, 60, 80, 0 }; // weak
__int16 nEnchantTick_0[6] = { 10, 20, 30, 40, 50, 0 }; // weak
__int16 nEnchantLevel[6] = { 5, 10, 15, 20, 25, 0 }; // weak
const char byte_4DCC78 = '\xB4'; // idb
const char byte_4DCCF4 = '\xB8'; // idb
const char aDWorkRylSource_49[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DCDF0 = '\xBC'; // idb
char byte_4DCE10[40] =
{
  '\xBC',
  '\xD2',
  '\xC8',
  '\xAF',
  ' ',
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  ' ',
  '\xC3',
  '\xCA',
  '\xB1',
  '\xE2',
  '\xC8',
  '\xAD',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xCF',
  '\xBF',
  '\xB4',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DCE54 = '\xB8'; // idb
const char aXDZD[] = "X:%d, Z:%d "; // idb
const char aMapindex0x04xX[] = "MapIndex:0x%04x, X:%d(Max:%d), Z:%d(Max:%d) "; // idb
const char aMapindex0x04x[] = "MapIndex:0x%04x, "; // idb
const char aXDYDZD[] = "X:%d, Y:%d, Z:%d "; // idb
void *std::istream::`vftable' = &std::istream::`vector deleting destructor'; // weak
void *std::iostream::`vftable' = &std::iostream::`vector deleting destructor'; // weak
const char aState[] = "State"; // idb
const char byte_4DCFAC = '\xB8'; // idb
const char byte_4DCFCC = '\xB8'; // idb
const char byte_4DCFEC = '\xB8'; // idb
const char aKidD_0[] = "KID : %d "; // idb
const char byte_4DD040 = '\xC0'; // idb
char byte_4DD084[44] =
{
  '\xC0',
  '\xDF',
  '\xB8',
  '\xF8',
  '\xB5',
  '\xC8',
  ' ',
  'C',
  'I',
  'D',
  '\xB8',
  '\xA6',
  ' ',
  '\xB0',
  '\xA1',
  '\xC1',
  '\xF8',
  ' ',
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  '\xB8',
  '\xA6',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xC7',
  '\xCF',
  '\xB7',
  '\xC1',
  ' ',
  '\xC7',
  '\xD5',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0'
}; // weak
const char aS_3[] = "%s "; // idb
char byte_4DD0E8[32] =
{
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xCF',
  '\xBF',
  '\xB4',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char aKindidD[10] = "KindID:%d "; // weak
char byte_4DD160[72] =
{
  '\xC7',
  '\xF6',
  '\xC0',
  '\xE7',
  ' ',
  '\xC1',
  '\xB8',
  '\xBF',
  '\xA1',
  ' ',
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  ' ',
  '\xBC',
  '\xF6',
  '\xB0',
  '\xA1',
  ' ',
  '\xC3',
  '\xD6',
  '\xB4',
  '\xEB',
  '\xC0',
  '\xD4',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xB4',
  '\xF5',
  '\xC0',
  '\xCC',
  '\xBB',
  '\xF3',
  ' ',
  '\xB8',
  '\xF3',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xCD',
  '\xB8',
  '\xA6',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
const char aKidD[] = "KID : %d  "; // idb
void *std::num_put<char,std::ostreambuf_iterator<char>>::`vftable' = &std::codecvt<char,char,int>::`vector deleting destructor'; // weak
_UNKNOWN std::iostream::`vbtable'{for `std::ostream'}; // weak
_UNKNOWN std::iostream::`vbtable'{for `std::istream'}; // weak
void *std::numpunct<char>::`vftable' = &std::numpunct<char>::`scalar deleting destructor'; // weak
void *std::fstream::`vftable' = &std::fstream::`vector deleting destructor'; // weak
_UNKNOWN std::fstream::`vbtable'{for `std::ostream'}; // weak
_UNKNOWN std::fstream::`vbtable'{for `std::istream'}; // weak
const char byte_4DD2E0 = '\xBC'; // idb
const char Val[] = "X/Z/"; // idb
const char byte_4DD340 = '\xBE'; // idb
const char byte_4DD3C4 = '\xBC'; // idb
const char byte_4DD414 = '\xB8'; // idb
const char byte_4DD42C = '\xB0'; // idb
const char aVirtualarea_0[] = "VirtualArea "; // idb
const char byte_4DD464 = '\xB8'; // idb
const char aNpc[] = "NPC "; // idb
const char byte_4DD570 = '\xC0'; // idb
const char aDWorkRylSource_19[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DD63C = '\xB5'; // idb
char byte_4DD660[80] =
{
  '\xBE',
  '\xD9',
  '\xC5',
  '\xCD',
  '\xB3',
  '\xCA',
  '\xC6',
  '\xBC',
  '\xBA',
  '\xEA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xC6',
  '\xD0',
  '\xBD',
  '\xC3',
  '\xBA',
  '\xEA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  '\xC0',
  '\xCC',
  ' ',
  '\xBA',
  '\xCE',
  '\xB8',
  '\xF0',
  '\xB7',
  '\xCE',
  ' ',
  '\xBC',
  '\xB3',
  '\xC1',
  '\xA4',
  '\xB5',
  '\xC7',
  '\xBE',
  '\xFA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'k',
  'i',
  'l',
  'l',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '4',
  'x',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DD6B0[80] =
{
  '\xBE',
  '\xD9',
  '\xC5',
  '\xCD',
  '\xB3',
  '\xCA',
  '\xC6',
  '\xBC',
  '\xBA',
  '\xEA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xC3',
  '\xA6',
  '\xC6',
  '\xAE',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  '\xC0',
  '\xCC',
  ' ',
  '\xC0',
  '\xDA',
  '\xBD',
  '\xC4',
  '\xC0',
  '\xB8',
  '\xB7',
  '\xCE',
  ' ',
  '\xBC',
  '\xB3',
  '\xC1',
  '\xA4',
  '\xB5',
  '\xC7',
  '\xBE',
  '\xFA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'k',
  'i',
  'l',
  'l',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '4',
  'x',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DD700[88] =
{
  '\xBE',
  '\xD9',
  '\xC5',
  '\xCD',
  '\xB3',
  '\xCA',
  '\xC6',
  '\xBC',
  '\xBA',
  '\xEA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xB4',
  '\xC2',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  '\xC0',
  '\xCC',
  ' ',
  '\xC0',
  '\xDA',
  '\xBD',
  '\xC4',
  '\xC0',
  '\xB8',
  '\xB7',
  '\xCE',
  ' ',
  '\xBC',
  '\xB3',
  '\xC1',
  '\xA4',
  '\xB5',
  '\xC7',
  '\xBE',
  '\xFA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'k',
  'i',
  'l',
  'l',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '4',
  'x',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DD758[84] =
{
  '\xBE',
  '\xD9',
  '\xC5',
  '\xCD',
  '\xB3',
  '\xCA',
  '\xC6',
  '\xBC',
  '\xBA',
  '\xEA',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xB4',
  '\xC2',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  '\xC0',
  '\xCC',
  ' ',
  '\xBA',
  '\xCE',
  '\xB8',
  '\xF0',
  '\xB7',
  '\xCE',
  ' ',
  '\xBC',
  '\xB3',
  '\xC1',
  '\xA4',
  '\xB5',
  '\xC7',
  '\xBE',
  '\xFA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'k',
  'i',
  'l',
  'l',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '4',
  'x',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DD7D0 = '\xBE'; // idb
const char byte_4DD828 = '\xBD'; // idb
char byte_4DD854[56] =
{
  '\xBD',
  '\xBA',
  '\xC5',
  '\xB3',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  '\xC0',
  '\xC7',
  ' ',
  '\xC0',
  '\xDF',
  '\xB8',
  '\xF8',
  '\xB5',
  '\xC8',
  ' ',
  '\xBA',
  '\xCE',
  '\xB8',
  '\xF0',
  '/',
  '\xC0',
  '\xDA',
  '\xBD',
  '\xC4',
  ' ',
  '\xB0',
  '\xFC',
  '\xB0',
  '\xE8',
  '\xB8',
  '\xA6',
  ' ',
  '\xBC',
  '\xF6',
  '\xC1',
  '\xA4',
  '\xC7',
  '\xD8',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xBC',
  '\xBF',
  '\xE4',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DD88C = '\xB8'; // idb
const char byte_4DD8A8 = '\xB0'; // idb
const char byte_4DD8D8 = '\xBD'; // idb
const char byte_4DDAEC = '\xBD'; // idb
const char aDWorkRylSource_11[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DDD38 = '\xC6'; // idb
char byte_4DDD5C[28] =
{
  '\xC5',
  '\xAC',
  '\xB6',
  '\xF3',
  '\xC0',
  '\xCC',
  '\xBE',
  '\xF0',
  '\xC6',
  '\xAE',
  ' ',
  '\xC1',
  '\xA6',
  '\xC7',
  '\xD1',
  ' ',
  '\xB9',
  '\xF6',
  '\xC1',
  '\xAF',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DDD84[16] =
{
  '\xC8',
  '\xA5',
  '\xC0',
  '\xDA',
  ' ',
  '\xB8',
  '\xF0',
  '\xB5',
  '\xE5',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0'
}; // weak
char byte_4DDD9C[16] =
{
  '\xC7',
  '\xCE',
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DDDB4[32] =
{
  '\xB7',
  '\xCE',
  '\xB1',
  '\xD7',
  '\xC0',
  '\xCE',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xB5',
  '\xF0',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DDE1C = '\xC0'; // idb
char byte_4DDE4C[40] =
{
  '\xC0',
  '\xCE',
  '\xC1',
  '\xF5',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  '\xB0',
  '\xA1',
  ' ',
  '\xB9',
  '\xE8',
  '\xC6',
  '\xB2',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  '\xC0',
  '\xCE',
  '\xC1',
  '\xF6',
  ' ',
  '\xBF',
  '\xA9',
  '\xBA',
  '\xCE',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0'
}; // weak
char byte_4DDE88[28] =
{
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DDEB0[20] =
{
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC5',
  '\xB8',
  '\xC0',
  '\xD4',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0'
}; // weak
char byte_4DDED0[16] =
{
  '\xC7',
  '\xD1',
  '\xB1',
  '\xDB',
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0'
}; // weak
char byte_4DDEEC[28] =
{
  '\xC0',
  '\xCE',
  '\xC1',
  '\xF5',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xB5',
  '\xF0',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0'
}; // weak
const char byte_4DDF30 = '\xC6'; // idb
char byte_4DDFA0[36] =
{
  '\xC6',
  '\xC4',
  '\xC6',
  '\xAE',
  '1',
  ' ',
  '\xC5',
  '\xEB',
  '\xC7',
  '\xD5',
  ' ',
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0'
}; // weak
char byte_4DDFDC[32] =
{
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xB1',
  '\xB9',
  '\xB0',
  '\xA1',
  ' ',
  '\xC5',
  '\xB8',
  '\xC0',
  '\xD4',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE00C[28] =
{
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC5',
  '\xB8',
  '\xC0',
  '\xD4',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE038[40] =
{
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  '\xB0',
  '\xA1',
  ' ',
  '\xB9',
  '\xE8',
  '\xC6',
  '\xB2',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  '\xC0',
  '\xCE',
  '\xC1',
  '\xF6',
  ' ',
  '\xBF',
  '\xA9',
  '\xBA',
  '\xCE',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0'
}; // weak
char byte_4DE074[24] =
{
  '\xB0',
  '\xF8',
  '\xBC',
  '\xBA',
  ' ',
  '\xB3',
  '\xA1',
  ' ',
  '\xBD',
  '\xC3',
  '\xB0',
  '\xA3',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0'
}; // weak
char byte_4DE09C[28] =
{
  '\xB0',
  '\xF8',
  '\xBC',
  '\xBA',
  ' ',
  '\xBD',
  '\xC3',
  '\xC0',
  '\xDB',
  ' ',
  '\xBD',
  '\xC3',
  '\xB0',
  '\xA3',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char aUid[4] = "UID "; // weak
char byte_4DE0E8[28] =
{
  '\xB7',
  '\xCE',
  '\xB1',
  '\xD7',
  '\xC0',
  '\xCE',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0'
}; // weak
char byte_4DE110[20] =
{
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  '\xBC',
  '\xB6',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE130[36] =
{
  '\xB9',
  '\xE8',
  '\xC6',
  '\xB2',
  '\xB1',
  '\xD7',
  '\xB6',
  '\xF3',
  '\xBF',
  '\xEE',
  '\xB5',
  '\xE5',
  ' ',
  '\xC1',
  '\xA6',
  '\xC7',
  '\xD1',
  ' ',
  '\xC0',
  '\xCE',
  '\xBF',
  '\xF8',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE160[16] =
{
  '\xC0',
  '\xAF',
  '\xC0',
  '\xFA',
  ' ',
  '\xC1',
  '\xA6',
  '\xC7',
  '\xD1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0'
}; // weak
char byte_4DE180[20] =
{
  '\xBF',
  '\xB5',
  '\xC0',
  '\xDA',
  ' ',
  'I',
  'P',
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE1A4[28] =
{
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xB0',
  '\xFA',
  '\xB1',
  '\xDD',
  ' ',
  '\xC5',
  '\xB8',
  '\xC0',
  '\xD4',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE1C0[28] =
{
  '\xC1',
  '\xDF',
  '\xB0',
  '\xE8',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xB5',
  '\xF0',
  ' ',
  '\xBE',
  '\xF2',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0'
}; // weak
const char byte_4DE218 = '\xC7'; // idb
char byte_4DE250[24] =
{
  '\xC7',
  '\xC3',
  '\xB7',
  '\xB9',
  '\xB1',
  '\xD7',
  ' ',
  '\xB9',
  '\xAB',
  '\xBD',
  '\xC3',
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE274[16] =
{
  '\xC7',
  '\xC1',
  '\xB8',
  '\xAE',
  '\xC3',
  '\xBC',
  '\xC5',
  '\xA9',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0',
  '\0'
}; // weak
const char byte_4DE2B8 = '\xBF'; // idb
char byte_4DE310[24] =
{
  '\xC3',
  '\xA4',
  '\xC6',
  '\xC3',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  'I',
  'D',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0'
}; // weak
void *CServerSetup::`vftable' = &CServerSetup::`scalar deleting destructor'; // weak
const char byte_4DE40C = '\xB9'; // idb
char byte_4DE45C[44] =
{
  '\xB5',
  '\xF0',
  '\xB9',
  '\xF6',
  '\xB1',
  '\xD7',
  '\xBF',
  '\xEB',
  ' ',
  'U',
  'I',
  'D',
  ' ',
  '\xB8',
  '\xAE',
  '\xBD',
  '\xBA',
  '\xC6',
  '\xAE',
  ' ',
  '\xBB',
  '\xFD',
  '\xBC',
  '\xBA',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE488[32] =
{
  '\xB0',
  '\xD4',
  '\xC0',
  '\xD3',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  'U',
  'D',
  'P',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  '\xB8',
  '\xA6',
  ' ',
  '\xBC',
  '\xBC',
  '\xC6',
  '\xC3',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE4A8[28] =
{
  '\xC3',
  '\xA4',
  '\xC6',
  '\xC3',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4DE4D4[28] =
{
  '\xB7',
  '\xCE',
  '\xB1',
  '\xD7',
  ' ',
  '\xBC',
  '\xAD',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xD6',
  '\xBC',
  '\xD2',
  ' ',
  '\xC0',
  '\xD0',
  '\xB1',
  '\xE2',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char aZ02dC02d_4[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_6[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_5[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_3[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_2[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_9[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_0[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_8[17] = "(z:%02d, c:%02d) "; // weak
char aZ02dC02d_7[17] = "(z:%02d, c:%02d) "; // weak
const char aZ02dC02d_1[] = "(z:%02d, c:%02d) "; // idb
const char aDWorkRylSource_66[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DE84C = '\xBE'; // idb
char byte_4DE880[56] =
{
  '\xB5',
  '\xA5',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xCD',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xD0',
  '\xBE',
  '\xEE',
  '\xBF',
  '\xC3',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'L',
  'i',
  'n',
  'e',
  ':',
  '%',
  'd',
  ',',
  ' ',
  'C',
  'o',
  'l',
  'u',
  'm',
  'n',
  'N',
  'a',
  'm',
  'e',
  ':',
  '%',
  'd',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DE8B8 = '\xBE'; // idb
const char byte_4DE8F8 = '\xB0'; // idb
char byte_4DE924[52] =
{
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  ' ',
  '\xC6',
  '\xC4',
  '\xC0',
  '\xCF',
  '\xC0',
  '\xBB',
  ' ',
  '\xB4',
  '\xD9',
  ' ',
  '\xC0',
  '\xD0',
  '\xC1',
  '\xF6',
  ' ',
  '\xB8',
  '\xF8',
  '\xC7',
  '\xCF',
  '\xB0',
  '\xED',
  ' ',
  '\xC1',
  '\xBE',
  '\xB7',
  '\xE1',
  '\xC7',
  '\xD5',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DE97C = '\xBE'; // idb
char byte_4DE9B0[48] =
{
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  ' ',
  '\xC6',
  '\xC4',
  '\xC0',
  '\xCF',
  ' ',
  '\xB7',
  '\xCE',
  '\xB5',
  '\xF9',
  '\xBF',
  '\xA1',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4DE9E0 = '\xC1'; // idb
const char byte_4DEA50 = '\xC7'; // idb
const char byte_4DEB48 = '\xBE'; // idb
const char aDWorkRylSource_7[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DEC74 = '\xBA'; // idb
void *CGameEventMgr::`vftable' = &CGameEventMgr::`vector deleting destructor'; // weak
const char aD_1[] = "%d "; // idb
char aD_0[3] = "%d "; // weak
const char byte_4DED28 = '\xBF'; // idb
const char byte_4DED54 = '\xB0'; // idb
const char aDWorkRylSource_37[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DEE48 = '\xB0'; // idb
void *CLogSaveThread::`vftable' = &CLogSaveThread::`vector deleting destructor'; // weak
const char byte_4DEE88 = '\xB9'; // idb
const char aFlush_0[] = "Flush"; // idb
char byte_4DEEE4[44] =
{
  '\xB0',
  '\xD4',
  '\xC0',
  '\xD3',
  ' ',
  '\xB7',
  '\xCE',
  '\xB1',
  '\xD7',
  ' ',
  '\xB9',
  '\xF6',
  '\xC6',
  '\xDB',
  '\xB8',
  '\xA6',
  ' ',
  '\xC7',
  '\xD2',
  '\xB4',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xB4',
  '\xC2',
  ' ',
  '\xB5',
  '\xA5',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0'
}; // weak
const char byte_4DEF28 = '\xB7'; // idb
const char byte_4DEF60 = '\xB0'; // idb
const char byte_4DEFE0 = '\xB7'; // idb
void *CChatDispatch::`vftable' = &CChatDispatch::`scalar deleting destructor'; // weak
const char aDWorkRylSource_62[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_24[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_29[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CLogDispatch::`vftable' = &CLogDispatch::`vector deleting destructor'; // weak
const char aDWorkRylSource_95[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DF8EC = '\xB7'; // idb
const char byte_4DF910 = '\xB7'; // idb
const char byte_4DF954 = '\xB7'; // idb
void *CConsoleCMDFactory::`vftable' = &CConsoleCMDFactory::`vector deleting destructor'; // weak
void *CValidateConnection::`vftable' = &_purecall; // weak
void *CLimitUserByIP::`vftable' = &CLimitUserByIP::operator(); // weak
void *CRegularAgentDispatch::`vftable' = &CRegularAgentDispatch::`scalar deleting destructor'; // weak
const char aDWorkRylSource_5[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4DFB78 = '\xC1'; // idb
const char byte_4DFBD8 = '\xC1'; // idb
const char byte_4DFC08 = '\xC1'; // idb
const char aCid0x08x_220[] = "CID:0x%08x "; // idb
const char aUid10uCid10u_3[] = "UID:%10u/CID:%10u/"; // idb
const char aUid10uCid10u_1[] = "UID:%10u/CID:%10u/"; // idb
const char aUid10uCid10u[] = "UID:%10u/CID:%10u/"; // idb
const char aCid0x08x_297[] = "CID:0x%08x "; // idb
const char aUid10uCid10u_0[] = "UID:%10u/CID:%10u/"; // idb
const char aUid10uCid10u_2[] = "UID:%10u/CID:%10u/"; // idb
const char aUid10uCid10u_4[] = "UID:%10u/CID:%10u/"; // idb
const char aCid0x08x_254[] = "CID:0x%08x "; // idb
const char aCid0x08xDatare_0[] = "CID:0x%08x/DataRequestCount:%u/"; // idb
const char aCid0x08xDatare[] = "CID:0x%08x/DataRequestCount:0x%08x/"; // idb
const char byte_4E00E0 = '\xC6'; // idb
void *CDBComponent::`vftable' = &CDBSingleObject::`scalar deleting destructor'; // weak
const char byte_4E0130 = '\xC3'; // idb
const char byte_4E0170 = '\xC5'; // idb
const char byte_4E01B0 = '\xC1'; // idb
const char byte_4E0230 = '\xC0'; // idb
const char aDWorkRylSource_100[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E0308 = '\xC0'; // idb
void *CDBAgentDispatch::`vftable' = &CDBAgentDispatch::`scalar deleting destructor'; // weak
const char byte_4E03BC = '\xB0'; // idb
const char byte_4E0408 = '\xC1'; // idb
const char aDWorkRylSource_16[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CNullSpell::`vftable' = &CNullSpell::`scalar deleting destructor'; // weak
const char aUidDCid0x08x0x_11[] = "UID:%d/CID:0x%08x(0x%p)/DispatchUID:%d/DispatchPointer:0x%p "; // idb
_UNKNOWN unk_4E06C0; // weak
_UNKNOWN unk_4E06CC; // weak
const char aCid0x08x_119[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_86[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E07C8 = '\xC6'; // idb
const char byte_4E0800 = '\xC6'; // idb
const char aCid0x08xDbagen[] = "CID:0x%08x DBAgent"; // idb
const char aUidDCid0x08xUi_0[] = "UID:%d/CID:0x%08x UID"; // idb
const char aCid0x08xCgamec[] = "CID:0x%08x CGameClientDispatch"; // idb
const char aCid0x08x_338[] = "CID:0x%08x "; // idb
const char aCid0x08x_69[] = "CID:0x%08x "; // idb
const char aCid0x08x_189[] = "CID:0x%08x "; // idb
const char aCid0x08x_82[] = "CID:0x%08x "; // idb
const char aUidDCid0x08xAd[] = "UID:%d/CID:0x%08x Admin "; // idb
const char aUidDCid0x08x_0[] = "UID:%d/CID:0x%08x "; // idb
const char aCid0x08xDbagen_1[] = "CID:0x%08x DBAgent"; // idb
char aCid0x08x_117[11] = "CID:0x%08x "; // weak
char aCid0x08x_172[11] = "CID:0x%08x "; // weak
const char aPid0x08x_1[] = "PID:0x%08x "; // idb
const char aDbagentDispatc[] = "DBAgent Dispatch "; // idb
char aCid0x08x_342[11] = "CID:0x%08x "; // weak
char aPid0x08x_4[11] = "PID:0x%08x "; // weak
char aPid0x08x_5[11] = "PID:0x%08x "; // weak
const char byte_4E0F5C = '\xC6'; // idb
const char aThis0xPDb[] = "this:0x%p DB"; // idb
const char aUidDCid0x08x0x_10[] = "UID:%d/CID:0x%08x(0x%p)/DispatchPointer:0x%p/DBRequest Success - "; // idb
char aUidDCid0x08x0x_7[74] = "UID:%d/CID:0x%08x(0x%p)/DispatchPointer:0x%p/DBRequest Failed - RequestKey"; // weak
const char byte_4E10F4 = '\xC6'; // idb
const char aUidDCid0x08x[] = "UID:%d/CID:0x%08x/"; // idb
const char aUidDCid0x08xUi[] = "UID:%d/CID:0x%08x/UID"; // idb
const char aUidUCid0x08xCg[] = "UID:%u/CID:0x%08x/CGameClientDispatch"; // idb
const char aUidUCid0x08x[] = "UID:%u/CID:0x%08x/"; // idb
const char aDWorkRylSource_8[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_43[] = "CID:0x%08x "; // idb
const char aCid0x08x_257[] = "CID:0x%08x "; // idb
const char aVirtualarea[] = "VirtualArea "; // idb
const char byte_4E1410 = '\xB0'; // idb
const char aVirtualArea[] = "Virtual Area "; // idb
void *CMonster::`vftable' = &CMonster::`vector deleting destructor'; // weak
const char aDWorkRylSource_103[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_14[] = "CID:0x%08x "; // idb
const char aCid0x08x_285[] = "CID:0x%08x "; // idb
const char aCid0x08x_22[] = "CID:0x%08x "; // idb
const char byte_4E1850 = '\xBE'; // idb
const char aCid0x08x_302[] = "CID:0x%08x "; // idb
const char byte_4E18E8 = '\xB8'; // idb
const char byte_4E1928 = '\xB8'; // idb
const char aDWorkRylSource_50[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E1A90 = '\xB5'; // idb
const char aDWorkRylSource_0[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_114[] = "CID:0x%08x "; // idb
const char aUidDCid0x08x0x_12[] = "UID:%d/CID:0x%08x(0x%p)/DispatchUID:%d "; // idb
const char aCid0x08xDbupda_0[] = "CID:0x%08x DBUpdate"; // idb
char aCid0x08xDbupda[19] = "CID:0x%08x DBUpdate"; // weak
const char aCid0x08xDbupda_1[] = "CID:0x%08x DBUpdate"; // idb
void *CConsoleWindow::`vftable' = &CConsoleWindow::`scalar deleting destructor'; // weak
const char aDWorkRylSource_20[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_157[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_55[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *CSummonMonster::`vftable' = &CSummonMonster::`scalar deleting destructor'; // weak
void *CStatue::`vftable' = &CStatue::`scalar deleting destructor'; // weak
const char aDWorkRylSource_51[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E22B4 = '\xBC'; // idb
void *CSkillMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
const char aBattleServerLo_2[] = "Battle Server Log :: (Channel : %d, %s) - "; // idb
const char aBattleServerLo_1[] = "Battle Server Log :: (Channel : %d, %s) - CID : 0x%08x "; // idb
const char aCid0x08x_245[] = "CID:0x%08x "; // idb
const char aCid0x08x_104[] = "CID:0x%08x "; // idb
void *CDefenderMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CWarriorMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CAcolyteMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CMageMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CBossMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CChiefMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
void *CNamedMonster::`vftable' = &CAcolyteMonster::`vector deleting destructor'; // weak
const char aCid0x08x_284[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_46[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_56[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const unsigned __int16 aryItemDropRate[41][10] =
{
  { 200u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 220u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 240u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 260u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 280u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 300u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 320u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 340u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 360u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 380u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 400u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 420u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 440u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 460u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 480u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 500u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 520u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 540u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 560u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 580u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 600u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 640u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 720u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 840u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 100u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 300u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 540u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 820u, 1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 240u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 600u, 1u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 100u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 540u, 1u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 120u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 640u, 1u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 300u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 1u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 640u, 1u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 900u, 420u, 1u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u, 240u, 1u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u, 100u },
  { 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u, 900u }
}; // idb
const float `CThreat::GetAggravation'::`2'::aryAggravation[41] =
{
  0.2,
  0.23999999,
  0.28,
  0.31999999,
  0.36000001,
  0.40000001,
  0.44,
  0.47999999,
  0.51999998,
  0.56,
  0.60000002,
  0.63999999,
  0.68000001,
  0.72000003,
  0.75999999,
  0.80000001,
  0.83999997,
  0.88,
  0.92000002,
  0.95999998,
  1.0,
  1.08,
  1.24,
  1.48,
  1.8,
  2.2,
  2.6800001,
  3.24,
  3.8800001,
  4.5999999,
  5.4000001,
  6.2800002,
  7.2399998,
  8.2799997,
  9.3999996,
  10.6,
  11.88,
  13.24,
  14.68,
  16.200001,
  17.799999
}; // idb
const char byte_4E3270 = '\xBD'; // idb
const char aDWorkRylSource_70[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E334C = '\xBC'; // idb
char byte_4E3368[72] =
{
  '\xBD',
  '\xBA',
  '\xB7',
  '\xB9',
  '\xC6',
  '\xAE',
  ' ',
  '\xB8',
  '\xAE',
  '\xBD',
  '\xBA',
  '\xC6',
  '\xAE',
  '\xB0',
  '\xA1',
  ' ',
  '\xBA',
  '\xF1',
  '\xBE',
  '\xEE',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xC0',
  '\xCC',
  ' ',
  '\xBB',
  '\xF3',
  '\xC5',
  '\xC2',
  '\xBF',
  '\xA1',
  '\xBC',
  '\xAD',
  '\xB4',
  '\xC2',
  ' ',
  'E',
  'x',
  'p',
  '\xB8',
  '\xA6',
  ' ',
  '\xB3',
  '\xAA',
  '\xB4',
  '\xAD',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4E33B0 = '\xB6'; // idb
const char byte_4E33F4 = '\xC0'; // idb
const char aCid0x08x_66[] = "CID:0x%08x "; // idb
const char byte_4E34B0 = '\xC5'; // idb
const char aCid0x08x_40[] = "CID:0x%08x "; // idb
const char aPid0x08x_0[] = "PID:0x%08x "; // idb
const char byte_4E3588 = '\xB8'; // idb
char byte_4E35B8[88] =
{
  '\xBD',
  '\xBA',
  '\xB7',
  '\xB9',
  '\xC6',
  '\xAE',
  ' ',
  '\xB8',
  '\xAE',
  '\xBD',
  '\xBA',
  '\xC6',
  '\xAE',
  '\xB0',
  '\xA1',
  ' ',
  '\xBA',
  '\xF1',
  '\xBE',
  '\xEE',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xC0',
  '\xCC',
  ' ',
  '\xBB',
  '\xF3',
  '\xC5',
  '\xC2',
  '\xBF',
  '\xA1',
  '\xBC',
  '\xAD',
  '\xB4',
  '\xC2',
  ' ',
  '\xB8',
  '\xED',
  '\xBC',
  '\xBA',
  ' ',
  '\xB9',
  '\xD7',
  ' ',
  '\xB0',
  '\xF8',
  '\xC7',
  '\xE5',
  '\xB8',
  '\xDE',
  '\xB4',
  '\xDE',
  '\xC0',
  '\xBB',
  ' ',
  '\xB3',
  '\xAA',
  '\xB4',
  '\xAD',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const __int16 aryEquipAdditionalValue[14][7][6] =
{
  {
    { 40, -1, -1, -1, -1, 40 },
    { 40, -1, -1, -1, -1, 40 },
    { 40, -1, -1, -1, -1, 40 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 40, -1, -1, -1, -1, 40 },
    { 40, -1, -1, -1, -1, 40 }
  },
  {
    { 1, -1, -1, -1, -1, 1 },
    { 1, -1, -1, -1, -1, 1 },
    { 1, -1, -1, -1, -1, 1 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 1, -1, -1, -1, -1, 1 },
    { 1, -1, -1, -1, -1, 1 }
  },
  {
    { 6, 3, 8, 1, 8, 4 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 6, 3, 8, 1, 8, 4 }
  },
  {
    { 4, 1, 8, 3, 8, 6 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 4, 1, 8, 3, 8, 6 }
  },
  {
    { 10, 1, 4, 1, 4, 10 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 1, 1, 8, 1, 8, 1 },
    { 2, 1, 4, 1, 4, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 1, 1, 8, 1, 8, 1 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 4, 1, 4, 1, 2, 5 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 10, 1, 4, 1, 4, 10 },
    { 0, 0, 0, 0, 0, 0 },
    { 1, 1, 8, 1, 8, 1 },
    { 2, 1, 4, 1, 4, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 1, 1, 8, 1, 8, 1 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 4, 1, 4, 1, 4, 4 },
    { 4, 1, 16, 1, 16, 4 },
    { 2, 1, 8, 1, 8, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 4, 1, 16, 1, 16, 4 }
  },
  {
    { 4, 1, 16, 1, 16, 4 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 }
  },
  {
    { 4, 1, 16, 1, 16, 4 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 8, 1, 8, 2 },
    { 0, 0, 0, 0, 0, 0 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 },
    { 2, 1, 2, 1, 2, 2 },
    { 2, 1, 2, 1, 2, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 }
  },
  {
    { 0, 0, 0, 0, 0, 0 },
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 },
    { 2, 1, 2, 1, 2, 2 },
    { 2, 1, 2, 1, 2, 2 },
    { 0, 0, 0, 0, 0, 0 },
    { 2, 1, 2, 1, 2, 2 }
  }
}; // idb
void *Item::CItem::`vftable' = &Item::CItem::`vector deleting destructor'; // weak
const char aDWorkRylSource_65[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aSerializein[] = "SerializeIn"; // idb
void *Item::CUseItem::`vftable' = &Item::CEquipment::`vector deleting destructor'; // weak
void *Item::CEquipment::`vftable' = &Item::CEquipment::`vector deleting destructor'; // weak
const unsigned __int16 s_UpgradeTable[15][7] =
{
  { 80u, 90u, 100u, 20u, 0u, 2001u, 3u },
  { 70u, 80u, 90u, 30u, 0u, 2001u, 6u },
  { 60u, 70u, 80u, 40u, 0u, 2001u, 9u },
  { 0u, 50u, 70u, 50u, 0u, 2002u, 3u },
  { 0u, 40u, 60u, 60u, 0u, 2002u, 6u },
  { 0u, 30u, 50u, 70u, 0u, 2002u, 9u },
  { 0u, 0u, 20u, 40u, 40u, 2003u, 3u },
  { 0u, 0u, 10u, 20u, 70u, 2003u, 6u },
  { 0u, 0u, 5u, 10u, 85u, 2003u, 9u },
  { 0u, 0u, 0u, 0u, 100u, 2004u, 3u },
  { 0u, 0u, 0u, 0u, 100u, 2004u, 6u },
  { 0u, 0u, 0u, 0u, 100u, 2004u, 9u },
  { 0u, 0u, 0u, 0u, 100u, 2005u, 3u },
  { 0u, 0u, 0u, 0u, 100u, 2005u, 6u },
  { 0u, 0u, 0u, 0u, 100u, 2005u, 9u }
}; // idb
__int16 word_4E3C1A[] = { 90 }; // weak
__int16 word_4E3C1C[] = { 100 }; // weak
__int16 word_4E3C20[] = { 0 }; // weak
__int16 word_4E3C24[] = { 3 }; // weak
const char aCid0x08xS[] = "CID:0x%08x %s"; // idb
_UNKNOWN unk_4E3D64; // weak
_UNKNOWN unk_4E3D6C; // weak
_UNKNOWN unk_4E3D7C; // weak
_UNKNOWN unk_4E3D98; // weak
_UNKNOWN unk_4E3DB4; // weak
_UNKNOWN unk_4E3DC4; // weak
_UNKNOWN unk_4E3DCC; // weak
_UNKNOWN unk_4E3DD8; // weak
_UNKNOWN unk_4E3DE0; // weak
_UNKNOWN unk_4E3DE8; // weak
_UNKNOWN unk_4E3DF0; // weak
_UNKNOWN unk_4E3DF8; // weak
const unsigned __int16 Math::Convert::m_FastHeToBi[256] =
{
  12336u,
  12592u,
  12848u,
  13104u,
  13360u,
  13616u,
  13872u,
  14128u,
  14384u,
  14640u,
  16688u,
  16944u,
  17200u,
  17456u,
  17712u,
  17968u,
  12337u,
  12593u,
  12849u,
  13105u,
  13361u,
  13617u,
  13873u,
  14129u,
  14385u,
  14641u,
  16689u,
  16945u,
  17201u,
  17457u,
  17713u,
  17969u,
  12338u,
  12594u,
  12850u,
  13106u,
  13362u,
  13618u,
  13874u,
  14130u,
  14386u,
  14642u,
  16690u,
  16946u,
  17202u,
  17458u,
  17714u,
  17970u,
  12339u,
  12595u,
  12851u,
  13107u,
  13363u,
  13619u,
  13875u,
  14131u,
  14387u,
  14643u,
  16691u,
  16947u,
  17203u,
  17459u,
  17715u,
  17971u,
  12340u,
  12596u,
  12852u,
  13108u,
  13364u,
  13620u,
  13876u,
  14132u,
  14388u,
  14644u,
  16692u,
  16948u,
  17204u,
  17460u,
  17716u,
  17972u,
  12341u,
  12597u,
  12853u,
  13109u,
  13365u,
  13621u,
  13877u,
  14133u,
  14389u,
  14645u,
  16693u,
  16949u,
  17205u,
  17461u,
  17717u,
  17973u,
  12342u,
  12598u,
  12854u,
  13110u,
  13366u,
  13622u,
  13878u,
  14134u,
  14390u,
  14646u,
  16694u,
  16950u,
  17206u,
  17462u,
  17718u,
  17974u,
  12343u,
  12599u,
  12855u,
  13111u,
  13367u,
  13623u,
  13879u,
  14135u,
  14391u,
  14647u,
  16695u,
  16951u,
  17207u,
  17463u,
  17719u,
  17975u,
  12344u,
  12600u,
  12856u,
  13112u,
  13368u,
  13624u,
  13880u,
  14136u,
  14392u,
  14648u,
  16696u,
  16952u,
  17208u,
  17464u,
  17720u,
  17976u,
  12345u,
  12601u,
  12857u,
  13113u,
  13369u,
  13625u,
  13881u,
  14137u,
  14393u,
  14649u,
  16697u,
  16953u,
  17209u,
  17465u,
  17721u,
  17977u,
  12353u,
  12609u,
  12865u,
  13121u,
  13377u,
  13633u,
  13889u,
  14145u,
  14401u,
  14657u,
  16705u,
  16961u,
  17217u,
  17473u,
  17729u,
  17985u,
  12354u,
  12610u,
  12866u,
  13122u,
  13378u,
  13634u,
  13890u,
  14146u,
  14402u,
  14658u,
  16706u,
  16962u,
  17218u,
  17474u,
  17730u,
  17986u,
  12355u,
  12611u,
  12867u,
  13123u,
  13379u,
  13635u,
  13891u,
  14147u,
  14403u,
  14659u,
  16707u,
  16963u,
  17219u,
  17475u,
  17731u,
  17987u,
  12356u,
  12612u,
  12868u,
  13124u,
  13380u,
  13636u,
  13892u,
  14148u,
  14404u,
  14660u,
  16708u,
  16964u,
  17220u,
  17476u,
  17732u,
  17988u,
  12357u,
  12613u,
  12869u,
  13125u,
  13381u,
  13637u,
  13893u,
  14149u,
  14405u,
  14661u,
  16709u,
  16965u,
  17221u,
  17477u,
  17733u,
  17989u,
  12358u,
  12614u,
  12870u,
  13126u,
  13382u,
  13638u,
  13894u,
  14150u,
  14406u,
  14662u,
  16710u,
  16966u,
  17222u,
  17478u,
  17734u,
  17990u
}; // idb
void *Item::CDepositContainer::`vftable' = &Item::CDepositContainer::`scalar deleting destructor'; // weak
const char aDWorkRylSource_43[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_240[] = "CID:0x%08x "; // idb
const char aItemCdepositco_4[] = "Item::CDepositContainer::Update "; // idb
const char szExtraString[] = "Item::CDepositContainer::Update "; // idb
const char aCid0x08x_137[] = "CID:0x%08x "; // idb
const char aCid0x08x_221[] = "CID:0x%08x "; // idb
const char aCid0x08x_166[] = "CID:0x%08x "; // idb
const char aCid0x08x_218[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_23[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aDWorkRylSource_67[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
void *Item::CStallContainer::`vftable' = &Item::CStallContainer::`vector deleting destructor'; // weak
const char aCid0x08x_230[] = "CID:0x%08x "; // idb
const char aCid0x08x_136[] = "CID:0x%08x "; // idb
char aCid0x08x_191[11] = "CID:0x%08x "; // weak
const unsigned __int8 ms_aryMaxMemberNum[5] = { 10u, 20u, 30u, 40u, 50u }; // idb
const unsigned int ms_arySetLevelFame[5] = { 0u, 2000u, 10000u, 25000u, 50000u }; // idb
const unsigned int ms_arySetLevelGold[5] = { 1000000u, 5000000u, 10000000u, 50000000u, 100000000u }; // idb
const unsigned int ms_arySetLevelGoldForChina[5] = { 100000u, 5000000u, 10000000u, 50000000u, 100000000u }; // idb
const char aDWorkRylSource_47[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aGid0x08x_2[] = "GID:0x%08x "; // idb
const char aGid0x08x_15[] = "GID:0x%08x "; // idb
const char szGuildName = '\xB1'; // idb
const char aGid0x08x_24[] = "GID:0x%08x "; // idb
const char aGid0x08x_1[] = "GID:0x%08x "; // idb
const char aGid0x08x_9[] = "GID:0x%08x "; // idb
const char byte_4E4920 = '\xC1'; // idb
const char aCid0x08x_95[] = "CID:0x%08x "; // idb
char aCid0x08x_330[11] = "CID:0x%08x "; // weak
const char aCid0x08x_323[] = "CID:0x%08x "; // idb
const char aGid0x08x_16[] = "GID:0x%08x "; // idb
const char aGid0x08x_11[] = "GID:0x%08x "; // idb
const char aGid0x08x_26[] = "GID:0x%08x "; // idb
const char aGid0x08x_21[] = "GID:0x%08x "; // idb
const char aGid0x08x_19[] = "GID:0x%08x "; // idb
const char aGid0x08x_7[] = "GID:0x%08x "; // idb
char aGid0x08x_0[11] = "GID:0x%08x "; // weak
const char byte_4E4C48 = '\xB1'; // idb
const char aGid0x08x_6[] = "GID:0x%08x "; // idb
const char aGid0x08x_23[] = "GID:0x%08x "; // idb
const char aGid0x08x_25[] = "GID:0x%08x "; // idb
const char aGid0x08x_10[] = "GID:0x%08x "; // idb
const char aGid0x08x_3[] = "GID:0x%08x "; // idb
const char aGid0x08x_22[] = "GID:0x%08x "; // idb
const char aGid0x08x_14[] = "GID:0x%08x "; // idb
const char aCid0x08x_37[] = "CID:0x%08x "; // idb
const char aCid0x08x_28[] = "CID:0x%08x "; // idb
const char aCid0x08x_9[] = "CID:0x%08x "; // idb
const char aGid0x08x[] = "GID:0x%08x "; // idb
char aGid0x08x_12[11] = "GID:0x%08x "; // weak
const char aGid0x08x_20[] = "GID:0x%08x "; // idb
const char aCid0x08x_73[] = "CID:0x%08x "; // idb
const char aCid0x08x_247[] = "CID:0x%08x "; // idb
const char aCid0x08x_47[] = "CID:0x%08x "; // idb
const char aCid0x08x_223[] = "CID:0x%08x "; // idb
const char aCid0x08x_105[] = "CID:0x%08x "; // idb
const char aGid0x08x_5[] = "GID:0x%08x "; // idb
const char aGid0x08x_4[] = "GID:0x%08x "; // idb
void *Guild::CGuild::`vftable' = &Guild::CGuild::SetTitle; // weak
const char aGid0x08x_8[] = "GID:0x%08x "; // idb
const char aCid0x08x_309[] = "CID:0x%08x "; // idb
const char aGid0x08x_18[] = "GID:0x%08x "; // idb
const char aDWorkRylSource_15[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_203[] = "CID:0x%08x "; // idb
const char aCid0x08x_56[] = "CID:0x%08x "; // idb
const char aCid0x08x_256[] = "CID:0x%08x "; // idb
void *Item::CEquipmentsContainer::`vftable' = &Item::CEquipmentsContainer::`scalar deleting destructor'; // weak
const char aDWorkRylSource_97[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4E5690 = '\xC0'; // idb
const char byte_4E56B0 = '\xBE'; // idb
const char aD_3[] = "%d"; // idb
char byte_4E5770[88] =
{
  '\xC3',
  '\xB9',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xB9',
  '\xBA',
  '\xB0',
  '\xA1',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xBE',
  '\xE7',
  '\xBC',
  '\xD5',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4E57C8[104] =
{
  '\xC3',
  '\xB9',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '(',
  '\xBC',
  '\xAE',
  '\xB1',
  '\xC3',
  ')',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xBA',
  '\xBC',
  '\xC6',
  '\xAE',
  '\xB0',
  '\xA1',
  ' ',
  '\xBE',
  '\xC6',
  '\xB4',
  '\xD1',
  ' ',
  '\xB9',
  '\xAB',
  '\xBE',
  '\xF0',
  '\xB0',
  '\xA1',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xBC',
  '\xAE',
  '\xB1',
  '\xC3',
  '\xC0',
  '\xBB',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4E5830[104] =
{
  '\xB5',
  '\xCE',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '(',
  '\xBC',
  '\xAE',
  '\xB1',
  '\xC3',
  ')',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xBA',
  '\xBC',
  '\xC6',
  '\xAE',
  '\xB0',
  '\xA1',
  ' ',
  '\xBE',
  '\xC6',
  '\xB4',
  '\xD1',
  ' ',
  '\xB9',
  '\xAB',
  '\xBE',
  '\xF0',
  '\xB0',
  '\xA1',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xBC',
  '\xAE',
  '\xB1',
  '\xC3',
  '\xC0',
  '\xBB',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4E5898[96] =
{
  '\xC3',
  '\xB9',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '(',
  '\xC8',
  '\xB0',
  ')',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xC8',
  '\xAD',
  '\xBB',
  '\xEC',
  '\xC0',
  '\xCC',
  ' ',
  '\xBE',
  '\xC6',
  '\xB4',
  '\xD1',
  ' ',
  '\xB9',
  '\xAB',
  '\xBE',
  '\xF0',
  '\xB0',
  '\xA1',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xC8',
  '\xB0',
  '\xC0',
  '\xBB',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0'
}; // weak
char byte_4E58F8[96] =
{
  '\xB5',
  '\xCE',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '(',
  '\xC8',
  '\xB0',
  ')',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xC8',
  '\xAD',
  '\xBB',
  '\xEC',
  '\xC0',
  '\xCC',
  ' ',
  '\xBE',
  '\xC6',
  '\xB4',
  '\xD1',
  ' ',
  '\xB9',
  '\xAB',
  '\xBE',
  '\xF0',
  '\xB0',
  '\xA1',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xC8',
  '\xB0',
  '\xC0',
  '\xBB',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0'
}; // weak
char byte_4E5958[88] =
{
  '\xC3',
  '\xB9',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xB9',
  '\xE6',
  '\xC6',
  '\xD0',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xBE',
  '\xE7',
  '\xBC',
  '\xD5',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4E59B0[88] =
{
  '\xB5',
  '\xCE',
  '\xB9',
  '\xF8',
  '\xC2',
  '\xB0',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  ':',
  ' ',
  '\xB9',
  '\xDD',
  '\xB4',
  '\xEB',
  '\xC6',
  '\xED',
  ' ',
  '\xBC',
  '\xD5',
  '\xBF',
  '\xA1',
  ' ',
  '\xB9',
  '\xE6',
  '\xC6',
  '\xD0',
  '\xB8',
  '\xA6',
  ' ',
  '\xB5',
  '\xE9',
  '\xB0',
  '\xED',
  ' ',
  '\xC0',
  '\xD6',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xBE',
  '\xE7',
  '\xBC',
  '\xD5',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  '\xB8',
  '\xA6',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  '\xC7',
  '\xD2',
  ' ',
  '\xBC',
  '\xF6',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4E5A08[28] =
{
  '\xC0',
  '\xCE',
  '\xB0',
  '\xA3',
  '\xBF',
  '\xEB',
  ' ',
  '\xC7',
  '\xD1',
  '\xBC',
  '\xD5',
  ' ',
  '\xB9',
  '\xAB',
  '\xB1',
  '\xE2',
  ' ',
  '\xC0',
  '\xE5',
  '\xC2',
  '\xF8',
  ' ',
  '\xBD',
  '\xC7',
  '\xC6',
  '\xD0',
  '.',
  '\0'
}; // weak
const char aCid0x08x_333[] = "CID:0x%08x "; // idb
const char byte_4E5A9C = '\xC0'; // idb
const char aCid0x08x_195[] = "CID:0x%08x "; // idb
const char aCid0x08x_318[] = "CID:0x%08x "; // idb
const char byte_4E5BA0 = '\xC0'; // idb
void *Item::CExchangeContainer::`vftable' = &Item::CExchangeContainer::`vector deleting destructor'; // weak
const char aDWorkRylSource_26[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_280[] = "CID:0x%08x "; // idb
const char aCid0x08x_13[] = "CID:0x%08x "; // idb
const char aCid0x08x_80[] = "CID:0x%08x "; // idb
const char aCid0x08x_291[] = "CID:0x%08x "; // idb
const char aCid0x08x_7[] = "CID:0x%08x "; // idb
char aCid0x08x_2[11] = "CID:0x%08x "; // weak
const char aCid0x08x_293[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_81[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x0xP[] = "CID:0x%08x(0x%p) "; // idb
_UNKNOWN unk_4E600C; // weak
_UNKNOWN unk_4E6014; // weak
_UNKNOWN unk_4E602C; // weak
const char aCid0x08x_294[] = "CID:0x%08x "; // idb
const char aCid0x08x_250[] = "CID:0x%08x "; // idb
const char aCid0x08x_34[] = "CID:0x%08x "; // idb
const char aCid0x08x_296[] = "CID:0x%08x "; // idb
const char aCid0x08x_252[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_4[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aVid0xXVirtuala[] = "VID : 0x%x VirtualAreaProtoType "; // idb
const char aVirtualareaSta[] = "VirtualArea StartPos "; // idb
const char aVirtualareaRes[] = "VirtualArea RespawnPos "; // idb
_UNKNOWN unk_4E6520; // weak
void *VirtualArea::CVirtualArea::`vftable' = &CSymbolTable::Create; // weak
const char aCid0x08x_54[] = "CID:0x%08x "; // idb
const char aCid0x08x_175[] = "CID:0x%08x "; // idb
char aCid0x08x_335[11] = "CID:0x%08x "; // weak
const char aDWorkRylSource_74[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_290[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_60[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_327[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_83[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_170[] = "CID:0x%08x "; // idb
const char aUidDCid0x08x_1[] = "UID:%d/CID:0x%08x/ "; // idb
const char aUidDCid0x08x0x_6[] = "UID:%d/CID:0x%08x(0x%p)/RequestKey:%d/DispatchPointer:0x%p "; // idb
const POS RespawnPos[2][2] =
{
  { { 2165.0, 1135.0, 1005.0 }, { 1727.0, 1135.0, 1005.0 } },
  { { 2119.0, 1132.0, 1841.0 }, { 1683.0, 1132.0, 1841.0 } }
}; // idb
const char aDWorkRylSource_104[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_118[] = "CID:0x%08x "; // idb
const char aBattleServerLo[] = "Battle Server Log :: (Channel : %d, %s) - "; // idb
const char aBattleServerLo_5[] = "Battle Server Log :: (Channel : %d, %s) - CID : 0x%08x "; // idb
const char aBattleServerLo_0[] = "Battle Server Log :: (Channel : %d, %s) - CID : 0x%08x "; // idb
const char aBattleServerLo_3[] = "Battle Server Log :: (Channel : %d, %s) - CID : 0x%08x "; // idb
void *VirtualArea::CBGServerMap::`vftable' = &VirtualArea::CBGServerMap::Process; // weak
const char byte_4E6F88 = '\xB8'; // idb
const char byte_4E6FB4 = '\xB0'; // idb
char aPc[4] = "\"PC "; // weak
char asc_4E6FF8 = '\"'; // weak
char aHp_0[4] = "\"HP "; // weak
char asc_4E7018 = '\"'; // weak
char asc_4E7028 = '\"'; // weak
char asc_4E7038 = '\"'; // weak
char asc_4E7050 = '\"'; // weak
char asc_4E7068 = '\"'; // weak
char asc_4E707C = '\"'; // weak
char asc_4E7090 = '\"'; // weak
char asc_4E70A4 = '\"'; // weak
char asc_4E70B8 = '\"'; // weak
char asc_4E70C4 = '\"'; // weak
char asc_4E70D0 = '\"'; // weak
char asc_4E70E0 = '\"'; // weak
char asc_4E70F4 = '\"'; // weak
char asc_4E7104 = '\"'; // weak
char asc_4E7118 = '\"'; // weak
char asc_4E712C = '\"'; // weak
char asc_4E7140 = '\"'; // weak
char asc_4E7150 = '\"'; // weak
char asc_4E7160 = '\"'; // weak
char asc_4E7170 = '\"'; // weak
char asc_4E7188 = '\"'; // weak
char asc_4E7194 = '\"'; // weak
char asc_4E71A0 = '\"'; // weak
char asc_4E71B0 = '\"'; // weak
char asc_4E71C4 = '\"'; // weak
char asc_4E71D4 = '\"'; // weak
char asc_4E71E0 = '\"'; // weak
char aMp[4] = "\"MP "; // weak
char aHp[4] = "\"HP "; // weak
char asc_4E721C = '\"'; // weak
char asc_4E7228 = '\"'; // weak
char asc_4E7234 = '\"'; // weak
char asc_4E7240 = '\"'; // weak
char asc_4E724C = '\"'; // weak
char asc_4E7258 = '\"'; // weak
char asc_4E7264 = '\"'; // weak
char asc_4E7274 = '\"'; // weak
char asc_4E7284 = '\"'; // weak
char asc_4E7290 = '\"'; // weak
char asc_4E729C = '\"'; // weak
char asc_4E72A8 = '\"'; // weak
char asc_4E72B4 = '\"'; // weak
char asc_4E72C4 = '\"'; // weak
char asc_4E72D4 = '\"'; // weak
char asc_4E72DC = '\"'; // weak
char asc_4E72EC = '\"'; // weak
char asc_4E72F8 = '\"'; // weak
char asc_4E7304 = '\"'; // weak
char asc_4E7310 = '\"'; // weak
char asc_4E7320 = '\"'; // weak
char asc_4E7330 = '\"'; // weak
char asc_4E7340 = '\"'; // weak
char asc_4E7350 = '\"'; // weak
char asc_4E735C = '\"'; // weak
char asc_4E736C = '\"'; // weak
char asc_4E7380 = '\"'; // weak
char asc_4E7398 = '\"'; // weak
char asc_4E73B0 = '\"'; // weak
const char byte_4E73B8 = '\xB8'; // idb
const char aDWorkRylSource_79[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const POS StartPosNum[2][6] =
{
  {
    { 2740.0, 23.0, 606.0 },
    { 2738.0, 24.0, 647.0 },
    { 2801.0, 22.0, 644.0 },
    { 1442.0, 12.0, 1973.0 },
    { 1464.0, 12.0, 2064.0 },
    { 1419.0, 12.0, 2065.0 }
  },
  {
    { 2446.0, 33.0, 3208.0 },
    { 2370.0, 32.0, 3233.0 },
    { 2324.0, 32.0, 3244.0 },
    { 2059.0, 65.0, 1663.0 },
    { 2014.0, 65.0, 1648.0 },
    { 2009.0, 65.0, 1690.0 }
  }
}; // idb
const POS BGServerStartPos[2][2] =
{
  { { 2165.0, 1135.0, 1005.0 }, { 1727.0, 1135.0, 1005.0 } },
  { { 2119.0, 1132.0, 1841.0 }, { 1683.0, 1132.0, 1841.0 } }
}; // idb
const unsigned int StartPointVariation[6] = { 500u, 1000u, 1500u, 2000u, 2500u, 4294967295u }; // idb
const char aDWorkRylSource_102[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_129[] = "CID:0x%08x "; // idb
const __int16 aryGemDropTable[10][3] =
{
  { 1, 0, 0 },
  { 2, 0, 0 },
  { 3, 1, 0 },
  { 4, 2, 0 },
  { 5, 3, 1 },
  { 6, 4, 2 },
  { 7, 5, 3 },
  { 8, 6, 4 },
  { 9, 7, 5 },
  { 10, 8, 6 }
}; // idb
const __int16 aryMetalDropTable[10][3] =
{
  { 1, 0, 0 },
  { 2, 0, 0 },
  { 3, 1, 0 },
  { 4, 2, 0 },
  { 5, 3, 1 },
  { 6, 4, 2 },
  { 7, 5, 3 },
  { 8, 6, 4 },
  { 9, 7, 5 },
  { 10, 8, 6 }
}; // idb
const __int16 aryPotionDropTable[10][3] =
{
  { 1, 1, 1 },
  { 2, 2, 2 },
  { 3, 3, 3 },
  { 4, 4, 4 },
  { 5, 5, 5 },
  { 6, 6, 6 },
  { 7, 7, 7 },
  { 8, 8, 8 },
  { 9, 9, 9 },
  { 10, 10, 10 }
}; // idb
const __int16 arySkillBookDropTable[10] = { 1, 2, 2, 3, 3, 4, 4, 4, 4, 4 }; // idb
const unsigned __int16 aryDropableSkillbookList[69] =
{
  33026u,
  33027u,
  33028u,
  33029u,
  33285u,
  33538u,
  33539u,
  33540u,
  33541u,
  33796u,
  33797u,
  34051u,
  34052u,
  34306u,
  34307u,
  34562u,
  34564u,
  34565u,
  34819u,
  34820u,
  34821u,
  35074u,
  35075u,
  35076u,
  35077u,
  35330u,
  35332u,
  35333u,
  35334u,
  35586u,
  35587u,
  35588u,
  35590u,
  35842u,
  35843u,
  35844u,
  37122u,
  37123u,
  37124u,
  37125u,
  37126u,
  37378u,
  37380u,
  37382u,
  37383u,
  37384u,
  37385u,
  37392u,
  37634u,
  37637u,
  37639u,
  37890u,
  37892u,
  37895u,
  38146u,
  38148u,
  38150u,
  38402u,
  38403u,
  38404u,
  38405u,
  38406u,
  38407u,
  38659u,
  38660u,
  38662u,
  38914u,
  38916u,
  38917u
}; // idb
_UNKNOWN unk_4E789A; // weak
const int aryBlackMarketItemList[228][13] =
{
  { 101, 101, 101, 101, 101, 101, 102, 102, 102, 102, 121, 121, 122 },
  { 103, 103, 103, 103, 103, 103, 104, 104, 104, 104, 123, 123, 124 },
  { 105, 105, 105, 105, 105, 105, 125, 125, 125, 125, 106, 106, 126 },
  { 108, 108, 108, 108, 108, 108, 128, 128, 128, 128, 160, 160, 109 },
  { 110, 110, 110, 110, 110, 110, 110, 110, 110, 130, 130, 130, 130 },
  { 111, 111, 111, 111, 111, 111, 111, 131, 131, 131, 131, 113, 113 },
  { 112, 112, 112, 112, 112, 112, 112, 112, 112, 132, 132, 132, 132 },
  { 115, 115, 115, 115, 115, 115, 115, 115, 135, 135, 135, 135, 135 },
  { 116, 116, 116, 116, 116, 116, 136, 136, 136, 117, 117, 117, 137 },
  { 118, 118, 118, 118, 118, 118, 118, 138, 138, 138, 138, 119, 119 },
  { 119, 119, 119, 119, 119, 119, 119, 119, 119, 139, 139, 139, 139 },
  { 133, 133, 133, 133, 133, 133, 133, 114, 114, 114, 114, 134, 134 },
  { 140, 140, 140, 140, 140, 140, 140, 140, 140, 140, 140, 140, 140 },
  { 141, 141, 141, 141, 141, 141, 141, 141, 143, 143, 143, 162, 162 },
  { 142, 142, 142, 142, 142, 142, 142, 142, 142, 129, 129, 129, 129 },
  { 146, 146, 146, 146, 146, 146, 145, 145, 145, 148, 148, 148, 161 },
  { 147, 147, 147, 147, 147, 147, 152, 152, 152, 153, 153, 153, 154 },
  { 149, 155, 155, 155, 155, 155, 155, 149, 149, 149, 149, 150, 150 },
  { 151, 151, 151, 151, 151, 151, 144, 144, 144, 144, 107, 107, 127 },
  { 156, 159, 159, 159, 159, 159, 159, 159, 156, 156, 156, 156, 156 },
  { 157, 157, 157, 157, 157, 157, 157, 157, 157, 158, 158, 158, 158 },
  { 163, 163, 163, 163, 163, 163, 163, 163, 163, 164, 164, 164, 164 },
  { 166, 166, 166, 166, 166, 166, 166, 166, 166, 165, 165, 165, 165 },
  { 402, 402, 402, 402, 402, 402, 402, 402, 434, 434, 434, 401, 401 },
  { 404, 404, 404, 404, 404, 404, 404, 404, 404, 420, 420, 420, 420 },
  { 405, 405, 405, 405, 405, 405, 405, 421, 421, 421, 421, 406, 406 },
  { 408, 408, 408, 408, 408, 408, 408, 438, 438, 438, 438, 424, 424 },
  { 409, 409, 409, 409, 409, 409, 409, 439, 439, 439, 439, 425, 425 },
  { 410, 410, 410, 410, 410, 410, 410, 440, 440, 440, 440, 426, 426 },
  { 411, 411, 411, 411, 411, 411, 411, 441, 441, 441, 441, 427, 427 },
  { 412, 412, 412, 412, 412, 412, 428, 428, 428, 428, 471, 471, 471 },
  { 413, 413, 413, 413, 413, 413, 413, 442, 442, 442, 442, 429, 429 },
  { 414, 414, 414, 414, 414, 414, 414, 443, 443, 443, 443, 430, 430 },
  { 415, 415, 415, 415, 415, 415, 415, 444, 444, 444, 444, 431, 431 },
  { 416, 416, 416, 416, 416, 416, 416, 445, 445, 445, 445, 432, 432 },
  { 417, 417, 417, 417, 417, 417, 433, 433, 433, 433, 466, 466, 466 },
  { 419, 436, 436, 436, 436, 436, 436, 419, 419, 419, 419, 437, 437 },
  { 422, 422, 422, 422, 422, 422, 422, 407, 407, 407, 407, 423, 423 },
  { 435, 435, 435, 435, 435, 435, 418, 418, 418, 418, 403, 403, 403 },
  { 446, 446, 446, 446, 446, 446, 446, 446, 446, 447, 447, 447, 447 },
  { 448, 448, 448, 448, 448, 448, 448, 449, 449, 449, 449, 450, 450 },
  { 451, 451, 451, 451, 451, 451, 451, 454, 454, 454, 454, 455, 455 },
  { 452, 452, 452, 452, 452, 452, 453, 453, 453, 456, 456, 457, 457 },
  { 458, 458, 458, 458, 458, 458, 458, 458, 459, 459, 459, 459, 459 },
  { 460, 460, 460, 460, 460, 460, 460, 460, 461, 461, 461, 461, 461 },
  { 462, 462, 462, 462, 462, 462, 462, 462, 463, 463, 463, 463, 463 },
  { 464, 464, 464, 464, 464, 464, 464, 464, 465, 465, 465, 465, 465 },
  { 467, 467, 467, 467, 467, 467, 467, 467, 468, 468, 468, 468, 468 },
  { 469, 469, 469, 469, 469, 469, 469, 469, 470, 470, 470, 470, 470 },
  { 472, 472, 472, 472, 472, 472, 472, 472, 473, 473, 473, 473, 473 },
  { 474, 474, 474, 474, 474, 474, 474, 474, 475, 475, 475, 475, 475 },
  { 701, 701, 701, 701, 701, 711, 711, 711, 702, 702, 712, 712, 721 },
  { 703, 703, 703, 703, 703, 703, 713, 713, 713, 713, 704, 704, 722 },
  { 705, 714, 714, 714, 714, 714, 714, 705, 705, 705, 715, 715, 723 },
  { 706, 706, 706, 706, 706, 706, 706, 706, 716, 716, 716, 716, 724 },
  { 707, 707, 707, 707, 707, 707, 707, 707, 717, 717, 717, 717, 725 },
  { 708, 708, 708, 708, 708, 708, 708, 708, 708, 718, 718, 718, 718 },
  { 709, 709, 709, 709, 709, 709, 709, 709, 709, 719, 719, 719, 726 },
  { 710, 710, 710, 710, 710, 710, 710, 710, 710, 710, 720, 720, 720 },
  { 727, 727, 727, 727, 727, 728, 728, 728, 728, 729, 729, 729, 729 },
  { 731, 730, 730, 730, 730, 730, 730, 730, 731, 731, 731, 731, 731 },
  { 801, 801, 801, 801, 801, 801, 801, 810, 810, 810, 810, 819, 819 },
  { 802, 802, 802, 802, 802, 802, 802, 811, 811, 811, 811, 820, 820 },
  { 804, 804, 804, 804, 804, 804, 804, 813, 813, 813, 813, 822, 822 },
  { 805, 803, 803, 803, 803, 803, 812, 812, 812, 805, 805, 805, 814 },
  { 806, 806, 806, 806, 806, 806, 806, 815, 815, 815, 815, 823, 823 },
  { 807, 807, 807, 807, 807, 807, 807, 816, 816, 816, 816, 824, 824 },
  { 808, 808, 808, 808, 808, 808, 808, 817, 817, 817, 817, 825, 825 },
  { 809, 809, 809, 809, 809, 809, 809, 818, 818, 818, 818, 826, 826 },
  { 827, 827, 827, 827, 827, 827, 827, 828, 828, 828, 828, 829, 829 },
  { 830, 830, 830, 830, 830, 830, 830, 830, 830, 830, 830, 830, 830 },
  { 901, 901, 901, 901, 901, 911, 911, 911, 902, 902, 912, 912, 921 },
  { 903, 903, 903, 903, 903, 913, 913, 913, 914, 914, 922, 922, 923 },
  { 905, 905, 905, 905, 905, 905, 905, 915, 915, 915, 915, 924, 924 },
  { 906, 906, 906, 906, 906, 906, 906, 916, 916, 916, 916, 925, 925 },
  { 907, 907, 907, 907, 907, 907, 907, 917, 917, 917, 917, 926, 926 },
  { 908, 908, 908, 908, 908, 908, 908, 918, 918, 918, 918, 927, 927 },
  { 909, 909, 909, 909, 909, 909, 909, 919, 919, 919, 919, 919, 919 },
  { 910, 910, 910, 910, 910, 910, 910, 920, 920, 920, 920, 920, 920 },
  { 928, 928, 928, 928, 928, 928, 928, 929, 929, 929, 929, 930, 930 },
  { 931, 931, 931, 931, 931, 931, 931, 931, 931, 931, 931, 931, 931 },
  {
    1001,
    1001,
    1001,
    1001,
    1001,
    1001,
    1001,
    1001,
    1001,
    1007,
    1007,
    1007,
    1007
  },
  {
    1002,
    1002,
    1002,
    1002,
    1002,
    1002,
    1002,
    1008,
    1008,
    1008,
    1008,
    1013,
    1013
  },
  {
    1003,
    1003,
    1003,
    1003,
    1003,
    1003,
    1003,
    1009,
    1009,
    1009,
    1009,
    1014,
    1014
  },
  {
    1004,
    1004,
    1004,
    1004,
    1004,
    1004,
    1004,
    1004,
    1004,
    1010,
    1010,
    1010,
    1010
  },
  {
    1005,
    1005,
    1005,
    1005,
    1005,
    1005,
    1005,
    1005,
    1011,
    1011,
    1011,
    1011,
    1011
  },
  {
    1006,
    1006,
    1006,
    1006,
    1006,
    1006,
    1006,
    1012,
    1012,
    1012,
    1012,
    1015,
    1015
  },
  {
    1016,
    1016,
    1016,
    1016,
    1016,
    1016,
    1016,
    1016,
    1017,
    1017,
    1017,
    1017,
    1017
  },
  {
    1101,
    1101,
    1101,
    1101,
    1101,
    1101,
    1101,
    1106,
    1106,
    1106,
    1106,
    1111,
    1111
  },
  {
    1102,
    1102,
    1102,
    1102,
    1102,
    1102,
    1102,
    1107,
    1107,
    1107,
    1107,
    1107,
    1107
  },
  {
    1103,
    1103,
    1103,
    1103,
    1103,
    1103,
    1103,
    1108,
    1108,
    1108,
    1108,
    1112,
    1112
  },
  {
    1104,
    1104,
    1104,
    1104,
    1104,
    1104,
    1104,
    1109,
    1109,
    1109,
    1109,
    1113,
    1113
  },
  {
    1110,
    1105,
    1105,
    1105,
    1105,
    1105,
    1110,
    1110,
    1110,
    1110,
    1114,
    1114,
    1114
  },
  {
    1115,
    1115,
    1115,
    1115,
    1115,
    1115,
    1116,
    1116,
    1116,
    1116,
    1117,
    1117,
    1117
  },
  {
    1201,
    1201,
    1201,
    1201,
    1201,
    1201,
    1205,
    1205,
    1205,
    1205,
    1209,
    1209,
    1212
  },
  {
    1202,
    1202,
    1202,
    1202,
    1202,
    1202,
    1206,
    1206,
    1206,
    1206,
    1210,
    1210,
    1213
  },
  {
    1203,
    1203,
    1203,
    1203,
    1203,
    1203,
    1203,
    1203,
    1207,
    1207,
    1207,
    1207,
    1211
  },
  {
    1204,
    1204,
    1204,
    1204,
    1204,
    1204,
    1208,
    1208,
    1208,
    1208,
    1214,
    1214,
    1214
  },
  {
    1215,
    1215,
    1215,
    1215,
    1215,
    1215,
    1216,
    1216,
    1216,
    1216,
    1217,
    1217,
    1217
  },
  {
    1301,
    1301,
    1301,
    1301,
    1301,
    1301,
    1301,
    1306,
    1306,
    1306,
    1306,
    1306,
    1311
  },
  {
    1302,
    1302,
    1302,
    1302,
    1302,
    1302,
    1302,
    1307,
    1307,
    1307,
    1307,
    1307,
    1312
  },
  {
    1303,
    1303,
    1303,
    1303,
    1303,
    1303,
    1303,
    1308,
    1308,
    1308,
    1308,
    1308,
    1313
  },
  {
    1304,
    1304,
    1304,
    1304,
    1304,
    1304,
    1304,
    1309,
    1309,
    1309,
    1309,
    1309,
    1314
  },
  {
    1305,
    1305,
    1305,
    1305,
    1305,
    1305,
    1310,
    1310,
    1310,
    1310,
    1315,
    1315,
    1315
  },
  {
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316,
    1316
  },
  {
    1401,
    1401,
    1401,
    1401,
    1401,
    1401,
    1401,
    1406,
    1406,
    1406,
    1406,
    1406,
    1411
  },
  {
    1402,
    1402,
    1402,
    1402,
    1402,
    1402,
    1402,
    1407,
    1407,
    1407,
    1407,
    1407,
    1412
  },
  {
    1403,
    1403,
    1403,
    1403,
    1403,
    1403,
    1403,
    1408,
    1408,
    1408,
    1408,
    1408,
    1413
  },
  {
    1404,
    1404,
    1404,
    1404,
    1404,
    1404,
    1404,
    1409,
    1409,
    1409,
    1409,
    1409,
    1414
  },
  {
    1405,
    1405,
    1405,
    1405,
    1405,
    1405,
    1405,
    1410,
    1410,
    1410,
    1410,
    1410,
    1415
  },
  {
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416,
    1416
  },
  {
    1501,
    1501,
    1501,
    1501,
    1501,
    1501,
    1501,
    1502,
    1502,
    1502,
    1502,
    1502,
    1507
  },
  {
    1503,
    1503,
    1503,
    1503,
    1503,
    1503,
    1503,
    1508,
    1508,
    1508,
    1508,
    1508,
    1512
  },
  {
    1504,
    1504,
    1504,
    1504,
    1504,
    1504,
    1504,
    1509,
    1509,
    1509,
    1509,
    1509,
    1513
  },
  {
    1505,
    1505,
    1505,
    1505,
    1505,
    1505,
    1505,
    1510,
    1510,
    1510,
    1510,
    1510,
    1514
  },
  {
    1506,
    1506,
    1506,
    1506,
    1506,
    1506,
    1506,
    1511,
    1511,
    1511,
    1515,
    1515,
    1516
  },
  {
    1517,
    1517,
    1517,
    1517,
    1517,
    1517,
    1517,
    1517,
    1518,
    1518,
    1518,
    1518,
    1518
  },
  {
    1601,
    1601,
    1601,
    1601,
    1601,
    1601,
    1601,
    1601,
    1611,
    1611,
    1611,
    1611,
    1606
  },
  {
    1602,
    1602,
    1602,
    1602,
    1602,
    1602,
    1602,
    1602,
    1615,
    1615,
    1615,
    1615,
    1607
  },
  {
    1603,
    1603,
    1603,
    1603,
    1603,
    1603,
    1603,
    1603,
    1619,
    1619,
    1619,
    1619,
    1608
  },
  {
    1604,
    1604,
    1604,
    1604,
    1604,
    1604,
    1604,
    1604,
    1623,
    1623,
    1623,
    1623,
    1609
  },
  {
    1605,
    1605,
    1605,
    1605,
    1605,
    1605,
    1605,
    1605,
    1627,
    1627,
    1627,
    1627,
    1610
  },
  {
    1612,
    1612,
    1612,
    1612,
    1612,
    1612,
    1612,
    1612,
    1613,
    1613,
    1613,
    1613,
    1614
  },
  {
    1616,
    1616,
    1616,
    1616,
    1616,
    1616,
    1616,
    1616,
    1617,
    1617,
    1617,
    1617,
    1618
  },
  {
    1620,
    1620,
    1620,
    1620,
    1620,
    1620,
    1620,
    1620,
    1621,
    1621,
    1621,
    1621,
    1622
  },
  {
    1624,
    1624,
    1624,
    1624,
    1624,
    1624,
    1624,
    1624,
    1625,
    1625,
    1625,
    1625,
    1626
  },
  {
    1628,
    1628,
    1628,
    1628,
    1628,
    1628,
    1629,
    1629,
    1629,
    1629,
    1630,
    1630,
    1630
  },
  {
    1631,
    1631,
    1631,
    1631,
    1631,
    1631,
    1631,
    1631,
    1632,
    1632,
    1632,
    1632,
    1632
  },
  {
    1701,
    1701,
    1701,
    1701,
    1701,
    1702,
    1702,
    1702,
    1702,
    1703,
    1703,
    1703,
    1704
  },
  {
    1705,
    1705,
    1705,
    1705,
    1705,
    1705,
    1705,
    1705,
    1706,
    1706,
    1706,
    1706,
    1706
  },
  {
    1707,
    1707,
    1707,
    1707,
    1707,
    1707,
    1707,
    1707,
    1708,
    1708,
    1708,
    1708,
    1708
  },
  {
    1709,
    1709,
    1709,
    1709,
    1709,
    1709,
    1709,
    1709,
    1710,
    1710,
    1710,
    1710,
    1710
  },
  {
    1711,
    1711,
    1711,
    1711,
    1711,
    1711,
    1711,
    1712,
    1712,
    1712,
    1712,
    1713,
    1713
  },
  {
    1714,
    1714,
    1714,
    1714,
    1714,
    1714,
    1714,
    1715,
    1715,
    1715,
    1715,
    1716,
    1716
  },
  {
    5001,
    5001,
    5001,
    5001,
    5001,
    5001,
    5002,
    5002,
    5002,
    5003,
    5003,
    5003,
    5004
  },
  {
    5005,
    5005,
    5005,
    5005,
    5005,
    5005,
    5006,
    5006,
    5006,
    5007,
    5007,
    5007,
    5008
  },

  {
    5009,
    5009,
    5009,
    5009,
    5009,
    5009,
    5010,
    5010,
    5010,
    5011,
    5011,
    5011,
    5012
  },
  {
    5013,
    5013,
    5013,
    5013,
    5013,
    5013,
    5014,
    5014,
    5014,
    5015,
    5015,
    5015,
    5016
  },
  {
    5018,
    5018,
    5018,
    5018,
    5018,
    5018,
    5019,
    5019,
    5019,
    5020,
    5020,
    5020,
    5021
  },
  {
    5022,
    5022,
    5022,
    5022,
    5022,
    5022,
    5023,
    5023,
    5023,
    5024,
    5024,
    5024,
    5025
  },
  {
    5026,
    5026,
    5026,
    5026,
    5026,
    5026,
    5027,
    5027,
    5027,
    5028,
    5028,
    5028,
    5029
  },
  {
    5030,
    5030,
    5030,
    5030,
    5030,
    5030,
    5031,
    5031,
    5031,
    5032,
    5032,
    5032,
    5033
  },
  {
    5035,
    5035,
    5035,
    5035,
    5035,
    5036,
    5036,
    5036,
    5037,
    5037,
    5037,
    5038,
    5038
  },
  {
    5039,
    5039,
    5039,
    5039,
    5039,
    5040,
    5040,
    5040,
    5041,
    5041,
    5041,
    5042,
    5042
  },
  {
    5043,
    5043,
    5043,
    5043,
    5043,
    5044,
    5044,
    5044,
    5045,
    5045,
    5045,
    5046,
    5046
  },
  {
    5047,
    5047,
    5047,
    5047,
    5047,
    5048,
    5048,
    5048,
    5049,
    5049,
    5049,
    5050,
    5050
  },
  {
    5101,
    5101,
    5101,
    5101,
    5101,
    5101,
    5101,
    5102,
    5102,
    5102,
    5102,
    5103,
    5103
  },
  {
    5104,
    5104,
    5104,
    5104,
    5104,
    5104,
    5104,
    5105,
    5105,
    5105,
    5105,
    5106,
    5106
  },
  {
    5107,
    5107,
    5107,
    5107,
    5107,
    5107,
    5107,
    5108,
    5108,
    5108,
    5108,
    5109,
    5109
  },
  {
    5110,
    5110,
    5110,
    5110,
    5110,
    5110,
    5110,
    5111,
    5111,
    5111,
    5111,
    5112,
    5112
  },
  {
    5113,
    5113,
    5113,
    5113,
    5113,
    5113,
    5113,
    5114,
    5114,
    5114,
    5114,
    5115,
    5115
  },
  {
    5116,
    5116,
    5116,
    5116,
    5116,
    5116,
    5116,
    5117,
    5117,
    5117,
    5117,
    5118,
    5118
  },
  {
    5119,
    5119,
    5119,
    5119,
    5119,
    5120,
    5120,
    5120,
    5121,
    5121,
    5121,
    5122,
    5122
  },
  {
    5123,
    5123,
    5123,
    5123,
    5123,
    5124,
    5124,
    5124,
    5125,
    5125,
    5125,
    5126,
    5126
  },
  {
    5127,
    5127,
    5127,
    5127,
    5127,
    5128,
    5128,
    5128,
    5129,
    5129,
    5129,
    5130,
    5130
  },
  {
    5131,
    5131,
    5131,
    5131,
    5131,
    5132,
    5132,
    5132,
    5133,
    5133,
    5133,
    5134,
    5134
  },
  {
    5135,
    5135,
    5135,
    5135,
    5135,
    5135,
    5135,
    5135,
    5136,
    5136,
    5136,
    5136,
    5136
  },
  {
    5137,
    5137,
    5137,
    5137,
    5137,
    5137,
    5137,
    5137,
    5138,
    5138,
    5138,
    5138,
    5138
  },
  {
    5140,
    5140,
    5140,
    5140,
    5140,
    5140,
    5140,
    5140,
    5141,
    5141,
    5141,
    5141,
    5141
  },
  {
    5142,
    5142,
    5142,
    5142,
    5142,
    5142,
    5142,
    5142,
    5143,
    5143,
    5143,
    5143,
    5143
  },
  {
    5401,
    5401,
    5401,
    5401,
    5401,
    5401,
    5401,
    5401,
    5402,
    5402,
    5402,
    5403,
    5403
  },
  {
    5404,
    5404,
    5404,
    5404,
    5404,
    5404,
    5404,
    5404,
    5405,
    5405,
    5405,
    5406,
    5406
  },
  {
    5407,
    5407,
    5407,
    5407,
    5407,
    5407,
    5407,
    5407,
    5408,
    5408,
    5408,
    5409,
    5409
  },
  {
    5410,
    5410,
    5410,
    5410,
    5410,
    5410,
    5410,
    5410,
    5411,
    5411,
    5411,
    5412,
    5412
  },
  {
    5413,
    5413,
    5413,
    5413,
    5413,
    5413,
    5413,
    5413,
    5414,
    5414,
    5414,
    5415,
    5415
  },
  {
    5416,
    5416,
    5416,
    5416,
    5416,
    5416,
    5416,
    5416,
    5417,
    5417,
    5417,
    5418,
    5418
  },
  {
    5419,
    5419,
    5419,
    5419,
    5419,
    5419,
    5419,
    5419,
    5420,
    5420,
    5420,
    5421,
    5421
  },
  {
    5422,
    5422,
    5422,
    5422,
    5422,
    5422,
    5422,
    5422,
    5423,
    5423,
    5423,
    5424,
    5424
  },
  {
    5425,
    5425,
    5425,
    5425,
    5425,
    5425,
    5425,
    5425,
    5426,
    5426,
    5426,
    5427,
    5427
  },
  {
    5428,
    5428,
    5428,
    5428,
    5428,
    5428,
    5428,
    5428,
    5429,
    5429,
    5429,
    5430,
    5430
  },
  {
    5431,
    5431,
    5431,
    5431,
    5431,
    5431,
    5432,
    5432,
    5432,
    5432,
    5433,
    5433,
    5433
  },
  {
    5501,
    5501,
    5501,
    5501,
    5501,
    5501,
    5501,
    5502,
    5502,
    5502,
    5502,
    5503,
    5503
  },
  {
    5504,
    5504,
    5504,
    5504,
    5504,
    5504,
    5504,
    5505,
    5505,
    5505,
    5505,
    5506,
    5506
  },
  {
    5507,
    5507,
    5507,
    5507,
    5507,
    5507,
    5507,
    5508,
    5508,
    5508,
    5508,
    5509,
    5509
  },
  {
    5510,
    5510,
    5510,
    5510,
    5510,
    5510,
    5510,
    5511,
    5511,
    5511,
    5511,
    5512,
    5512
  },
  {
    5513,
    5513,
    5513,
    5513,
    5513,
    5513,
    5513,
    5514,
    5514,
    5514,
    5514,
    5515,
    5515
  },
  {
    5516,
    5516,
    5516,
    5516,
    5516,
    5516,
    5516,
    5517,
    5517,
    5517,
    5517,
    5518,
    5518
  },
  {
    5519,
    5519,
    5519,
    5519,
    5519,
    5519,
    5519,
    5520,
    5520,
    5520,
    5520,
    5521,
    5521
  },
  {
    5522,
    5522,
    5522,
    5522,
    5522,
    5522,
    5522,
    5523,
    5523,
    5523,
    5523,
    5524,
    5524
  },
  {
    5525,
    5525,
    5525,
    5525,
    5525,
    5525,
    5525,
    5526,
    5526,
    5526,
    5526,
    5527,
    5527
  },
  {
    5528,
    5528,
    5528,
    5528,
    5528,
    5528,
    5528,
    5529,
    5529,
    5529,
    5529,
    5530,
    5530
  },
  {
    5531,
    5531,
    5531,
    5531,
    5531,
    5531,
    5532,
    5532,
    5532,
    5532,
    5533,
    5533,
    5533
  },
  {
    5601,
    5601,
    5601,
    5601,
    5601,
    5601,
    5601,
    5602,
    5602,
    5602,
    5602,
    5603,
    5603
  },
  {
    5604,
    5604,
    5604,
    5604,
    5604,
    5604,
    5604,
    5605,
    5605,
    5605,
    5605,
    5606,
    5606
  },
  {
    5607,
    5607,
    5607,
    5607,
    5607,
    5607,
    5607,
    5608,
    5608,
    5608,
    5608,
    5609,
    5609
  },
  {
    5610,
    5610,
    5610,
    5610,
    5610,
    5610,
    5610,
    5611,
    5611,
    5611,
    5611,
    5612,
    5612
  },
  {
    5613,
    5613,
    5613,
    5613,
    5613,
    5613,
    5613,
    5614,
    5614,
    5614,
    5614,
    5615,
    5615
  },
  {
    5616,
    5616,
    5616,
    5616,
    5616,
    5617,
    5617,
    5617,
    5618,
    5618,
    5618,
    5619,
    5619
  },
  {
    5701,
    5701,
    5701,
    5701,
    5701,
    5701,
    5701,
    5702,
    5702,
    5702,
    5702,
    5703,
    5703
  },
  {
    5704,
    5704,
    5704,
    5704,
    5704,
    5704,
    5704,
    5705,
    5705,
    5705,
    5705,
    5706,
    5706
  },
  {
    5707,
    5707,
    5707,
    5707,
    5707,
    5707,
    5707,
    5708,
    5708,
    5708,
    5708,
    5709,
    5709
  },
  {
    5710,
    5710,
    5710,
    5710,
    5710,
    5710,
    5710,
    5711,
    5711,
    5711,
    5711,
    5712,
    5712
  },
  {
    5713,
    5713,
    5713,
    5713,
    5713,
    5713,
    5713,
    5714,
    5714,
    5714,
    5714,
    5715,
    5715
  },
  {
    5716,
    5716,
    5716,
    5716,
    5716,
    5717,
    5717,
    5717,
    5718,
    5718,
    5718,
    5719,
    5719
  },
  {
    5801,
    5801,
    5801,
    5801,
    5801,
    5801,
    5801,
    5802,
    5802,
    5802,
    5802,
    5803,
    5803
  },
  {
    5804,
    5804,
    5804,
    5804,
    5804,
    5804,
    5804,
    5805,
    5805,
    5805,
    5805,
    5806,
    5806
  },
  {
    5807,
    5807,
    5807,
    5807,
    5807,
    5807,
    5807,
    5808,
    5808,
    5808,
    5808,
    5809,
    5809
  },
  {
    5809,
    5809,
    5809,
    5809,
    5809,
    5809,
    5809,
    5810,
    5810,
    5810,
    5810,
    5811,
    5811
  },
  {
    5812,
    5812,
    5812,
    5812,
    5812,
    5812,
    5812,
    5813,
    5813,
    5813,
    5813,
    5814,
    5814
  },
  {
    5815,
    5815,
    5815,
    5815,
    5815,
    5816,
    5816,
    5816,
    5817,
    5817,
    5817,
    5818,
    5818
  },
  {
    5901,
    5901,
    5901,
    5901,
    5901,
    5901,
    5901,
    5902,
    5902,
    5902,
    5902,
    5903,
    5903
  },
  {
    5904,
    5904,
    5904,
    5904,
    5904,
    5904,
    5904,
    5905,
    5905,
    5905,
    5905,
    5906,
    5906
  },
  {
    5907,
    5907,
    5907,
    5907,
    5907,
    5907,
    5907,
    5908,
    5908,
    5908,
    5908,
    5909,
    5909
  },
  {
    5910,
    5910,
    5910,
    5910,
    5910,
    5910,
    5910,
    5911,
    5911,
    5911,
    5911,
    5912,
    5912
  },
  {
    5913,
    5913,
    5913,
    5913,
    5913,
    5913,
    5913,
    5914,
    5914,
    5914,
    5914,
    5915,
    5915
  },
  {
    5916,
    5916,
    5916,
    5916,
    5916,
    5917,
    5917,
    5917,
    5918,
    5918,
    5918,
    5919,
    5919
  },
  {
    6001,
    6001,
    6001,
    6001,
    6001,
    6001,
    6001,
    6002,
    6002,
    6002,
    6002,
    6003,
    6003
  },
  {
    6004,
    6004,
    6004,
    6004,
    6004,
    6004,
    6004,
    6005,
    6005,
    6005,
    6005,
    6006,
    6006
  },
  {
    6007,
    6007,
    6007,
    6007,
    6007,
    6007,
    6007,
    6008,
    6008,
    6008,
    6008,
    6009,
    6009
  },
  {
    6010,
    6010,
    6010,
    6010,
    6010,
    6010,
    6010,
    6011,
    6011,
    6011,
    6011,
    6012,
    6012
  },
  {
    6013,
    6013,
    6013,
    6013,
    6013,
    6013,
    6013,
    6014,
    6014,
    6014,
    6014,
    6015,
    6015
  },
  {
    6016,
    6016,
    6016,
    6016,
    6016,
    6017,
    6017,
    6017,
    6018,
    6018,
    6018,
    6019,
    6019
  },
  {
    6101,
    6101,
    6101,
    6101,
    6101,
    6101,
    6110,
    6110,
    6110,
    6111,
    6111,
    6102,
    6102
  },
  {
    6105,
    6104,
    6104,
    6104,
    6104,
    6104,
    6117,
    6117,
    6117,
    6118,
    6118,
    6105,
    6105
  },
  {
    6112,
    6112,
    6112,
    6112,
    6112,
    6112,
    6113,
    6113,
    6113,
    6113,
    6103,
    6103,
    6103
  },
  {
    6114,
    6114,
    6114,
    6114,
    6114,
    6114,
    6115,
    6115,
    6115,
    6115,
    6116,
    6116,
    6116
  },
  {
    6119,
    6119,
    6119,
    6119,
    6119,
    6119,
    6120,
    6120,
    6120,
    6120,
    6106,
    6106,
    6106
  },
  {
    6121,
    6121,
    6121,
    6121,
    6121,
    6121,
    6122,
    6122,
    6122,
    6122,
    6123,
    6123,
    6123
  },
  {
    6125,
    6107,
    6107,
    6107,
    6107,
    6107,
    6124,
    6124,
    6124,
    6125,
    6125,
    6108,
    6108
  },
  {
    6126,
    6126,
    6126,
    6126,
    6126,
    6126,
    6127,
    6127,
    6127,
    6127,
    6109,
    6109,
    6109
  },
  {
    6128,
    6128,
    6128,
    6128,
    6128,
    6128,
    6129,
    6129,
    6129,
    6129,
    6130,
    6130,
    6130
  },
  {
    6131,
    6131,
    6131,
    6131,
    6131,
    6131,
    6132,
    6132,
    6132,
    6133,
    6133,
    6134,
    6134
  },
  {
    6135,
    6135,
    6135,
    6135,
    6135,
    6135,
    6136,
    6136,
    6136,
    6136,
    6137,
    6137,
    6137
  },
  {
    6138,
    6138,
    6138,
    6138,
    6138,
    6138,
    6139,
    6139,
    6139,
    6139,
    6140,
    6140,
    6140
  },
  {
    6141,
    6141,
    6141,
    6141,
    6141,
    6142,
    6142,
    6142,
    6142,
    6143,
    6143,
    6143,
    6143
  },
  {
    6145,
    6145,
    6145,
    6145,
    6145,
    6146,
    6146,
    6146,
    6146,
    6147,
    6147,
    6147,
    6147
  },
  {
    6149,
    6149,
    6149,
    6149,
    6149,
    6150,
    6150,
    6150,
    6150,
    6151,
    6151,
    6151,
    6151
  },
  {
    6153,
    6153,
    6153,
    6153,
    6153,
    6154,
    6154,
    6154,
    6154,
    6155,
    6155,
    6155,
    6155
  }
}; // idb
const int aryDropableHumanEquipList[99][16] =
{
  {
    601,
    501,
    1701,
    101,
    401,
    701,
    1201,
    901,
    1101,
    711,
    911,
    810,
    1306,
    1507,
    1606,
    1306
  },
  {
    801,
    1001,
    111,
    413,
    1502,
    1601,
    116,
    408,
    1301,
    711,
    911,
    810,
    1306,
    1507,
    1606,
    1306
  },
  {
    601,
    501,
    1701,
    101,
    401,
    702,
    1201,
    1401,
    1101,
    711,
    121,
    1106,
    819,
    811,
    1007,
    1507
  },
  {
    801,
    1001,
    1702,
    442,
    1502,
    702,
    117,
    408,
    1611,
    606,
    122,
    1106,
    819,
    811,
    1007,
    1507
  },
  {
    601,
    501,
    111,
    146,
    401,
    702,
    1201,
    1401,
    1611,
    424,
    506,
    912,
    1007,
    1111,
    820,
    1606
  },
  {
    802,
    1001,
    1703,
    442,
    402,
    702,
    117,
    408,
    1302,
    424,
    506,
    912,
    1007,
    1111,
    820,
    1606
  },
  {
    602,
    502,
    111,
    146,
    402,
    1601,
    903,
    902,
    803,
    506,
    1205,
    712,
    1311,
    1406,
    1507,
    1306
  },
  {
    802,
    1002,
    101,
    442,
    1612,
    1201,
    903,
    902,
    1302,
    506,
    1205,
    712,
    1311,
    1406,
    1507,
    1306
  },
  {
    602,
    502,
    111,
    1612,
    402,
    1602,
    1411,
    921,
    1701,
    121,
    721,
    1406,
    1008,
    1606,
    429,
    1406
  },
  {
    802,
    1002,
    1704,
    442,
    1503,
    703,
    1411,
    408,
    1302,
    122,
    721,
    1406,
    1008,
    1606,
    429,
    1406
  },
  {
    602,
    1703,
    112,
    103,
    703,
    1613,
    117,
    713,
    1102,
    607,
    1407,
    812,
    821,
    131,
    1406,
    424
  },
  {
    803,
    1002,
    722,
    414,
    703,
    1614,
    1209,
    713,
    1512,
    607,
    1407,
    812,
    821,
    131,
    1406,
    424
  },
  {
    1307,
    502,
    1705,
    147,
    722,
    1602,
    117,
    1102,
    1702,
    1407,
    507,
    913,
    1508,
    1307,
    1508,
    1607
  },
  {
    1307,
    1512,
    1705,
    414,
    1503,
    1402,
    1402,
    1102,
    1303,
    507,
    124,
    913,
    1508,
    1307,
    1508,
    429
  },
  {
    602,
    502,
    112,
    147,
    434,
    1615,
    1402,
    409,
    1103,
    507,
    123,
    1205,
    429,
    1008,
    922,
    429
  },
  {
    803,
    1013,
    1706,
    414,
    1503,
    704,
    1212,
    903,
    1703,
    151,
    1408,
    1206,
    429,
    1008,
    813,
    922
  },
  {
    603,
    1013,
    1412,
    145,
    434,
    1607,
    1212,
    903,
    1103,
    151,
    1408,
    1406,
    1607,
    1607,
    813,
    1607
  },
  {
    804,
    1002,
    1707,
    414,
    1503,
    704,
    903,
    1412,
    1704,
    144,
    418,
    1406,
    1616,
    1107,
    1312,
    1312
  },
  {
    1617,
    503,
    112,
    145,
    1616,
    1603,
    903,
    903,
    166,
    144,
    418,
    1308,
    1107,
    1508,
    1312,
    1407
  },
  {
    804,
    1003,
    1707,
    443,
    1617,
    705,
    1202,
    409,
    1303,
    425,
    915,
    1308,
    1107,
    1508,
    1508,
    1009
  },
  {
    603,
    503,
    112,
    1403,
    1504,
    1603,
    1202,
    1617,
    166,
    425,
    915,
    1407,
    136,
    136,
    1508,
    814
  },
  {
    804,
    1003,
    1708,
    443,
    436,
    705,
    1202,
    409,
    1705,
    1608,
    715,
    714,
    136,
    1307,
    136,
    814
  },
  {
    603,
    1313,
    113,
    1403,
    1504,
    1604,
    1618,
    904,
    1104,
    137,
    715,
    1009,
    914,
    822,
    1308,
    1608
  },
  {
    723,
    1313,
    1709,
    443,
    1108,
    706,
    706,
    409,
    1304,
    608,
    419,
    418,
    914,
    822,
    814,
    1509
  },
  {
    723,
    1513,
    1709,
    152,
    436,
    511,
    1619,
    905,
    1706,
    138,
    419,
    418,
    1206,
    914,
    914,
    430
  },
  {
    805,
    1513,
    1710,
    443,
    1504,
    706,
    923,
    409,
    1304,
    1309,
    425,
    1108,
    1206,
    914,
    1509,
    430
  },
  {
    603,
    815,
    1710,
    152,
    446,
    511,
    435,
    905,
    157,
    430,
    425,
    1608,
    1509,
    1608,
    132,
    1509
  },
  {
    805,
    1003,
    1404,
    443,
    1504,
    707,
    435,
    410,
    1707,
    125,
    508,
    1509,
    1509,
    1620,
    430,
    137
  },
  {
    603,
    503,
    1314,
    1621,
    403,
    1605,
    1210,
    906,
    1104,
    126,
    1609,
    915,
    1308,
    1206,
    430,
    823
  },
  {
    806,
    1003,
    1711,
    443,
    1505,
    707,
    118,
    410,
    1104,
    508,
    125,
    716,
    815,
    1206,
    1010,
    815
  },
  {
    716,
    823,
    1314,
    1514,
    446,
    1605,
    924,
    906,
    158,
    508,
    126,
    425,
    915,
    1407,
    1010,
    1309
  },
  {
    806,
    823,
    153,
    415,
    1505,
    161,
    924,
    410,
    1708,
    612,
    1207,
    1622,
    1207,
    132,
    915,
    133
  },
  {
    724,
    148,
    1514,
    105,
    434,
    1213,
    1203,
    436,
    158,
    612,
    916,
    717,
    425,
    1009,
    816,
    138
  },
  {
    724,
    1004,
    816,
    153,
    1112,
    1014,
    816,
    436,
    1305,
    609,
    916,
    133,
    1510,
    817,
    1109,
    817
  },
  {
    717,
    148,
    1622,
    1213,
    925,
    1014,
    1203,
    907,
    1709,
    1511,
    509,
    1604,
    1207,
    1112,
    1109,
    1510
  },
  {
    807,
    824,
    725,
    444,
    925,
    708,
    161,
    437,
    1604,
    1610,
    1310,
    1623,
    1207,
    816,
    1409,
    1309
  },
  {
    604,
    824,
    725,
    107,
    437,
    708,
    1211,
    917,
    1305,
    509,
    127,
    926,
    817,
    1206,
    133,
    1409
  },
  {
    807,
    1004,
    163,
    163,
    1505,
    825,
    119,
    917,
    1105,
    142,
    128,
    127,
    1011,
    1309,
    141,
    1610
  },
  {
    604,
    504,
    114,
    164,
    449,
    825,
    513,
    908,
    1710,
    142,
    160,
    128,
    818,
    1408,
    1610,
    1012
  },
  {
    807,
    1413,
    1713,
    444,
    1506,
    708,
    926,
    411,
    1305,
    448,
    160,
    447,
    138,
    425,
    1511,
    818
  },
  {
    1413,
    154,
    114,
    109,
    449,
    420,
    513,
    908,
    1105,
    1208,
    448,
    447,
    1309,
    916,
    818,
    134
  },
  {
    808,
    1005,
    1405,
    416,
    1506,
    709,
    120,
    411,
    143,
    431,
    1207,
    1109,
    918,
    1011,
    1310,
    159
  },
  {
    604,
    154,
    114,
    109,
    404,
    420,
    1211,
    908,
    1711,
    431,
    1207,
    1109,
    1511,
    133,
    426,
    1012
  },
  {
    927,
    1005,
    1714,
    416,
    1515,
    709,
    1609,
    437,
    1414,
    1511,
    718,
    718,
    1208,
    1409,
    1012,
    115
  },
  {
    927,
    504,
    1609,
    613,
    404,
    1515,
    1113,
    909,
    1414,
    1511,
    1624,
    1208,
    1624,
    1310,
    1310,
    818
  },
  {
    726,
    451,
    1315,
    445,
    1506,
    826,
    1113,
    452,
    1712,
    1625,
    719,
    919,
    450,
    1511,
    818,
    1310
  },
  {
    726,
    504,
    1315,
    613,
    405,
    826,
    1625,
    909,
    1305,
    1605,
    1511,
    143,
    139,
    1012,
    450,
    427
  },
  {
    809,
    1006,
    1715,
    445,
    164,
    709,
    1626,
    423,
    162,
    1012,
    1605,
    1207,
    919,
    1110,
    1012,
    818
  },
  {
    809,
    1208,
    453,
    109,
    405,
    827,
    512,
    1114,
    1713,
    1012,
    1626,
    1310,
    1410,
    918,
    1410,
    149
  },
  {
    1114,
    454,
    454,
    417,
    1506,
    710,
    120,
    909,
    1714,
    150,
    1110,
    1511,
    1610,
    426,
    1208,
    427
  },
  {
    605,
    505,
    454,
    1627,
    406,
    827,
    512,
    910,
    1305,
    610,
    115,
    432,
    149,
    139,
    140,
    1628
  },
  {
    605,
    1006,
    1208,
    445,
    1506,
    710,
    120,
    910,
    1715,
    610,
    135,
    1628,
    720,
    818,
    1628,
    1110
  },
  {
    727,
    1214,
    1715,
    417,
    456,
    826,
    1114,
    412,
    1715,
    150,
    510,
    1110,
    432,
    1410,
    920,
    155
  },
  {
    727,
    1214,
    611,
    445,
    455,
    827,
    1114,
    920,
    1105,
    818,
    451,
    1110,
    1511,
    450,
    1629,
    1310
  },
  {
    728,
    1006,
    1715,
    109,
    1015,
    1605,
    1211,
    412,
    1305,
    818,
    1012,
    129,
    720,
    1310,
    1605,
    155
  },
  {
    728,
    1006,
    1716,
    417,
    1115,
    1627,
    120,
    910,
    1105,
    427,
    451,
    720,
    1629,
    140,
    928,
    1629
  },
  {
    729,
    505,
    114,
    433,
    1215,
    828,
    1211,
    412,
    455,
    427,
    454,
    1012,
    433,
    1610,
    929,
    920
  },
  {
    729,
    505,
    1716,
    433,
    1516,
    828,
    120,
    910,
    1105,
    428,
    140,
    428,
    433,
    920,
    930,
    156
  },
  {
    729,
    1415,
    456,
    1015,
    456,
    829,
    1115,
    428,
    1215,
    428,
    455,
    135,
    1630,
    1511,
    1630,
    1610
  },
  {
    729,
    505,
    1715,
    433,
    1516,
    828,
    120,
    910,
    1105,
    428,
    1012,
    428,
    422,
    920,
    930,
    156
  },
  {
    729,
    828,
    1215,
    1015,
    456,
    1630,
    1115,
    130,
    466,
    140,
    471,
    1415,
    1715,
    457,
    929,
    455
  },
  {
    729,
    1215,
    929,
    1115,
    828,
    828,
    1630,
    1315,
    1516,
    1712,
    142,
    120,
    115,
    454,
    433,
    625
  },
  {
    729,
    1215,
    929,
    1115,
    828,
    1015,
    1630,
    1315,
    1516,
    1712,
    160,
    120,
    115,
    456,
    428,
    610
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1316,
    1517,
    1713,
    143,
    120,
    115,
    456,
    433,
    524
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1416,
    1517,
    1713,
    128,
    120,
    115,
    454,
    428,
    510
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1316,
    1517,
    1713,
    127,
    120,
    115,
    456,
    466,
    625
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1416,
    1517,
    1713,
    142,
    120,
    115,
    456,
    471,
    610
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1316,
    1517,
    1713,
    160,
    120,
    115,
    454,
    466,
    524
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1416,
    1517,
    1713,
    143,
    120,
    115,
    456,
    471,
    510
  },
  {
    730,
    1216,
    930,
    1116,
    829,
    1016,
    1631,
    1316,
    1517,
    1713,
    128,
    120,
    115,
    456,
    466,
    625
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1416,
    1518,
    1714,
    127,
    164,
    156,
    455,
    471,
    610
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1316,
    1518,
    1714,
    110,
    164,
    156,
    457,
    466,
    524
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1416,
    1518,
    1714,
    109,
    164,
    156,
    457,
    471,
    510
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1316,
    1518,
    1714,
    110,
    164,
    156,
    455,
    466,
    611
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1416,
    1518,
    1714,
    109,
    164,
    156,
    457,
    471,
    626
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1316,
    1518,
    1714,
    110,
    164,
    156,
    457,
    466,
    525
  },
  {
    731,
    1217,
    931,
    1117,
    830,
    1017,
    1632,
    1416,
    1518,
    1714,
    109,
    164,
    156,
    455,
    471,
    512
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1317,
    1519,
    1715,
    129,
    164,
    156,
    457,
    467,
    611
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1417,
    1519,
    1715,
    162,
    140,
    135,
    462,
    472,
    626
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1317,
    1519,
    1715,
    130,
    140,
    135,
    458,
    467,
    525
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1417,
    1519,
    1715,
    129,
    140,
    135,
    458,
    472,
    512
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1317,
    1519,
    1715,
    162,
    140,
    135,
    462,
    467,
    611
  },
  {
    732,
    1218,
    932,
    1118,
    831,
    1018,
    1633,
    1417,
    1519,
    1715,
    130,
    140,
    135,
    458,
    472,
    626
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    129,
    140,
    135,
    458,
    467,
    525
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    162,
    140,
    135,
    462,
    472,
    512
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    130,
    140,
    135,
    458,
    467,
    611
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    129,
    140,
    135,
    458,
    472,
    626
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    162,
    140,
    135,
    462,
    467,
    525
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    130,
    140,
    135,
    458,
    472,
    512
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    130,
    140,
    135,
    458,
    467,
    611
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    129,
    140,
    135,
    458,
    472,
    626
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    162,
    140,
    135,
    462,
    467,
    525
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    130,
    140,
    135,
    458,
    472,
    512
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    130,
    140,
    135,
    458,
    467,
    611
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    129,
    140,
    135,
    458,
    472,
    626
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    162,
    140,
    135,
    462,
    467,
    525
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    130,
    140,
    135,
    458,
    472,
    512
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1317,
    1520,
    1716,
    162,
    140,
    135,
    462,
    467,
    525
  },
  {
    733,
    1219,
    933,
    1119,
    832,
    1019,
    1634,
    1417,
    1520,
    1716,
    130,
    140,
    135,
    458,
    472,
    512
  }
}; // idb
const int aryDropableAkhanEquipList[99][16] =
{
  {
    5210,
    5201,
    5110,
    5101,
    6102,
    5301,
    5001,
    5018,
    5401,
    5501,
    5701,
    5601,
    5801,
    5901,
    6101,
    0
  },
  {
    5210,
    5201,
    5110,
    5101,
    6103,
    5309,
    5001,
    5018,
    5401,
    5501,
    5701,
    5601,
    5801,
    5901,
    6101,
    0
  },
  {
    5035,
    5201,
    5110,
    5101,
    6103,
    5301,
    5001,
    5018,
    5402,
    5502,
    5701,
    5601,
    5801,
    5901,
    6001,
    0
  },
  {
    5035,
    5201,
    5110,
    5101,
    6101,
    5309,
    5002,
    5019,
    5402,
    5502,
    5701,
    5601,
    5801,
    5901,
    6001,
    0
  },
  {
    5210,
    5201,
    5112,
    5102,
    6104,
    5301,
    5002,
    5019,
    5403,
    5503,
    5701,
    5602,
    5802,
    5901,
    6131,
    0
  },
  {
    5210,
    5201,
    5112,
    5102,
    6107,
    5309,
    5003,
    5019,
    5403,
    5503,
    5701,
    5602,
    5802,
    5902,
    6131,
    0
  },
  {
    5210,
    5201,
    5112,
    5102,
    6101,
    5301,
    5003,
    5019,
    5404,
    5504,
    5702,
    5602,
    5802,
    5902,
    6002,
    0
  },
  {
    5211,
    5201,
    5112,
    5102,
    6104,
    5309,
    5003,
    5020,
    5404,
    5504,
    5702,
    5602,
    5802,
    5902,
    6002,
    0
  },
  {
    5211,
    5201,
    5113,
    5103,
    6107,
    5302,
    5003,
    5020,
    5405,
    5505,
    5702,
    5603,
    5803,
    5903,
    6132,
    0
  },
  {
    5036,
    5202,
    5113,
    5103,
    6110,
    5310,
    5004,
    5020,
    5405,
    5505,
    5702,
    5603,
    5803,
    5903,
    6132,
    0
  },
  {
    5036,
    5202,
    5113,
    5103,
    6117,
    5302,
    5004,
    5020,
    5406,
    5506,
    5702,
    5603,
    5803,
    5903,
    6002,
    0
  },
  {
    5211,
    5202,
    5113,
    5103,
    6124,
    5310,
    5004,
    5021,
    5406,
    5506,
    5703,
    5603,
    5804,
    5903,
    6002,
    0
  },
  {
    5211,
    5202,
    5114,
    5104,
    6110,
    5302,
    5005,
    5021,
    5407,
    5507,
    5703,
    5604,
    5804,
    5904,
    6003,
    0
  },
  {
    5212,
    5202,
    5114,
    5104,
    6117,
    5310,
    5005,
    5021,
    5407,
    5507,
    5703,
    5604,
    5804,
    5904,
    6003,
    0
  },
  {
    5037,
    5202,
    5114,
    5104,
    6124,
    5303,
    5005,
    5021,
    5408,
    5508,
    5703,
    5604,
    5805,
    5904,
    6133,
    0
  },
  {
    5037,
    5203,
    5114,
    5104,
    6111,
    5311,
    5006,
    5022,
    5408,
    5508,
    5703,
    5604,
    5805,
    5904,
    6133,
    0
  },
  {
    5212,
    5203,
    5115,
    5105,
    6118,
    5303,
    5006,
    5022,
    5409,
    5509,
    5704,
    5605,
    5805,
    5905,
    6004,
    0
  },
  {
    5212,
    5203,
    5115,
    5105,
    6125,
    5311,
    5006,
    5022,
    5409,
    5509,
    5704,
    5605,
    5805,
    5905,
    6004,
    0
  },
  {
    5038,
    5203,
    5115,
    5105,
    6125,
    5303,
    5007,
    5022,
    5410,
    5510,
    5704,
    5605,
    5805,
    5905,
    6134,
    0
  },
  {
    5038,
    5203,
    5115,
    5105,
    6102,
    5311,
    5007,
    5023,
    5410,
    5510,
    5704,
    5605,
    5806,
    5905,
    6134,
    0
  },
  {
    5213,
    5203,
    5116,
    5106,
    6105,
    5304,
    5008,
    5023,
    5411,
    5511,
    5705,
    5606,
    5806,
    5906,
    6005,
    0
  },
  {
    5213,
    5203,
    5116,
    5106,
    6108,
    5312,
    5008,
    5023,
    5411,
    5511,
    5705,
    5606,
    5806,
    5906,
    6005,
    0
  },
  {
    5213,
    5203,
    5116,
    5106,
    6112,
    5304,
    5009,
    5023,
    5412,
    5512,
    5705,
    5606,
    5807,
    5906,
    6135,
    0
  },
  {
    5213,
    5204,
    5116,
    5107,
    6119,
    5312,
    5009,
    5024,
    5412,
    5512,
    5705,
    5606,
    5807,
    5906,
    6135,
    0
  },
  {
    5039,
    5204,
    5117,
    5107,
    6126,
    5304,
    5009,
    5024,
    5413,
    5513,
    5706,
    5607,
    5807,
    5907,
    6136,
    0
  },
  {
    5039,
    5204,
    5117,
    5107,
    6113,
    5312,
    5009,
    5024,
    5413,
    5513,
    5706,
    5607,
    5807,
    5907,
    6136,
    0
  },
  {
    5214,
    5204,
    5117,
    5107,
    6120,
    5305,
    5010,
    5024,
    5414,
    5514,
    5706,
    5607,
    5808,
    5907,
    6006,
    0
  },
  {
    5214,
    5204,
    5117,
    5104,
    6127,
    5313,
    5010,
    5025,
    5414,
    5514,
    5706,
    5607,
    5808,
    5908,
    6006,
    0
  },
  {
    5040,
    5204,
    5118,
    5104,
    6113,
    5305,
    5011,
    5025,
    5415,
    5515,
    5707,
    5608,
    5808,
    5908,
    6137,
    0
  },
  {
    5040,
    5204,
    5118,
    5108,
    6120,
    5313,
    5011,
    5025,
    5415,
    5515,
    5707,
    5608,
    5808,
    5908,
    6137,
    0
  },
  {
    5041,
    5205,
    5118,
    5108,
    6127,
    5305,
    5012,
    5025,
    5416,
    5516,
    5707,
    5608,
    5809,
    5909,
    6007,
    0
  },
  {
    5041,
    5205,
    5118,
    5108,
    6103,
    5313,
    5012,
    5026,
    5416,
    5516,
    5707,
    5609,
    5809,
    5909,
    6007,
    0
  },
  {
    5215,
    5205,
    5127,
    5119,
    6106,
    5305,
    5012,
    5026,
    5417,
    5517,
    5708,
    5609,
    5809,
    5909,
    6138,
    0
  },
  {
    5215,
    5205,
    5127,
    5119,
    6109,
    5313,
    5012,
    5026,
    5417,
    5517,
    5708,
    5609,
    5809,
    5910,
    6138,
    0
  },
  {
    5042,
    5205,
    5127,
    5119,
    6103,
    5306,
    5013,
    5026,
    5418,
    5518,
    5708,
    5610,
    5810,
    5910,
    6008,
    0
  },
  {
    5042,
    5205,
    5127,
    5120,
    6114,
    5314,
    5013,
    5027,
    5418,
    5518,
    5708,
    5610,
    5810,
    5910,
    6008,
    0
  },
  {
    5215,
    5119,
    5128,
    5520,
    6121,
    5306,
    5013,
    5027,
    5419,
    5519,
    5709,
    5610,
    5810,
    6139,
    6139,
    0
  },
  {
    5215,
    5206,
    5128,
    5120,
    6128,
    5314,
    5013,
    5027,
    5419,
    5520,
    5709,
    5611,
    5811,
    5911,
    6009,
    0
  },
  {
    5215,
    5206,
    5128,
    5420,
    6114,
    5306,
    5014,
    5028,
    5420,
    5521,
    5710,
    5611,
    5811,
    6139,
    6140,
    0
  },
  {
    5043,
    5206,
    5128,
    5120,
    6121,
    5314,
    5014,
    5028,
    5421,
    5522,
    5710,
    5611,
    5811,
    5911,
    6009,
    0
  },
  {
    5043,
    5121,
    5129,
    5522,
    6128,
    5307,
    5014,
    5029,
    5421,
    5523,
    5710,
    5611,
    5811,
    5912,
    6009,
    0
  },
  {
    5216,
    5207,
    5129,
    5423,
    6115,
    5315,
    5014,
    5029,
    5422,
    5524,
    5710,
    5611,
    5812,
    6139,
    6010,
    0
  },
  {
    5216,
    5207,
    5422,
    5121,
    6122,
    5307,
    5015,
    5029,
    5423,
    5525,
    5711,
    5612,
    5812,
    5912,
    6010,
    0
  },
  {
    5217,
    5207,
    5129,
    5121,
    6129,
    5315,
    5015,
    5030,
    5423,
    5525,
    5711,
    5612,
    5812,
    5913,
    6011,
    0
  },
  {
    5044,
    5208,
    5129,
    5425,
    6115,
    5307,
    5015,
    5031,
    5424,
    5526,
    5712,
    5613,
    5813,
    5913,
    6011,
    0
  },
  {
    5044,
    5208,
    5130,
    5525,
    6122,
    5122,
    5015,
    5031,
    5425,
    5526,
    5712,
    5613,
    5813,
    6139,
    6012,
    0
  },
  {
    5217,
    5208,
    5424,
    5526,
    6129,
    5122,
    5016,
    5031,
    5426,
    5527,
    5713,
    5613,
    5813,
    5914,
    6012,
    0
  },
  {
    5108,
    5426,
    5425,
    5108,
    6116,
    5122,
    5130,
    5032,
    5426,
    5528,
    5714,
    5614,
    5813,
    5914,
    6013,
    0
  },
  {
    5108,
    5427,
    5528,
    5527,
    6123,
    5130,
    5016,
    5032,
    5427,
    5529,
    5714,
    5614,
    5814,
    5914,
    6013,
    0
  },
  {
    5045,
    5428,
    5130,
    5529,
    6130,
    5316,
    5016,
    5032,
    5428,
    5530,
    5714,
    5614,
    5814,
    6139,
    6014,
    0
  },
  {
    5045,
    5429,
    5131,
    5530,
    6116,
    5308,
    5017,
    5032,
    5429,
    5530,
    5715,
    5615,
    5814,
    5915,
    6014,
    0
  },
  {
    5109,
    5209,
    5131,
    5123,
    6123,
    5316,
    5017,
    5032,
    5430,
    5530,
    5715,
    5615,
    5814,
    5915,
    6015,
    0
  },
  {
    5109,
    5209,
    5131,
    5123,
    6130,
    5308,
    5017,
    5032,
    5430,
    5530,
    5715,
    5615,
    5814,
    6139,
    6015,
    0
  },
  {
    5109,
    5209,
    5131,
    5123,
    6116,
    5316,
    5017,
    5032,
    5430,
    5530,
    5715,
    5615,
    5814,
    5915,
    6015,
    0
  },
  {
    5046,
    5209,
    5132,
    5124,
    6123,
    5308,
    5017,
    5032,
    5430,
    5530,
    5715,
    5615,
    5814,
    5915,
    6015,
    0
  },
  {
    5047,
    5209,
    5132,
    5124,
    6130,
    5316,
    5017,
    5033,
    5430,
    5530,
    5716,
    5616,
    5815,
    5916,
    6016,
    0
  },
  {
    5047,
    5209,
    5132,
    5124,
    6116,
    5308,
    5017,
    5033,
    5430,
    5530,
    5716,
    5616,
    5815,
    6140,
    6016,
    0
  },
  {
    5047,
    5209,
    5132,
    5125,
    6123,
    5316,
    5017,
    5033,
    5430,
    5530,
    5716,
    5616,
    5815,
    5916,
    6016,
    0
  },
  {
    5047,
    5209,
    5133,
    5125,
    6130,
    5308,
    5017,
    5033,
    5430,
    5530,
    5716,
    5616,
    5815,
    5916,
    6016,
    0
  },
  {
    5047,
    5209,
    5133,
    5125,
    6116,
    5316,
    5017,
    5033,
    5430,
    5530,
    5716,
    5616,
    5815,
    5916,
    6016,
    0
  },
  {
    5048,
    5209,
    5133,
    5125,
    6123,
    5518,
    5017,
    5033,
    5431,
    5531,
    5716,
    5616,
    5325,
    6140,
    6016,
    0
  },
  {
    5531,
    5431,
    6142,
    6146,
    6150,
    5718,
    6017,
    5917,
    6154,
    5125,
    5133,
    5223,
    5319,
    5015,
    5049,
    0
  },
  {
    5531,
    5431,
    6142,
    6146,
    6150,
    5817,
    6017,
    5917,
    6154,
    5125,
    5133,
    5221,
    5325,
    5015,
    5032,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5718,
    6018,
    5918,
    6154,
    5125,
    5133,
    5223,
    5319,
    5015,
    5032,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5817,
    6018,
    5918,
    6154,
    5125,
    5133,
    5221,
    5325,
    5015,
    5049,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5518,
    6018,
    5918,
    6154,
    5125,
    5133,
    5223,
    5319,
    5015,
    5032,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5718,
    6018,
    5918,
    6154,
    5125,
    5133,
    5221,
    5325,
    5015,
    5032,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5817,
    6018,
    5918,
    6154,
    5125,
    5133,
    5223,
    5319,
    5015,
    5049,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5519,
    6018,
    5918,
    6154,
    5125,
    5133,
    5221,
    5325,
    5015,
    5032,
    0
  },
  {
    5532,
    5432,
    6142,
    6146,
    6150,
    5719,
    6018,
    5918,
    6154,
    5125,
    5133,
    5223,
    5319,
    5015,
    5032,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5818,
    6019,
    5919,
    6155,
    5125,
    5133,
    5221,
    5325,
    5015,
    5049,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5519,
    6019,
    5919,
    6155,
    5125,
    5133,
    5223,
    5319,
    5015,
    5032,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5719,
    6019,
    5919,
    6155,
    5126,
    5134,
    5222,
    5326,
    5016,
    5033,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5818,
    6019,
    5919,
    6155,
    5126,
    5134,
    5224,
    5320,
    5016,
    5050,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5519,
    6019,
    5919,
    6155,
    5126,
    5134,
    5222,
    5326,
    5016,
    5033,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5719,
    6019,
    5919,
    6155,
    5126,
    5134,
    5224,
    5320,
    5016,
    5033,
    0
  },
  {
    5533,
    5433,
    6143,
    6147,
    6151,
    5818,
    6019,
    5919,
    6155,
    5126,
    5134,
    5222,
    5326,
    5016,
    5050,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5620,
    6020,
    5920,
    6155,
    5126,
    5134,
    5224,
    5320,
    5016,
    5033,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5720,
    6020,
    5920,
    6155,
    5126,
    5134,
    5222,
    5320,
    5016,
    5033,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5819,
    6020,
    5920,
    6155,
    5126,
    5134,
    5224,
    5326,
    5016,
    5050,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5620,
    6020,
    5920,
    6155,
    5126,
    5134,
    5222,
    5320,
    5016,
    5033,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5720,
    6020,
    5920,
    6155,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5534,
    5434,
    6143,
    6147,
    6151,
    5819,
    6020,
    5920,
    6155,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5721,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5820,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5721,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5820,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5721,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5820,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5721,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5820,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5721,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5820,
    6021,
    5921,
    6156,
    5135,
    5140,
    5230,
    5327,
    5017,
    5051,
    0
  },
  {
    5535,
    5435,
    6144,
    6148,
    6152,
    5621,
    6021,
    5921,
    6156,
    5135,
    5140,
    5225,
    5321,
    5017,
    5034,
    0
  }
}; // idb
const unsigned __int8 aryFirstDropableEquipList[25][8] =
{
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 8u, 10u, 3u, 16u, 57u, 57u },
  { 15u, 3u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 14u, 3u, 15u, 57u, 57u, 57u, 57u, 57u },
  { 7u, 9u, 3u, 57u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 8u, 10u, 3u, 16u, 9u, 7u },
  { 6u, 11u, 8u, 10u, 3u, 16u, 57u, 57u },
  { 15u, 3u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 12u, 13u, 3u, 57u, 57u, 57u, 57u, 57u },
  { 14u, 3u, 15u, 57u, 57u, 57u, 57u, 57u },
  { 12u, 15u, 14u, 13u, 3u, 57u, 57u, 57u },
  { 7u, 9u, 3u, 57u, 57u, 57u, 57u, 57u },
  { 7u, 9u, 3u, 6u, 11u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 33u, 34u, 29u, 30u, 57u, 57u, 57u, 57u },
  { 35u, 36u, 37u, 38u, 39u, 29u, 30u, 57u },
  { 33u, 34u, 40u, 30u, 57u, 57u, 57u, 57u },
  { 33u, 34u, 41u, 30u, 57u, 57u, 57u, 57u },
  { 33u, 34u, 42u, 30u, 57u, 57u, 57u, 57u },
  { 39u, 38u, 30u, 57u, 57u, 57u, 57u, 57u },
  { 35u, 37u, 30u, 57u, 57u, 57u, 57u, 57u },
  { 36u, 38u, 43u, 30u, 57u, 57u, 57u, 57u }
}; // idb
const unsigned __int8 arySecondDropableEquipList[25][8] =
{
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 4u, 9u, 7u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 16u, 4u, 5u, 57u, 57u, 57u },
  { 16u, 4u, 5u, 57u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 16u, 4u, 5u, 57u, 57u, 57u },
  { 4u, 5u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 7u, 9u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 6u, 11u, 4u, 5u, 57u, 57u, 57u, 57u },
  { 4u, 5u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 57u, 57u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 37u, 31u, 32u, 57u, 57u, 57u, 57u, 57u },
  { 31u, 32u, 57u, 57u, 57u, 57u, 57u, 57u },
  { 37u, 31u, 32u, 57u, 57u, 57u, 57u, 57u }
}; // idb
const char aDWorkRylSource_72[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4EDAA8 = '\xBA'; // idb
const char byte_4EDB04 = '\xC0'; // idb
const char aDWorkRylSource_33[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_0[] = "CID:0x%08x "; // idb
const char aCid0x08x_162[] = "CID:0x%08x "; // idb
const char aCid0x08x_180[] = "CID:0x%08x "; // idb
const char aCid0x08x_68[] = "CID:0x%08x "; // idb
const char aCid0x08x_315[] = "CID:0x%08x "; // idb
const char aCid0x08x_196[] = "CID:0x%08x "; // idb
const char aCid0x08x_20[] = "CID:0x%08x "; // idb
const char aCid0x08x_317[] = "CID:0x%08x "; // idb
const char aCid0x08x_177[] = "CID:0x%08x "; // idb
const char aCid0x08x_265[] = "CID:0x%08x "; // idb
const char aCid0x08x_154[] = "CID:0x%08x "; // idb
const char aCid0x08x_101[] = "CID:0x%08x "; // idb
const char aCid0x08x_206[] = "CID:0x%08x "; // idb
const char aCid0x08x_215[] = "CID:0x%08x "; // idb
const char aCid0x08x_181[] = "CID:0x%08x "; // idb
const char aCid0x08x_227[] = "CID:0x%08x "; // idb
const char aCid0x08x_147[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_101[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_134[] = "CID:0x%08x "; // idb
const char aCid0x08x_258[] = "CID:0x%08x "; // idb
const char aCid0x08x_49[] = "CID:0x%08x "; // idb
const char aCid0x08x_35[] = "CID:0x%08x "; // idb
const char aCid0x08x_103[] = "CID:0x%08x "; // idb
char aCid0x08x_94[11] = "CID:0x%08x "; // weak
const char aCid0x08x_130[] = "CID:0x%08x "; // idb
const char byte_4EE5AC = '\xBF'; // idb
char aCid0x08x_328[11] = "CID:0x%08x "; // weak
char aCid0x08xFameD[19] = "CID:0x%08x Fame:%d "; // weak
char aCid0x08xGid0x0[22] = "CID:0x%08x GID:0x%08x "; // weak
const char aCid0x08xGid0x0_0[] = "CID:0x%08x GID:0x%08x "; // idb
char aCid0x08x_281[11] = "CID:0x%08x "; // weak
const char aCid0x08xSrcpos[] = "CID:0x%08x SrcPos"; // idb
const char aCid0x08x_209[] = "CID:0x%08x "; // idb
const char aSrc[] = "Src"; // idb
const char aCid0x08x_81[] = "CID:0x%08x "; // idb
const char aCid0x08x_198[] = "CID:0x%08x "; // idb
char aCid0x08x_202[11] = "CID:0x%08x "; // weak
char aCid0x08x_65[11] = "CID:0x%08x "; // weak
const char aCid0x08x_204[] = "CID:0x%08x "; // idb
char aCid0x08x_246[11] = "CID:0x%08x "; // weak
const char aCid0x08x_38[] = "CID:0x%08x "; // idb
const char aCid0x08x_90[] = "CID:0x%08x "; // idb
char aCid0x08x_32[11] = "CID:0x%08x "; // weak
char aCid0x08x_12[11] = "CID:0x%08x "; // weak
const char aCid0x08x_289[] = "CID:0x%08x "; // idb
const char aCid0x08x_27[] = "CID:0x%08x "; // idb
const char aCid0x08x_143[] = "CID:0x%08x "; // idb
char aCid0x08x_120[11] = "CID:0x%08x "; // weak
char aCid0x08x_292[11] = "CID:0x%08x "; // weak
const char aCid0x08x_163[] = "CID:0x%08x "; // idb
const char aCid0x08xSrctak[] = "CID:0x%08x SrcTake"; // idb
const char aCid0x08x_99[] = "CID:0x%08x "; // idb
char aCid0x08xDsttak[18] = "CID:0x%08x DstTake"; // weak
char aCid0x08x_24[11] = "CID:0x%08x "; // weak
const char aCid0x08x_176[] = "CID:0x%08x "; // idb
const char aCid0x08x_39[] = "CID:0x%08x "; // idb
const char aCid0x08xSrcpos_0[] = "CID:0x%08x SrcPos"; // idb
const char aCid0x08x_72[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_17[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_319[] = "CID:0x%08x "; // idb
void *CParty::`vftable' = &CParty::`vector deleting destructor'; // weak
const char FileName[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4EF23C = '\xC0'; // idb
const char byte_4EF250 = '\xC0'; // idb
const char szMessage = '\xB8'; // idb
const char byte_4EF290 = '\xC1'; // idb
const char byte_4EF2F0 = '\xC6'; // idb
const char aCid0x08x_29[] = "CID:0x%08x "; // idb
const char byte_4EF370 = '\xB8'; // idb
const char byte_4EF3B0 = '\xC6'; // idb
const char aCid0x08x_100[] = "CID:0x%08x "; // idb
const char byte_4EF468 = '\xB8'; // idb
const char byte_4EF4A4 = '\xC4'; // idb
const char byte_4EF518 = '\xC0'; // idb
const char byte_4EF578 = '\xC4'; // idb
const char aPid0x08x_7[] = "PID:0x%08x "; // idb
void *CCharacterParty::`vftable' = &CCharacterParty::`scalar deleting destructor'; // weak
const char byte_4EF6C8 = '\xC6'; // idb
const char byte_4EF710 = '\xB0'; // idb
const char byte_4EF744 = '\xC4'; // idb
char aPid0x08x_2[11] = "PID:0x%08x "; // weak
const char aPid0x08x[] = "PID:0x%08x "; // idb
const char byte_4EF888 = '\xC6'; // idb
const char byte_4EF8F8 = '\xC6'; // idb
const char byte_4EF970 = '\xC6'; // idb
const char byte_4EF9F0 = '\xC6'; // idb
const char byte_4EFA4C = '\xC4'; // idb
const char SenderName_In = '\xC6'; // idb
const char byte_4EFAA0 = '\xC6'; // idb
const char byte_4EFAF4 = '\xBF'; // idb
const char aCid0x08x_310[] = "CID:0x%08x "; // idb
const char aCid0x08x_30[] = "CID:0x%08x "; // idb
void *CMonsterParty::`vftable' = &CMonsterParty::`vector deleting destructor'; // weak
void *CNPC::`vftable' = &CNPC::`scalar deleting destructor'; // weak
const char aNid0x08x_4[] = "NID:0x%08x "; // idb
char aNid0x08x_1[11] = "NID:0x%08x "; // weak
const char aNid0x08x_5[] = "NID:0x%08x "; // idb
const char aNid0x08x[] = "NID:0x%08x "; // idb
const char aNid0x08x_2[] = "NID:0x%08x "; // idb
char aNid0x08x_3[11] = "NID:0x%08x "; // weak
char byte_4EFE58[100] =
{
  '\xBE',
  '\xCF',
  '\xBD',
  '\xC3',
  '\xC0',
  '\xE5',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xB0',
  '\xE1',
  '\xB0',
  '\xFA',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  '\xC0',
  '\xC7',
  ' ',
  '\xC5',
  '\xA9',
  '\xB1',
  '\xE2',
  '\xB0',
  '\xA1',
  ' ',
  '\xB4',
  '\xEB',
  '\xC7',
  '\xA5',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  '\xC0',
  '\xC7',
  ' ',
  '\xC5',
  '\xA9',
  '\xB1',
  '\xE2',
  '\xBA',
  '\xB8',
  '\xB4',
  '\xD9',
  ' ',
  '\xC5',
  '\xAE',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xB0',
  '\xE1',
  '\xB0',
  '\xFA',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  'I',
  'D',
  ':',
  '%',
  'd',
  ',',
  ' ',
  '\xB4',
  '\xEB',
  '\xC7',
  '\xA5',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  'I',
  'D',
  ':',
  '%',
  'd',
  '\0',
  '\0'
}; // weak
const char byte_4EFEBC = '\xBE'; // idb
const char aDWorkRylSource_22[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4EFF78 = '\xBE'; // idb
char byte_4EFFB4[52] =
{
  '\xBE',
  '\xCF',
  '\xBD',
  '\xC3',
  '\xC0',
  '\xE5',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xB5',
  '\xB7',
  '\xC0',
  '\xCC',
  ' ',
  '\xBA',
  '\xCE',
  '\xC1',
  '\xB7',
  '\xC7',
  '\xD5',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  '\xB0',
  '\xA1',
  '\xB0',
  '\xDD',
  ':',
  '%',
  'd',
  ',',
  ' ',
  '\xBC',
  '\xD2',
  '\xC1',
  '\xF6',
  '\xB1',
  '\xDD',
  ':',
  '%',
  'd',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4EFFE8[64] =
{
  '\xBE',
  '\xCF',
  '\xBD',
  '\xC3',
  '\xC0',
  '\xE5',
  ' ',
  '\xBF',
  '\xC0',
  '\xB7',
  '\xF9',
  ' ',
  ':',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xB4',
  '\xC2',
  ' ',
  '\xB4',
  '\xEB',
  '\xC7',
  '\xA5',
  ' ',
  '\xBE',
  '\xC6',
  '\xC0',
  '\xCC',
  '\xC5',
  '\xDB',
  '\xC0',
  '\xD4',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'I',
  't',
  'e',
  'm',
  'I',
  'D',
  ':',
  '%',
  'd',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char aCid0x08x_205[] = "CID:0x%08x "; // idb
char aCid0x08x_313[11] = "CID:0x%08x "; // weak
const char aNid0x08x_0[] = "NID:0x%08x "; // idb
const char aCid0x08xNpc0x0[] = "CID:0x%08x NPC 0x%08x"; // idb
const char aNid0x08xNpc[] = "NID:0x%08x NPC"; // idb
const char aDWorkRylSource_39[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_60[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_42[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F0654 = '\xB0'; // idb
const char byte_4F06B0 = '\xC1'; // idb
void *CSiegeObject::`vftable' = &CSiegeObject::`vector deleting destructor'; // weak
const char aDWorkRylSource_82[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aPid0x08x_6[] = "PID:0x%08x "; // idb
char aPid0x08x_3[11] = "PID:0x%08x "; // weak
const char aDWorkRylSource_34[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_74[] = "CID : 0x%08x, "; // idb
const char byte_4F0E78 = '\xB5'; // idb
__int16 word_4F0ED6[] = { 0 }; // weak
const char aCid0x08xCastle[] = "CID:0x%08x CastleID:0x%08x "; // idb
const char aCid0x08x_334[] = "CID:0x%08x "; // idb
char byte_4F1048[104] =
{
  '\xBC',
  '\xBA',
  ' ',
  '\xB0',
  '\xFC',
  '\xB7',
  '\xC3',
  ' ',
  '\xB8',
  '\xED',
  '\xB7',
  '\xC9',
  '\xC0',
  '\xBB',
  ' ',
  '\xBF',
  '\xE4',
  '\xC3',
  '\xBB',
  '\xC7',
  '\xD1',
  ' ',
  '\xC4',
  '\xB3',
  '\xB8',
  '\xAF',
  '\xC5',
  '\xCD',
  '\xC0',
  '\xC7',
  ' ',
  '\xB1',
  '\xE6',
  '\xB5',
  '\xE5',
  '\xB0',
  '\xA1',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'e',
  'n',
  'd',
  'e',
  'r',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  ',',
  ' ',
  'S',
  'e',
  'n',
  'd',
  'e',
  'r',
  'G',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  ',',
  ' ',
  'C',
  'm',
  'd',
  ':',
  '%',
  'd',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4F10B0 = '\xBC'; // idb
const char aDwcastleid0x08[] = "dwCastleID:0x%08x "; // idb
const char aDWorkRylSource_25[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F1268 = '\xB1'; // idb
const char byte_4F12C8 = '\xBC'; // idb
const char byte_4F1328 = '\xBC'; // idb
const char aDwcastleid0x08_0[] = "dwCastleID:0x%08x "; // idb
const char byte_4F1448 = '\xB1'; // idb
const char byte_4F14A0 = '\xB1'; // idb
char byte_4F1500[104] =
{
  '\xC1',
  '\xF8',
  '\xC1',
  '\xF6',
  ' ',
  '\xB0',
  '\xFC',
  '\xB8',
  '\xAE',
  ' ',
  '\xB1',
  '\xC7',
  '\xC7',
  '\xD1',
  ' ',
  '\xBA',
  '\xAF',
  '\xB0',
  '\xE6',
  '\xC0',
  '\xBB',
  ' ',
  '\xBF',
  '\xE4',
  '\xC3',
  '\xBB',
  '\xC7',
  '\xD1',
  ' ',
  '\xC4',
  '\xB3',
  '\xB8',
  '\xAF',
  '\xC5',
  '\xCD',
  '\xC0',
  '\xC7',
  ' ',
  '\xB1',
  '\xE6',
  '\xB5',
  '\xE5',
  '\xB0',
  '\xA1',
  ' ',
  '\xC1',
  '\xB8',
  '\xC0',
  '\xE7',
  '\xC7',
  '\xCF',
  '\xC1',
  '\xF6',
  ' ',
  '\xBE',
  '\xCA',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'S',
  'e',
  'n',
  'd',
  'e',
  'r',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  ',',
  ' ',
  'S',
  'e',
  'n',
  'd',
  'e',
  'r',
  'G',
  'I',
  'D',
  ':',
  '0',
  'x',
  '%',
  '0',
  '8',
  'x',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4F1568 = '\xC1'; // idb
const char byte_4F15F8 = '\xC1'; // idb
const char aDwcampid0x08x[] = "dwCampID:0x%08x "; // idb
const char aCid0x08xCampid[] = "CID:0x%08x CampID:0x%08x "; // idb
const char aCid0x08x_10[] = "CID:0x%08x "; // idb
const char aCid0x08x_57[] = "CID:0x%08x "; // idb
const char byte_4F1780 = '\xC1'; // idb
const char aDwcampid0x08x_0[] = "dwCampID:0x%08x "; // idb
const char byte_4F1898 = '\xB1'; // idb
const char aCid0x08xArmsid[] = "CID:0x%08x ArmsID:0x%08x "; // idb
const char byte_4F1930 = '\xB0'; // idb
const char aDwarmsid0x08x[] = "dwArmsID:0x%08x "; // idb
char aDwarmsid0x08x_0[16] = "dwArmsID:0x%08x "; // weak
const char byte_4F1A98 = '\xB1'; // idb
const char aDWorkRylSource_93[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_325[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_58[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_259[] = "CID:0x%08x "; // idb
const char aCid0x08x_237[] = "CID:0x%08x "; // idb
const char aCid0x08x_303[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_31[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_235[] = "CID:0x%08x "; // idb
const char aCid0x08x_135[] = "CID:0x%08x "; // idb
const char *const szNoRemove_84 = "NoRemove"; // idb
char aCid0x08x_152[11] = "CID:0x%08x "; // weak
char aCid0x08x_21[11] = "CID:0x%08x "; // weak
char aCid0x08x_167[11] = "CID:0x%08x "; // weak
char aCid0x08x_187[11] = "CID:0x%08x "; // weak
const char aCid0x08x_273[] = "CID:0x%08x "; // idb
char aCid0x08x_115[11] = "CID:0x%08x "; // weak
char aCid0x08x_262[11] = "CID:0x%08x "; // weak
char aCid0x08x_216[11] = "CID:0x%08x "; // weak
const char aCid0x08x_190[] = "CID:0x%08x "; // idb
const char aCid0x08x_255[] = "CID:0x%08x "; // idb
const char aCid0x08x_41[] = "CID:0x%08x "; // idb
char aCid0x08x_295[11] = "CID:0x%08x "; // weak
char aCid0x08x_122[11] = "CID:0x%08x "; // weak
char aCid0x08x_102[11] = "CID:0x%08x "; // weak
char aCid0x08x_87[11] = "CID:0x%08x "; // weak
char aCid0x08x_316[11] = "CID:0x%08x "; // weak
char aCid0x08x_19[11] = "CID:0x%08x "; // weak
char aCid0x08x_11[11] = "CID:0x%08x "; // weak
const char aCid0x08x_253[] = "CID:0x%08x "; // idb
const char aCid0x08x_17[] = "CID:0x%08x "; // idb
char aCid0x08x_305[11] = "CID:0x%08x "; // weak
const char aCid0x08x_164[] = "CID:0x%08x "; // idb
const char aCid0x08x_83[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_91[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_173[] = "CID:0x%08x "; // idb
char aCid0x08x_331[11] = "CID:0x%08x "; // weak
char aCid0x08x_70[11] = "CID:0x%08x "; // weak
char aCid0x08x_264[11] = "CID:0x%08x "; // weak
char aCid0x08x_244[11] = "CID:0x%08x "; // weak
char aCid0x08x_186[11] = "CID:0x%08x "; // weak
char aCid0x08x_55[11] = "CID:0x%08x "; // weak
char aCid0x08x_50[11] = "CID:0x%08x "; // weak
const char byte_4F2CC4 = '\xBE'; // idb
const char aAdmin0x08x[] = "Admin:0x%08x "; // idb
const char aCid0x08x_36[] = "CID:0x%08x "; // idb
const char aCid0x08x_33[] = "CID:0x%08x "; // idb
const char byte_4F2E20 = '\xB0'; // idb
const char byte_4F2E68 = '\xB0'; // idb
const char aDWorkRylSource_57[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F2F78 = '\xBE'; // idb
char aCid0x08x_149[11] = "CID:0x%08x "; // weak
char aCid0x08x_161[11] = "CID:0x%08x "; // weak
const char aCid0x08x_208[] = "CID:0x%08x "; // idb
char aCid0x08x_155[11] = "CID:0x%08x "; // weak
const char aCid0x08x_8[] = "CID:0x%08x "; // idb
const char aCid0x08x_217[] = "CID:0x%08x "; // idb
const char aCid0x08x_112[] = "CID:0x%08x "; // idb
const char byte_4F3224 = '\xC4'; // idb
const char aCid0x08x[] = "CID:0x%08x "; // idb
const char a0x08x[] = "0x%08x "; // idb
const char aGid0x08x_13[] = "GID:0x%08x "; // idb
const char szSenderName = '\xBF'; // idb
char aGid0x08x_17[11] = "GID:0x%08x "; // weak
const char aCid0x08x_6[] = "CID:0x%08x "; // idb
const char aCid0x08x_193[] = "CID:0x%08x "; // idb
const char aCid0x08x_78[] = "CID:0x%08x "; // idb
const char aCid0x08x_320[] = "CID:0x%08x "; // idb
const char aCid0x08x_212[] = "CID:0x%08x "; // idb
char aCid0x08x_108[11] = "CID:0x%08x "; // weak
char aCid0x08x_306[11] = "CID:0x%08x "; // weak
const char aCid0x08x_88[] = "CID:0x%08x "; // idb
char aCid0x08x_184[11] = "CID:0x%08x "; // weak
char aCid0x08x_337[11] = "CID:0x%08x "; // weak
const char aCid0x08x_169[] = "CID:0x%08x "; // idb
char aCid0x08x_225[11] = "CID:0x%08x "; // weak
char aCid0x08x_142[11] = "CID:0x%08x "; // weak
char aReference[9] = "Reference"; // weak
const char byte_4F386C = '\xB8'; // idb
const char strSenderName = '\xC5'; // idb
const char aCid0x08x_213[] = "CID:0x%08x "; // idb
char aCid0x08x_201[11] = "CID:0x%08x "; // weak
const char aCid0x08x_160[] = "CID:0x%08x "; // idb
const char byte_4F391C = '\xC6'; // idb
const char aCid0x08x_140[] = "CID:0x%08x "; // idb
const char aCid0x08x_312[] = "CID:0x%08x "; // idb
char aCid0x08x_156[11] = "CID:0x%08x "; // weak
const char szSenderName_In = '\xB7'; // idb
const char aCid0x08x_51[] = "CID:0x%08x "; // idb
char aUid0x08x[11] = "UID:0x%08x "; // weak
const char byte_4F3A50 = '\xC6'; // idb
char aCid0x08x_282[11] = "CID:0x%08x "; // weak
char aCid0x08x_277[11] = "CID:0x%08x "; // weak
const char aCid0x08x_197[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_21[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F3C90 = '\xB0'; // idb
const char aCid0x08x_42[] = "CID:0x%08x "; // idb
char aCid0x08x_260[11] = "CID:0x%08x "; // weak
char aCid0x08x_5[11] = "CID:0x%08x "; // weak
char aCid0x08x_242[11] = "CID:0x%08x "; // weak
char aCid0x08x_238[11] = "CID:0x%08x "; // weak
char aCid0x08x_239[11] = "CID:0x%08x "; // weak
const char aCid0x08xRecver[] = "CID:0x%08x (Recver), SenderCID:0x%08x "; // idb
const char aCid0x08x_272[] = "CID:0x%08x "; // idb
const char aCid0x08x_174[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_18[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_150[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_63[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_106[] = "CID:0x%08x "; // idb
const char aCid0x08x_15[] = "CID:0x%08x "; // idb
const char aCid0x08x_307[] = "CID:0x%08x "; // idb
char aCid0x08x_124[11] = "CID:0x%08x "; // weak
const char aCid0x08x_321[] = "CID:0x08x "; // idb
char aCid0x08x_301[10] = "CID:0x08x "; // weak
char aCid0x08x_151[10] = "CID:0x08x "; // weak
const char aCid0x08x_275[] = "CID:0x%08x "; // idb
const char aCid0x08x_326[] = "CID:0x%08x "; // idb
char aCid0x08x_283[11] = "CID:0x%08x "; // weak
char aCid0x08x_210[11] = "CID:0x%08x "; // weak
const char aCid0x08x_48[] = "CID:0x%08x "; // idb
const char aCid0x08x_127[] = "CID:0x%08x "; // idb
char aCid0x08x_339[11] = "CID:0x%08x "; // weak
const char aDWorkRylSource_9[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_236[] = "CID:0x%08x "; // idb
char aCid0x08x_329[11] = "CID:0x%08x "; // weak
const char aCid0x08x_308[] = "CID:0x%08x "; // idb
char aCid0x08xSwap_0[15] = "CID:0x%08x Swap"; // weak
const char aCid0x08xSwap[] = "CID:0x%08x Swap"; // idb
const char aCid0x08x_97[] = "CID:0x%08x "; // idb
const char aCid0x08x_287[] = "CID:0x%08x "; // idb
const char aCid0x08x_109[] = "CID:0x%08x "; // idb
const char aCid0x08x_3[] = "CID:0x%08x "; // idb
const char aCid0x08x_141[] = "CID:0x%08x "; // idb
const char aCid0x08x_222[] = "CID:0x%08x "; // idb
const char aCid0x08x_93[] = "CID:0x%08x "; // idb
const char aCid0x08xDbagen_2[] = "CID:0x%08x DBAgent"; // idb
char aCid0x08xDbagen_0[18] = "CID:0x%08x DBAgent"; // weak
const char aCid0x08x_300[] = "CID:0x%08x "; // idb
const char aCid0x08x_158[] = "CID:0x%08x "; // idb
const char aCid0x08x_116[] = "CID:0x%08x "; // idb
const char aCid0x08x_226[] = "CID:0x%08x "; // idb
const char aCid0x08x2d0x04[] = "CID:0x%08x (%2d, 0x%04x)"; // idb
const char aCid0x08x_159[] = "CID:0x%08x "; // idb
char aCid0x08x_4[11] = "CID:0x%08x "; // weak
const char aCid0x08x_214[] = "CID:0x%08x "; // idb
const char aCid0x08x_185[] = "CID:0x%08x "; // idb
char aCid0x08x_71[11] = "CID:0x%08x "; // weak
char aCid0x08x_288[11] = "CID:0x%08x "; // weak
const char aCid0x08x_67[] = "CID:0x%08x "; // idb
char aCid0x08x_171[11] = "CID:0x%08x "; // weak
const char aCid0x08x_286[] = "CID:0x%08x "; // idb
const char aCid0x08x_241[] = "CID:0x%08x "; // idb
const char aCid0x08x_219[] = "CID:0x%08x "; // idb
const char aCid0x08x_132[] = "CID:0x%08x "; // idb
const char aUidDCid0x08xIp[] = "UID:%d/CID:0x%08x IP:%d.%d.%d.%d Name:%s "; // idb
const char aDWorkRylSource_80[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F50C8 = '\xBE'; // idb
const char aCid0x08x_266[] = "CID:0x%08x "; // idb
const char aCid0x08x_243[] = "CID:0x%08x "; // idb
const char aUidDCid0x08xNa_1[] = "/UID:%d/CID:0x%08x/Name:%s/IP:%d.%d.%d.%d/"; // idb
const char byte_4F537C = '\xB0'; // idb
const char byte_4F5398 = '\xC5'; // idb
const char aUidDCid0x08xNa_0[] = "/UID:%d/CID:0x%08x/Name:%s/IP:%d.%d.%d.%d/"; // idb
const char byte_4F54C8 = '\xB8'; // idb
const char aDWorkRylSource[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
char byte_4F5598[28] =
{
  '\xB8',
  '\xE2',
  '\xB9',
  '\xF6',
  ' ',
  '\xC3',
  '\xDF',
  '\xB0',
  '\xA1',
  '\xBF',
  '\xA1',
  ' ',
  '\xBC',
  '\xBA',
  '\xB0',
  '\xF8',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4F55D0[32] =
{
  '\xB8',
  '\xE2',
  '\xB9',
  '\xF6',
  ' ',
  '\xC0',
  '\xFC',
  '\xBA',
  '\xCE',
  ' ',
  '\xC1',
  '\xA6',
  '\xB0',
  '\xC5',
  '\xBF',
  '\xA1',
  ' ',
  '\xBC',
  '\xBA',
  '\xB0',
  '\xF8',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '\0',
  '\0',
  '\0'
}; // weak
char byte_4F5610[28] =
{
  '\xB8',
  '\xE2',
  '\xB9',
  '\xF6',
  ' ',
  '\xC1',
  '\xA6',
  '\xB0',
  '\xC5',
  '\xBF',
  '\xA1',
  ' ',
  '\xBC',
  '\xBA',
  '\xB0',
  '\xF8',
  '\xC7',
  '\xDF',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char aDWorkRylSource_69[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aMPdwoutputstat[] = "m_pdwOutputState"; // idb
char aMPdwinputs[11] = "m_pdwInputs"; // weak
const char aDWorkRylSource_77[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F59CC = '\xC6'; // idb
const char aDWorkRylSource_90[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F5F68 = '\xB9'; // idb
const char byte_4F5FB0 = '\xBE'; // idb
const char aDWorkRylSource_44[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F6240 = '\xBA'; // idb
void *CLotteryEvent::`vftable' = &CLotteryEvent::`vector deleting destructor'; // weak
const char byte_4F6288 = '\xBE'; // idb
char byte_4F62E0[64] =
{
  '\xBA',
  '\xB9',
  '\xB1',
  '\xC7',
  ' ',
  '\xC0',
  '\xCC',
  '\xBA',
  '\xA5',
  '\xC6',
  '\xAE',
  ' ',
  '\xBD',
  '\xBA',
  '\xC5',
  '\xA9',
  '\xB8',
  '\xB3',
  '\xC6',
  '\xAE',
  '\xBF',
  '\xA1',
  ' ',
  '\xBE',
  '\xCB',
  '\xB8',
  '\xC2',
  '\xC0',
  '\xBA',
  ' ',
  '\xBC',
  '\xBD',
  '\xBC',
  '\xC7',
  '\xC0',
  '\xCC',
  ' ',
  '\xBE',
  '\xF8',
  '\xBD',
  '\xC0',
  '\xB4',
  '\xCF',
  '\xB4',
  '\xD9',
  '.',
  ' ',
  'I',
  't',
  'e',
  'm',
  'I',
  'D',
  ':',
  '%',
  'd',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0',
  '\0'
}; // weak
const char byte_4F6320 = '\xBE'; // idb
const char byte_4F638C = '\xBA'; // idb
const char byte_4F63BC = '\xBA'; // idb
const char aDWorkRylSource_54[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
GUID CLSID_MSDAORA = { 3905703102u, 65023u, 4560u, { 184u, 101u, 0u, 160u, 201u, 8u, 28u, 29u } };
GUID CLSID_SQLOLEDB = { 209711468u, 14563u, 4560u, { 151u, 171u, 0u, 192u, 79u, 194u, 173u, 152u } };
GUID CLSID_MSDASQL = { 3367314123u, 23795u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } };
_GUID DBGUID_DBSQL = { 3367313915u, 23795u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } }; // idb
_GUID DBPROPSET_ROWSET = { 3367314110u, 23795u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } }; // idb
_GUID DBPROPSET_DBINIT = { 3367314108u, 23795u, 4558u, { 173u, 229u, 0u, 170u, 0u, 68u, 119u, 61u } }; // idb
tagDBID DB_NULLID = { { { 0u, 0u, 0u, { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u } } }, 0u, { NULL } }; // idb
void *OleDB::`vftable' = &OleDB::`scalar deleting destructor'; // weak
const char aIdbinitialize[] = "IDBInitialize "; // idb
const char aDWorkRylSource_85[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aIdbinitialize_0[] = "IDBInitialize "; // idb
const char aCreatesession[] = "CreateSession "; // idb
const char byte_4F6A58 = '\xBC'; // idb
const char byte_4F6AF8 = '\xB0'; // idb
const char asc_4F6B40[] = " "; // idb
const char byte_4F6B5C = '\xB7'; // idb
const char szGuildDestroyFileName[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_139[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_59[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_233[] = "CID:0x%08x "; // idb
const POS RespawnPos_0[2][2] =
{
  { { 2165.0, 1135.0, 1005.0 }, { 1727.0, 1135.0, 1005.0 } },
  { { 2119.0, 1132.0, 1841.0 }, { 1683.0, 1132.0, 1841.0 } }
}; // idb
const char aDWorkRylSource_87[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_128[] = "CID:0x%08x "; // idb
const unsigned __int8 DefaultMaxCharacterNumOfNation[2] = { 60u, 120u }; // idb
const unsigned __int16 DefaultTargetScore[2] = { 500u, 6u }; // idb
const unsigned __int8 DefaultLimitMin[2] = { 25u, 50u }; // idb
const unsigned __int8 DefaultRestMin[2] = { 5u, 10u }; // idb
const char aDWorkRylSource_2[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_63[] = "CID:0x%08x "; // idb
const char aCid0x08[] = "CID:0X%08 "; // idb
const char aDWorkRylSource_3[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08x_52[] = "CID:0x%08x "; // idb
const char aCid0x08x_91[] = "CID:0x%08x "; // idb
const char aCid0x08x_153[] = "CID:0x%08x "; // idb
const char aCid0x08x_249[] = "CID:0x%08x "; // idb
const char aDWorkRylSource_14[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char byte_4F75C4 = '\xC4'; // idb
const char aDWorkRylSource_38[] = "D:\\Work\\RYL\\Source\\v.538] "; // idb
const char aCid0x08_0[] = "CID:0X%08 "; // idb
_BYTE CXORCrypt::BitFields[800] =
{
  -126,
  83,
  67,
  76,
  43,
  13,
  55,
  -41,
  -39,
  -40,
  27,
  109,
  -96,
  -61,
  43,
  -18,
  69,
  -120,
  26,
  -90,
  24,
  29,
  -99,
  56,
  42,
  85,
  3,
  29,
  -51,
  -90,
  115,
  7,
  -19,
  -115,
  -59,
  -37,
  -93,
  -67,
  -74,
  -43,
  52,
  -75,
  -78,
  61,
  125,
  67,
  -116,
  -64,
  33,
  37,
  -51,
  -74,
  83,
  118,
  -50,
  93,
  -44,
  -121,
  -54,
  -124,
  -127,
  -53,
  94,
  4,
  -70,
  105,
  62,
  101,
  -34,
  33,
  -118,
  99,
  98,
  113,
  -112,
  -121,
  10,
  82,
  40,
  68,
  -93,
  73,
  -36,
  -22,
  9,
  -73,
  1,
  -92,
  -95,
  17,
  17,
  -114,
  128,
  53,
  91,
  -35,
  56,
  -43,
  78,
  54,
  12,
  -94,
  -69,
  5,
  54,
  87,
  46,
  -104,
  -66,
  -120,
  60,
  40,
  67,
  99,
  -96,
  -23,
  -31,
  109,
  81,
  -53,
  77,
  98,
  -124,
  67,
  -119,
  -57,
  -119,
  -125,
  101,
  41,
  83,
  -107,
  124,
  -64,
  -95,
  12,
  -37,
  -41,
  4,
  -40,
  106,
  -47,
  115,
  29,
  33,
  103,
  -122,
  -115,
  -92,
  -96,
  52,
  -67,
  49,
  32,
  97,
  14,
  -23,
  99,
  -76,
  -64,
  -57,
  54,
  27,
  65,
  35,
  -100,
  -47,
  -116,
  37,
  83,
  66,
  46,
  69,
  109,
  66,
  123,
  78,
  91,
  -21,
  36,
  51,
  116,
  82,
  40,
  -58,
  42,
  -61,
  22,
  96,
  -91,
  69,
  53,
  -37,
  -102,
  84,
  -105,
  -30,
  -18,
  -101,
  -34,
  -32,
  -61,
  -124,
  65,
  -19,
  69,
  76,
  105,
  -39,
  40,
  85,
  39,
  -114,
  58,
  60,
  -114,
  -124,
  -105,
  20,
  -26,
  88,
  81,
  38,
  13,
  -30,
  -98,
  102,
  124,
  13,
  1,
  125,
  23,
  76,
  8,
  -35,
  -105,
  28,
  123,
  -50,
  93,
  84,
  55,
  124,
  12,
  -114,
  39,
  122,
  120,
  46,
  -26,
  109,
  37,
  98,
  98,
  -104,
  32,
  46,
  35,
  21,
  97,
  125,
  -105,
  80,
  7,
  32,
  122,
  4,
  41,
  98,
  -112,
  107,
  -23,
  -26,
  34,
  114,
  56,
  86,
  -55,
  6,
  46,
  59,
  71,
  8,
  45,
  33,
  66,
  7,
  105,
  74,
  87,
  -117,
  121,
  -25,
  86,
  39,
  35,
  36,
  -123,
  71,
  116,
  117,
  -123,
  -87,
  -21,
  16,
  -53,
  23,
  -123,
  75,
  94,
  32,
  120,
  -48,
  125,
  -122,
  94,
  20,
  126,
  100,
  80,
  105,
  82,
  74,
  -67,
  -116,
  -101,
  -42,
  99,
  -67,
  38,
  -122,
  50,
  -107,
  -92,
  2,
  -101,
  1,
  20,
  73,
  120,
  -120,
  87,
  58,
  1,
  74,
  -68,
  80,
  -51,
  49,
  57,
  113,
  48,
  91,
  -100,
  77,
  33,
  103,
  -126,
  -24,
  92,
  102,
  16,
  -87,
  125,
  -46,
  54,
  -30,
  -79,
  40,
  32,
  -43,
  -25,
  -43,
  14,
  -44,
  12,
  44,
  119,
  128,
  14,
  -90,
  55,
  -66,
  97,
  -83,
  -42,
  23,
  101,
  19,
  112,
  -82,
  64,
  59,
  82,
  -18,
  83,
  -124,
  -21,
  4,
  13,
  73,
  -116,
  119,
  -64,
  -64,
  100,
  84,
  11,
  34,
  -67,
  -126,
  -109,
  -102,
  35,
  -115,
  -28,
  -56,
  -99,
  -77,
  80,
  68,
  -79,
  -30,
  -98,
  21,
  122,
  -95,
  12,
  36,
  -29,
  30,
  10,
  10,
  115,
  106,
  -91,
  -117,
  58,
  83,
  51,
  -80,
  -26,
  -73,
  81,
  112,
  -38,
  -42,
  41,
  -86,
  16,
  -75,
  -118,
  56,
  55,
  78,
  122,
  59,
  116,
  123,
  99,
  65,
  124,
  33,
  101,
  94,
  38,
  -107,
  68,
  117,
  -93,
  116,
  -35,
  -76,
  51,
  -98,
  84,
  60,
  -107,
  94,
  52,
  16,
  25,
  67,
  100,
  120,
  43,
  -90,
  96,
  125,
  -51,
  -87,
  40,
  -72,
  -123,
  14,
  102,
  -57,
  60,
  40,
  -36,
  -95,
  77,
  96,
  -101,
  -57,
  -45,
  116,
  -109,
  -26,
  -61,
  -105,
  118,
  18,
  -92,
  -53,
  -71,
  34,
  81,
  -71,
  121,
  92,
  104,
  -37,
  -26,
  89,
  87,
  -107,
  -51,
  -82,
  -54,
  103,
  -72,
  55,
  -112,
  -70,
  84,
  -104,
  -107,
  115,
  -114,
  71,
  -63,
  64,
  -70,
  128,
  38,
