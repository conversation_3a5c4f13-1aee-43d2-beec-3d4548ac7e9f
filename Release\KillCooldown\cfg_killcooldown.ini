[KillCooldown]
; Enable/disable the plugin (1 = enabled, 0 = disabled)
Enabled=1
; Cooldown time in milliseconds (default: 300000 = 5 minutes)
CooldownTime=300000
; Save interval in milliseconds (default: 30000 = 30 seconds)
SaveInterval=30000
; Ignore LatestEnemy array to avoid crashes (1 = ignore, 0 = use)
IgnoreLatestEnemy=1
; Enable strict concurrency protection (1 = enabled, 0 = disabled)
StrictConcurrencyProtection=1

[THREAT_TIMEOUT]
; Enable/disable the threat timeout system (1 = enabled, 0 = disabled)
EnableThreatTimeout=1
; Time in milliseconds after which threats expire (10 seconds = 10000ms)
ThreatTimeoutMs=10000
; How often to clean expired threats in milliseconds (2 seconds = 2000ms)
CleanupIntervalMs=2000
