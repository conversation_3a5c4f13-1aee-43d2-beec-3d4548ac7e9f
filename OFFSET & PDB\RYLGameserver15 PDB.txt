//----- (00491410) --------------------------------------------------------
char __thiscall CDelimitedFile::GotoColumn(CDelimitedFile *this, unsigned int nColumn)
{
  unsigned int m_dwColumnCount; // ecx
  unsigned int m_dwColumn; // eax
  unsigned int v6; // ecx
  unsigned int v7; // eax

  if ( (nColumn & 0x80000000) != 0 )
    return 0;
  m_dwColumnCount = this->m_dwColumnCount;
  if ( nColumn > m_dwColumnCount )
    return 0;
  m_dwColumn = this->m_dwColumn;
  if ( m_dwColumn != nColumn )
  {
    if ( !nColumn )
    {
      this->m_dwColumn = 0;
      strtok(this->m_szLine, this->m_szDelimiter);
      return 1;
    }
    if ( m_dwColumn < m_dwColumnCount )
    {
      do
      {
        if ( nColumn == this->m_dwColumn )
          break;
        strtok(0, this->m_szDelimiter);
        v6 = this->m_dwColumn + 1;
        this->m_dwColumn = v6;
      }
      while ( v6 < this->m_dwColumnCount );
    }
    if ( this->m_dwColumn == this->m_dwColumnCount )
    {
      qmemcpy(this->m_szLine, this->m_szBackupLine, sizeof(this->m_szLine));
      strtok(this->m_szLine, this->m_szDelimiter);
      this->m_dwColumn = 1;
      if ( nColumn > 1 && nColumn != this->m_dwColumn )
      {
        do
        {
          strtok(0, this->m_szDelimiter);
          v7 = this->m_dwColumn + 1;
          this->m_dwColumn = v7;
        }
        while ( nColumn > v7 );
      }
    }
  }
  return 1;
}

//----- (004914F0) --------------------------------------------------------
std::string *__thiscall std::string::replace(
        std::string *this,
        unsigned int _Off,
        unsigned int _N0,
        const std::string *_Right,
        unsigned int _Roff,
        unsigned int _Count)
{
  unsigned int Mysize; // ecx
  unsigned int v9; // esi
  unsigned int v10; // edx
  unsigned int v11; // eax
  unsigned int v12; // ebx
  unsigned int Myres; // eax
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v15; // ebx
  unsigned int v16; // eax
  std::string::_Bxty *v17; // ecx
  std::string::_Bxty *v18; // eax
  std::string::_Bxty *v19; // edx
  std::string::_Bxty *v20; // eax
  unsigned int v21; // eax
  std::string::_Bxty *v22; // ecx
  std::string::_Bxty *v23; // eax
  unsigned int v24; // eax
  std::string::_Bxty *v25; // ecx
  unsigned __int8 *v26; // ecx
  unsigned __int8 *v27; // eax
  unsigned int v28; // eax
  std::string::_Bxty *v29; // ecx
  std::string::_Bxty *v30; // eax
  unsigned int v31; // eax
  std::string::_Bxty *v32; // ecx
  unsigned int v33; // eax
  std::string::_Bxty *Ptr; // ecx
  std::string::_Bxty *v35; // eax
  unsigned int v36; // ecx
  std::string::_Bxty *v37; // eax
  std::string::_Bxty *v38; // edx
  std::string::_Bxty *v39; // ecx
  std::string::_Bxty *v40; // eax
  unsigned int v41; // eax
  std::string::_Bxty *v42; // ecx
  std::string::_Bxty *v43; // eax
  unsigned int v44; // eax
  std::string::_Bxty *v45; // ecx
  bool v46; // cf
  unsigned int v48; // [esp-8h] [ebp-14h]
  unsigned int _Offa; // [esp+10h] [ebp+4h]
  unsigned int _Nm; // [esp+14h] [ebp+8h]
  std::string::_Bxty *_Newsize; // [esp+18h] [ebp+Ch]

  if ( this->_Mysize < _Off || _Right->_Mysize < _Roff )
    std::_String_base::_Xran((std::_String_base *)this);
  Mysize = this->_Mysize;
  v9 = _N0;
  if ( this->_Mysize - _Off < _N0 )
    v9 = this->_Mysize - _Off;
  v10 = _Count;
  if ( _Right->_Mysize - _Roff < _Count )
  {
    v10 = _Right->_Mysize - _Roff;
    _Count = v10;
  }
  if ( -1 - v10 <= Mysize - v9 )
    std::_String_base::_Xlen((std::_String_base *)this);
  v11 = this->_Mysize;
  v12 = _Count + v11 - v9;
  _Offa = v11 - _Off - v9;
  _Nm = v12;
  if ( v11 < v12 )
  {
    if ( v12 == -1 )
      std::_String_base::_Xlen((std::_String_base *)this);
    Myres = this->_Myres;
    if ( Myres >= v12 )
    {
      if ( !v12 )
      {
        this->_Mysize = 0;
        if ( Myres < 0x10 )
          p_Bx = &this->_Bx;
        else
          p_Bx = (std::string::_Bxty *)this->_Bx._Ptr;
        p_Bx->_Buf[0] = 0;
      }
    }
    else
    {
      std::string::_Copy(this, v12, this->_Mysize);
    }
  }
  v15 = &this->_Bx;
  if ( this == _Right )
  {
    if ( _Count > v9 )
    {
      if ( _Roff > _Off )
      {
        v33 = this->_Myres;
        if ( _Off + v9 <= _Roff )
        {
          if ( v33 < 0x10 )
            Ptr = &this->_Bx;
          else
            Ptr = (std::string::_Bxty *)v15->_Ptr;
          if ( v33 < 0x10 )
            v35 = &this->_Bx;
          else
            v35 = (std::string::_Bxty *)v15->_Ptr;
          memmove((unsigned __int8 *)&v35->_Buf[_Off + _Count], (unsigned __int8 *)&Ptr->_Buf[_Off + v9], _Offa);
          v36 = this->_Myres;
          if ( v36 < 0x10 )
            v37 = &this->_Bx;
          else
            v37 = (std::string::_Bxty *)v15->_Ptr;
          if ( v36 < 0x10 )
            v38 = &this->_Bx;
          else
            v38 = (std::string::_Bxty *)v15->_Ptr;
          memmove((unsigned __int8 *)&v38->_Buf[_Off], (unsigned __int8 *)&v37->_Buf[_Roff - v9 + _Count], _Count);
          goto LABEL_94;
        }
        if ( v33 < 0x10 )
          v39 = &this->_Bx;
        else
          v39 = (std::string::_Bxty *)v15->_Ptr;
        if ( v33 < 0x10 )
          v40 = &this->_Bx;
        else
          v40 = (std::string::_Bxty *)v15->_Ptr;
        memmove((unsigned __int8 *)&v40->_Buf[_Off], (unsigned __int8 *)&v39->_Buf[_Roff], v9);
        v41 = this->_Myres;
        if ( v41 < 0x10 )
          v42 = &this->_Bx;
        else
          v42 = (std::string::_Bxty *)v15->_Ptr;
        if ( v41 < 0x10 )
          v43 = &this->_Bx;
        else
          v43 = (std::string::_Bxty *)v15->_Ptr;
        memmove((unsigned __int8 *)&v43->_Buf[_Off + _Count], (unsigned __int8 *)&v42->_Buf[_Off + v9], _Offa);
        v44 = this->_Myres;
        if ( v44 < 0x10 )
          v45 = &this->_Bx;
        else
          v45 = (std::string::_Bxty *)v15->_Ptr;
        if ( v44 < 0x10 )
          _Newsize = &this->_Bx;
        else
          _Newsize = (std::string::_Bxty *)v15->_Ptr;
        v48 = _Count - v9;
        v26 = (unsigned __int8 *)&v45->_Buf[_Roff + _Count];
        v27 = (unsigned __int8 *)&_Newsize->_Buf[_Off + v9];
      }
      else
      {
        v28 = this->_Myres;
        if ( v28 < 0x10 )
          v29 = &this->_Bx;
        else
          v29 = (std::string::_Bxty *)v15->_Ptr;
        if ( v28 < 0x10 )
          v30 = &this->_Bx;
        else
          v30 = (std::string::_Bxty *)v15->_Ptr;
        memmove((unsigned __int8 *)&v30->_Buf[_Off + _Count], (unsigned __int8 *)&v29->_Buf[_Off + v9], _Offa);
        v31 = this->_Myres;
        if ( v31 < 0x10 )
          v32 = &this->_Bx;
        else
          v32 = (std::string::_Bxty *)v15->_Ptr;
        v48 = _Count;
        v26 = (unsigned __int8 *)&v32->_Buf[_Roff];
        if ( v31 < 0x10 )
          v27 = (unsigned __int8 *)&v15->_Buf[_Off];
        else
          v27 = (unsigned __int8 *)&v15->_Ptr[_Off];
      }
    }
    else
    {
      v21 = this->_Myres;
      if ( v21 < 0x10 )
        v22 = &this->_Bx;
      else
        v22 = (std::string::_Bxty *)v15->_Ptr;
      if ( v21 < 0x10 )
        v23 = &this->_Bx;
      else
        v23 = (std::string::_Bxty *)v15->_Ptr;
      memmove((unsigned __int8 *)&v23->_Buf[_Off], (unsigned __int8 *)&v22->_Buf[_Roff], _Count);
      v24 = this->_Myres;
      if ( v24 < 0x10 )
        v25 = &this->_Bx;
      else
        v25 = (std::string::_Bxty *)v15->_Ptr;
      v48 = _Offa;
      v26 = (unsigned __int8 *)&v25->_Buf[_Off + v9];
      if ( v24 < 0x10 )
        v27 = (unsigned __int8 *)&v15->_Buf[_Off + _Count];
      else
        v27 = (unsigned __int8 *)&v15->_Ptr[_Off + _Count];
    }
    memmove(v27, v26, v48);
  }
  else
  {
    v16 = this->_Myres;
    if ( v16 < 0x10 )
      v17 = &this->_Bx;
    else
      v17 = (std::string::_Bxty *)v15->_Ptr;
    if ( v16 < 0x10 )
      v18 = &this->_Bx;
    else
      v18 = (std::string::_Bxty *)v15->_Ptr;
    memmove((unsigned __int8 *)&v18->_Buf[_Off + _Count], (unsigned __int8 *)&v17->_Buf[_Off + v9], _Offa);
    if ( _Right->_Myres < 0x10 )
      v19 = &_Right->_Bx;
    else
      v19 = (std::string::_Bxty *)_Right->_Bx._Ptr;
    if ( this->_Myres < 0x10 )
      v20 = &this->_Bx;
    else
      v20 = (std::string::_Bxty *)v15->_Ptr;
    qmemcpy(&v20->_Buf[_Off], &v19->_Buf[_Roff], _Count);
  }
LABEL_94:
  v46 = this->_Myres < 0x10;
  this->_Mysize = _Nm;
  if ( !v46 )
    v15 = (std::string::_Bxty *)v15->_Ptr;
  v15->_Buf[_Nm] = 0;
  return this;
}

//----- (00491840) --------------------------------------------------------
void __thiscall CDelimitedFile::Close(CDelimitedFile *this)
{
  char **Myfirst; // eax
  unsigned int v3; // ebx
  unsigned int i; // edi

  if ( this->m_fpFile )
  {
    fclose(this->m_fpFile);
    this->m_fpFile = 0;
  }
  Myfirst = this->m_ColumnNames._Myfirst;
  if ( Myfirst )
    v3 = this->m_ColumnNames._Mylast - Myfirst;
  else
    v3 = 0;
  for ( i = 0; i < v3; ++i )
    operator delete[](this->m_ColumnNames._Myfirst[i]);
  if ( this->m_ColumnNames._Myfirst )
    operator delete(this->m_ColumnNames._Myfirst);
  this->m_ColumnNames._Myfirst = 0;
  this->m_ColumnNames._Mylast = 0;
  this->m_ColumnNames._Myend = 0;
}

//----- (004918D0) --------------------------------------------------------
unsigned int __thiscall CDelimitedFile::GetSection(CDelimitedFile *this, std::string *szSectionName)
{
  CDelimitedFile::SectionInfo *Myfirst; // esi
  unsigned int Mysize; // ecx
  std::string::_Bxty *p_Bx; // ebp
  unsigned int Ptr; // ebx
  const char *v6; // edi
  unsigned int v7; // edx
  int v8; // ecx
  std::string::_Bxty *v9; // esi
  int v10; // eax
  std::vector<CDelimitedFile::SectionInfo>::iterator it; // [esp+10h] [ebp-10h]
  CDelimitedFile::SectionInfo *Mylast; // [esp+1Ch] [ebp-4h]

  Myfirst = this->m_SectionInfo._Myfirst;
  it._Myptr = Myfirst;
  Mylast = this->m_SectionInfo._Mylast;
  if ( Myfirst == Mylast )
    return 0;
  Mysize = szSectionName->_Mysize;
  p_Bx = &Myfirst->m_szSectionName._Bx;
  while ( 1 )
  {
    Ptr = (unsigned int)p_Bx[1]._Ptr;
    if ( *((_DWORD *)&p_Bx[1]._Ptr + 1) < 0x10u )
      v6 = (const char *)p_Bx;
    else
      v6 = p_Bx->_Ptr;
    v7 = szSectionName->_Mysize;
    if ( Mysize < v7 )
      v7 = Mysize;
    if ( !v7 )
      goto LABEL_17;
    v8 = v7;
    if ( v7 >= Ptr )
      v8 = (int)p_Bx[1]._Ptr;
    v9 = szSectionName->_Myres < 0x10 ? &szSectionName->_Bx : (std::string::_Bxty *)szSectionName->_Bx._Ptr;
    v10 = memcmp(v9->_Buf, v6, v8);
    Myfirst = it._Myptr;
    if ( !v10 )
    {
LABEL_17:
      if ( v7 >= Ptr && v7 == Ptr )
        return Myfirst->m_dwLine;
    }
    ++Myfirst;
    p_Bx += 2;
    it._Myptr = Myfirst;
    if ( Myfirst == Mylast )
      return 0;
    Mysize = szSectionName->_Mysize;
  }
}

//----- (004919A0) --------------------------------------------------------
void __thiscall CDelimitedFile::SectionInfo::~SectionInfo(CDelimitedFile::SectionInfo *this)
{
  if ( this->m_szSectionName._Myres >= 0x10 )
    operator delete(this->m_szSectionName._Bx._Ptr);
  this->m_szSectionName._Myres = 15;
  this->m_szSectionName._Mysize = 0;
  this->m_szSectionName._Bx._Buf[0] = 0;
}

//----- (004919D0) --------------------------------------------------------
unsigned int __thiscall CDelimitedFile::FindColumn(CDelimitedFile *this, const char *szField)
{
  char **Myfirst; // eax
  unsigned int i; // edi

  Myfirst = this->m_ColumnNames._Myfirst;
  for ( i = 0; Myfirst && i < this->m_ColumnNames._Mylast - Myfirst; ++i )
  {
    if ( !strcmp(szField, this->m_ColumnNames._Myfirst[i]) )
      return i;
  }
  return -1;
}

//----- (00491A50) --------------------------------------------------------
_BYTE *__thiscall std::string::_Construct<std::string::iterator>(std::string *this, char *a2, char *a3, int a4)
{
  char *v4; // ebx
  unsigned int Mysize; // edi
  _BYTE *result; // eax
  bool v8; // cf
  _DWORD v9[8]; // [esp+0h] [ebp-20h] BYREF

  v4 = a2;
  Mysize = this->_Mysize;
  result = (_BYTE *)(a3 - a2);
  v9[4] = v9;
  v9[3] = this;
  if ( Mysize <= a3 - a2 && (_BYTE *)this->_Myres != result )
  {
    result = (_BYTE *)std::string::_Grow(this, a3 - a2, 1);
    if ( (_BYTE)result )
    {
      v8 = this->_Myres < 0x10;
      this->_Mysize = Mysize;
      if ( v8 )
        result = this->_Bx._Buf;
      else
        result = this->_Bx._Ptr;
      result[Mysize] = 0;
    }
  }
  v9[7] = 0;
  while ( v4 != a3 )
  {
    result = std::string::append(this, (int)v4, 1u, *v4);
    ++v4;
  }
  return result;
}

//----- (00491B10) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, float *fNumber)
{
  unsigned int Column; // eax

  Column = CDelimitedFile::FindColumn(this, szField);
  if ( Column == -1 )
    return 0;
  CDelimitedFile::GotoColumn(this, Column);
  return CDelimitedFile::ReadData(this, fNumber);
}

//----- (00491B40) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, unsigned int *fNumber)
{
  unsigned int Column; // eax

  Column = CDelimitedFile::FindColumn(this, szField);
  if ( Column == -1 )
    return 0;
  CDelimitedFile::GotoColumn(this, Column);
  return CDelimitedFile::ReadData(this, fNumber);
}

//----- (00491B70) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, unsigned __int16 *iNumber)
{
  unsigned int Column; // eax

  Column = CDelimitedFile::FindColumn(this, szField);
  if ( Column == -1 )
    return 0;
  CDelimitedFile::GotoColumn(this, Column);
  return CDelimitedFile::ReadData(this, (__int16 *)iNumber);
}

//----- (00491BA0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, const char *szField, char *iNumber)
{
  unsigned int Column; // eax

  Column = CDelimitedFile::FindColumn(this, szField);
  if ( Column == -1 )
    return 0;
  CDelimitedFile::GotoColumn(this, Column);
  return CDelimitedFile::ReadData(this, iNumber);
}

//----- (00491BD0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadString(CDelimitedFile *this, const char *szField, char *szString, size_t dwSize)
{
  unsigned int Column; // eax

  Column = CDelimitedFile::FindColumn(this, szField);
  if ( Column == -1 )
    return 0;
  CDelimitedFile::GotoColumn(this, Column);
  return CDelimitedFile::ReadString(this, szString, dwSize);
}

//----- (00491C10) --------------------------------------------------------
void __thiscall std::vector<char *>::_Insert_n(
        std::vector<char *> *this,
        std::vector<char *>::iterator _Where,
        unsigned int _Count,
        char *const *_Val)
{
  char **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  char **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  char **v24; // ecx
  Quest::QuestNode **v25; // edi
  char **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<char *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (char *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<char *>::_Xlen((std::vector<CDelimitedFile::SectionInfo> *)this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (char **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (char **)std::vector<unsigned long>::_Ucopy<unsigned long *>(&Mylast[-v23], (int)Mylast, Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (char **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (char **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 491CF4: variable 'v15' is possibly undefined

//----- (00491DF0) --------------------------------------------------------
void __thiscall __noreturn std::vector<char *>::_Xlen(std::vector<CDelimitedFile::SectionInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00491E60) --------------------------------------------------------
CDelimitedFile::SectionInfo *__cdecl std::_Copy_backward_opt<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *>(
        CDelimitedFile::SectionInfo *_First,
        CDelimitedFile::SectionInfo *_Last,
        CDelimitedFile::SectionInfo *_Dest)
{
  CDelimitedFile::SectionInfo *v3; // esi
  CDelimitedFile::SectionInfo *v4; // edi
  unsigned int m_dwLine; // eax

  v3 = _Last;
  if ( _First == _Last )
    return _Dest;
  v4 = _Dest;
  do
  {
    m_dwLine = v3[-1].m_dwLine;
    --v3;
    --v4;
    v4->m_dwLine = m_dwLine;
    std::string::assign(&v4->m_szSectionName, &v3->m_szSectionName, 0, 0xFFFFFFFF);
  }
  while ( v3 != _First );
  return v4;
}

//----- (00491EA0) --------------------------------------------------------
CDelimitedFile::SectionInfo *__cdecl std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(
        CDelimitedFile::SectionInfo *_First,
        CDelimitedFile::SectionInfo *_Last,
        CDelimitedFile::SectionInfo *_Dest)
{
  CDelimitedFile::SectionInfo *v3; // esi
  int v6; // [esp+0h] [ebp-20h] BYREF
  CDelimitedFile::SectionInfo *_Next; // [esp+Ch] [ebp-14h]
  int *v8; // [esp+10h] [ebp-10h]
  int v9; // [esp+1Ch] [ebp-4h]

  v3 = _Dest;
  v8 = &v6;
  _Next = _Dest;
  v9 = 0;
  while ( _First != _Last )
  {
    LOBYTE(v9) = 1;
    if ( v3 )
    {
      v3->m_dwLine = _First->m_dwLine;
      v3->m_szSectionName._Myres = 15;
      v3->m_szSectionName._Mysize = 0;
      v3->m_szSectionName._Bx._Buf[0] = 0;
      std::string::assign(&v3->m_szSectionName, &_First->m_szSectionName, 0, 0xFFFFFFFF);
    }
    ++v3;
    LOBYTE(v9) = 0;
    ++_First;
  }
  return v3;
}

//----- (00491F70) --------------------------------------------------------
std::string *__thiscall std::string::_Replace<std::string::iterator>(
        std::string *this,
        unsigned int _Off,
        int a3,
        char *a4,
        char *a5,
        int a6)
{
  bool v7; // cf
  std::string::_Bxty *p_Bx; // edx
  unsigned int v9; // eax
  unsigned int v10; // ecx
  std::string _Right; // [esp+4h] [ebp-2Ch] BYREF
  int v13; // [esp+2Ch] [ebp-4h]

  _Right._Myres = 15;
  _Right._Mysize = 0;
  _Right._Bx._Buf[0] = 0;
  std::string::_Construct<std::string::iterator>(&_Right, a4, a5, (int)a5);
  v7 = this->_Myres < 0x10;
  v13 = 0;
  if ( v7 )
    p_Bx = &this->_Bx;
  else
    p_Bx = (std::string::_Bxty *)this->_Bx._Ptr;
  v9 = _Off;
  if ( a3 )
    v10 = a3 - _Off;
  else
    v10 = 0;
  if ( _Off )
    v9 = _Off - (_DWORD)p_Bx;
  std::string::replace(this, v9, v10, &_Right, 0, 0xFFFFFFFF);
  if ( _Right._Myres >= 0x10 )
    operator delete(_Right._Bx._Ptr);
  return this;
}

//----- (00492040) --------------------------------------------------------
void __thiscall std::vector<char *>::reserve(std::vector<char *> *this, unsigned int _Count)
{
  char **Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  char **v6; // edi
  char **v7; // eax
  char **v8; // [esp-18h] [ebp-38h]
  char **Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  char **_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x3FFFFFFF )
    std::vector<char *>::_Xlen((std::vector<CDelimitedFile::SectionInfo> *)this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(4 * _Count);
    v6 = (char **)operator new(_Counta);
    Mylast = this->_Mylast;
    v8 = this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
      (std::vector<unsigned long>::iterator)v8,
      (std::vector<unsigned long>::iterator)Mylast,
      (unsigned int *)v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (char **)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (00492110) --------------------------------------------------------
void __cdecl std::fill<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo>(
        CDelimitedFile::SectionInfo *_First,
        CDelimitedFile::SectionInfo *_Last,
        const CDelimitedFile::SectionInfo *_Val)
{
  CDelimitedFile::SectionInfo *i; // esi

  for ( i = _First; i != _Last; ++i )
  {
    i->m_dwLine = _Val->m_dwLine;
    std::string::assign(&i->m_szSectionName, &_Val->m_szSectionName, 0, 0xFFFFFFFF);
  }
}

//----- (00492150) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CDelimitedFile::SectionInfo *,unsigned int,CDelimitedFile::SectionInfo,std::allocator<CDelimitedFile::SectionInfo>>(
        CDelimitedFile::SectionInfo *_First,
        unsigned int _Count,
        const CDelimitedFile::SectionInfo *_Val)
{
  CDelimitedFile::SectionInfo *v3; // esi
  int v4; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v5; // [esp+Ch] [ebp-18h]
  CDelimitedFile::SectionInfo *_Next; // [esp+10h] [ebp-14h]
  int *v7; // [esp+14h] [ebp-10h]
  int v8; // [esp+20h] [ebp-4h]

  v3 = _First;
  v7 = &v4;
  _Next = _First;
  v8 = 0;
  while ( _Count )
  {
    v5 = (CPacketDispatch *)v3;
    LOBYTE(v8) = 1;
    if ( v3 )
    {
      v3->m_dwLine = _Val->m_dwLine;
      v3->m_szSectionName._Myres = 15;
      v3->m_szSectionName._Mysize = 0;
      v3->m_szSectionName._Bx._Buf[0] = 0;
      std::string::assign(&v3->m_szSectionName, &_Val->m_szSectionName, 0, 0xFFFFFFFF);
    }
    ++v3;
    LOBYTE(v8) = 0;
    --_Count;
  }
}

//----- (00492220) --------------------------------------------------------
char __thiscall CDelimitedFile::Open(
        CDelimitedFile *this,
        const char *szFilename,
        int nHeadLine,
        unsigned int nOpenFlags)
{
  const char *v5; // eax
  _iobuf *v6; // eax
  DWORD LastError; // eax
  int v9; // esi
  char *v10; // esi
  unsigned int m_dwColumn; // eax
  char *v12; // eax
  char *v13; // eax
  unsigned int v14; // eax
  char v15; // cl
  char **Myfirst; // edx
  unsigned int v17; // edi
  char **Mylast; // eax

  v5 = "wt";
  if ( (nOpenFlags & 0x1000) == 0 )
    v5 = "rt";
  v6 = fopen(szFilename, v5);
  this->m_fpFile = v6;
  if ( !v6 )
  {
    LastError = GetLastError();
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CDelimitedFile::Open",
      aDWorkRylSource_77,
      34,
      (char *)&byte_4F59CC,
      szFilename,
      LastError);
    return 0;
  }
  v9 = nHeadLine;
  if ( nHeadLine > -1 )
  {
    if ( nHeadLine > 0 )
    {
      do
      {
        CDelimitedFile::ReadLine(this);
        --v9;
      }
      while ( v9 );
    }
    this->m_dwColumnCount = 0;
    v10 = (char *)operator new[](0x40u);
    nOpenFlags = (unsigned int)v10;
    if ( !v10 )
      return 0;
    while ( 1 )
    {
      m_dwColumn = this->m_dwColumn;
      this->m_dwColumn = m_dwColumn + 1;
      v12 = m_dwColumn ? 0 : this->m_szLine;
      v13 = strtok(v12, this->m_szDelimiter);
      if ( !v13 || !*v13 )
        break;
      strncpy(v10, v13, 0x3Fu);
      v14 = strlen(v10);
      while ( v14 )
      {
        v15 = v10[--v14];
        if ( v15 == 13 || v15 == 10 )
          v10[v14] = 0;
      }
      v10[63] = 0;
      Myfirst = this->m_ColumnNames._Myfirst;
      if ( Myfirst )
        v17 = this->m_ColumnNames._Mylast - Myfirst;
      else
        v17 = 0;
      if ( Myfirst && v17 < this->m_ColumnNames._Myend - Myfirst )
      {
        Mylast = this->m_ColumnNames._Mylast;
        *Mylast = v10;
        this->m_ColumnNames._Mylast = Mylast + 1;
      }
      else
      {
        std::vector<char *>::_Insert_n(
          &this->m_ColumnNames,
          (std::vector<char *>::iterator)this->m_ColumnNames._Mylast,
          1u,
          (char *const *)&nOpenFlags);
      }
      ++this->m_dwColumnCount;
      v10 = (char *)operator new[](0x40u);
      nOpenFlags = (unsigned int)v10;
      if ( !v10 )
        return 0;
    }
    operator delete(v10);
  }
  return 1;
}

//----- (004923B0) --------------------------------------------------------
CDelimitedFile::SectionInfo *__thiscall std::vector<CDelimitedFile::SectionInfo>::_Ufill(
        std::vector<CDelimitedFile::SectionInfo> *this,
        CDelimitedFile::SectionInfo *_Ptr,
        unsigned int _Count,
        const CDelimitedFile::SectionInfo *_Val)
{
  std::_Uninit_fill_n<CDelimitedFile::SectionInfo *,unsigned int,CDelimitedFile::SectionInfo,std::allocator<CDelimitedFile::SectionInfo>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (004923E0) --------------------------------------------------------
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Destroy(
        std::vector<CDelimitedFile::SectionInfo> *this,
        CDelimitedFile::SectionInfo *_First,
        CDelimitedFile::SectionInfo *_Last)
{
  CDelimitedFile::SectionInfo *i; // esi

  for ( i = _First; i != _Last; ++i )
    CDelimitedFile::SectionInfo::~SectionInfo(i);
}

//----- (00492410) --------------------------------------------------------
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Insert_n(
        std::vector<CDelimitedFile::SectionInfo> *this,
        std::vector<CDelimitedFile::SectionInfo>::iterator _Where,
        unsigned int _Count,
        const CDelimitedFile::SectionInfo *_Val)
{
  CDelimitedFile::SectionInfo *Myfirst; // ecx
  unsigned int v6; // eax
  int v8; // edx
  int v9; // edx
  unsigned int v10; // eax
  int v11; // edx
  int v12; // eax
  CDelimitedFile::SectionInfo *v13; // ebx
  CDelimitedFile::SectionInfo *v14; // ecx
  int v15; // eax
  int v16; // edi
  CDelimitedFile::SectionInfo *Mylast; // eax
  CDelimitedFile::SectionInfo *v18; // edi
  CDelimitedFile::SectionInfo *v19; // edi
  CDelimitedFile::SectionInfo *v20; // [esp-18h] [ebp-64h]
  CDelimitedFile::SectionInfo *v21; // [esp-10h] [ebp-5Ch]
  CDelimitedFile::SectionInfo *v22; // [esp-Ch] [ebp-58h]
  unsigned int v23; // [esp-8h] [ebp-54h]
  int v24; // [esp+0h] [ebp-4Ch] BYREF
  std::vector<CDelimitedFile::SectionInfo> *v25; // [esp+Ch] [ebp-40h]
  CDelimitedFile::SectionInfo *_Newvec; // [esp+10h] [ebp-3Ch]
  CDelimitedFile::SectionInfo *_Ptr; // [esp+14h] [ebp-38h]
  CDelimitedFile::SectionInfo _Tmp; // [esp+18h] [ebp-34h] BYREF
  int *v29; // [esp+3Ch] [ebp-10h]
  int v30; // [esp+48h] [ebp-4h]
  unsigned int _Counta; // [esp+58h] [ebp+Ch]

  v29 = &v24;
  _Tmp.m_dwLine = _Val->m_dwLine;
  v25 = this;
  _Tmp.m_szSectionName._Myres = 15;
  _Tmp.m_szSectionName._Mysize = 0;
  _Tmp.m_szSectionName._Bx._Buf[0] = 0;
  std::string::assign(&_Tmp.m_szSectionName, &_Val->m_szSectionName, 0, 0xFFFFFFFF);
  Myfirst = this->_Myfirst;
  v30 = 0;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x7FFFFFF - v8 < _Count )
      std::vector<char *>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = this->_Mylast;
      _Newvec = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        v19 = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(
                          v19,
                          Mylast,
                          Mylast);
        std::_Copy_backward_opt<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *>(
          _Where._Myptr,
          v19,
          _Newvec);
        std::fill<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = this->_Mylast;
        LOBYTE(v30) = 3;
        std::vector<CDelimitedFile::SectionInfo>::_Ufill(this, v22, v23, &_Tmp);
        v18 = &this->_Mylast[_Count];
        this->_Mylast = v18;
        v30 = 0;
        std::fill<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo>(_Where._Myptr, &v18[-_Count], &_Tmp);
      }
    }
    else
    {
      if ( 0x7FFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (CDelimitedFile::SectionInfo *)operator new((tagHeader *)(32 * v10));
      v20 = this->_Myfirst;
      _Newvec = v13;
      _Ptr = v13;
      LOBYTE(v30) = 1;
      _Ptr = std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(
               v20,
               _Where._Myptr,
               v13);
      std::_Uninit_fill_n<CDelimitedFile::SectionInfo *,unsigned int,CDelimitedFile::SectionInfo,std::allocator<CDelimitedFile::SectionInfo>>(
        _Ptr,
        _Count,
        &_Tmp);
      v21 = this->_Mylast;
      _Ptr += _Count;
      std::_Uninit_copy<CDelimitedFile::SectionInfo *,CDelimitedFile::SectionInfo *,std::allocator<CDelimitedFile::SectionInfo>>(
        _Where._Myptr,
        v21,
        _Ptr);
      v14 = this->_Myfirst;
      if ( v14 )
        v15 = this->_Mylast - v14;
      else
        v15 = 0;
      v16 = v15 + _Count;
      if ( v14 )
      {
        std::vector<CDelimitedFile::SectionInfo>::_Destroy(this, v14, this->_Mylast);
        operator delete(this->_Myfirst);
      }
      this->_Myend = &v13[_Counta];
      this->_Mylast = &v13[v16];
      this->_Myfirst = v13;
    }
  }
  if ( _Tmp.m_szSectionName._Myres >= 0x10 )
    operator delete(_Tmp.m_szSectionName._Bx._Ptr);
}

//----- (004926E0) --------------------------------------------------------
void __thiscall std::vector<CDelimitedFile::SectionInfo>::_Tidy(std::vector<CDelimitedFile::SectionInfo> *this)
{
  CDelimitedFile::SectionInfo *Myfirst; // esi
  CDelimitedFile::SectionInfo *i; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
  {
    for ( i = this->_Mylast; Myfirst != i; ++Myfirst )
      CDelimitedFile::SectionInfo::~SectionInfo(Myfirst);
    operator delete(this->_Myfirst);
  }
  this->_Myfirst = 0;
  this->_Mylast = 0;
  this->_Myend = 0;
}

//----- (00492740) --------------------------------------------------------
void __thiscall std::vector<CDelimitedFile::SectionInfo>::push_back(
        std::vector<CDelimitedFile::SectionInfo> *this,
        const CDelimitedFile::SectionInfo *_Val)
{
  CDelimitedFile::SectionInfo *Myfirst; // edx
  unsigned int v4; // ecx
  CDelimitedFile::SectionInfo *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<CDelimitedFile::SectionInfo *,unsigned int,CDelimitedFile::SectionInfo,std::allocator<CDelimitedFile::SectionInfo>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CDelimitedFile::SectionInfo>::_Insert_n(
      this,
      (std::vector<CDelimitedFile::SectionInfo>::iterator)this->_Mylast,
      1u,
      _Val);
  }
}

//----- (004927B0) --------------------------------------------------------
void __thiscall CDelimitedFile::CDelimitedFile(CDelimitedFile *this, const char *pszDelimiter)
{
  std::vector<char *> *p_m_ColumnNames; // ecx

  p_m_ColumnNames = &this->m_ColumnNames;
  this->m_fpFile = 0;
  this->m_dwColumn = 0;
  this->m_dwColumnCount = 0;
  p_m_ColumnNames->_Myfirst = 0;
  p_m_ColumnNames->_Mylast = 0;
  p_m_ColumnNames->_Myend = 0;
  this->m_SectionInfo._Myfirst = 0;
  this->m_SectionInfo._Mylast = 0;
  this->m_SectionInfo._Myend = 0;
  std::vector<char *>::reserve(p_m_ColumnNames, 0x20u);
  strncpy(this->m_szDelimiter, pszDelimiter, 0x20u);
}

//----- (00492840) --------------------------------------------------------
void __thiscall CDelimitedFile::~CDelimitedFile(CDelimitedFile *this)
{
  CDelimitedFile::Close(this);
  std::vector<CDelimitedFile::SectionInfo>::_Tidy(&this->m_SectionInfo);
  if ( this->m_ColumnNames._Myfirst )
    operator delete(this->m_ColumnNames._Myfirst);
  this->m_ColumnNames._Myfirst = 0;
  this->m_ColumnNames._Mylast = 0;
  this->m_ColumnNames._Myend = 0;
}

//----- (00492890) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadSection(CDelimitedFile *this)
{
  CDelimitedFile *v1; // esi
  unsigned int Myres; // edx
  std::string::_Bxty *Ptr; // ecx
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v5; // eax
  std::string::_Bxty *v6; // esi
  std::string::_Bxty *v7; // eax
  std::string::_Bxty *v8; // esi
  CDelimitedFile::SectionInfo *Myfirst; // ecx
  _iobuf *m_fpFile; // [esp-Ch] [ebp-4084h]
  int v12; // [esp+8h] [ebp-4070h]
  CDelimitedFile::SectionInfo _Val; // [esp+10h] [ebp-4068h] BYREF
  std::string v15; // [esp+30h] [ebp-4048h] BYREF
  std::string _Right; // [esp+4Ch] [ebp-402Ch] BYREF
  char string[16384]; // [esp+68h] [ebp-4010h] BYREF
  int v18; // [esp+4074h] [ebp-4h]

  v1 = this;
  v12 = 1;
  std::vector<CDelimitedFile::SectionInfo>::_Tidy(&this->m_SectionInfo);
  if ( fgets(string, 0x4000, v1->m_fpFile) )
  {
    do
    {
      v15._Myres = 15;
      v15._Mysize = 0;
      v15._Bx._Buf[0] = 0;
      std::string::assign(&v15, string, strlen(string));
      Myres = v15._Myres;
      Ptr = (std::string::_Bxty *)v15._Bx._Ptr;
      v18 = 0;
      p_Bx = (std::string::_Bxty *)v15._Bx._Ptr;
      if ( v15._Myres < 0x10 )
        p_Bx = &v15._Bx;
      if ( p_Bx->_Buf[0] == 91 )
      {
        v5 = (std::string::_Bxty *)v15._Bx._Ptr;
        if ( v15._Myres < 0x10 )
          v5 = &v15._Bx;
        v6 = (std::string::_Bxty *)((char *)v5 + v15._Mysize);
        v7 = (std::string::_Bxty *)v15._Bx._Ptr;
        if ( v15._Myres < 0x10 )
          v7 = &v15._Bx;
        for ( ; v7 != v6; v7 = (std::string::_Bxty *)((char *)v7 + 1) )
        {
          if ( v7->_Buf[0] == 93 )
            break;
        }
        v8 = (std::string::_Bxty *)v15._Bx._Ptr;
        if ( v15._Myres < 0x10 )
          v8 = &v15._Bx;
        if ( v7 != (std::string::_Bxty *)&v8->_Buf[v15._Mysize] )
        {
          _Right._Myres = 15;
          _Right._Mysize = 0;
          _Right._Bx._Buf[0] = 0;
          LOBYTE(v18) = 1;
          if ( v15._Myres < 0x10 )
            Ptr = &v15._Bx;
          std::string::_Replace<std::string::iterator>(
            &_Right,
            (unsigned int)&_Right._Bx,
            (int)&_Right._Bx._Ptr,
            &Ptr->_Buf[1],
            v7->_Buf,
            v12);
          _Val.m_dwLine = v12;
          _Val.m_szSectionName._Myres = 15;
          _Val.m_szSectionName._Mysize = 0;
          _Val.m_szSectionName._Bx._Buf[0] = 0;
          std::string::assign(&_Val.m_szSectionName, &_Right, 0, 0xFFFFFFFF);
          LOBYTE(v18) = 2;
          std::vector<CDelimitedFile::SectionInfo>::push_back(&this->m_SectionInfo, &_Val);
          if ( _Val.m_szSectionName._Myres >= 0x10 )
            operator delete(_Val.m_szSectionName._Bx._Ptr);
          _Val.m_szSectionName._Myres = 15;
          _Val.m_szSectionName._Mysize = 0;
          _Val.m_szSectionName._Bx._Buf[0] = 0;
          if ( _Right._Myres >= 0x10 )
            operator delete(_Right._Bx._Ptr);
          Ptr = (std::string::_Bxty *)v15._Bx._Ptr;
          Myres = v15._Myres;
          _Right._Myres = 15;
          _Right._Mysize = 0;
          _Right._Bx._Buf[0] = 0;
        }
        v1 = this;
      }
      ++v12;
      v18 = -1;
      if ( Myres >= 0x10 )
        operator delete(Ptr);
      m_fpFile = v1->m_fpFile;
      v15._Myres = 15;
      v15._Mysize = 0;
      v15._Bx._Buf[0] = 0;
    }
    while ( fgets(string, 0x4000, m_fpFile) );
  }
  Myfirst = v1->m_SectionInfo._Myfirst;
  if ( !Myfirst || !(v1->m_SectionInfo._Mylast - Myfirst) )
    return 0;
  rewind(v1->m_fpFile);
  return 1;
}

//----- (00492AF0) --------------------------------------------------------
void __thiscall CChatPacket::CChatPacket(
        CChatPacket *this,
        unsigned int dwCID,
        const char *szMessage,
        unsigned __int16 usLength,
        PktCt::PktCtCmd ctCmd,
        unsigned __int16 usState,
        unsigned __int16 usError)
{
  char szSrcBuffer[378]; // [esp+8h] [ebp-180h] BYREF

  this->m_dwPacketSize = 0;
  if ( usLength < 0xB4u )
  {
    *(_DWORD *)&szSrcBuffer[12] = dwCID;
    *(_WORD *)&szSrcBuffer[16] = ctCmd;
    strncpy(&szSrcBuffer[18], szMessage, usLength);
    szSrcBuffer[usLength + 18] = 0;
    this->m_dwPacketSize = 378;
    if ( !PacketWrap::WrapCompress(
            (PktBase *)this->m_szBuffer,
            &this->m_dwPacketSize,
            szSrcBuffer,
            usLength + 18,
            0xDu,
            usState,
            usError) )
      this->m_dwPacketSize = 0;
  }
}

//----- (00492BC0) --------------------------------------------------------
void __cdecl _SE_Execute(struct CVirtualMachine *a1)
{
  CVirtualMachine::Execute(a1);
}

//----- (00492BD0) --------------------------------------------------------
void _SE_RegisterFunction(struct CVirtualMachine *a1, char *a2, enum eDataType a3, char *_Ptr, ...)
{
  va_list va; // [esp+14h] [ebp+14h] BYREF

  va_start(va, _Ptr);
  CVirtualMachine::RegisterFunction(a1, a2, a3, _Ptr, va);
}

//----- (00492BF0) --------------------------------------------------------
int *_SE_GetScriptFunction(int *a1, CVirtualMachine *a2, char *a3, char *_Ptr, ...)
{
  va_list va; // [esp+18h] [ebp+14h] BYREF

  va_start(va, _Ptr);
  CVirtualMachine::GetScriptFunction(a2, a1, a3, _Ptr, (int)va);
  return a1;
}

//----- (00492C30) --------------------------------------------------------
union AnyData __cdecl _SE_CallScriptFunction(struct CVirtualMachine *a1, struct ScriptFunc a2, unsigned int a3)
{
  *(_DWORD *)a1 = CVirtualMachine::CallScriptFunction(
                    (CVirtualMachine *)a2.pFunc,
                    (struct ScriptFunc)__PAIR64__(a3, a2.Type));
  return (union AnyData)a1;
}

//----- (00492C50) --------------------------------------------------------
struct CVirtualMachine *__cdecl _SE_Create(char *file)
{
  CVirtualMachine *v1; // eax
  CVirtualMachine *v2; // edi
  CThread v4; // [esp+4h] [ebp-A8h] BYREF
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator v5; // [esp+10h] [ebp-9Ch] BYREF
  CVirtualMachine *v6; // [esp+1Ch] [ebp-90h]
  char string[128]; // [esp+20h] [ebp-8Ch] BYREF
  int v8; // [esp+A8h] [ebp-4h]

  if ( !fopen(file, "r") )
    ErrorMessage2("Cannot open file : %s", file);
  v1 = (CVirtualMachine *)operator new((tagHeader *)0x38);
  v6 = v1;
  v8 = 0;
  if ( v1 )
    v2 = CVirtualMachine::CVirtualMachine(v1);
  else
    v2 = 0;
  v8 = -1;
  strcpy(string, file);
  strlwr(string);
  if ( strstr(string, ".mcf") )
  {
    CVirtualMachine::Create(v2, file);
    return v2;
  }
  else if ( strstr(string, ".gsf") )
  {
    CSyntaxTree::CSyntaxTree((CSyntaxTree *)&v4);
    v8 = 1;
    CIntermediateCode::CIntermediateCode((CIntermediateCode *)&v5);
    LOBYTE(v8) = 2;
    CSyntaxTree::Create((CSyntaxTree *)&v4, file);
    CIntermediateCode::Create((CIntermediateCode *)&v5, &v4);
    CVirtualMachine::Create(
      v2,
      (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator)&v5,
      (CAggresiveCreature *)v4.m_hThreadHandle);
    LOBYTE(v8) = 1;
    CIntermediateCode::~CIntermediateCode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **)&v5);
    v8 = -1;
    CSyntaxTree::~CSyntaxTree((CSymbolTable **)&v4);
    return v2;
  }
  else
  {
    return 0;
  }
}

//----- (00492E00) --------------------------------------------------------
void __cdecl _SE_Destroy(struct CVirtualMachine *p)
{
  if ( p )
  {
    CVirtualMachine::~CVirtualMachine(p);
    operator delete(p);
  }
}

//----- (00492E20) --------------------------------------------------------
unsigned int __cdecl CCrc32Static::BufferCrc32(const char *szBuffer, unsigned int size, unsigned int *dwCrc32)
{
  unsigned int v3; // esi
  unsigned int v5; // eax

  v3 = size;
  for ( *dwCrc32 = -1; v3; *dwCrc32 = v5 )
  {
    v5 = CCrc32Static::s_arrdwCrc32Table[*(unsigned __int8 *)szBuffer++ ^ (unsigned __int8)*dwCrc32] ^ (*dwCrc32 >> 8);
    --v3;
  }
  *dwCrc32 = ~*dwCrc32;
  return 0;
}

//----- (00492E70) --------------------------------------------------------
UINT __cdecl Registry::ReadInt(const char *FileName_In, const char *Section_In, const char *KeyName_In)
{
  return GetPrivateProfileIntA(Section_In, KeyName_In, 0, FileName_In);
}

//----- (00492E90) --------------------------------------------------------
BOOL __cdecl Registry::ReadString(
        const char *FileName_In,
        const char *Section_In,
        const char *KeyName_In,
        char *Buffer_Out,
        DWORD nBufferSize)
{
  return GetPrivateProfileStringA(Section_In, KeyName_In, 0, Buffer_Out, nBufferSize, FileName_In) != 0;
}

//----- (00492EC0) --------------------------------------------------------
void __thiscall std::ostream::_Sentry_base::~_Sentry_base(std::istream::sentry *this)
{
  std::_Mutex *v1; // eax

  v1 = *(std::_Mutex **)&this->_Myistr->gap8[*(_DWORD *)(*(_DWORD *)this->_Myistr + 4) + 32];
  if ( v1 )
    std::_Mutex::_Unlock(v1 + 1);
}

//----- (00492EE0) --------------------------------------------------------
int __thiscall std::streambuf::snextc(std::streambuf *this)
{
  char **IGnext; // eax
  int *IGcount; // edx
  char **v4; // eax
  char *v5; // ecx
  int result; // eax
  char **v7; // ecx

  IGnext = this->_IGnext;
  if ( *IGnext && (IGcount = this->_IGcount, *IGnext < &(*IGnext)[*IGcount]) )
  {
    --*IGcount;
    v4 = this->_IGnext;
    v5 = (*v4)++;
    result = (unsigned __int8)*v5;
  }
  else
  {
    result = this->uflow(this);
  }
  if ( result != -1 )
  {
    v7 = this->_IGnext;
    if ( *v7 && *v7 < &(*v7)[*this->_IGcount] )
      return (unsigned __int8)**v7;
    else
      return this->underflow(this);
  }
  return result;
}

//----- (00492F50) --------------------------------------------------------
char __thiscall std::istream::_Ipfx(std::istream *this, std::locale _Noskip)
{
  int v3; // ecx
  int v4; // eax
  std::ios_base *v5; // ecx
  std::ostream *Stdstr; // ecx
  int v7; // edx
  std::locale *v8; // eax
  const std::ctype<char> *v9; // eax
  std::locale::_Locimp *Ptr; // edi
  const std::ctype<char> *v11; // ebx
  unsigned int Refs; // eax
  void (__thiscall ***v13)(_DWORD, int); // edi
  _DWORD **v14; // ecx
  unsigned __int8 *v15; // eax
  int v16; // eax
  int v17; // ecx
  int v18; // eax
  int v19; // edx
  std::ios_base *v20; // ecx
  char v21; // al
  int Mystate; // eax
  int v24; // eax
  int v25; // [esp+0h] [ebp-24h] BYREF
  std::_Lockit v26; // [esp+Ch] [ebp-18h] BYREF
  std::istream *v27; // [esp+10h] [ebp-14h]
  int *v28; // [esp+14h] [ebp-10h]
  int v29; // [esp+20h] [ebp-4h]

  v3 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
  v4 = *(_DWORD *)&this->gap8[v3];
  v5 = (std::ios_base *)&this->gap0[v3];
  v28 = &v25;
  v27 = this;
  if ( !v4 )
  {
    Stdstr = (std::ostream *)v5[1]._Stdstr;
    if ( Stdstr )
      std::ostream::flush(Stdstr);
    if ( !LOBYTE(_Noskip._Ptr) )
    {
      v7 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
      if ( (*(_DWORD *)&this->gap8[v7 + 8] & 1) != 0 )
      {
        v8 = std::ios_base::getloc((std::ios_base *)&this->gap0[v7], &_Noskip);
        v29 = 0;
        v9 = std::use_facet<std::ctype<char>>(v8);
        Ptr = _Noskip._Ptr;
        v11 = v9;
        v29 = -1;
        if ( _Noskip._Ptr )
        {
          std::_Lockit::_Lockit(&v26, 0);
          Refs = Ptr->_Refs;
          if ( Refs && Refs != -1 )
            Ptr->_Refs = Refs - 1;
          v13 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
          std::_Lockit::~_Lockit(&v26);
          if ( v13 )
            (**v13)(v13, 1);
        }
        v14 = *(_DWORD ***)&this->gap8[*(_DWORD *)(*(_DWORD *)this->gap0 + 4) + 32];
        v15 = (unsigned __int8 *)*v14[8];
        v29 = 1;
        if ( v15 && *v14[8] < (unsigned int)(*v14[8] + *v14[12]) )
          v16 = *v15;
        else
          v16 = ((int (__thiscall *)(_DWORD **))(*v14)[4])(v14);
        while ( v16 != -1 )
        {
          if ( (v11->_Ctype._Table[(unsigned __int8)v16] & 0x48) == 0 )
            goto LABEL_22;
          v16 = std::streambuf::snextc(*(std::streambuf **)&this->gap8[*(_DWORD *)(*(_DWORD *)this->gap0 + 4) + 32]);
        }
        v17 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
        v18 = *(_DWORD *)&this->gap8[v17];
        v19 = *(_DWORD *)&this->gap8[v17 + 32];
        v20 = (std::ios_base *)&this->gap0[v17];
        v21 = v18 | 1;
        if ( !v19 )
          v21 |= 4u;
        std::ios_base::clear(v20, v21, 0);
      }
    }
LABEL_22:
    v5 = (std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
    Mystate = v5->_Mystate;
    v29 = -1;
    if ( !Mystate )
      return 1;
  }
  v24 = v5->_Mystate | 2;
  if ( !v5[1].__vftable )
    v24 = v5->_Mystate | 6;
  std::ios_base::clear(v5, v24, 0);
  return 0;
}

//----- (00493120) --------------------------------------------------------
void __thiscall std::istream::sentry::sentry(std::istream::sentry *this, std::istream *_Istr, std::locale _Noskip)
{
  std::_Mutex *v4; // eax

  this->_Myistr = _Istr;
  v4 = *(std::_Mutex **)&_Istr->gap8[*(_DWORD *)(*(_DWORD *)_Istr->gap0 + 4) + 32];
  if ( v4 )
    std::_Mutex::_Lock(v4 + 1);
  this->_Ok = std::istream::_Ipfx(this->_Myistr, _Noskip);
}

//----- (00493190) --------------------------------------------------------
char *__cdecl trim_string(char *szString)
{
  char *result; // eax
  char v2; // cl
  char *i; // ecx
  char v4; // dl

  for ( result = szString; ; ++result )
  {
    v2 = *result;
    if ( *result != 13 && v2 != 10 && v2 != 32 )
      break;
  }
  for ( i = &result[strlen(result)]; result != i; *i-- = 0 )
  {
    v4 = *i;
    if ( *i != 13 && v4 != 10 && v4 != 32 && v4 )
      break;
  }
  return result;
}

//----- (004931E0) --------------------------------------------------------
void __cdecl std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
        CConsoleCMDFactory::StringCMD *_First,
        CConsoleCMDFactory::StringCMD *_Last,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  CConsoleCMDFactory::StringCMD *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (00493220) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  unsigned int m_dwHashKey; // edx
  unsigned int m_dwIndex; // esi
  unsigned int v5; // esi
  unsigned int v6; // edi
  unsigned int v7; // edx
  unsigned int v8; // esi
  const char *v9; // edi
  const char *m_szColumnName; // [esp+18h] [ebp-4h]
  const char *v11; // [esp+18h] [ebp-4h]

  if ( _Mid._Myptr->m_dwHashKey < _First._Myptr->m_dwHashKey )
  {
    m_dwHashKey = _Mid._Myptr->m_dwHashKey;
    m_dwIndex = _Mid._Myptr->m_dwIndex;
    m_szColumnName = _Mid._Myptr->m_szColumnName;
    _Mid._Myptr->m_dwHashKey = _First._Myptr->m_dwHashKey;
    _Mid._Myptr->m_dwIndex = _First._Myptr->m_dwIndex;
    _Mid._Myptr->m_szColumnName = _First._Myptr->m_szColumnName;
    _First._Myptr->m_dwHashKey = m_dwHashKey;
    _First._Myptr->m_dwIndex = m_dwIndex;
    _First._Myptr->m_szColumnName = m_szColumnName;
  }
  if ( _Last._Myptr->m_dwHashKey < _Mid._Myptr->m_dwHashKey )
  {
    v5 = _Last._Myptr->m_dwHashKey;
    v6 = _Last._Myptr->m_dwIndex;
    v11 = _Last._Myptr->m_szColumnName;
    _Last._Myptr->m_dwHashKey = _Mid._Myptr->m_dwHashKey;
    _Last._Myptr->m_dwIndex = _Mid._Myptr->m_dwIndex;
    _Last._Myptr->m_szColumnName = _Mid._Myptr->m_szColumnName;
    _Mid._Myptr->m_dwHashKey = v5;
    _Mid._Myptr->m_dwIndex = v6;
    _Mid._Myptr->m_szColumnName = v11;
  }
  if ( _Mid._Myptr->m_dwHashKey < _First._Myptr->m_dwHashKey )
  {
    v7 = _Mid._Myptr->m_dwHashKey;
    v8 = _Mid._Myptr->m_dwIndex;
    v9 = _Mid._Myptr->m_szColumnName;
    _Mid._Myptr->m_dwHashKey = _First._Myptr->m_dwHashKey;
    _Mid._Myptr->m_dwIndex = _First._Myptr->m_dwIndex;
    _Mid._Myptr->m_szColumnName = _First._Myptr->m_szColumnName;
    _First._Myptr->m_dwHashKey = v7;
    _First._Myptr->m_dwIndex = v8;
    _First._Myptr->m_szColumnName = v9;
  }
}

//----- (004932D0) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        int _Hole,
        int _Top,
        CTokenlizedFile::ColumnInfo _Val)
{
  int v4; // ecx
  int i; // eax
  unsigned int m_dwHashKey; // ebp
  CTokenlizedFile::ColumnInfo *v7; // edx
  CTokenlizedFile::ColumnInfo *v8; // ecx

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    m_dwHashKey = _First._Myptr[i].m_dwHashKey;
    v7 = &_First._Myptr[i];
    if ( m_dwHashKey >= _Val.m_dwHashKey )
      break;
    v8 = &_First._Myptr[v4];
    v8->m_dwHashKey = m_dwHashKey;
    v8->m_dwIndex = v7->m_dwIndex;
    v8->m_szColumnName = v7->m_szColumnName;
    v4 = i;
  }
  _First._Myptr[v4] = _Val;
}

//----- (00493340) --------------------------------------------------------
void __cdecl std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  CTokenlizedFile::ColumnInfo *Myptr; // ebp
  int v4; // esi
  int v5; // eax
  int v6; // edi
  int v7; // edx
  CTokenlizedFile::ColumnInfo *v8; // edx
  CTokenlizedFile::ColumnInfo *v9; // ebx
  unsigned int m_dwIndex; // ecx
  CTokenlizedFile::ColumnInfo *v11; // edi
  std::vector<CTokenlizedFile::ColumnInfo>::iterator *p_First; // eax
  char *v13; // ecx
  int v14; // eax
  CTokenlizedFile::ColumnInfo **v15; // eax
  unsigned int v16; // eax
  const char *m_szColumnName; // ecx
  bool v18; // zf
  CTokenlizedFile::ColumnInfo *v19; // eax
  char *v20; // [esp+10h] [ebp-18h] BYREF
  char *v21; // [esp+14h] [ebp-14h] BYREF
  CTokenlizedFile::ColumnInfo *v22; // [esp+18h] [ebp-10h] BYREF
  CTokenlizedFile::ColumnInfo _Holeval; // [esp+1Ch] [ebp-Ch]

  Myptr = _Last._Myptr;
  v4 = _Mid._Myptr - _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  v6 = v4;
  if ( v4 )
  {
    do
    {
      v7 = v5 % v6;
      v5 = v6;
      v6 = v7;
    }
    while ( v7 );
  }
  if ( v5 < _Last._Myptr - _First._Myptr && v5 > 0 )
  {
    v8 = (CTokenlizedFile::ColumnInfo *)(12 * v4);
    _Mid._Myptr = (CTokenlizedFile::ColumnInfo *)(12 * v4);
    v9 = &_First._Myptr[v5];
    _Last._Myptr = (CTokenlizedFile::ColumnInfo *)v5;
    do
    {
      _Holeval.m_dwHashKey = v9->m_dwHashKey;
      m_dwIndex = v9->m_dwIndex;
      _Holeval.m_szColumnName = v9->m_szColumnName;
      v11 = v9;
      _Holeval.m_dwIndex = m_dwIndex;
      if ( (CTokenlizedFile::ColumnInfo *)((char *)v8 + (_DWORD)v9) == Myptr )
      {
        p_First = &_First;
      }
      else
      {
        v20 = (char *)v8 + (_DWORD)v9;
        p_First = (std::vector<CTokenlizedFile::ColumnInfo>::iterator *)&v20;
      }
      v13 = (char *)p_First->_Myptr;
      if ( p_First->_Myptr != v9 )
      {
        do
        {
          v11->m_dwHashKey = *(_DWORD *)v13;
          v11->m_dwIndex = *((_DWORD *)v13 + 1);
          v11->m_szColumnName = (const char *)*((_DWORD *)v13 + 2);
          v14 = ((char *)Myptr - v13) / 12;
          v11 = (CTokenlizedFile::ColumnInfo *)v13;
          if ( v4 >= v14 )
          {
            v22 = &_First._Myptr[v4 - v14];
            v15 = &v22;
          }
          else
          {
            v21 = &v13[(unsigned int)_Mid._Myptr];
            v15 = (CTokenlizedFile::ColumnInfo **)&v21;
          }
          v13 = (char *)*v15;
        }
        while ( *v15 != v9 );
        v8 = _Mid._Myptr;
      }
      v16 = _Holeval.m_dwIndex;
      v11->m_dwHashKey = _Holeval.m_dwHashKey;
      m_szColumnName = _Holeval.m_szColumnName;
      v11->m_dwIndex = v16;
      --v9;
      v19 = (CTokenlizedFile::ColumnInfo *)((char *)_Last._Myptr - 1);
      v18 = _Last._Myptr == (CTokenlizedFile::ColumnInfo *)1;
      v11->m_szColumnName = m_szColumnName;
      _Last._Myptr = v19;
    }
    while ( !v18 );
  }
}

//----- (00493470) --------------------------------------------------------
void __cdecl std::_Median<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Mid,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  int v3; // eax
  unsigned int v4; // edi
  unsigned int v5; // esi
  CTokenlizedFile::ColumnInfo *v7; // [esp-10h] [ebp-14h]
  CTokenlizedFile::ColumnInfo *_Firsta; // [esp+8h] [ebp+4h]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(_First, _Mid, _Last);
  }
  else
  {
    v4 = 24 * ((v3 + 1) / 8);
    v5 = 12 * ((v3 + 1) / 8);
    v7 = &_First._Myptr[v4 / 0xC];
    _Firsta = &_First._Myptr[v5 / 0xC];
    std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
      _First,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)_Firsta,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v7);
    std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Mid._Myptr[v5 / 0xFFFFFFF4],
      _Mid,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Mid._Myptr[v5 / 0xC]);
    std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Last._Myptr[v4 / 0xFFFFFFF4],
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF4],
      _Last);
    std::_Med3<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)_Firsta,
      _Mid,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF4]);
  }
}

//----- (00493510) --------------------------------------------------------
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__cdecl std::_Lower_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator *result,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last,
        const CTokenlizedFile::ColumnInfo *_Val)
{
  CTokenlizedFile::ColumnInfo *Myptr; // esi
  int v5; // ecx
  std::vector<CTokenlizedFile::ColumnInfo>::iterator *v6; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    if ( Myptr[v5 / 2].m_dwHashKey >= _Val->m_dwHashKey )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v5 / 2 + 1;
      v5 += -1 - v5 / 2;
    }
  }
  v6 = result;
  result->_Myptr = Myptr;
  return v6;
}

//----- (00493570) --------------------------------------------------------
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__cdecl std::_Upper_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator *result,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last,
        const CTokenlizedFile::ColumnInfo *_Val)
{
  CTokenlizedFile::ColumnInfo *Myptr; // esi
  int v5; // ecx
  std::vector<CTokenlizedFile::ColumnInfo>::iterator *v6; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    if ( _Val->m_dwHashKey < Myptr[v5 / 2].m_dwHashKey )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v5 / 2 + 1;
      v5 += -1 - v5 / 2;
    }
  }
  v6 = result;
  result->_Myptr = Myptr;
  return v6;
}

//----- (004935D0) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        CTokenlizedFile::ColumnInfo _Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf
  CTokenlizedFile::ColumnInfo *v7; // edx
  CTokenlizedFile::ColumnInfo *v8; // ecx
  CTokenlizedFile::ColumnInfo *v9; // eax
  CTokenlizedFile::ColumnInfo *v10; // ecx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5].m_dwHashKey < _First._Myptr[v5 - 1].m_dwHashKey )
      --v5;
    v7 = &_First._Myptr[v5];
    v8 = &_First._Myptr[v4];
    v8->m_dwHashKey = v7->m_dwHashKey;
    v8->m_dwIndex = v7->m_dwIndex;
    v8->m_szColumnName = v7->m_szColumnName;
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v9 = &_First._Myptr[_Bottom - 1];
    v10 = &_First._Myptr[v4];
    v10->m_dwHashKey = v9->m_dwHashKey;
    v10->m_dwIndex = v9->m_dwIndex;
    v10->m_szColumnName = v9->m_szColumnName;
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
    _First,
    v4,
    _Hole,
    _Val);
}

//----- (00493680) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        CConsoleCMDFactory::StringCMD *_First,
        unsigned int _Count,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  unsigned int v3; // ecx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (004936C0) --------------------------------------------------------
std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *result,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  CTokenlizedFile::ColumnInfo *v3; // ecx
  CTokenlizedFile::ColumnInfo *i; // edi
  unsigned int m_dwHashKey; // eax
  unsigned int v6; // edx
  CTokenlizedFile::ColumnInfo *Myptr; // esi
  CTokenlizedFile::ColumnInfo *v8; // edx
  bool v9; // zf
  CTokenlizedFile::ColumnInfo *v10; // eax
  unsigned int v11; // eax
  CTokenlizedFile::ColumnInfo *v12; // eax
  std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *v13; // eax
  std::vector<CTokenlizedFile::ColumnInfo>::iterator _Glast; // [esp+10h] [ebp-58h]
  unsigned int v15; // [esp+14h] [ebp-54h]
  unsigned int m_dwIndex; // [esp+18h] [ebp-50h]
  const char *m_szColumnName; // [esp+1Ch] [ebp-4Ch]
  unsigned int v18; // [esp+20h] [ebp-48h]
  unsigned int v19; // [esp+24h] [ebp-44h]
  const char *v20; // [esp+28h] [ebp-40h]
  unsigned int v21; // [esp+2Ch] [ebp-3Ch]
  unsigned int v22; // [esp+30h] [ebp-38h]
  const char *v23; // [esp+34h] [ebp-34h]
  unsigned int v24; // [esp+38h] [ebp-30h]
  unsigned int v25; // [esp+3Ch] [ebp-2Ch]
  const char *v26; // [esp+40h] [ebp-28h]
  unsigned int v27; // [esp+44h] [ebp-24h]
  unsigned int v28; // [esp+48h] [ebp-20h]
  const char *v29; // [esp+4Ch] [ebp-1Ch]
  unsigned int v30; // [esp+50h] [ebp-18h]
  unsigned int v31; // [esp+54h] [ebp-14h]
  const char *v32; // [esp+58h] [ebp-10h]
  unsigned int v33; // [esp+60h] [ebp-8h]
  const char *v34; // [esp+64h] [ebp-4h]

  std::_Median<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
    _First,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&_Last._Myptr[-1]);
  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v3 + 1; _First._Myptr < v3; --v3 )
  {
    m_dwHashKey = v3[-1].m_dwHashKey;
    if ( v3->m_dwHashKey > m_dwHashKey )
      break;
    if ( v3->m_dwHashKey < m_dwHashKey )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v6 = v3->m_dwHashKey;
    do
    {
      if ( v6 > i->m_dwHashKey )
        break;
      if ( v6 < i->m_dwHashKey )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  Myptr = v3;
  v8 = i;
  _Glast._Myptr = v3;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v8 < _Last._Myptr; ++v8 )
      {
        if ( v8->m_dwHashKey <= v3->m_dwHashKey )
        {
          if ( v8->m_dwHashKey < v3->m_dwHashKey )
            break;
          v15 = i->m_dwHashKey;
          m_szColumnName = i->m_szColumnName;
          m_dwIndex = i->m_dwIndex;
          i->m_dwHashKey = v8->m_dwHashKey;
          i->m_dwIndex = v8->m_dwIndex;
          i->m_szColumnName = v8->m_szColumnName;
          v8->m_dwHashKey = v15;
          v8->m_dwIndex = m_dwIndex;
          ++i;
          v8->m_szColumnName = m_szColumnName;
        }
      }
      v9 = Myptr == _First._Myptr;
      if ( Myptr > _First._Myptr )
      {
        v10 = Myptr - 1;
        do
        {
          if ( v3->m_dwHashKey <= v10->m_dwHashKey )
          {
            if ( v3->m_dwHashKey < v10->m_dwHashKey )
              break;
            --v3;
            v18 = v3->m_dwHashKey;
            v20 = v3->m_szColumnName;
            v19 = v3->m_dwIndex;
            v3->m_dwHashKey = v10->m_dwHashKey;
            v3->m_dwIndex = v10->m_dwIndex;
            v3->m_szColumnName = v10->m_szColumnName;
            v10->m_dwHashKey = v18;
            v10->m_dwIndex = v19;
            v10->m_szColumnName = v20;
            Myptr = _Glast._Myptr;
          }
          --Myptr;
          --v10;
          _Glast._Myptr = Myptr;
        }
        while ( _First._Myptr < Myptr );
        v9 = Myptr == _First._Myptr;
      }
      if ( v9 )
        break;
      _Glast._Myptr = --Myptr;
      if ( v8 == _Last._Myptr )
      {
        if ( Myptr != --v3 )
        {
          v24 = Myptr->m_dwHashKey;
          v26 = Myptr->m_szColumnName;
          v25 = Myptr->m_dwIndex;
          Myptr->m_dwHashKey = v3->m_dwHashKey;
          Myptr->m_dwIndex = v3->m_dwIndex;
          Myptr->m_szColumnName = v3->m_szColumnName;
          v3->m_dwHashKey = v24;
          v3->m_dwIndex = v25;
          v3->m_szColumnName = v26;
        }
        v27 = v3->m_dwHashKey;
        v29 = v3->m_szColumnName;
        v28 = v3->m_dwIndex;
        --i;
        v3->m_dwHashKey = i->m_dwHashKey;
        v3->m_dwIndex = i->m_dwIndex;
        v3->m_szColumnName = i->m_szColumnName;
        i->m_dwHashKey = v27;
        i->m_dwIndex = v28;
        i->m_szColumnName = v29;
      }
      else
      {
        v30 = v8->m_dwHashKey;
        v32 = v8->m_szColumnName;
        v31 = v8->m_dwIndex;
        v8->m_dwHashKey = Myptr->m_dwHashKey;
        v8->m_dwIndex = Myptr->m_dwIndex;
        v8->m_szColumnName = Myptr->m_szColumnName;
        Myptr->m_dwHashKey = v30;
        Myptr->m_dwIndex = v31;
        ++v8;
        Myptr->m_szColumnName = v32;
      }
    }
    if ( v8 == _Last._Myptr )
      break;
    if ( i != v8 )
    {
      v11 = v3->m_dwHashKey;
      v34 = v3->m_szColumnName;
      v33 = v3->m_dwIndex;
      v3->m_dwHashKey = i->m_dwHashKey;
      v3->m_dwIndex = i->m_dwIndex;
      v3->m_szColumnName = i->m_szColumnName;
      i->m_dwHashKey = v11;
      i->m_dwIndex = v33;
      i->m_szColumnName = v34;
    }
    v21 = v3->m_dwHashKey;
    v12 = v8;
    v23 = v3->m_szColumnName;
    v22 = v3->m_dwIndex;
    v3->m_dwHashKey = v8->m_dwHashKey;
    v3->m_dwIndex = v8->m_dwIndex;
    v3->m_szColumnName = v8->m_szColumnName;
    v8->m_dwHashKey = v21;
    v8->m_dwIndex = v22;
    ++i;
    ++v8;
    v12->m_szColumnName = v23;
    Myptr = _Glast._Myptr;
    ++v3;
  }
  v13 = result;
  result->second._Myptr = i;
  result->first._Myptr = v3;
  return v13;
}

//----- (004939A0) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  int v2; // esi
  CTokenlizedFile::ColumnInfo *v3; // ebx

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
      std::_Adjust_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
        _First,
        --v2,
        _Last._Myptr - _First._Myptr,
        *--v3);
    while ( v2 > 0 );
  }
}

//----- (00493A10) --------------------------------------------------------
std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *__cdecl std::_Equal_range<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
        std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *result,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last,
        const CTokenlizedFile::ColumnInfo *_Val)
{
  CTokenlizedFile::ColumnInfo *Myptr; // ebx
  int v5; // esi
  unsigned int m_dwHashKey; // ebp
  CTokenlizedFile::ColumnInfo *v7; // edi
  std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> *v8; // eax
  const CTokenlizedFile::ColumnInfo *v9; // ebp
  CTokenlizedFile::ColumnInfo *v10; // edx

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 0 )
  {
LABEL_8:
    v8 = result;
    result->first._Myptr = Myptr;
    result->second._Myptr = Myptr;
    return v8;
  }
  m_dwHashKey = _Val->m_dwHashKey;
  while ( 1 )
  {
    v7 = &Myptr[v5 / 2];
    if ( m_dwHashKey <= v7->m_dwHashKey )
      break;
    Myptr = v7 + 1;
    v5 += -1 - v5 / 2;
LABEL_7:
    if ( v5 <= 0 )
      goto LABEL_8;
  }
  if ( m_dwHashKey < v7->m_dwHashKey )
  {
    v5 /= 2;
    goto LABEL_7;
  }
  v9 = _Val;
  std::_Lower_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
    &_Last,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&Myptr[v5 / 2],
    _Val);
  std::_Upper_bound<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
    &_First,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&v7[1],
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&Myptr[v5],
    v9);
  v8 = result;
  v10 = _First._Myptr;
  result->first = _Last;
  result->second._Myptr = v10;
  return v8;
}

//----- (00493AC0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  CTokenlizedFile::ColumnInfo *i; // esi
  unsigned int m_dwHashKey; // ecx
  CTokenlizedFile::ColumnInfo *v4; // eax
  unsigned int v5; // ebp
  std::vector<CTokenlizedFile::ColumnInfo>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_dwHashKey = i->m_dwHashKey;
      if ( i->m_dwHashKey >= _First._Myptr->m_dwHashKey )
      {
        v4 = i - 1;
        if ( m_dwHashKey < i[-1].m_dwHashKey )
        {
          do
          {
            v5 = v4[-1].m_dwHashKey;
            v6._Myptr = v4--;
          }
          while ( m_dwHashKey < v5 );
          if ( v6._Myptr != i )
            std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
              v6,
              (std::vector<CTokenlizedFile::ColumnInfo>::iterator)i,
              (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
          _First,
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)i,
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&i[1]);
      }
    }
  }
}

//----- (00493B40) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last)
{
  int i; // esi
  unsigned int *v3; // eax
  unsigned int v4; // ecx
  unsigned int v5; // edx
  CTokenlizedFile::ColumnInfo v6; // [esp-Ch] [ebp-28h]
  unsigned int v7; // [esp+18h] [ebp-4h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 12 > 1; i -= 12 )
  {
    v3 = (unsigned int *)((char *)&_First._Myptr[-1].m_dwHashKey + i);
    v4 = *v3;
    v5 = v3[1];
    v7 = v3[2];
    *v3 = _First._Myptr->m_dwHashKey;
    v3[1] = _First._Myptr->m_dwIndex;
    v3[2] = (unsigned int)_First._Myptr->m_szColumnName;
    v6.m_dwHashKey = v4;
    *(_QWORD *)&v6.m_dwIndex = __PAIR64__(v7, v5);
    std::_Adjust_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
      _First,
      0,
      (i - 12) / 12,
      v6);
  }
}

//----- (00493BE0) --------------------------------------------------------
void __thiscall CTokenlizedFile::~CTokenlizedFile(CTokenlizedFile *this)
{
  if ( this->m_ColumnValues._Myfirst )
    operator delete(this->m_ColumnValues._Myfirst);
  this->m_ColumnValues._Myfirst = 0;
  this->m_ColumnValues._Mylast = 0;
  this->m_ColumnValues._Myend = 0;
  if ( this->m_ColumnInfo._Myfirst )
    operator delete(this->m_ColumnInfo._Myfirst);
  this->m_ColumnInfo._Myfirst = 0;
  this->m_ColumnInfo._Mylast = 0;
  this->m_ColumnInfo._Myend = 0;
}

//----- (00493C40) --------------------------------------------------------
void __thiscall CTokenlizedFile::Close(CTokenlizedFile *this)
{
  if ( this->m_lpFile )
    fclose(this->m_lpFile);
  if ( this->m_ColumnInfo._Myfirst )
    operator delete(this->m_ColumnInfo._Myfirst);
  this->m_ColumnInfo._Myfirst = 0;
  this->m_ColumnInfo._Mylast = 0;
  this->m_ColumnInfo._Myend = 0;
  if ( this->m_ColumnValues._Myfirst )
    operator delete(this->m_ColumnValues._Myfirst);
  this->m_ColumnValues._Myfirst = 0;
  this->m_ColumnValues._Mylast = 0;
  this->m_ColumnValues._Myend = 0;
}

//----- (00493CB0) --------------------------------------------------------
bool __thiscall CTokenlizedFile::Open(CTokenlizedFile *this, const char *szFilename, const char *szOpenMode)
{
  _iobuf *v4; // eax

  CTokenlizedFile::Close(this);
  this->m_nLine = 0;
  v4 = fopen(szFilename, szOpenMode);
  this->m_lpFile = v4;
  return v4 != 0;
}

//----- (00493CE0) --------------------------------------------------------
char *__thiscall CTokenlizedFile::GetStringValue(CTokenlizedFile *this, const char *szColumnName)
{
  int v2; // eax
  unsigned int v4; // ecx
  const char *i; // edx
  CTokenlizedFile::ColumnInfo *Myptr; // edi
  char **v8; // ecx
  unsigned int m_dwIndex; // edx
  CTokenlizedFile::ColumnInfo *Myfirst; // [esp-10h] [ebp-34h]
  CTokenlizedFile::ColumnInfo *Mylast; // [esp-Ch] [ebp-30h]
  std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> pair; // [esp+10h] [ebp-14h] BYREF
  CTokenlizedFile::ColumnInfo Info; // [esp+18h] [ebp-Ch] BYREF

  v2 = *(unsigned __int8 *)szColumnName;
  v4 = 0;
  for ( i = szColumnName; *i; v2 = *(unsigned __int8 *)i )
  {
    ++i;
    v4 = v2 + 65599 * v4;
  }
  Mylast = this->m_ColumnInfo._Mylast;
  Info.m_dwHashKey = v4;
  Myfirst = this->m_ColumnInfo._Myfirst;
  Info.m_dwIndex = 0;
  Info.m_szColumnName = szColumnName;
  std::_Equal_range<std::vector<CTokenlizedFile::ColumnInfo>::iterator,CTokenlizedFile::ColumnInfo,int>(
    &pair,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myfirst,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Mylast,
    &Info);
  Myptr = pair.first._Myptr;
  if ( pair.first._Myptr == pair.second._Myptr )
    return 0;
  while ( strcmp(szColumnName, Myptr->m_szColumnName) )
  {
    if ( ++Myptr == pair.second._Myptr )
      return 0;
  }
  v8 = this->m_ColumnValues._Myfirst;
  m_dwIndex = Myptr->m_dwIndex;
  if ( !v8 || m_dwIndex >= this->m_ColumnValues._Mylast - v8 )
    return 0;
  else
    return v8[m_dwIndex];
}

//----- (00493DD0) --------------------------------------------------------
void __thiscall __noreturn std::vector<CTokenlizedFile::ColumnInfo>::_Xlen(
        std::vector<CTokenlizedFile::ColumnInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00493E40) --------------------------------------------------------
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::_Insert_n(
        std::vector<CTokenlizedFile::ColumnInfo> *this,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Where,
        unsigned int _Count,
        const CTokenlizedFile::ColumnInfo *_Val)
{
  unsigned int m_dwHashKey; // ecx
  unsigned int m_dwIndex; // edx
  CTokenlizedFile::ColumnInfo *Myfirst; // ebx
  const char *m_szColumnName; // eax
  unsigned int v9; // ecx
  int v11; // eax
  int v12; // eax
  unsigned int v13; // ecx
  int v14; // eax
  int v15; // ebx
  CTokenlizedFile::ColumnInfo *v16; // eax
  char *v17; // edi
  CConsoleCMDFactory::StringCMD *Mylast; // ecx
  CTokenlizedFile::ColumnInfo *v20; // edi
  CConsoleCMDFactory::StringCMD *v21; // [esp-18h] [ebp-40h]
  CConsoleCMDFactory::StringCMD *v22; // [esp-Ch] [ebp-34h]
  unsigned int v23; // [esp-8h] [ebp-30h]
  int v24; // [esp+0h] [ebp-28h] BYREF
  CTokenlizedFile::ColumnInfo _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  int *v26; // [esp+18h] [ebp-10h]
  int v27; // [esp+24h] [ebp-4h]
  CConsoleCMDFactory::StringCMD *_Wherea; // [esp+30h] [ebp+8h]
  CConsoleCMDFactory::StringCMD *_Counta; // [esp+34h] [ebp+Ch]
  CConsoleCMDFactory::StringCMD *_Newvec; // [esp+38h] [ebp+10h]
  CConsoleCMDFactory::StringCMD *_Newveca; // [esp+38h] [ebp+10h]

  m_dwHashKey = _Val->m_dwHashKey;
  m_dwIndex = _Val->m_dwIndex;
  Myfirst = this->_Myfirst;
  m_szColumnName = _Val->m_szColumnName;
  v26 = &v24;
  _Tmp.m_dwHashKey = m_dwHashKey;
  _Tmp.m_dwIndex = m_dwIndex;
  _Tmp.m_szColumnName = m_szColumnName;
  if ( Myfirst )
    v9 = this->_Myend - Myfirst;
  else
    v9 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( 357913941 - v11 < _Count )
      std::vector<CTokenlizedFile::ColumnInfo>::_Xlen(this);
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( v9 >= _Count + v12 )
    {
      Mylast = (CConsoleCMDFactory::StringCMD *)this->_Mylast;
      _Newveca = Mylast;
      if ( ((char *)Mylast - (char *)_Where._Myptr) / 12 >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = (CTokenlizedFile::ColumnInfo *)std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                                                         _Wherea,
                                                         Mylast,
                                                         Mylast);
        std::_Copy_backward_opt<CTokenlizedFile::ColumnInfo *,CTokenlizedFile::ColumnInfo *>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          _Wherea,
          _Newveca);
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          (CConsoleCMDFactory::StringCMD *)&_Where._Myptr[_Count],
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      }
      else
      {
        std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          Mylast,
          (CConsoleCMDFactory::StringCMD *)&_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = (CConsoleCMDFactory::StringCMD *)this->_Mylast;
        v27 = 2;
        std::vector<CConsoleCMDFactory::StringCMD>::_Ufill(
          (std::vector<CConsoleCMDFactory::StringCMD> *)this,
          v22,
          v23,
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          (CConsoleCMDFactory::StringCMD *)&v20[-_Count],
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      }
    }
    else
    {
      if ( 357913941 - (v9 >> 1) >= v9 )
        v13 = (v9 >> 1) + v9;
      else
        v13 = 0;
      if ( Myfirst )
        v14 = this->_Mylast - Myfirst;
      else
        v14 = 0;
      if ( v13 < _Count + v14 )
        v13 = (unsigned int)std::vector<CTokenlizedFile::ColumnInfo>::size((std::vector<CConsoleCMDFactory::StringCMD> *)this)
            + _Count;
      v15 = v13;
      _Newvec = (CConsoleCMDFactory::StringCMD *)operator new((tagHeader *)(12 * v13));
      v21 = (CConsoleCMDFactory::StringCMD *)this->_Myfirst;
      v27 = 0;
      _Counta = std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                  v21,
                  (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
                  _Newvec);
      std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        _Counta,
        _Count,
        (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
        (CConsoleCMDFactory::StringCMD *)this->_Mylast,
        &_Counta[_Count]);
      v16 = this->_Myfirst;
      if ( v16 )
        v16 = (CTokenlizedFile::ColumnInfo *)(this->_Mylast - v16);
      v17 = (char *)v16 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = (CTokenlizedFile::ColumnInfo *)&_Newvec[v15];
      this->_Mylast = (CTokenlizedFile::ColumnInfo *)&_Newvec[(_DWORD)v17];
      this->_Myfirst = (CTokenlizedFile::ColumnInfo *)_Newvec;
    }
  }
}

//----- (00494100) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int>(
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _First,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Last,
        int _Ideal)
{
  CTokenlizedFile::ColumnInfo *Myptr; // ebx
  CTokenlizedFile::ColumnInfo *v4; // edi
  int v5; // eax
  CTokenlizedFile::ColumnInfo *v7; // ebp
  std::pair<std::vector<CTokenlizedFile::ColumnInfo>::iterator,std::vector<CTokenlizedFile::ColumnInfo>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
        &_Mid,
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int>(
          _Mid.second,
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int>(
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int,CTokenlizedFile::ColumnInfo>(
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
        (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v4);
    std::sort_heap<std::vector<CTokenlizedFile::ColumnInfo>::iterator>(
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Myptr,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)v4);
  }
}
// 4941E6: conditional instruction was optimized away because eax.4>=21

//----- (00494230) --------------------------------------------------------
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::reserve(
        std::vector<CTokenlizedFile::ColumnInfo> *this,
        CTokenlizedFile::ColumnInfo *_Count)
{
  CTokenlizedFile::ColumnInfo *Myfirst; // eax
  unsigned int v4; // ebx
  CTokenlizedFile::ColumnInfo *v5; // ecx
  int v6; // edi
  CConsoleCMDFactory::StringCMD *v7; // [esp-18h] [ebp-34h]
  CConsoleCMDFactory::StringCMD *Mylast; // [esp-14h] [ebp-30h]
  _DWORD v9[7]; // [esp+0h] [ebp-1Ch] BYREF
  CConsoleCMDFactory::StringCMD *_Ptr; // [esp+24h] [ebp+8h]

  v9[3] = v9;
  if ( (unsigned int)_Count > 0x15555555 )
    std::vector<CTokenlizedFile::ColumnInfo>::_Xlen(this);
  Myfirst = this->_Myfirst;
  if ( Myfirst )
    Myfirst = (CTokenlizedFile::ColumnInfo *)(this->_Myend - Myfirst);
  if ( Myfirst < _Count )
  {
    v4 = (unsigned int)_Count;
    _Ptr = (CConsoleCMDFactory::StringCMD *)operator new((tagHeader *)(12 * (_DWORD)_Count));
    Mylast = (CConsoleCMDFactory::StringCMD *)this->_Mylast;
    v7 = (CConsoleCMDFactory::StringCMD *)this->_Myfirst;
    v9[6] = 0;
    std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
      v7,
      Mylast,
      _Ptr);
    v5 = this->_Myfirst;
    if ( v5 )
      v6 = this->_Mylast - v5;
    else
      v6 = 0;
    if ( v5 )
      operator delete(this->_Myfirst);
    this->_Myend = (CTokenlizedFile::ColumnInfo *)&_Ptr[v4];
    this->_Mylast = (CTokenlizedFile::ColumnInfo *)&_Ptr[v6];
    this->_Myfirst = (CTokenlizedFile::ColumnInfo *)_Ptr;
  }
}

//----- (00494320) --------------------------------------------------------
std::vector<CTokenlizedFile::ColumnInfo>::iterator *__thiscall std::vector<CTokenlizedFile::ColumnInfo>::insert(
        std::vector<CTokenlizedFile::ColumnInfo> *this,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator *result,
        std::vector<CTokenlizedFile::ColumnInfo>::iterator _Where,
        const CTokenlizedFile::ColumnInfo *_Val)
{
  CTokenlizedFile::ColumnInfo *Myfirst; // esi
  int v6; // esi
  std::vector<CTokenlizedFile::ColumnInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CTokenlizedFile::ColumnInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (00494390) --------------------------------------------------------
void __thiscall std::vector<CTokenlizedFile::ColumnInfo>::push_back(
        std::vector<CTokenlizedFile::ColumnInfo> *this,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  CTokenlizedFile::ColumnInfo *Myfirst; // edi
  unsigned int v4; // ecx
  CTokenlizedFile::ColumnInfo *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
      (CConsoleCMDFactory::StringCMD *)Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CTokenlizedFile::ColumnInfo>::insert(
      this,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator *)&_Val,
      (std::vector<CTokenlizedFile::ColumnInfo>::iterator)this->_Mylast,
      (const CTokenlizedFile::ColumnInfo *)_Val);
  }
}

//----- (00494410) --------------------------------------------------------
void __thiscall CTokenlizedFile::CTokenlizedFile(CTokenlizedFile *this, const char *lpszDelimiter)
{
  std::vector<CTokenlizedFile::ColumnInfo> *p_m_ColumnInfo; // edi
  std::vector<char *> *p_m_ColumnValues; // ebp

  p_m_ColumnInfo = &this->m_ColumnInfo;
  this->m_lpFile = 0;
  this->m_nLine = 0;
  this->m_ColumnInfo._Myfirst = 0;
  this->m_ColumnInfo._Mylast = 0;
  this->m_ColumnInfo._Myend = 0;
  p_m_ColumnValues = &this->m_ColumnValues;
  this->m_ColumnValues._Myfirst = 0;
  this->m_ColumnValues._Mylast = 0;
  this->m_ColumnValues._Myend = 0;
  strncpy(this->m_szDelimiter, lpszDelimiter, 0xFu);
  this->m_szDelimiter[15] = 0;
  std::vector<CTokenlizedFile::ColumnInfo>::reserve(p_m_ColumnInfo, (CTokenlizedFile::ColumnInfo *)0x40);
  std::vector<char *>::reserve(p_m_ColumnValues, 0x40u);
}

//----- (004944A0) --------------------------------------------------------
char __thiscall CTokenlizedFile::ReadColumn(CTokenlizedFile *this)
{
  std::vector<CTokenlizedFile::ColumnInfo> *p_m_ColumnInfo; // ebp
  unsigned int v3; // ebx
  char *v4; // eax
  const char *v5; // eax
  int v6; // ecx
  unsigned int v7; // edx
  const char *i; // esi
  CTokenlizedFile::ColumnInfo *Mylast; // esi
  CTokenlizedFile::ColumnInfo _Val; // [esp+10h] [ebp-Ch] BYREF

  p_m_ColumnInfo = &this->m_ColumnInfo;
  v3 = 0;
  if ( this->m_ColumnInfo._Myfirst )
    operator delete(this->m_ColumnInfo._Myfirst);
  p_m_ColumnInfo->_Myfirst = 0;
  p_m_ColumnInfo->_Mylast = 0;
  p_m_ColumnInfo->_Myend = 0;
  if ( !fgets(this->m_szColumn, 0x4000, this->m_lpFile) )
    return 0;
  v4 = strtok(this->m_szColumn, this->m_szDelimiter);
  do
  {
    v5 = trim_string(v4);
    v6 = *(unsigned __int8 *)v5;
    v7 = 0;
    for ( i = v5; *i; v6 = *(unsigned __int8 *)i )
    {
      ++i;
      v7 = v6 + 65599 * v7;
    }
    _Val.m_dwHashKey = v7;
    _Val.m_dwIndex = v3;
    _Val.m_szColumnName = v5;
    std::vector<CTokenlizedFile::ColumnInfo>::push_back(p_m_ColumnInfo, (const CConsoleCMDFactory::StringCMD *)&_Val);
    ++v3;
    v4 = strtok(0, this->m_szDelimiter);
  }
  while ( v4 );
  Mylast = this->m_ColumnInfo._Mylast;
  ++this->m_nLine;
  std::_Sort<std::vector<CTokenlizedFile::ColumnInfo>::iterator,int>(
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)this->m_ColumnInfo._Myfirst,
    (std::vector<CTokenlizedFile::ColumnInfo>::iterator)Mylast,
    Mylast - this->m_ColumnInfo._Myfirst);
  return 1;
}

//----- (004945A0) --------------------------------------------------------
char __thiscall CTokenlizedFile::ReadLine(CTokenlizedFile *this)
{
  char **Myfirst; // eax
  std::vector<char *> *p_m_ColumnValues; // ebx
  char *v4; // eax
  char *v5; // eax
  char **v6; // edx
  unsigned int v7; // esi
  char **Mylast; // ecx
  char *_Val; // [esp+14h] [ebp-4h] BYREF

  Myfirst = this->m_ColumnValues._Myfirst;
  p_m_ColumnValues = &this->m_ColumnValues;
  if ( Myfirst )
    operator delete(Myfirst);
  p_m_ColumnValues->_Myfirst = 0;
  p_m_ColumnValues->_Mylast = 0;
  p_m_ColumnValues->_Myend = 0;
  if ( !fgets(this->m_szLine, 0x4000, this->m_lpFile) || !strcmp("\n", this->m_szLine) )
    return 0;
  v4 = strtok(this->m_szLine, this->m_szDelimiter);
  do
  {
    v5 = trim_string(v4);
    v6 = p_m_ColumnValues->_Myfirst;
    _Val = v5;
    if ( v6 )
      v7 = p_m_ColumnValues->_Mylast - v6;
    else
      v7 = 0;
    if ( v6 && v7 < p_m_ColumnValues->_Myend - v6 )
    {
      Mylast = p_m_ColumnValues->_Mylast;
      *Mylast = v5;
      p_m_ColumnValues->_Mylast = Mylast + 1;
    }
    else
    {
      std::vector<char *>::_Insert_n(
        p_m_ColumnValues,
        (std::vector<char *>::iterator)p_m_ColumnValues->_Mylast,
        1u,
        &_Val);
    }
    v4 = strtok(0, this->m_szDelimiter);
  }
  while ( v4 );
  ++this->m_nLine;
  return 1;
}

//----- (004946A0) --------------------------------------------------------
void __thiscall Item::CItemType::SetUseItemTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo)
{
  unsigned int m_dwFlags; // ecx

  switch ( itemInfo->m_DetailData.m_cItemType )
  {
    case 0x14u:
    case 0x15u:
    case 0x16u:
    case 0x2Fu:
    case 0x34u:
      itemInfo->m_DetailData.m_dwFlags |= 4u;
      break;
    default:
      break;
  }
  m_dwFlags = itemInfo->m_DetailData.m_dwFlags;
  if ( itemInfo->m_DetailData.m_cMaxDurabilityOrStack > 1u )
  {
    m_dwFlags |= 8u;
    itemInfo->m_DetailData.m_dwFlags = m_dwFlags;
  }
  itemInfo->m_DetailData.m_dwFlags = m_dwFlags | 2;
}

//----- (00494710) --------------------------------------------------------
void __thiscall Item::CItemType::SetEtcItemTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo)
{
  if ( itemInfo->m_DetailData.m_cMaxDurabilityOrStack > 1u )
    itemInfo->m_DetailData.m_dwFlags |= 8u;
}

//----- (00494730) --------------------------------------------------------
__int16 __cdecl GetEquipType(Item::ItemType::Type eItemType)
{
  __int16 result; // ax

  switch ( eItemType )
  {
    case HELM:
    case HEAD:
      result = 4;
      break;
    case ARMOUR:
    case BODY:
      result = 2;
      break;
    case SWORD:
    case BLUNT:
    case AXE:
    case TWOHANDED_BLUNT:
    case TWOHANDED_AXE:
    case TWOHANDED_SWORD:
    case BOW:
    case CROSSBOW:
    case STAFF:
    case DAGGER:
    case COM_BLUNT:
    case COM_SWORD:
    case OPP_HAMMER:
    case OPP_AXE:
    case OPP_SLUSHER:
    case OPP_TALON:
    case OPP_SYTHE:
    case SKILL_A_ATTACK:
    case SKILL_A_GUN:
    case SKILL_A_KNIFE:
      result = 1;
      break;
    case SHIELD:
    case SKILL_A_GUARD:
      result = 3;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}

//----- (004947A0) --------------------------------------------------------
void __cdecl Item::CItemType::GetInstallGemAttribute(
        const unsigned __int8 *cSockets_In,
        Item::ItemAttribute *cAttributes_Out,
        Item::ItemType::Type eItemType,
        unsigned __int8 cSocketNum,
        unsigned __int8 cMaxAttributeNum)
{
  int v5; // ecx
  unsigned __int8 i; // bl
  int v7; // eax
  __int16 v8; // dx
  Item::ItemAttribute GemAttr; // [esp+0h] [ebp-8h]
  __int16 EquipType; // [esp+4h] [ebp-4h]

  EquipType = GetEquipType(eItemType);
  if ( EquipType > 0 )
  {
    v5 = 0;
    for ( i = 0; i < cSocketNum; ++i )
    {
      if ( (unsigned __int16)v5 >= cMaxAttributeNum )
        break;
      v7 = EquipType + 4 * ((cSockets_In[i] - 1) % 5);
      v8 = i ? *(_WORD *)&GemAttribute1[4][v7 + 3] : *((_WORD *)&Item::Grade::Grades[3].m_lpszName + v7 + 1);
      GemAttr = (Item::ItemAttribute)(v8 & 0x3F ^ (((cSockets_In[i] - 1) / 5 + 1) * (v8 & 0xFFC0)));
      cAttributes_Out[(unsigned __int16)v5++] = GemAttr;
      if ( (*(_BYTE *)&GemAttr & 0x3F) == 4 )
        cAttributes_Out[(unsigned __int16)v5++] = (Item::ItemAttribute)(*(_WORD *)&GemAttr & 0xFFC0 | 3);
    }
  }
}

//----- (00494880) --------------------------------------------------------
void __cdecl Item::CItemType::GetUpgradeItemAttribute(
        Item::ItemType::Type eItemType,
        Item::ItemAttribute *cAttributes_Out,
        __int16 cUpgradeLevel)
{
  __int16 EquipType; // ax
  Item::ItemAttribute v4; // cx

  EquipType = GetEquipType(eItemType);
  if ( (_BYTE)cUpgradeLevel )
  {
    if ( EquipType > 0 )
    {
      v4 = GemAttribute2[(unsigned __int8)cUpgradeLevel + 3][EquipType + 3];
      if ( (*(_WORD *)&v4 & 0xFFC0) != 0 )
      {
        *cAttributes_Out = v4;
        if ( (*(_BYTE *)&v4 & 0x3F) == 4 )
          cAttributes_Out[1] = (Item::ItemAttribute)(*(_WORD *)&v4 & 0xFFC0 | 3);
      }
    }
  }
}

//----- (004948E0) --------------------------------------------------------
void __thiscall CSingleton<Item::CItemType>::~CSingleton<Item::CItemType>(CSingleton<Item::CItemType> *this)
{
  CSingleton<Item::CItemType>::ms_pSingleton = 0;
}

//----- (004948F0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<enum Item::ItemType::Type>::iterator>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Mid,
        std::vector<enum Item::ItemType::Type>::iterator _Last)
{
  int v4; // eax
  Item::ItemType::Type v5; // esi
  int v6; // eax
  unsigned int v7; // ebx
  unsigned int v8; // eax
  Item::ItemType::Type v9; // edx
  Item::ItemType::Type v10; // edx
  Item::ItemType::Type v11; // esi
  Item::ItemType::Type v12; // edx
  Item::ItemType::Type *v13; // esi
  Item::ItemType::Type *v14; // esi
  Item::ItemType::Type *v15; // edi
  Item::ItemType::Type v16; // ebx
  Item::ItemType::Type v17; // edi
  Item::ItemType::Type v18; // ebx
  Item::ItemType::Type v19; // edi
  Item::ItemType::Type v20; // esi
  Item::ItemType::Type v21; // edi
  Item::ItemType::Type v22; // edx
  Item::ItemType::Type v23; // edx
  Item::ItemType::Type v24; // edx
  Item::ItemType::Type _Lasta; // [esp+14h] [ebp+Ch]
  Item::ItemType::Type _Lastb; // [esp+14h] [ebp+Ch]
  Item::ItemType::Type _Lastc; // [esp+14h] [ebp+Ch]
  std::vector<enum Item::ItemType::Type>::iterator _Lastd; // [esp+14h] [ebp+Ch]
  Item::ItemType::Type _Laste; // [esp+14h] [ebp+Ch]

  v4 = _Last._Myptr - _First._Myptr;
  v5 = *_First._Myptr;
  if ( v4 <= 40 )
  {
    v22 = *_Mid._Myptr;
    if ( *_Mid._Myptr < v5 )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v22;
    }
    v23 = *_Last._Myptr;
    if ( *_Last._Myptr < *_Mid._Myptr )
    {
      *_Last._Myptr = *_Mid._Myptr;
      *_Mid._Myptr = v23;
    }
    v24 = *_Mid._Myptr;
    if ( *_Mid._Myptr < *_First._Myptr )
    {
      *_Mid._Myptr = *_First._Myptr;
      *_First._Myptr = v24;
    }
  }
  else
  {
    v6 = (v4 + 1) / 8;
    v7 = 8 * v6;
    v8 = 4 * v6;
    v9 = _First._Myptr[v8 / 4];
    if ( v9 < v5 )
    {
      _First._Myptr[v8 / 4] = v5;
      *_First._Myptr = v9;
    }
    v10 = _First._Myptr[v7 / 4];
    v11 = _First._Myptr[v8 / 4];
    if ( v10 < v11 )
    {
      _First._Myptr[v7 / 4] = v11;
      _First._Myptr[v8 / 4] = v10;
    }
    v12 = _First._Myptr[v8 / 4];
    if ( v12 < *_First._Myptr )
    {
      _First._Myptr[v8 / 4] = *_First._Myptr;
      *_First._Myptr = v12;
    }
    v13 = &_Mid._Myptr[v8 / 0xFFFFFFFC];
    if ( *_Mid._Myptr < _Mid._Myptr[v8 / 0xFFFFFFFC] )
    {
      _Lasta = *_Mid._Myptr;
      *_Mid._Myptr = *v13;
      *v13 = _Lasta;
    }
    if ( _Mid._Myptr[v8 / 4] < *_Mid._Myptr )
    {
      _Lastb = _Mid._Myptr[v8 / 4];
      _Mid._Myptr[v8 / 4] = *_Mid._Myptr;
      *_Mid._Myptr = _Lastb;
    }
    if ( *_Mid._Myptr < *v13 )
    {
      _Lastc = *_Mid._Myptr;
      *_Mid._Myptr = *v13;
      *v13 = _Lastc;
    }
    v14 = &_Last._Myptr[v8 / 0xFFFFFFFC];
    v15 = &_Last._Myptr[v7 / 0xFFFFFFFC];
    if ( _Last._Myptr[v8 / 0xFFFFFFFC] < _Last._Myptr[v7 / 0xFFFFFFFC] )
    {
      _Lastd._Myptr = (Item::ItemType::Type *)_Last._Myptr[v8 / 0xFFFFFFFC];
      *v14 = *v15;
      *(std::vector<enum Item::ItemType::Type>::iterator *)v15 = _Lastd;
    }
    if ( *_Last._Myptr < *v14 )
    {
      _Laste = *_Last._Myptr;
      *_Last._Myptr = *v14;
      *v14 = _Laste;
    }
    v16 = *v14;
    if ( *v14 < *v15 )
    {
      *v14 = *v15;
      *v15 = v16;
    }
    v17 = *_Mid._Myptr;
    v18 = _First._Myptr[v8 / 4];
    if ( *_Mid._Myptr < v18 )
    {
      *_Mid._Myptr = v18;
      _First._Myptr[v8 / 4] = v17;
    }
    v19 = *v14;
    if ( *v14 < *_Mid._Myptr )
    {
      *v14 = *_Mid._Myptr;
      *_Mid._Myptr = v19;
    }
    v20 = *_Mid._Myptr;
    v21 = _First._Myptr[v8 / 4];
    if ( *_Mid._Myptr < v21 )
    {
      *_Mid._Myptr = v21;
      _First._Myptr[v8 / 4] = v20;
    }
  }
}

//----- (00494A30) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        int _Hole,
        int _Bottom,
        Item::ItemType::Type _Val)
{
  int v4; // esi
  int v5; // eax
  bool i; // zf
  int j; // eax
  Item::ItemType::Type v8; // edx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5] < _First._Myptr[v5 - 1] )
      --v5;
    _First._Myptr[v4] = _First._Myptr[v5];
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    _First._Myptr[v4] = _First._Myptr[_Bottom - 1];
    v4 = _Bottom - 1;
  }
  for ( j = (v4 - 1) / 2; _Hole < v4; j = (j - 1) / 2 )
  {
    v8 = _First._Myptr[j];
    if ( v8 >= _Val )
      break;
    _First._Myptr[v4] = v8;
    v4 = j;
  }
  _First._Myptr[v4] = _Val;
}

//----- (00494AB0) --------------------------------------------------------
std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> *__cdecl std::_Unguarded_partition<std::vector<enum Item::ItemType::Type>::iterator>(
        std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> *result,
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last)
{
  Item::ItemType::Type *Myptr; // ebx
  Item::ItemType::Type *v4; // ecx
  Item::ItemType::Type *i; // esi
  Item::ItemType::Type v6; // eax
  Item::ItemType::Type v7; // edx
  Item::ItemType::Type *v8; // eax
  Item::ItemType::Type *v9; // ebp
  Item::ItemType::Type v10; // edi
  bool v11; // zf
  Item::ItemType::Type *v12; // edx
  Item::ItemType::Type v13; // edi
  Item::ItemType::Type v14; // edx
  Item::ItemType::Type *v15; // edx
  Item::ItemType::Type v16; // edx
  Item::ItemType::Type v17; // edi
  Item::ItemType::Type v18; // edx
  Item::ItemType::Type v19; // edi
  std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> *v20; // eax
  Item::ItemType::Type v21; // [esp+10h] [ebp-4h]

  Myptr = _Last._Myptr;
  std::_Median<std::vector<enum Item::ItemType::Type>::iterator>(
    _First,
    (std::vector<enum Item::ItemType::Type>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<enum Item::ItemType::Type>::iterator)(_Last._Myptr - 1));
  v4 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v4 + 1; _First._Myptr < v4; --v4 )
  {
    v6 = *((_DWORD *)v4 - 1);
    if ( *v4 > v6 )
      break;
    if ( *v4 < v6 )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v7 = *v4;
    do
    {
      if ( v7 > *i )
        break;
      if ( v7 < *i )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  v8 = i;
  v9 = v4;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v8 < Myptr; ++v8 )
      {
        if ( *v8 <= *v4 )
        {
          if ( *v8 < *v4 )
            break;
          v10 = *i;
          *i = *v8;
          Myptr = _Last._Myptr;
          ++i;
          *v8 = v10;
        }
      }
      v11 = v9 == _First._Myptr;
      if ( v9 > _First._Myptr )
      {
        v12 = v9 - 1;
        do
        {
          if ( *v4 <= *v12 )
          {
            if ( *v4 < *v12 )
              break;
            v13 = *((_DWORD *)v4-- - 1);
            *v4 = *v12;
            *v12 = v13;
          }
          --v9;
          --v12;
        }
        while ( _First._Myptr < v9 );
        Myptr = _Last._Myptr;
        v11 = v9 == _First._Myptr;
      }
      if ( v11 )
        break;
      --v9;
      if ( v8 == Myptr )
      {
        if ( v9 != --v4 )
        {
          v16 = *v9;
          *v9 = *v4;
          *v4 = v16;
        }
        v17 = *((_DWORD *)i - 1);
        v18 = *v4;
        --i;
        *v4 = v17;
        *i = v18;
      }
      else
      {
        v19 = *v8;
        *v8 = *v9;
        Myptr = _Last._Myptr;
        ++v8;
        *v9 = v19;
      }
    }
    if ( v8 == Myptr )
      break;
    if ( i != v8 )
    {
      v14 = *v4;
      *v4 = *i;
      *i = v14;
    }
    v15 = v8;
    v21 = *v4;
    *v4 = *v8;
    Myptr = _Last._Myptr;
    ++i;
    ++v8;
    ++v4;
    *v15 = v21;
  }
  v20 = result;
  result->second._Myptr = i;
  result->first._Myptr = v4;
  return v20;
}

//----- (00494C00) --------------------------------------------------------
std::vector<enum Item::ItemType::Type>::iterator *__cdecl std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
        std::vector<enum Item::ItemType::Type>::iterator *result,
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last,
        const unsigned __int8 *_Val)
{
  Item::ItemType::Type *Myptr; // esi
  int v5; // ecx
  int v6; // eax
  std::vector<enum Item::ItemType::Type>::iterator *v7; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    v6 = v5 / 2;
    if ( Myptr[v5 / 2] >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v6 + 1;
      v5 += -1 - v6;
    }
  }
  v7 = result;
  result->_Myptr = Myptr;
  return v7;
}

//----- (00494C50) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last)
{
  int i; // esi
  Item::ItemType::Type v3; // eax

  for ( i = (_Last._Myptr - _First._Myptr) / 2;
        i > 0;
        std::_Adjust_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          _First,
          i,
          _Last._Myptr - _First._Myptr,
          v3) )
  {
    v3 = _First._Myptr[--i];
  }
}

//----- (00494C90) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<enum Item::ItemType::Type>::iterator>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last)
{
  Item::ItemType::Type *i; // esi
  Item::ItemType::Type v3; // ecx
  unsigned int *v4; // eax
  Item::ItemType::Type v5; // ebp
  std::vector<unsigned long>::iterator v6; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      v3 = *i;
      if ( *i >= *_First._Myptr )
      {
        v4 = (unsigned int *)(i - 1);
        if ( v3 < *((_DWORD *)i - 1) )
        {
          do
          {
            v5 = *(v4 - 1);
            v6._Myptr = v4--;
          }
          while ( v3 < v5 );
          if ( (Item::ItemType::Type *)v6._Myptr != i )
            std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
              v6,
              (std::vector<unsigned long>::iterator)i,
              (std::vector<unsigned long>::iterator)(i + 1));
        }
      }
      else if ( _First._Myptr != i && i != i + 1 )
      {
        std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
          (std::vector<unsigned long>::iterator)_First._Myptr,
          (std::vector<unsigned long>::iterator)i,
          (std::vector<unsigned long>::iterator)(i + 1));
      }
    }
  }
}

//----- (00494D10) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<enum Item::ItemType::Type>::iterator>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last)
{
  int i; // esi
  Item::ItemType::Type v3; // [esp-4h] [ebp-Ch]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i >> 2 > 1; i -= 4 )
  {
    v3 = *(Item::ItemType::Type *)((char *)_First._Myptr + i - 4);
    *(Item::ItemType::Type *)((char *)_First._Myptr + i - 4) = *_First._Myptr;
    std::_Adjust_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
      _First,
      0,
      (i - 4) >> 2,
      v3);
  }
}

//----- (00494D60) --------------------------------------------------------
void __thiscall Item::CItemType::~CItemType(Item::CItemType *this)
{
  `eh vector destructor iterator'(
    (char *)this,
    0x10u,
    7,
    (void (__thiscall *)(void *))std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>);
  CSingleton<Item::CItemType>::ms_pSingleton = 0;
}

//----- (00494DB0) --------------------------------------------------------
BOOL __thiscall Item::CItemType::IsCorrectItemType(
        Item::CItemType *this,
        Item::CItemType::ArrayType itemType,
        unsigned __int8 cEquipType)
{
  Item::ItemType::Type *Mylast; // esi

  Mylast = this->m_ItemTypes[itemType]._Mylast;
  std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
    (std::vector<enum Item::ItemType::Type>::iterator *)&itemType,
    (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[itemType]._Myfirst,
    (std::vector<enum Item::ItemType::Type>::iterator)Mylast,
    &cEquipType);
  return (Item::ItemType::Type *)itemType != Mylast && cEquipType >= *(int *)itemType;
}

//----- (00494E00) --------------------------------------------------------
unsigned __int8 __thiscall Item::CItemType::ConvertRandomOptionType(Item::CItemType *this, unsigned __int8 cEquipType)
{
  unsigned __int8 v2; // bl
  Item::ItemType::Type *Mylast; // esi
  unsigned __int8 v4; // al
  std::vector<enum Item::ItemType::Type>::iterator result; // [esp+8h] [ebp-4h] BYREF

  v2 = cEquipType;
  Mylast = this->m_ItemTypes[2]._Mylast;
  std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
    &result,
    (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[2]._Myfirst,
    (std::vector<enum Item::ItemType::Type>::iterator)Mylast,
    &cEquipType);
  if ( result._Myptr != Mylast && v2 >= *(int *)result._Myptr )
    return 0;
  switch ( v2 )
  {
    case 0u:
    case 0x1Du:
      v4 = 2;
      break;
    case 3u:
    case 0x1Eu:
      v4 = 1;
      break;
    case 0x10u:
    case 0x28u:
      v4 = 5;
      break;
    case 0x12u:
    case 0x2Cu:
      v4 = 3;
      break;
    case 0x13u:
      v4 = 4;
      break;
    case 0x29u:
    case 0x2Au:
    case 0x2Bu:
      v4 = 6;
      break;
    default:
      v4 = 7;
      break;
  }
  return v4;
}

//----- (00494EE0) --------------------------------------------------------
void __thiscall Item::CItemType::SetEquipTypeFlags(Item::CItemType *this, Item::ItemInfo *itemInfo)
{
  Item::ItemInfo *v2; // edi
  unsigned __int8 m_cItemType; // bl
  Item::ItemType::Type *Mylast; // ebp
  int v6; // edx
  int v7; // ebp
  Item::ItemType::Type *v8; // ebx
  int v9; // ebp
  Item::ItemType::Type *v10; // ebp
  int v11; // eax
  Item::ItemType::Type *Myfirst; // [esp-10h] [ebp-24h]
  Item::ItemType::Type *v13; // [esp-10h] [ebp-24h]
  std::vector<enum Item::ItemType::Type>::iterator result; // [esp+10h] [ebp-4h] BYREF

  v2 = itemInfo;
  m_cItemType = itemInfo->m_DetailData.m_cItemType;
  if ( m_cItemType >= 0x2Du && m_cItemType <= 0x2Eu )
    itemInfo->m_DetailData.m_dwFlags |= 8u;
  Mylast = this->m_ItemTypes[2]._Mylast;
  Myfirst = this->m_ItemTypes[2]._Myfirst;
  LOBYTE(itemInfo) = m_cItemType;
  std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
    &result,
    (std::vector<enum Item::ItemType::Type>::iterator)Myfirst,
    (std::vector<enum Item::ItemType::Type>::iterator)Mylast,
    (const unsigned __int8 *)&itemInfo);
  if ( result._Myptr == Mylast || m_cItemType < *(int *)result._Myptr )
  {
    v10 = this->m_ItemTypes[4]._Mylast;
    v13 = this->m_ItemTypes[4]._Myfirst;
    LOBYTE(itemInfo) = m_cItemType;
    std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
      &result,
      (std::vector<enum Item::ItemType::Type>::iterator)v13,
      (std::vector<enum Item::ItemType::Type>::iterator)v10,
      (const unsigned __int8 *)&itemInfo);
    if ( result._Myptr != v10 && m_cItemType >= *(int *)result._Myptr )
    {
      v11 = v2->m_DetailData.m_dwFlags | 0x80;
      v2->m_DetailData.m_dwFlags = v11;
      v2->m_DetailData.m_dwFlags = v11 | 1;
      return;
    }
    if ( Item::CItemType::IsCorrectItemType(this, ARMOUR_TYPE, m_cItemType) )
      v2->m_DetailData.m_dwFlags |= 0x40u;
  }
  else
  {
    v6 = v2->m_DetailData.m_dwFlags | 0x20;
    v2->m_DetailData.m_dwFlags = v6;
    v7 = v6;
    LOBYTE(itemInfo) = m_cItemType;
    v8 = this->m_ItemTypes[5]._Mylast;
    std::_Lower_bound<std::vector<enum Item::ItemType::Type>::iterator,unsigned char,int>(
      &result,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[5]._Myfirst,
      (std::vector<enum Item::ItemType::Type>::iterator)v8,
      (const unsigned __int8 *)&itemInfo);
    if ( result._Myptr != v8 && (unsigned __int8)itemInfo >= *(int *)result._Myptr )
    {
      v9 = v7 | 0x10;
      v2->m_DetailData.m_dwFlags = v9;
      v2->m_DetailData.m_dwFlags = v9 | 1;
      return;
    }
  }
  v2->m_DetailData.m_dwFlags |= 1u;
}

//----- (00494FF0) --------------------------------------------------------
void __thiscall __noreturn std::vector<enum Item::ItemType::Type>::_Xlen(std::vector<enum Item::ItemType::Type> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00495060) --------------------------------------------------------
void __thiscall std::vector<enum Item::ItemType::Type>::_Insert_n(
        std::vector<enum Item::ItemType::Type> *this,
        std::vector<enum Item::ItemType::Type>::iterator _Where,
        unsigned int _Count,
        const Item::ItemType::Type **_Val)
{
  Item::ItemType::Type *Myfirst; // edx
  const Item::ItemType::Type *v6; // ecx
  unsigned int v7; // eax
  int v9; // ecx
  int v10; // ecx
  unsigned int v11; // eax
  int v12; // ecx
  int v13; // eax
  Item::ItemType::Type *v14; // edi
  Item::ItemType::Type *v15; // ecx
  int v16; // eax
  int v17; // ebx
  unsigned int *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  Item::ItemType::Type *v22; // ebx
  CLotteryEvent::LotteryEventPrize *v23; // ebx
  Item::ItemType::Type *v24; // [esp-18h] [ebp-3Ch]
  Item::ItemType::Type *v25; // [esp-Ch] [ebp-30h]
  unsigned int v26; // [esp-8h] [ebp-2Ch]
  int v27; // [esp+0h] [ebp-24h] BYREF
  Item::ItemType::Type *_Newvec; // [esp+Ch] [ebp-18h]
  int v29; // [esp+10h] [ebp-14h]
  int *v30; // [esp+14h] [ebp-10h]
  int v31; // [esp+20h] [ebp-4h]
  std::vector<enum Item::ItemType::Type>::iterator _Wherea; // [esp+2Ch] [ebp+8h]
  unsigned int *_Countb; // [esp+30h] [ebp+Ch]
  CLotteryEvent::LotteryEventPrize *_Counta; // [esp+30h] [ebp+Ch]

  Myfirst = this->_Myfirst;
  v6 = *_Val;
  v30 = &v27;
  _Val = (const Item::ItemType::Type **)v6;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x3FFFFFFF - v9 < _Count )
      std::vector<enum Item::ItemType::Type>::_Xlen(this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = (unsigned int *)this->_Mylast;
      v20 = ((char *)Mylast - (char *)_Where._Myptr) >> 2 < _Count;
      v21 = 4 * _Count;
      _Wherea._Myptr = (Item::ItemType::Type *)(4 * _Count);
      _Counta = (CLotteryEvent::LotteryEventPrize *)Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
          (std::vector<unsigned long>::iterator)_Where._Myptr,
          (std::vector<unsigned long>::iterator)Mylast,
          (unsigned int *)&_Where._Myptr[v21 / 4]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = this->_Mylast;
        v31 = 2;
        std::vector<CLotteryEvent::LotteryEventPrize>::_Ufill(
          (std::vector<CLotteryEvent::LotteryEventPrize> *)this,
          (CLotteryEvent::LotteryEventPrize *)v25,
          v26,
          (const CLotteryEvent::LotteryEventPrize *)&_Val);
        v22 = (Item::ItemType::Type *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v22 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        v23 = (CLotteryEvent::LotteryEventPrize *)&Mylast[v21 / 0xFFFFFFFC];
        this->_Mylast = (Item::ItemType::Type *)std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                                                  (std::vector<unsigned long>::iterator)&Mylast[v21 / 0xFFFFFFFC],
                                                  (std::vector<unsigned long>::iterator)Mylast,
                                                  Mylast);
        std::copy_backward<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
          (CLotteryEvent::LotteryEventPrize *)_Where._Myptr,
          v23,
          _Counta);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      v29 = 4 * v11;
      v14 = (Item::ItemType::Type *)operator new((tagHeader *)(4 * v11));
      v24 = this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Countb = std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                  (std::vector<unsigned long>::iterator)v24,
                  (std::vector<unsigned long>::iterator)_Where._Myptr,
                  (unsigned int *)v14);
      std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
        (CLotteryEvent::LotteryEventPrize *)_Countb,
        _Count,
        (const CLotteryEvent::LotteryEventPrize *)&_Val);
      std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
        (std::vector<unsigned long>::iterator)_Where._Myptr,
        (std::vector<unsigned long>::iterator)this->_Mylast,
        &_Countb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[v29 / 4u];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (004952A0) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<enum Item::ItemType::Type>::iterator,int>(
        std::vector<enum Item::ItemType::Type>::iterator _First,
        std::vector<enum Item::ItemType::Type>::iterator _Last,
        int _Ideal)
{
  Item::ItemType::Type *Myptr; // ebx
  Item::ItemType::Type *v4; // edi
  int v5; // eax
  Item::ItemType::Type *v7; // ebp
  std::pair<std::vector<enum Item::ItemType::Type>::iterator,std::vector<enum Item::ItemType::Type>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<enum Item::ItemType::Type>::iterator>(
        (std::vector<enum Item::ItemType::Type>::iterator)Myptr,
        (std::vector<enum Item::ItemType::Type>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<enum Item::ItemType::Type>::iterator>(
        &_Mid,
        (std::vector<enum Item::ItemType::Type>::iterator)Myptr,
        (std::vector<enum Item::ItemType::Type>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( (int)(((char *)_Mid.first._Myptr - (char *)Myptr) & 0xFFFFFFFC) >= (int)(((char *)v4
                                                                                   - (char *)_Mid.second._Myptr) & 0xFFFFFFFC) )
      {
        std::_Sort<std::vector<enum Item::ItemType::Type>::iterator,int>(
          _Mid.second,
          (std::vector<enum Item::ItemType::Type>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<enum Item::ItemType::Type>::iterator,int>(
          (std::vector<enum Item::ItemType::Type>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( (int)(((char *)v4 - (char *)Myptr) & 0xFFFFFFFC) > 4 )
      std::_Make_heap<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(
        (std::vector<enum Item::ItemType::Type>::iterator)Myptr,
        (std::vector<enum Item::ItemType::Type>::iterator)v4);
    std::sort_heap<std::vector<enum Item::ItemType::Type>::iterator>(
      (std::vector<enum Item::ItemType::Type>::iterator)Myptr,
      (std::vector<enum Item::ItemType::Type>::iterator)v4);
  }
}
// 49533D: conditional instruction was optimized away because eax.4>=21

//----- (00495370) --------------------------------------------------------
void __thiscall std::vector<enum Item::ItemType::Type>::reserve(
        std::vector<enum Item::ItemType::Type> *this,
        unsigned int _Count)
{
  Item::ItemType::Type *Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  Item::ItemType::Type *v6; // edi
  Item::ItemType::Type *v7; // eax
  Item::ItemType::Type *v8; // [esp-18h] [ebp-38h]
  Item::ItemType::Type *Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  Item::ItemType::Type *_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x3FFFFFFF )
    std::vector<enum Item::ItemType::Type>::_Xlen(this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(4 * _Count);
    v6 = (Item::ItemType::Type *)operator new(_Counta);
    Mylast = this->_Mylast;
    v8 = this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
      (std::vector<unsigned long>::iterator)v8,
      (std::vector<unsigned long>::iterator)Mylast,
      (unsigned int *)v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (Item::ItemType::Type *)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (00495440) --------------------------------------------------------
void __thiscall std::vector<enum Item::ItemType::Type>::push_back(
        std::vector<enum Item::ItemType::Type> *this,
        Item::ItemType::Type *_Val)
{
  Item::ItemType::Type *Myfirst; // edx
  unsigned int v4; // ecx
  Item::ItemType::Type *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)Mylast,
      1u,
      (const CLotteryEvent::LotteryEventPrize *)_Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this,
      (std::vector<enum Item::ItemType::Type>::iterator)this->_Mylast,
      1u,
      (const Item::ItemType::Type **)_Val);
  }
}

//----- (004954B0) --------------------------------------------------------
void __thiscall Item::CItemType::CItemType(Item::CItemType *this)
{
  CTypeName *m_ItemTypeNames; // eax
  int v3; // ecx
  Item::CItemType *v4; // ebx
  int v5; // ebp
  CLotteryEvent::LotteryEventPrize v6; // ecx
  CLotteryEvent::LotteryEventPrize v7; // edx
  CLotteryEvent::LotteryEventPrize v8; // ecx
  CLotteryEvent::LotteryEventPrize v9; // edx
  CLotteryEvent::LotteryEventPrize v10; // ecx
  CLotteryEvent::LotteryEventPrize v11; // edx
  CLotteryEvent::LotteryEventPrize v12; // ecx
  CLotteryEvent::LotteryEventPrize v13; // edx
  CLotteryEvent::LotteryEventPrize v14; // ecx
  CLotteryEvent::LotteryEventPrize v15; // edx
  CLotteryEvent::LotteryEventPrize v16; // ecx
  CLotteryEvent::LotteryEventPrize v17; // edx
  CLotteryEvent::LotteryEventPrize v18; // ecx
  CLotteryEvent::LotteryEventPrize v19; // edx
  CLotteryEvent::LotteryEventPrize v20; // ecx
  CLotteryEvent::LotteryEventPrize v21; // edx
  CLotteryEvent::LotteryEventPrize v22; // ecx
  CLotteryEvent::LotteryEventPrize v23; // edx
  CLotteryEvent::LotteryEventPrize v24; // ecx
  CLotteryEvent::LotteryEventPrize v25; // edx
  CLotteryEvent::LotteryEventPrize v26; // ecx
  CLotteryEvent::LotteryEventPrize v27; // edx
  CLotteryEvent::LotteryEventPrize v28; // ecx
  CLotteryEvent::LotteryEventPrize v29; // edx
  CLotteryEvent::LotteryEventPrize v30; // ecx
  CLotteryEvent::LotteryEventPrize v31; // edx
  CLotteryEvent::LotteryEventPrize v32; // ecx
  CLotteryEvent::LotteryEventPrize v33; // edx
  CLotteryEvent::LotteryEventPrize v34; // ecx
  CLotteryEvent::LotteryEventPrize v35; // edx
  CLotteryEvent::LotteryEventPrize v36; // ecx
  CLotteryEvent::LotteryEventPrize v37; // edx
  CLotteryEvent::LotteryEventPrize v38; // ecx
  CLotteryEvent::LotteryEventPrize v39; // edx
  CLotteryEvent::LotteryEventPrize v40; // ecx
  CLotteryEvent::LotteryEventPrize v41; // edx
  CLotteryEvent::LotteryEventPrize v42; // ecx
  CLotteryEvent::LotteryEventPrize v43; // edx
  CLotteryEvent::LotteryEventPrize v44; // ecx
  CLotteryEvent::LotteryEventPrize v45; // edx
  CLotteryEvent::LotteryEventPrize v46; // ecx
  CLotteryEvent::LotteryEventPrize v47; // edx
  CLotteryEvent::LotteryEventPrize v48; // ecx
  CLotteryEvent::LotteryEventPrize v49; // edx
  CLotteryEvent::LotteryEventPrize v50; // ecx
  CLotteryEvent::LotteryEventPrize v51; // edx
  CLotteryEvent::LotteryEventPrize v52; // edx
  CLotteryEvent::LotteryEventPrize v53; // edx
  CLotteryEvent::LotteryEventPrize v54; // edx
  CLotteryEvent::LotteryEventPrize v55; // edx
  CLotteryEvent::LotteryEventPrize v56; // edx
  CLotteryEvent::LotteryEventPrize v57; // edx
  CLotteryEvent::LotteryEventPrize v58; // edx
  CLotteryEvent::LotteryEventPrize v59; // edx
  CLotteryEvent::LotteryEventPrize v60; // edx
  CLotteryEvent::LotteryEventPrize v61; // edx
  std::vector<enum Item::ItemType::Type>::iterator *p_Myfirst; // ebp
  Item::ItemType::Type *Myfirst; // eax
  unsigned int v64; // edx
  Item::ItemType::Type *Mylast; // edi
  std::vector<enum Item::ItemType::Type>::iterator v66; // edx
  bool v67; // zf
  unsigned int v68; // ecx
  Item::ItemType::Type *v69; // edi
  Item::ItemType::Type *Myptr; // edx
  unsigned int v71; // ecx
  Item::ItemType::Type *v72; // edi
  Item::ItemType::Type *v73; // edx
  unsigned int v74; // ecx
  Item::ItemType::Type *v75; // edi
  Item::ItemType::Type *v76; // edx
  unsigned int v77; // ecx
  Item::ItemType::Type *v78; // edi
  Item::ItemType::Type *v79; // edx
  unsigned int v80; // ecx
  Item::ItemType::Type *v81; // edi
  Item::ItemType::Type *v82; // edx
  unsigned int v83; // ecx
  Item::ItemType::Type *v84; // edi
  Item::ItemType::Type *v85; // edx
  unsigned int v86; // ecx
  Item::ItemType::Type *v87; // edi
  Item::ItemType::Type *v88; // edx
  unsigned int v89; // ecx
  Item::ItemType::Type *v90; // edi
  Item::ItemType::Type *v91; // edx
  unsigned int v92; // ecx
  Item::ItemType::Type *v93; // edi
  Item::ItemType::Type *v94; // edx
  unsigned int v95; // ecx
  Item::ItemType::Type *v96; // edi
  Item::ItemType::Type *v97; // edx
  unsigned int v98; // ecx
  Item::ItemType::Type *v99; // edi
  Item::ItemType::Type *v100; // edx
  unsigned int v101; // ecx
  Item::ItemType::Type *v102; // edi
  Item::ItemType::Type *v103; // edx
  unsigned int v104; // ecx
  Item::ItemType::Type *v105; // edi
  Item::ItemType::Type *v106; // edx
  unsigned int v107; // ecx
  Item::ItemType::Type *v108; // edi
  Item::ItemType::Type *v109; // edx
  unsigned int v110; // ecx
  Item::ItemType::Type *v111; // edi
  Item::ItemType::Type *v112; // edx
  unsigned int v113; // ecx
  Item::ItemType::Type *v114; // edi
  Item::ItemType::Type *v115; // edx
  unsigned int v116; // ecx
  Item::ItemType::Type *v117; // edi
  Item::ItemType::Type *v118; // edx
  unsigned int v119; // ecx
  Item::ItemType::Type *v120; // edi
  Item::ItemType::Type *v121; // edx
  unsigned int v122; // ecx
  Item::ItemType::Type *v123; // edi
  Item::ItemType::Type *v124; // edx
  unsigned int v125; // ecx
  Item::ItemType::Type *v126; // edi
  Item::ItemType::Type *v127; // edx
  unsigned int v128; // ecx
  Item::ItemType::Type *v129; // edi
  Item::ItemType::Type *v130; // edx
  unsigned int v131; // ecx
  Item::ItemType::Type *v132; // edi
  Item::ItemType::Type *v133; // edx
  unsigned int v134; // ecx
  Item::ItemType::Type *v135; // edi
  Item::ItemType::Type *v136; // edx
  unsigned int v137; // ecx
  Item::ItemType::Type *v138; // edi
  Item::ItemType::Type *v139; // edx
  unsigned int v140; // ecx
  Item::ItemType::Type *v141; // edi
  Item::ItemType::Type *v142; // edx
  unsigned int v143; // ecx
  Item::ItemType::Type *v144; // edi
  Item::ItemType::Type *v145; // edx
  unsigned int v146; // ecx
  Item::ItemType::Type *v147; // edi
  int v148; // edi
  CLotteryEvent::LotteryEventPrize _Val; // [esp+10h] [ebp-18h] BYREF
  std::_Nonscalar_ptr_iterator_tag __formal[4]; // [esp+18h] [ebp-10h]
  int v151; // [esp+24h] [ebp-4h]

  *(_DWORD *)__formal = this;
  CSingleton<Item::CItemType>::ms_pSingleton = this;
  v151 = 1;
  `eh vector constructor iterator'(
    (char *)this,
    0x10u,
    7,
    (void (__thiscall *)(void *))std::vector<enum Item::ItemType::Type>::vector<enum Item::ItemType::Type>,
    (void (__thiscall *)(void *))std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>);
  m_ItemTypeNames = this->m_ItemTypeNames;
  v3 = 57;
  do
  {
    m_ItemTypeNames->m_nType = 0;
    m_ItemTypeNames->m_lpszName = 0;
    ++m_ItemTypeNames;
    --v3;
  }
  while ( v3 );
  v4 = this;
  v5 = 7;
  do
  {
    std::vector<enum Item::ItemType::Type>::reserve(v4->m_ItemTypes, 0x39u);
    v4 = (Item::CItemType *)((char *)v4 + 16);
    --v5;
  }
  while ( v5 );
  LOBYTE(_Val.m_usPrizeID) = 0;
  v6 = _Val;
  this->m_ItemTypeNames[0].m_lpszName = "HELM";
  LOBYTE(_Val.m_usPrizeID) = 1;
  v7 = _Val;
  this->m_ItemTypeNames[1].m_lpszName = "SHIRT";
  LOBYTE(_Val.m_usPrizeID) = 2;
  this->m_ItemTypeNames[2].m_lpszName = "TUNIC";
  this->m_ItemTypeNames[3].m_lpszName = "ARMOUR";
  this->m_ItemTypeNames[4].m_lpszName = "GLOVE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[0].m_nType = v6;
  v8 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 3;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[1].m_nType = v7;
  v9 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 4;
  this->m_ItemTypeNames[5].m_lpszName = "BOOTS";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[2].m_nType = v8;
  v10 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 5;
  this->m_ItemTypeNames[6].m_lpszName = "ONEHANDED_SWORD";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[3].m_nType = v9;
  v11 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 6;
  this->m_ItemTypeNames[7].m_lpszName = "ONEHANDED_BLUNT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[4].m_nType = v10;
  v12 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 7;
  this->m_ItemTypeNames[8].m_lpszName = "ONEHANDED_AXE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[5].m_nType = v11;
  v13 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 8;
  this->m_ItemTypeNames[9].m_lpszName = "TWOHANDED_BLUNT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[6].m_nType = v12;
  v14 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 9;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[7].m_nType = v13;
  v15 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 10;
  this->m_ItemTypeNames[10].m_lpszName = "TWOHANDED_AXE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[8].m_nType = v14;
  v16 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 11;
  this->m_ItemTypeNames[11].m_lpszName = "TWOHANDED_SWORD";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[9].m_nType = v15;
  v17 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 12;
  this->m_ItemTypeNames[12].m_lpszName = "BOW";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[10].m_nType = v16;
  v18 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 13;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[11].m_nType = v17;
  v19 = _Val;
  this->m_ItemTypeNames[13].m_lpszName = "CROSSBOW";
  LOBYTE(_Val.m_usPrizeID) = 14;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[12].m_nType = v18;
  v20 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 15;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[13].m_nType = v19;
  v21 = _Val;
  this->m_ItemTypeNames[14].m_lpszName = "STAFF";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[14].m_nType = v20;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[15].m_nType = v21;
  this->m_ItemTypeNames[15].m_lpszName = "DAGGER";
  LOBYTE(_Val.m_usPrizeID) = 16;
  v22 = _Val;
  this->m_ItemTypeNames[16].m_lpszName = "SHIELD";
  LOBYTE(_Val.m_usPrizeID) = 17;
  v23 = _Val;
  this->m_ItemTypeNames[17].m_lpszName = "CLOAK";
  LOBYTE(_Val.m_usPrizeID) = 18;
  this->m_ItemTypeNames[18].m_lpszName = "RING";
  this->m_ItemTypeNames[19].m_lpszName = "NECKLACE";
  this->m_ItemTypeNames[20].m_lpszName = "POTION";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[16].m_nType = v22;
  v24 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 19;
  this->m_ItemTypeNames[21].m_lpszName = "POISON";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[17].m_nType = v23;
  v25 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 20;
  this->m_ItemTypeNames[22].m_lpszName = "TRAP";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[18].m_nType = v24;
  v26 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 21;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[19].m_nType = v25;
  v27 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 22;
  this->m_ItemTypeNames[23].m_lpszName = "SKILLBOOK";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[20].m_nType = v26;
  v28 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 23;
  this->m_ItemTypeNames[24].m_lpszName = "GEM";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[21].m_nType = v27;
  v29 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 24;
  this->m_ItemTypeNames[25].m_lpszName = "RUNE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[22].m_nType = v28;
  v30 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 25;
  this->m_ItemTypeNames[26].m_lpszName = "ORE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[23].m_nType = v29;
  v31 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 26;
  this->m_ItemTypeNames[27].m_lpszName = "AMMO";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[24].m_nType = v30;
  v32 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 27;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[25].m_nType = v31;
  v33 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 28;
  this->m_ItemTypeNames[28].m_lpszName = "ETC";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[26].m_nType = v32;
  v34 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 29;
  this->m_ItemTypeNames[29].m_lpszName = "HEAD";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[27].m_nType = v33;
  v35 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 30;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[28].m_nType = v34;
  v36 = _Val;
  this->m_ItemTypeNames[30].m_lpszName = "BODY";
  LOBYTE(_Val.m_usPrizeID) = 31;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[29].m_nType = v35;
  v37 = _Val;
  this->m_ItemTypeNames[31].m_lpszName = "PROTECT_A";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[30].m_nType = v36;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[31].m_nType = v37;
  LOBYTE(_Val.m_usPrizeID) = 32;
  v38 = _Val;
  this->m_ItemTypeNames[32].m_lpszName = "PELVIS";
  LOBYTE(_Val.m_usPrizeID) = 33;
  v39 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 34;
  this->m_ItemTypeNames[33].m_lpszName = "COM_BLUNT";
  this->m_ItemTypeNames[34].m_lpszName = "COM_SWORD";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[32].m_nType = v38;
  v40 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 35;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[33].m_nType = v39;
  v41 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 36;
  this->m_ItemTypeNames[35].m_lpszName = "OPP_HAMMER";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[34].m_nType = v40;
  v42 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 37;
  this->m_ItemTypeNames[36].m_lpszName = "OPP_AXE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[35].m_nType = v41;
  v43 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 38;
  this->m_ItemTypeNames[37].m_lpszName = "OPP_SLUSHER";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[36].m_nType = v42;
  v44 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 39;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[37].m_nType = v43;
  v45 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 40;
  this->m_ItemTypeNames[38].m_lpszName = "OPP_TALON";
  this->m_ItemTypeNames[39].m_lpszName = "OPP_SYTHE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[38].m_nType = v44;
  v46 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 41;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[39].m_nType = v45;
  v47 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 42;
  this->m_ItemTypeNames[40].m_lpszName = "SKILL_A_GUARD";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[40].m_nType = v46;
  v48 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 43;
  this->m_ItemTypeNames[41].m_lpszName = "SKILL_A_ATTACK";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[41].m_nType = v47;
  v49 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 44;
  this->m_ItemTypeNames[42].m_lpszName = "SKILL_A_GUN";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[42].m_nType = v48;
  v50 = _Val;
  this->m_ItemTypeNames[43].m_lpszName = "SKILL_A_KNIFE";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[44].m_nType = v50;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[43].m_nType = v49;
  this->m_ItemTypeNames[44].m_lpszName = "ACCESSORY";
  LOBYTE(_Val.m_usPrizeID) = 45;
  v51 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 46;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[45].m_nType = v51;
  v52 = _Val;
  this->m_ItemTypeNames[45].m_lpszName = "ARROW";
  LOBYTE(_Val.m_usPrizeID) = 47;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[46].m_nType = v52;
  v53 = _Val;
  this->m_ItemTypeNames[46].m_lpszName = "BOLT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[47].m_nType = v53;
  this->m_ItemTypeNames[47].m_lpszName = "PORTAL";
  LOBYTE(_Val.m_usPrizeID) = 48;
  v54 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 49;
  this->m_ItemTypeNames[48].m_lpszName = "EVENT_DROP";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[48].m_nType = v54;
  v55 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 50;
  this->m_ItemTypeNames[49].m_lpszName = "EVENT_LOTTERY";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[49].m_nType = v55;
  v56 = _Val;
  this->m_ItemTypeNames[50].m_lpszName = "EXPBOOK";
  LOBYTE(_Val.m_usPrizeID) = 51;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[50].m_nType = v56;
  v57 = _Val;
  this->m_ItemTypeNames[51].m_lpszName = "CASHBOOK";
  LOBYTE(_Val.m_usPrizeID) = 52;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[51].m_nType = v57;
  v58 = _Val;
  this->m_ItemTypeNames[52].m_lpszName = "FIRE_CRACKER";
  LOBYTE(_Val.m_usPrizeID) = 53;
  this->m_ItemTypeNames[53].m_lpszName = "CAMP_KIT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[52].m_nType = v58;
  v59 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 54;
  this->m_ItemTypeNames[54].m_lpszName = "SHORT_RANGE_ARMS_KIT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[53].m_nType = v59;
  v60 = _Val;
  LOBYTE(_Val.m_usPrizeID) = 55;
  this->m_ItemTypeNames[55].m_lpszName = "LONG_RANGE_ARMS_KIT";
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[54].m_nType = v60;
  v61 = _Val;
  p_Myfirst = (std::vector<enum Item::ItemType::Type>::iterator *)&this->m_ItemTypes[0]._Myfirst;
  LOBYTE(_Val.m_usPrizeID) = 56;
  this->m_ItemTypeNames[56].m_lpszName = "AIRSHIP_KIT";
  Myfirst = this->m_ItemTypes[0]._Myfirst;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[55].m_nType = v61;
  *(CLotteryEvent::LotteryEventPrize *)&this->m_ItemTypeNames[56].m_nType = _Val;
  _Val = (CLotteryEvent::LotteryEventPrize)45;
  if ( Myfirst )
    v64 = this->m_ItemTypes[0]._Mylast - Myfirst;
  else
    v64 = 0;
  if ( Myfirst && v64 < this->m_ItemTypes[0]._Myend - Myfirst )
  {
    Mylast = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)Mylast,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = Mylast + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v66._Myptr = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)46;
  if ( v67 )
    v68 = 0;
  else
    v68 = this->m_ItemTypes[0]._Mylast - v66._Myptr;
  if ( v66._Myptr && v68 < this->m_ItemTypes[0]._Myend - v66._Myptr )
  {
    v69 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v69,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v69 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  Myptr = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = 0;
  if ( v67 )
    v71 = 0;
  else
    v71 = this->m_ItemTypes[0]._Mylast - Myptr;
  if ( Myptr && v71 < this->m_ItemTypes[0]._Myend - Myptr )
  {
    v72 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v72,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v72 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v73 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)1;
  if ( v67 )
    v74 = 0;
  else
    v74 = this->m_ItemTypes[0]._Mylast - v73;
  if ( v73 && v74 < this->m_ItemTypes[0]._Myend - v73 )
  {
    v75 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v75,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v75 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v76 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)2;
  if ( v67 )
    v77 = 0;
  else
    v77 = this->m_ItemTypes[0]._Mylast - v76;
  if ( v76 && v77 < this->m_ItemTypes[0]._Myend - v76 )
  {
    v78 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v78,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v78 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v79 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)3;
  if ( v67 )
    v80 = 0;
  else
    v80 = this->m_ItemTypes[0]._Mylast - v79;
  if ( v79 && v80 < this->m_ItemTypes[0]._Myend - v79 )
  {
    v81 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v81,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v81 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v82 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)16;
  if ( v67 )
    v83 = 0;
  else
    v83 = this->m_ItemTypes[0]._Mylast - v82;
  if ( v82 && v83 < this->m_ItemTypes[0]._Myend - v82 )
  {
    v84 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v84,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v84 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v85 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)17;
  if ( v67 )
    v86 = 0;
  else
    v86 = this->m_ItemTypes[0]._Mylast - v85;
  if ( v85 && v86 < this->m_ItemTypes[0]._Myend - v85 )
  {
    v87 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v87,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v87 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v88 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)4;
  if ( v67 )
    v89 = 0;
  else
    v89 = this->m_ItemTypes[0]._Mylast - v88;
  if ( v88 && v89 < this->m_ItemTypes[0]._Myend - v88 )
  {
    v90 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v90,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v90 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v91 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)5;
  if ( v67 )
    v92 = 0;
  else
    v92 = this->m_ItemTypes[0]._Mylast - v91;
  if ( v91 && v92 < this->m_ItemTypes[0]._Myend - v91 )
  {
    v93 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v93,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v93 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v94 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)6;
  if ( v67 )
    v95 = 0;
  else
    v95 = this->m_ItemTypes[0]._Mylast - v94;
  if ( v94 && v95 < this->m_ItemTypes[0]._Myend - v94 )
  {
    v96 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v96,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v96 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v97 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)7;
  if ( v67 )
    v98 = 0;
  else
    v98 = this->m_ItemTypes[0]._Mylast - v97;
  if ( v97 && v98 < this->m_ItemTypes[0]._Myend - v97 )
  {
    v99 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v99,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v99 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v100 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)8;
  if ( v67 )
    v101 = 0;
  else
    v101 = this->m_ItemTypes[0]._Mylast - v100;
  if ( v100 && v101 < this->m_ItemTypes[0]._Myend - v100 )
  {
    v102 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v102,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v102 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v103 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)15;
  if ( v67 )
    v104 = 0;
  else
    v104 = this->m_ItemTypes[0]._Mylast - v103;
  if ( v103 && v104 < this->m_ItemTypes[0]._Myend - v103 )
  {
    v105 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v105,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v105 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v106 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)14;
  if ( v67 )
    v107 = 0;
  else
    v107 = this->m_ItemTypes[0]._Mylast - v106;
  if ( v106 && v107 < this->m_ItemTypes[0]._Myend - v106 )
  {
    v108 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v108,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v108 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v109 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)9;
  if ( v67 )
    v110 = 0;
  else
    v110 = this->m_ItemTypes[0]._Mylast - v109;
  if ( v109 && v110 < this->m_ItemTypes[0]._Myend - v109 )
  {
    v111 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v111,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v111 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v112 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)10;
  if ( v67 )
    v113 = 0;
  else
    v113 = this->m_ItemTypes[0]._Mylast - v112;
  if ( v112 && v113 < this->m_ItemTypes[0]._Myend - v112 )
  {
    v114 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v114,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v114 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v115 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)11;
  if ( v67 )
    v116 = 0;
  else
    v116 = this->m_ItemTypes[0]._Mylast - v115;
  if ( v115 && v116 < this->m_ItemTypes[0]._Myend - v115 )
  {
    v117 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v117,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v117 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v118 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)12;
  if ( v67 )
    v119 = 0;
  else
    v119 = this->m_ItemTypes[0]._Mylast - v118;
  if ( v118 && v119 < this->m_ItemTypes[0]._Myend - v118 )
  {
    v120 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v120,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v120 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v121 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)13;
  if ( v67 )
    v122 = 0;
  else
    v122 = this->m_ItemTypes[0]._Mylast - v121;
  if ( v121 && v122 < this->m_ItemTypes[0]._Myend - v121 )
  {
    v123 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v123,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v123 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v124 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)18;
  if ( v67 )
    v125 = 0;
  else
    v125 = this->m_ItemTypes[0]._Mylast - v124;
  if ( v124 && v125 < this->m_ItemTypes[0]._Myend - v124 )
  {
    v126 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v126,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v126 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v127 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)19;
  if ( v67 )
    v128 = 0;
  else
    v128 = this->m_ItemTypes[0]._Mylast - v127;
  if ( v127 && v128 < this->m_ItemTypes[0]._Myend - v127 )
  {
    v129 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v129,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v129 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v130 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)29;
  if ( v67 )
    v131 = 0;
  else
    v131 = this->m_ItemTypes[0]._Mylast - v130;
  if ( v130 && v131 < this->m_ItemTypes[0]._Myend - v130 )
  {
    v132 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v132,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v132 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v133 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)30;
  if ( v67 )
    v134 = 0;
  else
    v134 = this->m_ItemTypes[0]._Mylast - v133;
  if ( v133 && v134 < this->m_ItemTypes[0]._Myend - v133 )
  {
    v135 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v135,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v135 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v136 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)32;
  if ( v67 )
    v137 = 0;
  else
    v137 = this->m_ItemTypes[0]._Mylast - v136;
  if ( v136 && v137 < this->m_ItemTypes[0]._Myend - v136 )
  {
    v138 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v138,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v138 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v139 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)31;
  if ( v67 )
    v140 = 0;
  else
    v140 = this->m_ItemTypes[0]._Mylast - v139;
  if ( v139 && v140 < this->m_ItemTypes[0]._Myend - v139 )
  {
    v141 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v141,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v141 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v142 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)33;
  if ( v67 )
    v143 = 0;
  else
    v143 = this->m_ItemTypes[0]._Mylast - v142;
  if ( v142 && v143 < this->m_ItemTypes[0]._Myend - v142 )
  {
    v144 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v144,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v144 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  v145 = p_Myfirst->_Myptr;
  v67 = p_Myfirst->_Myptr == 0;
  _Val = (CLotteryEvent::LotteryEventPrize)34;
  if ( v67 )
    v146 = 0;
  else
    v146 = this->m_ItemTypes[0]._Mylast - v145;
  if ( v145 && v146 < this->m_ItemTypes[0]._Myend - v145 )
  {
    v147 = this->m_ItemTypes[0]._Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
      (CLotteryEvent::LotteryEventPrize *)v147,
      1u,
      &_Val);
    this->m_ItemTypes[0]._Mylast = v147 + 1;
  }
  else
  {
    std::vector<enum Item::ItemType::Type>::_Insert_n(
      this->m_ItemTypes,
      (std::vector<enum Item::ItemType::Type>::iterator)this->m_ItemTypes[0]._Mylast,
      1u,
      (const Item::ItemType::Type **)&_Val);
  }
  _Val = (CLotteryEvent::LotteryEventPrize)35;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)36;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)37;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)38;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)39;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)40;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)41;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)42;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)43;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)44;
  std::vector<enum Item::ItemType::Type>::push_back(this->m_ItemTypes, (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)20;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)21;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)22;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)23;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)27;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)47;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)49;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)50;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)51;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)52;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)53;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)54;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)55;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)56;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[1], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)6;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)7;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)8;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)15;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)14;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)9;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)10;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)11;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)12;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)13;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)33;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)34;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)35;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)36;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)37;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)38;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)39;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[2], (Item::ItemType::Type *)&_Val);
  _Val = 0;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)1;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)2;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)3;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)16;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)17;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)4;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)5;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)29;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)30;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)32;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)31;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[3], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)40;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[4], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)41;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[4], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)42;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[4], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)43;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[4], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)14;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)9;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)10;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)11;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)12;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)13;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[5], (Item::ItemType::Type *)&_Val);
  _Val = 0;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[6], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)18;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[6], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)19;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[6], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)29;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[6], (Item::ItemType::Type *)&_Val);
  _Val = (CLotteryEvent::LotteryEventPrize)44;
  std::vector<enum Item::ItemType::Type>::push_back(&this->m_ItemTypes[6], (Item::ItemType::Type *)&_Val);
  v148 = 7;
  do
  {
    std::_Sort<std::vector<enum Item::ItemType::Type>::iterator,int>(
      (std::vector<enum Item::ItemType::Type>::iterator)p_Myfirst->_Myptr,
      p_Myfirst[1],
      p_Myfirst[1]._Myptr - p_Myfirst->_Myptr);
    p_Myfirst += 4;
    --v148;
  }
  while ( v148 );
}

//----- (00496AD0) --------------------------------------------------------
char __cdecl ReadString(char *szBuffer_Out, int nBufferLen, const char *szValue_In)
{
  int v3; // eax

  v3 = _snprintf(szBuffer_Out, nBufferLen - 1, "%s", szValue_In);
  if ( v3 < 0 )
  {
    szBuffer_Out[nBufferLen - 1] = 0;
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "ReadString", aDWorkRylSource_90, 26, (char *)&byte_4F5F68, szValue_In);
    return 0;
  }
  else
  {
    szBuffer_Out[v3] = 0;
    return 1;
  }
}

//----- (00496B30) --------------------------------------------------------
char __cdecl ReadStringToTypeValue(
        unsigned __int8 *cVaule_Out,
        const CTypeName *lpTypeArray,
        unsigned __int8 nMaxType,
        const char *szValue_In)
{
  unsigned __int8 v4; // dl

  v4 = 0;
  if ( !nMaxType )
    return 0;
  while ( strcmp(szValue_In, lpTypeArray[v4].m_lpszName) )
  {
    if ( ++v4 >= nMaxType )
      return 0;
  }
  *cVaule_Out = lpTypeArray[v4].m_nType;
  return 1;
}

//----- (00496BB0) --------------------------------------------------------
char __cdecl ReadID(Item::ItemInfo *itemInfo, const char *szValue)
{
  unsigned __int16 v2; // ax

  v2 = atoi(szValue);
  itemInfo->m_usProtoTypeID = v2;
  if ( v2 && v2 != 0xFFFF )
    return 1;
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "ReadID", aDWorkRylSource_90, 79, (char *)&byte_4F5FB0, v2);
  return 0;
}

//----- (00496C00) --------------------------------------------------------
char __cdecl ReadItemName(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_SpriteData.m_szName, 64, szValue);
}

//----- (00496C20) --------------------------------------------------------
char __cdecl ReadFieldModelName(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_StringData.m_szFieldModelName, 32, szValue);
}

//----- (00496C40) --------------------------------------------------------
char __cdecl ReadAttachedModelName(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_StringData.m_szEquipModelName, 32, szValue);
}

//----- (00496C60) --------------------------------------------------------
char __cdecl ReadSpriteDDS(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_SpriteData.m_szSpriteName, 32, szValue);
}

//----- (00496C80) --------------------------------------------------------
char __cdecl ReadMinX(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_SpriteData.m_nSpriteMinX = atoi(szValue);
  return 1;
}

//----- (00496CA0) --------------------------------------------------------
char __cdecl ReadMinY(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_SpriteData.m_nSpriteMinY = atoi(szValue);
  return 1;
}

//----- (00496CC0) --------------------------------------------------------
char __cdecl ReadMaxX(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_SpriteData.m_nSpriteMaxX = atoi(szValue);
  return 1;
}

//----- (00496CE0) --------------------------------------------------------
char __cdecl ReadMaxY(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_SpriteData.m_nSpriteMaxY = atoi(szValue);
  return 1;
}

//----- (00496D00) --------------------------------------------------------
char __cdecl ReadSizeX(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cXSize = atoi(szValue);
  return 1;
}

//----- (00496D20) --------------------------------------------------------
char __cdecl ReadSizeY(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cYSize = atoi(szValue);
  return 1;
}

//----- (00496D40) --------------------------------------------------------
char __cdecl ReadEffectSound(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_StringData.m_szEffectSoundName, 32, szValue);
}

//----- (00496D60) --------------------------------------------------------
char __cdecl ReadItemLevel(Item::ItemInfo *itemInfo, const char *szValue)
{
  unsigned __int8 cGrade; // [esp+3h] [ebp-1h] BYREF

  cGrade = 0;
  if ( !ReadStringToTypeValue(&cGrade, Item::Grade::Grades, 4u, szValue) )
    return 0;
  itemInfo->m_DetailData.m_ItemGrade = cGrade;
  return 1;
}

//----- (00496DA0) --------------------------------------------------------
char __cdecl ReadTypeName(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_StringData.m_szTypeName, 32, szValue);
}

//----- (00496DC0) --------------------------------------------------------
char __cdecl ReadPrice(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_dwPrice = atol(szValue);
  return 1;
}

//----- (00496DE0) --------------------------------------------------------
char __cdecl ReadBlackPrice(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_dwBlackPrice = atol(szValue);
  return 1;
}

//----- (00496E00) --------------------------------------------------------
char __cdecl ReadMedalPrice(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_dwMedalPrice = atol(szValue);
  return 1;
}

//----- (00496E20) --------------------------------------------------------
char __cdecl ReadOptionLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cOptionLimit = atoi(szValue);
  return 1;
}

//----- (00496E40) --------------------------------------------------------
char __cdecl ReadDurability(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cDefaultDurabilityOrStack = atoi(szValue);
  return 1;
}

//----- (00496E60) --------------------------------------------------------
char __cdecl ReadMaxDurability(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cMaxDurabilityOrStack = atoi(szValue);
  return 1;
}

//----- (00496E80) --------------------------------------------------------
char __cdecl ReadClassLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_dwClassLimit = atol(szValue);
  return 1;
}

//----- (00496EA0) --------------------------------------------------------
char __cdecl ReadSkillLimitType(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nSkillType = atoi(szValue);
  return 1;
}

//----- (00496EC0) --------------------------------------------------------
char __cdecl ReadSkillLimitLevel(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_cSkillLevel = atoi(szValue);
  return 1;
}

//----- (00496EE0) --------------------------------------------------------
char __cdecl ReadLevelLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_cLevelLimit = atoi(szValue);
  return 1;
}

//----- (00496F00) --------------------------------------------------------
char __cdecl ReadSTRLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nStatusLimit[2] = atoi(szValue);
  return 1;
}

//----- (00496F20) --------------------------------------------------------
char __cdecl ReadDEXLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nStatusLimit[3] = atoi(szValue);
  return 1;
}

//----- (00496F40) --------------------------------------------------------
char __cdecl ReadCONLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nStatusLimit[4] = atoi(szValue);
  return 1;
}

//----- (00496F60) --------------------------------------------------------
char __cdecl ReadINTLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nStatusLimit[5] = atoi(szValue);
  return 1;
}

//----- (00496F80) --------------------------------------------------------
char __cdecl ReadWISLimit(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseLimit.m_nStatusLimit[6] = atoi(szValue);
  return 1;
}

//----- (00496FA0) --------------------------------------------------------
char __cdecl ReadCraftExp(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_dwCraftExp = atol(szValue);
  return 1;
}

//----- (00496FC0) --------------------------------------------------------
char __cdecl ReadCriticalType(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[1] = atoi(szValue);
  return 1;
}

//----- (00496FF0) --------------------------------------------------------
char __cdecl ReadCriticalPercentage(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[2] = atoi(szValue);
  return 1;
}

//----- (00497020) --------------------------------------------------------
char __cdecl ReadMinDamage(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[3] = atoi(szValue);
  return 1;
}

//----- (00497050) --------------------------------------------------------
char __cdecl ReadMaxDamage(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[4] = atoi(szValue);
  return 1;
}

//----- (00497080) --------------------------------------------------------
char __cdecl ReadDRC(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[5] = atoi(szValue);
  return 1;
}

//----- (004970B0) --------------------------------------------------------
char __cdecl ReadOffenceRevision(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[6] = atoi(szValue);
  return 1;
}

//----- (004970E0) --------------------------------------------------------
char __cdecl ReadDefence(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[7] = atoi(szValue);
  return 1;
}

//----- (00497110) --------------------------------------------------------
char __cdecl ReadDefenceRevision(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[8] = atoi(szValue);
  return 1;
}

//----- (00497140) --------------------------------------------------------
char __cdecl ReadResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[9] = atoi(szValue);
  return 1;
}

//----- (00497170) --------------------------------------------------------
char __cdecl ReadBlockingPercentage(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[10] = atoi(szValue);
  return 1;
}

//----- (004971A0) --------------------------------------------------------
char __cdecl ReadAttackSpeed(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[11] = atoi(szValue);
  return 1;
}

//----- (004971D0) --------------------------------------------------------
char __cdecl ReadMoveSpeed(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[12] = atoi(szValue);
  return 1;
}

//----- (00497200) --------------------------------------------------------
char __cdecl ReadAttackRange(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[13] = atoi(szValue);
  return 1;
}

//----- (00497230) --------------------------------------------------------
char __cdecl ReadRangedAttack(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[14] = atoi(szValue);
  return 1;
}

//----- (00497260) --------------------------------------------------------
char __cdecl ReadHpMaxPlus(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[15] = atoi(szValue);
  return 1;
}

//----- (00497290) --------------------------------------------------------
char __cdecl ReadMpMaxPlus(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[16] = atoi(szValue);
  return 1;
}

//----- (004972C0) --------------------------------------------------------
char __cdecl ReadHPRegen(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[17] = atoi(szValue);
  return 1;
}

//----- (004972F0) --------------------------------------------------------
char __cdecl ReadMPRegen(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[18] = atoi(szValue);
  return 1;
}

//----- (00497320) --------------------------------------------------------
char __cdecl ReadFireAttribute(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[19] = atoi(szValue);
  return 1;
}

//----- (00497350) --------------------------------------------------------
char __cdecl ReadLightingAttribute(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[20] = atoi(szValue);
  return 1;
}

//----- (00497380) --------------------------------------------------------
char __cdecl ReadColdAttribute(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[21] = atoi(szValue);
  return 1;
}

//----- (004973B0) --------------------------------------------------------
char __cdecl ReadDrainAttribute(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[22] = atoi(szValue);
  return 1;
}

//----- (004973E0) --------------------------------------------------------
char __cdecl ReadPoisonAttribute(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[23] = atoi(szValue);
  return 1;
}

//----- (00497410) --------------------------------------------------------
char __cdecl ReadFireResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[24] = atoi(szValue);
  return 1;
}

//----- (00497440) --------------------------------------------------------
char __cdecl ReadLightningResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[25] = atoi(szValue);
  return 1;
}

//----- (00497470) --------------------------------------------------------
char __cdecl ReadColdResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[26] = atoi(szValue);
  return 1;
}

//----- (004974A0) --------------------------------------------------------
char __cdecl ReadDrainResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[27] = atoi(szValue);
  return 1;
}

//----- (004974D0) --------------------------------------------------------
char __cdecl ReadPoisonResistance(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[28] = atoi(szValue);
  return 1;
}

//----- (00497500) --------------------------------------------------------
char __cdecl ReadSTRAdd(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[29] = atoi(szValue);
  return 1;
}

//----- (00497530) --------------------------------------------------------
char __cdecl ReadDEXAdd(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[30] = atoi(szValue);
  return 1;
}

//----- (00497560) --------------------------------------------------------
char __cdecl ReadCONAdd(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[31] = atoi(szValue);
  return 1;
}

//----- (00497590) --------------------------------------------------------
char __cdecl ReadINTAdd(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[32] = atoi(szValue);
  return 1;
}

//----- (004975C0) --------------------------------------------------------
char __cdecl ReadWISAdd(Item::ItemInfo *itemInfo, const char *szValue)
{
  ++itemInfo->m_EquipAttribute.m_nAttibuteNum;
  itemInfo->m_EquipAttribute.m_usAttributeValue[33] = atoi(szValue);
  return 1;
}

//----- (004975F0) --------------------------------------------------------
char __cdecl ReadMaterialValue(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cMaterialType = atoi(szValue);
  return 1;
}

//----- (00497610) --------------------------------------------------------
char __cdecl ReadMaxSocketNum(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_DetailData.m_cMaxSocketNum = atoi(szValue);
  return 1;
}

//----- (00497630) --------------------------------------------------------
char __cdecl ReadSkill_Level(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_usSkill_LockCount = atoi(szValue);
  return 1;
}



//----- (00497650) --------------------------------------------------------
char __cdecl ReadZone(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_cZone = atoi(szValue);
  return 1;
}

//----- (00497670) --------------------------------------------------------
char __cdecl ReadPositionX(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_Pos.m_fPointX = atof(szValue);
  return 1;
}

//----- (00497690) --------------------------------------------------------
char __cdecl ReadPositionY(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_Pos.m_fPointY = atof(szValue);
  return 1;
}

//----- (004976B0) --------------------------------------------------------
char __cdecl ReadPositionZ(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_Pos.m_fPointZ = atof(szValue);
  return 1;
}

//----- (004976D0) --------------------------------------------------------
char __cdecl ReadAmount(Item::ItemInfo *itemInfo, const char *szValue)
{
  itemInfo->m_UseItemInfo.m_dwAmount = atol(szValue);
  return 1;
}

//----- (004976F0) --------------------------------------------------------
char __cdecl ReadItemDescribe(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadString(itemInfo->m_StringData.m_szItemDescribe, 256, szValue);
}

//----- (00497710) --------------------------------------------------------
char __cdecl ReadItemType(Item::ItemInfo *itemInfo, const char *szValue)
{
  return ReadStringToTypeValue(
           &itemInfo->m_DetailData.m_cItemType,
           CSingleton<Item::CItemType>::ms_pSingleton->m_ItemTypeNames,
           0x39u,
           szValue);
}

//----- (00497740) --------------------------------------------------------
char __cdecl ReadSkill_ID(Item::ItemInfo *itemInfo, char *szValue)
{
  int v2; // ecx
  int v3; // eax
  unsigned __int16 v4; // ax

  _mbsnbcmp((unsigned __int8 *)szValue, "0x", 2u);
  if ( v3 )
    v4 = atoi(szValue);
  else
    v4 = Math::Convert::StrToHex16(0, v2, (int)szValue, szValue);
  itemInfo->m_UseItemInfo.m_usSkill_ID = v4;
  return 1;
}
// 497758: variable 'v3' is possibly undefined
// 49775A: variable 'v2' is possibly undefined

//----- (00497790) --------------------------------------------------------
std::pair<unsigned long,unsigned long> *__cdecl std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(
        std::pair<unsigned long,unsigned long> *_First,
        std::pair<unsigned long,unsigned long> *_Last,
        std::pair<unsigned long,unsigned long> *_Dest)
{
  std::pair<unsigned long,unsigned long> *v3; // ecx
  std::pair<unsigned long,unsigned long> *result; // eax
  unsigned int first; // esi

  v3 = _Last;
  for ( result = _Dest; v3 != _First; result->second = v3->second )
  {
    first = v3[-1].first;
    --v3;
    --result;
    result->first = first;
  }
  return result;
}

//----- (004977C0) --------------------------------------------------------
void __thiscall std::vector<ItemDataParser::ParseData>::_Insert_n(
        std::vector<ItemDataParser::ParseData> *this,
        std::vector<ItemDataParser::ParseData>::iterator _Where,
        unsigned int _Count,
        const ItemDataParser::ParseData *_Val)
{
  bool (__cdecl *m_fnParseFunc)(Item::ItemInfo *, const char *); // edx
  ItemDataParser::ParseData *Myfirst; // ecx
  unsigned int v7; // eax
  int v9; // edx
  int v10; // edx
  unsigned int v11; // eax
  int v12; // edx
  int v13; // eax
  ItemDataParser::ParseData *v14; // edi
  ItemDataParser::ParseData *v15; // ecx
  int v16; // eax
  int v17; // ebx
  std::pair<unsigned long,unsigned long> *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  ItemDataParser::ParseData *v22; // ebx
  std::pair<unsigned long,unsigned long> *v23; // ebx
  std::pair<unsigned long,unsigned long> *v24; // [esp-18h] [ebp-40h]
  std::pair<unsigned long,unsigned long> *v25; // [esp-Ch] [ebp-34h]
  unsigned int v26; // [esp-8h] [ebp-30h]
  int v27; // [esp+0h] [ebp-28h] BYREF
  ItemDataParser::ParseData _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  ItemDataParser::ParseData *_Newvec; // [esp+14h] [ebp-14h]
  int *v30; // [esp+18h] [ebp-10h]
  int v31; // [esp+24h] [ebp-4h]
  std::vector<ItemDataParser::ParseData>::iterator _Wherea; // [esp+30h] [ebp+8h]
  unsigned int _Counta; // [esp+34h] [ebp+Ch]
  std::pair<unsigned long,unsigned long> *_Valb; // [esp+38h] [ebp+10h]
  std::pair<unsigned long,unsigned long> *_Vala; // [esp+38h] [ebp+10h]

  m_fnParseFunc = _Val->m_fnParseFunc;
  _Tmp.m_szColumnName = _Val->m_szColumnName;
  Myfirst = this->_Myfirst;
  v30 = &v27;
  _Tmp.m_fnParseFunc = m_fnParseFunc;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x1FFFFFFF - v9 < _Count )
      std::vector<ItemDataParser::ParseData>::_Xlen((std::vector<Item::ChemicalInfo> *)this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
      v20 = ((char *)Mylast - (char *)_Where._Myptr) >> 3 < _Count;
      v21 = 8 * _Count;
      _Wherea._Myptr = (ItemDataParser::ParseData *)(8 * _Count);
      _Vala = Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          Mylast,
          (std::pair<unsigned long,unsigned long> *)&_Where._Myptr[v21 / 8]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
        v31 = 2;
        std::vector<std::pair<enum eStdFunc,int>>::_Ufill(
          (std::vector<std::pair<unsigned long,unsigned long>> *)this,
          v25,
          v26,
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
        v22 = (ItemDataParser::ParseData *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)v22 - (char *)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
      else
      {
        v23 = &Mylast[v21 / 0xFFFFFFF8];
        this->_Mylast = (ItemDataParser::ParseData *)std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                                                       &Mylast[v21 / 0xFFFFFFF8],
                                                       Mylast,
                                                       Mylast);
        std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          v23,
          _Vala);
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
          (std::pair<unsigned long,unsigned long> *)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      }
    }
    else
    {
      if ( 0x1FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      _Counta = v11;
      v14 = (ItemDataParser::ParseData *)operator new((tagHeader *)(8 * v11));
      v24 = (std::pair<unsigned long,unsigned long> *)this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Valb = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                v24,
                (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
                (std::pair<unsigned long,unsigned long> *)v14);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        _Valb,
        _Count,
        (const std::pair<unsigned long,unsigned long> *)&_Tmp);
      std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        (std::pair<unsigned long,unsigned long> *)_Where._Myptr,
        (std::pair<unsigned long,unsigned long> *)this->_Mylast,
        &_Valb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[_Counta];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (00497A10) --------------------------------------------------------
void __thiscall std::vector<ItemDataParser::ParseData>::reserve(
        std::vector<ItemDataParser::ParseData> *this,
        unsigned int _Count)
{
  ItemDataParser::ParseData *Myfirst; // ecx
  int v4; // ebx
  unsigned int v5; // eax
  ItemDataParser::ParseData *v6; // edi
  ItemDataParser::ParseData *v7; // eax
  std::pair<unsigned long,unsigned long> *v8; // [esp-18h] [ebp-38h]
  std::pair<unsigned long,unsigned long> *Mylast; // [esp-14h] [ebp-34h]
  int v10; // [esp+0h] [ebp-20h] BYREF
  ItemDataParser::ParseData *_Ptr; // [esp+Ch] [ebp-14h]
  int *v12; // [esp+10h] [ebp-10h]
  int v13; // [esp+1Ch] [ebp-4h]
  tagHeader *_Counta; // [esp+28h] [ebp+8h]

  v12 = &v10;
  if ( _Count > 0x1FFFFFFF )
    std::vector<ItemDataParser::ParseData>::_Xlen((std::vector<Item::ChemicalInfo> *)this);
  Myfirst = this->_Myfirst;
  v4 = 0;
  if ( Myfirst )
    v5 = this->_Myend - Myfirst;
  else
    v5 = 0;
  if ( v5 < _Count )
  {
    _Counta = (tagHeader *)(8 * _Count);
    v6 = (ItemDataParser::ParseData *)operator new(_Counta);
    Mylast = (std::pair<unsigned long,unsigned long> *)this->_Mylast;
    v8 = (std::pair<unsigned long,unsigned long> *)this->_Myfirst;
    _Ptr = v6;
    v13 = 0;
    std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
      v8,
      Mylast,
      (std::pair<unsigned long,unsigned long> *)v6);
    v7 = this->_Myfirst;
    if ( v7 )
    {
      v4 = this->_Mylast - v7;
      operator delete(this->_Myfirst);
    }
    this->_Myend = (ItemDataParser::ParseData *)((char *)_Counta + (_DWORD)v6);
    this->_Mylast = &v6[v4];
    this->_Myfirst = v6;
  }
}

//----- (00497AE0) --------------------------------------------------------
void __thiscall std::vector<ItemDataParser::ParseData>::push_back(
        std::vector<ItemDataParser::ParseData> *this,
        const std::pair<unsigned long,unsigned long> *_Val)
{
  ItemDataParser::ParseData *Myfirst; // edx
  unsigned int v4; // ecx
  ItemDataParser::ParseData *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
      (std::pair<unsigned long,unsigned long> *)Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<ItemDataParser::ParseData>::_Insert_n(
      this,
      (std::vector<ItemDataParser::ParseData>::iterator)this->_Mylast,
      1u,
      (const ItemDataParser::ParseData *)_Val);
  }
}

//----- (00497B50) --------------------------------------------------------
char __cdecl ItemDataParser::SetDefaultData(std::vector<ItemDataParser::ParseData> *parseDataArray)
{
  ItemDataParser::ParseData _Val; // [esp+8h] [ebp-8h] BYREF

  std::vector<ItemDataParser::ParseData>::reserve(parseDataArray, 0x28u);
  _Val.m_szColumnName = "ID";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadID;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ItemName";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadItemName;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "FieldModelName";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadFieldModelName;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "AttachedModelName";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadAttachedModelName;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "SpriteDDS";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSpriteDDS;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MinX";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMinX;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MinY";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMinY;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxX";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaxX;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxY";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaxY;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "SizeX";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSizeX;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "SizeY";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSizeY;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "EffectSound";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadEffectSound;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ItemLevel";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadItemLevel;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ItemType";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadItemType;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "TypeName";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadTypeName;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "Price";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPrice;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "BlackPrice";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadBlackPrice;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MedalPrice";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMedalPrice;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "OptionLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadOptionLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "Durability";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDurability;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxDurability";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaxDurability;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ClassLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadClassLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "SkillLimitType";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSkillLimitType;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "SkillLimitLevel";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSkillLimitLevel;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "LevelLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadLevelLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "STRLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSTRLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DEXLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDEXLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "CONLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadCONLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "INTLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadINTLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "WISLimit";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadWISLimit;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "CraftExp";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadCraftExp;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  return 1;
}

//----- (00497EE0) --------------------------------------------------------
char __cdecl ItemDataParser::SetEquipData(std::vector<ItemDataParser::ParseData> *parseDataArray)
{
  ItemDataParser::ParseData _Val; // [esp+8h] [ebp-8h] BYREF

  _Val.m_szColumnName = "CriticalType";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadCriticalType;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "CriticalPercentage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadCriticalPercentage;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MinDamage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMinDamage;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxDamage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaxDamage;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DRC";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDRC;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "OffenceRevision";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadOffenceRevision;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "Defence";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDefence;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DefenceRevision";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDefenceRevision;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "Resistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "BlockingPercentage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadBlockingPercentage;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "AttackSpeed";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadAttackSpeed;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MoveSpeed";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMoveSpeed;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "AttackRange";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadAttackRange;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "LongRangeAttack";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadRangedAttack;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "HpMaxPlus";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadHpMaxPlus;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MpMaxPlus";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMpMaxPlus;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "HPRegen";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadHPRegen;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MPRegen";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMPRegen;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "FireAttribute";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadFireAttribute;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "LightingAttribute";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadLightingAttribute;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ColdAttribute";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadColdAttribute;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DrainAttribute";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDrainAttribute;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "PoisonAttribute";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPoisonAttribute;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "FireResistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadFireResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "LightningResistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadLightningResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "ColdResistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadColdResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DrainResistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDrainResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "PoisonResistance";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPoisonResistance;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "STRAdd";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSTRAdd;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DEXAdd";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadDEXAdd;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "CONAdd";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadCONAdd;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "INTAdd";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadINTAdd;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "WISAdd";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadWISAdd;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaterialValue";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaterialValue;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxSocketNum";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadMaxSocketNum;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  return 1;
}

//----- (004982D0) --------------------------------------------------------
char __cdecl ItemDataParser::SetUseItemData(std::vector<ItemDataParser::ParseData> *parseDataArray)
{
  ItemDataParser::ParseData _Val; // [esp+4h] [ebp-8h] BYREF

  _Val.m_szColumnName = "CriticalType";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSkill_ID;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "CriticalPercentage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadSkill_Level;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MinDamage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadZone;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "MaxDamage";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPositionX;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DRC";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPositionY;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "OffenceRevision";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadPositionZ;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "Defence";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadAmount;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  _Val.m_szColumnName = "DefenceRevision";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadItemDescribe;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  return 1;
}

//----- (004983C0) --------------------------------------------------------
char __cdecl ItemDataParser::SetEtcItemData(std::vector<ItemDataParser::ParseData> *parseDataArray)
{
  ItemDataParser::ParseData _Val; // [esp+0h] [ebp-8h] BYREF

  _Val.m_szColumnName = "CriticalType";
  _Val.m_fnParseFunc = (bool (__cdecl *)(Item::ItemInfo *, const char *))ReadItemDescribe;
  std::vector<ItemDataParser::ParseData>::push_back(
    parseDataArray,
    (const std::pair<unsigned long,unsigned long> *)&_Val);
  return 1;
}

//----- (004983F0) --------------------------------------------------------
CLotteryEvent::LotteryEventItem *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size(
        std::vector<CLotteryEvent::LotteryEventItem> *this)
{
  CLotteryEvent::LotteryEventItem *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (CLotteryEvent::LotteryEventItem *)(this->_Mylast - result);
  return result;
}

//----- (00498410) --------------------------------------------------------
CLotteryEvent::LotteryEventPrize *__cdecl std::copy_backward<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
        CLotteryEvent::LotteryEventPrize *_First,
        CLotteryEvent::LotteryEventPrize *_Last,
        CLotteryEvent::LotteryEventPrize *_Dest)
{
  CLotteryEvent::LotteryEventPrize *v3; // ecx
  CLotteryEvent::LotteryEventPrize *result; // eax
  CLotteryEvent::LotteryEventPrize v5; // esi

  v3 = _Last;
  for ( result = _Dest; v3 != _First; *result = v5 )
  {
    v5 = v3[-1];
    --v3;
    --result;
  }
  return result;
}

//----- (00498440) --------------------------------------------------------
CLotteryEvent::LotteryEventPrize *__cdecl std::copy<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
        CLotteryEvent::LotteryEventPrize *_First,
        CLotteryEvent::LotteryEventPrize *_Last,
        CLotteryEvent::LotteryEventPrize *_Dest)
{
  CLotteryEvent::LotteryEventPrize *v3; // ecx
  CLotteryEvent::LotteryEventPrize *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
    *result = *v3++;
  return result;
}

//----- (00498470) --------------------------------------------------------
unsigned __int16 __thiscall CLotteryEvent::GetLottery(CLotteryEvent *this)
{
  CLotteryEvent::LotteryEventItem *Myfirst; // eax
  CLotteryEvent::LotteryEventItem *Mylast; // edx
  int i; // ecx
  int m_cProbability; // edi
  unsigned int v6; // eax
  CLotteryEvent::LotteryEventItem *v7; // ecx
  CLotteryEvent::LotteryEventItem *v8; // esi
  unsigned int v9; // edx

  Myfirst = this->m_aryItem._Myfirst;
  Mylast = this->m_aryItem._Mylast;
  for ( i = 0; Myfirst != Mylast; i += m_cProbability )
  {
    m_cProbability = Myfirst->m_cProbability;
    ++Myfirst;
  }
  v6 = Math::Random::ComplexRandom(i, 0);
  v7 = this->m_aryItem._Myfirst;
  v8 = this->m_aryItem._Mylast;
  v9 = 0;
  if ( v7 == v8 )
    return 0;
  while ( 1 )
  {
    v9 += v7->m_cProbability;
    if ( v6 < v9 )
      break;
    if ( ++v7 == v8 )
      return 0;
  }
  return v7->m_usItemID;
}

//----- (004984D0) --------------------------------------------------------
void __thiscall CLotteryEvent::PrizeLottery(CLotteryEvent *this, unsigned __int16 usLotteryID)
{
  CLotteryEvent::LotteryEventItem *Myfirst; // edi
  unsigned int v4; // eax
  CLotteryEvent::LotteryEventPrize *v5; // esi
  CLotteryEvent::LotteryEventPrize *Mylast; // edx
  unsigned int v7; // ecx
  int v8; // eax
  unsigned __int8 m_cNum; // cl

  Myfirst = this->m_aryItem._Myfirst;
  if ( Myfirst == this->m_aryItem._Mylast )
  {
LABEL_7:
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CLotteryEvent::PrizeLottery",
      aDWorkRylSource_44,
      164,
      (char *)&byte_4F6240,
      usLotteryID);
  }
  else
  {
    while ( 1 )
    {
      if ( usLotteryID == Myfirst->m_usItemID )
      {
        v4 = Math::Random::ComplexRandom(Myfirst->m_usPrizeSumProb, 0);
        v5 = Myfirst->m_aryPrize._Myfirst;
        Mylast = Myfirst->m_aryPrize._Mylast;
        v7 = 0;
        if ( v5 != Mylast )
          break;
      }
LABEL_6:
      if ( ++Myfirst == this->m_aryItem._Mylast )
        goto LABEL_7;
    }
    while ( 1 )
    {
      v7 += v5->m_cProbability;
      if ( v4 < v7 )
        break;
      if ( ++v5 == Mylast )
        goto LABEL_6;
    }
    if ( v5->m_usPrizeID )
    {
      Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, v5->m_usPrizeID);
      if ( v8 )
      {
        m_cNum = *(_BYTE *)(v8 + 24);
        if ( v5->m_cNum < m_cNum )
          m_cNum = v5->m_cNum;
        *(_BYTE *)(v8 + 21) = m_cNum;
      }
    }
  }
}
// 498568: variable 'v8' is possibly undefined

//----- (00498580) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
        CLotteryEvent::LotteryEventPrize *_First,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventPrize *_Val)
{
  unsigned int v3; // ecx

  if ( _Count )
  {
    v3 = _Count;
    do
    {
      if ( _First )
        *_First = *_Val;
      ++_First;
      --v3;
    }
    while ( v3 );
  }
}

//----- (004985B0) --------------------------------------------------------
unsigned int *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Ucopy<CLotteryEvent::LotteryEventPrize *>(
        std::allocator<unsigned long> *_Al,
        std::vector<unsigned long>::iterator _First,
        std::vector<unsigned long>::iterator _Last,
        unsigned int *_Dest)
{
  return std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
           _First,
           _Last,
           _Dest);
}

//----- (004985D0) --------------------------------------------------------
CLotteryEvent::LotteryEventPrize *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Ufill(
        std::vector<CLotteryEvent::LotteryEventPrize> *this,
        CLotteryEvent::LotteryEventPrize *_Ptr,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventPrize *_Val)
{
  std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (00498600) --------------------------------------------------------
void __thiscall __noreturn std::vector<CLotteryEvent::LotteryEventItem>::_Xlen(
        std::vector<CLotteryEvent::LotteryEventItem> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00498670) --------------------------------------------------------
void __thiscall Quest::PhaseNode::~PhaseNode(Quest::PhaseNode *this)
{
  if ( this->m_lstTrigger._Myfirst )
    operator delete(this->m_lstTrigger._Myfirst);
  this->m_lstTrigger._Myfirst = 0;
  this->m_lstTrigger._Mylast = 0;
  this->m_lstTrigger._Myend = 0;
}

//----- (004986A0) --------------------------------------------------------
char __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Buy(
        std::vector<CLotteryEvent::LotteryEventPrize> *this,
        unsigned int _Capacity)
{
  CLotteryEvent::LotteryEventPrize *v4; // eax

  this->_Myfirst = 0;
  this->_Mylast = 0;
  this->_Myend = 0;
  if ( !_Capacity )
    return 0;
  if ( _Capacity > 0x3FFFFFFF )
    std::vector<CLotteryEvent::LotteryEventItem>::_Xlen((std::vector<CLotteryEvent::LotteryEventItem> *)this);
  v4 = (CLotteryEvent::LotteryEventPrize *)operator new((tagHeader *)(4 * _Capacity));
  this->_Myend = &v4[_Capacity];
  this->_Myfirst = v4;
  this->_Mylast = v4;
  return 1;
}

//----- (004986F0) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::_Insert_n(
        std::vector<CLotteryEvent::LotteryEventPrize> *this,
        std::vector<CLotteryEvent::LotteryEventPrize>::iterator _Where,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventPrize **_Val)
{
  CLotteryEvent::LotteryEventPrize *Myfirst; // edx
  const CLotteryEvent::LotteryEventPrize *v6; // ecx
  unsigned int v7; // eax
  int v9; // ecx
  int v10; // ecx
  unsigned int v11; // eax
  int v12; // ecx
  int v13; // eax
  CLotteryEvent::LotteryEventPrize *v14; // edi
  CLotteryEvent::LotteryEventPrize *v15; // ecx
  int v16; // eax
  int v17; // ebx
  unsigned int *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  CLotteryEvent::LotteryEventPrize *v22; // ebx
  CLotteryEvent::LotteryEventPrize *v23; // ebx
  CLotteryEvent::LotteryEventPrize *v24; // [esp-18h] [ebp-3Ch]
  CLotteryEvent::LotteryEventPrize *v25; // [esp-Ch] [ebp-30h]
  unsigned int v26; // [esp-8h] [ebp-2Ch]
  int v27; // [esp+0h] [ebp-24h] BYREF
  CLotteryEvent::LotteryEventPrize *_Newvec; // [esp+Ch] [ebp-18h]
  int v29; // [esp+10h] [ebp-14h]
  int *v30; // [esp+14h] [ebp-10h]
  int v31; // [esp+20h] [ebp-4h]
  std::vector<CLotteryEvent::LotteryEventPrize>::iterator _Wherea; // [esp+2Ch] [ebp+8h]
  unsigned int *_Countb; // [esp+30h] [ebp+Ch]
  CLotteryEvent::LotteryEventPrize *_Counta; // [esp+30h] [ebp+Ch]

  Myfirst = this->_Myfirst;
  v6 = *_Val;
  v30 = &v27;
  _Val = (const CLotteryEvent::LotteryEventPrize **)v6;
  if ( Myfirst )
    v7 = this->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x3FFFFFFF - v9 < _Count )
      std::vector<CLotteryEvent::LotteryEventItem>::_Xlen((std::vector<CLotteryEvent::LotteryEventItem> *)this);
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = (unsigned int *)this->_Mylast;
      v20 = ((char *)Mylast - (char *)_Where._Myptr) >> 2 < _Count;
      v21 = 4 * _Count;
      _Wherea._Myptr = (CLotteryEvent::LotteryEventPrize *)(4 * _Count);
      _Counta = (CLotteryEvent::LotteryEventPrize *)Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
          (std::vector<unsigned long>::iterator)_Where._Myptr,
          (std::vector<unsigned long>::iterator)Mylast,
          (unsigned int *)&_Where._Myptr[v21 / 4]);
        v26 = _Count - (this->_Mylast - _Where._Myptr);
        v25 = this->_Mylast;
        v31 = 2;
        std::vector<CLotteryEvent::LotteryEventPrize>::_Ufill(
          this,
          v25,
          v26,
          (const CLotteryEvent::LotteryEventPrize *)&_Val);
        v22 = (CLotteryEvent::LotteryEventPrize *)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v22;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v22 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        v23 = (CLotteryEvent::LotteryEventPrize *)&Mylast[v21 / 0xFFFFFFFC];
        this->_Mylast = (CLotteryEvent::LotteryEventPrize *)std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                                                              (std::vector<unsigned long>::iterator)&Mylast[v21 / 0xFFFFFFFC],
                                                              (std::vector<unsigned long>::iterator)Mylast,
                                                              Mylast);
        std::copy_backward<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
          _Where._Myptr,
          v23,
          _Counta);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = this->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = this->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      v29 = 4 * v11;
      v14 = (CLotteryEvent::LotteryEventPrize *)operator new((tagHeader *)(4 * v11));
      v24 = this->_Myfirst;
      _Newvec = v14;
      v31 = 0;
      _Countb = std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                  (std::vector<unsigned long>::iterator)v24,
                  (std::vector<unsigned long>::iterator)_Where._Myptr,
                  (unsigned int *)v14);
      std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
        (CLotteryEvent::LotteryEventPrize *)_Countb,
        _Count,
        (const CLotteryEvent::LotteryEventPrize *)&_Val);
      std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
        (std::vector<unsigned long>::iterator)_Where._Myptr,
        (std::vector<unsigned long>::iterator)this->_Mylast,
        &_Countb[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v16 = this->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(this->_Myfirst);
      this->_Myend = &v14[v29 / 4u];
      this->_Mylast = &v14[v17];
      this->_Myfirst = v14;
    }
  }
}

//----- (00498930) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventPrize>::vector<CLotteryEvent::LotteryEventPrize>(
        std::vector<CLotteryEvent::LotteryEventPrize> *this,
        const std::vector<CLotteryEvent::LotteryEventPrize> *_Right)
{
  CLotteryEvent::LotteryEventPrize *Myfirst; // ecx
  unsigned int v4; // eax
  int v5; // edi
  CLotteryEvent::LotteryEventPrize *v6; // eax
  CLotteryEvent::LotteryEventPrize *v7; // [esp-18h] [ebp-38h]
  CLotteryEvent::LotteryEventPrize *Mylast; // [esp-14h] [ebp-34h]
  int v9; // [esp+0h] [ebp-20h] BYREF
  CBanList *v10; // [esp+Ch] [ebp-14h]
  int *v11; // [esp+10h] [ebp-10h]
  int v12; // [esp+1Ch] [ebp-4h]

  Myfirst = _Right->_Myfirst;
  v11 = &v9;
  v10 = (CBanList *)this;
  if ( Myfirst )
    v4 = _Right->_Mylast - Myfirst;
  else
    v4 = 0;
  this->_Myfirst = 0;
  this->_Mylast = 0;
  this->_Myend = 0;
  if ( v4 )
  {
    if ( v4 > 0x3FFFFFFF )
      std::vector<CLotteryEvent::LotteryEventItem>::_Xlen((std::vector<CLotteryEvent::LotteryEventItem> *)this);
    v5 = v4;
    v6 = (CLotteryEvent::LotteryEventPrize *)operator new((tagHeader *)(4 * v4));
    this->_Myfirst = v6;
    this->_Mylast = v6;
    this->_Myend = &v6[v5];
    Mylast = _Right->_Mylast;
    v7 = _Right->_Myfirst;
    v12 = 0;
    this->_Mylast = (CLotteryEvent::LotteryEventPrize *)std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                                                          (std::vector<unsigned long>::iterator)v7,
                                                          (std::vector<unsigned long>::iterator)Mylast,
                                                          (unsigned int *)v6);
  }
}

//----- (004989F0) --------------------------------------------------------
std::allocator<unsigned long> *__thiscall std::vector<CLotteryEvent::LotteryEventPrize>::operator=(
        std::allocator<unsigned long> *_Al,
        int __formal)
{
  CLotteryEvent::LotteryEventPrize *v3; // ebx
  unsigned int v4; // ecx
  int v6; // eax
  unsigned int v7; // edx
  int v8; // eax
  int v9; // ecx
  unsigned int v10; // edx
  int v11; // edx
  CLotteryEvent::LotteryEventPrize *v12; // ebx
  int v13; // ecx
  unsigned int v14; // eax

  if ( _Al == (std::allocator<unsigned long> *)__formal )
    return _Al;
  v3 = *(CLotteryEvent::LotteryEventPrize **)(__formal + 4);
  if ( !v3 || (v4 = (*(_DWORD *)(__formal + 8) - (int)v3) >> 2) == 0 )
  {
    if ( *(_DWORD *)&_Al[4].std::_Allocator_base<unsigned long> )
      operator delete(*(void **)&_Al[4].std::_Allocator_base<unsigned long>);
    *(_DWORD *)&_Al[4].std::_Allocator_base<unsigned long> = 0;
    *(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> = 0;
    *(_DWORD *)&_Al[12].std::_Allocator_base<unsigned long> = 0;
    return _Al;
  }
  v6 = *(_DWORD *)&_Al[4].std::_Allocator_base<unsigned long>;
  if ( v6 )
    v7 = (*(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> - v6) >> 2;
  else
    v7 = 0;
  if ( v4 <= v7 )
  {
    std::copy<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
      v3,
      *(CLotteryEvent::LotteryEventPrize **)(__formal + 8),
      *(CLotteryEvent::LotteryEventPrize **)&_Al[4].std::_Allocator_base<unsigned long>);
    v8 = *(_DWORD *)(__formal + 4);
    if ( v8 )
      v9 = *(_DWORD *)&_Al[4].std::_Allocator_base<unsigned long> + 4 * ((*(_DWORD *)(__formal + 8) - v8) >> 2);
    else
      v9 = *(_DWORD *)&_Al[4].std::_Allocator_base<unsigned long>;
    *(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> = v9;
    return _Al;
  }
  if ( v6 )
    v10 = (*(_DWORD *)&_Al[12].std::_Allocator_base<unsigned long> - v6) >> 2;
  else
    v10 = 0;
  if ( v4 > v10 )
  {
    if ( v6 )
      operator delete(*(void **)&_Al[4].std::_Allocator_base<unsigned long>);
    v13 = *(_DWORD *)(__formal + 4);
    if ( v13 )
      v14 = (*(_DWORD *)(__formal + 8) - v13) >> 2;
    else
      v14 = 0;
    if ( std::vector<CLotteryEvent::LotteryEventPrize>::_Buy((std::vector<CLotteryEvent::LotteryEventPrize> *)_Al, v14) )
      *(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> = std::vector<CLotteryEvent::LotteryEventPrize>::_Ucopy<CLotteryEvent::LotteryEventPrize *>(
                                                                 _Al,
                                                                 *(std::vector<unsigned long>::iterator *)(__formal + 4),
                                                                 *(std::vector<unsigned long>::iterator *)(__formal + 8),
                                                                 *(unsigned int **)&_Al[4].std::_Allocator_base<unsigned long>);
    return _Al;
  }
  if ( v6 )
    v11 = (*(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> - v6) >> 2;
  else
    v11 = 0;
  v12 = (CLotteryEvent::LotteryEventPrize *)(*(_DWORD *)(__formal + 4) + 4 * v11);
  std::copy<CLotteryEvent::LotteryEventPrize *,CLotteryEvent::LotteryEventPrize *>(
    *(CLotteryEvent::LotteryEventPrize **)(__formal + 4),
    v12,
    *(CLotteryEvent::LotteryEventPrize **)&_Al[4].std::_Allocator_base<unsigned long>);
  *(_DWORD *)&_Al[8].std::_Allocator_base<unsigned long> = std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(
                                                             (std::vector<unsigned long>::iterator)v12,
                                                             *(std::vector<unsigned long>::iterator *)(__formal + 8),
                                                             *(unsigned int **)&_Al[8].std::_Allocator_base<unsigned long>);
  return _Al;
}

//----- (00498B40) --------------------------------------------------------
CLotteryEvent::LotteryEventItem *__cdecl std::_Copy_backward_opt<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *>(
        CLotteryEvent::LotteryEventItem *_First,
        CLotteryEvent::LotteryEventItem *_Last,
        CLotteryEvent::LotteryEventItem *_Dest)
{
  CLotteryEvent::LotteryEventItem *v3; // esi
  CLotteryEvent::LotteryEventItem *v4; // edi
  unsigned __int16 m_usItemID; // ax

  v3 = _Last;
  if ( _First == _Last )
    return _Dest;
  v4 = _Dest;
  do
  {
    m_usItemID = v3[-1].m_usItemID;
    --v3;
    --v4;
    v4->m_usItemID = m_usItemID;
    v4->m_cProbability = v3->m_cProbability;
    v4->m_usPrizeSumProb = v3->m_usPrizeSumProb;
    std::vector<CLotteryEvent::LotteryEventPrize>::operator=(
      (std::allocator<unsigned long> *)&v4->m_aryPrize,
      (int)&v3->m_aryPrize);
  }
  while ( v3 != _First );
  return v4;
}

//----- (00498B90) --------------------------------------------------------
void __cdecl std::fill<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem>(
        CLotteryEvent::LotteryEventItem *_First,
        CLotteryEvent::LotteryEventItem *_Last,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  CLotteryEvent::LotteryEventItem *i; // esi

  for ( i = _First; i != _Last; ++i )
  {
    i->m_usItemID = _Val->m_usItemID;
    i->m_cProbability = _Val->m_cProbability;
    i->m_usPrizeSumProb = _Val->m_usPrizeSumProb;
    std::vector<CLotteryEvent::LotteryEventPrize>::operator=(
      (std::allocator<unsigned long> *)&i->m_aryPrize,
      (int)&_Val->m_aryPrize);
  }
}

//----- (00498BD0) --------------------------------------------------------
CLotteryEvent::LotteryEventItem *__cdecl std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(
        CLotteryEvent::LotteryEventItem *_First,
        CLotteryEvent::LotteryEventItem *_Last,
        CLotteryEvent::LotteryEventItem *_Dest)
{
  CLotteryEvent::LotteryEventItem *v3; // esi
  int v6; // [esp+0h] [ebp-20h] BYREF
  CLotteryEvent::LotteryEventItem *_Next; // [esp+Ch] [ebp-14h]
  int *v8; // [esp+10h] [ebp-10h]
  int v9; // [esp+1Ch] [ebp-4h]

  v3 = _Dest;
  v8 = &v6;
  _Next = _Dest;
  v9 = 0;
  while ( _First != _Last )
  {
    LOBYTE(v9) = 1;
    if ( v3 )
    {
      v3->m_usItemID = _First->m_usItemID;
      v3->m_cProbability = _First->m_cProbability;
      v3->m_usPrizeSumProb = _First->m_usPrizeSumProb;
      std::vector<CLotteryEvent::LotteryEventPrize>::vector<CLotteryEvent::LotteryEventPrize>(
        &v3->m_aryPrize,
        &_First->m_aryPrize);
    }
    ++v3;
    LOBYTE(v9) = 0;
    ++_First;
  }
  return v3;
}

//----- (00498C90) --------------------------------------------------------
void __cdecl std::_Uninit_fill_n<CLotteryEvent::LotteryEventItem *,unsigned int,CLotteryEvent::LotteryEventItem,std::allocator<CLotteryEvent::LotteryEventItem>>(
        CLotteryEvent::LotteryEventItem *_First,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  CLotteryEvent::LotteryEventItem *v3; // esi
  int v5; // [esp+0h] [ebp-20h] BYREF
  CLotteryEvent::LotteryEventItem *_Next; // [esp+Ch] [ebp-14h]
  int *v7; // [esp+10h] [ebp-10h]
  int v8; // [esp+1Ch] [ebp-4h]

  v3 = _First;
  v7 = &v5;
  _Next = _First;
  v8 = 0;
  while ( _Count )
  {
    LOBYTE(v8) = 1;
    if ( v3 )
    {
      v3->m_usItemID = _Val->m_usItemID;
      v3->m_cProbability = _Val->m_cProbability;
      v3->m_usPrizeSumProb = _Val->m_usPrizeSumProb;
      std::vector<CLotteryEvent::LotteryEventPrize>::vector<CLotteryEvent::LotteryEventPrize>(
        &v3->m_aryPrize,
        &_Val->m_aryPrize);
    }
    --_Count;
    ++v3;
    LOBYTE(v8) = 0;
  }
}

//----- (00498D50) --------------------------------------------------------
CLotteryEvent::LotteryEventItem *__thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Ufill(
        std::vector<CLotteryEvent::LotteryEventItem> *this,
        CLotteryEvent::LotteryEventItem *_Ptr,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  std::_Uninit_fill_n<CLotteryEvent::LotteryEventItem *,unsigned int,CLotteryEvent::LotteryEventItem,std::allocator<CLotteryEvent::LotteryEventItem>>(
    _Ptr,
    _Count,
    _Val);
  return &_Ptr[_Count];
}

//----- (00498D80) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Destroy(
        std::vector<CLotteryEvent::LotteryEventItem> *this,
        CLotteryEvent::LotteryEventItem *_First,
        CLotteryEvent::LotteryEventItem *_Last)
{
  Quest::PhaseNode *v3; // esi

  v3 = (Quest::PhaseNode *)_First;
  if ( _First != _Last )
  {
    do
      Quest::PhaseNode::~PhaseNode(v3++);
    while ( v3 != (Quest::PhaseNode *)_Last );
  }
}

//----- (00498DB0) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Insert_n(
        std::vector<CLotteryEvent::LotteryEventItem> *this,
        std::vector<CLotteryEvent::LotteryEventItem>::iterator _Where,
        unsigned int _Count,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  unsigned __int8 m_cProbability; // dl
  unsigned __int16 m_usPrizeSumProb; // cx
  CLotteryEvent::LotteryEventItem *Myfirst; // ebx
  unsigned int v8; // ecx
  int v10; // eax
  int v11; // eax
  unsigned int v12; // ecx
  int v13; // eax
  CLotteryEvent::LotteryEventItem *v14; // ebx
  CLotteryEvent::LotteryEventItem *v15; // eax
  char *v16; // edi
  CLotteryEvent::LotteryEventItem *v17; // eax
  CLotteryEvent::LotteryEventItem *Mylast; // ecx
  CLotteryEvent::LotteryEventItem *v20; // edi
  CLotteryEvent::LotteryEventItem *v21; // [esp-18h] [ebp-54h]
  CLotteryEvent::LotteryEventItem *v22; // [esp-Ch] [ebp-48h]
  unsigned int v23; // [esp-8h] [ebp-44h]
  int v24; // [esp+0h] [ebp-3Ch] BYREF
  CLotteryEvent::LotteryEventItem _Tmp; // [esp+Ch] [ebp-30h] BYREF
  CLotteryEvent::LotteryEventItem *_Newvec; // [esp+24h] [ebp-18h]
  std::vector<CLotteryEvent::LotteryEventItem> *v27; // [esp+28h] [ebp-14h]
  int *v28; // [esp+2Ch] [ebp-10h]
  int v29; // [esp+38h] [ebp-4h]
  CLotteryEvent::LotteryEventItem *_Wherea; // [esp+44h] [ebp+8h]
  CLotteryEvent::LotteryEventItem *_Ptr; // [esp+48h] [ebp+Ch]
  const CLotteryEvent::LotteryEventItem *_Vala; // [esp+4Ch] [ebp+10h]
  CLotteryEvent::LotteryEventItem *_Valb; // [esp+4Ch] [ebp+10h]

  m_cProbability = _Val->m_cProbability;
  _Tmp.m_usItemID = _Val->m_usItemID;
  m_usPrizeSumProb = _Val->m_usPrizeSumProb;
  v28 = &v24;
  _Tmp.m_usPrizeSumProb = m_usPrizeSumProb;
  v27 = this;
  _Tmp.m_cProbability = m_cProbability;
  std::vector<CLotteryEvent::LotteryEventPrize>::vector<CLotteryEvent::LotteryEventPrize>(
    &_Tmp.m_aryPrize,
    &_Val->m_aryPrize);
  Myfirst = this->_Myfirst;
  v8 = 0;
  v29 = 0;
  if ( Myfirst )
    v8 = this->_Myend - Myfirst;
  if ( _Count )
  {
    if ( Myfirst )
      v10 = this->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( 178956970 - v10 < _Count )
      std::vector<CLotteryEvent::LotteryEventItem>::_Xlen(this);
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( v8 >= _Count + v11 )
    {
      Mylast = this->_Mylast;
      _Valb = Mylast;
      if ( Mylast - _Where._Myptr >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(
                          _Wherea,
                          Mylast,
                          Mylast);
        std::_Copy_backward_opt<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *>(
          _Where._Myptr,
          _Wherea,
          _Valb);
        std::fill<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem>(
          _Where._Myptr,
          &_Where._Myptr[_Count],
          &_Tmp);
      }
      else
      {
        std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(
          _Where._Myptr,
          Mylast,
          &_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = this->_Mylast;
        LOBYTE(v29) = 3;
        std::vector<CLotteryEvent::LotteryEventItem>::_Ufill(this, v22, v23, &_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        v29 = 0;
        std::fill<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem>(
          _Where._Myptr,
          &v20[-_Count],
          &_Tmp);
      }
    }
    else
    {
      if ( 178956970 - (v8 >> 1) >= v8 )
        v12 = (v8 >> 1) + v8;
      else
        v12 = 0;
      if ( Myfirst )
        v13 = this->_Mylast - Myfirst;
      else
        v13 = 0;
      if ( v12 < _Count + v13 )
        v12 = (unsigned int)std::vector<std::pair<unsigned long,Guild::RelationInfo>>::size(this) + _Count;
      _Vala = (const CLotteryEvent::LotteryEventItem *)(24 * v12);
      v14 = (CLotteryEvent::LotteryEventItem *)operator new((tagHeader *)(24 * v12));
      v21 = this->_Myfirst;
      _Newvec = v14;
      LOBYTE(v29) = 1;
      _Ptr = std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(
               v21,
               _Where._Myptr,
               v14);
      std::_Uninit_fill_n<CLotteryEvent::LotteryEventItem *,unsigned int,CLotteryEvent::LotteryEventItem,std::allocator<CLotteryEvent::LotteryEventItem>>(
        _Ptr,
        _Count,
        &_Tmp);
      std::_Uninit_copy<CLotteryEvent::LotteryEventItem *,CLotteryEvent::LotteryEventItem *,std::allocator<CLotteryEvent::LotteryEventItem>>(
        _Where._Myptr,
        this->_Mylast,
        &_Ptr[_Count]);
      v15 = this->_Myfirst;
      if ( v15 )
        v15 = (CLotteryEvent::LotteryEventItem *)(this->_Mylast - v15);
      v16 = (char *)v15 + _Count;
      v17 = this->_Myfirst;
      if ( v17 )
      {
        std::vector<CLotteryEvent::LotteryEventItem>::_Destroy(this, v17, this->_Mylast);
        operator delete(this->_Myfirst);
      }
      this->_Myend = (CLotteryEvent::LotteryEventItem *)((char *)_Vala + (_DWORD)v14);
      this->_Mylast = &v14[(_DWORD)v16];
      this->_Myfirst = v14;
    }
  }
  if ( _Tmp.m_aryPrize._Myfirst )
    operator delete(_Tmp.m_aryPrize._Myfirst);
}

//----- (004990C0) --------------------------------------------------------
std::vector<CLotteryEvent::LotteryEventItem>::iterator *__thiscall std::vector<CLotteryEvent::LotteryEventItem>::insert(
        std::vector<CLotteryEvent::LotteryEventItem> *this,
        std::vector<CLotteryEvent::LotteryEventItem>::iterator *result,
        std::vector<CLotteryEvent::LotteryEventItem>::iterator _Where,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  CLotteryEvent::LotteryEventItem *Myfirst; // esi
  int v6; // esi
  std::vector<CLotteryEvent::LotteryEventItem>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CLotteryEvent::LotteryEventItem>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (00499130) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::_Tidy(std::vector<CLotteryEvent::LotteryEventItem> *this)
{
  Quest::PhaseNode *Myfirst; // esi
  CLotteryEvent::LotteryEventItem *i; // edi

  Myfirst = (Quest::PhaseNode *)this->_Myfirst;
  if ( Myfirst )
  {
    for ( i = this->_Mylast; Myfirst != (Quest::PhaseNode *)i; ++Myfirst )
      Quest::PhaseNode::~PhaseNode(Myfirst);
    operator delete(this->_Myfirst);
  }
  this->_Myfirst = 0;
  this->_Mylast = 0;
  this->_Myend = 0;
}

//----- (00499180) --------------------------------------------------------
void __thiscall std::vector<CLotteryEvent::LotteryEventItem>::push_back(
        std::vector<CLotteryEvent::LotteryEventItem> *this,
        const CLotteryEvent::LotteryEventItem *_Val)
{
  CLotteryEvent::LotteryEventItem *Myfirst; // edi
  unsigned int v4; // ecx
  CLotteryEvent::LotteryEventItem *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<CLotteryEvent::LotteryEventItem *,unsigned int,CLotteryEvent::LotteryEventItem,std::allocator<CLotteryEvent::LotteryEventItem>>(
      Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<CLotteryEvent::LotteryEventItem>::insert(
      this,
      (std::vector<CLotteryEvent::LotteryEventItem>::iterator *)&_Val,
      (std::vector<CLotteryEvent::LotteryEventItem>::iterator)this->_Mylast,
      _Val);
  }
}

//----- (00499210) --------------------------------------------------------
void __thiscall CLotteryEvent::CLotteryEvent(CLotteryEvent *this)
{
  this->__vftable = (CLotteryEvent_vtbl *)&CLotteryEvent::`vftable';
  this->m_bActive = 0;
  this->m_aryItem._Myfirst = 0;
  this->m_aryItem._Mylast = 0;
  this->m_aryItem._Myend = 0;
}
// 4F6284: using guessed type void *CLotteryEvent::`vftable';

//----- (00499230) --------------------------------------------------------
void __thiscall CLotteryEvent::~CLotteryEvent(CLotteryEvent *this)
{
  this->__vftable = (CLotteryEvent_vtbl *)&CLotteryEvent::`vftable';
  std::vector<CLotteryEvent::LotteryEventItem>::_Tidy(&this->m_aryItem);
}
// 4F6284: using guessed type void *CLotteryEvent::`vftable';

//----- (00499240) --------------------------------------------------------
char __thiscall CLotteryEvent::Initialize(CLotteryEvent *this)
{
  int Section; // esi
  Quest::TriggerNode **Mylast; // esi
  CLotteryEvent::LotteryEventPrize _Val; // [esp+8h] [ebp-100F0h] BYREF
  Quest::PhaseNode iNumber; // [esp+Ch] [ebp-100ECh] BYREF
  std::_Nonscalar_ptr_iterator_tag __formal[4]; // [esp+24h] [ebp-100D4h]
  std::string szSectionName; // [esp+28h] [ebp-100D0h] BYREF
  char buf[12]; // [esp+44h] [ebp-100B4h] BYREF
  CDelimitedFile v9; // [esp+50h] [ebp-100A8h] BYREF
  CDelimitedFile v10; // [esp+809Ch] [ebp-805Ch] BYREF
  int v11; // [esp+100F4h] [ebp-4h]

  *(_DWORD *)__formal = this;
  CDelimitedFile::CDelimitedFile(&v9, "\t");
  v11 = 0;
  if ( !CDelimitedFile::Open(&v9, CLotteryEvent::ms_szEventScriptFileName, -1, 0) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLotteryEvent::Initialize", aDWorkRylSource_44, 33, (char *)&byte_4F63BC);
LABEL_5:
    v11 = -1;
    CDelimitedFile::~CDelimitedFile(&v9);
    return 0;
  }
  if ( !CDelimitedFile::ReadSection(&v9) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CLotteryEvent::Initialize", aDWorkRylSource_44, 39, (char *)&byte_4F638C);
    goto LABEL_5;
  }
  if ( CDelimitedFile::ReadLine(&v9) == 1 )
  {
    while ( 1 )
    {
      LOWORD(iNumber.m_dwZondID) = 0;
      BYTE2(iNumber.m_dwZondID) = 0;
      LOWORD(iNumber.m_dwPhaseNumber) = 0;
      memset(&iNumber.m_lstTrigger._Myfirst, 0, 12);
      LOBYTE(v11) = 1;
      CDelimitedFile::ReadData(&v9, (__int16 *)&iNumber);
      CDelimitedFile::ReadData(&v9, (char *)&iNumber.m_dwZondID + 2);
      if ( !Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, iNumber.m_dwZondID) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CLotteryEvent::Initialize",
          aDWorkRylSource_44,
          53,
          (char *)&byte_4F6320,
          LOWORD(iNumber.m_dwZondID));
LABEL_24:
        Quest::PhaseNode::~PhaseNode(&iNumber);
        goto LABEL_30;
      }
      itoa(LOWORD(iNumber.m_dwZondID), buf, 0xAu);
      szSectionName._Myres = 15;
      szSectionName._Mysize = 0;
      szSectionName._Bx._Buf[0] = 0;
      std::string::assign(&szSectionName, buf, strlen(buf));
      LOBYTE(v11) = 2;
      Section = CDelimitedFile::GetSection(&v9, &szSectionName);
      LOBYTE(v11) = 1;
      if ( szSectionName._Myres >= 0x10 )
        operator delete(szSectionName._Bx._Ptr);
      szSectionName._Myres = 15;
      szSectionName._Mysize = 0;
      szSectionName._Bx._Buf[0] = 0;
      if ( !Section )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CLotteryEvent::Initialize",
          aDWorkRylSource_44,
          63,
          byte_4F62E0,
          LOWORD(iNumber.m_dwZondID));
        goto LABEL_24;
      }
      CDelimitedFile::CDelimitedFile(&v10, "\t");
      LOBYTE(v11) = 3;
      if ( !CDelimitedFile::Open(&v10, CLotteryEvent::ms_szEventScriptFileName, Section, 0) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CLotteryEvent::Initialize",
          aDWorkRylSource_44,
          71,
          (char *)&byte_4F63BC);
        LOBYTE(v11) = 1;
        CDelimitedFile::~CDelimitedFile(&v10);
        Quest::PhaseNode::~PhaseNode(&iNumber);
        goto LABEL_30;
      }
      if ( CDelimitedFile::ReadLine(&v10) == 1 )
        break;
LABEL_19:
      std::vector<CLotteryEvent::LotteryEventItem>::push_back(
        (std::vector<CLotteryEvent::LotteryEventItem> *)(*(_DWORD *)__formal + 8),
        (const CLotteryEvent::LotteryEventItem *)&iNumber);
      LOBYTE(v11) = 1;
      CDelimitedFile::~CDelimitedFile(&v10);
      LOBYTE(v11) = 0;
      if ( iNumber.m_lstTrigger._Myfirst )
        operator delete(iNumber.m_lstTrigger._Myfirst);
      memset(&iNumber.m_lstTrigger._Myfirst, 0, 12);
      if ( CDelimitedFile::ReadLine(&v9) != 1 )
        goto LABEL_22;
    }
    while ( 1 )
    {
      _Val.m_usPrizeID = 0;
      _Val.m_cProbability = 0;
      _Val.m_cNum = 0;
      CDelimitedFile::ReadData(&v10, (__int16 *)&_Val);
      CDelimitedFile::ReadData(&v10, (char *)&_Val.m_cProbability);
      CDelimitedFile::ReadData(&v10, (char *)&_Val.m_cNum);
      LOWORD(iNumber.m_dwPhaseNumber) += _Val.m_cProbability;
      if ( !Item::CItemMgr::GetItemInfo(CSingleton<Item::CItemMgr>::ms_pSingleton, _Val.m_usPrizeID) )
        break;
      if ( iNumber.m_lstTrigger._Myfirst
        && iNumber.m_lstTrigger._Mylast - iNumber.m_lstTrigger._Myfirst < (unsigned int)(iNumber.m_lstTrigger._Myend
                                                                                       - iNumber.m_lstTrigger._Myfirst) )
      {
        Mylast = iNumber.m_lstTrigger._Mylast;
        std::_Uninit_fill_n<CLotteryEvent::LotteryEventPrize *,unsigned int,CLotteryEvent::LotteryEventPrize,std::allocator<CLotteryEvent::LotteryEventPrize>>(
          (CLotteryEvent::LotteryEventPrize *)iNumber.m_lstTrigger._Mylast,
          1u,
          &_Val);
        iNumber.m_lstTrigger._Mylast = Mylast + 1;
      }
      else
      {
        std::vector<CLotteryEvent::LotteryEventPrize>::_Insert_n(
          (std::vector<CLotteryEvent::LotteryEventPrize> *)&iNumber.m_lstTrigger,
          (std::vector<CLotteryEvent::LotteryEventPrize>::iterator)iNumber.m_lstTrigger._Mylast,
          1u,
          (const CLotteryEvent::LotteryEventPrize **)&_Val);
      }
      if ( CDelimitedFile::ReadLine(&v10) != 1 )
        goto LABEL_19;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CLotteryEvent::Initialize",
      aDWorkRylSource_44,
      88,
      (char *)&byte_4F6288,
      _Val.m_usPrizeID);
    LOBYTE(v11) = 1;
    CDelimitedFile::~CDelimitedFile(&v10);
    if ( iNumber.m_lstTrigger._Myfirst )
      operator delete(iNumber.m_lstTrigger._Myfirst);
    memset(&iNumber.m_lstTrigger._Myfirst, 0, 12);
LABEL_30:
    v11 = -1;
    CDelimitedFile::~CDelimitedFile(&v9);
    return 0;
  }
  else
  {
LABEL_22:
    v11 = -1;
    CDelimitedFile::~CDelimitedFile(&v9);
    return 1;
  }
}

//----- (00499690) --------------------------------------------------------
CLotteryEvent *__thiscall CLotteryEvent::`vector deleting destructor'(CLotteryEvent *this, char a2)
{
  CLotteryEvent::~CLotteryEvent(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004996B0) --------------------------------------------------------
int __usercall get_byte@<eax>(gz_stream *s@<esi>)
{
  int result; // eax
  unsigned int v2; // eax
  _iobuf *file; // edx

  if ( s->z_eof )
    return -1;
  if ( s->stream.avail_in )
    goto LABEL_8;
  *_errno() = 0;
  v2 = fread(s->inbuf, 1u, 0x4000u, s->file);
  s->stream.avail_in = v2;
  if ( v2 )
  {
    s->stream.next_in = s->inbuf;
LABEL_8:
    --s->stream.avail_in;
    return *s->stream.next_in++;
  }
  file = s->file;
  s->z_eof = 1;
  result = -1;
  if ( (file->_flag & 0x20) != 0 )
    s->z_err = -1;
  return result;
}

//----- (00499720) --------------------------------------------------------
void __usercall check_header(gz_stream *s@<eax>)
{
  unsigned int i; // edi
  int v3; // eax
  unsigned int v4; // eax
  _iobuf *file; // edx
  unsigned __int8 v6; // cl
  int v7; // edi
  unsigned __int8 *v8; // ecx
  unsigned __int8 *v9; // eax
  unsigned int v10; // eax
  _iobuf *v11; // ecx
  unsigned __int8 v12; // cl
  char v13; // bl
  unsigned int v14; // eax
  _iobuf *v15; // edx
  char v16; // cl
  int v17; // edi
  unsigned int v18; // eax
  _iobuf *v19; // eax
  unsigned __int8 *v20; // eax
  int byte; // edi
  int v22; // edi
  int v23; // edx
  unsigned int v24; // eax
  unsigned __int8 *next_in; // eax
  unsigned __int8 v26; // cl
  _iobuf *v27; // eax
  unsigned int v28; // eax
  unsigned __int8 *v29; // eax
  unsigned __int8 v30; // cl
  _iobuf *v31; // ecx
  unsigned int v32; // eax
  unsigned __int8 *v33; // eax
  unsigned __int8 v34; // cl
  _iobuf *v35; // edx
  int j; // edi
  unsigned int v37; // eax
  _iobuf *v38; // edx
  unsigned __int8 *v39; // eax

  for ( i = 0; i < 2; ++i )
  {
    if ( s->z_eof )
    {
      v3 = -1;
      goto LABEL_11;
    }
    if ( s->stream.avail_in )
      goto LABEL_10;
    *_errno() = 0;
    v4 = fread(s->inbuf, 1u, 0x4000u, s->file);
    s->stream.avail_in = v4;
    if ( v4 )
    {
      s->stream.next_in = s->inbuf;
LABEL_10:
      --s->stream.avail_in;
      v6 = *s->stream.next_in++;
      v3 = v6;
      goto LABEL_11;
    }
    file = s->file;
    s->z_eof = 1;
    if ( (file->_flag & 0x20) != 0 )
      s->z_err = -1;
    v3 = -1;
LABEL_11:
    if ( v3 != gz_magic[i] )
    {
      if ( i )
      {
        v8 = s->stream.next_in - 1;
        ++s->stream.avail_in;
        s->stream.next_in = v8;
      }
      if ( v3 != -1 )
      {
        v9 = s->stream.next_in - 1;
        ++s->stream.avail_in;
        s->stream.next_in = v9;
        s->transparent = 1;
      }
      s->z_err = s->stream.avail_in == 0;
      return;
    }
  }
  if ( s->z_eof )
  {
    v7 = -1;
    goto LABEL_27;
  }
  if ( s->stream.avail_in )
    goto LABEL_26;
  *_errno() = 0;
  v10 = fread(s->inbuf, 1u, 0x4000u, s->file);
  s->stream.avail_in = v10;
  if ( v10 )
  {
    s->stream.next_in = s->inbuf;
LABEL_26:
    --s->stream.avail_in;
    v12 = *s->stream.next_in++;
    v7 = v12;
    goto LABEL_27;
  }
  v11 = s->file;
  s->z_eof = 1;
  if ( (v11->_flag & 0x20) != 0 )
    s->z_err = -1;
  v7 = -1;
LABEL_27:
  if ( s->z_eof )
  {
    v13 = -1;
    goto LABEL_36;
  }
  if ( s->stream.avail_in )
  {
LABEL_35:
    --s->stream.avail_in;
    v16 = *s->stream.next_in++;
    v13 = v16;
    goto LABEL_36;
  }
  *_errno() = 0;
  v14 = fread(s->inbuf, 1u, 0x4000u, s->file);
  s->stream.avail_in = v14;
  if ( v14 )
  {
    s->stream.next_in = s->inbuf;
    goto LABEL_35;
  }
  v15 = s->file;
  s->z_eof = 1;
  if ( (v15->_flag & 0x20) != 0 )
    s->z_err = -1;
  v13 = -1;
LABEL_36:
  if ( v7 != 8 || (v13 & 0xE0) != 0 )
  {
    s->z_err = -3;
    return;
  }
  v17 = 6;
  while ( 2 )
  {
    if ( !s->z_eof )
    {
      if ( !s->stream.avail_in )
      {
        *_errno() = 0;
        v18 = fread(s->inbuf, 1u, 0x4000u, s->file);
        s->stream.avail_in = v18;
        if ( !v18 )
        {
          v19 = s->file;
          s->z_eof = 1;
          if ( (v19->_flag & 0x20) != 0 )
            s->z_err = -1;
          goto LABEL_46;
        }
        s->stream.next_in = s->inbuf;
      }
      v20 = s->stream.next_in + 1;
      --s->stream.avail_in;
      s->stream.next_in = v20;
    }
LABEL_46:
    if ( --v17 )
      continue;
    break;
  }
  if ( (v13 & 4) != 0 )
  {
    byte = get_byte(s);
    v22 = (get_byte(s) << 8) + byte;
    do
    {
      v23 = v22--;
      if ( !v23 || s->z_eof )
        break;
      if ( !s->stream.avail_in )
      {
        *_errno() = 0;
        v24 = fread(s->inbuf, 1u, 0x4000u, s->file);
        s->stream.avail_in = v24;
        if ( !v24 )
        {
          v27 = s->file;
          s->z_eof = 1;
          if ( (v27->_flag & 0x20) != 0 )
            s->z_err = -1;
          break;
        }
        s->stream.next_in = s->inbuf;
      }
      next_in = s->stream.next_in;
      --s->stream.avail_in;
      v26 = *next_in;
      s->stream.next_in = next_in + 1;
    }
    while ( v26 != -1 );
  }
  if ( (v13 & 8) != 0 )
  {
    while ( !s->z_eof )
    {
      if ( !s->stream.avail_in )
      {
        *_errno() = 0;
        v28 = fread(s->inbuf, 1u, 0x4000u, s->file);
        s->stream.avail_in = v28;
        if ( !v28 )
        {
          v31 = s->file;
          s->z_eof = 1;
          if ( (v31->_flag & 0x20) != 0 )
            s->z_err = -1;
          break;
        }
        s->stream.next_in = s->inbuf;
      }
      v29 = s->stream.next_in;
      --s->stream.avail_in;
      v30 = *v29;
      s->stream.next_in = v29 + 1;
      if ( !v30 || v30 == -1 )
        break;
    }
  }
  if ( (v13 & 0x10) != 0 )
  {
    while ( !s->z_eof )
    {
      if ( !s->stream.avail_in )
      {
        *_errno() = 0;
        v32 = fread(s->inbuf, 1u, 0x4000u, s->file);
        s->stream.avail_in = v32;
        if ( !v32 )
        {
          v35 = s->file;
          s->z_eof = 1;
          if ( (v35->_flag & 0x20) != 0 )
            s->z_err = -1;
          break;
        }
        s->stream.next_in = s->inbuf;
      }
      v33 = s->stream.next_in;
      --s->stream.avail_in;
      v34 = *v33;
      s->stream.next_in = v33 + 1;
      if ( !v34 || v34 == -1 )
        break;
    }
  }
  if ( (v13 & 2) != 0 )
  {
    for ( j = 2; j; --j )
    {
      if ( !s->z_eof )
      {
        if ( !s->stream.avail_in )
        {
          *_errno() = 0;
          v37 = fread(s->inbuf, 1u, 0x4000u, s->file);
          s->stream.avail_in = v37;
          if ( !v37 )
          {
            v38 = s->file;
            s->z_eof = 1;
            if ( (v38->_flag & 0x20) != 0 )
              s->z_err = -1;
            continue;
          }
          s->stream.next_in = s->inbuf;
        }
        v39 = s->stream.next_in + 1;
        --s->stream.avail_in;
        s->stream.next_in = v39;
      }
    }
  }
  s->z_err = s->z_eof != 0 ? 0xFFFFFFFD : 0;
}

//----- (00499B00) --------------------------------------------------------
int __usercall destroy@<eax>(tagEntry *s@<esi>)
{
  int v1; // edi
  char pEntryPrev; // al
  unsigned int v4; // eax

  v1 = 0;
  if ( !s )
    return -2;
  if ( s[6].pEntryPrev )
    free(s[6].pEntryPrev);
  if ( s[2].pEntryNext )
  {
    pEntryPrev = (char)s[7].pEntryPrev;
    if ( pEntryPrev == 119 )
    {
      v4 = deflateEnd((z_stream_s *)s);
    }
    else
    {
      if ( pEntryPrev != 114 )
        goto LABEL_11;
      v4 = inflateEnd((z_stream_s *)s);
    }
    v1 = v4;
  }
LABEL_11:
  if ( s[5].pEntryNext && fclose((_iobuf *)s[5].pEntryNext) && *_errno() != 29 )
    v1 = -1;
  if ( (int)s[4].pEntryPrev < 0 )
    v1 = (int)s[4].pEntryPrev;
  if ( s[5].pEntryPrev )
    free(s[5].pEntryPrev);
  if ( s[6].sizeFront )
    free((tagEntry *)s[6].sizeFront);
  if ( s[7].sizeFront )
    free((tagEntry *)s[7].sizeFront);
  free(s);
  return v1;
}

//----- (00499BB0) --------------------------------------------------------
int __cdecl gzwrite(void *file, const unsigned __int8 *buf, unsigned int len)
{
  int v3; // eax
  int v4; // eax
  int v5; // ecx
  unsigned __int8 *v7; // [esp-18h] [ebp-1Ch]
  _iobuf *v8; // [esp-Ch] [ebp-10h]

  if ( !file || *((_BYTE *)file + 92) != 119 )
    return -2;
  *(_DWORD *)file = buf;
  *((_DWORD *)file + 1) = len;
  if ( !len )
    goto LABEL_11;
  while ( 1 )
  {
    if ( *((_DWORD *)file + 4) )
      goto LABEL_7;
    v8 = (_iobuf *)*((_DWORD *)file + 16);
    v7 = (unsigned __int8 *)*((_DWORD *)file + 18);
    *((_DWORD *)file + 3) = v7;
    if ( fwrite(v7, 1u, 0x4000u, v8) != 0x4000 )
      break;
    *((_DWORD *)file + 4) = 0x4000;
LABEL_7:
    v3 = deflate((z_stream_s *)file, 0);
    *((_DWORD *)file + 14) = v3;
    if ( v3 || !*((_DWORD *)file + 1) )
      goto LABEL_11;
  }
  *((_DWORD *)file + 14) = -1;
LABEL_11:
  v4 = crc32(*((_DWORD *)file + 19), buf, len);
  v5 = *((_DWORD *)file + 1);
  *((_DWORD *)file + 19) = v4;
  return len - v5;
}

//----- (00499C50) --------------------------------------------------------
int __usercall do_flush@<eax>(void *file@<eax>, unsigned int flush)
{
  BOOL v3; // ebx
  int v4; // eax
  unsigned int v5; // edi
  int v6; // eax
  int result; // eax

  v3 = 0;
  if ( file && *((_BYTE *)file + 92) == 119 )
  {
    *((_DWORD *)file + 1) = 0;
    while ( 1 )
    {
      v4 = *((_DWORD *)file + 4);
      v5 = 0x4000 - v4;
      if ( v4 != 0x4000 )
      {
        if ( fwrite(*((unsigned __int8 **)file + 18), 1u, v5, *((_iobuf **)file + 16)) != v5 )
        {
          result = -1;
          *((_DWORD *)file + 14) = -1;
          return result;
        }
        *((_DWORD *)file + 3) = *((_DWORD *)file + 18);
        *((_DWORD *)file + 4) = 0x4000;
      }
      if ( !v3 )
      {
        v6 = deflate((z_stream_s *)file, flush);
        *((_DWORD *)file + 14) = v6;
        if ( !v5 && v6 == -5 )
          *((_DWORD *)file + 14) = 0;
        v3 = *((_DWORD *)file + 4) || *((_DWORD *)file + 14) == 1;
        if ( *((_DWORD *)file + 14) < 2u )
          continue;
      }
      return *((_DWORD *)file + 14) != 1 ? *((_DWORD *)file + 14) : 0;
    }
  }
  return -2;
}

//----- (00499D10) --------------------------------------------------------
void __usercall putLong(_iobuf *file@<ebx>, unsigned int x@<eax>)
{
  int v3; // edi

  v3 = 4;
  do
  {
    fputc(x, file);
    x >>= 8;
    --v3;
  }
  while ( v3 );
}

//----- (00499D40) --------------------------------------------------------
int __cdecl gzclose(void *file)
{
  if ( !file )
    return -2;
  if ( *((_BYTE *)file + 92) == 119 && !do_flush(file, 4u) )
  {
    putLong(*((_iobuf **)file + 16), *((_DWORD *)file + 19));
    putLong(*((_iobuf **)file + 16), *((_DWORD *)file + 2));
  }
  return destroy((tagEntry *)file);
}

//----- (00499D90) --------------------------------------------------------
_DWORD *__cdecl gz_open(const char *path, const char *mode, int fd)
{
  unsigned int v4; // ebp
  const char *v5; // edi
  _DWORD *v6; // eax
  _DWORD *v7; // esi
  char *v8; // eax
  char v9; // al
  char v10; // al
  int v11; // edi
  LPVOID v12; // eax
  LPVOID v14; // eax
  _iobuf *v15; // eax
  char *m; // [esp+Ch] [ebp-58h]
  char fmode[80]; // [esp+10h] [ebp-54h] BYREF
  unsigned int cookie; // [esp+60h] [ebp-4h] BYREF
  unsigned int modea; // [esp+6Ch] [ebp+8h]

  m = fmode;
  v4 = -1;
  modea = 0;
  v5 = mode;
  if ( !path || !mode )
    return 0;
  v6 = malloc((tagHeader *)0x64);
  v7 = v6;
  if ( !v6 )
    return 0;
  v6[8] = 0;
  v6[9] = 0;
  v6[10] = 0;
  v6[17] = 0;
  *v6 = 0;
  v6[18] = 0;
  v6[3] = 0;
  v6[4] = 0;
  v6[1] = 0;
  v6[16] = 0;
  v6[14] = 0;
  v6[15] = 0;
  v6[19] = crc32(0, 0, 0);
  v7[20] = 0;
  v7[22] = 0;
  v8 = (char *)malloc((tagHeader *)(strlen(path) + 1));
  v7[21] = v8;
  if ( !v8 )
    goto LABEL_25;
  strcpy(v8, path);
  *((_BYTE *)v7 + 92) = 0;
  do
  {
    if ( *v5 == 114 )
      *((_BYTE *)v7 + 92) = 114;
    if ( *v5 == 119 || *v5 == 97 )
      *((_BYTE *)v7 + 92) = 119;
    v9 = *v5;
    if ( *v5 < 48 || v9 > 57 )
    {
      if ( v9 == 102 )
      {
        modea = 1;
      }
      else if ( v9 == 104 )
      {
        modea = 2;
      }
      else
      {
        *m++ = v9;
      }
    }
    else
    {
      v4 = v9 - 48;
    }
    ++v5;
  }
  while ( v9 && m != (char *)&cookie );
  v10 = *((_BYTE *)v7 + 92);
  if ( !v10 )
  {
LABEL_25:
    destroy((tagEntry *)v7);
    return 0;
  }
  if ( v10 == 119 )
  {
    v11 = deflateInit2_((z_stream_s *)v7, v4, 8, -15, 8, modea, "1.1.4", 56);
    v12 = malloc((tagHeader *)0x4000);
    v7[18] = v12;
    v7[3] = v12;
    if ( v11 || !v12 )
      goto LABEL_25;
  }
  else
  {
    v14 = malloc((tagHeader *)0x4000);
    v7[17] = v14;
    *v7 = v14;
    if ( inflateInit2_((z_stream_s *)v7, -15, "1.1.4", 56) || !v7[17] )
      goto LABEL_25;
  }
  v7[4] = 0x4000;
  *_errno() = 0;
  if ( fd >= 0 )
    v15 = _fdopen(fd, fmode);
  else
    v15 = fopen(path, fmode);
  v7[16] = v15;
  if ( !v15 )
    goto LABEL_25;
  if ( *((_BYTE *)v7 + 92) == 119 )
  {
    fprintf(v15, "%c%c%c%c%c%c%c%c%c%c", gz_magic[0], gz_magic[1], 8, 0, 0, 0, 0, 0, 0, 11);
    v7[24] = 10;
  }
  else
  {
    check_header((gz_stream *)v7);
    v7[24] = ftell((_iobuf *)v7[16]) - v7[1];
  }
  return v7;
}

//----- (0049A000) --------------------------------------------------------
_DWORD *__cdecl gzopen(const char *path, const char *mode)
{
  return gz_open(path, mode, -1);
}

//----- (0049A020) --------------------------------------------------------
char __cdecl SendLogPacket::ServerLogin(CSendStream *LogSendStream, unsigned int dwServerID)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(LogSendStream, (char *)0x14);
  if ( !Buffer )
    return 0;
  *(_DWORD *)Buffer = 0;
  *((_DWORD *)Buffer + 1) = 0;
  *((_DWORD *)Buffer + 2) = 0;
  *((_DWORD *)Buffer + 3) = 0;
  *((_DWORD *)Buffer + 4) = 0;
  *((_DWORD *)Buffer + 3) = dwServerID;
  return CSendStream::WrapHeader(LogSendStream, 0x14u, 0x28u, 0, 0);
}

//----- (0049A060) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0049A0A0) --------------------------------------------------------
CPacketDispatch *__thiscall CMultiDispatch::GetDispatch(CMultiDispatch *this, unsigned int dispatchKey)
{
  CPacketDispatch *second; // edi
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator find; // [esp+4h] [ebp-14h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+8h] [ebp-10h]
  int v7; // [esp+14h] [ebp-4h]

  sync.m_Lock = &this->m_DispatchLock;
  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  v7 = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_DispatchMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&find,
    &dispatchKey);
  if ( find._Ptr == this->m_DispatchMap._Myhead )
  {
    LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
    return 0;
  }
  else
  {
    second = find._Ptr->_Myval.second;
    CSession::AddRef(second->m_Session);
    LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
    return second;
  }
}

//----- (0049A130) --------------------------------------------------------
void __thiscall CMultiDispatch::Storage::Storage(
        CMultiDispatch::Storage *this,
        CMultiDispatch *multiDispatch,
        unsigned int dispatchKey)
{
  CPacketDispatch *Dispatch; // eax

  Dispatch = CMultiDispatch::GetDispatch(multiDispatch, dispatchKey);
  this->m_MultiDispatch = multiDispatch;
  this->m_lpDispatch = Dispatch;
}

//----- (0049A150) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CPacketDispatch *> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CPacketDispatch *> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0049A300) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0049A5C0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  unsigned int Left; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (unsigned int)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}

//----- (0049A680) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0049A740) --------------------------------------------------------
void __thiscall CMultiDispatch::InternalRemoveDispatch(CMultiDispatch *this, unsigned int dispatchKey)
{
  std::map<unsigned long,CPacketDispatch *> *p_m_DispatchMap; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Ptr; // ebx
  CPacketDispatch *second; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator find; // [esp+Ch] [ebp-4h] BYREF

  p_m_DispatchMap = &this->m_DispatchMap;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_DispatchMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&find,
    &dispatchKey);
  Ptr = find._Ptr;
  if ( find._Ptr == this->m_DispatchMap._Myhead )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CMultiDispatch::InternalRemoveDispatch",
      aDWorkRylSource_54,
      126,
      "Reset dispatch failed. Invalid dispatch key(%u)",
      dispatchKey);
  }
  else
  {
    second = find._Ptr->_Myval.second;
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CMultiDispatch::InternalRemoveDispatch",
      aDWorkRylSource_54,
      132,
      "Reset dispatch(Key:%u/DP:0x%p)",
      dispatchKey,
      second);
    CSession::Release(second->m_Session);
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
      p_m_DispatchMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *)&dispatchKey,
      (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator)Ptr);
  }
}

//----- (0049A7E0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator _Where,
        std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // eax
  std::pair<unsigned long const ,unsigned long> *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Right; // eax
  CPacketDispatch *first; // ebp
  bool v10; // cf
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator,bool> v12; // [esp+8h] [ebp-8h] BYREF

  if ( !this->_Mysize )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
      this,
      result,
      1,
      this->_Myhead,
      _Val);
    return result;
  }
  Myhead = this->_Myhead;
  v7 = _Val;
  if ( _Where._Ptr == Myhead->_Left )
  {
    if ( _Val->first < _Where._Ptr->_Myval.first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  if ( _Where._Ptr == Myhead )
  {
    Right = Myhead->_Right;
    if ( Right->_Myval.first < _Val->first )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
        this,
        result,
        0,
        Right,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  first = (CPacketDispatch *)_Val->first;
  v10 = _Where._Ptr->_Myval.first < _Val->first;
  if ( _Where._Ptr->_Myval.first > _Val->first )
  {
    _Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr;
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    if ( _Val[1].second < (unsigned int)first )
    {
      if ( *(_BYTE *)(_Val[1].first + 21) )
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
          this,
          result,
          0,
          (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)_Val,
          v7);
      else
        std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
          this,
          result,
          1,
          _Where._Ptr,
          v7);
      return result;
    }
    v10 = _Where._Ptr->_Myval.first < (unsigned int)first;
  }
  if ( !v10
    || (_Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Val),
        _Val != (std::pair<unsigned long const ,unsigned long> *)this->_Myhead)
    && (unsigned int)first >= _Val[1].second )
  {
LABEL_23:
    Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(
            this,
            &v12,
            (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)v7)->first._Ptr;
    v5 = result;
    result->_Ptr = Ptr;
    return v5;
  }
  if ( _Where._Ptr->_Right->_Isnil )
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
      this,
      result,
      0,
      _Where._Ptr,
      v7);
  else
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Insert(
      this,
      result,
      1,
      (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)_Val,
      v7);
  return result;
}

//----- (0049A950) --------------------------------------------------------
char __thiscall CMultiDispatch::SetDispatch(
        CMultiDispatch *this,
        unsigned int dispatchKey,
        CPacketDispatch *lpDispatch)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  CPacketDispatch *v4; // edi
  CPacketDispatch *v6; // ebp
  CPacketDispatch_vtbl *v8; // ebp
  std::pair<unsigned long const ,CPacketDispatch *> _Val; // [esp+4h] [ebp-14h] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v10; // [esp+Ch] [ebp-Ch]
  void *v11; // [esp+10h] [ebp-8h]
  int v12; // [esp+14h] [ebp-4h]

  v12 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v11 = &_ehhandler__SetDispatch_CMultiDispatch__QAE_NKPAVCPacketDispatch___Z;
  v10 = ExceptionList;
  v4 = lpDispatch;
  if ( lpDispatch )
  {
    EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
    v12 = 0;
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
      (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_DispatchMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&lpDispatch,
      &dispatchKey);
    v6 = lpDispatch;
    if ( lpDispatch == (CPacketDispatch *)this->m_DispatchMap._Myhead )
    {
      CSession::AddRef(v4->m_Session);
      _Val.first = dispatchKey;
      _Val.second = v4;
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::insert(
        &this->m_DispatchMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *)&lpDispatch,
        (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator)v6,
        (std::pair<unsigned long const ,unsigned long> *)&_Val);
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CMultiDispatch::SetDispatch",
        aDWorkRylSource_54,
        25,
        "Setting new dispatch(Key:%u/DP:0x%p)",
        dispatchKey,
        v4);
      LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
      return 1;
    }
    v8 = lpDispatch[2].__vftable;
    if ( v8 )
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CMultiDispatch::SetDispatch",
        aDWorkRylSource_54,
        34,
        "DP:0x%p/Already dispatch exist. cannot set dispatch",
        v8);
    LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CMultiDispatch::SetDispatch",
      aDWorkRylSource_54,
      12,
      "this:0x%p/Setting null dispatch",
      this);
  }
  return 0;
}

//----- (0049AA80) --------------------------------------------------------
void __thiscall CMultiDispatch::RemoveDispatch(CMultiDispatch *this, unsigned int dispatchKey)
{
  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  CMultiDispatch::InternalRemoveDispatch(this, dispatchKey);
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
}

//----- (0049AAE0) --------------------------------------------------------
void __thiscall std::map<unsigned long,CPacketDispatch *>::~map<unsigned long,CPacketDispatch *>(
        std::map<unsigned long,CPacketDispatch *> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (0049AB10) --------------------------------------------------------
void __thiscall CMultiDispatch::~CMultiDispatch(CMultiDispatch *this)
{
  bool v2; // zf
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *i; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator v8; // [esp-8h] [ebp-2Ch]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v9; // [esp-4h] [ebp-28h]
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator pos; // [esp+Ch] [ebp-18h] BYREF
  CMultiDispatch *v11; // [esp+10h] [ebp-14h]
  CLock<CCSLock>::Syncronize sync; // [esp+14h] [ebp-10h] BYREF
  int v13; // [esp+20h] [ebp-4h]

  v11 = this;
  v13 = 0;
  sync.m_Lock = &this->m_DispatchLock;
  EnterCriticalSection(&this->m_DispatchLock.m_CSLock);
  v2 = this->m_DispatchMap._Mysize == 0;
  LOBYTE(v13) = 2;
  if ( !v2 )
  {
    Myhead = this->m_DispatchMap._Myhead;
    Left = Myhead->_Left;
    for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CMultiDispatch::~CMultiDispatch",
        aDWorkRylSource_54,
        111,
        "this:0x%p/Dispatch is not removed until destroy",
        Left->_Myval.second);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
    }
    Parent = this->m_DispatchMap._Myhead->_Parent;
    for ( i = Parent; !i->_Isnil; Parent = i )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::_Erase(
        &this->m_DispatchMap,
        i->_Right);
      i = i->_Left;
      operator delete(Parent);
    }
    this->m_DispatchMap._Myhead->_Parent = this->m_DispatchMap._Myhead;
    v7 = this->m_DispatchMap._Myhead;
    this->m_DispatchMap._Mysize = 0;
    v7->_Left = v7;
    this->m_DispatchMap._Myhead->_Right = this->m_DispatchMap._Myhead;
  }
  LeaveCriticalSection(&this->m_DispatchLock.m_CSLock);
  v9 = this->m_DispatchMap._Myhead;
  v8._Ptr = v9->_Left;
  LOBYTE(v13) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::erase(
    &this->m_DispatchMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator *)&sync,
    v8,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::iterator)v9);
  operator delete(this->m_DispatchMap._Myhead);
  this->m_DispatchMap._Myhead = 0;
  this->m_DispatchMap._Mysize = 0;
  DeleteCriticalSection(&this->m_DispatchLock.m_CSLock);
}

//----- (0049AC30) --------------------------------------------------------
void __thiscall CMultiDispatch::CMultiDispatch(CMultiDispatch *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *v2; // eax

  InitializeCriticalSection(&this->m_DispatchLock.m_CSLock);
  v2 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPacketDispatch *> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_DispatchMap);
  this->m_DispatchMap._Myhead = v2;
  v2->_Isnil = 1;
  this->m_DispatchMap._Myhead->_Parent = this->m_DispatchMap._Myhead;
  this->m_DispatchMap._Myhead->_Left = this->m_DispatchMap._Myhead;
  this->m_DispatchMap._Myhead->_Right = this->m_DispatchMap._Myhead;
  this->m_DispatchMap._Mysize = 0;
}

//----- (0049ACA0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharBGServerMoveZone(
        CSendStream *SendStream,
        unsigned __int8 cZone,
        unsigned __int8 cMoveType,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x10);
  if ( !Buffer )
    return 0;
  Buffer[14] = cMoveType;
  *((_WORD *)Buffer + 6) = 0;
  Buffer[15] = cZone;
  return CSendStream::WrapCrypt(SendStream, 0x10u, 0x9Au, 0, wError);
}

//----- (0049ACF0) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendCharBGServerMileageChange(
        CSendStream *SendStream,
        unsigned int dwCID,
        unsigned __int8 cGroup,
        unsigned __int8 cCmd,
        unsigned int dwGold,
        unsigned int dwMileage,
        unsigned __int16 wError)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x1A);
  if ( !Buffer )
    return 0;
  Buffer[16] = cGroup;
  *((_DWORD *)Buffer + 3) = dwCID;
  *(_DWORD *)(Buffer + 18) = dwGold;
  Buffer[17] = cCmd;
  *(_DWORD *)(Buffer + 22) = dwMileage;
  return CSendStream::WrapCrypt(SendStream, 0x1Au, 0x9Bu, 0, wError);
}

//----- (0049AD40) --------------------------------------------------------
char __cdecl GameClientSendPacket::SendRegularServerList(CSendStream *SendStream)
{
  char *Buffer; // esi
  char *v2; // edi
  char *v3; // eax
  int v4; // ecx
  char *v5; // ebx
  int v6; // ebx
  bool v7; // zf
  int v8; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x18E);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = 0;
  memset(Buffer + 16, 0, 0x64u);
  v2 = Buffer + 119;
  v3 = Buffer + 119;
  v4 = 10;
  do
  {
    v5 = v3;
    *(_DWORD *)v3 = 0;
    *((_DWORD *)v3 + 1) = 0;
    *((_DWORD *)v3 + 2) = 0;
    *((_WORD *)v3 + 6) = 0;
    v3 += 28;
    --v4;
    v5[14] = 0;
  }
  while ( v4 );
  v6 = 0;
  v7 = (unsigned __int16)CRegularAgentDispatch::ms_cCurrentGroupNum == 0;
  *((_WORD *)Buffer + 58) = CRegularAgentDispatch::ms_cCurrentGroupNum;
  if ( !v7 )
  {
    do
    {
      v8 = (unsigned __int8)v6;
      if ( CRegularAgentDispatch::ms_AgentServerInfo[v8].m_ServerAddress.S_un.S_addr )
        strncpy(v2, CRegularAgentDispatch::ms_AgentServerInfo[v8].m_szGroupName, 0xFu);
      ++v6;
      v2 += 28;
    }
    while ( v6 < *((unsigned __int16 *)Buffer + 58) );
  }
  return CSendStream::WrapCrypt(SendStream, 0x18Eu, 0x56u, 0, 0);
}

//----- (0049AE00) --------------------------------------------------------
CCrc32 *__thiscall CCrc32::CCrc32(CCrc32 *this)
{
  CCrc32 *result; // eax
  int v2; // esi
  CCrc32 *v3; // edi
  unsigned int v4; // edx
  int v5; // ebx

  result = this;
  v2 = 0;
  v3 = this;
  do
  {
    v4 = v2;
    v5 = 8;
    do
    {
      if ( (v4 & 1) != 0 )
        v4 = (v4 >> 1) ^ 0xEDB88320;
      else
        v4 >>= 1;
      --v5;
    }
    while ( v5 );
    *(_DWORD *)v3 = v4;
    ++v2;
    v3 = (CCrc32 *)((char *)v3 + 4);
  }
  while ( v2 < 256 );
  return result;
}

//----- (0049AE50) --------------------------------------------------------
char __thiscall CCrc32::Crc32Mem(CCrc32 *this, unsigned __int8 *a2, unsigned int a3, unsigned int *a4)
{
  *a4 = -1;
  CCrc32::Crc32MemCont(this, a2, a3, a4);
  *a4 = ~*a4;
  return 1;
}

//----- (0049AE80) --------------------------------------------------------
char __thiscall CCrc32::Crc32MemCont(CCrc32 *this, unsigned __int8 *a2, unsigned int a3, unsigned int *a4)
{
  unsigned __int8 *v4; // edx
  unsigned int v7; // ecx

  v4 = a2;
  if ( a2 )
  {
    if ( a2 < &a2[a3] )
    {
      do
      {
        v7 = (*a4 >> 8) ^ *((_DWORD *)this + (*v4++ ^ (unsigned __int8)*a4));
        *a4 = v7;
      }
      while ( v4 < &a2[a3] );
    }
    return 1;
  }
  else
  {
    *((_DWORD *)this + 256) = 1;
    return 0;
  }
}

//----- (0049AEE0) --------------------------------------------------------
void __thiscall OleDB::OleDB(OleDB *this)
{
  this->__vftable = (OleDB_vtbl *)&OleDB::`vftable';
  this->m_pIDBInit = 0;
  this->m_pIDBCreateSession = 0;
  this->m_pIDBCreateCommand = 0;
  this->m_pIRowset = 0;
  this->m_pIRowsetChange = 0;
  CoInitializeEx(0, 0);
  memset(&this->m_ColInfo, 0, sizeof(this->m_ColInfo));
  memset(this->m_ErrorString, 0, sizeof(this->m_ErrorString));
}
// 4F6864: using guessed type void *OleDB::`vftable';

//----- (0049AF40) --------------------------------------------------------
void __thiscall OleDB::~OleDB(OleDB *this)
{
  IDBInitialize *m_pIDBInit; // eax
  IDBCreateSession *m_pIDBCreateSession; // eax
  IDBCreateCommand *m_pIDBCreateCommand; // eax
  IRowset *m_pIRowset; // eax
  IRowsetChange *m_pIRowsetChange; // eax

  m_pIDBInit = this->m_pIDBInit;
  this->__vftable = (OleDB_vtbl *)&OleDB::`vftable';
  if ( m_pIDBInit )
  {
    m_pIDBInit->Uninitialize(m_pIDBInit);
    this->m_pIDBInit->Release(this->m_pIDBInit);
    this->m_pIDBInit = 0;
  }
  m_pIDBCreateSession = this->m_pIDBCreateSession;
  if ( m_pIDBCreateSession )
  {
    m_pIDBCreateSession->Release(this->m_pIDBCreateSession);
    this->m_pIDBCreateSession = 0;
  }
  m_pIDBCreateCommand = this->m_pIDBCreateCommand;
  if ( m_pIDBCreateCommand )
  {
    m_pIDBCreateCommand->Release(this->m_pIDBCreateCommand);
    this->m_pIDBCreateCommand = 0;
  }
  m_pIRowset = this->m_pIRowset;
  if ( m_pIRowset )
  {
    m_pIRowset->Release(this->m_pIRowset);
    this->m_pIRowset = 0;
  }
  m_pIRowsetChange = this->m_pIRowsetChange;
  if ( m_pIRowsetChange )
  {
    m_pIRowsetChange->Release(this->m_pIRowsetChange);
    this->m_pIRowsetChange = 0;
  }
  CoUninitialize();
}
// 4F6864: using guessed type void *OleDB::`vftable';

//----- (0049AFD0) --------------------------------------------------------
char __thiscall OleDB::HandleError(OleDB *this, int ErrorLine_In, HRESULT hResult_In, char *Buffer_In)
{
  bool v5; // sf
  IErrorInfo *v6; // eax
  const char *v7; // eax
  const char *v9; // eax
  IErrorInfo *pIErrorInfo; // [esp+Ch] [ebp-210h] BYREF
  unsigned __int16 *ErrorString; // [esp+10h] [ebp-20Ch] BYREF
  int Bool; // [esp+14h] [ebp-208h] BYREF
  char Buffer[512]; // [esp+18h] [ebp-204h] BYREF

  pIErrorInfo = 0;
  ErrorString = 0;
  v5 = GetErrorInfo(0, &pIErrorInfo) < 0;
  v6 = pIErrorInfo;
  if ( v5 )
    goto LABEL_8;
  if ( !pIErrorInfo )
  {
    v7 = Buffer_In;
    if ( !Buffer_In )
      v7 = szLoseCharName;
LABEL_12:
    _snprintf(this->m_ErrorString, 0x200u, "[%3d:0x%5x] [M:%s]", ErrorLine_In, hResult_In, v7);
    return 0;
  }
  if ( pIErrorInfo->GetDescription(pIErrorInfo, &ErrorString) < 0 )
  {
    v6 = pIErrorInfo;
LABEL_8:
    if ( v6 )
    {
      v6->Release(v6);
      pIErrorInfo = 0;
    }
    v7 = Buffer_In;
    if ( !Buffer_In )
      v7 = szLoseCharName;
    goto LABEL_12;
  }
  WideCharToMultiByte(0, 0, ErrorString, -1, Buffer, 462, 0, &Bool);
  v9 = Buffer_In;
  if ( !Buffer_In )
    v9 = szLoseCharName;
  _snprintf(this->m_ErrorString, 0x200u, "[%3d:0x%5x] %s  [M:%s]", ErrorLine_In, hResult_In, Buffer, v9);
  if ( pIErrorInfo )
    pIErrorInfo->Release(pIErrorInfo);
  return 1;
}

//----- (0049B130) --------------------------------------------------------
char __thiscall OleDB::CreateSession(OleDB *this)
{
  IDBCreateSession *m_pIDBCreateSession; // eax
  IDBCreateSession **p_m_pIDBCreateSession; // esi
  HRESULT v5; // esi

  if ( this->m_pIDBInit )
  {
    m_pIDBCreateSession = this->m_pIDBCreateSession;
    p_m_pIDBCreateSession = &this->m_pIDBCreateSession;
    if ( m_pIDBCreateSession )
    {
      m_pIDBCreateSession->Release(m_pIDBCreateSession);
      *p_m_pIDBCreateSession = 0;
    }
    v5 = this->m_pIDBInit->QueryInterface(this->m_pIDBInit, &IID_IDBCreateSession, (void **)&this->m_pIDBCreateSession);
    if ( v5 >= 0 )
    {
      return 1;
    }
    else
    {
      CServerLog::DetailLog(&g_Log, LOG_SYSERR, "OleDB::CreateSession", aDWorkRylSource_85, 1054, aIdbinitialize);
      OleDB::HandleError(this, 1055, v5, "IDBInitialize::QueryInterface");
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_SYSERR, "OleDB::CreateSession", aDWorkRylSource_85, 1045, aIdbinitialize_0);
    return 0;
  }
}

//----- (0049B1E0) --------------------------------------------------------
char __thiscall OleDB::DBCreateCommand(OleDB *this)
{
  char result; // al
  IDBCreateCommand *m_pIDBCreateCommand; // eax
  HRESULT v4; // eax
  HRESULT v5; // eax
  HRESULT v6; // edi
  DWORD LastError; // eax

  if ( this->m_pIDBCreateSession || (result = OleDB::CreateSession(this)) != 0 )
  {
    m_pIDBCreateCommand = this->m_pIDBCreateCommand;
    if ( m_pIDBCreateCommand )
    {
      m_pIDBCreateCommand->Release(m_pIDBCreateCommand);
      this->m_pIDBCreateCommand = 0;
    }
    v4 = this->m_pIDBCreateSession->CreateSession(
           this->m_pIDBCreateSession,
           0,
           &IID_IDBCreateCommand,
           &this->m_pIDBCreateCommand);
    if ( v4 < 0 )
    {
      OleDB::HandleError(this, 1087, v4, "IDBCreateCommand::CreateSession");
      if ( !OleDB::CreateSession(this) )
        return 0;
      v5 = this->m_pIDBCreateSession->CreateSession(
             this->m_pIDBCreateSession,
             0,
             &IID_IDBCreateCommand,
             &this->m_pIDBCreateCommand);
      v6 = v5;
      if ( v5 < 0 )
      {
        OleDB::HandleError(this, 1093, v5, "IDBCreateCommand::CreateSession");
        LastError = GetLastError();
        CServerLog::DetailLog(
          &g_Log,
          LOG_SYSERR,
          "OleDB::DBCreateCommand",
          aDWorkRylSource_85,
          1094,
          aCreatesession,
          LastError,
          v6);
        return 0;
      }
    }
    return this->m_pIDBCreateSession != 0;
  }
  return result;
}

//----- (0049B2C0) --------------------------------------------------------
char __thiscall OleDB::AllocResultCols(OleDB *this, IUnknown *lpIUnknown_In, OleDB::_RESULT_COLS *Rsult_Cols)
{
  IUnknown_vtbl *v3; // ecx
  IColumnsInfo *v4; // eax
  bool v6; // sf
  IColumnsInfo *pIColumnsInfo; // [esp+1Ch] [ebp-4h] BYREF

  pIColumnsInfo = (IColumnsInfo *)this;
  v3 = lpIUnknown_In->__vftable;
  pIColumnsInfo = 0;
  if ( v3->QueryInterface(lpIUnknown_In, &IID_IColumnsInfo, (void **)&pIColumnsInfo) < 0 )
  {
    v4 = pIColumnsInfo;
LABEL_3:
    if ( v4 )
      v4->Release(v4);
    return 0;
  }
  v6 = pIColumnsInfo->GetColumnInfo(
         pIColumnsInfo,
         (unsigned int *)Rsult_Cols,
         &Rsult_Cols->lpDBColumnInfo,
         &Rsult_Cols->lpStringsBuffer) < 0;
  v4 = pIColumnsInfo;
  if ( v6 )
    goto LABEL_3;
  if ( pIColumnsInfo )
    pIColumnsInfo->Release(pIColumnsInfo);
  return 1;
}

//----- (0049B330) --------------------------------------------------------
char __thiscall OleDB::ReleaseResultCols(OleDB *this, IUnknown *lpIUnknown_In, OleDB::_RESULT_COLS *Rsult_Cols)
{
  bool v3; // zf
  IMalloc *v5; // eax
  IMalloc *pMalloc; // [esp+10h] [ebp-4h] BYREF

  v3 = Rsult_Cols->ColNum == 0;
  pMalloc = 0;
  if ( v3 || CoGetMalloc(1u, &pMalloc) )
    return 0;
  if ( Rsult_Cols->lpDBColumnInfo )
  {
    pMalloc->Free(pMalloc, Rsult_Cols->lpDBColumnInfo);
    Rsult_Cols->lpDBColumnInfo = 0;
  }
  if ( Rsult_Cols->lpStringsBuffer )
  {
    pMalloc->Free(pMalloc, Rsult_Cols->lpStringsBuffer);
    Rsult_Cols->lpStringsBuffer = 0;
  }
  v5 = pMalloc;
  v3 = pMalloc == 0;
  Rsult_Cols->ColNum = 0;
  if ( !v3 )
    v5->Release(v5);
  return 1;
}

//----- (0049B3B0) --------------------------------------------------------
char __thiscall OleDB::SetConnectionProperties(
        OleDB *this,
        const char *ServerName_In,
        const char *DataBaseName_In,
        const char *UserID_In,
        const char *UserPass_In)
{
  tagVARIANT *p_vValue; // edx
  int v6; // esi
  char *p_cVal; // eax
  OleDB *v8; // esi
  IDBInitialize *m_pIDBInit; // eax
  HRESULT v10; // eax
  HRESULT v12; // eax
  IDBProperties *pIDBProperties; // [esp+18h] [ebp-464h] BYREF
  OleDB *v14; // [esp+1Ch] [ebp-460h]
  tagDBPROPSET dbPropSet[2]; // [esp+20h] [ebp-45Ch] BYREF
  tagDBPROP RowsetProp[1]; // [esp+50h] [ebp-42Ch] BYREF
  tagDBPROP DataSourceProp[4]; // [esp+84h] [ebp-3F8h] BYREF
  unsigned __int16 ServerName[100]; // [esp+154h] [ebp-328h] BYREF
  unsigned __int16 UserPass[100]; // [esp+21Ch] [ebp-260h] BYREF
  unsigned __int16 UserID[100]; // [esp+2E4h] [ebp-198h] BYREF
  OLECHAR DataBaseName[102]; // [esp+3ACh] [ebp-D0h] BYREF

  v14 = this;
  memset(ServerName, 0, sizeof(ServerName));
  memset(DataBaseName, 0, 200);
  memset(UserID, 0, sizeof(UserID));
  memset(UserPass, 0, sizeof(UserPass));
  if ( !this->m_pIDBInit )
    return 0;
  MultiByteToWideChar(0, 0, ServerName_In, -1, ServerName, 100);
  MultiByteToWideChar(0, 0, DataBaseName_In, -1, DataBaseName, 100);
  MultiByteToWideChar(0, 0, UserID_In, -1, UserID, 100);
  MultiByteToWideChar(0, 0, UserPass_In, -1, UserPass, 100);
  memset(dbPropSet, 0, 24);
  p_vValue = &DataSourceProp[0].vValue;
  v6 = 4;
  do
  {
    memset(&p_vValue[-3].11 + 1, 0, 0x34u);
    *(_DWORD *)&p_vValue[-2].vt = 0;
    p_cVal = &p_vValue[-2].cVal;
    *(_QWORD *)p_cVal = *(_QWORD *)&DB_NULLID.uGuid.guid.Data1;
    *((_QWORD *)p_cVal + 1) = *((_QWORD *)&DB_NULLID.uGuid.pguid + 1);
    p_vValue->vt = 8;
    *((_DWORD *)p_cVal + 4) = DB_NULLID.eKind;
    p_vValue = (tagVARIANT *)((char *)p_vValue + 52);
    --v6;
    *((_DWORD *)p_cVal + 5) = DB_NULLID.uName.pwszName;
  }
  while ( v6 );
  DataSourceProp[0].dwPropertyID = wcslen(ServerName) != 0 ? 0x3B : 0;
  DataSourceProp[0].vValue.decVal.Lo32 = (unsigned int)SysAllocString(ServerName);
  DataSourceProp[1].dwPropertyID = wcslen(DataBaseName) != 0 ? 0xE9 : 0;
  DataSourceProp[1].vValue.decVal.Lo32 = (unsigned int)SysAllocString(DataBaseName);
  DataSourceProp[2].dwPropertyID = wcslen(UserID) != 0 ? 0xC : 0;
  DataSourceProp[2].vValue.decVal.Lo32 = (unsigned int)SysAllocString(UserID);
  DataSourceProp[3].dwPropertyID = wcslen(UserPass) != 0 ? 9 : 0;
  DataSourceProp[3].vValue.decVal.Lo32 = (unsigned int)SysAllocString(UserPass);
  dbPropSet[0].guidPropertySet = DBPROPSET_DBINIT;
  dbPropSet[0].rgProperties = DataSourceProp;
  RowsetProp[0].colid = DB_NULLID;
  dbPropSet[0].cProperties = 4;
  RowsetProp[0].dwOptions = 0;
  RowsetProp[0].dwPropertyID = 117;
  RowsetProp[0].vValue.vt = 3;
  RowsetProp[0].vValue.decVal.Lo32 = -1;
  dbPropSet[1].guidPropertySet.Data1 = DBPROPSET_ROWSET.Data1;
  *(_DWORD *)&dbPropSet[1].guidPropertySet.Data2 = *(_DWORD *)&DBPROPSET_ROWSET.Data2;
  v8 = v14;
  dbPropSet[1].rgProperties = RowsetProp;
  *(_DWORD *)&dbPropSet[1].guidPropertySet.Data4[4] = *(_DWORD *)&DBPROPSET_ROWSET.Data4[4];
  m_pIDBInit = v14->m_pIDBInit;
  *(_DWORD *)dbPropSet[1].guidPropertySet.Data4 = *(_DWORD *)DBPROPSET_ROWSET.Data4;
  dbPropSet[1].cProperties = 1;
  v10 = m_pIDBInit->QueryInterface(m_pIDBInit, &IID_IDBProperties, (void **)&pIDBProperties);
  if ( v10 < 0 )
  {
    OleDB::HandleError(v8, 1248, v10, "IDBInitialize::QueryInterface");
    SysFreeString(DataSourceProp[0].vValue.bstrVal);
    SysFreeString(DataSourceProp[1].vValue.bstrVal);
    SysFreeString(DataSourceProp[2].vValue.bstrVal);
    SysFreeString(DataSourceProp[3].vValue.bstrVal);
    if ( pIDBProperties )
    {
      pIDBProperties->Release(pIDBProperties);
      return 0;
    }
    return 0;
  }
  v12 = pIDBProperties->SetProperties(pIDBProperties, 2u, dbPropSet);
  if ( v12 < 0 )
  {
    OleDB::HandleError(v8, 1262, v12, "IDBProperties::SetProperties");
    SysFreeString(DataSourceProp[0].vValue.bstrVal);
    SysFreeString(DataSourceProp[1].vValue.bstrVal);
    SysFreeString(DataSourceProp[2].vValue.bstrVal);
    SysFreeString(DataSourceProp[3].vValue.bstrVal);
    if ( pIDBProperties )
      pIDBProperties->Release(pIDBProperties);
    return 0;
  }
  SysFreeString(DataSourceProp[0].vValue.bstrVal);
  SysFreeString(DataSourceProp[1].vValue.bstrVal);
  SysFreeString(DataSourceProp[2].vValue.bstrVal);
  SysFreeString(DataSourceProp[3].vValue.bstrVal);
  if ( pIDBProperties )
    pIDBProperties->Release(pIDBProperties);
  return 1;
}

//----- (0049B800) --------------------------------------------------------
tagDBBINDING *__thiscall OleDB::AllocBindGetData(OleDB *this, int ColsNum_In, tagDBBINDING *pDBColumnInfo_In)
{
  int v3; // ebx
  tagDBBINDING *result; // eax
  unsigned int v5; // esi
  unsigned int *p_pTypeInfo; // edx
  unsigned int *p_obValue; // ecx
  unsigned int v8; // eax
  tagDBBINDING *ColsNum_Ina; // [esp+10h] [ebp+4h]

  v3 = ColsNum_In;
  result = (tagDBBINDING *)malloc((tagHeader *)(52 * ColsNum_In));
  v5 = 0;
  ColsNum_Ina = result;
  if ( v3 > 0 )
  {
    p_pTypeInfo = (unsigned int *)&pDBColumnInfo_In->pTypeInfo;
    p_obValue = &result->obValue;
    do
    {
      v8 = *(p_pTypeInfo - 2);
      *p_obValue = v5;
      *(p_obValue - 1) = v8;
      p_obValue[6] = 1;
      p_obValue[3] = 0;
      p_obValue[4] = 0;
      p_obValue[5] = 0;
      p_obValue[7] = 0;
      p_obValue[8] = 0;
      p_obValue[9] = *p_pTypeInfo;
      p_obValue[10] = 0;
      *((_WORD *)p_obValue + 22) = *((_WORD *)p_pTypeInfo + 2);
      v5 += *p_pTypeInfo;
      p_obValue += 13;
      p_pTypeInfo += 12;
      --v3;
    }
    while ( v3 );
    return ColsNum_Ina;
  }
  return result;
}

//----- (0049B880) --------------------------------------------------------
OleDB *__thiscall OleDB::`scalar deleting destructor'(OleDB *this, char a2)
{
  OleDB::~OleDB(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0049B8A0) --------------------------------------------------------
char __thiscall OleDB::ConnectSQLServer(
        OleDB *this,
        const char *ServerName_In,
        const char *DataBaseName_In,
        const char *UserID_In,
        const char *UserPass_In,
        OleDB::ConnType ConnType_In)
{
  int v6; // edx
  IDBInitialize *m_pIDBInit; // eax
  IDBInitialize **p_m_pIDBInit; // esi
  HRESULT v10; // eax
  _GUID ProviderCLSID; // [esp+10h] [ebp-14h] BYREF

  v6 = *(_DWORD *)&CLSID_MSDASQL.Data2;
  ProviderCLSID = CLSID_MSDASQL;
  switch ( ConnType_In )
  {
    case ConnType_ODBC:
      ProviderCLSID.Data1 = CLSID_MSDASQL.Data1;
      *(_DWORD *)ProviderCLSID.Data4 = *(_DWORD *)CLSID_MSDASQL.Data4;
      *(_DWORD *)&ProviderCLSID.Data4[4] = *(_DWORD *)&CLSID_MSDASQL.Data4[4];
      goto LABEL_7;
    case ConnType_MSSQL:
      v6 = *(_DWORD *)&CLSID_SQLOLEDB.Data2;
      ProviderCLSID.Data1 = CLSID_SQLOLEDB.Data1;
      *(_DWORD *)ProviderCLSID.Data4 = *(_DWORD *)CLSID_SQLOLEDB.Data4;
      *(_DWORD *)&ProviderCLSID.Data4[4] = *(_DWORD *)&CLSID_SQLOLEDB.Data4[4];
LABEL_7:
      *(_DWORD *)&ProviderCLSID.Data2 = v6;
      break;
    case ConnType_ORACLE:
      ProviderCLSID = CLSID_MSDAORA;
      break;
  }
  if ( !ServerName_In || !UserID_In || !UserPass_In )
    return 0;
  m_pIDBInit = this->m_pIDBInit;
  p_m_pIDBInit = &this->m_pIDBInit;
  if ( m_pIDBInit )
  {
    m_pIDBInit->Release(this->m_pIDBInit);
    *p_m_pIDBInit = 0;
  }
  CoInitializeEx(0, 0);
  if ( CoCreateInstance(&ProviderCLSID, 0, 1u, &IID_IDBInitialize, (LPVOID *)&this->m_pIDBInit) < 0 )
    return 0;
  OleDB::SetConnectionProperties(this, ServerName_In, DataBaseName_In, UserID_In, UserPass_In);
  v10 = (*p_m_pIDBInit)->Initialize(*p_m_pIDBInit);
  if ( v10 < 0 )
  {
    OleDB::HandleError(this, 163, v10, "IDBInitialize::Initialize");
    if ( *p_m_pIDBInit )
    {
      (*p_m_pIDBInit)->Release(*p_m_pIDBInit);
      *p_m_pIDBInit = 0;
    }
    return 0;
  }
  return OleDB::CreateSession(this);
}

//----- (0049BA20) --------------------------------------------------------
char __userpurge OleDB::ExcuteQueryGetData@<al>(
        OleDB *this@<ecx>,
        int a2@<ebx>,
        IUnknown *Query_In,
        IRowset *Buffer_Out,
        void *a5)
{
  const char *v6; // ebp
  IDBCreateCommand *m_pIDBCreateCommand; // eax
  HRESULT v8; // eax
  HRESULT v10; // eax
  ICommandText *v11; // eax
  HRESULT v12; // eax
  HRESULT v13; // ebx
  ICommandText *v14; // eax
  HRESULT v15; // eax
  tagDBBINDING *Data; // ebx
  HRESULT v17; // eax
  HRESULT v18; // eax
  HRESULT v19; // eax
  IAccessor *pIAccessor; // [esp+ACh] [ebp-20h] BYREF
  unsigned int *pRows; // [esp+B0h] [ebp-1Ch] BYREF
  ICommandText *pICommandText; // [esp+B4h] [ebp-18h] BYREF
  unsigned int hAccessor; // [esp+B8h] [ebp-14h] BYREF
  unsigned int RowNums; // [esp+BCh] [ebp-10h] BYREF
  OleDB::_RESULT_COLS ResultCols; // [esp+C0h] [ebp-Ch] BYREF

  if ( !this->m_pIDBCreateSession || (v6 = (const char *)Query_In) == 0 )
  {
    OleDB::HandleError(this, 440, 0, (char *)&byte_4F6A58);
    return 0;
  }
  if ( !this->m_pIDBCreateCommand )
    OleDB::DBCreateCommand(this);
  m_pIDBCreateCommand = this->m_pIDBCreateCommand;
  pICommandText = 0;
  v8 = m_pIDBCreateCommand->CreateCommand(m_pIDBCreateCommand, 0, &IID_ICommandText, &pICommandText);
  if ( v8 < 0 )
  {
    OleDB::HandleError(this, 467, v8, "IDBCreateCommand::CreateCommand");
    if ( pICommandText )
      pICommandText->Release(pICommandText);
    return 0;
  }
  MultiByteToWideChar(0, 0, v6, -1, this->m_QueryText, 4096);
  v10 = pICommandText->SetCommandText(pICommandText, &DBGUID_DBSQL, this->m_QueryText);
  if ( v10 < 0 )
  {
    OleDB::HandleError(this, 476, v10, "ICommandText::SetCommandText");
    v11 = pICommandText;
    goto LABEL_11;
  }
  Query_In = 0;
  v12 = pICommandText->Execute(pICommandText, 0, &IID_IRowset, 0, 0, &Query_In);
  v13 = v12;
  if ( v12 < 0 )
  {
    OleDB::HandleError(this, 490, v12, "ICommandText::Execute");
    v14 = pICommandText;
    goto LABEL_48;
  }
  if ( pICommandText )
  {
    pICommandText->Release(pICommandText);
    pICommandText = 0;
  }
  if ( !Query_In )
  {
    OleDB::HandleError(this, 500, v13, (char *)&byte_4F6B5C);
    return 0;
  }
  memset(&ResultCols, 0, sizeof(ResultCols));
  if ( !OleDB::AllocResultCols(this, Query_In, &ResultCols) )
  {
    OleDB::HandleError(this, 511, 0, asc_4F6B40);
LABEL_47:
    v14 = (ICommandText *)Query_In;
LABEL_48:
    if ( v14 )
      v14->Release(v14);
    return 0;
  }
  pIAccessor = 0;
  v15 = Query_In->QueryInterface(Query_In, &IID_IAccessor, (void **)&pIAccessor);
  if ( v15 < 0 )
  {
    OleDB::HandleError(this, 521, v15, "IRowset::QueryInterface");
    if ( pIAccessor )
    {
      pIAccessor->Release(pIAccessor);
      pIAccessor = 0;
    }
    goto LABEL_47;
  }
  Data = OleDB::AllocBindGetData(this, ResultCols.ColNum, (tagDBBINDING *)ResultCols.lpDBColumnInfo);
  hAccessor = 0;
  v17 = pIAccessor->CreateAccessor(pIAccessor, 2u, ResultCols.ColNum, Data, 0, &hAccessor, 0);
  if ( v17 < 0 )
  {
    OleDB::HandleError(this, 535, v17, "IAccessor::CreateAccessor");
    if ( Data )
      free((tagEntry *)Data);
    if ( pIAccessor )
    {
      pIAccessor->Release(pIAccessor);
      pIAccessor = 0;
    }
    v11 = (ICommandText *)Query_In;
    goto LABEL_11;
  }
  if ( Data )
    free((tagEntry *)Data);
  pRows = (unsigned int *)operator new((tagHeader *)4);
  RowNums = 0;
  v18 = ((int (__stdcall *)(IUnknown *, _DWORD, _DWORD, int, unsigned int *, unsigned int **, int))Query_In->__vftable[1].Release)(
          Query_In,
          0,
          0,
          1,
          &RowNums,
          &pRows,
          a2);
  if ( v18 < 0 )
  {
    OleDB::HandleError(this, 555, v18, "IRowset::GetNextRows");
LABEL_42:
    (*(void (__stdcall **)(unsigned int *, unsigned int, _DWORD))(*pRows + 24))(pRows, RowNums, 0);
    if ( pRows )
    {
      (*(void (__stdcall **)(unsigned int *))(*pRows + 8))(pRows);
      pRows = 0;
    }
    ((void (__stdcall *)(IRowset *, unsigned int, ICommandText *, _DWORD, _DWORD))Buffer_Out->ReleaseRows)(
      Buffer_Out,
      ResultCols.ColNum,
      pICommandText,
      0,
      0);
    if ( pRows )
    {
      operator delete(pRows);
      pRows = 0;
    }
    OleDB::ReleaseResultCols(this, Query_In, &ResultCols);
    goto LABEL_47;
  }
  if ( !ResultCols.ColNum )
  {
    OleDB::HandleError(this, 570, 0, (char *)&byte_4F6AF8);
    (*(void (__stdcall **)(unsigned int *, unsigned int, _DWORD))(*pRows + 24))(pRows, RowNums, 0);
    if ( pRows )
    {
      (*(void (__stdcall **)(unsigned int *))(*pRows + 8))(pRows);
      pRows = 0;
    }
    ((void (__stdcall *)(IRowset *, unsigned int, ICommandText *, _DWORD, _DWORD))Buffer_Out->ReleaseRows)(
      Buffer_Out,
      ResultCols.ColNum,
      pICommandText,
      0,
      0);
    if ( pRows )
    {
      operator delete(pRows);
      pRows = 0;
    }
    OleDB::ReleaseResultCols(this, Query_In, &ResultCols);
    v11 = (ICommandText *)Query_In;
LABEL_11:
    if ( v11 )
    {
      v11->Release(v11);
      return 0;
    }
    return 0;
  }
  v19 = Buffer_Out->GetData(Buffer_Out, (unsigned int)pICommandText->__vftable, RowNums, a5);
  if ( v19 < 0 )
  {
    OleDB::HandleError(this, 586, v19, "IRowset::GetData");
    goto LABEL_42;
  }
  (*(void (__stdcall **)(unsigned int *, unsigned int, _DWORD))(*pRows + 24))(pRows, RowNums, 0);
  if ( pRows )
  {
    (*(void (__stdcall **)(unsigned int *))(*pRows + 8))(pRows);
    pRows = 0;
  }
  ((void (__stdcall *)(IRowset *, unsigned int, ICommandText *, _DWORD, _DWORD))Buffer_Out->ReleaseRows)(
    Buffer_Out,
    ResultCols.ColNum,
    pICommandText,
    0,
    0);
  if ( pRows )
  {
    operator delete(pRows);
    pRows = 0;
  }
  OleDB::ReleaseResultCols(this, Query_In, &ResultCols);
  if ( Query_In )
    Query_In->Release(Query_In);
  return 1;
}
// 49BA20: could not find valid save-restore pair for ebx

//----- (0049BE70) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCastleInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  unsigned __int16 m_Len; // di
  Castle::CCastleMgr *Instance; // eax
  Castle::CCastleMgr *v5; // eax
  unsigned __int8 lpPktBasea; // [esp+10h] [ebp+8h]

  lpPktBasea = lpPktBase[1].m_Cmd;
  m_Len = lpPktBase[1].m_Len;
  if ( lpPktBase[1].m_StartBit )
  {
    Instance = Castle::CCastleMgr::GetInstance();
    Castle::CCastleMgr::Destroy(Instance);
  }
  v5 = Castle::CCastleMgr::GetInstance();
  Castle::CCastleMgr::SerializeIn(v5, (char *)&lpPktBase[1].m_CodePage, m_Len, lpPktBasea);
  return 1;
}

//----- (0049BEB0) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCampInfo(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  CSiegeObjectMgr *v3; // eax
  unsigned int v5; // [esp-28h] [ebp-2Ch]
  unsigned int v6; // [esp-24h] [ebp-28h]
  unsigned int v7; // [esp-20h] [ebp-24h]
  int v8; // [esp-1Ch] [ebp-20h]
  unsigned __int8 v9; // [esp-18h] [ebp-1Ch]
  unsigned __int8 v10; // [esp-14h] [ebp-18h]
  __int128 v11; // [esp-10h] [ebp-14h]

  if ( lpPktBase[1].m_StartBit )
  {
    Instance = CSiegeObjectMgr::GetInstance();
    CSiegeObjectMgr::DestroyAllCamp(Instance);
  }
  *(PktBase *)&v11 = *(PktBase *)((char *)lpPktBase + 31);
  v10 = BYTE2(lpPktBase[2].m_CodePage);
  v9 = BYTE1(lpPktBase[2].m_CodePage);
  v8 = *(_DWORD *)&lpPktBase[2].m_Cmd;
  v7 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo + 1);
  v6 = *(unsigned int *)((char *)&lpPktBase[1].m_CodePage + 1);
  v5 = *(_DWORD *)&lpPktBase[1].m_Cmd;
  v3 = CSiegeObjectMgr::GetInstance();
  CSiegeObjectMgr::CreateCamp(v3, v5, v6, v7, v8, v9, v10, v11);
  return 1;
}
// 49BF03: variable 'v11' is possibly undefined

//----- (0049BF10) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCreateCamp(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *Camp; // eax
  CSiegeObject *v4; // esi
  unsigned int m_dwOwnerID; // ecx
  unsigned int m_dwGID; // edx
  int m_nNowHP; // eax
  CServerSetup *v8; // eax
  char ServerZone; // al
  char m_cState; // cl
  char m_cUpgradeStep; // dl
  float m_fPointY; // ecx
  float m_fPointZ; // edx
  CCreatureManager *v14; // eax
  PktBase v16; // [esp-28h] [ebp-5Ch]
  int v17; // [esp-1Ch] [ebp-50h]
  unsigned __int8 v18; // [esp-18h] [ebp-4Ch]
  unsigned __int8 v19; // [esp-14h] [ebp-48h]
  __int128 v20; // [esp-10h] [ebp-44h]
  char szBuffer[43]; // [esp+4h] [ebp-30h] BYREF

  HIDWORD(v20) = 1;
  *(PktBase *)&v20 = *(PktBase *)((char *)lpPktBase + 31);
  v19 = BYTE2(lpPktBase[2].m_CodePage);
  v18 = BYTE1(lpPktBase[2].m_CodePage);
  v17 = *(_DWORD *)&lpPktBase[2].m_StartBit;
  v16 = lpPktBase[1];
  Instance = CSiegeObjectMgr::GetInstance();
  Camp = CSiegeObjectMgr::CreateCamp(
           Instance,
           *(unsigned int *)&v16.m_StartBit,
           v16.m_CodePage,
           v16.m_SrvInfo.dwServerInfo,
           v17,
           v18,
           v19,
           v20);
  v4 = Camp;
  if ( Camp )
  {
    m_dwOwnerID = Camp->m_dwOwnerID;
    m_dwGID = Camp->m_dwGID;
    *(_DWORD *)&szBuffer[12] = Camp->m_dwCID;
    m_nNowHP = Camp->m_CreatureStatus.m_nNowHP;
    *(_DWORD *)&szBuffer[16] = m_dwOwnerID;
    *(_DWORD *)&szBuffer[20] = m_dwGID;
    *(_DWORD *)&szBuffer[24] = m_nNowHP;
    v8 = CServerSetup::GetInstance();
    ServerZone = CServerSetup::GetServerZone(v8);
    m_cState = v4->m_cState;
    m_cUpgradeStep = v4->m_cUpgradeStep;
    szBuffer[28] = ServerZone;
    *(float *)&szBuffer[31] = v4->m_CurrentPos.m_fPointX;
    szBuffer[29] = m_cState;
    m_fPointY = v4->m_CurrentPos.m_fPointY;
    szBuffer[30] = m_cUpgradeStep;
    m_fPointZ = v4->m_CurrentPos.m_fPointZ;
    *(float *)&szBuffer[35] = m_fPointY;
    *(float *)&szBuffer[39] = m_fPointZ;
    if ( PacketWrap::WrapCrypt(szBuffer, 0x2Bu, 0xA8u, 0, 0) == 1 )
    {
      v14 = CCreatureManager::GetInstance();
      CCreatureManager::SendAllCharacter(v14, szBuffer, 0x2Bu, 0xA8u, 1);
    }
  }
  return 1;
}

//----- (0049C010) --------------------------------------------------------
char __cdecl DBAgentPacketParse::ParseCreateSiegeArms(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *SiegeArms; // eax
  CSiegeObject *v4; // esi
  unsigned int m_dwOwnerID; // edx
  unsigned int m_dwGID; // eax
  unsigned __int16 m_wObjectType; // cx
  int m_nNowHP; // edx
  CServerSetup *v9; // eax
  char ServerZone; // al
  float m_fPointZ; // ecx
  unsigned int v13; // [esp-28h] [ebp-60h]
  unsigned int m_CodePage; // [esp-24h] [ebp-5Ch]
  unsigned int v15; // [esp-20h] [ebp-58h]
  unsigned int v16; // [esp-1Ch] [ebp-54h]
  unsigned __int16 wError; // [esp-18h] [ebp-50h]
  unsigned __int8 m_CodePage_high; // [esp-14h] [ebp-4Ch]
  unsigned __int8 dwServerInfo; // [esp-10h] [ebp-48h]
  __int128 v20; // [esp-Ch] [ebp-44h]
  char szBuffer[45]; // [esp+4h] [ebp-34h] BYREF

  *(PktBase *)&v20 = *(PktBase *)((char *)lpPktBase + 33);
  dwServerInfo = lpPktBase[2].m_SrvInfo.dwServerInfo;
  m_CodePage_high = HIBYTE(lpPktBase[2].m_CodePage);
  wError = lpPktBase[1].m_SrvInfo.SrvState.wError;
  v16 = *(_DWORD *)&lpPktBase[2].m_Len;
  v15 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
  m_CodePage = lpPktBase[1].m_CodePage;
  v13 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = CSiegeObjectMgr::GetInstance();
  SiegeArms = CSiegeObjectMgr::CreateSiegeArms(
                Instance,
                v13,
                m_CodePage,
                v15,
                v16,
                wError,
                m_CodePage_high,
                dwServerInfo,
                v20);
  v4 = SiegeArms;
  if ( SiegeArms )
  {
    m_dwOwnerID = SiegeArms->m_dwOwnerID;
    m_dwGID = SiegeArms->m_dwGID;
    *(_DWORD *)&szBuffer[12] = v4->m_dwCID;
    m_wObjectType = v4->m_wObjectType;
    *(_DWORD *)&szBuffer[16] = m_dwOwnerID;
    m_nNowHP = v4->m_CreatureStatus.m_nNowHP;
    *(_DWORD *)&szBuffer[22] = m_dwGID;
    LOBYTE(m_dwGID) = v4->m_cState;
    *(_WORD *)&szBuffer[20] = m_wObjectType;
    LOBYTE(m_wObjectType) = v4->m_cUpgradeStep;
    *(_DWORD *)&szBuffer[26] = m_nNowHP;
    szBuffer[31] = m_dwGID;
    szBuffer[32] = m_wObjectType;
    v9 = CServerSetup::GetInstance();
    ServerZone = CServerSetup::GetServerZone(v9);
    m_fPointZ = v4->m_CurrentPos.m_fPointZ;
    *(float *)&szBuffer[33] = v4->m_CurrentPos.m_fPointX;
    szBuffer[30] = ServerZone;
    *(float *)&szBuffer[37] = v4->m_CurrentPos.m_fPointY;
    *(float *)&szBuffer[41] = m_fPointZ;
    if ( PacketWrap::WrapCrypt(szBuffer, 0x2Du, 0xA9u, 0, 0) == 1 )
      CSiegeObject::SendToRadiusCell(v4, szBuffer, 0x2Du, 0xA9u);
  }
  return 1;
}
// 49C061: variable 'v20' is possibly undefined

//----- (0049C120) --------------------------------------------------------
bool __cdecl DBAgentPacketParse::ParseCastleCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CMsgProcessMgr *Instance; // eax
  CMsgProc *Castle; // eax
  Castle::CCastle *v4; // edi
  CSiegeObjectMgr *v5; // eax
  CSiegeObject *SiegeObject; // eax
  CSiegeObject *v7; // ecx
  unsigned __int8 wError_high; // al
  bool result; // al
  unsigned int m_CodePage; // [esp-4h] [ebp-Ch]
  unsigned int dwServerInfo; // [esp-4h] [ebp-Ch]

  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = Castle::CCastleMgr::GetCastle(Instance, m_CodePage);
  dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v4 = (Castle::CCastle *)Castle;
  v5 = CSiegeObjectMgr::GetInstance();
  SiegeObject = CSiegeObjectMgr::GetSiegeObject(v5, dwServerInfo);
  v7 = SiegeObject;
  if ( !v4 || !SiegeObject )
    return 1;
  wError_high = HIBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError);
  switch ( wError_high )
  {
    case 1u:
      Castle::CCastle::SetTax(v4, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 3u:
    case 5u:
      CSiegeObject::UpdateEmblem(v7, lpPktBase[2].m_SrvInfo.dwServerInfo, lpPktBase[2].m_CodePage, wError_high);
      result = 1;
      break;
    case 4u:
      CSiegeObject::UpgradeEmblem(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 6u:
      Castle::CCastle::ChangeCastleMaster(v4, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 7u:
      Castle::CCastle::GetTaxIncome(v4, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 8u:
      Castle::CCastle::DecreaseDayValue(v4);
      result = 1;
      break;
    case 9u:
      CSiegeObject::Open(v7);
      result = 1;
      break;
    case 0xAu:
      CSiegeObject::Close(v7);
      result = 1;
      break;
    case 0xDu:
    case 0xFu:
    case 0x11u:
      CSiegeObject::UpdateGate(v7, lpPktBase[2].m_SrvInfo.dwServerInfo, lpPktBase[2].m_CodePage, wError_high);
      result = 1;
      break;
    case 0xEu:
      CSiegeObject::UpgradeGate(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 0x10u:
      CSiegeObject::RepairGate(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 0x12u:
      CSiegeObject::RestoreGate(v7);
      result = 1;
      break;
    case 0x13u:
      CSiegeObject::DestroyGate(v7);
      result = 1;
      break;
    case 0x15u:
    case 0x19u:
    case 0x1Bu:
      CSiegeObject::UpdateCastleArms(v7, lpPktBase[2].m_SrvInfo.dwServerInfo, lpPktBase[2].m_CodePage, wError_high);
      result = 1;
      break;
    case 0x16u:
      CSiegeObject::ChangeCastleArms(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 0x1Au:
      CSiegeObject::UpgradeCastleArms(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 0x1Cu:
      CSiegeObject::RepairCastleArms(v7, lpPktBase[2].m_CodePage);
      result = 1;
      break;
    case 0x1Du:
      CSiegeObject::DestroyCastleArms(v7);
      return 1;
    default:
      return 1;
  }
  return result;
}

//----- (0049C300) --------------------------------------------------------
bool __cdecl DBAgentPacketParse::ParseCampCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase)
{
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *Camp; // edi
  unsigned __int8 m_Len; // al
  bool result; // al
  CSiegeObjectMgr *v6; // eax
  unsigned int m_CodePage; // [esp-4h] [ebp-Ch]
  unsigned int m_dwOwnerID; // [esp-4h] [ebp-Ch]

  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CSiegeObjectMgr::GetInstance();
  Camp = CSiegeObjectMgr::GetCamp(Instance, m_CodePage);
  if ( !Camp )
    return 1;
  m_Len = lpPktBase[2].m_Len;
  switch ( m_Len )
  {
    case 2u:
      CSiegeObject::BuildCamp(Camp);
      result = 1;
      break;
    case 3u:
    case 5u:
    case 7u:
    case 9u:
      CSiegeObject::UpdateCampInfo(Camp, lpPktBase[2].m_StartBit, lpPktBase[2].m_Cmd, m_Len);
      result = 1;
      break;
    case 4u:
    case 0xAu:
      CSiegeObject::ChangeToStartKit(Camp);
      CSiegeObject::DestroyCamp(Camp);
      m_dwOwnerID = Camp->m_dwOwnerID;
      v6 = CSiegeObjectMgr::GetInstance();
      CSiegeObjectMgr::DeleteCamp(v6, m_dwOwnerID);
      result = 1;
      break;
    case 6u:
      CSiegeObject::UpgradeCamp(Camp, lpPktBase[2].m_Cmd);
      result = 1;
      break;
    case 8u:
      CSiegeObject::RepairCamp(Camp, lpPktBase[1].m_SrvInfo.SrvState.wError);
      return 1;
    default:
      return 1;
  }
  return result;
}

