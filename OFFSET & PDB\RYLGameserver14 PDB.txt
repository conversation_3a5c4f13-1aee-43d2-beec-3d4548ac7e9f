//----- (00484550) --------------------------------------------------------
char __cdecl GAMELOG::LogDamagePumping(
        CCharacter *AttackCharacter_In,
        CMonster *DefendMonster_In,
        __int16 nDamage_In,
        bool bWriteForce_In)
{
  int m_nLevel; // eax
  int v6; // esi
  int v7; // eax
  unsigned int m_dwCID; // edi
  int v9; // esi
  unsigned __int16 v10; // ax
  int v11; // esi
  int v12; // eax
  int v13; // esi
  int v14; // esi
  int v15; // esi
  int v16; // esi
  int v17; // ebp
  Item::CItem *Item; // eax
  Item::CItem *v19; // edi
  int v20; // esi
  int v21; // edi
  unsigned __int8 *v22; // ebp
  __int16 v23; // ax
  int v24; // eax
  int v25; // esi
  int v26; // eax
  int v27; // [esp+20h] [ebp-20058h]
  int v28; // [esp+24h] [ebp-20054h]
  int SrcPos; // [esp+28h] [ebp-20050h]
  unsigned __int8 dst[72]; // [esp+2Ch] [ebp-2004Ch] BYREF
  char string[131072]; // [esp+74h] [ebp-20004h] BYREF

  if ( !bWriteForce_In )
  {
    m_nLevel = DefendMonster_In->m_CreatureStatus.m_nLevel;
    if ( m_nLevel < AttackCharacter_In->m_CreatureStatus.m_nLevel || nDamage_In < word_4F0ED6[m_nLevel] )
      return 1;
  }
  v6 = _snprintf(string, 0x20000u, (char *)&byte_4F0E78);
  v7 = _snprintf(
         &string[v6],
         0x20000 - v6,
         "============================================================================================");
  m_dwCID = AttackCharacter_In->m_dwCID;
  v9 = v7 + v6;
  v10 = AttackCharacter_In->GetClass(AttackCharacter_In);
  v11 = _snprintf(
          &string[v9],
          0x20000 - v9,
          aCid0x08x_74,
          m_dwCID,
          AttackCharacter_In->m_DBData.m_Info.Name,
          AttackCharacter_In->m_CreatureStatus.m_nLevel,
          v10)
      + v9;
  v12 = _snprintf(
          &string[v11],
          0x20000 - v11,
          "STR : %d, DEX : %d, CON : %d, INT : %d, WIS : %d                                            ",
          AttackCharacter_In->m_CharacterStatus.m_nSTR,
          AttackCharacter_In->m_CharacterStatus.m_nDEX,
          AttackCharacter_In->m_CharacterStatus.m_nCON,
          AttackCharacter_In->m_CharacterStatus.m_nINT,
          AttackCharacter_In->m_CharacterStatus.m_nWIS);
  v13 = _snprintf(
          &string[v12 + v11],
          0x20000 - (v12 + v11),
          "MinDamage : %d, MaxDamage : %d, DRC : %.1f, OffenceRevision : %d, CriticalPercentage : %d   ",
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nMinDamage,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nMaxDamage,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_fDRC,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nOffenceRevision,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nCriticalPercentage)
      + v12
      + v11;
  v14 = _snprintf(
          &string[v13],
          0x20000 - v13,
          "FireAttack : %d, LightningAttack : %d, ColdAttack : %d, DrainAttack : %d, PoisonAttack : %d ",
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[0],
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[1],
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[2],
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[3],
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nWeaponAttributeLevel[4])
      + v13;
  v15 = _snprintf(
          &string[v14],
          0x20000 - v14,
          "Defence : %d, DefenceRevision : %d, MagicResistance : %d, BlockingPercentage : %d           ",
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nDefence,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nDefenceRevision,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nMagicResistance,
          AttackCharacter_In->m_CreatureStatus.m_StatusInfo.m_nBlockingPercentage)
      + v14;
  v16 = _snprintf(
          &string[v15],
          0x20000 - v15,
          "--------------------------------------------------------------------------------------------")
      + v15;
  v17 = 0;
  v28 = 0;
  do
  {
    LOWORD(SrcPos) = (16 * v17) | 1;
    Item = CCharacter::GetItem(AttackCharacter_In, SrcPos);
    if ( Item )
    {
      v19 = (Item->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? Item : 0;
      if ( v19 )
      {
        v20 = _snprintf(
                &string[v16],
                0x20000 - v16,
                "- Equip #%d -                                                                               ",
                v17)
            + v16;
        v16 = _snprintf(
                &string[v20],
                0x20000 - v20,
                "ProtoTypeID : %d, Durability : %d / %d                                                      ",
                v19->m_ItemData.m_usProtoTypeID,
                v19->m_ItemData.m_cNumOrDurability,
                v19->m_cMaxNumOrDurability)
            + v20;
        memmove(dst, (unsigned __int8 *)&v19[1].m_ItemData, 0x46u);
        v21 = -2;
        v22 = dst;
        v27 = 35;
        do
        {
          v23 = *(_WORD *)v22;
          if ( *(_WORD *)v22 )
          {
            switch ( v21 )
            {
              case 0:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "CRITICAL_PERCENTAGE", v23);
                goto LABEL_25;
              case 1:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "MIN_DAMAGE", v23);
                goto LABEL_25;
              case 2:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "MAX_DAMAGE", v23);
                goto LABEL_25;
              case 3:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "DRC", v23);
                goto LABEL_25;
              case 4:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "OFFENCE_REVISION", v23);
                goto LABEL_25;
              case 5:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "DEFENCE", v23);
                goto LABEL_25;
              case 6:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "DEFENCE_REVISION", v23);
                goto LABEL_25;
              case 7:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "MAGIC_RESISTANCE", v23);
                goto LABEL_25;
              case 8:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "BLOCKING_PERCENTAGE", v23);
                goto LABEL_25;
              case 17:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "FIRE_ATTACK", v23);
                goto LABEL_25;
              case 18:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "LIGHTNING_ATTACK", v23);
                goto LABEL_25;
              case 19:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "COLD_ATTACK", v23);
                goto LABEL_25;
              case 20:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "DRAIN_ATTACK", v23);
                goto LABEL_25;
              case 21:
                v24 = _snprintf(&string[v16], 0x20000 - v16, "%15s %2d", "POISON_ATTACK", v23);
LABEL_25:
                v16 += v24;
                break;
              default:
                break;
            }
          }
          ++v21;
          v22 += 2;
          --v27;
        }
        while ( v27 );
        v17 = v28;
      }
    }
    v28 = ++v17;
  }
  while ( v17 < 16 );
  v25 = _snprintf(
          &string[v16],
          0x20000 - v16,
          "--------------------------------------------------------------------------------------------")
      + v16;
  v26 = _snprintf(
          &string[v25],
          0x20000 - v25,
          "Damage : %d, MonsterCID : 0x%08x, MonsterLevel : %d                                         ",
          nDamage_In,
          DefendMonster_In->m_dwCID,
          DefendMonster_In->m_CreatureStatus.m_nLevel);
  _snprintf(
    &string[v26 + v25],
    0x20000 - (v26 + v25),
    "============================================================================================");
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "GAMELOG::LogDamagePumping", aDWorkRylSource_34, 150, string);
  return 0;
}
// 484764: variable 'SrcPos' is possibly undefined
// 4F0ED6: using guessed type __int16 word_4F0ED6[];

//----- (00484A10) --------------------------------------------------------
void __thiscall CXORCrypt::XORF(
        CXORCrypt *this,
        char *Start_In,
        int Length_In,
        unsigned __int16 PageVer,
        unsigned __int8 PageNum1,
        unsigned __int8 PageNum2)
{
  unsigned int v6; // esi
  int v7; // eax
  char v9; // bl

  v6 = 0;
  if ( Length_In > 0 )
  {
    v7 = PageVer;
    do
    {
      v9 = CXORCrypt::BitFields[v7][PageNum1][v6 % 0x28] ^ *Start_In;
      *Start_In = v9;
      *Start_In++ = v9 ^ CXORCrypt::BitFields[v7][PageNum2][v6 % 0x28];
      ++v6;
    }
    while ( (int)v6 < Length_In );
  }
}

//----- (00484A80) --------------------------------------------------------
void __thiscall CXORCrypt::XORB(
        CXORCrypt *this,
        char *Start_In,
        int Length_In,
        unsigned __int16 PageVer,
        unsigned __int8 PageNum1,
        unsigned __int8 PageNum2)
{
  unsigned int v6; // esi
  int v7; // eax
  char v9; // bl

  v6 = 0;
  if ( Length_In > 0 )
  {
    v7 = PageVer;
    do
    {
      v9 = CXORCrypt::BitFields[v7][PageNum2][v6 % 0x28] ^ *Start_In;
      *Start_In = v9;
      *Start_In++ = v9 ^ CXORCrypt::BitFields[v7][PageNum1][v6 % 0x28];
      ++v6;
    }
    while ( (int)v6 < Length_In );
  }
}

//----- (00484AF0) --------------------------------------------------------
void __thiscall CXORCrypt::InitCodePage(CXORCrypt *this)
{
  unsigned int v2; // eax
  int i; // esi
  unsigned __int8 v4; // dl

  v2 = time(0);
  srand(v2);
  for ( i = 0; i < 100; ++i )
  {
    v4 = (int)rand() % 10;
    this->m_CodePage[i] = v4;
    if ( i > 1 && v4 == this->m_CodePage[i - 1] )
      --i;
  }
  this->m_CodePageCount = (int)rand() % 100;
}

//----- (00484B40) --------------------------------------------------------
int __thiscall CXORCrypt::GetCodePage(CXORCrypt *this)
{
  unsigned int v1; // edx

  if ( this->m_CodePageCount > 0xFFFFFF00 )
    this->m_CodePageCount = 0;
  v1 = (this->m_CodePageCount + 2) % 0x64;
  this->m_CodePageCount = v1;
  return this->m_CodePage[v1 - 1] | ((this->m_CodePage[v1] | 0x100) << 8);
}

//----- (00484B80) --------------------------------------------------------
char __thiscall CXORCrypt::EncodePacket(CXORCrypt *this, char *Start_In, int Length_In, unsigned int CodePage_In)
{
  if ( HIWORD(CodePage_In) > 1u || BYTE1(CodePage_In) >= 0xAu || (unsigned __int8)CodePage_In >= 0xAu )
    return 0;
  CXORCrypt::XORF(this, Start_In, Length_In, HIWORD(CodePage_In), BYTE1(CodePage_In), CodePage_In);
  return 1;
}

//----- (00484BD0) --------------------------------------------------------
char __thiscall CXORCrypt::DecodePacket(CXORCrypt *this, char *Start_In, int Length_In, unsigned int CodePage_In)
{
  if ( HIWORD(CodePage_In) > 1u || BYTE1(CodePage_In) >= 0xAu || (unsigned __int8)CodePage_In >= 0xAu )
    return 0;
  CXORCrypt::XORB(this, Start_In, Length_In, HIWORD(CodePage_In), BYTE1(CodePage_In), CodePage_In);
  return 1;
}

//----- (00484C20) --------------------------------------------------------
void __thiscall CXORCrypt::EncodeHeader(
        CXORCrypt *this,
        char *Start_In,
        int Length_In,
        unsigned __int16 PageVer_In,
        unsigned __int8 PageNum_In)
{
  unsigned int i; // esi
  int v7; // edi

  for ( i = 0; (int)i < Length_In; ++i )
  {
    v7 = 40 * (PageNum_In + 10 * PageVer_In);
    *Start_In = (2 * (*Start_In ^ CXORCrypt::BitFields[i % 0x28 + v7])) | ((char)(*Start_In ^ CXORCrypt::BitFields[i % 0x28 + v7]) >> 7) & 1;
    ++Start_In;
  }
}
// 4F7750: using guessed type _BYTE CXORCrypt::BitFields[800];

//----- (00484C80) --------------------------------------------------------
void __thiscall CXORCrypt::DecodeHeader(
        CXORCrypt *this,
        char *Start_In,
        int Length_In,
        unsigned __int16 PageVer_In,
        unsigned __int8 PageNum_In)
{
  int i; // edi
  char v7; // cl

  for ( i = 0; i < Length_In; ++i )
  {
    v7 = (*Start_In << 7) | (*Start_In >> 1) & 0x7F;
    *Start_In = v7;
    *Start_In++ = v7 ^ CXORCrypt::BitFields[400 * PageVer_In + 40 * PageNum_In + i % 0x28u];
  }
}
// 4F7750: using guessed type _BYTE CXORCrypt::BitFields[800];

//----- (00484CE0) --------------------------------------------------------
void __thiscall CXORCrypt::CXORCrypt(CXORCrypt *this)
{
  CSingleton<CXORCrypt>::ms_pSingleton = this;
  CXORCrypt::InitCodePage(this);
}

//----- (00484D00) --------------------------------------------------------
void __thiscall CXORCrypt::~CXORCrypt(CXORCrypt *this)
{
  CSingleton<CXORCrypt>::ms_pSingleton = 0;
}

//----- (00484D10) --------------------------------------------------------
BOOL __cdecl CMiniLZO::Compress(const char *in, unsigned int in_len, char *out, unsigned int *lp_out_len)
{
  _DWORD wrkmem[65536]; // [esp+0h] [ebp-40000h] BYREF

  return lzo1x_1_compress((const unsigned __int8 *)in, in_len, (unsigned __int8 *)out, lp_out_len, wrkmem) == 0;
}

//----- (00484D50) --------------------------------------------------------
BOOL __cdecl CMiniLZO::Decompress(
        const char *in,
        unsigned __int8 *in_len,
        char *out,
        unsigned int *buffersize_in_out_len)
{
  return lzo1x_decompress_safe((const unsigned __int8 *)in, in_len, (unsigned __int8 *)out, buffersize_in_out_len) == 0;
}

//----- (00484D80) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCastleCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebp
  unsigned int v4; // edi
  unsigned __int8 wError_high; // bl
  unsigned __int16 v6; // si
  CMsgProcessMgr *Instance; // eax
  Castle::CCastle *Castle; // edi
  int v9; // eax
  int v10; // eax
  CPartyMgr *v11; // eax
  unsigned int m_dwCID; // esi
  unsigned int v13; // eax
  CSingleDispatch *DispatchTable; // eax
  char v15; // al
  unsigned int v16; // ebx
  unsigned int v17; // eax
  CSiegeObject *CastleEmblem; // eax
  CSiegeObject *v19; // ebx
  int v20; // edi
  Item::CItem *Item; // eax
  unsigned __int16 m_usProtoTypeID; // cx
  int v23; // eax
  unsigned __int8 v24; // cl
  unsigned int v25; // ebx
  int i; // edi
  Item::CItem *v27; // esi
  CSingleDispatch *v28; // eax
  CMsgProc *v29; // ebx
  unsigned int v30; // eax
  CSingleDispatch *v31; // eax
  CSiegeObject *v32; // ebx
  unsigned int v33; // eax
  CSendStream *m_lpGameClientDispatch; // ebp
  CSiegeObject *v35; // esi
  unsigned int v36; // ebx
  unsigned int v37; // eax
  CSendStream *v38; // ebp
  CMsgProc *v39; // eax
  CSiegeObject *v40; // ebx
  unsigned int v41; // eax
  unsigned int m_dwGold; // edi
  CSingleDispatch *v43; // eax
  CPacketDispatch *m_lpDispatch; // esi
  CMsgProc *v45; // eax
  CSiegeObject *v46; // ebx
  unsigned int v47; // eax
  unsigned int v48; // edi
  CSingleDispatch *v49; // eax
  CPacketDispatch *v50; // edi
  unsigned int m_nNowHP; // esi
  CMsgProc *v52; // eax
  unsigned int v53; // eax
  unsigned int v54; // edi
  CSingleDispatch *v55; // eax
  unsigned int v56; // ebx
  unsigned int v57; // eax
  CMsgProc *v58; // eax
  unsigned int v59; // eax
  CSingleDispatch *v60; // eax
  CMsgProc *v61; // eax
  CSiegeObject *v62; // ebx
  __int16 v63; // ax
  unsigned int v64; // eax
  CSendStream *v65; // ebp
  CMsgProc *v66; // eax
  CSiegeObject *v67; // ebx
  __int16 v68; // ax
  unsigned int v69; // eax
  CSendStream *v70; // ebp
  CSiegeObject *v71; // ebx
  CCharacter_vtbl *v72; // edx
  unsigned int v73; // eax
  unsigned int v74; // edi
  CSingleDispatch *v75; // eax
  CPacketDispatch *v76; // esi
  CSiegeObject *v77; // ebx
  CCharacter_vtbl *v78; // eax
  unsigned int v79; // eax
  unsigned int v80; // edi
  CSingleDispatch *v81; // eax
  CPacketDispatch *v82; // edi
  unsigned int v83; // esi
  char v84; // bl
  CSiegeObject *v85; // ebx
  unsigned int v86; // eax
  CSingleDispatch *v87; // eax
  char v88; // bl
  CSendStream *v89; // ebp
  unsigned int v90; // [esp-Ch] [ebp-70h]
  unsigned int RepairHP; // [esp-Ch] [ebp-70h]
  unsigned int v92; // [esp-Ch] [ebp-70h]
  int v93; // [esp-8h] [ebp-6Ch]
  int v94; // [esp-8h] [ebp-6Ch]
  unsigned __int8 v95; // [esp-8h] [ebp-6Ch]
  unsigned int v96; // [esp-4h] [ebp-68h]
  int v97; // [esp-4h] [ebp-68h]
  ServerInfo dwCastleObjectID; // [esp+10h] [ebp-54h]
  unsigned int dwCastleID; // [esp+14h] [ebp-50h]
  CSiegeObject *lpEmblemb; // [esp+18h] [ebp-4Ch]
  Guild::CGuild *lpEmblem; // [esp+18h] [ebp-4Ch]
  CSiegeObject *lpEmblema; // [esp+18h] [ebp-4Ch]
  unsigned int dwCID; // [esp+1Ch] [ebp-48h]
  unsigned int dwCIDa; // [esp+1Ch] [ebp-48h]
  unsigned int dwCIDb; // [esp+1Ch] [ebp-48h]
  unsigned int dwCIDc; // [esp+1Ch] [ebp-48h]
  unsigned int dwCIDd; // [esp+1Ch] [ebp-48h]
  unsigned __int8 cSubCmd; // [esp+20h] [ebp-44h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+24h] [ebp-40h] BYREF
  CSingleDispatch::Storage v110; // [esp+2Ch] [ebp-38h] BYREF
  Item::CItem *lpJewel[9]; // [esp+34h] [ebp-30h]
  int v112; // [esp+60h] [ebp-4h]
  unsigned int lpPktBasea; // [esp+6Ch] [ebp+8h]
  unsigned __int8 lpPktBaseb; // [esp+6Ch] [ebp+8h]
  PktBase *lpPktBasec; // [esp+6Ch] [ebp+8h]
  PktBase *lpPktBased; // [esp+6Ch] [ebp+8h]
  PktBase *lpPktBasee; // [esp+6Ch] [ebp+8h]
  PktBase *lpPktBasef; // [esp+6Ch] [ebp+8h]
  PktBase *lpPktBaseg; // [esp+6Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x22 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  wError_high = HIBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError);
  dwCastleID = lpPktBase[1].m_CodePage;
  dwCastleObjectID = lpPktBase[1].m_SrvInfo;
  lpPktBasea = lpPktBase[2].m_CodePage;
  dwCID = v4;
  cSubCmd = wError_high;
  v6 = 0;
  if ( !m_lpCharacter->GetGID(m_lpCharacter) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCastleCmd",
      aDWorkRylSource_25,
      51,
      (char *)&byte_4F1268,
      v4,
      wError_high);
    v6 = 1;
  }
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = (Castle::CCastle *)Castle::CCastleMgr::GetCastle(Instance, dwCastleID);
  if ( Castle )
  {
    lpEmblemb = (CSiegeObject *)Castle->m_dwGID;
    if ( (CSiegeObject *)m_lpCharacter->GetGID(m_lpCharacter) != lpEmblemb )
    {
      v10 = ((int (__thiscall *)(CCharacter *, unsigned int, unsigned int, _DWORD))m_lpCharacter->GetGID)(
              m_lpCharacter,
              dwCastleID,
              Castle->m_dwGID,
              wError_high);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCastleCmd",
        aDWorkRylSource_25,
        67,
        (char *)&byte_4F10B0,
        dwCID,
        v10);
      v6 = 1;
    }
    v96 = m_lpCharacter->GetGID(m_lpCharacter);
    v11 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    lpEmblem = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v11, v96);
    if ( lpEmblem )
    {
      if ( v6 )
        goto LABEL_175;
      switch ( wError_high )
      {
        case 1u:
          m_dwCID = m_lpCharacter->m_dwCID;
          v13 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 0, m_dwCID, v13) )
            goto LABEL_174;
          DispatchTable = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
          v112 = 0;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 99;
            goto LABEL_161;
          }
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  0,
                  lpPktBasea,
                  1u,
                  0);
          goto LABEL_164;
        case 3u:
          v16 = m_lpCharacter->m_dwCID;
          v17 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 0, v16, v17) )
            goto LABEL_174;
          CastleEmblem = Castle::CCastle::GetCastleEmblem(Castle);
          v19 = CastleEmblem;
          lpEmblema = CastleEmblem;
          if ( !CastleEmblem || CastleEmblem->m_cUpgradeStep == 3 )
            goto LABEL_174;
          v20 = 0;
          break;
        case 9u:
        case 0xAu:
          v29 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v29 )
            goto LABEL_174;
          lpPktBasec = (PktBase *)m_lpCharacter->m_dwCID;
          v30 = m_lpCharacter->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 2u, (unsigned int)lpPktBasec, v30);
          if ( BYTE2(v29[207].__vftable) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v31 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v31);
          v112 = 2;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 290;
            goto LABEL_161;
          }
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  LOWORD(v29[37].__vftable),
                  0,
                  cSubCmd,
                  0);
          goto LABEL_164;
        case 0xBu:
          v32 = (CSiegeObject *)Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v32 )
            goto LABEL_174;
          lpPktBased = (PktBase *)m_lpCharacter->m_dwCID;
          v33 = m_lpCharacter->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 2u, (unsigned int)lpPktBased, v33);
          if ( v32->m_cState )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          CSiegeObject::AddProtectGate(v32, m_lpCharacter->m_dwCID);
          m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
          if ( !m_lpGameClientDispatch )
            return 1;
          return GameClientSendPacket::SendCharCastleCmd(
                   m_lpGameClientDispatch + 8,
                   dwCastleID,
                   dwCastleObjectID.dwServerInfo,
                   0xBu,
                   0);
        case 0xCu:
          v35 = (CSiegeObject *)Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v35 )
            goto LABEL_174;
          v36 = m_lpCharacter->m_dwCID;
          v37 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 2u, v36, v37) )
            goto LABEL_174;
          CSiegeObject::DeleteProtectGate(v35, m_lpCharacter->m_dwCID);
          v38 = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
          if ( !v38 )
            return 1;
          return GameClientSendPacket::SendCharCastleCmd(v38 + 8, dwCastleID, dwCastleObjectID.dwServerInfo, 0xCu, 0);
        case 0xDu:
          v39 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v40 = (CSiegeObject *)v39;
          if ( !v39 )
            goto LABEL_174;
          v6 = BYTE2(v39[207].__vftable) != 0;
          dwCIDa = m_lpCharacter->m_dwCID;
          v41 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 1u, dwCIDa, v41) )
            v6 = 1;
          if ( lpPktBasea != CSiegeObject::GetUpgradeGold(v40) )
            v6 = 1;
          m_dwGold = lpEmblem->m_dwGold;
          if ( m_dwGold < CSiegeObject::GetUpgradeGold(v40) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v43 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v43);
          m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 3;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 406;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, lpPktBasea);
          v95 = 13;
          goto LABEL_70;
        case 0xFu:
          v45 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v46 = (CSiegeObject *)v45;
          if ( !v45 )
            goto LABEL_174;
          if ( BYTE2(v45[207].__vftable) || LOWORD(v45[37].__vftable) == HIWORD(v45[46].__vftable) )
            v6 = 1;
          dwCIDb = m_lpCharacter->m_dwCID;
          v47 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 1u, dwCIDb, v47) )
            v6 = 1;
          if ( lpPktBasea != CSiegeObject::GetRepairGold(v46) )
            v6 = 1;
          v48 = lpEmblem->m_dwGold;
          if ( v48 < CSiegeObject::GetRepairGold(v46) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v49 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v49);
          v50 = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 4;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 456;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, lpPktBasea);
          m_nNowHP = v46->m_CreatureStatus.m_nNowHP;
          RepairHP = CSiegeObject::GetRepairHP(v46);
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&v50[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  m_nNowHP,
                  RepairHP,
                  0xFu,
                  0);
          goto LABEL_164;
        case 0x11u:
          v52 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v40 = (CSiegeObject *)v52;
          if ( !v52 )
            goto LABEL_174;
          v6 = BYTE2(v52[207].__vftable) != 7;
          dwCIDc = m_lpCharacter->m_dwCID;
          v53 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 1u, dwCIDc, v53) )
            v6 = 1;
          if ( lpPktBasea != CSiegeObject::GetRestoreGold(v40) )
            v6 = 1;
          v54 = lpEmblem->m_dwGold;
          if ( v54 < CSiegeObject::GetRestoreGold(v40) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v55 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v55);
          m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 5;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 506;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, lpPktBasea);
          v95 = 17;
LABEL_70:
          v90 = 0;
          goto LABEL_71;
        case 0x14u:
          if ( !Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo) )
            goto LABEL_174;
          v56 = m_lpCharacter->m_dwCID;
          v57 = m_lpCharacter->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 5u, v56, v57);
          if ( lpPktBasea > 1 )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          CCharacter::MovePos(m_lpCharacter, Castle->m_BackDoorPos[lpPktBasea], 0, 0);
          return 1;
        case 0x15u:
          v58 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v40 = (CSiegeObject *)v58;
          if ( !v58 )
            goto LABEL_174;
          v6 = BYTE2(v58[207].__vftable) != 7;
          dwCIDd = m_lpCharacter->m_dwCID;
          v59 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 3u, dwCIDd, v59) )
            v6 = 1;
          if ( lpPktBasea != 5434 && lpPktBasea != 5482 && lpPktBasea != 5386 )
            v6 = 1;
          if ( lpEmblem->m_dwGold < 0xF4240 )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v60 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v60);
          m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 6;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 591;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, 0xF4240u);
          v95 = 21;
          v90 = lpPktBasea;
LABEL_71:
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&m_lpDispatch[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  v40->m_CreatureStatus.m_nNowHP,
                  v90,
                  v95,
                  0);
          goto LABEL_164;
        case 0x17u:
          v61 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v62 = (CSiegeObject *)v61;
          if ( !v61 )
            goto LABEL_174;
          v63 = (__int16)v61[207].__vftable;
          if ( v63 != 5434 )
            v6 = v63 != 5482;
          lpPktBasee = (PktBase *)m_lpCharacter->m_dwCID;
          v64 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 4u, (unsigned int)lpPktBasee, v64) )
            v6 = 1;
          if ( v62->m_cState )
            v6 = 1;
          if ( CSiegeObject::IsRide(v62, dwCID) == 1 )
            v6 = 1;
          if ( m_lpCharacter->m_dwRideArmsCID )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          if ( !CSiegeObject::Ride(v62, dwCID) )
            goto LABEL_174;
          v65 = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
          if ( !v65 )
            return 1;
          return GameClientSendPacket::SendCharCastleCmd(v65 + 8, dwCastleID, dwCastleObjectID.dwServerInfo, 0x17u, 0);
        case 0x18u:
          v66 = Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          v67 = (CSiegeObject *)v66;
          if ( !v66 )
            goto LABEL_174;
          v68 = (__int16)v66[207].__vftable;
          if ( v68 != 5434 )
            v6 = v68 != 5482;
          lpPktBasef = (PktBase *)m_lpCharacter->m_dwCID;
          v69 = m_lpCharacter->GetGID(m_lpCharacter);
          if ( !Castle::CCastle::CheckRight(Castle, 4u, (unsigned int)lpPktBasef, v69) )
            v6 = 1;
          if ( v67->m_cState )
            v6 = 1;
          if ( !CSiegeObject::IsRide(v67, dwCID) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          if ( !CSiegeObject::GetOff(v67, dwCID) )
            goto LABEL_174;
          v70 = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
          if ( !v70 )
            return 1;
          return GameClientSendPacket::SendCharCastleCmd(v70 + 8, dwCastleID, dwCastleObjectID.dwServerInfo, 0x18u, 0);
        case 0x19u:
          v71 = (CSiegeObject *)Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v71 )
            goto LABEL_174;
          v72 = m_lpCharacter->__vftable;
          StoragelpDBAgentDispatch.m_lpDispatch = (CPacketDispatch *)m_lpCharacter->m_dwCID;
          v73 = v72->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 3u, (unsigned int)StoragelpDBAgentDispatch.m_lpDispatch, v73);
          if ( v71->m_cState )
            v6 = 1;
          if ( CSiegeObject::IsRide(v71, dwCID) == 1 )
            v6 = 1;
          if ( lpPktBasea != CSiegeObject::GetUpgradeGold(v71) )
            v6 = 1;
          v74 = lpEmblem->m_dwGold;
          if ( v74 < CSiegeObject::GetUpgradeGold(v71) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v75 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v75);
          v76 = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 7;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 756;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, lpPktBasea);
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&v76[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  v71->m_CreatureStatus.m_nNowHP,
                  0,
                  0x19u,
                  0);
          goto LABEL_164;
        case 0x1Bu:
          v77 = (CSiegeObject *)Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v77 )
            goto LABEL_174;
          v78 = m_lpCharacter->__vftable;
          StoragelpDBAgentDispatch.m_lpDispatch = (CPacketDispatch *)m_lpCharacter->m_dwCID;
          v79 = v78->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 3u, (unsigned int)StoragelpDBAgentDispatch.m_lpDispatch, v79);
          if ( v77->m_cState )
            v6 = 1;
          if ( CSiegeObject::IsRide(v77, dwCID) == 1 )
            v6 = 1;
          if ( lpPktBasea != CSiegeObject::GetRepairGold(v77) )
            v6 = 1;
          v80 = lpEmblem->m_dwGold;
          if ( v80 < CSiegeObject::GetRepairGold(v77) )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v81 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v81);
          v82 = StoragelpDBAgentDispatch.m_lpDispatch;
          v112 = 8;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 811;
            goto LABEL_161;
          }
          Guild::CGuild::DeductGold(lpEmblem, lpPktBasea);
          v83 = v77->m_CreatureStatus.m_nNowHP;
          v92 = CSiegeObject::GetRepairHP(v77);
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&v82[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  v83,
                  v92,
                  0x1Bu,
                  0);
          goto LABEL_164;
        case 0x1Du:
          v85 = (CSiegeObject *)Castle::CCastleMgr::GetCastle((CMsgProcessMgr *)Castle, dwCastleObjectID.dwServerInfo);
          if ( !v85 )
            goto LABEL_174;
          lpPktBaseg = (PktBase *)m_lpCharacter->m_dwCID;
          v86 = m_lpCharacter->GetGID(m_lpCharacter);
          v6 = !Castle::CCastle::CheckRight(Castle, 3u, (unsigned int)lpPktBaseg, v86);
          if ( v85->m_cState == 7 )
            v6 = 1;
          if ( CSiegeObject::IsRide(v85, dwCID) == 1 )
            goto LABEL_174;
          if ( v6 )
            goto LABEL_175;
          v87 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&v110, v87);
          v112 = 9;
          if ( !v110.m_lpDispatch )
          {
            v6 = 1;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ParseCharCastleCmd",
              aDWorkRylSource_25,
              857,
              (char *)&byte_4EE5AC);
            v112 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v110);
            goto LABEL_175;
          }
          v88 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&v110.m_lpDispatch[8],
                  dwCastleID,
                  dwCastleObjectID.dwServerInfo,
                  v85->m_CreatureStatus.m_nNowHP,
                  1u,
                  0x1Du,
                  0);
          v112 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v110);
          return v88;
        default:
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharCastleCmd",
            aDWorkRylSource_25,
            872,
            aCid0x08xCastle,
            dwCID,
            dwCastleID,
            wError_high);
          goto LABEL_174;
      }
      while ( 1 )
      {
        LOWORD(lpPktBasea) = (16 * (v20 + 5)) | 0xD;
        Item = CCharacter::GetItem(m_lpCharacter, lpPktBasea);
        lpJewel[v20] = Item;
        if ( !Item || Item->m_ItemData.m_cNumOrDurability != 10 )
          break;
        if ( ++v20 >= 9 )
        {
          m_usProtoTypeID = lpJewel[0]->m_ItemData.m_usProtoTypeID;
          v23 = 1;
          while ( m_usProtoTypeID == lpJewel[v23]->m_ItemData.m_usProtoTypeID )
          {
            if ( ++v23 >= 9 )
              goto LABEL_31;
          }
          v6 = 1;
LABEL_31:
          v24 = m_usProtoTypeID - 5 * v19->m_cUpgradeStep - 108;
          lpPktBaseb = v24;
          if ( !v19->m_cUpgradeStep && v24 > 5u || lpEmblema->m_cUpgradeType != v24 )
            break;
          if ( v6 )
            goto LABEL_175;
          v25 = dwCID;
          for ( i = 0; i < 9; ++i )
          {
            v27 = lpJewel[i];
            LOWORD(dwCID) = v27->m_ItemData.m_ItemPos;
            if ( CCharacter::RemoveItem(m_lpCharacter, dwCID) )
            {
              if ( v27 )
                ((void (__thiscall *)(Item::CItem *, int))v27->~Item::CItem)(v27, 1);
              lpJewel[i] = 0;
            }
            else
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "GameClientParsePacket::ParseCharCastleCmd",
                aDWorkRylSource_25,
                178,
                aCid0x08x_334,
                v25);
            }
          }
          v28 = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v28);
          v112 = 1;
          if ( !StoragelpDBAgentDispatch.m_lpDispatch )
          {
            v94 = 192;
LABEL_161:
            v6 = 1;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ParseCharCastleCmd",
              aDWorkRylSource_25,
              v94,
              (char *)&byte_4EE5AC);
            v112 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
            goto LABEL_175;
          }
          v15 = GameClientSendPacket::SendCharCastleCmdToDBAgent(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  dwCastleID,
                  lpEmblema->m_dwCID,
                  lpEmblema->m_CreatureStatus.m_nNowHP,
                  lpPktBaseb,
                  3u,
                  0);
LABEL_164:
          v84 = v15;
          v112 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return v84;
        }
      }
    }
    else
    {
      v93 = ((int (__thiscall *)(CCharacter *, _DWORD))m_lpCharacter->GetGID)(m_lpCharacter, wError_high);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCastleCmd",
        aDWorkRylSource_25,
        75,
        byte_4F1048,
        dwCID,
        v93,
        v97);
    }
  }
  else
  {
    v9 = ((int (__thiscall *)(CCharacter *, _DWORD))m_lpCharacter->GetGID)(m_lpCharacter, wError_high);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCastleCmd",
      aDWorkRylSource_25,
      59,
      aDwcastleid0x08,
      dwCastleID,
      dwCID,
      v9);
  }
LABEL_174:
  v6 = 1;
LABEL_175:
  v89 = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
  if ( v89 )
    return GameClientSendPacket::SendCharCastleCmd(v89 + 8, dwCastleID, dwCastleObjectID.dwServerInfo, cSubCmd, v6);
  else
    return 1;
}
// 484F3D: variable 'v97' is possibly undefined

//----- (00485D00) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCastleRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  int v3; // ebx
  unsigned int m_CodePage; // ebp
  CCharacter *m_lpCharacter; // esi
  __int16 v6; // dx
  CMsgProcessMgr *Instance; // eax
  CMsgProc *Castle; // eax
  Castle::CCastle *v9; // edi
  int v10; // eax
  int v11; // eax
  CPartyMgr *v12; // eax
  int v13; // eax
  CSingleDispatch *DispatchTable; // eax
  CSendStream *m_lpGameClientDispatch; // esi
  char v16; // bl
  unsigned int v17; // [esp-14h] [ebp-38h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+0h] [ebp-24h] BYREF
  CastleRight casteRight; // [esp+8h] [ebp-1Ch] BYREF
  int v20; // [esp+20h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+2Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1E )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  *(_DWORD *)casteRight.m_aryCastleRight = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v6 = lpPktBase[2].m_CodePage;
  *(_DWORD *)&casteRight.m_aryCastleRight[4] = *(_DWORD *)&lpPktBase[2].m_StartBit;
  *(_WORD *)&casteRight.m_aryCastleRight[8] = v6;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  if ( !m_lpCharacter->GetGID(m_lpCharacter) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCastleRight",
      aDWorkRylSource_25,
      909,
      (char *)&byte_4F1448,
      v3);
  Instance = (CMsgProcessMgr *)Castle::CCastleMgr::GetInstance();
  Castle = Castle::CCastleMgr::GetCastle(Instance, m_CodePage);
  v9 = (Castle::CCastle *)Castle;
  if ( Castle )
  {
    lpPktBasea = (PktBase *)Castle[4].__vftable;
    if ( (PktBase *)m_lpCharacter->GetGID(m_lpCharacter) != lpPktBasea )
    {
      v11 = ((int (__thiscall *)(CCharacter *, unsigned int, unsigned int))m_lpCharacter->GetGID)(
              m_lpCharacter,
              m_CodePage,
              v9->m_dwGID);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCastleRight",
        aDWorkRylSource_25,
        925,
        (char *)&byte_4F1328,
        v3,
        v11);
    }
    v17 = m_lpCharacter->GetGID(m_lpCharacter);
    v12 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    if ( Guild::CGuildMgr::GetGuild(v12, v17) )
    {
      if ( Castle::CCastle::CheckRight(v9, 0, v9->m_dwGID, m_lpCharacter->m_dwCID) )
      {
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v20 = 0;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          v16 = GameClientSendPacket::SendCharCastleRight(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  m_CodePage,
                  &casteRight,
                  0);
          v20 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return v16;
        }
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCastleRight",
          aDWorkRylSource_25,
          948,
          (char *)&byte_4EE5AC);
        v20 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      }
    }
    else
    {
      v13 = m_lpCharacter->GetGID(m_lpCharacter);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCastleRight",
        aDWorkRylSource_25,
        932,
        (char *)&byte_4F12C8,
        v3,
        v13);
    }
  }
  else
  {
    v10 = m_lpCharacter->GetGID(m_lpCharacter);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCastleRight",
      aDWorkRylSource_25,
      917,
      aDwcastleid0x08_0,
      m_CodePage,
      v3,
      v10);
  }
  m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharCastleRight(m_lpGameClientDispatch + 8, m_CodePage, &casteRight, 1u);
  else
    return 1;
}

//----- (00485F70) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCampRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebp
  CCharacter *m_lpCharacter; // esi
  __int16 v5; // dx
  int v6; // edi
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *Camp; // eax
  CSiegeObject *v9; // ebx
  int v10; // eax
  int v11; // eax
  CPartyMgr *v12; // eax
  Guild::CGuild *Guild; // eax
  CSingleDispatch *DispatchTable; // eax
  CSendStream *m_lpGameClientDispatch; // esi
  char v16; // bl
  unsigned int v17; // [esp-14h] [ebp-70h]
  int v18; // [esp-14h] [ebp-70h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+0h] [ebp-5Ch] BYREF
  CampRight campRight; // [esp+8h] [ebp-54h] BYREF
  Guild::MemberInfo Master; // [esp+14h] [ebp-48h] BYREF
  int v22; // [esp+58h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+64h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1E )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  *(_DWORD *)campRight.m_aryCampRight = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v5 = lpPktBase[2].m_CodePage;
  v6 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  *(_DWORD *)&campRight.m_aryCampRight[4] = *(_DWORD *)&lpPktBase[2].m_StartBit;
  *(_WORD *)&campRight.m_aryCampRight[8] = v5;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  if ( !m_lpCharacter->GetGID(m_lpCharacter) )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCampRight",
      aDWorkRylSource_25,
      989,
      (char *)&byte_4F1448,
      v6);
  Instance = CSiegeObjectMgr::GetInstance();
  Camp = CSiegeObjectMgr::GetCamp(Instance, m_CodePage);
  v9 = Camp;
  if ( Camp )
  {
    if ( Camp->m_wObjectType != 5337 )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCampRight",
        aDWorkRylSource_25,
        1004,
        (char *)&byte_4F15F8,
        v6,
        m_CodePage);
    lpPktBasea = (PktBase *)v9->m_dwGID;
    if ( (PktBase *)m_lpCharacter->GetGID(m_lpCharacter) != lpPktBasea )
    {
      v11 = ((int (__thiscall *)(CCharacter *, unsigned int, unsigned int))m_lpCharacter->GetGID)(
              m_lpCharacter,
              m_CodePage,
              v9->m_dwGID);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCampRight",
        aDWorkRylSource_25,
        1011,
        (char *)&byte_4F1568,
        v6,
        v11);
    }
    v17 = m_lpCharacter->GetGID(m_lpCharacter);
    v12 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v12, v17);
    if ( Guild )
    {
      Guild::CGuild::GetMaster(Guild, &Master);
      if ( Master.m_dwCID == v6 )
      {
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v22 = 0;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          v16 = GameClientSendPacket::SendCharCampRight(
                  (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                  m_CodePage,
                  &campRight,
                  0);
          v22 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return v16;
        }
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCampRight",
          aDWorkRylSource_25,
          1036,
          (char *)&byte_4EE5AC);
        v22 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCampRight",
          aDWorkRylSource_25,
          1026,
          (char *)&byte_4F14A0,
          v6,
          Master.m_dwCID);
      }
    }
    else
    {
      v18 = m_lpCharacter->GetGID(m_lpCharacter);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCampRight",
        aDWorkRylSource_25,
        1018,
        byte_4F1500,
        v6,
        v18);
    }
  }
  else
  {
    v10 = m_lpCharacter->GetGID(m_lpCharacter);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCampRight",
      aDWorkRylSource_25,
      997,
      aDwcampid0x08x,
      m_CodePage,
      v6,
      v10);
  }
  m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharCampRight(m_lpGameClientDispatch + 8, m_CodePage, &campRight, 1u);
  else
    return 1;
}

//----- (00486220) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCampCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  char result; // al
  CCharacter *m_lpCharacter; // ebp
  CCharacter_vtbl *v4; // edx
  unsigned __int8 m_Len; // bl
  unsigned int v6; // edi
  unsigned __int16 v7; // si
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *Camp; // edi
  int v10; // eax
  CCharacter_vtbl *v11; // edx
  int v12; // eax
  CPartyMgr *v13; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ecx
  char v16; // al
  CSingleDispatch::Storage *p_StoragelpDBAgentDispatch; // ecx
  char v18; // bl
  unsigned int v19; // ebx
  Item::CItem *Item; // eax
  Item::CItem *v21; // esi
  int v22; // eax
  CSingleDispatch *v23; // eax
  unsigned int v24; // ebx
  Item::CItem *v25; // eax
  Item::CItem *v26; // esi
  int v27; // eax
  CSingleDispatch *v28; // eax
  CPacketDispatch *v29; // ebx
  unsigned int m_dwCID; // esi
  CPartyMgr *v31; // eax
  CSingleDispatch *v32; // eax
  CSingleDispatch *v33; // eax
  CSendStream *m_lpGameClientDispatch; // ebp
  unsigned int RepairHP; // [esp-18h] [ebp-7Ch]
  int v36; // [esp-14h] [ebp-78h]
  unsigned __int8 v37; // [esp-14h] [ebp-78h]
  unsigned int m_dwGID; // [esp-10h] [ebp-74h]
  unsigned int v39; // [esp-10h] [ebp-74h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-60h] BYREF
  unsigned int dwCID; // [esp+Ch] [ebp-58h]
  int cSubCmd; // [esp+10h] [ebp-54h]
  CSingleDispatch::Storage v43; // [esp+14h] [ebp-50h] BYREF
  Guild::MemberInfo Master; // [esp+1Ch] [ebp-48h] BYREF
  int v45; // [esp+60h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+6Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1B )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = m_lpCharacter->__vftable;
  m_Len = lpPktBase[2].m_Len;
  v6 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  lpPktBasea = (PktBase *)lpPktBase[1].m_CodePage;
  dwCID = v6;
  LOBYTE(cSubCmd) = m_Len;
  v7 = 0;
  if ( !v4->GetGID(m_lpCharacter) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCampCmd",
      aDWorkRylSource_25,
      1077,
      (char *)&byte_4F1898,
      v6,
      m_Len);
    v7 = 1;
  }
  Instance = CSiegeObjectMgr::GetInstance();
  Camp = CSiegeObjectMgr::GetCamp(Instance, (unsigned int)lpPktBasea);
  if ( !Camp )
  {
    v10 = ((int (__thiscall *)(CCharacter *, _DWORD))m_lpCharacter->GetGID)(m_lpCharacter, m_Len);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCampCmd",
      aDWorkRylSource_25,
      1085,
      aDwcampid0x08x_0,
      lpPktBasea,
      dwCID,
      v10);
LABEL_51:
    v7 = 1;
LABEL_52:
    m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      return GameClientSendPacket::SendCharCampCmd(
               m_lpGameClientDispatch + 8,
               dwCID,
               (unsigned int)lpPktBasea,
               cSubCmd,
               v7);
    else
      return 1;
  }
  v11 = m_lpCharacter->__vftable;
  StoragelpDBAgentDispatch.m_lpDispatch = (CPacketDispatch *)Camp->m_dwGID;
  if ( (CPacketDispatch *)v11->GetGID(m_lpCharacter) != StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v12 = ((int (__thiscall *)(CCharacter *, PktBase *, unsigned int, _DWORD))m_lpCharacter->GetGID)(
            m_lpCharacter,
            lpPktBasea,
            Camp->m_dwGID,
            m_Len);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharCampCmd",
      aDWorkRylSource_25,
      1093,
      (char *)&byte_4F1780,
      dwCID,
      v12);
    goto LABEL_51;
  }
  if ( v7 )
    goto LABEL_52;
  switch ( m_Len )
  {
    case 3u:
      if ( Camp->m_cState != 4 )
        goto LABEL_51;
      m_dwGID = Camp->m_dwGID;
      v13 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      if ( Guild::CGuildMgr::GetGuild(v13, m_dwGID) )
        goto LABEL_51;
      Guild::CGuild::GetMaster(0, &Master);
      if ( dwCID != Master.m_dwCID )
        goto LABEL_51;
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
      m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
      v45 = 0;
      if ( !StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v36 = 1129;
        goto LABEL_43;
      }
      v37 = 3;
      goto LABEL_19;
    case 5u:
      v19 = dwCID;
      if ( !CSiegeObject::CheckRight(Camp, 0, dwCID) )
        goto LABEL_51;
      LOWORD(StoragelpDBAgentDispatch.m_lpDispatch) = 76;
      Item = CCharacter::GetItem(m_lpCharacter, (int)StoragelpDBAgentDispatch.m_lpDispatch);
      v21 = Item;
      if ( !Item )
        goto LABEL_51;
      v22 = Item->m_ItemData.m_cNumOrDurability - 10;
      v21->m_ItemData.m_cNumOrDurability = v22;
      if ( !v22 )
      {
        LOWORD(StoragelpDBAgentDispatch.m_lpDispatch) = v21->m_ItemData.m_ItemPos;
        if ( CCharacter::RemoveItem(m_lpCharacter, (int)StoragelpDBAgentDispatch.m_lpDispatch) )
          ((void (__thiscall *)(Item::CItem *, int))v21->~Item::CItem)(v21, 1);
        else
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharCampCmd",
            aDWorkRylSource_25,
            1166,
            aCid0x08x_57,
            v19);
      }
      v23 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v23);
      m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
      v7 = 1;
      v45 = 1;
      if ( !StoragelpDBAgentDispatch.m_lpDispatch )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCampCmd",
          aDWorkRylSource_25,
          1180,
          (char *)&byte_4EE5AC);
        goto LABEL_44;
      }
      v37 = 5;
      goto LABEL_19;
    case 7u:
      v24 = dwCID;
      if ( !CSiegeObject::CheckRight(Camp, 1u, dwCID) )
        goto LABEL_51;
      LOWORD(StoragelpDBAgentDispatch.m_lpDispatch) = 76;
      v25 = CCharacter::GetItem(m_lpCharacter, (int)StoragelpDBAgentDispatch.m_lpDispatch);
      v26 = v25;
      if ( !v25 )
        goto LABEL_51;
      v27 = v25->m_ItemData.m_cNumOrDurability - 10;
      v26->m_ItemData.m_cNumOrDurability = v27;
      if ( !v27 )
      {
        LOWORD(StoragelpDBAgentDispatch.m_lpDispatch) = v26->m_ItemData.m_ItemPos;
        if ( CCharacter::RemoveItem(m_lpCharacter, (int)StoragelpDBAgentDispatch.m_lpDispatch) )
          ((void (__thiscall *)(Item::CItem *, int))v26->~Item::CItem)(v26, 1);
        else
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharCampCmd",
            aDWorkRylSource_25,
            1216,
            aCid0x08x_10,
            v24);
      }
      v28 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v28);
      v29 = StoragelpDBAgentDispatch.m_lpDispatch;
      v45 = 2;
      if ( !StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v36 = 1230;
        goto LABEL_43;
      }
      m_dwCID = Camp->m_dwCID;
      RepairHP = CSiegeObject::GetRepairHP(Camp);
      v16 = GameClientSendPacket::SendCharCampCmdToDBAgent(
              (CSendStream *)&v29[8],
              m_dwCID,
              (unsigned int)lpPktBasea,
              RepairHP,
              7u,
              0);
      goto LABEL_20;
    case 9u:
      if ( !CSiegeObject::CheckRight(Camp, 2u, dwCID) )
        goto LABEL_51;
      v33 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&v43, v33);
      v45 = 4;
      if ( !v43.m_lpDispatch )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCampCmd",
          aDWorkRylSource_25,
          1282,
          (char *)&byte_4EE5AC);
        v45 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v43);
        goto LABEL_52;
      }
      v16 = GameClientSendPacket::SendCharCampCmdToDBAgent(
              (CSendStream *)&v43.m_lpDispatch[8],
              Camp->m_dwCID,
              (unsigned int)lpPktBasea,
              Camp->m_CreatureStatus.m_nNowHP,
              9u,
              0);
      p_StoragelpDBAgentDispatch = &v43;
      goto LABEL_21;
    case 0xCu:
      v39 = m_lpCharacter->GetGID(m_lpCharacter);
      v31 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      if ( !Guild::CGuildMgr::GetGuild(v31, v39) )
        goto LABEL_51;
      v32 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v32);
      m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
      v45 = 3;
      if ( !StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v36 = 1253;
LABEL_43:
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharCampCmd",
          aDWorkRylSource_25,
          v36,
          (char *)&byte_4EE5AC);
LABEL_44:
        v45 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
        goto LABEL_52;
      }
      v37 = 12;
LABEL_19:
      v16 = GameClientSendPacket::SendCharCampCmdToDBAgent(
              (CSendStream *)&m_lpDispatch[8],
              Camp->m_dwCID,
              (unsigned int)lpPktBasea,
              Camp->m_CreatureStatus.m_nNowHP,
              v37,
              0);
LABEL_20:
      p_StoragelpDBAgentDispatch = &StoragelpDBAgentDispatch;
LABEL_21:
      v18 = v16;
      v45 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)p_StoragelpDBAgentDispatch);
      result = v18;
      break;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharCampCmd",
        aDWorkRylSource_25,
        1295,
        aCid0x08xCampid,
        dwCID,
        lpPktBasea,
        m_Len);
      goto LABEL_51;
  }
  return result;
}

//----- (004867B0) --------------------------------------------------------
bool __thiscall std::vector<Item::ItemGarbage>::empty(std::vector<Item::ItemGarbage> *this)
{
  Item::ItemGarbage *Myfirst; // edx

  Myfirst = this->_Myfirst;
  return !Myfirst || this->_Mylast - Myfirst == 0;
}

//----- (004867E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSiegeArmsCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  char result; // al
  CCharacter *m_lpCharacter; // ecx
  unsigned __int8 v5; // bl
  unsigned int m_CodePage; // ebp
  unsigned int v7; // edi
  unsigned __int16 v8; // si
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *SiegeObject; // eax
  CSiegeObject *v11; // ecx
  unsigned __int16 m_wObjectType; // ax
  unsigned int m_dwOwnerID; // edx
  CSingleDispatch *DispatchTable; // eax
  char v15; // al
  CSingleDispatch *v16; // eax
  CSendStream *v17; // eax
  CSingleDispatch *v18; // eax
  CSendStream *v19; // eax
  char v20; // bl
  CPacketDispatch *v21; // ebx
  CSingleDispatch *v22; // eax
  int RepairHP; // eax
  CPacketDispatch *m_lpDispatch; // ecx
  unsigned int v25; // ebx
  char v26; // bl
  CSingleDispatch *v27; // eax
  char v28; // bl
  CSendStream *m_lpGameClientDispatch; // eax
  int v30; // [esp-14h] [ebp-54h]
  int v31; // [esp-10h] [ebp-50h]
  CSiegeObject *lpSiegeArms; // [esp+4h] [ebp-3Ch]
  unsigned __int8 wRequireItemNum; // [esp+8h] [ebp-38h]
  int wRequireItemNuma; // [esp+8h] [ebp-38h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+Ch] [ebp-34h] BYREF
  CMultiDispatch::Storage lpDBAgentDispatch; // [esp+14h] [ebp-2Ch] BYREF
  CSingleDispatch::Storage v37; // [esp+1Ch] [ebp-24h] BYREF
  std::vector<Item::ItemGarbage> vecItemGarbage; // [esp+24h] [ebp-1Ch] BYREF
  int v39; // [esp+3Ch] [ebp-4h]
  CCharacter *lpPktBasea; // [esp+48h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1E )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  lpPktBasea = m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v5 = BYTE1(lpPktBase[2].m_CodePage);
  m_CodePage = lpPktBase[1].m_CodePage;
  v7 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  wRequireItemNum = v5;
  v8 = 0;
  if ( !m_lpCharacter->GetGID(m_lpCharacter) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSiegeArmsCmd",
      aDWorkRylSource_25,
      1333,
      (char *)&byte_4F1A98,
      v7,
      v5);
    v8 = 1;
  }
  Instance = CSiegeObjectMgr::GetInstance();
  SiegeObject = CSiegeObjectMgr::GetSiegeObject(Instance, m_CodePage);
  v11 = SiegeObject;
  lpSiegeArms = SiegeObject;
  if ( !SiegeObject )
  {
    v30 = ((int (__thiscall *)(CCharacter *, _DWORD))lpPktBasea->GetGID)(lpPktBasea, v5);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSiegeArmsCmd",
      aDWorkRylSource_25,
      1341,
      aDwarmsid0x08x_0,
      m_CodePage,
      v7,
      v30,
      v31);
LABEL_54:
    v8 = 1;
LABEL_55:
    m_lpGameClientDispatch = (CSendStream *)lpPktBasea->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      return GameClientSendPacket::SendCharSiegeArmsCmd(m_lpGameClientDispatch + 8, v7, m_CodePage, wRequireItemNum, v8);
    else
      return 1;
  }
  m_wObjectType = SiegeObject->m_wObjectType;
  if ( m_wObjectType != 5530 && m_wObjectType != 5578 && m_wObjectType != 5626 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSiegeArmsCmd",
      aDWorkRylSource_25,
      1353,
      aDwarmsid0x08x,
      m_CodePage,
      v7,
      v5);
    v11 = lpSiegeArms;
    v8 = 1;
  }
  m_dwOwnerID = v11->m_dwOwnerID;
  if ( lpPktBasea->m_dwCID != m_dwOwnerID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSiegeArmsCmd",
      aDWorkRylSource_25,
      1360,
      (char *)&byte_4F1930,
      v7,
      m_CodePage,
      m_dwOwnerID,
      v5);
    goto LABEL_54;
  }
  if ( v8 )
    goto LABEL_55;
  switch ( v5 )
  {
    case 3u:
      if ( v11->m_cState != 4 )
        goto LABEL_54;
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage((CSingleDispatch::Storage *)&lpDBAgentDispatch, DispatchTable);
      v39 = 0;
      if ( !lpDBAgentDispatch.m_lpDispatch )
      {
        v8 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSiegeArmsCmd",
          aDWorkRylSource_25,
          1383,
          (char *)&byte_4EE5AC);
        v39 = -1;
        CSingleDispatch::Storage::~Storage(&lpDBAgentDispatch);
        goto LABEL_55;
      }
      v15 = GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
              (CSendStream *)&lpDBAgentDispatch.m_lpDispatch[8],
              v7,
              m_CodePage,
              0,
              3u,
              0);
      goto LABEL_37;
    case 4u:
      v8 = v11->m_cState != 0;
      if ( CSiegeObject::IsRide(v11, v7) == 1 )
        v8 = 1;
      if ( lpPktBasea->m_dwRideArmsCID )
        goto LABEL_54;
      if ( v8 )
        goto LABEL_55;
      if ( !CSiegeObject::Ride(lpSiegeArms, v7) )
        goto LABEL_54;
      v16 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage((CSingleDispatch::Storage *)&lpDBAgentDispatch, v16);
      v39 = 1;
      if ( lpDBAgentDispatch.m_lpDispatch )
        GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
          (CSendStream *)&lpDBAgentDispatch.m_lpDispatch[8],
          v7,
          m_CodePage,
          0,
          4u,
          0);
      v17 = (CSendStream *)lpPktBasea->m_lpGameClientDispatch;
      if ( !v17 )
        goto LABEL_38;
      v15 = GameClientSendPacket::SendCharSiegeArmsCmd(v17 + 8, v7, m_CodePage, 4u, 0);
      goto LABEL_37;
    case 5u:
      v8 = v11->m_cState != 0;
      if ( !CSiegeObject::IsRide(v11, v7) )
        goto LABEL_54;
      if ( v8 )
        goto LABEL_55;
      if ( !CSiegeObject::GetOff(lpSiegeArms, v7) )
        goto LABEL_54;
      v18 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage((CSingleDispatch::Storage *)&lpDBAgentDispatch, v18);
      v39 = 2;
      if ( lpDBAgentDispatch.m_lpDispatch )
        GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
          (CSendStream *)&lpDBAgentDispatch.m_lpDispatch[8],
          v7,
          m_CodePage,
          0,
          5u,
          0);
      v19 = (CSendStream *)lpPktBasea->m_lpGameClientDispatch;
      if ( !v19 )
      {
LABEL_38:
        v39 = -1;
        CSingleDispatch::Storage::~Storage(&lpDBAgentDispatch);
        return 1;
      }
      v15 = GameClientSendPacket::SendCharSiegeArmsCmd(v19 + 8, v7, m_CodePage, 5u, 0);
LABEL_37:
      v20 = v15;
      v39 = -1;
      CSingleDispatch::Storage::~Storage(&lpDBAgentDispatch);
      return v20;
    case 6u:
      v21 = (CPacketDispatch *)((int (__thiscall *)(Item::CArrayContainer *, int))lpPktBasea->m_Inventory.GetItemNum)(
                                 &lpPktBasea->m_Inventory,
                                 9914);
      StoragelpDBAgentDispatch.m_lpDispatch = v21;
      if ( !(_WORD)v21 || lpSiegeArms->m_CreatureStatus.m_nNowHP == lpSiegeArms->m_CreatureStatus.m_StatusInfo.m_nMaxHP )
        goto LABEL_54;
      v22 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&v37, v22);
      v39 = 3;
      lpDBAgentDispatch.m_lpDispatch = v37.m_lpDispatch;
      if ( !v37.m_lpDispatch )
      {
        v8 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSiegeArmsCmd",
          aDWorkRylSource_25,
          1499,
          (char *)&byte_4EE5AC);
        v39 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v37);
        goto LABEL_55;
      }
      memset(&vecItemGarbage._Myfirst, 0, 12);
      LOBYTE(v39) = 4;
      wRequireItemNuma = CSiegeObject::GetRepairMaterialNum(lpSiegeArms);
      if ( (unsigned __int16)v21 < (unsigned __int16)wRequireItemNuma )
      {
        m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
        v25 = 10 * (unsigned __int16)v21;
      }
      else
      {
        RepairHP = CSiegeObject::GetRepairHP(lpSiegeArms);
        m_lpDispatch = (CPacketDispatch *)wRequireItemNuma;
        v25 = RepairHP;
      }
      lpPktBasea->m_Inventory.DisappearItem(
        &lpPktBasea->m_Inventory,
        9914u,
        (unsigned __int16)m_lpDispatch,
        &vecItemGarbage);
      if ( !std::vector<Item::ItemGarbage>::empty(&vecItemGarbage) )
        CCharacter::ClearGarbage(lpPktBasea, &vecItemGarbage);
      v26 = GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
              (CSendStream *)&lpDBAgentDispatch.m_lpDispatch[8],
              v7,
              m_CodePage,
              v25,
              6u,
              0);
      std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&vecItemGarbage);
      v39 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v37);
      return v26;
    case 8u:
      v27 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v27);
      v39 = 5;
      if ( !StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v8 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSiegeArmsCmd",
          aDWorkRylSource_25,
          1541,
          (char *)&byte_4EE5AC);
        v39 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
        goto LABEL_55;
      }
      v28 = GameClientSendPacket::SendCharSiegeArmsCmdToDBAgent(
              (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
              v7,
              m_CodePage,
              0,
              8u,
              0);
      v39 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      result = v28;
      break;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharSiegeArmsCmd",
        aDWorkRylSource_25,
        1554,
        aCid0x08xArmsid,
        v7,
        m_CodePage,
        v5);
      goto LABEL_54;
  }
  return result;
}
// 4868DE: variable 'v31' is possibly undefined

//----- (00486DF0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerMapList(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  CCharacter *m_lpCharacter; // [esp-4h] [ebp-4h]

  if ( GameClientDispatch->m_lpCharacter )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea::CVirtualAreaMgr::SendBGServerMapList(Instance, m_lpCharacter);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00486E30) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerResultList(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  CCharacter *m_lpCharacter; // [esp-4h] [ebp-4h]

  if ( GameClientDispatch->m_lpCharacter )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea::CVirtualAreaMgr::SendBGServerResultList(Instance, m_lpCharacter);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00486E70) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerMoveZone(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  __int16 v3; // ax
  VirtualArea::CVirtualAreaMgr *v4; // eax
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  CCharacter *m_lpCharacter; // [esp-10h] [ebp-10h]
  unsigned __int16 v7; // [esp-Ch] [ebp-Ch]
  CCharacter *v8; // [esp-8h] [ebp-8h]

  if ( GameClientDispatch->m_lpCharacter )
  {
    v3 = *(_WORD *)&lpPktBase[1].m_StartBit;
    if ( v3 )
    {
      LOBYTE(lpPktBase) = lpPktBase[1].m_Len;
      v7 = v3 | 0x8000;
      m_lpCharacter = GameClientDispatch->m_lpCharacter;
      Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea::CVirtualAreaMgr::EnterVirtualArea(Instance, m_lpCharacter, v7, (int)lpPktBase);
    }
    else
    {
      v8 = GameClientDispatch->m_lpCharacter;
      v4 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea::CVirtualAreaMgr::LeaveVirtualArea(v4, v8);
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00486EE0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerMileageChange(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  int v4; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v5; // esi
  unsigned int v6; // eax
  unsigned __int8 lpPktBasea; // [esp+8h] [ebp+8h]

  if ( !GameClientDispatch->m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  lpPktBasea = lpPktBase[1].m_CodePage;
  v4 = BYTE1(lpPktBase[1].m_CodePage);
  v5 = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node **)&lpPktBase[1].m_StartBit;
  v6 = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 2);
  if ( v4 )
  {
    if ( v4 != 2 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharBGServerMileageChange",
        aDWorkRylSource_93,
        97,
        aCid0x08x_325,
        v5,
        v4);
      return 1;
    }
    RegularAgentPacketParse::SendSetCharData(v5, v6, lpPktBasea);
  }
  return 1;
}

//----- (00486F60) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerCharSlot(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  if ( GameClientDispatch->m_lpCharacter )
  {
    RegularAgentPacketParse::SendGetCharSlot(
      *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node **)&lpPktBase[1].m_StartBit,
      lpPktBase[1].m_SrvInfo.SrvState.wError);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00486FA0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBGServerList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // eax
  CSendStream *m_lpGameClientDispatch; // eax

  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( m_lpCharacter )
  {
    m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      GameClientSendPacket::SendRegularServerList(m_lpGameClientDispatch + 8);
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00486FE0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSuicide(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  int v4; // edi
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-24h]
  Position Pos; // [esp+8h] [ebp-18h] BYREF
  Position result; // [esp+14h] [ebp-Ch] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x10 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      memset(&Pos, 0, sizeof(Pos));
      m_lpCharacter->Dead(m_lpCharacter, 0);
      if ( m_lpCharacter->m_CellPos.m_wMapIndex )
      {
        m_wMapIndex = m_lpCharacter->m_CellPos.m_wMapIndex;
        Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
        VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, m_wMapIndex);
        if ( VirtualArea )
          Pos = *VirtualArea::CVirtualArea::GetRespawnPosition(
                   VirtualArea,
                   &result,
                   m_lpCharacter->m_DBData.m_Info.Nationality,
                   0);
      }
      if ( CCharacter::Respawn(m_lpCharacter, Pos, 1) )
      {
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSuicide",
          aDWorkRylSource_58,
          55,
          aCid0x08x_259,
          v4);
        return 0;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00487100) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharBindPosition(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  signed __int8 m_StartBit; // cl
  unsigned int v6; // ebx
  float v7; // edx
  unsigned __int8 cCmd; // [esp+2Ch] [ebp-10h]
  Position DestPos; // [esp+30h] [ebp-Ch] BYREF
  char lpPktBasea; // [esp+44h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1E )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      m_StartBit = lpPktBase[2].m_StartBit;
      v6 = *(_DWORD *)&lpPktBase[2].m_Cmd;
      cCmd = BYTE1(lpPktBase[2].m_CodePage);
      DestPos.m_fPointX = *(float *)&lpPktBase[1].m_StartBit;
      LODWORD(DestPos.m_fPointY) = lpPktBase[1].m_CodePage;
      v7 = *(float *)&lpPktBase[1].m_SrvInfo.dwServerInfo;
      lpPktBasea = m_StartBit;
      DestPos.m_fPointZ = v7;
      if ( cCmd )
      {
        if ( cCmd == 1 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "GameClientParsePacket::ParseCharBindPosition",
            aDWorkRylSource_58,
            88,
            aCid0x08x_170,
            m_lpCharacter->m_dwCID,
            *(float *)&lpPktBase[1].m_StartBit,
            *(float *)&lpPktBase[1].m_CodePage,
            *(float *)&lpPktBase[1].m_SrvInfo.dwServerInfo,
            m_StartBit);
          CAggresiveCreature::MoveTo(m_lpCharacter, &DestPos, 0);
        }
      }
      else
      {
        CCharacter::BindPositionToNPC(m_lpCharacter, v6);
      }
      return GameClientSendPacket::SendCharBindPosition(
               &GameClientDispatch->m_SendStream,
               v6,
               cCmd,
               (Position)lpPktBase[1],
               lpPktBasea,
               0);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00487220) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharQuickSlotMove(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  int v4; // edi
  unsigned __int8 m_CodePage; // al
  unsigned __int16 v6; // di
  TakeType v7; // [esp-Ch] [ebp-20h]
  unsigned __int16 v8; // [esp-4h] [ebp-18h]
  TakeType takeType; // [esp+Ch] [ebp-8h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x13 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      v8 = *(_WORD *)((char *)&lpPktBase[1].m_CodePage + 1);
      takeType.m_cNum = lpPktBase[1].m_CodePage;
      m_CodePage = lpPktBase[1].m_CodePage;
      *(_DWORD *)&v7.m_srcPos = *(_DWORD *)&lpPktBase[1].m_StartBit;
      *(_DWORD *)&takeType.m_srcPos = v4;
      v6 = 0;
      v7.m_cNum = m_CodePage;
      if ( !CCharacter::MoveQuickSlot(m_lpCharacter, v7, v8) )
        v6 = 2;
      return GameClientSendPacket::SendCharQuickSlotMove(&GameClientDispatch->m_SendStream, &takeType, v6);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004872D0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharControlOption(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int v4; // edi
  CSingleDispatch *DispatchTable; // eax
  char v6; // bl
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+0h] [ebp-14h] BYREF
  int v8; // [esp+10h] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x12 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      LOWORD(lpPktBase) = lpPktBase[1].m_CodePage;
      if ( CCharacter::ControlOption(m_lpCharacter, (RejectOption)lpPktBase, 0) )
      {
        DispatchTable = CChatDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
        v8 = 0;
        if ( StoragelpChatDispatch.m_lpDispatch )
          CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], m_lpCharacter);
        v6 = GameClientSendPacket::SendCharControlOption(
               &GameClientDispatch->m_SendStream,
               v4,
               (RejectOption *)&lpPktBase);
        v8 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
        return v6;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharControlOption",
          aDWorkRylSource_58,
          139,
          aCid0x08x_237,
          v4);
        return 0;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00487410) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharAuthorizePanel(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  int v4; // esi
  unsigned int m_CodePage; // ebx
  CCreatureManager *v6; // eax
  CCharacter *Character; // eax
  POS *p_m_CurrentPos; // esi
  CServerSetup *v9; // eax
  char ServerZone; // al
  CServerSetup *Instance; // eax
  CCreatureManager *v12; // eax
  POS DestPos; // [esp+10h] [ebp-Ch]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  if ( LOBYTE(lpPktBase[1].m_SrvInfo.SrvState.wError) == 1 )
  {
    Instance = CServerSetup::GetInstance();
    if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 3 )
    {
      v12 = CCreatureManager::GetInstance();
      CCreatureManager::PopRespawnQueue(v12, m_lpCharacter);
    }
    if ( !CCharacter::Respawn(m_lpCharacter, m_lpCharacter->m_CurrentPos, 0) )
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharAuthorizePanel",
        aDWorkRylSource_58,
        176,
        aCid0x08x_303,
        v4);
  }
  else if ( LOBYTE(lpPktBase[1].m_SrvInfo.SrvState.wError) == 2 )
  {
    v6 = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(v6, m_CodePage);
    if ( Character )
    {
      p_m_CurrentPos = (POS *)&Character->m_CurrentPos;
      CAggresiveCreature::MoveTo(m_lpCharacter, &Character->m_CurrentPos, 0);
      DestPos = *p_m_CurrentPos;
      v9 = CServerSetup::GetInstance();
      ServerZone = CServerSetup::GetServerZone(v9);
      return GameClientSendPacket::SendCharBindPosition(
               &GameClientDispatch->m_SendStream,
               m_CodePage,
               2u,
               (Position)DestPos,
               ServerZone,
               0);
    }
  }
  return 1;
}

//----- (00487570) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharFameInfo(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x10 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      return GameClientSendPacket::SendCharFameInfo(
               &GameClientDispatch->m_SendStream,
               m_lpCharacter,
               szLoseCharName,
               szLoseCharName,
               0);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004875E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharRankingInfo(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x14 )
  {
    if ( GameClientDispatch->m_lpCharacter )
    {
      return CRankingMgr::SendRankingInfo(
               CSingleton<CRankingMgr>::ms_pSingleton,
               *(_DWORD *)&lpPktBase[1].m_StartBit,
               lpPktBase[1].m_CodePage,
               BYTE1(lpPktBase[1].m_CodePage));
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00487650) --------------------------------------------------------
bool __cdecl GameClientParsePacket::ParseCharStartQuest(
        CGameClientDispatch *GameClientDispatch,
        Quest::QuestNode *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebx
  unsigned __int16 v4; // bp
  unsigned int v5; // edi
  unsigned __int16 v6; // si
  CMsgProcessMgr *Instance; // eax
  CNPC *Castle; // eax
  unsigned int dwCharID; // [esp+0h] [ebp-4h]

  if ( (lpPktBase->m_wMaxPhase & 0x3FFF) == 0x16 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_WORD *)&lpPktBase->m_lstPhase._Alval.std::_Allocator_base<Quest::PhaseNode *>;
      v5 = *(_DWORD *)&lpPktBase->m_bSave;
      dwCharID = lpPktBase->m_dwCompletedQuest;
      v6 = 0;
      Instance = (CMsgProcessMgr *)CCreatureManager::GetInstance();
      Castle = (CNPC *)Castle::CCastleMgr::GetCastle(Instance, v5);
      if ( Castle )
      {
        lpPktBase = 0;
        if ( CNPC::GetQuest(Castle, m_lpCharacter, v4, &lpPktBase) )
        {
          if ( CCharacter::HasQuest(m_lpCharacter, lpPktBase->m_wQuestID) == 1 )
          {
            v6 = 5;
          }
          else if ( !CCharacter::GiveQuest(m_lpCharacter, lpPktBase) )
          {
            v6 = 4;
          }
        }
        else
        {
          v6 = 3;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharStartQuest",
          aDWorkRylSource_31,
          36,
          aCid0x08x_235,
          m_lpCharacter->m_dwCID,
          v5);
        v6 = 2;
      }
      LOBYTE(lpPktBase) = GameClientSendPacket::SendCharStartQuest(
                            &GameClientDispatch->m_SendStream,
                            dwCharID,
                            v5,
                            v4,
                            v6);
      if ( !v6 )
        CCharacter::StartPhase(m_lpCharacter, v4, 1);
      return (char)lpPktBase;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, HIBYTE(lpPktBase->m_wQuestID));
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, HIBYTE(lpPktBase->m_wQuestID));
    return 0;
  }
}

//----- (00487780) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharOperateTrigger(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  int v4; // edi
  unsigned __int16 v5; // ax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      v5 = CCharacter::OperateTrigger(
             m_lpCharacter,
             lpPktBase[1].m_CodePage,
             BYTE2(lpPktBase[1].m_CodePage),
             HIBYTE(lpPktBase[1].m_CodePage),
             LOBYTE(lpPktBase[1].m_SrvInfo.SrvState.wError) - 1,
             m_lpCharacter->m_CurrentPos);
      if ( v5 )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharOperateTrigger",
          aDWorkRylSource_31,
          93,
          aCid0x08x_135,
          v4,
          v5);
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00487850) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCancelQuest(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  unsigned int v4; // ebx
  unsigned __int16 m_CodePage; // si
  unsigned __int16 v6; // di

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x12 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_CodePage = lpPktBase[1].m_CodePage;
      v6 = 0;
      if ( !CCharacter::CancelQuest(m_lpCharacter, m_CodePage) )
        v6 = 2;
      return GameClientSendPacket::SendCharCancelQuest(&GameClientDispatch->m_SendStream, v4, m_CodePage, v6);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004878D0) --------------------------------------------------------
int __thiscall  __thiscall `vcall'{64,{flat}}(void *this)
{
  return (*(int (__thiscall **)(void *))(*(_DWORD *)this + 64))(this);
}

//----- (004878E0) --------------------------------------------------------
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>,std::pair<unsigned long const,CMonster *>>>(
        CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // edi
  std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> > *m_fnSecondProcess; // esi
  CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *v7; // eax

  Ptr = _First._Ptr;
  v5 = _Last._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v7 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  else
  {
    m_fnSecondProcess = _Func.m_fnSecondProcess;
    do
    {
      m_fnSecondProcess->op._Pmemfun(Ptr->_Myval.second, m_fnSecondProcess->value);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
      Ptr = _First._Ptr;
    }
    while ( _First._Ptr != v5 );
    v7 = result;
    result->m_fnSecondProcess = m_fnSecondProcess;
  }
  return v7;
}

//----- (00487930) --------------------------------------------------------
CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const,CCharacter *>>>(
        CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // edi
  CCharacter *second; // ecx
  CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *v7; // eax
  Position v8[2]; // [esp-10h] [ebp-18h] BYREF

  Ptr = _First._Ptr;
  v5 = _Last._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v7 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  else
  {
    do
    {
      second = Ptr->_Myval.second;
      if ( second )
      {
        memset(v8, 0, 12);
        CCharacter::Respawn(second, v8[0], 1);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
      Ptr = _First._Ptr;
    }
    while ( _First._Ptr != v5 );
    v7 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  return v7;
}

//----- (00487990) --------------------------------------------------------
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > *__thiscall CCreatureManager::ProcessAllMonster<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>>(
        int this,
        CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > _Func,
        int a3)
{
  CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> > result; // [esp+0h] [ebp-4h] BYREF

  result.m_fnSecondProcess = (std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> > *)this;
  return std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>,std::pair<unsigned long const,CMonster *>>>(
           &result,
           (std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)(*(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator **)(this + 16))->_Ptr,
           *(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)(this + 16),
           (CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> >)&_Func);
}

//----- (004879B0) --------------------------------------------------------
CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > *__thiscall CCreatureManager::ProcessAllCharacter<Respawn>(
        int this,
        CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > _Func)
{
  CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > result; // [esp+0h] [ebp-4h] BYREF

  result.m_fnSecondProcess = (Respawn *)this;
  return std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const,CCharacter *>>>(
           &result,
           (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)(*(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator **)(this + 40))->_Ptr,
           *(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *)(this + 40),
           (CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> >)&_Func);
}

//----- (004879D0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ProcessAdminCmd(CCharacter *lpAdmin, CCharacter *lpTarget, PktAdmin *lpPktAdmin)
{
  int v3; // ebp
  unsigned int m_dwAmount; // ebx
  POS *p_m_Position; // edi
  int m_cZone; // eax
  float fPointX; // edx
  __int64 v8; // rax
  CParty *v9; // eax
  float v10; // ecx
  CParty *v11; // eax
  CParty_vtbl *v12; // edx
  int v13; // ecx
  float v14; // esi
  int m_usProtoTypeID; // edx
  CCellManager *Instance; // eax
  unsigned int Gold; // edi
  CCharacter *v18; // esi
  int v19; // eax
  char dwHighDateTime_high; // cl
  CGameClientDispatch *m_lpGameClientDispatch; // eax
  int v22; // eax
  char v23; // cl
  CGameClientDispatch *v24; // eax
  CGameClientDispatch *v25; // ecx
  double m_fPointY; // st7
  double m_fPointZ; // st6
  char v28; // al
  Skill::CAddSpell<CInvincibleSpell> *v29; // eax
  CGameClientDispatch *v30; // esi
  CCell *m_lpCell; // ecx
  CCell *v32; // ebp
  CCharacter_vtbl *v33; // edx
  AwardTable::CAward *v34; // ebx
  unsigned __int8 v35; // al
  unsigned int AwardEquipment; // eax
  CCreatureManager *v37; // edi
  CServerSetup *v38; // eax
  CCreatureManager *v39; // eax
  CCreatureManager *v40; // eax
  CCharacter_vtbl *v41; // edx
  unsigned __int8 Nationality; // bl
  unsigned __int8 v43; // al
  __int16 v44; // bx
  unsigned __int8 PreviousJob; // al
  int m_nLevel; // esi
  unsigned int m_dwCID; // esi
  unsigned __int8 v48; // bl
  unsigned __int64 fPointY; // rax
  int v50; // esi
  unsigned __int8 v51; // bl
  Respawn *v52; // esi
  Respawn *v53; // eax
  Item::CUseItem *v54; // eax
  unsigned __int8 v55; // cl
  CPacketDispatch *v56; // eax
  double v57; // st7
  unsigned __int8 v58; // bl
  char SkillSlotIndex; // al
  CCharacter_vtbl *v60; // edx
  Respawn *v61; // eax
  unsigned int v62; // edi
  unsigned int m_dwAdminCID; // ebx
  unsigned __int16 v64; // si
  Quest::QuestNode *QuestNode; // edi
  CGameClientDispatch *v66; // edi
  unsigned __int16 v67; // si
  CGameClientDispatch *v68; // edi
  unsigned __int16 v69; // si
  VirtualArea::CVirtualAreaMgr *v70; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  VirtualArea::CBGServerMap *v72; // esi
  char v73; // al
  VirtualArea::CVirtualAreaMgr *v74; // eax
  VirtualArea::CBGServerMap *v75; // eax
  VirtualArea::CBGServerMap *v76; // esi
  VirtualArea::CVirtualAreaMgr *v77; // eax
  VirtualArea::CBGServerMap *v78; // eax
  VirtualArea::CVirtualAreaMgr *v79; // eax
  VirtualArea::CBGServerMap *v80; // eax
  VirtualArea::CVirtualAreaMgr *v81; // eax
  VirtualArea::CBGServerMap *v82; // eax
  VirtualArea::CVirtualAreaMgr *v83; // eax
  VirtualArea::CBGServerMap *v84; // eax
  VirtualArea::CBGServerMap *v85; // esi
  VirtualArea::CVirtualAreaMgr *v86; // eax
  VirtualArea::CBGServerMap *v87; // eax
  bool v88; // zf
  CSingleDispatch *DispatchTable; // eax
  CSingleDispatch::Storage *p_StoragelpChatDispatch; // ecx
  CSingleDispatch *v91; // eax
  _BYTE v93[20]; // [esp+4h] [ebp-574h] BYREF
  unsigned __int8 cLockCount; // [esp+2Bh] [ebp-54Dh]
  unsigned int dwAdminCID; // [esp+2Ch] [ebp-54Ch]
  CCreatureManager::CProcessSecond<Respawn,std::pair<unsigned long const ,CCharacter *> > cClassType; // [esp+30h] [ebp-548h]
  Item::CUseItem *cPrevClassType; // [esp+34h] [ebp-544h] BYREF
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+38h] [ebp-540h] BYREF
  __int16 v99; // [esp+40h] [ebp-538h]
  __int16 v100; // [esp+42h] [ebp-536h]
  __int16 v101; // [esp+44h] [ebp-534h]
  char v102; // [esp+46h] [ebp-532h]
  CSingleDispatch::Storage v103; // [esp+48h] [ebp-530h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v104; // [esp+50h] [ebp-528h] BYREF
  Item::ItemInfo tempItemInfo; // [esp+60h] [ebp-518h] BYREF
  Item::ItemInfo itemInfo; // [esp+2E4h] [ebp-294h] BYREF
  int v107; // [esp+574h] [ebp-4h]

  HIWORD(v3) = HIWORD(lpAdmin);
  if ( lpAdmin )
    dwAdminCID = lpAdmin->m_dwCID;
  else
    dwAdminCID = lpPktAdmin->m_dwAdminCID;
  m_dwAmount = lpPktAdmin->m_dwAmount;
  p_m_Position = &lpPktAdmin->m_Position;
  switch ( lpPktAdmin->m_usCmd )
  {
    case 1u:
      m_cZone = (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cZone;
      *(_DWORD *)&v93[16] = (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cChannel;
      fPointX = p_m_Position->fPointX;
      *(_DWORD *)&v93[12] = m_cZone;
      *(float *)&v8 = lpPktAdmin->m_Position.fPointY;
      *(float *)v93 = fPointX;
      HIDWORD(v8) = LODWORD(lpPktAdmin->m_Position.fPointZ);
      *(_QWORD *)&v93[4] = v8;
      if ( !CCharacter::MoveZone(lpTarget, *(POS *)v93, v93[12], v93[16]) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          118,
          aCid0x08x_50,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 2u:
      v9 = lpTarget->GetParty(lpTarget);
      if ( v9 )
      {
        ((void (__thiscall *)(CParty *, _DWORD, _DWORD, _DWORD, _DWORD, _DWORD))v9->MoveZone)(
          v9,
          LODWORD(lpPktAdmin->m_Position.fPointX),
          LODWORD(lpPktAdmin->m_Position.fPointY),
          LODWORD(lpPktAdmin->m_Position.fPointZ),
          (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cZone,
          (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cChannel);
      }
      else
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          130,
          aCid0x08x_55,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 3u:
      v10 = p_m_Position->fPointX;
      *(_QWORD *)&v93[12] = (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cZone;
      *(float *)v93 = v10;
      *(_QWORD *)&v93[4] = *(_QWORD *)&lpPktAdmin->m_Position.fPointY;
      cClassType.m_fnSecondProcess = (Respawn *)v93;
      if ( !CCharacter::MovePos(lpTarget, *(Position *)v93, v93[12], v93[16]) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          143,
          aCid0x08x_186,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 4u:
      v11 = lpTarget->GetParty(lpTarget);
      if ( v11 )
      {
        v12 = v11->__vftable;
        v13 = (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cZone;
        v14 = p_m_Position->fPointX;
        *(_QWORD *)&v93[12] = (unsigned __int8)lpPktAdmin->m_ZoneInfo.m_cZone;
        ((void (__thiscall *)(CParty *, float, _DWORD, _DWORD, int, _DWORD))v12->MovePos)(
          v11,
          COERCE_FLOAT(LODWORD(v14)),
          LODWORD(lpPktAdmin->m_Position.fPointY),
          LODWORD(lpPktAdmin->m_Position.fPointZ),
          v13,
          0);
      }
      else
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          155,
          aCid0x08x_55,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 5u:
      memset(&v93[4], 0, 12);
      cClassType.m_fnSecondProcess = (Respawn *)&v93[4];
      if ( !CCharacter::Respawn(lpTarget, *(Position *)&v93[4], 1) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          168,
          aCid0x08x_244,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 7u:
      if ( !CCharacter::Kill(lpTarget, lpAdmin) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          180,
          aCid0x08x_264,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 9u:
      if ( !CCharacter::DuelInit(lpTarget, 8u) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          192,
          aCid0x08x_70,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0xAu:
      if ( CCharacter::DropItem(lpTarget, lpPktAdmin->m_usProtoTypeID, lpPktAdmin->m_dwAmount) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          208,
          aCid0x08x_173,
          lpTarget->m_dwCID,
          lpPktAdmin->m_usProtoTypeID,
          m_dwAmount);
      }
      else
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          203,
          aCid0x08x_331,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0xBu:
      if ( lpAdmin->m_CellPos.m_wMapIndex
        || (*(float *)&v93[8] = p_m_Position->fPointX,
            *(_QWORD *)&v93[12] = *(_QWORD *)&lpPktAdmin->m_Position.fPointY,
            m_usProtoTypeID = lpPktAdmin->m_usProtoTypeID,
            cClassType.m_fnSecondProcess = (Respawn *)&v93[8],
            *(_DWORD *)&v93[4] = m_usProtoTypeID,
            Instance = CCellManager::GetInstance(),
            CCellManager::AdminSummonMonster(Instance, *(int *)&v93[4], *(Position *)&v93[8])) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          240,
          aCid0x08x_164,
          dwAdminCID,
          lpPktAdmin->m_usProtoTypeID,
          p_m_Position->fPointX,
          lpPktAdmin->m_Position.fPointY,
          lpPktAdmin->m_Position.fPointZ);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          234,
          aCid0x08x_83,
          dwAdminCID);
      }
      break;
    case 0xCu:
      if ( !CCharacter::NotifyInfo(lpTarget, dwAdminCID) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          249,
          aCid0x08x_305,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0xDu:
      CCharacter::IncrementExp(lpTarget, lpPktAdmin->m_dwAmount);
      break;
    case 0xEu:
      Gold = lpTarget->m_DBData.m_Info.Gold;
      CCharacter::AddGold(lpTarget, m_dwAmount, 1);
      GAMELOG::LogTakeGold(lpTarget, Gold, lpTarget->m_DBData.m_Info.Gold, m_dwAmount, 2u, 2u, 6u, 0);
      break;
    case 0xFu:
      if ( m_dwAmount && m_dwAmount <= 0x63 && lpTarget->m_CreatureStatus.m_nLevel < m_dwAmount )
      {
        while ( CCharacter::IncrementExp(
                  lpTarget,
                  (unsigned int)(&szNoRemove_84)[2 * lpTarget->m_CreatureStatus.m_nLevel])
             && lpTarget->m_CreatureStatus.m_nLevel < m_dwAmount )
          ;
      }
      break;
    case 0x10u:
      v18 = lpTarget;
      StoragelpChatDispatch.m_lpDispatch = *(CPacketDispatch **)&lpTarget->m_PeaceMode.m_bPeace;
      v19 = *(unsigned int *)((char *)&lpTarget->m_PeaceMode.m_FileTime.dwLowDateTime + 3);
      dwHighDateTime_high = HIBYTE(lpTarget->m_PeaceMode.m_FileTime.dwHighDateTime);
      LOBYTE(StoragelpChatDispatch.m_lpDispatch) = 1;
      *(_DWORD *)&v93[4] = StoragelpChatDispatch.m_lpDispatch;
      *(_DWORD *)&v93[8] = v19;
      v93[12] = dwHighDateTime_high;
      CCharacter::SetPeaceMode(lpTarget, *(PeaceModeInfo *)&v93[4], 0);
      m_lpGameClientDispatch = lpTarget->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        *(_QWORD *)&v93[12] = 1LL;
        goto LABEL_42;
      }
      break;
    case 0x11u:
      v18 = lpTarget;
      StoragelpChatDispatch.m_lpDispatch = *(CPacketDispatch **)&lpTarget->m_PeaceMode.m_bPeace;
      v22 = *(unsigned int *)((char *)&lpTarget->m_PeaceMode.m_FileTime.dwLowDateTime + 3);
      v23 = HIBYTE(lpTarget->m_PeaceMode.m_FileTime.dwHighDateTime);
      LOBYTE(StoragelpChatDispatch.m_lpDispatch) = 0;
      *(_DWORD *)&v93[4] = StoragelpChatDispatch.m_lpDispatch;
      *(_DWORD *)&v93[8] = v22;
      v93[12] = v23;
      CCharacter::SetPeaceMode(lpTarget, *(PeaceModeInfo *)&v93[4], 0);
      m_lpGameClientDispatch = lpTarget->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
      {
        *(_QWORD *)&v93[12] = 0LL;
LABEL_42:
        GameClientSendPacket::SendCharPeaceMode(
          &m_lpGameClientDispatch->m_SendStream,
          v18->m_dwCID,
          0,
          v93[12],
          *(unsigned __int16 *)&v93[16]);
      }
      break;
    case 0x12u:
      CCharacter::SetFame(lpTarget, m_dwAmount);
      v24 = lpTarget->m_lpGameClientDispatch;
      if ( v24 )
        GameClientSendPacket::SendCharFameInfo(&v24->m_SendStream, lpTarget, szLoseCharName, szLoseCharName, 0);
      break;
    case 0x13u:
      v25 = lpTarget->m_lpGameClientDispatch;
      lpTarget->m_DBData.m_Info.Mileage = m_dwAmount;
      if ( v25 )
        GameClientSendPacket::SendCharFameInfo(&v25->m_SendStream, lpTarget, szLoseCharName, szLoseCharName, 0);
      break;
    case 0x14u:
      m_fPointY = lpTarget->m_CurrentPos.m_fPointY;
      m_fPointZ = lpTarget->m_CurrentPos.m_fPointZ;
      v28 = lpPktAdmin->m_ZoneInfo.m_cZone;
      StoragelpChatDispatch.m_lpDispatch = (CPacketDispatch *)LODWORD(lpTarget->m_CurrentPos.m_fPointX);
      *(float *)v93 = *(float *)&StoragelpChatDispatch.m_lpDispatch + 5.0;
      cClassType.m_fnSecondProcess = (Respawn *)v93;
      *(float *)&v93[4] = m_fPointY;
      *(float *)&v93[8] = m_fPointZ;
      if ( !CCharacter::MovePos(lpAdmin, *(Position *)v93, v28, 0) )
      {
        *(_DWORD *)&v93[16] = lpPktAdmin->m_stName;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          354,
          aCid0x08x_17,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0x15u:
      CAffectedSpell::ClearChant(&lpAdmin->m_SpellMgr.m_AffectedInfo);
      CAffectedSpell::ClearEnchant(&lpAdmin->m_SpellMgr.m_AffectedInfo);
      CCastingSpell::ClearChant(&lpAdmin->m_SpellMgr.m_CastingInfo);
      CCastingSpell::ClearEnchant(&lpAdmin->m_SpellMgr.m_CastingInfo);
      lpAdmin->m_dwStatusFlag = 0x40000000;
      break;
    case 0x16u:
      lpAdmin->m_dwStatusFlag &= ~0x40000000u;
      break;
    case 0x17u:
      StoragelpChatDispatch.m_lpDispatch = (CPacketDispatch *)&Skill::CProcessTable::ProcessInfo::m_NullProtoType;
      StoragelpChatDispatch.m_SingleDispatch = (CSingleDispatch *)lpAdmin;
      v99 = -1;
      v100 = 30;
      v101 = 1;
      v102 = 1;
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(
        &v104,
        (const CSpell::Spell_Info *)&StoragelpChatDispatch);
      Skill::CAddSpell<CInvincibleSpell>::operator()(v29, lpAdmin);
      break;
    case 0x18u:
      CAffectedSpell::RemoveEnchantBySpellType(&lpAdmin->m_SpellMgr.m_AffectedInfo, 0x20000000u);
      break;
    case 0x19u:
      v30 = lpTarget->m_lpGameClientDispatch;
      if ( v30 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          395,
          aCid0x08x_253,
          lpTarget->m_dwCID,
          dwAdminCID);
        CGameClientDispatch::Disconnect(v30);
        v30->Disconnected(v30);
      }
      break;
    case 0x1Au:
      m_lpCell = lpAdmin->m_CellPos.m_lpCell;
      if ( m_lpCell )
        CCell::KillAll(m_lpCell, lpAdmin);
      else
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          408,
          aCid0x08x_11,
          dwAdminCID);
      break;
    case 0x1Bu:
      v32 = lpTarget->m_CellPos.m_lpCell;
      if ( v32 )
      {
        v33 = lpTarget->__vftable;
        v34 = CSingleton<AwardTable::CAward>::ms_pSingleton;
        LOBYTE(cClassType.m_fnSecondProcess) = lpTarget->m_DBData.m_Info.Nationality;
        v35 = v33->GetClass(lpTarget);
        AwardEquipment = AwardTable::CAward::GetAwardEquipment(
                           v34,
                           lpPktAdmin->m_usProtoTypeID,
                           (char)cClassType.m_fnSecondProcess,
                           v35);
        CCell::SetItem(v32, 0, &lpTarget->m_CurrentPos, 0, lpTarget->m_dwCID, lpTarget->m_dwCID, AwardEquipment, 1u, 0);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          421,
          aCid0x08x_19,
          lpTarget->m_dwCID);
      }
      break;
    case 0x1Cu:
      v37 = CCreatureManager::GetInstance();
      EliteBonus::EliteBonusData::EliteBonusData(
        (EliteBonus::EliteBonusData *)&cPrevClassType,
        lpPktAdmin->m_ZoneInfo.m_cZone,
        lpPktAdmin->m_ZoneInfo.m_cChannel);
      if ( (unsigned __int8)cPrevClassType > 1u || BYTE1(cPrevClassType) > 0xAu )
      {
        v37->m_bAutoBalance = 1;
      }
      else
      {
        v37->m_bAutoBalance = 0;
        CCreatureManager::SetEliteBonus(v37, (EliteBonus::EliteBonusData)cPrevClassType);
      }
      break;
    case 0x1Du:
      v38 = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(v38) == 3 )
      {
        v39 = CCreatureManager::GetInstance();
        CCreatureManager::ProcessAllMonster<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *>>>(
          (int)v39,
          (CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CMonster,CAggresiveCreature *> >,std::pair<unsigned long const ,CMonster *> >) __thiscall `vcall'{64,{flat}},
          0);
      }
      break;
    case 0x1Eu:
      LOBYTE(cClassType.m_fnSecondProcess) = 0;
      v40 = CCreatureManager::GetInstance();
      CCreatureManager::ProcessAllCharacter<Respawn>((int)v40, cClassType);
      break;
    case 0x1Fu:
      if ( lpTarget->m_CreatureStatus.m_nLevel >= 10 )
      {
        v41 = lpTarget->__vftable;
        LOBYTE(cPrevClassType) = lpPktAdmin->m_ZoneInfo.m_cZone;
        if ( v41->GetClass(lpTarget) == (unsigned __int8)cPrevClassType )
        {
          *(_DWORD *)&v93[16] = (unsigned __int8)cPrevClassType;
          *(_DWORD *)&v93[12] = dwAdminCID;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            478,
            aCid0x08x_87,
            *(_QWORD *)&v93[12]);
        }
        else
        {
          Nationality = lpTarget->m_DBData.m_Info.Nationality;
          if ( Nationality == CClass::GetRace((unsigned __int8)cPrevClassType) )
          {
            v43 = lpTarget->GetClass(lpTarget);
            if ( CClass::GetJobLevel(v43) == 1
              && (v44 = CClass::GetPreviousJob((unsigned __int8)cPrevClassType), v44 == lpTarget->GetClass(lpTarget)) )
            {
              if ( !CCharacter::ChangeClass(lpTarget, (unsigned __int8)cPrevClassType) )
              {
                *(_DWORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID;
                *(_DWORD *)&v93[12] = dwAdminCID;
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "GameClientParsePacket::ProcessAdminCmd",
                  aDWorkRylSource_91,
                  496,
                  aCid0x08x_122,
                  *(_QWORD *)&v93[12]);
              }
            }
            else
            {
              PreviousJob = CClass::GetPreviousJob((unsigned __int8)cPrevClassType);
              m_nLevel = lpTarget->m_CreatureStatus.m_nLevel;
              LOBYTE(cClassType.m_fnSecondProcess) = PreviousJob;
              if ( CCharacter::InitLevel1Char(lpTarget, PreviousJob) )
              {
                if ( lpTarget->m_CreatureStatus.m_nLevel < m_nLevel )
                {
                  while ( CCharacter::IncrementExp(
                            lpTarget,
                            (unsigned int)(&szNoRemove_84)[2 * lpTarget->m_CreatureStatus.m_nLevel])
                       && lpTarget->m_CreatureStatus.m_nLevel < m_nLevel )
                    ;
                }
                if ( lpTarget->m_CreatureStatus.m_nLevel >= 10
                  && (_BYTE)cPrevClassType != LOBYTE(cClassType.m_fnSecondProcess)
                  && !CCharacter::ChangeClass(lpTarget, (unsigned __int8)cPrevClassType) )
                {
                  m_dwCID = lpTarget->m_dwCID;
                  *(_DWORD *)&v93[12] = ((unsigned __int16 (__thiscall *)(CCharacter *, _DWORD))lpTarget->GetClass)(
                                          lpTarget,
                                          (unsigned __int8)cPrevClassType);
                  CServerLog::DetailLog(
                    &g_Log,
                    LOG_ERROR,
                    "GameClientParsePacket::ProcessAdminCmd",
                    aDWorkRylSource_91,
                    533,
                    aCid0x08x_41,
                    __PAIR64__(m_dwCID, dwAdminCID),
                    *(_QWORD *)&v93[12]);
                }
              }
              else
              {
                *(_DWORD *)&v93[16] = lpTarget->m_dwCID;
                *(_DWORD *)&v93[12] = dwAdminCID;
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "GameClientParsePacket::ProcessAdminCmd",
                  aDWorkRylSource_91,
                  511,
                  aCid0x08x_295,
                  *(_QWORD *)&v93[12]);
              }
            }
          }
          else
          {
            *(_DWORD *)&v93[16] = (unsigned __int8)cPrevClassType;
            *(_DWORD *)&v93[12] = dwAdminCID;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              485,
              aCid0x08x_102,
              *(_QWORD *)&v93[12]);
          }
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          470,
          aCid0x08x_316,
          dwAdminCID);
      }
      break;
    case 0x20u:
      LOWORD(v3) = lpPktAdmin->m_usProtoTypeID;
      v48 = (unsigned __int64)p_m_Position->fPointX - 1;
      fPointY = (unsigned __int64)lpPktAdmin->m_Position.fPointY;
      cLockCount = fPointY;
      if ( v48 < ((__int16 (__fastcall *)(CCharacter *, _DWORD, int))lpTarget->GetSkillLockCount)(
                   lpTarget,
                   HIDWORD(fPointY),
                   v3)
        || v48 == lpTarget->GetSkillLockCount(lpTarget, v3) && cLockCount <= CCharacter::GetSkillLevel(lpTarget, v3) )
      {
        *(_DWORD *)&v93[16] = cLockCount;
        *(_DWORD *)&v93[12] = v48;
        *(_DWORD *)&v93[8] = (unsigned __int16)v3;
        *(_DWORD *)&v93[4] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          553,
          aCid0x08x_255,
          *(_QWORD *)&v93[4],
          *(_QWORD *)&v93[12]);
      }
      else
      {
        v50 = cLockCount + 6 * v48 + 1;
        if ( (unsigned __int16)lpTarget->GetSkillLockCount(lpTarget, v3) == 0xFFFF )
          v51 = 0;
        else
          v51 = lpTarget->GetSkillLockCount(lpTarget, v3);
        if ( CCharacter::GetSkillLevel(lpTarget, v3) == -1 )
          cLockCount = 0;
        else
          cLockCount = CCharacter::GetSkillLevel(lpTarget, v3);
        v52 = (Respawn *)(-1 - 6 * v51 - cLockCount + v50);
        Item::ItemInfo::ItemInfo(&tempItemInfo, v3);
        tempItemInfo.m_UseItemInfo.m_usSkill_ID = v3;
        tempItemInfo.m_UseItemInfo.m_usSkill_LockCount = v51;
        v53 = (Respawn *)operator new((tagHeader *)0x24);
        cClassType.m_fnSecondProcess = v53;
        v107 = 0;
        if ( v53 )
        {
          Item::CUseItem::CUseItem((Item::CUseItem *)v53, 0LL, &tempItemInfo);
          cPrevClassType = v54;
        }
        else
        {
          cPrevClassType = 0;
        }
        v107 = -1;
        if ( (int)v52 > 0 )
        {
          cClassType.m_fnSecondProcess = v52;
          do
          {
            if ( !CCharacter::SkillCreate(lpTarget, cPrevClassType) )
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "GameClientParsePacket::ProcessAdminCmd",
                aDWorkRylSource_91,
                573,
                aCid0x08x_255,
                dwAdminCID,
                (unsigned __int16)v3,
                v51,
                cLockCount);
            v55 = ++cLockCount;
            if ( v51 != 4 && v55 == 6 )
            {
              ++v51;
              cLockCount = 0;
              if ( cPrevClassType )
                ((void (__thiscall *)(Item::CUseItem *, int))cPrevClassType->~Item::CUseItem)(cPrevClassType, 1);
              Item::ItemInfo::ItemInfo(&itemInfo, v3);
              itemInfo.m_UseItemInfo.m_usSkill_ID = v3;
              itemInfo.m_UseItemInfo.m_usSkill_LockCount = v51;
              v56 = (CPacketDispatch *)operator new((tagHeader *)0x24);
              StoragelpChatDispatch.m_lpDispatch = v56;
              v107 = 1;
              if ( v56 )
                Item::CUseItem::CUseItem((Item::CUseItem *)v56, 0LL, &itemInfo);
              v107 = -1;
            }
            --cClassType.m_fnSecondProcess;
          }
          while ( cClassType.m_fnSecondProcess );
        }
        if ( cPrevClassType )
          ((void (__thiscall *)(Item::CUseItem *, int))cPrevClassType->~Item::CUseItem)(cPrevClassType, 1);
      }
      break;
    case 0x21u:
      LOWORD(v3) = lpPktAdmin->m_usProtoTypeID;
      v57 = lpPktAdmin->m_Position.fPointY;
      cLockCount = (unsigned __int64)p_m_Position->fPointX - 1;
      v58 = (unsigned __int64)v57;
      if ( cLockCount > ((__int16 (__fastcall *)(CCharacter *, _DWORD, int))lpTarget->GetSkillLockCount)(
                          lpTarget,
                          (unsigned __int64)v57 >> 32,
                          v3)
        || cLockCount == lpTarget->GetSkillLockCount(lpTarget, v3) && v58 >= CCharacter::GetSkillLevel(lpTarget, v3) )
      {
        *(_DWORD *)&v93[16] = v58;
        *(_DWORD *)&v93[12] = cLockCount;
        *(_DWORD *)&v93[8] = (unsigned __int16)v3;
        *(_DWORD *)&v93[4] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          604,
          aCid0x08x_190,
          *(_QWORD *)&v93[4],
          *(_QWORD *)&v93[12]);
      }
      else
      {
        SkillSlotIndex = CCharacter::GetSkillSlotIndex(lpTarget, v3);
        v60 = lpTarget->__vftable;
        LOBYTE(cPrevClassType) = SkillSlotIndex;
        cClassType.m_fnSecondProcess = (Respawn *)(6 * (v60->GetSkillLockCount(lpTarget, v3) - cLockCount) - v58);
        v61 = &cClassType.m_fnSecondProcess[CCharacter::GetSkillLevel(lpTarget, v3)];
        cClassType.m_fnSecondProcess = v61;
        if ( (int)v61 > 0 )
        {
          cClassType.m_fnSecondProcess = v61;
          do
          {
            if ( !CCharacter::SkillErase(lpTarget, (unsigned __int8)cPrevClassType) )
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "GameClientParsePacket::ProcessAdminCmd",
                aDWorkRylSource_91,
                617,
                aCid0x08x_190,
                dwAdminCID,
                (unsigned __int16)v3,
                cLockCount,
                v58);
            --v58;
            --cClassType.m_fnSecondProcess;
          }
          while ( cClassType.m_fnSecondProcess );
        }
      }
      break;
    case 0x22u:
      if ( m_dwAmount && m_dwAmount <= 0x63 )
      {
        LOBYTE(cClassType.m_fnSecondProcess) = lpTarget->GetClass(lpTarget);
        LOBYTE(cPrevClassType) = cClassType.m_fnSecondProcess;
        if ( CClass::GetJobLevel((unsigned __int8)cClassType.m_fnSecondProcess) == 2 )
          LOBYTE(cPrevClassType) = CClass::GetPreviousJob((unsigned __int8)cClassType.m_fnSecondProcess);
        if ( CCharacter::InitLevel1Char(lpTarget, (unsigned __int8)cPrevClassType) )
        {
          if ( lpTarget->m_CreatureStatus.m_nLevel < m_dwAmount )
          {
            while ( CCharacter::IncrementExp(
                      lpTarget,
                      (unsigned int)(&szNoRemove_84)[2 * lpTarget->m_CreatureStatus.m_nLevel])
                 && lpTarget->m_CreatureStatus.m_nLevel < m_dwAmount )
              ;
          }
          if ( lpTarget->m_CreatureStatus.m_nLevel >= 10
            && LOBYTE(cClassType.m_fnSecondProcess) != (_BYTE)cPrevClassType
            && !CCharacter::ChangeClass(lpTarget, (unsigned __int8)cClassType.m_fnSecondProcess) )
          {
            v62 = lpTarget->m_dwCID;
            *(_DWORD *)&v93[12] = ((unsigned __int16 (__thiscall *)(CCharacter *, _DWORD))lpTarget->GetClass)(
                                    lpTarget,
                                    LOBYTE(cClassType.m_fnSecondProcess));
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              672,
              aCid0x08x_41,
              __PAIR64__(v62, dwAdminCID),
              *(_QWORD *)&v93[12]);
          }
        }
        else
        {
          *(_DWORD *)&v93[16] = lpTarget->m_dwCID;
          *(_DWORD *)&v93[12] = dwAdminCID;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            650,
            aCid0x08x_295,
            *(_QWORD *)&v93[12]);
        }
      }
      break;
    case 0x23u:
      m_dwAdminCID = lpPktAdmin->m_dwAdminCID;
      v64 = lpPktAdmin->m_usProtoTypeID;
      QuestNode = CQuestMgr::GetQuestNode(CSingleton<CQuestMgr>::ms_pSingleton, v64);
      if ( QuestNode )
      {
        if ( CCharacter::HasQuest(lpAdmin, v64) == 1 )
        {
          *(_DWORD *)&v93[16] = v64;
          *(_DWORD *)&v93[12] = dwAdminCID;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            693,
            aCid0x08x_262,
            *(_QWORD *)&v93[12]);
        }
        else if ( CCharacter::GiveQuest(lpAdmin, QuestNode) )
        {
          v66 = lpAdmin->m_lpGameClientDispatch;
          if ( v66 )
            GameClientSendPacket::SendCharStartQuest(&v66->m_SendStream, dwAdminCID, m_dwAdminCID, v64, 0);
          if ( !CCharacter::StartPhase(lpAdmin, v64, 1) )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              713,
              aCid0x08x_273,
              dwAdminCID,
              v64);
            CCharacter::CancelQuest(lpAdmin, v64);
            GameClientSendPacket::SendCharCancelQuest(&v66->m_SendStream, dwAdminCID, v64, 0);
          }
        }
        else
        {
          *(_DWORD *)&v93[16] = v64;
          *(_DWORD *)&v93[12] = dwAdminCID;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            699,
            aCid0x08x_115,
            *(_QWORD *)&v93[12]);
        }
      }
      else
      {
        *(_DWORD *)&v93[16] = v64;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          687,
          aCid0x08x_216,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0x24u:
      v67 = lpPktAdmin->m_usProtoTypeID;
      if ( CQuestMgr::GetQuestNode(CSingleton<CQuestMgr>::ms_pSingleton, v67) )
      {
        v68 = lpAdmin->m_lpGameClientDispatch;
        if ( v68 )
        {
          if ( CCharacter::HasHistoryQuest(lpAdmin, v67) == 1 )
          {
            *(_DWORD *)&v93[16] = v67;
            *(_DWORD *)&v93[12] = dwAdminCID;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              745,
              aCid0x08x_167,
              *(_QWORD *)&v93[12]);
          }
          else
          {
            if ( CCharacter::HasExecutingQuest(lpAdmin, v67) == 1 )
            {
              CCharacter::CancelQuest(lpAdmin, v67);
              GameClientSendPacket::SendCharCancelQuest(&v68->m_SendStream, dwAdminCID, v67, 0);
            }
            if ( CCharacter::InsertHistoryQuest(lpAdmin, v67) )
              goto LABEL_167;
            *(_DWORD *)&v93[16] = v67;
            *(_DWORD *)&v93[12] = dwAdminCID;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              761,
              aCid0x08x_21,
              *(_QWORD *)&v93[12]);
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            738,
            aCid0x08x_187,
            dwAdminCID);
        }
      }
      else
      {
        *(_DWORD *)&v93[16] = v67;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          731,
          aCid0x08x_216,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0x25u:
      v69 = lpPktAdmin->m_usProtoTypeID;
      if ( CQuestMgr::GetQuestNode(CSingleton<CQuestMgr>::ms_pSingleton, v69) )
      {
        v68 = lpAdmin->m_lpGameClientDispatch;
        if ( v68 )
        {
          if ( CCharacter::HasQuest(lpAdmin, v69) )
          {
            if ( CCharacter::HasExecutingQuest(lpAdmin, v69) == 1 )
            {
              CCharacter::CancelQuest(lpTarget, v69);
              GameClientSendPacket::SendCharCancelQuest(&v68->m_SendStream, dwAdminCID, v69, 0);
            }
            if ( CCharacter::HasHistoryQuest(lpAdmin, v69) == 1 )
              CCharacter::DeleteHistoryQuest(lpAdmin, v69);
LABEL_167:
            GameClientSendPacket::SendCharQuestInfo(&v68->m_SendStream, lpAdmin);
          }
          else
          {
            *(_DWORD *)&v93[16] = v69;
            *(_DWORD *)&v93[12] = dwAdminCID;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ProcessAdminCmd",
              aDWorkRylSource_91,
              789,
              aCid0x08x_152,
              *(_QWORD *)&v93[12]);
          }
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessAdminCmd",
            aDWorkRylSource_91,
            783,
            aCid0x08x_187,
            dwAdminCID);
        }
      }
      else
      {
        *(_DWORD *)&v93[16] = v69;
        *(_DWORD *)&v93[12] = dwAdminCID;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ProcessAdminCmd",
          aDWorkRylSource_91,
          776,
          aCid0x08x_216,
          *(_QWORD *)&v93[12]);
      }
      break;
    case 0x26u:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v70 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v70, *(unsigned __int16 *)&v93[16]);
      v72 = VirtualArea;
      if ( VirtualArea )
      {
        v73 = VirtualArea::CBGServerMap::RuleCheck(VirtualArea, 1);
        *(_DWORD *)&v93[16] = &v72->m_ResultInfo;
        *(_DWORD *)&v93[12] = &v72->m_MapInfo.m_cLimitMin;
        *(_DWORD *)&v93[8] = v72->m_MapInfo.m_wScore;
        v72->m_ResultInfo.m_cWinNation = v73;
        std::copy<unsigned short *,unsigned short *>(
          *(unsigned __int8 **)&v93[8],
          *(unsigned __int16 **)&v93[12],
          *(unsigned __int8 **)&v93[16]);
        VirtualArea::CBGServerMap::SendResultInfo(v72);
      }
      break;
    case 0x27u:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v74 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v75 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v74, *(unsigned __int16 *)&v93[16]);
      v76 = v75;
      if ( v75 )
      {
        VirtualArea::CBGServerMap::RuleCheck(v75, 1);
        *(_DWORD *)&v93[16] = &v76->m_ResultInfo;
        *(_DWORD *)&v93[12] = &v76->m_MapInfo.m_cLimitMin;
        *(_DWORD *)&v93[8] = v76->m_MapInfo.m_wScore;
        v76->m_ResultInfo.m_cWinNation = 2;
        std::copy<unsigned short *,unsigned short *>(
          *(unsigned __int8 **)&v93[8],
          *(unsigned __int16 **)&v93[12],
          *(unsigned __int8 **)&v93[16]);
        VirtualArea::CBGServerMap::SendResultInfo(v76);
      }
      break;
    case 0x28u:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v77 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v78 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v77, *(unsigned __int16 *)&v93[16]);
      if ( v78 )
        VirtualArea::CBGServerMap::RuleCheck(v78, 1);
      break;
    case 0x29u:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v79 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v80 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v79, *(unsigned __int16 *)&v93[16]);
      if ( v80 )
      {
        v80->m_MapInfo.m_cMaxCharNumOfNation = m_dwAmount;
        VirtualArea::CBGServerMap::SendMapInfo(v80);
      }
      break;
    case 0x2Au:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v81 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v82 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v81, *(unsigned __int16 *)&v93[16]);
      if ( v82 )
      {
        v82->m_MapInfo.m_wTargetScore = m_dwAmount;
        VirtualArea::CBGServerMap::SendMapInfo(v82);
      }
      break;
    case 0x2Bu:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v83 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v84 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v83, *(unsigned __int16 *)&v93[16]);
      v85 = v84;
      if ( v84 )
      {
        v93[16] = m_dwAmount;
        v84->m_MapInfo.m_cLimitMin = m_dwAmount;
        v84->m_MapInfo.m_cRemainPlayMin = m_dwAmount;
        VirtualArea::CBGServerMap::ResetEnteringMin(v84, v93[16]);
        if ( v85->m_cStatus == 1 )
          v85->m_dwRemainTime = 60000 * (unsigned __int8)m_dwAmount + timeGetTime();
        goto LABEL_181;
      }
      break;
    case 0x2Cu:
      *(_WORD *)&v93[16] = lpPktAdmin->m_usProtoTypeID | 0x8000;
      v86 = VirtualArea::CVirtualAreaMgr::GetInstance();
      v87 = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v86, *(unsigned __int16 *)&v93[16]);
      v85 = v87;
      if ( v87 )
      {
        v88 = v87->m_cStatus == 2;
        v87->m_MapInfo.m_cRestMin = m_dwAmount;
        v87->m_MapInfo.m_cRemainRestMin = m_dwAmount;
        if ( v88 )
          v87->m_dwRemainTime = 60000 * (unsigned __int8)m_dwAmount + timeGetTime();
LABEL_181:
        VirtualArea::CBGServerMap::SendMapInfo(v85);
      }
      break;
    case 0x32u:
      DispatchTable = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
      v107 = 2;
      if ( StoragelpChatDispatch.m_lpDispatch )
        GameClientSendPacket::SendCharChatBan(
          (CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8],
          dwAdminCID,
          lpTarget->m_dwCID,
          m_dwAmount);
      p_StoragelpChatDispatch = &StoragelpChatDispatch;
      goto LABEL_192;
    case 0x33u:
      v91 = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&v103, v91);
      v107 = 3;
      if ( v103.m_lpDispatch )
        GameClientSendPacket::SendCharChatBan((CSendStream *)&v103.m_lpDispatch[8], dwAdminCID, lpTarget->m_dwCID, 0);
      p_StoragelpChatDispatch = &v103;
LABEL_192:
      v107 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)p_StoragelpChatDispatch);
      break;
    default:
      return 1;
  }
  return 1;
}
// 488001: variable 'v29' is possibly undefined
// 48843A: variable 'v54' is possibly undefined

//----- (00488D50) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharAdminCmd(CGameClientDispatch *GameClientDispatch, PktAdmin *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebp
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-14h] BYREF
  int v9; // [esp+14h] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x34 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      if ( m_lpCharacter->m_DBData.m_cAdminLevel )
      {
        Instance = CCreatureManager::GetInstance();
        Character = CCreatureManager::GetCharacter(Instance, lpPktBase->m_stName);
        if ( Character )
        {
          return GameClientParsePacket::ProcessAdminCmd(m_lpCharacter, Character, lpPktBase);
        }
        else
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharAdminCmd",
            aDWorkRylSource_91,
            76,
            aAdmin0x08x,
            m_lpCharacter->m_dwCID,
            lpPktBase->m_stName);
          DispatchTable = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
          v9 = 0;
          m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
          if ( StoragelpDBAgentDispatch.m_lpDispatch )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_DETAIL,
              "GameClientParsePacket::ParseCharAdminCmd",
              aDWorkRylSource_91,
              88,
              (char *)&byte_4F2CC4,
              m_lpCharacter->m_dwCID,
              lpPktBase->m_stName,
              lpPktBase->m_Cmd);
            lpPktBase->m_dwAdminCID = m_lpCharacter->m_dwCID;
            GameClientSendPacket::SendCharAdminCmdToDBAgent((CSendStream *)&m_lpDispatch[8], lpPktBase);
          }
          else
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ParseCharAdminCmd",
              aDWorkRylSource_91,
              83,
              (char *)&byte_4EE5AC);
          }
          v9 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          return 1;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharAdminCmd",
          aDWorkRylSource_91,
          67,
          aCid0x08x_36,
          m_lpCharacter->m_dwCID);
        return 1;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00488F20) --------------------------------------------------------
BOOL __thiscall CFriendList::Rebind::IsFriend(CFriendList::Rebind *this, char *szName)
{
  int v2; // eax

  strncmp((unsigned __int8 *)szName, (unsigned __int8 *)this->m_friendInfo.m_szName, 0x10u);
  return v2 == 0;
}
// 488F33: variable 'v2' is possibly undefined

//----- (00488F40) --------------------------------------------------------
char __thiscall CFriendList::Rebind::SetGroup(CFriendList::Rebind *this, unsigned int dwGroup)
{
  if ( dwGroup > 0xF )
    return 0;
  this->m_friendInfo.m_dwStatusFlag = (16 * dwGroup) | this->m_friendInfo.m_dwStatusFlag & 0xFFFFFF0F;
  return 1;
}

//----- (00488F70) --------------------------------------------------------
bool __cdecl GameClientParsePacket::ParseCharExchangeCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  bool result; // al
  unsigned int v3; // esi
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // ebx
  CCharacter *Character; // ebp
  CCharacter *v7; // eax
  CGameClientDispatch *m_lpGameClientDispatch; // ebx
  CGameClientDispatch *v9; // ebp
  Item::CExchangeContainer *p_m_Exchange; // ecx
  CSendStream *p_m_SendStream; // ebx
  CSendStream *v12; // ebp
  CSendStream *SendStream; // [esp+0h] [ebp-14h]
  CCharacter *lpSenderCharacter; // [esp+4h] [ebp-10h]
  CCharacter *lpCharacter; // [esp+8h] [ebp-Ch]
  CCharacter *lpRecverCharacter; // [esp+Ch] [ebp-8h]
  unsigned __int8 lpPktBasea; // [esp+1Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  SendStream = &GameClientDispatch->m_SendStream;
  m_CodePage = lpPktBase[1].m_CodePage;
  lpPktBasea = lpPktBase[1].m_SrvInfo.dwServerInfo;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v3);
  lpSenderCharacter = Character;
  v7 = CCreatureManager::GetCharacter(Instance, m_CodePage);
  lpRecverCharacter = v7;
  if ( !Character || !v7 )
  {
    GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 1u);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharExchangeCmd",
      aDWorkRylSource_57,
      63,
      aCid0x08x_338,
      m_CodePage);
    return 1;
  }
  m_lpGameClientDispatch = Character->m_lpGameClientDispatch;
  v9 = v7->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch || !v9 )
  {
    GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 1u);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharExchangeCmd",
      aDWorkRylSource_57,
      74,
      aCid0x08x_33,
      m_CodePage);
    return 1;
  }
  p_m_Exchange = &lpSenderCharacter->m_Exchange;
  p_m_SendStream = &m_lpGameClientDispatch->m_SendStream;
  v12 = &v9->m_SendStream;
  switch ( lpPktBasea )
  {
    case 0u:
      if ( lpSenderCharacter->m_Exchange.m_lpExchangeCharacter || v7->m_Exchange.m_lpExchangeCharacter )
      {
        GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 2u);
        return 1;
      }
      else if ( (*(_BYTE *)&v7->m_RejectOption.Reject & 2) != 0
             || CBanList::IsBan(&v7->m_banList, lpCharacter->m_dwCID, lpCharacter->m_DBData.m_Info.Name) )
      {
        GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 4u);
        return 1;
      }
      else
      {
LABEL_14:
        GameClientSendPacket::SendCharExchangeCmd(v12, v3, m_CodePage, lpPktBasea, 0);
        return 1;
      }
    case 1u:
      lpSenderCharacter->m_Exchange.m_lpExchangeCharacter = v7;
      v7->m_Exchange.m_lpExchangeCharacter = lpSenderCharacter;
      goto LABEL_33;
    case 2u:
      goto LABEL_14;
    case 3u:
      if ( lpSenderCharacter->m_Exchange.m_bLock && v7->m_Exchange.m_bLock )
      {
        lpSenderCharacter->m_Exchange.m_bAccept = 1;
        GameClientSendPacket::SendCharExchangeCmd(p_m_SendStream, v3, m_CodePage, lpPktBasea, 0);
        GameClientSendPacket::SendCharExchangeCmd(v12, v3, m_CodePage, lpPktBasea, 0);
        return 1;
      }
      GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 1u);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharExchangeCmd",
        aDWorkRylSource_57,
        170,
        aCid0x08x_149,
        v3);
      return 1;
    case 4u:
      Item::CExchangeContainer::ExchangeCancel(p_m_Exchange);
      return 1;
    case 5u:
      if ( v7->m_Exchange.m_bAccept )
      {
        lpSenderCharacter->m_Exchange.m_bAccept = 1;
        if ( !Item::CExchangeContainer::ExchangeOK(p_m_Exchange, 1) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharExchangeCmd",
            aDWorkRylSource_57,
            202,
            (char *)&byte_4F2E68,
            v3,
            m_CodePage);
LABEL_33:
        GameClientSendPacket::SendCharExchangeCmd(p_m_SendStream, v3, m_CodePage, lpPktBasea, 0);
        GameClientSendPacket::SendCharExchangeCmd(v12, v3, m_CodePage, lpPktBasea, 0);
        return 1;
      }
      else
      {
        GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 3u);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharExchangeCmd",
          aDWorkRylSource_57,
          194,
          (char *)&byte_4F2F78,
          v3,
          m_CodePage);
        return 1;
      }
    case 6u:
      if ( !Item::CExchangeContainer::ExchangeOK(p_m_Exchange, 0) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharExchangeCmd",
          aDWorkRylSource_57,
          215,
          (char *)&byte_4F2E20,
          v3,
          m_CodePage);
      goto LABEL_33;
    case 7u:
      lpSenderCharacter->m_Exchange.m_bLock = 1;
      goto LABEL_33;
    case 8u:
      if ( lpSenderCharacter->m_Exchange.m_bAccept )
      {
        GameClientSendPacket::SendCharExchangeCmd(SendStream, v3, m_CodePage, lpPktBasea, 1u);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharExchangeCmd",
          aDWorkRylSource_57,
          140,
          aCid0x08x_161,
          v3);
        return 1;
      }
      if ( v7->m_Exchange.m_bAccept )
      {
        v7->m_Exchange.m_bAccept = 0;
        GameClientSendPacket::SendCharExchangeCmd(p_m_SendStream, m_CodePage, v3, 4u, 0);
        GameClientSendPacket::SendCharExchangeCmd(v12, m_CodePage, v3, 4u, 0);
        lpRecverCharacter->m_Exchange.m_bLock = 0;
        GameClientSendPacket::SendCharExchangeCmd(p_m_SendStream, m_CodePage, v3, 8u, 0);
        GameClientSendPacket::SendCharExchangeCmd(v12, m_CodePage, v3, 8u, 0);
        p_m_Exchange = &lpSenderCharacter->m_Exchange;
      }
      p_m_Exchange->m_bLock = 0;
      GameClientSendPacket::SendCharExchangeCmd(p_m_SendStream, v3, m_CodePage, lpPktBasea, 0);
      GameClientSendPacket::SendCharExchangeCmd(v12, v3, m_CodePage, lpPktBasea, 0);
      result = 1;
      break;
    default:
      return 1;
  }
  return result;
}

//----- (004893C0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharStallOpen(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebx
  int v4; // ebp
  Item::CStallContainer *p_m_Stall; // ecx
  char strStallName[32]; // [esp+0h] [ebp-24h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x30 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      strncpy(strStallName, (const char *)&lpPktBase[1].m_CodePage, 0x20u);
      p_m_Stall = &m_lpCharacter->m_Stall;
      if ( !strcmp(strStallName, szLoseCharName) )
      {
        if ( !Item::CStallContainer::Close(p_m_Stall) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharStallOpen",
            aDWorkRylSource_57,
            1058,
            aCid0x08x_155,
            v4);
      }
      else if ( !Item::CStallContainer::Open(p_m_Stall, strStallName) )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharStallOpen",
          aDWorkRylSource_57,
          1065,
          aCid0x08x_208,
          v4);
      }
      return Item::CStallContainer::SendCharStallOpen(&m_lpCharacter->m_Stall, strStallName);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (004894D0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharStallRegisterItem(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebp
  Item::CItem *Item; // esi
  unsigned int m_CodePage; // edi
  unsigned __int8 dwServerInfo; // bl
  TakeType v7; // [esp-14h] [ebp-38h]
  TakeType v8; // [esp-10h] [ebp-34h]
  TakeType v9; // [esp-10h] [ebp-34h]
  __int64 v10; // [esp-8h] [ebp-2Ch]
  unsigned __int8 cCmd; // [esp+10h] [ebp-14h]
  unsigned int dwPrice; // [esp+14h] [ebp-10h]
  unsigned int dwCharID; // [esp+18h] [ebp-Ch]
  int takeType_2; // [esp+1Eh] [ebp-6h]
  unsigned __int16 lpPktBasea; // [esp+2Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1A )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  Item = 0;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
  cCmd = lpPktBase[2].m_Cmd;
  LOWORD(takeType_2) = HIWORD(m_CodePage);
  BYTE2(takeType_2) = dwServerInfo;
  dwCharID = *(_DWORD *)&lpPktBase[1].m_StartBit;
  dwPrice = *(unsigned int *)((char *)&lpPktBase[1].m_SrvInfo + 1);
  lpPktBasea = 0;
  if ( cCmd )
  {
    if ( cCmd != 1 )
    {
      if ( cCmd == 2 )
      {
        Item = CCharacter::GetItem(m_lpCharacter, takeType_2);
        if ( CCharacter::RemoveItem(m_lpCharacter, takeType_2) )
        {
          *(_DWORD *)&v8.m_srcPos = m_CodePage;
          v8.m_cNum = dwServerInfo;
          GAMELOG::LogStallRegisterRemoveItem(m_lpCharacter, Item, v8, cCmd, 0);
          return 1;
        }
        goto LABEL_17;
      }
      goto LABEL_18;
    }
    Item = CCharacter::GetItem(m_lpCharacter, m_CodePage);
    if ( Item )
    {
      Item->m_dwStallPrice = dwPrice;
      goto LABEL_18;
    }
LABEL_11:
    lpPktBasea = 2;
    goto LABEL_18;
  }
  Item = CCharacter::GetItem(m_lpCharacter, m_CodePage);
  if ( !Item )
    goto LABEL_11;
  if ( (BYTE2(m_CodePage) & 0xF) == 0xA )
  {
    LODWORD(v10) = m_CodePage;
    BYTE4(v10) = dwServerInfo;
    Item->m_dwStallPrice = dwPrice;
    if ( !CCharacter::MoveItem(m_lpCharacter, v10) )
    {
LABEL_17:
      lpPktBasea = 1;
      Item->m_dwStallPrice = 0;
    }
  }
  else
  {
    lpPktBasea = 1;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharStallRegisterItem",
      aDWorkRylSource_57,
      1104,
      aCid0x08x_8,
      m_lpCharacter->m_dwCID);
  }
LABEL_18:
  *(_DWORD *)&v9.m_srcPos = m_CodePage;
  v9.m_cNum = dwServerInfo;
  GAMELOG::LogStallRegisterRemoveItem(m_lpCharacter, Item, v9, cCmd, lpPktBasea);
  *(_DWORD *)&v7.m_srcPos = m_CodePage;
  v7.m_cNum = dwServerInfo;
  return GameClientSendPacket::SendCharStallRegisterItem(
           &GameClientDispatch->m_SendStream,
           dwCharID,
           v7,
           dwPrice,
           cCmd,
           lpPktBasea);
}
// 48956E: variable 'takeType_2' is possibly undefined
// 48962D: variable 'v10' is possibly undefined

//----- (004896A0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharStallEnter(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int v3; // ebx
  unsigned int m_CodePage; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // esi
  unsigned __int16 v7; // ax
  CCreatureManager *v8; // eax
  CCharacter *v9; // edi
  CCharacter *m_lpOtherOwner; // eax
  Item::CStallContainer *p_m_Stall; // edi

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x14 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  if ( !GameClientDispatch->m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v3);
  if ( Character )
  {
    v8 = CCreatureManager::GetInstance();
    v9 = CCreatureManager::GetCharacter(v8, m_CodePage);
    m_lpOtherOwner = Character->m_Stall.m_lpOtherOwner;
    if ( v9 )
    {
      if ( m_lpOtherOwner )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharStallEnter",
          aDWorkRylSource_57,
          1195,
          aCid0x08x_112,
          Character->m_dwCID,
          v9->m_dwCID,
          m_lpOtherOwner->m_dwCID);
        v7 = 3;
      }
      else
      {
        if ( Item::CStallContainer::Enter(&v9->m_Stall, Character) )
          return Item::CStallContainer::SendCharStallEnter(&v9->m_Stall, Character->m_dwCID, v9->m_dwCID);
        v7 = 2;
      }
    }
    else
    {
      if ( m_lpOtherOwner )
      {
        p_m_Stall = &m_lpOtherOwner->m_Stall;
        Item::CStallContainer::Leave(&m_lpOtherOwner->m_Stall, Character);
        return Item::CStallContainer::SendCharStallEnter(p_m_Stall, Character->m_dwCID, 0);
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharStallEnter",
        aDWorkRylSource_57,
        1208,
        aCid0x08x_217,
        Character->m_dwCID);
      v7 = 4;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharStallEnter",
      aDWorkRylSource_57,
      1171,
      (char *)&byte_4F3224);
    v7 = 1;
  }
  return GameClientSendPacket::SendCharStallEnter(&GameClientDispatch->m_SendStream, v3, m_CodePage, v7);
}

//----- (00489830) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharFriendRemove(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  int m_CodePage_low; // eax
  unsigned int m_dwCID; // ebp
  char *BanName; // eax
  CSingleDispatch *v8; // eax
  CPacketDispatch *v9; // ebx
  CSingleDispatch *v10; // eax
  CFriendList::Rebind *Friend; // eax
  char *m_szName; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebx
  CSingleDispatch *v15; // eax
  int v16; // [esp-18h] [ebp-64h]
  unsigned int v17; // [esp-10h] [ebp-5Ch]
  unsigned __int8 cAckCmd; // [esp+4h] [ebp-48h]
  CChatDispatch *lpChatDispatch; // [esp+8h] [ebp-44h]
  CChatDispatch *lpChatDispatcha; // [esp+8h] [ebp-44h]
  CSingleDispatch::Storage v21; // [esp+Ch] [ebp-40h] BYREF
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+14h] [ebp-38h] BYREF
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+1Ch] [ebp-30h] BYREF
  CSingleDispatch::Storage v24; // [esp+24h] [ebp-28h] BYREF
  char szRemovedName[16]; // [esp+2Ch] [ebp-20h] BYREF
  int v26; // [esp+48h] [ebp-4h]
  unsigned __int16 lpPktBasea; // [esp+54h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x11 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage_low = LOBYTE(lpPktBase[1].m_CodePage);
  m_dwCID = m_lpCharacter->m_dwCID;
  lpPktBasea = 0;
  cAckCmd = 0;
  *(_DWORD *)szRemovedName = 0;
  if ( !m_CodePage_low )
  {
    cAckCmd = 3;
    Friend = CFriendList::GetFriend(&m_lpCharacter->m_friendList, *(_DWORD *)&lpPktBase[1].m_StartBit);
    if ( Friend )
    {
      m_szName = Friend->m_friendInfo.m_szName;
      *(_DWORD *)szRemovedName = *(_DWORD *)m_szName;
      *(_DWORD *)&szRemovedName[4] = *((_DWORD *)m_szName + 1);
      *(_DWORD *)&szRemovedName[8] = *((_DWORD *)m_szName + 2);
      *(_DWORD *)&szRemovedName[12] = *((_DWORD *)m_szName + 3);
    }
    if ( CFriendList::Remove(&m_lpCharacter->m_friendList, *(_DWORD *)&lpPktBase[1].m_StartBit) )
    {
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
      m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
      v26 = 0;
      v15 = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, v15);
      LOBYTE(v26) = 1;
      lpChatDispatcha = (CChatDispatch *)StoragelpChatDispatch.m_lpDispatch;
      if ( m_lpDispatch && StoragelpChatDispatch.m_lpDispatch )
      {
        GameClientSendPacket::SendFriendListChangeToDB(
          (CSendStream *)&m_lpDispatch[8],
          m_lpCharacter->m_dwUID,
          m_dwCID,
          0,
          *(_DWORD *)&lpPktBase[1].m_StartBit,
          0,
          1u);
        GameClientSendPacket::SendFriendListChangeToDB(
          &lpChatDispatcha->m_SendStream,
          m_lpCharacter->m_dwUID,
          m_dwCID,
          0,
          *(_DWORD *)&lpPktBase[1].m_StartBit,
          0,
          1u);
      }
      LOBYTE(v26) = 0;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
      v26 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return GameClientSendPacket::SendCharFriendAck(
               &GameClientDispatch->m_SendStream,
               cAckCmd,
               *(_DWORD *)&lpPktBase[1].m_StartBit,
               szRemovedName,
               lpPktBasea);
    }
    v17 = m_dwCID;
    v16 = 1451;
LABEL_24:
    lpPktBasea = 6;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharFriendRemove",
      aDWorkRylSource_57,
      v16,
      aCid0x08x,
      v17);
    return GameClientSendPacket::SendCharFriendAck(
             &GameClientDispatch->m_SendStream,
             cAckCmd,
             *(_DWORD *)&lpPktBase[1].m_StartBit,
             szRemovedName,
             lpPktBasea);
  }
  if ( m_CodePage_low == 1 )
  {
    cAckCmd = 6;
    BanName = CBanList::GetBanName(&m_lpCharacter->m_banList, *(_DWORD *)&lpPktBase[1].m_StartBit);
    if ( BanName )
    {
      *(_DWORD *)szRemovedName = *(_DWORD *)BanName;
      *(_DWORD *)&szRemovedName[4] = *((_DWORD *)BanName + 1);
      *(_DWORD *)&szRemovedName[8] = *((_DWORD *)BanName + 2);
      *(_DWORD *)&szRemovedName[12] = *((_DWORD *)BanName + 3);
    }
    if ( CBanList::Remove(&m_lpCharacter->m_banList, *(_DWORD *)&lpPktBase[1].m_StartBit) )
    {
      v8 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&v21, v8);
      v9 = v21.m_lpDispatch;
      v26 = 2;
      v10 = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&v24, v10);
      LOBYTE(v26) = 3;
      lpChatDispatch = (CChatDispatch *)v24.m_lpDispatch;
      if ( v9 )
      {
        if ( v24.m_lpDispatch )
        {
          GameClientSendPacket::SendFriendListChangeToDB(
            (CSendStream *)&v9[8],
            m_lpCharacter->m_dwUID,
            m_dwCID,
            0,
            *(_DWORD *)&lpPktBase[1].m_StartBit,
            0,
            3u);
          GameClientSendPacket::SendFriendListChangeToDB(
            &lpChatDispatch->m_SendStream,
            m_lpCharacter->m_dwUID,
            m_dwCID,
            0,
            *(_DWORD *)&lpPktBase[1].m_StartBit,
            0,
            3u);
        }
      }
      LOBYTE(v26) = 2;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v24);
      v26 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v21);
      return GameClientSendPacket::SendCharFriendAck(
               &GameClientDispatch->m_SendStream,
               cAckCmd,
               *(_DWORD *)&lpPktBase[1].m_StartBit,
               szRemovedName,
               lpPktBasea);
    }
    v17 = m_dwCID;
    v16 = 1488;
    goto LABEL_24;
  }
  lpPktBasea = 2;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "GameClientParsePacket::ParseCharFriendRemove",
    aDWorkRylSource_57,
    1497,
    a0x08x,
    m_dwCID,
    m_CodePage_low);
  return GameClientSendPacket::SendCharFriendAck(
           &GameClientDispatch->m_SendStream,
           cAckCmd,
           *(_DWORD *)&lpPktBase[1].m_StartBit,
           szRemovedName,
           lpPktBasea);
}

//----- (00489B50) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharFriendEtc(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  CFriendList::Rebind *Friend; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebp
  CSingleDispatch *v7; // eax
  CPacketDispatch *v8; // ebx
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-1Ch] BYREF
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+Ch] [ebp-14h] BYREF
  int v11; // [esp+1Ch] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      if ( !LOBYTE(lpPktBase[1].m_SrvInfo.SrvState.wError) )
      {
        Friend = CFriendList::GetFriend(&m_lpCharacter->m_friendList, *(_DWORD *)&lpPktBase[1].m_StartBit);
        if ( Friend )
        {
          if ( CFriendList::Rebind::SetGroup(Friend, lpPktBase[1].m_CodePage) )
          {
            DispatchTable = CDBAgentDispatch::GetDispatchTable();
            CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
            m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
            v11 = 0;
            v7 = CChatDispatch::GetDispatchTable();
            CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, v7);
            v8 = StoragelpChatDispatch.m_lpDispatch;
            LOBYTE(v11) = 1;
            if ( m_lpDispatch )
            {
              if ( StoragelpChatDispatch.m_lpDispatch )
              {
                GameClientSendPacket::SendFriendListChangeToDB(
                  (CSendStream *)&m_lpDispatch[8],
                  m_lpCharacter->m_dwUID,
                  m_lpCharacter->m_dwCID,
                  0,
                  *(_DWORD *)&lpPktBase[1].m_StartBit,
                  lpPktBase[1].m_CodePage,
                  4u);
                GameClientSendPacket::SendFriendListChangeToDB(
                  (CSendStream *)&v8[8],
                  m_lpCharacter->m_dwUID,
                  m_lpCharacter->m_dwCID,
                  0,
                  *(_DWORD *)&lpPktBase[1].m_StartBit,
                  lpPktBase[1].m_CodePage,
                  4u);
              }
            }
            LOBYTE(v11) = 0;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
            v11 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
          }
        }
      }
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (00489CC0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharCreateGuild(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebp
  CCreatureManager *Instance; // eax
  unsigned __int16 v6; // di
  Guild::CGuildMgr *v7; // eax
  unsigned __int8 v8; // bl
  unsigned int v9; // edi
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // edi
  CSendStream *m_lpGameClientDispatch; // eax
  char Guild; // bl
  unsigned int v14; // [esp-10h] [ebp-28h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-14h] BYREF
  int v16; // [esp+14h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+20h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x20 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v14 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = CCreatureManager::GetInstance();
  if ( !CCreatureManager::GetCharacter(Instance, v14) )
  {
    v6 = 1;
    goto LABEL_17;
  }
  v7 = Guild::CGuildMgr::GetInstance();
  if ( Guild::CGuildMgr::GetGuild(v7, (char *)&lpPktBase[1].m_SrvInfo + 1) )
  {
    v6 = 2;
    goto LABEL_17;
  }
  v8 = 30;
  v9 = 1000000;
  if ( CServerSetup::GetInstance()->m_eNationType == CHINA )
  {
    v8 = 15;
    v9 = 100000;
  }
  if ( v8 > m_lpCharacter->m_CreatureStatus.m_nLevel )
  {
    v6 = 1;
    goto LABEL_17;
  }
  if ( v9 > m_lpCharacter->m_DBData.m_Info.Gold )
  {
    v6 = 1;
    goto LABEL_17;
  }
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  v16 = 0;
  m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
  if ( !StoragelpDBAgentDispatch.m_lpDispatch )
  {
    v6 = 1;
    v16 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
LABEL_17:
    GAMELOG::LogGuildCreate(0, 0, *(_DWORD *)&lpPktBase[1].m_StartBit, 0, v6);
    m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
    if ( m_lpGameClientDispatch )
      return GameClientSendPacket::SendCharCreateGuild(
               m_lpGameClientDispatch + 8,
               *(_DWORD *)&lpPktBase[1].m_StartBit,
               0,
               lpPktBase[1].m_SrvInfo.dwServerInfo,
               (char *)&lpPktBase[1].m_SrvInfo + 1,
               v6);
    else
      return 1;
  }
  GAMELOG::LogGuildCreate(0, 0, *(_DWORD *)&lpPktBase[1].m_StartBit, 0, 0);
  lpPktBasea = 0;
  if ( m_lpCharacter->m_DBData.m_Info.Nationality )
  {
    if ( m_lpCharacter->m_DBData.m_Info.Nationality == 1 )
      lpPktBasea = 105;
  }
  else
  {
    lpPktBasea = 101;
  }
  Guild = GameClientSendPacket::SendCharCreateGuild(
            (CSendStream *)&m_lpDispatch[8],
            *(_DWORD *)&lpPktBase[1].m_StartBit,
            0,
            lpPktBasea,
            (char *)&lpPktBase[1].m_SrvInfo + 1,
            0);
  v16 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  return Guild;
}



//----- (00489ED0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildCmd(CGameClientDispatch *GameClientDispatch, int lpPktBase)
{
  ServerInfo v3; // ebx
  unsigned int v4; // ebp
  unsigned int v5; // esi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // edi
  unsigned __int16 v8; // ax
  CSendStream *v9; // ecx
  CCreatureManager *v10; // eax
  CCharacter *Character; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned __int16 wCmd; // [esp+0h] [ebp-8h]
  CCharacter *lpCharacter; // [esp+4h] [ebp-4h]

  if ( (*(_WORD *)(lpPktBase + 2) & 0x3FFF) == 0x35 )
  {
    lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( lpCharacter )
    {
      v3 = *(ServerInfo *)(lpPktBase + 20);
      v4 = *(_DWORD *)(lpPktBase + 12);
      v5 = *(_DWORD *)(lpPktBase + 16);
      wCmd = *(_WORD *)(lpPktBase + 51);
      lpPktBase = 0;
      Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, v4);
      if ( Guild )
      {
        switch ( wCmd )
        {
          case 0u:
            Guild::CGuild::InviteMember(Guild, v3.dwServerInfo, v5, (unsigned __int16 *)&lpPktBase);
            goto LABEL_10;
          case 1u:
            Guild::CGuild::JoinMember(Guild, v5, 3u, (unsigned __int16 *)&lpPktBase);
            GAMELOG::LogGuildJoin(0, Guild->m_dwGID, v5, 3u, lpPktBase);
            goto LABEL_10;
          case 2u:
            v10 = CCreatureManager::GetInstance();
            Character = CCreatureManager::GetCharacter(v10, v3.dwServerInfo);
            if ( !Character )
              return 1;
            m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
            if ( !m_lpGameClientDispatch )
              goto LABEL_10;
            return GameClientSendPacket::SendCharGuildCmd(
                     m_lpGameClientDispatch + 8,
                     v4,
                     v5,
                     v3.dwServerInfo,
                     lpCharacter->m_DBData.m_Info.Name,
                     Guild->m_strName,
                     wCmd,
                     lpPktBase);
          case 3u:
            Guild::CGuild::JoinMember(Guild, v5, 5u, (unsigned __int16 *)&lpPktBase);
            GAMELOG::LogGuildJoin(0, Guild->m_dwGID, v5, 5u, lpPktBase);
            goto LABEL_10;
          case 4u:
            Guild::CGuild::SetTitle(Guild, v5, v5, 4u, (unsigned __int16 *)&lpPktBase);
            GAMELOG::LogGuildMemberLevelAdjust(0, Guild->m_dwGID, v5, v5, 4u, lpPktBase);
            GAMELOG::LogGuildLeave(0, Guild->m_dwGID, v5, v5, lpPktBase);
            goto LABEL_10;
          case 5u:
            Guild::CGuild::KickMember(Guild, v3.dwServerInfo, v5, (unsigned __int16 *)&lpPktBase);
            GAMELOG::LogGuildLeave(0, Guild->m_dwGID, v3.dwServerInfo, v5, lpPktBase);
            goto LABEL_10;
          case 6u:
            Guild::CGuild::SetTitle(Guild, lpCharacter->m_dwCID, v5, v3.dwServerInfo, (unsigned __int16 *)&lpPktBase);
            GAMELOG::LogGuildMemberLevelAdjust(0, Guild->m_dwGID, 0, v5, v3.dwServerInfo, lpPktBase);
LABEL_10:
            v8 = lpPktBase;
            if ( (_WORD)lpPktBase )
              goto LABEL_11;
            return 1;
          default:
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ParseCharGuildCmd",
              aDWorkRylSource_57,
              1820,
              aGid0x08x_13,
              v4,
              v5,
              v3.dwServerInfo,
              wCmd);
            goto LABEL_21;
        }
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharGuildCmd",
        aDWorkRylSource_57,
        1698,
        aGid0x08x_17,
        v4,
        v5,
        v3.dwServerInfo,
        wCmd);
LABEL_21:
      v8 = 1;
      lpPktBase = 1;
LABEL_11:
      v9 = (CSendStream *)lpCharacter->m_lpGameClientDispatch;
      if ( v9 )
        return GameClientSendPacket::SendCharGuildCmd(
                 v9 + 8,
                 v4,
                 v5,
                 v3.dwServerInfo,
                 &szSenderName,
                 &szSenderName,
                 wCmd,
                 v8);
      else
        return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, *(_BYTE *)(lpPktBase + 1));
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, *(_BYTE *)(lpPktBase + 1));
    return 0;
  }
}

//----- (0048A160) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildMark(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int v4; // ebp
  unsigned int m_CodePage; // edi
  CPartyMgr *Instance; // eax
  Guild::CGuild *Guild; // esi
  unsigned __int16 v8; // di
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int dwGID; // [esp+4h] [ebp-4h]
  CCharacter *lpPktBasea; // [esp+10h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1C9 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  lpPktBasea = GameClientDispatch->m_lpCharacter;
  if ( !lpPktBasea )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  dwGID = m_CodePage;
  Instance = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(Instance, m_CodePage);
  if ( Guild )
  {
    v8 = Guild::CGuild::SetMark(Guild, v4, (char *)&lpPktBase[1].m_SrvInfo);
    GAMELOG::LogGuildMarkChange(
      0,
      Guild->m_dwGID,
      v4,
      Guild->m_dwGold,
      Guild->m_dwGold,
      (const char *)&lpPktBase[1].m_SrvInfo,
      0x1B1u,
      v8);
    if ( !v8 )
      return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharGuildMark",
      aDWorkRylSource_57,
      1874,
      aCid0x08x_6,
      m_CodePage);
    v8 = 2;
  }
  m_lpGameClientDispatch = (CSendStream *)lpPktBasea->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharGuildMark(
             m_lpGameClientDispatch + 8,
             v4,
             dwGID,
             (char *)&lpPktBase[1].m_SrvInfo,
             0,
             v8);
  return 1;
}

//----- (0048A270) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildLevel(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned __int8 m_CodePage; // bl
  unsigned int v4; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned __int16 v7; // bp
  CPartyMgr *v8; // eax
  Guild::CGuild *Guild; // edi
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int v11; // [esp-14h] [ebp-18h]
  CCharacter *lpCharacter; // [esp+0h] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v4);
  if ( Character )
  {
    v11 = Character->GetGID(Character);
    v8 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v8, v11);
    if ( Guild )
    {
      v7 = Guild::CGuild::SetLevel(Guild, v4, m_CodePage) == 0;
      GAMELOG::LogGuildLevel(0, Guild->m_dwGID, v4, m_CodePage, Guild->m_dwGold, Guild->m_dwGold, 0);
      if ( !v7 )
        return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharGuildLevel",
        aDWorkRylSource_57,
        1940,
        aCid0x08x_193,
        v4);
      v7 = 1;
    }
  }
  else
  {
    v7 = 1;
  }
  m_lpGameClientDispatch = (CSendStream *)lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharGuildLevel(m_lpGameClientDispatch + 8, v4, m_CodePage, 0, v7);
  return 1;
}

//----- (0048A3A0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildRelation(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebp
  unsigned __int8 dwServerInfo; // bl
  unsigned int v5; // esi
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CPartyMgr *v9; // eax
  Guild::CGuild *Guild; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int v12; // [esp-14h] [ebp-28h]
  Guild::RelationInfo Info; // [esp+2h] [ebp-12h] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v5);
  if ( Character )
  {
    v12 = Character->GetGID(Character);
    v9 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v9, v12);
    if ( Guild )
    {
      Info.m_cRelation = 0;
      Info.m_cState = 0;
      if ( Guild::CGuild::SetRelation(Guild, m_CodePage, dwServerInfo, &Info) )
        return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharGuildRelation",
        aDWorkRylSource_57,
        2010,
        aCid0x08x_78,
        v5);
    }
  }
  m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharGuildRelation(m_lpGameClientDispatch + 8, v5, m_CodePage, dwServerInfo, 1u);
  return 1;
}

//----- (0048A4A0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildInclination(CGameClientDispatch *GameClientDispatch, int lpPktBase)
{
  CCharacter *m_lpCharacter; // ebx
  unsigned int v4; // esi
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned __int16 v7; // ax
  CPartyMgr *v8; // eax
  Guild::CGuild *Guild; // eax
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned int v11; // [esp-Ch] [ebp-10h]
  unsigned __int8 GameClientDispatcha; // [esp+8h] [ebp+4h]

  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, *(_BYTE *)(lpPktBase + 1));
    return 0;
  }
  v4 = *(_DWORD *)(lpPktBase + 12);
  GameClientDispatcha = *(_BYTE *)(lpPktBase + 16);
  lpPktBase = 0;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v4);
  if ( Character )
  {
    v11 = Character->GetGID(Character);
    v8 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v8, v11);
    if ( Guild )
    {
      Guild::CGuild::SetInclination(Guild, v4, GameClientDispatcha, (unsigned __int16 *)&lpPktBase);
      v7 = lpPktBase;
      if ( !(_WORD)lpPktBase )
        return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharGuildInclination",
        aDWorkRylSource_57,
        2074,
        aCid0x08x_320,
        v4);
      v7 = 1;
      lpPktBase = 1;
    }
  }
  else
  {
    v7 = 1;
    lpPktBase = 1;
  }
  m_lpGameClientDispatch = (CSendStream *)m_lpCharacter->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharGuildInclination(m_lpGameClientDispatch + 8, v4, GameClientDispatcha, v7);
  return 1;
}

//----- (0048A580) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildList(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  Guild::CGuildMgr *Instance; // eax
  unsigned int v4; // [esp-14h] [ebp-18h]
  unsigned __int8 m_CodePage; // [esp-10h] [ebp-14h]
  unsigned __int8 v6; // [esp-Ch] [ebp-10h]
  unsigned __int8 v7; // [esp-8h] [ebp-Ch]

  if ( GameClientDispatch->m_lpCharacter )
  {
    v7 = BYTE2(lpPktBase[1].m_CodePage);
    v6 = BYTE1(lpPktBase[1].m_CodePage);
    m_CodePage = lpPktBase[1].m_CodePage;
    v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    Instance = Guild::CGuildMgr::GetInstance();
    return Guild::CGuildMgr::SendGuildList(
             Instance,
             v4,
             m_CodePage,
             v6,
             v7,
             (GuildCheckSumNode *)&lpPktBase[1].m_SrvInfo);
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048A5E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildRight(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int v3; // ebx
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  unsigned int *p_m_CodePage; // esi
  char *v7; // edx
  int v8; // ecx
  CPartyMgr *v9; // eax
  Guild::CGuild *Guild; // eax
  Guild::CGuild *v11; // ebp
  unsigned __int16 v12; // si
  CGameClientDispatch *m_lpGameClientDispatch; // ebp
  GuildRight *p_guildRight; // ecx
  char *v15; // eax
  int v16; // edx
  _BYTE v17[56]; // [esp-38h] [ebp-18Ch] BYREF
  int wError; // [esp+10h] [ebp-144h]
  CCharacter *lpCharacter; // [esp+14h] [ebp-140h]
  GuildRight guildRight; // [esp+18h] [ebp-13Ch] BYREF
  char szTempBuf[260]; // [esp+4Ch] [ebp-108h] BYREF

  lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v3 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  qmemcpy(&guildRight, &lpPktBase[1].m_CodePage, 0x30u);
  *(_DWORD *)&v17[52] = v3;
  *(_WORD *)&guildRight.m_aryRight[48] = lpPktBase[5].m_CodePage;
  wError = 0;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v3);
  if ( Character )
  {
    p_m_CodePage = &lpPktBase[1].m_CodePage;
    v7 = szTempBuf;
    v8 = 50;
    do
    {
      *(_WORD *)v7 = Math::Convert::m_FastHeToBi[*(unsigned __int8 *)p_m_CodePage];
      v7[2] = 0;
      p_m_CodePage = (unsigned int *)((char *)p_m_CodePage + 1);
      v7 += 2;
      --v8;
    }
    while ( v8 );
    *(_DWORD *)&v17[52] = Character->GetGID(Character);
    v9 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
    Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v9, *(unsigned int *)&v17[52]);
    v11 = Guild;
    if ( Guild )
    {
      qmemcpy(&v17[4], &guildRight, 0x30u);
      *(_WORD *)&v17[52] = *(_WORD *)&guildRight.m_aryRight[48];
      if ( Guild::CGuild::SetRight(Guild, v3, *(GuildRight *)&v17[4]) )
      {
        v12 = wError;
      }
      else
      {
        v12 = 1;
        wError = 1;
      }
      GAMELOG::LogGuildRightsChange(0, v11->m_dwGID, v3, (const char *)&guildRight, 0x32u, v12);
      if ( !v12 )
        return 1;
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharGuildRight",
        aDWorkRylSource_57,
        2175,
        aCid0x08x_193,
        v3);
      wError = 1;
    }
  }
  else
  {
    wError = 1;
  }
  m_lpGameClientDispatch = lpCharacter->m_lpGameClientDispatch;
  if ( !m_lpGameClientDispatch )
    return 1;
  p_guildRight = &guildRight;
  v15 = szTempBuf;
  v16 = 50;
  do
  {
    *(_WORD *)v15 = Math::Convert::m_FastHeToBi[p_guildRight->m_aryRight[0]];
    v15[2] = 0;
    p_guildRight = (GuildRight *)((char *)p_guildRight + 1);
    v15 += 2;
    --v16;
  }
  while ( v16 );
  qmemcpy(v17, &guildRight, 0x30u);
  *(_WORD *)&v17[48] = *(_WORD *)&guildRight.m_aryRight[48];
  return GameClientSendPacket::SendCharGuildRight(&m_lpGameClientDispatch->m_SendStream, v3, *(GuildRight *)v17, wError);
}

//----- (0048A7C0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildMemberList(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v5; // esi
  CPartyMgr *v6; // eax
  Guild::CGuild *Guild; // eax
  unsigned int v8; // [esp-8h] [ebp-8h]
  unsigned int v9; // [esp-8h] [ebp-8h]
  unsigned __int8 GameClientDispatcha; // [esp+4h] [ebp+4h]
  unsigned __int8 cSortCmd; // [esp+8h] [ebp+8h]

  if ( GameClientDispatch->m_lpCharacter )
  {
    v8 = *(_DWORD *)&lpPktBase[1].m_StartBit;
    GameClientDispatcha = lpPktBase[1].m_CodePage;
    cSortCmd = BYTE1(lpPktBase[1].m_CodePage);
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, v8);
    v5 = Character;
    if ( Character )
    {
      v9 = Character->GetGID(Character);
      v6 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
      Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v6, v9);
      if ( Guild )
        Guild::CGuild::SendMemberList(Guild, v5, GameClientDispatcha, cSortCmd);
    }
    return 1;
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048A840) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharGuildSafe(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebx
  unsigned __int16 v4; // bp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // eax
  CCharacter *v7; // edi
  CPartyMgr *v8; // eax
  Guild::CGuild *Guild; // esi
  unsigned int v10; // eax
  unsigned int m_dwGold; // eax
  CSingleDispatch *DispatchTable; // eax
  char v13; // bl
  CSendStream *m_lpGameClientDispatch; // eax
  unsigned int v15; // [esp-14h] [ebp-30h]
  unsigned int v16; // [esp-14h] [ebp-30h]
  unsigned int dwCID; // [esp+0h] [ebp-1Ch]
  unsigned int dwCharGold; // [esp+4h] [ebp-18h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+8h] [ebp-14h] BYREF
  int v20; // [esp+18h] [ebp-4h]
  unsigned __int8 GameClientDispatcha; // [esp+20h] [ebp+4h]
  unsigned int cCmd; // [esp+24h] [ebp+8h]

  if ( !GameClientDispatch->m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  v15 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  dwCID = v15;
  GameClientDispatcha = lpPktBase[1].m_SrvInfo.dwServerInfo;
  v4 = 0;
  Instance = CCreatureManager::GetInstance();
  Character = CCreatureManager::GetCharacter(Instance, v15);
  v7 = Character;
  if ( !Character )
    return 1;
  dwCharGold = Character->m_DBData.m_Info.Gold;
  v16 = Character->GetGID(Character);
  v8 = (CPartyMgr *)Guild::CGuildMgr::GetInstance();
  Guild = (Guild::CGuild *)Guild::CGuildMgr::GetGuild(v8, v16);
  if ( !Guild )
  {
    v4 = 1;
    goto LABEL_25;
  }
  cCmd = 0;
  if ( GameClientDispatcha )
  {
    if ( GameClientDispatcha != 1 )
    {
      if ( GameClientDispatcha == 2 && Guild::CGuild::GetTitle(Guild, v7->m_dwCID) > Guild->m_GuildRight.m_aryRight[2] )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharGuildSafe",
          aDWorkRylSource_57,
          2339,
          aCid0x08x_108,
          v7->m_dwCID,
          Guild->m_dwGID);
LABEL_15:
        v4 = 1;
        goto LABEL_21;
      }
      goto LABEL_21;
    }
    if ( m_CodePage > dwCharGold )
      goto LABEL_15;
    v10 = m_CodePage + Guild->m_dwGold;
LABEL_20:
    cCmd = v10;
    goto LABEL_21;
  }
  if ( Guild::CGuild::GetTitle(Guild, v7->m_dwCID) > Guild->m_GuildRight.m_aryRight[2] )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharGuildSafe",
      aDWorkRylSource_57,
      2295,
      aCid0x08x_212,
      v7->m_dwCID,
      Guild->m_dwGID);
    goto LABEL_15;
  }
  if ( m_CodePage + dwCharGold == -1 )
  {
    v4 = 1;
    goto LABEL_21;
  }
  m_dwGold = Guild->m_dwGold;
  if ( m_CodePage < m_dwGold )
  {
    v10 = m_dwGold - m_CodePage;
    goto LABEL_20;
  }
LABEL_21:
  GAMELOG::LogGuildStoreGoldChange(0, Guild->m_dwGID, dwCID, Guild->m_dwGold, cCmd, v4);
  if ( !v4 )
  {
    DispatchTable = CDBAgentDispatch::GetDispatchTable();
    CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
    v20 = 0;
    if ( StoragelpDBAgentDispatch.m_lpDispatch )
    {
      v13 = GameClientSendPacket::SendCharGuildSafe(
              (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
              dwCID,
              Guild->m_dwGID,
              0,
              m_CodePage,
              GameClientDispatcha,
              v7->m_DBData.m_Info.Name,
              0);
      v20 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      return v13;
    }
    v20 = -1;
    CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
  }
LABEL_25:
  m_lpGameClientDispatch = (CSendStream *)v7->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
    return GameClientSendPacket::SendCharGuildSafe(
             m_lpGameClientDispatch + 8,
             dwCID,
             0,
             0,
             m_CodePage,
             GameClientDispatcha,
             v7->m_DBData.m_Info.Name,
             v4);
  return 1;
}

//----- (0048AAA0) --------------------------------------------------------
CFriendList::Rebind *__thiscall CFriendList::GetFriendNum(CFriendList *this)
{
  CFriendList::Rebind *result; // eax

  result = this->m_friendList._Myfirst;
  if ( result )
    return (CFriendList::Rebind *)(this->m_friendList._Mylast - result);
  return result;
}

//----- (0048AAD0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharPartyCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *v3; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *m_CodePage; // ebx
  unsigned __int16 v5; // cx
  CPacketDispatch *dwServerInfo; // edi
  unsigned __int16 v7; // bp
  CCharacter *m_lpCharacter; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *m_dwCID; // eax
  const char *v10; // ecx
  CSendStream *p_m_SendStream; // eax
  CCreatureManager *Instance; // eax
  unsigned int v13; // eax
  const void *v14; // eax
  CCharacter *v15; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v16; // eax
  CCreatureManager *v17; // eax
  CCharacter *Character; // eax
  CCharacter *v19; // edi
  CSingleDispatch *v20; // eax
  CSingleDispatch::Storage *p_lpParty; // ecx
  CSingleDispatch *DispatchTable; // eax
  CServerSetup *v23; // eax
  unsigned int ServerID; // eax
  CSingleDispatch *v25; // eax
  CCharacter *v26; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *v27; // eax
  CCreatureManager *v28; // eax
  CCharacter *v29; // eax
  const void *v30; // eax
  CCharacter *v31; // ecx
  CParty *v32; // eax
  CParty *v33; // esi
  CSingleDispatch *v34; // eax
  CCharacter *v35; // esi
  CSingleDispatch *v36; // eax
  CCharacter *v37; // ecx
  CParty *v38; // eax
  CParty *v39; // esi
  CSingleDispatch *v40; // eax
  CCharacter *v41; // ecx
  int v42; // eax
  int v43; // esi
  CSingleDispatch *v44; // eax
  CCharacter *v45; // esi
  int v46; // eax
  CCharacter *v47; // esi
  CSingleDispatch *v48; // eax
  CSendStream *v49; // eax
  int v50; // [esp-20h] [ebp-D0h]
  int v51; // [esp-1Ch] [ebp-CCh]
  int v52; // [esp-1Ch] [ebp-CCh]
  char *v53; // [esp-1Ch] [ebp-CCh]
  int v54; // [esp-1Ch] [ebp-CCh]
  char *v55; // [esp-18h] [ebp-C8h]
  char *v56; // [esp-18h] [ebp-C8h]
  unsigned int v57; // [esp-18h] [ebp-C8h]
  char *v58; // [esp-18h] [ebp-C8h]
  unsigned int m_dwUID; // [esp-14h] [ebp-C4h]
  unsigned int v60; // [esp-14h] [ebp-C4h]
  void *v61; // [esp-14h] [ebp-C4h]
  unsigned int v62; // [esp-14h] [ebp-C4h]
  CParty *lpSendCharacter; // [esp+0h] [ebp-B0h]
  CParty *lpSendCharactera; // [esp+0h] [ebp-B0h]
  CParty *lpSendCharacterb; // [esp+0h] [ebp-B0h]
  CSendStream *lpSendCharactere; // [esp+0h] [ebp-B0h]
  CParty *lpSendCharacterc; // [esp+0h] [ebp-B0h]
  CParty *lpSendCharacterd; // [esp+0h] [ebp-B0h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node *dwPartyID; // [esp+4h] [ebp-ACh]
  unsigned __int16 usCmd; // [esp+8h] [ebp-A8h]
  CSendStream *SendStream; // [esp+Ch] [ebp-A4h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+10h] [ebp-A0h] BYREF
  CSingleDispatch::Storage dwReferenceID; // [esp+18h] [ebp-98h] BYREF
  CSingleDispatch::Storage lpParty; // [esp+20h] [ebp-90h] BYREF
  unsigned int Name; // [esp+28h] [ebp-88h]
  CSingleDispatch::Storage v76; // [esp+2Ch] [ebp-84h] BYREF
  AddressInfo v77; // [esp+34h] [ebp-7Ch] BYREF
  AddressInfo SenderAddress; // [esp+58h] [ebp-58h] BYREF
  AddressInfo v79; // [esp+7Ch] [ebp-34h] BYREF
  int v80; // [esp+ACh] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFFu) < 0x1A )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharPartyCmd",
      aDWorkRylSource_57,
      235,
      (char *)&byte_4F3A50);
    return 0;
  }
  v3 = *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CParty *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CParty *> >,0> >::_Node **)&lpPktBase[1].m_StartBit;
  m_CodePage = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)lpPktBase[1].m_CodePage;
  v5 = *(_WORD *)&lpPktBase[2].m_StartBit;
  dwServerInfo = (CPacketDispatch *)lpPktBase[1].m_SrvInfo.dwServerInfo;
  SendStream = &GameClientDispatch->m_SendStream;
  memset(&SenderAddress, 0, sizeof(SenderAddress));
  usCmd = v5;
  v7 = 0;
  dwPartyID = v3;
  dwReferenceID.m_lpDispatch = dwServerInfo;
  switch ( v5 )
  {
    case 0u:
      m_lpCharacter = GameClientDispatch->m_lpCharacter;
      if ( !m_lpCharacter )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          264,
          aUid0x08x,
          GameClientDispatch->m_dwUID);
LABEL_8:
        v10 = &szSenderName_In;
        goto LABEL_9;
      }
      m_dwCID = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)m_lpCharacter->m_dwCID;
      if ( m_CodePage != m_dwCID )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          269,
          aCid0x08x_51,
          m_dwCID,
          m_CodePage);
        goto LABEL_8;
      }
      Instance = CCreatureManager::GetInstance();
      lpSendCharacter = (CParty *)CCreatureManager::GetCharacter(Instance, (unsigned int)dwServerInfo);
      if ( !lpSendCharacter )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          277,
          aCid0x08x_338,
          dwServerInfo);
        goto LABEL_8;
      }
      if ( (lpSendCharacter[4].m_Party.ServerID[1] & 1) != 0
        || (v13 = m_lpCharacter->m_dwCID,
            Name = (unsigned int)m_lpCharacter->m_DBData.m_Info.Name,
            CBanList::IsBan(
              (CBanList *)&lpSendCharacter[2].m_Party.Name[7][3],
              v13,
              m_lpCharacter->m_DBData.m_Info.Name)) )
      {
        v7 = 9;
        goto LABEL_8;
      }
      if ( ((int (__thiscall *)(CParty *))lpSendCharacter->Login)(lpSendCharacter) )
      {
        v7 = 2;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          288,
          aCid0x08x_156,
          dwServerInfo);
        goto LABEL_8;
      }
      lpSendCharactera = *(CParty **)((char *)&lpSendCharacter[4].m_Party.ServerID[6] + 3);
      if ( !lpSendCharactera )
      {
        v7 = 8;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          296,
          aCid0x08x_312,
          dwServerInfo);
        goto LABEL_8;
      }
      AddressInfo::AddressInfo(
        &v79,
        &m_lpCharacter->m_PublicAddress,
        &m_lpCharacter->m_PrivateAddress,
        m_lpCharacter->m_dwCID);
      qmemcpy(&SenderAddress, v14, sizeof(SenderAddress));
      v10 = (const char *)Name;
      p_m_SendStream = (CSendStream *)&lpSendCharactera->m_Party.Name[0][3];
      goto LABEL_10;
    case 1u:
      v15 = GameClientDispatch->m_lpCharacter;
      if ( !v15 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 318;
        goto LABEL_111;
      }
      v16 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v15->m_dwCID;
      if ( m_CodePage != v16 )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          324,
          aCid0x08x_51,
          v16,
          m_CodePage);
        goto LABEL_137;
      }
      if ( v15->GetParty(v15) )
      {
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_140;
        v51 = 329;
        goto LABEL_111;
      }
      v17 = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(v17, (unsigned int)dwReferenceID.m_lpDispatch);
      v19 = Character;
      if ( !Character )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          337,
          aCid0x08x_338,
          dwReferenceID.m_lpDispatch);
LABEL_44:
        dwServerInfo = dwReferenceID.m_lpDispatch;
        goto LABEL_137;
      }
      lpSendCharacterb = Character->GetParty(Character);
      if ( lpSendCharacterb )
      {
        if ( lpSendCharacterb->m_Party.m_cMemberNum == 10 )
        {
          v7 = 7;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            366,
            (char *)&byte_4F391C);
          goto LABEL_41;
        }
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&v76, DispatchTable);
        v80 = 1;
        lpParty.m_lpDispatch = v76.m_lpDispatch;
        if ( v76.m_lpDispatch )
        {
          Name = lpSendCharacterb->m_Party.m_dwPartyID;
          v23 = CServerSetup::GetInstance();
          ServerID = CServerSetup::GetServerID(v23);
          GameClientSendPacket::SendPartyMemberData(
            (CSendStream *)&lpParty.m_lpDispatch[8],
            Name,
            (unsigned int)m_CodePage,
            ServerID,
            v15->m_DBData.m_Info.Name,
            0x34u);
        }
        else
        {
          v7 = 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            376,
            (char *)&byte_4EE5AC);
        }
        p_lpParty = &v76;
      }
      else
      {
        v20 = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&lpParty, v20);
        v80 = 0;
        if ( lpParty.m_lpDispatch )
        {
          GameClientSendPacket::SendCharPartyCreateToDBAgent(
            (CSendStream *)&lpParty.m_lpDispatch[8],
            (unsigned int)dwReferenceID.m_lpDispatch,
            (unsigned int)m_CodePage);
        }
        else
        {
          v7 = 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            352,
            (char *)&byte_4EE5AC);
        }
        p_lpParty = &lpParty;
      }
      v80 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)p_lpParty);
LABEL_41:
      v25 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v25);
      v80 = 2;
      lpParty.m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          392,
          (char *)&byte_500E18,
          v15->m_DBData.m_Info.Party,
          v15->m_dwCID);
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          393,
          (char *)&byte_500E18,
          v15->m_DBData.m_Info.Party,
          v19->m_dwCID);
        lpSendCharactere = (CSendStream *)&lpParty.m_lpDispatch[8];
        GameClientSendPacket::SendPartyAddress(
          (CSendStream *)&lpParty.m_lpDispatch[8],
          v15->m_DBData.m_Info.Party,
          v15->m_dwCID,
          &v15->m_PublicAddress,
          &v15->m_PrivateAddress,
          0x3Bu);
        GameClientSendPacket::SendPartyAddress(
          lpSendCharactere,
          v15->m_DBData.m_Info.Party,
          v19->m_dwCID,
          &v19->m_PublicAddress,
          &v19->m_PrivateAddress,
          0x3Bu);
      }
      v80 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      if ( v7 )
        goto LABEL_44;
      return 1;
    case 2u:
      v26 = GameClientDispatch->m_lpCharacter;
      lpSendCharacterc = (CParty *)v26;
      if ( !v26 )
      {
        v60 = GameClientDispatch->m_dwUID;
        v56 = aUid0x08x;
        v52 = 421;
LABEL_53:
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          v52,
          v56,
          v60);
        v10 = 0;
        goto LABEL_9;
      }
      v27 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v26->m_dwCID;
      if ( m_CodePage != v27 )
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          426,
          aCid0x08x_51,
          v27,
          m_CodePage);
        v10 = 0;
LABEL_9:
        p_m_SendStream = &GameClientDispatch->m_SendStream;
        goto LABEL_10;
      }
      v28 = CCreatureManager::GetInstance();
      v29 = CCreatureManager::GetCharacter(v28, (unsigned int)dwServerInfo);
      if ( !v29 )
      {
        v60 = (unsigned int)dwServerInfo;
        v56 = aCid0x08x_338;
        v52 = 434;
        goto LABEL_53;
      }
      lpParty.m_lpDispatch = v29->m_lpGameClientDispatch;
      if ( !lpParty.m_lpDispatch )
      {
        v60 = (unsigned int)dwServerInfo;
        v56 = aCid0x08x_33;
        v52 = 443;
        goto LABEL_53;
      }
      AddressInfo::AddressInfo(
        &v77,
        (const sockaddr_in *)&lpSendCharacterc[1].m_Party.Name[3][11],
        (const sockaddr_in *)&lpSendCharacterc[1].m_Party.Name[4][11],
        (unsigned int)lpSendCharacterc->m_PartySpellMgr.m_pPartyMember[6]);
      qmemcpy(&SenderAddress, v30, sizeof(SenderAddress));
      v10 = (char *)&lpSendCharacterc[3].m_Party.ServerID[7] + 3;
      p_m_SendStream = (CSendStream *)&lpParty.m_lpDispatch[8];
LABEL_10:
      GameClientSendPacket::SendCharPartyCmd(
        p_m_SendStream,
        (unsigned int)dwPartyID,
        &SenderAddress,
        v10,
        (unsigned int)m_CodePage,
        (unsigned int)dwReferenceID.m_lpDispatch,
        usCmd,
        v7);
      return 1;
    case 3u:
      v31 = GameClientDispatch->m_lpCharacter;
      if ( !v31 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 465;
        goto LABEL_111;
      }
      if ( dwServerInfo != (CPacketDispatch *)v31->m_dwCID )
      {
        v61 = dwServerInfo;
        v57 = v31->m_dwCID;
        v53 = aCid0x08x_51;
        v50 = 470;
        goto LABEL_65;
      }
      v32 = v31->GetParty(v31);
      v33 = v32;
      if ( !v32 )
      {
        m_dwUID = (unsigned int)dwServerInfo;
        v55 = aCid0x08x_160;
        v51 = 478;
        goto LABEL_111;
      }
      if ( !CParty::IsMember(v32, (unsigned int)m_CodePage) )
      {
        v61 = m_CodePage;
        v57 = (unsigned int)dwServerInfo;
        v53 = aCid0x08x_201;
        v50 = 485;
        goto LABEL_65;
      }
      if ( (CPacketDispatch *)v33->m_Party.m_dwLeaderID != dwServerInfo )
      {
        v61 = m_CodePage;
        v57 = (unsigned int)dwServerInfo;
        v53 = aCid0x08x_213;
        v50 = 492;
        goto LABEL_65;
      }
      v34 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v34);
      v80 = 3;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        GameClientSendPacket::SendPartyMemberData(
          (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
          v33->m_Party.m_dwPartyID,
          (unsigned int)m_CodePage,
          (unsigned int)dwServerInfo,
          &strSenderName,
          0x35u);
      }
      else
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          502,
          (char *)&byte_4EE5AC);
      }
      v80 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
      if ( v7 )
        goto LABEL_137;
      return 1;
    case 4u:
      v35 = GameClientDispatch->m_lpCharacter;
      if ( !v35 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 530;
        goto LABEL_111;
      }
      if ( m_CodePage != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v35->m_dwCID )
      {
        v61 = m_CodePage;
        v57 = v35->m_dwCID;
        v53 = aCid0x08x_51;
        v50 = 535;
        goto LABEL_65;
      }
      lpParty.m_lpDispatch = (CPacketDispatch *)v35->GetParty(v35);
      if ( !lpParty.m_lpDispatch )
      {
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_160;
        v51 = 543;
        goto LABEL_111;
      }
      v36 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v36);
      v80 = 4;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        GameClientSendPacket::SendPartyMemberData(
          (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
          (unsigned int)lpParty.m_lpDispatch[6].m_Session,
          (unsigned int)m_CodePage,
          (unsigned int)m_CodePage,
          v35->m_DBData.m_Info.Name,
          0x35u);
      }
      else
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          553,
          (char *)&byte_4EE5AC);
      }
      goto LABEL_102;
    case 5u:
      v37 = GameClientDispatch->m_lpCharacter;
      if ( !v37 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 578;
        goto LABEL_111;
      }
      if ( dwServerInfo != (CPacketDispatch *)v37->m_dwCID )
      {
        v61 = dwServerInfo;
        v57 = v37->m_dwCID;
        v53 = aCid0x08x_51;
        v50 = 583;
        goto LABEL_65;
      }
      v38 = v37->GetParty(v37);
      v39 = v38;
      if ( !v38 )
      {
        m_dwUID = (unsigned int)dwServerInfo;
        v55 = aCid0x08x_160;
        v51 = 591;
        goto LABEL_111;
      }
      if ( !CParty::IsMember(v38, (unsigned int)m_CodePage)
        || (CPacketDispatch *)v39->m_Party.m_dwLeaderID != dwServerInfo )
      {
        v61 = dwServerInfo;
        v57 = (unsigned int)m_CodePage;
        v53 = aReference;
        v50 = 599;
        goto LABEL_65;
      }
      v40 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v40);
      v80 = 5;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        GameClientSendPacket::SendPartyMemberData(
          (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
          v39->m_Party.m_dwPartyID,
          (unsigned int)m_CodePage,
          (unsigned int)dwServerInfo,
          &byte_4F386C,
          0x38u);
      }
      else
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          609,
          (char *)&byte_4EE5AC);
      }
      goto LABEL_102;
    case 6u:
      v41 = GameClientDispatch->m_lpCharacter;
      if ( !v41 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 636;
        goto LABEL_111;
      }
      if ( m_CodePage != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node *)v41->m_dwCID )
      {
        v61 = m_CodePage;
        v57 = v41->m_dwCID;
        v53 = aCid0x08x_51;
        v50 = 641;
LABEL_65:
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          v50,
          v53,
          v57,
          v61);
        goto LABEL_137;
      }
      v42 = (int)v41->GetParty(v41);
      v43 = v42;
      if ( !v42 )
      {
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_160;
        v51 = 649;
        goto LABEL_111;
      }
      if ( *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node **)(v42 + 56) == m_CodePage )
      {
        v44 = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v44);
        v80 = 6;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          GameClientSendPacket::SendCharPartyDestroyToDBAgent(
            (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
            *(_DWORD *)(v43 + 52));
        }
        else
        {
          v7 = 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            659,
            (char *)&byte_4EE5AC);
        }
LABEL_102:
        v80 = -1;
        CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
        if ( v7 )
          goto LABEL_137;
      }
      return 1;
    case 0xBu:
      if ( v3 )
      {
        v7 = 10;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          682,
          aCid0x08x_142,
          m_CodePage);
        goto LABEL_137;
      }
      if ( dwServerInfo )
      {
        if ( CPartyMgr::AddFindPartyList(CSingleton<CPartyMgr>::ms_pSingleton, m_CodePage) )
          goto LABEL_137;
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_169;
        v51 = 697;
      }
      else
      {
        if ( CPartyMgr::DeleteFindPartyList(CSingleton<CPartyMgr>::ms_pSingleton, (unsigned int)m_CodePage) )
          goto LABEL_137;
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_225;
        v51 = 689;
      }
      goto LABEL_111;
    case 0xCu:
      v45 = GameClientDispatch->m_lpCharacter;
      if ( v45 )
      {
        v46 = (int)v45->GetParty(GameClientDispatch->m_lpCharacter);
        if ( !v46 )
        {
          v7 = 11;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            720,
            aCid0x08x_160,
            v45->m_dwCID);
          goto LABEL_124;
        }
        if ( *(std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCharacter *> >,0> >::_Node **)(v46 + 56) != m_CodePage )
        {
          v7 = 12;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPartyCmd",
            aDWorkRylSource_57,
            725,
            aCid0x08x_337,
            m_CodePage);
          goto LABEL_124;
        }
        if ( dwServerInfo )
        {
          if ( CPartyMgr::AddFindMemberList(CSingleton<CPartyMgr>::ms_pSingleton, dwPartyID) )
            goto LABEL_124;
          v62 = (unsigned int)m_CodePage;
          v58 = aCid0x08x_88;
          v54 = 740;
        }
        else
        {
          if ( CPartyMgr::DeleteFindMemberList(CSingleton<CPartyMgr>::ms_pSingleton, (unsigned int)dwPartyID) )
            goto LABEL_124;
          v62 = (unsigned int)m_CodePage;
          v58 = aCid0x08x_184;
          v54 = 732;
        }
      }
      else
      {
        v62 = GameClientDispatch->m_dwUID;
        v58 = aUid0x08x;
        v54 = 712;
      }
      v7 = 1;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharPartyCmd",
        aDWorkRylSource_57,
        v54,
        v58,
        v62);
LABEL_124:
      GameClientSendPacket::SendCharPartyCmd(
        SendStream,
        (unsigned int)dwPartyID,
        &SenderAddress,
        &szSenderName_In,
        (unsigned int)m_CodePage,
        (unsigned int)dwServerInfo,
        usCmd,
        v7);
      return 1;
    case 0xDu:
    case 0xEu:
      v47 = GameClientDispatch->m_lpCharacter;
      if ( !v47 )
      {
        m_dwUID = GameClientDispatch->m_dwUID;
        v55 = aUid0x08x;
        v51 = 757;
LABEL_111:
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          v51,
          v55,
          m_dwUID);
LABEL_137:
        GameClientSendPacket::SendCharPartyCmd(
          SendStream,
          (unsigned int)dwPartyID,
          &SenderAddress,
          &szSenderName_In,
          (unsigned int)m_CodePage,
          (unsigned int)dwServerInfo,
          usCmd,
          v7);
        return 1;
      }
      lpSendCharacterd = v47->GetParty(GameClientDispatch->m_lpCharacter);
      if ( !lpSendCharacterd )
      {
        m_dwUID = v47->m_dwCID;
        v55 = aCid0x08x_160;
        v51 = 765;
        goto LABEL_111;
      }
      if ( lpSendCharacterd->m_Party.m_dwLeaderID != v47->m_dwCID )
      {
        m_dwUID = (unsigned int)m_CodePage;
        v55 = aCid0x08x_306;
        v51 = 770;
        goto LABEL_111;
      }
      v48 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&dwReferenceID, v48);
      v80 = 7;
      if ( dwReferenceID.m_lpDispatch )
      {
        v49 = (CSendStream *)&dwReferenceID.m_lpDispatch[8];
        if ( usCmd == 13 )
          GameClientSendPacket::SendPartyMemberData(
            v49,
            lpSendCharacterd->m_Party.m_dwPartyID,
            (unsigned int)m_CodePage,
            (unsigned int)dwServerInfo,
            &byte_4EFAF4,
            0x39u);
        else
          GameClientSendPacket::SendPartyMemberData(
            v49,
            lpSendCharacterd->m_Party.m_dwPartyID,
            (unsigned int)m_CodePage,
            (unsigned int)dwServerInfo,
            &byte_4EFAF4,
            0x3Au);
      }
      else
      {
        v7 = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPartyCmd",
          aDWorkRylSource_57,
          780,
          (char *)&byte_4EE5AC);
      }
      v80 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&dwReferenceID);
      if ( v7 )
        goto LABEL_137;
      return 1;
    default:
      return 1;
  }
}
// 48AD0A: variable 'v14' is possibly undefined
// 48B136: variable 'v30' is possibly undefined

//----- (0048B820) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharPartyFind(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x10 )
  {
    if ( GameClientDispatch->m_lpCharacter )
    {
      CPartyMgr::SendPartyFind(CSingleton<CPartyMgr>::ms_pSingleton, GameClientDispatch->m_lpCharacter);
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048B880) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharFriendAdd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int v4; // edi
  unsigned int m_dwCID; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // ebx
  CFriendList::Rebind *Friend; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ebx
  CSingleDispatch *v12; // eax
  CSendStream *v13; // eax
  CSingleDispatch *v14; // eax
  CPacketDispatch *v15; // ebx
  CSingleDispatch *v16; // eax
  BanInfo *v17; // [esp-14h] [ebp-54h]
  CFriendList::Rebind *FriendNum; // [esp-14h] [ebp-54h]
  unsigned __int16 usError; // [esp+0h] [ebp-40h]
  unsigned __int8 cAckCmd; // [esp+4h] [ebp-3Ch]
  const char *szFriendName; // [esp+8h] [ebp-38h]
  CSendStream *ChatStream; // [esp+Ch] [ebp-34h]
  CSendStream *ChatStreama; // [esp+Ch] [ebp-34h]
  CSingleDispatch::Storage v24; // [esp+14h] [ebp-2Ch] BYREF
  CSingleDispatch::Storage v25; // [esp+1Ch] [ebp-24h] BYREF
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+24h] [ebp-1Ch] BYREF
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+2Ch] [ebp-14h] BYREF
  int v28; // [esp+3Ch] [ebp-4h]
  PktBase *lpPktBasea; // [esp+48h] [ebp+8h]
  PktBase *lpPktBaseb; // [esp+48h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x21 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  v4 = 0;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_dwCID = m_lpCharacter->m_dwCID;
  usError = 0;
  cAckCmd = 0;
  if ( LOBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError) )
  {
    if ( LOBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError) == 1 )
      cAckCmd = 4;
    else
      usError = 2;
  }
  else
  {
    cAckCmd = 1;
  }
  szFriendName = 0;
  if ( !usError )
  {
    Instance = CCreatureManager::GetInstance();
    Character = CCreatureManager::GetCharacter(Instance, (char *)&lpPktBase[1]);
    if ( !Character )
      return GameClientSendPacket::SendCharFriendAck(&GameClientDispatch->m_SendStream, cAckCmd, 0, 0, 3u);
    if ( LOBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError) )
    {
      if ( LOBYTE(lpPktBase[2].m_SrvInfo.SrvState.wError) == 1 )
      {
        if ( CBanList::Add(&m_lpCharacter->m_banList, Character->m_dwCID, Character->m_DBData.m_Info.Name) )
        {
          Friend = CFriendList::GetFriend(&Character->m_friendList, m_dwCID);
          if ( Friend )
          {
            if ( CFriendList::Rebind::IsFriend(Friend, m_lpCharacter->m_DBData.m_Info.Name) )
            {
              m_lpGameClientDispatch = (CSendStream *)Character->m_lpGameClientDispatch;
              if ( m_lpGameClientDispatch )
                GameClientSendPacket::SendCharFriendAck(
                  m_lpGameClientDispatch + 8,
                  8u,
                  m_dwCID,
                  m_lpCharacter->m_DBData.m_Info.Name,
                  0);
            }
          }
          if ( CFriendList::Remove(&m_lpCharacter->m_friendList, Character->m_dwCID) )
            cAckCmd = 5;
          v4 = Character->m_dwCID;
          szFriendName = Character->m_DBData.m_Info.Name;
          lpPktBasea = (PktBase *)Character->m_dwUID;
          DispatchTable = CDBAgentDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&v25, DispatchTable);
          m_lpDispatch = v25.m_lpDispatch;
          v28 = 2;
          v12 = CChatDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&v24, v12);
          LOBYTE(v28) = 3;
          if ( m_lpDispatch && v24.m_lpDispatch )
          {
            ChatStream = (CSendStream *)&v24.m_lpDispatch[8];
            GameClientSendPacket::SendFriendListChangeToDB(
              (CSendStream *)&m_lpDispatch[8],
              m_lpCharacter->m_dwUID,
              m_dwCID,
              (unsigned int)lpPktBasea,
              v4,
              0,
              2u);
            GameClientSendPacket::SendFriendListChangeToDB(
              ChatStream,
              m_lpCharacter->m_dwUID,
              m_dwCID,
              (unsigned int)lpPktBasea,
              v4,
              0,
              2u);
            if ( cAckCmd == 5 )
            {
              GameClientSendPacket::SendFriendListChangeToDB(
                (CSendStream *)&m_lpDispatch[8],
                m_lpCharacter->m_dwUID,
                m_dwCID,
                (unsigned int)lpPktBasea,
                v4,
                0,
                1u);
              GameClientSendPacket::SendFriendListChangeToDB(
                ChatStream,
                m_lpCharacter->m_dwUID,
                m_dwCID,
                (unsigned int)lpPktBasea,
                v4,
                0,
                1u);
            }
          }
          LOBYTE(v28) = 2;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v24);
          v28 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&v25);
        }
        else
        {
          usError = 4;
          v17 = std::vector<CSpeedHackCheck::CoolDownInfo>::size(&m_lpCharacter->m_banList.m_banList);
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharFriendAdd",
            aDWorkRylSource_57,
            1330,
            aCid0x08x_277,
            m_dwCID,
            v17);
        }
      }
      else
      {
        usError = 2;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharFriendAdd",
          aDWorkRylSource_57,
          1390,
          aCid0x08x_197,
          m_dwCID,
          LOBYTE(lpPktBase[2].m_SrvInfo.dwServerInfo));
      }
    }
    else if ( (*(_BYTE *)&Character->m_RejectOption.Reject & 0x10) != 0
           || CBanList::IsBan(&Character->m_banList, m_dwCID, m_lpCharacter->m_DBData.m_Info.Name) )
    {
      usError = 5;
    }
    else if ( CFriendList::Add(&m_lpCharacter->m_friendList, Character->m_dwCID, Character->m_DBData.m_Info.Name) )
    {
      v13 = (CSendStream *)Character->m_lpGameClientDispatch;
      if ( v13 )
        GameClientSendPacket::SendCharFriendAdded(v13 + 8, m_lpCharacter->m_dwCID, m_lpCharacter->m_DBData.m_Info.Name);
      if ( CBanList::Remove(&m_lpCharacter->m_banList, Character->m_dwCID) )
        cAckCmd = 2;
      v4 = Character->m_dwCID;
      szFriendName = Character->m_DBData.m_Info.Name;
      lpPktBaseb = (PktBase *)Character->m_dwUID;
      v14 = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, v14);
      v15 = StoragelpDBAgentDispatch.m_lpDispatch;
      v28 = 0;
      v16 = CChatDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, v16);
      LOBYTE(v28) = 1;
      if ( v15 )
      {
        if ( StoragelpChatDispatch.m_lpDispatch )
        {
          ChatStreama = (CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8];
          GameClientSendPacket::SendFriendListChangeToDB(
            (CSendStream *)&v15[8],
            m_lpCharacter->m_dwUID,
            m_dwCID,
            (unsigned int)lpPktBaseb,
            v4,
            0,
            0);
          GameClientSendPacket::SendFriendListChangeToDB(
            ChatStreama,
            m_lpCharacter->m_dwUID,
            m_dwCID,
            (unsigned int)lpPktBaseb,
            v4,
            0,
            0);
          if ( cAckCmd == 2 )
          {
            GameClientSendPacket::SendFriendListChangeToDB(
              (CSendStream *)&v15[8],
              m_lpCharacter->m_dwUID,
              m_dwCID,
              (unsigned int)lpPktBaseb,
              v4,
              0,
              3u);
            GameClientSendPacket::SendFriendListChangeToDB(
              ChatStreama,
              m_lpCharacter->m_dwUID,
              m_dwCID,
              (unsigned int)lpPktBaseb,
              v4,
              0,
              3u);
          }
        }
      }
      LOBYTE(v28) = 0;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
      v28 = -1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    }
    else
    {
      usError = 4;
      FriendNum = CFriendList::GetFriendNum(&m_lpCharacter->m_friendList);
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharFriendAdd",
        aDWorkRylSource_57,
        1271,
        aCid0x08x_282,
        m_dwCID,
        FriendNum);
    }
  }
  return GameClientSendPacket::SendCharFriendAck(&GameClientDispatch->m_SendStream, cAckCmd, v4, szFriendName, usError);
}

//----- (0048BD10) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharClassUpgrade(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x11 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      return CCharacter::ChangeClass(m_lpCharacter, lpPktBase[1].m_CodePage);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048BD70) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharIncreasePoint(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  unsigned __int8 m_CodePage; // dl
  unsigned int v5; // esi
  unsigned __int16 v6; // ax
  ChState State; // [esp+8h] [ebp-Ch] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x11 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      m_CodePage = lpPktBase[1].m_CodePage;
      v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      memset(&State, 0, sizeof(State));
      v6 = CCharacter::AddState(m_lpCharacter, m_CodePage, &State);
      return GameClientSendPacket::SendCharIncreasePoint(&GameClientDispatch->m_SendStream, v5, State, v6);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048BE30) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharStateRedistribution(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // eax
  ServerInfo m_SrvInfo; // edx
  ChState State; // [esp+0h] [ebp-Ch] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x18 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      *(_DWORD *)&State.m_wIP = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_SrvInfo = lpPktBase[1].m_SrvInfo;
      *(_DWORD *)&State.m_wDEX = lpPktBase[1].m_CodePage;
      *(ServerInfo *)&State.m_wINT = m_SrvInfo;
      return CCharacter::StateRedistribution(m_lpCharacter, &State);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048BEB0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharStatusRetrain(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  __int16 v4; // ax
  unsigned int m_CodePage; // esi
  ServerInfo m_SrvInfo; // edx
  ChState State; // [esp+0h] [ebp-Ch] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1A )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_WORD *)&lpPktBase[2].m_StartBit;
      *(_DWORD *)&State.m_wIP = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_CodePage = lpPktBase[1].m_CodePage;
      m_SrvInfo = lpPktBase[1].m_SrvInfo;
      LOWORD(lpPktBase) = v4;
      *(ServerInfo *)&State.m_wINT = m_SrvInfo;
      *(_DWORD *)&State.m_wDEX = m_CodePage;
      return CCharacter::StatusRetrain(m_lpCharacter, &State, (int)lpPktBase);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048BF40) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharAttack(CGameClientDispatch *GameClientDispatch, int lpPktBase)
{
  PktBase *v2; // ebp
  PktBase *v3; // ebx
  int v4; // eax
  unsigned __int16 v6; // cx
  unsigned int m_CodePage; // edx
  float v8; // ecx
  float v9; // edx
  float v10; // edx
  unsigned int v11; // eax
  bool v12; // zf
  CCharacter *v13; // edi
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *SiegeObject; // esi
  float v16; // ecx
  float v17; // edx
  CSendStream *p_m_SendStream; // esi
  char *Buffer; // eax
  unsigned int v20; // [esp-8h] [ebp-64h]
  AtType AtType; // [esp+Ch] [ebp-50h]
  Position NewPosition; // [esp+10h] [ebp-4Ch] BYREF
  unsigned int dwCharID; // [esp+1Ch] [ebp-40h]
  CCharacter *lpCharacter; // [esp+20h] [ebp-3Ch]
  AtNode AtNode; // [esp+24h] [ebp-38h] BYREF

  v2 = (PktBase *)lpPktBase;
  v3 = (PktBase *)(lpPktBase + 12);
  v4 = CCSAuth::DecryptPacket(
         &GameClientDispatch->m_CSAuth,
         (unsigned __int8 *)(lpPktBase + 12),
         (*(_WORD *)(lpPktBase + 2) & 0x3FFF) - 12);
  if ( !v4 )
  {
    CGameClientDispatch::PrintGameGuardError(GameClientDispatch);
    return 0;
  }
  v6 = (v4 + 12) | v2->m_Len & 0xC000;
  v2->m_Len = v6;
  if ( (v6 & 0x3FFF) != 0x57 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, v2->m_Cmd);
    return 0;
  }
  lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, v2->m_Cmd);
    return 0;
  }
  m_CodePage = v2[2].m_CodePage;
  AtType = (AtType)v2[2].m_SrvInfo.dwServerInfo;
  qmemcpy(&AtNode, &v2[3], 0x30u);
  v8 = *(float *)&v3->m_CodePage;
  dwCharID = m_CodePage;
  v9 = *(float *)&v3->m_StartBit;
  NewPosition.m_fPointY = v8;
  *(_WORD *)&AtNode.m_cDefenserJudge[7] = *(_WORD *)&v2[7].m_StartBit;
  NewPosition.m_fPointX = v9;
  v10 = *(float *)&v3->m_SrvInfo.dwServerInfo;
  lpPktBase = 0;
  AtNode.m_cDefenserJudge[9] = v2[7].m_Len;
  NewPosition.m_fPointZ = v10;
  CAggresiveCreature::MoveTo(lpCharacter, &NewPosition, 0);
  v11 = dwCharID;
  v12 = (dwCharID & 0xC0000000) == 0;
  v13 = lpCharacter;
  lpCharacter->m_MotionInfo.m_fDirection = *(float *)&v2[2].m_StartBit;
  if ( !v12 || (v11 & 0x10000000) == 0 || !v13->m_dwRideArmsCID )
  {
    if ( (*((_BYTE *)&AtType + 2) & 1) != 0 )
    {
      CCharacter::Casting(v13, AtType, &AtNode);
    }
    else if ( !CCharacter::AttackCID(v13, AtType, &AtNode, (unsigned __int16 *)&lpPktBase) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharAttack",
        aDWorkRylSource_21,
        112,
        (char *)&byte_4F3C90,
        v13->m_DBData.m_Info.Name);
      if ( !(_WORD)lpPktBase )
        lpPktBase = 1;
      p_m_SendStream = &GameClientDispatch->m_SendStream;
      Buffer = CSendStream::GetBuffer(&GameClientDispatch->m_SendStream, (char *)0x1A);
      if ( Buffer )
        goto LABEL_24;
    }
    return 1;
  }
  v20 = v11;
  Instance = CSiegeObjectMgr::GetInstance();
  SiegeObject = CSiegeObjectMgr::GetSiegeObject(Instance, v20);
  if ( SiegeObject )
  {
    v16 = *(float *)&v3->m_CodePage;
    v17 = *(float *)&v3->m_SrvInfo.dwServerInfo;
    NewPosition.m_fPointX = *(float *)&v3->m_StartBit;
    NewPosition.m_fPointY = v16;
    NewPosition.m_fPointZ = v17;
    CAggresiveCreature::MoveTo(SiegeObject, &NewPosition, 0);
    SiegeObject->m_MotionInfo.m_fDirection = *(float *)&v2[2].m_StartBit;
  }
  if ( CSiegeObject::AttackCID(SiegeObject, v13, AtType, &AtNode, (unsigned __int16 *)&lpPktBase) )
    return 1;
  if ( !(_WORD)lpPktBase )
    lpPktBase = 1;
  p_m_SendStream = &GameClientDispatch->m_SendStream;
  Buffer = CSendStream::GetBuffer(&GameClientDispatch->m_SendStream, (char *)0x1A);
  if ( !Buffer )
    return 1;
LABEL_24:
  *((_DWORD *)Buffer + 3) = 0;
  *((_WORD *)Buffer + 10) = 0;
  *((_WORD *)Buffer + 11) = 0;
  Buffer[24] = 0;
  Buffer[25] = 0;
  return CSendStream::WrapCrypt(p_m_SendStream, 0x1Au, 0xEu, 0, lpPktBase);
}

//----- (0048C1F0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSwitchHand(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  char v4; // bl
  unsigned __int8 lpPktBasea; // [esp+8h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0xD )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      lpPktBasea = lpPktBase[1].m_StartBit;
      v4 = CCharacter::ChangeWeaponAndShield(m_lpCharacter, lpPktBasea);
      GAMELOG::LogChangeWeapon(m_lpCharacter, lpPktBasea);
      return v4;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048C260) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharRespawn(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  int v4; // ebp
  char m_CodePage; // bl
  POS *v6; // edi
  CServerSetup *Instance; // eax
  CServerSetup *v8; // eax
  CServerSetup *v9; // eax
  CCreatureManager *v10; // eax
  unsigned __int8 Nationality; // al
  VirtualArea::CVirtualAreaMgr *v12; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  VirtualArea::CVirtualArea *v14; // edi
  unsigned __int8 MaxRespawnPos; // al
  unsigned int v16; // eax
  CServerSetup *v17; // eax
  unsigned __int16 m_nMaxMP; // ax
  __int128 v19; // [esp-14h] [ebp-3Ch] BYREF
  unsigned __int16 m_wMapIndex; // [esp-4h] [ebp-2Ch]
  Position DestPos; // [esp+10h] [ebp-18h]
  Position result; // [esp+1Ch] [ebp-Ch] BYREF
  unsigned __int8 lpPktBaseb; // [esp+30h] [ebp+8h]
  signed __int8 lpPktBasea; // [esp+30h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1D )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  m_CodePage = lpPktBase[1].m_CodePage;
  v6 = (POS *)((char *)&lpPktBase[1].m_CodePage + 1);
  DestPos = *(Position *)((char *)&lpPktBase[1].m_CodePage + 1);
  switch ( m_CodePage )
  {
    case 0:
      memset((char *)&v19 + 4, 0, 12);
      if ( !CCharacter::Respawn(m_lpCharacter, *(Position *)((char *)&v19 + 4), 1) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          182,
          aCid0x08x_239,
          v4);
      return 1;
    case 1:
      Instance = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 4
        || (v8 = CServerSetup::GetInstance(), (unsigned __int8)CServerSetup::GetServerZone(v8) == 5) )
      {
        if ( !CCharacter::Respawn(m_lpCharacter, DestPos, 1) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharRespawn",
            aDWorkRylSource_21,
            194,
            aCid0x08x_239,
            v4);
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          199,
          aCid0x08x_238,
          v4);
      }
      return 1;
    case 2:
      return 1;
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      v9 = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(v9) == 3 )
      {
        v10 = CCreatureManager::GetInstance();
        CCreatureManager::PushRespawnQueue(v10, m_lpCharacter, m_CodePage - 3);
        return 1;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharRespawn",
        aDWorkRylSource_21,
        224,
        aCid0x08x_242,
        v4);
      return 1;
    case 9:
      Nationality = m_lpCharacter->m_DBData.m_Info.Nationality;
      if ( Nationality > 2u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          234,
          aCid0x08x_111,
          v4,
          Nationality);
        return 1;
      }
      m_wMapIndex = m_lpCharacter->m_CellPos.m_wMapIndex;
      v12 = VirtualArea::CVirtualAreaMgr::GetInstance();
      VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v12, m_wMapIndex);
      v14 = VirtualArea;
      if ( !VirtualArea )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          241,
          aCid0x08x_5,
          v4,
          m_lpCharacter->m_CellPos.m_wMapIndex);
        return 1;
      }
      lpPktBaseb = m_lpCharacter->m_DBData.m_Info.Nationality;
      MaxRespawnPos = VirtualArea::CVirtualArea::GetMaxRespawnPos(VirtualArea);
      v16 = Math::Random::ComplexRandom(MaxRespawnPos, 0);
      *(Position *)((char *)&v19 + 4) = *VirtualArea::CVirtualArea::GetRespawnPosition(v14, &result, lpPktBaseb, v16);
      if ( !CCharacter::Respawn(m_lpCharacter, *(Position *)((char *)&v19 + 4), 1) )
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          248,
          aCid0x08x_239,
          v4);
      return 1;
    case 10:
      v17 = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(v17) != 3 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharRespawn",
          aDWorkRylSource_21,
          286,
          aCid0x08x_260,
          v4);
        return 1;
      }
      if ( !m_lpCharacter->m_DBData.m_Info.Nationality )
      {
        lpPktBasea = 1;
LABEL_29:
        m_nMaxMP = m_lpCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
        m_lpCharacter->m_CreatureStatus.m_nNowHP = m_lpCharacter->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
        m_lpCharacter->m_CreatureStatus.m_nNowMP = m_nMaxMP;
        *(POS *)&v19 = *v6;
        if ( !CCharacter::MoveZone(m_lpCharacter, (POS)v19, lpPktBasea, 0) )
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharRespawn",
            aDWorkRylSource_21,
            280,
            aCid0x08x_64,
            v4);
        return 1;
      }
      if ( m_lpCharacter->m_DBData.m_Info.Nationality == 1 )
      {
        lpPktBasea = 2;
        goto LABEL_29;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharRespawn",
        aDWorkRylSource_21,
        269,
        aCid0x08x_111,
        v4,
        (unsigned __int8)m_lpCharacter->m_DBData.m_Info.Nationality);
      return 1;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharRespawn",
        aDWorkRylSource_21,
        294,
        aCid0x08x_42,
        v4);
      return 1;
  }
}

//----- (0048C5A0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharRespawnWaitQueue(
        CGameClientDispatch *GameClientDispatch,
        PktBase *lpPktBase)
{
  CCreatureManager *Instance; // eax
  unsigned int v4; // [esp-4h] [ebp-4h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x10 )
  {
    if ( GameClientDispatch->m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      Instance = CCreatureManager::GetInstance();
      return CCreatureManager::SendRespawnQueue(Instance, v4);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048C600) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharMoveUpdate(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  char wError; // al
  float v5; // esi
  float Pos_8; // [esp+8h] [ebp-10h]
  Position DestPos; // [esp+Ch] [ebp-Ch] BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x21 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      wError = lpPktBase[2].m_SrvInfo.SrvState.wError;
      v5 = *(float *)&lpPktBase[1].m_CodePage;
      Pos_8 = *(float *)&lpPktBase[1].m_SrvInfo.dwServerInfo;
      DestPos.m_fPointX = *(float *)&lpPktBase[1].m_StartBit;
      DestPos.m_fPointY = v5;
      DestPos.m_fPointZ = Pos_8;
      CAggresiveCreature::MoveTo(m_lpCharacter, &DestPos, wError);
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048C6A0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharDuelCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  unsigned int m_CodePage; // ebx
  unsigned int v4; // ebp
  CCreatureManager *Instance; // eax
  CCharacter *Character; // esi
  CCreatureManager *v7; // eax
  CCharacter *v8; // eax
  CCharacter *v9; // edi
  CSendStream *m_lpGameClientDispatch; // ecx
  CCharacterParty *v11; // eax
  CCharacter_vtbl *v12; // edx
  CCharacterParty *v13; // eax
  int v14; // eax
  int v15; // edi
  CCreatureManager *v16; // eax
  CCharacter *v17; // eax
  CSendStream *v18; // eax
  CCharacterParty *v19; // esi
  CCharacterParty *v20; // eax
  CCharacterParty *v21; // edi
  unsigned int v22; // [esp-14h] [ebp-78h]
  unsigned __int16 lpSendDispatch[2]; // [esp+0h] [ebp-64h] BYREF
  CCharacterParty *lpSParty; // [esp+4h] [ebp-60h]
  CSendStream *dwBufferSize; // [esp+8h] [ebp-5Ch] BYREF
  char Buffer[84]; // [esp+Ch] [ebp-58h] BYREF
  char lpPktBasea; // [esp+6Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    lpSParty = (CCharacterParty *)GameClientDispatch->m_lpCharacter;
    if ( lpSParty )
    {
      m_CodePage = lpPktBase[1].m_CodePage;
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      lpPktBasea = lpPktBase[1].m_SrvInfo.SrvState.wError;
      Instance = CCreatureManager::GetInstance();
      Character = CCreatureManager::GetCharacter(Instance, v4);
      v7 = CCreatureManager::GetInstance();
      v8 = CCreatureManager::GetCharacter(v7, m_CodePage);
      v9 = v8;
      if ( Character && v8 )
      {
        m_lpGameClientDispatch = (CSendStream *)v8->m_lpGameClientDispatch;
        *(_DWORD *)lpSendDispatch = Character->m_lpGameClientDispatch;
        dwBufferSize = m_lpGameClientDispatch;
        switch ( lpPktBasea )
        {
          case 0:
            if ( Character->GetDuelOpponent(Character) || v9->GetDuelOpponent(v9) )
              goto LABEL_15;
            if ( (*(_BYTE *)&v9->m_RejectOption.Reject & 4) != 0
              || CBanList::IsBan(
                   &v9->m_banList,
                   (unsigned int)lpSParty->m_PartySpellMgr.m_pPartyMember[6],
                   (char *)&lpSParty[3].m_PartySpellMgr) )
            {
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 3u);
              return 1;
            }
            if ( !dwBufferSize )
              return 1;
LABEL_13:
            GameClientSendPacket::SendCharDuelCmd(dwBufferSize + 8, v4, m_CodePage, lpPktBasea, 0);
            return 1;
          case 1:
            if ( Character->GetDuelOpponent(Character) || v9->GetDuelOpponent(v9) )
              goto LABEL_15;
            CCharacter::SetDuelOpponent(Character, v9);
            CCharacter::SetDuelOpponent(v9, Character);
            if ( *(_DWORD *)lpSendDispatch )
              GameClientSendPacket::SendCharDuelCmd(
                (CSendStream *)(*(_DWORD *)lpSendDispatch + 64),
                v4,
                m_CodePage,
                lpPktBasea,
                0);
            if ( dwBufferSize )
              goto LABEL_13;
            return 1;
          case 2:
            if ( !m_lpGameClientDispatch )
              return 1;
            GameClientSendPacket::SendCharDuelCmd(m_lpGameClientDispatch + 8, v4, m_CodePage, lpPktBasea, 0);
            return 1;
          case 5:
            lpSParty = (CCharacterParty *)Character->GetParty(Character);
            v14 = (int)v9->GetParty(v9);
            v15 = v14;
            if ( !lpSParty || !v14 )
            {
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 5u);
              return 1;
            }
            if ( lpSParty->m_pHostileParty || *(_DWORD *)(v14 + 356) )
            {
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 2u);
              return 1;
            }
            *(_DWORD *)lpSendDispatch = 0;
            if ( CCharacterParty::MakeTeamBattleInfo(lpSParty, Buffer, lpSendDispatch, Character, lpPktBasea) != 1 )
              return 1;
            v22 = *(_DWORD *)(v15 + 56);
            v16 = CCreatureManager::GetInstance();
            v17 = CCreatureManager::GetCharacter(v16, v22);
            if ( !v17 )
            {
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 1u);
              return 1;
            }
            v18 = (CSendStream *)v17->m_lpGameClientDispatch;
            if ( v18 )
            {
              CSendStream::PutBuffer(v18 + 8, Buffer, lpSendDispatch[0], 0x63u);
              return 1;
            }
            return 1;
          case 6:
            v19 = (CCharacterParty *)Character->GetParty(Character);
            v20 = (CCharacterParty *)v9->GetParty(v9);
            v21 = v20;
            if ( !v19 || !v20 )
              goto LABEL_50;
            if ( v19->m_pHostileParty || v20->m_pHostileParty )
              goto LABEL_15;
            CCharacterParty::StartTeamBattle(v19, v20);
            CCharacterParty::StartTeamBattle(v21, v19);
            return 1;
          case 7:
            v11 = (CCharacterParty *)Character->GetParty(Character);
            v12 = v9->__vftable;
            lpSParty = v11;
            v13 = (CCharacterParty *)v12->GetParty(v9);
            if ( !lpSParty || !v13 )
            {
LABEL_50:
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 5u);
              return 1;
            }
            if ( lpSParty->m_Party.m_dwLeaderID != Character->m_dwCID )
            {
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 4u);
              return 1;
            }
            if ( lpSParty->m_pHostileParty || v13->m_pHostileParty )
            {
LABEL_15:
              GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 2u);
              return 1;
            }
            dwBufferSize = 0;
            if ( CCharacterParty::MakeTeamBattleInfo(v13, Buffer, (unsigned __int16 *)&dwBufferSize, v9, lpPktBasea) == 1
              && *(_DWORD *)lpSendDispatch )
            {
              CSendStream::PutBuffer(
                (CSendStream *)(*(_DWORD *)lpSendDispatch + 64),
                Buffer,
                (unsigned __int16)dwBufferSize,
                0x63u);
              return 1;
            }
            break;
          default:
            return 1;
        }
      }
      else
      {
        GameClientSendPacket::SendCharDuelCmd(&GameClientDispatch->m_SendStream, v4, m_CodePage, lpPktBasea, 1u);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharDuelCmd",
          aDWorkRylSource_21,
          365,
          aCid0x08xRecver,
          m_CodePage,
          v4);
      }
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048CBD0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharPeaceMode(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  unsigned int v4; // ebp
  unsigned __int16 v5; // si
  CServerSetup *Instance; // eax
  CSingleDispatch *DispatchTable; // eax
  char bPeace; // [esp+0h] [ebp-18h]
  CSingleDispatch::Storage StoragelpChatDispatch; // [esp+4h] [ebp-14h] BYREF
  int v10; // [esp+14h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+20h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x12 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      bPeace = BYTE1(lpPktBase[1].m_CodePage);
      v5 = 0;
      lpPktBasea = -1;
      Instance = CServerSetup::GetInstance();
      if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 3 )
      {
        v5 = 3;
      }
      else
      {
        lpPktBasea = CCharacter::SetPeaceMode(m_lpCharacter, bPeace);
        if ( lpPktBasea == 0xFF )
        {
          v5 = 2;
        }
        else
        {
          DispatchTable = CChatDispatch::GetDispatchTable();
          CSingleDispatch::Storage::Storage(&StoragelpChatDispatch, DispatchTable);
          v10 = 0;
          if ( StoragelpChatDispatch.m_lpDispatch )
            CChatDispatch::SendCharInfoChanged((CSendStream *)&StoragelpChatDispatch.m_lpDispatch[8], m_lpCharacter);
          v10 = -1;
          CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpChatDispatch);
        }
      }
      return GameClientSendPacket::SendCharPeaceMode(&GameClientDispatch->m_SendStream, v4, lpPktBasea, bPeace, v5);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048CD00) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSummonCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  char result; // al
  CCharacter *m_lpCharacter; // ebx
  unsigned __int8 dwServerInfo; // cl
  int v5; // ebp
  CMonster *m_lpSummonee; // esi
  unsigned int m_CodePage; // edi
  CCreatureManager *Instance; // eax
  unsigned __int8 cCmd; // [esp+4h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+10h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
      v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_lpSummonee = m_lpCharacter->m_lpSummonee;
      m_CodePage = lpPktBase[1].m_CodePage;
      cCmd = dwServerInfo;
      if ( m_lpSummonee )
      {
        switch ( dwServerInfo )
        {
          case 0u:
            Instance = CCreatureManager::GetInstance();
            lpPktBasea = (PktBase *)CCreatureManager::GetAggresiveCreature(Instance, m_CodePage);
            if ( lpPktBasea )
            {
              CThreat::ClearThreatList(&m_lpSummonee->m_Threat);
              m_lpSummonee->AttackCmd(m_lpSummonee, (CAggresiveCreature *)lpPktBasea, 1u);
            }
            else
            {
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "GameClientParsePacket::ParseCharSummonCmd",
                aDWorkRylSource_21,
                617,
                aCid0x08x_272,
                v5,
                m_CodePage);
            }
            goto LABEL_15;
          case 1u:
            CMonster::CancelTarget(m_lpSummonee);
            goto LABEL_15;
          case 2u:
            return m_lpSummonee->Dead(m_lpSummonee, 0);
          case 3u:
            m_lpSummonee->SetGuard(m_lpSummonee, 1);
            goto LABEL_15;
          case 4u:
            m_lpSummonee->SetGuard(m_lpSummonee, 0);
            goto LABEL_15;
          default:
LABEL_15:
            result = GameClientSendPacket::SendCharSummonCmd(m_lpCharacter, m_lpSummonee, cCmd, m_CodePage, 0);
            break;
        }
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSummonCmd",
          aDWorkRylSource_21,
          605,
          aCid0x08x_174,
          v5,
          dwServerInfo,
          m_CodePage);
        return 1;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  return result;
}

//----- (0048CE60) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharMoveEx(CGameClientDispatch *GameClientDispatch, CSiegeObject *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int m_fPointZ_low; // ebx
  unsigned __int8 m_wAction; // al
  int m_fDirection_high; // ecx
  int m_fVelocity_low; // edx
  bool v8; // al
  unsigned int m_dwRideArmsCID; // eax
  CSiegeObjectMgr *Instance; // eax
  CSiegeObject *SiegeObject; // eax
  DWORD Time; // eax
  unsigned int m_dwLastUpdateExTime; // edx
  char v14; // bl
  CCell *m_lpCell; // ebp
  unsigned int v16; // eax
  bool v17; // zf
  unsigned int m_fDirection_low; // ecx
  ServerInfo v19; // edx
  CCharacterParty *v20; // eax
  unsigned __int8 cUAct; // [esp+Fh] [ebp-2Dh]
  unsigned int dwCID; // [esp+10h] [ebp-2Ch]
  Position Pos; // [esp+14h] [ebp-28h] BYREF
  PktMVEx pktMVEx; // [esp+20h] [ebp-1Ch] BYREF
  CGameClientDispatch *GameClientDispatcha; // [esp+40h] [ebp+4h]
  char lpArms; // [esp+44h] [ebp+8h]

  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  m_fPointZ_low = LODWORD(lpPktBase->m_CurrentPos.m_fPointZ);
  m_wAction = lpPktBase->m_MotionInfo.m_wAction;
  dwCID = m_fPointZ_low;
  cUAct = m_wAction;
  if ( m_lpCharacter )
  {
    if ( m_fPointZ_low )
    {
      m_fDirection_high = HIWORD(lpPktBase->m_MotionInfo.m_fDirection);
      m_fVelocity_low = LOWORD(lpPktBase->m_MotionInfo.m_fVelocity);
      v17 = m_lpCharacter->m_eCellLoginStatus == MASTER;
      Pos.m_fPointX = (double)LOWORD(lpPktBase->m_MotionInfo.m_fDirection) * (1.0 / 10.0);
      Pos.m_fPointY = (double)m_fDirection_high * (1.0 / 10.0);
      Pos.m_fPointZ = (double)m_fVelocity_low * (1.0 / 10.0);
      if ( v17 )
      {
        v8 = m_wAction == 10 && HIBYTE(lpPktBase->m_MotionInfo.m_wAction) == 10;
        CAggresiveCreature::MoveTo(m_lpCharacter, &Pos, v8);
        m_dwRideArmsCID = m_lpCharacter->m_dwRideArmsCID;
        ++m_lpCharacter->m_cMoveUpdateExCount;
        GameClientDispatcha = 0;
        if ( m_dwRideArmsCID )
        {
          if ( m_fPointZ_low == m_dwRideArmsCID )
          {
            Instance = CSiegeObjectMgr::GetInstance();
            SiegeObject = CSiegeObjectMgr::GetSiegeObject(Instance, m_fPointZ_low);
            GameClientDispatcha = (CGameClientDispatch *)SiegeObject;
            if ( SiegeObject )
              CAggresiveCreature::MoveTo(SiegeObject, &Pos, 0);
          }
        }
        lpArms = m_lpCharacter->m_cMoveUpdateExCount;
        Time = timeGetTime();
        m_dwLastUpdateExTime = m_lpCharacter->m_dwLastUpdateExTime;
        m_lpCharacter->m_dwLastUpdateExTime = Time;
        v14 = 1;
        switch ( cUAct )
        {
          case 0u:
          case 1u:
          case 8u:
          case 0xAu:
            v14 = 0;
            break;
          default:
            break;
        }
        if ( Time - m_dwLastUpdateExTime > 0x32 || v14 )
        {
          m_lpCell = m_lpCharacter->m_CellPos.m_lpCell;
          if ( m_lpCell )
          {
            v16 = CCell::GetNearCellCharacterNum(m_lpCharacter->m_CellPos.m_lpCell);
            if ( v16 < 0x64 || (v16 >= 0xC8 ? (v17 = (lpArms & 1) == 0) : (v17 = (lpArms & 3) == 0), !v17 || v14) )
            {
              m_fDirection_low = LODWORD(lpPktBase->m_MotionInfo.m_fDirection);
              v19 = LODWORD(lpPktBase->m_MotionInfo.m_fVelocity);
              pktMVEx.m_dwCID = dwCID;
              *(_DWORD *)&pktMVEx.m_NetworkPos.m_usXPos = m_fDirection_low;
              LOBYTE(m_fDirection_low) = lpPktBase->m_MotionInfo.m_wAction;
              *(ServerInfo *)&pktMVEx.m_NetworkPos.m_usZPos = v19;
              LOBYTE(v19.SrvState.wError) = HIBYTE(lpPktBase->m_MotionInfo.m_wAction);
              pktMVEx.m_cUAct = m_fDirection_low;
              pktMVEx.m_cLAct = v19.SrvState.wError;
              if ( PacketWrap::WrapCrypt((char *)&pktMVEx, 0x1Au, 0xBu, dwCID) )
              {
                if ( m_lpCharacter->m_dwRideArmsCID && GameClientDispatcha )
                  CSiegeObject::SendToRadiusCell((CSiegeObject *)GameClientDispatcha, (char *)&pktMVEx, 0x1Au, 0xBu);
                else
                  CCell::SendAllNearCellCharacter(m_lpCell, (char *)&pktMVEx, 0x1Au, 0xBu);
                v20 = (CCharacterParty *)m_lpCharacter->GetParty(m_lpCharacter);
                if ( v20 )
                {
                  pktMVEx.m_dwCID = m_lpCharacter->m_dwCID;
                  CCharacterParty::SendAllLoggedMember(v20, (char *)&pktMVEx, 0x1Au, pktMVEx.m_dwCID, 0xBu);
                }
              }
            }
          }
        }
      }
    }
  }
  return 1;
}

//----- (0048D090) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSkillErase(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ecx
  unsigned __int8 v4; // bl
  unsigned int v5; // esi
  unsigned __int16 m_CodePage; // di

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x13 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = BYTE2(lpPktBase[1].m_CodePage);
      v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_CodePage = lpPktBase[1].m_CodePage;
      if ( !CCharacter::SkillErase(m_lpCharacter, v4) )
      {
        GameClientSendPacket::SendCharSkillCommand(&GameClientDispatch->m_SendStream, v5, 0x18u, v4, m_CodePage, 1u);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSkillErase",
          aDWorkRylSource_18,
          40,
          aCid0x08x_150,
          v5,
          m_CodePage,
          v4);
      }
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048D140) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharInstallSocket(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  Item::CItem *Item; // eax
  Item::CEquipment *v5; // ebp
  Item::CItem *v6; // eax
  Item::CItem *v7; // esi
  unsigned __int16 v8; // bx
  TakeType v9; // [esp-14h] [ebp-34h]
  int EquipPos; // [esp+10h] [ebp-10h]
  unsigned int dwCharID; // [esp+14h] [ebp-Ch]
  int v12; // [esp+1Bh] [ebp-5h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x14 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  dwCharID = *(_DWORD *)&lpPktBase[1].m_StartBit;
  LOWORD(EquipPos) = lpPktBase[1].m_CodePage;
  LOWORD(lpPktBase) = HIWORD(lpPktBase[1].m_CodePage);
  Item = CCharacter::GetItem(m_lpCharacter, EquipPos);
  if ( Item )
    v5 = (Item->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? (Item::CEquipment *)Item : 0;
  else
    v5 = 0;
  v6 = CCharacter::GetItem(m_lpCharacter, (int)lpPktBase);
  v7 = v6;
  if ( v5 )
  {
    if ( v6 )
      v8 = Item::CEquipment::InstallSocket(v5, v6);
    else
      v8 = 3;
  }
  else
  {
    v8 = 2;
  }
  HIWORD(v12) = EquipPos;
  LOWORD(v12) = (_WORD)lpPktBase;
  *(_DWORD *)&v9.m_srcPos = v12;
  v9.m_cNum = 1;
  GAMELOG::LogInstallSocket(m_lpCharacter, v9, v7, v5, (unsigned __int8)v8);
  if ( v7 && !v7->m_ItemData.m_cNumOrDurability )
  {
    if ( !CCharacter::RemoveItem(m_lpCharacter, (int)lpPktBase) )
    {
      v8 = 6;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharInstallSocket",
        aDWorkRylSource_63,
        70,
        aCid0x08x_106,
        m_lpCharacter->m_dwCID);
      goto LABEL_21;
    }
    ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
  }
  if ( !v8 )
  {
    if ( (EquipPos & 0xF) == 1 )
      CCharacter::CalculateStatusData(m_lpCharacter, 0);
    return GameClientSendPacket::SendCharInstallSocket(
             &GameClientDispatch->m_SendStream,
             dwCharID,
             (Item::ItemPos)EquipPos,
             (Item::ItemPos)lpPktBase,
             v5,
             v8);
  }
LABEL_21:
  v5 = 0;
  return GameClientSendPacket::SendCharInstallSocket(
           &GameClientDispatch->m_SendStream,
           dwCharID,
           (Item::ItemPos)EquipPos,
           (Item::ItemPos)lpPktBase,
           v5,
           v8);
}
// 48D1BB: variable 'EquipPos' is possibly undefined

//----- (0048D2E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharItemChemical(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int v4; // ebx
  Item::CItem *Item; // edi
  Item::CItem *v6; // ebp
  Item::CItem *v7; // eax
  Item::CItem *v8; // ebp
  unsigned __int16 v9; // bx
  Item::ItemPos m_ItemPos; // ax
  unsigned __int8 ChemicalResult; // al
  Item::CItem *v12; // eax
  int TargetPos; // [esp+0h] [ebp-2Ch]
  int PickkingPos; // [esp+4h] [ebp-28h]
  unsigned int dwCharID; // [esp+8h] [ebp-24h]
  char cPickkingItemNum; // [esp+Ch] [ebp-20h]
  unsigned __int16 wPickkingItemID; // [esp+10h] [ebp-1Ch]
  int HoldPos; // [esp+14h] [ebp-18h]
  Item::CItem *lpResultItem; // [esp+18h] [ebp-14h]
  Item::CItem *lpTargetItem; // [esp+1Ch] [ebp-10h]
  Item::ChemicalInfo chemicalInfo; // [esp+20h] [ebp-Ch] BYREF
  unsigned __int8 lpPktBasea; // [esp+34h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  LOWORD(PickkingPos) = lpPktBase[1].m_CodePage;
  dwCharID = v4;
  LOWORD(TargetPos) = HIWORD(lpPktBase[1].m_CodePage);
  lpPktBasea = lpPktBase[1].m_SrvInfo.dwServerInfo;
  Item = CCharacter::GetItem(m_lpCharacter, PickkingPos);
  v6 = CCharacter::GetItem(m_lpCharacter, TargetPos);
  lpTargetItem = v6;
  Item::ChemicalInfo::ChemicalInfo(&chemicalInfo);
  wPickkingItemID = 0;
  cPickkingItemNum = 0;
  if ( !Item || !v6 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharItemChemical",
      aDWorkRylSource_63,
      119,
      aCid0x08x_15,
      v4,
      PickkingPos & 0xF,
      (unsigned __int16)PickkingPos >> 4,
      TargetPos & 0xF,
      (unsigned __int16)TargetPos >> 4);
    m_lpCharacter->m_Inventory.DumpItemInfo(&m_lpCharacter->m_Inventory);
    goto LABEL_30;
  }
  if ( lpPktBasea && lpPktBasea < Item->m_ItemData.m_cNumOrDurability )
  {
    LOWORD(HoldPos) = 38;
    if ( CCharacter::GetItem(m_lpCharacter, HoldPos) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharItemChemical",
        aDWorkRylSource_63,
        133,
        aCid0x08x_151,
        v4);
LABEL_30:
      v9 = 1;
      return GameClientSendPacket::SendCharItemChemical(
               &GameClientDispatch->m_SendStream,
               dwCharID,
               (Item::ItemPos)PickkingPos,
               (Item::ItemPos)TargetPos,
               wPickkingItemID,
               cPickkingItemNum,
               0,
               v9);
    }
    Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, Item->m_ItemInfo);
    v8 = v7;
    if ( !v7 )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharItemChemical",
        aDWorkRylSource_63,
        141,
        aCid0x08x_301,
        v4);
      goto LABEL_30;
    }
    v7->m_ItemData.m_cNumOrDurability = lpPktBasea;
    if ( !CCharacter::SetItem(m_lpCharacter, HoldPos, v7) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharItemChemical",
        aDWorkRylSource_63,
        150,
        aCid0x08x_321,
        v4);
      v9 = 1;
      ((void (__thiscall *)(Item::CItem *, int))v8->~Item::CItem)(v8, 1);
      return GameClientSendPacket::SendCharItemChemical(
               &GameClientDispatch->m_SendStream,
               dwCharID,
               (Item::ItemPos)PickkingPos,
               (Item::ItemPos)TargetPos,
               wPickkingItemID,
               cPickkingItemNum,
               0,
               v9);
    }
    Item->m_ItemData.m_cNumOrDurability -= lpPktBasea;
    m_ItemPos = v8->m_ItemData.m_ItemPos;
    Item = v8;
    v6 = lpTargetItem;
    LOWORD(PickkingPos) = m_ItemPos;
  }
  chemicalInfo.m_wPickkingItemID = Item->m_ItemData.m_usProtoTypeID;
  chemicalInfo.m_cPickkingItemNum = Item->m_ItemData.m_cNumOrDurability;
  chemicalInfo.m_wTargetItemID = v6->m_ItemData.m_usProtoTypeID;
  chemicalInfo.m_cTargetItemNum = v6->m_ItemData.m_cNumOrDurability;
  ChemicalResult = Item::CItemMgr::GetChemicalResult(CSingleton<Item::CItemMgr>::ms_pSingleton, &chemicalInfo);
  v9 = ChemicalResult;
  if ( !ChemicalResult )
  {
    if ( chemicalInfo.m_cPickkingItemNum )
    {
      Item->m_ItemData.m_cNumOrDurability = chemicalInfo.m_cPickkingItemNum;
      wPickkingItemID = chemicalInfo.m_wPickkingItemID;
      cPickkingItemNum = chemicalInfo.m_cPickkingItemNum;
    }
    else
    {
      if ( CCharacter::RemoveItem(m_lpCharacter, PickkingPos) != 1 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharItemChemical",
          aDWorkRylSource_63,
          186,
          aCid0x08x_124,
          dwCharID);
        goto LABEL_30;
      }
      ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
    }
    if ( chemicalInfo.m_cTargetItemNum )
    {
      v6->m_ItemData.m_cNumOrDurability = chemicalInfo.m_cTargetItemNum;
      CCharacter::RemoveItem(m_lpCharacter, TargetPos);
      CCharacter::SetItem(m_lpCharacter, PickkingPos, v6);
      wPickkingItemID = chemicalInfo.m_wTargetItemID;
      cPickkingItemNum = chemicalInfo.m_cTargetItemNum;
    }
    else
    {
      if ( CCharacter::RemoveItem(m_lpCharacter, TargetPos) != 1 )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharItemChemical",
          aDWorkRylSource_63,
          209,
          aCid0x08x_307,
          dwCharID);
        goto LABEL_30;
      }
      ((void (__thiscall *)(Item::CItem *, int))v6->~Item::CItem)(v6, 1);
    }
    Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, chemicalInfo.m_wResultItemID);
    v12->m_ItemData.m_cNumOrDurability = chemicalInfo.m_cResultItemNum;
    lpResultItem = v12;
    CCharacter::SetItem(m_lpCharacter, TargetPos, v12);
    return GameClientSendPacket::SendCharItemChemical(
             &GameClientDispatch->m_SendStream,
             dwCharID,
             (Item::ItemPos)PickkingPos,
             (Item::ItemPos)TargetPos,
             wPickkingItemID,
             cPickkingItemNum,
             lpResultItem,
             v9);
  }
  return GameClientSendPacket::SendCharItemChemical(
           &GameClientDispatch->m_SendStream,
           dwCharID,
           (Item::ItemPos)PickkingPos,
           (Item::ItemPos)TargetPos,
           wPickkingItemID,
           cPickkingItemNum,
           0,
           v9);
}
// 48D35B: variable 'PickkingPos' is possibly undefined
// 48D369: variable 'TargetPos' is possibly undefined
// 48D3C5: variable 'HoldPos' is possibly undefined
// 48D3ED: variable 'v7' is possibly undefined
// 48D583: variable 'v12' is possibly undefined

//----- (0048D620) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharUpgradeItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // edi
  PktBase *m_dwCID; // ecx
  Item::CItem *Item; // esi
  Item::CItem *v6; // eax
  Item::CItem *v7; // ebx
  Item::CEquipment *v8; // ecx
  unsigned __int16 v9; // bp
  Item::CItem *v10; // eax
  unsigned int dwUsedGold; // [esp+8h] [ebp-Ch] BYREF
  int cCurrentMineralNum; // [esp+Ch] [ebp-8h]
  Item::ItemPos SrcPos[2]; // [esp+10h] [ebp-4h]
  PktBase *lpPktBasea; // [esp+1Ch] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x10 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_dwCID = (PktBase *)m_lpCharacter->m_dwCID;
  SrcPos[0] = (Item::ItemPos)7;
  lpPktBasea = m_dwCID;
  dwUsedGold = 0;
  LOBYTE(cCurrentMineralNum) = 0;
  Item = CCharacter::GetItem(m_lpCharacter, *(int *)SrcPos);
  SrcPos[0] = (Item::ItemPos)23;
  v6 = CCharacter::GetItem(m_lpCharacter, *(int *)SrcPos);
  v7 = v6;
  if ( !Item )
  {
    Item = 0;
    v9 = 2;
    v7 = 0;
    goto LABEL_28;
  }
  v8 = (Item->m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 ? (Item::CEquipment *)Item : 0;
  if ( v6 )
  {
    if ( !v8 )
    {
      Item = 0;
      v9 = 4;
      v7 = 0;
      goto LABEL_28;
    }
    v9 = Item::CEquipment::UpgradeItem(v8, v6, m_lpCharacter->m_DBData.m_Info.Gold, &dwUsedGold);
    if ( dwUsedGold )
      CCharacter::DeductGold(m_lpCharacter, dwUsedGold, 0);
    LOBYTE(cCurrentMineralNum) = v7->m_ItemData.m_cNumOrDurability;
    if ( !(_BYTE)cCurrentMineralNum )
    {
      SrcPos[0] = v7->m_ItemData.m_ItemPos;
      if ( !CCharacter::RemoveItem(m_lpCharacter, *(int *)SrcPos) )
      {
        v9 = 6;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharUpgradeItem",
          aDWorkRylSource_63,
          290,
          aCid0x08x_210,
          lpPktBasea);
LABEL_22:
        Item = 0;
        v7 = 0;
        goto LABEL_28;
      }
      ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
      v7 = 0;
    }
    if ( v9 == 14 )
    {
      SrcPos[0] = (Item::ItemPos)7;
      if ( !CCharacter::RemoveItem(m_lpCharacter, *(int *)SrcPos) )
      {
        v9 = 8;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharUpgradeItem",
          aDWorkRylSource_63,
          306,
          aCid0x08x_283,
          lpPktBasea);
        goto LABEL_22;
      }
      ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
      Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, 0x835u);
      Item = v10;
      if ( !v10 )
      {
        v9 = 9;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharUpgradeItem",
          aDWorkRylSource_63,
          316,
          aCid0x08x_326,
          lpPktBasea);
        goto LABEL_22;
      }
      SrcPos[0] = (Item::ItemPos)7;
      if ( !CCharacter::SetItem(m_lpCharacter, *(int *)SrcPos, v10) )
      {
        v9 = 10;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharUpgradeItem",
          aDWorkRylSource_63,
          324,
          aCid0x08x_275,
          lpPktBasea);
        ((void (__thiscall *)(Item::CItem *, int))Item->~Item::CItem)(Item, 1);
        Item = 0;
        v7 = 0;
        goto LABEL_28;
      }
    }
    else if ( v9 )
    {
      Item = 0;
      v7 = 0;
      goto LABEL_28;
    }
    v9 = 0;
    goto LABEL_28;
  }
  v9 = 3;
  Item = 0;
LABEL_28:
  GAMELOG::LogUpgradeItem(m_lpCharacter, m_lpCharacter->m_DBData.m_Info.Gold, dwUsedGold, Item, v7, v9);
  return GameClientSendPacket::SendCharUpgradeItem(
           &GameClientDispatch->m_SendStream,
           (unsigned int)lpPktBasea,
           m_lpCharacter->m_DBData.m_Info.Gold,
           Item,
           cCurrentMineralNum,
           v9);
}
// 48D7B5: variable 'v10' is possibly undefined



//----- (0048D890) --------------------------------------------------------
unsigned int __thiscall Item::CItem::GetSellPrice(Item::CItem *this)
{
  unsigned int result; // eax
  unsigned int m_dwPrice; // edx
  const Item::ItemInfo *m_ItemInfo; // eax
  unsigned int v4; // edx

  result = this->m_dwStallPrice;
  if ( !result )
  {
    m_dwPrice = this->m_dwPrice;
    if ( m_dwPrice )
    {
      m_ItemInfo = this->m_ItemInfo;
      if ( (m_ItemInfo->m_DetailData.m_dwFlags & 1) == 1 )
      {
        if ( (m_ItemInfo->m_DetailData.m_dwFlags & 8) != 8 )
        {
          v4 = (m_dwPrice * this->m_ItemData.m_cNumOrDurability / this->m_cMaxNumOrDurability) >> 1;
          goto LABEL_9;
        }
      }
      else if ( m_ItemInfo->m_DetailData.m_cItemType == 23 )
      {
        v4 = m_dwPrice >> 2;
        goto LABEL_9;
      }
      v4 = m_dwPrice >> 1;
LABEL_9:
      result = 1;
      if ( v4 )
        return v4;
    }
  }
  return result;
}

//----- (0048D8F0) --------------------------------------------------------
bool __thiscall Item::CDepositContainer::Login(
        Item::CDepositContainer *this,
        const char *szPassword,
        unsigned int nPasswordLength,
        char bSavePassword)
{
  int v5; // ecx

  v5 = nPasswordLength;
  if ( nPasswordLength > 4 )
    v5 = 4;
  if ( !memcmp((const char *)this->m_szPassword, szPassword, v5) )
  {
    this->m_bLoginSuccess = 1;
    if ( bSavePassword )
      this->m_dwTabFlag |= 0x80000000;
    this->m_dwTabFlag |= 0x40000000u;
  }
  return this->m_bLoginSuccess;
}

//----- (0048D940) --------------------------------------------------------
char __thiscall Item::CDepositContainer::BuyTab(Item::CDepositContainer *this, unsigned __int8 cTabNum)
{
  if ( cTabNum >= this->m_nTabNum )
    return 0;
  this->m_dwTabFlag |= 1 << cTabNum;
  return 1;
}

//----- (0048D960) --------------------------------------------------------
BOOL __cdecl IsPutDeposit(unsigned __int8 cSrc, unsigned __int8 cDst)
{
  return cSrc != 9 && cDst == 9;
}

//----- (0048D980) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharTakeItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned __int16 v4; // di
  int v5; // ebp
  Item::CItem *Item; // eax
  const Item::CItem *v7; // ebx
  Item::CItemContainer *ItemContainer; // eax
  unsigned int Gold; // ebp
  int v10; // [esp-Ch] [ebp-34h]
  char *v11; // [esp-8h] [ebp-30h]
  __int64 v12; // [esp-8h] [ebp-30h]
  int v13; // [esp-4h] [ebp-2Ch]
  unsigned int dwTakeMoney; // [esp+10h] [ebp-18h]
  TakeType takeType; // [esp+14h] [ebp-14h]
  GAMELOG::sMinItemInfo minItemInfo; // [esp+1Ch] [ebp-Ch] BYREF
  char lpPktBasea; // [esp+30h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  v4 = 0;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  v5 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  *(_DWORD *)&takeType.m_srcPos = lpPktBase[1].m_CodePage;
  takeType.m_cNum = lpPktBase[1].m_SrvInfo.dwServerInfo;
  dwTakeMoney = 0;
  Item = CCharacter::GetItem(m_lpCharacter, *(int *)&takeType.m_srcPos);
  v7 = Item;
  lpPktBasea = 0;
  if ( !Item )
  {
    v4 = 2;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharTakeItem",
      aDWorkRylSource_9,
      74,
      aCid0x08x_236,
      m_lpCharacter->m_dwCID,
      *(_BYTE *)&takeType.m_srcPos & 0xF,
      *(_WORD *)&takeType.m_srcPos >> 4,
      *(_BYTE *)&takeType.m_dstPos & 0xF,
      *(_WORD *)&takeType.m_dstPos >> 4);
    ItemContainer = CCharacter::GetItemContainer(m_lpCharacter, *(_BYTE *)&takeType.m_srcPos & 0xF);
    if ( ItemContainer )
      ItemContainer->DumpItemInfo(ItemContainer);
    goto LABEL_16;
  }
  if ( (*(_WORD *)&Item->m_itemPos_Real & 0xF) == 9 || (*(_BYTE *)&takeType.m_dstPos & 0xF) != 9 )
  {
    lpPktBasea = 0;
  }
  else
  {
    lpPktBasea = 1;
    if ( !m_lpCharacter->m_Deposit.m_bLoginSuccess )
    {
      v13 = v5;
      v11 = aCid0x08x_339;
      v10 = 91;
LABEL_15:
      v4 = 2;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharTakeItem",
        aDWorkRylSource_9,
        v10,
        v11,
        v13);
      goto LABEL_16;
    }
    dwTakeMoney = 20 * m_lpCharacter->m_CreatureStatus.m_nLevel;
    if ( m_lpCharacter->m_DBData.m_Info.Gold < dwTakeMoney )
    {
      v13 = v5;
      v11 = aCid0x08x_127;
      v10 = 99;
      goto LABEL_15;
    }
  }
LABEL_16:
  memset(&minItemInfo, 0, sizeof(minItemInfo));
  if ( !v4 )
  {
    GAMELOG::sMinItemInfo::InitMinItemInfo(&minItemInfo, v7);
    LODWORD(v12) = *(_DWORD *)&takeType.m_srcPos;
    BYTE4(v12) = takeType.m_cNum;
    if ( CCharacter::MoveItem(m_lpCharacter, v12) )
    {
      if ( lpPktBasea )
      {
        Gold = m_lpCharacter->m_DBData.m_Info.Gold;
        CCharacter::DeductGold(m_lpCharacter, dwTakeMoney, 0);
        GAMELOG::LogTakeGold(m_lpCharacter, Gold, m_lpCharacter->m_DBData.m_Info.Gold, dwTakeMoney, 2u, 2u, 1u, 0);
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharTakeItem",
        aDWorkRylSource_9,
        115,
        aCid0x08x_48,
        m_lpCharacter->m_dwCID,
        *(_BYTE *)&takeType.m_srcPos & 0xF,
        *(_WORD *)&takeType.m_srcPos >> 4,
        *(_BYTE *)&takeType.m_dstPos & 0xF,
        *(_WORD *)&takeType.m_dstPos >> 4);
      v4 = 2;
    }
  }
  GAMELOG::LogMoveItem(m_lpCharacter, takeType, &minItemInfo, v4);
  return GameClientSendPacket::SendCharTakeItem(&GameClientDispatch->m_SendStream, m_lpCharacter->m_dwCID, takeType, v4);
}
// 48DB1F: variable 'v12' is possibly undefined

//----- (0048DBF0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSwapItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int m_CodePage; // ebx
  unsigned __int16 v5; // bp
  Item::CItem *Item; // eax
  unsigned int v7; // edi
  Item::ItemPos m_itemPos_Real; // ax
  unsigned int v9; // edi
  TakeType v10; // [esp-1Ch] [ebp-4Ch]
  TakeType v11; // [esp-14h] [ebp-44h]
  __int64 v12; // [esp-10h] [ebp-40h]
  Item::CItem *lpSrcItem; // [esp+10h] [ebp-20h]
  unsigned int dwCID; // [esp+18h] [ebp-18h]
  Item::CItem *lpDstItem; // [esp+1Ch] [ebp-14h]
  TakeType swapDst; // [esp+20h] [ebp-10h]
  unsigned __int8 swapSrc_4; // [esp+2Ch] [ebp-4h]
  char lpPktBasea; // [esp+38h] [ebp+8h]
  PktBase *lpPktBaseb; // [esp+38h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1A )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_CodePage = lpPktBase[1].m_CodePage;
  swapSrc_4 = lpPktBase[1].m_SrvInfo.dwServerInfo;
  swapDst.m_cNum = lpPktBase[2].m_Cmd;
  v5 = 0;
  *(ServerInfo *)&swapDst.m_srcPos = *(ServerInfo *)((char *)&lpPktBase[1].m_SrvInfo + 1);
  lpSrcItem = CCharacter::GetItem(m_lpCharacter, m_CodePage);
  Item = CCharacter::GetItem(m_lpCharacter, *(int *)&swapDst.m_srcPos);
  v7 = *(_DWORD *)&lpPktBase[1].m_StartBit;
  lpDstItem = Item;
  dwCID = v7;
  if ( !lpSrcItem )
  {
    v5 = 2;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSwapItem",
      aDWorkRylSource_9,
      161,
      aCid0x08xSwap,
      v7,
      m_CodePage & 0xF,
      (unsigned __int16)m_CodePage >> 4);
    goto LABEL_23;
  }
  if ( Item )
  {
    m_itemPos_Real = lpSrcItem->m_itemPos_Real;
    if ( (*(_BYTE *)&m_itemPos_Real & 0xF) != 9 && (BYTE2(m_CodePage) & 0xF) == 9
      || IsPutDeposit(*(_BYTE *)&m_itemPos_Real & 0xF, *(_BYTE *)&swapDst.m_dstPos & 0xF) )
    {
      lpPktBasea = 1;
      if ( !m_lpCharacter->m_Deposit.m_bLoginSuccess )
      {
        v5 = 2;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSwapItem",
          aDWorkRylSource_9,
          179,
          aCid0x08x_329,
          v7);
        goto LABEL_23;
      }
      v9 = 20 * m_lpCharacter->m_CreatureStatus.m_nLevel;
      if ( m_lpCharacter->m_DBData.m_Info.Gold < v9 )
      {
        v5 = 4;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharSwapItem",
          aDWorkRylSource_9,
          187,
          aCid0x08x_127,
          dwCID);
        goto LABEL_23;
      }
    }
    else
    {
      v9 = 0;
      lpPktBasea = 0;
    }
    LODWORD(v12) = m_CodePage;
    BYTE4(v12) = swapSrc_4;
    if ( CCharacter::SwapItem(m_lpCharacter, v12, swapDst) )
    {
      if ( lpPktBasea )
      {
        lpPktBaseb = (PktBase *)m_lpCharacter->m_DBData.m_Info.Gold;
        CCharacter::DeductGold(m_lpCharacter, v9, 0);
        GAMELOG::LogTakeGold(
          m_lpCharacter,
          (unsigned int)lpPktBaseb,
          m_lpCharacter->m_DBData.m_Info.Gold,
          v9,
          2u,
          2u,
          1u,
          0);
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharSwapItem",
        aDWorkRylSource_9,
        199,
        aCid0x08x_308,
        m_lpCharacter->m_dwCID,
        m_CodePage & 0xF,
        (unsigned __int16)m_CodePage >> 4,
        BYTE2(m_CodePage) & 0xF,
        HIWORD(m_CodePage) >> 4,
        *(_BYTE *)&swapDst.m_srcPos & 0xF,
        *(_WORD *)&swapDst.m_srcPos >> 4,
        *(_BYTE *)&swapDst.m_dstPos & 0xF,
        *(_WORD *)&swapDst.m_dstPos >> 4);
      v5 = 4;
    }
    goto LABEL_23;
  }
  v5 = 3;
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "GameClientParsePacket::ParseCharSwapItem",
    aDWorkRylSource_9,
    167,
    aCid0x08xSwap_0,
    v7,
    *(_BYTE *)&swapDst.m_srcPos & 0xF,
    *(_WORD *)&swapDst.m_srcPos >> 4);
LABEL_23:
  *(_DWORD *)&v10.m_srcPos = m_CodePage;
  v10.m_cNum = swapSrc_4;
  GAMELOG::LogSwapItem(m_lpCharacter, v10, swapDst, lpSrcItem, lpDstItem, v5);
  *(_DWORD *)&v11.m_srcPos = m_CodePage;
  v11.m_cNum = swapSrc_4;
  return GameClientSendPacket::SendCharSwapItem(
           &GameClientDispatch->m_SendStream,
           m_lpCharacter->m_dwCID,
           v11,
           swapDst,
           v5);
}
// 48DD6B: variable 'v12' is possibly undefined

//----- (0048DF00) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharRepairItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  PktBase *v2; // edi
  CCharacter *m_lpCharacter; // esi
  PktBase *v5; // ebp
  unsigned __int16 v6; // bx
  Item::CItem *Item; // eax
  unsigned int v8; // edi
  Item::CItem *lpItem; // [esp+4h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+10h] [ebp+8h]

  v2 = lpPktBase;
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x16 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      LOWORD(lpPktBase) = lpPktBase[1].m_SrvInfo.SrvState.wError;
      v5 = lpPktBase;
      v6 = 0;
      Item = CCharacter::GetItem(m_lpCharacter, (int)lpPktBase);
      lpItem = Item;
      if ( Item )
        lpPktBasea = Item->m_ItemData.m_cNumOrDurability;
      else
        lpPktBasea = 0;
      v8 = CCharacter::RepairItem(m_lpCharacter, *(_DWORD *)&v2[1].m_StartBit, (int)v5);
      if ( !v8 )
        v6 = 1;
      GAMELOG::LogRepairItem(m_lpCharacter, lpItem, v8, lpPktBasea, v6);
      return GameClientSendPacket::SendCharRepairItem(
               &GameClientDispatch->m_SendStream,
               m_lpCharacter->m_dwCID,
               v8,
               (Item::ItemPos)v5,
               v6);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048DFD0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharRepairAllItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned __int16 v4; // di
  unsigned int v5; // eax

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x14 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = 0;
      v5 = 0;
      if ( lpPktBase[1].m_CodePage > m_lpCharacter->m_DBData.m_Info.Gold
        || (v5 = CCharacter::RepairAllItem(m_lpCharacter, *(_DWORD *)&lpPktBase[1].m_StartBit)) == 0 )
      {
        v4 = 1;
      }
      return GameClientSendPacket::SendCharRepairAllItem(
               &GameClientDispatch->m_SendStream,
               m_lpCharacter->m_dwCID,
               v5,
               v4);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048E060) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharUseItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  PktBase *v2; // ebp
  CCharacter *m_lpCharacter; // edi
  PktBase *v5; // ebx
  Item::CItem *Item; // eax
  Item::CItem *v7; // esi
  unsigned __int16 lpPktBasea; // [esp+10h] [ebp+8h]

  v2 = lpPktBase;
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x16 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      LOWORD(lpPktBase) = lpPktBase[1].m_SrvInfo.SrvState.wError;
      v5 = lpPktBase;
      Item = CCharacter::GetItem(m_lpCharacter, (int)lpPktBase);
      if ( Item )
        v7 = (Item->m_ItemInfo->m_DetailData.m_dwFlags & 2) == 2 ? Item : 0;
      else
        v7 = 0;
      lpPktBasea = 0;
      if ( !v7 || (m_lpCharacter->m_dwStatusFlag & 0x2000000) != 0 )
      {
        GAMELOG::LogUseItem(m_lpCharacter, (Item::ItemPos)v5, v7, 2u);
        return GameClientSendPacket::SendCharUseItem(
                 &GameClientDispatch->m_SendStream,
                 *(_DWORD *)&v2[1].m_StartBit,
                 v2[1].m_CodePage,
                 (Item::ItemPos)v5,
                 2u);
      }
      else
      {
        if ( CCharacter::RemoveItem(m_lpCharacter, (int)v5) == 1 && (*(_WORD *)&v7->m_itemPos_Real & 0xF) == 0xA )
          ((void (__thiscall *)(Item::CStallContainer *, _WORD))m_lpCharacter->m_Stall.RemoveItem)(
            &m_lpCharacter->m_Stall,
            *(_WORD *)&v7->m_itemPos_Real);
        if ( !Item::CUseItem::Use((Item::CUseItem *)v7, m_lpCharacter) )
          lpPktBasea = 2;
        GAMELOG::LogUseItem(m_lpCharacter, (Item::ItemPos)v5, v7, lpPktBasea);
        if ( v7->m_ItemData.m_cNumOrDurability )
        {
          CCharacter::SetItem(m_lpCharacter, (int)v5, v7);
        }
        else
        {
          ((void (__thiscall *)(Item::CItem *, int))v7->~Item::CItem)(v7, 1);
          v7 = 0;
        }
        if ( v7 == CCharacter::GetItem(m_lpCharacter, (int)v5) )
          return GameClientSendPacket::SendCharUseItem(
                   &GameClientDispatch->m_SendStream,
                   *(_DWORD *)&v2[1].m_StartBit,
                   v2[1].m_CodePage,
                   (Item::ItemPos)v5,
                   lpPktBasea);
        else
          return 1;
      }
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048E1E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharPullDown(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebx
  unsigned __int16 m_CodePage; // cx
  unsigned int v6; // esi
  unsigned __int16 v7; // bp
  Item::CItem *v8; // eax
  Item::CItem *v9; // ebx
  int itemPos; // [esp+10h] [ebp-3Ch]
  unsigned __int8 cNum; // [esp+14h] [ebp-38h]
  CCell *lpCell; // [esp+18h] [ebp-34h]
  CCell::ItemInfo itemInfo; // [esp+1Ch] [ebp-30h] BYREF
  CCharacter *lpPktBasea; // [esp+54h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x13 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    lpPktBasea = m_lpCharacter;
    if ( m_lpCharacter )
    {
      m_CodePage = lpPktBase[1].m_CodePage;
      v6 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      LOWORD(itemPos) = m_CodePage;
      cNum = BYTE2(lpPktBase[1].m_CodePage);
      v7 = 0;
      memset(&itemInfo, 0, 12);
      itemInfo.cNum = 0;
      memset(&itemInfo.UID, 0, 18);
      itemInfo.lpItem = 0;
      if ( (m_CodePage & 0xF) == 9 || (m_CodePage & 0xF) == 8 )
      {
        v7 = 4;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharPullDown",
          aDWorkRylSource_9,
          403,
          aCid0x08x_97,
          v6,
          m_CodePage & 0xF,
          m_CodePage >> 4);
      }
      else
      {
        lpCell = m_lpCharacter->m_CellPos.m_lpCell;
        if ( lpCell )
        {
          v8 = CCharacter::Drop(m_lpCharacter, itemPos, cNum);
          v9 = v8;
          if ( v8 )
          {
            if ( (v8->m_ItemInfo->m_DetailData.m_dwFlags & 8) != 8 || v8->m_ItemData.m_cNumOrDurability )
            {
              if ( !CCell::SetItem(lpCell, v8, &lpPktBasea->m_CurrentPos, &itemInfo, v6, 0, 0, 1u, 0) )
              {
                v7 = 4;
                CServerLog::DetailLog(
                  &g_Log,
                  LOG_ERROR,
                  "GameClientParsePacket::ParseCharPullDown",
                  aDWorkRylSource_9,
                  440,
                  aCid0x08x_287,
                  v6,
                  itemPos & 0xF,
                  (unsigned __int16)itemPos >> 4,
                  cNum);
              }
              CCharacter::CheckTrigger(lpPktBasea, 1u, v9->m_ItemData.m_usProtoTypeID, lpPktBasea->m_CurrentPos, 1);
              m_lpCharacter = lpPktBasea;
            }
            else
            {
              v7 = 5;
              ((void (__thiscall *)(Item::CItem *, int))v8->~Item::CItem)(v8, 1);
              m_lpCharacter = lpPktBasea;
            }
          }
          else
          {
            v7 = 3;
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "GameClientParsePacket::ParseCharPullDown",
              aDWorkRylSource_9,
              422,
              aCid0x08x_109,
              v6,
              itemPos & 0xF,
              (unsigned __int16)itemPos >> 4,
              cNum);
            m_lpCharacter = lpPktBasea;
          }
        }
        else
        {
          v7 = 2;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ParseCharPullDown",
            aDWorkRylSource_9,
            412,
            aCid0x08x_3,
            v6);
        }
      }
      GAMELOG::LogDropItem(m_lpCharacter, (Item::ItemPos)itemPos, itemInfo.lpItem, 0, v7);
      return GameClientSendPacket::SendCharPullDown(
               &GameClientDispatch->m_SendStream,
               v6,
               (Item::ItemPos)itemPos,
               &itemInfo,
               v7);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}
// 48E2E1: variable 'itemPos' is possibly undefined

//----- (0048E460) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharTakeGold(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned int m_dwCID; // ecx
  unsigned int m_CodePage; // edi
  unsigned __int8 dwServerInfo; // al
  unsigned __int8 v7; // bl
  unsigned __int8 v8; // al
  unsigned int v9; // eax
  unsigned int v10; // eax
  unsigned int v11; // [esp-28h] [ebp-34h]
  unsigned int Gold; // [esp-18h] [ebp-24h]
  unsigned __int16 v13; // [esp-14h] [ebp-20h]
  int usError; // [esp+0h] [ebp-Ch] BYREF
  unsigned int dwCID; // [esp+4h] [ebp-8h]
  int cSrcPos; // [esp+8h] [ebp-4h]
  unsigned __int8 lpPktBasea; // [esp+14h] [ebp+8h]

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x15 )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      m_dwCID = m_lpCharacter->m_dwCID;
      m_CodePage = lpPktBase[1].m_CodePage;
      dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
      v7 = dwServerInfo & 0xF;
      v8 = dwServerInfo >> 4;
      LOBYTE(cSrcPos) = v7;
      dwCID = m_dwCID;
      usError = 0;
      lpPktBasea = v8;
      if ( (v7 == 9 || v8 == 9) && !m_lpCharacter->m_Deposit.m_bLoginSuccess )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharTakeGold",
          aDWorkRylSource_9,
          601,
          aCid0x08x_222,
          m_dwCID,
          v7,
          v8);
        usError = 2;
      }
      else if ( !CCharacter::MoveGold(m_lpCharacter, m_CodePage, cSrcPos, v8, (unsigned __int16 *)&usError) )
      {
        Gold = CCharacter::GetGold(m_lpCharacter, lpPktBasea);
        v9 = CCharacter::GetGold(m_lpCharacter, v7);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharTakeGold",
          aDWorkRylSource_9,
          608,
          aCid0x08x_141,
          dwCID,
          v7,
          v9,
          lpPktBasea,
          Gold,
          m_CodePage);
      }
      v13 = usError;
      v11 = CCharacter::GetGold(m_lpCharacter, lpPktBasea);
      v10 = CCharacter::GetGold(m_lpCharacter, v7);
      GAMELOG::LogTakeGold(m_lpCharacter, v10, v11, m_CodePage, v7, lpPktBasea, 0, v13);
      return GameClientSendPacket::SendCharTakeGold(
               &GameClientDispatch->m_SendStream,
               dwCID,
               m_CodePage,
               v7,
               lpPktBasea,
               usError);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}

//----- (0048E5E0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharDepositCmd(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  PktBase *v2; // ebx
  CCharacter *m_lpCharacter; // esi
  unsigned int m_dwCID; // edi
  unsigned __int8 m_CodePage_high; // al
  unsigned int Gold; // ebx
  unsigned int v8; // edi
  CSingleDispatch *DispatchTable; // eax
  int v10; // [esp-1Ch] [ebp-50h]
  int v11; // [esp-18h] [ebp-4Ch]
  unsigned int v12; // [esp-14h] [ebp-48h]
  char *v13; // [esp-14h] [ebp-48h]
  CPacketDispatch *m_lpDispatch; // [esp-10h] [ebp-44h]
  unsigned int v15; // [esp-10h] [ebp-44h]
  unsigned __int16 usError; // [esp+4h] [ebp-30h]
  unsigned int dwCID; // [esp+8h] [ebp-2Ch]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+10h] [ebp-24h] BYREF
  unsigned int dwTabBuyPrice[4]; // [esp+18h] [ebp-1Ch]
  int v20; // [esp+30h] [ebp-4h]

  v2 = lpPktBase;
  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1B )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  m_dwCID = m_lpCharacter->m_dwCID;
  dwCID = m_dwCID;
  usError = 0;
  switch ( BYTE2(lpPktBase[1].m_CodePage) )
  {
    case 1:
      if ( Item::CDepositContainer::Login(
             &m_lpCharacter->m_Deposit,
             (const char *)&lpPktBase[1].m_CodePage + 3,
             4u,
             HIBYTE(lpPktBase[1].m_SrvInfo.dwServerInfo)) )
      {
        return GameClientSendPacket::SendCharDepositCmd(
                 &GameClientDispatch->m_SendStream,
                 BYTE2(v2[1].m_CodePage),
                 0,
                 0,
                 usError);
      }
      return GameClientSendPacket::SendCharDepositCmd(
               &GameClientDispatch->m_SendStream,
               BYTE2(lpPktBase[1].m_CodePage),
               0,
               0,
               3u);
    case 2:
      m_CodePage_high = HIBYTE(lpPktBase[1].m_CodePage);
      Gold = m_lpCharacter->m_DBData.m_Info.Gold;
      dwTabBuyPrice[0] = 0;
      dwTabBuyPrice[1] = 100000;
      dwTabBuyPrice[2] = 1000000;
      dwTabBuyPrice[3] = (unsigned int)&_L20577 + 2;
      if ( m_CodePage_high >= 4u )
      {
        m_lpDispatch = (CPacketDispatch *)m_CodePage_high;
        v12 = m_dwCID;
        v10 = 656;
LABEL_10:
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharDepositCmd",
          aDWorkRylSource_9,
          v10,
          aCid0x08x_158,
          v12,
          m_lpDispatch);
        return GameClientSendPacket::SendCharDepositCmd(
                 &GameClientDispatch->m_SendStream,
                 BYTE2(lpPktBase[1].m_CodePage),
                 0,
                 0,
                 4u);
      }
      v8 = dwTabBuyPrice[m_CodePage_high];
      StoragelpDBAgentDispatch.m_lpDispatch = (CPacketDispatch *)m_CodePage_high;
      if ( Gold >= v8 )
      {
        if ( Item::CDepositContainer::BuyTab(&m_lpCharacter->m_Deposit, m_CodePage_high) )
        {
          CCharacter::DeductGold(m_lpCharacter, v8, 0);
          GAMELOG::LogTakeGold(m_lpCharacter, Gold, m_lpCharacter->m_DBData.m_Info.Gold, v8, 9u, 9u, 2u, 0);
          v2 = lpPktBase;
          return GameClientSendPacket::SendCharDepositCmd(
                   &GameClientDispatch->m_SendStream,
                   BYTE2(v2[1].m_CodePage),
                   0,
                   0,
                   usError);
        }
        m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
        v12 = dwCID;
        v10 = 667;
        goto LABEL_10;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharDepositCmd",
        aDWorkRylSource_9,
        662,
        aCid0x08x_300,
        dwCID,
        Gold,
        v8);
      return GameClientSendPacket::SendCharDepositCmd(
               &GameClientDispatch->m_SendStream,
               BYTE2(lpPktBase[1].m_CodePage),
               0,
               0,
               2u);
    case 4:
      if ( m_lpCharacter->m_Deposit.m_bLoginSuccess )
      {
        CCharacter::DBUpdateForce(m_lpCharacter, UPDATE);
        if ( m_lpCharacter->m_Deposit.m_bLoginSuccess )
          m_lpCharacter->m_Deposit.m_bLoginSuccess = 0;
        return GameClientSendPacket::SendCharDepositCmd(
                 &GameClientDispatch->m_SendStream,
                 BYTE2(v2[1].m_CodePage),
                 0,
                 0,
                 usError);
      }
      return GameClientSendPacket::SendCharDepositCmd(
               &GameClientDispatch->m_SendStream,
               BYTE2(lpPktBase[1].m_CodePage),
               0,
               0,
               1u);
    case 5:
      if ( Item::CDepositContainer::ChangePassword(
             &m_lpCharacter->m_Deposit,
             (const char *)&lpPktBase[1].m_CodePage + 3,
             4u,
             (const char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 3,
             4u) )
      {
        DispatchTable = CDBAgentDispatch::GetDispatchTable();
        CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
        v20 = 0;
        if ( StoragelpDBAgentDispatch.m_lpDispatch )
        {
          if ( GameClientSendPacket::SendCharDepositPasswordToDBAgent(
                 (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8],
                 GameClientDispatch->m_dwUID,
                 (const char *)&lpPktBase[1].m_SrvInfo.dwServerInfo + 3,
                 4u) )
          {
LABEL_26:
            v20 = -1;
            CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
            return GameClientSendPacket::SendCharDepositCmd(
                     &GameClientDispatch->m_SendStream,
                     BYTE2(v2[1].m_CodePage),
                     0,
                     0,
                     usError);
          }
          v15 = m_dwCID;
          v13 = aCid0x08xDbagen_0;
          v11 = 706;
        }
        else
        {
          v15 = m_dwCID;
          v13 = aCid0x08xDbagen_2;
          v11 = 712;
        }
        usError = 1;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "GameClientParsePacket::ParseCharDepositCmd",
          aDWorkRylSource_9,
          v11,
          v13,
          v15);
        goto LABEL_26;
      }
      usError = 3;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ParseCharDepositCmd",
        aDWorkRylSource_9,
        718,
        aCid0x08x_93,
        m_dwCID);
      return GameClientSendPacket::SendCharDepositCmd(
               &GameClientDispatch->m_SendStream,
               BYTE2(v2[1].m_CodePage),
               0,
               0,
               usError);
    default:
      return GameClientSendPacket::SendCharDepositCmd(
               &GameClientDispatch->m_SendStream,
               BYTE2(v2[1].m_CodePage),
               0,
               0,
               usError);
  }
}

//----- (0048E900) --------------------------------------------------------
char __cdecl GameClientParsePacket::ProcessItemSell(CGameClientDispatch *GameClientDispatch, PktTr *lpPktTr)
{
  CCharacter *m_lpCharacter; // ecx
  unsigned __int8 m_cNum; // bl
  unsigned int v5; // edi
  unsigned __int16 v6; // bp
  Item::CItem *Item; // eax
  Item::CItem *v8; // esi
  unsigned __int8 m_cNumOrDurability; // cl
  int v10; // edx
  bool bEraseItem; // [esp+2h] [ebp-1Ah]
  unsigned __int8 v12; // [esp+3h] [ebp-19h]
  int itemPos; // [esp+4h] [ebp-18h]
  unsigned int dwCID; // [esp+8h] [ebp-14h]
  int v15; // [esp+Ch] [ebp-10h]
  CCharacter *lpCharacter; // [esp+10h] [ebp-Ch]
  unsigned int dwNPCID; // [esp+14h] [ebp-8h]

  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  lpCharacter = m_lpCharacter;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktTr->m_Cmd);
    return 0;
  }
  m_cNum = lpPktTr->m_TakeType.m_cNum;
  dwCID = lpPktTr->m_dwCustomerID;
  dwNPCID = lpPktTr->m_dwOwnerID;
  LOWORD(itemPos) = lpPktTr->m_TakeType.m_srcPos;
  v5 = 0;
  v6 = 0;
  Item = CCharacter::GetItem(m_lpCharacter, itemPos);
  v8 = Item;
  bEraseItem = 0;
  if ( !Item )
  {
    v6 = 2;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemSell",
      aDWorkRylSource_9,
      813,
      aCid0x08x_226,
      dwCID,
      itemPos & 0xF,
      (unsigned __int16)itemPos >> 4);
    goto LABEL_14;
  }
  m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability;
  v10 = Item->m_ItemInfo->m_DetailData.m_dwFlags & 8;
  v12 = m_cNumOrDurability;
  if ( (_BYTE)v10 != 8 )
    goto LABEL_8;
  if ( m_cNum > m_cNumOrDurability )
  {
    v6 = 2;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemSell",
      aDWorkRylSource_9,
      807,
      aCid0x08x2d0x04,
      dwCID,
      itemPos & 0xF,
      (unsigned __int16)itemPos >> 4,
      m_cNumOrDurability,
      m_cNum);
    goto LABEL_14;
  }
  if ( m_cNum == m_cNumOrDurability )
LABEL_8:
    bEraseItem = 1;
  if ( (_BYTE)v10 == 8 )
    v15 = m_cNum;
  else
    v15 = 1;
  v5 = v15 * Item::CItem::GetSellPrice(Item);
  CCharacter::AddGold(lpCharacter, v5, 0);
  v8->m_ItemData.m_cNumOrDurability = v12 - m_cNum;
LABEL_14:
  GAMELOG::LogTradeItem(lpCharacter, dwNPCID, v5, v8, (Item::ItemPos)itemPos, 2u, v6);
  if ( bEraseItem )
  {
    if ( CCharacter::RemoveItem(lpCharacter, itemPos) == 1 )
    {
      if ( v8 )
        ((void (__thiscall *)(Item::CItem *, int))v8->~Item::CItem)(v8, 1);
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ProcessItemSell",
        aDWorkRylSource_9,
        830,
        aCid0x08x_116,
        dwCID,
        itemPos & 0xF,
        (unsigned __int16)itemPos >> 4);
    }
  }
  return GameClientSendPacket::SendCharTradeItem(
           &GameClientDispatch->m_SendStream,
           lpCharacter,
           dwNPCID,
           0,
           (Item::ItemPos)itemPos,
           m_cNum,
           v6);
}
// 48E95D: variable 'itemPos' is possibly undefined

//----- (0048EAF0) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __cdecl GameClientParsePacket::ProcessItemBuy(CGameClientDispatch *GameClientDispatch, PktTr *lpPktTr)
{
  CCharacter *m_lpCharacter; // ebp
  Item::CItem *v3; // esi
  signed int m_dwOwnerID; // ebx
  unsigned __int8 m_cCmd; // dl
  unsigned __int16 m_wBuyItemID; // cx
  int v8; // edx
  unsigned __int8 m_cNum; // al
  unsigned __int16 v10; // di
  CCreatureManager *Instance; // eax
  CMsgProc *Creature; // eax
  CMsgProc_vtbl *v13; // edx
  Item::CItem *v14; // eax
  bool v15; // al
  Item::CItemContainer *ItemContainer; // eax
  unsigned int dwPrice; // [esp+18h] [ebp-1Ch] BYREF
  unsigned int dwCustomerID; // [esp+1Ch] [ebp-18h]
  int wBuyItemID; // [esp+20h] [ebp-14h]
  int cCmd; // [esp+24h] [ebp-10h]
  unsigned int dwTraderCID; // [esp+28h] [ebp-Ch]
  _BYTE takeType[6]; // [esp+2Ch] [ebp-8h] OVERLAPPED

  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  v3 = 0;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktTr->m_Cmd);
    return 0;
  }
  m_dwOwnerID = lpPktTr->m_dwOwnerID;
  m_cCmd = lpPktTr->m_cCmd;
  dwCustomerID = lpPktTr->m_dwCustomerID;
  m_wBuyItemID = lpPktTr->m_wBuyItemID;
  LOBYTE(cCmd) = m_cCmd;
  v8 = *(_DWORD *)&lpPktTr->m_TakeType.m_srcPos;
  m_cNum = lpPktTr->m_TakeType.m_cNum;
  LOWORD(wBuyItemID) = m_wBuyItemID;
  *(_DWORD *)takeType = v8;
  takeType[4] = m_cNum;
  dwPrice = 0;
  v10 = 0;
  Instance = CCreatureManager::GetInstance();
  Creature = CCreatureManager::GetCreature(Instance, m_dwOwnerID);
  if ( !Creature )
  {
    v10 = 3;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemBuy",
      aDWorkRylSource_9,
      917,
      aCid0x08x_159,
      dwCustomerID,
      m_dwOwnerID);
    goto LABEL_18;
  }
  v13 = Creature->__vftable;
  dwTraderCID = m_dwOwnerID;
  v14 = (Item::CItem *)((int (__thiscall *)(CMsgProc *, CCharacter *, int, _DWORD, _BYTE, unsigned int *))v13->operator())(
                         Creature,
                         m_lpCharacter,
                         wBuyItemID,
                         *(_DWORD *)takeType,
                         takeType[4],
                         &dwPrice);
  m_dwOwnerID = dwTraderCID;
  v3 = v14;
  if ( !v14 )
  {
    v10 = 4;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemBuy",
      aDWorkRylSource_9,
      911,
      aCid0x08x_4,
      dwCustomerID,
      dwTraderCID);
    goto LABEL_18;
  }
  v15 = CCharacter::SetItem(m_lpCharacter, *(int *)&takeType[2], v14);
  if ( !v15 )
  {
    if ( v15 )
      goto LABEL_18;
    goto LABEL_12;
  }
  if ( (_BYTE)cCmd )
  {
    if ( (_BYTE)cCmd == 1 )
    {
      m_lpCharacter->m_DBData.m_Info.Mileage -= dwPrice;
      goto LABEL_18;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemBuy",
      aDWorkRylSource_9,
      878,
      aCid0x08x_214,
      m_lpCharacter->m_dwCID,
      (unsigned __int8)cCmd);
LABEL_12:
    ItemContainer = CCharacter::GetItemContainer(m_lpCharacter, takeType[2] & 0xF);
    if ( ItemContainer )
      ItemContainer->DumpItemInfo(ItemContainer);
    else
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ProcessItemBuy",
        aDWorkRylSource_9,
        892,
        aCid0x08x_1,
        m_dwOwnerID);
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemBuy",
      aDWorkRylSource_9,
      896,
      aCid0x08x_263,
      dwCustomerID,
      (unsigned __int16)wBuyItemID,
      takeType[2] & 0xF,
      *(unsigned __int16 *)&takeType[2] >> 4);
    ((void (__thiscall *)(Item::CItem *, int))v3->~Item::CItem)(v3, 1);
    v3 = 0;
    dwPrice = 0;
    v10 = 5;
    goto LABEL_18;
  }
  CCharacter::DeductGold(m_lpCharacter, dwPrice, 0);
LABEL_18:
  GAMELOG::LogTradeItem(m_lpCharacter, m_dwOwnerID, dwPrice, v3, *(Item::ItemPos *)&takeType[2], cCmd, v10);
  return GameClientSendPacket::SendCharTradeItem(
           &GameClientDispatch->m_SendStream,
           m_lpCharacter,
           m_dwOwnerID,
           v3,
           *(Item::ItemPos *)&takeType[2],
           takeType[4],
           v10);
}
// 48EAF0: variables would overlap: ^5C.6 and stkvar "takeType" ^5C.5(has user info)

//----- (0048ED30) --------------------------------------------------------
char __cdecl GameClientParsePacket::ProcessItemPickUp(
        CGameClientDispatch *GameClientDispatch,
        CFindItemInfoFromUID nObjectID,
        int itemPos,
        Item::CItem **lppItem,
        unsigned __int16 *usError)
{
  unsigned int m_dwCID; // edi
  unsigned __int16 *v7; // esi
  CCellManager *Instance; // eax
  CCell *Cell; // eax
  unsigned __int16 v10; // ax
  Item::CItem *lpItem; // [esp+4h] [ebp-14h] BYREF
  CCharacter *lpCharacter; // [esp+8h] [ebp-10h]
  int cNum; // [esp+Ch] [ebp-Ch]
  unsigned int dwGold; // [esp+10h] [ebp-8h] BYREF
  CCell *lpCell; // [esp+14h] [ebp-4h]

  lpCharacter = GameClientDispatch->m_lpCharacter;
  if ( !lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, 0);
    return 0;
  }
  m_dwCID = lpCharacter->m_dwCID;
  lpItem = 0;
  LOBYTE(cNum) = 0;
  dwGold = 0;
  if ( (itemPos & 0xF) == 0 || (itemPos & 0xF) == 2 )
  {
    Instance = CCellManager::GetInstance();
    Cell = CCellManager::GetCell(Instance, nObjectID.m_nUID);
    v7 = usError;
    lpCell = Cell;
    if ( Cell )
    {
      switch ( CCell::GetItem(Cell, m_dwCID, nObjectID, &lpItem, &dwGold) )
      {
        case S_SUCCESS:
          if ( dwGold )
          {
            CCharacter::AddGold(lpCharacter, dwGold, 0);
          }
          else if ( lpItem )
          {
            LOBYTE(cNum) = (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 8) == 8
                         ? lpItem->m_ItemData.m_cNumOrDurability
                         : 1;
            if ( !CCharacter::Pickup(lpCharacter, lpItem, itemPos) )
            {
              LOBYTE(cNum) = 0;
              *usError = 4;
              CServerLog::DetailLog(
                &g_Log,
                LOG_ERROR,
                "GameClientParsePacket::ProcessItemPickUp",
                aDWorkRylSource_9,
                977,
                aCid0x08x_67,
                m_dwCID,
                itemPos & 0xF,
                (unsigned __int16)itemPos >> 4);
            }
          }
          break;
        case E_SERVER_ERROR:
          *usError = 1;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessItemPickUp",
            aDWorkRylSource_9,
            989,
            aCid0x08x_288,
            m_dwCID,
            nObjectID.m_nUID);
          break;
        case E_GET_EQUIP_FAILED:
          *usError = 5;
          break;
        case E_GET_GEM_FAILED:
          *usError = 6;
          break;
        case E_NOT_EQUIP:
          *usError = 7;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessItemPickUp",
            aDWorkRylSource_9,
            1004,
            aCid0x08x_71,
            m_dwCID,
            nObjectID.m_nUID,
            7);
          break;
        case E_NOT_GEM:
          *usError = 8;
          break;
        default:
          *usError = 3;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "GameClientParsePacket::ProcessItemPickUp",
            aDWorkRylSource_9,
            1009,
            aCid0x08x_185,
            m_dwCID,
            nObjectID.m_nUID,
            3);
          break;
      }
    }
    else
    {
      *usError = 2;
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "GameClientParsePacket::ProcessItemPickUp",
        aDWorkRylSource_9,
        952,
        aCid0x08x_171,
        m_dwCID,
        nObjectID.m_nUID);
    }
    v10 = *usError;
    if ( *usError != 5 && v10 != 6 )
      GAMELOG::LogPickupItem(lpCharacter, (Item::ItemPos)itemPos, lpItem, dwGold, v10);
    if ( lpItem )
    {
      if ( (lpItem->m_ItemInfo->m_DetailData.m_dwFlags & 8) != 8 || lpItem->m_ItemData.m_cNumOrDurability )
      {
        if ( lpCell )
        {
          if ( !*usError )
          {
LABEL_35:
            *lppItem = lpItem;
            return GameClientSendPacket::SendCharPickUp(
                     &GameClientDispatch->m_SendStream,
                     m_dwCID,
                     nObjectID.m_nUID,
                     dwGold,
                     lpItem,
                     (Item::ItemPos)itemPos,
                     cNum,
                     *v7);
          }
          CCell::SetItem(lpCell, lpItem, &lpCharacter->m_CurrentPos, 0, 0, 0, 0, 1u, 0);
        }
      }
      else
      {
        ((void (__thiscall *)(Item::CItem *, int))lpItem->~Item::CItem)(lpItem, 1);
        lpItem = 0;
      }
    }
  }
  else
  {
    v7 = usError;
    *usError = 4;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ProcessItemPickUp",
      aDWorkRylSource_9,
      943,
      aCid0x08x_286,
      m_dwCID);
  }
  if ( !*v7 )
    goto LABEL_35;
  return GameClientSendPacket::SendCharPickUp(
           &GameClientDispatch->m_SendStream,
           m_dwCID,
           nObjectID.m_nUID,
           dwGold,
           lpItem,
           (Item::ItemPos)itemPos,
           cNum,
           *v7);
}

//----- (0048EFF0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharTradeItem(CGameClientDispatch *GameClientDispatch, PktTr *lpPktBase)
{
  unsigned __int8 m_cCmd; // cl

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x1C )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_cCmd = lpPktBase->m_cCmd;
  if ( m_cCmd <= 1u )
    return GameClientParsePacket::ProcessItemBuy(GameClientDispatch, lpPktBase);
  if ( m_cCmd != 2 )
    return 0;
  return GameClientParsePacket::ProcessItemSell(GameClientDispatch, lpPktBase);
}

//----- (0048F040) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __cdecl GameClientParsePacket::ParseCharPickUp(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  Item::CItem *v2; // ecx
  unsigned int v4; // edx
  unsigned int m_CodePage; // esi
  Item::CItem *itemPos; // [esp+0h] [ebp-4h] OVERLAPPED BYREF

  itemPos = v2;
  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1A )
  {
    if ( GameClientDispatch->m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      m_CodePage = lpPktBase[1].m_CodePage;
      LOWORD(itemPos) = *(_WORD *)&lpPktBase[2].m_StartBit;
      lpPktBase = 0;
      return GameClientParsePacket::ProcessItemPickUp(
               GameClientDispatch,
               (CFindItemInfoFromUID)__PAIR64__(m_CodePage, v4),
               (int)itemPos,
               &itemPos,
               (unsigned __int16 *)&lpPktBase);
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}
// 48F040: variables would overlap: ^1C.4 and stkvar "itemPos" ^1C.2(has user info)

//----- (0048F0C0) --------------------------------------------------------
char __cdecl GameClientParsePacket::ParseCharSplitItem(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // esi
  unsigned __int16 v4; // bx
  Item::CItem *Item; // edx
  int v6; // eax
  int v7; // edi
  unsigned int v8; // edi
  const Item::ItemInfo *m_ItemInfo; // ecx
  unsigned __int8 m_cNumOrDurability; // dl
  Item::CItem *v11; // eax
  char *v12; // [esp-10h] [ebp-1040h]
  unsigned int v13; // [esp-Ch] [ebp-103Ch]
  int v14; // [esp-8h] [ebp-1038h]
  int v15; // [esp-4h] [ebp-1034h]
  char v16; // [esp+Fh] [ebp-1021h]
  unsigned int dwCID; // [esp+10h] [ebp-1020h]
  _BYTE SrcPos[6]; // [esp+14h] [ebp-101Ch] OVERLAPPED
  Item::CItem *lpSplitItem; // [esp+1Ch] [ebp-1014h]
  unsigned int dwGold; // [esp+20h] [ebp-1010h]
  Item::CItem *lpPrevItem; // [esp+24h] [ebp-100Ch]
  char string[4093]; // [esp+28h] [ebp-1008h] BYREF
  __int16 v23; // [esp+1025h] [ebp-Bh]
  char v24; // [esp+1027h] [ebp-9h]

  if ( (lpPktBase->m_Len & 0x3FFF) != 0x15 )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
  m_lpCharacter = GameClientDispatch->m_lpCharacter;
  v4 = 0;
  if ( !m_lpCharacter )
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
    return 0;
  }
  dwCID = m_lpCharacter->m_dwCID;
  *(_DWORD *)SrcPos = lpPktBase[1].m_CodePage;
  SrcPos[4] = lpPktBase[1].m_SrvInfo.SrvState.wError;
  Item = CCharacter::GetItem(m_lpCharacter, *(int *)SrcPos);
  memset(string, 0, sizeof(string));
  v23 = 0;
  lpPrevItem = Item;
  lpSplitItem = 0;
  v24 = 0;
  if ( !Item )
  {
    v15 = *(unsigned __int16 *)SrcPos >> 4;
    v14 = SrcPos[0] & 0xF;
    v13 = dwCID;
    v12 = aCid0x08x_132;
LABEL_7:
    v4 = 2;
    v6 = _snprintf(string, 0x1000u, v12, v13, v14, v15);
LABEL_12:
    v7 = v6;
    goto LABEL_13;
  }
  if ( (*(_WORD *)&Item->m_itemPos_Real & 0xF) == 9 || (SrcPos[2] & 0xF) != 9 )
  {
    v8 = 0;
    v16 = 0;
  }
  else
  {
    v16 = 1;
    if ( !m_lpCharacter->m_Deposit.m_bLoginSuccess )
    {
      v4 = 2;
      v6 = _snprintf(string, 0x1000u, aCid0x08x_219, dwCID);
      goto LABEL_12;
    }
    v8 = 20 * m_lpCharacter->m_CreatureStatus.m_nLevel;
    if ( m_lpCharacter->m_DBData.m_Info.Gold < v8 )
    {
      v4 = 2;
      v6 = _snprintf(string, 0x1000u, aCid0x08x_127, dwCID);
      goto LABEL_12;
    }
  }
  m_ItemInfo = Item->m_ItemInfo;
  if ( (m_ItemInfo->m_DetailData.m_dwFlags & 8) != 8 )
  {
    v4 = 2;
    v6 = _snprintf(string, 0x1000u, aCid0x08x_289, dwCID, Item->m_ItemData.m_usProtoTypeID);
    goto LABEL_12;
  }
  m_cNumOrDurability = Item->m_ItemData.m_cNumOrDurability;
  if ( m_cNumOrDurability <= SrcPos[4] )
  {
    v15 = SrcPos[4];
    v14 = m_cNumOrDurability;
    v13 = dwCID;
    v12 = aCid0x08x_12;
    goto LABEL_7;
  }
  Item::CItemFactory::CreateItem(CSingleton<Item::CItemFactory>::ms_pSingleton, m_ItemInfo);
  lpSplitItem = v11;
  if ( !v11 )
  {
    v4 = 2;
    v6 = _snprintf(string, 0x1000u, aCid0x08x_32, dwCID);
    goto LABEL_12;
  }
  v11->m_ItemData.m_cNumOrDurability = SrcPos[4];
  if ( CCharacter::SetItem(m_lpCharacter, *(int *)&SrcPos[2], v11) )
  {
    lpPrevItem->m_ItemData.m_cNumOrDurability -= SrcPos[4];
    if ( v16 )
    {
      dwGold = m_lpCharacter->m_DBData.m_Info.Gold;
      CCharacter::DeductGold(m_lpCharacter, v8, 0);
      GAMELOG::LogTakeGold(m_lpCharacter, dwGold, m_lpCharacter->m_DBData.m_Info.Gold, v8, 2u, 2u, 1u, 0);
    }
    goto LABEL_15;
  }
  v4 = 2;
  v7 = _snprintf(string, 0x1000u, aCid0x08x_241, dwCID, SrcPos[2] & 0xF, *(unsigned __int16 *)&SrcPos[2] >> 4);
  ((void (__thiscall *)(Item::CItem *, int))lpSplitItem->~Item::CItem)(lpSplitItem, 1);
  lpSplitItem = 0;
LABEL_13:
  if ( v7 > 0 )
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "GameClientParsePacket::ParseCharSplitItem",
      aDWorkRylSource_9,
      572,
      string);
LABEL_15:
  GAMELOG::LogSplitItem(m_lpCharacter, *(TakeType *)SrcPos, lpPrevItem, lpSplitItem, v4);
  return GameClientSendPacket::SendCharSplitItem(
           &GameClientDispatch->m_SendStream,
           dwCID,
           lpSplitItem,
           *(TakeType *)SrcPos,
           v4);
}
// 48F342: variable 'v11' is possibly undefined
// 48F375: variable 'SrcPos' is possibly undefined

//----- (0048F420) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __cdecl GameClientParsePacket::ParseCharAutoRouting(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase)
{
  CCharacter *m_lpCharacter; // ebp
  unsigned int v4; // edx
  unsigned __int16 v5; // bx
  unsigned int m_CodePage; // esi
  unsigned int dwServerInfo; // edi
  unsigned __int8 v8; // al
  CCharacterParty *v9; // eax
  unsigned __int8 cCmd; // [esp+0h] [ebp-8h]
  Item::CItem *itemPos; // [esp+4h] [ebp-4h] OVERLAPPED BYREF

  if ( (lpPktBase->m_Len & 0x3FFF) == 0x1E )
  {
    m_lpCharacter = GameClientDispatch->m_lpCharacter;
    if ( m_lpCharacter )
    {
      v4 = *(_DWORD *)&lpPktBase[1].m_StartBit;
      v5 = *(_WORD *)&lpPktBase[2].m_StartBit;
      m_CodePage = lpPktBase[1].m_CodePage;
      dwServerInfo = lpPktBase[1].m_SrvInfo.dwServerInfo;
      v8 = BYTE1(lpPktBase[2].m_CodePage);
      LOWORD(itemPos) = *(unsigned __int16 *)((char *)&lpPktBase[2].m_Len + 1);
      cCmd = v8;
      lpPktBase = 0;
      if ( v8 == 1
        && (!GameClientParsePacket::ProcessItemPickUp(
               GameClientDispatch,
               (CFindItemInfoFromUID)__PAIR64__(m_CodePage, v4),
               (int)itemPos,
               &itemPos,
               (unsigned __int16 *)&lpPktBase)
         || (_WORD)lpPktBase) )
      {
        cCmd = 2;
      }
      v9 = (CCharacterParty *)m_lpCharacter->GetParty(m_lpCharacter);
      if ( v9 )
        CCharacterParty::SendAutoRouting(v9, dwServerInfo, v5, cCmd);
      return 1;
    }
    else
    {
      CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DBA94, lpPktBase->m_Cmd);
      return 0;
    }
  }
  else
  {
    CRylServerDispatch::LogErrorPacket(GameClientDispatch, &byte_4DB9F0, lpPktBase->m_Cmd);
    return 0;
  }
}
// 48F420: variables would overlap: ^2C.4 and stkvar "itemPos" ^2C.2(has user info)

//----- (0048F4F0) --------------------------------------------------------
void __thiscall CCheckPing::CCheckPing(CCheckPing *this)
{
  InitializeCriticalSection(&this->m_PingLock.m_CSLock);
  this->m_dwPingCount = 0;
  this->m_dwLastPingRecvTime = 0;
  this->m_dwFirstCheckTime = 0;
}

//----- (0048F510) --------------------------------------------------------
char __thiscall CCheckPing::CheckPing(CCheckPing *this, unsigned int dwCurrentTime)
{
  unsigned int m_dwLastPingRecvTime; // eax
  unsigned int m_dwFirstCheckTime; // eax
  bool v6; // bl

  if ( !CServerSetup::GetInstance()->m_bPingCheck )
    return 1;
  EnterCriticalSection(&this->m_PingLock.m_CSLock);
  m_dwLastPingRecvTime = this->m_dwLastPingRecvTime;
  if ( m_dwLastPingRecvTime || this->m_dwPingCount )
  {
    v6 = (int)(dwCurrentTime - m_dwLastPingRecvTime) < 120000;
    LeaveCriticalSection(&this->m_PingLock.m_CSLock);
    return v6;
  }
  else
  {
    m_dwFirstCheckTime = this->m_dwFirstCheckTime;
    if ( !m_dwFirstCheckTime )
    {
      this->m_dwFirstCheckTime = dwCurrentTime;
      if ( !dwCurrentTime )
        this->m_dwFirstCheckTime = 1;
      goto LABEL_7;
    }
    if ( (int)(dwCurrentTime - m_dwFirstCheckTime) <= 300000 )
    {
LABEL_7:
      LeaveCriticalSection(&this->m_PingLock.m_CSLock);
      return 1;
    }
    LeaveCriticalSection(&this->m_PingLock.m_CSLock);
    return 0;
  }
}

//----- (0048F5A0) --------------------------------------------------------
void __thiscall CCheckPing::SetLastPingRecvTime(CCheckPing *this, unsigned int dwCurrentTime)
{
  EnterCriticalSection(&this->m_PingLock.m_CSLock);
  this->m_dwLastPingRecvTime = dwCurrentTime;
  if ( this->m_dwPingCount++ == -1 )
    this->m_dwPingCount = 1;
  LeaveCriticalSection(&this->m_PingLock.m_CSLock);
}

//----- (0048F5D0) --------------------------------------------------------
void __thiscall CCheckPing::GetPingData(
        CCheckPing *this,
        unsigned int *dwPingCount,
        unsigned int *dwLastPingRecvTime,
        unsigned int *dwFirstCheckTime)
{
  EnterCriticalSection(&this->m_PingLock.m_CSLock);
  *dwPingCount = this->m_dwPingCount;
  *dwLastPingRecvTime = this->m_dwLastPingRecvTime;
  *dwFirstCheckTime = this->m_dwFirstCheckTime;
  LeaveCriticalSection(&this->m_PingLock.m_CSLock);
}

//----- (0048F600) --------------------------------------------------------
void __thiscall CSpeedHackCheck::CoolDownInfo::CoolDownInfo(
        CSpeedHackCheck::CoolDownInfo *this,
        const Skill::ProtoType *lpSkillProtoType,
        AtType attackType,
        unsigned int dwServerLastCastingTime,
        unsigned int dwClientLastCastingTime)
{
  this->m_lpSkillProtoType = lpSkillProtoType;
  this->m_attackType = attackType;
  this->m_dwServerLastCastingTime = dwServerLastCastingTime;
  this->m_dwClientLastCastingTime = dwClientLastCastingTime;
  this->m_dwSkillUsedCount = 1;
}

//----- (0048F630) --------------------------------------------------------
void __thiscall CSpeedHackCheck::LogSpeedHackMoveUpdate(CSpeedHackCheck *this)
{
  CCharacter *m_lpCharacter; // eax

  if ( this->m_lpCharacter )
  {
    m_lpCharacter = this->m_lpCharacter;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSpeedHackCheck::LogSpeedHackMoveUpdate",
      aDWorkRylSource_80,
      89,
      aUidDCid0x08xIp,
      m_lpCharacter->m_dwUID,
      m_lpCharacter->m_dwCID,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b1,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b2,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b3,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b4,
      m_lpCharacter->m_DBData.m_Info.Name);
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSpeedHackCheck::LogSpeedHackMoveUpdate",
      aDWorkRylSource_80,
      79,
      (char *)&byte_4F50C8);
  }
}

//----- (0048F6B0) --------------------------------------------------------
char __thiscall CSpeedHackCheck::CheckSpeedHack(
        CSpeedHackCheck *this,
        unsigned int CurrentServer,
        unsigned int CurrentClient)
{
  unsigned int m_dwServerLast; // ecx
  unsigned int m_dwClinetLast; // edx
  int v6; // eax
  int v7; // ecx
  char result; // al
  unsigned int v9; // edx
  unsigned int v10; // edi
  unsigned int v11; // ebx
  int v12; // edx
  CCharacter *v13; // ebp
  unsigned int v14; // ebp
  CCharacter *m_lpCharacter; // ebp
  unsigned int m_dwCID; // ebp

  if ( !CPulse::GetInstance()->m_bTPPOverTwoTime
    && (m_dwServerLast = this->m_dwServerLast) != 0
    && (m_dwClinetLast = this->m_dwClinetLast) != 0 )
  {
    v6 = CurrentServer - m_dwServerLast;
    v7 = CurrentClient - m_dwClinetLast;
    if ( v6 )
    {
      v9 = v7 + this->m_dwClientTotal;
      v10 = v6 + this->m_dwServerTotal;
      this->m_dwClientTotal = v9;
      v11 = v9;
      v12 = v9 - v10;
      this->m_dwServerTotal = v10;
      if ( v12 <= 2500 )
      {
        if ( v12 >= -3000 )
        {
          if ( v10 > 0xFFFFFFF0 || v11 > 0xFFFFFFF0 )
          {
            this->m_dwServerTotal = 0;
            this->m_dwClientTotal = 0;
            this->m_dwServerLast = 0;
            this->m_dwClinetLast = 0;
          }
          this->m_dwServerLast = CurrentServer;
          this->m_dwClinetLast = CurrentClient;
          return 0;
        }
        else
        {
          m_lpCharacter = this->m_lpCharacter;
          if ( m_lpCharacter )
            m_dwCID = m_lpCharacter->m_dwCID;
          else
            m_dwCID = 0;
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CSpeedHackCheck::CheckSpeedHack",
            aDWorkRylSource_80,
            161,
            aCid0x08x_266,
            m_dwCID,
            v6,
            v7,
            v10,
            v11,
            v12);
          result = 0;
          this->m_dwServerTotal = 0;
          this->m_dwClientTotal = 0;
          this->m_dwServerLast = 0;
          this->m_dwClinetLast = 0;
        }
      }
      else
      {
        v13 = this->m_lpCharacter;
        if ( v13 )
          v14 = v13->m_dwCID;
        else
          v14 = 0;
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CSpeedHackCheck::CheckSpeedHack",
          aDWorkRylSource_80,
          139,
          aCid0x08x_243,
          v14,
          v6,
          v7,
          v10,
          v11,
          v12);
        this->m_dwServerLast = CurrentServer;
        this->m_dwServerTotal = 0;
        this->m_dwClientTotal = 0;
        this->m_dwClinetLast = CurrentClient;
        return 1;
      }
    }
    else
    {
      this->m_dwClinetLast = CurrentClient;
      this->m_dwServerLast = CurrentServer;
      this->m_dwServerTotal = 0;
      this->m_dwClientTotal = 0;
      return 0;
    }
  }
  else
  {
    this->m_dwServerLast = CurrentServer;
    this->m_dwClinetLast = CurrentClient;
    return 0;
  }
  return result;
}

//----- (0048F810) --------------------------------------------------------
BanInfo *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::size(std::vector<BanInfo> *this)
{
  BanInfo *result; // eax

  result = this->_Myfirst;
  if ( result )
    return (BanInfo *)(this->_Mylast - result);
  return result;
}

//----- (0048F830) --------------------------------------------------------
void __cdecl std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(
        BanInfo *_First,
        BanInfo *_Last,
        const BanInfo *_Val)
{
  BanInfo *i; // eax

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

//----- (0048F870) --------------------------------------------------------
BanInfo *__cdecl std::_Copy_backward_opt<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo *>(
        BanInfo *_First,
        BanInfo *_Last,
        BanInfo *_Dest)
{
  BanInfo *v3; // ecx
  BanInfo *result; // eax

  v3 = _Last;
  for ( result = _Dest; v3 != _First; *(_DWORD *)&result->m_szName[12] = *(_DWORD *)&v3->m_szName[12] )
  {
    --v3;
    --result;
    result->m_dwCID = v3->m_dwCID;
    *(_DWORD *)result->m_szName = *(_DWORD *)v3->m_szName;
    *(_DWORD *)&result->m_szName[4] = *(_DWORD *)&v3->m_szName[4];
    *(_DWORD *)&result->m_szName[8] = *(_DWORD *)&v3->m_szName[8];
  }
  return result;
}

//----- (0048F8C0) --------------------------------------------------------
char __thiscall CSpeedHackCheck::CheckMoveUpdate(CSpeedHackCheck *this, PktMU *lpPktMU)
{
  DWORD Time; // eax
  unsigned int m_dwTick; // [esp-4h] [ebp-8h]

  if ( CServerSetup::GetInstance()->m_bHackCheck )
  {
    m_dwTick = lpPktMU->m_dwTick;
    Time = timeGetTime();
    if ( CSpeedHackCheck::CheckSpeedHack(this, Time, m_dwTick) )
      CSpeedHackCheck::LogSpeedHackMoveUpdate(this);
  }
  return 1;
}

//----- (0048F900) --------------------------------------------------------
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::pop_front(std::deque<CSpeedHackCheck::SkillHistory> *this)
{
  unsigned int Mysize; // eax
  unsigned int Mapsize; // edi
  unsigned int v3; // edx
  unsigned int v4; // eax

  Mysize = this->_Mysize;
  if ( Mysize )
  {
    Mapsize = this->_Mapsize;
    v3 = this->_Myoff + 1;
    this->_Myoff = v3;
    if ( Mapsize <= v3 )
      this->_Myoff = 0;
    v4 = Mysize - 1;
    this->_Mysize = v4;
    if ( !v4 )
      this->_Myoff = 0;
  }
}

//----- (0048F930) --------------------------------------------------------
void __thiscall CSpeedHackCheck::LogAttackHack(CSpeedHackCheck *this, const char *szDetailText)
{
  CCharacter *m_lpCharacter; // eax
  int v4; // eax
  unsigned int Myoff; // ebx
  int v6; // edi
  unsigned int v7; // eax
  unsigned int v8; // eax
  unsigned int Mapsize; // edx
  CSpeedHackCheck::AttackTime *v10; // eax
  unsigned int last_4; // [esp+14h] [ebp-408h]
  char szBuffer[1024]; // [esp+18h] [ebp-404h] BYREF

  szBuffer[1023] = 0;
  m_lpCharacter = this->m_lpCharacter;
  v4 = _snprintf(
         szBuffer,
         0x3FFu,
         "/UID:%d/CID:0x%08x/Name:%s/IP:%d.%d.%d.%d/%s/ServerDiff(ClientDiff):",
         m_lpCharacter->m_dwUID,
         m_lpCharacter->m_dwCID,
         m_lpCharacter->m_DBData.m_Info.Name,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b1,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b2,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b3,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b4,
         szDetailText);
  Myoff = this->m_AttackTimeList._Myoff;
  v6 = v4;
  v7 = Myoff + this->m_AttackTimeList._Mysize;
  last_4 = v7;
  while ( Myoff != v7 )
  {
    v8 = Myoff >> 1;
    Mapsize = this->m_AttackTimeList._Mapsize;
    if ( Mapsize <= Myoff >> 1 )
      v8 -= Mapsize;
    v10 = this->m_AttackTimeList._Map[v8];
    v6 += _snprintf(
            &szBuffer[v6],
            1023 - v6,
            "%dms(%dms),",
            v10[Myoff - 2 * (Myoff >> 1)].m_nDiffServerInterval,
            v10[Myoff - 2 * (Myoff >> 1)].m_nDiffClientInterval);
    v7 = last_4;
    ++Myoff;
  }
  _snprintf(
    &szBuffer[v6],
    1023 - v6,
    "/ServerSum: %dms/ClientSum: %dms/AttackCount: %d/",
    this->m_SumAttackTime.m_nSumServerAttackTime,
    this->m_SumAttackTime.m_nSumClientAttackTime,
    this->m_AttackTimeList._Mysize);
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSpeedHackCheck::LogAttackHack", aDWorkRylSource_80, 354, szBuffer);
}

//----- (0048FA70) --------------------------------------------------------
void __thiscall CSpeedHackCheck::LogSkillCoolDownTime(
        CSpeedHackCheck *this,
        const CSpeedHackCheck::CoolDownInfo *coolDownInfo,
        int nMinCoolDownTime,
        int nServerCoolDownTimeInterval,
        int nClientCoolDownTimeInterval)
{
  CCharacter *m_lpCharacter; // eax
  int v7; // eax
  unsigned int Myoff; // edi
  unsigned int Mysize; // ebp
  std::deque<CSpeedHackCheck::SkillHistory> *p_m_SkillHistoryList; // esi
  int v11; // ebx
  unsigned int v12; // ebp
  unsigned int Mapsize; // ecx
  unsigned int v14; // eax
  CSpeedHackCheck::SkillHistory *v15; // eax
  unsigned int m_dwSkillUsedCount; // [esp-4h] [ebp-101Ch]
  char string[4100]; // [esp+10h] [ebp-1008h] BYREF

  m_dwSkillUsedCount = coolDownInfo->m_dwSkillUsedCount;
  string[4096] = 0;
  m_lpCharacter = this->m_lpCharacter;
  v7 = _snprintf(
         string,
         0x1000u,
         aUidDCid0x08xNa_1,
         m_lpCharacter->m_dwUID,
         m_lpCharacter->m_dwCID,
         m_lpCharacter->m_DBData.m_Info.Name,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b1,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b2,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b3,
         m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b4,
         coolDownInfo->m_attackType.m_wType,
         *((unsigned __int8 *)&coolDownInfo->m_attackType + 2) >> 4,
         coolDownInfo->m_attackType.m_cSkillLevel,
         m_lpCharacter->m_CreatureStatus.m_nLevel,
         m_lpCharacter->m_CharacterStatus.m_nDEX,
         nServerCoolDownTimeInterval,
         nClientCoolDownTimeInterval,
         nMinCoolDownTime,
         m_dwSkillUsedCount);
  Myoff = this->m_SkillHistoryList._Myoff;
  Mysize = this->m_SkillHistoryList._Mysize;
  p_m_SkillHistoryList = &this->m_SkillHistoryList;
  v11 = v7;
  v12 = Myoff + Mysize;
  while ( Myoff != v12 )
  {
    Mapsize = p_m_SkillHistoryList->_Mapsize;
    v14 = Myoff;
    if ( Mapsize <= Myoff )
      v14 = Myoff - Mapsize;
    v15 = p_m_SkillHistoryList->_Map[v14];
    v11 += _snprintf(
             &string[v11],
             4096 - v11,
             "ID:0x%04x Lc:%2d Lv:%2d SInterval:%d CInterval:%d MinCoolDownTime:%d/",
             v15->m_attackType.m_wType,
             *((unsigned __int8 *)&v15->m_attackType + 2) >> 4,
             v15->m_attackType.m_cSkillLevel,
             v15->m_nServerInterval,
             v15->m_nClientInterval,
             v15->m_nMinCoolDownTime);
    ++Myoff;
  }
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CSpeedHackCheck::LogSkillCoolDownTime", aDWorkRylSource_80, 395, string);
}

//----- (0048FBD0) --------------------------------------------------------
std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *__cdecl std::_Lower_bound<std::vector<CSpeedHackCheck::CoolDownInfo>::iterator,unsigned short,int>(
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *result,
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _First,
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Last,
        unsigned __int16 *_Val)
{
  CSpeedHackCheck::CoolDownInfo *Myptr; // esi
  int v5; // ecx
  std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *v6; // eax

  Myptr = _First._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  while ( v5 > 0 )
  {
    if ( Myptr[v5 / 2].m_attackType.m_wType >= *_Val )
    {
      v5 /= 2;
    }
    else
    {
      Myptr += v5 / 2 + 1;
      v5 += -1 - v5 / 2;
    }
  }
  v6 = result;
  result->_Myptr = Myptr;
  return v6;
}

//----- (0048FC30) --------------------------------------------------------
BanInfo *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::_Ufill(
        std::vector<BanInfo> *this,
        BanInfo *_Ptr,
        unsigned int _Count,
        const BanInfo *_Val)
{
  std::_Uninit_fill_n<BanInfo *,unsigned int,BanInfo,std::allocator<BanInfo>>(_Ptr, _Count, _Val);
  return &_Ptr[_Count];
}

//----- (0048FC60) --------------------------------------------------------
void __thiscall __noreturn std::vector<CSpeedHackCheck::CoolDownInfo>::_Xlen(
        std::vector<CSpeedHackCheck::CoolDownInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0048FCD0) --------------------------------------------------------
void __thiscall __noreturn std::deque<CSpeedHackCheck::AttackTime>::_Xlen(
        std::deque<CSpeedHackCheck::SkillHistory> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "deque<T> too long", 0x11u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0048FD40) --------------------------------------------------------
void __thiscall std::deque<CSpeedHackCheck::AttackTime>::_Growmap(
        std::deque<CSpeedHackCheck::AttackTime> *this,
        unsigned int _Count)
{
  unsigned int Mapsize; // eax
  unsigned int v4; // edi
  unsigned int v5; // ecx
  unsigned int v6; // ebp
  int v7; // esi
  unsigned __int8 *Map; // ecx
  int v9; // eax
  unsigned __int8 *v10; // eax
  int v11; // eax
  unsigned __int8 *v12; // edi
  unsigned int v13; // ecx
  unsigned int v14; // esi
  int v15; // eax
  unsigned int v16; // eax
  unsigned __int8 *_Newmap; // [esp+8h] [ebp-Ch]
  unsigned int v18; // [esp+10h] [ebp-4h]
  unsigned int v19; // [esp+10h] [ebp-4h]

  Mapsize = this->_Mapsize;
  v4 = _Count;
  if ( 0xFFFFFFF - Mapsize < _Count )
    std::deque<CSpeedHackCheck::AttackTime>::_Xlen((std::deque<CSpeedHackCheck::SkillHistory> *)this);
  v5 = Mapsize >> 1;
  if ( Mapsize >> 1 < 8 )
    v5 = 8;
  if ( _Count < v5 && Mapsize <= 0xFFFFFFF - v5 )
  {
    _Count = v5;
    v4 = v5;
  }
  v6 = this->_Myoff >> 1;
  v7 = 4 * v6;
  _Newmap = (unsigned __int8 *)operator new((tagHeader *)(4 * (v4 + Mapsize)));
  v18 = 4 * ((int)(4 * this->_Mapsize - 4 * v6) >> 2);
  memmove(&_Newmap[4 * v6], (unsigned __int8 *)&this->_Map[v6], v18);
  Map = (unsigned __int8 *)this->_Map;
  v10 = (unsigned __int8 *)(v18 + v9);
  if ( v6 > v4 )
  {
    memmove(v10, Map, 4 * ((int)(4 * v4) >> 2));
    v14 = 4 * ((int)(v7 - 4 * v4) >> 2);
    memmove(_Newmap, (unsigned __int8 *)&this->_Map[v4], v14);
    if ( !v4 )
      goto LABEL_16;
    v13 = v4;
    v12 = (unsigned __int8 *)(v14 + v15);
    goto LABEL_15;
  }
  v19 = 4 * (v7 >> 2);
  memmove(v10, Map, v19);
  if ( v4 != v6 )
  {
    memset((void *)(v19 + v11), 0, 4 * (v4 - v6));
    v4 = _Count;
  }
  if ( v6 )
  {
    v12 = _Newmap;
    v13 = v6;
LABEL_15:
    memset(v12, 0, 4 * v13);
    v4 = _Count;
  }
LABEL_16:
  if ( this->_Map )
    operator delete(this->_Map);
  v16 = v4 + this->_Mapsize;
  this->_Map = (CSpeedHackCheck::AttackTime **)_Newmap;
  this->_Mapsize = v16;
}
// 48FDDA: variable 'v9' is possibly undefined
// 48FE01: variable 'v11' is possibly undefined
// 48FE5F: variable 'v15' is possibly undefined

//----- (0048FEA0) --------------------------------------------------------
void __thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::_Insert_n(
        std::vector<CSpeedHackCheck::CoolDownInfo> *this,
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Where,
        unsigned int _Count,
        const CSpeedHackCheck::CoolDownInfo *_Val)
{
  AtType m_attackType; // edx
  CSpeedHackCheck::CoolDownInfo *Myfirst; // ebx
  unsigned int m_dwServerLastCastingTime; // ecx
  unsigned int m_dwClientLastCastingTime; // edx
  unsigned int m_dwSkillUsedCount; // eax
  unsigned int v10; // ecx
  int v12; // eax
  int v13; // eax
  unsigned int v14; // ecx
  int v15; // eax
  int v16; // ebx
  CSpeedHackCheck::CoolDownInfo *v17; // eax
  char *v18; // edi
  BanInfo *Mylast; // ecx
  CSpeedHackCheck::CoolDownInfo *v21; // edi
  BanInfo *v22; // [esp-18h] [ebp-48h]
  BanInfo *v23; // [esp-Ch] [ebp-3Ch]
  unsigned int v24; // [esp-8h] [ebp-38h]
  int v25; // [esp+0h] [ebp-30h] BYREF
  CSpeedHackCheck::CoolDownInfo _Tmp; // [esp+Ch] [ebp-24h] BYREF
  int *v27; // [esp+20h] [ebp-10h]
  int v28; // [esp+2Ch] [ebp-4h]
  BanInfo *_Wherea; // [esp+38h] [ebp+8h]
  BanInfo *_Counta; // [esp+3Ch] [ebp+Ch]
  BanInfo *_Newvec; // [esp+40h] [ebp+10h]
  BanInfo *_Newveca; // [esp+40h] [ebp+10h]

  m_attackType = _Val->m_attackType;
  Myfirst = this->_Myfirst;
  _Tmp.m_lpSkillProtoType = _Val->m_lpSkillProtoType;
  m_dwServerLastCastingTime = _Val->m_dwServerLastCastingTime;
  _Tmp.m_attackType = m_attackType;
  m_dwClientLastCastingTime = _Val->m_dwClientLastCastingTime;
  m_dwSkillUsedCount = _Val->m_dwSkillUsedCount;
  v27 = &v25;
  _Tmp.m_dwServerLastCastingTime = m_dwServerLastCastingTime;
  _Tmp.m_dwClientLastCastingTime = m_dwClientLastCastingTime;
  _Tmp.m_dwSkillUsedCount = m_dwSkillUsedCount;
  if ( Myfirst )
    v10 = this->_Myend - Myfirst;
  else
    v10 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( 214748364 - v12 < _Count )
      std::vector<CSpeedHackCheck::CoolDownInfo>::_Xlen(this);
    if ( Myfirst )
      v13 = this->_Mylast - Myfirst;
    else
      v13 = 0;
    if ( v10 >= _Count + v13 )
    {
      Mylast = (BanInfo *)this->_Mylast;
      _Newveca = Mylast;
      if ( ((char *)Mylast - (char *)_Where._Myptr) / 20 >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = (CSpeedHackCheck::CoolDownInfo *)std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(
                                                           _Wherea,
                                                           Mylast,
                                                           Mylast);
        std::_Copy_backward_opt<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo *>(
          (BanInfo *)_Where._Myptr,
          _Wherea,
          _Newveca);
        std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(
          (BanInfo *)_Where._Myptr,
          (BanInfo *)&_Where._Myptr[_Count],
          (const BanInfo *)&_Tmp);
      }
      else
      {
        std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(
          (BanInfo *)_Where._Myptr,
          Mylast,
          (BanInfo *)&_Where._Myptr[_Count]);
        v24 = _Count - (this->_Mylast - _Where._Myptr);
        v23 = (BanInfo *)this->_Mylast;
        v28 = 2;
        std::vector<CSpeedHackCheck::CoolDownInfo>::_Ufill(
          (std::vector<BanInfo> *)this,
          v23,
          v24,
          (const BanInfo *)&_Tmp);
        v21 = &this->_Mylast[_Count];
        this->_Mylast = v21;
        std::fill<CSpeedHackCheck::CoolDownInfo *,CSpeedHackCheck::CoolDownInfo>(
          (BanInfo *)_Where._Myptr,
          (BanInfo *)&v21[-_Count],
          (const BanInfo *)&_Tmp);
      }
    }
    else
    {
      if ( 214748364 - (v10 >> 1) >= v10 )
        v14 = (v10 >> 1) + v10;
      else
        v14 = 0;
      if ( Myfirst )
        v15 = this->_Mylast - Myfirst;
      else
        v15 = 0;
      if ( v14 < _Count + v15 )
        v14 = (unsigned int)std::vector<CSpeedHackCheck::CoolDownInfo>::size((std::vector<BanInfo> *)this) + _Count;
      v16 = v14;
      _Newvec = (BanInfo *)operator new((tagHeader *)(20 * v14));
      v22 = (BanInfo *)this->_Myfirst;
      v28 = 0;
      _Counta = std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(v22, (BanInfo *)_Where._Myptr, _Newvec);
      std::_Uninit_fill_n<BanInfo *,unsigned int,BanInfo,std::allocator<BanInfo>>(
        _Counta,
        _Count,
        (const BanInfo *)&_Tmp);
      std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(
        (BanInfo *)_Where._Myptr,
        (BanInfo *)this->_Mylast,
        &_Counta[_Count]);
      v17 = this->_Myfirst;
      if ( v17 )
        v17 = (CSpeedHackCheck::CoolDownInfo *)(this->_Mylast - v17);
      v18 = (char *)v17 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = (CSpeedHackCheck::CoolDownInfo *)&_Newvec[v16];
      this->_Mylast = (CSpeedHackCheck::CoolDownInfo *)&_Newvec[(_DWORD)v18];
      this->_Myfirst = (CSpeedHackCheck::CoolDownInfo *)_Newvec;
    }
  }
}

//----- (00490170) --------------------------------------------------------
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::_Growmap(
        std::deque<CSpeedHackCheck::SkillHistory> *this,
        unsigned int _Count)
{
  unsigned int Mapsize; // eax
  unsigned int v4; // ebp
  unsigned int v5; // ecx
  unsigned int Myoff; // edi
  int v7; // esi
  unsigned __int8 *Map; // ecx
  int v9; // eax
  unsigned __int8 *v10; // eax
  int v11; // eax
  unsigned int v12; // ecx
  unsigned __int8 *v13; // edi
  unsigned int v14; // esi
  int v15; // eax
  unsigned int v16; // eax
  unsigned int v17; // [esp+Ch] [ebp-8h]
  unsigned int v18; // [esp+Ch] [ebp-8h]
  unsigned __int8 *_Counta; // [esp+18h] [ebp+4h]

  Mapsize = this->_Mapsize;
  v4 = _Count;
  if ( 0xFFFFFFF - Mapsize < _Count )
    std::deque<CSpeedHackCheck::AttackTime>::_Xlen(this);
  v5 = Mapsize >> 1;
  if ( Mapsize >> 1 < 8 )
    v5 = 8;
  if ( _Count < v5 && Mapsize <= 0xFFFFFFF - v5 )
    v4 = v5;
  Myoff = this->_Myoff;
  v7 = 4 * Myoff;
  _Counta = (unsigned __int8 *)operator new((tagHeader *)(4 * (v4 + Mapsize)));
  v17 = 4 * ((int)(4 * this->_Mapsize - 4 * Myoff) >> 2);
  memmove(&_Counta[4 * Myoff], (unsigned __int8 *)&this->_Map[Myoff], v17);
  Map = (unsigned __int8 *)this->_Map;
  v10 = (unsigned __int8 *)(v17 + v9);
  if ( Myoff > v4 )
  {
    memmove(v10, Map, 4 * ((int)(4 * v4) >> 2));
    v14 = 4 * ((int)(v7 - 4 * v4) >> 2);
    memmove(_Counta, (unsigned __int8 *)&this->_Map[v4], v14);
    v13 = (unsigned __int8 *)(v14 + v15);
    if ( !v4 )
      goto LABEL_16;
    v12 = v4;
    goto LABEL_15;
  }
  v18 = 4 * (v7 >> 2);
  memmove(v10, Map, v18);
  if ( v4 != Myoff )
    memset((void *)(v18 + v11), 0, 4 * (v4 - Myoff));
  if ( Myoff )
  {
    v12 = Myoff;
    v13 = _Counta;
LABEL_15:
    memset(v13, 0, 4 * v12);
  }
LABEL_16:
  if ( this->_Map )
    operator delete(this->_Map);
  v16 = v4 + this->_Mapsize;
  this->_Map = (CSpeedHackCheck::SkillHistory **)_Counta;
  this->_Mapsize = v16;
}
// 490208: variable 'v9' is possibly undefined
// 49022F: variable 'v11' is possibly undefined
// 490285: variable 'v15' is possibly undefined

//----- (004902C0) --------------------------------------------------------
void __thiscall std::deque<CSpeedHackCheck::AttackTime>::push_back(
        std::deque<CSpeedHackCheck::AttackTime> *this,
        const CSpeedHackCheck::AttackTime *_Val)
{
  unsigned int Mysize; // eax
  unsigned int Mapsize; // eax
  unsigned int v5; // edi
  unsigned int v6; // ebx
  CSpeedHackCheck::AttackTime *v7; // edi

  Mysize = this->_Mysize;
  if ( (((_BYTE)Mysize + LOBYTE(this->_Myoff)) & 1) == 0 && this->_Mapsize <= (Mysize + 2) >> 1 )
    std::deque<CSpeedHackCheck::AttackTime>::_Growmap(this, 1u);
  Mapsize = this->_Mapsize;
  v5 = this->_Mysize + this->_Myoff;
  v6 = v5 >> 1;
  if ( Mapsize <= v5 >> 1 )
    v6 -= Mapsize;
  if ( !this->_Map[v6] )
    this->_Map[v6] = (CSpeedHackCheck::AttackTime *)operator new((tagHeader *)0x10);
  v7 = &this->_Map[v6][v5 & 1];
  if ( v7 )
    *v7 = *_Val;
  ++this->_Mysize;
}

//----- (00490340) --------------------------------------------------------
std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *__thiscall std::vector<CSpeedHackCheck::CoolDownInfo>::insert(
        std::vector<CSpeedHackCheck::CoolDownInfo> *this,
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *result,
        std::vector<CSpeedHackCheck::CoolDownInfo>::iterator _Where,
        const CSpeedHackCheck::CoolDownInfo *_Val)
{
  CSpeedHackCheck::CoolDownInfo *Myfirst; // esi
  int v6; // esi
  std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<CSpeedHackCheck::CoolDownInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}

//----- (004903B0) --------------------------------------------------------
void __thiscall std::deque<CSpeedHackCheck::SkillHistory>::push_back(
        std::deque<CSpeedHackCheck::SkillHistory> *this,
        const CSpeedHackCheck::SkillHistory *_Val)
{
  unsigned int Mapsize; // eax
  unsigned int v4; // edi
  CSpeedHackCheck::SkillHistory *v5; // edi

  if ( this->_Mapsize <= this->_Mysize + 1 )
    std::deque<CSpeedHackCheck::SkillHistory>::_Growmap(this, 1u);
  Mapsize = this->_Mapsize;
  v4 = this->_Mysize + this->_Myoff;
  if ( Mapsize <= v4 )
    v4 -= Mapsize;
  if ( !this->_Map[v4] )
    this->_Map[v4] = (CSpeedHackCheck::SkillHistory *)operator new((tagHeader *)0x10);
  v5 = this->_Map[v4];
  if ( v5 )
    *v5 = *_Val;
  ++this->_Mysize;
}

//----- (00490430) --------------------------------------------------------
void __thiscall CSpeedHackCheck::CSpeedHackCheck(CSpeedHackCheck *this)
{
  this->m_dwServerTotal = 0;
  this->m_dwServerLast = 0;
  this->m_dwClientTotal = 0;
  this->m_dwClinetLast = 0;
  this->m_dwLastServerAttackTime = 0;
  this->m_dwLastClientAttackTime = 0;
  this->m_dwHackFoundCount = 0;
  this->m_SumAttackTime.m_nSumServerAttackTime = 0;
  this->m_SumAttackTime.m_nSumClientAttackTime = 0;
  this->m_AttackTimeList._Map = 0;
  this->m_AttackTimeList._Mapsize = 0;
  this->m_AttackTimeList._Myoff = 0;
  this->m_AttackTimeList._Mysize = 0;
  this->m_CoolDownInfo._Myfirst = 0;
  this->m_CoolDownInfo._Mylast = 0;
  this->m_CoolDownInfo._Myend = 0;
  this->m_SkillHistoryList._Map = 0;
  this->m_SkillHistoryList._Mapsize = 0;
  this->m_SkillHistoryList._Myoff = 0;
  this->m_SkillHistoryList._Mysize = 0;
  this->m_lpCharacter = 0;
}

//----- (004904A0) --------------------------------------------------------
char __thiscall CSpeedHackCheck::CheckAttackHack(CSpeedHackCheck *this, signed int lpPktAt, int dwCurrentServerTime)
{
  signed int v3; // ebx
  unsigned __int8 v4; // al
  unsigned int m_dwLastServerAttackTime; // eax
  unsigned int v7; // edx
  int v8; // ebp
  unsigned int m_dwLastClientAttackTime; // ecx
  int v10; // eax
  int v11; // edx
  int v12; // ebp
  int v13; // ecx
  unsigned int v14; // eax
  unsigned int v15; // ecx
  unsigned int Mapsize; // edx
  int m_nSumClientAttackTime; // ebx
  CSpeedHackCheck::AttackTime *v18; // eax
  unsigned int Mysize; // eax
  unsigned int v20; // ecx
  unsigned int v21; // edx
  unsigned int v22; // eax
  CSpeedHackCheck::CoolDownInfo *Mylast; // ebp
  std::vector<CSpeedHackCheck::CoolDownInfo>::iterator v25; // edi
  bool v26; // zf
  __int16 v27; // bp
  int v28; // ecx
  int v29; // edx
  int v30; // ebp
  double v31; // st7
  const Skill::ProtoType *v32; // ecx
  int v33; // eax
  int v34; // ecx
  AtType v35; // edx
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  const CSpeedHackCheck::CoolDownInfo *v37; // eax
  unsigned __int16 usSkill_LockCount; // [esp+10h] [ebp-20h]
  int nMinCoolDownTime[2]; // [esp+14h] [ebp-1Ch] BYREF
  CSpeedHackCheck::CoolDownInfo _Val; // [esp+1Ch] [ebp-14h] BYREF

  v3 = lpPktAt;
  v4 = *(_BYTE *)(lpPktAt + 34);
  lpPktAt = *(unsigned __int16 *)(lpPktAt + 32);
  usSkill_LockCount = v4 >> 4;
  if ( (lpPktAt & 0x8000) == 0 )
  {
    m_dwLastServerAttackTime = this->m_dwLastServerAttackTime;
    v7 = dwCurrentServerTime;
    if ( m_dwLastServerAttackTime )
      v8 = dwCurrentServerTime - m_dwLastServerAttackTime;
    else
      v8 = 500;
    m_dwLastClientAttackTime = this->m_dwLastClientAttackTime;
    if ( m_dwLastClientAttackTime )
    {
      v10 = *(_DWORD *)(v3 + 8) - m_dwLastClientAttackTime;
      dwCurrentServerTime = v10;
    }
    else
    {
      dwCurrentServerTime = 500;
      v10 = 500;
    }
    this->m_dwLastServerAttackTime = v7;
    this->m_dwLastClientAttackTime = *(_DWORD *)(v3 + 8);
    nMinCoolDownTime[0] = v8;
    nMinCoolDownTime[1] = v10;
    std::deque<CSpeedHackCheck::AttackTime>::push_back(
      &this->m_AttackTimeList,
      (const CSpeedHackCheck::AttackTime *)nMinCoolDownTime);
    v11 = v8 + this->m_SumAttackTime.m_nSumServerAttackTime;
    v12 = dwCurrentServerTime;
    v13 = dwCurrentServerTime + this->m_SumAttackTime.m_nSumClientAttackTime;
    this->m_SumAttackTime.m_nSumServerAttackTime = v11;
    this->m_SumAttackTime.m_nSumClientAttackTime = v13;
    if ( this->m_AttackTimeList._Mysize > 5 )
    {
      v14 = this->m_AttackTimeList._Myoff >> 1;
      v15 = this->m_AttackTimeList._Myoff - 2 * v14;
      Mapsize = this->m_AttackTimeList._Mapsize;
      if ( Mapsize <= v14 )
        v14 -= Mapsize;
      m_nSumClientAttackTime = this->m_SumAttackTime.m_nSumClientAttackTime;
      v18 = &this->m_AttackTimeList._Map[v14][v15];
      this->m_SumAttackTime.m_nSumServerAttackTime -= v18->m_nDiffServerInterval;
      this->m_SumAttackTime.m_nSumClientAttackTime = m_nSumClientAttackTime - v18->m_nDiffClientInterval;
      Mysize = this->m_AttackTimeList._Mysize;
      if ( Mysize )
      {
        v20 = this->m_AttackTimeList._Myoff + 1;
        v21 = 2 * this->m_AttackTimeList._Mapsize;
        this->m_AttackTimeList._Myoff = v20;
        if ( v21 <= v20 )
          this->m_AttackTimeList._Myoff = 0;
        v22 = Mysize - 1;
        this->m_AttackTimeList._Mysize = v22;
        if ( !v22 )
          this->m_AttackTimeList._Myoff = 0;
      }
    }
    if ( v12 <= 120 )
    {
      ++this->m_dwHackFoundCount;
      CSpeedHackCheck::LogAttackHack(this, &byte_4F5398);
      this->m_SumAttackTime.m_nSumClientAttackTime = 0;
      this->m_SumAttackTime.m_nSumServerAttackTime = 0;
      std::deque<unsigned long>::_Tidy((std::deque<CSpeedHackCheck::SkillHistory> *)&this->m_AttackTimeList);
      return 0;
    }
    if ( this->m_AttackTimeList._Mysize == 5
      && (this->m_SumAttackTime.m_nSumServerAttackTime < 0x5DCu || this->m_SumAttackTime.m_nSumClientAttackTime < 0x5DCu) )
    {
      ++this->m_dwHackFoundCount;
      CSpeedHackCheck::LogAttackHack(this, &byte_4F537C);
      this->m_SumAttackTime.m_nSumClientAttackTime = 0;
      this->m_SumAttackTime.m_nSumServerAttackTime = 0;
      std::deque<unsigned long>::_Tidy((std::deque<CSpeedHackCheck::SkillHistory> *)&this->m_AttackTimeList);
      return 0;
    }
    return 1;
  }
  if ( (v4 & 1) != 0 )
    return 1;
  Mylast = this->m_CoolDownInfo._Mylast;
  std::_Lower_bound<std::vector<CSpeedHackCheck::CoolDownInfo>::iterator,unsigned short,int>(
    (std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *)nMinCoolDownTime,
    (std::vector<CSpeedHackCheck::CoolDownInfo>::iterator)this->m_CoolDownInfo._Myfirst,
    (std::vector<CSpeedHackCheck::CoolDownInfo>::iterator)Mylast,
    (unsigned __int16 *)&lpPktAt);
  v25._Myptr = (CSpeedHackCheck::CoolDownInfo *)nMinCoolDownTime[0];
  v26 = nMinCoolDownTime[0] == (_DWORD)Mylast;
  v27 = lpPktAt;
  if ( v26 || *(_WORD *)(nMinCoolDownTime[0] + 4) != (_WORD)lpPktAt || usSkill_LockCount >= 5u )
  {
    SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, lpPktAt);
    if ( SkillProtoType
      && usSkill_LockCount < 5u
      && SkillProtoType->m_ProtoTypes[0].m_dwCoolDownTime
      && SkillProtoType->m_ProtoTypes[0].m_eSkillType != LEAVE_WAIT
      && v27 != -27899 )
    {
      CSpeedHackCheck::CoolDownInfo::CoolDownInfo(
        &_Val,
        SkillProtoType->m_ProtoTypes,
        *(AtType *)(v3 + 32),
        dwCurrentServerTime,
        *(_DWORD *)(v3 + 8));
      std::vector<CSpeedHackCheck::CoolDownInfo>::insert(
        &this->m_CoolDownInfo,
        (std::vector<CSpeedHackCheck::CoolDownInfo>::iterator *)&dwCurrentServerTime,
        v25,
        v37);
    }
    return 1;
  }
  v28 = *(_DWORD *)(nMinCoolDownTime[0] + 12);
  v29 = *(_DWORD *)(nMinCoolDownTime[0] + 8);
  ++*(_DWORD *)(nMinCoolDownTime[0] + 16);
  lpPktAt = *(_DWORD *)(v3 + 8) - v28;
  v30 = dwCurrentServerTime - v29;
  nMinCoolDownTime[0] = 520 - this->m_lpCharacter->m_CharacterStatus.m_nDEX;
  v31 = (double)nMinCoolDownTime[0] * 0.0020000001;
  if ( v31 < 0.5 )
    v31 = 0.5;
  v32 = *(const Skill::ProtoType **)(v3 + 32);
  nMinCoolDownTime[0] = (unsigned __int64)((double)v25._Myptr->m_lpSkillProtoType[*((unsigned __int8 *)&v25._Myptr->m_attackType
                                                                                  + 2) >> 4].m_dwCoolDownTime
                                         * v31);
  _Val.m_dwClientLastCastingTime = nMinCoolDownTime[0];
  _Val.m_lpSkillProtoType = v32;
  _Val.m_attackType = (AtType)(dwCurrentServerTime - v29);
  _Val.m_dwServerLastCastingTime = lpPktAt;
  std::deque<CSpeedHackCheck::SkillHistory>::push_back(
    &this->m_SkillHistoryList,
    (const CSpeedHackCheck::SkillHistory *)&_Val);
  if ( this->m_SkillHistoryList._Mysize > 5 )
    std::deque<CSpeedHackCheck::SkillHistory>::pop_front(&this->m_SkillHistoryList);
  v33 = nMinCoolDownTime[0];
  if ( v30 + 1000 < nMinCoolDownTime[0] || lpPktAt + 1000 < nMinCoolDownTime[0] )
  {
    ++this->m_dwHackFoundCount;
    CSpeedHackCheck::LogSkillCoolDownTime(this, v25._Myptr, v33, v30, lpPktAt);
    v35 = *(AtType *)(v3 + 32);
    v25._Myptr->m_dwServerLastCastingTime = dwCurrentServerTime;
    v25._Myptr->m_attackType = v35;
    v25._Myptr->m_dwClientLastCastingTime = *(_DWORD *)(v3 + 8);
    return 0;
  }
  else
  {
    v34 = dwCurrentServerTime;
    v25._Myptr->m_attackType = *(AtType *)(v3 + 32);
    v25._Myptr->m_dwServerLastCastingTime = v34;
    v25._Myptr->m_dwClientLastCastingTime = *(_DWORD *)(v3 + 8);
    return 1;
  }
}
// 4907CD: variable 'v37' is possibly undefined

//----- (004907E0) --------------------------------------------------------
char __thiscall CSpeedHackCheck::CheckAttackReplay(CSpeedHackCheck *this, PktAt *lpPktAt)
{
  DWORD Time; // eax
  CCharacter *m_lpCharacter; // eax

  if ( !this->m_lpCharacter )
    return 0;
  Time = timeGetTime();
  CSpeedHackCheck::CheckAttackHack(this, (signed int)lpPktAt, Time);
  if ( CServerSetup::GetInstance()->m_bHackCheck && this->m_dwHackFoundCount > 0xA )
  {
    m_lpCharacter = this->m_lpCharacter;
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CSpeedHackCheck::CheckAttackReplay",
      aDWorkRylSource_80,
      65,
      aUidDCid0x08xNa_0,
      m_lpCharacter->m_dwUID,
      m_lpCharacter->m_dwCID,
      m_lpCharacter->m_DBData.m_Info.Name,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b1,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b2,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b3,
      m_lpCharacter->m_PublicAddress.sin_addr.S_un.S_un_b.s_b4);
  }
  return 1;
}

//----- (00490880) --------------------------------------------------------
void __thiscall CDBRequest::CDBRequest(
        CDBRequest *this,
        CGameClientDispatch *GameClientDispatch,
        unsigned int dwDurationSec,
        void (__cdecl *lpTimeoutRequest)(CPacketDispatch *))
{
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *m_lpDispatch; // ecx
  CServerRequest *Instance; // eax
  unsigned int v8; // eax
  CPacketDispatch *v9; // [esp-Ch] [ebp-24h]
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+4h] [ebp-14h] BYREF
  int v11; // [esp+14h] [ebp-4h]

  this->m_dwRequestKey = 0;
  this->m_lpDBAgentSendStream = 0;
  DispatchTable = CDBAgentDispatch::GetDispatchTable();
  CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
  m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
  v11 = 0;
  if ( StoragelpDBAgentDispatch.m_lpDispatch )
  {
    this->m_lpDBAgentSendStream = (CSendStream *)&StoragelpDBAgentDispatch.m_lpDispatch[8];
    if ( m_lpDispatch != (CPacketDispatch *)-64 )
    {
      v9 = m_lpDispatch;
      Instance = CServerRequest::GetInstance();
      v8 = CServerRequest::AddRequest(Instance, GameClientDispatch, v9, dwDurationSec, lpTimeoutRequest);
      this->m_dwRequestKey = v8;
      if ( !v8 )
        this->m_lpDBAgentSendStream = 0;
    }
  }
  v11 = -1;
  CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
}

//----- (00490920) --------------------------------------------------------
void __thiscall CDBRequest::CancelRequest(CDBRequest *this)
{
  CServerRequest *Instance; // eax
  unsigned int m_dwRequestKey; // [esp-4h] [ebp-4h]

  if ( this->m_dwRequestKey )
  {
    m_dwRequestKey = this->m_dwRequestKey;
    Instance = CServerRequest::GetInstance();
    CServerRequest::RemoveRequest(Instance, m_dwRequestKey);
  }
}

//----- (00490940) --------------------------------------------------------
void __thiscall CPartySpellMgr::AddAffectedToAllMember(
        CPartySpellMgr *this,
        CSpell *pSpell,
        unsigned __int16 wMapIndex)
{
  CAggresiveCreature **m_pPartyMember; // esi
  CPartySpellMgr *v4; // ebx
  CAggresiveCreature *v5; // eax
  int wError; // [esp+8h] [ebp-4h] BYREF

  m_pPartyMember = this->m_pPartyMember;
  v4 = (CPartySpellMgr *)&this->m_pPartyMember[this->m_dwMemberNum];
  wError = 0;
  if ( this->m_pPartyMember != (CAggresiveCreature **)v4 )
  {
    do
    {
      v5 = *m_pPartyMember;
      if ( *m_pPartyMember
        && v5->m_CellPos.m_wMapIndex == wMapIndex
        && (v5->m_dwStatusFlag & 0x40000000) == 0
        && pSpell->m_pCaster->IsEnemy(pSpell->m_pCaster, *m_pPartyMember) == NEUTRALITY )
      {
        CSpell::AddAffected(pSpell, *m_pPartyMember, (unsigned __int16 *)&wError);
      }
      ++m_pPartyMember;
    }
    while ( m_pPartyMember != (CAggresiveCreature **)v4 );
  }
}

//----- (004909B0) --------------------------------------------------------
void __cdecl LogChantBug(
        CAggresiveCreature *lpCreature,
        CParty *lpParty,
        const char *szMessage,
        const char *lpRtn,
        const char *lpFileName,
        int nLine)
{
  CServerSetup *Instance; // eax

  Instance = CServerSetup::GetInstance();
  if ( !CServerSetup::GetServerGroup(Instance) && szMessage && lpParty )
  {
    if ( lpCreature )
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        lpRtn,
        lpFileName,
        nLine,
        "CID:%10u(0x%08x)/PID:%5d(0x%08x)/ChantBug - %s",
        lpCreature->m_dwCID,
        lpCreature,
        lpParty->m_Party.m_dwPartyID,
        lpParty,
        szMessage);
    else
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        lpRtn,
        lpFileName,
        nLine,
        "PID:%5d(0x%08x)/ChantBug - %s",
        lpParty->m_Party.m_dwPartyID,
        lpParty,
        szMessage);
  }
}

//----- (00490A30) --------------------------------------------------------
void __thiscall CPartySpellMgr::CPartySpellMgr(CPartySpellMgr *this)
{
  this->m_lpOwnerParty = 0;
  this->m_dwMemberNum = 0;
  this->m_pPartyMember[0] = 0;
  this->m_pPartyMember[1] = 0;
  this->m_pPartyMember[2] = 0;
  this->m_pPartyMember[3] = 0;
  this->m_pPartyMember[4] = 0;
  this->m_pPartyMember[5] = 0;
  this->m_pPartyMember[6] = 0;
  this->m_pPartyMember[7] = 0;
  this->m_pPartyMember[8] = 0;
  this->m_pPartyMember[9] = 0;
}

//----- (00490A60) --------------------------------------------------------
char __thiscall CPartySpellMgr::AddMember(CPartySpellMgr *this, CAggresiveCreature *lpNewMember)
{
  unsigned int m_dwMemberNum; // edx
  CAggresiveCreature **m_pPartyMember; // esi
  CAggresiveCreature **v4; // eax
  CPartySpellMgr *v5; // ebx
  CAggresiveCreature *v6; // edi
  CParty *m_lpOwnerParty; // esi
  CServerSetup *Instance; // eax
  CPartySpellMgr *v10; // [esp+4h] [ebp-4h]

  m_dwMemberNum = this->m_dwMemberNum;
  v10 = this;
  if ( m_dwMemberNum >= 0xA || !lpNewMember )
    return 0;
  m_pPartyMember = this->m_pPartyMember;
  v4 = this->m_pPartyMember;
  v5 = (CPartySpellMgr *)&this->m_pPartyMember[m_dwMemberNum];
  if ( this->m_pPartyMember == (CAggresiveCreature **)v5 )
  {
LABEL_6:
    if ( m_pPartyMember != (CAggresiveCreature **)v5 )
    {
      do
      {
        v6 = *m_pPartyMember;
        CAffectedSpell::ApplyPartyChant(&(*m_pPartyMember)->m_SpellMgr.m_AffectedInfo, lpNewMember);
        CAffectedSpell::ApplyPartyChant(&lpNewMember->m_SpellMgr.m_AffectedInfo, v6);
        ++m_pPartyMember;
      }
      while ( m_pPartyMember != (CAggresiveCreature **)v5 );
      this = v10;
    }
    this->m_pPartyMember[this->m_dwMemberNum] = lpNewMember;
    m_lpOwnerParty = this->m_lpOwnerParty;
    ++this->m_dwMemberNum;
    Instance = CServerSetup::GetInstance();
    if ( !CServerSetup::GetServerGroup(Instance) && byte_4F5598 )
    {
      if ( m_lpOwnerParty )
        CServerLog::DetailLog(
          &g_Log,
          LOG_DETAIL,
          "CPartySpellMgr::AddMember",
          aDWorkRylSource,
          65,
          "CID:%10u(0x%08x)/PID:%5d(0x%08x)/ChantBug - %s",
          lpNewMember->m_dwCID,
          lpNewMember,
          m_lpOwnerParty->m_Party.m_dwPartyID,
          m_lpOwnerParty,
          byte_4F5598);
    }
    return 1;
  }
  else
  {
    while ( lpNewMember != *v4 )
    {
      if ( ++v4 == (CAggresiveCreature **)v5 )
        goto LABEL_6;
    }
    LogChantBug(lpNewMember, this->m_lpOwnerParty, &byte_4F54C8, "CPartySpellMgr::AddMember", aDWorkRylSource, 39);
    return 0;
  }
}

//----- (00490B60) --------------------------------------------------------
void __thiscall CPartySpellMgr::ClearMember(CPartySpellMgr *this)
{
  CAggresiveCreature **m_pPartyMember; // edi
  CPartySpellMgr *v2; // edx
  CAggresiveCreature **v3; // ebx
  CAggresiveCreature **v4; // esi
  CAffectedSpell *i; // ebp
  CParty *m_lpOwnerParty; // esi
  CServerSetup *Instance; // eax
  CAggresiveCreature **ppPastEnd; // [esp+Ch] [ebp-8h]
  CPartySpellMgr *v9; // [esp+10h] [ebp-4h]

  m_pPartyMember = this->m_pPartyMember;
  v2 = (CPartySpellMgr *)&this->m_pPartyMember[this->m_dwMemberNum];
  v3 = this->m_pPartyMember;
  v9 = this;
  ppPastEnd = (CAggresiveCreature **)v2;
  if ( this->m_pPartyMember != (CAggresiveCreature **)v2 )
  {
    do
    {
      v4 = m_pPartyMember;
      for ( i = &(*v3)->m_SpellMgr.m_AffectedInfo; v4 != (CAggresiveCreature **)v2; ++v4 )
      {
        if ( *v4 != *v3 )
        {
          CAffectedSpell::RemoveChantByCaster(i, *v4);
          v2 = (CPartySpellMgr *)ppPastEnd;
        }
      }
      ++v3;
    }
    while ( v3 != (CAggresiveCreature **)v2 );
    this = v9;
  }
  *m_pPartyMember = 0;
  m_pPartyMember[1] = 0;
  m_pPartyMember[2] = 0;
  m_pPartyMember[3] = 0;
  m_pPartyMember[4] = 0;
  m_pPartyMember[5] = 0;
  m_pPartyMember[6] = 0;
  m_pPartyMember[7] = 0;
  m_pPartyMember[8] = 0;
  m_pPartyMember[9] = 0;
  m_lpOwnerParty = this->m_lpOwnerParty;
  this->m_dwMemberNum = 0;
  Instance = CServerSetup::GetInstance();
  if ( !CServerSetup::GetServerGroup(Instance) && byte_4F55D0 )
  {
    if ( m_lpOwnerParty )
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CPartySpellMgr::ClearMember",
        aDWorkRylSource,
        144,
        "PID:%5d(0x%08x)/ChantBug - %s",
        m_lpOwnerParty->m_Party.m_dwPartyID,
        m_lpOwnerParty,
        byte_4F55D0);
  }
}

//----- (00490C40) --------------------------------------------------------
char __thiscall CPartySpellMgr::RemoveMember(CPartySpellMgr *this, CAggresiveCreature *lpRemoveMember)
{
  unsigned int m_dwMemberNum; // eax
  CAggresiveCreature **m_pPartyMember; // edi
  unsigned __int8 *v5; // ecx
  CAggresiveCreature **v6; // eax
  CAggresiveCreature *v7; // ebx
  unsigned int v9; // eax
  CAggresiveCreature **v10; // ebp
  CAggresiveCreature *v11; // ebx
  CParty *m_lpOwnerParty; // esi
  CServerSetup *Instance; // eax

  m_dwMemberNum = this->m_dwMemberNum;
  m_pPartyMember = this->m_pPartyMember;
  v5 = (unsigned __int8 *)this->m_pPartyMember;
  v6 = &this->m_pPartyMember[m_dwMemberNum];
  if ( v5 == (unsigned __int8 *)v6 )
    return 0;
  while ( 1 )
  {
    v7 = lpRemoveMember;
    if ( lpRemoveMember )
    {
      if ( *(CAggresiveCreature **)v5 == lpRemoveMember )
        break;
    }
    v5 += 4;
    if ( v5 == (unsigned __int8 *)v6 )
      return 0;
  }
  memmove(v5, v5 + 4, 4 * (((char *)v6 - (char *)(v5 + 4)) >> 2));
  v9 = this->m_dwMemberNum - 1;
  this->m_dwMemberNum = v9;
  this->m_pPartyMember[v9] = 0;
  v10 = &this->m_pPartyMember[this->m_dwMemberNum];
  if ( m_pPartyMember != v10 )
  {
    do
    {
      v11 = *m_pPartyMember;
      CAffectedSpell::RemoveChantByCaster(&lpRemoveMember->m_SpellMgr.m_AffectedInfo, *m_pPartyMember);
      CAffectedSpell::RemoveChantByCaster(&v11->m_SpellMgr.m_AffectedInfo, lpRemoveMember);
      ++m_pPartyMember;
    }
    while ( m_pPartyMember != v10 );
    v7 = lpRemoveMember;
  }
  m_lpOwnerParty = this->m_lpOwnerParty;
  Instance = CServerSetup::GetInstance();
  if ( !CServerSetup::GetServerGroup(Instance) && byte_4F5610 )
  {
    if ( m_lpOwnerParty )
      CServerLog::DetailLog(
        &g_Log,
        LOG_DETAIL,
        "CPartySpellMgr::RemoveMember",
        aDWorkRylSource,
        110,
        "CID:%10u(0x%08x)/PID:%5d(0x%08x)/ChantBug - %s",
        v7->m_dwCID,
        v7,
        m_lpOwnerParty->m_Party.m_dwPartyID,
        m_lpOwnerParty,
        byte_4F5610);
  }
  return 1;
}

//----- (00490D30) --------------------------------------------------------
void __thiscall Skill::ProtoType::Initialize(Skill::ProtoType *this)
{
  this->m_eSkillType = NONE;
  this->m_eUseLimit = NONE;
  this->m_eTargetType = NONE;
  this->m_EffectExtent = 0.0;
  this->m_EffectDistance = 0.0;
  this->m_bIsClassSkill = 0;
  this->m_bGauge = 0;
  this->m_bProtection = 0;
  this->m_bInterrupt = 0;
  this->m_cDRC = 0;
  this->m_cEndCoolDown = 0;
  this->m_cEndScript = 0;
  this->m_cSpell_LockCount = 0;
  this->m_cStrikeNum = 0;
  this->m_usChildSkill = 0;
  this->m_usParentSkill = 0;
  this->m_LevelTick = 0;
  this->m_StartTick = 0;
  this->m_LevelMP = 0;
  this->m_StartMP = 0;
  this->m_usSkill_ID = 0;
  this->m_dwCoolDownTime = 0;
  Item::SpriteData::Initialize(&this->m_SpriteInfo);
  *(_WORD *)this->m_StatusLimitType = 0;
  *(_WORD *)this->m_StatusLimitValue = 0;
  *(_DWORD *)this->m_szEffectFileName = 0;
  *(_DWORD *)&this->m_szEffectFileName[4] = 0;
  *(_DWORD *)&this->m_szEffectFileName[8] = 0;
  *(_DWORD *)&this->m_szEffectFileName[12] = 0;
  *(_DWORD *)&this->m_szEffectFileName[16] = 0;
  *(_DWORD *)&this->m_szEffectFileName[20] = 0;
  *(_DWORD *)&this->m_szEffectFileName[24] = 0;
  *(_DWORD *)&this->m_szEffectFileName[28] = 0;
  *(_DWORD *)this->m_szHitFileName = 0;
  *(_DWORD *)&this->m_szHitFileName[4] = 0;
  *(_DWORD *)&this->m_szHitFileName[8] = 0;
  *(_DWORD *)&this->m_szHitFileName[12] = 0;
  *(_DWORD *)&this->m_szHitFileName[16] = 0;
  *(_DWORD *)&this->m_szHitFileName[20] = 0;
  *(_DWORD *)&this->m_szHitFileName[24] = 0;
  *(_DWORD *)&this->m_szHitFileName[28] = 0;
  *(_DWORD *)this->m_szCastingFileName = 0;
  *(_DWORD *)&this->m_szCastingFileName[4] = 0;
  *(_DWORD *)&this->m_szCastingFileName[8] = 0;
  *(_DWORD *)&this->m_szCastingFileName[12] = 0;
  *(_DWORD *)&this->m_szCastingFileName[16] = 0;
  *(_DWORD *)&this->m_szCastingFileName[20] = 0;
  *(_DWORD *)&this->m_szCastingFileName[24] = 0;
  *(_DWORD *)&this->m_szCastingFileName[28] = 0;
  memset(this->m_szSkillDescribe, 0, sizeof(this->m_szSkillDescribe));
}

//----- (00490E40) --------------------------------------------------------
void __thiscall Skill::ProtoType::ProtoType(Skill::ProtoType *this)
{
  Item::SpriteData::SpriteData(&this->m_SpriteInfo);
  Skill::ProtoType::Initialize(this);
}

//----- (00490E60) --------------------------------------------------------
void __thiscall CFSMState::~CFSMState(CFSMState *this)
{
  if ( this->m_pdwInputs )
  {
    operator delete(this->m_pdwInputs);
    this->m_pdwInputs = 0;
  }
  if ( this->m_pdwOutputState )
  {
    operator delete(this->m_pdwOutputState);
    this->m_pdwOutputState = 0;
  }
}

//----- (00490EA0) --------------------------------------------------------
void __thiscall CFSMState::AddTransition(CFSMState *this, int Input, int OutputID)
{
  int m_dwNumberOfTransistions; // edx
  int v4; // eax
  int *m_pdwOutputState; // esi

  m_dwNumberOfTransistions = this->m_dwNumberOfTransistions;
  v4 = 0;
  if ( this->m_dwNumberOfTransistions > 0 )
  {
    m_pdwOutputState = this->m_pdwOutputState;
    do
    {
      if ( !*m_pdwOutputState )
        break;
      ++v4;
      ++m_pdwOutputState;
    }
    while ( v4 < m_dwNumberOfTransistions );
  }
  if ( v4 < m_dwNumberOfTransistions )
  {
    this->m_pdwInputs[v4] = Input;
    this->m_pdwOutputState[v4] = OutputID;
  }
}

//----- (00490EE0) --------------------------------------------------------
int __thiscall CFSMState::GetOutput(CFSMState *this, int Input)
{
  int result; // eax
  int v3; // edx
  int *m_pdwOutputState; // edi

  result = this->m_dwStateID;
  v3 = 0;
  if ( this->m_dwNumberOfTransistions > 0 )
  {
    m_pdwOutputState = this->m_pdwOutputState;
    while ( m_pdwOutputState[v3] )
    {
      if ( Input == this->m_pdwInputs[v3] )
        return m_pdwOutputState[v3];
      if ( ++v3 >= this->m_dwNumberOfTransistions )
        return result;
    }
  }
  return result;
}

//----- (00490F20) --------------------------------------------------------
void __thiscall CFSMState::CFSMState(CFSMState *this, int StateID, int Transitions)
{
  int v3; // eax
  int *v5; // edi
  int *v6; // edi

  v3 = Transitions;
  if ( !Transitions )
    v3 = 1;
  this->m_dwNumberOfTransistions = v3;
  this->m_dwStateID = StateID;
  v5 = (int *)operator new[](4 * v3);
  this->m_pdwInputs = v5;
  if ( v5 )
  {
    if ( this->m_dwNumberOfTransistions > 0 )
      memset(v5, 0, 4 * this->m_dwNumberOfTransistions);
    v6 = (int *)operator new[](4 * this->m_dwNumberOfTransistions);
    this->m_pdwOutputState = v6;
    if ( v6 )
    {
      if ( this->m_dwNumberOfTransistions > 0 )
        memset(v6, 0, 4 * this->m_dwNumberOfTransistions);
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CFSMState::CFSMState",
        aDWorkRylSource_69,
        29,
        aMPdwoutputstat,
        this->m_dwNumberOfTransistions);
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CFSMState::CFSMState",
      aDWorkRylSource_69,
      19,
      aMPdwinputs,
      this->m_dwNumberOfTransistions);
  }
}

//----- (00490FC0) --------------------------------------------------------
char __thiscall CFSM::AddState(CFSM *this, CFSMState *lpNewState)
{
  if ( this->m_nStateNum >= 8 )
    return 0;
  this->m_lpFSMState[this->m_nStateNum++] = lpNewState;
  return 1;
}

//----- (00490FE0) --------------------------------------------------------
void __thiscall CSingleton<CFSM>::~CSingleton<CFSM>(CSingleton<CFSM> *this)
{
  CSingleton<CFSM>::ms_pSingleton = 0;
}

//----- (00490FF0) --------------------------------------------------------
void __thiscall CFSM::CFSM(CFSM *this)
{
  CSingleton<CFSM>::ms_pSingleton = this;
  this->m_nStateNum = 0;
  this->m_lpFSMState[0] = 0;
  this->m_lpFSMState[1] = 0;
  this->m_lpFSMState[2] = 0;
  this->m_lpFSMState[3] = 0;
  this->m_lpFSMState[4] = 0;
  this->m_lpFSMState[5] = 0;
  this->m_lpFSMState[6] = 0;
  this->m_lpFSMState[7] = 0;
}

//----- (00491020) --------------------------------------------------------
void __thiscall CFSM::~CFSM(CFSM *this)
{
  CFSM *v1; // ebx
  CFSMState **m_lpFSMState; // esi
  CFSMState *v3; // edi

  v1 = this + 1;
  m_lpFSMState = this->m_lpFSMState;
  if ( this->m_lpFSMState != (CFSMState **)&this[1] )
  {
    do
    {
      v3 = *m_lpFSMState;
      if ( *m_lpFSMState )
      {
        CFSMState::~CFSMState(*m_lpFSMState);
        operator delete(v3);
      }
      *m_lpFSMState++ = 0;
    }
    while ( m_lpFSMState != (CFSMState **)v1 );
  }
  CSingleton<CFSM>::ms_pSingleton = 0;
}

//----- (00491090) --------------------------------------------------------
int __thiscall CFSM::StateTransition(CFSM *this, int nCrurrentState, int Input)
{
  int result; // eax
  CFSM *v4; // esi
  CFSMState **m_lpFSMState; // ecx
  CFSMState *v6; // ecx

  result = nCrurrentState;
  if ( nCrurrentState )
  {
    v4 = this + 1;
    m_lpFSMState = this->m_lpFSMState;
    if ( m_lpFSMState != (CFSMState **)v4 )
    {
      while ( *m_lpFSMState )
      {
        if ( nCrurrentState == (*m_lpFSMState)->m_dwStateID )
        {
          v6 = *m_lpFSMState;
          if ( v6 )
            return CFSMState::GetOutput(v6, Input);
          return result;
        }
        if ( ++m_lpFSMState == (CFSMState **)v4 )
          return result;
      }
    }
  }
  return result;
}

//----- (004910D0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadLine(CDelimitedFile *this)
{
  char *m_szLine; // ebx

  m_szLine = this->m_szLine;
  if ( fgets(this->m_szLine, 0x4000, this->m_fpFile) )
  {
    while ( strcmp("\n", m_szLine) )
    {
      if ( *m_szLine != 59 )
      {
        this->m_dwColumn = 0;
        qmemcpy(this->m_szBackupLine, m_szLine, sizeof(this->m_szBackupLine));
        return 1;
      }
      if ( !fgets(m_szLine, 0x4000, this->m_fpFile) )
        return 0;
    }
  }
  return 0;
}

//----- (00491150) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, bool *bNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax
  const char *v5; // esi
  CDelimitedFile *szString; // [esp+0h] [ebp-4h] OVERLAPPED BYREF

  szString = this;
  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  v5 = v4;
  if ( !v4 || !*v4 )
    return 0;
  *bNumber = 0;
  if ( atoi(v4) == 1 )
    *bNumber = 1;
  strncpy((char *)&szString, v5, 1u);
  if ( !strcmp((const char *)&szString, "O") )
    *bNumber = 1;
  return 1;
}
// 491150: variables would overlap: ^18.4 and stkvar "szString" ^18.2(has user info)

//----- (004911E0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, float *fNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *fNumber = atof(v4);
  return 1;
}

//----- (00491230) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, unsigned int *fNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *fNumber = atol(v4);
  return 1;
}

//----- (00491280) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, int *iNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *iNumber = atoi(v4);
  return 1;
}

//----- (004912D0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, __int16 *iNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *iNumber = atoi(v4);
  return 1;
}

//----- (00491320) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, char *iNumber)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *iNumber = atoi(v4);
  return 1;
}

//----- (00491370) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadData(CDelimitedFile *this, __int64 *i64Number)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v4; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v4 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v4 || !*v4 )
    return 0;
  *i64Number = _atoi64(v4);
  return 1;
}

//----- (004913C0) --------------------------------------------------------
char __thiscall CDelimitedFile::ReadString(CDelimitedFile *this, char *szString, size_t dwSize)
{
  unsigned int m_dwColumn; // eax
  char *m_szLine; // eax
  char *v5; // eax

  m_dwColumn = this->m_dwColumn;
  this->m_dwColumn = m_dwColumn + 1;
  if ( m_dwColumn )
    m_szLine = 0;
  else
    m_szLine = this->m_szLine;
  v5 = strtok(m_szLine, this->m_szDelimiter);
  if ( !v5 || !*v5 )
    return 0;
  strncpy(szString, v5, dwSize);
  return 1;
}

