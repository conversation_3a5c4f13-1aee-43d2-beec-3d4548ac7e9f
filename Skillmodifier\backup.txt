#define WIN32_LEAN_AND_MEAN

#include <Windows.h>
#include <iostream>
#include <vector>
#include <sstream>
#include <fstream>
#include <thread>
#include <chrono>
#include <algorithm> // For std::remove_if
#include "../Header/detours.h"
#include "../Header/License.h"

// Define constants for INI file path and update interval
const std::string INI_FILE_PATH = "cfg_skillmodifier.ini";
const std::chrono::milliseconds UPDATE_INTERVAL(1000); // Check every 1 seconds

// Constants already defined above, removing duplicate definitions

// Using the Detours library for function hooking instead of custom implementation

namespace skill
{
    // Class-specific critical multipliers (default is 1.5 or 150%)
    float fighter_crit = 1.5f;    // Fighter class critical multiplier
    float rogue_crit = 1.5f;      // Rogue class critical multiplier
    float mage_crit = 1.5f;       // Mage class critical multiplier
    float acolyte_crit = 1.5f;    // Acolyte class critical multiplier
    float defender_crit = 1.5f;   // Defender class critical multiplier
    float warrior_crit = 1.5f;    // Warrior class critical multiplier
    float assassin_crit = 1.5f;   // Assassin class critical multiplier
    float archer_crit = 1.5f;     // Archer class critical multiplier
    float sorcerer_crit = 1.5f;   // Sorcerer class critical multiplier
    float enchanter_crit = 1.5f;  // Enchanter class critical multiplier
    float priest_crit = 1.5f;     // Priest class critical multiplier
    float cleric_crit = 1.5f;     // Cleric class critical multiplier
    float combatant_crit = 1.5f;  // Combatant class critical multiplier
    float officiator_crit = 1.5f; // Officiator class critical multiplier
    float templar_crit = 1.5f;    // Templar class critical multiplier
    float attacker_crit = 1.5f;   // Attacker class critical multiplier
    float gunner_crit = 1.5f;     // Gunner class critical multiplier
    float runeoff_crit = 1.5f;    // RuneOff class critical multiplier
    float lifeoff_crit = 1.5f;    // LifeOff class critical multiplier
    float shadowoff_crit = 1.5f;  // ShadowOff class critical multiplier

    // Current active critical multiplier (will be set based on class)
    float active_crit = 1.5f; // Default critical multiplier

    float Bash = 0.0500f;       // Initial value which can be updated
    float backut = 0.0500f;     // Initial value which can be updated
    float combo = 0.0500f;      // Initial value which can be updated
    float shadow = 0.0500f;     // Initial value which can be updated
    float spirit = 0.0500f;     // Initial value which can be updated
    float multiple = 0.0500f;   // Initial value which can be updated
    float fatal = 0.0500f;      // Initial value which can be updated
    float skyrocket = 0.0500f;  // Initial value which can be updated
    float firearrow = 0.0500f;  // Initial value which can be updated
    float gunbomb = 0.0500f;    // Initial value which can be updated
    float energy = 0.0500f;     // Initial value which can be updated
    float vampire = 0.00010f;   // Initial value which can be updated
    float fireball = 0.00010f;  // Initial value which can be updated
    float electric = 0.00010f;  // Initial value which can be updated
    float ice = 0.00010f;       // Initial value which can be updated
    float death = 0.00010f;     // Initial value which can be updated
    float seal = 0.00010f;      // Initial value which can be updated
    float crush = 0.00010f;     // Initial value which can be updated
    float dark = 0.0100f;       // Initial value which can be updated
    float magicarw = 0.00010f;  // Initial value which can be updated
    float poison = 0.00010f;    // Initial value which can be updated
    float lightning = 0.00010f; // Initial value which can be updated
    float gaia = 0.00010f;      // Initial value which can be updated
    float instant = 0.00010f;   // Initial value which can be updated
    float blazing = 0.0500f;    // Initial value which can be updated
    float heal = 0.0100f;       // Initial value which can be updated
    float lotos = 0.0100f;      // Initial value which can be updated
    float harm = 0.00010f;      // Initial value which can be updated

    unsigned int RetContinue = 0x00421B69; // Default value if not read from the INI file

    __declspec(naked) void PatchFunction()
    {
        __asm
        {
            PUSH EBP
            // Use the active critical multiplier instead of blockvalue
            // This will apply the class-specific critical multiplier
            FMUL DWORD PTR DS:[active_crit]
            JMP[RetContinue]
        }
    }
}

// Function to read a float value from the INI file
float ReadFloatFromIni(const std::string &fileName, const std::string &section, const std::string &key, float defaultValue)

{
    char buffer[64];
    GetPrivateProfileStringA(section.c_str(), key.c_str(), "", buffer, sizeof(buffer), fileName.c_str());
    if (strlen(buffer) == 0)
    {
        return defaultValue;
    }
    return static_cast<float>(atof(buffer));
}

// Function to read an integer value from the INI file
int ReadIntFromIni(const std::string &fileName, const std::string &section, const std::string &key, int defaultValue)
{
    char buffer[64];
    GetPrivateProfileStringA(section.c_str(), key.c_str(), "", buffer, sizeof(buffer), fileName.c_str());
    if (strlen(buffer) == 0)
    {
        return defaultValue;
    }
    return atoi(buffer);
}

// Function to update configuration from INI file
void UpdateConfiguration()
{
    char currentDir[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, currentDir);
    std::string iniPath = std::string(currentDir) + "\\" + INI_FILE_PATH;

    // Skill values
    skill::Bash = ReadFloatFromIni(iniPath, "SKILL", "SLIGHT", 0.0500f);
    skill::backut = ReadFloatFromIni(iniPath, "SKILL", "BACKCUT", 0.0500f);
    skill::combo = ReadFloatFromIni(iniPath, "SKILL", "COMBOSTRIKE", 0.0500f);
    skill::shadow = ReadFloatFromIni(iniPath, "SKILL", "SHADOWSTRIKE", 0.0500f);
    skill::spirit = ReadFloatFromIni(iniPath, "SKILL", "SPIRITARROW", 0.0500f);
    skill::multiple = ReadFloatFromIni(iniPath, "SKILL", "MULTIPLEARROW", 0.0500f);
    skill::fatal = ReadFloatFromIni(iniPath, "SKILL", "FATALATTACK", 0.0500f);
    skill::skyrocket = ReadFloatFromIni(iniPath, "SKILL", "SKYROCKET", 0.0500f);
    skill::firearrow = ReadFloatFromIni(iniPath, "SKILL", "FIREARROW", 0.0500f);
    skill::gunbomb = ReadFloatFromIni(iniPath, "SKILL", "GUNBOMB", 0.0500f);
    skill::energy = ReadFloatFromIni(iniPath, "SKILL", "ENERGYABSORB", 0.0500f);
    skill::vampire = ReadFloatFromIni(iniPath, "SKILL", "VAMPIREKISS", 0.00010f);
    skill::fireball = ReadFloatFromIni(iniPath, "SKILL", "FIREBALL", 0.00010f);
    skill::electric = ReadFloatFromIni(iniPath, "SKILL", "ELECTRICSHOCK", 0.00010f);
    skill::ice = ReadFloatFromIni(iniPath, "SKILL", "ICEBLAZE", 0.00010f);
    skill::death = ReadFloatFromIni(iniPath, "SKILL", "DEATHBLAZE", 0.00010f);
    skill::seal = ReadFloatFromIni(iniPath, "SKILL", "SEALATTACK", 0.00010f);
    skill::crush = ReadFloatFromIni(iniPath, "SKILL", "CRUSHATTACK", 0.00010f);
    skill::dark = ReadFloatFromIni(iniPath, "SKILL", "DARKPOWER", 0.0100f);
    skill::magicarw = ReadFloatFromIni(iniPath, "SKILL", "MAGICARROW", 0.00010f);
    skill::poison = ReadFloatFromIni(iniPath, "SKILL", "POISON", 0.00010f);
    skill::lightning = ReadFloatFromIni(iniPath, "SKILL", "LIGHTNINGSTRIKE", 0.00010f);
    skill::gaia = ReadFloatFromIni(iniPath, "SKILL", "GAIAROOT", 0.00010f);
    skill::instant = ReadFloatFromIni(iniPath, "SKILL", "INSTANTDAMAGE", 0.00010f);
    skill::blazing = ReadFloatFromIni(iniPath, "SKILL", "BLAZINGSTRIKE", 0.0500f);
    skill::heal = ReadFloatFromIni(iniPath, "SKILL", "HEAL", 0.0100f);
    skill::lotos = ReadFloatFromIni(iniPath, "SKILL", "LOTOS", 0.0100f);
    skill::harm = ReadFloatFromIni(iniPath, "SKILL", "HARMTRANSFER", 0.00010f);
}

// Function to update memory with the current configuration values
void ApplyConfiguration()
{
    DWORD dwProtect;

    // NOP Block
    VirtualProtect((void *)0x00421b68, 1, PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x00421b68, 1) == FALSE)
    {
        memset((void *)0x00421b68, 0x90, 1);
    }
    VirtualProtect((void *)0x00421b68, 1, dwProtect, &dwProtect);

    // AMOUNT RESIST
    void *addresses31[] = {
        (void *)0x00508c64,
        (void *)0x00508c65,
        (void *)0x00508c66,
        (void *)0x00508c67,
        (void *)0x00508c68,
        (void *)0x00508c69,
        (void *)0x00508c6A,
        (void *)0x00508c6B,
        (void *)0x00508c6C,
        (void *)0x00508c6D};
    unsigned char values31[] = {0x90, 0x01, 0x90, 0x01, 0x90, 0x01, 0x90, 0x01, 0x90, 0x01};

    for (int i = 0; i < sizeof(addresses31) / sizeof(addresses31[0]); ++i)
    {
        VirtualProtect(addresses31[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses31[i], 1) == FALSE)
        {
            memset(addresses31[i], values31[i], 1);
        }
        VirtualProtect(addresses31[i], 1, dwProtect, &dwProtect);
    }

    // AMOUNT RESIST
    void *addresses32[] = {
        (void *)0x004306A2,
        (void *)0x004306A3,
        (void *)0x0043037F,
        (void *)0x00430380,
        (void *)0x0042FF61,
        (void *)0x0042FF62,
        (void *)0x0042EFB4,
        (void *)0x0042EFB5,
        (void *)0x0042EB44,
        (void *)0x0042EB45,
        (void *)0x0042E26D,
        (void *)0x0042E26E,
        (void *)0x0042DE5F,
        (void *)0x0042DE60,
        (void *)0x0042DA4F,
        (void *)0x0042DA50,
        (void *)0x0042D01D,
        (void *)0x0042D01E,
        (void *)0x0042C8FD,
        (void *)0x0042C8FE};
    unsigned char values32[] = {0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50, 0x8C, 0x50};

    for (int i = 0; i < sizeof(addresses32) / sizeof(addresses32[0]); ++i)
    {
        VirtualProtect(addresses32[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses32[i], 1) == FALSE)
        {
            memset(addresses32[i], values32[i], 1);
        }
        VirtualProtect(addresses32[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Bash
    void *addresses[] = {
        (void *)0x0042C579,
        (void *)0x0042C57A,
        (void *)0x0042C57B,
        (void *)0x0042C57C,
        (void *)0x0042C57D,
        (void *)0x0042C57E};
    unsigned char values[] = {0xD8, 0x0D, 0x2D, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses) / sizeof(addresses[0]); ++i)
    {
        VirtualProtect(addresses[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses[i], 1) == FALSE)
        {
            memset(addresses[i], values[i], 1);
        }
        VirtualProtect(addresses[i], 1, dwProtect, &dwProtect);
    }

    // Fmul backut
    void *addresses2[] = {
        (void *)0x0042D313,
        (void *)0x0042D314,
        (void *)0x0042D315,
        (void *)0x0042D316,
        (void *)0x0042D317,
        (void *)0x0042D318};
    unsigned char values2[] = {0xD8, 0x0D, 0x31, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses2) / sizeof(addresses2[0]); ++i)
    {
        VirtualProtect(addresses2[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses2[i], 1) == FALSE)
        {
            memset(addresses2[i], values2[i], 1);
        }
        VirtualProtect(addresses2[i], 1, dwProtect, &dwProtect);
    }

    // Fmul combo
    void *addresses3[] = {
        (void *)0x0042D503,
        (void *)0x0042D504,
        (void *)0x0042D505,
        (void *)0x0042D506,
        (void *)0x0042D507,
        (void *)0x0042D508};
    unsigned char values3[] = {0xD8, 0x0D, 0x35, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses3) / sizeof(addresses3[0]); ++i)
    {
        VirtualProtect(addresses3[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses3[i], 1) == FALSE)
        {
            memset(addresses3[i], values3[i], 1);
        }
        VirtualProtect(addresses3[i], 1, dwProtect, &dwProtect);
    }

    // Fmul shadow
    void *addresses4[] = {
        (void *)0x00427CCF,
        (void *)0x00427CD0,
        (void *)0x00427CD1,
        (void *)0x00427CD2,
        (void *)0x00427CD3,
        (void *)0x00427CD4};
    unsigned char values4[] = {0xD8, 0x0D, 0x39, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses4) / sizeof(addresses4[0]); ++i)
    {
        VirtualProtect(addresses4[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses4[i], 1) == FALSE)
        {
            memset(addresses4[i], values4[i], 1);
        }
        VirtualProtect(addresses4[i], 1, dwProtect, &dwProtect);
    }

    // Fmul spirit
    void *addresses5[] = {
        (void *)0x00427DBF,
        (void *)0x00427DC0,
        (void *)0x00427DC1,
        (void *)0x00427DC2,
        (void *)0x00427DC3,
        (void *)0x00427DC4};
    unsigned char values5[] = {0xD8, 0x0D, 0x3D, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses5) / sizeof(addresses5[0]); ++i)
    {
        VirtualProtect(addresses5[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses5[i], 1) == FALSE)
        {
            memset(addresses5[i], values5[i], 1);
        }
        VirtualProtect(addresses5[i], 1, dwProtect, &dwProtect);
    }

    // Fmul multiple
    void *addresses6[] = {
        (void *)0x00427E9B,
        (void *)0x00427E9C,
        (void *)0x00427E9D,
        (void *)0x00427E9E,
        (void *)0x00427E9F,
        (void *)0x00427EA0};
    unsigned char values6[] = {0xD8, 0x0D, 0x41, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses6) / sizeof(addresses6[0]); ++i)
    {
        VirtualProtect(addresses6[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses6[i], 1) == FALSE)
        {
            memset(addresses6[i], values6[i], 1);
        }
        VirtualProtect(addresses6[i], 1, dwProtect, &dwProtect);
    }

    // Fmul fatal
    void *addresses7[] = {
        (void *)0x0042F942,
        (void *)0x0042F943,
        (void *)0x0042F944,
        (void *)0x0042F945,
        (void *)0x0042F946,
        (void *)0x0042F947};
    unsigned char values7[] = {0xD8, 0x0D, 0x46, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses7) / sizeof(addresses7[0]); ++i)
    {
        VirtualProtect(addresses7[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses7[i], 1) == FALSE)
        {
            memset(addresses7[i], values7[i], 1);
        }
        VirtualProtect(addresses7[i], 1, dwProtect, &dwProtect);
    }

    // Fmul skyrocket
    void *addresses8[] = {
        (void *)0x0042FBC3,
        (void *)0x0042FBC4,
        (void *)0x0042FBC5,
        (void *)0x0042FBC6,
        (void *)0x0042FBC7,
        (void *)0x0042FBC8};
    unsigned char values8[] = {0xD8, 0x0D, 0x4A, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses8) / sizeof(addresses8[0]); ++i)
    {
        VirtualProtect(addresses8[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses8[i], 1) == FALSE)
        {
            memset(addresses8[i], values8[i], 1);
        }
        VirtualProtect(addresses8[i], 1, dwProtect, &dwProtect);
    }

    // Fmul firearrow
    void *addresses9[] = {
        (void *)0x004299BE,
        (void *)0x004299BF,
        (void *)0x004299C0,
        (void *)0x004299C1,
        (void *)0x004299C2,
        (void *)0x004299C3};
    unsigned char values9[] = {0xD8, 0x0D, 0x4E, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses9) / sizeof(addresses9[0]); ++i)
    {
        VirtualProtect(addresses9[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses9[i], 1) == FALSE)
        {
            memset(addresses9[i], values9[i], 1);
        }
        VirtualProtect(addresses9[i], 1, dwProtect, &dwProtect);
    }

    // Fmul gunbomb
    void *addresses10[] = {
        (void *)0x00429ACE,
        (void *)0x00429ACF,
        (void *)0x00429AD0,
        (void *)0x00429AD1,
        (void *)0x00429AD2,
        (void *)0x00429AD3};
    unsigned char values10[] = {0xD8, 0x0D, 0x52, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses10) / sizeof(addresses10[0]); ++i)
    {
        VirtualProtect(addresses10[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses10[i], 1) == FALSE)
        {
            memset(addresses10[i], values10[i], 1);
        }
        VirtualProtect(addresses10[i], 1, dwProtect, &dwProtect);
    }

    // Fmul energy
    void *addresses11[] = {
        (void *)0x004D475B,
        (void *)0x004D475C,
        (void *)0x004D475D,
        (void *)0x004D475E,
        (void *)0x004D475F,
        (void *)0x004D4760,
        (void *)0x004D4761,
        (void *)0x004D4762,
        (void *)0x004D4763,
        (void *)0x004D4764,
        (void *)0x004D4765,
        (void *)0x004D4766,
        (void *)0x004D4767,
        (void *)0x004D4768,
        (void *)0x004D4769,
        (void *)0x004D476A,
        (void *)0x004D476B,
        (void *)0x004D476C,
        (void *)0x004D476D,
        (void *)0x004D476E,
        (void *)0x004D476F,
        (void *)0x004D4770,
        (void *)0x004D4771,
        (void *)0x004D4772,
        (void *)0x004D4773,
        (void *)0x004D4774,
        (void *)0x004D4775,
        (void *)0x004D4776,
        (void *)0x004D4777,
        (void *)0x004D4778,
        (void *)0x004D4779,
        (void *)0x004D477A,
        (void *)0x004D477B,
        (void *)0x004D477C,
        (void *)0x004D477D,
        (void *)0x004D477E,
        (void *)0x004D477F,
        (void *)0x004D4780,
        (void *)0x004D4781,
        (void *)0x004D4782,
        (void *)0x004D4783,
        (void *)0x004D4784,
        (void *)0x004D4785,
        (void *)0x004D4786,
        (void *)0x004D4787,
        (void *)0x004D4788,
        (void *)0x004D4789,
        (void *)0x004D478A,
        (void *)0x004D478B,
        (void *)0x004D478C,
        (void *)0x004D478D,
        (void *)0x004D478E,
        (void *)0x004D478F,
        (void *)0x004D4790,
        (void *)0x004D4791,
        (void *)0x004D4792,
        (void *)0x004D4793,
        (void *)0x004D4794,
        (void *)0x004D4795,
        (void *)0x004D4796,
        (void *)0x004D4797,
        (void *)0x004D4798,
        (void *)0x004D4799,
        (void *)0x004D479A,
        (void *)0x004D479B,
        (void *)0x004D479C,
        (void *)0x004D479D,
        (void *)0x004D479E,
        (void *)0x004D479F,
        (void *)0x004D47A0,
        (void *)0x004D47A1,
        (void *)0x004D47A2,
        (void *)0x004D47A3,
        (void *)0x004D47A4,
        (void *)0x004D47A5,
        (void *)0x004D47A6,
        (void *)0x004D47A7,
        (void *)0x004D47A8,
        (void *)0x004D47A9,
        (void *)0x004D47AA,
        (void *)0x004D47AB,
        (void *)0x004D47AC,
        (void *)0x004D47AD,
        (void *)0x004D47AE,
        (void *)0x004D47AF,
        (void *)0x004D47B0,
        (void *)0x004D47B1,
        (void *)0x004D47B2,
        (void *)0x004D47B3,
        (void *)0x004D47B4,
        (void *)0x004D47B5,
        (void *)0x004D47B6,
        (void *)0x004D47B7,
        (void *)0x004D47B8,
        (void *)0x004D47B9,
        (void *)0x004D47BA,
        (void *)0x004D47BB,
        (void *)0x004D47BC,
        (void *)0x004D47BD,
        (void *)0x004D47BE,
        (void *)0x004D47BF,
        (void *)0x004D47C0,
        (void *)0x004D47C1,
        (void *)0x004D47C2,
        (void *)0x004D47C3,
        (void *)0x004D47C4,
        (void *)0x004D47C5,
        (void *)0x004D47C6,
        (void *)0x004D47C7,
        (void *)0x004D47C8,
        (void *)0x004D47C9,
        (void *)0x004D47CA,
        (void *)0x004D47CB,
        (void *)0x004D47CC,
        (void *)0x004D47CD,
        (void *)0x004D47CE,
        (void *)0x004D47CF,
        (void *)0x004D47D0,
        (void *)0x004D47D1,
        (void *)0x004D47D2,
        (void *)0x004D47D3,
        (void *)0x004D47D4,
        (void *)0x004D47D5,
        (void *)0x004D47D6,
        (void *)0x004D47D7,
        (void *)0x004D47D8,
        (void *)0x004D47D9,
        (void *)0x004D47DA,
        (void *)0x004D47DB,
        (void *)0x004D47DC,
        (void *)0x004D47DD,
        (void *)0x004D47DE,
        (void *)0x004D47DF,
        (void *)0x004D47E0,
        (void *)0x004D47E1,
        (void *)0x004D47E2,
        (void *)0x004D47E3,
        (void *)0x004D47E4,
        (void *)0x004D47E5,
        (void *)0x004D47E6,
        (void *)0x004D47E7,
        (void *)0x004D47E8,
        (void *)0x004D47E9,
        (void *)0x004D47EA,
        (void *)0x004D47EB,
        (void *)0x004D47EC,
        (void *)0x004D47ED,
        (void *)0x004D47EE,
        (void *)0x004D47EF,
        (void *)0x004D47F0,
        (void *)0x004D47F1,
        (void *)0x004D47F2,
        (void *)0x004D47F3,
        (void *)0x004D47F4,
        (void *)0x004D47F5,
        (void *)0x004D47F6,
        (void *)0x004D47F7,
        (void *)0x004D47F8,
        (void *)0x004D47F9,
        (void *)0x004D47FA,
        (void *)0x004D47FB,
        (void *)0x004D47FC,
        (void *)0x004D47FD,
        (void *)0x004D47FE,
        (void *)0x004D47FF,
        (void *)0x004D4800,
        (void *)0x004D4801,
        (void *)0x004D4802,
        (void *)0x004D4803,
        (void *)0x004D4804,
        (void *)0x004D4805,
        (void *)0x004D4806,
        (void *)0x004D4807,
        (void *)0x004D4808,
        (void *)0x004D4809,
        (void *)0x004D480A,
        (void *)0x004D480B,
        (void *)0x004D480C,
        (void *)0x004D480D,
        (void *)0x004D480E,
        (void *)0x004D480F,
        (void *)0x004D4810,
        (void *)0x004D4811,
        (void *)0x004D4812,
        (void *)0x004D4813,
        (void *)0x004D4814,
        (void *)0x004D4815,
        (void *)0x004D4816,
        (void *)0x004D4817,
        (void *)0x004D4818,
        (void *)0x004D4819,
        (void *)0x004D481A,
        (void *)0x004D481B,
        (void *)0x004D481C,
        (void *)0x004D481D,
        (void *)0x004D481E,
        (void *)0x004D481F,
        (void *)0x004D4820,
        (void *)0x004D4821,
        (void *)0x004D4822,
        (void *)0x004D4823,
        (void *)0x004D4824,
        (void *)0x004D4825,
        (void *)0x004D4826,
        (void *)0x004D4827,
        (void *)0x004D4828,
        (void *)0x004D4829,
        (void *)0x004D482A,
        (void *)0x004D482B,
        (void *)0x004D482C,
        (void *)0x004D482D,
        (void *)0x004D482E,
        (void *)0x004D482F,
        (void *)0x004D4830,
        (void *)0x004D4831,
        (void *)0x004D4832,
        (void *)0x004D4833,
        (void *)0x004D4834,
        (void *)0x004D4835,
        (void *)0x004D4836,
        (void *)0x004D4837,
        (void *)0x004D4838,
        (void *)0x004D4839,
        (void *)0x004D483A,
        (void *)0x004D483B,
        (void *)0x004D483C,
        (void *)0x004D483D,
        (void *)0x004D483E,
        (void *)0x004D483F,
        (void *)0x004D4840,
        (void *)0x004D4841,
        (void *)0x004D4842,
        (void *)0x004D4843,
        (void *)0x004D4844,
        (void *)0x004D4845,
        (void *)0x004D4846,
        (void *)0x004D4847,
        (void *)0x004D4848,
        (void *)0x004D4849,
        (void *)0x004D484A,
        (void *)0x004D484B,
        (void *)0x004D484C,
        (void *)0x004D484D,
        (void *)0x004D484E,
        (void *)0x004D484F,
        (void *)0x004D4850,
        (void *)0x004D4851,
        (void *)0x004D4852,
        (void *)0x004D4853,
        (void *)0x004D4854,
        (void *)0x004D4855,
        (void *)0x004D4856,
        (void *)0x004D4857,
        (void *)0x004D4858,
        (void *)0x004D4859,
        (void *)0x004D485A,
        (void *)0x004D485B,
        (void *)0x004D485C,
        (void *)0x004D485D,
        (void *)0x004D485E,
        (void *)0x004D485F,
        (void *)0x004D4860,
        (void *)0x004D4861,
        (void *)0x004D4862,
        (void *)0x004D4863,
        (void *)0x004D4864,
        (void *)0x004D4865,
        (void *)0x004D4866,
        (void *)0x004D4867,
        (void *)0x004D4868,
        (void *)0x004D4869,
        (void *)0x004D486A,
        (void *)0x004D486B,
        (void *)0x004D486C,
        (void *)0x004D486D,
        (void *)0x004D486E,
        (void *)0x004D486F,
        (void *)0x004D4870,
        (void *)0x004D4871,
        (void *)0x004D4872,
        (void *)0x004D4873,
        (void *)0x004D4874,
        (void *)0x004D4875,
        (void *)0x004D4876,
        (void *)0x004D4877,
        (void *)0x004D4878,
        (void *)0x004D4879,
        (void *)0x004D487A,
        (void *)0x004D487B,
        (void *)0x004D487C,
        (void *)0x004D487D,
        (void *)0x004D487E,
        (void *)0x004D487F,
        (void *)0x004D4880,
        (void *)0x004D4881,
        (void *)0x004D4882,
        (void *)0x004D4883,
        (void *)0x004D4884,
        (void *)0x004D4885,

    };

    unsigned char values11[] = {0x83, 0xEC, 0x10, 0x53, 0x8B, 0x5C, 0x24, 0x24, 0x56, 0x8B, 0x74, 0x24, 0x24, 0x85, 0xF6, 0x0F, 0x84, 0xE8, 0x00, 0x00, 0x00, 0x85, 0xDB, 0x0F, 0x84, 0xE0, 0x00, 0x00, 0x00, 0x57, 0x8B, 0x7C, 0x24, 0x24, 0x56, 0x57, 0xE8, 0x2C, 0x87, 0xF4, 0xFF, 0x83, 0xC4, 0x08, 0x66, 0x3D, 0xFF, 0xFF, 0x75, 0x0A, 0x5F, 0x5E, 0x66, 0x33, 0xC0, 0x5B, 0x83, 0xC4, 0x10, 0xC3, 0x8B, 0x44, 0x24, 0x20, 0x53, 0x56, 0x57, 0x50, 0xE8, 0x2C, 0x79, 0xF5, 0xFF, 0x83, 0xC4, 0x10, 0x84, 0xC0, 0x75, 0x09, 0x8B, 0x4C, 0x24, 0x38, 0x66, 0xC7, 0x01, 0x06, 0x00, 0x66, 0x8B, 0x4C, 0x24, 0x26, 0x0F, 0xBE, 0xD5, 0x89, 0x54, 0x24, 0x28, 0x0F, 0xB6, 0xC1, 0xDB, 0x44, 0x24, 0x28, 0xC1, 0xE8, 0x04, 0x66, 0x0F, 0xBE, 0xCD, 0xD8, 0x0D, 0xEE, 0x49, 0x4D, 0x00, 0x6A, 0x00, 0xD8, 0x04, 0x85, 0xDC, 0xCA, 0x4D, 0x00, 0x0F, 0xBF, 0x04, 0x45, 0x74, 0xC5, 0x4D, 0x00, 0x66, 0x03, 0xC1, 0xD9, 0x5C, 0x24, 0x14, 0x8B, 0xCE, 0xC6, 0x44, 0x24, 0x10, 0x01, 0x8D, 0x14, 0x85, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x66, 0x89, 0x44, 0x24, 0x1A, 0x66, 0x89, 0x44, 0x24, 0x1C, 0x8B, 0x06, 0x66, 0x89, 0x54, 0x24, 0x18, 0xFF, 0x50, 0x38, 0x66, 0x85, 0xC0, 0x0F, 0x84, 0x74, 0xFF, 0xFF, 0xFF, 0x8B, 0x4C, 0x24, 0x34, 0x51, 0x8D, 0x54, 0x24, 0x10, 0x52, 0x56, 0x8B, 0xCB, 0xE8, 0x85, 0xD2, 0xF4, 0xFF, 0x6A, 0x01, 0x8B, 0xCE, 0x8B, 0xF8, 0x8B, 0x06, 0xFF, 0x50, 0x38, 0x66, 0x85, 0xC0, 0x0F, 0x84, 0x4E, 0xFF, 0xFF, 0xFF, 0x0F, 0xB7, 0xCF, 0x6A, 0x02, 0x51, 0x56, 0x8D, 0x4B, 0x30, 0xE8, 0x62, 0x79, 0xF8, 0xFF, 0x66, 0x8B, 0xC7, 0x5F, 0x5E, 0x5B, 0x83, 0xC4, 0x10, 0xC3, 0x53, 0x56, 0x68, 0xA8, 0xBF, 0x4D, 0x00, 0x68, 0x32, 0x0C, 0x00, 0x00, 0x68, 0xC0, 0xBD, 0x4D, 0x00, 0x68, 0xBC, 0xCA, 0x4D, 0x00, 0x6A, 0x05, 0x68, 0x58, 0xEF, 0x50, 0x00, 0xE8, 0x06, 0x3F, 0xF3, 0xFF, 0x83, 0xC4, 0x20, 0x5E, 0x66, 0x33, 0xC0, 0x5B, 0x83, 0xC4, 0x10, 0xC3};

    for (int i = 0; i < sizeof(addresses11) / sizeof(addresses11[0]); ++i)
    {
        VirtualProtect(addresses11[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses11[i], 1) == FALSE)
        {
            memset(addresses11[i], values11[i], 1);
        }
        VirtualProtect(addresses11[i], 1, dwProtect, &dwProtect);
    }

    // Fmul energy3
    void *addresses13[] = {
        (void *)0x00432246,
        (void *)0x00432247,
        (void *)0x00432248,
        (void *)0x00432249,
        (void *)0x0043224A

    };
    unsigned char values13[] = {0x68, 0x5B, 0x47, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses13) / sizeof(addresses13[0]); ++i)
    {
        VirtualProtect(addresses13[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses13[i], 1) == FALSE)
        {
            memset(addresses13[i], values13[i], 1);
        }
        VirtualProtect(addresses13[i], 1, dwProtect, &dwProtect);
    }

    // Fmul vampire
    void *addresses14[] = {
        (void *)0x00427630,
        (void *)0x00427631,
        (void *)0x00427632,
        (void *)0x00427633,
        (void *)0x00427634,
        (void *)0x00427635

    };
    unsigned char values14[] = {0xd8, 0x0D, 0xF7, 0x49, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses14) / sizeof(addresses14[0]); ++i)
    {
        VirtualProtect(addresses14[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses14[i], 1) == FALSE)
        {
            memset(addresses14[i], values14[i], 1);
        }
        VirtualProtect(addresses14[i], 1, dwProtect, &dwProtect);
    }

    // Fmul fireball
    void *addresses15[] = {
        (void *)0x00428111,
        (void *)0x00428112,
        (void *)0x00428113,
        (void *)0x00428114,
        (void *)0x00428115,
        (void *)0x00428116

    };
    unsigned char values15[] = {0xD8, 0x0D, 0xFB, 0x49, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses15) / sizeof(addresses15[0]); ++i)
    {
        VirtualProtect(addresses15[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses15[i], 1) == FALSE)
        {
            memset(addresses15[i], values15[i], 1);
        }
        VirtualProtect(addresses15[i], 1, dwProtect, &dwProtect);
    }

    // Fmul electric
    void *addresses16[] = {
        (void *)0x0042DACE,
        (void *)0x0042DACF,
        (void *)0x0042DAD0,
        (void *)0x0042DAD1,
        (void *)0x0042DAD2,
        (void *)0x0042DAD3

    };
    unsigned char values16[] = {0xD8, 0x0D, 0xFF, 0x49, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses16) / sizeof(addresses16[0]); ++i)
    {
        VirtualProtect(addresses16[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses16[i], 1) == FALSE)
        {
            memset(addresses16[i], values16[i], 1);
        }
        VirtualProtect(addresses16[i], 1, dwProtect, &dwProtect);
    }

    // Fmul ice
    void *addresses17[] = {
        (void *)0x0042DEDE,
        (void *)0x0042DEDF,
        (void *)0x0042DEE0,
        (void *)0x0042DEE1,
        (void *)0x0042DEE2,
        (void *)0x0042DEE3

    };
    unsigned char values17[] = {0xD8, 0x0D, 0x03, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses17) / sizeof(addresses17[0]); ++i)
    {
        VirtualProtect(addresses17[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses17[i], 1) == FALSE)
        {
            memset(addresses17[i], values17[i], 1);
        }
        VirtualProtect(addresses17[i], 1, dwProtect, &dwProtect);
    }

    // Fmul death
    void *addresses18[] = {
        (void *)0x004282AC,
        (void *)0x004282AD,
        (void *)0x004282AE,
        (void *)0x004282AF,
        (void *)0x004282B0,
        (void *)0x004282B1

    };
    unsigned char values18[] = {0xD8, 0x0D, 0x07, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses18) / sizeof(addresses18[0]); ++i)
    {
        VirtualProtect(addresses18[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses18[i], 1) == FALSE)
        {
            memset(addresses18[i], values18[i], 1);
        }
        VirtualProtect(addresses18[i], 1, dwProtect, &dwProtect);
    }

    // Fmul seal
    void *addresses19[] = {
        (void *)0x0042E2EC,
        (void *)0x0042E2ED,
        (void *)0x0042E2EE,
        (void *)0x0042E2EF,
        (void *)0x0042E2F0,
        (void *)0x0042E2F1

    };
    unsigned char values19[] = {0xD8, 0x0D, 0x0B, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses19) / sizeof(addresses19[0]); ++i)
    {
        VirtualProtect(addresses19[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses19[i], 1) == FALSE)
        {
            memset(addresses19[i], values19[i], 1);
        }
        VirtualProtect(addresses19[i], 1, dwProtect, &dwProtect);
    }

    // Fmul crush
    void *addresses20[] = {
        (void *)0x0042EBCA,
        (void *)0x0042EBCB,
        (void *)0x0042EBCC,
        (void *)0x0042EBCD,
        (void *)0x0042EBCE,
        (void *)0x0042EBCF

    };
    unsigned char values20[] = {0xD8, 0x0D, 0x0F, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses20) / sizeof(addresses20[0]); ++i)
    {
        VirtualProtect(addresses20[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses20[i], 1) == FALSE)
        {
            memset(addresses20[i], values20[i], 1);
        }
        VirtualProtect(addresses20[i], 1, dwProtect, &dwProtect);
    }

    // Fmul dark
    void *addresses21[] = {
        (void *)0x00426A4E,
        (void *)0x00426A4F,
        (void *)0x00426A50,
        (void *)0x00426A51,
        (void *)0x00426A52,
        (void *)0x00426A53

    };
    unsigned char values21[] = {0xD8, 0x0D, 0x13, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses21) / sizeof(addresses21[0]); ++i)
    {
        VirtualProtect(addresses21[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses21[i], 1) == FALSE)
        {
            memset(addresses21[i], values21[i], 1);
        }
        VirtualProtect(addresses21[i], 1, dwProtect, &dwProtect);
    }

    // Fmul magicarrow
    void *addresses22[] = {
        (void *)0x00429581,
        (void *)0x00429582,
        (void *)0x00429583,
        (void *)0x00429584,
        (void *)0x00429585,
        (void *)0x00429586

    };

    unsigned char values22[] = {0xD8, 0x0D, 0x18, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses22) / sizeof(addresses22[0]); ++i)
    {
        VirtualProtect(addresses22[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses22[i], 1) == FALSE)
        {
            memset(addresses22[i], values22[i], 1);
        }
        VirtualProtect(addresses22[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Poison
    void *addresses23[] = {
        (void *)0x0042FFE7,
        (void *)0x0042FFE8,
        (void *)0x0042FFE9,
        (void *)0x0042FFEA,
        (void *)0x0042FFEB,
        (void *)0x0042FFEC

    };

    unsigned char values23[] = {0xD8, 0x0D, 0x1C, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses23) / sizeof(addresses23[0]); ++i)
    {
        VirtualProtect(addresses23[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses23[i], 1) == FALSE)
        {
            memset(addresses23[i], values23[i], 1);
        }
        VirtualProtect(addresses23[i], 1, dwProtect, &dwProtect);
    }

    // Fmul lightning
    void *addresses24[] = {
        (void *)0x00429CC1,
        (void *)0x00429CC2,
        (void *)0x00429CC3,
        (void *)0x00429CC4,
        (void *)0x00429CC5,
        (void *)0x00429CC6

    };

    unsigned char values24[] = {0xD8, 0x0D, 0x20, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses24) / sizeof(addresses24[0]); ++i)
    {
        VirtualProtect(addresses24[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses24[i], 1) == FALSE)
        {
            memset(addresses24[i], values24[i], 1);
        }
        VirtualProtect(addresses24[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Gaia
    void *addresses25[] = {
        (void *)0x004303FE,
        (void *)0x004303FF,
        (void *)0x00430400,
        (void *)0x00430401,
        (void *)0x00430402,
        (void *)0x00430403

    };

    unsigned char values25[] = {0xD8, 0x0D, 0x24, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses25) / sizeof(addresses25[0]); ++i)
    {
        VirtualProtect(addresses25[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses25[i], 1) == FALSE)
        {
            memset(addresses25[i], values25[i], 1);
        }
        VirtualProtect(addresses25[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Instant
    void *addresses26[] = {
        (void *)0x00429E5C,
        (void *)0x00429E5D,
        (void *)0x00429E5E,
        (void *)0x00429E5F,
        (void *)0x00429E60,
        (void *)0x00429E61

    };

    unsigned char values26[] = {0xD8, 0x0D, 0x28, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses26) / sizeof(addresses26[0]); ++i)
    {
        VirtualProtect(addresses26[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses26[i], 1) == FALSE)
        {
            memset(addresses26[i], values26[i], 1);
        }
        VirtualProtect(addresses26[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Blazing
    void *addresses27[] = {
        (void *)0x00429F1C,
        (void *)0x00429F1D,
        (void *)0x00429F1E,
        (void *)0x00429F1F,
        (void *)0x00429F20,
        (void *)0x00429F21

    };
    unsigned char values27[] = {0xD8, 0x0D, 0x2C, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses27) / sizeof(addresses27[0]); ++i)
    {
        VirtualProtect(addresses27[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses27[i], 1) == FALSE)
        {
            memset(addresses27[i], values27[i], 1);
        }
        VirtualProtect(addresses27[i], 1, dwProtect, &dwProtect);
    }

    // Fmul Heal
    void *addresses28[] = {
        (void *)0x00428CE0,
        (void *)0x00428CE1,
        (void *)0x00428CE2,
        (void *)0x00428CE3,
        (void *)0x00428CE4,
        (void *)0x00428CE5

    };
    unsigned char values28[] = {0xD8, 0x0D, 0x31, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses28) / sizeof(addresses28[0]); ++i)
    {
        VirtualProtect(addresses28[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses28[i], 1) == FALSE)
        {
            memset(addresses28[i], values28[i], 1);
        }
        VirtualProtect(addresses28[i], 1, dwProtect, &dwProtect);
    }

    // Fmul lotos
    void *addresses29[] = {
        (void *)0x00428EC9,
        (void *)0x00428ECA,
        (void *)0x00428ECB,
        (void *)0x00428ECC,
        (void *)0x00428ECD,
        (void *)0x00428ECE

    };
    unsigned char values29[] = {0xD8, 0x0D, 0x35, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses29) / sizeof(addresses29[0]); ++i)
    {
        VirtualProtect(addresses29[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses29[i], 1) == FALSE)
        {
            memset(addresses29[i], values29[i], 1);
        }
        VirtualProtect(addresses29[i], 1, dwProtect, &dwProtect);
    }

    // Fmul harm
    void *addresses30[] = {
        (void *)0x004291F9,
        (void *)0x004291FA,
        (void *)0x004291FB,
        (void *)0x004291FC,
        (void *)0x004291FD,
        (void *)0x004291FE

    };
    unsigned char values30[] = {0xD8, 0x0D, 0x39, 0x4A, 0x4D, 0x00};

    for (int i = 0; i < sizeof(addresses30) / sizeof(addresses30[0]); ++i)
    {
        VirtualProtect(addresses30[i], 1, PAGE_READWRITE, &dwProtect);
        if (IsBadWritePtr(addresses30[i], 1) == FALSE)
        {
            memset(addresses30[i], values30[i], 1);
        }
        VirtualProtect(addresses30[i], 1, dwProtect, &dwProtect);
    }

    // Float Bash
    VirtualProtect((void *)0x004d472D, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d472D, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d472D, &skill::Bash, sizeof(float));
    }
    VirtualProtect((void *)0x004d472D, sizeof(float), dwProtect, &dwProtect);

    // Float backut
    VirtualProtect((void *)0x004D4731, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004D4731, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004D4731, &skill::backut, sizeof(float));
    }
    VirtualProtect((void *)0x004D4731, sizeof(float), dwProtect, &dwProtect);

    // Float combo
    VirtualProtect((void *)0x004d4735, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4735, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4735, &skill::combo, sizeof(float));
    }
    VirtualProtect((void *)0x004d4735, sizeof(float), dwProtect, &dwProtect);

    // Float shadow
    VirtualProtect((void *)0x004d4739, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4739, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4739, &skill::shadow, sizeof(float));
    }
    VirtualProtect((void *)0x004d4739, sizeof(float), dwProtect, &dwProtect);

    // Float spirit
    VirtualProtect((void *)0x004d473D, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d473D, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d473D, &skill::spirit, sizeof(float));
    }
    VirtualProtect((void *)0x004d473D, sizeof(float), dwProtect, &dwProtect);

    // Float multiple
    VirtualProtect((void *)0x004d4741, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4741, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4741, &skill::multiple, sizeof(float));
    }
    VirtualProtect((void *)0x004d4741, sizeof(float), dwProtect, &dwProtect);

    // Float fatal
    VirtualProtect((void *)0x004d4746, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4746, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4746, &skill::fatal, sizeof(float));
    }
    VirtualProtect((void *)0x004d4746, sizeof(float), dwProtect, &dwProtect);

    // Float skyrocket
    VirtualProtect((void *)0x004d474a, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d474a, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d474a, &skill::skyrocket, sizeof(float));
    }
    VirtualProtect((void *)0x004d474a, sizeof(float), dwProtect, &dwProtect);

    // Float firearrow
    VirtualProtect((void *)0x004d474e, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d474e, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d474e, &skill::firearrow, sizeof(float));
    }
    VirtualProtect((void *)0x004d474e, sizeof(float), dwProtect, &dwProtect);

    // Float gunbomb
    VirtualProtect((void *)0x004d4752, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4752, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4752, &skill::gunbomb, sizeof(float));
    }
    VirtualProtect((void *)0x004d4752, sizeof(float), dwProtect, &dwProtect);

    // Float energy
    VirtualProtect((void *)0x004d49EE, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d49EE, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d49EE, &skill::energy, sizeof(float));
    }
    VirtualProtect((void *)0x004d49EE, sizeof(float), dwProtect, &dwProtect);

    // Float vampire
    VirtualProtect((void *)0x004d49f7, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d49f7, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d49f7, &skill::vampire, sizeof(float));
    }
    VirtualProtect((void *)0x004d49f7, sizeof(float), dwProtect, &dwProtect);

    // Float fireball
    VirtualProtect((void *)0x004d49fb, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d49fb, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d49fb, &skill::fireball, sizeof(float));
    }
    VirtualProtect((void *)0x004d49fb, sizeof(float), dwProtect, &dwProtect);

    // Float electric
    VirtualProtect((void *)0x004d49fF, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d49fF, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d49fF, &skill::electric, sizeof(float));
    }
    VirtualProtect((void *)0x004d49fF, sizeof(float), dwProtect, &dwProtect);

    // Float ice
    VirtualProtect((void *)0x004d4A03, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4A03, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4A03, &skill::ice, sizeof(float));
    }
    VirtualProtect((void *)0x004d4A03, sizeof(float), dwProtect, &dwProtect);

    // Float death
    VirtualProtect((void *)0x004d4A07, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4A07, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4A07, &skill::death, sizeof(float));
    }
    VirtualProtect((void *)0x004d4A07, sizeof(float), dwProtect, &dwProtect);

    // Float seal
    VirtualProtect((void *)0x004d4A0B, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4A0B, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4A0B, &skill::seal, sizeof(float));
    }
    VirtualProtect((void *)0x004d4A0B, sizeof(float), dwProtect, &dwProtect);

    // Float crush
    VirtualProtect((void *)0x004d4A0F, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4A0F, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4A0F, &skill::crush, sizeof(float));
    }
    VirtualProtect((void *)0x004d4A0F, sizeof(float), dwProtect, &dwProtect);

    // Float dark
    VirtualProtect((void *)0x004d4A13, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4A13, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4A13, &skill::dark, sizeof(float));
    }
    VirtualProtect((void *)0x004d4A13, sizeof(float), dwProtect, &dwProtect);

    // Float Magicarrow
    VirtualProtect((void *)0x004d4a18, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a18, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a18, &skill::magicarw, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a18, sizeof(float), dwProtect, &dwProtect);

    // Float Poison
    VirtualProtect((void *)0x004d4a1C, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a1C, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a1C, &skill::poison, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a1C, sizeof(float), dwProtect, &dwProtect);

    // Float Ligthning
    VirtualProtect((void *)0x004d4a20, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a20, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a20, &skill::lightning, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a20, sizeof(float), dwProtect, &dwProtect);

    // Float Gaiaroot
    VirtualProtect((void *)0x004d4a24, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a24, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a24, &skill::gaia, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a24, sizeof(float), dwProtect, &dwProtect);

    // Float instantdamage
    VirtualProtect((void *)0x004d4a28, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a28, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a28, &skill::instant, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a28, sizeof(float), dwProtect, &dwProtect);

    // Float BlazingStrike
    VirtualProtect((void *)0x004d4a2C, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a2C, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a2C, &skill::blazing, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a2C, sizeof(float), dwProtect, &dwProtect);

    // Float heal
    VirtualProtect((void *)0x004d4a31, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a31, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a31, &skill::heal, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a31, sizeof(float), dwProtect, &dwProtect);

    // Float lotos
    VirtualProtect((void *)0x004d4a35, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a35, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a35, &skill::lotos, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a35, sizeof(float), dwProtect, &dwProtect);

    // Float Harm
    VirtualProtect((void *)0x004d4a39, sizeof(float), PAGE_READWRITE, &dwProtect);
    if (IsBadWritePtr((void *)0x004d4a39, sizeof(float)) == FALSE)
    {
        memcpy((void *)0x004d4a39, &skill::harm, sizeof(float));
    }
    VirtualProtect((void *)0x004d4a39, sizeof(float), dwProtect, &dwProtect);
}

// Function to manually trigger the configuration update and apply changes
void UpdateValues()
{
    UpdateConfiguration();
    ApplyConfiguration();
}

// Timer function to periodically update configuration
void ConfigUpdateTimer()
{
    while (true)
    {
        std::this_thread::sleep_for(UPDATE_INTERVAL);
        UpdateValues();
    }
}

// Using the GenerateLoaderDat function from License.h directly

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID)
{
    Sleep(1000);
    // Initialize licensing system
    InitializeLicensing();

    // Section Of Licensing--------------------
    //
    // Use shared licensing functions from License.h
    if (RYL1Plugin::CheckClockTampering())
        return 0;
    if (RYL1Plugin::IsPluginExpired())
        return 0;

    RYL1Plugin::LoadAuthorizedIPs();
    if (!RYL1Plugin::IsIPAuthorized())
    {
        RYL1Plugin::ShowRegistrationMessage(); // This will crash the application
        return 0;                              // This line will never be reached due to crash in ShowRegistrationMessage
    }
    // End Section Of Licensing-----------------

    // Initial setup
    UpdateValues();

    // Detour functions Fame using our improved Detours library
    DWORD oldProtect;
    // Make the memory writable before detour
    if (VirtualProtect((LPVOID)0x00421B63, 5, PAGE_EXECUTE_READWRITE, &oldProtect) == 0)
    {
        DWORD lastError = GetLastError();
        char errorMsg[256];
        sprintf_s(errorMsg, "Failed to change memory protection. Error code: %lu\n\n", lastError);
        MessageBoxA(NULL, errorMsg, "Error", MB_ICONERROR | MB_OK | MB_TOPMOST);
        return 0;
    }

    // Apply the detour
    if (Detours::DetourFunction((PBYTE)0x00421B63, (PBYTE)skill::PatchFunction, 5) != DETOUR_SUCCESS)
    {
        DWORD lastError = GetLastError();
        char errorMsg[256];
        sprintf_s(errorMsg, "Failed to initialize plugin. Error code: %lu\n\n", lastError);
        MessageBoxA(NULL, errorMsg, "Error", MB_ICONERROR | MB_OK | MB_TOPMOST);
        VirtualProtect((LPVOID)0x00421B63, 5, oldProtect, &oldProtect); // Restore protection
        return 0;
    }

    // Restore the original protection
    VirtualProtect((LPVOID)0x00421B63, 5, oldProtect, &oldProtect);

    // Start configuration update timer
    std::thread configUpdateThread(ConfigUpdateTimer);
    configUpdateThread.detach(); // Detach thread so it runs independently

    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
    UNREFERENCED_PARAMETER(lpReserved);

    switch (reason)
    {
    case DLL_PROCESS_ATTACH:
    {
        DisableThreadLibraryCalls(hModule);
        CreateThread(0, 0, MainThread, 0, 0, 0);
        break;
    }
    case DLL_PROCESS_DETACH:
    {
        // Clean up licensing resources
        CleanupLicensing();
        break;
    }
    default:
        break;
    }
    return TRUE;
}
