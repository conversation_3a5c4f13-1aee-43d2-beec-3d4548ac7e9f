
//----- (0042AB60) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CChargingSpell>::operator()(
        Skill::CAddSpell<CChargingSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x8000u);
      v7->__vftable = (CSpell_vtbl *)&CChargingSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC90: using guessed type void *CChargingSpell::`vftable';

//----- (0042AC90) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CBlazeSpell>::operator()(
        Skill::CAddSpell<CBlazeSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x4000u);
      v7->__vftable = (CSpell_vtbl *)&CBlazeSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC80: using guessed type void *CBlazeSpell::`vftable';

//----- (0042ADC0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CStealthSpell>::operator()(
        Skill::CAddSpell<CStealthSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x10000u);
      v7->__vftable = (CSpell_vtbl *)&CStealthSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCA0: using guessed type void *CStealthSpell::`vftable';

//----- (0042AEF0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CFrozenSpell>::operator()(
        Skill::CAddSpell<CFrozenSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x4000000u);
      v7->__vftable = (CSpell_vtbl *)&CFrozenSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD60: using guessed type void *CFrozenSpell::`vftable';

//----- (0042B020) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CLowerStrengthSpell>::operator()(
        Skill::CAddSpell<CLowerStrengthSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x10000000u);
      v7->__vftable = (CSpell_vtbl *)&CLowerStrengthSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD80: using guessed type void *CLowerStrengthSpell::`vftable';

//----- (0042B150) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CEnchantWeaponSpell>::operator()(
        Skill::CAddSpell<CEnchantWeaponSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x80000u);
      v7->__vftable = (CSpell_vtbl *)&CEnchantWeaponSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCD0: using guessed type void *CEnchantWeaponSpell::`vftable';

//----- (0042B280) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CBrightArmorSpell>::operator()(
        Skill::CAddSpell<CBrightArmorSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x100000u);
      v7->__vftable = (CSpell_vtbl *)&CBrightArmorSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCE0: using guessed type void *CBrightArmorSpell::`vftable';

//----- (0042B3B0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CHardenSkinSpell>::operator()(
        Skill::CAddSpell<CHardenSkinSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x200000u);
      v7->__vftable = (CSpell_vtbl *)&CHardenSkinSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBCF0: using guessed type void *CHardenSkinSpell::`vftable';

//----- (0042B4E0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CFlexibilitySpell>::operator()(
        Skill::CAddSpell<CFlexibilitySpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x400000u);
      v7->__vftable = (CSpell_vtbl *)&CFlexibilitySpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD00: using guessed type void *CFlexibilitySpell::`vftable';

//----- (0042B610) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CRegenerationSpell>::operator()(
        Skill::CAddSpell<CRegenerationSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x400u);
      v7->__vftable = (CSpell_vtbl *)&CRegenerationSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC60: using guessed type void *CRegenerationSpell::`vftable';

//----- (0042B740) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CGuardSpell>::operator()(
        Skill::CAddSpell<CGuardSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CGuardSpell *v6; // eax
  CSpell *v7; // eax
  CSpell *v8; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+8h] [ebp-10h] BYREF
  int v11; // [esp+14h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CGuardSpell *)operator new((tagHeader *)0x54);
    v11 = 0;
    if ( v6 )
    {
      CGuardSpell::CGuardSpell(v6, &this->m_Spell_Info);
      v8 = v7;
    }
    else
    {
      v8 = 0;
    }
    v11 = -1;
    if ( v8 )
    {
      if ( CSpell::AddAffected(v8, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v8);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v8->~CSpell)(v8, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 42B7E7: variable 'v7' is possibly undefined

//----- (0042B850) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CPoisonedSpell>::operator()(
        Skill::CAddSpell<CPoisonedSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x8000000u);
      v7->__vftable = (CSpell_vtbl *)&CPoisonedSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBD70: using guessed type void *CPoisonedSpell::`vftable';

//----- (0042B980) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CStrengthSpell>::operator()(
        Skill::CAddSpell<CStrengthSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x800u);
      v7->__vftable = (CSpell_vtbl *)&CStrengthSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC70: using guessed type void *CStrengthSpell::`vftable';

//----- (0042BAB0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CDefencePotionSpell>::operator()(
        Skill::CAddSpell<CDefencePotionSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x40u);
      v7->__vftable = (CSpell_vtbl *)&CDefencePotionSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC20: using guessed type void *CDefencePotionSpell::`vftable';

//----- (0042BBD0) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CDisenchantPotionSpell>::operator()(
        Skill::CAddSpell<CDisenchantPotionSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x80u);
      v7->__vftable = (CSpell_vtbl *)&CDisenchantPotionSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC30: using guessed type void *CDisenchantPotionSpell::`vftable';

//----- (0042BD00) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CMagicPotionSpell>::operator()(
        Skill::CAddSpell<CMagicPotionSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x100u);
      v7->__vftable = (CSpell_vtbl *)&CMagicPotionSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC40: using guessed type void *CMagicPotionSpell::`vftable';

//----- (0042BE30) --------------------------------------------------------
bool __thiscall Skill::CAddSpell<CLightningPotionSpell>::operator()(
        Skill::CAddSpell<CLightningPotionSpell> *this,
        CAggresiveCreature *lpCharacter)
{
  signed int m_dwCID; // eax
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CSpell *v6; // eax
  CSpell *v7; // esi
  CGlobalSpellMgr *Instance; // eax
  int wError; // [esp+Ch] [ebp-10h] BYREF
  int v10; // [esp+18h] [ebp-4h]

  wError = 0;
  if ( lpCharacter )
  {
    if ( (lpCharacter->m_dwStatusFlag & 0x80u) != 0 )
      return 1;
    m_dwCID = lpCharacter->m_dwCID;
    if ( m_dwCID >= 0 )
    {
      if ( (m_dwCID & 0xC0000000) == 0 && (m_dwCID & 0x10000000) != 0 )
        return 1;
    }
    else
    {
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(
                           CSingleton<CMonsterMgr>::ms_pSingleton,
                           (unsigned __int16)lpCharacter->m_dwCID);
      if ( MonsterProtoType && MonsterProtoType->m_MonsterInfo.m_bIgnoreEnchant )
        return 1;
    }
    v6 = (CSpell *)operator new((tagHeader *)0x50);
    v7 = v6;
    v10 = 0;
    if ( v6 )
    {
      CSpell::CSpell(v6, &this->m_Spell_Info, JOIN_WAIT, 0x200u);
      v7->__vftable = (CSpell_vtbl *)&CLightningPotionSpell::`vftable';
    }
    else
    {
      v7 = 0;
    }
    v10 = -1;
    if ( v7 )
    {
      if ( CSpell::AddAffected(v7, lpCharacter, (unsigned __int16 *)&wError) == 1 )
      {
        Instance = CGlobalSpellMgr::GetInstance();
        CGlobalSpellMgr::Add(Instance, v7);
        return 1;
      }
      ((void (__thiscall *)(CSpell *, int))v7->~CSpell)(v7, 1);
    }
  }
  return (_WORD)wError == 1;
}
// 4DBC50: using guessed type void *CLightningPotionSpell::`vftable';

//----- (0042BF60) --------------------------------------------------------
CConsoleCMDFactory::StringCMD *__cdecl std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        CConsoleCMDFactory::StringCMD *_First,
        CConsoleCMDFactory::StringCMD *_Last,
        CConsoleCMDFactory::StringCMD *_Dest)
{
  CConsoleCMDFactory::StringCMD *v3; // ecx
  CConsoleCMDFactory::StringCMD *result; // eax

  v3 = _First;
  for ( result = _Dest; v3 != _Last; ++result )
  {
    if ( result )
    {
      result->m_dwHashValue = v3->m_dwHashValue;
      result->m_szCommand = v3->m_szCommand;
      result->m_lpCMD = v3->m_lpCMD;
    }
    ++v3;
  }
  return result;
}

//----- (0042BFA0) --------------------------------------------------------
void __cdecl std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Mid,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  int v3; // edx
  unsigned __int16 (__cdecl *m_fnProcess)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // esi
  int v5; // esi
  unsigned __int16 (__cdecl *v6)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // edi
  int v7; // edx
  unsigned __int16 (__cdecl *v8)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // esi
  Skill::ProtoType *v9; // edi
  Skill::ProtoType *m_lpProtoType; // [esp+18h] [ebp-4h]
  Skill::ProtoType *v11; // [esp+18h] [ebp-4h]

  if ( _Mid._Myptr->m_usSkill_ID < _First._Myptr->m_usSkill_ID )
  {
    v3 = *(_DWORD *)&_Mid._Myptr->m_usSkill_ID;
    m_fnProcess = _Mid._Myptr->m_fnProcess;
    m_lpProtoType = _Mid._Myptr->m_lpProtoType;
    *(_DWORD *)&_Mid._Myptr->m_usSkill_ID = *(_DWORD *)&_First._Myptr->m_usSkill_ID;
    _Mid._Myptr->m_fnProcess = _First._Myptr->m_fnProcess;
    _Mid._Myptr->m_lpProtoType = _First._Myptr->m_lpProtoType;
    *(_DWORD *)&_First._Myptr->m_usSkill_ID = v3;
    _First._Myptr->m_fnProcess = m_fnProcess;
    _First._Myptr->m_lpProtoType = m_lpProtoType;
  }
  if ( _Last._Myptr->m_usSkill_ID < _Mid._Myptr->m_usSkill_ID )
  {
    v5 = *(_DWORD *)&_Last._Myptr->m_usSkill_ID;
    v6 = _Last._Myptr->m_fnProcess;
    v11 = _Last._Myptr->m_lpProtoType;
    *(_DWORD *)&_Last._Myptr->m_usSkill_ID = *(_DWORD *)&_Mid._Myptr->m_usSkill_ID;
    _Last._Myptr->m_fnProcess = _Mid._Myptr->m_fnProcess;
    _Last._Myptr->m_lpProtoType = _Mid._Myptr->m_lpProtoType;
    *(_DWORD *)&_Mid._Myptr->m_usSkill_ID = v5;
    _Mid._Myptr->m_fnProcess = v6;
    _Mid._Myptr->m_lpProtoType = v11;
  }
  if ( _Mid._Myptr->m_usSkill_ID < _First._Myptr->m_usSkill_ID )
  {
    v7 = *(_DWORD *)&_Mid._Myptr->m_usSkill_ID;
    v8 = _Mid._Myptr->m_fnProcess;
    v9 = _Mid._Myptr->m_lpProtoType;
    *(_DWORD *)&_Mid._Myptr->m_usSkill_ID = *(_DWORD *)&_First._Myptr->m_usSkill_ID;
    _Mid._Myptr->m_fnProcess = _First._Myptr->m_fnProcess;
    _Mid._Myptr->m_lpProtoType = _First._Myptr->m_lpProtoType;
    *(_DWORD *)&_First._Myptr->m_usSkill_ID = v7;
    _First._Myptr->m_fnProcess = v8;
    _First._Myptr->m_lpProtoType = v9;
  }
}

//----- (0042C060) --------------------------------------------------------
void __cdecl std::_Push_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        int _Hole,
        int _Top,
        Skill::CProcessTable::ProcessInfo _Val)
{
  int v4; // ecx
  int i; // eax
  Skill::CProcessTable::ProcessInfo *v6; // edx
  Skill::CProcessTable::ProcessInfo *v7; // ecx

  v4 = _Hole;
  for ( i = (_Hole - 1) / 2; _Top < v4; i = (i - 1) / 2 )
  {
    v6 = &_First._Myptr[i];
    if ( v6->m_usSkill_ID >= _Val.m_usSkill_ID )
      break;
    v7 = &_First._Myptr[v4];
    *(_DWORD *)&v7->m_usSkill_ID = *(_DWORD *)&v6->m_usSkill_ID;
    v7->m_fnProcess = v6->m_fnProcess;
    v7->m_lpProtoType = v6->m_lpProtoType;
    v4 = i;
  }
  _First._Myptr[v4] = _Val;
}

//----- (0042C0D0) --------------------------------------------------------
bool __cdecl Skill::CFunctions::SlowlySkillAttack(
        const Skill::ProtoType *ProtoType,
        unsigned int attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim)
{
  unsigned int v4; // eax
  unsigned __int8 v5; // bl
  double v6; // st7
  float fEnchantTick; // [esp+10h] [ebp-24h]
  int v9; // [esp+1Ch] [ebp-18h]
  int v10; // [esp+20h] [ebp-14h]
  Skill::CAddSpell<CSlowSpell> v11; // [esp+24h] [ebp-10h] BYREF

  v4 = HIWORD(attackType);
  v5 = 0;
  fEnchantTick = 0.0;
  switch ( BYTE2(attackType) >> 4 )
  {
    case 0:
      v5 = BYTE1(v4);
      v6 = (double)SBYTE1(v4) * 0.5;
      goto LABEL_8;
    case 1:
      v6 = (double)SBYTE1(v4) * 0.40000001 + 3.0;
      goto LABEL_7;
    case 2:
      v6 = (double)SBYTE1(v4) * 0.30000001 + 5.4000001;
      goto LABEL_7;
    case 3:
      v5 = BYTE1(v4);
      v6 = (double)SBYTE1(v4) * 0.2 + 7.1999998;
      goto LABEL_8;
    case 4:
      v6 = (double)SBYTE1(v4) * 0.1 + 8.3999996;
LABEL_7:
      v5 = BYTE1(v4);
LABEL_8:
      fEnchantTick = v6;
      break;
    default:
      break;
  }
  LOWORD(v10) = v5;
  BYTE2(v10) = 1;
  LOWORD(v9) = (unsigned __int64)(CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * fEnchantTick);
  HIWORD(v9) = 13;
  *(_DWORD *)&v11.m_Spell_Info.m_wSpellLevel = v10;
  v11.m_Spell_Info.m_SkillProtoType = ProtoType;
  v11.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v11.m_Spell_Info.m_wDurationSec = v9;
  return Skill::CAddSpell<CSlowSpell>::operator()(&v11, lpVictim);
}
// 42C1BC: variable 'v10' is possibly undefined

//----- (0042C200) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Net(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  signed int m_dwCID; // eax
  double v9; // st7
  const CSpell::Spell_Info *v10; // eax
  Skill::CAddSpell<CHoldSpell> *v11; // eax
  CSpell::Spell_Info v12; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v13; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Net",
      aDWorkRylSource_68,
      311,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  m_dwCID = lpVictim->m_dwCID;
  if ( m_dwCID >= 0 )
  {
    if ( Creature::GetCreatureType(m_dwCID) == 5 )
    {
      *cDefenserJudge = 11;
      return 0;
    }
    else
    {
      v9 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * 8.0;
      CSpell::Spell_Info::Spell_Info(&v12, ProtoType, lpSkillUser, 2u, 0x19u, 1u, (unsigned __int64)v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v13, v10);
      if ( Skill::CAddSpell<CHoldSpell>::operator()(v11, lpVictim) )
      {
        CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
        if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
          lpVictim->__vftable[1].GetGID(lpVictim);
        *cDefenserJudge = 8;
        return 0;
      }
      else
      {
        *wError = 6;
        *cDefenserJudge = 8;
        return 0;
      }
    }
  }
  else
  {
    *cDefenserJudge = 11;
    return 0;
  }
}
// 42C294: variable 'v10' is possibly undefined
// 42C29B: variable 'v11' is possibly undefined

//----- (0042C320) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::HardHit(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  CAggresiveCreature *v7; // ebp
  char m_cSkillLevel; // bh
  double v11; // st7
  int v12; // eax
  unsigned __int16 v13; // si
  double v14; // st7
  double v15; // st7
  const CSpell::Spell_Info *v16; // eax
  Skill::CAddSpell<CStunSpell> *v17; // eax
  int v18; // eax
  unsigned __int16 v19; // si
  __int16 v20; // bp
  double v21; // st7
  const CSpell::Spell_Info *v22; // eax
  Skill::CAddSpell<CArmorBrokenSpell> *v23; // eax
  unsigned __int16 v24; // si
  float fEnchantTick; // [esp+8h] [ebp-5Ch]
  int v26; // [esp+Ch] [ebp-58h]
  float fDRC; // [esp+10h] [ebp-54h]
  CalculateDamageInfo AddEffectInfo; // [esp+14h] [ebp-50h] BYREF
  CSpell::Spell_Info v29; // [esp+24h] [ebp-40h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v30; // [esp+34h] [ebp-30h] BYREF
  CSpell::Spell_Info v31; // [esp+44h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v32; // [esp+54h] [ebp-10h] BYREF
  float lpSkillUsera; // [esp+70h] [ebp+Ch]

  v7 = lpVictim;
  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::HardHit",
      aDWorkRylSource_68,
      375,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  switch ( lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nCriticalType )
  {
    case 1:
      m_cSkillLevel = attackType.m_cSkillLevel;
      v26 = *((unsigned __int8 *)&attackType + 2) >> 4;
      fDRC = (double)attackType.m_cSkillLevel * 0.050000001 + fSwordDRCs[v26];
      break;
    case 2:
      m_cSkillLevel = attackType.m_cSkillLevel;
      v18 = *((unsigned __int8 *)&attackType + 2) >> 4;
      v26 = v18;
      fDRC = (double)attackType.m_cSkillLevel * 0.050000001 + fDRCs_4[v18];
      if ( (*((_BYTE *)&attackType + 2) & 0xF0) != 0 )
      {
        v19 = 0;
        v20 = 0;
        switch ( v18 )
        {
          case 1:
            v19 = attackType.m_cSkillLevel;
            goto LABEL_23;
          case 2:
            v19 = attackType.m_cSkillLevel + 6;
            goto LABEL_23;
          case 3:
            v19 = attackType.m_cSkillLevel + 12;
            goto LABEL_23;
          case 4:
            v19 = attackType.m_cSkillLevel + 18;
LABEL_23:
            v20 = 20;
            break;
          default:
            break;
        }
        v21 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * (double)v20;
        CSpell::Spell_Info::Spell_Info(&v31, ProtoType, lpSkillUser, 1u, 0xEu, v19, (unsigned __int64)v21);
        Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v32, v22);
        v7 = lpVictim;
        if ( !Skill::CAddSpell<CArmorBrokenSpell>::operator()(v23, lpVictim) )
          *wError = 6;
      }
      break;
    case 3:
      m_cSkillLevel = attackType.m_cSkillLevel;
      v11 = (double)attackType.m_cSkillLevel;
      v12 = *((unsigned __int8 *)&attackType + 2) >> 4;
      lpSkillUsera = v11;
      v26 = v12;
      fDRC = v11 * 0.050000001 + fDRCs_4[v12];
      if ( (*((_BYTE *)&attackType + 2) & 0xF0) != 0 )
      {
        v13 = 0;
        fEnchantTick = 0.0;
        switch ( v12 )
        {
          case 1:
            v13 = attackType.m_cSkillLevel;
            v14 = lpSkillUsera * 0.40000001;
            goto LABEL_14;
          case 2:
            v14 = lpSkillUsera * 0.30000001 + 2.4000001;
            v13 = attackType.m_cSkillLevel + 6;
            goto LABEL_14;
          case 3:
            v14 = lpSkillUsera * 0.2 + 4.1999998;
            v13 = attackType.m_cSkillLevel + 12;
            goto LABEL_14;
          case 4:
            v14 = lpSkillUsera * 0.1 + 5.4000001;
            v13 = attackType.m_cSkillLevel + 18;
LABEL_14:
            fEnchantTick = v14;
            break;
          default:
            break;
        }
        v15 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * fEnchantTick;
        CSpell::Spell_Info::Spell_Info(&v29, ProtoType, lpSkillUser, 1u, 0x1Au, v13, (unsigned __int64)v15);
        Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v30, v16);
        if ( !Skill::CAddSpell<CStunSpell>::operator()(v17, lpVictim) )
          *wError = 6;
      }
      break;
    default:
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::HardHit",
        aDWorkRylSource_68,
        448,
        aCid0x08x_267,
        lpSkillUser->m_dwCID,
        lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nCriticalType);
      return 0;
  }
  if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, v7) )
    *wError = 6;
  AddEffectInfo.m_fDRC = fDRC;
  AddEffectInfo.m_nMinDamage = m_cSkillLevel + nSkillLevels[v26];
  AddEffectInfo.m_nMaxDamage = AddEffectInfo.m_nMinDamage;
  AddEffectInfo.m_bForceDRC = 0;
  AddEffectInfo.m_nOffenceRevision = 2 * AddEffectInfo.m_nMinDamage;
  v24 = CAggresiveCreature::CalculateDamage(
          v7,
          (int)lpSkillUser,
          COERCE_FLOAT(&AddEffectInfo),
          *(float *)&cDefenserJudge);
  CThreat::AffectThreat(&v7->m_Threat, lpSkillUser, v24, TAUNT);
  return v24;
}
// 42C478: variable 'v16' is possibly undefined
// 42C47F: variable 'v17' is possibly undefined
// 42C53D: variable 'v22' is possibly undefined
// 42C544: variable 'v23' is possibly undefined
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (0042C670) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::ManaShell(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  unsigned __int16 v8; // cx
  unsigned __int16 v9; // si
  int v10; // eax
  __int16 v11; // si
  const CSpell::Spell_Info *v12; // eax
  Skill::CAddSpell<CManaShellSpell> *v13; // eax
  CSpell::Spell_Info v15; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v16; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      v8 = 0;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v10 = v7;
          v8 = attackType.m_cSkillLevel + nTickPlusCountBonus2nd[v10];
          v11 = 20 * attackType.m_cSkillLevel;
          goto LABEL_14;
        case 1:
          v10 = v7;
          v8 = attackType.m_cSkillLevel + nTickPlusCountBonus2nd[v10] + 6;
          v11 = 40 * attackType.m_cSkillLevel;
          goto LABEL_14;
        case 2:
          v10 = v7;
          v8 = attackType.m_cSkillLevel + nTickPlusCountBonus2nd[v10] + 12;
          v11 = 60 * attackType.m_cSkillLevel;
          goto LABEL_14;
        case 3:
          v10 = v7;
          v8 = attackType.m_cSkillLevel + nTickPlusCountBonus2nd[v10] + 18;
          v11 = 80 * attackType.m_cSkillLevel;
          goto LABEL_14;
        case 4:
          v10 = v7;
          v8 = attackType.m_cSkillLevel + nTickPlusCountBonus2nd[v10] + 24;
          v11 = 100 * attackType.m_cSkillLevel;
LABEL_14:
          v9 = nLockCountTickBonus[v10] + v11;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(
        &v15,
        ProtoType,
        lpSkillUser,
        1u,
        0x12u,
        (unsigned __int64)((double)(v8 * (lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower / 2 + 100))
                         * 0.0099999998),
        v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v16, v12);
      if ( !Skill::CAddSpell<CManaShellSpell>::operator()(v13, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ManaShell",
      aDWorkRylSource_68,
      690,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ManaShell",
      aDWorkRylSource_68,
      670,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42C7D0: variable 'v12' is possibly undefined
// 42C7D7: variable 'v13' is possibly undefined

//----- (0042C880) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Grease(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  int v8; // edi
  unsigned __int16 m_cSkillLevel; // si
  unsigned __int64 v11; // rax
  double v12; // st7
  double v13; // st6
  const CSpell::Spell_Info *v14; // eax
  Skill::CAddSpell<CHoldSpell> *v15; // eax
  CSpell::Spell_Info v16; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v17; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 > 4u )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::Grease",
        aDWorkRylSource_68,
        754,
        aCid0x08x_89,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        v7);
      return 0;
    }
    else
    {
      v8 = v7;
      if ( Math::Random::ComplexRandom(
             lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
           + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
           + nResistanceBonus_0[v8],
             0) <= (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
      {
        *cDefenserJudge = 11;
        return 0;
      }
      m_cSkillLevel = 0;
      LOWORD(v11) = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          m_cSkillLevel = attackType.m_cSkillLevel;
          v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          v13 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 1));
          goto LABEL_17;
        case 1:
          m_cSkillLevel = attackType.m_cSkillLevel + 6;
          v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          v13 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 2));
          goto LABEL_17;
        case 2:
          m_cSkillLevel = attackType.m_cSkillLevel + 12;
          v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          v13 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 3));
          goto LABEL_17;
        case 3:
          m_cSkillLevel = attackType.m_cSkillLevel + 18;
          v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          v13 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 4));
          goto LABEL_17;
        case 4:
          m_cSkillLevel = attackType.m_cSkillLevel + 24;
          v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          v13 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 5));
LABEL_17:
          v11 = (unsigned __int64)(v13 * v12 * 0.5);
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v16, ProtoType, lpSkillUser, 1u, 0x19u, m_cSkillLevel, v11);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v17, v14);
      if ( Skill::CAddSpell<CHoldSpell>::operator()(v15, lpVictim) )
      {
        CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
        if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
          lpVictim->__vftable[1].GetGID(lpVictim);
        *cDefenserJudge = 8;
        return 0;
      }
      else
      {
        *wError = 6;
        *cDefenserJudge = 8;
        return 0;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Grease",
      aDWorkRylSource_68,
      733,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42CA81: variable 'v14' is possibly undefined
// 42CA88: variable 'v15' is possibly undefined

//----- (0042CB60) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Encourage(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // cx
  unsigned __int64 v8; // rax
  unsigned __int16 v9; // di
  __int16 m_nMagicPower; // di
  double v11; // st7
  double v12; // st6
  const CSpell::Spell_Info *v13; // eax
  Skill::CAddSpell<CEncourageSpell> *v14; // eax
  CSpell::Spell_Info v16; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v17; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      LOWORD(v8) = 0;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(attackType.m_cSkillLevel + nLockCountBonus[v7]);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 1:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 6);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 2:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 12);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 3:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 18);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 4:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 24);
          v12 = (double)m_nMagicPower;
LABEL_14:
          v8 = (unsigned __int64)(v11 * (v12 * 0.5 + 100.0) * 0.0099999998);
          v9 = m_nMagicPower + 100;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v16, ProtoType, lpSkillUser, 1u, 0x13u, v8, v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v17, v13);
      if ( !Skill::CAddSpell<CEncourageSpell>::operator()(v14, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Encourage",
      aDWorkRylSource_68,
      857,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Encourage",
      aDWorkRylSource_68,
      838,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42CD04: variable 'v13' is possibly undefined
// 42CD0B: variable 'v14' is possibly undefined
// 4DC99C: using guessed type __int16 nLockCountBonus[6];

//----- (0042CDB0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::HammerOfLight(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v8; // bp
  __int64 v9; // rax
  __int16 v10; // ax
  __int16 v11; // si
  int v12; // ebp
  double v13; // st7
  double v14; // st6
  int v15; // ebp
  int v16; // ebp
  int v17; // ebp
  int v18; // ebp
  double v19; // st7
  const CSpell::Spell_Info *v20; // eax
  Skill::CAddSpell<CStunSpell> *v21; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  float v24; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v25; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v26; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  v8 = 0;
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v9) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v10 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v10 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v10;
      if ( (unsigned __int16)v10 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::HammerOfLight",
          aDWorkRylSource_68,
          961,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v10);
        LOWORD(v9) = 0;
      }
      else
      {
        v11 = 0;
        nEnchantLevel = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v12 = v10;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = nPlusLockCountBonus_2[v12] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v12] + 40);
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v12] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v12] + 1));
            goto LABEL_15;
          case 1:
            v15 = v10;
            v11 = nPlusLockCountBonus_2[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v15] + 80);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v15] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 2));
            goto LABEL_15;
          case 2:
            v16 = v10;
            v11 = nPlusLockCountBonus_2[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v16] + 120);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v16] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 3));
            goto LABEL_15;
          case 3:
            v17 = v10;
            v11 = nPlusLockCountBonus_2[v17] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v17] + 160);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v17] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v17] + 4));
            goto LABEL_15;
          case 4:
            v18 = v10;
            v11 = nPlusLockCountBonus_2[v18] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v18] + 200);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v18] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v18] + 5));
LABEL_15:
            v8 = (unsigned __int64)(v14 * v13 * 0.33333334);
            v10 = lpSkillUsera;
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[v10],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v25, ProtoType, lpSkillUser, 1u, 0x1Au, nEnchantLevel, v8);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v26, v20);
          if ( !Skill::CAddSpell<CStunSpell>::operator()(v21, lpVictim) )
            *wError = 6;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v19 * v24 * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::HammerOfLight",
      aDWorkRylSource_68,
      939,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v9) = 0;
  }
  return v9;
}
// 42D0CD: variable 'v20' is possibly undefined
// 42D0D4: variable 'v21' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042D1C0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Charging(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CChargingSpell> v9; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Charging",
      aDWorkRylSource_68,
      1039,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, NONE, COMMON, NONE, 0, 0xFFu);
  LOWORD(v8) = 1;
  BYTE2(v8) = 1;
  *(_DWORD *)&v9.m_Spell_Info.m_wDurationSec = 1048606;
  v9.m_Spell_Info.m_SkillProtoType = ProtoType;
  v9.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v9.m_Spell_Info.m_wSpellLevel = v8;
  if ( !Skill::CAddSpell<CChargingSpell>::operator()(&v9, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 42D246: variable 'v8' is possibly undefined

//----- (0042D2A0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FullSwing(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // eax
  double v9; // st7
  unsigned __int16 v10; // di
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      return 0;
    }
    else
    {
      if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, lpVictim) )
        *wError = 6;
      v8 = *((unsigned __int8 *)&attackType + 2) >> 4;
      AddEffectInfo.m_bForceDRC = 0;
      v9 = (double)attackType.m_cSkillLevel * 0.050000001 + fDRCs_4[v8];
      LOWORD(v8) = attackType.m_cSkillLevel + nSkillLevels[v8];
      AddEffectInfo.m_fDRC = v9;
      AddEffectInfo.m_nMinDamage = v8;
      AddEffectInfo.m_nMaxDamage = v8;
      AddEffectInfo.m_nOffenceRevision = 2 * v8;
      v10 = CAggresiveCreature::CalculateDamage(
              lpVictim,
              (int)lpSkillUser,
              COERCE_FLOAT(&AddEffectInfo),
              *(float *)&cDefenserJudge);
      CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v10, TAUNT);
      return v10;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FullSwing",
      aDWorkRylSource_68,
      1109,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (0042D3B0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Blaze(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CBlazeSpell> v9; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Blaze",
      aDWorkRylSource_68,
      1138,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, NONE, COMMON, NONE, 0, 0xFFu);
  LOWORD(v8) = 1;
  BYTE2(v8) = 1;
  *(_DWORD *)&v9.m_Spell_Info.m_wDurationSec = 983070;
  v9.m_Spell_Info.m_SkillProtoType = ProtoType;
  v9.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v9.m_Spell_Info.m_wSpellLevel = v8;
  if ( !Skill::CAddSpell<CBlazeSpell>::operator()(&v9, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 42D436: variable 'v8' is possibly undefined

//----- (0042D490) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::ChainAction(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // eax
  double v9; // st7
  unsigned __int16 v10; // di
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      return 0;
    }
    else
    {
      if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, lpVictim) )
        *wError = 6;
      v8 = *((unsigned __int8 *)&attackType + 2) >> 4;
      AddEffectInfo.m_bForceDRC = 1;
      v9 = (double)attackType.m_cSkillLevel * 0.050000001 + fDRCs_1[v8];
      LOWORD(v8) = attackType.m_cSkillLevel + nSkillLevels[v8];
      AddEffectInfo.m_fDRC = v9;
      AddEffectInfo.m_nMinDamage = v8;
      AddEffectInfo.m_nMaxDamage = v8;
      AddEffectInfo.m_nOffenceRevision = 2 * v8;
      v10 = CAggresiveCreature::CalculateDamage(
              lpVictim,
              (int)lpSkillUser,
              COERCE_FLOAT(&AddEffectInfo),
              *(float *)&cDefenserJudge);
      CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v10, DETAUNT);
      return v10;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::ChainAction",
      aDWorkRylSource_68,
      1196,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (0042D5A0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Stealth(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 m_cSkillLevel; // ax
  __int16 v8; // cx
  int v10; // [esp+10h] [ebp-18h]
  int v11; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CStealthSpell> v12; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Stealth",
      aDWorkRylSource_68,
      1244,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  m_cSkillLevel = 0;
  v8 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      m_cSkillLevel = attackType.m_cSkillLevel;
      goto LABEL_10;
    case 1:
      m_cSkillLevel = attackType.m_cSkillLevel + 6;
      goto LABEL_10;
    case 2:
      m_cSkillLevel = attackType.m_cSkillLevel + 12;
      goto LABEL_10;
    case 3:
      m_cSkillLevel = attackType.m_cSkillLevel + 18;
      goto LABEL_10;
    case 4:
      m_cSkillLevel = attackType.m_cSkillLevel + 24;
LABEL_10:
      v8 = 2 * m_cSkillLevel;
      break;
    default:
      break;
  }
  LOWORD(v10) = v8;
  HIWORD(v10) = 17;
  LOWORD(v11) = m_cSkillLevel;
  v12.m_Spell_Info.m_SkillProtoType = ProtoType;
  BYTE2(v11) = 1;
  *(_DWORD *)&v12.m_Spell_Info.m_wDurationSec = v10;
  v12.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v12.m_Spell_Info.m_wSpellLevel = v11;
  if ( !Skill::CAddSpell<CStealthSpell>::operator()(&v12, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 42D64F: variable 'v11' is possibly undefined

//----- (0042D6C0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Camouflage(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 m_cSkillLevel; // ax
  __int16 v8; // cx
  int v10; // [esp+10h] [ebp-18h]
  int v11; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CStealthSpell> v12; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Camouflage",
      aDWorkRylSource_68,
      1325,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  m_cSkillLevel = 0;
  v8 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      m_cSkillLevel = attackType.m_cSkillLevel;
      goto LABEL_10;
    case 1:
      m_cSkillLevel = attackType.m_cSkillLevel + 6;
      goto LABEL_10;
    case 2:
      m_cSkillLevel = attackType.m_cSkillLevel + 12;
      goto LABEL_10;
    case 3:
      m_cSkillLevel = attackType.m_cSkillLevel + 18;
      goto LABEL_10;
    case 4:
      m_cSkillLevel = attackType.m_cSkillLevel + 24;
LABEL_10:
      v8 = 2 * m_cSkillLevel;
      break;
    default:
      break;
  }
  LOWORD(v10) = v8;
  HIWORD(v10) = 17;
  LOWORD(v11) = m_cSkillLevel;
  v12.m_Spell_Info.m_SkillProtoType = ProtoType;
  BYTE2(v11) = 1;
  *(_DWORD *)&v12.m_Spell_Info.m_wDurationSec = v10;
  v12.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v12.m_Spell_Info.m_wSpellLevel = v11;
  if ( !Skill::CAddSpell<CStealthSpell>::operator()(&v12, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 42D76F: variable 'v11' is possibly undefined

//----- (0042D7E0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::LightningArrow(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v8; // bp
  __int64 v9; // rax
  __int16 v10; // ax
  __int16 v11; // si
  int v12; // ebp
  double v13; // st7
  double v14; // st6
  int v15; // ebp
  int v16; // ebp
  int v17; // ebp
  int v18; // ebp
  double v19; // st7
  const CSpell::Spell_Info *v20; // eax
  Skill::CAddSpell<CStunSpell> *v21; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  float v24; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v25; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v26; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  v8 = 0;
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v9) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v10 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v10 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v10;
      if ( (unsigned __int16)v10 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::LightningArrow",
          aDWorkRylSource_68,
          1457,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v10);
        LOWORD(v9) = 0;
      }
      else
      {
        v11 = 0;
        nEnchantLevel = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v12 = v10;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = nPlusLockCountBonus_3[v12] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v12] + 70);
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v12] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v12] + 1));
            goto LABEL_15;
          case 1:
            v15 = v10;
            v11 = nPlusLockCountBonus_3[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v15] + 140);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v15] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 2));
            goto LABEL_15;
          case 2:
            v16 = v10;
            v11 = nPlusLockCountBonus_3[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v16] + 210);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v16] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 3));
            goto LABEL_15;
          case 3:
            v17 = v10;
            v11 = nPlusLockCountBonus_3[v17] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v17] + 280);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v17] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v17] + 4));
            goto LABEL_15;
          case 4:
            v18 = v10;
            v11 = nPlusLockCountBonus_3[v18] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v18] + 350);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v18] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v18] + 5));
LABEL_15:
            v8 = (unsigned __int64)(v14 * v13 * 0.25);
            v10 = lpSkillUsera;
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[v10],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v25, ProtoType, lpSkillUser, 1u, 0x1Au, nEnchantLevel, v8);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v26, v20);
          if ( !Skill::CAddSpell<CStunSpell>::operator()(v21, lpVictim) )
            *wError = 6;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v19 * v24 * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::LightningArrow",
      aDWorkRylSource_68,
      1435,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v9) = 0;
  }
  return v9;
}
// 42DAFF: variable 'v20' is possibly undefined
// 42DB06: variable 'v21' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042DBF0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FrostBolt(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v8; // bp
  __int64 v9; // rax
  __int16 v10; // ax
  __int16 v11; // si
  int v12; // ebp
  double v13; // st7
  double v14; // st6
  int v15; // ebp
  int v16; // ebp
  int v17; // ebp
  int v18; // ebp
  double v19; // st7
  const CSpell::Spell_Info *v20; // eax
  Skill::CAddSpell<CFrozenSpell> *v21; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  float v24; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v25; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v26; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  v8 = 0;
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v9) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v10 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v10 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v10;
      if ( (unsigned __int16)v10 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::FrostBolt",
          aDWorkRylSource_68,
          1553,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v10);
        LOWORD(v9) = 0;
      }
      else
      {
        v11 = 0;
        nEnchantLevel = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v12 = v10;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = nPlusLockCountBonus_3[v12] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v12] + 70);
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v12] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v12] + 1));
            goto LABEL_15;
          case 1:
            v15 = v10;
            v11 = nPlusLockCountBonus_3[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v15] + 140);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v15] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 2));
            goto LABEL_15;
          case 2:
            v16 = v10;
            v11 = nPlusLockCountBonus_3[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v16] + 210);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v16] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 3));
            goto LABEL_15;
          case 3:
            v17 = v10;
            v11 = nPlusLockCountBonus_3[v17] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v17] + 280);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v17] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v17] + 4));
            goto LABEL_15;
          case 4:
            v18 = v10;
            v11 = nPlusLockCountBonus_3[v18] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v18] + 350);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v18] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v18] + 5));
LABEL_15:
            v8 = (unsigned __int64)(v14 * v13 * 0.25);
            v10 = lpSkillUsera;
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[v10],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v25, ProtoType, lpSkillUser, 1u, 0x1Bu, nEnchantLevel, v8);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v26, v20);
          if ( !Skill::CAddSpell<CFrozenSpell>::operator()(v21, lpVictim) )
            *wError = 6;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v19 * v24 * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::FrostBolt",
      aDWorkRylSource_68,
      1531,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v9) = 0;
  }
  return v9;
}
// 42DF0F: variable 'v20' is possibly undefined
// 42DF16: variable 'v21' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042E000) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Entangle(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v8; // bp
  __int64 v9; // rax
  __int16 v10; // ax
  __int16 v11; // si
  int v12; // ebp
  double v13; // st7
  double v14; // st6
  int v15; // ebp
  int v16; // ebp
  int v17; // ebp
  int v18; // ebp
  double v19; // st7
  const CSpell::Spell_Info *v20; // eax
  Skill::CAddSpell<CHoldSpell> *v21; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  float v24; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v25; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v26; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  v8 = 0;
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v9) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v10 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v10 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v10;
      if ( (unsigned __int16)v10 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Entangle",
          aDWorkRylSource_68,
          1693,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v10);
        LOWORD(v9) = 0;
      }
      else
      {
        v11 = 0;
        nEnchantLevel = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v12 = v10;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = nPlusLockCountBonus_2[v12] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v12] + 40);
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v12] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v12] + 1));
            goto LABEL_15;
          case 1:
            v15 = v10;
            v11 = nPlusLockCountBonus_2[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v15] + 80);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v15] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 2));
            goto LABEL_15;
          case 2:
            v16 = v10;
            v11 = nPlusLockCountBonus_2[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v16] + 120);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v16] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 3));
            goto LABEL_15;
          case 3:
            v17 = v10;
            v11 = nPlusLockCountBonus_2[v17] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v17] + 160);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v17] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v17] + 4));
            goto LABEL_15;
          case 4:
            v18 = v10;
            v11 = nPlusLockCountBonus_2[v18] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_2[v18] + 200);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v18] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v18] + 5));
LABEL_15:
            v8 = (unsigned __int64)(v14 * v13 * 0.5);
            v10 = lpSkillUsera;
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[v10],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v25, ProtoType, lpSkillUser, 1u, 0x19u, nEnchantLevel, v8);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v26, v20);
          if ( !Skill::CAddSpell<CHoldSpell>::operator()(v21, lpVictim) )
            *wError = 6;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v19 * v24 * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Entangle",
      aDWorkRylSource_68,
      1671,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v9) = 0;
  }
  return v9;
}
// 42E31D: variable 'v20' is possibly undefined
// 42E324: variable 'v21' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042E410) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::LowerStrength(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  int v8; // edi
  int v10; // ebx
  unsigned __int64 v11; // rax
  double v12; // st7
  double m_nMagicPower; // st6
  const CSpell::Spell_Info *v14; // eax
  Skill::CAddSpell<CLowerStrengthSpell> *v15; // eax
  CSpell::Spell_Info v16; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v17; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 > 4u )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::LowerStrength",
        aDWorkRylSource_68,
        1786,
        aCid0x08x_89,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        v7);
      return 0;
    }
    else
    {
      v8 = v7;
      if ( Math::Random::ComplexRandom(
             lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
           + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
           + nResistanceBonus[v8],
             0) <= (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
      {
        *cDefenserJudge = 11;
        return 0;
      }
      LOWORD(v10) = 0;
      LOWORD(v11) = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v12 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 1));
          m_nMagicPower = (double)lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          goto LABEL_17;
        case 1:
          v12 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 2));
          m_nMagicPower = (double)lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          goto LABEL_17;
        case 2:
          v12 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 3));
          m_nMagicPower = (double)lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          goto LABEL_17;
        case 3:
          v12 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 4));
          m_nMagicPower = (double)lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          goto LABEL_17;
        case 4:
          v12 = (double)(nTickPlusCountBonus2nd[v8] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v8] + 5));
          m_nMagicPower = (double)lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
LABEL_17:
          v10 = (unsigned __int64)(v12 * (m_nMagicPower * 0.5 + 100.0) * 0.0049999999);
          v11 = (unsigned __int64)(CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim) * 60.0);
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v16, ProtoType, lpSkillUser, 1u, 0x1Du, v10, v11);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v17, v14);
      if ( Skill::CAddSpell<CLowerStrengthSpell>::operator()(v15, lpVictim) )
      {
        CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
        if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
          lpVictim->__vftable[1].GetGID(lpVictim);
        *cDefenserJudge = 8;
        return 0;
      }
      else
      {
        *wError = 6;
        *cDefenserJudge = 8;
        return 0;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::LowerStrength",
      aDWorkRylSource_68,
      1765,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42E61B: variable 'v14' is possibly undefined
// 42E622: variable 'v15' is possibly undefined

//----- (0042E700) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::EnchantWeapon(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // cx
  unsigned __int16 v8; // ax
  unsigned __int16 v9; // di
  __int16 m_nINT; // di
  __int16 v11; // di
  int v12; // eax
  __int16 v13; // di
  int v14; // eax
  __int16 v15; // di
  int v16; // eax
  __int16 v17; // di
  int v18; // eax
  const CSpell::Spell_Info *v19; // eax
  Skill::CAddSpell<CEnchantWeaponSpell> *v20; // eax
  CSpell::Spell_Info v22; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v23; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      v8 = 0;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          m_nINT = lpSkillUser->m_CharacterStatus.m_nINT;
          v8 = attackType.m_cSkillLevel
             + (unsigned __int16)((int)((((unsigned __int64)m_nINT >> 32) & 7) + m_nINT) >> 8);
          v9 = m_nINT + 100;
          break;
        case 1:
          v11 = lpSkillUser->m_CharacterStatus.m_nINT;
          v12 = (int)((((unsigned __int64)v11 >> 32) & 7) + v11) >> 7;
          v9 = v11 + 120;
          v8 = v12 + attackType.m_cSkillLevel + 2;
          break;
        case 2:
          v13 = lpSkillUser->m_CharacterStatus.m_nINT;
          v14 = (int)((((unsigned __int64)v13 >> 32) & 7) + v13) >> 6;
          v9 = v13 + 140;
          v8 = v14 + attackType.m_cSkillLevel + 3;
          break;
        case 3:
          v15 = lpSkillUser->m_CharacterStatus.m_nINT;
          v16 = (int)((((unsigned __int64)v15 >> 32) & 7) + v15) >> 5;
          v9 = v15 + 160;
          v8 = v16 + attackType.m_cSkillLevel + 6;
          break;
        case 4:
          v17 = lpSkillUser->m_CharacterStatus.m_nINT;
          v18 = (int)((((unsigned __int64)v17 >> 32) & 7) + v17) >> 5;
          v9 = v17 + 180;
          v8 = v18 + attackType.m_cSkillLevel + 6;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v22, ProtoType, lpSkillUser, 1u, 0x14u, v8, v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v23, v19);
      if ( !Skill::CAddSpell<CEnchantWeaponSpell>::operator()(v20, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::EnchantWeapon",
      aDWorkRylSource_68,
      1875,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::EnchantWeapon",
      aDWorkRylSource_68,
      1856,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42E870: variable 'v19' is possibly undefined
// 42E877: variable 'v20' is possibly undefined

//----- (0042E920) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Shatter(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int64 v8; // rax
  __int16 v9; // ax
  unsigned __int16 v10; // si
  double v11; // st7
  double v12; // st6
  double v13; // st7
  const CSpell::Spell_Info *v14; // eax
  Skill::CAddSpell<CSlowSpell> *v15; // eax
  int nEnchantTick; // [esp+8h] [ebp-28h]
  unsigned __int16 nEnchantLevel; // [esp+Ch] [ebp-24h]
  float nEnchantLevela; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v20; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v21; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v8) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v9 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v9 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v9;
      if ( (unsigned __int16)v9 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Shatter",
          aDWorkRylSource_68,
          1995,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v9);
        LOWORD(v8) = 0;
      }
      else
      {
        nEnchantLevel = 0;
        LOWORD(nEnchantTick) = 0;
        v10 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v10 = 50 * attackType.m_cSkillLevel;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v12 = (double)(nTickPlusCountBonus2nd[lpSkillUsera]
                         + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[lpSkillUsera] + 1));
            goto LABEL_15;
          case 1:
            v10 = 100 * (attackType.m_cSkillLevel + 3);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v12 = (double)(nTickPlusCountBonus2nd[lpSkillUsera]
                         + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[lpSkillUsera] + 2));
            goto LABEL_15;
          case 2:
            v10 = 150 * (attackType.m_cSkillLevel + 6);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v12 = (double)(nTickPlusCountBonus2nd[lpSkillUsera]
                         + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[lpSkillUsera] + 3));
            goto LABEL_15;
          case 3:
            v10 = 200 * (attackType.m_cSkillLevel + 9);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v12 = (double)(nTickPlusCountBonus2nd[lpSkillUsera]
                         + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[lpSkillUsera] + 4));
            goto LABEL_15;
          case 4:
            v10 = 250 * (attackType.m_cSkillLevel + 12);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v12 = (double)(nTickPlusCountBonus2nd[lpSkillUsera]
                         + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[lpSkillUsera] + 5));
LABEL_15:
            nEnchantTick = (unsigned __int64)(v12 * v11 * 0.25);
            break;
          default:
            break;
        }
        CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, MASTER, MIDDLE_ADMIN, MIDDLE_ADMIN, 0, 1u);
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[lpSkillUsera],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v20, ProtoType, lpSkillUser, 1u, 0xDu, nEnchantLevel, nEnchantTick);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v21, v14);
          if ( !Skill::CAddSpell<CSlowSpell>::operator()(v15, lpVictim) )
            *wError = 6;
          nEnchantLevela = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v13 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          nEnchantLevela = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v13 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v13 * nEnchantLevela * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Shatter",
      aDWorkRylSource_68,
      1975,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v8) = 0;
  }
  return v8;
}
// 42EBFF: variable 'v14' is possibly undefined
// 42EC06: variable 'v15' is possibly undefined
// 4DCB24: using guessed type __int16 nTickPlusCountBonus2nd[6];
// 4DCB30: using guessed type __int16 nTickPlusCountBonus1st[6];
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042ECF0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::BrightArmor(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // cx
  unsigned __int64 v8; // rax
  unsigned __int16 v9; // di
  __int16 m_nMagicPower; // di
  double v11; // st7
  double v12; // st6
  const CSpell::Spell_Info *v13; // eax
  Skill::CAddSpell<CBrightArmorSpell> *v14; // eax
  CSpell::Spell_Info v16; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v17; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      LOWORD(v8) = 0;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(attackType.m_cSkillLevel + nLockCountBonus[v7]);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 1:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 6);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 2:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 12);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 3:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 18);
          v12 = (double)m_nMagicPower;
          goto LABEL_14;
        case 4:
          m_nMagicPower = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower;
          v11 = (double)(nLockCountBonus[v7] + attackType.m_cSkillLevel + 24);
          v12 = (double)m_nMagicPower;
LABEL_14:
          v8 = (unsigned __int64)(v11 * (v12 * 0.5 + 100.0) * 0.0099999998);
          v9 = m_nMagicPower + 100;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v16, ProtoType, lpSkillUser, 1u, 0x15u, v8, v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v17, v13);
      if ( !Skill::CAddSpell<CBrightArmorSpell>::operator()(v14, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BrightArmor",
      aDWorkRylSource_68,
      2220,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::BrightArmor",
      aDWorkRylSource_68,
      2201,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42EE94: variable 'v13' is possibly undefined
// 42EE9B: variable 'v14' is possibly undefined
// 4DC99C: using guessed type __int16 nLockCountBonus[6];

//----- (0042EF40) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Dazzle(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  unsigned __int16 m_cSkillLevel; // si
  unsigned __int64 v10; // rax
  double v11; // st7
  const CSpell::Spell_Info *v12; // eax
  Skill::CAddSpell<CStunSpell> *v13; // eax
  CSpell::Spell_Info v14; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v15; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 > 4u )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "Skill::CFunctions::Dazzle",
        aDWorkRylSource_68,
        2320,
        aCid0x08x_89,
        lpSkillUser->m_dwCID,
        attackType.m_wType,
        v7);
      return 0;
    }
    else
    {
      if ( Math::Random::ComplexRandom(
             lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
           + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
           + nResistanceBonus_0[v7],
             0) <= (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
      {
        *cDefenserJudge = 11;
        return 0;
      }
      m_cSkillLevel = 0;
      LOWORD(v10) = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          m_cSkillLevel = attackType.m_cSkillLevel;
          v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
              * (double)(attackType.m_cSkillLevel + 3);
          goto LABEL_17;
        case 1:
          m_cSkillLevel = attackType.m_cSkillLevel + 6;
          v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
              * (double)(attackType.m_cSkillLevel + 6);
          goto LABEL_17;
        case 2:
          m_cSkillLevel = attackType.m_cSkillLevel + 12;
          v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
              * (double)(attackType.m_cSkillLevel + 9);
          goto LABEL_17;
        case 3:
          m_cSkillLevel = attackType.m_cSkillLevel + 18;
          v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
              * (double)(attackType.m_cSkillLevel + 12);
          goto LABEL_17;
        case 4:
          m_cSkillLevel = attackType.m_cSkillLevel + 24;
          v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
              * (double)(attackType.m_cSkillLevel + 15);
LABEL_17:
          v10 = (unsigned __int64)(v11 * 0.5);
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v14, ProtoType, lpSkillUser, 1u, 0x1Au, m_cSkillLevel, v10);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v15, v12);
      if ( Skill::CAddSpell<CStunSpell>::operator()(v13, lpVictim) )
      {
        CThreat::AddToThreatList(&lpVictim->m_Threat, lpSkillUser, 1);
        if ( Creature::GetCreatureType(lpVictim->m_dwCID) == 2 )
          lpVictim->__vftable[1].GetGID(lpVictim);
        *cDefenserJudge = 8;
        return 0;
      }
      else
      {
        *wError = 6;
        *cDefenserJudge = 8;
        return 0;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Dazzle",
      aDWorkRylSource_68,
      2301,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42F0C0: variable 'v12' is possibly undefined
// 42F0C7: variable 'v13' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (0042F1A0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::HardenSkin(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  int v8; // eax
  unsigned __int16 v9; // cx
  const CSpell::Spell_Info *v10; // eax
  Skill::CAddSpell<CHardenSkinSpell> *v11; // eax
  CSpell::Spell_Info v13; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v14; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      v8 = v7;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v9 = nTickPlusCountBonus1st[v8] + attackType.m_cSkillLevel;
          break;
        case 1:
          v9 = nTickPlusCountBonus1st[v8] + attackType.m_cSkillLevel + 6;
          break;
        case 2:
          v9 = nTickPlusCountBonus1st[v8] + attackType.m_cSkillLevel + 12;
          break;
        case 3:
          v9 = nTickPlusCountBonus1st[v8] + attackType.m_cSkillLevel + 18;
          break;
        case 4:
          v9 = nTickPlusCountBonus1st[v8] + attackType.m_cSkillLevel + 24;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v13, ProtoType, lpSkillUser, 1u, 0x16u, v9, 100 * nTickBonus[v8]);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v14, v10);
      if ( !Skill::CAddSpell<CHardenSkinSpell>::operator()(v11, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::HardenSkin",
      aDWorkRylSource_68,
      2602,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::HardenSkin",
      aDWorkRylSource_68,
      2582,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42F293: variable 'v10' is possibly undefined
// 42F29A: variable 'v11' is possibly undefined
// 4DCB30: using guessed type __int16 nTickPlusCountBonus1st[6];

//----- (0042F340) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Flexibility(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 m_cSkillLevel; // ax
  __int16 v8; // cx
  int v10; // [esp+10h] [ebp-18h]
  int v11; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CFlexibilitySpell> v12; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Flexibility",
      aDWorkRylSource_68,
      2798,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  m_cSkillLevel = 0;
  v8 = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      m_cSkillLevel = attackType.m_cSkillLevel;
      v8 = 15 * attackType.m_cSkillLevel;
      break;
    case 1:
      m_cSkillLevel = attackType.m_cSkillLevel + 6;
      v8 = 15 * attackType.m_cSkillLevel + 95;
      break;
    case 2:
      m_cSkillLevel = attackType.m_cSkillLevel + 12;
      v8 = 15 * attackType.m_cSkillLevel + 190;
      break;
    case 3:
      m_cSkillLevel = attackType.m_cSkillLevel + 18;
      v8 = 15 * attackType.m_cSkillLevel + 275;
      break;
    case 4:
      m_cSkillLevel = attackType.m_cSkillLevel + 24;
      v8 = 15 * attackType.m_cSkillLevel + 370;
      break;
    default:
      break;
  }
  LOWORD(v10) = v8;
  HIWORD(v10) = 23;
  LOWORD(v11) = m_cSkillLevel;
  v12.m_Spell_Info.m_SkillProtoType = ProtoType;
  BYTE2(v11) = 1;
  *(_DWORD *)&v12.m_Spell_Info.m_wDurationSec = v10;
  v12.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v12.m_Spell_Info.m_wSpellLevel = v11;
  if ( !Skill::CAddSpell<CFlexibilitySpell>::operator()(&v12, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 42F412: variable 'v11' is possibly undefined

//----- (0042F480) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Regeneration(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // ax
  unsigned __int16 v8; // cx
  unsigned __int16 v9; // di
  __int16 v10; // ax
  __int16 v11; // dx
  const CSpell::Spell_Info *v12; // eax
  Skill::CAddSpell<CRegenerationSpell> *v13; // eax
  CSpell::Spell_Info v15; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v16; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      v8 = 0;
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v8 = attackType.m_cSkillLevel + nLevelBonus[v7];
          v10 = lpSkillUser->m_CharacterStatus.m_nWIS / 10;
          v11 = 5 * attackType.m_cSkillLevel;
          goto LABEL_14;
        case 1:
          v8 = attackType.m_cSkillLevel + nLevelBonus[v7];
          v10 = lpSkillUser->m_CharacterStatus.m_nWIS / 10;
          v11 = 5 * attackType.m_cSkillLevel + 30;
          goto LABEL_14;
        case 2:
          v8 = attackType.m_cSkillLevel + nLevelBonus[v7];
          v10 = lpSkillUser->m_CharacterStatus.m_nWIS / 10;
          v11 = 5 * attackType.m_cSkillLevel + 60;
          goto LABEL_14;
        case 3:
          v8 = attackType.m_cSkillLevel + nLevelBonus[v7];
          v10 = lpSkillUser->m_CharacterStatus.m_nWIS / 10;
          v11 = 5 * attackType.m_cSkillLevel + 90;
          goto LABEL_14;
        case 4:
          v8 = attackType.m_cSkillLevel + nLevelBonus[v7];
          v10 = lpSkillUser->m_CharacterStatus.m_nWIS / 10;
          v11 = 5 * attackType.m_cSkillLevel + 120;
LABEL_14:
          v9 = v10 + 2 * v11;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v15, ProtoType, lpSkillUser, 1u, 0xBu, v8, v9);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v16, v12);
      if ( !Skill::CAddSpell<CRegenerationSpell>::operator()(v13, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Regeneration",
      aDWorkRylSource_68,
      2904,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Regeneration",
      aDWorkRylSource_68,
      2885,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42F614: variable 'v12' is possibly undefined
// 42F61B: variable 'v13' is possibly undefined
// 4DCA24: using guessed type __int16 nLevelBonus[6];


//----- (0042F6C0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Guard(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v7; // cx
  unsigned __int16 v8; // bp
  unsigned __int16 v9; // ax
  const CSpell::Spell_Info *v10; // eax
  Skill::CAddSpell<CGuardSpell> *v11; // eax
  CSpell::Spell_Info v13; // [esp+8h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v14; // [esp+18h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
      v7 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
    else
      v7 = *((_BYTE *)&attackType + 2) >> 4;
    if ( (unsigned __int16)v7 <= 4u )
    {
      v8 = 100 * nTickBonus[v7];
      v9 = 0;
      switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
      {
        case 0:
          v9 = attackType.m_cSkillLevel
             + nTickPlusCountBonus1st[v7]
             + ((int (__thiscall *)(CAggresiveCreature *, int))lpSkillUser->EquipSkillArm)(lpSkillUser, 2);
          break;
        case 1:
          v9 = nTickPlusCountBonus1st[v7]
             + ((int (__thiscall *)(CAggresiveCreature *, int))lpSkillUser->EquipSkillArm)(lpSkillUser, 2)
             + attackType.m_cSkillLevel
             + 6;
          break;
        case 2:
          v9 = nTickPlusCountBonus1st[v7]
             + ((int (__thiscall *)(CAggresiveCreature *, int))lpSkillUser->EquipSkillArm)(lpSkillUser, 2)
             + attackType.m_cSkillLevel
             + 12;
          break;
        case 3:
          v9 = nTickPlusCountBonus1st[v7]
             + ((int (__thiscall *)(CAggresiveCreature *, int))lpSkillUser->EquipSkillArm)(lpSkillUser, 2)
             + attackType.m_cSkillLevel
             + 18;
          break;
        case 4:
          v9 = nTickPlusCountBonus1st[v7]
             + ((int (__thiscall *)(CAggresiveCreature *, int))lpSkillUser->EquipSkillArm)(lpSkillUser, 2)
             + attackType.m_cSkillLevel
             + 24;
          break;
        default:
          break;
      }
      CSpell::Spell_Info::Spell_Info(&v13, ProtoType, lpSkillUser, 1u, 0x18u, v9, v8);
      Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v14, v10);
      if ( !Skill::CAddSpell<CGuardSpell>::operator()(v11, lpVictim) )
        *wError = 6;
      *cDefenserJudge = 8;
      return 0;
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Guard",
      aDWorkRylSource_68,
      2971,
      aCid0x08x_89,
      lpSkillUser->m_dwCID,
      attackType.m_wType,
      v7);
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Guard",
      aDWorkRylSource_68,
      2951,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 42F7F0: variable 'v10' is possibly undefined
// 42F7F7: variable 'v11' is possibly undefined
// 4DCA4C: using guessed type __int16 nTickBonus[6];
// 4DCB30: using guessed type __int16 nTickPlusCountBonus1st[6];

//----- (0042F8A0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::FastHit(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int16 v8; // ax
  unsigned __int16 v9; // di
  float fDRC; // [esp+4h] [ebp-30h]
  CalculateDamageInfo AddEffectInfo; // [esp+24h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      return 0;
    }
    else
    {
      if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, lpVictim) )
        *wError = 6;
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v8 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v8 = *((_BYTE *)&attackType + 2) >> 4;
      if ( (unsigned __int16)v8 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          aSkillCfunction_83,
          aDWorkRylSource_68,
          3039,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v8);
        return 0;
      }
      else
      {
        fDRC = (double)attackType.m_cSkillLevel * 0.050000001
             + flt_4DCA70[(*((unsigned __int8 *)&attackType + 2) >> 1) & 7];
        CalculateDamageInfo::CalculateDamageInfo(
          &AddEffectInfo,
          0,
          fDRC,
          2 * (attackType.m_cSkillLevel + 6 * v8),
          attackType.m_cSkillLevel + 6 * v8,
          attackType.m_cSkillLevel + 6 * v8);
        v9 = CAggresiveCreature::CalculateDamage(
               lpVictim,
               (int)lpSkillUser,
               COERCE_FLOAT(&AddEffectInfo),
               *(float *)&cDefenserJudge);
        CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v9, TAUNT);
        return v9;
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      aSkillCfunction_83,
      aDWorkRylSource_68,
      3015,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4DCA70: using guessed type float flt_4DCA70[];

//----- (0042FA00) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::PowerDrain(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  CAggresiveCreature_vtbl *v8; // edx
  __int16 v9; // ax
  unsigned __int16 v10; // di
  unsigned __int16 m_nMaxHP; // ax
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, lpVictim) )
      *wError = 6;
    v8 = lpSkillUser->__vftable;
    v9 = attackType.m_cSkillLevel + nSkillLevels[*((unsigned __int8 *)&attackType + 2) >> 4];
    AddEffectInfo.m_fDRC = (double)attackType.m_cSkillLevel * 0.050000001
                         + fDRCs_3[*((unsigned __int8 *)&attackType + 2) >> 4];
    AddEffectInfo.m_bForceDRC = 1;
    AddEffectInfo.m_nOffenceRevision = v9;
    AddEffectInfo.m_nMinDamage = v9;
    AddEffectInfo.m_nMaxDamage = v9;
    if ( !v8->EquipSkillArm(lpSkillUser, Attach) )
      return 0;
    v10 = CAggresiveCreature::CalculateDamage(
            lpVictim,
            (int)lpSkillUser,
            COERCE_FLOAT(&AddEffectInfo),
            *(float *)&cDefenserJudge);
    if ( lpSkillUser->EquipSkillArm(lpSkillUser, Detach) )
    {
      lpSkillUser->m_CreatureStatus.m_nNowHP += v10 >> 1;
      m_nMaxHP = lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMaxHP;
      if ( lpSkillUser->m_CreatureStatus.m_nNowHP > m_nMaxHP )
        lpSkillUser->m_CreatureStatus.m_nNowHP = m_nMaxHP;
      *cOffencerJudge = 5;
      CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v10, DETAUNT);
      return v10;
    }
    else
    {
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::PowerDrain",
      aDWorkRylSource_68,
      3086,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (0042FB50) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::RingGeyser(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // eax
  double v9; // st7
  __int16 v10; // dx
  CAggresiveCreature_vtbl *v11; // eax
  unsigned __int16 v12; // di
  CalculateDamageInfo AddEffectInfo; // [esp+8h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
      return 0;
    if ( !Skill::CFunctions::SlowlySkillAttack(ProtoType, *(_DWORD *)&attackType, lpSkillUser, lpVictim) )
      *wError = 6;
    v8 = *((unsigned __int8 *)&attackType + 2) >> 4;
    v9 = (double)attackType.m_cSkillLevel * 0.050000001 + fDRCs_4[v8];
    LOWORD(v8) = attackType.m_cSkillLevel + nSkillLevels[v8];
    AddEffectInfo.m_fDRC = v9;
    AddEffectInfo.m_bForceDRC = 1;
    v10 = 4 * v8;
    AddEffectInfo.m_nMinDamage = 2 * v8;
    AddEffectInfo.m_nMaxDamage = 2 * v8;
    v11 = lpSkillUser->__vftable;
    AddEffectInfo.m_nOffenceRevision = v10;
    if ( !v11->EquipSkillArm(lpSkillUser, Attach) )
      return 0;
    v12 = CAggresiveCreature::CalculateDamage(
            lpVictim,
            (int)lpSkillUser,
            COERCE_FLOAT(&AddEffectInfo),
            *(float *)&cDefenserJudge);
    if ( lpSkillUser->EquipSkillArm(lpSkillUser, Detach) )
    {
      CThreat::AffectThreat(&lpVictim->m_Threat, lpSkillUser, v12, DETAUNT);
      return v12;
    }
    else
    {
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::RingGeyser",
      aDWorkRylSource_68,
      3122,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4DC574: using guessed type __int16 nSkillLevels[6];

//----- (0042FC80) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Rot(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int64 v8; // rax
  __int16 v9; // ax
  __int16 v10; // si
  int v11; // eax
  double v12; // st7
  int v13; // eax
  int v14; // eax
  int v15; // eax
  int v16; // eax
  double v17; // st7
  const CSpell::Spell_Info *v18; // eax
  Skill::CAddSpell<CPoisonedSpell> *v19; // eax
  int nEnchantTick; // [esp+8h] [ebp-28h]
  int nEnchantLevel; // [esp+Ch] [ebp-24h]
  float nEnchantLevela; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v24; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v25; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v8) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v9 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v9 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v9;
      if ( (unsigned __int16)v9 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Rot",
          aDWorkRylSource_68,
          3219,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v9);
        LOWORD(v8) = 0;
      }
      else
      {
        LOWORD(nEnchantLevel) = 0;
        LOWORD(nEnchantTick) = 0;
        v10 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v11 = v9;
            v10 = nPlusLockCountBonus_3[v11] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v11] + 70);
            nEnchantLevel = (unsigned __int64)((double)(nTickPlusCountBonus2nd[v11]
                                                      + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v11] + 1))
                                             * 0.5);
            v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower + 100);
            goto LABEL_15;
          case 1:
            v13 = v9;
            v10 = nPlusLockCountBonus_3[v13] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v13] + 140);
            nEnchantLevel = (unsigned __int64)((double)(nTickPlusCountBonus2nd[v13]
                                                      + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v13] + 2))
                                             * 0.5);
            v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower + 100);
            goto LABEL_15;
          case 2:
            v14 = v9;
            v10 = nPlusLockCountBonus_3[v14] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v14] + 210);
            nEnchantLevel = (unsigned __int64)((double)(nTickPlusCountBonus2nd[v14]
                                                      + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v14] + 3))
                                             * 0.5);
            v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower + 100);
            goto LABEL_15;
          case 3:
            v15 = v9;
            v10 = nPlusLockCountBonus_3[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v15] + 280);
            nEnchantLevel = (unsigned __int64)((double)(nTickPlusCountBonus2nd[v15]
                                                      + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 4))
                                             * 0.5);
            v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower + 100);
            goto LABEL_15;
          case 4:
            v16 = v9;
            v10 = nPlusLockCountBonus_3[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v16] + 350);
            nEnchantLevel = (unsigned __int64)((double)(nTickPlusCountBonus2nd[v16]
                                                      + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 5))
                                             * 0.5);
            v12 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower + 100);
LABEL_15:
            nEnchantTick = (unsigned __int64)(v12 * 0.12);
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[lpSkillUsera],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v24, ProtoType, lpSkillUser, 1u, 0x1Cu, nEnchantLevel, nEnchantTick);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v25, v18);
          if ( !Skill::CAddSpell<CPoisonedSpell>::operator()(v19, lpVictim) )
            *wError = 6;
          nEnchantLevela = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v17 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          nEnchantLevela = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v17 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v17 * nEnchantLevela * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Rot",
      aDWorkRylSource_68,
      3197,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v8) = 0;
  }
  return v8;
}
// 43001C: variable 'v18' is possibly undefined
// 430023: variable 'v19' is possibly undefined
// 4DCB24: using guessed type __int16 nTickPlusCountBonus2nd[6];
// 4DCB30: using guessed type __int16 nTickPlusCountBonus1st[6];
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (00430110) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Shackle(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  unsigned __int16 v8; // bp
  __int64 v9; // rax
  __int16 v10; // ax
  __int16 v11; // si
  int v12; // ebp
  double v13; // st7
  double v14; // st6
  int v15; // ebp
  int v16; // ebp
  int v17; // ebp
  int v18; // ebp
  double v19; // st7
  const CSpell::Spell_Info *v20; // eax
  Skill::CAddSpell<CHoldSpell> *v21; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  float v24; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v25; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v26; // [esp+20h] [ebp-10h] BYREF
  __int16 lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  v8 = 0;
  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v9) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v10 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v10 = *((_BYTE *)&attackType + 2) >> 4;
      lpSkillUsera = v10;
      if ( (unsigned __int16)v10 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Shackle",
          aDWorkRylSource_68,
          3371,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v10);
        LOWORD(v9) = 0;
      }
      else
      {
        v11 = 0;
        nEnchantLevel = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v12 = v10;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = nPlusLockCountBonus_3[v12] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v12] + 70);
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v12] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v12] + 1));
            goto LABEL_15;
          case 1:
            v15 = v10;
            v11 = nPlusLockCountBonus_3[v15] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v15] + 140);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v15] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v15] + 2));
            goto LABEL_15;
          case 2:
            v16 = v10;
            v11 = nPlusLockCountBonus_3[v16] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v16] + 210);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v16] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v16] + 3));
            goto LABEL_15;
          case 3:
            v17 = v10;
            v11 = nPlusLockCountBonus_3[v17] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v17] + 280);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v17] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v17] + 4));
            goto LABEL_15;
          case 4:
            v18 = v10;
            v11 = nPlusLockCountBonus_3[v18] + attackType.m_cSkillLevel * (nMultiplyLockCountBonus_1[v18] + 350);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v13 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
            v14 = (double)(nTickPlusCountBonus2nd[v18] + attackType.m_cSkillLevel * (nTickPlusCountBonus1st[v18] + 5));
LABEL_15:
            v8 = (unsigned __int64)(v14 * v13 * 0.25);
            v10 = lpSkillUsera;
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[v10],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(&v25, ProtoType, lpSkillUser, 1u, 0x19u, nEnchantLevel, v8);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v26, v20);
          if ( !Skill::CAddSpell<CHoldSpell>::operator()(v21, lpVictim) )
            *wError = 6;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          v24 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v19 = (double)(v11
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v19 * v24 * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Shackle",
      aDWorkRylSource_68,
      3349,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v9) = 0;
  }
  return v9;
}
// 43042F: variable 'v20' is possibly undefined
// 430436: variable 'v21' is possibly undefined
// 4DCB24: using guessed type __int16 nTickPlusCountBonus2nd[6];
// 4DCB30: using guessed type __int16 nTickPlusCountBonus1st[6];
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (00430520) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::Flash(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  __int64 v8; // rax
  __int16 v9; // ax
  __int16 v10; // si
  double v11; // st7
  double v12; // st7
  const CSpell::Spell_Info *v13; // eax
  Skill::CAddSpell<CStunSpell> *v14; // eax
  unsigned __int16 nEnchantLevel; // [esp+8h] [ebp-28h]
  __int16 nLockCount; // [esp+Ch] [ebp-24h]
  float nLockCounta; // [esp+Ch] [ebp-24h]
  CSpell::Spell_Info v19; // [esp+10h] [ebp-20h] BYREF
  Skill::CAddSpell<CPoisonedSpell> v20; // [esp+20h] [ebp-10h] BYREF
  CAggresiveCreature *lpSkillUsera; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserb; // [esp+3Ch] [ebp+Ch]
  float lpSkillUserc; // [esp+3Ch] [ebp+Ch]

  if ( lpSkillUser && lpVictim )
  {
    if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    {
      LOWORD(v8) = 0;
    }
    else
    {
      if ( (lpSkillUser->m_dwCID & 0x80000000) == 0 )
        v9 = lpSkillUser->GetSkillLockCount(lpSkillUser, (unsigned __int16)attackType);
      else
        v9 = *((_BYTE *)&attackType + 2) >> 4;
      nLockCount = v9;
      if ( (unsigned __int16)v9 > 4u )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CFunctions::Flash",
          aDWorkRylSource_68,
          3483,
          aCid0x08x_89,
          lpSkillUser->m_dwCID,
          attackType.m_wType,
          v9);
        LOWORD(v8) = 0;
      }
      else
      {
        nEnchantLevel = 0;
        LOWORD(lpSkillUsera) = 0;
        v10 = 0;
        switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
        {
          case 0:
            v10 = 40 * attackType.m_cSkillLevel;
            nEnchantLevel = attackType.m_cSkillLevel;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(attackType.m_cSkillLevel + 3);
            goto LABEL_15;
          case 1:
            v10 = 16 * (5 * attackType.m_cSkillLevel + 15);
            nEnchantLevel = attackType.m_cSkillLevel + 6;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(attackType.m_cSkillLevel + 6);
            goto LABEL_15;
          case 2:
            v10 = 120 * (attackType.m_cSkillLevel + 6);
            nEnchantLevel = attackType.m_cSkillLevel + 12;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(attackType.m_cSkillLevel + 9);
            goto LABEL_15;
          case 3:
            v10 = 32 * (5 * attackType.m_cSkillLevel + 45);
            nEnchantLevel = attackType.m_cSkillLevel + 18;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(attackType.m_cSkillLevel + 12);
            goto LABEL_15;
          case 4:
            v10 = 200 * (attackType.m_cSkillLevel + 12);
            nEnchantLevel = attackType.m_cSkillLevel + 24;
            v11 = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim)
                * (double)(attackType.m_cSkillLevel + 15);
LABEL_15:
            lpSkillUsera = (CAggresiveCreature *)(unsigned __int64)(v11 * 0.5);
            break;
          default:
            break;
        }
        if ( Math::Random::ComplexRandom(
               lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
             + lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance
             + nResistanceBonus_0[nLockCount],
               0) > (unsigned __int16)lpVictim->m_CreatureStatus.m_StatusInfo.m_nMagicResistance )
        {
          CSpell::Spell_Info::Spell_Info(
            &v19,
            ProtoType,
            lpSkillUser,
            1u,
            0x1Au,
            nEnchantLevel,
            (unsigned __int16)lpSkillUsera);
          Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(&v20, v13);
          if ( !Skill::CAddSpell<CStunSpell>::operator()(v14, lpVictim) )
            *wError = 6;
          nLockCounta = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserc = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v12 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserc;
        }
        else
        {
          *cDefenserJudge = 11;
          nLockCounta = CAggresiveCreature::CalculateLevelGapAffect(lpSkillUser, lpVictim);
          lpSkillUserb = 100.0 - Skill::CFunctions::ResistanceFactor(lpSkillUser, lpVictim);
          v12 = (double)(v10
                       * (Math::Random::ComplexRandom(50, 0)
                        + lpSkillUser->m_CreatureStatus.m_StatusInfo.m_nMagicPower
                        + 100))
              * lpSkillUserb;
        }
        return (unsigned __int64)(v12 * nLockCounta * 0.000099999997);
      }
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::Flash",
      aDWorkRylSource_68,
      3464,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    LOWORD(v8) = 0;
  }
  return v8;
}
// 43075D: variable 'v13' is possibly undefined
// 430764: variable 'v14' is possibly undefined
// 4DCB64: using guessed type __int16 nResistanceBonus_0[6];

//----- (00430850) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::StrengthPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+Ch] [ebp-14h]
  Skill::CAddSpell<CStrengthSpell> v9; // [esp+10h] [ebp-10h] BYREF

  if ( lpSkillUser && lpVictim )
  {
    v9.m_Spell_Info.m_lpCaster = lpSkillUser;
    LOWORD(v8) = (*((_BYTE *)&attackType + 2) >> 4) + 1;
    BYTE2(v8) = 1;
    v9.m_Spell_Info.m_SkillProtoType = ProtoType;
    *(_DWORD *)&v9.m_Spell_Info.m_wDurationSec = 786612;
    *(_DWORD *)&v9.m_Spell_Info.m_wSpellLevel = v8;
    if ( !Skill::CAddSpell<CStrengthSpell>::operator()(&v9, lpVictim) )
      *wError = 6;
    *cDefenserJudge = 8;
    return 0;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::StrengthPotion",
      aDWorkRylSource_68,
      3663,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
}
// 4308A8: variable 'v8' is possibly undefined

//----- (00430900) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DefencePotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CDefencePotionSpell> v9; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DefencePotion",
      aDWorkRylSource_68,
      3749,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  LOWORD(v8) = nEnchantLevel[*((unsigned __int8 *)&attackType + 2) >> 4];
  BYTE2(v8) = 1;
  v9.m_Spell_Info.m_SkillProtoType = ProtoType;
  *(_DWORD *)&v9.m_Spell_Info.m_wSpellLevel = v8;
  v9.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v9.m_Spell_Info.m_wDurationSec = 458932;
  if ( !Skill::CAddSpell<CDefencePotionSpell>::operator()(&v9, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 43096D: variable 'v8' is possibly undefined
// 4DCC48: using guessed type __int16 nEnchantLevel[6];

//----- (004309D0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::DisenchantPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+10h] [ebp-18h]
  int v9; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CDisenchantPotionSpell> v10; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::DisenchantPotion",
      aDWorkRylSource_68,
      3767,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  CAffectedSpell::Disenchant(&lpVictim->m_SpellMgr.m_AffectedInfo, MASTER, LEAVE_WAIT, NONE, 0, 0xFFu);
  if ( (*((_BYTE *)&attackType + 2) & 0xF0) != 0 )
  {
    LOWORD(v9) = 1;
    BYTE2(v9) = 1;
    LOWORD(v8) = nEnchantTick[*((unsigned __int8 *)&attackType + 2) >> 4];
    v10.m_Spell_Info.m_SkillProtoType = ProtoType;
    HIWORD(v8) = 8;
    *(_DWORD *)&v10.m_Spell_Info.m_wSpellLevel = v9;
    v10.m_Spell_Info.m_lpCaster = lpSkillUser;
    *(_DWORD *)&v10.m_Spell_Info.m_wDurationSec = v8;
    if ( !Skill::CAddSpell<CDisenchantPotionSpell>::operator()(&v10, lpVictim) )
      *wError = 6;
  }
  *cDefenserJudge = 9;
  return 0;
}
// 430A5C: variable 'v9' is possibly undefined
// 4DCBDC: using guessed type __int16 nEnchantTick[6];

//----- (00430AC0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::MagicPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  CAggresiveCreature *v7; // ebx
  CAggresiveCreature *v8; // esi
  unsigned __int64 v9; // rax
  double v10; // st7
  bool v11; // c0
  double v12; // st7
  double v13; // st7
  double v14; // st7
  double v15; // st7
  float *p_lpSkillUser; // eax
  unsigned __int16 m_nMaxMP; // ax
  int v19; // [esp+8h] [ebp-24h] BYREF
  int v20; // [esp+14h] [ebp-18h]
  int v21; // [esp+18h] [ebp-14h]
  Skill::CAddSpell<CMagicPotionSpell> v22; // [esp+1Ch] [ebp-10h] BYREF

  v7 = lpSkillUser;
  v8 = lpVictim;
  if ( *(float *)&lpSkillUser == 0.0 || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::MagicPotion",
      aDWorkRylSource_68,
      3792,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  LOWORD(v9) = 0;
  switch ( *((unsigned __int8 *)&attackType + 2) >> 4 )
  {
    case 0:
      lpSkillUser = (CAggresiveCreature *)v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      v19 = 1128792064;
      v10 = (double)(int)lpSkillUser * 0.1;
      *(float *)&lpSkillUser = v10;
      v11 = v10 < 200.0;
      goto LABEL_10;
    case 1:
      lpSkillUser = (CAggresiveCreature *)v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      v19 = 1137180672;
      v12 = (double)(int)lpSkillUser * 0.2;
      *(float *)&lpSkillUser = v12;
      v11 = v12 < 400.0;
      goto LABEL_10;
    case 2:
      lpSkillUser = (CAggresiveCreature *)v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      v19 = 1142292480;
      v13 = (double)(int)lpSkillUser * 0.30000001;
      *(float *)&lpSkillUser = v13;
      v11 = v13 < 600.0;
      goto LABEL_10;
    case 3:
      lpSkillUser = (CAggresiveCreature *)v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      v19 = 1145569280;
      v14 = (double)(int)lpSkillUser * 0.40000001;
      *(float *)&lpSkillUser = v14;
      v11 = v14 < 800.0;
      goto LABEL_10;
    case 4:
      lpSkillUser = (CAggresiveCreature *)v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
      v19 = 1148846080;
      v15 = (double)(int)lpSkillUser * 0.5;
      *(float *)&lpSkillUser = v15;
      v11 = v15 < 1000.0;
LABEL_10:
      p_lpSkillUser = (float *)&v19;
      if ( !v11 )
        p_lpSkillUser = (float *)&lpSkillUser;
      v9 = (unsigned __int64)*p_lpSkillUser;
      break;
    default:
      break;
  }
  v8->m_CreatureStatus.m_nNowMP += v9;
  m_nMaxMP = v8->m_CreatureStatus.m_StatusInfo.m_nMaxMP;
  if ( v8->m_CreatureStatus.m_nNowMP > m_nMaxMP )
    v8->m_CreatureStatus.m_nNowMP = m_nMaxMP;
  LOWORD(v20) = nEnchantTick_0[*((unsigned __int8 *)&attackType + 2) >> 4];
  LOWORD(v21) = 1;
  BYTE2(v21) = 1;
  v22.m_Spell_Info.m_SkillProtoType = ProtoType;
  HIWORD(v20) = 9;
  v22.m_Spell_Info.m_lpCaster = v7;
  *(_DWORD *)&v22.m_Spell_Info.m_wDurationSec = v20;
  *(_DWORD *)&v22.m_Spell_Info.m_wSpellLevel = v21;
  if ( !Skill::CAddSpell<CMagicPotionSpell>::operator()(&v22, v8) )
    *wError = 6;
  *cDefenserJudge = 6;
  return 0;
}
// 4DCC08: using guessed type __int16 nEnchantTick_0[6];

//----- (00430CC0) --------------------------------------------------------
unsigned __int16 __cdecl Skill::CFunctions::LightningPotion(
        const Skill::ProtoType *ProtoType,
        AtType attackType,
        CAggresiveCreature *lpSkillUser,
        CAggresiveCreature *lpVictim,
        unsigned __int8 *cOffencerJudge,
        unsigned __int8 *cDefenserJudge,
        unsigned __int16 *wError)
{
  int v8; // [esp+14h] [ebp-14h]
  Skill::CAddSpell<CLightningPotionSpell> v9; // [esp+18h] [ebp-10h] BYREF

  if ( !lpSkillUser || !lpVictim )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CFunctions::LightningPotion",
      aDWorkRylSource_68,
      3827,
      (char *)&byte_4DBFA8,
      lpSkillUser,
      lpVictim);
    return 0;
  }
  if ( Skill::CFunctions::ConsumeMP(attackType, lpSkillUser) == -1 )
    return 0;
  LOWORD(v8) = nEnchantLevel[*((unsigned __int8 *)&attackType + 2) >> 4];
  BYTE2(v8) = 1;
  v9.m_Spell_Info.m_SkillProtoType = ProtoType;
  *(_DWORD *)&v9.m_Spell_Info.m_wSpellLevel = v8;
  v9.m_Spell_Info.m_lpCaster = lpSkillUser;
  *(_DWORD *)&v9.m_Spell_Info.m_wDurationSec = 655540;
  if ( !Skill::CAddSpell<CLightningPotionSpell>::operator()(&v9, lpVictim) )
    *wError = 6;
  *cDefenserJudge = 8;
  return 0;
}
// 430D2D: variable 'v8' is possibly undefined
// 4DCC48: using guessed type __int16 nEnchantLevel[6];

//----- (00430D90) --------------------------------------------------------
Skill::CProcessTable::ProcessInfo *__cdecl std::_Copy_opt<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last,
        Skill::CProcessTable::ProcessInfo *_Dest)
{
  Skill::CProcessTable::ProcessInfo *Myptr; // ecx
  Skill::CProcessTable::ProcessInfo *result; // eax
  Skill::CProcessTable::ProcessInfo *v5; // edi
  Skill::ProtoType *m_lpProtoType; // esi

  Myptr = _First._Myptr;
  for ( result = _Dest; Myptr != _Last._Myptr; v5->m_lpProtoType = m_lpProtoType )
  {
    v5 = result;
    *(_DWORD *)&result->m_usSkill_ID = *(_DWORD *)&Myptr->m_usSkill_ID;
    result->m_fnProcess = Myptr->m_fnProcess;
    m_lpProtoType = Myptr->m_lpProtoType;
    ++Myptr;
    ++result;
  }
  return result;
}

//----- (00430DD0) --------------------------------------------------------
void __cdecl std::_Median<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Mid,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  int v3; // eax
  unsigned int v4; // edi
  unsigned int v5; // esi
  Skill::CProcessTable::ProcessInfo *v7; // [esp-10h] [ebp-14h]
  Skill::CProcessTable::ProcessInfo *_Firsta; // [esp+8h] [ebp+4h]

  v3 = _Last._Myptr - _First._Myptr;
  if ( v3 <= 40 )
  {
    std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(_First, _Mid, _Last);
  }
  else
  {
    v4 = 24 * ((v3 + 1) / 8);
    v5 = 12 * ((v3 + 1) / 8);
    v7 = &_First._Myptr[v4 / 0xC];
    _Firsta = &_First._Myptr[v5 / 0xC];
    std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
      _First,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)_Firsta,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v7);
    std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Mid._Myptr[v5 / 0xFFFFFFF4],
      _Mid,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Mid._Myptr[v5 / 0xC]);
    std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Last._Myptr[v4 / 0xFFFFFFF4],
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF4],
      _Last);
    std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)_Firsta,
      _Mid,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Last._Myptr[v5 / 0xFFFFFFF4]);
  }
}

//----- (00430E70) --------------------------------------------------------
void __cdecl std::_Adjust_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        int _Hole,
        int _Bottom,
        Skill::CProcessTable::ProcessInfo _Val)
{
  int v4; // ecx
  int v5; // eax
  bool i; // zf
  Skill::CProcessTable::ProcessInfo *v7; // edx
  Skill::CProcessTable::ProcessInfo *v8; // ecx
  Skill::CProcessTable::ProcessInfo *v9; // eax
  Skill::CProcessTable::ProcessInfo *v10; // ecx

  v4 = _Hole;
  v5 = 2 * _Hole + 2;
  for ( i = v5 == _Bottom; v5 < _Bottom; i = v5 == _Bottom )
  {
    if ( _First._Myptr[v5].m_usSkill_ID < _First._Myptr[v5 - 1].m_usSkill_ID )
      --v5;
    v7 = &_First._Myptr[v5];
    v8 = &_First._Myptr[v4];
    *(_DWORD *)&v8->m_usSkill_ID = *(_DWORD *)&v7->m_usSkill_ID;
    v8->m_fnProcess = v7->m_fnProcess;
    v8->m_lpProtoType = v7->m_lpProtoType;
    v4 = v5;
    v5 = 2 * v5 + 2;
  }
  if ( i )
  {
    v9 = &_First._Myptr[_Bottom - 1];
    v10 = &_First._Myptr[v4];
    *(_DWORD *)&v10->m_usSkill_ID = *(_DWORD *)&v9->m_usSkill_ID;
    v10->m_fnProcess = v9->m_fnProcess;
    v10->m_lpProtoType = v9->m_lpProtoType;
    v4 = _Bottom - 1;
  }
  std::_Push_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
    _First,
    v4,
    _Hole,
    _Val);
}

//----- (00430F20) --------------------------------------------------------
Skill::CProcessTable::ProcessInfo *__cdecl std::copy<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last,
        Skill::CProcessTable::ProcessInfo *_Dest)
{
  return std::_Copy_opt<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(
           _First,
           _Last,
           _Dest);
}

//----- (00430F40) --------------------------------------------------------
std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> *result,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  Skill::CProcessTable::ProcessInfo *v3; // ecx
  Skill::CProcessTable::ProcessInfo *i; // edi
  unsigned __int16 m_usSkill_ID; // ax
  unsigned __int16 v6; // dx
  Skill::CProcessTable::ProcessInfo *Myptr; // esi
  Skill::CProcessTable::ProcessInfo *v8; // edx
  bool v9; // zf
  Skill::CProcessTable::ProcessInfo *v10; // eax
  int v11; // eax
  Skill::CProcessTable::ProcessInfo *v12; // eax
  std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> *v13; // eax
  std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Glast; // [esp+10h] [ebp-58h]
  int v15; // [esp+14h] [ebp-54h]
  unsigned __int16 (__cdecl *m_fnProcess)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+18h] [ebp-50h]
  Skill::ProtoType *m_lpProtoType; // [esp+1Ch] [ebp-4Ch]
  int v18; // [esp+20h] [ebp-48h]
  unsigned __int16 (__cdecl *v19)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+24h] [ebp-44h]
  Skill::ProtoType *v20; // [esp+28h] [ebp-40h]
  int v21; // [esp+2Ch] [ebp-3Ch]
  unsigned __int16 (__cdecl *v22)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+30h] [ebp-38h]
  Skill::ProtoType *v23; // [esp+34h] [ebp-34h]
  int v24; // [esp+38h] [ebp-30h]
  unsigned __int16 (__cdecl *v25)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+3Ch] [ebp-2Ch]
  Skill::ProtoType *v26; // [esp+40h] [ebp-28h]
  int v27; // [esp+44h] [ebp-24h]
  unsigned __int16 (__cdecl *v28)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+48h] [ebp-20h]
  Skill::ProtoType *v29; // [esp+4Ch] [ebp-1Ch]
  int v30; // [esp+50h] [ebp-18h]
  unsigned __int16 (__cdecl *v31)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+54h] [ebp-14h]
  Skill::ProtoType *v32; // [esp+58h] [ebp-10h]
  unsigned __int16 (__cdecl *v33)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // [esp+60h] [ebp-8h]
  Skill::ProtoType *v34; // [esp+64h] [ebp-4h]

  std::_Median<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
    _First,
    (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_First._Myptr[(_Last._Myptr - _First._Myptr) / 2],
    (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)&_Last._Myptr[-1]);
  v3 = &_First._Myptr[(_Last._Myptr - _First._Myptr) / 2];
  for ( i = v3 + 1; _First._Myptr < v3; --v3 )
  {
    m_usSkill_ID = v3[-1].m_usSkill_ID;
    if ( v3->m_usSkill_ID > m_usSkill_ID )
      break;
    if ( v3->m_usSkill_ID < m_usSkill_ID )
      break;
  }
  if ( i < _Last._Myptr )
  {
    v6 = v3->m_usSkill_ID;
    do
    {
      if ( v6 > i->m_usSkill_ID )
        break;
      if ( v6 < i->m_usSkill_ID )
        break;
      ++i;
    }
    while ( i < _Last._Myptr );
  }
  Myptr = v3;
  v8 = i;
  _Glast._Myptr = v3;
  while ( 1 )
  {
    while ( 1 )
    {
      for ( ; v8 < _Last._Myptr; ++v8 )
      {
        if ( v8->m_usSkill_ID <= v3->m_usSkill_ID )
        {
          if ( v8->m_usSkill_ID < v3->m_usSkill_ID )
            break;
          v15 = *(_DWORD *)&i->m_usSkill_ID;
          m_lpProtoType = i->m_lpProtoType;
          m_fnProcess = i->m_fnProcess;
          *(_DWORD *)&i->m_usSkill_ID = *(_DWORD *)&v8->m_usSkill_ID;
          i->m_fnProcess = v8->m_fnProcess;
          i->m_lpProtoType = v8->m_lpProtoType;
          *(_DWORD *)&v8->m_usSkill_ID = v15;
          v8->m_fnProcess = m_fnProcess;
          ++i;
          v8->m_lpProtoType = m_lpProtoType;
        }
      }
      v9 = Myptr == _First._Myptr;
      if ( Myptr > _First._Myptr )
      {
        v10 = Myptr - 1;
        do
        {
          if ( v3->m_usSkill_ID <= v10->m_usSkill_ID )
          {
            if ( v3->m_usSkill_ID < v10->m_usSkill_ID )
              break;
            --v3;
            v18 = *(_DWORD *)&v3->m_usSkill_ID;
            v20 = v3->m_lpProtoType;
            v19 = v3->m_fnProcess;
            *(_DWORD *)&v3->m_usSkill_ID = *(_DWORD *)&v10->m_usSkill_ID;
            v3->m_fnProcess = v10->m_fnProcess;
            v3->m_lpProtoType = v10->m_lpProtoType;
            *(_DWORD *)&v10->m_usSkill_ID = v18;
            v10->m_fnProcess = v19;
            v10->m_lpProtoType = v20;
            Myptr = _Glast._Myptr;
          }
          --Myptr;
          --v10;
          _Glast._Myptr = Myptr;
        }
        while ( _First._Myptr < Myptr );
        v9 = Myptr == _First._Myptr;
      }
      if ( v9 )
        break;
      _Glast._Myptr = --Myptr;
      if ( v8 == _Last._Myptr )
      {
        if ( Myptr != --v3 )
        {
          v24 = *(_DWORD *)&Myptr->m_usSkill_ID;
          v26 = Myptr->m_lpProtoType;
          v25 = Myptr->m_fnProcess;
          *(_DWORD *)&Myptr->m_usSkill_ID = *(_DWORD *)&v3->m_usSkill_ID;
          Myptr->m_fnProcess = v3->m_fnProcess;
          Myptr->m_lpProtoType = v3->m_lpProtoType;
          *(_DWORD *)&v3->m_usSkill_ID = v24;
          v3->m_fnProcess = v25;
          v3->m_lpProtoType = v26;
        }
        v27 = *(_DWORD *)&v3->m_usSkill_ID;
        v29 = v3->m_lpProtoType;
        v28 = v3->m_fnProcess;
        --i;
        *(_DWORD *)&v3->m_usSkill_ID = *(_DWORD *)&i->m_usSkill_ID;
        v3->m_fnProcess = i->m_fnProcess;
        v3->m_lpProtoType = i->m_lpProtoType;
        *(_DWORD *)&i->m_usSkill_ID = v27;
        i->m_fnProcess = v28;
        i->m_lpProtoType = v29;
      }
      else
      {
        v30 = *(_DWORD *)&v8->m_usSkill_ID;
        v32 = v8->m_lpProtoType;
        v31 = v8->m_fnProcess;
        *(_DWORD *)&v8->m_usSkill_ID = *(_DWORD *)&Myptr->m_usSkill_ID;
        v8->m_fnProcess = Myptr->m_fnProcess;
        v8->m_lpProtoType = Myptr->m_lpProtoType;
        *(_DWORD *)&Myptr->m_usSkill_ID = v30;
        Myptr->m_fnProcess = v31;
        ++v8;
        Myptr->m_lpProtoType = v32;
      }
    }
    if ( v8 == _Last._Myptr )
      break;
    if ( i != v8 )
    {
      v11 = *(_DWORD *)&v3->m_usSkill_ID;
      v34 = v3->m_lpProtoType;
      v33 = v3->m_fnProcess;
      *(_DWORD *)&v3->m_usSkill_ID = *(_DWORD *)&i->m_usSkill_ID;
      v3->m_fnProcess = i->m_fnProcess;
      v3->m_lpProtoType = i->m_lpProtoType;
      *(_DWORD *)&i->m_usSkill_ID = v11;
      i->m_fnProcess = v33;
      i->m_lpProtoType = v34;
    }
    v21 = *(_DWORD *)&v3->m_usSkill_ID;
    v12 = v8;
    v23 = v3->m_lpProtoType;
    v22 = v3->m_fnProcess;
    *(_DWORD *)&v3->m_usSkill_ID = *(_DWORD *)&v8->m_usSkill_ID;
    v3->m_fnProcess = v8->m_fnProcess;
    v3->m_lpProtoType = v8->m_lpProtoType;
    *(_DWORD *)&v8->m_usSkill_ID = v21;
    v8->m_fnProcess = v22;
    ++i;
    ++v8;
    v12->m_lpProtoType = v23;
    Myptr = _Glast._Myptr;
    ++v3;
  }
  v13 = result;
  result->second._Myptr = i;
  result->first._Myptr = v3;
  return v13;
}

//----- (00431240) --------------------------------------------------------
void __cdecl std::_Make_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  int v2; // esi
  Skill::CProcessTable::ProcessInfo *v3; // ebx

  v2 = (_Last._Myptr - _First._Myptr) / 2;
  if ( v2 > 0 )
  {
    v3 = &_First._Myptr[v2];
    do
      std::_Adjust_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        _First,
        --v2,
        _Last._Myptr - _First._Myptr,
        *--v3);
    while ( v2 > 0 );
  }
}

//----- (004312B0) --------------------------------------------------------
void __cdecl std::_Insertion_sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  Skill::CProcessTable::ProcessInfo *i; // esi
  unsigned __int16 m_usSkill_ID; // cx
  Skill::CProcessTable::ProcessInfo *v4; // eax
  std::vector<CTokenlizedFile::ColumnInfo>::iterator v5; // edx

  if ( _First._Myptr != _Last._Myptr )
  {
    for ( i = _First._Myptr + 1; i != _Last._Myptr; ++i )
    {
      m_usSkill_ID = i->m_usSkill_ID;
      if ( i->m_usSkill_ID >= _First._Myptr->m_usSkill_ID )
      {
        v4 = i - 1;
        if ( m_usSkill_ID < i[-1].m_usSkill_ID )
        {
          do
            v5._Myptr = (CTokenlizedFile::ColumnInfo *)v4--;
          while ( m_usSkill_ID < v4->m_usSkill_ID );
          if ( (Skill::CProcessTable::ProcessInfo *)v5._Myptr != i )
            std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
              v5,
              (std::vector<CTokenlizedFile::ColumnInfo>::iterator)i,
              (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&i[1]);
        }
      }
      else if ( _First._Myptr != i && i != &i[1] )
      {
        std::_Rotate<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)_First._Myptr,
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)i,
          (std::vector<CTokenlizedFile::ColumnInfo>::iterator)&i[1]);
      }
    }
  }
}

//----- (00431320) --------------------------------------------------------
void __cdecl std::sort_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  int i; // esi
  int *v3; // eax
  int v4; // ecx
  unsigned int v5; // edx
  Skill::CProcessTable::ProcessInfo v6; // [esp-Ch] [ebp-28h]
  unsigned int v7; // [esp+18h] [ebp-4h]

  for ( i = (char *)_Last._Myptr - (char *)_First._Myptr; i / 12 > 1; i -= 12 )
  {
    v3 = (int *)((char *)&_First._Myptr[-1] + i);
    v4 = *v3;
    v5 = v3[1];
    v7 = v3[2];
    *v3 = *(_DWORD *)&_First._Myptr->m_usSkill_ID;
    v3[1] = (int)_First._Myptr->m_fnProcess;
    v3[2] = (int)_First._Myptr->m_lpProtoType->m_szEffectFileName;
    *(_DWORD *)&v6.m_usSkill_ID = v4;
    *(_QWORD *)&v6.m_fnProcess = __PAIR64__(v7, v5);
    std::_Adjust_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
      _First,
      0,
      (i - 12) / 12,
      v6);
  }
}

//----- (004313C0) --------------------------------------------------------
void __thiscall __noreturn std::vector<Skill::CProcessTable::ProcessInfo>::_Xlen(
        std::vector<Skill::CProcessTable::ProcessInfo> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00431430) --------------------------------------------------------
void __cdecl std::_Sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last,
        int _Ideal)
{
  Skill::CProcessTable::ProcessInfo *Myptr; // ebx
  Skill::CProcessTable::ProcessInfo *v4; // edi
  int v5; // eax
  Skill::CProcessTable::ProcessInfo *v7; // ebp
  std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> _Mid; // [esp+10h] [ebp-8h] BYREF

  Myptr = _First._Myptr;
  v4 = _Last._Myptr;
  v5 = _Last._Myptr - _First._Myptr;
  if ( v5 <= 32 )
  {
LABEL_7:
    if ( v5 > 1 )
      std::_Insertion_sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myptr,
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v4);
  }
  else
  {
    while ( _Ideal > 0 )
    {
      std::_Unguarded_partition<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        &_Mid,
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myptr,
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v4);
      v7 = _Mid.second._Myptr;
      _Ideal = _Ideal / 2 / 2 + _Ideal / 2;
      if ( _Mid.first._Myptr - Myptr >= v4 - _Mid.second._Myptr )
      {
        std::_Sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int>(
          _Mid.second,
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v4,
          _Ideal);
        v4 = _Mid.first._Myptr;
      }
      else
      {
        std::_Sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int>(
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myptr,
          _Mid.first,
          _Ideal);
        Myptr = v7;
      }
      v5 = v4 - Myptr;
      if ( v5 <= 32 )
        goto LABEL_7;
    }
    if ( v4 - Myptr > 1 )
      std::_Make_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myptr,
        (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v4);
    std::sort_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myptr,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)v4);
  }
}
// 431516: conditional instruction was optimized away because eax.4>=21

//----- (00431560) --------------------------------------------------------
void __thiscall std::vector<Skill::CProcessTable::ProcessInfo>::_Insert_n(
        std::vector<Skill::CProcessTable::ProcessInfo> *this,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Where,
        unsigned int _Count,
        const Skill::CProcessTable::ProcessInfo *_Val)
{
  int v5; // ecx
  unsigned __int16 (__cdecl *m_fnProcess)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *); // edx
  Skill::CProcessTable::ProcessInfo *Myfirst; // ebx
  Skill::ProtoType *m_lpProtoType; // eax
  unsigned int v9; // ecx
  int v11; // eax
  int v12; // eax
  unsigned int v13; // ecx
  int v14; // eax
  int v15; // ebx
  Skill::CProcessTable::ProcessInfo *v16; // eax
  char *v17; // edi
  CConsoleCMDFactory::StringCMD *Mylast; // ecx
  Skill::CProcessTable::ProcessInfo *v20; // edi
  CConsoleCMDFactory::StringCMD *v21; // [esp-18h] [ebp-40h]
  CConsoleCMDFactory::StringCMD *v22; // [esp-Ch] [ebp-34h]
  unsigned int v23; // [esp-8h] [ebp-30h]
  int v24; // [esp+0h] [ebp-28h] BYREF
  Skill::CProcessTable::ProcessInfo _Tmp; // [esp+Ch] [ebp-1Ch] BYREF
  int *v26; // [esp+18h] [ebp-10h]
  int v27; // [esp+24h] [ebp-4h]
  CConsoleCMDFactory::StringCMD *_Wherea; // [esp+30h] [ebp+8h]
  CConsoleCMDFactory::StringCMD *_Counta; // [esp+34h] [ebp+Ch]
  CConsoleCMDFactory::StringCMD *_Newvec; // [esp+38h] [ebp+10h]
  CConsoleCMDFactory::StringCMD *_Newveca; // [esp+38h] [ebp+10h]

  v5 = *(_DWORD *)&_Val->m_usSkill_ID;
  m_fnProcess = _Val->m_fnProcess;
  Myfirst = this->_Myfirst;
  m_lpProtoType = _Val->m_lpProtoType;
  v26 = &v24;
  *(_DWORD *)&_Tmp.m_usSkill_ID = v5;
  _Tmp.m_fnProcess = m_fnProcess;
  _Tmp.m_lpProtoType = m_lpProtoType;
  if ( Myfirst )
    v9 = this->_Myend - Myfirst;
  else
    v9 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v11 = this->_Mylast - Myfirst;
    else
      v11 = 0;
    if ( 357913941 - v11 < _Count )
      std::vector<Skill::CProcessTable::ProcessInfo>::_Xlen(this);
    if ( Myfirst )
      v12 = this->_Mylast - Myfirst;
    else
      v12 = 0;
    if ( v9 >= _Count + v12 )
    {
      Mylast = (CConsoleCMDFactory::StringCMD *)this->_Mylast;
      _Newveca = Mylast;
      if ( ((char *)Mylast - (char *)_Where._Myptr) / 12 >= _Count )
      {
        _Wherea = &Mylast[-_Count];
        this->_Mylast = (Skill::CProcessTable::ProcessInfo *)std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                                                               _Wherea,
                                                               Mylast,
                                                               Mylast);
        std::_Copy_backward_opt<CTokenlizedFile::ColumnInfo *,CTokenlizedFile::ColumnInfo *>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          _Wherea,
          _Newveca);
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          (CConsoleCMDFactory::StringCMD *)&_Where._Myptr[_Count],
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      }
      else
      {
        std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          Mylast,
          (CConsoleCMDFactory::StringCMD *)&_Where._Myptr[_Count]);
        v23 = _Count - (this->_Mylast - _Where._Myptr);
        v22 = (CConsoleCMDFactory::StringCMD *)this->_Mylast;
        v27 = 2;
        std::vector<CConsoleCMDFactory::StringCMD>::_Ufill(
          (std::vector<CConsoleCMDFactory::StringCMD> *)this,
          v22,
          v23,
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
        v20 = &this->_Mylast[_Count];
        this->_Mylast = v20;
        std::fill<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo>(
          (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
          (CConsoleCMDFactory::StringCMD *)&v20[-_Count],
          (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      }
    }
    else
    {
      if ( 357913941 - (v9 >> 1) >= v9 )
        v13 = (v9 >> 1) + v9;
      else
        v13 = 0;
      if ( Myfirst )
        v14 = this->_Mylast - Myfirst;
      else
        v14 = 0;
      if ( v13 < _Count + v14 )
        v13 = (unsigned int)std::vector<CTokenlizedFile::ColumnInfo>::size((std::vector<CConsoleCMDFactory::StringCMD> *)this)
            + _Count;
      v15 = v13;
      _Newvec = (CConsoleCMDFactory::StringCMD *)operator new((tagHeader *)(12 * v13));
      v21 = (CConsoleCMDFactory::StringCMD *)this->_Myfirst;
      v27 = 0;
      _Counta = std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
                  v21,
                  (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
                  _Newvec);
      std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        _Counta,
        _Count,
        (const CConsoleCMDFactory::StringCMD *)&_Tmp);
      std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(
        (CConsoleCMDFactory::StringCMD *)_Where._Myptr,
        (CConsoleCMDFactory::StringCMD *)this->_Mylast,
        &_Counta[_Count]);
      v16 = this->_Myfirst;
      if ( v16 )
        v16 = (Skill::CProcessTable::ProcessInfo *)(this->_Mylast - v16);
      v17 = (char *)v16 + _Count;
      if ( this->_Myfirst )
        operator delete(this->_Myfirst);
      this->_Myend = (Skill::CProcessTable::ProcessInfo *)&_Newvec[v15];
      this->_Mylast = (Skill::CProcessTable::ProcessInfo *)&_Newvec[(_DWORD)v17];
      this->_Myfirst = (Skill::CProcessTable::ProcessInfo *)_Newvec;
    }
  }
}

//----- (00431820) --------------------------------------------------------
void __cdecl std::sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last)
{
  std::_Sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int>(_First, _Last, _Last._Myptr - _First._Myptr);
}

//----- (00431850) --------------------------------------------------------
std::vector<Skill::CProcessTable::ProcessInfo>::iterator *__thiscall std::vector<Skill::CProcessTable::ProcessInfo>::insert(
        std::vector<Skill::CProcessTable::ProcessInfo> *this,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator *result,
        std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Where,
        const Skill::CProcessTable::ProcessInfo *_Val)
{
  Skill::CProcessTable::ProcessInfo *Myfirst; // esi
  int v6; // esi
  std::vector<Skill::CProcessTable::ProcessInfo>::iterator *v7; // eax

  Myfirst = this->_Myfirst;
  if ( Myfirst && this->_Mylast - Myfirst )
    v6 = _Where._Myptr - Myfirst;
  else
    v6 = 0;
  std::vector<Skill::CProcessTable::ProcessInfo>::_Insert_n(this, _Where, 1u, _Val);
  v7 = result;
  result->_Myptr = &this->_Myfirst[v6];
  return v7;
}



//----- (004318C0) --------------------------------------------------------
void __thiscall std::vector<Skill::CProcessTable::ProcessInfo>::push_back(
        std::vector<Skill::CProcessTable::ProcessInfo> *this,
        const CConsoleCMDFactory::StringCMD *_Val)
{
  Skill::CProcessTable::ProcessInfo *Myfirst; // edi
  unsigned int v4; // ecx
  Skill::CProcessTable::ProcessInfo *Mylast; // edi

  Myfirst = this->_Myfirst;
  if ( Myfirst )
    v4 = this->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < this->_Myend - Myfirst )
  {
    Mylast = this->_Mylast;
    std::_Uninit_fill_n<Skill::CProcessTable::ProcessInfo *,unsigned int,Skill::CProcessTable::ProcessInfo,std::allocator<Skill::CProcessTable::ProcessInfo>>(
      (CConsoleCMDFactory::StringCMD *)Mylast,
      1u,
      _Val);
    this->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<Skill::CProcessTable::ProcessInfo>::insert(
      this,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator *)&_Val,
      (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)this->_Mylast,
      (const Skill::CProcessTable::ProcessInfo *)_Val);
  }
}

//----- (00431940) --------------------------------------------------------
char __thiscall Skill::CProcessTable::InsertSkill(
        Skill::CProcessTable *this,
        std::vector<Skill::CProcessTable::ProcessInfo> *processVector,
        unsigned __int16 usSkill_ID,
        unsigned __int16 (__cdecl *fnProcess)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))
{
  CSkillMgr::ProtoTypeArray *SkillProtoType; // eax
  Skill::CProcessTable::ProcessInfo _Val; // [esp+4h] [ebp-Ch] BYREF

  SkillProtoType = CSkillMgr::GetSkillProtoType(CSingleton<CSkillMgr>::ms_pSingleton, usSkill_ID);
  if ( SkillProtoType )
  {
    _Val.m_fnProcess = fnProcess;
    _Val.m_usSkill_ID = usSkill_ID;
    _Val.m_lpProtoType = (Skill::ProtoType *)SkillProtoType;
    std::vector<Skill::CProcessTable::ProcessInfo>::push_back(
      processVector,
      (const CConsoleCMDFactory::StringCMD *)&_Val);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "Skill::CProcessTable::InsertSkill",
      aDWorkRylSource_68,
      268,
      (char *)&byte_4DCC78,
      usSkill_ID);
    return 0;
  }
}

//----- (004319B0) --------------------------------------------------------
char __thiscall Skill::CProcessTable::Initialize(Skill::CProcessTable *this)
{
  unsigned __int16 v3; // ax
  int v4; // edi
  char *v5; // eax
  Skill::CProcessTable::ProcessInfo *v6; // ebp
  Skill::CProcessTable::ProcessInfo *Mylast; // edi
  Skill::CProcessTable::ProcessInfo *Myfirst; // ebx
  std::vector<Skill::CProcessTable::ProcessInfo> tempProcessInfo; // [esp+Ch] [ebp-1Ch] BYREF
  int v10; // [esp+24h] [ebp-4h]

  memset(&tempProcessInfo._Myfirst, 0, 12);
  v10 = 0;
  if ( Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8101u, Skill::CFunctions::Net)
    && Skill::CProcessTable::InsertSkill(
         this,
         &tempProcessInfo,
         0x8102u,
         (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::SwordMastery)
    && Skill::CProcessTable::InsertSkill(
         this,
         &tempProcessInfo,
         0x8103u,
         (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::AxeMastery) )
  {
    if ( Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8105u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BluntMastery)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8104u, Skill::CFunctions::HardHit)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8701u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8703u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8801u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9803u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8205u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::NeedleSpit)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8302u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BloodyMana)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8303u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::VampiricTouch)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8304u, Skill::CFunctions::ManaShell)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8305u, Skill::CFunctions::Grease)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8401u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Purification)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8404u, Skill::CFunctions::Encourage)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8405u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FirstAid)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8406u, Skill::CFunctions::HammerOfLight)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8501u, Skill::CFunctions::Charging)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8503u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::SharedPain)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8504u, Skill::CFunctions::FullSwing)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8601u, Skill::CFunctions::Blaze)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8602u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BattleSong)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8603u, Skill::CFunctions::ChainAction)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8702u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BackStab)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8704u, Skill::CFunctions::Stealth)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8705u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::DaggerMastery)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8707u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Disenchant)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8B07u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Disenchant)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8803u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::AimedShot)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8804u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::DualShot)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8805u, Skill::CFunctions::Camouflage)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8901u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Recall)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8902u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireBolt)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8903u, Skill::CFunctions::LightningArrow)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8904u, Skill::CFunctions::FrostBolt)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8905u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::DeathRay)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8A01u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Disenchant)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8A07u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Recall)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8A02u, Skill::CFunctions::Entangle)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8A04u, Skill::CFunctions::LowerStrength)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8A05u, Skill::CFunctions::EnchantWeapon)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8A06u, Skill::CFunctions::Shatter)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8B01u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Resurrection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8B02u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::MaintenanceChant)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8B03u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::AccelerationChant)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8B04u, Skill::CFunctions::BrightArmor)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8B06u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::ManaFlow)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8C01u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Resurrection)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x8C02u, Skill::CFunctions::Dazzle)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8C03u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::CureWounds)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8C04u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::WoundsCrafting)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8C05u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::CureLight)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8C06u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::WoundsMake)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9102u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::CrushWeapon)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9103u, Skill::CFunctions::HardenSkin)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9104u, Skill::CFunctions::HardHit)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9105u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Blade)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9106u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::SplitLife)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9107u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Toughness)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9201u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Dispel)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9202u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::MagicMissile)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9204u, Skill::CFunctions::Flexibility)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9206u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BloodyMana)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9207u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::ClawMastery)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9208u, Skill::CFunctions::Grease)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9209u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::LifeAura)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9210u, Skill::CFunctions::Encourage)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9211u, Skill::CFunctions::Regeneration)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9301u, Skill::CFunctions::Net)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9308u, Skill::CFunctions::Charging)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9302u, Skill::CFunctions::Guard)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9305u, Skill::CFunctions::FastHit)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9307u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::ManaConvert)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9401u, Skill::CFunctions::Net)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9408u, Skill::CFunctions::Blaze)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9402u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::VampiricTouch)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9404u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BattleSong)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9407u, Skill::CFunctions::RingGeyser)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9501u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9502u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireRing)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9504u, Skill::CFunctions::Camouflage)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9506u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Blast)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9601u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Recall)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9602u, Skill::CFunctions::Rot)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9603u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Shock)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9604u, Skill::CFunctions::Shackle)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9605u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Crevice)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9606u, Skill::CFunctions::EnchantWeapon)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9607u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::SummonKindling)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9701u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Resurrection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9702u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Purification)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9703u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::WoundsCrafting)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9704u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::CureWounds)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9706u, Skill::CFunctions::Flash)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9707u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::WoundsMake)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9801u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::Detection)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9802u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::WoundsBlast)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9804u, Skill::CFunctions::Stealth)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9805u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::DaggerFire)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8D02u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::UseFood)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8D03u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::UseFood2)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8D04u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::UseFood2)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9902u, Skill::CFunctions::StrengthPotion)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9903u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::RefreshmentPotion)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9904u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::HealingPotion)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9905u, Skill::CFunctions::DefencePotion)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9906u, Skill::CFunctions::DisenchantPotion)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9907u, Skill::CFunctions::MagicPotion)
      && Skill::CProcessTable::InsertSkill(this, &tempProcessInfo, 0x9908u, Skill::CFunctions::LightningPotion)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x9909u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::MoonCake)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x990Au,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::RedMoonCake)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x990Bu,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::BlueMoonCake)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F01u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F02u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F03u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F04u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F05u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F06u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker)
      && Skill::CProcessTable::InsertSkill(
           this,
           &tempProcessInfo,
           0x8F07u,
           (unsigned __int16 (__cdecl *)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *))Skill::CFunctions::FireCracker) )
    {
      v3 = (unsigned __int16)std::vector<CTokenlizedFile::ColumnInfo>::size((std::vector<CConsoleCMDFactory::StringCMD> *)&tempProcessInfo);
      v4 = v3;
      this->m_usProcessInfo = v3;
      v5 = (char *)operator new[](12 * v3);
      v6 = (Skill::CProcessTable::ProcessInfo *)v5;
      LOBYTE(v10) = 1;
      if ( v5 )
        `vector constructor iterator'(
          v5,
          0xCu,
          v4,
          (void *(__thiscall *)(void *))Skill::CProcessTable::ProcessInfo::ProcessInfo);
      else
        v6 = 0;
      LOBYTE(v10) = 0;
      this->m_fnProcessTable = v6;
      if ( v6 )
      {
        Mylast = tempProcessInfo._Mylast;
        Myfirst = tempProcessInfo._Myfirst;
        std::sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)tempProcessInfo._Myfirst,
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)tempProcessInfo._Mylast);
        std::copy<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Myfirst,
          (std::vector<Skill::CProcessTable::ProcessInfo>::iterator)Mylast,
          this->m_fnProcessTable);
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&tempProcessInfo);
        return 1;
      }
      else
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "Skill::CProcessTable::Initialize",
          aDWorkRylSource_68,
          252,
          (char *)&byte_4DCCF4);
        std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&tempProcessInfo);
        return 0;
      }
    }
    else
    {
      std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>((CBanList *)&tempProcessInfo);
      return 0;
    }
  }
  else
  {
    if ( tempProcessInfo._Myfirst )
      operator delete(tempProcessInfo._Myfirst);
    return 0;
  }
}

//----- (00432840) --------------------------------------------------------
void __thiscall std::locale::facet::_Incref(std::locale::facet *this)
{
  unsigned int Refs; // eax
  std::_Lockit _Lock; // [esp+4h] [ebp-4h] BYREF

  std::_Lockit::_Lockit(&_Lock, 0);
  Refs = this->_Refs;
  if ( Refs != -1 )
    this->_Refs = Refs + 1;
  std::_Lockit::~_Lockit(&_Lock);
}

//----- (00432870) --------------------------------------------------------
std::locale::facet *__thiscall std::locale::facet::_Decref(std::locale::facet *this)
{
  unsigned int Refs; // eax
  std::locale::facet *v3; // esi
  std::_Lockit _Lock; // [esp+4h] [ebp-4h] BYREF

  std::_Lockit::_Lockit(&_Lock, 0);
  Refs = this->_Refs;
  if ( Refs && Refs != -1 )
    this->_Refs = Refs - 1;
  v3 = this->_Refs != 0 ? 0 : this;
  std::_Lockit::~_Lockit(&_Lock);
  return v3;
}

//----- (004328B0) --------------------------------------------------------
void __thiscall std::locale::~locale(std::locale *this)
{
  std::locale::_Locimp *Ptr; // ecx
  std::locale::facet *v2; // eax

  Ptr = this->_Ptr;
  if ( Ptr )
  {
    v2 = std::locale::facet::_Decref(Ptr);
    if ( v2 )
      ((void (__thiscall *)(std::locale::facet *, int))v2->~std::locale::facet)(v2, 1);
  }
}

//----- (004328D0) --------------------------------------------------------
std::locale::facet *__thiscall std::locale::facet::`scalar deleting destructor'(std::locale::facet *this, char a2)
{
  this->__vftable = (std::locale::facet_vtbl *)&std::locale::facet::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4FC98C: using guessed type void *std::locale::facet::`vftable';

//----- (004328F0) --------------------------------------------------------
char __cdecl CConsoleCMDSingleton<CCMDReloadSetup>::Destroy()
{
  return 1;
}

//----- (00432900) --------------------------------------------------------
std::locale *__thiscall std::ios_base::getloc(std::ios_base *this, std::locale *result)
{
  std::locale::_Locimp *Ptr; // esi
  std::locale *v3; // edi
  unsigned int Refs; // eax

  Ptr = this->_Ploc->_Ptr;
  v3 = result;
  result->_Ptr = Ptr;
  std::_Lockit::_Lockit((std::_Lockit *)&result, 0);
  Refs = Ptr->_Refs;
  if ( Refs != -1 )
    Ptr->_Refs = Refs + 1;
  std::_Lockit::~_Lockit((std::_Lockit *)&result);
  return v3;
}

//----- (00432940) --------------------------------------------------------
std::ios_base *__thiscall std::ios_base::`vector deleting destructor'(std::ios_base *this, char a2)
{
  std::ios_base::~ios_base(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00432960) --------------------------------------------------------
unsigned int __cdecl Math::Convert::StrToHex32(const char *szSrc)
{
  char v1; // al
  int v2; // ecx
  char v3; // al
  int v4; // ecx
  int v5; // eax
  char v6; // cl
  int v7; // esi
  char v8; // cl
  int v9; // esi
  int v10; // eax
  char v11; // cl
  int v12; // esi
  char v13; // cl
  int v14; // esi
  int v15; // eax
  char v16; // cl
  int v17; // esi
  char v18; // cl
  int v19; // esi

  v1 = szSrc[2];
  if ( v1 < 48 || v1 > 57 )
  {
    if ( v1 < 65 || v1 > 70 )
    {
      if ( v1 >= 97 && v1 <= 102 )
        v1 -= 87;
    }
    else
    {
      v1 -= 55;
    }
  }
  else
  {
    v1 -= 48;
  }
  v2 = v1;
  v3 = szSrc[3];
  v4 = v2 << 28;
  if ( v3 < 48 || v3 > 57 )
  {
    if ( v3 < 65 || v3 > 70 )
    {
      if ( v3 >= 97 && v3 <= 102 )
        v3 -= 87;
    }
    else
    {
      v3 -= 55;
    }
  }
  else
  {
    v3 -= 48;
  }
  v5 = v4 + (v3 << 24);
  v6 = szSrc[4];
  if ( v6 < 48 || v6 > 57 )
  {
    if ( v6 < 65 || v6 > 70 )
    {
      if ( v6 >= 97 && v6 <= 102 )
        v6 -= 87;
    }
    else
    {
      v6 -= 55;
    }
  }
  else
  {
    v6 -= 48;
  }
  v7 = v6;
  v8 = szSrc[5];
  v9 = v7 << 20;
  if ( v8 < 48 || v8 > 57 )
  {
    if ( v8 < 65 || v8 > 70 )
    {
      if ( v8 >= 97 && v8 <= 102 )
        v8 -= 87;
    }
    else
    {
      v8 -= 55;
    }
  }
  else
  {
    v8 -= 48;
  }
  v10 = v9 + (v8 << 16) + v5;
  v11 = szSrc[6];
  if ( v11 < 48 || v11 > 57 )
  {
    if ( v11 < 65 || v11 > 70 )
    {
      if ( v11 >= 97 && v11 <= 102 )
        v11 -= 87;
    }
    else
    {
      v11 -= 55;
    }
  }
  else
  {
    v11 -= 48;
  }
  v12 = v11;
  v13 = szSrc[7];
  v14 = v12 << 12;
  if ( v13 < 48 || v13 > 57 )
  {
    if ( v13 < 65 || v13 > 70 )
    {
      if ( v13 >= 97 && v13 <= 102 )
        v13 -= 87;
    }
    else
    {
      v13 -= 55;
    }
  }
  else
  {
    v13 -= 48;
  }
  v15 = v14 + (v13 << 8) + v10;
  v16 = szSrc[8];
  if ( v16 < 48 || v16 > 57 )
  {
    if ( v16 < 65 || v16 > 70 )
    {
      if ( v16 >= 97 && v16 <= 102 )
        v16 -= 87;
    }
    else
    {
      v16 -= 55;
    }
  }
  else
  {
    v16 -= 48;
  }
  v17 = v16;
  v18 = szSrc[9];
  v19 = 16 * v17;
  if ( v18 >= 48 && v18 <= 57 )
    return v19 + (char)(v18 - 48) + v15;
  if ( v18 >= 65 && v18 <= 70 )
    return v19 + (char)(v18 - 55) + v15;
  if ( v18 >= 97 && v18 <= 102 )
    v18 -= 87;
  return v19 + v18 + v15;
}

//----- (00432B20) --------------------------------------------------------
int __cdecl Math::Convert::Atoi(char *szSrc)
{
  int v1; // eax

  _mbsnbcmp((unsigned __int8 *)szSrc, "0x", 2u);
  if ( v1 )
    return atol(szSrc);
  else
    return Math::Convert::StrToHex32(szSrc);
}
// 432B38: variable 'v1' is possibly undefined

//----- (00432B50) --------------------------------------------------------
bool __thiscall CCellManager::CheckPositionInZone(CCellManager *this, Position Pos)
{
  CServerSetup *Instance; // eax
  char ServerZone; // al
  Position ZoneMinPos[15]; // [esp+0h] [ebp-168h] BYREF
  Position ZoneMaxPos[15]; // [esp+B4h] [ebp-B4h] BYREF

  memset(ZoneMinPos, 0, 12);
  ZoneMinPos[1].m_fPointX = 473.0;
  ZoneMinPos[1].m_fPointY = 0.0;
  ZoneMinPos[1].m_fPointZ = 184.0;
  ZoneMinPos[2].m_fPointX = 315.0;
  ZoneMinPos[2].m_fPointY = 0.0;
  ZoneMinPos[2].m_fPointZ = 315.0;
  ZoneMinPos[3].m_fPointX = 591.0;
  ZoneMinPos[3].m_fPointY = 0.0;
  ZoneMinPos[3].m_fPointZ = 575.0;
  ZoneMinPos[4].m_fPointX = 484.0;
  ZoneMinPos[4].m_fPointY = 0.0;
  ZoneMinPos[4].m_fPointZ = 452.0;
  ZoneMinPos[5].m_fPointX = 450.0;
  ZoneMinPos[5].m_fPointY = 0.0;
  ZoneMinPos[5].m_fPointZ = 450.0;
  ZoneMinPos[6].m_fPointX = 591.0;
  ZoneMinPos[6].m_fPointY = 0.0;
  ZoneMinPos[6].m_fPointZ = 575.0;
  memset(&ZoneMinPos[7], 0, sizeof(Position));
  ZoneMinPos[8].m_fPointX = 630.0;
  ZoneMinPos[8].m_fPointY = 0.0;
  ZoneMinPos[8].m_fPointZ = 630.0;
  memset(&ZoneMinPos[9], 0, sizeof(Position));
  ZoneMinPos[10].m_fPointX = 945.0;
  ZoneMinPos[10].m_fPointY = 0.0;
  ZoneMinPos[10].m_fPointZ = 945.0;
  ZoneMinPos[11].m_fPointX = 1574.0;
  ZoneMinPos[11].m_fPointY = 0.0;
  ZoneMinPos[11].m_fPointZ = 954.0;
  memset(&ZoneMinPos[12], 0, sizeof(Position));
  ZoneMinPos[13].m_fPointX = 645.0;
  ZoneMinPos[13].m_fPointY = 0.0;
  ZoneMinPos[13].m_fPointZ = 641.0;
  ZoneMinPos[14].m_fPointX = 1574.0;
  ZoneMinPos[14].m_fPointY = 0.0;
  ZoneMinPos[14].m_fPointZ = 954.0;
  memset(ZoneMaxPos, 0, 12);
  ZoneMaxPos[1].m_fPointX = 3387.0;
  ZoneMaxPos[1].m_fPointY = 0.0;
  ZoneMaxPos[1].m_fPointZ = 3064.0;
  ZoneMaxPos[2].m_fPointX = 3781.0;
  ZoneMaxPos[2].m_fPointY = 0.0;
  ZoneMaxPos[2].m_fPointZ = 3781.0;
  ZoneMaxPos[3].m_fPointX = 2227.0;
  ZoneMaxPos[3].m_fPointY = 0.0;
  ZoneMaxPos[3].m_fPointZ = 2250.0;
  ZoneMaxPos[4].m_fPointX = 2705.0;
  ZoneMaxPos[4].m_fPointY = 0.0;
  ZoneMaxPos[4].m_fPointZ = 2706.0;
  ZoneMaxPos[5].m_fPointX = 3326.0;
  ZoneMaxPos[5].m_fPointY = 0.0;
  ZoneMaxPos[5].m_fPointZ = 3347.0;
  ZoneMaxPos[6].m_fPointX = 2227.0;
  ZoneMaxPos[6].m_fPointY = 0.0;
  ZoneMaxPos[6].m_fPointZ = 2250.0;
  memset(&ZoneMaxPos[7], 0, sizeof(Position));
  ZoneMaxPos[8].m_fPointX = 3465.0;
  ZoneMaxPos[8].m_fPointY = 0.0;
  ZoneMaxPos[8].m_fPointZ = 3465.0;
  memset(&ZoneMaxPos[9], 0, sizeof(Position));
  ZoneMaxPos[10].m_fPointX = 1259.0;
  ZoneMaxPos[10].m_fPointY = 0.0;
  ZoneMaxPos[10].m_fPointZ = 1237.0;
  ZoneMaxPos[11].m_fPointX = 2250.0;
  ZoneMaxPos[11].m_fPointY = 0.0;
  ZoneMaxPos[11].m_fPointZ = 1884.0;
  memset(&ZoneMaxPos[12], 0, sizeof(Position));
  ZoneMaxPos[13].m_fPointX = 1523.0;
  ZoneMaxPos[13].m_fPointY = 0.0;
  ZoneMaxPos[13].m_fPointZ = 1529.0;
  ZoneMaxPos[14].m_fPointX = 2250.0;
  ZoneMaxPos[14].m_fPointY = 0.0;
  ZoneMaxPos[14].m_fPointZ = 1884.0;
  Instance = CServerSetup::GetInstance();
  ServerZone = CServerSetup::GetServerZone(Instance);
  return Pos.m_fPointX >= (double)ZoneMinPos[ServerZone].m_fPointX
      && Pos.m_fPointX <= (double)ZoneMaxPos[ServerZone].m_fPointX
      && Pos.m_fPointZ >= (double)ZoneMinPos[ServerZone].m_fPointZ
      && Pos.m_fPointZ <= (double)ZoneMaxPos[ServerZone].m_fPointZ;
}

//----- (00432F50) --------------------------------------------------------
char __thiscall CCellManager::CreateCell(CCellManager *this)
{
  CServerSetup *Instance; // eax
  VirtualArea::CVirtualAreaMgr *v3; // eax
  int v4; // esi
  char *v5; // eax
  CCell *v6; // edi
  CCell *v7; // eax
  unsigned __int8 v9; // dl
  int v10; // eax
  int v11; // ecx
  CCell *v12; // eax
  int v13; // ecx
  int v14; // edi
  int v15; // esi
  int v16; // ebp
  int v17; // ebp
  CCell *v18; // ecx
  CCell *v19; // eax
  CCell *v20; // [esp-8h] [ebp-24h]
  int nZ; // [esp+Ch] [ebp-10h]

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 11 )
  {
    CCell::ms_CellSize = 70;
    v3 = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea::CVirtualAreaMgr::CreateBGServer(v3);
  }
  v4 = CCell::ms_CellSize * CCell::ms_CellSize;
  v5 = (char *)operator new[](116 * v4 + 4);
  if ( v5 )
  {
    v6 = (CCell *)(v5 + 4);
    *(_DWORD *)v5 = v4;
    `eh vector constructor iterator'(
      v5 + 4,
      0x74u,
      v4,
      (void (__thiscall *)(void *))CCell::CCell,
      (void (__thiscall *)(void *))CCell::~CCell);
    v7 = v6;
  }
  else
  {
    v7 = 0;
  }
  this->m_CellData = v7;
  if ( v7 )
  {
    v9 = CCell::ms_CellSize;
    v10 = CCell::ms_CellSize;
    nZ = 0;
    if ( CCell::ms_CellSize )
    {
      do
      {
        v11 = 0;
        if ( v10 > 0 )
        {
          do
          {
            v12 = &this->m_CellData[v11 + nZ * v10];
            v12->m_cIndexX = v11;
            v12->m_cIndexZ = nZ;
            v9 = CCell::ms_CellSize;
            v10 = CCell::ms_CellSize;
            ++v11;
          }
          while ( v11 < CCell::ms_CellSize );
        }
        v10 = v9;
        ++nZ;
      }
      while ( nZ < v9 );
    }
    v13 = v9;
    v14 = 0;
    if ( v9 )
    {
      do
      {
        v15 = 0;
        if ( v13 > 0 )
        {
          do
          {
            if ( v14 > 0 )
            {
              v16 = v14 - 1;
              CCell::SetConnectCell(&this->m_CellData[v15 + v14 * v13], 1u, &this->m_CellData[v15 + v13 * (v14 - 1)]);
              if ( v15 > 0 )
                CCell::SetConnectCell(
                  &this->m_CellData[v15 + v14 * CCell::ms_CellSize],
                  5u,
                  &this->m_CellData[v15 - 1 + CCell::ms_CellSize * v16]);
              v9 = CCell::ms_CellSize;
              if ( v15 < CCell::ms_CellSize - 1 )
              {
                CCell::SetConnectCell(
                  &this->m_CellData[v15 + v14 * CCell::ms_CellSize],
                  6u,
                  &this->m_CellData[v15 + 1 + CCell::ms_CellSize * v16]);
                v9 = CCell::ms_CellSize;
              }
            }
            if ( v14 < v9 - 1 )
            {
              v17 = v14 + 1;
              CCell::SetConnectCell(&this->m_CellData[v15 + v14 * v9], 2u, &this->m_CellData[v15 + v9 * (v14 + 1)]);
              if ( v15 > 0 )
                CCell::SetConnectCell(
                  &this->m_CellData[v15 + v14 * CCell::ms_CellSize],
                  7u,
                  &this->m_CellData[v15 - 1 + CCell::ms_CellSize * v17]);
              v9 = CCell::ms_CellSize;
              if ( v15 < CCell::ms_CellSize - 1 )
              {
                CCell::SetConnectCell(
                  &this->m_CellData[v15 + v14 * CCell::ms_CellSize],
                  8u,
                  &this->m_CellData[v15 + 1 + CCell::ms_CellSize * v17]);
                v9 = CCell::ms_CellSize;
              }
            }
            if ( v15 > 0 )
            {
              v18 = &this->m_CellData[v15 + v14 * v9];
              CCell::SetConnectCell(v18, 3u, v18 - 1);
              v9 = CCell::ms_CellSize;
            }
            if ( v15 < v9 - 1 )
            {
              v19 = &this->m_CellData[v15 + v14 * v9];
              CCell::SetConnectCell(v19, 4u, v19 + 1);
              v9 = CCell::ms_CellSize;
            }
            v20 = &this->m_CellData[v15 + v14 * v9];
            CCell::SetConnectCell(v20, 0, v20);
            v9 = CCell::ms_CellSize;
            v13 = CCell::ms_CellSize;
            ++v15;
          }
          while ( v15 < CCell::ms_CellSize );
        }
        v13 = v9;
        ++v14;
      }
      while ( v14 < v9 );
    }
    return 1;
  }
  else
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::CreateCell", aDWorkRylSource_49, 238, (char *)&byte_4DCDF0);
    return 0;
  }
}

//----- (00433250) --------------------------------------------------------
char __thiscall CCellManager::SummonMonster(CCellManager *this, int nKID, Position Pos, CCharacter *lpMaster)
{
  CCreatureManager *Instance; // eax
  CMonster *m_lpSummonee; // ecx
  CSummonMonster *v8; // eax
  CMonster *v9; // eax
  CMonster *v10; // esi
  CCreatureManager *v11; // eax
  unsigned int m_dwCID; // [esp-Ch] [ebp-44h]
  unsigned int v13; // [esp-4h] [ebp-3Ch]
  CMonster::MonsterCreateInfo tempInfo; // [esp+Ch] [ebp-2Ch] BYREF
  int v15; // [esp+34h] [ebp-4h]

  v13 = nKID + ((this->m_usSummonCount + 40960) << 16);
  memset(&tempInfo, 0, 12);
  memset(&tempInfo.m_dwPID, 0, 13);
  tempInfo.m_wRespawnArea = 0;
  tempInfo.m_dwCID = v13;
  Instance = CCreatureManager::GetInstance();
  if ( CCreatureManager::GetCreature(Instance, v13) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::SummonMonster",
      aDWorkRylSource_49,
      609,
      (char *)&byte_4DCE54);
    return 0;
  }
  tempInfo.m_nKID = nKID;
  tempInfo.m_Pos = Pos;
  if ( lpMaster )
  {
    m_lpSummonee = lpMaster->m_lpSummonee;
    if ( m_lpSummonee )
      m_lpSummonee->Dead(m_lpSummonee, 0);
  }
  v8 = (CSummonMonster *)operator new((tagHeader *)0x308);
  v15 = 0;
  if ( v8 )
  {
    CSummonMonster::CSummonMonster(v8, &tempInfo, lpMaster);
    v10 = v9;
  }
  else
  {
    v10 = 0;
  }
  v15 = -1;
  if ( !v10 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::SummonMonster",
      aDWorkRylSource_49,
      629,
      (char *)&byte_4DCE54);
    return 0;
  }
  if ( !CMonster::InitMonster(v10, &tempInfo.m_Pos, LOGINOUT) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::SummonMonster", aDWorkRylSource_49, 634, byte_4DCE10);
    return 0;
  }
  v11 = CCreatureManager::GetInstance();
  CCreatureManager::AddCreature(v11, (CNPC *)v10);
  if ( ++this->m_usSummonCount == 0x3FFF )
    this->m_usSummonCount = 0;
  if ( lpMaster )
  {
    m_dwCID = lpMaster->m_dwCID;
    lpMaster->m_lpSummonee = v10;
    GameClientSendPacket::SendCharSummon(m_dwCID, v10);
  }
  else
  {
    CMonster::SendMove(v10, 1u);
  }
  return 1;
}
// 433357: variable 'v9' is possibly undefined

//----- (00433420) --------------------------------------------------------
CCell *__thiscall CCellManager::GetCell(
        CCellManager *this,
        unsigned __int16 wMapIndex,
        unsigned __int8 cCellX,
        unsigned __int8 cCellZ)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  VirtualArea::CVirtualArea *v6; // esi
  unsigned __int16 Width; // ax
  int Height; // [esp-4h] [ebp-10h]

  if ( wMapIndex )
  {
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, wMapIndex);
    v6 = VirtualArea;
    if ( VirtualArea )
    {
      if ( cCellX >= VirtualArea::CVirtualArea::GetWidth(VirtualArea)
        || cCellZ >= VirtualArea::CVirtualArea::GetHeight(v6) )
      {
        Height = VirtualArea::CVirtualArea::GetHeight(v6);
        Width = VirtualArea::CVirtualArea::GetWidth(v6);
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCellManager::GetCell",
          aDWorkRylSource_49,
          673,
          aMapindex0x04xX,
          wMapIndex,
          cCellX,
          Width,
          cCellZ,
          Height);
        return 0;
      }
      else
      {
        return &v6->m_CellData[cCellX + cCellZ * VirtualArea::CVirtualArea::GetWidth(v6)];
      }
    }
    else
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCellManager::GetCell",
        aDWorkRylSource_49,
        666,
        aMapindex0x04x,
        wMapIndex);
      return 0;
    }
  }
  else if ( cCellX >= CCell::ms_CellSize || cCellZ >= CCell::ms_CellSize )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::GetCell", aDWorkRylSource_49, 683, aXDZD, cCellX, cCellZ);
    return 0;
  }
  else
  {
    return &this->m_CellData[cCellX + cCellZ * CCell::ms_CellSize];
  }
}

//----- (00433570) --------------------------------------------------------
CCell *__thiscall CCellManager::GetCell(
        CCellManager *this,
        unsigned __int16 wMapIndex,
        unsigned int dwPosX,
        unsigned int dwPosY,
        unsigned int dwPosZ)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  VirtualArea::CVirtualArea *v7; // esi
  int v9; // ebp
  int v10; // ebp
  unsigned int v11; // edi
  unsigned int v12; // ebx
  unsigned int v13; // esi

  if ( wMapIndex )
  {
    Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
    VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(Instance, wMapIndex);
    v7 = VirtualArea;
    if ( !VirtualArea )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCellManager::GetCell",
        aDWorkRylSource_49,
        702,
        aMapindex0x04x,
        wMapIndex);
      return 0;
    }
    if ( dwPosX >= VirtualArea::CVirtualArea::GetStartX(VirtualArea)
      && dwPosZ >= VirtualArea::CVirtualArea::GetStartZ(v7) )
    {
      v9 = 32 * VirtualArea::CVirtualArea::GetWidth(v7);
      if ( dwPosX <= (unsigned int)VirtualArea::CVirtualArea::GetStartX(v7) + v9 )
      {
        v10 = 32 * VirtualArea::CVirtualArea::GetHeight(v7);
        if ( dwPosZ <= (unsigned int)VirtualArea::CVirtualArea::GetStartZ(v7) + v10 )
        {
          v11 = dwPosX - VirtualArea::CVirtualArea::GetStartX(v7);
          v12 = dwPosZ - VirtualArea::CVirtualArea::GetStartZ(v7);
          return &v7->m_CellData[(v11 >> 5) + (v12 >> 5) * VirtualArea::CVirtualArea::GetWidth(v7)];
        }
      }
    }
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::GetCell",
      aDWorkRylSource_49,
      711,
      aXDYDZD,
      dwPosX,
      dwPosY,
      dwPosZ);
  }
  else
  {
    v13 = 32 * CCell::ms_CellSize;
    if ( dwPosX <= v13 && dwPosZ <= v13 )
      return &this->m_CellData[(dwPosX >> 5) + CCell::ms_CellSize * (dwPosZ >> 5)];
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::GetCell",
      aDWorkRylSource_49,
      725,
      aXDYDZD,
      dwPosX,
      dwPosY,
      dwPosZ);
  }
  return 0;
}

//----- (004336E0) --------------------------------------------------------
CCell *__thiscall CCellManager::GetCell(CCellManager *this, unsigned __int64 nItemID)
{
  return CCellManager::GetCell(this, WORD2(nItemID), BYTE6(nItemID), HIBYTE(nItemID));
}

//----- (00433700) --------------------------------------------------------
CBuffer *__thiscall CCellManager::ReallocateBuffer(
        CCellManager *this,
        CBuffer *lpOldBuffer,
        unsigned int dwReserveSize)
{
  CBuffer *result; // eax
  CBufferFactory *m_lpBufferFactory; // ecx

  result = lpOldBuffer;
  if ( lpOldBuffer )
  {
    lpOldBuffer->bufferfactory_->Release(lpOldBuffer->bufferfactory_, lpOldBuffer);
    result = 0;
  }
  m_lpBufferFactory = this->m_lpBufferFactory;
  if ( m_lpBufferFactory )
    return m_lpBufferFactory->Create(m_lpBufferFactory, dwReserveSize);
  return result;
}

//----- (00433730) --------------------------------------------------------
CBuffer *__thiscall CCellManager::GetBroadCastBuffer(CCellManager *this, unsigned int dwReserveSize)
{
  CBuffer *m_lpBroadCastBuffer; // eax
  CBufferFactory *m_lpBufferFactory; // ecx
  CBuffer *v5; // eax
  char *internal_buffer; // ecx

  m_lpBroadCastBuffer = this->m_lpBroadCastBuffer;
  if ( m_lpBroadCastBuffer )
  {
    if ( m_lpBroadCastBuffer->wr_ptr_ - m_lpBroadCastBuffer->rd_ptr_ >= dwReserveSize )
      goto LABEL_7;
    m_lpBroadCastBuffer->bufferfactory_->Release(m_lpBroadCastBuffer->bufferfactory_, m_lpBroadCastBuffer);
    m_lpBroadCastBuffer = 0;
  }
  m_lpBufferFactory = this->m_lpBufferFactory;
  if ( m_lpBufferFactory )
    m_lpBroadCastBuffer = m_lpBufferFactory->Create(m_lpBufferFactory, 2 * dwReserveSize);
  this->m_lpBroadCastBuffer = m_lpBroadCastBuffer;
LABEL_7:
  v5 = this->m_lpBroadCastBuffer;
  if ( v5 )
  {
    internal_buffer = v5->internal_buffer_;
    v5->wr_ptr_ = v5->internal_buffer_;
    this->m_lpBroadCastBuffer->rd_ptr_ = internal_buffer;
  }
  return this->m_lpBroadCastBuffer;
}
// 43374B: conditional instruction was optimized away because eax.4!=0

//----- (00433790) --------------------------------------------------------
CBuffer *__thiscall CCellManager::GetBroadCastCompressBuffer(CCellManager *this, unsigned int dwReserveSize)
{
  CBuffer *m_lpBroadCastCompressBuffer; // eax
  CBufferFactory *m_lpBufferFactory; // ecx
  CBuffer *v5; // eax
  char *internal_buffer; // ecx

  m_lpBroadCastCompressBuffer = this->m_lpBroadCastCompressBuffer;
  if ( !m_lpBroadCastCompressBuffer
    || m_lpBroadCastCompressBuffer->wr_ptr_ - m_lpBroadCastCompressBuffer->rd_ptr_ < dwReserveSize )
  {
    if ( m_lpBroadCastCompressBuffer )
    {
      m_lpBroadCastCompressBuffer->bufferfactory_->Release(
        m_lpBroadCastCompressBuffer->bufferfactory_,
        m_lpBroadCastCompressBuffer);
      m_lpBroadCastCompressBuffer = 0;
    }
    m_lpBufferFactory = this->m_lpBufferFactory;
    if ( m_lpBufferFactory )
      m_lpBroadCastCompressBuffer = m_lpBufferFactory->Create(
                                      m_lpBufferFactory,
                                      ((2 * dwReserveSize) >> 6) + 2 * dwReserveSize + 19);
    this->m_lpBroadCastCompressBuffer = m_lpBroadCastCompressBuffer;
  }
  v5 = this->m_lpBroadCastCompressBuffer;
  if ( v5 )
  {
    internal_buffer = v5->internal_buffer_;
    v5->wr_ptr_ = v5->internal_buffer_;
    this->m_lpBroadCastCompressBuffer->rd_ptr_ = internal_buffer;
  }
  return this->m_lpBroadCastCompressBuffer;
}

//----- (004337F0) --------------------------------------------------------
void __thiscall std::ios::~ios<char,std::char_traits<char>>(std::ios *this)
{
  this->__vftable = (std::ios_vtbl *)&std::ios::`vftable';
  std::ios_base::~ios_base(this);
}
// 4FCA14: using guessed type void *std::ios::`vftable';

//----- (00433800) --------------------------------------------------------
void __thiscall std::istream::~istream<char,std::char_traits<char>>(std::istream *this)
{
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)&this[-1].gap8[44] + 4) - 8] = &std::istream::`vftable';
}
// 4DCF70: using guessed type void *std::istream::`vftable';

//----- (00433810) --------------------------------------------------------
void __thiscall std::iostream::~basic_iostream<char,std::char_traits<char>>(std::iostream *this)
{
  char *bytes_3c; // eax

  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)&this[-1].gap8[44] + 4) - 12] = &std::iostream::`vftable';
  if ( this == (std::iostream *)12 )
    bytes_3c = 0;
  else
    bytes_3c = this[-1]._bytes_3c;
  *(_DWORD *)&bytes_3c[*(_DWORD *)(*(_DWORD *)bytes_3c + 4)] = &std::ostream::`vftable';
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)&this[-1].gap8[44] + 4) - 12] = &std::istream::`vftable';
}
// 4DCF70: using guessed type void *std::istream::`vftable';
// 4DCF74: using guessed type void *std::iostream::`vftable';
// 4FCA0C: using guessed type void *std::ostream::`vftable';

//----- (00433850) --------------------------------------------------------
std::ios *__thiscall std::ios::`scalar deleting destructor'(std::ios *this, char a2)
{
  std::ios::~ios<char,std::char_traits<char>>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00433870) --------------------------------------------------------
std::locale *__thiscall std::streambuf::getloc(std::streambuf *this, std::locale *result)
{
  std::locale::_Locimp *Ptr; // esi
  std::locale *v3; // edi
  unsigned int Refs; // eax

  Ptr = this->_Plocale->_Ptr;
  v3 = result;
  result->_Ptr = Ptr;
  std::_Lockit::_Lockit((std::_Lockit *)&result, 0);
  Refs = Ptr->_Refs;
  if ( Refs != -1 )
    Ptr->_Refs = Refs + 1;
  std::_Lockit::~_Lockit((std::_Lockit *)&result);
  return v3;
}

//----- (004338B0) --------------------------------------------------------
void __thiscall std::ctype<char>::ctype<char>(
        std::ctype<char> *this,
        const __int16 *_Table,
        bool _Deletetable,
        unsigned int _Refs)
{
  int Delfl; // eax
  _Ctypevec result; // [esp+Ch] [ebp-90h] BYREF
  std::_Locinfo v7; // [esp+1Ch] [ebp-80h] BYREF
  int v8; // [esp+98h] [ebp-4h]

  this->_Refs = _Refs;
  v8 = 0;
  this->__vftable = (std::ctype<char>_vtbl *)&std::ctype<char>::`vftable';
  std::_Locinfo::_Locinfo(&v7, "C");
  this->_Ctype = *_Getctype(&result);
  std::_Locinfo::~_Locinfo(&v7);
  if ( _Table )
  {
    Delfl = this->_Ctype._Delfl;
    if ( Delfl <= 0 )
    {
      if ( Delfl < 0 )
        operator delete[]((void *)this->_Ctype._Table);
    }
    else
    {
      free((tagEntry *)this->_Ctype._Table);
    }
    this->_Ctype._Table = _Table;
    this->_Ctype._Delfl = -_Deletetable;
  }
}
// 4FCA1C: using guessed type void *std::ctype<char>::`vftable';

//----- (00433990) --------------------------------------------------------
int __thiscall std::ctype<char>::do_tolower(std::ctype<char> *this, unsigned __int8 _Ch)
{
  return _Tolower(_Ch, &this->_Ctype);
}

//----- (004339B0) --------------------------------------------------------
char *__thiscall std::ctype<char>::do_tolower(std::ctype<char> *this, char *_First, char *_Last)
{
  char *v3; // esi
  _Ctypevec *p_Ctype; // edi

  v3 = _First;
  if ( _First != _Last )
  {
    p_Ctype = &this->_Ctype;
    do
    {
      *v3 = _Tolower((unsigned __int8)*v3, p_Ctype);
      ++v3;
    }
    while ( v3 != _Last );
  }
  return v3;
}

//----- (004339E0) --------------------------------------------------------
int __thiscall std::ctype<char>::do_toupper(std::ctype<char> *this, unsigned __int8 _Ch)
{
  return _Toupper(_Ch, &this->_Ctype);
}

//----- (00433A00) --------------------------------------------------------
char *__thiscall std::ctype<char>::do_toupper(std::ctype<char> *this, char *_First, char *_Last)
{
  char *v3; // esi
  _Ctypevec *p_Ctype; // edi

  v3 = _First;
  if ( _First != _Last )
  {
    p_Ctype = &this->_Ctype;
    do
    {
      *v3 = _Toupper((unsigned __int8)*v3, p_Ctype);
      ++v3;
    }
    while ( v3 != _Last );
  }
  return v3;
}

//----- (00433A30) --------------------------------------------------------
char __thiscall std::ctype<char>::do_widen(std::ctype<char> *this, char _Byte)
{
  return _Byte;
}

//----- (00433A40) --------------------------------------------------------
const char *__thiscall std::ctype<char>::do_widen(
        std::ctype<char> *this,
        const char *_First,
        const char *_Last,
        char *_Dest)
{
  const char *result; // eax

  result = _Last;
  qmemcpy(_Dest, _First, _Last - _First);
  return result;
}

//----- (00433A70) --------------------------------------------------------
char __thiscall std::ctype<char>::do_narrow(std::ctype<char> *this, char _Ch, char __formal)
{
  return _Ch;
}

//----- (00433A80) --------------------------------------------------------
const char *__thiscall std::ctype<char>::do_narrow(
        std::ctype<char> *this,
        const char *_First,
        const char *_Last,
        char __formal,
        char *_Dest)
{
  const char *result; // eax

  result = _Last;
  qmemcpy(_Dest, _First, _Last - _First);
  return result;
}

//----- (00433AB0) --------------------------------------------------------
unsigned int __cdecl std::ctype<char>::_Getcat(const std::locale::facet **_Ppf)
{
  std::ctype<char> *v1; // eax
  const std::locale::facet *v2; // eax

  if ( _Ppf && !*_Ppf )
  {
    v1 = (std::ctype<char> *)operator new((tagHeader *)0x18);
    if ( v1 )
      std::ctype<char>::ctype<char>(v1, 0, 0, 0);
    else
      v2 = 0;
    *_Ppf = v2;
  }
  return 2;
}
// 433AFE: variable 'v2' is possibly undefined

//----- (00433B20) --------------------------------------------------------
std::ctype<char> *__thiscall std::ctype<char>::`vector deleting destructor'(std::ctype<char> *this, char a2)
{
  std::ctype<char>::~ctype<char>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00433B40) --------------------------------------------------------
void __thiscall std::ctype<char>::~ctype<char>(std::ctype<char> *this)
{
  int Delfl; // eax

  Delfl = this->_Ctype._Delfl;
  this->__vftable = (std::ctype<char>_vtbl *)&std::ctype<char>::`vftable';
  if ( Delfl <= 0 )
  {
    if ( Delfl < 0 )
      operator delete[]((void *)this->_Ctype._Table);
    this->__vftable = (std::ctype<char>_vtbl *)&std::locale::facet::`vftable';
  }
  else
  {
    free((tagEntry *)this->_Ctype._Table);
    this->__vftable = (std::ctype<char>_vtbl *)&std::locale::facet::`vftable';
  }
}
// 4FC98C: using guessed type void *std::locale::facet::`vftable';
// 4FCA1C: using guessed type void *std::ctype<char>::`vftable';

//----- (00433B80) --------------------------------------------------------
char __thiscall CCellManager::InitAI(CCellManager *this)
{
  int *v1; // ebx
  CFSMState *v2; // eax
  CFSMState *v3; // eax
  CFSMState *v4; // ebp
  int v5; // eax
  int *v6; // esi
  unsigned int v7; // edi
  bool v8; // cc
  int (*v10)[8]; // [esp+10h] [ebp-90h]
  int nIndexState; // [esp+14h] [ebp-8Ch]
  int aryStateIDandTriggerNum[6]; // [esp+18h] [ebp-88h] BYREF
  int aryTriggerandNextState[3][8]; // [esp+30h] [ebp-70h] BYREF
  CFSMState *v14; // [esp+90h] [ebp-10h]
  int v15; // [esp+9Ch] [ebp-4h]

  aryStateIDandTriggerNum[2] = 2;
  aryStateIDandTriggerNum[0] = 1;
  aryStateIDandTriggerNum[1] = 4;
  aryStateIDandTriggerNum[3] = 3;
  aryStateIDandTriggerNum[4] = 3;
  aryStateIDandTriggerNum[5] = 3;
  *(_QWORD *)&aryTriggerandNextState[0][0] = 0x200000064LL;
  *(_QWORD *)&aryTriggerandNextState[0][2] = 0x200000069LL;
  *(_QWORD *)&aryTriggerandNextState[0][4] = 0x500000066LL;
  *(_QWORD *)&aryTriggerandNextState[0][6] = 0x20000006ALL;
  *(_QWORD *)&aryTriggerandNextState[1][0] = 0x300000067LL;
  *(_QWORD *)&aryTriggerandNextState[1][2] = 0x500000066LL;
  *(_QWORD *)&aryTriggerandNextState[1][4] = 0x30000006BLL;
  *(_QWORD *)&aryTriggerandNextState[1][6] = 0LL;
  *(_QWORD *)&aryTriggerandNextState[2][0] = 0x100000068LL;
  *(_QWORD *)&aryTriggerandNextState[2][2] = 0x200000069LL;
  *(_QWORD *)&aryTriggerandNextState[2][4] = 0x500000066LL;
  *(_QWORD *)&aryTriggerandNextState[2][6] = 0LL;
  nIndexState = 0;
  v10 = aryTriggerandNextState;
  v1 = &aryStateIDandTriggerNum[1];
  while ( 1 )
  {
    v2 = (CFSMState *)operator new((tagHeader *)0x10);
    v14 = v2;
    v15 = 0;
    if ( v2 )
    {
      CFSMState::CFSMState(v2, *(v1 - 1), *v1);
      v4 = v3;
    }
    else
    {
      v4 = 0;
    }
    v15 = -1;
    if ( !v4 )
    {
      CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::InitAI", aDWorkRylSource_49, 198, (char *)&byte_4DCFAC);
      return 0;
    }
    v5 = 2 * *v1;
    if ( v5 > 0 )
    {
      v6 = (int *)v10;
      v7 = ((unsigned int)(v5 - 1) >> 1) + 1;
      do
      {
        CFSMState::AddTransition(v4, *v6, v6[1]);
        v6 += 2;
        --v7;
      }
      while ( v7 );
    }
    if ( !CFSM::AddState(CSingleton<CFSM>::ms_pSingleton, v4) )
      break;
    v1 += 2;
    v8 = ++nIndexState < 3;
    ++v10;
    if ( !v8 )
      return 1;
  }
  CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::InitAI", aDWorkRylSource_49, 211, aState);
  CFSMState::~CFSMState(v4);
  operator delete(v4);
  return 1;
}
// 433C9A: variable 'v3' is possibly undefined

//----- (00433D90) --------------------------------------------------------
char __thiscall CCellManager::LoginMonster(CCellManager *this, char *szFileName, int wMapIndex)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  void *v4; // esp
  int v6; // eax
  unsigned int v7; // eax
  int v8; // eax
  unsigned int v9; // eax
  CStatue *v10; // eax
  CStatue *v11; // eax
  CStatue *v12; // esi
  char LinkStatue; // al
  CStatue *v14; // ecx
  char v15; // al
  char v16; // al
  CStatue *v17; // eax
  CStatue *v18; // eax
  char v19; // al
  char v20; // al
  char v21; // al
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // eax
  CDefenderMonster *v23; // eax
  CStatue *v24; // eax
  CWarriorMonster *v25; // eax
  CAcolyteMonster *v26; // eax
  CMageMonster *v27; // eax
  CBossMonster *v28; // eax
  CNamedMonster *v29; // eax
  CChiefMonster *v30; // eax
  CMonster *v31; // eax
  VirtualArea::CVirtualAreaMgr *v32; // eax
  VirtualArea::CBGServerMap *VirtualArea; // eax
  CVirtualMonsterMgr *m_pVirtualMonsterMgr; // eax
  CParty *Guild; // edi
  CMonsterParty *v36; // eax
  CParty *v37; // eax
  CMonster::MonsterCreateInfo fNumber; // [esp+4h] [ebp-8184h] BYREF
  CCreatureManager *Instance; // [esp+24h] [ebp-8164h]
  char szString[260]; // [esp+28h] [ebp-8160h] BYREF
  CDelimitedFile v41; // [esp+12Ch] [ebp-805Ch] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v42; // [esp+817Ch] [ebp-Ch]
  void *v43; // [esp+8180h] [ebp-8h]
  int v44; // [esp+8184h] [ebp-4h]

  v44 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v43 = &_ehhandler__LoginMonster_CCellManager__AAE_NPBDG_Z;
  v42 = ExceptionList;
  v4 = alloca(33148);
  CDelimitedFile::CDelimitedFile(&v41, "\t");
  v44 = 0;
  if ( !CDelimitedFile::Open(&v41, szFileName, 1, 0) )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::LoginMonster", aDWorkRylSource_49, 314, aS_3, szFileName);
    v44 = -1;
    CDelimitedFile::~CDelimitedFile(&v41);
    return 0;
  }
  memset(&fNumber, 0, 29);
  fNumber.m_wRespawnArea = 0;
  Instance = CCreatureManager::GetInstance();
  if ( !CDelimitedFile::ReadLine(&v41) )
  {
LABEL_66:
    CDelimitedFile::Close(&v41);
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CCellManager::LoginMonster",
      aDWorkRylSource_49,
      477,
      (char *)&byte_4DCFEC);
    v44 = -1;
    CDelimitedFile::~CDelimitedFile(&v41);
    return 1;
  }
  while ( 1 )
  {
    CDelimitedFile::ReadString(&v41, szString, 0x104u);
    _mbsnbcmp((unsigned __int8 *)szString, "0x", 2u);
    v7 = v6 ? atol(szString) : Math::Convert::StrToHex32(szString);
    fNumber.m_dwCID = v7;
    CDelimitedFile::ReadData(&v41, &fNumber.m_nKID);
    CDelimitedFile::ReadString(&v41, szString, 0x104u);
    _mbsnbcmp((unsigned __int8 *)szString, "0x", 2u);
    v9 = v8 ? atol(szString) : Math::Convert::StrToHex32(szString);
    fNumber.m_dwPID = v9;
    CDelimitedFile::ReadData(&v41, &fNumber.m_Pos.m_fPointX);
    CDelimitedFile::ReadData(&v41, &fNumber.m_Pos.m_fPointY);
    CDelimitedFile::ReadData(&v41, &fNumber.m_Pos.m_fPointZ);
    CDelimitedFile::ReadData(&v41, &fNumber.m_bScout);
    CDelimitedFile::ReadData(&v41, &fNumber.m_nMovingPattern);
    CDelimitedFile::ReadData(&v41, (__int16 *)&fNumber.m_wRespawnArea);
    if ( (fNumber.m_dwCID & 0x80000000) != 0 )
      break;
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::LoginMonster", aDWorkRylSource_49, 343, byte_4DD084);
LABEL_65:
    if ( !CDelimitedFile::ReadLine(&v41) )
      goto LABEL_66;
  }
  if ( LOWORD(fNumber.m_dwCID) != fNumber.m_nKID )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::LoginMonster",
      aDWorkRylSource_49,
      349,
      (char *)&byte_4DD040);
    goto LABEL_65;
  }
  switch ( fNumber.m_nKID )
  {
    case 0x40A:
    case 0x40B:
    case 0x40C:
    case 0x40D:
    case 0x40E:
      v10 = (CStatue *)operator new((tagHeader *)0x310);
      LOBYTE(v44) = 1;
      if ( v10 )
      {
        CStatue::CStatue(v10, &fNumber, 0);
        v12 = v11;
      }
      else
      {
        v12 = 0;
      }
      LOBYTE(v44) = 0;
      v12->m_CellPos.m_wMapIndex = wMapIndex;
      LinkStatue = CStatue::CreateLinkStatue(v12, 0x40Bu);
      v14 = v12;
      if ( !LinkStatue )
        goto LABEL_67;
      v15 = CStatue::CreateLinkStatue(v12, 0x40Cu);
      v14 = v12;
      if ( !v15 )
        goto LABEL_67;
      v16 = CStatue::CreateLinkStatue(v12, 0x40Du);
      v14 = v12;
      if ( !v16 )
        goto LABEL_67;
      if ( CStatue::CreateLinkStatue(v12, 0x40Eu) )
        goto LABEL_52;
      goto LABEL_22;
    case 0x40F:
    case 0x410:
    case 0x411:
    case 0x412:
    case 0x413:
      v17 = (CStatue *)operator new((tagHeader *)0x310);
      LOBYTE(v44) = 2;
      if ( v17 )
      {
        CStatue::CStatue(v17, &fNumber, 0);
        v12 = v18;
      }
      else
      {
        v12 = 0;
      }
      LOBYTE(v44) = 0;
      v12->m_CellPos.m_wMapIndex = wMapIndex;
      v19 = CStatue::CreateLinkStatue(v12, 0x410u);
      v14 = v12;
      if ( !v19
        || (v20 = CStatue::CreateLinkStatue(v12, 0x411u), v14 = v12, !v20)
        || (v21 = CStatue::CreateLinkStatue(v12, 0x412u), v14 = v12, !v21) )
      {
LABEL_67:
        ((void (__thiscall *)(CStatue *, int))v12->~CAggresiveCreature)(v14, 1);
        goto LABEL_69;
      }
      if ( !CStatue::CreateLinkStatue(v12, 0x413u) )
      {
LABEL_22:
        ((void (__thiscall *)(CStatue *, int))v12->~CAggresiveCreature)(v12, 1);
        goto LABEL_69;
      }
      goto LABEL_52;
    default:
      MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, fNumber.m_nKID);
      if ( !MonsterProtoType )
      {
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CCellManager::LoginMonster",
          aDWorkRylSource_49,
          405,
          aKidD_0,
          fNumber.m_nKID);
        goto LABEL_65;
      }
      switch ( MonsterProtoType->m_MonsterInfo.m_cSkillPattern )
      {
        case 1u:
          v25 = (CWarriorMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 4;
          if ( !v25 )
            goto LABEL_50;
          CWarriorMonster::CWarriorMonster(v25, &fNumber, 0);
          break;
        case 2u:
          v23 = (CDefenderMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 3;
          if ( !v23 )
            goto LABEL_50;
          CDefenderMonster::CDefenderMonster(v23, &fNumber, 0);
          break;
        case 3u:
          v27 = (CMageMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 6;
          if ( !v27 )
            goto LABEL_50;
          CMageMonster::CMageMonster(v27, &fNumber, 0);
          break;
        case 4u:
          v26 = (CAcolyteMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 5;
          if ( !v26 )
            goto LABEL_50;
          CAcolyteMonster::CAcolyteMonster(v26, &fNumber, 0);
          break;
        case 5u:
          v28 = (CBossMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 7;
          if ( !v28 )
            goto LABEL_50;
          CBossMonster::CBossMonster(v28, &fNumber, 0);
          break;
        case 9u:
          v29 = (CNamedMonster *)operator new((tagHeader *)0x338);
          LOBYTE(v44) = 8;
          if ( !v29 )
            goto LABEL_50;
          CNamedMonster::CNamedMonster(v29, &fNumber, 0);
          break;
        case 0xAu:
          v30 = (CChiefMonster *)operator new((tagHeader *)0x328);
          LOBYTE(v44) = 9;
          if ( !v30 )
            goto LABEL_50;
          CChiefMonster::CChiefMonster(v30, &fNumber, 0);
          break;
        default:
          v31 = (CMonster *)operator new((tagHeader *)0x300);
          LOBYTE(v44) = 10;
          if ( v31 )
            CMonster::CMonster(v31, &fNumber, 0);
          else
LABEL_50:
            v24 = 0;
          break;
      }
      v12 = v24;
      LOBYTE(v44) = 0;
      v24->m_CellPos.m_wMapIndex = wMapIndex;
LABEL_52:
      if ( CMonster::InitMonster(v12, &fNumber.m_Pos, LOGINOUT) )
      {
        if ( (_WORD)wMapIndex )
        {
          v32 = VirtualArea::CVirtualAreaMgr::GetInstance();
          VirtualArea = VirtualArea::CVirtualAreaMgr::GetVirtualArea(v32, wMapIndex);
          if ( VirtualArea )
          {
            m_pVirtualMonsterMgr = VirtualArea->m_pVirtualMonsterMgr;
            if ( m_pVirtualMonsterMgr )
              CVirtualMonsterMgr::AddMonster(m_pVirtualMonsterMgr, v12);
          }
        }
        else
        {
          CCreatureManager::AddCreature(Instance, (CNPC *)v12);
        }
        if ( v12->m_dwPID )
        {
          Guild = Guild::CGuildMgr::GetGuild(CSingleton<CPartyMgr>::ms_pSingleton, v12->m_dwPID);
          if ( !Guild )
          {
            v36 = (CMonsterParty *)operator new((tagHeader *)0x16C);
            LOBYTE(v44) = 11;
            if ( v36 )
              CMonsterParty::CMonsterParty(v36);
            else
              v37 = 0;
            v37->m_Party.m_dwPartyID = v12->m_dwPID;
            LOBYTE(v44) = 0;
            Guild = v37;
            CPartyMgr::AddParty(CSingleton<CPartyMgr>::ms_pSingleton, v37);
          }
          v12->m_pParty = Guild;
          Guild->Join(Guild, v12->m_dwCID, 0, 0, wMapIndex);
        }
        goto LABEL_65;
      }
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CCellManager::LoginMonster",
        aDWorkRylSource_49,
        435,
        (char *)&byte_4DCFCC);
LABEL_69:
      v44 = -1;
      CDelimitedFile::~CDelimitedFile(&v41);
      return 0;
  }
}
// 433EBC: variable 'v6' is possibly undefined
// 433F18: variable 'v8' is possibly undefined
// 43402D: variable 'v11' is possibly undefined
// 4340C8: variable 'v18' is possibly undefined
// 43430B: variable 'v24' is possibly undefined
// 4343AC: variable 'v37' is possibly undefined

//----- (004344F0) --------------------------------------------------------
bool __thiscall CCellManager::AdminSummonMonster(CCellManager *this, int nKID, Position Pos)
{
  CCreatureManager *Instance; // ebp
  const CMonsterMgr::MonsterProtoType *MonsterProtoType; // edi
  unsigned __int16 AvailableMonsterUID; // ax
  CDefenderMonster *v7; // eax
  CMonster *v8; // eax
  CWarriorMonster *v9; // eax
  CAcolyteMonster *v10; // eax
  CMageMonster *v11; // eax
  CBossMonster *v12; // eax
  CNamedMonster *v13; // eax
  CChiefMonster *v14; // eax
  CMonster *v15; // eax
  CNPC *v16; // esi
  CMonster::MonsterCreateInfo tempInfo; // [esp+10h] [ebp-2Ch] BYREF
  int v18; // [esp+38h] [ebp-4h]

  memset(&tempInfo, 0, 29);
  tempInfo.m_wRespawnArea = 0;
  Instance = CCreatureManager::GetInstance();
  MonsterProtoType = CMonsterMgr::GetMonsterProtoType(CSingleton<CMonsterMgr>::ms_pSingleton, nKID);
  if ( !MonsterProtoType )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::AdminSummonMonster", aDWorkRylSource_49, 545, aKidD, nKID);
    return 0;
  }
  if ( LOWORD(Instance->m_MonsterMap._Mysize) >= 0x3FFFu )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::AdminSummonMonster", aDWorkRylSource_49, 552, byte_4DD160);
    return 0;
  }
  AvailableMonsterUID = CCreatureManager::GetAvailableMonsterUID(Instance, nKID);
  if ( AvailableMonsterUID == 36863 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::AdminSummonMonster",
      aDWorkRylSource_49,
      560,
      aKindidD,
      nKID);
    return 0;
  }
  tempInfo.m_dwCID = nKID + (AvailableMonsterUID << 16);
  tempInfo.m_Pos = Pos;
  tempInfo.m_nKID = nKID;
  tempInfo.m_dwPID = 0;
  tempInfo.m_bScout = 0;
  tempInfo.m_nMovingPattern = 0;
  tempInfo.m_wRespawnArea = 32;
  switch ( MonsterProtoType->m_MonsterInfo.m_cSkillPattern )
  {
    case 1u:
      v9 = (CWarriorMonster *)operator new((tagHeader *)0x328);
      v18 = 1;
      if ( !v9 )
        goto LABEL_25;
      CWarriorMonster::CWarriorMonster(v9, &tempInfo, 1);
      break;
    case 2u:
      v7 = (CDefenderMonster *)operator new((tagHeader *)0x328);
      v18 = 0;
      if ( !v7 )
        goto LABEL_25;
      CDefenderMonster::CDefenderMonster(v7, &tempInfo, 1);
      break;
    case 3u:
      v11 = (CMageMonster *)operator new((tagHeader *)0x328);
      v18 = 3;
      if ( !v11 )
        goto LABEL_25;
      CMageMonster::CMageMonster(v11, &tempInfo, 1);
      break;
    case 4u:
      v10 = (CAcolyteMonster *)operator new((tagHeader *)0x328);
      v18 = 2;
      if ( !v10 )
        goto LABEL_25;
      CAcolyteMonster::CAcolyteMonster(v10, &tempInfo, 1);
      break;
    case 5u:
      v12 = (CBossMonster *)operator new((tagHeader *)0x328);
      v18 = 4;
      if ( !v12 )
        goto LABEL_25;
      CBossMonster::CBossMonster(v12, &tempInfo, 1);
      break;
    case 9u:
      v13 = (CNamedMonster *)operator new((tagHeader *)0x338);
      v18 = 5;
      if ( !v13 )
        goto LABEL_25;
      CNamedMonster::CNamedMonster(v13, &tempInfo, 1);
      break;
    case 0xAu:
      v14 = (CChiefMonster *)operator new((tagHeader *)0x328);
      v18 = 6;
      if ( !v14 )
        goto LABEL_25;
      CChiefMonster::CChiefMonster(v14, &tempInfo, 1);
      break;
    default:
      v15 = (CMonster *)operator new((tagHeader *)0x300);
      v18 = 7;
      if ( v15 )
        CMonster::CMonster(v15, &tempInfo, 1);
      else
LABEL_25:
        v8 = 0;
      break;
  }
  v16 = (CNPC *)v8;
  v18 = -1;
  if ( !v8 )
  {
    CServerLog::DetailLog(&g_Log, LOG_ERROR, "CCellManager::AdminSummonMonster", aDWorkRylSource_49, 588, byte_4DD0E8);
    return 0;
  }
  if ( !CMonster::InitMonster(v8, &tempInfo.m_Pos, LOGINOUT) )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::AdminSummonMonster",
      aDWorkRylSource_49,
      594,
      (char *)&byte_4DCFCC);
    return 0;
  }
  return CCreatureManager::AddCreature(Instance, v16);
}
// 4347A6: variable 'v8' is possibly undefined

//----- (00434860) --------------------------------------------------------
void __thiscall std::streambuf::~streambuf<char,std::char_traits<char>>(std::streambuf *this)
{
  std::locale *Plocale; // edi
  std::locale::_Locimp *Ptr; // esi
  unsigned int Refs; // eax
  void (__thiscall ***v5)(_DWORD, int); // esi
  std::_Lockit v6; // [esp+8h] [ebp-4h] BYREF

  Plocale = this->_Plocale;
  this->__vftable = (std::streambuf_vtbl *)&std::streambuf::`vftable';
  if ( Plocale )
  {
    Ptr = Plocale->_Ptr;
    if ( Plocale->_Ptr )
    {
      std::_Lockit::_Lockit(&v6, 0);
      Refs = Ptr->_Refs;
      if ( Refs && Refs != -1 )
        Ptr->_Refs = Refs - 1;
      v5 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
      std::_Lockit::~_Lockit(&v6);
      if ( v5 )
        (**v5)(v5, 1);
    }
    operator delete(Plocale);
  }
  std::_Mutex::~_Mutex(&this->_Mylock);
}
// 4FCA44: using guessed type void *std::streambuf::`vftable';

//----- (004348D0) --------------------------------------------------------
int __thiscall std::streambuf::overflow(std::streambuf *this, int __formal)
{
  return -1;
}

//----- (004348E0) --------------------------------------------------------
int __thiscall std::streambuf::underflow(std::streambuf *this)
{
  return -1;
}

//----- (004348F0) --------------------------------------------------------
std::streambuf *__thiscall std::streambuf::setbuf(std::streambuf *this, char *__formal, int a3)
{
  return this;
}

//----- (00434900) --------------------------------------------------------
int __thiscall std::streambuf::uflow(std::streambuf *this)
{
  int result; // eax

  result = this->underflow(this);
  if ( result != -1 )
    return (unsigned __int8)*std::streambuf::_Gninc(this);
  return result;
}

//----- (00434920) --------------------------------------------------------
char *__thiscall std::streambuf::_Gninc(std::streambuf *this)
{
  char **IGnext; // ecx

  --*this->_IGcount;
  IGnext = this->_IGnext;
  return (*IGnext)++;
}

//----- (00434930) --------------------------------------------------------
int __thiscall std::streambuf::xsgetn(std::streambuf *this, char *_Ptr, int _Count)
{
  int v3; // edi
  int result; // eax
  char *v7; // esi
  signed int v8; // eax
  int v9; // eax
  int _Copied; // [esp+8h] [ebp-4h]

  v3 = _Count;
  result = 0;
  _Copied = 0;
  if ( _Count > 0 )
  {
    do
    {
      v7 = *this->_IGnext;
      if ( v7 && (v8 = *this->_IGcount, v8 > 0) )
      {
        if ( v3 < v8 )
          v8 = v3;
        qmemcpy(_Ptr, v7, v8);
        _Copied += v8;
        *this->_IGcount -= v8;
        v3 = _Count - v8;
        _Ptr += v8;
        _Count -= v8;
        *this->_IGnext += v8;
      }
      else
      {
        v9 = this->uflow(this);
        if ( v9 == -1 )
          return _Copied;
        *_Ptr++ = v9;
        --v3;
        ++_Copied;
        _Count = v3;
      }
    }
    while ( v3 > 0 );
    return _Copied;
  }
  return result;
}

//----- (004349D0) --------------------------------------------------------
int __thiscall std::streambuf::xsputn(std::streambuf *this, const char *_Ptr, int _Count)
{
  int v3; // esi
  int result; // eax
  char *v7; // edi
  signed int v8; // eax
  int _Copied; // [esp+8h] [ebp-4h]

  v3 = _Count;
  result = 0;
  _Copied = 0;
  if ( _Count > 0 )
  {
    do
    {
      v7 = *this->_IPnext;
      if ( v7 && (v8 = *this->_IPcount, v8 > 0) )
      {
        if ( v3 < v8 )
          v8 = v3;
        qmemcpy(v7, _Ptr, v8);
        _Copied += v8;
        *this->_IPcount -= v8;
        _Ptr += v8;
        _Count -= v8;
        *this->_IPnext += v8;
        v3 = _Count;
      }
      else
      {
        if ( this->overflow(this, *(unsigned __int8 *)_Ptr) == -1 )
          return _Copied;
        ++_Ptr;
        --v3;
        ++_Copied;
        _Count = v3;
      }
    }
    while ( v3 > 0 );
    return _Copied;
  }
  return result;
}

//----- (00434A80) --------------------------------------------------------
std::fpos<int> *__thiscall std::streambuf::seekoff(
        std::streambuf *this,
        std::fpos<int> *result,
        int __formal,
        int a4,
        int a5)
{
  std::fpos<int> *v5; // eax

  v5 = result;
  result->_Myoff = std::_BADOFF;
  result->_Fpos = std::_Fpz;
  result->_Mystate = std::fpos<int>::_Stz;
  return v5;
}

//----- (00434AB0) --------------------------------------------------------
std::fpos<int> *__thiscall std::streambuf::seekpos(
        std::streambuf *this,
        std::fpos<int> *result,
        std::fpos<int> __formal,
        int a4)
{
  std::fpos<int> *v4; // eax

  v4 = result;
  result->_Myoff = std::_BADOFF;
  result->_Fpos = std::_Fpz;
  result->_Mystate = std::fpos<int>::_Stz;
  return v4;
}

//----- (00434AE0) --------------------------------------------------------
char *__thiscall std::ostream::`vector deleting destructor'(std::ios *this, char a2)
{
  char *p_Fillch; // esi

  p_Fillch = &this[-1]._Fillch;
  *(_DWORD *)((char *)this + *(_DWORD *)(*(_DWORD *)&this[-1]._Fillch + 4) - 4) = &std::ostream::`vftable';
  std::ios::~ios<char,std::char_traits<char>>(this);
  if ( (a2 & 1) != 0 )
    operator delete(p_Fillch);
  return p_Fillch;
}
// 4FCA0C: using guessed type void *std::ostream::`vftable';

//----- (00434B10) --------------------------------------------------------
std::ostream **__thiscall std::istream::`vector deleting destructor'(std::ios *this, char a2)
{
  std::ostream **p_Tiestr; // esi

  p_Tiestr = &this[-1]._Tiestr;
  *(_DWORD *)((char *)this + *(_DWORD *)&this[-1]._Tiestr->gap0[4] - 8) = &std::istream::`vftable';
  std::ios::~ios<char,std::char_traits<char>>(this);
  if ( (a2 & 1) != 0 )
    operator delete(p_Tiestr);
  return p_Tiestr;
}
// 4DCF70: using guessed type void *std::istream::`vftable';

//----- (00434B40) --------------------------------------------------------
std::streambuf *__thiscall std::streambuf::`vector deleting destructor'(std::streambuf *this, char a2)
{
  std::streambuf::~streambuf<char,std::char_traits<char>>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00434B60) --------------------------------------------------------
_BYTE *__thiscall std::iostream::`vector deleting destructor'(std::iostream *this, char a2)
{
  _BYTE *v2; // esi

  v2 = &this[-1].gap8[44];
  std::iostream::~basic_iostream<char,std::char_traits<char>>(this);
  std::ios::~ios<char,std::char_traits<char>>((std::ios *)this);
  if ( (a2 & 1) != 0 )
    operator delete(v2);
  return v2;
}

//----- (00434B90) --------------------------------------------------------
void __thiscall std::streambuf::_Init(std::streambuf *this)
{
  this->_IGnext = &this->_Gnext;
  this->_IPnext = &this->_Pnext;
  this->_IGfirst = &this->_Gfirst;
  this->_IGcount = &this->_Gcount;
  this->_IPfirst = &this->_Pfirst;
  this->_IPcount = &this->_Pcount;
  this->_Pfirst = 0;
  *this->_IPnext = 0;
  *this->_IPcount = 0;
  *this->_IGfirst = 0;
  *this->_IGnext = 0;
  *this->_IGcount = 0;
}

//----- (00434BF0) --------------------------------------------------------
const std::ctype<char> *__cdecl std::use_facet<std::ctype<char>>(std::locale *_Loc)
{
  const std::locale::facet *v1; // edi
  std::locale::facet *v2; // esi
  unsigned int Refs; // eax
  const std::locale::facet *_Psave; // [esp+8h] [ebp-24h] BYREF
  std::_Lockit v6; // [esp+Ch] [ebp-20h] BYREF
  std::_Lockit _Lock; // [esp+10h] [ebp-1Ch] BYREF
  bad_cast pExceptionObject; // [esp+14h] [ebp-18h] BYREF
  int v9; // [esp+28h] [ebp-4h]

  std::_Lockit::_Lockit(&_Lock, 0);
  v1 = std::_Facetptr<std::ctype<char>>::_Psave;
  v9 = 0;
  _Psave = std::_Facetptr<std::ctype<char>>::_Psave;
  if ( !std::ctype<char>::id._Id )
  {
    std::_Lockit::_Lockit(&v6, 0);
    if ( !std::ctype<char>::id._Id )
      std::ctype<char>::id._Id = ++std::locale::id::_Id_cnt;
    std::_Lockit::~_Lockit(&v6);
  }
  v2 = (std::locale::facet *)std::locale::_Getfacet(_Loc, std::ctype<char>::id._Id);
  if ( !v2 )
  {
    if ( v1 )
    {
      v2 = (std::locale::facet *)v1;
    }
    else
    {
      if ( std::ctype<char>::_Getcat(&_Psave) == -1 )
      {
        bad_cast::bad_cast(&pExceptionObject, "bad cast");
        _CxxThrowException(&pExceptionObject, &_TI2_AVbad_cast__);
      }
      v2 = (std::locale::facet *)_Psave;
      std::_Facetptr<std::ctype<char>>::_Psave = _Psave;
      std::_Lockit::_Lockit(&v6, 0);
      Refs = v2->_Refs;
      if ( Refs != -1 )
        v2->_Refs = Refs + 1;
      std::_Lockit::~_Lockit(&v6);
      std::locale::facet::_Register(v2);
    }
  }
  v9 = -1;
  std::_Lockit::~_Lockit(&_Lock);
  return (const std::ctype<char> *)v2;
}

//----- (00434D00) --------------------------------------------------------
void __thiscall std::num_put<char,std::ostreambuf_iterator<char>>::num_put<char,std::ostreambuf_iterator<char>>(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        unsigned int _Refs)
{
  std::_Locinfo v3; // [esp+8h] [ebp-80h] BYREF
  int v4; // [esp+84h] [ebp-4h]

  this->_Refs = _Refs;
  v4 = 0;
  this->__vftable = (std::num_put<char,std::ostreambuf_iterator<char> >_vtbl *)&std::num_put<char,std::ostreambuf_iterator<char>>::`vftable';
  std::_Locinfo::_Locinfo(&v3, "C");
  this->_Cvt = _Getcvt();
  std::_Locinfo::~_Locinfo(&v3);
}
// 4DD208: using guessed type void *std::num_put<char,std::ostreambuf_iterator<char>>::`vftable';

//----- (00434D80) --------------------------------------------------------
std::codecvt<char,char,int> *__thiscall std::codecvt<char,char,int>::`vector deleting destructor'(
        std::codecvt<char,char,int> *this,
        char a2)
{
  std::codecvt_base::~codecvt_base(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00434DA0) --------------------------------------------------------
void __thiscall std::codecvt<char,char,int>::codecvt<char,char,int>(
        std::codecvt<char,char,int> *this,
        unsigned int _Refs)
{
  std::_Locinfo v2; // [esp+8h] [ebp-80h] BYREF
  int v3; // [esp+84h] [ebp-4h]

  this->_Refs = _Refs;
  v3 = 0;
  this->__vftable = (std::codecvt<char,char,int>_vtbl *)&std::codecvt<char,char,int>::`vftable';
  std::_Locinfo::_Locinfo(&v2, "C");
  std::_Locinfo::~_Locinfo(&v2);
}
// 4FCAB4: using guessed type void *std::codecvt<char,char,int>::`vftable';

//----- (00434E10) --------------------------------------------------------
int __thiscall std::codecvt<char,char,int>::do_out(
        std::codecvt<char,char,int> *this,
        int *__formal,
        const char *_First1,
        const char *a4,
        const char **_Mid1,
        char *_First2,
        char *a7,
        char **_Mid2)
{
  *_Mid1 = _First1;
  *_Mid2 = _First2;
  return 3;
}

//----- (00434E30) --------------------------------------------------------
int __thiscall std::codecvt<char,char,int>::do_unshift(
        std::codecvt<char,char,int> *this,
        int *__formal,
        char *a3,
        char *a4,
        char **a5)
{
  return 3;
}

//----- (00434E40) --------------------------------------------------------
int __thiscall std::codecvt<char,char,int>::do_length(
        std::codecvt<char,char,int> *this,
        const int *__formal,
        const char *_First1,
        const char *_Last1,
        unsigned int _Count)
{
  int result; // eax

  result = _Last1 - _First1;
  if ( _Count < _Last1 - _First1 )
    return _Count;
  return result;
}

//----- (00434E60) --------------------------------------------------------
void __thiscall std::codecvt_base::~codecvt_base(std::codecvt<char,char,int> *this)
{
  this->__vftable = (std::codecvt<char,char,int>_vtbl *)&std::locale::facet::`vftable';
}
// 4FC98C: using guessed type void *std::locale::facet::`vftable';

//----- (00434E70) --------------------------------------------------------
void __thiscall std::streambuf::streambuf(std::streambuf *this)
{
  std::locale *v2; // eax
  std::locale *v3; // eax

  this->__vftable = (std::streambuf_vtbl *)&std::streambuf::`vftable';
  std::_Mutex::_Mutex(&this->_Mylock);
  v2 = (std::locale *)operator new((tagHeader *)4);
  if ( v2 )
    std::locale::locale(v2);
  else
    v3 = 0;
  this->_Plocale = v3;
  std::streambuf::_Init(this);
}
// 434EBE: variable 'v3' is possibly undefined
// 4FCA44: using guessed type void *std::streambuf::`vftable';

//----- (00434EE0) --------------------------------------------------------
int __thiscall std::ios::widen(std::ios *this, int _Byte)
{
  std::locale *v2; // eax
  const std::ctype<char> *v3; // eax
  std::locale::_Locimp *Ptr; // esi
  const std::ctype<char> *v5; // edi
  unsigned int Refs; // eax
  void (__thiscall ***v7)(_DWORD, int); // esi
  std::locale result; // [esp+8h] [ebp-14h] BYREF
  std::_Lockit v10; // [esp+Ch] [ebp-10h] BYREF
  int v11; // [esp+18h] [ebp-4h]

  v2 = std::ios_base::getloc(this, &result);
  v11 = 0;
  v3 = std::use_facet<std::ctype<char>>(v2);
  Ptr = result._Ptr;
  v5 = v3;
  v11 = -1;
  if ( result._Ptr )
  {
    std::_Lockit::_Lockit(&v10, 0);
    Refs = Ptr->_Refs;
    if ( Refs && Refs != -1 )
      Ptr->_Refs = Refs - 1;
    v7 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
    std::_Lockit::~_Lockit(&v10);
    if ( v7 )
      (**v7)(v7, 1);
  }
  return ((int (__thiscall *)(const std::ctype<char> *, int))v5->do_widen)(v5, _Byte);
}

//----- (00434F90) --------------------------------------------------------
unsigned int __cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Getcat(const std::locale::facet **_Ppf)
{
  std::num_put<char,std::ostreambuf_iterator<char> > *v1; // eax
  const std::locale::facet *v2; // eax

  if ( _Ppf && !*_Ppf )
  {
    v1 = (std::num_put<char,std::ostreambuf_iterator<char> > *)operator new((tagHeader *)0x10);
    if ( v1 )
      std::num_put<char,std::ostreambuf_iterator<char>>::num_put<char,std::ostreambuf_iterator<char>>(v1, 0);
    else
      v2 = 0;
    *_Ppf = v2;
  }
  return 4;
}
// 434FDA: variable 'v2' is possibly undefined

//----- (00435000) --------------------------------------------------------
unsigned int __cdecl std::codecvt<char,char,int>::_Getcat(const std::locale::facet **_Ppf)
{
  std::codecvt<char,char,int> *v1; // eax
  const std::locale::facet *v2; // eax

  if ( _Ppf && !*_Ppf )
  {
    v1 = (std::codecvt<char,char,int> *)operator new((tagHeader *)8);
    if ( v1 )
      std::codecvt<char,char,int>::codecvt<char,char,int>(v1, 0);
    else
      v2 = 0;
    *_Ppf = v2;
  }
  return 2;
}
// 43504A: variable 'v2' is possibly undefined

//----- (00435070) --------------------------------------------------------
char __thiscall CCellManager::IsSafetyZone(CCellManager *this, Position Pos)
{
  CServerSetup *Instance; // eax
  CServerSetup *v4; // eax
  double v5; // st7
  double v6; // st6
  CCellManager::SafetyZoneInfo **Mylast; // edi
  CCellManager::SafetyZoneInfo **Myfirst; // eax
  CCellManager::SafetyZoneInfo *v9; // ecx
  unsigned int v10; // ebp
  unsigned int *v11; // edx
  unsigned int *v12; // esi
  unsigned int *v13; // ecx
  int v14; // ebp
  unsigned int v15; // edi
  float v17; // [esp+4h] [ebp-Ch]
  CCellManager::SafetyZoneInfo **v18; // [esp+4h] [ebp-Ch]
  int nDestZ; // [esp+8h] [ebp-8h]

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 1 )
    return 1;
  v4 = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(v4) == 2 )
    return 1;
  v5 = Pos.m_fPointX * 100.0;
  v6 = Pos.m_fPointZ * 100.0;
  v17 = v6;
  Mylast = this->m_vecSafetyZone._Mylast;
  nDestZ = (unsigned __int64)(128.0 - (double)((int)(unsigned __int64)v17 % 31508) * 0.0040650405 - 1.0);
  Myfirst = this->m_vecSafetyZone._Myfirst;
  v18 = Mylast;
  if ( Myfirst == Mylast )
    return 0;
  while ( 1 )
  {
    v9 = *Myfirst;
    if ( (*Myfirst)->m_dwSectorX == (unsigned __int8)(unsigned __int64)(0.000031737971 * v5)
      && v9->m_dwSectorY == (unsigned __int8)(unsigned __int64)(v6 * 0.000031737971) )
    {
      v10 = v9->m_aryColorTable[nDestZ][(unsigned __int64)((double)((int)(unsigned __int64)v5 % 31508) * 0.0040650405)];
      v11 = v9->m_vecBGMColorKey._Myfirst;
      v12 = v9->m_vecEventKey._Myfirst;
      v13 = v9->m_vecBGMColorKey._Mylast;
      v14 = v10 & 0xFFFFFF;
      if ( v11 != v13 )
        break;
    }
LABEL_13:
    if ( ++Myfirst == Mylast )
      return 0;
  }
  while ( 1 )
  {
    if ( v14 == *v11 )
    {
      v15 = *v12;
      if ( !*v12 || v15 == 1 || v15 == 4 )
        return 1;
    }
    ++v11;
    ++v12;
    if ( v11 == v13 )
    {
      Mylast = v18;
      goto LABEL_13;
    }
  }
}

//----- (004351D0) --------------------------------------------------------
void __thiscall std::string::string(std::string *this)
{
  this->_Myres = 15;
  this->_Mysize = 0;
  this->_Bx._Buf[0] = 0;
}

//----- (004351F0) --------------------------------------------------------
void __thiscall std::filebuf::filebuf(std::filebuf *this, _iobuf *_File)
{
  int v3; // ecx

  std::streambuf::streambuf(this);
  this->__vftable = (std::filebuf_vtbl *)&std::filebuf::`vftable';
  this->_Mystr = 0;
  this->_Closef = 0;
  this->_Wrotesome = 0;
  std::streambuf::_Init(this);
  if ( _File )
  {
    this->_IGfirst = &_File->_base;
    this->_IPfirst = &_File->_base;
    this->_IGnext = &_File->_ptr;
    this->_IPnext = &_File->_ptr;
    this->_IGcount = &_File->_cnt;
    this->_IPcount = &_File->_cnt;
  }
  this->_Myfile = _File;
  this->_State = `std::filebuf::_Init'::`2'::_Stinit;
  v3 = `std::filebuf::_Init'::`2'::_Stinit;
  this->_Pcvt = 0;
  this->_State0 = v3;
}
// 4FCA7C: using guessed type void *std::filebuf::`vftable';
// 523C50: using guessed type int `std::filebuf::_Init'::`2'::_Stinit;

//----- (00435250) --------------------------------------------------------
std::filebuf *__thiscall std::filebuf::setbuf(std::filebuf *this, char *_Buffer, int _Count)
{
  _iobuf *Myfile; // ecx
  int v5; // eax

  Myfile = this->_Myfile;
  if ( !Myfile )
    return 0;
  v5 = _Buffer || _Count ? 0 : 4;
  if ( setvbuf(Myfile, _Buffer, v5, _Count) )
    return 0;
  else
    return this;
}

//----- (004352A0) --------------------------------------------------------
int __thiscall std::filebuf::sync(std::filebuf *this)
{
  if ( !this->_Myfile || this->overflow(this, -1) == -1 || fflush(this->_Myfile) >= 0 )
    return 0;
  else
    return -1;
}

//----- (004352D0) --------------------------------------------------------
int __thiscall std::filebuf::pbackfail(std::filebuf *this, int _Meta)
{
  char *v3; // eax
  char *p_Mychar; // eax
  char **IGfirst; // edx

  v3 = *this->_IGnext;
  if ( v3 && *this->_IGfirst < v3 && (_Meta == -1 || (unsigned __int8)*(v3 - 1) == _Meta) )
  {
    std::streambuf::_Gndec(this);
    return _Meta == -1 ? 0 : _Meta;
  }
  if ( this->_Myfile && _Meta != -1 )
  {
    if ( !this->_Pcvt && ungetc((unsigned __int8)_Meta, this->_Myfile) != -1 )
      return _Meta;
    p_Mychar = &this->_Mychar;
    if ( *(std::filebuf **)this->_IGnext != (std::filebuf *)&this->_Mychar )
    {
      IGfirst = this->_IGfirst;
      *p_Mychar = _Meta;
      *IGfirst = p_Mychar;
      *this->_IGnext = p_Mychar;
      *this->_IGcount = 1;
      return _Meta;
    }
  }
  return -1;
}

//----- (00435370) --------------------------------------------------------
char *__thiscall std::streambuf::_Gndec(std::streambuf *this)
{
  ++*this->_IGcount;
  return --*this->_IGnext;
}

//----- (00435380) --------------------------------------------------------
int __thiscall std::filebuf::underflow(std::filebuf *this)
{
  char **IGnext; // eax
  int result; // eax
  int v4; // edi

  IGnext = this->_IGnext;
  if ( *IGnext && *IGnext < &(*IGnext)[*this->_IGcount] )
    return (unsigned __int8)**IGnext;
  result = this->uflow(this);
  v4 = result;
  if ( result != -1 )
  {
    this->pbackfail(this, result);
    return v4;
  }
  return result;
}

//----- (004353D0) --------------------------------------------------------
void __thiscall std::filebuf::_Initcvt(std::filebuf *this, std::codecvt<char,char,int> *_Newpcvt)
{
  std::string *v3; // eax

  if ( _Newpcvt->do_always_noconv(_Newpcvt) )
  {
    this->_Pcvt = 0;
  }
  else
  {
    this->_Pcvt = _Newpcvt;
    std::streambuf::_Init(this);
    if ( !this->_Mystr )
    {
      v3 = (std::string *)operator new((tagHeader *)0x1C);
      if ( v3 )
      {
        v3->_Myres = 15;
        v3->_Mysize = 0;
        v3->_Bx._Buf[0] = 0;
        this->_Mystr = v3;
      }
      else
      {
        this->_Mystr = 0;
      }
    }
  }
}

//----- (00435440) --------------------------------------------------------
void __thiscall std::ios::init(std::ios *this, std::streambuf *_Strbuf, bool _Isstd)
{
  std::ios_base::_Init(this);
  this->_Mystrbuf = _Strbuf;
  this->_Tiestr = 0;
  this->_Fillch = std::ios::widen(this, 32);
  if ( !this->_Mystrbuf )
    std::ios_base::clear(this, LOBYTE(this->_Mystate) | 4, 0);
  if ( _Isstd )
    std::ios_base::_Addstd(this);
  else
    this->_Stdstr = 0;
}

//----- (004354A0) --------------------------------------------------------
std::ostream *__cdecl std::operator<<<std::char_traits<char>>(std::ostream *_Ostr, unsigned __int8 _Ch)
{
  int v2; // esi
  int v3; // ecx
  int v4; // eax
  char *v5; // ecx
  int v6; // eax
  char *v7; // ecx
  int v8; // eax
  int v9; // ecx
  _DWORD *v10; // edx
  _DWORD *v11; // ecx
  _BYTE *v12; // edx
  int v13; // ecx
  _DWORD *v14; // eax
  _DWORD *v15; // ecx
  unsigned __int8 *v16; // eax
  int v17; // eax
  int v18; // ecx
  int v19; // eax
  int v20; // ecx
  _DWORD *v21; // edx
  _DWORD *v22; // ecx
  _BYTE *v23; // edx
  std::ios_base *v24; // ecx
  int v25; // eax
  std::ostream *Myostr; // edi
  int v27; // eax
  char v28; // cl
  char *v29; // eax
  int v30; // esi
  std::ios_base *v31; // ecx
  int v32; // eax
  std::_Mutex *v33; // eax
  int v35; // [esp+0h] [ebp-2Ch] BYREF
  std::ostream::sentry _Ok; // [esp+Ch] [ebp-20h] BYREF
  int _State; // [esp+14h] [ebp-18h]
  int _Pad; // [esp+18h] [ebp-14h]
  int *v39; // [esp+1Ch] [ebp-10h]
  int v40; // [esp+28h] [ebp-4h]

  v39 = &v35;
  v2 = 0;
  _State = 0;
  std::ostream::sentry::sentry(&_Ok, _Ostr);
  v40 = 0;
  if ( _Ok._Ok )
  {
    v3 = *(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4);
    v4 = *(_DWORD *)&_Ostr->gap0[v3 + 24];
    v5 = &_Ostr->gap0[v3];
    if ( v4 > 1 )
      _Pad = v4 - 1;
    else
      _Pad = 0;
    v6 = *((_DWORD *)v5 + 4) & 0x1C0;
    LOBYTE(v40) = 1;
    if ( v6 == 64 )
    {
LABEL_15:
      v13 = *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 40];
      v14 = *(_DWORD **)(v13 + 36);
      if ( *v14 && *v14 < (unsigned int)(*v14 + **(_DWORD **)(v13 + 52)) )
      {
        --**(_DWORD **)(v13 + 52);
        v15 = *(_DWORD **)(v13 + 36);
        v16 = (unsigned __int8 *)(*v15)++;
        *v16 = _Ch;
        v17 = _Ch;
      }
      else
      {
        v17 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v13 + 4))(v13, _Ch);
      }
      if ( v17 == -1 )
      {
        v2 = 4;
        _State = 4;
      }
      while ( !v2 && _Pad > 0 )
      {
        v18 = *(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4);
        LOBYTE(v19) = _Ostr->gap0[v18 + 48];
        v20 = *(_DWORD *)&_Ostr->gap0[v18 + 40];
        v21 = *(_DWORD **)(v20 + 36);
        if ( *v21 && *v21 < (unsigned int)(*v21 + **(_DWORD **)(v20 + 52)) )
        {
          --**(_DWORD **)(v20 + 52);
          v22 = *(_DWORD **)(v20 + 36);
          v23 = (_BYTE *)(*v22)++;
          *v23 = v19;
          v19 = (unsigned __int8)v19;
        }
        else
        {
          v19 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v20 + 4))(v20, (unsigned __int8)v19);
        }
        if ( v19 == -1 )
        {
          v2 = 4;
          _State = 4;
        }
        --_Pad;
      }
    }
    else
    {
      while ( !v2 )
      {
        if ( _Pad <= 0 )
          goto LABEL_15;
        v7 = &_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4)];
        LOBYTE(v8) = v7[48];
        v9 = *((_DWORD *)v7 + 10);
        v10 = *(_DWORD **)(v9 + 36);
        if ( *v10 && *v10 < (unsigned int)(*v10 + **(_DWORD **)(v9 + 52)) )
        {
          --**(_DWORD **)(v9 + 52);
          v11 = *(_DWORD **)(v9 + 36);
          v12 = (_BYTE *)(*v11)++;
          *v12 = v8;
          v8 = (unsigned __int8)v8;
        }
        else
        {
          v8 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v9 + 4))(v9, (unsigned __int8)v8);
        }
        if ( v8 == -1 )
        {
          v2 = 4;
          _State = 4;
        }
        --_Pad;
      }
    }
  }
  *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 24] = 0;
  v24 = (std::ios_base *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4)];
  v40 = 0;
  if ( v2 )
  {
    v25 = v2 | v24->_Mystate;
    if ( !v24[1].__vftable )
      LOBYTE(v25) = v25 | 4;
    std::ios_base::clear(v24, v25, 0);
  }
  v40 = 3;
  if ( !std::uncaught_exception() )
  {
    Myostr = _Ok._Myostr;
    v27 = *(_DWORD *)(*(_DWORD *)_Ok._Myostr + 4);
    v28 = _Ok._Myostr->gap0[v27 + 16];
    v29 = &_Ok._Myostr->gap0[v27];
    if ( (v28 & 2) != 0 )
    {
      v30 = 0;
      if ( (v29[8] & 6) == 0
        && (*(int (__thiscall **)(_DWORD))(**((_DWORD **)v29 + 10) + 44))(*((_DWORD *)v29 + 10)) == -1 )
      {
        v30 = 4;
      }
      v31 = (std::ios_base *)&Myostr->gap0[*(_DWORD *)(*(_DWORD *)Myostr->gap0 + 4)];
      if ( v30 )
      {
        v32 = v30 | v31->_Mystate;
        if ( !v31[1].__vftable )
          LOBYTE(v32) = v32 | 4;
        std::ios_base::clear(v31, v32, 0);
      }
    }
  }
  v33 = *(std::_Mutex **)&_Ok._Myostr->gap0[*(_DWORD *)(*(_DWORD *)_Ok._Myostr + 4) + 40];
  v40 = -1;
  if ( v33 )
    std::_Mutex::_Unlock(v33 + 1);
  return _Ostr;
}

//----- (00435720) --------------------------------------------------------
const std::num_put<char,std::ostreambuf_iterator<char> > *__cdecl std::use_facet<std::num_put<char,std::ostreambuf_iterator<char>>>(
        std::locale *_Loc)
{
  const std::locale::facet *v1; // edi
  std::locale::facet *v2; // esi
  unsigned int Refs; // eax
  const std::locale::facet *_Psave; // [esp+8h] [ebp-24h] BYREF
  std::_Lockit v6; // [esp+Ch] [ebp-20h] BYREF
  std::_Lockit _Lock; // [esp+10h] [ebp-1Ch] BYREF
  bad_cast pExceptionObject; // [esp+14h] [ebp-18h] BYREF
  int v9; // [esp+28h] [ebp-4h]

  std::_Lockit::_Lockit(&_Lock, 0);
  v1 = std::_Facetptr<std::num_put<char,std::ostreambuf_iterator<char>>>::_Psave;
  v9 = 0;
  _Psave = std::_Facetptr<std::num_put<char,std::ostreambuf_iterator<char>>>::_Psave;
  if ( !std::num_put<char,std::ostreambuf_iterator<char>>::id._Id )
  {
    std::_Lockit::_Lockit(&v6, 0);
    if ( !std::num_put<char,std::ostreambuf_iterator<char>>::id._Id )
      std::num_put<char,std::ostreambuf_iterator<char>>::id._Id = ++std::locale::id::_Id_cnt;
    std::_Lockit::~_Lockit(&v6);
  }
  v2 = (std::locale::facet *)std::locale::_Getfacet(_Loc, std::num_put<char,std::ostreambuf_iterator<char>>::id._Id);
  if ( !v2 )
  {
    if ( v1 )
    {
      v2 = (std::locale::facet *)v1;
    }
    else
    {
      if ( std::num_put<char,std::ostreambuf_iterator<char>>::_Getcat(&_Psave) == -1 )
      {
        bad_cast::bad_cast(&pExceptionObject, "bad cast");
        _CxxThrowException(&pExceptionObject, &_TI2_AVbad_cast__);
      }
      v2 = (std::locale::facet *)_Psave;
      std::_Facetptr<std::num_put<char,std::ostreambuf_iterator<char>>>::_Psave = _Psave;
      std::_Lockit::_Lockit(&v6, 0);
      Refs = v2->_Refs;
      if ( Refs != -1 )
        v2->_Refs = Refs + 1;
      std::_Lockit::~_Lockit(&v6);
      std::locale::facet::_Register(v2);
    }
  }
  v9 = -1;
  std::_Lockit::~_Lockit(&_Lock);
  return (const std::num_put<char,std::ostreambuf_iterator<char> > *)v2;
}

//----- (00435830) --------------------------------------------------------
const std::codecvt<char,char,int> *__cdecl std::use_facet<std::codecvt<char,char,int>>(std::locale *_Loc)
{
  const std::locale::facet *v1; // edi
  std::locale::facet *v2; // esi
  unsigned int Refs; // eax
  const std::locale::facet *_Psave; // [esp+8h] [ebp-24h] BYREF
  std::_Lockit v6; // [esp+Ch] [ebp-20h] BYREF
  std::_Lockit _Lock; // [esp+10h] [ebp-1Ch] BYREF
  bad_cast pExceptionObject; // [esp+14h] [ebp-18h] BYREF
  int v9; // [esp+28h] [ebp-4h]

  std::_Lockit::_Lockit(&_Lock, 0);
  v1 = std::_Facetptr<std::codecvt<char,char,int>>::_Psave;
  v9 = 0;
  _Psave = std::_Facetptr<std::codecvt<char,char,int>>::_Psave;
  if ( !std::codecvt<char,char,int>::id._Id )
  {
    std::_Lockit::_Lockit(&v6, 0);
    if ( !std::codecvt<char,char,int>::id._Id )
      std::codecvt<char,char,int>::id._Id = ++std::locale::id::_Id_cnt;
    std::_Lockit::~_Lockit(&v6);
  }
  v2 = (std::locale::facet *)std::locale::_Getfacet(_Loc, std::codecvt<char,char,int>::id._Id);
  if ( !v2 )
  {
    if ( v1 )
    {
      v2 = (std::locale::facet *)v1;
    }
    else
    {
      if ( std::codecvt<char,char,int>::_Getcat(&_Psave) == -1 )
      {
        bad_cast::bad_cast(&pExceptionObject, "bad cast");
        _CxxThrowException(&pExceptionObject, &_TI2_AVbad_cast__);
      }
      v2 = (std::locale::facet *)_Psave;
      std::_Facetptr<std::codecvt<char,char,int>>::_Psave = _Psave;
      std::_Lockit::_Lockit(&v6, 0);
      Refs = v2->_Refs;
      if ( Refs != -1 )
        v2->_Refs = Refs + 1;
      std::_Lockit::~_Lockit(&v6);
      std::locale::facet::_Register(v2);
    }
  }
  v9 = -1;
  std::_Lockit::~_Lockit(&_Lock);
  return (const std::codecvt<char,char,int> *)v2;
}

//----- (00435940) --------------------------------------------------------
std::ostream *__thiscall std::ostream::operator<<(std::ostream *this, int _Val)
{
  std::locale *v3; // eax
  const std::num_put<char,std::ostreambuf_iterator<char> > *v4; // eax
  std::locale::_Locimp *Ptr; // edi
  int *v6; // ebx
  unsigned int Refs; // eax
  void (__thiscall ***v8)(_DWORD, int); // edi
  int v9; // eax
  int v10; // ecx
  int v11; // edx
  std::ios_base *v12; // ecx
  int v13; // eax
  int v15; // [esp+0h] [ebp-3Ch] BYREF
  int v16; // [esp+Ch] [ebp-30h] BYREF
  std::ostream::sentry _Ok; // [esp+14h] [ebp-28h] BYREF
  std::_Lockit v18; // [esp+1Ch] [ebp-20h] BYREF
  std::locale result; // [esp+20h] [ebp-1Ch] BYREF
  int _State; // [esp+24h] [ebp-18h]
  std::ostream *v21; // [esp+28h] [ebp-14h]
  int *v22; // [esp+2Ch] [ebp-10h]
  int v23; // [esp+38h] [ebp-4h]

  v22 = &v15;
  v21 = this;
  _State = 0;
  std::ostream::sentry::sentry(&_Ok, this);
  v23 = 0;
  if ( _Ok._Ok )
  {
    v3 = std::ios_base::getloc((std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)], &result);
    LOBYTE(v23) = 1;
    v4 = std::use_facet<std::num_put<char,std::ostreambuf_iterator<char>>>(v3);
    Ptr = result._Ptr;
    v6 = (int *)v4;
    LOBYTE(v23) = 0;
    if ( result._Ptr )
    {
      std::_Lockit::_Lockit(&v18, 0);
      Refs = Ptr->_Refs;
      if ( Refs && Refs != -1 )
        Ptr->_Refs = Refs - 1;
      v8 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
      std::_Lockit::~_Lockit(&v18);
      if ( v8 )
        (**v8)(v8, 1);
    }
    v9 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
    v10 = *(_DWORD *)&this->gap0[v9 + 40];
    LOBYTE(v18._Locktype) = this->gap0[v9 + 48];
    v11 = *v6;
    LOBYTE(v16) = 0;
    LOBYTE(v23) = 2;
    (*(void (__thiscall **)(int *, int *, int, int, _BYTE *, int, int))(v11 + 28))(
      v6,
      &v16,
      v16,
      v10,
      &this->gap0[v9],
      v18._Locktype,
      _Val);
    if ( (_BYTE)v16 )
      _State = 4;
  }
  v12 = (std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
  v23 = 0;
  if ( _State )
  {
    v13 = _State | v12->_Mystate;
    if ( !v12[1].__vftable )
      LOBYTE(v13) = v13 | 4;
    std::ios_base::clear(v12, v13, 0);
  }
  v23 = -1;
  std::ostream::sentry::~sentry(&_Ok);
  return this;
}

//----- (00435AB0) --------------------------------------------------------
std::ostream *__thiscall std::ostream::operator<<(std::ostream *this, unsigned int _Val)
{
  std::locale *v3; // eax
  const std::num_put<char,std::ostreambuf_iterator<char> > *v4; // eax
  std::locale::_Locimp *Ptr; // edi
  int *v6; // ebx
  unsigned int Refs; // eax
  void (__thiscall ***v8)(_DWORD, int); // edi
  int v9; // eax
  int v10; // ecx
  int v11; // edx
  std::ios_base *v12; // ecx
  int v13; // eax
  int v15; // [esp+0h] [ebp-3Ch] BYREF
  int v16; // [esp+Ch] [ebp-30h] BYREF
  std::ostream::sentry _Ok; // [esp+14h] [ebp-28h] BYREF
  std::_Lockit v18; // [esp+1Ch] [ebp-20h] BYREF
  std::locale result; // [esp+20h] [ebp-1Ch] BYREF
  int _State; // [esp+24h] [ebp-18h]
  std::ostream *v21; // [esp+28h] [ebp-14h]
  int *v22; // [esp+2Ch] [ebp-10h]
  int v23; // [esp+38h] [ebp-4h]

  v22 = &v15;
  v21 = this;
  _State = 0;
  std::ostream::sentry::sentry(&_Ok, this);
  v23 = 0;
  if ( _Ok._Ok )
  {
    v3 = std::ios_base::getloc((std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)], &result);
    LOBYTE(v23) = 1;
    v4 = std::use_facet<std::num_put<char,std::ostreambuf_iterator<char>>>(v3);
    Ptr = result._Ptr;
    v6 = (int *)v4;
    LOBYTE(v23) = 0;
    if ( result._Ptr )
    {
      std::_Lockit::_Lockit(&v18, 0);
      Refs = Ptr->_Refs;
      if ( Refs && Refs != -1 )
        Ptr->_Refs = Refs - 1;
      v8 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
      std::_Lockit::~_Lockit(&v18);
      if ( v8 )
        (**v8)(v8, 1);
    }
    v9 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
    v10 = *(_DWORD *)&this->gap0[v9 + 40];
    LOBYTE(v18._Locktype) = this->gap0[v9 + 48];
    v11 = *v6;
    LOBYTE(v16) = 0;
    LOBYTE(v23) = 2;
    (*(void (__thiscall **)(int *, int *, int, int, _BYTE *, int, unsigned int))(v11 + 24))(
      v6,
      &v16,
      v16,
      v10,
      &this->gap0[v9],
      v18._Locktype,
      _Val);
    if ( (_BYTE)v16 )
      _State = 4;
  }
  v12 = (std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
  v23 = 0;
  if ( _State )
  {
    v13 = _State | v12->_Mystate;
    if ( !v12[1].__vftable )
      LOBYTE(v13) = v13 | 4;
    std::ios_base::clear(v12, v13, 0);
  }
  v23 = -1;
  std::ostream::sentry::~sentry(&_Ok);
  return this;
}

//----- (00435C20) --------------------------------------------------------
std::ostream *__thiscall std::ostream::put(std::ostream *this, unsigned __int8 _Ch)
{
  int v3; // edi
  int v4; // ecx
  _DWORD *v5; // edx
  bool v6; // zf
  _DWORD *v7; // ecx
  unsigned __int8 *v8; // eax
  int v9; // eax
  std::ios_base *v10; // ecx
  int v11; // eax
  int v13; // [esp+0h] [ebp-2Ch] BYREF
  std::ostream::sentry _Ok; // [esp+Ch] [ebp-20h] BYREF
  int _State; // [esp+14h] [ebp-18h]
  std::ostream *v16; // [esp+18h] [ebp-14h]
  int *v17; // [esp+1Ch] [ebp-10h]
  int v18; // [esp+28h] [ebp-4h]

  v17 = &v13;
  v3 = 0;
  v16 = this;
  _State = 0;
  std::ostream::sentry::sentry(&_Ok, this);
  v18 = 0;
  if ( _Ok._Ok )
  {
    v4 = *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4) + 40];
    v5 = *(_DWORD **)(v4 + 36);
    v6 = *v5 == 0;
    LOBYTE(v18) = 1;
    if ( v6 || *v5 >= (unsigned int)(*v5 + **(_DWORD **)(v4 + 52)) )
    {
      v9 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v4 + 4))(v4, _Ch);
    }
    else
    {
      --**(_DWORD **)(v4 + 52);
      v7 = *(_DWORD **)(v4 + 36);
      v8 = (unsigned __int8 *)(*v7)++;
      *v8 = _Ch;
      v9 = _Ch;
    }
    if ( v9 == -1 )
      v3 = 4;
  }
  else
  {
    v3 = 4;
  }
  v10 = (std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
  v18 = 0;
  if ( v3 )
  {
    v11 = v3 | v10->_Mystate;
    if ( !v10[1].__vftable )
      LOBYTE(v11) = v11 | 4;
    std::ios_base::clear(v10, v11, 0);
  }
  v18 = -1;
  std::ostream::sentry::~sentry(&_Ok);
  return this;
}

//----- (00435D40) --------------------------------------------------------
void __thiscall std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>(CBanList *this)
{
  if ( this->m_banList._Myfirst )
    operator delete(this->m_banList._Myfirst);
  this->m_banList._Myfirst = 0;
  this->m_banList._Mylast = 0;
  this->m_banList._Myend = 0;
}

//----- (00435D70) --------------------------------------------------------
void __thiscall std::filebuf::imbue(std::filebuf *this, std::locale *_Loc)
{
  std::codecvt<char,char,int> *v3; // eax

  v3 = (std::codecvt<char,char,int> *)std::use_facet<std::codecvt<char,char,int>>(_Loc);
  std::filebuf::_Initcvt(this, v3);
}

//----- (00435D90) --------------------------------------------------------
std::string *__userpurge std::string::append@<eax>(
        std::string *this@<ecx>,
        int a2@<ebx>,
        unsigned int _Count,
        char _Ch)
{
  unsigned int v5; // ebp
  unsigned int Myres; // eax
  bool v7; // zf
  std::string::_Bxty *p_Bx; // edx
  char *v10; // edi
  int v11; // eax
  bool v12; // cf

  if ( -1 - this->_Mysize <= _Count )
    std::_String_base::_Xlen((std::_String_base *)this);
  if ( !_Count )
    return this;
  v5 = _Count + this->_Mysize;
  if ( v5 == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  Myres = this->_Myres;
  if ( Myres < v5 )
  {
    std::string::_Copy(this, v5, this->_Mysize);
    v7 = v5 == 0;
    goto LABEL_8;
  }
  v7 = v5 == 0;
  if ( v5 )
  {
LABEL_8:
    if ( !v7 )
    {
      if ( this->_Myres < 0x10 )
        p_Bx = &this->_Bx;
      else
        p_Bx = (std::string::_Bxty *)this->_Bx._Ptr;
      LOBYTE(a2) = _Ch;
      BYTE1(a2) = _Ch;
      v10 = &p_Bx->_Buf[this->_Mysize];
      v11 = a2 << 16;
      LOWORD(v11) = a2;
      memset32(v10, v11, _Count >> 2);
      memset(&v10[4 * (_Count >> 2)], _Ch, _Count & 3);
      v12 = this->_Myres < 0x10;
      this->_Mysize = v5;
      if ( !v12 )
      {
        this->_Bx._Ptr[v5] = 0;
        return this;
      }
      this->_Bx._Buf[v5] = 0;
    }
    return this;
  }
  this->_Mysize = 0;
  if ( Myres < 0x10 )
    this->_Bx._Buf[0] = 0;
  else
    *this->_Bx._Ptr = 0;
  return this;
}
// 435D90: could not find valid save-restore pair for ebx

//----- (00435E60) --------------------------------------------------------
std::filebuf *__thiscall std::filebuf::open(std::filebuf *this, const char *_Filename, int _Mode, int _Prot)
{
  _iobuf *v5; // edi
  std::locale *v6; // eax
  std::codecvt<char,char,int> *v7; // eax
  int v8; // edi
  int v9; // eax
  void (__thiscall ***v10)(_DWORD, int); // edi

  if ( this->_Myfile )
    return 0;
  v5 = std::_Fiopen(_Filename, _Mode);
  if ( !v5 )
    return 0;
  this->_Closef = 1;
  this->_Wrotesome = 0;
  std::streambuf::_Init(this);
  this->_IGcount = &v5->_cnt;
  this->_IPcount = &v5->_cnt;
  this->_IGfirst = &v5->_base;
  this->_IPfirst = &v5->_base;
  this->_IGnext = &v5->_ptr;
  this->_IPnext = &v5->_ptr;
  this->_Myfile = v5;
  this->_State = `std::filebuf::_Init'::`2'::_Stinit;
  this->_State0 = `std::filebuf::_Init'::`2'::_Stinit;
  this->_Pcvt = 0;
  v6 = std::streambuf::getloc(this, (std::locale *)&_Prot);
  v7 = (std::codecvt<char,char,int> *)std::use_facet<std::codecvt<char,char,int>>(v6);
  std::filebuf::_Initcvt(this, v7);
  v8 = _Prot;
  if ( _Prot )
  {
    std::_Lockit::_Lockit((std::_Lockit *)&_Mode, 0);
    v9 = *(_DWORD *)(v8 + 4);
    if ( v9 && v9 != -1 )
      *(_DWORD *)(v8 + 4) = v9 - 1;
    v10 = *(_DWORD *)(v8 + 4) != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))v8;
    std::_Lockit::~_Lockit((std::_Lockit *)&_Mode);
    if ( v10 )
      (**v10)(v10, 1);
  }
  return this;
}
// 523C50: using guessed type int `std::filebuf::_Init'::`2'::_Stinit;

//----- (00435F90) --------------------------------------------------------
void __thiscall std::ostream::ostream(std::ostream *this, std::streambuf *_Strbuf, bool _Isstd, int a4)
{
  if ( a4 )
  {
    *(_DWORD *)this->gap0 = &std::iostream::`vbtable'{for `std::ostream'};
    *(_DWORD *)&this->gap0[4] = &std::ios::`vftable';
  }
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)] = &std::ostream::`vftable';
  std::ios::init((std::ios *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)], _Strbuf, _Isstd);
}
// 4FCA0C: using guessed type void *std::ostream::`vftable';
// 4FCA14: using guessed type void *std::ios::`vftable';

//----- (00436010) --------------------------------------------------------
int __thiscall std::filebuf::uflow(std::filebuf *this)
{
  char **IGnext; // ecx
  int *IGcount; // eax
  char **v4; // esi
  char *v5; // eax
  int result; // eax
  std::string *Mystr; // ecx
  int v8; // eax
  std::string *v9; // eax
  int v10; // edi
  unsigned int Mysize; // edx
  const char *v12; // eax
  int v13; // eax
  int v14; // eax
  std::string *v15; // ecx
  std::string::_Bxty *p_Bx; // eax
  std::string *v17; // esi
  std::string *v18; // eax
  std::string::_Bxty *Ptr; // ecx
  int i; // edi
  int v21; // eax
  char _Ch; // [esp+1Dh] [ebp-9h] BYREF
  const char *_Source; // [esp+1Eh] [ebp-8h] BYREF
  char *_Dest; // [esp+22h] [ebp-4h] BYREF

  IGnext = this->_IGnext;
  if ( *IGnext )
  {
    IGcount = this->_IGcount;
    if ( *IGnext < &(*IGnext)[*IGcount] )
    {
      --*IGcount;
      v4 = this->_IGnext;
      v5 = (*v4)++;
      return (unsigned __int8)*v5;
    }
  }
  if ( !this->_Myfile )
    return -1;
  if ( this->_Pcvt )
  {
    Mystr = this->_Mystr;
    this->_State0 = this->_State;
    std::string::erase(Mystr, 0, 0xFFFFFFFF);
    v8 = getc(this->_Myfile);
    if ( v8 == -1 )
    {
      return -1;
    }
    else
    {
      while ( 1 )
      {
        std::string::append(this->_Mystr, 16, 1u, v8);
        v9 = this->_Mystr;
        v10 = v9->_Myres < 0x10 ? (int)&v9->_Bx : (int)v9->_Bx._Ptr;
        Mysize = v9->_Mysize;
        v12 = v9->_Myres < 0x10 ? (const char *)&v9->_Bx : v9->_Bx._Ptr;
        v13 = this->_Pcvt->do_in(
                this->_Pcvt,
                &this->_State,
                v12,
                (const char *)(Mysize + v10),
                &_Source,
                &_Ch,
                (char *)&_Source,
                &_Dest);
        if ( !v13 )
          break;
        v14 = v13 - 1;
        if ( v14 )
        {
          if ( v14 != 2 )
            return -1;
          if ( this->_Mystr->_Mysize )
          {
            v17 = this->_Mystr;
            if ( v17->_Myres < 0x10 )
              return (unsigned __int8)v17->_Bx._Buf[0];
            else
              return *(unsigned __int8 *)v17->_Bx._Ptr;
          }
        }
        else
        {
          v15 = this->_Mystr;
          if ( v15->_Myres < 0x10 )
            p_Bx = &v15->_Bx;
          else
            p_Bx = (std::string::_Bxty *)v15->_Bx._Ptr;
          std::string::erase(v15, 0, _Source - (const char *)p_Bx);
        }
        v8 = getc(this->_Myfile);
        if ( v8 == -1 )
          return -1;
      }
      v18 = this->_Mystr;
      if ( v18->_Myres < 0x10 )
        Ptr = &v18->_Bx;
      else
        Ptr = (std::string::_Bxty *)v18->_Bx._Ptr;
      for ( i = (int)&Ptr->_Buf[v18->_Mysize - (_DWORD)_Source]; i > 0; --i )
      {
        v21 = _Source[i - 1];
        ungetc(v21, this->_Myfile);
      }
      return (unsigned __int8)_Ch;
    }
  }
  else
  {
    result = getc(this->_Myfile);
    if ( result == -1 )
      return -1;
    return (unsigned __int8)result;
  }
}

//----- (004361D0) --------------------------------------------------------
void __thiscall std::iostream::basic_iostream<char>(std::iostream *this, std::streambuf *_Strbuf, int a3)
{
  std::ios *v4; // esi

  if ( a3 )
  {
    *(_DWORD *)this->gap0 = &std::iostream::`vbtable'{for `std::istream'};
    *(_DWORD *)this->gap8 = &std::iostream::`vbtable'{for `std::ostream'};
    *(_DWORD *)&this->gap8[4] = &std::ios::`vftable';
  }
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)] = &std::istream::`vftable';
  this->_Chcount = 0;
  *(_DWORD *)&this->gap8[*(_DWORD *)(*(_DWORD *)this->gap8 + 4)] = &std::ostream::`vftable';
  v4 = (std::ios *)&this->gap8[*(_DWORD *)(*(_DWORD *)this->gap8 + 4)];
  std::ios_base::_Init(v4);
  v4->_Mystrbuf = _Strbuf;
  v4->_Tiestr = 0;
  v4->_Fillch = std::ios::widen(v4, 32);
  if ( !v4->_Mystrbuf )
    std::ios_base::clear(v4, LOBYTE(v4->_Mystate) | 4, 0);
  v4->_Stdstr = 0;
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)] = &std::iostream::`vftable';
}
// 4DCF70: using guessed type void *std::istream::`vftable';
// 4DCF74: using guessed type void *std::iostream::`vftable';
// 4FCA0C: using guessed type void *std::ostream::`vftable';
// 4FCA14: using guessed type void *std::ios::`vftable';


//----- (004362B0) --------------------------------------------------------
char __thiscall std::filebuf::_Endwrite(std::filebuf *this)
{
  std::string::_Bxty *Ptr; // eax
  std::string::_Bxty *p_Bx; // edx
  int v4; // ebx
  int v5; // eax
  int v6; // eax
  std::string::_Bxty *v8; // eax
  std::string::_Bxty *v9; // ecx
  char *v10; // edi
  char *_Dest; // [esp+14h] [ebp-30h] BYREF
  std::string _Str; // [esp+18h] [ebp-2Ch] BYREF
  int v13; // [esp+40h] [ebp-4h]

  if ( !this->_Pcvt || !this->_Wrotesome )
    return 1;
  this->overflow(this, -1);
  _Str._Myres = 15;
  memset(&_Str._Bx, 0, 9);
  _Str._Mysize = 8;
  v13 = 0;
  while ( 1 )
  {
    Ptr = (std::string::_Bxty *)_Str._Bx._Ptr;
    p_Bx = (std::string::_Bxty *)_Str._Bx._Ptr;
    if ( _Str._Myres < 0x10 )
    {
      p_Bx = &_Str._Bx;
      Ptr = &_Str._Bx;
    }
    v4 = (int)&p_Bx->_Buf[_Str._Mysize];
    v5 = this->_Pcvt->do_unshift(this->_Pcvt, &this->_State, (char *)Ptr, &p_Bx->_Buf[_Str._Mysize], &_Dest);
    if ( v5 )
      break;
    this->_Wrotesome = 0;
LABEL_11:
    v8 = (std::string::_Bxty *)_Str._Bx._Ptr;
    v9 = (std::string::_Bxty *)_Str._Bx._Ptr;
    if ( _Str._Myres < 0x10 )
      v9 = &_Str._Bx;
    if ( _Dest != (char *)v9 )
    {
      if ( _Str._Myres < 0x10 )
        v8 = &_Str._Bx;
      v10 = (char *)(_Dest - (char *)v9);
      if ( v10 != (char *)fwrite((unsigned __int8 *)v8, (unsigned int)v10, 1u, this->_Myfile) )
        goto LABEL_19;
    }
    if ( !this->_Wrotesome )
      goto LABEL_9;
    std::string::append(&_Str, v4, 8u, 0);
  }
  v6 = v5 - 1;
  if ( !v6 )
    goto LABEL_11;
  if ( v6 == 2 )
  {
LABEL_9:
    std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>((std::pair<std::string const ,unsigned char> *)&_Str);
    return 1;
  }
LABEL_19:
  std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>((std::pair<std::string const ,unsigned char> *)&_Str);
  return 0;
}

//----- (00436400) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        int _Fill,
        bool _Val)
{
  int Fmtfl; // edx
  std::ostreambuf_iterator<char> *v7; // esi
  std::locale *v8; // eax
  const std::numpunct<char> *v9; // eax
  std::locale::_Locimp *Ptr; // esi
  std::numpunct<char> *v11; // edi
  unsigned int Refs; // eax
  void (__thiscall ***v13)(_DWORD, int); // esi
  const std::string *v14; // eax
  const std::string *v15; // eax
  std::ios_base *v16; // ebx
  int Wide; // eax
  unsigned int Mysize; // edx
  unsigned int v19; // esi
  int v20; // ecx
  std::streambuf *Strbuf; // eax
  std::string::_Bxty *p_Bx; // edi
  unsigned int v23; // ebx
  std::num_put<char,std::ostreambuf_iterator<char> > *v25; // [esp-18h] [ebp-88h]
  std::ostreambuf_iterator<char> v26; // [esp-10h] [ebp-80h]
  unsigned int v27; // [esp-4h] [ebp-74h]
  std::ostreambuf_iterator<char> v28; // [esp+Ch] [ebp-64h] BYREF
  std::ostreambuf_iterator<char> v29; // [esp+14h] [ebp-5Ch] BYREF
  std::num_put<char,std::ostreambuf_iterator<char> > *v30; // [esp+1Ch] [ebp-54h]
  std::locale v31; // [esp+20h] [ebp-50h] BYREF
  std::string v32; // [esp+24h] [ebp-4Ch] BYREF
  std::string _Str; // [esp+40h] [ebp-30h] BYREF
  int v34; // [esp+6Ch] [ebp-4h]

  Fmtfl = _Iosbase->_Fmtfl;
  v30 = this;
  if ( (Fmtfl & 0x4000) == 0 )
  {
    v7 = result;
    (*(void (__thiscall **)(std::num_put<char,std::ostreambuf_iterator<char> > *, std::ostreambuf_iterator<char> *, _DWORD, std::streambuf *, std::ios_base *, int, bool))&this->gap4[24])(
      this,
      result,
      *(_DWORD *)&_Dest._Failed,
      _Dest._Strbuf,
      _Iosbase,
      _Fill,
      _Val);
    return v7;
  }
  v8 = std::ios_base::getloc(_Iosbase, &v31);
  v34 = 0;
  v9 = std::use_facet<std::numpunct<char>>(v8);
  Ptr = v31._Ptr;
  v11 = (std::numpunct<char> *)v9;
  v34 = -1;
  if ( v31._Ptr )
  {
    std::_Lockit::_Lockit((std::_Lockit *)&v28, 0);
    Refs = Ptr->_Refs;
    if ( Refs && Refs != -1 )
      Ptr->_Refs = Refs - 1;
    v13 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
    std::_Lockit::~_Lockit((std::_Lockit *)&v28);
    if ( v13 )
      (**v13)(v13, 1);
  }
  _Str._Myres = 15;
  _Str._Mysize = 0;
  _Str._Bx._Buf[0] = 0;
  v34 = 1;
  if ( _Val )
  {
    v14 = std::numpunct<char>::truename(v11, &v32);
    LOBYTE(v34) = 2;
    std::string::assign(&_Str, v14, 0, 0xFFFFFFFF);
    LOBYTE(v34) = 1;
    if ( v32._Myres < 0x10 )
      goto LABEL_14;
  }
  else
  {
    v15 = std::numpunct<char>::falsename(v11, &v32);
    LOBYTE(v34) = 3;
    std::string::assign(&_Str, v15, 0, 0xFFFFFFFF);
    LOBYTE(v34) = 1;
    if ( v32._Myres < 0x10 )
      goto LABEL_14;
  }
  operator delete(v32._Bx._Ptr);
LABEL_14:
  v16 = _Iosbase;
  Wide = _Iosbase->_Wide;
  Mysize = _Str._Mysize;
  if ( Wide <= 0 || Wide <= _Str._Mysize )
    v19 = 0;
  else
    v19 = Wide - _Str._Mysize;
  v20 = *(_DWORD *)&_Dest._Failed;
  Strbuf = _Dest._Strbuf;
  if ( (_Iosbase->_Fmtfl & 0x1C0) != 0x40 )
  {
    v28 = _Dest;
    if ( v19 )
    {
      do
      {
        std::ostreambuf_iterator<char>::operator=(&v28, _Fill);
        --v19;
      }
      while ( v19 );
      Mysize = _Str._Mysize;
    }
    v20 = *(_DWORD *)&v28._Failed;
    Strbuf = v28._Strbuf;
    v19 = 0;
  }
  p_Bx = (std::string::_Bxty *)_Str._Bx._Ptr;
  if ( _Str._Myres < 0x10 )
    p_Bx = &_Str._Bx;
  *(_DWORD *)&v29._Failed = v20;
  v29._Strbuf = Strbuf;
  if ( Mysize )
  {
    v23 = Mysize;
    do
    {
      std::ostreambuf_iterator<char>::operator=(&v29, p_Bx->_Buf[0]);
      p_Bx = (std::string::_Bxty *)((char *)p_Bx + 1);
      --v23;
    }
    while ( v23 );
    v16 = _Iosbase;
  }
  v27 = v19;
  v7 = result;
  v26 = v29;
  v25 = v30;
  v16->_Wide = 0;
  std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(v25, result, v26, _Fill, v27);
  if ( _Str._Myres >= 0x10 )
    operator delete(_Str._Bx._Ptr);
  return v7;
}

//----- (00436650) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::ostreambuf_iterator<char>::operator=(
        std::ostreambuf_iterator<char> *this,
        unsigned __int8 _Right)
{
  std::streambuf *Strbuf; // ecx
  char **IPnext; // eax
  char **v5; // ecx
  unsigned __int8 *v6; // eax
  int v7; // eax

  Strbuf = this->_Strbuf;
  if ( !Strbuf
    || ((IPnext = Strbuf->_IPnext, !*IPnext) || *IPnext >= &(*IPnext)[*Strbuf->_IPcount]
      ? (v7 = Strbuf->overflow(Strbuf, _Right))
      : (--*Strbuf->_IPcount, v5 = Strbuf->_IPnext, v6 = (unsigned __int8 *)*v5, ++*v5, *v6 = _Right, v7 = _Right),
        v7 == -1) )
  {
    this->_Failed = 1;
  }
  return this;
}

//----- (004366B0) --------------------------------------------------------
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        unsigned __int8 _Ch,
        unsigned int _Count)
{
  unsigned int v5; // edi
  char **IPnext; // eax
  int *IPcount; // edx
  char **v8; // eax
  unsigned __int8 *v9; // ecx
  int v10; // eax
  std::ostreambuf_iterator<char> *v11; // eax

  if ( _Count )
  {
    v5 = _Count;
    do
    {
      if ( !_Dest._Strbuf )
        goto LABEL_9;
      IPnext = _Dest._Strbuf->_IPnext;
      if ( *IPnext && (IPcount = _Dest._Strbuf->_IPcount, *IPnext < &(*IPnext)[*IPcount]) )
      {
        --*IPcount;
        v8 = _Dest._Strbuf->_IPnext;
        v9 = (unsigned __int8 *)(*v8)++;
        *v9 = _Ch;
        v10 = _Ch;
      }
      else
      {
        v10 = _Dest._Strbuf->overflow(_Dest._Strbuf, _Ch);
      }
      if ( v10 == -1 )
LABEL_9:
        _Dest._Failed = 1;
      --v5;
    }
    while ( v5 );
  }
  v11 = result;
  *result = _Dest;
  return v11;
}

//----- (00436720) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::falsename(std::numpunct<char> *this, std::string *result)
{
  this->do_falsename(this, result);
  return result;
}

//----- (00436740) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::truename(std::numpunct<char> *this, std::string *result)
{
  this->do_truename(this, result);
  return result;
}

//----- (00436760) --------------------------------------------------------
const std::numpunct<char> *__cdecl std::use_facet<std::numpunct<char>>(std::locale *_Loc)
{
  const std::locale::facet *v1; // esi
  std::locale::facet *v2; // edi
  unsigned int Refs; // eax
  const std::locale::facet *_Psave; // [esp+0h] [ebp-24h] BYREF
  std::_Lockit v6; // [esp+4h] [ebp-20h] BYREF
  std::_Lockit _Lock; // [esp+8h] [ebp-1Ch] BYREF
  bad_cast pExceptionObject; // [esp+Ch] [ebp-18h] BYREF
  int v9; // [esp+20h] [ebp-4h]

  std::_Lockit::_Lockit(&_Lock, 0);
  _Psave = std::_Facetptr<std::numpunct<char>>::_Psave;
  v9 = 0;
  if ( !std::numpunct<char>::id._Id )
  {
    std::_Lockit::_Lockit(&v6, 0);
    if ( !std::numpunct<char>::id._Id )
      std::numpunct<char>::id._Id = ++std::locale::id::_Id_cnt;
    std::_Lockit::~_Lockit(&v6);
  }
  v1 = std::locale::_Getfacet(_Loc, std::numpunct<char>::id._Id);
  if ( !v1 )
  {
    v1 = _Psave;
    if ( !_Psave )
    {
      if ( std::numpunct<char>::_Getcat((std::numpunct<char> **)&_Psave) == -1 )
      {
        bad_cast::bad_cast(&pExceptionObject, "bad cast");
        _CxxThrowException(&pExceptionObject, &_TI2_AVbad_cast__);
      }
      v1 = _Psave;
      std::_Facetptr<std::numpunct<char>>::_Psave = _Psave;
      v2 = (std::locale::facet *)_Psave;
      std::_Lockit::_Lockit(&v6, 0);
      Refs = v2->_Refs;
      if ( Refs != -1 )
        v2->_Refs = Refs + 1;
      std::_Lockit::~_Lockit(&v6);
      std::locale::facet::_Register(v2);
    }
  }
  v9 = -1;
  std::_Lockit::~_Lockit(&_Lock);
  return (const std::numpunct<char> *)v1;
}

//----- (00436870) --------------------------------------------------------
unsigned int __cdecl std::numpunct<char>::_Getcat(std::numpunct<char> **_Ppf)
{
  std::numpunct<char> *v1; // eax
  std::numpunct<char> *v2; // esi
  const std::_Locinfo *v3; // eax
  std::_Locinfo v5; // [esp+8h] [ebp-80h] BYREF
  int v6; // [esp+84h] [ebp-4h]

  if ( _Ppf && !*_Ppf )
  {
    v1 = (std::numpunct<char> *)operator new((tagHeader *)0x18);
    v2 = v1;
    v6 = 0;
    if ( v1 )
    {
      v1->_Refs = 0;
      LOBYTE(v6) = 1;
      v1->__vftable = (std::numpunct<char>_vtbl *)&std::numpunct<char>::`vftable';
      std::_Locinfo::_Locinfo(&v5, "C");
      LOBYTE(v6) = 2;
      std::numpunct<char>::_Init(v2, v3);
      LOBYTE(v6) = 1;
      std::_Locinfo::~_Locinfo(&v5);
    }
    else
    {
      v2 = 0;
    }
    *_Ppf = v2;
  }
  return 4;
}
// 4368E8: variable 'v3' is possibly undefined
// 4DD248: using guessed type void *std::numpunct<char>::`vftable';

//----- (00436920) --------------------------------------------------------
char __thiscall std::numpunct<char>::do_decimal_point(std::numpunct<char> *this)
{
  return this->_Dp;
}

//----- (00436930) --------------------------------------------------------
char __thiscall std::numpunct<char>::do_thousands_sep(std::numpunct<char> *this)
{
  return this->_Kseparator;
}

//----- (00436940) --------------------------------------------------------
std::numpunct<char> *__thiscall std::numpunct<char>::`scalar deleting destructor'(std::numpunct<char> *this, char a2)
{
  std::numpunct<char>::~numpunct<char>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00436960) --------------------------------------------------------
void __thiscall std::numpunct<char>::~numpunct<char>(std::numpunct<char> *this)
{
  this->__vftable = (std::numpunct<char>_vtbl *)&std::numpunct<char>::`vftable';
  std::numpunct<char>::_Tidy(this);
  this->__vftable = (std::numpunct<char>_vtbl *)&std::locale::facet::`vftable';
}
// 4DD248: using guessed type void *std::numpunct<char>::`vftable';
// 4FC98C: using guessed type void *std::locale::facet::`vftable';

//----- (004369B0) --------------------------------------------------------
void __thiscall std::numpunct<char>::_Tidy(std::numpunct<char> *this)
{
  operator delete[]((void *)this->_Grouping);
  operator delete[]((void *)this->_Falsename);
  operator delete[]((void *)this->_Truename);
}

//----- (004369E0) --------------------------------------------------------
void __thiscall std::numpunct<char>::_Init(std::numpunct<char> *this, const std::_Locinfo *_Lobj)
{
  lconv *v3; // edi
  _Cvtvec v4; // rax
  const char *grouping; // ecx
  int v6; // [esp+0h] [ebp-30h] BYREF
  _Cvtvec v7; // [esp+Ch] [ebp-24h]
  _Cvtvec __formal; // [esp+14h] [ebp-1Ch]
  std::numpunct<char> *v9; // [esp+1Ch] [ebp-14h]
  int *v10; // [esp+20h] [ebp-10h]
  int v11; // [esp+2Ch] [ebp-4h]

  v10 = &v6;
  v9 = this;
  v3 = localeconv();
  this->_Grouping = 0;
  this->_Falsename = 0;
  this->_Truename = 0;
  v11 = 0;
  v4 = _Getcvt();
  grouping = v3->grouping;
  __formal = v4;
  this->_Grouping = std::_Maklocstr<char>(grouping);
  __formal = _Getcvt();
  this->_Falsename = std::_Maklocstr<char>("false");
  v7 = _Getcvt();
  this->_Truename = std::_Maklocstr<char>("true");
  _Getcvt();
  this->_Dp = *v3->decimal_point;
  _Getcvt();
  this->_Kseparator = *v3->thousands_sep;
}

//----- (00436AC0) --------------------------------------------------------
char *__cdecl std::_Maklocstr<char>(const char *_Ptr)
{
  const char *v1; // edi
  unsigned int v2; // kr00_4
  unsigned int v3; // esi
  char *result; // eax
  char *v5; // ecx

  v1 = _Ptr;
  v2 = strlen(_Ptr);
  v3 = v2 + 1;
  result = (char *)operator new[](v2 + 1);
  v5 = result;
  if ( v2 != -1 )
  {
    do
    {
      *v5++ = *v1++;
      --v3;
    }
    while ( v3 );
  }
  return result;
}

//----- (00436B00) --------------------------------------------------------
int __thiscall std::filebuf::overflow(std::filebuf *this, int _Meta)
{
  char *v4; // ecx
  int *IPcount; // eax
  char **IPnext; // esi
  char *v7; // eax
  std::string *Mystr; // ecx
  std::string::_Bxty *Ptr; // eax
  unsigned int Myres; // ebp
  std::string::_Bxty *p_Bx; // edx
  int v12; // eax
  std::string::_Bxty *v13; // ecx
  char *v14; // edi
  bool v15; // zf
  char _Ch; // [esp+1Dh] [ebp-35h] BYREF
  char *_Dest; // [esp+1Eh] [ebp-34h] BYREF
  const char *_Source; // [esp+22h] [ebp-30h] BYREF
  std::string _Str; // [esp+26h] [ebp-2Ch] BYREF
  int v20; // [esp+4Eh] [ebp-4h]

  if ( _Meta == -1 )
    return 0;
  v4 = *this->_IPnext;
  if ( v4 )
  {
    IPcount = this->_IPcount;
    if ( v4 < &v4[*IPcount] )
    {
      --*IPcount;
      IPnext = this->_IPnext;
      v7 = (*IPnext)++;
      *v7 = _Meta;
      return _Meta;
    }
  }
  if ( !this->_Myfile )
    return -1;
  if ( this->_Pcvt )
  {
    Mystr = this->_Mystr;
    _Ch = _Meta;
    std::string::erase(Mystr, 0, 0xFFFFFFFF);
    _Str._Myres = 15;
    memset(&_Str._Bx, 0, 9);
    _Str._Mysize = 8;
    v20 = 0;
LABEL_12:
    Ptr = (std::string::_Bxty *)_Str._Bx._Ptr;
    Myres = _Str._Myres;
    while ( 1 )
    {
      p_Bx = Ptr;
      if ( Myres < 0x10 )
      {
        p_Bx = &_Str._Bx;
        Ptr = &_Str._Bx;
      }
      v12 = this->_Pcvt->do_out(
              this->_Pcvt,
              &this->_State,
              &_Ch,
              (const char *)&_Dest,
              &_Source,
              (char *)Ptr,
              &p_Bx->_Buf[_Str._Mysize],
              &_Dest);
      if ( v12 < 0 )
        break;
      if ( v12 > 1 )
      {
        if ( v12 == 3 && fputc(_Ch, this->_Myfile) != -1 )
        {
LABEL_29:
          std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>((std::pair<std::string const ,unsigned char> *)&_Str);
          return _Meta;
        }
        break;
      }
      Myres = _Str._Myres;
      Ptr = (std::string::_Bxty *)_Str._Bx._Ptr;
      v13 = (std::string::_Bxty *)_Str._Bx._Ptr;
      if ( _Str._Myres < 0x10 )
        v13 = &_Str._Bx;
      v14 = (char *)(_Dest - (char *)v13);
      if ( _Dest != (char *)v13 )
      {
        if ( _Str._Myres < 0x10 )
          Ptr = &_Str._Bx;
        if ( v14 != (char *)fwrite((unsigned __int8 *)Ptr, 1u, (unsigned int)v14, this->_Myfile) )
          break;
        Ptr = (std::string::_Bxty *)_Str._Bx._Ptr;
        Myres = _Str._Myres;
      }
      v15 = _Source == &_Ch;
      this->_Wrotesome = 1;
      if ( !v15 )
        goto LABEL_29;
      if ( !v14 )
      {
        std::string::append(&_Str, (int)&this->_State, 8u, 0);
        goto LABEL_12;
      }
    }
    std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>((std::pair<std::string const ,unsigned char> *)&_Str);
    return -1;
  }
  else
  {
    if ( fputc(_Meta, this->_Myfile) == -1 )
      return -1;
    return _Meta;
  }
}

//----- (00436D00) --------------------------------------------------------
std::fpos<int> *__thiscall std::filebuf::seekoff(
        std::filebuf *this,
        std::fpos<int> *result,
        LONG _Off,
        unsigned int _Way,
        int __formal)
{
  char **IGnext; // ecx
  signed int i; // edi
  std::string *Mystr; // eax
  std::string::_Bxty *p_Bx; // eax
  char *p_Mychar; // eax
  std::fpos<int> *v11; // eax
  __int64 v12; // kr00_8
  __int64 _Fileposition; // [esp+8h] [ebp-8h] BYREF

  IGnext = this->_IGnext;
  if ( &(*IGnext)[*this->_IGcount] > *IGnext && *IGnext == &this->_Mychar && _Way == 1 )
  {
    if ( this->_Pcvt )
    {
      for ( i = this->_Mystr->_Mysize; i > 0; ungetc(p_Bx->_Buf[i], this->_Myfile) )
      {
        Mystr = this->_Mystr;
        --i;
        if ( Mystr->_Myres < 0x10 )
          p_Bx = &Mystr->_Bx;
        else
          p_Bx = (std::string::_Bxty *)Mystr->_Bx._Ptr;
      }
      std::string::erase(this->_Mystr, 0, 0xFFFFFFFF);
      this->_State = this->_State0;
    }
    else
    {
      --_Off;
    }
  }
  if ( !this->_Myfile
    || !std::filebuf::_Endwrite(this)
    || (_Off || _Way != 1) && fseek(this->_Myfile, _Off, _Way)
    || fgetpos(this->_Myfile, &_Fileposition) )
  {
    v11 = result;
    result->_Myoff = std::_BADOFF;
    result->_Fpos = std::_Fpz;
    result->_Mystate = std::fpos<int>::_Stz;
  }
  else
  {
    p_Mychar = &this->_Mychar;
    if ( *(std::filebuf **)this->_IGnext == (std::filebuf *)&this->_Mychar )
    {
      *this->_IGfirst = p_Mychar;
      *this->_IGnext = p_Mychar;
      *this->_IGcount = 0;
    }
    v11 = result;
    v12 = _Fileposition;
    result->_Mystate = this->_State;
    result->_Myoff = 0;
    result->_Fpos = v12;
  }
  return v11;
}

//----- (00436E40) --------------------------------------------------------
std::fpos<int> *__thiscall std::filebuf::seekpos(
        std::filebuf *this,
        std::fpos<int> *result,
        std::fpos<int> _Pos,
        int __formal)
{
  std::string *Mystr; // ecx
  char *p_Mychar; // eax
  std::fpos<int> *v7; // eax
  __int64 v8; // kr00_8
  __int64 _Fileposition; // [esp+4h] [ebp-8h] BYREF

  _Fileposition = _Pos._Fpos;
  if ( !this->_Myfile
    || !std::filebuf::_Endwrite(this)
    || fsetpos(this->_Myfile, &_Fileposition)
    || _Pos._Myoff && fseek(this->_Myfile, _Pos._Myoff, 1u)
    || fgetpos(this->_Myfile, &_Fileposition) )
  {
    v7 = result;
    result->_Myoff = std::_BADOFF;
    result->_Fpos = std::_Fpz;
    result->_Mystate = std::fpos<int>::_Stz;
  }
  else
  {
    Mystr = this->_Mystr;
    if ( Mystr )
    {
      this->_State = _Pos._Mystate;
      std::string::erase(Mystr, 0, 0xFFFFFFFF);
    }
    p_Mychar = &this->_Mychar;
    if ( *(std::filebuf **)this->_IGnext == (std::filebuf *)&this->_Mychar )
    {
      *this->_IGfirst = p_Mychar;
      *this->_IGnext = p_Mychar;
      *this->_IGcount = 0;
    }
    v7 = result;
    v8 = _Fileposition;
    result->_Mystate = this->_State;
    result->_Myoff = 0;
    result->_Fpos = v8;
  }
  return v7;
}

//----- (00436F50) --------------------------------------------------------
std::filebuf *__thiscall std::filebuf::close(std::filebuf *this)
{
  int v2; // edx

  if ( !this->_Myfile || !std::filebuf::_Endwrite(this) || fclose(this->_Myfile) )
    return 0;
  this->_Closef = 0;
  this->_Wrotesome = 0;
  std::streambuf::_Init(this);
  this->_Myfile = 0;
  this->_State = `std::filebuf::_Init'::`2'::_Stinit;
  v2 = `std::filebuf::_Init'::`2'::_Stinit;
  this->_Pcvt = 0;
  this->_State0 = v2;
  return this;
}
// 523C50: using guessed type int `std::filebuf::_Init'::`2'::_Stinit;

//----- (00436FB0) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        int _Val)
{
  char *v7; // eax
  size_t v8; // eax
  char _Fmt[8]; // [esp+8h] [ebp-4Ch] BYREF
  char _Buf[64]; // [esp+10h] [ebp-44h] BYREF

  v7 = std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(this, _Fmt, "ld", _Iosbase->_Fmtfl);
  v8 = sprintf(_Buf, v7, _Val);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(this, result, _Dest, _Iosbase, _Fill, _Buf, v8);
  return result;
}

//----- (00437030) --------------------------------------------------------
char *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        char *_Fmt,
        char *_Spec,
        __int16 _Flags)
{
  char *result; // eax
  char *v5; // edx
  _BYTE *v6; // edx
  int v7; // esi
  char *v8; // edx
  char v9; // cl

  result = _Fmt;
  *_Fmt = 37;
  v5 = _Fmt + 1;
  if ( (_Flags & 0x20) != 0 )
  {
    *v5 = 43;
    v5 = _Fmt + 2;
  }
  if ( (_Flags & 8) != 0 )
    *v5++ = 35;
  if ( *_Spec == 76 )
  {
    *v5 = 73;
    v6 = v5 + 1;
    *v6 = 54;
    v5 = v6 + 1;
    *v5 = 52;
  }
  else
  {
    *v5 = *_Spec;
  }
  v7 = _Flags & 0xE00;
  v8 = v5 + 1;
  if ( v7 == 1024 )
  {
    *v8 = 111;
    v8[1] = 0;
  }
  else
  {
    if ( v7 == 2048 )
      v9 = ~(8 * _Flags) & 0x20 | 0x58;
    else
      v9 = _Spec[1];
    *v8 = v9;
    v8[1] = 0;
  }
  return result;
}

//----- (004370C0) --------------------------------------------------------
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        char *_Buf,
        size_t _Count)
{
  std::locale *v7; // eax
  const std::numpunct<char> *v8; // eax
  std::locale::_Locimp *Ptr; // esi
  std::numpunct<char> *v10; // edi
  unsigned int Refs; // eax
  void (__thiscall ***v12)(_DWORD, int); // esi
  char *v13; // ecx
  char v14; // al
  bool v15; // zf
  char v16; // al
  std::string::_Bxty *v17; // ebx
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v19; // eax
  char v20; // al
  size_t v21; // ebp
  unsigned int v22; // esi
  char *v23; // edi
  int Wide; // esi
  unsigned int v25; // esi
  int v26; // eax
  std::streambuf *v27; // eax
  unsigned int v28; // ecx
  unsigned int v29; // eax
  unsigned __int8 *v30; // ebx
  std::ostreambuf_iterator<char> *v31; // eax
  std::streambuf *Strbuf; // ebx
  unsigned __int8 v33; // al
  std::ostreambuf_iterator<char> *v34; // eax
  unsigned int v35; // ecx
  std::streambuf *v36; // eax
  unsigned int _Prefix; // [esp+10h] [ebp-44h]
  std::_Lockit v39; // [esp+14h] [ebp-40h] BYREF
  std::ostreambuf_iterator<char> v40; // [esp+18h] [ebp-3Ch] BYREF
  std::locale v41; // [esp+20h] [ebp-34h] BYREF
  const std::numpunct<char> *_Punct_fac; // [esp+24h] [ebp-30h]
  std::string _Grouping; // [esp+28h] [ebp-2Ch] BYREF
  int v44; // [esp+50h] [ebp-4h]

  v7 = std::ios_base::getloc(_Iosbase, &v41);
  v44 = 0;
  v8 = std::use_facet<std::numpunct<char>>(v7);
  Ptr = v41._Ptr;
  v10 = (std::numpunct<char> *)v8;
  _Punct_fac = v8;
  v44 = -1;
  if ( v41._Ptr )
  {
    std::_Lockit::_Lockit(&v39, 0);
    Refs = Ptr->_Refs;
    if ( Refs && Refs != -1 )
      Ptr->_Refs = Refs - 1;
    v12 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
    std::_Lockit::~_Lockit(&v39);
    if ( v12 )
      (**v12)(v12, 1);
  }
  std::numpunct<char>::grouping(v10, &_Grouping);
  v13 = _Buf;
  v14 = *_Buf;
  v15 = *_Buf == 43;
  v44 = 1;
  if ( v15 || v14 == 45 )
  {
    _Prefix = 1;
  }
  else if ( v14 == 48 && ((v16 = _Buf[1], v16 == 120) || v16 == 88) )
  {
    _Prefix = 2;
  }
  else
  {
    _Prefix = 0;
  }
  v17 = (std::string::_Bxty *)_Grouping._Bx._Ptr;
  p_Bx = (std::string::_Bxty *)_Grouping._Bx._Ptr;
  if ( _Grouping._Myres < 0x10 )
    p_Bx = &_Grouping._Bx;
  if ( p_Bx->_Buf[0] == 127 )
    goto LABEL_35;
  v19 = (std::string::_Bxty *)_Grouping._Bx._Ptr;
  if ( _Grouping._Myres < 0x10 )
    v19 = &_Grouping._Bx;
  if ( v19->_Buf[0] <= 0 )
  {
LABEL_35:
    v21 = _Count;
  }
  else
  {
    if ( _Grouping._Myres < 0x10 )
      v17 = &_Grouping._Bx;
    v20 = v17->_Buf[0];
    v21 = _Count;
    v22 = _Count;
    if ( v17->_Buf[0] != 127 )
    {
      while ( v20 > 0 && v20 < v22 - _Prefix )
      {
        v22 -= v20;
        v23 = &v13[v22];
        memmove((unsigned __int8 *)&v13[v22 + 1], (unsigned __int8 *)&v13[v22], v21 - v22 + 1);
        *v23 = 44;
        ++v21;
        if ( v17->_Buf[1] > 0 )
          v17 = (std::string::_Bxty *)((char *)v17 + 1);
        v20 = v17->_Buf[0];
        if ( v17->_Buf[0] == 127 )
        {
          v13 = _Buf;
          break;
        }
        v13 = _Buf;
      }
    }
  }
  Wide = _Iosbase->_Wide;
  if ( Wide <= 0 || Wide <= v21 )
    v25 = 0;
  else
    v25 = Wide - v21;
  v26 = _Iosbase->_Fmtfl & 0x1C0;
  if ( v26 == 64 )
  {
    Strbuf = _Dest._Strbuf;
  }
  else
  {
    v15 = v26 == 256;
    v27 = _Dest._Strbuf;
    if ( v15 )
    {
      v40 = _Dest;
      v29 = _Prefix;
      v30 = (unsigned __int8 *)v13;
      if ( _Prefix )
      {
        v39._Locktype = _Prefix;
        do
        {
          std::ostreambuf_iterator<char>::operator=(&v40, *v30++);
          --v39._Locktype;
        }
        while ( v39._Locktype );
        v13 = _Buf;
        v29 = _Prefix;
      }
      _Buf = &v13[v29];
      v28 = *(_DWORD *)&v40._Failed;
      v21 -= v29;
      v27 = v40._Strbuf;
    }
    else
    {
      v28 = *(_DWORD *)&_Dest._Failed;
    }
    v31 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
            this,
            &v40,
            (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v27, v28),
            _Fill,
            v25);
    Strbuf = v31->_Strbuf;
    *(_DWORD *)&_Dest._Failed = *(_DWORD *)&v31->_Failed;
    v25 = 0;
  }
  v33 = _Punct_fac->do_thousands_sep((std::numpunct<char> *)_Punct_fac);
  v34 = std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(
          this,
          &v40,
          (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)Strbuf, *(unsigned int *)&_Dest._Failed),
          _Buf,
          v21,
          v33);
  v35 = *(_DWORD *)&v34->_Failed;
  v36 = v34->_Strbuf;
  _Iosbase->_Wide = 0;
  std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
    this,
    result,
    (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v36, v35),
    _Fill,
    v25);
  if ( _Grouping._Myres >= 0x10 )
    operator delete(_Grouping._Bx._Ptr);
  return result;
}

//----- (00437360) --------------------------------------------------------
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        char *_Ptr,
        size_t _Count,
        unsigned __int8 _Kseparator)
{
  std::streambuf *Strbuf; // ebx
  unsigned int v7; // edi
  _BYTE *v8; // eax
  size_t v9; // esi
  unsigned int v10; // edx
  std::streambuf *v11; // ecx
  size_t v12; // edi
  bool v13; // zf
  std::ostreambuf_iterator<char> *v14; // eax
  std::ostreambuf_iterator<char> *v15; // eax
  unsigned __int8 *v16; // [esp+Ch] [ebp-14h]
  std::ostreambuf_iterator<char> v17; // [esp+10h] [ebp-10h] BYREF
  std::ostreambuf_iterator<char> v18; // [esp+18h] [ebp-8h] BYREF
  const char *_Ptra; // [esp+38h] [ebp+18h]
  unsigned int _Counta; // [esp+3Ch] [ebp+1Ch]

  Strbuf = _Dest._Strbuf;
  v7 = *(_DWORD *)&_Dest._Failed;
  while ( 1 )
  {
    v8 = memchr(_Ptr, 44, _Count);
    if ( v8 )
      v9 = v8 - _Ptr;
    else
      v9 = _Count;
    v16 = (unsigned __int8 *)_Ptr;
    v10 = v7;
    v11 = Strbuf;
    *(_DWORD *)&v17._Failed = v7;
    v17._Strbuf = Strbuf;
    if ( v9 )
    {
      v12 = v9;
      do
      {
        std::ostreambuf_iterator<char>::operator=(&v17, *v16);
        --v12;
        ++v16;
      }
      while ( v12 );
      v11 = v17._Strbuf;
      v10 = *(_DWORD *)&v17._Failed;
    }
    _Ptra = &_Ptr[v9];
    v13 = _Count == v9;
    v7 = v10;
    Strbuf = v11;
    _Counta = _Count - v9;
    if ( v13 )
      break;
    if ( _Kseparator )
    {
      v14 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
              this,
              &v18,
              (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v11, v10),
              _Kseparator,
              1u);
      v7 = *(_DWORD *)&v14->_Failed;
      Strbuf = v14->_Strbuf;
    }
    _Ptr = (char *)(_Ptra + 1);
    _Count = _Counta - 1;
  }
  v15 = result;
  *(_DWORD *)&result->_Failed = v10;
  result->_Strbuf = v11;
  return v15;
}

//----- (00437430) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::grouping(std::numpunct<char> *this, std::string *result)
{
  this->do_grouping(this, result);
  return result;
}

//----- (00437450) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        unsigned int _Val)
{
  char *v7; // eax
  size_t v8; // eax
  char _Fmt[8]; // [esp+8h] [ebp-4Ch] BYREF
  char _Buf[64]; // [esp+10h] [ebp-44h] BYREF

  v7 = std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(this, _Fmt, "lu", _Iosbase->_Fmtfl);
  v8 = sprintf(_Buf, v7, _Val);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(this, result, _Dest, _Iosbase, _Fill, _Buf, v8);
  return result;
}

//----- (004374D0) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        __int64 _Val)
{
  char *v7; // eax
  size_t v8; // eax
  char _Fmt[8]; // [esp+8h] [ebp-4Ch] BYREF
  char _Buf[64]; // [esp+10h] [ebp-44h] BYREF

  v7 = std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(this, _Fmt, "Ld", _Iosbase->_Fmtfl);
  v8 = sprintf(_Buf, v7, _Val);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(this, result, _Dest, _Iosbase, _Fill, _Buf, v8);
  return result;
}

//----- (00437550) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        unsigned __int64 _Val)
{
  char *v7; // eax
  size_t v8; // eax
  char _Fmt[8]; // [esp+8h] [ebp-4Ch] BYREF
  char _Buf[64]; // [esp+10h] [ebp-44h] BYREF

  v7 = std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(this, _Fmt, "Lu", _Iosbase->_Fmtfl);
  v8 = sprintf(_Buf, v7, _Val);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(this, result, _Dest, _Iosbase, _Fill, _Buf, v8);
  return result;
}

//----- (004375D0) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        double _Val)
{
  int Prec; // eax
  int v7; // ebp
  int Fmtfl; // ecx
  double v9; // st7
  signed int v10; // esi
  unsigned int v11; // ebx
  unsigned int v12; // edi
  char v13; // dl
  char *v14; // eax
  unsigned int v15; // eax
  char _Fmt[8]; // [esp+1Ch] [ebp-78h] BYREF
  char _Buf[108]; // [esp+24h] [ebp-70h] BYREF

  Prec = _Iosbase->_Prec;
  if ( Prec <= 0 && (_Iosbase->_Fmtfl & 0x2000) == 0 )
    Prec = 6;
  v7 = 36;
  if ( Prec <= 36 )
    v7 = Prec;
  Fmtfl = _Iosbase->_Fmtfl;
  v9 = _Val;
  v10 = Prec - v7;
  v11 = 0;
  v12 = 0;
  if ( (Fmtfl & 0x3000) == 0x2000 )
  {
    if ( _Val >= 0.0 )
    {
      v13 = 0;
    }
    else
    {
      v13 = 1;
      v9 = -_Val;
    }
    for ( ; v9 >= 1.0e35; v11 += 10 )
    {
      if ( v11 >= 0x1388 )
        break;
      v9 = v9 * 1.0e-10;
    }
    if ( v9 > 0.0 && v10 >= 10 )
    {
      do
      {
        if ( v9 > 1.0e-35 )
          break;
        if ( v12 >= 0x1388 )
          break;
        v9 = v9 * 1.0e10;
        v10 -= 10;
        v12 += 10;
      }
      while ( v10 >= 10 );
    }
    if ( v13 )
      v9 = -v9;
  }
  v14 = std::num_put<char,std::ostreambuf_iterator<char>>::_Ffmt(this, _Fmt, 0, Fmtfl);
  v15 = sprintf(_Buf, v14, v7, v9);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Fput(
    this,
    result,
    _Dest,
    _Iosbase,
    _Fill,
    (std::string::_Bxty *)_Buf,
    v11,
    v12,
    v10,
    v15);
  return result;
}

//----- (00437740) --------------------------------------------------------
char *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Ffmt(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        char *_Fmt,
        char _Spec,
        __int16 _Flags)
{
  char *result; // eax
  char *v5; // ecx
  _BYTE *v6; // ecx
  _BYTE *v7; // ecx

  result = _Fmt;
  *_Fmt = 37;
  v5 = _Fmt + 1;
  if ( (_Flags & 0x20) != 0 )
  {
    *v5 = 43;
    v5 = _Fmt + 2;
  }
  if ( (_Flags & 0x10) != 0 )
    *v5++ = 35;
  *v5 = 46;
  v6 = v5 + 1;
  *v6 = 42;
  v7 = v6 + 1;
  if ( _Spec )
    *v7++ = _Spec;
  if ( (_Flags & 0x3000) == 0x2000 )
    *v7 = 102;
  else
    *v7 = 2 * ((_Flags & 0x3000) != 4096) + 101;
  v7[1] = 0;
  return result;
}

//----- (004377A0) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Fput(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        std::string::_Bxty *_Buf,
        unsigned int _Beforepoint,
        unsigned int _Afterpoint,
        unsigned int _Trailing,
        unsigned int _Count)
{
  std::locale *v10; // eax
  const std::numpunct<char> *v11; // eax
  std::locale::_Locimp *Ptr; // esi
  std::numpunct<char> *v13; // edi
  unsigned int Refs; // eax
  void (__thiscall ***v15)(_DWORD, int); // esi
  std::numpunct<char>_vtbl *v16; // edx
  std::string::_Bxty *v17; // edi
  char v18; // al
  bool v19; // zf
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v21; // eax
  _BYTE *v22; // eax
  _BYTE *v23; // eax
  unsigned int v24; // esi
  std::string::_Bxty *v25; // edi
  std::string::_Bxty *v26; // eax
  unsigned int v27; // eax
  unsigned int v28; // esi
  char i; // al
  int Wide; // ecx
  unsigned int v31; // eax
  int v32; // eax
  std::ostreambuf_iterator<char> *v33; // eax
  std::streambuf *v34; // eax
  unsigned __int8 v35; // cl
  unsigned int v36; // ecx
  const std::numpunct<char> *v37; // ebp
  std::streambuf *Strbuf; // ebx
  _BYTE *v39; // eax
  std::ostreambuf_iterator<char> *v40; // eax
  std::ostreambuf_iterator<char> *v41; // eax
  unsigned int v42; // ebx
  std::streambuf *v43; // ebp
  unsigned __int8 v44; // al
  std::ostreambuf_iterator<char> *v45; // eax
  std::ostreambuf_iterator<char> *v46; // eax
  _BYTE *v47; // eax
  std::ostreambuf_iterator<char> *v48; // eax
  std::ostreambuf_iterator<char> *v49; // eax
  const char *v50; // ecx
  const std::numpunct<char> *v51; // edx
  std::streambuf *v52; // eax
  std::ostreambuf_iterator<char> *v53; // eax
  std::ostreambuf_iterator<char> *v54; // eax
  unsigned int v55; // ecx
  std::streambuf *v56; // eax
  unsigned __int8 v58; // [esp-4h] [ebp-88h]
  int _Enders; // [esp+10h] [ebp-74h] BYREF
  std::ostreambuf_iterator<char> _Fracoffset; // [esp+14h] [ebp-70h] OVERLAPPED BYREF
  std::_Lockit _Kseparator; // [esp+1Ch] [ebp-68h] BYREF
  const std::numpunct<char> *_Punct_fac; // [esp+20h] [ebp-64h] BYREF
  std::streambuf *v63; // [esp+24h] [ebp-60h]
  std::ostreambuf_iterator<char> v64; // [esp+28h] [ebp-5Ch] BYREF
  std::locale v65; // [esp+30h] [ebp-54h] BYREF
  std::ostreambuf_iterator<char> v66; // [esp+34h] [ebp-50h] BYREF
  std::string _Groupstring; // [esp+3Ch] [ebp-48h] BYREF
  std::string _Grouping; // [esp+58h] [ebp-2Ch] BYREF
  int v69; // [esp+80h] [ebp-4h]
  const char *_Bufa; // [esp+A0h] [ebp+1Ch]
  unsigned int _Fillcount; // [esp+A4h] [ebp+20h]

  v10 = std::ios_base::getloc(_Iosbase, &v65);
  v69 = 0;
  v11 = std::use_facet<std::numpunct<char>>(v10);
  Ptr = v65._Ptr;
  v13 = (std::numpunct<char> *)v11;
  _Punct_fac = v11;
  v69 = -1;
  if ( v65._Ptr )
  {
    std::_Lockit::_Lockit(&_Kseparator, 0);
    Refs = Ptr->_Refs;
    if ( Refs && Refs != -1 )
      Ptr->_Refs = Refs - 1;
    v15 = Ptr->_Refs != 0 ? 0 : (void (__thiscall ***)(_DWORD, int))Ptr;
    std::_Lockit::~_Lockit(&_Kseparator);
    if ( v15 )
      (**v15)(v15, 1);
  }
  std::numpunct<char>::grouping(v13, &_Grouping);
  v16 = v13->__vftable;
  v69 = 1;
  LOBYTE(_Kseparator._Locktype) = v16->do_thousands_sep(v13);
  _Groupstring._Myres = 15;
  _Groupstring._Mysize = 0;
  _Groupstring._Bx._Buf[0] = 0;
  v17 = _Buf;
  v18 = _Buf->_Buf[0];
  v19 = _Buf->_Buf[0] == 43;
  LOBYTE(v69) = 2;
  if ( v19 || (*(_DWORD *)&_Fracoffset._Failed = 0, v18 == 45) )
    *(_DWORD *)&_Fracoffset._Failed = 1;
  LOBYTE(_Enders) = *localeconv()->decimal_point;
  *(_WORD *)((char *)&_Enders + 1) = 101;
  p_Bx = (std::string::_Bxty *)_Grouping._Bx._Ptr;
  if ( _Grouping._Myres < 0x10 )
    p_Bx = &_Grouping._Bx;
  if ( p_Bx->_Buf[0] != 127 )
  {
    v21 = (std::string::_Bxty *)_Grouping._Bx._Ptr;
    if ( _Grouping._Myres < 0x10 )
      v21 = &_Grouping._Bx;
    if ( v21->_Buf[0] > 0 )
    {
      std::string::append(&_Groupstring, _Buf->_Buf, _Count);
      v22 = memchr(_Buf, 101, _Count);
      if ( v22 )
        std::string::insert(&_Groupstring, v22 - (_BYTE *)_Buf, _Trailing, 48);
      else
        std::string::append(&_Groupstring, 16, _Trailing, 48);
      v23 = memchr(_Buf, (char)_Enders, _Count);
      if ( v23 )
      {
        v24 = v23 - (_BYTE *)_Buf;
        std::string::insert(&_Groupstring, v23 - (_BYTE *)_Buf + 1, _Afterpoint, 48);
        std::string::insert(&_Groupstring, v24, _Beforepoint, 48);
      }
      else
      {
        std::string::append(&_Groupstring, 16, _Beforepoint, 48);
      }
      v25 = (std::string::_Bxty *)_Grouping._Bx._Ptr;
      if ( _Grouping._Myres < 0x10 )
        v25 = &_Grouping._Bx;
      v26 = (std::string::_Bxty *)_Groupstring._Bx._Ptr;
      if ( _Groupstring._Myres < 0x10 )
        v26 = &_Groupstring._Bx;
      strcspn((unsigned __int8 *)v26, (unsigned __int8 *)&_Enders);
      v28 = v27;
      for ( i = v25->_Buf[0]; v25->_Buf[0] != 127; i = v25->_Buf[0] )
      {
        if ( i <= 0 || i >= v28 - *(_DWORD *)&_Fracoffset._Failed )
          break;
        v28 -= i;
        std::string::insert(&_Groupstring, v28, 1u, 44);
        if ( v25->_Buf[1] > 0 )
          v25 = (std::string::_Bxty *)((char *)v25 + 1);
      }
      v17 = (std::string::_Bxty *)_Groupstring._Bx._Ptr;
      if ( _Groupstring._Myres < 0x10 )
        v17 = &_Groupstring._Bx;
      _Beforepoint = 0;
      _Afterpoint = 0;
      _Trailing = 0;
      _Count = _Groupstring._Mysize;
    }
  }
  Wide = _Iosbase->_Wide;
  v31 = _Count + _Trailing + _Beforepoint + _Afterpoint;
  if ( Wide <= 0 || Wide <= v31 )
    _Bufa = 0;
  else
    _Bufa = (const char *)(Wide - v31);
  v32 = _Iosbase->_Fmtfl & 0x1C0;
  if ( v32 == 64 )
  {
    Strbuf = _Dest._Strbuf;
    v37 = *(const std::numpunct<char> **)&_Dest._Failed;
  }
  else
  {
    if ( v32 == 256 )
    {
      v34 = _Dest._Strbuf;
      if ( *(_DWORD *)&_Fracoffset._Failed )
      {
        v35 = v17->_Buf[0];
        _Fracoffset = _Dest;
        std::ostreambuf_iterator<char>::operator=(&_Fracoffset, v35);
        v34 = _Fracoffset._Strbuf;
        v36 = *(_DWORD *)&_Fracoffset._Failed;
        v17 = (std::string::_Bxty *)((char *)v17 + 1);
        --_Count;
      }
      else
      {
        v36 = *(_DWORD *)&_Dest._Failed;
      }
      v33 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
              this,
              &_Fracoffset,
              (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v34, v36),
              _Fill,
              (unsigned int)_Bufa);
    }
    else
    {
      v33 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
              this,
              &_Fracoffset,
              _Dest,
              _Fill,
              (unsigned int)_Bufa);
    }
    v37 = *(const std::numpunct<char> **)&v33->_Failed;
    Strbuf = v33->_Strbuf;
    _Bufa = 0;
  }
  v39 = memchr(v17, (char)_Enders, _Count);
  if ( v39 )
  {
    *(_DWORD *)&_Fracoffset._Failed = v39 - (_BYTE *)v17 + 1;
    v40 = std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(
            this,
            &v66,
            (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)Strbuf, (unsigned int)v37),
            v17->_Buf,
            v39 - (_BYTE *)v17,
            _Kseparator._Locktype);
    v41 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(this, &v64, *v40, 0x30u, _Beforepoint);
    v42 = *(_DWORD *)&v41->_Failed;
    v43 = v41->_Strbuf;
    v44 = _Punct_fac->do_decimal_point((std::numpunct<char> *)_Punct_fac);
    v45 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
            this,
            &v64,
            (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v43, v42),
            v44,
            1u);
    v46 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
            this,
            (std::ostreambuf_iterator<char> *)&_Punct_fac,
            *v45,
            0x30u,
            _Afterpoint);
    v37 = *(const std::numpunct<char> **)&v46->_Failed;
    Strbuf = v46->_Strbuf;
    v17 = (std::string::_Bxty *)((char *)v17 + *(_DWORD *)&_Fracoffset._Failed);
    _Count -= *(_DWORD *)&_Fracoffset._Failed;
  }
  v47 = memchr(v17, 101, _Count);
  if ( v47 )
  {
    _Fillcount = v47 - (_BYTE *)v17 + 1;
    v48 = std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(
            this,
            &v64,
            (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)Strbuf, (unsigned int)v37),
            v17->_Buf,
            v47 - (_BYTE *)v17,
            _Kseparator._Locktype);
    v49 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(this, &v66, *v48, 0x30u, _Trailing);
    _Trailing = 0;
    v50 = "E";
    if ( (_Iosbase->_Fmtfl & 4) == 0 )
      v50 = "e";
    v51 = *(const std::numpunct<char> **)&v49->_Failed;
    v52 = v49->_Strbuf;
    v58 = *v50;
    _Punct_fac = v51;
    v63 = v52;
    std::ostreambuf_iterator<char>::operator=((std::ostreambuf_iterator<char> *)&_Punct_fac, v58);
    v37 = _Punct_fac;
    Strbuf = v63;
    v17 = (std::string::_Bxty *)((char *)v17 + _Fillcount);
    _Count -= _Fillcount;
  }
  v53 = std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(
          this,
          &v64,
          (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)Strbuf, (unsigned int)v37),
          v17->_Buf,
          _Count,
          _Kseparator._Locktype);
  v54 = std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(this, &v66, *v53, 0x30u, _Trailing);
  v55 = *(_DWORD *)&v54->_Failed;
  v56 = v54->_Strbuf;
  _Iosbase->_Wide = 0;
  std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(
    this,
    result,
    (std::ostreambuf_iterator<char>)__PAIR64__((unsigned int)v56, v55),
    _Fill,
    (unsigned int)_Bufa);
  if ( _Groupstring._Myres >= 0x10 )
    operator delete(_Groupstring._Bx._Ptr);
  _Groupstring._Myres = 15;
  _Groupstring._Mysize = 0;
  _Groupstring._Bx._Buf[0] = 0;
  if ( _Grouping._Myres >= 0x10 )
    operator delete(_Grouping._Bx._Ptr);
  return result;
}
// 4377A0: variables would overlap: ^60.8 and stkvar "_Fracoffset" ^60.4(has user info)

//----- (00437D60) --------------------------------------------------------
std::string *__thiscall std::string::insert(std::string *this, unsigned int _Off, unsigned int _Count, char _Ch)
{
  unsigned int v4; // ebx
  unsigned int v6; // edi
  unsigned int Myres; // eax
  bool v8; // zf
  unsigned int v9; // eax
  std::string::_Bxty *p_Bx; // ebp
  std::string::_Bxty *Ptr; // ecx
  std::string::_Bxty *v13; // eax
  std::string::_Bxty *v14; // edx
  char *v15; // edi
  int v16; // eax
  bool v17; // cf
  unsigned int _Offa; // [esp+10h] [ebp+4h]

  v4 = _Off;
  if ( this->_Mysize < _Off )
    std::_String_base::_Xran((std::_String_base *)this);
  if ( -1 - this->_Mysize <= _Count )
    std::_String_base::_Xlen((std::_String_base *)this);
  if ( !_Count )
    return this;
  v6 = _Count + this->_Mysize;
  _Offa = v6;
  if ( v6 == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  Myres = this->_Myres;
  if ( Myres < v6 )
  {
    std::string::_Copy(this, v6, this->_Mysize);
    v8 = v6 == 0;
    goto LABEL_10;
  }
  v8 = v6 == 0;
  if ( v6 )
  {
LABEL_10:
    if ( !v8 )
    {
      v9 = this->_Myres;
      p_Bx = &this->_Bx;
      if ( v9 < 0x10 )
        Ptr = &this->_Bx;
      else
        Ptr = (std::string::_Bxty *)p_Bx->_Ptr;
      if ( v9 < 0x10 )
        v13 = &this->_Bx;
      else
        v13 = (std::string::_Bxty *)p_Bx->_Ptr;
      memmove((unsigned __int8 *)&v13->_Buf[v4 + _Count], (unsigned __int8 *)&Ptr->_Buf[v4], this->_Mysize - v4);
      if ( this->_Myres < 0x10 )
        v14 = &this->_Bx;
      else
        v14 = (std::string::_Bxty *)p_Bx->_Ptr;
      v15 = &v14->_Buf[v4];
      LOBYTE(v4) = _Ch;
      BYTE1(v4) = _Ch;
      v16 = v4 << 16;
      LOWORD(v16) = v4;
      memset32(v15, v16, _Count >> 2);
      memset(&v15[4 * (_Count >> 2)], _Ch, _Count & 3);
      v17 = this->_Myres < 0x10;
      this->_Mysize = _Offa;
      if ( !v17 )
        p_Bx = (std::string::_Bxty *)p_Bx->_Ptr;
      p_Bx->_Buf[_Offa] = 0;
    }
    return this;
  }
  this->_Mysize = 0;
  if ( Myres < 0x10 )
    this->_Bx._Buf[0] = 0;
  else
    *this->_Bx._Ptr = 0;
  return this;
}

//----- (00437E80) --------------------------------------------------------
std::string *__thiscall std::string::append(std::string *this, const char *_Ptr, unsigned int _Count)
{
  unsigned int Myres; // edx
  std::string::_Bxty *p_Bx; // eax
  std::string::_Bxty *v6; // eax
  std::string::_Bxty *v7; // ecx
  unsigned int v9; // ebp
  unsigned int v10; // eax
  bool v11; // zf
  std::string::_Bxty *Ptr; // eax
  bool v13; // cf

  Myres = this->_Myres;
  if ( Myres < 0x10 )
    p_Bx = &this->_Bx;
  else
    p_Bx = (std::string::_Bxty *)this->_Bx._Ptr;
  if ( _Ptr >= (const char *)p_Bx )
  {
    v6 = &this->_Bx;
    v7 = Myres < 0x10 ? &this->_Bx : (std::string::_Bxty *)v6->_Ptr;
    if ( &v7->_Buf[this->_Mysize] > _Ptr )
    {
      if ( Myres >= 0x10 )
        v6 = (std::string::_Bxty *)v6->_Ptr;
      return std::string::append(this, this, _Ptr - (const char *)v6, _Count);
    }
  }
  if ( -1 - this->_Mysize <= _Count )
    std::_String_base::_Xlen((std::_String_base *)this);
  if ( !_Count )
    return this;
  v9 = _Count + this->_Mysize;
  if ( v9 == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  v10 = this->_Myres;
  if ( v10 < v9 )
  {
    std::string::_Copy(this, v9, this->_Mysize);
    v11 = v9 == 0;
    goto LABEL_19;
  }
  v11 = v9 == 0;
  if ( v9 )
  {
LABEL_19:
    if ( !v11 )
    {
      if ( this->_Myres < 0x10 )
        Ptr = &this->_Bx;
      else
        Ptr = (std::string::_Bxty *)this->_Bx._Ptr;
      qmemcpy(&Ptr->_Buf[this->_Mysize], _Ptr, _Count);
      v13 = this->_Myres < 0x10;
      this->_Mysize = v9;
      if ( !v13 )
      {
        this->_Bx._Ptr[v9] = 0;
        return this;
      }
      this->_Bx._Buf[v9] = 0;
    }
    return this;
  }
  this->_Mysize = 0;
  if ( v10 < 0x10 )
    this->_Bx._Buf[0] = 0;
  else
    *this->_Bx._Ptr = 0;
  return this;
}

//----- (00437F90) --------------------------------------------------------
std::string *__thiscall std::string::append(
        std::string *this,
        const std::string *_Right,
        unsigned int _Roff,
        unsigned int _Count)
{
  unsigned int v5; // esi
  unsigned int v6; // ebp
  unsigned int Myres; // eax
  bool v8; // zf
  std::string::_Bxty *p_Bx; // edi
  std::string::_Bxty *v11; // edx
  std::string::_Bxty *Ptr; // eax
  bool v13; // cf

  if ( _Right->_Mysize < _Roff )
    std::_String_base::_Xran((std::_String_base *)this);
  v5 = _Count;
  if ( _Right->_Mysize - _Roff < _Count )
    v5 = _Right->_Mysize - _Roff;
  if ( -1 - this->_Mysize <= v5 )
    std::_String_base::_Xlen((std::_String_base *)this);
  if ( !v5 )
    return this;
  v6 = v5 + this->_Mysize;
  if ( v6 == -1 )
    std::_String_base::_Xlen((std::_String_base *)this);
  Myres = this->_Myres;
  if ( Myres < v6 )
  {
    std::string::_Copy(this, v6, this->_Mysize);
    v8 = v6 == 0;
    goto LABEL_12;
  }
  v8 = v6 == 0;
  if ( v6 )
  {
LABEL_12:
    if ( !v8 )
    {
      if ( _Right->_Myres < 0x10 )
        p_Bx = &_Right->_Bx;
      else
        p_Bx = (std::string::_Bxty *)_Right->_Bx._Ptr;
      v11 = &this->_Bx;
      if ( this->_Myres < 0x10 )
        Ptr = &this->_Bx;
      else
        Ptr = (std::string::_Bxty *)v11->_Ptr;
      qmemcpy(&Ptr->_Buf[this->_Mysize], &p_Bx->_Buf[_Roff], v5);
      v13 = this->_Myres < 0x10;
      this->_Mysize = v6;
      if ( !v13 )
        v11 = (std::string::_Bxty *)v11->_Ptr;
      v11->_Buf[v6] = 0;
    }
    return this;
  }
  this->_Mysize = 0;
  if ( Myres < 0x10 )
    this->_Bx._Buf[0] = 0;
  else
    *this->_Bx._Ptr = 0;
  return this;
}

//----- (00438080) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        double _Val)
{
  int Prec; // eax
  int v7; // ebp
  int Fmtfl; // ecx
  double v9; // st7
  signed int v10; // esi
  unsigned int v11; // ebx
  unsigned int v12; // edi
  char v13; // dl
  char *v14; // eax
  _BYTE *v15; // eax
  int v16; // ecx
  char *v17; // eax
  char v18; // cl
  unsigned int v19; // eax
  char _Fmt[8]; // [esp+1Ch] [ebp-78h] BYREF
  char _Buf[108]; // [esp+24h] [ebp-70h] BYREF

  Prec = _Iosbase->_Prec;
  if ( Prec <= 0 && (_Iosbase->_Fmtfl & 0x2000) == 0 )
    Prec = 6;
  v7 = 36;
  if ( Prec <= 36 )
    v7 = Prec;
  Fmtfl = _Iosbase->_Fmtfl;
  v9 = _Val;
  v10 = Prec - v7;
  v11 = 0;
  v12 = 0;
  if ( (Fmtfl & 0x3000) == 0x2000 )
  {
    if ( _Val >= 0.0 )
    {
      v13 = 0;
    }
    else
    {
      v13 = 1;
      v9 = -_Val;
    }
    for ( ; v9 >= 1.0e35; v11 += 10 )
    {
      if ( v11 >= 0x1388 )
        break;
      v9 = v9 * 1.0e-10;
    }
    if ( v9 > 0.0 && v10 >= 10 )
    {
      do
      {
        if ( v9 > 1.0e-35 )
          break;
        if ( v12 >= 0x1388 )
          break;
        v9 = v9 * 1.0e10;
        v10 -= 10;
        v12 += 10;
      }
      while ( v10 >= 10 );
    }
    if ( v13 )
      v9 = -v9;
  }
  _Fmt[0] = 37;
  v14 = &_Fmt[1];
  if ( (Fmtfl & 0x20) != 0 )
  {
    _Fmt[1] = 43;
    v14 = &_Fmt[2];
  }
  if ( (Fmtfl & 0x10) != 0 )
    *v14++ = 35;
  *v14 = 46;
  v15 = v14 + 1;
  *v15++ = 42;
  *v15 = 76;
  v16 = Fmtfl & 0x3000;
  v17 = v15 + 1;
  if ( v16 == 0x2000 )
    v18 = 102;
  else
    v18 = 2 * (v16 != 4096) + 101;
  *v17 = v18;
  v17[1] = 0;
  v19 = sprintf(_Buf, _Fmt, v7, v9);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Fput(
    this,
    result,
    _Dest,
    _Iosbase,
    _Fill,
    (std::string::_Bxty *)_Buf,
    v11,
    v12,
    v10,
    v19);
  return result;
}

//----- (00438230) --------------------------------------------------------
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(
        std::num_put<char,std::ostreambuf_iterator<char> > *this,
        std::ostreambuf_iterator<char> *result,
        std::ostreambuf_iterator<char> _Dest,
        std::ios_base *_Iosbase,
        unsigned __int8 _Fill,
        const void *_Val)
{
  size_t v7; // eax
  char _Buf[64]; // [esp+8h] [ebp-44h] BYREF

  v7 = sprintf(_Buf, "%p", _Val);
  std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(this, result, _Dest, _Iosbase, _Fill, _Buf, v7);
  return result;
}

//----- (00438290) --------------------------------------------------------
void __thiscall __noreturn std::vector<unsigned long>::_Xlen(std::vector<CCellManager::SafetyZoneInfo *> *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00438300) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::do_grouping(std::numpunct<char> *this, std::string *result)
{
  const char *Grouping; // ecx

  Grouping = this->_Grouping;
  result->_Myres = 15;
  result->_Mysize = 0;
  result->_Bx._Buf[0] = 0;
  std::string::assign(result, Grouping, strlen(Grouping));
  return result;
}

//----- (00438350) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::do_falsename(std::numpunct<char> *this, std::string *result)
{
  const char *Falsename; // ecx

  Falsename = this->_Falsename;
  result->_Myres = 15;
  result->_Mysize = 0;
  result->_Bx._Buf[0] = 0;
  std::string::assign(result, Falsename, strlen(Falsename));
  return result;
}

//----- (004383A0) --------------------------------------------------------
std::string *__thiscall std::numpunct<char>::do_truename(std::numpunct<char> *this, std::string *result)
{
  const char *Truename; // ecx

  Truename = this->_Truename;
  result->_Myres = 15;
  result->_Mysize = 0;
  result->_Bx._Buf[0] = 0;
  std::string::assign(result, Truename, strlen(Truename));
  return result;
}

//----- (004383F0) --------------------------------------------------------
void __thiscall CCellManager::~CCellManager(CCellManager *this)
{
  CCellManager::SafetyZoneInfo **i; // ebp
  CCellManager::SafetyZoneInfo *v3; // esi
  char *m_CellData; // eax
  char *v5; // esi
  CBuffer *m_lpBroadCastBuffer; // eax
  CBuffer *m_lpBroadCastCompressBuffer; // eax
  CBufferFactory *m_lpBufferFactory; // ecx

  for ( i = this->m_vecSafetyZone._Myfirst; i != this->m_vecSafetyZone._Mylast; ++i )
  {
    v3 = *i;
    if ( *i )
    {
      if ( v3->m_vecBGMColorKey._Myfirst )
        operator delete(v3->m_vecBGMColorKey._Myfirst);
      v3->m_vecBGMColorKey._Myfirst = 0;
      v3->m_vecBGMColorKey._Mylast = 0;
      v3->m_vecBGMColorKey._Myend = 0;
      if ( v3->m_vecEventKey._Myfirst )
        operator delete(v3->m_vecEventKey._Myfirst);
      v3->m_vecEventKey._Myfirst = 0;
      v3->m_vecEventKey._Mylast = 0;
      v3->m_vecEventKey._Myend = 0;
      operator delete(v3);
    }
  }
  if ( this->m_vecSafetyZone._Myfirst )
    operator delete(this->m_vecSafetyZone._Myfirst);
  this->m_vecSafetyZone._Myfirst = 0;
  this->m_vecSafetyZone._Mylast = 0;
  this->m_vecSafetyZone._Myend = 0;
  m_CellData = (char *)this->m_CellData;
  if ( this->m_CellData )
  {
    v5 = m_CellData - 4;
    `eh vector destructor iterator'(
      m_CellData,
      0x74u,
      *((_DWORD *)m_CellData - 1),
      (void (__thiscall *)(void *))CCell::~CCell);
    operator delete[](v5);
    this->m_CellData = 0;
  }
  m_lpBroadCastBuffer = this->m_lpBroadCastBuffer;
  if ( m_lpBroadCastBuffer )
  {
    m_lpBroadCastBuffer->bufferfactory_->Release(m_lpBroadCastBuffer->bufferfactory_, this->m_lpBroadCastBuffer);
    this->m_lpBroadCastBuffer = 0;
  }
  m_lpBroadCastCompressBuffer = this->m_lpBroadCastCompressBuffer;
  if ( m_lpBroadCastCompressBuffer )
  {
    m_lpBroadCastCompressBuffer->bufferfactory_->Release(
      m_lpBroadCastCompressBuffer->bufferfactory_,
      this->m_lpBroadCastCompressBuffer);
    this->m_lpBroadCastCompressBuffer = 0;
  }
  m_lpBufferFactory = this->m_lpBufferFactory;
  if ( m_lpBufferFactory )
  {
    ((void (__thiscall *)(CBufferFactory *, int))m_lpBufferFactory->~CBufferFactory)(m_lpBufferFactory, 1);
    this->m_lpBufferFactory = 0;
  }
  if ( this->m_vecSafetyZone._Myfirst )
    operator delete(this->m_vecSafetyZone._Myfirst);
  this->m_vecSafetyZone._Myfirst = 0;
  this->m_vecSafetyZone._Mylast = 0;
  this->m_vecSafetyZone._Myend = 0;
}

//----- (00438530) --------------------------------------------------------
void __thiscall std::filebuf::~filebuf<char,std::char_traits<char>>(std::filebuf *this)
{
  std::string *Mystr; // esi

  this->__vftable = (std::filebuf_vtbl *)&std::filebuf::`vftable';
  if ( this->_Closef )
    std::filebuf::close(this);
  Mystr = this->_Mystr;
  if ( Mystr )
  {
    if ( Mystr->_Myres >= 0x10 )
      operator delete(Mystr->_Bx._Ptr);
    Mystr->_Myres = 15;
    Mystr->_Mysize = 0;
    Mystr->_Bx._Buf[0] = 0;
    operator delete((void *)Mystr);
  }
  std::streambuf::~streambuf<char,std::char_traits<char>>(this);
}
// 4FCA7C: using guessed type void *std::filebuf::`vftable';

//----- (004385C0) --------------------------------------------------------
std::filebuf *__thiscall std::filebuf::`scalar deleting destructor'(std::filebuf *this, char a2)
{
  std::filebuf::~filebuf<char,std::char_traits<char>>(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (004385E0) --------------------------------------------------------
void __thiscall std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
        std::vector<CCellManager::SafetyZoneInfo *> *this,
        std::vector<CCellManager::SafetyZoneInfo *>::iterator _Where,
        unsigned int _Count,
        CCellManager::SafetyZoneInfo *const *_Val)
{
  CCellManager::SafetyZoneInfo **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  CCellManager::SafetyZoneInfo **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  CCellManager::SafetyZoneInfo **v24; // ecx
  Quest::QuestNode **v25; // edi
  CCellManager::SafetyZoneInfo **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<CCellManager::SafetyZoneInfo *>::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (CCellManager::SafetyZoneInfo *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<unsigned long>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (CCellManager::SafetyZoneInfo **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (CCellManager::SafetyZoneInfo **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                                           &Mylast[-v23],
                                                           (int)Mylast,
                                                           Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (CCellManager::SafetyZoneInfo **)((char *)_Wherea._Myptr + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (CCellManager::SafetyZoneInfo **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 4386C4: variable 'v15' is possibly undefined

//----- (004387C0) --------------------------------------------------------
void __thiscall std::fstream::fstream(std::fstream *this, const char *_Filename, int _Mode, int _Prot, int a5)
{
  _DWORD *v6; // esi
  int v7; // ecx
  int v8; // eax
  int v9; // edx
  std::ios_base *v10; // ecx
  char v11; // al

  if ( a5 )
  {
    *(_DWORD *)this->gap0 = &std::fstream::`vbtable'{for `std::istream'};
    *(_DWORD *)this->gap8 = &std::fstream::`vbtable'{for `std::ostream'};
    *(_DWORD *)this->gap68 = &std::ios::`vftable';
  }
  v6 = &this->gap8[4];
  std::iostream::basic_iostream<char>(this, (std::streambuf *)&this->gap8[4], 0);
  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)] = &std::fstream::`vftable';
  std::streambuf::streambuf((std::streambuf *)&this->gap8[4]);
  *v6 = &std::filebuf::`vftable';
  *(_DWORD *)&this->_Filebuffer[20] = 0;
  this->_Filebuffer[32] = 0;
  this->_Filebuffer[24] = 0;
  std::streambuf::_Init((std::streambuf *)&this->gap8[4]);
  *(_DWORD *)&this->_Filebuffer[36] = 0;
  *(_DWORD *)&this->_Filebuffer[28] = `std::filebuf::_Init'::`2'::_Stinit;
  *(_DWORD *)&this->_Filebuffer[12] = `std::filebuf::_Init'::`2'::_Stinit;
  *(_DWORD *)&this->_Filebuffer[8] = 0;
  if ( !std::filebuf::open((std::filebuf *)&this->gap8[4], _Filename, _Mode, _Prot) )
  {
    v7 = *(_DWORD *)(*(_DWORD *)this->gap0 + 4);
    v8 = *(_DWORD *)&this->gap8[v7];
    v9 = *(_DWORD *)&this->gap8[v7 + 32];
    v10 = (std::ios_base *)&this->gap0[v7];
    v11 = v8 | 2;
    if ( !v9 )
      v11 |= 4u;
    std::ios_base::clear(v10, v11, 0);
  }
}
// 4DD2AC: using guessed type void *std::fstream::`vftable';
// 4FCA14: using guessed type void *std::ios::`vftable';
// 4FCA7C: using guessed type void *std::filebuf::`vftable';
// 523C50: using guessed type int `std::filebuf::_Init'::`2'::_Stinit;

//----- (004388C0) --------------------------------------------------------
void __thiscall std::fstream::~fstream<char,std::char_traits<char>>(std::fstream *this)
{
  char *Filebuffer; // esi
  char *v2; // eax

  *(_DWORD *)&this->gap0[*(_DWORD *)(*(_DWORD *)&this[-1].gap8[44] + 4) - 104] = &std::fstream::`vftable';
  Filebuffer = this[-1]._Filebuffer;
  std::filebuf::~filebuf<char,std::char_traits<char>>((std::filebuf *)this[-1]._Filebuffer);
  *(_DWORD *)&Filebuffer[*(_DWORD *)(*((_DWORD *)Filebuffer - 3) + 4) - 12] = &std::iostream::`vftable';
  if ( Filebuffer == (char *)12 )
    v2 = 0;
  else
    v2 = Filebuffer - 4;
  *(_DWORD *)&v2[*(_DWORD *)(*(_DWORD *)v2 + 4)] = &std::ostream::`vftable';
  *(_DWORD *)&Filebuffer[*(_DWORD *)(*((_DWORD *)Filebuffer - 3) + 4) - 12] = &std::istream::`vftable';
}
// 4DCF70: using guessed type void *std::istream::`vftable';
// 4DCF74: using guessed type void *std::iostream::`vftable';
// 4DD2AC: using guessed type void *std::fstream::`vftable';
// 4FCA0C: using guessed type void *std::ostream::`vftable';

//----- (00438950) --------------------------------------------------------
void __thiscall CCellManager::CCellManager(CCellManager *this)
{
  CPoolBufferFactory *v2; // eax
  CBufferFactory *v3; // eax

  this->m_CellData = 0;
  this->m_wNumMoving = 0;
  this->m_usSummonCount = 0;
  v2 = (CPoolBufferFactory *)operator new((tagHeader *)0x30);
  if ( v2 )
    CPoolBufferFactory::CPoolBufferFactory(v2);
  else
    v3 = 0;
  this->m_lpBufferFactory = v3;
  this->m_lpBroadCastBuffer = 0;
  this->m_lpBroadCastCompressBuffer = 0;
  this->m_vecSafetyZone._Myfirst = 0;
  this->m_vecSafetyZone._Mylast = 0;
  this->m_vecSafetyZone._Myend = 0;
  this->m_bMoving = 0;
  this->m_bAvoid = 0;
  this->m_bLoadComplete = 0;
}
// 43899B: variable 'v3' is possibly undefined

//----- (004389D0) --------------------------------------------------------
CCellManager *__cdecl CCellManager::GetInstance()
{
  if ( (_S5_0 & 1) == 0 )
  {
    _S5_0 |= 1u;
    CCellManager::CCellManager(&cellManager);
    atexit(_E6_2);
  }
  return &cellManager;
}

//----- (00438A30) --------------------------------------------------------
void __thiscall std::fstream::`vbase destructor'(std::fstream *this)
{
  std::ios *v1; // esi

  v1 = (std::ios *)this->gap68;
  std::fstream::~fstream<char,std::char_traits<char>>((std::fstream *)this->gap68);
  std::ios::~ios<char,std::char_traits<char>>(v1);
}

//----- (00438A50) --------------------------------------------------------
_BYTE *__thiscall std::fstream::`vector deleting destructor'(std::fstream *this, char a2)
{
  _BYTE *v2; // esi

  v2 = &this[-1].gap8[44];
  std::fstream::~fstream<char,std::char_traits<char>>(this);
  std::ios::~ios<char,std::char_traits<char>>((std::ios *)this);
  if ( (a2 & 1) != 0 )
    operator delete(v2);
  return v2;
}

//----- (00438A80) --------------------------------------------------------
char __thiscall CCellManager::LoadSafetyZone(CCellManager *this, const char *szFileName)
{
  _iobuf *v3; // edi
  unsigned int v4; // esi
  _DWORD *v6; // eax
  _DWORD *v7; // ebx
  unsigned int *v8; // ebp
  int v9; // ecx
  unsigned int v10; // edx
  unsigned int *v11; // eax
  int v12; // ecx
  unsigned int v13; // edx
  unsigned int *v14; // eax
  std::vector<CCellManager::SafetyZoneInfo *> *v15; // esi
  CCellManager::SafetyZoneInfo **Myfirst; // edx
  unsigned int v17; // ecx
  CCellManager::SafetyZoneInfo **Mylast; // eax
  unsigned int dwIndex; // [esp+14h] [ebp-130h]
  unsigned int dwIndexa; // [esp+14h] [ebp-130h]
  unsigned int dwEventKey; // [esp+18h] [ebp-12Ch] BYREF
  unsigned int dwColorKey; // [esp+1Ch] [ebp-128h] BYREF
  unsigned int dwTriggerIndex; // [esp+20h] [ebp-124h]
  unsigned int dwTriggerNum; // [esp+24h] [ebp-120h] BYREF
  std::vector<CCellManager::SafetyZoneInfo *> *p_m_vecSafetyZone; // [esp+28h] [ebp-11Ch]
  CCellManager::SafetyZoneInfo *lpInfo; // [esp+2Ch] [ebp-118h] BYREF
  char szFilePath[260]; // [esp+30h] [ebp-114h] BYREF
  int v28; // [esp+140h] [ebp-4h]

  v3 = fopen(szFileName, "rb");
  v4 = 0;
  if ( v3 )
  {
    fread((unsigned __int8 *)&dwTriggerNum, 4u, 1u, v3);
    dwTriggerIndex = 0;
    if ( dwTriggerNum )
    {
      p_m_vecSafetyZone = &this->m_vecSafetyZone;
      while ( 1 )
      {
        v6 = operator new((tagHeader *)0x10130);
        if ( v6 )
        {
          v6[69] = 0;
          v6[70] = 0;
          v6[71] = 0;
          v6[73] = 0;
          v6[74] = 0;
          v6[75] = 0;
          v7 = v6;
        }
        else
        {
          v7 = 0;
        }
        v28 = -1;
        lpInfo = (CCellManager::SafetyZoneInfo *)v7;
        fread((unsigned __int8 *)v7, 4u, 1u, v3);
        fread((unsigned __int8 *)v7 + 4, 4u, 1u, v3);
        fread((unsigned __int8 *)v7 + 8, 1u, 0x104u, v3);
        v8 = v7 + 67;
        fread((unsigned __int8 *)v7 + 268, 4u, 1u, v3);
        fread((unsigned __int8 *)v7 + 304, 4u, 0x4000u, v3);
        dwEventKey = 0;
        dwIndex = 0;
        if ( v7[67] )
        {
          do
          {
            fread((unsigned __int8 *)&dwEventKey, 4u, 1u, v3);
            v9 = v7[69];
            if ( v9 )
              v10 = (v7[70] - v9) >> 2;
            else
              v10 = 0;
            if ( v9 && v10 < (v7[71] - v9) >> 2 )
            {
              v11 = (unsigned int *)v7[70];
              *v11 = dwEventKey;
              v7[70] = v11 + 1;
            }
            else
            {
              std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
                (std::vector<CCellManager::SafetyZoneInfo *> *)v7 + 17,
                (std::vector<CCellManager::SafetyZoneInfo *>::iterator)v7[70],
                1u,
                (CCellManager::SafetyZoneInfo *const *)&dwEventKey);
            }
            ++dwIndex;
          }
          while ( dwIndex < *v8 );
          v4 = 0;
        }
        dwColorKey = 0;
        dwIndexa = 0;
        if ( *v8 )
        {
          do
          {
            fread((unsigned __int8 *)&dwColorKey, 4u, 1u, v3);
            v12 = v7[73];
            if ( v12 )
              v13 = (v7[74] - v12) >> 2;
            else
              v13 = 0;
            if ( v12 && v13 < (v7[75] - v12) >> 2 )
            {
              v14 = (unsigned int *)v7[74];
              *v14 = dwColorKey;
              v7[74] = v14 + 1;
            }
            else
            {
              std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
                (std::vector<CCellManager::SafetyZoneInfo *> *)v7 + 18,
                (std::vector<CCellManager::SafetyZoneInfo *>::iterator)v7[74],
                1u,
                (CCellManager::SafetyZoneInfo *const *)&dwColorKey);
            }
            ++dwIndexa;
          }
          while ( dwIndexa < *v8 );
          v4 = 0;
        }
        if ( *v8 )
        {
          do
          {
            fread((unsigned __int8 *)szFilePath, 1u, 0x104u, v3);
            ++v4;
          }
          while ( v4 < *v8 );
        }
        v15 = p_m_vecSafetyZone;
        Myfirst = p_m_vecSafetyZone->_Myfirst;
        v17 = Myfirst ? p_m_vecSafetyZone->_Mylast - Myfirst : 0;
        if ( Myfirst && v17 < p_m_vecSafetyZone->_Myend - Myfirst )
        {
          Mylast = p_m_vecSafetyZone->_Mylast;
          *Mylast = (CCellManager::SafetyZoneInfo *)v7;
          v15->_Mylast = Mylast + 1;
        }
        else
        {
          std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(
            p_m_vecSafetyZone,
            (std::vector<CCellManager::SafetyZoneInfo *>::iterator)p_m_vecSafetyZone->_Mylast,
            1u,
            &lpInfo);
        }
        if ( ++dwTriggerIndex >= dwTriggerNum )
          break;
        v4 = 0;
      }
    }
    fclose(v3);
    return 1;
  }
  else
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CCellManager::LoadSafetyZone",
      aDWorkRylSource_49,
      490,
      (char *)&byte_4DD2E0,
      szFileName);
    return 0;
  }
}

//----- (00438D70) --------------------------------------------------------
char __thiscall CCellManager::CheckCellStatus(CCellManager *this)
{
  CCellManager *v1; // edi
  std::ostream *v3; // eax
  std::ostream *v4; // esi
  int v5; // ecx
  int v6; // edx
  CCell *m_CellData; // esi
  unsigned int v8; // edi
  unsigned int v9; // ebx
  unsigned int Mysize; // ecx
  CCell *v11; // esi
  unsigned int v12; // ebp
  CCell *ConnectCell; // eax
  std::ostream *v14; // eax
  std::ostream *v15; // eax
  std::ostream *v16; // eax
  std::ostream *v17; // eax
  std::ostream *v18; // eax
  std::ostream *v19; // eax
  std::ostream *v20; // eax
  std::ostream *v21; // eax
  std::ostream *v22; // eax
  std::ostream *v23; // eax
  std::ostream *v24; // eax
  std::ostream *v25; // eax
  std::ostream *v26; // eax
  std::ostream *v27; // eax
  std::ostream *v28; // eax
  std::ostream *v29; // eax
  std::ostream *v30; // eax
  std::ostream *v31; // eax
  std::ostream *v32; // eax
  std::ostream *v33; // eax
  std::ostream *v34; // eax
  std::ostream *v35; // eax
  std::ostream *v36; // eax
  std::ostream *v37; // esi
  int v38; // edx
  int v39; // edi
  std::ios_base *v40; // ecx
  int v41; // eax
  bool v42; // cc
  int nIndex; // [esp+8h] [ebp-CCh]
  signed int nCell; // [esp+Ch] [ebp-C8h]
  int v45; // [esp+10h] [ebp-C4h]
  unsigned int nCurrentItem; // [esp+14h] [ebp-C0h]
  unsigned int nCurrentCharacter; // [esp+18h] [ebp-BCh]
  int v48; // [esp+1Ch] [ebp-B8h]
  unsigned int nCurrentMonster; // [esp+20h] [ebp-B4h]
  int _Val; // [esp+24h] [ebp-B0h]
  std::fstream file; // [esp+2Ch] [ebp-A8h] BYREF
  int v53; // [esp+D0h] [ebp-4h]

  v1 = this;
  if ( !this->m_CellData )
    return 0;
  std::fstream::fstream(&file, "CellStat.txt", 2, 438, 1);
  v53 = 0;
  v3 = std::operator<<<std::char_traits<char>>((std::ostream *)file.gap8, Val);
  v4 = std::operator<<<std::char_traits<char>>(v3, &byte_4DD340);
  std::ostream::put(v4, 0xAu);
  std::ostream::flush(v4);
  v5 = CCell::ms_CellSize;
  nIndex = 0;
  if ( v5 * v5 > 0 )
  {
    v45 = 0;
    while ( 1 )
    {
      v6 = nIndex % v5;
      m_CellData = v1->m_CellData;
      v8 = 0;
      v9 = 0;
      v48 = nIndex / v5;
      Mysize = m_CellData[v45].m_lstMonster._Mysize;
      v11 = &m_CellData[v45];
      _Val = v6;
      v12 = 0;
      nCurrentCharacter = v11->m_lstCharacter._Mysize;
      nCurrentMonster = Mysize;
      nCurrentItem = v11->m_lstItem._Mysize;
      for ( nCell = 0; nCell < 9; ++nCell )
      {
        ConnectCell = CCell::GetConnectCell(v11, nCell);
        if ( ConnectCell )
        {
          v8 += ConnectCell->m_lstCharacter._Mysize;
          v9 += ConnectCell->m_lstMonster._Mysize;
          v12 += ConnectCell->m_lstItem._Mysize;
        }
      }
      v14 = std::ostream::operator<<((std::ostream *)file.gap8, _Val);
      v15 = std::operator<<<std::char_traits<char>>(v14, 0x2Fu);
      v16 = std::ostream::operator<<(v15, v48);
      v17 = std::operator<<<std::char_traits<char>>(v16, 0x2Fu);
      v18 = std::ostream::operator<<(v17, nCurrentCharacter);
      v19 = std::operator<<<std::char_traits<char>>(v18, 0x2Fu);
      v20 = std::ostream::operator<<(v19, v8);
      v21 = std::operator<<<std::char_traits<char>>(v20, 0x2Fu);
      v22 = std::ostream::operator<<(v21, nCurrentMonster);
      v23 = std::operator<<<std::char_traits<char>>(v22, 0x2Fu);
      v24 = std::ostream::operator<<(v23, v9);
      v25 = std::operator<<<std::char_traits<char>>(v24, 0x2Fu);
      v26 = std::ostream::operator<<(v25, nCurrentItem);
      v27 = std::operator<<<std::char_traits<char>>(v26, 0x2Fu);
      v28 = std::ostream::operator<<(v27, v12);
      v29 = std::operator<<<std::char_traits<char>>(v28, 0x2Fu);
      v30 = std::ostream::operator<<(v29, nCurrentMonster + nCurrentCharacter);
      v31 = std::operator<<<std::char_traits<char>>(v30, 0x2Fu);
      v32 = std::ostream::operator<<(v31, v9 + v8);
      v33 = std::operator<<<std::char_traits<char>>(v32, 0x2Fu);
      v34 = std::ostream::operator<<(v33, nCurrentCharacter + nCurrentItem + nCurrentMonster);
      v35 = std::operator<<<std::char_traits<char>>(v34, 0x2Fu);
      v36 = std::ostream::operator<<(v35, v8 + v9 + v12);
      v37 = std::operator<<<std::char_traits<char>>(v36, 0x2Fu);
      std::ostream::put(v37, 0xAu);
      v38 = *(_DWORD *)(*(_DWORD *)v37->gap0 + 4);
      v39 = 0;
      if ( (v37->gap0[v38 + 8] & 6) == 0
        && (*(int (__thiscall **)(_DWORD))(**(_DWORD **)&v37->gap0[v38 + 40] + 44))(*(_DWORD *)&v37->gap0[v38 + 40]) == -1 )
      {
        v39 = 4;
      }
      v40 = (std::ios_base *)&v37->gap0[*(_DWORD *)(*(_DWORD *)v37->gap0 + 4)];
      if ( v39 )
      {
        v41 = v39 | v40->_Mystate;
        if ( !v40[1].__vftable )
          LOBYTE(v41) = v41 | 4;
        std::ios_base::clear(v40, v41, 0);
      }
      v5 = CCell::ms_CellSize;
      v42 = ++nIndex < v5 * v5;
      ++v45;
      if ( !v42 )
        break;
      v1 = this;
    }
  }
  v53 = -1;
  std::fstream::~fstream<char,std::char_traits<char>>((std::fstream *)file.gap68);
  *(_DWORD *)file.gap68 = &std::ios::`vftable';
  std::ios_base::~ios_base((std::ios_base *)file.gap68);
  return 1;
}
// 4FCA14: using guessed type void *std::ios::`vftable';

//----- (00439060) --------------------------------------------------------
void __thiscall CCellManager::Load(CCellManager *this)
{
  VirtualArea::CVirtualAreaMgr *Instance; // eax
  CServerSetup *v3; // eax
  char ServerZone; // al
  CServerSetup *v5; // eax
  char v6; // al
  char szArrangement[260]; // [esp+4h] [ebp-20Ch] BYREF
  char szSafetyZone[260]; // [esp+108h] [ebp-108h] BYREF

  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 120, aNpc);
  CNPC::LoadNPCInfo();
  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 123, (char *)&byte_4DD464);
  CMonsterMgr::LoadMonstersFromFile(CSingleton<CMonsterMgr>::ms_pSingleton, 0);
  CCellManager::InitAI(this);
  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 127, aVirtualarea_0);
  Instance = VirtualArea::CVirtualAreaMgr::GetInstance();
  VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType(Instance, 0);
  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 130, (char *)&byte_4DD42C);
  CCellManager::CreateCell(this);
  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 133, (char *)&byte_4DD414);
  v3 = CServerSetup::GetInstance();
  ServerZone = CServerSetup::GetServerZone(v3);
  sprintf(szArrangement, "./Script/Game/Arrangement/Arrangement%d.txt", ServerZone);
  CCellManager::LoginMonster(this, szArrangement, 0);
  CServerLog::DetailLog(&g_Log, LOG_DETAIL, "CCellManager::Load", aDWorkRylSource_49, 138, (char *)&byte_4DD3C4);
  v5 = CServerSetup::GetInstance();
  v6 = CServerSetup::GetServerZone(v5);
  sprintf(szSafetyZone, "./Script/Game/SafetyZone/Zone%d.bgm", v6);
  CCellManager::LoadSafetyZone(this, szSafetyZone);
  this->m_bLoadComplete = 1;
}

//----- (004391F0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (00439250) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (00439270) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (004392D0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (004392F0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}


