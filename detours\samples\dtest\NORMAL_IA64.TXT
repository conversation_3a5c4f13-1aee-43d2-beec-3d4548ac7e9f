    ..\..\bin.IA64\dtest.exe
Calling LocalTarget1 w/o detour
  LocalTarget1 (1)
Calling LocalTarget1 w/ detour
  MyLocalTarget1 (2)
  LocalTarget1 (2)
Calling Target0 function.
  MyTarget0 ()
Calling TargetN functions.
  MyLocalTarget1 (1)
  LocalTarget1 (1)
  MyTarget0 ()
  MyTarget1 (1)
  MyTarget2 (1,2)
  MyTarget3 (1,2,3)
  MyTarget4 (1,2,3,4)
  MyTarget5 (1,2,3,4,5)
  MyTarget6 (1,2,3,4,5,6)
  MyTarget7 (1,2,3,4,5,6,7)
  MyTarget8 (1,2,3,4,5,6,7,8)
  MyTarget9 (1,2,3,4,5,6,7,8,9)
  MyTarget10(1,2,3,4,5,6,7,8,9,10)
  MyTarget11(1,2,3,4,5,6,7,8,9,10,11)
  MyTarget12(1,2,3,4,5,6,7,8,9,10,11,12)
  MyTarget13(1,2,3,4,5,6,7,8,9,10,11,12,13)
  MyTarget14(1,2,3,4,5,6,7,8,9,10,11,12,13,14)
  MyTarget15(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15)
  MyTarget16(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16)
  MyTargetV (0)
  MyTargetV (0,1)
  MyTargetV (0,1,2)
  MyTargetV (0,1,2,3)
  MyTargetV (0,1,2,3,4)
  MyTargetV (0,1,2,3,4,5)
  MyTargetV (0,1,2,3,4,5,6)
  MyTargetV (0,1,2,3,4,5,6,7)
  MyTargetV (0,1,2,3,4,5,6,7,8)
  MyTargetV (0,1,2,3,4,5,6,7,8,9)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11,12)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15)
  MyTargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16)
  MyTargetR (0,1,2,3,4)
  MyTargetR (0,1,2,3,3)
  MyTargetR (0,1,2,3,2)
  MyTargetR (0,1,2,3,1)
....................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................  MyTargetR (0,1,2,3,4,5,6,7,8,9,10,4)
  MyTargetR (0,1,2,3,4,5,6,7,8,9,10,3)
  MyTargetR (0,1,2,3,4,5,6,7,8,9,10,2)
  MyTargetR (0,1,2,3,4,5,6,7,8,9,10,1)
 => 3011
Calling Target0 again with 1 detour.
  MyTarget0 ()
Calling Target0 again with 2 detours.
  Starting Target0_1.
  MyTarget0 ()
  End Target0_1.
Calling Target0 again with 3 detours.
  Starting Target0_2.
  Starting Target0_1.
  MyTarget0 ()
  End Target0_1.
  End Target0_2.
Calling Target0 again with 4 detours.
  Starting Target0_3.
  Starting Target0_2.
  Starting Target0_1.
  MyTarget0 ()
  End Target0_1.
  End Target0_2.
  End Target0_3.
Done.
    Target0 ()
    Target0 ()
    Target1 (1)
    Target2 (1,2)
    Target3 (1,2,3)
    Target4 (1,2,3,4)
    Target5 (1,2,3,4,5)
    Target6 (1,2,3,4,5,6)
    Target7 (1,2,3,4,5,6,7)
    Target8 (1,2,3,4,5,6,7,8)
    Target9 (1,2,3,4,5,6,7,8,9)
    Target10(1,2,3,4,5,6,7,8,9,10)
    Target11(1,2,3,4,5,6,7,8,9,10,11)
    Target12(1,2,3,4,5,6,7,8,9,10,11,12)
    Target13(1,2,3,4,5,6,7,8,9,10,11,12,13)
    Target14(1,2,3,4,5,6,7,8,9,10,11,12,13,14)
    Target15(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15)
    Target16(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16)
    TargetV (0)
    TargetV (0,1)
    TargetV (0,1,2)
    TargetV (0,1,2,3)
    TargetV (0,1,2,3,4)
    TargetV (0,1,2,3,4,5)
    TargetV (0,1,2,3,4,5,6)
    TargetV (0,1,2,3,4,5,6,7)
    TargetV (0,1,2,3,4,5,6,7,8)
    TargetV (0,1,2,3,4,5,6,7,8,9)
    TargetV (0,1,2,3,4,5,6,7,8,9,10)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11,12)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15)
    TargetV (0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16)
:::    TargetR (0,1,2,3,1)
:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::    TargetR (0,1,2,3,4,5,6,7,8,9,10,1)
    Target0 ()
    Target0 ()
    Target0 ()
    Target0 ()
