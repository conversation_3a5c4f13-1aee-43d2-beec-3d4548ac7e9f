[BLOCK_ATTACK_REDUCER]

; 1.0 = base value
; 1.1 = Reduce 1% Block On Attack
; 2.0 = Almost 10% Reduce Block On Attack
; 3.0 = Almost 30% Reduce Block On Attack
; 5.0 = Almost 100% Reduce block On Attack


; 0.9 = 10% increase Block On Attack
; 0.8 = 20% increase Block On Attack
; 0.5 = 50% increase Block On Attack
; 0.0 = 100% Full Block On Attack

; Class specific multipliers
; How to test : Set 0.0 to warrior , warrior attack will give full block Attacks.

;Human
Fighter=1.0
Rogue=1.0
Mage=1.0
Acolyte=1.0
Defender=1.0
Warrior=1.0
Assassin=1.0
Archer=1.0
Sorcerer=1.0
Enchanter=1.0
Priest=1.0
Cleric=1.0

;<PERSON>k<PERSON>n
Combatant=1.0
Officiator=1.0
Templar=1.0
Attacker=1.0
Gunner=1.0
RuneOff=1.0
LifeOff=1.0
ShadowOff=1.0
