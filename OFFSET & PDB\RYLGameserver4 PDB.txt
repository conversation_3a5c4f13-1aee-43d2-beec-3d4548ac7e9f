

//----- (00407680) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const,CMonster *>>>@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const ,CMonster *> > _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v4; // esi
  CMonster *second; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax

  v4 = result;
  if ( result == _First._Ptr )
  {
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
  else
  {
    do
    {
      second = v4->_Myval.second;
      if ( second )
        second->RegenHPAndMP(second, 0, 0, 1);
      if ( !v4->_Isnil )
      {
        Right = v4->_Right;
        if ( Right->_Isnil )
        {
          for ( i = v4->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( v4 != i->_Right )
              break;
            v4 = i;
          }
          v4 = i;
        }
        else
        {
          v4 = v4->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            v4 = j;
        }
      }
    }
    while ( v4 != _First._Ptr );
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
}

//----- (00407710) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const,CCharacter *>>>@<eax>(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const ,CCharacter *> > _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v4; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax
  FnCSAuth *v9; // [esp+0h] [ebp-8h]

  v4 = result;
  if ( result == _First._Ptr )
  {
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
  else
  {
    do
    {
      FnCSAuth::operator()(v4->_Myval.second, v9);
      if ( !v4->_Isnil )
      {
        Right = v4->_Right;
        if ( Right->_Isnil )
        {
          for ( i = v4->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( v4 != i->_Right )
              break;
            v4 = i;
          }
          v4 = i;
        }
        else
        {
          v4 = v4->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            v4 = j;
        }
      }
    }
    while ( v4 != _First._Ptr );
    a1->_Ptr = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)_Last.m_fnSecondProcess;
    return a1;
  }
}
// 407723: variable 'v9' is possibly undefined

//----- (00407790) --------------------------------------------------------
int __thiscall CProcessCOMMAND::operator()(
        CProcessCOMMAND *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        __int16 wParam,
        int lParam)
{
  char *v5; // edi
  unsigned int v6; // eax
  CConsoleWindow *m_ConsoleWindow; // edx
  CCommandProcess *m_CMDProcess; // esi
  CConsoleCommand *v9; // eax

  switch ( wParam )
  {
    case 'f':
      v5 = "connect";
      goto LABEL_8;
    case 'k':
      v5 = "startall";
      goto LABEL_8;
    case 'r':
      v5 = "pool";
      goto LABEL_8;
    case 't':
      v5 = "reloadsetup";
LABEL_8:
      if ( v5 )
      {
        v6 = strlen(v5);
        m_ConsoleWindow = this->m_ConsoleWindow;
        m_CMDProcess = m_ConsoleWindow->m_CMDProcess;
        v9 = CConsoleCMDFactory::Create(m_ConsoleWindow->m_CMDFactory, v5, v6);
        CCommandProcess::Add(m_CMDProcess, v9);
      }
      break;
    case 'w':
      CConsoleWindow::Initialize(this->m_ConsoleWindow);
      break;
    case 'x':
      CConsoleWindow::Destroy(this->m_ConsoleWindow);
      break;
    default:
      break;
  }
  if ( wParam == 109 )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CProcessCOMMAND::operator`()'",
      aDWorkRylSource_73,
      40,
      "Terminate GameServer System Tray.");
    PostMessageA(hWnd, 0x12u, 0, 0);
  }
  return 0;
}

//----- (00407880) --------------------------------------------------------
int __thiscall CProcessRYLGAME_AUTOSTART::operator()(
        CProcessRYLGAME_AUTOSTART *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        unsigned int wParam,
        int lParam)
{
  CConsoleWindow *m_ConsoleWindow; // eax
  CCommandProcess *m_CMDProcess; // esi
  CConsoleCommand *v8; // eax

  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CProcessRYLGAME_AUTOSTART::operator`()'",
    aDWorkRylSource_73,
    65,
    "AutoStart Started");
  m_ConsoleWindow = this->m_ConsoleWindow;
  m_CMDProcess = m_ConsoleWindow->m_CMDProcess;
  v8 = CConsoleCMDFactory::Create(m_ConsoleWindow->m_CMDFactory, "startall", 8u);
  CCommandProcess::Add(m_CMDProcess, v8);
  return 0;
}

//----- (004078D0) --------------------------------------------------------
int __thiscall CProcessGAME_CONNECTTOAGENT::operator()(
        CProcessGAME_CONNECTTOAGENT *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        unsigned int wParam,
        int lParam)
{
  CConsoleWindow *m_ConsoleWindow; // eax
  CCommandProcess *m_CMDProcess; // esi
  CConsoleCommand *v8; // eax

  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CProcessGAME_CONNECTTOAGENT::operator`()'",
    aDWorkRylSource_73,
    90,
    (char *)&byte_500CC8);
  m_ConsoleWindow = this->m_ConsoleWindow;
  m_CMDProcess = m_ConsoleWindow->m_CMDProcess;
  v8 = CConsoleCMDFactory::Create(m_ConsoleWindow->m_CMDFactory, "connect", 7u);
  CCommandProcess::Add(m_CMDProcess, v8);
  return 0;
}

//----- (00407920) --------------------------------------------------------
int __thiscall CProcessRYLGAME_QUIT::operator()(
        CProcessRYLGAME_QUIT *this,
        HWND__ *hWnd,
        unsigned int uMsg,
        unsigned int wParam,
        int lParam)
{
  CServerLog::DetailLog(
    &g_Log,
    LOG_DETAIL,
    "CProcessRYLGAME_QUIT::operator`()'",
    aDWorkRylSource_73,
    110,
    (char *)&byte_500C64);
  SendMessageA(hWnd, 0x111u, 0x6Du, 0);
  return 0;
}

//----- (00407960) --------------------------------------------------------
BOOL __usercall CRylGameServer::InitializeMsgProc@<eax>(CRylGameServer *this@<ecx>, int a2@<eax>)
{
  CMsgProcessMgr *v3; // edi
  int v4; // esi
  CMsgProc *v5; // eax
  CMsgProc_vtbl *v6; // ecx
  BOOL v7; // esi
  CMsgProc *v8; // eax
  CMsgProc_vtbl *v9; // ecx
  int v10; // esi
  CMsgProc *v11; // eax
  CMsgProc_vtbl *v12; // ebx
  CMsgProc *v13; // eax

  v3 = *(CMsgProcessMgr **)(a2 + 44);
  v4 = 0;
  if ( v3 )
  {
    if ( *(_DWORD *)(a2 + 28) )
    {
      v5 = (CMsgProc *)operator new((tagHeader *)8);
      if ( v5 )
      {
        v6 = *(CMsgProc_vtbl **)(a2 + 28);
        v5->__vftable = (CMsgProc_vtbl *)&CProcessCOMMAND::`vftable';
        v5[1].__vftable = v6;
      }
      else
      {
        v5 = 0;
      }
      v7 = !CMsgProcessMgr::Register(v3, 0x111u, v5);
      v8 = (CMsgProc *)operator new((tagHeader *)8);
      if ( v8 )
      {
        v9 = *(CMsgProc_vtbl **)(a2 + 28);
        v8->__vftable = (CMsgProc_vtbl *)&CProcessRYLGAME_AUTOSTART::`vftable';
        v8[1].__vftable = v9;
      }
      else
      {
        v8 = 0;
      }
      v10 = !CMsgProcessMgr::Register(v3, 0x4B5u, v8) + v7;
      v11 = (CMsgProc *)operator new((tagHeader *)8);
      if ( v11 )
      {
        v12 = *(CMsgProc_vtbl **)(a2 + 28);
        v11->__vftable = (CMsgProc_vtbl *)&CProcessGAME_CONNECTTOAGENT::`vftable';
        v11[1].__vftable = v12;
      }
      else
      {
        v11 = 0;
      }
      v4 = !CMsgProcessMgr::Register(v3, 0x4DDu, v11) + v10;
    }
    v13 = (CMsgProc *)operator new((tagHeader *)4);
    if ( v13 )
      v13->__vftable = (CMsgProc_vtbl *)&CProcessRYLGAME_QUIT::`vftable';
    else
      v13 = 0;
    v4 += !CMsgProcessMgr::Register(v3, 0x4BFu, v13);
  }
  return v4 == 0;
}
// 500C18: using guessed type void *CProcessRYLGAME_QUIT::`vftable';
// 500C20: using guessed type void *CProcessGAME_CONNECTTOAGENT::`vftable';
// 500C30: using guessed type void *CProcessRYLGAME_AUTOSTART::`vftable';
// 500C38: using guessed type void *CProcessCOMMAND::`vftable';

//----- (00407A50) --------------------------------------------------------
char __usercall GameServerSendPacket::SendUpdateAddressAck@<al>(
        CSendStream *SendStream@<ebx>,
        const sockaddr_in *PublicAddr@<edi>,
        const sockaddr_in *PrivateAddr@<esi>,
        unsigned int dwCID)
{
  char *Buffer; // eax

  Buffer = CSendStream::GetBuffer(SendStream, (char *)0x30);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 11) = dwCID;
  *(sockaddr_in *)(Buffer + 12) = *PublicAddr;
  *(sockaddr_in *)(Buffer + 28) = *PrivateAddr;
  return CSendStream::WrapCrypt(SendStream, 0x30u, 0x1Eu, 0, 0);
}

//----- (00407AB0) --------------------------------------------------------
CSocketFactory *__thiscall CSocketFactory::`vector deleting destructor'(CSocketFactory *this, char a2)
{
  this->__vftable = (CSocketFactory_vtbl *)&CSocketFactory::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 500E08: using guessed type void *CSocketFactory::`vftable';

//----- (00407AD0) --------------------------------------------------------
void __thiscall CUDPFactory::~CUDPFactory(CTCPFactory *this)
{
  this->__vftable = (CTCPFactory_vtbl *)&CSocketFactory::`vftable';
}
// 500E08: using guessed type void *CSocketFactory::`vftable';

//----- (00407AE0) --------------------------------------------------------
CTCPFactory *__thiscall CUDPFactory::`scalar deleting destructor'(CTCPFactory *this, char a2)
{
  CUDPFactory::~CUDPFactory(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00407B00) --------------------------------------------------------
CThread *__thiscall CThread::`vector deleting destructor'(CThread *this, char a2)
{
  this->__vftable = (CThread_vtbl *)&CThread::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 500DF0: using guessed type void *CThread::`vftable';

//----- (00407B20) --------------------------------------------------------
void __usercall LogAddress(const sockaddr_in *sockAddr@<esi>, const char *szDetailText, unsigned int dwCID)
{
  char *v3; // eax
  int v4; // [esp-4h] [ebp-4h]

  v4 = ntohs(sockAddr->sin_port);
  v3 = inet_ntoa(sockAddr->sin_addr);
  CServerLog::DetailLog(
    &g_Log,
    LOG_ERROR,
    "LogAddress",
    aDWorkRylSource_71,
    30,
    aCid0x08xSIpSD,
    dwCID,
    szDetailText,
    v3,
    v4);
}

//----- (00407B70) --------------------------------------------------------
void __thiscall CUDPWish::CUDPWish(CUDPWish *this, CUDPWish *dwMaxProcessPerCall, unsigned int dwMaxProcessPerCalla)
{
  bool v3; // cf
  unsigned int *p_dwMaxProcessPerCalla; // eax
  int v5; // [esp+4h] [ebp-10h] BYREF
  int v6; // [esp+10h] [ebp-4h]

  dwMaxProcessPerCall->m_hThreadHandle = (void *)-1;
  v6 = 0;
  dwMaxProcessPerCall->__vftable = (CUDPWish_vtbl *)&CUDPWish::`vftable';
  InitializeCriticalSection(&dwMaxProcessPerCall->m_ProcessLock.m_CSLock);
  LOBYTE(v6) = 1;
  v5 = 20;
  v3 = dwMaxProcessPerCalla < 0x14;
  dwMaxProcessPerCall->m_UDPBindSocket = -1;
  p_dwMaxProcessPerCalla = (unsigned int *)&v5;
  if ( !v3 )
    p_dwMaxProcessPerCalla = &dwMaxProcessPerCalla;
  dwMaxProcessPerCall->m_dwMaxProcessPerCall = *p_dwMaxProcessPerCalla;
  CBufferQueue::CBufferQueue(&dwMaxProcessPerCall->m_BufferQueue);
  LOBYTE(v6) = 2;
  CPoolBufferFactory::CPoolBufferFactory(&dwMaxProcessPerCall->m_BufferFactory);
  dwMaxProcessPerCall->m_hEvent[0] = 0;
  dwMaxProcessPerCall->m_hEvent[1] = 0;
}
// 500DFC: using guessed type void *CUDPWish::`vftable';

//----- (00407C10) --------------------------------------------------------
CUDPWish *__thiscall CUDPWish::`scalar deleting destructor'(CUDPWish *this, char a2)
{
  CUDPWish::~CUDPWish(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00407C30) --------------------------------------------------------
void __thiscall CUDPWish::~CUDPWish(CUDPWish *this)
{
  this->__vftable = (CUDPWish_vtbl *)&CUDPWish::`vftable';
  CThreadMgr::Stop(this, 0xFFFFFFFF);
  if ( this->m_UDPBindSocket != -1 )
  {
    closesocket(this->m_UDPBindSocket);
    this->m_UDPBindSocket = -1;
  }
  if ( this->m_hEvent[0] )
  {
    CloseHandle(this->m_hEvent[0]);
    this->m_hEvent[0] = 0;
  }
  if ( this->m_hEvent[1] )
  {
    CloseHandle(this->m_hEvent[1]);
    this->m_hEvent[1] = 0;
  }
  CBufferQueue::clear(&this->m_BufferQueue);
  CPoolBufferFactory::Destroy(&this->m_BufferFactory);
  CPoolBufferFactory::~CPoolBufferFactory(&this->m_BufferFactory);
  CBufferQueue::~CBufferQueue(&this->m_BufferQueue);
  DeleteCriticalSection(&this->m_ProcessLock.m_CSLock);
  this->__vftable = (CUDPWish_vtbl *)&CThread::`vftable';
}
// 500DF0: using guessed type void *CThread::`vftable';
// 500DFC: using guessed type void *CUDPWish::`vftable';

//----- (00407CF0) --------------------------------------------------------
bool __usercall CUDPWish::Initialize@<al>(CUDPWish *this@<ecx>, _DWORD *a2@<esi>)
{
  CServerSetup *Instance; // eax
  int v3; // eax
  HANDLE EventA; // eax
  int Error; // eax
  SOCKET v7; // [esp-Ch] [ebp-30h]
  CUDPFactory udpFactory; // [esp+4h] [ebp-20h] BYREF
  int v9; // [esp+20h] [ebp-4h]

  CUDPFactory::CUDPFactory(&udpFactory);
  v9 = 0;
  Instance = CServerSetup::GetInstance();
  LOWORD(v3) = ntohs(*(_WORD *)Instance->m_GameServerUDPAddr.m_SockAddr.sa_data);
  a2[12] = CSocketFactory::CreateBindedSocket(&udpFactory, 0, v3);
  a2[10] = CreateEventA(0, 1, 0, 0);
  EventA = CreateEventA(0, 1, 0, 0);
  v7 = a2[12];
  a2[11] = EventA;
  if ( WSAEventSelect(v7, EventA, 1) == -1 )
  {
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CUDPWish::Initialize",
      aDWorkRylSource_71,
      86,
      "UDPWish Initialize failed : WSAEventSelect failed : %d",
      Error);
    return 0;
  }
  return a2[12] != -1 && a2[10] && a2[11];
}
// 407D34: variable 'v3' is possibly undefined

//----- (00407DD0) --------------------------------------------------------
unsigned int __thiscall CUDPWish::Run(CUDPWish *this)
{
  unsigned int result; // eax
  DWORD v3; // eax
  CUDPWish *v4; // ecx
  _WSABUF v5; // [esp-10h] [ebp-1048h]
  int v6; // [esp-Ch] [ebp-1044h]
  int Error; // [esp-Ch] [ebp-1044h]
  _WSANETWORKEVENTS NetworkEvents; // [esp+8h] [ebp-1030h] BYREF
  char v9; // [esp+34h] [ebp-1004h] BYREF

  result = -1;
  if ( this->m_UDPBindSocket != -1 && this->m_hEvent[0] && this->m_hEvent[1] )
  {
    while ( 1 )
    {
      v3 = WaitForMultipleObjects(2u, this->m_hEvent, 0, 0xFFFFFFFF);
      if ( !v3 )
        break;
      if ( v3 == 1 )
      {
        if ( WSAEnumNetworkEvents(this->m_UDPBindSocket, this->m_hEvent[1], &NetworkEvents) == -1 )
        {
          Error = WSAGetLastError();
          CServerLog::DetailLog(
            &g_Log,
            LOG_ERROR,
            "CUDPWish::Run",
            aDWorkRylSource_71,
            133,
            "UDPWish Error. WSAEnumNetworkEvents error : %d",
            Error);
          return 0;
        }
        if ( (NetworkEvents.lNetworkEvents & 1) != 0 )
        {
          if ( NetworkEvents.iErrorCode[0] )
          {
            CServerLog::DetailLog(
              &g_Log,
              LOG_ERROR,
              "CUDPWish::Run",
              aDWorkRylSource_71,
              145,
              "UDPWish Error. WSAReceive error : %d",
              NetworkEvents.iErrorCode[0]);
          }
          else
          {
            v5.buf = &v9;
            v5.len = 4096;
            CUDPWish::ReceivePacket(v4, (int)this, v5);
          }
        }
      }
      else if ( v3 == -1 )
      {
        v6 = WSAGetLastError();
        CServerLog::DetailLog(
          &g_Log,
          LOG_ERROR,
          "CUDPWish::Run",
          aDWorkRylSource_71,
          153,
          "UDPWish Error. WaitforMultipleObject error : %d",
          v6);
        return 0;
      }
    }
    return 0;
  }
  return result;
}
// 407E7C: variable 'v4' is possibly undefined

//----- (00407EF0) --------------------------------------------------------
BOOL __thiscall CUDPWish::End(CUDPWish *this)
{
  return SetEvent(this->m_hEvent[0]);
}

//----- (00407F00) --------------------------------------------------------
char __userpurge CUDPWish::ReceivePacket@<al>(CUDPWish *this@<ecx>, int a2@<esi>, _WSABUF wsaBuf)
{
  int Error; // eax
  CBuffer *v5; // ebp
  SOCKET v6; // [esp-24h] [ebp-74h]
  unsigned int dwReceived; // [esp+8h] [ebp-48h] BYREF
  int nAddrLen; // [esp+Ch] [ebp-44h] BYREF
  unsigned int dwFlags; // [esp+10h] [ebp-40h] BYREF
  CBufferQueue bufferQueue; // [esp+14h] [ebp-3Ch] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+2Ch] [ebp-24h]
  sockaddr publicAddr; // [esp+30h] [ebp-20h] BYREF
  int v13; // [esp+4Ch] [ebp-4h]

  CBufferQueue::CBufferQueue(&bufferQueue);
  v13 = 0;
  memset(&publicAddr, 0, sizeof(publicAddr));
  v6 = *(_DWORD *)(a2 + 48);
  dwReceived = 0;
  dwFlags = 0;
  nAddrLen = 16;
  if ( WSARecvFrom(v6, &wsaBuf, 1u, &dwReceived, &dwFlags, &publicAddr, &nAddrLen, 0, 0) != -1
    || WSAGetLastError() == 10035 )
  {
    if ( !CRylServerDispatch::CreatePacket((CBufferFactory *)(a2 + 80), &bufferQueue, (int)wsaBuf.buf, &dwReceived) )
    {
      v5 = CBufferQueue::dequeue(&bufferQueue);
      if ( v5 )
      {
        INET_Addr::set_addr(&v5->address_, &publicAddr, nAddrLen);
        sync.m_Lock = (CCSLock *)(a2 + 8);
        EnterCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
        LOBYTE(v13) = 1;
        CBufferQueue::enqueue((CBufferQueue *)(a2 + 56), v5, 0);
        LOBYTE(v13) = 0;
        LeaveCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
      }
      if ( bufferQueue.m_bufferNum )
        CBufferQueue::clear(&bufferQueue);
    }
    v13 = -1;
    CBufferQueue::~CBufferQueue(&bufferQueue);
    return 1;
  }
  else
  {
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CUDPWish::ReceivePacket",
      aDWorkRylSource_71,
      185,
      "UDPWish Error. WSARecvFrom error : %d",
      Error);
    v13 = -1;
    CBufferQueue::~CBufferQueue(&bufferQueue);
    return 0;
  }
}

//----- (00408080) --------------------------------------------------------
void __usercall CUDPWish::Process(CUDPWish *this@<ecx>, int a2@<edi>)
{
  unsigned int v2; // ebp
  CBuffer *v3; // eax
  CBuffer *v4; // esi
  PktBase *rd_ptr; // edx
  CUDPWish *v6; // [esp+0h] [ebp-38h]
  CBufferQueue processQueue; // [esp+14h] [ebp-24h] BYREF
  int v8; // [esp+34h] [ebp-4h]

  CBufferQueue::CBufferQueue(&processQueue);
  v2 = 0;
  v8 = 0;
  EnterCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
  LOBYTE(v8) = 1;
  CBufferQueue::splice(&processQueue, (CBufferQueue *)(a2 + 56), 0);
  LOBYTE(v8) = 0;
  LeaveCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
  if ( *(_DWORD *)(a2 + 52) )
  {
    do
    {
      v3 = CBufferQueue::dequeue(&processQueue);
      v4 = v3;
      if ( !v3 )
        break;
      rd_ptr = (PktBase *)v3->rd_ptr_;
      if ( rd_ptr->m_Cmd == 30 )
        CUDPWish::ProcessCharUpdateAddress((const sockaddr_in *)&v3->address_, rd_ptr, v6);
      v4->bufferfactory_->Release(v4->bufferfactory_, v4);
      ++v2;
    }
    while ( v2 < *(_DWORD *)(a2 + 52) );
  }
  if ( processQueue.m_bufferNum )
  {
    CServerLog::DetailLog(
      &g_Log,
      LOG_DETAIL,
      "CUDPWish::Process",
      aDWorkRylSource_71,
      250,
      "Too many UDP Packets. Processed:%u, Remain:%u.",
      v2,
      processQueue.m_bufferNum);
    EnterCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
    LOBYTE(v8) = 2;
    CBufferQueue::splice((CBufferQueue *)(a2 + 56), &processQueue, 0);
    LeaveCriticalSection((LPCRITICAL_SECTION)(a2 + 8));
  }
  v8 = -1;
  CBufferQueue::~CBufferQueue(&processQueue);
}
// 4080FF: variable 'v6' is possibly undefined

//----- (00408190) --------------------------------------------------------
void __fastcall CUDPWish::ProcessCharUpdateAddress(const sockaddr_in *publicAddr, PktBase *lpPktBase, CUDPWish *this)
{
  unsigned int m_CodePage; // ebp
  CCreatureManager *Instance; // eax
  CPacketDispatch *Character; // eax
  CPacketDispatch *m_lpDispatch; // ebx
  CSendStream *v9; // eax
  PktBase *v10; // esi
  unsigned int v11; // ecx
  char *v12; // eax
  CSingleDispatch *DispatchTable; // eax
  CPacketDispatch *v14; // ebp
  int v15; // ecx
  CSingleDispatch::Storage StoragelpDBAgentDispatch; // [esp+14h] [ebp-14h] BYREF
  CUDPWish *thisa; // [esp+24h] [ebp-4h]

  m_CodePage = lpPktBase[2].m_CodePage;
  Instance = CCreatureManager::GetInstance();
  Character = (CPacketDispatch *)CCreatureManager::GetCharacter(Instance, m_CodePage);
  m_lpDispatch = Character;
  StoragelpDBAgentDispatch.m_lpDispatch = Character;
  if ( Character )
  {
    v9 = (CSendStream *)Character[188].__vftable;
    v10 = lpPktBase + 1;
    if ( v9 )
    {
      GameServerSendPacket::SendUpdateAddressAck(v9 + 8, publicAddr, (const sockaddr_in *)v10, m_CodePage);
      m_lpDispatch = StoragelpDBAgentDispatch.m_lpDispatch;
    }
    v11 = v10->m_CodePage;
    if ( publicAddr->sin_addr.S_un.S_addr != v11 && (!(_BYTE)v11 || (_BYTE)v11 == 0xA9) )
    {
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CUDPWish::ProcessCharUpdateAddress",
        aDWorkRylSource_71,
        285,
        "CID:0x%08x/Wrong IP Client",
        m_CodePage);
      LogAddress(publicAddr, "Real Public", m_CodePage);
      LogAddress((const sockaddr_in *)v10, "Private", m_CodePage);
    }
    if ( m_lpDispatch[53].m_Session && HIWORD(m_lpDispatch[53].__vftable) != publicAddr->sin_port )
    {
      v12 = "CID:0x%08x/UDP Port changed.";
      if ( m_lpDispatch[187].m_Session != (CSession *)1 )
        v12 = "CID:0x%08x/Disconnect(reconnect) or Login.";
      CServerLog::DetailLog(
        &g_Log,
        LOG_ERROR,
        "CUDPWish::ProcessCharUpdateAddress",
        aDWorkRylSource_71,
        300,
        v12,
        m_CodePage);
      LogAddress((const sockaddr_in *)&m_lpDispatch[53], "Old Public", m_CodePage);
      LogAddress(publicAddr, "New Public", m_CodePage);
    }
    if ( m_lpDispatch[53].m_Session != (CSession *)publicAddr->sin_addr.S_un.S_addr
      || HIWORD(m_lpDispatch[53].__vftable) != publicAddr->sin_port )
    {
      DispatchTable = CDBAgentDispatch::GetDispatchTable();
      CSingleDispatch::Storage::Storage(&StoragelpDBAgentDispatch, DispatchTable);
      thisa = 0;
      v14 = StoragelpDBAgentDispatch.m_lpDispatch;
      if ( StoragelpDBAgentDispatch.m_lpDispatch )
      {
        v15 = *(int *)((char *)&m_lpDispatch[154].m_Session + 2);
        if ( v15 )
        {
          CServerLog::DetailLog(
            &g_Log,
            LOG_DETAIL,
            "CUDPWish::ProcessCharUpdateAddress",
            aDWorkRylSource_71,
            311,
            (char *)&byte_500E18,
            v15,
            m_lpDispatch[4].__vftable);
          GameClientSendPacket::SendPartyAddress(
            (CSendStream *)&v14[8],
            *(unsigned int *)((char *)&m_lpDispatch[154].m_Session + 2),
            (unsigned int)m_lpDispatch[4].__vftable,
            publicAddr,
            (const sockaddr_in *)v10,
            0x3Bu);
        }
      }
      thisa = (CUDPWish *)-1;
      CSingleDispatch::Storage::~Storage((CMultiDispatch::Storage *)&StoragelpDBAgentDispatch);
    }
    m_lpDispatch[53].__vftable = *(CPacketDispatch_vtbl **)&publicAddr->sin_family;
    m_lpDispatch[53].m_Session = (CSession *)publicAddr->sin_addr.S_un.S_addr;
    m_lpDispatch[54].__vftable = *(CPacketDispatch_vtbl **)publicAddr->sin_zero;
    m_lpDispatch[54].m_Session = *(CSession **)&publicAddr->sin_zero[4];
    m_lpDispatch[55].__vftable = *(CPacketDispatch_vtbl **)&v10->m_StartBit;
    m_lpDispatch[55].m_Session = (CSession *)v10->m_CodePage;
    m_lpDispatch[56].__vftable = (CPacketDispatch_vtbl *)v10->m_SrvInfo.dwServerInfo;
    m_lpDispatch[56].m_Session = *(CSession **)&v10[1].m_StartBit;
    CCharacter::CharacterCellLogin((CCharacter *)m_lpDispatch);
  }
}

//----- (004083A0) --------------------------------------------------------
void __thiscall CServerLog::CServerLog(CServerLog *this, const char *szLogName, const char *szPathName)
{
  this->__vftable = (CServerLog_vtbl *)&CServerLog::`vftable';
  InitializeCriticalSection(&this->m_ServerLogLock.m_CSLock);
  this->m_hFile = (void *)-1;
  this->m_dwLogEnabled = -1;
  this->m_dwWritten = 0;
  this->m_dwBufferUsed = 0;
  if ( szLogName )
  {
    _snprintf(this->m_szProgramName, 0x103u, "%s", szLogName);
    this->m_szProgramName[259] = 0;
  }
  else
  {
    DbgUtils::SetProgramName(this->m_szProgramName, 0x104u, 0);
  }
  if ( szPathName )
  {
    _snprintf(this->m_szLogPath, 0x103u, "%s", szPathName);
    this->m_szLogPath[259] = 0;
  }
  else
  {
    DbgUtils::SetProgramName(this->m_szLogPath, 0x104u, 0);
  }
  this->m_lpBuffer = (char *)operator new[](0x40000u, &std::nothrow);
}
// 4D534C: using guessed type void *CServerLog::`vftable';

//----- (00408490) --------------------------------------------------------
int __thiscall CServerLog::CreateLogFile(CServerLog *this)
{
  char *m_szLogPath; // esi
  int v3; // edi
  _SYSTEMTIME sysTime; // [esp+Ch] [ebp-21Ch] BYREF
  char szFileName[260]; // [esp+1Ch] [ebp-20Ch] BYREF
  char szTime[260]; // [esp+120h] [ebp-108h] BYREF

  m_szLogPath = this->m_szLogPath;
  if ( GetFileAttributesA(this->m_szLogPath) == -1 && !CreateDirectoryA(m_szLogPath, 0) )
    return -1;
  GetLocalTime(&sysTime);
  _snprintf(
    szTime,
    0x104u,
    "%s%04d-%02d-%02d %02d,%02d,%02d-",
    this->m_szProgramName,
    sysTime.wYear,
    sysTime.wMonth,
    sysTime.wDay,
    sysTime.wHour,
    sysTime.wMinute,
    sysTime.wSecond);
  v3 = 0;
  if ( _snprintf(szFileName, 0x104u, "%s/%s%04d.log", m_szLogPath, szTime, 0) < 0 )
    return -1;
  while ( GetFileAttributesA(szFileName) != -1 )
  {
    if ( _snprintf(szFileName, 0x104u, "%s/%s%04d.log", m_szLogPath, szTime, ++v3) < 0 )
      return -1;
  }
  return (int)CreateFileA(szFileName, 0x40000000u, 1u, 0, 1u, 0x80u, 0);
}

//----- (004085C0) --------------------------------------------------------
void __thiscall CServerLog::InternalFlush(CServerLog *this)
{
  bool v2; // zf
  unsigned int v3; // eax
  unsigned int dwWritten; // [esp+4h] [ebp-4h] BYREF

  v2 = this->m_hFile == (void *)-1;
  dwWritten = 0;
  if ( v2 )
    this->m_hFile = (void *)CServerLog::CreateLogFile(this);
  WriteFile(this->m_hFile, this->m_lpBuffer, this->m_dwBufferUsed, &dwWritten, 0);
  v3 = dwWritten + this->m_dwWritten;
  this->m_dwWritten = v3;
  this->m_dwBufferUsed = 0;
  if ( v3 > 0x6400000 )
  {
    CloseHandle(this->m_hFile);
    this->m_hFile = (void *)CServerLog::CreateLogFile(this);
    this->m_dwWritten = 0;
  }
}

//----- (00408630) --------------------------------------------------------
void CServerLog::SimpleLog(CServerLog *this, int eLogType, char *pFormat, ...)
{
  int v3; // eax
  unsigned int v4; // eax
  _SYSTEMTIME SystemTime; // [esp+8h] [ebp-4014h] BYREF
  char string[16384]; // [esp+18h] [ebp-4004h] BYREF
  va_list ap; // [esp+402Ch] [ebp+10h] BYREF

  va_start(ap, pFormat);
  if ( ((1 << eLogType) & this->m_dwLogEnabled) != 0 && this->m_lpBuffer )
  {
    v3 = 0;
    if ( eLogType )
    {
      if ( eLogType > 0 && eLogType <= 2 )
      {
        GetLocalTime(&SystemTime);
        v3 = _snprintf(
               string,
               0x4000u,
               "[Ty-%s][Tm-%04d-%02d-%02d %02d:%02d:%02d][Ex-",
               s_LogType[eLogType],
               SystemTime.wYear,
               SystemTime.wMonth,
               SystemTime.wDay,
               SystemTime.wHour,
               SystemTime.wMinute,
               SystemTime.wSecond);
      }
    }
    else
    {
      v3 = _snprintf(string, 0x4000u, "[Ty-%s][", s_LogType[0]);
    }
    _vsnprintf(&string[v3], 0x4000 - v3, pFormat, ap);
    EnterCriticalSection(&this->m_ServerLogLock.m_CSLock);
    v4 = _snprintf(&this->m_lpBuffer[this->m_dwBufferUsed], 0x40000 - this->m_dwBufferUsed, "%s]\r\n", string)
       + this->m_dwBufferUsed;
    this->m_dwBufferUsed = v4;
    if ( v4 >= 0x3E000 )
      CServerLog::InternalFlush(this);
    LeaveCriticalSection(&this->m_ServerLogLock.m_CSLock);
  }
}

//----- (00408780) --------------------------------------------------------
void CServerLog::DetailLog(
        CServerLog *this,
        LOG_TYPE eLogType,
        const char *pRtn,
        const char *pFileName,
        int nLine,
        char *pFormat,
        ...)
{
  int v6; // eax
  unsigned int v7; // eax
  _SYSTEMTIME SystemTime; // [esp+8h] [ebp-4014h] BYREF
  char string[16384]; // [esp+18h] [ebp-4004h] BYREF
  va_list ap; // [esp+4038h] [ebp+1Ch] BYREF

  va_start(ap, pFormat);
  if ( ((1 << eLogType) & this->m_dwLogEnabled) != 0 && this->m_lpBuffer )
  {
    GetLocalTime(&SystemTime);
    v6 = _snprintf(
           string,
           0x4000u,
           "[Ty-%s][Tm-%04d-%02d-%02d %02d:%02d:%02d][Ex-",
           s_LogType[eLogType],
           SystemTime.wYear,
           SystemTime.wMonth,
           SystemTime.wDay,
           SystemTime.wHour,
           SystemTime.wMinute,
           SystemTime.wSecond);
    _vsnprintf(&string[v6], 0x4000 - v6, pFormat, ap);
    EnterCriticalSection(&this->m_ServerLogLock.m_CSLock);
    v7 = _snprintf(
           &this->m_lpBuffer[this->m_dwBufferUsed],
           0x40000 - this->m_dwBufferUsed,
           "%s][Rt-%s][FN-%s][LN-%d]\r\n",
           string,
           pRtn,
           pFileName,
           nLine)
       + this->m_dwBufferUsed;
    this->m_dwBufferUsed = v7;
    if ( v7 >= 0x3E000 || eLogType == LOG_SYSERR )
      CServerLog::InternalFlush(this);
    LeaveCriticalSection(&this->m_ServerLogLock.m_CSLock);
  }
}

//----- (004088C0) --------------------------------------------------------
void __thiscall CServerLog::SetLogFileName(CServerLog *this, const char *szLogName, const char *szPathName)
{
  CCSLock *p_m_ServerLogLock; // edi

  p_m_ServerLogLock = &this->m_ServerLogLock;
  EnterCriticalSection(&this->m_ServerLogLock.m_CSLock);
  if ( this->m_dwBufferUsed )
    CServerLog::InternalFlush(this);
  if ( this->m_hFile != (void *)-1 )
  {
    CloseHandle(this->m_hFile);
    this->m_hFile = (void *)-1;
  }
  this->m_dwWritten = 0;
  if ( szLogName )
  {
    _snprintf(this->m_szProgramName, 0x103u, "%s", szLogName);
    this->m_szProgramName[259] = 0;
  }
  else
  {
    DbgUtils::SetProgramName(this->m_szProgramName, 0x104u, 0);
  }
  if ( szPathName )
  {
    _snprintf(this->m_szLogPath, 0x103u, "%s", szPathName);
    this->m_szLogPath[259] = 0;
  }
  else
  {
    DbgUtils::SetProgramName(this->m_szLogPath, 0x104u, 0);
  }
  LeaveCriticalSection(&p_m_ServerLogLock->m_CSLock);
}

//----- (004089B0) --------------------------------------------------------
void __thiscall CServerLog::~CServerLog(CServerLog *this)
{
  CCSLock *p_m_ServerLogLock; // edi

  p_m_ServerLogLock = &this->m_ServerLogLock;
  this->__vftable = (CServerLog_vtbl *)&CServerLog::`vftable';
  EnterCriticalSection(&this->m_ServerLogLock.m_CSLock);
  CServerLog::SimpleLog(this, 0, (char *)&byte_4D53E8);
  if ( this->m_dwBufferUsed )
    CServerLog::InternalFlush(this);
  if ( this->m_hFile != (void *)-1 )
    CloseHandle(this->m_hFile);
  operator delete[](this->m_lpBuffer);
  this->m_lpBuffer = 0;
  LeaveCriticalSection(&p_m_ServerLogLock->m_CSLock);
  DeleteCriticalSection(&p_m_ServerLogLock->m_CSLock);
}
// 4D534C: using guessed type void *CServerLog::`vftable';

//----- (00408A20) --------------------------------------------------------
CServerLog *__thiscall CServerLog::`scalar deleting destructor'(CServerLog *this, char a2)
{
  CServerLog::~CServerLog(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00408A40) --------------------------------------------------------
void __thiscall CPerformanceCheck::SetUserMessageFunc(
        CPerformanceCheck *this,
        void (__cdecl *fnPreFix)(_iobuf *),
        void (__cdecl *fnPostFix)(_iobuf *))
{
  this->m_fnPreFix = fnPreFix;
  this->m_fnPostFix = fnPostFix;
}

//----- (00408A60) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Min(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Left; !i->_Isnil; i = i->_Left )
    result = i;
  return result;
}

//----- (00408A80) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Max(
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Pnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *result; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *i; // ecx

  result = _Pnode;
  for ( i = _Pnode->_Right; !i->_Isnil; i = i->_Right )
    result = i;
  return result;
}

//----- (00408AA0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::const_iterator *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Right; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *i; // eax

  Ptr = this->_Ptr;
  if ( !this->_Ptr->_Isnil )
  {
    Right = Ptr->_Right;
    if ( Right->_Isnil )
    {
      for ( i = Ptr->_Parent; !i->_Isnil; i = i->_Parent )
      {
        if ( this->_Ptr != i->_Right )
          break;
        this->_Ptr = i;
      }
      this->_Ptr = i;
    }
    else
    {
      for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
        Right = j;
      this->_Ptr = Right;
    }
  }
}

//----- (00408B00) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // ecx

  Right = _Wherenode->_Right;
  _Wherenode->_Right = Right->_Left;
  if ( !Right->_Left->_Isnil )
    Right->_Left->_Parent = _Wherenode;
  Right->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Left )
      Parent->_Left = Right;
    else
      Parent->_Right = Right;
    Right->_Left = _Wherenode;
    _Wherenode->_Parent = Right;
  }
}

//----- (00408B60) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Right; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // ecx

  Left = _Wherenode->_Left;
  _Wherenode->_Left = _Wherenode->_Left->_Right;
  Right = Left->_Right;
  if ( !Right->_Isnil )
    Right->_Parent = _Wherenode;
  Left->_Parent = _Wherenode->_Parent;
  Myhead = this->_Myhead;
  if ( _Wherenode == Myhead->_Parent )
  {
    Myhead->_Parent = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
  else
  {
    Parent = _Wherenode->_Parent;
    if ( _Wherenode == Parent->_Right )
      Parent->_Right = Left;
    else
      Parent->_Left = Left;
    Left->_Right = _Wherenode;
    _Wherenode->_Parent = Left;
  }
}

//----- (00408BC0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (00408C00) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *)operator new((tagHeader *)0x48);
  if ( result )
    result->_Left = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *)-4 )
    result->_Parent = 0;
  if ( result != (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *)-8 )
    result->_Right = 0;
  result->_Color = 1;
  result->_Isnil = 0;
  return result;
}

//----- (00408C40) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Rarg,
        const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val,
        char _Carg)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *result; // eax

  result = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *)operator new((tagHeader *)0x48);
  if ( result )
  {
    result->_Left = _Larg;
    result->_Right = _Rarg;
    result->_Parent = _Parg;
    qmemcpy(&result->_Myval, _Val, sizeof(result->_Myval));
    result->_Color = _Carg;
    result->_Isnil = 0;
  }
  return result;
}

//----- (00408C90) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::equal_range(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> *result,
        const unsigned int *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v5; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v6; // eax
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> *v7; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( *_Keyval >= Parent->_Myval.first )
    {
      Parent = Parent->_Right;
    }
    else
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
  }
  v5 = this->_Myhead;
  v6 = v5->_Parent;
  while ( !v6->_Isnil )
  {
    if ( v6->_Myval.first >= *_Keyval )
    {
      v5 = v6;
      v6 = v6->_Left;
    }
    else
    {
      v6 = v6->_Right;
    }
  }
  v7 = result;
  result->first._Ptr = v5;
  result->second._Ptr = Myhead;
  return v7;
}

//----- (00408D00) --------------------------------------------------------
char __thiscall CPerformanceCheck::PrintAllTime(CPerformanceCheck *this, const char *fileName, bool bReset)
{
  int v3; // edi
  HANDLE i; // esi
  CCSLock *m_Lock; // ebx
  _iobuf *v6; // esi
  void (__cdecl *OwningThread)(_iobuf *); // eax
  unsigned int *LockCount; // edi
  unsigned int v10; // ecx
  double v11; // st7
  void (__cdecl *LockSemaphore)(_iobuf *); // eax
  unsigned int *v13; // esi
  unsigned int v14; // eax
  int v15; // [esp+20h] [ebp-250h]
  const char *v16; // [esp+24h] [ebp-24Ch]
  unsigned int dwHighValue; // [esp+34h] [ebp-23Ch] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+38h] [ebp-238h]
  unsigned int dwFileSize; // [esp+3Ch] [ebp-234h]
  double fTotalAvgTime; // [esp+40h] [ebp-230h]
  char strLogPath[264]; // [esp+48h] [ebp-228h] BYREF
  char strLogFileName[268]; // [esp+150h] [ebp-120h] BYREF
  int v23; // [esp+26Ch] [ebp-4h]

  sync.m_Lock = &this->m_TableLock;
  if ( !this->m_InstrumentTable._Mysize )
    return 0;
  DbgUtils::SetProgramName(strLogPath, 0x104u, 0);
  if ( GetFileAttributesA(strLogPath) == -1 && !CreateDirectoryA(strLogPath, 0) )
    return 0;
  v3 = 0;
  _snprintf(strLogFileName, 0x104u, "%s/PerformanceLog-%s-%05d.log", strLogPath, fileName, 0);
  for ( i = CreateFileA(strLogFileName, 0x40000000u, 1u, 0, 3u, 0x80u, 0);
        i != (HANDLE)-1;
        i = CreateFileA(strLogFileName, 0x40000000u, 1u, 0, 3u, 0x80u, 0) )
  {
    dwHighValue = 0;
    dwFileSize = GetFileSize(i, &dwHighValue);
    CloseHandle(i);
    if ( !dwHighValue && dwFileSize < 0x3200000 )
      break;
    _snprintf(strLogFileName, 0x104u, "%s/PerformanceLog-%s-%05d.log", strLogPath, fileName, ++v3);
  }
  m_Lock = sync.m_Lock;
  EnterCriticalSection(&sync.m_Lock->m_CSLock);
  v23 = 0;
  v6 = fopen(strLogFileName, "at");
  if ( !v6 )
  {
    LeaveCriticalSection(&m_Lock->m_CSLock);
    return 0;
  }
  OwningThread = (void (__cdecl *)(_iobuf *))m_Lock[2].m_CSLock.OwningThread;
  if ( OwningThread )
    OwningThread(v6);
  fprintf(
    v6,
    "    Average :    Minimum :    Maximum :        Total :         # :   Profile Name\n"
    "------------------------------------------------------------------------------------------------\n");
  LockCount = (unsigned int *)m_Lock[2].m_CSLock.LockCount;
  fTotalAvgTime = 0.0;
  v10 = *LockCount;
  for ( dwHighValue = *LockCount; (unsigned int *)dwHighValue != LockCount; v10 = dwHighValue )
  {
    if ( *(_DWORD *)(v10 + 56) )
    {
      if ( 0.0 == *(double *)(v10 + 24) )
        v11 = 0.0;
      else
        v11 = *(double *)(v10 + 24) / (double)*(unsigned int *)(v10 + 52);
      v16 = *(const char **)(v10 + 56);
      v15 = *(_DWORD *)(v10 + 52);
      fTotalAvgTime = fTotalAvgTime + v11;
      fprintf(
        v6,
        "   %3.6f     %3.6f     %3.6f      %8.1f    %10d    %s\n",
        v11,
        *(double *)(v10 + 32),
        *(double *)(v10 + 40),
        *(double *)(v10 + 24),
        v15,
        v16);
    }
    std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::const_iterator *)&dwHighValue);
  }
  fprintf(v6, "\n\n Total AvgTime : %3.6f\n\n\n", fTotalAvgTime);
  LockSemaphore = (void (__cdecl *)(_iobuf *))m_Lock[2].m_CSLock.LockSemaphore;
  if ( LockSemaphore )
    LockSemaphore(v6);
  fclose(v6);
  if ( bReset )
  {
    v13 = (unsigned int *)m_Lock[2].m_CSLock.LockCount;
    v14 = *v13;
    for ( dwHighValue = *v13; (unsigned int *)dwHighValue != v13; v14 = dwHighValue )
    {
      *(double *)(v14 + 40) = 0.0;
      *(_DWORD *)(v14 + 52) = 0;
      *(double *)(v14 + 32) = 0.0;
      *(double *)(v14 + 24) = 0.0;
      std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::const_iterator *)&dwHighValue);
    }
  }
  LeaveCriticalSection(&m_Lock->m_CSLock);
  return 1;
}

//----- (00408FE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc(&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = Ptr->_Right;
LABEL_8:
    Parent = Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = Right;
    }
    else if ( Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Min(Right);
      v9->_Left = v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = Parent;
      else
        v11->_Right = std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = _Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = v6;
  }
  else
  {
    Parent = v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
              this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
                this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
              this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
              this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
                this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
              this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004092A0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (00409360) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x5555554 )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(
         this,
         this->_Myhead,
         _Wherenode,
         this->_Myhead,
         _Val,
         0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = &v6->_Parent;
  v11 = v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
              this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
            this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(
            this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(
          this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00409510) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::insert(
        std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *result,
        const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // esi
  unsigned int first; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *v7; // eax
  bool _Addleft; // [esp+8h] [ebp-4h]

  Parent = this->_Myhead->_Parent;
  Myhead = this->_Myhead;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    first = _Val->first;
    do
    {
      Myhead = Parent;
      _Addleft = first < Parent->_Myval.first;
      if ( first >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  Ptr = std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Insert(
          this,
          (std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *)&_Val,
          _Addleft,
          Myhead,
          _Val)->_Ptr;
  v7 = result;
  result->first._Ptr = Ptr;
  result->second = 1;
  return v7;
}

//----- (00409580) --------------------------------------------------------
void __thiscall std::multimap<unsigned long,CPerformanceCheck::Instrument>::~multimap<unsigned long,CPerformanceCheck::Instrument>(
        std::multimap<unsigned long,CPerformanceCheck::Instrument> *this)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (004095B0) --------------------------------------------------------
void __thiscall CPerformanceCheck::~CPerformanceCheck(CPerformanceCheck *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Myhead; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator v3; // [esp-8h] [ebp-28h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Parent; // [esp-4h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v5; // [esp-4h] [ebp-24h]
  CLock<CCSLock>::Syncronize sync; // [esp+10h] [ebp-10h] BYREF
  int v7; // [esp+1Ch] [ebp-4h]

  v7 = 0;
  sync.m_Lock = &this->m_TableLock;
  EnterCriticalSection(&this->m_TableLock.m_CSLock);
  Parent = this->m_InstrumentTable._Myhead->_Parent;
  LOBYTE(v7) = 2;
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Erase(
    &this->m_InstrumentTable,
    Parent);
  this->m_InstrumentTable._Myhead->_Parent = this->m_InstrumentTable._Myhead;
  Myhead = this->m_InstrumentTable._Myhead;
  this->m_InstrumentTable._Mysize = 0;
  Myhead->_Left = Myhead;
  this->m_InstrumentTable._Myhead->_Right = this->m_InstrumentTable._Myhead;
  LeaveCriticalSection(&this->m_TableLock.m_CSLock);
  v5 = this->m_InstrumentTable._Myhead;
  v3._Ptr = v5->_Left;
  LOBYTE(v7) = 0;
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(
    &this->m_InstrumentTable,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *)&sync,
    v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator)v5);
  operator delete(this->m_InstrumentTable._Myhead);
  this->m_InstrumentTable._Myhead = 0;
  this->m_InstrumentTable._Mysize = 0;
  DeleteCriticalSection(&this->m_TableLock.m_CSLock);
}

//----- (00409660) --------------------------------------------------------
void __thiscall CPerformanceCheck::AddTime(CPerformanceCheck *this, const char *szfunctionName, double fEstimateTime)
{
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // eax
  const char *v4; // esi
  int v5; // eax
  unsigned int v6; // edi
  const char *i; // ecx
  long double v9; // st7
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *Ptr; // ebp
  long double v11; // st7
  unsigned int m_dwCalled; // ecx
  unsigned int dwHashedKey; // [esp+0h] [ebp-74h] BYREF
  CLock<CCSLock>::Syncronize sync; // [esp+4h] [ebp-70h]
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> itrPair; // [esp+8h] [ebp-6Ch] BYREF
  double v16[5]; // [esp+10h] [ebp-64h] BYREF
  std::pair<unsigned long const ,CPerformanceCheck::Instrument> _Val; // [esp+38h] [ebp-3Ch] BYREF
  struct _EXCEPTION_REGISTRATION_RECORD *v18; // [esp+68h] [ebp-Ch]
  void *v19; // [esp+6Ch] [ebp-8h]
  int v20; // [esp+70h] [ebp-4h]

  v20 = -1;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  v19 = &_ehhandler__AddTime_CPerformanceCheck__QAEXPBDN_Z;
  v18 = ExceptionList;
  v4 = szfunctionName;
  v5 = *(unsigned __int8 *)szfunctionName;
  v6 = 0;
  for ( i = szfunctionName; *i; v5 = *(unsigned __int8 *)i )
  {
    ++i;
    v6 = v5 + 65599 * v6;
  }
  dwHashedKey = v6;
  sync.m_Lock = &this->m_TableLock;
  EnterCriticalSection(&this->m_TableLock.m_CSLock);
  v9 = fEstimateTime / this->m_fFrequency;
  v20 = 0;
  fEstimateTime = v9;
  std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::equal_range(
    &this->m_InstrumentTable,
    &itrPair,
    &dwHashedKey);
  Ptr = itrPair.first._Ptr;
  if ( itrPair.first._Ptr == itrPair.second._Ptr )
  {
LABEL_7:
    _Val.first = v6;
    *(_QWORD *)&v16[3] = v6 | 0x100000000LL;
    v16[0] = fEstimateTime;
    LODWORD(v16[4]) = v4;
    v16[1] = fEstimateTime;
    v16[2] = fEstimateTime;
    qmemcpy(&_Val.second, v16, sizeof(_Val.second));
    std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::insert(
      &this->m_InstrumentTable,
      (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *)&fEstimateTime,
      &_Val);
  }
  else
  {
    while ( strcmp(szfunctionName, Ptr->_Myval.second.m_szFunctionName) )
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc(&itrPair.first);
      Ptr = itrPair.first._Ptr;
      if ( itrPair.first._Ptr == itrPair.second._Ptr )
      {
        v4 = szfunctionName;
        goto LABEL_7;
      }
    }
    v11 = fEstimateTime;
    Ptr->_Myval.second.m_fProcessTime = fEstimateTime + Ptr->_Myval.second.m_fProcessTime;
    if ( v11 < Ptr->_Myval.second.m_fMinTime )
      Ptr->_Myval.second.m_fMinTime = v11;
    m_dwCalled = Ptr->_Myval.second.m_dwCalled;
    if ( v11 > Ptr->_Myval.second.m_fMaxTime )
      Ptr->_Myval.second.m_fMaxTime = v11;
    Ptr->_Myval.second.m_dwCalled = m_dwCalled + 1;
  }
  LeaveCriticalSection(&this->m_TableLock.m_CSLock);
}

//----- (004097F0) --------------------------------------------------------
void __thiscall CPerformanceCheck::CPerformanceCheck(CPerformanceCheck *this)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *v2; // eax
  _LARGE_INTEGER StartFrequency; // [esp+14h] [ebp-14h]

  InitializeCriticalSection(&this->m_TableLock.m_CSLock);
  v2 = std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(&this->m_InstrumentTable);
  this->m_InstrumentTable._Myhead = v2;
  v2->_Isnil = 1;
  this->m_InstrumentTable._Myhead->_Parent = this->m_InstrumentTable._Myhead;
  this->m_InstrumentTable._Myhead->_Left = this->m_InstrumentTable._Myhead;
  this->m_InstrumentTable._Myhead->_Right = this->m_InstrumentTable._Myhead;
  this->m_InstrumentTable._Mysize = 0;
  this->m_fnPreFix = 0;
  this->m_fnPostFix = 0;
  StartFrequency.QuadPart = __rdtsc();
  Sleep(0x3E8u);
  this->m_Frequency.QuadPart = __rdtsc() - StartFrequency.QuadPart - 57;
  this->m_fFrequency = (double)this->m_Frequency.QuadPart;
}

//----- (004098B0) --------------------------------------------------------
CPerformanceCheck *__cdecl CPerformanceCheck::GetInstance()
{
  if ( (_S1 & 1) == 0 )
  {
    _S1 |= 1u;
    CPerformanceCheck::CPerformanceCheck(&performanceCheck);
    atexit(_E2_2);
  }
  return &performanceCheck;
}

//----- (00409910) --------------------------------------------------------
void __cdecl DbgUtils::SetProgramName(char *pszOutBuffer, size_t nBufferSize, char *pszProgramName)
{
  _BYTE *v3; // eax
  char szFilename[260]; // [esp+0h] [ebp-414h] BYREF
  char szExt[260]; // [esp+104h] [ebp-310h] BYREF
  char szDir[260]; // [esp+208h] [ebp-20Ch] BYREF
  char szDrive[260]; // [esp+30Ch] [ebp-108h] BYREF

  if ( pszProgramName )
  {
    _mbsnbcpy((unsigned __int8 *)pszOutBuffer, (const unsigned __int8 *)pszProgramName, nBufferSize);
  }
  else
  {
    GetModuleFileNameA(0, pszOutBuffer, nBufferSize);
    _mbsrchr((unsigned __int8 *)pszOutBuffer, 0x2Eu);
    if ( v3 )
      *v3 = 0;
    _splitpath(pszOutBuffer, szDrive, szDir, szFilename, szExt);
    _mbsnbcpy((unsigned __int8 *)pszOutBuffer, (const unsigned __int8 *)szFilename, nBufferSize);
  }
}
// 409953: variable 'v3' is possibly undefined

//----- (004099D0) --------------------------------------------------------
COverlappedFactory *__thiscall COverlappedFactory::`scalar deleting destructor'(COverlappedFactory *this, char a2)
{
  this->__vftable = (COverlappedFactory_vtbl *)&COverlappedFactory::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D5558: using guessed type void *COverlappedFactory::`vftable';

//----- (004099F0) --------------------------------------------------------
void __thiscall CSendOverlapped::CSendOverlapped(
        CSendOverlapped *this,
        COverlappedFactory *ovlFactory,
        CBuffer *lpSendBuffer)
{
  this->m_ovlFactory = ovlFactory;
  this->m_lpBuffer = lpSendBuffer;
  this->OffsetHigh = 0;
  this->Offset = 0;
  this->InternalHigh = 0;
  this->Internal = 0;
  this->hEvent = 0;
  this->__vftable = (CSendOverlapped_vtbl *)&CSendOverlapped::`vftable';
}
// 4D5574: using guessed type void *CSendOverlapped::`vftable';

//----- (00409A20) --------------------------------------------------------
void __thiscall CSendOverlapped::Dispatch(
        CSendOverlapped *this,
        int bResult,
        CSession *lpSessionKey,
        unsigned int dwProcessedBytes)
{
  CSession::SendCompleted(lpSessionKey, bResult, dwProcessedBytes);
  this->m_ovlFactory->DeleteOverlapped(this->m_ovlFactory, this);
}

//----- (00409A50) --------------------------------------------------------
void __thiscall CStreamRecvOverlapped::CStreamRecvOverlapped(
        CStreamRecvOverlapped *this,
        COverlappedFactory *ovlFactory)
{
  this->m_ovlFactory = ovlFactory;
  this->m_lpBuffer = 0;
  this->OffsetHigh = 0;
  this->Offset = 0;
  this->InternalHigh = 0;
  this->Internal = 0;
  this->hEvent = 0;
  this->__vftable = (CStreamRecvOverlapped_vtbl *)&CStreamRecvOverlapped::`vftable';
}
// 4D557C: using guessed type void *CStreamRecvOverlapped::`vftable';

//----- (00409A80) --------------------------------------------------------
void __thiscall CAcceptOverlapped::CAcceptOverlapped(
        CAcceptOverlapped *this,
        COverlappedFactory *ovlFactory,
        CListener *Listener,
        unsigned int hSocket,
        CBuffer *lpAddrBuffer)
{
  this->m_ovlFactory = ovlFactory;
  this->m_lpBuffer = lpAddrBuffer;
  this->OffsetHigh = 0;
  this->Offset = 0;
  this->InternalHigh = 0;
  this->Internal = 0;
  this->hEvent = 0;
  this->__vftable = (CAcceptOverlapped_vtbl *)&CAcceptOverlapped::`vftable';
  this->m_Listener = Listener;
  this->m_hSocket = hSocket;
}
// 4D5584: using guessed type void *CAcceptOverlapped::`vftable';

//----- (00409AC0) --------------------------------------------------------
void __thiscall CAcceptOverlapped::Dispatch(
        CAcceptOverlapped *this,
        int bResult,
        CListener *lpSessionKey,
        unsigned int dwProcessedBytes)
{
  CListener::ProcessAccept(lpSessionKey, bResult, (void *)this->m_hSocket, this->m_lpBuffer, dwProcessedBytes);
  this->m_ovlFactory->DeleteOverlapped(this->m_ovlFactory, this);
}

//----- (00409AF0) --------------------------------------------------------
CAcceptOverlapped *__thiscall CAcceptOverlapped::`vector deleting destructor'(CAcceptOverlapped *this, char a2)
{
  CSendOverlapped::~CSendOverlapped(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00409B10) --------------------------------------------------------
void __thiscall CSendOverlapped::~CSendOverlapped(CAcceptOverlapped *this)
{
  CBuffer *m_lpBuffer; // eax

  m_lpBuffer = this->m_lpBuffer;
  this->__vftable = (CAcceptOverlapped_vtbl *)&COverlapped::`vftable';
  if ( m_lpBuffer )
  {
    m_lpBuffer->bufferfactory_->Release(m_lpBuffer->bufferfactory_, m_lpBuffer);
    this->m_lpBuffer = 0;
  }
}
// 4D556C: using guessed type void *COverlapped::`vftable';

//----- (00409B40) --------------------------------------------------------
COverlapped *__thiscall COverlapped::`scalar deleting destructor'(COverlapped *this, char a2)
{
  CBuffer *m_lpBuffer; // eax

  m_lpBuffer = this->m_lpBuffer;
  this->__vftable = (COverlapped_vtbl *)&COverlapped::`vftable';
  if ( m_lpBuffer )
  {
    m_lpBuffer->bufferfactory_->Release(m_lpBuffer->bufferfactory_, m_lpBuffer);
    this->m_lpBuffer = 0;
  }
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D556C: using guessed type void *COverlapped::`vftable';

//----- (00409B80) --------------------------------------------------------
void __thiscall CStreamRecvOverlapped::Dispatch(
        CStreamRecvOverlapped *this,
        int bResult,
        CSession *lpSessionKey,
        unsigned int dwProcessedBytes)
{
  if ( CSession::Dispatch(lpSessionKey, dwProcessedBytes) )
    CSession::Recv(lpSessionKey);
  this->m_ovlFactory->DeleteOverlapped(this->m_ovlFactory, this);
}

//----- (00409BB0) --------------------------------------------------------
char __thiscall CSession::Recv(CSession *this)
{
  char v2; // bl

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v2 = CSession::InternalRecv(this);
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return v2;
}


//----- (00409C00) --------------------------------------------------------
void __thiscall CStreamOverlappedFactory::CStreamOverlappedFactory(CStreamOverlappedFactory *this)
{
  CCSLock *p_m_Lock; // edi
  boost::pool<boost::default_user_allocator_new_delete> *v3; // eax

  p_m_Lock = &this->m_Lock;
  this->__vftable = (CStreamOverlappedFactory_vtbl *)&CStreamOverlappedFactory::`vftable';
  InitializeCriticalSection(&this->m_Lock.m_CSLock);
  EnterCriticalSection(&p_m_Lock->m_CSLock);
  v3 = (boost::pool<boost::default_user_allocator_new_delete> *)operator new((tagHeader *)0x14);
  if ( v3 )
  {
    v3->first = 0;
    v3->list.ptr = 0;
    v3->list.sz = 0;
    v3->requested_size = 40;
    v3->next_size = 32;
    this->m_lpOverlappedPool = v3;
  }
  else
  {
    this->m_lpOverlappedPool = 0;
  }
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
}
// 4D558C: using guessed type void *CStreamOverlappedFactory::`vftable';

//----- (00409C70) --------------------------------------------------------
void __thiscall CStreamOverlappedFactory::DeleteOverlapped(CStreamOverlappedFactory *this, COverlapped *lpOverlapped)
{
  CCSLock *p_m_Lock; // ebx
  COverlapped_vtbl **m_lpOverlappedPool; // edi

  p_m_Lock = &this->m_Lock;
  EnterCriticalSection(&this->m_Lock.m_CSLock);
  if ( lpOverlapped && this->m_lpOverlappedPool )
  {
    ((void (__thiscall *)(COverlapped *, _DWORD))lpOverlapped->~COverlapped)(lpOverlapped, 0);
    m_lpOverlappedPool = (COverlapped_vtbl **)this->m_lpOverlappedPool;
    lpOverlapped->__vftable = *m_lpOverlappedPool;
    *m_lpOverlappedPool = (COverlapped_vtbl *)lpOverlapped;
  }
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
}

//----- (00409CE0) --------------------------------------------------------
COverlapped *__thiscall CStreamOverlappedFactory::CreateSend(
        CStreamOverlappedFactory *this,
        CSession *lpSession,
        CBuffer *lpMsgBlock)
{
  CCSLock *p_m_Lock; // ebx
  int v5; // edi
  boost::pool<boost::default_user_allocator_new_delete> *m_lpOverlappedPool; // ecx
  CSendOverlapped *first; // eax
  int v8; // eax

  p_m_Lock = &this->m_Lock;
  v5 = 0;
  EnterCriticalSection(&this->m_Lock.m_CSLock);
  if ( lpSession )
  {
    if ( lpMsgBlock )
    {
      m_lpOverlappedPool = this->m_lpOverlappedPool;
      if ( m_lpOverlappedPool )
      {
        first = (CSendOverlapped *)m_lpOverlappedPool->first;
        if ( m_lpOverlappedPool->first )
          m_lpOverlappedPool->first = first->__vftable;
        else
          first = (CSendOverlapped *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpOverlappedPool);
        if ( first )
        {
          CSendOverlapped::CSendOverlapped(first, this, lpMsgBlock);
          v5 = v8;
        }
      }
    }
  }
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
  return (COverlapped *)v5;
}
// 409D27: variable 'v8' is possibly undefined

//----- (00409D40) --------------------------------------------------------
COverlapped *__thiscall CStreamOverlappedFactory::CreateRecv(
        CStreamOverlappedFactory *this,
        CSession *lpSession,
        CBuffer *lpMsgBlock)
{
  CCSLock *p_m_Lock; // ebx
  int v5; // edi
  boost::pool<boost::default_user_allocator_new_delete> *m_lpOverlappedPool; // ecx
  CStreamRecvOverlapped *first; // eax
  int v8; // eax

  p_m_Lock = &this->m_Lock;
  v5 = 0;
  EnterCriticalSection(&this->m_Lock.m_CSLock);
  if ( lpSession )
  {
    if ( lpMsgBlock )
    {
      m_lpOverlappedPool = this->m_lpOverlappedPool;
      if ( m_lpOverlappedPool )
      {
        first = (CStreamRecvOverlapped *)m_lpOverlappedPool->first;
        if ( m_lpOverlappedPool->first )
          m_lpOverlappedPool->first = first->__vftable;
        else
          first = (CStreamRecvOverlapped *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpOverlappedPool);
        if ( first )
        {
          CStreamRecvOverlapped::CStreamRecvOverlapped(first, this);
          v5 = v8;
        }
      }
    }
  }
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
  return (COverlapped *)v5;
}
// 409D85: variable 'v8' is possibly undefined

//----- (00409DA0) --------------------------------------------------------
COverlapped *__thiscall CStreamOverlappedFactory::CreateAccept(
        CStreamOverlappedFactory *this,
        CListener *lpListener,
        unsigned int hSocket,
        CBuffer *lpMsgBlock)
{
  CCSLock *p_m_Lock; // ebp
  int v6; // edi
  boost::pool<boost::default_user_allocator_new_delete> *m_lpOverlappedPool; // ecx
  CAcceptOverlapped *first; // eax
  int v9; // eax

  p_m_Lock = &this->m_Lock;
  v6 = 0;
  EnterCriticalSection(&this->m_Lock.m_CSLock);
  if ( lpListener )
  {
    if ( lpMsgBlock )
    {
      m_lpOverlappedPool = this->m_lpOverlappedPool;
      if ( m_lpOverlappedPool )
      {
        first = (CAcceptOverlapped *)m_lpOverlappedPool->first;
        if ( m_lpOverlappedPool->first )
          m_lpOverlappedPool->first = first->__vftable;
        else
          first = (CAcceptOverlapped *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpOverlappedPool);
        if ( first )
        {
          CAcceptOverlapped::CAcceptOverlapped(first, this, lpListener, hSocket, lpMsgBlock);
          v6 = v9;
        }
      }
    }
  }
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
  return (COverlapped *)v6;
}
// 409DF1: variable 'v9' is possibly undefined

//----- (00409E10) --------------------------------------------------------
void __thiscall CStreamOverlappedFactory::~CStreamOverlappedFactory(CStreamOverlappedFactory *this)
{
  CCSLock *p_m_Lock; // edi
  boost::pool<boost::default_user_allocator_new_delete> *m_lpOverlappedPool; // ebx

  p_m_Lock = &this->m_Lock;
  this->__vftable = (CStreamOverlappedFactory_vtbl *)&CStreamOverlappedFactory::`vftable';
  EnterCriticalSection(&this->m_Lock.m_CSLock);
  m_lpOverlappedPool = this->m_lpOverlappedPool;
  if ( m_lpOverlappedPool )
  {
    boost::pool<boost::default_user_allocator_new_delete>::purge_memory(this->m_lpOverlappedPool);
    operator delete(m_lpOverlappedPool);
  }
  this->m_lpOverlappedPool = 0;
  LeaveCriticalSection(&p_m_Lock->m_CSLock);
  DeleteCriticalSection(&p_m_Lock->m_CSLock);
  this->__vftable = (CStreamOverlappedFactory_vtbl *)&COverlappedFactory::`vftable';
}
// 4D5558: using guessed type void *COverlappedFactory::`vftable';
// 4D558C: using guessed type void *CStreamOverlappedFactory::`vftable';

//----- (00409E60) --------------------------------------------------------
CStreamOverlappedFactory *__thiscall CStreamOverlappedFactory::`vector deleting destructor'(
        CStreamOverlappedFactory *this,
        char a2)
{
  CStreamOverlappedFactory::~CStreamOverlappedFactory(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00409E80) --------------------------------------------------------
void __thiscall CBufferFactory::~CBufferFactory(CBufferFactory *this)
{
  this->__vftable = (CBufferFactory_vtbl *)&CBufferFactory::`vftable';
}
// 4D55A0: using guessed type void *CBufferFactory::`vftable';

//----- (00409E90) --------------------------------------------------------
CBufferFactory *__thiscall CBufferFactory::`scalar deleting destructor'(CBufferFactory *this, char a2)
{
  this->__vftable = (CBufferFactory_vtbl *)&CBufferFactory::`vftable';
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D55A0: using guessed type void *CBufferFactory::`vftable';

//----- (00409EB0) --------------------------------------------------------
void __thiscall CPoolBufferFactory::Release(CPoolBufferFactory *this, CPacketDispatch *buffer)
{
  boost::pool<boost::default_user_allocator_new_delete> **Myfirst; // eax
  boost::pool<boost::default_user_allocator_new_delete> **Mylast; // edx
  _DWORD *v6; // ecx
  char *v7; // ebx
  char **m_lpBufferPool; // edi
  CCSLock *buffera; // [esp+20h] [ebp+4h]

  if ( buffer )
  {
    buffera = &this->m_BufferLock;
    EnterCriticalSection(&this->m_BufferLock.m_CSLock);
    Myfirst = this->m_PoolArray._Myfirst;
    Mylast = this->m_PoolArray._Mylast;
    if ( Myfirst == Mylast )
      goto LABEL_7;
    while ( 1 )
    {
      v6 = *Myfirst;
      if ( buffer[1].m_Session <= (CSession *)(*Myfirst)->requested_size )
        break;
      if ( ++Myfirst == Mylast )
        goto LABEL_7;
    }
    v7 = (char *)buffer->__vftable;
    *(_DWORD *)v7 = *v6;
    *v6 = v7;
    if ( Myfirst == Mylast )
LABEL_7:
      operator delete[](buffer->__vftable);
    CSymbolTable::Create(buffer);
    m_lpBufferPool = (char **)this->m_lpBufferPool;
    buffer->__vftable = (CPacketDispatch_vtbl *)*m_lpBufferPool;
    *m_lpBufferPool = (char *)buffer;
    LeaveCriticalSection(&buffera->m_CSLock);
  }
}

//----- (00409F50) --------------------------------------------------------
CBuffer *__thiscall CPoolBufferFactory::Create(CPoolBufferFactory *this, unsigned int size)
{
  boost::pool<boost::default_user_allocator_new_delete> *m_lpBufferPool; // ecx
  CBuffer *first; // eax
  CBuffer *v5; // edi
  boost::pool<boost::default_user_allocator_new_delete> **Mylast; // edx
  CBuffer *v7; // eax
  boost::pool<boost::default_user_allocator_new_delete> **Myfirst; // eax
  boost::pool<boost::default_user_allocator_new_delete> *v9; // ecx
  char *v10; // eax
  boost::pool<boost::default_user_allocator_new_delete> *v11; // esi
  CCSLock *sync; // [esp+Ch] [ebp-14h]

  sync = &this->m_BufferLock;
  EnterCriticalSection(&this->m_BufferLock.m_CSLock);
  m_lpBufferPool = this->m_lpBufferPool;
  first = (CBuffer *)m_lpBufferPool->first;
  v5 = 0;
  if ( m_lpBufferPool->first )
    m_lpBufferPool->first = first->internal_buffer_;
  else
    first = (CBuffer *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpBufferPool);
  if ( first )
  {
    CBuffer::CBuffer(first, this);
    Mylast = this->m_PoolArray._Mylast;
    v5 = v7;
    Myfirst = this->m_PoolArray._Myfirst;
    if ( Myfirst == Mylast )
      goto LABEL_13;
    while ( 1 )
    {
      v9 = *Myfirst;
      if ( size <= (*Myfirst)->requested_size )
        break;
      if ( ++Myfirst == Mylast )
        goto LABEL_13;
    }
    v10 = (char *)v9->first;
    if ( v9->first )
      v9->first = *(void **)v10;
    else
      v10 = (char *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(v9);
    if ( !v10 )
    {
LABEL_13:
      v10 = (char *)operator new[](size);
      if ( !v10 )
      {
        v11 = this->m_lpBufferPool;
        v5->internal_buffer_ = (char *)v11->first;
        v11->first = v5;
        v5 = 0;
        goto LABEL_16;
      }
    }
    CBuffer::init(v5, v10, size);
  }
LABEL_16:
  LeaveCriticalSection(&sync->m_CSLock);
  return v5;
}
// 409FB5: variable 'v7' is possibly undefined

//----- (0040A030) --------------------------------------------------------
void __thiscall CPoolBufferFactory::Destroy(CPoolBufferFactory *this)
{
  CCSLock *p_m_BufferLock; // ebx
  boost::pool<boost::default_user_allocator_new_delete> *m_lpBufferPool; // esi
  boost::pool<boost::default_user_allocator_new_delete> **Myfirst; // esi
  boost::pool<boost::default_user_allocator_new_delete> **Mylast; // edi
  boost::pool<boost::default_user_allocator_new_delete> *v6; // ebx
  char *ptr; // eax
  unsigned int sz; // ecx
  char *v9; // esi
  unsigned int v10; // edi
  std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator pos; // [esp+Ch] [ebp-Ch]
  std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator end; // [esp+10h] [ebp-8h]
  CCSLock *sync; // [esp+14h] [ebp-4h]

  p_m_BufferLock = &this->m_BufferLock;
  sync = &this->m_BufferLock;
  EnterCriticalSection(&this->m_BufferLock.m_CSLock);
  m_lpBufferPool = this->m_lpBufferPool;
  if ( m_lpBufferPool )
  {
    boost::pool<boost::default_user_allocator_new_delete>::purge_memory(this->m_lpBufferPool);
    operator delete(m_lpBufferPool);
  }
  Myfirst = this->m_PoolArray._Myfirst;
  Mylast = this->m_PoolArray._Mylast;
  this->m_lpBufferPool = 0;
  pos._Myptr = Myfirst;
  end._Myptr = Mylast;
  if ( Myfirst != Mylast )
  {
    do
    {
      v6 = *Myfirst;
      if ( *Myfirst )
      {
        ptr = v6->list.ptr;
        sz = v6->list.sz;
        if ( ptr )
        {
          do
          {
            v9 = *(char **)&ptr[sz - 8];
            v10 = *(_DWORD *)&ptr[sz - 4];
            operator delete[](ptr);
            ptr = v9;
            sz = v10;
          }
          while ( v9 );
          Mylast = end._Myptr;
          v6->list.ptr = 0;
          v6->first = 0;
          Myfirst = pos._Myptr;
        }
        operator delete(v6);
      }
      pos._Myptr = ++Myfirst;
    }
    while ( Myfirst != Mylast );
    p_m_BufferLock = sync;
  }
  if ( this->m_PoolArray._Myfirst )
    operator delete(this->m_PoolArray._Myfirst);
  this->m_PoolArray._Myfirst = 0;
  this->m_PoolArray._Mylast = 0;
  this->m_PoolArray._Myend = 0;
  LeaveCriticalSection(&p_m_BufferLock->m_CSLock);
}

//----- (0040A0F0) --------------------------------------------------------
void __thiscall CPoolBufferFactory::~CPoolBufferFactory(CPoolBufferFactory *this)
{
  this->__vftable = (CPoolBufferFactory_vtbl *)&CPoolBufferFactory::`vftable';
  CPoolBufferFactory::Destroy(this);
  if ( this->m_PoolArray._Myfirst )
    operator delete(this->m_PoolArray._Myfirst);
  this->m_PoolArray._Myfirst = 0;
  this->m_PoolArray._Mylast = 0;
  this->m_PoolArray._Myend = 0;
  DeleteCriticalSection(&this->m_BufferLock.m_CSLock);
  this->__vftable = (CPoolBufferFactory_vtbl *)&CBufferFactory::`vftable';
}
// 4D55A0: using guessed type void *CBufferFactory::`vftable';
// 4D55B0: using guessed type void *CPoolBufferFactory::`vftable';

//----- (0040A140) --------------------------------------------------------
void __thiscall __noreturn std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Xlen(
        std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> > *this)
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v3; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v3 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0040A1B0) --------------------------------------------------------
CPoolBufferFactory *__thiscall CPoolBufferFactory::`vector deleting destructor'(CPoolBufferFactory *this, char a2)
{
  CPoolBufferFactory::~CPoolBufferFactory(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0040A1D0) --------------------------------------------------------
void __thiscall std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Insert_n(
        std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> > *this,
        std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator _Where,
        unsigned int _Count,
        boost::pool<boost::default_user_allocator_new_delete> *const *_Val)
{
  boost::pool<boost::default_user_allocator_new_delete> **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  boost::pool<boost::default_user_allocator_new_delete> **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  boost::pool<boost::default_user_allocator_new_delete> **v24; // ecx
  Quest::QuestNode **v25; // edi
  boost::pool<boost::default_user_allocator_new_delete> **_Newvec; // [esp+8h] [ebp-4h]
  std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator _Wherea; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = (boost::pool<boost::default_user_allocator_new_delete> *const *)*_Val;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - (unsigned __int8 *)_Where._Myptr) >> 2;
      v23 = 4 * _Count;
      _Wherea._Myptr = (boost::pool<boost::default_user_allocator_new_delete> **)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (boost::pool<boost::default_user_allocator_new_delete> **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                                                                    &Mylast[-v23],
                                                                                    (int)Mylast,
                                                                                    Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)_Where._Myptr,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)_Where._Myptr + (unsigned int)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(
          (unsigned __int8 *)_Where._Myptr,
          (int)Mylast,
          (unsigned __int8 *)&_Where._Myptr[v23 / 4]);
        std::vector<Quest::EventNode *>::_Ufill(
          (std::vector<Quest::QuestNode *> *)this,
          (Quest::QuestNode **)this->_Mylast,
          _Count - (this->_Mylast - _Where._Myptr),
          (Quest::QuestNode *const *)&_Val);
        v24 = (boost::pool<boost::default_user_allocator_new_delete> **)((char *)_Wherea._Myptr
                                                                       + (unsigned int)this->_Mylast);
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)_Where._Myptr,
          (Quest::QuestNode **)((char *)v24 - (char *)_Wherea._Myptr),
          (Quest::QuestNode **)&_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * (_Where._Myptr - this->_Myfirst);
      _Newvec = (boost::pool<boost::default_user_allocator_new_delete> **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 (std::vector<Quest::QuestNode *> *)this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 (Quest::QuestNode *const *)&_Val);
      memmove(v16, (unsigned __int8 *)_Where._Myptr, 4 * (this->_Mylast - _Where._Myptr));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &_Newvec[_Counta];
      this->_Mylast = &_Newvec[v19];
      this->_Myfirst = _Newvec;
    }
  }
}
// 40A2B4: variable 'v15' is possibly undefined

//----- (0040A3B0) --------------------------------------------------------
char __thiscall CPoolBufferFactory::Initialize(CPoolBufferFactory *this)
{
  CCSLock *p_m_BufferLock; // esi
  boost::pool<boost::default_user_allocator_new_delete> *v3; // eax
  boost::pool<boost::default_user_allocator_new_delete> *v5; // eax
  unsigned int v6; // edx
  boost::pool<boost::default_user_allocator_new_delete> **Myfirst; // edi
  unsigned int v8; // esi
  boost::pool<boost::default_user_allocator_new_delete> **Mylast; // edx
  unsigned int dwCount; // [esp+Ch] [ebp-34h]
  boost::pool<boost::default_user_allocator_new_delete> *lpMemoryPool; // [esp+14h] [ebp-2Ch] BYREF
  unsigned int dwSizeArray[7]; // [esp+18h] [ebp-28h]
  int v13; // [esp+3Ch] [ebp-4h]

  CPoolBufferFactory::Destroy(this);
  p_m_BufferLock = &this->m_BufferLock;
  EnterCriticalSection(&this->m_BufferLock.m_CSLock);
  v13 = 0;
  v3 = (boost::pool<boost::default_user_allocator_new_delete> *)operator new((tagHeader *)0x14);
  if ( v3 )
  {
    v3->first = 0;
    v3->list.ptr = 0;
    v3->list.sz = 0;
    v3->requested_size = 48;
    v3->next_size = 32;
  }
  else
  {
    v3 = 0;
  }
  this->m_lpBufferPool = v3;
  if ( v3 )
  {
    dwSizeArray[0] = 1024;
    dwSizeArray[1] = 2048;
    dwSizeArray[2] = 4096;
    dwSizeArray[3] = 0x2000;
    dwSizeArray[4] = 0x4000;
    dwSizeArray[5] = 0x8000;
    dwSizeArray[6] = 0xFFFF;
    dwCount = 0;
    while ( 1 )
    {
      v5 = (boost::pool<boost::default_user_allocator_new_delete> *)operator new((tagHeader *)0x14);
      if ( v5 )
      {
        v6 = dwSizeArray[dwCount];
        v5->first = 0;
        v5->list.ptr = 0;
        v5->list.sz = 0;
        v5->requested_size = v6;
        v5->next_size = 32;
      }
      else
      {
        v5 = 0;
      }
      lpMemoryPool = v5;
      if ( !v5 )
        break;
      Myfirst = this->m_PoolArray._Myfirst;
      if ( Myfirst )
        v8 = this->m_PoolArray._Mylast - Myfirst;
      else
        v8 = 0;
      if ( Myfirst && v8 < this->m_PoolArray._Myend - Myfirst )
      {
        Mylast = this->m_PoolArray._Mylast;
        *Mylast = v5;
        this->m_PoolArray._Mylast = Mylast + 1;
      }
      else
      {
        std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Insert_n(
          &this->m_PoolArray,
          (std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator)this->m_PoolArray._Mylast,
          1u,
          &lpMemoryPool);
      }
      if ( ++dwCount >= 7 )
      {
        LeaveCriticalSection(&this->m_BufferLock.m_CSLock);
        return 1;
      }
      p_m_BufferLock = &this->m_BufferLock;
    }
    LeaveCriticalSection(&p_m_BufferLock->m_CSLock);
    return 0;
  }
  else
  {
    LeaveCriticalSection(&this->m_BufferLock.m_CSLock);
    return 0;
  }
}

//----- (0040A540) --------------------------------------------------------
void __thiscall CPoolBufferFactory::CPoolBufferFactory(CPoolBufferFactory *this)
{
  this->__vftable = (CPoolBufferFactory_vtbl *)&CPoolBufferFactory::`vftable';
  InitializeCriticalSection(&this->m_BufferLock.m_CSLock);
  this->m_lpBufferPool = 0;
  this->m_PoolArray._Myfirst = 0;
  this->m_PoolArray._Mylast = 0;
  this->m_PoolArray._Myend = 0;
  CPoolBufferFactory::Initialize(this);
}
// 4D55B0: using guessed type void *CPoolBufferFactory::`vftable';

//----- (0040A5A0) --------------------------------------------------------
void __thiscall CSessionPolicy::~CSessionPolicy(CSessionPolicy *this)
{
  CSocketFactory *m_lpSocketFactory; // ecx
  CBufferFactory *m_lpBufferFactory; // ecx
  CDispatchFactory *m_lpDispatchFactory; // ecx
  COverlappedFactory *m_lpOverlappedFactory; // ecx

  m_lpSocketFactory = this->m_lpSocketFactory;
  if ( m_lpSocketFactory )
    ((void (__thiscall *)(CSocketFactory *, int))m_lpSocketFactory->~CSocketFactory)(m_lpSocketFactory, 1);
  m_lpBufferFactory = this->m_lpBufferFactory;
  if ( m_lpBufferFactory )
    ((void (__thiscall *)(CBufferFactory *, int))m_lpBufferFactory->~CBufferFactory)(m_lpBufferFactory, 1);
  m_lpDispatchFactory = this->m_lpDispatchFactory;
  if ( m_lpDispatchFactory )
    ((void (__thiscall *)(CDispatchFactory *, int))m_lpDispatchFactory->~CDispatchFactory)(m_lpDispatchFactory, 1);
  m_lpOverlappedFactory = this->m_lpOverlappedFactory;
  if ( m_lpOverlappedFactory )
    ((void (__thiscall *)(COverlappedFactory *, int))m_lpOverlappedFactory->~COverlappedFactory)(
      m_lpOverlappedFactory,
      1);
}

//----- (0040A5E0) --------------------------------------------------------
LONG __thiscall CSessionPolicy::AddRef(CSessionPolicy *this)
{
  return InterlockedIncrement(&this->m_nRefCount);
}

//----- (0040A5F0) --------------------------------------------------------
LONG __thiscall CSessionPolicy::Release(CSessionPolicy *this)
{
  LONG v2; // edi
  CBufferFactory *m_lpBufferFactory; // ecx
  CDispatchFactory *m_lpDispatchFactory; // ecx
  COverlappedFactory *m_lpOverlappedFactory; // ecx

  v2 = InterlockedDecrement(&this->m_nRefCount);
  if ( !v2 && this )
  {
    if ( this->m_lpSocketFactory )
      ((void (__thiscall *)(CSocketFactory *, int))this->m_lpSocketFactory->~CSocketFactory)(this->m_lpSocketFactory, 1);
    m_lpBufferFactory = this->m_lpBufferFactory;
    if ( m_lpBufferFactory )
      ((void (__thiscall *)(CBufferFactory *, int))m_lpBufferFactory->~CBufferFactory)(m_lpBufferFactory, 1);
    m_lpDispatchFactory = this->m_lpDispatchFactory;
    if ( m_lpDispatchFactory )
      ((void (__thiscall *)(CDispatchFactory *, int))m_lpDispatchFactory->~CDispatchFactory)(m_lpDispatchFactory, 1);
    m_lpOverlappedFactory = this->m_lpOverlappedFactory;
    if ( m_lpOverlappedFactory )
      ((void (__thiscall *)(COverlappedFactory *, int))m_lpOverlappedFactory->~COverlappedFactory)(
        m_lpOverlappedFactory,
        1);
    operator delete(this);
  }
  return v2;
}

//----- (0040A650) --------------------------------------------------------
void __thiscall CTCPFactory::CTCPFactory(CTCPFactory *this)
{
  this->m_nSockFamily = 2;
  this->m_nSockType = 1;
  this->m_nSockProtocol = 6;
  this->m_nAddressLen = 16;
  this->__vftable = (CTCPFactory_vtbl *)&CTCPFactory::`vftable';
}
// 4D55BC: using guessed type void *CTCPFactory::`vftable';

//----- (0040A680) --------------------------------------------------------
void __thiscall CUDPFactory::CUDPFactory(CUDPFactory *this)
{
  this->m_nSockFamily = 2;
  this->m_nSockType = 2;
  this->m_nSockProtocol = 17;
  this->m_nAddressLen = 16;
  this->__vftable = (CUDPFactory_vtbl *)&CTCPFactory::`vftable';
}
// 4D55BC: using guessed type void *CTCPFactory::`vftable';

//----- (0040A6B0) --------------------------------------------------------
SOCKET __thiscall CSocketFactory::CreateSocket(CSocketFactory *this)
{
  return WSASocketA(this->m_nSockFamily, this->m_nSockType, this->m_nSockProtocol, 0, 0, 1u);
}

//----- (0040A6D0) --------------------------------------------------------
SOCKET __thiscall CSocketFactory::CreateConnectedSocket(CSocketFactory *this, const char *lpConnAddr, int usPort)
{
  SOCKET v5; // esi
  sockaddr sockAddr; // [esp+Ch] [ebp-14h] BYREF

  if ( !this->SetAddr(this, &sockAddr, lpConnAddr, usPort) )
    return -1;
  v5 = this->CreateSocket(this);
  if ( v5 != -1 && connect(v5, &sockAddr, this->m_nAddressLen) == -1 )
  {
    closesocket(v5);
    return -1;
  }
  return v5;
}

//----- (0040A750) --------------------------------------------------------
SOCKET __thiscall CSocketFactory::CreateBindedSocket(CSocketFactory *this, const char *lpBindAddr, int usPort)
{
  SOCKET v5; // esi
  sockaddr sockAddr; // [esp+Ch] [ebp-14h] BYREF

  if ( !this->SetAddr(this, &sockAddr, lpBindAddr, usPort) )
    return -1;
  v5 = this->CreateSocket(this);
  if ( v5 != -1 )
  {
    usPort = 1;
    if ( setsockopt(v5, 0xFFFF, 4, (const char *)&usPort, 4) == -1 || bind(v5, &sockAddr, this->m_nAddressLen) == -1 )
    {
      closesocket(v5);
      return -1;
    }
  }
  return v5;
}

//----- (0040A7F0) --------------------------------------------------------
SOCKET __thiscall CSocketFactory::CreateListenSocket(
        CSocketFactory *this,
        const char *lpListenAddr,
        int usPort,
        int nBackLog)
{
  int v4; // edi
  SOCKET BindedSocket; // eax
  SOCKET v6; // esi

  v4 = nBackLog;
  if ( nBackLog >= 63 )
    v4 = 63;
  BindedSocket = CSocketFactory::CreateBindedSocket(this, lpListenAddr, usPort);
  v6 = BindedSocket;
  if ( BindedSocket == -1 || listen(BindedSocket, v4) != -1 )
    return v6;
  closesocket(v6);
  return -1;
}

//----- (0040A840) --------------------------------------------------------
char __thiscall CINETFamilyFactory::SetAddr(
        CINETFamilyFactory *this,
        sockaddr *lpSA,
        const char *szAddr,
        u_short usPort)
{
  *(_DWORD *)&lpSA->sa_family = 0;
  *(_DWORD *)&lpSA->sa_data[2] = 0;
  *(_DWORD *)&lpSA->sa_data[6] = 0;
  *(_DWORD *)&lpSA->sa_data[10] = 0;
  if ( szAddr )
  {
    lpSA->sa_family = this->m_nSockFamily;
    *(_DWORD *)&lpSA->sa_data[2] = inet_addr(szAddr);
    *(_WORD *)lpSA->sa_data = htons(usPort);
    if ( !*(_DWORD *)&lpSA->sa_data[2] )
      return 0;
  }
  else
  {
    *(_DWORD *)&lpSA->sa_data[2] = htonl(0);
  }
  lpSA->sa_family = this->m_nSockFamily;
  *(_WORD *)lpSA->sa_data = htons(usPort);
  return 1;
}

//----- (0040A8C0) --------------------------------------------------------
bool __thiscall CINETFamilyFactory::GetNetworkInfo(CINETFamilyFactory *this, char *Address_Out, int nMaxBufferSize)
{
  struct hostent *v3; // eax
  char **h_addr_list; // eax
  int v5; // esi
  struct in_addr v6; // ebx
  char *v7; // eax
  hostent *pHost; // [esp+10h] [ebp-108h]
  char szHostName[256]; // [esp+14h] [ebp-104h] BYREF

  if ( gethostname(szHostName, 255) )
    return 0;
  szHostName[255] = 0;
  v3 = gethostbyname(szHostName);
  pHost = v3;
  if ( !v3 )
    return 0;
  h_addr_list = v3->h_addr_list;
  v5 = 0;
  if ( !*h_addr_list )
    return 0;
  do
  {
    v6 = *(struct in_addr *)h_addr_list[v5];
    v7 = inet_ntoa(v6);
    strncpy(Address_Out, v7, nMaxBufferSize - 1);
    Address_Out[nMaxBufferSize - 1] = 0;
    if ( v6.S_un.S_un_b.s_b1 != 10 )
    {
      if ( v6.S_un.S_un_b.s_b1 == 0xAC )
      {
        if ( v6.S_un.S_un_b.s_b2 < 0x10u || v6.S_un.S_un_b.s_b2 > 0x1Fu )
          return 1;
      }
      else if ( v6.S_un.S_un_w.s_w1 != 0xA8C0 )
      {
        return 1;
      }
    }
    h_addr_list = pHost->h_addr_list;
  }
  while ( h_addr_list[++v5] );
  return v5 != 0;
}

//----- (0040A9A0) --------------------------------------------------------
BOOL __thiscall CCompletionHandler::AttachToHander(CCompletionHandler *this, void *hAttach, ULONG_PTR pCompletionKey)
{
  return CreateIoCompletionPort(hAttach, this->m_hIOCP, pCompletionKey, this->m_nThread) != 0;
}

//----- (0040A9D0) --------------------------------------------------------
void __thiscall INET_Addr::INET_Addr(INET_Addr *this)
{
  this->m_iAddrLen = 0;
  *(_DWORD *)&this->m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[10] = 0;
}

//----- (0040A9F0) --------------------------------------------------------
void __thiscall INET_Addr::INET_Addr(INET_Addr *this, const char *addr, u_short port)
{
  INET_Addr::set_addr(this, addr, port);
}

//----- (0040AA10) --------------------------------------------------------
char __thiscall CListener::Initialize(
        CListener *this,
        const char *lpListenAddress,
        u_short usPort,
        unsigned int dwMaxPending)
{
  INET_Addr addrListen; // [esp+4h] [ebp-14h] BYREF

  INET_Addr::set_addr(&addrListen, lpListenAddress, usPort);
  return CListener::Initialize(this, &addrListen, dwMaxPending);
}

//----- (0040AA50) --------------------------------------------------------
unsigned int __cdecl GetOptimalThreadNum()
{
  _SYSTEM_INFO systemInfo; // [esp+0h] [ebp-24h] BYREF

  GetSystemInfo(&systemInfo);
  return 2 * systemInfo.dwNumberOfProcessors;
}

//----- (0040AA70) --------------------------------------------------------
void __thiscall CSessionMgr::Process(CSessionMgr *this)
{
  CCSLock *p_m_ProcessLock; // edi

  p_m_ProcessLock = &this->m_ProcessLock;
  EnterCriticalSection(&this->m_ProcessLock.m_CSLock);
  CSessionMgr::InternalProcess(this);
  LeaveCriticalSection(&p_m_ProcessLock->m_CSLock);
}

//----- (0040AAC0) --------------------------------------------------------
unsigned int __thiscall CSessionMgr::GetSessionNum(CSessionMgr *this)
{
  CCSLock *p_m_ProcessLock; // edi
  unsigned int Mysize; // esi

  p_m_ProcessLock = &this->m_ProcessLock;
  EnterCriticalSection(&this->m_ProcessLock.m_CSLock);
  Mysize = this->m_current._Mysize;
  LeaveCriticalSection(&p_m_ProcessLock->m_CSLock);
  return Mysize;
}

//----- (0040AAE0) --------------------------------------------------------
char __thiscall CIOCPNet::Initialize(CIOCPNet *this)
{
  unsigned int m_dwFlags; // eax
  CCompletionHandler *m_lpSocketHandler; // ecx
  unsigned int OptimalThreadNum; // edi
  CIOWorker *v6; // eax
  CThread *v7; // eax

  EnterCriticalSection(&this->m_IOCPLock.m_CSLock);
  m_dwFlags = this->m_dwFlags;
  if ( (m_dwFlags & 1) != 0 )
  {
    LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
    return 0;
  }
  this->m_dwFlags = m_dwFlags | 1;
  LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
  m_lpSocketHandler = this->m_lpSocketHandler;
  if ( !m_lpSocketHandler
    || !this->m_lpThreadMgr
    || !this->m_lpSessionMgr
    || !CCompletionHandler::Initialize(m_lpSocketHandler, 0, 0xFFFFFFFF) )
  {
    return 0;
  }
  OptimalThreadNum = GetOptimalThreadNum();
  if ( !OptimalThreadNum )
    return 1;
  while ( 1 )
  {
    --OptimalThreadNum;
    v6 = (CIOWorker *)operator new((tagHeader *)0xC);
    if ( v6 )
      CIOWorker::CIOWorker(v6, this->m_lpSocketHandler);
    else
      v7 = 0;
    if ( !CThreadMgr::RegisterAndRun(this->m_lpThreadMgr, v7) )
      break;
    if ( !OptimalThreadNum )
      return 1;
  }
  return 0;
}
// 40AB9B: variable 'v7' is possibly undefined

//----- (0040ABD0) --------------------------------------------------------
void __thiscall CIOCPNet::Process(CIOCPNet *this)
{
  CSessionMgr *m_lpSessionMgr; // ecx

  m_lpSessionMgr = this->m_lpSessionMgr;
  if ( m_lpSessionMgr )
    CSessionMgr::Process(m_lpSessionMgr);
}

//----- (0040ABE0) --------------------------------------------------------
unsigned int __thiscall CIOCPNet::GetSessionNum(CIOCPNet *this)
{
  CSessionMgr *m_lpSessionMgr; // ecx

  m_lpSessionMgr = this->m_lpSessionMgr;
  if ( m_lpSessionMgr )
    return CSessionMgr::GetSessionNum(m_lpSessionMgr);
  else
    return 0;
}

//----- (0040ABF0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    operator delete(v2);
  }
}

//----- (0040AC30) --------------------------------------------------------
int __thiscall CIOCPNet::GetAcceptPendingNum(CIOCPNet *this)
{
  int v2; // ebx
  std::_List_nod<CListener *>::_Node *Myhead; // eax
  std::_List_nod<CListener *>::_Node *Next; // esi
  unsigned int PendingAcceptNum; // eax

  v2 = 0;
  EnterCriticalSection(&this->m_IOCPLock.m_CSLock);
  Myhead = this->m_ListenerList._Myhead;
  Next = Myhead->_Next;
  if ( Myhead->_Next != Myhead )
  {
    do
    {
      PendingAcceptNum = CListener::GetPendingAcceptNum(Next->_Myval);
      Next = Next->_Next;
      v2 += PendingAcceptNum;
    }
    while ( Next != this->m_ListenerList._Myhead );
  }
  LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
  return v2;
}

//----- (0040ACA0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Rootnode,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode)
{
  char Isnil; // al
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v5; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Left; // [esp-8h] [ebp-2Ch]
  int v8; // [esp+0h] [ebp-24h] BYREF
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *v9; // [esp+Ch] [ebp-18h]
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Newroot; // [esp+10h] [ebp-14h]
  int *v11; // [esp+14h] [ebp-10h]
  int v12; // [esp+20h] [ebp-4h]

  _Newroot = this->_Myhead;
  Isnil = _Rootnode->_Isnil;
  v11 = &v8;
  v9 = this;
  if ( !Isnil )
  {
    v5 = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (const std::pair<unsigned long const ,unsigned long> *)&_Rootnode->_Myval, _Rootnode->_Color);
    if ( _Newroot->_Isnil )
      _Newroot = v5;
    Left = _Rootnode->_Left;
    v12 = 0;
    v5->_Left = std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(
                  this,
                  Left,
                  v5);
    v5->_Right = std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(
                   this,
                   _Rootnode->_Right,
                   v5);
  }
  return _Newroot;
}

//----- (0040AD50) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        const std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *_Right)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v4; // edx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *i; // ecx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v8; // ecx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *j; // eax

  Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead;
  Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(this, _Right->_Myhead->_Parent, Myhead);
  this->_Mysize = _Right->_Mysize;
  v4 = this->_Myhead;
  Parent = v4->_Parent;
  if ( Parent->_Isnil )
  {
    v4->_Left = v4;
    this->_Myhead->_Right = this->_Myhead;
  }
  else
  {
    for ( i = Parent->_Left; !i->_Isnil; i = i->_Left )
      Parent = i;
    v4->_Left = Parent;
    v7 = this->_Myhead;
    v8 = v7->_Parent;
    for ( j = v8->_Right; !j->_Isnil; j = j->_Right )
      v8 = j;
    v7->_Right = v8;
  }
}

//----- (0040ADE0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  bool v16; // zf
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *v18; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v22; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v22 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
                (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  operator delete(_Erasednode);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v18 = result;
  result->_Ptr = _Where._Ptr;
  return v18;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (0040B0A0) --------------------------------------------------------
void __thiscall std::list<CListener *>::_Incsize(std::list<CListener *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0040B140) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Last)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v8; // eax
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *v9; // eax
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator v10; // ecx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *i; // eax

  Ptr = _Last._Ptr;
  v5 = _First._Ptr;
  Myhead = this->_Myhead;
  if ( _First._Ptr == Myhead->_Left && _Last._Ptr == Myhead )
  {
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Erase(
      this,
      Myhead->_Parent);
    this->_Myhead->_Parent = this->_Myhead;
    v8 = this->_Myhead;
    this->_Mysize = 0;
    v8->_Left = v8;
    this->_Myhead->_Right = this->_Myhead;
    v9 = result;
    result->_Ptr = this->_Myhead->_Left;
  }
  else
  {
    if ( _First._Ptr != _Last._Ptr )
    {
      do
      {
        v10._Ptr = v5;
        if ( !v5->_Isnil )
        {
          Right = v5->_Right;
          if ( Right->_Isnil )
          {
            for ( i = v5->_Parent; !i->_Isnil; i = i->_Parent )
            {
              if ( v5 != i->_Right )
                break;
              v5 = i;
            }
            v5 = i;
          }
          else
          {
            v5 = v5->_Right;
            for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
          this,
          &_First,
          v10);
      }
      while ( v5 != Ptr );
    }
    v9 = result;
    result->_Ptr = v5;
  }
  return v9;
}

//----- (0040B200) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result,
        bool _Addleft,
        std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Wherenode,
        const std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v8; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **p_Parent; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v11; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Parent; // ebp
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // edx
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *v15; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::pair<CSessionPolicy * const,unsigned int> *_Vala; // [esp+68h] [ebp+10h]

  if ( this->_Mysize >= 0x1FFFFFFE )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCharacter *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)_Wherenode, (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)this->_Myhead, _Val, 0);
  Myhead = this->_Myhead;
  _Vala = (const std::pair<CSessionPolicy * const,unsigned int> *)v6;
  ++this->_Mysize;
  if ( _Wherenode == Myhead )
  {
    Myhead->_Parent = v6;
    this->_Myhead->_Left = v6;
    this->_Myhead->_Right = v6;
  }
  else if ( _Addleft )
  {
    _Wherenode->_Left = v6;
    v8 = this->_Myhead;
    if ( _Wherenode == v8->_Left )
      v8->_Left = v6;
  }
  else
  {
    _Wherenode->_Right = v6;
    v9 = this->_Myhead;
    if ( _Wherenode == v9->_Right )
      v9->_Right = v6;
  }
  p_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v6->_Parent;
  v11 = (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *)v6;
  if ( !v6->_Parent->_Color )
  {
    while ( 1 )
    {
      v12 = *p_Parent;
      Parent = (*p_Parent)->_Parent;
      Left = Parent->_Left;
      if ( *p_Parent == Parent->_Left )
      {
        Left = Parent->_Right;
        if ( Left->_Color )
        {
          if ( v11 == v12->_Right )
          {
            v11 = *p_Parent;
            std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
              (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
              *p_Parent);
          }
          v11->_Parent->_Color = 1;
          v11->_Parent->_Parent->_Color = 0;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            v11->_Parent->_Parent);
          goto LABEL_21;
        }
      }
      else if ( Left->_Color )
      {
        if ( v11 == v12->_Left )
        {
          v11 = *p_Parent;
          std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(
            (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
            *p_Parent);
        }
        v11->_Parent->_Color = 1;
        v11->_Parent->_Parent->_Color = 0;
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(
          (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this,
          v11->_Parent->_Parent);
        goto LABEL_21;
      }
      (*p_Parent)->_Color = 1;
      Left->_Color = 1;
      (*p_Parent)->_Parent->_Color = 0;
      v11 = (*p_Parent)->_Parent;
LABEL_21:
      p_Parent = &v11->_Parent;
      if ( v11->_Parent->_Color )
      {
        v6 = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)_Vala;
        break;
      }
    }
  }
  v15 = result;
  this->_Myhead->_Parent->_Color = 1;
  result->_Ptr = v6;
  return v15;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0040B3B0) --------------------------------------------------------
void __thiscall std::map<CSessionPolicy *,unsigned int>::~map<CSessionPolicy *,unsigned int>(
        std::map<CSessionPolicy *,unsigned int> *this)
{
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
    this,
    &result,
    (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator)this->_Myhead->_Left,
    (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator)this->_Myhead);
  operator delete(this->_Myhead);
  this->_Myhead = 0;
  this->_Mysize = 0;
}

//----- (0040B3E0) --------------------------------------------------------
std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> *result,
        std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Val)
{
  const std::pair<unsigned long const ,unsigned long> *v3; // ebp
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Parent; // eax
  bool v7; // cl
  CSessionPolicy *Left; // edx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v9; // edx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Ptr; // edx
  std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> *v11; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v12; // ecx
  bool _Addleft; // [esp+Ch] [ebp-4h]

  v3 = (const std::pair<unsigned long const ,unsigned long> *)_Val;
  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  v7 = 1;
  _Addleft = 1;
  if ( !Parent->_Isnil )
  {
    Left = (CSessionPolicy *)_Val->_Left;
    do
    {
      v7 = Left < Parent->_Myval.first;
      Myhead = Parent;
      _Addleft = v7;
      if ( Left >= Parent->_Myval.first )
        Parent = Parent->_Right;
      else
        Parent = Parent->_Left;
    }
    while ( !Parent->_Isnil );
  }
  v9 = Myhead;
  _Val = Myhead;
  if ( v7 )
  {
    if ( Myhead == this->_Myhead->_Left )
    {
      Ptr = std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
              this,
              (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *)&_Val,
              1,
              Myhead,
              v3)->_Ptr;
      v11 = result;
      result->second = 1;
      result->first._Ptr = Ptr;
      return v11;
    }
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    v9 = _Val;
  }
  if ( v9->_Myval.first >= (CSessionPolicy *const)v3->first )
  {
    v11 = result;
    result->second = 0;
    result->first._Ptr = v9;
  }
  else
  {
    v12 = std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
            this,
            (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *)&_Val,
            _Addleft,
            Myhead,
            v3)->_Ptr;
    v11 = result;
    result->first._Ptr = v12;
    result->second = 1;
  }
  return v11;
}


//----- (0040B4A0) --------------------------------------------------------
void __thiscall std::list<CListener *>::push_back(std::list<CListener *> *this, CThread **_Val)
{
  std::_List_nod<CThread *>::_Node *Myhead; // edi
  std::_List_nod<CThread *>::_Node *v4; // ebx

  Myhead = (std::_List_nod<CThread *>::_Node *)this->_Myhead;
  v4 = std::list<IOPCode *>::_Buynode((std::list<CThread *> *)this, Myhead, Myhead->_Prev, _Val);
  std::list<CListener *>::_Incsize(this, 1u);
  Myhead->_Prev = v4;
  v4->_Prev->_Next = v4;
}

//----- (0040B4E0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        const std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *_Right)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v3; // eax
  int v4; // [esp+0h] [ebp-20h] BYREF
  std::map<CSessionPolicy *,unsigned int> *v5; // [esp+Ch] [ebp-14h]
  int *v6; // [esp+10h] [ebp-10h]
  int v7; // [esp+1Ch] [ebp-4h]

  v6 = &v4;
  v5 = (std::map<CSessionPolicy *,unsigned int> *)this;
  v3 = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)this);
  this->_Myhead = v3;
  v3->_Isnil = 1;
  this->_Myhead->_Parent = this->_Myhead;
  this->_Myhead->_Left = this->_Myhead;
  this->_Myhead->_Right = this->_Myhead;
  this->_Mysize = 0;
  v7 = 0;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(
    this,
    _Right);
}

//----- (0040B570) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Where,
        std::pair<unsigned long const ,unsigned long> *_Val)
{
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *v5; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // eax
  std::pair<unsigned long const ,unsigned long> *v7; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Right; // eax
  CSessionPolicy *first; // ebp
  bool v10; // cf
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Ptr; // ecx
  std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> v12; // [esp+8h] [ebp-8h] BYREF

  if ( !this->_Mysize )
  {
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
      this,
      result,
      1,
      this->_Myhead,
      _Val);
    return result;
  }
  Myhead = this->_Myhead;
  v7 = _Val;
  if ( _Where._Ptr == Myhead->_Left )
  {
    if ( (CSessionPolicy *const)_Val->first < _Where._Ptr->_Myval.first )
    {
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
        this,
        result,
        1,
        _Where._Ptr,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  if ( _Where._Ptr == Myhead )
  {
    Right = Myhead->_Right;
    if ( Right->_Myval.first < (CSessionPolicy *const)_Val->first )
    {
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
        this,
        result,
        0,
        Right,
        _Val);
      return result;
    }
    goto LABEL_23;
  }
  first = (CSessionPolicy *)_Val->first;
  v10 = _Where._Ptr->_Myval.first < (CSessionPolicy *const)_Val->first;
  if ( _Where._Ptr->_Myval.first > (CSessionPolicy *const)_Val->first )
  {
    _Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr;
    std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec((std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *)&_Val);
    if ( _Val[1].second < (unsigned int)first )
    {
      if ( *(_BYTE *)(_Val[1].first + 21) )
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
          this,
          result,
          0,
          (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)_Val,
          v7);
      else
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
          this,
          result,
          1,
          _Where._Ptr,
          v7);
      return result;
    }
    v10 = _Where._Ptr->_Myval.first < first;
  }
  if ( !v10
    || (_Val = (std::pair<unsigned long const ,unsigned long> *)_Where._Ptr,
        std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_Val),
        _Val != (std::pair<unsigned long const ,unsigned long> *)this->_Myhead)
    && (unsigned int)first >= _Val[1].second )
  {
LABEL_23:
    Ptr = std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(
            this,
            &v12,
            (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)v7)->first._Ptr;
    v5 = result;
    result->_Ptr = Ptr;
    return v5;
  }
  if ( _Where._Ptr->_Right->_Isnil )
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
      this,
      result,
      0,
      _Where._Ptr,
      v7);
  else
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(
      this,
      result,
      1,
      (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)_Val,
      v7);
  return result;
}

//----- (0040B6E0) --------------------------------------------------------
unsigned int *__thiscall std::map<CSessionPolicy *,unsigned int>::operator[](
        std::map<CSessionPolicy *,unsigned int> *this,
        CSessionPolicy *const *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Parent; // eax
  std::pair<CSessionPolicy * const,unsigned int> _Val; // [esp+Ch] [ebp-8h] BYREF

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead != this->_Myhead && *_Keyval >= Myhead->_Myval.first )
    return &Myhead->_Myval.second;
  _Val.first = *_Keyval;
  _Val.second = 0;
  return &std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(
            this,
            (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *)&_Keyval,
            (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator)Myhead,
            (std::pair<unsigned long const ,unsigned long> *)&_Val)->_Ptr->_Myval.second;
}

//----- (0040B760) --------------------------------------------------------
void __thiscall std::list<CListener *>::_Splice(
        std::list<CListener *> *this,
        std::list<CListener *>::iterator _Where,
        std::list<CListener *> *_Right,
        std::list<CListener *>::iterator _First,
        std::list<CListener *>::iterator _Last,
        unsigned int _Count)
{
  std::_List_nod<CListener *>::_Node *Prev; // esi

  if ( this != _Right )
  {
    std::list<CListener *>::_Incsize(this, _Count);
    _Right->_Mysize -= _Count;
  }
  _First._Ptr->_Prev->_Next = _Last._Ptr;
  _Last._Ptr->_Prev->_Next = _Where._Ptr;
  _Where._Ptr->_Prev->_Next = _First._Ptr;
  Prev = _Where._Ptr->_Prev;
  _Where._Ptr->_Prev = _Last._Ptr->_Prev;
  _Last._Ptr->_Prev = _First._Ptr->_Prev;
  _First._Ptr->_Prev = Prev;
}

//----- (0040B7B0) --------------------------------------------------------
void __thiscall CIOCPNet::CIOCPNet(CIOCPNet *this)
{
  CCompletionHandler *v2; // eax
  CCompletionHandler *v3; // eax
  CThreadMgr *v4; // eax
  CThreadMgr *v5; // eax
  CSessionMgr *v6; // eax
  CSessionMgr *v7; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v8; // eax
  WSAData wsaData; // [esp+14h] [ebp-1A0h] BYREF
  int v10; // [esp+1B0h] [ebp-4h]

  InitializeCriticalSection(&this->m_IOCPLock.m_CSLock);
  v10 = 1;
  v2 = (CCompletionHandler *)operator new((tagHeader *)0x10);
  if ( v2 )
    CCompletionHandler::CCompletionHandler(v2);
  else
    v3 = 0;
  this->m_lpSocketHandler = v3;
  v4 = (CThreadMgr *)operator new((tagHeader *)0x21C);
  LOBYTE(v10) = 2;
  if ( v4 )
    CThreadMgr::CThreadMgr(v4);
  else
    v5 = 0;
  this->m_lpThreadMgr = v5;
  v6 = (CSessionMgr *)operator new((tagHeader *)0x8C);
  LOBYTE(v10) = 3;
  if ( v6 )
    CSessionMgr::CSessionMgr(v6);
  else
    v7 = 0;
  this->m_lpSessionMgr = v7;
  this->m_ListenerList._Myhead = (std::_List_nod<CListener *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_ListenerList);
  this->m_ListenerList._Mysize = 0;
  LOBYTE(v10) = 4;
  v8 = (std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)&this->m_SessionPolicyMap);
  this->m_SessionPolicyMap._Myhead = v8;
  v8->_Isnil = 1;
  this->m_SessionPolicyMap._Myhead->_Parent = this->m_SessionPolicyMap._Myhead;
  this->m_SessionPolicyMap._Myhead->_Left = this->m_SessionPolicyMap._Myhead;
  this->m_SessionPolicyMap._Myhead->_Right = this->m_SessionPolicyMap._Myhead;
  this->m_SessionPolicyMap._Mysize = 0;
  this->m_dwFlags = 0;
  WSAStartup(0x202u, &wsaData);
}
// 40B824: variable 'v3' is possibly undefined
// 40B854: variable 'v5' is possibly undefined
// 40B886: variable 'v7' is possibly undefined

//----- (0040B900) --------------------------------------------------------
char __thiscall CIOCPNet::AddListener(
        CIOCPNet *this,
        CSessionPolicy *lpSessionPolicy,
        const char *lpListenAddress,
        u_short usPort,
        _RTL_CRITICAL_SECTION *dwMaxListenPeding,
        CValidateConnection *lpValidateConnection)
{
  CSessionPolicy *v6; // edi
  CListener *v8; // eax
  CValidateConnection *v9; // eax
  CValidateConnection *v10; // ebx
  unsigned int *v12; // eax
  _RTL_CRITICAL_SECTION *v13; // [esp-8h] [ebp-20h]

  v6 = lpSessionPolicy;
  if ( !lpSessionPolicy
    || !lpSessionPolicy->m_lpBufferFactory
    || !lpSessionPolicy->m_lpSocketFactory
    || !lpSessionPolicy->m_lpDispatchFactory
    || !lpSessionPolicy->m_lpOverlappedFactory
    || !this->m_lpSocketHandler
    || !this->m_lpSessionMgr )
  {
    return 0;
  }
  v8 = (CListener *)operator new((tagHeader *)0x5C);
  if ( v8 )
  {
    CListener::CListener(v8, this->m_lpSocketHandler, v6, this->m_lpSessionMgr, lpValidateConnection);
    v10 = v9;
  }
  else
  {
    v10 = 0;
  }
  lpValidateConnection = v10;
  if ( !v10 )
    return 0;
  if ( !CListener::Initialize((CListener *)v10, lpListenAddress, usPort, (unsigned int)dwMaxListenPeding) )
  {
    ((void (__thiscall *)(CValidateConnection *, int))v10->operator())(v10, 1);
    return 0;
  }
  CSessionPolicy::AddRef(v6);
  CLock<CCSLock>::Syncronize::Syncronize((CLock<CCSLock>::Syncronize *)&dwMaxListenPeding, &this->m_IOCPLock);
  std::list<CListener *>::push_back(&this->m_ListenerList, (CThread **)&lpValidateConnection);
  v12 = std::map<CSessionPolicy *,unsigned int>::operator[](&this->m_SessionPolicyMap, &lpSessionPolicy);
  v13 = dwMaxListenPeding;
  ++*v12;
  LeaveCriticalSection(v13);
  return 1;
}
// 40B99B: variable 'v9' is possibly undefined

//----- (0040BA60) --------------------------------------------------------
char __thiscall CIOCPNet::Connect(
        CIOCPNet *this,
        CSessionPolicy *lpSessionPolicy,
        char *lpConnectAddress,
        _RTL_CRITICAL_SECTION *usPort)
{
  CSessionPolicy *v4; // ebp
  const char *v6; // ebx
  unsigned __int16 v7; // si
  int Error; // eax
  CSession *Session; // eax
  CSession *v11; // esi
  INET_Addr *v12; // eax
  unsigned int *v13; // eax
  INET_Addr localAddr; // [esp+8h] [ebp-34h] BYREF
  INET_Addr v15; // [esp+1Ch] [ebp-20h] BYREF
  int v16; // [esp+38h] [ebp-4h]

  v4 = lpSessionPolicy;
  if ( !lpSessionPolicy
    || !lpSessionPolicy->m_lpBufferFactory
    || !lpSessionPolicy->m_lpSocketFactory
    || !lpSessionPolicy->m_lpDispatchFactory
    || !lpSessionPolicy->m_lpOverlappedFactory
    || !this->m_lpSocketHandler
    || !this->m_lpSessionMgr )
  {
    return 0;
  }
  v6 = lpConnectAddress;
  v7 = (unsigned __int16)usPort;
  lpConnectAddress = (char *)CSocketFactory::CreateConnectedSocket(
                               lpSessionPolicy->m_lpSocketFactory,
                               lpConnectAddress,
                               (int)usPort);
  if ( lpConnectAddress == (char *)-1 )
  {
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_Log,
      LOG_ERROR,
      "CIOCPNet::Connect",
      aDWorkRylSource_92,
      149,
      "Connected socket creation error : %d, destination : %s:%d",
      Error,
      v6,
      v7);
    return 0;
  }
  Session = CSessionMgr::CreateSession(this->m_lpSessionMgr, v4);
  v11 = Session;
  if ( Session )
  {
    if ( CCompletionHandler::AttachToHander(this->m_lpSocketHandler, lpConnectAddress, (ULONG_PTR)Session) )
    {
      *(_DWORD *)&localAddr.m_SockAddr.sa_family = 0;
      v11->m_hSocket = (unsigned int)lpConnectAddress;
      memset(&localAddr.m_SockAddr.sa_data[2], 0, 16);
      INET_Addr::INET_Addr(&v15, v6, (u_short)usPort);
      CSession::SetAddress(v11, v12, &localAddr);
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_DETAIL,
        "CIOCPNet::Connect",
        aDWorkRylSource_92,
        165,
        "SP:0x%p/DP:0x%p/IP:%15s/ Connect Success.",
        v11,
        v11->m_lpDispatch,
        v6);
      CSession::InternalRecv(v11);
      CSessionMgr::Add(this->m_lpSessionMgr, v11);
      CSessionPolicy::AddRef(v4);
      CLock<CCSLock>::Syncronize::Syncronize((CLock<CCSLock>::Syncronize *)&usPort, &this->m_IOCPLock);
      v16 = 0;
      v13 = std::map<CSessionPolicy *,unsigned int>::operator[](&this->m_SessionPolicyMap, &lpSessionPolicy);
      ++*v13;
      LeaveCriticalSection(usPort);
      return 1;
    }
    CSessionMgr::DeleteSession(this->m_lpSessionMgr, v11);
  }
  closesocket((SOCKET)lpConnectAddress);
  return 0;
}
// 40BB8B: variable 'v12' is possibly undefined

//----- (0040BC60) --------------------------------------------------------
void __thiscall CIOCPNet::DestroyListener(CIOCPNet *this)
{
  std::_List_nod<CListener *>::_Node *Myhead; // esi
  unsigned int Mysize; // edx
  std::_List_nod<CListener *>::_Node *i; // edi
  CListener *Myval; // ecx
  std::_List_nod<CListener *>::_Node *Next; // eax
  bool v7; // zf
  std::_List_nod<CListener *>::_Node *v8; // edi
  std::list<CListener *> deleteList; // [esp+Ch] [ebp-18h] BYREF
  int v10; // [esp+20h] [ebp-4h]

  Myhead = (std::_List_nod<CListener *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&deleteList);
  deleteList._Myhead = Myhead;
  deleteList._Mysize = 0;
  v10 = 0;
  EnterCriticalSection(&this->m_IOCPLock.m_CSLock);
  LOBYTE(v10) = 1;
  if ( &deleteList != &this->m_ListenerList )
  {
    Mysize = this->m_ListenerList._Mysize;
    if ( Mysize )
    {
      std::list<CListener *>::_Splice(
        &deleteList,
        (std::list<CListener *>::iterator)Myhead,
        &this->m_ListenerList,
        (std::list<CListener *>::iterator)this->m_ListenerList._Myhead->_Next,
        (std::list<CListener *>::iterator)this->m_ListenerList._Myhead,
        Mysize);
      Myhead = deleteList._Myhead;
    }
  }
  LOBYTE(v10) = 0;
  LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
  {
    Myval = i->_Myval;
    if ( Myval )
      ((void (__thiscall *)(CListener *, int))Myval->~CListener)(Myval, 1);
  }
  Next = Myhead->_Next;
  v7 = Myhead->_Next == Myhead;
  Myhead->_Next = Myhead;
  Myhead->_Prev = Myhead;
  if ( !v7 )
  {
    do
    {
      v8 = Next->_Next;
      operator delete(Next);
      Next = v8;
    }
    while ( v8 != Myhead );
  }
  operator delete(Myhead);
}

//----- (0040BD40) --------------------------------------------------------
char __thiscall CIOCPNet::Destroy(CIOCPNet *this)
{
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // eax
  CThreadMgr *m_lpThreadMgr; // ebp
  CCompletionHandler *m_lpSocketHandler; // ebx
  CSessionMgr *m_lpSessionMgr; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Left; // esi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *v8; // ebx
  unsigned int second; // edi
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *i; // eax
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator v14; // [esp-10h] [ebp-34h]
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Parent; // [esp-Ch] [ebp-30h]
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator result; // [esp+8h] [ebp-1Ch] BYREF
  std::map<CSessionPolicy *,unsigned int> tempMap; // [esp+Ch] [ebp-18h] BYREF
  int v18; // [esp+20h] [ebp-4h]

  EnterCriticalSection(&this->m_IOCPLock.m_CSLock);
  if ( (this->m_dwFlags & 2) != 0 )
  {
    LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
    return 0;
  }
  else
  {
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>(
      &tempMap,
      &this->m_SessionPolicyMap);
    Parent = this->m_SessionPolicyMap._Myhead->_Parent;
    v18 = 0;
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Erase(
      &this->m_SessionPolicyMap,
      Parent);
    this->m_SessionPolicyMap._Myhead->_Parent = this->m_SessionPolicyMap._Myhead;
    Myhead = this->m_SessionPolicyMap._Myhead;
    this->m_SessionPolicyMap._Mysize = 0;
    Myhead->_Left = Myhead;
    this->m_SessionPolicyMap._Myhead->_Right = this->m_SessionPolicyMap._Myhead;
    m_lpThreadMgr = this->m_lpThreadMgr;
    this->m_lpThreadMgr = 0;
    m_lpSocketHandler = this->m_lpSocketHandler;
    this->m_lpSocketHandler = 0;
    m_lpSessionMgr = this->m_lpSessionMgr;
    this->m_lpSessionMgr = 0;
    this->m_dwFlags |= 2u;
    LeaveCriticalSection(&this->m_IOCPLock.m_CSLock);
    CIOCPNet::DestroyListener(this);
    if ( m_lpSessionMgr )
    {
      CSessionMgr::~CSessionMgr(m_lpSessionMgr);
      operator delete(m_lpSessionMgr);
    }
    if ( m_lpThreadMgr )
      ((void (__thiscall *)(CThreadMgr *, int))m_lpThreadMgr->~CThreadMgr)(m_lpThreadMgr, 1);
    if ( m_lpSocketHandler )
      ((void (__thiscall *)(CCompletionHandler *, int))m_lpSocketHandler->~CCompletionHandler)(m_lpSocketHandler, 1);
    v6 = tempMap._Myhead;
    Left = tempMap._Myhead->_Left;
    v8 = tempMap._Myhead;
    while ( Left != v8 )
    {
      if ( Left->_Myval.second )
      {
        second = Left->_Myval.second;
        do
        {
          CSessionPolicy::Release(Left->_Myval.first);
          --second;
        }
        while ( second );
        v6 = tempMap._Myhead;
      }
      if ( !Left->_Isnil )
      {
        Right = Left->_Right;
        if ( Right->_Isnil )
        {
          for ( i = Left->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( Left != i->_Right )
              break;
            Left = i;
          }
          Left = i;
        }
        else
        {
          Left = Left->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            Left = j;
        }
      }
    }
    v14._Ptr = v6->_Left;
    v18 = -1;
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
      &tempMap,
      &result,
      v14,
      (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator)v6);
    operator delete(tempMap._Myhead);
    return 1;
  }
}

//----- (0040BED0) --------------------------------------------------------
void __thiscall CIOCPNet::~CIOCPNet(CIOCPNet *this)
{
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator v2; // [esp-8h] [ebp-24h]
  std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *Myhead; // [esp-4h] [ebp-20h]
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator result; // [esp+Ch] [ebp-10h] BYREF
  int v5; // [esp+18h] [ebp-4h]

  v5 = 2;
  CIOCPNet::Destroy(this);
  WSACleanup();
  Myhead = this->m_SessionPolicyMap._Myhead;
  v2._Ptr = Myhead->_Left;
  LOBYTE(v5) = 1;
  std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(
    &this->m_SessionPolicyMap,
    &result,
    v2,
    (std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator)Myhead);
  operator delete(this->m_SessionPolicyMap._Myhead);
  this->m_SessionPolicyMap._Myhead = 0;
  this->m_SessionPolicyMap._Mysize = 0;
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_ListenerList);
  operator delete(this->m_ListenerList._Myhead);
  this->m_ListenerList._Myhead = 0;
  DeleteCriticalSection(&this->m_IOCPLock.m_CSLock);
}

//----- (0040BF70) --------------------------------------------------------
CDLLModule *__thiscall CDLLModule::`vector deleting destructor'(CDLLModule *this, char a2)
{
  HINSTANCE__ *m_hDLL; // eax

  m_hDLL = this->m_hDLL;
  this->__vftable = (CDLLModule_vtbl *)&CDLLModule::`vftable';
  if ( m_hDLL )
    FreeLibrary(m_hDLL);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D56C4: using guessed type void *CDLLModule::`vftable';

//----- (0040BFA0) --------------------------------------------------------
int __thiscall CDBGFuncClass::Init(CDBGFuncClass *this, const char *szDll)
{
  HINSTANCE__ *LibraryA; // eax
  int v4; // ebp
  HINSTANCE__ *m_hDLL; // eax
  HINSTANCE__ *v6; // eax
  HINSTANCE__ *v7; // eax
  HINSTANCE__ *v8; // eax
  HINSTANCE__ *v9; // eax
  HINSTANCE__ *v10; // eax
  HINSTANCE__ *v11; // eax
  HINSTANCE__ *v12; // eax
  HINSTANCE__ *v13; // eax
  HINSTANCE__ *v14; // eax
  HINSTANCE__ *v15; // eax
  HINSTANCE__ *v16; // eax
  HINSTANCE__ *v17; // eax
  int result; // eax

  if ( this->m_hDLL )
    FreeLibrary(this->m_hDLL);
  LibraryA = LoadLibraryA(szDll);
  v4 = 0;
  this->m_hDLL = LibraryA;
  if ( LibraryA )
    this->MiniDumpWriteDump = (int (__stdcall *)(void *, unsigned int, void *, _MINIDUMP_TYPE, _MINIDUMP_EXCEPTION_INFORMATION *const, _MINIDUMP_USER_STREAM_INFORMATION *const, _MINIDUMP_CALLBACK_INFORMATION *const))GetProcAddress(LibraryA, "MiniDumpWriteDump");
  else
    this->MiniDumpWriteDump = 0;
  if ( this->MiniDumpWriteDump )
    v4 = 1;
  m_hDLL = this->m_hDLL;
  if ( m_hDLL )
    this->SymEnumSymbols = (int (__stdcall *)(void *, unsigned __int64, const char *, int (__stdcall *)(_SYMBOL_INFO *, unsigned int, void *), void *))GetProcAddress(m_hDLL, "SymEnumSymbols");
  else
    this->SymEnumSymbols = 0;
  if ( this->SymEnumSymbols )
    v4 = 1;
  v6 = this->m_hDLL;
  if ( v6 )
    this->SymSetContext = (unsigned int (__stdcall *)(void *, _IMAGEHLP_STACK_FRAME *, void *))GetProcAddress(
                                                                                                 v6,
                                                                                                 "SymSetContext");
  else
    this->SymSetContext = 0;
  if ( this->SymSetContext )
    v4 = 1;
  v7 = this->m_hDLL;
  if ( v7 )
    this->SymFromAddr = (int (__stdcall *)(void *, unsigned __int64, unsigned __int64 *, _SYMBOL_INFO *))GetProcAddress(v7, "SymFromAddr");
  else
    this->SymFromAddr = 0;
  if ( this->SymFromAddr )
    v4 = 1;
  v8 = this->m_hDLL;
  if ( v8 )
    this->StackWalk = (int (__stdcall *)(unsigned int, void *, void *, _tagSTACKFRAME *, void *, int (__stdcall *)(void *, unsigned int, void *, unsigned int, unsigned int *), void *(__stdcall *)(void *, unsigned int), unsigned int (__stdcall *)(void *, unsigned int), unsigned int (__stdcall *)(void *, void *, _tagADDRESS *)))GetProcAddress(v8, "StackWalk");
  else
    this->StackWalk = 0;
  if ( this->StackWalk )
    v4 = 1;
  v9 = this->m_hDLL;
  if ( v9 )
    this->SymGetLineFromAddr = (int (__stdcall *)(void *, unsigned int, unsigned int *, _IMAGEHLP_LINE *))GetProcAddress(v9, "SymGetLineFromAddr");
  else
    this->SymGetLineFromAddr = 0;
  if ( this->SymGetLineFromAddr )
    v4 = 1;
  v10 = this->m_hDLL;
  if ( v10 )
    this->SymGetLineFromAddr64 = (int (__stdcall *)(void *, unsigned __int64, unsigned int *, _IMAGEHLP_LINE64 *))GetProcAddress(v10, "SymGetLineFromAddr64");
  else
    this->SymGetLineFromAddr64 = 0;
  if ( this->SymGetLineFromAddr64 )
    v4 = 1;
  v11 = this->m_hDLL;
  if ( v11 )
    this->SymFunctionTableAccess = (void *(__stdcall *)(void *, unsigned int))GetProcAddress(
                                                                                v11,
                                                                                "SymFunctionTableAccess");
  else
    this->SymFunctionTableAccess = 0;
  if ( this->SymFunctionTableAccess )
    v4 = 1;
  v12 = this->m_hDLL;
  if ( v12 )
    this->SymGetModuleBase64 = (unsigned __int64 (__stdcall *)(void *, unsigned __int64))GetProcAddress(
                                                                                           v12,
                                                                                           "SymGetModuleBase64");
  else
    this->SymGetModuleBase64 = 0;
  if ( this->SymGetModuleBase64 )
    v4 = 1;
  v13 = this->m_hDLL;
  if ( v13 )
    this->SymInitialize = (int (__stdcall *)(void *, char *, int))GetProcAddress(v13, "SymInitialize");
  else
    this->SymInitialize = 0;
  if ( this->SymInitialize )
    v4 = 1;
  v14 = this->m_hDLL;
  if ( v14 )
    this->SymSetOptions = (unsigned int (__stdcall *)(unsigned int))GetProcAddress(v14, "SymSetOptions");
  else
    this->SymSetOptions = 0;
  if ( this->SymSetOptions )
    v4 = 1;
  v15 = this->m_hDLL;
  if ( v15 )
    this->SymCleanup = (int (__stdcall *)(void *))GetProcAddress(v15, "SymCleanup");
  else
    this->SymCleanup = 0;
  if ( this->SymCleanup )
    v4 = 1;
  v16 = this->m_hDLL;
  if ( v16 )
    this->SymGetModuleBase = (unsigned int (__stdcall *)(void *, unsigned int))GetProcAddress(v16, "SymGetModuleBase");
  else
    this->SymGetModuleBase = 0;
  if ( this->SymGetModuleBase )
    v4 = 1;
  v17 = this->m_hDLL;
  if ( v17 )
    this->SymGetTypeInfo = (int (__stdcall *)(void *, unsigned __int64, unsigned int, _IMAGEHLP_SYMBOL_TYPE_INFO, void *))GetProcAddress(v17, "SymGetTypeInfo");
  else
    this->SymGetTypeInfo = 0;
  result = 1;
  if ( !this->SymGetTypeInfo )
    return v4;
  return result;
}

//----- (0040C1A0) --------------------------------------------------------
void __thiscall CDBGFuncClass::~CDBGFuncClass(CDBGFuncClass *this)
{
  HINSTANCE__ *m_hDLL; // ecx

  this->__vftable = (CDBGFuncClass_vtbl *)&CDLLModule::`vftable';
  m_hDLL = this->m_hDLL;
  if ( m_hDLL )
    FreeLibrary(m_hDLL);
}
// 4D56C4: using guessed type void *CDLLModule::`vftable';

//----- (0040C1C0) --------------------------------------------------------
CDBGFuncClass *__thiscall CDBGFuncClass::`vector deleting destructor'(CDBGFuncClass *this, char a2)
{
  CDBGFuncClass::~CDBGFuncClass(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0040C1E0) --------------------------------------------------------
int __cdecl CExceptionReport::WriteRegistersInfo(char *szBuffer_Out, int nBufferSize, _CONTEXT *pContext)
{
  return _snprintf(
           szBuffer_Out,
           nBufferSize,
           "------------------------------------------------------------------------------\r\n"
           "    x86 Registers\r\n"
           "------------------------------------------------------------------------------\r\n"
           "\r\n"
           "EAX=%08x  EBX=%08x  ECX=%08x  EDX=%08x\r\n"
           "ESI=%08x  EDI=%08x  EBP=%08x\r\n"
           "DS =%04X      ES=%04X       FS=%04X       GS:%04X\r\n"
           "CS:EIP=%04X:%08x\r\n"
           "SS:ESP=%04X:%08x\r\n"
           "Flags=%08x\r\n"
           "\r\n"
           "\r\n",
           pContext->Eax,
           pContext->Ebx,
           pContext->Ecx,
           pContext->Edx,
           pContext->Esi,
           pContext->Edi,
           pContext->Ebp,
           pContext->SegDs,
           pContext->SegEs,
           pContext->SegFs,
           pContext->SegGs,
           pContext->SegCs,
           pContext->Eip,
           pContext->SegSs,
           pContext->Esp,
           pContext->EFlags);
}

//----- (0040C270) --------------------------------------------------------
char *__cdecl CExceptionReport::GetExceptionString(DWORD dwCode)
{
  char *result; // eax
  char *v2; // esi
  HMODULE ModuleHandleA; // eax

  if ( dwCode > 0xC000008D )
  {
    switch ( dwCode )
    {
      case 0xC000008E:
        result = "FLT_DIVIDE_BY_ZERO";
        break;
      case 0xC000008F:
        result = "FLT_INEXACT_RESULT";
        break;
      case 0xC0000090:
        result = "FLT_INVALID_OPERATION";
        break;
      case 0xC0000091:
        result = "FLT_OVERFLOW";
        break;
      case 0xC0000092:
        result = "FLT_STACK_CHECK";
        break;
      case 0xC0000093:
        result = "FLT_UNDERFLOW";
        break;
      case 0xC0000094:
        result = "INT_DIVIDE_BY_ZERO";
        break;
      case 0xC0000095:
        result = "INT_OVERFLOW";
        break;
      case 0xC0000096:
        result = "PRIV_INSTRUCTION";
        break;
      case 0xC00000FD:
        result = "STACK_OVERFLOW";
        break;
      default:
        goto LABEL_35;
    }
  }
  else if ( dwCode == -1073741683 )
  {
    return "FLT_DENORMAL_OPERAND";
  }
  else if ( dwCode > 0xC0000006 )
  {
    switch ( dwCode )
    {
      case 0xC0000008:
        result = "INVALID_HANDLE";
        break;
      case 0xC000001D:
        result = "ILLEGAL_INSTRUCTION";
        break;
      case 0xC0000025:
        result = "NONCONTINUABLE_EXCEPTION";
        break;
      case 0xC0000026:
        result = "INVALID_DISPOSITION";
        break;
      case 0xC000008C:
        result = "ARRAY_BOUNDS_EXCEEDED";
        break;
      default:
        goto LABEL_35;
    }
  }
  else if ( dwCode == -1073741818 )
  {
    return "IN_PAGE_ERROR";
  }
  else
  {
    if ( dwCode > 0x80000003 )
    {
      if ( dwCode == -2147483644 )
        return "SINGLE_STEP";
      if ( dwCode == -1073741819 )
        return "ACCESS_VIOLATION";
    }
    else
    {
      switch ( dwCode )
      {
        case 0x80000003:
          return "BREAKPOINT";
        case 0x80000001:
          return "GUARD_PAGE";
        case 0x80000002:
          return "DATATYPE_MISALIGNMENT";
      }
    }
LABEL_35:
    v2 = (char *)(*((_DWORD *)NtCurrentTeb()->ThreadLocalStoragePointer + _tls_index) + 8);
    ModuleHandleA = GetModuleHandleA("NTDLL.DLL");
    FormatMessageA(0xA00u, ModuleHandleA, dwCode, 0, v2, 0x104u, 0);
    return v2;
  }
  return result;
}

//----- (0040C500) --------------------------------------------------------
SIZE_T __cdecl CExceptionReport::GetLogicalAddress(
        void *addr,
        char *szModule,
        DWORD len,
        unsigned int *section,
        unsigned int *offset)
{
  SIZE_T result; // eax
  _DWORD *AllocationBase; // esi
  int v7; // ecx
  int v8; // eax
  char *v9; // ecx
  int v10; // edi
  unsigned __int64 v11; // rax
  int v12; // ebp
  int *i; // ebx
  int v14; // ecx
  unsigned int v15; // edi
  unsigned int v16; // [esp+4h] [ebp-20h]
  _MEMORY_BASIC_INFORMATION mbi; // [esp+8h] [ebp-1Ch] BYREF

  result = VirtualQuery(addr, &mbi, 0x1Cu);
  if ( result )
  {
    AllocationBase = mbi.AllocationBase;
    if ( mbi.AllocationBase && GetModuleFileNameA((HMODULE)mbi.AllocationBase, szModule, len) )
    {
      v7 = AllocationBase[15];
      v8 = *(unsigned __int16 *)((char *)AllocationBase + v7 + 20);
      v9 = (char *)AllocationBase + v7;
      v10 = (int)&v9[v8 + 24];
      v11 = (unsigned __int64)addr - (int)AllocationBase;
      v12 = 0;
      v16 = *((unsigned __int16 *)v9 + 3);
      if ( *((_WORD *)v9 + 3) )
      {
        for ( i = (int *)(v10 + 16); ; i += 10 )
        {
          v14 = *i;
          v15 = *(i - 1);
          if ( *i <= (unsigned int)*(i - 2) )
            v14 = *(i - 2);
          if ( v11 >= v15 && v11 <= v14 + v15 )
            break;
          if ( ++v12 >= v16 )
            return 0;
        }
        *section = v12 + 1;
        *offset = v11 - v15;
        return 1;
      }
      else
      {
        return 0;
      }
    }
    else
    {
      return 0;
    }
  }
  return result;
}

//----- (0040C5D0) --------------------------------------------------------
int __cdecl CExceptionReport::FormatOutputValue(
        char *szBuffer_Out,
        int nBufferSize,
        BasicType basicType,
        unsigned __int64 length,
        LPCSTR *pAddress)
{
  int result; // eax
  LPCSTR v6; // ecx
  BOOL v7; // eax
  const char *v8; // [esp+4h] [ebp-4Ch]
  char szAddress[64]; // [esp+Ch] [ebp-44h] BYREF

  result = 0;
  switch ( length )
  {
    case 1uLL:
      v6 = (LPCSTR)*(unsigned __int8 *)pAddress;
LABEL_3:
      result = _snprintf(szBuffer_Out, nBufferSize, " = %X", v6);
      break;
    case 2uLL:
      result = _snprintf(szBuffer_Out, nBufferSize, " = %X", *(unsigned __int16 *)pAddress);
      break;
    case 4uLL:
      if ( basicType != btFloat )
      {
        if ( basicType == btChar )
        {
          v7 = IsBadStringPtrA(*pAddress, 0x20u);
          v8 = *pAddress;
          if ( v7 )
            result = _snprintf(szBuffer_Out, nBufferSize, " = %X", v8);
          else
            result = _snprintf(szBuffer_Out, nBufferSize, " = \"%.31s\"", v8);
          break;
        }
        v6 = *pAddress;
        goto LABEL_3;
      }
      result = _snprintf(szBuffer_Out, nBufferSize, " = %f", *(float *)pAddress);
      break;
    case 8uLL:
      if ( basicType == btFloat )
      {
        result = _snprintf(szBuffer_Out, nBufferSize, " = %lf", *(double *)pAddress);
      }
      else
      {
        memset(szAddress, 0, sizeof(szAddress));
        _ui64toa(*(_QWORD *)pAddress, szAddress, 0x10u);
        result = _snprintf(szBuffer_Out, nBufferSize, " = 0x%s", szAddress);
      }
      break;
    default:
      return result;
  }
  if ( result < 0 )
    return -1;
  return result;
}

//----- (0040C770) --------------------------------------------------------
int __cdecl CExceptionReport::Dump(
        char *szBuffer_Out,
        int nBufferSize,
        unsigned __int64 dw64Offset,
        unsigned int dwSize,
        int bAlign)
{
  int v5; // esi
  unsigned __int8 *v6; // eax
  int v7; // eax
  int v8; // esi
  unsigned int v9; // eax
  unsigned int v10; // ecx
  int v11; // eax
  int v12; // eax
  int v13; // esi
  unsigned int v14; // edx
  int v15; // eax
  int v16; // eax
  int v17; // eax
  unsigned __int8 *pOut; // [esp+14h] [ebp-30h]
  unsigned int dwLoc; // [esp+18h] [ebp-2Ch]
  unsigned int dwILoc; // [esp+1Ch] [ebp-28h]
  unsigned int dwILoca; // [esp+1Ch] [ebp-28h]
  unsigned int dwX; // [esp+24h] [ebp-20h]
  unsigned int dwXa; // [esp+24h] [ebp-20h]
  unsigned __int8 *pLine; // [esp+28h] [ebp-1Ch]
  unsigned __int8 *pLinea; // [esp+28h] [ebp-1Ch]

  v5 = 0;
  v6 = (unsigned __int8 *)dw64Offset;
  pOut = (unsigned __int8 *)dw64Offset;
  if ( bAlign == 1 )
  {
    v6 = (unsigned __int8 *)(dw64Offset & 0xFFFFFFF0);
    pOut = (unsigned __int8 *)(dw64Offset & 0xFFFFFFF0);
  }
  for ( dwLoc = 0; dwLoc < dwSize; dwLoc += 16 )
  {
    pLine = v6;
    v7 = _snprintf(&szBuffer_Out[v5], nBufferSize - v5, "%08lX: ", v6);
    if ( v7 < 0 )
      return -1;
    v8 = v7 + v5;
    v9 = 0;
    dwX = 0;
    dwILoc = dwLoc;
    while ( v9 < 0x10 )
    {
      if ( v9 == 8 )
        _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "   ");
      v10 = dwILoc++;
      if ( v10 <= dwSize )
        v11 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "%02X ", *pLine++);
      else
        v11 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "?? ");
      if ( v11 < 0 )
        return -1;
      v8 += v11;
      v9 = ++dwX;
    }
    pLinea = pOut;
    v12 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, " ");
    if ( v12 < 0 )
      return -1;
    v13 = v12 + v8;
    dwXa = 0;
    dwILoca = dwLoc;
    while ( dwXa < 0x10 )
    {
      v14 = dwILoca++;
      if ( v14 <= dwSize )
      {
        if ( isprint(*pLinea) )
          v16 = *pLinea;
        else
          v16 = 46;
        v15 = _snprintf(&szBuffer_Out[v13], nBufferSize - v13, "%c", v16);
        ++pLinea;
      }
      else
      {
        v15 = _snprintf(&szBuffer_Out[v13], nBufferSize - v13, " ");
      }
      if ( v15 < 0 )
        return -1;
      v13 += v15;
      ++dwXa;
    }
    v17 = _snprintf(&szBuffer_Out[v13], nBufferSize - v13, "\r\n");
    if ( v17 < 0 )
      return -1;
    v5 = v17 + v13;
    pOut += 16;
    v6 = pOut;
  }
  return v5;
}

//----- (0040CA00) --------------------------------------------------------
int __stdcall CExceptionReport::UnhandledSecondExceptionFilter(_EXCEPTION_POINTERS *lpExceptionInfo)
{
  CExceptionReport *Instance; // esi
  _iobuf *m_logFile; // eax
  int (__stdcall *m_OldFilter)(_EXCEPTION_POINTERS *); // esi

  Instance = CExceptionReport::GetInstance();
  m_logFile = Instance->m_logFile;
  if ( m_logFile )
  {
    fprintf(m_logFile, "Exception occured AGAIN! EXITING NOW!\r\n");
    fclose(Instance->m_logFile);
    Instance->m_logFile = 0;
  }
  m_OldFilter = Instance->m_OldFilter;
  if ( m_OldFilter )
    return m_OldFilter(lpExceptionInfo);
  else
    return 0;
}

//----- (0040CA50) --------------------------------------------------------
void __thiscall CExceptionReport::~CExceptionReport(CExceptionReport *this)
{
  _iobuf *m_logFile; // eax
  HINSTANCE__ *m_hDLL; // esi

  if ( this->m_OldFilter )
  {
    SetUnhandledExceptionFilter(this->m_OldFilter);
    this->m_OldFilter = 0;
  }
  m_logFile = this->m_logFile;
  this->m_dwFeaturesFlag &= 0xFFFFFFF8;
  if ( m_logFile )
  {
    fprintf(m_logFile, "Exception occured AGAIN! EXITING NOW!\r\n");
    fclose(this->m_logFile);
    this->m_logFile = 0;
  }
  this->m_DBGHELP.__vftable = (CDBGFuncClass_vtbl *)&CDLLModule::`vftable';
  m_hDLL = this->m_DBGHELP.m_hDLL;
  if ( m_hDLL )
    FreeLibrary(m_hDLL);
}
// 4D56C4: using guessed type void *CDLLModule::`vftable';

//----- (0040CAC0) --------------------------------------------------------
int __cdecl CExceptionReport::WriteBasicInfo(char *szBuffer_Out, int nBufferSize, _EXCEPTION_RECORD *pExceptionRecord)
{
  _BYTE *v3; // eax
  char *ExceptionString; // eax
  void *ExceptionAddress; // [esp-Ch] [ebp-948h]
  unsigned int v7; // [esp-8h] [ebp-944h]
  unsigned int v8; // [esp-4h] [ebp-940h]
  unsigned int dwOffset; // [esp+4h] [ebp-938h] BYREF
  unsigned int dwSection; // [esp+8h] [ebp-934h] BYREF
  unsigned int dwUserLen; // [esp+Ch] [ebp-930h] BYREF
  unsigned int dwComputerLen; // [esp+10h] [ebp-92Ch] BYREF
  char szProgramName[260]; // [esp+14h] [ebp-928h] BYREF
  char szModuleName[260]; // [esp+118h] [ebp-824h] BYREF
  char szFileName[260]; // [esp+21Ch] [ebp-720h] BYREF
  char fname[260]; // [esp+320h] [ebp-61Ch] BYREF
  char szUserName[260]; // [esp+424h] [ebp-518h] BYREF
  char szComputerName[260]; // [esp+528h] [ebp-414h] BYREF
  char ext[260]; // [esp+62Ch] [ebp-310h] BYREF
  char dir[260]; // [esp+730h] [ebp-20Ch] BYREF
  char drive[260]; // [esp+834h] [ebp-108h] BYREF

  dwUserLen = 260;
  dwComputerLen = 260;
  dwSection = 0;
  dwOffset = 0;
  GetModuleFileNameA(0, szProgramName, 0x104u);
  _mbsrchr((unsigned __int8 *)szProgramName, 0x2Eu);
  if ( v3 )
    *v3 = 0;
  _splitpath(szProgramName, drive, dir, fname, ext);
  _mbsnbcpy((unsigned __int8 *)szProgramName, (const unsigned __int8 *)fname, 0x104u);
  GetModuleFileNameA(0, szFileName, 0x104u);
  GetUserNameA(szUserName, &dwUserLen);
  GetComputerNameA(szComputerName, &dwComputerLen);
  CExceptionReport::GetLogicalAddress(pExceptionRecord->ExceptionAddress, szModuleName, 0x104u, &dwSection, &dwOffset);
  v8 = dwOffset;
  v7 = dwSection;
  ExceptionAddress = pExceptionRecord->ExceptionAddress;
  ExceptionString = CExceptionReport::GetExceptionString(pExceptionRecord->ExceptionCode);
  return _snprintf(
           szBuffer_Out,
           nBufferSize,
           "------------------------------------------------------------------------------\r\n"
           "    Basic Information\r\n"
           "------------------------------------------------------------------------------\r\n"
           "\r\n"
           "Program Name : %s\r\n"
           "EXE          : %s\r\n"
           "User         : %s\r\n"
           "Computer     : %s\r\n"
           "\r\n"
           "Program      : %s\r\n"
           "Exception    : %08x (%s)\r\n"
           "Fault Address: %08x %02X:%08x\r\n"
           "\r\n"
           "\r\n",
           szProgramName,
           szFileName,
           szUserName,
           szComputerName,
           szModuleName,
           pExceptionRecord->ExceptionCode,
           ExceptionString,
           ExceptionAddress,
           v7,
           v8);
}
// 40CB18: variable 'v3' is possibly undefined

//----- (0040CC40) --------------------------------------------------------
int __cdecl CExceptionReport::WriteMemoryDump(
        char *szBuffer_Out,
        int nBufferSize,
        _CONTEXT *pContext,
        unsigned int nMaxIPDump,
        unsigned int nMaxStackDump)
{
  int v5; // eax
  int v7; // esi
  int v8; // eax
  int v9; // esi
  int v10; // eax
  int v11; // esi
  int v12; // eax
  int v13; // esi
  int v14; // eax

  v5 = _snprintf(
         szBuffer_Out,
         nBufferSize,
         "------------------------------------------------------------------------------\r\n"
         "    Memory Dump\r\n"
         "------------------------------------------------------------------------------\r\n"
         "\r\n"
         "Code: %d bytes starting at (EIP = %08lX)\r\n",
         nMaxIPDump,
         pContext->Eip);
  if ( v5 < 0 )
    return -1;
  v7 = v5;
  v8 = CExceptionReport::Dump(&szBuffer_Out[v5], nBufferSize - v5, pContext->Eip, nMaxIPDump, 0);
  if ( v8 < 0 )
    return -1;
  v9 = v8 + v7;
  v10 = _snprintf(
          &szBuffer_Out[v9],
          nBufferSize - v9,
          "\r\nStack: %d bytes starting at (ESP = %08lX)\r\n",
          nMaxStackDump,
          pContext->Esp);
  if ( v10 < 0 )
    return -1;
  v11 = v10 + v9;
  v12 = CExceptionReport::Dump(&szBuffer_Out[v11], nBufferSize - v11, pContext->Esp, nMaxStackDump, 1);
  if ( v12 < 0 )
    return -1;
  v13 = v12 + v11;
  v14 = _snprintf(&szBuffer_Out[v13], nBufferSize - v13, "\r\n\r\n");
  if ( v14 < 0 )
    return -1;
  else
    return v13 + v14;
}

//----- (0040CD20) --------------------------------------------------------
int __cdecl CExceptionReport::DumpTypeIndex(
        char *szBuffer_Out,
        unsigned int nBufferSize,
        unsigned __int64 modBase,
        unsigned int dwTypeIndex,
        unsigned int nestingLevel,
        unsigned int offset,
        int *bHandled)
{
  int v7; // esi
  CExceptionReport *Instance; // ebp
  HANDLE CurrentProcess; // eax
  HANDLE v11; // eax
  HANDLE v12; // eax
  int v13; // ecx
  char *v14; // ecx
  bool v15; // zf
  int v16; // eax
  HANDLE v17; // eax
  HANDLE v18; // eax
  HANDLE v19; // eax
  BasicType BasicType; // eax
  int v21; // eax
  int v22; // esi
  int v23; // eax
  int v24; // [esp+74h] [ebp-1050h]
  int v25; // [esp+74h] [ebp-1050h]
  int v26; // [esp+74h] [ebp-1050h]
  int v27; // [esp+90h] [ebp-1034h]
  int v28; // [esp+90h] [ebp-1034h]
  unsigned int v29; // [esp+94h] [ebp-1030h] BYREF
  unsigned int v30; // [esp+98h] [ebp-102Ch] BYREF
  HLOCAL hMem; // [esp+9Ch] [ebp-1028h] BYREF
  unsigned int nestingLevela; // [esp+A0h] [ebp-1024h]
  int v33; // [esp+A4h] [ebp-1020h] BYREF
  _BYTE length[12]; // [esp+A8h] [ebp-101Ch] BYREF
  int bHandleda; // [esp+B4h] [ebp-1010h] BYREF
  _DWORD v36[1027]; // [esp+B8h] [ebp-100Ch] BYREF

  v7 = 0;
  *bHandled = 0;
  v27 = 0;
  Instance = CExceptionReport::GetInstance();
  if ( !Instance->m_DBGHELP.SymGetTypeInfo )
    return 0;
  hMem = 0;
  CurrentProcess = GetCurrentProcess();
  if ( ((int (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, HLOCAL *))Instance->m_DBGHELP.SymGetTypeInfo)(
         CurrentProcess,
         modBase,
         HIDWORD(modBase),
         dwTypeIndex,
         1,
         &hMem) )
  {
    v7 = _snprintf(szBuffer_Out, nBufferSize, " %ls", (const wchar_t *)hMem);
    v27 = v7;
    LocalFree(hMem);
    if ( v7 < 0 )
      return -1;
  }
  v30 = 0;
  v11 = GetCurrentProcess();
  ((void (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, unsigned int *))Instance->m_DBGHELP.SymGetTypeInfo)(
    v11,
    modBase,
    HIDWORD(modBase),
    dwTypeIndex,
    13,
    &v30);
  if ( !v30 )
    return v7;
  v36[0] = v30;
  v36[1] = 0;
  v12 = GetCurrentProcess();
  if ( !((int (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, _DWORD *))Instance->m_DBGHELP.SymGetTypeInfo)(
          v12,
          modBase,
          HIDWORD(modBase),
          dwTypeIndex,
          7,
          v36) )
    return v7;
  v13 = _snprintf(&szBuffer_Out[v7], nBufferSize - v7, "\r\n") + v27;
  if ( v13 >= 0 )
  {
    v7 += v13;
    v28 = 0;
    if ( v30 )
    {
      nestingLevela = nestingLevel + 1;
      do
      {
        v29 = nestingLevela + 1;
        do
        {
          v14 = &szBuffer_Out[sprintf(szBuffer_Out, "\t")];
          v15 = v29 == 1;
          szBuffer_Out = v14;
          --v29;
        }
        while ( !v15 );
        v16 = CExceptionReport::DumpTypeIndex(
                &v14[v7],
                nBufferSize - v7,
                modBase,
                v36[v28 + 2],
                nestingLevela,
                offset,
                &bHandleda);
        if ( v16 < 0 )
          return -1;
        v7 += v16;
        if ( !bHandleda )
        {
          v24 = v36[v28 + 2];
          v29 = 0;
          v17 = GetCurrentProcess();
          ((void (__stdcall *)(HANDLE, _DWORD, _DWORD, int, int, unsigned int *))Instance->m_DBGHELP.SymGetTypeInfo)(
            v17,
            modBase,
            HIDWORD(modBase),
            v24,
            10,
            &v29);
          v25 = v36[v28 + 2];
          v33 = 0;
          v18 = GetCurrentProcess();
          ((void (__stdcall *)(HANDLE, _DWORD, _DWORD, int, int, int *))Instance->m_DBGHELP.SymGetTypeInfo)(
            v18,
            modBase,
            HIDWORD(modBase),
            v25,
            4,
            &v33);
          v26 = v33;
          *(_QWORD *)length = 0LL;
          v19 = GetCurrentProcess();
          ((void (__stdcall *)(HANDLE, _DWORD, _DWORD, int, int, _BYTE *))Instance->m_DBGHELP.SymGetTypeInfo)(
            v19,
            modBase,
            HIDWORD(modBase),
            v26,
            2,
            length);
          *(_DWORD *)&length[8] = offset + v29;
          BasicType = CExceptionReport::GetBasicType(v36[v28 + 2], modBase);
          v21 = CExceptionReport::FormatOutputValue(
                  &szBuffer_Out[v7],
                  nBufferSize - v7,
                  BasicType,
                  *(unsigned __int64 *)length,
                  *(LPCSTR **)&length[8]);
          if ( v21 < 0 )
            return -1;
          v22 = v21 + v7;
          v23 = _snprintf(&szBuffer_Out[v22], nBufferSize - v22, "\r\n");
          if ( v23 < 0 )
            return -1;
          v7 = v23 + v22;
        }
      }
      while ( ++v28 < v30 );
    }
    *bHandled = 1;
    return v7;
  }
  return -1;
}

//----- (0040D030) --------------------------------------------------------
int __cdecl CExceptionReport::FormatSymbolValue(
        _SYMBOL_INFO *pSym,
        _tagSTACKFRAME *sf,
        char *pszBuffer,
        unsigned int nBufferSize)
{
  _SYMBOL_INFO *v4; // esi
  int v5; // edi
  unsigned int Flags; // eax
  unsigned int v8; // ebx
  int v9; // eax
  int v10; // eax
  int v11; // edi
  unsigned int v12; // eax
  LPCSTR *Address; // ebp
  int v14; // eax
  int v15; // edi
  BasicType BasicType; // eax
  int v17; // eax

  v4 = pSym;
  v5 = 0;
  if ( pSym->Tag == 5 )
    return 0;
  Flags = pSym->Flags;
  v8 = nBufferSize;
  if ( (Flags & 0x40) != 0 )
  {
    v9 = _snprintf(pszBuffer, nBufferSize, "Parameter ");
  }
  else
  {
    if ( (Flags & 0x80u) == 0 )
      goto LABEL_10;
    v9 = _snprintf(pszBuffer, nBufferSize, "Local ");
  }
  if ( v9 < 0 )
    return -1;
  v5 = v9;
LABEL_10:
  v10 = _snprintf(&pszBuffer[v5], v8 - v5, "'%s'", v4->Name);
  if ( v10 < 0 )
    return -1;
  v11 = v10 + v5;
  v12 = v4->Flags;
  if ( (v12 & 0x10) != 0 )
  {
    Address = (LPCSTR *)(sf->AddrFrame.Offset + LODWORD(v4->Address));
  }
  else
  {
    if ( (v12 & 8) != 0 )
      return 0;
    Address = (LPCSTR *)v4->Address;
  }
  v14 = CExceptionReport::DumpTypeIndex(
          &pszBuffer[v11],
          v8 - v11,
          v4->ModBase,
          v4->TypeIndex,
          0,
          (unsigned int)Address,
          (int *)&pSym);
  if ( v14 < 0 )
    return -1;
  v15 = v14 + v11;
  if ( !pSym )
  {
    BasicType = CExceptionReport::GetBasicType(v4->TypeIndex, v4->ModBase);
    v17 = CExceptionReport::FormatOutputValue(&pszBuffer[v15], v8 - v15, BasicType, v4->Size, Address);
    if ( v17 < 0 )
      return -1;
    v15 += v17;
  }
  return v15;
}

//----- (0040D140) --------------------------------------------------------
int __stdcall CExceptionReport::EnumerateSymbolsCallback(
        _SYMBOL_INFO *pSymInfo,
        unsigned int SymbolSize,
        void *UserContext)
{
  char szBuffer[3072]; // [esp+Ch] [ebp-C1Ch] BYREF
  CPPEH_RECORD ms_exc; // [esp+C10h] [ebp-18h]

  ms_exc.registration.TryLevel = 0;
  if ( CExceptionReport::FormatSymbolValue(pSymInfo, *(_tagSTACKFRAME **)UserContext, szBuffer, 0xC00u) > 0 )
    *((_DWORD *)UserContext + 3) = _snprintf(
                                     *((char **)UserContext + 1),
                                     *((_DWORD *)UserContext + 2),
                                     "\t%s\r\n",
                                     szBuffer);
  return 1;
}

//----- (0040D200) --------------------------------------------------------
int __cdecl CExceptionReport::WriteStackDetails(
        char *szBuffer_Out,
        int nBufferSize,
        _CONTEXT *pContext,
        int bWriteVariables,
        int *bHasSymbol_Out,
        int nStackDepth)
{
  const char *v6; // eax
  int v7; // eax
  int v8; // esi
  void *Esp; // edx
  void *Ebp; // eax
  CExceptionReport *Instance; // edi
  HANDLE CurrentProcess; // eax
  int v13; // eax
  int v14; // esi
  bool v15; // zf
  HANDLE v16; // eax
  int v17; // eax
  int v18; // esi
  HANDLE v19; // eax
  int v20; // eax
  int v21; // eax
  HANDLE v22; // eax
  HANDLE v23; // eax
  int v24; // eax
  int v25; // eax
  HANDLE CurrentThread; // [esp+34h] [ebp-AFCh]
  void *v28; // [esp+40h] [ebp-AF0h]
  void *(__stdcall *SymFunctionTableAccess)(void *, unsigned int); // [esp+44h] [ebp-AECh]
  void *v30; // [esp+44h] [ebp-AECh]
  unsigned int (__stdcall *SymGetModuleBase)(void *, unsigned int); // [esp+48h] [ebp-AE8h]
  CDBGFuncClass *dbgFuncClass; // [esp+60h] [ebp-AD0h]
  unsigned int offset; // [esp+64h] [ebp-ACCh] BYREF
  unsigned int section; // [esp+68h] [ebp-AC8h] BYREF
  int nCount; // [esp+6Ch] [ebp-AC4h]
  unsigned __int64 symDisplacement; // [esp+70h] [ebp-AC0h] BYREF
  unsigned int dwLineDisplacement; // [esp+78h] [ebp-AB8h] BYREF
  _IMAGEHLP_LINE lineInfo; // [esp+7Ch] [ebp-AB4h] BYREF
  SymEnumSymbols_UserData symbol_userdata; // [esp+90h] [ebp-AA0h] BYREF
  void *addr[42]; // [esp+A0h] [ebp-A90h] BYREF
  _IMAGEHLP_STACK_FRAME imagehlpStackFrame; // [esp+148h] [ebp-9E8h] BYREF
  char szModule[264]; // [esp+1C8h] [ebp-968h] BYREF
  unsigned __int8 symbolBuffer[2136]; // [esp+2D0h] [ebp-860h] BYREF

  v6 = "Detail";
  if ( !bWriteVariables )
    v6 = "Short";
  v7 = _snprintf(
         szBuffer_Out,
         nBufferSize,
         "------------------------------------------------------------------------------\r\n"
         "    Call Stack (%s)\r\n"
         "------------------------------------------------------------------------------\r\n"
         "\r\n"
         "Address   Frame     Function                        SourceFile\r\n",
         v6);
  if ( v7 < 0 )
    return -1;
  v8 = v7;
  memset(addr, 0, 0xA4u);
  Esp = (void *)pContext->Esp;
  Ebp = (void *)pContext->Ebp;
  addr[0] = (void *)pContext->Eip;
  addr[6] = Ebp;
  addr[2] = (void *)3;
  addr[11] = (void *)3;
  addr[8] = (void *)3;
  memset(szModule, 0, 260);
  addr[9] = Esp;
  Instance = CExceptionReport::GetInstance();
  dbgFuncClass = &Instance->m_DBGHELP;
  if ( Instance->m_DBGHELP.StackWalk )
  {
    nCount = 0;
    if ( nStackDepth > 0 )
    {
      while ( addr[6] )
      {
        SymGetModuleBase = Instance->m_DBGHELP.SymGetModuleBase;
        SymFunctionTableAccess = Instance->m_DBGHELP.SymFunctionTableAccess;
        CurrentThread = GetCurrentThread();
        CurrentProcess = GetCurrentProcess();
        if ( !Instance->m_DBGHELP.StackWalk(
                332u,
                CurrentProcess,
                CurrentThread,
                (_tagSTACKFRAME *)addr,
                pContext,
                0,
                SymFunctionTableAccess,
                SymGetModuleBase,
                0) )
          break;
        v13 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "%08x  %08x  ", addr[0], addr[6]);
        if ( v13 < 0 )
          return -1;
        v14 = v13 + v8;
        *(_DWORD *)symbolBuffer = 2136;
        *(_DWORD *)&symbolBuffer[80] = 2048;
        v15 = dbgFuncClass->SymFromAddr == 0;
        symDisplacement = 0LL;
        if ( v15
          || (v28 = addr[0],
              v16 = GetCurrentProcess(),
              !((int (__stdcall *)(HANDLE, void *, _DWORD, unsigned __int64 *, unsigned __int8 *))dbgFuncClass->SymFromAddr)(
                 v16,
                 v28,
                 0,
                 &symDisplacement,
                 symbolBuffer)) )
        {
          section = 0;
          offset = 0;
          CExceptionReport::GetLogicalAddress(addr[0], szModule, 0x104u, &section, &offset);
          v17 = _snprintf(
                  &szBuffer_Out[v14],
                  nBufferSize - v14,
                  "%04X:%08x                   %s",
                  section,
                  offset,
                  szModule);
        }
        else
        {
          _wsprintfA(szModule, "%-s+0x%p", (const char *)&symbolBuffer[84], (const void *)symDisplacement);
          v17 = _snprintf(&szBuffer_Out[v14], nBufferSize - v14, "%-30s", szModule);
        }
        if ( v17 < 0 )
          return -1;
        v18 = v17 + v14;
        memset(&lineInfo.Key, 0, 16);
        lineInfo.SizeOfStruct = 20;
        dwLineDisplacement = 0;
        if ( dbgFuncClass->SymGetLineFromAddr )
        {
          v30 = addr[0];
          v19 = GetCurrentProcess();
          if ( dbgFuncClass->SymGetLineFromAddr(v19, (unsigned int)v30, &dwLineDisplacement, &lineInfo) )
          {
            v20 = _snprintf(
                    &szBuffer_Out[v18],
                    nBufferSize - v18,
                    "  %s line %u",
                    lineInfo.FileName,
                    lineInfo.LineNumber);
            if ( v20 < 0 )
              return -1;
            v18 += v20;
            if ( !*bHasSymbol_Out )
              *bHasSymbol_Out = 1;
          }
        }
        v21 = _snprintf(&szBuffer_Out[v18], nBufferSize - v18, "\r\n");
        if ( v21 < 0 )
          return -1;
        v8 = v21 + v18;
        if ( bWriteVariables )
        {
          memset(&imagehlpStackFrame, 0, sizeof(imagehlpStackFrame));
          imagehlpStackFrame.InstructionOffset = (unsigned int)addr[0];
          if ( dbgFuncClass->SymSetContext )
          {
            v22 = GetCurrentProcess();
            dbgFuncClass->SymSetContext(v22, &imagehlpStackFrame, 0);
          }
          if ( dbgFuncClass->SymEnumSymbols )
          {
            symbol_userdata.m_lpsf = (_tagSTACKFRAME *)addr;
            symbol_userdata.m_szBuffer_Out = &szBuffer_Out[v8];
            symbol_userdata.m_nWritten_Out = 0;
            symbol_userdata.m_nBufferSize_In = nBufferSize - v8;
            v23 = GetCurrentProcess();
            ((void (__stdcall *)(HANDLE, _DWORD, _DWORD, _DWORD, int (__stdcall *)(_SYMBOL_INFO *, unsigned int, void *), SymEnumSymbols_UserData *))dbgFuncClass->SymEnumSymbols)(
              v23,
              0,
              0,
              0,
              CExceptionReport::EnumerateSymbolsCallback,
              &symbol_userdata);
            if ( symbol_userdata.m_nWritten_Out < 0 )
              return -1;
            v8 += symbol_userdata.m_nWritten_Out;
          }
          v24 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "\r\n");
          if ( v24 < 0 )
            return -1;
          v8 += v24;
        }
        if ( ++nCount >= nStackDepth )
          break;
        Instance = (CExceptionReport *)dbgFuncClass;
      }
    }
  }
  v25 = _snprintf(&szBuffer_Out[v8], nBufferSize - v8, "\r\n\r\n");
  if ( v25 >= 0 )
    return v8 + v25;
  else
    return -1;
}

//----- (0040D600) --------------------------------------------------------
void __thiscall CExceptionReport::WriteExceptionReport(CExceptionReport *this, _EXCEPTION_POINTERS *pExceptionInfo)
{
  _iobuf *m_logFile; // eax
  HANDLE CurrentProcess; // eax
  HANDLE v5; // eax
  int bHasSymbol; // [esp+Ch] [ebp-4h] BYREF

  m_logFile = this->m_logFile;
  if ( m_logFile )
  {
    fprintf(m_logFile, "==============================================================================\r\n\r\n");
    if ( CExceptionReport::WriteBasicInfo(this->m_szLogBuffer, 0xFFFF, pExceptionInfo->ExceptionRecord) < 0 )
      _snprintf(this->m_szLogBuffer, 0xFFFFu, "Failed to write basic info.\r\n\r\n");
    fputs(this->m_szLogBuffer, this->m_logFile);
    fflush(this->m_logFile);
    if ( CExceptionReport::WriteRegistersInfo(this->m_szLogBuffer, 0xFFFF, pExceptionInfo->ContextRecord) < 0 )
      _snprintf(this->m_szLogBuffer, 0xFFFFu, "Failed to write registers info.\r\n\r\n");
    fputs(this->m_szLogBuffer, this->m_logFile);
    fflush(this->m_logFile);
    if ( this->m_DBGHELP.SymInitialize )
    {
      CurrentProcess = GetCurrentProcess();
      if ( this->m_DBGHELP.SymInitialize(CurrentProcess, 0, 1) )
      {
        qmemcpy(&this->m_tempContext, pExceptionInfo->ContextRecord, sizeof(this->m_tempContext));
        bHasSymbol = 0;
        if ( CExceptionReport::WriteStackDetails(this->m_szLogBuffer, 0xFFFF, &this->m_tempContext, 0, &bHasSymbol, 256) < 0 )
          _snprintf(this->m_szLogBuffer, 0xFFFFu, "Failed to write stack details.\r\n\r\n");
        fputs(this->m_szLogBuffer, this->m_logFile);
        fflush(this->m_logFile);
        if ( this->m_DBGHELP.SymCleanup )
        {
          v5 = GetCurrentProcess();
          this->m_DBGHELP.SymCleanup(v5);
        }
        fputs("\r\n\r\n\r\n", this->m_logFile);
        if ( CExceptionReport::WriteMemoryDump(
               this->m_szLogBuffer,
               0xFFFF,
               pExceptionInfo->ContextRecord,
               0x10u,
               0x400u) < 0 )
          _snprintf(this->m_szLogBuffer, 0xFFFFu, "Failed to write memory dump.\r\n\r\n");
        fputs(this->m_szLogBuffer, this->m_logFile);
        fflush(this->m_logFile);
      }
    }
  }
}

//----- (0040D7C0) --------------------------------------------------------
int __thiscall CExceptionReport::ProcessException(CExceptionReport *this, _EXCEPTION_POINTERS *lpExceptionInfo)
{
  char *m_szLogPrefixName; // eax
  char v4; // cl
  HANDLE FileA; // edi
  HANDLE CurrentProcess; // eax
  char *v7; // eax
  char v8; // cl
  _iobuf *v9; // eax
  _iobuf **p_m_logFile; // edi
  DWORD CurrentProcessId; // [esp-18h] [ebp-4Ch]
  _MINIDUMP_TYPE m_eMiniDumpType; // [esp-10h] [ebp-44h]

  SetUnhandledExceptionFilter(CExceptionReport::UnhandledSecondExceptionFilter);
  GetLocalTime(&this->m_tempSystemTime);
  m_szLogPrefixName = this->m_szLogPrefixName;
  do
  {
    v4 = *m_szLogPrefixName;
    m_szLogPrefixName[520] = *m_szLogPrefixName;
    ++m_szLogPrefixName;
  }
  while ( v4 );
  _wsprintfA(
    &this->m_szTempBuffer[strlen(this->m_szTempBuffer)],
    " %04d-%02d-%02d %02d,%02d,%02d.dmp",
    this->m_tempSystemTime.wYear,
    this->m_tempSystemTime.wMonth,
    this->m_tempSystemTime.wDay,
    this->m_tempSystemTime.wHour,
    this->m_tempSystemTime.wMinute,
    this->m_tempSystemTime.wSecond);
  FileA = CreateFileA(this->m_szTempBuffer, 0x40000000u, 1u, 0, 2u, 0x80u, 0);
  if ( this->m_DBGHELP.MiniDumpWriteDump && FileA != (HANDLE)-1 )
  {
    this->m_miniDumpInfo.ThreadId = 0;
    this->m_miniDumpInfo.ExceptionPointers = 0;
    this->m_miniDumpInfo.ClientPointers = 0;
    this->m_miniDumpInfo.ThreadId = GetCurrentThreadId();
    this->m_miniDumpInfo.ExceptionPointers = lpExceptionInfo;
    this->m_miniDumpInfo.ClientPointers = 0;
    m_eMiniDumpType = this->m_eMiniDumpType;
    CurrentProcessId = GetCurrentProcessId();
    CurrentProcess = GetCurrentProcess();
    this->m_DBGHELP.MiniDumpWriteDump(
      CurrentProcess,
      CurrentProcessId,
      FileA,
      m_eMiniDumpType,
      &this->m_miniDumpInfo,
      0,
      0);
    CloseHandle(FileA);
  }
  v7 = this->m_szLogPrefixName;
  do
  {
    v8 = *v7;
    v7[520] = *v7;
    ++v7;
  }
  while ( v8 );
  _wsprintfA(
    &this->m_szTempBuffer[strlen(this->m_szTempBuffer)],
    " %04d-%02d-%02d %02d,%02d,%02d.txt",
    this->m_tempSystemTime.wYear,
    this->m_tempSystemTime.wMonth,
    this->m_tempSystemTime.wDay,
    this->m_tempSystemTime.wHour,
    this->m_tempSystemTime.wMinute,
    this->m_tempSystemTime.wSecond);
  v9 = fopen(this->m_szTempBuffer, "wt");
  p_m_logFile = &this->m_logFile;
  this->m_logFile = v9;
  if ( v9 )
  {
    CExceptionReport::WriteExceptionReport(this, lpExceptionInfo);
    if ( this->m_lpUserFunc )
    {
      fprintf(
        *p_m_logFile,
        "------------------------------------------------------------------------------\r\n"
        "    Application-specific log\r\n"
        "------------------------------------------------------------------------------\r\n"
        "\r\n");
      fflush(*p_m_logFile);
      if ( this->m_lpUserFunc(this->m_szLogBuffer, 0xFFFF) > 0 )
      {
        this->m_szLogBuffer[65534] = 0;
        fprintf(*p_m_logFile, "%s", this->m_szLogBuffer);
      }
      fprintf(
        *p_m_logFile,
        "\r\n\r\n==============================================================================\r\n");
      if ( *p_m_logFile )
      {
        fprintf(*p_m_logFile, "Exception occured AGAIN! EXITING NOW!\r\n");
        fclose(*p_m_logFile);
        *p_m_logFile = 0;
      }
    }
  }
  return 1;
}

//----- (0040DA70) --------------------------------------------------------
int __stdcall CExceptionReport::UnhandledExceptionFilter(_EXCEPTION_POINTERS *lpExceptionInfo)
{
  CExceptionReport *Instance; // eax

  Instance = CExceptionReport::GetInstance();
  return CExceptionReport::ProcessException(Instance, lpExceptionInfo);
}

//----- (0040DA90) --------------------------------------------------------
void __thiscall CExceptionReport::Enable(CExceptionReport *this, unsigned int dwEnableFeature)
{
  if ( this->m_OldFilter )
  {
    this->m_dwFeaturesFlag |= dwEnableFeature;
  }
  else
  {
    if ( (dwEnableFeature & 1) != 0 )
      this->m_OldFilter = SetUnhandledExceptionFilter(CExceptionReport::UnhandledExceptionFilter);
    this->m_dwFeaturesFlag |= dwEnableFeature;
  }
}

//----- (0040DAE0) --------------------------------------------------------
void __thiscall CExceptionReport::CExceptionReport(CExceptionReport *this)
{
  unsigned __int8 *m_szLogPrefixName; // edi
  _BYTE *v3; // eax
  CDBGFuncClass_vtbl *v4; // edx
  char fname[260]; // [esp+10h] [ebp-420h] BYREF
  char ext[260]; // [esp+114h] [ebp-31Ch] BYREF
  char dir[260]; // [esp+218h] [ebp-218h] BYREF
  char drive[256]; // [esp+31Ch] [ebp-114h] BYREF
  int v9; // [esp+42Ch] [ebp-4h]

  this->m_DBGHELP.m_hDLL = 0;
  this->m_DBGHELP.__vftable = (CDBGFuncClass_vtbl *)&CDBGFuncClass::`vftable';
  m_szLogPrefixName = (unsigned __int8 *)this->m_szLogPrefixName;
  v9 = 0;
  this->m_lpUserFunc = 0;
  this->m_OldFilter = 0;
  this->m_logFile = 0;
  this->m_dwFeaturesFlag = 0;
  this->m_eMiniDumpType = MiniDumpNormal;
  GetModuleFileNameA(0, this->m_szLogPrefixName, 0x104u);
  _mbsrchr(m_szLogPrefixName, 0x2Eu);
  if ( v3 )
    *v3 = 0;
  _splitpath((char *)m_szLogPrefixName, drive, dir, fname, ext);
  _mbsnbcpy(m_szLogPrefixName, (const unsigned __int8 *)fname, 0x104u);
  if ( !this->m_OldFilter )
    this->m_OldFilter = SetUnhandledExceptionFilter(CExceptionReport::UnhandledExceptionFilter);
  v4 = this->m_DBGHELP.__vftable;
  this->m_dwFeaturesFlag |= 7u;
  v4->Init(&this->m_DBGHELP, "DBGHELP.DLL");
  LoadLibraryA("Loader.dll");
}
// 40DB57: variable 'v3' is possibly undefined
// 4D56CC: using guessed type void *CDBGFuncClass::`vftable';

//----- (0040DBF0) --------------------------------------------------------
CExceptionReport *__cdecl CExceptionReport::GetInstance()
{
  if ( (_S1_0 & 1) == 0 )
  {
    _S1_0 |= 1u;
    CExceptionReport::CExceptionReport(&exceptionReport);
    atexit(_E2_3);
  }
  return &exceptionReport;
}

//----- (0040DC50) --------------------------------------------------------
BasicType __cdecl CExceptionReport::GetBasicType(unsigned int typeIndex, unsigned __int64 modBase)
{
  CExceptionReport *Instance; // esi
  HANDLE CurrentProcess; // eax
  HANDLE v5; // eax
  HANDLE v6; // eax
  unsigned int v7; // [esp+14h] [ebp-24h]
  BasicType basicType; // [esp+30h] [ebp-8h] BYREF
  unsigned int typeId; // [esp+34h] [ebp-4h] BYREF

  Instance = CExceptionReport::GetInstance();
  if ( !Instance->m_DBGHELP.SymGetTypeInfo )
    return 0;
  CurrentProcess = GetCurrentProcess();
  if ( ((int (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, BasicType *))Instance->m_DBGHELP.SymGetTypeInfo)(
         CurrentProcess,
         modBase,
         HIDWORD(modBase),
         typeIndex,
         5,
         &basicType) )
  {
    return basicType;
  }
  typeId = 0;
  v5 = GetCurrentProcess();
  if ( ((int (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, unsigned int *))Instance->m_DBGHELP.SymGetTypeInfo)(
         v5,
         modBase,
         HIDWORD(modBase),
         typeIndex,
         4,
         &typeId)
    && (v7 = typeId,
        v6 = GetCurrentProcess(),
        ((int (__stdcall *)(HANDLE, _DWORD, _DWORD, unsigned int, int, BasicType *))Instance->m_DBGHELP.SymGetTypeInfo)(
          v6,
          modBase,
          HIDWORD(modBase),
          v7,
          5,
          &basicType)) )
  {
    return basicType;
  }
  else
  {
    return 0;
  }
}

//----- (0040DCE0) --------------------------------------------------------
void __thiscall INET_Addr::set_addr(INET_Addr *this, const char *addr, u_short port)
{
  unsigned int v4; // eax

  *(_DWORD *)&this->m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[10] = 0;
  this->m_SockAddr.sa_family = 2;
  if ( addr )
    v4 = inet_addr(addr);
  else
    v4 = htonl(0);
  *(_DWORD *)&this->m_SockAddr.sa_data[2] = v4;
  if ( port )
    *(_WORD *)this->m_SockAddr.sa_data = htons(port);
  else
    *(_WORD *)this->m_SockAddr.sa_data = 0;
  this->m_iAddrLen = 16;
}

//----- (0040DD50) --------------------------------------------------------
void __thiscall INET_Addr::set_addr(INET_Addr *this, const sockaddr *addr, int size)
{
  this->m_SockAddr = *addr;
  this->m_iAddrLen = size;
}

//----- (0040DD80) --------------------------------------------------------
void __thiscall INET_Addr::set_addr(INET_Addr *this, in_addr addr, u_short port)
{
  *(_DWORD *)&this->m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_SockAddr.sa_data[10] = 0;
  this->m_SockAddr.sa_family = 2;
  *(in_addr *)&this->m_SockAddr.sa_data[2] = addr;
  if ( port )
  {
    *(_WORD *)this->m_SockAddr.sa_data = htons(port);
    this->m_iAddrLen = 16;
  }
  else
  {
    *(_WORD *)this->m_SockAddr.sa_data = 0;
    this->m_iAddrLen = 16;
  }
}

//----- (0040DDD0) --------------------------------------------------------
int __stdcall CThread::ThreadFunc(void *pArg)
{
  return (*(int (__thiscall **)(void *))(*(_DWORD *)pArg + 4))(pArg);
}

//----- (0040DDE0) --------------------------------------------------------
HANDLE __cdecl CThreadMgr::Run(CThread *lpThread)
{
  HANDLE result; // eax
  unsigned int nThreadID; // [esp+4h] [ebp-4h] BYREF

  nThreadID = 0;
  result = _beginthreadex(
             0,
             0,
             (unsigned int (__stdcall *)(void *))CThread::ThreadFunc,
             (tagEntry *)lpThread,
             0,
             &nThreadID);
  lpThread->m_hThreadHandle = result;
  return result;
}

//----- (0040DE10) --------------------------------------------------------
bool __cdecl CThreadMgr::Stop(CThread *lpThread, DWORD dwTimeout)
{
  void *m_hThreadHandle; // esi
  CThread_vtbl *v4; // eax

  if ( !lpThread )
    return 0;
  m_hThreadHandle = lpThread->m_hThreadHandle;
  if ( m_hThreadHandle == (void *)-1 )
    return 0;
  v4 = lpThread->__vftable;
  lpThread->m_hThreadHandle = (void *)-1;
  ((void (*)(void))v4->End)();
  WaitForSingleObject(m_hThreadHandle, dwTimeout);
  return CloseHandle(m_hThreadHandle);
}

//----- (0040DE50) --------------------------------------------------------
int __thiscall  __thiscall `vcall'{8,{flat}}(void *this)
{
  return (*(int (__thiscall **)(void *))(*(_DWORD *)this + 8))(this);
}

//----- (0040DE60) --------------------------------------------------------
void __thiscall CThreadMgr::CThreadMgr(CThreadMgr *this)
{
  this->__vftable = (CThreadMgr_vtbl *)&CThreadMgr::`vftable';
  InitializeCriticalSection(&this->m_ThreadLock.m_CSLock);
  this->m_nThreadNum = 0;
  this->m_bJoinStarted = 0;
  memset(this->m_lpThreads, 0, sizeof(this->m_lpThreads));
  memset(this->m_hThreads, 0xFFu, sizeof(this->m_hThreads));
}
// 4D6250: using guessed type void *CThreadMgr::`vftable';

//----- (0040DEB0) --------------------------------------------------------
bool __thiscall CThreadMgr::RegisterAndRun(CThreadMgr *this, CThread *llpThread)
{
  unsigned int m_nThreadNum; // edi
  HANDLE v5; // eax
  unsigned int nThreadID; // [esp+8h] [ebp-4h] BYREF

  nThreadID = 0;
  if ( !llpThread )
    return 0;
  EnterCriticalSection(&this->m_ThreadLock.m_CSLock);
  m_nThreadNum = this->m_nThreadNum;
  if ( m_nThreadNum >= 0x3F || this->m_bJoinStarted == 1 )
  {
    LeaveCriticalSection(&this->m_ThreadLock.m_CSLock);
    return 0;
  }
  else
  {
    this->m_nThreadNum = m_nThreadNum + 1;
    LeaveCriticalSection(&this->m_ThreadLock.m_CSLock);
    this->m_lpThreads[m_nThreadNum] = llpThread;
    v5 = _beginthreadex(
           0,
           0,
           (unsigned int (__stdcall *)(void *))CThread::ThreadFunc,
           (tagEntry *)llpThread,
           0,
           &nThreadID);
    this->m_hThreads[m_nThreadNum] = v5;
    return v5 != 0;
  }
}

//----- (0040DF50) --------------------------------------------------------
std::mem_fun_t<int,CThread> *__cdecl std::for_each<CThread * *,std::mem_fun_t<int,CThread>>(
        std::mem_fun_t<int,CThread> *result,
        CThread **_First,
        CThread **_Last,
        std::mem_fun_t<int,CThread> _Func)
{
  CThread **v4; // esi
  std::mem_fun_t<int,CThread> *v5; // eax

  v4 = _First;
  if ( _First == _Last )
  {
    v5 = result;
    result->_Pmemfun = _Func._Pmemfun;
  }
  else
  {
    do
      _Func._Pmemfun(*v4++);
    while ( v4 != _Last );
    v5 = result;
    result->_Pmemfun = _Func._Pmemfun;
  }
  return v5;
}

//----- (0040DF90) --------------------------------------------------------
char __thiscall CThreadMgr::JoinThread(CThreadMgr *this)
{
  CCSLock *p_m_ThreadLock; // ebx
  CThread **m_lpThreads; // edi
  void **m_hThreads; // ebp
  void **v6; // edi
  std::mem_fun_t<int,CThread> result; // [esp+8h] [ebp-4h] BYREF

  p_m_ThreadLock = &this->m_ThreadLock;
  EnterCriticalSection(&this->m_ThreadLock.m_CSLock);
  if ( this->m_nThreadNum )
  {
    this->m_bJoinStarted = 1;
    LeaveCriticalSection(&p_m_ThreadLock->m_CSLock);
    m_lpThreads = this->m_lpThreads;
    std::for_each<CThread * *,std::mem_fun_t<int,CThread>>(
      &result,
      this->m_lpThreads,
      &this->m_lpThreads[this->m_nThreadNum],
      (std::mem_fun_t<int,CThread>) __thiscall `vcall'{8,{flat}});
    m_hThreads = this->m_hThreads;
    WaitForMultipleObjects(this->m_nThreadNum, this->m_hThreads, 1, 0xFFFFFFFF);
    if ( this->m_lpThreads != (CThread **)this->m_hThreads )
    {
      do
      {
        if ( *m_lpThreads )
          ((void (__thiscall *)(CThread *, int))(*m_lpThreads)->~CThread)(*m_lpThreads, 1);
        ++m_lpThreads;
      }
      while ( m_lpThreads != (CThread **)m_hThreads );
    }
    v6 = this->m_hThreads;
    if ( m_hThreads != (void **)p_m_ThreadLock )
    {
      do
        CloseHandle(*v6++);
      while ( v6 != (void **)p_m_ThreadLock );
    }
    EnterCriticalSection(&p_m_ThreadLock->m_CSLock);
    this->m_nThreadNum = 0;
    this->m_bJoinStarted = 0;
    LeaveCriticalSection(&p_m_ThreadLock->m_CSLock);
    return 1;
  }
  else
  {
    LeaveCriticalSection(&p_m_ThreadLock->m_CSLock);
    return 1;
  }
}

//----- (0040E060) --------------------------------------------------------
void __thiscall CThreadMgr::~CThreadMgr(CThreadMgr *this)
{
  this->__vftable = (CThreadMgr_vtbl *)&CThreadMgr::`vftable';
  CThreadMgr::JoinThread(this);
  DeleteCriticalSection(&this->m_ThreadLock.m_CSLock);
}
// 4D6250: using guessed type void *CThreadMgr::`vftable';

//----- (0040E0B0) --------------------------------------------------------
CThreadMgr *__thiscall CThreadMgr::`scalar deleting destructor'(CThreadMgr *this, char a2)
{
  CThreadMgr::~CThreadMgr(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0040E0D0) --------------------------------------------------------
void __thiscall CBufferQueue::CBufferQueue(CBufferQueue *this)
{
  this->__vftable = (CBufferQueue_vtbl *)&CBufferQueue::`vftable';
  this->m_lpHead = 0;
  this->m_lpTail = 0;
  this->m_bufferNum = 0;
  this->m_queueSize = 0;
  this->m_maxQueueSize = -1;
}
// 4D6254: using guessed type void *CBufferQueue::`vftable';

//----- (0040E0F0) --------------------------------------------------------
char __thiscall CBufferQueue::enqueue(CBufferQueue *this, CBuffer *lpBuffer, bool bPendHead)
{
  int v3; // esi
  CBuffer *m_lpHead; // edx
  unsigned int v5; // edx

  if ( lpBuffer )
  {
    v3 = lpBuffer->wr_ptr_ - lpBuffer->rd_ptr_;
    if ( v3 + this->m_queueSize < this->m_maxQueueSize )
    {
      m_lpHead = this->m_lpHead;
      if ( m_lpHead )
      {
        if ( !bPendHead )
        {
          this->m_lpTail->next_ = lpBuffer;
          lpBuffer->prev_ = this->m_lpTail;
          this->m_lpTail = lpBuffer;
LABEL_9:
          v5 = this->m_bufferNum + 1;
          this->m_queueSize += v3;
          this->m_bufferNum = v5;
          return 1;
        }
        lpBuffer->next_ = m_lpHead;
        this->m_lpHead->prev_ = lpBuffer;
      }
      else
      {
        this->m_lpTail = lpBuffer;
      }
      this->m_lpHead = lpBuffer;
      goto LABEL_9;
    }
  }
  return 0;
}

//----- (0040E160) --------------------------------------------------------
CBuffer *__thiscall CBufferQueue::dequeue(CBufferQueue *this)
{
  CBuffer *result; // eax

  result = this->m_lpHead;
  if ( !result )
    return 0;
  this->m_lpHead = result->next_;
  result->prev_ = 0;
  result->next_ = 0;
  if ( this->m_lpTail == result )
    this->m_lpTail = 0;
  --this->m_bufferNum;
  this->m_queueSize += result->rd_ptr_ - result->wr_ptr_;
  return result;
}

//----- (0040E1A0) --------------------------------------------------------
void __thiscall CBufferQueue::clear(CBufferQueue *this)
{
  CBuffer *m_lpHead; // eax

  while ( this->m_lpHead )
  {
    m_lpHead = this->m_lpHead;
    this->m_lpHead = m_lpHead->next_;
    if ( m_lpHead )
      m_lpHead->bufferfactory_->Release(m_lpHead->bufferfactory_, m_lpHead);
  }
  this->m_lpTail = 0;
  this->m_bufferNum = 0;
  this->m_queueSize = 0;
}

//----- (0040E1F0) --------------------------------------------------------
void __thiscall CBufferQueue::splice(CBufferQueue *this, CBufferQueue *Buffer_In, bool bPendHead)
{
  CBuffer *m_lpHead; // edx
  CBuffer *v4; // esi

  m_lpHead = Buffer_In->m_lpHead;
  if ( m_lpHead )
  {
    v4 = this->m_lpHead;
    if ( v4 )
    {
      if ( bPendHead )
      {
        Buffer_In->m_lpTail->next_ = v4;
        this->m_lpHead->prev_ = Buffer_In->m_lpTail;
        this->m_lpHead = Buffer_In->m_lpHead;
      }
      else
      {
        this->m_lpTail->next_ = m_lpHead;
        Buffer_In->m_lpHead->prev_ = this->m_lpTail;
        this->m_lpTail = Buffer_In->m_lpTail;
      }
    }
    else
    {
      this->m_lpHead = m_lpHead;
      this->m_lpTail = Buffer_In->m_lpTail;
    }
    this->m_bufferNum += Buffer_In->m_bufferNum;
    this->m_queueSize += Buffer_In->m_queueSize;
    Buffer_In->m_lpHead = 0;
    Buffer_In->m_lpTail = 0;
    Buffer_In->m_bufferNum = 0;
    Buffer_In->m_queueSize = 0;
  }
}

//----- (0040E270) --------------------------------------------------------
void __thiscall CBufferQueue::~CBufferQueue(CBufferQueue *this)
{
  this->__vftable = (CBufferQueue_vtbl *)&CBufferQueue::`vftable';
  CBufferQueue::clear(this);
}
// 4D6254: using guessed type void *CBufferQueue::`vftable';

//----- (0040E280) --------------------------------------------------------
CBufferQueue *__thiscall CBufferQueue::`scalar deleting destructor'(CBufferQueue *this, char a2)
{
  this->__vftable = (CBufferQueue_vtbl *)&CBufferQueue::`vftable';
  CBufferQueue::clear(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D6254: using guessed type void *CBufferQueue::`vftable';

//----- (0040E2B0) --------------------------------------------------------
void __thiscall CSession::CSession(CSession *this, CSessionPolicy *SessionPolicy)
{
  CSessionPolicy *m_SessionPolicy; // ecx

  InitializeCriticalSection(&this->m_SessionLock.m_CSLock);
  this->m_SessionPolicy = SessionPolicy;
  this->m_hSocket = -1;
  this->m_RemoteAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_RemoteAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_RemoteAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_RemoteAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_RemoteAddr.m_SockAddr.sa_data[10] = 0;
  this->m_LocalAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_LocalAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_LocalAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_LocalAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_LocalAddr.m_SockAddr.sa_data[10] = 0;
  CBufferQueue::CBufferQueue(&this->m_SendQueue);
  this->m_dwRecvPending = 0;
  this->m_dwSendPending = 0;
  this->m_nRefCount = 0;
  this->m_Statistics.m_dwTotalReceived = 0;
  this->m_Statistics.m_dwTotalSendPending = 0;
  this->m_Statistics.m_dwTotalSendCompleted = 0;
  this->m_Statistics.m_ConnectedTime = 0;
  this->m_Statistics.m_DisconnectedTime = 0;
  m_SessionPolicy = this->m_SessionPolicy;
  this->m_cCurrentStatus = 0;
  this->m_cFlags = 0;
  this->m_usPadding = 17219;
  CSessionPolicy::AddRef(m_SessionPolicy);
  this->m_lpRecvBuffer = SessionPolicy->m_lpBufferFactory->Create(SessionPolicy->m_lpBufferFactory, 0x8000);
  this->m_lpDispatch = SessionPolicy->m_lpDispatchFactory->CreateDispatch(SessionPolicy->m_lpDispatchFactory, this);
}

//----- (0040E390) --------------------------------------------------------
void __thiscall CSession::SetAddress(CSession *this, INET_Addr *remoteAddr, INET_Addr *localAddr)
{
  this->m_RemoteAddr = *remoteAddr;
  this->m_LocalAddr = *localAddr;
}

//----- (0040E3E0) --------------------------------------------------------
int __thiscall CSession::InternalPrintStatistics(CSession *this, char *szBuffer, int nBufferLen)
{
  const char *v4; // edi
  int v5; // eax
  double v6; // st7
  int result; // eax
  int m_ConnectedTime; // [esp+4h] [ebp-28h]
  unsigned int m_dwTotalReceived; // [esp+8h] [ebp-24h]
  unsigned int m_dwTotalSendPending; // [esp+Ch] [ebp-20h]
  unsigned int m_dwTotalSendCompleted; // [esp+10h] [ebp-1Ch]
  unsigned int m_dwSendPending; // [esp+14h] [ebp-18h]
  unsigned int m_dwRecvPending; // [esp+18h] [ebp-14h]
  int m_nRefCount; // [esp+1Ch] [ebp-10h]
  int m_cFlags; // [esp+20h] [ebp-Ch]

  if ( this->m_cCurrentStatus )
  {
    if ( this->m_cCurrentStatus == 1 )
      v4 = "DISCONNECTED";
    else
      v4 = "Unknown Mode";
  }
  else
  {
    v4 = "CONNECTED";
  }
  m_cFlags = this->m_cFlags;
  m_nRefCount = this->m_nRefCount;
  m_dwRecvPending = this->m_dwRecvPending;
  m_dwSendPending = this->m_dwSendPending;
  m_dwTotalSendCompleted = this->m_Statistics.m_dwTotalSendCompleted;
  m_dwTotalSendPending = this->m_Statistics.m_dwTotalSendPending;
  m_dwTotalReceived = this->m_Statistics.m_dwTotalReceived;
  m_ConnectedTime = this->m_Statistics.m_ConnectedTime;
  v5 = time(0);
  v6 = difftime(v5, m_ConnectedTime);
  result = _snprintf(
             szBuffer,
             nBufferLen - 1,
             "[m_hSocket:0x%08x][%15s][DiffTime(Sec):%8f][m_dwTotalReceived:%5d][m_dwTotalSendPending:%5d][m_dwTotalSendC"
             "ompleted:%5d][m_dwSendPending:%d][m_dwRecvPending:%d][m_nRefCount:%2d][flags:0x%02x]",
             this->m_hSocket,
             v4,
             v6,
             m_dwTotalReceived,
             m_dwTotalSendPending,
             m_dwTotalSendCompleted,
             m_dwSendPending,
             m_dwRecvPending,
             m_nRefCount,
             m_cFlags);
  szBuffer[nBufferLen - 1] = 0;
  return result;
}

//----- (0040E470) --------------------------------------------------------
void __thiscall CSession::InternalCloseSocket(CSession *this)
{
  char *v2; // edi
  int Error; // eax
  int v4; // eax
  char szBuffer[1024]; // [esp+4h] [ebp-404h] BYREF

  if ( this->m_hSocket != -1 )
  {
    v2 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    if ( shutdown(this->m_hSocket, 1) == -1 )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_DETAIL,
        "CSession::InternalCloseSocket",
        aDWorkRylSource_75,
        241,
        "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ Shutdown failed - ErrorCode:%d",
        this,
        this->m_lpDispatch,
        v2,
        (const void *)this->m_hSocket,
        Error);
    }
    if ( closesocket(this->m_hSocket) == -1 )
    {
      v4 = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_DETAIL,
        "CSession::InternalCloseSocket",
        aDWorkRylSource_75,
        247,
        "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket failed - ErrorCode:%d",
        this,
        this->m_lpDispatch,
        v2,
        (const void *)this->m_hSocket,
        v4);
    }
    if ( CSession::InternalPrintStatistics(this, szBuffer, 1024) > 0 )
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_DETAIL,
        "CSession::InternalCloseSocket",
        aDWorkRylSource_75,
        256,
        "SP:0x%p/DP:0x%p/IP:%15s/ InternalCloseSocket - Statistics : %s",
        this,
        this->m_lpDispatch,
        v2,
        szBuffer);
    this->m_cFlags |= 8u;
    this->m_hSocket = -1;
  }
  this->m_cCurrentStatus = 1;
}

//----- (0040E5A0) --------------------------------------------------------
bool __thiscall CSession::Shutdown(CSession *this)
{
  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  this->m_cFlags |= 4u;
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return 0;
}

//----- (0040E5D0) --------------------------------------------------------
int __thiscall CSession::AddRef(CSession *this)
{
  int v2; // ecx
  int v3; // edi

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v2 = this->m_nRefCount + 1;
  this->m_nRefCount = v2;
  v3 = v2;
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return v3;
}

//----- (0040E5F0) --------------------------------------------------------
int __thiscall CSession::Release(CSession *this)
{
  int v2; // ecx
  int v3; // edi

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v2 = this->m_nRefCount - 1;
  this->m_nRefCount = v2;
  v3 = v2;
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return v3;
}

//----- (0040E610) --------------------------------------------------------
void __thiscall CSession::CloseSession(CSession *this)
{
  char *v2; // edi
  int Error; // eax

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v2 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
  Error = WSAGetLastError();
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_DETAIL,
    "CSession::CloseSession",
    aDWorkRylSource_75,
    293,
    "SP:0x%p/DP:0x%p/IP:%15s/ InternalCloseSocket - SessionClose Called : %d",
    this,
    this->m_lpDispatch,
    v2,
    Error);
  CSession::InternalCloseSocket(this);
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
}

//----- (0040E6A0) --------------------------------------------------------
char __thiscall CSession::InternalRecv(CSession *this)
{
  char *v2; // eax
  CBuffer *m_lpRecvBuffer; // eax
  CSessionPolicy *m_SessionPolicy; // ecx
  int v6; // eax
  COverlapped *v7; // edi
  char *v8; // ebp
  char *v9; // ebx
  int Error; // eax
  unsigned int dwFlags; // [esp+8h] [ebp-10h] BYREF
  unsigned int dwReceived; // [esp+Ch] [ebp-Ch] BYREF
  _WSABUF wsaBuf; // [esp+10h] [ebp-8h] BYREF

  if ( this->m_dwRecvPending )
  {
    v2 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::InternalRecv",
      aDWorkRylSource_75,
      396,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ Recv pending now",
      this,
      this->m_lpDispatch,
      v2,
      (const void *)this->m_hSocket);
    return 1;
  }
  m_lpRecvBuffer = this->m_lpRecvBuffer;
  dwReceived = 0;
  dwFlags = 0;
  wsaBuf.buf = m_lpRecvBuffer->wr_ptr_;
  m_SessionPolicy = this->m_SessionPolicy;
  wsaBuf.len = (unsigned int)&m_lpRecvBuffer->internal_buffer_[m_lpRecvBuffer->buffer_size_
                                                             - (unsigned int)m_lpRecvBuffer->wr_ptr_];
  v6 = (int)m_SessionPolicy->m_lpOverlappedFactory->CreateRecv(
              m_SessionPolicy->m_lpOverlappedFactory,
              this,
              m_lpRecvBuffer);
  v7 = (COverlapped *)v6;
  if ( v6 )
  {
    if ( WSARecv(this->m_hSocket, &wsaBuf, 1u, &dwReceived, &dwFlags, (LPWSAOVERLAPPED)(v6 + 4), 0) != -1
      || WSAGetLastError() == 997 )
    {
      goto LABEL_10;
    }
    v8 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/InternalCloseSocket - WSARecv Error - ErrorCode:%d";
  }
  else
  {
    v8 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/InternalCloseSocket - WSARecv Error - Create RecvOverlapped Failed - ErrorCode:%d";
  }
  if ( v8 )
  {
    v9 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::InternalRecv",
      aDWorkRylSource_75,
      433,
      v8,
      this,
      this->m_lpDispatch,
      v9,
      this->m_hSocket,
      Error);
    CSession::InternalCloseSocket(this);
    this->m_SessionPolicy->m_lpOverlappedFactory->DeleteOverlapped(this->m_SessionPolicy->m_lpOverlappedFactory, v7);
    return 0;
  }
LABEL_10:
  ++this->m_dwRecvPending;
  return 1;
}

//----- (0040E7D0) --------------------------------------------------------
char __thiscall CSession::InternalSend(CSession *this, CBuffer *lpBuffer)
{
  CBuffer *v4; // edi
  COverlapped *v5; // ebx
  char *rd_ptr; // eax
  unsigned int m_dwTotalSendPending; // ebp
  unsigned int v8; // edi
  char *v9; // edi
  char *v10; // ebp
  int Error; // eax
  SOCKET m_hSocket; // [esp-24h] [ebp-34h]
  _WSABUF wsaBuf; // [esp+8h] [ebp-8h] BYREF

  if ( this->m_dwSendPending )
    return 1;
  v4 = lpBuffer;
  v5 = 0;
  if ( lpBuffer )
  {
    v5 = this->m_SessionPolicy->m_lpOverlappedFactory->CreateSend(
           this->m_SessionPolicy->m_lpOverlappedFactory,
           this,
           lpBuffer);
    if ( v5 )
    {
      rd_ptr = v4->rd_ptr_;
      m_dwTotalSendPending = this->m_Statistics.m_dwTotalSendPending;
      v8 = v4->wr_ptr_ - rd_ptr;
      wsaBuf.buf = rd_ptr;
      m_hSocket = this->m_hSocket;
      lpBuffer = 0;
      wsaBuf.len = v8;
      this->m_Statistics.m_dwTotalSendPending = v8 + m_dwTotalSendPending;
      if ( WSASend(m_hSocket, &wsaBuf, 1u, (LPDWORD)&lpBuffer, 0, &v5->_OVERLAPPED, 0) != -1 || WSAGetLastError() == 997 )
        goto LABEL_12;
      v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASend Error - ErrorCode:%d";
    }
    else
    {
      v4->bufferfactory_->Release(v4->bufferfactory_, v4);
      v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASend Error - Cannot Create SendOverlapped - ErrorCode:%d";
    }
  }
  else
  {
    v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASend Error - lpBuffer is 0 - ErrorCode:%d";
  }
  if ( v9 )
  {
    v10 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::InternalSend",
      aDWorkRylSource_75,
      558,
      v9,
      this,
      this->m_lpDispatch,
      v10,
      this->m_hSocket,
      Error);
    CSession::InternalCloseSocket(this);
    this->m_SessionPolicy->m_lpOverlappedFactory->DeleteOverlapped(this->m_SessionPolicy->m_lpOverlappedFactory, v5);
    return 0;
  }
LABEL_12:
  ++this->m_dwSendPending;
  return 1;
}

//----- (0040E900) --------------------------------------------------------
char __thiscall CSession::InternalSendTo(CSession *this, CBuffer *lpBuffer)
{
  char *v3; // eax
  CBuffer *v5; // edi
  COverlapped *v6; // ebx
  unsigned int v7; // eax
  unsigned int m_dwTotalSendPending; // ecx
  char *v9; // edi
  char *v10; // ebp
  int Error; // eax
  SOCKET m_hSocket; // [esp-28h] [ebp-38h]
  int m_iAddrLen; // [esp-10h] [ebp-20h]
  _WSABUF wsaBuf; // [esp+8h] [ebp-8h] BYREF

  if ( this->m_dwSendPending )
  {
    v3 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::InternalSendTo",
      aDWorkRylSource_75,
      577,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ SendTo pending now",
      this,
      this->m_lpDispatch,
      v3,
      (const void *)this->m_hSocket);
    return 1;
  }
  v5 = lpBuffer;
  v6 = 0;
  if ( lpBuffer )
  {
    v6 = this->m_SessionPolicy->m_lpOverlappedFactory->CreateSend(
           this->m_SessionPolicy->m_lpOverlappedFactory,
           this,
           lpBuffer);
    if ( v6 )
    {
      v7 = v5->wr_ptr_ - v5->rd_ptr_;
      wsaBuf.buf = v5->rd_ptr_;
      m_dwTotalSendPending = this->m_Statistics.m_dwTotalSendPending;
      wsaBuf.len = v7;
      this->m_Statistics.m_dwTotalSendPending = v7 + m_dwTotalSendPending;
      m_iAddrLen = v5->address_.m_iAddrLen;
      m_hSocket = this->m_hSocket;
      lpBuffer = 0;
      if ( WSASendTo(
             m_hSocket,
             &wsaBuf,
             1u,
             (LPDWORD)&lpBuffer,
             0,
             &v5->address_.m_SockAddr,
             m_iAddrLen,
             &v6->_OVERLAPPED,
             0) != -1
        || WSAGetLastError() == 997 )
      {
        goto LABEL_12;
      }
      v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASendTo Error - ErrorCode:%d";
    }
    else
    {
      v5->bufferfactory_->Release(v5->bufferfactory_, v5);
      v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASendTo Error - Cannot Create SendOverlapped - ErrorCode:%d";
    }
  }
  else
  {
    v9 = "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ InternalCloseSocket - WSASendTo Error - lpBuffer is 0 - ErrorCode:%d";
  }
  if ( v9 )
  {
    v10 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::InternalSendTo",
      aDWorkRylSource_75,
      628,
      v9,
      this,
      this->m_lpDispatch,
      v10,
      this->m_hSocket,
      Error);
    CSession::InternalCloseSocket(this);
    this->m_SessionPolicy->m_lpOverlappedFactory->DeleteOverlapped(this->m_SessionPolicy->m_lpOverlappedFactory, v6);
    return 0;
  }
LABEL_12:
  ++this->m_dwSendPending;
  return 1;
}

//----- (0040EA60) --------------------------------------------------------
char __thiscall CSession::SendPending(CSession *this, CBuffer *lpBuffer)
{
  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  if ( (this->m_cFlags & 4) != 0 || this->m_hSocket == -1 )
  {
    LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
    return 0;
  }
  else
  {
    CBufferQueue::enqueue(&this->m_SendQueue, lpBuffer, 0);
    LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
    return 1;
  }
}

//----- (0040EAE0) --------------------------------------------------------
char __thiscall CSession::InternalSend(CSession *this)
{
  CBuffer *v2; // eax

  if ( this->m_dwSendPending )
    return 1;
  v2 = CBufferQueue::dequeue(&this->m_SendQueue);
  if ( !v2 )
    return 1;
  if ( v2->address_.m_iAddrLen )
    return CSession::InternalSendTo(this, v2);
  return CSession::InternalSend(this, v2);
}

//----- (0040EB20) --------------------------------------------------------
void __thiscall CSession::~CSession(CSession *this)
{
  CBuffer *m_lpRecvBuffer; // eax

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  CSession::InternalCloseSocket(this);
  m_lpRecvBuffer = this->m_lpRecvBuffer;
  if ( m_lpRecvBuffer )
  {
    m_lpRecvBuffer->bufferfactory_->Release(m_lpRecvBuffer->bufferfactory_, this->m_lpRecvBuffer);
    this->m_lpRecvBuffer = 0;
  }
  this->m_SessionPolicy->m_lpDispatchFactory->DeleteDispatch(
    this->m_SessionPolicy->m_lpDispatchFactory,
    this->m_lpDispatch);
  this->m_usPadding = 17476;
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  CSessionPolicy::Release(this->m_SessionPolicy);
  CBufferQueue::~CBufferQueue(&this->m_SendQueue);
  DeleteCriticalSection(&this->m_SessionLock.m_CSLock);
}

//----- (0040EBD0) --------------------------------------------------------
char __thiscall CSession::Process(CSession *this)
{
  char v2; // bl
  CPacketDispatch *m_lpDispatch; // edi
  unsigned __int8 v4; // al
  char *v5; // eax
  unsigned __int8 m_cFlags; // al
  char *v8; // eax
  char *v9; // eax
  char v10; // bl
  char *v11; // eax
  CBuffer *v12; // eax
  SOCKET m_hSocket; // eax
  bool bCallDisconnected; // [esp+Eh] [ebp-16h]
  int m_cCurrentStatus; // [esp+10h] [ebp-14h]

  v2 = 0;
  bCallDisconnected = 0;
  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  m_lpDispatch = this->m_lpDispatch;
  m_cCurrentStatus = this->m_cCurrentStatus;
  if ( this->m_cCurrentStatus )
  {
    if ( this->m_cCurrentStatus == 1 )
    {
      if ( (this->m_cFlags & 2) == 0 )
      {
        bCallDisconnected = 1;
        this->m_Statistics.m_DisconnectedTime = time(0);
        v4 = this->m_cFlags | 2;
LABEL_12:
        this->m_cFlags = v4;
        goto LABEL_13;
      }
      if ( this->m_hSocket == -1 && !this->m_dwRecvPending && !this->m_dwSendPending && !this->m_nRefCount )
      {
        CSession::InternalCloseSocket(this);
        v5 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
        CServerLog::DetailLog(
          &g_SessionLog,
          LOG_DETAIL,
          "CSession::Process",
          aDWorkRylSource_75,
          131,
          "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/Eliminate Session",
          this,
          this->m_lpDispatch,
          v5,
          (const void *)this->m_hSocket);
        LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
        return 0;
      }
    }
  }
  else
  {
    m_cFlags = this->m_cFlags;
    if ( (m_cFlags & 1) == 0 )
    {
      v2 = 1;
      v4 = m_cFlags | 1;
      goto LABEL_12;
    }
  }
LABEL_13:
  ++this->m_nRefCount;
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  if ( v2 )
  {
    v8 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::Process",
      aDWorkRylSource_75,
      148,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/Connection Open - Process connected",
      this,
      this->m_lpDispatch,
      v8,
      (const void *)this->m_hSocket);
    m_lpDispatch->Connected(m_lpDispatch);
    this->m_Statistics.m_ConnectedTime = time(0);
  }
  if ( m_cCurrentStatus || m_lpDispatch->Dispatch(m_lpDispatch) )
  {
    v10 = 0;
  }
  else
  {
    v9 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::Process",
      aDWorkRylSource_75,
      159,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/CloseSocket - Failed Dispatch",
      this,
      this->m_lpDispatch,
      v9,
      (const void *)this->m_hSocket);
    v10 = 1;
  }
  if ( bCallDisconnected )
  {
    v11 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::Process",
      aDWorkRylSource_75,
      168,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/ Connection Closed - Process disconnect",
      this,
      this->m_lpDispatch,
      v11,
      (const void *)this->m_hSocket);
    m_lpDispatch->Disconnected(m_lpDispatch);
  }
  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  if ( !this->m_dwSendPending )
  {
    v12 = CBufferQueue::dequeue(&this->m_SendQueue);
    if ( v12 )
    {
      if ( v12->address_.m_iAddrLen )
        CSession::InternalSendTo(this, v12);
      else
        CSession::InternalSend(this, v12);
    }
  }
  if ( (this->m_cFlags & 4) != 0 && !this->m_SendQueue.m_bufferNum )
  {
    m_hSocket = this->m_hSocket;
    if ( m_hSocket != -1 )
    {
      if ( shutdown(m_hSocket, 1) )
        this->m_cFlags |= 8u;
    }
  }
  --this->m_nRefCount;
  if ( v10 )
    CSession::InternalCloseSocket(this);
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return 1;
}

//----- (0040EE80) --------------------------------------------------------
bool __thiscall CSession::Dispatch(CSession *this, int dwReceivedBytes)
{
  unsigned int v3; // edi
  CBuffer *m_lpRecvBuffer; // ecx
  int v5; // eax
  bool v6; // zf
  bool v7; // bl
  unsigned int v8; // ecx
  char *v9; // ebp
  int Error; // eax
  const char *v12; // [esp+10h] [ebp-14h]

  v3 = dwReceivedBytes;
  this->m_lpRecvBuffer->wr_ptr_ += dwReceivedBytes;
  m_lpRecvBuffer = this->m_lpRecvBuffer;
  v5 = m_lpRecvBuffer->wr_ptr_ - m_lpRecvBuffer->rd_ptr_;
  dwReceivedBytes = v5;
  v7 = 0;
  if ( v3 )
  {
    v6 = !this->m_lpDispatch->ParsePacket(this->m_lpDispatch, m_lpRecvBuffer->rd_ptr_, (unsigned int *)&dwReceivedBytes);
    v5 = dwReceivedBytes;
    if ( !v6 )
      v7 = 1;
  }
  this->m_lpRecvBuffer->rd_ptr_ += v5;
  CBuffer::pop_read_data(this->m_lpRecvBuffer);
  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v8 = this->m_dwRecvPending - 1;
  this->m_Statistics.m_dwTotalReceived += v3;
  this->m_dwRecvPending = v8;
  if ( !v7 )
  {
    v12 = "Disconnected";
    if ( v3 )
      v12 = "Failed To Dispatch Packet";
    v9 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::Dispatch",
      aDWorkRylSource_75,
      224,
      "SP:0x%p/DP:0x%p/IP:%15s/SOCKET:0x%p/shutdown - %s : dwReceivedBytes:%d, ErrorCode:%d",
      this,
      this->m_lpDispatch,
      v9,
      (const void *)this->m_hSocket,
      v12,
      v3,
      Error);
    CSession::InternalCloseSocket(this);
  }
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
  return v7;
}

//----- (0040EFA0) --------------------------------------------------------
void __thiscall CSession::SendCompleted(CSession *this, int bResult, unsigned int dwSendedBytes)
{
  unsigned int v4; // edx
  unsigned int v5; // edi
  char *v6; // edi
  int Error; // eax
  CBuffer *v8; // eax

  EnterCriticalSection(&this->m_SessionLock.m_CSLock);
  v4 = dwSendedBytes + this->m_Statistics.m_dwTotalSendCompleted;
  v5 = this->m_dwSendPending - 1;
  this->m_dwSendPending = v5;
  this->m_Statistics.m_dwTotalSendCompleted = v4;
  if ( bResult )
  {
    if ( !v5 )
    {
      v8 = CBufferQueue::dequeue(&this->m_SendQueue);
      if ( v8 )
      {
        if ( v8->address_.m_iAddrLen )
          CSession::InternalSendTo(this, v8);
        else
          CSession::InternalSend(this, v8);
      }
    }
  }
  else
  {
    v6 = inet_ntoa(*(struct in_addr *)&this->m_RemoteAddr.m_SockAddr.sa_data[2]);
    Error = WSAGetLastError();
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_DETAIL,
      "CSession::SendCompleted",
      aDWorkRylSource_75,
      328,
      "SP:0x%p/DP:0x%p/IP:%15s/ InternalCloseSocket - Send Completion Error : %d",
      this,
      this->m_lpDispatch,
      v6,
      Error);
    CSession::InternalCloseSocket(this);
  }
  LeaveCriticalSection(&this->m_SessionLock.m_CSLock);
}

//----- (0040F080) --------------------------------------------------------
std::ostream *__thiscall std::ostream::flush(std::ostream *this)
{
  char *v2; // eax
  int v3; // edi
  std::ios_base *v4; // ecx
  int v5; // eax

  v2 = &this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
  v3 = 0;
  if ( (v2[8] & 6) == 0 && (*(int (__thiscall **)(_DWORD))(**((_DWORD **)v2 + 10) + 44))(*((_DWORD *)v2 + 10)) == -1 )
    v3 = 4;
  v4 = (std::ios_base *)&this->gap0[*(_DWORD *)(*(_DWORD *)this->gap0 + 4)];
  if ( v3 )
  {
    v5 = v3 | v4->_Mystate;
    if ( !v4[1].__vftable )
      LOBYTE(v5) = v5 | 4;
    std::ios_base::clear(v4, v5, 0);
  }
  return this;
}

//----- (0040F0E0) --------------------------------------------------------
void __thiscall std::ostream::sentry::sentry(std::ostream::sentry *this, std::ostream *_Ostr)
{
  std::_Mutex *v3; // eax
  int v4; // eax
  int v5; // ecx
  char *v6; // eax
  std::ostream *v7; // eax

  this->_Myostr = _Ostr;
  v3 = *(std::_Mutex **)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 40];
  if ( v3 )
    std::_Mutex::_Lock(v3 + 1);
  v4 = *(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4);
  v5 = *(_DWORD *)&_Ostr->gap0[v4 + 8];
  v6 = &_Ostr->gap0[v4];
  if ( !v5 )
  {
    v7 = (std::ostream *)*((_DWORD *)v6 + 11);
    if ( v7 )
      std::ostream::flush(v7);
  }
  this->_Ok = *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 8] == 0;
}

//----- (0040F170) --------------------------------------------------------
void __thiscall std::ostream::sentry::~sentry(std::ostream::sentry *this)
{
  std::_Mutex *v2; // eax

  if ( !std::uncaught_exception() && (this->_Myostr->gap0[*(_DWORD *)(*(_DWORD *)this->_Myostr + 4) + 16] & 2) != 0 )
    std::ostream::flush(this->_Myostr);
  v2 = *(std::_Mutex **)&this->_Myostr->gap0[*(_DWORD *)(*(_DWORD *)this->_Myostr + 4) + 40];
  if ( v2 )
    std::_Mutex::_Unlock(v2 + 1);
}

//----- (0040F1E0) --------------------------------------------------------
std::ostream *__cdecl std::operator<<<std::char_traits<char>>(std::ostream *_Ostr, const char *_Val)
{
  int v2; // ebx
  signed int v3; // edi
  int v4; // eax
  int v5; // edx
  int v6; // eax
  int v7; // edx
  int v8; // eax
  int v9; // ecx
  _DWORD *v10; // edx
  _DWORD *v11; // ecx
  _BYTE *v12; // edx
  int v13; // ecx
  int v14; // edx
  int v15; // eax
  int v16; // ecx
  _DWORD *v17; // edx
  _DWORD *v18; // ecx
  _BYTE *v19; // edx
  int v20; // edx
  std::ios_base *v21; // ecx
  int v22; // eax
  int v24; // [esp+0h] [ebp-2Ch] BYREF
  std::ostream::sentry _Ok; // [esp+Ch] [ebp-20h] BYREF
  int _Pad; // [esp+14h] [ebp-18h]
  int _State; // [esp+18h] [ebp-14h]
  int *v28; // [esp+1Ch] [ebp-10h]
  int v29; // [esp+28h] [ebp-4h]

  v2 = 0;
  v28 = &v24;
  _State = 0;
  v3 = strlen(_Val);
  v4 = *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 24];
  if ( v4 > 0 && v4 > v3 )
    v2 = v4 - v3;
  _Pad = v2;
  std::ostream::sentry::sentry(&_Ok, _Ostr);
  v29 = 0;
  if ( !_Ok._Ok )
  {
    v5 = 4;
    goto LABEL_29;
  }
  v6 = *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 16] & 0x1C0;
  LOBYTE(v29) = 1;
  if ( v6 == 64 )
  {
LABEL_16:
    v13 = *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 40];
    if ( (*(int (__thiscall **)(int, const char *, signed int))(*(_DWORD *)v13 + 28))(v13, _Val, v3) == v3 )
    {
      while ( v2 > 0 )
      {
        v14 = *(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4);
        LOBYTE(v15) = _Ostr->gap0[v14 + 48];
        v16 = *(_DWORD *)&_Ostr->gap0[v14 + 40];
        v17 = *(_DWORD **)(v16 + 36);
        if ( *v17 && *v17 < (unsigned int)(*v17 + **(_DWORD **)(v16 + 52)) )
        {
          --**(_DWORD **)(v16 + 52);
          v18 = *(_DWORD **)(v16 + 36);
          v19 = (_BYTE *)(*v18)++;
          *v19 = v15;
          v15 = (unsigned __int8)v15;
        }
        else
        {
          v15 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v16 + 4))(v16, (unsigned __int8)v15);
        }
        if ( v15 == -1 )
        {
          v20 = *(_DWORD *)_Ostr->gap0;
          _State |= 4u;
          *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(v20 + 4) + 24] = 0;
          goto LABEL_28;
        }
        --v2;
      }
    }
    else
    {
      _State = 4;
    }
    goto LABEL_18;
  }
  while ( v2 > 0 )
  {
    v7 = *(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4);
    LOBYTE(v8) = _Ostr->gap0[v7 + 48];
    v9 = *(_DWORD *)&_Ostr->gap0[v7 + 40];
    v10 = *(_DWORD **)(v9 + 36);
    if ( *v10 )
    {
      if ( *v10 < (unsigned int)(*v10 + **(_DWORD **)(v9 + 52)) )
      {
        --**(_DWORD **)(v9 + 52);
        v11 = *(_DWORD **)(v9 + 36);
        v12 = (_BYTE *)(*v11)++;
        v2 = _Pad;
        *v12 = v8;
        v8 = (unsigned __int8)v8;
        goto LABEL_13;
      }
      v2 = _Pad;
    }
    v8 = (*(int (__thiscall **)(int, _DWORD))(*(_DWORD *)v9 + 4))(v9, (unsigned __int8)v8);
LABEL_13:
    if ( v8 == -1 )
    {
      _State |= 4u;
      break;
    }
    _Pad = --v2;
  }
  if ( !_State )
    goto LABEL_16;
LABEL_18:
  *(_DWORD *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4) + 24] = 0;
LABEL_28:
  v5 = _State;
LABEL_29:
  v21 = (std::ios_base *)&_Ostr->gap0[*(_DWORD *)(*(_DWORD *)_Ostr->gap0 + 4)];
  v29 = 0;
  if ( v5 )
  {
    v22 = v5 | v21->_Mystate;
    if ( !v21[1].__vftable )
      LOBYTE(v22) = v22 | 4;
    std::ios_base::clear(v21, v22, 0);
  }
  v29 = -1;
  std::ostream::sentry::~sentry(&_Ok);
  return _Ostr;
}

//----- (0040F3F0) --------------------------------------------------------
void __thiscall CListener::InternalCloseListen(CListener *this)
{
  int Error; // eax

  if ( this->m_hListen != -1 )
  {
    if ( closesocket(this->m_hListen) == -1 )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_ERROR,
        "CListener::InternalCloseListen",
        aDWorkRylSource_13,
        131,
        "closesocket failed : %d",
        Error);
    }
    this->m_hListen = -1;
  }
}

//----- (0040F440) --------------------------------------------------------
void __thiscall CListener::CListener(
        CListener *this,
        CCompletionHandler *SocketHandler,
        CSessionPolicy *SessionPolicy,
        CSessionMgr *SessionMgr,
        CValidateConnection *lpValidateConnection)
{
  CValidateConnection *m_lpValidateConnection; // eax

  this->__vftable = (CListener_vtbl *)&CListener::`vftable';
  InitializeCriticalSection(&this->m_ListenerLock.m_CSLock);
  this->m_hListen = -1;
  this->m_ListenAddr.m_iAddrLen = 0;
  *(_DWORD *)&this->m_ListenAddr.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->m_ListenAddr.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->m_ListenAddr.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->m_ListenAddr.m_SockAddr.sa_data[10] = 0;
  this->m_SocketHandler = SocketHandler;
  this->m_SessionPolicy = SessionPolicy;
  this->m_SessionMgr = SessionMgr;
  this->m_dwMaxPending = 10;
  this->m_dwCurrentPending = 0;
  this->m_dwTotalPendingCount = 0;
  this->m_dwTotalAcceptCompleteCount = 0;
  this->m_lpValidateConnection = lpValidateConnection;
  CSessionPolicy::AddRef(SessionPolicy);
  m_lpValidateConnection = this->m_lpValidateConnection;
  if ( m_lpValidateConnection )
    InterlockedIncrement(&m_lpValidateConnection->m_nRefCount);
}
// 4D6C04: using guessed type void *CListener::`vftable';

//----- (0040F4F0) --------------------------------------------------------
void __thiscall CListener::WaitForPendingComplete(CListener *this, unsigned int dwTime)
{
  DWORD Time; // ebx
  CCSLock *p_m_ListenerLock; // esi

  Time = timeGetTime();
  if ( dwTime >= abs32(timeGetTime() - Time) )
  {
    p_m_ListenerLock = &this->m_ListenerLock;
    while ( 1 )
    {
      EnterCriticalSection(&p_m_ListenerLock->m_CSLock);
      if ( !this->m_dwCurrentPending )
        break;
      LeaveCriticalSection(&p_m_ListenerLock->m_CSLock);
      Sleep(0xAu);
      if ( dwTime < abs32(timeGetTime() - Time) )
        return;
    }
    LeaveCriticalSection(&p_m_ListenerLock->m_CSLock);
  }
}

//----- (0040F560) --------------------------------------------------------
char __thiscall CListener::PendingAccept(CListener *this)
{
  CCSLock *p_m_ListenerLock; // edi
  unsigned int m_hListen; // ecx
  unsigned int v5; // edi
  CBuffer *v6; // ebx
  COverlapped *v7; // eax
  COverlapped *v8; // ebp
  _OVERLAPPED *v9; // eax
  unsigned int m_dwTotalPendingCount; // ecx
  CCSLock *sync; // [esp+14h] [ebp-14h]
  unsigned int dwBytesReceived[4]; // [esp+18h] [ebp-10h] BYREF

  p_m_ListenerLock = &this->m_ListenerLock;
  sync = &this->m_ListenerLock;
  EnterCriticalSection(&this->m_ListenerLock.m_CSLock);
  m_hListen = this->m_hListen;
  dwBytesReceived[3] = 0;
  if ( m_hListen == -1 )
  {
    LeaveCriticalSection(&p_m_ListenerLock->m_CSLock);
    return 0;
  }
  else
  {
    dwBytesReceived[0] = 0;
    v5 = this->m_SessionPolicy->m_lpSocketFactory->CreateSocket(this->m_SessionPolicy->m_lpSocketFactory);
    v6 = this->m_SessionPolicy->m_lpBufferFactory->Create(this->m_SessionPolicy->m_lpBufferFactory, 64);
    v7 = this->m_SessionPolicy->m_lpOverlappedFactory->CreateAccept(
           this->m_SessionPolicy->m_lpOverlappedFactory,
           this,
           v5,
           v6);
    v8 = v7;
    if ( v7 )
      v9 = &v7->_OVERLAPPED;
    else
      v9 = 0;
    if ( AcceptEx(this->m_hListen, v5, v6->wr_ptr_, 0, 0x20u, 0x20u, dwBytesReceived, v9) != -1
      || WSAGetLastError() == 997 )
    {
      m_dwTotalPendingCount = this->m_dwTotalPendingCount;
      ++this->m_dwCurrentPending;
      this->m_dwTotalPendingCount = m_dwTotalPendingCount + 1;
      LeaveCriticalSection(&sync->m_CSLock);
      return 1;
    }
    else
    {
      this->m_SessionPolicy->m_lpOverlappedFactory->DeleteOverlapped(this->m_SessionPolicy->m_lpOverlappedFactory, v8);
      closesocket(v5);
      LeaveCriticalSection(&sync->m_CSLock);
      return 0;
    }
  }
}

//----- (0040F680) --------------------------------------------------------
void __thiscall CListener::ProcessAccept(
        CListener *this,
        int bResult,
        void *hSocket,
        CBuffer *lpBuffer,
        unsigned int dwProcessedBytes)
{
  int v5; // ebp
  CValidateConnection *m_lpValidateConnection; // ecx
  CSession *v8; // esi
  CSession *Session; // eax
  SOCKET v10; // ebx
  CPacketDispatch *m_lpDispatch; // ebx
  char *v12; // eax
  unsigned int v13; // ecx
  char *rd_ptr; // [esp-20h] [ebp-68h]
  int nRemoteSockAddrLen; // [esp+10h] [ebp-38h] BYREF
  sockaddr *lpLocalAddr; // [esp+14h] [ebp-34h] BYREF
  sockaddr *lpRemoteAddr; // [esp+18h] [ebp-30h] BYREF
  INET_Addr remoteAddr; // [esp+1Ch] [ebp-2Ch] BYREF
  INET_Addr localAddr; // [esp+30h] [ebp-18h] BYREF

  v5 = bResult;
  memset(&localAddr, 0, sizeof(localAddr));
  memset(&remoteAddr, 0, sizeof(remoteAddr));
  if ( bResult )
  {
    if ( setsockopt((SOCKET)hSocket, 0xFFFF, 28683, (const char *)&this->m_hListen, 4) == -1 )
      goto LABEL_9;
    rd_ptr = lpBuffer->rd_ptr_;
    lpLocalAddr = 0;
    lpRemoteAddr = 0;
    bResult = 0;
    nRemoteSockAddrLen = 0;
    GetAcceptExSockaddrs(rd_ptr, 0, 0x20u, 0x20u, &lpLocalAddr, &bResult, &lpRemoteAddr, &nRemoteSockAddrLen);
    if ( lpLocalAddr )
      INET_Addr::set_addr(&localAddr, lpLocalAddr, bResult);
    if ( lpRemoteAddr )
      INET_Addr::set_addr(&remoteAddr, lpRemoteAddr, nRemoteSockAddrLen);
    m_lpValidateConnection = this->m_lpValidateConnection;
    if ( m_lpValidateConnection )
    {
      if ( !m_lpValidateConnection->operator()(m_lpValidateConnection, &localAddr, &remoteAddr) )
LABEL_9:
        v5 = 0;
    }
  }
  v8 = 0;
  if ( !v5
    || (Session = CSessionMgr::CreateSession(this->m_SessionMgr, this->m_SessionPolicy), (v8 = Session) == 0)
    || !Session->m_lpRecvBuffer
    || !Session->m_lpDispatch )
  {
    v10 = (SOCKET)hSocket;
    goto LABEL_17;
  }
  v10 = (SOCKET)hSocket;
  if ( !CreateIoCompletionPort(
          hSocket,
          this->m_SocketHandler->m_hIOCP,
          (ULONG_PTR)Session,
          this->m_SocketHandler->m_nThread) )
  {
LABEL_17:
    CSessionMgr::DeleteSession(this->m_SessionMgr, v8);
    closesocket(v10);
    goto LABEL_18;
  }
  v8->m_hSocket = v10;
  CSession::SetAddress(v8, &remoteAddr, &localAddr);
  m_lpDispatch = v8->m_lpDispatch;
  v12 = inet_ntoa(*(struct in_addr *)&remoteAddr.m_SockAddr.sa_data[2]);
  CServerLog::DetailLog(
    &g_SessionLog,
    LOG_DETAIL,
    "CListener::ProcessAccept",
    aDWorkRylSource_13,
    269,
    "SP:0x%p/DP:0x%p/IP:%15s/ Accept Session Complete.",
    v8,
    m_lpDispatch,
    v12);
  CSession::InternalRecv(v8);
  CSessionMgr::Add(this->m_SessionMgr, v8);
LABEL_18:
  EnterCriticalSection(&this->m_ListenerLock.m_CSLock);
  v13 = this->m_dwTotalAcceptCompleteCount + 1;
  --this->m_dwCurrentPending;
  this->m_dwTotalAcceptCompleteCount = v13;
  LeaveCriticalSection(&this->m_ListenerLock.m_CSLock);
  CListener::PendingAccept(this);
}

//----- (0040F860) --------------------------------------------------------
unsigned int __thiscall CListener::GetPendingAcceptNum(CListener *this)
{
  CCSLock *p_m_ListenerLock; // edi
  unsigned int m_dwCurrentPending; // esi

  p_m_ListenerLock = &this->m_ListenerLock;
  EnterCriticalSection(&this->m_ListenerLock.m_CSLock);
  m_dwCurrentPending = this->m_dwCurrentPending;
  LeaveCriticalSection(&p_m_ListenerLock->m_CSLock);
  return m_dwCurrentPending;
}

//----- (0040F880) --------------------------------------------------------
void __thiscall CListener::~CListener(CListener *this)
{
  CCSLock *p_m_ListenerLock; // edi
  SOCKET m_hListen; // eax
  int Error; // eax
  CValidateConnection *m_lpValidateConnection; // esi

  this->__vftable = (CListener_vtbl *)&CListener::`vftable';
  p_m_ListenerLock = &this->m_ListenerLock;
  EnterCriticalSection(&this->m_ListenerLock.m_CSLock);
  m_hListen = this->m_hListen;
  if ( m_hListen != -1 )
  {
    if ( closesocket(m_hListen) == -1 )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_ERROR,
        "CListener::InternalCloseListen",
        aDWorkRylSource_13,
        131,
        "closesocket failed : %d",
        Error);
    }
    this->m_hListen = -1;
  }
  LeaveCriticalSection(&p_m_ListenerLock->m_CSLock);
  CListener::WaitForPendingComplete(this, 0x1388u);
  CSessionPolicy::Release(this->m_SessionPolicy);
  m_lpValidateConnection = this->m_lpValidateConnection;
  if ( m_lpValidateConnection && !InterlockedDecrement(&m_lpValidateConnection->m_nRefCount) )
    ((void (__thiscall *)(CValidateConnection *, int))m_lpValidateConnection->~CValidateConnection)(
      m_lpValidateConnection,
      1);
  DeleteCriticalSection(&p_m_ListenerLock->m_CSLock);
}
// 40F93D: conditional instruction was optimized away because esi.4!=0
// 4D6C04: using guessed type void *CListener::`vftable';

//----- (0040F960) --------------------------------------------------------
char __thiscall CListener::Initialize(CListener *this, INET_Addr *addrListen, unsigned int dwMaxPending)
{
  CSessionPolicy *m_SessionPolicy; // eax
  char *v5; // ebp
  CSocketFactory *m_lpSocketFactory; // edi
  int v8; // eax
  void *ListenSocket; // eax
  int Error; // eax
  int v11; // eax
  unsigned int m_dwMaxPending; // edi
  CCSLock *sync; // [esp+8h] [ebp-10h]

  m_SessionPolicy = this->m_SessionPolicy;
  v5 = 0;
  if ( m_SessionPolicy->m_lpBufferFactory
    && m_SessionPolicy->m_lpSocketFactory
    && m_SessionPolicy->m_lpDispatchFactory
    && m_SessionPolicy->m_lpOverlappedFactory )
  {
    sync = &this->m_ListenerLock;
    EnterCriticalSection(&this->m_ListenerLock.m_CSLock);
    CListener::InternalCloseListen(this);
    if ( *(_DWORD *)&addrListen->m_SockAddr.sa_data[2] )
      v5 = inet_ntoa(*(struct in_addr *)&addrListen->m_SockAddr.sa_data[2]);
    m_lpSocketFactory = this->m_SessionPolicy->m_lpSocketFactory;
    LOWORD(v8) = ntohs(*(_WORD *)addrListen->m_SockAddr.sa_data);
    ListenSocket = (void *)CSocketFactory::CreateListenSocket(m_lpSocketFactory, v5, v8, 63);
    this->m_hListen = (unsigned int)ListenSocket;
    if ( ListenSocket == (void *)-1 )
    {
      Error = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_ERROR,
        "CListener::Initialize",
        aDWorkRylSource_13,
        94,
        "CListener initialized failed : Create listen socket failed : %d",
        Error);
      LeaveCriticalSection(&sync->m_CSLock);
      return 0;
    }
    else if ( CreateIoCompletionPort(
                ListenSocket,
                this->m_SocketHandler->m_hIOCP,
                (ULONG_PTR)this,
                this->m_SocketHandler->m_nThread) )
    {
      if ( this->m_dwMaxPending < dwMaxPending )
        this->m_dwMaxPending = dwMaxPending;
      m_dwMaxPending = this->m_dwMaxPending;
      LeaveCriticalSection(&sync->m_CSLock);
      for ( ; m_dwMaxPending; --m_dwMaxPending )
        CListener::PendingAccept(this);
      return 1;
    }
    else
    {
      v11 = WSAGetLastError();
      CServerLog::DetailLog(
        &g_SessionLog,
        LOG_ERROR,
        "CListener::Initialize",
        aDWorkRylSource_13,
        103,
        "CListener initialized failed : Attach to Handler failed : %d",
        v11);
      LeaveCriticalSection(&sync->m_CSLock);
      return 0;
    }
  }
  else
  {
    CServerLog::DetailLog(
      &g_SessionLog,
      LOG_ERROR,
      "CListener::Initialize",
      aDWorkRylSource_13,
      76,
      "CListener initialized failed : Invalid SessionPolicy");
    return 0;
  }
}
// 40FA10: variable 'v8' is possibly undefined

//----- (0040FB20) --------------------------------------------------------
CListener *__thiscall CListener::`vector deleting destructor'(CListener *this, char a2)
{
  CListener::~CListener(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (0040FB40) --------------------------------------------------------
void __thiscall CBuffer::CBuffer(CBuffer *this, CBufferFactory *bufferfactory)
{
  this->internal_buffer_ = 0;
  this->rd_ptr_ = 0;
  this->wr_ptr_ = 0;
  this->buffer_size_ = 0;
  this->next_ = 0;
  this->prev_ = 0;
  this->address_.m_iAddrLen = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[10] = 0;
  this->bufferfactory_ = bufferfactory;
}

//----- (0040FB70) --------------------------------------------------------
void __thiscall CBuffer::init(CBuffer *this, char *internal_buffer, unsigned int buffer_size)
{
  this->internal_buffer_ = internal_buffer;
  this->wr_ptr_ = internal_buffer;
  this->rd_ptr_ = internal_buffer;
  this->buffer_size_ = buffer_size;
  this->next_ = 0;
  this->prev_ = 0;
  this->address_.m_iAddrLen = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_family = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[2] = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[6] = 0;
  *(_DWORD *)&this->address_.m_SockAddr.sa_data[10] = 0;
}

//----- (0040FBB0) --------------------------------------------------------
void __thiscall CBuffer::pop_read_data(CBuffer *this)
{
  unsigned __int8 *internal_buffer; // ecx
  char *rd_ptr; // eax
  unsigned int v4; // edi
  char *v5; // eax

  internal_buffer = (unsigned __int8 *)this->internal_buffer_;
  if ( internal_buffer )
  {
    rd_ptr = this->rd_ptr_;
    v4 = this->wr_ptr_ - rd_ptr;
    memmove(internal_buffer, (unsigned __int8 *)rd_ptr, v4);
    v5 = this->internal_buffer_;
    this->wr_ptr_ = &this->internal_buffer_[v4];
    this->rd_ptr_ = v5;
  }
}

//----- (0040FBE0) --------------------------------------------------------
void __thiscall CSessionMgr::DeleteSession(CSessionMgr *this, CSession *lpSession)
{
  CCSLock *p_m_CreationLock; // ebx
  _RTL_CRITICAL_SECTION_DEBUG **m_lpSessionPool; // edi

  p_m_CreationLock = &this->m_CreationLock;
  EnterCriticalSection(&this->m_CreationLock.m_CSLock);
  if ( this->m_lpSessionPool && lpSession )
  {
    CSession::~CSession(lpSession);
    m_lpSessionPool = (_RTL_CRITICAL_SECTION_DEBUG **)this->m_lpSessionPool;
    lpSession->m_SessionLock.m_CSLock.DebugInfo = *m_lpSessionPool;
    *m_lpSessionPool = (_RTL_CRITICAL_SECTION_DEBUG *)lpSession;
  }
  LeaveCriticalSection(&p_m_CreationLock->m_CSLock);
}

//----- (0040FC60) --------------------------------------------------------
void __thiscall CSessionMgr::CSessionMgr(CSessionMgr *this)
{
  boost::pool<boost::default_user_allocator_new_delete> *v2; // eax

  InitializeCriticalSection(&this->m_AddLock.m_CSLock);
  this->m_to_be_added._Myhead = (std::_List_nod<CSession *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_to_be_added);
  this->m_to_be_added._Mysize = 0;
  InitializeCriticalSection(&this->m_ProcessLock.m_CSLock);
  this->m_current._Myhead = (std::_List_nod<CSession *>::_Node *)std::list<CModifyDummyCharacter *>::_Buynode((std::list<CThread *> *)&this->m_current);
  this->m_current._Mysize = 0;
  InitializeCriticalSection(&this->m_CreationLock.m_CSLock);
  v2 = (boost::pool<boost::default_user_allocator_new_delete> *)operator new((tagHeader *)0x14);
  if ( v2 )
  {
    v2->first = 0;
    v2->list.ptr = 0;
    v2->list.sz = 0;
    v2->requested_size = 148;
    v2->next_size = 32;
    this->m_lpSessionPool = v2;
  }
  else
  {
    this->m_lpSessionPool = 0;
  }
}

//----- (0040FD20) --------------------------------------------------------
CSession *__thiscall CSessionMgr::CreateSession(CSessionMgr *this, CSessionPolicy *SessionPolicy)
{
  CCSLock *p_m_CreationLock; // edi
  boost::pool<boost::default_user_allocator_new_delete> *m_lpSessionPool; // ecx
  CSession *first; // eax
  int v6; // eax
  int v7; // esi

  p_m_CreationLock = &this->m_CreationLock;
  EnterCriticalSection(&this->m_CreationLock.m_CSLock);
  m_lpSessionPool = this->m_lpSessionPool;
  first = (CSession *)m_lpSessionPool->first;
  if ( m_lpSessionPool->first )
    m_lpSessionPool->first = first->m_SessionLock.m_CSLock.DebugInfo;
  else
    first = (CSession *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(m_lpSessionPool);
  if ( first )
  {
    CSession::CSession(first, SessionPolicy);
    v7 = v6;
  }
  else
  {
    v7 = 0;
  }
  LeaveCriticalSection(&p_m_CreationLock->m_CSLock);
  return (CSession *)v7;
}
// 40FD86: variable 'v6' is possibly undefined

//----- (0040FDB0) --------------------------------------------------------
void __thiscall std::list<CSession *>::_Incsize(std::list<CSession *> *this, unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 0x3FFFFFFF - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (0040FE50) --------------------------------------------------------
void __thiscall CSessionMgr::Add(CSessionMgr *this, CSession *lpSession)
{
  std::_List_nod<CThread *>::_Node *Myhead; // ebx
  std::_List_nod<CThread *>::_Node *v4; // ebp

  if ( lpSession )
  {
    EnterCriticalSection(&this->m_AddLock.m_CSLock);
    Myhead = (std::_List_nod<CThread *>::_Node *)this->m_to_be_added._Myhead;
    v4 = std::list<IOPCode *>::_Buynode(
           (std::list<CThread *> *)&this->m_to_be_added,
           Myhead,
           Myhead->_Prev,
           (CThread **)&lpSession);
    std::list<CSession *>::_Incsize(&this->m_to_be_added, 1u);
    Myhead->_Prev = v4;
    v4->_Prev->_Next = v4;
    LeaveCriticalSection(&this->m_AddLock.m_CSLock);
  }
}

//----- (0040FED0) --------------------------------------------------------
void __thiscall std::list<CSession *>::_Splice(
        std::list<CSession *> *this,
        std::list<CSession *>::iterator _Where,
        std::list<CSession *> *_Right,
        std::list<CSession *>::iterator _First,
        std::list<CSession *>::iterator _Last,
        unsigned int _Count)
{
  std::_List_nod<CSession *>::_Node *Prev; // esi

  if ( this != _Right )
  {
    std::list<CSession *>::_Incsize(this, _Count);
    _Right->_Mysize -= _Count;
  }
  _First._Ptr->_Prev->_Next = _Last._Ptr;
  _Last._Ptr->_Prev->_Next = _Where._Ptr;
  _Where._Ptr->_Prev->_Next = _First._Ptr;
  Prev = _Where._Ptr->_Prev;
  _Where._Ptr->_Prev = _Last._Ptr->_Prev;
  _Last._Ptr->_Prev = _First._Ptr->_Prev;
  _First._Ptr->_Prev = Prev;
}

//----- (0040FF20) --------------------------------------------------------
void __thiscall CSessionMgr::InternalProcess(CSessionMgr *this)
{
  std::_List_nod<CSession *>::_Node *Myhead; // ebp
  std::_List_nod<CSession *>::_Node *Next; // esi
  std::_List_nod<CSession *>::_Node *v4; // ebp
  CSession *lpSession; // [esp+10h] [ebp-14h]
  CLock<CCSLock>::Syncronize sync; // [esp+14h] [ebp-10h]

  EnterCriticalSection(&this->m_AddLock.m_CSLock);
  if ( &this->m_current != &this->m_to_be_added && this->m_to_be_added._Mysize )
    std::list<CSession *>::_Splice(
      &this->m_current,
      (std::list<CSession *>::iterator)this->m_current._Myhead,
      &this->m_to_be_added,
      (std::list<CSession *>::iterator)this->m_to_be_added._Myhead->_Next,
      (std::list<CSession *>::iterator)this->m_to_be_added._Myhead,
      this->m_to_be_added._Mysize);
  LeaveCriticalSection(&this->m_AddLock.m_CSLock);
  Myhead = this->m_current._Myhead;
  Next = Myhead->_Next;
  sync.m_Lock = (CCSLock *)Myhead;
  while ( Next != Myhead )
  {
    lpSession = Next->_Myval;
    if ( CSession::Process(lpSession) )
    {
      Next = Next->_Next;
    }
    else
    {
      v4 = Next->_Next;
      if ( Next != this->m_current._Myhead )
      {
        Next->_Prev->_Next = v4;
        Next->_Next->_Prev = Next->_Prev;
        operator delete(Next);
        --this->m_current._Mysize;
      }
      Next = v4;
      CSessionMgr::DeleteSession(this, lpSession);
      Myhead = (std::_List_nod<CSession *>::_Node *)sync.m_Lock;
    }
  }
}

//----- (00410000) --------------------------------------------------------
void __thiscall CSessionMgr::Destroy(CSessionMgr *this, unsigned int dwWaitTime)
{
  std::_List_nod<CSession *>::_Node *Myhead; // ebp
  std::_List_nod<CSession *>::_Node *i; // edi
  DWORD Time; // eax
  DWORD v6; // ebp
  CCSLock *process_sync; // [esp+10h] [ebp-14h]

  process_sync = &this->m_ProcessLock;
  EnterCriticalSection(&this->m_ProcessLock.m_CSLock);
  EnterCriticalSection(&this->m_AddLock.m_CSLock);
  if ( &this->m_current != &this->m_to_be_added && this->m_to_be_added._Mysize )
    std::list<CSession *>::_Splice(
      &this->m_current,
      (std::list<CSession *>::iterator)this->m_current._Myhead,
      &this->m_to_be_added,
      (std::list<CSession *>::iterator)this->m_to_be_added._Myhead->_Next,
      (std::list<CSession *>::iterator)this->m_to_be_added._Myhead,
      this->m_to_be_added._Mysize);
  LeaveCriticalSection(&this->m_AddLock.m_CSLock);
  Myhead = this->m_current._Myhead;
  for ( i = Myhead->_Next; i != Myhead; i = i->_Next )
    CSession::CloseSession(i->_Myval);
  Time = timeGetTime();
  if ( this->m_current._Mysize )
  {
    v6 = Time + dwWaitTime;
    do
    {
      if ( timeGetTime() >= v6 )
        break;
      CSessionMgr::InternalProcess(this);
      Sleep(0xAu);
    }
    while ( this->m_current._Mysize );
  }
  LeaveCriticalSection(&process_sync->m_CSLock);
}

//----- (004100F0) --------------------------------------------------------
void __thiscall CSessionMgr::~CSessionMgr(CSessionMgr *this)
{
  boost::pool<boost::default_user_allocator_new_delete> *m_lpSessionPool; // edi

  CSessionMgr::Destroy(this, 0x1388u);
  m_lpSessionPool = this->m_lpSessionPool;
  if ( m_lpSessionPool )
  {
    boost::pool<boost::default_user_allocator_new_delete>::purge_memory(this->m_lpSessionPool);
    operator delete(m_lpSessionPool);
  }
  DeleteCriticalSection(&this->m_CreationLock.m_CSLock);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_current);
  operator delete(this->m_current._Myhead);
  this->m_current._Myhead = 0;
  DeleteCriticalSection(&this->m_ProcessLock.m_CSLock);
  std::list<IOPCode *>::clear((std::list<CThread *> *)&this->m_to_be_added);
  operator delete(this->m_to_be_added._Myhead);
  this->m_to_be_added._Myhead = 0;
  DeleteCriticalSection(&this->m_AddLock.m_CSLock);
}

//----- (004101A0) --------------------------------------------------------
void __thiscall CIOWorker::CIOWorker(CIOWorker *this, CCompletionHandler *SocketHandler)
{
  this->m_hThreadHandle = (void *)-1;
  this->__vftable = (CIOWorker_vtbl *)&CIOWorker::`vftable';
  this->m_SocketHandler = SocketHandler;
}
// 4D6D28: using guessed type void *CIOWorker::`vftable';

//----- (004101C0) --------------------------------------------------------
void __thiscall CIOWorker::~CIOWorker(CIOWorker *this)
{
  this->__vftable = (CIOWorker_vtbl *)&CThread::`vftable';
}
// 500DF0: using guessed type void *CThread::`vftable';

//----- (004101D0) --------------------------------------------------------
unsigned int __thiscall CIOWorker::Run(CIOWorker *this)
{
  BOOL QueuedCompletionStatus; // eax
  _OVERLAPPED *lpOverlappedStruct; // [esp+10h] [ebp-Ch] BYREF
  unsigned int lpSessionKey; // [esp+14h] [ebp-8h] BYREF
  unsigned int dwProcessedBytes; // [esp+18h] [ebp-4h] BYREF

  dwProcessedBytes = 0;
  lpSessionKey = 0;
  lpOverlappedStruct = 0;
  while ( 1 )
  {
    do
      QueuedCompletionStatus = GetQueuedCompletionStatus(
                                 this->m_SocketHandler->m_hIOCP,
                                 &dwProcessedBytes,
                                 &lpSessionKey,
                                 &lpOverlappedStruct,
                                 this->m_SocketHandler->m_nTimeOutMS);
    while ( !QueuedCompletionStatus && !lpOverlappedStruct );
    if ( !lpSessionKey )
      break;
    if ( lpOverlappedStruct )
      (*((void (__thiscall **)(void **, BOOL, unsigned int, unsigned int))lpOverlappedStruct[-1].hEvent + 1))(
        &lpOverlappedStruct[-1].hEvent,
        QueuedCompletionStatus,
        lpSessionKey,
        dwProcessedBytes);
    else
      (*(void (__stdcall **)(BOOL, unsigned int, unsigned int))(MEMORY[0] + 4))(
        QueuedCompletionStatus,
        lpSessionKey,
        dwProcessedBytes);
  }
  return 0;
}

//----- (00410250) --------------------------------------------------------
int __thiscall CIOWorker::End(CIOWorker *this)
{
  PostQueuedCompletionStatus(this->m_SocketHandler->m_hIOCP, 0, 0, 0);
  return 1;
}

//----- (00410270) --------------------------------------------------------
CIOWorker *__thiscall CIOWorker::`scalar deleting destructor'(CIOWorker *this, char a2)
{
  CIOWorker::~CIOWorker(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00410290) --------------------------------------------------------
void __thiscall CCompletionHandler::CCompletionHandler(CCompletionHandler *this)
{
  this->__vftable = (CCompletionHandler_vtbl *)&CCompletionHandler::`vftable';
  this->m_hIOCP = (void *)-1;
  this->m_nThread = 0;
  this->m_nTimeOutMS = 0;
}
// 4D6D34: using guessed type void *CCompletionHandler::`vftable';

//----- (004102B0) --------------------------------------------------------
char __thiscall CCompletionHandler::Initialize(CCompletionHandler *this, DWORD nThread, unsigned int nTimeOutMS)
{
  HANDLE IoCompletionPort; // eax

  if ( this->m_hIOCP != (void *)-1 )
    return 0;
  IoCompletionPort = CreateIoCompletionPort((HANDLE)0xFFFFFFFF, 0, 0, nThread);
  this->m_hIOCP = IoCompletionPort;
  if ( !IoCompletionPort )
    return 0;
  this->m_nThread = nThread;
  this->m_nTimeOutMS = nTimeOutMS;
  return 1;
}

//----- (004102F0) --------------------------------------------------------
CCompletionHandler *__thiscall CCompletionHandler::`scalar deleting destructor'(CCompletionHandler *this, char a2)
{
  void *m_hIOCP; // eax

  m_hIOCP = this->m_hIOCP;
  this->__vftable = (CCompletionHandler_vtbl *)&CCompletionHandler::`vftable';
  if ( m_hIOCP == (void *)-1 || CloseHandle(m_hIOCP) )
    this->m_hIOCP = (void *)-1;
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}
// 4D6D34: using guessed type void *CCompletionHandler::`vftable';

//----- (00410330) --------------------------------------------------------
unsigned int __cdecl Math::HashFunc::sdbmHash(const unsigned __int8 *str)
{
  const unsigned __int8 *v1; // edx
  int v2; // ecx
  unsigned int result; // eax

  v1 = str;
  v2 = *str;
  for ( result = 0; *v1; v2 = *v1 )
  {
    ++v1;
    result = v2 + 65599 * result;
  }
  return result;
}

//----- (00410360) --------------------------------------------------------
void __stdcall __noreturn ATL::AtlThrow(HRESULT hr)
{
  _CxxThrowException(&hr, &_TI1_AVCAtlException_ATL__);
}

//----- (00410380) --------------------------------------------------------
void __thiscall ATL::CComCriticalSection::CComCriticalSection(ATL::CComCriticalSection *this)
{
  this->m_sec.DebugInfo = 0;
  this->m_sec.LockCount = 0;
  this->m_sec.RecursionCount = 0;
  this->m_sec.OwningThread = 0;
  this->m_sec.LockSemaphore = 0;
  this->m_sec.SpinCount = 0;
}

//----- (004103A0) --------------------------------------------------------
HRESULT __thiscall ATL::CComCriticalSection::Init(ATL::CComCriticalSection *this)
{
  InitializeCriticalSection(&this->m_sec);
  return 0;
}

//----- (00410420) --------------------------------------------------------
void __thiscall Position::Position(Position *this)
{
  this->m_fPointX = 0.0;
  this->m_fPointY = 0.0;
  this->m_fPointZ = 0.0;
}

//----- (00410430) --------------------------------------------------------
BOOL __thiscall CCharacter::StillAlive(CCharacter *this)
{
  bool v1; // sf

  v1 = --this->m_nLogoutCount < 0;
  return !v1;
}

//----- (00410440) --------------------------------------------------------
CStatue *__thiscall CMonster::DowncastToStatue(CMonster *this)
{
  CStatue *result; // eax
  unsigned int m_dwKID; // edx

  result = (CStatue *)this;
  m_dwKID = this->m_MonsterInfo.m_dwKID;
  if ( m_dwKID != 1035
    && m_dwKID != 1036
    && m_dwKID != 1037
    && m_dwKID != 1038
    && m_dwKID != 1034
    && m_dwKID != 1040
    && m_dwKID != 1041
    && m_dwKID != 1042
    && m_dwKID != 1043
    && m_dwKID != 1039 )
  {
    return 0;
  }
  return result;
}

//----- (004104A0) --------------------------------------------------------
BOOL __thiscall CVirtualMonsterMgr::IsSummonee(CVirtualMonsterMgr *this, unsigned int dwCID)
{
  return (dwCID & 0xA0000000) == -1610612736;
}

//----- (004104C0) --------------------------------------------------------
unsigned __int8 __thiscall CCreatureManager::IsAttackable(
        CCreatureManager *this,
        unsigned __int8 cAttackerACT,
        unsigned __int8 cDefenderACT)
{
  if ( Castle::CCastleMgr::GetInstance()->m_bIsSiegeTime )
    return CCreatureManager::m_arySiegeTimeAttackable[cAttackerACT][cDefenderACT];
  else
    return CCreatureManager::m_aryNormalAttackable[cAttackerACT][cDefenderACT];
}

//----- (00410500) --------------------------------------------------------
std::bad_alloc *__thiscall std::bad_alloc::`vector deleting destructor'(std::bad_alloc *this, char a2)
{
  std::bad_alloc::~bad_alloc(this);
  if ( (a2 & 1) != 0 )
    operator delete(this);
  return this;
}

//----- (00410520) --------------------------------------------------------
void __thiscall std::bad_alloc::~bad_alloc(std::bad_alloc *this)
{
  this->__vftable = (std::bad_alloc_vtbl *)&std::bad_alloc::`vftable';
  exception::~exception(this);
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00410530) --------------------------------------------------------
std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *__thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(
        std::list<CCreatureManager::BattleGroundRespawnInfo> *this)
{
  std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *result; // eax

  result = (std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *)operator new((tagHeader *)0x1C);
  if ( result )
    result->_Next = result;
  if ( result != (std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *)-4 )
    result->_Prev = result;
  return result;
}

//----- (00410550) --------------------------------------------------------
std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *__thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(
        std::list<CCreatureManager::BattleGroundRespawnInfo> *this,
        std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *_Next,
        std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *_Prev,
        const CCreatureManager::BattleGroundRespawnInfo *_Val)
{
  std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *result; // eax

  result = (std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *)operator new((tagHeader *)0x1C);
  if ( result )
  {
    result->_Next = _Next;
    result->_Prev = _Prev;
    result->_Myval = *_Val;
  }
  return result;
}

//----- (00410590) --------------------------------------------------------
FnLeaveParty __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnLeaveParty>(
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        FnLeaveParty _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // edi
  CMonster *second; // eax
  int v6; // eax

  Ptr = _First._Ptr;
  for ( i = _Last._Ptr; _First._Ptr != i; Ptr = _First._Ptr )
  {
    second = Ptr->_Myval.second;
    if ( second )
    {
      v6 = ((int (*)(void))second->GetParty)();
      if ( v6 )
        (*(void (__thiscall **)(int, unsigned int, _DWORD, _DWORD))(*(_DWORD *)v6 + 8))(
          v6,
          Ptr->_Myval.second->m_dwCID,
          Ptr->_Myval.second->m_CellPos.m_wMapIndex,
          0);
    }
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
  }
  return _Func;
}

//----- (004105F0) --------------------------------------------------------
FnDeleteSecond __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        FnDeleteSecond _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi
  CNPC *second; // ecx

  Ptr = _First._Ptr;
  for ( i = _Last._Ptr; _First._Ptr != i; Ptr = _First._Ptr )
  {
    second = Ptr->_Myval.second;
    if ( second )
      ((void (__thiscall *)(CNPC *, int))second->~CNPC)(second, 1);
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
  }
  return _Func;
}

//----- (00410630) --------------------------------------------------------
std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *__cdecl std::for_each<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>>(
        std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *result,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last,
        std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > _Func)
{
  std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *i; // esi
  std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *v5; // eax

  for ( i = _First._Ptr; i != _Last._Ptr; i = i->_Next )
    _Func.op._Pmemfun(i->_Myval, _Func.value);
  v5 = result;
  *result = _Func;
  return v5;
}

//----- (00410670) --------------------------------------------------------
CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const,CCharacter *>>>(
        CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > *result,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First,
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last,
        CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > _Func)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v5; // edi
  _DWORD *v6; // ecx
  CSendStream *v7; // esi
  char v8; // al
  CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > *v9; // eax

  Ptr = _First._Ptr;
  v5 = _Last._Ptr;
  if ( _First._Ptr == _Last._Ptr )
  {
    v9 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  else
  {
    do
    {
      v6 = &Ptr->_Myval.second->__vftable;
      if ( v6 )
      {
        v7 = (CSendStream *)v6[376];
        if ( v7 )
        {
          v8 = (*(int (__thiscall **)(_DWORD *))(*v6 + 88))(v6);
          GameClientSendPacket::SendCharEliteBonus(v7 + 8, v8);
        }
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&_First);
      Ptr = _First._Ptr;
    }
    while ( _First._Ptr != v5 );
    v9 = result;
    result->m_fnSecondProcess = _Func.m_fnSecondProcess;
  }
  return v9;
}

//----- (004106D0) --------------------------------------------------------
void __thiscall CCreatureManager::DeleteCharacter(CCreatureManager *this, CCharacter *lpCharacter)
{
  ((void (__thiscall *)(CCharacter *, _DWORD))lpCharacter->~CAggresiveCreature)(lpCharacter, 0);
  lpCharacter->__vftable = (CCharacter_vtbl *)this->m_CharacterPool.first;
  this->m_CharacterPool.first = lpCharacter;
}

//----- (004106F0) --------------------------------------------------------
void __thiscall CCreatureManager::SendAllCharacter(
        CCreatureManager *this,
        char *szBuffer,
        unsigned int dwLength,
        unsigned __int8 cCMD_In,
        bool bAll)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  CCharacter *second; // eax
  CSendStream *m_lpGameClientDispatch; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  Myhead = this->m_CharacterMap._Myhead;
  Left = Myhead->_Left;
  for ( pos._Ptr = Myhead->_Left; pos._Ptr != Myhead; Left = pos._Ptr )
  {
    second = Left->_Myval.second;
    if ( second && (bAll || !second->m_CellPos.m_wMapIndex) )
    {
      m_lpGameClientDispatch = (CSendStream *)second->m_lpGameClientDispatch;
      if ( m_lpGameClientDispatch )
        CSendStream::PutBuffer(m_lpGameClientDispatch + 8, szBuffer, dwLength, cCMD_In);
    }
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
  }
}

//----- (00410760) --------------------------------------------------------
void __thiscall CCreatureManager::PopRespawnQueue(CCreatureManager *this, CCharacter *lpCharacter)
{
  unsigned int *p_Mysize; // esi
  int v3; // ebp
  CCharacter ***v4; // ecx
  CCharacter **v5; // eax
  CCharacter *v6; // edi

  p_Mysize = &this->m_lstRespawn[0]._Mysize;
  v3 = 6;
  do
  {
    if ( *p_Mysize )
    {
      v4 = (CCharacter ***)*(p_Mysize - 1);
      v5 = *v4;
      if ( *v4 != (CCharacter **)v4 )
      {
        do
        {
          if ( v5[2] == lpCharacter )
          {
            v6 = *v5;
            if ( v5 != (CCharacter **)*(p_Mysize - 1) )
            {
              v5[1]->__vftable = (CCharacter_vtbl *)v6;
              LODWORD((*v5)->m_CurrentPos.m_fPointX) = v5[1];
              operator delete(v5);
              --*p_Mysize;
            }
            v5 = (CCharacter **)v6;
          }
          else
          {
            v5 = (CCharacter **)*v5;
          }
        }
        while ( v5 != (CCharacter **)*(p_Mysize - 1) );
      }
    }
    p_Mysize += 3;
    --v3;
  }
  while ( v3 );
}

//----- (004107C0) --------------------------------------------------------
void __thiscall CCreatureManager::ProcessRespawnQueue(CCreatureManager *this)
{
  unsigned __int16 *p_Mysize; // ebp
  void **v2; // eax
  void *v3; // esi
  CCharacter *v4; // ebx
  int v5; // edi
  int v6; // edi
  void *v7; // edi
  CSendStream *m_lpGameClientDispatch; // ecx
  unsigned __int16 wTurn; // [esp+10h] [ebp-20h]
  int v10; // [esp+14h] [ebp-1Ch]
  Position tempInfo_4; // [esp+20h] [ebp-10h]

  p_Mysize = (unsigned __int16 *)&this->m_lstRespawn[0]._Mysize;
  v10 = 6;
  do
  {
    if ( *(_DWORD *)p_Mysize )
    {
      v2 = (void **)*((_DWORD *)p_Mysize - 1);
      v3 = *v2;
      wTurn = 0;
      if ( *v2 != v2 )
      {
        do
        {
          v4 = (CCharacter *)*((_DWORD *)v3 + 2);
          v5 = *((_DWORD *)v3 + 6);
          tempInfo_4.m_fPointX = *((float *)v3 + 3);
          *(_QWORD *)&tempInfo_4.m_fPointY = *((_QWORD *)v3 + 2);
          v6 = this->m_dwLastUpdateTime - CPulse::GetInstance()->m_dwLastTick + v5;
          if ( v6 > 0 )
          {
            ++wTurn;
            m_lpGameClientDispatch = (CSendStream *)v4->m_lpGameClientDispatch;
            if ( m_lpGameClientDispatch )
              GameClientSendPacket::SendCharBattleGroundRespawn(
                m_lpGameClientDispatch + 8,
                v4->m_dwCID,
                wTurn,
                *p_Mysize,
                v6 / 1000,
                this->m_wCharacterNum[0],
                this->m_wCharacterNum[1],
                0);
            v3 = *(void **)v3;
          }
          else
          {
            CCharacter::Respawn(v4, tempInfo_4, 1);
            v7 = *(void **)v3;
            if ( v3 != *((void **)p_Mysize - 1) )
            {
              **((_DWORD **)v3 + 1) = v7;
              *(_DWORD *)(*(_DWORD *)v3 + 4) = *((_DWORD *)v3 + 1);
              operator delete(v3);
              --*(_DWORD *)p_Mysize;
            }
            v3 = v7;
          }
        }
        while ( v3 != *((void **)p_Mysize - 1) );
      }
    }
    p_Mysize += 6;
    --v10;
  }
  while ( v10 );
}

//----- (004108F0) --------------------------------------------------------
unsigned __int8 __thiscall CCreatureManager::GetBonusTurn(CCreatureManager *this, unsigned __int16 wMapIndex)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  bool v4; // zf
  char v5; // bl
  CMonster *second; // ecx
  CStatue *v7; // eax
  CStatue *v8; // ecx
  __int16 m_dwKID; // ax
  unsigned __int8 result; // al
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+Ch] [ebp-4h] BYREF

  Myhead = this->m_MonsterMap._Myhead;
  Left = Myhead->_Left;
  v4 = Myhead->_Left == Myhead;
  v5 = 0x80;
  pos._Ptr = Myhead->_Left;
  if ( v4 )
    return 2;
  do
  {
    second = Left->_Myval.second;
    if ( second )
    {
      if ( second->m_CellPos.m_wMapIndex == wMapIndex )
      {
        v7 = CMonster::DowncastToStatue(second);
        v8 = v7;
        if ( v7 )
        {
          if ( v7->m_CreatureStatus.m_nNowHP )
          {
            m_dwKID = v7->m_MonsterInfo.m_dwKID;
            if ( m_dwKID != 1036 && m_dwKID != 1038 )
              return 2;
            if ( v5 == (char)0x80 )
            {
              v5 = v8->GetNation(v8);
            }
            else if ( v5 != v8->GetNation(v8) )
            {
              return 2;
            }
          }
        }
      }
    }
    std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&pos);
    Left = pos._Ptr;
  }
  while ( pos._Ptr != Myhead );
  if ( v5 == 1 )
    return 0;
  result = 1;
  if ( v5 != 2 )
    return 2;
  return result;
}

//----- (00410990) --------------------------------------------------------
void __thiscall CCreatureManager::ProcessBattleGround(CCreatureManager *this)
{
  CServerSetup *Instance; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Left; // eax
  CStatue *second; // ecx
  unsigned int m_dwKID; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v7; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v8; // esi
  CPulse *v9; // eax
  unsigned int m_dwLastTick; // ebp
  int v11; // edi
  CCharacter *v12; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator mon_pos; // [esp+4h] [ebp-4h] BYREF

  Instance = CServerSetup::GetInstance();
  if ( (unsigned __int8)CServerSetup::GetServerZone(Instance) == 8 )
  {
    Myhead = this->m_MonsterMap._Myhead;
    Left = Myhead->_Left;
    for ( mon_pos._Ptr = Myhead->_Left; mon_pos._Ptr != Myhead; Left = mon_pos._Ptr )
    {
      second = (CStatue *)Left->_Myval.second;
      m_dwKID = second->m_MonsterInfo.m_dwKID;
      if ( (m_dwKID == 1035
         || m_dwKID == 1036
         || m_dwKID == 1037
         || m_dwKID == 1038
         || m_dwKID == 1034
         || m_dwKID == 1040
         || m_dwKID == 1041
         || m_dwKID == 1042
         || m_dwKID == 1043
         || m_dwKID == 1039)
        && second->m_CreatureStatus.m_nNowHP )
      {
        CStatue::GiveMileage(second);
      }
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc((std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *)&mon_pos);
    }
    v7 = this->m_CharacterMap._Myhead;
    v8 = v7->_Left;
    v9 = CPulse::GetInstance();
    m_dwLastTick = v9->m_dwLastTick;
    v11 = 150 * v9->m_nTicksPerPulse;
    while ( v8 != v7 )
    {
      v12 = v8->_Myval.second;
      if ( v12 && !v12->m_CreatureStatus.m_nNowHP && v11 + v12->m_dwLastTime < m_dwLastTick )
        CCharacter::AutoRespawn(v12);
      if ( !v8->_Isnil )
      {
        Right = v8->_Right;
        if ( Right->_Isnil )
        {
          for ( i = v8->_Parent; !i->_Isnil; i = i->_Parent )
          {
            if ( v8 != i->_Right )
              break;
            v8 = i;
          }
          v8 = i;
        }
        else
        {
          v8 = v8->_Right;
          for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
            v8 = j;
        }
      }
    }
  }
}

//----- (00410AD0) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::find(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        const unsigned __int16 *_Keyval)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // edx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Parent; // eax
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v5; // eax

  Myhead = this->_Myhead;
  Parent = Myhead->_Parent;
  while ( !Parent->_Isnil )
  {
    if ( Parent->_Myval.first >= *_Keyval )
    {
      Myhead = Parent;
      Parent = Parent->_Left;
    }
    else
    {
      Parent = Parent->_Right;
    }
  }
  if ( Myhead == this->_Myhead || *_Keyval < Myhead->_Myval.first )
  {
    v5 = result;
    result->_Ptr = this->_Myhead;
  }
  else
  {
    v5 = result;
    result->_Ptr = Myhead;
  }
  return v5;
}

//----- (00410B40) --------------------------------------------------------
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::partition<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::mem_fun_t<bool,CCharacter>>(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First,
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last,
        std::mem_fun_t<bool,CCharacter> _Pred)
{
  std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *v6; // eax
  CCharacter *Myval; // eax

LABEL_1:
  if ( _First._Ptr != _Last._Ptr )
  {
    while ( _Pred._Pmemfun(_First._Ptr->_Myval) )
    {
      _First._Ptr = _First._Ptr->_Next;
      if ( _First._Ptr == _Last._Ptr )
        goto LABEL_4;
    }
    if ( _First._Ptr != _Last._Ptr )
    {
      while ( 1 )
      {
        _Last._Ptr = _Last._Ptr->_Prev;
        if ( _First._Ptr == _Last._Ptr )
          break;
        if ( _Pred._Pmemfun(_Last._Ptr->_Myval) )
        {
          Myval = _First._Ptr->_Myval;
          _First._Ptr->_Myval = _Last._Ptr->_Myval;
          _Last._Ptr->_Myval = Myval;
          _First._Ptr = _First._Ptr->_Next;
          goto LABEL_1;
        }
      }
    }
  }
LABEL_4:
  v6 = result;
  result->_Ptr = _First._Ptr;
  return v6;
}

//----- (00410BA0) --------------------------------------------------------
CCharacter *__thiscall CCreatureManager::GetCharacter(CCreatureManager *this, unsigned int dwCID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CharacterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
    &dwCID);
  if ( pos._Ptr == this->m_CharacterMap._Myhead )
    return 0;
  else
    return pos._Ptr->_Myval.second;
}

//----- (00410BD0) --------------------------------------------------------
CCharacter *__thiscall CCreatureManager::GetCharacter(CCreatureManager *this, char *szCharacterName)
{
  unsigned __int8 *v2; // ebx
  int v3; // eax
  char *v4; // edx
  const char *i; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Ptr; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v7; // ebp
  CCharacter *second; // edi
  int v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *k; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *j; // eax
  std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator> findpair; // [esp+10h] [ebp-8h] BYREF

  v2 = (unsigned __int8 *)szCharacterName;
  v3 = (unsigned __int8)*szCharacterName;
  v4 = 0;
  for ( i = szCharacterName; *i; v3 = *(unsigned __int8 *)i )
  {
    ++i;
    v4 = (char *)(v3 + 65599 * (_DWORD)v4);
  }
  szCharacterName = v4;
  std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(
    (std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *)&this->m_CharacterNameMap,
    (std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *)&findpair,
    (const unsigned int *)&szCharacterName);
  Ptr = findpair.first._Ptr;
  v7 = findpair.second._Ptr;
  if ( findpair.first._Ptr == findpair.second._Ptr )
    return 0;
  while ( 1 )
  {
    second = Ptr->_Myval.second;
    strncmp((unsigned __int8 *)second->m_DBData.m_Info.Name, v2, 0x10u);
    if ( !v9 )
      break;
    if ( !Ptr->_Isnil )
    {
      Right = Ptr->_Right;
      if ( Right->_Isnil )
      {
        for ( j = Ptr->_Parent; !j->_Isnil; j = j->_Parent )
        {
          if ( Ptr != j->_Right )
            break;
          Ptr = j;
        }
        Ptr = j;
      }
      else
      {
        Ptr = Ptr->_Right;
        for ( k = Right->_Left; !k->_Isnil; k = k->_Left )
          Ptr = k;
      }
    }
    if ( Ptr == v7 )
      return 0;
  }
  return second;
}
// 410C37: variable 'v9' is possibly undefined

//----- (00410CA0) --------------------------------------------------------
CMonster *__thiscall CCreatureManager::GetMonster(CCreatureManager *this, unsigned int dwCID)
{
  std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator pos; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MonsterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&pos,
    &dwCID);
  if ( pos._Ptr == this->m_MonsterMap._Myhead )
    return 0;
  else
    return pos._Ptr->_Myval.second;
}

//----- (00410CD0) --------------------------------------------------------
void __thiscall CCreatureManager::SetEliteBonus(CCreatureManager *this, EliteBonus::EliteBonusData eliteBonus)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // ecx
  CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > result; // [esp+0h] [ebp-4h] BYREF

  result.m_fnSecondProcess = (CSendEliteBonus *)this;
  if ( this->m_EliteBonus.m_cNation != eliteBonus.m_cNation || this->m_EliteBonus.m_cLevel != eliteBonus.m_cLevel )
  {
    this->m_EliteBonus = eliteBonus;
    Myhead = this->m_CharacterMap._Myhead;
    eliteBonus.m_cNation = 0;
    std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const,CCharacter *>>>(
      &result,
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead->_Left,
      (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead,
      (CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> >)&eliteBonus);
  }
}

//----- (00410D10) --------------------------------------------------------
char __thiscall CCreatureManager::SendRespawnQueue(CCreatureManager *this, unsigned int dwCID)
{
  unsigned int v2; // ebp
  CMsgProc *second; // edi
  CSendStream *v5; // eax
  CSendStream *v7; // ebx
  char *Buffer; // eax
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+Ch] [ebp-4h] BYREF

  v2 = dwCID;
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CharacterMap,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
    &dwCID);
  if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_CharacterMap._Myhead )
    return 0;
  second = result._Ptr->_Myval.second;
  if ( !second )
    return 0;
  CCreatureManager::PopRespawnQueue(this, (CCharacter *)result._Ptr->_Myval.second);
  v5 = (CSendStream *)second[376].__vftable;
  if ( !v5 )
    return 0;
  v7 = v5 + 8;
  Buffer = CSendStream::GetBuffer(v5 + 8, (char *)0x16);
  if ( !Buffer )
    return 0;
  *((_DWORD *)Buffer + 3) = v2;
  if ( HIBYTE(second[305].__vftable) )
  {
    if ( HIBYTE(second[305].__vftable) != 1 )
      return 0;
    *((_WORD *)Buffer + 8) = this->m_lstRespawn[3]._Mysize;
    *((_WORD *)Buffer + 9) = this->m_lstRespawn[4]._Mysize;
    *((_WORD *)Buffer + 10) = this->m_lstRespawn[5]._Mysize;
  }
  else
  {
    *((_WORD *)Buffer + 8) = this->m_lstRespawn[0]._Mysize;
    *((_WORD *)Buffer + 9) = this->m_lstRespawn[1]._Mysize;
    *((_WORD *)Buffer + 10) = this->m_lstRespawn[2]._Mysize;
  }
  return CSendStream::WrapCrypt(v7, 0x16u, 0x84u, 0, 0);
}

//----- (00410DF0) --------------------------------------------------------
void __thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::list<CCreatureManager::BattleGroundRespawnInfo>(
        std::list<CCreatureManager::BattleGroundRespawnInfo> *this)
{
  this->_Myhead = std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(this);
  this->_Mysize = 0;
}

//----- (00410E10) --------------------------------------------------------
void __thiscall boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(
        boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *this)
{
  boost::pool<boost::default_user_allocator_new_delete>::purge_memory(&this->p);
  DeleteCriticalSection(&this->mtx);
}

//----- (00410E30) --------------------------------------------------------
CMsgProc *__thiscall CCreatureManager::GetAggresiveCreature(CCreatureManager *this, signed int dwCID)
{
  unsigned __int8 v3; // al
  int v4; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *Myhead; // ecx
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  if ( dwCID >= 0 )
  {
    if ( (dwCID & 0x40000000) != 0 )
      v3 = 1;
    else
      v3 = (dwCID & 0x10000000) != 0 ? 5 : 0;
  }
  else
  {
    v3 = 2;
  }
  if ( v3 )
  {
    v4 = v3 - 2;
    if ( v4 )
    {
      if ( v4 != 3 )
        return 0;
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_SiegeObjectMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
        (const unsigned int *)&dwCID);
      Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_SiegeObjectMap._Myhead;
    }
    else
    {
      std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_MonsterMap,
        (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
        (const unsigned int *)&dwCID);
      Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_MonsterMap._Myhead;
    }
  }
  else
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
      (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)&this->m_CharacterMap,
      (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
      (const unsigned int *)&dwCID);
    Myhead = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_CharacterMap._Myhead;
  }
  if ( result._Ptr != Myhead )
    return result._Ptr->_Myval.second;
  return 0;
}

//----- (00410EE0) --------------------------------------------------------
void __thiscall CCreatureManager::CreateCharacter(CCreatureManager *this, unsigned int dwCID)
{
  boost::pool<boost::default_user_allocator_new_delete> *p_m_CharacterPool; // ecx
  CCharacter *first; // eax

  p_m_CharacterPool = &this->m_CharacterPool;
  first = (CCharacter *)p_m_CharacterPool->first;
  if ( p_m_CharacterPool->first )
    p_m_CharacterPool->first = first->__vftable;
  else
    first = (CCharacter *)boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_m_CharacterPool);
  if ( first )
    CCharacter::CCharacter(first, dwCID);
}

//----- (00410F50) --------------------------------------------------------
void __thiscall CCreatureManager::CalculateEliteBonus(CCreatureManager *this, float usPeopleNum)
{
  __int16 v3; // ax
  unsigned __int16 v4; // cx
  unsigned __int16 v5; // dx
  unsigned __int16 v6; // ax
  char v7; // cl
  unsigned __int8 m_cLevel; // al
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // [esp-8h] [ebp-14h]
  EliteBonus::EliteBonusData NewEliteBonus; // [esp+4h] [ebp-8h] BYREF
  CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > result; // [esp+8h] [ebp-4h] BYREF

  if ( this->m_bAutoBalance )
  {
    EliteBonus::EliteBonusData::EliteBonusData(&NewEliteBonus);
    v3 = *(_WORD *)LODWORD(usPeopleNum);
    v4 = *(_WORD *)(LODWORD(usPeopleNum) + 2);
    if ( *(_WORD *)LODWORD(usPeopleNum) >= v4 )
    {
      v5 = v4 - 1;
      v6 = v3 - 1;
      v7 = 1;
    }
    else
    {
      v5 = v3 - 1;
      v6 = v4 - 1;
      v7 = 0;
    }
    NewEliteBonus.m_cNation = v7;
    if ( v5 )
    {
      usPeopleNum = (double)v6 / (double)v5;
      if ( usPeopleNum < 1.5 || usPeopleNum >= 2.0 )
      {
        if ( usPeopleNum < 2.0 || usPeopleNum >= 2.5 )
        {
          if ( usPeopleNum < 2.5 || usPeopleNum >= 3.0 )
          {
            if ( usPeopleNum < 3.0 || usPeopleNum >= 3.5 )
            {
              if ( usPeopleNum < 3.5 )
              {
                m_cLevel = NewEliteBonus.m_cLevel;
                if ( !NewEliteBonus.m_cLevel )
                {
                  v7 = 2;
                  NewEliteBonus.m_cNation = 2;
                }
              }
              else
              {
                m_cLevel = 5;
                NewEliteBonus.m_cLevel = 5;
              }
            }
            else
            {
              m_cLevel = 4;
              NewEliteBonus.m_cLevel = 4;
            }
          }
          else
          {
            m_cLevel = 3;
            NewEliteBonus.m_cLevel = 3;
          }
        }
        else
        {
          m_cLevel = 2;
          NewEliteBonus.m_cLevel = 2;
        }
      }
      else
      {
        m_cLevel = 1;
        NewEliteBonus.m_cLevel = 1;
      }
    }
    else
    {
      m_cLevel = 10;
      NewEliteBonus.m_cLevel = 10;
    }
    if ( this->m_EliteBonus.m_cNation != v7 || this->m_EliteBonus.m_cLevel != m_cLevel )
    {
      this->m_EliteBonus.m_cLevel = m_cLevel;
      LOBYTE(usPeopleNum) = 0;
      Myhead = this->m_CharacterMap._Myhead;
      this->m_EliteBonus.m_cNation = v7;
      std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const,CCharacter *>>>(
        &result,
        (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead->_Left,
        (std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator)Myhead,
        (CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> >)&usPeopleNum);
    }
  }
}

//----- (004110D0) --------------------------------------------------------
CMsgProc *__thiscall CCreatureManager::GetCreature(CCreatureManager *this, signed int dwCID)
{
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator result; // [esp+4h] [ebp-4h] BYREF

  if ( dwCID < 0 || (dwCID & 0x40000000) == 0 )
    return CCreatureManager::GetAggresiveCreature(this, dwCID);
  std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(
    (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *)this,
    (std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *)&result,
    (const unsigned int *)&dwCID);
  if ( result._Ptr == (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)this->m_NPCMap._Myhead )
    return 0;
  else
    return result._Ptr->_Myval.second;
}

//----- (00411120) --------------------------------------------------------
void __thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(
        std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this,
        unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( -1 - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004111C0) --------------------------------------------------------
void __thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Incsize(
        std::list<CCreatureManager::BattleGroundRespawnInfo> *this,
        unsigned int _Count)
{
  unsigned int Mysize; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v5; // [esp+50h] [ebp-4h]

  Mysize = this->_Mysize;
  if ( 214748364 - Mysize < _Count )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v5 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  this->_Mysize = _Count + Mysize;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (00411260) --------------------------------------------------------
boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance()
{
  if ( (__S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
  {
    __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
    InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 24;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
    atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
  }
  return &`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj;
}
// 51FA98: using guessed type int __S7__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BI_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (004112C0) --------------------------------------------------------
boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance()
{
  if ( (__S11__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA & 1) == 0 )
  {
    __S11__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA |= 1u;
    InitializeCriticalSection(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.mtx);
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.first = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.ptr = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.list.sz = 0;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.requested_size = 20;
    `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj.p.next_size = 32;
    atexit(`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
  }
  return &`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj;
}
// 51FAC8: using guessed type int __S11__1__instance___singleton_default_Upool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__boost___pool_details_boost__SAAAUpool_type___singleton_pool_Ufast_pool_allocator_tag_boost___0BE_Udefault_user_allocator_new_delete_2_Vwin32_mutex_pool_details_2__0CA__5_XZ_4IA;

//----- (00411320) --------------------------------------------------------
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(
        void **ptr)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // esi

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  *ptr = v1->p.first;
  v1->p.first = ptr;
  LeaveCriticalSection(&v1->mtx);
}

//----- (00411350) --------------------------------------------------------
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(
        void **ptr)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // esi

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  *ptr = v1->p.first;
  v1->p.first = ptr;
  LeaveCriticalSection(&v1->mtx);
}

//----- (00411380) --------------------------------------------------------
void __thiscall std::bad_alloc::bad_alloc(std::bad_alloc *this, const std::bad_alloc *__that)
{
  exception::exception(this, __that);
  this->__vftable = (std::bad_alloc_vtbl *)&std::bad_alloc::`vftable';
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (004113A0) --------------------------------------------------------
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(
        void **ptr)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // esi

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  *ptr = v1->p.first;
  v1->p.first = ptr;
  LeaveCriticalSection(&v1->mtx);
}

//----- (004113D0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Larg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Parg,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rarg,
        const std::pair<unsigned long const ,CTempCharacter *> *_Val,
        const char *_Carg)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v6; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  CTempCharacter *second; // edx
  exception pExceptionObject; // [esp+8h] [ebp-Ch] BYREF

  v6 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v6->mtx);
  first = (void **)v6->p.first;
  p_p = &v6->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v6->mtx);
  if ( !first )
  {
    _Carg = "bad allocation";
    exception::exception(&pExceptionObject, &_Carg);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = _Larg;
  first[1] = _Parg;
  first[2] = _Rarg;
  first[3] = (void *)_Val->first;
  second = _Val->second;
  *((_BYTE *)first + 20) = (_BYTE)_Carg;
  first[4] = second;
  *((_BYTE *)first + 21) = 0;
  return (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (00411470) --------------------------------------------------------
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result,
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Ptr; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Right; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v6; // ecx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Parent; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *Myhead; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v9; // ebx
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *v10; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v11; // ebx
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v12; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v13; // eax
  char Color; // al
  std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *Left; // eax
  bool v16; // zf
  boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v17; // esi
  unsigned int Mysize; // eax
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *v19; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Erasednode; // [esp+8h] [ebp-54h]
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( _Where._Ptr->_Isnil )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  Ptr = _Where._Ptr;
  _Erasednode = _Where._Ptr;
  std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::const_iterator::_Inc((std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::const_iterator *)&_Where);
  if ( Ptr->_Left->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Right;
LABEL_8:
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Myhead = this->_Myhead;
    if ( Myhead->_Parent == Ptr )
    {
      Myhead->_Parent = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Right;
    }
    else if ( (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Parent->_Left == Ptr )
    {
      Parent->_Left = Right;
    }
    else
    {
      Parent->_Right = Right;
    }
    v9 = this->_Myhead;
    if ( v9->_Left == _Erasednode )
    {
      if ( Right->_Isnil )
        v10 = Parent;
      else
        v10 = std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Min(Right);
      v9->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v10;
    }
    v11 = this->_Myhead;
    if ( v11->_Right == _Erasednode )
    {
      if ( Right->_Isnil )
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)Parent;
      else
        v11->_Right = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Max(Right);
    }
    goto LABEL_35;
  }
  if ( Ptr->_Right->_Isnil )
  {
    Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)Ptr->_Left;
    goto LABEL_8;
  }
  v6 = _Where._Ptr;
  Right = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)_Where._Ptr->_Right;
  if ( _Where._Ptr == Ptr )
    goto LABEL_8;
  Ptr->_Left->_Parent = _Where._Ptr;
  v6->_Left = Ptr->_Left;
  if ( v6 == Ptr->_Right )
  {
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6;
  }
  else
  {
    Parent = (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)v6->_Parent;
    if ( !Right->_Isnil )
      Right->_Parent = Parent;
    Parent->_Left = Right;
    v6->_Right = Ptr->_Right;
    Ptr->_Right->_Parent = v6;
  }
  v12 = this->_Myhead;
  if ( v12->_Parent == Ptr )
  {
    v12->_Parent = v6;
  }
  else
  {
    v13 = Ptr->_Parent;
    if ( v13->_Left == Ptr )
      v13->_Left = v6;
    else
      v13->_Right = v6;
  }
  v6->_Parent = Ptr->_Parent;
  Color = v6->_Color;
  v6->_Color = Ptr->_Color;
  Ptr->_Color = Color;
LABEL_35:
  if ( _Erasednode->_Color == 1 )
  {
    if ( Right != (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)this->_Myhead->_Parent )
    {
      do
      {
        if ( Right->_Color != 1 )
          break;
        Left = Parent->_Left;
        if ( Right == Parent->_Left )
        {
          Left = Parent->_Right;
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            Left = Parent->_Right;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Left->_Color != 1 || Left->_Right->_Color != 1 )
          {
            if ( Left->_Right->_Color == 1 )
            {
              Left->_Left->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
                (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
                Left);
              Left = Parent->_Right;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Right->_Color = 1;
            std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            break;
          }
        }
        else
        {
          if ( !Left->_Color )
          {
            Left->_Color = 1;
            Parent->_Color = 0;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            Left = Parent->_Left;
          }
          if ( Left->_Isnil )
            goto LABEL_53;
          if ( Left->_Right->_Color != 1 || Left->_Left->_Color != 1 )
          {
            if ( Left->_Left->_Color == 1 )
            {
              Left->_Right->_Color = 1;
              Left->_Color = 0;
              std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Lrotate(
                (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
                Left);
              Left = Parent->_Left;
            }
            Left->_Color = Parent->_Color;
            Parent->_Color = 1;
            Left->_Left->_Color = 1;
            std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Rrotate(
              (std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> > *)this,
              Parent);
            break;
          }
        }
        Left->_Color = 0;
LABEL_53:
        Right = Parent;
        v16 = Parent == (std::_Tree_nod<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Node *)this->_Myhead->_Parent;
        Parent = Parent->_Parent;
      }
      while ( !v16 );
    }
    Right->_Color = 1;
  }
  v17 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v17->mtx);
  _Erasednode->_Left = (std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *)v17->p.first;
  v17->p.first = _Erasednode;
  LeaveCriticalSection(&v17->mtx);
  Mysize = this->_Mysize;
  if ( Mysize )
    this->_Mysize = Mysize - 1;
  v19 = result;
  result->_Ptr = _Where._Ptr;
  return v19;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (00411740) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode(
        std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this)
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *v1; // edi
  void **first; // esi
  boost::pool<boost::default_user_allocator_new_delete> *p_p; // ecx
  char *what; // [esp+8h] [ebp-10h] BYREF
  exception pExceptionObject; // [esp+Ch] [ebp-Ch] BYREF

  v1 = boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance();
  EnterCriticalSection(&v1->mtx);
  first = (void **)v1->p.first;
  p_p = &v1->p;
  if ( first )
    p_p->first = *first;
  else
    first = boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(p_p);
  LeaveCriticalSection(&v1->mtx);
  if ( !first )
  {
    what = "bad allocation";
    exception::exception(&pExceptionObject, (const char **)&what);
    pExceptionObject.__vftable = (exception_vtbl *)&std::bad_alloc::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI2_AVbad_alloc_std__);
  }
  *first = 0;
  if ( first != (void **)-4 )
    first[1] = 0;
  if ( first != (void **)-8 )
    first[2] = 0;
  *((_BYTE *)first + 20) = 1;
  *((_BYTE *)first + 21) = 0;
  return (std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *)first;
}
// 4FCAD8: using guessed type void *std::bad_alloc::`vftable';

//----- (004117E0) --------------------------------------------------------
void __thiscall CCreatureManager::PushRespawnQueue(
        CCreatureManager *this,
        CCharacter *lpCharacter,
        unsigned __int8 cPointNumber)
{
  CServerSetup *Instance; // eax
  CCharacter *v5; // ebx
  double v6; // st7
  double v7; // st6
  int v8; // eax
  unsigned int *p_Mysize; // ecx
  unsigned __int16 v10; // ax
  unsigned __int16 v11; // cx
  int v12; // eax
  Position *v13; // ecx
  double m_fPointX; // st6
  double m_fPointY; // st7
  int v16; // edx
  std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *v17; // ebp
  std::list<CCreatureManager::BattleGroundRespawnInfo> *v18; // edi
  std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *v19; // ebx
  CSendStream *m_lpGameClientDispatch; // edi
  unsigned __int16 Mysize; // [esp-18h] [ebp-58h]
  unsigned __int16 wError; // [esp+10h] [ebp-30h]
  int lTime; // [esp+14h] [ebp-2Ch]
  int v24; // [esp+18h] [ebp-28h]
  CCreatureManager::BattleGroundRespawnInfo tempInfo; // [esp+2Ch] [ebp-14h] BYREF

  wError = 0;
  lTime = 0;
  Instance = CServerSetup::GetInstance();
  v5 = lpCharacter;
  v24 = this->m_wCharacterNum[0];
  if ( this->m_wCharacterNum[1] + v24 <= Instance->m_wBattleLimit )
    goto LABEL_9;
  v6 = (double)v24;
  v7 = (double)this->m_wCharacterNum[1];
  if ( v6 > 1.5 * v7 && !lpCharacter->m_DBData.m_Info.Nationality )
    wError = 2;
  if ( v7 > v6 * 1.5 && lpCharacter->m_DBData.m_Info.Nationality == 1 )
  {
    wError = 2;
    goto LABEL_22;
  }
  if ( !wError )
  {
LABEL_9:
    v8 = 0;
    p_Mysize = &this->m_lstRespawn[0]._Mysize;
    do
    {
      if ( *p_Mysize )
        break;
      ++v8;
      p_Mysize += 3;
    }
    while ( v8 < 6 );
    if ( v8 == 6 )
      this->m_dwLastUpdateTime = CPulse::GetInstance()->m_dwLastTick;
    CCreatureManager::PopRespawnQueue(this, lpCharacter);
    v10 = this->m_wCharacterNum[0];
    if ( !v10 )
      goto LABEL_21;
    v11 = this->m_wCharacterNum[1];
    if ( !v11 )
      goto LABEL_21;
    if ( lpCharacter->m_DBData.m_Info.Nationality )
    {
      if ( lpCharacter->m_DBData.m_Info.Nationality != 1 )
      {
LABEL_21:
        v13 = &this->m_RespawnPoint[cPointNumber];
        tempInfo.m_lpCharacter = lpCharacter;
        m_fPointX = v13->m_fPointX;
        m_fPointY = v13->m_fPointY;
        tempInfo.m_RespawnPos.m_fPointZ = v13->m_fPointZ;
        tempInfo.m_RespawnPos.m_fPointX = m_fPointX;
        v16 = 3 * cPointNumber + 27;
        v17 = (std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *)*((_DWORD *)&this->m_NPCMap._Myhead
                                                                                  + v16);
        tempInfo.m_RespawnPos.m_fPointY = m_fPointY;
        v18 = (std::list<CCreatureManager::BattleGroundRespawnInfo> *)(&this->m_NPCMap.std::_Tree_val<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >
                                                                     + v16);
        tempInfo.m_lLeftTime = lTime;
        v19 = std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(v18, v17, v17->_Prev, &tempInfo);
        std::list<CCreatureManager::BattleGroundRespawnInfo>::_Incsize(v18, 1u);
        v17->_Prev = v19;
        v19->_Prev->_Next = v19;
        v5 = lpCharacter;
        goto LABEL_22;
      }
      v12 = v11 / (int)v10;
    }
    else
    {
      v12 = v10 / (int)v11;
    }
    lTime = 5000 * v12;
    goto LABEL_21;
  }
LABEL_22:
  m_lpGameClientDispatch = (CSendStream *)v5->m_lpGameClientDispatch;
  if ( m_lpGameClientDispatch )
  {
    Mysize = this->m_lstRespawn[cPointNumber]._Mysize;
    GameClientSendPacket::SendCharBattleGroundRespawn(
      m_lpGameClientDispatch + 8,
      v5->m_dwCID,
      Mysize,
      Mysize,
      lTime / 1000,
      this->m_wCharacterNum[0],
      this->m_wCharacterNum[1],
      wError);
  }
}

//----- (004119C0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00411A00) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00411A40) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00411A80) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00411AC0) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

//----- (00411B00) --------------------------------------------------------
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
        std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this,
        std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *v2; // edi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *i; // esi

  v2 = _Rootnode;
  for ( i = _Rootnode; !i->_Isnil; v2 = i )
  {
    std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(
      this,
      i->_Right);
    i = i->_Left;
    boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free((void **)&v2->_Left);
  }
}

