# FameConfig Plugin

This plugin provides static fame and medal values for party members in RYL1 game server.

## Features

- **Static Fame Values**: Configure fixed fame values for each party member (1-10)
- **Static Medal Values**: Configure fixed medal values for each party member (1-10)
- **Enable/Disable**: Turn the system on or off with a simple setting
- **Real-time Configuration**: Changes to config file are applied without server restart
- **Logging**: Detailed logs of fame/medal adjustments

## Configuration

Edit `cfg_FameConfig.ini` to adjust fame and medal settings:

```ini
[SETTING]
enable=1


[FAME_CONFIG]
FAME_MEMBER_1= 50
FAME_MEMBER_2= 50
FAME_MEMBER_3= 50
FAME_MEMBER_4= 50
FAME_MEMBER_5= 50
FAME_MEMBER_6= 50
FAME_MEMBER_7= 50
FAME_MEMBER_8= 50
FAME_MEMBER_9= 50
FAME_MEMBER_10= 50


[MEDAL_CONFIG]
MEDAL_MEMBER_1= 10
MEDAL_MEMBER_2= 10
MEDAL_MEMBER_3= 10
MEDAL_MEMBER_4= 10
MEDAL_MEMBER_5= 10
MEDAL_MEMBER_6= 10
MEDAL_MEMBER_7= 10
MEDAL_MEMBER_8= 10
MEDAL_MEMBER_9= 10
MEDAL_MEMBER_10= 10
```

### Settings

- **enable**: Enable or disable the fame/medal system (default: 1)
  - `1` = System enabled
  - `0` = System disabled

- **FAME_MEMBER_X**: Fame value for party member X (1-10)
  - Values can be set individually for each party member
  - Default value is 50

- **MEDAL_MEMBER_X**: Medal value for party member X (1-10)
  - Values can be set individually for each party member
  - Default value is 10

## Usage Examples

### Default Configuration
```ini
[SETTING]
enable=1

[FAME_CONFIG]
FAME_MEMBER_1= 50
...

[MEDAL_CONFIG]
MEDAL_MEMBER_1= 10
...
```

### Custom Fame Values
```ini
[SETTING]
enable=1

[FAME_CONFIG]
FAME_MEMBER_1= 100
FAME_MEMBER_2= 90
FAME_MEMBER_3= 80
...
```

### Custom Medal Values
```ini
[SETTING]
enable=1

[MEDAL_CONFIG]
MEDAL_MEMBER_1= 20
MEDAL_MEMBER_2= 15
MEDAL_MEMBER_3= 10
...
```

### Disable System
```ini
[SETTING]
enable=0
```

## Logs

The plugin creates detailed logs in the `logs/` directory:

- `fameconfig.txt`: Configuration changes and plugin events
- `fameconfig_debug.txt`: Detailed fame/medal value logs

## Installation

1. Copy `FameConfig.dll` to your server directory
2. Ensure `cfg_FameConfig.ini` is in the same directory
3. The plugin will automatically create the config file if it doesn't exist

## Important Notes

- Configuration changes are applied automatically without server restart
- The plugin requires the correct memory addresses for the fame/medal functions
- Make sure to update the hook addresses in the code for your specific game server version

## Finding Function Addresses

To use this plugin, you'll need to find the correct memory addresses for the party member fame and medal functions in your game server. Here's how to find them:

1. Use a debugger or disassembler (like IDA Pro, Ghidra, or x64dbg) to analyze your RylGameServer.exe
2. Look for functions related to:
   - Party member data processing
   - Fame/medal calculations
   - Functions that send party member information to clients
3. Typical function names might be:
   - `CParty::GetMemberFame`
   - `CParty::GetMemberMedal`
   - `GameClientSendPacket::SendPartyMemberData`
4. Once found, update the addresses in the FameConfig.cpp file:
   ```cpp
   oGetPartyFame = (tGetPartyFame)0x00XXXXXX; // Replace with actual address
   DetourAttach(&(PVOID&)oGetPartyFame, Hook_GetPartyFame);
   
   oGetPartyMedal = (tGetPartyMedal)0x00XXXXXX; // Replace with actual address
   DetourAttach(&(PVOID&)oGetPartyMedal, Hook_GetPartyMedal);
   ```
5. Compile the plugin and test it with your server 