/* This file was generated by the Hex-Rays decompiler version 9.0.0.240807.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: Visual C++
*/

#include <windows.h>
#include <math.h>
#include <defs.h>

#include <stdarg.h>


//-------------------------------------------------------------------------
// Function declarations

#define __thiscall __cdecl // Test compile in C mode

void __stdcall `vector constructor iterator'(char *__t, unsigned int __s, int __n, void *(__thiscall *__f)(void *));
CConsoleCommand *__thiscall CCMDAutoBalance::Clone(CCMDAutoBalance *this, const char *szCommand, unsigned int nCommandLength); // idb
char __thiscall CCMDAutoBalance::DoProcess(CCMDAutoBalance *this);
CConsoleCommand *__thiscall CCMDDropItem::Clone(CCMDDropItem *this, const char *szCommand, unsigned int nCommandLength); // idb
char __thiscall CCMDDropItem::DoProcess(CCMDDropItem *this);
boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance(); // idb
void __thiscall boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *this); // idb
void __thiscall boost::pool<boost::default_user_allocator_new_delete>::~pool<boost::default_user_allocator_new_delete>(boost::pool<boost::default_user_allocator_new_delete> *this); // idb
char __thiscall boost::pool<boost::default_user_allocator_new_delete>::purge_memory(boost::pool<boost::default_user_allocator_new_delete> *this);
char __thiscall CCMDNotify::Destroy(CCMDDropItemList *this);
CConsoleCommand *__thiscall CCMDDropItemList::Clone(CCMDDropItemList *this, const char *szCommand, unsigned int nCommandLength); // idb
char __thiscall CCMDDropItemList::DoProcess(CCMDDropItemList *this);
void __thiscall std::logic_error::logic_error(std::logic_error *this, const std::string *_Message); // idb
std::string::_Bxty *__thiscall std::logic_error::what(std::logic_error *this);
void __thiscall std::length_error::~length_error(std::length_error *this); // idb
std::length_error *__thiscall std::out_of_range::`vector deleting destructor'(std::length_error *this, char a2);
void __thiscall SKILL::SKILL(SKILL *this); // idb
void __thiscall QUICK::QUICK(QUICK *this); // idb
CParty *__thiscall CSymbolTable::GetStringBufferSize(CAggresiveCreature *this); // idb
char __thiscall CCharacter::GetConsumeMPCount(CCharacter *this); // idb
unsigned __int16 __thiscall CCharacter::GetClass(CCharacter *this); // idb
unsigned int __thiscall CCharacter::GetGID(CCharacter *this); // idb
unsigned int __thiscall CCharacter::GetFame(CCharacter *this); // idb
CCharacter *__thiscall CCharacter::GetDuelOpponent(CCharacter *this);
void __thiscall CModifyDummyCharacter::~CModifyDummyCharacter(CModifyDummyCharacter *this); // idb
CModifyDummyCharacter *__thiscall CModifyDummyCharacter::`vector deleting destructor'(CModifyDummyCharacter *this, char a2);
// void __usercall CModifyDummyCharacter::ModifyEquipmentData(CModifyDummyCharacter *this@<eax>, CDummyCharacterList::EquipmentData *equipmentData@<ebx>);
CDummyCharacterList *__fastcall CDummyCharacterList::GetInstance(CDummyCharacterList *a1);
void __thiscall CDummyCharacterList::CDummyCharacterList(CDummyCharacterList *this, CDummyCharacterList *thisa);
// void __usercall std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>>::~map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>>(std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>> *this@<ecx>, int a2@<eax>);
void __thiscall CDummyCharacterList::~CDummyCharacterList(CDummyCharacterList *this, CDummyCharacterList *thisa);
char __thiscall CDummyCharacterList::Initialize(CDummyCharacterList *this, CDummyCharacterList *thisa);
// void __usercall std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>::~pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>(std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *this@<ecx>, int a2@<esi>);
char __thiscall CDummyCharacterList::Destroy(CDummyCharacterList *this, CDummyCharacterList *thisa);
char __cdecl CDummyCharacterList::LoadEquipments(const char *szFileName, std::vector<CDummyCharacterList::EquipmentData> *equipmentVector);
char __cdecl CDummyCharacterList::LoadDummyChars(const char *szFileName, std::map<unsigned long,std::vector<CDummyCharacterList::EquipmentData>> *equipmentSet, std::list<CModifyDummyCharacter *> *dummyCharList);
void __thiscall std::string::string(std::string *this, const std::string *_Right); // idb
void __thiscall std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>(std::pair<std::string const ,unsigned char> *this); // idb
// std::pair<std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator,bool> *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::insert@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ecx>, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *a2@<eax>, int a3@<edi>, const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *result, const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val);
void __thiscall std::vector<CDummyCharacterList::EquipmentData>::vector<CDummyCharacterList::EquipmentData>(std::vector<CDummyCharacterList::EquipmentData> *this, const std::vector<CDummyCharacterList::EquipmentData> *_Right); // idb
// void __usercall std::vector<CDummyCharacterList::EquipmentData>::~vector<CDummyCharacterList::EquipmentData>(std::vector<CDummyCharacterList::EquipmentData> *this@<ecx>, int a2@<esi>);
void __thiscall std::out_of_range::out_of_range(std::out_of_range *this, const std::out_of_range *__that); // idb
void __thiscall std::logic_error::logic_error(std::logic_error *this, const std::logic_error *__that); // idb
void __thiscall std::string::string(std::string *this, const char *_Ptr); // idb
std::string *__thiscall std::string::assign(std::string *this, const std::string *_Right, unsigned int _Roff, unsigned int _Count); // idb
void __thiscall std::string::_Tidy(std::string *this, bool _Built, unsigned int _Newsize); // idb
// void __usercall std::list<CModifyDummyCharacter *>::_Tidy(std::list<CModifyDummyCharacter *> *this@<ecx>, int a2@<esi>);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Rootnode); // idb
// std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Insert@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ebx>, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Wherenode@<ecx>, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node **_Addleft, const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val, const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Vala);
void __thiscall std::length_error::length_error(std::length_error *this, const std::length_error *__that); // idb
std::string *__thiscall std::string::assign(std::string *this, const char *_Ptr); // idb
std::string *__thiscall std::string::erase(std::string *this, unsigned int _Off, unsigned int _Count); // idb
BOOL __thiscall std::string::_Grow(std::string *this, unsigned int _Newsize, bool _Trim);
void __fastcall std::list<CModifyDummyCharacter *>::_Incsize(std::list<CModifyDummyCharacter *> *this, int a2);
// std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this@<ecx>, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *a2@<edi>, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Last);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Lrotate(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *_Wherenode);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Rrotate(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *_Wherenode);
// std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__userpurge std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Buynode@<eax>(const std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > *_Val@<ecx>, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Rarg, char _Carg);
void __fastcall std::vector<CDummyCharacterList::EquipmentData>::_Insert_n(int a1, const CDummyCharacterList::EquipmentData *_Val, std::vector<CDummyCharacterList::EquipmentData> *this, std::vector<CDummyCharacterList::EquipmentData>::iterator _Where, unsigned int _Count);
void __thiscall __noreturn std::vector<CDummyCharacterList::EquipmentData>::_Xlen(std::vector<CDummyCharacterList::EquipmentData> *this);
std::string *__thiscall std::string::assign(std::string *this, const char *_Ptr, unsigned int _Num); // idb
void __thiscall std::string::_Copy(std::string *this, unsigned int _Newsize, unsigned int _Oldlen); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> > *result, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Where, std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::iterator _Wherea);
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this); // idb
void __fastcall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Dec(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *this, int a2);
// std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__usercall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Max@<eax>(std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Pnode@<eax>); idb
// std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *__usercall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Min@<eax>(std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *_Pnode@<eax>); idb
void __fastcall std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::const_iterator *this, int *a2);
// std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *__usercall std::make_pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData>>@<eax>(int a1@<esi>, std::pair<enum CClass::JobType,std::vector<CDummyCharacterList::EquipmentData> > *result, CClass::JobType _Val1, std::vector<CDummyCharacterList::EquipmentData> _Val2);
// void __usercall std::fill<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData>(CDummyCharacterList::EquipmentData *_First@<eax>, CDummyCharacterList::EquipmentData *_Last@<edx>, const CDummyCharacterList::EquipmentData *_Val@<ebx>);
// CDummyCharacterList::EquipmentData *__usercall std::copy_backward<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *>@<eax>(CDummyCharacterList::EquipmentData *_First@<ebx>, CDummyCharacterList::EquipmentData *_Last@<edx>, CDummyCharacterList::EquipmentData *_Dest@<eax>); idb
// void __usercall std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,std::vector<CDummyCharacterList::EquipmentData>>>,0>>::_Node::~_Node(std::_Tree_nod<std::_Tmap_traits<unsigned long,std::vector<CDummyCharacterList::EquipmentData>,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,std::vector<CDummyCharacterList::EquipmentData> > >,0> >::_Node *this@<ecx>, int a2@<esi>);
// void __usercall std::_Uninit_fill_n<CDummyCharacterList::EquipmentData *,unsigned int,CDummyCharacterList::EquipmentData,std::allocator<CDummyCharacterList::EquipmentData>>(CDummyCharacterList::EquipmentData *_First@<eax>, unsigned int _Count@<ecx>, const CDummyCharacterList::EquipmentData *_Val@<ebx>);
// CDummyCharacterList::EquipmentData *__usercall std::_Uninit_copy<std::vector<CDummyCharacterList::EquipmentData>::const_iterator,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>@<eax>(CDummyCharacterList::EquipmentData *_Dest@<eax>, std::vector<CDummyCharacterList::EquipmentData>::const_iterator _First, std::vector<CDummyCharacterList::EquipmentData>::const_iterator _Last);
// CDummyCharacterList::EquipmentData *__usercall std::_Uninit_copy<CDummyCharacterList::EquipmentData *,CDummyCharacterList::EquipmentData *,std::allocator<CDummyCharacterList::EquipmentData>>@<eax>(CDummyCharacterList::EquipmentData *_First@<edx>, CDummyCharacterList::EquipmentData *_Last@<ebx>, CDummyCharacterList::EquipmentData *_Dest@<eax>);
CGameClientDispatchTable *__thiscall CGameClientDispatchTable::`scalar deleting destructor'(CGameClientDispatchTable *this, char a2);
CFieldGameClientDispatchTable *__cdecl CFieldGameClientDispatchTable::GetInstance(); // idb
// void __usercall CFieldGameClientDispatchTable::~CFieldGameClientDispatchTable(CFieldGameClientDispatchTable *this@<ecx>, _DWORD *a2@<eax>);
CFieldGameClientDispatchTable *__thiscall CFieldGameClientDispatchTable::`scalar deleting destructor'(CFieldGameClientDispatchTable *this, char a2);
int __thiscall CFieldGameClientDispatchTable::Initialize(CFieldGameClientDispatchTable *this);
CFieldGameClientDispatch *__thiscall CFieldGameClientDispatch::`vector deleting destructor'(CFieldGameClientDispatch *this, char a2);
void __thiscall CFieldGameClientDispatch::~CFieldGameClientDispatch(CFieldGameClientDispatch *this); // idb
CConsoleCommand *__thiscall CCMDLotteryEvent::Clone(CCMDLotteryEvent *this, const char *szCommand, unsigned int nCommandLength); // idb
char __thiscall CCMDLotteryEvent::DoProcess(CCMDLotteryEvent *this);
void __cdecl PrePerformanceMsg(_iobuf *lpFile); // idb
char __thiscall CRylGameServer::InitializeGameObject(CRylGameServer *this, CRylGameServer *thisa);
CConsoleCommand *__thiscall CCMDNotify::Clone(CCMDNotify *this, const char *szCommand, unsigned int nCommandLength); // idb
char __thiscall CCMDNotify::DoProcess(CCMDNotify *this);
CSingleDispatch *__cdecl CLogDispatch::GetDispatchTable(); // idb
void __thiscall CCommandProcess::Add(CCommandProcess *this, CCommand *lpNewCMD); // idb
CRylGameServer *__fastcall CRylGameServer::GetInstance(CRylGameServer *a1);
void __thiscall CRylGameServer::CRylGameServer(CRylGameServer *this, CRylGameServer *thisa);
CRylGameServer *__thiscall CRylGameServer::`vector deleting destructor'(CRylGameServer *this, char a2);
void __thiscall CRylGameServer::~CRylGameServer(CRylGameServer *this, CRylGameServer *thisa);
char __thiscall CRylGameServer::ApplicationSpecificInit(CRylGameServer *this, const char *szCmdLine);
// void __usercall CRylGameServer::StartServer(CRylGameServer *this@<ecx>, CRylGameServer *a2@<esi>);
void __thiscall CRylGameServer::ConnectToRegularAgent(CRylGameServer *this, CRylGameServer *thisa);
void __thiscall CRylGameServer::ConnectToAgent(CRylGameServer *this, CRylGameServer *thisa);
void __thiscall CRylGameServer::ConnectToChatServer(CRylGameServer *this, CRylGameServer *thisa);
// void __usercall CRylGameServer::PrintStatistics(CRylGameServer *this@<ecx>, CServerWindowFramework *a2@<edi>);
void __thiscall CRylGameServer::PrintServerInfo(CRylGameServer *this, CServerWindowFramework *a2);
void __thiscall CRylGameServer::ReloadSetup(CRylGameServer *this, CRylGameServer *thisa);
void __thiscall CLock<CCSLock>::Syncronize::Syncronize(CLock<CCSLock>::Syncronize *this, CCSLock *LockClass); // idb
void __thiscall CLock<CCSLock>::Syncronize::~Syncronize(CLock<CCSLock>::Syncronize *this); // idb
std::_List_nod<CThread *>::_Node *__thiscall std::list<IOPCode *>::_Buynode(std::list<CThread *> *this, std::_List_nod<CThread *>::_Node *_Next, std::_List_nod<CThread *>::_Node *_Prev, CThread **_Val);
void __thiscall std::list<CCommand *>::_Incsize(std::list<CCommand *> *this, unsigned int _Count); // idb
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CFieldGameClientDispatch>,CStreamOverlappedFactory>();
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CRegularAgentDispatch>,CStreamOverlappedFactory>();
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CDBAgentDispatch>,CStreamOverlappedFactory>();
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CChatDispatch>,CStreamOverlappedFactory>();
CSessionPolicy *CSessionPolicy::Create<CTCPFactory,CPoolBufferFactory,CPoolDispatchFactory<CLogDispatch>,CStreamOverlappedFactory>();
void __thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::~CPoolDispatchFactory<CFieldGameClientDispatch>(CPoolDispatchFactory<CFieldGameClientDispatch> *this); // idb
CGameClientDispatch *__thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::CreateDispatch(CPoolDispatchFactory<CFieldGameClientDispatch> *this, CSession *Session);
void __thiscall CPoolDispatchFactory<CRegularAgentDispatch>::~CPoolDispatchFactory<CRegularAgentDispatch>(CPoolDispatchFactory<CRegularAgentDispatch> *this); // idb
void __thiscall CPoolDispatchFactory<CRegularAgentDispatch>::CreateDispatch(CPoolDispatchFactory<CRegularAgentDispatch> *this, CSession *Session);
void __thiscall CPoolDispatchFactory<CDBAgentDispatch>::~CPoolDispatchFactory<CDBAgentDispatch>(CPoolDispatchFactory<CDBAgentDispatch> *this); // idb
void __thiscall CPoolDispatchFactory<CDBAgentDispatch>::CreateDispatch(CPoolDispatchFactory<CDBAgentDispatch> *this, CSession *Session);
void __thiscall CPoolDispatchFactory<CChatDispatch>::~CPoolDispatchFactory<CChatDispatch>(CPoolDispatchFactory<CChatDispatch> *this); // idb
void __thiscall CPoolDispatchFactory<CChatDispatch>::CreateDispatch(CPoolDispatchFactory<CChatDispatch> *this, CSession *Session);
void __thiscall CPoolDispatchFactory<CLogDispatch>::~CPoolDispatchFactory<CLogDispatch>(CPoolDispatchFactory<CLogDispatch> *this); // idb
void __thiscall CPoolDispatchFactory<CLogDispatch>::CreateDispatch(CPoolDispatchFactory<CLogDispatch> *this, CSession *Session);
void __thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::DeleteDispatch(CPoolDispatchFactory<CFieldGameClientDispatch> *this, CPacketDispatch *lpDispatch); // idb
CPoolDispatchFactory<CFieldGameClientDispatch> *__thiscall CPoolDispatchFactory<CFieldGameClientDispatch>::`vector deleting destructor'(CPoolDispatchFactory<CFieldGameClientDispatch> *this, char a2);
CPoolDispatchFactory<CRegularAgentDispatch> *__thiscall CPoolDispatchFactory<CRegularAgentDispatch>::`vector deleting destructor'(CPoolDispatchFactory<CRegularAgentDispatch> *this, char a2);
CPoolDispatchFactory<CDBAgentDispatch> *__thiscall CPoolDispatchFactory<CDBAgentDispatch>::`scalar deleting destructor'(CPoolDispatchFactory<CDBAgentDispatch> *this, char a2);
CPoolDispatchFactory<CChatDispatch> *__thiscall CPoolDispatchFactory<CChatDispatch>::`vector deleting destructor'(CPoolDispatchFactory<CChatDispatch> *this, char a2);
CPoolDispatchFactory<CLogDispatch> *__thiscall CPoolDispatchFactory<CLogDispatch>::`vector deleting destructor'(CPoolDispatchFactory<CLogDispatch> *this, char a2);
void **__thiscall boost::pool<boost::default_user_allocator_new_delete>::malloc_need_resize(boost::pool<boost::default_user_allocator_new_delete> *this);
void __thiscall boost::simple_segregated_storage<unsigned int>::add_block(boost::simple_segregated_storage<unsigned int> *this, boost::simple_segregated_storage<unsigned int> *block, unsigned int nsz, unsigned int npartition_sz);
char __thiscall CCMDStatClear::DoProcess(CCMDStatClear *this);
char __thiscall CCMDStatLog::DoProcess(CCMDStatLog *this);
char __thiscall CCMDStartServer::DoProcess(CCMDStartServer *this);
char __thiscall CCMDConnect::DoProcess(CCMDConnect *this);
char __thiscall CCMDShowStatistics::DoProcess(CCMDShowStatistics *this);
char __thiscall CCMDPrintLog::DoProcess(CCMDPrintLog *this);
char __thiscall CCMDFlushLog::DoProcess(CCMDFlushLog *this);
char __thiscall CCMDReloadSetup::DoProcess(CCMDReloadSetup *this);
char __thiscall CCMDDummyCharacters::DoProcess(CCMDDummyCharacters *this);
char __thiscall CCMDClearDummyCharacters::DoProcess(CCMDClearDummyCharacters *this);
// char __usercall CRylGameServer::InitializeCommand@<al>(CRylGameServer *this@<ecx>, int a2@<esi>);
CCMDStatClear *__thiscall CConsoleCMDSingleton<CCMDStatClear>::Clone(CConsoleCMDSingleton<CCMDStatClear> *this, const char *szCommand, unsigned int nCommandLength);
CCMDStatLog *__thiscall CConsoleCMDSingleton<CCMDStatLog>::Clone(CConsoleCMDSingleton<CCMDStatLog> *this, const char *szCommand, unsigned int nCommandLength);
CCMDStartServer *__thiscall CConsoleCMDSingleton<CCMDStartServer>::Clone(CConsoleCMDSingleton<CCMDStartServer> *this, const char *szCommand, unsigned int nCommandLength);
CCMDConnect *__thiscall CConsoleCMDSingleton<CCMDConnect>::Clone(CConsoleCMDSingleton<CCMDConnect> *this, const char *szCommand, unsigned int nCommandLength);
CCMDShowStatistics *__thiscall CConsoleCMDSingleton<CCMDShowStatistics>::Clone(CConsoleCMDSingleton<CCMDShowStatistics> *this, const char *szCommand, unsigned int nCommandLength);
CCMDPrintLog *__thiscall CConsoleCMDSingleton<CCMDPrintLog>::Clone(CConsoleCMDSingleton<CCMDPrintLog> *this, const char *szCommand, unsigned int nCommandLength);
CCMDFlushLog *__thiscall CConsoleCMDSingleton<CCMDFlushLog>::Clone(CConsoleCMDSingleton<CCMDFlushLog> *this, const char *szCommand, unsigned int nCommandLength);
CCMDReloadSetup *__thiscall CConsoleCMDSingleton<CCMDReloadSetup>::Clone(CConsoleCMDSingleton<CCMDReloadSetup> *this, const char *szCommand, unsigned int nCommandLength);
CCMDDummyCharacters *__thiscall CConsoleCMDSingleton<CCMDDummyCharacters>::Clone(CConsoleCMDSingleton<CCMDDummyCharacters> *this, const char *szCommand, unsigned int nCommandLength);
CCMDClearDummyCharacters *__thiscall CConsoleCMDSingleton<CCMDClearDummyCharacters>::Clone(CConsoleCMDSingleton<CCMDClearDummyCharacters> *this, const char *szCommand, unsigned int nCommandLength);
void __cdecl __noreturn report_failure();
int __stdcall ExceptionUserFunc(char *szBuffer, int nBufferSize);
int __stdcall WinMain(HINSTANCE__ *hInstance, HINSTANCE__ *hPrevInstance, char *lpCmdLine, int nCmdShow); // idb
void __thiscall CPerformanceInstrument::CPerformanceInstrument(CPerformanceInstrument *this); // idb
void __thiscall CPerformanceInstrument::Stop(CPerformanceInstrument *this); // idb
void __thiscall CAutoInstrument::~CAutoInstrument(CAutoInstrument *this); // idb
char __thiscall CCharacter::DBUpdateForce(CCharacter *this, DBUpdateData::UpdateType eUpdateType);
int __thiscall CProcessThread::End(CProcessThread *this); // idb
// char __usercall FnCSAuth::operator()@<al>(CCharacter *lpCharacter@<eax>, FnCSAuth *this);
char __thiscall CRylGameServer::AddGameProcessThread(CRylGameServer *this);
void __stdcall CGameServerProcessThread::CGameServerProcessThread(CGameServerProcessThread *this);
CGameServerProcessThread *__thiscall CGameServerProcessThread::`scalar deleting destructor'(CGameServerProcessThread *this, char a2);
void __thiscall CGameServerProcessThread::~CGameServerProcessThread(CGameServerProcessThread *this); // idb
void __thiscall CGameServerProcessThread::InternalRun(CGameServerProcessThread *this, CPerformanceInstrument *pulse);
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator *this); // idb
// char __userpurge CDuelCellManager::ProcessAllCell<std::mem_fun_ref_t<void,CCell>>@<al>(int a1@<edi>, void (__thiscall *a2)(_DWORD));
// std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const,CCharacter *>>>@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, CCreatureManager::CProcessSecond<FnDisconnectCharacter,std::pair<unsigned long const ,CCharacter *> > _Last);
CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>,std::pair<unsigned long const,CCharacter *>>>(CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> >,std::pair<unsigned long const ,CCharacter *> > _Func); // idb
CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const,CMonster *>>>(CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<std::mem_fun_t<bool,CMonster>,std::pair<unsigned long const ,CMonster *> > _Func); // idb
// std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const,CMonster *>>>@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>, std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, CCreatureManager::CProcessSecond<FnRegenHPAndMP,std::pair<unsigned long const ,CMonster *> > _Last);
// std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__usercall std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const,CCharacter *>>>@<eax>(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *a1@<ebx>, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, CCreatureManager::CProcessSecond<FnCSAuth,std::pair<unsigned long const ,CCharacter *> > _Last);
int __thiscall CProcessCOMMAND::operator()(CProcessCOMMAND *this, HWND__ *hWnd, unsigned int uMsg, __int16 wParam, int lParam);
int __thiscall CProcessRYLGAME_AUTOSTART::operator()(CProcessRYLGAME_AUTOSTART *this, HWND__ *hWnd, unsigned int uMsg, unsigned int wParam, int lParam); // idb
int __thiscall CProcessGAME_CONNECTTOAGENT::operator()(CProcessGAME_CONNECTTOAGENT *this, HWND__ *hWnd, unsigned int uMsg, unsigned int wParam, int lParam); // idb
int __thiscall CProcessRYLGAME_QUIT::operator()(CProcessRYLGAME_QUIT *this, HWND__ *hWnd, unsigned int uMsg, unsigned int wParam, int lParam); // idb
// BOOL __usercall CRylGameServer::InitializeMsgProc@<eax>(CRylGameServer *this@<ecx>, int a2@<eax>);
// char __usercall GameServerSendPacket::SendUpdateAddressAck@<al>(CSendStream *SendStream@<ebx>, const sockaddr_in *PublicAddr@<edi>, const sockaddr_in *PrivateAddr@<esi>, unsigned int dwCID);
CSocketFactory *__thiscall CSocketFactory::`vector deleting destructor'(CSocketFactory *this, char a2);
void __thiscall CUDPFactory::~CUDPFactory(CTCPFactory *this); // idb
CTCPFactory *__thiscall CUDPFactory::`scalar deleting destructor'(CTCPFactory *this, char a2);
CThread *__thiscall CThread::`vector deleting destructor'(CThread *this, char a2);
// void __usercall LogAddress(const sockaddr_in *sockAddr@<esi>, const char *szDetailText, unsigned int dwCID);
void __thiscall CUDPWish::CUDPWish(CUDPWish *this, CUDPWish *dwMaxProcessPerCall, unsigned int dwMaxProcessPerCalla);
CUDPWish *__thiscall CUDPWish::`scalar deleting destructor'(CUDPWish *this, char a2);
void __thiscall CUDPWish::~CUDPWish(CUDPWish *this); // idb
// bool __usercall CUDPWish::Initialize@<al>(CUDPWish *this@<ecx>, _DWORD *a2@<esi>);
unsigned int __thiscall CUDPWish::Run(CUDPWish *this); // idb
BOOL __thiscall CUDPWish::End(CUDPWish *this);
// char __userpurge CUDPWish::ReceivePacket@<al>(CUDPWish *this@<ecx>, int a2@<esi>, _WSABUF wsaBuf);
// void __usercall CUDPWish::Process(CUDPWish *this@<ecx>, int a2@<edi>);
void __fastcall CUDPWish::ProcessCharUpdateAddress(const sockaddr_in *publicAddr, PktBase *lpPktBase, CUDPWish *this);
void __thiscall CServerLog::CServerLog(CServerLog *this, const char *szLogName, const char *szPathName); // idb
int __thiscall CServerLog::CreateLogFile(CServerLog *this);
void __thiscall CServerLog::InternalFlush(CServerLog *this); // idb
void CServerLog::SimpleLog(CServerLog *this, int eLogType, char *pFormat, ...);
void CServerLog::DetailLog(CServerLog *this, LOG_TYPE eLogType, const char *pRtn, const char *pFileName, int nLine, char *pFormat, ...);
void __thiscall CServerLog::SetLogFileName(CServerLog *this, const char *szLogName, const char *szPathName); // idb
void __thiscall CServerLog::~CServerLog(CServerLog *this); // idb
CServerLog *__thiscall CServerLog::`scalar deleting destructor'(CServerLog *this, char a2);
void __thiscall CPerformanceCheck::SetUserMessageFunc(CPerformanceCheck *this, void (__cdecl *fnPreFix)(_iobuf *), void (__cdecl *fnPostFix)(_iobuf *)); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Min(std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Pnode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Max(std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::const_iterator *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Lrotate(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Rrotate(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Rootnode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Rarg, const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val, char _Carg); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::equal_range(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator> *result, const unsigned int *_Keyval); // idb
char __thiscall CPerformanceCheck::PrintAllTime(CPerformanceCheck *this, const char *fileName, bool bReset);
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::_Node *_Wherenode, const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPerformanceCheck::Instrument>>,1>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CPerformanceCheck::Instrument,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CPerformanceCheck::Instrument> >,1> >::iterator,bool> *result, const std::pair<unsigned long const ,CPerformanceCheck::Instrument> *_Val); // idb
void __thiscall std::multimap<unsigned long,CPerformanceCheck::Instrument>::~multimap<unsigned long,CPerformanceCheck::Instrument>(std::multimap<unsigned long,CPerformanceCheck::Instrument> *this); // idb
void __thiscall CPerformanceCheck::~CPerformanceCheck(CPerformanceCheck *this); // idb
void __thiscall CPerformanceCheck::AddTime(CPerformanceCheck *this, const char *szfunctionName, double fEstimateTime);
void __thiscall CPerformanceCheck::CPerformanceCheck(CPerformanceCheck *this); // idb
CPerformanceCheck *__cdecl CPerformanceCheck::GetInstance(); // idb
void __cdecl DbgUtils::SetProgramName(char *pszOutBuffer, size_t nBufferSize, char *pszProgramName);
COverlappedFactory *__thiscall COverlappedFactory::`scalar deleting destructor'(COverlappedFactory *this, char a2);
void __thiscall CSendOverlapped::CSendOverlapped(CSendOverlapped *this, COverlappedFactory *ovlFactory, CBuffer *lpSendBuffer); // idb
void __thiscall CSendOverlapped::Dispatch(CSendOverlapped *this, int bResult, CSession *lpSessionKey, unsigned int dwProcessedBytes);
void __thiscall CStreamRecvOverlapped::CStreamRecvOverlapped(CStreamRecvOverlapped *this, COverlappedFactory *ovlFactory); // idb
void __thiscall CAcceptOverlapped::CAcceptOverlapped(CAcceptOverlapped *this, COverlappedFactory *ovlFactory, CListener *Listener, unsigned int hSocket, CBuffer *lpAddrBuffer); // idb
void __thiscall CAcceptOverlapped::Dispatch(CAcceptOverlapped *this, int bResult, CListener *lpSessionKey, unsigned int dwProcessedBytes);
CAcceptOverlapped *__thiscall CAcceptOverlapped::`vector deleting destructor'(CAcceptOverlapped *this, char a2);
void __thiscall CSendOverlapped::~CSendOverlapped(CAcceptOverlapped *this); // idb
COverlapped *__thiscall COverlapped::`scalar deleting destructor'(COverlapped *this, char a2);
void __thiscall CStreamRecvOverlapped::Dispatch(CStreamRecvOverlapped *this, int bResult, CSession *lpSessionKey, unsigned int dwProcessedBytes);
char __thiscall CSession::Recv(CSession *this);
void __thiscall CStreamOverlappedFactory::CStreamOverlappedFactory(CStreamOverlappedFactory *this); // idb
void __thiscall CStreamOverlappedFactory::DeleteOverlapped(CStreamOverlappedFactory *this, COverlapped *lpOverlapped); // idb
COverlapped *__thiscall CStreamOverlappedFactory::CreateSend(CStreamOverlappedFactory *this, CSession *lpSession, CBuffer *lpMsgBlock); // idb
COverlapped *__thiscall CStreamOverlappedFactory::CreateRecv(CStreamOverlappedFactory *this, CSession *lpSession, CBuffer *lpMsgBlock); // idb
COverlapped *__thiscall CStreamOverlappedFactory::CreateAccept(CStreamOverlappedFactory *this, CListener *lpListener, unsigned int hSocket, CBuffer *lpMsgBlock); // idb
void __thiscall CStreamOverlappedFactory::~CStreamOverlappedFactory(CStreamOverlappedFactory *this); // idb
CStreamOverlappedFactory *__thiscall CStreamOverlappedFactory::`vector deleting destructor'(CStreamOverlappedFactory *this, char a2);
void __thiscall CBufferFactory::~CBufferFactory(CBufferFactory *this); // idb
CBufferFactory *__thiscall CBufferFactory::`scalar deleting destructor'(CBufferFactory *this, char a2);
void __thiscall CPoolBufferFactory::Release(CPoolBufferFactory *this, CPacketDispatch *buffer);
CBuffer *__thiscall CPoolBufferFactory::Create(CPoolBufferFactory *this, unsigned int size); // idb
void __thiscall CPoolBufferFactory::Destroy(CPoolBufferFactory *this); // idb
void __thiscall CPoolBufferFactory::~CPoolBufferFactory(CPoolBufferFactory *this); // idb
void __thiscall __noreturn std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Xlen(std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> > *this);
CPoolBufferFactory *__thiscall CPoolBufferFactory::`vector deleting destructor'(CPoolBufferFactory *this, char a2);
void __thiscall std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *>>::_Insert_n(std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> > *this, std::vector<boost::pool<boost::default_user_allocator_new_delete> *,std::allocator<boost::pool<boost::default_user_allocator_new_delete> *> >::iterator _Where, unsigned int _Count, boost::pool<boost::default_user_allocator_new_delete> *const *_Val); // idb
char __thiscall CPoolBufferFactory::Initialize(CPoolBufferFactory *this);
void __thiscall CPoolBufferFactory::CPoolBufferFactory(CPoolBufferFactory *this); // idb
void __thiscall CSessionPolicy::~CSessionPolicy(CSessionPolicy *this); // idb
LONG __thiscall CSessionPolicy::AddRef(CSessionPolicy *this);
LONG __thiscall CSessionPolicy::Release(CSessionPolicy *this);
void __thiscall CTCPFactory::CTCPFactory(CTCPFactory *this); // idb
void __thiscall CUDPFactory::CUDPFactory(CUDPFactory *this); // idb
SOCKET __thiscall CSocketFactory::CreateSocket(CSocketFactory *this);
SOCKET __thiscall CSocketFactory::CreateConnectedSocket(CSocketFactory *this, const char *lpConnAddr, int usPort);
SOCKET __thiscall CSocketFactory::CreateBindedSocket(CSocketFactory *this, const char *lpBindAddr, int usPort);
SOCKET __thiscall CSocketFactory::CreateListenSocket(CSocketFactory *this, const char *lpListenAddr, int usPort, int nBackLog);
char __thiscall CINETFamilyFactory::SetAddr(CINETFamilyFactory *this, sockaddr *lpSA, const char *szAddr, u_short usPort);
bool __thiscall CINETFamilyFactory::GetNetworkInfo(CINETFamilyFactory *this, char *Address_Out, int nMaxBufferSize); // idb
BOOL __thiscall CCompletionHandler::AttachToHander(CCompletionHandler *this, void *hAttach, ULONG_PTR pCompletionKey);
void __thiscall INET_Addr::INET_Addr(INET_Addr *this); // idb
void __thiscall INET_Addr::INET_Addr(INET_Addr *this, const char *addr, u_short port);
char __thiscall CListener::Initialize(CListener *this, const char *lpListenAddress, u_short usPort, unsigned int dwMaxPending);
unsigned int __cdecl GetOptimalThreadNum(); // idb
void __thiscall CSessionMgr::Process(CSessionMgr *this); // idb
unsigned int __thiscall CSessionMgr::GetSessionNum(CSessionMgr *this); // idb
char __thiscall CIOCPNet::Initialize(CIOCPNet *this);
void __thiscall CIOCPNet::Process(CIOCPNet *this); // idb
unsigned int __thiscall CIOCPNet::GetSessionNum(CIOCPNet *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Rootnode); // idb
int __thiscall CIOCPNet::GetAcceptPendingNum(CIOCPNet *this);
std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Rootnode, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode);
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Copy(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, const std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *_Right); // idb
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Where); // idb
void __thiscall std::list<CListener *>::_Incsize(std::list<CListener *> *this, unsigned int _Count); // idb
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::erase(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
void __thiscall std::map<CSessionPolicy *,unsigned int>::~map<CSessionPolicy *,unsigned int>(std::map<CSessionPolicy *,unsigned int> *this); // idb
std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::_Node *_Val);
void __thiscall std::list<CListener *>::push_back(std::list<CListener *> *this, CThread **_Val);
void __thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, const std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *_Right); // idb
std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::insert(std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> > *this, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int> >,0> >::iterator _Where, std::pair<unsigned long const ,unsigned long> *_Val);
unsigned int *__thiscall std::map<CSessionPolicy *,unsigned int>::operator[](std::map<CSessionPolicy *,unsigned int> *this, CSessionPolicy *const *_Keyval); // idb
void __thiscall std::list<CListener *>::_Splice(std::list<CListener *> *this, std::list<CListener *>::iterator _Where, std::list<CListener *> *_Right, std::list<CListener *>::iterator _First, std::list<CListener *>::iterator _Last, unsigned int _Count); // idb
void __thiscall CIOCPNet::CIOCPNet(CIOCPNet *this); // idb
char __thiscall CIOCPNet::AddListener(CIOCPNet *this, CSessionPolicy *lpSessionPolicy, const char *lpListenAddress, u_short usPort, _RTL_CRITICAL_SECTION *dwMaxListenPeding, CValidateConnection *lpValidateConnection);
char __thiscall CIOCPNet::Connect(CIOCPNet *this, CSessionPolicy *lpSessionPolicy, char *lpConnectAddress, _RTL_CRITICAL_SECTION *usPort);
void __thiscall CIOCPNet::DestroyListener(CIOCPNet *this); // idb
char __thiscall CIOCPNet::Destroy(CIOCPNet *this);
void __thiscall CIOCPNet::~CIOCPNet(CIOCPNet *this); // idb
CDLLModule *__thiscall CDLLModule::`vector deleting destructor'(CDLLModule *this, char a2);
int __thiscall CDBGFuncClass::Init(CDBGFuncClass *this, const char *szDll); // idb
void __thiscall CDBGFuncClass::~CDBGFuncClass(CDBGFuncClass *this); // idb
CDBGFuncClass *__thiscall CDBGFuncClass::`vector deleting destructor'(CDBGFuncClass *this, char a2);
int __cdecl CExceptionReport::WriteRegistersInfo(char *szBuffer_Out, int nBufferSize, _CONTEXT *pContext);
char *__cdecl CExceptionReport::GetExceptionString(DWORD dwCode);
SIZE_T __cdecl CExceptionReport::GetLogicalAddress(void *addr, char *szModule, DWORD len, unsigned int *section, unsigned int *offset);
int __cdecl CExceptionReport::FormatOutputValue(char *szBuffer_Out, int nBufferSize, BasicType basicType, unsigned __int64 length, LPCSTR *pAddress);
int __cdecl CExceptionReport::Dump(char *szBuffer_Out, int nBufferSize, unsigned __int64 dw64Offset, unsigned int dwSize, int bAlign);
int __stdcall CExceptionReport::UnhandledSecondExceptionFilter(_EXCEPTION_POINTERS *lpExceptionInfo); // idb
void __thiscall CExceptionReport::~CExceptionReport(CExceptionReport *this); // idb
int __cdecl CExceptionReport::WriteBasicInfo(char *szBuffer_Out, int nBufferSize, _EXCEPTION_RECORD *pExceptionRecord);
int __cdecl CExceptionReport::WriteMemoryDump(char *szBuffer_Out, int nBufferSize, _CONTEXT *pContext, unsigned int nMaxIPDump, unsigned int nMaxStackDump);
int __cdecl CExceptionReport::DumpTypeIndex(char *szBuffer_Out, unsigned int nBufferSize, unsigned __int64 modBase, unsigned int dwTypeIndex, unsigned int nestingLevel, unsigned int offset, int *bHandled);
int __cdecl CExceptionReport::FormatSymbolValue(_SYMBOL_INFO *pSym, _tagSTACKFRAME *sf, char *pszBuffer, unsigned int nBufferSize); // idb
int __stdcall CExceptionReport::EnumerateSymbolsCallback(_SYMBOL_INFO *pSymInfo, unsigned int SymbolSize, void *UserContext); // idb
int __cdecl CExceptionReport::WriteStackDetails(char *szBuffer_Out, int nBufferSize, _CONTEXT *pContext, int bWriteVariables, int *bHasSymbol_Out, int nStackDepth);
void __thiscall CExceptionReport::WriteExceptionReport(CExceptionReport *this, _EXCEPTION_POINTERS *pExceptionInfo); // idb
int __thiscall CExceptionReport::ProcessException(CExceptionReport *this, _EXCEPTION_POINTERS *lpExceptionInfo); // idb
int __stdcall CExceptionReport::UnhandledExceptionFilter(_EXCEPTION_POINTERS *lpExceptionInfo); // idb
void __thiscall CExceptionReport::Enable(CExceptionReport *this, unsigned int dwEnableFeature); // idb
void __thiscall CExceptionReport::CExceptionReport(CExceptionReport *this); // idb
CExceptionReport *__cdecl CExceptionReport::GetInstance(); // idb
BasicType __cdecl CExceptionReport::GetBasicType(unsigned int typeIndex, unsigned __int64 modBase); // idb
void __thiscall INET_Addr::set_addr(INET_Addr *this, const char *addr, u_short port);
void __thiscall INET_Addr::set_addr(INET_Addr *this, const sockaddr *addr, int size);
void __thiscall INET_Addr::set_addr(INET_Addr *this, in_addr addr, u_short port);
int __stdcall CThread::ThreadFunc(void *pArg);
HANDLE __cdecl CThreadMgr::Run(CThread *lpThread);
bool __cdecl CThreadMgr::Stop(CThread *lpThread, DWORD dwTimeout);
int __thiscall  __thiscall `vcall'{8,{flat}}(void *this);
void __thiscall CThreadMgr::CThreadMgr(CThreadMgr *this); // idb
bool __thiscall CThreadMgr::RegisterAndRun(CThreadMgr *this, CThread *llpThread); // idb
std::mem_fun_t<int,CThread> *__cdecl std::for_each<CThread * *,std::mem_fun_t<int,CThread>>(std::mem_fun_t<int,CThread> *result, CThread **_First, CThread **_Last, std::mem_fun_t<int,CThread> _Func); // idb
char __thiscall CThreadMgr::JoinThread(CThreadMgr *this);
void __thiscall CThreadMgr::~CThreadMgr(CThreadMgr *this); // idb
CThreadMgr *__thiscall CThreadMgr::`scalar deleting destructor'(CThreadMgr *this, char a2);
void __thiscall CBufferQueue::CBufferQueue(CBufferQueue *this); // idb
char __thiscall CBufferQueue::enqueue(CBufferQueue *this, CBuffer *lpBuffer, bool bPendHead);
CBuffer *__thiscall CBufferQueue::dequeue(CBufferQueue *this); // idb
void __thiscall CBufferQueue::clear(CBufferQueue *this); // idb
void __thiscall CBufferQueue::splice(CBufferQueue *this, CBufferQueue *Buffer_In, bool bPendHead); // idb
void __thiscall CBufferQueue::~CBufferQueue(CBufferQueue *this); // idb
CBufferQueue *__thiscall CBufferQueue::`scalar deleting destructor'(CBufferQueue *this, char a2);
void __thiscall CSession::CSession(CSession *this, CSessionPolicy *SessionPolicy); // idb
void __thiscall CSession::SetAddress(CSession *this, INET_Addr *remoteAddr, INET_Addr *localAddr); // idb
int __thiscall CSession::InternalPrintStatistics(CSession *this, char *szBuffer, int nBufferLen); // idb
void __thiscall CSession::InternalCloseSocket(CSession *this); // idb
bool __thiscall CSession::Shutdown(CSession *this); // idb
int __thiscall CSession::AddRef(CSession *this); // idb
int __thiscall CSession::Release(CSession *this); // idb
void __thiscall CSession::CloseSession(CSession *this); // idb
char __thiscall CSession::InternalRecv(CSession *this);
char __thiscall CSession::InternalSend(CSession *this, CBuffer *lpBuffer);
char __thiscall CSession::InternalSendTo(CSession *this, CBuffer *lpBuffer);
char __thiscall CSession::SendPending(CSession *this, CBuffer *lpBuffer);
char __thiscall CSession::InternalSend(CSession *this);
void __thiscall CSession::~CSession(CSession *this); // idb
char __thiscall CSession::Process(CSession *this);
bool __thiscall CSession::Dispatch(CSession *this, int dwReceivedBytes);
void __thiscall CSession::SendCompleted(CSession *this, int bResult, unsigned int dwSendedBytes); // idb
std::ostream *__thiscall std::ostream::flush(std::ostream *this); // idb
void __thiscall std::ostream::sentry::sentry(std::ostream::sentry *this, std::ostream *_Ostr); // idb
void __thiscall std::ostream::sentry::~sentry(std::ostream::sentry *this); // idb
std::ostream *__cdecl std::operator<<<std::char_traits<char>>(std::ostream *_Ostr, const char *_Val); // idb
void __thiscall CListener::InternalCloseListen(CListener *this); // idb
void __thiscall CListener::CListener(CListener *this, CCompletionHandler *SocketHandler, CSessionPolicy *SessionPolicy, CSessionMgr *SessionMgr, CValidateConnection *lpValidateConnection); // idb
void __thiscall CListener::WaitForPendingComplete(CListener *this, unsigned int dwTime); // idb
char __thiscall CListener::PendingAccept(CListener *this);
void __thiscall CListener::ProcessAccept(CListener *this, int bResult, void *hSocket, CBuffer *lpBuffer, unsigned int dwProcessedBytes);
unsigned int __thiscall CListener::GetPendingAcceptNum(CListener *this); // idb
void __thiscall CListener::~CListener(CListener *this); // idb
char __thiscall CListener::Initialize(CListener *this, INET_Addr *addrListen, unsigned int dwMaxPending);
CListener *__thiscall CListener::`vector deleting destructor'(CListener *this, char a2);
void __thiscall CBuffer::CBuffer(CBuffer *this, CBufferFactory *bufferfactory); // idb
void __thiscall CBuffer::init(CBuffer *this, char *internal_buffer, unsigned int buffer_size); // idb
void __thiscall CBuffer::pop_read_data(CBuffer *this); // idb
void __thiscall CSessionMgr::DeleteSession(CSessionMgr *this, CSession *lpSession); // idb
void __thiscall CSessionMgr::CSessionMgr(CSessionMgr *this); // idb
CSession *__thiscall CSessionMgr::CreateSession(CSessionMgr *this, CSessionPolicy *SessionPolicy); // idb
void __thiscall std::list<CSession *>::_Incsize(std::list<CSession *> *this, unsigned int _Count); // idb
void __thiscall CSessionMgr::Add(CSessionMgr *this, CSession *lpSession); // idb
void __thiscall std::list<CSession *>::_Splice(std::list<CSession *> *this, std::list<CSession *>::iterator _Where, std::list<CSession *> *_Right, std::list<CSession *>::iterator _First, std::list<CSession *>::iterator _Last, unsigned int _Count); // idb
void __thiscall CSessionMgr::InternalProcess(CSessionMgr *this); // idb
void __thiscall CSessionMgr::Destroy(CSessionMgr *this, unsigned int dwWaitTime); // idb
void __thiscall CSessionMgr::~CSessionMgr(CSessionMgr *this); // idb
void __thiscall CIOWorker::CIOWorker(CIOWorker *this, CCompletionHandler *SocketHandler); // idb
void __thiscall CIOWorker::~CIOWorker(CIOWorker *this); // idb
unsigned int __thiscall CIOWorker::Run(CIOWorker *this); // idb
int __thiscall CIOWorker::End(CIOWorker *this); // idb
CIOWorker *__thiscall CIOWorker::`scalar deleting destructor'(CIOWorker *this, char a2);
void __thiscall CCompletionHandler::CCompletionHandler(CCompletionHandler *this); // idb
char __thiscall CCompletionHandler::Initialize(CCompletionHandler *this, DWORD nThread, unsigned int nTimeOutMS);
CCompletionHandler *__thiscall CCompletionHandler::`scalar deleting destructor'(CCompletionHandler *this, char a2);
unsigned int __cdecl Math::HashFunc::sdbmHash(const unsigned __int8 *str); // idb
void __stdcall __noreturn ATL::AtlThrow(HRESULT hr);
void __thiscall ATL::CComCriticalSection::CComCriticalSection(ATL::CComCriticalSection *this); // idb
HRESULT __thiscall ATL::CComCriticalSection::Init(ATL::CComCriticalSection *this); // idb
void __thiscall Position::Position(Position *this); // idb
BOOL __thiscall CCharacter::StillAlive(CCharacter *this);
CStatue *__thiscall CMonster::DowncastToStatue(CMonster *this); // idb
BOOL __thiscall CVirtualMonsterMgr::IsSummonee(CVirtualMonsterMgr *this, unsigned int dwCID);
unsigned __int8 __thiscall CCreatureManager::IsAttackable(CCreatureManager *this, unsigned __int8 cAttackerACT, unsigned __int8 cDefenderACT); // idb
std::bad_alloc *__thiscall std::bad_alloc::`vector deleting destructor'(std::bad_alloc *this, char a2);
void __thiscall std::bad_alloc::~bad_alloc(std::bad_alloc *this); // idb
std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *__thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(std::list<CCreatureManager::BattleGroundRespawnInfo> *this); // idb
std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *__thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Buynode(std::list<CCreatureManager::BattleGroundRespawnInfo> *this, std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *_Next, std::_List_nod<CCreatureManager::BattleGroundRespawnInfo>::_Node *_Prev, const CCreatureManager::BattleGroundRespawnInfo *_Val); // idb
FnLeaveParty __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnLeaveParty>(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, FnLeaveParty _Func); // idb
FnDeleteSecond __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,FnDeleteSecond>(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, FnDeleteSecond _Func); // idb
std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *__cdecl std::for_each<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType>>>(std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > *result, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last, std::binder2nd<std::mem_fun1_t<bool,CCharacter,enum DBUpdateData::UpdateType> > _Func); // idb
CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const,CCharacter *>>>(CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<CSendEliteBonus,std::pair<unsigned long const ,CCharacter *> > _Func); // idb
void __thiscall CCreatureManager::DeleteCharacter(CCreatureManager *this, CCharacter *lpCharacter); // idb
void __thiscall CCreatureManager::SendAllCharacter(CCreatureManager *this, char *szBuffer, unsigned int dwLength, unsigned __int8 cCMD_In, bool bAll);
void __thiscall CCreatureManager::PopRespawnQueue(CCreatureManager *this, CCharacter *lpCharacter); // idb
void __thiscall CCreatureManager::ProcessRespawnQueue(CCreatureManager *this); // idb
unsigned __int8 __thiscall CCreatureManager::GetBonusTurn(CCreatureManager *this, unsigned __int16 wMapIndex);
void __thiscall CCreatureManager::ProcessBattleGround(CCreatureManager *this); // idb
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::find(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, const unsigned __int16 *_Keyval); // idb
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::partition<std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,std::mem_fun_t<bool,CCharacter>>(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last, std::mem_fun_t<bool,CCharacter> _Pred); // idb
CCharacter *__thiscall CCreatureManager::GetCharacter(CCreatureManager *this, unsigned int dwCID); // idb
CCharacter *__thiscall CCreatureManager::GetCharacter(CCreatureManager *this, char *szCharacterName);
CMonster *__thiscall CCreatureManager::GetMonster(CCreatureManager *this, unsigned int dwCID); // idb
void __thiscall CCreatureManager::SetEliteBonus(CCreatureManager *this, EliteBonus::EliteBonusData eliteBonus); // idb
char __thiscall CCreatureManager::SendRespawnQueue(CCreatureManager *this, unsigned int dwCID);
void __thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::list<CCreatureManager::BattleGroundRespawnInfo>(std::list<CCreatureManager::BattleGroundRespawnInfo> *this); // idb
void __thiscall boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *this); // idb
CMsgProc *__thiscall CCreatureManager::GetAggresiveCreature(CCreatureManager *this, signed int dwCID);
void __thiscall CCreatureManager::CreateCharacter(CCreatureManager *this, unsigned int dwCID);
void __thiscall CCreatureManager::CalculateEliteBonus(CCreatureManager *this, float usPeopleNum);
CMsgProc *__thiscall CCreatureManager::GetCreature(CCreatureManager *this, signed int dwCID);
void __thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, unsigned int _Count); // idb
void __thiscall std::list<CCreatureManager::BattleGroundRespawnInfo>::_Incsize(std::list<CCreatureManager::BattleGroundRespawnInfo> *this, unsigned int _Count); // idb
boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance(); // idb
boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance(); // idb
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(void **ptr);
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(void **ptr);
void __thiscall std::bad_alloc::bad_alloc(std::bad_alloc *this, const std::bad_alloc *__that); // idb
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(void **ptr);
std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rarg, const std::pair<unsigned long const ,CTempCharacter *> *_Val, const char *_Carg);
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this); // idb
void __thiscall CCreatureManager::PushRespawnQueue(CCreatureManager *this, CCharacter *lpCharacter, unsigned __int8 cPointNumber); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rootnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Rarg, const std::pair<unsigned short const ,unsigned short> *_Val, const char *_Carg);
std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Next, std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Prev, CCharacter *const *_Val); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this); // idb
char __thiscall CCreatureManager::CancelLogout(CCreatureManager *this, CCharacter *lpCharacter);
void __thiscall CCreatureManager::ProcessSummonMonsterDead(CCreatureManager *this); // idb
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,CNPC *> *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::_Node *_Wherenode, const std::pair<unsigned short const ,unsigned short> *_Val); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator,bool> *result, const std::pair<unsigned long const ,CNPC *> *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last); // idb
void __thiscall CCreatureManager::DestoryCharacterList(CCreatureManager *this); // idb
void __thiscall CCreatureManager::DestoryMonsterList(CCreatureManager *this); // idb
void __thiscall CCreatureManager::DestoryNPCList(CCreatureManager *this); // idb
void __thiscall CCreatureManager::DestroyAll(CCreatureManager *this); // idb
char __thiscall CCreatureManager::ProcessCharacterLogout(CCreatureManager *this);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *result, std::pair<unsigned long const ,CNPC *> *_Val); // idb
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, const unsigned int *_Keyval);
std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator,bool> *result, std::pair<unsigned short const ,unsigned short> *_Val); // idb
void __thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, CCharacter *const *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> > *this, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Where, std::pair<unsigned short const ,unsigned short> *_Val); // idb
void __thiscall std::map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::map<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
bool __thiscall CCreatureManager::AddCreature(CCreatureManager *this, CNPC *lpCreature);
bool __thiscall CCreatureManager::DeleteCreature(CCreatureManager *this, signed int dwCID);
void __thiscall CCreatureManager::EnqueueLogout(CCreatureManager *this, CCharacter *lpCharacter); // idb
unsigned __int16 *__thiscall std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::operator[](std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, const unsigned __int16 *_Keyval); // idb
void __thiscall std::map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::map<unsigned long,CMonster *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CMonster *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::map<unsigned short,unsigned short,std::less<unsigned short>,boost::fast_pool_allocator<std::pair<unsigned short,unsigned short>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall std::map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::map<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall std::multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::multimap<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall std::map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::map<unsigned long,CSiegeObject *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CSiegeObject *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall CCreatureManager::~CCreatureManager(CCreatureManager *this); // idb
unsigned __int16 __thiscall CCreatureManager::GetAvailableMonsterUID(CCreatureManager *this, unsigned __int16 wKindID); // idb
void __thiscall CCreatureManager::CCreatureManager(CCreatureManager *this); // idb
CCreatureManager *__cdecl CCreatureManager::GetInstance(); // idb
void __thiscall PktBase::InitPtHead(PktBase *this, unsigned __int16 Len_In, unsigned __int8 Cmd_In, unsigned __int16 State_In, unsigned __int16 Error_In); // idb
int __thiscall CSendDataToManageClient::operator()(CSendDataToManageClient *this, HWND__ *hWnd, unsigned int uMsg, unsigned int wParam, int lParam); // idb
int __stdcall CServerWindowFramework::ServerWindowFrameworkProc(HWND__ *hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void __thiscall CServerWindowFramework::PrintOutput(CServerWindowFramework *this, const char *szText, int nLength); // idb
void __thiscall CServerWindowFramework::PrintInfo(CServerWindowFramework *this, const char *szText, int nLength); // idb
char __thiscall CServerWindowFramework::SendManageClientPacket(CServerWindowFramework *this, const void *lpData, unsigned __int16 usLength);
void __thiscall CProcessThread::CProcessThread(CProcessThread *this, CServerWindowFramework *ServerWindowFramework, int nProcessTick); // idb
void __thiscall CProcessThread::~CProcessThread(CProcessThread *this); // idb
char __thiscall CServerWindowFramework::InitializeFramework(CServerWindowFramework *this, HINSTANCE__ *hInstance, const char *szWndApplicationName, unsigned int nICON_ID, int nMenu_ID);
CSendDataToManageClient *__thiscall CSendDataToManageClient::`vector deleting destructor'(CSendDataToManageClient *this, char a2);
void __thiscall CSendDataToManageClient::~CSendDataToManageClient(CSendDataToManageClient *this); // idb
void __thiscall CServerWindowFramework::Destroy(CServerWindowFramework *this); // idb
void __thiscall CServerWindowFramework::SendManageClientPing(CServerWindowFramework *this); // idb
CProcessThread *__thiscall CProcessThread::`vector deleting destructor'(CProcessThread *this, char a2);
unsigned int __thiscall CProcessThread::Run(CProcessThread *this); // idb
char __thiscall CServerWindowFramework::Initialize(CServerWindowFramework *this, HINSTANCE__ *hInstance, const char *szWndApplicationName, const char *szCmdLine, unsigned int nICON_ID, int nMenu_ID);
void __thiscall CServerWindowFramework::ProcessMessage(CServerWindowFramework *this); // idb
void __thiscall CServerWindowFramework::~CServerWindowFramework(CServerWindowFramework *this); // idb
void __thiscall CServerWindowFramework::CServerWindowFramework(CServerWindowFramework *this); // idb
CServerWindowFramework *__thiscall CServerWindowFramework::`vector deleting destructor'(CServerWindowFramework *this, char a2);
void __thiscall std::list<CThread *>::_Incsize(std::list<CThread *> *this, unsigned int _Count); // idb
int __thiscall CRecvCommandFromManageClient::operator()(CRecvCommandFromManageClient *this, HWND__ *hWnd, unsigned int uMsg, unsigned int wParam, int lParam); // idb
char __thiscall CServerWindowFramework::AddProcessThread(CServerWindowFramework *this, CThread *lpProcessThread);
void __thiscall CNetworkPos::Initialize(CNetworkPos *this, float fXPos, float fYPos, float fZPos, float fDir, float fVel); // idb
void __thiscall Item::CEquipment::AddAttribute(Item::CEquipment *this, Item::Attribute::Type eAttributeType, unsigned __int16 nAttributeValue); // idb
Item::CEquipment *__cdecl Item::CEquipment::DowncastToEquipment(Item::CEquipment *lpItem);
CCell *__thiscall CCell::GetConnectCell(CCell *this, unsigned int nDir); // idb
void __thiscall CCell::SetConnectCell(CCell *this, unsigned int nDir, CCell *lpConnectedCell); // idb
char __thiscall CCell::IsNearCell(CCell *this, CCell *lpNearCell);
unsigned int __thiscall CCell::GetNearCellCharacterNum(CCell *this); // idb
std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::find_if<std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindCreatureFromCID>(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First, std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last, CFindCreatureFromCID _Pred);
std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__cdecl std::find_if<std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::iterator,CFindItemInfoFromUID>(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _First, std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Last, CFindItemInfoFromUID _Pred); // idb
CSiegeObject *__thiscall CCell::GetFirstAirShip(CCell *this); // idb
CSiegeObject *__thiscall CCell::GetNextAirShip(CCell *this); // idb
CSiegeObject *__thiscall CCell::GetFirstSiegeObject(CCell *this); // idb
CSiegeObject *__thiscall CCell::GetNextSiegeObject(CCell *this); // idb
CCharacter *__thiscall CCell::GetFirstCharacter(CCell *this); // idb
CMonster *__thiscall CCell::GetFirstAggresiveCreature(CCell *this);
CSiegeObject *__thiscall CCell::GetNextAggresiveCreature(CCell *this);
CCreature *__thiscall CCell::GetCreature(CCell *this, signed int dwCID);
void __thiscall CCell::PrepareBroadCast(CCell *this); // idb
void __thiscall CCell::SendCellInfo(CCell *this, CCharacter *lpCharacter); // idb
void __thiscall CCell::SendAllCharacter(CCell *this, char *szPacket, unsigned int dwPacketSize, unsigned __int8 cCMD_In);
void __thiscall CCell::RespawnAllCharacter(CCell *this, unsigned int dwExcepGID); // idb
void __thiscall CCell::BroadCast(CCell *this); // idb
void __thiscall CCell::SendAllNearCellCharacter(CCell *this, char *szPacket, unsigned int dwPacketSize, unsigned __int8 cCMD_In);
void __thiscall CCell::SendPullDownInfo(CCell *this, unsigned int dwPreOwnerID, CCell::ItemInfo *lpPullDownItem); // idb
void __thiscall CCell::SendPickUpInfo(CCell *this, unsigned int dwCreatureID, unsigned __int64 nItemInfoID); // idb
void __thiscall CCell::SendAttackInfo(CCell *this, unsigned int AttackerID_In, const AtType *AtType_In, unsigned __int8 DefenserNum_In, DefenserNode *lpNode_In); // idb
void __thiscall CCell::SendCastObjectInfo(CCell *this, unsigned int SenderID, unsigned int ReceiverID, CastObject *CastObject_In); // idb
void __thiscall CCell::KillAll(CCell *this, CCharacter *lpAttacker); // idb
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Incsize(std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, unsigned int _Count); // idb
boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *__cdecl boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance(); // idb
void __cdecl boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::free(void **ptr);
void __thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Where); // idb
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::clear(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *__thiscall std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::erase(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator *result, std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator _Where);
std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
std::_List_nod<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *__thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::_Buynode(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Next, std::_List_nod<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::_Node *_Prev, const CCell::ItemInfo *_Val); // idb
void __thiscall CCell::DeleteCreature(CCell *this, signed int dwCID, std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> >::iterator eCellMoveType);
CCell::ErrorCode __thiscall CCell::GetItem(CCell *this, unsigned int dwCreatureID, CFindItemInfoFromUID nItemInfoID, Item::CItem **lppItem, unsigned int *dwMoney_Out);
void __thiscall CCell::CheckDeleteItem(CCell *this); // idb
void __thiscall CCell::DeleteAllItem(CCell *this); // idb
void __thiscall std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::list<CCell::ItemInfo,boost::fast_pool_allocator<CCell::ItemInfo,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::list<CCharacter *,boost::fast_pool_allocator<CCharacter *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall CCell::CCell(CCell *this); // idb
void __thiscall CCell::~CCell(CCell *this); // idb
void __thiscall std::list<CMonster *,boost::fast_pool_allocator<CMonster *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::push_back(std::list<CSiegeObject *,boost::fast_pool_allocator<CSiegeObject *,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this, CCharacter *const *_Val);
void __thiscall CCell::SetCreature(CCell *this, signed int dwCID, CCharacter *lpCreature, CCell::CellMoveType eCellMoveType);
char __thiscall CCell::SetItem(CCell *this, Item::CItem *lpItemBase, const Position *Pos, CCell::ItemInfo *lpCellItemInfo, unsigned int dwPreOwnerID, unsigned int dwOwnerID, unsigned int dwKindID, unsigned __int8 cItemNum, bool bAutoRouting);
void __thiscall CClass::IncrementByType(CClass *this, _CHAR_INFOST *InfoSt, unsigned __int8 cTypeIndex, unsigned __int8 cIncrementIndex); // idb
unsigned __int8 __cdecl CClass::GetRequiredIP(unsigned __int16 usClass, unsigned __int8 cType); // idb
// bool __userpurge CClass::UpgradeClass@<al>(CClass *this@<ecx>, int a2@<edi>, CharacterDBData *DBData, unsigned __int8 cClassType);
bool __thiscall CClass::CheckState(CClass *this, ChState *State, unsigned __int8 cLevel); // idb
bool __thiscall CClass::CheckMinState(CClass *this, ChState *State, unsigned __int8 cLevel); // idb
int __cdecl CClass::GetJobLevel(unsigned __int8 cClass);
unsigned __int8 __cdecl CClass::GetPreviousJob(unsigned __int8 cClass); // idb
int __cdecl CClass::GetRace(unsigned __int8 cClass);
void __thiscall CClass::LevelUp(CClass *this, _CHAR_INFOST *InfoSt); // idb
char __thiscall CClass::DowngradeClass(CClass *this, CharacterDBData *DBData, unsigned __int8 cClassType);
char __thiscall CClass::InitializeClass(CClass *this, CharacterDBData *DBData, unsigned __int8 cClassType);
// char __userpurge CClass::JobChange@<al>(CClass *this@<ecx>, int a2@<edi>, CharacterDBData *DBData, unsigned __int8 cClassType);
void __cdecl Math::Convert::Hex64ToStr(char *szDest, unsigned __int64 hex); // idb
void __thiscall Item::CItem::MoveItem(Item::CItem *this, Item::ItemPos itemPos); // idb
unsigned int __cdecl LogErrorItem(unsigned int dwCID, const char *szDetail, const char *szBuffer); // idb
void __thiscall Item::CItemContainer::DumpItemInfo(Item::CItemContainer *this); // idb
char __thiscall Item::CItemContainer::SerializeOut(Item::CItemContainer *this, char *szItemBuffer_Out, unsigned int *dwBufferSize_InOut);
Item::CItem *__thiscall Item::CArrayContainer::GetItem(Item::CArrayContainer *this, unsigned int itemPos);
char __thiscall Item::CArrayContainer::SetItem(Item::CArrayContainer *this, unsigned int itemPos, Item::CItem *lpItem);
bool __thiscall Item::CArrayContainer::TestItem(Item::CArrayContainer *this, Item::CItem *lpItem); // idb
char __thiscall Item::CArrayContainer::RemoveItem(Item::CArrayContainer *this, unsigned int itemPos);
__int16 __thiscall Item::CArrayContainer::GetItemNum(Item::CArrayContainer *this, unsigned __int16 usProtoTypeID);
void __thiscall Item::CArrayContainer::DumpItemInfo(Item::CArrayContainer *this); // idb
Item::CItem *__thiscall Item::CListContainer::GetItem(Item::CListContainer *this, unsigned __int16 itemPos);
char __thiscall Item::CListContainer::SetItem(Item::CListContainer *this, unsigned __int16 itemPos, Item::CItem *lpItem);
bool __thiscall Item::CListContainer::TestItem(Item::CListContainer *this, unsigned __int16 itemPos, unsigned __int16 usProtoTypeID);
char __thiscall Item::CListContainer::RemoveItem(Item::CListContainer *this, unsigned __int16 itemPos);
void __thiscall Item::CNullItem::~CNullItem(Item::CNullItem *this); // idb
void __thiscall Item::CNullItem::CNullItem(Item::CNullItem *this, const Item::ItemInfo *itemInfo); // idb
Item::CNullItem *__thiscall Item::CNullItem::`vector deleting destructor'(Item::CNullItem *this, char a2);
void __thiscall Item::CItemContainer::CItemContainer(Item::CItemContainer *this); // idb
char __thiscall Item::CItemContainer::Initialize(Item::CItemContainer *this, unsigned int dwCID, unsigned __int16 nMaxSize);
void __thiscall Item::CItemContainer::ClearItems(Item::CItemContainer *this); // idb
void __thiscall Item::CItemContainer::Destroy(Item::CItemContainer *this); // idb
char __thiscall Item::CArrayContainer::Initialize(Item::CArrayContainer *this, unsigned int dwCID, unsigned __int8 nXSize, unsigned __int8 nYSize, unsigned __int8 nTabNum);
char __thiscall Item::CArrayContainer::TestItem(Item::CArrayContainer *this, unsigned int itemPos, unsigned __int16 usProtoTypeID);
Item::ItemPos *__thiscall Item::CArrayContainer::GetBlankPos(Item::CArrayContainer *this, Item::ItemPos *result, unsigned __int16 wProtoTypeID); // idb
char __thiscall Item::CArrayContainer::SerializeIn(Item::CArrayContainer *this, const char *szItemBuffer_In, unsigned int dwBufferSize_In);
char __thiscall Item::CListContainer::SerializeIn(Item::CListContainer *this, const char *szItemBuffer_In, unsigned int dwBufferSize_In);
void __thiscall Item::CArrayContainer::~CArrayContainer(Item::CListContainer *this); // idb
void __thiscall Item::CArrayContainer::CArrayContainer(Item::CArrayContainer *this); // idb
void __thiscall Item::CListContainer::CListContainer(Item::CListContainer *this); // idb
Item::CItemContainer *__thiscall Item::CItemContainer::`vector deleting destructor'(Item::CItemContainer *this, char a2);
Item::CListContainer *__thiscall Item::CListContainer::`vector deleting destructor'(Item::CListContainer *this, char a2);
void __thiscall __noreturn std::vector<Item::ItemGarbage>::_Xlen(std::vector<Item::ItemGarbage> *this);
void __thiscall std::vector<Item::ItemGarbage>::_Insert_n(std::vector<Item::ItemGarbage> *this, std::vector<Item::ItemGarbage>::iterator _Where, unsigned int _Count, const Item::ItemGarbage *_Val); // idb
void __thiscall std::vector<Item::ItemGarbage>::push_back(std::vector<Item::ItemGarbage> *this, const Item::ItemGarbage *_Val); // idb
char __thiscall Item::CArrayContainer::DisappearItem(Item::CArrayContainer *this, int wItemID, unsigned __int16 wItemNum, std::vector<Item::ItemGarbage> *vecItemGarbage);
void __thiscall Item::CItemFactory::CreateItem(Item::CItemFactory *this, const Item::ItemInfo *itemInfo);
void __thiscall Item::CItemFactory::CItemFactory(Item::CItemFactory *this); // idb
void __thiscall Item::CItemFactory::~CItemFactory(Item::CItemFactory *this); // idb
void __thiscall Item::CItemFactory::CreateItem(Item::CItemFactory *this, unsigned __int16 usProtoTypeID);
Item::CItem *__thiscall Item::CItemFactory::CreateItem(Item::CItemFactory *this, const char *lpSerializedItem_In, unsigned int *nParseLength_InOut); // idb
void __thiscall Item::CEquipmentsContainer::GetEquipmentView(Item::CEquipmentsContainer *this, unsigned __int16 *usProtoTypeArray, signed int nStartPos, int nCopyNum);
void __thiscall CCharacter::SetDispatcher(CCharacter *this, CGameClientDispatch *lpGameClientDispatch); // idb
char __thiscall CCharacter::ControlOption(CCharacter *this, RejectOption Reject, bool bLogin);
unsigned __int8 __thiscall CCharacter::GetNation(CCharacter *this); // idb
void __thiscall CCharacter::SetPID(CCharacter *this, unsigned int dwPID); // idb
void __thiscall CCharacter::SetGID(CCharacter *this, unsigned int dwGID); // idb
void __thiscall CCharacter::SetFame(CCharacter *this, unsigned int dwFame); // idb
unsigned __int64 __thiscall Position::GetDistance(Position *this, const Position *rhs);
void __thiscall CCharacter::~CCharacter(CCharacter *this); // idb
bool __thiscall CCharacter::Initialize(CCharacter *this, CGameClientDispatch *lpGameClientDispatch); // idb
BOOL __thiscall CCharacter::BindPositionToNPC(CCharacter *this, unsigned int dwNPCID);
void __thiscall CCharacter::CCharacter(CCharacter *this, unsigned int dwCID); // idb
CCharacter *__thiscall CCharacter::`scalar deleting destructor'(CCharacter *this, char a2);
CMsgProc *__thiscall Castle::CCastleMgr::GetCastle(CMsgProcessMgr *this, unsigned int uMsg); // idb
char __thiscall CCharacter::SetDuelOpponent(CCharacter *this, CCharacter *lpCharacter);
unsigned __int16 __thiscall Skill::CProcessTable::UseSkill(Skill::CProcessTable *this, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
void __cdecl LogSkillSlot(const CharacterDBData *DBData); // idb
char __thiscall CCharacter::SkillLock(CCharacter *this, unsigned __int8 Index_In);
char __thiscall CCharacter::SkillUnLock(CCharacter *this, unsigned __int8 Index_In);
void __thiscall CCharacter::CalculatePassiveSkill(CCharacter *this, Item::CEquipment *pEquipment, Item::EquipType::Type eEquipType); // idb
void __thiscall CCharacter::CalculatePassiveSkill(CCharacter *this, Item::CEquipment *pRightEquipment, Item::CEquipment *pLeftEquipment, Item::EquipType::Type eEquipType); // idb
unsigned __int16 __thiscall CCharacter::ReadSkill(CCharacter *this, SKILLSLOT SkillSlot, int wSkillID, unsigned __int16 wSkillLockCount);
char __thiscall CCharacter::RedistributionSkill(CCharacter *this);
char __thiscall CCharacter::SkillCreate(CCharacter *this, Item::CUseItem *pUseItem);
char __thiscall CCharacter::SkillErase(CCharacter *this, unsigned __int8 Index_In);
char __thiscall CCharacter::HasSkill(CCharacter *this, unsigned __int16 usSkillType, unsigned __int8 cLockCount, signed __int8 cLevel);
__int16 __thiscall CCharacter::GetSkillLockCount(CCharacter *this, unsigned __int16 usSkillType); // idb
__int16 __thiscall CCharacter::GetSkillLevel(CCharacter *this, unsigned __int16 usSkillType); // idb
__int16 __thiscall CCharacter::GetSkillSlotIndex(CCharacter *this, unsigned __int16 usSkillType); // idb
void __thiscall CChantSpell::~CChantSpell(CChantSpell *this); // idb
CSpell *__thiscall CSpell::`scalar deleting destructor'(CSpell *this, char a2);
void __thiscall CSpell::Spell_Info::Spell_Info(CSpell::Spell_Info *this, const Skill::ProtoType *SkillProtoType, CAggresiveCreature *lpCaster, unsigned __int8 cSpellType, unsigned __int16 wSpellID, unsigned __int16 wSpellLevel, unsigned __int16 wDurationSec); // idb
char __thiscall CAggresiveCreature::Dead(CAggresiveCreature *this, CAggresiveCreature *pOffencer);
CInvincibleSpell *__thiscall CInvincibleSpell::`scalar deleting destructor'(CInvincibleSpell *this, char a2);
void __thiscall CInvincibleSpell::~CInvincibleSpell(CInvincibleSpell *this); // idb
int __thiscall CCharacter::CalculateFixLevelGap(CCharacter *this, CAggresiveCreature *pDefender); // idb
void __thiscall CCharacter::Casting(CCharacter *this, AtType attackType, AtNode *attackNode); // idb
BOOL __thiscall CCharacter::Dead(CCharacter *this, CCharacter *pOffencer);
char __thiscall CCharacter::AutoRespawn(CCharacter *this);
bool __thiscall CCharacter::UseAmmo(CCharacter *this, Item::CUseItem *pUseItem); // idb
bool __thiscall CCharacter::IsPeaceMode(CCharacter *this); // idb
void __thiscall CCharacter::SetPeaceMode(CCharacter *this, PeaceModeInfo PeaceMode, bool bLogin); // idb
char __thiscall CCharacter::SetPeaceMode(CCharacter *this, bool bPeace); // idb
CCreature::MutualType __thiscall CCharacter::IsEnemy(CCharacter *this, CCharacter *lpTarget);
char __thiscall CCharacter::GetEliteBonus(CCharacter *this); // idb
__int16 __cdecl Skill::CFunctions::ConsumeMP(AtType attackType, CAggresiveCreature *lpSkillUser); // idb
char __thiscall CCharacter::AttackCID(CCharacter *this, AtType attackType, AtNode *attackNode, unsigned __int16 *wError);
char __thiscall CCharacter::Attack(CCharacter *this, AtType attackType, unsigned __int8 cDefenderNum, CAggresiveCreature **ppDefenders, unsigned __int8 *cDefenserJudges);
char __thiscall CCharacter::AttackUsingBow(CCharacter *this, int wType);
bool __thiscall Skill::CAddSpell<CInvincibleSpell>::operator()(Skill::CAddSpell<CInvincibleSpell> *this, CAggresiveCreature *lpCharacter); // idb
char __thiscall CCharacter::Respawn(CCharacter *this, Position Pos, bool bDropExp);
char __thiscall CCharacter::RegenHPAndMP(CCharacter *this, signed __int16 usAdditionalHP, signed __int16 usAdditionalMP, bool bAddDefaultRegenValue);
char __thiscall CCharacter::UpdateMasteryInfo(CCharacter *this, unsigned __int8 cMasteryType, unsigned __int8 cPassiveType);
bool __thiscall CCharacter::CheckEquipable(CCharacter *this, Item::CItem *lpItem); // idb
void __thiscall CCharacter::DegradeRespawnSpeedByEmblem(CCharacter *this); // idb
bool __thiscall CCharacter::CalculateStatus(CCharacter *this); // idb
__int16 __thiscall CCharacter::EquipSkillArm(CCharacter *this, Item::EquipType::Type eEquipType); // idb
void __thiscall CCharacter::UpgradeRespawnSpeedByEmblem(CCharacter *this, unsigned __int8 cUpgradeType, unsigned __int8 cUpgradeStep); // idb
char __thiscall CCharacter::CalculateStatusData(CCharacter *this, bool bFullHPandMP);
char __thiscall CCharacter::ChangeClass(CCharacter *this, unsigned __int8 cClassType);
char __thiscall CCharacter::InitLevel1Char(CCharacter *this, unsigned __int8 cClassType);
char __thiscall CCharacter::IncrementExp(CCharacter *this, unsigned int dwExp);
unsigned __int16 __thiscall CCharacter::AddState(CCharacter *this, unsigned __int8 Type_In, ChState *ChState_Out); // idb
char __thiscall CCharacter::StateRedistribution(CCharacter *this, ChState *State);
char __thiscall CCharacter::StatusRetrain(CCharacter *this, ChState *State, int InvenPos);
char __thiscall CCharacter::ChangeWeaponAndShield(CCharacter *this, unsigned __int8 cSelect);
void __cdecl std::_Random_shuffle<unsigned char *,int>(unsigned __int8 *_First, unsigned __int8 *_Last);
char __thiscall CCharacter::GetHuntingExp(CCharacter *this, unsigned int lpDeadCreature, signed int dwExp, int cMemberNum);
char __thiscall CCharacter::CalculateEquipDurability(CCharacter *this, unsigned int wAttackType);
Item::CItemContainer *__thiscall CCharacter::GetItemContainer(CCharacter *this, unsigned __int8 cPos); // idb
unsigned int __thiscall CCharacter::RepairItem(CCharacter *this, unsigned int dwNPCID, int itemPos);
int __thiscall CCharacter::RepairAllItem(CCharacter *this, unsigned int dwNPCID);
Item::CItem *__thiscall CCharacter::SellToCharacter(CCharacter *this, CCharacter *lpCustomer, unsigned __int16 wKindItem, __int64 takeType, unsigned int *dwPrice);
char __thiscall CCharacter::Login(CCharacter *this, CCharacter::CellLoginStatus eLoginStatus);
bool __thiscall CCharacter::Logout(CCharacter *this, DBUpdateData::UpdateType eUpdateType); // idb
char __thiscall CCharacter::CharacterCellLogin(CCharacter *this);
// unsigned __int8 __usercall Creature::GetCreatureType@<al>(int dwCID@<eax>);
void __thiscall CalculateDamageInfo::CalculateDamageInfo(CalculateDamageInfo *this, bool bForceDRC, float fDRC, __int16 nOffenceRevision, __int16 nMinDamage, __int16 nMaxDamage); // idb
void __thiscall CCreature::~CCreature(CCreature *this); // idb
CCreature *__thiscall CCreature::`scalar deleting destructor'(CCreature *this, char a2);
bool __thiscall CellPosition::MoveTo(CellPosition *this, const Position *WorldPos); // idb
void __thiscall CAggresiveCreature::CAggresiveCreature(CAggresiveCreature *this, unsigned int dwCID); // idb
char __thiscall CAggresiveCreature::CalculateEquipDurability(CAggresiveCreature *this, unsigned __int16 wType);
__int16 __thiscall CAggresiveCreature::EquipSkillArm(CAggresiveCreature *this, Item::EquipType::Type eEquipType); // idb
char __thiscall CAggresiveCreature::HasSkill(CAggresiveCreature *this, unsigned __int16 usSkillType, unsigned __int8 cLockCount, unsigned __int8 cLevel);
unsigned __int16 __thiscall CAggresiveCreature::GetClass(CAggresiveCreature *this); // idb
void __thiscall CAggresiveCreature::~CAggresiveCreature(CAggresiveCreature *this); // idb
char __thiscall CAggresiveCreature::RegenHPAndMP(CAggresiveCreature *this, signed __int16 usAdditionalHP, signed __int16 usAdditionalMP, bool bAddDefaultRegenValue);
CAggresiveCreature *__thiscall CAggresiveCreature::`scalar deleting destructor'(CAggresiveCreature *this, char a2);
int __thiscall CAggresiveCreature::MoveTo(CAggresiveCreature *this, const Position *NewPosition, bool bSitDown);
int __thiscall CAggresiveCreature::CalculateLevelGap(CAggresiveCreature *this, int nOffencerLevel, int nDefenderLevel); // idb
double __thiscall CAggresiveCreature::CalculateLevelGapAffect(CAggresiveCreature *this, CAggresiveCreature *pDefender);
unsigned __int16 __thiscall CAggresiveCreature::CalculateDamage(CAggresiveCreature *this, int pOffencer, float AddEffectInfo, float cDefenserJudge);
double __thiscall CAggresiveCreature::CalcDir2D(CAggresiveCreature *this, float fSrcX, float fSrcY, float fDstX, float fDstY);
int __thiscall CAggresiveCreature::CalculateLevelGap(CAggresiveCreature *this, int pDefender);
unsigned __int16 __thiscall CAggresiveCreature::ApplyDamage(CAggresiveCreature *this, __int64 attackType, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError);
bool __thiscall CAggresiveCreature::MultiAttack(CAggresiveCreature *this, AtType attackType, int nDefenderNum, CAggresiveCreature **ppDefenders, CCell *cDefenserJudges, Position CenterPos, float fDir, float nRange, float fAngle, char cTargetType);
void __thiscall CPacketDispatch::~CPacketDispatch(CPacketDispatch *this); // idb
CPacketDispatch *__thiscall CPacketDispatch::`vector deleting destructor'(CPacketDispatch *this, char a2);
void __thiscall CRylServerDispatch::CRylServerDispatch(CRylServerDispatch *this, CSession *Session, unsigned int dwMaxProcessPacketPerPulse); // idb
INET_Addr *__thiscall CRylServerDispatch::GetRemoteAddr(CRylServerDispatch *this); // idb
bool __thiscall CRylServerDispatch::Shutdown(CRylServerDispatch *this); // idb
void __thiscall CRylServerDispatch::CloseSession(CRylServerDispatch *this); // idb
void __thiscall CRylServerDispatch::LogErrorPacket(CRylServerDispatch *this, const char *szDetailText, unsigned __int8 cCmd);
void __thiscall CRylServerDispatch::LogErrorPacketCreation(CRylServerDispatch *this, CRylServerDispatch::CreationResult eResult); // idb
BOOL __cdecl greator_second<std::pair<unsigned long,unsigned long>>(const std::pair<unsigned long,unsigned long> *lhspair, const std::pair<unsigned long,unsigned long> *rhspair);
void __thiscall CRylServerDispatch::~CRylServerDispatch(CRylServerDispatch *this); // idb
int __cdecl CRylServerDispatch::CreatePacket(CBufferFactory *bufferFactory, CBufferQueue *bufferQueue, int lpStream_In, unsigned int *dwStreamSize_InOut);
char __thiscall CRylServerDispatch::ParsePacket(CRylServerDispatch *this, char *const lpStream_In, unsigned int *dwStreamSize_InOut);
char __thiscall CRylServerDispatch::Dispatch(CRylServerDispatch *this);
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, int _Hole, int _Top, std::pair<unsigned long,unsigned long> _Val, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __cdecl std::_Rotate<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last);
CRylServerDispatch *__thiscall CRylServerDispatch::`vector deleting destructor'(CRylServerDispatch *this, char a2);
void __cdecl std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(std::pair<unsigned long,unsigned long> *_First, unsigned int _Count, const std::pair<unsigned long,unsigned long> *_Val);
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, int _Hole, int _Bottom, std::pair<unsigned long,unsigned long> _Val, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __cdecl std::_Make_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,std::pair<unsigned long,unsigned long>,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *));
void __cdecl std::_Med3<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
std::pair<unsigned long,unsigned long> *__thiscall std::vector<std::pair<enum eStdFunc,int>>::_Ufill(std::vector<std::pair<unsigned long,unsigned long>> *this, std::pair<unsigned long,unsigned long> *_Ptr, unsigned int _Count, const std::pair<unsigned long,unsigned long> *_Val); // idb
void __cdecl std::_Insertion_sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __cdecl std::_Median<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Mid, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> *__cdecl std::_Unguarded_partition<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::pair<std::vector<std::pair<unsigned long,unsigned long>>::iterator,std::vector<std::pair<unsigned long,unsigned long>>::iterator> *result, std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,unsigned long>>::iterator,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __cdecl std::_Sort<std::vector<std::pair<unsigned long,unsigned long>>::iterator,int,bool (__cdecl *)(std::pair<unsigned long,unsigned long> const &,std::pair<unsigned long,unsigned long> const &)>(std::vector<std::pair<unsigned long,unsigned long>>::iterator _First, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Last, int _Ideal, bool (__cdecl *_Pred)(const std::pair<unsigned long,unsigned long> *, const std::pair<unsigned long,unsigned long> *)); // idb
void __thiscall __noreturn std::vector<std::pair<unsigned long,unsigned long>>::_Xlen(std::vector<std::pair<unsigned long,unsigned long>> *this);
void __thiscall std::vector<std::pair<unsigned long,unsigned long>>::_Insert_n(std::vector<std::pair<unsigned long,unsigned long>> *this, std::vector<std::pair<unsigned long,unsigned long>>::iterator _Where, unsigned int _Count, const std::pair<unsigned long,unsigned long> *_Val); // idb
void __thiscall std::vector<std::pair<unsigned long,unsigned long>>::reserve(std::vector<std::pair<unsigned long,unsigned long>> *this, unsigned int _Count); // idb
void __thiscall CRylServerDispatch::ProcessTooManyPacket(CRylServerDispatch *this, CBufferQueue *bufferQueue); // idb
int __thiscall CGameClientDispatchTable::Initialize(CGameClientDispatchTable *this);
void __thiscall CGameClientDispatch::Disconnected(CGameClientDispatch *this); // idb
void __thiscall CGameClientDispatch::Disconnect(CGameClientDispatch *this); // idb
char __thiscall CGameClientDispatch::Dispatch(CGameClientDispatch *this);
bool __cdecl LogFailDispatch(CGameClientDispatch *GameClientDispatch, const char *szDetailText, unsigned __int8 cCmd);
void __thiscall CGameClientDispatch::PrintGameGuardError(CGameClientDispatch *this); // idb
void __thiscall CGameClientDispatch::SetCharacter(CGameClientDispatch *this, CCharacter *lpCharacter); // idb
void __thiscall CCSLock::~CCSLock(CCheckPing *this); // idb
char __thiscall CGameClientDispatch::DispatchPacket(CGameClientDispatch *this, PktMU *lpPktBase);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *_Rootnode); // idb
void __thiscall std::deque<unsigned long>::_Tidy(std::deque<CSpeedHackCheck::SkillHistory> *this); // idb
unsigned int __thiscall CGameClientDispatch::PopRequestKey(CGameClientDispatch *this); // idb
void __thiscall __noreturn std::deque<unsigned long>::_Xlen(std::deque<unsigned long> *this);
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Where); // idb
void __thiscall CSpeedHackCheck::~CSpeedHackCheck(CSpeedHackCheck *this); // idb
void __thiscall CGameClientDispatch::~CGameClientDispatch(CGameClientDispatch *this); // idb
void __thiscall std::deque<unsigned long>::_Growmap(std::deque<unsigned long> *this, unsigned int _Count); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator,bool> *result, std::pair<unsigned long const ,unsigned long> *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Last); // idb
void __thiscall CGameClientDispatch::CGameClientDispatch(CGameClientDispatch *this, CSession *Session, CGameClientDispatchTable *GameClientDispatchTable); // idb
CGameClientDispatch *__thiscall CGameClientDispatch::`vector deleting destructor'(CGameClientDispatch *this, char a2);
void __thiscall std::deque<unsigned long>::push_back(std::deque<unsigned long> *this, unsigned int *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,0> >::iterator _Where, std::pair<unsigned long const ,unsigned long> *_Val); // idb
void __thiscall CGameClientDispatch::PushRequestKey(CGameClientDispatch *this, unsigned int dwRequestKey); // idb
unsigned int *__thiscall std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>::operator[](std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> > > *this, const unsigned int *_Keyval); // idb
void __thiscall std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>::~map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>>(std::map<unsigned long,unsigned long,std::greater<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> > > *this); // idb
void __thiscall CGameClientDispatch::ProcessTooManyPacket(CGameClientDispatch *this, CBufferQueue *bufferQueue); // idb
BOOL __thiscall CDBRequest::IsValid(CDBRequest *this);
char __cdecl GameClientParsePacket::ParseCharLogin(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCharMoveZone(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
bool __cdecl GameClientParsePacket::ParseCharLogout(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase); // idb
char __cdecl GameClientParsePacket::ParseServerZone(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
char __cdecl GameClientParsePacket::ParseCSAuth(CGameClientDispatch *GameClientDispatch, PktBase *lpPktBase);
void __thiscall CBattleSongSpell::CBattleSongSpell(CBattleSongSpell *this, CSpell::Spell_Info *spell_Info, __int16 nConsumeMPAmount); // idb
CBattleSongSpell *__thiscall CBattleSongSpell::`vector deleting destructor'(CBattleSongSpell *this, char a2);
void __thiscall CBattleSongSpell::~CBattleSongSpell(CBattleSongSpell *this); // idb
void __thiscall CMaintenanceChantSpell::CMaintenanceChantSpell(CMaintenanceChantSpell *this, CSpell::Spell_Info *spell_Info, __int16 nConsumeMPAmount); // idb
CMaintenanceChantSpell *__thiscall CMaintenanceChantSpell::`vector deleting destructor'(CMaintenanceChantSpell *this, char a2);
void __thiscall CMaintenanceChantSpell::~CMaintenanceChantSpell(CMaintenanceChantSpell *this); // idb
void __thiscall CAccelerationChantSpell::CAccelerationChantSpell(CAccelerationChantSpell *this, CSpell::Spell_Info *spell_Info, __int16 nConsumeMPAmount); // idb
CAccelerationChantSpell *__thiscall CAccelerationChantSpell::`vector deleting destructor'(CAccelerationChantSpell *this, char a2);
void __thiscall CAccelerationChantSpell::~CAccelerationChantSpell(CAccelerationChantSpell *this); // idb
void __thiscall CLifeAuraSpell::CLifeAuraSpell(CLifeAuraSpell *this, CSpell::Spell_Info *spell_Info, __int16 nConsumeMPAmount); // idb
CLifeAuraSpell *__thiscall CLifeAuraSpell::`vector deleting destructor'(CLifeAuraSpell *this, char a2);
void __thiscall CLifeAuraSpell::~CLifeAuraSpell(CLifeAuraSpell *this); // idb
CDefencePotionSpell *__thiscall CDefencePotionSpell::`scalar deleting destructor'(CDefencePotionSpell *this, char a2);
void __thiscall CDefencePotionSpell::~CDefencePotionSpell(CDefencePotionSpell *this); // idb
CDisenchantPotionSpell *__thiscall CDisenchantPotionSpell::`vector deleting destructor'(CDisenchantPotionSpell *this, char a2);
void __thiscall CDisenchantPotionSpell::~CDisenchantPotionSpell(CDisenchantPotionSpell *this); // idb
CMagicPotionSpell *__thiscall CMagicPotionSpell::`vector deleting destructor'(CMagicPotionSpell *this, char a2);
void __thiscall CMagicPotionSpell::~CMagicPotionSpell(CMagicPotionSpell *this); // idb
CLightningPotionSpell *__thiscall CLightningPotionSpell::`vector deleting destructor'(CLightningPotionSpell *this, char a2);
void __thiscall CLightningPotionSpell::~CLightningPotionSpell(CLightningPotionSpell *this); // idb
CRegenerationSpell *__thiscall CRegenerationSpell::`vector deleting destructor'(CRegenerationSpell *this, char a2);
void __thiscall CRegenerationSpell::~CRegenerationSpell(CRegenerationSpell *this); // idb
CStrengthSpell *__thiscall CStrengthSpell::`scalar deleting destructor'(CStrengthSpell *this, char a2);
void __thiscall CStrengthSpell::~CStrengthSpell(CStrengthSpell *this); // idb
CBlazeSpell *__thiscall CBlazeSpell::`scalar deleting destructor'(CBlazeSpell *this, char a2);
void __thiscall CBlazeSpell::~CBlazeSpell(CBlazeSpell *this); // idb
CChargingSpell *__thiscall CChargingSpell::`vector deleting destructor'(CChargingSpell *this, char a2);
void __thiscall CChargingSpell::~CChargingSpell(CChargingSpell *this); // idb
CStealthSpell *__thiscall CStealthSpell::`vector deleting destructor'(CStealthSpell *this, char a2);
void __thiscall CStealthSpell::~CStealthSpell(CStealthSpell *this); // idb
CManaShellSpell *__thiscall CManaShellSpell::`vector deleting destructor'(CManaShellSpell *this, char a2);
void __thiscall CManaShellSpell::~CManaShellSpell(CManaShellSpell *this); // idb
CEncourageSpell *__thiscall CEncourageSpell::`vector deleting destructor'(CEncourageSpell *this, char a2);
void __thiscall CEncourageSpell::~CEncourageSpell(CEncourageSpell *this); // idb
CEnchantWeaponSpell *__thiscall CEnchantWeaponSpell::`vector deleting destructor'(CEnchantWeaponSpell *this, char a2);
void __thiscall CEnchantWeaponSpell::~CEnchantWeaponSpell(CEnchantWeaponSpell *this); // idb
CBrightArmorSpell *__thiscall CBrightArmorSpell::`scalar deleting destructor'(CBrightArmorSpell *this, char a2);
void __thiscall CBrightArmorSpell::~CBrightArmorSpell(CBrightArmorSpell *this); // idb
CHardenSkinSpell *__thiscall CHardenSkinSpell::`scalar deleting destructor'(CHardenSkinSpell *this, char a2);
void __thiscall CHardenSkinSpell::~CHardenSkinSpell(CHardenSkinSpell *this); // idb
CFlexibilitySpell *__thiscall CFlexibilitySpell::`scalar deleting destructor'(CFlexibilitySpell *this, char a2);
void __thiscall CFlexibilitySpell::~CFlexibilitySpell(CFlexibilitySpell *this); // idb
void __thiscall CGuardSpell::CGuardSpell(CGuardSpell *this, CSpell::Spell_Info *spell_Info); // idb
CGuardSpell *__thiscall CGuardSpell::`vector deleting destructor'(CGuardSpell *this, char a2);
void __thiscall CGuardSpell::~CGuardSpell(CGuardSpell *this); // idb
CSlowSpell *__thiscall CSlowSpell::`scalar deleting destructor'(CSlowSpell *this, char a2);
void __thiscall CSlowSpell::~CSlowSpell(CSlowSpell *this); // idb
CArmorBrokenSpell *__thiscall CArmorBrokenSpell::`vector deleting destructor'(CArmorBrokenSpell *this, char a2);
void __thiscall CArmorBrokenSpell::~CArmorBrokenSpell(CArmorBrokenSpell *this); // idb
CHoldSpell *__thiscall CHoldSpell::`scalar deleting destructor'(CHoldSpell *this, char a2);
void __thiscall CHoldSpell::~CHoldSpell(CHoldSpell *this); // idb
CStunSpell *__thiscall CStunSpell::`scalar deleting destructor'(CStunSpell *this, char a2);
void __thiscall CStunSpell::~CStunSpell(CStunSpell *this); // idb
CFrozenSpell *__thiscall CFrozenSpell::`vector deleting destructor'(CFrozenSpell *this, char a2);
void __thiscall CFrozenSpell::~CFrozenSpell(CFrozenSpell *this); // idb
CPoisonedSpell *__thiscall CPoisonedSpell::`vector deleting destructor'(CPoisonedSpell *this, char a2);
void __thiscall CPoisonedSpell::~CPoisonedSpell(CPoisonedSpell *this); // idb
CLowerStrengthSpell *__thiscall CLowerStrengthSpell::`vector deleting destructor'(CLowerStrengthSpell *this, char a2);
void __thiscall CLowerStrengthSpell::~CLowerStrengthSpell(CLowerStrengthSpell *this); // idb
void __thiscall Skill::CProcessTable::ProcessInfo::ProcessInfo(Skill::CProcessTable::ProcessInfo *this); // idb
double __cdecl Skill::CFunctions::ResistanceFactor(CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
void __cdecl Skill::CFunctions::WeaponMastery(AtType attackType, CAggresiveCreature *lpSkillUser); // idb
const Skill::CProcessTable::ProcessInfo *__thiscall Skill::CProcessTable::GetProcessInfo(Skill::CProcessTable *this, unsigned __int16 usSkill_ID); // idb
unsigned __int16 __cdecl Skill::CFunctions::SwordMastery(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::AxeMastery(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::BluntMastery(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::BloodyMana(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::DaggerMastery(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::Disenchant(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::CrushWeapon(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::Blade(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::Toughness(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::ClawMastery(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::UseFood(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::UseFood2(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::RefreshmentPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::MoonCake(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::FireCracker(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
void __thiscall Skill::CAddSpell<CRegenerationSpell>::CAddSpell<CRegenerationSpell>(Skill::CAddSpell<CPoisonedSpell> *this, const CSpell::Spell_Info *Spell_Info); // idb
CConsoleCMDFactory::StringCMD *__cdecl std::_Copy_backward_opt<CTokenlizedFile::ColumnInfo *,CTokenlizedFile::ColumnInfo *>(CConsoleCMDFactory::StringCMD *_First, CConsoleCMDFactory::StringCMD *_Last, CConsoleCMDFactory::StringCMD *_Dest);
void __thiscall Skill::CProcessTable::CProcessTable(Skill::CProcessTable *this); // idb
void __thiscall Skill::CProcessTable::~CProcessTable(Skill::CProcessTable *this); // idb
unsigned __int16 __cdecl Skill::CFunctions::Detection(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::NeedleSpit(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
__int16 __cdecl Skill::CFunctions::VampiricTouch(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge);
unsigned __int16 __cdecl Skill::CFunctions::Purification(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::FirstAid(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
__int16 __cdecl Skill::CFunctions::SharedPain(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::BattleSong(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::BackStab(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::AimedShot(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::DualShot(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::Recall(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CCharacter *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::FireBolt(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::DeathRay(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::SummonKindling(const Skill::ProtoType *ProtoType, AtType attackType, CCharacter *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::Resurrection(const Skill::ProtoType *ProtoType, AtType attackType, CCharacter *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError);
unsigned __int16 __cdecl Skill::CFunctions::MaintenanceChant(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::AccelerationChant(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::ManaFlow(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::CureWounds(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::WoundsCrafting(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::CureLight(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::WoundsMake(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::SplitLife(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::Dispel(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::MagicMissile(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::LifeAura(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::ManaConvert(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::FireRing(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::Blast(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::Shock(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::Crevice(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::WoundsBlast(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::DaggerFire(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::HealingPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::RedMoonCake(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
unsigned __int16 __cdecl Skill::CFunctions::BlueMoonCake(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge);
bool __thiscall Skill::CAddSpell<CSlowSpell>::operator()(Skill::CAddSpell<CSlowSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CHoldSpell>::operator()(Skill::CAddSpell<CHoldSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CArmorBrokenSpell>::operator()(Skill::CAddSpell<CArmorBrokenSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CStunSpell>::operator()(Skill::CAddSpell<CStunSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CManaShellSpell>::operator()(Skill::CAddSpell<CManaShellSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CEncourageSpell>::operator()(Skill::CAddSpell<CEncourageSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CChargingSpell>::operator()(Skill::CAddSpell<CChargingSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CBlazeSpell>::operator()(Skill::CAddSpell<CBlazeSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CStealthSpell>::operator()(Skill::CAddSpell<CStealthSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CFrozenSpell>::operator()(Skill::CAddSpell<CFrozenSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CLowerStrengthSpell>::operator()(Skill::CAddSpell<CLowerStrengthSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CEnchantWeaponSpell>::operator()(Skill::CAddSpell<CEnchantWeaponSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CBrightArmorSpell>::operator()(Skill::CAddSpell<CBrightArmorSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CHardenSkinSpell>::operator()(Skill::CAddSpell<CHardenSkinSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CFlexibilitySpell>::operator()(Skill::CAddSpell<CFlexibilitySpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CRegenerationSpell>::operator()(Skill::CAddSpell<CRegenerationSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CGuardSpell>::operator()(Skill::CAddSpell<CGuardSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CPoisonedSpell>::operator()(Skill::CAddSpell<CPoisonedSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CStrengthSpell>::operator()(Skill::CAddSpell<CStrengthSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CDefencePotionSpell>::operator()(Skill::CAddSpell<CDefencePotionSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CDisenchantPotionSpell>::operator()(Skill::CAddSpell<CDisenchantPotionSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CMagicPotionSpell>::operator()(Skill::CAddSpell<CMagicPotionSpell> *this, CAggresiveCreature *lpCharacter); // idb
bool __thiscall Skill::CAddSpell<CLightningPotionSpell>::operator()(Skill::CAddSpell<CLightningPotionSpell> *this, CAggresiveCreature *lpCharacter); // idb
CConsoleCMDFactory::StringCMD *__cdecl std::_Uninit_copy<Skill::CProcessTable::ProcessInfo *,Skill::CProcessTable::ProcessInfo *,std::allocator<Skill::CProcessTable::ProcessInfo>>(CConsoleCMDFactory::StringCMD *_First, CConsoleCMDFactory::StringCMD *_Last, CConsoleCMDFactory::StringCMD *_Dest);
void __cdecl std::_Med3<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Mid, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
void __cdecl std::_Push_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, int _Hole, int _Top, Skill::CProcessTable::ProcessInfo _Val); // idb
bool __cdecl Skill::CFunctions::SlowlySkillAttack(const Skill::ProtoType *ProtoType, unsigned int attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim);
unsigned __int16 __cdecl Skill::CFunctions::Net(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::HardHit(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::ManaShell(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Grease(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Encourage(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::HammerOfLight(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Charging(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::FullSwing(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Blaze(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::ChainAction(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Stealth(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Camouflage(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::LightningArrow(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::FrostBolt(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Entangle(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::LowerStrength(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::EnchantWeapon(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Shatter(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::BrightArmor(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Dazzle(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::HardenSkin(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Flexibility(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Regeneration(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Guard(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::FastHit(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::PowerDrain(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::RingGeyser(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Rot(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Shackle(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::Flash(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::StrengthPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::DefencePotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::DisenchantPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::MagicPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
unsigned __int16 __cdecl Skill::CFunctions::LightningPotion(const Skill::ProtoType *ProtoType, AtType attackType, CAggresiveCreature *lpSkillUser, CAggresiveCreature *lpVictim, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
Skill::CProcessTable::ProcessInfo *__cdecl std::_Copy_opt<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last, Skill::CProcessTable::ProcessInfo *_Dest);
void __cdecl std::_Median<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Mid, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, int _Hole, int _Bottom, Skill::CProcessTable::ProcessInfo _Val); // idb
Skill::CProcessTable::ProcessInfo *__cdecl std::copy<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,Skill::CProcessTable::ProcessInfo *>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last, Skill::CProcessTable::ProcessInfo *_Dest); // idb
std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::pair<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,std::vector<Skill::CProcessTable::ProcessInfo>::iterator> *result, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int,Skill::CProcessTable::ProcessInfo>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last);
void __cdecl std::_Insertion_sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
void __cdecl std::sort_heap<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
void __thiscall __noreturn std::vector<Skill::CProcessTable::ProcessInfo>::_Xlen(std::vector<Skill::CProcessTable::ProcessInfo> *this);
void __cdecl std::_Sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator,int>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<Skill::CProcessTable::ProcessInfo>::_Insert_n(std::vector<Skill::CProcessTable::ProcessInfo> *this, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Where, unsigned int _Count, const Skill::CProcessTable::ProcessInfo *_Val); // idb
void __cdecl std::sort<std::vector<Skill::CProcessTable::ProcessInfo>::iterator>(std::vector<Skill::CProcessTable::ProcessInfo>::iterator _First, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Last); // idb
std::vector<Skill::CProcessTable::ProcessInfo>::iterator *__thiscall std::vector<Skill::CProcessTable::ProcessInfo>::insert(std::vector<Skill::CProcessTable::ProcessInfo> *this, std::vector<Skill::CProcessTable::ProcessInfo>::iterator *result, std::vector<Skill::CProcessTable::ProcessInfo>::iterator _Where, const Skill::CProcessTable::ProcessInfo *_Val); // idb
void __thiscall std::vector<Skill::CProcessTable::ProcessInfo>::push_back(std::vector<Skill::CProcessTable::ProcessInfo> *this, const CConsoleCMDFactory::StringCMD *_Val);
char __thiscall Skill::CProcessTable::InsertSkill(Skill::CProcessTable *this, std::vector<Skill::CProcessTable::ProcessInfo> *processVector, unsigned __int16 usSkill_ID, unsigned __int16 (__cdecl *fnProcess)(const Skill::ProtoType *, AtType, CAggresiveCreature *, CAggresiveCreature *, unsigned __int8 *, unsigned __int8 *, unsigned __int16 *));
char __thiscall Skill::CProcessTable::Initialize(Skill::CProcessTable *this);
void __thiscall std::locale::facet::_Incref(std::locale::facet *this); // idb
std::locale::facet *__thiscall std::locale::facet::_Decref(std::locale::facet *this); // idb
void __thiscall std::locale::~locale(std::locale *this); // idb
std::locale::facet *__thiscall std::locale::facet::`scalar deleting destructor'(std::locale::facet *this, char a2);
char __cdecl CConsoleCMDSingleton<CCMDReloadSetup>::Destroy();
std::locale *__thiscall std::ios_base::getloc(std::ios_base *this, std::locale *result); // idb
std::ios_base *__thiscall std::ios_base::`vector deleting destructor'(std::ios_base *this, char a2);
unsigned int __cdecl Math::Convert::StrToHex32(const char *szSrc); // idb
int __cdecl Math::Convert::Atoi(char *szSrc);
bool __thiscall CCellManager::CheckPositionInZone(CCellManager *this, Position Pos); // idb
char __thiscall CCellManager::CreateCell(CCellManager *this);
char __thiscall CCellManager::SummonMonster(CCellManager *this, int nKID, Position Pos, CCharacter *lpMaster);
CCell *__thiscall CCellManager::GetCell(CCellManager *this, unsigned __int16 wMapIndex, unsigned __int8 cCellX, unsigned __int8 cCellZ); // idb
CCell *__thiscall CCellManager::GetCell(CCellManager *this, unsigned __int16 wMapIndex, unsigned int dwPosX, unsigned int dwPosY, unsigned int dwPosZ); // idb
CCell *__thiscall CCellManager::GetCell(CCellManager *this, unsigned __int64 nItemID); // idb
CBuffer *__thiscall CCellManager::ReallocateBuffer(CCellManager *this, CBuffer *lpOldBuffer, unsigned int dwReserveSize); // idb
CBuffer *__thiscall CCellManager::GetBroadCastBuffer(CCellManager *this, unsigned int dwReserveSize); // idb
CBuffer *__thiscall CCellManager::GetBroadCastCompressBuffer(CCellManager *this, unsigned int dwReserveSize); // idb
void __thiscall std::ios::~ios<char,std::char_traits<char>>(std::ios *this); // idb
void __thiscall std::istream::~istream<char,std::char_traits<char>>(std::istream *this); // idb
void __thiscall std::iostream::~basic_iostream<char,std::char_traits<char>>(std::iostream *this); // idb
std::ios *__thiscall std::ios::`scalar deleting destructor'(std::ios *this, char a2);
std::locale *__thiscall std::streambuf::getloc(std::streambuf *this, std::locale *result); // idb
void __thiscall std::ctype<char>::ctype<char>(std::ctype<char> *this, const __int16 *_Table, bool _Deletetable, unsigned int _Refs); // idb
int __thiscall std::ctype<char>::do_tolower(std::ctype<char> *this, unsigned __int8 _Ch);
char *__thiscall std::ctype<char>::do_tolower(std::ctype<char> *this, char *_First, char *_Last);
int __thiscall std::ctype<char>::do_toupper(std::ctype<char> *this, unsigned __int8 _Ch);
char *__thiscall std::ctype<char>::do_toupper(std::ctype<char> *this, char *_First, char *_Last);
char __thiscall std::ctype<char>::do_widen(std::ctype<char> *this, char _Byte); // idb
const char *__thiscall std::ctype<char>::do_widen(std::ctype<char> *this, const char *_First, const char *_Last, char *_Dest); // idb
char __thiscall std::ctype<char>::do_narrow(std::ctype<char> *this, char _Ch, char __formal); // idb
const char *__thiscall std::ctype<char>::do_narrow(std::ctype<char> *this, const char *_First, const char *_Last, char __formal, char *_Dest); // idb
unsigned int __cdecl std::ctype<char>::_Getcat(const std::locale::facet **_Ppf); // idb
std::ctype<char> *__thiscall std::ctype<char>::`vector deleting destructor'(std::ctype<char> *this, char a2);
void __thiscall std::ctype<char>::~ctype<char>(std::ctype<char> *this); // idb
char __thiscall CCellManager::InitAI(CCellManager *this);
char __thiscall CCellManager::LoginMonster(CCellManager *this, char *szFileName, int wMapIndex);
bool __thiscall CCellManager::AdminSummonMonster(CCellManager *this, int nKID, Position Pos); // idb
void __thiscall std::streambuf::~streambuf<char,std::char_traits<char>>(std::streambuf *this); // idb
int __thiscall std::streambuf::overflow(std::streambuf *this, int __formal); // idb
int __thiscall std::streambuf::underflow(std::streambuf *this); // idb
std::streambuf *__thiscall std::streambuf::setbuf(std::streambuf *this, char *__formal, int a3); // idb
int __thiscall std::streambuf::uflow(std::streambuf *this); // idb
char *__thiscall std::streambuf::_Gninc(std::streambuf *this); // idb
int __thiscall std::streambuf::xsgetn(std::streambuf *this, char *_Ptr, int _Count); // idb
int __thiscall std::streambuf::xsputn(std::streambuf *this, const char *_Ptr, int _Count); // idb
std::fpos<int> *__thiscall std::streambuf::seekoff(std::streambuf *this, std::fpos<int> *result, int __formal, int a4, int a5); // idb
std::fpos<int> *__thiscall std::streambuf::seekpos(std::streambuf *this, std::fpos<int> *result, std::fpos<int> __formal, int a4); // idb
char *__thiscall std::ostream::`vector deleting destructor'(std::ios *this, char a2);
std::ostream **__thiscall std::istream::`vector deleting destructor'(std::ios *this, char a2);
std::streambuf *__thiscall std::streambuf::`vector deleting destructor'(std::streambuf *this, char a2);
_BYTE *__thiscall std::iostream::`vector deleting destructor'(std::iostream *this, char a2);
void __thiscall std::streambuf::_Init(std::streambuf *this); // idb
const std::ctype<char> *__cdecl std::use_facet<std::ctype<char>>(std::locale *_Loc); // idb
void __thiscall std::num_put<char,std::ostreambuf_iterator<char>>::num_put<char,std::ostreambuf_iterator<char>>(std::num_put<char,std::ostreambuf_iterator<char> > *this, unsigned int _Refs); // idb
std::codecvt<char,char,int> *__thiscall std::codecvt<char,char,int>::`vector deleting destructor'(std::codecvt<char,char,int> *this, char a2);
void __thiscall std::codecvt<char,char,int>::codecvt<char,char,int>(std::codecvt<char,char,int> *this, unsigned int _Refs); // idb
int __thiscall std::codecvt<char,char,int>::do_out(std::codecvt<char,char,int> *this, int *__formal, const char *_First1, const char *a4, const char **_Mid1, char *_First2, char *a7, char **_Mid2); // idb
int __thiscall std::codecvt<char,char,int>::do_unshift(std::codecvt<char,char,int> *this, int *__formal, char *a3, char *a4, char **a5); // idb
int __thiscall std::codecvt<char,char,int>::do_length(std::codecvt<char,char,int> *this, const int *__formal, const char *_First1, const char *_Last1, unsigned int _Count); // idb
void __thiscall std::codecvt_base::~codecvt_base(std::codecvt<char,char,int> *this); // idb
void __thiscall std::streambuf::streambuf(std::streambuf *this); // idb
int __thiscall std::ios::widen(std::ios *this, int _Byte);
unsigned int __cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Getcat(const std::locale::facet **_Ppf); // idb
unsigned int __cdecl std::codecvt<char,char,int>::_Getcat(const std::locale::facet **_Ppf); // idb
char __thiscall CCellManager::IsSafetyZone(CCellManager *this, Position Pos);
void __thiscall std::string::string(std::string *this); // idb
void __thiscall std::filebuf::filebuf(std::filebuf *this, _iobuf *_File); // idb
std::filebuf *__thiscall std::filebuf::setbuf(std::filebuf *this, char *_Buffer, int _Count);
int __thiscall std::filebuf::sync(std::filebuf *this); // idb
int __thiscall std::filebuf::pbackfail(std::filebuf *this, int _Meta); // idb
char *__thiscall std::streambuf::_Gndec(std::streambuf *this); // idb
int __thiscall std::filebuf::underflow(std::filebuf *this); // idb
void __thiscall std::filebuf::_Initcvt(std::filebuf *this, std::codecvt<char,char,int> *_Newpcvt); // idb
void __thiscall std::ios::init(std::ios *this, std::streambuf *_Strbuf, bool _Isstd); // idb
std::ostream *__cdecl std::operator<<<std::char_traits<char>>(std::ostream *_Ostr, unsigned __int8 _Ch);
const std::num_put<char,std::ostreambuf_iterator<char> > *__cdecl std::use_facet<std::num_put<char,std::ostreambuf_iterator<char>>>(std::locale *_Loc); // idb
const std::codecvt<char,char,int> *__cdecl std::use_facet<std::codecvt<char,char,int>>(std::locale *_Loc); // idb
std::ostream *__thiscall std::ostream::operator<<(std::ostream *this, int _Val); // idb
std::ostream *__thiscall std::ostream::operator<<(std::ostream *this, unsigned int _Val); // idb
std::ostream *__thiscall std::ostream::put(std::ostream *this, unsigned __int8 _Ch);
void __thiscall std::vector<Item::ItemInfo>::~vector<Item::ItemInfo>(CBanList *this); // idb
void __thiscall std::filebuf::imbue(std::filebuf *this, std::locale *_Loc); // idb
// std::string *__userpurge std::string::append@<eax>(std::string *this@<ecx>, int a2@<ebx>, unsigned int _Count, char _Ch);
std::filebuf *__thiscall std::filebuf::open(std::filebuf *this, const char *_Filename, int _Mode, int _Prot); // idb
void __thiscall std::ostream::ostream(std::ostream *this, std::streambuf *_Strbuf, bool _Isstd, int a4);
int __thiscall std::filebuf::uflow(std::filebuf *this); // idb
void __thiscall std::iostream::basic_iostream<char>(std::iostream *this, std::streambuf *_Strbuf, int a3);
char __thiscall std::filebuf::_Endwrite(std::filebuf *this);
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, int _Fill, bool _Val);
std::ostreambuf_iterator<char> *__thiscall std::ostreambuf_iterator<char>::operator=(std::ostreambuf_iterator<char> *this, unsigned __int8 _Right);
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Rep(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, unsigned __int8 _Ch, unsigned int _Count);
std::string *__thiscall std::numpunct<char>::falsename(std::numpunct<char> *this, std::string *result); // idb
std::string *__thiscall std::numpunct<char>::truename(std::numpunct<char> *this, std::string *result); // idb
const std::numpunct<char> *__cdecl std::use_facet<std::numpunct<char>>(std::locale *_Loc); // idb
unsigned int __cdecl std::numpunct<char>::_Getcat(std::numpunct<char> **_Ppf);
char __thiscall std::numpunct<char>::do_decimal_point(std::numpunct<char> *this); // idb
char __thiscall std::numpunct<char>::do_thousands_sep(std::numpunct<char> *this); // idb
std::numpunct<char> *__thiscall std::numpunct<char>::`scalar deleting destructor'(std::numpunct<char> *this, char a2);
void __thiscall std::numpunct<char>::~numpunct<char>(std::numpunct<char> *this); // idb
void __thiscall std::numpunct<char>::_Tidy(std::numpunct<char> *this); // idb
void __thiscall std::numpunct<char>::_Init(std::numpunct<char> *this, const std::_Locinfo *_Lobj); // idb
char *__cdecl std::_Maklocstr<char>(const char *_Ptr);
int __thiscall std::filebuf::overflow(std::filebuf *this, int _Meta); // idb
std::fpos<int> *__thiscall std::filebuf::seekoff(std::filebuf *this, std::fpos<int> *result, LONG _Off, unsigned int _Way, int __formal);
std::fpos<int> *__thiscall std::filebuf::seekpos(std::filebuf *this, std::fpos<int> *result, std::fpos<int> _Pos, int __formal); // idb
std::filebuf *__thiscall std::filebuf::close(std::filebuf *this); // idb
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, int _Val);
char *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Ifmt(std::num_put<char,std::ostreambuf_iterator<char> > *this, char *_Fmt, char *_Spec, __int16 _Flags);
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Iput(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, char *_Buf, size_t _Count);
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Putgrouped(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, char *_Ptr, size_t _Count, unsigned __int8 _Kseparator);
std::string *__thiscall std::numpunct<char>::grouping(std::numpunct<char> *this, std::string *result); // idb
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, unsigned int _Val);
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, __int64 _Val);
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, unsigned __int64 _Val);
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, double _Val);
char *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Ffmt(std::num_put<char,std::ostreambuf_iterator<char> > *this, char *_Fmt, char _Spec, __int16 _Flags);
std::ostreambuf_iterator<char> *__cdecl std::num_put<char,std::ostreambuf_iterator<char>>::_Fput(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, std::string::_Bxty *_Buf, unsigned int _Beforepoint, unsigned int _Afterpoint, unsigned int _Trailing, unsigned int _Count);
std::string *__thiscall std::string::insert(std::string *this, unsigned int _Off, unsigned int _Count, char _Ch); // idb
std::string *__thiscall std::string::append(std::string *this, const char *_Ptr, unsigned int _Count); // idb
std::string *__thiscall std::string::append(std::string *this, const std::string *_Right, unsigned int _Roff, unsigned int _Count); // idb
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, double _Val);
std::ostreambuf_iterator<char> *__thiscall std::num_put<char,std::ostreambuf_iterator<char>>::do_put(std::num_put<char,std::ostreambuf_iterator<char> > *this, std::ostreambuf_iterator<char> *result, std::ostreambuf_iterator<char> _Dest, std::ios_base *_Iosbase, unsigned __int8 _Fill, const void *_Val);
void __thiscall __noreturn std::vector<unsigned long>::_Xlen(std::vector<CCellManager::SafetyZoneInfo *> *this);
std::string *__thiscall std::numpunct<char>::do_grouping(std::numpunct<char> *this, std::string *result); // idb
std::string *__thiscall std::numpunct<char>::do_falsename(std::numpunct<char> *this, std::string *result); // idb
std::string *__thiscall std::numpunct<char>::do_truename(std::numpunct<char> *this, std::string *result); // idb
void __thiscall CCellManager::~CCellManager(CCellManager *this); // idb
void __thiscall std::filebuf::~filebuf<char,std::char_traits<char>>(std::filebuf *this); // idb
std::filebuf *__thiscall std::filebuf::`scalar deleting destructor'(std::filebuf *this, char a2);
void __thiscall std::vector<CCellManager::SafetyZoneInfo *>::_Insert_n(std::vector<CCellManager::SafetyZoneInfo *> *this, std::vector<CCellManager::SafetyZoneInfo *>::iterator _Where, unsigned int _Count, CCellManager::SafetyZoneInfo *const *_Val); // idb
void __thiscall std::fstream::fstream(std::fstream *this, const char *_Filename, int _Mode, int _Prot, int a5);
void __thiscall std::fstream::~fstream<char,std::char_traits<char>>(std::fstream *this); // idb
void __thiscall CCellManager::CCellManager(CCellManager *this); // idb
CCellManager *__cdecl CCellManager::GetInstance(); // idb
void __thiscall std::fstream::`vbase destructor'(std::fstream *this); // idb
_BYTE *__thiscall std::fstream::`vector deleting destructor'(std::fstream *this, char a2);
char __thiscall CCellManager::LoadSafetyZone(CCellManager *this, const char *szFileName);
char __thiscall CCellManager::CheckCellStatus(CCellManager *this);
void __thiscall CCellManager::Load(CCellManager *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Lrotate(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode); // idb
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Min(std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Rrotate(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode); // idb
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Max(std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::const_iterator *this); // idb
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Rarg, const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val, char _Carg); // idb
void __thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Erase(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Rootnode); // idb
std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::equal_range(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator> *result, const int *_Keyval); // idb
std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Buynode(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this); // idb
void __thiscall CMonsterShout::Shout(CMonsterShout *this, unsigned int dwMonsterCID, CPacketDispatch *nKID, unsigned __int16 usXPos, unsigned __int16 usZPos, CMonsterShout::Behavior eBehavior, const char *szName, unsigned __int16 usSkill_ID);
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::_Insert(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::_Node *_Wherenode, const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val); // idb
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::insert(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator,bool> *result, const std::pair<int const ,CMonsterShout::ShoutInfo> *_Val); // idb
std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const,CMonsterShout::ShoutInfo>>,1>>::erase(std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> > *this, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _First, std::_Tree<std::_Tmap_traits<int,CMonsterShout::ShoutInfo,std::less<int>,std::allocator<std::pair<int const ,CMonsterShout::ShoutInfo> >,1> >::iterator _Last); // idb
void __thiscall CMonsterShout::AddMonsterShout(CMonsterShout *this, CMonsterShout::ShoutInfo *shoutInfo, CMonsterShout::ChatNode *chatNode); // idb
void __thiscall CMonsterShout::~CMonsterShout(CMonsterShout *this); // idb
CMonsterShout *__cdecl CMonsterShout::GetInstance(); // idb
void __cdecl SetShoutText(int nKID, int nBehavior, int nSkill_ID, int nChatType, int nPercentage, const char *szMessage); // idb
char __thiscall CMonsterShout::LoadScript(CMonsterShout *this, char *szFileName);
// int __usercall Math::Convert::StrToHex16@<eax>(int a1@<eax>, int a2@<ecx>, int a3@<esi>, const char *szSrc);
CSkillMgr::ProtoTypeArray *__thiscall CSkillMgr::GetSkillProtoType(CSkillMgr *this, unsigned __int16 usSkill_ID);
unsigned __int8 __thiscall CSkillMgr::ReadStringToTypeValue(CSkillMgr *this, CDelimitedFile *DelimitedFile, const char *szColumn, const CTypeName *TypeArray, unsigned __int8 nMaxType);
char __thiscall CSkillMgr::CheckParentChildRule(CSkillMgr *this);
CSkillMgr::ProtoTypeArray *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::size(std::vector<CSkillMgr::ProtoTypeArray> *this);
void __cdecl std::fill<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray>(CSkillMgr::ProtoTypeArray *_First, CSkillMgr::ProtoTypeArray *_Last, const CSkillMgr::ProtoTypeArray *_Val); // idb
int __cdecl Math::Convert::Atos(char *szSrc);
void __thiscall CSkillMgr::CSkillMgr(CSkillMgr *this); // idb
void __thiscall CSkillMgr::~CSkillMgr(CSkillMgr *this); // idb
CSkillMgr::ProtoTypeArray *__cdecl std::copy_backward<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *>(CSkillMgr::ProtoTypeArray *_First, CSkillMgr::ProtoTypeArray *_Last, CSkillMgr::ProtoTypeArray *_Dest); // idb
void __cdecl std::iter_swap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _Left, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Right); // idb
void __cdecl std::_Med3<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last); // idb
void __cdecl std::_Push_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, int _Hole, int _Top, CSkillMgr::ProtoTypeArray _Val); // idb
void __cdecl std::_Rotate<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last);
CSkillMgr::ProtoTypeArray *__cdecl std::_Uninit_copy<CSkillMgr::ProtoTypeArray *,CSkillMgr::ProtoTypeArray *,std::allocator<CSkillMgr::ProtoTypeArray>>(CSkillMgr::ProtoTypeArray *_First, CSkillMgr::ProtoTypeArray *_Last, CSkillMgr::ProtoTypeArray *_Dest);
void __cdecl std::_Median<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Mid, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, int _Hole, int _Bottom, CSkillMgr::ProtoTypeArray _Val); // idb
void __cdecl std::_Uninit_fill_n<CSkillMgr::ProtoTypeArray *,unsigned int,CSkillMgr::ProtoTypeArray,std::allocator<CSkillMgr::ProtoTypeArray>>(CSkillMgr::ProtoTypeArray *_First, unsigned int _Count, const CSkillMgr::ProtoTypeArray *_Val);
std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::pair<std::vector<CSkillMgr::ProtoTypeArray>::iterator,std::vector<CSkillMgr::ProtoTypeArray>::iterator> *result, std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int,CSkillMgr::ProtoTypeArray>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last);
void __cdecl std::_Pop_heap_0<std::vector<CSkillMgr::ProtoTypeArray>::iterator,CSkillMgr::ProtoTypeArray>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last);
void __cdecl std::_Insertion_sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last); // idb
CSkillMgr::ProtoTypeArray *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::_Ufill(std::vector<CSkillMgr::ProtoTypeArray> *this, CSkillMgr::ProtoTypeArray *_Ptr, unsigned int _Count, const CSkillMgr::ProtoTypeArray *_Val); // idb
void __cdecl std::sort_heap<std::vector<CSkillMgr::ProtoTypeArray>::iterator>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last); // idb
void __thiscall __noreturn std::vector<CSkillMgr::ProtoTypeArray>::_Xlen(std::vector<CSkillMgr::ProtoTypeArray> *this);
void __cdecl std::_Sort<std::vector<CSkillMgr::ProtoTypeArray>::iterator,int>(std::vector<CSkillMgr::ProtoTypeArray>::iterator _First, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<CSkillMgr::ProtoTypeArray>::_Insert_n(std::vector<CSkillMgr::ProtoTypeArray> *this, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Where, unsigned int _Count, const CSkillMgr::ProtoTypeArray *_Val); // idb
std::vector<CSkillMgr::ProtoTypeArray>::iterator *__thiscall std::vector<CSkillMgr::ProtoTypeArray>::insert(std::vector<CSkillMgr::ProtoTypeArray> *this, std::vector<CSkillMgr::ProtoTypeArray>::iterator *result, std::vector<CSkillMgr::ProtoTypeArray>::iterator _Where, const CSkillMgr::ProtoTypeArray *_Val); // idb
void __thiscall std::vector<CSkillMgr::ProtoTypeArray>::push_back(std::vector<CSkillMgr::ProtoTypeArray> *this, const CSkillMgr::ProtoTypeArray *_Val); // idb
char __thiscall CSkillMgr::LoadSkillsFromFile(CSkillMgr *this, char *szFileName);
unsigned int __thiscall CServerSetup::GetServerID(CServerSetup *this); // idb
char __thiscall CServerSetup::GetServerGroup(CServerSetup *this); // idb
unsigned int __thiscall CServerSetup::GetServerZone(CServerSetup *this);
unsigned int __thiscall CServerSetup::GetServerChannel(CServerSetup *this);
char __thiscall CServerSetup::InitLoginServer(CServerSetup *this);
char __thiscall CServerSetup::InitAuthServer(CServerSetup *this);
char __thiscall CServerSetup::InitAgentServer(CServerSetup *this);
char __thiscall CServerSetup::InitUIDServer(CServerSetup *this);
char __thiscall CServerSetup::InitChatServer(CServerSetup *this);
const char *__cdecl CServerSetup::GetManageClientWindowName(); // idb
int __cdecl CServerSetup::GetGameServerTCPPort(signed int dwServerID);
int __cdecl CServerSetup::GetGameServerUDPPort(signed int dwServerID);
char __cdecl CServerSetup::GetZoneFromCmdLine(); // idb
char __cdecl CServerSetup::GetChannelFromCmdLine(); // idb
void __thiscall CServerSetup::~CServerSetup(CServerSetup *this); // idb
CServerSetup *__thiscall CServerSetup::`scalar deleting destructor'(CServerSetup *this, char a2);
void __thiscall CServerSetup::CServerSetup(CServerSetup *this); // idb
CServerSetup *__cdecl CServerSetup::GetInstance(); // idb
char __thiscall CServerSetup::LoadAdminUID(CServerSetup *this);
char __thiscall CServerSetup::InitGameServer(CServerSetup *this);
char __thiscall CServerSetup::Initialize(CServerSetup *this, int Type);
const Item::ItemInfo *__thiscall Item::CItemMgr::GetItemInfo(Item::CItemMgr *this, unsigned __int16 usProtoTypeID); // idb
unsigned __int16 __thiscall Item::CItemMgr::GetItemIDFromSkillID(Item::CItemMgr *this, unsigned __int16 usSkill_ID, unsigned __int16 unSkill_LockCount);
Item::ItemInfo *__thiscall std::vector<Item::ItemInfo>::size(std::vector<Item::ItemInfo> *this);
Item::ChemicalInfo *__thiscall std::vector<Item::ChemicalInfo>::size(std::vector<Item::ChemicalInfo> *this);
void __cdecl std::fill<Item::ItemInfo *,Item::ItemInfo>(Item::ItemInfo *_First, Item::ItemInfo *_Last, const Item::ItemInfo *_Val); // idb
void __cdecl std::fill<Item::ChemicalInfo *,Item::ChemicalInfo>(Item::ChemicalInfo *_First, Item::ChemicalInfo *_Last, const Item::ChemicalInfo *_Val); // idb
Item::ChemicalInfo *__cdecl std::_Copy_backward_opt<Item::ChemicalInfo *,Item::ChemicalInfo *>(Item::ChemicalInfo *_First, Item::ChemicalInfo *_Last, Item::ChemicalInfo *_Dest);
void __thiscall Item::CItemMgr::CItemMgr(Item::CItemMgr *this); // idb
void __thiscall Item::CItemMgr::~CItemMgr(Item::CItemMgr *this); // idb
unsigned __int8 __thiscall Item::CItemMgr::GetChemicalResult(Item::CItemMgr *this, Item::ChemicalInfo *chemical); // idb
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this, const Item::ItemInfo *__that); // idb
Item::ItemInfo *__cdecl std::copy_backward<Item::ItemInfo *,Item::ItemInfo *>(Item::ItemInfo *_First, Item::ItemInfo *_Last, Item::ItemInfo *_Dest); // idb
void __cdecl std::swap<Item::ItemInfo>(Item::ItemInfo *_Left, Item::ItemInfo *_Right); // idb
void __cdecl std::_Med3<std::vector<Item::ChemicalInfo>::iterator>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Mid, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
void __cdecl std::_Push_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(std::vector<Item::ItemInfo>::iterator _First, int _Hole, int _Top, Item::ItemInfo _Val); // idb
void __cdecl std::_Rotate<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Mid, std::vector<Item::ItemInfo>::iterator _Last);
void __cdecl std::_Push_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(std::vector<Item::ChemicalInfo>::iterator _First, int _Hole, int _Top, Item::ChemicalInfo _Val); // idb
void __cdecl std::_Rotate<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Mid, std::vector<Item::ChemicalInfo>::iterator _Last);
Item::ItemInfo *__cdecl std::copy<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo *>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last, Item::ItemInfo *_Dest); // idb
Item::ChemicalInfo *__cdecl std::_Copy_opt<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last, Item::ChemicalInfo *_Dest);
Item::ItemInfo *__cdecl std::_Uninit_copy<Item::ItemInfo *,Item::ItemInfo *,std::allocator<Item::ItemInfo>>(Item::ItemInfo *_First, Item::ItemInfo *_Last, Item::ItemInfo *_Dest);
Item::ChemicalInfo *__cdecl std::_Uninit_copy<Item::ChemicalInfo *,Item::ChemicalInfo *,std::allocator<Item::ChemicalInfo>>(Item::ChemicalInfo *_First, Item::ChemicalInfo *_Last, Item::ChemicalInfo *_Dest);
void __cdecl std::_Median<std::vector<Item::ChemicalInfo>::iterator>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Mid, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(std::vector<Item::ItemInfo>::iterator _First, int _Hole, int _Bottom, Item::ItemInfo _Val); // idb
void __cdecl std::_Adjust_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(std::vector<Item::ChemicalInfo>::iterator _First, int _Hole, int _Bottom, Item::ChemicalInfo _Val); // idb
char __thiscall CParseDelimitedData::operator()(CParseDelimitedData *this, std::vector<ItemDataParser::ParseData> *ParserArray, Item::ItemInfo *itemInfo);
Item::ChemicalInfo *__cdecl std::copy<std::vector<Item::ChemicalInfo>::iterator,Item::ChemicalInfo *>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last, Item::ChemicalInfo *_Dest); // idb
void __cdecl std::_Uninit_fill_n<Item::ItemInfo *,unsigned int,Item::ItemInfo,std::allocator<Item::ItemInfo>>(Item::ItemInfo *_First, unsigned int _Count, const Item::ItemInfo *_Val);
void __cdecl std::_Uninit_fill_n<Item::ChemicalInfo *,unsigned int,Item::ChemicalInfo,std::allocator<Item::ChemicalInfo>>(Item::ChemicalInfo *_First, unsigned int _Count, const Item::ChemicalInfo *_Val);
std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Item::ChemicalInfo>::iterator>(std::pair<std::vector<Item::ChemicalInfo>::iterator,std::vector<Item::ChemicalInfo>::iterator> *result, std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
void __cdecl std::_Median<std::vector<Item::ItemInfo>::iterator>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Mid, std::vector<Item::ItemInfo>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<Item::ItemInfo>::iterator,int,Item::ItemInfo>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last);
void __cdecl std::_Make_heap<std::vector<Item::ChemicalInfo>::iterator,int,Item::ChemicalInfo>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last);
void __cdecl std::_Pop_heap_0<std::vector<Item::ItemInfo>::iterator,Item::ItemInfo>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last);
std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Item::ItemInfo>::iterator>(std::pair<std::vector<Item::ItemInfo>::iterator,std::vector<Item::ItemInfo>::iterator> *result, std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last); // idb
void __cdecl std::_Insertion_sort<std::vector<Item::ItemInfo>::iterator>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last); // idb
void __cdecl std::_Insertion_sort<std::vector<Item::ChemicalInfo>::iterator>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
Item::ItemInfo *__thiscall std::vector<Item::ItemInfo>::_Ufill(std::vector<Item::ItemInfo> *this, Item::ItemInfo *_Ptr, unsigned int _Count, const Item::ItemInfo *_Val); // idb
Item::ChemicalInfo *__thiscall std::vector<Item::ChemicalInfo>::_Ufill(std::vector<Item::ChemicalInfo> *this, Item::ChemicalInfo *_Ptr, unsigned int _Count, const Item::ChemicalInfo *_Val); // idb
void __cdecl std::sort_heap<std::vector<Item::ItemInfo>::iterator>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last); // idb
void __cdecl std::sort_heap<std::vector<Item::ChemicalInfo>::iterator>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
void __thiscall __noreturn std::vector<ItemDataParser::ParseData>::_Xlen(std::vector<Item::ChemicalInfo> *this);
void __cdecl std::_Sort<std::vector<Item::ItemInfo>::iterator,int>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last, int _Ideal); // idb
void __cdecl std::_Sort<std::vector<Item::ChemicalInfo>::iterator,int>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<Item::ItemInfo>::_Insert_n(std::vector<Item::ItemInfo> *this, std::vector<Item::ItemInfo>::iterator _Where, unsigned int _Count, const Item::ItemInfo *_Val); // idb
void __thiscall std::vector<Item::ChemicalInfo>::_Insert_n(std::vector<Item::ChemicalInfo> *this, std::vector<Item::ChemicalInfo>::iterator _Where, unsigned int _Count, const Item::ChemicalInfo *_Val); // idb
void __cdecl std::sort<std::vector<Item::ItemInfo>::iterator>(std::vector<Item::ItemInfo>::iterator _First, std::vector<Item::ItemInfo>::iterator _Last); // idb
void __cdecl std::sort<std::vector<Item::ChemicalInfo>::iterator>(std::vector<Item::ChemicalInfo>::iterator _First, std::vector<Item::ChemicalInfo>::iterator _Last); // idb
std::vector<Item::ItemInfo>::iterator *__thiscall std::vector<Item::ItemInfo>::insert(std::vector<Item::ItemInfo> *this, std::vector<Item::ItemInfo>::iterator *result, std::vector<Item::ItemInfo>::iterator _Where, const Item::ItemInfo *_Val); // idb
std::vector<Item::ChemicalInfo>::iterator *__thiscall std::vector<Item::ChemicalInfo>::insert(std::vector<Item::ChemicalInfo> *this, std::vector<Item::ChemicalInfo>::iterator *result, std::vector<Item::ChemicalInfo>::iterator _Where, const Item::ChemicalInfo *_Val); // idb
void __thiscall std::vector<Item::ItemInfo>::push_back(std::vector<Item::ItemInfo> *this, const Item::ItemInfo *_Val); // idb
void __thiscall std::vector<Item::ChemicalInfo>::push_back(std::vector<Item::ChemicalInfo> *this, const Item::ChemicalInfo *_Val); // idb
char __thiscall Item::CItemMgr::LoadItemProtoType(Item::CItemMgr *this, char *szFileName);
char __thiscall Item::CItemMgr::LoadItemChemical(Item::CItemMgr *this, char *szFileName);
char __thiscall CGameEventMgr::Initialize(CGameEventMgr *this);
void __thiscall CSingleton<CGameEventMgr>::~CSingleton<CGameEventMgr>(CSingleton<CGameEventMgr> *this); // idb
void __thiscall CGameEventMgr::CGameEventMgr(CGameEventMgr *this); // idb
void __thiscall CGameEventMgr::~CGameEventMgr(CGameEventMgr *this); // idb
CGameEventMgr *__thiscall CGameEventMgr::`vector deleting destructor'(CGameEventMgr *this, char a2);
int __thiscall CLogSaveThread::End(CLogSaveThread *this); // idb
char __thiscall CLogSaveThread::Compress(CLogSaveThread *this);
std::_List_nod<CThread *>::_Node *__thiscall std::list<CModifyDummyCharacter *>::_Buynode(std::list<CThread *> *this); // idb
void __thiscall CLogSaveThread::~CLogSaveThread(CLogSaveThread *this); // idb
LogBuffer *__thiscall CGameLog::GetBuffer(CGameLog *this); // idb
void __thiscall CLogSaveThread::CLogSaveThread(CLogSaveThread *this); // idb
CLogSaveThread *__thiscall CLogSaveThread::`vector deleting destructor'(CLogSaveThread *this, char a2);
void __thiscall CGameLog::CGameLog(CGameLog *this); // idb
void __thiscall std::list<LogBuffer *>::_Incsize(std::list<LogBuffer *> *this, unsigned int _Count); // idb
char __thiscall CGameLog::Initialize(CGameLog *this, const char *szLogFilePrefix);
void __thiscall CGameLog::PushFullBuffer(CGameLog *this, CThread **ppLogBuffer);
void __thiscall std::list<LogBuffer *>::_Splice(std::list<LogBuffer *> *this, std::list<LogBuffer *>::iterator _Where, std::list<LogBuffer *> *_Right, std::list<LogBuffer *>::iterator _First, std::list<LogBuffer *>::iterator _Last, unsigned int _Count); // idb
BOOL __thiscall CGameLog::Flush(CGameLog *this);
char __thiscall CGameLog::Destroy(CGameLog *this);
char *__thiscall CGameLog::ReserveBuffer(CGameLog *this, unsigned __int16 usReserve); // idb
void __thiscall CGameLog::SpliceOutFullBuffer(CGameLog *this, std::list<LogBuffer *> *logBufferList); // idb
void __thiscall CGameLog::SpliceInFreeBuffer(CGameLog *this, std::list<LogBuffer *> *logBufferList); // idb
void __thiscall CGameLog::~CGameLog(CGameLog *this); // idb
CGameLog *__cdecl CGameLog::GetInstance(); // idb
char __thiscall CLogSaveThread::SetLogFileName(CLogSaveThread *this);
char __thiscall CLogSaveThread::WriteLog(CLogSaveThread *this);
unsigned int __thiscall CLogSaveThread::Run(CLogSaveThread *this); // idb
void __thiscall CPacketStatistics::PacketData::Clear(CPacketStatistics::PacketData *this); // idb
void __thiscall CPacketStatistics::AddSendPacket(CPacketStatistics *this, unsigned __int8 cCmd, unsigned int dwPacketSize); // idb
void __thiscall CPacketStatistics::AddRecvPacket(CPacketStatistics *this, unsigned __int8 cCmd, unsigned int dwPacketSize); // idb
void __thiscall CPacketStatistics::SetUserMessageFunc(CPacketStatistics *this, void (__cdecl *fnPreFix)(_iobuf *), void (__cdecl *fnPostFix)(_iobuf *)); // idb
char __thiscall CPacketStatistics::Log(CPacketStatistics *this);
void __thiscall CPacketStatistics::CPacketStatistics(CPacketStatistics *this); // idb
CPacketStatistics *__cdecl CPacketStatistics::GetInstance(); // idb
void __thiscall CPacketStatistics::Clear(CPacketStatistics *this); // idb
void __thiscall CChatDispatch::CChatDispatch(CChatDispatch *this, CSession *Session); // idb
void __thiscall CChatDispatch::~CChatDispatch(CChatDispatch *this); // idb
char __cdecl CChatDispatch::SendCharInfoChanged(CSendStream *SendStream, CCharacter *Character);
CSingleDispatch *__cdecl CChatDispatch::GetDispatchTable(); // idb
CChatDispatch *__thiscall CChatDispatch::`scalar deleting destructor'(CChatDispatch *this, char a2);
void __thiscall CChatDispatch::Connected(CChatDispatch *this); // idb
void __thiscall CChatDispatch::Disconnected(CChatDispatch *this); // idb
char __thiscall CChatDispatch::DispatchPacket(CChatDispatch *this, PktBase *lpPktBase);
void __thiscall CSendStream::CSendStream(CSendStream *this, CSession *Session); // idb
void __thiscall CSendStream::~CSendStream(CSendStream *this); // idb
char *__thiscall CSendStream::GetBuffer(CSendStream *this, char *dwReserve_In);
char __thiscall CSession::Send(CSession *this);
char __thiscall CSendStream::ForceSend(CSendStream *this);
char __thiscall CSendStream::WrapHeader(CSendStream *this, unsigned __int16 usUsed_In, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __thiscall CSendStream::WrapCrypt(CSendStream *this, unsigned __int16 usUsed_In, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __thiscall CSendStream::WrapCompress(CSendStream *this, char *lpSourceData, char *usSourceLength, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __thiscall CSendStream::PutBuffer(CSendStream *this, char *szBuffer, unsigned int dwBufferSize, unsigned __int8 cCMD_In);
void __thiscall CSingleDispatch::CSingleDispatch(CSingleDispatch *this); // idb
void __thiscall CSingleDispatch::InternalRemoveDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch); // idb
void __thiscall CSingleDispatch::Storage::~Storage(CMultiDispatch::Storage *this); // idb
void __thiscall CSingleDispatch::SetDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch); // idb
void __thiscall CSingleDispatch::RemoveDispatch(CSingleDispatch *this, CPacketDispatch *lpDispatch); // idb
BOOL __thiscall CSingleDispatch::GetDispatchNum(CSingleDispatch *this);
void __thiscall CSingleDispatch::~CSingleDispatch(CSingleDispatch *this); // idb
CPacketDispatch *__thiscall CSingleDispatch::GetDispatch(CSingleDispatch *this); // idb
void __thiscall CSingleDispatch::Storage::Storage(CSingleDispatch::Storage *this, CSingleDispatch *singleDispatch); // idb
char __cdecl PacketWrap::WrapHeader(char *lpBuffer_In, unsigned __int16 usUsed_In, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __cdecl PacketWrap::WrapCrypt(char *lpBuffer_In, unsigned __int16 usUsed_In, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __cdecl PacketWrap::WrapCrypt(char *lpBuffer_In, unsigned __int16 usUsed_In, unsigned __int8 cCMD_In, unsigned int dwTick);
char __cdecl PacketWrap::WrapCompress(PktBase *lpDstBuffer, unsigned int *dwDstBufferSize_InOut, char *lpSourceData, unsigned __int16 usSourceLength, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
char __cdecl PacketWrap::WrapCompress(CBuffer *lpDstBuffer, char *lpSourceData, unsigned __int16 usSourceLength, unsigned __int8 cCMD_In, unsigned __int16 usState_In, unsigned __int16 usError_In);
void __thiscall CLogDispatch::CLogDispatch(CLogDispatch *this, CSession *Session); // idb
void __thiscall CLogDispatch::~CLogDispatch(CLogDispatch *this); // idb
void __thiscall CLogDispatch::Connected(CLogDispatch *this); // idb
CLogDispatch *__thiscall CLogDispatch::`vector deleting destructor'(CLogDispatch *this, char a2);
void __thiscall CLogDispatch::Disconnected(CLogDispatch *this); // idb
char __thiscall CLogDispatch::ProcessServerLoginAck(CLogDispatch *this, PktBase *lpPktBase);
char __thiscall CLogDispatch::DispatchPacket(CLogDispatch *this, PktBase *lpPktBase);
void __thiscall CConsoleCMDFactory::StringCMD::StringCMD(CConsoleCMDFactory::StringCMD *this, const char *szCommand, CConsoleCommand *lpCMD); // idb
CConsoleCMDFactory::StringCMD *__thiscall std::vector<CTokenlizedFile::ColumnInfo>::size(std::vector<CConsoleCMDFactory::StringCMD> *this);
CConsoleCommand *__thiscall CConsoleCMDFactory::Create(CConsoleCMDFactory *this, char *szCommand, unsigned int nCommandLength);
CConsoleCMDFactory::StringCMD *__thiscall std::vector<CConsoleCMDFactory::StringCMD>::_Ufill(std::vector<CConsoleCMDFactory::StringCMD> *this, CConsoleCMDFactory::StringCMD *_Ptr, unsigned int _Count, const CConsoleCMDFactory::StringCMD *_Val); // idb
void __thiscall CConsoleCMDFactory::~CConsoleCMDFactory(CConsoleCMDFactory *this); // idb
void __thiscall __noreturn std::vector<CConsoleCMDFactory::StringCMD>::_Xlen(std::vector<CConsoleCMDFactory::StringCMD> *this);
CConsoleCMDFactory *__thiscall CConsoleCMDFactory::`vector deleting destructor'(CConsoleCMDFactory *this, char a2);
void __thiscall std::vector<CConsoleCMDFactory::StringCMD>::_Insert_n(std::vector<CConsoleCMDFactory::StringCMD> *this, std::vector<CConsoleCMDFactory::StringCMD>::iterator _Where, unsigned int _Count, const CConsoleCMDFactory::StringCMD *_Val); // idb
std::vector<CConsoleCMDFactory::StringCMD>::iterator *__thiscall std::vector<CConsoleCMDFactory::StringCMD>::insert(std::vector<CConsoleCMDFactory::StringCMD> *this, std::vector<CConsoleCMDFactory::StringCMD>::iterator *result, std::vector<CConsoleCMDFactory::StringCMD>::iterator _Where, const CConsoleCMDFactory::StringCMD *_Val); // idb
void __thiscall CConsoleCMDFactory::CConsoleCMDFactory(CConsoleCMDFactory *this); // idb
void __thiscall std::vector<CConsoleCMDFactory::StringCMD>::push_back(std::vector<CConsoleCMDFactory::StringCMD> *this, const CConsoleCMDFactory::StringCMD *_Val); // idb
char __thiscall CConsoleCMDFactory::AddCommand(CConsoleCMDFactory *this, const char *szCommand, CConsoleCommand *lpConsoleCommand);
void __thiscall CValidateConnection::~CValidateConnection(CValidateConnection *this); // idb
CValidateConnection *__thiscall CValidateConnection::`scalar deleting destructor'(CValidateConnection *this, char a2);
void __thiscall CLimitUserByIP::OperateMode(CLimitUserByIP *this, CLimitUserByIP::AllowMode_t eAllowMode); // idb
void __cdecl std::_Median<std::vector<unsigned long>::iterator>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Mid, std::vector<unsigned long>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<unsigned long>::iterator,int,unsigned long>(std::vector<unsigned long>::iterator _First, int _Hole, int _Bottom, unsigned int _Val); // idb
std::vector<unsigned long>::iterator *__cdecl std::_Lower_bound<std::vector<unsigned long>::iterator,unsigned long,int>(std::vector<unsigned long>::iterator *result, std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last, const unsigned int *_Val);
std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> *__cdecl std::_Unguarded_partition<std::vector<unsigned long>::iterator>(std::pair<std::vector<unsigned long>::iterator,std::vector<unsigned long>::iterator> *result, std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<unsigned long>::iterator,int,unsigned long>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last);
void __thiscall CLimitUserByIP::~CLimitUserByIP(CLimitUserByIP *this); // idb
void __cdecl std::_Insertion_sort<std::vector<unsigned long>::iterator>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last); // idb
CLimitUserByIP *__thiscall CLimitUserByIP::`vector deleting destructor'(CLimitUserByIP *this, char a2);
BOOL __cdecl std::binary_search<std::vector<unsigned long>::iterator,unsigned long>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last, const unsigned int *_Val);
void __cdecl std::sort_heap<std::vector<unsigned long>::iterator>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last); // idb
char __thiscall CLimitUserByIP::operator()(CLimitUserByIP *this, INET_Addr *localAddr, INET_Addr *remoteAddr);
void __cdecl std::_Sort<std::vector<unsigned long>::iterator,int>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<unsigned long>::reserve(std::vector<unsigned long> *this, unsigned int _Count); // idb
char __thiscall CLimitUserByIP::LoadAllowIPList(CLimitUserByIP *this, const char *szFileName);
void __thiscall CLimitUserByIP::CLimitUserByIP(CLimitUserByIP *this, const char *szFileName); // idb
void __thiscall CRegularAgentDispatch::CRegularAgentDispatch(CRegularAgentDispatch *this, CSession *Session); // idb
void __thiscall CRegularAgentDispatch::~CRegularAgentDispatch(CRegularAgentDispatch *this); // idb
char __cdecl CRegularAgentDispatch::Initialize();
void __thiscall CRegularAgentDispatch::Connected(CRegularAgentDispatch *this); // idb
CMultiDispatch *__cdecl CRegularAgentDispatch::GetDispatchTable(); // idb
CRegularAgentDispatch *__thiscall CRegularAgentDispatch::`scalar deleting destructor'(CRegularAgentDispatch *this, char a2);
void __thiscall CRegularAgentDispatch::Disconnected(CRegularAgentDispatch *this); // idb
char __thiscall CRegularAgentDispatch::ParseServerLogin(CRegularAgentDispatch *this, PktBase *lpPktBase);
char __cdecl RegularAgentPacketParse::SendGetCharData(unsigned int dwUID, unsigned int dwSlotCID, const char *szSlotName, unsigned int dwCID, unsigned __int8 cGroup);
void __thiscall boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete>::~object_pool<CTempCharacter,boost::default_user_allocator_new_delete>(boost::object_pool<CTempCharacter,boost::default_user_allocator_new_delete> *this); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Where); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::iterator _Last); // idb
void __thiscall std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>::~multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>>(std::multimap<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32> > *this); // idb
void __thiscall CTempCharacterMgr::CTempCharacterMgr(CTempCharacterMgr *this); // idb
CTempCharacterMgr *__cdecl CRegularAgentDispatch::GetTempCharacterMgr(); // idb
char __cdecl RegularAgentPacketParse::ParseGetCharSlot(PktBase *lpPktBase);
char __cdecl RegularAgentPacketParse::ParseGetCharData(PktBase *lpPktBase, unsigned __int8 cGroup);
char __cdecl RegularAgentPacketParse::SendGetCharSlot(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwCID, char cGroup);
char __cdecl RegularAgentPacketParse::SendSetCharData(std::_Tree_nod<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> >::_Node *dwCID, unsigned int dwMileage, unsigned __int8 cGroup);
char __thiscall CRegularAgentDispatch::DispatchPacket(CRegularAgentDispatch *this, PktBase *lpPktBase);
void __cdecl SeedDecryptBlock(unsigned __int8 *a1, unsigned int *a2); // idb
int __cdecl SeedDecrypt(unsigned __int8 *a1, int a2, unsigned int *a3); // idb
void __cdecl SeedEncRoundKey(unsigned int *a1, char *a2); // idb
CCrc32 *sub_447450();
void sub_447470();
_iobuf *__cdecl LoadAuthTable(char *file);
_iobuf *__cdecl LoadAuthIndex(char *file);
int __cdecl InitPacketProtect(char *a1, int a2);
CCSAuth *__thiscall CCSAuth::CCSAuth(CCSAuth *this); // idb
void __thiscall CCSAuth::Init(CCSAuth *this); // idb
unsigned int __thiscall CCSAuth::IsAuth(CCSAuth *this); // idb
void __thiscall CCSAuth::np_srandom(CCSAuth *this, unsigned int a2); // idb
signed int __thiscall CCSAuth::np_random(CCSAuth *this);
unsigned int __thiscall CCSAuth::GetAuthDword(CCSAuth *this); // idb
unsigned int __thiscall CCSAuth::TransAuthDword(CCSAuth *this, unsigned int a2, int a3); // idb
unsigned int __thiscall CCSAuth::CheckAuthDword(CCSAuth *this, unsigned int a2); // idb
int __thiscall CCSAuth::DecryptPacket(CCSAuth *this, unsigned __int8 *a2, unsigned int a3);
unsigned int __thiscall CCSAuth::CheckLastPacket(CCSAuth *this, unsigned int a2); // idb
unsigned int __thiscall CCSAuth::PPGetLastError(CCSAuth *this); // idb
void __thiscall CDBComponent::CDBComponent(CDBComponent *this); // idb
void __thiscall CDBComponent::~CDBComponent(CDBSingleObject *this); // idb
// char __userpurge CDBComponent::GetUIDFromBattleUID@<al>(CDBComponent *this@<ecx>, int a2@<ebx>, unsigned int dwBattleUID, unsigned int *lpUID);
CDBComponent *__thiscall CDBSingleObject::`scalar deleting destructor'(CDBComponent *this, char a2);
char __thiscall CDBComponent::Connect(CDBComponent *this, int DBClass_In);
CDBSingleObject *__cdecl CDBSingleObject::GetInstance(); // idb
void __thiscall CDBAgentDispatch::CDBAgentDispatch(CDBAgentDispatch *this, CSession *Session); // idb
void __thiscall CDBAgentDispatch::~CDBAgentDispatch(CDBAgentDispatch *this); // idb
void __thiscall CDBAgentDispatch::Connected(CDBAgentDispatch *this); // idb
char __thiscall CDBAgentDispatch::DispatchPacket(CDBAgentDispatch *this, PktAdmin *lpPktBase);
CSingleDispatch *__cdecl CDBAgentDispatch::GetDispatchTable(); // idb
CDBAgentDispatch *__thiscall CDBAgentDispatch::`scalar deleting destructor'(CDBAgentDispatch *this, char a2);
void __thiscall CDBAgentDispatch::Disconnected(CDBAgentDispatch *this); // idb
void __thiscall CNullSpell::CNullSpell(CNullSpell *this); // idb
char __thiscall CNullSpell::Activate(CNullSpell *this, CAggresiveCreature *pAffected, unsigned int dwOperateFlag);
CNullSpell *__thiscall CNullSpell::`scalar deleting destructor'(CNullSpell *this, char a2);
void __thiscall CGlobalSpellMgr::Process(CGlobalSpellMgr *this); // idb
void __thiscall CGlobalSpellMgr::Add(CGlobalSpellMgr *this, CSpell *pSpell); // idb
void __thiscall CGlobalSpellMgr::~CGlobalSpellMgr(CGlobalSpellMgr *this); // idb
CGlobalSpellMgr *__cdecl CGlobalSpellMgr::GetInstance(); // idb
void __thiscall Item::CDepositContainer::SetTabFlag(Item::CDepositContainer *this, unsigned int dwTabFlag); // idb
void __thiscall CGameClientDispatch::SetMoveAddress(CGameClientDispatch *this, unsigned int dwMoveServerID, const INET_Addr *moveAddress); // idb
char __cdecl DBAgentPacketParse::ParseDepositUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseDepositCmd(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseConfigInfoDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseSysServerVerUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseSysChannelUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::SendUserKill(CDBAgentDispatch *DBAgentDispatch, unsigned int dwUserID, unsigned __int16 wError);
char __cdecl DBAgentPacketParse::SendServerLogout(CDBAgentDispatch *DBAgentDispatch);
char __cdecl DBAgentPacketParse::ParseFriendList(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseQuestDB(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseBillingTimeoutNotify(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseBillingTimeCheckNotify(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseCharAdminCmd(CDBAgentDispatch *DBAgentDispatch, PktAdmin *lpPktBase);
bool __thiscall CCharacter::MoveZoneProcess(CCharacter *this); // idb
char __cdecl DBAgentPacketParse::SendAbnormalLogout(unsigned int dwUID, unsigned int dwCID, unsigned int dwRequestKey, CGameClientDispatch *lpGameClientDispatch);
char __cdecl DBAgentPacketParse::ParseUpdateDBData(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
bool __cdecl DBAgentPacketParse::ParseAgentParty(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase); // idb
char __cdecl DBAgentPacketParse::ParseAgentZone(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseSysServerLogin(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseSysRankingUpdate(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
char __cdecl DBAgentPacketParse::ParseUserKill(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
std::_List_nod<unsigned short>::_Node *__thiscall std::list<unsigned short>::_Buynode(std::list<unsigned short> *this, std::_List_nod<unsigned short>::_Node *_Next, std::_List_nod<unsigned short>::_Node *_Prev, unsigned __int16 *_Val);
void __thiscall std::list<unsigned short>::_Incsize(std::list<unsigned short> *this, unsigned int _Count); // idb
char __cdecl DBAgentPacketParse::ParseEventDropItem(CDBAgentDispatch *DBAgentDispatch, PktBase *lpPktBase);
void __thiscall VirtualArea::ProtoType::ProtoType(VirtualArea::ProtoType *this); // idb
VirtualArea::CBGServerMap *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualArea(VirtualArea::CVirtualAreaMgr *this, unsigned __int16 wMapIndex);
const VirtualArea::ProtoType *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(VirtualArea::CVirtualAreaMgr *this, char *szMapType); // idb
char __thiscall VirtualArea::CVirtualAreaMgr::EnterVirtualArea(VirtualArea::CVirtualAreaMgr *this, CCharacter *lpCharacter, unsigned __int16 wMapIndex, int cMoveType);
char __thiscall VirtualArea::CVirtualAreaMgr::LeaveVirtualArea(VirtualArea::CVirtualAreaMgr *this, CCharacter *lpCharacter);
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllVirtualArea(VirtualArea::CVirtualAreaMgr *this); // idb
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllMonster(VirtualArea::CVirtualAreaMgr *this); // idb
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessMonsterRegenHPAndMP(VirtualArea::CVirtualAreaMgr *this); // idb
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessSummonMonsterDead(VirtualArea::CVirtualAreaMgr *this); // idb
char __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllCellPrepareBroadCast(VirtualArea::CVirtualAreaMgr *this);
char __thiscall VirtualArea::CVirtualAreaMgr::ProcessAllCellBroadCast(VirtualArea::CVirtualAreaMgr *this);
void __thiscall VirtualArea::CVirtualAreaMgr::ProcessDeleteItem(VirtualArea::CVirtualAreaMgr *this); // idb
char __thiscall VirtualArea::CVirtualAreaMgr::CreateBGServer(VirtualArea::CVirtualAreaMgr *this);
char __thiscall VirtualArea::CVirtualAreaMgr::SendBGServerMapList(VirtualArea::CVirtualAreaMgr *this, CCharacter *lpCharacter);
char __thiscall VirtualArea::CVirtualAreaMgr::SendBGServerResultList(VirtualArea::CVirtualAreaMgr *this, CCharacter *lpCharacter);
VirtualArea::ProtoType *__thiscall std::vector<VirtualArea::ProtoType>::size(std::vector<VirtualArea::ProtoType> *this);
void __thiscall VirtualArea::ProtoType::ProtoType(VirtualArea::ProtoType *this, const VirtualArea::ProtoType *__that); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Min(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Rrotate(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode); // idb
void __cdecl std::fill<VirtualArea::ProtoType *,VirtualArea::ProtoType>(VirtualArea::ProtoType *_First, VirtualArea::ProtoType *_Last, const VirtualArea::ProtoType *_Val); // idb
int __thiscall std::string::compare(std::string *this, unsigned int _Off, unsigned int _N0, const char *_Ptr, unsigned int _Count); // idb
void __cdecl std::swap<VirtualArea::ProtoType>(VirtualArea::ProtoType *_Left, VirtualArea::ProtoType *_Right); // idb
const VirtualArea::ProtoType *__thiscall VirtualArea::CVirtualAreaMgr::GetVirtualAreaProtoType(VirtualArea::CVirtualAreaMgr *this, unsigned int dwVID); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lrotate(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Wherenode); // idb
VirtualArea::ProtoType *__cdecl std::copy_backward<VirtualArea::ProtoType *,VirtualArea::ProtoType *>(VirtualArea::ProtoType *_First, VirtualArea::ProtoType *_Last, VirtualArea::ProtoType *_Dest); // idb
VirtualArea::ProtoType *__cdecl std::_Uninit_copy<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType *,std::allocator<VirtualArea::ProtoType>>(VirtualArea::ProtoType *_First, VirtualArea::ProtoType *_Last, VirtualArea::ProtoType *_Dest);
void __cdecl std::_Push_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(std::vector<VirtualArea::ProtoType>::iterator _First, int _Hole, int _Top, VirtualArea::ProtoType _Val); // idb
void __cdecl std::_Rotate<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Mid, std::vector<VirtualArea::ProtoType>::iterator _Last);
void __cdecl std::_Uninit_fill_n<VirtualArea::ProtoType *,unsigned int,VirtualArea::ProtoType,std::allocator<VirtualArea::ProtoType>>(VirtualArea::ProtoType *_First, unsigned int _Count, const VirtualArea::ProtoType *_Val);
void __cdecl std::_Median<std::vector<VirtualArea::ProtoType>::iterator>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Mid, std::vector<VirtualArea::ProtoType>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(std::vector<VirtualArea::ProtoType>::iterator _First, int _Hole, int _Bottom, VirtualArea::ProtoType _Val); // idb
std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> *__cdecl std::_Unguarded_partition<std::vector<VirtualArea::ProtoType>::iterator>(std::pair<std::vector<VirtualArea::ProtoType>::iterator,std::vector<VirtualArea::ProtoType>::iterator> *result, std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<VirtualArea::ProtoType>::iterator,int,VirtualArea::ProtoType>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last);
void __cdecl std::_Pop_heap_0<std::vector<VirtualArea::ProtoType>::iterator,VirtualArea::ProtoType>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last);
VirtualArea::ProtoType *__thiscall std::vector<VirtualArea::ProtoType>::_Ufill(std::vector<VirtualArea::ProtoType> *this, VirtualArea::ProtoType *_Ptr, unsigned int _Count, const VirtualArea::ProtoType *_Val); // idb
void __cdecl std::_Insertion_sort<std::vector<VirtualArea::ProtoType>::iterator>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last); // idb
void __cdecl std::sort_heap<std::vector<VirtualArea::ProtoType>::iterator>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last); // idb
void __thiscall __noreturn std::vector<VirtualArea::ProtoType>::_Xlen(std::vector<VirtualArea::ProtoType> *this);
void __thiscall std::vector<VirtualArea::ProtoType>::_Insert_n(std::vector<VirtualArea::ProtoType> *this, std::vector<VirtualArea::ProtoType>::iterator _Where, unsigned int _Count, const VirtualArea::ProtoType *_Val); // idb
void __cdecl std::_Sort<std::vector<VirtualArea::ProtoType>::iterator,int>(std::vector<VirtualArea::ProtoType>::iterator _First, std::vector<VirtualArea::ProtoType>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<VirtualArea::ProtoType>::reserve(std::vector<VirtualArea::ProtoType> *this, VirtualArea::ProtoType *_Count);
std::vector<VirtualArea::ProtoType>::iterator *__thiscall std::vector<VirtualArea::ProtoType>::insert(std::vector<VirtualArea::ProtoType> *this, std::vector<VirtualArea::ProtoType>::iterator *result, std::vector<VirtualArea::ProtoType>::iterator _Where, const VirtualArea::ProtoType *_Val); // idb
void __thiscall std::vector<VirtualArea::ProtoType>::push_back(std::vector<VirtualArea::ProtoType> *this, const VirtualArea::ProtoType *_Val); // idb
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _Where); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rootnode); // idb
char __thiscall VirtualArea::CVirtualAreaMgr::LoadVirtualAreaProtoType(VirtualArea::CVirtualAreaMgr *this, char *szFileName);
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::erase(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::iterator _Last); // idb
void __thiscall std::map<std::string,unsigned char>::~map<std::string,unsigned char>(std::map<std::string,unsigned char> *this); // idb
void __thiscall VirtualArea::CVirtualAreaMgr::~CVirtualAreaMgr(VirtualArea::CVirtualAreaMgr *this); // idb
VirtualArea::CVirtualAreaMgr *__cdecl VirtualArea::CVirtualAreaMgr::GetInstance(); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::_Max(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Rrotate(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Rootnode); // idb
char __thiscall CSiegeObjectMgr::ExistBuildingCamp(CSiegeObjectMgr *this, unsigned int dwGID);
char __thiscall CSiegeObjectMgr::ExistCampInRadius(CSiegeObjectMgr *this, const Position *Pos);
void __thiscall CSiegeObjectMgr::ProcessAllSiegeObject(CSiegeObjectMgr *this); // idb
char __thiscall CSiegeObjectMgr::SendCampInfo(CSiegeObjectMgr *this, CSendStream *SendStream);
char __thiscall CSiegeObjectMgr::SendChangeMaster(CSiegeObjectMgr *this, unsigned int dwCastleID, unsigned int dwNewGID);
char __thiscall CSiegeObjectMgr::BroadCast(CSiegeObjectMgr *this);
CSiegeObject *__thiscall CSiegeObjectMgr::GetCamp(CSiegeObjectMgr *this, unsigned int dwCampID); // idb
CSiegeObject *__thiscall CSiegeObjectMgr::GetSiegeObject(CSiegeObjectMgr *this, unsigned int dwObjectID); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CSiegeObject *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CSiegeObject *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CSiegeObject *> >,0> >::iterator _Last); // idb
void __thiscall CSiegeObjectMgr::DestroyAllCamp(CSiegeObjectMgr *this); // idb
char __thiscall CSiegeObjectMgr::DeleteCamp(CSiegeObjectMgr *this, unsigned int dwCampID);
char __thiscall CSiegeObjectMgr::DeleteSiegeObject(CSiegeObjectMgr *this, unsigned int dwCID);
void __thiscall CSiegeObjectMgr::Destroy(CSiegeObjectMgr *this); // idb
CSiegeObject *__thiscall CSiegeObjectMgr::CreateSiegeObject(CSiegeObjectMgr *this, CastleObjectInfo *CastleObject); // idb
CSiegeObject *__thiscall CSiegeObjectMgr::CreateCamp(CSiegeObjectMgr *this, unsigned int dwCID, unsigned int dwCampID, unsigned int dwGID, unsigned __int16 dwHP, unsigned __int8 cState, unsigned __int8 cUpgradeStep, __int128 Pos);
// CSiegeObject *__userpurge CSiegeObjectMgr::CreateSiegeArms@<eax>(CSiegeObjectMgr *this@<ecx>, unsigned int dwCID, unsigned int dwOwnerID, unsigned int dwGID, unsigned int dwHP, unsigned __int16 wObjectType, unsigned __int8 cState, unsigned __int8 cUpgradeStep, __int128 Pos);
void __thiscall CSiegeObjectMgr::~CSiegeObjectMgr(CSiegeObjectMgr *this); // idb
void __thiscall CSiegeObjectMgr::CSiegeObjectMgr(CSiegeObjectMgr *this); // idb
CSiegeObjectMgr *__cdecl CSiegeObjectMgr::GetInstance(); // idb
Item::CItem *__thiscall CMonster::SellToCharacter(CMonster *this, CCharacter *lpCustomer, unsigned __int16 wKindItem, TakeType takeType, unsigned int *dwPrice); // idb
char __thiscall CMonster::GetNation(CMonster *this);
void __thiscall CMonster::~CMonster(CMonster *this); // idb
char __thiscall CMonster::InitMonster(CMonster *this, Position *Pos, CCell::CellMoveType eMoveType);
bool __thiscall CMonster::GetMotion(CMonster *this, unsigned int MotionID, MotionInfo *Motion); // idb
void __thiscall CMonster::UpdateBehavior(CMonster *this, unsigned int dwTick); // idb
char __thiscall CMonster::Process(CMonster *this);
int __thiscall CMonster::IsEnemy(CMonster *this, CCreature *lpTarget);
int __thiscall CMonster::CalculateFixLevelGap(CMonster *this, CAggresiveCreature *pDefender); // idb
// char __userpurge CMonster::Attack@<al>(CMonster *this@<ecx>, int a2@<ebx>, AtType attackType, unsigned __int8 cDefenderNum, CAggresiveCreature **ppDefenders, unsigned __int8 *cDefenderJudges);
bool __thiscall CMonster::IsDeadSummonMonster(CMonster *this); // idb
unsigned __int16 __thiscall CMonster::ApplyDamage(CMonster *this, AtType attackType, CAggresiveCreature *pOffencer, unsigned __int8 *cOffencerJudge, unsigned __int8 *cDefenserJudge, unsigned __int16 *wError); // idb
void __cdecl std::fill_n<unsigned char *,short,enum ClientConstants::Judge>(unsigned __int8 *_First, unsigned __int16 _Count, const ClientConstants::Judge *_Val);
void __thiscall CMonster::Attacked(CMonster *this); // idb
CMonster *__thiscall CMonster::`vector deleting destructor'(CMonster *this, char a2);
void __thiscall CMonster::CMonster(CMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
bool __thiscall CMonster::MultiAttack(CMonster *this); // idb
void __thiscall CMonster::Respawn(CMonster *this, unsigned int dwTick); // idb
int __thiscall CMonster::SendMove(CMonster *this, unsigned __int16 nAniNum); // idb
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::find(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result, unsigned int *_Keyval);
char __thiscall CMonster::Dead(CMonster *this, CCharacter *pOffencer);
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _Where); // idb
void __thiscall CMonster::CancelTarget(CMonster *this); // idb
char __thiscall Castle::CCastleMgr::SendSiegeTimeInfo(Castle::CCastleMgr *this, CSendStream *SendStream);
void __thiscall Castle::CCastleMgr::SetSiegeTime(Castle::CCastleMgr *this, char bIsSiegeTime);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Rootnode); // idb
void __thiscall Castle::CCastleMgr::Destroy(Castle::CCastleMgr *this); // idb
Castle::CCastle *__thiscall Castle::CCastleMgr::GetCastleByGID(Castle::CCastleMgr *this, unsigned int dwGID); // idb
char __thiscall Castle::CCastleMgr::ExistCastleInRadius(Castle::CCastleMgr *this, const Position *Pos);
char __thiscall Castle::CCastleMgr::SendCastleInfo(Castle::CCastleMgr *this, CSendStream *SendStream);
void __thiscall Castle::CCastleMgr::ProcessEmblemRegenHPAndMP(Castle::CCastleMgr *this); // idb
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Castle::CCastle *> >,0> >::iterator _Last); // idb
char __thiscall Castle::CCastleMgr::SerializeIn(Castle::CCastleMgr *this, char *lpBuffer_In, unsigned __int16 wBufferSize_In, unsigned __int8 cObjectNum);
void __thiscall std::map<unsigned long,Castle::CCastle *>::~map<unsigned long,Castle::CCastle *>(std::map<unsigned long,Castle::CCastle *> *this); // idb
void __thiscall Castle::CCastleMgr::~CCastleMgr(Castle::CCastleMgr *this); // idb
Castle::CCastleMgr *__cdecl Castle::CCastleMgr::GetInstance(); // idb
char __cdecl GameClientSendPacket::SendServerZone(CSendStream *SendStream, unsigned int dwServerID, const sockaddr_in *gameServerTCPAddress, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCSAuth(CSendStream *SendStream, unsigned int dwCID, unsigned int dwAuthCode, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendMoveZoneToDBAgent(CSendStream *AgentSendStream, POS *newPos, unsigned int dwRequestKey, unsigned int dwUserID, unsigned __int8 cZone, char cChannel);
char __cdecl GameClientSendPacket::SendServerZoneToDBAgent(CSendStream *AgentSendStream, unsigned int dwRequestKey, unsigned int dwUserID, unsigned __int8 cZone, unsigned __int8 cChannel);
char __cdecl GameClientSendPacket::SendCharLogin(CSendStream *SendStream, CCharacter *lpCharacter, sockaddr_in *lpSockAddr_In, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendLoginToDBAgent(CSendStream *AgentSendStream, unsigned int dwRequestKey, unsigned int dwSessionID, unsigned int dwUserID, unsigned int dwCharID, in_addr remoteAddress);
unsigned __int16 *__cdecl std::copy<unsigned short *,unsigned short *>(unsigned __int8 *_First, unsigned __int16 *_Last, unsigned __int8 *_Dest);
char __cdecl GameClientSendPacket::SendCharMoveZone(CSendStream *SendStream, unsigned __int8 *lpChannelNums, unsigned __int8 cChannelNum, unsigned __int8 cZone, unsigned __int16 usError);
void __thiscall CCharacter::SaveToDBData(CCharacter *this); // idb
char __thiscall CCharacter::GetCharacterInfo(CCharacter *this, char *pBuffer, int *nBufferSize_InOut, unsigned __int16 *lpUpdateLen);
char __thiscall CCharacter::SetCharacterInfo(CCharacter *this, float *pBuffer, unsigned __int16 *usUpdateLen);
char __thiscall CCharacter::DBUpdate(CCharacter *this, DBUpdateData::UpdateType eUpdateType);
char __thiscall CCharacter::ItemDump(CCharacter *this, char *pBuffer, char **nBufferSize_InOut);
char __thiscall CConsoleWindow::Destroy(CConsoleWindow *this);
void __thiscall CConsoleWindow::CConsoleWindow(CConsoleWindow *this, HINSTANCE__ *hInstance, HWND__ *hParentWnd, CConsoleCMDFactory *CMDFactory, CCommandProcess *CMDProcess); // idb
HBRUSH __stdcall CConsoleWindow::ConsoleWindowProc(HWND__ *hWnd, UINT msg, char *wParam, LPARAM lParam);
void __thiscall CConsoleWindow::PrintOutput(CConsoleWindow *this, const char *lpText, int nTextLen); // idb
void __thiscall CConsoleWindow::PrintInfo(CConsoleWindow *this, const char *lpText, int nTextLen); // idb
void __thiscall CConsoleWindow::~CConsoleWindow(CConsoleWindow *this); // idb
CConsoleWindow *__thiscall CConsoleWindow::`scalar deleting destructor'(CConsoleWindow *this, char a2);
void __thiscall CConsoleWindow::CreateCommand(CConsoleWindow *this); // idb
LRESULT __stdcall CConsoleWindow::InputWindowProc(HWND__ *hWnd, unsigned __int16 msg, unsigned __int16 wParam, LPARAM lParam);
char __thiscall CConsoleWindow::Initialize(CConsoleWindow *this);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CPacketDispatch *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CPacketDispatch *>>,0>>::find(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result, const unsigned int *_Keyval);
void __thiscall CMsgProcessMgr::Clear(CMsgProcessMgr *this); // idb
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *_Val);
void __thiscall std::map<unsigned int,CMsgProc *>::~map<unsigned int,CMsgProc *>(std::map<unsigned int,CMsgProc *> *this); // idb
void __thiscall CMsgProcessMgr::~CMsgProcessMgr(CMsgProcessMgr *this); // idb
bool __thiscall CMsgProcessMgr::Register(CMsgProcessMgr *this, unsigned int uMsg, CMsgProc *lpMsgProcessProc); // idb
void __thiscall CMsgProcessMgr::CMsgProcessMgr(CMsgProcessMgr *this); // idb
char __cdecl GameClientSendPacket::SendCharExchangeCmd(CSendStream *SendStream, unsigned int dwSenderID_In, unsigned int dwRecverID_In, unsigned __int8 cCmd_In, unsigned __int16 usError_In);
char __cdecl GameClientSendPacket::SendCharPartyCmd(CSendStream *SendStream, unsigned int dwPartyID_In, const AddressInfo *AddressInfo_In, const char *szSenderName_In, unsigned int dwSenderID_In, unsigned int dwReceiverID_In, unsigned __int16 usCmd_In, unsigned __int16 usError_In);
char __cdecl GameClientSendPacket::SendCharPartyCreateToDBAgent(CSendStream *AgentSendStream, unsigned int dwLeaderID, unsigned int dwMemberID);
char __cdecl GameClientSendPacket::SendCharPartyDestroyToDBAgent(CSendStream *AgentSendStream, unsigned int dwPartyID);
char __cdecl GameClientSendPacket::SendPartyMemberData(CSendStream *SendStream, unsigned int dwPartyID, unsigned int dwSenderID, unsigned int dwReferenceID, const char *strSenderName, unsigned __int16 usCmd);
char __cdecl GameClientSendPacket::SendPartyAddress(CSendStream *SendStream, unsigned int dwPartyID, unsigned int dwSenderID, const sockaddr_in *PublicAddress, const sockaddr_in *PrivateAddress, unsigned __int16 usCmd);
char __cdecl GameClientSendPacket::SendCharStallRegisterItem(CSendStream *SendStream, unsigned int dwCharID, TakeType takeType, unsigned int dwPrice, unsigned __int8 cCmd, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharStallEnter(CSendStream *SendStream, unsigned int dwCustomerID, unsigned int dwOwnerID, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharFriendAck(CSendStream *SendStream, unsigned __int8 cAckCmd, unsigned int dwCID, const char *szFriendName, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharFriendAdded(CSendStream *SendStream, unsigned int dwCID, const char *szName);
char __cdecl GameClientSendPacket::SendFriendListChangeToDB(CSendStream *AgentSendStream, unsigned int dwUID, unsigned int dwCID, unsigned int dwReferenceUID, unsigned int dwReferenceCID, unsigned int dwData, unsigned __int8 cChangeType);
char __cdecl GameClientSendPacket::SendCharCreateGuild(CSendStream *SendStream, unsigned int dwMasterID, unsigned int dwGuildID, unsigned __int8 cInclination, char *szGuildName, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildCmd(CSendStream *SendStream, unsigned int dwGID, unsigned int dwSenderID, unsigned int dwReceiverID, const char *szGuildName, const char *szSenderName, unsigned __int16 wCmd, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildMark(CSendStream *SendStream, unsigned int dwCID, unsigned int dwGID, char *szMark, unsigned int dwGold, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildLevel(CSendStream *SendStream, unsigned int dwUID, unsigned __int8 cLevel, unsigned int dwGold, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildRelation(CSendStream *SendStream, unsigned int dwCID, unsigned int dwGID, unsigned __int8 cRelation, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildInclination(CSendStream *SendStream, unsigned int dwUID, unsigned __int8 cInclination, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildRight(CSendStream *SendStream, unsigned int dwGID, GuildRight guildRight, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharMyGuildInfo(CSendStream *SendStream, unsigned int dwGold, GuildRight guildRight, unsigned __int8 cTitle, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharGuildSafe(CSendStream *SendStream, unsigned int dwCID, unsigned int dwGID, unsigned int dwSafeGold, unsigned int dwCharGold, unsigned __int8 cCmd, const char *szCharName, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharUpdateGuildMemberInfo(CSendStream *SendStream, unsigned int dwGID, unsigned int dwCID, Guild::MemberInfo memberInfo, unsigned __int16 wError);
char __cdecl GameClientSendPacket::SendCharChatBan(CSendStream *SendStream, unsigned int dwAdminCID, unsigned int dwTargetCID, unsigned int dwMinutes);
char __cdecl GameClientSendPacket::SendCharBindPosition(CSendStream *SendStream, unsigned int dwNPCID, unsigned __int8 cCmd, Position Pos, char cZone, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharHPRegen(CSendStream *SendStream, unsigned int dwCID, unsigned __int16 nNowHP, unsigned __int16 nNowMP);
char __cdecl GameClientSendPacket::SendCharQuickSlotMove(CSendStream *SendStream, TakeType *takeType, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharControlOption(CSendStream *SendStream, unsigned int dwCharID, RejectOption *Reject);
char __cdecl GameClientSendPacket::SendCharAuthorizePanel(CSendStream *SendStream, CCharacter *lpCaster, unsigned __int8 cCmd);
char __cdecl GameClientSendPacket::SendConfigInfoDB(CSendStream *SendStream, CCharacter *lpCharacter);
char __cdecl GameClientSendPacket::SendCharEliteBonus(CSendStream *SendStream, char cEliteBonus);
char __cdecl GameClientSendPacket::SendCharFameInfo(CSendStream *SendStream, CCharacter *lpRequestCharacter, const char *szWinCharName, const char *szLoseCharName, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharDuelCmd(CSendStream *SendStream, unsigned int dwSenderID, unsigned int dwRecverID, unsigned __int8 cCmd, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharAttacked(CSendStream *SendStream, CAggresiveCreature *pAttackCreature, CAggresiveCreature *pDefendCharacter, AtType attackType, float fDir, unsigned __int16 wDamage, unsigned __int8 cDefenserJudge, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharEquipDurability(CSendStream *SendStream, unsigned int dwCharID, unsigned __int8 cIndex, unsigned __int8 cValue, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharPeaceMode(CSendStream *SendStream, unsigned int dwCharID, unsigned __int8 cLeftTime, bool bPeace, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharSummonCmd(CCharacter *lpCharacter, CMonster *lpSummonee, unsigned __int8 cCmd, unsigned int dwTargetID, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharBattleGroundRespawn(CSendStream *SendStream, unsigned int dwCharID, unsigned __int16 wTurn, unsigned __int16 wWaitNum, unsigned __int16 wLeftTime, unsigned __int16 wHumanNum, unsigned __int16 wAkhanNum, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharSummon(unsigned int dwCharID, CAggresiveCreature *lpSummonee);
unsigned int __thiscall CPulse::CheckSleep(CPulse *this); // idb
CPulse *__cdecl CPulse::GetInstance(); // idb
void __thiscall CSummonMonster::SetGuard(CSummonMonster *this, bool bGuard); // idb
unsigned __int8 __thiscall CSummonMonster::GetNation(CSummonMonster *this); // idb
void __thiscall CSummonMonster::CSummonMonster(CSummonMonster *this, CMonster::MonsterCreateInfo *MonsterCreateInfo, CCharacter *lpMaster); // idb
void __thiscall CSummonMonster::~CSummonMonster(CSummonMonster *this); // idb
void __thiscall CSummonMonster::GuardMe(CSummonMonster *this, CAggresiveCreature *lpTarget, int wThreat);
char __thiscall CSummonMonster::Attack(CSummonMonster *this, AtType attackType, unsigned __int8 cDefenderNum, CAggresiveCreature **ppDefenders, unsigned __int8 *cDefenderJudges);
CCreature::MutualType __thiscall CSummonMonster::IsEnemy(CSummonMonster *this, CCreature *lpTarget); // idb
char __thiscall CSummonMonster::GetMotion(CSummonMonster *this, unsigned int MotionID, MotionInfo *Motion);
void __thiscall CStatue::CStatue(CStatue *this, CMonster::MonsterCreateInfo *MonsterCreateInfo, CStatue *lpParent); // idb
void __thiscall CStatue::~CStatue(CStatue *this); // idb
int __thiscall CStatue::IsEnemy(CStatue *this, CCreature *lpTarget);
char __thiscall CStatue::CreateLinkStatue(CStatue *this, unsigned __int16 wKind);
CStatue *__thiscall CStatue::GetLinkStatue(CStatue *this, unsigned __int16 wKind); // idb
Item::CItem *__thiscall CStatue::SellToCharacter(CStatue *this, CCharacter *lpCustomer, int wKindItem, TakeType takeType, unsigned int *dwPrice);
void __thiscall CStatueInfo::CStatueInfo(CStatueInfo *this, CStatue *lpStatue, bool bBonusTurn); // idb
bool __thiscall CAggresiveCreature::GetConsumeMPCount(CSiegeObject *this); // idb
char __thiscall CSkillMonster::Dead(CSkillMonster *this, CCharacter *pOffencer);
void __thiscall CMageMonster::~CMageMonster(CNamedMonster *this); // idb
void __thiscall std::_Tree<std::_Tset_traits<void *,std::less<void *>,std::allocator<void *>,0>>::const_iterator::_Dec(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::const_iterator *this); // idb
CSummonMonster *__thiscall CSummonMonster::`scalar deleting destructor'(CSummonMonster *this, char a2);
void __thiscall CSummonMonster::AttackCmd(CSummonMonster *this, CAggresiveCreature *lpTarget, unsigned __int16 wThreat); // idb
void __thiscall CSummonMonster::Attacked(CSummonMonster *this); // idb
char __thiscall CSummonMonster::Dead(CSummonMonster *this, CAggresiveCreature *pOffencer);
CStatue *__thiscall CStatue::`scalar deleting destructor'(CStatue *this, char a2);
char __thiscall CStatue::Dead(CStatue *this, CAggresiveCreature *pOffencer);
char __thiscall CStatue::Rest(CStatue *this);
char __thiscall CStatueInfo::operator()(CStatueInfo *this, CCharacter *lpCharacter);
void __thiscall CSkillMonster::CSkillMonster(CSkillMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
char __thiscall CSkillMonster::Attack(CSkillMonster *this, AtType attackType, unsigned __int8 cDefenderNum, CAggresiveCreature **ppDefenders, unsigned __int8 *cDefenderJudges);
char __thiscall CSkillMonster::UseSkill(CSkillMonster *this, AtType attackType, CAggresiveCreature **ppDefenders, char cSkillPattern);
bool __thiscall CSkillMonster::UseCastedSkill(CSkillMonster *this); // idb
void __thiscall CDefenderMonster::CDefenderMonster(CDefenderMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CWarriorMonster::CWarriorMonster(CWarriorMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CAcolyteMonster::CAcolyteMonster(CAcolyteMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
CNamedMonster *__thiscall CAcolyteMonster::`vector deleting destructor'(CNamedMonster *this, char a2);
void __thiscall CMageMonster::CMageMonster(CMageMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CBossMonster::CBossMonster(CBossMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CChiefMonster::CChiefMonster(CChiefMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CNamedMonster::CNamedMonster(CNamedMonster *this, CMonster::MonsterCreateInfo *MonsterCreate, bool bAdminCmdSummon); // idb
void __thiscall CNamedMonster::Respawn(CNamedMonster *this, unsigned int dwTick); // idb
void __cdecl std::_Med3<std::vector<CMonster *>::iterator,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Mid, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Med3<std::vector<CMonster *>::iterator,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Mid, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(std::vector<CMonster *>::iterator _First, int _Hole, int _Top, CMonster *_Val);
void __cdecl std::_Rotate<std::vector<enum Item::ItemType::Type>::iterator,int,enum Item::ItemType::Type>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Mid, std::vector<unsigned long>::iterator _Last);
void __cdecl std::_Push_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(std::vector<CMonster *>::iterator _First, int _Hole, int _Top, CMonster *_Val);
void __thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Erase(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rootnode); // idb
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0>>::_Buynode(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Larg, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Parg, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rarg, unsigned int *_Val, char _Carg);
unsigned int *__cdecl std::_Uninit_copy<enum Item::ItemType::Type *,enum Item::ItemType::Type *,std::allocator<enum Item::ItemType::Type>>(std::vector<unsigned long>::iterator _First, std::vector<unsigned long>::iterator _Last, unsigned int *_Dest);
void __cdecl std::_Median<std::vector<CMonster *>::iterator,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Mid, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Median<std::vector<CMonster *>::iterator,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Mid, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(std::vector<CMonster *>::iterator _First, int _Hole, int _Bottom, CMonster *_Val);
void __cdecl std::_Adjust_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(std::vector<CMonster *>::iterator _First, int _Hole, int _Bottom, CMonster *_Val);
std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Rootnode, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Wherenode); // idb
std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareLevel>(std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *result, std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonster *>::iterator,CompareHP>(std::pair<std::vector<CMonster *>::iterator,std::vector<CMonster *>::iterator> *result, std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Make_heap<std::vector<CMonster *>::iterator,int,CMonster *,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Copy(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, const std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *_Right); // idb
CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > *__cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::iterator,CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const,CCharacter *>>>(CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > *result, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0> >::iterator _Last, CCreatureManager::CProcessSecond<CStatueInfo,std::pair<unsigned long const ,CCharacter *> > _Func); // idb
void __cdecl std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __cdecl std::_Insertion_sort<std::vector<CMonster *>::iterator,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __cdecl std::sort_heap<std::vector<CMonster *>::iterator,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __cdecl std::sort_heap<std::vector<CMonster *>::iterator,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last);
void __thiscall CStatue::GiveMileage(CStatue *this); // idb
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::_Insert(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::_Node *_Wherenode, unsigned int *_Val);
void __thiscall __noreturn std::vector<CMonster *>::_Xlen(std::vector<CMonster *> *this);
void __thiscall std::vector<CMonster *>::_Insert_n(std::vector<CMonster *> *this, std::vector<CMonster *>::iterator _Where, unsigned int _Count, CMonster *const *_Val); // idb
void __cdecl std::_Sort<std::vector<CMonster *>::iterator,int,CompareLevel>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last, int _Ideal, CompareLevel _Pred); // idb
void __cdecl std::_Sort<std::vector<CMonster *>::iterator,int,CompareHP>(std::vector<CMonster *>::iterator _First, std::vector<CMonster *>::iterator _Last, int _Ideal, CompareHP _Pred); // idb
std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::insert(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::pair<std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator,bool> *result, unsigned int *_Val);
void __thiscall std::vector<CMonster *>::reserve(std::vector<CMonster *> *this, unsigned int _Count); // idb
std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *__thiscall std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0>>::erase(std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> > *this, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator *result, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _First, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator _Last); // idb
void __thiscall std::vector<CMonster *>::push_back(std::vector<CMonster *> *this, CMonster **_Val);
CMonster *__thiscall CSkillMonster::FindHighestLVMember(CSkillMonster *this);
CSkillMonster *__thiscall CSkillMonster::FindHighestLVMember(CSkillMonster *this, const AtType *attackType);
CSkillMonster *__thiscall CSkillMonster::FindLowestHPMember(CSkillMonster *this, const AtType *attackType, bool bRegin, float fRemainHP);
void __thiscall std::set<unsigned long>::~set<unsigned long>(std::set<unsigned long> *this); // idb
char __thiscall CDefenderMonster::SkillAttack(CDefenderMonster *this);
char __thiscall CWarriorMonster::SkillAttack(CWarriorMonster *this);
char __thiscall CAcolyteMonster::SkillAttack(CAcolyteMonster *this);
char __thiscall CChiefMonster::SkillAttack(CChiefMonster *this);
char __thiscall CNamedMonster::SkillAttack(CNamedMonster *this);
CAggresiveCreature *__thiscall CSkillMonster::FindEnemy(CSkillMonster *this, AtType *attackType, std::_Tree<std::_Tset_traits<unsigned long,std::less<unsigned long>,std::allocator<unsigned long>,0> >::iterator cType1, unsigned __int8 cType2);
char __thiscall CMageMonster::SkillAttack(CMageMonster *this);
char __thiscall CBossMonster::SkillAttack(CBossMonster *this);
void __thiscall EliteBonus::EliteBonusData::EliteBonusData(EliteBonus::EliteBonusData *this); // idb
void __thiscall EliteBonus::EliteBonusData::EliteBonusData(EliteBonus::EliteBonusData *this, char cNation, unsigned __int8 cLevel); // idb
void __thiscall MotionInfo::MotionInfo(MotionInfo *this); // idb
void __thiscall CharacterStatus::CharacterStatus(CharacterStatus *this); // idb
void __thiscall CharacterStatus::Init(CharacterStatus *this, _CHAR_INFOST *characterDBData); // idb
void __thiscall StatusInfo::operator+=(StatusInfo *this, const StatusInfo *rhs); // idb
void __thiscall StatusInfo::operator-=(StatusInfo *this, const StatusInfo *rhs); // idb
StatusInfo *__thiscall StatusInfo::CalculateDoubleSword(StatusInfo *this, StatusInfo *result, Item::EquipType::DoubleSwordType eDoubleSwordType); // idb
void __thiscall CreatureStatus::Init(CreatureStatus *this, _CHAR_INFOST *characterDBData); // idb
void __thiscall StatusInfo::StatusInfo(StatusInfo *this); // idb
void __thiscall CreatureStatus::CreatureStatus(CreatureStatus *this); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Max(std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Rrotate(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Lrotate(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Wherenode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this); // idb
void __thiscall std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Node::~_Node(std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *this); // idb
std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _Where); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::iterator _Last); // idb
void __thiscall CTimerProcMgr::ClearAll(CTimerProcMgr *this); // idb
void __thiscall std::map<unsigned int,CTimerProcMgr::InternalTimerData>::~map<unsigned int,CTimerProcMgr::InternalTimerData>(std::map<unsigned int,CTimerProcMgr::InternalTimerData> *this); // idb
void __thiscall CTimerProcMgr::~CTimerProcMgr(CTimerProcMgr *this); // idb
void __thiscall CTimerProcMgr::CTimerProcMgr(CTimerProcMgr *this); // idb
void __thiscall CSysTray::CSysTray(CSysTray *this, HWND__ *hWnd, HINSTANCE__ *hInstance); // idb
void __thiscall CSysTray::ShowPopupMenu(CSysTray *this, HWND__ *hWnd, HMENU__ *hMenu); // idb
UINT __cdecl CSysTray::GetSysTrayNotifyMsg();
void __thiscall CSysTray::~CSysTray(CSysTray *this); // idb
BOOL __thiscall CSysTray::AddIcon(CSysTray *this, const char *lpToolTip, HICON__ *hIcon, unsigned int uID);
int __thiscall  __thiscall `vcall'{0,{flat}}(int (__thiscall ***this)(_DWORD));
int __thiscall  __thiscall `vcall'{4,{flat}}(void *this);
std::mem_fun_t<bool,CCommand> *__cdecl std::for_each<std::list<CCommand *>::iterator,std::mem_fun_t<bool,CCommand>>(std::mem_fun_t<bool,CCommand> *result, std::list<CCommand *>::iterator _First, std::list<CCommand *>::iterator _Last, std::mem_fun_t<bool,CCommand> _Func); // idb
void __thiscall CCommandProcess::ClearAll(CCommandProcess *this); // idb
void __thiscall CCommandProcess::CCommandProcess(CCommandProcess *this); // idb
void __thiscall CCommandProcess::~CCommandProcess(CCommandProcess *this); // idb
void __thiscall std::list<CCommand *>::_Splice(std::list<CCommand *> *this, std::list<CCommand *>::iterator _Where, std::list<CCommand *> *_Right, std::list<CCommand *>::iterator _First, std::list<CCommand *>::iterator _Last, unsigned int _Count); // idb
void __thiscall CCommandProcess::ProcessAll(CCommandProcess *this); // idb
void __thiscall CServerRequest::Result::~Result(CServerRequest::Result *this); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Max(std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Pnode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Min(std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Rrotate(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::const_iterator *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::const_iterator::_Dec(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::const_iterator *this); // idb
void __thiscall CServerRequest::RequestOff(CServerRequest *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Lrotate(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Rootnode); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Rarg, const std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val, char _Carg); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::find(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result, const unsigned int *_Keyval); // idb
void __thiscall CServerRequest::RemoveAllRequest(CServerRequest *this); // idb
CPacketDispatch *__thiscall CServerRequest::GetRequest(CServerRequest *this, unsigned int dwRequestKey); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator,bool> *result, std::pair<unsigned long const ,CServerRequest::RequestInfo> *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CServerRequest::RequestInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CServerRequest::RequestInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CServerRequest::RequestInfo> >,0> >::iterator _Last); // idb
unsigned int __thiscall CServerRequest::AddRequest(CServerRequest *this, CPacketDispatch *lpSrcDispatch, CPacketDispatch *lpDstDispatch, unsigned int dwDurationSec, void (__cdecl *lpTimeoutRequest)(CPacketDispatch *)); // idb
void __thiscall CServerRequest::RemoveRequest(CServerRequest *this, unsigned int dwRequestKey); // idb
void __thiscall CServerRequest::RemoveTimeoutRequest(CServerRequest *this); // idb
CPacketDispatch *__thiscall CServerRequest::PopRequest(CServerRequest *this, unsigned int dwRequestKey); // idb
void __thiscall std::map<unsigned long,CServerRequest::RequestInfo>::~map<unsigned long,CServerRequest::RequestInfo>(std::map<unsigned long,CServerRequest::RequestInfo> *this); // idb
void __thiscall CServerRequest::~CServerRequest(CServerRequest *this); // idb
void __thiscall CServerRequest::CServerRequest(CServerRequest *this); // idb
CServerRequest *__cdecl CServerRequest::GetInstance(); // idb
void __thiscall CServerRequest::Result::Result(CServerRequest::Result *this, unsigned int dwRequestKey, bool bRemove); // idb
unsigned int __cdecl BroadcastInfo::GetObjectSize(BroadcastInfo::DataType::Type dataType, BroadcastInfo::ObjectPhase::Type objectPhase); // idb
char __cdecl BroadcastInfo::SerializeOutAggresiveCreatureInfo(CAggresiveCreature *AggresiveCreature, BroadcastInfo::DataType::Type eDataType, BroadcastInfo::ObjectPhase::Type ePhase, char *szBuffer_Out, unsigned int *dwBufferSize_InOut);
char __cdecl BroadcastInfo::SerializeOutCharacterInfo(CCharacter *Character, BroadcastInfo::DataType::Type eDataType, BroadcastInfo::ObjectPhase::Type ePhase, char *szBuffer_Out, unsigned int *dwBufferSize_InOut);
char __cdecl BroadcastInfo::SerializeOutMonsterInfo(CMonster *Monster, BroadcastInfo::DataType::Type eDataType, BroadcastInfo::ObjectPhase::Type ePhase, char *szBuffer_Out, unsigned int *dwBufferSize_InOut);
char __cdecl BroadcastInfo::SerializeOutSummonMonsterInfo(CSummonMonster *SummonMonster, BroadcastInfo::DataType::Type eDataType, BroadcastInfo::ObjectPhase::Type ePhase, char *szBuffer_Out, unsigned int *dwBufferSize_InOut);
void __thiscall CVirtualMonsterMgr::ProcessAllMonster(CVirtualMonsterMgr *this); // idb
void __thiscall CVirtualMonsterMgr::ProcessMonsterRegenHPAndMP(CVirtualMonsterMgr *this); // idb
CMsgProc *__thiscall CVirtualMonsterMgr::GetAggresiveCreature(CVirtualMonsterMgr *this, signed int dwCID);
void __thiscall CVirtualMonsterMgr::ProcessSummonMonsterDead(CVirtualMonsterMgr *this); // idb
void __thiscall CVirtualMonsterMgr::DestroyMonsterList(CVirtualMonsterMgr *this); // idb
bool __thiscall CVirtualMonsterMgr::AddMonster(CVirtualMonsterMgr *this, CMonster *lpMonster); // idb
char __thiscall CVirtualMonsterMgr::SummonMonster(CVirtualMonsterMgr *this, unsigned int nKID, __int128 Pos);
void __thiscall CVirtualMonsterMgr::~CVirtualMonsterMgr(CVirtualMonsterMgr *this); // idb
void __thiscall CVirtualMonsterMgr::CVirtualMonsterMgr(CVirtualMonsterMgr *this); // idb
double __thiscall CThreat::GetAggravation(CThreat *this, CAggresiveCreature *pCreature);
char __thiscall CThreat::SaveEnemy(CThreat *this, unsigned int dwCID);
void __thiscall std::list<IOPCode *>::clear(std::list<CThread *> *this); // idb
std::_List_nod<CThreat::ThreatInfo>::_Node *__thiscall std::list<CThreat::ThreatInfo>::_Buynode(std::list<CThreat::ThreatInfo> *this); // idb
std::_List_nod<CThreat::ThreatInfo>::_Node *__thiscall std::list<CThreat::ThreatInfo>::_Buynode(std::list<CThreat::ThreatInfo> *this, std::_List_nod<CThreat::ThreatInfo>::_Node *_Next, std::_List_nod<CThreat::ThreatInfo>::_Node *_Prev, const CThreat::ThreatInfo *_Val); // idb
char __thiscall CThreat::DeleteThreatened(CThreat *this, CAggresiveCreature *pDefendCreature);
char __thiscall CThreat::DeleteThreat(CThreat *this, CAggresiveCreature *pAttackCreature);
void __thiscall std::list<CThreat::ThreatInfo>::pop_front(std::list<CThreat::ThreatInfo> *this); // idb
void __thiscall CThreat::~CThreat(CThreat *this); // idb
void __thiscall CThreat::ClearThreatList(CThreat *this); // idb
void __thiscall CThreat::ClearThreatenedList(CThreat *this); // idb
void __thiscall CThreat::DivisionExp(CThreat *this); // idb
unsigned int *__thiscall CThreat::GetAward(CThreat *this, unsigned int *dwItemKind, unsigned int *dwOwnerID);
void __thiscall std::list<CThreat::ThreatInfo>::list<CThreat::ThreatInfo>(std::list<CThreat::ThreatInfo> *this); // idb
void __thiscall CThreat::ClearAll(CThreat *this); // idb
void __thiscall CThreat::CThreat(CThreat *this); // idb
void __thiscall std::list<CThreat::ThreatInfo>::_Incsize(std::list<CThreat::ThreatInfo> *this, unsigned int _Count); // idb
void __thiscall std::list<CAggresiveCreature *>::_Incsize(std::list<CAggresiveCreature *> *this, unsigned int _Count); // idb
void __thiscall CThreat::AddToThreatenedList(CThreat *this, CAggresiveCreature *pDefendCreature); // idb
void __thiscall std::list<CThreat::ThreatInfo>::_Splice(std::list<CThreat::ThreatInfo> *this, std::list<CThreat::ThreatInfo>::iterator _Where, std::list<CThreat::ThreatInfo> *_Right, std::list<CThreat::ThreatInfo>::iterator _First, std::list<CThreat::ThreatInfo>::iterator _Last, unsigned int _Count); // idb
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareAmount>(std::list<CThreat::ThreatInfo> *this, std::list<CThreat::ThreatInfo> *_Right, int a3);
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareLevel>(std::list<CThreat::ThreatInfo> *this, std::list<CThreat::ThreatInfo> *_Right, int a3);
void __thiscall std::list<CThreat::ThreatInfo>::merge<CompareFame>(std::list<CThreat::ThreatInfo> *this, std::list<CThreat::ThreatInfo> *_Right, int a3);
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareAmount>(std::list<CThreat::ThreatInfo> *this, int _Pred);
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareLevel>(std::list<CThreat::ThreatInfo> *this, int a2);
void __thiscall std::list<CThreat::ThreatInfo>::sort<CompareFame>(std::list<CThreat::ThreatInfo> *this, int a2);
void __thiscall CThreat::AddToThreatList(CThreat *this, CAggresiveCreature *pAttackCreature, int lThreatAmount); // idb
void __thiscall CThreat::AffectThreat(CThreat *this, CAggresiveCreature *pTauntCreature, int lDamage, CThreat::AffectThreatType eType); // idb
CAggresiveCreature *__thiscall CThreat::GetTarget(CThreat *this); // idb
char __thiscall CThreat::DivisionFame(CThreat *this);
void __thiscall CThreat::HealThreat(CThreat *this, CAggresiveCreature *pHealCreature, int lThreatAmount); // idb
char __thiscall Item::CEquipment::AddSocket(Item::CEquipment *this, unsigned __int8 cSocket);
void __thiscall Item::CEquipment::InitializeAttribute(Item::CEquipment *this); // idb
void __thiscall Item::CEquipment::ApplyGemAttribute(Item::CEquipment *this, Item::CEquipment::ApplyType eApplyType); // idb
void __thiscall Item::CEquipment::ApplyUpgradeAttribute(Item::CEquipment *this, Item::CEquipment::ApplyType eApplyType); // idb
void __thiscall Item::CItem::CItem(Item::CItem *this, unsigned __int64 dwItemUID, const Item::ItemInfo *itemInfo); // idb
char __thiscall Item::CItem::SerializeOut(Item::CItem *this, char *lpSerializeItem_Out, unsigned int *nBufferLength_InOut);
char __thiscall Item::CItem::SerializeIn(Item::CItem *this, const char *lpSerializeItem_In, unsigned int *nBufferLength_InOut);
char __thiscall Item::CEquipment::SerializeOut(Item::CEquipment *this, char *lpSerializeItem_Out, unsigned int *nBufferLength_InOut);
StatusInfo *__thiscall Item::CEquipment::GetStatusInfo(Item::CEquipment *this, StatusInfo *result, char cTrend); // idb
int __thiscall Item::CEquipment::CheckPassiveType(Item::CEquipment *this, unsigned __int16 usSkillType); // idb
void __thiscall Item::CUseItem::CUseItem(Item::CUseItem *this, unsigned __int64 dwItemUID, const Item::ItemInfo *itemInfo); // idb
void __thiscall Item::CUseItem::~CUseItem(Item::CUseItem *this); // idb
char __thiscall Item::CUseItem::UsePotion(Item::CUseItem *this, CCharacter *lpCharacter);
double __cdecl _Pow_int<float>(float _X, int _Y);
void __thiscall Item::CEquipment::InitializeGemAttribute(Item::CEquipment *this); // idb
void __thiscall Item::CEquipment::InitializeUpgradeAttribute(Item::CEquipment *this); // idb
Item::CItem *__thiscall Item::CItem::`vector deleting destructor'(Item::CItem *this, char a2);
void __thiscall Item::CEquipment::CEquipment(Item::CEquipment *this, unsigned __int64 dwItemUID, const Item::ItemInfo *itemInfo); // idb
Item::CUseItem *__thiscall Item::CEquipment::`vector deleting destructor'(Item::CUseItem *this, char a2);
int __thiscall Item::CEquipment::InstallSocket(Item::CEquipment *this, Item::CItem *Gem);
int __thiscall Item::CEquipment::UpgradeItem(Item::CEquipment *this, Item::CItem *Mineral_InOut, unsigned int dwCurrentGold_In, unsigned int *dwUsedGold_Out);
void __thiscall Item::CEquipment::AddRandomOption(Item::CEquipment *this, unsigned __int8 cBaseLevel, bool bBadOption); // idb
bool __thiscall Item::CUseItem::Use(Item::CUseItem *this, CCharacter *lpCharacter); // idb
char __thiscall Item::CEquipment::SerializeIn(Item::CEquipment *this, const char *lpSerializeItem_In, unsigned int *nBufferLength_InOut);
unsigned int __cdecl Math::Random::ComplexRandom(int nEndExtent, int nBeginExtent); // idb
int __cdecl Math::Random::SimpleRandom(unsigned int dwSeed, int nEndExtent, int nBeginExtent); // idb
void __thiscall Item::CDepositContainer::CDepositContainer(Item::CDepositContainer *this); // idb
bool __thiscall CParty::Destory(CParty *this, unsigned int dwTargetID, bool bSwitch); // idb
char __thiscall Item::CDepositContainer::Initialize(Item::CDepositContainer *this, CCharacter *lpCharacter, unsigned __int8 nXSize, unsigned __int8 nYSize, unsigned __int8 nTabNum);
unsigned __int8 *__thiscall Item::CArrayContainer::`vector deleting destructor'(Item::CArrayContainer *this, char a2);
Item::CItem *__thiscall Item::CDepositContainer::GetItem(Item::CDepositContainer *this, unsigned int itemPos);
char __thiscall Item::CDepositContainer::SetItem(Item::CDepositContainer *this, unsigned int itemPos, Item::CItem *lpItem);
bool __thiscall Item::CDepositContainer::TestItem(Item::CDepositContainer *this, unsigned int itemPos, int usProtoTypeID);
bool __thiscall Item::CDepositContainer::RemoveItem(Item::CDepositContainer *this, unsigned int itemPos);
char __thiscall Item::CDepositContainer::Update(Item::CDepositContainer *this, CSendStream *SendStream);
char __thiscall Item::CDepositContainer::ChangePassword(Item::CDepositContainer *this, const char *szCurrentPassword, unsigned int nCurrentPasswordLength, const char *szChangePassword, unsigned int nChangePasswordLength);
char __thiscall Item::CDepositContainer::AddGold(Item::CDepositContainer *this, unsigned int dwGold);
char __thiscall Item::CDepositContainer::DeductGold(Item::CDepositContainer *this, unsigned int dwGold);
void __thiscall Item::CDepositContainer::DumpItemInfo(Item::CDepositContainer *this); // idb
char __thiscall Item::CDepositContainer::LogUpdate(Item::CDepositContainer *this, char *szLogBuffer_Out, unsigned int *dwBufferSize_InOut);
void __thiscall Item::CDepositContainer::~CDepositContainer(Item::CDepositContainer *this); // idb
char __thiscall Item::CDepositContainer::SerializeIn(Item::CDepositContainer *this, const char *szItemBuffer_In, unsigned int dwBufferSize_In);
char __thiscall Item::CDepositContainer::ClientUpdate(Item::CDepositContainer *this, CSendStream *ClientSendStream);
char __thiscall Item::CDepositContainer::DBUpdate(Item::CDepositContainer *this, CSendStream *AgentSendStream);
Item::CDepositContainer *__thiscall Item::CDepositContainer::`scalar deleting destructor'(Item::CDepositContainer *this, char a2);
void __thiscall Item::ItemData::DumpInfo(Item::ItemData *this, unsigned int dwCID, const char *szExtraString); // idb
void __thiscall Item::SpriteData::Initialize(Item::SpriteData *this); // idb
void __thiscall Item::StringData::Initialize(Item::StringData *this); // idb
void __thiscall Item::ItemGarbage::ItemGarbage(Item::ItemGarbage *this, Item::CItem *lpItem, unsigned int dwRemainNum); // idb
void __thiscall Item::ChemicalInfo::ChemicalInfo(Item::ChemicalInfo *this); // idb
void __thiscall Item::SpriteData::SpriteData(Item::SpriteData *this); // idb
void __thiscall Item::ItemInfo::Initialize(Item::ItemInfo *this); // idb
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this); // idb
void __thiscall Item::ItemInfo::ItemInfo(Item::ItemInfo *this, unsigned __int16 usProtoTypeID); // idb
char __thiscall Item::CExchangeContainer::Initialize(Item::CStallContainer *this, CCharacter *lpCharacter, unsigned __int8 cStallWidth, unsigned __int8 cStallHeight);
void __thiscall Item::CStallContainer::Destroy(Item::CStallContainer *this); // idb
char __thiscall Item::CStallContainer::StallPriceOut(Item::CStallContainer *this, unsigned int *szStallPriceBuffer_Out, unsigned __int8 *cItemNum_Out);
char __thiscall Item::CStallContainer::Open(Item::CStallContainer *this, char *strStallName);
char __thiscall Item::CStallContainer::Leave(Item::CStallContainer *this, CCharacter *lpCustomer);
char __thiscall Item::CStallContainer::SendCharStallOpen(Item::CStallContainer *this, char *strStallName);
char __thiscall Item::CStallContainer::SendAllCustomer(Item::CStallContainer *this, char *szPacket, unsigned int dwPacketSize, bool bIncludeOwner, unsigned __int8 cCMD_In);
void __thiscall Item::CStallContainer::~CStallContainer(Item::CStallContainer *this); // idb
void __thiscall Item::CStallContainer::RollBackAllItem(Item::CStallContainer *this); // idb
char __thiscall Item::CStallContainer::SetItem(Item::CStallContainer *this, unsigned int itemPos, Item::CItem *lpItem);
void __thiscall Item::CStallContainer::SwapPosAllItem(Item::CStallContainer *this); // idb
char __thiscall Item::CStallContainer::Close(Item::CStallContainer *this);
char __thiscall Item::CStallContainer::SendCharStallItemInfo(Item::CStallContainer *this, CCharacter *pCustomer);
char __thiscall Item::CStallContainer::SendRemoveItem(Item::CStallContainer *this, Item::ItemPos itemPos, unsigned __int8 cNum);
char __thiscall Item::CStallContainer::SendCharStallEnter(Item::CStallContainer *this, unsigned int dwCustomerID, unsigned int dwOwnerID);
void __thiscall Item::CStallContainer::CStallContainer(Item::CStallContainer *this); // idb
Item::CStallContainer *__thiscall Item::CStallContainer::`vector deleting destructor'(Item::CStallContainer *this, char a2);
char __thiscall Item::CStallContainer::RemoveItem(Item::CStallContainer *this, unsigned int itemPos);
char __thiscall Item::CStallContainer::Enter(Item::CStallContainer *this, CCharacter *lpCustomer);
unsigned __int16 __thiscall Guild::CGuild::SetMark(Guild::CGuild *this, unsigned int dwSenderID, char *szMark, unsigned int dwGold); // idb
char __thiscall Guild::CGuild::SetInclination(Guild::CGuild *this, unsigned __int8 cInclination);
char __thiscall Guild::CGuild::AddGold(Guild::CGuild *this, unsigned int dwGold);
char __thiscall Guild::CGuild::DeductGold(Guild::CGuild *this, unsigned int dwGold);
Guild::MemberInfo *__thiscall std::vector<Guild::MemberInfo>::size(std::vector<Guild::MemberInfo> *this);
std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Min(std::_Tree_nod<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CTimerProcMgr::InternalTimerData>>,0>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<unsigned int,CTimerProcMgr::InternalTimerData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CTimerProcMgr::InternalTimerData> >,0> >::const_iterator *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::const_iterator::_Dec(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::const_iterator *this); // idb
_DWORD *__thiscall std::pair<unsigned long const,Guild::RelationInfo>::pair<unsigned long const,Guild::RelationInfo>(_DWORD *this, int a2);
void __cdecl std::fill<Guild::MemberInfo *,Guild::MemberInfo>(Guild::MemberInfo *_First, Guild::MemberInfo *_Last, const Guild::MemberInfo *_Val); // idb
void __thiscall std::pair<unsigned long,Guild::RelationInfo>::pair<unsigned long,Guild::RelationInfo>(std::pair<unsigned long,Guild::RelationInfo> *this, const unsigned int *_Val1, const Guild::RelationInfo *_Val2); // idb
void __thiscall ATL::CTime::CTime(ATL::CTime *this, int nYear, int nMonth, int nDay, int nHour, int nMin, int nSec, int nDST); // idb
void __thiscall GuildSmallInfoNode::GuildSmallInfoNode(GuildSmallInfoNode *this, unsigned int dwGID, unsigned __int8 cIndex, unsigned __int8 cInclination, unsigned __int16 wRank, unsigned int dwFame, unsigned __int8 cLevel, unsigned __int8 cCurrentMemberNum, const char *szGuildName, const char *szMasterName); // idb
void __thiscall GuildLargeInfoNode::GuildLargeInfoNode(GuildLargeInfoNode *this, unsigned int dwGID, unsigned __int8 cIndex, unsigned __int8 cInclination, unsigned __int16 wRank, unsigned int dwFame, unsigned __int8 cLevel, unsigned __int8 cCurrentMemberNum, const char *szMasterName, const char *szGuildName, const char *szMark, unsigned __int8 cRelation); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Rootnode); // idb
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::make_pair<unsigned long,Guild::RelationInfo>(std::pair<unsigned long,Guild::RelationInfo> *result, unsigned int _Val1, Guild::RelationInfo _Val2); // idb
Guild::MemberInfo *__cdecl std::copy_backward<Guild::MemberInfo *,Guild::MemberInfo *>(Guild::MemberInfo *_First, Guild::MemberInfo *_Last, Guild::MemberInfo *_Dest); // idb
Guild::MemberInfo *__cdecl std::_Uninit_copy<Guild::MemberInfo *,Guild::MemberInfo *,std::allocator<Guild::MemberInfo>>(Guild::MemberInfo *_First, Guild::MemberInfo *_Last, Guild::MemberInfo *_Dest);
void __cdecl std::_Rotate<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Mid, std::vector<Guild::MemberInfo>::iterator _Last);
int __thiscall GuildRight::IsValid(GuildRight *this); // idb
int __thiscall Guild::CGuild::GetCurrentMemberNum(Guild::CGuild *this);
char __thiscall Guild::CGuild::JoinMember(Guild::CGuild *this, unsigned int dwCID, unsigned __int8 cTitle, unsigned __int16 *wError);
bool __thiscall Guild::CGuild::JoinMemberDB(Guild::CGuild *this, GuildMemberDB *guildMemberDB); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Rarg, const std::pair<unsigned long const ,Guild::RelationInfo> *_Val, char _Carg); // idb
void __cdecl std::_Uninit_fill_n<Guild::MemberInfo *,unsigned int,Guild::MemberInfo,std::allocator<Guild::MemberInfo>>(Guild::MemberInfo *_First, unsigned int _Count, const Guild::MemberInfo *_Val);
char __thiscall Guild::CGuild::SetTitle(Guild::CGuild *this, unsigned int dwSuperior, unsigned int dwFollower, unsigned __int8 cTitle, unsigned __int16 *wError);
Guild::MemberInfo *__thiscall Guild::CGuild::GetMaster(Guild::CGuild *this, Guild::MemberInfo *result); // idb
unsigned __int8 __thiscall Guild::CGuild::GetTitle(Guild::CGuild *this, unsigned int dwCID); // idb
void __thiscall Guild::CGuild::ReleaseGold(Guild::CGuild *this, unsigned int dwGold); // idb
void __thiscall Guild::CGuild::UpgradeMemberRespawnSpeedByEmblem(Guild::CGuild *this, unsigned __int8 cUpgradeType, unsigned __int8 cUpgradeStep); // idb
void __thiscall Guild::CGuild::DegradeMemberRespawnSpeedByEmblem(Guild::CGuild *this); // idb
char __thiscall Guild::CGuild::InviteMember(Guild::CGuild *this, unsigned int dwMember, unsigned int dwGuest, unsigned __int16 *wError);
char __thiscall Guild::CGuild::KickMember(Guild::CGuild *this, unsigned int dwSuperior, unsigned int dwFollower, unsigned __int16 *wError);
char __thiscall Guild::CGuild::LogInOutMember(Guild::CGuild *this, unsigned int dwCID, unsigned int dwServerID);
char __thiscall Guild::CGuild::UpdateMemberInfo(Guild::CGuild *this, unsigned int dwCID, unsigned int dwValue, unsigned __int8 cCmd);
bool __thiscall Guild::CGuild::UpdateMemberInfo(Guild::CGuild *this, unsigned int dwCID, Guild::MemberListInfo memberListInfo, Guild::MemberDetailInfo memberDetailInfo); // idb
char __thiscall Guild::CGuild::SendGuildSafe(Guild::CGuild *this, unsigned int dwCID, char *szCharName, unsigned __int8 cCmd);
void __thiscall Guild::CGuild::SendAllMember(Guild::CGuild *this, char *szPacket, unsigned int dwPacketSize, unsigned __int8 cCMD_In);
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::find(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result, const unsigned int *_Keyval); // idb
unsigned __int16 __thiscall Guild::CGuild::SetMark(Guild::CGuild *this, unsigned int dwSenderID, char *szMark); // idb
char __thiscall Guild::CGuild::SetLevel(Guild::CGuild *this, unsigned int dwMaster, unsigned __int8 cLevel);
char __thiscall Guild::CGuild::SetLevel(Guild::CGuild *this, unsigned __int8 cLevel, unsigned int dwGold);
char __thiscall Guild::CGuild::SetInclination(Guild::CGuild *this, unsigned int dwMaster, unsigned __int8 cInclination, unsigned __int16 *wError);
char __thiscall Guild::CGuild::SetRight(Guild::CGuild *this, unsigned int dwMaster, GuildRight guildRight);
char __thiscall Guild::CGuild::SetRight(Guild::CGuild *this, GuildRight guildRight);
char __thiscall Guild::CGuild::SetTitle(Guild::CGuild *this, unsigned int dwCID, unsigned __int8 cTitle);
void __thiscall Guild::CGuild::SendGuildRelationToAllMember(Guild::CGuild *this, unsigned int dwRelationGID, char cRelation, unsigned __int8 cState, unsigned __int8 cWaitTime, unsigned __int8 cSubCmd);
bool __thiscall Guild::CGuild::IsFriendlyGuild(Guild::CGuild *this, unsigned int dwGID); // idb
bool __thiscall Guild::CGuild::IsNeutralityGuild(Guild::CGuild *this, unsigned int dwGID); // idb
unsigned __int8 __thiscall Guild::CGuild::GetRelation(Guild::CGuild *this, unsigned int dwRelationGID); // idb
unsigned __int8 __thiscall Guild::CGuild::GetRelationState(Guild::CGuild *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator cRelationType, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator dwRelationGID);
const GuildLargeInfoNode *__thiscall Guild::CGuild::GetLargeInfo(Guild::CGuild *this, GuildLargeInfoNode *result, unsigned __int8 cIndexOfPage, unsigned __int16 wRank, Guild::CGuild *lpRelationGuild); // idb
Guild::MemberInfo *__thiscall std::vector<Guild::MemberInfo>::_Ufill(std::vector<Guild::MemberInfo> *this, Guild::MemberInfo *_Ptr, unsigned int _Count, const Guild::MemberInfo *_Val); // idb
char __thiscall Guild::CGuild::LeaveMember(Guild::CGuild *this, unsigned int dwCID);
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,Guild::RelationInfo> *_Val); // idb
void __thiscall __noreturn std::vector<Guild::MemberInfo>::_Xlen(std::vector<Guild::MemberInfo> *this);
std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last); // idb
bool __thiscall Guild::CompareMemberName::operator()(Guild::CompareMemberName *this, const Guild::MemberInfo *lhs, const Guild::MemberInfo *rhs); // idb
char __thiscall Guild::CGuild::DeleteRelationList(Guild::CGuild *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator cRelationType, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator dwRelationGID);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator,bool> *result, std::pair<unsigned long const ,Guild::RelationInfo> *_Val); // idb
void __thiscall std::vector<Guild::MemberInfo>::_Insert_n(std::vector<Guild::MemberInfo> *this, std::vector<Guild::MemberInfo>::iterator _Where, unsigned int _Count, const Guild::MemberInfo *_Val); // idb
void std::_Insertion_sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, ...);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val, ...);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void __cdecl std::_Push_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Top, Guild::MemberInfo _Val);
void std::_Med3<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Mid, std::vector<Guild::MemberInfo>::iterator _Last, ...);
bool __thiscall Guild::CGuild::InsertRelation(Guild::CGuild *this, unsigned int dwRelationGID, const Guild::RelationInfo *Info); // idb
char __thiscall Guild::CGuild::InsertRelationList(Guild::CGuild *this, unsigned __int8 cRelation, unsigned __int8 cState, unsigned int dwRelationGID);
void __thiscall std::vector<Guild::MemberInfo>::_Construct_n(std::vector<Guild::MemberInfo> *this, unsigned int _Count, const Guild::MemberInfo *_Val); // idb
std::vector<Guild::MemberInfo>::iterator *__thiscall std::vector<Guild::MemberInfo>::insert(std::vector<Guild::MemberInfo> *this, std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _Where, const Guild::MemberInfo *_Val); // idb
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Adjust_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator _First, int _Hole, int _Bottom, Guild::MemberInfo _Val, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Make_heap<std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::_Median<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Mid, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __thiscall std::map<unsigned long,Guild::RelationInfo>::~map<unsigned long,Guild::RelationInfo>(std::map<unsigned long,Guild::RelationInfo> *this); // idb
void __thiscall Guild::CGuild::~CGuild(Guild::CGuild *this); // idb
bool __thiscall Guild::CGuild::ChangeRelation(Guild::CGuild *this, unsigned int dwRelationGID, std::map<unsigned long,Guild::RelationInfo> *Info);
void __thiscall std::vector<Guild::MemberInfo>::vector<Guild::MemberInfo>(std::vector<Guild::MemberInfo> *this, unsigned int _Count); // idb
void __thiscall std::vector<Guild::MemberInfo>::push_back(std::vector<Guild::MemberInfo> *this, const Guild::MemberInfo *_Val); // idb
std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> *__cdecl std::_Unguarded_partition<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::pair<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator> *result, std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __thiscall Guild::CGuild::CGuild(Guild::CGuild *this, unsigned int dwGID, unsigned __int8 cInclination, char *szName); // idb
void __thiscall Guild::CGuild::CGuild(Guild::CGuild *this, GuildInfoDB *guildInfo); // idb
bool __thiscall Guild::CGuild::SetRelation(Guild::CGuild *this, unsigned __int8 cType, unsigned int dwRelationGID, Guild::RelationInfo *Info); // idb
char __thiscall Guild::CGuild::JoinMember(Guild::CGuild *this, Guild::MemberInfo *memberInfo);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
void __cdecl std::sort_heap<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::_Partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,int,Guild::MemberInfo,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
void __cdecl std::_Sort<std::vector<Guild::MemberInfo>::iterator,int,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Ideal, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberTitle>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberClass>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberLevel>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberFame>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberGold>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
std::vector<Guild::MemberInfo>::iterator *__cdecl std::partial_sort_copy<std::vector<Guild::MemberInfo>::iterator,std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberPosition>(std::vector<Guild::MemberInfo>::iterator *result, std::vector<Guild::MemberInfo>::iterator _First1, std::vector<Guild::MemberInfo>::iterator _Last1, std::vector<Guild::MemberInfo>::iterator _First2, std::vector<Guild::MemberInfo>::iterator _Last2, int _Pred);
void __cdecl std::sort<std::vector<Guild::MemberInfo>::iterator,Guild::CompareMemberName>(std::vector<Guild::MemberInfo>::iterator _First, std::vector<Guild::MemberInfo>::iterator _Last, int _Pred);
unsigned __int8 __thiscall Guild::CGuild::SendMemberList(Guild::CGuild *this, CCharacter *lpCharacter, unsigned __int8 cSortCmd, unsigned __int8 cPage);
bool __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::empty(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this); // idb
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Min(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Pnode); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__cdecl std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Max(std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Pnode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const,CMsgProc *>>,0>>::const_iterator::_Dec(std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Dec(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *this); // idb
void __cdecl std::fill<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo>>(std::pair<unsigned long,Guild::RelationInfo> *_First, std::pair<unsigned long,Guild::RelationInfo> *_Last, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
void __cdecl std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, const std::pair<unsigned long,unsigned long> *_Val); // idb
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Copy_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(std::pair<unsigned long,Guild::RelationInfo> *_First, std::pair<unsigned long,Guild::RelationInfo> *_Last, std::pair<unsigned long,Guild::RelationInfo> *_Dest);
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Copy_backward_opt<std::pair<unsigned long,Guild::RelationInfo> *,std::pair<unsigned long,Guild::RelationInfo> *>(std::pair<unsigned long,Guild::RelationInfo> *_First, std::pair<unsigned long,Guild::RelationInfo> *_Last, std::pair<unsigned long,Guild::RelationInfo> *_Dest);
void __cdecl std::swap<Guild::RelationInfo>(Guild::RelationInfo *_Left, Guild::RelationInfo *_Right); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::const_iterator::_Inc(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *this); // idb
std::pair<unsigned long,Guild::RelationInfo> *__cdecl std::_Uninit_copy<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::pair<unsigned long,Guild::RelationInfo> *,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(std::pair<unsigned long,Guild::RelationInfo> *_First, std::pair<unsigned long,Guild::RelationInfo> *_Last, std::pair<unsigned long,Guild::RelationInfo> *_Dest);
std::pair<unsigned long,unsigned long> *__cdecl std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, std::pair<unsigned long,unsigned long> *_Dest);
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First, int _Hole, int _Top, std::pair<unsigned long,Guild::CGuild *> _Val);
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, int _Hole, int _Top, std::pair<unsigned long const ,Guild::RelationInfo> _Val);
void __cdecl std::_Push_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, int _Hole, int _Top, std::pair<unsigned long,Guild::RelationInfo> _Val);
void __cdecl std::_Rotate<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Rootnode); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this); // idb
Guild::FnDeleteSecond __cdecl std::for_each<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,Guild::FnDeleteSecond>(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last, Guild::FnDeleteSecond _Func); // idb
void __cdecl std::_Uninit_fill_n<std::pair<unsigned long,Guild::RelationInfo> *,unsigned int,std::pair<unsigned long,Guild::RelationInfo>,std::allocator<std::pair<unsigned long,Guild::RelationInfo>>>(std::pair<unsigned long,Guild::RelationInfo> *_First, unsigned int _Count, const std::pair<unsigned long,Guild::RelationInfo> *_Val);
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, int _Hole, int _Bottom, std::pair<unsigned long const ,Guild::RelationInfo> _Val);
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long,Guild::CGuild *>,Guild::CompareGuildFame>(std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First, int _Hole, int _Bottom, std::pair<unsigned long,Guild::CGuild *> _Val);
void __cdecl std::_Adjust_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, int _Hole, int _Bottom, std::pair<unsigned long,Guild::RelationInfo> _Val);
void __cdecl std::_Pop_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Dest, std::pair<unsigned long,Guild::RelationInfo> _Val);
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::_Lbound(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, const std::string *_Keyval); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Ubound(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, const std::string *_Keyval); // idb
void __cdecl std::_Make_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
void __cdecl std::_Med3<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
CParty *__thiscall Guild::CGuildMgr::GetGuild(CPartyMgr *this, unsigned int dwPartyUID); // idb
std::pair<unsigned long,Guild::RelationInfo> *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Ufill(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, std::pair<unsigned long,Guild::RelationInfo> *_Ptr, unsigned int _Count, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
void __cdecl std::advance<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::iterator,unsigned short>(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *_Where, unsigned __int16 _Off);
void __cdecl std::_Insertion_sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
void __cdecl std::_Median<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Mid, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string>,std::allocator<std::pair<std::string const,unsigned char>>,0>>::find(std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::const_iterator *result, const std::string *_Keyval); // idb
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,Guild::CompareGuildFame>(std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First, std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Last);
void __cdecl std::sort_heap<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> *__cdecl std::_Unguarded_partition<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::pair<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator> *result, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last);
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last); // idb
std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
void __thiscall __noreturn std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Xlen(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this);
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Insert_n(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Where, unsigned int _Count, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Insert_n(std::vector<std::pair<unsigned long,Guild::CGuild *>> *this, std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Where, unsigned int _Count, const std::pair<unsigned long,Guild::CGuild *> *_Val); // idb
void __thiscall std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Node::_Node(std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *this, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Rarg, const std::pair<std::string const ,Guild::CGuild *> *_Val, char _Carg); // idb
std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *__cdecl std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator,int,std::pair<unsigned long const,Guild::CGuild *>,Guild::CompareGuildFame>(std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First1, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last1, std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _First2, std::vector<std::pair<unsigned long,Guild::CGuild *>>::iterator _Last2);
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__cdecl std::_Partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,std::pair<unsigned long const,Guild::RelationInfo>,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First1, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last1, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First2, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last2);
void __cdecl std::_Sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,int,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last, int _Ideal, Guild::CompareRelationState _Pred); // idb
Guild::CGuild *__thiscall Guild::CGuildMgr::GetGuild(Guild::CGuildMgr *this, char *szName); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::_Node *_Val);
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::CGuild *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> > *this, const unsigned int *_Keyval);
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::reserve(std::vector<std::pair<unsigned long,Guild::CGuild *>> *this, unsigned int _Count); // idb
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::reserve(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, std::pair<unsigned long,Guild::RelationInfo> *_Count);
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::insert(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Where, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Larg, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Parg, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Rarg, const std::pair<std::string const ,Guild::CGuild *> *_Val, char _Carg); // idb
std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *__cdecl std::partial_sort_copy<std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Guild::RelationInfo>>,0>>::iterator,std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _First1, std::_Tree<std::_Tmap_traits<unsigned long,Guild::RelationInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::RelationInfo> >,0> >::iterator _Last1, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First2, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last2);
void __cdecl std::sort<std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator,Guild::CompareRelationState>(std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _First, std::vector<std::pair<unsigned long,Guild::RelationInfo>>::iterator _Last, Guild::CompareRelationState _Pred); // idb
void __thiscall std::map<unsigned long,Guild::CGuild *>::~map<unsigned long,Guild::CGuild *>(std::map<unsigned long,Guild::CGuild *> *this); // idb
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::push_back(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
void __thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *_Rootnode);
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::_Node *_Wherenode, const std::pair<std::string const ,Guild::CGuild *> *_Val); // idb
void __thiscall std::vector<std::pair<unsigned long,Guild::CGuild *>>::_Assign_n(std::vector<std::pair<unsigned long,Guild::CGuild *>> *this, unsigned int _Count, const std::pair<unsigned long,Guild::CGuild *> *_Val); // idb
void __thiscall std::vector<std::pair<unsigned long,Guild::RelationInfo>>::_Assign_n(std::vector<std::pair<unsigned long,Guild::RelationInfo>> *this, unsigned int _Count, const std::pair<unsigned long,Guild::RelationInfo> *_Val); // idb
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::insert(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator,bool> *result, std::pair<std::string const ,Guild::CGuild *> *_Val); // idb
std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string>,std::allocator<std::pair<std::string const,Guild::CGuild *>>,0>>::erase(std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> > *this, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<std::string,Guild::CGuild *,std::less<std::string >,std::allocator<std::pair<std::string const ,Guild::CGuild *> >,0> >::iterator _Last); // idb
void __thiscall Guild::CGuildMgr::Destroy(Guild::CGuildMgr *this); // idb
char __thiscall Guild::CGuildMgr::CreateGuild(Guild::CGuildMgr *this, unsigned int dwMasterID, unsigned int dwGuildID, unsigned __int8 cInclination, char *szGuildName);
char __thiscall Guild::CGuildMgr::GetSortingPageList(Guild::CGuildMgr *this, CCharacter *lpCharacter, unsigned __int8 cSortCmd, unsigned __int8 cPage, GuildLargeInfoNode *aryCurrentInfoList);
char __thiscall Guild::CGuildMgr::SerializeIn(Guild::CGuildMgr *this, char *lpBuffer_In, unsigned __int16 wBufferSize_In, unsigned int cTotalMemberNum, unsigned __int8 cFriendlyNum, unsigned __int8 cHostilityNum, unsigned __int8 cNeutralityNum);
void __thiscall std::map<std::string,Guild::CGuild *>::~map<std::string,Guild::CGuild *>(std::map<std::string,Guild::CGuild *> *this); // idb
void __thiscall Guild::CGuildMgr::~CGuildMgr(Guild::CGuildMgr *this); // idb
char __thiscall Guild::CGuildMgr::DissolveGuild(Guild::CGuildMgr *this, unsigned int dwGID);
char __thiscall Guild::CGuildMgr::SendGuildList(Guild::CGuildMgr *this, unsigned int dwCID, unsigned __int8 cSortCmd, unsigned __int8 cPage, unsigned __int8 cNum, GuildCheckSumNode *lpNode);
void __thiscall Guild::CGuildMgr::CGuildMgr(Guild::CGuildMgr *this); // idb
Guild::CGuildMgr *__cdecl Guild::CGuildMgr::GetInstance(); // idb
void __thiscall Item::CEquipmentsContainer::CEquipmentsContainer(Item::CEquipmentsContainer *this); // idb
void __thiscall Item::CEquipmentsContainer::~CEquipmentsContainer(Item::CEquipmentsContainer *this); // idb
char __thiscall Item::CEquipmentsContainer::Initialize(Item::CEquipmentsContainer *this, CCharacter *lpCharacter, unsigned __int16 nMaxSize);
char __thiscall Item::CEquipmentsContainer::RemoveItem(Item::CEquipmentsContainer *this, Item::ItemPos itemPos);
bool __thiscall Item::CEquipmentsContainer::CheckEquipPos(Item::CEquipmentsContainer *this, unsigned __int16 itemPos, Item::CItem *lpItem);
void __thiscall Item::CEquipmentsContainer::CalculateCharacterStatus(Item::CEquipmentsContainer *this, CCharacter *pCharacter); // idb
char __thiscall Item::CEquipmentsContainer::SetEquipmentsAttribute(Item::CEquipmentsContainer *this);
Item::CEquipmentsContainer *__thiscall Item::CEquipmentsContainer::`scalar deleting destructor'(Item::CEquipmentsContainer *this, char a2);
char __thiscall Item::CEquipmentsContainer::SetItem(Item::CEquipmentsContainer *this, Item::ItemPos itemPos, Item::CItem *lpItem);
void __thiscall Item::CExchangeContainer::CExchangeContainer(Item::CExchangeContainer *this); // idb
void __thiscall Item::CExchangeContainer::~CExchangeContainer(Item::CExchangeContainer *this); // idb
char __thiscall Item::CExchangeContainer::CheckLock(Item::CExchangeContainer *this);
void __thiscall Item::CExchangeContainer::ExchangeCancel(Item::CExchangeContainer *this); // idb
Item::CExchangeContainer *__thiscall Item::CExchangeContainer::`vector deleting destructor'(Item::CExchangeContainer *this, char a2);
char __thiscall Item::CExchangeContainer::SetItem(Item::CExchangeContainer *this, unsigned int itemPos, Item::CItem *lpItem);
char __thiscall Item::CExchangeContainer::RemoveItem(Item::CExchangeContainer *this, unsigned int itemPos);
char __thiscall Item::CExchangeContainer::AddGold(Item::CExchangeContainer *this, unsigned int dwGold);
char __thiscall Item::CExchangeContainer::DeductGold(Item::CExchangeContainer *this, unsigned int dwGold);
bool __thiscall Item::CExchangeContainer::ExchangeOK(Item::CExchangeContainer *this, bool bOK); // idb
CFriendList::Rebind *__thiscall std::vector<CFriendList::Rebind>::size(std::vector<CFriendList::Rebind> *this);
void __thiscall CSingleton<CXRefFriends>::~CSingleton<CXRefFriends>(CSingleton<CXRefFriends> *this); // idb
void __cdecl std::fill<CFriendList::Rebind *,CFriendList::Rebind>(CFriendList::Rebind *_First, CFriendList::Rebind *_Last, const CFriendList::Rebind *_Val); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Rootnode); // idb
CFriendList::Rebind *__cdecl std::copy_backward<CFriendList::Rebind *,CFriendList::Rebind *>(CFriendList::Rebind *_First, CFriendList::Rebind *_Last, CFriendList::Rebind *_Dest); // idb
CFriendList::Rebind *__cdecl std::_Uninit_copy<CFriendList::Rebind *,CFriendList::Rebind *,std::allocator<CFriendList::Rebind>>(CFriendList::Rebind *_First, CFriendList::Rebind *_Last, CFriendList::Rebind *_Dest);
std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this); // idb
void __cdecl std::_Uninit_fill_n<CFriendList::Rebind *,unsigned int,CFriendList::Rebind,std::allocator<CFriendList::Rebind>>(CFriendList::Rebind *_First, unsigned int _Count, const CFriendList::Rebind *_Val);
std::vector<CFriendList::Rebind>::iterator *__cdecl std::_Lower_bound<std::vector<CFriendList::Rebind>::iterator,unsigned long,int>(std::vector<CFriendList::Rebind>::iterator *result, std::vector<CFriendList::Rebind>::iterator _First, std::vector<CFriendList::Rebind>::iterator _Last, unsigned int *_Val);
CFriendList::Rebind *__thiscall std::vector<CFriendList::Rebind>::_Ufill(std::vector<CFriendList::Rebind> *this, CFriendList::Rebind *_Ptr, unsigned int _Count, const CFriendList::Rebind *_Val); // idb
CFriendList::Rebind *__thiscall CFriendList::GetFriend(CFriendList *this, unsigned int dwFriendCID); // idb
std::vector<CFriendList::Rebind>::iterator *__thiscall std::vector<CFriendList::Rebind>::erase(std::vector<CFriendList::Rebind> *this, std::vector<CFriendList::Rebind>::iterator *result, std::vector<CFriendList::Rebind>::iterator _Where); // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val); // idb
void __thiscall __noreturn std::vector<CFriendList::Rebind>::_Xlen(std::vector<CFriendList::Rebind> *this);
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator,bool> *result, const std::pair<unsigned long const ,unsigned long> *_Val); // idb
void __thiscall std::vector<CFriendList::Rebind>::_Insert_n(std::vector<CFriendList::Rebind> *this, std::vector<CFriendList::Rebind>::iterator _Where, unsigned int _Count, const CFriendList::Rebind *_Val); // idb
std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,unsigned long>>,1>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::iterator _Last); // idb
void __thiscall CFriendList::Clear(CFriendList *this); // idb
char __thiscall CFriendList::Remove(CFriendList *this, unsigned int dwFriendCID);
std::vector<CFriendList::Rebind>::iterator *__thiscall std::vector<CFriendList::Rebind>::insert(std::vector<CFriendList::Rebind> *this, std::vector<CFriendList::Rebind>::iterator *result, std::vector<CFriendList::Rebind>::iterator _Where, const CFriendList::Rebind *_Val); // idb
void __thiscall CFriendList::CFriendList(CFriendList *this, unsigned int dwOwnerCID, CXRefFriends *lpXRefTable); // idb
void __thiscall CFriendList::~CFriendList(CFriendList *this); // idb
char __thiscall CFriendList::Add(CFriendList *this, unsigned int dwFriendCID, const char *szCharacterName);
char __thiscall CFriendList::SerializeIn(CFriendList *this, char *szBuffer_In, unsigned int dwBufferSize_In);
void __thiscall CXRefFriends::CXRefFriends(CXRefFriends *this); // idb
void __thiscall CXRefFriends::~CXRefFriends(CXRefFriends *this); // idb
BanInfo *__cdecl std::_Copy_opt<BanInfo *,BanInfo *>(BanInfo *_First, BanInfo *_Last, BanInfo *_Dest);
BanInfo *__cdecl std::_Uninit_copy<BanInfo *,BanInfo *,std::allocator<BanInfo>>(BanInfo *_First, BanInfo *_Last, BanInfo *_Dest);
void __cdecl std::_Uninit_fill_n<BanInfo *,unsigned int,BanInfo,std::allocator<BanInfo>>(BanInfo *_First, unsigned int _Count, const BanInfo *_Val);
std::vector<BanInfo>::iterator *__cdecl std::_Lower_bound<std::vector<BanInfo>::iterator,unsigned long,int>(std::vector<BanInfo>::iterator *result, std::vector<BanInfo>::iterator _First, std::vector<BanInfo>::iterator _Last, unsigned int *_Val);
bool __thiscall CBanList::IsBan(CBanList *this, unsigned int dwBanCID, char *szCharacterName);
char *__thiscall CBanList::GetBanName(CBanList *this, unsigned int dwCID);
char __thiscall CBanList::Remove(CBanList *this, unsigned int dwBanCID);
void __thiscall __noreturn std::vector<BanInfo>::_Xlen(std::vector<BanInfo> *this);
void __thiscall std::vector<BanInfo>::_Insert_n(std::vector<BanInfo> *this, std::vector<BanInfo>::iterator _Where, unsigned int _Count, const BanInfo *_Val); // idb
std::vector<BanInfo>::iterator *__thiscall std::vector<BanInfo>::insert(std::vector<BanInfo> *this, std::vector<BanInfo>::iterator *result, std::vector<BanInfo>::iterator _Where, const BanInfo *_Val); // idb
void __thiscall std::vector<enum Item::ItemType::Type>::vector<enum Item::ItemType::Type>(CBanList *this); // idb
char __thiscall CBanList::Add(CBanList *this, unsigned int dwBanCID, const char *szCharacterName);
char __thiscall CBanList::SerializeIn(CBanList *this, const char *szBuffer_In, unsigned int dwBufferSize_In);
void __thiscall GAMELOG::sLogBase::InitLogBase(GAMELOG::sLogBase *this, unsigned int dwUID, unsigned int dwCID, const Position *Pos, int time, unsigned __int8 cCmd, unsigned __int8 cErr);
void __thiscall GAMELOG::sCharLoginOut::InitCharLog(GAMELOG::sCharLoginOut *this, const sockaddr_in *lpSockAddr_In, unsigned __int8 cAdmin, const unsigned __int16 *usDataSize, unsigned __int16 usDepositData, unsigned int dwDepositMoney);
void __cdecl GAMELOG::LogCharLoginOut(unsigned int dwUID, CCharacter *lpCharacter_In, const sockaddr_in *lpSockAddr, const char *lpCharacterInfo, int nTotalSize, const unsigned __int16 *usUpdates, unsigned __int8 cCMD, const unsigned __int16 eError);
void __cdecl GAMELOG::LogCharLevelUp(const CCharacter *character, unsigned __int16 usIP, unsigned __int8 cLevel, unsigned __int16 eError);
void __cdecl GAMELOG::LogCharBindPos(const CCharacter *character, unsigned int dwNPCID, unsigned __int16 eError);
void __cdecl GAMELOG::LogCharDead(const CCharacter *character, unsigned __int64 dwPrevExp, unsigned __int64 dwNextExp, const CAggresiveCreature *lpOffencer_In, unsigned __int8 cLevel, unsigned __int16 eError);
void __cdecl GAMELOG::LogCharRespawn(const CCharacter *character, unsigned __int64 dwPrevExp, unsigned __int64 dwNextExp, unsigned __int16 eError);
void __cdecl GAMELOG::LogZoneMove(const CCharacter *character, unsigned __int8 cZone, unsigned __int8 cChannel, unsigned __int16 usError); // idb
char __thiscall VirtualArea::CVirtualArea::CreateCell(VirtualArea::CVirtualArea *this, unsigned __int16 wWidth, unsigned __int16 wHeight, unsigned __int16 wMapIndex);
void __thiscall VirtualArea::CVirtualArea::CreateVirtualMonsterManager(VirtualArea::CVirtualArea *this); // idb
void __thiscall VirtualArea::CVirtualArea::ProcessAllMonster(VirtualArea::CVirtualArea *this); // idb
void __thiscall VirtualArea::CVirtualArea::ProcessMonsterRegenHPAndMP(VirtualArea::CVirtualArea *this); // idb
void __thiscall VirtualArea::CVirtualArea::ProcessSummonMonsterDead(VirtualArea::CVirtualArea *this); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetStartX(VirtualArea::CVirtualArea *this); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetStartZ(VirtualArea::CVirtualArea *this); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetWidth(VirtualArea::CVirtualArea *this); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::GetHeight(VirtualArea::CVirtualArea *this); // idb
unsigned __int8 __thiscall VirtualArea::CVirtualArea::GetVirtualZone(VirtualArea::CVirtualArea *this); // idb
Position *__thiscall VirtualArea::CVirtualArea::GetStartPosition(VirtualArea::CVirtualArea *this, Position *result, unsigned __int8 cNation); // idb
Position *__thiscall VirtualArea::CVirtualArea::GetRespawnPosition(VirtualArea::CVirtualArea *this, Position *result, unsigned __int8 cNation, unsigned int nIndex);
unsigned __int8 __thiscall VirtualArea::CVirtualArea::GetMaxRespawnPos(VirtualArea::CVirtualArea *this); // idb
char *__thiscall VirtualArea::CVirtualArea::GetMapTypeName(VirtualArea::CVirtualArea *this);
void __thiscall VirtualArea::CVirtualArea::ProcessDeleteItem(VirtualArea::CVirtualArea *this); // idb
char __thiscall VirtualArea::CVirtualArea::ProcessAllCellPrepareBroadCast(VirtualArea::CVirtualArea *this);
char __thiscall VirtualArea::CVirtualArea::ProcessAllCellBroadCast(VirtualArea::CVirtualArea *this);
void __thiscall VirtualArea::CVirtualArea::~CVirtualArea(VirtualArea::CVirtualArea *this); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::Enter(VirtualArea::CVirtualArea *this, CCharacter *lpCharacter, unsigned __int8 cMoveType); // idb
bool __thiscall CParty::ReLogin(VirtualArea::CVirtualArea *this, CCharacter *lpCharacter); // idb
unsigned __int16 __thiscall VirtualArea::CVirtualArea::AddCharacter(VirtualArea::CVirtualArea *this, CCharacter *lpSpectator); // idb
void __thiscall VirtualArea::CVirtualArea::CVirtualArea(VirtualArea::CVirtualArea *this, const VirtualArea::ProtoType *lpProtoType, unsigned __int16 wMapIndex); // idb
VirtualArea::CVirtualArea *__thiscall VirtualArea::CVirtualArea::`scalar deleting destructor'(VirtualArea::CVirtualArea *this, char a2);
void __thiscall Quest::ExecutingQuest::ExecutingQuest(Quest::ExecutingQuest *this); // idb
void __thiscall Quest::ExecutingQuest::ExecutingQuest(Quest::ExecutingQuest *this, unsigned __int16 wQuestID, unsigned __int8 cPhase, unsigned __int8 *aryTriggerCount); // idb
void __thiscall CSingleton<CDuelCellManager>::~CSingleton<CDuelCellManager>(CSingleton<CDuelCellManager> *this); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1>>::_Lrotate(std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *_Wherenode); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Rootnode); // idb
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _Where); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::iterator _Last); // idb
CCell *__thiscall CDuelCellManager::CreateCell(CDuelCellManager *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> >::_Node *dwCID);
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,CCell *> >,0> > *this, const unsigned int *_Keyval);
char __thiscall CDuelCellManager::DestroyCell(CDuelCellManager *this, unsigned int dwCID);
void __thiscall CDuelCellManager::~CDuelCellManager(CDuelCellManager *this); // idb
void __thiscall CDuelCellManager::CDuelCellManager(CDuelCellManager *this); // idb
char __cdecl GameClientSendPacket::SendCharSkillCommand(CSendStream *SendStream, unsigned int dwCharID, unsigned __int8 Cmd, unsigned __int8 cIndex, unsigned __int16 usSkillID, unsigned __int16 usError);
void __thiscall CCharacter::UpdateQuickSlotSkill(CCharacter *this, SKILLSLOT Slot); // idb
void __cdecl std::swap<QUICKSLOT>(QUICKSLOT *_Left, QUICKSLOT *_Right); // idb
bool __thiscall CCharacter::MoveQuickSlot(CCharacter *this, TakeType takeType, unsigned __int16 usSkillID);
void __thiscall CSpell::ClearAffected(CSpell *this); // idb
Skill::SpellTarget::Type __thiscall CSpell::GetSpellTarget(CSpell *this); // idb
void __thiscall CSpell::Disable(CSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CSpell::SendSpellInfo(CSpell *this, CAggresiveCreature *lpAffected, unsigned __int8 cSpellType, unsigned __int16 nEnchantLevel, bool bOnOff); // idb
void __thiscall CSpell::CSpell(CSpell *this, CSpell::Spell_Info *spell_Info, Skill::Type::SkillType eSpellType, unsigned int dwEffectFlag); // idb
void __thiscall CSpell::ClearAll(CSpell *this); // idb
void __thiscall CSpell::Destroy(CSpell *this); // idb
void __thiscall CSpell::CheckRange(CSpell *this); // idb
void __thiscall CSpell::Enable(CSpell *this, unsigned int dwOperateFlag); // idb
char __thiscall CSpell::AddAffected(CSpell *this, CAggresiveCreature *lpAffected, unsigned __int16 *wError);
char __thiscall CSpell::RemoveAffected(CSpell *this, CAggresiveCreature *pRemoved);
char __thiscall CSpell::Operate(CSpell *this);
void __thiscall CCastingSpell::ClearChant(CCastingSpell *this); // idb
void __thiscall CCastingSpell::ClearEnchant(CCastingSpell *this); // idb
void __thiscall CCastingSpell::EnableChant(CCastingSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CCastingSpell::DisableChant(CCastingSpell *this, unsigned int dwOperateFlag); // idb
char __thiscall CCastingSpell::Add(CCastingSpell *this, CSpell *pSpell);
char __thiscall CCastingSpell::Remove(CCastingSpell *this, CSpell *pSpell);
void __thiscall CAffectedSpell::ClearChant(CAffectedSpell *this); // idb
void __thiscall CAffectedSpell::ClearEnchant(CAffectedSpell *this); // idb
void __thiscall CAffectedSpell::EnableChant(CAffectedSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CAffectedSpell::EnableEnchant(CAffectedSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CAffectedSpell::DisableChant(CAffectedSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CAffectedSpell::DisableEnchant(CAffectedSpell *this, unsigned int dwOperateFlag); // idb
void __thiscall CAffectedSpell::ApplyPartyChant(CAffectedSpell *this, CAggresiveCreature *pAffected); // idb
void __thiscall CAffectedSpell::RemoveChantByCaster(CAffectedSpell *this, CAggresiveCreature *pCaster); // idb
char __thiscall CAffectedSpell::RemoveEnchantBySpellType(CAffectedSpell *this, unsigned int dwStatusFlag);
char __thiscall CAffectedSpell::IsSpellOfEnemyCharacter(CAffectedSpell *this);
char __thiscall CAffectedSpell::IsSpellThisTargetType(CAffectedSpell *this, Skill::SpellTarget::Type eTargetType);
unsigned __int8 __thiscall CAffectedSpell::Disenchant(CAffectedSpell *this, Skill::SpellType::Type eSpellType, Skill::SpellTarget::Type eTargetType, Skill::Disenchant::Type eDisenchantType, unsigned __int16 usSkillLevel, unsigned __int8 cNum); // idb
bool __thiscall CSpell::IsActivate(CSpell *this, CAggresiveCreature *lpAffected); // idb
CSpell *__thiscall CAffectedSpell::GetSpell(CAffectedSpell *this, unsigned __int16 usSpellID); // idb
char __thiscall CAffectedSpell::RemoveOverlappedSpell(CAffectedSpell *this, CSpell *pSpell);
char __thiscall CAffectedSpell::Add(CAffectedSpell *this, CSpell *pSpell, unsigned __int16 *wError);
char __thiscall CAffectedSpell::Remove(CAffectedSpell *this, CSpell *pSpell);
void __thiscall CChantSpell::Operate(CChantSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CBattleSongSpell::Activate(CBattleSongSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CBattleSongSpell::Deactivate(CBattleSongSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
void __thiscall CMaintenanceChantSpell::Operate(CMaintenanceChantSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CMaintenanceChantSpell::Activate(CMaintenanceChantSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CMaintenanceChantSpell::Deactivate(CMaintenanceChantSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
void __thiscall CAccelerationChantSpell::Operate(CAccelerationChantSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CAccelerationChantSpell::Activate(CAccelerationChantSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CAccelerationChantSpell::Deactivate(CAccelerationChantSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
void __thiscall CLifeAuraSpell::Operate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CLifeAuraSpell::Activate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CLifeAuraSpell::Deactivate(CLifeAuraSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CDefencePotionSpell::Activate(CDefencePotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CDefencePotionSpell::Deactivate(CDefencePotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CDisenchantPotionSpell::Activate(CDisenchantPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CDisenchantPotionSpell::Deactivate(CDisenchantPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CMagicPotionSpell::Activate(CMagicPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CMagicPotionSpell::Deactivate(CMagicPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CLightningPotionSpell::Activate(CLightningPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CLightningPotionSpell::Deactivate(CLightningPotionSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
void __thiscall CRegenerationSpell::Operate(CRegenerationSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CRegenerationSpell::Activate(CRegenerationSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CRegenerationSpell::Deactivate(CRegenerationSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStrengthSpell::Activate(CStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStrengthSpell::Deactivate(CStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CBlazeSpell::Activate(CBlazeSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CBlazeSpell::Deactivate(CBlazeSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CChargingSpell::Activate(CChargingSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CChargingSpell::Deactivate(CChargingSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStealthSpell::Activate(CStealthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStealthSpell::Deactivate(CStealthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CManaShellSpell::Activate(CManaShellSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CManaShellSpell::Deactivate(CManaShellSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CEncourageSpell::Activate(CEncourageSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CEncourageSpell::Deactivate(CEncourageSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CEnchantWeaponSpell::Activate(CEnchantWeaponSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CEnchantWeaponSpell::Deactivate(CEnchantWeaponSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CBrightArmorSpell::Activate(CBrightArmorSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CBrightArmorSpell::Deactivate(CBrightArmorSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CHardenSkinSpell::Activate(CHardenSkinSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CHardenSkinSpell::Deactivate(CHardenSkinSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CFlexibilitySpell::Activate(CFlexibilitySpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CFlexibilitySpell::Deactivate(CFlexibilitySpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CGuardSpell::Activate(CGuardSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CGuardSpell::Deactivate(CGuardSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CSlowSpell::Activate(CSlowSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CSlowSpell::Deactivate(CSlowSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CArmorBrokenSpell::Activate(CArmorBrokenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CArmorBrokenSpell::Deactivate(CArmorBrokenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CHoldSpell::Activate(CHoldSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CHoldSpell::Deactivate(CHoldSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStunSpell::Activate(CStunSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CStunSpell::Deactivate(CStunSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CFrozenSpell::Activate(CFrozenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CFrozenSpell::Deactivate(CFrozenSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
void __thiscall CPoisonedSpell::Operate(CPoisonedSpell *this, CAggresiveCreature *lpAffected); // idb
char __thiscall CPoisonedSpell::Activate(CPoisonedSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CPoisonedSpell::Deactivate(CPoisonedSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CLowerStrengthSpell::Activate(CLowerStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CLowerStrengthSpell::Deactivate(CLowerStrengthSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CInvincibleSpell::Activate(CInvincibleSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CInvincibleSpell::Deactivate(CInvincibleSpell *this, CAggresiveCreature *lpAffected, char dwOperateFlag);
char __thiscall CCharacter::DropItem(CCharacter *this, unsigned __int16 usProtoTypeID, unsigned __int8 cNum);
char __thiscall CCharacter::MovePos(CCharacter *this, Position Pos, char cZone, bool bSitDown);
char __thiscall CCharacter::MoveZone(CCharacter *this, POS NewPos, signed __int8 cZone, char Channel);
char __thiscall CCharacter::Kill(CCharacter *this, CCharacter *lpAttacker);
char __thiscall CCharacter::NotifyInfo(CCharacter *this, unsigned int dwAdminCID);
char __thiscall CCharacter::DuelInit(CCharacter *this, unsigned __int8 cCmd);
unsigned __int16 __thiscall VirtualArea::CBGServerMap::Enter(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter, unsigned __int8 cMoveType); // idb
char __thiscall VirtualArea::CBGServerMap::RuleCheck(VirtualArea::CBGServerMap *this, bool bTimeout);
void __thiscall VirtualArea::CBGServerMap::DeleteAllItem(VirtualArea::CBGServerMap *this); // idb
std::list<CCharacter *>::iterator *__thiscall std::list<CCharacter *>::erase(std::list<CCharacter *> *this, std::list<CCharacter *>::iterator *result, std::list<CCharacter *>::iterator _Where); // idb
void __thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Erase(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Rootnode); // idb
bool __thiscall VirtualArea::CBGServerMap::IsPlayer(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter); // idb
char __thiscall VirtualArea::CBGServerMap::AllRespawn(VirtualArea::CBGServerMap *this);
char __thiscall VirtualArea::CBGServerMap::SendMapInfo(VirtualArea::CBGServerMap *this);
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,CNPC *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CNPC *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,0>>::equal_range(std::_Tree<std::_Tmap_traits<unsigned long,CTempCharacter *,std::less<unsigned long>,boost::fast_pool_allocator<std::pair<unsigned long,CTempCharacter *>,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>,1> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator> *result, const unsigned int *_Keyval);
void __thiscall VirtualArea::CBGServerMap::UpdateKillInfo(VirtualArea::CBGServerMap *this, unsigned int dwDeadCID, unsigned int dwKillerCID); // idb
void __thiscall VirtualArea::CBGServerMap::CalculateScore(VirtualArea::CBGServerMap *this); // idb
char __thiscall VirtualArea::CBGServerMap::SendResultInfo(VirtualArea::CBGServerMap *this);
void __thiscall VirtualArea::CBGServerMap::ResetEnteringMin(VirtualArea::CBGServerMap *this, unsigned __int8 cMin); // idb
void __cdecl std::_Distance<std::_Tree<std::_Tmap_traits<unsigned long,CCell *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,CCell *>>,0>>::iterator,unsigned int>(std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,Guild::CGuild *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,Guild::CGuild *> >,0> >::iterator _Last, unsigned int *_Off); // idb
char __thiscall VirtualArea::CBGServerMap::InitializeGameObject(VirtualArea::CBGServerMap *this);
void __thiscall VirtualArea::CBGServerMap::KillChar(VirtualArea::CBGServerMap *this, unsigned int dwDeadCID, CCharacter *lpOffencer); // idb
bool __thiscall VirtualArea::CBGServerMap::GameStart(VirtualArea::CBGServerMap *this); // idb
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::_Insert(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result, bool _Addleft, std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Wherenode, const std::pair<unsigned long const ,unsigned long> *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _Where); // idb
void __thiscall std::list<CCharacter *>::_Incsize(std::list<CCharacter *> *this, unsigned int _Count); // idb
void __thiscall VirtualArea::CBGServerMap::Process(VirtualArea::CBGServerMap *this); // idb
std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::insert(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, std::pair<std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator,bool> *result, std::_Tree_nod<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::_Node *_Val);
std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator *result, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _First, std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> >::iterator _Last); // idb
const unsigned int *__thiscall std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,VirtualArea::MapInfo::PersonalInfo>>,0>>::erase(std::_Tree<std::_Tmap_traits<unsigned long,VirtualArea::MapInfo::PersonalInfo,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,VirtualArea::MapInfo::PersonalInfo> >,0> > *this, const unsigned int *_Keyval);
void __thiscall std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo>::~map<unsigned long,VirtualArea::MapInfo::PersonalInfo>(std::map<unsigned long,VirtualArea::MapInfo::PersonalInfo> *this); // idb
char __thiscall VirtualArea::CBGServerMap::Leave(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter);
unsigned __int16 __thiscall VirtualArea::CBGServerMap::AddCharacter(VirtualArea::CBGServerMap *this, CCharacter *lpCharacter); // idb
unsigned __int16 __thiscall VirtualArea::CBGServerMap::AddSpectator(VirtualArea::CBGServerMap *this, CCharacter *lpSpectator); // idb
void __thiscall VirtualArea::MapInfo::~MapInfo(VirtualArea::MapInfo *this); // idb
void __thiscall VirtualArea::CBGServerMap::~CBGServerMap(VirtualArea::CBGServerMap *this); // idb
void __thiscall VirtualArea::CBGServerMap::CBGServerMap(VirtualArea::CBGServerMap *this, const VirtualArea::ProtoType *lpProtoType, unsigned __int16 wMapNumber); // idb
VirtualArea::CBGServerMap *__thiscall VirtualArea::CBGServerMap::`scalar deleting destructor'(VirtualArea::CBGServerMap *this, char a2);
const CMonsterMgr::MonsterProtoType *__thiscall CMonsterMgr::GetMonsterProtoType(CMonsterMgr *this, unsigned int dwKID); // idb
CMonsterMgr::MonsterProtoType *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::size(std::vector<CMonsterMgr::MonsterProtoType> *this);
void __cdecl std::fill<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType>(CMonsterMgr::MonsterProtoType *_First, CMonsterMgr::MonsterProtoType *_Last, const CMonsterMgr::MonsterProtoType *_Val); // idb
void __thiscall CMonsterMgr::CMonsterMgr(CMonsterMgr *this); // idb
void __thiscall CMonsterMgr::~CMonsterMgr(CMonsterMgr *this); // idb
CMonsterMgr::MonsterProtoType *__cdecl std::copy_backward<CMonsterMgr::MonsterProtoType *,CMonsterMgr::MonsterProtoType *>(CMonsterMgr::MonsterProtoType *_First, CMonsterMgr::MonsterProtoType *_Last, CMonsterMgr::MonsterProtoType *_Dest); // idb
void __cdecl std::iter_swap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _Left, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Right); // idb
void __cdecl std::_Med3<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last); // idb
void __cdecl std::_Push_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, int _Hole, int _Top, CMonsterMgr::MonsterProtoType _Val); // idb
void __cdecl std::_Rotate<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last);
CMonsterMgr::MonsterProtoType *__cdecl std::_Uninit_copy<std::vector<CMonsterMgr::MonsterProtoType>::iterator,CMonsterMgr::MonsterProtoType *,std::allocator<CMonsterMgr::MonsterProtoType>>(CMonsterMgr::MonsterProtoType *_First, CMonsterMgr::MonsterProtoType *_Last, CMonsterMgr::MonsterProtoType *_Dest);
void __cdecl std::_Median<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Mid, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last); // idb
void __cdecl std::_Adjust_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, int _Hole, int _Bottom, CMonsterMgr::MonsterProtoType _Val); // idb
void __cdecl std::_Uninit_fill_n<CMonsterMgr::MonsterProtoType *,unsigned int,CMonsterMgr::MonsterProtoType,std::allocator<CMonsterMgr::MonsterProtoType>>(CMonsterMgr::MonsterProtoType *_First, unsigned int _Count, const CMonsterMgr::MonsterProtoType *_Val);
std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> *__cdecl std::_Unguarded_partition<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::pair<std::vector<CMonsterMgr::MonsterProtoType>::iterator,std::vector<CMonsterMgr::MonsterProtoType>::iterator> *result, std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last); // idb
void __cdecl std::_Make_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int,CMonsterMgr::MonsterProtoType>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last);
void __cdecl std::_Insertion_sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last); // idb
CMonsterMgr::MonsterProtoType *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::_Ufill(std::vector<CMonsterMgr::MonsterProtoType> *this, CMonsterMgr::MonsterProtoType *_Ptr, unsigned int _Count, const CMonsterMgr::MonsterProtoType *_Val); // idb
void __cdecl std::sort_heap<std::vector<CMonsterMgr::MonsterProtoType>::iterator>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last); // idb
void __thiscall __noreturn std::vector<CMonsterMgr::MonsterProtoType>::_Xlen(std::vector<CMonsterMgr::MonsterProtoType> *this);
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::_Insert_n(std::vector<CMonsterMgr::MonsterProtoType> *this, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Where, unsigned int _Count, const CMonsterMgr::MonsterProtoType *_Val); // idb
void __cdecl std::_Sort<std::vector<CMonsterMgr::MonsterProtoType>::iterator,int>(std::vector<CMonsterMgr::MonsterProtoType>::iterator _First, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Last, int _Ideal); // idb
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::reserve(std::vector<CMonsterMgr::MonsterProtoType> *this, CMonsterMgr::MonsterProtoType *_Count);
std::vector<CMonsterMgr::MonsterProtoType>::iterator *__thiscall std::vector<CMonsterMgr::MonsterProtoType>::insert(std::vector<CMonsterMgr::MonsterProtoType> *this, std::vector<CMonsterMgr::MonsterProtoType>::iterator *result, std::vector<CMonsterMgr::MonsterProtoType>::iterator _Where, const CMonsterMgr::MonsterProtoType *_Val); // idb
void __thiscall std::vector<CMonsterMgr::MonsterProtoType>::push_back(std::vector<CMonsterMgr::MonsterProtoType> *this, const CMonsterMgr::MonsterProtoType *_Val); // idb
char __thiscall CMonsterMgr::LoadMonstersFromFile(CMonsterMgr *this, const char *szFileName);
POS *__cdecl CharCreate::GetDefaultCharacterPos(POS *result, unsigned int dwRace, unsigned int dwNationPlayerNum); // idb
char __cdecl GameClientSendPacket::SendCharClassUpgrade(CSendStream *SendStream, unsigned int dwCharID, CharacterDBData *DBData, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharIncreasePoint(CSendStream *SendStream, unsigned int dwCharID, ChState State, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharLevelUp(CSendStream *SendStream, unsigned int dwCharID, CharacterDBData *DBData);
char __cdecl GameClientSendPacket::SendCharAward(CSendStream *SendStream, unsigned int dwCharID, unsigned int dwExp);
char __cdecl GameClientSendPacket::SendCharStateRedistribution(CSendStream *SendStream, unsigned int dwCharID, CharacterDBData *DBData, unsigned __int16 usError);
char __cdecl GameClientSendPacket::SendCharStatusRetrain(CSendStream *SendStream, unsigned int dwCharID, CharacterDBData *DBData, Item::ItemPos InvenPos, unsigned int dwGold, unsigned __int16 usError);
void __thiscall AwardTable::CAward::CAward(AwardTable::CAward *this); // idb
void __thiscall AwardTable::CAward::~CAward(AwardTable::CAward *this); // idb
unsigned int __thiscall AwardTable::CAward::GetAwardCoin(AwardTable::CAward *this, unsigned int nDeadMonsterLevel, unsigned int bPeaceMode);
unsigned int __thiscall AwardTable::CAward::GetAwardGem(AwardTable::CAward *this, int nDeadMonsterLevel); // idb
int __thiscall AwardTable::CAward::GetAwardMetal(AwardTable::CAward *this, int nDeadMonsterLevel);
int __thiscall AwardTable::CAward::GetAwardPotion(AwardTable::CAward *this, int nDeadMonsterLevel);
unsigned int __thiscall AwardTable::CAward::GetAwardLottery(AwardTable::CAward *this); // idb
void __cdecl std::_Random_shuffle<int *,int>(int *_First, int *_Last);
unsigned int __thiscall AwardTable::CAward::GetAwardSkill(AwardTable::CAward *this, __int16 nDeadMonsterLevel, char cAttackCharacterNation);
int __thiscall AwardTable::CAward::GetAwardEquipment(AwardTable::CAward *this, int nDeadMonsterLevel, char cAttackCharacterNation, unsigned __int8 cAttackCharacterClass);
unsigned __int16 __thiscall AwardTable::CAward::GetBlackMarketItem(AwardTable::CAward *this, unsigned __int16 usMainItemID); // idb
unsigned __int16 __thiscall CGameEventMgr::PopDropEventItem(CGameEventMgr *this); // idb
unsigned int __thiscall AwardTable::CAward::GetAward(AwardTable::CAward *this, int nItemKind, CMonster *pDeadMonster, CCharacter *pAttackCharacter); // idb
void __thiscall GAMELOG::sMinItemInfo::InitMinItemInfo(GAMELOG::sMinItemInfo *this, const Item::CItem *lpItem_In); // idb
void __thiscall GAMELOG::sSwapItemLog::InitSwapItemLog(GAMELOG::sSwapItemLog *this, TakeType srcTake, TakeType dstTake, const Item::CItem *lpSrcItem, const Item::CItem *lpDstItem); // idb
void __thiscall GAMELOG::sSplitItemLog::InitSplitItemLog(GAMELOG::sSplitItemLog *this, TakeType splitTake, const Item::CItem *lpPrevItem, const Item::CItem *lpSplitItem); // idb
void __thiscall GAMELOG::sRepairItemLog::InitRepairItemLog(GAMELOG::sRepairItemLog *this, unsigned int dwUsed, const Item::CItem *lpItem, unsigned __int8 cPreRepairDurability);
void __thiscall GAMELOG::sStallRegisterRemoveItemLog::InitStallRegisterRemoveItemLog(GAMELOG::sStallRegisterRemoveItemLog *this, GAMELOG::sStallRegisterRemoveItemLog::StallMode eStallMode, const Item::CItem *lpItem, TakeType takeType); // idb
void __cdecl GAMELOG::LogMoveItem(CCharacter *character, TakeType takeType, const GAMELOG::sMinItemInfo *minItemInfo, unsigned __int16 eError);
void __cdecl GAMELOG::LogSwapItem(CCharacter *character, TakeType srcTake, TakeType dstTake, Item::CItem *lpSrcItem, Item::CItem *lpDstItem, unsigned __int16 eError);
void __cdecl GAMELOG::LogUseItem(CCharacter *character, Item::ItemPos usePos, Item::CItem *lpUseItem, unsigned __int16 eError);
void __cdecl GAMELOG::LogSplitItem(CCharacter *character, TakeType splitTake, Item::CItem *lpPrevItem, Item::CItem *lpSplitItem, unsigned __int16 eError);
void __cdecl GAMELOG::LogPickupItem(CCharacter *character, Item::ItemPos pickupPos, Item::CItem *lpItem, unsigned int dwGold, unsigned __int16 eError);
void __cdecl GAMELOG::LogDropItem(CCharacter *character, Item::ItemPos dropFrom, Item::CItem *lpItem, unsigned int dwGold, unsigned __int16 eError);
void __cdecl GAMELOG::LogTradeItem(CCharacter *character, unsigned int dwTraderCID, unsigned int dwGold, Item::CItem *lpItem, Item::ItemPos itemPos, unsigned __int8 cCmd, unsigned __int16 eError);
void __cdecl GAMELOG::LogExchangeItem(const CCharacter *character, unsigned int dwDstCID, Item::CExchangeContainer *Exchange, unsigned __int8 cLogCMD);
void __cdecl GAMELOG::LogInstallSocket(CCharacter *character, TakeType GemAndEquip, Item::CItem *lpGemItem, Item::CItem *lpEquipItem, unsigned __int16 eError);
void __cdecl GAMELOG::LogUpgradeItem(CCharacter *character, unsigned int dwCurrentGold, unsigned int dwUsedGold, Item::CItem *lpResult, Item::CItem *lpMineral, unsigned __int16 eError); // idb
void __cdecl GAMELOG::LogRepairItem(const CCharacter *character, Item::CItem *lpRepairedItem, unsigned int dwRepairPrice, unsigned __int8 cPreRepairDurability, unsigned __int16 eError);
void __cdecl GAMELOG::LogChangeWeapon(const CCharacter *character, unsigned __int8 cCurrentHand);
void __cdecl GAMELOG::LogTakeGold(const CCharacter *character, unsigned int dwSrcGold, unsigned int dwDstGold, unsigned int dwMoveGold, unsigned __int8 cSrcPos, unsigned __int8 cDstPos, unsigned __int8 cPurpose, unsigned __int16 eError); // idb
void __cdecl GAMELOG::LogStallOpenClose(const CCharacter *character, const char *szStallName, bool bOpen);
void __cdecl GAMELOG::LogStallEnterLeave(const CCharacter *character, unsigned int dwCustomerCID, bool bEnter);
void __cdecl GAMELOG::LogStallRegisterRemoveItem(const CCharacter *character, const Item::CItem *lpItem, TakeType takeType, unsigned __int8 cPktStRICMD, unsigned __int16 usError);
char __thiscall CCharacter::AddGold(CCharacter *this, unsigned int dwGold, bool bNotice);
char __thiscall CCharacter::DeductGold(CCharacter *this, unsigned int dwGold, bool bNotice);
unsigned int __thiscall CCharacter::GetGold(CCharacter *this, unsigned __int8 cPos); // idb
char __thiscall CCharacter::MoveGold(CCharacter *this, unsigned int dwGold, unsigned __int8 cSrcPos, unsigned __int8 cDstPos, unsigned __int16 *usError);
char __thiscall CCharacter::Pickup(CCharacter *this, Item::CItem *lpItem, int dstPos);
char __thiscall CCharacter::UseStartKit(CCharacter *this, unsigned __int16 wObjectType);
char __thiscall CCharacter::MoveItem(CCharacter *this, __int64 takeType);
char __thiscall CCharacter::SwapItem(CCharacter *this, __int64 SrcTake, TakeType DstTake);
Item::CItem *__thiscall CCharacter::Drop(CCharacter *this, int SrcPos, unsigned __int8 cNum);
char __thiscall CCharacter::UseLottery(CCharacter *this, unsigned __int16 usItemID, int itemPos);
Item::CItem *__thiscall CCharacter::GetItem(CCharacter *this, int SrcPos);
char __thiscall CCharacter::RemoveItem(CCharacter *this, int SrcPos);
bool __thiscall CCharacter::SetItem(CCharacter *this, int SrcPos, Item::CItem *lpItem);
void __thiscall AddressInfo::AddressInfo(AddressInfo *this, const sockaddr_in *PublicAddress, const sockaddr_in *PrivateAddress, unsigned int dwCharID);
bool __thiscall CParty::Join(CParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, const char *strSenderName, unsigned __int16 wMapIndex); // idb
int __thiscall CParty::Leave(CParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, unsigned __int16 wMapIndex); // idb
int __thiscall CParty::Logout(CParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID); // idb
bool __thiscall CParty::AutoRouting(CParty *this, AtType attackType, CAggresiveCreature **pDefenders, unsigned __int8 *cDefenserJudges, CAggresiveCreature *lpOffencer, float fDistance, unsigned __int8 cTargetType); // idb
char __thiscall CParty::IsMember(CParty *this, unsigned int dwMemberCID);
char __thiscall CMonsterParty::Join(CMonsterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, const char *strSenderName, unsigned __int16 wMapIndex);
int __thiscall CMonsterParty::Leave(CMonsterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID, unsigned __int16 wMapIndex); // idb
char __thiscall CMonsterParty::Destory(CMonsterParty *this, unsigned int dwSenderCID, unsigned int dwReferenceID);
void __thiscall CCharacterParty::PrepareLogout(CCharacterParty *this, unsigned int dwMemberID); // idb
unsigned __int8 __thiscall CCharacterParty::GetLoggedMemberAverageLevel(CCharacterParty *this); // idb
