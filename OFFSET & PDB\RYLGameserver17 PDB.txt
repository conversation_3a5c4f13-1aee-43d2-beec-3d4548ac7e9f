//----- (004A9270) --------------------------------------------------------
struct CRelocTable *__thiscall CIntermediateCode::ToMachineCode(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **this,
        char *a2,
        struct CRelocTable *a3)
{
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Myhead; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Left; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *first; // ecx
  int v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v8; // ecx
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v10; // esi
  struct CRelocTable *v12; // ebx
  int v13; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v14; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v15; // edi
  unsigned int second; // ebx
  _DWORD *v17; // eax
  _DWORD *v18; // esi
  int v19; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *Right; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *j; // eax
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *i; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator v24; // [esp+10h] [ebp-18h] BYREF
  CIntermediateCode *v25; // [esp+14h] [ebp-14h]
  std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator v26; // [esp+18h] [ebp-10h] BYREF
  int v27; // [esp+1Ch] [ebp-Ch]
  int v28; // [esp+20h] [ebp-8h] BYREF
  struct CRelocTable *v29; // [esp+30h] [ebp+8h]

  v25 = (CIntermediateCode *)this;
  CIntermediateCode::Addressing((CIntermediateCode *)this, 0);
  Myhead = this[1]->_Myhead;
  Left = Myhead->_Left;
  v24._Ptr = Myhead->_Left;
  if ( v24._Ptr != Myhead )
  {
    do
    {
      first = (std::_Tree_nod<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::_Node *)Left->_Myval.first;
      v7 = *(_DWORD *)(Left->_Myval.second + 4);
      v26._Ptr = first;
      v8 = this[2];
      v27 = *(_DWORD *)(*(_DWORD *)(*(_DWORD *)v7 + 8) + 4);
      std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::insert(
        v8,
        (int)&v28,
        (std::_Tree<std::_Tmap_traits<unsigned int,CMsgProc *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,CMsgProc *> >,0> >::const_iterator)&v26);
      std::_Tree<std::_Tmap_traits<CSessionPolicy *,unsigned int,std::less<CSessionPolicy *>,std::allocator<std::pair<CSessionPolicy * const,unsigned int>>,0>>::const_iterator::_Inc(&v24);
      Left = v24._Ptr;
    }
    while ( v24._Ptr != this[1]->_Myhead );
  }
  SetFuncTable((int)this[2]);
  SetRelocTable(a3);
  v9 = (*this)->_Myhead;
  v10 = v9->_Left;
  v12 = 0;
  v29 = 0;
  if ( v9->_Left != v9 )
  {
    do
    {
      v13 = ((int (__thiscall *)(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *, char *))v10->_Right->_Left->_Parent)(
              v10->_Right,
              a2);
      v10 = v10->_Left;
      a2 += v13;
      v12 = (struct CRelocTable *)((char *)v12 + v13);
    }
    while ( v10 != (*this)->_Myhead );
    v29 = v12;
  }
  v14 = this[1]->_Myhead;
  v15 = v14->_Left;
  if ( v14->_Left == v14 )
    return v12;
  do
  {
    second = v15->_Myval.second;
    v17 = *(_DWORD **)(second + 4);
    v18 = (_DWORD *)*v17;
    if ( (_DWORD *)*v17 != v17 )
    {
      do
      {
        v19 = (*(int (__thiscall **)(_DWORD, char *))(*(_DWORD *)v18[2] + 4))(v18[2], a2);
        v18 = (_DWORD *)*v18;
        a2 += v19;
        v29 = (struct CRelocTable *)((char *)v29 + v19);
      }
      while ( v18 != *(_DWORD **)(second + 4) );
    }
    if ( !v15->_Isnil )
    {
      Right = v15->_Right;
      if ( Right->_Isnil )
      {
        for ( i = v15->_Parent; !i->_Isnil; i = i->_Parent )
        {
          if ( v15 != i->_Right )
            break;
          v15 = i;
        }
        v15 = i;
      }
      else
      {
        v15 = v15->_Right;
        for ( j = Right->_Left; !j->_Isnil; j = j->_Left )
          v15 = j;
      }
    }
  }
  while ( v15 != *(std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)(*((_DWORD *)v25 + 1) + 4) );
  return v29;
}

//----- (004A93D0) --------------------------------------------------------
int __thiscall std::map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>::~map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this)
{
  int result; // eax
  int v3; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>,0>>::erase(
    this,
    (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator)this->_Myhead->_Left,
    this->_Myhead);
  operator delete(this->_Myhead);
  result = 0;
  this->_Myhead = 0;
  this->_Mysize = 0;
  return result;
}

//----- (004A9400) --------------------------------------------------------
int __thiscall std::map<int,long>::~map<int,long>(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *this)
{
  int result; // eax
  int v3; // [esp+4h] [ebp-4h] BYREF

  std::_Tree<std::_Tmap_traits<int,long,std::less<int>,std::allocator<std::pair<int const,long>>,0>>::erase(
    this,
    (std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node **)&v3,
    (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::const_iterator)this->_Myhead->_Left,
    this->_Myhead);
  operator delete(this->_Myhead);
  result = 0;
  this->_Myhead = 0;
  this->_Mysize = 0;
  return result;
}

//----- (004A9430) --------------------------------------------------------
CIntermediateCode *__thiscall CIntermediateCode::CIntermediateCode(CIntermediateCode *this)
{
  std::list<CThread *> *v2; // eax
  std::list<CThread *> *v3; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v4; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v6; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v7; // eax
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v8; // esi
  std::_Tree_nod<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> >::_Node *v9; // eax

  v2 = (std::list<CThread *> *)operator new((tagHeader *)0xC);
  v3 = v2;
  if ( v2 )
  {
    v2->_Myhead = std::list<CModifyDummyCharacter *>::_Buynode(v2);
    v3->_Mysize = 0;
  }
  else
  {
    v3 = 0;
  }
  *(_DWORD *)this = v3;
  v4 = (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)operator new((tagHeader *)0xC);
  v5 = v4;
  if ( v4 )
  {
    v6 = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode(v4);
    v5->_Myhead = v6;
    v6->_Isnil = 1;
    v5->_Myhead->_Parent = v5->_Myhead;
    v5->_Myhead->_Left = v5->_Myhead;
    v5->_Myhead->_Right = v5->_Myhead;
    v5->_Mysize = 0;
  }
  else
  {
    v5 = 0;
  }
  *((_DWORD *)this + 1) = v5;
  v7 = (std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *)operator new((tagHeader *)0xC);
  v8 = v7;
  if ( v7 )
  {
    v9 = std::_Tree<std::_Tmap_traits<unsigned long,Castle::CCastle *,std::less<unsigned long>,std::allocator<std::pair<unsigned long const,Castle::CCastle *>>,0>>::_Buynode(v7);
    v8->_Myhead = v9;
    v9->_Isnil = 1;
    v8->_Myhead->_Parent = v8->_Myhead;
    v8->_Myhead->_Left = v8->_Myhead;
    v8->_Myhead->_Right = v8->_Myhead;
    v8->_Mysize = 0;
    *((_DWORD *)this + 2) = v8;
  }
  else
  {
    *((_DWORD *)this + 2) = 0;
  }
  return this;
}

//----- (004A9540) --------------------------------------------------------
void __thiscall CIntermediateCode::~CIntermediateCode(
        std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > **this)
{
  void **v2; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v3; // esi
  std::_Tree<std::_Tmap_traits<unsigned long,unsigned long,std::less<unsigned long>,std::allocator<std::pair<unsigned long const ,unsigned long> >,1> > *v4; // edi

  CIntermediateCode::Destroy((CIntermediateCode *)this);
  v2 = (void **)*this;
  if ( *this )
  {
    std::list<IOPCode *>::clear((std::list<CThread *> *)*this);
    operator delete(v2[1]);
    v2[1] = 0;
    operator delete(v2);
  }
  v3 = this[1];
  if ( v3 )
  {
    std::map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>::~map<int,std::list<IOPCode *> *,std::less<int>,std::allocator<std::pair<int const,std::list<IOPCode *> *>>>(this[1]);
    operator delete(v3);
  }
  v4 = this[2];
  if ( v4 )
  {
    std::map<int,long>::~map<int,long>(v4);
    operator delete(v4);
  }
}

//----- (004A95A0) --------------------------------------------------------
void __usercall putShortMSB(internal_state *s@<eax>, __int16 b@<cx>)
{
  unsigned __int8 *pending_buf; // esi
  int v3; // edx

  s->pending_buf[s->pending] = HIBYTE(b);
  pending_buf = s->pending_buf;
  v3 = s->pending + 1;
  s->pending = v3;
  pending_buf[v3] = b;
  ++s->pending;
}

//----- (004A95D0) --------------------------------------------------------
void __usercall flush_pending(z_stream_s *strm@<eax>)
{
  internal_state *state; // esi
  unsigned int pending; // edx
  internal_state *v3; // ecx
  unsigned int avail_out; // edi
  internal_state *v5; // ecx
  internal_state *v6; // eax

  state = strm->state;
  pending = state->pending;
  if ( pending > strm->avail_out )
    pending = strm->avail_out;
  if ( pending )
  {
    qmemcpy(strm->next_out, state->pending_out, pending);
    v3 = strm->state;
    strm->next_out += pending;
    v3->pending_out += pending;
    avail_out = strm->avail_out;
    v5 = strm->state;
    strm->total_out += pending;
    strm->avail_out = avail_out - pending;
    v5->pending -= pending;
    v6 = strm->state;
    if ( !v6->pending )
      v6->pending_out = v6->pending_buf;
  }
}

//----- (004A9640) --------------------------------------------------------
int __cdecl deflate(z_stream_s *strm, unsigned int flush)
{
  internal_state *state; // esi
  int status; // eax
  z_stream_s *last_flush; // ecx
  unsigned int v7; // eax
  unsigned int v8; // ecx
  internal_state *v9; // eax
  int v10; // eax
  unsigned int avail_in; // ecx
  block_state v12; // eax
  internal_state *v13; // eax
  int pending; // ecx
  z_stream_s *strma; // [esp+10h] [ebp+4h]

  if ( !strm )
    return -2;
  state = strm->state;
  if ( !state || flush > 4 )
    return -2;
  if ( !strm->next_out || !strm->next_in && strm->avail_in || (status = state->status, status == 666) && flush != 4 )
  {
    strm->msg = (char *)z_errmsg[4];
    return -2;
  }
  if ( !strm->avail_out )
  {
    strm->msg = (char *)z_errmsg[7];
    return -5;
  }
  last_flush = (z_stream_s *)state->last_flush;
  state->strm = strm;
  strma = last_flush;
  state->last_flush = flush;
  if ( status == 42 )
  {
    v7 = (state->level - 1) >> 1;
    if ( v7 > 3 )
      v7 = 3;
    v8 = (v7 << 6) | (((state->w_bits - 8) << 12) + 2048);
    if ( state->strstart )
      v8 |= 0x20u;
    state->status = 113;
    putShortMSB(state, v8 - v8 % 0x1F + 31);
    if ( state->strstart )
    {
      putShortMSB(state, HIWORD(strm->adler));
      putShortMSB(v9, strm->adler);
    }
    strm->adler = 1;
  }
  if ( state->pending )
  {
    flush_pending(strm);
    if ( !strm->avail_out )
    {
LABEL_21:
      state->last_flush = -1;
      return 0;
    }
  }
  else if ( !strm->avail_in && (int)flush <= (int)strma && flush != 4 )
  {
    strm->msg = (char *)z_errmsg[7];
    return -5;
  }
  v10 = state->status;
  avail_in = strm->avail_in;
  if ( v10 == 666 )
  {
    if ( avail_in )
    {
      strm->msg = (char *)z_errmsg[7];
      return -5;
    }
LABEL_31:
    if ( !state->lookahead && (!flush || v10 == 666) )
      goto LABEL_45;
    goto LABEL_34;
  }
  if ( !avail_in )
    goto LABEL_31;
LABEL_34:
  v12 = configuration_table[state->level].func(state, flush);
  if ( v12 == finish_started || v12 == finish_done )
    state->status = 666;
  if ( v12 == need_more || v12 == finish_started )
  {
    if ( strm->avail_out )
      return 0;
    state->last_flush = -1;
    return 0;
  }
  if ( v12 == block_done )
  {
    if ( flush == 1 )
    {
      _tr_align(state);
    }
    else
    {
      _tr_stored_block(state, 0, 0, 0);
      if ( flush == 3 )
      {
        state->head[state->hash_size - 1] = 0;
        memset(state->head, 0, 2 * state->hash_size - 2);
      }
    }
    flush_pending(strm);
    if ( !strm->avail_out )
      goto LABEL_21;
  }
LABEL_45:
  if ( flush != 4 )
    return 0;
  if ( state->noheader )
    return 1;
  putShortMSB(state, HIWORD(strm->adler));
  putShortMSB(v13, strm->adler);
  flush_pending(strm);
  pending = state->pending;
  state->noheader = -1;
  return pending == 0;
}
// 4A972D: variable 'v9' is possibly undefined
// 4A9884: variable 'v13' is possibly undefined

//----- (004A98D0) --------------------------------------------------------
unsigned int __cdecl deflateEnd(z_stream_s *strm)
{
  internal_state *state; // eax
  int status; // edi
  unsigned __int8 *pending_buf; // eax
  internal_state *v5; // ecx
  internal_state *v6; // edx

  if ( !strm )
    return -2;
  state = strm->state;
  if ( !state )
    return -2;
  status = state->status;
  if ( status != 42 && status != 113 && status != 666 )
    return -2;
  pending_buf = state->pending_buf;
  if ( pending_buf )
    strm->zfree(strm->opaque, pending_buf);
  v5 = strm->state;
  if ( v5->head )
    strm->zfree(strm->opaque, v5->head);
  if ( strm->state->prev )
    strm->zfree(strm->opaque, strm->state->prev);
  v6 = strm->state;
  if ( v6->window )
    strm->zfree(strm->opaque, v6->window);
  strm->zfree(strm->opaque, strm->state);
  strm->state = 0;
  return status != 113 ? 0 : 0xFFFFFFFD;
}

//----- (004A9990) --------------------------------------------------------
void __fastcall lm_init(int a1, internal_state *s)
{
  unsigned int hash_size; // ecx
  int level; // eax

  hash_size = s->hash_size;
  s->window_size = 2 * s->w_size;
  s->head[hash_size - 1] = 0;
  memset(s->head, 0, 2 * s->hash_size - 2);
  level = s->level;
  s->max_lazy_match = configuration_table[level].max_lazy;
  s->good_match = configuration_table[level].good_length;
  s->nice_match = configuration_table[level].nice_length;
  s->max_chain_length = configuration_table[level].max_chain;
  s->strstart = 0;
  s->block_start = 0;
  s->lookahead = 0;
  s->match_available = 0;
  s->ins_h = 0;
  s->prev_length = 2;
  s->match_length = 2;
}

//----- (004A9A20) --------------------------------------------------------
unsigned int __usercall longest_match@<eax>(internal_state *s@<esi>, unsigned int cur_match@<eax>)
{
  unsigned int strstart; // edx
  unsigned int prev_length; // ebp
  unsigned int w_size; // edi
  unsigned __int8 *v5; // ecx
  unsigned __int8 *v6; // edi
  unsigned __int8 *v7; // edx
  unsigned __int8 v8; // bl
  unsigned __int8 *v9; // edx
  unsigned __int8 *v10; // ecx
  _BYTE *v11; // edx
  char v12; // bl
  _BYTE *v13; // edx
  char v14; // bl
  _BYTE *v15; // edx
  char v16; // bl
  _BYTE *v17; // edx
  char v18; // bl
  _BYTE *v19; // edx
  char v20; // bl
  _BYTE *v21; // edx
  char v22; // bl
  _BYTE *v23; // edx
  char v24; // bl
  _BYTE *v25; // edx
  char v26; // bl
  int v27; // edx
  unsigned int result; // eax
  unsigned __int8 scan_end1; // [esp+Eh] [ebp-12h]
  unsigned __int8 scan_end; // [esp+Fh] [ebp-11h]
  unsigned int chain_length; // [esp+10h] [ebp-10h]
  int nice_match; // [esp+14h] [ebp-Ch]
  unsigned int limit; // [esp+18h] [ebp-8h]
  unsigned int lookahead; // [esp+1Ch] [ebp-4h]

  strstart = s->strstart;
  prev_length = s->prev_length;
  chain_length = s->max_chain_length;
  nice_match = s->nice_match;
  w_size = s->w_size;
  v5 = &s->window[strstart];
  if ( strstart <= w_size - 262 )
    limit = 0;
  else
    limit = strstart - w_size + 262;
  scan_end1 = v5[prev_length - 1];
  scan_end = v5[prev_length];
  v6 = v5 + 258;
  if ( prev_length >= s->good_match )
    chain_length >>= 2;
  lookahead = s->lookahead;
  if ( nice_match > lookahead )
    nice_match = s->lookahead;
  do
  {
    v7 = &s->window[cur_match];
    if ( v7[prev_length] == scan_end && v7[prev_length - 1] == scan_end1 && *v7 == *v5 )
    {
      v8 = v7[1];
      v9 = v7 + 1;
      if ( v8 == v5[1] )
      {
        v10 = v5 + 2;
        v11 = v9 + 1;
        do
        {
          v12 = *++v10;
          v13 = v11 + 1;
          if ( v12 != *v13 )
            break;
          v14 = *++v10;
          v15 = v13 + 1;
          if ( v14 != *v15 )
            break;
          v16 = *++v10;
          v17 = v15 + 1;
          if ( v16 != *v17 )
            break;
          v18 = *++v10;
          v19 = v17 + 1;
          if ( v18 != *v19 )
            break;
          v20 = *++v10;
          v21 = v19 + 1;
          if ( v20 != *v21 )
            break;
          v22 = *++v10;
          v23 = v21 + 1;
          if ( v22 != *v23 )
            break;
          v24 = *++v10;
          v25 = v23 + 1;
          if ( v24 != *v25 )
            break;
          v26 = *++v10;
          v11 = v25 + 1;
          if ( v26 != *v11 )
            break;
        }
        while ( v10 < v6 );
        v27 = v10 - v6 + 258;
        v5 = v6 - 258;
        if ( v27 > (int)prev_length )
        {
          s->match_start = cur_match;
          prev_length = v27;
          if ( v27 >= nice_match )
            break;
          scan_end1 = v5[v27 - 1];
          scan_end = v5[v27];
        }
      }
    }
    cur_match = s->prev[cur_match & s->w_mask];
    if ( cur_match <= limit )
      break;
    --chain_length;
  }
  while ( chain_length );
  result = lookahead;
  if ( prev_length <= lookahead )
    return prev_length;
  return result;
}

//----- (004A9B90) --------------------------------------------------------
void __usercall fill_window(internal_state *s@<ebx>)
{
  unsigned int w_size; // eax
  unsigned int strstart; // ecx
  unsigned int lookahead; // esi
  unsigned int v4; // edx
  unsigned int v5; // esi
  int v6; // ecx
  unsigned int hash_size; // edi
  unsigned __int16 *head; // ecx
  unsigned __int16 *v9; // esi
  unsigned int v10; // ecx
  unsigned __int16 v11; // cx
  unsigned int v12; // edi
  unsigned __int16 *v13; // esi
  unsigned int v14; // ecx
  unsigned __int16 v15; // cx
  z_stream_s *strm; // ebp
  unsigned __int8 *v17; // edi
  unsigned int avail_in; // esi
  unsigned int v19; // ecx
  internal_state *state; // edx
  unsigned int v21; // edx
  unsigned int v22; // edi
  unsigned __int8 *v23; // esi
  unsigned int v24; // edx
  unsigned int hash_shift; // ecx
  unsigned int v26; // [esp+Ch] [ebp-8h]
  unsigned int wsize; // [esp+10h] [ebp-4h]

  w_size = s->w_size;
  wsize = w_size;
  do
  {
    strstart = s->strstart;
    lookahead = s->lookahead;
    v4 = s->window_size - strstart - lookahead;
    if ( v4 )
    {
      if ( v4 == -1 )
      {
        v4 = -2;
        goto LABEL_20;
      }
    }
    else if ( !strstart && !lookahead )
    {
      v4 = w_size;
      goto LABEL_20;
    }
    if ( strstart >= w_size + s->w_size - 262 )
    {
      qmemcpy(s->window, &s->window[w_size], w_size);
      v5 = s->strstart - w_size;
      v6 = s->block_start - w_size;
      s->match_start -= w_size;
      hash_size = s->hash_size;
      s->block_start = v6;
      head = s->head;
      s->strstart = v5;
      v9 = &head[hash_size];
      do
      {
        v10 = *--v9;
        if ( v10 < w_size )
          v11 = 0;
        else
          v11 = v10 - w_size;
        --hash_size;
        *v9 = v11;
      }
      while ( hash_size );
      v12 = w_size;
      v13 = &s->prev[w_size];
      do
      {
        v14 = *--v13;
        if ( v14 < w_size )
          v15 = 0;
        else
          v15 = v14 - w_size;
        --v12;
        *v13 = v15;
      }
      while ( v12 );
      v4 += w_size;
    }
LABEL_20:
    strm = s->strm;
    if ( !s->strm->avail_in )
      break;
    v17 = &s->window[s->strstart + s->lookahead];
    avail_in = strm->avail_in;
    v26 = avail_in;
    if ( avail_in > v4 )
    {
      v26 = v4;
      avail_in = v4;
    }
    if ( avail_in )
    {
      state = strm->state;
      strm->avail_in -= avail_in;
      if ( !state->noheader )
      {
        strm->adler = adler32(strm->adler, strm->next_in, avail_in);
        w_size = wsize;
      }
      qmemcpy(v17, strm->next_in, avail_in);
      v19 = v26;
      v21 = v26 + strm->total_in;
      strm->next_in += v26;
      strm->total_in = v21;
    }
    else
    {
      v19 = 0;
    }
    v22 = v19 + s->lookahead;
    s->lookahead = v22;
    if ( v22 >= 3 )
    {
      v23 = &s->window[s->strstart];
      v24 = *v23;
      hash_shift = s->hash_shift;
      s->ins_h = v24;
      s->ins_h = s->hash_mask & (v23[1] ^ (v24 << hash_shift));
    }
  }
  while ( v22 < 0x106 && s->strm->avail_in );
}

//----- (004A9D20) --------------------------------------------------------
int __cdecl deflate_stored(internal_state *s, int flush)
{
  unsigned int lookahead; // eax
  unsigned int strstart; // ecx
  bool v4; // zf
  int block_start; // ecx
  unsigned int v6; // edx
  unsigned int v7; // eax
  char *v8; // edx
  z_stream_s *strm; // eax
  internal_state *state; // esi
  unsigned int pending; // edx
  internal_state *v12; // ecx
  unsigned int avail_out; // edi
  internal_state *v14; // ecx
  internal_state *v15; // eax
  int v16; // edx
  unsigned int v17; // ecx
  char *v18; // eax
  z_stream_s *v19; // eax
  internal_state *v20; // esi
  unsigned int v21; // edx
  internal_state *v22; // ecx
  unsigned int v23; // edi
  internal_state *v24; // ecx
  internal_state *v25; // eax
  int v27; // esi
  char *v28; // eax
  unsigned int max_block_size; // [esp+10h] [ebp-4h]

  max_block_size = 0xFFFF;
  if ( s->pending_buf_size - 5 < 0xFFFF )
    max_block_size = s->pending_buf_size - 5;
  while ( 1 )
  {
    lookahead = s->lookahead;
    if ( lookahead <= 1 )
    {
      fill_window(s);
      lookahead = s->lookahead;
      if ( !lookahead )
        break;
    }
    strstart = s->strstart;
    v4 = lookahead + strstart == 0;
    s->strstart = lookahead + strstart;
    block_start = s->block_start;
    v6 = s->strstart;
    s->lookahead = 0;
    v7 = block_start + max_block_size;
    if ( !v4 && v6 < v7 )
      goto LABEL_36;
    s->lookahead = v6 - v7;
    s->strstart = v7;
    if ( block_start < 0 )
      v8 = 0;
    else
      v8 = (char *)&s->window[block_start];
    _tr_flush_block(s, v8, max_block_size, 0);
    strm = s->strm;
    s->block_start = s->strstart;
    state = strm->state;
    pending = state->pending;
    if ( pending > strm->avail_out )
      pending = strm->avail_out;
    if ( pending )
    {
      qmemcpy(strm->next_out, state->pending_out, pending);
      v12 = strm->state;
      strm->next_out += pending;
      v12->pending_out += pending;
      avail_out = strm->avail_out;
      v14 = strm->state;
      strm->total_out += pending;
      strm->avail_out = avail_out - pending;
      v14->pending -= pending;
      v15 = strm->state;
      if ( !v15->pending )
        v15->pending_out = v15->pending_buf;
    }
    if ( s->strm->avail_out )
    {
LABEL_36:
      v16 = s->block_start;
      v17 = s->strstart - v16;
      if ( v17 < s->w_size - 262 )
        continue;
      if ( v16 < 0 )
        v18 = 0;
      else
        v18 = (char *)&s->window[v16];
      _tr_flush_block(s, v18, v17, 0);
      v19 = s->strm;
      s->block_start = s->strstart;
      v20 = v19->state;
      v21 = v20->pending;
      if ( v21 > v19->avail_out )
        v21 = v19->avail_out;
      if ( v21 )
      {
        qmemcpy(v19->next_out, v20->pending_out, v21);
        v22 = v19->state;
        v19->next_out += v21;
        v22->pending_out += v21;
        v23 = v19->avail_out;
        v24 = v19->state;
        v19->total_out += v21;
        v19->avail_out = v23 - v21;
        v24->pending -= v21;
        v25 = v19->state;
        if ( !v25->pending )
          v25->pending_out = v25->pending_buf;
      }
      if ( s->strm->avail_out )
        continue;
    }
    return 0;
  }
  if ( !flush )
    return 0;
  v27 = s->block_start;
  if ( v27 < 0 )
    v28 = 0;
  else
    v28 = (char *)&s->window[v27];
  _tr_flush_block(s, v28, s->strstart - v27, flush == 4);
  s->block_start = s->strstart;
  flush_pending(s->strm);
  if ( s->strm->avail_out )
    return 2 * (flush == 4) + 1;
  else
    return flush != 4 ? 0 : 2;
}

//----- (004A9F30) --------------------------------------------------------
int __cdecl deflate_fast(internal_state *s, int flush)
{
  unsigned int v2; // edi
  unsigned int lookahead; // eax
  unsigned int strstart; // edx
  unsigned __int16 *head; // ecx
  unsigned int v6; // eax
  unsigned __int16 v7; // ax
  unsigned __int8 v8; // cl
  unsigned __int16 v9; // ax
  int v10; // eax
  unsigned int max_lazy_match; // edx
  BOOL v12; // esi
  unsigned int match_length; // eax
  unsigned int v14; // ecx
  unsigned __int8 *window; // ecx
  unsigned int ins_h; // edi
  unsigned int v17; // edx
  int v18; // eax
  unsigned __int16 *v19; // ecx
  unsigned int v20; // eax
  unsigned int v21; // eax
  unsigned int v22; // ecx
  unsigned __int8 *v23; // edx
  unsigned int hash_shift; // ecx
  unsigned int v25; // eax
  unsigned __int8 v26; // al
  BOOL v27; // edx
  int block_start; // ecx
  char *v29; // eax
  z_stream_s *strm; // eax
  internal_state *state; // esi
  unsigned int pending; // edx
  internal_state *v33; // ecx
  unsigned int total_out; // ebp
  internal_state *v35; // ecx
  internal_state *v36; // eax
  int v38; // esi
  char *v39; // eax
  unsigned int hash_head; // [esp+10h] [ebp-4h]

  v2 = 0;
  hash_head = 0;
  while ( 1 )
  {
    lookahead = s->lookahead;
    if ( lookahead < 0x106 )
    {
      fill_window(s);
      lookahead = s->lookahead;
      if ( lookahead < 0x106 && !flush )
        return 0;
      if ( !lookahead )
        break;
    }
    if ( lookahead >= 3 )
    {
      strstart = s->strstart;
      head = s->head;
      v6 = s->hash_mask & (s->window[strstart + 2] ^ (s->ins_h << s->hash_shift));
      s->ins_h = v6;
      v2 = head[v6];
      s->prev[strstart & s->w_mask] = v2;
      hash_head = v2;
      s->head[s->ins_h] = s->strstart;
    }
    if ( v2 && s->strstart - v2 <= s->w_size - 262 && s->strategy != 2 )
      s->match_length = longest_match(s, v2);
    if ( s->match_length < 3 )
    {
      v26 = s->window[s->strstart];
      s->d_buf[s->last_lit] = 0;
      s->l_buf[s->last_lit++] = v26;
      ++s->dyn_ltree[v26].fc.freq;
      v27 = s->last_lit == s->lit_bufsize - 1;
      --s->lookahead;
      v12 = v27;
    }
    else
    {
      v7 = LOWORD(s->strstart) - LOWORD(s->match_start);
      v8 = s->match_length - 3;
      s->d_buf[s->last_lit] = v7;
      s->l_buf[s->last_lit++] = v8;
      v9 = v7 - 1;
      ++s->dyn_ltree[_length_code[v8] + 257].fc.freq;
      if ( v9 >= 0x100u )
        v10 = (unsigned __int8)byte_4FC340[v9 >> 7];
      else
        v10 = _dist_code[v9];
      ++s->dyn_dtree[v10].fc.freq;
      max_lazy_match = s->max_lazy_match;
      v12 = s->last_lit == s->lit_bufsize - 1;
      match_length = s->match_length;
      v14 = s->lookahead - match_length;
      s->lookahead = v14;
      if ( match_length > max_lazy_match || v14 < 3 )
      {
        v22 = match_length + s->strstart;
        v23 = &s->window[v22];
        s->strstart = v22;
        hash_shift = s->hash_shift;
        s->match_length = 0;
        v25 = *v23;
        s->ins_h = v25;
        s->ins_h = s->hash_mask & (v23[1] ^ (v25 << hash_shift));
        goto LABEL_24;
      }
      s->match_length = match_length - 1;
      do
      {
        window = s->window;
        ins_h = s->ins_h;
        v17 = s->strstart + 1;
        s->strstart = v17;
        v18 = window[v17 + 2];
        v19 = s->head;
        v20 = s->hash_mask & ((ins_h << s->hash_shift) ^ v18);
        s->ins_h = v20;
        v2 = v19[v20];
        s->prev[s->w_mask & v17] = v2;
        s->head[s->ins_h] = s->strstart;
        v21 = s->match_length - 1;
        hash_head = v2;
        s->match_length = v21;
      }
      while ( v21 );
    }
    ++s->strstart;
LABEL_24:
    if ( v12 )
    {
      block_start = s->block_start;
      if ( block_start < 0 )
        v29 = 0;
      else
        v29 = (char *)&s->window[block_start];
      _tr_flush_block(s, v29, s->strstart - block_start, 0);
      s->block_start = s->strstart;
      strm = s->strm;
      state = s->strm->state;
      pending = state->pending;
      if ( pending > s->strm->avail_out )
        pending = s->strm->avail_out;
      if ( pending )
      {
        qmemcpy(strm->next_out, state->pending_out, pending);
        v33 = strm->state;
        strm->next_out += pending;
        v33->pending_out += pending;
        total_out = strm->total_out;
        v35 = strm->state;
        strm->avail_out -= pending;
        v2 = hash_head;
        strm->total_out = pending + total_out;
        v35->pending -= pending;
        v36 = strm->state;
        if ( !v36->pending )
          v36->pending_out = v36->pending_buf;
      }
      if ( !s->strm->avail_out )
        return 0;
    }
  }
  v38 = s->block_start;
  if ( v38 < 0 )
    v39 = 0;
  else
    v39 = (char *)&s->window[v38];
  _tr_flush_block(s, v39, s->strstart - v38, flush == 4);
  s->block_start = s->strstart;
  flush_pending(s->strm);
  if ( s->strm->avail_out )
    return 2 * (flush == 4) + 1;
  else
    return flush != 4 ? 0 : 2;
}

//----- (004AA2A0) --------------------------------------------------------
int __cdecl deflate_slow(internal_state *s, int flush)
{
  unsigned int v2; // ebp
  unsigned int lookahead; // eax
  unsigned int strstart; // edx
  unsigned __int16 *head; // ecx
  unsigned int v6; // eax
  unsigned int match_length; // edx
  unsigned int match_start; // eax
  unsigned int v9; // eax
  unsigned int prev_length; // eax
  unsigned int v11; // edi
  unsigned __int8 v12; // cl
  unsigned __int16 v13; // ax
  unsigned __int16 v14; // ax
  int v15; // eax
  unsigned int v16; // eax
  BOOL v17; // ebx
  unsigned int v18; // edx
  unsigned __int16 *v19; // ecx
  unsigned int v20; // eax
  unsigned int v21; // ecx
  int v22; // edx
  char *v23; // eax
  z_stream_s *v24; // eax
  bool v25; // zf
  unsigned __int8 v27; // al
  int block_start; // ecx
  char *v29; // eax
  unsigned int v30; // edx
  z_stream_s *strm; // ecx
  unsigned int v32; // ecx
  unsigned int v33; // eax
  unsigned __int8 v34; // al
  int v35; // ecx
  char *v36; // eax

  v2 = 0;
  while ( 1 )
  {
    lookahead = s->lookahead;
    if ( lookahead < 0x106 )
    {
      fill_window(s);
      lookahead = s->lookahead;
      if ( lookahead < 0x106 && !flush )
        return 0;
      if ( !lookahead )
        break;
    }
    if ( lookahead >= 3 )
    {
      strstart = s->strstart;
      head = s->head;
      v6 = s->hash_mask & (s->window[strstart + 2] ^ (s->ins_h << s->hash_shift));
      s->ins_h = v6;
      v2 = head[v6];
      s->prev[strstart & s->w_mask] = v2;
      s->head[s->ins_h] = s->strstart;
    }
    match_length = s->match_length;
    match_start = s->match_start;
    s->prev_length = match_length;
    s->prev_match = match_start;
    s->match_length = 2;
    if ( v2 && match_length < s->max_lazy_match && s->strstart - v2 <= s->w_size - 262 )
    {
      if ( s->strategy != 2 )
        s->match_length = longest_match(s, v2);
      v9 = s->match_length;
      if ( v9 <= 5 && (s->strategy == 1 || v9 == 3 && s->strstart - s->match_start > 0x1000) )
        s->match_length = 2;
    }
    prev_length = s->prev_length;
    if ( prev_length < 3 || s->match_length > prev_length )
    {
      if ( s->match_available )
      {
        v27 = s->window[s->strstart - 1];
        s->d_buf[s->last_lit] = 0;
        s->l_buf[s->last_lit++] = v27;
        ++s->dyn_ltree[v27].fc.freq;
        if ( s->last_lit == s->lit_bufsize - 1 )
        {
          block_start = s->block_start;
          if ( block_start < 0 )
            v29 = 0;
          else
            v29 = (char *)&s->window[block_start];
          _tr_flush_block(s, v29, s->strstart - block_start, 0);
          s->block_start = s->strstart;
          flush_pending(s->strm);
        }
        v30 = s->strstart + 1;
        --s->lookahead;
        strm = s->strm;
        s->strstart = v30;
        v25 = strm->avail_out == 0;
        goto LABEL_32;
      }
      v32 = s->strstart + 1;
      v33 = s->lookahead - 1;
      s->match_available = 1;
      s->strstart = v32;
      s->lookahead = v33;
    }
    else
    {
      v11 = s->strstart + s->lookahead - 3;
      v12 = LOBYTE(s->prev_length) - 3;
      v13 = s->strstart - LOWORD(s->prev_match) - 1;
      s->d_buf[s->last_lit] = v13;
      s->l_buf[s->last_lit++] = v12;
      v14 = v13 - 1;
      ++s->dyn_ltree[_length_code[v12] + 257].fc.freq;
      if ( v14 >= 0x100u )
        v15 = (unsigned __int8)byte_4FC340[v14 >> 7];
      else
        v15 = _dist_code[v14];
      ++s->dyn_dtree[v15].fc.freq;
      v16 = s->prev_length;
      v17 = s->last_lit == s->lit_bufsize - 1;
      s->lookahead += 1 - v16;
      s->prev_length = v16 - 2;
      do
      {
        v18 = s->strstart + 1;
        s->strstart = v18;
        if ( v18 <= v11 )
        {
          v19 = s->head;
          v20 = s->hash_mask & (s->window[v18 + 2] ^ (s->ins_h << s->hash_shift));
          s->ins_h = v20;
          v2 = v19[v20];
          s->prev[v18 & s->w_mask] = v2;
          s->head[s->ins_h] = s->strstart;
        }
        v25 = s->prev_length-- == 1;
      }
      while ( !v25 );
      v21 = s->strstart + 1;
      s->match_available = 0;
      s->match_length = 2;
      s->strstart = v21;
      if ( v17 )
      {
        v22 = s->block_start;
        if ( v22 < 0 )
          v23 = 0;
        else
          v23 = (char *)&s->window[v22];
        _tr_flush_block(s, v23, v21 - v22, 0);
        v24 = s->strm;
        s->block_start = s->strstart;
        flush_pending(v24);
        v25 = s->strm->avail_out == 0;
LABEL_32:
        if ( v25 )
          return 0;
      }
    }
  }
  if ( s->match_available )
  {
    v34 = s->window[s->strstart - 1];
    s->d_buf[s->last_lit] = 0;
    s->l_buf[s->last_lit++] = v34;
    ++s->dyn_ltree[v34].fc.freq;
    s->match_available = 0;
  }
  v35 = s->block_start;
  if ( v35 < 0 )
    v36 = 0;
  else
    v36 = (char *)&s->window[v35];
  _tr_flush_block(s, v36, s->strstart - v35, flush == 4);
  s->block_start = s->strstart;
  flush_pending(s->strm);
  if ( s->strm->avail_out )
    return 2 * (flush == 4) + 1;
  else
    return flush != 4 ? 0 : 2;
}

//----- (004AA690) --------------------------------------------------------
int __cdecl deflateReset(z_stream_s *strm)
{
  internal_state *state; // esi
  bool v2; // sf
  int v3; // ecx

  if ( !strm )
    return -2;
  state = strm->state;
  if ( !state || !strm->zalloc || !strm->zfree )
    return -2;
  strm->total_out = 0;
  strm->total_in = 0;
  strm->msg = 0;
  strm->data_type = 2;
  state->pending_out = state->pending_buf;
  v2 = state->noheader < 0;
  state->pending = 0;
  if ( v2 )
    state->noheader = 0;
  state->status = state->noheader != 0 ? 113 : 42;
  strm->adler = 1;
  state->last_flush = 0;
  _tr_init(state);
  lm_init(v3, state);
  return 0;
}
// 4AA6F2: variable 'v3' is possibly undefined

//----- (004AA710) --------------------------------------------------------
int __cdecl deflateInit2_(
        z_stream_s *strm,
        unsigned int level,
        int method,
        int windowBits,
        int memLevel,
        unsigned int strategy,
        const char *version,
        int stream_size)
{
  int v8; // ebp
  bool v10; // zf
  unsigned int v11; // ecx
  int v12; // ebx
  internal_state *v13; // eax
  internal_state *v14; // esi
  unsigned __int8 *v15; // eax
  unsigned int w_size; // edx
  unsigned __int16 *v17; // eax
  unsigned int hash_size; // ecx
  unsigned __int8 *v19; // eax
  unsigned int lit_bufsize; // ecx
  unsigned __int8 *window; // edx
  unsigned int v22; // [esp-34h] [ebp-3Ch]

  v8 = 0;
  if ( !version || *version != *my_version || stream_size != 56 )
    return -6;
  if ( !strm )
    return -2;
  v10 = strm->zalloc == 0;
  strm->msg = 0;
  if ( v10 )
  {
    strm->zalloc = zcalloc;
    strm->opaque = 0;
  }
  if ( !strm->zfree )
    strm->zfree = (void (__cdecl *)(void *, void *))zcfree;
  v11 = level;
  if ( level == -1 )
  {
    level = 6;
    v11 = 6;
  }
  v12 = windowBits;
  if ( windowBits < 0 )
  {
    v8 = 1;
    v12 = -windowBits;
  }
  if ( memLevel < 1 || memLevel > 9 || method != 8 || v12 < 9 || v12 > 15 || v11 > 9 || strategy > 2 )
    return -2;
  v13 = (internal_state *)strm->zalloc(strm->opaque, 1, 5816);
  v14 = v13;
  if ( v13 )
  {
    strm->state = v13;
    v13->noheader = v8;
    v13->w_bits = v12;
    v13->w_mask = (1 << v12) - 1;
    v13->hash_bits = memLevel + 7;
    v13->hash_size = 1 << (memLevel + 7);
    v13->hash_mask = (1 << (memLevel + 7)) - 1;
    v13->strm = strm;
    v13->w_size = 1 << v12;
    v13->hash_shift = (memLevel + 9) / 3u;
    v15 = (unsigned __int8 *)strm->zalloc(strm->opaque, 1 << v12, 2);
    w_size = v14->w_size;
    v14->window = v15;
    v17 = (unsigned __int16 *)strm->zalloc(strm->opaque, w_size, 2);
    hash_size = v14->hash_size;
    v14->prev = v17;
    v14->head = (unsigned __int16 *)strm->zalloc(strm->opaque, hash_size, 2);
    v22 = 1 << (memLevel + 6);
    v14->lit_bufsize = v22;
    v19 = (unsigned __int8 *)strm->zalloc(strm->opaque, v22, 4);
    lit_bufsize = v14->lit_bufsize;
    v14->pending_buf_size = 4 * lit_bufsize;
    window = v14->window;
    v14->pending_buf = v19;
    if ( window && v14->prev && v14->head && v19 )
    {
      v14->d_buf = (unsigned __int16 *)&v19[2 * (lit_bufsize >> 1)];
      v14->l_buf = &v19[2 * lit_bufsize + lit_bufsize];
      v14->level = level;
      v14->strategy = strategy;
      v14->method = 8;
      return deflateReset(strm);
    }
    strm->msg = (char *)z_errmsg[6];
    deflateEnd(strm);
  }
  return -4;
}

//----- (004AA900) --------------------------------------------------------
int __cdecl inflateReset(z_stream_s *z)
{
  internal_state *state; // ecx

  if ( !z )
    return -2;
  state = z->state;
  if ( !state )
    return -2;
  z->total_out = 0;
  z->total_in = 0;
  z->msg = 0;
  state->strm = state->pending_buf_size != 0 ? (z_stream_s *)7 : 0;
  inflate_blocks_reset((inflate_blocks_state *)z->state->pending, z, 0);
  return 0;
}

//----- (004AA950) --------------------------------------------------------
int __cdecl inflateEnd(z_stream_s *z)
{
  internal_state *state; // eax
  inflate_blocks_state *pending; // eax

  if ( !z )
    return -2;
  state = z->state;
  if ( !state || !z->zfree )
    return -2;
  pending = (inflate_blocks_state *)state->pending;
  if ( pending )
    inflate_blocks_free(pending, z);
  z->zfree(z->opaque, z->state);
  z->state = 0;
  return 0;
}

//----- (004AA9A0) --------------------------------------------------------
int __cdecl inflateInit2_(z_stream_s *z, int w, const char *version, int stream_size)
{
  bool v4; // zf
  internal_state *v5; // eax
  int v6; // ecx

  if ( !version || *version != 49 || stream_size != 56 )
    return -6;
  if ( !z )
    return -2;
  v4 = z->zalloc == 0;
  z->msg = 0;
  if ( v4 )
  {
    z->zalloc = zcalloc;
    z->opaque = 0;
  }
  if ( !z->zfree )
    z->zfree = (void (__cdecl *)(void *, void *))zcfree;
  v5 = (internal_state *)z->zalloc(z->opaque, 1, 24);
  z->state = v5;
  if ( !v5 )
    return -4;
  v5->pending = 0;
  z->state->pending_buf_size = 0;
  v6 = w;
  if ( w < 0 )
  {
    v6 = -w;
    z->state->pending_buf_size = 1;
  }
  if ( v6 < 8 || v6 > 15 )
  {
    inflateEnd(z);
    return -2;
  }
  z->state->pending_out = (unsigned __int8 *)v6;
  z->state->pending = (int)inflate_blocks_new(
                             z,
                             z->state->pending_buf_size != 0
                           ? 0
                           : (unsigned int (__cdecl *)(unsigned int, const unsigned __int8 *, unsigned int))adler32,
                             1 << v6);
  if ( !z->state->pending )
  {
    inflateEnd(z);
    return -4;
  }
  inflateReset(z);
  return 0;
}

//----- (004AAAA0) --------------------------------------------------------
int __cdecl crc32(unsigned int crc, const unsigned __int8 *buf, unsigned int len)
{
  const unsigned __int8 *v3; // ecx
  unsigned int v5; // edi
  unsigned int v6; // esi
  unsigned int v7; // ebp
  unsigned __int8 v8; // dl
  unsigned int v9; // eax
  const unsigned __int8 *v10; // ecx
  unsigned int v11; // esi
  unsigned __int8 v12; // dl
  unsigned int v13; // eax
  unsigned int v14; // esi
  unsigned __int8 v15; // dl
  unsigned int v16; // eax
  unsigned int v17; // esi
  char v18; // dl
  unsigned int v19; // eax
  char v20; // dl
  unsigned int v21; // eax
  char v22; // dl
  unsigned int v23; // eax
  int v24; // edx

  v3 = buf;
  if ( !buf )
    return 0;
  v5 = len;
  v6 = ~crc;
  if ( len >= 8 )
  {
    v7 = len >> 3;
    do
    {
      v5 -= 8;
      v8 = v3[1];
      v9 = crc_table[(unsigned __int8)(v6 ^ *v3)] ^ (v6 >> 8);
      v10 = v3 + 1;
      v11 = crc_table[(unsigned __int8)(v9 ^ v8)];
      v12 = v10[1];
      v13 = v11 ^ (v9 >> 8);
      ++v10;
      v14 = crc_table[(unsigned __int8)(v13 ^ v12)];
      v15 = v10[1];
      v16 = v14 ^ (v13 >> 8);
      ++v10;
      v17 = crc_table[(unsigned __int8)(v16 ^ v15)];
      v18 = *++v10;
      v19 = crc_table[(unsigned __int8)(v17 ^ BYTE1(v16) ^ v18)] ^ ((v17 ^ (v16 >> 8)) >> 8);
      v20 = *++v10;
      v21 = crc_table[(unsigned __int8)(v19 ^ v20)] ^ (v19 >> 8);
      v22 = *++v10;
      v23 = crc_table[(unsigned __int8)(v21 ^ v22)] ^ (v21 >> 8);
      v24 = (unsigned __int8)(v23 ^ v10[1]);
      v3 = v10 + 2;
      --v7;
      v6 = crc_table[v24] ^ (v23 >> 8);
    }
    while ( v7 );
  }
  for ( ; v5; --v5 )
    v6 = crc_table[(unsigned __int8)(v6 ^ *v3++)] ^ (v6 >> 8);
  return ~v6;
}

//----- (004AABD0) --------------------------------------------------------
void *__cdecl zcalloc(void *opaque, unsigned int items, unsigned int size)
{
  return calloc(items, size);
}

//----- (004AABF0) --------------------------------------------------------
void __cdecl zcfree(void *opaque, tagEntry *ptr)
{
  free(ptr);
}

//----- (004AAC00) --------------------------------------------------------
void __thiscall CRelocTable::Relocate(CRelocTable *this, void *a2, void *a3, char *a4)
{
  _DWORD *v4; // edx
  bool v5; // zf
  int v6; // eax
  _DWORD *v7; // edx
  int v8; // edx
  _DWORD *v9; // eax
  _DWORD v10[9]; // [esp+Ch] [ebp-24h]

  v4 = *(_DWORD **)(*(_DWORD *)this + 4);
  v5 = v4 == *(_DWORD **)(*(_DWORD *)this + 8);
  v10[0] = itoa;
  v10[1] = gcvt;
  v10[2] = malloc;
  v10[3] = free;
  v10[4] = strcpy;
  v10[5] = strcmp;
  v10[6] = strlen;
  v10[7] = RegisterAllocatedMemory;
  v10[8] = UnregisterAllocatedMemory;
  if ( !v5 )
  {
    do
      *(_DWORD *)&a4[*v4++] += a2;
    while ( v4 != *(_DWORD **)(*(_DWORD *)this + 8) );
  }
  v6 = *((_DWORD *)this + 1);
  v7 = *(_DWORD **)(v6 + 4);
  if ( v7 != *(_DWORD **)(v6 + 8) )
  {
    do
      *(_DWORD *)&a4[*v7++] += a3;
    while ( v7 != *(_DWORD **)(*((_DWORD *)this + 1) + 8) );
  }
  v8 = *((_DWORD *)this + 2);
  v9 = *(_DWORD **)(v8 + 4);
  if ( v9 != *(_DWORD **)(v8 + 8) )
  {
    do
    {
      *(_DWORD *)&a4[v9[1]] = v10[*v9];
      v9 += 2;
    }
    while ( v9 != *(_DWORD **)(*((_DWORD *)this + 2) + 8) );
  }
}

//----- (004AACE0) --------------------------------------------------------
void __thiscall CRelocTable::Destroy(CRelocTable *this)
{
  _DWORD *v2; // esi
  int v3; // esi

  v2 = *(_DWORD **)this;
  if ( *(_DWORD *)(*(_DWORD *)this + 4) )
    operator delete(*(void **)(*(_DWORD *)this + 4));
  v2[1] = 0;
  v2[2] = 0;
  v2[3] = 0;
  v3 = *((_DWORD *)this + 1);
  if ( *(_DWORD *)(v3 + 4) )
    operator delete(*(void **)(v3 + 4));
  *(_DWORD *)(v3 + 4) = 0;
  *(_DWORD *)(v3 + 8) = 0;
  *(_DWORD *)(v3 + 12) = 0;
}

//----- (004AAD30) --------------------------------------------------------
void __noreturn std::vector<int>::_Xlen()
{
  std::string _Message; // [esp+0h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+1Ch] [ebp-34h] BYREF
  int v2; // [esp+4Ch] [ebp-4h]

  _Message._Myres = 15;
  _Message._Mysize = 0;
  _Message._Bx._Buf[0] = 0;
  std::string::assign(&_Message, "vector<T> too long", 0x12u);
  v2 = 0;
  std::logic_error::logic_error(&pExceptionObject, &_Message);
  pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
  _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
}
// 4AAD30: using guessed type void __noreturn std::vector<int>::_Xlen();
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004AADA0) --------------------------------------------------------
void __thiscall std::vector<int>::_Insert_n(
        std::vector<Quest::QuestNode *> *this,
        unsigned __int8 *src,
        unsigned int _Count,
        Quest::QuestNode *_Val)
{
  Quest::QuestNode **Myfirst; // edx
  unsigned int v6; // eax
  int v8; // ecx
  int v9; // ecx
  unsigned int v10; // eax
  int v11; // ecx
  int v12; // eax
  unsigned __int8 *v13; // eax
  unsigned int v14; // ebp
  int v15; // eax
  unsigned __int8 *v16; // eax
  Quest::QuestNode **v17; // eax
  int v18; // ecx
  int v19; // edi
  unsigned __int8 *Mylast; // ebp
  unsigned int v22; // edx
  unsigned int v23; // eax
  Quest::QuestNode **v24; // ecx
  Quest::QuestNode **v25; // edi
  Quest::QuestNode **v26; // [esp+8h] [ebp-4h]
  unsigned __int8 *srca; // [esp+10h] [ebp+4h]
  unsigned int _Counta; // [esp+14h] [ebp+8h]

  Myfirst = this->_Myfirst;
  _Val = *(Quest::QuestNode **)&_Val->m_wQuestID;
  if ( Myfirst )
    v6 = this->_Myend - Myfirst;
  else
    v6 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v8 = this->_Mylast - Myfirst;
    else
      v8 = 0;
    if ( 0x3FFFFFFF - v8 < _Count )
      std::vector<int>::_Xlen(this);
    if ( Myfirst )
      v9 = this->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( v6 >= _Count + v9 )
    {
      Mylast = (unsigned __int8 *)this->_Mylast;
      v22 = (Mylast - src) >> 2;
      v23 = 4 * _Count;
      srca = (unsigned __int8 *)(4 * _Count);
      if ( v22 >= _Count )
      {
        v25 = (Quest::QuestNode **)&Mylast[-v23];
        this->_Mylast = (Quest::QuestNode **)std::vector<unsigned long>::_Ucopy<unsigned long *>(
                                               &Mylast[-v23],
                                               (int)Mylast,
                                               Mylast);
        std::copy_backward<Quest::PhaseNode * *,Quest::PhaseNode * *>(
          (Quest::QuestNode **)src,
          v25,
          (Quest::QuestNode **)Mylast);
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)src,
          (Quest::QuestNode **)&srca[(_DWORD)src],
          &_Val);
      }
      else
      {
        std::vector<unsigned long>::_Ucopy<unsigned long *>(src, (int)Mylast, &src[v23]);
        std::vector<Quest::EventNode *>::_Ufill(
          this,
          this->_Mylast,
          _Count - (((char *)this->_Mylast - (char *)src) >> 2),
          &_Val);
        v24 = (Quest::QuestNode **)&srca[(unsigned int)this->_Mylast];
        this->_Mylast = v24;
        std::fill<enum Item::ItemType::Type *,enum Item::ItemType::Type>(
          (Quest::QuestNode **)src,
          (Quest::QuestNode **)((char *)v24 - (char *)srca),
          &_Val);
      }
    }
    else
    {
      if ( 0x3FFFFFFF - (v6 >> 1) >= v6 )
        v10 = (v6 >> 1) + v6;
      else
        v10 = 0;
      if ( Myfirst )
        v11 = this->_Mylast - Myfirst;
      else
        v11 = 0;
      if ( v10 < _Count + v11 )
      {
        if ( Myfirst )
          v12 = this->_Mylast - Myfirst;
        else
          v12 = 0;
        v10 = _Count + v12;
      }
      _Counta = v10;
      v13 = (unsigned __int8 *)operator new((tagHeader *)(4 * v10));
      v14 = 4 * ((src - (unsigned __int8 *)this->_Myfirst) >> 2);
      v26 = (Quest::QuestNode **)v13;
      memmove(v13, (unsigned __int8 *)this->_Myfirst, v14);
      v16 = (unsigned __int8 *)std::vector<Quest::EventNode *>::_Ufill(
                                 this,
                                 (Quest::QuestNode **)(v14 + v15),
                                 _Count,
                                 &_Val);
      memmove(v16, src, 4 * (((char *)this->_Mylast - (char *)src) >> 2));
      v17 = this->_Myfirst;
      if ( v17 )
        v18 = this->_Mylast - v17;
      else
        v18 = 0;
      v19 = v18 + _Count;
      if ( v17 )
        operator delete(this->_Myfirst);
      this->_Myend = &v26[_Counta];
      this->_Mylast = &v26[v19];
      this->_Myfirst = v26;
    }
  }
}
// 4AAE84: variable 'v15' is possibly undefined
// 4AAD30: using guessed type void __thiscall __noreturn std::vector<int>::_Xlen(_DWORD);

//----- (004AAF80) --------------------------------------------------------
void __thiscall std::vector<std::pair<enum eStdFunc,int>>::_Insert_n(
        std::vector<std::pair<unsigned long,unsigned long>> *_Al,
        std::pair<unsigned long,unsigned long> *_Last,
        unsigned int _Count,
        std::pair<unsigned long,unsigned long> *_Dest)
{
  unsigned int second; // edx
  std::pair<unsigned long,unsigned long> *Myfirst; // ecx
  unsigned int v7; // eax
  int v9; // edx
  int v10; // edx
  unsigned int v11; // eax
  int v12; // edx
  int v13; // eax
  std::pair<unsigned long,unsigned long> *v14; // edi
  std::pair<unsigned long,unsigned long> *v15; // ecx
  int v16; // eax
  int v17; // ebx
  std::pair<unsigned long,unsigned long> *Mylast; // eax
  bool v20; // cf
  unsigned int v21; // ecx
  std::pair<unsigned long,unsigned long> *v22; // ebx
  std::pair<unsigned long,unsigned long> *v23; // ebx
  std::pair<unsigned long,unsigned long> *v24; // [esp-18h] [ebp-40h]
  std::pair<unsigned long,unsigned long> *v25; // [esp-Ch] [ebp-34h]
  unsigned int v26; // [esp-8h] [ebp-30h]
  int v27; // [esp+0h] [ebp-28h] BYREF
  std::pair<unsigned long,unsigned long> _Val; // [esp+Ch] [ebp-1Ch] BYREF
  void *p; // [esp+14h] [ebp-14h]
  int *v30; // [esp+18h] [ebp-10h]
  int v31; // [esp+24h] [ebp-4h]
  std::pair<unsigned long,unsigned long> *_Lasta; // [esp+30h] [ebp+8h]
  unsigned int _Counta; // [esp+34h] [ebp+Ch]
  std::pair<unsigned long,unsigned long> *_Destb; // [esp+38h] [ebp+10h]
  std::pair<unsigned long,unsigned long> *_Desta; // [esp+38h] [ebp+10h]

  second = _Dest->second;
  _Val.first = _Dest->first;
  Myfirst = _Al->_Myfirst;
  v30 = &v27;
  _Val.second = second;
  if ( Myfirst )
    v7 = _Al->_Myend - Myfirst;
  else
    v7 = 0;
  if ( _Count )
  {
    if ( Myfirst )
      v9 = _Al->_Mylast - Myfirst;
    else
      v9 = 0;
    if ( 0x1FFFFFFF - v9 < _Count )
      std::vector<int>::_Xlen(_Al);
    if ( Myfirst )
      v10 = _Al->_Mylast - Myfirst;
    else
      v10 = 0;
    if ( v7 >= _Count + v10 )
    {
      Mylast = _Al->_Mylast;
      v20 = Mylast - _Last < _Count;
      v21 = 8 * _Count;
      _Lasta = (std::pair<unsigned long,unsigned long> *)(8 * _Count);
      _Desta = Mylast;
      if ( v20 )
      {
        std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
          _Last,
          Mylast,
          &_Last[v21 / 8]);
        v26 = _Count - (_Al->_Mylast - _Last);
        v25 = _Al->_Mylast;
        v31 = 2;
        std::vector<std::pair<enum eStdFunc,int>>::_Ufill(_Al, v25, v26, &_Val);
        v22 = (std::pair<unsigned long,unsigned long> *)((char *)_Lasta + (unsigned int)_Al->_Mylast);
        _Al->_Mylast = v22;
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          _Last,
          (std::pair<unsigned long,unsigned long> *)((char *)v22 - (char *)_Lasta),
          &_Val);
      }
      else
      {
        v23 = &Mylast[v21 / 0xFFFFFFF8];
        _Al->_Mylast = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                         &Mylast[v21 / 0xFFFFFFF8],
                         Mylast,
                         Mylast);
        std::copy_backward<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int> *>(_Last, v23, _Desta);
        std::fill<std::pair<enum eStdFunc,int> *,std::pair<enum eStdFunc,int>>(
          _Last,
          (std::pair<unsigned long,unsigned long> *)((char *)_Lasta + (_DWORD)_Last),
          &_Val);
      }
    }
    else
    {
      if ( 0x1FFFFFFF - (v7 >> 1) >= v7 )
        v11 = (v7 >> 1) + v7;
      else
        v11 = 0;
      if ( Myfirst )
        v12 = _Al->_Mylast - Myfirst;
      else
        v12 = 0;
      if ( v11 < _Count + v12 )
      {
        if ( Myfirst )
          v13 = _Al->_Mylast - Myfirst;
        else
          v13 = 0;
        v11 = _Count + v13;
      }
      _Counta = v11;
      v14 = (std::pair<unsigned long,unsigned long> *)operator new((tagHeader *)(8 * v11));
      v24 = _Al->_Myfirst;
      p = v14;
      v31 = 0;
      _Destb = std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
                 v24,
                 _Last,
                 v14);
      std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
        _Destb,
        _Count,
        &_Val);
      std::_Uninit_copy<std::vector<ItemDataParser::ParseData>::iterator,ItemDataParser::ParseData *,std::allocator<ItemDataParser::ParseData>>(
        _Last,
        _Al->_Mylast,
        &_Destb[_Count]);
      v15 = _Al->_Myfirst;
      if ( v15 )
        v16 = _Al->_Mylast - v15;
      else
        v16 = 0;
      v17 = v16 + _Count;
      if ( v15 )
        operator delete(_Al->_Myfirst);
      _Al->_Myend = &v14[_Counta];
      _Al->_Mylast = &v14[v17];
      _Al->_Myfirst = v14;
    }
  }
}
// 4AAD30: using guessed type void __thiscall __noreturn std::vector<int>::_Xlen(_DWORD);

//----- (004AB1D0) --------------------------------------------------------
CRelocTable *__thiscall CRelocTable::CRelocTable(CRelocTable *this)
{
  _DWORD *v2; // eax
  _DWORD *v3; // eax
  _DWORD *v4; // eax

  v2 = operator new((tagHeader *)0x10);
  if ( v2 )
  {
    v2[1] = 0;
    v2[2] = 0;
    v2[3] = 0;
  }
  else
  {
    v2 = 0;
  }
  *(_DWORD *)this = v2;
  v3 = operator new((tagHeader *)0x10);
  if ( v3 )
  {
    v3[1] = 0;
    v3[2] = 0;
    v3[3] = 0;
  }
  else
  {
    v3 = 0;
  }
  *((_DWORD *)this + 1) = v3;
  v4 = operator new((tagHeader *)0x10);
  if ( v4 )
  {
    v4[1] = 0;
    v4[2] = 0;
    v4[3] = 0;
    *((_DWORD *)this + 2) = v4;
  }
  else
  {
    *((_DWORD *)this + 2) = 0;
  }
  return this;
}

//----- (004AB280) --------------------------------------------------------
void __thiscall CRelocTable::~CRelocTable(CRelocTable *this)
{
  int v2; // esi
  int v3; // esi
  int v4; // esi

  v2 = *(_DWORD *)this;
  if ( *(_DWORD *)this )
  {
    if ( *(_DWORD *)(v2 + 4) )
      operator delete(*(void **)(v2 + 4));
    *(_DWORD *)(v2 + 4) = 0;
    *(_DWORD *)(v2 + 8) = 0;
    *(_DWORD *)(v2 + 12) = 0;
    operator delete((void *)v2);
  }
  v3 = *((_DWORD *)this + 1);
  if ( v3 )
  {
    if ( *(_DWORD *)(v3 + 4) )
      operator delete(*(void **)(v3 + 4));
    *(_DWORD *)(v3 + 4) = 0;
    *(_DWORD *)(v3 + 8) = 0;
    *(_DWORD *)(v3 + 12) = 0;
    operator delete((void *)v3);
  }
  v4 = *((_DWORD *)this + 2);
  if ( v4 )
  {
    if ( *(_DWORD *)(v4 + 4) )
      operator delete(*(void **)(v4 + 4));
    *(_DWORD *)(v4 + 4) = 0;
    *(_DWORD *)(v4 + 8) = 0;
    *(_DWORD *)(v4 + 12) = 0;
    operator delete((void *)v4);
  }
}

//----- (004AB310) --------------------------------------------------------
void __thiscall std::vector<std::pair<enum eStdFunc,int>>::push_back(
        std::vector<std::pair<unsigned long,unsigned long>> *_Al,
        std::pair<unsigned long,unsigned long> *_Val)
{
  std::pair<unsigned long,unsigned long> *Myfirst; // edx
  unsigned int v4; // ecx
  std::pair<unsigned long,unsigned long> *Mylast; // edi

  Myfirst = _Al->_Myfirst;
  if ( Myfirst )
    v4 = _Al->_Mylast - Myfirst;
  else
    v4 = 0;
  if ( Myfirst && v4 < _Al->_Myend - Myfirst )
  {
    Mylast = _Al->_Mylast;
    std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
      Mylast,
      1u,
      _Val);
    _Al->_Mylast = Mylast + 1;
  }
  else
  {
    std::vector<std::pair<enum eStdFunc,int>>::_Insert_n(_Al, _Al->_Mylast, 1u, _Val);
  }
}

//----- (004AB380) --------------------------------------------------------
unsigned int *__userpurge CRelocTable::Create@<eax>(CRelocTable *this@<ecx>, Quest::QuestNode __formal)
{
  _DWORD *v3; // esi
  int v4; // esi
  int *v5; // ecx
  int v6; // eax
  _DWORD *v7; // esi
  int v8; // ecx
  unsigned int v9; // edx
  unsigned int *v10; // esi
  int v11; // ecx
  unsigned int v12; // ebx
  int v13; // eax
  unsigned int v14; // edi
  unsigned int *v15; // eax
  int v16; // ecx
  unsigned int v17; // ebx
  int v18; // eax
  unsigned int v19; // edi
  unsigned int *v20; // eax
  std::vector<std::pair<unsigned long,unsigned long>> *v21; // edi
  unsigned int v22; // ecx
  std::pair<unsigned long,unsigned long> *Myfirst; // eax
  unsigned int v24; // edx
  std::pair<unsigned long,unsigned long> *Mylast; // ebx
  int v27; // [esp+10h] [ebp-10h]
  int v28; // [esp+10h] [ebp-10h]
  int v29; // [esp+14h] [ebp-Ch]
  std::pair<unsigned long,unsigned long> _Val; // [esp+18h] [ebp-8h] BYREF

  v3 = *(_DWORD **)this;
  if ( *(_DWORD *)(*(_DWORD *)this + 4) )
    operator delete(*(void **)(*(_DWORD *)this + 4));
  v3[1] = 0;
  v3[2] = 0;
  v3[3] = 0;
  v4 = *((_DWORD *)this + 1);
  if ( *(_DWORD *)(v4 + 4) )
    operator delete(*(void **)(v4 + 4));
  v5 = *(int **)&__formal.m_wQuestID;
  *(_DWORD *)(v4 + 4) = 0;
  *(_DWORD *)(v4 + 8) = 0;
  *(_DWORD *)(v4 + 12) = 0;
  v6 = *v5;
  v7 = v5 + 1;
  v8 = v5[1];
  v9 = v7[1];
  v10 = v7 + 2;
  v29 = v8;
  _Val.first = v9;
  if ( v6 > 0 )
  {
    v27 = v6;
    do
    {
      v11 = *(_DWORD *)this;
      v12 = *v10;
      v13 = *(_DWORD *)(*(_DWORD *)this + 4);
      ++v10;
      *(_DWORD *)&__formal.m_wQuestID = v12;
      if ( v13 )
        v14 = (*(_DWORD *)(v11 + 8) - v13) >> 2;
      else
        v14 = 0;
      if ( v13 && v14 < (*(_DWORD *)(v11 + 12) - v13) >> 2 )
      {
        v15 = *(unsigned int **)(v11 + 8);
        *v15 = v12;
        *(_DWORD *)(v11 + 8) = v15 + 1;
      }
      else
      {
        std::vector<int>::_Insert_n(
          (std::vector<Quest::QuestNode *> *)v11,
          *(unsigned __int8 **)(v11 + 8),
          1u,
          &__formal);
      }
      --v27;
    }
    while ( v27 );
    v8 = v29;
  }
  if ( v8 > 0 )
  {
    v28 = v8;
    do
    {
      v16 = *((_DWORD *)this + 1);
      v17 = *v10;
      v18 = *(_DWORD *)(v16 + 4);
      ++v10;
      *(_DWORD *)&__formal.m_wQuestID = v17;
      if ( v18 )
        v19 = (*(_DWORD *)(v16 + 8) - v18) >> 2;
      else
        v19 = 0;
      if ( v18 && v19 < (*(_DWORD *)(v16 + 12) - v18) >> 2 )
      {
        v20 = *(unsigned int **)(v16 + 8);
        *v20 = v17;
        *(_DWORD *)(v16 + 8) = v20 + 1;
      }
      else
      {
        std::vector<int>::_Insert_n(
          (std::vector<Quest::QuestNode *> *)v16,
          *(unsigned __int8 **)(v16 + 8),
          1u,
          &__formal);
      }
      --v28;
    }
    while ( v28 );
  }
  if ( (int)_Val.first > 0 )
  {
    *(_DWORD *)&__formal.m_wQuestID = _Val.first;
    do
    {
      v21 = (std::vector<std::pair<unsigned long,unsigned long>> *)*((_DWORD *)this + 2);
      v22 = v10[1];
      _Val.first = *v10;
      Myfirst = v21->_Myfirst;
      v10 += 2;
      _Val.second = v22;
      if ( Myfirst )
        v24 = v21->_Mylast - Myfirst;
      else
        v24 = 0;
      if ( Myfirst && v24 < v21->_Myend - Myfirst )
      {
        Mylast = v21->_Mylast;
        std::_Uninit_fill_n<std::pair<unsigned long,Guild::CGuild *> *,unsigned int,std::pair<unsigned long,Guild::CGuild *>,std::allocator<std::pair<unsigned long,Guild::CGuild *>>>(
          Mylast,
          1u,
          &_Val);
        v21->_Mylast = Mylast + 1;
      }
      else
      {
        std::vector<std::pair<enum eStdFunc,int>>::_Insert_n(v21, v21->_Mylast, 1u, &_Val);
      }
      --*(_DWORD *)&__formal.m_wQuestID;
    }
    while ( *(_DWORD *)&__formal.m_wQuestID );
  }
  return v10;
}

//----- (004AB530) --------------------------------------------------------
void __userpurge CRelocTable::AddGlobalVar(CRelocTable *this@<ecx>, Quest::QuestNode _Val)
{
  int v2; // ecx
  int v3; // esi
  unsigned int v4; // edx
  _DWORD *v5; // eax

  v2 = *(_DWORD *)this;
  v3 = *(_DWORD *)(v2 + 4);
  if ( v3 )
    v4 = (*(_DWORD *)(v2 + 8) - v3) >> 2;
  else
    v4 = 0;
  if ( v3 && v4 < (*(_DWORD *)(v2 + 12) - v3) >> 2 )
  {
    v5 = *(_DWORD **)(v2 + 8);
    *v5 = *(_DWORD *)&_Val.m_wQuestID;
    *(_DWORD *)(v2 + 8) = v5 + 1;
  }
  else
  {
    std::vector<int>::_Insert_n((std::vector<Quest::QuestNode *> *)v2, *(unsigned __int8 **)(v2 + 8), 1u, &_Val);
  }
}

//----- (004AB580) --------------------------------------------------------
void __userpurge CRelocTable::AddConstString(CRelocTable *this@<ecx>, Quest::QuestNode _Val)
{
  int v2; // ecx
  int v3; // esi
  unsigned int v4; // edx
  _DWORD *v5; // eax

  v2 = *((_DWORD *)this + 1);
  v3 = *(_DWORD *)(v2 + 4);
  if ( v3 )
    v4 = (*(_DWORD *)(v2 + 8) - v3) >> 2;
  else
    v4 = 0;
  if ( v3 && v4 < (*(_DWORD *)(v2 + 12) - v3) >> 2 )
  {
    v5 = *(_DWORD **)(v2 + 8);
    *v5 = *(_DWORD *)&_Val.m_wQuestID;
    *(_DWORD *)(v2 + 8) = v5 + 1;
  }
  else
  {
    std::vector<int>::_Insert_n((std::vector<Quest::QuestNode *> *)v2, *(unsigned __int8 **)(v2 + 8), 1u, &_Val);
  }
}

//----- (004AB5D0) --------------------------------------------------------
void __thiscall CRelocTable::AddFuncBind(_DWORD *this, unsigned int a2, unsigned int a3)
{
  std::vector<std::pair<unsigned long,unsigned long>> *v3; // ecx
  std::pair<unsigned long,unsigned long> _Val; // [esp+0h] [ebp-8h] BYREF

  v3 = (std::vector<std::pair<unsigned long,unsigned long>> *)this[2];
  _Val.first = a2;
  _Val.second = a3;
  std::vector<std::pair<enum eStdFunc,int>>::push_back(v3, &_Val);
}

//----- (004AB600) --------------------------------------------------------
void __thiscall CharacterFightInfo::CharacterFightInfo(CharacterFightInfo *this)
{
  this->m_pDuelOpponent = 0;
}

//----- (004AB610) --------------------------------------------------------
SFuncType *__thiscall SFuncType::SFuncType(SFuncType *this, unsigned int a2)
{
  SFuncType *result; // eax

  result = this;
  *(_DWORD *)this = a2;
  return result;
}

//----- (004AB620) --------------------------------------------------------
enum eDataType __thiscall CSymbolTable::GetTypeOfVar(CSymbolTable *this, int a2)
{
  return *(_DWORD *)(a2 + 28);
}

//----- (004AB630) --------------------------------------------------------
int __thiscall CSymbolTable::GetOffsetOfConst(CSymbolTable *this, int a2)
{
  return *(_DWORD *)(a2 + 32);
}

//----- (004AB640) --------------------------------------------------------
int __thiscall CSymbolTable::GetOffsetOfVar(CSymbolTable *this, int a2)
{
  return *(_DWORD *)(a2 + 36);
}

//----- (004AB650) --------------------------------------------------------
_DWORD *__stdcall CSymbolTable::GetTypeOfFunc(_DWORD *a1, int a2)
{
  _DWORD *result; // eax

  result = a1;
  *a1 = *(_DWORD *)(a2 + 28);
  return result;
}

//----- (004AB660) --------------------------------------------------------
void __thiscall CSymbolTable::EndArgument(CSymbolTable *this)
{
  int v1; // eax

  v1 = *((_DWORD *)this + 6);
  if ( v1 )
  {
    *((_DWORD *)this + 3) = v1 + 16;
    *((_DWORD *)this + 4) = v1 + 4;
    *((_DWORD *)this + 8) = 0;
    *((_DWORD *)this + 10) = -4;
  }
}

//----- (004AB690) --------------------------------------------------------
int __cdecl std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Max(
        int a1)
{
  int result; // eax
  int i; // ecx

  result = a1;
  for ( i = *(_DWORD *)(a1 + 8); !*(_BYTE *)(i + 53); i = *(_DWORD *)(i + 8) )
    result = i;
  return result;
}

//----- (004AB6B0) --------------------------------------------------------
_DWORD *__cdecl std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Min(
        _DWORD *a1)
{
  _DWORD *result; // eax
  int *v2; // ecx

  result = a1;
  v2 = (int *)*a1;
  if ( !*(_BYTE *)(*a1 + 53) )
  {
    do
    {
      result = v2;
      v2 = (int *)*v2;
    }
    while ( !*((_BYTE *)v2 + 53) );
  }
  return result;
}

//----- (004AB6D0) --------------------------------------------------------
int __cdecl std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Max(
        int a1)
{
  int result; // eax
  int i; // ecx

  result = a1;
  for ( i = *(_DWORD *)(a1 + 8); !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 8) )
    result = i;
  return result;
}

//----- (004AB6F0) --------------------------------------------------------
int __thiscall SFuncType::GetArgCount(SFuncType *this)
{
  unsigned int v1; // esi
  int result; // eax
  int i; // ecx

  v1 = *(_DWORD *)this;
  result = 0;
  for ( i = 0; i < 28; i += 4 )
  {
    if ( ((v1 >> i) & 0xF) == 0 )
      break;
    ++result;
  }
  return result;
}

//----- (004AB710) --------------------------------------------------------
char *__thiscall SFuncType::ToString(SFuncType *this, const char *a2)
{
  char *v3; // esi
  char *v4; // esi
  int i; // edi
  int v6; // eax

  v3 = &byte_53C148[sprintf(byte_53C148, "%s %s(", (const char *)(&DataTypeString)[*(_DWORD *)this >> 28], a2)];
  if ( (*(_DWORD *)this & 0xF) != 0 )
  {
    v4 = &v3[sprintf(v3, " %s", (const char *)(&DataTypeString)[*(_DWORD *)this & 0xF])];
    for ( i = 4; i < 28; i += 4 )
    {
      v6 = (*(_DWORD *)this >> i) & 0xF;
      if ( !v6 )
        break;
      v4 += sprintf(v4, ", %s", (const char *)(&DataTypeString)[v6]);
    }
    *(_WORD *)v4 = 32;
    *(_WORD *)(v4 + 1) = 41;
    return byte_53C148;
  }
  else
  {
    *(_WORD *)v3 = 41;
    return byte_53C148;
  }
}
// 50C55C: using guessed type const char **DataTypeString;

//----- (004AB7C0) --------------------------------------------------------
_DWORD *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
        _DWORD *this,
        int a2)
{
  _DWORD *result; // eax
  int v3; // ecx
  _DWORD *v4; // ecx

  result = *(_DWORD **)(a2 + 8);
  *(_DWORD *)(a2 + 8) = *result;
  if ( !*(_BYTE *)(*result + 53) )
    *(_DWORD *)(*result + 4) = a2;
  result[1] = *(_DWORD *)(a2 + 4);
  v3 = this[1];
  if ( a2 == *(_DWORD *)(v3 + 4) )
  {
    *(_DWORD *)(v3 + 4) = result;
    *result = a2;
    *(_DWORD *)(a2 + 4) = result;
  }
  else
  {
    v4 = *(_DWORD **)(a2 + 4);
    if ( a2 == *v4 )
      *v4 = result;
    else
      v4[2] = result;
    *result = a2;
    *(_DWORD *)(a2 + 4) = result;
  }
  return result;
}

//----- (004AB820) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
        _DWORD *this,
        _DWORD *a2)
{
  int result; // eax
  int v3; // esi
  int v4; // ecx
  _DWORD *v5; // ecx

  result = *a2;
  *a2 = *(_DWORD *)(*a2 + 8);
  v3 = *(_DWORD *)(result + 8);
  if ( !*(_BYTE *)(v3 + 53) )
    *(_DWORD *)(v3 + 4) = a2;
  *(_DWORD *)(result + 4) = a2[1];
  v4 = this[1];
  if ( a2 == *(_DWORD **)(v4 + 4) )
  {
    *(_DWORD *)(v4 + 4) = result;
    *(_DWORD *)(result + 8) = a2;
    a2[1] = result;
  }
  else
  {
    v5 = (_DWORD *)a2[1];
    if ( a2 == (_DWORD *)v5[2] )
      v5[2] = result;
    else
      *v5 = result;
    *(_DWORD *)(result + 8) = a2;
    a2[1] = result;
  }
  return result;
}

//----- (004AB880) --------------------------------------------------------
_DWORD *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
        _DWORD *this,
        int a2)
{
  _DWORD *result; // eax
  int v3; // ecx
  _DWORD *v4; // ecx

  result = *(_DWORD **)(a2 + 8);
  *(_DWORD *)(a2 + 8) = *result;
  if ( !*(_BYTE *)(*result + 49) )
    *(_DWORD *)(*result + 4) = a2;
  result[1] = *(_DWORD *)(a2 + 4);
  v3 = this[1];
  if ( a2 == *(_DWORD *)(v3 + 4) )
  {
    *(_DWORD *)(v3 + 4) = result;
    *result = a2;
    *(_DWORD *)(a2 + 4) = result;
  }
  else
  {
    v4 = *(_DWORD **)(a2 + 4);
    if ( a2 == *v4 )
      *v4 = result;
    else
      v4[2] = result;
    *result = a2;
    *(_DWORD *)(a2 + 4) = result;
  }
  return result;
}

//----- (004AB8E0) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
        _DWORD *this,
        _DWORD *a2)
{
  int result; // eax
  int v3; // esi
  int v4; // ecx
  _DWORD *v5; // ecx

  result = *a2;
  *a2 = *(_DWORD *)(*a2 + 8);
  v3 = *(_DWORD *)(result + 8);
  if ( !*(_BYTE *)(v3 + 49) )
    *(_DWORD *)(v3 + 4) = a2;
  *(_DWORD *)(result + 4) = a2[1];
  v4 = this[1];
  if ( a2 == *(_DWORD **)(v4 + 4) )
  {
    *(_DWORD *)(v4 + 4) = result;
    *(_DWORD *)(result + 8) = a2;
    a2[1] = result;
  }
  else
  {
    v5 = (_DWORD *)a2[1];
    if ( a2 == (_DWORD *)v5[2] )
      v5[2] = result;
    else
      *v5 = result;
    *(_DWORD *)(result + 8) = a2;
    a2[1] = result;
  }
  return result;
}

//----- (004AB940) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::const_iterator::_Dec(
        void *this)
{
  _DWORD *v1; // eax
  int result; // eax
  int v3; // edx

  v1 = *(_DWORD **)this;
  if ( *(_BYTE *)(*(_DWORD *)this + 49) )
  {
    result = v1[2];
    *(_DWORD *)this = result;
  }
  else
  {
    v3 = *v1;
    if ( *(_BYTE *)(*v1 + 49) )
    {
      result = v1[1];
      if ( !*(_BYTE *)(result + 49) )
      {
        do
        {
          if ( *(_DWORD *)this != *(_DWORD *)result )
            break;
          *(_DWORD *)this = result;
          result = *(_DWORD *)(result + 4);
        }
        while ( !*(_BYTE *)(result + 49) );
        if ( !*(_BYTE *)(result + 49) )
          *(_DWORD *)this = result;
      }
    }
    else
    {
      for ( result = *(_DWORD *)(v3 + 8); !*(_BYTE *)(result + 49); result = *(_DWORD *)(result + 8) )
        v3 = result;
      *(_DWORD *)this = v3;
    }
  }
  return result;
}

//----- (004AB9A0) --------------------------------------------------------
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::const_iterator::_Inc(
        int *this)
{
  int *result; // eax
  int *v2; // edx

  result = (int *)*this;
  if ( !*(_BYTE *)(*this + 49) )
  {
    v2 = (int *)result[2];
    if ( *((_BYTE *)v2 + 49) )
    {
      for ( result = (int *)result[1]; !*((_BYTE *)result + 49); result = (int *)result[1] )
      {
        if ( *this != result[2] )
          break;
        *this = (int)result;
      }
      *this = (int)result;
    }
    else
    {
      result = (int *)*v2;
      if ( !*(_BYTE *)(*v2 + 49) )
      {
        do
        {
          v2 = result;
          result = (int *)*result;
        }
        while ( !*((_BYTE *)result + 49) );
      }
      *this = (int)v2;
    }
  }
  return result;
}

//----- (004ABA00) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Dec(
        void *this)
{
  _DWORD *v1; // eax
  int result; // eax
  int v3; // edx

  v1 = *(_DWORD **)this;
  if ( *(_BYTE *)(*(_DWORD *)this + 53) )
  {
    result = v1[2];
    *(_DWORD *)this = result;
  }
  else
  {
    v3 = *v1;
    if ( *(_BYTE *)(*v1 + 53) )
    {
      result = v1[1];
      if ( !*(_BYTE *)(result + 53) )
      {
        do
        {
          if ( *(_DWORD *)this != *(_DWORD *)result )
            break;
          *(_DWORD *)this = result;
          result = *(_DWORD *)(result + 4);
        }
        while ( !*(_BYTE *)(result + 53) );
        if ( !*(_BYTE *)(result + 53) )
          *(_DWORD *)this = result;
      }
    }
    else
    {
      for ( result = *(_DWORD *)(v3 + 8); !*(_BYTE *)(result + 53); result = *(_DWORD *)(result + 8) )
        v3 = result;
      *(_DWORD *)this = v3;
    }
  }
  return result;
}

//----- (004ABA60) --------------------------------------------------------
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Inc(
        int *this)
{
  int *result; // eax
  int *v2; // edx

  result = (int *)*this;
  if ( !*(_BYTE *)(*this + 53) )
  {
    v2 = (int *)result[2];
    if ( *((_BYTE *)v2 + 53) )
    {
      for ( result = (int *)result[1]; !*((_BYTE *)result + 53); result = (int *)result[1] )
      {
        if ( *this != result[2] )
          break;
        *this = (int)result;
      }
      *this = (int)result;
    }
    else
    {
      result = (int *)*v2;
      if ( !*(_BYTE *)(*v2 + 53) )
      {
        do
        {
          v2 = result;
          result = (int *)*result;
        }
        while ( !*((_BYTE *)result + 53) );
      }
      *this = (int)v2;
    }
  }
  return result;
}

//----- (004ABAC0) --------------------------------------------------------
const char *__thiscall CSymbolTable::GetNameOfVar(CSymbolTable *this, int a2)
{
  if ( *(_DWORD *)(a2 + 24) < 0x10u )
    return (const char *)(a2 + 4);
  else
    return *(const char **)(a2 + 4);
}

//----- (004ABAE0) --------------------------------------------------------
char *__thiscall CSymbolTable::GetTypeStringOfFunc(CSymbolTable *this, int a2)
{
  int v2; // eax

  v2 = a2;
  a2 = *(_DWORD *)(a2 + 28);
  if ( *(_DWORD *)(v2 + 24) < 0x10u )
    return SFuncType::ToString((SFuncType *)&a2, (const char *)(v2 + 4));
  else
    return SFuncType::ToString((SFuncType *)&a2, *(const char **)(v2 + 4));
}

//----- (004ABB20) --------------------------------------------------------
_DWORD *std::list<SLocalVarInfo>::_Buynode()
{
  _DWORD *result; // eax

  result = operator new((tagHeader *)0x24);
  if ( result )
    *result = result;
  if ( result != (_DWORD *)-4 )
    result[1] = result;
  return result;
}

//----- (004ABB40) --------------------------------------------------------
_DWORD *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode()
{
  _DWORD *result; // eax

  result = operator new((tagHeader *)0x38);
  if ( result )
    *result = 0;
  if ( result != (_DWORD *)-4 )
    result[1] = 0;
  if ( result != (_DWORD *)-8 )
    result[2] = 0;
  *((_BYTE *)result + 52) = 1;
  *((_BYTE *)result + 53) = 0;
  return result;
}

//----- (004ABB80) --------------------------------------------------------
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lbound(
        _DWORD *this,
        int a2)
{
  int *result; // eax
  int *v3; // ebp
  unsigned int v4; // eax
  const char **v5; // ecx
  const char *v6; // edi
  unsigned int v7; // ebx
  int v8; // ecx
  const char *v9; // esi
  int v10; // edx
  bool v11; // sf
  int *v12; // [esp+4h] [ebp-8h]
  unsigned int v13; // [esp+8h] [ebp-4h]
  const char **i; // [esp+10h] [ebp+4h]

  result = (int *)this[1];
  v3 = (int *)result[1];
  v12 = result;
  if ( !*((_BYTE *)v3 + 53) )
  {
    v4 = *(_DWORD *)(a2 + 20);
    v5 = (const char **)(a2 + 4);
    v13 = *(_DWORD *)(a2 + 24);
    for ( i = (const char **)(a2 + 4); ; v5 = i )
    {
      if ( v13 < 0x10 )
        v6 = (const char *)v5;
      else
        v6 = *v5;
      v7 = v3[8];
      if ( !v7 )
        goto LABEL_14;
      v8 = v3[8];
      if ( v7 >= v4 )
        v8 = v4;
      v9 = (unsigned int)v3[9] < 0x10 ? (const char *)(v3 + 4) : (const char *)v3[4];
      v10 = memcmp(v9, v6, v8);
      v11 = v10 < 0;
      if ( !v10 )
      {
LABEL_14:
        if ( v7 < v4 )
          goto LABEL_17;
        v11 = 0;
      }
      if ( v11 )
      {
LABEL_17:
        v3 = (int *)v3[2];
        goto LABEL_19;
      }
      v12 = v3;
      v3 = (int *)*v3;
LABEL_19:
      if ( *((_BYTE *)v3 + 53) )
        return v12;
    }
  }
  return result;
}

//----- (004ABC40) --------------------------------------------------------
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lbound(
        _DWORD *this,
        int a2)
{
  int *result; // eax
  int *v3; // ebp
  unsigned int v4; // eax
  const char **v5; // ecx
  const char *v6; // edi
  unsigned int v7; // ebx
  int v8; // ecx
  const char *v9; // esi
  int v10; // edx
  bool v11; // sf
  int *v12; // [esp+4h] [ebp-8h]
  unsigned int v13; // [esp+8h] [ebp-4h]
  const char **i; // [esp+10h] [ebp+4h]

  result = (int *)this[1];
  v3 = (int *)result[1];
  v12 = result;
  if ( !*((_BYTE *)v3 + 49) )
  {
    v4 = *(_DWORD *)(a2 + 20);
    v5 = (const char **)(a2 + 4);
    v13 = *(_DWORD *)(a2 + 24);
    for ( i = (const char **)(a2 + 4); ; v5 = i )
    {
      if ( v13 < 0x10 )
        v6 = (const char *)v5;
      else
        v6 = *v5;
      v7 = v3[8];
      if ( !v7 )
        goto LABEL_14;
      v8 = v3[8];
      if ( v7 >= v4 )
        v8 = v4;
      v9 = (unsigned int)v3[9] < 0x10 ? (const char *)(v3 + 4) : (const char *)v3[4];
      v10 = memcmp(v9, v6, v8);
      v11 = v10 < 0;
      if ( !v10 )
      {
LABEL_14:
        if ( v7 < v4 )
          goto LABEL_17;
        v11 = 0;
      }
      if ( v11 )
      {
LABEL_17:
        v3 = (int *)v3[2];
        goto LABEL_19;
      }
      v12 = v3;
      v3 = (int *)*v3;
LABEL_19:
      if ( *((_BYTE *)v3 + 49) )
        return v12;
    }
  }
  return result;
}

//----- (004ABD00) --------------------------------------------------------
int *__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Ubound(
        _DWORD *this,
        int a2)
{
  int *result; // eax
  int *v3; // ebp
  int v4; // esi
  unsigned int i; // ecx
  unsigned int v6; // eax
  const char *v7; // edi
  unsigned int v8; // ebx
  int v9; // ecx
  const char *v10; // esi
  int v11; // edx
  bool v12; // sf
  int *v13; // [esp+4h] [ebp-Ch]

  result = (int *)this[1];
  v3 = (int *)result[1];
  v13 = result;
  if ( !*((_BYTE *)v3 + 49) )
  {
    v4 = a2;
    for ( i = *(_DWORD *)(a2 + 20); ; i = *(_DWORD *)(a2 + 20) )
    {
      v6 = v3[8];
      if ( (unsigned int)v3[9] < 0x10 )
        v7 = (const char *)(v3 + 4);
      else
        v7 = (const char *)v3[4];
      v8 = *(_DWORD *)(a2 + 20);
      if ( i < v8 )
        v8 = i;
      if ( !v8 )
        goto LABEL_16;
      v9 = v8;
      if ( v8 >= v6 )
        v9 = v3[8];
      v10 = *(_DWORD *)(v4 + 24) < 0x10u ? (const char *)(v4 + 4) : *(const char **)(v4 + 4);
      v11 = memcmp(v10, v7, v9);
      v12 = v11 < 0;
      v4 = a2;
      if ( !v11 )
      {
LABEL_16:
        if ( v8 < v6 )
          goto LABEL_19;
        v12 = 0;
      }
      if ( v12 )
      {
LABEL_19:
        v13 = v3;
        v3 = (int *)*v3;
        goto LABEL_21;
      }
      v3 = (int *)v3[2];
LABEL_21:
      if ( *((_BYTE *)v3 + 49) )
        return v13;
    }
  }
  return result;
}

//----- (004ABDB0) --------------------------------------------------------
int __thiscall CSymbolTable::GetLocalVarSize(CSymbolTable *this, int a2)
{
  _DWORD *v2; // edx
  _DWORD *v3; // ecx
  int result; // eax
  _DWORD *v5; // ecx
  int *v6; // esi
  int *v7; // edx
  int **v8; // ecx
  int *j; // ecx
  int *i; // ecx

  v2 = *(_DWORD **)(*((_DWORD *)this + 2) + 4);
  v3 = (_DWORD *)*v2;
  result = 0;
  if ( (_DWORD *)*v2 == v2 )
    goto LABEL_4;
  while ( v3[2] != a2 )
  {
    v3 = (_DWORD *)*v3;
    if ( v3 == v2 )
      goto LABEL_4;
  }
  v5 = v3 + 6;
  if ( v5 )
  {
    v6 = (int *)v5[1];
    v7 = (int *)*v6;
    while ( v7 != v6 )
    {
      result += 4 * v7[11];
      if ( !*((_BYTE *)v7 + 53) )
      {
        v8 = (int **)v7[2];
        if ( *((_BYTE *)v8 + 53) )
        {
          for ( i = (int *)v7[1]; !*((_BYTE *)i + 53); i = (int *)i[1] )
          {
            if ( v7 != (int *)i[2] )
              break;
            v7 = i;
          }
          v7 = i;
        }
        else
        {
          v7 = (int *)v7[2];
          for ( j = *v8; !*((_BYTE *)j + 53); j = (int *)*j )
            v7 = j;
        }
      }
    }
  }
  else
  {
LABEL_4:
    ScriptSystemError(&byte_4F8B40);
    return 0;
  }
  return result;
}

//----- (004ABE60) --------------------------------------------------------
int __thiscall CSymbolTable::GetGlobalVarSize(CSymbolTable *this)
{
  int *v1; // edi
  int v2; // eax
  int v3; // esi
  int *i; // [esp+8h] [ebp-4h] BYREF

  v1 = *(int **)(*((_DWORD *)this + 1) + 4);
  v2 = *v1;
  v3 = 0;
  for ( i = (int *)*v1; i != v1; v2 = (int)i )
  {
    v3 += 4 * *(_DWORD *)(v2 + 44);
    std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Inc((int *)&i);
  }
  return v3;
}

//----- (004ABEA0) --------------------------------------------------------
void __thiscall CSymbolTable::StringBuffer(CSymbolTable *this, char *a2)
{
  _DWORD *v3; // ecx
  _DWORD *v4; // eax
  const char *v5; // edx
  _DWORD *v6; // [esp+4h] [ebp-4h] BYREF

  v3 = *(_DWORD **)(*(_DWORD *)this + 4);
  v4 = (_DWORD *)*v3;
  v6 = (_DWORD *)*v3;
  if ( v6 != v3 )
  {
    do
    {
      if ( v4[10] == 4 )
      {
        if ( v4[9] < 0x10u )
          v5 = (const char *)(v4 + 4);
        else
          v5 = (const char *)v4[4];
        strcpy(&a2[v4[11]], v5);
      }
      std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::const_iterator::_Inc((int *)&v6);
      v4 = v6;
    }
    while ( v6 != *(_DWORD **)(*(_DWORD *)this + 4) );
  }
}

//----- (004ABF00) --------------------------------------------------------
std::string **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::find(
        _DWORD *this,
        std::string **a2,
        std::string *a3)
{
  int *v5; // eax
  unsigned int v6; // ecx
  const char *v7; // eax
  std::string **result; // eax
  std::string *v9; // [esp+14h] [ebp+8h]

  v5 = std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lbound(
         this,
         (int)a3);
  v9 = (std::string *)v5;
  if ( v5 == (int *)this[1]
    || ((v6 = v5[8], (unsigned int)v5[9] < 0x10) ? (v7 = (const char *)(v5 + 4)) : (v7 = (const char *)v5[4]),
        std::string::compare(a3, 0, a3->_Mysize, v7, v6) < 0) )
  {
    result = a2;
    *a2 = (std::string *)this[1];
  }
  else
  {
    result = a2;
    *a2 = v9;
  }
  return result;
}

//----- (004ABF70) --------------------------------------------------------
std::string **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::find(
        _DWORD *this,
        std::string **a2,
        std::string *a3)
{
  int *v5; // eax
  unsigned int v6; // ecx
  const char *v7; // eax
  std::string **result; // eax
  std::string *v9; // [esp+14h] [ebp+8h]

  v5 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lbound(
         this,
         (int)a3);
  v9 = (std::string *)v5;
  if ( v5 == (int *)this[1]
    || ((v6 = v5[8], (unsigned int)v5[9] < 0x10) ? (v7 = (const char *)(v5 + 4)) : (v7 = (const char *)v5[4]),
        std::string::compare(a3, 0, a3->_Mysize, v7, v6) < 0) )
  {
    result = a2;
    *a2 = (std::string *)this[1];
  }
  else
  {
    result = a2;
    *a2 = v9;
  }
  return result;
}

//----- (004ABFE0) --------------------------------------------------------
int __thiscall std::list<SLocalVarInfo>::_Incsize(_DWORD *this, unsigned int a2)
{
  int v2; // eax
  int result; // eax
  std::string _Message; // [esp+4h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+20h] [ebp-34h] BYREF
  int v6; // [esp+50h] [ebp-4h]

  v2 = this[2];
  if ( 153391689 - v2 < a2 )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "list<T> too long", 0x10u);
    v6 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  result = a2 + v2;
  this[2] = result;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004AC080) --------------------------------------------------------
_DWORD *__thiscall std::_Tree_nod<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Node::_Node(
        _DWORD *this,
        int a2,
        int a3,
        int a4,
        const std::string *_Right,
        char a6)
{
  _DWORD *v7; // edi

  this[1] = a3;
  v7 = this + 3;
  *this = a2;
  this[2] = a4;
  this[9] = 15;
  this[8] = 0;
  *((_BYTE *)this + 16) = 0;
  std::string::assign((std::string *)(this + 3), _Right, 0, 0xFFFFFFFF);
  v7 += 7;
  *v7 = *(_DWORD *)&_Right[1]._Alval.std::_Allocator_base<char>;
  v7[1] = _Right[1]._Bx._Ptr;
  v7[2] = *((_DWORD *)&_Right[1]._Bx._Ptr + 1);
  *((_BYTE *)this + 52) = a6;
  *((_BYTE *)this + 53) = 0;
  return this;
}

//----- (004AC0F0) --------------------------------------------------------
char *__thiscall std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Node::_Node(
        char *this,
        int a2,
        int a3,
        int a4,
        const std::string *_Right,
        char a6)
{
  char *v7; // edi

  *((_DWORD *)this + 1) = a3;
  v7 = this + 12;
  *(_DWORD *)this = a2;
  *((_DWORD *)this + 2) = a4;
  *((_DWORD *)this + 9) = 15;
  *((_DWORD *)this + 8) = 0;
  this[16] = 0;
  std::string::assign((std::string *)(this + 12), _Right, 0, 0xFFFFFFFF);
  *((_DWORD *)v7 + 7) = *(_DWORD *)&_Right[1]._Alval.std::_Allocator_base<char>;
  *((_DWORD *)v7 + 8) = _Right[1]._Bx._Ptr;
  this[48] = a6;
  this[49] = 0;
  return this;
}

//----- (004AC150) --------------------------------------------------------
const char *__thiscall CSymbolTable::FindConst(CSymbolTable *this, char *_Ptr)
{
  _DWORD *v3; // ecx
  std::string v5; // [esp+8h] [ebp-28h] BYREF
  int v6; // [esp+2Ch] [ebp-4h]

  v5._Myres = 15;
  v5._Mysize = 0;
  v5._Bx._Buf[0] = 0;
  std::string::assign(&v5, _Ptr, strlen(_Ptr));
  v3 = *(_DWORD **)this;
  v6 = 0;
  std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::find(
    v3,
    (std::string **)&_Ptr,
    &v5);
  if ( v5._Myres >= 0x10 )
    operator delete(v5._Bx._Ptr);
  if ( _Ptr == *(char **)(*(_DWORD *)this + 4) )
    return 0;
  else
    return _Ptr + 12;
}

//----- (004AC210) --------------------------------------------------------
char **__thiscall CSymbolTable::FindVar(CSymbolTable *this, char *_Ptr)
{
  const char *v2; // ebp
  _DWORD *v4; // ecx
  std::string *v5; // edi
  _DWORD *v6; // ecx
  _DWORD *v7; // ecx
  int v9; // [esp+10h] [ebp-48h] BYREF
  std::string v10; // [esp+14h] [ebp-44h] BYREF
  std::pair<std::string const ,unsigned char> v11; // [esp+30h] [ebp-28h] BYREF
  int v12; // [esp+54h] [ebp-4h]

  v2 = _Ptr;
  v10._Myres = 15;
  v10._Mysize = 0;
  v10._Bx._Buf[0] = 0;
  std::string::assign(&v10, _Ptr, strlen(_Ptr));
  v4 = (_DWORD *)*((_DWORD *)this + 3);
  v12 = 0;
  std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::find(
    v4,
    (std::string **)&_Ptr,
    &v10);
  v12 = -1;
  if ( v10._Myres >= 0x10 )
    operator delete(v10._Bx._Ptr);
  v5 = (std::string *)_Ptr;
  if ( _Ptr != *(char **)(*((_DWORD *)this + 3) + 4) )
    return &v5->_Bx._Ptr + 2;
  if ( !*((_DWORD *)this + 4) )
    return 0;
  v10._Myres = 15;
  v10._Mysize = 0;
  v10._Bx._Buf[0] = 0;
  std::string::assign(&v10, v2, strlen(v2));
  v6 = (_DWORD *)*((_DWORD *)this + 4);
  v12 = 1;
  v5 = *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::find(
          v6,
          (std::string **)&_Ptr,
          &v10);
  v12 = -1;
  if ( v10._Myres >= 0x10 )
    operator delete(v10._Bx._Ptr);
  if ( v5 == *(std::string **)(*((_DWORD *)this + 4) + 4)
    && (*((_DWORD *)this + 3) == *((_DWORD *)this + 1)
     || (std::string::string(&v11.first, v2),
         v7 = (_DWORD *)*((_DWORD *)this + 1),
         v12 = 2,
         v5 = *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::find(
                 v7,
                 (std::string **)&v9,
                 &v11.first),
         std::pair<std::string const,VarInfo>::~pair<std::string const,VarInfo>(&v11),
         v5 == *(std::string **)(*((_DWORD *)this + 1) + 4))) )
  {
    return 0;
  }
  else
  {
    return &v5->_Bx._Ptr + 2;
  }
}

//----- (004AC390) --------------------------------------------------------
int *__thiscall CSymbolTable::FindFunc(_DWORD *this, char *_Ptr, int a3)
{
  _DWORD *v4; // esi
  int *v5; // edi
  int *v6; // esi
  int v7; // eax
  int j; // eax
  int i; // eax
  std::string v11; // [esp+8h] [ebp-28h] BYREF
  int v12; // [esp+2Ch] [ebp-4h]

  v11._Myres = 15;
  v11._Mysize = 0;
  v11._Bx._Buf[0] = 0;
  std::string::assign(&v11, _Ptr, strlen(_Ptr));
  v4 = (_DWORD *)this[5];
  v12 = 0;
  v5 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Ubound(
         v4,
         (int)&v11);
  v6 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lbound(
         v4,
         (int)&v11);
  if ( v11._Myres >= 0x10 )
    operator delete(v11._Bx._Ptr);
  if ( v6 == v5 )
    return 0;
  while ( (v6[10] & 0xFFFFFFF) != (a3 & 0xFFFFFFF) )
  {
    if ( !*((_BYTE *)v6 + 49) )
    {
      v7 = v6[2];
      if ( *(_BYTE *)(v7 + 49) )
      {
        for ( i = v6[1]; !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 4) )
        {
          if ( v6 != *(int **)(i + 8) )
            break;
          v6 = (int *)i;
        }
        v6 = (int *)i;
      }
      else
      {
        v6 = (int *)v6[2];
        for ( j = *(_DWORD *)v7; !*(_BYTE *)(j + 49); j = *(_DWORD *)j )
          v6 = (int *)j;
      }
    }
    if ( v6 == v5 )
      return 0;
  }
  return v6 + 3;
}

//----- (004AC4C0) --------------------------------------------------------
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode(
        int a1,
        int a2,
        int a3,
        const std::string *_Right,
        char a5)
{
  CPacketDispatch *v5; // esi
  int v7; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v8; // [esp+Ch] [ebp-18h]
  void *p; // [esp+10h] [ebp-14h]
  int *v10; // [esp+14h] [ebp-10h]
  int v11; // [esp+20h] [ebp-4h]

  v10 = &v7;
  v5 = (CPacketDispatch *)operator new((tagHeader *)0x38);
  p = v5;
  v11 = 1;
  v8 = v5;
  if ( v5 )
    std::_Tree_nod<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Node::_Node(
      v5,
      a1,
      a2,
      a3,
      _Right,
      a5);
  return v5;
}

//----- (004AC550) --------------------------------------------------------
CPacketDispatch *__stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Buynode(
        int a1,
        int a2,
        int a3,
        const std::string *_Right,
        char a5)
{
  CPacketDispatch *v5; // esi
  int v7; // [esp+0h] [ebp-24h] BYREF
  CPacketDispatch *v8; // [esp+Ch] [ebp-18h]
  void *p; // [esp+10h] [ebp-14h]
  int *v10; // [esp+14h] [ebp-10h]
  int v11; // [esp+20h] [ebp-4h]

  v10 = &v7;
  v5 = (CPacketDispatch *)operator new((tagHeader *)0x34);
  p = v5;
  v11 = 1;
  v8 = v5;
  if ( v5 )
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Node::_Node(
      (char *)v5,
      a1,
      a2,
      a3,
      _Right,
      a5);
  return v5;
}

//----- (004AC5E0) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::erase(
        _DWORD *this,
        int **a2,
        int *p)
{
  int *v4; // ebp
  int v5; // edi
  int *v6; // ecx
  int v7; // esi
  int v8; // eax
  int **v9; // ebx
  int *v10; // eax
  int v11; // ebx
  int v12; // eax
  int **v13; // eax
  char v14; // al
  _DWORD *v15; // ecx
  _BYTE *v16; // eax
  bool v17; // zf
  int v18; // eax
  int **result; // eax
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( *((_BYTE *)p + 53) )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  v4 = p;
  std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Inc((int *)&p);
  if ( *(_BYTE *)(*v4 + 53) )
  {
    v5 = v4[2];
LABEL_8:
    v7 = v4[1];
    if ( !*(_BYTE *)(v5 + 53) )
      *(_DWORD *)(v5 + 4) = v7;
    v8 = this[1];
    if ( *(int **)(v8 + 4) == v4 )
    {
      *(_DWORD *)(v8 + 4) = v5;
    }
    else if ( *(int **)v7 == v4 )
    {
      *(_DWORD *)v7 = v5;
    }
    else
    {
      *(_DWORD *)(v7 + 8) = v5;
    }
    v9 = (int **)this[1];
    if ( *v9 == v4 )
    {
      if ( *(_BYTE *)(v5 + 53) )
        v10 = (int *)v7;
      else
        v10 = std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Min((_DWORD *)v5);
      *v9 = v10;
    }
    v11 = this[1];
    if ( *(int **)(v11 + 8) == v4 )
    {
      if ( *(_BYTE *)(v5 + 53) )
        *(_DWORD *)(v11 + 8) = v7;
      else
        *(_DWORD *)(v11 + 8) = std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Max(v5);
    }
    goto LABEL_35;
  }
  if ( *(_BYTE *)(v4[2] + 53) )
  {
    v5 = *v4;
    goto LABEL_8;
  }
  v6 = p;
  v5 = p[2];
  if ( p == v4 )
    goto LABEL_8;
  *(_DWORD *)(*v4 + 4) = p;
  *v6 = *v4;
  if ( v6 == (int *)v4[2] )
  {
    v7 = (int)v6;
  }
  else
  {
    v7 = v6[1];
    if ( !*(_BYTE *)(v5 + 53) )
      *(_DWORD *)(v5 + 4) = v7;
    *(_DWORD *)v7 = v5;
    v6[2] = v4[2];
    *(_DWORD *)(v4[2] + 4) = v6;
  }
  v12 = this[1];
  if ( *(int **)(v12 + 4) == v4 )
  {
    *(_DWORD *)(v12 + 4) = v6;
  }
  else
  {
    v13 = (int **)v4[1];
    if ( *v13 == v4 )
      *v13 = v6;
    else
      v13[2] = v6;
  }
  v6[1] = v4[1];
  v14 = *((_BYTE *)v6 + 52);
  *((_BYTE *)v6 + 52) = *((_BYTE *)v4 + 52);
  *((_BYTE *)v4 + 52) = v14;
LABEL_35:
  if ( *((_BYTE *)v4 + 52) == 1 )
  {
    v15 = this;
    if ( v5 != *(_DWORD *)(this[1] + 4) )
    {
      do
      {
        if ( *(_BYTE *)(v5 + 52) != 1 )
          break;
        v16 = *(_BYTE **)v7;
        if ( v5 == *(_DWORD *)v7 )
        {
          v16 = *(_BYTE **)(v7 + 8);
          if ( !v16[52] )
          {
            v16[52] = 1;
            *(_BYTE *)(v7 + 52) = 0;
            std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
              v15,
              v7);
            v16 = *(_BYTE **)(v7 + 8);
            v15 = this;
          }
          if ( v16[53] )
            goto LABEL_53;
          if ( *(_BYTE *)(*(_DWORD *)v16 + 52) != 1 || *(_BYTE *)(*((_DWORD *)v16 + 2) + 52) != 1 )
          {
            if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 52) == 1 )
            {
              *(_BYTE *)(*(_DWORD *)v16 + 52) = 1;
              v16[52] = 0;
              std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
                v15,
                v16);
              v16 = *(_BYTE **)(v7 + 8);
              v15 = this;
            }
            v16[52] = *(_BYTE *)(v7 + 52);
            *(_BYTE *)(v7 + 52) = 1;
            *(_BYTE *)(*((_DWORD *)v16 + 2) + 52) = 1;
            std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
              v15,
              v7);
            break;
          }
        }
        else
        {
          if ( !v16[52] )
          {
            v16[52] = 1;
            *(_BYTE *)(v7 + 52) = 0;
            std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            v16 = *(_BYTE **)v7;
            v15 = this;
          }
          if ( v16[53] )
            goto LABEL_53;
          if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 52) != 1 || *(_BYTE *)(*(_DWORD *)v16 + 52) != 1 )
          {
            if ( *(_BYTE *)(*(_DWORD *)v16 + 52) == 1 )
            {
              *(_BYTE *)(*((_DWORD *)v16 + 2) + 52) = 1;
              v16[52] = 0;
              std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
                v15,
                (int)v16);
              v16 = *(_BYTE **)v7;
              v15 = this;
            }
            v16[52] = *(_BYTE *)(v7 + 52);
            *(_BYTE *)(v7 + 52) = 1;
            *(_BYTE *)(*(_DWORD *)v16 + 52) = 1;
            std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            break;
          }
        }
        v16[52] = 0;
LABEL_53:
        v5 = v7;
        v17 = v7 == *(_DWORD *)(v15[1] + 4);
        v7 = *(_DWORD *)(v7 + 4);
      }
      while ( !v17 );
    }
    *(_BYTE *)(v5 + 52) = 1;
  }
  if ( (unsigned int)v4[9] >= 0x10 )
    operator delete((void *)v4[4]);
  v4[9] = 15;
  v4[8] = 0;
  *((_BYTE *)v4 + 16) = 0;
  operator delete(v4);
  v18 = this[2];
  if ( v18 )
    this[2] = v18 - 1;
  result = a2;
  *a2 = p;
  return result;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004AC8C0) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(
        _DWORD *this,
        int **a2,
        int *p)
{
  int *v4; // ebp
  int v5; // edi
  int *v6; // ecx
  int v7; // esi
  int v8; // eax
  int **v9; // ebx
  int *v10; // eax
  int v11; // ebx
  int v12; // eax
  int **v13; // eax
  char v14; // al
  _DWORD *v15; // ecx
  _BYTE *v16; // eax
  bool v17; // zf
  int v18; // eax
  int **result; // eax
  std::string _Message; // [esp+Ch] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+28h] [ebp-34h] BYREF
  int v23; // [esp+58h] [ebp-4h]

  if ( *((_BYTE *)p + 49) )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "invalid map/set<T> iterator", 0x1Bu);
    v23 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::out_of_range::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVout_of_range_std__);
  }
  v4 = p;
  std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::const_iterator::_Inc((int *)&p);
  if ( *(_BYTE *)(*v4 + 49) )
  {
    v5 = v4[2];
LABEL_8:
    v7 = v4[1];
    if ( !*(_BYTE *)(v5 + 49) )
      *(_DWORD *)(v5 + 4) = v7;
    v8 = this[1];
    if ( *(int **)(v8 + 4) == v4 )
    {
      *(_DWORD *)(v8 + 4) = v5;
    }
    else if ( *(int **)v7 == v4 )
    {
      *(_DWORD *)v7 = v5;
    }
    else
    {
      *(_DWORD *)(v7 + 8) = v5;
    }
    v9 = (int **)this[1];
    if ( *v9 == v4 )
    {
      if ( *(_BYTE *)(v5 + 49) )
        v10 = (int *)v7;
      else
        v10 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Min((_DWORD *)v5);
      *v9 = v10;
    }
    v11 = this[1];
    if ( *(int **)(v11 + 8) == v4 )
    {
      if ( *(_BYTE *)(v5 + 49) )
        *(_DWORD *)(v11 + 8) = v7;
      else
        *(_DWORD *)(v11 + 8) = std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Max(v5);
    }
    goto LABEL_35;
  }
  if ( *(_BYTE *)(v4[2] + 49) )
  {
    v5 = *v4;
    goto LABEL_8;
  }
  v6 = p;
  v5 = p[2];
  if ( p == v4 )
    goto LABEL_8;
  *(_DWORD *)(*v4 + 4) = p;
  *v6 = *v4;
  if ( v6 == (int *)v4[2] )
  {
    v7 = (int)v6;
  }
  else
  {
    v7 = v6[1];
    if ( !*(_BYTE *)(v5 + 49) )
      *(_DWORD *)(v5 + 4) = v7;
    *(_DWORD *)v7 = v5;
    v6[2] = v4[2];
    *(_DWORD *)(v4[2] + 4) = v6;
  }
  v12 = this[1];
  if ( *(int **)(v12 + 4) == v4 )
  {
    *(_DWORD *)(v12 + 4) = v6;
  }
  else
  {
    v13 = (int **)v4[1];
    if ( *v13 == v4 )
      *v13 = v6;
    else
      v13[2] = v6;
  }
  v6[1] = v4[1];
  v14 = *((_BYTE *)v6 + 48);
  *((_BYTE *)v6 + 48) = *((_BYTE *)v4 + 48);
  *((_BYTE *)v4 + 48) = v14;
LABEL_35:
  if ( *((_BYTE *)v4 + 48) == 1 )
  {
    v15 = this;
    if ( v5 != *(_DWORD *)(this[1] + 4) )
    {
      do
      {
        if ( *(_BYTE *)(v5 + 48) != 1 )
          break;
        v16 = *(_BYTE **)v7;
        if ( v5 == *(_DWORD *)v7 )
        {
          v16 = *(_BYTE **)(v7 + 8);
          if ( !v16[48] )
          {
            v16[48] = 1;
            *(_BYTE *)(v7 + 48) = 0;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              v15,
              v7);
            v16 = *(_BYTE **)(v7 + 8);
            v15 = this;
          }
          if ( v16[49] )
            goto LABEL_53;
          if ( *(_BYTE *)(*(_DWORD *)v16 + 48) != 1 || *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) != 1 )
          {
            if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) == 1 )
            {
              *(_BYTE *)(*(_DWORD *)v16 + 48) = 1;
              v16[48] = 0;
              std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
                v15,
                v16);
              v16 = *(_BYTE **)(v7 + 8);
              v15 = this;
            }
            v16[48] = *(_BYTE *)(v7 + 48);
            *(_BYTE *)(v7 + 48) = 1;
            *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) = 1;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              v15,
              v7);
            break;
          }
        }
        else
        {
          if ( !v16[48] )
          {
            v16[48] = 1;
            *(_BYTE *)(v7 + 48) = 0;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            v16 = *(_BYTE **)v7;
            v15 = this;
          }
          if ( v16[49] )
            goto LABEL_53;
          if ( *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) != 1 || *(_BYTE *)(*(_DWORD *)v16 + 48) != 1 )
          {
            if ( *(_BYTE *)(*(_DWORD *)v16 + 48) == 1 )
            {
              *(_BYTE *)(*((_DWORD *)v16 + 2) + 48) = 1;
              v16[48] = 0;
              std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
                v15,
                (int)v16);
              v16 = *(_BYTE **)v7;
              v15 = this;
            }
            v16[48] = *(_BYTE *)(v7 + 48);
            *(_BYTE *)(v7 + 48) = 1;
            *(_BYTE *)(*(_DWORD *)v16 + 48) = 1;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
              v15,
              (_DWORD *)v7);
            break;
          }
        }
        v16[48] = 0;
LABEL_53:
        v5 = v7;
        v17 = v7 == *(_DWORD *)(v15[1] + 4);
        v7 = *(_DWORD *)(v7 + 4);
      }
      while ( !v17 );
    }
    *(_BYTE *)(v5 + 48) = 1;
  }
  if ( (unsigned int)v4[9] >= 0x10 )
    operator delete((void *)v4[4]);
  v4[9] = 15;
  v4[8] = 0;
  *((_BYTE *)v4 + 16) = 0;
  operator delete(v4);
  v18 = this[2];
  if ( v18 )
    this[2] = v18 - 1;
  result = a2;
  *a2 = p;
  return result;
}
// 4FC8BC: using guessed type void *std::out_of_range::`vftable';

//----- (004ACBA0) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Erase(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v1; // edi
  char result; // al
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // esi

  v1 = a1;
  result = BYTE1(a1[1]._Parent);
  for ( i = a1; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Erase(i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v1);
    operator delete(v1);
    result = BYTE1(i[1]._Parent);
  }
  return result;
}

//----- (004ACBE0) --------------------------------------------------------
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Insert(
        _DWORD *this,
        CPacketDispatch **a2,
        char a3,
        CPacketDispatch **a4,
        const std::string *_Right)
{
  CPacketDispatch *v6; // ecx
  CPacketDispatch **v7; // eax
  CPacketDispatch **v8; // eax
  int v9; // eax
  int *p_m_Session; // eax
  int v11; // esi
  _DWORD *v12; // ecx
  int *v13; // ebp
  int v14; // edx
  CPacketDispatch **result; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::string *_Righta; // [esp+68h] [ebp+10h]

  if ( this[2] >= 0x6666665u )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode(
         this[1],
         (int)a4,
         this[1],
         _Right,
         0);
  v7 = (CPacketDispatch **)this[1];
  _Righta = (const std::string *)v6;
  ++this[2];
  if ( a4 == v7 )
  {
    v7[1] = v6;
    *(_DWORD *)this[1] = v6;
    *(_DWORD *)(this[1] + 8) = v6;
  }
  else if ( a3 )
  {
    *a4 = v6;
    v8 = (CPacketDispatch **)this[1];
    if ( a4 == (CPacketDispatch **)*v8 )
      *v8 = v6;
  }
  else
  {
    a4[2] = v6;
    v9 = this[1];
    if ( a4 == *(CPacketDispatch ***)(v9 + 8) )
      *(_DWORD *)(v9 + 8) = v6;
  }
  p_m_Session = (int *)&v6->m_Session;
  v11 = (int)v6;
  if ( !v6->m_Session->m_RemoteAddr.m_SockAddr.sa_data[6] )
  {
    while ( 1 )
    {
      v12 = (_DWORD *)*p_m_Session;
      v13 = *(int **)(*p_m_Session + 4);
      v14 = *v13;
      if ( *p_m_Session == *v13 )
      {
        v14 = v13[2];
        if ( *(_BYTE *)(v14 + 52) )
        {
          if ( v11 == v12[2] )
          {
            v11 = *p_m_Session;
            std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
              this,
              *p_m_Session);
          }
          *(_BYTE *)(*(_DWORD *)(v11 + 4) + 52) = 1;
          *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 52) = 0;
          std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
            this,
            *(_DWORD **)(*(_DWORD *)(v11 + 4) + 4));
          goto LABEL_21;
        }
      }
      else if ( *(_BYTE *)(v14 + 52) )
      {
        if ( v11 == *v12 )
        {
          v11 = *p_m_Session;
          std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Rrotate(
            this,
            (_DWORD *)*p_m_Session);
        }
        *(_BYTE *)(*(_DWORD *)(v11 + 4) + 52) = 1;
        *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 52) = 0;
        std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Lrotate(
          this,
          *(_DWORD *)(*(_DWORD *)(v11 + 4) + 4));
        goto LABEL_21;
      }
      *(_BYTE *)(*p_m_Session + 52) = 1;
      *(_BYTE *)(v14 + 52) = 1;
      *(_BYTE *)(*(_DWORD *)(*p_m_Session + 4) + 52) = 0;
      v11 = *(_DWORD *)(*p_m_Session + 4);
LABEL_21:
      p_m_Session = (int *)(v11 + 4);
      if ( *(_BYTE *)(*(_DWORD *)(v11 + 4) + 52) )
      {
        v6 = (CPacketDispatch *)_Righta;
        break;
      }
    }
  }
  result = a2;
  *(_BYTE *)(*(_DWORD *)(this[1] + 4) + 52) = 1;
  *a2 = v6;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004ACD90) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Erase(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v1; // edi
  char result; // al
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // esi

  v1 = a1;
  result = BYTE1(a1[1]._Left);
  for ( i = a1; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Erase(i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v1);
    operator delete(v1);
    result = BYTE1(i[1]._Left);
  }
  return result;
}

//----- (004ACDD0) --------------------------------------------------------
CPacketDispatch **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Insert(
        _DWORD *this,
        CPacketDispatch **a2,
        char a3,
        CPacketDispatch **a4,
        const std::string *_Right)
{
  CPacketDispatch *v6; // ecx
  CPacketDispatch **v7; // eax
  CPacketDispatch **v8; // eax
  int v9; // eax
  int *p_m_Session; // eax
  int v11; // esi
  _DWORD *v12; // ecx
  int *v13; // ebp
  int v14; // edx
  CPacketDispatch **result; // eax
  std::string _Message; // [esp+8h] [ebp-50h] BYREF
  std::logic_error pExceptionObject; // [esp+24h] [ebp-34h] BYREF
  int v18; // [esp+54h] [ebp-4h]
  const std::string *_Righta; // [esp+68h] [ebp+10h]

  if ( this[2] >= 0x71C71C6u )
  {
    _Message._Myres = 15;
    _Message._Mysize = 0;
    _Message._Bx._Buf[0] = 0;
    std::string::assign(&_Message, "map/set<T> too long", 0x13u);
    v18 = 0;
    std::logic_error::logic_error(&pExceptionObject, &_Message);
    pExceptionObject.__vftable = (std::logic_error_vtbl *)&std::length_error::`vftable';
    _CxxThrowException(&pExceptionObject, &_TI3_AVlength_error_std__);
  }
  v6 = std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Buynode(
         this[1],
         (int)a4,
         this[1],
         _Right,
         0);
  v7 = (CPacketDispatch **)this[1];
  _Righta = (const std::string *)v6;
  ++this[2];
  if ( a4 == v7 )
  {
    v7[1] = v6;
    *(_DWORD *)this[1] = v6;
    *(_DWORD *)(this[1] + 8) = v6;
  }
  else if ( a3 )
  {
    *a4 = v6;
    v8 = (CPacketDispatch **)this[1];
    if ( a4 == (CPacketDispatch **)*v8 )
      *v8 = v6;
  }
  else
  {
    a4[2] = v6;
    v9 = this[1];
    if ( a4 == *(CPacketDispatch ***)(v9 + 8) )
      *(_DWORD *)(v9 + 8) = v6;
  }
  p_m_Session = (int *)&v6->m_Session;
  v11 = (int)v6;
  if ( !v6->m_Session->m_RemoteAddr.m_SockAddr.sa_data[2] )
  {
    while ( 1 )
    {
      v12 = (_DWORD *)*p_m_Session;
      v13 = *(int **)(*p_m_Session + 4);
      v14 = *v13;
      if ( *p_m_Session == *v13 )
      {
        v14 = v13[2];
        if ( *(_BYTE *)(v14 + 48) )
        {
          if ( v11 == v12[2] )
          {
            v11 = *p_m_Session;
            std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
              this,
              *p_m_Session);
          }
          *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) = 1;
          *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 48) = 0;
          std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
            this,
            *(_DWORD **)(*(_DWORD *)(v11 + 4) + 4));
          goto LABEL_21;
        }
      }
      else if ( *(_BYTE *)(v14 + 48) )
      {
        if ( v11 == *v12 )
        {
          v11 = *p_m_Session;
          std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Rrotate(
            this,
            (_DWORD *)*p_m_Session);
        }
        *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) = 1;
        *(_BYTE *)(*(_DWORD *)(*(_DWORD *)(v11 + 4) + 4) + 48) = 0;
        std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,void *>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,void *>>>,1>>::_Lrotate(
          this,
          *(_DWORD *)(*(_DWORD *)(v11 + 4) + 4));
        goto LABEL_21;
      }
      *(_BYTE *)(*p_m_Session + 48) = 1;
      *(_BYTE *)(v14 + 48) = 1;
      *(_BYTE *)(*(_DWORD *)(*p_m_Session + 4) + 48) = 0;
      v11 = *(_DWORD *)(*p_m_Session + 4);
LABEL_21:
      p_m_Session = (int *)(v11 + 4);
      if ( *(_BYTE *)(*(_DWORD *)(v11 + 4) + 48) )
      {
        v6 = (CPacketDispatch *)_Righta;
        break;
      }
    }
  }
  result = a2;
  *(_BYTE *)(*(_DWORD *)(this[1] + 4) + 48) = 1;
  *a2 = v6;
  return result;
}
// 4FC8B0: using guessed type void *std::length_error::`vftable';

//----- (004ACF80) --------------------------------------------------------
char __stdcall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Erase(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *a1)
{
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v1; // edi
  char result; // al
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *i; // esi

  v1 = a1;
  result = BYTE1(a1[1]._Left);
  for ( i = a1; !result; v1 = i )
  {
    std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Erase(i->_Right);
    i = i->_Left;
    std::_Tree_nod<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Node::~_Node(v1);
    operator delete(v1);
    result = BYTE1(i[1]._Left);
  }
  return result;
}

//----- (004ACFC0) --------------------------------------------------------
std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Copy(
        std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **this,
        int *a2,
        int a3)
{
  char v3; // al
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v4; // edi
  int v6; // [esp-8h] [ebp-2Ch]
  _DWORD v7[4]; // [esp+0h] [ebp-24h] BYREF
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *v8; // [esp+10h] [ebp-14h]
  _DWORD *v9; // [esp+14h] [ebp-10h]
  int v10; // [esp+20h] [ebp-4h]

  v8 = this[1];
  v3 = *((_BYTE *)a2 + 53);
  v9 = v7;
  v7[3] = this;
  if ( !v3 )
  {
    v4 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Buynode((int)this[1], a3, (int)this[1], (const std::string *)(a2 + 3), *((_BYTE *)a2 + 52));
    if ( BYTE1(v8[1]._Parent) )
      v8 = v4;
    v6 = *a2;
    v10 = 0;
    v4->_Left = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Copy(v6, v4);
    v4->_Right = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node *)std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Copy(a2[2], v4);
  }
  return v8;
}

//----- (004AD070) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::insert(
        _DWORD *this,
        int a2,
        const std::string *_Right)
{
  int v3; // ebp
  const std::string *v4; // ebx
  char v5; // al
  const std::string *v6; // edi
  unsigned int i; // ecx
  unsigned int v8; // ebx
  const char *v9; // eax
  unsigned int Mysize; // edx
  int v11; // ecx
  const char *v12; // esi
  int v13; // eax
  bool v14; // sf
  int v15; // eax
  const std::string *v16; // esi
  CPacketDispatch *v17; // ecx
  int result; // eax
  const char *Buf; // eax
  CPacketDispatch *v20; // edx
  char v21; // [esp+10h] [ebp-14h]
  _DWORD *v22; // [esp+14h] [ebp-10h]
  const std::string *v23; // [esp+20h] [ebp-4h]

  v3 = *(_DWORD *)(this[1] + 4);
  v4 = (const std::string *)this[1];
  v5 = 1;
  v6 = _Right;
  v22 = this;
  v21 = 1;
  if ( !*(_BYTE *)(v3 + 53) )
  {
    for ( i = _Right->_Mysize; ; i = _Right->_Mysize )
    {
      v8 = *(_DWORD *)(v3 + 32);
      v23 = (const std::string *)v3;
      if ( *(_DWORD *)(v3 + 36) < 0x10u )
        v9 = (const char *)(v3 + 16);
      else
        v9 = *(const char **)(v3 + 16);
      Mysize = _Right->_Mysize;
      if ( i < Mysize )
        Mysize = i;
      if ( !Mysize )
        goto LABEL_16;
      v11 = Mysize;
      if ( Mysize >= v8 )
        v11 = *(_DWORD *)(v3 + 32);
      v12 = v6->_Myres < 0x10 ? (const char *)&v6->_Bx : v6->_Bx._Ptr;
      v13 = memcmp(v12, v9, v11);
      v14 = v13 < 0;
      v6 = _Right;
      if ( !v13 )
      {
LABEL_16:
        if ( Mysize >= v8 )
          v15 = Mysize != v8;
        else
          v15 = -1;
        v14 = v15 < 0;
      }
      v5 = v14;
      v21 = v14;
      v3 = v14 ? *(_DWORD *)v3 : *(_DWORD *)(v3 + 8);
      if ( *(_BYTE *)(v3 + 53) )
        break;
    }
    v4 = v23;
    this = v22;
  }
  v16 = v4;
  _Right = v4;
  if ( v5 )
  {
    if ( v4 == *(const std::string **)this[1] )
    {
      v17 = *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Insert(
               this,
               (CPacketDispatch **)&_Right,
               1,
               (CPacketDispatch **)v4,
               v6);
      result = a2;
      *(_DWORD *)a2 = v17;
      *(_BYTE *)(a2 + 4) = 1;
      return result;
    }
    std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::const_iterator::_Dec(&_Right);
    v16 = _Right;
  }
  if ( v6->_Myres < 0x10 )
    Buf = v6->_Bx._Buf;
  else
    Buf = v6->_Bx._Ptr;
  if ( std::string::compare((std::string *)(&v16->_Bx._Ptr + 2), 0, (unsigned int)v16[1]._Bx._Ptr, Buf, v6->_Mysize) >= 0 )
  {
    result = a2;
    *(_DWORD *)a2 = v16;
    *(_BYTE *)(a2 + 4) = 0;
  }
  else
  {
    v20 = *std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Insert(
             v22,
             (CPacketDispatch **)&_Right,
             v21,
             (CPacketDispatch **)v4,
             v6);
    result = a2;
    *(_DWORD *)a2 = v20;
    *(_BYTE *)(a2 + 4) = 1;
  }
  return result;
}

//----- (004AD1F0) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::insert(
        _DWORD *this,
        int a2,
        const std::string *_Right)
{
  int v3; // ebp
  const std::string *v4; // ebx
  char v5; // al
  const std::string *v6; // edi
  unsigned int i; // ecx
  unsigned int v8; // ebx
  const char *v9; // eax
  unsigned int Mysize; // edx
  int v11; // ecx
  const char *v12; // esi
  int v13; // eax
  bool v14; // sf
  int v15; // eax
  const std::string *v16; // esi
  CPacketDispatch *v17; // ecx
  int result; // eax
  const char *Buf; // eax
  CPacketDispatch *v20; // edx
  char v21; // [esp+10h] [ebp-14h]
  _DWORD *v22; // [esp+14h] [ebp-10h]
  const std::string *v23; // [esp+20h] [ebp-4h]

  v3 = *(_DWORD *)(this[1] + 4);
  v4 = (const std::string *)this[1];
  v5 = 1;
  v6 = _Right;
  v22 = this;
  v21 = 1;
  if ( !*(_BYTE *)(v3 + 49) )
  {
    for ( i = _Right->_Mysize; ; i = _Right->_Mysize )
    {
      v8 = *(_DWORD *)(v3 + 32);
      v23 = (const std::string *)v3;
      if ( *(_DWORD *)(v3 + 36) < 0x10u )
        v9 = (const char *)(v3 + 16);
      else
        v9 = *(const char **)(v3 + 16);
      Mysize = _Right->_Mysize;
      if ( i < Mysize )
        Mysize = i;
      if ( !Mysize )
        goto LABEL_16;
      v11 = Mysize;
      if ( Mysize >= v8 )
        v11 = *(_DWORD *)(v3 + 32);
      v12 = v6->_Myres < 0x10 ? (const char *)&v6->_Bx : v6->_Bx._Ptr;
      v13 = memcmp(v12, v9, v11);
      v14 = v13 < 0;
      v6 = _Right;
      if ( !v13 )
      {
LABEL_16:
        if ( Mysize >= v8 )
          v15 = Mysize != v8;
        else
          v15 = -1;
        v14 = v15 < 0;
      }
      v5 = v14;
      v21 = v14;
      v3 = v14 ? *(_DWORD *)v3 : *(_DWORD *)(v3 + 8);
      if ( *(_BYTE *)(v3 + 49) )
        break;
    }
    v4 = v23;
    this = v22;
  }
  v16 = v4;
  _Right = v4;
  if ( v5 )
  {
    if ( v4 == *(const std::string **)this[1] )
    {
      v17 = *std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Insert(
               this,
               (CPacketDispatch **)&_Right,
               1,
               (CPacketDispatch **)v4,
               v6);
      result = a2;
      *(_DWORD *)a2 = v17;
      *(_BYTE *)(a2 + 4) = 1;
      return result;
    }
    std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::const_iterator::_Dec(&_Right);
    v16 = _Right;
  }
  if ( v6->_Myres < 0x10 )
    Buf = v6->_Bx._Buf;
  else
    Buf = v6->_Bx._Ptr;
  if ( std::string::compare((std::string *)(&v16->_Bx._Ptr + 2), 0, (unsigned int)v16[1]._Bx._Ptr, Buf, v6->_Mysize) >= 0 )
  {
    result = a2;
    *(_DWORD *)a2 = v16;
    *(_BYTE *)(a2 + 4) = 0;
  }
  else
  {
    v20 = *std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Insert(
             v22,
             (CPacketDispatch **)&_Right,
             v21,
             (CPacketDispatch **)v4,
             v6);
    result = a2;
    *(_DWORD *)a2 = v20;
    *(_BYTE *)(a2 + 4) = 1;
  }
  return result;
}

//----- (004AD370) --------------------------------------------------------
int __thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::insert(
        _DWORD *this,
        int a2,
        const std::string *_Right)
{
  int v3; // eax
  int v4; // ebp
  const std::string *v5; // esi
  unsigned int i; // ecx
  unsigned int v7; // ebx
  const char *v8; // edi
  unsigned int Mysize; // edx
  int v10; // ecx
  const char *v11; // esi
  int v12; // eax
  bool v13; // sf
  int v14; // eax
  CPacketDispatch *v15; // ecx
  int result; // eax
  char v17; // [esp+8h] [ebp-14h]
  int v18; // [esp+14h] [ebp-8h]
  _DWORD *v19; // [esp+18h] [ebp-4h]

  v3 = this[1];
  v4 = *(_DWORD *)(v3 + 4);
  v5 = _Right;
  v19 = this;
  v17 = 1;
  if ( !*(_BYTE *)(v4 + 49) )
  {
    for ( i = _Right->_Mysize; ; i = _Right->_Mysize )
    {
      v7 = *(_DWORD *)(v4 + 32);
      v18 = v4;
      if ( *(_DWORD *)(v4 + 36) < 0x10u )
        v8 = (const char *)(v4 + 16);
      else
        v8 = *(const char **)(v4 + 16);
      Mysize = _Right->_Mysize;
      if ( i < Mysize )
        Mysize = i;
      if ( !Mysize )
        goto LABEL_16;
      v10 = Mysize;
      if ( Mysize >= v7 )
        v10 = *(_DWORD *)(v4 + 32);
      v11 = v5->_Myres < 0x10 ? (const char *)&v5->_Bx : v5->_Bx._Ptr;
      v12 = memcmp(v11, v8, v10);
      v13 = v12 < 0;
      v5 = _Right;
      if ( !v12 )
      {
LABEL_16:
        if ( Mysize >= v7 )
          v14 = Mysize != v7;
        else
          v14 = -1;
        v13 = v14 < 0;
      }
      v17 = v13;
      v4 = v13 ? *(_DWORD *)v4 : *(_DWORD *)(v4 + 8);
      if ( *(_BYTE *)(v4 + 49) )
        break;
    }
    v3 = v18;
    this = v19;
  }
  v15 = *std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Insert(
           this,
           (CPacketDispatch **)&_Right,
           v17,
           (CPacketDispatch **)v3,
           v5);
  result = a2;
  *(_DWORD *)a2 = v15;
  *(_BYTE *)(a2 + 4) = 1;
  return result;
}

//----- (004AD460) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::erase(
        _DWORD *this,
        int **a2,
        int *p,
        int *a4)
{
  int *v4; // ebx
  int *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **v7; // eax
  _DWORD *v8; // eax
  int **result; // eax
  int *v10; // ecx
  int v11; // eax
  int *j; // eax
  int i; // eax

  v4 = a4;
  v5 = p;
  v7 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)this[1];
  if ( p == (int *)*v7 && a4 == (int *)v7 )
  {
    std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::_Erase(v7[1]);
    *(_DWORD *)(this[1] + 4) = this[1];
    v8 = (_DWORD *)this[1];
    this[2] = 0;
    *v8 = v8;
    *(_DWORD *)(this[1] + 8) = this[1];
    result = a2;
    *a2 = *(int **)this[1];
  }
  else
  {
    if ( p != a4 )
    {
      do
      {
        v10 = v5;
        if ( !*((_BYTE *)v5 + 53) )
        {
          v11 = v5[2];
          if ( *(_BYTE *)(v11 + 53) )
          {
            for ( i = v5[1]; !*(_BYTE *)(i + 53); i = *(_DWORD *)(i + 4) )
            {
              if ( v5 != *(int **)(i + 8) )
                break;
              v5 = (int *)i;
            }
            v5 = (int *)i;
          }
          else
          {
            v5 = (int *)v5[2];
            for ( j = *(int **)v11; !*((_BYTE *)j + 53); j = (int *)*j )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,VarInfo,std::less<std::string>,std::allocator<std::pair<std::string const,VarInfo>>,0>>::erase(
          this,
          &p,
          v10);
      }
      while ( v5 != v4 );
    }
    result = a2;
    *a2 = v5;
  }
  return result;
}

//----- (004AD520) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(
        _DWORD *this,
        int **a2,
        int *p,
        int *a4)
{
  int *v4; // ebx
  int *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **v7; // eax
  _DWORD *v8; // eax
  int **result; // eax
  int *v10; // ecx
  int v11; // eax
  int *j; // eax
  int i; // eax

  v4 = a4;
  v5 = p;
  v7 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)this[1];
  if ( p == (int *)*v7 && a4 == (int *)v7 )
  {
    std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::_Erase(v7[1]);
    *(_DWORD *)(this[1] + 4) = this[1];
    v8 = (_DWORD *)this[1];
    this[2] = 0;
    *v8 = v8;
    *(_DWORD *)(this[1] + 8) = this[1];
    result = a2;
    *a2 = *(int **)this[1];
  }
  else
  {
    if ( p != a4 )
    {
      do
      {
        v10 = v5;
        if ( !*((_BYTE *)v5 + 49) )
        {
          v11 = v5[2];
          if ( *(_BYTE *)(v11 + 49) )
          {
            for ( i = v5[1]; !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 4) )
            {
              if ( v5 != *(int **)(i + 8) )
                break;
              v5 = (int *)i;
            }
            v5 = (int *)i;
          }
          else
          {
            v5 = (int *)v5[2];
            for ( j = *(int **)v11; !*((_BYTE *)j + 49); j = (int *)*j )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(
          this,
          &p,
          v10);
      }
      while ( v5 != v4 );
    }
    result = a2;
    *a2 = v5;
  }
  return result;
}

//----- (004AD5E0) --------------------------------------------------------
int **__thiscall std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::erase(
        _DWORD *this,
        int **a2,
        int *p,
        int *a4)
{
  int *v4; // ebx
  int *v5; // esi
  std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **v7; // eax
  _DWORD *v8; // eax
  int **result; // eax
  int *v10; // ecx
  int v11; // eax
  int *j; // eax
  int i; // eax

  v4 = a4;
  v5 = p;
  v7 = (std::_Tree_nod<std::_Tmap_traits<std::string,unsigned char,std::less<std::string >,std::allocator<std::pair<std::string const ,unsigned char> >,0> >::_Node **)this[1];
  if ( p == (int *)*v7 && a4 == (int *)v7 )
  {
    std::_Tree<std::_Tmap_traits<std::string,std::pair<SFuncType,bool>,std::less<std::string>,std::allocator<std::pair<std::string const,std::pair<SFuncType,bool>>>,1>>::_Erase(v7[1]);
    *(_DWORD *)(this[1] + 4) = this[1];
    v8 = (_DWORD *)this[1];
    this[2] = 0;
    *v8 = v8;
    *(_DWORD *)(this[1] + 8) = this[1];
    result = a2;
    *a2 = *(int **)this[1];
  }
  else
  {
    if ( p != a4 )
    {
      do
      {
        v10 = v5;
        if ( !*((_BYTE *)v5 + 49) )
        {
          v11 = v5[2];
          if ( *(_BYTE *)(v11 + 49) )
          {
            for ( i = v5[1]; !*(_BYTE *)(i + 49); i = *(_DWORD *)(i + 4) )
            {
              if ( v5 != *(int **)(i + 8) )
                break;
              v5 = (int *)i;
            }
            v5 = (int *)i;
          }
          else
          {
            v5 = (int *)v5[2];
            for ( j = *(int **)v11; !*((_BYTE *)j + 49); j = (int *)*j )
              v5 = j;
          }
        }
        std::_Tree<std::_Tmap_traits<std::string,ConstInfo,std::less<std::string>,std::allocator<std::pair<std::string const,ConstInfo>>,0>>::erase(
          this,
          &p,
          v10);
      }
      while ( v5 != v4 );
    }
    result = a2;
    *a2 = v5;
  }
  return result;
}
