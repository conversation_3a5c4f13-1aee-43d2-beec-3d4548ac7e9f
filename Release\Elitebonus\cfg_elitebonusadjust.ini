[config]
; This section defines the amount of fame points needed to reach each tier.
E1 = 2000
E2 = 10000
E3 = 25000
E4 = 50000
E5 = 75000
E6 = 100000
E7 = 150000
E8 = 250000
E9 = 500000
EX = 1000000
Ex1 = 1500000


; The system will cap elite bonuses at E0 (1.0) and prevent them from going into negative tiers (E-1 to E-11).
disablenegative=0

[FAME_REVISED]
; Set Number based following on Mapping below
E0=0
E1=1
E2=2
E3=3
E4=4
E5=5
E6=6
E7=7
E8=8
E9=9
E10=10
E11=11


[PERCENTAGE]

;----------------------------------------------------------------------
;			Original Mapping Percentage(%)		     //
;			0=0%  (1.0)				     //
;			1=7%  (1.7)	-1=-2%  (-0.98)		     //
;			2=16% (1.16) 	-2=-4%  (-0.96)	 	     //
;			3=23% (1.23)	-3=-6%  (-0.94)		     //
;			4=33% (1.33)	-4=-8%  (-0.92)		     //
;			5=40% (1.40)	-5=-10% (-0.90)		     //
;			6=46% (1.46)	-6=-12% (-0.88)		     //
;			7=56% (1.56)	-7=-14% (-0.86)		     //
;			8=60% (1.60)	-8=-16% (-0.84)		     //
;			9=70% (1.70)	-9=-18% (-0.82)		     //
;			10=76% (1.76)	-10=-20%(-0.80)	 	     //
;			11=80% (1.80)	-11=-20%(-0.80)     	     //
;----------------------------------------------------------------------
; Percentage increases based on actual multiplier values
; Example: if you want 2% on E1 and E2, set (1=2%) in [PERCENTAGE] Section
; Or if you want multiple tiers with same value, set (E1=1 E2=1) in [FAME_REVISED] Section
; Don't set Same Percentage with 2 different numbers (might unbalance). use [FAME_REVISED].
; Don't change if you don't know what you are doing!



;Positive Elitebonus
0=0%
1=7%
2=16%
3=23%
4=33%
5=40%
6=46%
7=56%
8=60%
9=70%
10=76%
11=80%

;Negative Elitebonus
-1=-2%
-2=-4%
-3=-5%
-4=-6%
-5=-10%
-6=-12%
-7=-14%
-8=-16%
-9=-18%
-10=-20%


;--------------------------------------------------------------
; Must Restart server After Changes.
[gap]
; This section controls the player imbalance bonus.
; This is the differential between Akkhans and Humans that triggers the bonus. Add if want more zone gap(zone8 = 10)

zone3 = 5  
Other = 10


[disable_elitebonus]
; This section allows you to disable the entire elite bonus plugin for specific zones.
; List the zone numbers separated by commas. 0 if none

Zone = 1,2,4,5

[disable_damage]
; This section allows you to disable Damage & Elitebonus of that zones 
; List the zone numbers separated by commas. 0 if none
; Suitable for bazzar , avoid from fame farming

Zone = 11